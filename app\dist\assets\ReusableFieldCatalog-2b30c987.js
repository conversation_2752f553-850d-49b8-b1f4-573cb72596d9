import{a as e,ar as v,j as h,G as c,ay as R,eJ as H,aq as O,bb as f,bc as u,bd as m,r as C,T as P,F as G}from"./index-75c1660a.js";const I=({field:s,checked:d,radioValue:i,onCheckboxChange:p,onRadioChange:x,isDisabled:l})=>e(c,{container:!0,md:6,alignItems:"center",children:e(v,{fullWidth:!0,children:h(c,{container:!0,alignItems:"center",children:[e(c,{item:!0,md:1,children:e(R,{checked:d,onChange:p,disabled:l})}),e(c,{item:!0,md:4,children:h(H,{sx:O,children:[s,":"]})}),e(c,{item:!0,md:7,children:h(f,{sx:{display:"flex",justifyContent:"center"},row:!0,value:i==="0"?"Mandatory":i,onChange:x,children:[e(u,{value:"Mandatory",control:e(m,{}),label:"Mandatory",disabled:l?!0:!d}),e(u,{value:"Hide",control:e(m,{}),label:"Hide",disabled:l?!0:!d}),e(u,{value:"Optional",control:e(m,{}),label:"Optional",disabled:l?!0:!d})]})})]})})}),T=({fields:s,heading:d,childCheckedStates:i,setChildCheckedStates:p,childRadioValues:x,setChildRadioValues:l,onSubmitButtonClick:L,mandatoryFields:k,DisabledChildCheck:g,fieldVisibility:F})=>{C.useState(null);const[b,S]=C.useState(!1),[y,w]=C.useState("");C.useState(s.reduce((a,n)=>(a[n]={mandatory:k.includes(n),hide:!1,optional:!1},a),{}));const M=a=>{const n=a.target.checked;S(n);const o={...i};s.forEach(t=>{g[t]||(n&&!o[t]?o[t]=!0:!n&&o[t]&&(o[t]=!1))}),p(o);const r={...x};s.forEach(t=>{g[t]||(n?r[t]=y:r[t]="")}),l(r)},j=a=>{const n=a.target.value;w(n),l(o=>{const r={...o};return s.forEach(t=>{g[t]||i[t]&&(r[t]=n)}),r})},z=a=>n=>{const o=n.target.checked;p(r=>({...r,[a]:o})),!o&&F[a]!=="0"&&(console.log(F[a],"hemlo"),l(r=>({...r,[a]:F[a]})))},E=a=>n=>{const o=n.target.value;l(r=>({...r,[a]:o}))};return h(G,{children:[h(c,{container:!0,sx:{backgroundColor:"#F1F0FF",border:"1px",borderRadius:"10px"},children:[e(c,{item:!0,md:8,children:e(u,{style:{marginLeft:"1.5px"},control:e(R,{checked:b,onChange:M}),label:e(P,{sx:{fontWeight:"700",margin:"0px !important"},children:d})})}),e(c,{item:!0,md:4,children:e(v,{sx:{display:"flex",alignItems:"center",width:"100%",fontWeight:"bold",padding:"2px 0","&:hover":{backgroundColor:"#F1F0FF"}},component:"fieldset",children:h(f,{row:!0,value:y,onChange:j,sx:{fontSize:"10px !important"},children:[e(u,{sx:{fontSize:"8px !important"},value:"Mandatory",control:e(m,{}),label:"Mandatory",disabled:!b}),e(u,{sx:{fontSize:"12px !important"},value:"Hide",control:e(m,{}),label:"Hide",disabled:!b}),e(u,{sx:{fontSize:"8px !important"},value:"Optional",control:e(m,{}),label:"Optional",disabled:!b})]})})})]}),e(c,{container:!0,md:12,sx:{display:"flex"},children:s.map(a=>e(I,{field:a,checked:i[a]||!1,isDisabled:g[a]||!1,radioValue:x[a]||"",onCheckboxChange:z(a),onRadioChange:E(a),setChildCheckedStates:p,setChildRadioValues:l},a))})]})};export{T as R};
