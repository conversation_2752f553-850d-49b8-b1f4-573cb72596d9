import React from "react";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DemoContainer } from "@mui/x-date-pickers/internals/demo";
import DeleteIcon from "@mui/icons-material/Delete";
import DownloadIcon from "@mui/icons-material/Download";
import CloseIcon from "@mui/icons-material/Close";
import ReusableAttachementAndComments from "../../Common/ReusableAttachmentAndComments/ReusableAttachementAndComments";
import {
  InfoOutlined,
  IosShare,
  Refresh,
  TuneOutlined,
} from "@mui/icons-material";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import {
  Button,
  Checkbox,
  Grid,
  Paper,
  IconButton,
  Typography,
  TextField,
  Box,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Popper,
  BottomNavigation,
  ListItemText,
  InputLabel,
  tooltipClasses,
  Card,
  CardContent,
  OutlinedInput,
  Autocomplete,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  ButtonGroup,
  ClickAwayListener,
  MenuList,
  Divider,
} from "@mui/material";
import FileUpload from "../../Common/FileUpload";
import moment from "moment/moment";
import { Stack } from "@mui/system";
import Select from "@mui/material/Select";
import { FormControl, MenuItem } from "@mui/material";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import ForwardToInboxOutlinedIcon from "@mui/icons-material/ForwardToInboxOutlined";
import CheckCircleOutlineOutlinedIcon from "@mui/icons-material/CheckCircleOutlineOutlined";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import SearchBar from "../../Common/SearchBar";
import ReusableDialog from "../../Common/ReusableDialog";
import { ViewDetailsIcon } from "../../Common/icons";
import { useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import TrackChangesIcon from "@mui/icons-material/TrackChanges";
import styled from "@emotion/styled";
import html2canvas from "html2canvas";

import {
  commonFilterClear,
  commonFilterUpdate,
} from "../../../app/commonFilterSlice";
import { v4 as uuidv4 } from "uuid";
import {
  button_Marginleft,
  button_Outlined,
  button_Primary,
  container_filter,
  container_table,
  font_Small,
  iconButton_SpacingSmall,
  outerContainer_Information,
  outermostContainer,
  outermostContainer_Information,
} from "../../Common/commonStyles";
import {
  DateField,
  DatePicker,
  DesktopDatePicker,
  DesktopDateTimePicker,
  LocalizationProvider,
} from "@mui/x-date-pickers";
import DateRange from "../../Common/DateRangePicker";
import {
  destination_CostCenter,
  destination_MaterialMgmt,
  destination_ProfitCenter,
} from "../../../destinationVariables";
import { doAjax } from "../../Common/fetchService";
import ClearIcon from "@mui/icons-material/Clear";
import ReusableTable from "../../common/ReusableTable";

import { setDropDown } from "../../../app/dropDownDataSlice";
import { DateRangePicker } from "@mui/lab";
import { Fragment } from "react";

// import "./masterDataCockpit.css";
import {
  clearCostCenter,
  setControllingArea,
  setCostCenterAddressTab,
  setCostCenterBasicDataTab,
  setCostCenterCommunicationTab,
  setCostCenterControlTab,
  setCostCenterHistoryTab,
  setCostCenterTemplatesTab,
  setHandleMassMode,
  setMultipleCostCenterData,
} from "../../../app/costCenterTabsSlice";
import { MatDownload } from "../../DocumentManagement/UtilDoc";
import AttachmentUploadDialog from "../../Common/AttachmentUploadDialog";
import ReusableIcon from "../../Common/ReusableIcon";
import { initialDataUpdate } from "../../../app/initialDataSlice";
import { checkIwaAccess, saveExcel } from "../../../functions";
import { setTaskData } from "../../../app/userManagementSlice";
import LoadingComponent from "../../Common/LoadingComponent";

// const ITEM_HEIGHT = 48;
// const ITEM_PADDING_TOP = 8;
// const MenuProps = {
//   PaperProps: {
//     style: {
//       maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
//       width: 250,
//     },
//   },
// };
// const exportAsPicture = () => {
//   const html = document.getElementsByTagName("HTML")[0];
//   const body = document.getElementsByTagName("BODY")[0];
//   let htmlWidth = html.clientWidth;
//   let bodyWidth = body.clientWidth;

//   const data = document.getElementById("e-invoice-export"); //CHANGE THIS ID WITH ID OF OUTERMOST DIV CONTAINER
//   const newWidth = data.scrollWidth - data.clientWidth;

//   if (newWidth > data.clientWidth) {
//     htmlWidth += newWidth;
//     bodyWidth += newWidth;
//   }

//   html.style.width = htmlWidth + "px";
//   body.style.width = bodyWidth + "px";

//   html2canvas(data)
//     .then((canvas) => {
//       return canvas.toDataURL("image/png", 1.0);
//     })
//     .then((image) => {
//       saveAs(image, "E-InvoiceReport.png"); //CHANGE THE NAME OF THE FILE
//       html.style.width = null;
//       body.style.width = null;
//     });
// };

// const saveAs = (blob, fileName) => {
//   const elem = window.document.createElement("a");
//   elem.href = blob;
//   elem.download = fileName;
//   (document.body || document.documentElement).appendChild(elem);
//   if (typeof elem.click === "function") {
//     elem.click();
//   } else {
//     elem.target = "_blank";
//     elem.dispatchEvent(
//       new MouseEvent("click", {
//         view: window,
//         bubbles: true,
//         cancelable: true,
//       })
//     );
//   }
//   URL.revokeObjectURL(elem.href);
//   elem.remove();
// };
const CostCenter = () => {
  const [snackbar, setSnackbar] = useState(false);
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [messageDialogSeverity, setMessageDialogSeverity] = useState("");
  const appSettings = useSelector((state) => state.appSettings["Format"]);
  const [fileSizeError, setFileSizeError] = useState(false);
  const [docList, setDocList] = useState([]);
  const dropdownData = useSelector((state) => state.AllDropDown.dropDown);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [count, setCount] = useState(0);
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [skip, setSkip] = useState(0);
  const [buttonDisabledForSingleWithoutCopy, setButtonDisabledForSingleWithoutCopy] = useState(true);
  const [newCostCenterValid, setNewCostCenterValid] = useState(false)
  const [newCostCenterValidWithCopy, setNewCostCenterValidWithCopy] = useState(false)
  const [newCompanyCodeValid, setNewCompanyCodeValid] = useState(false)
  const [newControllingAreavalied, setNewControllingAreaValied] = useState(false)
  const [selectedMassChangeRowData, setSelectedMassChangeRowData] = useState(
    []
  );
  // let userData = useSelector((state) => state.userManagement.userData);
  // let iwaAccessData = useSelector(
  //   (state) => state.userManagement.entitiesAndActivities?.["Return Order"]
  // );
  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };
  let iwaAccessData = useSelector(
    (state) => state.userManagement.entitiesAndActivities?.["Display Material"]
  );
  let userData = useSelector((state) => state.userManagement.userData);
  const handlePageSizeChange = (event) => {
    const newPageSize = event.target.value;
    setPageSize(newPageSize);
    setPage(0);
    setSkip(0);
  };
  const ITEM_HEIGHT = 48;
  const ITEM_PADDING_TOP = 8;
  const MenuProps = {
    PaperProps: {
      style: {
        maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
        width: 250,
      },
    },
  };

  const handleOnClick = (costCenterNumber) => {
    // setViewDetailpage(true);
    //     dispatch(setHistoryPath({url:window.location.pathname, module:"po workbench"}));

    navigate(
      "/masterDataCockpit/materialMaster/displayMaterialDetail/" +
      materialNumber
    );
  };

  useEffect(() => {
    if ((parseInt(page) + 1) * parseInt(pageSize) >= parseInt(skip) + 1000) {
      getFilter(skip + 1000);
      setSkip((prev) => prev + 1000);
    }
  }, [page, pageSize]);

  var newDate = new Date();
  var validToDate = new Date("December 31, 9999 01:15:00");
  // validToDate.setFullYear(("July 21, 1983 01:15:00"));
  const [Status_ServiceReqForm, setStatus_ServiceReqForm] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [tableLoading, setTableLoading] = useState(false);
  const [value, setValue] = useState("1");
  const ariaLabel = { "aria-label": "description" };
  const [rmDataRows, setRmDataRows] = useState([]);
  const [UserName, setUserName] = React.useState("");
  const [openSnackBaraccept, setOpenSnackBaraccept] = useState(false);
  const [confirmingid, setConfirmingid] = useState("");
  const [materialNumber, setMaterialNumber] = useState("");
  const [confirmStatus, setConfirmStatus] = useState(true);
  const [messageDialogExtra, setMessageDialogExtra] = useState(false);
  const [messageDialogOK, setMessageDialogOK] = useState(true);
  const [companyCodeSet, setCompanyCodeSet] = useState([]);
  const [plantCodeSet, setPlantCodeSet] = useState([]);
  const [vendorDetailsSet, setVendorDetailsSet] = useState([]);
  const [taskstatusSet, setTasksttusSet] = useState([]);
  const [disableButton, setDisableButton] = useState(true);
  const [selectedRow, setSelectedRow] = useState([]);
  const [selectedDetails, setSelectedDetails] = useState([]);
  const [downloadError, setdownloadError] = useState(false);
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [displayedFields, setDisplayedFields] = useState([]);
  const [filterFieldData, setFilterFieldData] = useState({});
  const [matType, setMatType] = useState([]);
  const [matGroup, setMatGroup] = useState([]);
  const [viewDetailpage, setViewDetailpage] = useState(false);
  const [matNumber, setMatNumber] = useState([]);
  const [dynamicOptions, setDynamicOptions] = useState([]);
  const [plantForWarehouse, setPlantForWarehouse] = useState([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogOpenCreate, setDialogOpenCreate] = useState(false);
  const [fullWidth, setFullWidth] = useState(true);
  const [maxWidth, setMaxWidth] = useState("sm");
  const [enableDocumentUpload, setEnableDocumentUpload] = useState(false);
  const [successMsg, setsuccessMsg] = useState(false);
  const [newCostCenterName, setNewCostCenterName] = useState("");
  const [newCompanyCode, setNewComapnyCode] = useState("");
  // const [newCombinedCostcenter, setNewCombinedCostcenter] = useState("");
  const [newValidFromDate, setNewValidFromDate] = useState(newDate);
  const [newValidToDate, setNewValidToDate] = useState(validToDate);
  const [newControllingArea, setNewControllingArea] = useState("");
  const [newControllingAreaCopyFrom, setNewControllingAreaCopyFrom] =
    useState("");
  const [newCostCenterCopyFrom, setNewCostCenterCopyFrom] =
    useState("");
  const [newCostCenter, setNewCostCenter] = useState("");
  const [isValidationError, setIsValidationError] = useState(false);
  const [isValidationErrorwithCopy, setIsValidationErrorwithCopy] = useState(false);
  const [checkValidationCostCenter, setCheckValidationCostCenter] =
    useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  // const [handleMassMode, setHandleMassMode] = useState("");
  const [openButton, setOpenButton] = useState(false);
  const [openButtonCreateSingle, setOpenButtonCreateSingle] = useState(false);
  const anchorRef = React.useRef(null);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [openButtonChange, setOpenButtonChange] = useState(false);
  const [openButtonCreate, setOpenButtonCreate] = useState(false);
  const anchorRefChange = React.useRef(null);
  const anchorRefCreate = React.useRef(null);
  const [selectedIndexChange, setSelectedIndexChange] = useState(0);
  const [selectedIndexCreate, setSelectedIndexCreate] = useState(0);
  const [isCheckboxSelected, setIsCheckboxSelected] = useState(true);
  const [isAnyFieldEmpty, setIsAnyFieldEmpty] = useState(false);
  const [isAnyFieldEmptyForSingleCopy, setIsAnyFieldEmptyForSingleCopy] = useState(false);
  const handleMassModeCC = useSelector(
    (state) => state.costCenter.handleMassMode
  );
  const options = ["Create Multiple", "Upload Template ", "Download Template "];
  const optionsCreateSingle = ["Create Single", "With Copy", "Without Copy"];
  const optionsChange = ["Change Multiple", "Upload Template", "Download Template"];

  const rmSearchForm = useSelector((state) => state.commonFilter["CostCenter"]);
  const formcontroller_SearchBar = useSelector(
    (state) => state.commonSearchBar["CostCenter"]
  );
  const dropDownData = useSelector((state) => state?.AllDropDown?.dropDown);
  const handleDialogClickOpen = () => {
    setDialogOpen(true);
  };
  const handleDialogClickOpenWithCopy = () => {
    setDialogOpenCreate(true);
  };

  const sendNewCostCenterData = {
    companyCode: { newCompanyCode },
    costCenterName: { newCostCenterName },
    controllingAreaData: { newControllingArea },
    controllingAreaDataCopy: { newControllingAreaCopyFrom },
    // combinedCostCenter:{newCombinedCostcenter},
    //costCenterDataCopy:{newCostCenterCopyFrom}, 
    costCenter: { newCostCenter },
    // costCenter: "TUK1-PR43",
    validFromDate: { newValidFromDate },
    validToDate: { newValidToDate },
  };
  //console.log("typecc", dropdownData.CostCenter);
  //console.log("newValidFromDate", selectedRows, handleMassModeCC);
  const duplicateCheck = () => {

    //console.log(newControllingArea?.code,newCompanyCode?.code,newCostCenterName,"newCostCenterName")
    let selectedControllingArea =
      sendNewCostCenterData?.controllingAreaData?.newControllingArea?.code;
    let selectedCompanyCode =
      sendNewCostCenterData?.companyCode?.newCompanyCode?.code;
    let selectedCostCenterName =
      sendNewCostCenterData?.costCenterName?.newCostCenterName;
    console.log(newControllingAreaCopyFrom, newCostCenterCopyFrom, selectedCostCenterName,
      "selectedCostCenterName")

    if ((newControllingArea?.code === undefined || newControllingArea?.code === '') || (newCompanyCode?.code === undefined || newCompanyCode?.code === '') || (newCostCenterName === undefined || newCostCenterName === '')) {
      setNewCostCenterValid(false)
      setIsValidationError(true)
      setIsAnyFieldEmpty(true)
      return;
    }
    else {
      if (newCostCenterName.length !== 6) {
        setNewCostCenterValid(true)
        setIsValidationError(false)
        return;
        //duplicateCheck()
      } else {

        setNewCostCenterValid(false)
      }
      setIsValidationError(false)
      // setIsAnyFieldEmpty(false)
    }

    let result = selectedControllingArea.concat(
      "$$",
      selectedCompanyCode,
      selectedCostCenterName
    );
    // setNewCombinedCostcenter(result);
    console.log("sendNewCostCenterData", rmSearchForm);
    // let ctrlAreaCCToCheck = (sendNewCostCenterData?.controllingAreaData?.newControllingArea).join(sendNewCostCenterData?.costCenterName?.newCostCenterName)
    // console.log("ctrlAreaCCToCheck",(sendNewCostCenterData?.controllingAreaData?.newControllingArea).join(sendNewCostCenterData?.costCenterName?.newCostCenterName));
    setIsLoading(true);
    const hSuccess = (data) => {
      setIsLoading(false);
      //console.log("dupli", data);
      if (data.body.length > 0) {
        setCheckValidationCostCenter(true);
      } else {
        navigate("/masterDataCockpit/costCenter/newSingleCostCenter", {
          state: sendNewCostCenterData,
        });
      }
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/alter/fetchCoAreaCCDupliChk?ctrlAreaCCToCheck=${result}`,
      "get",
      hSuccess,
      hError
    );
  };
  const duplicateCheckWithCopy = () => {
    
    console.log(newControllingAreaCopyFrom,newCostCenterCopyFrom,"newControllingAreaCopyFrom?.code ")
    if ((newControllingArea?.code === undefined || newControllingArea?.code === '') || (newCompanyCode?.code === undefined || newCompanyCode?.code === '') || (newCostCenterName === undefined || newCostCenterName === '') ||(newControllingAreaCopyFrom?.code === undefined || newControllingAreaCopyFrom?.code === '')||(newCostCenter?.code === undefined || newCostCenter?.code === '')) { 
      setNewCostCenterValidWithCopy(false)
      setIsValidationErrorwithCopy(true)
      return;
    }
    else {
      if (newCostCenterName.length !== 6) {
        setNewCostCenterValidWithCopy(true)
        setIsValidationErrorwithCopy(false)
        return;
        //duplicateCheck()
      } else {

        setNewCostCenterValidWithCopy(false)
      }
      setIsValidationErrorwithCopy(false)
      // setIsAnyFieldEmpty(false)
    }
    //console.log(newControllingAreaCopyFrom,"newControllingAreaCopyFrom")
    let selectedControllingArea =
      sendNewCostCenterData?.controllingAreaData?.newControllingArea.code;
    let selectedCompanyCode =
      sendNewCostCenterData?.companyCode?.newCompanyCode.code;
    let selectedCostCenterName =
      sendNewCostCenterData?.costCenterName?.newCostCenterName;

    let result = selectedControllingArea.concat(
      "$$",
      selectedCompanyCode,
      selectedCostCenterName
    );

    console.log(sendNewCostCenterData?.costCenter?.newCostCenter, "sendNewCostCenterData==========")
    console.log(sendNewCostCenterData?.costCenterName?.newCostCenterName, "sendNewCostCenterData==========")
    setIsLoading(true);
    const hSuccess = (data) => {
      setIsLoading(false);
      console.log("dupli", data);
      if (data.body.length > 0) {
        setCheckValidationCostCenter(true);
      } else {
        navigate(
          `/masterDataCockpit/costCenter/displayCopyCostCenter/${sendNewCostCenterData?.costCenter?.newCostCenter?.code}`,
          {
            state: sendNewCostCenterData,
          }
        );
      }
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/alter/fetchCoAreaCCDupliChk?ctrlAreaCCToCheck=${result}`,
      "get",
      hSuccess,
      hError
    );
  };
  const handleDialogProceed = () => {
    duplicateCheck();
  };
  const handleDialogProceedWithCopy = () => {
    duplicateCheckWithCopy();
  };


  //console.log(newCostCenterValid,"NewCostCenterValid===========")

  //const check
  const handleDialogClose = () => {
    setDialogOpen(false);
    setIsValidationError(false)
    setButtonDisabledForSingleWithoutCopy(true)
    setNewCostCenterValid(false)
    setNewCompanyCodeValid(false)
    setNewControllingArea('');
    setNewComapnyCode('');
    setNewCostCenterName('')
  };
  const handleDialogCloseCreate = () => {
    setIsValidationError(false)
    setIsValidationErrorwithCopy(false)
    setNewCostCenterValidWithCopy(false)
    setNewControllingArea('');
    setNewComapnyCode('');
    setNewCostCenterName('')
    setNewControllingAreaCopyFrom('')
    setNewCostCenterCopyFrom('')
    setDialogOpenCreate(false);
  };
  const handleDialogCloseCreateSingle = (event) => {
    if (
      anchorRefCreate.current &&
      anchorRefCreate.current.contains(event.target)
    ) {
      return;
    }
    //setDialogOpenCreate(false);
    setOpenButtonCreate((prevOpen) => !prevOpen);
  };
  const handleCostCenterName = (e) => {
    if (e.target.value !== null) {
      var tempCostCenterName = e.target.value;

      let tempFilterData = {
        ...rmSearchForm,
        costCenterName: tempCostCenterName,
      };
      dispatch(
        commonFilterUpdate({
          module: "CostCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleCostCenter = (e) => {
    if (e.target.value !== null) {
      var tempCostCenter = e.target.value;

      let tempFilterData = {
        ...rmSearchForm,
        costCenter: tempCostCenter,
      };
      dispatch(
        commonFilterUpdate({
          module: "CostCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleControllingArea = (e, value) => {
    if (true) {
      var tempControllingArea = value;

      let tempFilterData = {
        ...rmSearchForm,
        controllingArea: tempControllingArea,
      };
      dispatch(
        commonFilterUpdate({
          module: "CostCenter",
          filterData: tempFilterData,
        })
      );
      getHierarchyArea(tempFilterData);
      getProfitCenter(tempFilterData);
      getCompanyCode(tempFilterData);
      // getCostCenterCategory(tempFilterData);
    }
  };
  const handleCompanyCode = (e, value) => {
    if (true) {
      var tempCompanyCode = value;

      let tempFilterData = {
        ...rmSearchForm,
        companyCode: tempCompanyCode,
      };
      dispatch(
        commonFilterUpdate({
          module: "CostCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleProfitCenter = (e, value) => {
    if (true) {
      var tempProfitCenter = value;

      let tempFilterData = {
        ...rmSearchForm,
        profitCenter: tempProfitCenter,
      };
      dispatch(
        commonFilterUpdate({
          module: "CostCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleHierarchyArea = (e, value) => {
    if (true) {
      var tempHierarchyArea = value;

      let tempFilterData = {
        ...rmSearchForm,
        hierarchyArea: tempHierarchyArea,
      };
      dispatch(
        commonFilterUpdate({
          module: "CostCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleCostCenterCategory = (e, value) => {
    if (true) {
      var tempCostCenterCategory = value;

      let tempFilterData = {
        ...rmSearchForm,
        costCenterCategory: tempCostCenterCategory,
      };
      dispatch(
        commonFilterUpdate({
          module: "CostCenter",
          filterData: tempFilterData,
        })
      );
    }
  };

  // const handleCreatedBy = (e, value) => {
  //   if (true) {
  //     var tempCreatedBy = value;

  //     let tempFilterData = {
  //       ...rmSearchForm,
  //       createdBy: tempCreatedBy,
  //     };
  //     dispatch(
  //       commonFilterUpdate({
  //         module: "MaterialMaster",
  //         filterData: tempFilterData,
  //       })
  //     );
  //   }
  // };
  // const handleChangedBy = (e, value) => {
  //   if (true) {
  //     var tempChangedBy = value;

  //     let tempFilterData = {
  //       ...rmSearchForm,
  //       changedBy: tempChangedBy,
  //     };
  //     dispatch(
  //       commonFilterUpdate({
  //         module: "MaterialMaster",
  //         filterData: tempFilterData,
  //       })
  //     );
  //   }
  // };

  // const handleSelection = (event) => {
  //   const selectedItems = event.target.value;
  //   fetchOptionsForDynamicFilter(dynamicDataApis[selectedItems])
  //   setSelectedOptions(selectedItems);
  //   setDisplayedFields([]);
  // };

  let dynamicDataApis = {
    "Person Responsible": `/${destination_CostCenter}/data/getSalesOrg`,
    "Business Area": `/${destination_CostCenter}/data/getBusinessArea`,
    "Functional Area": `/${destination_CostCenter}/data/getFunctionalArea`,
    // "Transportation Group" : `/${destination_MaterialMgmt}/data/getTransportationGroup`,
    // "Batch Management" : `/${destination_MaterialMgmt}/data/getBatchManagement`,
    // "Old Material Number" : `/${destination_MaterialMgmt}/data/getOldMaterialNo`,
  };
  // console.log('rmdatarows', rmDataRows)
  const handleSelection = (event) => {
    const selectedItems = event.target.value;
    setSelectedOptions(selectedItems);
    setDisplayedFields([]);
    console.log("selected field", event.target.value);

    selectedItems.forEach(async (selectedItem) => {
      const apiEndpoint = dynamicDataApis[selectedItem];
      fetchOptionsForDynamicFilter(apiEndpoint);
    });
  };

  const handleAddFields = () => {
    const numSelected = selectedOptions.length;
    const newFields = Array.from({ length: numSelected }, (_, index) => ({
      id: index,
      value: "",
    }));
    setDisplayedFields(newFields);
  };

  const handleFieldChange = (fieldId, value) => {
    setDisplayedFields(
      (selectedOptions) => selectedOptions.map((option) => option)
      // prevFields.map((field) => (field.id === fieldId ? { ...field, value } : field))
    );
  };
  const items = [
    { title: "Person Responsible" },
    { title: "Business Area" },
    { title: "Functional Area" },
    // Add more options as needed
  ];
  /*const titleToFieldMapping = {
    "Task ID": "taskId",
    Status: "status",
    SalesOrganization: "salesOrg",
    Division: "division",
    OldMaterialNumber: "oldMaterialNumber",
    "Lab/Office": "labOffice",
    "Transportation Group": "transportationGroup",
    "Batch management": "batchManagement",
    // Add more mappings as needed
  };*/

  const titleToFieldMapping = {
    "Task ID": "taskId",
    Status: "status",
    SalesOrganization: "salesOrg",
    Division: "division",
    OldMaterialNumber: "oldMaterialNumber",
    "Lab/Office": "labOffice",
    "Transportation Group": "transportationGroup",
    "Batch management": "batchManagement",
    "Person Responsible": 'personResponsible',
    "Business Area": 'businessArea',
    "Functional Area": 'functionalArea'
    // Add more mappings as needed
  };

  const getUserResponsible = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "UserResponsible", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getUserResponsible`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCostCenterCategory = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CostCenterCategory", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getCostCenterCategory`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCostCenter = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CostCenter", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getCostCenter`,
      "get",
      hSuccess,
      hError
    );
  };
  const getProfitCenter = (CA) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ProfitCenterSearch", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getProfitCenterAsPerControllingArea?controllingArea=${CA.controllingArea?.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getHierarchyArea = (CA) => {
    console.log("CA",CA);
    // var HA = "TZUS";
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "HierarchyAreaSearch", data: data.body }));
      console.log("data", data);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getHierarchyArea?controllingArea=${CA.controllingArea?.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getControllingArea = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ControllingArea", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getControllingArea`,
      "get",
      hSuccess,
      hError
    );
  };

  const getCompanyCode = (CA) => {
    // var HA = "TZUS";
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CompCodeSearch", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/data/getCompCode?contrllingArea=${CA?.controllingArea?.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCompanyCodeBasedOnControllingArea = (value) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CompanyCode", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getCompanyCodeBasedOnControllingArea?controllingArea=${value.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCompanyCodeForCreate = (value) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CompanyCode", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getCompanyCodeBasedOnControllingArea?controllingArea=${value.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCompanyCodeBasedOnControllingAreaCopy = (value) => {


    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CompCodeCopy", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getCostCenterBasedOnCOA?controllingArea=${value.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getActyIndepFormPlngTemp = () => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "ActyIndepFormPlngTemp", data: data.body })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getCostCenterCategory`,
      "get",
      hSuccess,
      hError
    );
  };
  const getActyDepFormPlngTemp = () => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "ActyDepFormPlngTemp", data: data.body })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getCostCenterCategory`,
      "get",
      hSuccess,
      hError
    );
  };
  const getActyIndepAllocTemp = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ActyIndepAllocTemp", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getCostCenterCategory`,
      "get",
      hSuccess,
      hError
    );
  };
  const getActyDepAllocTemp = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ActyDepAllocTemp", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getCostCenterCategory`,
      "get",
      hSuccess,
      hError
    );
  };
  const getTemplActStatKeyFigure = () => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "TemplActStatKeyFigure", data: data.body })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getCostCenterCategory`,
      "get",
      hSuccess,
      hError
    );
  };
  const getBusinessArea = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "BusinessArea", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getBusinessArea`,
      "get",
      hSuccess,
      hError
    );
  };
  const getFunctionalArea = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "FunctionalArea", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getFunctionalArea`,
      "get",
      hSuccess,
      hError
    );
  };

  const getCostingSheet = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CostingSheet", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getCostingSheet`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCountryOrRegion = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CountryReg", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getCountry`,
      "get",
      hSuccess,
      hError
    );
  };
  const getJurisdiction = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "Jurisdiction", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getJurisdiction`,
      "get",
      hSuccess,
      hError
    );
  };
  const getRegion = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "Region", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getRegion`,
      "get",
      hSuccess,
      hError
    );
  };

  const getCostCenterBasicDetails = () => {
    let viewName = "Basic Data";
    const hSuccess = (data) => {
      dispatch(setCostCenterBasicDataTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getControlCostCenter = () => {
    let viewName = "Control";
    const hSuccess = (data) => {
      dispatch(setCostCenterControlTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getTemplatesCostCenter = () => {
    let viewName = "Templates";
    const hSuccess = (data) => {
      dispatch(setCostCenterTemplatesTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getAddressCostCenter = () => {
    let viewName = "Address";
    const hSuccess = (data) => {
      dispatch(setCostCenterAddressTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCommunicationCostCenter = () => {
    let viewName = "Communication";
    const hSuccess = (data) => {
      dispatch(setCostCenterCommunicationTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getHistoryCostCenter = () => {
    let viewName = "History";
    const hSuccess = (data) => {
      dispatch(setCostCenterHistoryTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getLanguageKey = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "LanguageKey", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getLanguageKey`,
      "get",
      hSuccess,
      hError
    );
  };

  useEffect(() => {
    // getUserResponsible();
    getCostCenterCategory();
    // getBusinessArea();
    // getFunctionalArea();
    // getCostingSheet();
    // getCountryOrRegion();
    // getJurisdiction();
    // getRegion();
    // getLanguageKey();
    getCostCenterBasicDetails();
    getControlCostCenter();
    getTemplatesCostCenter();
    getAddressCostCenter();
    getCommunicationCostCenter();
    getHistoryCostCenter();
    getCostCenter();
    // getProfitCenter();
    getControllingArea();
    // getHierarchyArea();
    // getCompanyCode();
    //check_all_fields_are_filled_are_empty_or_not();
    dispatch(setTaskData({}))
    dispatch(clearCostCenter());
  }, []);
  // const fetchOptionsForDynamicFilter = (apiEndpoint) => {
  //   const hSuccess = (data) => {
  //     setDynamicOptions({
  //       ...dynamicOptions,
  //       [apiEndpoint]: data.body,

  //     });
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(apiEndpoint, "get", hSuccess, hError);
  // };
  const fetchOptionsForDynamicFilter = (apiEndpoint) => {
    const hSuccess = (data) => {
      const newOptions = data.body;

      // Merge the new options with the existing dynamicOptions
      //setDynamicOptions((prevOptions) => [...prevOptions, ...newOptions]);
      setDynamicOptions((newOptions));
    };

    console.log(dynamicOptions, "dinamicoptions==================");
    const hError = (error) => {
      console.log(error);
    };
    doAjax(apiEndpoint, "get", hSuccess, hError);
  };
  const clearSearchBar = () => {
    setMaterialNumber("");
  };
  const getMaterialNoGlobalSearch = (fetchSkip) => {
    // var tempCostCenter = value;

    // let tempFilterData = {
    //   ...rmSearchForm,
    //   costCenter: tempCostCenter,
    // };
    // dispatch(
    //   commonFilterUpdate({
    //     module: "CostCenter",
    //     filterData: tempFilterData,
    //   })
    // );
    console.log("rmSearchForm", formcontroller_SearchBar);
    setTableLoading(true);
    if (!fetchSkip) {
      setPage(0);
      setPageSize(10);
      setSkip(0);
    }
    let payload = {
      costCenterName: "",
      costCenter: formcontroller_SearchBar?.number ?? "",
      controllingArea: "",
      companyCode: "",
      profitCenter: "",
      heirarchyArea: "",
      costCenterCategory: "",
      createdBy: "",
      fromDate: "",
      toDate: "",
      personResponsible: "",
      businessArea: "",
      functionalArea: "",
      top: 1000,
      skip: fetchSkip ?? 0,
    };
    const hSuccess = (data) => {
      var rows = [];
      for (let index = 0; index < data?.body?.list?.length; index++) {
        var tempObj = data?.body?.list[index];
        if (true) {
          var tempRow = {
            id: uuidv4(),
            description: tempObj.Description,
            controllingArea: tempObj.controllingArea,
            companyCode: tempObj.CompanyCode,
            profitCenter: tempObj.ProfitCenter,
            hierarchyArea: tempObj.HeirarchyArea,
            costCenterCategory: tempObj.CCtrCategory,
            costCenter: tempObj.costCenter,
            CostCenterName: tempObj.CostCenterName,

            // id: uuidv4(),
            // materialNumber: tempObj["MaterialNo"],
            // materialType: `${tempObj["Materialtype"]} - ${tempObj["MaterialTypeDesc"]}`,
            // materialDesc: tempObj["MaterialDescrption"],
            // materialGroup: `${tempObj["MaterialGroup"]} - ${tempObj["materialGroupDesc"]}`,
            // costCenterName: tempObj["CostCenterName"],
            // plant:
            //   tempObj["Plant"] !== ""
            //     ? `${tempObj["Plant"]} - ${tempObj["Plantname"]}`
            //     : "Not Available",
            // createdOn: moment(tempObj.CreatedOn).format("DD MMM YYYY"),
            // changedOn: moment(tempObj.LastChange).format("DD MMM YYYY"),
            // changedBy: tempObj["ChangedBy"],
            // createdBy: tempObj.CreatedBy,
          };
          rows.push(tempRow);
        }
      }
      // rows.sort(
      //   (a, b) =>
      //     moment(a.createdOn, "DD MMM YYYY HH:mm") -
      //     moment(b.createdOn, "DD MMM YYYY HH:mm")
      // );
      setRmDataRows(rows);
      setTableLoading(false);
      setroCount(rows.length);
      setCount(data?.body?.count);
    };
    let hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getCostCentersBasedOnAdditionalParams`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  /* Setting Default Dates */
  const presentDate = new Date();
  const backDate = new Date();
  backDate.setDate(backDate.getDate() - 15);

  const [date, setDate] = useState([backDate, presentDate]);
  const [date1, setDate1] = useState([backDate, presentDate]);

  const handleDate = (e) => {
    // if (e !== null) setDate(e.reverse());
    if (e !== null) {
      var createdOn = e.reverse();
      dispatch(
        commonFilterUpdate({
          module: "CostCenter",
          filterData: {
            ...rmSearchForm,
            createdOn: createdOn,
          },
        })
      );
    }
  };

  const handleDate1 = (e) => {
    if (e !== null) setDate1(e.reverse());
  };

  const handleSnackBarClickaccept = () => {
    setOpenSnackBaraccept(true);
  };

  const handleSnackBarCloseaccept = (event, reason) => {
    if (reason === "clickaway") {
      return;
    }

    setOpenSnackBaraccept(false);
  };

  const handleUserName = (e) => {
    setUserName(e.target.value);
  };

  console.log('newcontrollingarea', newControllingArea)

  // Get Filter Data
  const getFilter = (fetchSkip) => {
    console.log("rmSearchForm", rmSearchForm);
    setTableLoading(true);
    if (!fetchSkip) {
      setPage(0);
      setPageSize(10);
      setSkip(0);
    }
    console.log(filterFieldData,"filterFieldData============")
    let payload = {
      costCenterName: rmSearchForm?.costCenterName ?? "",
      costCenter: "",
      controllingArea: rmSearchForm?.controllingArea?.code ?? "",
      companyCode: rmSearchForm?.companyCode?.code ?? "",
      profitCenter: rmSearchForm?.profitCenter?.code ?? "",
      heirarchyArea: rmSearchForm?.hierarchyArea?.code ?? "",
      costCenterCategory: rmSearchForm?.costCenterCategory?.code ?? "",
      createdBy: "",
      fromDate: "",
      toDate: "",
      personResponsible: filterFieldData?.["Person Responsible"] ?? "",
      businessArea: filterFieldData?.["Business Area"]?.code ?? "",
      functionalArea: filterFieldData?.["Functional Area"]?.code ?? "",
      top: 1000,
      skip: fetchSkip ?? 0,
    };
    const hSuccess = (data) => {
      console.log("data", data.body.list);
      var rows = [];
      for (let index = 0; index < data?.body?.list?.length; index++) {
        var tempObj = data?.body?.list[index];
        console.log("hshshsh", tempObj);

        var tempRow = {
          id: uuidv4(),
          description: tempObj.Description,
          controllingArea: tempObj.controllingArea,
          companyCode: tempObj.CompanyCode,
          profitCenter: tempObj.ProfitCenter,
          hierarchyArea: tempObj.HeirarchyArea,
          costCenterCategory: tempObj.CCtrCategory,
          costCenter: tempObj.costCenter,
          CostCenterName: tempObj?.CostCenterName,
          businessArea:
          tempObj["BusinessArea"] !== ""
            ? `${tempObj["BusinessArea"]}`
            : "Not Available",

          functionalArea:
            tempObj["FunctionalArea"] !== ""
              ? `${tempObj["FunctionalArea"]}`
              : "Not Available",
          personResponsible:
            tempObj["PersonResponsible"] !== ""
              ? `${tempObj["PersonResponsible"]}`
              : "Not Available",
          // createdOn: moment(tempObj.CreatedOn).format("DD MMM YYYY"),
          // changedOn: moment(tempObj.LastChange).format("DD MMM YYYY"),
          // changedBy: tempObj["ChangedBy"],
          // createdBy: tempObj.CreatedBy,
          // division: tempObj.Division,
          // oldMaterialNumber: tempObj.OldMaterialNumber,
          // labOffice: tempObj.LabOffice,
          // transportationGroup: tempObj.TrnsportGroup,
          // salesOrg: tempObj.SalesOrg,
        };
        rows.push(tempRow);
      }
      console.log("tempobj", tempRow);
      console.log("tempObH", tempObj);
      rows.sort(
        (a, b) =>
          moment(a.createdOn, "DD MMM YYYY HH:mm") -
          moment(b.createdOn, "DD MMM YYYY HH:mm")
      );
      setRmDataRows(rows.reverse());
      setTableLoading(false);
      setroCount(rows.length);
      setCount(data?.body?.count);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getCostCentersBasedOnAdditionalParams`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const [userList, setUserList] = useState([]);

  const moduleFilterData = [
    {
      type: "singleSelect",
      filterName: "company",
      // filterData: masterData?.companyCode,
      filterTitle: "Company",
    },
    {
      type: "singleSelect",
      filterName: "vendor",
      filterData: vendorDetailsSet,
      filterTitle: "Supplier",
    },
    {
      type: "text",
      filterName: "poNum",
      filterTitle: "PO Number",
    },
    // {
    //   type: "multiSelect",
    //   filterName: "poStatus",
    //   filterData: keyword["Return Status"],
    //   filterTitle: "Return Status",
    // },
    {
      type: "autoComplete",
      filterName: "createdBy",
      filterData: userList,
      filterTitle: "Created By",
    },
    {
      type: "singleSelectKV",
      filterName: "returnType",
      filterData: {
        "Debit Note": "Debit Note",
        Replacement: "Replacement",
      },
      filterTitle: "Return Type",
    },
    {
      type: "singleSelect",
      filterName: "plant",
      filterData: plantCodeSet,
      filterTitle: "Plant",
    },
    {
      type: "dateRange",
      filterName: "createdOn",
      filterTitle: "Return Request Date",
    },
  ];

  const [confirmation, setconfirmation] = useState([]);
  const [confirmationText, setConfirmationText] = useState(null);
  const [poHeader, setPoHeader] = useState(null);
  const [roCount, setroCount] = useState(0);
  const [showBtmNav, setShowBtmNav] = useState(false);
  const [opendialog, setOpendialog] = useState(false);
  const [openSnackbarDialog, setOpenSnackbarDialog] = useState(false);
  const [opendialog2, setOpendialog2] = useState(false);
  const [opendialog3, setOpendialog3] = useState(false);
  const [openforwarddialog, setOpenforwarddialog] = useState(false);
  const [rejectInputText, setRejectInputText] = useState("");
  const [acceptInputText, setAcceptInputText] = useState("");
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState("");
  const [messageDialogMessage, setMessageDialogMessage] = useState();

  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };

  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };

  const [anchorEl_Preset, setAnchorEl] = useState(null);
  const openAnchor = Boolean(anchorEl_Preset);

  const handleSnackBarOpen = () => {
    setopenSnackbar(true);
  };

  const [presets, setPresets] = useState(null);
  const [presetName, setPresetName] = useState(null);

  const handleClear = () => {
    // setMaterialFilterDetails({
    //   number: null,
    //   type: null,
    //   description: "",
    //   group: null,
    //   plant: null,
    //   createdBy: "",
    //   createdOn: [null, null],
    // });
    dispatch(commonFilterClear({ module: "CostCenter" }));
  };

  const uploadExcel = (file) => {
    setIsLoading(true);
    console.log(file);
    const formData = new FormData();
    [...file].forEach((item) => formData.append("files", item));
    if(handleMassModeCC === 'Change'){
        var uploadUrl = `/${destination_CostCenter}/massAction/getAllCostCenterFromExcelForMassChange`;
    }else{
        var uploadUrl = `/${destination_CostCenter}/massAction/getAllCostCenterFromExcel`;
    }
    const hSuccess = (data) => {
      setIsLoading(false);
      console.log(data, "example");
      dispatch(setControllingArea(data?.body?.controllingArea));
      //navigate(`/masterDataCockpit/costCenter/createMultipleCostCenter`);
      // setIsLoading();
      if (data.statusCode === 200) {
        setEnableDocumentUpload(false);
        dispatch(setMultipleCostCenterData(data?.body));
        setMessageDialogTitle("Create");
        setMessageDialogMessage(`${file.name} has been Uploaded Succesfully`);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
        navigate(`/masterDataCockpit/costCenter/createMultipleCostCenter`);
      } else {
        setEnableDocumentUpload(false);
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Error Uploading Cost Center Excel");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(uploadUrl, "postformdata", hSuccess, hError, formData);
  };
  const handleSelectionModelChange = (newSelection) => {
    console.log("newselection", newSelection);
    // handlesna;
    setSelectedRows(newSelection);
    let filterValueColumns = columns.map((t) => t.field);
    const selectedRowsDetails = rmDataRows.filter((row) =>
      newSelection.includes(row.id)
    );
    let requiredArrayDetails = [];
    selectedRowsDetails.map((s) => {
      console.log("sssssss", s);
      let requiredObject = {};
      filterValueColumns.forEach((y) => {
        console.log("yyyyy", s[y]);
        if (s[y] !== null) {
          requiredObject[y] = s[y] || "";
        }
      });
      requiredArrayDetails.push(requiredObject);
      setSelectedMassChangeRowData(requiredArrayDetails);
      console.log("requiredArrayDetails", requiredArrayDetails);
    });
  };

  // const onRowsSelectionHandler = (ids) => {
  //   //Selected Columns stored here
  //   const selectedRowsData = ids.map((id) =>
  //     rmDataRows.find((row) => row.id === id)
  //   );
  //   var compCodes = selectedRowsData.map((row) => row.company);
  //   var companySet = new Set(compCodes);
  //   var vendors = selectedRowsData.map((row) => row.vendor);
  //   var vendorSet = new Set(vendors);
  //   var paymentTerms = selectedRowsData.map((row) => row.paymentTerm);
  //   var paymentTermsSet = new Set(paymentTerms);
  //   if (selectedRowsData.length > 0) {
  //     if (companySet.size === 1) {
  //       if (vendorSet.size === 1) {
  //         if (paymentTermsSet.size !== 1) {
  //           setDisableButton(true);
  //           setMessageDialogTitle("Error");
  //           setMessageDialogMessage(
  //             "Invoice cannot be generated for vendors with different payment terms"
  //           );
  //           setMessageDialogSeverity("danger");
  //           handleMessageDialogClickOpen();
  //         } else setDisableButton(false);
  //       } else {
  //         setDisableButton(true);
  //         setMessageDialogTitle("Error");
  //         setMessageDialogMessage(
  //           "Invoice cannot be generated for multiple suppliers"
  //         );
  //         setMessageDialogSeverity("danger");
  //         handleMessageDialogClickOpen();
  //       }
  //     } else {
  //       setDisableButton(true);
  //       setMessageDialogTitle("Error");
  //       setMessageDialogMessage(
  //         "Invoice cannot be generated for multiple companies"
  //       );
  //       setMessageDialogSeverity("danger");
  //       handleMessageDialogClickOpen();
  //     }
  //   } else {
  //     setDisableButton(true); //Enable the Create E-Invoice button when at least one row is selected and no two companys or vendors are same
  //   }
  //   setSelectedRow(ids); //Setting the ids(PO Numbers) of selected rows
  //   setSelectedDetails(selectedRowsData); //Setting the entire data of a selected row
  // };
  function refreshPage() {
    dispatch(commonFilterClear({ module: "CostCenter" }));
    getFilter();
  }

  const [company, setCompany] = useState([]);
  const [Companyid, setCompanyid] = useState([]);

  // let { poId } = useParams();
  const [open, setOpen] = useState(false);
  const [matAnchorEl, setMatAnchorEl] = useState(null);
  const [materialDetails, setMaterialDetails] = useState(null);
  const [itemDataRows, setItemDataRows] = useState([]);

  const handlePODetailsClick = (event) => {
    setOpendialog3(true);
  };

  const matOpen = Boolean(matAnchorEl);
  const popperId = matOpen ? "simple-popper" : undefined;

  const handleClose = () => {
    setOpen(false);
  };

  const [poNum, setPONum] = useState(null);
  const [id, setID] = useState("");
  const columns = [
    {
      field: "costCenter",
      headerName: "Cost Center",
      editable: false, //dd
      flex: 1,
    },
    {
      field: "CostCenterName",
      headerName: "Cost Center Name",
      editable: false, //dd
      flex: 1,
    },
    {
      field: "description",
      headerName: "Description",
      editable: false, //text
      flex: 1,
    },
    {
      field: "controllingArea",
      headerName: "Controlling Area",
      editable: false, //dd
      flex: 1,
    },
    {
      field: "companyCode",
      headerName: "Company Code",
      editable: false, //dd
      flex: 1,
    },
    {
      field: "profitCenter",
      headerName: "Profit Center", //dd
      editable: false,
      flex: 1,
    },
    {
      field: "hierarchyArea",
      headerName: "Hierarchy Area", //dd
      editable: false,
      flex: 1,
    },
    {
      field: "costCenterCategory",
      headerName: "Cost Center Category", //dd
      editable: false,
      flex: 1,
    },
  ];
  const dynamicFilterColumns = selectedOptions
    .map((option) => {
      const field = titleToFieldMapping[option]; // Get the corresponding field from the mapping
      if (!field) {
        return null; // Handle the case when the field doesn't exist in the mapping
      }
      return {
        field: field, // Use the field name from the mapping
        headerName: option,
        editable: false,
        flex: 1,
      };
    })
    .filter((column) => column !== null); // Remove any null columns

  const allColumns = [
    ...columns,
    ...dynamicFilterColumns,
    // Other fixed and dynamic columns as needed
  ];
  const capitalize = (str) => {
    //  str.map((str)=>{
    const arr = str.split(" ");
    for (var i = 0; i < arr.length; i++) {
      arr[i] = arr[i].charAt(0) + arr[i].slice(1).toLowerCase();
    }

    const str2 = arr.join(" ");
    return str2;
    //  })
  };

  useEffect(() => {
    getFilter();
    // functions_PresetFilter.getFilterPresets();
  }, []);




  // useEffect(() => {
  //   if ((rmSearchForm?.company).length) {
  //     getVendorDetails();
  //     getPlantCodeSet()
  //   }
  // }, [rmSearchForm?.company]);

  // let serviceRequestForm_Component = new createServiceRequestForm(Status_ServiceReqForm, setStatus_ServiceReqForm)
  // <-- Function for taking screenshot (Export button) -->
  let ref_elementForExport = useRef(null);
  // let exportAsPicture = () => {
  //   setTimeout(() => {
  //     captureScreenShot("Material-Single");
  //   }, 100);
  // };
  const functions_ExportAsExcel = {
    convertJsonToExcel: () => {
      let excelColumns = [];
      columns.forEach((item) => {
        if (item.headerName.toLowerCase() !== "action" && !item.hide) {
          excelColumns.push({ header: item.headerName, key: item.field });
        }
      });
      saveExcel({
        fileName: `Cost Center Data-${moment(presentDate).format(
          "DD-MMM-YYYY"
        )}`,
        columns: excelColumns,
        rows: rmDataRows,
      });
    },
    button: () => {
      return (
        <Button
          sx={{
            textTransform: "capitalize",
            position: "absolute",
            right: 0,
            top: 0,
          }}
          onClick={() => functions_ExportAsExcel.convertJsonToExcel()}
        >
          Download
        </Button>
      );
    },
  };

  const handleCreateDownload = () => {
    let hSuccess = (response) => {
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");
      link.href = href;
      link.setAttribute("download", `Cost Center_Mass Create.xls`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
      handleMessageDialogClickOpen();
      setMessageDialogTitle("Success");

      setMessageDialogMessage(
        `Cost Center_Mass Create.xls has been downloaded successfully`
      );
      setMessageDialogSeverity("success");
    };
    let hError = (error) => {
      if (error.message) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(`${error.message}`);
        setMessageDialogSeverity("danger");
      }
    };
    doAjax(
      `/${destination_CostCenter}/excel/downloadExcel`,
      "getblobfile",
      hSuccess,
      hError
    );
  };
  const handleChangeDownload = () => {
    var downloadPayload = selectedMassChangeRowData.map((x) => {
      return {
        costCenter: x.costCenter,
        controllingArea: x.controllingArea,
      };
    });
    console.log("downloadPayload", downloadPayload);
    let hSuccess = (response) => {
      setIsLoading(false);
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");
      link.href = href;
      link.setAttribute("download", `Cost Center_Mass Change.xls`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
      handleMessageDialogClickOpen();
      setMessageDialogTitle("Success");

      setMessageDialogMessage(
        `Cost Center_Mass Change.xls has been downloaded successfully`
      );

      setMessageDialogSeverity("success");
    };
    let hError = (error) => {
      if (error.message) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(`${error.message}`);
        setMessageDialogSeverity("danger");
      }
    };
    doAjax(
      `/${destination_CostCenter}/excel/downloadExcelWithData`,
      "postandgetblob",
      hSuccess,
      hError,
      downloadPayload
    );
  };

  const handleCreateSingleWithoutCopy = () => {
    handleDialogClickOpen();
  };
  const handleCreateSingleWithCopy = () => {
    handleDialogClickOpenWithCopy();
  };
  const handleCreateMultiple = () => {
    setEnableDocumentUpload(true);
    setHandleMassMode("Create");
    dispatch(setHandleMassMode("Create"));
  };

  const handleToggle = () => {
    setOpenButton((prevOpen) => !prevOpen);
  };
  const handleClick = (option, index) => {
    // dispatch(setHandleMassMode("Create"));
    if (index !== 0) {
      setSelectedIndex(index);
      setOpenButton(false);
      if (index === 1) {
        handleCreateMultiple();
      } else if (index === 2) {
        dispatch(setHandleMassMode("Create"));
        handleCreateDownload();
      }
    }
  };
  const handleCloseButton = (event) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }
    setOpenButton(false);
  };
  const handleToggleChange = () => {
    setOpenButtonChange((prevOpen) => !prevOpen);
  };
  const handleToggleCreate = () => {
    setOpenButtonCreate((prevOpen) => !prevOpen);
  };
  const handleCloseButtonChange = (event) => {
    if (
      anchorRefChange.current &&
      anchorRefChange.current.contains(event.target)
    ) {
      return;
    }
    setOpenButtonChange(false);
  };
  const handleCloseButtonCreate = (event) => {
    if (
      anchorRefCreate.current &&
      anchorRefCreate.current.contains(event.target)
    ) {
      return;
    }
    setOpenButtonCreate(false);
  };
  const handleClickChange = (option, index) => {
    console.log("indexx", index);
    if (index !== 0) {
      setSelectedIndexChange(index);
      setOpenButtonChange(false);

      if (index === 1) {
        handleChangeMultiple();
      } else if (index === 2) {
        // Check if at least one row is selected
        if (selectedRows.length > 0) {
          console.log("selectedRows",selectedRows);
          setIsLoading(true);
          setIsCheckboxSelected(false);
          handleChangeDownload();
        } else {
          // Handle the case when no rows are selected (e.g., show a message)
          console.log("Please select at least one row to download Excel.");
        }
      }
    }
  };

  const handleClickCreate = (option, index) => {
    // dispatch(setHandleMassMode("Change"));
    if (index !== 0) {
      setSelectedIndexCreate(index);
      setOpenButtonCreate(false);
      if (index === 1) {
        handleCreateSingleWithCopy();
      } else if (index === 2) {
        handleCreateSingleWithoutCopy();
      }
    }
  };
  const handleChangeMultiple = () => {
    setEnableDocumentUpload(true);
    dispatch(setHandleMassMode("Change"));
  };
  console.log("costCenterName", newValidFromDate);
  return (
    <>
      {
        isLoading===true?
        <LoadingComponent/>
        :
        <div ref={ref_elementForExport}>
      <ReusableDialog
        dialogState={openMessageDialog}
        openReusableDialog={handleMessageDialogClickOpen}
        closeReusableDialog={handleMessageDialogClose}
        dialogTitle={messageDialogTitle}
        dialogMessage={messageDialogMessage}
        handleDialogConfirm={handleMessageDialogClose}
        dialogOkText={"OK"}
        // handleExtraButton={handleMessageDialogNavigate}
        dialogSeverity={messageDialogSeverity}
      />

      {successMsg && (
        <ReusableSnackBar
          openSnackBar={openSnackbar}
          alertMsg={messageDialogMessage}
          handleSnackBarClose={handleSnackBarClose}
        />
      )}
      <div style={{ ...outermostContainer, backgroundColor: "#FAFCFF" }}>
        <Stack spacing={1}>
          {/* Information */}
          <Grid container sx={outermostContainer_Information}>
            <Grid item md={5} sx={outerContainer_Information}>
              <Typography variant="h3">
                <strong>Cost Center</strong>
              </Typography>
              <Typography variant="body2" color="#777">
                This view displays the list of Cost Centers
              </Typography>
            </Grid>
            <Grid item md={7} sx={{ display: "flex" }}>
              <Grid
                container
                direction="row"
                justifyContent="flex-end"
                alignItems="center"
                spacing={0}
              >
                <SearchBar
                  title="Search for multiple Cost Center numbers separated by comma"
                  handleSearchAction={() => getMaterialNoGlobalSearch()}
                  module="CostCenter"
                  keyName="number"
                  message={"Search Cost Center "}
                  clearSearchBar={clearSearchBar}
                />

                {/* <Tooltip title="Table Configuration">
                <IconButton  sx={iconButton_SpacingSmall}>
                  <TuneOutlined />
                </IconButton>
              </Tooltip> */}

                <Tooltip title="Reload">
                  <IconButton sx={iconButton_SpacingSmall}>
                    <Refresh
                      sx={{
                        "&:hover": {
                          transform: "rotate(360deg)",
                          transition: "0.9s",
                        },
                      }}
                      onClick={refreshPage}
                    />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Export Table">
                  <IconButton
                    sx={iconButton_SpacingSmall}
                    onClick={functions_ExportAsExcel.convertJsonToExcel}
                  >
                    <ReusableIcon iconName={"IosShare"} />
                  </IconButton>
                </Tooltip>
                {/* <Tooltip title="Export">
                  <IconButton sx={iconButton_SpacingSmall}>
                    <IosShare onClick={exportAsPicture} />
                  </IconButton>
                </Tooltip> */}
              </Grid>
            </Grid>
          </Grid>
          <Grid container sx={container_filter}>
            <Grid item md={12}>
              <Accordion className="filter-accordian">
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  aria-controls="panel1a-content"
                  id="panel1a-header"
                  sx={{
                    minHeight: "2rem !important",
                    margin: "0px !important",
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: "700",
                    }}
                  >
                    Search Cost Center
                  </Typography>
                </AccordionSummary>
                <AccordionDetails sx={{ padding: "0.5rem 1rem 0.5rem" }}>
                  <Grid
                    container
                    rowSpacing={1}
                    spacing={2}
                    justifyContent="space-between"
                    alignItems="center"
                  // sx={{ marginBottom: "0.5rem" }}
                  >
                    <Grid
                      container
                      spacing={1}
                      sx={{ padding: "0rem 1rem 0.5rem" }}
                    >
                      <Grid item md={2}>
                        <Typography sx={font_Small}>
                          Cost Center Name
                        </Typography>
                        <FormControl size="small" fullWidth>
                          <TextField
                            sx={{ fontSize: "12px !important" }}
                            fullWidth
                            size="small"
                            value={rmSearchForm?.costCenterName}
                            onChange={handleCostCenterName}
                            placeholder="Enter Cost Center Name"
                          />
                        </FormControl>
                      </Grid>
                      <Grid item md={2}>
                        <Typography sx={font_Small}>
                          Controlling Area
                        </Typography>
                        <FormControl size="small" fullWidth>
                          <Autocomplete
                            sx={{ height: "31px" }}
                            fullWidth
                            size="small"
                            value={rmSearchForm?.controllingArea}
                            onChange={handleControllingArea}
                            options={dropDownData?.ControllingArea ?? []}
                            getOptionLabel={(option) => {
                              if (option?.code)
                                return (
                                  `${option?.code} - ${option?.desc}` ?? ""
                                );
                              else return "";
                            }}
                            renderOption={(props, option) => (
                              <li {...props}>
                                <Typography style={{ fontSize: 12 }}>
                                  {`${option?.code}-${option?.desc}`}
                                </Typography>
                              </li>
                            )}
                            renderInput={(params) => (
                              <TextField
                                sx={{ fontSize: "12px !important" }}
                                {...params}
                                variant="outlined"
                                placeholder="Select Controlling Area"
                              />
                            )}
                          />
                        </FormControl>
                      </Grid>
                      <Grid item md={2}>
                        <Typography sx={font_Small}>Company Code</Typography>
                        <FormControl fullWidth size="small">
                          <Autocomplete
                            sx={{ height: "31px" }}
                            fullWidth
                            size="small"
                            onChange={handleCompanyCode}
                            // onChange={(e) => handleMatTypeChange(e)}
                            options={dropdownData?.CompCodeSearch ?? []}
                            getOptionLabel={(option) => {
                              if (option?.code)
                                return (
                                  `${option?.code} - ${option?.desc}` ?? ""
                                );
                              else return "";
                            }}
                            value={rmSearchForm?.companyCode}
                            renderOption={(props, option) => (
                              <li {...props}>
                                <Typography style={{ fontSize: 12 }}>
                                  {`${option?.code}-${option?.desc}`}
                                </Typography>
                              </li>
                            )}
                            renderInput={(params) => (
                              <TextField
                                sx={{ fontSize: "12px !important" }}
                                {...params}
                                variant="outlined"
                                placeholder="Select Company Code"
                              />
                            )}
                          />
                        </FormControl>
                      </Grid>
                      <Grid item md={2}>
                        <Typography sx={font_Small}>Profit Center</Typography>
                        <FormControl fullWidth size="small">
                          <Autocomplete
                            sx={{ height: "31px" }}
                            fullWidth
                            size="small"
                            placeholder={"Select Profit Center"}
                            value={rmSearchForm?.profitCenter}
                            onChange={handleProfitCenter}
                            // onChange={(e) => handleMatTypeChange(e)}
                            options={dropDownData.ProfitCenterSearch ?? []}
                            getOptionLabel={(option) => {
                              if (option?.code)
                                return (
                                  `${option?.code} - ${option?.desc}` ?? ""
                                );
                              else return "";
                            }}
                            renderOption={(props, option) => (
                              <li {...props}>
                                <Typography style={{ fontSize: 12 }}>
                                  {`${option?.code} - ${option?.desc}`}
                                </Typography>
                              </li>
                            )}
                            renderInput={(params) => (
                              <TextField
                                sx={{ fontSize: "12px !important" }}
                                {...params}
                                variant="outlined"
                                placeholder={"Select Profit Center"}
                              />
                            )}
                          />
                        </FormControl>
                      </Grid>
                      <Grid item md={2}>
                        <Typography sx={font_Small}>Hierarchy Area</Typography>
                        <FormControl fullWidth size="small">
                          <Autocomplete
                            sx={{ height: "31px" }}
                            fullWidth
                            size="small"
                            onChange={handleHierarchyArea}
                            // onChange={(e) => handleMatTypeChange(e)}
                            options={dropdownData?.HierarchyAreaSearch ?? []}
                            getOptionLabel={(option) => {
                              if (option?.code)
                                return (
                                  `${option?.code} - ${option?.desc}` ?? ""
                                );
                              else return "";
                            }}
                            value={rmSearchForm?.hierarchyArea}
                            renderOption={(props, option) => (
                              <li {...props}>
                                <Typography style={{ fontSize: 12 }}>
                                  {`${option?.code}-${option?.desc}`}
                                </Typography>
                              </li>
                            )}
                            renderInput={(params) => (
                              <TextField
                                sx={{ fontSize: "12px !important" }}
                                {...params}
                                variant="outlined"
                                placeholder="Select Hierarchy Area"
                              />
                            )}
                          />
                        </FormControl>
                      </Grid>
                      <Grid item md={2}>
                        <Typography sx={font_Small}>
                          Cost Center Category
                        </Typography>
                        <FormControl fullWidth size="small">
                          <Autocomplete
                            sx={{ height: "31px" }}
                            fullWidth
                            size="small"
                            onChange={handleCostCenterCategory}
                            // onChange={(e) => handleMatTypeChange(e)}
                            options={dropdownData?.CostCenterCategory ?? []}
                            getOptionLabel={(option) => {
                              if (option?.code)
                                return (
                                  `${option?.code} - ${option?.desc}` ?? ""
                                );
                              else return "";
                            }}
                            value={rmSearchForm?.costCenterCategory}
                            renderOption={(props, option) => (
                              <li {...props}>
                                <Typography style={{ fontSize: 12 }}>
                                  {`${option?.code} - ${option?.desc}`}
                                </Typography>
                              </li>
                            )}
                            renderInput={(params) => (
                              <TextField
                                sx={{ fontSize: "12px !important" }}
                                {...params}
                                variant="outlined"
                                placeholder="Select Cost Center Category"
                              />
                            )}
                          />
                        </FormControl>
                      </Grid>
                      {/* <Grid item md={2}>
                        <Typography sx={font_Small}>Created By</Typography>
                        <TextField
                          sx={{ fontSize: "12px !important" }}
                          fullWidth
                          size="small"
                          value={rmSearchForm?.createdBy}
                          onChange={handleCreatedBy}
                          placeholder="Enter Created By"
                        />
                      </Grid> */}
                      {/* <Grid item md={2}>
                        <Typography sx={font_Small}>Changed By</Typography>
                        <TextField
                          sx={{ fontSize: "12px !important" }}
                          fullWidth
                          size="small"
                          value={rmSearchForm?.changedBy}
                          onChange={handleChangedBy}
                          placeholder="Enter Changed By"
                        />
                      </Grid> */}
                      {/* <Grid item md={2}>
                        <Typography sx={font_Small}>Created On</Typography>
                        <FormControl fullWidth sx={{ padding: 0 }}>
                          <LocalizationProvider dateAdapter={AdapterDateFns}>
                            <DateRange
                              onChange={(e) => handleDate(e)}
                              // onChange={(e) => handleMatTypeChange(e)
                              value={rmSearchForm?.createdOn}
                            />
                            <DatePicker
                                size="small"
                                
                                inputFormat="dd/MM/yyyy" />
                          </LocalizationProvider>
                        </FormControl>
                      </Grid> */}
                      {/* dynamic filter// */}
                      <Grid item md={2}>
                        <Typography sx={font_Small}>Add New Filters</Typography>
                        <FormControl>
                          <Select
                            sx={{
                              font_Small,
                              height: "31px",
                              fontSize: "12px",
                              width: "200px",
                            }}
                            // fullWidth
                            size="small"
                            multiple
                            limitTags={2}
                            value={selectedOptions}
                            onChange={handleSelection}
                            renderValue={(selected) => selected.join(", ")}
                            MenuProps={{
                              MenuProps,
                            }}
                            endAdornment={
                              selectedOptions.length > 0 && (
                                <InputAdornment position="end">
                                  <IconButton
                                    size="small"
                                    onClick={() => setSelectedOptions([])}
                                    aria-label="Clear selections"
                                  >
                                    <ClearIcon />
                                  </IconButton>
                                </InputAdornment>
                              )
                            }
                          >
                            {items.map((option) => (
                              <MenuItem key={option.title} value={option.title}>
                                <Checkbox
                                  checked={
                                    selectedOptions.indexOf(option.title) > -1
                                  }
                                />
                                {option.title}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                        <Grid
                          style={{
                            display: "flex",
                            justifyContent: "space-around",
                          }}
                        ></Grid>
                      </Grid>
                    </Grid>
                    <Grid
                      container
                      sx={{ flexDirection: "row", padding: "0rem 1rem 0.5rem" }}
                      gap={1}
                    >
                      {selectedOptions.map((option, i) => {
                        if (option === 'Person Responsible') {
                          return (
                            <Grid item>
                              <Stack>
                                <Typography sx={{ fontSize: "12px" }}>
                                  {option}
                                </Typography>

                                <TextField
                                  sx={{
                                    font_Small,
                                    height: "31px",
                                    fontSize: "12px",
                                    width: "200px",
                                  }}
                                  size="small"
                                  fullWidth
                                  onChange={(event, newValue) =>
                                    /*setFilterFieldData({
                                      ...filterFieldData,
                                      [option]: newValue,
                                    })*/
                                    //console.log(event.target.value)
                                    setFilterFieldData({
                                      ...filterFieldData,
                                      [option]: event.target.value,
                                    })
                                  }

                                  placeholder={`Enter ${option}`}
                                  value={filterFieldData[option]}
                                />
                              </Stack>
                            </Grid>
                          );
                        } else {
                          return (
                            <Grid item>
                              <Stack>
                                <Typography sx={{ fontSize: "12px" }}>
                                  {option}
                                </Typography>
                                <Autocomplete
                                  sx={{
                                    font_Small,
                                    height: "31px",
                                    fontSize: "12px",
                                    width: "200px",
                                  }}
                                  size="small"
                                  options={dynamicOptions ?? []}
                                  getOptionLabel={(option) =>
                                    `${option.code} - ${option.desc}`
                                  }
                                  placeholder={`Enter ${option}`}
                                  value={filterFieldData[option]}
                                  onChange={(event, newValue) =>
                                    setFilterFieldData({
                                      ...filterFieldData,
                                      [option]: newValue,
                                    })
                                  }
                                  renderInput={(params) => (
                                    <TextField
                                      sx={{ fontSize: "12px !important" }}
                                      {...params}
                                      size="small"
                                      placeholder={`Enter ${option}`}
                                      variant="outlined"
                                    />
                                  )}
                                />
                              </Stack>
                            </Grid>
                          )

                        }
                      })}
                      {/* //clear search button according to UX */}
                      {/* <Grid
                          item
                          style={{
                            width:"100%",
                            display: "flex",
                            justifyContent: "space-around",
                          }}
                        >
                          <Button
                            
                            sx={{ fontSize: "12px",  width:"129px", backgroundColor:" #7575751A", color:"#757575" }}
                            onClick={handleClear}
                          >
                            Clear
                          </Button>

                          <Button
                            
                            sx={{ ...button_Marginleft, fontSize: "12px",  width:"129px", backgroundColor:"#F7F5FF"  }}
                          // onClick={getFilter}
                          >
                            Search
                          </Button>
                        </Grid> */}
                    </Grid>
                  </Grid>
                  <Grid
                    container
                    style={{
                      display: "flex",
                      justifyContent: "flex-end",
                    }}
                  >
                    <Grid
                      item
                      style={{
                        display: "flex",
                        justifyContent: "space-around",
                      }}
                    >
                      <Button
                        variant="outlined"
                        sx={button_Outlined}
                        onClick={handleClear}
                      >
                        Clear
                      </Button>
                      {/* <PresetV3
                        anchorEl={anchorEl_Preset}
                        setAnchorEl={setAnchorEl}
                        open={openAnchor}
                        handleClose={handleClose_Preset}
                        presets={presets}
                        setPresets={setPresets}
                        presetName={presetName}
                        setPresetName={setPresetName}
                        deletePreset={functions_PresetFilter.deletePreset}
                        saveFilterPreset={functions_PresetFilter.saveFilterPreset}
                        setPresetFilter={functions_PresetFilter.setPresetFilter}
                        setFilterDefault={functions_PresetFilter.setFilterDefault}
                        handleSearch={() => { }}
                      /> */}
                      {/* <PresetV3
                        moduleName={"ReturnOrder"}
                        handleSearch={getFilter}
                        PresetMethod={PresetMethod}

                        PresetObj={PresetObj}
                      /> */}

                      <Button
                        variant="contained"
                        sx={{ ...button_Primary, ...button_Marginleft }}
                        onClick={() => getFilter()}
                      >
                        Search
                      </Button>
                    </Grid>
                  </Grid>
                </AccordionDetails>
              </Accordion>
            </Grid>
          </Grid>

          <Grid item sx={{ position: "relative" }}>
            <Stack>
              <ReusableTable
                isLoading={tableLoading}
                module={"CostCenter"}
                width="100%"
                title={"List of Cost Centers (" + count + ")"}
                rows={rmDataRows}
                columns={allColumns}
                page={page}
                pageSize={pageSize}
                rowCount={count ?? rmDataRows?.length ?? 0}
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                getRowIdValue={"id"}
                hideFooter={true}
                checkboxSelection={true}
                disableSelectionOnClick={true}
                status_onRowSingleClick={true}
                onRowsSelectionHandler={handleSelectionModelChange}
                callback_onRowSingleClick={(params) => {
                  const materialNumber = params.row.costCenter.slice(0, 10); // Adjust this based on your data structure
                  console.log("materialNumber", materialNumber);
                  navigate(
                    `/masterDataCockpit/costCenter/displayCostCenter/${materialNumber}`,
                    {
                      state: params.row,
                    }
                  );
                }}
                // setShowWork={setShowWork}
                stopPropagation_Column={"action"}
                status_onRowDoubleClick={true}
                showCustomNavigation={true}
              />
              {/* {viewDetailpage && <SingleMaterialDetail />} */}
            </Stack>
          </Grid>
          {/* {
            showBtmNav && */}
          {checkIwaAccess(iwaAccessData, "Cost Center", "CreateCC") && 
          userData?.role === "Super User" ||  userData?.role === "Finance" ? 
          (
          <Paper
            sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
            elevation={2}
          >
            <BottomNavigation
              className="container_BottomNav"
              showLabels
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                gap: 1,
              }}
              value={value}
              onChange={(newValue) => {
                setValue(newValue);
              }}
            >
           
              <ButtonGroup
                variant="contained"
                ref={anchorRefCreate}
                aria-label="split button"
              >
                <Button
                  size="small"
                  variant="contained"
                  // sx={{ cursor: "default" }}
                  onClick={() => handleClickCreate(optionsCreateSingle[0], 0)}
                // onClick={handleDialogClickOpen}
                >
                  {optionsCreateSingle[0]}
                </Button>
                <Button
                  size="small"
                  aria-controls={
                    openButtonCreate ? "split-button-menu" : undefined
                  }
                  aria-expanded={openButtonCreate ? "true" : undefined}
                  aria-label="select action"
                  aria-haspopup="menu"
                  onClick={handleToggleCreate}
                >
                  <ReusableIcon
                    iconName={"ArrowDropUp"}
                    iconColor={"#FFFFFF"}
                  />
                </Button>
              </ButtonGroup>
              <Popper
                sx={{
                  zIndex: 1,
                }}
                open={openButtonCreate}
                anchorEl={anchorRefCreate.current}
                placement={"top-end"}
              // transition
              >
                <Paper style={{ width: anchorRefCreate.current?.clientWidth }}>
                  <ClickAwayListener
                    onClickAway={handleDialogCloseCreateSingle}
                  >
                    <MenuList id="split-button-menu" autoFocusItem>
                      {optionsCreateSingle.slice(1).map((option, index) => (
                        <MenuItem
                          // autoFocusItem
                          key={option}
                          selected={index === selectedIndexCreate - 1}
                          onClick={() => handleClickCreate(option, index + 1)}
                        >
                          {option}
                        </MenuItem>
                      ))}
                    </MenuList>
                  </ClickAwayListener>
                </Paper>
              </Popper>

              <ButtonGroup
                variant="contained"
                ref={anchorRef}
                aria-label="split button"
              >
                <Button
                  size="small"
                  onClick={() => handleClick(options[0], 0)}
                  sx={{ cursor: "default" }}
                >
                  {options[0]}
                </Button>
                <Button
                  size="small"
                  aria-controls={openButton ? "split-button-menu" : undefined}
                  aria-expanded={openButton ? "true" : undefined}
                  aria-label="select action"
                  aria-haspopup="menu"
                  onClick={handleToggle}
                >
                  <ReusableIcon
                    iconName={"ArrowDropUp"}
                    iconColor={"#FFFFFF"}
                  />
                </Button>
              </ButtonGroup>
              <Popper
                sx={{
                  zIndex: 1,
                }}
                open={openButton}
                anchorEl={anchorRef.current}
                placement={"top-end"}
              >
                <Paper style={{ width: anchorRef.current?.clientWidth }}>
                  <ClickAwayListener onClickAway={handleCloseButton}>
                    <MenuList id="split-button-menu" autoFocusItem>
                      {options.slice(1).map((option, index) => (
                        <MenuItem
                          key={option}
                          selected={index === selectedIndex - 1}
                          onClick={() => handleClick(option, index + 1)}
                        >
                          {option}
                        </MenuItem>
                      ))}
                    </MenuList>
                  </ClickAwayListener>
                </Paper>
              </Popper>
              <ButtonGroup
                variant="contained"
                ref={anchorRefChange}
                aria-label="split button"
              >
                <Button
                  size="small"
                  onClick={() => handleClickChange(optionsChange[0], 0)}
                  sx={{ cursor: "default" }}
                >
                  {optionsChange[0]}
                </Button>
                <Button
                  size="small"
                  aria-controls={
                    openButtonChange ? "split-button-menu" : undefined
                  }
                  aria-expanded={openButtonChange ? "true" : undefined}
                  aria-label="select action"
                  aria-haspopup="menu"
                  onClick={handleToggleChange}
                >
                  <ReusableIcon
                    iconName={"ArrowDropUp"}
                    iconColor={"#FFFFFF"}
                  />
                </Button>
              </ButtonGroup>
              <Popper
                sx={{
                  zIndex: 1,
                }}
                open={openButtonChange}
                anchorEl={anchorRefChange.current}
                placement={"top-end"}
              >
                <Paper style={{ width: anchorRefChange.current?.clientWidth }}>
                  <ClickAwayListener onClickAway={handleCloseButtonChange}>
                    <MenuList id="split-button-menu" autoFocusItem>
                      {optionsChange.slice(1).map((option, index) => (
                        <MenuItem
                          key={option}
                          selected={index === selectedIndexChange - 1}
                          onClick={() => handleClickChange(option, index + 1)}
                        // disabled={index === 2 }
                        >
                          {option}
                        </MenuItem>
                      ))}
                    </MenuList>
                  </ClickAwayListener>
                </Paper>
              </Popper>

              <Dialog
                open={dialogOpen}
                onClose={handleDialogClose}
                sx={{
                  "&::webkit-scrollbar": {
                    width: "1px",
                  },
                }}
              >
                <DialogTitle
                  sx={{
                    justifyContent: "space-between",
                    alignItems: "center",
                    height: "max-content",
                    padding: ".5rem",
                    paddingLeft: "1rem",
                    backgroundColor: "#EAE9FF40",
                    // borderBottom: "1px solid grey",
                    display: "flex",
                  }}
                >
                  <Typography variant="h6">New Cost Center</Typography>

                  <IconButton
                    sx={{ width: "max-content" }}
                    onClick={handleDialogClose}
                    children={<CloseIcon />}
                  />
                </DialogTitle>
                <DialogContent sx={{ padding: ".5rem 1rem" }}>
                  <Grid container spacing={3}>
                    <Grid
                      item
                      md={6}
                      sx={{ width: "100%", marginTop: ".5rem" }}
                    >
                      <Typography>
                        Controlling Area
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                      <FormControl fullWidth sx={{ margin: ".5em 0px" }}>
                        <Autocomplete
                          sx={{ height: "42px" }}
                          required="true"
                          value={newControllingArea}
                          size="small"
                          onChange={(e, value) => {
                            setNewControllingArea(value);
                            getCompanyCodeBasedOnControllingArea(value);
                          }}
                          options={dropdownData?.ControllingArea ?? []}
                          getOptionLabel={(option) => {
                            if (option?.code)
                              return `${option?.code} - ${option?.desc}` ?? "";
                            else return "";
                          }}
                          // value={rmSearchForm?.plant}
                          renderOption={(props, option) => (
                            <li {...props}>
                              <Typography style={{ fontSize: 12 }}>
                                {`${option?.code}-${option?.desc}`}
                              </Typography>
                            </li>
                          )}
                          // error={newControllingArea === "" ? true : false}
                          renderInput={(params) => (
                            <TextField
                              sx={{ fontSize: "12px !important" }}
                              {...params}
                              variant="outlined"
                              placeholder="SELECT CONTROLLING AREA"
                            // error
                            />
                          )}
                        />
                      </FormControl>
                    </Grid>
                    <Grid
                      item
                      md={6}
                      sx={{
                        width: "100%",
                        marginTop: ".5rem",
                      }}
                    >
                      <Typography>
                        Cost Center
                        <span style={{ color: "red" }}>*</span>
                      </Typography>

                      <FormControl
                        fullWidth
                        sx={{
                          margin: ".5em 0px",
                          minWidth: "250px",
                          flexDirection: "row",
                        }}
                      >
                        <Grid md={5}>
                          <Autocomplete
                            sx={{ height: "42px" }}
                            required="true"
                            size="small"
                            onChange={(e, value) => {
                              setNewComapnyCode(value);

                              //check_all_fields_are_filled_are_empty_or_not();

                              // let companyCodeValue = e.target.value;
                              // let companyCodeUpperCase =
                              // companyCodeValue.toUpperCase();
                              // setNewCombinedCostcenter(companyCodeUpperCase);
                            }}
                            options={dropdownData?.CompanyCode ?? []}
                            getOptionLabel={(option) =>
                              `${option?.code}-${option?.desc}`
                            }
                            // value={rmSearchForm?.plant}
                            renderOption={(props, option) => (
                              <li {...props}>
                                <Typography style={{ fontSize: 12 }}>
                                  {`${option?.code}-${option?.desc}`}
                                </Typography>
                              </li>
                            )}
                            // error={newControllingArea === "" ? true : false}
                            renderInput={(params) => (
                              <TextField
                                sx={{ fontSize: "12px !important" }}
                                {...params}
                                variant="outlined"
                                placeholder="SELECT COMPANY CODE"
                                error={newCompanyCodeValid}
                              />
                            )}
                          />
                          {newCompanyCodeValid && (
                            <Typography variant="caption" color="error">
                              Please Select Any value
                            </Typography>
                          )}
                        </Grid>
                        <Grid md={7}>
                          <TextField
                            sx={{ fontSize: "12px !important", height: "40px" }}
                            fullWidth
                            // maxLength={6}
                            // minLength={5}
                            size="small"
                            //value={rmSearchForm?.changedBy}
                            value={newCostCenterName}
                            onChange={(e) => {
                              const newValue = e.target.value;
                              if (/^[a-zA-Z0-9\-/'#&]*$/.test(newValue)) {
                                if (newValue.length > 0 && newValue[0] === ' ') {
                                  setNewCostCenterName(newValue.trimStart());
                                } else {
                                  //let costCenterValue = e.target.value;
                                  let costCenterUpperCase = newValue.toUpperCase();
                                  setNewCostCenterName(costCenterUpperCase);
                                }
                              }
                              

                            }}
                            inputProps={{
                              length: 6,
                              maxLength: 6,
                              // minLength:6,
                              style: { textTransform: "uppercase" },
                            }}
                            placeholder="Enter Cost Center"
                            error={newCostCenterValid}
                            required={true}
                          />
                          {newCostCenterValid && (
                            <Typography variant="caption" color="error">
                              Cost Center must be 10 digits
                            </Typography>
                          )}
                        </Grid>
                      </FormControl>
                    </Grid>

                    <Grid
                      item
                      md={6}
                      sx={{
                        width: "100%",
                        marginTop: ".5rem",
                      }}
                    >
                      <Typography>
                        Valid From
                        <span style={{ color: "red" }}>*</span>
                      </Typography>

                      <FormControl fullWidth sx={{ margin: ".5em 0px" }}>
                        <LocalizationProvider dateAdapter={AdapterDateFns}>
                          {/* <DemoContainer components={["DatePicker"]}> */}
                          <DatePicker
                            slotProps={{ textField: { size: "small" } }}
                            // format=

                            value={newValidFromDate}
                            onChange={(value) => setNewValidFromDate(value)

                            }
                          />

                          {/* </DemoContainer> */}
                        </LocalizationProvider>
                      </FormControl>
                    </Grid>
                    <Grid
                      item
                      md={6}
                      sx={{
                        width: "100%",
                        marginTop: ".5rem",
                      }}
                    >
                      <Typography>
                        Valid To
                        <span style={{ color: "red" }}>*</span>
                      </Typography>

                      <FormControl fullWidth sx={{ margin: ".5em 0px" }}>
                        <LocalizationProvider dateAdapter={AdapterDateFns}>
                          {/* <DemoContainer components={["DatePicker"]}> */}
                          <DatePicker
                            slotProps={{ textField: { size: "small" } }}
                            value={newValidToDate}
                            onChange={(value) => setNewValidToDate(value)}
                            maxDate={new Date(9999, 12, 31)}
                          />
                          {/* </DemoContainer> */}
                        </LocalizationProvider>
                      </FormControl>
                    </Grid>
                  </Grid>

                  {isValidationError && (
                    <Grid>
                      <Typography style={{ color: "red" }}>
                        Please Enter Mandatory Fields
                      </Typography>
                    </Grid>
                  )}
                  {checkValidationCostCenter && (
                    <Grid>
                      <Typography style={{ color: "red" }}>
                        *The Cost Center with Controlling Area already exists.
                        Please enter different Cost Center or Controlling Area
                      </Typography>
                    </Grid>
                  )}
                </DialogContent>

                <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
                  <Button
                    sx={{ width: "max-content", textTransform: "capitalize" }}
                    onClick={handleDialogClose}
                  >
                    Cancel
                  </Button>
                  <Button
                    className="button_primary--normal"
                    type="save"
                    onClick={handleDialogProceed}
                    variant="contained"
                    disabled={!buttonDisabledForSingleWithoutCopy}
                  >
                    Proceed
                  </Button>
                </DialogActions>
              </Dialog>




              <Dialog
                open={dialogOpenCreate}
                onClose={handleDialogCloseCreate}
                sx={{
                  "&::webkit-scrollbar": {
                    width: "1px",
                  },
                }}
              >
                <DialogTitle
                  sx={{
                    justifyContent: "space-between",
                    alignItems: "center",
                    height: "max-content",
                    padding: ".5rem",
                    paddingLeft: "1rem",
                    backgroundColor: "#EAE9FF40",
                    // borderBottom: "1px solid grey",
                    display: "flex",
                  }}
                >
                  <Typography variant="h6">New Cost Center</Typography>

                  <IconButton
                    sx={{ width: "max-content" }}
                    onClick={handleDialogCloseCreate}
                    children={<CloseIcon />}
                  />
                </DialogTitle>
                <DialogContent sx={{ padding: ".5rem 1rem" }}>
                  <Grid container spacing={3}>
                    <Grid
                      item
                      md={6}
                      sx={{ width: "100%", marginTop: ".5rem" }}
                    >
                      <Typography>
                        Controlling Area
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                      <FormControl fullWidth sx={{ margin: ".5em 0px" }}>
                        <Autocomplete
                          sx={{ height: "42px" }}
                          required="true"
                          size="small"
                          onChange={(e, value) => {
                            setNewControllingArea(value);
                            getCompanyCodeBasedOnControllingArea(value);
                            getCompanyCodeForCreate(value);
                          }}
                          options={dropdownData?.ControllingArea ?? []}
                          getOptionLabel={(option) =>
                            `${option?.code}-${option?.desc}`
                          }
                          // value={rmSearchForm?.plant}
                          renderOption={(props, option) => (
                            <li {...props}>
                              <Typography style={{ fontSize: 12 }}>
                                {`${option?.code}-${option?.desc}`}
                              </Typography>
                            </li>
                          )}
                          // error={newControllingArea === "" ? true : false}
                          renderInput={(params) => (
                            <TextField
                              sx={{ fontSize: "12px !important" }}
                              {...params}
                              variant="outlined"
                              placeholder="SELECT CONTROLLING AREA"
                            />
                          )}
                        />
                      </FormControl>
                    </Grid>
                    <Grid
                      item
                      md={6}
                      sx={{
                        width: "100%",
                        marginTop: ".5rem",
                      }}
                    >
                      <Typography>
                        Cost Center
                        <span style={{ color: "red" }}>*</span>
                      </Typography>

                      <FormControl
                        fullWidth
                        sx={{
                          margin: ".5em 0px",
                          minWidth: "250px",
                          flexDirection: "row",
                        }}
                      >
                        <Grid md={5}>
                          <Autocomplete
                            sx={{ height: "42px" }}
                            required="true"
                            size="small"
                            onChange={(e, value) => {
                              setNewComapnyCode(value);
                            }}
                            options={dropdownData?.CompanyCode ?? []}
                            getOptionLabel={(option) =>
                              `${option?.code}-${option?.desc}`
                            }
                            // value={rmSearchForm?.plant}
                            renderOption={(props, option) => (
                              <li {...props}>
                                <Typography style={{ fontSize: 12 }}>
                                  {`${option?.code}-${option?.desc}`}
                                </Typography>
                              </li>
                            )}
                            // error={newControllingArea === "" ? true : false}
                            renderInput={(params) => (
                              <TextField
                                sx={{ fontSize: "12px !important" }}
                                {...params}
                                variant="outlined"
                                placeholder="SELECT COMPANY CODE"
                              />
                            )}
                          />
                        </Grid>
                        <Grid md={7}>
                          <TextField
                            sx={{ fontSize: "12px !important", height: "40px" }}
                            fullWidth
                            // maxLength={6}
                            // minLength={5}
                            size="small"
                            //value={rmSearchForm?.changedBy}
                            value={newCostCenterName}
                            onChange={(e) => {

                              const newValue = e.target.value;
                              if (newValue.length > 0 && newValue[0] === ' ') {
                                setNewCostCenterName(newValue.trimStart());
                              } else {
                                //let costCenterValue = e.target.value;
                                let costCenterUpperCase = newValue.toUpperCase();
                                setNewCostCenterName(costCenterUpperCase);
                              }
                              /*let costCenterElement = e.target.value.trim()
                              if (costCenterElement === '') {
                                setNewCostCenterName('');
                              } else {
                                if((JSON.stringify(newCostCenterName)[0].trim() === ''){

                                }
                                let costCenterValue = e.target.value;
                                let costCenterUpperCase =
                                  costCenterValue.toUpperCase();
                                  setNewCostCenterName(costCenterUpperCase);
                              }*/
                            }}
                            inputProps={{
                              length: 6,
                              maxLength: 6,
                              // minLength:6,
                              style: { textTransform: "uppercase" },
                            }}
                            placeholder="Enter Cost Center"
                            // error={newCostCenterName === "" ? true : false}
                            required={true}
                          />
                          {newCostCenterValidWithCopy && (
                            <Typography variant="caption" color="error">
                              Cost Center must be 10 digits
                            </Typography>
                          )}
                        </Grid>
                      </FormControl>
                    </Grid>

                    <Grid
                      item
                      md={6}
                      sx={{
                        width: "100%",
                        marginTop: ".5rem",
                      }}
                    >
                      <Typography>
                        Valid From
                        <span style={{ color: "red" }}>*</span>
                      </Typography>

                      <FormControl fullWidth sx={{ margin: ".5em 0px" }}>
                        <LocalizationProvider dateAdapter={AdapterDateFns}>
                          {/* <DemoContainer components={["DatePicker"]}> */}
                          <DatePicker
                            slotProps={{ textField: { size: "small" } }}
                            // format=

                            value={newValidFromDate}
                            onChange={(value) => setNewValidFromDate(value)}
                          />

                          {/* </DemoContainer> */}
                        </LocalizationProvider>
                      </FormControl>
                    </Grid>
                    <Grid
                      item
                      md={6}
                      sx={{
                        width: "100%",
                        marginTop: ".5rem",
                      }}
                    >
                      <Typography>
                        Valid To
                        <span style={{ color: "red" }}>*</span>
                      </Typography>

                      <FormControl fullWidth sx={{ margin: ".5em 0px" }}>
                        <LocalizationProvider dateAdapter={AdapterDateFns}>
                          {/* <DemoContainer components={["DatePicker"]}> */}
                          <DatePicker
                            slotProps={{ textField: { size: "small" } }}
                            value={newValidToDate}
                            onChange={(value) => setNewValidToDate(value)}
                            maxDate={new Date(9999, 12, 31)}
                          />
                          {/* </DemoContainer> */}
                        </LocalizationProvider>
                      </FormControl>
                    </Grid>
                    <Divider sx={{ width: "100%", marginLeft: "2%" }}>
                      <b>Copy From</b>
                    </Divider>
                    <Grid
                      item
                      md={6}
                      sx={{ width: "100%", marginTop: ".5rem" }}
                    >
                      <Typography>
                        Controlling Area
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                      <FormControl fullWidth sx={{ margin: ".5em 0px" }}>
                        <Autocomplete
                          sx={{ height: "42px" }}
                          required="true"
                          size="small"
                          onChange={(e, value) => {
                            setNewControllingAreaCopyFrom(value);
                            getCompanyCodeBasedOnControllingAreaCopy(value);
                          }}
                          options={dropdownData?.ControllingArea ?? []}
                          getOptionLabel={(option) =>
                            `${option?.code}-${option?.desc}`
                          }
                          // value={rmSearchForm?.plant}
                          renderOption={(props, option) => (
                            <li {...props}>
                              <Typography style={{ fontSize: 12 }}>
                                {`${option?.code}-${option?.desc}`}
                              </Typography>
                            </li>
                          )}
                          // error={newControllingArea === "" ? true : false}
                          renderInput={(params) => (
                            <TextField
                              sx={{ fontSize: "12px !important" }}
                              {...params}
                              variant="outlined"
                              placeholder="SELECT CONTROLLING AREA"
                            />
                          )}
                        />
                      </FormControl>
                    </Grid>
                    <Grid
                      item
                      md={6}
                      sx={{
                        width: "100%",
                        marginTop: ".5rem",
                      }}
                    >
                      <Typography>
                        Cost Center
                        <span style={{ color: "red" }}>*</span>
                      </Typography>

                      <FormControl
                        fullWidth
                        sx={{
                          margin: ".5em 0px",
                          minWidth: "250px",
                          flexDirection: "row",
                        }}
                      >
                        <Grid md={12}>
                          <Autocomplete
                            sx={{ height: "42px" }}
                            required="true"
                            size="small"
                            onChange={(e, value) => {
                              setNewCostCenter(value);
                            }}
                            options={dropdownData?.CompCodeCopy ?? []}
                            getOptionLabel={(option) =>
                              `${option?.code}-${option?.desc}`
                            }
                            // value={rmSearchForm?.plant}
                            renderOption={(props, option) => (
                              <li {...props}>
                                <Typography style={{ fontSize: 12 }}>
                                  {`${option?.code}-${option?.desc}`}
                                </Typography>
                              </li>
                            )}
                            // error={newControllingArea === "" ? true : false}
                            renderInput={(params) => (
                              <TextField
                                sx={{ fontSize: "12px !important" }}
                                {...params}
                                variant="outlined"
                                placeholder="SELECT COST CENTER"
                              />
                            )}
                          />
                        </Grid>
                      </FormControl>
                    </Grid>
                    {/* <Grid  sx={{ width: "100%" }} container>
                    <Divider />
                    </Grid> */}
                  </Grid>
                  {/* <Grid container spacing={3} >
                  
                  </Grid> */}
                  {isValidationErrorwithCopy && (
                    <Grid>
                      <Typography style={{ color: "red" }}>
                        Please Enter Mandatory Fields
                      </Typography>
                    </Grid>
                  )}
                  {checkValidationCostCenter && (
                    <Grid>
                      <Typography style={{ color: "red" }}>
                        *The Cost Center with Controlling Area already exists.
                        Please enter different Cost Center or Controlling Area
                      </Typography>
                    </Grid>
                  )}
                </DialogContent>

                <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
                  <Button
                    sx={{ width: "max-content", textTransform: "capitalize" }}
                    onClick={handleDialogCloseCreate}
                  >
                    Cancel
                  </Button>
                  <Button
                    className="button_primary--normal"
                    type="save"
                    onClick={handleDialogProceedWithCopy}
                    variant="contained"
                  >
                    Proceed
                  </Button>
                </DialogActions>
              </Dialog>




{/* 
              <Backdrop
  sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 1 }}
  open={tableLoading}
  // onClick={handleClose}
>
  <CircularProgress color="inherit" />
</Backdrop> */}
              {enableDocumentUpload && (
                <AttachmentUploadDialog
                  artifactId=""
                  artifactName=""
                  setOpen={setEnableDocumentUpload}
                  handleUpload={uploadExcel}
                />
              )}
            </BottomNavigation>
            
          </Paper>
          )
          :""
          }
        </Stack>
      </div>
    </div>
      }
    </>
  
  );
};

export default CostCenter;
