import { Box, Checkbox, Grid, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import { container_Padding } from "../../Common/commonStyles";
import ReusableTable from "../../Common/ReusableTable";
import { useDispatch, useSelector } from "react-redux";
import { setDropDown } from "../../../app/dropDownDataSlice";

const CompanyCodesDisplayTable = ({
  //   compCodesTabDetails = [],
  displayCompCode = [],
}) => {
  //   let filterFields = Object?.entries(compCodesTabDetails);
  // console.log("basic", filterFields);
  const [compcodejsx, setCompCodeJsx] = useState([]);
  const dispatch = useDispatch();
  const compCodesDropdown = useSelector(
    (state) => state.AllDropDown.dropDown.CompCodeBasedOnControllingArea ?? []
  );

  console.log(
    compcodejsx,
    displayCompCode,
    compCodesDropdown,
    "props1234"
  );
  // console.log('props.compcodes',props?.displayCompCode)
  // console.log("computer", props.displayCompCode.map((x)=>x.assigned==='true'))

  const columns = [
    {
      field: "id",
      headerName: "ID",
      type: "text",
      hide: "true",
      editable: "false",
    },
    {
      field: "companyCodes",
      headerName: "Company Codes",
      width: 350,
      editable: "false",
    },
    {
      field: "companyName",
      headerName: "Company Name",
      type: "text",
      editable: "false",
      width: 350,
    },
    {
      field: "assigned",
      headerName: "Assigned",
      width: 350,
      editable: "true",
      renderCell: (params) => {
        console.log("params", params);
        return (
          <Checkbox
            sx={{ padding: 0 }}
            checked={
            //   displayCompCode?.map((x) => x.assigned === true) ||
              params.row.assigned === true || params.row.assigned === "X"
            }
            // checked={true}
            onChange={(e) => {
              console.log("dddd", displayCompCode, e.target.checked);
              let count = 0;

              let rowsData = displayCompCode?.map((x) => {
                if (x.id === params.row.id) {
                  count++;
                  return { ...x, assigned: x.assigned == false ? "X" : "" };
                  
                }
                return x;
                
              });
              setCompCodeJsx(rowsData)
              console.log("finalcount", count);
            // setDisplayCompCode(rowsData);
            console.log('companycoderows', rowsData)
            }}
          />
        );
      },
    },
  ];

  return (
    <>
      {/* <Box> */}
        <ReusableTable
          width="100%"
          rows={displayCompCode ?? []}
          columns={columns}
          getRowIdValue={"id"}
          hideFooter={true}
          checkboxSelection={false}
          // experimentalFeatures={{ newEditingApi: true }}
        />
      {/* </Box */}
    </>
  );
};

export default CompanyCodesDisplayTable;
