import{m as te,n as he,o as be,r as m,j as g,b4 as Be,a as n,T as R,G as y,B as X,F as oe,q as se,a3 as ke,a5 as Ce,l as I,an as ve,A as Ee,bK as Fe,aD as qe,bL as Ke,bM as Je,bN as ye,bO as G,bP as Xe,f as me,g as Re,e as Ye,b as Qe,u as Ze,s as ce,bQ as De,K as D,bR as s,al as eo,I as oo,b1 as no,x as de,v as lo,w as ro,Z as z,Y as io,bS as le,bT as O,bl as so,bG as to,$ as re}from"./index-17b8d91e.js";import{d as ho}from"./ArrowCircleLeftOutlined-2a09f8e2.js";import{d as bo}from"./Description-22845efe.js";import{F as xo}from"./FilterField-727c53f0.js";import{u as uo}from"./useMaterialFieldConfig-2e883586.js";import{G as Ie}from"./GenericViewGeneral-68ecd4bc.js";import go from"./AdditionalData-070972f8.js";import{m as fo}from"./makeStyles-213dac7f.js";import"./useChangeLogUpdate-f322f7d1.js";import"./AutoCompleteType-9336ea79.js";import"./dayjs.min-ce01f2c7.js";import"./AdapterDayjs-1a4a6504.js";import"./isBetween-3aeee754.js";import"./dateViewRenderers-34586552.js";import"./useSlotProps-e34e1e13.js";import"./InputAdornment-5b0053c5.js";import"./CSSTransition-30917e2c.js";import"./useMediaQuery-6a073ac5.js";import"./useMobilePicker-9978caff.js";import"./DeleteOutline-30de3a49.js";import"./SingleSelectDropdown-29664b58.js";import"./useCustomDtCall-15db5725.js";import"./toConsumableArray-c7e4bd84.js";var xe={},ao=he;Object.defineProperty(xe,"__esModule",{value:!0});var Me=xe.default=void 0,po=ao(te()),So=be;Me=xe.default=(0,po.default)((0,So.jsx)("path",{d:"M12 7V3H2v18h20V7zM6 19H4v-2h2zm0-4H4v-2h2zm0-4H4V9h2zm0-4H4V5h2zm4 12H8v-2h2zm0-4H8v-2h2zm0-4H8V9h2zm0-4H8V5h2zm10 12h-8v-2h2v-2h-2v-2h2v-2h-2V9h8zm-2-8h-2v2h2zm0 4h-2v2h2z"}),"Business");var ue={},To=he;Object.defineProperty(ue,"__esModule",{value:!0});var Pe=ue.default=void 0,Ao=To(te()),yo=be;Pe=ue.default=(0,Ao.default)((0,yo.jsx)("path",{d:"M20 2H4c-1 0-2 .9-2 2v3.01c0 .72.43 1.34 1 1.69V20c0 1.1 1.1 2 2 2h14c.9 0 2-.9 2-2V8.7c.57-.35 1-.97 1-1.69V4c0-1.1-1-2-2-2m-5 12H9v-2h6zm5-7H4V4l16-.02z"}),"Inventory");var ge={},Io=he;Object.defineProperty(ge,"__esModule",{value:!0});var Le=ge.default=void 0,No=Io(te()),ie=be;Le=ge.default=(0,No.default)([(0,ie.jsx)("path",{d:"m12 2-5.5 9h11z"},"0"),(0,ie.jsx)("circle",{cx:"17.5",cy:"17.5",r:"4.5"},"1"),(0,ie.jsx)("path",{d:"M3 13.5h8v8H3z"},"2")],"Category");const Ne=d=>{let S=(d==null?void 0:d.basicDataTabDetails)&&(Object==null?void 0:Object.entries(d==null?void 0:d.basicDataTabDetails));const[v,f]=m.useState([]);return m.useEffect(()=>{f(S==null?void 0:S.map(a=>{var w;return g(y,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Be},children:[n(y,{container:!0,children:n(R,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:a[0]})}),n(X,{children:n(y,{container:!0,spacing:1,paddingBottom:1,children:(w=[...a[1]].filter(p=>p.visibility!="Hidden").sort((p,u)=>p.sequenceNo-u.sequenceNo))==null?void 0:w.map(p=>n(xo,{disabled:d==null?void 0:d.disabled,field:p,dropDownData:d.dropDownData,materialID:d==null?void 0:d.materialID,viewName:d==null?void 0:d.activeViewTab,plantData:d==null?void 0:d.plantData},p.fieldName))})})]},a[0])}))},[d==null?void 0:d.basicDataTabDetails,d.activeViewTab,d==null?void 0:d.materialID]),n(oe,{children:v})},Co=({materialID:d})=>{var a,w;const S=se(p=>{var u,i,B,U;return(U=(B=(i=(u=p.payload[d])==null?void 0:u.payloadData)==null?void 0:i.TaxData)==null?void 0:B.TaxData)==null?void 0:U.UniqueTaxDataSet}),[v,f]=m.useState((a=ke)==null?void 0:a.TAXDATA_LOADING);return m.useEffect(()=>{let p;return(S==null?void 0:S.length)===0&&(p=setTimeout(()=>{f(Ce.NO_DATA_AVAILABLE)},500)),()=>{p&&clearTimeout(p)}},[S]),!S||S.length===0?n(R,{sx:{textAlign:"center",marginTop:"10px"},children:v}):g(Re,{sx:{marginBottom:"20px",boxShadow:3,borderRadius:"10px",borderColor:(w=I)==null?void 0:w.primary.white},defaultExpanded:!0,children:[n(Ee,{expandIcon:n(ve,{}),sx:{backgroundColor:I.primary.whiteSmoke,borderRadius:"10px",padding:"8px 16px","&:hover":{backgroundColor:I.hover.hoverbg}},children:n(R,{variant:"h6",sx:{fontWeight:"bold"},children:"Tax Classification"})}),n(me,{children:g(Fe,{component:qe,sx:{maxWidth:"100%"},children:[n(R,{variant:"h6",sx:{p:1,fontWeight:"bold",textAlign:"center"},children:"Tax Data"}),g(Ke,{children:[n(Je,{children:g(ye,{sx:{backgroundColor:I.primary.whiteSmoke},children:[n(G,{sx:{fontWeight:"bold"},children:"Country"}),n(G,{sx:{fontWeight:"bold"},children:"Tax Type"}),n(G,{sx:{fontWeight:"bold"},children:"Tax Class"}),n(G,{sx:{fontWeight:"bold"},children:"Description"})]})}),n(Xe,{children:S.map(({Country:p,TaxType:u,SelectedTaxClass:i},B)=>g(ye,{children:[n(G,{sx:{fontWeight:"bold"},children:p}),n(G,{sx:{fontWeight:"bold"},children:u}),n(G,{children:(i==null?void 0:i.TaxClass)||"N/A"}),n(G,{children:(i==null?void 0:i.TaxClassDesc)||"N/A"})]},`${p}-${u}-${B}`))})]})]})})]},"Tax_Classification_Static")},ee=({label:d,value:S,labelWidth:v="25%",centerWidth:f="5%",icon:a})=>g(de,{flexDirection:"row",alignItems:"center",children:[a&&n("div",{style:{marginRight:"10px"},children:a}),n(R,{variant:"body2",color:I.secondary.grey,style:{width:v},children:d}),n(R,{variant:"body2",fontWeight:"bold",sx:{width:f,textAlign:"center"},children:":"}),n(R,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:S||""})]}),Jo=()=>{var ae,pe,Se;const{fetchMaterialFieldConfig:d}=uo(),S=fo(()=>({customTabs:{"& .MuiTabs-scroller":{overflowX:"auto !important",overflowY:"hidden !important"}}})),v=se(r=>r.payload),f=se(r=>r.tabsData.allTabsData),{t:a}=Ye(),w=Qe();S();const p=Ze(),u=ce(),i=p.state,{customError:B}=De(),U=[],[b,_e]=m.useState([]),[$e,Oe]=m.useState({}),[We,fe]=m.useState(!1),[Ge,we]=m.useState(""),[x,Ue]=m.useState(0),[He,ne]=m.useState(null);m.useEffect(()=>{Ve(),d(i==null?void 0:i.materialType.split(" - ")[0])},[]);const Ve=()=>{fe(!0);const r={materialNo:i==null?void 0:i.Number},e={"Basic Data":"Toclientdata",Sales:"Tosalesdata",Purchasing:"Toplantdata",Costing:"Toplantdata",Accounting:"Toaccountingdata",MRP:"Toplantdata",Warehouse:"Towarehousedata","Sales-Plant":"Tosalesdata","Work Scheduling":"Toplantdata"};function h(l){if(!Array.isArray(l))return{UniqueTaxDataSet:[]};const A=new Map;return l.forEach(T=>{const H=T==null?void 0:T.Depcountry;for(let M=1;M<=9;M++){const V=T[`TaxType${M}`],N=T[`Taxclass${M}`];if(V&&N!==void 0){const P=`${H}-${V}`,L=N==="1"?"Taxable":"Exempt";A.has(P)||A.set(P,{Country:H,TaxType:V,TaxClasses:[],SelectedTaxClass:{TaxClass:N,TaxClassDesc:L}});const t=A.get(P);t.TaxClasses.some(_=>_.TaxClass===N)||t.TaxClasses.push({TaxClass:N,TaxClassDesc:L}),t.SelectedTaxClass={TaxClass:N,TaxClassDesc:L}}}}),{UniqueTaxDataSet:Array.from(A.values())}}const Te=l=>{var A,T,H,M,V,N,P,L,t,W,_,j,k,F;if(l!=null&&l.body){const $=((A=l.body[0].ViewNames)==null?void 0:A.split(",").map(o=>o.trim()))||[],q=h((T=l==null?void 0:l.body[0])==null?void 0:T.Tocontroldata),K=[],c={};$.forEach(o=>{const E=e[o]??"";E!==""&&l.body[0][E]&&(K.push(o),c[o]=l.body[0][E])}),$.includes("Sales")&&(K.push("Sales-Plant"),c["Sales-Plant"]=l.body[0].Toplantdata),K.push("Additional Data"),_e(K),Oe(c);const C=(H=l==null?void 0:l.body[0])==null?void 0:H.Toclientdata;if(C){if(C.Pvalidfrom){const o=le(C.Pvalidfrom);C.Pvalidfrom=o?o.toISOString().split("T")[0]:""}if(C.Svalidfrom){const o=le(C.Svalidfrom);C.Svalidfrom=o?o.toISOString().split("T")[0]:""}}u(O({materialID:(M=l==null?void 0:l.body[0])==null?void 0:M.Material,viewID:"Basic Data",itemID:"basic",data:C})),u(O({materialID:(V=l==null?void 0:l.body[0])==null?void 0:V.Material,viewID:s.PURCHASING_GENERAL,itemID:s.PURCHASING_GENERAL,data:(N=l==null?void 0:l.body[0])==null?void 0:N.Toclientdata})),u(O({materialID:(P=l==null?void 0:l.body[0])==null?void 0:P.Material,viewID:s.SALES_GENERAL,itemID:s.SALES_GENERAL,data:(L=l==null?void 0:l.body[0])==null?void 0:L.Toclientdata})),u(so({materialID:(t=l==null?void 0:l.body[0])==null?void 0:t.Material,data:(_=(W=l==null?void 0:l.body[0])==null?void 0:W.Touomdata)==null?void 0:_.map((o,E)=>{var J,Ae;return{...o,id:`${o.Material}-${E}`,uomId:`${o.Material}-${E}`,xValue:(o==null?void 0:o.Denominatr)||"",aUnit:(o==null?void 0:o.AltUnit)||"",measureUnitText:"",yValue:(o==null?void 0:o.Numerator)||"",eanUpc:(o==null?void 0:o.EanUpc)||"",eanCategory:(o==null?void 0:o.EanCat)||"",length:o==null?void 0:o.Length,width:o==null?void 0:o.Width,height:o==null?void 0:o.Height,unitsOfDimension:(o==null?void 0:o.UnitDim)||"",volume:(o==null?void 0:o.Volume)||"",volumeUnit:(o==null?void 0:o.Volumeunit)||"",grossWeight:(o==null?void 0:o.GrossWt)||"",netWeight:E===0&&(((Ae=(J=l==null?void 0:l.body[0])==null?void 0:J.Toclientdata)==null?void 0:Ae.NetWeight)||(o==null?void 0:o.NetWeight))||"",weightUnit:(o==null?void 0:o.UnitOfWt)||""}})})),u(to({materialID:(j=l==null?void 0:l.body[0])==null?void 0:j.Material,data:(k=l==null?void 0:l.body[0])==null?void 0:k.Tomaterialdescription.map((o,E)=>({id:E+1,language:o==null?void 0:o.Langu,materialDescription:o==null?void 0:o.MatlDesc}))})),u(O({materialID:(F=l==null?void 0:l.body[0])==null?void 0:F.Material,viewID:"TaxData",itemID:"TaxData",data:q||{TaxData:{UniqueTaxDataSet:[]}}})),fe(!1),we("")}},Z=()=>{};D(`/${z}/data/displayLimitedMaterialData`,"post",Te,Z,r)},je=(r,e)=>{Ue(e),ne(null)},ze=(r,e,h)=>(Te,Z)=>{var N,P,L;if(h===s.COSTING){let t={materialNo:e==null?void 0:e.Material,plant:e==null?void 0:e.Plant};e==null||e.Plant;let W={materialNo:e==null?void 0:e.Material,valArea:e==null?void 0:e.Plant},_=`/${z}/${re.ACCORDION_API.PLANT}`,j=`/${z}/${re.ACCORDION_API.ACCOUNTING}`;D(_,"post",$=>{const q=$==null?void 0:$.body[0].Toplantdata[0];D(j,"post",C=>{var J;const o=(J=C==null?void 0:C.body[0])==null?void 0:J.Toaccountingdata[0],E={...q,...o};u(O({materialID:e==null?void 0:e.Material,viewID:h,itemID:e==null?void 0:e.Plant,data:E}))},()=>{},W)},()=>{},t),ne(Z?r:null);return}let l={},A="",T="";h===s.PURCHASING||h===s.MRP||h===s.WORKSCHEDULING||h===s.SALES_PLANT?(l={materialNo:e==null?void 0:e.Material,plant:e==null?void 0:e.Plant},T=e==null?void 0:e.Plant,A=`/${z}/data/displayLimitedPlantData`):h===s.WAREHOUSE?(l={materialNo:e==null?void 0:e.Material,whNumber:e==null?void 0:e.WhseNo},T=e==null?void 0:e.WhseNo,A=`/${z}/${re.ACCORDION_API.WAREHOUSE}`):h===s.ACCOUNTING?(l={materialNo:e==null?void 0:e.Material,valArea:e==null?void 0:e.ValArea},T=e==null?void 0:e.ValArea,A=`/${z}/data/displayLimitedAccountingData`):h===s.SALES&&(l={materialNo:e==null?void 0:e.Material,salesOrg:e==null?void 0:e.SalesOrg,distChnl:e==null?void 0:e.DistrChan},T=`${e==null?void 0:e.SalesOrg}-${e==null?void 0:e.DistrChan}`,A=`/${z}/data/displayLimitedSalesData`);const H=t=>{var W,_,j,k,F,$;if(h===s.PURCHASING||h===s.MRP||h===s.WORKSCHEDULING||h===s.SALES_PLANT)u(O({materialID:e==null?void 0:e.Material,viewID:h,itemID:e==null?void 0:e.Plant,data:{...t==null?void 0:t.body[0].Toplantdata[0],ProdProf:((W=t==null?void 0:t.body[0].Toplantdata[0])==null?void 0:W.Prodprof)||""}}));else if(h===s.ACCOUNTING)u(O({materialID:e==null?void 0:e.Material,viewID:h,itemID:e==null?void 0:e.ValArea,data:(_=t==null?void 0:t.body[0])==null?void 0:_.Toaccountingdata[0]}));else if(h===s.WAREHOUSE)u(O({materialID:e==null?void 0:e.Material,viewID:h,itemID:e==null?void 0:e.WhseNo,data:(j=t==null?void 0:t.body[0])==null?void 0:j.Towarehousedata[0]}));else if(h===s.SALES){if((F=(k=t==null?void 0:t.body[0])==null?void 0:k.Tosalesdata[0])!=null&&F.ValidFrom){const q=le(t.body[0].Tosalesdata[0].ValidFrom);t.body[0].Tosalesdata[0].ValidFrom=q?q.toISOString().split("T")[0]:""}u(O({materialID:e==null?void 0:e.Material,viewID:h,itemID:`${e==null?void 0:e.SalesOrg}-${e==null?void 0:e.DistrChan}`,data:($=t==null?void 0:t.body[0])==null?void 0:$.Tosalesdata[0]}))}},M=t=>{B(t)};!((L=(P=(N=v==null?void 0:v[e==null?void 0:e.Material])==null?void 0:N.payloadData)==null?void 0:P[h])!=null&&L[T])&&D(A,"post",H,M,l),ne(Z?r:null)},Y=f!=null&&f.hasOwnProperty(s.SALES_GENERAL)?Object.entries(f[s.SALES_GENERAL]):[],Q=f!=null&&f.hasOwnProperty(s.PURCHASING_GENERAL)?Object.entries(f[s.PURCHASING_GENERAL]):[];return g("div",{style:{backgroundColor:"#FAFCFF"},children:[n(y,{container:!0,sx:eo,children:n(y,{item:!0,md:12,sx:{padding:"16px",display:"flex"},children:g(y,{md:9,sx:{display:"flex"},children:[n(oo,{color:"primary",sx:no,onClick:()=>w(-1),children:n(ho,{sx:{fontSize:"25px",color:"#000000"}})}),g(y,{item:!0,md:12,children:[n(R,{variant:"h3",children:n("strong",{children:a("Display Material")})}),n(R,{variant:"body2",color:"#777",children:a("This view displays the details of the materials")})]})]})})}),g(y,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",sx:{justifyContent:"space-between",alignItems:"center",paddingLeft:"29px",backgroundColor:I.basic.lighterGrey,borderRadius:"10px",boxShadow:"0 2px 8px rgba(0,0,0,0.06)"},children:[g(de,{width:"48%",spacing:1,sx:{padding:"10px 15px",borderRight:"1px solid #eaedf0"},children:[n(y,{item:!0,children:n(ee,{label:a("Material"),value:(i==null?void 0:i.Number)||"",labelWidth:"35%",icon:n(Pe,{sx:{color:I.blue.indigo,fontSize:"20px"}})})}),n(y,{item:!0,children:n(ee,{label:a("Industry Sector"),value:(i==null?void 0:i.indSector)||"",labelWidth:"35%",icon:n(Me,{sx:{color:I.blue.indigo,fontSize:"20px"}})})})]}),g(de,{width:"48%",spacing:1,marginRight:"-10%",sx:{padding:"10px 15px"},children:[n(y,{item:!0,children:n(ee,{label:a("Material Type"),value:(i==null?void 0:i.materialType)||"",labelWidth:"35%",icon:n(Le,{sx:{color:I.blue.indigo,fontSize:"20px"}})})}),n(y,{item:!0,children:n(ee,{label:a("Material Description"),value:(i==null?void 0:i.materialDesc)||"",labelWidth:"35%",icon:n(bo,{sx:{color:I.blue.indigo,fontSize:"20px"}})})})]})]}),n(y,{children:f&&b.length>0?n(X,{sx:{marginTop:"30px",border:"1px solid #e0e0e0",padding:"16px",background:I.primary.white},children:g(X,{sx:{marginTop:"-10px",marginLeft:"5px"},children:[n(lo,{value:x,onChange:je,"aria-label":"material tabs",sx:{top:100,zIndex:1e3,background:"#fafcff",marginLeft:"20px",marginBottom:"-20px"},children:b.map((r,e)=>n(ro,{label:a(r)},e))}),n(X,{sx:{padding:2,marginTop:2},children:b[x]==="Basic Data"&&v?n(Ne,{disabled:!0,materialID:i==null?void 0:i.Number,dropDownData:U,basicDataTabDetails:f["Basic Data"],activeViewTab:"Basic Data",plantData:"basic"}):b[x]==="Additional Data"?n(go,{disableCheck:!0,materialID:i==null?void 0:i.Number}):g(oe,{children:[b[x]===s.SALES&&g(oe,{children:[n(Co,{materialID:i==null?void 0:i.Number}),(Y==null?void 0:Y.length)>0&&n(Ie,{materialID:i==null?void 0:i.Number,GeneralFields:Y,disabled:!0,dropDownData:U,viewName:(ae=s)==null?void 0:ae.SALES_GENERAL})]}),b[x]===s.PURCHASING&&g(oe,{children:[" ",(Q==null?void 0:Q.length)>0&&n(Ie,{materialID:i==null?void 0:i.Number,GeneralFields:Q,disabled:!0,dropDownData:U,viewName:(pe=s)==null?void 0:pe.PURCHASING_GENERAL})]}),(Se=$e[b[x]])==null?void 0:Se.map((r,e)=>g(Re,{sx:{marginBottom:"20px",boxShadow:3},expanded:He===e,onChange:ze(e,r,b[x]),children:[n(Ee,{expandIcon:n(ve,{}),sx:{backgroundColor:"#f5f5f5",borderRadius:"10px",padding:"8px 16px","&:hover":{backgroundColor:"#e0e0e0"}},children:n(R,{variant:"h6",sx:{fontWeight:"bold"},children:b[x]===s.PURCHASING||b[x]===s.COSTING||b[x]===s.MRP||b[x]===s.WORKSCHEDULING||b[x]===s.SALES_PLANT?`Plant - ${r==null?void 0:r.Plant}`:b[x]===s.SALES?`Sales Org - ${r==null?void 0:r.SalesOrg} ,  Distribution Channel - ${r==null?void 0:r.DistrChan}`:b[x]===s.ACCOUNTING?`Plant - ${r==null?void 0:r.ValArea}`:b[x]===s.WAREHOUSE?`Warehouse - ${r==null?void 0:r.WhseNo}`:`${r==null?void 0:r.Material}`})}),v&&n(me,{sx:{padding:"16px"},children:n(R,{sx:{fontSize:"0.875rem",color:"#555"},children:n(Ne,{disabled:!0,materialID:i==null?void 0:i.Number,dropDownData:U,basicDataTabDetails:f[b[x]],activeViewTab:b[x],plantData:b[x]==="Sales"?`${r==null?void 0:r.SalesOrg}-${r==null?void 0:r.DistrChan}`:b[x]==="Purchasing"?`${r==null?void 0:r.Plant}`:b[x]==="Accounting"?`${r==null?void 0:r.ValArea}`:b[x]==="Warehouse"?`${r==null?void 0:r.WhseNo}`:`${r==null?void 0:r.Plant}`})})})]},e))]})})]})}):n(X,{sx:{marginTop:"30px",border:`1px solid ${I.secondary.grey}`,padding:"16px",background:`${I.primary.white}`,textAlign:"center"},children:n("span",{children:Ce.NO_DATA_AVAILABLE})})}),n(io,{blurLoading:We,loaderMessage:Ge})]})};export{Jo as default};
