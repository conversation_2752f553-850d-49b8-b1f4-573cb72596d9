import{cB as C,V as H,d_ as h,W as z,dV as G,o as i,cE as a,cI as K,r as N}from"./index-17b8d91e.js";import{a2 as S,m as q,a3 as J,a4 as Q,a5 as U,a6 as X}from"./dateViewRenderers-34586552.js";import{a as Y,u as Z}from"./useSlotProps-e34e1e13.js";const ee=C(H)({[`& .${h.container}`]:{outline:0},[`& .${h.paper}`]:{outline:0,minWidth:S}}),oe=C(z)({"&:first-of-type":{padding:0}});function ae(n){const{children:s,onDismiss:d,open:c,slots:o,slotProps:e}=n,p=(o==null?void 0:o.dialog)??ee,u=(o==null?void 0:o.mobileTransition)??G;return i.jsx(p,a({open:c,onClose:d},e==null?void 0:e.dialog,{TransitionComponent:u,TransitionProps:e==null?void 0:e.mobileTransition,PaperComponent:o==null?void 0:o.mobilePaper,PaperProps:e==null?void 0:e.mobilePaper,children:i.jsx(oe,{children:s})}))}const te=["props","getOpenDialogAriaText"],re=n=>{var T;let{props:s,getOpenDialogAriaText:d}=n,c=K(n,te);const{slots:o,slotProps:e,className:p,sx:u,format:O,formatDensity:R,enableAccessibleFieldDOMStructure:j,selectedSections:V,onSelectedSectionsChange:I,timezone:w,name:M,label:f,inputRef:x,readOnly:P,disabled:g,localeText:A}=s,y=N.useRef(null),r=q(),D=((T=e==null?void 0:e.toolbar)==null?void 0:T.hidden)??!1,{open:E,actions:m,layoutProps:L,renderCurrentView:W,fieldProps:k,contextValue:_}=J(a({},c,{props:s,fieldRef:y,autoFocusView:!0,additionalViewProps:{},wrapperVariant:"mobile"})),F=o.field,l=Y({elementType:F,externalSlotProps:e==null?void 0:e.field,additionalProps:a({},k,D&&{id:r},!(g||P)&&{onClick:m.onOpen,onKeyDown:Q(m.onOpen)},{readOnly:P??!0,disabled:g,className:p,sx:u,format:O,formatDensity:R,enableAccessibleFieldDOMStructure:j,selectedSections:V,onSelectedSectionsChange:I,timezone:w,label:f,name:M},x?{inputRef:x}:{}),ownerState:s});l.inputProps=a({},l.inputProps,{"aria-label":d(k.value)});const $=a({textField:o.textField},l.slots),v=o.layout??U;let b=r;D&&(f?b=`${r}-label`:b=void 0);const t=a({},e,{toolbar:a({},e==null?void 0:e.toolbar,{titleId:r}),mobilePaper:a({"aria-labelledby":b},e==null?void 0:e.mobilePaper)}),B=Z(y,l.unstableFieldRef);return{renderPicker:()=>i.jsxs(X,{contextValue:_,localeText:A,children:[i.jsx(F,a({},l,{slots:$,slotProps:t,unstableFieldRef:B})),i.jsx(ae,a({},m,{open:E,slots:o,slotProps:t,children:i.jsx(v,a({},L,t==null?void 0:t.layout,{slots:o,slotProps:t,children:W()}))}))]})}};export{re as u};
