import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { basicDataTabs, changeTabStatusEffect, clearOrgData, clearTabStatus, setOrgData, setTabStatus, updateTabStatus } from "../../app/tabsDetailsSlice";
import { salesDataTabs } from "../../app/tabsDetailsSlice";
import { purchasingDataTabs } from "../../app/tabsDetailsSlice";
import { mrpDataTabs } from "../../app/tabsDetailsSlice";
import { accountingDataTabs } from "../../app/tabsDetailsSlice";
import { setDropDown } from "../../app/dropDownDataSlice";
import { clearPayload, clearRequiredFields, setErrorFields, setPayload, setUOmData, setMatRequiredFieldsGI, setSingleMaterialPayload } from "../../app/payloadslice";
import { commonFilterClear } from "../../app/commonFilterSlice";
import { checkIwaAccess, formValidator, idGenerator } from "../../functions";
import { useLocation } from "react-router-dom";
// import "./masterDataCockpit.css";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import ReusableDialog from "../Common/ReusableDialog";
import CloseIcon from "@mui/icons-material/Close";
import AttachFileOutlinedIcon from "@mui/icons-material/AttachFileOutlined";
import ReusableAttachementAndComments from "../Common/ReusableAttachmentAndComments/ReusableAttachementAndComments";
import DescriptionIcon from "@mui/icons-material/Description";
import { BottomNavigation, Dialog, DialogActions, DialogContent, DialogTitle, Grid, IconButton, Paper, Stack, Tabs, Tooltip, FormControl, TextField } from "@mui/material";
import Tab from "@mui/material/Tab";
import { Navigate, useNavigate } from "react-router-dom";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import ClassificationTabs from "./ClassificationTabs";
import SalesTab from "./SalesTab";
import PurchasingDataTab from "./PurchasingDataTab";
import MRPDataTab from "./MRPDataTab";
import { button_Outlined, button_Primary, iconButton_SpacingSmall, outermostContainer_Information } from "../Common/commonStyles";
import { destination_MaterialMgmt, destination_DocumentManagement, destination_IDM } from "../../destinationVariables";
import { doAjax } from "../Common/fetchService";
import ReusableSnackBar from "../Common/ReusableSnackBar";
import AccountingTab from "./AccountingTab";
import GeneralInformationTabForMaterial from "./GeneralInformationTabForMaterial";
import { ToastContainer, toast, Bounce } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import ReusableToast from "./ReusableToast";
import GenericTabs from "./GenericTabs";

const CreateMaterialDetail = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const [value, setValue] = useState("1");
  const [open, setOpen] = useState(false);
  const [opendialog, setOpendialog] = useState(false);
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [basicData, setBasicData] = useState({});
  const [dropDownData, setDropDownData] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [messageDialogSeverity, setMessageDialogSeverity] = useState(false);
  const [successMsg, setsuccessMsg] = useState(false);
  const [mmNumber, setMmNumber] = useState("");
  const [openAttachDialog, setOpenAttachDialog] = useState(false);
  const [messageDialogOK, setMessageDialogOK] = useState(true);
  const [messageDialogExtra, setMessageDialogExtra] = useState(false);
  const [isForwardClicked, setIsForwardClicked] = useState(false);

  const [ruleData, setRuleData] = useState([]);
  const [attachmentsData, setAttachmentsData] = useState([]);
  const applicationConfig = useSelector((state) => state.applicationConfig);

  let displayData = useSelector((state) => state?.tabsData?.dataToSend);
  console.log("displaydata", displayData);
  const nmSearchForm = useSelector((state) => state.commonFilter["NewMaterial"]);
  console.log("nihar", nmSearchForm);
  let orgElementsData = useSelector((state) => state?.tabsData?.orgData);
  // const [tabStatusEffect, setTabStatusEffect] = useState(true);
  const [activeTab, setActiveTab] = useState(0);
  const [formValidationErrorItems, setFormValidationErrorItems] = useState([]);
  const [openSnackbarValidation, setOpenSnackbarValidation] = useState(false);
  const [correctionPopUp, setCorrectionPopoup] = useState(false);
  const payloadFields = useSelector((state) => state.payload.payloadData);
  const requiredFields = useSelector((state) => state.payload.requiredFields);
  const basicDataTabDetails = useSelector((state) => state.tabsData.basicData);
  const salesDataTabDetails = useSelector((state) => state.tabsData.salesData);
  const purchasingDataTabDetails = useSelector((state) => state.tabsData.purchasingData);
  const MRPDataTabDetails = useSelector((state) => state.tabsData.mrpData);
  const accountingTabDetails = useSelector((state) => state.tabsData.accountingData);
  const descriptionTabData = useSelector((state) => state?.payload?.additionalData);
  const PayloadData = useSelector((state) => state.payload);
  console.log("pppp1", PayloadData);
  const uomTabData = useSelector((state) => state.payload.unitsOfMeasureData);
  console.log("asdd", displayData?.selectedViews);
  console.log("uomTabData", uomTabData);
  let factorsArray = displayData?.selectedViews?.value.map((view) => {
    return view.label;
  });
  let selTabsArray = displayData?.selectedViews?.value.map((view) => {
    return view.value;
  });

  console.log("facccc", factorsArray);
  const singleMatPayload = useSelector((state) => state.payload.singleMatPayload);

  const [openCreateDialog, setOpenCreateDialog] = useState(false);
  const [openForwardReview, setOpenForwardReview] = useState(false);
  const [remarks, setRemarks] = useState("");

  const tabsStatus = useSelector((state) => state.tabsData.tabsStatus);
  const selectedTabs = useSelector((state) => state.tabsData);
  console.log("seltabs", selectedTabs.value.length, activeTab);
  console.log("tabsStatus", tabsStatus);
  console.log("hiii", selTabsArray, tabsStatus);
  let iwaAccessData = useSelector((state) => state.userManagement.entitiesAndActivities?.["Create Single Material"]);
  const tabStatusEffect = useSelector((state) => state.tabsData.tabsStatusEffect);
  let userData = useSelector((state) => state.userManagement.userData);
  console.log("userdata", userData);
  let rolesAccessTabs = useSelector((state) => state.tabsData.rolesTabs);

  const [isLoadingToast, setIsLoadingToast] = useState(false);
  const [toastType, setToastType] = useState("");
  const [toastMessage, setToastMessage] = useState("");
  const [isToastPromise, setIsToastPromise] = useState(false);

  useEffect(() => {
    dispatch(setErrorFields(formValidationErrorItems));
  }, [formValidationErrorItems]);

  const payloadData = useSelector((state) => state.payload.singleMatPayload);

  useEffect(() => {
    setOrgElements();
    // dispatch(clearPayload()
    // );
    // getGITemplate();
    getAttachmentsIDM();
  }, []);

  useEffect(() => {
    let views = "";
    selectedTabs?.value?.map((item) => (views += `${item.label},`));
    dispatch(
      setPayload({ keyName: "selectedViews", data: views.slice(0, -1) }) // slice removes last ,
    );
  }, []);

  useEffect(() => {
    setMmNumber(idGenerator("MM"));
  }, []);

  //to set org elements in redux because its coming in form of object inside object, so can't map in payload
  const setOrgElements = () => {
    let orgElementsArray = [];
    displayData?.orgData?.map((item) => {
      if (item.info) {
        // console.log("item", item.desc);
        orgElementsArray.push(item.desc);
      }
    });
    if (displayData && displayData.orgData) {
      displayData.orgData.forEach(function (element) {
        if (orgElementsArray.includes(element.desc)) {
          dispatch(
            setOrgData({
              keyName: element.desc.split(" ").join(""),
              data: element.info.code,
            })
          );
        }
      });
    }
  };

  const allMaterialTabs = [
    "General Information",
    "Basic Data",
    // "Classification",
    "Sales",
    "Purchasing",
    "Accounting",
    "Attachments & Comments",
  ];

  const artifactId = useSelector((state) => state.initialData.ArtifactId);
  const distinctArtifactId = [...new Set(artifactId)];
  console.log("artifactID", artifactId, distinctArtifactId);

  useEffect(() => {
    selectedTabs.value.map((item) => {
      // console.log("item", item);
      if (tabStatusEffect) {
        // setTabStatusEffect(false);
        dispatch(changeTabStatusEffect());
        dispatch(setTabStatus({ keyname: item.value, status: false }));
      }
    });
  }, [tabStatusEffect]);

  console.log("souma", tabStatusEffect);
  const onCorrection = () => {
    setCorrectionPopoup(true);
  };
  const handleCommentClose = () => {
    setCorrectionPopoup(false);
  };
  const commentAction = () => {
    handleSubmitCorrection();
    setCorrectionPopoup(false);
  };
  const UOMRows = useSelector((state) => state.payload.unitsOfMeasureData);
  //For UOM Table Rows
  useEffect(() => {
    if (UOMRows.length == 0) {
      dispatch(
        setUOmData([
          {
            id: 1,
            xValue: "1",
            aUnit: payloadFields?.BaseUnit?.code,
            measureUnitText: payloadFields?.BaseUnit?.desc,
            yValue: "1",
            bUnit: payloadFields?.BaseUnit?.code,
            measurementUnitText: payloadFields?.BaseUnit?.desc,                                         
            eanUpc: "",
            eanCategory: "",
            autoCheckDigit: "",
            addEans: "",
            length: "",
            width: "",
            height: "",
            unitsOfDimension: "",
            volume: payloadFields?.Volume,
            volumeUnit: payloadFields?.VolumeUnit?.code,
            grossWeight: payloadFields?.GrossWeight,
            netWeight: payloadFields?.NetWeight,
            weightUnit: payloadFields?.NetWeight?.code,
            noLowerLvlUnits: "",
            lowerLvlUnits: "",
            remVolAfterNesting: "",
            maxStackFactor: "",
            maxTopLoadFullPkg: "",
            UomToploadFullPkg: "",
            capacityUsage: "",
            UomCategory: "",
          },
        ])
      );
    } else if (PayloadData?.payloadData?.BaseUnit) {
      dispatch(
        setUOmData([
          {
            id: 1,
            xValue: "1",
            aUnit: payloadFields?.BaseUnit?.code,
            measureUnitText: payloadFields?.BaseUnit?.desc,
            yValue: "1",
            bUnit: payloadFields?.BaseUnit?.code,
            measurementUnitText: payloadFields?.BaseUnit?.desc,
            eanUpc: "",
            eanCategory: "",
            autoCheckDigit: "",
            addEans: "",
            length: "",
            width: "",
            height: "",
            unitsOfDimension: "",
            volume: payloadFields?.Volume,
            volumeUnit: payloadFields?.VolumeUnit?.code,
            grossWeight: payloadFields?.GrossWeight,
            netWeight: payloadFields?.NetWeight,
            weightUnit: payloadFields?.NetWeight?.code,
            noLowerLvlUnits: "",
            lowerLvlUnits: "",
            remVolAfterNesting: "",
            maxStackFactor: "",
            maxTopLoadFullPkg: "",
            UomToploadFullPkg: "",
            capacityUsage: "",
            UomCategory: "",
          },
        ])
      );
    }
  }, [payloadFields.BaseUnit]);

  console.log(PayloadData?.payloadData?.ANPCode?.code, "anp");

  var payload = {
    ProductID: "",
    BasicDataID: "",
    Product: displayData?.materialNo?.materialNumber ? displayData?.materialNo?.materialNumber : "",
    ProductType: displayData?.materialType?.fieldReference ?? "",
    ViewNames: PayloadData?.payloadData?.selectedViews ?? "",
    CorrectionViewNames: "",
    CreationID: PayloadData?.payloadData?.creationId ?? "",
    Description: nmSearchForm?.description,
    Remarks: remarks ? remarks : "",
    EditID: "",
    ExtendID: "",
    MassCreationID: "",
    MassEditID: "",
    MassExtendID: "",
    ReqCreatedOn: "",
    ReqCreatedBy: userData.user_id ?? "",
    CrossPlantStatus: PayloadData?.payloadData?.CrossPlantMaterialStatus?.code ?? "",
    CrossPlantStatusValidityDate: "/Date(" + Date.parse(PayloadData?.payloadData?.ValidFrom) + ")/" ?? null,
    // CrossPlantStatusValidityDate: null,
    IsMarkedForDeletion: false,
    ProductOldID: PayloadData?.payloadData?.OldMaterialNumber ?? "",
    GrossWeight: PayloadData?.payloadData?.GrossWeight ?? "",
    PurchaseOrderQuantityUnit: "",
    SourceOfSupply: "",
    WeightUnit: PayloadData?.payloadData?.WeightUnit?.code ?? "",
    NetWeight: PayloadData?.payloadData?.NetWeight ?? "",
    CountryOfOrigin: "",
    CompetitorID: "",
    ProductGroup: PayloadData?.payloadData?.MaterialGroup?.code ?? "",
    BaseUnit: PayloadData?.payloadData?.BaseUnit?.code ?? "",
    ItemCategoryGroup: PayloadData?.payloadData?.ItemCategoryGroup?.code ?? "NORM",
    ProductHierarchy: PayloadData?.payloadData?.ProductHierarchy?.code ?? "",
    Division: PayloadData?.payloadData?.Division?.code ?? "",
    VarblPurOrdUnitIsActive: "",
    VolumeUnit: PayloadData?.payloadData?.VolumeUnit?.code ?? "",
    MaterialVolume: PayloadData?.payloadData?.Volume ?? "",
    ANPCode: PayloadData?.payloadData?.ANPCode?.code ?? "",
    Brand: "",
    ProcurementRule: "",
    ValidityStartDate: null,
    LowLevelCode: "",
    ProdNoInGenProdInPrepackProd: "",
    SerialIdentifierAssgmtProfile: "",
    SizeOrDimensionText: PayloadData?.payloadData?.SizeDimensions ?? "",
    IndustryStandardName: PayloadData?.payloadData?.IndStdName ?? "",
    ProductStandardID: PayloadData?.payloadData?.InternationalArticleNumberEANUPC ?? "",
    InternationalArticleNumberCat: PayloadData?.payloadData?.CategoryOfInternationalArticleNumberEAN?.code ?? "",
    ProductIsConfigurable: PayloadData.payloadData?.ConfigurableMaterial ?? false,
    IsBatchManagementRequired: false,
    ExternalProductGroup: PayloadData?.payloadData?.ExtMatlGroup?.code ?? "",
    CrossPlantConfigurableProduct: PayloadData.payloadData?.CrossPlantConfMaterial ?? "",
    SerialNoExplicitnessLevel: "",
    ProductManufacturerNumber: "",
    ManufacturerNumber: "",
    ManufacturerPartProfile: "",
    QltyMgmtInProcmtIsActive: false,
    IndustrySector: displayData?.industrySector?.industryReference ?? "",
    ChangeNumber: "",
    MaterialRevisionLevel: "",
    HandlingIndicator: PayloadData?.payloadData?.HandlingIndicator?.code ?? "",
    WarehouseProductGroup: PayloadData?.payloadData?.WarehouseMaterialGroup?.code ?? "",
    WarehouseStorageCondition: PayloadData?.payloadData?.WarehouseStorageCondition?.code ?? "",
    StandardHandlingUnitType: PayloadData?.payloadData?.StdHUType?.code ?? "",
    SerialNumberProfile: PayloadData?.payloadData?.SerialNumberProfile?.code ?? "",
    AdjustmentProfile: "",
    PreferredUnitOfMeasure: "",
    IsPilferable: PayloadData?.payloadData?.Pilferable ?? false,
    IsRelevantForHzdsSubstances: PayloadData?.payloadData?.RelevantForHazardousSubstances ?? false,
    QuarantinePeriod: PayloadData?.payloadData?.QuarantinePeriod?.code ?? "",
    TimeUnitForQuarantinePeriod: "",
    QualityInspectionGroup: PayloadData?.payloadData?.QualityInspectionGroup?.code ?? "",
    AuthorizationGroup: PayloadData?.payloadData?.AuthorizationGroup ?? "",
    HandlingUnitType: PayloadData?.payloadData?.HandlingUnitType?.code ?? "",
    HasVariableTareWeight: PayloadData?.payloadData?.VariableTareWeight ?? false,
    MaximumPackagingLength: PayloadData?.payloadData?.MaxPackagingLength ?? "",
    MaximumPackagingWidth: PayloadData?.payloadData?.MaxPackagingWidth ?? "",
    MaximumPackagingHeight: PayloadData?.payloadData?.MaxPackagingHeight ?? "",
    UnitForMaxPackagingDimensions: "",
    IsFirstChangeLogCommit: true,
    RequestPriority: singleMatPayload?.ChoosePriorityLevel ? singleMatPayload?.ChoosePriorityLevel : "",
    BusinessJustification: singleMatPayload?.BusinessJustification ? singleMatPayload?.BusinessJustification : "",
    to_Description: descriptionTabData?.map((item) => {
      return {
        DescriptionID: "",
        Product: displayData?.materialNo?.materialNumber ? displayData?.materialNo?.materialNumber : "",
        Language: item?.language == "" ? "EN" : item?.language,
        ProductDescription: item?.materialDescription == "" ? nmSearchForm?.description : item?.materialDescription,
      };
    }),
    to_ProductUnitsOfMeasure: uomTabData?.map((item) => {
      console.log(item, "itemmm");
      return {
        Product: displayData?.materialNo?.materialNumber ? displayData?.materialNo?.materialNumber : "",
        AlternativeUnit: "PC",
        QuantityNumerator: "1",
        QuantityDenominator: "1",
        MaterialVolume: "12.000",
        VolumeUnit: "M3",
        GrossWeight: "18.000",
        WeightUnit: "KG",
        GlobalTradeItemNumber: "2150000000000",
        GlobalTradeItemNumberCategory: "EA",
        UnitSpecificProductLength: "0.000",
        UnitSpecificProductWidth: "0.000",
        UnitSpecificProductHeight: "0.000",
        ProductMeasurementUnit: "",
        LowerLevelPackagingUnit: "",
        RemainingVolumeAfterNesting: "0",
        MaximumStackingFactor: 0,
        CapacityUsage: "0.000",
        BaseUnit: "EA",
        // Product: displayData?.materialNo?.materialNumber ? displayData?.materialNo?.materialNumber : "",
        // UomID: "",
        // AlternativeUnit: "",
        // QuantityNumerator: item?.xValue ?? "",
        // QuantityDenominator: item?.yValue ?? "",
        // MaterialVolume: "12.000",
        // VolumeUnit: "M3",
        // GrossWeight: "18.000",
        // WeightUnit: "KG",
        // GlobalTradeItemNumber: "2150000000000 ",
        // GlobalTradeItemNumberCategory: "EA",
        // UnitSpecificProductLength: "0.000",
        // UnitSpecificProductWidth: "0.000",
        // UnitSpecificProductHeight: "0.000",
        // ProductMeasurementUnit: "",
        // LowerLevelPackagingUnit: "",
        // RemainingVolumeAfterNesting: "0",
        // MaximumStackingFactor: 0,
        // CapacityUsage: "0.000",
        // BaseUnit: item?.bUnit ?? "",
      };
    }),
    to_ProductStorage: {
      StorageID: "",
      Product: displayData?.materialNo?.materialNumber ? displayData?.materialNo?.materialNumber : "",
      StorageConditions: "",
      TemperatureConditionInd: "",
      HazardousMaterialNumber: "",
      NmbrOfGROrGISlipsToPrintQty: "",
      LabelType: "",
      LabelForm: "",
      MinRemainingShelfLife: "",
      ExpirationDate: "",
      ShelfLifeExpirationDatePeriod: null,
      TotalShelfLife: "",
      BaseUnit: PayloadData?.payloadData?.BaseUnit?.code ?? "",
    },
    to_ProductSalesTax: [
      {
        SalesTaxID: "",
        Product: "",
        Country: "US",
        TaxCategory: "UTXJ",
        TaxClassification: "1",
        Status: "",
      },
      {
        SalesTaxID: "",
        Product: "",
        Country: "IN",
        TaxCategory: "JOSG",
        TaxClassification: "1",
        Status: "",
      },
      {
        SalesTaxID: "",
        Product: "",
        Country: "IN",
        TaxCategory: "JOIG",
        TaxClassification: "1",
        Status: "",
      },
      {
        SalesTaxID: "",
        Product: "",
        Country: "IN",
        TaxCategory: "JOUG",
        TaxClassification: "1",
        Status: "",
      },
      {
        SalesTaxID: "",
        Product: "",
        Country: "IN",
        TaxCategory: "JOCG",
        TaxClassification: "1",
        Status: "",
      },
    ],
    to_ProductSales: {
      SalesID: "",
      Product: displayData?.materialNo?.materialNumber ? displayData?.materialNo?.materialNumber : "",
      SalesStatus: "",
      SalesStatusValidityDate: null,
      TaxClassification: "",
      TransportationGroup: "",
    },
    to_ProductQualityMgmt: {
      QualityMgmtID: "",
      Product: displayData?.materialNo?.materialNumber ? displayData?.materialNo?.materialNumber : "",
      QltyMgmtInProcmtIsActive: false,
    },
    to_ProductPurchaseText: [
      {
        PurchaseTextID: "",
        Product: displayData?.materialNo?.materialNumber ? displayData?.materialNo?.materialNumber : "",
        Language: "",
        LongText: "",
      },
    ],
    to_ProductProcurement: {
      ProcurementID: "",
      Product: displayData?.materialNo?.materialNumber ? displayData?.materialNo?.materialNumber : "",
      PurchaseOrderQuantityUnit: PayloadData?.payloadData?.OrderUnit?.code ?? "",
      VarblPurOrdUnitStatus: PayloadData?.payloadData?.VarPurOrderUnitActive?.code ?? "",
      PurchasingAcknProfile: "",
    },
    to_ProductInspectionText: [
      {
        InspectionTextID: "",
        Product: displayData?.materialNo?.materialNumber ? displayData?.materialNo?.materialNumber : "",
        Language: "",
        LongText: "",
      },
    ],
    to_ProductBasicText: [
      {
        BasicDataTextID: "",
        Product: displayData?.materialNo?.materialNumber ? displayData?.materialNo?.materialNumber : "",
        Language: "",
        LongText: "",
      },
    ],
    to_Plant: [
      {
        PlantID: "",
        Product: displayData?.materialNo?.materialNumber ? displayData?.materialNo?.materialNumber : "",
        Plant: orgElementsData?.Plant ?? "",
        PurchasingGroup: PayloadData?.payloadData?.PurchasingGroup?.code ?? "",
        CountryOfOrigin: "",
        RegionOfOrigin: "",
        ProductionInvtryManagedLoc: "",
        ProfileCode: PayloadData?.payloadData?.PlantSpMatStatus?.code ?? "",
        ProfileValidityStartDate: null,
        AvailabilityCheckType: "",
        FiscalYearVariant: "",
        PeriodType: "",
        ProfitCenter: PayloadData?.payloadData?.ProfitCenter?.code ?? "",
        Commodity: "",
        GoodsReceiptDuration: "",
        MaintenanceStatusName: "",
        IsMarkedForDeletion: false,
        MRPType: PayloadData?.payloadData.MRPType?.code ?? "",
        MRPResponsible: "",
        ABCIndicator: "",
        MinimumLotSizeQuantity: "",
        MaximumLotSizeQuantity: "",
        FixedLotSizeQuantity: "",
        ConsumptionTaxCtrlCode: "",
        IsCoProduct: false,
        ProductIsConfigurable: "",
        StockDeterminationGroup: "",
        StockInTransferQuantity: "",
        StockInTransitQuantity: "",
        HasPostToInspectionStock: false,
        IsBatchManagementRequired: PayloadData?.payloadData?.BatchManagementPlant ?? false,
        SerialNumberProfile: "",
        IsNegativeStockAllowed: false,
        GoodsReceiptBlockedStockQty: "",
        HasConsignmentCtrl: "",
        FiscalYearCurrentPeriod: "",
        FiscalMonthCurrentPeriod: "",
        ProcurementType: "",
        IsInternalBatchManaged: PayloadData?.payloadData?.BatchManagement ?? "",
        ProductCFOPCategory: "",
        ProductIsExciseTaxRelevant: false,
        BaseUnit: PayloadData?.payloadData?.BaseUnit?.code ?? "",
        ConfigurableProduct: "",
        GoodsIssueUnit: "",
        MaterialFreightGroup: PayloadData?.payloadData?.MaterialFreightGroup?.code ?? "",
        OriginalBatchReferenceMaterial: "",
        OriglBatchManagementIsRequired: "",
        ProductIsCriticalPrt: false,
        ProductLogisticsHandlingGroup: "",
        to_PlantMRPArea: [
          {
            PlantMRPAreaID: "",
          },
        ],
        to_PlantQualityMgmt: {
          PlantQualityMgmtID: "",
          Product: displayData?.materialNo?.materialNumber ? displayData?.materialNo?.materialNumber : "",
          Plant: orgElementsData?.Plant ?? "",
          MaximumStoragePeriod: "",
          QualityMgmtCtrlKey: "",
          MatlQualityAuthorizationGroup: "",
          HasPostToInspectionStock: false,
          InspLotDocumentationIsRequired: false,
          SuplrQualityManagementSystem: "",
          RecrrgInspIntervalTimeInDays: "",
          ProductQualityCertificateType: "",
        },
        to_PlantSales: {
          PlantSalesID: "",
          Product: displayData?.materialNo?.materialNumber ? displayData?.materialNo?.materialNumber : "",
          Plant: orgElementsData?.Plant ?? "",
          LoadingGroup: "",
          ReplacementPartType: "",
          CapPlanningQuantityInBaseUoM: "",
          ProductShippingProcessingTime: "",
          WrkCentersShipgSetupTimeInDays: "",
          AvailabilityCheckType: "",
          BaseUnit: PayloadData?.payloadData?.BaseUnit?.code ?? "",
        },
        to_PlantStorage: {
          PlantStorageID: "",
          Product: displayData?.materialNo?.materialNumber ? displayData?.materialNo?.materialNumber : "",
          Plant: orgElementsData?.Plant ?? "",
          InventoryForCycleCountInd: "",
          ProvisioningServiceLevel: "",
          CycleCountingIndicatorIsFixed: false,
          ProdMaximumStoragePeriodUnit: "",
          WrhsMgmtPtwyAndStkRemovalStrgy: "",
        },
        to_PlantText: [
          {
            PlantTextID: "",
          },
        ],
        to_ProdPlantInternationalTrade: {
          PlantInternationalTradeID: "",
          Product: displayData?.materialNo?.materialNumber ? displayData?.materialNo?.materialNumber : "",
          Plant: orgElementsData?.Plant ?? "",
          CountryOfOrigin: "",
          RegionOfOrigin: "",
          ConsumptionTaxCtrlCode: "",
          ProductCASNumber: "",
          ProdIntlTradeClassification: "",
          ExportAndImportProductGroup: "",
        },
        to_ProductPlantCosting: {
          PlantCostingID: "",
          Product: displayData?.materialNo?.materialNumber ? displayData?.materialNo?.materialNumber : "",
          Plant: orgElementsData?.Plant ?? "",
          IsCoProduct: false,
          CostingLotSize: "",
          VarianceKey: "",
          BaseUnit: PayloadData?.payloadData?.BaseUnit?.code ?? "",
          TaskListGroupCounter: "",
          TaskListGroup: "",
          TaskListType: "",
          CostingProductionVersion: "",
          IsFixedPriceCoProduct: false,
          CostingSpecialProcurementType: "",
          SourceBOMAlternative: "",
          ProductBOMUsage: "",
          ProductIsCostingRelevant: false,
        },
        to_ProductPlantForecast: {
          PlantForecastID: "",
          Product: displayData?.materialNo?.materialNumber ? displayData?.materialNo?.materialNumber : "",
          Plant: orgElementsData?.Plant ?? "",
          ConsumptionRefUsageEndDate: null,
          ConsumptionQtyMultiplier: "",
          ConsumptionReferenceProduct: "",
          ConsumptionReferencePlant: "",
        },
        to_ProductPlantProcurement: {
          PlantProcurementID: "",
          Product: displayData?.materialNo?.materialNumber ? displayData?.materialNo?.materialNumber : "",
          Plant: orgElementsData?.Plant ?? "",
          IsAutoPurOrdCreationAllowed: PayloadData?.payloadData?.AutomaticPO ?? false,
          IsSourceListRequired: false,
          SourceOfSupplyCategory: "",
          ItmIsRlvtToJITDelivSchedules: "",
        },
        to_ProductSupplyPlanning: {
          PlantSupplyPlanningID: "",
          Product: displayData?.materialNo?.materialNumber ? displayData?.materialNo?.materialNumber : "",
          Plant: orgElementsData?.Plant ?? "",
          FixedLotSizeQuantity: "",
          MaximumLotSizeQuantity: "",
          MinimumLotSizeQuantity: "",
          LotSizeRoundingQuantity: "",
          LotSizingProcedure: "",
          MRPType: "",
          MRPResponsible: "",
          SafetyStockQuantity: "",
          MinimumSafetyStockQuantity: "",
          PlanningTimeFence: "",
          ABCIndicator: PayloadData?.payloadData?.ABCIndicator?.code ?? "",
          MaximumStockQuantity: "",
          ReorderThresholdQuantity: "",
          PlannedDeliveryDurationInDays: "",
          SafetyDuration: "",
          PlanningStrategyGroup: "",
          TotalReplenishmentLeadTime: "",
          ProcurementType: "",
          ProcurementSubType: "",
          AssemblyScrapPercent: "",
          AvailabilityCheckType: "",
          GoodsReceiptDuration: "",
          MRPGroup: "",
          DfltStorageLocationExtProcmt: "",
          ProdRqmtsConsumptionMode: "",
          BackwardCnsmpnPeriodInWorkDays: "",
          FwdConsumptionPeriodInWorkDays: "",
          BaseUnit: PayloadData?.payloadData?.BaseUnit?.code ?? "",
          PlanAndOrderDayDetermination: "",
          RoundingProfile: "",
          LotSizeIndependentCosts: "",
          MRPPlanningCalendar: "",
          RangeOfCvrgPrflCode: "",
          IsSafetyTime: "",
          PerdPrflForSftyTme: "",
          IsMRPDependentRqmt: "",
          InHouseProductionTime: "",
          ProductIsForCrossProject: "",
          StorageCostsPercentageCode: "",
          SrvcLvl: "",
          MRPAvailabilityType: "",
          FollowUpProduct: "",
          RepetitiveManufacturingIsAllwd: false,
          DependentRequirementsType: "",
          IsBulkMaterialComponent: false,
          RepetitiveManufacturingProfile: "",
          RqmtQtyRcptTaktTmeInWrkgDays: "",
          ForecastRequirementsAreSplit: "",
          EffectiveOutDate: null,
          MRPProfile: orgElementsData?.MRPProfile ?? "",
          ComponentScrapInPercent: "",
          ProductIsToBeDiscontinued: "",
          ProdRqmtsAreConsolidated: "",
          MatlCompIsMarkedForBackflush: "",
          ProposedProductSupplyArea: "",
          Currency: "",
          PlannedOrderActionControl: "",
        },
        to_ProductWorkScheduling: {
          PlantWorkSchedulingID: "",
          Product: displayData?.materialNo?.materialNumber ? displayData?.materialNo?.materialNumber : "",
          Plant: orgElementsData?.Plant ?? "",
          MaterialBaseQuantity: "",
          UnlimitedOverDelivIsAllowed: false,
          OverDelivToleranceLimit: "",
          UnderDelivToleranceLimit: "",
          ProductionInvtryManagedLoc: "",
          BaseUnit: PayloadData?.payloadData?.BaseUnit?.code ?? "",
          ProductProcessingTime: "",
          ProductionSupervisor: "",
          ProductProductionQuantityUnit: "",
          ProdnOrderIsBatchRequired: "",
          TransitionMatrixProductsGroup: "",
          OrderChangeManagementProfile: "",
          MatlCompIsMarkedForBackflush: "",
          SetupAndTeardownTime: "",
          ProductionSchedulingProfile: "",
          TransitionTime: "",
        },
        to_StorageLocation: [
          {
            StorageLocationID: "",
          },
        ],
      },
    ],
    to_SalesDelivery: [
      {
        SalesDeliveryID: "",
        Product: displayData?.materialNo?.materialNumber ? displayData?.materialNo?.materialNumber : "",
        ProductSalesOrg: orgElementsData?.SalesOrganization ?? "",
        ProductDistributionChnl: orgElementsData?.DistributionChannel ?? "",
        MinimumOrderQuantity: PayloadData?.payloadData?.MinOrderQty ?? "",
        SupplyingPlant: "",
        PriceSpecificationProductGroup: "",
        AccountDetnProductGroup: "",
        DeliveryNoteProcMinDelivQty: PayloadData?.payloadData?.MinDelQty ?? "",
        ItemCategoryGroup: PayloadData?.payloadData?.Itemcategorygroup?.code ?? "NORM",
        DeliveryQuantityUnit: "",
        DeliveryQuantity: PayloadData?.payloadData?.DeliveryUnit?.code ?? "",
        ProductSalesStatus: "",
        ProductSalesStatusValidityDate: null,
        SalesMeasureUnit: PayloadData?.payloadData?.Salesunit?.code ?? "",
        IsMarkedForDeletion: false,
        ProductHierarchy: "",
        FirstSalesSpecProductGroup: PayloadData?.payloadData?.MaterialGroup1?.code ?? "",
        SecondSalesSpecProductGroup: PayloadData?.payloadData?.MaterialGroup2?.code ?? "",
        ThirdSalesSpecProductGroup: PayloadData?.payloadData?.MaterialGroup3?.code ?? "",
        FourthSalesSpecProductGroup: PayloadData?.payloadData?.MaterialGroup4?.code ?? "",
        FifthSalesSpecProductGroup: PayloadData?.payloadData?.MaterialGroup5?.code ?? "",
        MinimumMakeToOrderOrderQty: PayloadData?.payloadData?.MakeToOrderQty ?? "",
        BaseUnit: PayloadData?.payloadData?.BaseUnit?.code ?? "",
        LogisticsStatisticsGroup: "",
        VolumeRebateGroup: "",
        ProductCommissionGroup: "",
        CashDiscountIsDeductible: false,
        PricingReferenceProduct: "",
        RoundingProfile: "",
        ProductUnitGroup: "",
        VariableSalesUnitIsNotAllowed: false,
        ProductHasAttributeID01: false,
        ProductHasAttributeID02: false,
        ProductHasAttributeID03: false,
        ProductHasAttributeID04: false,
        ProductHasAttributeID05: false,
        ProductHasAttributeID06: false,
        ProductHasAttributeID07: false,
        ProductHasAttributeID08: false,
        ProductHasAttributeID09: false,
        ProductHasAttributeID10: false,
        to_SalesTax: [
          {
            SalesTaxID: "",
          },
        ],
        to_SalesText: [
          {
            SalesTextID: "",
          },
        ],
      },
    ],
    to_Valuation: [
      {
        ValuationID: "",
        Product: displayData?.materialNo?.materialNumber ? displayData?.materialNo?.materialNumber : "",
        ValuationArea: "",
        ValuationType: "",
        ValuationClass: PayloadData?.payloadData?.ValuationClass?.code ?? "",
        PriceDeterminationControl: "",
        StandardPrice: PayloadData?.payloadData?.StandardPrice ?? "",
        PriceUnitQty: PayloadData?.payloadData?.PriceUnit?.code ?? "",
        InventoryValuationProcedure: "",
        IsMarkedForDeletion: false,
        MovingAveragePrice: PayloadData?.payloadData?.PerUnitPrice ?? "",
        ValuationCategory: "",
        ProductUsageType: "",
        ProductOriginType: "",
        IsProducedInhouse: false,
        ProdCostEstNumber: "",
        ProjectStockValuationClass: "",
        ValuationClassSalesOrderStock: "",
        PlannedPrice1InCoCodeCrcy: "",
        PlannedPrice2InCoCodeCrcy: "",
        PlannedPrice3InCoCodeCrcy: "",
        FuturePlndPrice1ValdtyDate: null,
        FuturePlndPrice2ValdtyDate: null,
        FuturePlndPrice3ValdtyDate: null,
        TaxBasedPricesPriceUnitQty: "",
        PriceLastChangeDate: null,
        PlannedPrice: "",
        PrevInvtryPriceInCoCodeCrcy: "",
        Currency: "",
        BaseUnit: "",
        to_MLAccount: [
          {
            MLAccountID: "",
          },
        ],
        to_MLPrices: [
          {
            MLPricesID: "",
          },
        ],
        to_ValuationAccount: {
          ValuationAccountID: "",
          Product: displayData?.materialNo?.materialNumber ? displayData?.materialNo?.materialNumber : "",
          ValuationArea: "",
          ValuationType: "",
          CommercialPrice1InCoCodeCrcy: "",
          CommercialPrice2InCoCodeCrcy: "",
          CommercialPrice3InCoCodeCrcy: "",
          DevaluationYearCount: "",
          FutureEvaluatedAmountValue: "",
          FuturePriceValidityStartDate: null,
          IsLIFOAndFIFORelevant: false,
          LIFOValuationPoolNumber: "",
          TaxPricel1InCoCodeCrcy: "",
          TaxPrice2InCoCodeCrcy: "",
          TaxPrice3InCoCodeCrcy: "",
          Currency: "",
        },
        to_ValuationCosting: {
          ValuationCostingID: "",
          Product: displayData?.materialNo?.materialNumber ? displayData?.materialNo?.materialNumber : "",
          ValuationArea: "",
          ValuationType: "",
          IsMaterialCostedWithQtyStruc: false,
          IsMaterialRelatedOrigin: "",
          CostOriginGroup: "",
          CostingOverheadGroup: "",
        },
      },
    ],
  };

  const handleChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  const handleMessageDialogClose = () => {
    navigate("/masterDataCockpit/materialMaster/materialSingle");
  };
  const handleMessageDialogNavigate = () => {
    navigate("/masterDataCockpit/materialMaster/materialSingle");
  };
  const handleClosedialog = () => {
    setOpendialog(false);
  };
  const handleOK = () => {
    setOpendialog(false);
    // setOpendialog2(false);
  };
  const handleClose = () => {
    setOpen(false);
  };
  const handleSnackBarClose = () => {
    setopenSnackbar(false);
    // navigate("/masterDataCockpit/materialMaster/materialSingle");
  };
  const handleSnackBarCloseValidation = () => {
    setOpenSnackbarValidation(false);
    // navigate("/masterDataCockpit/materialMaster/materialSingle");
  };
  const handleSnackBarOpen = () => {
    setopenSnackbar(true);
  };
  const handleSnackBarOpenValidation = () => {
    setOpenSnackbarValidation(true);
  };
  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };
  const handleCheckValidationError = () => {
    return formValidator(payloadFields, requiredFields, setFormValidationErrorItems);
  };

  const giInfo = useSelector((state) => state.payload.generalInformation);
  console.log("giinfo", giInfo);

  const [questions, setQuestions] = useState([]);
  console.log("qqqqq", questions);

  // const getGITemplate = () => {
  //   let payload = {
  //     decisionTableId: null,
  //     decisionTableName: "MDG_GI_MATERIAL_QUESTIONS",
  //     version: "v1",
  //     rulePolicy: null,
  //     validityDate: null,
  //     conditions: [
  //       {
  //         // "MDG_CONDITIONS.MDG_GI_MODULE": "CC SUNOCO",
  //         "MDG_CONDITIONS.MDG_GI_SCENARIO": "Create",
  //       },
  //     ],
  //     systemFilters: null,
  //     systemOrders: null,
  //     filterString: null,
  //   };
  //   setIsLoading(true);
  //   // const formData = new FormData();

  //   const hSuccess = (data) => {
  //     console.log("gggg1", data);
  //     if (data.statusCode === 200) {
  //       const questionsData =
  //         data?.data?.result[0]?.MDG_GI_QUESTIONS_ACTION_TYPE || [];
  //       console.log("gggg2", questionsData);
  //       setQuestions(questionsData);
  //       questionsData.map((question) => {
  //         const input = question?.MDG_GI_INPUT_OPTION;
  //         console.log("inpppp", input);
  //       })
  //       questionsData.map((question) => {
  //         if (question?.MDG_GI_INPUT_OPTION === 'Radio Button') {
  //           if (question?.MDG_GI_VISIBILITY === " Mandatory") {
  //             console.log("insidevisibility");
  //             (setMatRequiredFieldsGI(question.MDG_GI_QUESTION_TYPE
  //               .replaceAll("(", "")
  //               .replaceAll(")", "")
  //               .replaceAll("/", "")
  //               .replaceAll("-", "")
  //               .replaceAll(".", "")
  //               .split(" ")
  //               .join(""),))
  //           }
  //           if (question?.MDG_GI_QUESTION_TYPE !== "Choose Priority Level") {
  //             dispatch(
  //               setSingleMaterialPayload({
  //                 keyName: question.MDG_GI_QUESTION_TYPE
  //                   .replaceAll("(", "")
  //                   .replaceAll(")", "")
  //                   .replaceAll("/", "")
  //                   .replaceAll("-", "")
  //                   .replaceAll(".", "")
  //                   .split(" ")
  //                   .join(""),
  //                 data: "No",
  //               })
  //             )
  //           } else {
  //             dispatch(
  //               setSingleMaterialPayload({
  //                 keyName: question.MDG_GI_QUESTION_TYPE
  //                   .replaceAll("(", "")
  //                   .replaceAll(")", "")
  //                   .replaceAll("/", "")
  //                   .replaceAll("-", "")
  //                   .replaceAll(".", "")
  //                   .split(" ")
  //                   .join(""),
  //                 data: "Medium",
  //               })
  //             )
  //           }
  //         } else {
  //           if (question?.MDG_GI_VISIBILITY === " Mandatory") {
  //             console.log("insidevisibility");
  //             dispatch(setMatRequiredFieldsGI(question.MDG_GI_QUESTION_TYPE
  //               .replaceAll("(", "")
  //               .replaceAll(")", "")
  //               .replaceAll("/", "")
  //               .replaceAll("-", "")
  //               .replaceAll(".", "")
  //               .split(" ")
  //               .join(""),));
  //           }
  //           dispatch(
  //             setSingleMaterialPayload({
  //               keyName: question.MDG_GI_QUESTION_TYPE
  //                 .replaceAll("(", "")
  //                 .replaceAll(")", "")
  //                 .replaceAll("/", "")
  //                 .replaceAll("-", "")
  //                 .replaceAll(".", "")
  //                 .split(" ")
  //                 .join(""),
  //               data: payloadData?.[
  //                 question.MDG_GI_QUESTION_TYPE?.replaceAll("(", "")
  //                   .replaceAll(")", "")
  //                   .replaceAll("/", "")
  //                   .replaceAll("-", "")
  //                   .replaceAll(".", "")
  //                   .split(" ")
  //                   .join("")
  //               ] ?? "",
  //             }))
  //         }

  //       })
  //     }

  //   };

  //   const hError = (error) => {
  //     console.log(error);
  //   };

  //   if (applicationConfig.environment === "localhost") {
  //     doAjax(
  //       `/${destination_IDM}/rest/v1/invoke-rules`,
  //       "post",
  //       hSuccess,
  //       hError,
  //       payload
  //     );
  //   } else {
  //     doAjax(
  //       `/${destination_IDM}/v1/invoke-rules`,
  //       "post",
  //       hSuccess,
  //       hError,
  //       payload
  //     );
  //   }
  // };

  const getAttachmentsIDM = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_ATTACHMENTS_LIST_DT",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE": "Material",
          "MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO": "Create",
          "MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE": 1,
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    setIsLoading(true);
    // const formData = new FormData();
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        let responseData = data?.data?.result[0]?.MDG_ATTACHMENTS_ACTION_TYPE;
        console.log("resss1", responseData);
        let templateData = [];
        responseData.map((element, index) => {
          console.log("resss2", element);

          var tempRow = {
            id: index,
            // templateName: element?.MDG_CHANGE_TEMPLATE_NAME,
            // templateData: element?.MDG_CHANGE_TEMPLATE_FIELD_LIST,
          };
          templateData.push(tempRow);
        });
        console.log("resss3", templateData);
        setRuleData(templateData);
        const attachmentNames = data?.data?.result[0]?.MDG_ATTACHMENTS_ACTION_TYPE || [];

        // Update state with attachment names
        setAttachmentsData(attachmentNames);
      } else {
        // setMessageDialogTitle("Create");
        // setsuccessMsg(false);
        // setMessageDialogMessage("Creation Failed");
        // setHandleExtrabutton(false);
        // setMessageDialogSeverity("danger");
        // setMessageDialogOK(false);
        // setMessageDialogExtra(true);
        // handleMessageDialogClickOpen();
        // handleCreateDialogClose();
        // setIsLoading(false);
      }
      handleClose();
    };

    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(`/${destination_IDM}/rest/v1/invoke-rules`, "post", hSuccess, hError, payload);
    } else {
      doAjax(`/${destination_IDM}/v1/invoke-rules`, "post", hSuccess, hError, payload);
    }
  };

  const handleOpenAttachDialog = () => {
    setOpenAttachDialog(true);
  };
  const handleCloseAttachDialog = () => {
    setOpenAttachDialog(false);
  };
  const handleRemarks = (e, value) => {
    const newValue = e.target.value;
    if (newValue.length > 0 && newValue[0] === " ") {
      setRemarks(newValue.trimStart());
    } else {
      //let costCenterValue = e.target.value;
      let remarksUpperCase = newValue.toUpperCase();
      setRemarks(remarksUpperCase);
    }
  };

  const onSaveButtonClick = () => {
    const isValidation = handleCheckValidationError();
    if (isValidation) {
      if (activeTab === displayData?.selectedViews?.value?.map((item) => item.value).findIndex((item) => item === "basicData")) {
        var saveBasicDataUrl = `/${destination_MaterialMgmt}/alter/saveBasicDataViewAsDraft`;
        setIsToastPromise(true);
        setIsLoadingToast(true);
        const hSuccess = (data) => {
          console.log(data, "example");
          setIsLoading();
          let payloadKey = {};

          if (data.statusCode === 200) {
            payloadKey["keyName"] = "creationId";
            payloadKey["data"] = data.body;
            console.log(payloadKey);
            dispatch(setPayload(payloadKey));
            // setMessageDialogTitle("Create");
            // setMessageDialogMessage(
            //   `Basic Data has been saved successfully with creation ID ${data.body}`
            // );
            // setMessageDialogSeverity("success");
            // setMessageDialogOK(false);
            setsuccessMsg(true);
            // handleSnackBarOpen();
            // setMessageDialogExtra(true);
            // setIsLoading(false);
            setIsLoadingToast(false);
            setToastType("success");
            setToastMessage(`${data.message} With ID: ${data.body}`);

            distinctArtifactId.map((artifactId) => {
              console.log(artifactId, "artifactId=====");
              const secondApiPayload = {
                artifactId: artifactId,
                createdBy: userData?.emailId,
                artifactType: "Material",
                requestId: `NMS${data?.body}`,
              };
              const secondApiSuccess = (secondApiData) => {
                console.log("Second API success", secondApiData);
                // Handle success for the second API if needed
              };

              const secondApiError = (secondApiError) => {
                console.error("Second API error", secondApiError);
                // Handle error for the second API if needed
              };
              // {requestId&&
              doAjax(`/${destination_DocumentManagement}/documentManagement/updateDocRequestId`, "post", secondApiSuccess, secondApiError, secondApiPayload);
            });
          } else {
            // setMessageDialogTitle("Error");
            // setsuccessMsg(false);
            // setMessageDialogMessage("Failed to save Basic Data");
            // setMessageDialogSeverity("danger");
            // setMessageDialogOK(false);
            // setMessageDialogExtra(true);
            // handleMessageDialogClickOpen();
            // setIsLoading(false);
            setIsLoadingToast(false);
            setToastType("error");
            setToastMessage("Failed to save Basic Data");
            dispatch(clearPayload());
            dispatch(clearOrgData());
            dispatch(clearTabStatus());
          }
          handleClose();
        };
        const hError = (error) => {
          console.log(error);
        };
        doAjax(saveBasicDataUrl, "post", hSuccess, hError, payload);
      } else {
        if (activeTab === displayData?.selectedViews?.value?.map((item) => item.value).findIndex((item) => item === "sales")) {
          var saveActiveTabData = `/${destination_MaterialMgmt}/alter/saveSalesViewAsDraft`;
        } else if (activeTab === displayData?.selectedViews?.value?.map((item) => item.value).findIndex((item) => item === "purchasing")) {
          var saveActiveTabData = `/${destination_MaterialMgmt}/alter/savePurchaseViewAsDraft`;
        } else if (activeTab === displayData?.selectedViews?.value?.map((item) => item.value).findIndex((item) => item === "mrp")) {
          var saveActiveTabData = `/${destination_MaterialMgmt}/alter/saveMRPViewAsDraft`;
        } else if (activeTab === displayData?.selectedViews?.value?.map((item) => item.value).findIndex((item) => item === "accounting")) {
          var saveActiveTabData = `/${destination_MaterialMgmt}/alter/saveAccountingAsDraft`;
        }

        const hSuccess = (data) => {
          setIsLoading();
          if (data.statusCode === 201) {
            console.log("success");
            setMessageDialogTitle("Create");
            setMessageDialogMessage(`Data has been saved successfully`);
            setMessageDialogSeverity("success");
            setMessageDialogOK(false);
            setsuccessMsg(true);
            handleSnackBarOpen();
            setMessageDialogExtra(true);
            setIsLoading(false);
          } else {
            // setMessageDialogTitle("Create");
            // setsuccessMsg(false);
            // setMessageDialogMessage("Creation Failed");
            // setMessageDialogSeverity("danger");
            // setMessageDialogOK(false);
            // setMessageDialogExtra(true);
            // handleMessageDialogClickOpen();
            // setIsLoading(false);
          }
          handleClose();
        };
        const hError = (error) => {
          console.log(error);
        };
        doAjax(saveActiveTabData, "post", hSuccess, hError, payload);
      }
    } else {
      handleSnackBarOpenValidation();
    }
  };
  const onForwardClick = () => {
    setIsToastPromise(true);
    setIsLoadingToast(true);
    handleForwardReviewDialogClose();
    const isValidation = handleCheckValidationError();
    if (isValidation) {
      setIsForwardClicked(true);
      if (activeTab === displayData?.selectedViews?.value?.map((item) => item.value).findIndex((item) => item === "basicData")) {
        dispatch(updateTabStatus(activeTab));
        // payloadKey["keyName"] = "creationId";
        // payloadKey["data"] = data.body;
        // console.log(payloadKey);

        var createBasicDataUrl = `/${destination_MaterialMgmt}/alter/createBasicDataAndForward`;
        const hSuccess = (data) => {
          console.log(data, "example");
          setIsLoading();
          console.log("TESTDATA");
          if (data.statusCode === 200) {
            // debugger
            dispatch(setPayload({ keyName: "creationId", data: data.body }));
            console.log("success");
            // setMessageDialogTitle("Create");
            // setMessageDialogMessage(
            //   `Material  has been created successfully with creation ID ${data.body}`
            // );
            // setMessageDialogSeverity("success");
            // setMessageDialogOK(false);
            // setsuccessMsg(true);
            // handleSnackBarOpen();
            // setMessageDialogExtra(true);
            // setIsLoading(false);
            setIsLoadingToast(false);
            setToastType("success");
            setToastMessage(`Material  has been created successfully with creation ID ${data.body}`);

            distinctArtifactId.map((artifactId) => {
              console.log(artifactId, "artifactId=====");
              const secondApiPayload = {
                artifactId: artifactId,
                createdBy: userData?.emailId,
                artifactType: "Material",
                requestId: `NMS${data?.body}`,
              };
              const secondApiSuccess = (secondApiData) => {
                console.log("Second API success", secondApiData);
                // Handle success for the second API if needed
              };

              const secondApiError = (secondApiError) => {
                console.error("Second API error", secondApiError);
                // Handle error for the second API if needed
              };
              // {requestId&&
              doAjax(`/${destination_DocumentManagement}/documentManagement/updateDocRequestId`, "post", secondApiSuccess, secondApiError, secondApiPayload);
            });
          } else {
            // setMessageDialogTitle("Create");
            // setsuccessMsg(false);
            // setMessageDialogMessage("Creation Failed");
            // setMessageDialogSeverity("danger");
            // setMessageDialogOK(false);
            // setMessageDialogExtra(true);
            // handleMessageDialogClickOpen();
            // setIsLoading(false);
            setIsLoadingToast(false);
            setToastType("error");
            setToastMessage("Creation Failed");
          }
          handleClose();
        };
        const hError = (error) => {
          console.log(error);
          setIsLoadingToast(false);
          setToastType("error");
          setToastMessage("Creation Failed");
        };
        doAjax(createBasicDataUrl, "post", hSuccess, hError, payload);
      }
    } else {
      handleSnackBarOpenValidation();
    }
  };
  const handleSubmitForReview = () => {
    const isValidation = handleCheckValidationError();
    if (isValidation) {
      dispatch(updateTabStatus(activeTab));
      const isValidation = handleCheckValidationError();
      if (isValidation) {
        if (activeTab === displayData?.selectedViews?.value?.map((item) => item.value).findIndex((item) => item === "sales")) {
          var saveActiveTabData = `/${destination_MaterialMgmt}/alter/createSalesAndSubmitForReview`;
        } else if (activeTab === displayData?.selectedViews?.value?.map((item) => item.value).findIndex((item) => item === "purchasing")) {
          var saveActiveTabData = `/${destination_MaterialMgmt}/alter/createPurchaseAndSubmitForReview`;
        } else if (activeTab === displayData?.selectedViews?.value?.map((item) => item.value).findIndex((item) => item === "mrp")) {
          var saveActiveTabData = `/${destination_MaterialMgmt}/alter/createMRPAndSubmitForReview`;
        } else if (activeTab === displayData?.selectedViews?.value?.map((item) => item.value).findIndex((item) => item === "accounting")) {
          var saveActiveTabData = `/${destination_MaterialMgmt}/alter/createAccountingAndSubmitForReview`;
        }

        const hSuccess = (data) => {
          setIsLoading();
          if (data.statusCode === 201) {
            console.log("success");
            setMessageDialogTitle("Create");
            setMessageDialogMessage(`Material has been submitted for review`);
            setMessageDialogSeverity("success");
            setMessageDialogOK(false);
            setsuccessMsg(true);
            handleSnackBarOpen();
            setMessageDialogExtra(true);
            setIsLoading(false);
          } else {
            // setMessageDialogTitle("Create");
            // setsuccessMsg(false);
            // setMessageDialogMessage("Creation Failed");
            // setMessageDialogSeverity("danger");
            // setMessageDialogOK(false);
            // setMessageDialogExtra(true);
            // handleMessageDialogClickOpen();
            // setIsLoading(false);
          }
          handleClose();
        };
        const hError = (error) => {
          console.log(error);
        };
        doAjax(saveActiveTabData, "post", hSuccess, hError, payload);
      }
    } else {
      handleSnackBarOpenValidation();
    }
  };

  const handleCreateDialog = () => {
    setOpenCreateDialog(true);
  };
  const handleForwardDialog = () => {
    setOpenForwardReview(true);
  };

  const handleCreateDialogClose = () => {
    setOpenCreateDialog(false);
  };

  const handleForwardReviewDialogClose = () => {
    setOpenForwardReview(false);
  };

  const handleSubmitForApproval = () => {
    setIsToastPromise(true);
    setIsLoadingToast(true);
    handleCreateDialogClose();
    const isValidation = handleCheckValidationError();
    if (isValidation) {
      if (selectedTabs.value.length === 1) {
        var submitForApprovalBasicData = `/${destination_MaterialMgmt}/alter/createBasicDataApprovalSubmit`;
        const hSuccess = (data) => {
          if (data.statusCode === 200) {
            dispatch(setPayload({ keyName: "creationId", data: data.body }));
         
            // setMessageDialogTitle("Create");
            // setMessageDialogMessage(
            //   `Material  has been created successfully with Material No ${data.body}`
            // );
            // setMessageDialogSeverity("success");
            // setMessageDialogOK(false);
            // setsuccessMsg(true);
            // handleSnackBarOpen();
            // setMessageDialogExtra(true);
            // setIsLoading(false);
            setIsLoadingToast(false);
            setToastType("success");
            setToastMessage(`Material  has been created successfully with Material No ${data.body}`);
            dispatch(clearPayload());
            dispatch(clearOrgData());
            dispatch(clearTabStatus());

            distinctArtifactId.map((artifactId) => {
              const secondApiPayload = {
                artifactId: artifactId,
                createdBy: userData?.emailId,
                artifactType: "Material",
                requestId: `NMS${data?.body}`,
              };
              const secondApiSuccess = (secondApiData) => {
                console.log("Second API success", secondApiData);
                // Handle success for the second API if needed
              };

              const secondApiError = (secondApiError) => {
                console.error("Second API error", secondApiError);
                // Handle error for the second API if needed
              };
              // {requestId&&
              doAjax(`/${destination_DocumentManagement}/documentManagement/updateDocRequestId`, "post", secondApiSuccess, secondApiError, secondApiPayload);
            });
          } else {
            // setMessageDialogTitle("Create");
            // setsuccessMsg(false);
            // setMessageDialogMessage("Creation Failed");
            // setMessageDialogSeverity("danger");
            // setMessageDialogOK(false);
            // setMessageDialogExtra(true);
            // handleMessageDialogClickOpen();
            // setIsLoading(false);
            setIsLoadingToast(false);
            setToastType("error");
            setToastMessage("Creation Failed");
          }
          handleClose();
        };
        const hError = (error) => {
          setIsLoadingToast(false);
          setToastType("error");
          setToastMessage("Creation Failed");
        };
        doAjax(submitForApprovalBasicData, "post", hSuccess, hError, payload);
      } else if (factorsArray.length > 1) {
        var submitForApprovalBasicData = `/${destination_MaterialMgmt}/alter/allViewsApproved`;
        const hSuccess = (data) => {
          console.log(data, "example");
          setIsLoading();
          if (data.statusCode === 201) {
            dispatch(setPayload({ keyName: "creationId", data: data.body }));
            console.log("success");
            setMessageDialogTitle("Create");
            setMessageDialogMessage(`Material  has been created successfully with creation ID ${data.body}`);
            setMessageDialogSeverity("success");
            setMessageDialogOK(false);
            setsuccessMsg(true);
            handleSnackBarOpen();
            setMessageDialogExtra(true);
            setIsLoading(false);
          } else {
            // setMessageDialogTitle("Create");
            // setsuccessMsg(false);
            // setMessageDialogMessage("Creation Failed");
            // setMessageDialogSeverity("danger");
            // setMessageDialogOK(false);
            // setMessageDialogExtra(true);
            // handleMessageDialogClickOpen();
            // setIsLoading(false);
          }
          handleClose();
        };
        const hError = (error) => {
          console.log(error);
        };
        doAjax(submitForApprovalBasicData, "post", hSuccess, hError, payload);
      }
    } else {
      handleSnackBarOpenValidation();
    }
    // if (isValidation) {
    //   if (
    //     activeTab ===
    //     displayData?.selectedViews?.value
    //       ?.map((item) => item.value)
    //       .findIndex((item) => item === "basicData")
    //   ) {
    //     var submitForApprovalBasicData = `/${destination_MaterialMgmt}/alter/createBasicDataApproved`;
    //     const hSuccess = (data) => {
    //       console.log(data, "example");
    //       setIsLoading();
    //       if (data.statusCode === 201) {
    //         dispatch(setPayload({ keyName: "creationId", data: data.body }));
    //         console.log("success");
    //         setMessageDialogTitle("Create");
    //         setMessageDialogMessage(
    //           `Material  has been created successfully with creation ID ${data.body}`
    //         );
    //         setMessageDialogSeverity("success");
    //         setMessageDialogOK(false);
    //         setsuccessMsg(true);
    //         handleSnackBarOpen();
    //         setMessageDialogExtra(true);
    //         setIsLoading(false);
    //       } else {
    //         // setMessageDialogTitle("Create");
    //         // setsuccessMsg(false);
    //         // setMessageDialogMessage("Creation Failed");
    //         // setMessageDialogSeverity("danger");
    //         // setMessageDialogOK(false);
    //         // setMessageDialogExtra(true);
    //         // handleMessageDialogClickOpen();
    //         // setIsLoading(false);
    //       }
    //       handleClose();
    //     };
    //     const hError = (error) => {
    //       console.log(error);
    //     };
    //     doAjax(submitForApprovalBasicData, "post", hSuccess, hError, payload);
    //   }
    // } else {
    //   handleSnackBarOpenValidation();
    // }
  };
  useEffect(() => {
    console.log("pay", PayloadData?.payloadData);
  }, [PayloadData]);

  const tabContents = displayData?.selectedViews?.value?.map((item) => {
    console.log("TabContents", item.value);

    if (item?.value == "basicData") {
      return [<GenericTabs basicData={basicData} setBasicData={setBasicData} dropDownData={dropDownData} basicDataTabDetails={basicDataTabDetails} />];
    } else if (item?.value == "classification") {
      return [<ClassificationTabs basicData={basicData} setBasicData={setBasicData} dropDownData={dropDownData} />];
    } else if (item?.value == "sales") {
      return [<SalesTab basicData={basicData} setBasicData={setBasicData} dropDownData={dropDownData} salesDataTabDetails={salesDataTabDetails} />];
    } else if (item.value == "purchasing") {
      return [<PurchasingDataTab basicData={basicData} setBasicData={setBasicData} dropDownData={dropDownData} purchasingDataTabDetails={purchasingDataTabDetails} />];
    } else if (item.value == "mrp") {
      return [<MRPDataTab basicData={basicData} setBasicData={setBasicData} dropDownData={dropDownData} MRPDataTabDetails={MRPDataTabDetails} />];
    } else if (item.value == "accounting") {
      return [<AccountingTab basicData={basicData} setBasicData={setBasicData} dropDownData={dropDownData} accountingTabDetails={accountingTabDetails} />];
    } else if (item.value == "storageLocationStocks") {
      return [<h1>Storage Location Stocks Data</h1>];
    } else if (item.value == "storage") {
      return [<h1>Storage Data</h1>];
    } else if (item.value == "qualityManagement") {
      return [<h1>"Quality Management" Data</h1>];
    } else if (item.value == "attachments&comments") {
      return [
        <>
          {attachmentsData.map((attachment, index) => (
            <Grid
              key={index}
              item
              md={12}
              sx={{
                backgroundColor: "white",
                maxHeight: "max-content",
                height: "max-content",
                borderRadius: "8px",
                border: "1px solid #E0E0E0",
                mt: 0.25,
                boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                padding: "1rem 1.5rem",
                // ...container_Padding,
                // ...container_columnGap,
              }}
            >
              <Grid container>
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                  }}
                >
                  {attachment.MDG_ATTACHMENTS_NAME}
                </Typography>
              </Grid>

              <Grid container>
                <Grid item>
                  <ReusableAttachementAndComments title="Material" useMetaData={false} artifactId={`${attachment.MDG_ATTACHMENTS_NAME}_${mmNumber}`} artifactName="Material" attachmentType={attachment.MDG_ATTACHMENTS_NAME} />
                </Grid>
              </Grid>
            </Grid>
          ))}
        </>,
      ];
    } else if (item.value == "generalInformation") {
      return [
        // <h1>general</h1>
        <GeneralInformationTabForMaterial
          questions={giInfo}
          // handleCheckValidationError={handleCheckValidationError}
        />,
      ];
    } else {
      return [<h1>attach</h1>];
    }
  });

  const shouldDisableTab = (index, allMaterialTabs, factorsArray) => {
    if (isForwardClicked) {
      return false; // No tabs are disabled if isForwardClicked is true
    }

    const lastFactor = factorsArray[factorsArray.length - 1];
    const lastIndex = allMaterialTabs.indexOf(lastFactor);

    const isInitialTab = index === 0;
    const isSecondTab = index === 1;
    const isLastTab = index === lastIndex;

    // Disable all tabs except index 0, 1, and the last tab in factorsArray
    return !(isInitialTab || isSecondTab || isLastTab);
  };

  console.log("factor", factorsArray);
  return (
    <div>
      <ReusableDialog dialogState={openMessageDialog} openReusableDialog={handleMessageDialogClickOpen} closeReusableDialog={handleMessageDialogClose} dialogTitle={messageDialogTitle} dialogMessage={messageDialogMessage} handleDialogConfirm={handleMessageDialogClose} dialogOkText={"OK"} handleExtraButton={handleMessageDialogNavigate} dialogSeverity={messageDialogSeverity} />

      <Dialog open={correctionPopUp} onClose={handleCommentClose} sx={{ display: "flex", justifyContent: "center" }}>
        <Box sx={{ width: "600px !important" }}>
          <DialogTitle>
            <DescriptionIcon
              style={{
                height: "20px",
                width: "20px",
                marginBottom: "-5px",
              }}
            />
            <span>Enter Comments for Correction</span>
          </DialogTitle>
          <DialogContent>
            <Grid container columnSpacing={1}>
              <textarea />
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCommentClose}>Cancel</Button>
            <Button onClick={commentAction}>OK</Button>
          </DialogActions>
        </Box>
      </Dialog>
      {
        // successMsg &&
        // <ReusableSnackBar
        //   openSnackBar={openSnackbar}
        //   alertMsg={messageDialogMessage}
        //   handleSnackBarClose={handleSnackBarClose}
        // />
        <ReusableToast isLoadingToast={isLoadingToast} type={toastType} message={toastMessage} isToastPromise={isToastPromise} />
      }
      {formValidationErrorItems.length != 0 && <ReusableSnackBar openSnackBar={openSnackbarValidation} alertMsg={"Please fill the following Field: " + formValidationErrorItems.join(", ")} handleSnackBarClose={handleSnackBarCloseValidation} />}
      <Grid
        container
        style={{
          ...outermostContainer_Information,
          backgroundColor: "#FAFCFF",
        }}
      >
        <Grid sx={{ width: "inherit" }}>
          <Grid item md={12} sx={{ display: "flex", marginBottom: "0" }}>
            <Grid item md={11} style={{ padding: "16px", display: "flex" }}>
              <div style={{ display: "flex" }}>
                <div>
                  <ArrowCircleLeftOutlinedIcon
                    style={{ height: "1em", width: "1em", color: "#000000" }}
                    onClick={() => {
                      navigate("/masterDataCockpit/materialMaster/newMaterial");
                      dispatch(clearPayload());
                      dispatch(clearOrgData());
                      dispatch(clearTabStatus());
                      dispatch(commonFilterClear({ module: "DuplicateDesc" }));
                    }}
                  />
                </div>
                <div>
                  <Typography variant="h3">
                    <strong>Create Material</strong>
                  </Typography>
                  <Typography variant="body2" color="#777">
                    This view creates a new material
                  </Typography>
                </div>
              </div>
            </Grid>
            {/* <Grid item md={1} sx={{ padding: "16px", display: "flex" }}>
              <Tooltip title="Upload documents if any" arrow>
                <IconButton onClick={handleOpenAttachDialog}>
                  <AttachFileOutlinedIcon />
                </IconButton>
              </Tooltip>
            </Grid> */}
            <Dialog
              hideBackdrop={false}
              elevation={2}
              PaperProps={{
                sx: { boxShadow: "none" },
              }}
              open={openAttachDialog}
              onClose={handleCloseAttachDialog}
            >
              <DialogTitle
                sx={{
                  justifyContent: "space-between",
                  alignItems: "center",
                  height: "max-content",
                  padding: ".5rem",
                  paddingLeft: "1rem",
                  backgroundColor: "#EAE9FF40",
                  // borderBottom: "1px solid grey",
                  display: "flex",
                }}
              >
                <Typography variant="h6">Add Attachment</Typography>
              </DialogTitle>
              <DialogContent sx={{ padding: ".5rem 1rem" }}>
                <Stack>
                  <Box sx={{ minWidth: 400 }}>
                    <ReusableAttachementAndComments title="Material" useMetaData={false} artifactId={mmNumber} artifactName="Material" attachmentType={"SAP Error"} />
                  </Box>
                </Stack>
              </DialogContent>
              <DialogActions>
                <Button onClick={handleCloseAttachDialog}>Close</Button>
              </DialogActions>
            </Dialog>
          </Grid>

          <Grid container style={{ padding: "0 1rem 0 1rem" }}>
            <Grid container sx={outermostContainer_Information}>
              <Grid container display="flex" flexDirection="row" flexWrap="nowrap">
                <Box width="70%" sx={{ marginLeft: "40px" }}>
                  <Grid item sx={{ paddingTop: "2px !important" }}>
                    <Stack flexDirection="row">
                      <div style={{ width: "15%" }}>
                        <Typography variant="body2" color="#777">
                          Material
                        </Typography>
                      </div>
                      <Typography variant="body2" fontWeight="bold" justifyContent="flex-start">
                        : {displayData?.materialNo?.materialNumber}
                      </Typography>
                    </Stack>
                  </Grid>

                  <Grid item sx={{ paddingTop: "2px !important" }}>
                    <Stack flexDirection="row">
                      <div style={{ width: "15%" }}>
                        <Typography variant="body2" color="#777">
                          Material Description
                        </Typography>
                      </div>
                      <Typography variant="body2" fontWeight="bold">
                        : {/* {displayData?.materialDescription?.materialDescription} */}
                        {nmSearchForm?.description}
                      </Typography>
                    </Stack>
                  </Grid>
                  <Grid item sx={{ paddingTop: "2px !important" }}>
                    <Stack flexDirection="row">
                      <div style={{ width: "15%" }}>
                        <Typography variant="body2" color="#777">
                          Industry Sector
                        </Typography>
                      </div>
                      <Typography variant="body2" fontWeight="bold">
                        : {displayData?.industrySector?.industryReference ? `${displayData?.industrySector?.industryReference} - ${displayData?.industrySectorDesc?.industrySectorDesc}` : ""}
                      </Typography>
                    </Stack>
                  </Grid>

                  <Grid item sx={{ paddingTop: "2px !important" }}>
                    <Stack flexDirection="row">
                      <div style={{ width: "15%" }}>
                        <Typography variant="body2" color="#777">
                          Material Type
                        </Typography>
                      </div>
                      <Typography variant="body2" fontWeight="bold">
                        : {displayData?.materialType?.fieldReference} - {displayData?.materialTypeDesc?.materialTypeDesc}
                      </Typography>
                    </Stack>
                  </Grid>
                </Box>
                <Box width="30%" sx={{ marginLeft: "40px" }}>
                  {displayData?.orgData?.map((item) => {
                    return (
                      <Grid item>
                        <Stack flexDirection="row">
                          <Typography variant="body2" color="#777" style={{ width: "30%" }}>
                            {item.info ? item.desc : ""}
                          </Typography>

                          <Typography variant="body2" fontWeight="bold" sx={{ width: "8%", textAlign: "center" }}>
                            {item.info ? ":" : ""}
                          </Typography>

                          <Typography variant="body2" fontWeight="bold" justifyContent="flex-start">
                            {item?.info?.code}
                            {item?.info?.code && item?.info?.desc ? " - " : null}
                            {item?.info?.desc}
                          </Typography>
                        </Stack>
                      </Grid>
                    );
                  })}
                </Box>
              </Grid>

              {basicDataTabDetails && (
                <Grid container>
                  <Tabs
                    value={activeTab}
                    onChange={handleChange}
                    variant="scrollable"
                    sx={{
                      background: "#FFF",
                      borderBottom: "1px solid #BDBDBD",
                      width: "100%",
                    }}
                    aria-label="mui tabs example"
                  >
                    {allMaterialTabs?.map((factor, index) => {
                      console.log("allmate", factor, index);
                      return (
                        checkIwaAccess(iwaAccessData, "Create Single Material", factor) &&
                        (rolesAccessTabs[userData.role] == ("MDM Steward" || "Super User") ? factorsArray.includes(factor) : factorsArray.includes(factor)) && (
                          <Tab
                            sx={{ fontSize: "12px", fontWeight: "700" }}
                            key={index}
                            label={factor}
                            disabled={shouldDisableTab(index, allMaterialTabs, factorsArray)}

                            // disabled={!isForwardClicked && index !== 0 && factor !== "Attachments & Comments"}
                            // disabled={!isForwardClicked && index !== 1}
                            // disabled={!isForwardClicked && !(index === 0 || index === 1 || index === displayData.selectedViews.value.length - 1}
                          />
                        )
                      );
                    })}
                  </Tabs>
                </Grid>
              )}
              <Grid container>
                {basicDataTabDetails &&
                  tabContents &&
                  tabContents[activeTab]?.map((cardContent, index) => (
                    <Box key={index} sx={{ width: "100%" }}>
                      {cardContent}
                    </Box>
                  ))}
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Grid>

      <Paper sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }} elevation={2}>
        <BottomNavigation
          className="container_BottomNav"
          showLabels
          sx={{
            display: "flex",

            justifyContent: "flex-end",
          }}
          value={value}
          // onChange={(newValue) => {
          // setValue(newValue);
          // }}
        >
          {activeTab === 1 ? (
            <Button
              variant="outlined"
              size="small"
              sx={{ button_Outlined, mr: 1 }}
              onClick={() => {
                navigate(`/masterDataCockpit/materialMaster/createMaterialDetail/additionalData`);
              }}
            >
              Additional Data
            </Button>
          ) : null}

          {/* {tabsStatus.filter((item) => item.status === true).length !=
          tabsStatus.length? (
            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary, mr: 1 }}
              onClick={onSaveButtonClick}
              disabled={
                tabsStatus
                  .filter((item) => item.status == true)
                  .findIndex(
                    (item) =>
                      item.keyname ==
                      displayData?.selectedViews?.value?.map(
                        (itemtab) => itemtab.value
                      )[activeTab]
                  ) != -1
                  ? true
                  : false
              }
            >
              Save As Draft
            </Button>
          ) : null} */}

          {activeTab !== 0 && activeTab !== displayData?.selectedViews?.value.length - 1 ? (
            <Button variant="contained" size="small" sx={{ ...button_Primary, mr: 1 }} onClick={onSaveButtonClick} disabled={tabsStatus.filter((item) => item.status == true).findIndex((item) => item.keyname == displayData?.selectedViews?.value?.map((itemtab) => itemtab.value)[activeTab]) != -1 ? true : false}>
              Save As Draft
            </Button>
          ) : null}

          {activeTab === displayData?.selectedViews?.value?.map((item) => item.value).findIndex((item) => item === "basicData") && displayData?.selectedViews?.value.length > 3 && tabsStatus.filter((item) => item.status === true).length !== tabsStatus.length ? (
            <Button
              variant="contained"
              size="small"
              disabled={isForwardClicked ? true : false}
              sx={{ ...button_Primary, mr: 1 }}
              // disabled= {!isForwardClicked? 'true' : 'false'}
              // onClick={onForwardClick}
              onClick={handleForwardDialog}
            >
              Forward
            </Button>
          ) : null}

          {/* {activeTab != 0 ? (
            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary }}
              onClick={handleSubmitForReview}
            >
              Submit for Review
            </Button>
          ) : null} */}

          {activeTab !== 0 && activeTab !== 1 && activeTab !== displayData?.selectedViews?.value.length - 1 ? (
            <Button variant="contained" size="small" sx={{ ...button_Primary }} onClick={handleSubmitForReview}>
              Submit for Review
            </Button>
          ) : null}

          {/* {displayData.selectedViews.value.length === 1 ||
          (activeTab === 0 &&
            tabsStatus.filter((item) => item.status === false).length === 0) ? (
            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary }}
              // onClick={handleSubmitForApproval}
              onClick={handleCreateDialog}
            >
              Submit for Approval
            </Button>
          ) : null} */}

          {activeTab === 1 && selectedTabs.value.length === 1 ? (
            <Button variant="contained" size="small" sx={{ ...button_Primary }} onClick={handleCreateDialog}>
              Submit for Approval
            </Button>
          ) : null}

          <Dialog
            hideBackdrop={false}
            elevation={2}
            PaperProps={{
              sx: { boxShadow: "none", marginBottom: "8px", borderRadius: "8px" },
            }}
            open={openCreateDialog}
            onClose={handleCreateDialogClose}
          >
            <DialogTitle
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF",
                borderBottom: "3px ",
                display: "flex",
                marginBottom: "8px",
              }}
            >
              <Typography variant="h6">Remarks</Typography>

              <IconButton sx={{ width: "max-content" }} onClick={handleCreateDialogClose} children={<CloseIcon />} />
            </DialogTitle>
            <DialogContent sx={{ padding: ".5rem 1rem" }}>
              <Stack>
                <Box sx={{ minWidth: 400 }}>
                  <FormControl sx={{ height: "auto" }} fullWidth>
                    <TextField
                      sx={{ backgroundColor: "#F5F5F5" }}
                      // value={inputText}
                      value={remarks}
                      onChange={handleRemarks}
                      multiline
                      placeholder={"Enter Remarks"}
                      inputProps={{ maxLength: 254 }}
                    ></TextField>
                  </FormControl>
                </Box>
              </Stack>
            </DialogContent>
            <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
              <Button sx={{ width: "max-content", textTransform: "capitalize" }} onClick={handleCreateDialogClose} variant="outlined">
                Cancel
              </Button>
              <Button className="button_primary--normal" type="save" onClick={handleSubmitForApproval} variant="contained">
                Submit
              </Button>
            </DialogActions>
          </Dialog>
          <Dialog
            hideBackdrop={false}
            elevation={2}
            PaperProps={{
              sx: { boxShadow: "none", marginBottom: "8px", borderRadius: "8px" },
            }}
            open={openForwardReview}
            onClose={handleForwardReviewDialogClose}
          >
            <DialogTitle
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF",
                borderBottom: "3px",
                display: "flex",
                marginBottom: "8px",
                // color: "white"
              }}
            >
              <Typography variant="h6">Remarks</Typography>

              <IconButton sx={{ width: "max-content" }} onClick={handleForwardReviewDialogClose} children={<CloseIcon />} />
            </DialogTitle>
            <DialogContent sx={{ padding: ".5rem 1rem" }}>
              <Stack>
                <Box sx={{ minWidth: 400 }}>
                  <FormControl sx={{ height: "auto" }} fullWidth>
                    <TextField
                      sx={{ backgroundColor: "#F5F5F5" }}
                      // value={inputText}
                      value={remarks}
                      onChange={handleRemarks}
                      multiline
                      placeholder={"Enter Remarks"}
                      inputProps={{ maxLength: 254 }}
                    ></TextField>
                  </FormControl>
                </Box>
              </Stack>
            </DialogContent>
            <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
              <Button sx={{ width: "max-content", textTransform: "capitalize" }} onClick={handleForwardReviewDialogClose} variant="outlined">
                Cancel
              </Button>
              <Button className="button_primary--normal" type="save" onClick={onForwardClick} variant="contained">
                Submit
              </Button>
            </DialogActions>
          </Dialog>
        </BottomNavigation>
      </Paper>
    </div>
  );
};

export default CreateMaterialDetail;
