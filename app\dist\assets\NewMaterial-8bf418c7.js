import{b as Vt,s as It,r,O as re,bu as b,bD as Rt,K as f,q as B,a as l,j as c,z as zt,x as Ft,G as i,I as Wt,b1 as Lt,T as g,bE as N,aq as m,b6 as P,E as C,h as ne,b7 as Bt,t as M,b8 as Xt,ay as jt,V as qt,B as _t,aF as Ht,W as Gt,X as Kt,F as Ut,bw as ce,Z as x,P as $}from"./index-75c1660a.js";import{d as Zt}from"./ArrowCircleLeftOutlined-f7b52d40.js";import{d as Jt,a as Qt}from"./CheckBox-09a94074.js";import{d as Yt}from"./Description-1dd6e306.js";import{D as ea}from"./DuplicateDesc-0ffe7a85.js";const ta=l(Jt,{fontSize:"small"}),aa=l(Qt,{fontSize:"small"}),p=[{label:"Basic Data",value:"basicData",fields:[],code:"K"},{label:"Sales",value:"sales",fields:["Plant","Sales Organization","Distribution Channel"],code:"V"},{label:"Purchasing",value:"purchasing",fields:["Plant"],code:"E"},{label:"Accounting",value:"accounting",fields:["Plant"],code:"B"}],Da=()=>{const T=Vt(),d=It();r.useState(!1);const[u,D]=r.useState([]),[X,j]=r.useState(!1),[ie,q]=r.useState(!1),[oe,de]=r.useState([]),[ue,ge]=r.useState([]),[h,he]=r.useState([]),[A,fe]=r.useState([]),[w,me]=r.useState([]);console.log("displayedFields",w);const[xe,Se]=r.useState([]),[pe,ye]=r.useState([]),[be,De]=r.useState([]),[Ce,Me]=r.useState([]),[la,we]=r.useState([]),[V,ve]=r.useState([]),[sa,_]=r.useState(!0),[Ee,ke]=r.useState([]),[Oe,Ne]=r.useState([]);r.useState([]);const[Pe,$e]=r.useState([]);r.useState([]),r.useState([]);const[Te,Ae]=r.useState(""),[Ve,Ie]=r.useState(""),[Re,ze]=r.useState("");r.useState("");const[ra,Fe]=r.useState(""),[na,We]=r.useState(""),[Le,ca]=r.useState(""),[ia,Be]=r.useState(""),[H,G]=r.useState(!0),[K,U]=r.useState(!0),[Z,Xe]=r.useState(!0),[J,Q]=r.useState(!0),[je,v]=r.useState(!1),[I,oa]=r.useState([]);r.useState([]);const[E,R]=r.useState([]),[da,qe]=r.useState(!1),[ua,_e]=r.useState(!1),[He,z]=r.useState(""),[ga,Ge]=r.useState(!1),[Ke,Ue]=r.useState(!1),[ha,Ze]=r.useState(!0),[fa,Je]=r.useState(!1),[Qe,Y]=r.useState(!1),[Ye,et]=r.useState(""),[tt,at]=r.useState({}),[lt,st]=r.useState(!1),y={orgData:[{info:Te,desc:"Plant"},{info:Ve,desc:"Sales Organization"},{info:Re,desc:"Distribution Channel"}],materialNo:{materialNumber:Ye},industrySector:{industryReference:pe},industrySectorDesc:{industrySectorDesc:be},materialType:{fieldReference:xe},materialTypeDesc:{materialTypeDesc:Ce},materialDescription:{materialDescription:Le},selectedViews:{value:u}};console.log("datatosend",y),r.useEffect(()=>{gt(),ht(),d(re({module:"NewMaterial"})),d(b([]))},[]),r.useEffect(()=>{d(Rt(I))},[I]);const rt=t=>{if(console.log("displayedfieldsss",t),t.includes("Plant")){const e=s=>{ve(s.body)},a=s=>{console.log(s)};f(`/${x}/data/getPlant`,"get",e,a)}if(t.includes("Sales Organization")){const e=s=>{ke(s.body)},a=s=>{console.log(s)};f(`/${x}/data/getSalesOrg`,"get",e,a)}if(t.includes("MRP Profile")){const e=s=>{},a=s=>{console.log(s)};f(`/${x}/data/getPlant`,"get",e,a)}};console.log(V);const nt=t=>{if(console.log("eeeeeeee",t),w.includes("Warehouse No")){const e=s=>{$e(s.body)},a=s=>{console.log(s)};f(`/${x}/data/getWareHouseNoForPlant?plant=${t.code}`,"get",e,a)}},ct=t=>{if(console.log("eeeeeeeeeee",t.code),w.includes("Distribution Channel")){const e=s=>{Ne(s.body)},a=s=>{console.log(s)};f(`/${x}/data/getDistributionForSalesOrg?salesOrg=${t.code}`,"get",e,a)}};console.log("forvslidation",I),console.log("displayfields",w),console.log("dataToSend",E);const it=()=>{if(y.orgData.map(t=>t.info)==="")alert("Please enter necessary data");else{const t={label:"Attachments & Comments",value:"attachments&comments"},a=[{label:"General Information",value:"generalInformation"},...u,t],s={value:a};console.log("dddd1",u),console.log("dddd2",a),console.log("dddd3",s),y.selectedViews=s,d(ce(y)),T("/masterDataCockpit/materialMaster/createMaterialDetail",{state:y})}},ot=()=>{d(re({module:"NewMaterial"}))},dt=t=>{v(!0),D(k),d(b(k)),n.selectViewsValue=k},ut=()=>{var o,O;const t={label:"Attachments & Comments",value:"attachments&comments"},s={value:[{label:"General Information",value:"generalInformation"},...u,t]};if(y.selectedViews=s,d(ce(y)),!((o=n==null?void 0:n.sector)!=null&&o.code)){G(!1);return}if(!((O=n==null?void 0:n.type)!=null&&O.code)){U(!1);return}if(!(n!=null&&n.description)){Xe(!1);return}if(!se&&!(n!=null&&n.materialNo)){Q(!1);return}if(u.length===2&&u.some(S=>S.label==="Basic Data")&&u.some(S=>S.label==="Classification")||u.length===1&&u.some(S=>S.label==="Basic Data"))T("/masterDataCockpit/materialMaster/createMaterialDetail",{state:y});else{const S=Array.from(new Set(u.reduce((Tt,At)=>[...Tt,...At.fields],[])));console.log("vvlaue",u),rt(S),me(S),q(!0),console.log("valuesselected",S)}},ee=()=>{q(!1)},gt=()=>{const t=a=>{de(a.body)},e=a=>{console.log(a)};f(`/${x}/data/getIndustrySector`,"get",t,e)},ht=()=>{const t=a=>{ge(a.body)},e=a=>{console.log(a)};f(`/${x}/data/getMaterialType`,"get",t,e)},ft=t=>{const e=s=>{he(s.body),console.log("body",s.body)},a=s=>{console.log(s)};f(`/${x}/data/getNumberRangeForMaterialType?materialType=${t}`,"get",e,a)},mt=t=>{const e=s=>{fe(s.body[0].MaintStatus.split("")),Se(s.body[0].MaterialType),te(A)},a=s=>{console.log(s)};f(`/${x}/data/getViewForMaterialType?materialType=${t==null?void 0:t.code}`,"get",e,a)};function te(t){const e=[];return console.log("codes",t),p.forEach(a=>{t.includes(a.code)&&e.push({label:a.label,value:a.value,fields:a.fields})}),e}const k=te(A);console.log("autocompleteOptions",A);const xt=t=>{switch(t){case"Plant":return V.map(e=>e);case"Sales Organization":return Ee.map(e=>e);case"Distribution Channel":return Oe.map(e=>e);case"MRP Profile":return V.map(e=>e);case"Warehouse No":return Pe.map(e=>e);default:return[]}},St=(t,e)=>{console.log("selected",e);const a={...tt,[t]:e};at(a);const s=w.every(o=>a[o]);switch(st(s),t){case"Plant":nt(e),Ae(e),R([...E,"Plant"]);break;case"Sales Organization":ct(e),Ie(e),R([...E,"Sales Organization"]);break;case"Distribution Channel":ze(e),R([...E,"Distribution Channel"]);break;case"Warehouse No":We(e);break;case"MRP Profile":Fe(e);break}},ae=(t,e)=>{switch(t){case"Plant":return(e==null?void 0:e.plant)===""?"Select Plant":`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`;case"Sales Organization":return(e==null?void 0:e.Salesorg)===""?"Select Sales Organization":`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`;case"Distribution Channel":return(e==null?void 0:e.DistributionChannel)===""?"Select Distribution Channel":`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`;case"MRP Profile":return(e==null?void 0:e.plant)===""?"Select MRP Profile":`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`;case"Warehouse No":return(e==null?void 0:e.WareHouseNo)===""?"Select Plant":`${e==null?void 0:e.code}`;default:return""}},pt=(t,e,a)=>{switch(t){case"Plant":case"Sales Organization":case"Distribution Channel":case"MRP Profile":case"Warehouse No":return l("li",{...e,children:l(g,{style:{fontSize:12},children:ae(t,a)})});default:return null}},n=B(t=>t.commonFilter.NewMaterial);console.log("nmSearchForm",n);const F=B(t=>t.tabsData.value),yt=B(t=>t.tabsData.materialType);console.log("valueRequiredForSelectViews",F),console.log("valueRequiredForMaterialType",yt);const bt=(t,e)=>{console.log("binat",Pt,e);var a=e;let s={...n,sector:a};d($({module:"NewMaterial",filterData:s})),G(!0),ye(a.code),De(a.desc)},Dt=(t,e)=>{var a=e;let s={...n,type:a};d($({module:"NewMaterial",filterData:s})),D([p[0]]),console.log("setval",e),d(b([p[0]])),v(!1),mt(e),U(!0),Me(a.desc),we(a.code),ft(a.code),console.log("Valueee",e)},Ct=t=>{if(t.target.value!==null){var e=t.target.value;let a={...n,materialNo:e};d($({module:"NewMaterial",filterData:a}))}et(t.target.value)},[Mt,le]=r.useState(!1);r.useState(!1),r.useEffect(()=>{_(!0)},[n.type]);const wt=()=>{Y(!0)},vt=()=>{Y(!1)},Et=()=>{z("Please Wait");const t=a=>{_e("Create"),a.body.length!=0?z("This Material already Exists. Please Enter Different Material Number"):z("No Duplicate Material found with this Material Number"),Ge("success"),Ze(!1),Ue(!0),wt(),qe(!0),Je(!1),Q(a.body.length==0),_(!1)},e=a=>{console.log(a)};f(`/${x}/alter/fetchMaterialNoDupliChk?materialNoToCheck=${n==null?void 0:n.materialNo}`,"get",t,e)},kt=()=>{le(!0)},Ot=()=>{Et()},Nt=t=>{if(console.log("eeeeeeeee",t.target.value),t.target.value!==null){var e=t.target.value;let a={...n,copyMaterial:e};d($({module:"NewMaterial",filterData:a}))}Be(t.target.value)},Pt=["N"],se=h==null?void 0:h.some(t=>t.External!=="X"),W=h==null?void 0:h.some(t=>t.External==="X"),L=h==null?void 0:h.some(t=>t.ExtNAwock==="X");console.log("isInternalRangeAllowed",se),console.log("isExternalRangeAllowed",W),console.log("isExtWOCheckAllowed",L);const $t=(t=>{const e=new Set;let a=null;t==null||t.forEach(o=>{o.External==="X"&&o.ExtNAwock==="X"?(e.add(`External Number Range: Allowed (${o.FromNumber}-${o.ToNumber})`),e.add("Ext W/O Check: Allowed")):o.External!=="X"&&o.ExtNAwock==="X"?(e.add("Internal Number Range: Allowed"),e.add("Ext W/O Check: Allowed")):o.External==="X"&&o.ExtNAwock!=="X"?(e.add(`External Number Range: Allowed (${o.FromNumber}-${o.ToNumber})`),a="Ext W/O Check: Not Allowed"):o.External!=="X"&&o.ExtNAwock!=="X"&&(e.add("Internal Number Range: Allowed"),a="Ext W/O Check: Not Allowed")});const s=Array.from(e);return a&&s.push(a),s.map((o,O)=>l("div",{children:l(g,{sx:m,children:o})},O))})(h);return l(Ut,{children:c("div",{children:[Ke&&l(zt,{openSnackBar:Qe,alertMsg:He,handleSnackBarClose:vt}),l("div",{style:{backgroundColor:"#FAFCFF"},children:c(Ft,{spacing:1,sx:{padding:"16px"},children:[l(i,{container:!0,children:c(i,{item:!0,md:5,sx:{display:"flex"},children:[l(i,{children:l(Wt,{color:"primary","aria-label":"upload picture",component:"label",sx:Lt,children:l(Zt,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{T("/masterDataCockpit/materialMaster/materialSingle")}})})}),c(i,{children:[l(g,{variant:"h3",children:l("strong",{children:"New Material"})}),l(g,{variant:"body2",color:"#777",children:"This view creates a new material"})]})]})}),c("div",{style:{marginTop:0},children:[c("div",{className:"leftColumn",style:{width:"100%",display:"flex",flexDirection:"column"},children:[l(i,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:1,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",padding:"10px"},children:c(i,{md:12,container:!0,children:[c(i,{md:3,sx:{padding:"5px"},children:[c(N,{id:"demo-simple-select-label",sx:m,children:["Industry Sector",l("span",{style:{color:"red"},children:"*"})]}),l(P,{sx:{height:"31px"},fullWidth:!0,size:"small",value:n==null?void 0:n.sector,onChange:bt,options:oe??[],getOptionLabel:t=>t!=null&&t.code?`${t==null?void 0:t.code} - ${t==null?void 0:t.desc}`??"":"",renderOption:(t,e)=>l("li",{...t,children:l(g,{style:{fontSize:12},children:`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`})}),renderInput:t=>l(C,{...t,variant:"outlined",placeholder:"Select Industry Sector",error:!H})}),!H&&l(g,{variant:"caption",color:"error",children:"Please select an Industry Sector."})]}),c(i,{md:3,sx:{padding:"5px"},children:[c(N,{id:"demo-simple-select-label",sx:m,children:["Material Type",l("span",{style:{color:"red"},children:"*"})]}),l(P,{sx:{height:"31px"},fullWidth:!0,size:"small",value:n==null?void 0:n.type,onChange:Dt,options:ue??[],getOptionLabel:t=>t!=null&&t.code?`${t==null?void 0:t.code} - ${t==null?void 0:t.desc}`??"":"",renderOption:(t,e)=>l("li",{...t,children:l(g,{style:{fontSize:12},children:`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`})}),renderInput:t=>l(C,{...t,variant:"outlined",placeholder:"Select Material Type",error:!K})}),!K&&l(g,{variant:"caption",color:"error",children:"Please select an Material Type."})]}),l(i,{md:3,sx:{padding:"5px"},children:c("div",{children:[l(N,{id:"demo-simple-select-label",sx:m,children:"Material"}),l(ne,{title:$t,arrow:!0,children:c("div",{children:[l(C,{sx:m,size:"small",id:"outlined-basic",variant:"outlined",style:{width:"100%"},value:n==null?void 0:n.materialNo,onChange:Ct,placeholder:"Enter Material No",disabled:!W&&!L,error:!J,inputProps:{maxLength:40}}),!J&&l(g,{variant:"caption",color:"error",children:"Please Enter Material Number"})]})})]})}),c(i,{item:!0,md:3,sx:{padding:"5px"},children:[c(g,{sx:m,children:["Material Description",l("span",{style:{color:"red"},children:"*"})]}),l(C,{size:"small",value:n==null?void 0:n.description,style:{width:"100%"},error:!Z,onClick:kt,placeholder:"Enter Material Description"}),!Z&&l(g,{variant:"caption",color:"error",children:"Please Enter Material Description."})]}),l(i,{item:!0,md:2,sx:{padding:"5px"},style:{display:"flex",flexDirection:"column",justifyContent:"flex-end"}}),c(i,{sx:{width:"100%"},children:[l(Bt,{}),c(i,{sx:{display:"flex",justifyContent:"space-between"},children:[c(i,{md:3,sx:{padding:"5px"},children:[l(N,{id:"demo-simple-select-label",sx:m,children:"Copy From Material"}),l(C,{sx:m,size:"small",id:"outlined-basic",variant:"outlined",style:{width:"100%"},placeholder:"Select Copy from Material",value:n==null?void 0:n.copyMaterial,onChange:Nt})]}),l(ea,{open:Mt,onClose:()=>le(!1)}),c(i,{md:2,sx:{padding:"5px",display:"flex",justifyContent:"flex-end",alignItems:"center"},children:[l(ne,{title:"Duplicate Material Number Check",arrow:!0,children:l(M,{variant:"contained",onClick:Ot,disabled:(n==null?void 0:n.materialNo)===""||!W&&!L,sx:{marginRight:"10px"},children:"Check"})}),l(M,{variant:"outlined",sx:Xt,onClick:ot,children:"Clear"})]})]})]})]})}),l(i,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:1,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",padding:"10px",mb:1},children:c(i,{container:!0,sx:{display:"flex",flexDirection:"column"},children:[l("div",{style:{display:"flex",justifyContent:"space-between",paddingBottom:"5px"},children:l("div",{children:l(g,{sx:m,children:"Select Views"})})}),c(i,{container:!0,spacing:1,style:{display:"flex",paddingBottom:"5px"},children:[l(i,{item:!0,md:6,children:l(P,{size:"small",multiple:!0,disableCloseOnSelect:!0,id:"checkboxes-tags-demo",options:k??[],getOptionLabel:t=>t.label,isOptionEqualToValue:(t,e)=>t.value==e.value,value:F.length?F:u,onChange:(t,e,a)=>{console.log("newvalue",e,t);let s=[...new Set(e)];console.log("dattt1",s),console.log("reason",a),console.log("dattt2",e),console.log("dattt3",u),a==="selectOption"?(D(s),d(b(s))):a==="removeOption"?(j(!1),v(!1),e.length?s.find(o=>o.value=="basicData")?(console.log("test"),D(s),d(b(s))):(D([p[0],...s]),d(b([p[0],...s])),console.log("valueee",u)):(D([p[0]]),d(b([p[0]])))):a==="clear"&&(D([p[0]]),d(b([p[0]])),j(!1),v(!1))},renderOption:(t,e,{selected:a})=>(console.log("option",e),console.log("selected",a),console.log("props",t),c("li",{...t,children:[l(jt,{icon:ta,checkedIcon:aa,style:{marginRight:8},checked:t["data-option-index"]===0?!0:X||a}),l("option",{children:e.label})]})),style:{minWidth:"100%"},renderInput:t=>l(C,{...t,placeholder:"Select Views"})})}),l(i,{item:!0,md:2,sx:{display:"flex",alignItems:"center"},children:je?l(M,{variant:"outlined",disabled:!0,children:"Select all"}):l(M,{variant:"outlined",onClick:dt,onMouseDown:t=>t.preventDefault(),children:"Select all"})}),l(i,{item:!0,md:4,style:{display:"flex",justifyContent:"end",alignItems:"center"},children:l(M,{onClick:ut,variant:"outlined",children:"Proceed"})})]})]})})]}),l("div",{className:"rightColumn",children:l("div",{children:l(qt,{open:ie,onClose:ee,sx:{display:"flex",justifyContent:"center"},children:c(_t,{sx:{width:"600px !important"},children:[c(Ht,{sx:{backgroundColor:"#EAE9FF",marginBottom:".5rem"},children:[l(Yt,{style:{height:"20px",width:"20px",marginBottom:"-5px"}}),l("span",{children:"Select Org Data"})]}),l(Gt,{sx:{paddingBottom:".5rem"},children:l(i,{container:!0,columnSpacing:1,children:w.map((t,e)=>c(r.Fragment,{children:[l(i,{item:!0,md:4,children:c(g,{sx:m,children:[t,l("span",{style:{color:"red"},children:"*"})]})}),l(i,{item:!0,md:8,children:l(P,{options:xt(t)??[],sx:{height:"42px"},size:"small",getOptionLabel:a=>ae(t,a),renderOption:(a,s)=>pt(t,a,s),renderInput:a=>l(C,{...a,placeholder:`Select ${t}`}),onChange:(a,s)=>{St(t,s)},placeholder:"Select from Dropdown"})})]},e))})}),c(Kt,{children:[l(M,{onClick:ee,variant:"outlined",children:"Cancel"}),l(M,{onClick:()=>{it()},disabled:!lt,variant:"contained",children:"Proceed"})]})]})})})})]})]})})]})})};export{Da as default};
