import{r as l,b as Lt,u as Ot,s as zt,q as $,bh as Vt,ab as q,j as f,a as t,V as ee,aF as te,X as ae,aj as $t,x as oe,aC as xe,G as b,F as qt,T as h,bX as jt,aG as et,I as j,B as tt,ar as Jt,E as Ut,W as se,t as E,C as Xt,z as Wt,al as Ht,b1 as Kt,h as Qt,aE as Yt,aB as ne,aD as Zt,cc as xt,cb as ea,K as A,c0 as O,ai as ta,bq as at}from"./index-75c1660a.js";import{d as aa}from"./ArrowCircleLeftOutlined-f7b52d40.js";import{l as ot}from"./lookup-1dcf10ac.js";import{d as oa}from"./AttachFileOutlined-872f8e38.js";import{R as sa}from"./ReusableAttachementAndComments-682b0475.js";import"./CloudUpload-d5d09566.js";import"./utilityImages-067c3dc2.js";import"./Add-62a207fb.js";import"./Delete-1d158507.js";const ha=()=>{var we,ye,Se,Ae;const[J,re]=l.useState(!0),[st,D]=l.useState(!1),[nt,na]=l.useState("1"),[U,rt]=l.useState([]),[it,y]=l.useState(!1),[lt,ie]=l.useState(!1),[ct,R]=l.useState(!1),[ra,k]=l.useState(!1),[dt,X]=l.useState(!1),[ut,le]=l.useState(!1),[ce,N]=l.useState(""),[mt,T]=l.useState(!1),[ia,M]=l.useState(!0),[Ct,de]=l.useState(!1),[B,ft]=l.useState([]),[ue,me]=l.useState(!0),[pt,Ce]=l.useState(!1),[gt,fe]=l.useState(!1),[W,ht]=l.useState(""),[H,pe]=l.useState(""),[ge,G]=l.useState(!1),[Dt,vt]=l.useState([]),K=Lt();Ot();const Pt=zt(),w=$(e=>e.profitCenter.MultipleProfitCenterData),g=$(e=>e.appSettings);let S=$(e=>e.profitCenter.handleMassMode);console.log("massHandleType",S);let m=$(e=>e.userManagement.userData);const[he,bt]=l.useState(0),wt=(e,o)=>{const a=d=>{Pt(ta({keyName:e,data:d.body})),bt(s=>s+1)},i=d=>{console.log(d)};A(`/${O}/data/${o}`,"get",a,i)},yt=()=>{var e,o;(o=(e=ot)==null?void 0:e.profitCenter)==null||o.map(a=>{wt(a==null?void 0:a.keyName,a==null?void 0:a.endPoint)})},St=()=>{var e,o;he==((o=(e=ot)==null?void 0:e.profitCenter)==null?void 0:o.length)?re(!1):re(!0)};l.useEffect(()=>{St()},[he]),l.useEffect(()=>{ht(Vt("PC"))},[]),l.useEffect(()=>{yt()},[]);const At=[{field:"profitCenter",headerName:"Profit Center",editable:!1,flex:1,renderCell:e=>{const o=B.find(a=>a.profitCenter===e.value);return console.log(o,"isDirectMatch"),console.log(e,"params"),o&&o.code===400?t(h,{sx:{fontSize:"12px",color:"red"},children:e.value}):t(h,{sx:{fontSize:"12px"},children:e.value})}},{field:"controllingArea",headerName:"Controlling Area",editable:!1,flex:1},{field:"profitCenterName",headerName:"Profit Center Name",editable:!1,flex:1,renderCell:e=>{const o=Dt.includes(e.row.profitCenterName);return t(h,{sx:{fontSize:"12px",color:o?"red":"inherit"},children:e.value})}},{field:"personResponsible",headerName:"Person Responsible",editable:!1,flex:1},{field:"profitCenterGroup",headerName:"Profit Center Group",editable:!1,flex:1},{field:"analysisPeriodFrom",headerName:"Ananlysis Period From",editable:!1,flex:1,renderCell:e=>t(h,{sx:{fontSize:"12px"},children:q(e.row.analysisPeriodFrom).format(g==null?void 0:g.dateFormat)})},{field:"analysisPeriodTo",headerName:"Analysis Period To",editable:!1,flex:1,renderCell:e=>t(h,{sx:{fontSize:"12px"},children:q(e.row.analysisPeriodTo).format(g==null?void 0:g.dateFormat)})}];console.log("multipleProfitData",w);const Q=()=>{X(!0)},Rt=()=>{fe(!0)},De=()=>{fe(!1)},z=()=>{le(!0)},ve=()=>{le(!1)},kt=()=>{},Nt=()=>{lt?(X(!1),ie(!1)):(X(!1),K("/masterDataCockpit/profitCenter"))},n=(e,o)=>{console.log("getvalueforfieldname",e,o);const a=e==null?void 0:e.find(i=>(i==null?void 0:i.fieldName)===o);return a?a.value:""},Tt=e=>{e.length>0?(G(!0),console.log("selectedIds1",e)):G(!1),console.log("selectedIds",e),rt(e)};let Y=(Se=(ye=(we=w[0])==null?void 0:we.viewData)==null?void 0:ye["Comp Codes"])==null?void 0:Se["Company Code Assignment for Profit Center"];const L=w==null?void 0:w.map((e,o)=>{var d,s,u,v,P,C;const a=e,i=((d=e==null?void 0:e.viewData)==null?void 0:d["Basic Data"])||{};return{id:o,profitCenter:a==null?void 0:a.profitCenter,controllingArea:a==null?void 0:a.controllingArea,profitCenterName:((s=i["General Data"].find(c=>(c==null?void 0:c.fieldName)==="Name"))==null?void 0:s.value)||"",personResponsible:((u=i["General Data"].find(c=>(c==null?void 0:c.fieldName)==="Person Responsible"))==null?void 0:u.value)||"",profitCenterGroup:((v=i["General Data"].find(c=>(c==null?void 0:c.fieldName)==="Profit Ctr Group"))==null?void 0:v.value)||"",analysisPeriodFrom:q((P=i["General Data"].find(c=>(c==null?void 0:c.fieldName)==="Analysis Period From"))==null?void 0:P.value).format(g==null?void 0:g.dateFormat)||"",analysisPeriodTo:q((C=i["General Data"].find(c=>(c==null?void 0:c.fieldName)==="Analysis Period To"))==null?void 0:C.value).format(g==null?void 0:g.dateFormat)||""}});_.zip(Y[0].value,Y[1].value,Y[2].value).map(e=>({CompCodeID:0,CompCode:e[0],CompanyName:e[1],AssignToPrctr:e[2]==!0?"X":"",Venture:"",RecInd:"",EquityTyp:"",JvOtype:"",JvJibcl:"",JvJibsa:""}));var F=w.map(e=>{var o,a,i,d,s,u,v,P,C,c,r,p,I,Re,ke,Ne,Te,Me,Fe,Ie,Ee,Be,Ge,_e,Le,Oe,ze,Ve,$e,qe,je,Je,Ue,Xe,We,He,Ke,Qe,Ye,Ze;return console.log("samsung",e),{ProfitCenterID:"",Action:S==="Create"?"I":"U",RequestID:"",TaskStatus:"",TaskId:"",remarks:H||"",ReqCreatedBy:m==null?void 0:m.user_id,ReqCreatedOn:m!=null&&m.createdOn?"/Date("+(m==null?void 0:m.createdOn)+")/":"",RequestStatus:"",CreationId:"",EditId:"",DeleteId:"",MassCreationId:"",MassEditId:"",MassDeleteId:"",RequestType:S==="Create"?"Mass Create":"Mass Change",MassRequestStatus:"",PrctrName:n((o=e==null?void 0:e.viewData["Basic Data"])==null?void 0:o["General Data"],"Name"),LongText:n((a=e==null?void 0:e.viewData["Basic Data"])==null?void 0:a["General Data"],"Long Text"),InChargeUser:n((i=e==null?void 0:e.viewData["Basic Data"])==null?void 0:i["General Data"],"User Responsible"),InCharge:n((d=e==null?void 0:e.viewData["Basic Data"])==null?void 0:d["General Data"],"Person Responsible"),Department:n((s=e==null?void 0:e.viewData["Basic Data"])==null?void 0:s["General Data"],"Department"),PrctrHierGrp:n((u=e==null?void 0:e.viewData["Basic Data"])==null?void 0:u["General Data"],"Profit Ctr Group"),Segment:n((v=e==null?void 0:e.viewData["Basic Data"])==null?void 0:v["General Data"],"Segment"),LockInd:n((P=e==null?void 0:e.viewData.Indicators)==null?void 0:P.Indicator,"Lock indicator")===!0?"X":"",Template:n((C=e==null?void 0:e.viewData.Indicators)==null?void 0:C["Formula Planning"],"Form. Planning Temp"),Title:n((c=e==null?void 0:e.viewData.Address)==null?void 0:c["Address Data"],"Title"),Name1:n((r=e==null?void 0:e.viewData.Address)==null?void 0:r["Address Data"],"Name 1"),Name2:n((p=e==null?void 0:e.viewData.Address)==null?void 0:p["Address Data"],"Name 2"),Name3:n((I=e==null?void 0:e.viewData.Address)==null?void 0:I["Address Data"],"Name 3"),Name4:n((Re=e==null?void 0:e.viewData.Address)==null?void 0:Re["Address Data"],"Name 4"),Street:n((ke=e==null?void 0:e.viewData.Address)==null?void 0:ke["Address Data"],"Street"),City:n((Ne=e==null?void 0:e.viewData.Address)==null?void 0:Ne["Address Data"],"City"),District:n((Te=e==null?void 0:e.viewData.Address)==null?void 0:Te["Address Data"],"District"),Country:n((Me=e==null?void 0:e.viewData.Address)==null?void 0:Me["Address Data"],"Country/Reg."),Taxjurcode:n((Fe=e==null?void 0:e.viewData.Address)==null?void 0:Fe["Address Data"],"Tax Jur."),PoBox:n((Ie=e==null?void 0:e.viewData.Address)==null?void 0:Ie["Address Data"],"P.O.Box"),PostlCode:n((Ee=e==null?void 0:e.viewData.Address)==null?void 0:Ee["Address Data"],"Postal Code"),PobxPcd:n((Be=e==null?void 0:e.viewData.Address)==null?void 0:Be["Address Data"],"PO Box PCode"),Region:n((Ge=e==null?void 0:e.viewData.Address)==null?void 0:Ge["Address Data"],"Region"),Langu:n((_e=e==null?void 0:e.viewData.Communication)==null?void 0:_e["Communication Data"],"Language"),Telephone:n((Le=e==null?void 0:e.viewData.Communication)==null?void 0:Le["Communication Data"],"Telephone 1"),Telephone2:n((Oe=e==null?void 0:e.viewData.Communication)==null?void 0:Oe["Communication Data"],"Telephone 2"),Telebox:n((ze=e==null?void 0:e.viewData.Communication)==null?void 0:ze["Communication Data"],"Telebox"),Telex:n((Ve=e==null?void 0:e.viewData.Communication)==null?void 0:Ve["Communication Data"],"Telex"),FaxNumber:n(($e=e==null?void 0:e.viewData.Communication)==null?void 0:$e["Communication Data"],"Fax Number"),Teletex:n((qe=e==null?void 0:e.viewData.Communication)==null?void 0:qe["Communication Data"],"Teletex"),Printer:n((je=e==null?void 0:e.viewData.Communication)==null?void 0:je["Communication Data"],"Printer name"),DataLine:n((Je=e==null?void 0:e.viewData.Communication)==null?void 0:Je["Communication Data"],"Data line"),ProfitCenter:e==null?void 0:e.profitCenter,ControllingArea:e==null?void 0:e.controllingArea,ValidfromDate:n((Ue=e==null?void 0:e.viewData["Basic Data"])==null?void 0:Ue["General Data"],"Analysis Period From"),ValidtoDate:n((Xe=e==null?void 0:e.viewData["Basic Data"])==null?void 0:Xe["General Data"],"Analysis Period To"),Testrun:ge,Countryiso:"",LanguIso:"",Logsystem:"",ToCompanycode:_.zip((He=(We=e==null?void 0:e.viewData)==null?void 0:We["Comp Codes"])==null?void 0:He["Company Code Assignment for Profit Center"][0].value,(Qe=(Ke=e==null?void 0:e.viewData)==null?void 0:Ke["Comp Codes"])==null?void 0:Qe["Company Code Assignment for Profit Center"][1].value,(Ze=(Ye=e==null?void 0:e.viewData)==null?void 0:Ye["Comp Codes"])==null?void 0:Ze["Company Code Assignment for Profit Center"][2].value).map(x=>({CompCodeID:"",CompCode:x[0],CompanyName:x[1],AssignToPrctr:(x[2]===!0,"X"),Venture:"",RecInd:"",EquityTyp:"",JvOtype:"",JvJibcl:"",JvJibsa:""}))}});const Mt=(e,o)=>e.every(a=>!o.includes(a)),Ft=()=>{const e=L.filter((s,u)=>U.includes(u));console.log("selectedData",e);const o=e.map(s=>({...F[s==null?void 0:s.id]}));let a=F;a=o,console.log("selectedProfitCenterRows",o);const i=s=>{if(s.statusCode===200){console.log("success"),y("Create"),N(`Mass Profit Center Sent for Review with ID NPM${s.body}`),T("success"),M(!1),R(!0),Q(),k(!0),D(!1);const u={artifactId:W,createdBy:m==null?void 0:m.emailId,artifactType:"ProfitCenter",requestId:`NPM${s==null?void 0:s.body}`},v=C=>{console.log("Second API success",C)},P=C=>{console.error("Second API error",C)};A(`/${at}/documentManagement/updateDocRequestId`,"post",v,P,u)}else y("Error"),R(!1),N("Failed Submitting the Profit Center for Review "),T("danger"),M(!1),k(!0),z(),D(!1);handleClose(),D(!1)},d=s=>{console.log(s)};A(`/${O}/massAction/profitCentersSubmitForReview`,"post",i,d,a)},It=()=>{D(!0);const e=L.filter((r,p)=>U.includes(p));console.log("selectedData",e);const o=e.map(r=>({...F[r==null?void 0:r.id]}));console.log("selectedProfitCenterRows",o);const a=[],i=[];o.map(r=>{var p={coArea:r==null?void 0:r.ControllingArea,name:r==null?void 0:r.PrctrName.toUpperCase()};a.push(p),i.push(r==null?void 0:r.PrctrName.toUpperCase())});const d=[];w.map(r=>{var p;d.push((p=r.profitCenterName)==null?void 0:p.toUpperCase())});let s=Mt(d,i);console.log(s,"isuserEditPCName"),console.log("duplicateCheckPayload",a.coArea),console.log(i,"duplicatePCName");let u=F;u=o;const v=r=>{r.statusCode===400?(ft(r.body),de(!0),D(!1)):(y("Create"),console.log("success"),y("Create"),N("All Data has been Validated. Profit Center can be Send for Review"),T("success"),M(!1),R(!0),Q(),k(!0),ie(!0),(a.coArea!==""||a.name!=="")&&A(`/${O}/alter/fetchPCDescriptionsDupliChk`,"post",P,C,a))},P=r=>{if(console.log("dataaaa",r),r.body.length===0||!r.body.some(p=>a.some(I=>p.matches.includes(I.name.toUpperCase()))))alert(1),D(!1),me(!1),G(!0);else{alert(2);const p=r.body.map(I=>I.matches[0]);D(!1),y("Duplicate Check"),R(!1),N("There is a direct match for the Profit Center name."),T("danger"),M(!1),k(!0),z(),me(!0),vt(p)}},C=r=>{console.log(r)},c=r=>{console.log(r)};A(`/${O}/massAction/validateMassProfitCenter`,"post",v,c,u)},Et=()=>{const e=L.filter((s,u)=>U.includes(u));console.log("selectedData",e);const o=e.map(s=>({...F[s==null?void 0:s.id]}));let a=F;a=o,console.log("selectedProfitCenterRows",o);const i=s=>{if(s.statusCode===200){console.log("success"),y("Create"),N(`Mass Profit Center Sent for Review with ID CPM${s.body}`),T("success"),M(!1),R(!0),Q(),k(!0),D(!1);const u={artifactId:W,createdBy:m==null?void 0:m.emailId,artifactType:"ProfitCenter",requestId:`CPM${s==null?void 0:s.body}`},v=C=>{console.log("Second API success",C)},P=C=>{console.error("Second API error",C)};A(`/${at}/documentManagement/updateDocRequestId`,"post",v,P,u)}else y("Error"),R(!1),N("Failed Submitting the Profit Center for Review "),T("danger"),M(!1),k(!0),z(),D(!1);handleClose(),D(!1)},d=s=>{console.log(s)};A(`/${O}/massAction/changeProfitCentersSubmitForReview`,"post",i,d,a)},Pe=()=>{de(!1)},Bt=()=>{D(!0),V(),S==="Create"?Ft():Et()},Gt=(e,o)=>{const a=e.target.value;if(a.length>0&&a[0]===" ")pe(a.trimStart());else{let i=a.toUpperCase();pe(i)}},_t=[{field:"profitCenter",headerName:"Profit Center",editable:!1,flex:1},{field:"error",headerName:"Error",editable:!1,flex:1}];console.log("profitValidationError",B);const Z=(Ae=B==null?void 0:B.filter(e=>(e==null?void 0:e.code)===400))==null?void 0:Ae.map((e,o)=>{var a;if(e.code===400)return{id:o,profitCenter:e==null?void 0:e.profitCenter,error:(a=e==null?void 0:e.status)==null?void 0:a.message}});console.log("validationRows",Z);const V=()=>{G(!0),Ce(!1)},be=()=>{G(!1),Ce(!0)};return f(qt,{children:[J===!0?t(jt,{}):f("div",{children:[f(ee,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:pt,onClose:V,children:[f(te,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(h,{variant:"h6",children:"Remarks"}),t(j,{sx:{width:"max-content"},onClick:V,children:t(et,{})})]}),t(se,{sx:{padding:".5rem 1rem"},children:t(oe,{children:t(tt,{sx:{minWidth:400},children:t(Jt,{sx:{height:"auto"},fullWidth:!0,children:t(Ut,{sx:{backgroundColor:"#F5F5F5"},value:H,onChange:Gt,multiline:!0,placeholder:"Enter Remarks",inputProps:{maxLength:254}})})})})}),f(ae,{sx:{display:"flex",justifyContent:"end"},children:[t(E,{sx:{width:"max-content",textTransform:"capitalize"},onClick:V,children:"Cancel"}),t(E,{className:"button_primary--normal",type:"save",onClick:Bt,variant:"contained",children:"Submit"})]})]}),t(Xt,{dialogState:ut,openReusableDialog:z,closeReusableDialog:ve,dialogTitle:it,dialogMessage:ce,handleDialogConfirm:ve,dialogOkText:"OK",handleExtraButton:kt,dialogSeverity:mt}),ct&&t(Wt,{openSnackBar:dt,alertMsg:ce,handleSnackBarClose:Nt}),f("div",{style:{...$t,backgroundColor:"#FAFCFF"},children:[t(b,{container:!0,sx:Ht,children:f(b,{item:!0,md:12,sx:{display:"flex",marginBottom:"0"},children:[f(b,{item:!0,md:11,sx:{display:"flex"},children:[t(b,{children:t(j,{color:"primary","aria-label":"upload picture",component:"label",sx:Kt,children:t(aa,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{K("/masterDataCockpit/profitCenter")}})})}),t(b,{children:S==="Create"?f(b,{item:!0,md:12,children:[t(h,{variant:"h3",children:t("strong",{children:"Create Multiple Profit Centers"})}),t(h,{variant:"body2",color:"#777",children:"This view creates multiple Profit Centers"})]}):f(b,{item:!0,md:12,children:[t(h,{variant:"h3",children:t("strong",{children:"Change Multiple Profit Centers"})}),t(h,{variant:"body2",color:"#777",children:"This view changes multiple Profit Centers"})]})})]}),t(b,{item:!0,md:1,sx:{display:"flex"},children:t(Qt,{title:"Upload documents if any",arrow:!0,children:t(j,{onClick:Rt,children:t(oa,{})})})}),f(ee,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:gt,onClose:De,children:[t(te,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:t(h,{variant:"h6",children:"Add Attachment"})}),t(se,{sx:{padding:".5rem 1rem"},children:t(oe,{children:t(tt,{sx:{minWidth:400},children:t(sa,{title:"ProfitCenter",useMetaData:!1,artifactId:W,artifactName:"ProfitCenter"})})})}),t(ae,{children:t(E,{onClick:De,children:"Close"})})]})]})}),t(b,{item:!0,sx:{position:"relative"},children:t(oe,{children:t(xe,{isLoading:J,width:"100%",title:"Profit Center Master List ("+L.length+")",rows:L,columns:At,pageSize:10,getRowIdValue:"id",hideFooter:!1,checkboxSelection:!0,disableSelectionOnClick:!0,status_onRowSingleClick:!0,onRowsSelectionHandler:Tt,callback_onRowSingleClick:e=>{console.log("paramss",e);const o=e.row.profitCenter,a=w.find(i=>i.profitCenter===o);K(`/masterDataCockpit/profitCenter/createMultipleProfitCenter/editMultipleProfitCenter/${o}`,{state:{tabsData:a,rowData:e.row}})},stopPropagation_Column:"action",status_onRowDoubleClick:!0})})})]}),t(Zt,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:f(Yt,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},value:nt,children:[t(E,{variant:"contained",size:"small",sx:{...ne,mr:1},onClick:It,disabled:!ge,children:"Validate"}),S==="Create"?t(E,{variant:"contained",size:"small",sx:{...ne},onClick:be,disabled:ue,children:"Submit for Review"}):S==="Change"?t(E,{variant:"contained",size:"small",sx:{...ne},onClick:be,disabled:ue,children:"Submit for Review"}):""]})}),f(ee,{open:Ct,fullWidth:!0,onClose:Pe,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[f(te,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(h,{variant:"h6",color:"red",children:"Errors"}),t(j,{sx:{width:"max-content"},onClick:Pe,children:t(et,{})})]}),t(se,{sx:{padding:".5rem 1rem"},children:Z&&t(xe,{isLoading:J,width:"100%",rows:Z,columns:_t,pageSize:10,getRowIdValue:"id",hideFooter:!0,checkboxSelection:!1,disableSelectionOnClick:!0,status_onRowSingleClick:!0,stopPropagation_Column:"action",status_onRowDoubleClick:!0})}),t(ae,{sx:{display:"flex",justifyContent:"end"}})]})]}),t(ea,{sx:{color:"#fff",zIndex:e=>e.zIndex.drawer+1},open:st,children:t(xt,{color:"inherit"})})]})};export{ha as default};
