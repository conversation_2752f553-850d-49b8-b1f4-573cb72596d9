import{r as d,b as hs,s as gs,u as ms,q as Z,bh as fs,ab as H,a as t,bX as vs,j as p,V as se,aF as te,T as g,I as x,aG as ve,W as ne,x as re,B as ae,ar as fo,E as vo,X as le,t as T,aC as be,cb as bs,cc as ys,z as Ms,C as Ss,aj as As,G as B,al as Ts,b1 as Fs,h as Ds,F as j,bs as bo,bt as Is,bm as Ns,aD as Ps,aE as ws,b8 as ie,aB as G,K as F,bq as ye,c0 as N,cv as qs,ai as Es}from"./index-17b8d91e.js";import{d as ks}from"./ArrowCircleLeftOutlined-2a09f8e2.js";import{l as yo}from"./lookup-1dcf10ac.js";import{M as Bs,a as $s}from"./UtilDoc-d76e2af6.js";import{d as Ls}from"./AttachFileOutlined-e3968291.js";import{R as zs}from"./ReusableAttachementAndComments-bab6bbfc.js";import{T as Os}from"./Timeline-bd0ec33e.js";import{t as _s,T as js,a as Gs,b as Vs,c as Us,d as Ws}from"./TimelineSeparator-0839d5e3.js";/* empty css            */import"./FileDownloadOutlined-c800f30b.js";import"./VisibilityOutlined-315d5644.js";import"./DeleteOutlined-888bfc33.js";import"./Slider-3eb7e770.js";import"./CloudUpload-27b6d63e.js";import"./utilityImages-067c3dc2.js";import"./Add-98854918.js";import"./Delete-9f4d7a45.js";import"./clsx-a965ebfb.js";const ht=()=>{var je;const[$,O]=d.useState(!0),[Mo,Js]=d.useState("1"),[J,So]=d.useState([]);d.useState([]);const ce=hs(),Me=gs(),s=ms().state,[Se,m]=d.useState(""),[Ao,de]=d.useState(!1),[Xs,f]=d.useState(!1),[To,C]=d.useState(!1),[Fo,v]=d.useState(!1),[Ks,b]=d.useState(!0),[Do,y]=d.useState(!1),[Io,Ae]=d.useState(!1),[No,Te]=d.useState(!1),[Po,Fe]=d.useState(!1),[K,De]=d.useState(""),[ue,D]=d.useState(!1),[Ie,Ne]=d.useState(!0),[Pe,we]=d.useState(!0),[Ys,qe]=d.useState(!0),[wo,Ee]=d.useState(!1),[pe,qo]=d.useState([]),[Ce,Eo]=d.useState([]),[ko,he]=d.useState(!1),[Bo,A]=d.useState(!1),[ge,$o]=d.useState(""),[Lo,me]=d.useState(!1),[Q,ke]=d.useState([]),[zo,Oo]=d.useState([]);console.log("mouse",J);let r=Z(e=>e.userManagement.taskData),o=Z(e=>e.userManagement.userData),_o=Z(e=>{var l;return(l=e.userManagement.entitiesAndActivities)==null?void 0:l["Profit Center"]});const P=Z(e=>e.appSettings),V=Z(e=>e.profitCenter.MultipleProfitCenterData),E=()=>{de(!0)},jo=()=>{ko?(de(!1),he(!1)):(de(!1),ce("/masterDataCockpit/profitCenter"))},k=()=>{Ae(!0)},Be=()=>{Ae(!1)},Go=()=>{},Vo=()=>{Ee(!0)},$e=()=>{Ee(!1)},Uo=[{field:"id",headerName:"Document ID",flex:1,hide:!0},{field:"docType",headerName:"Type",flex:1},{field:"docName",headerName:"Document Name",flex:1},{field:"uploadedOn",headerName:"Uploaded On",flex:1,align:"center",headerAlign:"center"},{field:"uploadedBy",headerName:"Uploaded By",sortable:!1,flex:1},{field:"action",headerName:"Action",sortable:!1,filterable:!1,align:"center",headerAlign:"center",flex:1,renderCell:e=>p(j,{children:[t(Bs,{index:e.row.id,name:e.row.docName}),t($s,{index:e.row.id,name:e.row.docName})]})}],Wo=()=>{let e=r!=null&&r.subject?r==null?void 0:r.subject:s==null?void 0:s.requestId,l=a=>{var n=[];a.documentDetailDtoList.forEach(i=>{var M={id:i.documentId,docType:i.fileType,docName:i.fileName,uploadedOn:H(i.docCreationDate).format(P.date),uploadedBy:i.createdBy};n.push(M)}),qo(n)};F(`/${ye}/documentManagement/getDocByRequestId/${e}`,"get",l)},Jo=()=>{let e=r!=null&&r.subject?r==null?void 0:r.subject:s==null?void 0:s.requestId,l=n=>{console.log("commentsdata",n);var i=[];n.body.forEach(M=>{var I={id:M.requestId,comment:M.comment,user:M.createdByUser,createdAt:M.updatedAt};i.push(I)}),Eo(i),console.log("commentrows",i)},a=n=>{console.log(n)};F(`/${N}/activitylog/fetchTaskDetailsForRequestId?requestId=${e}`,"get",l,a)};d.useEffect(()=>{Wo(),Jo(),$o(fs("PC"))},[]);const c=(e,l)=>{const a=e==null?void 0:e.find(n=>(n==null?void 0:n.fieldName)===l);return a?a.value:""},Ro=()=>{var n,i,M,I;console.log("sdfkhdgkf"),O(!0);let e={};(r==null?void 0:r.processDesc)==="Mass Change"?e={massCreationId:"",massChangeId:r!=null&&r.subject?(n=r==null?void 0:r.subject)==null?void 0:n.slice(3):s==null?void 0:s.requestId.slice(3),screenName:"Change"}:(r==null?void 0:r.processDesc)==="Mass Create"?e={massCreationId:r!=null&&r.subject?(i=r==null?void 0:r.subject)==null?void 0:i.slice(3):s==null?void 0:s.requestId.slice(3),massChangeId:"",screenName:"Create"}:(s==null?void 0:s.requestType)==="Mass Create"?e={massCreationId:(M=s==null?void 0:s.requestId)==null?void 0:M.slice(3),massChangeId:"",screenName:"Create"}:(s==null?void 0:s.requestType)==="Mass Change"&&(e={massCreationId:"",massChangeId:(I=s==null?void 0:s.requestId)==null?void 0:I.slice(3),screenName:"Change"});const l=S=>{O(!1),S.body&&Me(qs(S==null?void 0:S.body))},a=S=>{console.log(S)};F(`/${N}/data/displayMassProfitCenter`,"post",l,a,e)},[fe,Xo]=d.useState(0),Ko=(e,l)=>{const a=i=>{Me(Es({keyName:e,data:i.body})),Xo(M=>M+1)},n=i=>{console.log(i)};F(`/${N}/data/${l}`,"get",a,n)},Yo=()=>{var e,l;console.log("Calleddddd"),(l=(e=yo)==null?void 0:e.profitCenter)==null||l.map(a=>{Ko(a==null?void 0:a.keyName,a==null?void 0:a.endPoint)})},Ho=()=>{var e,l;console.log("apiCount",fe),fe==((l=(e=yo)==null?void 0:e.profitCenter)==null?void 0:l.length)?O(!1):O(!0)};d.useEffect(()=>{Ho()},[fe]),d.useEffect(()=>{if(V.length===0)Ro();else return},[]),d.useEffect(()=>{Yo()},[]);const Qo=e=>{e.length>0?(D(!0),console.log("selectedIds1",e)):D(!1),console.log("selectedIds",e),So(e)},L=V==null?void 0:V.map((e,l)=>{var i,M,I,S,R,X;const a=e,n=((i=e==null?void 0:e.viewData)==null?void 0:i["Basic Data"])||{};return{id:l,profitCenter:a==null?void 0:a.profitCenter,controllingArea:a==null?void 0:a.controllingArea,profitCenterName:((M=n["General Data"].find(h=>(h==null?void 0:h.fieldName)==="Name"))==null?void 0:M.value)||"",personResponsible:((I=n["General Data"].find(h=>(h==null?void 0:h.fieldName)==="Person Responsible"))==null?void 0:I.value)||"",profitCenterGroup:((S=n["General Data"].find(h=>(h==null?void 0:h.fieldName)==="Profit Ctr Group"))==null?void 0:S.value)||"",analysisPeriodFrom:H((R=n["General Data"].find(h=>(h==null?void 0:h.fieldName)==="Analysis Period From"))==null?void 0:R.value).format(P==null?void 0:P.dateFormat)||"",analysisPeriodTo:H((X=n["General Data"].find(h=>(h==null?void 0:h.fieldName)==="Analysis Period To"))==null?void 0:X.value).format(P==null?void 0:P.dateFormat)||""}}),Zo=[{field:"profitCenter",headerName:"Profit Center",editable:!1,flex:1,renderCell:e=>{const l=Q.find(a=>a.profitCenter===e.value);return console.log(l,"isDirectMatch"),console.log(e,"params"),l&&l.code===400?t(g,{sx:{fontSize:"12px",color:"red"},children:e.value}):t(g,{sx:{fontSize:"12px"},children:e.value})}},{field:"controllingArea",headerName:"Controlling Area",editable:!1,flex:1},{field:"profitCenterName",headerName:"Profit Center Name",editable:!1,flex:1,renderCell:e=>{const l=zo.includes(e.row.profitCenterName);return t(g,{sx:{fontSize:"12px",color:l?"red":"inherit"},children:e.value})}},{field:"personResponsible",headerName:"Person Responsible",editable:!1,flex:1},{field:"profitCenterGroup",headerName:"Profit Center Group",editable:!1,flex:1},{field:"analysisPeriodFrom",headerName:"Ananlysis Period From",editable:!1,flex:1,renderCell:e=>t(g,{sx:{fontSize:"12px"},children:H(e.row.analysisPeriodFrom).format(P==null?void 0:P.dateFormat)})},{field:"analysisPeriodTo",headerName:"Analysis Period To",editable:!1,flex:1,renderCell:e=>t(g,{sx:{fontSize:"12px"},children:H(e.row.analysisPeriodTo).format(P==null?void 0:P.dateFormat)})}];var w=V==null?void 0:V.map(e=>{var l,a,n,i,M,I,S,R,X,h,u,q,W,Ge,Ve,Ue,We,Je,Re,Xe,Ke,Ye,He,Qe,Ze,xe,eo,oo,so,to,no,ro,ao,lo,io,co,uo,po,Co,ho,go,mo;return console.log("samsung",e),{ProfitCenterID:e==null?void 0:e.profitCenterId,Action:(r==null?void 0:r.processDesc)==="Mass Create"?"I":(r==null?void 0:r.processDesc)==="Mass Change"||(s==null?void 0:s.requestType)==="Mass Change"?"U":(s==null?void 0:s.requestType)==="Mass Create"?"I":"",RequestID:"",TaskStatus:"",TaskId:r!=null&&r.taskId?r==null?void 0:r.taskId:"",ReqCreatedBy:o==null?void 0:o.user_id,ReqCreatedOn:r!=null&&r.createdOn?"/Date("+(r==null?void 0:r.createdOn)+")/":"",RequestStatus:"",Remarks:K||"",CreationId:"",EditId:"",DeleteId:"",MassCreationId:(r==null?void 0:r.processDesc)==="Mass Create"?(l=r==null?void 0:r.subject)==null?void 0:l.slice(3):(s==null?void 0:s.requestType)==="Mass Create"?s==null?void 0:s.requestId.slice(3):"",MassEditId:(r==null?void 0:r.processDesc)==="Mass Change"?(a=r==null?void 0:r.subject)==null?void 0:a.slice(3):(s==null?void 0:s.requestType)==="Mass Change"?s==null?void 0:s.requestId.slice(3):"",MassDeleteId:"",RequestType:(r==null?void 0:r.processDesc)==="Mass Create"?"Mass Create":(r==null?void 0:r.processDesc)==="Mass Change"||(s==null?void 0:s.requestType)==="Mass Change"?"Mass Change":(s==null?void 0:s.requestType)==="Mass Create"?"Mass Create":"",MassRequestStatus:"",PrctrName:c((n=e==null?void 0:e.viewData["Basic Data"])==null?void 0:n["General Data"],"Name"),LongText:c((i=e==null?void 0:e.viewData["Basic Data"])==null?void 0:i["General Data"],"Long Text"),InChargeUser:c((M=e==null?void 0:e.viewData["Basic Data"])==null?void 0:M["General Data"],"User Responsible"),InCharge:c((I=e==null?void 0:e.viewData["Basic Data"])==null?void 0:I["General Data"],"Person Responsible"),Department:c((S=e==null?void 0:e.viewData["Basic Data"])==null?void 0:S["General Data"],"Department"),PrctrHierGrp:c((R=e==null?void 0:e.viewData["Basic Data"])==null?void 0:R["General Data"],"Profit Ctr Group"),Segment:c((X=e==null?void 0:e.viewData["Basic Data"])==null?void 0:X["General Data"],"Segment"),LockInd:c((h=e==null?void 0:e.viewData.Indicators)==null?void 0:h.Indicator,"Lock indicator")===!0?"X":"",Template:c((u=e==null?void 0:e.viewData.Indicators)==null?void 0:u["Formula Planning"],"Form. Planning Temp"),Title:c((q=e==null?void 0:e.viewData.Address)==null?void 0:q["Address Data"],"Title"),Name1:c((W=e==null?void 0:e.viewData.Address)==null?void 0:W["Address Data"],"Name 1"),Name2:c((Ge=e==null?void 0:e.viewData.Address)==null?void 0:Ge["Address Data"],"Name 2"),Name3:c((Ve=e==null?void 0:e.viewData.Address)==null?void 0:Ve["Address Data"],"Name 3"),Name4:c((Ue=e==null?void 0:e.viewData.Address)==null?void 0:Ue["Address Data"],"Name 4"),Street:c((We=e==null?void 0:e.viewData.Address)==null?void 0:We["Address Data"],"Street"),City:c((Je=e==null?void 0:e.viewData.Address)==null?void 0:Je["Address Data"],"City"),District:c((Re=e==null?void 0:e.viewData.Address)==null?void 0:Re["Address Data"],"District"),Country:c((Xe=e==null?void 0:e.viewData.Address)==null?void 0:Xe["Address Data"],"Country/Reg."),Taxjurcode:c((Ke=e==null?void 0:e.viewData.Address)==null?void 0:Ke["Address Data"],"Tax Jur."),PoBox:c((Ye=e==null?void 0:e.viewData.Address)==null?void 0:Ye["Address Data"],"P.O.Box"),PostlCode:c((He=e==null?void 0:e.viewData.Address)==null?void 0:He["Address Data"],"Postal Code"),PobxPcd:c((Qe=e==null?void 0:e.viewData.Address)==null?void 0:Qe["Address Data"],"PO Box PCode"),Region:c((Ze=e==null?void 0:e.viewData.Address)==null?void 0:Ze["Address Data"],"Region"),Langu:c((xe=e==null?void 0:e.viewData.Communication)==null?void 0:xe["Communication Data"],"Language"),Telephone:c((eo=e==null?void 0:e.viewData.Communication)==null?void 0:eo["Communication Data"],"Telephone 1"),Telephone2:c((oo=e==null?void 0:e.viewData.Communication)==null?void 0:oo["Communication Data"],"Telephone 2"),Telebox:c((so=e==null?void 0:e.viewData.Communication)==null?void 0:so["Communication Data"],"Telebox"),Telex:c((to=e==null?void 0:e.viewData.Communication)==null?void 0:to["Communication Data"],"Telex"),FaxNumber:c((no=e==null?void 0:e.viewData.Communication)==null?void 0:no["Communication Data"],"Fax Number"),Teletex:c((ro=e==null?void 0:e.viewData.Communication)==null?void 0:ro["Communication Data"],"Teletex"),Printer:c((ao=e==null?void 0:e.viewData.Communication)==null?void 0:ao["Communication Data"],"Printer name"),DataLine:c((lo=e==null?void 0:e.viewData.Communication)==null?void 0:lo["Communication Data"],"Data line"),ProfitCenter:e==null?void 0:e.profitCenter,ControllingArea:e==null?void 0:e.controllingArea,ValidfromDate:c((io=e==null?void 0:e.viewData["Basic Data"])==null?void 0:io["General Data"],"Analysis Period From"),ValidtoDate:c((co=e==null?void 0:e.viewData["Basic Data"])==null?void 0:co["General Data"],"Analysis Period To"),Testrun:ue,Countryiso:"",LanguIso:"",Logsystem:"",ToCompanycode:_.zip((po=(uo=e==null?void 0:e.viewData)==null?void 0:uo["Comp Codes"])==null?void 0:po["Company Code Assignment for Profit Center"][0].value,(ho=(Co=e==null?void 0:e.viewData)==null?void 0:Co["Comp Codes"])==null?void 0:ho["Company Code Assignment for Profit Center"][1].value,(mo=(go=e==null?void 0:e.viewData)==null?void 0:go["Comp Codes"])==null?void 0:mo["Company Code Assignment for Profit Center"][2].value).map(oe=>({CompCodeID:oe[0].split("$$$")[1],CompCode:oe[0].split("$$$")[0],CompanyName:oe[1],AssignToPrctr:(oe[2]===!0,"X"),Venture:"",RecInd:"",EquityTyp:"",JvOtype:"",JvJibcl:"",JvJibsa:""}))}});console.log("massProfitRowData",s);const xo=()=>{const e=w;console.log("isLoading1",$),console.log("paylaod",e);const l=n=>{A(!1),n.statusCode===200?(console.log("success"),C("Create"),m(`Mass Profit Center Submitted for Approval with ID NPM${n.body}`),v("success"),b(!1),y(!0),E(),f(!0),D(!0)):(C("Error"),y(!1),m("Failed Submitting the Mass Profit Center for Approval  "),v("danger"),b(!1),f(!0),k(),D(!0)),handleClose()},a=n=>{console.log(n)};F(`/${N}/massAction/profitCentersApprovalSubmit`,"post",l,a,e)},es=()=>{L.filter((n,i)=>J.includes(i));const e=w;console.log("paylaod",e),console.log("isLoading2",$);const l=n=>{A(!1),D(!0),n.statusCode===200?(console.log("success"),C("Create"),m(`Mass Profit Center Change Submitted for Approval with ID CPM${n.body}`),v("success"),b(!1),y(!0),E(),f(!0)):(C("Error"),y(!1),m("Failed Submitting Mass Profit Center Data for Approval"),v("danger"),b(!1),f(!0),k())},a=n=>{console.log("error")};F(`/${N}/massAction/changeProfitCentersApprovalSubmit`,"post",l,a,e)},os=()=>{L.filter((n,i)=>J.includes(i));const e=w;console.log("paylaod",e),console.log("isLoading3",$);const l=n=>{A(!1),n.statusCode===201?(console.log("success"),C("Create"),m("Mass Profit Center Approved & SAP Syndication Completed"),v("success"),b(!1),y(!0),E(),f(!0),D(!0)):(C("Error"),y(!1),m("Failed Submitting the Profit Center for Approval "),v("danger"),b(!1),f(!0),k(),D(!0)),handleClose()},a=n=>{console.log(n)};F(`/${N}/massAction/createProfitCentersApproved`,"post",l,a,e)},ss=()=>{const e=w;console.log("paylaod",e),console.log("isLoading4",$);const l=n=>{A(!1),D(!0),n.statusCode===201?(console.log("success"),C("Create"),m("Mass Profit Center Change Approved & SAP Syndication Completed"),v("success"),b(!1),y(!0),E(),f(!0)):(C("Error"),y(!1),m("Failed Approving Mass Profit Center Change"),v("danger"),b(!1),f(!0),k())},a=n=>{console.log("error")};F(`/${N}/massAction/changeProfitCentersApproved`,"post",l,a,e)},ts=()=>{const e=w;console.log("paylaod",e),console.log("isLoading5",$);const l=n=>{if(A(!1),D(!0),n.statusCode===200){console.log("success"),C("Create"),m(`Mass Profit Center Submitted for Review with ID NPM${n.body}`),v("success"),b(!1),y(!0),E(),f(!0);const i={artifactId:ge,createdBy:o==null?void 0:o.emailId,artifactType:"ProfitCenter",requestId:`NPM${n==null?void 0:n.body}`},M=S=>{console.log("Second API success",S)},I=S=>{console.error("Second API error",S)};F(`/${ye}/documentManagement/updateDocRequestId`,"post",M,I,i)}else C("Error"),y(!1),m("Failed Submitting the Mass Profit Center for Review  "),v("danger"),b(!1),f(!0),k();handleClose()},a=n=>{console.log(n)};F(`/${N}/massAction/profitCentersSubmitForReview`,"post",l,a,e)},ns=()=>{const e=w;console.log("paylaod",e),console.log("isLoading5",$);const l=n=>{if(A(!1),D(!0),n.statusCode===200){console.log("success"),C("Create"),m(`Mass Profit Center Submitted for Review with ID CPM${n.body}`),v("success"),b(!1),y(!0),E(),f(!0);const i={artifactId:ge,createdBy:o==null?void 0:o.emailId,artifactType:"ProfitCenter",requestId:`CPM${n==null?void 0:n.body}`},M=S=>{console.log("Second API success",S)},I=S=>{console.error("Second API error",S)};F(`/${ye}/documentManagement/updateDocRequestId`,"post",M,I,i)}else C("Error"),y(!1),m("Failed Submitting the Mass Profit Center for Review  "),v("danger"),b(!1),f(!0),k();handleClose()},a=n=>{console.log(n)};F(`/${N}/massAction/changeProfitCentersSubmitForReview`,"post",l,a,e)},Le=(e,l)=>{const a=e.target.value;if(a.length>0&&a[0]===" ")De(a.trimStart());else{let n=a.toUpperCase();De(n)}},U=()=>{D(!0),Fe(!1)},rs=()=>{(o==null?void 0:o.role)==="MDM Steward"&&r.processDesc==="Mass Create"||(o==null?void 0:o.role)==="MDM Steward"&&(s==null?void 0:s.requestType)==="Mass Create"?(A(!0),U(),xo()):(o==null?void 0:o.role)==="Approver"&&r.processDesc==="Mass Create"||(o==null?void 0:o.role)==="Approver"&&(s==null?void 0:s.requestType)==="Mass Create"?(A(!0),U(),os()):(o==null?void 0:o.role)==="Finance"&&r.processDesc==="Mass Create"||(o==null?void 0:o.role)==="Finance"&&(s==null?void 0:s.requestType)==="Mass Create"?(A(!0),U(),ts()):(o==null?void 0:o.role)==="MDM Steward"&&r.processDesc==="Mass Change"||(o==null?void 0:o.role)==="MDM Steward"&&(s==null?void 0:s.requestType)==="Mass Change"?(A(!0),U(),es()):(o==null?void 0:o.role)==="Approver"&&r.processDesc==="Mass Change"||(o==null?void 0:o.role)==="Approver"&&(s==null?void 0:s.requestType)==="Mass Change"?(A(!0),U(),ss()):((o==null?void 0:o.role)==="Finance"&&r.processDesc==="Mass Change"||(o==null?void 0:o.role)==="Finance"&&(s==null?void 0:s.requestType)==="Mass Change")&&(A(!0),U(),ns())},Y=()=>{D(!1),Fe(!0)},z=()=>{Te(!1)},ee=()=>{D(!1),Te(!0)},as=()=>{console.log("isLoading6",$),L.filter((n,i)=>J.includes(i));const e=w,l=n=>{O(!1),D(!0),n.statusCode===200?(console.log("success"),C("Create"),m(`Profit Center Submitted for Correction with ID NPS${n.body}`),v("success"),b(!1),y(!0),E(),f(!0)):(C("Error"),y(!1),m("Failed Submitting Profit Center for Correction"),v("danger"),b(!1),f(!0),k()),z()},a=n=>{console.log(n)};console.log("remarkssssssssss",K),F(`/${N}/massAction/profitCentersSendForCorrection`,"post",l,a,e)},ls=()=>{L.filter((n,i)=>J.includes(i));const e=w,l=n=>{O(!1),D(!0),n.statusCode===200?(console.log("success"),C("Create"),m(`Profit Center Submitted for Correction with ID NPS${n.body}`),v("success"),b(!1),y(!0),E(),f(!0)):(C("Error"),y(!1),m("Failed Submitting Profit Center for Correction"),v("danger"),b(!1),f(!0),k()),z()},a=n=>{console.log(n)};console.log("remarkssssssssss",K),F(`/${N}/massAction/changeProfitCentersSendForCorrection`,"post",l,a,e)},is=()=>{console.log("isLoading7",$),L.filter((n,i)=>J.includes(i));const e=w,l=n=>{O(!1),D(!0),n.statusCode===200?(console.log("success"),C("Create"),m(`Profit Center Submitted for Correction with ID NCS${n.body}`),v("success"),b(!1),y(!0),E(),f(!0)):(C("Error"),y(!1),m("Failed Submitting Profit Center for Correction"),v("danger"),b(!1),f(!0),k()),z()},a=n=>{console.log(n)};F(`/${N}/massAction/profitCentersSendForReview`,"post",l,a,e)},cs=()=>{L.filter((n,i)=>J.includes(i));const e=w,l=n=>{O(!1),D(!0),n.statusCode===200?(console.log("success"),C("Create"),m(`Profit Center Submitted for Correction with ID NCS${n.body}`),v("success"),b(!1),y(!0),E(),f(!0)):(C("Error"),y(!1),m("Failed Submitting Profit Center for Correction"),v("danger"),b(!1),f(!0),k()),z()},a=n=>{console.log(n)};F(`/${N}/massAction/changeProfitCentersSendForReview`,"post",l,a,e)},ds=()=>{(o==null?void 0:o.role)==="MDM Steward"&&r.processDesc==="Mass Create"||(o==null?void 0:o.role)==="MDM Steward"&&(s==null?void 0:s.requestType)==="Mass Create"?(O(!0),z(),as()):(o==null?void 0:o.role)==="Approver"&&r.processDesc==="Mass Create"||(o==null?void 0:o.role)==="Approver"&&(s==null?void 0:s.requestType)==="Mass Create"?(O(!0),z(),is()):(o==null?void 0:o.role)==="MDM Steward"&&r.processDesc==="Mass Change"||(o==null?void 0:o.role)==="MDM Steward"&&(s==null?void 0:s.requestType)==="Mass Change"?(z(),ls()):((o==null?void 0:o.role)==="Approver"&&r.processDesc==="Mass Change"||(o==null?void 0:o.role)==="Approver"&&(s==null?void 0:s.requestType)==="Mass Change")&&(z(),cs())},ze=()=>{A(!0);let e=w;const l=n=>{n.statusCode===400?(ke(n.body),me(!0),we(!0),A(!1)):(we(!1),qe(!1),C("Create"),console.log("success"),C("Create"),m("All Data has been Validated. Profit Center can be Sent for Review"),v("success"),b(!1),y(!0),E(),f(!0),he(!0),A(!1))},a=n=>{console.log(n)};F(`/${N}/massAction/validateMassProfitCenter`,"post",l,a,e)},us=(e,l)=>e.every(a=>!l.includes(a)),Oe=()=>{A(!0);const e=L.filter((u,q)=>J.includes(q));console.log("selectedData",e);const l=e.map(u=>({...w[u==null?void 0:u.id]}));console.log("selectedProfitCenterRows",l);const a=[],n=[];l.map(u=>{var W;var q={coArea:u==null?void 0:u.ControllingArea,name:u==null?void 0:u.PrctrName.toUpperCase()};a.push(q),n.push((W=u==null?void 0:u.PrctrName)==null?void 0:W.toUpperCase())}),console.log("duplicateCheckPayload",a);const i=[];V.map(u=>{var q;i.push((q=u.profitCenterName)==null?void 0:q.toUpperCase())}),console.log(i,n,"arrayofviewand");let M=us(i,n),I=w;I=l;const S=u=>{u.statusCode===400?(ke(u.body),me(!0),A(!1)):(qe(!1),C("Create"),console.log("success"),C("Create"),m("All Data has been Validated. Profit Center can be Sent for Review"),v("success"),b(!1),y(!0),E(),f(!0),he(!0),(a.coArea!==""||a.name!=="")&&((s==null?void 0:s.requestType)==="Mass Change"&&!M?A(!1):F(`/${N}/alter/fetchPCDescriptionsDupliChk`,"post",R,X,a)))},R=u=>{if(console.log("dataaaa",u),u.body.length===0||!u.body.some(q=>a.some(W=>W.name.toUpperCase()===q.matches[0])))A(!1),Ne(!1);else{const q=u.body.map(W=>W.matches[0]);A(!1),C("Duplicate Check"),y(!1),m("There is a direct match for the Profit Center name."),v("danger"),b(!1),f(!0),k(),Ne(!0),Oo(q)}},X=u=>{console.log(u)},h=u=>{console.log(u)};F(`/${N}/massAction/validateMassProfitCenter`,"post",S,h,I)},_e=()=>{me(!1)},ps=[{field:"profitCenter",headerName:"Profit Center",editable:!1,flex:1},{field:"error",headerName:"Error",editable:!1,flex:1}];console.log("profitValidationError",Q);const Cs=(je=Q==null?void 0:Q.filter(e=>(e==null?void 0:e.code)===400))==null?void 0:je.map((e,l)=>{var a;if(e.code===400)return{id:l,profitCenter:e==null?void 0:e.profitCenter,error:(a=e==null?void 0:e.status)==null?void 0:a.message}});return t(j,{children:$===!0?t(vs,{}):p("div",{style:{backgroundColor:"#FAFCFF"},children:[p(se,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:No,onClose:z,children:[p(te,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(g,{variant:"h6",children:"Remarks"}),t(x,{sx:{width:"max-content"},onClick:z,children:t(ve,{})})]}),t(ne,{sx:{padding:".5rem 1rem"},children:t(re,{children:t(ae,{sx:{minWidth:400},children:t(fo,{sx:{height:"auto"},fullWidth:!0,children:t(vo,{sx:{backgroundColor:"#F5F5F5"},onChange:Le,value:K,multiline:!0,placeholder:"Enter Remarks for Correction",inputProps:{maxLength:254}})})})})}),p(le,{sx:{display:"flex",justifyContent:"end"},children:[t(T,{sx:{width:"max-content",textTransform:"capitalize"},onClick:z,children:"Cancel"}),t(T,{className:"button_primary--normal",type:"save",onClick:ds,variant:"contained",children:"Submit"})]})]}),p(se,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:Po,onClose:U,children:[p(te,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(g,{variant:"h6",children:"Remarks"}),t(x,{sx:{width:"max-content"},onClick:U,children:t(ve,{})})]}),t(ne,{sx:{padding:".5rem 1rem"},children:t(re,{children:t(ae,{sx:{minWidth:400},children:t(fo,{sx:{height:"auto"},fullWidth:!0,children:t(vo,{sx:{backgroundColor:"#F5F5F5"},onChange:Le,value:K,multiline:!0,placeholder:"Enter Remarks",inputProps:{maxLength:254}})})})})}),p(le,{sx:{display:"flex",justifyContent:"end"},children:[t(T,{sx:{width:"max-content",textTransform:"capitalize"},onClick:U,children:"Cancel"}),t(T,{className:"button_primary--normal",type:"save",onClick:rs,variant:"contained",children:"Submit"})]})]}),p(se,{open:Lo,fullWidth:!0,onClose:_e,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[p(te,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(g,{variant:"h6",color:"red",children:"Errors"}),t(x,{sx:{width:"max-content"},onClick:_e,children:t(ve,{})})]}),t(ne,{sx:{padding:".5rem 1rem"},children:t(be,{isLoading:$,width:"100%",rows:Cs,columns:ps,pageSize:10,getRowId:"id",hideFooter:!0,checkboxSelection:!1,disableSelectionOnClick:!0,status_onRowSingleClick:!0,stopPropagation_Column:"action",status_onRowDoubleClick:!0})}),t(le,{sx:{display:"flex",justifyContent:"end"}})]}),t(bs,{sx:{color:"#fff",zIndex:e=>e.zIndex.drawer+1},open:Bo,children:t(ys,{color:"inherit"})}),Do&&t(Ms,{openSnackBar:Ao,alertMsg:Se,handleSnackBarClose:jo}),t(Ss,{dialogState:Io,openReusableDialog:k,closeReusableDialog:Be,dialogTitle:To,dialogMessage:Se,handleDialogConfirm:Be,dialogOkText:"OK",handleExtraButton:Go,dialogSeverity:Fo}),p("div",{style:{...As,backgroundColor:"#FAFCFF"},children:[t(B,{container:!0,sx:Ts,children:p(B,{item:!0,md:12,sx:{display:"flex",marginBottom:"0"},children:[p(B,{item:!0,md:11,sx:{display:"flex"},children:[t(B,{children:t(x,{color:"primary","aria-label":"upload picture",component:"label",sx:Fs,children:t(ks,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{ce(-1)}})})}),r.processDesc==="Mass Create"?p(B,{children:[t(g,{variant:"h3",children:t("strong",{children:"Create Multiple Profit Center"})}),t(g,{variant:"body2",color:"#777",children:"This view displays list of Profit Centers"})]}):(s==null?void 0:s.requestType)==="Mass Create"?p(B,{children:[t(g,{variant:"h3",children:t("strong",{children:"Create Multiple Profit Center"})}),t(g,{variant:"body2",color:"#777",children:"This view displays list of Profit Centers"})]}):(s==null?void 0:s.requestType)==="Mass Change"?p(B,{children:[t(g,{variant:"h3",children:t("strong",{children:"Change Multiple Profit Center"})}),t(g,{variant:"body2",color:"#777",children:"This view displays list of Profit Centers"})]}):(r==null?void 0:r.processDesc)==="Mass Change"?p(B,{children:[t(g,{variant:"h3",children:t("strong",{children:"Change Multiple Profit Center"})}),t(g,{variant:"body2",color:"#777",children:"This view displays list of Profit Centers"})]}):""]}),t(B,{item:!0,md:1,sx:{display:"flex"},children:t(Ds,{title:"Uploaded documents",arrow:!0,children:t(x,{onClick:Vo,children:t(Ls,{})})})}),p(se,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:wo,onClose:$e,children:[t(te,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:(o==null?void 0:o.role)==="Finance"?t(j,{children:t(g,{variant:"h6",children:"Add Attachment"})}):""}),t(ne,{sx:{padding:".5rem 1rem"},children:p(bo,{sx:{padding:"1rem 1rem 0rem 1rem"},children:[(o==null?void 0:o.role)==="Finance"?t(j,{children:t(re,{children:t(ae,{sx:{minWidth:400},children:t(zs,{title:"ProfitCenter",useMetaData:!1,artifactId:ge,artifactName:"ProfitCenter"})})})}):"",t(B,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:t(g,{variant:"h6",children:t("strong",{children:"Attachments"})})}),!!pe.length&&t(be,{width:"100%",rows:pe,columns:Uo,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action"}),!pe.length&&t(g,{variant:"body2",children:"No Attachments Found"}),t("br",{}),t(g,{variant:"h6",children:"Comments"}),!!Ce.length&&t(Os,{sx:{[`& .${_s.root}:before`]:{flex:0,padding:0}},children:Ce.map(e=>p(js,{children:[p(Gs,{children:[t(Vs,{children:t(Is,{sx:{color:"#757575"}})}),t(Us,{})]}),t(Ws,{sx:{py:"12px",px:2},children:t(bo,{elevation:0,sx:{border:1,borderColor:"#C4C4C4",borderRadius:"8px",width:"650px"},children:t(ae,{sx:{padding:"1rem"},children:p(re,{spacing:1,children:[t(B,{sx:{display:"flex",justifyContent:"space-between"},children:t(g,{sx:{textAlign:"right",color:" #757575",fontWeight:"500",fontSize:"12px"},children:H(e.createdAt).format("DD MMM YYYY")})}),t(g,{sx:{fontSize:"12px",color:" #757575",fontWeight:"500"},children:e.user}),t(g,{sx:{fontSize:"12px",color:"#1D1D1D",fontWeight:"600"},children:e.comment})]})})})})]}))}),!Ce.length&&t(g,{variant:"body2",children:"No Comments Found"}),t("br",{})]})}),t(le,{children:t(T,{onClick:$e,children:"Close"})})]})]})}),t(B,{item:!0,sx:{position:"relative"},children:t(be,{isLoading:$,width:"100%",title:"Mass Profit Center List ("+(L==null?void 0:L.length)+")",rows:L,columns:Zo,pageSize:10,getRowIdValue:"id",hideFooter:!1,checkboxSelection:(o==null?void 0:o.role)==="Finance"&&r.processDesc==="Mass Create"||(o==null?void 0:o.role)==="Finance"&&(s==null?void 0:s.requestType)==="Mass Create"||(o==null?void 0:o.role)==="Finance"&&r.processDesc==="Mass Change"||(o==null?void 0:o.role)==="Finance"&&(s==null?void 0:s.requestType)==="Mass Change",disableSelectionOnClick:!0,status_onRowSingleClick:!0,onRowsSelectionHandler:Qo,callback_onRowSingleClick:e=>{const l=e.row.profitCenter,a=V.find(n=>n.profitCenter===l);console.log(a,"yo"),ce(`/masterDataCockpit/profitCenter/massProfitCenterTableRequestBench/displayMultipleProfitCenterRequestBench/${l}`,{state:{rowData:e.row,requestNumber:s==null?void 0:s.requestId.slice(3),tabsData:a,requestbenchRowData:s}})},stopPropagation_Column:"action",status_onRowDoubleClick:!0})})]}),Ns(_o,"Profit Center","ChangePC")?t(Ps,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:p(ws,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},value:Mo,children:[(o==null?void 0:o.role)==="MDM Steward"&&r.processDesc==="Mass Create"||(o==null?void 0:o.role)==="MDM Steward"&&(s==null?void 0:s.requestType)==="Mass Create"?p(j,{children:[t(T,{variant:"outlined",size:"small",sx:{button_Outlined:ie,mr:1},onClick:ee,children:"Correction"}),t(T,{variant:"contained",size:"small",sx:{...G},onClick:Y,children:"Submit For Approval"})]}):(o==null?void 0:o.role)==="Approver"&&r.processDesc==="Mass Create"||(o==null?void 0:o.role)==="Approver"&&(s==null?void 0:s.requestType)==="Mass Create"?p(j,{children:[t(T,{variant:"outlined",size:"small",sx:{button_Outlined:ie,mr:1},onClick:ee,children:"Correction"}),t(T,{variant:"contained",size:"small",sx:{...G,mr:1},onClick:ze,children:"Validate"}),t(T,{variant:"contained",size:"small",sx:{...G},onClick:Y,disabled:Pe,children:"Approve"})]}):(o==null?void 0:o.role)==="Finance"&&r.processDesc==="Mass Create"||(o==null?void 0:o.role)==="Finance"&&(s==null?void 0:s.requestType)==="Mass Create"?p(j,{children:[t(T,{variant:"contained",size:"small",sx:{...G,mr:1},onClick:Oe,disabled:!ue,children:"Validate"}),t(T,{variant:"contained",size:"small",sx:{...G},onClick:Y,disabled:Ie,children:"Submit For Review"})]}):"",(o==null?void 0:o.role)==="MDM Steward"&&r.processDesc==="Mass Change"||(o==null?void 0:o.role)==="MDM Steward"&&(s==null?void 0:s.requestType)==="Mass Change"?p(j,{children:[t(T,{variant:"outlined",size:"small",sx:{button_Outlined:ie,mr:1},onClick:ee,children:"Correction"}),t(T,{variant:"contained",size:"small",sx:{...G},onClick:Y,children:"Submit For Approval"})]}):(o==null?void 0:o.role)==="Approver"&&r.processDesc==="Mass Change"||(o==null?void 0:o.role)==="Approver"&&(s==null?void 0:s.requestType)==="Mass Change"?p(j,{children:[t(T,{variant:"outlined",size:"small",sx:{button_Outlined:ie,mr:1},onClick:ee,children:"Correction"}),t(T,{variant:"contained",size:"small",sx:{...G,mr:1},onClick:ze,children:"Validate"}),t(T,{variant:"contained",size:"small",sx:{...G},onClick:Y,disabled:Pe,children:"Approve"})]}):(o==null?void 0:o.role)==="Finance"&&r.processDesc==="Mass Change"||(o==null?void 0:o.role)==="Finance"&&(s==null?void 0:s.requestType)==="Mass Change"?p(j,{children:[t(T,{variant:"contained",size:"small",sx:{...G,mr:1},onClick:Oe,disabled:!ue,children:"Validate"}),t(T,{variant:"contained",size:"small",sx:{...G},onClick:Y,disabled:Ie,children:"Submit For Review"})]}):""]})}):""]})})};export{ht as default};
