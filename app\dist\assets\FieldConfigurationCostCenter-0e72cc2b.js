import{b as ye,r as s,j as y,a as e,b3 as Ce,z as xe,am as ke,G as E,g as Y,A as Z,T as N,f as J,an as Fe,aE as De,t as ve,aD as fe,K as Q,b$ as X,ck as Ee,s as je,F as me,aj as Ae,ak as qe,al as Ie,bY as We,bZ as He,h as be,I as pe,b1 as Se,b_ as we,cl as Le,v as Ve,w as _e,B as ze,cm as Ke}from"./index-17b8d91e.js";import{R as Me}from"./ReusableFieldCatalog-0c7e68b5.js";const $e=({})=>{const h=ye();s.useState("");const[C,c]=s.useState(null),[F,D]=s.useState([]),[u,M]=s.useState({}),[Re,ee]=s.useState(null),[T,j]=s.useState({}),[se,te]=s.useState({}),[x,A]=s.useState({}),[ae,q]=s.useState(!1),[ie,oe]=s.useState(!1),[ne,I]=s.useState(!1),[W,H]=s.useState(""),[le,w]=s.useState(!1),[Be,L]=s.useState(!1),[Oe,V]=s.useState(!0),[ce,_]=s.useState(!1),[Ne,R]=s.useState(!1),z=()=>{h("/masterDataCockpit/costCenter")},de=()=>{h("/masterDataCockpit/costCenter")},re=()=>{setOpen(!1)},ge=()=>{q(!1),h("/masterDataCockpit/costCenter")},P=()=>{q(!0)},U=()=>{oe(!0)},he=()=>{const r=b=>{const o=[],v=[];Object.keys(b.body).map(t=>{const g=b.body[t];Object.keys(g).map(a=>{const l=b.body[t][a];if(Array.isArray(l)){let p={heading:a,fields:l.map(n=>n.fieldName),viewName:t,fieldVisibility:l.map(n=>({fieldName:n.fieldName,visibility:n.visibility}))};o.push(p),console.log(o,"hello"),l.forEach(n=>{console.log("Field Name:",n.fieldName),console.log("Is Required:",n.Required),n.Required==="true"&&v.push(n.fieldName)})}})}),D(o),console.log("Required Fields:",v);const k={},O={},i={};o.forEach(t=>{const{heading:g,fields:a,viewName:l,fieldVisibility:p}=t;k[l]||(k[l]={heading:l,subheadings:[]}),k[l].subheadings.push({heading:g,fields:a}),p.forEach(n=>{let B=n.visibility==="Required"?"Mandatory":n.visibility==="Hidden"?"Hide":n.visibility==="0"?"0":"Optional";O[n.fieldName]=B,n.visibility==="0"&&(i[n.fieldName]=!0)})}),M(k),A(O),te(i),j(i),console.log(k,"Fieldset")},f=b=>{console.log(b)};Q(`/${X}/data/getFieldCatalogueDetails?screenName=Change`,"get",r,f)};s.useEffect(()=>{he()},[]);const G=()=>{console.log("helloooo");let r={};Object.keys(u).forEach(o=>{u[o].subheadings.forEach(k=>{const{heading:O,fields:i}=k;i.forEach(t=>{if(x[t]!=="0"&&T[t]){const g=x[t]==="Mandatory"?"Required":x[t]==="Hide"?"Hidden":"Optional";r[o]||(r[o]=[]),r[o].some(l=>l.fieldName===t)||r[o].push({fieldName:t,cardName:O,viewName:o,visibility:g,screenName:"Change"})}})})});const f=o=>{console.log(o,"example"),R(),o.statusCode===200?(console.log("success"),I("Submit"),H("Field Catalog has been submitted successfully"),w("success"),V(!1),_(!0),P(),L(!0),R(!1)):(I("Submit"),_(!1),H("Submission Failed"),w("danger"),V(!1),L(!0),U(),R(!1)),re()},b=o=>{console.log(o)};Object.keys(r).forEach(o=>{const v=r[o];v.length>0?Q(`/${X}/alter/changeVisibility`,"post",f,b,v):console.log(`No payload data to send for viewName: ${o}`)}),dispatch(Ee())};return y("div",{children:[e(Ce,{dialogState:ie,openReusableDialog:U,closeReusableDialog:z,dialogTitle:ne,dialogMessage:W,handleDialogConfirm:z,dialogOkText:"OK",handleExtraButton:de,dialogSeverity:le}),ce&&e(xe,{openSnackBar:ae,alertMsg:W,handleSnackBarClose:ge}),e(E,{container:!0,sx:ke,children:e(E,{item:!0,md:12,children:Object.keys(u).map(r=>y(Y,{sx:{mb:2},className:"filter-accordion",children:[e(Z,{sx:{backgroundColor:"#f5f5f5"},children:e(N,{sx:{fontWeight:"700",margin:"0px !important"},children:r})}),e(J,{children:u[r].subheadings.map((f,b)=>y(Y,{sx:{mb:2},children:[e(Z,{expandIcon:e(Fe,{}),sx:{backgroundColor:"#F1F0FF"},children:e(N,{sx:{fontWeight:"700",margin:"0px !important",fontSize:"14px"},children:f.heading})}),e(J,{children:e("div",{sx:{fontSize:"25px"},children:e(Me,{fields:f.fields,heading:f.heading,childCheckedStates:T,setChildCheckedStates:j,childRadioValues:x,setChildRadioValues:A,onSubmitButtonClick:()=>G(),mandatoryFields:F,DisabledChildCheck:se})})})]},b))})]},r))})}),e(fe,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:e(De,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},value:C,onChange:(r,f)=>{ee(F[f]),c(f)},children:e(ve,{size:"small",variant:"contained",onClick:G,children:"Submit"})})})]})},Pe=()=>{const h=document.getElementsByTagName("HTML")[0],C=document.getElementsByTagName("BODY")[0];let c=h.clientWidth,F=C.clientWidth;const D=document.getElementById("e-invoice-export"),u=D.scrollWidth-D.clientWidth;u>D.clientWidth&&(c+=u,F+=u),h.style.width=c+"px",C.style.width=F+"px",Ke(D).then(M=>M.toDataURL("image/png",1)).then(M=>{Ue(M,"FieldCatalog.png"),h.style.width=null,C.style.width=null})},Ue=(h,C)=>{const c=window.document.createElement("a");c.href=h,c.download=C,(document.body||document.documentElement).appendChild(c),typeof c.click=="function"?c.click():(c.target="_blank",c.dispatchEvent(new MouseEvent("click",{view:window,bubbles:!0,cancelable:!0}))),URL.revokeObjectURL(c.href),c.remove()},Ze=({})=>{const h=ye();s.useState("");const[C,c]=s.useState(null),[F,D]=s.useState([]),[u,M]=s.useState({}),[Re,ee]=s.useState(null),[T,j]=s.useState({}),[se,te]=s.useState({}),[x,A]=s.useState({}),[ae,q]=s.useState(!1),[ie,oe]=s.useState(!1),[ne,I]=s.useState(!1),[W,H]=s.useState(""),[le,w]=s.useState(!1),[Be,L]=s.useState(!1),[Oe,V]=s.useState(!0),[ce,_]=s.useState(!1),[Ne,R]=s.useState(!1),[z,de]=s.useState(0),re=["For Create","For Change"],ge=je(),P=()=>{h("/masterDataCockpit/costCenter")},U=()=>{h("/masterDataCockpit/costCenter")},he=()=>{setOpen(!1)},G=()=>{q(!1),h("/masterDataCockpit/costCenter")},r=()=>{q(!0)},f=()=>{oe(!0)},b=()=>{const i=g=>{const a=[],l=[];Object.keys(g.body).map(m=>{const K=g.body[m];Object.keys(K).map($=>{const S=g.body[m][$];if(Array.isArray(S)){let ue={heading:$,fields:S.map(d=>d.fieldName),viewName:m,fieldVisibility:S.map(d=>({fieldName:d.fieldName,visibility:d.visibility}))};a.push(ue),console.log(a,"hello"),S.forEach(d=>{console.log("Field Name:",d.fieldName),console.log("Is Required:",d.Required),d.Required==="true"&&l.push(d.fieldName)})}})}),D(a),console.log("Required Fields:",l);const p={},n={},B={};a.forEach(m=>{const{heading:K,fields:$,viewName:S,fieldVisibility:ue}=m;p[S]||(p[S]={heading:S,subheadings:[]}),p[S].subheadings.push({heading:K,fields:$}),ue.forEach(d=>{let Te=d.visibility==="Required"?"Mandatory":d.visibility==="Hidden"?"Hide":d.visibility==="0"?"0":"Optional";n[d.fieldName]=Te,d.visibility==="0"&&(B[d.fieldName]=!0)})}),M(p),A(n),te(B),j(B),console.log(p,"Fieldset")},t=g=>{console.log(g)};Q(`/${X}/data/getFieldCatalogueDetails?screenName=Create`,"get",i,t)};s.useEffect(()=>{b()},[]);const o=()=>{console.log("Clicked");let i={};Object.keys(u).forEach(a=>{u[a].subheadings.forEach(p=>{const{heading:n,fields:B}=p;B.forEach(m=>{if(x[m]!=="0"&&T[m]){const K=x[m]==="Mandatory"?"Required":x[m]==="Hide"?"Hidden":"Optional";i[a]||(i[a]=[]),i[a].some(S=>S.fieldName===m)||i[a].push({fieldName:m,cardName:n,viewName:a,visibility:K,screenName:"Create"})}})})});const t=a=>{console.log(a,"example"),R(),a.statusCode===200?(console.log("success"),I("Submit"),H("Field Catalog has been submitted successfully"),w("success"),V(!1),_(!0),r(),L(!0),R(!1)):(I("Submit"),_(!1),H("Submission Failed"),w("danger"),V(!1),L(!0),f(),R(!1)),he()},g=a=>{console.log(a)};Object.keys(i).forEach(a=>{const l=i[a];l.length>0?Q(`/${X}/alter/changeVisibility`,"post",t,g,l):console.log(`No payload data to send for viewName: ${a}`)}),ge(Ee())},v=[[e(me,{children:y(E,{container:!0,sx:ke,children:[e(E,{item:!0,md:12,children:Object.keys(u).map(i=>y(Y,{sx:{mb:2},className:"filter-accordion",children:[e(Z,{sx:{backgroundColor:"#f5f5f5"},children:e(N,{sx:{fontWeight:"700",margin:"0px !important"},children:i})}),e(J,{children:u[i].subheadings.map((t,g)=>y(Y,{sx:{mb:2},children:[e(Z,{expandIcon:e(Fe,{}),sx:{backgroundColor:"#F1F0FF"},children:e(N,{sx:{fontWeight:"700",margin:"0px !important",fontSize:"14px"},children:t.heading})}),e(J,{children:e("div",{sx:{fontSize:"25px"},children:e(Me,{fields:t.fields,heading:t.heading,childCheckedStates:T,setChildCheckedStates:j,childRadioValues:x,setChildRadioValues:A,onSubmitButtonClick:()=>o(),mandatoryFields:F,DisabledChildCheck:se})})})]},g))})]},i))}),e(fe,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:e(De,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},value:C,onChange:(i,t)=>{ee(F[t]),c(t)},children:e(ve,{size:"small",variant:"contained",onClick:o,children:"Submit"})})})]})})],[e(me,{children:e($e,{})})]],k=(i,t)=>{de(t)};s.useState(""),s.useState([]);function O(){}return y("div",{children:[e(Ce,{dialogState:ie,openReusableDialog:f,closeReusableDialog:P,dialogTitle:ne,dialogMessage:W,handleDialogConfirm:P,dialogOkText:"OK",handleExtraButton:U,dialogSeverity:le}),ce&&e(xe,{openSnackBar:ae,alertMsg:W,handleSnackBarClose:G}),e("div",{style:{...Ae,backgroundColor:"#FAFCFF"},children:y(qe,{spacing:1,children:[y(E,{container:!0,sx:Ie,children:[y(E,{item:!0,md:5,sx:We,children:[e(N,{variant:"h3",children:e("strong",{children:"Field Configurations"})}),e(N,{variant:"body2",color:"#777",children:"This view displays the setiings for configuring the Fields"})]}),e(E,{item:!0,md:7,sx:{display:"flex"},children:y(E,{container:!0,direction:"row",justifyContent:"flex-end",alignItems:"center",spacing:0,children:[e(He,{title:"Search for fields in different views",module:"FieldSelection",keyName:"string",message:"Search for fields in different views"}),e(be,{title:"Reload",children:e(pe,{sx:Se,children:e(we,{sx:{"&:hover":{transform:"rotate(360deg)",transition:"0.9s"}},onClick:O})})}),e(be,{title:"Export",children:e(pe,{sx:Se,children:e(Le,{onClick:Pe})})})]})})]}),e(fe,{children:e(Ve,{value:z,onChange:k,variant:"scrollable",sx:{background:"#FFF",borderBottom:"1px solid #BDBDBD",width:"100%"},"aria-label":"mui tabs example",children:re.map((i,t)=>e(_e,{sx:{fontSize:"12px",fontWeight:"700"},label:i},t))})}),v[z].map((i,t)=>e(ze,{children:i},t))]})})]})};export{Ze as default};
