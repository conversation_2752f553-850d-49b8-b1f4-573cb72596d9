import{d7 as j,d8 as U,r as u,cB as _,cC as V,db as L,cE as g,ds as W,cI as P,pO as I,cH as E,o as O,cM as D,cN as F,pP as q}from"./index-17b8d91e.js";function J(t){return j("MuiToggleButton",t)}const K=U("MuiToggleButton",["root","disabled","selected","standard","primary","secondary","sizeSmall","sizeMedium","sizeLarge","fullWidth"]),x=K,Q=u.createContext({}),A=Q,X=u.createContext(void 0),H=X;function Y(t,o){return o===void 0||t===void 0?!1:Array.isArray(o)?o.indexOf(t)>=0:t===o}const Z=["value"],w=["children","className","color","disabled","disableFocusRipple","fullWidth","onChange","onClick","selected","size","value"],S=t=>{const{classes:o,fullWidth:s,selected:r,disabled:f,size:c,color:v}=t,B={root:["root",r&&"selected",f&&"disabled",s&&"fullWidth",`size${L(c)}`,v]};return F(B,J,o)},tt=_(V,{name:"MuiToggleButton",slot:"Root",overridesResolver:(t,o)=>{const{ownerState:s}=t;return[o.root,o[`size${L(s.size)}`]]}})(({theme:t,ownerState:o})=>{let s=o.color==="standard"?t.palette.text.primary:t.palette[o.color].main,r;return t.vars&&(s=o.color==="standard"?t.vars.palette.text.primary:t.vars.palette[o.color].main,r=o.color==="standard"?t.vars.palette.text.primaryChannel:t.vars.palette[o.color].mainChannel),g({},t.typography.button,{borderRadius:(t.vars||t).shape.borderRadius,padding:11,border:`1px solid ${(t.vars||t).palette.divider}`,color:(t.vars||t).palette.action.active},o.fullWidth&&{width:"100%"},{[`&.${x.disabled}`]:{color:(t.vars||t).palette.action.disabled,border:`1px solid ${(t.vars||t).palette.action.disabledBackground}`},"&:hover":{textDecoration:"none",backgroundColor:t.vars?`rgba(${t.vars.palette.text.primaryChannel} / ${t.vars.palette.action.hoverOpacity})`:W(t.palette.text.primary,t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${x.selected}`]:{color:s,backgroundColor:t.vars?`rgba(${r} / ${t.vars.palette.action.selectedOpacity})`:W(s,t.palette.action.selectedOpacity),"&:hover":{backgroundColor:t.vars?`rgba(${r} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.hoverOpacity}))`:W(s,t.palette.action.selectedOpacity+t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:t.vars?`rgba(${r} / ${t.vars.palette.action.selectedOpacity})`:W(s,t.palette.action.selectedOpacity)}}}},o.size==="small"&&{padding:7,fontSize:t.typography.pxToRem(13)},o.size==="large"&&{padding:15,fontSize:t.typography.pxToRem(15)})}),ot=u.forwardRef(function(o,s){const r=u.useContext(A),{value:f}=r,c=P(r,Z),v=u.useContext(H),B=I(g({},c,{selected:Y(o.value,f)}),o),$=E({props:B,name:"MuiToggleButton"}),{children:h,className:p,color:N="standard",disabled:C=!1,disableFocusRipple:a=!1,fullWidth:k=!1,onChange:b,onClick:i,selected:R,size:m="medium",value:T}=$,y=P($,w),z=g({},$,{color:N,disabled:C,disableFocusRipple:a,fullWidth:k,size:m}),M=S(z),n=d=>{i&&(i(d,T),d.defaultPrevented)||b&&b(d,T)},l=v||"";return O.jsx(tt,g({className:D(c.className,M.root,p,l),disabled:C,focusRipple:!a,ref:s,onClick:n,onChange:b,value:T,ownerState:z,"aria-pressed":R},y,{children:h}))}),dt=ot;function et(t){return j("MuiToggleButtonGroup",t)}const st=U("MuiToggleButtonGroup",["root","selected","horizontal","vertical","disabled","grouped","groupedHorizontal","groupedVertical","fullWidth","firstButton","lastButton","middleButton"]),e=st,rt=["children","className","color","disabled","exclusive","fullWidth","onChange","orientation","size","value"],at=t=>{const{classes:o,orientation:s,fullWidth:r,disabled:f}=t,c={root:["root",s==="vertical"&&"vertical",r&&"fullWidth"],grouped:["grouped",`grouped${L(s)}`,f&&"disabled"],firstButton:["firstButton"],lastButton:["lastButton"],middleButton:["middleButton"]};return F(c,et,o)},lt=_("div",{name:"MuiToggleButtonGroup",slot:"Root",overridesResolver:(t,o)=>{const{ownerState:s}=t;return[{[`& .${e.grouped}`]:o.grouped},{[`& .${e.grouped}`]:o[`grouped${L(s.orientation)}`]},{[`& .${e.firstButton}`]:o.firstButton},{[`& .${e.lastButton}`]:o.lastButton},{[`& .${e.middleButton}`]:o.middleButton},o.root,s.orientation==="vertical"&&o.vertical,s.fullWidth&&o.fullWidth]}})(({ownerState:t,theme:o})=>g({display:"inline-flex",borderRadius:(o.vars||o).shape.borderRadius},t.orientation==="vertical"&&{flexDirection:"column"},t.fullWidth&&{width:"100%"},{[`& .${e.grouped}`]:g({},t.orientation==="horizontal"?{[`&.${e.selected} + .${e.grouped}.${e.selected}`]:{borderLeft:0,marginLeft:0}}:{[`&.${e.selected} + .${e.grouped}.${e.selected}`]:{borderTop:0,marginTop:0}})},t.orientation==="horizontal"?{[`& .${e.firstButton},& .${e.middleButton}`]:{borderTopRightRadius:0,borderBottomRightRadius:0},[`& .${e.lastButton},& .${e.middleButton}`]:{marginLeft:-1,borderLeft:"1px solid transparent",borderTopLeftRadius:0,borderBottomLeftRadius:0}}:{[`& .${e.firstButton},& .${e.middleButton}`]:{borderBottomLeftRadius:0,borderBottomRightRadius:0},[`& .${e.lastButton},& .${e.middleButton}`]:{marginTop:-1,borderTop:"1px solid transparent",borderTopLeftRadius:0,borderTopRightRadius:0}},t.orientation==="horizontal"?{[`& .${e.lastButton}.${x.disabled},& .${e.middleButton}.${x.disabled}`]:{borderLeft:"1px solid transparent"}}:{[`& .${e.lastButton}.${x.disabled},& .${e.middleButton}.${x.disabled}`]:{borderTop:"1px solid transparent"}})),nt=u.forwardRef(function(o,s){const r=E({props:o,name:"MuiToggleButtonGroup"}),{children:f,className:c,color:v="standard",disabled:B=!1,exclusive:$=!1,fullWidth:h=!1,onChange:p,orientation:N="horizontal",size:C="medium",value:a}=r,k=P(r,rt),b=g({},r,{disabled:B,fullWidth:h,orientation:N,size:C}),i=at(b),R=u.useCallback((n,l)=>{if(!p)return;const d=a&&a.indexOf(l);let G;a&&d>=0?(G=a.slice(),G.splice(d,1)):G=a?a.concat(l):[l],p(n,G)},[p,a]),m=u.useCallback((n,l)=>{p&&p(n,a===l?null:l)},[p,a]),T=u.useMemo(()=>({className:i.grouped,onChange:$?m:R,value:a,size:C,fullWidth:h,color:v,disabled:B}),[i.grouped,$,m,R,a,C,h,v,B]),y=q(f),z=y.length,M=n=>{const l=n===0,d=n===z-1;return l&&d?"":l?i.firstButton:d?i.lastButton:i.middleButton};return O.jsx(lt,g({role:"group",className:D(i.root,c),ref:s,ownerState:b},k,{children:O.jsx(A.Provider,{value:T,children:y.map((n,l)=>O.jsx(H.Provider,{value:M(l),children:n},l))})}))}),ut=nt;export{dt as T,ut as a,et as b,e as c,J as g,x as t};
