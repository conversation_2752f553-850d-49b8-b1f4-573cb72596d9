import{r as P,dr as x,cI as k,E as j,cE as r,o as y,cB as v,T as S,dv as e}from"./index-75c1660a.js";import{a7 as V,t as I,w as N,s as w,x as A,v as F,y as L,z as _,A as E,g as z,a as U,J as W,u as h,b as O,E as M,c as Y,L as $,N as B,O as g,W as T,X as q,Y as G,Z as H,_ as J,$ as K}from"./dateViewRenderers-dbe02df3.js";import{a as X,c as Z,r as Q}from"./useSlotProps-da724f1f.js";const ee=n=>{const a=V(n),{forwardedProps:l,internalProps:s}=I(a,"date");return N({forwardedProps:l,internalProps:s,valueManager:w,fieldValueManager:A,validator:F,valueType:"date"})},oe=["slots","slotProps","InputProps","inputProps"],te=P.forwardRef(function(a,l){const s=x({props:a,name:"MuiDateField"}),{slots:o,slotProps:t,InputProps:c,inputProps:u}=s,p=k(s,oe),f=s,d=(o==null?void 0:o.textField)??(a.enableAccessibleFieldDOMStructure?L:j),i=X({elementType:d,externalSlotProps:t==null?void 0:t.textField,externalForwardedProps:p,additionalProps:{ref:l},ownerState:f});i.inputProps=r({},u,i.inputProps),i.InputProps=r({},c,i.InputProps);const b=ee(i),m=_(b),D=E(r({},m,{slots:o,slotProps:t}));return y.jsx(d,r({},D))});function ae(n){return z("MuiDatePickerToolbar",n)}U("MuiDatePickerToolbar",["root","title"]);const se=["value","isLandscape","onChange","toolbarFormat","toolbarPlaceholder","views","className","onViewChange","view"],re=n=>{const{classes:a}=n;return Z({root:["root"],title:["title"]},ae,a)},ne=v(W,{name:"MuiDatePickerToolbar",slot:"Root",overridesResolver:(n,a)=>a.root})({}),le=v(S,{name:"MuiDatePickerToolbar",slot:"Title",overridesResolver:(n,a)=>a.title})({variants:[{props:{isLandscape:!0},style:{margin:"auto 16px auto auto"}}]}),ie=P.forwardRef(function(a,l){const s=x({props:a,name:"MuiDatePickerToolbar"}),{value:o,isLandscape:t,toolbarFormat:c,toolbarPlaceholder:u="––",views:p,className:f}=s,d=k(s,se),i=h(),b=O(),m=re(s),D=P.useMemo(()=>{if(!o)return u;const C=M(i,{format:c,views:p},!0);return i.formatByString(o,C)},[o,c,u,i,p]),R=s;return y.jsx(ne,r({ref:l,toolbarTitle:b.datePickerToolbarTitle,isLandscape:t,className:Y(m.root,f)},d,{children:y.jsx(le,{variant:"h4",align:t?"left":"center",ownerState:R,className:m.title,children:D})}))});function ce(n,a){const l=h(),s=$(),o=x({props:n,name:a}),t=P.useMemo(()=>{var c;return((c=o.localeText)==null?void 0:c.toolbarTitle)==null?o.localeText:r({},o.localeText,{datePickerToolbarTitle:o.localeText.toolbarTitle})},[o.localeText]);return r({},o,{localeText:t},B({views:o.views,openTo:o.openTo,defaultViews:["year","day"],defaultOpenTo:"day"}),{disableFuture:o.disableFuture??!1,disablePast:o.disablePast??!1,minDate:g(l,o.minDate,s.minDate),maxDate:g(l,o.maxDate,s.maxDate),slots:r({toolbar:ie},o.slots)})}const ue=P.forwardRef(function(a,l){var f,d;const s=O(),o=h(),t=ce(a,"MuiDesktopDatePicker"),c=r({day:T,month:T,year:T},t.viewRenderers),u=r({},t,{viewRenderers:c,format:M(o,t,!1),yearsPerRow:t.yearsPerRow??4,slots:r({openPickerIcon:q,field:te},t.slots),slotProps:r({},t.slotProps,{field:i=>{var b;return r({},Q((b=t.slotProps)==null?void 0:b.field,i),G(t),{ref:l})},toolbar:r({hidden:!0},(f=t.slotProps)==null?void 0:f.toolbar)})}),{renderPicker:p}=H({props:u,valueManager:w,valueType:"date",getOpenDialogAriaText:J({utils:o,formatKey:"fullDate",contextTranslation:s.openDatePickerDialogue,propsTranslation:(d=u.localeText)==null?void 0:d.openDatePickerDialogue}),validator:F});return p()});ue.propTypes={autoFocus:e.bool,className:e.string,closeOnSelect:e.bool,dayOfWeekFormatter:e.func,defaultValue:e.object,disabled:e.bool,disableFuture:e.bool,disableHighlightToday:e.bool,disableOpenPicker:e.bool,disablePast:e.bool,displayWeekNumber:e.bool,enableAccessibleFieldDOMStructure:e.any,fixedWeekNumber:e.number,format:e.string,formatDensity:e.oneOf(["dense","spacious"]),inputRef:K,label:e.node,loading:e.bool,localeText:e.object,maxDate:e.object,minDate:e.object,monthsPerRow:e.oneOf([3,4]),name:e.string,onAccept:e.func,onChange:e.func,onClose:e.func,onError:e.func,onMonthChange:e.func,onOpen:e.func,onSelectedSectionsChange:e.func,onViewChange:e.func,onYearChange:e.func,open:e.bool,openTo:e.oneOf(["day","month","year"]),orientation:e.oneOf(["landscape","portrait"]),readOnly:e.bool,reduceAnimations:e.bool,referenceDate:e.object,renderLoading:e.func,selectedSections:e.oneOfType([e.oneOf(["all","day","empty","hours","meridiem","minutes","month","seconds","weekDay","year"]),e.number]),shouldDisableDate:e.func,shouldDisableMonth:e.func,shouldDisableYear:e.func,showDaysOutsideCurrentMonth:e.bool,slotProps:e.object,slots:e.object,sx:e.oneOfType([e.arrayOf(e.oneOfType([e.func,e.object,e.bool])),e.func,e.object]),timezone:e.string,value:e.object,view:e.oneOf(["day","month","year"]),viewRenderers:e.shape({day:e.func,month:e.func,year:e.func}),views:e.arrayOf(e.oneOf(["day","month","year"]).isRequired),yearsOrder:e.oneOf(["asc","desc"]),yearsPerRow:e.oneOf([3,4])};export{te as D,ue as a,ce as u};
