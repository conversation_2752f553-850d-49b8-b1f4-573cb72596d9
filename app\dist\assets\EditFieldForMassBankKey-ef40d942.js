import{r as x,q as R,s as V,eP as ee,a as r,x as ae,j as A,T as C,b6 as te,E as W,at as oe,au as re,G as _,ay as H,F,bI as J,eO as ne,K,eK as N,ai as O}from"./index-17b8d91e.js";import{D as ce}from"./DatePicker-********.js";function se(o,u){return Array.isArray(u)&&u.find(S=>S.code===o)||""}const ye=({label:o,value:u,length:P,units:S,onSave:le,fieldGroup:s,isEditMode:Q,activeTabIndex:q,visibility:l,isExtendMode:de,pcTabs:z,selectedRowData:I,options:ge=[],type:y})=>{var L,T;const[D,E]=x.useState(u),[ie,X]=x.useState(!1),m=R(e=>e.AllDropDown.dropDown),c=R(e=>e.bankKey.MultipleBankKeyData);console.log(c,"bankKeyData====");const i=V();se(D,m),R(e=>e.appSettings),R(e=>e.edit.payload);let M={},v=-1;console.log(I,"selectedRowData");for(let e=0;e<(c==null?void 0:c.length);e++)if(console.log("ccdata",c[e].BankKey),c[e].BankKey===I){M=c[e],v=e;break}let d=z[q];console.log("activerow",v,M,d);const p=(e,a)=>{console.log(e,a,"fieldNamedata");const n=e==null?void 0:e.find(t=>(t==null?void 0:t.fieldName)===a);return n?n.value:""},h=c[v];console.log(h,"bankKeyInnerData=====");let g=o.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("");x.useEffect(()=>{E(u)},[u]),console.log(l,g,"visibility45"),x.useEffect(()=>{(l==="0"||l==="Required")&&i(ee(g))},[d]);const B={label:o,value:D,units:S,type:y,visibility:l};console.log("fieldData==========",B);const k=(e,a)=>{i(J({keyname:g.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""),data:a}));let n=c==null?void 0:c.map((t,b)=>{let $=z[q];if(b===v){let G=t.viewData,U=t.viewData[$];console.log("temp",U);let f=t.viewData[$][s];return console.log("temp2",f),{...t,viewData:{...G,[$]:{...U,[s]:f==null?void 0:f.map(j=>j.fieldName===e?{...j,value:a}:j)}}}}else return t});console.log("changedData",n),i(ne(n))},Y=e=>{console.log("countryyyyy",e);const a=t=>{console.log("value",t),i(J({keyname:"Region",data:""})),i(O({keyName:"Region",data:t.body}))},n=t=>{console.log(t,"error in dojax")};K(`/${N}/data/getRegionBasedOnCountry?country=${e==null?void 0:e.code}`,"get",a,n)},Z=e=>{const a=t=>{i(O({keyName:"Region1",data:t.body}))},n=t=>{console.log(t,"error in dojax")};K(`/${N}/data/getRegionBasedOnCountry?country=${e.code}`,"get",a,n)},w=e=>{const a=t=>{i(O({keyName:"Region2",data:t.body}))},n=t=>{console.log(t,"error in dojax")};K(`/${N}/data/getRegionBasedOnCountry?country=${e.code}`,"get",a,n)};return console.log("chiranjit",D),console.log("editedValue[key] ",m[g]),console.log("editedValue[key] ",D),r(_,{item:!0,children:r(ae,{children:Q?A(F,{children:[A(C,{variant:"body2",color:"#777",children:[o," ",l==="Required"||l==="0"?r("span",{style:{color:"red"},children:"*"}):""]}),y==="Drop Down"?r(te,{options:m[g]??[],value:p(h.viewData[d][s],o)&&((L=m[g])==null?void 0:L.filter(e=>e.code===p(h.viewData[d][s],o)))&&((T=m[g])==null?void 0:T.filter(e=>e.code===p(h.viewData[d][s],o))[0])||"",onChange:(e,a)=>{o==="Country/Reg"&&Y(a),o==="Country 1"&&Z(a),o==="Country 2"&&w(a),k(o,a==null?void 0:a.code),console.log("newValue",a),E(a==null?void 0:a.code),X(!0),console.log("keys",g)},getOptionLabel:e=>(console.log("optionn",e),e===""||(e==null?void 0:e.code)===""?"":`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??""),renderOption:(e,a)=>r("li",{...e,children:r(C,{style:{fontSize:12},children:`${a==null?void 0:a.code} - ${a==null?void 0:a.desc}`})}),renderInput:e=>r(W,{...e,variant:"outlined",placeholder:`Select ${B.label}`,size:"small",label:null})}):y==="Input"?r(W,{variant:"outlined",size:"small",value:p(h.viewData[d][s],o).toUpperCase(),placeholder:`Enter ${B.label}`,inputProps:{maxLength:P},onChange:e=>{const a=e.target.value;if(a.length>0&&a[0]===" ")k(o,a.trimStart());else{let n=a.toUpperCase();k(o,n)}}}):y==="Calendar"?r(oe,{dateAdapter:re,children:r(ce,{slotProps:{textField:{size:"small"}},value:parseInt(p(h.viewData[d][s],o).replace("/Date(","").replace(")/","")),placeholder:"Select Date Range",maxDate:new Date(9999,12,31),onChange:e=>{k(e),E(e)}})}):y==="Radio Button"?r(_,{item:!0,md:2,children:r(H,{sx:{padding:0},checked:p(h.viewData[d][s],o)==!0,onChange:e=>{console.log("oncheckbox",o,e.target.checked),k(o,e.target.checked)}})}):""]}):r(F,{children:A(F,{children:[A(C,{variant:"body2",color:"#777",children:[o," ",l==="Required"||l==="0"?r("span",{style:{color:"red"},children:"*"}):""]}),r(C,{variant:"body2",fontWeight:"bold",children:y==="Radio Button"?r(H,{sx:{padding:0},checked:D,disabled:!0}):D})]})})})})};export{ye as E};
