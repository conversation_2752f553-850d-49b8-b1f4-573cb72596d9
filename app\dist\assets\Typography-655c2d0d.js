import{o as n}from"./index-75c1660a.js";import{G as r,I as o}from"./Button-c2ace85e.js";const h={primary:"var(--primary-main)",secondary:"var(--secondary-main)",error:"var(--error-main)",warning:"var(--warning-main)",info:"var(--info-main)",success:"var(--success-main)",textPrimary:"var(--text-primary)",textSecondary:"var(--text-secondary)",inherit:"inherit",initial:"initial"},m=r(o)({display:"block","&.MuiTypography-h1":{fontWeight:400,fontSize:"3rem",lineHeight:"3.6rem",fontFamily:"inherit"},"&.MuiTypography-h1Medium":{fontWeight:500,fontSize:"3rem",lineHeight:"3.6rem",fontFamily:"inherit"},"&.MuiTypography-h2":{fontWeight:400,fontSize:"2.5rem",lineHeight:"3rem",fontFamily:"inherit"},"&.MuiTypography-h2Medium":{fontWeight:500,fontSize:"2.5rem",lineHeight:"3rem",fontFamily:"inherit"},"&.MuiTypography-h3":{fontWeight:400,fontSize:"2.25rem",lineHeight:"2.7rem",fontFamily:"inherit"},"&.MuiTypography-h3Medium":{fontWeight:500,fontSize:"2.25rem",lineHeight:"2.7rem",fontFamily:"inherit"},"&.MuiTypography-h4":{fontWeight:400,fontSize:"2rem",lineHeight:"2.4rem",fontFamily:"inherit"},"&.MuiTypography-h4Medium":{fontWeight:500,fontSize:"2rem",lineHeight:"2.4rem",fontFamily:"inherit"},"&.MuiTypography-h5":{fontWeight:400,fontSize:"1.5rem",lineHeight:"1.8rem",fontFamily:"inherit"},"&.MuiTypography-h5Medium":{fontWeight:500,fontSize:"1.5rem",lineHeight:"1.8rem",fontFamily:"inherit"},"&.MuiTypography-h6":{fontWeight:400,fontSize:"1.25rem",lineHeight:"1.5rem",fontFamily:"inherit"},"&.MuiTypography-h6Medium":{fontWeight:500,fontSize:"1.25rem",lineHeight:"1.5rem",fontFamily:"inherit"},"&.MuiTypography-subtitle1":{fontWeight:400,fontSize:"1rem",lineHeight:"1.2rem",fontFamily:"inherit"},"&.MuiTypography-subtitle2":{fontWeight:500,fontSize:"1rem",lineHeight:"1.2rem",fontFamily:"inherit"},"&.MuiTypography-body1":{fontWeight:400,fontSize:"0.875rem",lineHeight:"1.05rem",fontFamily:"inherit"},"&.MuiTypography-body2":{fontWeight:400,fontSize:"0.75rem",lineHeight:"1rem",fontFamily:"inherit"},"&.MuiTypography-caption":{fontWeight:400,fontSize:"0.625rem",lineHeight:"1rem",fontFamily:"inherit"}});function f({color:i,...t}){let e;return typeof i=="string"&&(e=h[i]??i),n.jsx(m,{style:{color:e},...t})}export{f};
