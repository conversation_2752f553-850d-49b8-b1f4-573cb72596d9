import React, { useEffect, useState } from "react";
import ReusableSnackBar from "../../Common/ReusableSnackBar";
import {
  BottomNavigation,
  Box,
  Button,
  CardContent,
  Grid,
  IconButton,
  Paper,
  Stack,
  Tab,
  Tabs,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  FormControl,
  TextField,
  Autocomplete,
  Stepper,
  Step,
  StepLabel,
  Card,
  Backdrop,
  CircularProgress,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import MarkunreadOutlinedIcon from "@mui/icons-material/MarkunreadOutlined";
import {
  DateField,
  DatePicker,
  DesktopDatePicker,
  DesktopDateTimePicker,
  LocalizationProvider,
} from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import {
  button_Outlined,
  iconButton_SpacingSmall,
  container_Padding,
  outermostContainer_Information,
  button_Primary,
} from "../../common/commonStyles";
import ReusableTable from "../../Common/ReusableTable";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import { checkIwaAccess, idGenerator,formValidator } from "../../../functions";
import { useDispatch, useSelector } from "react-redux";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import { useLocation, useNavigate } from "react-router-dom";
import { destination_CostCenter, destination_DocumentManagement } from "../../../destinationVariables";
import { doAjax } from "../../Common/fetchService";
import EditableFieldForCostCenter from "./EditableFieldForCostCenter";
import { setDropDown } from "../../../app/dropDownDataSlice";
import moment from "moment/moment";
import ReusableDialog from "../../Common/ReusableDialog";
// import destination_CommonUtils from"../../../destinationVariables";
import ReusableAttachementAndComments from "../../Common/ReusableAttachmentAndComments/ReusableAttachementAndComments";
import {
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineItem,
  TimelineSeparator,
} from "@mui/lab";
import { CheckCircleOutlineOutlined } from "@mui/icons-material";
import { MatDownload, MatView } from "../../DocumentManagement/UtilDoc";
import { clearCostCenter, setCostCenterViewData } from "../../../app/costCenterTabsSlice";
import { setPayloadWhole } from "../../../app/editPayloadSlice";
import lookup from "../../../data/lookup.json"
import LoadingComponent from "../../Common/LoadingComponent";
import { clearProfitCenter } from "../../../app/profitCenterTabsSlice";



const DisplayCopyCostCenter = () => {
  const [isEditMode, setIsEditMode] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [isDisplayMode, setIsDisplayMode] = useState(true);
  const [responseFromAPI, setResponseFromAPI] = useState({});
  const [factorsArray, setFactorsArray] = useState([]);
  const [activeStep, setActiveStep] = useState(0);
  // const [compCode, setCompanyCode] = useState();
  const [costCenterDetails, setCostCenterDetails] = useState([]);
  const [iDs, setIds] = useState();
  const [dupliDialog, setDupliDialog] = useState(false);
  const [value, setValue] = useState([]);
  const [messageDialogTitle, setMessageDialogTitle] = useState(false);
  const [successMsg, setsuccessMsg] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [messageDialogSeverity, setMessageDialogSeverity] = useState(false);
  const [messageDialogOK, setMessageDialogOK] = useState(true);
  const [messageDialogExtra, setMessageDialogExtra] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [blurLoading, setBlurLoading] = useState(false);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [validateFlag, setValidateFlag] = useState(false);
  const [remarks, setRemarks] = useState("");
  const [attachments, setAttachments] = useState([]);
  const [openCorrectionDialog, setOpenCorrectionDialog] = useState(false);
  const [submitForReviewDisabled, setSubmitForReviewDisabled] = useState(true);
  const [openRemarkDialog, setOpenRemarkDialog] = useState(false);
  const [comments, setComments] = useState([]);
  const [handleExtrabutton,setHandleExtrabutton]=useState(false)
  const [testrunStatus, setTestrunStatus] = useState(true);
  const [openCreateDialog, setOpenCreateDialog] = useState(false);
  const [handleExtraText,setHandleExtraText]=useState('')

  const [ccNumber, setCcNumber] = useState("");
  const [errorsFields, setErrorsFields] = useState([]);
  const [formValidationErrorItems, setFormValidationErrorItems] = useState([]); //chiranjit
  const [openSnackbarValidation, setOpenSnackbarValidation] = useState(false);

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const appSettings = useSelector((state) => state.appSettings);
  let iwaAccessData = useSelector(
    (state) => state.userManagement.entitiesAndActivities?.["Cost Center"]
  );
  let taskData = useSelector((state) => state?.initialData?.IWMMyTask);
  let userData = useSelector((state) => state.userManagement.userData);
  let costCenterRowData = location.state;
  let taskRowDetails = useSelector((state) => state.userManagement.taskData);
  let requiredFieldTabWise= useSelector((state) => state.costCenter.requiredFields);
  const singleCCPayload = useSelector((state) => state.edit.payload);
  let singleCCPayloadAfterChange = useSelector((state) => state.edit.payload);
  console.log(
    "ccroewdata",
    new Date(`${iDs?.ValidFrom} GMT-0000`).toUTCString()
  );

  console.log("costCenterDetails", costCenterDetails);
  console.log(remarks, "Remarks");
  const costCenterViewData = useSelector(
    (state) => state.costCenter?.costCenterViewData
  );
  const requiredFields = useSelector(
    (state) => state.costCenter.requiredFields
  );
  const handleChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  console.log("costCenterViewData", costCenterViewData);
  var payload = {
    TaskId: taskRowDetails?.taskId ? taskRowDetails?.taskId : "",
    CostCenterHeaderID: iDs?.costCenterHeaderId ? iDs?.costCenterHeaderId : "",
    ControllingArea: costCenterRowData?.controllingAreaData?.newControllingArea
      ?.code
      ? costCenterRowData?.controllingAreaData?.newControllingArea?.code
      : "",
    // ControllingArea: "TZUS",
    Testrun: testrunStatus,
    Action: "I",
    //   costCenterRowData?.requestType === "Create"
    //     ? "I"
    //     : costCenterRowData?.requestType === "Change"
    //     ? "U"
    //     : "U",
    ReqCreatedBy: userData?.user_id,
    ReqCreatedOn: "",
    RequestStatus: "",
    CreationId: taskRowDetails?.subject ? taskRowDetails?.subject.slice(3) : "",
    EditId: "",
    DeleteId: "",
    MassCreationId: "",
    MassEditId: "",
    MassDeleteId: "",
    RequestType: "Create",
    MassRequestStatus: "",
    Remarks: remarks ? remarks : "",
    Toitem: [
      {
        CostCenterID: iDs?.costCenterId ? iDs?.costCenterId : "",
        // Costcenter: "Utility",
        ValidFrom: costCenterRowData?.validFromDate?.newValidFromDate
          ? "/Date(" +
          Date.parse(costCenterRowData?.validFromDate?.newValidFromDate) +
          ")/"
          : "",
        ValidTo: costCenterRowData?.validToDate?.newValidToDate
          ? "/Date(" +
          Date.parse(costCenterRowData?.validToDate?.newValidToDate) +
          ")/"
          : "",
        Costcenter:
          `${costCenterRowData?.companyCode?.newCompanyCode?.code}${costCenterRowData?.costCenterName?.newCostCenterName}`
            ? `${costCenterRowData?.companyCode?.newCompanyCode?.code}${costCenterRowData?.costCenterName?.newCostCenterName}`
            : "",
        // ValidFrom: displayData?.validFromDate?.newValidFromDate?"/Date(" + Date.parse(displayData?.validFromDate?.newValidFromDate) + ")/":"",
        // ValidTo: displayData?.validToDate?.newValidToDate?"/Date(" + Date.parse(displayData?.validToDate?.newValidToDate) + ")/":"",
        PersonInCharge: singleCCPayload?.PersonResponsible
          ? singleCCPayload?.PersonResponsible
          : "",
        CostcenterType: singleCCPayload?.CostCenterCategory
          ? singleCCPayload?.CostCenterCategory
          : "",
        CostctrHierGrp: "TUK1-PRODU",
        // CostctrHierGrp: singleCCPayload?.HierarchyArea?.code
        //   ? singleCCPayload?.HierarchyArea?.code
        //   : "",
        BusArea: singleCCPayload?.BusinessArea
          ? singleCCPayload?.BusinessArea
          : "",
        CompCode: costCenterRowData?.companyCode?.newCompanyCode?.code
          ? costCenterRowData?.companyCode?.newCompanyCode?.code
          : "TUK1",
        Currency: singleCCPayload?.Currency ? singleCCPayload?.Currency : "",
        ProfitCtr: singleCCPayload?.ProfitCenter
          ? singleCCPayload?.ProfitCenter
          : "",
        Name: singleCCPayload?.Name ? singleCCPayload?.Name : "",
        Descript: singleCCPayload?.Description
          ? singleCCPayload?.Description
          : "",
        PersonInChargeUser: singleCCPayload?.UserResponsible
          ? singleCCPayload?.UserResponsible
          : "",
        // PersonInChargeUser: singleCCPayload?.PersonResponsible
        //   ? singleCCPayload?.PersonResponsible
        //   : "",
        RecordQuantity: singleCCPayload?.RecordQuantity === true ? "X" : "",
        LockIndActualPrimaryCosts:
          singleCCPayload?.ActualPrimaryCosts === true ? "X" : "",
        LockIndPlanPrimaryCosts:
          singleCCPayload?.PlanPrimaryCosts === true ? "X" : "",
        LockIndActSecondaryCosts:
          singleCCPayload?.ActsecondaryCosts === true ? "X" : "",
        LockIndPlanSecondaryCosts:
          singleCCPayload?.PlanSecondaryCosts === true ? "X" : "",
        LockIndActualRevenues:
          singleCCPayload?.ActualRevenue === true ? "X" : "",
        LockIndPlanRevenues: singleCCPayload?.PlanRevenue === true ? "X" : "",
        LockIndCommitmentUpdate:
          singleCCPayload?.CommitmentUpdate === true ? "X" : "",
        // RecordQuantity: singleCCPayload?.RecordQuantity?singleCCPayload?.RecordQuantity:"false" ,
        // LockIndActualPrimaryCosts: singleCCPayload?.ActualPrimaryCosts?singleCCPayload?.ActualPrimaryCosts:"false" ,
        // LockIndPlanPrimaryCosts: singleCCPayload?.PlanPrimaryCosts?singleCCPayload?.PlanPrimaryCosts:"false" ,
        // LockIndActSecondaryCosts: singleCCPayload?.ActsecondaryCosts?singleCCPayload?.ActsecondaryCosts:"false" ,
        // LockIndPlanSecondaryCosts: singleCCPayload?.PlanSecondaryCosts?singleCCPayload?.PlanSecondaryCosts:"false" ,
        // LockIndActualRevenues: singleCCPayload?.ActualRevenue?singleCCPayload?.ActualRevenue:"false" ,
        // LockIndPlanRevenues: singleCCPayload?.PlanRevenue?singleCCPayload?.PlanRevenue:"false" ,
        // LockIndCommitmentUpdate:singleCCPayload?.CommitmentUpdate?singleCCPayload?.CommitmentUpdate:"false" ,
        ConditionTableUsage: "",
        Application: "",
        CstgSheet: singleCCPayload?.CostingSheet
          ? singleCCPayload?.CostingSheet
          : "",
        ActyIndepTemplate: singleCCPayload?.ActyIndepFromPlngTemp
          ? singleCCPayload?.ActyIndepFromPlngTemp
          : "",
        ActyDepTemplate: singleCCPayload?.ActyDepFromPlngTemp
          ? singleCCPayload?.ActyDepFromPlngTemp
          : "",
        AddrTitle: singleCCPayload?.Title ? singleCCPayload?.Title : "",
        AddrName1: singleCCPayload?.Name1 ? singleCCPayload?.Name1 : "",
        AddrName2: singleCCPayload?.Name2 ? singleCCPayload?.Name2 : "",
        AddrName3: singleCCPayload?.Name3 ? singleCCPayload?.Name3 : "",
        AddrName4: singleCCPayload?.Name4 ? singleCCPayload?.Name4 : "",
        AddrStreet: singleCCPayload?.Street ? singleCCPayload?.Street : "",
        AddrCity: singleCCPayload?.Location ? singleCCPayload?.Location : "",
        AddrDistrict: singleCCPayload?.District
          ? singleCCPayload?.District
          : "",
        AddrCountry: singleCCPayload?.CountryReg
          ? singleCCPayload?.CountryReg
          : "",
        AddrCountryIso: "",
        AddrTaxjurcode: singleCCPayload?.Jurisdiction
          ? singleCCPayload?.Jurisdiction
          : "",
        AddrPoBox: singleCCPayload?.POBox ? singleCCPayload?.POBox : "",
        AddrPostlCode: singleCCPayload?.PostalCode
          ? singleCCPayload?.PostalCode
          : "",
        AddrPobxPcd: singleCCPayload?.POBoxPostCod
          ? singleCCPayload?.POBoxPostCod
          : "",
        AddrRegion: singleCCPayload?.Region ? singleCCPayload?.Region : "",
        TelcoLangu: "",
        TelcoLanguIso: singleCCPayload?.LanguageKey
          ? singleCCPayload?.LanguageKey
          : "",
        TelcoTelephone: singleCCPayload?.Telephone1
          ? singleCCPayload?.Telephone1
          : "",
        TelcoTelephone2: singleCCPayload?.Telephone2
          ? singleCCPayload?.Telephone2
          : "",
        TelcoTelebox: singleCCPayload?.TeleboxNumber
          ? singleCCPayload?.TeleboxNumber
          : "",
        TelcoTelex: singleCCPayload?.TelexNumber
          ? singleCCPayload?.TelexNumber
          : "",
        TelcoFaxNumber: singleCCPayload?.FaxNumber
          ? singleCCPayload?.FaxNumber
          : "",
        TelcoTeletex: singleCCPayload?.TeletexNumber
          ? singleCCPayload?.TeletexNumber
          : "",
        TelcoPrinter: singleCCPayload?.PrinterDestination
          ? singleCCPayload?.PrinterDestination
          : "",
        TelcoDataLine: singleCCPayload?.DataLine
          ? singleCCPayload?.DataLine
          : "",
        ActyDepTemplateAllocCc: singleCCPayload?.ActyDepAllocTemp
          ? singleCCPayload?.ActyDepAllocTemp
          : "",
        ActyDepTemplateSk: singleCCPayload?.TempActStatKeyFigure
          ? singleCCPayload?.TempActStatKeyFigure
          : "",
        ActyIndepTemplateAllocCc: singleCCPayload?.ActyIndepAllocTemp
          ? singleCCPayload?.ActyIndepAllocTemp
          : "",
        ActyIndepTemplateSk: singleCCPayload?.TempActStatKeyFigure
          ? singleCCPayload?.TempActStatKeyFigure
          : "",
        AvcActive: false,
        AvcProfile: "",
        BudgetCarryingCostCtr: "",
        CurrencyIso: "",
        Department: singleCCPayload?.Department
          ? singleCCPayload?.Department
          : "",
        FuncArea: singleCCPayload?.FunctionalArea
          ? singleCCPayload?.FunctionalArea
          : "",
        FuncAreaFixAssigned: "",
        FuncAreaLong: "",
        Fund: "",
        FundFixAssigned: "",
        GrantFixAssigned: "",
        GrantId: "",
        JvEquityTyp: "",
        JvJibcl: "",
        JvJibsa: "",
        JvOtype: "",
        JvRecInd: "",
        JvVenture: "",
        Logsystem: "",
      },
    ],
  };

  console.log("taskData", taskData);
  console.log("taskRowDetails", taskRowDetails);

  const handleSnackBarCloseValidation = () => {
    setOpenSnackbarValidation(false);
  };
  const handleCheckValidationError = () => { //chiranjit
    return formValidator(
      singleCCPayloadAfterChange,
      requiredFieldTabWise,
      setFormValidationErrorItems
    );
  };

  const getRegion = (value) => {
    console.log("compcode", value);
    const hSuccess = (data) => {
      console.log("value", data);
      dispatch(setDropDown({ keyName: "Region", data: data.body }));
    };
    const hError = (error) => {
      console.log(error, "error in dojax");
    };
    doAjax(
      `/${destination_CostCenter}/data/getRegionBasedOnCountry?country=${value}`,
      "get",
      hSuccess,
      hError
    );
  };

  const checkErrorFields = (viewData) => {
    //console.log(viewData,"viewData===============")
    let error_field_arr = [];
    for (const section in viewData) {
      if (viewData.hasOwnProperty(section)) {
        for (const fieldGroup in viewData[section]) {
          if (viewData[section].hasOwnProperty(fieldGroup)) {
            const fields = viewData[section][fieldGroup];
            //console.log(fields,"fieldsview_data")
            for (const field of fields) {
              if (field.visibility === "0" || field.visibility === "Required") {
                console.log(field.fieldName, "field.fieldName");
                //let required_field_name = field.fieldName;
                let required_field_name = field.fieldName.replace(/\s/g, '');
                error_field_arr.push(required_field_name);
              }
            }
          }
        }
      }
      setErrorsFields((prevState) => ({
        ...prevState,
        error_field_arr,
      }));
    }
    //return result;
  };

  const getCostCenterDisplayData = () => {
    var payload = {
      id: costCenterRowData?.reqStatus ? costCenterRowData?.id : "",
      costCenter: costCenterRowData?.costCenter?.newCostCenter?.code
        ? costCenterRowData?.costCenter?.newCostCenter?.code
        : costCenterRowData?.costCenter
          ? costCenterRowData?.costCenter?.split(" ")[0]
          : "",
      controllingArea: costCenterRowData?.controllingAreaDataCopy
        ?.newControllingAreaCopyFrom?.code
        ? costCenterRowData?.controllingAreaDataCopy?.newControllingAreaCopyFrom
          ?.code
        : "",
      reqStatus: costCenterRowData?.reqStatus
        ? costCenterRowData?.reqStatus
        : "Approved",
      screenName: "Create",
    };

    const hSuccess = (data) => {
      const responseBody = data.body.viewData;
      const responseIDs = data.body;
      console.log("ccdata", data.body);
      dispatch(setCostCenterViewData(responseBody));
      getCurrency(
        responseBody["Basic Data"]["Basic Data"].find(
          (field) => field?.fieldName === "Company Code"
        ).value
      );
     
      getRegion(responseBody["Address"]["Address Data"].find(
        (field) => field?.fieldName === "Country/Reg"
      ).value)


      const categoryKeys = Object.keys(responseBody);
      setFactorsArray(categoryKeys);
      // setFactorsArray(...categoryKeys, 'Attachment & Comments')
      const attachment = ["Attachment & Documents"];
      factorsArray.concat(attachment);
      // factorsArray.push('Attachment & Comments')
      const mappedData = categoryKeys.map((category) => ({
        category,
        data: responseBody[category],
        setIsEditMode,
      }));

      setCostCenterDetails(mappedData);
      checkErrorFields(responseBody);
      setIds(responseIDs);
      console.log("mappedData", mappedData, iDs);
      // dispatch(setSingleCostCenterPayload(data));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/displayCostCenter`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  // const handleSetEditedPayload = () => {
  //   let activeTabName = factorsArray[activeStep];
  //   console.log("activeTabName", activeTabName, factorsArray);
  //   let viewDataArray = Object.entries(costCenterViewData);

  //   // viewDataArray.push(costCenterViewData);

  //   console.log("viewDataArray", viewDataArray);
  //   const toSetArray = {};
  //   viewDataArray.map((item) => {
  //     console.log("bottle", item[1]);
  //     let temp = Object.entries(item[1]); //Basic Data\
  //     console.log("notebook", temp);
  //     temp.forEach((fieldGroup) => {
  //       fieldGroup[1].forEach((field) => {
  //         toSetArray[
  //           field.fieldName
  //             .replaceAll("(", "")
  //             .replaceAll(")", "")
  //             .replaceAll("/", "")
  //             .replaceAll("-", "")
  //             .replaceAll(".", "")
  //             .split(" ")
  //             .join("")
  //         ] = field.value;
  //       });
  //     });
  //     return item;
  //   });
  //   console.log("toSetArray", toSetArray);
  //   dispatch(setPayloadWhole(toSetArray));
  // };


  const handleSetEditedPayload = () => {
    let activeTabName = factorsArray[activeStep];
    console.log("costCenterViewData", costCenterViewData);
    let viewDataArray = Object.entries(costCenterViewData);

    // viewDataArray.push(costCenterViewData);

    console.log("viewDataArray", viewDataArray);
    const toSetArray = {};
    viewDataArray.map((item) => {
      console.log("bottle", item[1]);
      let temp = Object.entries(item[1]); //Basic Data\
      console.log("notebook", temp);
      temp.forEach((fieldGroup) => {
        fieldGroup[1].forEach((field) => {
          toSetArray[
            field.fieldName
              .replaceAll("(", "")
              .replaceAll(")", "")
              .replaceAll("/", "")
              .replaceAll("-", "")
              .replaceAll(".", "")
              .split(" ")
              .join("")
          ] = field.value;
        });
      });
      return item;
    });
    console.log("toSetArray", toSetArray);
    dispatch(setPayloadWhole(toSetArray));
  };




// Loader and lookup for independent apis start
const [apiCount, setApiCount] = useState(0);
const fetchDynamicApiData = (keyName,endPoint) => {
  const hSuccess = (data) => {
    dispatch(setDropDown({ keyName: keyName, data: data.body }));
    // setIsLoading(false);
    setApiCount((prev)=>prev+1)
  };
  const hError = (error) => {
    console.log(error);
  };
  doAjax(
    `/${destination_CostCenter}/data/${endPoint}`,
    "get",
    hSuccess,
    hError
  );
}
const getAllLookups = () => {
  lookup?.costCenter?.map((item) => {
    fetchDynamicApiData(item?.keyName, item?.endPoint)
  })
}
const loaderCount=()=>{
if(apiCount==lookup?.costCenter?.length){
  setIsLoading(false);
}else{
  setIsLoading(true);
}
}
useEffect(() => {
  loaderCount(); 
}, [apiCount])
useEffect(() => {
  setCcNumber(idGenerator("CC"));
}, []);
// Loader and lookup for independent apis end


  useEffect(() => {
    getAllLookups();
    getCostCenterDisplayData();
    getHierarchyArea();
    getCompanyCode();
    getProfitCenter();
    // getCurrency();
  }, []);

  useEffect(() => {
    if (costCenterViewData.length === 0) {
      return;
    }
    handleSetEditedPayload();
  }, [costCenterViewData]);

  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };
  const handleMessageDialogNavigate = () => {
    navigate("/masterDataCockpit/costCenter");
  };
  const handleSnackBarClose = () => {
    if (validateFlag) {
      setopenSnackbar(false);
      setValidateFlag(false);
    } else {
      setopenSnackbar(false);
      navigate("/masterDataCockpit/costCenter");
    }
  };
  const handleBack = () => {
    //setActiveStep((prevActiveStep) => prevActiveStep - 1);
    setSubmitForReviewDisabled(true);
    const isValidation = handleCheckValidationError();
    
    if(isEditMode){
      if (isValidation) {
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
        dispatch(clearCostCenter());
      } else {
        handleSnackBarOpenValidation();
      }
    }else{
      setActiveStep((prevActiveStep) => prevActiveStep - 1);
        dispatch(clearCostCenter());
    }
  };
  const handleNext = () => {
    //setActiveStep((prevActiveStep) => prevActiveStep + 1);
    //setSubmitForReviewDisabled(true);
    const isValidation = handleCheckValidationError();
    
    if(isEditMode){
      if (isValidation) {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
        dispatch(clearCostCenter());
      } else {
        handleSnackBarOpenValidation();
      }
    }else{
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
        dispatch(clearCostCenter());
    }
  };
  const handleSnackBarOpenValidation = () => {
    setOpenSnackbarValidation(true);
  };
  const getCurrency = (value) => {
    console.log("compcode", value);
    const hSuccess = (data) => {
      console.log("value", data);
      dispatch(setDropDown({ keyName: "Currency", data: data.body }));
    };
    const hError = (error) => {
      console.log(error, "error in dojax");
    };
    doAjax(
      `/${destination_CostCenter}/data/getCurrency?companyCode=${value}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getHierarchyArea = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "HierarchyArea", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getHierarchyArea?controllingArea=${costCenterRowData?.controllingAreaDataCopy?.newControllingAreaCopyFrom?.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCompanyCode = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CompCode", data: data.body }));
      // getCurrency(data);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getCompanyCodeBasedOnControllingArea?controllingArea=${costCenterRowData?.controllingAreaDataCopy?.newControllingAreaCopyFrom?.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getProfitCenter = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ProfitCenter", data: data.body }));
      // getCurrency(data);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getProfitCenterAsPerControllingArea?controllingArea=${costCenterRowData?.controllingAreaDataCopy?.newControllingAreaCopyFrom?.code}`,
      "get",
      hSuccess,
      hError
    );
  };

  const duplicateCheck = () => {
    let selectedControllingArea =
      costCenterRowData?.controllingAreaDataCopy?.newControllingAreaCopyFrom
        ?.code;
    let selectedCostCenterName = `${costCenterRowData?.companyCode?.newCompanyCode?.code}${costCenterRowData?.costCenter?.costCenterName?.newCostCenterName}`;
    // let result = "";
    let result = selectedControllingArea.concat("$$", selectedCostCenterName);
    console.log("sendNewCostCenterData", result);
    // let ctrlAreaCCToCheck = (sendNewCostCenterData?.controllingAreaData?.newControllingArea).join(sendNewCostCenterData?.costCenterName?.newCostCenterName)
    // console.log("ctrlAreaCCToCheck",(sendNewCostCenterData?.controllingAreaData?.newControllingArea).join(sendNewCostCenterData?.costCenterName?.newCostCenterName));
    const hSuccess = (data) => {
      console.log("dupli", data);
      if (data.body) {
        setIsEditMode(true);
        setIsDisplayMode(false);
      } else {
        setDupliDialog(true);
      }
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/alter/fetchCoAreaCCDupliChk?ctrlAreaCCToCheck=${result}`,
      "get",
      hSuccess,
      hError
    );
  };
  const onEdit = () => {
    // duplicateCheck();
    setIsEditMode(true);
    setIsDisplayMode(false);
  };

  const onCostCenterSubmitChange = () => {
    setIsLoading(true);
    handleCostCenterSubmitChange();
  };
  const onCostCenterSubmitCreate = () => {
    setIsLoading(true);
    handleCostCenterSubmitCreate();
  };

  const onCostCenterReviewCreateWithCopy = () => {
    setIsLoading(true);
    handleCostCenterReviewCreateWithCopy();
  };
  const onCostCenterReviewCreate = () => {
    setIsLoading(true);
    handleCostCenterReviewCreate();
  };

  const onCostCenterSaveAsDraftChange = () => {
   
    handleCostCenterSaveAsDraftChange();
  };

  const onCostCenterCorrectionChange = () => {
    setIsLoading(true);
    handleCostCenterCorrectionChange();
  };
  const onCostCenterCorrectionCreate = () => {
    setIsLoading(true);
    handleCostCenterCorrectionCreate();
  };

  const onCostCenterApproveChange = () => {
    setIsLoading(true);
    handleCostCenterApproveChange();
  };
  const onCostCenterApproveCreate = () => {
    setIsLoading(true);
    handleCostCenterApproveCreate();
  };
  const onCostCenterRereview = () => {
    setIsLoading(true);
    handleCostCenterRereview();
  };
  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };
  const handleSnackBarOpen = () => {
    setopenSnackbar(true);
  };
  
  const onValidateCostCenter = () => {
    setBlurLoading(true);
    // Define duplicateCheckPayload outside of the onValidateCostCenter function
    const duplicateCheckPayload = {
      coArea: costCenterRowData?.controllingAreaData?.newControllingArea?.code
        ? costCenterRowData?.controllingAreaData?.newControllingArea?.code
        : "",
      name: singleCCPayload?.Name ? singleCCPayload?.Name.toUpperCase() : "",
    };
    const hSuccess = (data) => {
      // setIsLoading(false);
      if (data.statusCode === 201) {
       // Handle success
       setMessageDialogTitle("Create");
       setMessageDialogTitle("Create");
       setMessageDialogMessage(
         `All Data has been Validated. Cost Center can be Sent for Review`
       );
       setMessageDialogSeverity("success");
       setMessageDialogOK(false);
       setsuccessMsg(true);
       handleSnackBarOpen();
       setMessageDialogExtra(true);
       // setIsLoading(false);
       setValidateFlag(true);

       // Now, make the duplicate check API call
       // Ensure that the conditions for making the duplicate check API call are met
       if (
         duplicateCheckPayload.coArea !== "" ||
         duplicateCheckPayload.name !== ""
       ) {
         doAjax(
           `/${destination_CostCenter}/alter/fetchCCDescriptionDupliChk`,
           "post",
           hDuplicateCheckSuccess,
           hDuplicateCheckError,
           duplicateCheckPayload
         );
       }
      } else {
          // Handle error
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          setMessageDialogMessage(
            `${data?.body?.message[0] ? data?.body?.message[0] : data?.body?.value
            }`
          );
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          setBlurLoading(false);
          // setIsLoading(false);
      }
    };

    const hDuplicateCheckSuccess = (data) => {
      // Handle success of duplicate check
      if (
        data.body.length === 0 ||
        !data.body.some(
          (item) => item.toUpperCase() === duplicateCheckPayload.name
        )
      ) {
        // No direct match, enable the "Submit for Review" button
        setBlurLoading(false);
        setSubmitForReviewDisabled(false);
        setTestrunStatus(false);
      } else {
        // Handle direct match
        setBlurLoading(false);
        setMessageDialogTitle("Duplicate Check");
        setsuccessMsg(false);
        setMessageDialogMessage(
          `There is a direct match for the Cost Center name. Please change the name.`
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setSubmitForReviewDisabled(true);
      }
    };

    const hDuplicateCheckError = (error) => {
      // Handle error of duplicate check
      console.log(error);
    };

    const hError = (error) => {
      console.log(error);
    };

    // Call the main API for validation
    doAjax(
      `/${destination_CostCenter}/alter/validateCostCenter`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const onCostCenterCorrection = () => {
    if (
      userData?.role === "MDM Steward" &&
      (costCenterRowData?.requestType === "Create" ||
        taskRowDetails?.processDesc === "Create")
    ) {
      setIsLoading(true);
      handleCorrectionMDMCreate();
    } else if (
      userData?.role === "MDM Steward" &&
      (costCenterRowData?.requestType === "Change" ||
        taskRowDetails?.processDesc === "Change")
    ) {
      setIsLoading(true);
      handleCorrectionMDMChange();
    } else if (
      userData?.role === "Approver" &&
      (costCenterRowData?.requestType === "Create" ||
        taskRowDetails?.processDesc === "Create")
    ) {
      setIsLoading(true);
      handleCorrectionApproverCreate();
    } else if (
      userData?.role === "Approver" &&
      (costCenterRowData?.requestType === "Change" ||
        taskRowDetails?.processDesc === "Change")
    ) {
      setIsLoading(true);
      handleCorrectionApproverChange();
    }
  };
  const handleCorrectionMDMCreate = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Cost Center Submitted for Correction with ID NCR${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Cost Center for Correction");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    console.log("remarkssssssssss", remarks);
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_CostCenter}/alter/costCenterSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const handleCorrectionMDMChange = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Cost Center Submitted for Correction with ID NCR${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Cost Center for Correction");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    console.log("hsdfjgdh", payload);
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_CostCenter}/alter/changeCostCenterSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCorrectionApproverCreate = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Cost Center Submitted for Correction with ID NCR${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Cost Center for Correction");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_CostCenter}/alter/costCenterSendForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const handleCorrectionApproverChange = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Cost Center Submitted for Correction with ID NCR${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Cost Center for Correction");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    console.log("remarksssaaaa", remarks);
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_CostCenter}/alter/changeCostCenterSendForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const attachmentColumns = [
    {
      field: "id",
      headerName: "Document ID",
      flex: 1,
      hide: true,
    },
    {
      field: "docType",
      headerName: "Type",
      flex: 1,
    },
    {
      field: "docName",
      headerName: "Document Name",
      flex: 1,
    },
    {
      field: "uploadedOn",
      headerName: "Uploaded On",
      flex: 1,
      align: "center",
      headerAlign: "center",
    },
    {
      field: "uploadedBy",
      headerName: "Uploaded By",
      sortable: false,
      flex: 1,
    },
    {
      field: "action",
      headerName: "Action",
      sortable: false,
      filterable: false,
      align: "center",
      headerAlign: "center",
      flex: 1,
      renderCell: (cellValues) => {
        return (
          <>
            <MatView index={cellValues.row.id} name={cellValues.row.docName} />
            <MatDownload
              index={cellValues.row.id}
              name={cellValues.row.docName}
            />
          </>
        );
      },
    },
  ];


 

  // const tabContents = factorsArray
  //   .map((item) => {
  //     // const ddata = Object.entries(allTabs).filter((i) => {
  //     //   return reference[i[0]]?.split(" ")[0] == item?.split(" ")[0];
  //     // })[0]?.[1];

  //     const mdata = costCenterDetails.filter(
  //       (ii) => ii.category?.split(" ")[0] == item?.split(" ")[0]
  //     );
  //     if (mdata.length != 0) {
  //       return { category: item?.split(" ")[0], data: mdata[0].data };
  //     }
  //   })
  //   .map((categoryData, index) => {
  //     console.log("categoryy", categoryData.category);
  //     if (categoryData?.category == "Basic" && activeStep == 0) {
  //       return [
  //         <Grid
  //           key={categoryData.category}
  //           container
  //           item
  //           md={12}
  //           sx={{
  //             backgroundColor: "white",
  //             maxHeight: "max-content",
  //             height: "max-content",
  //             // borderRadius: "8px",
  //             // border: "1px solid #E0E0E0",
  //             mt: 1,
  //             // boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
  //             // padding: "10px",
  //             mb: 1,
  //           }}
  //         >
  //           {Object.keys(categoryData.data).map((fieldGroup) => (
  //             <Grid
  //               key={fieldGroup}
  //               item
  //               md={12}
  //               sx={{
  //                 backgroundColor: "white",
  //                 maxHeight: "max-content",
  //                 height: "max-content",
  //                 borderRadius: "8px",
  //                 border: "1px solid #E0E0E0",
  //                 mt: 0.25,
  //                 boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
  //                 ...container_Padding,
  //                 // ...container_columnGap,
  //               }}
  //             >
  //               <Typography
  //                 sx={{
  //                   fontSize: "12px",
  //                   fontWeight: "700",
  //                   margin: "0px !important",
  //                 }}
  //               >
  //                 {fieldGroup}
  //               </Typography>
  //               <Box sx={{ width: "100%" }}>
  //                 <CardContent
  //                   sx={{
  //                     padding: "0",
  //                     paddingBottom: "0 !important",
  //                     paddingTop: "10px !important",
  //                   }}
  //                 >
  //                   <Grid
  //                     container
  //                     style={{
  //                       display: "grid",
  //                       gridTemplateColumns: "repeat(6,1fr)",
  //                       gap: "15px",
  //                     }}
  //                     justifyContent="space-between"
  //                     alignItems="flex-start"
  //                     md={12}
  //                   >
  //                     {categoryData.data[fieldGroup].map((field) => {
  //                       console.log("fieldDatatttt", field);
  //                       return (
  //                         <EditableFieldForCostCenter
  //                           // key={field.fieldName}
  //                           length={field.maxLength}
  //                           label={field.fieldName}
  //                           data={singleCCPayload}
  //                           value={field.value}
  //                           visibility={field.visibility}
  //                           onSave={(newValue) =>
  //                             handleFieldSave(field.fieldName, newValue)
  //                           }
  //                           isEditMode={isEditMode}
  //                           // isExtendMode={isExtendMode}
  //                           type={field.fieldType}
  //                           field={field} // Update the type as needed
  //                         />
  //                       );
  //                     })}
  //                   </Grid>
  //                 </CardContent>
  //               </Box>
  //             </Grid>
  //           ))}
  //         </Grid>,
  //       ];
  //     } else if (categoryData?.category == "Control" && activeStep == 1) {
  //       return [
  //         <Grid
  //           key={categoryData.category}
  //           container
  //           item
  //           md={12}
  //           sx={{
  //             backgroundColor: "white",
  //             maxHeight: "max-content",
  //             height: "max-content",
  //             mt: 1,
  //             mb: 1,
  //           }}
  //         >
  //           {Object.keys(categoryData.data).map((fieldGroup) => (
  //             <Grid
  //               key={fieldGroup}
  //               item
  //               md={12}
  //               sx={{
  //                 backgroundColor: "white",
  //                 maxHeight: "max-content",
  //                 height: "max-content",
  //                 borderRadius: "8px",
  //                 border: "1px solid #E0E0E0",
  //                 mt: 0.25,
  //                 boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
  //                 ...container_Padding,
  //                 // ...container_columnGap,
  //               }}
  //             >
  //               <Typography
  //                 sx={{
  //                   fontSize: "12px",
  //                   fontWeight: "700",
  //                   margin: "0px !important",
  //                 }}
  //               >
  //                 {fieldGroup}
  //               </Typography>
  //               <Box sx={{ width: "100%" }}>
  //                 <CardContent>
  //                   <Grid
  //                     container
  //                     style={{
  //                       display: "grid",
  //                       gridTemplateColumns: "repeat(6,1fr)",
  //                       gap: "15px",
  //                     }}
  //                     justifyContent="space-between"
  //                     alignItems="flex-start"
  //                     md={12}
  //                   >
  //                     {categoryData.data[fieldGroup].map((field) => (
  //                       <EditableFieldForCostCenter
  //                         key={field.fieldName}
  //                         data={singleCCPayload}
  //                         label={field.fieldName}
  //                         value={field?.value === "X" ? true : false}
  //                         onSave={(newValue) =>
  //                           handleFieldSave(field.fieldName, newValue)
  //                         }
  //                         visibility={field.visibility}
  //                         isEditMode={isEditMode}
  //                         type={field.fieldType} // Update the type as needed
  //                       />
  //                     ))}
  //                   </Grid>
  //                 </CardContent>
  //               </Box>
  //             </Grid>
  //           ))}
  //         </Grid>,
  //       ];
  //     } else if (categoryData?.category == "Templates" && activeStep == 2) {
  //       return [
  //         <Grid
  //           key={categoryData.category}
  //           container
  //           item
  //           md={12}
  //           sx={{
  //             backgroundColor: "white",
  //             maxHeight: "max-content",
  //             height: "max-content",
  //             // borderRadius: "8px",
  //             // border: "1px solid #E0E0E0",
  //             mt: 1,
  //             // boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
  //             // padding: "10px",
  //             mb: 1,
  //           }}
  //         >
  //           {Object.keys(categoryData.data).map((fieldGroup) => (
  //             <Grid
  //               key={fieldGroup}
  //               item
  //               md={12}
  //               sx={{
  //                 backgroundColor: "white",
  //                 maxHeight: "max-content",
  //                 height: "max-content",
  //                 borderRadius: "8px",
  //                 border: "1px solid #E0E0E0",
  //                 mt: 0.25,
  //                 boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
  //                 ...container_Padding,
  //                 // ...container_columnGap,
  //               }}
  //             >
  //               <Typography
  //                 sx={{
  //                   fontSize: "12px",
  //                   fontWeight: "700",
  //                   margin: "0px !important",
  //                 }}
  //               >
  //                 {fieldGroup}
  //               </Typography>
  //               <Box sx={{ width: "100%" }}>
  //                 <CardContent>
  //                   <Grid
  //                     container
  //                     style={{
  //                       display: "grid",
  //                       gridTemplateColumns: "repeat(6,1fr)",
  //                       gap: "15px",
  //                     }}
  //                     justifyContent="space-between"
  //                     alignItems="flex-start"
  //                     md={12}
  //                   >
  //                     {categoryData.data[fieldGroup].map((field) => (
  //                       <EditableFieldForCostCenter
  //                         key={field.fieldName}
  //                         label={field.fieldName}
  //                         data={singleCCPayload}
  //                         value={field.value}
  //                         onSave={(newValue) =>
  //                           handleFieldSave(field.fieldName, newValue)
  //                         }
  //                         visibility={field.visibility}
  //                         isEditMode={isEditMode}
  //                         // isExtendMode={isExtendMode}
  //                         type={field.fieldType} // Update the type as needed
  //                       />
  //                     ))}
  //                   </Grid>
  //                 </CardContent>
  //               </Box>
  //             </Grid>
  //           ))}
  //         </Grid>,
  //       ];
  //     } else if (categoryData?.category == "Address" && activeStep == 3) {
  //       return [
  //         <Grid
  //           key={categoryData.category}
  //           container
  //           item
  //           md={12}
  //           sx={{
  //             backgroundColor: "white",
  //             maxHeight: "max-content",
  //             height: "max-content",
  //             // borderRadius: "8px",
  //             // border: "1px solid #E0E0E0",
  //             mt: 1,
  //             // boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
  //             // padding: "10px",
  //             mb: 1,
  //           }}
  //         >
  //           {Object.keys(categoryData.data).map((fieldGroup) => (
  //             <Grid
  //               key={fieldGroup}
  //               item
  //               md={12}
  //               sx={{
  //                 backgroundColor: "white",
  //                 maxHeight: "max-content",
  //                 height: "max-content",
  //                 borderRadius: "8px",
  //                 border: "1px solid #E0E0E0",
  //                 mt: 0.25,
  //                 boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
  //                 ...container_Padding,
  //                 // ...container_columnGap,
  //               }}
  //             >
  //               <Typography
  //                 sx={{
  //                   fontSize: "12px",
  //                   fontWeight: "700",
  //                   margin: "0px !important",
  //                 }}
  //               >
  //                 {fieldGroup}
  //               </Typography>
  //               <Box sx={{ width: "100%" }}>
  //                 <CardContent>
  //                   <Grid
  //                     container
  //                     style={{
  //                       display: "grid",
  //                       gridTemplateColumns: "repeat(6,1fr)",
  //                       gap: "15px",
  //                     }}
  //                     justifyContent="space-between"
  //                     alignItems="flex-start"
  //                     md={12}
  //                   >
  //                     {categoryData.data[fieldGroup].map((field) => (
  //                       <EditableFieldForCostCenter
  //                         key={field.fieldName}
  //                         label={field.fieldName}
  //                         data={singleCCPayload}
  //                         value={field.value}
  //                         onSave={(newValue) =>
  //                           handleFieldSave(field.fieldName, newValue)
  //                         }
  //                         visibility={field.visibility}
  //                         isEditMode={isEditMode}
  //                         // isExtendMode={isExtendMode}
  //                         type={field.fieldType} // Update the type as needed
  //                       />
  //                     ))}
  //                   </Grid>
  //                 </CardContent>
  //               </Box>
  //             </Grid>
  //           ))}
  //         </Grid>,
  //       ];
  //     } else if (categoryData?.category == "Communication" && activeStep == 4) {
  //       return [
  //         <Grid
  //           key={categoryData.category}
  //           container
  //           item
  //           md={12}
  //           sx={{
  //             backgroundColor: "white",
  //             maxHeight: "max-content",
  //             height: "max-content",
  //             // borderRadius: "8px",
  //             // border: "1px solid #E0E0E0",
  //             mt: 1,
  //             // boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
  //             // padding: "10px",
  //             mb: 1,
  //           }}
  //         >
  //           {Object.keys(categoryData.data).map((fieldGroup) => (
  //             <Grid
  //               key={fieldGroup}
  //               item
  //               md={12}
  //               sx={{
  //                 backgroundColor: "white",
  //                 maxHeight: "max-content",
  //                 height: "max-content",
  //                 borderRadius: "8px",
  //                 border: "1px solid #E0E0E0",
  //                 mt: 0.25,
  //                 boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
  //                 ...container_Padding,
  //                 // ...container_columnGap,
  //               }}
  //             >
  //               <Typography
  //                 sx={{
  //                   fontSize: "12px",
  //                   fontWeight: "700",
  //                   margin: "0px !important",
  //                 }}
  //               >
  //                 {fieldGroup}
  //               </Typography>
  //               <Box sx={{ width: "100%" }}>
  //                 <CardContent>
  //                   <Grid
  //                     container
  //                     style={{
  //                       display: "grid",
  //                       gridTemplateColumns: "repeat(6,1fr)",
  //                       gap: "15px",
  //                     }}
  //                     justifyContent="space-between"
  //                     alignItems="flex-start"
  //                     md={12}
  //                   >
  //                     {categoryData.data[fieldGroup].map((field) => (
  //                       <EditableFieldForCostCenter
  //                         key={field.fieldName}
  //                         data={singleCCPayload}
  //                         label={field.fieldName}
  //                         value={field.value}
  //                         onSave={(newValue) =>
  //                           handleFieldSave(field.fieldName, newValue)
  //                         }
  //                         visibility={field.visibility}
  //                         isEditMode={isEditMode}
  //                         // isExtendMode={isExtendMode}
  //                         type={field.fieldType} // Update the type as needed
  //                       />
  //                     ))}
  //                   </Grid>
  //                 </CardContent>
  //               </Box>
  //             </Grid>
  //           ))}
  //         </Grid>,
  //       ];
  //     } else if (categoryData?.category == "Attachments" && activeStep == 5) {
  //       return [
  //         // <Grid
  //         //   key={categoryData.category}
  //         //   container
  //         //   item
  //         //   md={12}
  //         //   sx={{
  //         //     backgroundColor: "white",
  //         //     maxHeight: "max-content",
  //         //     height: "max-content",
  //         //     // borderRadius: "8px",
  //         //     // border: "1px solid #E0E0E0",
  //         //     mt: 1,
  //         //     // boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
  //         //     // padding: "10px",
  //         //     mb: 1,
  //         //   }}
  //         // >
  //         <>
  //         {!isEditMode ?
  //         (<Card sx={{ padding: "1rem 1rem 0rem 1rem" }}>
  //           <Grid
  //             container
  //             sx={{
  //               display: "flex",
  //               justifyContent: "space-between",
  //             }}
  //           >
  //             <Typography variant="h6">
  //               <strong>Attachments</strong>
  //             </Typography>
  //           </Grid>
  //           {Boolean(attachments.length) && (
  //             <ReusableTable
  //               width="100%"
  //               rows={attachments}
  //               columns={attachmentColumns}
  //               hideFooter={false}
  //               getRowIdValue={"id"}
  //               disableSelectionOnClick={true}
  //               stopPropagation_Column={"action"}
  //             />
  //           )}
  //           {!Boolean(attachments.length) && (
  //             <Typography variant="body2">No Attachments Found</Typography>
  //           )}
  //           <br />
  //           <Typography variant="h6">Comments</Typography>
  //           {Boolean(comments.length) && (
  //             <Timeline
  //               sx={{
  //                 [`& .${timelineItemClasses.root}:before`]: {
  //                   flex: 0,
  //                   padding: 0,
  //                 },
  //               }}
  //             >
  //               {comments.map((comment) => (
  //                 <TimelineItem>
  //                   <TimelineSeparator>
  //                     <TimelineDot>
  //                       <CheckCircleOutlineOutlined sx={{ color: "#757575" }} />
  //                     </TimelineDot>
  //                     <TimelineConnector />
  //                   </TimelineSeparator>
  //                   <TimelineContent sx={{ py: "12px", px: 2 }}>
  //                     <Card
  //                       elevation={0}
  //                       sx={{
  //                         border: 1,
  //                         borderColor: "#C4C4C4",
  //                         borderRadius: "8px",
  //                         width: "650px",
  //                       }}
  //                     >
  //                       <Box sx={{ padding: "1rem" }}>
  //                         <Stack spacing={1}>
  //                           <Grid
  //                             sx={{
  //                               display: "flex",
  //                               justifyContent: "space-between",
  //                             }}
  //                           >
  //                             <Typography
  //                               sx={{
  //                                 textAlign: "right",
  //                                 color: " #757575",
  //                                 fontWeight: "500",
  //                                 fontSize: "12px",
  //                               }}
  //                             >
  //                               {moment(comment.createdAt).format(
  //                                 "DD MMM YYYY"
  //                               )}
  //                             </Typography>
  //                           </Grid>

  //                           <Typography
  //                             sx={{
  //                               fontSize: "12px",

  //                               color: " #757575",
  //                               fontWeight: "500",
  //                             }}
  //                           >
  //                             {comment.user}
  //                           </Typography>
  //                           <Typography
  //                             sx={{
  //                               fontSize: "12px",
  //                               color: "#1D1D1D",
  //                               fontWeight: "600",
  //                             }}
  //                           >
  //                             {comment.comment}
  //                           </Typography>
  //                         </Stack>
  //                       </Box>
  //                     </Card>
  //                   </TimelineContent>
  //                 </TimelineItem>
  //               ))}
  //             </Timeline>
  //           )}
  //           {!Boolean(comments.length) && (
  //             <Typography variant="body2">No Comments Found</Typography>
  //           )}
  //           <br />
  //         </Card>
          
  //         )
  //           : 
  //           <>
  //           <ReusableAttachementAndComments
  //           title="CostCenter"
  //           useMetaData={false}
  //           artifactId={ccNumber}
  //           artifactName="CostCenter"
  //         />
  //         <Card sx={{ padding: "1rem 1rem 0rem 1rem" }}>
  //           <Grid
  //             container
  //             sx={{
  //               display: "flex",
  //               justifyContent: "space-between",
  //             }}
  //           >
  //             <Typography variant="h6">
  //               <strong>Attachments</strong>
  //             </Typography>
  //           </Grid>
  //           {Boolean(attachments.length) && (
  //             <ReusableTable
  //               width="100%"
  //               rows={attachments}
  //               columns={attachmentColumns}
  //               hideFooter={false}
  //               getRowIdValue={"id"}
  //               disableSelectionOnClick={true}
  //               stopPropagation_Column={"action"}
  //             />
  //           )}
  //           {!Boolean(attachments.length) && (
  //             <Typography variant="body2">No Attachments Found</Typography>
  //           )}
  //           <br />
  //           <Typography variant="h6">Comments</Typography>
  //           {Boolean(comments.length) && (
  //             <Timeline
  //               sx={{
  //                 [`& .${timelineItemClasses.root}:before`]: {
  //                   flex: 0,
  //                   padding: 0,
  //                 },
  //               }}
  //             >
  //               {comments.map((comment) => (
  //                 <TimelineItem>
  //                   <TimelineSeparator>
  //                     <TimelineDot>
  //                       <CheckCircleOutlineOutlined sx={{ color: "#757575" }} />
  //                     </TimelineDot>
  //                     <TimelineConnector />
  //                   </TimelineSeparator>
  //                   <TimelineContent sx={{ py: "12px", px: 2 }}>
  //                     <Card
  //                       elevation={0}
  //                       sx={{
  //                         border: 1,
  //                         borderColor: "#C4C4C4",
  //                         borderRadius: "8px",
  //                         width: "650px",
  //                       }}
  //                     >
  //                       <Box sx={{ padding: "1rem" }}>
  //                         <Stack spacing={1}>
  //                           <Grid
  //                             sx={{
  //                               display: "flex",
  //                               justifyContent: "space-between",
  //                             }}
  //                           >
  //                             <Typography
  //                               sx={{
  //                                 textAlign: "right",
  //                                 color: " #757575",
  //                                 fontWeight: "500",
  //                                 fontSize: "12px",
  //                               }}
  //                             >
  //                               {moment(comment.createdAt).format(
  //                                 "DD MMM YYYY"
  //                               )}
  //                             </Typography>
  //                           </Grid>

  //                           <Typography
  //                             sx={{
  //                               fontSize: "12px",

  //                               color: " #757575",
  //                               fontWeight: "500",
  //                             }}
  //                           >
  //                             {comment.user}
  //                           </Typography>
  //                           <Typography
  //                             sx={{
  //                               fontSize: "12px",
  //                               color: "#1D1D1D",
  //                               fontWeight: "600",
  //                             }}
  //                           >
  //                             {comment.comment}
  //                           </Typography>
  //                         </Stack>
  //                       </Box>
  //                     </Card>
  //                   </TimelineContent>
  //                 </TimelineItem>
  //               ))}
  //             </Timeline>
  //           )}
  //           {!Boolean(comments.length) && (
  //             <Typography variant="body2">No Comments Found</Typography>
  //           )}
  //           <br />
  //         </Card>
  //         </>}
  //         </>
  //         // </Grid>,
  //       ];
  //     }
  //   });
  const tabContents = factorsArray
  .map((item) => {
    // const ddata = Object.entries(allTabs).filter((i) => {
    //   return reference[i[0]]?.split(" ")[0] == item?.split(" ")[0];
    // })[0]?.[1];

    const mdata = costCenterDetails.filter(
      (ii) => ii.category?.split(" ")[0] == item?.split(" ")[0]
    );
    if (mdata.length != 0) {
      return { category: item?.split(" ")[0], data: mdata[0].data };
    }
  })
  .map((categoryData, index) => {
    //console.log("categoryy", categoryData.category);
    if (categoryData?.category == "Basic" && activeStep == 0) {
      return [
        <Grid
          key={categoryData.category}
          container
          item
          md={12}
          sx={{
            backgroundColor: "white",
            maxHeight: "max-content",
            height: "max-content",
            // borderRadius: "8px",
            // border: "1px solid #E0E0E0",
            mt: 1,
            // boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
            // padding: "10px",
            mb: 1,
          }}
        >
          {Object.keys(categoryData.data).map((fieldGroup) => (
            <Grid
              key={fieldGroup}
              item
              md={12}
              sx={{
                backgroundColor: "white",
                maxHeight: "max-content",
                height: "max-content",
                borderRadius: "8px",
                border: "1px solid #E0E0E0",
                mt: 0.25,
                boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                ...container_Padding,
                // ...container_columnGap,
              }}
            >
              <Typography
                sx={{
                  fontSize: "12px",
                  fontWeight: "700",
                  margin: "0px !important",
                }}
              >
                {fieldGroup}
              </Typography>
              <Box sx={{ width: "100%" }}>
                <CardContent
                  sx={{
                    padding: "0",
                    paddingBottom: "0 !important",
                    paddingTop: "10px !important",
                  }}
                >
                  <Grid
                    container
                    style={{
                      display: "grid",
                      gridTemplateColumns: "repeat(6,1fr)",
                      gap: "15px",
                    }}
                    justifyContent="space-between"
                    alignItems="flex-start"
                    md={12}
                  >
                    {categoryData.data[fieldGroup].map((field) => {
                      console.log("fieldDatatttt", field);
                      return (
                        <EditableFieldForCostCenter
                          // key={field.fieldName}
                          length={field.maxLength}
                          label={field.fieldName}
                          data={singleCCPayload}
                          value={field.value}
                          visibility={field.visibility}
                          onSave={(newValue) =>
                            handleFieldSave(field.fieldName, newValue)
                          }
                          isEditMode={isEditMode}
                          // isExtendMode={isExtendMode}
                          type={field.fieldType}
                          field={field} // Update the type as needed
                          taskRequestId={costCenterRowData?.requestId}
                        />
                      );
                    })}
                  </Grid>
                </CardContent>
              </Box>
            </Grid>
          ))}
        </Grid>,
      ];
    } else if (categoryData?.category == "Control" && activeStep == 1) {
      return [
        <Grid
          key={categoryData.category}
          container
          item
          md={12}
          sx={{
            backgroundColor: "white",
            maxHeight: "max-content",
            height: "max-content",
            mt: 1,
            mb: 1,
          }}
        >
          {Object.keys(categoryData.data).map((fieldGroup) => (
            <Grid
              key={fieldGroup}
              item
              md={12}
              sx={{
                backgroundColor: "white",
                maxHeight: "max-content",
                height: "max-content",
                borderRadius: "8px",
                border: "1px solid #E0E0E0",
                mt: 0.25,
                boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                ...container_Padding,
                // ...container_columnGap,
              }}
            >
              <Typography
                sx={{
                  fontSize: "12px",
                  fontWeight: "700",
                  margin: "0px !important",
                }}
              >
                {fieldGroup}
              </Typography>
              <Box sx={{ width: "100%" }}>
                <CardContent>
                  <Grid
                    container
                    style={{
                      display: "grid",
                      gridTemplateColumns: "repeat(6,1fr)",
                      gap: "15px",
                    }}
                    justifyContent="space-between"
                    alignItems="flex-start"
                    md={12}
                  >
                    {categoryData.data[fieldGroup].map((field) => (
                      <EditableFieldForCostCenter
                        key={field.fieldName}
                        data={singleCCPayload}
                        label={field.fieldName}
                        value={field?.value === "X" ? true : false}
                        onSave={(newValue) =>
                          handleFieldSave(field.fieldName, newValue)
                        }
                        visibility={field.visibility}
                        isEditMode={isEditMode}
                        type={field.fieldType} // Update the type as needed
                        taskRequestId={costCenterRowData?.requestId}
                      />
                    ))}
                  </Grid>
                </CardContent>
              </Box>
            </Grid>
          ))}
        </Grid>,
      ];
    } else if (categoryData?.category == "Templates" && activeStep == 2) {
      return [
        <Grid
          key={categoryData.category}
          container
          item
          md={12}
          sx={{
            backgroundColor: "white",
            maxHeight: "max-content",
            height: "max-content",
            // borderRadius: "8px",
            // border: "1px solid #E0E0E0",
            mt: 1,
            // boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
            // padding: "10px",
            mb: 1,
          }}
        >
          {Object.keys(categoryData.data).map((fieldGroup) => (
            <Grid
              key={fieldGroup}
              item
              md={12}
              sx={{
                backgroundColor: "white",
                maxHeight: "max-content",
                height: "max-content",
                borderRadius: "8px",
                border: "1px solid #E0E0E0",
                mt: 0.25,
                boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                ...container_Padding,
                // ...container_columnGap,
              }}
            >
              <Typography
                sx={{
                  fontSize: "12px",
                  fontWeight: "700",
                  margin: "0px !important",
                }}
              >
                {fieldGroup}
              </Typography>
              <Box sx={{ width: "100%" }}>
                <CardContent>
                  <Grid
                    container
                    style={{
                      display: "grid",
                      gridTemplateColumns: "repeat(6,1fr)",
                      gap: "15px",
                    }}
                    justifyContent="space-between"
                    alignItems="flex-start"
                    md={12}
                  >
                    {categoryData.data[fieldGroup].map((field) => (
                      <EditableFieldForCostCenter
                        key={field.fieldName}
                        label={field.fieldName}
                        data={singleCCPayload}
                        value={field.value}
                        onSave={(newValue) =>
                          handleFieldSave(field.fieldName, newValue)
                        }
                        isEditMode={isEditMode}
                        // isExtendMode={isExtendMode}
                        visibility={field.visibility}
                        type={field.fieldType} // Update the type as needed
                        taskRequestId={costCenterRowData?.requestId}
                      />
                    ))}
                  </Grid>
                </CardContent>
              </Box>
            </Grid>
          ))}
        </Grid>,
      ];
    } else if (categoryData?.category == "Address" && activeStep == 3) {
      return [
        <Grid
          key={categoryData.category}
          container
          item
          md={12}
          sx={{
            backgroundColor: "white",
            maxHeight: "max-content",
            height: "max-content",
            // borderRadius: "8px",
            // border: "1px solid #E0E0E0",
            mt: 1,
            // boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
            // padding: "10px",
            mb: 1,
          }}
        >
          {Object.keys(categoryData.data).map((fieldGroup) => (
            <Grid
              key={fieldGroup}
              item
              md={12}
              sx={{
                backgroundColor: "white",
                maxHeight: "max-content",
                height: "max-content",
                borderRadius: "8px",
                border: "1px solid #E0E0E0",
                mt: 0.25,
                boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                ...container_Padding,
                // ...container_columnGap,
              }}
            >
              <Typography
                sx={{
                  fontSize: "12px",
                  fontWeight: "700",
                  margin: "0px !important",
                }}
              >
                {fieldGroup}
              </Typography>
              <Box sx={{ width: "100%" }}>
                <CardContent>
                  <Grid
                    container
                    style={{
                      display: "grid",
                      gridTemplateColumns: "repeat(6,1fr)",
                      gap: "15px",
                    }}
                    justifyContent="space-between"
                    alignItems="flex-start"
                    md={12}
                  >
                    {categoryData.data[fieldGroup].map((field) => (
                      <EditableFieldForCostCenter
                        key={field.fieldName}
                        label={field.fieldName}
                        data={singleCCPayload}
                        value={field.value}
                        onSave={(newValue) =>
                          handleFieldSave(field.fieldName, newValue)
                        }
                        isEditMode={isEditMode}
                        // isExtendMode={isExtendMode}
                        type={field.fieldType} // Update the type as needed
                        visibility={field.visibility}
                        taskRequestId={costCenterRowData?.requestId}
                      />
                    ))}
                  </Grid>
                </CardContent>
              </Box>
            </Grid>
          ))}
        </Grid>,
      ];
    } else if (categoryData?.category == "Communication" && activeStep == 4) {
      return [
        <Grid
          key={categoryData.category}
          container
          item
          md={12}
          sx={{
            backgroundColor: "white",
            maxHeight: "max-content",
            height: "max-content",
            // borderRadius: "8px",
            // border: "1px solid #E0E0E0",
            mt: 1,
            // boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
            // padding: "10px",
            mb: 1,
          }}
        >
          {Object.keys(categoryData.data).map((fieldGroup) => (
            <Grid
              key={fieldGroup}
              item
              md={12}
              sx={{
                backgroundColor: "white",
                maxHeight: "max-content",
                height: "max-content",
                borderRadius: "8px",
                border: "1px solid #E0E0E0",
                mt: 0.25,
                boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                ...container_Padding,
                // ...container_columnGap,
              }}
            >
              <Typography
                sx={{
                  fontSize: "12px",
                  fontWeight: "700",
                  margin: "0px !important",
                }}
              >
                {fieldGroup}
              </Typography>
              <Box sx={{ width: "100%" }}>
                <CardContent>
                  <Grid
                    container
                    style={{
                      display: "grid",
                      gridTemplateColumns: "repeat(6,1fr)",
                      gap: "15px",
                    }}
                    justifyContent="space-between"
                    alignItems="flex-start"
                    md={12}
                  >
                    {categoryData.data[fieldGroup].map((field) => (
                      <EditableFieldForCostCenter
                        key={field.fieldName}
                        label={field.fieldName}
                        data={singleCCPayload}
                        value={field.value}
                        onSave={(newValue) =>
                          handleFieldSave(field.fieldName, newValue)
                        }
                        isEditMode={isEditMode}
                        // isExtendMode={isExtendMode}
                        visibility={field.visibility}
                        type={field.fieldType} // Update the type as needed
                        taskRequestId={costCenterRowData?.requestId}
                      />
                    ))}
                  </Grid>
                </CardContent>
              </Box>
            </Grid>
          ))}
        </Grid>,
      ];
    } else if (categoryData?.category == "Attachments" && activeStep == 5) {
      return [
        <>
          {!isEditMode ? (
            <Card sx={{ padding: "1rem 1rem 0rem 1rem" }}>
              <Grid
                container
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                }}
              >
                <Typography variant="h6">
                  <strong>Attachments</strong>
                </Typography>
              </Grid>
              {Boolean(attachments.length) && (
                <ReusableTable
                  width="100%"
                  rows={attachments}
                  columns={attachmentColumns}
                  hideFooter={false}
                  getRowIdValue={"id"}
                  disableSelectionOnClick={true}
                  stopPropagation_Column={"action"}
                />
              )}
              {!Boolean(attachments.length) && (
                <Typography variant="body2">No Attachments Found</Typography>
              )}
              <br />
              <Typography variant="h6">Comments</Typography>
              {Boolean(comments.length) && (
                <Timeline
                  sx={{
                    [`& .${timelineItemClasses.root}:before`]: {
                      flex: 0,
                      padding: 0,
                    },
                  }}
                >
                  {comments.map((comment) => (
                    <TimelineItem>
                      <TimelineSeparator>
                        <TimelineDot>
                          <CheckCircleOutlineOutlined
                            sx={{ color: "#757575" }}
                          />
                        </TimelineDot>
                        <TimelineConnector />
                      </TimelineSeparator>
                      <TimelineContent sx={{ py: "12px", px: 2 }}>
                        <Card
                          elevation={0}
                          sx={{
                            border: 1,
                            borderColor: "#C4C4C4",
                            borderRadius: "8px",
                            width: "650px",
                          }}
                        >
                          <Box sx={{ padding: "1rem" }}>
                            <Stack spacing={1}>
                              <Grid
                                sx={{
                                  display: "flex",
                                  justifyContent: "space-between",
                                }}
                              >
                                <Typography
                                  sx={{
                                    textAlign: "right",
                                    color: " #757575",
                                    fontWeight: "500",
                                    fontSize: "12px",
                                  }}
                                >
                                  {moment(comment.createdAt).format(
                                    "DD MMM YYYY"
                                  )}
                                </Typography>
                              </Grid>

                              <Typography
                                sx={{
                                  fontSize: "12px",

                                  color: " #757575",
                                  fontWeight: "500",
                                }}
                              >
                                {comment.user}
                              </Typography>
                              <Typography
                                sx={{
                                  fontSize: "12px",
                                  color: "#1D1D1D",
                                  fontWeight: "600",
                                }}
                              >
                                {comment.comment}
                              </Typography>
                            </Stack>
                          </Box>
                        </Card>
                      </TimelineContent>
                    </TimelineItem>
                  ))}
                </Timeline>
              )}
              {!Boolean(comments.length) && (
                <Typography variant="body2">No Comments Found</Typography>
              )}
              <br />
            </Card>
          ) : (
            <>
              <ReusableAttachementAndComments
                title="CostCenter"
                useMetaData={false}
                artifactId={ccNumber}
                artifactName="CostCenter"
              />
              <Card sx={{ padding: "1rem 1rem 0rem 1rem" }}>
                <Grid
                  container
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                  }}
                >
                  <Typography variant="h6">
                    <strong>Attachments</strong>
                  </Typography>
                </Grid>
                {Boolean(attachments.length) && (
                  <ReusableTable
                    width="100%"
                    rows={attachments}
                    columns={attachmentColumns}
                    hideFooter={false}
                    getRowIdValue={"id"}
                    disableSelectionOnClick={true}
                    stopPropagation_Column={"action"}
                  />
                )}
                {!Boolean(attachments.length) && (
                  <Typography variant="body2">
                    No Attachments Found
                  </Typography>
                )}
                <br />
                <Typography variant="h6">Comments</Typography>
                {Boolean(comments.length) && (
                  <Timeline
                    sx={{
                      [`& .${timelineItemClasses.root}:before`]: {
                        flex: 0,
                        padding: 0,
                      },
                    }}
                  >
                    {comments.map((comment) => (
                      <TimelineItem>
                        <TimelineSeparator>
                          <TimelineDot>
                            <CheckCircleOutlineOutlined
                              sx={{ color: "#757575" }}
                            />
                          </TimelineDot>
                          <TimelineConnector />
                        </TimelineSeparator>
                        <TimelineContent sx={{ py: "12px", px: 2 }}>
                          <Card
                            elevation={0}
                            sx={{
                              border: 1,
                              borderColor: "#C4C4C4",
                              borderRadius: "8px",
                              width: "650px",
                            }}
                          >
                            <Box sx={{ padding: "1rem" }}>
                              <Stack spacing={1}>
                                <Grid
                                  sx={{
                                    display: "flex",
                                    justifyContent: "space-between",
                                  }}
                                >
                                  <Typography
                                    sx={{
                                      textAlign: "right",
                                      color: " #757575",
                                      fontWeight: "500",
                                      fontSize: "12px",
                                    }}
                                  >
                                    {moment(comment.createdAt).format(
                                      "DD MMM YYYY"
                                    )}
                                  </Typography>
                                </Grid>

                                <Typography
                                  sx={{
                                    fontSize: "12px",

                                    color: " #757575",
                                    fontWeight: "500",
                                  }}
                                >
                                  {comment.user}
                                </Typography>
                                <Typography
                                  sx={{
                                    fontSize: "12px",
                                    color: "#1D1D1D",
                                    fontWeight: "600",
                                  }}
                                >
                                  {comment.comment}
                                </Typography>
                              </Stack>
                            </Box>
                          </Card>
                        </TimelineContent>
                      </TimelineItem>
                    ))}
                  </Timeline>
                )}
                {!Boolean(comments.length) && (
                  <Typography variant="body2">No Comments Found</Typography>
                )}
                <br />
              </Card>
            </>
          )}
        </>,
      ];
    }
  });


  const handleCostCenterSubmitChange = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Cost Center Submitted for Approval with ID CCR${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Approve");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Cost Center");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_CostCenter}/alter/changeCostCenterApprovalSubmit`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCostCenterSubmitCreate = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Cost Center Submitted for Approval with ID NCR${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Cost Center");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_CostCenter}/alter/costCenterApprovalSubmit`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCostCenterReviewCreateWithCopy = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Cost Center Submitted For Review with ID NCS${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        const secondApiPayload = {
          artifactId: ccNumber,
          createdBy: userData?.emailId,
          artifactType: "CostCenter",
          requestId: `NCS${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Cost Center");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_CostCenter}/alter/costCenterSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCostCenterReviewCreate = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Cost Center Submitted for Review with ID NCR${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Saving the Data");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_CostCenter}/alter/costCenterSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCostCenterSaveAsDraftChange = () => {
      setMessageDialogSeverity(false);
      handleMessageDialogClickOpen();
      setMessageDialogTitle("Confirm");
      setMessageDialogMessage(
        `Do You Want to Save as Draft ?`
      );
      setHandleExtrabutton(true);
      setHandleExtraText("proceed")
    
  };
  const handleProceedbutton =() =>{
    handleMessageDialogClose()
    setIsLoading(true);
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Cost Center Saved As Draft with ID NCS${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        const secondApiPayload = {
          artifactId: ccNumber,
          createdBy: userData?.emailId,
          artifactType: "CostCenter",
          requestId: `NCS${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Saving Cost Center");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_CostCenter}/alter/costCenterAsDraft`,
      "post",
      hSuccess,
      hError,
      payload
    );
  }
  const handleCostCenterCorrectionChange = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Cost Center submitted for Correction with ID NCR${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting for Correction");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_CostCenter}/alter/changeCostCenterSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCostCenterCorrectionCreate = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Cost Center submitted for Correction with ID NCR${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting for Correction");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_CostCenter}/alter/costCenterSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCostCenterApproveChange = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 201) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(`${data.message}`);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Approving Cost Center");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_CostCenter}/alter/changeCostCenterApproved`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCostCenterApproveCreate = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 201) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(`${data.message}`);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Approving the Cost Center");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_CostCenter}/alter/createCostCenterApproved`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCostCenterRereview = () => {
    console.log("apicallllllllll");
    const hSuccess = (data) => {
      setIsLoading(false);
      setMessageDialogMessage(
        `Create id generated for Data Owners CMS${data.body}`
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_CostCenter}/alter/costCenterSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCreateDialog = () => {
    setOpenCreateDialog(true);
  };
  const handleCreateDialogClose = () => {
    setOpenCreateDialog(false);
  };
  const handleOpenCorrectionDialog = () => {
    setTestrunStatus(false);
    setOpenCorrectionDialog(true);
  };
  const handleCorrectionDialogClose = () => {
    setTestrunStatus(true);
    setOpenCorrectionDialog(false);
  };
  const handleRemarks = (e, value) => {
    //setRemarks(e.target.value);
    const newValue = e.target.value;
    if (newValue.length > 0 && newValue[0] === ' ') {
      setRemarks(newValue.trimStart());
    } else {
    //let costCenterValue = e.target.value;
    let remarksUpperCase =newValue.toUpperCase();
    setRemarks(remarksUpperCase);
    }
  };
  const handleRemarksDialogClose = () => {
    setTestrunStatus(true);
    setOpenRemarkDialog(false);
  };
  const handleOpenRemarkDialog = () => {
    setTestrunStatus(false);
    setOpenRemarkDialog(true);
  };

  const onSubmitForReviewButtonClick = () => {
    setIsLoading(true);
    // const formData = new FormData();
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Cost Center has been Submitted for review NCS${data.body}`
        );
        setHandleExtrabutton(false)
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        handleRemarksDialogClose();
        // setIsLoading(false);
  
        // Make the second API call
        
        const secondApiPayload = {
          artifactId: ccNumber,
          createdBy: userData?.emailId,
          artifactType: "CostCenter",
          requestId:`NCS${data?.body}`
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };
  
        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
        // }
  
      } else {
        setMessageDialogTitle("Create");
        setsuccessMsg(false);
        setMessageDialogMessage("Creation Failed");
        setHandleExtrabutton(false)
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleClose();
    };
  
    const hError = (error) => {
      console.log(error);
    };
  
    doAjax(
      `/${destination_CostCenter}/alter/costCenterSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const onCostCenterSubmitRemarks = () => {
    handleCreateDialogClose();
    onSubmitForReviewButtonClick();
  };
  console.log("factorsarray", factorsArray);
  const handleWarningDialogClose = () => {
    setOpenMessageDialog(false);
  };
  return (
    <>
       {isLoading===true
      ?
      <LoadingComponent/>
      :
      <div style={{ backgroundColor: "#FAFCFF" }}>
      <ReusableDialog
        dialogState={openMessageDialog}
        openReusableDialog={handleMessageDialogClickOpen}
        closeReusableDialog={handleMessageDialogClose}
        dialogTitle={messageDialogTitle}
        dialogMessage={messageDialogMessage}
        handleDialogConfirm={handleMessageDialogClose}
        dialogOkText={"OK"}
        //handleExtraButton={handleMessageDialogNavigate}
        dialogSeverity={messageDialogSeverity}
        showCancelButton={true}
        handleDialogReject={handleWarningDialogClose}
        handleExtraText={handleExtraText}
        handleExtraButton={handleProceedbutton}
        showExtraButton={handleExtrabutton}
      />

      {successMsg && (
        <ReusableSnackBar
          openSnackBar={openSnackbar}
          alertMsg={messageDialogMessage}
          handleSnackBarClose={handleSnackBarClose}
        />
      )}
      {formValidationErrorItems.length != 0 && (
          <ReusableSnackBar
            openSnackBar={openSnackbarValidation}
            alertMsg={
              "Please fill the following Field: " +
              formValidationErrorItems.join(", ")
            }
            handleSnackBarClose={handleSnackBarCloseValidation}
          />
        )}

      <Dialog
        hideBackdrop={false}
        elevation={2}
        PaperProps={{
          sx: { boxShadow: "none" },
        }}
        open={openCorrectionDialog}
        onClose={handleCorrectionDialogClose}
      >
        <Grid
          container
          sx={{ display: "flex", justifyContent: "space-between" }}
        >
          <Grid item>
            <DialogTitle
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF40",
                display: "flex",
              }}
            >
              <Typography variant="h6">Remarks</Typography>

              <IconButton
                sx={{ width: "max-content" }}
                onClick={handleCorrectionDialogClose}
                children={<CloseIcon />}
              />
            </DialogTitle>
          </Grid>
        </Grid>
        <DialogContent sx={{ padding: ".5rem 1rem" }}>
          <Stack>
            <Box sx={{ minWidth: 400 }}>
              <FormControl sx={{ height: "auto" }} fullWidth>
                <TextField
                  sx={{ backgroundColor: "#F5F5F5" }}
                  onChange={handleRemarks}
                  multiline
                  value={remarks}
                  placeholder={"Enter Remarks for Correction"}
                  inputProps={{maxLength: 254}}
                ></TextField>
              </FormControl>
            </Box>
          </Stack>
        </DialogContent>
        <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
          <Button
            sx={{ width: "max-content", textTransform: "capitalize" }}
            onClick={handleCorrectionDialogClose}
          >
            Cancel
          </Button>
          <Button
            className="button_primary--normal"
            type="save"
            onClick={onCostCenterCorrection}
            variant="contained"
          >
            Submit
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        hideBackdrop={false}
        elevation={2}
        PaperProps={{
          sx: { boxShadow: "none" },
        }}
        open={openRemarkDialog}
        onClose={handleRemarksDialogClose}
      >
        {/* <Grid
          container
          sx={{ display: "flex", justifyContent: "space-between" }}
        > */}
        {/* <Grid item> */}
        <DialogTitle
          sx={{
            justifyContent: "space-between",
            alignItems: "center",
            height: "max-content",
            padding: ".5rem",
            paddingLeft: "1rem",
            backgroundColor: "#EAE9FF40",
            // borderBottom: "1px solid grey",
            display: "flex",
          }}
        >
          <Typography variant="h6">Remarks</Typography>

          <IconButton
            sx={{ width: "max-content" }}
            onClick={handleRemarksDialogClose}
            children={<CloseIcon />}
          />
        </DialogTitle>
        {/* </Grid> */}
        {/* </Grid> */}
        <DialogContent sx={{ padding: ".5rem 1rem" }}>
          <Stack>
            <Box sx={{ minWidth: 400 }}>
              <FormControl sx={{ height: "auto" }} fullWidth>
                <TextField
                  sx={{ backgroundColor: "#F5F5F5" }}
                  value={remarks}
                  onChange={handleRemarks}
                  multiline
                  placeholder={"Enter Remarks"}
                  inputProps={{maxLength: 254}}
                ></TextField>
              </FormControl>
            </Box>
          </Stack>
          {/* <TextField
            autoFocus
            margin="dense"
            id="name"
            label="Enter Remarks for Correction"
            type="text"
            fullWidth
            variant="standard"
            onChange={handleRemarks}
          /> */}
        </DialogContent>
        <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
          <Button
            sx={{ width: "max-content", textTransform: "capitalize" }}
            onClick={handleRemarksDialogClose}
          >
            Cancel
          </Button>
          <Button
            className="button_primary--normal"
            type="save"
            onClick={onCostCenterSubmitRemarks}
            variant="contained"
          >
            Submit
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
            hideBackdrop={false}
            elevation={2}
            PaperProps={{
              sx: { boxShadow: "none" },
            }}
            open={openCreateDialog}
            onClose={handleCreateDialogClose}
          >
            {/* <Grid
          container
          sx={{ display: "flex", justifyContent: "space-between" }}
        > */}
            {/* <Grid item> */}
            <DialogTitle
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF40",
                // borderBottom: "1px solid grey",
                display: "flex",
              }}
            >
              <Typography variant="h6">Remarks</Typography>

              <IconButton
                sx={{ width: "max-content" }}
                onClick={handleCreateDialogClose}
                children={<CloseIcon />}
              />
            </DialogTitle>
            {/* </Grid> */}
            {/* </Grid> */}
            <DialogContent sx={{ padding: ".5rem 1rem" }}>
              <Stack>
                <Box sx={{ minWidth: 400 }}>
                  <FormControl sx={{ height: "auto" }} fullWidth>
                    <TextField
                      sx={{ backgroundColor: "#F5F5F5" }}
                      // value={inputText}
                      onChange={handleRemarks}
                      value={remarks}
                      multiline
                      placeholder={"Enter Remarks"}
                      inputProps={{maxLength: 254}}
                    ></TextField>
                  </FormControl>
                </Box>
              </Stack>
              {/* <TextField
            autoFocus
            margin="dense"
            id="name"
            label="Enter Remarks for Correction"
            type="text"
            fullWidth
            variant="standard"
            onChange={handleRemarks}
          /> */}
            </DialogContent>
            <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
              <Button
                sx={{ width: "max-content", textTransform: "capitalize" }}
                onClick={handleCreateDialogClose}
              >
                Cancel
              </Button>
              <Button
                className="button_primary--normal"
                type="save"
                onClick={onCostCenterSubmitRemarks}
                variant="contained"
              >
                Submit
              </Button>
            </DialogActions>
          </Dialog>

      <Backdrop
  sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 1 }}
  open={blurLoading}
  // onClick={handleClose}
>
  <CircularProgress color="inherit" />
</Backdrop>

      <Grid container sx={outermostContainer_Information}>
        <Grid item md={12} style={{ padding: "16px", display: "flex" }}>
          <Grid md={9} sx={{ display: "flex" }}>
            <Grid>
              <IconButton
                // onClick={handleBacktoRO}
                color="primary"
                aria-label="upload picture"
                component="label"
                sx={iconButton_SpacingSmall}
              >
                <ArrowCircleLeftOutlinedIcon
                  sx={{
                    fontSize: "25px",
                    color: "#000000",
                  }}
                  onClick={() => {
                    navigate("/masterDataCockpit/costCenter");
                  }}
                />
              </IconButton>
            </Grid>
            <Grid>
              {isEditMode ? (
                <Grid item md={12}>
                  <Typography variant="h3">
                    <strong>Create Cost Center: </strong>
                  </Typography>

                  <Typography variant="body2" color="#777">
                    This view creates Cost Center from existing Cost Center
                  </Typography>
                </Grid>
              ) : (
                ""
              )}
              {isDisplayMode ? (
                <Grid item md={12}>
                  <Typography variant="h3">
                    <strong>Display Cost Center </strong>
                  </Typography>

                  <Typography variant="body2" color="#777">
                    This view displays the details of the Cost Center
                  </Typography>
                </Grid>
              ) : (
                ""
              )}
            </Grid>
          </Grid>
          {/* {costCenterRowData?.reqStatus === "Correction Pending" ? (
            <Grid>
              <IconButton
                color="primary"
                aria-label="upload picture"
                component="label"
                sx={iconButton_SpacingSmall}
              >
                <MarkunreadOutlinedIcon
                  sx={{
                    fontSize: "25px",
                    color: "#000000",
                    alignItems: "flex-end",
                  }}
                  onClick={() => {}}
                />
              </IconButton>
            </Grid>
          ) : (
            ""
          )} */}
          <Grid
            md={3}
            sx={{ display: "flex", justifyContent: "flex-end" }}
            gap={2}
          >
            {checkIwaAccess(iwaAccessData, "Cost Center", "ChangeCC") &&
              (userData?.role === "Super User" &&
                costCenterRowData?.requestType &&
                isDisplayMode ? (
                <Grid gap={1} sx={{ display: "flex" }}>
                  <Grid
                    gap={1}
                    sx={{ display: "flex", justifyContent: "space-between" }}
                  >
                    <>
                      <Grid item>
                        <Button
                          variant="outlined"
                          size="small"
                          sx={button_Outlined}
                          onClick={onEdit}
                        >
                          Fill Details
                          <EditOutlinedIcon
                            sx={{ padding: "2px" }}
                            fontSize="small"
                          />
                        </Button>
                      </Grid>
                    </>
                  </Grid>
                </Grid>
              ) : userData?.role === "Finance" &&
                costCenterRowData?.requestType &&
                isDisplayMode ? (
                <Grid gap={1} sx={{ display: "flex" }}>
                  <Grid
                    gap={1}
                    sx={{ display: "flex", justifyContent: "space-between" }}
                  >
                    <>
                      <Grid item>
                        <Button
                          variant="outlined"
                          size="small"
                          sx={button_Outlined}
                          onClick={onEdit}
                        >
                          Fill Details
                          <EditOutlinedIcon
                            sx={{ padding: "2px" }}
                            fontSize="small"
                          />
                        </Button>
                      </Grid>
                    </>
                  </Grid>
                </Grid>
              ) : userData?.role === "Super User" &&
                !costCenterRowData?.requestType &&
                isDisplayMode ? (
                <Grid gap={1} sx={{ display: "flex" }}>
                  <Grid
                    gap={1}
                    sx={{ display: "flex", justifyContent: "space-between" }}
                  >
                    <>
                      <Grid item>
                        <Button
                          variant="outlined"
                          size="small"
                          sx={button_Outlined}
                          onClick={onEdit}
                        >
                          Change
                          <EditOutlinedIcon
                            sx={{ padding: "2px" }}
                            fontSize="small"
                          />
                        </Button>
                      </Grid>
                    </>
                  </Grid>
                </Grid>
              ) : userData?.role === "Finance" &&
                !costCenterRowData?.requestType &&
                isDisplayMode ? (
                <Grid gap={1} sx={{ display: "flex" }}>
                  <Grid
                    gap={1}
                    sx={{ display: "flex", justifyContent: "space-between" }}
                  >
                    <>
                      <Grid item>
                        <Button
                          variant="outlined"
                          size="small"
                          sx={button_Outlined}
                          onClick={onEdit}
                        >
                          Change
                          <EditOutlinedIcon
                            sx={{ padding: "2px" }}
                            fontSize="small"
                          />
                        </Button>
                      </Grid>
                    </>
                  </Grid>
                </Grid>
              ) : (
                ""
              ))}
          </Grid>
        </Grid>
        <Grid container display="flex" flexDirection="row" flexWrap="nowrap">
          <Grid item md={10} sx={{ marginLeft: "40px" }}>
            <Grid item sx={{ paddingTop: "2px !important" }}>
              <Stack flexDirection="row">
                <div style={{ width: "12%" }}>
                  <Typography variant="body2" color="#777">
                    Cost Center
                  </Typography>
                </div>
                <Typography
                  variant="body2"
                  fontWeight="bold"
                  justifyContent="flex-start"
                >
                  :{" "}
                  {`${costCenterRowData?.companyCode?.newCompanyCode?.code}${costCenterRowData?.costCenterName?.newCostCenterName}`
                    ? `${costCenterRowData?.companyCode?.newCompanyCode?.code}${costCenterRowData?.costCenterName?.newCostCenterName}`
                    : ""}
                </Typography>
              </Stack>
            </Grid>

            <Grid item sx={{ paddingTop: "2px !important" }}>
              <Stack flexDirection="row">
                <div style={{ width: "12%" }}>
                  <Typography variant="body2" color="#777">
                    Controlling Area
                  </Typography>
                </div>
                <Typography variant="body2" fontWeight="bold">
                  :{" "}
                  {costCenterRowData?.controllingAreaData?.newControllingArea
                    ?.code
                    ? costCenterRowData?.controllingAreaData?.newControllingArea
                      ?.code
                    : ""}
                </Typography>
              </Stack>
            </Grid>
          </Grid>
          <Grid item md={2} sx={{ marginLeft: "40px" }}>
            <Grid item sx={{ paddingTop: "2px !important" }}>
              <Stack flexDirection="row">
                <div style={{ width: "50%" }}>
                  <Typography variant="body2" color="#777">
                    Valid From
                  </Typography>
                </div>
                <Typography variant="body2" fontWeight="bold">
                  :{" "}
                  {moment(
                    costCenterRowData?.validFromDate?.newValidFromDate
                  ).format(appSettings?.dateFormat)}
                </Typography>
              </Stack>
            </Grid>
            <Grid item sx={{ paddingTop: "2px !important" }}>
              <Stack flexDirection="row">
                <div style={{ width: "50%" }}>
                  <Typography variant="body2" color="#777">
                    Valid To
                  </Typography>
                </div>
                <Typography variant="body2" fontWeight="bold">
                  :{" "}
                  {moment(
                    costCenterRowData?.validToDate?.newValidToDate
                  ).format(appSettings?.dateFormat)}
                </Typography>
                <Typography variant="body2" fontWeight="bold"></Typography>
              </Stack>
            </Grid>
          </Grid>
        </Grid>

        <Grid container style={{ marginLeft: 25 }}>
          <Stepper
            activeStep={activeStep}
            // onChange={handleC/hange}
            // variant="scrollable"
            sx={{
              background: "#FFFFFF",
              borderBottom: "1px solid #BDBDBD",
              width: "100%",
              height: "48px",
            }}
            aria-label="mui tabs example"
          >
            {factorsArray.map((factor, index) => (
              <Step key={factor}>
                <StepLabel sx={{ fontWeight: "700" }}>{factor}</StepLabel>
              </Step>
            ))}
          </Stepper>

          {/* Display the cards of the currently active tab */}
          {tabContents &&
            tabContents[activeStep]?.map((cardContent, index) => (
              <Box key={index} sx={{ mb: 2, width: "100%" }}>
                <Typography variant="body2">{cardContent}</Typography>
              </Box>
            ))}
        </Grid>
      </Grid>

      <Grid gap={1} sx={{ display: "flex", justifyContent: "space-between" }}>
        {checkIwaAccess(iwaAccessData, "Cost Center", "ChangeCC") &&
          (!costCenterRowData?.requestType && !isEditMode ? (
            <Paper
              sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
              elevation={2}
            >
              <BottomNavigation
                className="container_BottomNav"
                showLabels
                sx={{ display: "flex", justifyContent: "flex-end" }}
              >
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary, mr: 1 }}
                  onClick={handleBack}
                  disabled={activeStep === 0}
                >
                  Back
                </Button>
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary, mr: 1 }}
                  onClick={handleNext}
                  disabled={
                    activeStep === factorsArray.length - 1 ? true : false
                  }
                >
                  Next
                </Button>
              </BottomNavigation>
            </Paper>
          ) : (
            ""
          ))}

        {checkIwaAccess(iwaAccessData, "Cost Center", "ChangeCC") &&
          (userData?.role === "Super User" &&
            !costCenterRowData?.requestType &&
            isEditMode ? (
            <Paper
              sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
              elevation={2}
            >
              <BottomNavigation
                className="container_BottomNav"
                showLabels
                sx={{ display: "flex", justifyContent: "flex-end" }}
              >
                <Button
                  variant="contained"
                  size="small"submit for review
                  sx={{ ...button_Primary, mr: 1 }}
                  onClick={onCostCenterSaveAsDraftChange}
                >
                  Save As Draft
                </Button>
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary, mr: 1 }}
                  onClick={handleBack}
                  disabled={activeStep === 0}
                >
                  Back
                </Button>

                {activeStep === factorsArray.length - 1 ? (
                  <>
                    <Button
                      variant="outlined"
                      size="small"
                      sx={{ button_Outlined, mr: 1 }}
                      onClick={onValidateCostCenter}
                    >
                      Validate
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleOpenRemarkDialog}
                      // disabled={submitForReviewDisabled}
                    >
                      Submit For Review
                    </Button>
                  </>
                ) : (
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleNext}
                    disabled={
                      activeStep === factorsArray.length - 1 ? true : false
                    }
                  >
                    Next
                  </Button>
                )}
              </BottomNavigation>
            </Paper>
          ) : userData?.role === "Finance" &&
            !costCenterRowData?.requestType && //for change from master table
            isEditMode ? (
            <Paper
              sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
              elevation={2}
            >
              <BottomNavigation
                className="container_BottomNav"
                showLabels
                sx={{ display: "flex", justifyContent: "flex-end" }}
              >
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary, mr: 1 }}
                  onClick={onCostCenterSaveAsDraftChange}
                >
                  Save As Draft
                </Button>
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary, mr: 1 }}
                  onClick={handleBack}
                  disabled={activeStep === 0}
                >
                  Back
                </Button>
                {activeStep === factorsArray.length - 1 ? (
                  <>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={onValidateCostCenter}
                    >
                      Validate
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleCreateDialog}
                      disabled={submitForReviewDisabled}
                    >
  
                      Submit For Review
                    </Button>
                  </>
                ) : (
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleNext}
                    disabled={
                      activeStep === factorsArray.length - 1 ? true : false
                    }
                  >
                    Next
                  </Button>
                )}
              </BottomNavigation>
            </Paper>
          ) : (
            ""
          ))}

        {checkIwaAccess(iwaAccessData, "Cost Center", "ChangeCC") &&
          (userData?.role === "Super User" &&
            costCenterRowData?.requestType === "Create" &&
            !isEditMode ? (
            <Paper
              sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
              elevation={2}
            >
              <BottomNavigation
                className="container_BottomNav"
                showLabels
                sx={{ display: "flex", justifyContent: "flex-end" }}
              >
                <Button
                  variant="contained"
                  size="small"
                  sx={{ button_Outlined, mr: 1 }}
                  onClick={onCostCenterApproveCreate}
                >
                  Approve
                </Button>
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary, mr: 1 }}
                  onClick={onCostCenterSubmitCreate}
                >
                  Submit For Approval
                </Button>
                {/* <Button
                  variant="outlined"
                  size="small"
                  sx={button_Outlined}
                  onClick={onCostCenterCorrectionCreate}
                >
                  Correction
                  <EditOutlinedIcon
                    sx={{ marginLeft: "5px", padding: "2px" }}
                    fontSize="small"
                  />
                </Button> */}
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary, mr: 1 }}
                  onClick={handleBack}
                  disabled={activeStep === 0}
                >
                  Back
                </Button>
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary, mr: 1 }}
                  onClick={handleNext}
                  disabled={
                    activeStep === factorsArray.length - 1 ? true : false
                  }
                >
                  Next
                </Button>
              </BottomNavigation>
            </Paper>
          ) : userData?.role === "Super User" &&
            costCenterRowData?.requestType === "Change" &&
            !isEditMode ? (
            <Paper
              sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
              elevation={2}
            >
              <BottomNavigation
                className="container_BottomNav"
                showLabels
                sx={{ display: "flex", justifyContent: "flex-end" }}
              >
                <Button
                  variant="contained"
                  size="small"
                  sx={{ button_Outlined, mr: 1 }}
                  onClick={onCostCenterApproveChange}
                >
                  Approve
                </Button>
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary, mr: 1 }}
                  onClick={onCostCenterSubmitChange}
                >
                  Submit For Approval
                </Button>
                {/* <Button
                  variant="outlined"
                  size="small"
                  sx={button_Outlined}
                  onClick={onCostCenterCorrectionChange}
                >
                  Correction
                  <EditOutlinedIcon
                    sx={{ marginLeft: "5px", padding: "2px" }}
                    fontSize="small"
                  />
                </Button> */}
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary, mr: 1 }}
                  onClick={handleBack}
                  disabled={activeStep === 0}
                >
                  Back
                </Button>
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary, mr: 1 }}
                  onClick={handleNext}
                  disabled={
                    activeStep === factorsArray.length - 1 ? true : false
                  }
                >
                  Next
                </Button>
              </BottomNavigation>
            </Paper>
          ) : userData?.role === "MDM Steward" &&
            (costCenterRowData?.requestType === "Create" ||
              taskRowDetails?.processDesc === "Create") &&
            !isEditMode ? (
            <Paper
              sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
              elevation={2}
            >
              <BottomNavigation
                className="container_BottomNav"
                showLabels
                sx={{ display: "flex", justifyContent: "flex-end" }}
              >
                <Button
                  variant="outlined"
                  size="small"
                  sx={{ button_Outlined, mr: 1 }}
                  onClick={handleOpenCorrectionDialog}
                >
                  Correction
                </Button>
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary, mr: 1 }}
                  onClick={onCostCenterSubmitCreate}
                >
                  Submit For Approval
                </Button>
                {/* <Button
                  variant="outlined"
                  size="small"
                  sx={button_Outlined}
                  onClick={onCostCenterCorrectionCreate}
                >
                  Correction
                  <EditOutlinedIcon
                    sx={{ marginLeft: "5px", padding: "2px" }}
                    fontSize="small"
                  />
                </Button> */}
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary, mr: 1 }}
                  onClick={handleBack}
                  disabled={activeStep === 0}
                >
                  Back
                </Button>
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary, mr: 1 }}
                  onClick={handleNext}
                  disabled={
                    activeStep === factorsArray.length - 1 ? true : false
                  }
                >
                  Next
                </Button>
              </BottomNavigation>
            </Paper>
          ) : userData?.role === "MDM Steward" &&
            (costCenterRowData?.requestType === "Change" ||
              taskRowDetails?.processDesc === "Change") &&
            !isEditMode ? (
            <Paper
              sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
              elevation={2}
            >
              <BottomNavigation
                className="container_BottomNav"
                showLabels
                sx={{ display: "flex", justifyContent: "flex-end" }}
              >
                <Button
                  variant="outlined"
                  size="small"
                  sx={{ button_Outlined, mr: 1 }}
                  onClick={handleOpenCorrectionDialog}
                >
                  Correction
                </Button>
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary, mr: 1 }}
                  onClick={onCostCenterSubmitChange}
                >
                  Submit For Approval
                </Button>
                {/* <Button
                  variant="outlined"
                  size="small"
                  sx={button_Outlined}
                  onClick={onCostCenterCorrectionChange}
                >
                  Correction
                  <EditOutlinedIcon
                    sx={{ marginLeft: "5px", padding: "2px" }}
                    fontSize="small"
                  />
                </Button> */}
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary, mr: 1 }}
                  onClick={handleBack}
                  disabled={activeStep === 0}
                >
                  Back
                </Button>
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary, mr: 1 }}
                  onClick={handleNext}
                  disabled={
                    activeStep === factorsArray.length - 1 ? true : false
                  }
                >
                  Next
                </Button>
              </BottomNavigation>
            </Paper>
          ) : userData?.role === "Approver" &&
            (costCenterRowData?.requestType === "Create" ||
              taskRowDetails?.processDesc === "Create") &&
            !isEditMode ? (
            <Paper
              sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
              elevation={2}
            >
              <BottomNavigation
                className="container_BottomNav"
                showLabels
                sx={{ display: "flex", justifyContent: "flex-end" }}
              >
                <Button
                  variant="outlined"
                  size="small"
                  sx={{ button_Outlined, mr: 1 }}
                  onClick={handleOpenCorrectionDialog}
                >
                  Correction
                </Button>
                <Button
                  variant="contained"
                  size="small"
                  sx={{ button_Outlined, mr: 1 }}
                  onClick={onCostCenterApproveCreate}
                >
                  Approve
                </Button>
                {/* <Button
                  variant="outlined"
                  size="small"
                  sx={button_Outlined}
                  onClick={onCostCenterCorrectionCreate}
                >
                  Correction
                  <EditOutlinedIcon
                    sx={{ marginLeft: "5px", padding: "2px" }}
                    fontSize="small"
                  />
                </Button> */}
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary, mr: 1 }}
                  onClick={handleBack}
                  disabled={activeStep === 0}
                >
                  Back
                </Button>
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary, mr: 1 }}
                  onClick={handleNext}
                  disabled={
                    activeStep === factorsArray.length - 1 ? true : false
                  }
                >
                  Next
                </Button>
              </BottomNavigation>
            </Paper>
          ) : userData?.role === "Approver" &&
            (costCenterRowData?.requestType === "Change" ||
              taskRowDetails?.processDesc === "Change") &&
            !isEditMode ? (
            <Paper
              sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
              elevation={2}
            >
              <BottomNavigation
                className="container_BottomNav"
                showLabels
                sx={{ display: "flex", justifyContent: "flex-end" }}
              >
                <Button
                  variant="outlined"
                  size="small"
                  sx={{ button_Outlined, mr: 1 }}
                  onClick={handleOpenCorrectionDialog}
                >
                  Correction
                </Button>
                <Button
                  variant="contained"
                  size="small"
                  sx={{ button_Outlined, mr: 1 }}
                  onClick={onCostCenterApproveChange}
                >
                  Approve
                </Button>
                {/* <Button
                  variant="outlined"
                  size="small"
                  sx={button_Outlined}
                  onClick={onCostCenterCorrectionChange}
                >
                  Correction
                  <EditOutlinedIcon
                    sx={{ marginLeft: "5px", padding: "2px" }}
                    fontSize="small"
                  />
                </Button> */}
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary, mr: 1 }}
                  onClick={handleBack}
                  disabled={activeStep === 0}
                >
                  Back
                </Button>
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary, mr: 1 }}
                  onClick={handleNext}
                  disabled={
                    activeStep === factorsArray.length - 1 ? true : false
                  }
                >
                  Next
                </Button>
              </BottomNavigation>
            </Paper>
          ) : userData?.role === "Super User" &&
            costCenterRowData?.requestType === "Create" &&
            isEditMode ? (
            <Paper
              sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
              elevation={2}
            >
              <BottomNavigation
                className="container_BottomNav"
                showLabels
                sx={{ display: "flex", justifyContent: "flex-end" }}
              >
                {/* <Button
                  variant="outlined"
                  size="small"
                  sx={button_Outlined}
                  onClick={onCostCenterSaveAsDraft}
                >
                  Save As Draft
                </Button> */}
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary, mr: 1 }}
                  onClick={handleBack}
                  disabled={activeStep === 0}
                >
                  Back
                </Button>
                {activeStep === factorsArray.length - 1 ? (
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={onCostCenterReviewCreate}
                    // disabled={submitForReviewDisabled}
                  >
                    Submit For Review
                  </Button>
                ) : (
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleNext}
                    disabled={
                      activeStep === factorsArray.length - 1 ? true : false
                    }
                  >
                    Next
                  </Button>
                )}
              </BottomNavigation>
            </Paper>
          ) : userData?.role === "Super User" &&
            costCenterRowData?.requestType === "Change" &&
            isEditMode ? (
            <Paper
              sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
              elevation={2}
            >
              <BottomNavigation
                className="container_BottomNav"
                showLabels
                sx={{ display: "flex", justifyContent: "flex-end" }}
              >
                {/* <Button
                  variant="outlined"
                  size="small"
                  sx={button_Outlined}
                  onClick={onCostCenterSaveAsDraft}
                >
                  Save As Draft
                </Button> */}
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary, mr: 1 }}
                  onClick={handleBack}
                  disabled={activeStep === 0}
                >
                  Back
                </Button>
                {activeStep === factorsArray.length - 1 ? (
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={onCostCenterReviewCreateWithCopy}
                    // disabled={submitForReviewDisabled}
                  >
                    Submit For Review
                  </Button>
                ) : (
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleNext}
                    disabled={
                      activeStep === factorsArray.length - 1 ? true : false
                    }
                  >
                    Next
                  </Button>
                )}
              </BottomNavigation>
            </Paper>
          ) : userData?.role === "Finance" &&
            (costCenterRowData?.requestType === "Create" ||
              taskRowDetails?.processDesc === "Create") &&
            isEditMode ? (
            <Paper
              sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
              elevation={2}
            >
              <BottomNavigation
                className="container_BottomNav"
                showLabels
                sx={{ display: "flex", justifyContent: "flex-end" }}
              >
                {/* <Button
                  variant="outlined"
                  size="small"
                  sx={button_Outlined}
                  onClick={onCostCenterSaveAsDraft}
                >
                  Save As Draft
                </Button> */}
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary, mr: 1 }}
                  onClick={handleBack}
                  disabled={activeStep === 0}
                >
                  Back
                </Button>
                {activeStep === factorsArray.length - 1 ? (
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={onCostCenterReviewCreate}
                    disabled={submitForReviewDisabled}
                  >
                    Submit For Review
                  </Button>
                ) : (
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleNext}
                    disabled={
                      activeStep === factorsArray.length - 1 ? true : false
                    }
                  >
                    Next
                  </Button>
                )}
              </BottomNavigation>
            </Paper>
          ) : userData?.role === "Finance" &&
            (costCenterRowData?.requestType === "Change" ||
              taskRowDetails?.processDesc === "Change") &&
            isEditMode ? (
            <Paper
              sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
              elevation={2}
            >
              <BottomNavigation
                className="container_BottomNav"
                showLabels
                sx={{ display: "flex", justifyContent: "flex-end" }}
              >
                {/* <Button
                  variant="outlined"
                  size="small"
                  sx={button_Outlined}
                  onClick={onCostCenterSaveAsDraft}
                >
                  Save As Draft
                </Button> */}
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary, mr: 1 }}
                  onClick={handleBack}
                  disabled={activeStep === 0}
                >
                  Back
                </Button>
                {activeStep === factorsArray.length - 1 ? (
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={onCostCenterReviewCreateWithCopy}
                    disabled={submitForReviewDisabled}
                  >
                    Submit For Review
                  </Button>
                ) : (
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleNext}
                    disabled={
                      activeStep === factorsArray.length - 1 ? true : false
                    }
                  >
                    Next
                  </Button>
                )}
              </BottomNavigation>
            </Paper>
          ) : (
            ""
          ))}
      </Grid>
      <Dialog
        open={dupliDialog}
        // onClose={handleDialogClose}
        sx={{
          "&::webkit-scrollbar": {
            width: "1px",
          },
        }}
      >
        <DialogTitle
          sx={{
            justifyContent: "space-between",
            alignItems: "center",
            height: "max-content",
            padding: ".5rem",
            paddingLeft: "1rem",
            backgroundColor: "#EAE9FF40",
            // borderBottom: "1px solid grey",
            display: "flex",
          }}
        >
          <Typography variant="h6">New Cost Center</Typography>

          {/* <IconButton
                    sx={{ width: "max-content" }}
                    onClick={handleDialogClose}
                    children={<CloseIcon />}
                  /> */}
        </DialogTitle>
        <DialogContent sx={{ padding: ".5rem 1rem" }}>
          <Grid container spacing={1}>
            <Grid
              item
              md={6}
              sx={{
                width: "100%",
                marginTop: ".5rem",
              }}
            >
              <Typography>
                Cost Center
                <span style={{ color: "red" }}>*</span>
              </Typography>
              <FormControl
                fullWidth
                sx={{ margin: ".5em 0px", minWidth: "250px" }}
              >
                <TextField
                  sx={{ fontSize: "12px !important", height: "31px" }}
                  fullWidth
                  size="small"
                  // value={rmSearchForm?.changedBy}
                  // onChange={(e, value) => {
                  //   setNewCostCenterName(e.target.value);
                  // }}
                  placeholder="Enter Cost Center Name"
                  // error={newCostCenterName === "" ? true : false}
                  required={true}
                />
              </FormControl>
            </Grid>
            <Grid item md={6} sx={{ width: "100%", marginTop: ".5rem" }}>
              <Typography>
                Controlling Area
                <span style={{ color: "red" }}>*</span>
              </Typography>
              <FormControl
                fullWidth
                sx={{ margin: ".5em 0px", minWidth: "250px" }}
              >
                <Autocomplete
                  sx={{ height: "42px" }}
                  required="true"
                  size="small"
                  // onChange={(e, value) => {
                  //   setNewControllingArea(value);
                  // }}
                  // options={dropdownData?.ControllingArea ?? []}
                  // getOptionLabel={(option) => `${option?.code}`}
                  // value={rmSearchForm?.plant}
                  // renderOption={(props, option) => (
                  //   <li {...props}>
                  //     <Typography style={{ fontSize: 12 }}>
                  //       {`${option?.code}`}
                  //     </Typography>
                  //   </li>
                  // )}
                  // error={newControllingArea === "" ? true : false}
                  renderInput={(params) => (
                    <TextField
                      sx={{ fontSize: "12px !important" }}
                      {...params}
                      variant="outlined"
                      placeholder="Select Cost Center"
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid
              item
              md={6}
              sx={{
                width: "100%",
                marginTop: ".5rem",
              }}
            >
              <Typography>
                Valid From
                <span style={{ color: "red" }}>*</span>
              </Typography>

              <FormControl fullWidth sx={{ margin: ".5em 0px" }}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  {/* <DemoContainer components={["DatePicker"]}> */}
                  <DatePicker
                    slotProps={{ textField: { size: "small" } }}
                  // value={newValidFromDate}
                  // onChange={(value) => setNewValidFromDate(value)}
                  />

                  {/* </DemoContainer> */}
                </LocalizationProvider>
              </FormControl>
            </Grid>
            <Grid
              item
              md={6}
              sx={{
                width: "100%",
                marginTop: ".5rem",
              }}
            >
              <Typography>
                Valid To
                <span style={{ color: "red" }}>*</span>
              </Typography>

              <FormControl fullWidth sx={{ margin: ".5em 0px" }}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  {/* <DemoContainer components={["DatePicker"]}> */}
                  <DatePicker
                    slotProps={{ textField: { size: "small" } }}
                  // value={newValidToDate}
                  // onChange={(value) => setNewValidToDate(value)}
                  />
                  {/* </DemoContainer> */}
                </LocalizationProvider>
              </FormControl>
            </Grid>
          </Grid>

          {/* {isValidationError && (
                    <Grid>
                      <Typography style={{ color: "red" }}>
                        Please Enter Mandatory Fields
                      </Typography>
                    </Grid>
                  )} */}
        </DialogContent>

        <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
          <Button
            sx={{ width: "max-content", textTransform: "capitalize" }}
          // onClick={handleDialogClose}
          >
            Cancel
          </Button>
          <Button
            className="button_primary--normal"
            type="save"
            // onClick={handleDialogProceed}
            variant="contained"
          >
            Proceed
          </Button>
        </DialogActions>
      </Dialog>
    </div>
       }
  </>
  );
};

export default DisplayCopyCostCenter;
