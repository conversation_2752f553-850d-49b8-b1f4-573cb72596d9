import{r as L,a as d,S as ye,T as O,p as U,d4 as ue,d6 as he,s as fe,u as ge,q as w,e as Ne,aX as b,dx as Se,dA as xe,dk as M,x as ke,j as R,l as N,dy as Ee,h as Ie,b6 as Ae,dB as _,E as be,F as Re,G as Te,dw as De}from"./index-17b8d91e.js";import{u as ve}from"./useChangeLogUpdate-f322f7d1.js";const Ce=({fallback:e="--",variant:c="text",delay:h=3e3,sx:S={fontSize:"1rem"}})=>{const[E,f]=L.useState(!0);return L.useEffect(()=>{const m=setTimeout(()=>{f(!1)},h);return()=>clearTimeout(m)},[h]),E?d(ye,{variant:c,sx:S}):d(O,{component:"span",sx:S,children:e})},ce=2,ne=U.createContext({}),we=U.forwardRef((e,c)=>{const h=U.useContext(ne);return d("div",{ref:c,...e,...h})}),Le=U.forwardRef(function(c,h){const{children:S,...E}=c,f=[];S.forEach(y=>{f.push(y)});const m=f.length,T=he.HEIGHT,D=()=>m>8?8*T:f.length*T;return d("div",{ref:h,children:d(ne.Provider,{value:E,children:d(ue,{itemData:f,height:D()+2*ce,width:"100%",outerElementType:we,innerElementType:"ul",itemSize:T,overscanCount:5,itemCount:m,children:({data:y,index:x,style:i})=>{const q=y[x],l={...i,top:i.top+ce};return d("li",{style:{...l,listStyle:"none"},children:q})}})})})}),Me=(e,c,h,S)=>{var D;if(e!=="Region")return!1;const E=(D=h==null?void 0:h.code)==null?void 0:D.toUpperCase(),f=c.some(y=>y.includes("CA-MDG-MRKTNG-US")),m=c.some(y=>y.includes("CA-MDG-MRKTNG-FERT-EUR")||y.includes("CA-MDG-MRKTNG-SALES-EUR"));return f&&m?!1:E==="US"?!f:E==="EUR"?!m:!0};function qe(e){var Y,$,G,B,W,z,F,H,j,Q,K,X,J,Z,V,s,r,o,p,ee,te,ae,ie;const c=fe(),{updateChangeLog:h}=ve(),S=ge(),f=new URLSearchParams(S.search).get("RequestId"),m=w(a=>a.payload.payloadData),T=S.pathname.includes("DisplayMaterialSAPView");let D=w(a=>a.userManagement.roles);const{t:y}=Ne(),x=w(a=>a.payload||{}),i=w(a=>{var t;return((t=a.AllDropDown)==null?void 0:t.dropDown)||{}}),q=w(a=>{var t;return((t=a.payload)==null?void 0:t.errorFields)||[]}),[l,I]=L.useState(null),n=((B=(G=($=(Y=x==null?void 0:x[e==null?void 0:e.materialID])==null?void 0:Y.payloadData)==null?void 0:$[e==null?void 0:e.viewName])==null?void 0:G[e==null?void 0:e.plantData])==null?void 0:B[e==null?void 0:e.keyName])??((W=x==null?void 0:x.payloadData)==null?void 0:W[e==null?void 0:e.keyName])??(((z=e==null?void 0:e.details)==null?void 0:z.fieldPriority)==="ApplyBus"||e!=null&&e.isRequestHeader?(F=e==null?void 0:e.details)==null?void 0:F.value:null);L.useEffect(()=>{var a,t,g;(((a=e==null?void 0:e.details)==null?void 0:a.visibility)===b.MANDATORY||((t=e==null?void 0:e.details)==null?void 0:t.visibility)==="Required")&&c(Se((e==null?void 0:e.keyName)||"")),((g=e==null?void 0:e.details)==null?void 0:g.visibility)===b.DISPLAY&&c(xe(e==null?void 0:e.keyName))},[c,(H=e==null?void 0:e.details)==null?void 0:H.visibility,e==null?void 0:e.keyName]),L.useEffect(()=>{var a,t,g,k,le,de;if(n!=null&&n!=="")if(n!=null&&n.code)I(n),M({materialID:(e==null?void 0:e.materialID)||"",keyName:(e==null?void 0:e.keyName)||"",data:n,viewID:e==null?void 0:e.viewName,itemID:e==null?void 0:e.plantData});else if(i!=null&&i[e==null?void 0:e.keyName]||(a=i==null?void 0:i[e==null?void 0:e.keyName])!=null&&a[e==null?void 0:e.plantData]){if(!Array.isArray(i==null?void 0:i[e==null?void 0:e.keyName])&&!Array.isArray((t=i==null?void 0:i[e==null?void 0:e.keyName])==null?void 0:t[e==null?void 0:e.plantData])){I(null);return}const u=(g=i[e==null?void 0:e.keyName])!=null&&g.length?(k=i[e==null?void 0:e.keyName])==null?void 0:k.find(A=>{var v,C;return((v=A==null?void 0:A.code)==null?void 0:v.trim())===((C=n==null?void 0:n.toString())==null?void 0:C.trim())}):(de=(le=i[e==null?void 0:e.keyName])==null?void 0:le[e==null?void 0:e.plantData])==null?void 0:de.find(A=>{var v,C;return((v=A==null?void 0:A.code)==null?void 0:v.trim())===((C=n==null?void 0:n.toString())==null?void 0:C.trim())});u?(I({code:u==null?void 0:u.code,desc:u==null?void 0:u.desc}),c(M({materialID:(e==null?void 0:e.materialID)||"",keyName:(e==null?void 0:e.keyName)||"",data:{code:u==null?void 0:u.code,desc:u==null?void 0:u.desc},viewID:e==null?void 0:e.viewName,itemID:e==null?void 0:e.plantData}))):(I(null),c(M({materialID:(e==null?void 0:e.materialID)||"",keyName:(e==null?void 0:e.keyName)||"",data:null,viewID:e==null?void 0:e.viewName,itemID:e==null?void 0:e.plantData})))}else I(null);else I(null)},[n]);const me=(a,t)=>{var g,k;I(t),c(M({materialID:(e==null?void 0:e.materialID)||"",keyName:(e==null?void 0:e.keyName)||"",data:t??null,viewID:e==null?void 0:e.viewName,itemID:e==null?void 0:e.plantData})),f&&!De.includes(m==null?void 0:m.RequestStatus)&&h({materialID:e==null?void 0:e.selectedMaterialNumber,viewName:e==null?void 0:e.viewName,plantData:e==null?void 0:e.plantData,fieldName:(g=e==null?void 0:e.details)==null?void 0:g.fieldName,jsonName:(k=e==null?void 0:e.details)==null?void 0:k.jsonName,currentValue:`${t==null?void 0:t.code}-${(t==null?void 0:t.desc)??""}`,requestId:m==null?void 0:m.RequestId,childRequestId:f})},P=(j=e==null?void 0:e.details)==null?void 0:j.jsonName;return d(Te,{item:!0,md:2,sx:{marginBottom:"12px !important"},children:((Q=e==null?void 0:e.details)==null?void 0:Q.visibility)==="Hidden"?null:d(ke,{children:T?R("div",{style:{padding:"16px",backgroundColor:N.primary.white,borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.1)",margin:"16px 0",transition:"all 0.3s ease"},children:[R(O,{variant:"body1",style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%",fontWeight:600,fontSize:"12px",marginBottom:"4px",display:"flex",alignItems:"center"},title:y((K=e==null?void 0:e.details)==null?void 0:K.fieldName),children:[y((X=e==null?void 0:e.details)==null?void 0:X.fieldName)||"Field Name",(((J=e==null?void 0:e.details)==null?void 0:J.visibility)===b.REQUIRED||((Z=e==null?void 0:e.details)==null?void 0:Z.visibility)===Ee.MANDATORY)&&d("span",{style:{color:N.error.darkRed,marginLeft:"5px",fontSize:"1.1rem"},children:"*"})]}),d("div",{style:{fontSize:"0.8rem",color:N.black.dark,marginTop:"4px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",width:"100%",cursor:"pointer"},children:l!=null&&l.code||l!=null&&l.desc?d(Ie,{title:l!=null&&l.code?`${l==null?void 0:l.code} - ${(l==null?void 0:l.desc)||""}`:"--",arrow:!0,children:R("span",{children:[d("strong",{style:{fontWeight:600,color:N.secondary.grey,marginRight:"6px",letterSpacing:"0.5px",wordSpacing:"1px"},children:l==null?void 0:l.code}),(l==null?void 0:l.desc)&&R("span",{style:{fontWeight:500,color:N.secondary.grey,letterSpacing:"0.5px",wordSpacing:"1px"},children:["- ",l==null?void 0:l.desc]})]})}):d(Ce,{fallback:"--"})})]}):R(Re,{children:[R(O,{variant:"body2",color:N.secondary.grey,sx:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%"},title:y((V=e==null?void 0:e.details)==null?void 0:V.fieldName),children:[y((s=e==null?void 0:e.details)==null?void 0:s.fieldName)||"Field Name",(((r=e==null?void 0:e.details)==null?void 0:r.visibility)==="Required"||((o=e==null?void 0:e.details)==null?void 0:o.visibility)===b.MANDATORY)&&d("span",{style:{color:N.error.dark},children:"*"})]}),d(Ae,{sx:{height:"31px","& .MuiAutocomplete-listbox":{padding:0,"& .MuiAutocomplete-option":{paddingLeft:"16px",paddingTop:"4px",paddingBottom:"4px",justifyContent:"flex-start"}},"& .MuiAutocomplete-option":{display:"flex",alignItems:"center",minHeight:"36px"},"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:N.black.dark,color:N.black.dark},backgroundColor:N.hover.light}},fullWidth:!0,disabled:(e==null?void 0:e.disabled)||((p=e.details)==null?void 0:p.visibility)===b.DISPLAY,size:"small",value:l,onChange:me,options:(ee=i==null?void 0:i[e==null?void 0:e.keyName])!=null&&ee.length?i==null?void 0:i[e==null?void 0:e.keyName]:((te=i==null?void 0:i[e==null?void 0:e.keyName])==null?void 0:te[e==null?void 0:e.plantData])||[],required:((ae=e==null?void 0:e.details)==null?void 0:ae.visibility)===b.MANDATORY||((ie=e==null?void 0:e.details)==null?void 0:ie.visibility)==="Required",ListboxComponent:Le,getOptionLabel:a=>a!=null&&a.desc?`${(a==null?void 0:a.code)||""} - ${(a==null?void 0:a.desc)||""}`:`${(a==null?void 0:a.code)||""}`,getOptionDisabled:a=>Me(e==null?void 0:e.keyName,D,a),renderOption:(a,t)=>d(O,{...a,component:"li",style:{fontSize:12,padding:"8px 16px",width:"100%",cursor:"pointer",display:"flex",alignItems:"start"},title:`${P===_.REQUEST_TYPE?"":t==null?void 0:t.code}${t!=null&&t.desc&&P!==_.REQUEST_TYPE?` - ${t==null?void 0:t.desc}`:`${t==null?void 0:t.desc}`}`,children:R("span",{style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:[d("strong",{children:t==null?void 0:t.code}),t!=null&&t.desc&&P!==_.REQUEST_TYPE?` - ${t==null?void 0:t.desc}`:""]})}),renderInput:a=>{var t,g,k;return d(be,{...a,variant:"outlined",placeholder:e!=null&&e.disabled||((t=e.details)==null?void 0:t.visibility)===b.DISPLAY?"":y(`SELECT ${((k=(g=e==null?void 0:e.details)==null?void 0:g.fieldName)==null?void 0:k.toUpperCase())||""}`),error:q.includes((e==null?void 0:e.keyName)||""),InputProps:{...a.InputProps}})}})]})})})}export{qe as A,Ce as S};
