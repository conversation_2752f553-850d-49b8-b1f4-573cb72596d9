import { destination_ServiceRequest } from "../destinationVariables";
//   import { startDate, endDate } from "../components/common/DateRangeByQuarter";
import store from "../app/store";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";

/* Setting Default Dates */
const presentDate = new Date();
const backDate = new Date();
// const appSettings=useSelector((state)=> state.appSettings["Format"])

backDate.setDate(backDate.getDate() - 30);
const DashboardBackDate= new Date()
DashboardBackDate.setDate(DashboardBackDate.getDate() - 9000);
/* Setting Default Dates For Scheduler*/
let presentDateScheduler = new Date() ;
let backDateScheduler = new Date();
presentDateScheduler.setDate(presentDateScheduler.getDate() + 1);
backDateScheduler.setDate(backDateScheduler.getDate() - 7); //chnages Date ta 15/10/2024
let initialState = {
  Dashboard: {
    vendorNo: "",
    companyCode: "",
    dashboardDate: [DashboardBackDate, presentDate],
    selectedusersId:[],
    selectedRegion:"",
    selectedRequestType :[],
    selectedRequestStatus:[],
    selectedRequestTypeSLA:["Create"],
    selectedRequestTypeInGroup:["Create"],
    selectedRequestTypeSLATable:["Create"],
    selectedRequestTypeRole:["Create"],
    tab: 0,
  },
  Reports: {
    reportType: "",
    reportDate: [backDate, presentDate],
    selectedUsers: [],
    selectedStatus: [],
  },
  MaterialMaster: {
    plant:  "",
    group: {
      code: "",
      desc: "",
    },
    "Lab/Office": {
      code: "",
      desc: "",
    },
    "Transportation Group": {
      code: "",
      desc: "",
    },
    "Product Hierarchy": "",
    createdBy: "",
    changedBy: "",
    division:"",
    oldMaterialNumber: "",
    createdOn: [backDate, presentDate],
    number: "",
    type: {
      code: "",
      desc: "",
    },
    salesOrg: {
      code: "",
      desc: "",
    },
    description: "",
    distributionChannel: {
      code: "",
      desc: ""
    }
  },
  DuplicateDesc: {
    group: {
      code: "",
      desc: "",
    },
    baseUnit: {
      code: "",
      desc: "",
    },
    packagingMaterials: {
      code: "",
      desc: "",
    },
    prefix: "",
  },
  NewMaterial: {
    selectViewsValue: [],
    materialNo: "",
    sector: {
      code: "",
      desc: "",
    },
    type: {
      code: "",
      desc: "",
    },
    description: "",
    description1: "",
    copyMaterial: "",
    selectedViews: "Basic data",
    text: "",
    // plant: { selectedPlantNo },
    // salesOrg: { selectedSalesOrg },
    // distributionChannel: { selectedDistributionChannel },
    // storageLocation: { selectedStorageLocation },
    // mrpProfile: { selectedMRPProfile },
    // forecastProfile: { selectedForecastProfile },
    // warehouseNo: { selectedWarehouseNo },
    // storageType: { selectedStorageType },
  },
  DocumentManagement: {
    docName: "",
    transactionId: "",
    requestType:"",
    transactionType: [],
    docType: "",
    uploadedBy: "",
    uploadedDate: [backDate, presentDate],
  },
  Userswb: {
    // taskId:'',
    assignedTo: "",
    date: [backDate, presentDate],
    company: "",
    supplier: "",
    taskName: [],
    taskStatus: [],
    type: [],
    createdBy: "",
  },
  RequestBench: {
    requestId: "",
    childRequestId: "",
    requestType: "",
    createdBy: "",
    createdOn: [backDate, presentDate],
    reqPriority:[],
    reqStatus: [],
    tempName: [],
    userId: "",
    distChnl: "",
    plantOrg: "",
    salesOrg: "",
    controllingArea:"",
    costCenters:""
  },
  SchedulerManager:{
    createdOnScheduler:[backDateScheduler,presentDateScheduler],
  },
  CostCenter: {
    costCenterName: "",
    costCenter:"",
    number:"",
    controllingArea: {
      code: "",
      desc: "",
    },
    companyCode: {
      code: "",
      desc: "",
    },
    profitCenter: {
      code: "",
      desc: "",
    },
    hierarchyArea: {
      code: "",
      desc: "",
    },
    costCenterCategory: {
      code: "",
      desc: "",
    },
  },
  ProfitCenter: {
    controllingArea:"",
    profitCenter:"",
    profitCenterName:"",
    createdBy:"",
    segment:"",
    profitCenterGroup:"",
  },
  BankKey: {
    bankCtrRegion:  {
      code: "",
      desc: "",
    },
    bankKey:"",
    bankName:"",
    bankBranch:"",
    swiftBic:"",
    bankNumber:"",
    createdBy:"",
    changedBy:"",
    createdOn:""
  },
  GeneralLedger :{
    chartOfAccount:{
      code:"",
      desc:""
    },
    companyCode :{
      code:"",
      desc:""
    },
    glAccount:{
      code:"",
      desc:""
    },
    glAccountType:{
      code:"",
      desc:""
    },
    accountGroup:{
      code:"",
      desc:""
    },
    shortText:"",
    createdBy: "",
    glAccountText: {
      code:"",
      desc:""
    },
    groupAccountNumber :{
      code:"",
      desc:""
    },
    accountCurrency:{
      code:"",
      desc:""
    },
    taxCategory:{
      code:"",
      desc:""
    }

  }
};

// Payload Format :
// {
//     module:"PurchaseOrder",
//     filterData:{
// company:"",
// supplier:"",
// date:[]
//     }
// }

const commonFilterSlice = createSlice({
  name: "commonFilterSlice",
  initialState,
  reducers: {
    defaultFilterDataSetup(state, action) {
      initialState = action.payload;
      state = action.payload;
      return state;
    },
    commonFilterUpdate(state, action) {
      state[action.payload["module"]] = {
        ...state[action.payload["module"]],
        ...action.payload["filterData"],
      };
      return state;
    },
    commonFilterClear(state, action) {
      const { module, days } = action.payload;
      var date = new Date();
      var backDate = new Date();
      var presentDate = new Date();
      backDate.setDate(backDate.getDate() - 30);

      const updatedState = {
        ...state,
        [module]: {
          ...initialState[module],
        },
      };

      if (module) {
        updatedState[module] = {
          ...updatedState[module],
          ...(module === "Dashboard"
            ? { dashboardDate: [backDate, presentDate] }
            : {}),
          ...(module === "Reports"
            ? { reportDate: [backDate, presentDate] }
            : {}),
          ...(module === "MaterialMaster"
            ? { createdOn: [backDate, presentDate] }
            : {}),
          ...(module === "NewMaterial"
            ? { createdOn: [backDate, presentDate] }
            : {}),
          ...(module === "DuplicateDesc"
            ? { createdOn: [backDate, presentDate] }
            : {}),
          ...(module === "CostCenter"
            ? { createdOn: [backDate, presentDate] }
            : {}),
            ...(module === "SchedulerManager"
            ? { createdOn: [backDateScheduler, presentDateScheduler] }
            : {}),
          ...(module === "GeneralLedger"
            ? { createdOn: [backDate, presentDate] }
            : {}),
        };
      }

      return updatedState;
    },
    selectedUserIdClear(state, action) {
      const { module, days } = action.payload;
      const updatedState = {
        ...state,
        [module]: {
          ...initialState[module],
        },
      };

      if (module) {
        updatedState[module] = {
          ...updatedState[module],
          ...(module === "Dashboard"
            ? { selectedusersId: [] }
            : {}),
        };
      }

      return updatedState;
    },
    setErrorFields: (state, action) => {
      state.errorFields = action.payload;
      return state;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(fetchPresetsByModule.fulfilled, (state, action) => {
      state[action.payload["module"].preset] = action.payload.data;
      return state;
    });
    builder.addCase(fetchAllPresets.fulfilled, (state, action) => {
      console.log(action.payload);
    });
    builder.addCase(savePreset.fulfilled, (state, action) => {
      console.log(action.payload);
    });
  },
});

export const {
  commonFilterUpdate,
  commonFilterClear,
  defaultFilterDataSetup,
  setErrorFields,
  selectedUserIdClear,
} = commonFilterSlice.actions;

export default commonFilterSlice.reducer;

//Preset Filter Thunks

export const fetchPresetsByModule = createAsyncThunk(
  "preset/fetchByModule",
  async (data) => {
    const module = data;
    return fetch(
      `/${destination_ServiceRequest}/presetFilter/listOfFilters/${module}`
    )
      .then((res) => res.json())
      .then((data) => {
        return {
          module,
          data,
        };
      });
  }
);

export const savePreset = createAsyncThunk(
  "preset/savePreset",
  async (data) => {
    return fetch(
      `/${destination_ServiceRequest}/presetFilter/savePresetFilter`,
      {
        method: "POST",
        body: data,
      }
    )
      .then((res) => res.json())
      .then((data) => {
        return data;
      });
  }
);

export const fetchAllPresets = createAsyncThunk("preset/fetchAll", async () => {
  return fetch(`/${destination_ServiceRequest}/presetFilter/allListOfFilters`)
    .then((res) => res.json())
    .then((data) => {
      return data;
    });
});

// export function fetchPresets(){
//   return async function fetchPresetThunk(dispatch, getState){
//     try{
//       const res = await fetch(`/${destination_ServiceRequest}/presetFilter/listOfFilters/{module}`);
//       const data = await res.json()
//       const state = getState()
//       let tempFilterData = { ...state[], poStatus };
//       dispatch(commonFilterUpdate({module:"PurchaseOrder",filterData:tempFilterData}));commonFilterUpdate
//     }catch(err){

//     }
//   }
// }
