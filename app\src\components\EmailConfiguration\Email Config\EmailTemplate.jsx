import React, { useEffect, useState } from "react";
import Box from "@mui/material/Box";
import Tab from "@mui/material/Tab";
import EmailOutlinedIcon from '@mui/icons-material/EmailOutlined';
import Button from "@mui/material/Button";
import Stack from "@mui/material/Stack";
import BusyLoader from "@mui/material/CircularProgress";
import ReorderOutlinedIcon from "@mui/icons-material/ReorderOutlined";
import AllTemplate from "./AllTemplate";
import ActiveTemplate from "./ActiveTemplate";
import Draft from "./Draft";
import { useNavigate } from "react-router-dom";
import { doAjax, doCrudApi } from "../utility/serviceRequest";
import { useDispatch, useSelector } from "react-redux";
import { Provider } from "react-redux";
import { makeStyles } from "@mui/styles";
import { setAPIBaseUrl, setConfigs, setToken, setfeature } from "../redux/reducers/userReducer";
import store from "../redux/store";
import CreateTemplate from "./CreateTemplate";
import ManageGroup from "./ManageGroup";
import ManageTemplate from "./ManageTemplate";
import { Backdrop, BottomNavigation, CircularProgress, Grid, Paper, Tabs, TextField, Typography } from "@mui/material";
import EditNoteIcon from "@mui/icons-material/EditNote";
import SearchIcon from '@mui/icons-material/Search';
import { outerContainer_Information, outermostContainer, outermostContainer_Information } from "../../common/commonStyles";
import ReusablePromptBox from "../component/PromptBox_Email/ReusablePromptBox";
import useLang from "@hooks/useLang";
import "./EmailConfig.css";
// import { Routes, Route, useNavigate ,BrowserRouter as Router} from "react-router-dom";

// import TemplateCreation from "./TemplateCreation"
import mailDefinitionResponse from "../../../data/demoData/mailDefinition";

const EmailTemplateWrapper = (props) => {
  return (
    <Provider store={store}>
      {/* <Router> */}

      <EmailTemplate {...props} />
      {/* </Router> */}
    </Provider>
  );
};
export default React.memo(EmailTemplateWrapper);

function EmailTemplate({ token, destinations, feature, environment, useWorkAccess, useConfigServerDestination, userId, applicationName, applicationId, needHeading, showManageGroups, useCrud, ...props }) {
  const userReducer = useSelector((state) => state.userReducer);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [searchParam, setSearchParam] = React.useState("");
  const [value, setValue] = React.useState("1");
  const [showConfirmation, setShowConfirmation] = React.useState(false);
  const [mailDefination, setMailDefination] = React.useState([]);
  const [unorderedMailDefination, setUnorderedMailDefination] = React.useState([]);
  const [active, setActive] = React.useState([]);
  const [unorderedActive, setUnorderedActive] = React.useState([]);
  const [draft, setDraft] = React.useState([]);
  const [unordereddraft, setUnorderedDraft] = React.useState([]);
  const [isLoader, setLoader] = React.useState(false);
  const [selectedRow, setSelectedRow] = React.useState(null);
  const [creationType, setCreationType] = React.useState("new");
  const [scenario, setScenario] = React.useState("INITIAL");

  const [filteredData, setFilteredData] = React.useState([]);
  const [openAlert, setAlert] = React.useState(false);
  const [alertMessage, setAlertMessage] = React.useState("");
  const [alertSeverity, setAlertSeverity] = React.useState("success");
  const [isEditing, setIsEditing] = React.useState(null);
  const [confirmationMessage, setConfirmationMessage] = React.useState("");
  const [buttonAction, setButtonAction] = React.useState("Cancel");
  const [openCreateTemplate, setOpenCreateTemplate] = useState(false);
  const [openEditTemplate, setOpenEditTemplate] = useState(false);

  const [openManageGroup, setManageGroup] = useState(false);
  const [openManageTemplate, setOpenManageTemplate] = useState(false);
  const [mailmappingData, setMailmappingData] = useState([]);
  const [userDetails, setUserDetails] = useState(new Map());
  const [groupDetails, setGroupDetails] = useState(new Map());
  const [ccToParticipant, setCcToParticipant] = useState([]);
  const [toparticipant, setToparticipant] = useState([]);
  const [emailTemplateData, setEmailTemplateData] = useState([]);
  const [groupData, setGroupData] = useState([]);
  const [allGroups, setAllGroups] = useState([]);
  const [isEmailTemplate, setIsEmailTemplate] = useState(true);
  const { t } = useLang();


  const handleManageGroupClick = () => {
    if (showManageGroups) {
      navigate(props?.pathName);
      console.log(props?.pathName);
      setIsEmailTemplate(false);
    } else {
      setManageGroup(true);
      setIsEmailTemplate(false);
    }
    setScenario("MANAGE_GROUPS");
  };

  const handleManageTemplateClick = () => {
    // if (applicationName === "ITM") {
    //   navigate(props?.pathName);
    // } else {
    setOpenManageTemplate(true);
    setIsEmailTemplate(false);
    setScenario("ASSOCIATED_TEMPLATE");
  };

  const handletemplateclose = () => {
    setOpenManageTemplate(false);
    setIsEmailTemplate(true);
  };

  const handleGroupClose = () => {
    setManageGroup(false);
    setIsEmailTemplate(true);
  };

  const handleButtonClick = () => {
    setOpenCreateTemplate(true);
    setIsEmailTemplate(false);
    setCreationType("new");
    setScenario("CREATE");
  };

  const handleDialogClose = () => {
    setOpenCreateTemplate(false);
    setIsEmailTemplate(true);
  };

  useEffect(() => {
    if (mailDefination.length && mailmappingData.length) combineObjectsById();
  }, [mailDefination, mailmappingData]);

  function combineObjectsById() {
    var array1 = mailDefination;
    var array2 = mailmappingData;

    const generatedCode = [];

    array1.forEach((obj1) => {
      const matchingObj2 = array2.find((obj2) => obj1.emailDefinitionId === obj2.templateId);
      const code = {
        ...obj1,
        toList: matchingObj2?.toList ?? "",
        toParticipant: matchingObj2?.toParticipant ?? "",
        toParticipantType: matchingObj2?.toParticipantType ?? "",
        ccList: matchingObj2?.ccList ?? "",
        ccParticipant: matchingObj2?.ccParticipant ?? "",
        ccParticipantType: matchingObj2?.ccParticipantType ?? "",
      };
      generatedCode.push(code);
    });
    setEmailTemplateData(generatedCode);
    setActive(generatedCode.filter((ele) => ele.status === "Active"));
    setDraft(generatedCode.filter((ele) => ele.status === "Draft"));
  }

  const getMailDefinition = () => {
    setLoader(true);
    if (!destinations) {
      setLoader(false);
      return;
    }

    // For development/testing, use mock data
    // if (process.env.NODE_ENV === "development") {
    //   const oData = mailDefinitionResponse;
    //   const allData = oData.data || [];
    //   const mdgBatchTemplates = allData.filter((ele) => ele.identifierDesc === "MDG_BATCH");

    //   setMailDefination(mdgBatchTemplates);
    //   setUnorderedMailDefination(mdgBatchTemplates);

    //   const active = mdgBatchTemplates.filter((ele) => ele.status === "Active");
    //   const draft = mdgBatchTemplates.filter((ele) => ele.status === "Draft");

    //   setActive(active);
    //   setUnorderedActive(active);
    //   setDraft(draft);
    //   setUnorderedDraft(draft);

    //   setLoader(false);
    //   return;
    // }

    // Original API call for production
    doAjax(
      `/WorkUtilsServices/v2/mail-definition`,
      "get",
      null,
      function (oData) {
        if (oData.statusCode === 401 || oData.statusCode === "401") {
          setShowConfirmation(true);
          setButtonAction("Timeout");
          setConfirmationMessage("Session Timed Out. Kindly Refresh");
          setLoader(false);
          return;
        }
  
        const allData = oData.data || [];
        const mdgBatchTemplates = allData.filter(
          (ele) => ele.identifierDesc === "MDG_BATCH"
        );
  
        setMailDefination(mdgBatchTemplates);
        setUnorderedMailDefination(mdgBatchTemplates);
  
        const active = mdgBatchTemplates.filter((ele) => ele.status === "Active");
        const draft = mdgBatchTemplates.filter((ele) => ele.status === "Draft");
  
        setActive(active);
        setUnorderedActive(active);
        setDraft(draft);
        setUnorderedDraft(draft);
  
        setLoader(false);
      },
      function (error) {
        setAlert(true);
        setAlertSeverity("error");
        setAlertMessage(error);
        setLoader(false);
      }
    );
  };
  React.useEffect(() => {
    if (Boolean(mailDefination?.length)) TemplateGroupData();
  }, [mailDefination]);
  React.useEffect(() => {
    dispatch(
      setConfigs({
        useWorkAccess: useWorkAccess,
        useConfigServerDestination: useConfigServerDestination,
        userId: userId,
        applicationName: applicationName,
        applicationId: applicationId,
        useCrud,
      })
    );
    dispatch(setfeature(feature));
    Promise.all([dispatch(setToken(token)), dispatch(setAPIBaseUrl({ destinations: destinations, environment: environment }))])
      .then(([]) => {
        getMailDefinition();
        TemplateGroupData();
      })
      .catch((error) => { });
    // eslint-disable-next-line
  }, [userReducer?.refreshTemplates]);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  const getSearchedParams = (value) => {
    setFilteredData(mailDefination.filter((ele) => ele.name.toLowerCase().includes(value.toLowerCase()) || ele.entityDesc.toLowerCase().includes(value.toLowerCase()) || ele.processDesc.toLowerCase().includes(value.toLowerCase())));
  };

  const sortingTemplate = (array, property, order, setArray, setArrayactive, setArraydraft) => {
    let temp = [...array];
    temp.sort(function (x, y) {
      if (order === "ascending") {
        return x[property] - y[property];
      } else {
        return y[property] - x[property];
      }
    });
    setArray(temp);
    setArrayactive(temp.filter((ele) => ele.status === "Active"));
    setArraydraft(temp.filter((ele) => ele.status === "Draft"));
  };

  const TemplateGroupData = () => {
    // setLoader(true);
    // if (applicationName === "ITM")

    doCrudApi(
      "fetchMailMappingHana",
      [],

      function (oData) {
        if (oData.statusCode === 401 || oData.statusCode === "401") {
          setShowConfirmation(true);
          setButtonAction("Timeout");
          setConfirmationMessage("Session Timed Out.Kindly Refresh");
        }
        if (oData) {
          // oData.forEach((e) => {
          //   e["isRowEditable"] = false;
          //   e["isEdited"] = false;
          // });
          setMailmappingData(oData);
        }
        // setLoader(false);
      },
      function (error) {
        setAlert(true);
        setAlertSeverity("error");
        setAlertMessage(error);
        setLoader(false);
      }
    );

    //  else {
    //   doAjax(
    //     `/WorkUtilsServices/v1/mail-mapping`,
    //     "get",
    //     null,
    //     function (oData) {
    //       if (oData.statusCode === 401 || oData.statusCode === "401") {
    //         setShowConfirmation(true);
    //         setButtonAction("Timeout");
    //         setConfirmationMessage("Session Timed Out.Kindly Refresh");
    //       }
    //       if (!oData.data) {
    //         oData.data = [];
    //       } else {
    //         // oData.data.forEach((e) => {
    //         //   e["isRowEditable"] = false;
    //         //   e["isEdited"] = false;
    //         // });
    //         setMailmappingData(oData.data);
    //       }
    //       setLoader(false);
    //     },
    //     function (error) {
    //       setAlert(true);
    //       setAlertSeverity("error");
    //       setAlertMessage(error);
    //       // setOpenalert(true);
    //       // setEmailError("error");
    //       // setSeverity("error");
    //       setLoader(false);
    //     }
    //   );
    // }
  };
  const content = () => {
    switch (scenario) {
      case "EDIT":
        return <CreateTemplate headers={props?.headers} isRecepientData={props?.isRecepientData} open={openCreateTemplate} onClose={handleDialogClose} {...props} getMailDefinition={getMailDefinition} data={selectedRow} creationType={creationType} setSelectedRow={setSelectedRow} setCreationType={setCreationType} setAlert={setAlert} setAlertMessage={setAlertMessage} setAlertSeverity={setAlertSeverity} setIsEditing={setIsEditing} TemplateGroupData={TemplateGroupData} userList={props?.userList} groupList={props?.groupList} contentHTML={props?.contentHTML} setScenario={setScenario} />;
      case "INITIAL":
        return (
          <Stack>
            <Stack spacing={1}>
              {/* Information */}
              <Grid container sx={{ ...outermostContainer_Information }}>
                <Grid item md={5} xs={12} sx={outerContainer_Information}>
                  <Typography variant="h3">
                    <strong>{t("Email Template Configurations")}</strong>
                  </Typography>
                  <Typography variant="body2" color="#777">
                    {t("This view allows the user to create and display Email Templates")}
                  </Typography>
                </Grid>
                <Grid item md={7} xs={12} sx={{ 
                  display: 'flex',
                  justifyContent: 'flex-end',
                  alignItems: 'center'
                }}>
                  <Box sx={{ 
                    maxWidth: 200,
                    marginLeft: 'auto',
                  }}>
                    <TextField
                      fullWidth
                      size="small"
                      placeholder={t("Search Templates")}
                      variant="outlined"
                      value={searchParam}
                      onChange={(e) => {
                        setSearchParam(e.target.value);
                        getSearchedParams(e.target.value);
                      }}
                      InputProps={{
                        startAdornment: <SearchIcon sx={{ color: "action.active" }} />,
                      }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: '10px', // More curved corners
                          backgroundColor: 'white',
                          '&:hover .MuiOutlinedInput-notchedOutline': {
                            borderColor: 'primary.main',
                          },
                          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                            borderColor: 'primary.main',
                            borderWidth: '2px',
                          },
                        },
                        '& .MuiOutlinedInput-input': {
                          padding: '10px 14px',
                        },
                      }}
                    />
                  </Box>
                </Grid>
              </Grid>
            </Stack>
            <Paper sx={{ position: "fixed", bottom: 0, left: 0, right: 0, zIndex: 2 }} elevation={2}>
              <BottomNavigation className="container_BottomNav">
                {/* NOTE: Might be required in future in case associated template feature might be required templates need to be shown */}
                {/* {props?.isAssociatedTemplate && (
                <Button size="small"
                  variant="outlined"
                  className='btn-ml' onClick={handleManageTemplateClick} >
                  Associated Template
                </Button>
              )} */}

                {feature.EMAIL_CONFIG_CREATE === "True" && (
                  <Button
                    size="small"
                    variant="contained"
                    className="btn-ml"
                    // onClick={handleClickOpenCreateInvoice}
                    onClick={handleButtonClick}
                  >
                    {t("Create New Template")}
                  </Button>
                )}
                {/* {showManageGroups && */}
                {/* {feature.EMAIL_CONFIG_MANAGE_GROUPS === "True" && (<Button size="small"
                variant="contained"
                className='btn-ml btn-mr' onClick={handleManageGroupClick} >
                Manage Groups
              </Button>)} */}
                {/* } */}
              </BottomNavigation>
            </Paper>
            {isEmailTemplate && (
              <>
                {feature.EMAIL_CONFIG_SUMMARY === "True" && (
                  <Box borderBottom={1} sx={{ borderColor: "divider", marginBottom: "1rem" }}>
                    <Tabs value={value} onChange={handleChange}>
                      {feature.EMAIL_CONFIG_SUMMARY_ACTIVE === "True" && (
                        <Tab
                          label={
                            <Stack
                              direction="row"
                              sx={{
                                alignItems: "center",
                              }}
                            >
                              <EmailOutlinedIcon sx={{ fontSize: "15px" }} />

                              <Typography
                                variant="body1"
                                ml={1}
                                sx={{
                                  fontWeight: 600,
                                  fontSize: "14px",
                                }}
                              >
                                {t("Active Template")}
                              </Typography>
                            </Stack>
                          }
                          value="1"
                          sx={{
                            fontWeight: "700",
                            fontSize: "14px",
                            textTransform: "none",
                          }}
                        />
                      )}
                      {feature.EMAIL_CONFIG_SUMMARY_DRAFT === "True" && (
                        <Tab
                          label={
                            <Stack
                              direction="row"
                              sx={{
                                alignItems: "center",
                              }}
                            >
                              <EditNoteIcon sx={{ fontSize: "15px" }} />

                              <Typography
                                variant="body1"
                                ml={1}
                                sx={{
                                  fontWeight: 600,
                                  fontSize: "14px",
                                }}
                              >
                                {t("Draft")}
                              </Typography>
                            </Stack>
                          }
                          value="2"
                          sx={{
                            fontWeight: "700",
                            fontSize: "14px",
                            textTransform: "none",
                          }}
                        />
                      )}
                      {/* NOTE: Might be required in future in case all templates need to be shown */}
                      {/* <Tab
                      label={
                        <Stack
                          direction="row"
                          sx={{
                            alignItems: "center",
                          }}
                        >
                          <ReorderOutlinedIcon sx={{ fontSize: "15px" }} />

                          <Typography
                            variant="body1"
                            ml={1}
                            sx={{
                              fontWeight: 600,
                              fontSize: "14px",
                            }}
                          >
                            All Template
                          </Typography>
                        </Stack>
                      }
                      value="3"
                      sx={{
                        fontWeight: "700",
                        fontSize: "14px",
                        textTransform: "none",
                      }}
                    /> */}
                    </Tabs>
                  </Box>
                )}

                <Stack>
                  {value === "1" && (
                    <ActiveTemplate
                      setScenario={setScenario}
                      destinations={destinations}
                      setCreationType={setCreationType}
                      applicationName={applicationName}
                      setSelectedRow={setSelectedRow}
                      setShowConfirmation={setShowConfirmation}
                      setIsEditing={setIsEditing}
                      isEditing={isEditing}
                      setButtonAction={setButtonAction}
                      setConfirmationMessage={setConfirmationMessage}
                      filteredData={filteredData}
                      active={active}
                      mailDefination={mailDefination}
                      mailmappingData={mailmappingData}
                      searchParam={searchParam}
                      setOpenCreateTemplate={setOpenCreateTemplate}
                      // groupDetails={groupDetails}
                      // userDetails={userDetails}
                      ccToParticipant={ccToParticipant}
                      toparticipant={toparticipant}
                      emailTemplateData={emailTemplateData}
                      groupList={props?.groupList}
                      userList={props?.userList}
                      isLoading={isLoader}
                      allGroups={allGroups}
                      headers={props?.headers}
                    />
                  )}
                </Stack>
                <Stack>
                  {value === "2" && (
                    <Draft
                      destinations={destinations}
                      setCreationType={setCreationType}
                      applicationName={applicationName}
                      setSelectedRow={setSelectedRow}
                      setShowConfirmation={setShowConfirmation}
                      draft={draft}
                      setIsEditing={setIsEditing}
                      isEditing={isEditing}
                      setButtonAction={setButtonAction}
                      setConfirmationMessage={setConfirmationMessage}
                      filteredData={filteredData}
                      mailDefination={mailDefination}
                      searchParam={searchParam}
                      setOpenCreateTemplate={setOpenCreateTemplate}
                      // groupDetails={groupDetails}
                      // userDetails={userDetails}
                      mailmappingData={mailmappingData}
                      emailTemplateData={emailTemplateData}
                      groupList={props?.groupList}
                      userList={props?.userList}
                      isLoading={isLoader}
                      allGroups={allGroups}
                      headers={props?.headers}
                      setScenario={setScenario}
                    />
                  )}
                </Stack>

                <Stack>
                  {value === "3" && (
                    <AllTemplate
                      destinations={destinations}
                      setCreationType={setCreationType}
                      applicationName={applicationName}
                      setSelectedRow={setSelectedRow}
                      setScenario={setScenario}
                      setShowConfirmation={setShowConfirmation}
                      setIsEditing={setIsEditing}
                      isEditing={isEditing}
                      setButtonAction={setButtonAction}
                      setConfirmationMessage={setConfirmationMessage}
                      filteredData={filteredData}
                      mailDefination={mailDefination}
                      searchParam={searchParam}
                      setOpenCreateTemplate={setOpenCreateTemplate}
                      // groupDetails={groupDetails}
                      // userDetails={userDetails}
                      mailmappingData={mailmappingData}
                      emailTemplateData={emailTemplateData}
                      groupList={props?.groupList}
                      userList={props?.userList}
                      isLoading={isLoader}
                      allGroups={allGroups}
                      headers={props?.headers}
                    />
                  )}
                </Stack>
              </>
            )}
          </Stack>
        );
      case "CREATE":
        return (
          <CreateTemplate
            scenario={scenario}
            headers={props?.headers}
            isRecepientData={props?.isRecepientData}
            open={openCreateTemplate}
            onClose={handleDialogClose}
            {...props}
            getMailDefinition={getMailDefinition}
            data={selectedRow}
            creationType={creationType}
            setSelectedRow={setSelectedRow}
            setCreationType={setCreationType}
            setAlert={setAlert}
            setAlertMessage={setAlertMessage}
            setAlertSeverity={setAlertSeverity}
            setIsEditing={setIsEditing}
            TemplateGroupData={TemplateGroupData}
            userList={props?.userList}
            groupList={props?.groupList}
            contentHTML={props?.contentHTML}
            setScenario={setScenario}
          />
        );
      case "MANAGE_GROUPS":
        return <ManageGroup headers={props?.headers} open={openManageGroup} onClose={handleGroupClose} {...props} getMailDefinition={getMailDefinition} data={selectedRow} creationType={creationType} setSelectedRow={setSelectedRow} setCreationType={setCreationType} setAlert={setAlert} setAlertMessage={setAlertMessage} setAlertSeverity={setAlertSeverity} setIsEditing={setIsEditing} setScenario={setScenario} isAssociatedTemplate={props?.isAssociatedTemplate} />;
      case "ASSOCIATED_TEMPLATE":
        return <ManageTemplate headers={props?.headers} open={openManageTemplate} onClose={handletemplateclose} {...props} getMailDefinition={getMailDefinition} data={selectedRow} creationType={creationType} setSelectedRow={setSelectedRow} setCreationType={setCreationType} setAlert={setAlert} setAlertMessage={setAlertMessage} setAlertSeverity={setAlertSeverity} setIsEditing={setIsEditing} userList={props?.userList} groupList={props?.groupList} setScenario={setScenario} promptAction_Functions={promptAction_Functions} />;
    }
  };
  //   setLoader(true);
  //   doAjax(
  //     `/IWAServices/api/v1/users?applicationId=${applicationId}`,
  //     "get",
  //     null,
  //     function (oData) {
  //       if (oData.statusCode === 401 || oData.statusCode === "401") {
  //         setShowConfirmation(true);
  //         setButtonAction("Timeout");
  //         setConfirmationMessage("Session Timed Out.Kindly Refresh");
  //       }
  //       // if (oData.data) setter(oData.data);
  //       if (oData.data) {
  //         let transFormData = oData.data.map((ele) => {
  //           userDetails.set(ele.emailId, ele.userName);
  //           return { code: ele.userName, description: ele.emailId };
  //         });
  //         setUserDetails(new Map(userDetails));
  //         // if (json.data) {
  //         //   let transFormData = json.data.map((ele) => ({
  //         //     code: ele.userName,
  //         //     description: ele.emailId,
  //         //   }));
  //         setter(transFormData);
  //       }
  //       setLoader(false);
  //     },
  //     function (error) {
  //       setAlert(true);
  //       setAlertSeverity("error");
  //       setAlertMessage(error);
  //       // setOpenalert(true);
  //       // setEmailError("error");
  //       // setSeverity("error");
  //       setLoader(false);
  //     }
  //   );
  // };

  // const getParticipantList = (app, prtc, setter) => {
  //   setLoader(true);
  //   if (app === "ITM") {
  //     doAjax(
  //       "/IWAServices/api/v1/groups",
  //       "get",
  //       null,
  //       function (oData) {
  //         if (oData.statusCode === 401 || oData.statusCode === "401") {
  //           setShowConfirmation(true);
  //           setButtonAction("Timeout");
  //           setConfirmationMessage("Session Timed Out.Kindly Refresh");
  //         }

  //         if (oData.data) {
  //           let transFormData = oData.data.map((ele) => {
  //             groupDetails.set(ele.id, ele.name);
  //             return { code: ele.name, description: ele.id };
  //           });
  //           setGroupDetails(new Map(groupDetails));
  //           setter(transFormData);
  //         }
  //         setLoader(false);
  //       },
  //       function (error) {
  //         setAlert(true);
  //         setAlertSeverity("error");
  //         setAlertMessage(error);
  //         // setOpenalert(true);
  //         // setEmailError("error");
  //         // setSeverity("error");
  //         setLoader(false);
  //       }
  //     );
  //   } else {
  //     doAjax(
  //       "/WorkUtilsServices/v1/mail-mapping/particpants?applicationId=" + app + "&toParticipantType=" + prtc,
  //       "get",
  //       null,
  //       function (oData) {
  //         if (oData.statusCode === 401 || oData.statusCode === "401") {
  //           setShowConfirmation(true);
  //           setButtonAction("Timeout");
  //           setConfirmationMessage("Session Timed Out.Kindly Refresh");
  //         }
  //         if (oData.data) setter(oData.data);
  //         setLoader(false);
  //       },
  //       function (error) {
  //         setAlert(true);
  //         setAlertSeverity("error");
  //         setAlertMessage(error);
  //         // setOpenalert(true);
  //         // setEmailError("error");
  //         // setSeverity("error");
  //         setLoader(false);
  //       }
  //     );
  //   }
  // };

  //<-- Functions and variables for ReusablePromptBox *promptAction_Functions -->
  const [promptBoxState, setPromptBoxState] = useState({
    open: false,
    type: "",
    redirectOnClose: true,
    message: "",
    title: "",
    severity: "",
  });
  const [promptBoxScenario, setPromptBoxScenario] = useState("");

  const promptAction_Functions = {
    handleClosePromptBox: () => {
      setPromptBoxState((prev) => ({
        open: false,
        type: "",
        redirectOnClose: true,
        message: "",
        title: "",
        severity: "",
      }));
      setPromptBoxScenario("");
    },
    handleOpenPromptBox: (ref, data = {}) => {
      // SUCCESS,FAILURE,WARNING,QUANTITYERROR,
      let initialData = {
        open: true,
        title: "",
        message: "",
        okButton: true,
        cancelButton: true,
        okText: "Ok",
        cancelText: "Cancel",
        type: "dialog",
      };
      if (ref === "SUCCESS") {
        initialData.type = "snackbar";
      }
      setPromptBoxScenario(ref);
      setPromptBoxState({
        ...initialData,
        ...data,
      });
    },
    handleCloseAndRedirect: () => {
      promptAction_Functions.handleClosePromptBox();
      navigate("/purchaseManagement/purchaseOrder");
    },
    getCancelFunction: () => {
      switch (promptBoxScenario) {
        default:
          return () => {
            promptAction_Functions.handleClosePromptBox();
          };
      }
    },
    getCloseFunction: () => {
      switch (promptBoxScenario) {
        case "COMMENTERROR":
        default:
          return (value) => {
            promptAction_Functions.handleClosePromptBox();
          };
      }
    },
    getOkFunction: () => {
      switch (promptBoxScenario) {
        case "CONFIRMDELETE_PROCESS":
          return () => deleteProcess();
        case "CONFIRMDELETE_METADATA":
          return () => deleteMetaData();
        default:
          return () => promptAction_Functions.handleClosePromptBox();
      }
    },
    getCloseAndRedirectFunction: () => {
      if (!promptBoxState.redirectOnClose) {
        return promptAction_Functions.handleClosePromptBox;
      }
      return promptAction_Functions.handleCloseAndRedirect;
    },
  };

  let handleConfirmation = (ref) => {
    switch (ref) {
      case "CONFIRM_DISCARD":
        promptAction_Functions.handleOpenPromptBox("CONFIRM_DISCARD", {
          title: "Confirm Discard",
          message: `Confirming would discard all the changes made`,
          severity: "warning",
          cancelButton: true,
          okButton: true,
          okButtonText: "Delete",
        });
    }
  };

  const getGroupdefinitiondata = () => {
    setLoader(true);

    doCrudApi(
      "fetchMailGroupingHana",
      [],
      function (oData) {
        console.log(oData);
        if (oData.statusCode === 401 || oData.statusCode === "401") {
          // setShowConfirmation(true);
          // setButtonAction("Timeout");
          // setConfirmationMessage("Session Timed Out.Kindly Refresh");
        }
        if (oData) {
          let transFormData = oData.map((ele) => {
            return { id: ele.id, name: ele.groupName, userIdList: ele.email };
          });

          setGroupData(transFormData);
          console.log(props?.groupList, "props?.groupList");

          let result = transFormData.concat(props?.groupList);
          let transFormgroupData1 = result.map((ele, index) => {
            groupDetails.set(ele.id, ele.name);
            return { id: ele.id, name: ele.name };
            // return ele.name;
          });
          setGroupDetails(new Map(groupDetails));

          setAllGroups(transFormgroupData1);

          // Array.prototype.push.apply(groupData,props?.groupList);
          // console.log(groupData);
          //   oData.forEach((e) => (e["isRowEditable"] = false));
          // setGroupData(oData);
          // let merged = [...props?.groupList, ...groupData];
          // setAllGroups(merged);
        }
        setLoader(false);
      },
      function (error) {
        // setEmailError("Error");
        // setSeverity("error");
        // handleClickalert();
        // setLoader(false);
        // setAlert(true);
        // setAlertSeverity("error");
        // setAlertMessage(error);
        setAlert(true);
        setAlertSeverity("error");
        setAlertMessage(error);
      }
    );
  };
  useEffect(() => {
    getGroupdefinitiondata();
  }, [props?.groupList]);
  useEffect(() => {
    getGroupdefinitiondata();
    if (userReducer.applicationName !== "") {
      // getGroupdefinitiondata();
      // console.log(userReducer.applicationName,'appname');
    }
  }, [userReducer]);

  return destinations === null ? (
    <Backdrop className="backdrop" open={true}>
      <CircularProgress color="primary" />
    </Backdrop>
  ) : (
    <>
      <ReusablePromptBox
        type={promptBoxState.type}
        promptState={promptBoxState.open}
        setPromptState={promptAction_Functions.handleClosePromptBox}
        onCloseAction={promptAction_Functions.getCloseFunction()}
        promptMessage={promptBoxState.message}
        dialogSeverity={promptBoxState.severity}
        dialogTitleText={promptBoxState.title}
        handleCancelButtonAction={promptAction_Functions.getCancelFunction()}
        cancelButtonText={promptBoxState.cancelText} //Cancel button display text
        showCancelButton={promptBoxState.cancelButton} //Enable Cancel button
        handleSnackBarPromptClose={promptAction_Functions.getCloseAndRedirectFunction()}
        handleOkButtonAction={promptAction_Functions.getOkFunction()}
        okButtonText={promptBoxState.okButtonText}
        showOkButton={promptBoxState.okButton}
      />
      <Stack sx={{ ...outermostContainer, minHeight: "100vh", height: "max-content", backgroundColor: (theme) => theme.background.default }}>{content()}</Stack>
    </>
  );
}
