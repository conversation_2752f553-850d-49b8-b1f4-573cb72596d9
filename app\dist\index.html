<!DOCTYPE html>

<html lang="en">

<head>

  <meta charset="utf-8" />

  <meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="theme-color" content="#000000" />

  <meta name="description" content="Web site created using create-react-app" />

  <link rel="icon" href="/favicon.ico" />

  <!--

      manifest.json provides metadata used when your web app is installed on a

      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/

    -->

  <link rel="manifest" href="./manifest.json" />

  <!--

      Notice the use of %PUBLIC_URL% in the tags above.

      It will be replaced with the URL of the `public` folder during the build.

      Only files inside the `public` folder can be referenced from the HTML.




      Unlike "/favicon.ico" or "favicon.ico", "./src/favicon.ico" will

      work correctly both with client-side routing and a non-root public URL.

      Learn how to configure a non-root public URL by running `npm run build`.

    -->

  <title>Master Data Governance</title>

  <script type="module" crossorigin src="/assets/index-17b8d91e.js"></script>
  <link rel="stylesheet" href="/assets/index-a517bf54.css">
</head>

<body>

  <noscript>You need to enable JavaScript to run this app.</noscript>

  <div id="root"></div>
  <div id="portals"></div>

  
  <script src="https://cdnjs.cloudflare.com/ajax/libs/underscore.js/1.6.0/underscore.js" type="text/javascript"></script>
  <script> const global = globalThis; </script>
</body>

</html>