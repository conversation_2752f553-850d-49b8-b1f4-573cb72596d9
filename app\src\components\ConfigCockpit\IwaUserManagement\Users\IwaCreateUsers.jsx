import { AddUser } from "@cw/adduser";
import { Stack } from "@mui/material";
import { useNavigate } from "react-router-dom";
import { useSnackbar } from "../../../../hooks/useSnackbar";
import {APP_END_POINTS} from "../../../../constant/appEndPoints";

const IwaCreateUsers = () => {
  const { showSnackbar } = useSnackbar();
  const navigate = useNavigate();
  const onUserActionClick = (action, response) => {
    if (action === "usersummary") {
      navigate(APP_END_POINTS.IWA_USER_MANAGEMENT.USERS_SUMMARY);
    }
    if (response) {
      if (response?.status === "success" || response?.status === "SUCCESS" || response?.status === "Success") {
        showSnackbar(response.message, "info");
        return;
      }
      showSnackbar(response?.err?.data?.message, "error");
    }
  };

  return (
    <Stack>
      <AddUser onUserActionClick={onUserActionClick} />
    </Stack>
  );
};

export default IwaCreateUsers;
