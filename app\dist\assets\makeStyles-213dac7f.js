import{cE as p,r as b,et as Xe,eu as Ne,cI as W,d$ as Te,ev as me,ew as Ye,o as et}from"./index-17b8d91e.js";import{_ as tt}from"./toConsumableArray-c7e4bd84.js";function O(i){if(typeof i!="object"||i===null)return!1;const t=Object.getPrototypeOf(i);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in i)&&!(Symbol.iterator in i)}function Ve(i){if(b.isValidElement(i)||!O(i))return i;const t={};return Object.keys(i).forEach(r=>{t[r]=Ve(i[r])}),t}function te(i,t,r={clone:!0}){const e=r.clone?p({},i):i;return O(i)&&O(t)&&Object.keys(t).forEach(n=>{b.isValidElement(t[n])?e[n]=t[n]:O(t[n])&&Object.prototype.hasOwnProperty.call(i,n)&&O(i[n])?e[n]=te(i[n],t[n],r):r.clone?e[n]=O(t[n])?Ve(t[n]):t[n]:e[n]=t[n]}),e}function rt(i){let t="https://mui.com/production-error/?code="+i;for(let r=1;r<arguments.length;r+=1)t+="&args[]="+encodeURIComponent(arguments[r]);return"Minified MUI error #"+i+"; visit "+t+" for the full message."}function J(i){if(typeof i!="string")throw new Error(rt(7));return i.charAt(0).toUpperCase()+i.slice(1)}const nt=["checked","disabled","error","focused","focusVisible","required","expanded","selected"];function it(i={}){const{disableGlobal:t=!1,productionPrefix:r="jss",seed:e=""}=i,n=e===""?"":`${e}-`;let s=0;const a=()=>(s+=1,s);return(o,l)=>{const f=l.options.name;if(f&&f.indexOf("Mui")===0&&!l.options.link&&!t){if(nt.indexOf(o.key)!==-1)return`Mui-${o.key}`;const c=`${n}${f}-${o.key}`;return!l.options.theme[Xe]||e!==""?c:`${c}-${a()}`}return`${n}${r}${a()}`}}var be=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(i){return typeof i}:function(i){return i&&typeof Symbol=="function"&&i.constructor===Symbol&&i!==Symbol.prototype?"symbol":typeof i},G=(typeof window>"u"?"undefined":be(window))==="object"&&(typeof document>"u"?"undefined":be(document))==="object"&&document.nodeType===9,st={}.constructor;function re(i){if(i==null||typeof i!="object")return i;if(Array.isArray(i))return i.map(re);if(i.constructor!==st)return i;var t={};for(var r in i)t[r]=re(i[r]);return t}function de(i,t,r){i===void 0&&(i="unnamed");var e=r.jss,n=re(t),s=e.plugins.onCreateRule(i,n,r);return s||(i[0],null)}var Re=function(t,r){for(var e="",n=0;n<t.length&&t[n]!=="!important";n++)e&&(e+=r),e+=t[n];return e},w=function(t){if(!Array.isArray(t))return t;var r="";if(Array.isArray(t[0]))for(var e=0;e<t.length&&t[e]!=="!important";e++)r&&(r+=", "),r+=Re(t[e]," ");else r=Re(t,", ");return t[t.length-1]==="!important"&&(r+=" !important"),r};function I(i){return i&&i.format===!1?{linebreak:"",space:""}:{linebreak:`
`,space:" "}}function $(i,t){for(var r="",e=0;e<t;e++)r+="  ";return r+i}function V(i,t,r){r===void 0&&(r={});var e="";if(!t)return e;var n=r,s=n.indent,a=s===void 0?0:s,o=t.fallbacks;r.format===!1&&(a=-1/0);var l=I(r),f=l.linebreak,c=l.space;if(i&&a++,o)if(Array.isArray(o))for(var h=0;h<o.length;h++){var y=o[h];for(var g in y){var v=y[g];v!=null&&(e&&(e+=f),e+=$(g+":"+c+w(v)+";",a))}}else for(var m in o){var R=o[m];R!=null&&(e&&(e+=f),e+=$(m+":"+c+w(R)+";",a))}for(var q in t){var ye=t[q];ye!=null&&q!=="fallbacks"&&(e&&(e+=f),e+=$(q+":"+c+w(ye)+";",a))}return!e&&!r.allowEmpty||!i?e:(a--,e&&(e=""+f+e+f),$(""+i+c+"{"+e,a)+$("}",a))}var at=/([[\].#*$><+~=|^:(),"'`\s])/g,Se=typeof CSS<"u"&&CSS.escape,he=function(i){return Se?Se(i):i.replace(at,"\\$1")},Ge=function(){function i(r,e,n){this.type="style",this.isProcessed=!1;var s=n.sheet,a=n.Renderer;this.key=r,this.options=n,this.style=e,s?this.renderer=s.renderer:a&&(this.renderer=new a)}var t=i.prototype;return t.prop=function(e,n,s){if(n===void 0)return this.style[e];var a=s?s.force:!1;if(!a&&this.style[e]===n)return this;var o=n;(!s||s.process!==!1)&&(o=this.options.jss.plugins.onChangeValue(n,e,this));var l=o==null||o===!1,f=e in this.style;if(l&&!f&&!a)return this;var c=l&&f;if(c?delete this.style[e]:this.style[e]=o,this.renderable&&this.renderer)return c?this.renderer.removeProperty(this.renderable,e):this.renderer.setProperty(this.renderable,e,o),this;var h=this.options.sheet;return h&&h.attached,this},i}(),ne=function(i){Te(t,i);function t(e,n,s){var a;a=i.call(this,e,n,s)||this;var o=s.selector,l=s.scoped,f=s.sheet,c=s.generateId;return o?a.selectorText=o:l!==!1&&(a.id=c(me(me(a)),f),a.selectorText="."+he(a.id)),a}var r=t.prototype;return r.applyTo=function(n){var s=this.renderer;if(s){var a=this.toJSON();for(var o in a)s.setProperty(n,o,a[o])}return this},r.toJSON=function(){var n={};for(var s in this.style){var a=this.style[s];typeof a!="object"?n[s]=a:Array.isArray(a)&&(n[s]=w(a))}return n},r.toString=function(n){var s=this.options.sheet,a=s?s.options.link:!1,o=a?p({},n,{allowEmpty:!0}):n;return V(this.selectorText,this.style,o)},Ne(t,[{key:"selector",set:function(n){if(n!==this.selectorText){this.selectorText=n;var s=this.renderer,a=this.renderable;if(!(!a||!s)){var o=s.setSelector(a,n);o||s.replaceRule(a,this)}}},get:function(){return this.selectorText}}]),t}(Ge),ot={onCreateRule:function(t,r,e){return t[0]==="@"||e.parent&&e.parent.type==="keyframes"?null:new ne(t,r,e)}},L={indent:1,children:!0},ut=/@([\w-]+)/,lt=function(){function i(r,e,n){this.type="conditional",this.isProcessed=!1,this.key=r;var s=r.match(ut);this.at=s?s[1]:"unknown",this.query=n.name||"@"+this.at,this.options=n,this.rules=new K(p({},n,{parent:this}));for(var a in e)this.rules.add(a,e[a]);this.rules.process()}var t=i.prototype;return t.getRule=function(e){return this.rules.get(e)},t.indexOf=function(e){return this.rules.indexOf(e)},t.addRule=function(e,n,s){var a=this.rules.add(e,n,s);return a?(this.options.jss.plugins.onProcessRule(a),a):null},t.replaceRule=function(e,n,s){var a=this.rules.replace(e,n,s);return a&&this.options.jss.plugins.onProcessRule(a),a},t.toString=function(e){e===void 0&&(e=L);var n=I(e),s=n.linebreak;if(e.indent==null&&(e.indent=L.indent),e.children==null&&(e.children=L.children),e.children===!1)return this.query+" {}";var a=this.rules.toString(e);return a?this.query+" {"+s+a+s+"}":""},i}(),ft=/@container|@media|@supports\s+/,ct={onCreateRule:function(t,r,e){return ft.test(t)?new lt(t,r,e):null}},F={indent:1,children:!0},dt=/@keyframes\s+([\w-]+)/,ie=function(){function i(r,e,n){this.type="keyframes",this.at="@keyframes",this.isProcessed=!1;var s=r.match(dt);s&&s[1]?this.name=s[1]:this.name="noname",this.key=this.type+"-"+this.name,this.options=n;var a=n.scoped,o=n.sheet,l=n.generateId;this.id=a===!1?this.name:he(l(this,o)),this.rules=new K(p({},n,{parent:this}));for(var f in e)this.rules.add(f,e[f],p({},n,{parent:this}));this.rules.process()}var t=i.prototype;return t.toString=function(e){e===void 0&&(e=F);var n=I(e),s=n.linebreak;if(e.indent==null&&(e.indent=F.indent),e.children==null&&(e.children=F.children),e.children===!1)return this.at+" "+this.id+" {}";var a=this.rules.toString(e);return a&&(a=""+s+a+s),this.at+" "+this.id+" {"+a+"}"},i}(),ht=/@keyframes\s+/,pt=/\$([\w-]+)/g,se=function(t,r){return typeof t=="string"?t.replace(pt,function(e,n){return n in r?r[n]:e}):t},xe=function(t,r,e){var n=t[r],s=se(n,e);s!==n&&(t[r]=s)},vt={onCreateRule:function(t,r,e){return typeof t=="string"&&ht.test(t)?new ie(t,r,e):null},onProcessStyle:function(t,r,e){return r.type!=="style"||!e||("animation-name"in t&&xe(t,"animation-name",e.keyframes),"animation"in t&&xe(t,"animation",e.keyframes)),t},onChangeValue:function(t,r,e){var n=e.options.sheet;if(!n)return t;switch(r){case"animation":return se(t,n.keyframes);case"animation-name":return se(t,n.keyframes);default:return t}}},gt=function(i){Te(t,i);function t(){return i.apply(this,arguments)||this}var r=t.prototype;return r.toString=function(n){var s=this.options.sheet,a=s?s.options.link:!1,o=a?p({},n,{allowEmpty:!0}):n;return V(this.key,this.style,o)},t}(Ge),yt={onCreateRule:function(t,r,e){return e.parent&&e.parent.type==="keyframes"?new gt(t,r,e):null}},mt=function(){function i(r,e,n){this.type="font-face",this.at="@font-face",this.isProcessed=!1,this.key=r,this.style=e,this.options=n}var t=i.prototype;return t.toString=function(e){var n=I(e),s=n.linebreak;if(Array.isArray(this.style)){for(var a="",o=0;o<this.style.length;o++)a+=V(this.at,this.style[o]),this.style[o+1]&&(a+=s);return a}return V(this.at,this.style,e)},i}(),bt=/@font-face/,Rt={onCreateRule:function(t,r,e){return bt.test(t)?new mt(t,r,e):null}},St=function(){function i(r,e,n){this.type="viewport",this.at="@viewport",this.isProcessed=!1,this.key=r,this.style=e,this.options=n}var t=i.prototype;return t.toString=function(e){return V(this.key,this.style,e)},i}(),xt={onCreateRule:function(t,r,e){return t==="@viewport"||t==="@-ms-viewport"?new St(t,r,e):null}},Pt=function(){function i(r,e,n){this.type="simple",this.isProcessed=!1,this.key=r,this.value=e,this.options=n}var t=i.prototype;return t.toString=function(e){if(Array.isArray(this.value)){for(var n="",s=0;s<this.value.length;s++)n+=this.key+" "+this.value[s]+";",this.value[s+1]&&(n+=`
`);return n}return this.key+" "+this.value+";"},i}(),Ct={"@charset":!0,"@import":!0,"@namespace":!0},wt={onCreateRule:function(t,r,e){return t in Ct?new Pt(t,r,e):null}},Pe=[ot,ct,vt,yt,Rt,xt,wt],kt={process:!0},Ce={force:!0,process:!0},K=function(){function i(r){this.map={},this.raw={},this.index=[],this.counter=0,this.options=r,this.classes=r.classes,this.keyframes=r.keyframes}var t=i.prototype;return t.add=function(e,n,s){var a=this.options,o=a.parent,l=a.sheet,f=a.jss,c=a.Renderer,h=a.generateId,y=a.scoped,g=p({classes:this.classes,parent:o,sheet:l,jss:f,Renderer:c,generateId:h,scoped:y,name:e,keyframes:this.keyframes,selector:void 0},s),v=e;e in this.raw&&(v=e+"-d"+this.counter++),this.raw[v]=n,v in this.classes&&(g.selector="."+he(this.classes[v]));var m=de(v,n,g);if(!m)return null;this.register(m);var R=g.index===void 0?this.index.length:g.index;return this.index.splice(R,0,m),m},t.replace=function(e,n,s){var a=this.get(e),o=this.index.indexOf(a);a&&this.remove(a);var l=s;return o!==-1&&(l=p({},s,{index:o})),this.add(e,n,l)},t.get=function(e){return this.map[e]},t.remove=function(e){this.unregister(e),delete this.raw[e.key],this.index.splice(this.index.indexOf(e),1)},t.indexOf=function(e){return this.index.indexOf(e)},t.process=function(){var e=this.options.jss.plugins;this.index.slice(0).forEach(e.onProcessRule,e)},t.register=function(e){this.map[e.key]=e,e instanceof ne?(this.map[e.selector]=e,e.id&&(this.classes[e.key]=e.id)):e instanceof ie&&this.keyframes&&(this.keyframes[e.name]=e.id)},t.unregister=function(e){delete this.map[e.key],e instanceof ne?(delete this.map[e.selector],delete this.classes[e.key]):e instanceof ie&&delete this.keyframes[e.name]},t.update=function(){var e,n,s;if(typeof(arguments.length<=0?void 0:arguments[0])=="string"?(e=arguments.length<=0?void 0:arguments[0],n=arguments.length<=1?void 0:arguments[1],s=arguments.length<=2?void 0:arguments[2]):(n=arguments.length<=0?void 0:arguments[0],s=arguments.length<=1?void 0:arguments[1],e=null),e)this.updateOne(this.get(e),n,s);else for(var a=0;a<this.index.length;a++)this.updateOne(this.index[a],n,s)},t.updateOne=function(e,n,s){s===void 0&&(s=kt);var a=this.options,o=a.jss.plugins,l=a.sheet;if(e.rules instanceof i){e.rules.update(n,s);return}var f=e.style;if(o.onUpdate(n,e,l,s),s.process&&f&&f!==e.style){o.onProcessStyle(e.style,e,l);for(var c in e.style){var h=e.style[c],y=f[c];h!==y&&e.prop(c,h,Ce)}for(var g in f){var v=e.style[g],m=f[g];v==null&&v!==m&&e.prop(g,null,Ce)}}},t.toString=function(e){for(var n="",s=this.options.sheet,a=s?s.options.link:!1,o=I(e),l=o.linebreak,f=0;f<this.index.length;f++){var c=this.index[f],h=c.toString(e);!h&&!a||(n&&(n+=l),n+=h)}return n},i}(),_e=function(){function i(r,e){this.attached=!1,this.deployed=!1,this.classes={},this.keyframes={},this.options=p({},e,{sheet:this,parent:this,classes:this.classes,keyframes:this.keyframes}),e.Renderer&&(this.renderer=new e.Renderer(this)),this.rules=new K(this.options);for(var n in r)this.rules.add(n,r[n]);this.rules.process()}var t=i.prototype;return t.attach=function(){return this.attached?this:(this.renderer&&this.renderer.attach(),this.attached=!0,this.deployed||this.deploy(),this)},t.detach=function(){return this.attached?(this.renderer&&this.renderer.detach(),this.attached=!1,this):this},t.addRule=function(e,n,s){var a=this.queue;this.attached&&!a&&(this.queue=[]);var o=this.rules.add(e,n,s);return o?(this.options.jss.plugins.onProcessRule(o),this.attached?(this.deployed&&(a?a.push(o):(this.insertRule(o),this.queue&&(this.queue.forEach(this.insertRule,this),this.queue=void 0))),o):(this.deployed=!1,o)):null},t.replaceRule=function(e,n,s){var a=this.rules.get(e);if(!a)return this.addRule(e,n,s);var o=this.rules.replace(e,n,s);return o&&this.options.jss.plugins.onProcessRule(o),this.attached?(this.deployed&&this.renderer&&(o?a.renderable&&this.renderer.replaceRule(a.renderable,o):this.renderer.deleteRule(a)),o):(this.deployed=!1,o)},t.insertRule=function(e){this.renderer&&this.renderer.insertRule(e)},t.addRules=function(e,n){var s=[];for(var a in e){var o=this.addRule(a,e[a],n);o&&s.push(o)}return s},t.getRule=function(e){return this.rules.get(e)},t.deleteRule=function(e){var n=typeof e=="object"?e:this.rules.get(e);return!n||this.attached&&!n.renderable?!1:(this.rules.remove(n),this.attached&&n.renderable&&this.renderer?this.renderer.deleteRule(n.renderable):!0)},t.indexOf=function(e){return this.rules.indexOf(e)},t.deploy=function(){return this.renderer&&this.renderer.deploy(),this.deployed=!0,this},t.update=function(){var e;return(e=this.rules).update.apply(e,arguments),this},t.updateOne=function(e,n,s){return this.rules.updateOne(e,n,s),this},t.toString=function(e){return this.rules.toString(e)},i}(),jt=function(){function i(){this.plugins={internal:[],external:[]},this.registry={}}var t=i.prototype;return t.onCreateRule=function(e,n,s){for(var a=0;a<this.registry.onCreateRule.length;a++){var o=this.registry.onCreateRule[a](e,n,s);if(o)return o}return null},t.onProcessRule=function(e){if(!e.isProcessed){for(var n=e.options.sheet,s=0;s<this.registry.onProcessRule.length;s++)this.registry.onProcessRule[s](e,n);e.style&&this.onProcessStyle(e.style,e,n),e.isProcessed=!0}},t.onProcessStyle=function(e,n,s){for(var a=0;a<this.registry.onProcessStyle.length;a++)n.style=this.registry.onProcessStyle[a](n.style,n,s)},t.onProcessSheet=function(e){for(var n=0;n<this.registry.onProcessSheet.length;n++)this.registry.onProcessSheet[n](e)},t.onUpdate=function(e,n,s,a){for(var o=0;o<this.registry.onUpdate.length;o++)this.registry.onUpdate[o](e,n,s,a)},t.onChangeValue=function(e,n,s){for(var a=e,o=0;o<this.registry.onChangeValue.length;o++)a=this.registry.onChangeValue[o](a,n,s);return a},t.use=function(e,n){n===void 0&&(n={queue:"external"});var s=this.plugins[n.queue];s.indexOf(e)===-1&&(s.push(e),this.registry=[].concat(this.plugins.external,this.plugins.internal).reduce(function(a,o){for(var l in o)l in a&&a[l].push(o[l]);return a},{onCreateRule:[],onProcessRule:[],onProcessStyle:[],onProcessSheet:[],onChangeValue:[],onUpdate:[]}))},i}(),Ot=function(){function i(){this.registry=[]}var t=i.prototype;return t.add=function(e){var n=this.registry,s=e.options.index;if(n.indexOf(e)===-1){if(n.length===0||s>=this.index){n.push(e);return}for(var a=0;a<n.length;a++)if(n[a].options.index>s){n.splice(a,0,e);return}}},t.reset=function(){this.registry=[]},t.remove=function(e){var n=this.registry.indexOf(e);this.registry.splice(n,1)},t.toString=function(e){for(var n=e===void 0?{}:e,s=n.attached,a=W(n,["attached"]),o=I(a),l=o.linebreak,f="",c=0;c<this.registry.length;c++){var h=this.registry[c];s!=null&&h.attached!==s||(f&&(f+=l),f+=h.toString(a))}return f},Ne(i,[{key:"index",get:function(){return this.registry.length===0?0:this.registry[this.registry.length-1].options.index}}]),i}(),N=new Ot,ae=typeof globalThis<"u"?globalThis:typeof window<"u"&&window.Math===Math?window:typeof self<"u"&&self.Math===Math?self:Function("return this")(),oe="2f1acc6c3a606b082e5eef5e54414ffb";ae[oe]==null&&(ae[oe]=0);var we=ae[oe]++,ke=function(t){t===void 0&&(t={});var r=0,e=function(s,a){r+=1;var o="",l="";return a&&(a.options.classNamePrefix&&(l=a.options.classNamePrefix),a.options.jss.id!=null&&(o=String(a.options.jss.id))),t.minify?""+(l||"c")+we+o+r:l+s.key+"-"+we+(o?"-"+o:"")+"-"+r};return e},ze=function(t){var r;return function(){return r||(r=t()),r}},Et=function(t,r){try{return t.attributeStyleMap?t.attributeStyleMap.get(r):t.style.getPropertyValue(r)}catch{return""}},It=function(t,r,e){try{var n=e;if(Array.isArray(e)&&(n=w(e)),t.attributeStyleMap)t.attributeStyleMap.set(r,n);else{var s=n?n.indexOf("!important"):-1,a=s>-1?n.substr(0,s-1):n;t.style.setProperty(r,a,s>-1?"important":"")}}catch{return!1}return!0},$t=function(t,r){try{t.attributeStyleMap?t.attributeStyleMap.delete(r):t.style.removeProperty(r)}catch{}},At=function(t,r){return t.selectorText=r,t.selectorText===r},Ue=ze(function(){return document.querySelector("head")});function Mt(i,t){for(var r=0;r<i.length;r++){var e=i[r];if(e.attached&&e.options.index>t.index&&e.options.insertionPoint===t.insertionPoint)return e}return null}function Nt(i,t){for(var r=i.length-1;r>=0;r--){var e=i[r];if(e.attached&&e.options.insertionPoint===t.insertionPoint)return e}return null}function Tt(i){for(var t=Ue(),r=0;r<t.childNodes.length;r++){var e=t.childNodes[r];if(e.nodeType===8&&e.nodeValue.trim()===i)return e}return null}function Vt(i){var t=N.registry;if(t.length>0){var r=Mt(t,i);if(r&&r.renderer)return{parent:r.renderer.element.parentNode,node:r.renderer.element};if(r=Nt(t,i),r&&r.renderer)return{parent:r.renderer.element.parentNode,node:r.renderer.element.nextSibling}}var e=i.insertionPoint;if(e&&typeof e=="string"){var n=Tt(e);if(n)return{parent:n.parentNode,node:n.nextSibling}}return!1}function Gt(i,t){var r=t.insertionPoint,e=Vt(t);if(e!==!1&&e.parent){e.parent.insertBefore(i,e.node);return}if(r&&typeof r.nodeType=="number"){var n=r,s=n.parentNode;s&&s.insertBefore(i,n.nextSibling);return}Ue().appendChild(i)}var _t=ze(function(){var i=document.querySelector('meta[property="csp-nonce"]');return i?i.getAttribute("content"):null}),je=function(t,r,e){try{"insertRule"in t?t.insertRule(r,e):"appendRule"in t&&t.appendRule(r)}catch{return!1}return t.cssRules[e]},Oe=function(t,r){var e=t.cssRules.length;return r===void 0||r>e?e:r},zt=function(){var t=document.createElement("style");return t.textContent=`
`,t},Ut=function(){function i(r){this.getPropertyValue=Et,this.setProperty=It,this.removeProperty=$t,this.setSelector=At,this.hasInsertedRules=!1,this.cssRules=[],r&&N.add(r),this.sheet=r;var e=this.sheet?this.sheet.options:{},n=e.media,s=e.meta,a=e.element;this.element=a||zt(),this.element.setAttribute("data-jss",""),n&&this.element.setAttribute("media",n),s&&this.element.setAttribute("data-meta",s);var o=_t();o&&this.element.setAttribute("nonce",o)}var t=i.prototype;return t.attach=function(){if(!(this.element.parentNode||!this.sheet)){Gt(this.element,this.sheet.options);var e=!!(this.sheet&&this.sheet.deployed);this.hasInsertedRules&&e&&(this.hasInsertedRules=!1,this.deploy())}},t.detach=function(){if(this.sheet){var e=this.element.parentNode;e&&e.removeChild(this.element),this.sheet.options.link&&(this.cssRules=[],this.element.textContent=`
`)}},t.deploy=function(){var e=this.sheet;if(e){if(e.options.link){this.insertRules(e.rules);return}this.element.textContent=`
`+e.toString()+`
`}},t.insertRules=function(e,n){for(var s=0;s<e.index.length;s++)this.insertRule(e.index[s],s,n)},t.insertRule=function(e,n,s){if(s===void 0&&(s=this.element.sheet),e.rules){var a=e,o=s;if(e.type==="conditional"||e.type==="keyframes"){var l=Oe(s,n);if(o=je(s,a.toString({children:!1}),l),o===!1)return!1;this.refCssRule(e,l,o)}return this.insertRules(a.rules,o),o}var f=e.toString();if(!f)return!1;var c=Oe(s,n),h=je(s,f,c);return h===!1?!1:(this.hasInsertedRules=!0,this.refCssRule(e,c,h),h)},t.refCssRule=function(e,n,s){e.renderable=s,e.options.parent instanceof _e&&this.cssRules.splice(n,0,s)},t.deleteRule=function(e){var n=this.element.sheet,s=this.indexOf(e);return s===-1?!1:(n.deleteRule(s),this.cssRules.splice(s,1),!0)},t.indexOf=function(e){return this.cssRules.indexOf(e)},t.replaceRule=function(e,n){var s=this.indexOf(e);return s===-1?!1:(this.element.sheet.deleteRule(s),this.cssRules.splice(s,1),this.insertRule(n,s))},t.getRules=function(){return this.element.sheet.cssRules},i}(),Wt=0,Kt=function(){function i(r){this.id=Wt++,this.version="10.10.0",this.plugins=new jt,this.options={id:{minify:!1},createGenerateId:ke,Renderer:G?Ut:null,plugins:[]},this.generateId=ke({minify:!1});for(var e=0;e<Pe.length;e++)this.plugins.use(Pe[e],{queue:"internal"});this.setup(r)}var t=i.prototype;return t.setup=function(e){return e===void 0&&(e={}),e.createGenerateId&&(this.options.createGenerateId=e.createGenerateId),e.id&&(this.options.id=p({},this.options.id,e.id)),(e.createGenerateId||e.id)&&(this.generateId=this.options.createGenerateId(this.options.id)),e.insertionPoint!=null&&(this.options.insertionPoint=e.insertionPoint),"Renderer"in e&&(this.options.Renderer=e.Renderer),e.plugins&&this.use.apply(this,e.plugins),this},t.createStyleSheet=function(e,n){n===void 0&&(n={});var s=n,a=s.index;typeof a!="number"&&(a=N.index===0?0:N.index+1);var o=new _e(e,p({},n,{jss:this,generateId:n.generateId||this.generateId,insertionPoint:this.options.insertionPoint,Renderer:this.options.Renderer,index:a}));return this.plugins.onProcessSheet(o),o},t.removeStyleSheet=function(e){return e.detach(),N.remove(e),this},t.createRule=function(e,n,s){if(n===void 0&&(n={}),s===void 0&&(s={}),typeof e=="object")return this.createRule(void 0,e,n);var a=p({},s,{name:e,jss:this,Renderer:this.options.Renderer});a.generateId||(a.generateId=this.generateId),a.classes||(a.classes={}),a.keyframes||(a.keyframes={});var o=de(e,n,a);return o&&this.plugins.onProcessRule(o),o},t.use=function(){for(var e=this,n=arguments.length,s=new Array(n),a=0;a<n;a++)s[a]=arguments[a];return s.forEach(function(o){e.plugins.use(o)}),this},i}(),pe=function(t){return new Kt(t)},ve=typeof CSS=="object"&&CSS!=null&&"number"in CSS;function We(i){var t=null;for(var r in i){var e=i[r],n=typeof e;if(n==="function")t||(t={}),t[r]=e;else if(n==="object"&&e!==null&&!Array.isArray(e)){var s=We(e);s&&(t||(t={}),t[r]=s)}}return t}/**
 * A better abstraction over CSS.
 *
 * @copyright Oleg Isonen (Slobodskoi) / Isonen 2014-present
 * @website https://github.com/cssinjs/jss
 * @license MIT
 */pe();var Ke=Date.now(),B="fnValues"+Ke,D="fnStyle"+ ++Ke,qt=function(){return{onCreateRule:function(r,e,n){if(typeof e!="function")return null;var s=de(r,{},n);return s[D]=e,s},onProcessStyle:function(r,e){if(B in e||D in e)return r;var n={};for(var s in r){var a=r[s];typeof a=="function"&&(delete r[s],n[s]=a)}return e[B]=n,r},onUpdate:function(r,e,n,s){var a=e,o=a[D];o&&(a.style=o(r)||{});var l=a[B];if(l)for(var f in l)a.prop(f,l[f](r),s)}}};const Jt=qt;var x="@global",ue="@global ",Lt=function(){function i(r,e,n){this.type="global",this.at=x,this.isProcessed=!1,this.key=r,this.options=n,this.rules=new K(p({},n,{parent:this}));for(var s in e)this.rules.add(s,e[s]);this.rules.process()}var t=i.prototype;return t.getRule=function(e){return this.rules.get(e)},t.addRule=function(e,n,s){var a=this.rules.add(e,n,s);return a&&this.options.jss.plugins.onProcessRule(a),a},t.replaceRule=function(e,n,s){var a=this.rules.replace(e,n,s);return a&&this.options.jss.plugins.onProcessRule(a),a},t.indexOf=function(e){return this.rules.indexOf(e)},t.toString=function(e){return this.rules.toString(e)},i}(),Ft=function(){function i(r,e,n){this.type="global",this.at=x,this.isProcessed=!1,this.key=r,this.options=n;var s=r.substr(ue.length);this.rule=n.jss.createRule(s,e,p({},n,{parent:this}))}var t=i.prototype;return t.toString=function(e){return this.rule?this.rule.toString(e):""},i}(),Bt=/\s*,\s*/g;function qe(i,t){for(var r=i.split(Bt),e="",n=0;n<r.length;n++)e+=t+" "+r[n].trim(),r[n+1]&&(e+=", ");return e}function Dt(i,t){var r=i.options,e=i.style,n=e?e[x]:null;if(n){for(var s in n)t.addRule(s,n[s],p({},r,{selector:qe(s,i.selector)}));delete e[x]}}function Ht(i,t){var r=i.options,e=i.style;for(var n in e)if(!(n[0]!=="@"||n.substr(0,x.length)!==x)){var s=qe(n.substr(x.length),i.selector);t.addRule(s,e[n],p({},r,{selector:s})),delete e[n]}}function Zt(){function i(r,e,n){if(!r)return null;if(r===x)return new Lt(r,e,n);if(r[0]==="@"&&r.substr(0,ue.length)===ue)return new Ft(r,e,n);var s=n.parent;return s&&(s.type==="global"||s.options.parent&&s.options.parent.type==="global")&&(n.scoped=!1),!n.selector&&n.scoped===!1&&(n.selector=r),null}function t(r,e){r.type!=="style"||!e||(Dt(r,e),Ht(r,e))}return{onCreateRule:i,onProcessRule:t}}var Ee=/\s*,\s*/g,Qt=/&/g,Xt=/\$([\w-]+)/g;function Yt(){function i(n,s){return function(a,o){var l=n.getRule(o)||s&&s.getRule(o);return l?l.selector:o}}function t(n,s){for(var a=s.split(Ee),o=n.split(Ee),l="",f=0;f<a.length;f++)for(var c=a[f],h=0;h<o.length;h++){var y=o[h];l&&(l+=", "),l+=y.indexOf("&")!==-1?y.replace(Qt,c):c+" "+y}return l}function r(n,s,a){if(a)return p({},a,{index:a.index+1});var o=n.options.nestingLevel;o=o===void 0?1:o+1;var l=p({},n.options,{nestingLevel:o,index:s.indexOf(n)+1});return delete l.name,l}function e(n,s,a){if(s.type!=="style")return n;var o=s,l=o.options.parent,f,c;for(var h in n){var y=h.indexOf("&")!==-1,g=h[0]==="@";if(!(!y&&!g)){if(f=r(o,l,f),y){var v=t(h,o.selector);c||(c=i(l,a)),v=v.replace(Xt,c);var m=o.key+"-"+h;"replaceRule"in l?l.replaceRule(m,n[h],p({},f,{selector:v})):l.addRule(m,n[h],p({},f,{selector:v}))}else g&&l.addRule(h,{},f).addRule(o.key,n[h],{selector:o.selector});delete n[h]}}return n}return{onProcessStyle:e}}var er=/[A-Z]/g,tr=/^ms-/,H={};function rr(i){return"-"+i.toLowerCase()}function Je(i){if(H.hasOwnProperty(i))return H[i];var t=i.replace(er,rr);return H[i]=tr.test(t)?"-"+t:t}function U(i){var t={};for(var r in i){var e=r.indexOf("--")===0?r:Je(r);t[e]=i[r]}return i.fallbacks&&(Array.isArray(i.fallbacks)?t.fallbacks=i.fallbacks.map(U):t.fallbacks=U(i.fallbacks)),t}function nr(){function i(r){if(Array.isArray(r)){for(var e=0;e<r.length;e++)r[e]=U(r[e]);return r}return U(r)}function t(r,e,n){if(e.indexOf("--")===0)return r;var s=Je(e);return e===s?r:(n.prop(s,r),null)}return{onProcessStyle:i,onChangeValue:t}}var u=ve&&CSS?CSS.px:"px",_=ve&&CSS?CSS.ms:"ms",k=ve&&CSS?CSS.percent:"%",ir={"animation-delay":_,"animation-duration":_,"background-position":u,"background-position-x":u,"background-position-y":u,"background-size":u,border:u,"border-bottom":u,"border-bottom-left-radius":u,"border-bottom-right-radius":u,"border-bottom-width":u,"border-left":u,"border-left-width":u,"border-radius":u,"border-right":u,"border-right-width":u,"border-top":u,"border-top-left-radius":u,"border-top-right-radius":u,"border-top-width":u,"border-width":u,"border-block":u,"border-block-end":u,"border-block-end-width":u,"border-block-start":u,"border-block-start-width":u,"border-block-width":u,"border-inline":u,"border-inline-end":u,"border-inline-end-width":u,"border-inline-start":u,"border-inline-start-width":u,"border-inline-width":u,"border-start-start-radius":u,"border-start-end-radius":u,"border-end-start-radius":u,"border-end-end-radius":u,margin:u,"margin-bottom":u,"margin-left":u,"margin-right":u,"margin-top":u,"margin-block":u,"margin-block-end":u,"margin-block-start":u,"margin-inline":u,"margin-inline-end":u,"margin-inline-start":u,padding:u,"padding-bottom":u,"padding-left":u,"padding-right":u,"padding-top":u,"padding-block":u,"padding-block-end":u,"padding-block-start":u,"padding-inline":u,"padding-inline-end":u,"padding-inline-start":u,"mask-position-x":u,"mask-position-y":u,"mask-size":u,height:u,width:u,"min-height":u,"max-height":u,"min-width":u,"max-width":u,bottom:u,left:u,top:u,right:u,inset:u,"inset-block":u,"inset-block-end":u,"inset-block-start":u,"inset-inline":u,"inset-inline-end":u,"inset-inline-start":u,"box-shadow":u,"text-shadow":u,"column-gap":u,"column-rule":u,"column-rule-width":u,"column-width":u,"font-size":u,"font-size-delta":u,"letter-spacing":u,"text-decoration-thickness":u,"text-indent":u,"text-stroke":u,"text-stroke-width":u,"word-spacing":u,motion:u,"motion-offset":u,outline:u,"outline-offset":u,"outline-width":u,perspective:u,"perspective-origin-x":k,"perspective-origin-y":k,"transform-origin":k,"transform-origin-x":k,"transform-origin-y":k,"transform-origin-z":k,"transition-delay":_,"transition-duration":_,"vertical-align":u,"flex-basis":u,"shape-margin":u,size:u,gap:u,grid:u,"grid-gap":u,"row-gap":u,"grid-row-gap":u,"grid-column-gap":u,"grid-template-rows":u,"grid-template-columns":u,"grid-auto-rows":u,"grid-auto-columns":u,"box-shadow-x":u,"box-shadow-y":u,"box-shadow-blur":u,"box-shadow-spread":u,"font-line-height":u,"text-shadow-x":u,"text-shadow-y":u,"text-shadow-blur":u};function Le(i){var t=/(-[a-z])/g,r=function(a){return a[1].toUpperCase()},e={};for(var n in i)e[n]=i[n],e[n.replace(t,r)]=i[n];return e}var sr=Le(ir);function T(i,t,r){if(t==null)return t;if(Array.isArray(t))for(var e=0;e<t.length;e++)t[e]=T(i,t[e],r);else if(typeof t=="object")if(i==="fallbacks")for(var n in t)t[n]=T(n,t[n],r);else for(var s in t)t[s]=T(i+"-"+s,t[s],r);else if(typeof t=="number"&&isNaN(t)===!1){var a=r[i]||sr[i];return a&&!(t===0&&a===u)?typeof a=="function"?a(t).toString():""+t+a:t.toString()}return t}function ar(i){i===void 0&&(i={});var t=Le(i);function r(n,s){if(s.type!=="style")return n;for(var a in n)n[a]=T(a,n[a],t);return n}function e(n,s){return T(s,n,t)}return{onProcessStyle:r,onChangeValue:e}}var A="",le="",Fe="",Be="",or=G&&"ontouchstart"in document.documentElement;if(G){var Z={Moz:"-moz-",ms:"-ms-",O:"-o-",Webkit:"-webkit-"},ur=document.createElement("p"),Q=ur.style,lr="Transform";for(var X in Z)if(X+lr in Q){A=X,le=Z[X];break}A==="Webkit"&&"msHyphens"in Q&&(A="ms",le=Z.ms,Be="edge"),A==="Webkit"&&"-apple-trailing-word"in Q&&(Fe="apple")}var d={js:A,css:le,vendor:Fe,browser:Be,isTouch:or};function fr(i){return i[1]==="-"||d.js==="ms"?i:"@"+d.css+"keyframes"+i.substr(10)}var cr={noPrefill:["appearance"],supportedProperty:function(t){return t!=="appearance"?!1:d.js==="ms"?"-webkit-"+t:d.css+t}},dr={noPrefill:["color-adjust"],supportedProperty:function(t){return t!=="color-adjust"?!1:d.js==="Webkit"?d.css+"print-"+t:t}},hr=/[-\s]+(.)?/g;function pr(i,t){return t?t.toUpperCase():""}function ge(i){return i.replace(hr,pr)}function P(i){return ge("-"+i)}var vr={noPrefill:["mask"],supportedProperty:function(t,r){if(!/^mask/.test(t))return!1;if(d.js==="Webkit"){var e="mask-image";if(ge(e)in r)return t;if(d.js+P(e)in r)return d.css+t}return t}},gr={noPrefill:["text-orientation"],supportedProperty:function(t){return t!=="text-orientation"?!1:d.vendor==="apple"&&!d.isTouch?d.css+t:t}},yr={noPrefill:["transform"],supportedProperty:function(t,r,e){return t!=="transform"?!1:e.transform?t:d.css+t}},mr={noPrefill:["transition"],supportedProperty:function(t,r,e){return t!=="transition"?!1:e.transition?t:d.css+t}},br={noPrefill:["writing-mode"],supportedProperty:function(t){return t!=="writing-mode"?!1:d.js==="Webkit"||d.js==="ms"&&d.browser!=="edge"?d.css+t:t}},Rr={noPrefill:["user-select"],supportedProperty:function(t){return t!=="user-select"?!1:d.js==="Moz"||d.js==="ms"||d.vendor==="apple"?d.css+t:t}},Sr={supportedProperty:function(t,r){if(!/^break-/.test(t))return!1;if(d.js==="Webkit"){var e="WebkitColumn"+P(t);return e in r?d.css+"column-"+t:!1}if(d.js==="Moz"){var n="page"+P(t);return n in r?"page-"+t:!1}return!1}},xr={supportedProperty:function(t,r){if(!/^(border|margin|padding)-inline/.test(t))return!1;if(d.js==="Moz")return t;var e=t.replace("-inline","");return d.js+P(e)in r?d.css+e:!1}},Pr={supportedProperty:function(t,r){return ge(t)in r?t:!1}},Cr={supportedProperty:function(t,r){var e=P(t);return t[0]==="-"||t[0]==="-"&&t[1]==="-"?t:d.js+e in r?d.css+t:d.js!=="Webkit"&&"Webkit"+e in r?"-webkit-"+t:!1}},wr={supportedProperty:function(t){return t.substring(0,11)!=="scroll-snap"?!1:d.js==="ms"?""+d.css+t:t}},kr={supportedProperty:function(t){return t!=="overscroll-behavior"?!1:d.js==="ms"?d.css+"scroll-chaining":t}},jr={"flex-grow":"flex-positive","flex-shrink":"flex-negative","flex-basis":"flex-preferred-size","justify-content":"flex-pack",order:"flex-order","align-items":"flex-align","align-content":"flex-line-pack"},Or={supportedProperty:function(t,r){var e=jr[t];return e&&d.js+P(e)in r?d.css+e:!1}},De={flex:"box-flex","flex-grow":"box-flex","flex-direction":["box-orient","box-direction"],order:"box-ordinal-group","align-items":"box-align","flex-flow":["box-orient","box-direction"],"justify-content":"box-pack"},Er=Object.keys(De),Ir=function(t){return d.css+t},$r={supportedProperty:function(t,r,e){var n=e.multiple;if(Er.indexOf(t)>-1){var s=De[t];if(!Array.isArray(s))return d.js+P(s)in r?d.css+s:!1;if(!n)return!1;for(var a=0;a<s.length;a++)if(!(d.js+P(s[0])in r))return!1;return s.map(Ir)}return!1}},He=[cr,dr,vr,gr,yr,mr,br,Rr,Sr,xr,Pr,Cr,wr,kr,Or,$r],Ie=He.filter(function(i){return i.supportedProperty}).map(function(i){return i.supportedProperty}),Ar=He.filter(function(i){return i.noPrefill}).reduce(function(i,t){return i.push.apply(i,tt(t.noPrefill)),i},[]),M,C={};if(G){M=document.createElement("p");var Y=window.getComputedStyle(document.documentElement,"");for(var ee in Y)isNaN(ee)||(C[Y[ee]]=Y[ee]);Ar.forEach(function(i){return delete C[i]})}function fe(i,t){if(t===void 0&&(t={}),!M)return i;if(C[i]!=null)return C[i];(i==="transition"||i==="transform")&&(t[i]=i in M.style);for(var r=0;r<Ie.length&&(C[i]=Ie[r](i,M.style,t),!C[i]);r++);try{M.style[i]=""}catch{return!1}return C[i]}var j={},Mr={transition:1,"transition-property":1,"-webkit-transition":1,"-webkit-transition-property":1},Nr=/(^\s*[\w-]+)|, (\s*[\w-]+)(?![^()]*\))/g,S;function Tr(i,t,r){if(t==="var")return"var";if(t==="all")return"all";if(r==="all")return", all";var e=t?fe(t):", "+fe(r);return e||t||r}G&&(S=document.createElement("p"));function $e(i,t){var r=t;if(!S||i==="content")return t;if(typeof r!="string"||!isNaN(parseInt(r,10)))return r;var e=i+r;if(j[e]!=null)return j[e];try{S.style[i]=r}catch{return j[e]=!1,!1}if(Mr[i])r=r.replace(Nr,Tr);else if(S.style[i]===""&&(r=d.css+r,r==="-ms-flex"&&(S.style[i]="-ms-flexbox"),S.style[i]=r,S.style[i]===""))return j[e]=!1,!1;return S.style[i]="",j[e]=r,j[e]}function Vr(){function i(n){if(n.type==="keyframes"){var s=n;s.at=fr(s.at)}}function t(n){for(var s in n){var a=n[s];if(s==="fallbacks"&&Array.isArray(a)){n[s]=a.map(t);continue}var o=!1,l=fe(s);l&&l!==s&&(o=!0);var f=!1,c=$e(l,w(a));c&&c!==a&&(f=!0),(o||f)&&(o&&delete n[s],n[l||s]=c||a)}return n}function r(n,s){return s.type!=="style"?n:t(n)}function e(n,s){return $e(s,w(n))||n}return{onProcessRule:i,onProcessStyle:r,onChangeValue:e}}function Gr(){var i=function(r,e){return r.length===e.length?r>e?1:-1:r.length-e.length};return{onProcessStyle:function(r,e){if(e.type!=="style")return r;for(var n={},s=Object.keys(r).sort(i),a=0;a<s.length;a++)n[s[a]]=r[s[a]];return n}}}function Ze(){return{plugins:[Jt(),Zt(),Yt(),nr(),ar(),typeof window>"u"?null:Vr(),Gr()]}}function Qe(i={}){const{baseClasses:t,newClasses:r,Component:e}=i;if(!r)return t;const n=p({},t);return Object.keys(r).forEach(s=>{r[s]&&(n[s]=`${t[s]} ${r[s]}`)}),n}const _r={set:(i,t,r,e)=>{let n=i.get(t);n||(n=new Map,i.set(t,n)),n.set(r,e)},get:(i,t,r)=>{const e=i.get(t);return e?e.get(r):void 0},delete:(i,t,r)=>{i.get(t).delete(r)}},E=_r;function zr(){var i;const t=Ye();return(i=t==null?void 0:t.$$material)!=null?i:t}const Ur=["children","injectFirst","disableGeneration"],Wr=pe(Ze()),Kr=it(),qr=new Map,Jr={disableGeneration:!1,generateClassName:Kr,jss:Wr,sheetsCache:null,sheetsManager:qr,sheetsRegistry:null},ce=b.createContext(Jr);let z;function an(i){const{children:t,injectFirst:r=!1,disableGeneration:e=!1}=i,n=W(i,Ur),s=b.useContext(ce),{generateClassName:a,jss:o,serverGenerateClassName:l,sheetsCache:f,sheetsManager:c,sheetsRegistry:h}=p({},s,n),y=b.useMemo(()=>{const g={disableGeneration:e,generateClassName:a,jss:o,serverGenerateClassName:l,sheetsCache:f,sheetsManager:c,sheetsRegistry:h};if(!g.jss.options.insertionPoint&&r&&typeof window<"u"){if(!z){const v=document.head;z=document.createComment("mui-inject-first"),v.insertBefore(z,v.firstChild)}g.jss=pe({plugins:Ze().plugins,insertionPoint:z})}return g},[r,e,a,o,l,f,c,h]);return et.jsx(ce.Provider,{value:y,children:t})}let Ae=-1e9;function Lr(){return Ae+=1,Ae}const Fr=["variant"];function Me(i){return i.length===0}function Br(i){const{variant:t}=i,r=W(i,Fr);let e=t||"";return Object.keys(r).sort().forEach(n=>{n==="color"?e+=Me(e)?i[n]:J(i[n]):e+=`${Me(e)?n:J(n)}${J(i[n].toString())}`}),e}const Dr={},Hr=Dr;function Zr(i){const t=typeof i=="function";return{create:(r,e)=>{let n;try{n=t?i(r):i}catch(l){throw l}if(!e||!r.components||!r.components[e]||!r.components[e].styleOverrides&&!r.components[e].variants)return n;const s=r.components[e].styleOverrides||{},a=r.components[e].variants||[],o=p({},n);return Object.keys(s).forEach(l=>{o[l]=te(o[l]||{},s[l])}),a.forEach(l=>{const f=Br(l.props);o[f]=te(o[f]||{},l.style)}),o},options:{}}}const Qr=["name","classNamePrefix","Component","defaultTheme"];function Xr({state:i,stylesOptions:t},r,e){if(t.disableGeneration)return r||{};i.cacheClasses||(i.cacheClasses={value:null,lastProp:null,lastJSS:{}});let n=!1;return i.classes!==i.cacheClasses.lastJSS&&(i.cacheClasses.lastJSS=i.classes,n=!0),r!==i.cacheClasses.lastProp&&(i.cacheClasses.lastProp=r,n=!0),n&&(i.cacheClasses.value=Qe({baseClasses:i.cacheClasses.lastJSS,newClasses:r,Component:e})),i.cacheClasses.value}function Yr({state:i,theme:t,stylesOptions:r,stylesCreator:e,name:n},s){if(r.disableGeneration)return;let a=E.get(r.sheetsManager,e,t);a||(a={refs:0,staticSheet:null,dynamicStyles:null},E.set(r.sheetsManager,e,t,a));const o=p({},e.options,r,{theme:t,flip:typeof r.flip=="boolean"?r.flip:t.direction==="rtl"});o.generateId=o.serverGenerateClassName||o.generateClassName;const l=r.sheetsRegistry;if(a.refs===0){let f;r.sheetsCache&&(f=E.get(r.sheetsCache,e,t));const c=e.create(t,n);f||(f=r.jss.createStyleSheet(c,p({link:!1},o)),f.attach(),r.sheetsCache&&E.set(r.sheetsCache,e,t,f)),l&&l.add(f),a.staticSheet=f,a.dynamicStyles=We(c)}if(a.dynamicStyles){const f=r.jss.createStyleSheet(a.dynamicStyles,p({link:!0},o));f.update(s),f.attach(),i.dynamicSheet=f,i.classes=Qe({baseClasses:a.staticSheet.classes,newClasses:f.classes}),l&&l.add(f)}else i.classes=a.staticSheet.classes;a.refs+=1}function en({state:i},t){i.dynamicSheet&&i.dynamicSheet.update(t)}function tn({state:i,theme:t,stylesOptions:r,stylesCreator:e}){if(r.disableGeneration)return;const n=E.get(r.sheetsManager,e,t);n.refs-=1;const s=r.sheetsRegistry;n.refs===0&&(E.delete(r.sheetsManager,e,t),r.jss.removeStyleSheet(n.staticSheet),s&&s.remove(n.staticSheet)),i.dynamicSheet&&(r.jss.removeStyleSheet(i.dynamicSheet),s&&s.remove(i.dynamicSheet))}function rn(i,t){const r=b.useRef([]);let e;const n=b.useMemo(()=>({}),t);r.current!==n&&(r.current=n,e=i()),b.useEffect(()=>()=>{e&&e()},[n])}function on(i,t={}){const{name:r,classNamePrefix:e,Component:n,defaultTheme:s=Hr}=t,a=W(t,Qr),o=Zr(i),l=r||e||"makeStyles";return o.options={index:Lr(),name:r,meta:l,classNamePrefix:l},(c={})=>{const h=zr()||s,y=p({},b.useContext(ce),a),g=b.useRef(),v=b.useRef();return rn(()=>{const R={name:r,state:{},stylesCreator:o,stylesOptions:y,theme:h};return Yr(R,c),v.current=!1,g.current=R,()=>{tn(R)}},[h,o]),b.useEffect(()=>{v.current&&en(g.current,c),v.current=!0}),Xr(g.current,c.classes,n)}}export{Ot as S,an as a,ce as b,it as c,Qe as d,qr as e,Ze as j,on as m,Br as p,zr as u};
