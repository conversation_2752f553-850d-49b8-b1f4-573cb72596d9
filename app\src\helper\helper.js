import { TEMPLATE_KEYS, TEMPLATE_NAME_MANIPULATION } from "@constant/changeTemplates";
import { DT_KEY_NAMES, MODULE_MAP, prefixMap, REQUEST_TYPE } from "@constant/enum";
import moment from "moment";
import { removeAccordionCombination } from "@app/payloadSlice";
import { colors } from "@constant/colors";
import {MATERIAL_VIEWS} from "@constant/enum";
import * as XLSX from "xlsx";
import { END_POINTS } from "@constant/apiEndPoints";
const {VITE_DESTINATION_MATERIAL_MGMT,VITE_CW_MDG_PROFITCENTER_MASS_DEST,VITE_CW_MDG_GENERALLEDGER_MASS_DEST,VITE_CW_MDG_COSTCENTER_MASS_DEST}  = import.meta.env

/**
 * Checks if the given value is within the specified range (inclusive).
 *
 * @param {number} fromNumber - The lower bound of the range.
 * @param {number} toNumber - The upper bound of the range.
 * @param {number} value - The value to check if it lies within the range.
 * @returns {boolean} - Returns true if value is between fromNumber and toNumber (inclusive), otherwise false.
 */
export function isValueInRange(fromNumber, toNumber, value) {
  // Declare the variables with let instead of const
  let fromNum = BigInt(fromNumber);
  let toNum = BigInt(toNumber);
  const val = BigInt(value);

  // Swap the values if fromNum is greater than toNum
  if (fromNum > toNum) {
    [fromNum, toNum] = [toNum, fromNum];
  }

  return val >= fromNum && val <= toNum;
}

export function isNumericString(value) {
  return /^\d+$/.test(value.trim());
}

export function convertToDateFormat(isoDate) {
  const date = new Date(isoDate);
  const timestamp = date.getTime();
  const timezoneOffset = -date.getTimezoneOffset();
  const offsetHours = String(
    Math.floor(Math.abs(timezoneOffset) / 60)
  ).padStart(2, "0");
  const offsetMinutes = String(Math.abs(timezoneOffset) % 60).padStart(2, "0");
  const offsetSign = timezoneOffset > 0 ? "-" : "+";
  return `/Date(${timestamp}${offsetSign}${offsetHours}${offsetMinutes})/`;
}

export function groupBy(array, key) {
  return array.reduce((result, currentValue) => {
    (result[currentValue[key]] = result[currentValue[key]] || []).push(
      currentValue
    );
    return result;
  }, {});
}

export const filterConfigData = (configData, fieldNames, additionalFields = []) => {
  const fieldsToKeep = new Set([...fieldNames, ...additionalFields]);

  const filteredData = {};
  Object.keys(configData).forEach((key) => {
    filteredData[key] = configData[key].filter((item) =>
      fieldsToKeep.has(item.fieldName)
    );
  });

  return filteredData;
};

export const filterFieldNameData = (configData, fieldNames) => {
  const viewNamesSet = new Set();
  fieldNames.forEach(field => {
    const match = configData?.find(item => item.fieldName === field);
    if (match) {
      viewNamesSet.add(match.viewName);
    }
  });

  return viewNamesSet;
};

export const filterAndMapPlantData = (selectedSalesOrg, regionBasedSalesOrgData) => {
  const salesOrgCodes = selectedSalesOrg?.map(item => item.code);
  const salesOrgBasedPlantData = regionBasedSalesOrgData?.salesOrgData
    .filter(obj => salesOrgCodes?.includes(obj.MDG_MAT_SALES_ORG))
    .map(obj => ({
        code: obj.MDG_MAT_PLANT,
        desc: obj.MDG_MAT_PLANT_DESC
    }));
  const uniquePlantData = salesOrgBasedPlantData && Array.from(new Map(salesOrgBasedPlantData?.map(item => [item.code, item]))?.values());
  return sortByCode(uniquePlantData);
}

export const filterAndMapWarehouseData = (selectedPlant, regionBasedSalesOrgData) => {
  const plantCodes = selectedPlant?.map(item => item.code)

  const plantBasedWarehouseData = regionBasedSalesOrgData?.salesOrgData
    .filter(obj => 
      plantCodes.includes(obj.MDG_MAT_PLANT) && 
      obj.MDG_MAT_WAREHOUSE && 
      obj.MDG_MAT_WAREHOUSE.trim() !== ""
    )
    .map(obj => ({
      code: obj.MDG_MAT_WAREHOUSE,
      desc: obj.MDG_MAT_WAREHOUSE_DESC
    }));

  const uniqueWarehouseData = Array.from(
    new Map(plantBasedWarehouseData.map(item => [item.code, item])).values()
  );

  return sortByCode(uniqueWarehouseData);
};


export const checkDataValidation = (params, allRows=[], uniqueMaterials, value, templateName) => {
  const { Material } = params;
  if (templateName === TEMPLATE_KEYS?.LOGISTIC) {
    if (!uniqueMaterials.includes(Material)) {
      return "matError";
    }
    const isDuplicate = allRows?.some(row => row.Material === Material && row.AltUnit === value);
    
    if (isDuplicate) {
      return "altUnitError";
    }
  
    return true;
  }

  else if (templateName === TEMPLATE_KEYS?.UPD_DESC) {
    if (!uniqueMaterials.includes(Material)) {
      return "matError";
    }
    const isDuplicate = allRows?.some(row => row.Material === Material && row.Langu === value);
    
    if (isDuplicate) {
      return "languError";
    }
  
    return true;
  }
  
};


export function removeHiddenAndEmptyObjects(dataArray) {
  for (let i = 0; i < dataArray.length; i++) {
    let view = dataArray[i];
    view.cards = view.cards.filter((card) =>
      card.cardDetails.some((item) => item.visibility !== "Hidden")
    );
    if (view.cards.length === 0) {
      dataArray.splice(i, 1);
      i--; // Adjust index after removing element
    }
  }
  return dataArray;
}

export function transformStructureForAllTabsData(data) {
  const result = {};
  data?.forEach((obj) => {
    Object.keys(obj).forEach((key) => {
      if (!result[key]) {
        result[key] = {};
      }
      const nestedData = obj[key];
      Object.assign(result[key], nestedData);
    });
  });
  return result
}

export function convertForBasicDataMandatoryFields(data){
  let result = {};
  if (data?.basic) {
    Object.assign(result, data.basic);
  }
  return result;
}
export function checkOrgsKeys(data, viewType) {
  const uniqueCombinations = generateUniqueCombinations(data);

  if (!uniqueCombinations || typeof uniqueCombinations !== "object") {
    return { allValid: false, validCount: 0 };
  }

  const viewData = uniqueCombinations[viewType];

  if (!viewData) {
    return { allValid: false, validCount: 0 };
  }

  const { displayCombinations, requiredKeys } = viewData;

  if (!Array.isArray(displayCombinations) || displayCombinations.length === 0) {
    return { allValid: false, validCount: 0 };
  }
  const foundCombinations = new Set();

  data.forEach((item) => {
    let keyParts = requiredKeys.map(key => item[key]?.value?.code || item[key]?.code).filter(Boolean);

    if (keyParts.length === requiredKeys.length) {
      const combinationKey = keyParts.join("-");
      if (displayCombinations.includes(combinationKey)) {
        foundCombinations.add(combinationKey);
      }
    }
  });

  return {
    allValid: foundCombinations.size === displayCombinations.length,
    validCount: foundCombinations.size
  };
}


export function checkObjectAndCount(obj, mandatoryFields) {
  let totalCount = 0;
  let hasNullValue = false;
  
  Object.keys(obj).forEach(key => {
    totalCount++;
    
    // Check if all mandatory fields exist and have values
    if (mandatoryFields && Array.isArray(mandatoryFields)) {
      const missingMandatoryField = mandatoryFields.some(field => {
        const jsonName = field.jsonName;
        return !obj[key].hasOwnProperty(jsonName) || obj[key][jsonName] === null || obj[key][jsonName] === undefined;
      });
      
      if (missingMandatoryField) {
        hasNullValue = true;
      }
    } else {
      // Fallback to original behavior if mandatoryFields is not provided
      const nestedValues = Object.values(obj[key]);
      if (nestedValues?.includes(null)) {
        hasNullValue = true;
      }
    }
  });

  return {
    totalCount: totalCount,
    allValid: !hasNullValue
  };
}

export function checkAllKeys(obj, keysToCheck) {
  for (let key in obj) {
    const hasAllKeys = keysToCheck.every(k => obj[key]?.hasOwnProperty(k?.jsonName) && obj[key][k?.jsonName] !== null);
    if (!hasAllKeys) {
        return false;
    }
  }
  return true;
}

export function checkCommonView(firstArray, secondArray) {
  return secondArray?.some(item => firstArray?.includes(item));
}

export function getMissingValuesForBasic(array, obj){
  const requiredKeys = array;
  const missingValues = [];
  requiredKeys.forEach(item => {
      const jsonName = item.jsonName;
      const fieldName = item.fieldName;
      if (!obj?.hasOwnProperty(jsonName) || obj[jsonName] === null) {
          missingValues.push(fieldName);
      }
  });

  return missingValues;
}
export function getMissingValues(array, objOrObjMap) {
  const requiredKeys = array;
  const missingValues = {};

  if (typeof objOrObjMap === 'object' && !Array.isArray(objOrObjMap)) {
    if (Object.keys(objOrObjMap).some(key => typeof objOrObjMap[key] === 'object')) {
      Object.keys(objOrObjMap).forEach(combinationKey => {
        const obj = objOrObjMap[combinationKey];
        missingValues[combinationKey] = [];
        
        requiredKeys.forEach(item => {
          const jsonName = item.jsonName;
          const fieldName = item.fieldName;
          if (!obj || !obj.hasOwnProperty(jsonName) || obj[jsonName] === null || obj[jsonName] === undefined) {
            missingValues[combinationKey].push(fieldName);
          }
        });
        
        if (missingValues[combinationKey].length === 0) {
          delete missingValues[combinationKey];
        }
      });
      return missingValues;
    } else {
      const singleMissingValues = [];
      requiredKeys.forEach(item => {
        const jsonName = item.jsonName;
        const fieldName = item.fieldName;
        if (!objOrObjMap?.hasOwnProperty(jsonName) || objOrObjMap[jsonName] === null || objOrObjMap[jsonName] === undefined) {
          singleMissingValues.push(fieldName);
        }
      });
      return singleMissingValues;
    }
  }
  return Array.isArray(objOrObjMap) ? [] : missingValues;
}


export function generateUniqueCombinations(orgData,payloadData = '',matId = '',dispatch = '') {
  const viewMappings = [
    { view: "Basic Data", requiredKeys: [], requiredKeysRedux: [] },
    { view: "Sales", requiredKeys: ["salesOrg", "dc"], requiredKeysRedux: ["salesOrg", "dc"] },
    { view: "Purchasing", requiredKeys: ["plant"], requiredKeysRedux: ["plant"] },
    { view: "MRP", requiredKeys: ["plant","mrpProfile"], requiredKeysRedux: ["plant"] },
    { view: "Plant Data|Storage", requiredKeys: ["plant", "sloc"], requiredKeysRedux: ["plant","sloc"] },
    { view: "Accounting", requiredKeys: ["plant"], requiredKeysRedux: ["plant"] },
    { view: "Costing", requiredKeys: ["plant"], requiredKeysRedux: ["plant"] },
    { view: "Sales-Plant", requiredKeys: ["plant"], requiredKeysRedux: ["plant"] },
    { view: "Warehouse", requiredKeys: ["warehouse"], requiredKeysRedux: ["warehouse"] },
    { view: "Work Scheduling", requiredKeys: ["plant"], requiredKeysRedux: ["plant"] },
    { view: "BOM", requiredKeys: ["plant"], requiredKeysRedux: ["plant"] },
    { view: "Source List", requiredKeys: ["plant"], requiredKeysRedux: ["plant"] },
    { view: "PIR", requiredKeys: ["plant"], requiredKeysRedux: ["plant"] },
    { view : "Plant Data|Storage-Plant", requiredKeys: ["plant"], requiredKeysRedux: ["plant"] },
  ];

  const results = {};

  viewMappings.forEach(({ view, requiredKeys, requiredKeysRedux }) => {
    const displayCombinations = new Set();
    const reduxCombinations = new Set();

    orgData?.forEach((org) => {
      const displayValues = requiredKeys.map((key) => org[key]?.value?.code || org[key]?.code || "N/A");
      const reduxValues = requiredKeysRedux.map((key) => org[key]?.value?.code || org[key]?.code || "N/A");

      displayCombinations.add(displayValues.join("-"));
      reduxCombinations.add(reduxValues.join("-"));
    });

    results[view] = {
      displayCombinations: Array.from(displayCombinations),
      reduxCombinations: Array.from(reduxCombinations),
      requiredKeys,
    };
    if(view !== 'Basic Data' && dispatch && payloadData){
      const missingObjects = Object.keys(payloadData[view]? payloadData[view]: {})?.filter(data => 
        !results[view]?.reduxCombinations.some(comb => comb === data)
      );
      missingObjects.forEach(missItem => dispatch(removeAccordionCombination({materialID:matId, viewID:view, itemID:missItem})))
    }
  });

  return results;
}

export function getPlantCodes(data) {
  let codes = [];
  data.forEach(item => {
      if (item?.plant && item?.plant?.value?.code) {
          codes.push(item?.plant?.value?.code);
      }
  });
  return codes;
}

export function getMissingElements(codesArray, obj, propertyArray) {
  const missingFields = [];
  codesArray?.forEach(code => {
    const objValue = obj[code];
    if (objValue) {
      const missingProperties = propertyArray.filter(prop => !objValue.hasOwnProperty(prop?.jsonName) || objValue[prop?.jsonName] === null);
      if (missingProperties.length > 0) {
        missingFields.push(code);
      }
    } else {
      missingFields.push(code);
    }
  });

  return { missingFields };
}

export function filterNullValues(array, obj) {
  return array.filter(element => {
    const objValue = obj[element];
    if (objValue) {
      return Object.values(objValue).includes(null);
    }
    return false;
  });
}

export function appendPrefixByJavaKey(javaKey, number) {
    if (!prefixMap[javaKey]) {
      throw new Error(`Invalid Java key: ${javaKey}`);
    }
    const prefix = prefixMap[javaKey];
    return `${prefix}${number}`;
}

export function checkIncludedAndValidated(data) {
  for (let obj of data) {
      if (obj.included === true && obj.validated !== true) {
          return false; 
      }
  }
  return true;
}


export function getValidationStatus(row) {
  if (row?.validated === true) {
      return "success";
  } else if (row?.validated === false) {
      return "error";
  }
  return "default";
}

export function filterMrpCombination(plantArray, mrpDisplayArray) {
  if (!plantArray.length || !mrpDisplayArray.length) {
    return [];
  }
  let mrpCombination = mrpDisplayArray.filter(item => 
    plantArray.some(plant => item.includes(plant))
  );
  
  return mrpCombination;
}


export function convertKeysName(originalArray){
  const convertedArray = originalArray?.map(item => ({
    code: item[DT_KEY_NAMES.MDG_MAT_DIVISION], 
    desc: item[DT_KEY_NAMES.MDG_MAT_DIVISION_DESC]
  }));
  return convertedArray
}

export const getCurrentDate = () => {
  const utcTimestamp = Date.UTC(
    new Date().getUTCFullYear(),
    new Date().getUTCMonth(),
    new Date().getUTCDate(),
    new Date().getUTCHours(),
    new Date().getUTCMinutes(),
    new Date().getUTCSeconds()
  );

  return {
    sapFormat: `/Date(${utcTimestamp})/`,
    humanReadable: moment(utcTimestamp).format('DD MMM YYYY HH:mm:ss UTC')
  };
};

export const CURRENT_DATE = getCurrentDate().sapFormat;

export function segregateChangeLogData(data, templateName) {
  const result = { 
    RequestId: data?.RequestId,
    changeLogId: data?.changeLogId
  };
  if (data[templateName]) {
    data[templateName].forEach(log => {
      const material = log.ObjectNo.split('$$')[0];
      if (!result[material]) {
        result[material] = {};
      }
      if (!result[material][templateName]) {
        result[material][templateName] = [];
      }
      result[material][templateName].push({
        ObjectNo: log.ObjectNo,
        ChangedBy: log.ChangedBy,
        ChangedOn: log.ChangedOn,
        FieldName: log.FieldName,
        PreviousValue: log.PreviousValue,
        CurrentValue: log.CurrentValue,
        SAPValue:log.SAPValue,
      });
    });
  }
  return result;
}

export function getObjectValue(obj, key) {
  if (obj?.hasOwnProperty(key)) {
    return obj[key];
  }
  return null;
}

export const convertDate = (dateString) => {
  const timestamp = parseInt(dateString.match(/\d+/)[0]);
  return moment(timestamp).format("YYYY-MM-DD HH:mm");
};

export function extractDataBaedOnTemplateName(changeLogs, templateName) {
  if (!changeLogs || !Array.isArray(changeLogs)) {
    return [];
  }
  if (changeLogs.every(log => 
    log.hasOwnProperty('ObjectNo') && 
    log.hasOwnProperty('ChangedBy') && 
    log.hasOwnProperty('ChangedOn')
  )) {
    return changeLogs;
  }
  let result = [];
  changeLogs?.forEach(changeLog => {
    if (changeLog[templateName]) {
      result.push(...changeLog[templateName]);
    }
  });
  
  return result;
}

export function getSegregatedPart(objectNo, partIndex) {
  const parts = objectNo.split('$$');
  if (partIndex >= 1 && partIndex <= parts.length) {
      return parts[partIndex - 1];
  } else {
      return null;
  }
}


export const getTemplateManipulationValue = (templateKey, label) => {
  if (!templateKey || !label) return null;

  const templateData = TEMPLATE_NAME_MANIPULATION[templateKey];

  if (typeof templateData === 'string') {
    return templateData;
  }
  if (typeof templateData === 'object') {
    return templateData[label];
  }
  return null;
};

export function mergeArrays(response) {
  const merged = {};
  response.forEach(entry => {
    Object.keys(entry).forEach(key => {
      if (Array.isArray(entry[key]) && entry[key].length > 0) {
        if (!merged[key]) {
          merged[key] = [];
        }
        merged[key] = merged[key].concat(entry[key]);
      }
    });
  });
  return merged;
}

/**
 * Sets the current time components on a given date object
 * @param {Date} dateValue - The date object to update
 * @returns {Date} The date object with current time components
 */
export function setCurrentTimeToDate(dateValue) {
  if (!dateValue) return null;
  const currentTime = new Date();
  dateValue.setHours(currentTime.getHours());
  dateValue.setMinutes(currentTime.getMinutes());
  dateValue.setSeconds(currentTime.getSeconds());
  dateValue.setMilliseconds(currentTime.getMilliseconds());
  return dateValue;
}


export function getKeysWithOnlyNumbers(obj) {
  const result = {};
  for (const key in obj) {
      if (obj.hasOwnProperty(key) && /^\d+$/.test(key)) {
          result[key] = obj[key];
      }
  }
  return result;
}

export const getPreviousValueFromPayload = (
  materialID,
  viewName,
  plantData,
  jsonName,
  createPayloadCopyForChangeLog
) => {
  try {
    const materialData = Object.entries(createPayloadCopyForChangeLog || {})
      .find(([key]) => key === materialID)?.[1];
    if (plantData) {
      return materialData?.payloadData[viewName]?.[plantData]?.[jsonName] || "-";
    }
    return materialData?.payloadData[viewName]?.[jsonName] || "-";
  } catch (error) {
    return "-";
  }
};


export function splitPlantData(data, number) {
  const parts = data.split('-');
  if (number === 0) {
    return '';
  } else if (number === 1) {
      return `$$${parts[0]}`;
  } else if (number === 2) {
      return `$$${parts[0]}$$${parts[1]}`;
  } else {
      return "Invalid number";
  }
}

export function transformChangeLogArray(updatedCreateTemplateArray) {
  const result = {}
  updatedCreateTemplateArray.forEach(log => {
    const material = log.ObjectNo.split('$$')[0];
    if (!result[material]) {
      result[material] = {};
    }
    if (!result[material][log.tableName]) {
      result[material][log.tableName] = [];
    }
    result[material][log.tableName].push({
      ObjectNo: log.ObjectNo,
      ChangedBy: log.ChangedBy,
      ChangedOn: log.ChangedOn,
      FieldName: log.FieldName,
      PreviousValue: log.PreviousValue,
      CurrentValue: log.CurrentValue,
      SAPValue: log.SAPValue
    });
  });

  return result;
}

/**
 * Transforms material data to use material numbers as keys instead of numeric IDs
 * @param {Object} materialData - Original material data with numeric keys
 * @returns {Object} Transformed material data with material numbers as keys
 */
export function transformMaterialNumberKeys(materialData) {
  if (!materialData || typeof materialData !== 'object') {
    return {};
  }

  const transformedData = {};
  Object.keys(materialData).forEach(key => {
    const materialNumber = materialData[key]?.headerData?.materialNumber;
    if (materialNumber) {
      transformedData[materialNumber] = materialData[key];
    }
  });
  
  return transformedData;
}

/**
 * Handles changelog data by preventing duplicates and maintaining data structure
 * @param {Object} existingData - Current changelog data
 * @param {Object} newData - New changelog data to be merged
 * @returns {Object} Updated changelog data
 */
export const handleChangeLogData = (existingData, newData) => {
  const { RequestId, changeLogId, ChildRequestId, ...materialData } = newData;
  const result = { ...existingData };
  Object.entries(materialData).forEach(([material, tableData]) => {
    if (!result[material]) {
      result[material] = {};
    }

    Object.entries(tableData).forEach(([tableName, entries]) => {
      if (!result[material][tableName]) {
        result[material][tableName] = [];
      }
      entries.forEach(newEntry => {
        const existingIndex = result[material][tableName].findIndex(
          item => item.ObjectNo === newEntry.ObjectNo && item.FieldName === newEntry.FieldName
        );

        if (existingIndex !== -1) {
          result[material][tableName][existingIndex] = {
            ...result[material][tableName][existingIndex],
            ...newEntry
          };
        } else {
          result[material][tableName].push(newEntry);
        }
      });
    });
  });
  result.RequestId = RequestId;
  result.changeLogId = changeLogId;
  result.ChildRequestId = ChildRequestId
  return result;
};

export const getPreviousValueForUnitOfMeasure = (
  materialID,
  uomId,
  fieldName,
  payloadData
) => {
  let previousValue = '';
  if (!payloadData || typeof payloadData !== 'object') {
    return previousValue;
  }

  const materialData = payloadData[materialID];
  if (!materialData) {
    return previousValue;
  }
  const unitsOfMeasureArray = materialData.unitsOfMeasureData || [];
  const matchingUOM = unitsOfMeasureArray.find(uom => 
    uom.UomId === uomId || uom.id === uomId
  );
  if (matchingUOM) {
    previousValue = matchingUOM[fieldName] || '';
  }

  return previousValue;
};

export const getPreviousValueForEanData = (
  materialID,
  eanId,
  fieldName,
  payloadData
) => {
  let previousValue = '';
  if (!payloadData || typeof payloadData !== 'object') {
    return previousValue;
  }
  const materialData = payloadData[materialID] || Object.values(payloadData).find(data => data?.headerData?.id === materialID);
  if (!materialData) {
    return previousValue;
  }
  const eanDataArray = materialData?.eanData || [];
  const matchingUOM = eanDataArray?.find(uom => 
    uom.EanId === eanId || uom.EanId === eanId
  );
  if (matchingUOM) {
    previousValue = matchingUOM[fieldName] || '';
  }
  return previousValue;
};

export const getPreviousValueForAdditionalData = (
  materialID,
  jsonName,
  payloadData,
  language
) => {
  let previousValue = '';
  
  if (!payloadData || typeof payloadData !== 'object') {
    return previousValue;
  }

  const materialData = payloadData[materialID];
  if (!materialData) {
    return previousValue;
  }
  const additionalDataArray = materialData.additionalData || [];
  
  const matchingData = additionalDataArray.find(data => 
    data.Material === materialID && 
    data.language === language
  );

  if (matchingData) {
    previousValue = matchingData[jsonName] || '';
  }

  return previousValue;
};


  export const avatarColors = [colors.primary.main, colors.primary.accent, colors.primary.medium, colors.blue.main, colors.blue.indigo, colors.blue.sky, colors.secondary.teal, colors.secondary.green, colors.secondary.lime, colors.secondary.yellow, colors.secondary.amber, colors.icon.orange, colors.warning.orange, colors.warning.deep, colors.error.mild, colors.error.dark, colors.icon.purple, colors.success.vibrant, colors.info.bright];

  export const getAvatarColor = (str) => {
    const hash = str.split("").reduce((acc, char) => char.charCodeAt(0) + acc, 0);
    return avatarColors[hash % avatarColors.length];
  };

/**
 * Updates the payload data by merging transformed payload with existing payload
 * @param {Object} singlePayloadData - Original payload data
 * @param {Object} transformedPayload - New payload data to be merged
 * @returns {Object} Updated payload data
 */
export const updateExtendWithUploadPayloadData = (singlePayloadData, transformedPayload) => {
  const numericKeysPayload = Object.keys(singlePayloadData)?.reduce((acc, key) => {
    if (/^\d+$/.test(key)) {
      acc[key] = singlePayloadData[key];
    }
    return acc;
  }, {});
  const updatedPayload = JSON.parse(JSON.stringify(numericKeysPayload));
  
  Object.keys(singlePayloadData).forEach(key => {
    if (!/^\d+$/.test(key)) {
      updatedPayload[key] = singlePayloadData[key];
    }
  });
  
  const materialIds = Object.keys(transformedPayload);
  
  materialIds.forEach(materialId => {
    // Find the key in updatedPayload where headerData.materialNumber matches materialId
    const matchingKey = Object.keys(updatedPayload).find(key => 
      updatedPayload[key]?.headerData?.materialNumber === materialId
    );
    
    if (matchingKey) {
      // If matching material found, copy the payloadData
      if (transformedPayload[materialId]?.payloadData) {
        if (!updatedPayload[matchingKey]?.payloadData) {
          updatedPayload[matchingKey].payloadData = {};
        }
        
        // Copy all payloadData from transformedPayload to updatedPayload
        Object.keys(transformedPayload[materialId]?.payloadData)?.forEach(viewKey => {
          if (!updatedPayload[matchingKey].payloadData[viewKey]) {
            updatedPayload[matchingKey].payloadData[viewKey] = {};
          }
          
          // For each item in the view
          Object.keys(transformedPayload[materialId]?.payloadData[viewKey])?.forEach(itemKey => {
            if (!updatedPayload[matchingKey].payloadData[viewKey][itemKey]) {
              updatedPayload[matchingKey].payloadData[viewKey][itemKey] = {};
            }
            
            // For each property in the item
            const transformedItem = transformedPayload[materialId].payloadData[viewKey][itemKey];
            const updatedItem = updatedPayload[matchingKey].payloadData[viewKey][itemKey];
            
            // Copy all properties except specific IDs
            Object.keys(transformedItem)?.forEach(propKey => {
              // Skip PlantId, SalesId, WarehouseId to preserve original values
              if (propKey !== 'PlantId' && propKey !== 'SalesId' && propKey !== 'WarehouseId' && !propKey.endsWith('Id')) {
                updatedItem[propKey] = transformedItem[propKey];
              }
            });
          });
        });
      }
    }
  });
  return updatedPayload;
};

export const updateExtendPayloadData = (singlePayloadData, transformedPayload) => {
  const updatedPayload = JSON.parse(JSON.stringify(singlePayloadData));
  const materialIds = Object.keys(transformedPayload);
  
  materialIds.forEach(materialId => {
    if (updatedPayload[materialId]) {
      if (transformedPayload[materialId].headerData) {
        updatedPayload[materialId].headerData = {
          ...updatedPayload[materialId].headerData,
          ...transformedPayload[materialId].headerData
        };
      }
      
      if (transformedPayload[materialId].payloadData) {
        if (!updatedPayload[materialId].payloadData) {
          updatedPayload[materialId].payloadData = {};
        }
        
        Object.keys(transformedPayload[materialId].payloadData).forEach(key => {
          updatedPayload[materialId].payloadData[key] = transformedPayload[materialId].payloadData[key];
        });
      }
      
      Object.keys(transformedPayload[materialId]).forEach(key => {
        if (key !== 'headerData' && key !== 'payloadData') {
          updatedPayload[materialId][key] = transformedPayload[materialId][key];
        }
      });
    }
  });
  
  return updatedPayload;
};

export const addMissingViews = (plantIds, selectedViews, selectedMaterialPayload, selectedMaterialID, orgRow, dispatch, pushMaterialDisplayData) => { 
  if (!plantIds || plantIds.length === 0) return;
  selectedViews.forEach(view => {
    if ([
      MATERIAL_VIEWS.MRP, 
      MATERIAL_VIEWS.WORKSCHEDULING, 
      MATERIAL_VIEWS.SALES_PLANT, 
      MATERIAL_VIEWS.PURCHASING, 
      MATERIAL_VIEWS.ACCOUNTING, 
      MATERIAL_VIEWS.COSTING,
      MATERIAL_VIEWS.STORAGE
    ].includes(view)) {
      plantIds?.forEach(plantId => {
        dispatch(
          pushMaterialDisplayData({
            materialID: selectedMaterialID,
            viewID: view,
            itemID: plantId,
            data: {} 
          })
        );
      });
    }
     else if (view === MATERIAL_VIEWS.SALES) {
      const salesCombinations = orgRow
        ?.filter(row => row.salesOrg?.code && row.dc?.value?.code)
        ?.map(row => ({
          salesOrg: row.salesOrg.code,
          distrChan: row.dc.value.code
        }));
      
      if (salesCombinations.length === 0) {
        dispatch(
          pushMaterialDisplayData({
            materialID: selectedMaterialID,
            viewID: view,
            itemID: "",
            data: {}
          })
        );
      } else {
        salesCombinations.forEach(combo => {
          const salesId = `${combo.salesOrg}-${combo.distrChan}`;
          dispatch(
            pushMaterialDisplayData({
              materialID: selectedMaterialID,
              viewID: view,
              itemID: salesId,
              data: {}
            })
          );
        });
      }
    } else if (view === MATERIAL_VIEWS.WAREHOUSE) {
      const warehouseCombinations = orgRow
        .filter(row => row.warehouse?.value?.code)
        .map(row => row.warehouse.value.code);
        warehouseCombinations.forEach(warehouseCode => {
          dispatch(
            pushMaterialDisplayData({
              materialID: selectedMaterialID,
              viewID: view,
              itemID: warehouseCode,
              data: {}
            })
          );
        });
    } 
  });
};


export const sortByCode = (data) => {
  if (!Array.isArray(data)) return data;
  return [...data]?.sort((a, b) => {
    const codeA = String(a.code);
    const codeB = String(b.code);
    return codeA.localeCompare(codeB);
  });
};
// To convert both SAP date and Data base date format to required calendar format
export function convertSAPDateForCalendar(sapDate) {
  if (!sapDate || typeof sapDate !== 'string') {
    return null;
  }
  // Handle SAP `/Date(1739160000000)/` format
  if (sapDate.includes('/Date(')) {
    try {
      const timestamp = parseInt(sapDate.replace('/Date(', '').replace(')/', ''));
      return isNaN(timestamp) ? null : new Date(timestamp);
    } catch (error) {
      return null;
    }
  }
  // Handle "YYYY-MM-DD HH:mm:ss.SSS" format
  const datePattern = /^(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})(\.\d+)?$/;
  const match = sapDate.match(datePattern);
  if (match) {
    try {
      const [_, year, month, day, hours, minutes, seconds] = match;
      return new Date(`${year}-${month}-${day}T${hours}:${minutes}:${seconds}Z`);
    } catch (error) {
      return null;
    }
  }
  return null;
}

export const formatDateValue = (value) => {
  return value?.toString().startsWith('/Date(') && value?.toString().endsWith(')/') 
    ? convertDate(value)
    : value;
};
export const getKeysValue = (key,value,tabsData) =>{
  let val = '';
  for (const [section, fields] of Object.entries(tabsData)) {
    for (const field of fields) {
      if (field?.jsonName === key) {
        val = field?.fieldPriority === 'CopyRef' ? value : field?.fieldPriority === 'ApplyBus' ? field?.value : ''; 
        return val;
      }
    }
  }
}

export function removeOptionsFromOrgData(orgData) {
  if (!orgData || !Array.isArray(orgData)) return [];
  
  return orgData.map(org => ({
    ...org,
    plant: org.plant ? { value: org.plant.value } : org.plant,
    dc: org.dc ? { value: org.dc.value } : org.dc,
    sloc: org.sloc ? { value: org.sloc.value} : org.sloc,
    warehouse: org.warehouse ? { value: org.warehouse.value } : org.warehouse
  }));
}

/**
 * Gets an item from localStorage with optional JSON parsing
 * @param {string} key - The key to retrieve from localStorage
 * @param {boolean} parseJSON - Whether to parse the value as JSON (default: true)
 * @param {any} defaultValue - Value to return if key doesn't exist (default: null)
 * @returns {any} The retrieved value or defaultValue if not found
 */
export const getLocalStorage = (key, parseJSON = true, defaultValue = null) => {
  try {
    const value = localStorage.getItem(key);
    if (value === null) return defaultValue;
    return parseJSON ? JSON.parse(value) : value;
  } catch (error) {
    return defaultValue;
  }
};

/**
 * Sets an item in localStorage with optional JSON stringifying
 * @param {string} key - The key to set in localStorage
 * @param {any} value - The value to store
 * @param {boolean} stringify - Whether to stringify the value as JSON (default: true)
 * @returns {boolean} True if successful, false if failed
 */
export const setLocalStorage = (key, value, stringify = true) => {
  try {
    const valueToStore = stringify ? JSON.stringify(value) : value;
    localStorage.setItem(key, valueToStore);
    return true;
  } catch (error) {
    return false;
  }
};

/**
 * Removes a specific item from localStorage by key
 * @param {string} key - The key to remove from localStorage
 * @returns {boolean} True if successful, false if failed
 */
export const clearLocalStorageItem = (key) => {
  try {
    if (!key) {
      return false;
    }
    localStorage.removeItem(key);
    return true;
  } catch (error) {
    return false;
  }
};


export function capitalize(str) {
  if (!str) return "";
  const arr = str?.split(" ");
  for (let i = 0; i < arr?.length; i++) {
    arr[i] = arr[i].charAt(0)?.toUpperCase() + arr[i]?.slice(1)?.toLowerCase();
  }
  return arr.join(" ");
}

export const getRequestTypeFromId = (requestId) => {
  if (!requestId || typeof requestId !== 'string') return '';
  const prefix = requestId.substring(0, 3);
  const prefixToTypeMap = {
    'NMA': 'Create',
    'EMA': 'Extend',
    'CMA': 'Change',
    'NME': 'Create with Upload',
    'CME': 'Change with Upload',
    'EME': 'Extend with Upload',
    'FCA': 'Finance Costing'
  };
  
  return prefixToTypeMap[prefix] || '';
};

export const addViewForWithRefrence = (
  plantIds, 
  selectedViews, 
  selectedMaterialPayload, 
  selectedMaterialID, 
  orgRow, 
  dispatch, 
  pushMaterialDisplayData,
  MATERIAL_VIEWS
) => {
  if (!plantIds || plantIds.length === 0) return;
  
  selectedViews.forEach(view => {
    if ([
      MATERIAL_VIEWS.MRP, 
      MATERIAL_VIEWS.WORKSCHEDULING, 
      MATERIAL_VIEWS.SALES_PLANT, 
      MATERIAL_VIEWS.PURCHASING, 
      MATERIAL_VIEWS.ACCOUNTING, 
      MATERIAL_VIEWS.COSTING,
    ].includes(view)) {
      const viewData = selectedMaterialPayload[view] || {};
      plantIds.forEach(plantId => {
        if (!viewData[plantId]) {
          dispatch(
            pushMaterialDisplayData({
              materialID: selectedMaterialID,
              viewID: view,
              itemID: plantId,
              data: {}
            })
          );
        }
      });
    } else if (view === MATERIAL_VIEWS.SALES) {
      const salesCombinations = orgRow
        ?.filter(row => row.salesOrg?.code && row.dc?.value?.code)
        ?.map(row => ({
          salesOrg: row.salesOrg.code,
          distrChan: row.dc.value.code
        }));
      
      const viewData = selectedMaterialPayload[view] || {};
      
      if (salesCombinations.length === 0) {
        if (!viewData[""]) {
          dispatch(
            pushMaterialDisplayData({
              materialID: selectedMaterialID,
              viewID: view,
              itemID: "",
              data: {}
            })
          );
        }
      } else {
        salesCombinations.forEach(combo => {
          const salesId = `${combo.salesOrg}-${combo.distrChan}`;
          if (!viewData[salesId]) {
            dispatch(
              pushMaterialDisplayData({
                materialID: selectedMaterialID,
                viewID: view,
                itemID: salesId,
                data: {}
              })
            );
          }
        });
      }
    } else if (view === MATERIAL_VIEWS.WAREHOUSE) {
      const warehouseCombinations = orgRow
        .filter(row => row.warehouse?.value?.code)
        .map(row => row.warehouse.value.code);
      
      const viewData = selectedMaterialPayload[view] || {};
      
      warehouseCombinations.forEach(warehouseCode => {
        if (!viewData[warehouseCode]) {
          dispatch(
            pushMaterialDisplayData({
              materialID: selectedMaterialID,
              viewID: view,
              itemID: warehouseCode,
              data: {}
            })
          );
        }
      });
    }
  });
};
/* Used to extract the excel column values for extend copy material*/
export const readExcelFile = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const data = new Uint8Array(e.target.result);
      const workbook = XLSX.read(data, { type: "array" });

      const sheetName = workbook.SheetNames[0];
      const sheet = workbook.Sheets[sheetName];

      const rawJson = XLSX.utils.sheet_to_json(sheet);
      const stringifiedJson = rawJson.map((row) => {
        const stringRow = {};
        for (let key in row) {
          stringRow[key] = String(row[key]);
        }
        return stringRow;
      });
      resolve(stringifiedJson); // Array of objects
    };

    reader.onerror = reject;
    reader.readAsArrayBuffer(file);
  });
};

export const getDTNameAndVersion = (requestType) => {
  const lower = requestType?.toLowerCase() || "";
  if (lower.includes(REQUEST_TYPE.CREATE.toLowerCase())) {
    return {dtName:'MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG',version:"v1",scenario:REQUEST_TYPE.CREATE_WITH_UPLOAD};
  } else if (lower.includes(REQUEST_TYPE.CHANGE.toLowerCase())) {
    return {dtName:'MDG_MAT_CHANGE_TEMPLATE',version:"v4",scenario:REQUEST_TYPE.CHANGE_WITH_UPLOAD};
  } else if (lower.includes(REQUEST_TYPE.EXTEND.toLowerCase())) {
    return {dtName:'MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG',version:"v1",scenario:REQUEST_TYPE.EXTEND_WITH_UPLOAD};
  }
  return ''
}

export function throttle(func, delay) {
  let lastCall = 0;
  return function (...args) {
    const now = new Date().getTime();
    if (now - lastCall >= delay) {
      lastCall = now;
      func.apply(this, args);
    }
  };
}

export const filterButtonsBasedOnTab = (buttons, excludeActionTypes) => {
  return buttons.filter(
    btn => !excludeActionTypes.includes(btn.MDG_MAT_DYN_BTN_ACTION_TYPE)
  );
};



export const filterNavigation = (module,requestId) => {
  const getDestinationAndUrl = (mod) => {
    switch (mod) {
      case MODULE_MAP.MAT:
        return {
          destination: VITE_DESTINATION_MATERIAL_MGMT,
          navigation: "/requestBench/createRequest",
          childRequestUrl :`/data/${requestId?.replace(/\D/g, '')}/child-requests `,
          errorHistoryParentUrl : END_POINTS?.ERROR_HISTORY?.ERROR_LOG,
          errorHistoryChildUrl : END_POINTS?.ERROR_HISTORY?.ERROR_LOG_CHILD,
        };
      case MODULE_MAP.PC:
        return {
          destination: VITE_CW_MDG_PROFITCENTER_MASS_DEST,
          navigation: "/requestBench/ProfitCenterRequestTab",
          childRequestUrl : `/data/getRequestAsPerRequestHeader?requestId=${requestId?.replace(/\D/g, '')}&isSunoco=false`,
          errorHistoryParentUrl : END_POINTS?.ERROR_HISTORY?.ERROR_LOG,
          errorHistoryChildUrl : END_POINTS?.ERROR_HISTORY?.ERROR_LOG_CHILD,
        };
          case MODULE_MAP.CC:
        return {
          destination: VITE_CW_MDG_COSTCENTER_MASS_DEST,
          navigation: "/requestBench/CostCenterRequestTab",
          childRequestUrl : `/data/getRequestAsPerRequestHeader?requestId=${requestId?.replace(/\D/g, '')}&isSunoco=false`,
          errorHistoryParentUrl : END_POINTS?.ERROR_HISTORY?.ERROR_LOG,
          errorHistoryChildUrl :  END_POINTS?.ERROR_HISTORY?.ERROR_LOG_CHILD,
        };
          case MODULE_MAP.GL:
        return {
          destination: VITE_CW_MDG_GENERALLEDGER_MASS_DEST,
          navigation: "/requestBench/GeneralLedgerRequestTab",
          childRequestUrl : `/data/getRequestAsPerRequestHeader?requestId=${requestId?.replace(/\D/g, '')}&isSunoco=false`,
          errorHistoryParentUrl : END_POINTS?.ERROR_HISTORY?.ERROR_LOG,
          errorHistoryChildUrl : END_POINTS?.ERROR_HISTORY?.ERROR_LOG_CHILD,
        };
          case MODULE_MAP.PCG:
        return {
          destination: VITE_CW_MDG_PROFITCENTER_MASS_DEST,
          navigation: "/requestBench/ProfitCenterGroupRequestTab",
          childRequestUrl : `/data/getRequestAsPerRequestHeaderForHierarchyGroup?requestId=${requestId?.replace(/\D/g, '')}`,
          errorHistoryParentUrl : END_POINTS?.ERROR_HISTORY?.ERROR_LOG_HIERARCHY,
          errorHistoryChildUrl : END_POINTS?.ERROR_HISTORY?.ERROR_LOG_CHILD,
        };
          case MODULE_MAP.CCG:
        return {
          destination: VITE_CW_MDG_COSTCENTER_MASS_DEST,
          navigation: "/requestBench/CostCenterGroupRequestTab",
          childRequestUrl :  `/data/getRequestAsPerRequestHeaderForHierarchyGroup?requestId=${requestId?.replace(/\D/g, '')}`,
          errorHistoryParentUrl :END_POINTS?.ERROR_HISTORY?.ERROR_LOG_HIERARCHY,
          errorHistoryChildUrl : END_POINTS?.ERROR_HISTORY?.ERROR_LOG_CHILD,
        };
          case MODULE_MAP.CEG:
        return {
          destination: VITE_CW_MDG_GENERALLEDGER_MASS_DEST,
          navigation: "/requestBench/CostElementGroupRequestTab",
          childRequestUrl : `/data/getRequestAsPerRequestHeaderForHierarchyGroup?requestId=${requestId?.replace(/\D/g, '')}`,
          errorHistoryParentUrl : END_POINTS?.ERROR_HISTORY?.ERROR_LOG_HIERARCHY,
          errorHistoryChildUrl :  END_POINTS?.ERROR_HISTORY?.ERROR_LOG_CHILD,
        };

      default:
        return {};
    }
  };

  const { destination,navigation, childRequestUrl,errorHistoryParentUrl,errorHistoryChildUrl } = getDestinationAndUrl(module,requestId);
  return { destination,navigation,childRequestUrl,errorHistoryParentUrl,errorHistoryChildUrl };
};







