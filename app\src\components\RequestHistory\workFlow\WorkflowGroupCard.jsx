import { useState, useEffect } from 'react';
import WorkflowDiagram from './WorkflowDiagram';
import './workflow.css';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import { colors } from '@constant/colors';

const WorkflowGroupCard = ({ groupName, groupData,materialTypes, onTaskClick }) => {
  const [collapsed, setCollapsed] = useState(false);

  const toggleCollapse = () => setCollapsed(!collapsed);

  useEffect(() => {
    if (!collapsed) {
      // Optional callback to re-render arrows or layout if needed
    }
  }, [collapsed]);

  return (
    <div
      style={{
        border: '1px solid #e0e0e0',
        borderRadius: 8,
        marginBottom: 16,
        background: '#fff',
        boxShadow: '0 2px 8px rgba(0,0,0,0.04)',
        overflow: 'hidden',
        transition: 'box-shadow 0.2s',
      }}
    >
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '16px',
          cursor: 'pointer',
          backgroundImage: "linear-gradient(180deg, rgb(242, 241, 255) 0%, rgb(255, 255, 255) 100%)",
          border: "1px solid #E0E0E0",
          borderBottom: '1px solid #e0e0e0',
        }}
        onClick={toggleCollapse}
      >
        <div>
          <div style={{ fontWeight: 600, fontSize: 18, color: colors?.primary?.main }}>{groupName}{` (${materialTypes.join(", ")})`}</div>
          <div style={{ fontSize: 14, color: colors?.primary?.main, marginTop: 2 }}>
            {groupData?.mdmTaskGroup} • {groupData?.mdmTaskLevelName} 
          </div>
        </div>
        <div
          style={{
            fontSize: 28,
            color: '#fff',
            transition: 'transform 0.2s',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          {collapsed
            ? <KeyboardArrowUpIcon style={{ color: colors?.primary?.main, fontSize: 32 }} />
            : <KeyboardArrowDownIcon style={{ color: colors?.primary?.main, fontSize: 32 }} />
          }
        </div>
      </div>
      {!collapsed && (
        <div style={{ padding: 16, background: '#fff' }}>
          <WorkflowDiagram groupName={groupName} groupData={groupData} onTaskClick={onTaskClick} />
        </div>
      )}
    </div>
  );
};

export default WorkflowGroupCard;
