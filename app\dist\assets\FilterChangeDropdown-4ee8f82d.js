import{m as w,n as J,o as K,p as S,a as t,j as f,ay as Q,bF as M,d5 as U,l as F,B as Z,F as O,E as a,cc as N,b6 as V,d4 as p}from"./index-17b8d91e.js";var P={},D=J;Object.defineProperty(P,"__esModule",{value:!0});var ee=P.default=void 0,re=D(w()),le=K;ee=P.default=(0,re.default)((0,le.jsx)("path",{d:"M16 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V8zm3 16H5V5h10v4h4zM7 17h10v-2H7zm5-10H7v2h5zm-5 6h10v-2H7z"}),"FeedOutlined");const _=8,j=S.createContext({}),te=S.forwardRef((r,v)=>{const s=S.useContext(j);return t("div",{ref:v,...r,...s})});function se(r){const{children:v,...s}=r,i=[];v.forEach(x=>{i.push(x)});const c=i.length,h=48,z=()=>c>8?8*h:i.length*h;return t("div",{ref:r.ref,children:t(j.Provider,{value:s,children:t(p,{itemData:i,height:z()+2*_,width:"100%",outerElementType:te,innerElementType:"ul",itemSize:h,overscanCount:5,itemCount:c,children:({data:x,index:g,style:y})=>{const A=x[g],o={...y,top:y.top+_};return S.cloneElement(A,{style:o})}})})})}const de=({param:r,mandatory:v=!1,dropDownData:s,allDropDownData:i,selectedValues:c,inputState:h,handleSelectAll:z,handleSelectionChange:x,handleMatInputChange:g,handleScroll:y,dropdownRef:A,errors:o,formatOptionLabel:b,handlePopoverOpen:H,handlePopoverClose:R,handleMouseEnterPopover:T,handleMouseLeavePopover:$,isPopoverVisible:L,popoverId:m,popoverAnchorEl:q,popoverRef:B,popoverContent:W,isMaterialNum:k=!1,isLoading:G=!1,isSelectAll:X=!1,singleSelect:n=!1})=>{const E=()=>{const l=k?(s==null?void 0:s[r==null?void 0:r.key])||[]:(s==null?void 0:s[r==null?void 0:r.key])||(i==null?void 0:i[r==null?void 0:r.key])||[];return X&&l.length>0&&!n?["Select All",...l]:l},Y=()=>{if(!n)return c[r.key]||[];const l=c[r.key];return Array.isArray(l)&&l.length>0?l[0]:null};return t(V,{multiple:!n,disableListWrap:!0,ListboxComponent:se,options:E(),getOptionLabel:l=>typeof l=="string"?l:l==="Select All"?"Select All":b(l),value:n?Y():c[r.key]||[],inputValue:k&&!n?h==null?void 0:h.code:void 0,onChange:(l,e)=>{!n&&e.includes("Select All")?z(r.key,E().filter(u=>u!=="Select All")):n?x(r.key,e?[e]:[]):x(r.key,e)},disableCloseOnSelect:!n,renderOption:(l,e,{selected:u})=>{var d,C;return f("li",{...l,style:{display:"flex",alignItems:"center",width:"100%",cursor:"pointer"},children:[!n&&t(Q,{checked:e==="Select All"?((d=c[r.key])==null?void 0:d.length)===E().length-1:(C=c[r.key])==null?void 0:C.some(I=>(I==null?void 0:I.code)===(e==null?void 0:e.code)),sx:{marginRight:1}}),typeof e=="string"||e==="Select All"?t("span",{style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},title:e,children:e}):f("span",{style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},title:`${e==null?void 0:e.code}${e!=null&&e.desc?` - ${e==null?void 0:e.desc}`:""}`,children:[t("strong",{children:e==null?void 0:e.code}),e!=null&&e.desc?` - ${e==null?void 0:e.desc}`:""]})]})},renderTags:(l,e)=>{if(n)return null;const u=l.map(d=>typeof d=="string"?d:b(d)).join("<br />");return l.length>1?f(O,{children:[t(M,{sx:{height:25,fontSize:"0.85rem",".MuiChip-label":{padding:"0 6px"}},label:`${b(l[0])}`,...e({index:0})}),t(M,{sx:{height:25,fontSize:"0.85rem",".MuiChip-label":{padding:"0 6px"}},label:`+${l.length-1}`,onMouseEnter:d=>H(d,u),onMouseLeave:R}),t(U,{id:m,open:L,anchorEl:q,onClose:R,anchorOrigin:{vertical:"bottom",horizontal:"center"},transformOrigin:{vertical:"top",horizontal:"center"},onMouseEnter:T,onMouseLeave:$,ref:B,sx:{"& .MuiPopover-paper":{backgroundColor:F.primary.whiteSmoke,boxShadow:"0px 4px 8px rgba(0, 0, 0, 0.1)",borderRadius:"8px",padding:"10px",fontSize:"0.875rem",color:F.blue.main,border:"1px solid #ddd"}},children:t(Z,{sx:{maxHeight:"270px",overflowY:"auto",padding:"5px"},dangerouslySetInnerHTML:{__html:W}})})]}):l.map((d,C)=>t(M,{sx:{height:25,fontSize:"0.85rem",".MuiChip-label":{padding:"0 6px"}},label:`${b(d)}`,...e({index:C})}))},renderInput:l=>{var e,u;return t(a,{...l,label:v?f(O,{children:[f("strong",{children:["Select ",r.key]})," ",t("span",{style:{color:(u=(e=F)==null?void 0:e.error)==null?void 0:u.dark},children:"*"})]}):`Select ${r.key}`,variant:"outlined",error:!!o[r.key],helperText:o[r.key],onChange:g||void 0,ListboxProps:{onScroll:k?y:void 0,ref:k?A:void 0},InputProps:{...l.InputProps,endAdornment:f(O,{children:[G?t(N,{size:20,sx:{mr:1}}):null,l.InputProps.endAdornment]})},sx:{"& .MuiOutlinedInput-root":{borderRadius:"8px",height:50,boxShadow:"0 2px 4px rgba(0, 0, 0, 0.1)"},"& .MuiInputLabel-root":{fontWeight:500}}})}},r.key)};export{de as F,ee as d};
