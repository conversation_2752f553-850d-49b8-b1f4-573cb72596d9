function b(e,t,n=void 0){const r={};return Object.keys(e).forEach(s=>{r[s]=e[s].reduce((o,a)=>{if(a){const i=t(a);i!==""&&o.push(i),n&&n[a]&&o.push(n[a])}return o},[]).join(" ")}),r}const u=e=>e,l=()=>{let e=u;return{configure(t){e=t},generate(t){return e(t)},reset(){e=u}}},f=l(),d=f,g={active:"active",checked:"checked",completed:"completed",disabled:"disabled",error:"error",expanded:"expanded",focused:"focused",focusVisible:"focusVisible",open:"open",readOnly:"readOnly",required:"required",selected:"selected"};function p(e,t,n="Mui"){const r=g[t];return r?`${n}-${r}`:`${d.generate(e)}-${t}`}function m(e,t,n="Mui"){const r={};return t.forEach(s=>{r[s]=p(e,s,n)}),r}function c(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var s=e.length;for(t=0;t<s;t++)e[t]&&(n=c(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function C(){for(var e,t,n=0,r="",s=arguments.length;n<s;n++)(e=arguments[n])&&(t=c(e))&&(r&&(r+=" "),r+=t);return r}export{m as a,b,C as c,p as g};
