import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  requestHeaderIO: {},
};

const internalOrderSlice = createSlice({
  name: "internalOrder",
  initialState,
  reducers: {
    setRequestHeaderIO: (state, action) => {
      state.requestHeaderIO = action.payload;
    },
    clearRequestHeaderIO: (state) => {
      state.requestHeaderIO = {};
    },
  },
});

export const { setRequestHeaderIO, clearRequestHeaderIO } = internalOrderSlice.actions;
export default internalOrderSlice.reducer; 