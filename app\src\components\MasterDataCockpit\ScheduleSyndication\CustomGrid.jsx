import React, { useEffect, useState } from 'react';
import { DataGrid } from '@mui/x-data-grid';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { useSelector } from 'react-redux';
import moment from 'moment';
import { IconButton, Paper, Tooltip, LinearProgress, Typography } from "@mui/material";
import AutoDeleteOutlinedIcon from "@mui/icons-material/AutoDeleteOutlined";
import DeleteForeverOutlinedIcon from "@mui/icons-material/DeleteForeverOutlined";
import PauseCircleOutlineIcon from '@mui/icons-material/PauseCircleOutline';
import PlayCircleOutlineIcon from '@mui/icons-material/PlayCircleOutline';
import RocketLaunchIcon from '@mui/icons-material/RocketLaunch';
import { styled } from "@mui/material/styles";
import { colors } from "@constant/colors";

import { destination_Admin, destination_CostCenter_Mass, destination_GeneralLedger_Mass, destination_MaterialMgmt, destination_ProfitCenter_Mass } from '../../../destinationVariables';
import { doAjax } from '@components/Common/fetchService';
import ReusableBackDrop from "../../Common/ReusableBackDrop";
import ReusableDialog from '@components/Common/ReusableDialog';


const groupRowsByModuleAndRequestType = (rows) => {
  const grouped = {};
  rows.forEach((row) => {
    const groupKey = `${row.module}-${row.reqType}`;
    if (!grouped[groupKey]) {
      grouped[groupKey] = [];
    }
    grouped[groupKey].push(row);
  });
  return grouped;
};

const getColorForRow = (module, reqType) => {
  const colorMap = {
    "Cost Center-Mass Create": "#f0f8ff",  // Light Blue
    "Cost Center-Mass Change": "#e6ffe6",  // Light Green
    "Profit Center-Mass Create": "#ffe4e1", // Light Pink
    "Profit Center-Mass Change": "#f5f5dc", // Light Beige
    "CC-PC Combo-Mass Create": "#f5f5f5", // Light Grey
    "CC-PC Combo-Mass Change": "#ffffe0", // Light Yellow
    "General Ledger-Mass Create": "#f7bee375", // Light Green (for Mass Create)
    "General Ledger-Mass Change": "#ffcccb75", // Light Red (for Mass Change)
    "General Ledger-Mass Extend": "#fff2cc", // Light Yellow (for Mass Extend)
    "Material-Create with Upload": "#d1e7dd", // Light Teal (for Mass Create)
    "Material-Change with Upload": "#ddd7f8", // Light Pink (for Mass Change)
    "Material-Extend with Upload": "#fff3cd", // Light Yellow (for Mass Extend
  };

  return colorMap[`${module}-${reqType}`] || "#ffffff"; // white fallback
};

const StyledDataGridContainer = styled(Paper)(({ theme }) => ({
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
  borderRadius: '16px',
  overflow: 'hidden',
  border: `1px solid ${colors.primary.border}`,
  '& .MuiDataGrid-root': {
    border: 'none',
    '& .MuiDataGrid-cell': {
      borderBottom: `1px solid ${colors.primary.pale}`,
      padding: '8px 16px',
      '&:focus': {
        outline: 'none',
      },
      '&:focus-within': {
        outline: `2px solid ${colors.primary.main}`,
        outlineOffset: '-2px',
      },
    },
    '& .MuiDataGrid-columnHeaders': {
      backgroundColor: colors.primary.ultraLight,
      borderBottom: `2px solid ${colors.primary.border}`,
      '& .MuiDataGrid-columnHeader': {
        padding: '12px 16px',
        '&:focus': {
          outline: 'none',
        },
        '&:focus-within': {
          outline: `2px solid ${colors.primary.main}`,
          outlineOffset: '-2px',
        },
      },
    },
    '& .MuiDataGrid-row': {
      '&:nth-of-type(even)': {
        backgroundColor: colors.primary.veryLight,
      },
      '&:hover': {
        backgroundColor: `${colors.primary.light}40`,
      },
      '&.Mui-selected': {
        backgroundColor: `${colors.primary.main}20`,
        '&:hover': {
          backgroundColor: `${colors.primary.main}30`,
        },
      },
    },
  },
}));

const StyledLoadingOverlay = styled('div')({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: 'rgba(255, 255, 255, 0.7)',
  zIndex: 10,
});

function CustomLoadingOverlay() {
  return (
    <StyledLoadingOverlay>
      <LinearProgress 
        sx={{ 
          width: '200px',
          '& .MuiLinearProgress-bar': {
            backgroundColor: colors.primary.main,
          }
        }} 
      />
      <Typography 
        variant="body2" 
        sx={{ mt: 2, color: colors.secondary.grey }}
      >
        Loading data...
      </Typography>
    </StyledLoadingOverlay>
  );
}

const CustomDataGrid = (props) => {
  const { row, columns, onRowUpdate, isLoading, getRowIdValue, iconClicked, setIconClicked, fetchSAPScheduler=()=>{} } = props;
  const [rows, setRows] = useState(row || []);
  const [adhocClick, setAdhocClick] = useState(false);
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [messageDialogTitle, setMessageDialogTitle] = useState("");
  const [messageDialogSeverity, setMessageDialogSeverity] = useState("");
  const [adhocConfirmDialog, setAdhocConfirmDialog] = useState(false);
  const [selectedAdhocData, setSelectedAdhocData] = useState({});
  const appSettings = useSelector((state) => state.appSettings);

const getFormatedTime = (timestamp) => {
  const date = new Date(timestamp);
    
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-based
  const day = String(date.getDate()).padStart(2, '0');
  const hours = date.getHours() % 12 || 12; // Convert to 12-hour format
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  const ampm = date.getHours() >= 12 ? 'PM' : 'AM';

  return `${year}/${month}/${day} ${hours}:${minutes}:${seconds} ${ampm}`;
}
  let taskRowDetails = useSelector((state) => state.userManagement.taskData);
  const groupedRows = groupRowsByModuleAndRequestType(rows);
const handleAdhocDialogOpen = () => {
  setMessageDialogSeverity("warning");
  setMessageDialogTitle("Are you sure you want to proceed?");
  setMessageDialogMessage("The Request can not be reverted back to Scheduler Syndication.");
  setAdhocConfirmDialog(true);
}
const handleAdhocDialogClose = () => {
  setSelectedAdhocData({});
  setAdhocConfirmDialog(false);
}
const handleOk = () => {
  setBlurLoading(true);
 setAdhocConfirmDialog(true);
 setAdhocClick(true);
 setLoaderMessage("Request processing please wait.");
 handleAdhocSyncStatusUpdate(selectedAdhocData); 
 handleAdhocDialogClose();
}
  useEffect(() => {
  const sortedData = row.slice().sort((a, b) => {
    if (a.module !== b.module) {
      return a.module.localeCompare(b.module);
    }
    if (a.reqType !== b.reqType) {
      return a.reqType.localeCompare(b.reqType);
    }
    return a.priority - b.priority;
  });
  setRows(sortedData || []);
}, [row]);


  const handleDragEnd = (result) => {
    const { destination, source } = result;
  
    // If there's no destination (dropped outside the table), return early
    if (!destination) return;
  
    // If the source and destination are the same, no change
    if (destination.droppableId === source.droppableId && destination.index === source.index) {
      return;
    }
  
    const sourceGroupKey = source.droppableId;
    const destinationGroupKey = destination.droppableId;
  
    // **Prevent Dragging Between Different Groups (module + reqType)**
    if (sourceGroupKey !== destinationGroupKey) {
      return; // Exit early if trying to move across different groups
    }
  
    // **Clone groupedRows to avoid mutation**
    const updatedGroupedRows = { ...groupedRows, [sourceGroupKey]: [...groupedRows[sourceGroupKey]] };
  
    // Modify the cloned array
    const groupRows = updatedGroupedRows[sourceGroupKey];
    const [movedRow] = groupRows.splice(source.index, 1);
    groupRows.splice(destination.index, 0, movedRow);
  
    // Update priorities within the same group
    groupRows.forEach((row, index) => {
      row.priority = index + 1;
    });
  
    // Flatten updated grouped rows and update rows
    const updatedRows = Object.values(updatedGroupedRows).flat();
    setRows(updatedRows);
    onRowUpdate(updatedRows);
  };
  
  const handleIconClick = (rowId) => {
    setIconClicked((prev) => ({
      ...prev,
      [rowId]: !prev[rowId]  // Toggle the icon state for the specific rowId
    }));
    // setRows((prevRows) =>
    //   prevRows.map((row) =>
    //     row.id === rowId ? { ...row, schedulerStatus: !iconClicked[rowId] ? "Canceled" : row.schedulerStatus } : row
    //   )
    // );
  };

const handleAdhocSync = (user) => {
  let payload = {
      "requestId" : user?.reqId
  }
  const hSuccess = (data) => {
    // Fetch latest scheduler data
    fetchSAPScheduler();
    setAdhocClick(false);
    setBlurLoading(false);
    handleAdhocDialogClose();
    // setMaterialCount(data?.body?.count);
  };
  const hError = (error) => {
    console.log(error);
    handleAdhocDialogClose();
  };
  // Set URL based on module
  if (user.module === "CC-PC Combo") {
    var url = `/${destination_CostCenter_Mass}/scheduler/adhocComboSyndication`;
    doAjax(url, "post", hSuccess, hError, payload);
  } else if (user.module === "Cost Center") {
    var url = `/${destination_CostCenter_Mass}/scheduler/adhocCostCenterSyndication`;
    doAjax(url, "post", hSuccess, hError, payload);
  }  else if (user.module === "Profit Center") {
    var url = `/${destination_ProfitCenter_Mass}/scheduler/adhocProfitCenterSyndication`;
    doAjax(url, "post", hSuccess, hError, payload);
  } else if (user.module === "General Ledger") {
    var url = `/${destination_GeneralLedger_Mass}/scheduler/adhocGeneralLedgersSyndication`;
    doAjax(url, "post", hSuccess, hError, payload);
  } else if (user.module === "Material") {
    var url = `/${destination_MaterialMgmt}/scheduler/adhocMaterialSyndication`;
    doAjax(url, "post", hSuccess, hError, payload);
  }
   else {
    return false;
  }
}
const handleAdhocSyncStatusUpdate = (user) => {
  // setBlurLoading(true);
  var adhocSyndicationPayload = [{
    SchedulerId: user?.id,
    RequestId: user?.reqId,
    RequestType: user?.reqType,
    Module: user?.module,
    Priority: user?.priority,
    ObjectCount: user?.totalObjects,
    ObjectsSuccessCount: user?.objectSuccessCount,
    ObjectsFailureCount: user?.objectFailureCount,
    PendingObjectsCount: user?.pendingObjects,
    ReqScheduledBy: user?.scheduledBy,
    ReqScheduledOn: user?.scheduledOn,
    ReqUpdatedOn: user?.updatedOn,
    SchedulerStatus: "Adhoc Syndication - In Progress",
    RetryCount: user?.retryCount
  }]

    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        // Request status updated & Adhoc Syndication Process Started
        handleAdhocSync(user)
      } else {
        setBlurLoading(false);
      }
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_Admin}/scheduler/updatePriorityAndStatusForScheduledRequests`,
      "post",
      hSuccess,
      hError,
      adhocSyndicationPayload
    );
}
const handleDeleteAdhocSync = (user) => {
  setBlurLoading(true);
  var payloadForScheduler =  [{
      SchedulerId: user?.id,
      RequestId: user?.reqId,
      RequestType: user?.reqType,
      Module: user?.module,
      Priority: user?.priority,
      ObjectCount: user?.totalObjects,
      ObjectsSuccessCount: user?.objectSuccessCount,
      ObjectsFailureCount: user?.objectFailureCount,
      PendingObjectsCount: user?.pendingObjects,
      ReqScheduledBy: user?.scheduledBy,
      ReqScheduledOn: user?.scheduledOn,
      ReqUpdatedOn: user?.updatedOn,
      SchedulerStatus: "Adhoc Syndication - Canceled",
      RetryCount: user?.retryCount
    }]

    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        fetchSAPScheduler();
        setBlurLoading(false);
      } else {
        setBlurLoading(false);
      }
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_Admin}/scheduler/updatePriorityAndStatusForScheduledRequests`,
      "post",
      hSuccess,
      hError,
      payloadForScheduler
    );
}


  return (
    <StyledDataGridContainer style={{ position: 'relative' }}>
      {isLoading && <CustomLoadingOverlay />}
      <DragDropContext onDragEnd={handleDragEnd}>
        <div style={{ maxHeight: '30vh', overflowY: 'auto' }}>
          <table
            style={{
              width: '100%',
              borderCollapse: 'collapse',
              fontFamily: 'Roboto, sans-serif',
            }}
          >
            <thead style={{
              position: 'sticky',
              top: 0,
              backgroundColor: colors.primary.ultraLight,
              zIndex: 1000,
              borderBottom: `2px solid ${colors.primary.border}`,
            }}>
              <tr>
              {columns.map((column) => (
        <th key={column.field}
            style={{
              width: column.width || 'auto',
              padding: '12px 16px',
              textAlign: column.align || 'left',
              fontWeight: 'normal',
            }}
        >
          {column.headerName}
          {column.renderHeader}
        </th>
      ))}
              </tr>
            </thead>

            {Object.keys(groupedRows)?.length ? Object.keys(groupedRows).map((groupKey) => {
            const [module, requestType] = groupKey.split('-');
            const groupRowsData = groupedRows[groupKey];

            return (
            <Droppable droppableId={groupKey} key={groupKey}>
              {(provider) => (
                <tbody
                  ref={provider.innerRef}
                  {...provider.droppableProps}
                  style={{ width: '100%' }}
                >
                  {groupRowsData[0] ? groupRowsData?.map((user, index) => (
                    
                    <Draggable key={user.id} draggableId={user.id?.toString()} index={index} isDragDisabled={!props.showDragIcon}>
                      {(provider, snapshot) => {
                        const rowColor = getColorForRow(user.module, user.reqType);
                        return (
                          <tr
                            ref={provider.innerRef}
                            {...provider.draggableProps}
                            style={{
                              backgroundColor: snapshot.isDragging
                                ? "#e0e0e0"
                                : rowColor,
                              transition: "background-color 0.3s",
                              ...provider.draggableProps.style,
                            }}
                          >
                            {props.showDragIcon ? (
                              <Tooltip 
                                title="Drag to reorder priority"
                                arrow
                                placement="right"
                                PopperProps={{
                                  sx: { zIndex: 1500 },
                                  container: document.body
                                }}
                              ><td
                                {...provider.dragHandleProps}
                                style={{
                                  cursor: "grab",
                                  textAlign: "center",
                                  fontWeight: "bold",
                                  color: "#616161",
                                  padding: "16px",
                                  borderBottom: "1px solid #e0e0e0",
                                }}
                              >
                                ≡
                              </td></Tooltip>
                            ) : (
                              <td
                                style={{
                                  cursor: "grab",
                                  textAlign: "center",
                                  fontWeight: "bold",
                                  color: "#616161",
                                  padding: "16px",
                                  borderBottom: "1px solid #e0e0e0",
                                }}
                              ></td>
                            )}
                            <td
                              style={{
                                padding: "16px",
                                borderBottom: "1px solid #e0e0e0",
                                textAlign: "left",
                                fontWeight: 700,
                              }}
                            >
                              {user.reqId}
                            </td>
                            <td
                              style={{
                                padding: "16px",
                                borderBottom: "1px solid #e0e0e0",
                                textAlign: "left",
                              }}
                            >
                              {user.module}
                            </td>
                            <td
                              style={{
                                padding: "16px",
                                borderBottom: "1px solid #e0e0e0",
                                textAlign: "left",
                              }}
                            >
                              {user.reqType}
                            </td>
                            <td
                              style={{
                                padding: "16px",
                                borderBottom: "1px solid #e0e0e0",
                                textAlign: "left",
                              }}
                            >
                              {user.scheduledBy}
                            </td>
                            <td
                              style={{
                                padding: "16px",
                                borderBottom: "1px solid #e0e0e0",
                                textAlign: "left",
                              }}
                            >
                            {/* {user?.scheduledOn} */}
                            {getFormatedTime(user?.scheduledOn)}
                              {/* {moment(user.scheduledOn).format(
                                appSettings?.dateFormat
                              )} */}
                            </td>
                            <td
                              style={{
                                padding: "16px",
                                borderBottom: "1px solid #e0e0e0",
                                textAlign: "left",
                              }}
                            >
                              {user.totalObjects}
                            </td>
                            <td
                              style={{
                                padding: "16px",
                                borderBottom: "1px solid #e0e0e0",
                                textAlign: "left",
                              }}
                            >
                              {user.pendingObjects}
                            </td>
                            {props?.selectionType === "SAPScheduler" ||
                            props?.selectionType === "AdhocScheduler" ? (
                              <td
                                style={{
                                  padding: "16px",
                                  borderBottom: "1px solid #e0e0e0",
                                  textAlign: "left",
                                }}
                              >
                                {user.objectSuccessCount}
                              </td>
                            ) : (
                              ""
                            )}

                            {props?.selectionType === "SAPScheduler" ||
                            props?.selectionType === "AdhocScheduler" ? (
                              <td
                                style={{
                                  padding: "16px",
                                  borderBottom: "1px solid #e0e0e0",
                                  textAlign: "left",
                                }}
                              >
                                {user.objectFailureCount}
                              </td>
                            ) : (
                              ""
                            )}

                            {props?.selectionType === "SAPScheduler" ||
                            props?.selectionType === "AdhocScheduler" ? (
                              <td
                                style={{
                                  padding: "16px",
                                  borderBottom: "1px solid #e0e0e0",
                                  textAlign: "left",
                                }}
                              >
                                {user.retryCount}
                              </td>
                            ) : (
                              ""
                            )}

                            <td
                              style={{
                                padding: "16px",
                                borderBottom: "1px solid #e0e0e0",
                                textAlign: "left",
                              }}
                            >
                              {user.schedulerStatus}
                            </td>
                            {/* Last column for actions/icons */}
                            <td
                              style={{
                                padding: "16px",
                                borderBottom: "1px solid #e0e0e0",
                                textAlign: "left",
                              }}
                            >
                              {props?.selectionType === "SAPScheduler" ? (
                                (() => {
                                  if (Object?.entries(taskRowDetails).length === 0 && user?.schedulerStatus === "Scheduler - Pending") {
                                    return (
                                      <div style={{ width: "100%", display: "flex", justifyContent: "center" }}>
                                        <>
                                          <Tooltip
                                            title={iconClicked?.[user?.id] ? "Resume" : "Pause"}
                                            arrow
                                            placement="top"
                                            PopperProps={{ sx: { zIndex: 1500 } }}
                                          >
                                            <IconButton
                                              aria-label="Toggle Delete Icon"
                                              onClick={() => handleIconClick(user?.id)}
                                            >
                                              {iconClicked?.[user?.id] ? (
                                                <PlayCircleOutlineIcon sx={{ transition: "color 0.3s ease" }} />
                                              ) : (
                                                <PauseCircleOutlineIcon sx={{ transition: "color 0.3s ease" }} />
                                              )}
                                            </IconButton>
                                          </Tooltip>
                                          {props?.selectionType !== "AttachmentScheduler" && (
                                            <Tooltip title="Adhoc Syndication">
                                              <IconButton
                                                aria-label="View Metadata"
                                                onClick={() => {
                                                  handleAdhocDialogOpen();
                                                  setSelectedAdhocData(user);
                                                }}
                                              >
                                                <RocketLaunchIcon sx={{ color: "#cc3300", transition: "color 0.3s ease" }} />
                                              </IconButton>
                                            </Tooltip>
                                          )}
                                        </>
                                      </div>
                                    );
                                  } else if (Object?.entries(taskRowDetails).length === 0 && user?.schedulerStatus === "Scheduler - Paused") {
                                    return (
                                      <div style={{ width: "100%", display: "flex", justifyContent: "center" }}>
                                        <Tooltip
                                          title={iconClicked?.[user?.id] ? "Pause" : "Resume"}
                                          arrow
                                          placement="top"
                                          PopperProps={{ sx: { zIndex: 1500 } }}
                                        >
                                          <IconButton
                                            aria-label="Toggle Delete Icon"
                                            onClick={() => handleIconClick(user?.id)}
                                          >
                                            {iconClicked?.[user?.id] ? (
                                              <PauseCircleOutlineIcon sx={{ transition: "color 0.3s ease" }} />
                                            ) : (
                                              <PlayCircleOutlineIcon sx={{ transition: "color 0.3s ease" }} />
                                            )}
                                          </IconButton>
                                        </Tooltip>
                                        {props?.selectionType !== "AttachmentScheduler" && (
                                          <Tooltip title="Adhoc Syndication" arrow placement="top" PopperProps={{ sx: { zIndex: 1500 } }}>
                                            <IconButton
                                              aria-label="View Metadata"
                                              onClick={() => {
                                                handleAdhocDialogOpen();
                                                setSelectedAdhocData(user);
                                              }}
                                            >
                                              <RocketLaunchIcon sx={{ color: "#cc3300", transition: "color 0.3s ease" }} />
                                            </IconButton>
                                          </Tooltip>
                                        )}
                                      </div>
                                    );
                                  } else if (Object?.entries(taskRowDetails).length === 0 && user?.schedulerStatus !== "Scheduler - Canceled" && user?.schedulerStatus !== "Scheduler - Completed") {
                                    return (
                                      <div style={{ width: "100%", display: "flex", justifyContent: "center" }}>
                                        <Tooltip
                                          title={iconClicked?.[user?.id] ? "Canceled" : "Not Canceled"}
                                          arrow
                                          placement="top"
                                          PopperProps={{ sx: { zIndex: 1500 } }}
                                        >
                                          <IconButton
                                            aria-label="Toggle Delete Icon"
                                            onClick={() => handleIconClick(user?.id)}
                                          >
                                            {props?.selectionType === "SAPScheduler" || props?.selectionType === "AttachmentScheduler" ? (
                                              iconClicked?.[user?.id] ? (
                                                <DeleteForeverOutlinedIcon sx={{ color: "#cc3300", transition: "color 0.3s ease" }} />
                                              ) : (
                                                <AutoDeleteOutlinedIcon sx={{ color: "#2cbc34", transition: "color 0.3s ease" }} />
                                              )
                                            ) : null}
                                          </IconButton>
                                        </Tooltip>
                                        {props?.selectionType === "AdhocScheduler" && user?.schedulerStatus !== "Adhoc Syndication - In Progress" && user?.schedulerStatus !== "Adhoc Syndication - Completed" && user?.schedulerStatus !== "Adhoc Syndication - Canceled" && (
                                          <>
                                            <Tooltip title="Delete Adhoc Syndication Request">
                                              <IconButton
                                                aria-label="View Metadata"
                                                onClick={() => handleDeleteAdhocSync(user)}
                                              >
                                                <AutoDeleteOutlinedIcon sx={{ color: "#2cbc34", transition: "color 0.3s ease" }} />
                                              </IconButton>
                                            </Tooltip>
                                            <Tooltip title="Adhoc Syndication" arrow placement="top" PopperProps={{ sx: { zIndex: 1500 } }}>
                                              <IconButton
                                                aria-label="View Metadata"
                                                onClick={() => {
                                                  handleAdhocDialogOpen();
                                                  setSelectedAdhocData(user);
                                                }}
                                              >
                                                <RocketLaunchIcon sx={{ color: "#cc3300", transition: "color 0.3s ease" }} />
                                              </IconButton>
                                            </Tooltip>
                                          </>
                                        )}
                                        {props?.selectionType === "SAPScheduler" && user?.schedulerStatus !== "Adhoc Syndication - In Progress" && user?.schedulerStatus !== "Adhoc Syndication - Completed" && user?.schedulerStatus !== "Adhoc Syndication - Canceled" && (
                                          <Tooltip title="Adhoc Syndication" arrow placement="top" PopperProps={{ sx: { zIndex: 1500 } }}>
                                            <IconButton
                                              aria-label="View Metadata"
                                              onClick={() => {
                                                handleAdhocDialogOpen();
                                                setSelectedAdhocData(user);
                                              }}
                                            >
                                              <RocketLaunchIcon sx={{ color: "#cc3300", transition: "color 0.3s ease" }} />
                                            </IconButton>
                                          </Tooltip>
                                        )}
                                      </div>
                                    );
                                  } else {
                                    return null;
                                  }
                                })()
                              ) : props?.selectionType === "AttachmentScheduler" && user?.schedulerStatus !== "Scheduler - Completed" && user?.schedulerStatus !== "Scheduler - Canceled" ? (
                                <Tooltip
                                  title={iconClicked?.[user?.id] ? "Canceled" : "Not Canceled"}
                                >
                                  <IconButton
                                    aria-label="Toggle Delete Icon"
                                    onClick={() => handleIconClick(user?.id)}
                                  >
                                    {iconClicked?.[user?.id] ? (
                                      <DeleteForeverOutlinedIcon
                                        sx={{ color: "#cc3300", transition: "color 0.3s ease" }}
                                      />
                                    ) : (
                                      <AutoDeleteOutlinedIcon
                                        sx={{ color: "#2cbc34", transition: "color 0.3s ease" }}
                                      />
                                    )}
                                  </IconButton>
                                </Tooltip>
                              ) : null}
                            </td>
                          </tr>
                        );
                      }}
                    </Draggable>
                  )) :(
                    <tr>
                      <td colSpan={columns.length} style={{ textAlign: 'center', padding: '16px', color: '#616161' }}>
                        No Data
                      </td>
                    </tr>
                  )}
                  {provider.placeholder}
                </tbody>
              )}
            </Droppable>
            )}) : (
              <tbody><tr>
                    {!isLoading && <td colSpan={columns.length} style={{ textAlign: 'center', padding: '16px', color: '#616161' }}>
                      No Data
                    </td>}
                  </tr> </tbody>
                  
                )}
      </table>
    </div>
  </DragDropContext>
  <ReusableBackDrop
    blurLoading={blurLoading}
    loaderMessage={loaderMessage}
  />
      <ReusableDialog
    dialogState={adhocConfirmDialog}
    openReusableDialog={handleAdhocDialogOpen}
    closeReusableDialog={handleAdhocDialogClose}
    dialogTitle={messageDialogTitle}
    dialogMessage={messageDialogMessage}
    dialogOkText={"OK"}
    handleOk={handleOk}
    dialogSeverity={messageDialogSeverity}
    showCancelButton={false}
  />
    </StyledDataGridContainer>
);
};

export default CustomDataGrid;
