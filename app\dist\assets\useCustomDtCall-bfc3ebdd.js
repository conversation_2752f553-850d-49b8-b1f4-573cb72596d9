import{q as S,r as n,a0 as p,K as c,bn as a,$ as l}from"./index-75c1660a.js";const f=()=>{const i=S(s=>s.applicationConfig),[u,D]=n.useState(null),[C,e]=n.useState(null);return{getDtCall:async(s,E="")=>{try{const o=t=>{(t==null?void 0:t.statusCode)===p.STATUS_200&&D({data:t==null?void 0:t.data,customParam:E})},r=t=>{e(t)};i.environment==="localhost"?await c(`/${a}${l.INVOKE_RULES.LOCAL}`,"post",o,r,s):await c(`/${a}${l.INVOKE_RULES.PROD}`,"post",o,r,s)}catch(o){e(o)}},dtData:u,error:C}};export{f as u};
