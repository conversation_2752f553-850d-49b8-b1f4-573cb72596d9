import {
  BottomNavi<PERSON>,
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  Paper,
  Stack,
  Step,
  StepLabel,
  Stepper,
  Tab,
  Tabs,
  TextField,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import CloseIcon from "@mui/icons-material/Close";
import {
  iconButton_SpacingSmall,
  outermostContainer_Information,
  button_Primary,
  button_Outlined,
} from "../../common/commonStyles";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import { useLocation, useNavigate } from "react-router-dom";
import ControlDataTab from "./GeneralLedgerTabs/ControlDataTab";
import { useDispatch, useSelector } from "react-redux";
import TypeDescriptionTab from "./GeneralLedgerTabs/TypeDescriptionTab";
import CreateBankInterestTab from "./GeneralLedgerTabs/CreateBankInterestTab";
import KeywordTranslationTab from "./GeneralLedgerTabs/KeywordTranslationTab";
import InformationTab from "./GeneralLedgerTabs/InformationTab";
import ReusableAttachementAndComments from "../../Common/ReusableAttachmentAndComments/ReusableAttachementAndComments";
import {
  destination_DocumentManagement,
  destination_GeneralLedger,
} from "../../../destinationVariables";
import { doAjax } from "../../Common/fetchService";
import ReusableDialog from "../../Common/ReusableDialog";
import ReusableSnackBar from "../../Common/ReusableSnackBar";
import { formValidator, idGenerator } from "../../../functions";
import { setGLErrorFields } from "../../../app/generalLedgerTabSlice";
import LoadingComponent from "../../Common/LoadingComponent";

const NewSingleGeneralLedger = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [dropDownData, setDropDownData] = useState({});
  const [messageDialogTitle, setMessageDialogTitle] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [messageDialogSeverity, setMessageDialogSeverity] = useState(false);
  const [messageDialogOK, setMessageDialogOK] = useState(true);
  const [successMsg, setsuccessMsg] = useState(false);
  const [messageDialogExtra, setMessageDialogExtra] = useState(false);
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [formValidationErrorItems, setFormValidationErrorItems] = useState([]);
  const [openSnackbarValidation, setOpenSnackbarValidation] = useState(false);
  const [validateFlag, setValidateFlag] = useState(false);
  const [submitForReviewDisabled, setSubmitForReviewDisabled] = useState(true);
  const [handleExtrabutton, setHandleExtrabutton] = useState(false);
  const [handleExtraText, setHandleExtraText] = useState("");
  const [glNumber, setGlNumber] = useState("");
  const [remarks, setRemarks] = useState("");
  const [openSubmitRemarksDialog, setOpenSubmitRemarksDialog] = useState(false);
  const [testrunStatus, setTestrunStatus] = useState(true);
  const [blurLoading, setBlurLoading] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const displayData = location.state;
  console.log("displayData", displayData);
  const controlDataTabDetails = useSelector(
    (state) => state.generalLedger.generalLedgerControlData
  );
  const typeDescriptionTabDetails = useSelector(
    (state) => state.generalLedger.generalLedgerTypeDescription
  );
  const createBankInterestTabDetails = useSelector(
    (state) => state.generalLedger.generalLedgerCreateBankInterest
  );
  const keywordTranslationTabDetails = useSelector(
    (state) => state.generalLedger.generalLedgerKeywordTranslation
  );
  const informationTabDetails = useSelector(
    (state) => state.generalLedger.generalLedgerInformation
  );
  const singleGLPayload = useSelector(
    (state) => state.generalLedger.singleGLPayload
  );
  let userData = useSelector((state) => state.userManagement.userData);
  console.log("glpaylaod", singleGLPayload);
  const payloadFields = useSelector(
    (state) => state.generalLedger.singleGLPayload
  );
  const requiredFields = useSelector(
    (state) => state.generalLedger.requiredFields
  );
  let steps = [
    "TYPE/ DESCRIPTION",
    "CONTROL DATA",
    "CREATE/ BANK/ INTEREST",
    "KEYWORD/ TRANSLATION",
    "INFORMATION",
    "ATTACHMENTS & COMMENTS",
  ];
  const getStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <TypeDescriptionTab
            typeDescriptionTabDetails={typeDescriptionTabDetails}
            dropDownData={dropDownData}
          />
        );
      case 1:
        return (
          <ControlDataTab
            controlDataTabDetails={controlDataTabDetails}
            dropDownData={dropDownData}
          />
        );
      case 2:
        return (
          <CreateBankInterestTab
            createBankInterestTabDetails={createBankInterestTabDetails}
            dropDownData={dropDownData}
          />
        );
      case 3:
        return (
          <KeywordTranslationTab
            keywordTranslationTabDetails={keywordTranslationTabDetails}
            dropDownData={dropDownData}
          />
        );
      case 4:
        return (
          <InformationTab
            informationTabDetails={informationTabDetails}
            dropDownData={dropDownData}
          />
        );
      case 5:
        return (
          <ReusableAttachementAndComments
            title="GeneralLedger"
            useMetaData={false}
            artifactId={glNumber}
            artifactName="GeneralLedger"
          />
        );
      default:
        return "Unknown step";
    }
  };

  useEffect(() => {
    dispatch(setGLErrorFields(formValidationErrorItems));
  }, [formValidationErrorItems]);
  const handleCheckValidationError = () => {
    return formValidator(
      payloadFields,
      requiredFields,
      setFormValidationErrorItems
    );
  };
  useEffect(() => {
    setGlNumber(idGenerator("GL"));
  }, []);
  const handleSnackBarOpenValidation = () => {
    setOpenSnackbarValidation(true);
  };
  const handleNext = () => {
    const isValidation = handleCheckValidationError();
    if (isValidation) {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
    } else {
      handleSnackBarOpenValidation();
    }
  };
  const handleBack = () => {
    setSubmitForReviewDisabled(true)
    const isValidation = handleCheckValidationError();
    if (isValidation) {
      setActiveStep((prevActiveStep) => prevActiveStep - 1);
    } else {
      handleSnackBarOpenValidation();
    }
  };
  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };
  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };
  const handleWarningDialogClose = () => {
    setOpenMessageDialog(false);
  };
  const handleRemarksDialogClose = () => {
    setTestrunStatus(true);
    setOpenSubmitRemarksDialog(false);
  };
  const handleSnackBarCloseValidation = () => {
    setOpenSnackbarValidation(false);
  };
  const handleSnackBarClose = () => {
    if (validateFlag) {
      setopenSnackbar(false);
      setValidateFlag(false);
    } else {
      setopenSnackbar(false);
      navigate("/masterDataCockpit/generalLedger");
    }
  };
  var payload = {
    GeneralLedgerID: "",
    Action: "I",
    RequestID: "",
    TaskStatus: "",
    TaskId: "",
    Remarks:  remarks ? remarks : "",
    Info: "",
    CreationId: "",
    EditId: "",
    DeleteId: "",
    MassCreationId: "",
    MassEditId: "",
    MassDeleteId: "",
    RequestType: "Create",
    ReqCreatedBy: userData?.user_id,
    ReqCreatedOn: "",
    ReqUpdatedOn: "",
    RequestStatus: "",
    Testrun: testrunStatus,
    COA: displayData?.chartOfAccounts?.newChartOfAccount?.code,
    CompanyCode: displayData?.companyCode?.newCompanyCode?.code,
    CoCodeToExtend: "",
    GLAccount: displayData?.newGLAccount
      ? displayData?.newGLAccount
      : " ",
    Accounttype: singleGLPayload?.AccountType
      ? singleGLPayload?.AccountType?.code
      : "",
    AccountGroup: singleGLPayload?.AccountGroup
      ? singleGLPayload?.AccountGroup?.code
      : "",
    GLname: singleGLPayload?.ShortText ? singleGLPayload?.ShortText : "",
    Description: singleGLPayload?.LongText ? singleGLPayload?.LongText : "",
    TradingPartner: singleGLPayload?.TradingPartner
      ? singleGLPayload?.TradingPartner?.code
      : "",
    GroupAccNo: singleGLPayload?.GroupAccountNumber
      ? singleGLPayload?.GroupAccountNumber?.code
      : "121100",
    AccountCurrency: singleGLPayload?.AccountCurrency
      ? singleGLPayload?.AccountCurrency?.code
      : "",
    Exchangerate: singleGLPayload?.ExchangeRateDifferenceKey
      ? singleGLPayload?.ExchangeRateDifferenceKey?.code
      : "",
    Balanceinlocrcy:
      singleGLPayload?.OnlyBalanceInLocalCurrency === true ? "X" : "",
    Taxcategory: singleGLPayload?.TaxCategory
      ? singleGLPayload?.TaxCategory?.code
      : "",
    Pstnwotax: singleGLPayload?.PostingWithoutTaxAllowed === true ? "X" : "",
    ReconAcc: singleGLPayload?.ReconAccountForAccountType
      ? singleGLPayload?.ReconAccountForAccountType?.code
      : "",
    Valuationgrp: singleGLPayload?.ValuationGroup
      ? singleGLPayload?.ValuationGroup
      : "",
    AlterAccno: singleGLPayload?.AlternativeAccountNumber
      ? singleGLPayload?.AlternativeAccountNumber?.code
      : "",
    Openitmmanage: singleGLPayload?.OpenItemManagement === true ? "X" : "",
    Sortkey: singleGLPayload?.SortKey ? singleGLPayload?.SortKey?.code : "",
    CostEleCategory: singleGLPayload?.CostElementCategory
      ? singleGLPayload?.CostElementCategory?.code
      : "",
    FieldStsGrp: singleGLPayload?.FieldStatusGroup
      ? singleGLPayload?.FieldStatusGroup?.code
      : "",
    PostAuto: singleGLPayload?.PostAutomaticallyOnly === true ? "X" : "",
    Supplementautopost:
      singleGLPayload?.SupplementAutoPostings === true ? "X" : "",
    Planninglevel: singleGLPayload?.PlanningLevel
      ? singleGLPayload?.PlanningLevel?.code
      : "",
    Relvnttocashflow: singleGLPayload?.RelevantToCashFlows === true ? "X" : "",
    HouseBank: singleGLPayload?.HouseBank
      ? singleGLPayload?.HouseBank?.code
      : "",
    AccountId: singleGLPayload?.AccountID
      ? singleGLPayload?.AccountID?.code
      : "",
    Interestindicator: singleGLPayload?.InterestIndicator
      ? singleGLPayload?.InterestIndicator?.code
      : "",
    ICfrequency: singleGLPayload?.InterestCalculationFrequency
      ? singleGLPayload?.InterestCalculationFrequency?.code
      : "",
    KeydateofLIC: singleGLPayload?.KeyDateOfLastInterestCalculation
      ? "/Date(" + singleGLPayload?.KeyDateOfLastInterestCalculation + ")/"
      : "",
    LastIntrstundate: singleGLPayload?.DateOfLastInterestRun
      ? "/Date(" + singleGLPayload?.DateOfLastInterestRun + ")/"
      : "",
    AccmngExistsys: "",
    Infationkey: "",
    Tolerancegrp: "",
    AuthGroup: "",
    AccountClerk: "",
    ReconAccReady: "",
    PostingBlocked: "",
    PlanningBlocked: "",
  };
  const handleSnackBarOpen = () => {
    setopenSnackbar(true);
  };
  const onValidateGeneralLedger = () => {
    setBlurLoading(true);
    // const duplicateCheck = `${singleGLPayload?.ShortText}$$${displayData?.companyCode?.newCompanyCode?.code}`;
    var duplicateCheck = {
      glName: singleGLPayload?.ShortText?.toUpperCase(),
      compCode:displayData?.companyCode?.newCompanyCode?.code,
    };
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 201) {
        setMessageDialogTitle("Create");
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `All Data has been Validated. General Ledger can be Send for Review`
        );
        setSubmitForReviewDisabled(false);
        setHandleExtrabutton(false);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setValidateFlag(true);
        setBlurLoading(false);
      } else {
        setBlurLoading(false);
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          `${
            data?.body?.message[0] ? data?.body?.message[0] : data?.body?.value
          }`
        );
        setHandleExtrabutton(false);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setSubmitForReviewDisabled(true);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    const hDuplicateCheckSuccess = (data) => {
      // Handle success of duplicate check
      if (
        data.body.length === 0 ||
        !data.body.some((item) => item.toUpperCase() === duplicateCheck)
      ) {
        // No direct match, enable the "Submit for Review" button
        setBlurLoading(false);
        setSubmitForReviewDisabled(false);

        doAjax(
          `/${destination_GeneralLedger}/alter/validateSingleGeneralLedger`,
          "post",
          hSuccess,
          hError,
          payload
        );
      } else {
        // Handle direct match
        setBlurLoading(false);
        setMessageDialogTitle("Duplicate Check");
        setsuccessMsg(false);
        setMessageDialogMessage(
          `There is a direct match for the Cost Center name. Please change the name.`
        );
        setHandleExtrabutton(false);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setSubmitForReviewDisabled(true);
      }
    };

    const hDuplicateCheckError = (error) => {
      // Handle error of duplicate check
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger}/alter/fetchGlNameNCompCodeDupliChk`,
      "post",
      hDuplicateCheckSuccess,
      hDuplicateCheckError,
      duplicateCheck
    );
  };
  const onSaveAsDraftButtonClick = () => {
    setMessageDialogSeverity(false);
    handleMessageDialogClickOpen();
    setMessageDialogTitle("Confirm");
    setMessageDialogMessage(`Do You Want to Save as Draft ?`);
    setHandleExtrabutton(true);
    setHandleExtraText("proceed");
  };
  const handleProceedbutton = () => {
    setIsLoading(true);
    const hSuccess = (data) => {
      handleMessageDialogClose();
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `General Ledger has been Saved with ID NLS${data.body}`
        );
        setHandleExtrabutton(false);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        const secondApiPayload = {
          artifactId: glNumber,
          createdBy: userData?.emailId,
          artifactType: "GeneralLedger",
          requestId: `NLS${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
      } else {
        setMessageDialogTitle("Save");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Saving the Data");
        setHandleExtrabutton(false);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        //setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/alter/generalLedgerAsDraft`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const onSubmitForReviewButtonClick = () => {
    setIsLoading(true);
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `General Ledger has been Submitted for review NLS${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
        const secondApiPayload = {
          artifactId: glNumber,
          createdBy: userData?.emailId,
          artifactType: "GeneralLedger",
          requestId: `NLS${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
      } else {
        setMessageDialogTitle("Create");
        setsuccessMsg(false);
        setMessageDialogMessage("Creation Failed");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/alter/generalLedgerSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleRemarks = (e, value) => {
    //setRemarks(e.target.value);
    const newValue = e.target.value;
    if (newValue.length > 0 && newValue[0] === " ") {
      setRemarks(newValue.trimStart());
    } else {
      //let costCenterValue = e.target.value;
      let remarksUpperCase = newValue.toUpperCase();
      setRemarks(remarksUpperCase);
    }
  };
  const handleSubmitRemarksDialogOpen = () => {
    setTestrunStatus(false);
    setOpenSubmitRemarksDialog(true);
  };
  const onGeneralLedgerSubmitRemarks = () => {
    onSubmitForReviewButtonClick();
    handleRemarksDialogClose();
  };
  console.log("testrun", testrunStatus);
  return (
    <>
      {isLoading === true ? (
        <LoadingComponent />
      ) : (
        <div>
          <Dialog
            hideBackdrop={false}
            elevation={2}
            PaperProps={{
              sx: { boxShadow: "none" },
            }}
            open={openSubmitRemarksDialog}
            onClose={handleRemarksDialogClose}
          >
            <DialogTitle
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF40",
                // borderBottom: "1px solid grey",
                display: "flex",
              }}
            >
              <Typography variant="h6">Remarks</Typography>

              <IconButton
                sx={{ width: "max-content" }}
                onClick={handleRemarksDialogClose}
                children={<CloseIcon />}
              />
            </DialogTitle>

            <DialogContent sx={{ padding: ".5rem 1rem" }}>
              <Stack>
                <Box sx={{ minWidth: 400 }}>
                  <FormControl sx={{ height: "auto" }} fullWidth>
                    <TextField
                      sx={{ backgroundColor: "#F5F5F5" }}
                      value={remarks.toUpperCase()}
                      onChange={handleRemarks}
                      multiline
                      placeholder={"Enter Remarks"}
                      inputProps={{maxLength: 254}}
                    ></TextField>
                  </FormControl>
                </Box>
              </Stack>
            </DialogContent>
            <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
              <Button
                sx={{ width: "max-content", textTransform: "capitalize" }}
                onClick={handleRemarksDialogClose}
              >
                Cancel
              </Button>
              <Button
                className="button_primary--normal"
                type="save"
                onClick={onGeneralLedgerSubmitRemarks}
                variant="contained"
              >
                Submit
              </Button>
            </DialogActions>
          </Dialog>

          <ReusableDialog
            dialogState={openMessageDialog}
            openReusableDialog={handleMessageDialogClickOpen}
            closeReusableDialog={handleMessageDialogClose}
            dialogTitle={messageDialogTitle}
            dialogMessage={messageDialogMessage}
            handleDialogConfirm={handleMessageDialogClose}
            dialogOkText={"OK"}
            showExtraButton={handleExtrabutton}
            showCancelButton={true}
            // handleExtraButton={handleMessageDialogNavigate}
            dialogSeverity={messageDialogSeverity}
            handleDialogReject={handleWarningDialogClose}
            handleExtraText={handleExtraText}
            handleExtraButton={handleProceedbutton}
          />

          {formValidationErrorItems.length != 0 && (
            <ReusableSnackBar
              openSnackBar={openSnackbarValidation}
              alertMsg={
                "Please fill the following Field: " +
                formValidationErrorItems.join(", ")
              }
              handleSnackBarClose={handleSnackBarCloseValidation}
            />
          )}
          {successMsg && (
            <ReusableSnackBar
              openSnackBar={openSnackbar}
              alertMsg={messageDialogMessage}
              handleSnackBarClose={handleSnackBarClose}
            />
          )}

          <Grid
            container
            style={{
              ...outermostContainer_Information,
              backgroundColor: "#FAFCFF",
            }}
          >
            <Grid sx={{ width: "inherit" }}>
              <Grid item md={7} style={{ padding: "16px", display: "flex" }}>
                <Grid item md={5} sx={{ display: "flex" }}>
                  <Grid>
                    <IconButton
                      // onClick={handleBacktoRO}
                      color="primary"
                      aria-label="upload picture"
                      component="label"
                      sx={iconButton_SpacingSmall}
                    >
                      <ArrowCircleLeftOutlinedIcon
                        style={{
                          height: "1em",
                          width: "1em",
                          color: "#000000",
                        }}
                        // sx={{
                        //   fontSize: "1.5em",
                        //   color: "#000000",
                        // }}
                        onClick={() => {
                          navigate("/masterDataCockpit/generalLedger");
                        }}
                      />
                    </IconButton>
                  </Grid>
                  <Grid>
                    <Typography variant="h3">
                      <strong>Create General Ledger</strong>
                    </Typography>
                    <Typography variant="body2" color="#777">
                      This view creates a new General Ledger
                    </Typography>
                  </Grid>
                </Grid>
              </Grid>
              <Grid container style={{ padding: "0 1rem 0 1rem" }}>
                <Grid container sx={outermostContainer_Information}>
                  <Grid
                    container
                    display="flex"
                    flexDirection="row"
                    flexWrap="nowrap"
                  >
                    <Box width="70%" sx={{ marginLeft: "40px" }}>
                      <Grid item sx={{ paddingTop: "2px !important" }}>
                        <Stack flexDirection="row">
                          <div style={{ width: "15%" }}>
                            <Typography variant="body2" color="#777">
                              Chart Of Account
                            </Typography>
                          </div>
                          <Typography
                            variant="body2"
                            fontWeight="bold"
                            justifyContent="flex-start"
                          >
                            :{" "}
                            {
                              displayData?.chartOfAccounts?.newChartOfAccount
                                ?.code
                            }
                          </Typography>
                        </Stack>
                      </Grid>

                      <Grid item sx={{ paddingTop: "2px !important" }}>
                        <Stack flexDirection="row">
                          <div style={{ width: "15%" }}>
                            <Typography variant="body2" color="#777">
                              Company Code
                            </Typography>
                          </div>
                          <Typography variant="body2" fontWeight="bold">
                            : {displayData?.companyCode?.newCompanyCode?.code}
                          </Typography>
                        </Stack>
                      </Grid>
                      {/* <Grid item sx={{ paddingTop: "2px !important" }}>
                        <Stack flexDirection="row">
                          <div style={{ width: "15%" }}>
                            <Typography variant="body2" color="#777">
                              Account Type
                            </Typography>
                          </div>
                          <Typography variant="body2" fontWeight="bold">
                            : {displayData?.accountType?.newAccountType?.code}
                          </Typography>
                        </Stack>
                      </Grid>
                      <Grid item sx={{ paddingTop: "2px !important" }}>
                        <Stack flexDirection="row">
                          <div style={{ width: "15%" }}>
                            <Typography variant="body2" color="#777">
                              Account Group
                            </Typography>
                          </div>
                          <Typography variant="body2" fontWeight="bold">
                            :{" "}
                            {
                              displayData?.accountGroup?.newAccountGroup
                                ?.AccountGroup
                            }
                          </Typography>
                        </Stack>
                      </Grid> */}
                      <Grid item sx={{ paddingTop: "2px !important" }}>
                        <Stack flexDirection="row">
                          <div style={{ width: "15%" }}>
                            <Typography variant="body2" color="#777">
                              G/L Account
                            </Typography>
                          </div>
                          <Typography
                            variant="body2"
                            fontWeight="bold"
                            justifyContent="flex-start"
                          >
                            : {displayData?.newGLAccount}
                          </Typography>
                        </Stack>
                      </Grid>
                    </Box>
                    <Box width="30%" sx={{ marginLeft: "40px" }}>
                      <Grid item>
                        <Stack flexDirection="row">
                          <Typography
                            variant="body2"
                            color="#777"
                            style={{ width: "30%" }}
                          >
                            {/* {item.info ? item.desc : ""} */}
                          </Typography>

                          <Typography
                            variant="body2"
                            fontWeight="bold"
                            sx={{ width: "8%", textAlign: "center" }}
                          >
                            {/* {item.info ? ":" : ""} */}
                          </Typography>

                          <Typography
                            variant="body2"
                            fontWeight="bold"
                            justifyContent="flex-start"
                          >
                            {/* {item?.info?.code}
                            {item?.info?.code && item?.info?.desc
                              ? " - "
                              : null} */}
                            {/* {item?.info?.desc} */}
                          </Typography>
                        </Stack>
                      </Grid>
                    </Box>
                  </Grid>

                  <Grid container>
                    <Stepper
                      activeStep={activeStep}
                      sx={{
                        background: "#FFFFFF",
                        borderBottom: "1px solid #BDBDBD",
                        width: "100%",
                        height: "48px",
                      }}
                    >
                      {steps.map((label, index) => (
                        <Step key={label}>
                          <StepLabel sx={{ fontWeight: "700" }}>
                            {label}
                          </StepLabel>
                        </Step>
                      ))}
                    </Stepper>
                  </Grid>

                  <Grid container>{getStepContent(activeStep)}</Grid>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
          <Paper
            sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
            elevation={2}
          >
            <BottomNavigation
              className="container_BottomNav"
              showLabels
              sx={{ display: "flex", justifyContent: "flex-end" }}
            >
              <Button
                variant="contained"
                size="small"
                sx={{ ...button_Primary, mr: 1 }}
                onClick={onSaveAsDraftButtonClick}
                // disabled={activeStep === 0}
              >
                Save As Draft
              </Button>
              <Button
                variant="contained"
                size="small"
                sx={{ ...button_Primary, mr: 1 }}
                onClick={handleBack}
                disabled={activeStep === 0}
              >
                Back
              </Button>
              {activeStep === steps.length - 1 ? (
                <>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={onValidateGeneralLedger}
                    // disabled={activeStep === 0}
                  >
                    Validate
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleSubmitRemarksDialogOpen}
                    disabled={submitForReviewDisabled}
                  >
                    Submit For Review
                  </Button>
                </>
              ) : (
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary, mr: 1 }}
                  onClick={handleNext}
                >
                  Next
                </Button>
              )}
            </BottomNavigation>
          </Paper>
        </div>
      )}
    </>
  );
};

export default NewSingleGeneralLedger;
