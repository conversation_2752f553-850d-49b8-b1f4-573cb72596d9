import{b as ye,r as s,s as Ce,j as y,a as e,b3 as xe,z as ke,am as Fe,G as E,g as Y,A as Z,T as O,f as J,an as De,aE as ve,t as Me,aD as fe,K as Q,eV as X,ck as Ee,F as me,aj as Ae,ak as Le,al as qe,bY as Ie,bZ as We,h as be,I as pe,b1 as Se,b_ as He,cl as Ve,v as we,w as _e,B as ze,cm as Ge}from"./index-75c1660a.js";import{R as Re}from"./ReusableFieldCatalog-2b30c987.js";const Ke=({})=>{const u=ye();s.useState("");const[C,d]=s.useState(null),[F,D]=s.useState([]),[f,R]=s.useState({}),[Be,ee]=s.useState(null),[N,T]=s.useState({}),[se,te]=s.useState({}),[x,j]=s.useState({}),[ae,A]=s.useState(!1),[ie,oe]=s.useState(!1),[le,L]=s.useState(!1),[q,I]=s.useState(""),[ne,W]=s.useState(!1),[Oe,H]=s.useState(!1),[Ne,V]=s.useState(!0),[ce,w]=s.useState(!1),[Te,B]=s.useState(!1),G=Ce(),K=()=>{u("/masterDataCockpit/materialMaster/createMaterialDetail")},de=()=>{u("/masterDataCockpit/materialMaster/createMaterialDetail")},re=()=>{setOpen(!1)},$=()=>{A(!1),u("/masterDataCockpit/generalLedger")},ge=()=>{A(!0)},P=()=>{oe(!0)},he=()=>{const r=b=>{const n=[],v=[];Object.keys(b.body).map(o=>{const t=b.body[o];Object.keys(t).map(p=>{const c=b.body[o][p];if(Array.isArray(c)){let M={heading:p,fields:c.map(l=>l.fieldName),viewName:o,fieldVisibility:c.map(l=>({fieldName:l.fieldName,visibility:l.visibility}))};n.push(M),console.log(n,"hello"),c.forEach(l=>{console.log("Field Name:",l.fieldName),console.log("Is Required:",l.Required),l.Required==="true"&&v.push(l.fieldName)})}})}),D(n),console.log("Required Fields:",v);const k={},a={},i={};n.forEach(o=>{const{heading:t,fields:p,viewName:c,fieldVisibility:M}=o;k[c]||(k[c]={heading:c,subheadings:[]}),k[c].subheadings.push({heading:t,fields:p}),M.forEach(l=>{let h=l.visibility==="Required"?"Mandatory":l.visibility==="Hidden"?"Hide":l.visibility==="0"?"0":"Optional";a[l.fieldName]=h,l.visibility==="0"&&(i[l.fieldName]=!0)})}),R(k),j(a),te(i),T(i),console.log(k,"Fieldset")},m=b=>{console.log(b)};Q(`/${X}/data/getFieldCatalogueDetails?screenName=Change`,"get",r,m)};s.useEffect(()=>{he()},[]);const U=()=>{console.log("helloooo");let r={};Object.keys(f).forEach(n=>{f[n].subheadings.forEach(k=>{const{heading:a,fields:i}=k;i.forEach(o=>{if(x[o]!=="0"&&N[o]){const t=x[o]==="Mandatory"?"Required":x[o]==="Hide"?"Hidden":"Optional";r[n]||(r[n]=[]),r[n].some(c=>c.fieldName===o)||r[n].push({fieldName:o,cardName:a,viewName:n,visibility:t,screenName:"Change"})}})})});const m=n=>{console.log(n,"example"),B(),n.statusCode===200?(console.log("success"),L("Submit"),I("Field Catalog has been submitted successfully"),W("success"),V(!1),w(!0),ge(),H(!0),B(!1)):(L("Submit"),w(!1),I("Submission Failed"),W("danger"),V(!1),H(!0),P(),B(!1)),re()},b=n=>{console.log(n)};Object.keys(r).forEach(n=>{const v=r[n];v.length>0?Q(`/${X}/alter/changeVisibility`,"post",m,b,v):console.log(`No payload data to send for viewName: ${n}`)}),G(Ee())};return y("div",{children:[e(xe,{dialogState:ie,openReusableDialog:P,closeReusableDialog:K,dialogTitle:le,dialogMessage:q,handleDialogConfirm:K,dialogOkText:"OK",handleExtraButton:de,dialogSeverity:ne}),ce&&e(ke,{openSnackBar:ae,alertMsg:q,handleSnackBarClose:$}),e(E,{container:!0,sx:Fe,children:e(E,{item:!0,md:12,children:Object.keys(f).map(r=>y(Y,{sx:{mb:2},className:"filter-accordion",children:[e(Z,{sx:{backgroundColor:"#f5f5f5"},children:e(O,{sx:{fontWeight:"700",margin:"0px !important"},children:r})}),e(J,{children:f[r].subheadings.map((m,b)=>y(Y,{sx:{mb:2},children:[e(Z,{expandIcon:e(De,{}),sx:{backgroundColor:"#F1F0FF"},children:e(O,{sx:{fontWeight:"700",margin:"0px !important",fontSize:"14px"},children:m.heading})}),e(J,{children:e("div",{sx:{fontSize:"25px"},children:e(Re,{fields:m.fields,heading:m.heading,childCheckedStates:N,setChildCheckedStates:T,childRadioValues:x,setChildRadioValues:j,onSubmitButtonClick:()=>U(),mandatoryFields:F,DisabledChildCheck:se})})})]},b))})]},r))})}),e(fe,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:e(ve,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},value:C,onChange:(r,m)=>{ee(F[m]),d(m)},children:e(Me,{size:"small",variant:"contained",onClick:U,children:"Submit"})})})]})},$e=()=>{const u=document.getElementsByTagName("HTML")[0],C=document.getElementsByTagName("BODY")[0];let d=u.clientWidth,F=C.clientWidth;const D=document.getElementById("e-invoice-export"),f=D.scrollWidth-D.clientWidth;f>D.clientWidth&&(d+=f,F+=f),u.style.width=d+"px",C.style.width=F+"px",Ge(D).then(R=>R.toDataURL("image/png",1)).then(R=>{Pe(R,"FieldCatalog.png"),u.style.width=null,C.style.width=null})},Pe=(u,C)=>{const d=window.document.createElement("a");d.href=u,d.download=C,(document.body||document.documentElement).appendChild(d),typeof d.click=="function"?d.click():(d.target="_blank",d.dispatchEvent(new MouseEvent("click",{view:window,bubbles:!0,cancelable:!0}))),URL.revokeObjectURL(d.href),d.remove()},Ze=({})=>{const u=ye();s.useState("");const[C,d]=s.useState(null),[F,D]=s.useState([]),[f,R]=s.useState({}),[Be,ee]=s.useState(null),[N,T]=s.useState({}),[se,te]=s.useState({}),[x,j]=s.useState({}),[ae,A]=s.useState(!1),[ie,oe]=s.useState(!1),[le,L]=s.useState(!1),[q,I]=s.useState(""),[ne,W]=s.useState(!1),[Oe,H]=s.useState(!1),[Ne,V]=s.useState(!0),[ce,w]=s.useState(!1),[Te,B]=s.useState(!1),[G,K]=s.useState(0),de=["For Create","For Change"],re=Ce(),$=()=>{u("/masterDataCockpit/materialMaster/createMaterialDetail")},ge=()=>{u("/masterDataCockpit/materialMaster/createMaterialDetail")},P=()=>{setOpen(!1)},he=()=>{A(!1),u("/masterDataCockpit/generalLedger")},U=()=>{A(!0)},r=()=>{oe(!0)},m=()=>{const a=o=>{const t=[],p=[];Object.keys(o.body).map(h=>{const _=o.body[h];Object.keys(_).map(z=>{const S=o.body[h][z];if(Array.isArray(S)){let ue={heading:z,fields:S.map(g=>g.fieldName),viewName:h,fieldVisibility:S.map(g=>({fieldName:g.fieldName,visibility:g.visibility}))};t.push(ue),console.log(t,"hello"),S.forEach(g=>{console.log("Field Name:",g.fieldName),console.log("Is Required:",g.Required),g.Required==="true"&&p.push(g.fieldName)})}})}),D(t),console.log("Required Fields:",p);const c={},M={},l={};t.forEach(h=>{const{heading:_,fields:z,viewName:S,fieldVisibility:ue}=h;c[S]||(c[S]={heading:S,subheadings:[]}),c[S].subheadings.push({heading:_,fields:z}),ue.forEach(g=>{let je=g.visibility==="Required"?"Mandatory":g.visibility==="Hidden"?"Hide":g.visibility==="0"?"0":"Optional";M[g.fieldName]=je,g.visibility==="0"&&(l[g.fieldName]=!0)})}),R(c),j(M),te(l),T(l),console.log(c,"Fieldset")},i=o=>{console.log(o)};Q(`/${X}/data/getFieldCatalogueDetails?screenName=Create`,"get",a,i)};s.useEffect(()=>{m()},[]);const b=()=>{let a={};console.log("update"),Object.keys(f).forEach(t=>{f[t].subheadings.forEach(c=>{const{heading:M,fields:l}=c;l.forEach(h=>{if(x[h]!=="0"&&N[h]){const _=x[h]==="Mandatory"?"Required":x[h]==="Hide"?"Hidden":"Optional";a[t]||(a[t]=[]),a[t].some(S=>S.fieldName===h)||a[t].push({fieldName:h,cardName:M,viewName:t,visibility:_,screenName:"Create"})}})})});const i=t=>{console.log(t,"example"),B(),t.statusCode===200?(console.log("success"),L("Submit"),I("Field Catalog has been submitted successfully"),W("success"),V(!1),w(!0),U(),H(!0),B(!1)):(L("Submit"),w(!1),I("Submission Failed"),W("danger"),V(!1),H(!0),r(),B(!1)),P()},o=t=>{console.log(t)};Object.keys(a).forEach(t=>{const p=a[t];p.length>0?Q(`/${X}/alter/changeVisibility`,"post",i,o,p):console.log(`No payload data to send for viewName: ${t}`)}),re(Ee())},n=[[e(me,{children:y(E,{container:!0,sx:Fe,children:[e(E,{item:!0,md:12,children:Object.keys(f).map(a=>y(Y,{sx:{mb:2},className:"filter-accordion",children:[e(Z,{sx:{backgroundColor:"#f5f5f5"},children:e(O,{sx:{fontWeight:"700",margin:"0px !important"},children:a})}),e(J,{children:f[a].subheadings.map((i,o)=>y(Y,{sx:{mb:2},children:[e(Z,{expandIcon:e(De,{}),sx:{backgroundColor:"#F1F0FF"},children:e(O,{sx:{fontWeight:"700",margin:"0px !important",fontSize:"14px"},children:i.heading})}),e(J,{children:e("div",{sx:{fontSize:"25px"},children:e(Re,{fields:i.fields,heading:i.heading,childCheckedStates:N,setChildCheckedStates:T,childRadioValues:x,setChildRadioValues:j,onSubmitButtonClick:()=>b(),mandatoryFields:F,DisabledChildCheck:se})})})]},o))})]},a))}),e(fe,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:e(ve,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},value:C,onChange:(a,i)=>{ee(F[i]),d(i)},children:e(Me,{size:"small",variant:"contained",onClick:b,children:"Submit"})})})]})})],[e(me,{children:e(Ke,{})})]],v=(a,i)=>{K(i)};s.useState(""),s.useState([]);function k(){}return y("div",{children:[e(xe,{dialogState:ie,openReusableDialog:r,closeReusableDialog:$,dialogTitle:le,dialogMessage:q,handleDialogConfirm:$,dialogOkText:"OK",handleExtraButton:ge,dialogSeverity:ne}),ce&&e(ke,{openSnackBar:ae,alertMsg:q,handleSnackBarClose:he}),e("div",{style:{...Ae,backgroundColor:"#FAFCFF"},children:y(Le,{spacing:1,children:[y(E,{container:!0,sx:qe,children:[y(E,{item:!0,md:5,sx:Ie,children:[e(O,{variant:"h3",children:e("strong",{children:"Field Configurations"})}),e(O,{variant:"body2",color:"#777",children:"This view displays the setiings for configuring the Fields"})]}),e(E,{item:!0,md:7,sx:{display:"flex"},children:y(E,{container:!0,direction:"row",justifyContent:"flex-end",alignItems:"center",spacing:0,children:[e(We,{title:"Search for fields in different views",module:"FieldSelection",keyName:"string",message:"Search for fields in different views"}),e(be,{title:"Reload",children:e(pe,{sx:Se,children:e(He,{sx:{"&:hover":{transform:"rotate(360deg)",transition:"0.9s"}},onClick:k})})}),e(be,{title:"Export",children:e(pe,{sx:Se,children:e(Ve,{onClick:$e})})})]})})]}),e(fe,{children:e(we,{value:G,onChange:v,variant:"scrollable",sx:{background:"#FFF",borderBottom:"1px solid #BDBDBD",width:"100%"},"aria-label":"mui tabs example",children:de.map((a,i)=>e(_e,{sx:{fontSize:"12px",fontWeight:"700"},label:a},i))})}),n[G].map((a,i)=>e(ze,{children:a},i))]})})]})};export{Ze as default};
