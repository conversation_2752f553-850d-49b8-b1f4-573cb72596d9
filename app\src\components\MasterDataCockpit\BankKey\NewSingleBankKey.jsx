import {
  Backdrop,
  BottomNavigation,
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  Paper,
  Stack,
  Step,
  StepLabel,
  Stepper,
  Tab,
  Tabs,
  FormControl,
  TextField,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import {
  iconButton_SpacingSmall,
  outermostContainer_Information,
  button_Outlined,
  button_Primary,
} from "../../common/commonStyles";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import BasicDataCostCenter from "../CostCenterTabs/BasicDataCostCenter";
import { useLocation, useNavigate } from "react-router-dom";
import ReusableDialog from "../../Common/ReusableDialog";
import ReusableSnackBar from "../../Common/ReusableSnackBar";
import BankDataTab from "./BankKeyTabs/BankDataTab";
import { useSelector } from "react-redux";
import AddressDataTab from "./BankKeyTabs/AddressDataTab";
import { destination_BankKey, destination_DocumentManagement } from "../../../destinationVariables";
import { doAjax } from "../../Common/fetchService";
import lookup from "../../../data/lookup.json";
import ReusableAttachementAndComments from "../../Common/ReusableAttachmentAndComments/ReusableAttachementAndComments";
import { useDispatch } from "react-redux";
import { setDropDown } from "../../../app/dropDownDataSlice";
import LoadingComponent from "../../Common/LoadingComponent";
import CloseIcon from "@mui/icons-material/Close";
import { checkIwaAccess, idGenerator, formValidator } from "../../../functions";
import {
  clearBankKey,
setBKErrorFields
} from "../../../app/bankKeyTabSlice";


const NewSingleBankKey = () => {
  const location = useLocation();
  const [activeStep, setActiveStep] = useState(0);
  const [messageDialogTitle, setMessageDialogTitle] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [messageDialogSeverity, setMessageDialogSeverity] = useState(false);
  const [messageDialogOK, setMessageDialogOK] = useState(true);
  const [successMsg, setsuccessMsg] = useState(false);
  const [messageDialogExtra, setMessageDialogExtra] = useState(false);
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [blurLoading, setBlurLoading] = useState(false);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [formValidationErrorItems, setFormValidationErrorItems] = useState([]);
  const [openSnackbarValidation, setOpenSnackbarValidation] = useState(false);
  const [submitForReviewDisabled, setSubmitForReviewDisabled] = useState(true);
  const [testrunStatus, setTestrunStatus] = useState(true);
  const [openCreateDialog , setOpenCreateDialog] = useState(false);
  const [handleExtrabutton, setHandleExtrabutton] = useState(false);
  const [handleExtraText, setHandleExtraText] = useState("");
  const [bkNumber, setBKNumber] = useState("");
  const [remarks, setRemarks] = useState("");
  const [validateFlag, setValidateFlag] = useState(false);
  const [remarksValidationError,setRemarksValidationError]=useState(false)
  const navigate = useNavigate();
  const displayData = location.state;
  console.log("displayData", displayData);
  console.log("submitForReviewDisabled", submitForReviewDisabled);
  const singleBKPayload = useSelector((state) => state.bankKey.singleBKPayload);
  const dropDownData = useSelector((state) => state.AllDropDown.dropDown);
  const bankDataTabDetails = useSelector(
    (state) => state.bankKey.bankKeyBankData
  );
  
  const payloadFields = useSelector(
    (state) => state.bankKey.singleBKPayload
  );

  const requiredFields = useSelector(
    (state) => state.bankKey.requiredFields
  );

  console.log("payloadFields", payloadFields);
  console.log("requiredFields", singleBKPayload?.TimeZone?.code);
  const addressDataTabDetails = useSelector(
    (state) => state.bankKey.bankKeyAddressData
  );
  let userData = useSelector((state) => state.userManagement.userData);
  const dispatch = useDispatch();

  // Loader and lookup for independent apis start
  const [apiCount, setApiCount] = useState(0);
  const fetchDynamicApiData = (keyName, endPoint) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: keyName, data: data.body }));
      // setIsLoading(false);
      setApiCount((prev) => prev + 1);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_BankKey}/data/${endPoint}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getAllLookups = () => {
    lookup?.bankKey?.map((item) => {
      fetchDynamicApiData(item?.keyName, item?.endPoint);
    });
  };
  const loaderCount = () => {
    if (apiCount == lookup?.bankKey?.length) {
      setIsLoading(false);
    } else {
      setIsLoading(true);
    }
  };
  useEffect(() => {
    loaderCount();
  }, [apiCount]);

  // Loader and lookup for independent apis end

  useEffect(() => {
    getAllLookups();
  }, []);

  useEffect(() => {
    setBKNumber(idGenerator("BK"));
  }, []);

  let steps = ["BANK DETAILS", "ADDRESS DETAILS", "ATTACHMENTS & COMMENTS"];
  const getStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <BankDataTab
            bankDataTabDetails={bankDataTabDetails}
            dropDownData={dropDownData}
            country={displayData?.bankCtryReg?.newBankCtryReg?.code}
          />
        );
      case 1:
        return (
          <AddressDataTab
            addressDataTabDetails={addressDataTabDetails}
            dropDownData={dropDownData}
          />
        );
      case 2:
        return (
          // <HistoryCostCenter
          //   historyTabDetails={historyTabDetails}
          //   dropDownData={dropDownData}
          // />
          <ReusableAttachementAndComments
            title="BankKey"
            useMetaData={false}
            artifactId={bkNumber}
            artifactName="BankKey"
          />
        );
      default:
        return "Unknown step";
    }
  };

  const handleNext = () => {
    const isValidation = handleCheckValidationError();
    if(isValidation){
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
    }
    else{
      handleSnackBarOpenValidation();
    }
  };
  const handleBack = () => {
    const isValidation = handleCheckValidationError();
    if(isValidation){
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  }
  else{
    handleSnackBarOpenValidation();
  }
  };
  const handleSnackBarOpen = () => {
    setopenSnackbar(true);
  };
  const handleSnackBarClose = () => {
    if (validateFlag) {
      setopenSnackbar(false);
      setValidateFlag(false);
    } else {
      setopenSnackbar(false);
      navigate("/masterDataCockpit/bankKey");
    }
  };
  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };

  const handleSnackBarCloseValidation = () => {
    setOpenSnackbarValidation(false);
    // navigate("/masterDataCockpit/materialMaster/materialSingle");
  };
  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };
  const handleMessageDialogNavigate = () => {
    // navigate("/masterDataCockpit/materialMaster/materialSingle");
  };

  const handleSnackBarOpenValidation = () => {
    setOpenSnackbarValidation(true);
  };

  const handleCheckValidationError = () => {
    return formValidator(
      payloadFields,
      requiredFields,
      setFormValidationErrorItems
    );
  };
  useEffect(() => {
    dispatch(setBKErrorFields(formValidationErrorItems));
  }, [formValidationErrorItems]);

  console.log("singleBKPayload", singleBKPayload);
  var payload = {
    AddressDto: {
      AddressID: "",
      Title: singleBKPayload?.Title ? singleBKPayload?.Title : "",
      Name: singleBKPayload?.Name ? singleBKPayload?.Name : "",
      Name2: singleBKPayload?.Name1 ? singleBKPayload?.Name1 : "",
      Name3: singleBKPayload?.Name2 ? singleBKPayload?.Name2 : "",
      Name4: singleBKPayload?.Name3 ? singleBKPayload?.Name3 : "",
      Sort1: singleBKPayload?.SearchTerm1 ? singleBKPayload?.SearchTerm1 : "",
      Sort2: singleBKPayload?.SearchTerm2 ? singleBKPayload?.SearchTerm2 : "",
      BuildLong: singleBKPayload?.BuildingCode
        ? singleBKPayload?.BuildingCode
        : "",
      RoomNo: singleBKPayload?.RoomNumber ? singleBKPayload?.RoomNumber : "",
      Floor: singleBKPayload?.Floor ? singleBKPayload?.Floor : "",
      COName: singleBKPayload?.co ? singleBKPayload?.co : "",
      StrSuppl1: singleBKPayload?.Street1 ? singleBKPayload?.Street1 : "",
      StrSuppl2: singleBKPayload?.Street2 ? singleBKPayload?.Street2 : "",
      Street: singleBKPayload?.Street3 ? singleBKPayload?.Street3 : "",
      HouseNo: singleBKPayload?.HouseNumber ? singleBKPayload?.HouseNumber : "",
      HouseNo2: singleBKPayload?.Supplement ? singleBKPayload?.Supplement : "",
      StrSuppl3: singleBKPayload?.Street4 ? singleBKPayload?.Street4 : "",
      Location: singleBKPayload?.Street5 ? singleBKPayload?.Street5 : "",
      District: singleBKPayload?.District ? singleBKPayload?.District : "",
      HomeCity: singleBKPayload?.OtherCity ? singleBKPayload?.OtherCity : "",
      PostlCod1: singleBKPayload?.PostalCode ? singleBKPayload?.PostalCode : "",
      PostlCod2: singleBKPayload?.PostalCode1 ? singleBKPayload?.PostalCode1 : "",
      PostlCod3: singleBKPayload?.CompanyPostCd
        ? singleBKPayload?.CompanyPostCd
        : "",
      PoBox: singleBKPayload?.POBox ? singleBKPayload?.POBox : "",
      PoBoxCit: singleBKPayload?.POBoxCity ? singleBKPayload?.POBoxCity : "",
      PoBoxReg: singleBKPayload?.Region2?.code
        ? singleBKPayload?.Region2?.code
        : "",
      PoboxCtry: singleBKPayload?.Country2?.code ? singleBKPayload?.Country2?.code : "",
     Country: singleBKPayload?.Country1?.code ? singleBKPayload?.Country1?.code : "",
      TimeZone: singleBKPayload?.TimeZone?.code ? singleBKPayload?.TimeZone?.code : "",
      Taxjurcode: singleBKPayload?.TaxJurisdiction?.code
        ? singleBKPayload?.TaxJurisdiction?.code
        : "",
      Transpzone: singleBKPayload?.TransportZone?.code
        ? singleBKPayload?.TransportZone?.code
        : "",
      Regiogroup: singleBKPayload?.StructureGroup?.code
        ? singleBKPayload?.StructureGroup?.code
        : "",
      DontUseS: singleBKPayload?.Undeliverable?.code
        ? singleBKPayload?.Undeliverable?.code
        : "",
      DontUseP: singleBKPayload?.Undeliverable1?.code
        ? singleBKPayload?.Undeliverable1?.code
        : "",
      PoWONo: singleBKPayload?.POBoxwoNo === true ? "X" : "",
      DeliServType: singleBKPayload?.DeliveryServiceType?.code
        ? singleBKPayload?.DeliveryServiceType?.code
        : "",
      DeliServNumber: singleBKPayload?.DeliveryServiceNo
        ? singleBKPayload?.DeliveryServiceNo
        : "",
      Township: singleBKPayload?.Township ? singleBKPayload?.Township : "",
      Langu: singleBKPayload?.Language?.code ? singleBKPayload?.Language?.code : "",
      Tel1Numbr: singleBKPayload?.Telephone ? singleBKPayload?.Telephone : "",
      Tel1Ext: singleBKPayload?.Extension ? singleBKPayload?.Extension : "",
      MobilePhone: singleBKPayload?.MobilePhone ? singleBKPayload?.MobilePhone : "" ,
      FaxNumber: singleBKPayload?.Fax ? singleBKPayload?.Fax : "",
      FaxExtens: singleBKPayload?.Extension1 ? singleBKPayload?.Extension1 : "",
      EMail: singleBKPayload?.EMailAddress ? singleBKPayload?.EMailAddress : "",
      AdrNotes: singleBKPayload?.Notes ? singleBKPayload?.Notes : "",
      // Country: singleBKPayload?.Region1?.code ? singleBKPayload?.Region1?.code : "",
      Region: singleBKPayload?.Region1?.code ? singleBKPayload?.Region1?.code : "",
      PoBoxLobby: singleBKPayload?.PoBoxLobby
        ? singleBKPayload?.PoBoxLobby
        : "",
    },
    BankKeyID:"",
    ReqCreatedBy: userData?.user_id,
    ReqCreatedOn: "",
    RequestStatus: "",
    CreationId: "",
    EditId: "",
    DeleteId: "",
    MassCreationId: "",
    MassEditId: "",
    MassDeleteId: "",
    RequestType: "Create",
    TaskId: "",
    Remarks: remarks ? remarks : "",
    Action: "I",
    Validation: testrunStatus === true ? "X" : "",
    BankCtry: displayData?.bankCtryReg?.newBankCtryReg?.code ? displayData?.bankCtryReg?.newBankCtryReg?.code : "",
    BankKey: displayData?.bankKey?.newBankKey ? displayData?.bankKey?.newBankKey : "",
    BankName: singleBKPayload?.BankName ? singleBKPayload?.BankName : "",
    BankRegion: singleBKPayload?.Region?.code
      ? singleBKPayload?.Region?.code
      : "",
    BankStreet: singleBKPayload?.Street ? singleBKPayload?.Street : "",
    City: singleBKPayload?.City ? singleBKPayload?.City : "",
    BankBranch: singleBKPayload?.BankBranch ? singleBKPayload?.BankBranch : "",
    SwiftCode: singleBKPayload?.SWIFTBIC ? singleBKPayload?.SWIFTBIC : "",
    BankGroup: singleBKPayload?.BankGroup ? singleBKPayload?.BankGroup : "",
    PobkCurac: singleBKPayload?.PostbankAcct === true ? "X" : "",
    BankNo: singleBKPayload?.BankNumber ? singleBKPayload?.BankNumber : "",
  };
  const onValidateBankKey = () => {
    setBlurLoading(true);
    const hSuccess = (data) => {
      setBlurLoading(false);
      if (data.statusCode === 201) {
        // Handle success
        setSubmitForReviewDisabled(false);
        setMessageDialogTitle("Create");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `All Data has been Validated. Bank Key can be Sent for Review`
        );
        //setHandleExtrabutton(false);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
        setValidateFlag(true);
       
        // setBlurLoading(false);
      } else {
         // Handle error
         setMessageDialogTitle("Error");
         setsuccessMsg(false);
         setMessageDialogMessage(
           `${
             data?.body?.message[0] ? data?.body?.message[0] : data?.body?.value
           }`
         );
         //setHandleExtrabutton(false);
         setMessageDialogSeverity("danger");
         setMessageDialogOK(false);
         setMessageDialogExtra(true);
         handleMessageDialogClickOpen();
         // setBlurLoading(false);
         // setIsLoading(false);
      }
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_BankKey}/alter/validateSingleBankKey`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const onSaveAsDraftButtonClick = () => {
    setMessageDialogSeverity(false);
    handleMessageDialogClickOpen();
    setMessageDialogTitle("Confirm");
    setMessageDialogMessage(`Do You Want to Save as Draft ?`);
    setHandleExtrabutton(true);
    setHandleExtraText("proceed");
  };
  const handleProceedbutton = () => {
    // const isValidation = handleCheckValidationError();
    // if (isValidation) {
    const hSuccess = (data) => {
      setIsLoading(true);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Bank Key has been saved NBS${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
        const secondApiPayload = {
          artifactId: bkNumber,
          createdBy: userData?.emailId,
          artifactType: "BankKey",
          requestId: `CBS${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
        navigate("/masterDataCockpit/bankKey");
      } else {
        setMessageDialogTitle("Save");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Saving the Data ");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_BankKey}/alter/bankKeyAsDraft`,
      "post",
      hSuccess,
      hError,
      payload
    );
    // } else {
    //   handleSnackBarOpenValidation();
    // }
  };

  const onSubmitForReviewButtonClick = () => {
    if (remarks.length <= 0){
      setRemarksValidationError(true)
    }else{
    onSubmitForReviewBankKeySingle()
    }
  }

  const onSubmitForReviewBankKeySingle = () => {
    handleCreateDialogClose();
    //handleSnackBarClose();
    setIsLoading(true);
    // const isValidation = handleCheckValidationError();
    // if (isValidation) {
    const hSuccess = (data) => {
    
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Bank Key has been submitted for review NBS${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);

        const secondApiPayload = {
          artifactId: bkNumber,
          createdBy: userData?.emailId,
          artifactType: "BankKey",
          requestId: `NBS${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
        navigate("/masterDataCockpit/bankKey");
      } else {
        setMessageDialogTitle("Create");
        setsuccessMsg(false);
        setMessageDialogMessage("Creation Failed");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_BankKey}/alter/bankKeySubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
    // } else {
    //   handleSnackBarOpenValidation();
    // }
  };

  const handleCreateDialog = () => {
    setTestrunStatus(false);
    setOpenCreateDialog(true);
  };
  const handleCreateDialogClose = () => {
    setRemarksValidationError(false);
    setTestrunStatus(true);
    setOpenCreateDialog(false);
  };
  const handleRemarks = (e, value) => {
    //setRemarks(e.target.value);
    const newValue = e.target.value;
    if (newValue.length > 0 && newValue[0] === " ") {
      setRemarks(newValue.trimStart());
    } else {
      //let costCenterValue = e.target.value;
      let remarksUpperCase = newValue.toUpperCase();
      setRemarks(remarksUpperCase);
    }
  };
  const getRegion = (value) => {
    const hSuccess = (data) => {
      // console.log("value",data);
      dispatch(setDropDown({ keyName: "Region", data: data.body }));
    };
    const hError = (error) => {
      console.log(error,"error in dojax");
    };
    doAjax(
      `/${destination_BankKey}/data/getRegionBasedOnCountry?country=${value}`,
      "get",
      hSuccess,
      hError
    );
  };

  useEffect(()=>{
    getRegion(displayData?.bankCtryReg?.newBankCtryReg?.code)
  },[])

  const handleWarningDialogClose = () => {
    setOpenMessageDialog(false);
  };


  return (
    <>
{isLoading === true ? (
        <LoadingComponent />
      ) : (
        <div>
      <ReusableDialog
        dialogState={openMessageDialog}
        openReusableDialog={handleMessageDialogClickOpen}
        closeReusableDialog={handleMessageDialogClose}
        dialogTitle={messageDialogTitle}
        dialogMessage={messageDialogMessage}
        handleDialogConfirm={handleMessageDialogClose}
        dialogOkText={"OK"}
        handleExtraButton={handleProceedbutton}
        dialogSeverity={messageDialogSeverity}
        showCancelButton={true}
        showExtraButton={handleExtrabutton}
        handleDialogReject={handleWarningDialogClose}
        handleExtraText={handleExtraText}
      />

      {formValidationErrorItems.length != 0 && (
        <ReusableSnackBar
          openSnackBar={openSnackbarValidation}
          alertMsg={
            "Please fill the following Field: " +
            formValidationErrorItems.join(", ")
          }
          handleSnackBarClose={handleSnackBarCloseValidation}
        />
      )}
      {successMsg && (
        <ReusableSnackBar
          openSnackBar={openSnackbar}
          alertMsg={messageDialogMessage}
          handleSnackBarClose={handleSnackBarClose}
        />
      )}

      <Grid
        container
        style={{
          ...outermostContainer_Information,
          backgroundColor: "#FAFCFF",
        }}
      >
        <Grid sx={{ width: "inherit" }}>
          <Grid item md={7} style={{ padding: "16px", display: "flex" }}>
            <Grid item md={5} sx={{ display: "flex" }}>
              <Grid>
                <IconButton
                  // onClick={handleBacktoRO}
                  color="primary"
                  aria-label="upload picture"
                  component="label"
                  sx={iconButton_SpacingSmall}
                >
                  <ArrowCircleLeftOutlinedIcon
                    style={{ height: "1em", width: "1em", color: "#000000" }}
                    onClick={() => {
                      navigate("/masterDataCockpit/bankKey");
                      dispatch(clearPayload());
                      dispatch(clearOrgData());
                    }}
                  />
                </IconButton>
              </Grid>
              <Grid>
                <Typography variant="h3">
                  <strong>Create Bank Key</strong>
                </Typography>
                <Typography variant="body2" color="#777">
                  This view creates a new Bank Key
                </Typography>
              </Grid>
            </Grid>
          </Grid>
          <Grid container style={{ padding: "0 1rem 0 1rem" }}>
            <Grid container sx={outermostContainer_Information}>
              <Grid
                container
                display="flex"
                flexDirection="row"
                flexWrap="nowrap"
              >
                <Box width="70%" sx={{ marginLeft: "40px" }}>
                  <Grid item sx={{ paddingTop: "2px !important" }}>
                    <Stack flexDirection="row">
                      <div style={{ width: "15%" }}>
                        <Typography variant="body2" color="#777">
                          Bank Country
                        </Typography>
                      </div>
                      <Typography
                        variant="body2"
                        fontWeight="bold"
                        justifyContent="flex-start"
                      >
                        : {displayData?.bankCtryReg?.newBankCtryReg?.code}
                      </Typography>
                    </Stack>
                  </Grid>

                  <Grid item sx={{ paddingTop: "2px !important" }}>
                    <Stack flexDirection="row">
                      <div style={{ width: "15%" }}>
                        <Typography variant="body2" color="#777">
                          Bank Key
                        </Typography>
                      </div>
                      <Typography variant="body2" fontWeight="bold">
                        :{displayData?.bankKey?.newBankKey}
                      </Typography>
                    </Stack>
                  </Grid>
                </Box>
                <Box width="30%" sx={{ marginLeft: "40px" }}>
                  <Grid item>
                    <Stack flexDirection="row">
                      <Typography
                        variant="body2"
                        color="#777"
                        style={{ width: "30%" }}
                      >
                        {/* {item.info ? item.desc : ""} */}
                      </Typography>

                      <Typography
                        variant="body2"
                        fontWeight="bold"
                        sx={{ width: "8%", textAlign: "center" }}
                      >
                        {/* {item.info ? ":" : ""} */}
                      </Typography>

                      <Typography
                        variant="body2"
                        fontWeight="bold"
                        justifyContent="flex-start"
                      >
                        {/* {item?.info?.code}
                            {item?.info?.code && item?.info?.desc
                              ? " - "
                              : null} */}
                        {/* {item?.info?.desc} */}
                      </Typography>
                    </Stack>
                  </Grid>
                </Box>
              </Grid>

              <Grid container>
                <Stepper
                  activeStep={activeStep}
                  sx={{
                    background: "#FFFFFF",
                    borderBottom: "1px solid #BDBDBD",
                    width: "100%",
                    height: "48px",
                  }}
                >
                  {steps.map((label, index) => (
                    <Step key={label}>
                      <StepLabel sx={{ fontWeight: "700" }}>{label}</StepLabel>
                    </Step>
                  ))}
                </Stepper>
              </Grid>

              <Grid container>
                <Grid container>{getStepContent(activeStep)}</Grid>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
      <Paper
        sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
        elevation={2}
      >
        <BottomNavigation
          className="container_BottomNav"
          showLabels
          sx={{ display: "flex", justifyContent: "flex-end" }}
        >
          {/* <Button
            variant="outlined"
            size="small"
            sx={{ button_Outlined, mr: 1 }}
            onClick={onValidateProfitCenter}
          >
            Validate
          </Button> */}
          <Button
            variant="contained"
            size="small"
            sx={{ ...button_Primary, mr: 1 }}
            onClick={onSaveAsDraftButtonClick}
            // disabled={activeStep === 0}
          >
            Save As Draft
          </Button>
          <Button
            variant="contained"
            size="small"
            sx={{ ...button_Primary, mr: 1 }}
            onClick={handleBack}
            disabled={activeStep === 0}
          >
            Back
          </Button>
          {activeStep === steps.length - 1 ? (
            <>
              <Button
                variant="contained"
                size="small"
                sx={{ ...button_Primary, mr: 1 }}
                onClick={onValidateBankKey}
              >
                Validate
              </Button>
              <Button
                variant="contained"
                size="small"
                sx={{ ...button_Primary, mr: 1 }}
                onClick={handleCreateDialog}
                disabled={submitForReviewDisabled}
              >
                Submit For Review
              </Button>
            </>
          ) : (
            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary, mr: 1 }}
              onClick={handleNext}
            >
              Next
            </Button>
          )}
        </BottomNavigation>
      </Paper>

      <Dialog
            hideBackdrop={false}
            elevation={2}
            PaperProps={{
              sx: { boxShadow: "none" },
            }}
            open={openCreateDialog}
            onClose={handleCreateDialogClose}
          >
            {/* <Grid
          container
          sx={{ display: "flex", justifyContent: "space-between" }}
        > */}
            {/* <Grid item> */}
            <DialogTitle
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF40",
                // borderBottom: "1px solid grey",
                display: "flex",
              }}
            >
              <Typography variant="h6">REMARKS</Typography>

              <IconButton
                sx={{ width: "max-content" }}
                onClick={handleCreateDialogClose}
                children={<CloseIcon />}
              />
            </DialogTitle>
            {/* </Grid> */}
            {/* </Grid> */}
            <DialogContent sx={{ padding: ".5rem 1rem" }}>
              <Stack>
                <Box sx={{ minWidth: 400 }}>
                  <FormControl sx={{ height: "auto" }} fullWidth>
                    <TextField
                      sx={{ backgroundColor: "#F5F5F5" }}
                      value={remarks}
                      onChange={handleRemarks}
                      multiline
                      placeholder={"ENTER REMARKS"}
                      inputProps={{maxLength: 254}}
                    ></TextField>
                  </FormControl>
                </Box>
                {remarksValidationError && (
                      <Grid>
                        <Typography style={{ color: "red" }}>
                            Please Enter Remarks
                        </Typography>
                      </Grid>
                    )}
              </Stack>
              {/* <TextField
            autoFocus
            margin="dense"
            id="name"
            label="Enter Remarks for Correction"
            type="text"
            fullWidth
            variant="standard"
            onChange={handleRemarks}
          /> */}
            </DialogContent>
            <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
              <Button
                sx={{ width: "max-content", textTransform: "capitalize" }}
                onClick={handleCreateDialogClose}
              >
                Cancel
              </Button>
              <Button
                className="button_primary--normal"
                type="save"
                onClick={onSubmitForReviewButtonClick}
                variant="contained"
              >
                Submit
              </Button>
            </DialogActions>
          </Dialog>

      <Backdrop
        sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
        open={blurLoading}
        // onClick={handleClose}
      >
        <CircularProgress color="inherit" />
      </Backdrop>
    </div>
      )}
    </>
    
  );
};

export default NewSingleBankKey;
