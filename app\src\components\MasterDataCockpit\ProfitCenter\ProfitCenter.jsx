import React from "react";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DemoContainer } from "@mui/x-date-pickers/internals/demo";
import DeleteIcon from "@mui/icons-material/Delete";
import DownloadIcon from "@mui/icons-material/Download";
import CloseIcon from "@mui/icons-material/Close";
import ReusableAttachementAndComments from "../../Common/ReusableAttachmentAndComments/ReusableAttachementAndComments";
import {
  InfoOutlined,
  IosShare,
  Refresh,
  TuneOutlined,
} from "@mui/icons-material";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import {
  Button,
  Checkbox,
  Grid,
  Paper,
  IconButton,
  Typography,
  TextField,
  Box,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Popper,
  BottomNavigation,
  ListItemText,
  InputLabel,
  tooltipClasses,
  Card,
  CardContent,
  OutlinedInput,
  Autocomplete,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  ButtonGroup,
  ClickAwayListener,
  MenuList,
  Divider,
} from "@mui/material";
import FileUpload from "../../Common/FileUpload";
import moment from "moment/moment";
import { Stack } from "@mui/system";
import Select from "@mui/material/Select";
import { FormControl, MenuItem } from "@mui/material";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import ForwardToInboxOutlinedIcon from "@mui/icons-material/ForwardToInboxOutlined";
import CheckCircleOutlineOutlinedIcon from "@mui/icons-material/CheckCircleOutlineOutlined";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import SearchBar from "../../Common/SearchBar";
import ReusableDialog from "../../Common/ReusableDialog";
import { ViewDetailsIcon } from "../../Common/icons";
import { useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import TrackChangesIcon from "@mui/icons-material/TrackChanges";
import styled from "@emotion/styled";
import html2canvas from "html2canvas";
import {
  commonFilterClear,
  commonFilterUpdate,
} from "../../../app/commonFilterSlice";
import { v4 as uuidv4 } from "uuid";
import {
  button_Marginleft,
  button_Outlined,
  button_Primary,
  container_filter,
  container_table,
  font_Small,
  iconButton_SpacingSmall,
  outerContainer_Information,
  outermostContainer,
  outermostContainer_Information,
} from "../../Common/commonStyles";
import {

  destination_MaterialMgmt,
  destination_ProfitCenter,
} from "../../../destinationVariables";
import { doAjax } from "../../Common/fetchService";
import ClearIcon from "@mui/icons-material/Clear";
import ReusableTable from "../../Common/ReusableTable";

import { setDropDown } from "../../../app/dropDownDataSlice";


// import "./masterDataCockpit.css";
import {
  setProfitCenterAddressTab,
  setProfitCenterBasicDataTab,
  setProfitCenterCommunicationTab,
  setProfitCenterHistoryTab,
  setProfitCenterCompCodesTab,
  setProfitCenterIndicatorsTab,
  setMultipleProfitCenterData,
  setHandleMassMode,
  setPCRequiredFields,
  setPCErrorFields,
  clearProfitCenter,
} from "../../../app/profitCenterTabsSlice";
import { MatDownload } from "../../DocumentManagement/UtilDoc";
import AttachmentUploadDialog from "../../Common/AttachmentUploadDialog";
import ReusableIcon from "../../common/ReusableIcon";
import { checkIwaAccess, saveExcel } from "../../../functions";
import { setTaskData } from "../../../app/userManagementSlice";
import LoadingComponent from "../../Common/LoadingComponent";

// import { setProfitCenterControlTab } from "../../../app/profitCenterTabsSlice";
const HtmlTooltip = styled(({ className, ...props }) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: "#f5f5f9",
    color: "rgba(0, 0, 0, 0.87)",
    maxWidth: 250,
    border: "1px solid #dadde9",
  },
}));
const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};

// const exportAsPicture = () => {
//   const html = document.getElementsByTagName("HTML")[0];
//   const body = document.getElementsByTagName("BODY")[0];
//   let htmlWidth = html.clientWidth;
//   let bodyWidth = body.clientWidth;

//   const data = document.getElementById("e-invoice-export"); //CHANGE THIS ID WITH ID OF OUTERMOST DIV CONTAINER
//   const newWidth = data.scrollWidth - data.clientWidth;

//   if (newWidth > data.clientWidth) {
//     htmlWidth += newWidth;
//     bodyWidth += newWidth;
//   }

//   html.style.width = htmlWidth + "px";
//   body.style.width = bodyWidth + "px";

//   html2canvas(data)
//     .then((canvas) => {
//       return canvas.toDataURL("image/png", 1.0);
//     })
//     .then((image) => {
//       saveAs(image, "E-InvoiceReport.png"); //CHANGE THE NAME OF THE FILE
//       html.style.width = null;
//       body.style.width = null;
//     });
// };



const saveAs = (blob, fileName) => {
  const elem = window.document.createElement("a");
  elem.href = blob;
  elem.download = fileName;
  (document.body || document.documentElement).appendChild(elem);
  if (typeof elem.click === "function") {
    elem.click();
  } else {
    elem.target = "_blank";
    elem.dispatchEvent(
      new MouseEvent("click", {
        view: window,
        bubbles: true,
        cancelable: true,
      })
    );
  }
  URL.revokeObjectURL(elem.href);
  elem.remove();
};
const ProfitCenter = () => {
  const [snackbar, setSnackbar] = useState(false);
  const [openSnackbar, setopenSnackbar] = useState(false);
  // const [controllingArea, setControllingArea] = useState();
  const [messageDialogSeverity, setMessageDialogSeverity] = useState("");
  const appSettings = useSelector((state) => state.appSettings["Format"]);

  // console.log("controllingArea",controllingArea);
  // let userData = useSelector((state) => state.userManagement.userData);
  // let iwaAccessData = useSelector(
  //   (state) => state.userManagement.entitiesAndActivities?.["Return Order"]
  // );
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const ITEM_HEIGHT = 48;
  const ITEM_PADDING_TOP = 8;

  const MenuProps = {
    PaperProps: {
      style: {
        maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
        width: 250,
      },
    },
  };

  const handleOnClick = (materialNumber) => {
    navigate(
      "/masterDataCockpit/materialMaster/displayMaterialDetail/" +
        materialNumber
    );
  };

  const HtmlTooltip = styled(({ className, ...props }) => (
    <Tooltip {...props} classes={{ popper: className }} />
  ))(({ theme }) => ({
    [`& .${tooltipClasses.tooltip}`]: {
      backgroundColor: "#f5f5f9",

      color: "rgba(0, 0, 0, 0.87)",

      maxWidth: 250,

      border: "1px solid #dadde9",
    },
  }));
  const [Status_ServiceReqForm, setStatus_ServiceReqForm] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [tableLoading, setTableLoading] = useState(false);
  const [value, setValue] = useState(null);
  const ariaLabel = { "aria-label": "description" };
  const [rmDataRows, setRmDataRows] = useState([]);
  const [UserName, setUserName] = React.useState("");
  const [openSnackBaraccept, setOpenSnackBaraccept] = useState(false);
  const [confirmingid, setConfirmingid] = useState("");
  const [materialNumber, setMaterialNumber] = useState("");
  const [confirmStatus, setConfirmStatus] = useState(true);
  const [messageDialogExtra, setMessageDialogExtra] = useState(false);
  const [messageDialogOK, setMessageDialogOK] = useState(true);
  const [companyCodeSet, setCompanyCodeSet] = useState([]);
  const [plantCodeSet, setPlantCodeSet] = useState([]);
  const [vendorDetailsSet, setVendorDetailsSet] = useState([]);
  const [taskstatusSet, setTasksttusSet] = useState([]);
  const [disableButton, setDisableButton] = useState(true);
  const [selectedRow, setSelectedRow] = useState([]);
  const [selectedDetails, setSelectedDetails] = useState([]);
  const [downloadError, setdownloadError] = useState(false);
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [displayedFields, setDisplayedFields] = useState([]);
  const [filterFieldData, setFilterFieldData] = useState({});
  const [matType, setMatType] = useState([]);
  const [matGroup, setMatGroup] = useState([]);
  const [viewDetailpage, setViewDetailpage] = useState(false);
  const [matNumber, setMatNumber] = useState([]);
  const [dynamicOptions, setDynamicOptions] = useState([]);
  const [plantForWarehouse, setPlantForWarehouse] = useState([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogOpenCreate, setDialogOpenCreate] = useState(false);
  const [fullWidth, setFullWidth] = useState(true);
  const [maxWidth, setMaxWidth] = useState("sm");
  const [enableDocumentUpload, setEnableDocumentUpload] = useState(false);
  const [successMsg, setsuccessMsg] = useState(false);
  const [newControllingArea, setNewControllingArea] = useState("");
  const [newProfitCenter, setNewProfitCenter] = useState("");
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [skip, setSkip] = useState(0);
  const [confirmation, setconfirmation] = useState([]);
  const [confirmationText, setConfirmationText] = useState(null);
  const [poHeader, setPoHeader] = useState(null);
  const [roCount, setroCount] = useState(0);
  const [count, setCount] = useState(0);
  const [showBtmNav, setShowBtmNav] = useState(false);
  const [opendialog, setOpendialog] = useState(false);
  const [openSnackbarDialog, setOpenSnackbarDialog] = useState(false);
  const [opendialog2, setOpendialog2] = useState(false);
  const [opendialog3, setOpendialog3] = useState(false);
  const [openforwarddialog, setOpenforwarddialog] = useState(false);
  const [rejectInputText, setRejectInputText] = useState("");
  const [acceptInputText, setAcceptInputText] = useState("");
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState("");
  const [messageDialogMessage, setMessageDialogMessage] = useState();
  const [profitCenterValid, setProfitCenterValid] = useState(false);
  const [controllingAreaValid, setControllingAreaValid] = useState(false);
  const [anchorEl_Preset, setAnchorEl] = useState(null);
  const anchorRef = React.useRef(null);
  const [openButton, setOpenButton] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [openButtonChange, setOpenButtonChange] = useState(false);
  const anchorRefChange = React.useRef(null);
  const anchorRefCreate = React.useRef(null);
  const [newCompanyCode, setNewComapnyCode] = useState("");
  const [newCompanyCodeCopy, setNewComapnyCodeCopy] = useState("");
  const [newProfitCenterName, setNewProfitCenterName] = useState('');
  // const [handleMassMode, setHandleMassMode] = useState("");
  const [selectedIndexCreate, setSelectedIndexCreate] = useState(0);
  const [openButtonCreate, setOpenButtonCreate] = useState(false);
    const [newControllingAreaCopyFrom, setNewControllingAreaCopyFrom] = useState("");
  const [selectedIndexChange, setSelectedIndexChange] = useState(0);
  const [selectedMassChangeRowData, setSelectedMassChangeRowData] = useState(
    []
  );
  const [isValidationError, setIsValidationError] = useState(false);
  const [isValidationErrorwithCopy, setIsValidationErrorwithCopy] = useState(false);
  const [newProgfitCenterValid, setNewProfitCenterValid] = useState(false)
  const [newPofitCenterValidWithCopy,setNewProfitCenterValidWithCopy]=useState(false)
  const [selectedRows, setSelectedRows] = useState([]);
  const [isCheckboxSelected, setIsCheckboxSelected] = useState(true);
  const options = [
    "Create Multiple",
    "Upload Template ",
    "Download Template ",
  ];
  const optionsChange = [
    "Change Multiple",
    "Upload Template ",
    "Download Template ",
  ];
  const optionsCreateSingle = [
    "Create Single",
    "With Copy",
    "Without Copy",
  ];
  console.log("newProfitCenterName",newProfitCenterName);
  const openAnchor = Boolean(anchorEl_Preset);

  let iwaAccessData = useSelector(
    (state) => state.userManagement.entitiesAndActivities?.["Display Material"]
  );
  let userData = useSelector((state) => state.userManagement.userData);
  const [checkValidationProfitCenter, setCheckValidationProfitCenter] =
    useState(false);
  const pcSearchForm = useSelector(
    (state) => state.commonFilter["ProfitCenter"]
  );
  console.log("newCompanyCode", newCompanyCode);
  const formcontroller_SearchBar = useSelector(
    (state) => state.commonSearchBar["ProfitCenter"]
  );
  const handleMassModePC = useSelector(
    (state) => state.profitCenter.handleMassMode
  );
  console.log("formcontroller_SearchBar", formcontroller_SearchBar);
  const dropDownData = useSelector((state) => state?.AllDropDown?.dropDown);
  const dropdownData = useSelector((state) => state.AllDropDown.dropDown);
  console.log("dropDownData",dropDownData?.ProfitCenter);
  const sendNewProfitCenterData = {
    profitCenter: { newProfitCenter },
    companyCode: {newCompanyCode},
    companyCodeCopy: {newCompanyCodeCopy},
    profitCenterName: { newProfitCenterName },
    controllingArea: { newControllingArea },
    controllingAreaDataCopy: { newControllingAreaCopyFrom },
    // validFromDate: { newValidFromDate },
    // validToDate: { newValidToDate },
  };
  console.log("typepc",typeof(sendNewProfitCenterData));
  const handleDialogClickOpen = () => {
    setDialogOpen(true);
  };
  const handleDialogClickOpenWithCopy = () => {
    setDialogOpenCreate(true);
  };
  const handleDialogCloseCreate = () => {
    setDialogOpenCreate(false);
    setIsValidationErrorwithCopy(false)
    setNewProfitCenterValidWithCopy(false)
    setNewControllingArea('');
    setNewComapnyCode('');
    setNewProfitCenterName('')
    setNewProfitCenter('')
    //setNewCostCenterCopyFrom('')
    //setnewpro
  };
  // const getCompanyCodeBasedOnControllingArea = (newControllingArea) => {
  //   console.log("first", value);
  //   const hSuccess = (data) => {
  //     dispatch(
  //       setDropDown({
  //         keyName: "CompanyCode",
  //         data: data.body,
  //       })
  //     );
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(
  //     `/${destination_ProfitCenter}/data/getCompCodeBasedOnControllingArea?controllingArea=${newControllingArea}`,
  //     "get",
  //     hSuccess,
  //     hError
  //   );
  // };
  const getCompanyCodeBasedOnControllingArea = (newControllingArea) => {
    console.log("first", value);
    const hSuccess = (data) => {
      dispatch(
        setDropDown({
          keyName: "CompCodeBasedOnControllingArea",
          data: data.body.map((x, idx) => {
            return {
              id: idx,
              companyCodes: x.code,
              companyName: x.desc,
              assigned: "X",
            };
          }),
        })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/data/getCompCodeBasedOnControllingArea?controllingArea=${newControllingArea.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCompanyCodeBasedOnControllingAreaCopy = (value) => {
    console.log("first", value);
    const hSuccess = (data) => {
      dispatch(
        setDropDown({
          keyName: "CompCode",
          data: data.body,
        })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/data/getCompCodeBasedOnControllingArea?controllingArea=${value.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getProfitCenterBasedOnControllingAreaCopy = (value) => {
    console.log("first", value);
    const hSuccess = (data) => {
      dispatch(
        setDropDown({
          keyName: "ProfitCenter",
          data: data.body,
        })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/data/getProfitCenterAsPerControllingArea?controllingArea=${value.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  // const handleDialogProceedWithCopy = () => {
  //   if (newProfitCenter === "" || newControllingArea === null) {
  //     setProfitCenterValid(true);
  //     setControllingAreaValid(true);
  //   } else {
  //     setProfitCenterValid(false);
  //     setControllingAreaValid(false);
  //   }
  //   getProfitCenterGroup(newControllingArea);
  //   // getCompanyCodeBasedOnControllingArea();
  //   let selectedControllingArea = newControllingArea.code;
  //   let selectedCompanyCode = newCompanyCode.code;
  //   let selectedProfitCenterName = newProfitCenterName;
  //   let result = selectedControllingArea.concat("$$","P",selectedCompanyCode, selectedProfitCenterName);

  //   const hSuccess = (data) => {
  //     console.log("data", data);
  //     if (data.body.length > 0) {
  //       console.log('rakesj')
  //       setCheckValidationProfitCenter(true);
  //     } else {
  //       console.log('nihar')
  //       // navigate(`/masterDataCockpit/profitCenter/displayCopyProfitCenter/${selectedCompanyCode}${selectedProfitCenterName}`, {
  //       navigate(`/masterDataCockpit/profitCenter/displayCopyProfitCenter/TUK1_7899`, {
  //         state: sendNewProfitCenterData,
  //       });
  //     }
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };

  //   doAjax(
  //     `/${destination_ProfitCenter}/alter/fetchCoAreaPCDupliChk?ctrlAreaPCToCheck=${result}`,
  //     "get",
  //     hSuccess,
  //     hError
  //   );
  // };
  const handleDialogProceedWithCopy = () => {
    
    duplicateCheckWithCopy();
  };
  const duplicateCheckWithCopy = () => {
    if ((newControllingArea?.code === undefined || newControllingArea?.code === '') || (newCompanyCodeCopy?.code === undefined || newCompanyCodeCopy?.code === '') || (newProfitCenterName === undefined || newProfitCenterName === '') ||(newControllingAreaCopyFrom?.code === undefined || newControllingAreaCopyFrom?.code === '')||(newProfitCenter?.code === undefined || newProfitCenter?.code === '')) { 
      setNewProfitCenterValidWithCopy(false)
      setIsValidationErrorwithCopy(true)
      return;
    }
    else {
      if (newProfitCenterName.length !== 5) {
        setNewProfitCenterValidWithCopy(true)
        setIsValidationErrorwithCopy(false)
        return;
        //duplicateCheck()
      } else {
        
        setNewProfitCenterValidWithCopy(false)
      }
      setIsValidationErrorwithCopy(false)
    }

    let selectedControllingArea =
      sendNewProfitCenterData?.controllingArea?.newControllingArea.code;
    let selectedCompanyCode =
    sendNewProfitCenterData?.companyCodeCopy?.newCompanyCodeCopy.code;
    let selectedProfitCenterName =
    sendNewProfitCenterData?.profitCenterName?.newProfitCenterName;
    let result = selectedControllingArea.concat("$$","P",selectedCompanyCode, selectedProfitCenterName);
    // setNewCombinedCostcenter(result);
    console.log("sendNewProfitCenterData", sendNewProfitCenterData);

    setIsLoading(true);
    const hSuccess = (data) => {
      setIsLoading(false);
      console.log("dupli", data);
      if (data.body.length > 0) {
        setCheckValidationProfitCenter(true);
      } else {
        navigate(
          `/masterDataCockpit/profitCenter/displayCopyProfitCenter/${sendNewProfitCenterData?.profitCenterName?.newProfitCenterName?.code}`,
          {
            state: sendNewProfitCenterData,
          }
        );
      }
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/alter/fetchCoAreaPCDupliChk?ctrlAreaPCToCheck=${result}`,
      "get",
      hSuccess,
      hError
    );
  };
  const handleDialogProceed = () => {
    /*if (newProfitCenter === "" || newControllingArea === null) {
      setProfitCenterValid(true);
      setControllingAreaValid(true);
    } else {
      setProfitCenterValid(false);
      setControllingAreaValid(false);
    }*/

    if ((newControllingArea?.code === undefined || newControllingArea?.code === '') || (newCompanyCode?.code === undefined || newCompanyCode?.code === '') || (newProfitCenterName === undefined || newProfitCenterName === '')) { //chiranjit
      setNewProfitCenterValid(false)
      setIsValidationError(true)
      return;
    }
    else {
      if (newProfitCenterName.length !== 5) {
        setNewProfitCenterValid(true)
        setIsValidationError(false)
        return;
        //duplicateCheck()
      } else {
        
        setNewProfitCenterValid(false)
      }
      setIsValidationError(false)
    }
    
    // getProfitCenterGroup(newControllingArea);
    getCompanyCodeBasedOnControllingArea(newControllingArea);
    let selectedControllingArea = newControllingArea.code;
    let selectedCompanyCode = newCompanyCode.code;
    let selectedProfitCenterName = newProfitCenterName;
    let result = selectedControllingArea.concat("$$","P",selectedCompanyCode, selectedProfitCenterName);
    setIsLoading(true);
    const hSuccess = (data) => {
      setIsLoading(false);
      console.log("data", data);
      if (data.body.length > 0) {
        console.log("kfdkfgk");
        setCheckValidationProfitCenter(true);
      } else {
        console.log("gyggu");
        navigate("/masterDataCockpit/profitCenter/newSingleProfitCenter", {
          state: sendNewProfitCenterData,
        });
      }
    };
    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter}/alter/fetchCoAreaPCDupliChk?ctrlAreaPCToCheck=${result}`,
      "get",
      hSuccess,
      hError
    );
  };
  const handleCloseButtonCreate = (event) => {
    if (
      anchorRefCreate.current &&
      anchorRefCreate.current.contains(event.target)
    ) {
      return;
    }
    setOpenButtonCreate((prevOpen) => !prevOpen);
  };
  const handleDialogClose = () => {
    setProfitCenterValid(false);
    setControllingAreaValid(false);
    setDialogOpen(false);
    setIsValidationError(false)
    setNewProfitCenterValid(false)
    setNewControllingArea('');
    setNewComapnyCode('');
    setNewProfitCenterName('')
  };
  const handleProfitCenter = (e) => {
    if (e.target.value !== null) {
      var tempProfitCenter = e.target.value;

      let tempFilterData = {
        ...pcSearchForm,
        profitCenter: tempProfitCenter,
      };
      dispatch(
        commonFilterUpdate({
          module: "ProfitCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleProfitCenterName = (e) => {
    if (e.target.value !== null) {
      var tempProfitCenterName = e.target.value;

      let tempFilterData = {
        ...pcSearchForm,
        profitCenterName: tempProfitCenterName,
      };
      dispatch(
        commonFilterUpdate({
          module: "ProfitCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleCompanyCode = (e, value) => {
    if (true) {
      var tempCompanyCode = value;

      let tempFilterData = {
        ...pcSearchForm,
        companyCode: tempCompanyCode,
      };
      dispatch(
        commonFilterUpdate({
          module: "ProfitCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleControllingAreaChange = (e, value) => {
    if (true) {
      var tempControllingArea = value;

      let tempFilterData = {
        ...pcSearchForm,
        controllingArea: tempControllingArea,
      };
      dispatch(
        commonFilterUpdate({
          module: "ProfitCenter",
          filterData: tempFilterData,
        })
      );
      getProfitCenterGroupForSearch(tempFilterData);
    }
    
   
    // console.log('controllll', pcSearchForm?.controllingArea?.code)
  };
  const handleCreateSingleWithCopy = () => {
    handleDialogClickOpenWithCopy();
  };
  const handleCreateSingleWithoutCopy = () => {
    handleDialogClickOpen();
  };
  // useEffect(() => {
  //   getProfitCenterGroupForSearch(pcSearchForm?.controllingArea?.code);
  // }, [pcSearchForm?.controllingArea?.code]);

  const handleGroup = (e, value) => {
    if (true) {
      var tempGroup = value;

      let tempFilterData = {
        ...pcSearchForm,
        profitCenterGroup: tempGroup,
      };
      dispatch(
        commonFilterUpdate({
          module: "ProfitCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleSegment = (e, value) => {
    if (true) {
      var tempSegment = value;

      let tempFilterData = {
        ...pcSearchForm,
        segment: tempSegment,
      };
      dispatch(
        commonFilterUpdate({
          module: "ProfitCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleCreatedBy = (e, value) => {
    if (true) {
      var tempCreatedBy = value;

      let tempFilterData = {
        ...pcSearchForm,
        createdBy: tempCreatedBy,
      };
      dispatch(
        commonFilterUpdate({
          module: "ProfitCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleChangedBy = (e, value) => {
    if (true) {
      var tempChangedBy = value;

      let tempFilterData = {
        ...pcSearchForm,
        changedBy: tempChangedBy,
      };
      dispatch(
        commonFilterUpdate({
          module: "ProfitCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  let dynamicDataApis = {
    "Person Responsible": `/${destination_MaterialMgmt}/data/getSalesOrg`,
    "Business Area ": `/${destination_MaterialMgmt}/data/getDivision`,
    "Functional Area": `/${destination_MaterialMgmt}/data/getLaboratoryDesignOffice`,
  };
  const handleSelection = (event) => {
    const selectedItems = event.target.value;
    setSelectedOptions(selectedItems);
    setDisplayedFields([]);
    console.log("selected field", event.target.value);

    selectedItems.forEach(async (selectedItem) => {
      const apiEndpoint = dynamicDataApis[selectedItem];
      fetchOptionsForDynamicFilter(apiEndpoint);
    });
  };
  const handleAddFields = () => {
    const numSelected = selectedOptions.length;
    const newFields = Array.from({ length: numSelected }, (_, index) => ({
      id: index,
      value: "",
    }));
    setDisplayedFields(newFields);
  };
  const handleFieldChange = (fieldId, value) => {
    setDisplayedFields(
      (selectedOptions) => selectedOptions.map((option) => option)
      // prevFields.map((field) => (field.id === fieldId ? { ...field, value } : field))
    );
  };
  const lookupData = {
    "Task ID": [{ label: "Type A" }, { label: "Type B" }],
    SalesOrg: [{ label: "Type A" }, { label: "Type B" }],
    "Warehouse Number": [{ label: "Desc X" }, { label: "Desc Y" }],
    "Storage Location": [{ label: "Desc X" }, { label: "Desc Y" }],
    Division: [{ label: "Desc X" }, { label: "Desc Y" }],
    "Old Material Number": [{ label: "Desc X" }, { label: "Desc Y" }],
    "Lab/Office": [{ label: "Desc X" }, { label: "Desc Y" }],
    "Transportation Group": [{ label: "Desc X" }, { label: "Desc Y" }],
    // "Batch management": [{ label: "Desc X" }, { label: "Desc Y" }],
    // ... other options
  };
  const items = [
    { title: "Segment" },
    // { title: "Warehouse Number" },
    // { title: "Storage Location" },
    // { title: "Business Area" },
    // { title: "Functional Area" },
    // { title: "Task ID"},
    // { title: "Status"},
    // { title: "Lab/Office" },
    // { title: "Transportation Group" },
    // { title: "Batch management" },
    // Add more options as needed
  ];
  const titleToFieldMapping = {
    "Task ID": "taskId",
    Status: "status",
    SalesOrganization: "salesOrg",
    Division: "division",
    OldMaterialNumber: "oldMaterialNumber",
    "Lab/Office": "labOffice",
    "Transportation Group": "transportationGroup",
    "Batch management": "batchManagement",
    // Add more mappings as needed
  };
  // let [rmSearchForm, setRmSearchForm] = useState({
  //   companyCode: "",
  //   vendorNo: "",
  //   paymentStatus: "",
  // });
  //Checked PO rows
  const getProfitCenter = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ProfitCenter", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/data/getProfitCenter`,
      "get",
      hSuccess,
      hError
    );
  };
  const getControllingArea = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ControllingArea", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/data/getControllingArea`,
      "get",
      hSuccess,
      hError
    );
  };
  const getProfitCenterGroupForSearch = (CA) => {
    // console.log("first", searchControllingArea);
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ProfitCtrGroupSearch", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/data/getProfitCtrGroup?controllingArea=${CA?.controllingArea?.code}`,
      "get",
      hSuccess,
      hError
    );
  };
 
  // const getCompanyCodeBasedOnCA = (value) => {
  //   const hSuccess = (data) => {
  //     dispatch(setDropDown({ keyName: "CompanyCode", data: data.body }));
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(
  //     `/${destination_ProfitCenter}/data/getCompCodeBasedOnControllingArea?controllingArea=${value.code}`,
  //     "get",
  //     hSuccess,
  //     hError
  //   );
  // };
  const getCompanyCode = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CompanyCode", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/data/getCompCode`,
      "get",
      hSuccess,
      hError
    );
  };
  const getProfitCenterGroup = (newControllingArea) => {
    console.log("first", value);
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ProfitCtrGroup", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/data/getProfitCtrGroup?controllingArea=${newControllingArea.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getSegment = (CA) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "Segment", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/data/getSegment`,
      "get",
      hSuccess,
      hError
    );
  };
  const getLanguageKey = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "Language", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/data/getLanguageKey`,
      "get",
      hSuccess,
      hError
    );
  };
  const getProfitCenterBasicDetails = () => {
    let viewName = "Basic Data";
    const hSuccess = (data) => {
      dispatch(setProfitCenterBasicDataTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getIndicatorsProfitCenter = () => {
    let viewName = "Indicators";
    const hSuccess = (data) => {
      console.log("profit", data);
      dispatch(setProfitCenterIndicatorsTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getCompCodesProfitCenter = () => {
    let viewName = "Comp Codes";
    const hSuccess = (data) => {
      dispatch(setProfitCenterCompCodesTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getAddressProfitCenter = () => {
    let viewName = "Address";
    const hSuccess = (data) => {
      dispatch(setProfitCenterAddressTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCommunicationProfitCenter = () => {
    let viewName = "Communication";
    const hSuccess = (data) => {
      dispatch(setProfitCenterCommunicationTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getHistoryProfitCenter = () => {
    let viewName = "History";
    const hSuccess = (data) => {
      dispatch(setProfitCenterHistoryTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getUserResponsible = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "UserResponsible", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/data/getUserResponsible`,
      "get",
      hSuccess,
      hError
    );
  };
  const getFormPlanningTemp = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "FormPlanningTemp", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/data/getFormPlanningTemp`,
      "get",
      hSuccess,
      hError
    );
  };
  const getRegion = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "Region", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/data/getRegion`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCountryOrRegion = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CountryReg", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/data/getCountryOrReg`,
      "get",
      hSuccess,
      hError
    );
  };
  const getJurisdiction = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "TaxJur", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/data/getJurisdiction`,
      "get",
      hSuccess,
      hError
    );
  };

  const handleSnackBarOpen = () => {
    setopenSnackbar(true);
  };
  const uploadExcel = (file) => {
    setIsLoading(true);
    console.log(file);
    const formData = new FormData();
    [...file].forEach((item) => formData.append("files", item));
    console.log(handleMassModePC,"handleMassModePC")
    if(handleMassModePC === 'Change'){
      var uploadUrl = `/${destination_ProfitCenter}/massAction/getAllProfitCenterFromExcelForMassChange`;
    }else{
       var uploadUrl = `/${destination_ProfitCenter}/massAction/getAllProfitCenterFromExcel`;
    }
    const hSuccess = (data) => {
      console.log(data, "example");
      setIsLoading(false);
      if (data.statusCode === 200) {
        setEnableDocumentUpload(false);
        // dispatch(setControllingArea(data?.body?.controllingArea));
        dispatch(setMultipleProfitCenterData(data?.body));
        setMessageDialogTitle("Create");
        setMessageDialogMessage(`${file.name} has been Uploaded Succesfully`);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
        navigate(`/masterDataCockpit/profitCenter/createMultipleProfitCenter`);
      } else {
        setEnableDocumentUpload(false);
        setMessageDialogTitle("Create");
        setsuccessMsg(false);
        setMessageDialogMessage("Creation Failed");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(uploadUrl, "postformdata", hSuccess, hError, formData);
  };
  const handleSelectionModelChange = (newSelection) => {
    console.log("newselection", newSelection);
    setSelectedRows(newSelection);
    let filterValueColumns = columns.map((t) => t.field);
    const selectedRowsDetails = rmDataRows.filter((row) =>
      newSelection.includes(row.id)
    );
    let requiredArrayDetails = [];
    selectedRowsDetails.map((s) => {
      console.log("sssssss", s);
      let requiredObject = {};
      filterValueColumns.forEach((y) => {
        console.log("yyyyy", s[y]);
        if (s[y] !== null) {
          requiredObject[y] = s[y] || "";
        }
      });
      requiredArrayDetails.push(requiredObject);
      setSelectedMassChangeRowData(requiredArrayDetails);
      console.log("requiredArrayDetails", requiredArrayDetails);
    });
  };

  useEffect(() => {
    // getUserResponsible();
    // getCostCenterCategory();
    // getBusinessArea();
    // getFunctionalArea();
    // getProfitCenter();

    // getCostingSheet();
    // getCountryOrRegion();
    // getJurisdiction();
    // getRegion();
    // getLanguageKey();
    // getCostCenterBasicDetails();
    // getControlCostCenter();
    getControllingArea();
    // getCompanyCode();
    getSegment();
    // getLanguageKey();
    // getUserResponsible();
    // getFormPlanningTemp();
    // getRegion();
    // getCountryOrRegion();
    // getJurisdiction();
    getProfitCenterBasicDetails();
    getIndicatorsProfitCenter();
    getCompCodesProfitCenter();
    getAddressProfitCenter();
    getHistoryProfitCenter();
    getCommunicationProfitCenter();
    dispatch(setTaskData({}))
    dispatch(clearProfitCenter());
    
  }, []);
  // const fetchOptionsForDynamicFilter = (apiEndpoint) => {
  //   const hSuccess = (data) => {
  //     setDynamicOptions({
  //       ...dynamicOptions,
  //       [apiEndpoint]: data.body,

  //     });
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(apiEndpoint, "get", hSuccess, hError);
  // };

  const fetchOptionsForDynamicFilter = (apiEndpoint) => {
    const hSuccess = (data) => {
      console.log("dataaaaaaaa", data.body);
      setDynamicOptions([...dynamicOptions, data.body]);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(apiEndpoint, "get", hSuccess, hError);
  };

  const clearSearchBar = () => {
    setMaterialNumber("");
  };

  const getMaterialNoGlobalSearch = (fetchSkip) => {
    console.log("pcSearchForm",pcSearchForm);
    setTableLoading(true);
    if (!fetchSkip) {
      setPage(0);
      setPageSize(10);
      setSkip(0);
    }
    let payload = {
      controllingArea: "",
      profitCenter: formcontroller_SearchBar?.number ?? "",
      profitCenterName: "",
      createdBy: "",
      segment: "",
      profitCenterGroup: "",
      top: "1000",
      skip: fetchSkip ?? 0,
    };

    const hSuccess = (data) => {
      var rows = [];
      for (let index = 0; index < data?.body?.list?.length; index++) {
        var tempObj = data?.body?.list[index];
        if (true) {
          var tempRow = {
            id: uuidv4(),
          description: tempObj.Description,
          controllingArea: tempObj.ControllingArea,
          companyCode: tempObj.CompanyCode,
          profitCenter: tempObj.ProfitCenter,
          profitCenterGroup: tempObj.ProfitCenterGroup,
          profitCenterName: tempObj.ProfitCenterName,
          // costCenter: `${tempObj["CostCenter"]} - ${tempObj["CostCenterName"]}`,
          // createdOn: moment(tempObj.CreatedOn).format("DD MMM YYYY"),
          // changedOn: moment(tempObj.LastChange).format("DD MMM YYYY"),
          // changedBy: tempObj["ChangedBy"],
          createdBy: tempObj.CreatedBy,
          // division: tempObj.Division,
          // oldMaterialNumber: tempObj.OldMaterialNumber,
          // labOffice: tempObj.LabOffice,
          // transportationGroup: tempObj.TrnsportGroup,
          segment: tempObj.Segment,
            // id: uuidv4(),
            // materialNumber: tempObj["MaterialNo"],
            // materialType: `${tempObj["Materialtype"]} - ${tempObj["MaterialTypeDesc"]}`,
            // materialDesc: tempObj["MaterialDescrption"],
            // materialGroup: `${tempObj["MaterialGroup"]} - ${tempObj["materialGroupDesc"]}`,
            // plant:
            //   tempObj["Plant"] !== ""
            //     ? `${tempObj["Plant"]} - ${tempObj["Plantname"]}`
            //     : "Not Available",
            // createdOn: moment(tempObj.CreatedOn).format("DD MMM YYYY"),
            // changedOn: moment(tempObj.LastChange).format("DD MMM YYYY"),
            // changedBy: tempObj["ChangedBy"],
            // createdBy: tempObj.CreatedBy,
          };
          rows.push(tempRow);
        }
      }
      // rows.sort(
      //   (a, b) =>
      //     moment(a.createdOn, "DD MMM YYYY HH:mm") -
      //     moment(b.createdOn, "DD MMM YYYY HH:mm")
      // );
      console.log("rowsss", rows);
      setRmDataRows(rows.reverse());
      setTableLoading(false);
      setroCount(rows.length);
      setCount(data?.body?.count);
    };
    let hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/data/getProfitCentersBasedOnAdditionalParams`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  /* Setting Default Dates */
  const presentDate = new Date();
  const backDate = new Date();
  backDate.setDate(backDate.getDate() - 15);

  const [date, setDate] = useState([backDate, presentDate]);
  const [date1, setDate1] = useState([backDate, presentDate]);

  const handleDate = (e) => {
    // if (e !== null) setDate(e.reverse());
    if (e !== null) {
      var createdOn = e.reverse();
      dispatch(
        commonFilterUpdate({
          module: "ProfitCenter",
          filterData: {
            ...pcSearchForm,
            createdOn: createdOn,
          },
        })
      );
    }
  };

  const handleDate1 = (e) => {
    if (e !== null) setDate1(e.reverse());
  };

  const handleSnackBarClickaccept = () => {
    setOpenSnackBaraccept(true);
  };

  const handleSnackBarCloseaccept = (event, reason) => {
    if (reason === "clickaway") {
      return;
    }

    setOpenSnackBaraccept(false);
  };

  const handleUserName = (e) => {
    setUserName(e.target.value);
  };

  useEffect(() => {
    if ((parseInt(page) + 1) * parseInt(pageSize) >= parseInt(skip) + 1000) {
      getFilter(skip + 1000);
      setSkip((prev) => prev + 1000);
    }
  }, [page, pageSize]);
  // Get Filter Data

  console.log("pcsearchform", pcSearchForm);

  const getFilter = (fetchSkip) => {
    setTableLoading(true);
    if (!fetchSkip) {
      setPage(0);
      setPageSize(10);
      setSkip(0);
    }

    let payload = {
      controllingArea: pcSearchForm?.controllingArea?.code ?? "",
      profitCenter: pcSearchForm?.profitCenter ?? "",
      profitCenterName: pcSearchForm?.profitCenterName ?? "",
      createdBy: pcSearchForm?.createdBy ?? "",
      segment: pcSearchForm?.segment?.code ?? "",
      profitCenterGroup: pcSearchForm?.profitCenterGroup?.code ?? "",
      top: 1000,
      skip: fetchSkip ?? 0,
    };
    const hSuccess = (data) => {
      console.log("searchdata", data);
      var rows = [];
      for (let index = 0; index < data?.body?.list?.length; index++) {
        var tempObj = data?.body.list[index];
        // console.log("hshshsh", tempObj);
        // if (tempObj["MaterialNo"]) {
        var tempRow = {
          id: uuidv4(),
          description: tempObj.Description,
          controllingArea: tempObj.ControllingArea,
          companyCode: tempObj.CompanyCode,
          profitCenter: tempObj.ProfitCenter,
          profitCenterGroup: tempObj.ProfitCenterGroup,
          profitCenterName: tempObj.ProfitCenterName,
          // costCenter: `${tempObj["CostCenter"]} - ${tempObj["CostCenterName"]}`,
          // createdOn: moment(tempObj.CreatedOn).format("DD MMM YYYY"),
          // changedOn: moment(tempObj.LastChange).format("DD MMM YYYY"),
          // changedBy: tempObj["ChangedBy"],
          createdBy: tempObj.CreatedBy,
          // division: tempObj.Division,
          // oldMaterialNumber: tempObj.OldMaterialNumber,
          // labOffice: tempObj.LabOffice,
          // transportationGroup: tempObj.TrnsportGroup,
          segment: tempObj.Segment,
        };
        rows.push(tempRow);
        // }
      }
      rows.sort(
        (a, b) =>
          moment(a.createdOn, "DD MMM YYYY HH:mm") -
          moment(b.createdOn, "DD MMM YYYY HH:mm")
      );
      setRmDataRows(rows.reverse());
      setTableLoading(false);
      setroCount(rows.length);
      setCount(data?.body?.count);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/data/getProfitCentersBasedOnAdditionalParams`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  useEffect(() => {
    getFilter();
    // functions_PresetFilter.getFilterPresets();
  }, []);

  const [userList, setUserList] = useState([]);

  const moduleFilterData = [
    {
      type: "singleSelect",
      filterName: "company",
      // filterData: masterData?.companyCode,
      filterTitle: "Company",
    },
    {
      type: "singleSelect",
      filterName: "vendor",
      filterData: vendorDetailsSet,
      filterTitle: "Supplier",
    },
    {
      type: "text",
      filterName: "poNum",
      filterTitle: "PO Number",
    },
    {
      type: "autoComplete",
      filterName: "createdBy",
      filterData: userList,
      filterTitle: "Created By",
    },
    {
      type: "singleSelectKV",
      filterName: "returnType",
      filterData: {
        "Debit Note": "Debit Note",
        Replacement: "Replacement",
      },
      filterTitle: "Return Type",
    },
    {
      type: "singleSelect",
      filterName: "plant",
      filterData: plantCodeSet,
      filterTitle: "Plant",
    },
    {
      type: "dateRange",
      filterName: "createdOn",
      filterTitle: "Return Request Date",
    },
  ];

  const handleSnackbarClose = () => {
    setopenSnackbar(false);
  };
  const handleReject = () => {
    setMessageDialogTitle("Success");
    setMessageDialogMessage("Comment Posted");
    setMessageDialogSeverity("success");
    handleMessageDialogClickOpen();
  };
  const handleAccept = () => {
    setMessageDialogTitle("Success");
    setMessageDialogMessage("Comment Posted");
    setMessageDialogSeverity("success");
    handleMessageDialogClickOpen();
  };
  const handleOpendialog = (id) => {
    setID(id);
    fetchPOHeader(id);
    setOpendialog(true);
  };
  const handleClosedialog = () => {
    setOpendialog(false);
  };
  const handleOpendialog2 = (id) => {
    setID(id);
    fetchPOHeader(id);
    setOpendialog2(true);
  };
  const handleClosedialog2 = () => {
    setOpendialog2(false);
  };
  const handleOpendialog3 = (id) => {
    setOpendialog3(true);
    setConfirmingid(id);
    fetchPOHeader(id);
  };
  const handleClosedialog3 = () => {
    setOpendialog3(false);
  };
  const handleOpenforwarddialog = () => {
    setOpenforwarddialog(true);
  };
  const handleCloseforwarddialog = () => {
    setOpenforwarddialog(false);
  };
  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };
  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };

  const handleClose_Preset = () => {
    setPresetName("");
    setAnchorEl(null);
  };
  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };
  const handlePageSizeChange = (event) => {
    const newPageSize = event.target.value;
    setPageSize(newPageSize);
    setPage(0);
    setSkip(0);
  };

  const [presets, setPresets] = useState(null);
  const [presetName, setPresetName] = useState(null);

  const handleClear = () => {
    dispatch(commonFilterClear({ module: "ProfitCenter" }));
  };
  const onRowsSelectionHandler = (ids) => {
    //Selected Columns stored here
    const selectedRowsData = ids.map((id) =>
      rmDataRows.find((row) => row.id === id)
    );
    var compCodes = selectedRowsData.map((row) => row.company);
    var companySet = new Set(compCodes);
    var vendors = selectedRowsData.map((row) => row.vendor);
    var vendorSet = new Set(vendors);
    var paymentTerms = selectedRowsData.map((row) => row.paymentTerm);
    var paymentTermsSet = new Set(paymentTerms);
    if (selectedRowsData.length > 0) {
      if (companySet.size === 1) {
        if (vendorSet.size === 1) {
          if (paymentTermsSet.size !== 1) {
            setDisableButton(true);
            setMessageDialogTitle("Error");
            setMessageDialogMessage(
              "Invoice cannot be generated for vendors with different payment terms"
            );
            setMessageDialogSeverity("danger");
            handleMessageDialogClickOpen();
          } else setDisableButton(false);
        } else {
          setDisableButton(true);
          setMessageDialogTitle("Error");
          setMessageDialogMessage(
            "Invoice cannot be generated for multiple suppliers"
          );
          setMessageDialogSeverity("danger");
          handleMessageDialogClickOpen();
        }
      } else {
        setDisableButton(true);
        setMessageDialogTitle("Error");
        setMessageDialogMessage(
          "Invoice cannot be generated for multiple companies"
        );
        setMessageDialogSeverity("danger");
        handleMessageDialogClickOpen();
      }
    } else {
      setDisableButton(true); //Enable the Create E-Invoice button when at least one row is selected and no two companys or vendors are same
    }
    setSelectedRow(ids); //Setting the ids(PO Numbers) of selected rows
    setSelectedDetails(selectedRowsData); //Setting the entire data of a selected row
  };

  function refreshPage() {
    getFilter();
  }

  const [company, setCompany] = useState([]);
  const [Companyid, setCompanyid] = useState([]);

  const [open, setOpen] = useState(false);
  const [matAnchorEl, setMatAnchorEl] = useState(null);
  const [materialDetails, setMaterialDetails] = useState(null);
  const [itemDataRows, setItemDataRows] = useState([]);

  const handlePODetailsClick = (event) => {
    setOpendialog3(true);
  };

  const matOpen = Boolean(matAnchorEl);
  const popperId = matOpen ? "simple-popper" : undefined;

  const handleClickOpen = () => {
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
  };

  const [poNum, setPONum] = useState(null);
  const fetchPOHeader = (id) => {
    var formData = new FormData();
    if (id) formData.append("extReturnId", id);
    const hSuccess = (data) => {
      if (data) {
        setPoHeader(data);
        setPONum(data[0]["poNumber"] ?? "");
      }
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_Returns}/returnsHeader/getReturnsPreview`,
      "postformdata",
      hSuccess,
      hError,
      formData
    );
  };

  const [id, setID] = useState("");
  const columns = [
    {
      field: "profitCenter",
      headerName: "Profit Center",
      editable: false, //dd
      flex: 1,
    },
    {
      field: "profitCenterName",
      headerName: "Profit Center Name",
      editable: false, //text
      flex: 1,
    },
    {
      field: "controllingArea",
      headerName: "Controlling Area",
      editable: false, //dd
      flex: 1,
    },
    {
      field: "segment",
      headerName: "Segment", //dd
      editable: false,
      flex: 1,
    },
    {
      field: "profitCenterGroup",
      headerName: "Profit Center Group", //dd
      editable: false,
      flex: 1,
    },
    {
      field: "createdBy",
      headerName: "Created By",
      editable: false,
      flex: 1,
    },
  ];

  const dynamicFilterColumns = selectedOptions
    .map((option) => {
      const field = titleToFieldMapping[option]; // Get the corresponding field from the mapping
      if (!field) {
        return null; // Handle the case when the field doesn't exist in the mapping
      }
      return {
        field: field, // Use the field name from the mapping
        headerName: option,
        editable: false,
        flex: 1,
      };
    })
    .filter((column) => column !== null); // Remove any null columns
  const allColumns = [...columns, ...dynamicFilterColumns];
  const functions_ExportAsExcel = {
    convertJsonToExcel: () => {
      let excelColumns = [];
      allColumns.forEach((item) => {
        if (item.headerName.toLowerCase() !== "action" && !item.hide) {
          excelColumns.push({ header: item.headerName, key: item.field });
        }
      });
      saveExcel({
        fileName: `Profit Center Data-${moment(presentDate).format("DD-MMM-YYYY")}`,
        columns: excelColumns,
        rows: rmDataRows,
      })
    },
    button: () => {
      return (
        <Button
          sx={{
            textTransform: "capitalize",
            position: "absolute",
            right: 0,
            top: 0,
          }}
          onClick={() => functions_ExportAsExcel.convertJsonToExcel()}
        >
          Download
        </Button>
      );
    },
  };
  const capitalize = (str) => {
    const arr = str.split(" ");
    for (var i = 0; i < arr.length; i++) {
      arr[i] = arr[i].charAt(0) + arr[i].slice(1).toLowerCase();
    }

    const str2 = arr.join(" ");
    return str2;
    //  })
  };
  let ref_elementForExport = useRef(null);
  // let exportAsPicture = () => {
  //   setTimeout(() => {
  //     captureScreenShot("Material-Single");
  //   }, 100);
  // };
  const handleDownloadTemplate = async () => {
    var downloadPayload = selectedMassChangeRowData.map((x) => {
      return {
        profitCenter: x.profitCenter,
        controllingArea: x.controllingArea,
      };
    });
    console.log("downloadPayload", downloadPayload);
    let hSuccess = (response) => {
      setIsLoading(false);
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");
      link.href = href;
      link.setAttribute("download", `Profit Center_Mass Change.xls`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
      handleMessageDialogClickOpen();
      setMessageDialogTitle("Success");
      setMessageDialogMessage(
        `Profit Center_Mass Change.xls has been downloaded successfully`
      );
      setMessageDialogSeverity("success");
    };
    let hError = (error) => {
      if (error.message) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(`${error.message}`);
        setMessageDialogSeverity("danger");
      }
    };
    doAjax(
      `/${destination_ProfitCenter}/excel/downloadExcelWithData`,
      "postandgetblob",
      hSuccess,
      hError,
      downloadPayload
    );
  };
  const handleDownloadCreate = async () => {
    var downloadPayload = selectedMassChangeRowData.map((x) => {
      return {
        profitCenter: x.profitCenter,
        controllingArea: x.controllingArea,
      };
    });
    console.log("downloadPayload", downloadPayload);
    let hSuccess = (response) => {
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");
      link.href = href;
      link.setAttribute("download", `Profit Center_Mass Create.xls`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
      handleMessageDialogClickOpen();
      setMessageDialogTitle("Success");
      setMessageDialogMessage(
        `Profit Center_Mass Create.xls has been downloaded successfully`
      );
      setMessageDialogSeverity("success");
    };
    let hError = (error) => {
      if (error.message) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(`${error.message}`);
        setMessageDialogSeverity("danger");
      }
    };
    doAjax(
      `/${destination_ProfitCenter}/excel/downloadExcel`,
      "getblobfile",
      hSuccess,
      hError,
      // downloadPayload
    );
  };
  const handleClickCreate = (option, index) => {
    // dispatch(setHandleMassMode("Change"));
    if (index !== 0) {
      setSelectedIndexCreate(index);
      setOpenButtonCreate(false);
      if (index === 1) {
        handleCreateSingleWithCopy();
      } else if (index === 2) {
        handleCreateSingleWithoutCopy();
      }
    }
  };
  const handleCreateMultiple = () => {
    setEnableDocumentUpload(true);
    dispatch(setHandleMassMode("Create"));
  };
  const handleToggle = () => {
    setOpenButton((prevOpen) => !prevOpen);
  };
  const handleCloseButton = (event) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }
    // setOpenButton(false);
    setOpenButton((prevOpen) => !prevOpen);
  };

  const handleToggleChange = () => {
    setOpenButtonChange((prevOpen) => !prevOpen);
  };
  const handleToggleCreate = () => {
    setOpenButtonCreate((prevOpen) => !prevOpen);
  };

  const handleCloseButtonChange = (event) => {
    if (
      anchorRefChange.current &&
      anchorRefChange.current.contains(event.target)
    ) {
      return;
    }
    //setOpenButtonChange(false);
    setOpenButtonChange((prevOpen) => !prevOpen);
  };
  const handleCloseButtonCeateMultiple =(event) =>{
      if (
      anchorRefChange.current &&
      anchorRefChange.current.contains(event.target)
    ) {
      return;
    }
    //setOpenButtonChange(false);
    setOpenButton((prevOpen) => !prevOpen);
  }
  const handleClick = (option, index) => {
    if (index !== 0) {
      setSelectedIndex(index);
      setOpenButton(false);
      if (index === 1) {
        handleCreateMultiple();
      } else if (index === 2) {
        handleDownloadCreate();
      }
    }
  };
  const handleClickChange = (option, index) => {
    if (index !== 0) {
      setSelectedIndexChange(index);
      setOpenButtonChange(false);
      if (index === 1) {
        handleChangeMultiple();
      } else if (index === 2) {
        if (selectedRows.length > 0) {
          console.log("selectedRows",selectedRows);
          setIsLoading(true);
          setIsCheckboxSelected(false);
          handleDownloadTemplate();
        } else {
          // Handle the case when no rows are selected (e.g., show a message)
          console.log("Please select at least one row to download Excel.");
        }
        //handleDownloadTemplate();
      }
    }
  };
  const handleChangeMultiple = () => {
    setEnableDocumentUpload(true);
    dispatch(setHandleMassMode("Change"));
  };

  return (
    <>
      {
        isLoading===true?
        <LoadingComponent/>
        :
        <div ref={ref_elementForExport}>
      <ReusableDialog
        dialogState={openMessageDialog}
        openReusableDialog={handleMessageDialogClickOpen}
        closeReusableDialog={handleMessageDialogClose}
        dialogTitle={messageDialogTitle}
        dialogMessage={messageDialogMessage}
        handleDialogConfirm={handleMessageDialogClose}
        dialogOkText={"OK"}
        // handleExtraButton={handleMessageDialogNavigate}
        dialogSeverity={messageDialogSeverity}
      />
      <div style={{ ...outermostContainer, backgroundColor: "#FAFCFF" }}>
        <Stack spacing={1}>
          {/* Information */}
          <Grid container sx={outermostContainer_Information}>
            <Grid item md={5} sx={outerContainer_Information}>
              <Typography variant="h3">
                <strong>Profit Center</strong>
              </Typography>
              <Typography variant="body2" color="#777">
                This view displays the list of Profit Centers
              </Typography>
            </Grid>
            <Grid item md={7} sx={{ display: "flex" }}>
              <Grid
                container
                direction="row"
                justifyContent="flex-end"
                alignItems="center"
                spacing={0}
              >
                <SearchBar
                  title="Search for multiple Profit Center numbers separated by comma"
                  handleSearchAction={()=>getMaterialNoGlobalSearch()}
                  module="ProfitCenter"
                  keyName="number"
                  message={"Search Profit Center "}
                  clearSearchBar={clearSearchBar}
                />
                <Tooltip title="Reload">
                  <IconButton sx={iconButton_SpacingSmall}>
                    <Refresh
                      sx={{
                        "&:hover": {
                          transform: "rotate(360deg)",
                          transition: "0.9s",
                        },
                      }}
                      onClick={refreshPage}
                    />
                  </IconButton>
                </Tooltip>
                {/* <Tooltip title="Export">
                  <IconButton sx={iconButton_SpacingSmall}>
                    <IosShare onClick={exportAsPicture} />
                  </IconButton>
                </Tooltip> */}
                <Tooltip title="Export Table">
                  <IconButton
                    sx={iconButton_SpacingSmall}
                    onClick={functions_ExportAsExcel.convertJsonToExcel}
                  >
                    <ReusableIcon iconName={"IosShare"} />
                  </IconButton>
                </Tooltip>
              </Grid>
            </Grid>
          </Grid>
          <Grid container sx={container_filter}>
            <Grid item md={12}>
              <Accordion className="filter-accordian">
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  aria-controls="panel1a-content"
                  id="panel1a-header"
                  sx={{
                    minHeight: "2rem !important",
                    margin: "0px !important",
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: "700",
                    }}
                  >
                    Search Profit Center
                  </Typography>
                </AccordionSummary>
                <AccordionDetails sx={{ padding: "0.5rem 1rem 0.5rem" }}>
                  <Grid
                    container
                    rowSpacing={1}
                    spacing={2}
                    justifyContent="space-between"
                    alignItems="center"
                    // sx={{ marginBottom: "0.5rem" }}
                  >
                    <Grid
                      container
                      spacing={1}
                      sx={{ padding: "0rem 1rem 0.5rem" }}
                    >
                      <Grid item md={2}>
                        <Typography sx={font_Small}>Profit Center Name</Typography>
                        <FormControl size="small" fullWidth>
                          <TextField
                            sx={{ fontSize: "12px !important" }}
                            fullWidth
                            size="small"
                            value={pcSearchForm?.profitCenterName}
                            onChange={handleProfitCenterName}
                            placeholder="Enter Profit Center"
                          />
                        </FormControl>
                      </Grid>
                      <Grid item md={2}>
                        <Typography sx={font_Small}>
                          Controlling Area
                        </Typography>
                        <FormControl size="small" fullWidth>
                          <Autocomplete
                            sx={{ height: "31px" }}
                            fullWidth
                            size="small"
                            value={pcSearchForm?.controllingArea}
                            onChange={handleControllingAreaChange}
                            options={dropDownData?.ControllingArea ?? []}
                            getOptionLabel={(option) => {
                              if (option?.code) return `${option?.code}-${option?.desc}` ?? "";
                              else return "";
                            }}
                            renderOption={(props, option) => (
                              <li {...props}>
                                <Typography style={{ fontSize: 12 }}>
                                  {`${option?.code}-${option?.desc}`}
                                </Typography>
                              </li>
                            )}
                            renderInput={(params) => (
                              <TextField
                                sx={{ fontSize: "12px !important" }}
                                {...params}
                                variant="outlined"
                                placeholder="Select Controlling Area"
                              />
                            )}
                          />
                        </FormControl>
                      </Grid>
                      <Grid item md={2}>
                        <Typography sx={font_Small}>Segment</Typography>
                        <FormControl fullWidth size="small">
                          <Autocomplete
                            sx={{ height: "31px" }}
                            fullWidth
                            size="small"
                            onChange={handleSegment}
                            // onChange={(e) => handleMatTypeChange(e)}
                            options={dropDownData?.Segment ?? []}
                            getOptionLabel={(option) => {
                              if (option?.code)
                                return (
                                  `${option?.code} - ${option?.desc}` ?? ""
                                );
                              else return "";
                            }}
                            value={pcSearchForm?.segment}
                            renderOption={(props, option) => (
                              <li {...props}>
                                <Typography style={{ fontSize: 12 }}>
                                  {`${option?.code} - ${option?.desc}`}
                                </Typography>
                              </li>
                            )}
                            renderInput={(params) => (
                              <TextField
                                sx={{ fontSize: "12px !important" }}
                                {...params}
                                variant="outlined"
                                placeholder="Select Segment"
                              />
                            )}
                          />
                        </FormControl>
                      </Grid>
                      <Grid item md={2}>
                        <Typography sx={font_Small}>
                          Profit Center Group
                        </Typography>
                        <FormControl fullWidth size="small">
                          <Autocomplete
                            sx={{ height: "31px" }}
                            fullWidth
                            size="small"
                            onChange={handleGroup}
                            value={pcSearchForm?.profitCenterGroup}
                            options={dropDownData?.ProfitCtrGroupSearch ?? []}
                            getOptionLabel={(option) => {
                              if (option?.code)
                                return (
                                  `${option?.code} - ${option?.desc}` ?? ""
                                );
                              else return "";
                            }}
                            renderOption={(props, option) => (
                              <li {...props}>
                                <Typography style={{ fontSize: 12 }}>
                                  {`${option?.code} - ${option?.desc}`}
                                </Typography>
                              </li>
                            )}
                            renderInput={(params) => (
                              <TextField
                                sx={{ fontSize: "12px !important" }}
                                {...params}
                                variant="outlined"
                                placeholder="Select Profit Center Group"
                              />
                            )}
                          />
                        </FormControl>
                      </Grid>
                    </Grid>
                    <Grid
                      container
                      sx={{ flexDirection: "row", padding: "0rem 1rem 0.5rem" }}
                      gap={1}
                    >
                      {selectedOptions.map((option, i) => {
                        // if (option !== "Created Date" || option !== "Plant") {
                        return (
                          <Grid item>
                            <Stack>
                              <Typography sx={{ fontSize: "12px" }}>
                                {option}
                              </Typography>
                              <Autocomplete
                                sx={font_Small}
                                size="small"
                                key={option[i]}
                                options={dynamicOptions ?? []}
                                getOptionLabel={(option, i) =>
                                  `${option[i]?.code} - ${option[i]?.desc}`
                                }
                                placeholder={`Enter ${option}`}
                                value={filterFieldData[option]}
                                onChange={(event, newValue) =>
                                  setFilterFieldData({
                                    ...filterFieldData,
                                    [option]: newValue,
                                  })
                                }
                                renderInput={(params) => (
                                  <TextField
                                    sx={{ fontSize: "12px !important" }}
                                    {...params}
                                    size="small"
                                    placeholder={`Enter ${option}`}
                                    variant="outlined"
                                    // sx={font_Small}
                                  />
                                )}
                              />
                            </Stack>
                          </Grid>
                        );
                        // } if (option === "Created Date") {
                        //   return (
                        //     <Stack>

                        //       <DatePicker/>

                        //     </Stack>
                        //   )
                        // }
                      })}
                      {/* //clear search button according to UX */}
                      {/* <Grid
                          item
                          style={{
                            width:"100%",
                            display: "flex",
                            justifyContent: "space-around",
                          }}
                        >
                          <Button
                            
                            sx={{ fontSize: "12px",  width:"129px", backgroundColor:" #7575751A", color:"#757575" }}
                            onClick={handleClear}
                          >
                            Clear
                          </Button>

                          <Button
                            
                            sx={{ ...button_Marginleft, fontSize: "12px",  width:"129px", backgroundColor:"#F7F5FF"  }}
                          // onClick={getFilter}
                          >
                            Search
                          </Button>
                        </Grid> */}
                    </Grid>
                  </Grid>
                  <Grid
                    container
                    style={{
                      display: "flex",
                      justifyContent: "flex-end",
                    }}
                  >
                    <Grid
                      item
                      style={{
                        display: "flex",
                        justifyContent: "space-around",
                      }}
                    >
                      <Button
                        variant="outlined"
                        sx={button_Outlined}
                        onClick={handleClear}
                      >
                        Clear
                      </Button>
                      <Button
                        variant="contained"
                        sx={{ ...button_Primary, ...button_Marginleft }}
                        onClick={() => getFilter()}
                      >
                        Search
                      </Button>
                    </Grid>
                  </Grid>
                </AccordionDetails>
              </Accordion>
            </Grid>
          </Grid>

          <Grid item sx={{ position: "relative" }}>
            <Stack>
              <ReusableTable
                isLoading={tableLoading}
                module={"ProfitCenter"}
                width="100%"
                title={"List of Profit Centers (" + roCount + ")"}
                rows={rmDataRows}
                columns={allColumns}
                page={page}
                pageSize={10}
                rowCount={count ?? rmDataRows?.length ?? 0}
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                getRowIdValue={"id"}
                hideFooter={true}
                checkboxSelection={true}
                disableSelectionOnClick={true}
                status_onRowSingleClick={true}
                onRowsSelectionHandler={handleSelectionModelChange}
                callback_onRowSingleClick={(params) => {
                  console.log(params, "params");
                  const profitCenter = params.row.profitCenter; // Adjust this based on your data structure
                  navigate(
                    `/masterDataCockpit/profitCenter/displayProfitCenter/${profitCenter}`,
                    {
                      state: params.row,
                    }
                  );
                }}
                // setShowWork={setShowWork}
                stopPropagation_Column={"action"}
                status_onRowDoubleClick={true}
                showCustomNavigation={true}
              />
              {/* {viewDetailpage && <SingleMaterialDetail />} */}
            </Stack>
          </Grid>
          {/* {
            showBtmNav && */}
            {checkIwaAccess(iwaAccessData, "Profit Center", "CreatePC") && 
          userData?.role === "Super User" ||  userData?.role === "Finance" ? 
          (
          <Paper
            sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
            elevation={2}
          >
            <BottomNavigation
              className="container_BottomNav"
              showLabels
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                gap: 1,
              }}
              value={value}
              onChange={(newValue) => {
                setValue(newValue);
              }}
            >

<ButtonGroup
                variant="contained"
                ref={anchorRefCreate}
                aria-label="split button"
              >
                <Button
                  size="small"
                  variant="contained"
                  onClick={() => handleClickCreate(optionsCreateSingle[0], 0)}
                  // onClick={handleDialogClickOpen}
                >
                  {optionsCreateSingle[0]}
                </Button>
                <Button
                  size="small"
                  aria-controls={
                    openButtonCreate ? "split-button-menu" : undefined
                  }
                  aria-expanded={openButtonCreate ? "true" : undefined}
                  aria-label="select action"
                  aria-haspopup="menu"
                  onClick={handleToggleCreate}
                >
                  <ReusableIcon
                    iconName={"ArrowDropUp"}
                    iconColor={"#FFFFFF"}
                  />
                </Button>
              </ButtonGroup>
              <Popper
                sx={{
                  zIndex: 1,
                }}
                open={openButtonCreate}
                anchorEl={anchorRefCreate.current}
                placement={"top-end"}
              >
                <Paper style={{ width: anchorRefCreate.current?.clientWidth }}>
                  <ClickAwayListener onClickAway={handleCloseButtonCreate}>
                    <MenuList id="split-button-menu" autoFocusItem>
                      {optionsCreateSingle.slice(1).map((option, index) => (
                        <MenuItem
                          key={option}
                          selected={index === selectedIndexCreate - 1}
                          onClick={() => handleClickCreate(option, index + 1)}
                        >
                          {option}
                        </MenuItem>
                      ))}
                    </MenuList>
                  </ClickAwayListener>
                </Paper>
              </Popper>

              {/* <Button
                size="small"
                variant="contained"
                // onClick={handleClickOpenCreateInvoice}
                onClick={handleDialogClickOpen}
              >
                Create Single
              </Button> */}

              <Dialog
                // fullWidth={fullWidth}
                // maxWidth={maxWidth}

                open={dialogOpen}
                onClose={handleDialogClose}
                sx={{
                  "&::webkit-scrollbar": {
                    width: "1px",
                  },
                }}
              >
                <DialogTitle
                  sx={{
                    justifyContent: "space-between",
                    alignItems: "center",
                    height: "max-content",
                    padding: ".5rem",
                    paddingLeft: "1rem",
                    backgroundColor: "#EAE9FF40",
                    // borderBottom: "1px solid grey",
                    display: "flex",
                  }}
                >
                  <Typography variant="h6">New Profit Center</Typography>

                  <IconButton
                    sx={{ width: "max-content" }}
                    onClick={handleDialogClose}
                    children={<CloseIcon />}
                  />
                </DialogTitle>
                <DialogContent sx={{ padding: ".5rem 1rem" }}>
                  <Grid container spacing={1}>
                  <Grid
                      item
                      md={6}
                      sx={{ width: "100%", marginTop: ".5rem" }}
                    >
                      <Typography>
                        Controlling Area
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                      <FormControl
                        fullWidth
                        sx={{ margin: ".5em 0px", minWidth: "250px" }}
                      >
                        <Autocomplete
                          sx={{ height: "31px" }}
                          fullWidth
                          size="small"
                          onChange={(e, value) => {
                            setNewControllingArea(value);
                            getCompanyCodeBasedOnControllingArea(value);
                            getProfitCenterGroup(value);
                            getCompanyCodeBasedOnControllingAreaCopy(value);
                          }}
                          options={dropDownData?.ControllingArea ?? []}
                          getOptionLabel={(option) => {
                            if (option?.code) return `${option?.code}-${option?.desc}` ?? "";
                            else return "";
                          }}
                          value={pcSearchForm?.costCenterCategory}
                          renderOption={(props, option) => (
                            <li {...props}>
                              <Typography style={{ fontSize: 12 }}>
                                {`${option?.code}-${option?.desc}`}
                              </Typography>
                            </li>
                          )}
                          renderInput={(params) => (
                            <TextField
                              sx={{ fontSize: "12px !important" }}
                              {...params}
                              variant="outlined"
                              placeholder="SELECT CONTROLLING AREA"
                              error={controllingAreaValid}
                            />
                          )}
                        />
                      </FormControl>
                      {controllingAreaValid && (
                        <Typography variant="caption" color="error">
                          Please Select a Controlling Area.
                        </Typography>
                      )}
                    </Grid>
                    <Grid
                      item
                      md={6}
                      sx={{
                        width: "100%",
                        marginTop: ".5rem",
                        // marginRight: "5rem",
                      }}
                    >
                      <Typography>
                        Profit Center
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                      <FormControl
                        fullWidth
                        sx={{ margin: ".5em 0px", minWidth: "250px", flexDirection:"row" }}
                      >
                      <Grid md={2}>
                        <TextField
                        sx={{ fontSize: "12px !important", height: "31px" }}
                        value="P"
                        fullWidth
                        size="small"
                        editable={false}
                        />
                      </Grid>
                      <Grid md={5}>
                      <Autocomplete
                          sx={{ height: "42px" }}
                          required="true"
                          size="small"
                          onChange={(e, value) => {
                            setNewComapnyCode(value);
                          }}
                          options={dropDownData?.CompCode ?? []}
                          getOptionLabel={(option) => `${option?.code}-${option?.desc}`}
                          // value={rmSearchForm?.plant}
                          renderOption={(props, option) => (
                            <li {...props}>
                              <Typography style={{ fontSize: 12 }}>
                                {`${option?.code}-${option?.desc}`}
                              </Typography>
                            </li>
                          )}
                          // error={newControllingArea === "" ? true : false}
                          renderInput={(params) => (
                            <TextField
                              sx={{ fontSize: "12px !important" }}
                              {...params}
                              variant="outlined"
                              placeholder="SELECT COMPANY CODE"
                            />
                          )}
                        />
                        </Grid>
                        <Grid md={5}>
                        <TextField
                          // className={classes.input}
                          sx={{ fontSize: "12px !important", height: "31px" }}
                          fullWidth
                          size="small"
                          //value={pcSearchForm?.changedBy}
                          value={newProfitCenterName}
                          onChange={(e) => {
                            let newValue = e.target.value;
                            
                            if (newValue.length > 0 && newValue[0] === " ") {
                              setNewProfitCenterName(newValue.trimStart());
                            } else {
                              let profitCenterUpperCase = newValue.toUpperCase();
                              setNewProfitCenterName(profitCenterUpperCase);
                            }
                            
                          }}
                          inputProps={{
                            maxLength: 5,
                            style: { textTransform: "uppercase" },
                          }}
                          placeholder="Enter Profit Center"
                          // error={newCostCenterName === "" ? true : false}
                          required={true}
                          //error={profitCenterValid}
                          error={newProgfitCenterValid}
                        />
                        {newProgfitCenterValid && (
                        <Typography variant="caption" color="error">
                          Cost Center must be 10 digits
                        </Typography>
                      )}
                        </Grid>
                      </FormControl>
                      {profitCenterValid && (
                        <Typography variant="caption" color="error">
                          Please enter a Profit Center.
                        </Typography>
                      )}
                    </Grid>
                    {isValidationError && (
                      <Grid>
                        <Typography style={{ color: "red" }}>
                            Please Enter Mandatory Fields
                        </Typography>
                      </Grid>
                    )}
                    
                    {checkValidationProfitCenter && (
                      <Grid>
                        <Typography style={{ color: "red" }}>
                          *The Profit Center with Controlling Area already
                          exists. Please enter different Profit Center or
                          Controlling Area
                        </Typography>
                      </Grid>
                    )}
                  </Grid>
                </DialogContent>

                <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
                  <Button
                    sx={{ width: "max-content", textTransform: "capitalize" }}
                    onClick={handleDialogClose}
                  >
                    Cancel
                  </Button>
                  <Button
                    className="button_primary--normal"
                    type="save"
                    onClick={handleDialogProceed}
                    variant="contained"
                  >
                    Proceed
                  </Button>
                </DialogActions>
              </Dialog>
              <Dialog
                // fullWidth={fullWidth}
                // maxWidth={maxWidth}

                open={dialogOpenCreate}
                onClose={handleDialogCloseCreate}
                sx={{
                  "&::webkit-scrollbar": {
                    width: "1px",
                  },
                }}
              >
                <DialogTitle
                  sx={{
                    justifyContent: "space-between",
                    alignItems: "center",
                    height: "max-content",
                    padding: ".5rem",
                    paddingLeft: "1rem",
                    backgroundColor: "#EAE9FF40",
                    // borderBottom: "1px solid grey",
                    display: "flex",
                  }}
                >
                  <Typography variant="h6">New Profit Center</Typography>

                  <IconButton
                    sx={{ width: "max-content" }}
                    onClick={handleDialogCloseCreate}
                    children={<CloseIcon />}
                  />
                </DialogTitle>
                <DialogContent sx={{ padding: ".5rem 1rem" }}>
                  <Grid container spacing={1}>
                  <Grid
                      item
                      md={6}
                      sx={{ width: "100%", marginTop: ".5rem" }}
                    >
                      <Typography>
                        Controlling Area
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                      <FormControl
                        fullWidth
                        sx={{ margin: ".5em 0px", minWidth: "250px" }}
                      >
                        <Autocomplete
                          sx={{ height: "31px" }}
                          fullWidth
                          size="small"
                          onChange={(e, value) => {
                            setNewControllingArea(value);
                            getCompanyCodeBasedOnControllingAreaCopy(value);
                            getProfitCenterGroup(value);
                          }}
                          options={dropDownData?.ControllingArea ?? []}
                          getOptionLabel={(option) => `${option?.code}-${option?.desc}`}
                          // value={pcSearchForm?.costCenterCategory}
                          renderOption={(props, option) => (
                            <li {...props}>
                              <Typography style={{ fontSize: 12 }}>
                                {`${option?.code}-${option?.desc}`}
                              </Typography>
                            </li>
                          )}
                          renderInput={(params) => (
                            <TextField
                              sx={{ fontSize: "12px !important" }}
                              {...params}
                              variant="outlined"
                              placeholder="SELECT CONTROLLING AREA"
                              error={controllingAreaValid}
                            />
                          )}
                        />
                      </FormControl>
                      {controllingAreaValid && (
                        <Typography variant="caption" color="error">
                          Please Select a Controlling Area.
                        </Typography>
                      )}
                    </Grid>
                    <Grid
                      item
                      md={6}
                      sx={{
                        width: "100%",
                        marginTop: ".5rem",
                        // marginRight: "5rem",
                      }}
                    >
                      <Typography>
                        Profit Center
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                      <FormControl
                        fullWidth
                        sx={{ margin: ".5em 0px", minWidth: "250px", flexDirection:"row" }}
                      >
                      <Grid md={2}>
                        <TextField
                        sx={{ fontSize: "12px !important", height: "31px" }}
                        value="P"
                        fullWidth
                        size="small"
                        editable={false}
                        />
                      </Grid>
                      <Grid md={5}>
                      <Autocomplete
                          sx={{ height: "42px" }}
                          required="true"
                          size="small"
                          onChange={(e, value) => {
                            setNewComapnyCodeCopy(value);
                          }}
                          options={dropDownData?.CompCode ?? []}
                          getOptionLabel={(option) => `${option?.code}-${option.desc}`}
                          // value={rmSearchForm?.plant}
                          renderOption={(props, option) => (
                            <li {...props}>
                              <Typography style={{ fontSize: 12 }}>
                                {`${option?.code}-${option.desc}`}
                              </Typography>
                            </li>
                          )}
                          // error={newControllingArea === "" ? true : false}
                          renderInput={(params) => (
                            <TextField
                              sx={{ fontSize: "12px !important" }}
                              {...params}
                              variant="outlined"
                              placeholder="SELECT COMPANY CODE"
                            />
                          )}
                        />
                        </Grid>
                        <Grid md={5}>
                        <TextField
                          // className={classes.input}
                          sx={{ fontSize: "12px !important", height: "31px" }}
                          fullWidth
                          size="small"
                          value={newProfitCenterName}
                          onChange={(e) => {
                            let newValue=e.target.value;
                            if (newValue.length > 0 && newValue[0] === " ") {
                              setNewProfitCenterName(newValue.trimStart());
                            } else {
                              let profitCenterUpperCase = newValue.toUpperCase();
                              setNewProfitCenterName(profitCenterUpperCase);
                            }
                          }}
                          inputProps={{
                            maxLength: 5,
                            style: { textTransform: "uppercase" },
                          }}
                          placeholder="Enter Profit Center"
                          // error={newCostCenterName === "" ? true : false}
                          required={true}
                          error={newPofitCenterValidWithCopy}
                        />
                        {newPofitCenterValidWithCopy && (
                          <Typography variant="caption" color="error">
                            Profit Center must be 10 digits
                          </Typography>
                        )}
                        
                        </Grid>
                      </FormControl>
                      
                      {profitCenterValid && (
                        <Typography variant="caption" color="error">
                          Please enter a Profit Center.
                        </Typography>
                      )}
                    </Grid>

                    <Divider sx={{ width: "100%", marginLeft:"2%" }} >
                    <b>Copy From</b>
                    </Divider>
                    <Grid
                      item
                      md={6}
                      sx={{ width: "100%", marginTop: ".5rem" }}
                    >
                      <Typography>
                        Controlling Area
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                      <FormControl fullWidth sx={{ margin: ".5em 0px" }}>
                        <Autocomplete
                          sx={{ height: "42px" }}
                          required="true"
                          size="small"
                          onChange={(e, value) => {
                            setNewControllingAreaCopyFrom(value);
                            getProfitCenterBasedOnControllingAreaCopy(value);
                          }}
                          options={dropdownData?.ControllingArea ?? []}
                          getOptionLabel={(option) => `${option?.code}-${option?.desc}`}
                          // value={rmSearchForm?.plant}
                          renderOption={(props, option) => (
                            <li {...props}>
                              <Typography style={{ fontSize: 12 }}>
                                {`${option?.code}-${option?.desc}`}
                              </Typography>
                            </li>
                          )}
                          error={newControllingArea === "" ? true : false}
                          renderInput={(params) => (
                            <TextField
                              sx={{ fontSize: "12px !important" }}
                              {...params}
                              variant="outlined"
                              placeholder="SELECT CONTROLLING AREA"
                            />
                          )}
                        />
                      </FormControl>
                    </Grid>
                    <Grid
                      item
                      md={6}
                      sx={{
                        width: "100%",
                        marginTop: ".5rem",
                      }}
                    >
                      <Typography>
                        Profit Center
                        <span style={{ color: "red" }}>*</span>
                      </Typography>

                      <FormControl
                        fullWidth
                        sx={{
                          margin: ".5em 0px",
                          minWidth: "250px",
                          flexDirection: "row",
                        }}
                      >
                        <Grid md={12}>
                          <Autocomplete
                            sx={{ height: "42px" }}
                            required="true"
                            size="small"
                            onChange={(e, value) => {
                              setNewProfitCenter(value);
                            }}
                            options={dropdownData?.ProfitCenter ?? []}
                            getOptionLabel={(option) => `${option?.code}-${option?.desc}`}
                            // value={rmSearchForm?.plant}
                            renderOption={(props, option) => (
                              <li {...props}>
                                <Typography style={{ fontSize: 12 }}>
                                  {`${option?.code}-${option?.desc}`}
                                </Typography>
                              </li>
                            )}
                            // error={newControllingArea === "" ? true : false}
                            renderInput={(params) => (
                              <TextField
                                sx={{ fontSize: "12px !important" }}
                                {...params}
                                variant="outlined"
                                placeholder="SELECT COMPANY CODE"
                              />
                            )}
                          />
                          
                        </Grid>
                      </FormControl>
                    </Grid>
                    
                    {checkValidationProfitCenter && (
                      <Grid>
                        <Typography style={{ color: "red" }}>
                          *The Profit Center with Controlling Area already
                          exists. Please enter different Profit Center or
                          Controlling Area
                        </Typography>
                      </Grid>
                    )}
                  </Grid>
                  {isValidationErrorwithCopy && (
                    <Grid>
                      <Typography style={{ color: "red" }}>
                        Please Enter Mandatory Fields
                      </Typography>
                    </Grid>
                  )}
                </DialogContent>

                <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
                  <Button
                    sx={{ width: "max-content", textTransform: "capitalize" }}
                    onClick={handleDialogCloseCreate}
                  >
                    Cancel
                  </Button>
                  <Button
                    className="button_primary--normal"
                    type="save"
                    onClick={handleDialogProceedWithCopy}
                    variant="contained"
                  >
                    Proceed
                  </Button>
                </DialogActions>
              </Dialog>             
              <ButtonGroup
                variant="contained"
                ref={anchorRef}
                aria-label="split button"
              >
                <Button
                  size="small"
                  variant="contained"
                  onClick={() => handleClick(options[0], 0)}
                  sx={{ cursor: "default" }}
                >
                  {options[0]}
                </Button>
                <Button
                  size="small"
                  aria-controls={openButton ? "split-button-menu" : undefined}
                  aria-expanded={openButton ? "true" : undefined}
                  aria-label="select action"
                  aria-haspopup="menu"
                  onClick={handleToggle}
                >
                  <ReusableIcon
                    iconName={"ArrowDropUp"}
                    iconColor={"#FFFFFF"}
                  />
                </Button>
              </ButtonGroup>
              <Popper
                sx={{
                  zIndex: 1,
                }}
                open={openButton}
                anchorEl={anchorRef.current}
                placement={"top-end"}
              >
                <Paper style={{ width: anchorRef.current?.clientWidth }}>
                  <ClickAwayListener onClickAway={handleCloseButton}>
                    <MenuList id="split-button-menu" autoFocusItem>
                      {options.slice(1).map((option, index) => (
                        <MenuItem
                          key={option}
                          selected={index === selectedIndex - 1}
                          onClick={() => handleClick(option, index + 1)}
                        >
                          {option}
                        </MenuItem>
                      ))}
                    </MenuList>
                  </ClickAwayListener>
                </Paper>
              </Popper>
              <ButtonGroup
                variant="contained"
                ref={anchorRefChange}
                aria-label="split button"
              >
                <Button
                  size="small"
                  onClick={() => handleClickChange(optionsChange[0], 0)}
                  sx={{ cursor: "default" }}
                >
                  {optionsChange[0]}
                </Button>
                <Button
                  size="small"
                  aria-controls={
                    openButtonChange ? "split-button-menu" : undefined
                  }
                  aria-expanded={openButtonChange ? "true" : undefined}
                  aria-label="select action"
                  aria-haspopup="menu"
                  onClick={handleToggleChange}
                >
                  <ReusableIcon
                    iconName={"ArrowDropUp"}
                    iconColor={"#FFFFFF"}
                  />
                </Button>
              </ButtonGroup>
              <Popper
                sx={{
                  zIndex: 1,
                }}
                open={openButtonChange}
                anchorEl={anchorRefChange.current}
                placement={"top-end"}
              >
                <Paper style={{ width: anchorRefChange.current?.clientWidth }}>
                  <ClickAwayListener onClickAway={handleCloseButtonChange}>
                    <MenuList id="split-button-menu" autoFocusItem>
                      {optionsChange.slice(1).map((option, index) => (
                        <MenuItem
                          key={option}
                          selected={index === selectedIndexChange - 1}
                          onClick={() => handleClickChange(option, index + 1)}
                        >
                          {option}
                        </MenuItem>
                      ))}
                    </MenuList>
                  </ClickAwayListener>
                </Paper>
              </Popper>

              {enableDocumentUpload && (
                <AttachmentUploadDialog
                  artifactId=""
                  artifactName=""
                  setOpen={setEnableDocumentUpload}
                  handleUpload={uploadExcel}
                />
              )}
            </BottomNavigation>
          </Paper>
          ):""
            }
        </Stack>
      </div>
    </div>
      }
    </>
   
  );
};

export default ProfitCenter;
