import{s as tt,q as p,r as c,eP as Ht,a as t,j as s,T as S,E as K,eS as $,x as L,G as u,b6 as Ut,ay as _t,at as Gt,au as Jt,K as A,eK as q,ai as J,b4 as rt,B as z,F as Z,u as Zt,b as Xt,bh as Qt,eT as Yt,bX as gt,C as yt,z as ye,al as Be,I as Ke,b1 as Bt,aD as Kt,aE as Pt,t as O,aB as W,V as er,aF as tr,aG as rr,W as nr,ar as or,X as cr,cb as ir,cc as sr,bp as ar,bq as Pe}from"./index-17b8d91e.js";import{d as ur}from"./ArrowCircleLeftOutlined-2a09f8e2.js";import"./AutoCompleteType-9336ea79.js";import"./dayjs.min-ce01f2c7.js";import{D as dr}from"./DatePicker-68227989.js";import{l as et}from"./lookup-1dcf10ac.js";import{R as hr}from"./ReusableAttachementAndComments-bab6bbfc.js";import{S as mr,a as lr,b as fr}from"./Stepper-88e4fb0c.js";import"./useChangeLogUpdate-f322f7d1.js";import"./dateViewRenderers-34586552.js";import"./useSlotProps-e34e1e13.js";import"./InputAdornment-5b0053c5.js";import"./CSSTransition-30917e2c.js";import"./useMediaQuery-6a073ac5.js";import"./DesktopDatePicker-07c19cde.js";import"./useMobilePicker-9978caff.js";import"./CloudUpload-27b6d63e.js";import"./utilityImages-067c3dc2.js";import"./Add-********.js";import"./Delete-9f4d7a45.js";function nt(r){var l,b,X,w;console.log("maxlength",r.field);const m=tt();var f=r.field.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").replaceAll("%","").split(" ").join("");const v=p(i=>i.bankKey.singleBKPayload),x=i=>{const d=C=>{m(J({keyName:"Region1",data:C.body}))},N=C=>{console.log(C,"error in dojax")};A(`/${q}/data/getRegionBasedOnCountry?country=${i.code}`,"get",d,N)},R=i=>{const d=C=>{m(J({keyName:"Region2",data:C.body}))},N=C=>{console.log(C,"error in dojax")};A(`/${q}/data/getRegionBasedOnCountry?country=${i.code}`,"get",d,N)};c.useEffect(()=>{var i,d;(((i=r==null?void 0:r.field)==null?void 0:i.visibility)==="0"||((d=r==null?void 0:r.field)==null?void 0:d.visibility)==="Required")&&m(Ht(f))},[]);const k=p(i=>i.AllDropDown.dropDown);if(((l=r.field)==null?void 0:l.fieldType)==="Input")return t(u,{item:!0,md:2,children:r.field.visibility==="Hidden"?null:s(L,{children:[s(S,{variant:"body2",color:"#777",children:[r.field.fieldName,r.field.visibility==="Required"||r.field.visibility==="0"?t("span",{style:{color:"red"},children:"*"}):""]}),t(K,{size:"small",type:r.field.dataType==="QUAN"?"number":"",placeholder:`Enter ${r.field.fieldName}`,inputProps:{maxLength:r.field.maxLength},value:v[f],onChange:(i,d)=>{const N=i.target.value;Object.keys(v).length>0?(console.log("0"),N.length>0&&N[0]===" "?(console.log("1"),m($({keyName:f,data:N.trimStart()}))):(console.log("2"),m($({keyName:f,data:N.toUpperCase()})))):(console.log("3"),m($({keyName:f,data:N.trimStart()})))},required:r.field.visibility==="Required"||r.field.visibility==="0"})]})});if(((b=r.field)==null?void 0:b.fieldType)==="Drop Down")return t(u,{item:!0,md:2,children:r.field.visibility==="Hidden"?null:s(L,{children:[s(S,{variant:"body2",color:"#777",children:[r.field.fieldName,r.field.visibility==="Required"||r.field.visibility==="0"?t("span",{style:{color:"red"},children:"*"}):""]}),t(Ut,{sx:{height:"31px"},fullWidth:!0,size:"small",value:v[f],onChange:(i,d)=>{r.field.fieldName==="Country 1"&&x(d),r.field.fieldName==="Country 2"&&R(d),m($({keyName:f,data:d}))},options:k[f]??[],required:r.field.visibility==="0"||r.field.visibility==="Required",getOptionLabel:i=>`${i==null?void 0:i.code} - ${i==null?void 0:i.desc}`,renderOption:(i,d)=>t("li",{...i,children:s(S,{style:{fontSize:12},children:[d==null?void 0:d.code," - ",d==null?void 0:d.desc]})}),renderInput:i=>t(K,{...i,variant:"outlined",placeholder:`Select ${r.field.fieldName}`})})]})});if(((X=r.field)==null?void 0:X.fieldType)==="Radio Button")return s(u,{item:!0,md:2,children:[s(S,{variant:"body2",color:"#777",children:[r.field.fieldName,r.field.visibility==="Required"||r.field.visibility==="0"?t("span",{style:{color:"red"},children:"*"}):""]}),t(_t,{sx:{padding:0},checked:v[f]==!0,onChange:i=>{m($({keyName:f,data:i.target.checked}))}})]});if(((w=r.field)==null?void 0:w.fieldType)==="Calendar")return t(u,{item:!0,md:2,children:s(L,{children:[s(S,{variant:"body2",color:"#777",children:[r.field.fieldName,r.field.visibility==="Required"||r.field.visibility==="0"?t("span",{style:{color:"red"},children:"*"}):""]}),t(Gt,{dateAdapter:Jt,children:t(dr,{slotProps:{textField:{size:"small"}},value:v[f],onChange:i=>m($({keyName:f,data:"/Date("+Date.parse(i)+")/"})),required:r.field.visibility==="0"||r.field.visibility==="Required"})})]})})}const br=r=>{let m=Object==null?void 0:Object.entries(r==null?void 0:r.bankDataTabDetails);console.log("basic",r,m);const[f,v]=c.useState([]);return c.useEffect(()=>{v(m==null?void 0:m.map(x=>{var R,k;return s(u,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...rt},children:[t(u,{container:!0,children:t(S,{sx:{fontSize:"12px",fontWeight:"700"},children:x[0]})}),t(z,{children:t(u,{container:!0,spacing:1,children:(k=(R=[...x[1]].filter(l=>(l==null?void 0:l.visibility)!="Hidden"))==null?void 0:R.sort((l,b)=>(l==null?void 0:l.sequenceNo)-(b==null?void 0:b.sequenceNo)))==null?void 0:k.map(l=>t(nt,{field:l,dropDownData:r==null?void 0:r.dropDownData,country:r.country}))})})]})}))},[r==null?void 0:r.basicDataTabDetails]),t(Z,{children:f})},Sr=r=>{let m=Object==null?void 0:Object.entries(r==null?void 0:r.addressDataTabDetails);console.log("basic",r,m);const[f,v]=c.useState([]);return c.useEffect(()=>{v(m==null?void 0:m.map(x=>{var R,k;return s(u,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...rt},children:[t(u,{container:!0,children:t(S,{sx:{fontSize:"12px",fontWeight:"700"},children:x[0]})}),t(z,{children:t(u,{container:!0,spacing:1,children:(k=(R=[...x[1]].filter(l=>(l==null?void 0:l.visibility)!="Hidden"))==null?void 0:R.sort((l,b)=>(l==null?void 0:l.sequenceNo)-(b==null?void 0:b.sequenceNo)))==null?void 0:k.map(l=>t(nt,{field:l,dropDownData:r==null?void 0:r.dropDownData}))})})]})}))},[r==null?void 0:r.basicDataTabDetails]),t(Z,{children:f})},Wr=()=>{var fe,be,Se,xe,ke,Ce,De,ve,Re,Ne,Te,we,Ee,pe,Ae,Fe,Ie,Me,Oe,qe,Le,$e,Ve,je,We,ze,He,Ue,_e,Ge,Je,Ze,Xe,Qe,Ye,ge;const r=Zt(),[m,f]=c.useState(0),[v,x]=c.useState(!1),[R,k]=c.useState(""),[l,b]=c.useState(!1),[X,w]=c.useState(!0),[i,d]=c.useState(!1),[N,C]=c.useState(!1),[ot,Q]=c.useState(!1),[ct,F]=c.useState(!1),[it,P]=c.useState(!1),[st,Y]=c.useState(!1),[H,at]=c.useState([]),[ut,ee]=c.useState(!1),[te,dt]=c.useState(!0),[ht,re]=c.useState(!0),[mt,ne]=c.useState(!1),[lt,ft]=c.useState(!1),[bt,St]=c.useState(""),[g,xt]=c.useState(""),[U,oe]=c.useState(""),[kt,ce]=c.useState(!1),[Ct,ie]=c.useState(!1),_=Xt(),h=r.state;console.log("displayData",h),console.log("submitForReviewDisabled",te);const e=p(o=>o.bankKey.singleBKPayload),se=p(o=>o.AllDropDown.dropDown),Dt=p(o=>o.bankKey.bankKeyBankData),ae=p(o=>o.bankKey.singleBKPayload),vt=p(o=>o.bankKey.requiredFields);console.log("payloadFields",ae),console.log("requiredFields",(fe=e==null?void 0:e.TimeZone)==null?void 0:fe.code);const Rt=p(o=>o.bankKey.bankKeyAddressData);let E=p(o=>o.userManagement.userData);const V=tt(),[ue,Nt]=c.useState(0),Tt=(o,a)=>{const n=T=>{V(J({keyName:o,data:T.body})),Nt(I=>I+1)},D=T=>{console.log(T)};A(`/${q}/data/${a}`,"get",n,D)},wt=()=>{var o,a;(a=(o=et)==null?void 0:o.bankKey)==null||a.map(n=>{Tt(n==null?void 0:n.keyName,n==null?void 0:n.endPoint)})},Et=()=>{var o,a;ue==((a=(o=et)==null?void 0:o.bankKey)==null?void 0:a.length)?F(!1):F(!0)};c.useEffect(()=>{Et()},[ue]),c.useEffect(()=>{wt()},[]),c.useEffect(()=>{xt(Qt("BK"))},[]);let de=["BANK DETAILS","ADDRESS DETAILS","ATTACHMENTS & COMMENTS"];const pt=o=>{var a,n;switch(o){case 0:return t(br,{bankDataTabDetails:Dt,dropDownData:se,country:(n=(a=h==null?void 0:h.bankCtryReg)==null?void 0:a.newBankCtryReg)==null?void 0:n.code});case 1:return t(Sr,{addressDataTabDetails:Rt,dropDownData:se});case 2:return t(hr,{title:"BankKey",useMetaData:!1,artifactId:g,artifactName:"BankKey"});default:return"Unknown step"}},At=()=>{le()?f(a=>a+1):me()},Ft=()=>{le()?f(a=>a-1):me()},y=()=>{Q(!0)},It=()=>{kt?(Q(!1),ce(!1)):(Q(!1),_("/masterDataCockpit/bankKey"))},j=()=>{Y(!0)},Mt=()=>{ee(!1)},he=()=>{Y(!1)},me=()=>{ee(!0)},le=()=>ar(ae,vt,at);c.useEffect(()=>{V(Yt(H))},[H]),console.log("singleBKPayload",e);var B={AddressDto:{AddressID:"",Title:e!=null&&e.Title?e==null?void 0:e.Title:"",Name:e!=null&&e.Name?e==null?void 0:e.Name:"",Name2:e!=null&&e.Name1?e==null?void 0:e.Name1:"",Name3:e!=null&&e.Name2?e==null?void 0:e.Name2:"",Name4:e!=null&&e.Name3?e==null?void 0:e.Name3:"",Sort1:e!=null&&e.SearchTerm1?e==null?void 0:e.SearchTerm1:"",Sort2:e!=null&&e.SearchTerm2?e==null?void 0:e.SearchTerm2:"",BuildLong:e!=null&&e.BuildingCode?e==null?void 0:e.BuildingCode:"",RoomNo:e!=null&&e.RoomNumber?e==null?void 0:e.RoomNumber:"",Floor:e!=null&&e.Floor?e==null?void 0:e.Floor:"",COName:e!=null&&e.co?e==null?void 0:e.co:"",StrSuppl1:e!=null&&e.Street1?e==null?void 0:e.Street1:"",StrSuppl2:e!=null&&e.Street2?e==null?void 0:e.Street2:"",Street:e!=null&&e.Street3?e==null?void 0:e.Street3:"",HouseNo:e!=null&&e.HouseNumber?e==null?void 0:e.HouseNumber:"",HouseNo2:e!=null&&e.Supplement?e==null?void 0:e.Supplement:"",StrSuppl3:e!=null&&e.Street4?e==null?void 0:e.Street4:"",Location:e!=null&&e.Street5?e==null?void 0:e.Street5:"",District:e!=null&&e.District?e==null?void 0:e.District:"",HomeCity:e!=null&&e.OtherCity?e==null?void 0:e.OtherCity:"",PostlCod1:e!=null&&e.PostalCode?e==null?void 0:e.PostalCode:"",PostlCod2:e!=null&&e.PostalCode1?e==null?void 0:e.PostalCode1:"",PostlCod3:e!=null&&e.CompanyPostCd?e==null?void 0:e.CompanyPostCd:"",PoBox:e!=null&&e.POBox?e==null?void 0:e.POBox:"",PoBoxCit:e!=null&&e.POBoxCity?e==null?void 0:e.POBoxCity:"",PoBoxReg:(be=e==null?void 0:e.Region2)!=null&&be.code?(Se=e==null?void 0:e.Region2)==null?void 0:Se.code:"",PoboxCtry:(xe=e==null?void 0:e.Country2)!=null&&xe.code?(ke=e==null?void 0:e.Country2)==null?void 0:ke.code:"",Country:(Ce=e==null?void 0:e.Country1)!=null&&Ce.code?(De=e==null?void 0:e.Country1)==null?void 0:De.code:"",TimeZone:(ve=e==null?void 0:e.TimeZone)!=null&&ve.code?(Re=e==null?void 0:e.TimeZone)==null?void 0:Re.code:"",Taxjurcode:(Ne=e==null?void 0:e.TaxJurisdiction)!=null&&Ne.code?(Te=e==null?void 0:e.TaxJurisdiction)==null?void 0:Te.code:"",Transpzone:(we=e==null?void 0:e.TransportZone)!=null&&we.code?(Ee=e==null?void 0:e.TransportZone)==null?void 0:Ee.code:"",Regiogroup:(pe=e==null?void 0:e.StructureGroup)!=null&&pe.code?(Ae=e==null?void 0:e.StructureGroup)==null?void 0:Ae.code:"",DontUseS:(Fe=e==null?void 0:e.Undeliverable)!=null&&Fe.code?(Ie=e==null?void 0:e.Undeliverable)==null?void 0:Ie.code:"",DontUseP:(Me=e==null?void 0:e.Undeliverable1)!=null&&Me.code?(Oe=e==null?void 0:e.Undeliverable1)==null?void 0:Oe.code:"",PoWONo:(e==null?void 0:e.POBoxwoNo)===!0?"X":"",DeliServType:(qe=e==null?void 0:e.DeliveryServiceType)!=null&&qe.code?(Le=e==null?void 0:e.DeliveryServiceType)==null?void 0:Le.code:"",DeliServNumber:e!=null&&e.DeliveryServiceNo?e==null?void 0:e.DeliveryServiceNo:"",Township:e!=null&&e.Township?e==null?void 0:e.Township:"",Langu:($e=e==null?void 0:e.Language)!=null&&$e.code?(Ve=e==null?void 0:e.Language)==null?void 0:Ve.code:"",Tel1Numbr:e!=null&&e.Telephone?e==null?void 0:e.Telephone:"",Tel1Ext:e!=null&&e.Extension?e==null?void 0:e.Extension:"",MobilePhone:e!=null&&e.MobilePhone?e==null?void 0:e.MobilePhone:"",FaxNumber:e!=null&&e.Fax?e==null?void 0:e.Fax:"",FaxExtens:e!=null&&e.Extension1?e==null?void 0:e.Extension1:"",EMail:e!=null&&e.EMailAddress?e==null?void 0:e.EMailAddress:"",AdrNotes:e!=null&&e.Notes?e==null?void 0:e.Notes:"",Region:(je=e==null?void 0:e.Region1)!=null&&je.code?(We=e==null?void 0:e.Region1)==null?void 0:We.code:"",PoBoxLobby:e!=null&&e.PoBoxLobby?e==null?void 0:e.PoBoxLobby:""},BankKeyID:"",ReqCreatedBy:E==null?void 0:E.user_id,ReqCreatedOn:"",RequestStatus:"",CreationId:"",EditId:"",DeleteId:"",MassCreationId:"",MassEditId:"",MassDeleteId:"",RequestType:"Create",TaskId:"",Remarks:U||"",Action:"I",Validation:ht===!0?"X":"",BankCtry:(He=(ze=h==null?void 0:h.bankCtryReg)==null?void 0:ze.newBankCtryReg)!=null&&He.code?(_e=(Ue=h==null?void 0:h.bankCtryReg)==null?void 0:Ue.newBankCtryReg)==null?void 0:_e.code:"",BankKey:(Ge=h==null?void 0:h.bankKey)!=null&&Ge.newBankKey?(Je=h==null?void 0:h.bankKey)==null?void 0:Je.newBankKey:"",BankName:e!=null&&e.BankName?e==null?void 0:e.BankName:"",BankRegion:(Ze=e==null?void 0:e.Region)!=null&&Ze.code?(Xe=e==null?void 0:e.Region)==null?void 0:Xe.code:"",BankStreet:e!=null&&e.Street?e==null?void 0:e.Street:"",City:e!=null&&e.City?e==null?void 0:e.City:"",BankBranch:e!=null&&e.BankBranch?e==null?void 0:e.BankBranch:"",SwiftCode:e!=null&&e.SWIFTBIC?e==null?void 0:e.SWIFTBIC:"",BankGroup:e!=null&&e.BankGroup?e==null?void 0:e.BankGroup:"",PobkCurac:(e==null?void 0:e.PostbankAcct)===!0?"X":"",BankNo:e!=null&&e.BankNumber?e==null?void 0:e.BankNumber:""};const Ot=()=>{P(!0);const o=n=>{var D,T,I;P(!1),n.statusCode===201?(dt(!1),x("Create"),x("Create"),k("All Data has been Validated. Bank Key can be Sent for Review"),b("success"),w(!1),d(!0),y(),C(!0),ce(!0)):(x("Error"),d(!1),k(`${(D=n==null?void 0:n.body)!=null&&D.message[0]?(T=n==null?void 0:n.body)==null?void 0:T.message[0]:(I=n==null?void 0:n.body)==null?void 0:I.value}`),b("danger"),w(!1),C(!0),j())},a=n=>{console.log(n)};A(`/${q}/alter/validateSingleBankKey`,"post",o,a,B)},qt=()=>{b(!1),j(),x("Confirm"),k("Do You Want to Save as Draft ?"),ft(!0),St("proceed")},Lt=()=>{const o=n=>{if(F(!0),n.statusCode===200){console.log("success"),x("Create"),k(`Bank Key has been saved NBS${n.body}`),b("success"),w(!1),d(!0),y(),C(!0),F(!1);const D={artifactId:g,createdBy:E==null?void 0:E.emailId,artifactType:"BankKey",requestId:`CBS${n==null?void 0:n.body}`},T=M=>{console.log("Second API success",M)},I=M=>{console.error("Second API error",M)};A(`/${Pe}/documentManagement/updateDocRequestId`,"post",T,I,D),_("/masterDataCockpit/bankKey")}else x("Save"),d(!1),k("Failed Saving the Data "),b("danger"),w(!1),C(!0),j(),F(!1);handleClose()},a=n=>{console.log(n)};A(`/${q}/alter/bankKeyAsDraft`,"post",o,a,B)},$t=()=>{U.length<=0?ie(!0):Vt()},Vt=()=>{G(),F(!0);const o=n=>{if(n.statusCode===200){console.log("success"),x("Create"),k(`Bank Key has been submitted for review NBS${n.body}`),b("success"),w(!1),d(!0),y(),C(!0),F(!1);const D={artifactId:g,createdBy:E==null?void 0:E.emailId,artifactType:"BankKey",requestId:`NBS${n==null?void 0:n.body}`},T=M=>{console.log("Second API success",M)},I=M=>{console.error("Second API error",M)};A(`/${Pe}/documentManagement/updateDocRequestId`,"post",T,I,D),_("/masterDataCockpit/bankKey")}else x("Create"),d(!1),k("Creation Failed"),b("danger"),w(!1),C(!0),j(),F(!1);handleClose()},a=n=>{console.log(n)};A(`/${q}/alter/bankKeySubmitForReview`,"post",o,a,B)},jt=()=>{re(!1),ne(!0)},G=()=>{ie(!1),re(!0),ne(!1)},Wt=(o,a)=>{const n=o.target.value;if(n.length>0&&n[0]===" ")oe(n.trimStart());else{let D=n.toUpperCase();oe(D)}},zt=o=>{const a=D=>{V(J({keyName:"Region",data:D.body}))},n=D=>{console.log(D,"error in dojax")};A(`/${q}/data/getRegionBasedOnCountry?country=${o}`,"get",a,n)};return c.useEffect(()=>{var o,a;zt((a=(o=h==null?void 0:h.bankCtryReg)==null?void 0:o.newBankCtryReg)==null?void 0:a.code)},[]),t(Z,{children:ct===!0?t(gt,{}):s("div",{children:[t(yt,{dialogState:st,openReusableDialog:j,closeReusableDialog:he,dialogTitle:v,dialogMessage:R,handleDialogConfirm:he,dialogOkText:"OK",handleExtraButton:Lt,dialogSeverity:l,showCancelButton:!0,showExtraButton:lt,handleDialogReject:()=>{Y(!1)},handleExtraText:bt}),H.length!=0&&t(ye,{openSnackBar:ut,alertMsg:"Please fill the following Field: "+H.join(", "),handleSnackBarClose:Mt}),i&&t(ye,{openSnackBar:ot,alertMsg:R,handleSnackBarClose:It}),t(u,{container:!0,style:{...Be,backgroundColor:"#FAFCFF"},children:s(u,{sx:{width:"inherit"},children:[t(u,{item:!0,md:7,style:{padding:"16px",display:"flex"},children:s(u,{item:!0,md:5,sx:{display:"flex"},children:[t(u,{children:t(Ke,{color:"primary","aria-label":"upload picture",component:"label",sx:Bt,children:t(ur,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{_("/masterDataCockpit/bankKey"),V(clearPayload()),V(clearOrgData())}})})}),s(u,{children:[t(S,{variant:"h3",children:t("strong",{children:"Create Bank Key"})}),t(S,{variant:"body2",color:"#777",children:"This view creates a new Bank Key"})]})]})}),t(u,{container:!0,style:{padding:"0 1rem 0 1rem"},children:s(u,{container:!0,sx:Be,children:[s(u,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:[s(z,{width:"70%",sx:{marginLeft:"40px"},children:[t(u,{item:!0,sx:{paddingTop:"2px !important"},children:s(L,{flexDirection:"row",children:[t("div",{style:{width:"15%"},children:t(S,{variant:"body2",color:"#777",children:"Bank Country"})}),s(S,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[": ",(Ye=(Qe=h==null?void 0:h.bankCtryReg)==null?void 0:Qe.newBankCtryReg)==null?void 0:Ye.code]})]})}),t(u,{item:!0,sx:{paddingTop:"2px !important"},children:s(L,{flexDirection:"row",children:[t("div",{style:{width:"15%"},children:t(S,{variant:"body2",color:"#777",children:"Bank Key"})}),s(S,{variant:"body2",fontWeight:"bold",children:[":",(ge=h==null?void 0:h.bankKey)==null?void 0:ge.newBankKey]})]})})]}),t(z,{width:"30%",sx:{marginLeft:"40px"},children:t(u,{item:!0,children:s(L,{flexDirection:"row",children:[t(S,{variant:"body2",color:"#777",style:{width:"30%"}}),t(S,{variant:"body2",fontWeight:"bold",sx:{width:"8%",textAlign:"center"}}),t(S,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start"})]})})})]}),t(u,{container:!0,children:t(mr,{activeStep:m,sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},children:de.map((o,a)=>t(lr,{children:t(fr,{sx:{fontWeight:"700"},children:o})},o))})}),t(u,{container:!0,children:t(u,{container:!0,children:pt(m)})})]})})]})}),t(Kt,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:s(Pt,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t(O,{variant:"contained",size:"small",sx:{...W,mr:1},onClick:qt,children:"Save As Draft"}),t(O,{variant:"contained",size:"small",sx:{...W,mr:1},onClick:Ft,disabled:m===0,children:"Back"}),m===de.length-1?s(Z,{children:[t(O,{variant:"contained",size:"small",sx:{...W,mr:1},onClick:Ot,children:"Validate"}),t(O,{variant:"contained",size:"small",sx:{...W,mr:1},onClick:jt,disabled:te,children:"Submit For Review"})]}):t(O,{variant:"contained",size:"small",sx:{...W,mr:1},onClick:At,children:"Next"})]})}),s(er,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:mt,onClose:G,children:[s(tr,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(S,{variant:"h6",children:"REMARKS"}),t(Ke,{sx:{width:"max-content"},onClick:G,children:t(rr,{})})]}),t(nr,{sx:{padding:".5rem 1rem"},children:s(L,{children:[t(z,{sx:{minWidth:400},children:t(or,{sx:{height:"auto"},fullWidth:!0,children:t(K,{sx:{backgroundColor:"#F5F5F5"},value:U,onChange:Wt,multiline:!0,placeholder:"ENTER REMARKS",inputProps:{maxLength:254}})})}),Ct&&t(u,{children:t(S,{style:{color:"red"},children:"Please Enter Remarks"})})]})}),s(cr,{sx:{display:"flex",justifyContent:"end"},children:[t(O,{sx:{width:"max-content",textTransform:"capitalize"},onClick:G,children:"Cancel"}),t(O,{className:"button_primary--normal",type:"save",onClick:$t,variant:"contained",children:"Submit"})]})]}),t(ir,{sx:{color:"#fff",zIndex:o=>o.zIndex.drawer+1},open:it,children:t(sr,{color:"inherit"})})]})})};export{Wr as default};
