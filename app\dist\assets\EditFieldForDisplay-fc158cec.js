import{r as i,q as f,s as b,bI as A,a,x as z,j as v,T as p,b6 as O,E,at as P,au as w,ay as I,F as m,G as T}from"./index-17b8d91e.js";import{d as G}from"./dayjs.min-ce01f2c7.js";import{D as L}from"./DatePicker-68227989.js";function R(r,t){return Array.isArray(t)&&t.find(y=>y.code===r)||""}const M=({label:r,value:t,units:u,onSave:y,isEditMode:F,isExtendMode:S,options:q=[],type:c})=>{var D;const[n,d]=i.useState(t),[B,x]=i.useState(!1),s=f(e=>e.AllDropDown.dropDown),g=b(),j=R(n,s);console.log("dropdownData",n),console.log("value e",t),console.log("label",r),console.log("units",u),console.log("transformedValue",j);const C=f(e=>e.edit.payload);let V=f(e=>e.payload.errorFields);console.log("editField",C),console.log("fieldData",{label:r,value:n,units:u,type:c});let o=r.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("");i.useEffect(()=>{d(t)},[t]);const h=e=>{console.log("checkonedit"),g(A({keyname:o.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""),data:e}))};i.useEffect(()=>{console.log("lkey",o),console.log("data",t),g(A({keyname:o.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""),data:t||""}))},[]);const $=G();return console.log("editedValue[key] ",s[o]),console.log("editedValue[key] ",n),a(T,{item:!0,children:a(z,{children:F||S?v(m,{children:[a(p,{variant:"body2",color:"#777",children:r}),c==="Drop Down"?a(O,{options:s[o]??[],value:n&&((D=s[o])==null?void 0:D.filter(e=>e.code===n))||"",onChange:(e,l)=>{h(l.code),console.log("newValue",l),d(l.code),x(!0),console.log("keys",o)},getOptionLabel:e=>{var l,k;return console.log("optionoptionoption",e),e===""?"":`${e&&((l=e[0])==null?void 0:l.code)} - ${e&&((k=e[0])==null?void 0:k.desc)}`},renderOption:(e,l)=>(console.log("option vakue",l),a("li",{...e,children:a(p,{style:{fontSize:12},children:`${l==null?void 0:l.code} - ${l==null?void 0:l.desc}`})})),renderInput:e=>a(E,{...e,variant:"outlined",size:"small",label:null,placeholder:`Select ${r}`})}):c==="Input"?a(E,{variant:"outlined",size:"small",value:n,onChange:e=>{const l=e.target.value;h(l),d(l)},placeholder:`Enter ${r}`}):c==="Calendar"?a(P,{dateAdapter:w,children:a(L,{slotProps:{textField:{size:"small"}},value:s[o]?s[o]:"",defaultValue:$,placeholder:"Select Date Range",onChange:e=>g(A({keyname:o,data:"/Date("+Date.parse(e)+")/"})),onError:V.includes(o)})}):c==="Radio Button"?a(I,{sx:{padding:0},checked:n,onChange:(e,l)=>{h(l),d(l)}}):""]}):a(m,{children:v(m,{children:[a(p,{variant:"body2",color:"#777",children:r}),a(p,{variant:"body2",fontWeight:"bold",children:n})]})})})})};export{M as E};
