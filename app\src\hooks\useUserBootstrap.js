// ✅ File: src/hooks/useUserBootstrap.js
import { useState } from "react";
import { useDispatch } from "react-redux";
import { setUserDetails, setRoles } from "../app/userManagementSlice";
import { doAjax } from "../components/Common/fetchService";
import { LOADING_MESSAGE } from "../constant/enum";
import { destination_MaterialMgmt } from "../destinationVariables";

const combineUserData = (userRes, rolesRes) => {
  const u = userRes?.data;
  const user = {
    id: u.userDetails.masterUserId,
    user_id: u.userDetails.businessEmailId,
    firstName: u.userDetails.firstName,
    lastName: u.userDetails.lastName,
    emailId: u.userDetails.businessEmailId,
    displayName: `${u.userDetails.firstName} ${u.userDetails.lastName}`,
    userName: u.userDetails.userName,
  };

  const roles = rolesRes?.data?.map((r) => r.roleName);

  return {
    ...user,
    roles,
  };
};

const useUserBootstrap = ({ destination_IWA_NEW, fallbackUser, fallbackEntities, isLocalEnv }) => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [moduleAccessStatus, setModuleAccessStatus] = useState("loading");
  const [finalEmailId, setFinalEmailId] = useState("");

  const fetchAndDispatchUser = async () => {
    setLoading(true);
    setLoaderMessage(LOADING_MESSAGE.LOADING_USER);

    let userRes = null;
    let rolesRes = null;

    try {
      await new Promise((resolve, reject) => {
        doAjax(
          `/${destination_MaterialMgmt}/iwa/user/current-user`,
          "get",
          (res) => {
            userRes = res;
            resolve();
          },
          reject
        );
      });

      const email = userRes?.data?.userDetails?.businessEmailId;
      const version = userRes?.data?.version ?? 1;

      await new Promise((resolve, reject) => {
        doAjax(
          `/${destination_MaterialMgmt}/iwa/user/roles?businessEmailId=${encodeURIComponent(email)}&userVersionNo=${version}&includeRoleDetails=false&iwaAppIds=MDG`,
          "get",
          (res) => {
            rolesRes = res;
            resolve();
          },
          reject
        );
      });

      const finalUserData = combineUserData(userRes, rolesRes);
      dispatch(setUserDetails(finalUserData));
      dispatch(setRoles(finalUserData.roles));

      setFinalEmailId(finalUserData.emailId);
      setModuleAccessStatus(true);
    } catch (error) {
      console.error("User bootstrap failed:", error);
      if (isLocalEnv) {
        dispatch(setUserDetails(fallbackUser));
        dispatch(setRoles(fallbackUser.roles || []));
        setModuleAccessStatus(true);
      } else {
        setModuleAccessStatus(false);
        setTimeout(() => (window.location.href = "/do/logout"), 8000);
      }
    } finally {
      setLoaderMessage("");
      setLoading(false);
    }
  };

  return {
    fetchAndDispatchUser,
    loading,
    loaderMessage,
    moduleAccessStatus,
    finalEmailId,
  };
};

export default useUserBootstrap;
