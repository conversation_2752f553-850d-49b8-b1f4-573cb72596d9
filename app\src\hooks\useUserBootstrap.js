// ✅ File: src/hooks/useUserBootstrap.js
import { useState } from "react";
import { useDispatch } from "react-redux";
import { setUserDetails, setRoles } from "../app/userManagementSlice";
import { doAjax } from "../components/Common/fetchService";
import { LOADING_MESSAGE } from "../constant/enum";
import { destination_MaterialMgmt } from "../destinationVariables";

const combineUserData = (userRes, rolesRes) => {
  const u = userRes?.data;
  const user = {
    id: u.userDetails.masterUserId,
    user_id: u.userDetails.businessEmailId,
    firstName: u.userDetails.firstName,
    lastName: u.userDetails.lastName,
    emailId: u.userDetails.businessEmailId,
    displayName: `${u.userDetails.firstName} ${u.userDetails.lastName}`,
    userName: u.userDetails.userName,
  };

  const roles = rolesRes?.data?.map((r) => r.roleName);

  return {
    ...user,
    roles,
  };
};

const useUserBootstrap = ({ destination_IWA_NEW, fallbackUser, fallbackEntities, isLocalEnv }) => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [moduleAccessStatus, setModuleAccessStatus] = useState("loading");
  const [finalEmailId, setFinalEmailId] = useState("");

  const fetchAndDispatchUser = async () => {
    setLoading(true);
    setLoaderMessage(LOADING_MESSAGE.LOADING_USER);

    // In local environment, set fallback data immediately but still try API calls
    if (isLocalEnv) {
      dispatch(setUserDetails(fallbackUser));
      dispatch(setRoles(fallbackUser.roles || []));
      setFinalEmailId(fallbackUser.emailId);
      setModuleAccessStatus(true);
      setLoaderMessage("");
      setLoading(false);
      // Continue to try API calls in background - don't return here
    }

    let userRes = null;
    let rolesRes = null;

    try {
      await new Promise((resolve, reject) => {
        doAjax(
          `/${destination_MaterialMgmt}/iwa/user/current-user`,
          "get",
          (res) => {
            userRes = res;
            resolve();
          },
          reject
        );
      });

      const email = userRes?.data?.userDetails?.businessEmailId;
      const version = userRes?.data?.version ?? 1;

      await new Promise((resolve, reject) => {
        doAjax(
          `/${destination_MaterialMgmt}/iwa/user/roles?businessEmailId=${encodeURIComponent(email)}&userVersionNo=${version}&includeRoleDetails=false&iwaAppIds=MDG`,
          "get",
          (res) => {
            rolesRes = res;
            resolve();
          },
          reject
        );
      });

      const finalUserData = combineUserData(userRes, rolesRes);
      // Update with real data from API (this will override fallback data)
      dispatch(setUserDetails(finalUserData));
      dispatch(setRoles(finalUserData.roles));

      setFinalEmailId(finalUserData.emailId);
      setModuleAccessStatus(true);
    } catch (error) {
      console.error("User bootstrap failed:", error);
      if (isLocalEnv) {
        // In local env, fallback data is already set, just log the error
        console.log("API call failed in localhost, using fallback data");
      } else {
        setModuleAccessStatus(false);
        setTimeout(() => (window.location.href = "/do/logout"), 8000);
      }
    } finally {
      if (!isLocalEnv) {
        setLoaderMessage("");
        setLoading(false);
      }
    }
  };

  return {
    fetchAndDispatchUser,
    loading,
    loaderMessage,
    moduleAccessStatus,
    finalEmailId,
  };
};

export default useUserBootstrap;
