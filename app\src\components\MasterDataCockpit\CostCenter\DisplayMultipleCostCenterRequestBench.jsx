import {
  <PERSON>,
  Grid,
  Icon<PERSON>utton,
  Tabs,
  <PERSON>po<PERSON>,
  Stack,
  Paper,
  BottomNavigation,
  Button,
  CardContent,
  Stepper,
  Step,
  StepLabel,
} from "@mui/material";
import Tab from "@mui/material/Tab";
import React, { useState, useEffect } from "react";
import {
  iconButton_SpacingSmall,
  outermostContainer,
  container_Padding,
  outerContainer_Information,
  button_Primary,
  outermostContainer_Information,
  button_Outlined,
} from "../../common/commonStyles";
import { useSelector, useDispatch } from "react-redux";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import { useNavigate, useLocation } from "react-router-dom";
// import EditableField from "./EditFieldForDisplay";
import { doAjax } from "../../Common/fetchService";
import {
  destination_CostCenter,
  destination_MaterialMgmt,
} from "../../../destinationVariables";
import EditableFieldForCostCenter from "./EditableFieldForCostCenter";
import EditableFieldForMassCostCenter from "./EditFieldForMassCostCenter";
import moment from "moment";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import EditableFieldForMassCostCenterRB from "./EditableFieldForMassCostCenterRB";


import EditableField from "../EditFieldForDisplay";
import ChangeLog from "../../Changelog/ChangeLog";
import TrackChangesTwoToneIcon from '@mui/icons-material/TrackChangesTwoTone';
import { setDropDown } from "../../../app/dropDownDataSlice";
import { formValidator } from "../../../functions";
import { clearSingleCostCenter } from "../../../app/costCenterTabsSlice";
import ReusableSnackBar from "../../Common/ReusableSnackBar";
import { setPayloadWhole } from "../../../app/editPayloadSlice";
//   import EditableField from "./EditFieldForDisplay";

const DisplayMultipleCostCenterRequestBench = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [dropDownData, setDropDownData] = useState({});
  const [value, setValue] = useState(0);
  const [tablevalue, setTableValue] = useState([]);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isDisplayMode, setIsDisplayMode] = useState(true);
  let [factorsArray, setFactorsArray] = useState([]);
  const [materialDetails, setMaterialDetails] = useState([]);
  const [activeStep, setActiveStep] = useState(0);
  const [IDs, setIDs] = useState();
  const allTabs = useSelector((state) => state.tabsData);
  const [isChangeLogopen,setisChangeLogopen]=useState(false);
  const [formValidationErrorItems, setFormValidationErrorItems] = useState([]); //chiranjit
  const [openSnackbarValidation, setOpenSnackbarValidation] = useState(false); 
  const appSettings = useSelector((state) => state.appSettings);
  const reference = {
    basicData: "Basic Data",
  };
  const location = useLocation();
  const MultipleCostCenter = useSelector(
    (state) => state.costCenter.MultipleCostCenterRequestBench
  );
  const tabsData = location.state.tabsData;
  const selectedRowData = location.state.rowData;
  const massCostRowData =location.state.requestbenchRowData;
  console.log("selectedrowdata", MultipleCostCenter);
  //const requestNumber = location.state.requestNumber;
  console.log(location,"location=================")
  /*let requestNumber=''
  if (location.state.requestNumber !== undefined || location.state.requestNumber !== null){
    requestNumber = location.state.requestNumber;
  }else{
    requestNumber = useSelector((state) => state.userManagement.taskData.subject.slice(3));
  }*/
  let task = useSelector((state) => state.userManagement.taskData);
  let userData = useSelector((state) => state.userManagement.userData);
  let singleCCPayloadAfterChange = useSelector((state) => state.edit.payload);
  let requiredFieldTabWise= useSelector((state) => state.costCenter.requiredFields);
  console.log(singleCCPayloadAfterChange,requiredFieldTabWise,"required_field_for_data")
  console.log(task?.processDesc,"task?.processDesc")
  console.log(task?.subject,"task?.subject")
  console.log(massCostRowData?.requestId,"massCostRowData?.requestId")
  console.log(massCostRowData?.requestType,"massCostRowData?.requestType")
  let requestNumber=''
  let controllingArea=''
  let costCentername=''
  if (task?.processDesc === "Mass Change") {
    requestNumber = task?.subject
        ? task?.subject?.slice(3)
        : massCostRowData?.requestId.slice(3)
    controllingArea = task?.subject ? '':selectedRowData.controllingArea
    costCentername= task?.subject ? '':selectedRowData.costCenter
    
  } else if (task?.processDesc === "Mass Create") {
    requestNumber = task?.subject
        ? task?.subject?.slice(3)
        : massCostRowData?.requestId.slice(3)
    controllingArea = task?.subject ? '':selectedRowData.controllingArea
    costCentername= task?.subject ? '':selectedRowData.costCenter
  } else if (massCostRowData?.requestType === "Mass Create") {
    requestNumber = task?.subject
        ? task?.subject?.slice(3)
        : massCostRowData?.requestId.slice(3)
    controllingArea = task?.subject ? '':selectedRowData.controllingArea
    costCentername= task?.subject ? '':selectedRowData.costCenter
  } else if (massCostRowData?.requestType === "Mass Change") {
    requestNumber = task?.subject
        ? task?.subject?.slice(3)
        : massCostRowData?.requestId.slice(3)
    controllingArea = task?.subject ? '':selectedRowData.controllingArea
    costCentername= task?.subject ? '':selectedRowData.costCenter
  }

  
  //console.log(requestNumber,"requestNumber=============")
  //if(location.state.requestNumber)
  let activeRow = {};
  let activeIndex = -1;
  for (let index = 0; index < MultipleCostCenter?.length; index++) {
    if (MultipleCostCenter[index].costCenter === selectedRowData.costCenter) {
      activeRow = MultipleCostCenter[index];
      activeIndex = index;
      break;
    }
  }
  const handleChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  const handleBack = () => {
    //setActiveStep((prevActiveStep) => prevActiveStep - 1);
    
    const isValidation = handleCheckValidationError();
    if(isEditMode){
      if (isValidation) {
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
        dispatch(clearSingleCostCenter());
      } else {
        handleSnackBarOpenValidation();
      }
    }else{
      setActiveStep((prevActiveStep) => prevActiveStep - 1);
      dispatch(clearSingleCostCenter());
    }
  };
  const handleNext = () => {
    //setActiveStep((prevActiveStep) => prevActiveStep + 1);
    
    const isValidation = handleCheckValidationError();
    if(isEditMode){
      if (isValidation) {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
        dispatch(clearSingleCostCenter());
      } else {
        handleSnackBarOpenValidation();
      }
    }else{
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
      dispatch(clearSingleCostCenter());
    }
  };
  const handleSnackBarOpenValidation = () => {
    setOpenSnackbarValidation(true);
  };
  const onEdit = () => {
    setIsEditMode(true);
    setIsDisplayMode(false);
  };

  const tabsArray = Object.entries(tabsData?.viewData)
    .filter((item) => typeof item[1] == "object" && item[1] != null)
    .map((item) => item[0]);
  const tabContents = Object.entries(tabsData.viewData)
    .filter((item) => typeof item[1] == "object" && item[1] != null)
    .map((item) => Object.entries(item[1]));
  
    const tempHash = {};
    const gridArr = tabContents.map(item => {
      item.forEach((eachTab, i) => {
        eachTab.forEach((eachItem, i) => {
          if (i !== 0) {
            eachItem.forEach(fieldItem => {
              console.log(fieldItem.fieldName,)
              tempHash[fieldItem.fieldName
              .replaceAll("(", "")
              .replaceAll(")", "")
              .replaceAll("/", "")
              .replaceAll("-", "")
              .replaceAll(".", "")
              .split(" ")
              .join("")] =  fieldItem.value;
            });
          }
        });
      });
  });

  useEffect(() => {
    dispatch(setPayloadWhole(tempHash));
  }, []);
  console.log(tempHash,"tempHash")

  const handleClosemodalData = (data) => { 
    setisChangeLogopen(data);
  }
  const openChangeLog = () => { 
    setisChangeLogopen(true);
  }
  console.log(singleCCPayloadAfterChange,requiredFieldTabWise,"requiredFieldTabWise")
  const handleCheckValidationError = () => {
    //chiranjit
    return formValidator(
      singleCCPayloadAfterChange,
      requiredFieldTabWise,
      setFormValidationErrorItems
    );
  };
  const handleSnackBarCloseValidation = () => {
    setOpenSnackbarValidation(false);
  };
  const getHierarchyArea = (value) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "HierarchyArea", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getHierarchyArea?controllingArea=${value}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCompanyCode = (value) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CompanyCode", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getCompanyCodeBasedOnControllingArea?controllingArea=${value}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getProfitCenter = (value) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ProfitCenter", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getProfitCenterAsPerControllingArea?controllingArea=${value}`,
      "get",
      hSuccess,
      hError
    );
  };
  useEffect(() => {
    getHierarchyArea(selectedRowData.controllingArea);
    getCompanyCode(selectedRowData.controllingArea);
    getProfitCenter(selectedRowData.controllingArea);
  }, []);


  console.log("tabcontents", tabContents);
  return (
    <div>
      <Grid
        container
        style={{
          ...outermostContainer_Information,
          backgroundColor: "#FAFCFF",
        }}
      >
        {formValidationErrorItems.length != 0 && (
            <ReusableSnackBar
              openSnackBar={openSnackbarValidation}
              alertMsg={
                "Please fill the following Field: " +
                formValidationErrorItems.join(", ")
              }
              handleSnackBarClose={handleSnackBarCloseValidation}
            />
          )}
        <Grid sx={{ width: "inherit" }}>
          <Grid item md={12} style={{ padding: "16px", display: "flex" }}>
            {/* <Grid  sx={{ display: "flex" }}> */}
            <Grid item style={{ display: "flex", justifyContent: "flex-end" }}>
              <IconButton
                // onClick={handleBacktoRO}
                color="primary"
                aria-label="upload picture"
                component="label"
                sx={iconButton_SpacingSmall}
              >
                <ArrowCircleLeftOutlinedIcon
                  style={{ height: "1em", width: "1em", color: "#000000" }}
                  onClick={() => {
                    navigate(-1);
                    // navigate("/RequestBench");
                    // dispatch(clearPayload());
                    // dispatch(clearOrgData());
                  }}
                />
              </IconButton>
            </Grid>
            <Grid md={10}>
              <Typography variant="h3">
                <strong>
                  Multiple Cost Center : {selectedRowData.costCenter}{" "}
                </strong>
              </Typography>
              <Typography variant="body2" color="#777">
                This view displays details of uploaded Cost Center
              </Typography>
            </Grid>
            
             <Grid md={1} sx={{ display: "flex", justifyContent: "flex-end" ,marginRight:"4px"}}>
                <Button
                  variant="outlined"
                  size="small"
                  sx={button_Outlined}
                  onClick={openChangeLog}
                  title="Change Log"
                >

                  <TrackChangesTwoToneIcon
                    sx={{ padding: "2px" }}
                    fontSize="small"
                  />
                </Button>
              </Grid>
              {isChangeLogopen && <ChangeLog
                open={true}
                closeModal={handleClosemodalData}
                requestId={requestNumber}
                requestType={"Mass"}
                pageName={"costCenter"}
                controllingArea={selectedRowData.controllingArea}
                centerName={selectedRowData.costCenter}
              />}
              {!isEditMode ? (
                userData?.role === "Finance"?
              (<Grid md={1} sx={{ display: "flex", justifyContent: "flex-end" }}>
                <Grid item>
                  <Button
                    variant="outlined"
                    size="small"
                    sx={button_Outlined}
                    onClick={onEdit}
                  >
                    Change
                    <EditOutlinedIcon
                      sx={{ padding: "2px" }}
                      fontSize="small"
                    />
                  </Button>
                </Grid>
              </Grid>)
              :""
            ) : (
              ""
            )}
          </Grid>
          <Grid container display="flex" flexDirection="row" flexWrap="nowrap">
            <Box width="70%" sx={{ marginLeft: "40px" }}>
              <Grid item sx={{ paddingTop: "2px !important" }}>
                <Stack flexDirection="row">
                  <div style={{ width: "15%" }}>
                    <Typography variant="body2" color="#777">
                      Cost Center
                    </Typography>
                  </div>
                  <Typography
                    variant="body2"
                    fontWeight="bold"
                    justifyContent="flex-start"
                  >
                    : {selectedRowData.costCenter}
                  </Typography>
                </Stack>
              </Grid>

              <Grid item sx={{ paddingTop: "2px !important" }}>
                <Stack flexDirection="row">
                  <div style={{ width: "15%" }}>
                    <Typography variant="body2" color="#777">
                      Controlling Area
                    </Typography>
                  </div>
                  <Typography variant="body2" fontWeight="bold">
                    : {selectedRowData.controllingArea}
                  </Typography>
                </Stack>
              </Grid>
              <Grid item sx={{ paddingTop: "2px !important" }}>
                <Stack flexDirection="row">
                  <div style={{ width: "15%" }}>
                    <Typography variant="body2" color="#777">
                      Valid From
                    </Typography>
                  </div>
                  <Typography variant="body2" fontWeight="bold">
                    : {moment(selectedRowData.validFrom).format(appSettings?.dateFormat)}
                  </Typography>
                </Stack>
              </Grid>
              <Grid item sx={{ paddingTop: "2px !important" }}>
                <Stack flexDirection="row">
                  <div style={{ width: "15%" }}>
                    <Typography variant="body2" color="#777">
                      Valid To
                    </Typography>
                  </div>
                  <Typography variant="body2" fontWeight="bold">
                    : {moment(selectedRowData.validTo).format(appSettings?.dateFormat)}
                  </Typography>
                </Stack>
              </Grid>
            </Box>
          </Grid>

          <Grid container style={{ padding: "16px" }}>
          
            <Stepper
                 activeStep={activeStep}
                onChange={handleChange}
                variant="scrollable"
                sx={{
                  background: "#FFFFFF",
                  borderBottom: "1px solid #BDBDBD",
                  width: "100%",
                  height: "48px",
                }}
                aria-label="mui tabs example"
              >
                {tabsArray.map((factor, index) => (
                 <Step key={factor}>
                 <StepLabel sx={{ fontWeight: "700" }}>{factor}</StepLabel>
               </Step>
                ))}
              </Stepper>
           
            <Grid
              key={tabContents}
              container
              item
              md={12}
              sx={{
                backgroundColor: "white",
                maxHeight: "max-content",
                height: "max-content",
                // borderRadius: "8px",
                // border: "1px solid #E0E0E0",
                mt: 1,
                // boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                // padding: "10px",
                mb: 1,
              }}
            >
              {tabContents &&
                tabContents[activeStep]?.map((item, index) => {
                  console.log("narzo", item[0]);
                  return (
                    <Box key={index} sx={{ width: "100%" }}>
                      <Grid
                        item
                        md={12}
                        sx={{
                          backgroundColor: "white",
                          maxHeight: "max-content",
                          height: "max-content",
                          borderRadius: "8px",
                          border: "1px solid #E0E0E0",
                          mt: 0.25,
                          boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                          ...container_Padding,
                          // ...container_columnGap,
                        }}
                      >
                        <Grid container>
                          <Typography
                            sx={{
                              fontSize: "12px",
                              fontWeight: "700",
                              margin: "0px !important",
                            }}
                          >
                            {item[0]}
                          </Typography>
                        </Grid>
                        <Box>
                          <Box sx={{ width: "100%" }}>
                            <CardContent
                              sx={{
                                padding: "0",
                                paddingBottom: "0 !important",
                                paddingTop: "10px !important",
                              }}
                            >
                              <Grid
                                container
                                style={{
                                  display: "grid",
                                  gridTemplateColumns: "repeat(6,1fr)",
                                  gap: "15px",
                                }}
                                justifyContent="space-between"
                                alignItems="flex-start"
                                md={12}
                              >
                                {[...item[1]].map((innerItem) => {
                                  console.log("inneritem", item[1]);
                                  return (
                                    <EditableFieldForMassCostCenterRB
                                    // key={field.fieldName}
                                    activeTabIndex={activeStep}
                                    ccTabs={tabsArray}
                                    fieldGroup={item[0]}
                                    selectedRowData={
                                      selectedRowData.costCenter
                                    }
                                    visibility={innerItem.visibility}
                                    label={innerItem.fieldName}
                                    value={innerItem.value}
                                    length={innerItem.maxLength}
                                    onSave={(newValue) =>
                                      handleFieldSave(
                                        innerItem.fieldName,
                                        newValue
                                      )
                                    }
                                    isEditMode={isEditMode}
                                    // isExtendMode={isExtendMode}
                                    type={innerItem.fieldType}
                                    field={innerItem} // Update the type as needed
                                  />
                                  );
                                })}
                              </Grid>
                            </CardContent>
                          </Box>
                        </Box>
                      </Grid>
                     
                    </Box>
                  );
                })}
            </Grid>
          </Grid>
        </Grid>
      </Grid>
      {isEditMode ? (
        <Paper
          sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
          elevation={2}
        >
          <BottomNavigation
            className="container_BottomNav"
            showLabels
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              gap: 1,
            }}
            value={value}
            onChange={(newValue) => {
              setValue(newValue);
            }}
          >
            <Button
              size="small"
              variant="contained"
              onClick={() => {
                navigate(-1);
              }}
            >
              Save
            </Button>
            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary, mr: 1 }}
              onClick={handleBack}
              disabled={activeStep === 0}
            >
              Back
            </Button>
            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary, mr: 1 }}
              onClick={handleNext}
              disabled={activeStep === tabsArray.length - 1 ? true : false}
            >
              Next
            </Button>
          </BottomNavigation>
        </Paper>
      ) : (
        <Paper
        sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
        elevation={2}
      >
        <BottomNavigation
          className="container_BottomNav"
          showLabels
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            gap: 1,
          }}
          value={value}
          onChange={(newValue) => {
            setValue(newValue);
          }}
        >
          <Button
            variant="contained"
            size="small"
            sx={{ ...button_Primary, mr: 1 }}
            onClick={handleBack}
            disabled={activeStep === 0}
          >
            Back
          </Button>
          <Button
            variant="contained"
            size="small"
            sx={{ ...button_Primary, mr: 1 }}
            onClick={handleNext}
            disabled={activeStep === tabsArray.length - 1 ? true : false}
          >
            Next
          </Button>
          
        </BottomNavigation>
      </Paper>
      )}
    </div>
  );
};

export default DisplayMultipleCostCenterRequestBench;
