import{cB as F,cC as G,cD as N,cE as u,cF as r,cG as d,r as p,cH as V,cI as j,cJ as y,cK as E,cL as P,o as I,cM as $,cN as w,cO as D}from"./index-75c1660a.js";const M=["alignItems","autoFocus","component","children","dense","disableGutters","divider","focusVisibleClassName","selected","className"],S=(t,e)=>{const{ownerState:s}=t;return[e.root,s.dense&&e.dense,s.alignItems==="flex-start"&&e.alignItemsFlexStart,s.divider&&e.divider,!s.disableGutters&&e.gutters]},_=t=>{const{alignItems:e,classes:s,dense:a,disabled:o,disableGutters:c,divider:l,selected:f}=t,i=w({root:["root",a&&"dense",!c&&"gutters",l&&"divider",o&&"disabled",e==="flex-start"&&"alignItemsFlexStart",f&&"selected"]},D,s);return u({},s,i)},T=F(G,{shouldForwardProp:t=>N(t)||t==="classes",name:"MuiListItemButton",slot:"Root",overridesResolver:S})(({theme:t,ownerState:e})=>u({display:"flex",flexGrow:1,justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",minWidth:0,boxSizing:"border-box",textAlign:"left",paddingTop:8,paddingBottom:8,transition:t.transitions.create("background-color",{duration:t.transitions.duration.shortest}),"&:hover":{textDecoration:"none",backgroundColor:(t.vars||t).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${r.selected}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / ${t.vars.palette.action.selectedOpacity})`:d(t.palette.primary.main,t.palette.action.selectedOpacity),[`&.${r.focusVisible}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.focusOpacity}))`:d(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.focusOpacity)}},[`&.${r.selected}:hover`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.hoverOpacity}))`:d(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / ${t.vars.palette.action.selectedOpacity})`:d(t.palette.primary.main,t.palette.action.selectedOpacity)}},[`&.${r.focusVisible}`]:{backgroundColor:(t.vars||t).palette.action.focus},[`&.${r.disabled}`]:{opacity:(t.vars||t).palette.action.disabledOpacity}},e.divider&&{borderBottom:`1px solid ${(t.vars||t).palette.divider}`,backgroundClip:"padding-box"},e.alignItems==="flex-start"&&{alignItems:"flex-start"},!e.disableGutters&&{paddingLeft:16,paddingRight:16},e.dense&&{paddingTop:4,paddingBottom:4})),U=p.forwardRef(function(e,s){const a=V({props:e,name:"MuiListItemButton"}),{alignItems:o="center",autoFocus:c=!1,component:l="div",children:f,dense:v=!1,disableGutters:i=!1,divider:B=!1,focusVisibleClassName:O,selected:L=!1,className:k}=a,n=j(a,M),x=p.useContext(y),C=p.useMemo(()=>({dense:v||x.dense||!1,alignItems:o,disableGutters:i}),[o,x.dense,v,i]),b=p.useRef(null);E(()=>{c&&b.current&&b.current.focus()},[c]);const m=u({},a,{alignItems:o,dense:C.dense,disableGutters:i,divider:B,selected:L}),g=_(m),R=P(b,s);return I.jsx(y.Provider,{value:C,children:I.jsx(T,u({ref:R,href:n.href||n.to,component:(n.href||n.to)&&l==="div"?"button":l,focusVisibleClassName:$(g.focusVisible,O),ownerState:m,className:$(g.root,k)},n,{classes:g,children:f}))})}),z=U;export{z as L};
