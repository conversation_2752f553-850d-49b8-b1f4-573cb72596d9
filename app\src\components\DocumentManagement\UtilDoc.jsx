import {
  BottomNavigation,
  Box,
  Button,
  FormControlLabel,
  Grid,
  IconButton,
  Modal,
  Paper,
  Tooltip,
  Typography,
} from "@mui/material";
import { destination_Admin, destination_DocumentManagement, destination_MaterialMgmt } from "../../destinationVariables";
import { button_Primary, iconButton_SpacingSmall } from "../common/commonStyles";
import IframeComponent from "./IframeComponent";
import RtfIFrame from "./RtfIFrame";
import FileDownloadOutlinedIcon from "@mui/icons-material/FileDownloadOutlined";
import VisibilityOutlinedIcon from "@mui/icons-material/VisibilityOutlined";
import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
import ImageIcon from "@mui/icons-material/Image";
import { ViewDetailsIcon } from "../Common/icons";
import { useState } from "react";
import PictureAsPdfOutlinedIcon from '@mui/icons-material/PictureAsPdfOutlined';
import DescriptionOutlinedIcon from '@mui/icons-material/DescriptionOutlined';
import ImageOutlinedIcon from '@mui/icons-material/ImageOutlined';

import { baseUrl_Admin, baseUrl_DocumentManagement } from "../../data/baseUrl";
import { doAjax } from "../Common/fetchService";
import { API_CODE } from "@constant/enum";
import { END_POINTS } from '../../constant/apiEndPoints';
import { SUCCESS_MESSAGES, ERROR_MESSAGES } from '../../constant/enum';
import { colors } from "@constant/colors";
import VideoPlayer from "./VideoPlayer";
export const CustomIcon_DocumentType = ({ iconType }) => {
  if (iconType == "pdf") {

    
    return <PictureAsPdfOutlinedIcon sx={{ fontSize: "20px", color: "red" }} />;
  }
  if (iconType == "png" || iconType == "jpeg" || iconType == "jpg") {
    return <ImageOutlinedIcon sx={{ fontSize: "20px", color: "#1A9FB3" }} />;
  }
  if (iconType == "txt") {
    return <DescriptionOutlinedIcon sx={{ fontSize: "20px", color: "#757575" }} />;
  } else {
    return <DescriptionOutlinedIcon sx={{ fontSize: "20px",color: "#757575" }} />;
  }
};
export const MatIcon_DocumentType = ({ type, doctype }) => {
  let tempType = { type };
  let temp = [];
  tempType = tempType.type;
  temp = tempType.split(".");
  tempType = temp[1].toLowerCase();
  let tempTitle = doctype.charAt(0).toUpperCase() + doctype.slice(1);
  const noPointer = { cursor: "default" };

  return (
    <FormControlLabel
      control={
        <>
          <IconButton sx={{ ...iconButton_SpacingSmall }} style={noPointer}>
            <CustomIcon_DocumentType
              iconType={tempType}
            ></CustomIcon_DocumentType>
          </IconButton>
          <Tooltip title={tempTitle}>
            <p>{tempTitle}</p>
          </Tooltip>
        </>
      }
    />
  );
};

export const MatView = ({ index, name, isBroadcast=false }) => {
  let p = name?.toLowerCase();
  let temp = [];
  temp = p?.split(".");
  p = temp?.[temp?.length - 1];

  const [open, setOpen] = useState(false);
  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);
  const [value, setValue] = useState("1");

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };
  let url;
  {isBroadcast ? 
  url= `${baseUrl_Admin}/broadcastManagement/showBroadcastById/${index}` :
  url = `${baseUrl_DocumentManagement}/documentManagement/showDocument/${index}`}
  return (
    <>
      <IconButton sx={{ ...iconButton_SpacingSmall }} onClick={handleOpen}>
        <Tooltip title="View">
          <VisibilityOutlinedIcon sx={{ fontSize: "20px", color:`${colors.blue.indigo}` }} />
        </Tooltip>
      </IconButton>
     {(p=='pdf' || p=='docx' || p=='png' || p=='jpg' || p=='jpeg' || p=='xlxs' || p=='xlsx' || p=='mp4') ?
      <Modal
        open={open}
        onClose={handleClose}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <Box
          sx={{
            typography: "body1",
            left: "50%",
            top: "30%",
            width: 1100,
            position: "absolute",
            transform: "translate(-50%, -50%)",
            height: "400px",
            bgcolor: "background.paper",
            fontSize: 0,
          }}
        >
         
         {(p=='docx') && <RtfIFrame uri={url} driveType={2} />}
         {(p=='pdf') && <RtfIFrame uri={url} driveType={3} />}
         {(p=='png'||p=='jpg'||p=='jpeg') &&

          <IframeComponent uri={url} driveType={3} />}
     
         {(p=='xlxs'|| p=='xlsx') && 
         <>
            <IframeComponent
              uri={url}
              driveType={2}
            /></>}
             
        
          {p === 'mp4' && (
              <VideoPlayer videoUrlProp={url} />
            )}
        </Box>
      </Modal> :  <Modal
        open={open}
        onClose={handleClose}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <Box
        component={Paper}
        p={2}
          sx={{
          
            left: "50%",
            top: "50%",
           
            position: "absolute",
            transform: "translate(-50%, -50%)",
           
            bgcolor: "background.paper",
           
          }}
        >
          <Typography variant='h3'>No viewer detected for this format</Typography>
          </Box></Modal> }
      
    </>
  );
};

export const MatDownload = ({ index, name,transactionId,transactionType,setdownloadError, setMessageDialogTitle, setMessageDialogMessage,setMessageDialogSeverity ,handleMessageDialogClickOpen,setSnackbar,setopenSnackbar,handleSnackbarOpen}) => {
 
  const handleDownloadClick = async () => {
    let hSuccess=(response) => {
      const href = URL.createObjectURL(response);

      const link = document.createElement("a");
      link.href = href;
      link.setAttribute("download", `${name}`);
      document.body.appendChild(link);
      link.click();

      document.body.removeChild(link);
      URL.revokeObjectURL(href);
      setSnackbar(true);
     setopenSnackbar(true);
      setMessageDialogMessage(
        `Cost Center_Mass Create.xls has been downloaded successfully`
      );
    
     handleSnackbarOpen();
    }
    let hError=(error)=> {
      if (error.response) {
        console.log(error.response.data);
        console.log(error.response.status);
        console.log(error.response.headers);
      } else if (error.request) {
        console.log(error.request);
      } else {
        console.log("Error", error.message);
      }
      console.log(error.config);
      
    };
    doAjax(`/${destination_DocumentManagement}/documentManagement/download?id=${index}`,'getblobfile',hSuccess,hError)
     
  };
  return (
    <IconButton
      sx={{ ...iconButton_SpacingSmall, margin: "0px" }}
      onClick={handleDownloadClick}
    >
     
      <Tooltip title="Download">
        <FileDownloadOutlinedIcon color="success" />
      </Tooltip>
    
    </IconButton>
    
  );
};

export const DeleteRecord = ({index, name, setSnackbar, setopenSnackbar, setMessageDialogMessage, handleSnackbarOpen}) => {
  const handleDeleteRecord = () => {
    const hSuccess = (response) => {
      if(response.code == API_CODE.STATUS_0){
        setSnackbar(true);
        setopenSnackbar(true);
        setMessageDialogMessage(SUCCESS_MESSAGES.DOCUMENT_DELETED(name));
        handleSnackbarOpen();
      }
    };

    const hError = () => {
      setSnackbar(true);
      setopenSnackbar(true);
      setMessageDialogMessage(ERROR_MESSAGES.DOCUMENT_DELETE_FAILED(name));
      handleSnackbarOpen();
    };

    doAjax(
      `/${destination_DocumentManagement}${END_POINTS.DOCUMENT_MANAGEMENT.DELETE}/${index}`,
      'delete',
      hSuccess,
      hError
    );
  };

  return (
    <IconButton 
      sx={{ ...iconButton_SpacingSmall }} 
      onClick={handleDeleteRecord}
    >
      <Tooltip title="Delete">
        <DeleteOutlinedIcon color="danger" />
      </Tooltip>
    </IconButton>
  );
}
export const dateFormatter = (x) => {
  let temp = x.split(" ");
  let month;
  switch (temp[1]) {
    case "Jan":
      month = 1;
      break;
    case "Feb":
      month = 2;
      break;
    case "Mar":
      month = 3;
      break;
    case "Apr":
      month = 4;
      break;
    case "May":
      month = 5;
      break;
    case "Jun":
      month = 6;
      break;
    case "Jul":
      month = 7;
      break;
    case "Aug":
      month = 8;
      break;
    case "Sep":
      month = 9;
      break;
    case "Oct":
      month = 10;
      break;
    case "Nov":
      month = 11;
      break;
    case "Dec":
      month = 12;
      break;
  }
  let word = temp[3] + "-" + month + "-" + temp[2];
  return word;
};
export const DropdownNames = [
  "E-Invoice",
  "Purchase Order",
  "Service Request",
  "Advanced Shipment Notification",
  "Return",
  "User Management",
  "Purchase Request"
];
export const handleMultipleDownload = (DownloadArray) => {
  for (let i = 0; i < DownloadArray.length; i++) {
    let index = DownloadArray[i].documentId;
    let name = DownloadArray[i].fileName;
    let hSuccess=(response) => {
      const href = URL.createObjectURL(response);

      const link = document.createElement("a");
      link.href = href;
      link.setAttribute("download", `${name}`);
      document.body.appendChild(link);
      link.click();

      document.body.removeChild(link);
      URL.revokeObjectURL(href);
    }
  let hError=(error)=> {
      if (error.response) {
        console.log(error.response.data);
        console.log(error.response.status);
        console.log(error.response.headers);
      } else if (error.request) {
        console.log(error.request);
      } else {
        console.log("Error", error.message);
      }
      console.log(error.config);
    }
   doAjax(`/${destination_DocumentManagement}/documentManagement/download?id=${index}`,'getblobfile',hSuccess,hError)
     
    
  }
};
export const MultiDownloadButton=({value,setValue,DownloadNumber,DownloadArray})=>{
  return(
  <Paper
  sx={{ position: "fixed", bottom: 0, left: 0, right: 0,zIndex:1 }}
  elevation={2}
>
  <BottomNavigation
     className="container_BottomNav"
    showLabels
    value={value}
    onChange={(event, newValue) => {
      setValue(newValue);
    }}
  >
    <Grid
      container
      sx={{ display: "flex", alignContent: "center" }}
    >
      <Grid item md={1} />
      <Grid
        item
        md={12}
        sx={{
          display: "flex",
          justifyContent: "flex-end",
        }}
      >
        <Button
          size="small"
          variant="contained"
          
          onClick={() => handleMultipleDownload(DownloadArray)}
        >
          Download {DownloadNumber} Files
        </Button>
      </Grid>
    </Grid>
  </BottomNavigation>
</Paper>)
}




