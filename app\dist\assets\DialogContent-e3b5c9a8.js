import{o as e,p as s}from"./index-75c1660a.js";import{i as a}from"./Close-c868cc59.js";import{e as x}from"./IconButton-c40e0dd9.js";import{G as o,k as p,l as d,e as c,f as m}from"./Button-c2ace85e.js";const h=o(p)({overflow:"hidden",color:"var(--text-primary)",textOverflow:"ellipsis",whiteSpace:"nowrap",fontSize:"1rem",fontWeight:500,lineHeight:"normal",fontFamily:"inherit",padding:"1rem",display:"flex",alignItems:"center",gap:"1rem"});function f({titleIcon:t,closeIcon:r,onClose:i,...n}){return e.jsxs(h,{...n,children:[t??null,e.jsx("div",{style:{display:"flex",width:"100%",alignItems:"center"},children:n.children}),r?e.jsx(x,{onClick:i,children:e.jsx(a,{})}):null]})}const g=o(d)(({customWidth:t})=>({"& .MuiDialog-paper":{...t==="small"&&{maxWidth:"400px"},...t==="medium"&&{maxWidth:"600px"},...t==="large"&&{maxWidth:"900px"},...t==="xlarge"&&{maxWidth:"1200px"},...t==="xxlarge"&&{maxWidth:"1300px"},width:"100%"}}));function I({children:t,maxWidth:r="small",...i}){const n=s.Children.map(t,l=>s.isValidElement(l)&&l.type===f?s.cloneElement(l,{onClose:i.onClose}):l);return e.jsx(g,{customWidth:r,maxWidth:!1,fullWidth:!0,...i,children:n})}const u=o(c)({"&.MuiDialogActions-root":{display:"flex",height:"44px",padding:"0px 16px",flexDirection:"row",justifyContent:"center",alignItems:"flex-start",gap:"10px",alignSelf:"end"},"& .MuiButton-root":{height:"28px",fontSize:"14px",fontWeight:400,padding:"8px",justifyContent:"center",alignItems:"center"}});function S(t){return e.jsx(u,{...t})}const W=o(m)({display:"flex",padding:"16px",flexDirection:"column",gap:"16px",alignSelf:"stretch"});function v(t){return e.jsx(W,{...t})}export{f as g,S as l,v as r,I as u};
