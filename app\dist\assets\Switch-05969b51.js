import{o as i}from"./index-17b8d91e.js";import{M as t,p as a}from"./Button-f51b50ce.js";const o=t(a)({width:"2.125rem",height:"1.125rem",padding:0,margin:"0.56rem 0.5rem",display:"flex","& .MuiSwitch-switchBase":{height:"100%",padding:"0.05625rem 0.0625rem","&.Mui-checked":{transform:"translateX(1rem)",color:"var(--primary-main)","& + .MuiSwitch-track":{opacity:1,backgroundColor:"var(--primary-light)"}},"&:not(.Mui-checked)":{color:"var(--contrast-text)","& + .MuiSwitch-track":{opacity:.4,backgroundColor:"var(--text-secondary)"}},"&.Mui-disabled":{"&.Mui-checked":{opacity:.4,color:"var(--primary-main)","& + .<PERSON><PERSON><PERSON>witch-track":{backgroundColor:"var(--primary-light)"}},"&:not(.Mui-checked)":{opacity:.7,color:"var(--contrast-text)","& + .MuiSwitch-track":{opacity:.4,backgroundColor:"var(--text-disabled)"}}}},"& .MuiSwitch-thumb":{alignSelf:"center",borderRadius:"50%",width:"1rem",height:"1rem",boxShadow:"none"},"& .MuiSwitch-track":{height:"1.125rem",borderRadius:"1rem",opacity:1}}),h=r=>i.jsx(o,{...r});export{h};
