import React, { useState, useMemo } from "react";
import {
  Tree,
  Input,
  Button,
  Modal,
  Form,
  message,
  Tooltip,
  Table,
  Space,
  Typography,
  Alert,
  Card,
  Divider,
} from "antd";
import {
  DownOutlined,
  SisternodeOutlined,
  SubnodeOutlined,
  SignatureOutlined,
  CloseCircleOutlined,
  P<PERSON>pinOutlined,
  <PERSON>RightOutlined,
  ArrowLeftOutlined,
  CloseOutlined,
  LeftSquareOutlined,
  DeleteOutlined,
  RightSquareOutlined,
  FileTextOutlined,
  FolderOutlined,
  FolderOpenOutlined,
  TagOutlined,
  BranchesOutlined,
  NodeIndexOutlined,
  ApartmentOutlined,
  PlusCircleOutlined,
  MinusCircleOutlined,
  EditOutlined,
  SwapOutlined,
  SearchOutlined,
  ExpandOutlined,
  CompressOutlined,
  SettingOutlined,
  InfoCircleOutlined,
  HistoryOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
} from "@ant-design/icons";
import { useEffect } from "react";
import { setTreeData } from "@app/hierarchyDataSlice";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { Tag } from "@mui/icons-material";
import {
  addDescription,
  addNode,
  addTag,
  completeNodeMove,
  completeTagMove,
  deleteNode,
  editDescription,
  removeTag,
  selectNodeForMove,
  selectTagForMove,
  updateToChangeLog,
} from "../../../app/hierarchyDataSlice";
import useDuplicacyCheck from "./../../../hooks/useDuplicateCheckHierarchy";
import { MODULE_KEY_MAP } from "../../../constant/enum";
import { v4 as uuidv4 } from "uuid";

const { Search } = Input;
const { Item } = Form;
const { confirm } = Modal;

const treeTheme = {
  colors: {
    primary: "#1890ff",
    success: "#52c41a",
    warning: "#faad14",
    error: "#ff4d4f",
    info: "#13c2c2",

    // Node colors
    parentNode: "#2c3e50",
    leafNode: "#34495e",
    tagNode: "#7f8c8d",

    // Background colors
    primaryBg: "#f0f8ff",
    successBg: "#f6ffed",
    warningBg: "#fffbe6",
    errorBg: "#fff2f0",
    infoBg: "#e6fffb",

    // Border colors
    primaryBorder: "#d9e8fc",
    successBorder: "#b7eb8f",
    warningBorder: "#ffe58f",
    errorBorder: "#ffccc7",
    infoBorder: "#87e8de",

    // Text colors
    textPrimary: "#262626",
    textSecondary: "#595959",
    textDisabled: "#bfbfbf",

    // Hover and active states
    hoverBg: "#f5f5f5",
    activeBg: "#e6f7ff",
    selectedBg: "#bae7ff",
  },

  spacing: {
    xs: "5px",
    sm: "8px",
    md: "12px",
    lg: "16px",
    xl: "24px",
    xxl: "32px",
  },

  borderRadius: {
    sm: "4px",
    md: "6px",
    lg: "8px",
  },

  shadows: {
    sm: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
    md: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
    lg: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
  },

  transitions: {
    default: "all 0.3s ease",
    fast: "all 0.15s ease",
    slow: "all 0.5s ease",
  },
};

const treeStyles = {
  container: {
    fontFamily:
      '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    fontSize: "14px",
    lineHeight: 1.5,
    padding: "16px",
    backgroundColor: "#fafafa",
    // minHeight: '1vh',
  },

  searchContainer: {
    display: "flex",
    marginBottom: treeTheme.spacing.lg,
    gap: treeTheme.spacing.sm,
    alignItems: "center",
  },

  actionButtonsContainer: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: treeTheme.spacing.lg,
    padding: treeTheme.spacing.sm,
    backgroundColor: treeTheme.colors.primaryBg,
    borderRadius: treeTheme.borderRadius.md,
    border: `1px solid ${treeTheme.colors.primaryBorder}`,
  },

  actionButton: {
    borderRadius: treeTheme.borderRadius.md,
    boxShadow: treeTheme.shadows.sm,
    transition: treeTheme.transitions.default,
    fontWeight: 500,
  },

  treeContainer: {
    backgroundColor: "#fff",
    borderRadius: treeTheme.borderRadius.lg,
    border: `1px solid ${treeTheme.colors.primaryBorder}`,
    boxShadow: treeTheme.shadows.md,
    padding: "16px",
  },

  nodeTitle: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    width: "100%",
    padding: `${treeTheme.spacing.xs} ${treeTheme.spacing.sm}`,
    borderRadius: treeTheme.borderRadius.sm,
    transition: treeTheme.transitions.default,
  },

  nodeContent: {
    display: "flex",
    gap: treeTheme.spacing.md,
    alignItems: "center",
    flex: 1,
  },

  nodeIcon: {
    fontSize: "16px",
    minWidth: "20px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
  },

  nodeLabel: {
    fontWeight: 600,
    minWidth: "80px",
    color: treeTheme.colors.textPrimary,
    fontSize: "14px",
  },

  nodeDescription: {
    color: treeTheme.colors.textSecondary,
    fontSize: "13px",
    fontStyle: "italic",
    flex: 1,
  },

  tagTitle: {
    display: "flex",
    alignItems: "center",
    gap: treeTheme.spacing.sm,
    padding: `${treeTheme.spacing.xs} ${treeTheme.spacing.sm}`,
    borderRadius: treeTheme.borderRadius.sm,
    transition: treeTheme.transitions.default,
  },

  tagIcon: {
    fontSize: "14px",
    color: treeTheme.colors.info,
  },

  tagLabel: {
    color: treeTheme.colors.info,
    fontSize: "13px",
    fontWeight: 500,
  },

  nodeActionButtons: {
    display: "flex",
    gap: treeTheme.spacing.xs,
    alignItems: "center",
    opacity: 0,
    transition: treeTheme.transitions.default,
    paddingLeft: "20px",
  },

  actionIconButton: {
    border: "none",
    boxShadow: "none",
    borderRadius: "50%",
    width: "28px",
    height: "28px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    transition: treeTheme.transitions.fast,
  },

  searchHighlight: {
    backgroundColor: "#ffeb3b",
    color: "#d32f2f",
    fontWeight: 600,
    padding: "1px 2px",
    borderRadius: "2px",
  },

  moveAlert: {
    padding: treeTheme.spacing.lg,
    marginBottom: treeTheme.spacing.lg,
    backgroundColor: treeTheme.colors.warningBg,
    border: `1px solid ${treeTheme.colors.warningBorder}`,
    borderRadius: treeTheme.borderRadius.md,
    display: "flex",
    alignItems: "center",
    gap: treeTheme.spacing.sm,
    boxShadow: treeTheme.shadows.sm,
  },
};

const getAllNodeKeys = (nodes) => {
  let keys = [];
  const traverse = (node) => {
    keys.push(node.id);
    if (node.child) {
      node.child.forEach(traverse);
    }
  };
  nodes.forEach(traverse);
  return keys;
};

const generateNextChildId = (parentNode) => {
  if (!parentNode.child || parentNode.child.length === 0) {
    return `${parentNode.id}-0`; // First child
  }

  // Get all existing child numbers
  const childNumbers = parentNode.child.map((child) => {
    const parts = child.id.split("-");
    return parseInt(parts[parts.length - 1]);
  });

  const maxChildNumber = Math.max(...childNumbers);
  return `${parentNode.id}-${maxChildNumber + 1}`;
};

const ReusableHierarchyTree = ({
  initialRawTreeData = [],
  editmode = false,
  object = "Tag",
}) => {
  const [searchValue, setSearchValue] = useState("");
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const rawTreeData = useSelector((state) => state?.hierarchyData?.treeData);
  const requestorPayload = useSelector(
    (state) => state.payload.requestorPayload
  );
  const reduxPayload = useSelector((state) => state.hierarchyData);
  const deletedlist = useSelector(
    (state) => state?.hierarchyData?.DeleteNodeList
  );
  const [isChangeLogVisible, setIsChangeLogVisible] = useState(false);
  const [isTagModalVisible, setIsTagModalVisible] = useState(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [currentParent, setCurrentParent] = useState(null);
  const [currentTagParent, setCurrentTagParent] = useState(null);
  const [currentEditNode, setCurrentEditNode] = useState(null);
  const [selectedNode, setSelectedNode] = useState(null);
  const [selectedTag, setSelectedTag] = useState(null);
  const [originalParent, setOriginalParent] = useState(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [activeAction, setActiveAction] = useState("add");
  const [changeLog, setChangeLog] = useState([
    { type: "ADD", description: "node-1 added to root" },
    { type: "ADD", description: "node-2 added to node-1" },
    { type: "DELETE", description: "node-2 deleted from node-1" },
    { type: "MOVE", description: "node-3 moved from node-1 to node-4" },
  ]);
  const [form] = Form.useForm();
  const [tagForm] = Form.useForm();
  const [editForm] = Form.useForm();
  const dispatch = useDispatch();
  let userData = useSelector((state) => state.userManagement.userData);
  const {
    checkForNodeDuplicacy,
    checkForDescriptionDuplicacy,
    checkForObjectDuplicacy,
  } = useDuplicacyCheck();
  console.log("rawTreeData", rawTreeData, deletedlist, originalParent);
  // Generate a flat list of all nodes for search functionality

  useEffect(() => {
    dispatch(setTreeData(initialRawTreeData));
  }, [initialRawTreeData]);

  const generateFullNodeList = (nodes) => {
    const list = [];
    const processNode = (node) => {
      list.push({
        key: node.id,
        label: node.label,
        description: node.description,
        isTag: false,
      });

      if (node.tags) {
        node.tags.forEach((tag, index) => {
          list.push({
            key: `${node.id}-tag-${index}`,
            label: tag,
            description: `${node.label} Tag`,
            isTag: true,
            parentKey: node.id,
          });
        });
      }

      if (node.child) {
        node.child.forEach(processNode);
      }
    };

    nodes.forEach(processNode);
    return list;
  };

  const fullNodeList = useMemo(
    () => generateFullNodeList(rawTreeData),
    [rawTreeData]
  );

  const getNodeIcon = (node, isExpanded = false) => {
    if (node.child && node.child.length > 0) {
      return isExpanded ? (
        <FolderOpenOutlined
          style={{ color: treeTheme.colors.warning, ...treeStyles.nodeIcon }}
        />
      ) : (
        <FolderOutlined
          style={{ color: treeTheme.colors.primary, ...treeStyles.nodeIcon }}
        />
      );
    } else if (node.tags && node.tags.length > 0) {
      return (
        <BranchesOutlined
          style={{ color: treeTheme.colors.success, ...treeStyles.nodeIcon }}
        />
      );
    } else {
      return (
        <NodeIndexOutlined
          style={{
            color: treeTheme.colors.textSecondary,
            ...treeStyles.nodeIcon,
          }}
        />
      );
    }
  };

  const getActionButton = (
    icon,
    onClick,
    tooltip,
    type = "default",
    danger = false
  ) => {
    const iconColor = danger
      ? treeTheme.colors.error
      : type === "primary"
      ? "white"
      : treeTheme.colors.textPrimary;

    return (
      <Tooltip placement="top" title={tooltip}>
        <Button
          type={type}
          danger={danger}
          size="small"
          icon={React.cloneElement(icon, {
            style: { color: danger ? "white" : iconColor },
          })}
          onClick={onClick}
          style={{
            ...treeStyles.actionIconButton,
            ...(type === "primary" && {
              backgroundColor: treeTheme.colors.primary,
            }),
            ...(danger && {
              backgroundColor: treeTheme.colors.error,
              color: "white",
            }),
          }}
        />
      </Tooltip>
    );
  };

  // Convert rawTreeData to AntD Tree format
  const convertTreeData = (nodes, parentNode) => {
    return nodes.map((node) => {
      const tagNodes =
        node.tags?.map((tag, index) => {
          const tagKey = `${node.id}-tag-${index}`;
          const tagMatches =
            searchValue &&
            (tag.toLowerCase().includes(searchValue.toLowerCase()) ||
              `${node.label} Tag`
                .toLowerCase()
                .includes(searchValue.toLowerCase()));

          return {
            title: (
              <div style={treeStyles.tagTitle}>
                <TagOutlined style={treeStyles.tagIcon} />
                <div style={treeStyles.tagLabel}>
                  {searchValue ? (
                    <span>
                      {tag
                        .split(new RegExp(`(${searchValue})`, "i"))
                        .map((part, i) =>
                          part.toLowerCase() === searchValue.toLowerCase() ? (
                            <span key={i} style={treeStyles.searchHighlight}>
                              {part}
                            </span>
                          ) : (
                            part
                          )
                        )}
                    </span>
                  ) : (
                    <Tooltip
                      placement="bottomLeft"
                      title={`${tag} under ${node?.label}`}
                    >
                      {tag}
                    </Tooltip>
                  )}
                </div>
                {editmode && (
                  <div
                    className="node-action-buttons"
                    style={treeStyles.nodeActionButtons}
                  >
                    {activeAction === "remove" &&
                      getActionButton(
                        <CloseCircleOutlined />,
                        (e) => {
                          e.stopPropagation();
                          handleRemoveTag(tag, node);
                        },
                        `Remove ${object} ${tag}`,
                        "default",
                        true
                      )}
                    {activeAction === "move" &&
                      !selectedNode &&
                      !selectedTag &&
                      getActionButton(
                        <SwapOutlined />,
                        (e) => {
                          e.stopPropagation();
                          handleSelectTag(tag, node);
                        },
                        `Move ${object}`,
                        "default"
                      )}
                  </div>
                )}
              </div>
            ),
            key: tagKey,
            isLeaf: true,
            className: "tag-node",
            style: tagMatches
              ? { backgroundColor: treeTheme.colors.warningBg }
              : {},
          };
        }) || [];

      const childNodes = node.child ? convertTreeData(node.child, node) : [];
      const labelMatch =
        searchValue &&
        node.label.toLowerCase().includes(searchValue.toLowerCase());
      const descMatch =
        searchValue &&
        node.description.toLowerCase().includes(searchValue.toLowerCase());
      const isMatched = labelMatch || descMatch;
      const isExpanded = expandedKeys.includes(node.id);

      return {
        title: (
          <div className="node-title-container" style={treeStyles.nodeTitle}>
            <div style={treeStyles.nodeContent}>
              {getNodeIcon(node, isExpanded)}
              <div style={treeStyles.nodeLabel}>
                {labelMatch ? (
                  <span>
                    {node.label
                      .split(new RegExp(`(${searchValue})`, "i"))
                      .map((part, i) =>
                        part.toLowerCase() === searchValue.toLowerCase() ? (
                          <span key={i} style={treeStyles.searchHighlight}>
                            {part}
                          </span>
                        ) : (
                          part
                        )
                      )}
                  </span>
                ) : (
                  node.label
                )}
              </div>
              <div style={treeStyles.nodeDescription}>
                {descMatch ? (
                  <span>
                    {node.description
                      .split(new RegExp(`(${searchValue})`, "i"))
                      .map((part, i) =>
                        part.toLowerCase() === searchValue.toLowerCase() ? (
                          <span key={i} style={treeStyles.searchHighlight}>
                            {part}
                          </span>
                        ) : (
                          part
                        )
                      )}
                  </span>
                ) : (
                  node.description
                )}
              </div>
            </div>

            {editmode && (
              <div
                className="node-action-buttons"
                style={treeStyles.nodeActionButtons}
              >
                {activeAction === "add" && (
                  <>
                    {!node?.tags?.length &&
                      getActionButton(
                        <PlusCircleOutlined />,
                        (e) => {
                          e.stopPropagation();
                          setCurrentParent(node);
                          setIsModalVisible(true);
                        },
                        `Add New Node under ${node?.label}`,
                        "default"
                      )}
                    {node?.child?.length === 0 &&
                      getActionButton(
                        <TagOutlined />,
                        (e) => {
                          e.stopPropagation();
                          setCurrentTagParent(node);
                          setIsTagModalVisible(true);
                        },
                        `Add New ${object} under ${node?.label}`,
                        "default"
                      )}
                    {getActionButton(
                      <EditOutlined />,
                      (e) => {
                        e.stopPropagation();
                        setCurrentEditNode(node);
                        editForm.setFieldsValue({
                          description: node.description,
                        });
                        setIsEditModalVisible(true);
                      },
                      `Change Description for ${node?.label}`,
                      "default"
                    )}
                  </>
                )}

                {activeAction === "move" &&
                  node.id !== "1" &&
                  !selectedNode &&
                  !selectedTag &&
                  getActionButton(
                    <SwapOutlined />,
                    (e) => {
                      e.stopPropagation();
                      handleSelectNode(node);
                    },
                    `Move ${node?.label}`,
                    "default"
                  )}

                {selectedNode &&
                  node?.isParent &&
                  originalParent.label !== node.label &&
                  node?.tags?.length === 0 &&
                  getActionButton(
                    <ArrowLeftOutlined />,
                    (e) => {
                      e.stopPropagation();
                      handlePlaceNode(node);
                    },
                    `Put moved Node back to ${node?.label}`,
                    "primary"
                  )}

                {selectedTag &&
                  node.isParent &&
                  originalParent?.label !== node?.label &&
                  node?.child?.length === 0 &&
                  getActionButton(
                    <LeftSquareOutlined />,
                    (e) => {
                      e.stopPropagation();
                      handlePlaceTag(node);
                    },
                    `Put moved ${object} back to ${node?.label}`,
                    "primary"
                  )}

                {activeAction === "delete" &&
                  node?.id !== "1" &&
                  getActionButton(
                    <DeleteOutlined />,
                    (e) => {
                      e.stopPropagation();
                      handleDeleteNode(node, parentNode);
                    },
                    `Delete node ${node?.label}`,
                    "default",
                    true
                  )}
              </div>
            )}
          </div>
        ),
        key: node.id,
        children: [...childNodes, ...tagNodes],
        isLeaf: !node.isParent && tagNodes.length === 0,
        className: node.isParent ? "parent-node" : "leaf-node",
        style: isMatched ? { backgroundColor: treeTheme.colors.warningBg } : {},
        dataRef: node,
      };
    });
  };

  // Search functionality
  const onSearch = (value) => {
    setSearchValue(value);

    if (!value) {
      setExpandedKeys([]);
      return;
    }

    const matchedKeys = fullNodeList
      .filter((node) => {
        const matchesLabel = node.label
          .toLowerCase()
          .includes(value.toLowerCase());
        const matchesDesc = node.description
          .toLowerCase()
          .includes(value.toLowerCase());
        return matchesLabel || matchesDesc;
      })
      .map((node) => node.key);

    const parentKeys = new Set();

    const findParentKeys = (nodes, targetKey) => {
      for (const node of nodes) {
        if (
          node.id === targetKey ||
          (targetKey.startsWith(node.id) && targetKey.includes("-tag-"))
        ) {
          return true;
        }

        if (node.child) {
          if (findParentKeys(node.child, targetKey)) {
            parentKeys.add(node.id);
            return true;
          }
        }
      }
      return false;
    };

    matchedKeys.forEach((key) => {
      const nodeKey = key.includes("-tag-") ? key.split("-tag-")[0] : key;
      findParentKeys(rawTreeData, nodeKey);
    });

    setExpandedKeys([...matchedKeys, ...parentKeys]);
    setAutoExpandParent(true);
  };

  const addToChangeLog = (type, description) => {
    const id = uuidv4()
    const updatedBy = userData?.emailId || "";
     const updatedOn =`/Date(${new Date().getTime()})/` || "";
    dispatch(updateToChangeLog({ id, type, description ,updatedBy ,updatedOn }));
  };

  //function to find whether node is present in UI tree
  const findLabel = (node, label) => {
    if (node.label === label) {
      return true;
    }
    if (node.child && node.child.length > 0) {
      for (let i = 0; i < node.child.length; i++) {
        if (findLabel(node.child[i], label)) {
          return true;
        }
      }
    }
    return false;
  };

  // Handle node addition
  const handleAddNode = async () => {
    try {
      const values = await form.validateFields();
      const { label, description } = values;
      // 1. First check in UI tree
      const findNodeInUITree = findLabel(rawTreeData[0], label);
      if (findNodeInUITree) {
        message.error(`Node "${label}" already exists in the hierarchy!`);
        return;
      }

      const findDuplicateDescInTreeData = findDuplicateDescription(
        rawTreeData[0],
        description
      );

      if (findDuplicateDescInTreeData) {
        message.error(
          `Description "${description}" already exists in the hierarchy!`
        );
        return;
      }

      // 2. Then check in database/hierarchy
      const duplicateResponse = await checkForNodeDuplicacy(
        label,
        object,
        requestorPayload[MODULE_KEY_MAP?.[object]?.CTRL_AREA]?.[0]?.code ||
          reduxPayload?.ControllingArea ||
          "",
        "",
        ""
      );

      // 3. Check the duplicate response structure
      if (duplicateResponse?.body?.isDbDuplicate) {
        message.error(
          `Node "${label}" already exists in some ongoing request!`
        );
        return;
      }

      if (
        duplicateResponse?.body?.PresentInHier === "X" ||
        duplicateResponse.body.PresentInCA === "X" ||
        duplicateResponse.body?.PresentInCOA === "X"
      ) {
        message.error(`Node "${label}" already exists in the hierarchy!`);
        return;
      }

      const newNode = {
        id: currentParent
          ? generateNextChildId(currentParent)
          : `0-${rawTreeData.length}`, // For root nodes
        label,
        description,
        isParent: true,
        child: [],
      };

      // Update rawTreeData based on where we're adding the node
      const updatedTreeData = currentParent
        ? addChildNode(rawTreeData, currentParent.id, newNode)
        : [...rawTreeData, newNode];
      addToChangeLog(
        "ADD NODE",
        `${label} added under ${currentParent?.label} `
      );
      dispatch(
        addNode({ parentNode: `${currentParent?.label}`, newNode: `${label}` })
      );
      dispatch(
        addDescription({
          nodePath: `${currentParent?.label}`,
          description: `${description}`,
        })
      );
      dispatch(setTreeData(updatedTreeData));
      setIsModalVisible(false);
      form.resetFields();

      // Expand the parent to show the new node
      if (currentParent) {
        setExpandedKeys((prev) => [...prev, currentParent.id]);
      }

      message.success(`${label} added successfully`);
    } catch (error) {
      console.error("Validation failed:", error);
    }
  };

  //Check for a object present in UI Tree
  const findTag = (tree, tag) => {
    if (tree.tags && tree.tags.includes(tag)) {
      return true;
    }
    if (tree.child && tree.child.length > 0) {
      for (let i = 0; i < tree.child.length; i++) {
        if (findTag(tree.child[i], tag)) {
          return true;
        }
      }
    }
    return false;
  };

  const handleAddTag = async () => {
    try {
      const values = await tagForm.validateFields();
      const newTag = values.tag;

      const findTagInUITree = findTag(rawTreeData[0], newTag);

      if (findTagInUITree) {
        message.error(`${object} "${newTag}" already exists in the hierarchy!`);
        return;
      }

      const duplicateResponse = await checkForObjectDuplicacy(
        newTag,
        object,
        requestorPayload[MODULE_KEY_MAP?.[object]?.CTRL_AREA]?.[0]?.code ||
          reduxPayload?.ControllingArea ||
          "",
        "",
        requestorPayload?.[MODULE_KEY_MAP?.[object]?.CTR_GRP]?.[0]?.code ||
          requestorPayload?.[MODULE_KEY_MAP?.[object]?.CTR_GRP] ||
          reduxPayload?.ParentNode ||
          ""
      );

      if (duplicateResponse?.body.PresentInCA === "X" || duplicateResponse?.body.PresentInCOA === "X") {
        // valid case: both keys exist and equal "X"
        // proceed normally
      } else {
        message.error(`Invalid ${object}`);
        return;
      }

      if (duplicateResponse?.body?.isDuplicate) {
        message.error(`${object} "${newTag}" already exists in the database!`);
        return;
      }

      if (duplicateResponse.body.PresentInHier === "X") {
        message.error(`${object} "${newTag}" already exists in the hierarchy!`);
        return;
      }

      const updatedTreeData = addTagToNode(
        rawTreeData,
        currentTagParent.id,
        newTag
      );
      addToChangeLog(
        `ADD ${object}`,
        ` ${newTag} added to ${currentTagParent?.label} `
      );
      dispatch(
        addTag({ nodePath: `${currentTagParent?.label}`, tag: `${newTag}` })
      );
      dispatch(setTreeData(updatedTreeData));
      setIsTagModalVisible(false);
      tagForm.resetFields();
      setExpandedKeys((prev) => [...prev, currentTagParent.id]);
      message.success(`${newTag} added successfully`);
    } catch (error) {
      console.error("Validation failed:", error);
    }
  };

  //Check for duplicate description in UI
  const findDuplicateDescription = (tree, description) => {
    if (tree.description === description) {
      return true;
    }

    if (tree.child && tree.child.length > 0) {
      for (let i = 0; i < tree.child.length; i++) {
        if (findDuplicateDescription(tree.child[i], description)) {
          return true;
        }
      }
    }

    return false;
  };

  // Edit node description
  const handleEditDescription = async () => {
    try {
      const values = await editForm.validateFields();
      const updatedDescription = values.description;

      const findDuplicateDescInTreeData = findDuplicateDescription(
        rawTreeData[0],
        updatedDescription
      );

      if (findDuplicateDescInTreeData) {
        message.error(
          `Description "${updatedDescription}" already exists in the hierarchy!`
        );
        return;
      }
      const duplicateDescResponse = await checkForDescriptionDuplicacy(
        currentEditNode.label, // Using node label as identifier
        object,
        requestorPayload[MODULE_KEY_MAP?.[object]?.CTRL_AREA]?.[0]?.code ||
          reduxPayload?.ControllingArea ||
          "",
        "",
        ""
      );

      if (
        Object.keys(duplicateDescResponse.body).length != 0 &&
        duplicateDescResponse?.body?.isDbDuplicate === true
      ) {
        message.error(
          `Description "${updatedDescription}" already present in some ongoing request!`
        );
        return;
      }

      const updatedTreeData = updateNodeDescription(
        rawTreeData,
        currentEditNode.id,
        updatedDescription
      );
      addToChangeLog(
        "CHANGED DESCRIPTION",
        `${currentEditNode?.label} Node description changed from ${currentEditNode?.description} to ${updatedDescription} `
      );
      dispatch(
        editDescription({
          nodePath: `${currentEditNode?.label}`,
          newDescription: `${currentEditNode?.description}`,
        })
      );
      dispatch(setTreeData(updatedTreeData));
      setIsEditModalVisible(false);
      editForm.resetFields();
      message.success(`Description updated successfully`);
    } catch (error) {
      console.error("Validation failed:", error);
    }
  };

  const handleRemoveTag = async (tag, parentNode) => {
    try {
      // Remove tag from current node
      const updatedTree = removeTagFromNodeForMovement(
        [...rawTreeData],
        parentNode.id,
        tag
      );
      addToChangeLog(
        `REMOVE ${object} `,
        `${tag} removed from ${parentNode?.label} `
      );
      dispatch(
        removeTag({
          tagPath: `${parentNode?.label}$$${tag}`,
        })
      );
      dispatch(setTreeData(updatedTree));

      setIsEditModalVisible(false);
      editForm.resetFields();
      message.success(`${object} ${tag} Removed Successfully`);
    } catch (error) {
      console.error("Validation failed:", error);
    }
  };

  // When node is placed, add it to the new parent
  const handlePlaceNode = (parentNode) => {
    if (!selectedNode) return;

    // Step 1: Remove node from current position
    const cleanedTree = removeNode([...rawTreeData], selectedNode.id);

    // Step 2: Add node to new parent
    const treeWithNodeAdded = addChildNode(
      cleanedTree,
      parentNode.id,
      selectedNode
    );
    // Step 3: Reindex the entire tree
    const reindexedTree = reindexTree(treeWithNodeAdded);
    addToChangeLog(
      "MOVE NODE",
      `${selectedNode?.label} Node moved from ${selectedNode?.label} to ${parentNode?.label} `
    );
    dispatch(
      completeNodeMove({
        newParentNode: `${parentNode?.label}`,
        movedNode: `${selectedNode?.label}`,
      })
    );
    dispatch(setTreeData(reindexedTree));
    setSelectedNode(null);
    setOriginalParent(null);
    message.success(`Moved ${selectedNode.label} to ${parentNode.label}`);
  };

  // Handle tag selection for moving
  const handleSelectTag = (tag, parentNode) => {
    // Remove tag from current node
    const updatedTree = removeTagFromNodeForMovement(
      [...rawTreeData],
      parentNode.id,
      tag
    );
    setOriginalParent(parentNode);
    dispatch(
      selectTagForMove({
        nodePath: `${parentNode?.label}`,
        tag: `${tag}`,
      })
    );
    dispatch(setTreeData(updatedTree));

    setSelectedTag({ tag, sourceNodeId: parentNode.id });
    message.info(`Moving ${object} ${tag} - select destination node`);
  };

  const handleDeleteNode = (node, parentNode) => {
    confirm({
      title: `Delete ${node.label}?`,
      content:
        "Deleting node will remove all Profit Centers attached to the nodes & Sub-Nodes from this Hierarchy",
      okText: "Delete",
      okType: "danger",
      cancelText: "Cancel",
      onOk() {
        let updatedTree = removeNodeForDelete([...rawTreeData], node.id);
        updatedTree = reassignIds(updatedTree);
        addToChangeLog("DELETE NODE", `${node?.label} Node deleted `);
        dispatch(
          deleteNode({ nodePath: `${parentNode?.label}$$${node?.label}` })
        );

        dispatch(setTreeData(updatedTree));
        message.success(`Deleted ${node.label} and Updated Hierarchy`);
      },
    });
  };

  // Reassign IDs while maintaining the hierarchical structure
  const reassignIds = (nodes, parentId = "") => {
    return nodes.map((node, index) => {
      const newId = parentId === "" ? `${index}` : `${parentId}-${index}`;
      return {
        ...node,
        id: newId,
        child: node.child ? reassignIds(node.child, newId) : [],
      };
    });
  };

  const removeNodeForDelete = (nodes, nodeId) => {
    return nodes
      .map((node) => {
        const updatedNode = {
          ...node,
          child: node.child ? removeNodeForDelete(node.child, nodeId) : [],
        };
        return updatedNode;
      })
      .filter((node) => node.id !== nodeId);
  };

  // Helper to remove tag from node
  const removeTagFromNodeForMovement = (nodes, nodeId, tag) => {
    return nodes.map((node) => {
      if (node.id === nodeId) {
        return {
          ...node,
          tags: node.tags?.filter((t) => t !== tag) || [],
        };
      }
      if (node.child) {
        return {
          ...node,
          child: removeTagFromNodeForMovement(node.child, nodeId, tag),
        };
      }
      return node;
    });
  };

  // Handle placing tag to new node
  const handlePlaceTag = (targetNode) => {
    if (!selectedTag) return;

    // Add tag to target node
    const updatedTree = addTagToNode(
      [...rawTreeData],
      targetNode.id,
      selectedTag.tag
    );
    addToChangeLog(
      `MOVE ${object}`,
      `${selectedTag?.tag} ${object} moved to ${targetNode.label} `
    );
    dispatch(
      completeTagMove({
        newNodePath: `${targetNode?.label}`,
        tag: `${selectedTag.tag}`,
      })
    );
    dispatch(setTreeData(updatedTree));
    setOriginalParent(null);
    setSelectedTag(null);
    message.success(
      `Moved ${object} ${selectedTag.tag} to ${targetNode.label}`
    );
  };

  const reindexTree = (nodes, prefix = "") => {
    return nodes.map((node, index) => {
      const newId = prefix === "" ? `${index}` : `${prefix}-${index}`;
      const updatedChild = node.child ? reindexTree(node.child, newId) : [];
      return {
        ...node,
        id: newId,
        child: updatedChild,
      };
    });
  };

  const removeNode = (nodes, nodeId) => {
    return nodes
      .map((node) => {
        if (node.child) {
          return {
            ...node,
            child: removeNode(node.child, nodeId),
          };
        }
        return node;
      })
      .filter((node) => node.id !== nodeId);
  };

  // Helper function to add child node while maintaining rawTreeData structure
  const addChildNode = (nodes, parentId, newNode) => {
    return nodes.map((node) => {
      if (node.id === parentId) {
        return {
          ...node,
          child: [...(node.child || []), newNode],
        };
      }
      if (node.child) {
        return {
          ...node,
          child: addChildNode(node.child, parentId, newNode),
        };
      }
      return node;
    });
  };

  // Helper to add tag to node
  const addTagToNode = (nodes, nodeId, newTag) => {
    return nodes.map((node) => {
      if (node.id === nodeId) {
        return {
          ...node,
          tags: [...(node.tags || []), newTag],
        };
      }
      if (node.child) {
        return { ...node, child: addTagToNode(node.child, nodeId, newTag) };
      }
      return node;
    });
  };

  // Find node by ID
  const findNodeById = (nodes, id) => {
    for (const node of nodes) {
      if (node.id === id) return node;
      if (node.child) {
        const found = findNodeById(node.child, id);
        if (found) return found;
      }
    }
    return null;
  };

  // Cancel tag movement
  const handleCancelMove = () => {
    // Return tag to original node
    const updatedTree = addTagToNode(
      [...rawTreeData],
      selectedTag.sourceNodeId,
      selectedTag.tag
    );
    dispatch(setTreeData(updatedTree));
    setSelectedTag(null);
    setOriginalParent(null);
  };

  const onExpand = (keys) => {
    setExpandedKeys(keys);
    setAutoExpandParent(false);
  };

  const expandAll = () => {
    const allKeys = getAllNodeKeys(rawTreeData);
    setExpandedKeys(allKeys);
  };

  const collapseAll = () => {
    setExpandedKeys([]);
  };

  // Helper to update node description
  const updateNodeDescription = (nodes, nodeId, newDescription) => {
    return nodes.map((node) => {
      if (node.id === nodeId) {
        return {
          ...node,
          description: newDescription,
        };
      }
      if (node.child) {
        return {
          ...node,
          child: updateNodeDescription(node.child, nodeId, newDescription),
        };
      }
      return node;
    });
  };

  const removeTagFromNode = (nodes, nodeId, pcIndex) => {
    return nodes.map((node) => {
      if (node.id === nodeId) {
        // Remove the tags from the node's tags array
        node.tags.splice(pcIndex, 1);
      } else if (Array.isArray(node.child)) {
        // Recursively update child nodes
        node.child = removeTagFromNode(node.child, nodeId, pcIndex);
      }
      return node;
    });
  };

  const handleSelectNode = (node) => {
    const parent = findParentNode(rawTreeData, node.id);
    setOriginalParent(parent);

    const updatedTree = removeNode([...rawTreeData], node.id);
    dispatch(
      selectNodeForMove({
        parentNode: `${parent?.label}`,
        selectedNode: `${node?.label}`,
      })
    );
    dispatch(setTreeData(updatedTree));
    setSelectedNode(node);
    message.info(`Moving ${node.label} - select destination`);
  };

  const handleCancelMoveNode = () => {
    if (!selectedNode || !originalParent) return;

    // 1. Create a deep clone of the current tree
    const updatedTree = JSON.parse(JSON.stringify(rawTreeData));

    // 2. Track if restoration was successful
    let restorationComplete = false;

    // 3. Recursive function to restore node to exact original position
    const restoreToExactPosition = (nodes) => {
      for (let i = 0; i < nodes.length; i++) {
        // Check if current node is the original parent
        if (nodes[i].id === originalParent.id) {
          if (!nodes[i].child) nodes[i].child = [];

          // Find the original position by comparing with sibling IDs
          let insertPosition = 0;
          while (
            insertPosition < nodes[i].child.length &&
            nodes[i].child[insertPosition].id.localeCompare(selectedNode.id) < 0
          ) {
            insertPosition++;
          }

          // Insert at the correct position
          nodes[i].child.splice(insertPosition, 0, selectedNode);
          return true;
        }

        // Recursively check children
        if (nodes[i].child && restoreToExactPosition(nodes[i].child)) {
          return true;
        }
      }
      return false;
    };

    // 4. Execute the restoration
    restorationComplete = restoreToExactPosition(updatedTree);

    if (restorationComplete) {
      dispatch(setTreeData(updatedTree));
      message.success(
        `${selectedNode.label} restored to exact original position`
      );
    } else {
      message.error("Could not restore node - parent structure changed");
    }

    // 5. Reset state
    setSelectedNode(null);
    setOriginalParent(null);
    dispatch(selectNodeForMove({ parentNode: "", selectedNode: "" }));
  };

  const findParentNode = (nodes, childId) => {
    for (const node of nodes) {
      if (node.child?.some((child) => child.id === childId)) {
        return node;
      }
      if (node.child) {
        const found = findParentNode(node.child, childId);
        if (found) return found;
      }
    }
    return null;
  };

  const treeData = useMemo(
    () => convertTreeData(rawTreeData),
    [rawTreeData, searchValue, selectedNode, activeAction]
  );

  useEffect(() => {
    setExpandedKeys(getAllNodeKeys(rawTreeData));
  }, [rawTreeData]);

  // Toggle expand/collapse all
  const toggleExpandAll = () => {
    if (expandedKeys.length > 0) {
      setExpandedKeys([]);
    } else {
      setExpandedKeys(getAllNodeKeys(rawTreeData));
    }
  };

  const handleAdd = () => {
    setActiveAction("add");
    // your add logic here
  };

  const handleRemove = () => {
    setActiveAction("remove");
    // your remove logic here
  };

  const handleMove = () => {
    setActiveAction("move");
    // your move logic here
  };

  const handleDelete = () => {
    setActiveAction("delete");
    // your delete logic here
  };

  const columns = [
    {
      title: "Operation",
      dataIndex: "type",
      key: "type",
    },
    {
      title: "Description",
      dataIndex: "description",
      key: "description",
    },
  ];

  return (
    <div style={treeStyles.container}>
      <style>
        {`
          .hierarchy-tree-container .ant-tree-node-content-wrapper:hover .node-action-buttons {
            opacity: 1 !important;
          }
          .hierarchy-tree-container .parent-node > .ant-tree-node-content-wrapper {
            font-weight: 600;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
          }
          .hierarchy-tree-container .tag-node > .ant-tree-node-content-wrapper {
            background: linear-gradient(135deg, #e0f7fa 0%, #b2ebf2 100%);
            border-left: 3px solid ${treeTheme.colors.info};
            margin-left: 8px;
            }
          .hierarchy-tree-container .leaf-node > .ant-tree-node-content-wrapper {
            background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
          }
          .hierarchy-tree-container .ant-tree-switcher {
            background: none !important;
          }
          .hierarchy-tree-container .ant-tree-node-selected > .ant-tree-node-content-wrapper {
            background: ${treeTheme.colors.selectedBg} !important;
            border-radius: ${treeTheme.borderRadius.sm};
          }
          .hierarchy-tree-container .ant-tree-treenode {
            padding: 2px 0;
          }
          .hierarchy-tree-container .ant-tree-node-content-wrapper {
            border-radius: ${treeTheme.borderRadius.sm};
            transition: ${treeTheme.transitions.default};
            min-height: 32px;
            display: flex;
            align-items: center;
          }
          .hierarchy-tree-container .ant-tree-node-content-wrapper:hover {
            background: ${treeTheme.colors.hoverBg} !important;
          }
        `}
      </style>

      {/* Action Buttons Header */}
      <div style={treeStyles.actionButtonsContainer}>
        <div
          style={{
            display: "flex",
            gap: treeTheme.spacing.sm,
            alignItems: "center",
          }}
        >
          {editmode && (
            <Space>
              <Button
                type={activeAction === "add" ? "primary" : "default"}
                onClick={() => setActiveAction("add")}
                icon={<PlusCircleOutlined />}
                style={treeStyles.actionButton}
              >
                Add
              </Button>
              <Button
                type={activeAction === "move" ? "primary" : "default"}
                onClick={() => setActiveAction("move")}
                icon={<SwapOutlined />}
                style={treeStyles.actionButton}
              >
                Move
              </Button>
              <Button
                type={activeAction === "remove" ? "primary" : "default"}
                onClick={() => setActiveAction("remove")}
                icon={<MinusCircleOutlined />}
                style={treeStyles.actionButton}
              >
                Remove
              </Button>
              <Button
                type={activeAction === "delete" ? "primary" : "default"}
                onClick={() => setActiveAction("delete")}
                icon={<DeleteOutlined />}
                danger={activeAction === "delete"}
                style={treeStyles.actionButton}
              >
                Delete
              </Button>
            </Space>
          )}
        </div>

        <Space>
          <Button
            icon={<ExpandOutlined />}
            onClick={expandAll}
            style={treeStyles.actionButton}
          >
            Expand All
          </Button>
          <Button
            icon={<CompressOutlined />}
            onClick={collapseAll}
            style={treeStyles.actionButton}
          >
            Collapse All
          </Button>
          {/* {editmode && (
            <Button
              icon={<HistoryOutlined />}
              onClick={() => setIsChangeLogVisible(true)}
              style={treeStyles.actionButton}
            >
              Change Log
            </Button>
          )} */}
        </Space>
      </div>

      {/* Move Mode Alert */}
      {(selectedNode || selectedTag) && (
        <Alert
          message={
            <div>
              <InfoCircleOutlined style={{ marginRight: 8 }} />
              {selectedNode
                ? `Moving node "${selectedNode.label}" - Click on a parent node to place it`
                : `Moving tag "${selectedTag.tag}" - Click on a parent node to place it`}
            </div>
          }
          type="warning"
          showIcon={false}
          style={treeStyles.moveAlert}
          action={
            <Button
              size="small"
              type="text"
              onClick={() => {
                selectedNode ? handleCancelMoveNode() : handleCancelMove();
              }}
              icon={<CloseOutlined />}
            >
              Cancel
            </Button>
          }
        />
      )}

      {/* Search Bar */}
      <div style={treeStyles.searchContainer}>
        <Search
          placeholder={`Search Node, Descriptions or ${object}...`}
          allowClear
          onSearch={onSearch}
          onChange={(e) => onSearch(e.target.value)}
          style={{ flex: 1 }}
          prefix={<SearchOutlined />}
        />
      </div>

      {/* Tree Container */}
      <div
        className="hierarchy-tree-container"
        style={treeStyles.treeContainer}
      >
        <Tree
          showLine={{ showLeafIcon: false }}
          showIcon={false}
          switcherIcon={<DownOutlined />}
          treeData={treeData}
          onExpand={onExpand}
          expandedKeys={expandedKeys}
          autoExpandParent={autoExpandParent}
          height={600}
          virtual={true}
        />
      </div>

      {/* Add Node Modal */}
      <Modal
        title={`Add New Node under "${currentParent?.label}"`}
        open={isModalVisible}
        onOk={() => form.submit()}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
          setCurrentParent(null);
        }}
        okText="Add Node"
        cancelText="Cancel"
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleAddNode}
          initialValues={{ isParent: false }}
        >
          <Item
            name="label"
            label="Node Label"
            rules={[
              { required: true, message: "Please enter node label" },
              { max: 10, message: "Label cannot exceed 10 characters" },
            ]}
          >
            <Input
              placeholder="Enter Node Label"
              maxLength={10}
              showCount
              style={{ textTransform: "uppercase" }}
              onChange={(e) => {
                // Convert to uppercase
                const upperValue = e.target.value.replace(
                  /[^a-zA-Z0-9_\/-]/g,
                  ""
                );
                // Update the form value
                form.setFieldsValue({ label: upperValue?.toUpperCase() });
              }}
            />
          </Item>

          <Item
            name="description"
            label="Description"
            rules={[
              { required: true, message: "Please Enter Description" },
              { max: 40, message: "Description cannot exceed 40 characters" },
            ]}
          >
            <Input
              placeholder="Enter Node Description"
              style={{ textTransform: "uppercase" }}
              maxLength={40}
              showCount
              onChange={(e) => {
                const formattedValue = e.target.value
                  .replace(/[^a-zA-Z0-9-&()#, ]/g, "")
                  .replace(/\s{2,}/g, " ")
                  .replace(/\s*([-&()#,])\s*/g, "$1")
                  .trimStart()
                  .toUpperCase();

                form.setFieldsValue({ description: formattedValue });
              }}
            />
          </Item>
        </Form>
      </Modal>

      {/* Add Tag Modal */}
      <Modal
        title={`Add New ${object} to "${currentTagParent?.label || "node"}"`}
        open={isTagModalVisible}
        onOk={() => tagForm.submit()}
        onCancel={() => {
          setIsTagModalVisible(false);
          tagForm.resetFields();
          setCurrentTagParent(null);
        }}
        okText={`Add`}
        cancelText="Cancel"
      >
        <Form form={tagForm} layout="vertical" onFinish={handleAddTag}>
          <Item
            name="tag"
            label={`${object}`}
            rules={[
              {
                required: true,
                message: `Please enter ${object}`,
              },
              {
                max: 10,
                message: `${object} name cannot exceed 10 characters`,
              },
            ]}
          >
            <Input
              placeholder={`Enter ${object}`}
              style={{ textTransform: "uppercase" }}
              maxLength={10}
              showCount
              onChange={(e) => {
                const formattedValue = e.target.value
                  .replace(/[^a-zA-Z0-9-&()#, ]/g, "")
                  .replace(/\s{2,}/g, " ")
                  .replace(/\s*([-&()#,])\s*/g, "$1")
                  .trimStart()
                  .toUpperCase();

                tagForm.setFieldsValue({ tag: formattedValue });
              }}
            />
          </Item>
        </Form>
      </Modal>

      {/* Edit Description Modal */}
      <Modal
        title={`Edit Description for "${currentEditNode?.label}"`}
        open={isEditModalVisible}
        onOk={() => editForm.submit()}
        onCancel={() => {
          setIsEditModalVisible(false);
          editForm.resetFields();
          setCurrentEditNode(null);
        }}
        okText="Update Description"
        cancelText="Cancel"
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={handleEditDescription}
        >
          <Item
            name="description"
            label="Description"
            rules={[
              { required: true, message: "Please Enter Description" },

              { max: 40, message: "Description cannot exceed 40 characters" },
            ]}
          >
            <Input
              showCount
              maxLength={40}
              placeholder="Enter Node Description"
              onChange={(e) => {
                // Convert to uppercase
                const upperValue = e.target.value.toUpperCase();
                // Update the form value
                editForm.setFieldsValue({ description: upperValue });
              }}
            />
          </Item>
        </Form>
      </Modal>

      {/* Change Log Modal */}
      {/* <Modal
        title="Change Log"
        open={isChangeLogVisible}
        onCancel={() => setIsChangeLogVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsChangeLogVisible(false)}>
            Close
          </Button>
        ]}
        width={800}
      >
        <Table
          columns={changeLogColumns}
          dataSource={changeLog}
          rowKey={(record, index) => index}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} items`,
          }}
          scroll={{ y: 400 }}
        />
      </Modal> */}
    </div>
  );
};

export default ReusableHierarchyTree;
