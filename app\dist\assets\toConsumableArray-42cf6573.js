import{ex as e,ey as t}from"./index-75c1660a.js";function o(r){if(Array.isArray(r))return e(r)}function a(r){if(typeof Symbol<"u"&&r[Symbol.iterator]!=null||r["@@iterator"]!=null)return Array.from(r)}function n(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function l(r){return o(r)||a(r)||t(r)||n()}const u=Object.freeze(Object.defineProperty({__proto__:null,default:l},Symbol.toStringTag,{value:"Module"}));export{l as _,u as t};
