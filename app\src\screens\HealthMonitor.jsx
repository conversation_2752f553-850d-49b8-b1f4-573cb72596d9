import { useState, useRef, useEffect } from "react";
import { styled } from '@mui/material/styles';
import { 
  Box, Tooltip, IconButton, Paper, Typography, 
  CircularProgress, Stack, Pop<PERSON>, ClickAwayListener, <PERSON>row, Tabs, Tab
} from "@mui/material";
import HealthAndSafetyIcon from '@mui/icons-material/HealthAndSafety';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import RefreshIcon from '@mui/icons-material/Refresh';
import { destination_DocumentManagement, destination_MaterialMgmt } from "../destinationVariables";
import { doAjax } from "../components/Common/fetchService";
import { END_POINTS } from "@constant/apiEndPoints";
import { 
  LOADING_MESSAGE, 
  SUCCESS_MESSAGES, 
  ERROR_MESSAGES_HEALTH,
  SERVICE_NAME_MAP
} from "@constant/enum";
import { colors } from '@constant/colors';
import  useLang  from "@hooks/useLang";

const API_ENDPOINTS = [
  {
    name: "Database Service",
    checkHealth: (onSuccess, onError) => doAjax(
      `/${destination_MaterialMgmt}/actuator/health/db/CW-MDG-MM-DEV`,
      "get",
      onSuccess,
      onError
    ),
    validateResponse: (response) => response?.status === 'UP',
    successMessage: SUCCESS_MESSAGES.DB_OPERATIONAL,
    failureMessage: ERROR_MESSAGES_HEALTH.DB_DOWN
  },
  {
    name: "Email Services",
    checkHealth: (onSuccess, onError) => doAjax(
      `/${destination_MaterialMgmt}/actuator/health/mail`,
      "get",
      onSuccess,
      onError
    ),
    validateResponse: (response) => response?.status === 'UP',
    successMessage: SUCCESS_MESSAGES.EMAIL_OPERATIONAL,
    failureMessage: ERROR_MESSAGES_HEALTH.EMAIL_DOWN
  },
  {
    name: "IDM Services",
    checkHealth: (onSuccess, onError) => doAjax(
      `/${destination_MaterialMgmt}/workflow/getDemoIDMResponse`,
      "get",
      onSuccess,
      onError
    ),
    validateResponse: (response) => response?.status === 'UP',
    successMessage: SUCCESS_MESSAGES.IDM_OPERATIONAL,
    failureMessage: ERROR_MESSAGES_HEALTH.IDM_DOWN
  },
  {
    name: "OData Services",
    checkHealth: (onSuccess, onError) => doAjax(
      `/${destination_MaterialMgmt}/data/getDemoOdataResponse`,
      "get",
      onSuccess,
      onError
    ),
    validateResponse: (response) => response?.status === 'UP',
    successMessage: SUCCESS_MESSAGES.ODATA_OPERATIONAL,
    failureMessage: ERROR_MESSAGES_HEALTH.ODATA_DOWN
  },
  {
    name: "SAP BPA Workflow Services",
    checkHealth: (onSuccess, onError) => doAjax(
      `/${destination_MaterialMgmt}/actuator/health/custom`,
      "get",
      onSuccess,
      onError
    ),
    validateResponse: (response) => response?.details?.["SAP BPA Workflow"] === 'UP',
    successMessage: SUCCESS_MESSAGES.BPA_OPERATIONAL,
    failureMessage: ERROR_MESSAGES_HEALTH.BPA_DOWN
  },
  {
    name: "Document Management Service",
    checkHealth: (onSuccess, onError) => doAjax(
      `/${destination_DocumentManagement}/documentManagement/getDemoDMSResponse`,
      "get",
      onSuccess,
      onError
    ),
    validateResponse: (response) => response?.status === 'UP',
    successMessage: SUCCESS_MESSAGES.DOCUMENT_MANAGEMENT_OPERATIONAL,
    failureMessage: ERROR_MESSAGES_HEALTH.DOCUMENT_MANAGEMENT_DOWN
  },
];
 

// Styled Components
const HealthIconButton = styled(IconButton)(({ theme, healthColor }) => ({
  color: healthColor,
  transition: 'all 0.3s ease',
  // backgroundColor: `rgba(${healthColor === '#e74c3c' ? '231, 76, 60' : 
  //                       healthColor === '#f39c12' ? '243, 156, 18' : 
  //                       '46, 204, 113'}, 0.1)`,
  '&:hover': {
    backgroundColor: `rgba(${healthColor === '#e74c3c' ? '231, 76, 60' : 
                        healthColor === '#f39c12' ? '243, 156, 18' : 
                        '46, 204, 113'}, 0.2)`,
    transform: 'scale(1.05)'
  },
  // boxShadow: `0 0 12px rgba(${healthColor === '#e74c3c' ? '231, 76, 60' : 
  //                          healthColor === '#f39c12' ? '243, 156, 18' : 
  //                          '46, 204, 113'}, 0.3)`,
}));

const GlassPopper = styled(Paper)(({ theme }) => ({
  width: 360,
  maxHeight: 520,
  overflow: 'auto',
  borderRadius: 20,
  border: '1px solid rgba(255, 255, 255, 0.3)',
  position: 'relative',
  background: 'rgba(255, 255, 255, 0.85)',
  backdropFilter: 'blur(12px)',
  padding: 24,
  boxShadow: '0 10px 30px rgba(0, 0, 0, 0.15), 0 2px 8px rgba(0, 0, 0, 0.1)'
}));

const HealthIndicator = styled(Box)(({ theme }) => ({
  position: 'relative',
  width: 160,
  height: 160,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  margin: '0 auto 20px',
}));

const GlassCircle = styled(Box)(({ theme }) => ({
  position: 'absolute',
  width: 130,
  height: 130,
  borderRadius: '50%',
  background: 'rgba(255, 255, 255, 0.7)',
  backdropFilter: 'blur(8px)',
  boxShadow: 'inset 0 4px 15px rgba(0, 0, 0, 0.08)',
  border: '1px solid rgba(255, 255, 255, 0.3)',
  zIndex: 1
}));

const StatusBar = styled(Box)(({ theme, status, completed }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: '14px 16px',
  borderRadius: 16,
  marginBottom: 12,
  position: 'relative',
  minHeight: 56,
  transition: 'all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1)',
  overflow: 'hidden',
  background: completed ? 
    (status === 'success' ? 'rgba(46, 204, 113, 0.35)' : 
    status === 'error' ? 'rgba(231, 76, 60, 0.35)' : 
    'rgba(149, 165, 166, 0.15)') : 
    'rgba(189, 195, 199, 0.1)',
  border: `1px solid ${
    completed ? 
    (status === 'success' ? 'rgba(46, 204, 113, 0.3)' : 
    status === 'error' ? 'rgba(231, 76, 60, 0.3)' : 
    'rgba(149, 165, 166, 0.3)') : 
    'rgba(189, 195, 199, 0.2)'
  }`,
  backdropFilter: 'blur(8px)',
  boxShadow: completed ? 
    (status === 'success' ? '0 4px 12px rgba(46, 204, 113, 0.15)' : 
    status === 'error' ? '0 4px 12px rgba(231, 76, 60, 0.15)' : 
    '0 4px 8px rgba(0, 0, 0, 0.05)') :
    '0 2px 4px rgba(0, 0, 0, 0.05)',
  transform: completed ? 'translateY(0)' : 'translateY(10px)',
  opacity: completed ? 5 : 0.8,
  '&::before': completed ? {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: status === 'success' ? 
      'linear-gradient(135deg, rgba(46, 204, 113, 0.05) 0%, rgba(46, 204, 113, 0.2) 100%)' : 
      'linear-gradient(135deg, rgba(231, 76, 60, 0.05) 0%, rgba(231, 76, 60, 0.2) 100%)',
    zIndex: 0,
  } : {},
  '&:hover': completed ? {
    transform: 'translateY(-2px)',
    boxShadow: status === 'success' ? 
      '0 6px 15px rgba(46, 204, 113, 0.2)' : 
      status === 'error' ? 
      '0 6px 15px rgba(231, 76, 60, 0.2)' : 
      '0 6px 12px rgba(0, 0, 0, 0.07)'
  } : {},
}));

const StatusIcon = styled(Box)(({ theme, status, completed, position }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  borderRadius: '50%',
  width: 36,
  height: 36,
  backgroundColor: 'white',
  boxShadow: '0 3px 10px rgba(0,0,0,0.1)',
  zIndex: 2,
  position: 'absolute',
  transition: 'all 0.5s ease',
  left: position === 'left' ? 12 : 'auto',
  right: position === 'right' ? 12 : 'auto',
}));

const RefreshButton = styled(IconButton)(({ theme }) => ({
  background: 'rgba(46, 204, 113, 0.1)',
  borderRadius: '50%',
  width: 36,
  height: 36,
  boxShadow: '0 3px 10px rgba(0,0,0,0.1)',
  transition: 'all 0.3s ease',
  '&:hover': {
    background: 'rgba(46, 204, 113, 0.2)',
    transform: 'rotate(180deg)'
  },
}));

const HealthMonitor = () => {
  const [open, setOpen] = useState(false);
  const anchorRef = useRef(null);
  const [healthPercentage, setHealthPercentage] = useState(0);
  const [healthPercentage1, setHealthPercentage1] = useState(0);
  const [healthPercentage2, setHealthPercentage2] = useState(0);
  const [selectedTab, setSelectedTab] = useState("current");
  const [previous1, setPrevious1] = useState([]);
  const [previous2, setPrevious2] = useState([]);
  const [apiStatus, setApiStatus] = useState(
    API_ENDPOINTS.map(endpoint => ({
      ...endpoint,
      status: 'pending',
      completed: false,
      loading: false
    }))
  );
  const [animating, setAnimating] = useState(false);
  const { t } = useLang();

  const calculateHealthPercentage = (data) => {
    if (!data || data.length === 0) return 0;
  
    const upCount = data.filter((item) => item.status === "UP").length;
    const percentage = (upCount / 6) * 100;
    return parseFloat(percentage.toFixed(2));
  };
  
  useEffect(() => {
    const healthPrev1 = calculateHealthPercentage(previous1);
    const healthPrev2 = calculateHealthPercentage(previous2);
  
    setHealthPercentage1(healthPrev1);
    setHealthPercentage2(healthPrev2);
  }, [previous1, previous2]);

  useEffect(() => {
    checkApiHealth();
  }, []);


  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
    if (!open) {
      checkApiHealth();
    }
  };

  const handleClose = (event) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }
    setOpen(false);
  };

  // Reset and check all API health statuses
  const checkApiHealth = () => {
    setSelectedTab("current");
    const onSuccess = (previousData) => {
      if (!previousData || !Array.isArray(previousData)) return;
    
      const groupedData = previousData.reduce((acc, item) => {
        if (!acc[item.apiName]) {
          acc[item.apiName] = [];
        }
        acc[item.apiName].push(item);
        return acc;
      }, {});
    
      const latestEntries = [];
      const olderEntries = [];

      Object.values(groupedData).forEach((entries) => {
        if (entries.length === 2) {
          entries.sort((a, b) => new Date(b.lastUpdated) - new Date(a.lastUpdated));
          latestEntries.push(entries[0]);
          olderEntries.push(entries[1]); 
        } else {
          latestEntries.push(entries[0]);
        }
      });
    
      setPrevious1(latestEntries);
      setPrevious2(olderEntries);
    };
    
    const onError = (error) => {
      console.error(error, "API Error");
    };
    setAnimating(true);
    doAjax(
      `/${destination_MaterialMgmt}/${END_POINTS.HEALTH_API.LAST_TWO_RESULTS}`, 
      "get", 
      onSuccess, 
      onError
    ),
    // Reset states
    setHealthPercentage(0);
    setApiStatus(API_ENDPOINTS.map(endpoint => ({
      ...endpoint,
      status: 'pending',
      completed: false,
      loading: false
    })));

    // Check APIs sequentially with a slight delay between each
    API_ENDPOINTS.forEach((endpoint, index) => {
      setTimeout(() => {
        checkSingleApiHealth(index);
      }, index * 600);
    });

    setTimeout(() => {
      setAnimating(false);
    }, API_ENDPOINTS.length * 600 + 1500);
  };

  const formatServiceName = (name) => {
    return SERVICE_NAME_MAP[name] || name;
  };

  const getSelectedData = () => {
    if (selectedTab === "current") return apiStatus;

    const dataToMap = selectedTab === "previous1" ? previous1 : previous2;
    return dataToMap.map(prev => ({
      name: formatServiceName(prev.apiName),
      status: prev.status === "UP" ? "success" : "error",
      completed: true,
      loading: false,
      message: prev.status === "UP" ? "Service is operational" : "Service is down",
      response: { status: prev.status },
    }));
  };

  // Check health for a single API endpoint
  const checkSingleApiHealth = (index) => {
    setApiStatus((prev) =>
      prev.map((api, i) =>
        i === index ? { ...api, loading: true } : api
      )
    );

    API_ENDPOINTS[index].checkHealth(
      (response) => {
        const isHealthy = API_ENDPOINTS[index].validateResponse(response);
  
        setApiStatus((prev) => {
          const newStatus = prev.map((api, i) =>
            i === index
              ? {
                  ...api,
                  status: isHealthy ? 'success' : 'error',
                  completed: true,
                  loading: false,
                  response,
                  message: isHealthy
                    ? API_ENDPOINTS[index].successMessage
                    : API_ENDPOINTS[index].failureMessage,
                }
              : api
          );
         
     
          const healthyServices = newStatus.filter(
            service => service.completed && service.status === 'success'
          ).length;
          const totalCompletedServices = newStatus.filter(
            service => service.completed
          ).length;
         
          if (totalCompletedServices > 0) {
            const newPercentage = (healthyServices / API_ENDPOINTS.length) * 100;
            setHealthPercentage(parseFloat(newPercentage.toFixed(2)));
          }
         
          return newStatus;
        });
  
        if (!isHealthy) {
            setOpen(true)
        }
      },
      (error) => {
        setOpen(true)
        setApiStatus((prev) =>
          prev.map((api, i) =>
            i === index
              ? {
                  ...api,
                  status: 'error',
                  completed: true,
                  loading: false,
                  error,
                  message: API_ENDPOINTS[index].failureMessage,
                }
              : api
          )
        );
      }
    );
  };
  
  
  const getHealthColor = (healthPercentage) => {
    if (healthPercentage < 40) return colors.health.critical;
    if (healthPercentage < 100) return colors.health.warning;
    return colors.health.healthy;
  };

  return (
    <Box>
      <Tooltip title={t("Website Health")} placement="top" arrow>
        <HealthIconButton
          ref={anchorRef}
          onClick={handleToggle}
          aria-controls={open ? 'health-menu' : undefined}
          aria-haspopup="true"
          healthColor={getHealthColor(healthPercentage)}
          sx={{
            animation: animating ? 'pulse 2s infinite' : 'none',
            '@keyframes pulse': {
              '0%': { boxShadow: `0 0 0 0 rgba(${getHealthColor(healthPercentage) === '#e74c3c' ? '231, 76, 60' : 
                                              getHealthColor(healthPercentage) === '#f39c12' ? '243, 156, 18' : 
                                              '46, 204, 113'}, 0.4)` },
              '70%': { boxShadow: `0 0 0 10px rgba(${getHealthColor(healthPercentage) === '#e74c3c' ? '231, 76, 60' : 
                                                    getHealthColor(healthPercentage) === '#f39c12' ? '243, 156, 18' : 
                                                    '46, 204, 113'}, 0)` },
              '100%': { boxShadow: `0 0 0 0 rgba(${getHealthColor(healthPercentage) === '#e74c3c' ? '231, 76, 60' : 
                                                  getHealthColor(healthPercentage) === '#f39c12' ? '243, 156, 18' : 
                                                  '46, 204, 113'}, 0)` },
            }
          }}
        >
          <HealthAndSafetyIcon fontSize="medium" />
        </HealthIconButton>
      </Tooltip>

      <Popper
        open={open}
        anchorEl={anchorRef.current}
        role={undefined}
        placement="bottom-end"
        transition
        disablePortal
        style={{ zIndex: 1300 }}
        modifiers={[
          {
            name: 'offset',
            options: {
              offset: [0, 10],
            },
          },
        ]}
      >
        {({ TransitionProps }) => (
          <Grow
            {...TransitionProps}
            style={{ transformOrigin: 'top right' }}
            timeout={300}
          >
            <GlassPopper
              elevation={6}
              sx={{
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: -10,
                  right: 14,
                  width: 0,
                  height: 0,
                  borderLeft: '10px solid transparent',
                  borderRight: '10px solid transparent',
                  borderBottom: `10px solid rgba(${getHealthColor(healthPercentage) === '#e74c3c' ? '231, 76, 60' : 
                                                  getHealthColor(healthPercentage) === '#f39c12' ? '243, 156, 18' : 
                                                  '46, 204, 113'}, 0.3)`,
                  zIndex: 0,
                },
              }}
            >
              <ClickAwayListener onClickAway={handleClose}>
                <Box sx={{ width: "100%" }}>
                  <Box sx={{ 
                    display: 'flex', 
                    justifyContent: 'space-between', 
                    alignItems: 'center',
                    mb: 2 
                  }}>
                    <Typography variant="h6" sx={{ 
                      fontWeight: "bold", 
                      background: `linear-gradient(90deg, #333333 0%, ${getHealthColor(healthPercentage)} 100%)`,
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                    }}>
                      {t("System Health")}
                    </Typography>
                    <RefreshButton 
                      onClick={checkApiHealth}
                      disabled={animating}
                      size="small"
                    >
                      <RefreshIcon fontSize="small" />
                    </RefreshButton>
                  </Box>
                  <Box sx={{ width: "100%", display: 'flex', justifyContent: 'center' }}>
                  <Tabs 
                    value={selectedTab} 
                    onChange={(_, newTab) => setSelectedTab(newTab)}
                    sx={{
                      minHeight: '35px',
                      borderBottom: 'none',
                      '& .MuiTabs-indicator': {
                        display: 'none',
                      },
                      '& .MuiTab-root': {
                        minHeight: '32px',
                        fontSize: '13px',
                        fontWeight: 500,
                        textTransform: 'none',
                        color: 'rgba(0, 0, 0, 0.6)',
                        borderRadius: '20px',
                        marginRight: '10px',
                        padding: '6px 16px',
                        minWidth: 'unset',
                        transition: 'all 0.3s ease',
                        
                        '&.Mui-selected': {
                          color: getHealthColor(
                            selectedTab === "current" ? healthPercentage : selectedTab === "previous1" ? healthPercentage1 : healthPercentage2
                          ), 
                          fontWeight: 600,
                          backgroundColor: `rgba(${
                            getHealthColor(
                              selectedTab === "current" ? healthPercentage : selectedTab === "previous1" ? healthPercentage1 : healthPercentage2
                            ) === '#e74c3c' ? '231, 76, 60' : 
                            getHealthColor(
                              selectedTab === "current" ? healthPercentage : selectedTab === "previous1" ? healthPercentage1 : healthPercentage2
                            ) === '#f39c12' ? '243, 156, 18' : 
                            '46, 204, 113'
                          }, 0.1)`,
                        },
                        
                        '&:hover': {
                          backgroundColor: 'rgba(0, 0, 0, 0.04)',
                          opacity: 0.8,
                        },
                      },
                      mb: 2,
                    }}
                  >
                    <Tab label={t("Current")} value="current" />
                    <Tab label={t("Recent")} value="previous1" />
                    <Tab label={t("Earlier")} value="previous2" />
                  </Tabs>

                  </Box>
                  <HealthIndicator>
                    <GlassCircle />
                    <Box sx={{ 
                      zIndex: 2, 
                      display: 'flex', 
                      flexDirection: 'column', 
                      alignItems: 'center', 
                      justifyContent: 'center' 
                    }}>
                      <Typography 
                        variant="h4" 
                        sx={{ 
                          fontWeight: 'bold', 
                          color: getHealthColor(
                            selectedTab === "current" ? healthPercentage : selectedTab === "previous1" ? healthPercentage1 : healthPercentage2
                          ), 
                          transition: 'color 0.5s ease',
                          fontSize: '35px'
                        }}
                      >
                        {selectedTab === "current" 
                          ? healthPercentage 
                          : selectedTab === "previous1" 
                          ? healthPercentage1 
                          : healthPercentage2
                        }%
                      </Typography>
                      <Typography variant="caption" sx={{ opacity: 0.7 }}>
                        {t("System Health")}
                      </Typography>
                    </Box>
                    <Box sx={{ 
                      position: 'absolute', 
                      top: 0, 
                      left: 0, 
                      right: 0, 
                      bottom: 0, 
                      zIndex: 0 
                    }}>
                      <CircularProgress
                        variant="determinate"
                        value={100}
                        size={150}
                        thickness={6}
                        sx={{ 
                          color: 'rgba(0, 0, 0, 0.05)',
                          position: 'absolute',
                        }}
                      />
                      <CircularProgress
                        variant="determinate"
                        value={selectedTab === "current" ? healthPercentage : selectedTab === "previous1" ? healthPercentage1 : healthPercentage2}
                        size={150}
                        thickness={6}
                        sx={{ 
                          color: getHealthColor(
                            selectedTab === "current" ? healthPercentage : selectedTab === "previous1" ? healthPercentage1 : healthPercentage2
                          ), 
                          transition: 'color 0.5s ease',
                          position: 'absolute',
                          strokeLinecap: 'round',
                          filter: `drop-shadow(0 0 6px rgba(${getHealthColor(
                            selectedTab === "current" ? healthPercentage : selectedTab === "previous1" ? healthPercentage1 : healthPercentage2
                          ) === '#e74c3c' ? '231, 76, 60' : 
                          getHealthColor(
                            selectedTab === "current" ? healthPercentage : selectedTab === "previous1" ? healthPercentage1 : healthPercentage2
                          ) === '#f39c12' ? '243, 156, 18' : 
                                                         '46, 204, 113'}, 0.5))`,
                        }}
                      />
                    </Box>
                  </HealthIndicator>

                  <Typography variant="subtitle2" sx={{ 
                    mb: 1, 
                    opacity: 0.7, 
                    textAlign: 'center',
                    fontSize: '13px',
                    fontWeight: 500
                  }}>
                    {t("Monitoring")} {API_ENDPOINTS.length} {t("services")}
                  </Typography>

                  <Typography variant="subtitle2" sx={{ 
                    mb: 2, 
                    opacity: 0.7, 
                    textAlign: 'center',
                    fontSize: '12px',
                    fontWeight: 400,
                    color: 'rgba(0, 0, 0, 0.6)'
                  }}>
                    {t("Last updated on")}: {selectedTab === "current" 
                      ? new Date().toLocaleString('en-US', { 
                          hour: 'numeric', 
                          minute: 'numeric',
                          hour12: true,
                          month: 'short',
                          day: 'numeric',
                          year: 'numeric'
                        })
                      : selectedTab === "previous1" && previous1.length > 0
                        ? new Date(previous1[0].lastUpdated).toLocaleString('en-US', {
                            hour: 'numeric',
                            minute: 'numeric',
                            hour12: true,
                            month: 'short',
                            day: 'numeric',
                            year: 'numeric'
                          })
                        : previous2.length > 0
                          ? new Date(previous2[0].lastUpdated).toLocaleString('en-US', {
                              hour: 'numeric',
                              minute: 'numeric',
                              hour12: true,
                              month: 'short',
                              day: 'numeric',
                              year: 'numeric'
                            })
                          : 'N/A'
                    }
                  </Typography>

                  <Stack spacing={1.5}>
                  {getSelectedData().map((api, index) => (
                      <StatusBar 
                        key={index} 
                        status={api.status} 
                        completed={api.completed}
                        sx={{ 
                          animationDelay: `${index * 0.1}s`,
                          transition: `all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) ${index * 0.1}s`
                        }}
                      >
                        <Box sx={{ 
                          display: 'flex', 
                          flexDirection: 'column',
                          zIndex: 1,
                          transition: 'all 0.3s ease',
                          pl: api.loading ? 5 : 0,
                          width: '100%'
                        }}>
                          <Typography 
                            sx={{ 
                              fontWeight: api.completed ? "600" : "normal", 
                              opacity: api.completed ? 1 : 0.7,
                              fontSize: '14px'
                            }}
                          >
                            {api.name}
                          </Typography>
                          
                          {api.completed && (
                            <Typography 
                              variant="caption" 
                              sx={{ 
                                color: api.status === 'success' ? '#1F7D53' : '#CD1818',
                                mt: 0.5,
                                fontWeight: 500,
                                fontSize: '12px'
                              }}
                            >
                              {api.status === 'success' 
                                ? 'Healthy - Response time: ' + (Math.random() * 300).toFixed(0) + 'ms'
                                : 'Service unavailable'}
                            </Typography>
                          )}
                        </Box>
                        
                        {/* Status indicator */}
                        {api.loading && (
                          <StatusIcon 
                            position="left"
                            status={api.status}
                            completed={api.completed}
                          >
                            <CircularProgress size={20} thickness={5} />
                          </StatusIcon>
                        )}
                        
                        {api.completed && (
                          <StatusIcon 
                            position="right"
                            status={api.status}
                            completed={api.completed}
                          >
                            {api.status === 'success' ? (
                              <CheckCircleIcon sx={{ color: '#2ecc71', fontSize: 22 }} />
                            ) : (
                              <CancelIcon sx={{ color: '#e74c3c', fontSize: 22 }} />
                            )}
                          </StatusIcon>
                        )}
                      </StatusBar>
                    ))}
                  </Stack>
                </Box>
              </ClickAwayListener>
            </GlassPopper>
          </Grow>
        )}
      </Popper>
    </Box>
  );
};

export default HealthMonitor;