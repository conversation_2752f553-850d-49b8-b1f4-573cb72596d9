{"costCenter": [{"keyName": "UserResponsible", "endPoint": "getUserResponsible"}, {"keyName": "CostCenterCategory", "endPoint": "getCostCenterCategory"}, {"keyName": "CostingSheet", "endPoint": "getCostingSheet"}, {"keyName": "CountryReg", "endPoint": "getCountry"}, {"keyName": "Juris<PERSON>", "endPoint": "getJurisdiction"}, {"keyName": "BusinessArea", "endPoint": "getBusinessArea"}, {"keyName": "FunctionalArea", "endPoint": "getFunctionalArea"}, {"keyName": "LanguageKey", "endPoint": "getLanguageKey"}, {"keyName": "ActyIndepFormPlngTemp", "endPoint": "getActyIndepFormPln"}, {"keyName": "ActyDepFormPlngTemp", "endPoint": "getActyDepFormPlng"}, {"keyName": "ActyIndepAllocTemp", "endPoint": "getActyIndepAllocTem"}, {"keyName": "ActyDepAllocTemplate", "endPoint": "getActydepAllocTemp"}, {"keyName": "Templ:ActStatKeyFigure", "endPoint": "getTemplActStatKeyFigure"}], "profitCenter": [{"keyName": "UserResponsible", "endPoint": "getUserResponsible"}, {"keyName": "Segment", "endPoint": "getSegment"}, {"keyName": "FormPlanningTemp", "endPoint": "getFormPlanningTemp"}, {"keyName": "CountryReg", "endPoint": "getCountryOrReg"}, {"keyName": "TaxJur", "endPoint": "getJurisdiction"}, {"keyName": "Language", "endPoint": "getLanguageKey"}], "bankKey": [{"keyName": "TimeZone", "endPoint": "getTimezone"}, {"keyName": "CountryReg", "endPoint": "getCountry"}, {"keyName": "Country", "endPoint": "getCountry"}, {"keyName": "Country1", "endPoint": "getCountry"}, {"keyName": "Country2", "endPoint": "getCountry"}, {"keyName": "TaxJurisdiction", "endPoint": "getTaxJurisdiction"}, {"keyName": "Language", "endPoint": "getLanguageKey"}, {"keyName": "TransportZone", "endPoint": "getTransportZone"}, {"keyName": "Undeliverable", "endPoint": "getUndeliverable"}, {"keyName": "Undeliverable1", "endPoint": "getUndeliverable"}, {"keyName": "DeliveryServiceType", "endPoint": "getDeliverySrvType"}, {"keyName": "StructureGroup", "endPoint": "getStructureGroup"}], "generalLedger": [{"keyName": "Language", "endPoint": "getLanguageKey"}, {"keyName": "TradingPartner", "endPoint": "getTradingP<PERSON>ner"}, {"keyName": "<PERSON><PERSON><PERSON><PERSON>", "endPoint": "getSortKey"}, {"keyName": "PlanningLevel", "endPoint": "getPlanningLevel"}, {"keyName": "PlanningLevel", "endPoint": "getPlanningLevel"}, {"keyName": "InternalUOM", "endPoint": "getInternalUOM"}, {"keyName": "InterestIndicator", "endPoint": "getInterestIndicator"}, {"keyName": "InterestCalculationFrequency", "endPoint": "getInterestCalculationFreq"}, {"keyName": "AccountType", "endPoint": "getGLAccountType"}, {"keyName": "FunctionalArea", "endPoint": "getFunctionalArea"}, {"keyName": "ChartOfAccounts", "endPoint": "getChartOfAccounts"}, {"keyName": "Account<PERSON><PERSON><PERSON>cy", "endPoint": "getAccountCurrency"}, {"keyName": "ExchangeRateDiffKey", "endPoint": "getExchangeRateDiffKey"}, {"keyName": "ReconAccountForAccountType", "endPoint": "getReconAccountForAccountType"}]}