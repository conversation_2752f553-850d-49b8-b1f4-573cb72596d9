import React, { useState } from "react";
import StatsGridPC from "./StatsGridPC";
import WorkflowGroupCardPC from "./workFlowGroupCardPC";
import TaskModalPC from "./TaskModalPC";
import "./workflow.css";
import { Typography } from "@mui/material";

const WorkflowDashboardPC = ({ data }) => {
  const [modalData, setModalData] = useState(null);

  const handleShowModal = (taskInfo) => {
    setModalData(taskInfo);
  };

  const handleCloseModal = () => {
    setModalData(null);
  };

  return (
    <div>
      <Typography align="left" variant="h4" component="h2" gutterBottom>
        Workflow Details
      </Typography>
      <StatsGridPC data={data} />
      <div className="workflow-container">
        {Object.entries(data).map(([groupName, groupData]) => (
          <WorkflowGroupCardPC
            key={groupName}
            groupName={groupName}
            groupData={groupData.workflowDetails} // ✅ ORIGINAL structure passed here
            // materialTypes={groupData?.materialTypes} // Optional if available
            onTaskClick={handleShowModal}
          />
        ))}
      </div>
      {modalData && <TaskModalPC task={modalData} onClose={handleCloseModal} />}
    </div>
  );
};

export default WorkflowDashboardPC;
