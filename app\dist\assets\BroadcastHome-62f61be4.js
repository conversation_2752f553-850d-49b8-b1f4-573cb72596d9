import{m as G,n as q,o as K,cB as M,g as _e,l as k,A as Ne,G as d,t as Q,T as x,E as Oe,s as ee,q as ge,e as me,a as e,j as r,an as je,ao as Le,f as Pe,ax as Ue,cc as We,aA as He,at as Ve,au as Ye,ar as ce,aw as Ge,M as ue,r as m,bF as j,F as X,b6 as qe,B as D,am as Ke,dV as xe,I as O,aG as Je,e8 as Ze,S as U,x as E,aD as V,ds as J,f6 as Qe,f7 as Xe,f8 as et,b as tt,h as F,y as Z,C as he,z as at,aj as rt,al as nt,b1 as W,b_ as ot,cl as it,f9 as lt,aC as st,aE as dt,K as _,N}from"./index-17b8d91e.js";import{d as ct}from"./EditOutlined-36c8ca4d.js";import{d as ut,a as ht}from"./SlideshowOutlined-23143d05.js";import{d as pt}from"./DeleteOutlined-888bfc33.js";import{d as gt}from"./AddOutlined-5a7e5250.js";import{D as mt}from"./react-dropzone-uploader-f23f38ba.js";import{d as xt}from"./CloudUpload-27b6d63e.js";var te={},ft=q;Object.defineProperty(te,"__esModule",{value:!0});var Y=te.default=void 0,bt=ft(G()),St=K;Y=te.default=(0,bt.default)((0,St.jsx)("path",{d:"M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2M8.5 13.5l2.5 3.01L14.5 12l4.5 6H5z"}),"Image");var ae={},vt=q;Object.defineProperty(ae,"__esModule",{value:!0});var fe=ae.default=void 0,yt=vt(G()),Dt=K;fe=ae.default=(0,yt.default)((0,Dt.jsx)("path",{d:"M19 3h-1V1h-2v2H8V1H6v2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2m0 16H5V9h14zM5 7V5h14v2zm2 4h10v2H7zm0 4h7v2H7z"}),"EventNoteOutlined");const z={INPUT:"INPUT",MULTISELECT:"MULTISELECT",DROPDOWN:"DROPDOWN",DATERANGE:"DATERANGE",AUTOCOMPLETE:"AUTOCOMPLETE",CATEGORY:"CATEGORY",STATUS:"STATUS",SUPPLIER:"SUPPLIER"},Ct=M(_e)(({theme:n})=>({marginTop:"0px !important",border:`1px solid ${k.primary.border}`,borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.05)","&:not(:last-child)":{borderBottom:0},"&:before":{display:"none"}})),wt=M(Ne)(({theme:n})=>({minHeight:"2rem !important",margin:"0px !important",backgroundColor:k.primary.ultraLight,borderRadius:"8px 8px 0 0",transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:`${k.primary.light}20`}})),Tt=M(d)({padding:"0.75rem",gap:"0.5rem"}),It=M(d)({display:"flex",justifyContent:"flex-end",paddingRight:"0.75rem",paddingBottom:"0.75rem",paddingTop:"0rem",gap:"0.5rem"}),pe=M(Q)({borderRadius:"4px",padding:"4px 12px",textTransform:"none",fontSize:"0.875rem"}),A=M(x)({fontSize:"0.75rem",color:k.primary.dark,marginBottom:"0.25rem",fontWeight:500}),L=M(Oe)({"& .MuiOutlinedInput-root":{borderRadius:"4px","&:hover fieldset":{borderColor:k.primary.main}}}),H=({options:n,value:s,onChange:o,placeholder:u,multiple:v=!0})=>{const[f,b]=m.useState("");return e(qe,{multiple:v,size:"small",options:n||[],value:s||(v?[]:""),onChange:(t,a)=>o(a),inputValue:f,onInputChange:(t,a)=>b(a),renderInput:t=>e(L,{...t,placeholder:u,variant:"outlined"}),renderTags:(t,a)=>t.length<=1?t.map((y,S)=>e(j,{size:"small",label:y,...a({index:S}),sx:{height:24,fontSize:"0.75rem"}},S)):r(X,{children:[e(j,{size:"small",label:t[0],...a({index:0}),sx:{height:24,fontSize:"0.75rem"}}),e(j,{size:"small",label:`+${t.length-1}`,sx:{height:24,fontSize:"0.75rem"}})]}),sx:{"& .MuiAutocomplete-inputRoot":{padding:"2px 8px"}}})},Et=({handleDate:n,date:s})=>{const o=u=>u?new Date(u).toISOString().split("T")[0]:"";return r(D,{sx:{display:"flex",gap:1},children:[e(L,{type:"date",size:"small",value:o(s==null?void 0:s[0]),onChange:u=>n([new Date(u.target.value),s==null?void 0:s[1]]),InputLabelProps:{shrink:!0},sx:{flex:1}}),e(L,{type:"date",size:"small",value:o(s==null?void 0:s[1]),onChange:u=>n([s==null?void 0:s[0],new Date(u.target.value)]),InputLabelProps:{shrink:!0},sx:{flex:1}})]})},Rt=({searchParameters:n,filterData:s,onFilterChange:o,onSearch:u,onClear:v,moduleName:f="BroadcastHome",isLoading:b=!1})=>{ee();const t=ge(i=>{var h;return((h=i.commonFilter)==null?void 0:h[f])||{}}),{t:a}=me(),y=i=>{const{name:h,value:c}=i.target;o&&o(h,c)},S=(i,h)=>{const c=i==null?void 0:i.filterName,p=i==null?void 0:i.filterTitle,T=i==null?void 0:i.type,R=(i==null?void 0:i.filterData)||[];switch(T){case z.INPUT:case"text":return r(d,{item:!0,md:2,children:[e(A,{children:a(p)}),e(L,{size:"small",name:c,fullWidth:!0,onChange:y,placeholder:a(`ENTER ${p}`).toUpperCase(),value:(t==null?void 0:t[c])||""})]},h);case z.MULTISELECT:case"multiSelect":return r(d,{item:!0,md:2,children:[e(A,{children:a(p)}),e(H,{options:R,value:(t==null?void 0:t[c])||[],onChange:g=>{o&&o(c,g)},placeholder:a(`SELECT ${p}`).toUpperCase()})]},h);case z.DROPDOWN:case"dropdown":return r(d,{item:!0,md:2,children:[e(A,{children:a(p)}),e(ce,{fullWidth:!0,size:"small",children:r(Ge,{value:(t==null?void 0:t[c])||"",onChange:g=>{o&&o(c,g.target.value)},displayEmpty:!0,sx:{fontSize:"0.875rem",height:"36px"},children:[e(ue,{value:"",children:e("em",{children:a(`SELECT ${p}`).toUpperCase()})}),R.map(g=>e(ue,{value:g,children:g},g))]})})]},h);case z.DATERANGE:case"dateRange":return r(d,{item:!0,md:3,children:[e(A,{children:a(p)}),e(ce,{fullWidth:!0,sx:{padding:0,height:"37px"},children:e(Ve,{dateAdapter:Ye,children:e(Et,{handleDate:g=>{o&&o(c,g)},date:t==null?void 0:t[c]})})})]},h);case z.CATEGORY:return r(d,{item:!0,md:2,children:[e(A,{children:a(p)}),e(H,{options:["Announcements","Videos","Events"],value:(t==null?void 0:t[c])||[],onChange:g=>{o&&o(c,g)},placeholder:a(`SELECT ${p}`).toUpperCase()})]},h);case z.STATUS:return r(d,{item:!0,md:2,children:[e(A,{children:a(p)}),e(H,{options:["Active","Inactive","Draft","Archived"],value:(t==null?void 0:t[c])||[],onChange:g=>{o&&o(c,g)},placeholder:a(`SELECT ${p}`).toUpperCase()})]},h);case z.SUPPLIER:return r(d,{item:!0,md:2,children:[e(A,{children:a(p)}),e(H,{options:R,value:(t==null?void 0:t[c])||[],onChange:g=>{o&&o(c,g)},placeholder:a(`SELECT ${p}`).toUpperCase()})]},h);default:return r(d,{item:!0,md:2,children:[e(A,{children:a(p)}),e(L,{size:"small",name:c,fullWidth:!0,onChange:y,placeholder:a(`ENTER ${p}`).toUpperCase(),value:(t==null?void 0:t[c])||""})]},h)}};return e(d,{container:!0,children:e(d,{item:!0,md:12,children:r(Ct,{defaultExpanded:!1,children:[r(wt,{expandIcon:e(je,{sx:{fontSize:"1.25rem",color:k.primary.main}}),"aria-controls":"panel1a-content",id:"panel1a-header",children:[e(Le,{sx:{fontSize:"1.25rem",marginRight:1,color:k.primary.main}}),e(x,{sx:{fontSize:"0.875rem",fontWeight:600,color:k.primary.dark},children:a("Filter Broadcast")})]}),r(Pe,{sx:{padding:0},children:[e(Tt,{container:!0,children:n==null?void 0:n.map((i,h)=>S(i,h))}),r(It,{children:[e(pe,{variant:"outlined",size:"small",startIcon:e(Ue,{sx:{fontSize:"1rem"}}),onClick:v,sx:{borderColor:"#1976d2",color:"#1976d2"},disabled:b,children:a("Clear")}),e(pe,{variant:"contained",size:"small",startIcon:b?e(We,{size:16,color:"inherit"}):e(He,{sx:{fontSize:"1rem"}}),onClick:u,sx:{backgroundColor:"#1976d2"},disabled:b,children:a(b?"Searching...":"Search")})]})]})]})})})},Bt=({handleSearch:n,PresetMethod:s,PresetObj:o})=>{const u=new Date;return u.setDate(u.getDate()-8),e(d,{container:!0,sx:Ke,children:e(d,{item:!0,md:12,children:e(Rt,{searchParameters:[{type:"text",filterName:"id",filterTitle:"Broadcast ID"},{type:"multiSelect",filterName:"category",filterData:["Announcements","Videos","Events"],filterTitle:"Broadcast Category"},{type:"multiSelect",filterName:"status",filterData:["Active","Inactive","Draft","Archived"],filterTitle:"Broadcast Status"},{type:"dateRange",filterName:"startDate",filterTitle:"Start Date"},{type:"dateRange",filterName:"endDate",filterTitle:"End Date"}],onSearch:n,moduleName:"BroadcastHome",isLoading:!1,t:t=>t})})})};var re={},At=q;Object.defineProperty(re,"__esModule",{value:!0});var be=re.default=void 0,kt=At(G()),zt=K;be=re.default=(0,kt.default)((0,zt.jsx)("path",{d:"M18 20H4V6h9V4H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-9h-2zm-7.79-3.17-1.96-2.36L5.5 18h11l-3.54-4.71zM20 4V1h-2v3h-3c.01.01 0 2 0 2h3v2.99c.01.01 2 0 2 0V6h3V4z"}),"AddPhotoAlternateOutlined");var ne={},Mt=q;Object.defineProperty(ne,"__esModule",{value:!0});var Se=ne.default=void 0,$t=Mt(G()),Ft=K;Se=ne.default=(0,$t.default)((0,Ft.jsx)("path",{d:"M11 18c0 1.1-.9 2-2 2s-2-.9-2-2 .9-2 2-2 2 .9 2 2m-2-8c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2m0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2m6 4c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2m0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2m0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2"}),"DragIndicator");const _t=({input:n,previews:s,submitButton:o,dropzoneProps:u,files:v,extra:{maxFiles:f}})=>{const[b,t]=m.useState([]);ee(),m.useEffect(()=>{if(s&&Array.isArray(s)){const y=s.map((S,i)=>({...S,id:S.key||`preview-${i}`,uniqueId:`${Date.now()}-${i}`}));t(y)}},[s]);function a(y){if(!y.destination)return;const S=Array.from(b),[i]=S.splice(y.source.index,1);S.splice(y.destination.index,0,i),t(S)}return r(d,{container:!0,spacing:3,children:[e(d,{item:!0,xs:12,md:6,children:r(V,{elevation:0,sx:{border:"2px dashed",borderColor:"primary.main",borderRadius:"16px",padding:4,minHeight:"320px",display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:J("#3B30C8",.02),transition:"all 0.3s ease",position:"relative",overflow:"hidden","&:hover":{borderColor:"primary.dark",backgroundColor:J("#3B30C8",.04),transform:"translateY(-2px)",boxShadow:"0 8px 25px rgba(59, 48, 200, 0.15)"}},...u,children:[e(D,{sx:{position:"absolute",top:0,left:0,right:0,bottom:0,background:"linear-gradient(135deg, rgba(59, 48, 200, 0.05) 0%, rgba(59, 48, 200, 0.02) 100%)",zIndex:0}}),r(D,{textAlign:"center",sx:{position:"relative",zIndex:1},children:[e(xt,{sx:{fontSize:48,color:"primary.main",mb:2,animation:"bounce 2s infinite"}}),e(x,{sx:{color:"primary.main",fontSize:"18px",fontWeight:600,mb:1,letterSpacing:"0.5px"},children:"Drag & Drop Your Files Here"}),e(x,{sx:{fontSize:"14px",mb:3,color:"text.secondary",fontWeight:500},children:"or click to browse"}),e(D,{mb:3,sx:{"& > *":{borderRadius:"12px !important"}},children:n}),e(j,{icon:e(Y,{}),label:`PNG, JPG, SVG • Max ${f} files`,variant:"outlined",sx:{color:"text.secondary",borderColor:"divider",backgroundColor:"background.paper"}})]})]})}),e(d,{item:!0,xs:12,md:6,children:r(E,{spacing:2,sx:{height:"100%"},children:[r(D,{children:[r(x,{variant:"h6",sx:{fontWeight:600,color:"text.primary",display:"flex",alignItems:"center",gap:1},children:[e(Y,{color:"primary"}),"Uploaded Files (",b.length,")"]}),e(x,{variant:"body2",sx:{color:"text.secondary",mt:.5,fontStyle:"italic"},children:"Drag items to reorder • Click to remove"})]}),e(V,{elevation:0,sx:{flex:1,maxHeight:"280px",overflowY:"auto",border:"1px solid",borderColor:"divider",borderRadius:"12px",backgroundColor:"grey.50",p:1},children:b.length>0?e(Qe,{onDragEnd:a,children:e(Xe,{droppableId:"droppable",children:(y,S)=>r(E,{...y.droppableProps,ref:y.innerRef,spacing:1,sx:{minHeight:"100px",backgroundColor:S.isDraggingOver?J("#3B30C8",.05):"transparent",borderRadius:"8px",transition:"background-color 0.2s ease"},children:[b.map((i,h)=>{const c=i.uniqueId||i.id||`draggable-${h}`;return e(et,{index:h,draggableId:c,children:(p,T)=>{var R,g;return e(V,{elevation:T.isDragging?8:1,sx:{position:"relative",backgroundColor:T.isDragging?"primary.main":"background.paper",borderRadius:"12px",p:2,transition:"all 0.2s ease",border:T.isDragging?"2px solid":"1px solid",borderColor:T.isDragging?"primary.light":"divider",transform:T.isDragging?"rotate(2deg)":"none",cursor:"grab","&:hover":{boxShadow:"0 4px 12px rgba(0,0,0,0.1)",transform:"translateY(-1px)"}},...p.draggableProps,ref:p.innerRef,children:r(D,{sx:{display:"flex",alignItems:"center",gap:2},children:[e(D,{...p.dragHandleProps,sx:{display:"flex",alignItems:"center",color:T.isDragging?"white":"text.secondary","&:hover":{color:"primary.main"}},children:e(Se,{})}),e(D,{sx:{flex:1,display:"flex",alignItems:"center"},children:i}),e(D,{sx:{minWidth:120},children:e(x,{variant:"body2",sx:{fontWeight:500,color:T.isDragging?"white":"text.primary",textAlign:"right"},children:((g=(R=i.props)==null?void 0:R.meta)==null?void 0:g.name)||`Image ${h+1}`})})]})})}},c)}),y.placeholder]})})}):r(D,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"200px",color:"text.secondary"},children:[e(Y,{sx:{fontSize:48,mb:2,opacity:.5}}),e(x,{variant:"body1",sx:{fontWeight:500},children:"No files uploaded yet"}),e(x,{variant:"body2",sx:{opacity:.7},children:"Upload some files to see them here"})]})}),v.length>0&&e(xe,{in:!0,children:e(D,{sx:{pt:1},children:o})})]})})]})},Nt=n=>{const[s,o]=m.useState([]),u=async()=>{if(!(n!=null&&n.uploadedFiles)||n.uploadedFiles.length===0){o([]);return}try{const f=n.uploadedFiles.map(async(a,y)=>{if(!a.url||!a.name)return null;try{const i=await(await fetch(a.url)).blob();return new File([i],a.name,{type:a.type||"image/jpeg"})}catch(S){return console.error(`Error converting file ${a.name}:`,S),null}}),t=(await Promise.all(f)).filter(a=>a!==null);o(t)}catch(f){console.error("Error converting uploaded files:",f),o([])}};m.useEffect(()=>{u()},[n==null?void 0:n.uploadedFiles]);const v=({meta:f,file:b,xhr:t},a)=>{console.log("File status changed:",{meta:f.name,status:a})};return r("div",{id:"imageUpload",className:"dropzone",children:[e("style",{jsx:!0,children:`
        @keyframes bounce {
          0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
          }
          40% {
            transform: translateY(-10px);
          }
          60% {
            transform: translateY(-5px);
          }
        }
      `}),e(mt,{onSubmit:n==null?void 0:n.handleAttachmentsSubmit,onChangeStatus:v,styles:{submitButton:{background:"linear-gradient(135deg, #3B30C8 0%, #5B4FD1 100%)",color:"white",border:"none",borderRadius:"12px",padding:"12px 24px",cursor:"pointer",fontWeight:600,fontSize:"14px",transition:"all 0.3s ease",boxShadow:"0 4px 12px rgba(59, 48, 200, 0.3)","&:hover":{transform:"translateY(-2px)",boxShadow:"0 6px 20px rgba(59, 48, 200, 0.4)"}},dropzone:{border:"none",padding:0,overflow:"hidden",backgroundColor:"transparent"},submitButtonContainer:{textAlign:"right",marginTop:"8px"},previewImage:{borderRadius:"8px",width:"60px",height:"60px",objectFit:"cover",border:"2px solid #e0e0e0"},preview:{padding:"0",border:"none",borderRadius:"0",marginBottom:"0",backgroundColor:"transparent",display:"flex",alignItems:"center"},inputLabel:{textTransform:"none",height:"44px",background:"linear-gradient(135deg, #3B30C8 0%, #5B4FD1 100%)",color:"white",fontWeight:600,padding:"12px 24px",borderRadius:"12px",border:"none",cursor:"pointer",fontSize:"14px",transition:"all 0.3s ease",boxShadow:"0 4px 12px rgba(59, 48, 200, 0.3)","&:hover":{transform:"translateY(-2px)",boxShadow:"0 6px 20px rgba(59, 48, 200, 0.4)"}},inputLabelWithFiles:{textTransform:"none",background:"linear-gradient(135deg, #3B30C8 0%, #5B4FD1 100%)",color:"white",fontWeight:600,padding:"10px 20px",borderRadius:"12px",border:"none",cursor:"pointer",fontSize:"13px",transition:"all 0.3s ease",boxShadow:"0 2px 8px rgba(59, 48, 200, 0.3)"}},LayoutComponent:_t,inputContent:"Choose Files",inputWithFilesContent:"Add More",maxFiles:4,initialFiles:s,submitButtonContent:"Save Changes",accept:"image/*"})]})},Ot={position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",width:900,maxWidth:"95vw",bgcolor:"background.paper",borderRadius:"20px",boxShadow:"0 20px 60px rgba(0, 0, 0, 0.15)",p:0,maxHeight:"90vh",overflow:"hidden",border:"1px solid",borderColor:"divider"},jt={p:3,background:"linear-gradient(135deg,rgb(104, 214, 247) 0%, #5B4FD1 100%)",color:"white",display:"flex",justifyContent:"space-between",alignItems:"center",position:"relative",overflow:"hidden","&::before":{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,background:"linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)",zIndex:0}},Lt={p:3,maxHeight:"calc(90vh - 140px)",overflow:"auto",backgroundColor:"#fafafa"},Pt=({open:n,handleOpen:s,handleClose:o,handleAttachmentsSubmit:u,uploadedFiles:v=[],skeleton:f=!1})=>e(Ze,{open:n,onClose:(t,a)=>{f&&a==="backdropClick"||o()},"aria-labelledby":"manage-banner-title","aria-describedby":"manage-banner-description",disableEscapeKeyDown:f,closeAfterTransition:!0,children:e(xe,{in:n,children:r(D,{sx:Ot,children:[r(D,{sx:jt,children:[r(D,{sx:{position:"relative",zIndex:1},children:[e(x,{id:"manage-banner-title",variant:"h5",component:"h2",sx:{fontWeight:700,color:"white",letterSpacing:"0.5px",textShadow:"0 2px 4px rgba(255, 255, 255, 0.1)"},children:"Manage Banners"}),e(x,{variant:"body2",sx:{opacity:.9,mt:.5,fontSize:"13px"},children:"Upload and organize your banner images"})]}),!f&&e(O,{onClick:o,size:"small",sx:{color:"white",backgroundColor:"rgba(255,255,255,0.1)",backdropFilter:"blur(10px)",border:"1px solid rgba(255,255,255,0.2)",position:"relative",zIndex:1,"&:hover":{backgroundColor:"rgba(255,255,255,0.2)",transform:"scale(1.05)"}},children:e(Je,{})})]}),e(D,{sx:Lt,children:f?e(Ut,{}):e(Nt,{uploadedFiles:v,handleAttachmentsSubmit:u,closeModal:o})})]})})}),Ut=()=>r(d,{container:!0,spacing:3,children:[e(d,{item:!0,xs:12,md:6,children:e(U,{animation:"wave",variant:"rectangular",width:"100%",height:320,sx:{borderRadius:"16px"}})}),e(d,{item:!0,xs:12,md:6,children:r(E,{spacing:2,children:[e(U,{animation:"wave",variant:"text",width:"70%",height:32,sx:{borderRadius:"8px"}}),e(U,{animation:"wave",variant:"text",width:"90%",height:20,sx:{borderRadius:"4px"}}),e(D,{sx:{p:1,backgroundColor:"grey.100",borderRadius:"12px"},children:[...Array(4)].map((n,s)=>e(U,{animation:"wave",variant:"rectangular",width:"100%",height:80,sx:{borderRadius:"12px",mb:1}},s))})]})})]});function ta(){const n=tt();m.useState(!1),ee();const[s,o]=m.useState([]),[u,v]=m.useState(!0),f=new Date,b=ge(l=>l.appSettings),{t}=me(),a=()=>{v(!0);let l=w=>{v(!1),o(w.broadcastDetailsDtoList)},C=()=>{};_(`/${N}/broadcastManagement/getAllBroadcastDetails/${Z(f).format("YYYY-MM-DD hh:mm:ss.000")}`,"get",l,C)};m.useEffect(()=>{a()},[]);const y=({id:l})=>{const C=()=>{let $e=()=>a(),Fe=()=>{};_(`/${N}/broadcastManagement/deleteBroadcastById/${l}`,"delete",$e,Fe)},[w,I]=m.useState(!1),B=()=>I(!0),$=()=>{I(!1)};return r(O,{sx:{...W},onClick:B,children:[e(he,{dialogState:w,openReusableDialog:B,closeReusableDialog:$,dialogTitle:"Broadcast Deletion",dialogMessage:`Do you want to delete Broadcast ${l}?`,showInputText:!1,handleDialogConfirm:C,handleDialogReject:$,showCancelButton:!0,dialogCancelText:"Cancel",dialogOkText:"Delete",dialogSeverity:"danger",handleOk:C}),e(F,{title:"Delete",children:e(pt,{color:"danger"})})]})},[S,i]=m.useState(!1),h=()=>{i(!0)},c=()=>{i(!1)},p=(l,C)=>{P(!0);const w=new FormData;[...l].forEach($=>w.append("files",$.file));let I=$=>{oe(),P(!1),$.status=="Success"&&h()},B=()=>{};_(`/${N}/broadcastManagement/upload/banner`,"postformdata",I,B,w)},[T,R]=m.useState(!1),g=()=>R(!0),oe=()=>R(!1),ve=[{field:"broadcastId",headerName:t("Broadcast ID"),width:200,editable:!1},{field:"broadcastCategory",headerName:t("Broadcast Category"),flex:1,headerAlign:"left",renderCell:l=>{switch(l.row.broadcastCategory){case"Announcements":return r(E,{direction:"row",sx:{justifyContent:"center",alignItems:"center"},children:[" ",e(ht,{sx:{color:"#0087d5",fontSize:"16px"}}),r(x,{fontSize:"12px",children:[" ","  Announcements"]})]});case"Videos":return r(E,{direction:"row",sx:{justifyContent:"center",alignItems:"center"},children:[" ",e(ut,{sx:{color:"#0087d5",fontSize:"16px"}}),e(x,{fontSize:"12px",children:"   Videos"})]});case"Events":return r(E,{direction:"row",sx:{justifyContent:"center",alignItems:"center"},children:[" ",e(fe,{sx:{color:"#0087d5",fontSize:"16px"}}),e(x,{fontSize:"12px",children:"   Events"})]})}}},{field:"module",headerName:t("Module"),flex:1},{field:"broadcastTitle",headerName:t("Broadcast Title"),width:150,align:"left",renderCell:l=>e(E,{direction:"row",sx:{alignItems:"center",width:"150px"},children:e(x,{fontSize:"12px",sx:{textOverflow:"ellipsis",overflow:"hidden",whiteSpace:"noWrap"},children:e(F,{title:l.row.broadcastTitle,children:e("span",{children:l.row.broadcastTitle})})})})},{field:"startDate",headerName:t("Start Date & Time"),flex:1,align:"center",headerAlign:"center",renderCell:l=>{let C=Z(l.row.startDate).format(`${b.dateFormat}Thh:mm A`);return r(E,{sx:{justifyContent:"center",alignItems:"center"},children:[e(x,{variant:"body2",children:C.split("T")[0]}),e(x,{variant:"body2",sx:{color:"#7E7E7E"},children:C.split("T")[1]})]})}},{field:"endDate",headerName:t("End Date & Time"),flex:1,align:"center",headerAlign:"center",renderCell:l=>{let C=Z(l.row.endDate).format(`${b.dateFormat}Thh:mm A`);return r(E,{sx:{justifyContent:"center",alignItems:"center"},children:[e(x,{variant:"body2",children:C.split("T")[0]}),e(x,{variant:"body2",sx:{color:"#7E7E7E"},children:C.split("T")[1]})]})}},{field:"status",headerName:t("Broadcast Status"),sortable:!1,width:150,renderCell:l=>e(j,{sx:{justifyContent:"flex-start",fontSize:"12px",borderRadius:"4px",color:"#000",fontSize:"12px",minWidth:"130px",backgroundColor:l.row.status==="Active"?"#cdefd6":l.row.status==="Draft"?"#FFC88787":l.row.status==="Archived"?"#FAFFC0":"#cddcef"},label:l.row.status})},{field:"actions",headerName:t("Action"),sortable:!1,flex:1,align:"center",align:"center",headerAlign:"center",disableClickEventBubbling:!0,renderCell:l=>r(X,{children:[e(O,{sx:{...W},onClick:()=>n(`editBroadcast?BroadcastId=${l.row.broadcastId}`),children:e(F,{title:"Edit",children:e(ct,{})})}),e(y,{id:l.row.broadcastId})]})}];let ie=[];const[ye,De]=m.useState([{}]),[Ce,P]=m.useState(!1),we=()=>{P(!0);let l=w=>{for(var I in w){let B={name:"",url:"",type:""};B.name=w[I].headers["File Name"][0],B.type=w[I].headers["Content-Type"][0],B.url=`data:${w[I].headers["Content-Type"][0]};base64, ${w[I].body}`,ie.push(B)}De(ie),P(!1)},C=()=>{};_(`/${N}/broadcastManagement/getBanner`,"get",l,C)};m.useEffect(()=>{we()},[]);const[Wt,Te]=m.useState(""),Ie=()=>{v(!0);let l=I=>{v(!1),o(I.broadcastDetailsDtoList)},C=()=>{},w={broadcastId:FilterSearchForm.id,broadcastCategory:FilterSearchForm.category.toString(),startFromDate:FilterSearchForm.startDate[0],startToDate:FilterSearchForm.startDate[1],endFromDate:FilterSearchForm.endDate[0],endToDate:FilterSearchForm.endDate[1],status:FilterSearchForm.status.toString(),supplierCategory:FilterSearchForm.supplier.toString()};_(`/${N}/broadcastManagement/filter/broadcast`,"post",l,C,w)},le=new Date;le.setDate(le.getDate()-8);const[Ee,Ht]=m.useState(!1),[Re,se]=m.useState(!1),[Be,Vt]=m.useState(""),[Ae,Yt]=m.useState(""),[ke,Gt]=m.useState(""),ze=()=>{se(!0)},de=()=>{se(!1)},Me=l=>{};return e(X,{children:r("div",{className:"printScreen",id:"container_outermost",children:[Ee&&e(he,{dialogState:Re,openReusableDialog:ze,closeReusableDialog:de,dialogTitle:Be,dialogMessage:Ae,handleDialogConfirm:de,dialogOkText:"OK",dialogSeverity:ke}),e(at,{openSnackBar:S,alertMsg:"Banner uploaded Successfully",handleSnackBarClose:c}),r("div",{className:"ServiceRequest",style:{...rt,margin:"0rem 0rem",backgroundColor:"#FAFCFF"},children:[e(Pt,{open:T,handleOpen:g,handleClose:oe,handleAttachmentsSubmit:p,uploadedFiles:ye,skeleton:Ce}),r(E,{children:[r(d,{container:!0,mt:0,sx:nt,children:[r(d,{item:!0,md:5,xs:12,children:[e(x,{variant:"h3",children:e("strong",{children:t("Broadcast Management")})}),e(x,{variant:"body2",color:"#777",children:t("This view displays the list of Broadcasts")})]}),e(d,{item:!0,md:7,xs:12,sx:{display:"flex"},children:r(d,{container:!0,direction:"row",justifyContent:"flex-end",alignItems:"center",spacing:0,mt:0,children:[e(F,{title:"Reload",children:e(O,{sx:{...W},onClick:()=>a(),children:e(ot,{})})}),e(F,{title:"Export Table",children:e(O,{sx:{...W},children:e(it,{})})})]})})]}),e(Bt,{handleSearch:Ie,setIsLoading:v,setBroadcastRows:o,setTitle:Te,PresetMethod:Me}),e(d,{container:!0,sx:lt,children:e(d,{item:!0,md:12,children:e(st,{width:"100%",title:`${t("List of Broadcasts")} (${s.length})`,rows:s,columns:ve,hideFooter:!1,getRowIdValue:"broadcastId",status_onRowDoubleClick:!0,callback_onRowDoubleClick:l=>{n(`viewBroadcast?BroadcastId=${l.row.broadcastId}`)},isLoading:u,disableSelectionOnClick:!0})})}),e(V,{sx:{position:"fixed",bottom:0,left:0,right:0,zIndex:1},elevation:2,children:r(dt,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[e(Q,{size:"small",variant:"outlined",onClick:g,className:"btn-mr",startIcon:e(be,{}),children:t("Manage Banner")}),e(Q,{size:"small",variant:"contained",onClick:()=>n("newBroadcast"),startIcon:e(gt,{}),children:t("New Broadcast")})]})})]})]})]})})}export{ta as default};
