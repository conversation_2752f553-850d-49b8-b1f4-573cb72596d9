import React, { useState, useEffect, forwardRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Button,
  Select,
  Tooltip,
  MenuItem,
  FormControl,
  Slide,
  tooltipClasses,
  InputLabel,
  Typography,
  IconButton,
  Box,
  Paper,
  Checkbox,
  ListItemText,
  TextField,
} from "@mui/material";
import {
  destination_CostCenter,
  destination_CostCenter_Mass,
  destination_GeneralLedger,
  destination_GeneralLedger_Mass
} from "../../destinationVariables";
import { doAjax } from "../../components/Common/fetchService";
import { Search as SearchIcon } from "@mui/icons-material";
import { DataGrid } from "@mui/x-data-grid";
import { useLocation, useNavigate } from "react-router-dom";
import ReusableSnackBar from "@components/Common/ReusableSnackBar";
import { colors } from "@constant/colors";
import DownloadDialog from "../../components/Common/DownloadDialog";
import FeedOutlinedIcon from "@mui/icons-material/FeedOutlined";
import { END_POINTS } from "@constant/apiEndPoints";
import {
  API_CODE,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  LOADER_MESSAGES,
} from "@constant/enum";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import { REQUEST_TYPE } from "@constant/enum";
import {
  transformCostCenterResponseChange,
  showToast,
} from "../../functions";
import FilterChangeDropdown from "../../components/Common/ui/dropdown/FilterChangeDropdown";
import { APP_END_POINTS } from "@constant/appEndPoints";

import useButtonDTConfig from "@hooks/useButtonDTConfig";

import BottomNavGlobal from "../../components/RequestBench/RequestPages/BottomNavGlobal";
import { resetGLStateGL, updateReqBenchRowGL } from "@app/generalLedgerTabSlice";
import ReusableDataTable from "../../components/Common/ReusableTable";
import { setChangedFieldsMapGL, setFetchedGeneralLedgerDataGL, setFetchReqBenchDataGL, setOriginalGeneralLedgerDataGL, setOriginalReqBenchDataGL, updateGeneralLedgerRowGL } from "../../app/generalLedgerTabSlice";

const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="down" ref={ref} {...props} />;
});

const RequestDetailsChangeGL = ({
  reqBench,
  requestId,
  apiResponses,
  downloadClicked,
  setDownloadClicked,
}) => {
  console.log("reqBench", requestId);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const {
    fetchedGeneralLedgerData,
    originalGLData,
    fetchReqBenchData,
    originalReqBenchData,
    changedFieldsMap,
  } = useSelector((state) => state.generalLedger);
  const requestHeaderData = useSelector(
    (state) => state.generalLedger.payload.requestHeaderData
  );
  console.log("requestHeaderData", requestHeaderData);
  console.log("downloadClicked", downloadClicked);
  console.log("fetchedGlData", fetchedGeneralLedgerData);
  console.log("fetchReqBenchData", fetchReqBenchData);
  console.log("reqBenchreqBench", reqBench);

  const initialPayload = useSelector((state) => state.request.requestHeader);
  console.log("initialPayload", initialPayload);
  const filteredButtons = useSelector((state) => state.payload.filteredButtons);
  const task = useSelector((state) => state?.userManagement.taskData);
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const RequestId = queryParams.get("RequestId");

  const [open, setOpen] = useState(true);
  const [dropdown1Value, setDropdown1Value] = useState("");
  const [dropdown2Value, setDropdown2Value] = useState("");
  const [selectedRow, setSelectedRow] = useState(null);
  const [dropdownDataCompany, setDropdownDataCompany] = useState([]);
  const [openDownloadDialog, setOpenDownloadDialog] = useState(false);
  const [selectedCompanyCodes, setSelectedCompanyCodes] = useState([]);
  const [costCenterOptions, setCostCenterOptions] = useState([]);
  const [selectedCostCenters, setSelectedCostCenters] = useState([]);
  const [successMsg, setSuccessMsg] = useState(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [blurLoading, setBlurLoading] = useState("");
  const [loaderMessage, setLoaderMessage] = useState("");
  const [downloadType, setDownloadType] = useState("systemGenerated");
  const [dropdownDataCountry, setDropdownDataCountry] = useState([]);
  const [selectedCountry, setSelectedCountry] = useState("");
  const [dropdownDataRegion, setDropdownDataRegion] = useState([]);
  const [dropdownDataSegment, setDropdownDataSegment] = useState([]);
  const [dropdownAamnum, setDropdwnAamnum] = useState([]);
  const [costcenterResponse, setCostcenterResponse] = useState([]);
  const [openSnackBar, setOpenSnackBar] = useState(false);
  const [alertMsg, setAlertMsg] = useState("");
  const [alertType, setAlertType] = useState("success");
  const [isLoading, setIsLoading] = useState(false);

  const { getButtonsDisplayGlobal } = useButtonDTConfig();

  const dropdownFields = {
    segment: dropdownDataSegment,
    country: dropdownDataCountry,
    region: dropdownDataRegion,
    pcaamnum: dropdownAamnum,
  };

  console.log("costCenterOptions", costCenterOptions);

  // State to control the grid visibility after OK is clicked
  const [showGrid, setShowGrid] = useState(false);

  useEffect(() => {
    // Cleanup function on component unmount
    // alert('come')
    return () => {
      dispatch(resetGLStateGL());
    };
  }, []);

  useEffect(() => {
    if (task?.ATTRIBUTE_1 || RequestId) {
      getButtonsDisplayGlobal("ET PC", "MDG_DYN_BTN_DT", "v2");
    }
  }, [task]);

  // Function to handle opening and closing the popup
  const handleClose = () => {
    setOpen(false);
    setDownloadClicked(false);
    navigate("/requestbench");
  };

  const handleOk = () => {
    console.log("Selected Values:", dropdown1Value, dropdown2Value);
    setOpen(false);
    setShowGrid(true);
    fetchCostCenterDetails(); // 🔥 Call the new API here
  };

  // Dropdown change handlers

  const allColumns = [
    {
      field: "included",
      headerName: "Included",
      flex: 0.2,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => (
        <Checkbox checked={params.row.included} disabled={false} />
      ),
    },
    {
      field: "lineNumber",
      headerName: "Sl No",
      flex: 0.2,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        const rowIndex = (
          reqBench ? fetchReqBenchData : fetchedGeneralLedgerData
        ).findIndex((row) => row.id === params.row.id);

        return <div>{rowIndex + 1}</div>;
      },
    },
    {
      field: "generalLedger",
      headerName: "General Ledger",
      width: 150,
      editable: false,
      renderCell: (params) => (
        <span
          style={{ color: "#9e9e9e", pointerEvents: "none", cursor: "default" }}
        >
          {params.value || ""}
        </span>
      ),
    },
    {
      field: "compCode",
      headerName: "Company Codes",
      width: 150,
      editable: false,
      renderCell: (params) => (
        <span
          style={{ color: "#9e9e9e", pointerEvents: "none", cursor: "default" }}
        >
          {params.value || ""}
        </span>
      ),
    },
    {
      field: "controllingArea",
      headerName: "Controlling Area",
      width: 150,
      editable: true,
    },
    {
      field: "name",
      headerName: "Short Description",
      width: 200,
      editable: true,
    },
    {
      field: "description",
      headerName: "Long Description",
      width: 200,
      editable: true,
    },
    // { field: "segment", headerName: "Segment", width: 150, editable: true },
    // {
    //   field: "pcaamnum",
    //   headerName: "PC AAM Number",
    //   width: 150,
    //   editable: true,
    // },
    // {
    //   field: "businessSegment",
    //   headerName: "Business Segment",
    //   width: 150,
    //   editable: true,
    // },
    {
      field: "userResponsible",
      headerName: "CC User Responsible",
      width: 250,
      editable: true,
    },
    {
      field: "personResponsible",
      headerName: "CC Person Responsible",
      width: 250,
      editable: true,
    },
    {
      field: "blockingStatus",
      headerName: "Blocking Status",
      width: 250,
      editable: true,
    },
    { field: "createdBy", headerName: "Created By", width: 150 },
    {
      field: "validFrom",
      headerName: "Valid From",
      width: 150,
    },
    { field: "validTo", headerName: "Valid To", width: 150 },
    { field: "city", headerName: "City", width: 150, editable: true },
    {
      field: "country",
      headerName: "Country/Reg.",
      width: 150,
      editable: true,
    },
    { field: "street", headerName: "Street", width: 150, editable: true },
    { field: "pocode", headerName: "Postal Code", width: 150, editable: true },
    { field: "region", headerName: "Region", width: 150, editable: true },
    { field: "name1", headerName: "Name 1", width: 150, editable: true },
    { field: "name2", headerName: "Name 2", width: 150, editable: true },
    { field: "name3", headerName: "Name 3", width: 150, editable: true },
    { field: "name4", headerName: "Name 4", width: 150, editable: true },
  ];

  const fieldNameList = requestHeaderData?.FieldName || [];
  const fixedColumns = allColumns.slice(0, 4);
  // const columns = allColumns
  //   .filter((col) => fieldNameList.includes(col.headerName))
  //   .map((col) => {
  //     if (dropdownFields[col.field]) {
  //       return {
  //         ...col,
  //         editable: true,
  //         renderEditCell: (params) => (
  //           <Select
  //             value={params.value || ""}
  //             onChange={(e) =>
  //               params.api.setEditCellValue({
  //                 id: params.id,
  //                 field: params.field,
  //                 value: e.target.value,
  //               })
  //             }
  //             fullWidth
  //             sx={{ height: 1 }}
  //           >
  //             {dropdownFields[col.field].map((option, idx) => (
  //               <MenuItem key={idx} value={option.code || option.value}>
  //                 {option.desc || option.name || option.code || option}
  //               </MenuItem>
  //             ))}
  //           </Select>
  //         ),
  //       };
  //     }

  //     return {
  //       ...col,
  //       editable: true,
  //     };
  //   });

    const dynamicColumns = allColumns
      .slice(4)
      .filter((col) => fieldNameList.includes(col.headerName))
      .map((col) => {
        if (dropdownFields[col.field]) {
          return {
            ...col,
            editable: false,
            renderCell: (params) => {
              const value = params.value || "";
              return (
                <Select
                  value={value}
                  onChange={(e) => {
                    const newValue = e.target.value;
                    const updatedRow = {
                      ...params.row,
                      [params.field]: newValue,
                    };
  
                    // Manually dispatch your row update here
                    dispatch(updateGeneralLedgerRowGL(updatedRow));
                  }}
                  size="small"
                  fullWidth
                  sx={{ minHeight: "36px" }}
                >
                  {dropdownFields[col.field].map((option, idx) => (
                    <MenuItem key={idx} value={option.code || option.value}>
                      {option.desc || option.name || option.code || option}
                    </MenuItem>
                  ))}
                </Select>
              );
            },
          };
        }
        if (
          [
            "description",
            "name",
            "profitCenter",
            "costCenterType",
            "FunctionalArea",
            "userResponsible",
            "costCenterName",
            "personResponsible",
            "countryReg",
            "location",
            "street",
            "pocode",
            "name1",
            "name2",
            "name3",
            "name4",
          ].includes(col.field)
        ) {
          return {
            ...col,
            editable: true,
            renderCell: (params) => (
              <TextField
                value={params.value || ""}
                onChange={(e) =>
                  params.api.setEditCellValue({
                    id: params.id,
                    field: params.field,
                    value: e.target.value.toUpperCase(),
                  })
                }
                variant="outlined"
                size="small"
                fullWidth
              />
            ),
            renderEditCell: (params) => (
              <TextField
                value={params.value || ""}
                onChange={(e) =>
                  params.api.setEditCellValue({
                    id: params.id,
                    field: params.field,
                    value: e.target.value.toUpperCase(),
                  })
                }
                variant="outlined"
                size="small"
                fullWidth
                placeholder={
                  col.field === "longDescription"
                    ? "Enter Long Description"
                    : "Enter Short Description"
                }
                sx={{
                  "& .MuiInputBase-root.Mui-disabled": {
                    "& > input": {
                      WebkitTextFillColor: "#000",
                      color: "#000",
                    },
                  },
                }}
              />
            ),
          };
        }
  
        return {
          ...col,
          editable: true,
        };
      });

      const columns = [...fixedColumns, ...dynamicColumns];

  console.log("columns", columns);

  const processRowUpdate = (newRow) => {
    dispatch(updateCostCenterRowCc(newRow));
     dispatch(updateGeneralLedgerRowGL(newRow));
    return newRow;
  };
  const processRowUpdateReqBench = (newRow) => {
    console.log("newRownewRow",newRow)
    dispatch(updateReqBenchRowGL(newRow));
    return newRow;
  };

  const handleRowClick = (params) => {
    setSelectedRow(params.row);
  };

  useEffect(() => {
    
      fetchGLAccount();
    
  }, [dropdown1Value]);

  useEffect(() => {
    getCompanyCode();
  }, []);


  console.log(dropdownDataCompany,"dropdownDataCompany")

  const getCompanyCode = () => {
    const hSuccess = (data) => {
      setDropdownDataCompany(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "CompanyCode", data: data.body },
      });
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_CostCenter}/data/getCompanyCodeBasedOnControllingArea?controllingArea=ETCA&rolePrefix=ETP`,
      "get",
      hSuccess,
      hError
    );
  };

  const getCountryData = () => {
    const hSuccess = (data) => {
      setDropdownDataCountry(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "Country", data: data.body },
      });
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_CostCenter}/data/getCountryOrReg`,
      "get",
      hSuccess,
      hError
    );
  };

  useEffect(() => {
    getCountryData();
  }, []);


  const fetchGLAccount =() =>{
    const hSuccess = (data) => {

      let arr=[]
      data?.body?.map((item)=>{
        let hash={}
          hash["code"] =item?.code
        arr?.push(hash)
      })

      console.log(arr,"arrywui")
      
      setCostCenterOptions((prev) => [
        ...new Set([...prev, ...arr]),
      ]);
     
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getGLAccountByCOA?chartAccount=ETCN`,
      "get",
      hSuccess,
      hError
    );
  };

  console.log(costCenterOptions,"costCenteroptions")

  const transformCostCenterData = (data) => {
    console.log("datadatadatadata",data)
    return data.map((item) => ({
      id: item.costCenter,
      costCenterHeaderID: item?.costCenterHeaderId,
      costCenterErrorId: item?.costCenterErrorId,
      costCenter: item.costCenter,
      controllingArea: item.controllingArea,
      costCenterType: item?.basicDataTabDto?.CostcenterType,
      functionalArea: item?.basicDataTabDto?.FuncAreaLong,
      currency: item?.basicDataTabDto?.Currency,
      profitCtr: item?.basicDataTabDto?.ProfitCtr,
      compCode: item?.basicDataTabDto?.CompCode,
      name: item?.basicDataTabDto?.Name,
      description: item.basicDataTabDto?.Descript || "",
      userResponsible: item.basicDataTabDto?.PersonInCharge || "",
      personResponsible: item.basicDataTabDto?.PersonInChargeUser || "",
      createdBy: item.historyTabDto?.ReqCreatedBy || "",
      validFrom: item?.fromValid || "",
      validTo: item?.toValid || "",
      city: item.addressTabDto?.City || "",
      street: item.addressTabDto?.Street || "",
      country: item.addressTabDto?.Country || "",
      region: item.addressTabDto?.AddrRegion || "",
      pocode: item.addressTabDto?.PoBox || "",
      region: item.addressTabDto?.Regio || "",
      name1: item.addressTabDto?.Name1 || "",
      name2: item.addressTabDto?.Name2 || "",
      name3: item.addressTabDto?.Name3 || "",
      name4: item.addressTabDto?.Name4 || "",
      lockIndActualPrimaryCosts: item?.controlTabDto?.lockIndActualPrimaryCosts,
      lockIndPlanPrimaryCosts: item?.controlTabDto?.LockIndPlanPrimaryCosts,
      lockIndActSecondaryCosts: item?.controlTabDto?.LockIndActSecondaryCosts,
      lockIndPlanSecondaryCosts: item?.controlTabDto?.LockIndPlanSecondaryCosts,
      lockIndActualRevenues: item?.controlTabDto?.LockIndActualRevenues,
      lockIndPlanRevenues: item?.controlTabDto?.LockIndPlanRevenues,
    }));
  };

  const fetchCostCenterDetails = () => {
    if (!selectedCostCenters.length || !dropdown1Value) return;

    const payload = {
      coAreaPCs: selectedCostCenters.map((cc) => ({
        costCenter: cc,
        controllingArea: dropdown1Value,
      })),
    };

    const successHandler = (data) => {
      const rawData = data?.body || [];
      console.log("rawData", rawData);
      setCostcenterResponse(rawData);
      const transformed = transformCostCenterData(rawData);
console.log("transformed",transformed)
      dispatch(setFetchedGeneralLedgerDataGL(transformed));
      dispatch(setOriginalGeneralLedgerDataGL(transformed));
    };

    const errorHandler = (err) => {
      console.error("Failed to fetch cost center details", err);
    };

    doAjax(
      `/${destination_CostCenter}/data/getCostCentersData`,
      "post",
      successHandler,
      errorHandler,
      payload
    );
  };

  useEffect(() => {
    if (
      reqBench === "true" &&
      Array.isArray(apiResponses) &&
      apiResponses.length > 0 &&
      fetchReqBenchData.length === 0
    ) {
      console.log("apiResponses",apiResponses)
      const transformedData = transformCostCenterResponseChange(apiResponses);
      // setFetchReqBenchData(transformedData);
      // setOriginalReqBenchData(transformedData);
console.log("transformedData11111",transformedData);
      dispatch(setFetchReqBenchDataGL(transformedData));
      dispatch(setOriginalReqBenchDataGL(transformedData));
    }
  }, [apiResponses, reqBench]);

  useEffect(() => {
    if (downloadClicked) {
      setOpen(true);
    }
  }, [downloadClicked]);

  const parsedData = (apiResponses ?? []).map((item) => {
    let changedFields = {};

    // Prioritize the already parsed object if present
    if (typeof item.changedFields === "object" && item.changedFields !== null) {
      changedFields = item.changedFields;
    } else if (typeof item.ChangedFields === "string") {
      try {
        changedFields = JSON.parse(item.ChangedFields);
      } catch {
        changedFields = {};
      }
    }

    const { changedFields: _, ChangedFields, ...rest } = item;

    return {
      ...rest,
      changedFields,
    };
  });

  console.log("parsedData", parsedData);

  useEffect(() => {
    if (!parsedData || parsedData.length === 0) return;

    const newChangedFieldsMap = {};
    parsedData.forEach((row) => {
      newChangedFieldsMap[row.CostCenterID] = row.changedFields || {};
    });

    dispatch(setChangedFieldsMapGL(newChangedFieldsMap));
  }, [apiResponses]);

        const allData = reqBench ? fetchReqBenchData : fetchedGeneralLedgerData;
        console.log("alllldata",allData)
      const payload = allData?.map((ccData) => {
        console.log("ccData", ccData);
    return {
    CostCenterHeaderID: ccData?.costCenterHeaderID??"",
    ControllingArea: ccData?.controllingArea??"",
    Testrun: true, // Not from UI
    IsSunoco: false,
    IsSunocoCCPC: false,
    IsScheduled: false,
    Action: "I", // Not from UI
    ReqCreatedBy: ccData?.createdBy ?? "",
    ReqCreatedOn: "",
    RequestStatus: requestHeaderData?.RequestStatus ?? ccData?.requestStatus ?? "",
    CreationId: "", // Not from UI
    EditId: "", // Not from UI
    DeleteId: "", // Not from UI
    MassCreationId: "", // Not from UI
    MassEditId: ccData?.massEditId ?? "",
    MassDeleteId: "",
    RequestType: requestHeaderData?.RequestType === "Create" ? "Mass Create" : ccData?.requestStatus ? ccData?.requestStatus : requestHeaderData?.RequestType || "",
    MassRequestStatus: ccData?.massRequestStatus ?? "", // Not from UI
    TaskId: task?.taskId?task?.taskId:"",
    Remarks: ccData?.Comments ?? "",
    TempLockRemarks: "", // Not from UI
    Info: "Change All Other Fields or Unblock",
    TemplateName: requestHeaderData?.TemplateName ?? ccData?.templateName ?? "", // Not from UI
    TemplateHeaders: ccData?.templateHeaders ?? "", // Not from UI
    ChangedFields: ccData?.changedFields ?? "", // Not from UI
    GeneralInfoID: ccData?.generalInfoID ?? "",
    RequestPriority: requestHeaderData?.RequestPriority ?? ccData?.requestPriority ?? "",
    BusinessJustification: ccData?.businessJustification ?? "", // to be mapped from UI
    SAPorJEErrorCheck: ccData?.sAPorJEErrorCheck ?? "",// to be mapped from UI
    FERCIndicator: ccData?.fERCIndicator ?? "", // Not from UI
    BusinessSegment: ccData?.businessSegment ?? "", // Not from UI
    HierarchyRegion: ccData?.hierarchyRegion ?? "",
    AAMNumber: ccData?.aAMNumber ?? "",
    ValidationDoneBy: task?.taskId ? "MDM Approval" : "Requestor",
    TotalIntermediateTasks: task?.TotalIntermediateTasks ?? ccData?.totalIntermediateTasks ?? "",
    IsFirstSynCompleted: ccData?.isFirstSynCompleted ?? false, // Not from UI
    TempLockIsSelectedForSyn: ccData?.tempLockIsSelectedForSyn ?? false, // Not from UI
    SelectedByRequestorToDisplay: ccData?.included ?? true, // to be mapped from UI
    ToChildHeaderdata:{
        RequestId: ccData?.toChildHeaderDataId ?? "",
        Status: requestHeaderData?.ToChildHeaderdata?.Status ?? ccData?.toChildHeaderDataStatus ?? "",
        IntermediateTaskCount: requestHeaderData?.ToChildHeaderdata?.IntermediateTaskCount ?? ccData?.toChildHeaderDataIntermediateTaskCount ?? "",
        TotalTaskCount: requestHeaderData?.ToChildHeaderdata?.TotalTaskCount ?? ccData?.toChildHeaderDataTotalTaskCount ?? "",
        BifurcatedValue: requestHeaderData?.ToChildHeaderdata?.BifurcatedValue ?? ccData?.toChildHeaderDataBifurcatedValue ?? "",
        TaskId: requestHeaderData?.ToChildHeaderdata?.TaskId ?? ccData?.toChildHeaderDataTaskId ?? "",
        CreatedOn: "2025-06-05T06:34:55.995+00:00",
        UpdatedOn: "2025-06-05T06:34:58.653+00:00",
        RequestType: "Mass Change"
    },
    ToCostCenterData: [
      {
        CostCenterID: ccData?.toCostCenterDataId ?? "",
        CostCenterErrorID: ccData?.costCenterErrorId, // Not from UI
        Costcenter: ccData?.costCenter ?? "",
        ValidFrom: 
        // body?.ValidFrom
        // ? `/Date(${new Date(body.ValidFrom).getTime()})/`
        // : 
        "/Date(-2208988800000)/",
        ValidTo: 
        // body?.ValidTo
        // ? `/Date(${new Date(body.ValidTo).getTime()})/`
        // : 
        "/Date(32503680000000)/",
        PersonInCharge: ccData?.userResponsible ?? "",
        CostcenterType: ccData?.costCenterType ?? "",
        CostctrHierGrp: ccData?.CostctrHierGrp ?? "ET_CCA",
        BusArea: "",
        CompCode: ccData?.compCode ?? "",
        Currency: ccData?.currency ?? "",
        ProfitCtr: ccData?.profitCtr ?? "",
        Name: ccData?.name ?? "",
        Descript: ccData?.description ?? "",
        PersonInChargeUser: ccData?.personResponsible ?? "",
        RecordQuantity: ccData?.RecordQuantity ?? "",
        LockIndActualPrimaryCosts: ccData?.lockIndActualPrimaryCosts ?? "",
        LockIndPlanPrimaryCosts: ccData?.lockIndPlanPrimaryCosts || "",
        LockIndActSecondaryCosts: ccData?.lockIndActSecondaryCosts || "",
        LockIndPlanSecondaryCosts: ccData?.lockIndPlanSecondaryCosts || "",
        LockIndActualRevenues: ccData?.lockIndActualRevenues || "",
        LockIndPlanRevenues: ccData?.lockIndPlanRevenues || "",
        LockIndCommitmentUpdate: ccData?.lockIndCommitmentUpdate || "",
        ConditionTableUsage: "",
        Application: "",
        CstgSheet: "",
        ActyIndepTemplate: ccData?.ActyIndepTemplate || "",
        ActyDepTemplate: ccData?.ActyDepTemplate || "",
        AddrTitle: ccData?.AddrTitle || "",
        AddrName1: ccData?.name1 || "",
        AddrName2: ccData?.name2 || "",
        AddrName3: ccData?.name3 || "",
        AddrName4: ccData?.name4 || "",
        AddrStreet: ccData?.street || "",
        AddrCity: ccData?.city || "",
        AddrDistrict: ccData?.AddrDistrict || "",
        AddrCountry: ccData?.country || "",
        AddrCountryIso: ccData?.AddrCountryIso || "",
        AddrTaxjurcode: ccData?.AddrTaxjurcode || "",
        AddrPoBox: ccData?.pocode || "",
        AddrPostlCode: ccData?.AddrPostlCode || "",
        AddrPobxPcd: ccData?.AddrPobxPcd || "",
        AddrRegion: ccData?.region || "",
        TelcoLangu: ccData?.TelcoLangu || "",
        TelcoLanguIso: ccData?.TelcoLanguIso || "",
        TelcoTelephone: ccData?.TelcoTelephone || "",
        TelcoTelephone2: ccData?.TelcoTelephone2 || "",
        TelcoTelebox: ccData?.TelcoTelebox || "",
        TelcoTelex: ccData?.TelcoTelex || "",
        TelcoFaxNumber: ccData?.TelcoFaxNumber || "",
        TelcoTeletex: ccData?.TelcoTeletex || "",
        TelcoPrinter: ccData?.TelcoPrinter || "",
        TelcoDataLine: ccData?.TelcoDataLine || "",
        ActyDepTemplateAllocCc: ccData?.ActyDepTemplateAllocCc || "",
        ActyDepTemplateSk: ccData?.ActyDepTemplateSk || "",
        ActyIndepTemplateAllocCc: ccData?.ActyIndepTemplateAllocCc || "",
        ActyIndepTemplateSk: ccData?.ActyIndepTemplateSk || "",
        AvcActive: ccData?.AvcActive===true ? true : ccData?.AvcActive===false ? false: null ,
        AvcProfile: ccData?.AvcProfile || "",
        BudgetCarryingCostCtr: ccData?.BudgetCarryingCostCtr || "",
        CurrencyIso: ccData?.CurrencyIso || "",
        Department: ccData?.Department || "",
        FuncArea: ccData?.FuncArea || "",
        FuncAreaFixAssigned: ccData?.FuncAreaFixAssigned || "",
        FuncAreaLong: ccData?.functionalArea || "",
        Fund: ccData?.Fund || "",
        FundFixAssigned: ccData?.FundFixAssigned || "",
        GrantFixAssigned: ccData?.GrantFixAssigned || "",
        GrantId: ccData?.GrantId || "",
        JvEquityTyp: ccData?.JvEquityTyp || "",
        JvJibcl: ccData?.JvJibcl || "",
        JvJibsa: ccData?.JvJibsa || "",
        JvOtype: ccData?.JvOtype || "",
        JvRecInd: ccData?.JvRecInd || "",
        JvVenture: ccData?.JvVenture || "",
        Logsystem: ccData?.Logsystem || "",
        FERCIndicator: "",
        TochangeLogData: {
          ChangeLogId: ccData?.toChangeLogDataId ?? "", // should be mapped from display
          RequestId: ccData?.toChangeLogRequestId ?? "",  // should be mapped from display
          RequestHeaderId: ccData?.toChangeLogRequestHeaderId ?? "", // should be mapped from display
          ChangeLogData: null,
        },
        ToCostCenterErrorData: {
          CostCenterErrorId: ccData?.toCostCenterErrorDataId ?? "",
          RequestId: ccData?.toCostCenterErrorDataRequestId ?? "",
          RequestHeaderId: ccData?.toCostCenterErrorDataRequestHeaderId ?? "",
          CostCenter: ccData?.toCostCenterErrorDataCostCenter ?? "",
          CompCode: ccData?.toCostCenterErrorDataCompCode ?? "",
          ControllingArea: ccData?.toCostCenterErrorDataControllingArea ?? "",
          SAPMessage: ccData?.toCostCenterErrorDataSAPMessage ?? "",
          ObjectSAPError: ccData?.toCostCenterErrorDataObjectSAPError ?? "",
          ObjectDBError: ccData?.toCostCenterErrorDataObjectDBError ?? "",
          ObjectExcelError: ccData?.toCostCenterErrorDataObjectExcelError ?? "",
          ShortDescSAPError: ccData?.toCostCenterErrorDataShortDescSAPError ?? "",
          ShortDescDBError: ccData?.ShortDescDBError ?? "",
          ShortDescExcelError: ccData?.toCostCenterErrorDataShortDescExcelError ?? "",
          LongDescSAPError: ccData?.toCostCenterErrorDataLongDescSAPError ?? "",
          LongDescDBError: ccData?.toCostCenterErrorDataLongDescDBError ?? "",
          LongDescExcelError: ccData?.toCostCenterErrorDataLongDescExcelError ?? "",
          AddressValidation: ccData?. toCostCenterErrorDataAddressValidation ?? "",
          PersonResponsibleError: ccData?.PersonResponsibleError ?? "",
          UserResponsibleError: ccData?.toCostCenterErrorDataUserResponsibleError ?? "",
          CheckUserAndPersonResponsible: ccData?.toCostCenterErrorDataCheckUserAndPersonResponsible ?? "",
          DMSAttachmentErrorStatus: ccData?.toCostCenterErrorDataDMSAttachmentErrorStatus ?? "",
          SAPAttachmentErrorStatus: ccData?.toCostCenterErrorDataSAPAttachmentErrorStatus ?? ""
        }
      }
    ],
    Torequestheaderdata: {
        RequestId: ccData?.toRequestHeaderDataRequestId ?? initialPayload?.requestId ?? "",
        ReqCreatedBy: ccData?.toRequestHeaderDataReqCreatedBy ?? initialPayload?.reqCreatedBy ?? "",
        ReqCreatedOn: `/Date(${new Date(ccData?.toRequestHeaderDataReqCreatedOn??initialPayload?.reqCreatedOn).getTime()})/`,
        ReqUpdatedOn: `/Date(${new Date(ccData?.toRequestHeaderDataReqUpdatedOn??initialPayload?.reqUpdatedOn).getTime()})/`,
        RequestType: ccData?.toRequestHeaderDataRequestType ?? initialPayload?.requestType ?? "",
        RequestPrefix: ccData?.toRequestHeaderDataRequestPrefix ?? initialPayload?.requestPrefix ?? "",
        RequestPriority: ccData?.toRequestHeaderDataRequestPriority ?? initialPayload?.requestPriority ?? "",
        RequestDesc: ccData?.toRequestHeaderDataRequestDesc ?? initialPayload?.requestDesc ?? "",
        RequestStatus: ccData?.toRequestHeaderDataRequestStatus ?? initialPayload?.requestStatus ?? "",
        FirstProd: ccData?.toRequestHeaderDataFirstProd ?? initialPayload?.firstProd ?? "",
        LaunchDate: ccData?.toRequestHeaderDataLaunchDate ?? initialPayload?.launchDate ?? "",
        LeadingCat: ccData?.toRequestHeaderDataLeadingCat ?? initialPayload?.leadingCat ?? "",
        Division: ccData?.toRequestHeaderDataDivision ?? initialPayload?.division ?? "",
        TemplateName: ccData?.toRequestHeaderDataTemplateName ?? initialPayload?.templateName ?? "",
        FieldName: ccData?.toRequestHeaderDataFieldName ?? initialPayload?.fieldName ?? "",
        Region: ccData?.toRequestHeaderDataRegion ?? initialPayload?.region ?? "",
        FilterDetails: ccData?.toRequestHeaderFilterDetails ?? "",
        IsBifurcated: ccData?.toRequestHeaderIsBifurcated ?? initialPayload?.isBifurcated ?? "",
    },
    ToGeneralInfoData: {
      GeneralInfoId: ccData?.toGeneralInfoDataGeneralInfoId ?? "",
      RequestPriority: ccData?.toGeneralInfoDataRequestPriority ?? "",
      BusinessJustification: ccData?.toGeneralInfoDataBusinessJustification ?? "",
      SAPorJEErrorCheck: ccData?.toGeneralInfoDataSAPorJEErrorCheck ?? "",
      BusinessSegment: ccData?.toGeneralInfoDataBusinessSegment ?? "",
      Region: ccData?.toGeneralInfoDataRegion ?? "",
      AAMNumber: ccData?.toGeneralInfoDataAAMNumber ?? ""
    }
    };
      });
      console.log("payloadddd",payload);

  const handleSaveAsDraft = () => {
    // const Payload = fetchedCostCenterData.map((ccData) => {
    //   return {
    //     requestInProcess: "",
    //     ProfitCenterID: "",
    //     TemplateName: requestHeaderData?.TemplateName || "",
    //     TemplateHeaders: "",
    //     IsScheduled: null,
    //     Action: "",
    //     RequestID: "",
    //     TaskStatus: null,
    //     TaskId: null,
    //     ReqCreatedBy: pcData?.createdBy || "",??
    //     ReqCreatedOn: pcData?.historyTabDto?.ReqCreatedOn || "",
    //     RequestStatus: requestHeaderData?.requestStatus || "",
    //     CreationId: "",
    //     EditId: "",
    //     DeleteId: "",
    //     MassCreationId: "",
    //     MassEditId: "",
    //     MassDeleteId: "",
    //     ProfitCenterErrorID: "",
    //     RequestType: requestHeaderData?.requestType || "",
    //     MassRequestStatus: "",
    //     Remarks: null,
    //     TempLockRemarks: null,
    //     Info: "",
    //     // ChangedFields: "",
    //     ChangedFields: JSON.stringify(changedFieldsMap[pcData.id] || {}),
    //     PrctrName: pcData?.profitCenterName || "",
    //     LongText: pcData?.description || "",
    //     InChargeUser: pcData?.userResponsible || "",
    //     InCharge: pcData?.personResponsible || "",
    //     Department: pcData?.Department || "",
    //     PrctrHierGrp: pcData?.PrctrHierGrp || "",
    //     Segment: pcData?.segment || "",
    //     LockInd: pcData?.indicatorsTabDto?.LockIndicator || false,
    //     Template: "",
    //     Title: pcData?.addressTabDto?.Title || "",
    //     Name1: pcData?.name1 || "",
    //     Name2: pcData?.name2 || "",
    //     Name3: pcData?.name3 || "",
    //     Name4: pcData?.name4 || "",
    //     Street: pcData?.street || "",
    //     City: pcData?.city || "",
    //     District: pcData?.district || "",
    //     Country: pcData?.country || "",
    //     Taxjurcode: pcData?.TaxJurisdiction || "",
    //     PoBox: pcData?.PoBox || "",
    //     PostalCode: pcData?.PostalCode || "",
    //     PobxPcd: pcData?.PobxPcd || "",
    //     Regio: pcData?.Regio || "",
    //     Language: pcData?.communicationTabDto?.Language || "",
    //     Telephone: pcData?.communicationTabDto?.Telephone || "",
    //     Telephone2: pcData?.communicationTabDto?.Telephone2 || "",
    //     Telebox: pcData?.communicationTabDto?.Telebox || "",
    //     Telex: pcData?.communicationTabDto?.Telex || "",
    //     FaxNumber: pcData?.communicationTabDto?.FaxNumber || "",
    //     Teletex: pcData?.communicationTabDto?.Teletex || "",
    //     Printer: pcData?.communicationTabDto?.Printer || "",
    //     DataLine: pcData?.communicationTabDto?.DataLine || "",
    //     ProfitCenter: pcData?.profitCenter || "",
    //     COArea: pcData?.controllingArea || "ETCA",
    //     ValidfromDate:
    //       pcData?.basicDataTabDto?.ValidfromDate || "/Date(-2208988800000)/",
    //     ValidtoDate:
    //       pcData?.basicDataTabDto?.ValidtoDate || "/Date(253402214400000)/",
    //     Testrun: null,
    //     IsFirstSynCompleted: false,
    //     TempLockIsSelectedForSyn: false,
    //     SelectedByRequestorToDisplay: false,
    //     IsSunoco: false,
    //     Countryiso: "",
    //     LanguIso: "",
    //     Logsystem: "",
    //     GeneralInfoID: null,
    //     RequestPriority: requestHeaderData?.requestPriority || "",
    //     BusinessJustification: null,
    //     SAPorJEErrorCheck: null,
    //     BusinessSegment: "CRUDE",
    //     HierarchyRegion: null,
    //     PCAAMNumber: pcData?.basicDataTabDto?.PCAAMNumber || "",
    //     ValidationDoneBy: null,
    //     TotalIntermediateTasks: pcData?.totalIntermediateTasks ?? "",

    //     ToCompanycode: [
    //       {
    //         CompCodeID: null,
    //         CompanyCode: pcData.companyCode || "",
    //         CompanyName: "",
    //         AssignToPrctr: "",
    //         Venture: "",
    //         RecInd: "",
    //         EquityTyp: "",
    //         JvOtype: "",
    //         JvJibcl: "",
    //         JvJibsa: "",
    //       },
    //     ],

    //     Torequestheaderdata: {
    //       RequestId:
    //         requestHeaderData?.RequestId || initialPayload?.requestId || "",
    //       ReqCreatedBy: requestHeaderData?.ReqCreatedBy || "",
    //       ReqCreatedOn:
    //         requestHeaderData?.ReqCreatedOn || "/Date(1743998564967)/",
    //       ReqUpdatedOn:
    //         requestHeaderData?.ReqUpdatedOn || "/Date(1743998564967)/",
    //       RequestType: requestHeaderData?.RequestType || "",
    //       RequestPrefix: requestHeaderData?.RequestPrefix || "",
    //       RequestPriority: requestHeaderData?.RequestPriority || "",
    //       RequestDesc: requestHeaderData?.RequestDesc || "",
    //       RequestStatus: "Draft",
    //       FirstProd: "",
    //       LaunchDate: "",
    //       LeadingCat: "Anesthesia/Pain Management",
    //       Division: "00",
    //       TemplateName: "",
    //       FieldName: "",
    //       Region: "",
    //       FilterDetails: null,
    //       IsBifurcated: null,
    //     },

    //     TochangeLogData: {},

    //     ToProfitCenterErrorData: {
    //       ProfitCenterErrorId: null,
    //       RequestId: "",
    //       ProfitCenter: pcData?.profitCenter || "",
    //       CompanyCode: pcData?.compCodesTabDto?.CompanyCode?.[0] || "",
    //       Segment: pcData?.basicDataTabDto?.Segment || "",
    //       PcAamNumber: pcData?.basicDataTabDto?.PCAAMNumber || "",
    //       ControllingArea: pcData?.controllingArea || "",
    //       SapMessage: "",
    //       ObjectSapError: "",
    //       ObjectDbError: "",
    //       ObjectExcelError: "",
    //       ShortDescSapError: "",
    //       ShortDescDbError: "",
    //       ShortDescExcelError: "",
    //       LongDescSapError: "",
    //       LongDescDbError: "",
    //       LongDescExcelError: "",
    //       AddrValidationError: "",
    //       PersonResponsibleError: "",
    //       DmsAttachmentErrorStatus: "",
    //       SapAttachmentErrorStatus: "",
    //       RemovalNodeErrorStatus: "",
    //       AddNodeErrorStatus: "",
    //     },

    //     ToGeneralInfoData: {
    //       GeneralInfoId: "",
    //       RequestPriority: "",
    //       BusinessJustification: "",
    //       SAPorJEErrorCheck: "",
    //       BusinessSegment: "",
    //       Region: "",
    //     },
    //   };
    // });
    const Payload = payload;
    console.log("Payload", Payload);

    const hSuccess = (data) => {
      console.log("successdata", data);

      setIsLoading(false);
      setAlertType("success");
      setAlertMsg(
        "Cost Centers change submission for save as draft initiated"
      );
      setOpenSnackBar(true);

      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };

    const hError = (error) => {
      console.log("Errordata", error);
      setIsLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving the draft.");
      setOpenSnackBar(true);
    };

    // 🔄 API call
    doAjax(
      `/${destination_CostCenter_Mass}/massAction/changeCostCentersSaveAsDraft`,
      "POST",
      hSuccess,
      hError,
      Payload
    );

    // console.log("Payload", Payload);
  };
  const handleSendBack = () => {
    // const Payload = fetchedCostCenterData.map((ccData) => {
    //   return {
    //     requestInProcess: "",
    //     ProfitCenterID: "",
    //     TemplateName: requestHeaderData?.TemplateName || "",
    //     TemplateHeaders: "",
    //     IsScheduled: null,
    //     Action: "",
    //     RequestID: "",
    //     TaskStatus: null,
    //     TaskId: null,
    //     ReqCreatedBy: pcData?.createdBy || "",
    //     ReqCreatedOn: pcData?.historyTabDto?.ReqCreatedOn || "",
    //     RequestStatus: requestHeaderData?.requestStatus || "",
    //     CreationId: "",
    //     EditId: "",
    //     DeleteId: "",
    //     MassCreationId: "",
    //     MassEditId: "",
    //     MassDeleteId: "",
    //     ProfitCenterErrorID: "",
    //     RequestType: requestHeaderData?.requestType || "",
    //     MassRequestStatus: "",
    //     Remarks: null,
    //     TempLockRemarks: null,
    //     Info: "",
    //     // ChangedFields: "",
    //     ChangedFields: JSON.stringify(changedFieldsMap[pcData.id] || {}),
    //     PrctrName: pcData?.profitCenterName || "",
    //     LongText: pcData?.description || "",
    //     InChargeUser: pcData?.userResponsible || "",
    //     InCharge: pcData?.personResponsible || "",
    //     Department: pcData?.Department || "",
    //     PrctrHierGrp: pcData?.PrctrHierGrp || "",
    //     Segment: pcData?.segment || "",
    //     LockInd: pcData?.indicatorsTabDto?.LockIndicator || false,
    //     Template: "",
    //     Title: pcData?.addressTabDto?.Title || "",
    //     Name1: pcData?.name1 || "",
    //     Name2: pcData?.name2 || "",
    //     Name3: pcData?.name3 || "",
    //     Name4: pcData?.name4 || "",
    //     Street: pcData?.street || "",
    //     City: pcData?.city || "",
    //     District: pcData?.district || "",
    //     Country: pcData?.country || "",
    //     Taxjurcode: pcData?.TaxJurisdiction || "",
    //     PoBox: pcData?.PoBox || "",
    //     PostalCode: pcData?.PostalCode || "",
    //     PobxPcd: pcData?.PobxPcd || "",
    //     Regio: pcData?.Regio || "",
    //     Language: pcData?.communicationTabDto?.Language || "",
    //     Telephone: pcData?.communicationTabDto?.Telephone || "",
    //     Telephone2: pcData?.communicationTabDto?.Telephone2 || "",
    //     Telebox: pcData?.communicationTabDto?.Telebox || "",
    //     Telex: pcData?.communicationTabDto?.Telex || "",
    //     FaxNumber: pcData?.communicationTabDto?.FaxNumber || "",
    //     Teletex: pcData?.communicationTabDto?.Teletex || "",
    //     Printer: pcData?.communicationTabDto?.Printer || "",
    //     DataLine: pcData?.communicationTabDto?.DataLine || "",
    //     ProfitCenter: pcData?.profitCenter || "",
    //     COArea: pcData?.controllingArea || "ETCA",
    //     ValidfromDate:
    //       pcData?.basicDataTabDto?.ValidfromDate || "/Date(-2208988800000)/",
    //     ValidtoDate:
    //       pcData?.basicDataTabDto?.ValidtoDate || "/Date(253402214400000)/",
    //     Testrun: null,
    //     IsFirstSynCompleted: false,
    //     TempLockIsSelectedForSyn: false,
    //     SelectedByRequestorToDisplay: false,
    //     IsSunoco: false,
    //     Countryiso: "",
    //     LanguIso: "",
    //     Logsystem: "",
    //     GeneralInfoID: null,
    //     RequestPriority: requestHeaderData?.requestPriority || "",
    //     BusinessJustification: null,
    //     SAPorJEErrorCheck: null,
    //     BusinessSegment: "CRUDE",
    //     HierarchyRegion: null,
    //     PCAAMNumber: pcData?.basicDataTabDto?.PCAAMNumber || "",
    //     ValidationDoneBy: null,
    //     TotalIntermediateTasks: pcData?.totalIntermediateTasks ?? "",

    //     ToCompanycode: [
    //       {
    //         CompCodeID: null,
    //         CompanyCode: pcData.companyCode || "",
    //         CompanyName: "",
    //         AssignToPrctr: "",
    //         Venture: "",
    //         RecInd: "",
    //         EquityTyp: "",
    //         JvOtype: "",
    //         JvJibcl: "",
    //         JvJibsa: "",
    //       },
    //     ],

    //     Torequestheaderdata: {
    //       RequestId:
    //         requestHeaderData?.RequestId || initialPayload?.requestId || "",
    //       ReqCreatedBy: requestHeaderData?.ReqCreatedBy || "",
    //       ReqCreatedOn:
    //         requestHeaderData?.ReqCreatedOn || "/Date(1743998564967)/",
    //       ReqUpdatedOn:
    //         requestHeaderData?.ReqUpdatedOn || "/Date(1743998564967)/",
    //       RequestType: requestHeaderData?.RequestType || "",
    //       RequestPrefix: requestHeaderData?.RequestPrefix || "",
    //       RequestPriority: requestHeaderData?.RequestPriority || "",
    //       RequestDesc: requestHeaderData?.RequestDesc || "",
    //       RequestStatus: "Draft",
    //       FirstProd: "",
    //       LaunchDate: "",
    //       LeadingCat: "Anesthesia/Pain Management",
    //       Division: "00",
    //       TemplateName: "",
    //       FieldName: "",
    //       Region: "",
    //       FilterDetails: null,
    //       IsBifurcated: null,
    //     },

    //     TochangeLogData: {},

    //     ToProfitCenterErrorData: {
    //       ProfitCenterErrorId: null,
    //       RequestId: "",
    //       ProfitCenter: pcData?.profitCenter || "",
    //       CompanyCode: pcData?.compCodesTabDto?.CompanyCode?.[0] || "",
    //       Segment: pcData?.basicDataTabDto?.Segment || "",
    //       PcAamNumber: pcData?.basicDataTabDto?.PCAAMNumber || "",
    //       ControllingArea: pcData?.controllingArea || "",
    //       SapMessage: "",
    //       ObjectSapError: "",
    //       ObjectDbError: "",
    //       ObjectExcelError: "",
    //       ShortDescSapError: "",
    //       ShortDescDbError: "",
    //       ShortDescExcelError: "",
    //       LongDescSapError: "",
    //       LongDescDbError: "",
    //       LongDescExcelError: "",
    //       AddrValidationError: "",
    //       PersonResponsibleError: "",
    //       DmsAttachmentErrorStatus: "",
    //       SapAttachmentErrorStatus: "",
    //       RemovalNodeErrorStatus: "",
    //       AddNodeErrorStatus: "",
    //     },

    //     ToGeneralInfoData: {
    //       GeneralInfoId: "",
    //       RequestPriority: "",
    //       BusinessJustification: "",
    //       SAPorJEErrorCheck: "",
    //       BusinessSegment: "",
    //       Region: "",
    //     },
    //   };
    // });
 const Payload = payload;
    console.log("Payload", Payload);

    const hSuccess = (data) => {
      console.log("successdata", data);

      setIsLoading(false);
      setAlertType("success");
      setAlertMsg(
        "Cost Centers change submission for save as draft initiated"
      );
      setOpenSnackBar(true);

      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };

    const hError = (error) => {
      console.log("Errordata", error);
      setIsLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving the draft.");
      setOpenSnackBar(true);
    };

    // 🔄 API call
    doAjax(
      `/${destination_CostCenter_Mass}/massAction/changeCostCentersSaveAsDraft`,
      "POST",
      hSuccess,
      hError,
      Payload
    );

    // console.log("Payload", Payload);
  };
  const handleRejectAndCancel = () => {
    // const Payload = fetchedCostCenterData.map((ccData) => {
    //   return {
    //     requestInProcess: "",
    //     ProfitCenterID: "",
    //     TemplateName: requestHeaderData?.TemplateName || "",
    //     TemplateHeaders: "",
    //     IsScheduled: null,
    //     Action: "",
    //     RequestID: "",
    //     TaskStatus: null,
    //     TaskId: null,
    //     ReqCreatedBy: pcData?.createdBy || "",
    //     ReqCreatedOn: pcData?.historyTabDto?.ReqCreatedOn || "",
    //     RequestStatus: requestHeaderData?.requestStatus || "",
    //     CreationId: "",
    //     EditId: "",
    //     DeleteId: "",
    //     MassCreationId: "",
    //     MassEditId: "",
    //     MassDeleteId: "",
    //     ProfitCenterErrorID: "",
    //     RequestType: requestHeaderData?.requestType || "",
    //     MassRequestStatus: "",
    //     Remarks: null,
    //     TempLockRemarks: null,
    //     Info: "",
    //     // ChangedFields: "",
    //     ChangedFields: JSON.stringify(changedFieldsMap[pcData.id] || {}),
    //     PrctrName: pcData?.profitCenterName || "",
    //     LongText: pcData?.description || "",
    //     InChargeUser: pcData?.userResponsible || "",
    //     InCharge: pcData?.personResponsible || "",
    //     Department: pcData?.Department || "",
    //     PrctrHierGrp: pcData?.PrctrHierGrp || "",
    //     Segment: pcData?.segment || "",
    //     LockInd: pcData?.indicatorsTabDto?.LockIndicator || false,
    //     Template: "",
    //     Title: pcData?.addressTabDto?.Title || "",
    //     Name1: pcData?.name1 || "",
    //     Name2: pcData?.name2 || "",
    //     Name3: pcData?.name3 || "",
    //     Name4: pcData?.name4 || "",
    //     Street: pcData?.street || "",
    //     City: pcData?.city || "",
    //     District: pcData?.district || "",
    //     Country: pcData?.country || "",
    //     Taxjurcode: pcData?.TaxJurisdiction || "",
    //     PoBox: pcData?.PoBox || "",
    //     PostalCode: pcData?.PostalCode || "",
    //     PobxPcd: pcData?.PobxPcd || "",
    //     Regio: pcData?.Regio || "",
    //     Language: pcData?.communicationTabDto?.Language || "",
    //     Telephone: pcData?.communicationTabDto?.Telephone || "",
    //     Telephone2: pcData?.communicationTabDto?.Telephone2 || "",
    //     Telebox: pcData?.communicationTabDto?.Telebox || "",
    //     Telex: pcData?.communicationTabDto?.Telex || "",
    //     FaxNumber: pcData?.communicationTabDto?.FaxNumber || "",
    //     Teletex: pcData?.communicationTabDto?.Teletex || "",
    //     Printer: pcData?.communicationTabDto?.Printer || "",
    //     DataLine: pcData?.communicationTabDto?.DataLine || "",
    //     ProfitCenter: pcData?.profitCenter || "",
    //     COArea: pcData?.controllingArea || "ETCA",
    //     ValidfromDate:
    //       pcData?.basicDataTabDto?.ValidfromDate || "/Date(-2208988800000)/",
    //     ValidtoDate:
    //       pcData?.basicDataTabDto?.ValidtoDate || "/Date(253402214400000)/",
    //     Testrun: null,
    //     IsFirstSynCompleted: false,
    //     TempLockIsSelectedForSyn: false,
    //     SelectedByRequestorToDisplay: false,
    //     IsSunoco: false,
    //     Countryiso: "",
    //     LanguIso: "",
    //     Logsystem: "",
    //     GeneralInfoID: null,
    //     RequestPriority: requestHeaderData?.requestPriority || "",
    //     BusinessJustification: null,
    //     SAPorJEErrorCheck: null,
    //     BusinessSegment: "CRUDE",
    //     HierarchyRegion: null,
    //     PCAAMNumber: pcData?.basicDataTabDto?.PCAAMNumber || "",
    //     ValidationDoneBy: null,
    //     TotalIntermediateTasks: pcData?.totalIntermediateTasks ?? "",

    //     ToCompanycode: [
    //       {
    //         CompCodeID: null,
    //         CompanyCode: pcData.companyCode || "",
    //         CompanyName: "",
    //         AssignToPrctr: "",
    //         Venture: "",
    //         RecInd: "",
    //         EquityTyp: "",
    //         JvOtype: "",
    //         JvJibcl: "",
    //         JvJibsa: "",
    //       },
    //     ],

    //     Torequestheaderdata: {
    //       RequestId:
    //         requestHeaderData?.RequestId || initialPayload?.requestId || "",
    //       ReqCreatedBy: requestHeaderData?.ReqCreatedBy || "",
    //       ReqCreatedOn:
    //         requestHeaderData?.ReqCreatedOn || "/Date(1743998564967)/",
    //       ReqUpdatedOn:
    //         requestHeaderData?.ReqUpdatedOn || "/Date(1743998564967)/",
    //       RequestType: requestHeaderData?.RequestType || "",
    //       RequestPrefix: requestHeaderData?.RequestPrefix || "",
    //       RequestPriority: requestHeaderData?.RequestPriority || "",
    //       RequestDesc: requestHeaderData?.RequestDesc || "",
    //       RequestStatus: "Draft",
    //       FirstProd: "",
    //       LaunchDate: "",
    //       LeadingCat: "Anesthesia/Pain Management",
    //       Division: "00",
    //       TemplateName: "",
    //       FieldName: "",
    //       Region: "",
    //       FilterDetails: null,
    //       IsBifurcated: null,
    //     },

    //     TochangeLogData: {},

    //     ToProfitCenterErrorData: {
    //       ProfitCenterErrorId: null,
    //       RequestId: "",
    //       ProfitCenter: pcData?.profitCenter || "",
    //       CompanyCode: pcData?.compCodesTabDto?.CompanyCode?.[0] || "",
    //       Segment: pcData?.basicDataTabDto?.Segment || "",
    //       PcAamNumber: pcData?.basicDataTabDto?.PCAAMNumber || "",
    //       ControllingArea: pcData?.controllingArea || "",
    //       SapMessage: "",
    //       ObjectSapError: "",
    //       ObjectDbError: "",
    //       ObjectExcelError: "",
    //       ShortDescSapError: "",
    //       ShortDescDbError: "",
    //       ShortDescExcelError: "",
    //       LongDescSapError: "",
    //       LongDescDbError: "",
    //       LongDescExcelError: "",
    //       AddrValidationError: "",
    //       PersonResponsibleError: "",
    //       DmsAttachmentErrorStatus: "",
    //       SapAttachmentErrorStatus: "",
    //       RemovalNodeErrorStatus: "",
    //       AddNodeErrorStatus: "",
    //     },

    //     ToGeneralInfoData: {
    //       GeneralInfoId: "",
    //       RequestPriority: "",
    //       BusinessJustification: "",
    //       SAPorJEErrorCheck: "",
    //       BusinessSegment: "",
    //       Region: "",
    //     },
    //   };
    // });
 const Payload = payload;
    console.log("Payload", Payload);

    const hSuccess = (data) => {
      console.log("successdata", data);

      setIsLoading(false);
      setAlertType("success");
      setAlertMsg(
        "Cost Centers change submission for save as draft initiated"
      );
      setOpenSnackBar(true);

      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };

    const hError = (error) => {
      console.log("Errordata", error);
      setIsLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving the draft.");
      setOpenSnackBar(true);
    };

    // 🔄 API call
    doAjax(
      `/${destination_CostCenter_Mass}/massAction/changeCostCentersSaveAsDraft`,
      "POST",
      hSuccess,
      hError,
      Payload
    );

    // console.log("Payload", Payload);
  };

  const handleSubmitForReview = () => {
    // const Payload = fetchReqBenchData.map((pcData) => {
    //   return {
    //     requestInProcess: "",
    //     ProfitCenterID: pcData.ProfitCenterID || "",
    //     TemplateName: requestHeaderData?.TemplateName || "",
    //     TemplateHeaders: "",
    //     IsScheduled: null,
    //     Action: "",
    //     RequestID: "",
    //     TaskStatus: null,
    //     TaskId: null,
    //     ReqCreatedBy: pcData?.createdBy || "",
    //     ReqCreatedOn: pcData?.historyTabDto?.ReqCreatedOn || "",
    //     RequestStatus: requestHeaderData?.requestStatus || "",
    //     CreationId: "",
    //     EditId: "",
    //     DeleteId: "",
    //     MassCreationId: "",
    //     MassEditId: requestId || "",
    //     MassDeleteId: "",
    //     ProfitCenterErrorID: pcData.ProfitCenterErrorID || "",
    //     RequestType: requestHeaderData?.requestType || "",
    //     MassRequestStatus: "",
    //     Remarks: null,
    //     TempLockRemarks: null,
    //     Info: "",
    //     ChangedFields: "",
    //     PrctrName: pcData?.profitCenterName || "",
    //     LongText: pcData?.description || "",
    //     InChargeUser: pcData?.userResponsible || "",
    //     InCharge: pcData?.personResponsible || "",
    //     Department: pcData?.Department || "",
    //     PrctrHierGrp: pcData?.PrctrHierGrp || "",
    //     Segment: pcData?.segment || "",
    //     LockInd: pcData?.indicatorsTabDto?.LockIndicator || false,
    //     Template: "",
    //     Title: pcData?.addressTabDto?.Title || "",
    //     Name1: pcData?.name1 || "",
    //     Name2: pcData?.name2 || "",
    //     Name3: pcData?.name3 || "",
    //     Name4: pcData?.name4 || "",
    //     Street: pcData?.street || "",
    //     City: pcData?.city || "",
    //     District: pcData?.district || "",
    //     Country: pcData?.country || "",
    //     Taxjurcode: pcData?.TaxJurisdiction || "",
    //     PoBox: pcData?.PoBox || "",
    //     PostalCode: pcData?.PostalCode || "",
    //     PobxPcd: pcData?.PobxPcd || "",
    //     Regio: pcData?.Regio || "",
    //     Language: pcData?.communicationTabDto?.Language || "",
    //     Telephone: pcData?.communicationTabDto?.Telephone || "",
    //     Telephone2: pcData?.communicationTabDto?.Telephone2 || "",
    //     Telebox: pcData?.communicationTabDto?.Telebox || "",
    //     Telex: pcData?.communicationTabDto?.Telex || "",
    //     FaxNumber: pcData?.communicationTabDto?.FaxNumber || "",
    //     Teletex: pcData?.communicationTabDto?.Teletex || "",
    //     Printer: pcData?.communicationTabDto?.Printer || "",
    //     DataLine: pcData?.communicationTabDto?.DataLine || "",
    //     ProfitCenter: pcData?.profitCenter || "",
    //     COArea: pcData?.controllingArea || "ETCA",
    //     ValidfromDate:
    //       pcData?.basicDataTabDto?.ValidfromDate || "/Date(-2208988800000)/",
    //     ValidtoDate:
    //       pcData?.basicDataTabDto?.ValidtoDate || "/Date(253402214400000)/",
    //     Testrun: null,
    //     IsFirstSynCompleted: false,
    //     TempLockIsSelectedForSyn: false,
    //     SelectedByRequestorToDisplay: false,
    //     IsSunoco: false,
    //     Countryiso: "",
    //     LanguIso: "",
    //     Logsystem: "",
    //     GeneralInfoID: null,
    //     RequestPriority: requestHeaderData?.requestPriority || "",
    //     BusinessJustification: null,
    //     SAPorJEErrorCheck: null,
    //     BusinessSegment: "CRUDE",
    //     HierarchyRegion: null,
    //     PCAAMNumber: pcData?.basicDataTabDto?.PCAAMNumber || "",
    //     ValidationDoneBy: null,
    //     TotalIntermediateTasks: pcData?.totalIntermediateTasks ?? "",

    //     ToCompanycode: [
    //       {
    //         CompCodeID: pcData.CompCodeID || "",
    //         CompanyCode: pcData.companyCode || "",
    //         CompanyName: "",
    //         AssignToPrctr: "",
    //         Venture: "",
    //         RecInd: "",
    //         EquityTyp: "",
    //         JvOtype: "",
    //         JvJibcl: "",
    //         JvJibsa: "",
    //       },
    //     ],

    //     Torequestheaderdata: {
    //       RequestId:
    //         requestHeaderData?.RequestId || initialPayload?.requestId || "",
    //       ReqCreatedBy: requestHeaderData?.ReqCreatedBy || "",
    //       ReqCreatedOn:
    //         requestHeaderData?.ReqCreatedOn || "/Date(1743998564967)/",
    //       ReqUpdatedOn:
    //         requestHeaderData?.ReqUpdatedOn || "/Date(1743998564967)/",
    //       RequestType: requestHeaderData?.RequestType || "",
    //       RequestPrefix: requestHeaderData?.RequestPrefix || "",
    //       RequestPriority: requestHeaderData?.RequestPriority || "",
    //       RequestDesc: requestHeaderData?.RequestDesc || "",
    //       RequestStatus: "Draft",
    //       FirstProd: "",
    //       LaunchDate: "",
    //       LeadingCat: "Anesthesia/Pain Management",
    //       Division: "00",
    //       TemplateName: "",
    //       FieldName: "",
    //       Region: "",
    //       FilterDetails: null,
    //       IsBifurcated: null,
    //     },

    //     TochangeLogData: {},

    //     ToProfitCenterErrorData: {
    //       ProfitCenterErrorId: pcData?.ProfitCenterErrorID || "",
    //       RequestId: "",
    //       ProfitCenter: pcData?.profitCenter || "",
    //       CompanyCode: pcData?.companyCode || "",
    //       Segment: pcData?.basicDataTabDto?.Segment || "",
    //       PcAamNumber: pcData?.basicDataTabDto?.PCAAMNumber || "",
    //       ControllingArea: pcData?.controllingArea || "",
    //       SapMessage: "",
    //       ObjectSapError: "",
    //       ObjectDbError: "",
    //       ObjectExcelError: "",
    //       ShortDescSapError: "",
    //       ShortDescDbError: "",
    //       ShortDescExcelError: "",
    //       LongDescSapError: "",
    //       LongDescDbError: "",
    //       LongDescExcelError: "",
    //       AddrValidationError: "",
    //       PersonResponsibleError: "",
    //       DmsAttachmentErrorStatus: "",
    //       SapAttachmentErrorStatus: "",
    //       RemovalNodeErrorStatus: "",
    //       AddNodeErrorStatus: "",
    //     },

    //     ToGeneralInfoData: {
    //       GeneralInfoId: pcData.GeneralInfoId || "",
    //       RequestPriority: "",
    //       BusinessJustification: "",
    //       SAPorJEErrorCheck: "",
    //       BusinessSegment: "",
    //       Region: "",
    //     },
    //   };
    // });
 const Payload = payload;
    console.log("Payloadreview", Payload);

    const hSuccess = (data) => {
      console.log("successdata", data);

      setIsLoading(false);
      setAlertType("success");
      setAlertMsg("Cost Centers submit for review successful");
      setOpenSnackBar(true);

      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };

    const hError = (error) => {
      console.log("Errordata", error);
      setIsLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving submit for review.");
      setOpenSnackBar(true);
    };

    // 🔄 API call
    doAjax(
      `/${destination_CostCenter_Mass}/massAction/changeCostCentersSubmitForReview`,
      "POST",
      hSuccess,
      hError,
      Payload
    );

    console.log("Payload", Payload);
  };

  const handleSubmitForApprove = () => {
    console.log("Submit for Approve triggered");
    // const Payload = fetchReqBenchData.map((pcData) => {
    //   return {
    //     requestInProcess: "",
    //     ProfitCenterID: pcData.ProfitCenterID || "",
    //     TemplateName: requestHeaderData?.TemplateName || "",
    //     TemplateHeaders: "",
    //     IsScheduled: null,
    //     Action: "",
    //     RequestID: pcData.RequestID || "",
    //     TaskStatus: null,
    //     TaskId: task?.taskId,
    //     ReqCreatedBy: pcData?.createdBy || "",
    //     ReqCreatedOn: pcData?.historyTabDto?.ReqCreatedOn || "",
    //     RequestStatus: requestHeaderData?.requestStatus || "",
    //     CreationId: "",
    //     EditId: "",
    //     DeleteId: "",
    //     MassCreationId: "",
    //     MassEditId: requestId || "",
    //     MassDeleteId: "",
    //     ProfitCenterErrorID: pcData.ProfitCenterErrorID || "",
    //     RequestType: requestHeaderData?.requestType || "",
    //     MassRequestStatus: "",
    //     Remarks: null,
    //     TempLockRemarks: null,
    //     Info: "",
    //     ChangedFields: "",
    //     PrctrName: pcData?.profitCenterName || "",
    //     LongText: pcData?.description || "",
    //     InChargeUser: pcData?.userResponsible || "",
    //     InCharge: pcData?.personResponsible || "",
    //     Department: pcData?.Department || "",
    //     PrctrHierGrp: pcData?.PrctrHierGrp || "",
    //     Segment: pcData?.segment || "",
    //     LockInd: pcData?.indicatorsTabDto?.LockIndicator || false,
    //     Template: "",
    //     Title: pcData?.addressTabDto?.Title || "",
    //     Name1: pcData?.name1 || "",
    //     Name2: pcData?.name2 || "",
    //     Name3: pcData?.name3 || "",
    //     Name4: pcData?.name4 || "",
    //     Street: pcData?.street || "",
    //     City: pcData?.city || "",
    //     District: pcData?.district || "",
    //     Country: pcData?.country || "",
    //     Taxjurcode: pcData?.TaxJurisdiction || "",
    //     PoBox: pcData?.PoBox || "",
    //     PostalCode: pcData?.PostalCode || "",
    //     PobxPcd: pcData?.PobxPcd || "",
    //     Regio: pcData?.Regio || "",
    //     Language: pcData?.communicationTabDto?.Language || "",
    //     Telephone: pcData?.communicationTabDto?.Telephone || "",
    //     Telephone2: pcData?.communicationTabDto?.Telephone2 || "",
    //     Telebox: pcData?.communicationTabDto?.Telebox || "",
    //     Telex: pcData?.communicationTabDto?.Telex || "",
    //     FaxNumber: pcData?.communicationTabDto?.FaxNumber || "",
    //     Teletex: pcData?.communicationTabDto?.Teletex || "",
    //     Printer: pcData?.communicationTabDto?.Printer || "",
    //     DataLine: pcData?.communicationTabDto?.DataLine || "",
    //     ProfitCenter: pcData?.profitCenter || "",
    //     COArea: pcData?.controllingArea || "ETCA",
    //     ValidfromDate:
    //       pcData?.basicDataTabDto?.ValidfromDate || "/Date(-2208988800000)/",
    //     ValidtoDate:
    //       pcData?.basicDataTabDto?.ValidtoDate || "/Date(253402214400000)/",
    //     Testrun: null,
    //     IsFirstSynCompleted: false,
    //     TempLockIsSelectedForSyn: false,
    //     SelectedByRequestorToDisplay: false,
    //     IsSunoco: false,
    //     Countryiso: "",
    //     LanguIso: "",
    //     Logsystem: "",
    //     GeneralInfoID: null,
    //     RequestPriority: requestHeaderData?.requestPriority || "",
    //     BusinessJustification: null,
    //     SAPorJEErrorCheck: null,
    //     BusinessSegment: "CRUDE",
    //     HierarchyRegion: null,
    //     PCAAMNumber: pcData?.basicDataTabDto?.PCAAMNumber || "",
    //     ValidationDoneBy: null,
    //     TotalIntermediateTasks: pcData?.totalIntermediateTasks ?? "",

    //     ToCompanycode: [
    //       {
    //         CompCodeID: pcData.CompCodeID,
    //         CompanyCode: pcData.companyCode || "",
    //         CompanyName: "",
    //         AssignToPrctr: "",
    //         Venture: "",
    //         RecInd: "",
    //         EquityTyp: "",
    //         JvOtype: "",
    //         JvJibcl: "",
    //         JvJibsa: "",
    //       },
    //     ],

    //     Torequestheaderdata: {
    //       RequestId: requestHeaderData?.RequestId || "",
    //       ReqCreatedBy: requestHeaderData?.ReqCreatedBy || "",
    //       ReqCreatedOn:
    //         requestHeaderData?.ReqCreatedOn || "/Date(1743998564967)/",
    //       ReqUpdatedOn:
    //         requestHeaderData?.ReqUpdatedOn || "/Date(1743998564967)/",
    //       RequestType: requestHeaderData?.RequestType || "",
    //       RequestPrefix: requestHeaderData?.RequestPrefix || "",
    //       RequestPriority: requestHeaderData?.RequestPriority || "",
    //       RequestDesc: requestHeaderData?.RequestDesc || "",
    //       RequestStatus: "Draft",
    //       FirstProd: "",
    //       LaunchDate: "",
    //       LeadingCat: "Anesthesia/Pain Management",
    //       Division: "00",
    //       TemplateName: "",
    //       FieldName: "",
    //       Region: "",
    //       FilterDetails: null,
    //       IsBifurcated: null,
    //     },

    //     TochangeLogData: {
    //       ChangeLogData: null,
    //       ChangeLogId: pcData.ChangeLogId || "",
    //       RequestHeaderId: "",
    //       RequestId: "",
    //     },

    //     ToProfitCenterErrorData: {
    //       ProfitCenterErrorId: pcData.ProfitCenterErrorID || "",
    //       RequestId: pcData.RequestID || "",
    //       ProfitCenter: pcData?.profitCenter || "",
    //       CompanyCode: pcData?.companyCode || "",
    //       Segment: pcData?.basicDataTabDto?.Segment || "",
    //       PcAamNumber: pcData?.basicDataTabDto?.PCAAMNumber || "",
    //       ControllingArea: pcData?.controllingArea || "",
    //       SapMessage: "",
    //       ObjectSapError: "",
    //       ObjectDbError: "",
    //       ObjectExcelError: "",
    //       ShortDescSapError: "",
    //       ShortDescDbError: "",
    //       ShortDescExcelError: "",
    //       LongDescSapError: "",
    //       LongDescDbError: "",
    //       LongDescExcelError: "",
    //       AddrValidationError: "",
    //       PersonResponsibleError: "",
    //       DmsAttachmentErrorStatus: "",
    //       SapAttachmentErrorStatus: "",
    //       RemovalNodeErrorStatus: "",
    //       AddNodeErrorStatus: "",
    //     },

    //     ToGeneralInfoData: {
    //       GeneralInfoId: pcData.GeneralInfoId || "",
    //       RequestPriority: "",
    //       BusinessJustification: "",
    //       SAPorJEErrorCheck: "",
    //       BusinessSegment: "",
    //       Region: "",
    //     },
    //   };
    // });
 const Payload = payload;
    console.log("Payloadreview", Payload);

    const hSuccess = (data) => {
      console.log("successdata", data);

      setIsLoading(false);
      setAlertType("success");
      setAlertMsg("Cost Centers successfuly Approved");
      setOpenSnackBar(true);

      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };

    const hError = (error) => {
      console.log("Errordata", error);
      setIsLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving submit for review.");
      setOpenSnackBar(true);
    };

    // 🔄 API call
    doAjax(
      `/${destination_CostCenter_Mass}/massAction/changeCostCentersApprovalSubmit`,
      "POST",
      hSuccess,
      hError,
      Payload
    );

    console.log("Payload", Payload);
  };
  console.log("fetchReqBenchData",fetchReqBenchData)
  console.log("fetchedCostCenterData",fetchedGeneralLedgerData)
    const validateAllRows = () => {
    //   const allData = reqBench ? fetchReqBenchData : fetchedCostCenterData;
    //   const Payload = allData.map((ccData) => {
    //     console.log("ccData", ccData);
    // return {
    // CostCenterHeaderID: ccData?.costCenterHeaderID??"",
    // ControllingArea: ccData?.controllingArea??"",
    // Testrun: true, // Not from UI
    // IsSunoco: false,
    // IsSunocoCCPC: false,
    // IsScheduled: false,
    // Action: "I", // Not from UI
    // ReqCreatedBy: ccData?.createdBy ?? "",
    // ReqCreatedOn: "",
    // RequestStatus: requestHeaderData?.RequestStatus || "",
    // CreationId: "", // Not from UI
    // EditId: "", // Not from UI
    // DeleteId: "", // Not from UI
    // MassCreationId: "", // Not from UI
    // MassEditId: requestId ?? "",
    // MassDeleteId: "",
    // RequestType: requestHeaderData?.RequestType === "Create" ? "Mass Create" : requestHeaderData?.RequestType || "",
    // MassRequestStatus: "", // Not from UI
    // TaskId: task?.taskId?task?.taskId:"",
    // Remarks: ccData?.Comments ?? "",
    // TempLockRemarks: "", // Not from UI
    // Info: "",
    // TemplateName: requestHeaderData?.TemplateName ?? "", // Not from UI
    // TemplateHeaders: "", // Not from UI
    // ChangedFields: "", // Not from UI
    // GeneralInfoID: null,
    // RequestPriority: requestHeaderData?.RequestPriority || "",
    // BusinessJustification: "", // to be mapped from UI
    // SAPorJEErrorCheck: null,// to be mapped from UI
    // FERCIndicator: "", // Not from UI
    // BusinessSegment: "", // Not from UI
    // HierarchyRegion: "",
    // AAMNumber: "",
    // ValidationDoneBy: task?.taskId ? "MDM Approval" : "Requestor",
    // TotalIntermediateTasks: task?.TotalIntermediateTasks ?? "",
    // IsFirstSynCompleted: false, // Not from UI
    // TempLockIsSelectedForSyn: false, // Not from UI
    // SelectedByRequestorToDisplay: false, // to be mapped from UI
    // ToChildHeaderdata:{
    //     RequestId: requestId ?? "",
    //     Status: requestHeaderData?.ToChildHeaderdata?.Status || "",
    //     IntermediateTaskCount: requestHeaderData?.ToChildHeaderdata?.IntermediateTaskCount ?? null,
    //     TotalTaskCount: requestHeaderData?.ToChildHeaderdata?.TotalTaskCount ?? null,
    //     BifurcatedValue: requestHeaderData?.ToChildHeaderdata?.BifurcatedValue ?? "",
    //     TaskId: requestHeaderData?.ToChildHeaderdata?.TaskId ?? null,
    //     CreatedOn: "2025-06-05T06:34:55.995+00:00",
    //     UpdatedOn: "2025-06-05T06:34:58.653+00:00",
    //     RequestType: "Mass Create"
    // },
    // ToCostCenterData: [
    //   {
    //     CostCenterID: "",
    //     CostCenterErrorID: ccData?.costCenterErrorId, // Not from UI
    //     Costcenter: ccData?.costCenter ?? "",
    //     ValidFrom: 
    //     // body?.ValidFrom
    //     // ? `/Date(${new Date(body.ValidFrom).getTime()})/`
    //     // : 
    //     "/Date(-2208988800000)/",
    //     ValidTo: 
    //     // body?.ValidTo
    //     // ? `/Date(${new Date(body.ValidTo).getTime()})/`
    //     // : 
    //     "/Date(32503680000000)/",
    //     PersonInCharge: ccData?.userResponsible || "",
    //     CostcenterType: ccData?.CostcenterType || "",
    //     CostctrHierGrp: ccData?.CostctrHierGrp ?? "ET_CCA",
    //     BusArea: "",
    //     CompCode: ccData?.compCode ?? "",
    //     Currency: ccData?.currency || "",
    //     ProfitCtr: ccData?.profitCtr || "",
    //     Name: ccData?.name || "",
    //     Descript: ccData?.description || "",
    //     PersonInChargeUser: ccData?.personResponsible || "",
    //     RecordQuantity: ccData?.RecordQuantity || "",
    //     LockIndActualPrimaryCosts: ccData?.lockIndActualPrimaryCosts || "",
    //     LockIndPlanPrimaryCosts: ccData?.lockIndPlanPrimaryCosts || "",
    //     LockIndActSecondaryCosts: ccData?.lockIndActSecondaryCosts || "",
    //     LockIndPlanSecondaryCosts: ccData?.lockIndPlanSecondaryCosts || "",
    //     LockIndActualRevenues: ccData?.lockIndActualRevenues || "",
    //     LockIndPlanRevenues: ccData?.lockIndPlanRevenues || "",
    //     LockIndCommitmentUpdate: ccData?.lockIndCommitmentUpdate || "",
    //     ConditionTableUsage: "",
    //     Application: "",
    //     CstgSheet: "",
    //     ActyIndepTemplate: ccData?.ActyIndepTemplate || "",
    //     ActyDepTemplate: ccData?.ActyDepTemplate || "",
    //     AddrTitle: ccData?.AddrTitle || "",
    //     AddrName1: ccData?.name1 || "",
    //     AddrName2: ccData?.name2 || "",
    //     AddrName3: ccData?.name3 || "",
    //     AddrName4: ccData?.name4 || "",
    //     AddrStreet: ccData?.street || "",
    //     AddrCity: ccData?.city || "",
    //     AddrDistrict: ccData?.AddrDistrict || "",
    //     AddrCountry: ccData?.country || "",
    //     AddrCountryIso: ccData?.AddrCountryIso || "",
    //     AddrTaxjurcode: ccData?.AddrTaxjurcode || "",
    //     AddrPoBox: ccData?.pocode || "",
    //     AddrPostlCode: ccData?.AddrPostlCode || "",
    //     AddrPobxPcd: ccData?.AddrPobxPcd || "",
    //     AddrRegion: ccData?.region || "",
    //     TelcoLangu: ccData?.TelcoLangu || "",
    //     TelcoLanguIso: ccData?.TelcoLanguIso || "",
    //     TelcoTelephone: ccData?.TelcoTelephone || "",
    //     TelcoTelephone2: ccData?.TelcoTelephone2 || "",
    //     TelcoTelebox: ccData?.TelcoTelebox || "",
    //     TelcoTelex: ccData?.TelcoTelex || "",
    //     TelcoFaxNumber: ccData?.TelcoFaxNumber || "",
    //     TelcoTeletex: ccData?.TelcoTeletex || "",
    //     TelcoPrinter: ccData?.TelcoPrinter || "",
    //     TelcoDataLine: ccData?.TelcoDataLine || "",
    //     ActyDepTemplateAllocCc: ccData?.ActyDepTemplateAllocCc || "",
    //     ActyDepTemplateSk: ccData?.ActyDepTemplateSk || "",
    //     ActyIndepTemplateAllocCc: ccData?.ActyIndepTemplateAllocCc || "",
    //     ActyIndepTemplateSk: ccData?.ActyIndepTemplateSk || "",
    //     AvcActive: ccData?.AvcActive===true ? true : ccData?.AvcActive===false ? false: null ,
    //     AvcProfile: ccData?.AvcProfile || "",
    //     BudgetCarryingCostCtr: ccData?.BudgetCarryingCostCtr || "",
    //     CurrencyIso: ccData?.CurrencyIso || "",
    //     Department: ccData?.Department || "",
    //     FuncArea: ccData?.FuncArea || "",
    //     FuncAreaFixAssigned: ccData?.FuncAreaFixAssigned || "",
    //     FuncAreaLong: ccData?.functionalArea || "",
    //     Fund: ccData?.Fund || "",
    //     FundFixAssigned: ccData?.FundFixAssigned || "",
    //     GrantFixAssigned: ccData?.GrantFixAssigned || "",
    //     GrantId: ccData?.GrantId || "",
    //     JvEquityTyp: ccData?.JvEquityTyp || "",
    //     JvJibcl: ccData?.JvJibcl || "",
    //     JvJibsa: ccData?.JvJibsa || "",
    //     JvOtype: ccData?.JvOtype || "",
    //     JvRecInd: ccData?.JvRecInd || "",
    //     JvVenture: ccData?.JvVenture || "",
    //     Logsystem: ccData?.Logsystem || "",
    //     FERCIndicator: "",
    //     TochangeLogData: {
    //       ChangeLogId: ccData?.TochangeLogData?.ChangeLogId?ccData?.TochangeLogData?.ChangeLogId:"", // should be mapped from display
    //       RequestId: "",  // should be mapped from display
    //       RequestHeaderId: "", // should be mapped from display
    //       ChangeLogData: null,
    //     },
    //     ToCostCenterErrorData: {
    //       CostCenterErrorId: ccData?.ToCostCenterErrorData?.CostCenterErrorId ?? "",
    //       RequestId: "",
    //       RequestHeaderId: initialPayload?.requestId,
    //       CostCenter: "",
    //       CompCode: "",
    //       ControllingArea: "",
    //       SAPMessage: "",
    //       ObjectSAPError: "",
    //       ObjectDBError: "",
    //       ObjectExcelError: "",
    //       ShortDescSAPError: "",
    //       ShortDescDBError: "",
    //       ShortDescExcelError: "",
    //       LongDescSAPError: "",
    //       LongDescDBError: "",
    //       LongDescExcelError: "",
    //       AddressValidation: "",
    //       PersonResponsibleError: "",
    //       UserResponsibleError: "",
    //       CheckUserAndPersonResponsible: "",
    //       DMSAttachmentErrorStatus: "",
    //       SAPAttachmentErrorStatus: ""
    //     }
    //   }
    // ],
    // Torequestheaderdata: {
    //     RequestId: initialPayload?.requestId ?? "",
    //     ReqCreatedBy: requestHeaderData?.ReqCreatedBy || "",
    //     ReqCreatedOn: `/Date(${new Date(requestHeaderData?.ReqCreatedOn).getTime()})/`,
    //     ReqUpdatedOn: `/Date(${new Date(requestHeaderData?.ReqUpdatedOn).getTime()})/`,
    //     RequestType: requestHeaderData?.RequestType || "",
    //     RequestPrefix: requestHeaderData?.requestPrefix ? requestHeaderData?.requestPrefix : "",
    //     RequestPriority: requestHeaderData?.RequestPriority || "",
    //     RequestDesc: requestHeaderData?.RequestDesc || "",
    //     RequestStatus: requestHeaderData?.RequestStatus || "",
    //     FirstProd: "",
    //     LaunchDate: "",
    //     LeadingCat: "",
    //     Division: "00",
    //     TemplateName: requestHeaderData?.TemplateName || "",
    //     FieldName: "",
    //     Region: "",
    //     FilterDetails: null,
    //     IsBifurcated: null,
    // },
    // ToGeneralInfoData: {
    //   GeneralInfoId: ccData?.GeneralInfoId ?? "",
    //   RequestPriority: requestHeaderData?.RequestPriority || "",
    //   BusinessJustification: "",
    //   SAPorJEErrorCheck: null,
    //   BusinessSegment: ccData?.businessSegment?.code ?? ccData?.BusinessSegment ?? "",
    //   Region: "",
    //   AAMNumber: ""
    // }
    // };
    //   });
   const Payload = payload;
      console.log("Payload", Payload);
  
      const hSuccess = (data) => {
        console.log("successdata", data);
  
        setIsLoading(false);
        setAlertType("success");
        setAlertMsg(
          "Cost Centers change submission for save as draft initiated"
        );
        setOpenSnackBar(true);
  
        setTimeout(() => {
          navigate("/requestbench");
        }, 2000);
      };
  
      const hError = (error) => {
        console.log("Errordata", error);
        setIsLoading(false);
        setAlertType("error");
        setAlertMsg("Error occurred while saving the draft.");
        setOpenSnackBar(true);
      };
  
      // 🔄 API call
      doAjax(
        `/${destination_CostCenter_Mass}/massAction/validateMassCostCenter`,
        "POST",
        hSuccess,
        hError,
        Payload
      );
  
      // console.log("Payload", Payload);
    };

  const handleValidateAndSyndicate = (type) => {
    const Payload = fetchReqBenchData.map((pcData) => {
      return {
        requestInProcess: "",
        ProfitCenterID: pcData.ProfitCenterID || "",
        TemplateName: requestHeaderData?.TemplateName || "",
        TemplateHeaders: "",
        IsScheduled: false,
        Action: "",
        RequestID: pcData.RequestID || "",
        TaskStatus: null,
        TaskId: task?.taskId,
        ReqCreatedBy: pcData?.createdBy || "",
        ReqCreatedOn: pcData?.historyTabDto?.ReqCreatedOn || "",
        RequestStatus: requestHeaderData?.requestStatus || "",
        CreationId: "",
        EditId: "",
        DeleteId: "",
        MassCreationId: "",
        MassEditId: requestId || "",
        MassDeleteId: "",
        ProfitCenterErrorID: pcData.ProfitCenterErrorID || "",
        RequestType: requestHeaderData?.requestType || "",
        MassRequestStatus: "",
        Remarks: null,
        TempLockRemarks: null,
        Info: "",
        ChangedFields: "",
        PrctrName: pcData?.profitCenterName || "",
        LongText: pcData?.description || "",
        InChargeUser: pcData?.userResponsible || "",
        InCharge: pcData?.personResponsible || "",
        Department: pcData?.Department || "",
        PrctrHierGrp: pcData?.PrctrHierGrp || "",
        Segment: pcData?.segment || "",
        LockInd: pcData?.indicatorsTabDto?.LockIndicator || false,
        Template: "",
        Title: pcData?.addressTabDto?.Title || "",
        Name1: pcData?.name1 || "",
        Name2: pcData?.name2 || "",
        Name3: pcData?.name3 || "",
        Name4: pcData?.name4 || "",
        Street: pcData?.street || "",
        City: pcData?.city || "",
        District: pcData?.district || "",
        Country: pcData?.country || "",
        Taxjurcode: pcData?.TaxJurisdiction || "",
        PoBox: pcData?.PoBox || "",
        PostalCode: pcData?.PostalCode || "",
        PobxPcd: pcData?.PobxPcd || "",
        Regio: pcData?.Regio || "",
        Language: pcData?.communicationTabDto?.Language || "",
        Telephone: pcData?.communicationTabDto?.Telephone || "",
        Telephone2: pcData?.communicationTabDto?.Telephone2 || "",
        Telebox: pcData?.communicationTabDto?.Telebox || "",
        Telex: pcData?.communicationTabDto?.Telex || "",
        FaxNumber: pcData?.communicationTabDto?.FaxNumber || "",
        Teletex: pcData?.communicationTabDto?.Teletex || "",
        Printer: pcData?.communicationTabDto?.Printer || "",
        DataLine: pcData?.communicationTabDto?.DataLine || "",
        ProfitCenter: pcData?.profitCenter || "",
        COArea: pcData?.controllingArea || "ETCA",
        ValidfromDate:
          pcData?.basicDataTabDto?.ValidfromDate || "/Date(-2208988800000)/",
        ValidtoDate:
          pcData?.basicDataTabDto?.ValidtoDate || "/Date(253402214400000)/",
        Testrun: null,
        IsFirstSynCompleted: false,
        TempLockIsSelectedForSyn: false,
        SelectedByRequestorToDisplay: false,
        IsSunoco: false,
        Countryiso: "",
        LanguIso: "",
        Logsystem: "",
        GeneralInfoID: null,
        RequestPriority: requestHeaderData?.requestPriority || "",
        BusinessJustification: null,
        SAPorJEErrorCheck: null,
        BusinessSegment: "CRUDE",
        HierarchyRegion: null,
        PCAAMNumber: pcData?.basicDataTabDto?.PCAAMNumber || "",
        ValidationDoneBy: "MDM Approval",
        TotalIntermediateTasks: pcData?.totalIntermediateTasks ?? "",

        ToCompanycode: [
          {
            CompCodeID: pcData.CompCodeID,
            CompanyCode: pcData.companyCode || "",
            CompanyName: "",
            AssignToPrctr: "",
            Venture: "",
            RecInd: "",
            EquityTyp: "",
            JvOtype: "",
            JvJibcl: "",
            JvJibsa: "",
          },
        ],

        Torequestheaderdata: {
          RequestId: requestHeaderData?.RequestId || "",
          ReqCreatedBy: requestHeaderData?.ReqCreatedBy || "",
          ReqCreatedOn:
            requestHeaderData?.ReqCreatedOn || "/Date(1743998564967)/",
          ReqUpdatedOn:
            requestHeaderData?.ReqUpdatedOn || "/Date(1743998564967)/",
          RequestType: requestHeaderData?.RequestType || "",
          RequestPrefix: requestHeaderData?.RequestPrefix || "",
          RequestPriority: requestHeaderData?.RequestPriority || "",
          RequestDesc: requestHeaderData?.RequestDesc || "",
          RequestStatus: "Draft",
          FirstProd: "",
          LaunchDate: "",
          LeadingCat: "Anesthesia/Pain Management",
          Division: "00",
          TemplateName: "",
          FieldName: "",
          Region: "",
          FilterDetails: null,
          IsBifurcated: null,
        },

        TochangeLogData: {
          ChangeLogData: null,
          ChangeLogId: pcData.ChangeLogId || "",
          RequestHeaderId: "",
          RequestId: "",
        },

        ToProfitCenterErrorData: {
          ProfitCenterErrorId: pcData.ProfitCenterErrorID || "",
          RequestId: pcData.RequestID || "",
          ProfitCenter: pcData?.profitCenter || "",
          CompanyCode: pcData?.companyCode || "",
          Segment: pcData?.basicDataTabDto?.Segment || "",
          PcAamNumber: pcData?.basicDataTabDto?.PCAAMNumber || "",
          ControllingArea: pcData?.controllingArea || "",
          SapMessage: "",
          ObjectSapError: "",
          ObjectDbError: "",
          ObjectExcelError: "",
          ShortDescSapError: "",
          ShortDescDbError: "",
          ShortDescExcelError: "",
          LongDescSapError: "",
          LongDescDbError: "",
          LongDescExcelError: "",
          AddrValidationError: "",
          PersonResponsibleError: "",
          DmsAttachmentErrorStatus: "",
          SapAttachmentErrorStatus: "",
          RemovalNodeErrorStatus: "",
          AddNodeErrorStatus: "",
        },

        ToGeneralInfoData: {
          GeneralInfoId: pcData.GeneralInfoId || "",
          RequestPriority: "",
          BusinessJustification: "",
          SAPorJEErrorCheck: "",
          BusinessSegment: "",
          Region: "",
        },
      };
    });

    console.log("Payloadreview", Payload);

    const hSuccess = (data) => {
      console.log("successdata", data);

      setIsLoading(false);
      setAlertType("success");
      setAlertMsg("Cost Centers submit for review successful");
      setOpenSnackBar(true);

      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };

    const hError = (error) => {
      console.log("Errordata", error);
      setIsLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving submit for review.");
      setOpenSnackBar(true);
    };

    // 🔄 API call
    doAjax(
      type === "validate"
        ? `/${destination_CostCenter_Mass}/massAction/validateMassCostCenter`
        : `/${destination_CostCenter_Mass}/massAction/changeCostCentersApproved`,
      "POST",
      hSuccess,
      hError,
      Payload
    );

    console.log("Payload", Payload);
  };

  const handleSnackBarOpen = () => {
    setOpenSnackbar(true);
  };

  const handleSnackBarClose = () => {
    setOpenSnackbar(false);
  };

  const handleDownloadDialogOpen = () => {
    setOpenDownloadDialog(true);
  };

  const handleDownloadDialogClose = () => {
    setOpenDownloadDialog(false);
    setDownloadType("systemGenerated");
  };

  const handleDownloadTypeChange = (event) => {
    setDownloadType(event?.target?.value);
  };

  const onDownloadTypeChange = () => {
    if (downloadType === "systemGenerated") {
      handleDownload();
      handleDownloadDialogClose();
    }
    if (downloadType === "mailGenerated") {
      handleEmailDownload();
      handleDownloadDialogClose();
    }
  };

  const handleDownload = () => {
    setLoaderMessage(
      "Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience."
    );
    setBlurLoading(true);
    setOpen(false);
    setDownloadClicked(false);
    if (!RequestId) {
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    }
    let payload = {
      coAreaCCs: selectedCostCenters.map((cc) => ({
        costCenter: cc,
        controllingArea: dropdown1Value,
      })),
      requestId:
        requestHeaderData?.RequestId || initialPayload?.requestId || "",
      templateHeaders: requestHeaderData?.FieldName
        ? requestHeaderData.FieldName?.join("$^$")
        : "",
      templateName: requestHeaderData?.TemplateName
        ? requestHeaderData.TemplateName
        : "",
      dtName: "MDG_CHANGE_TEMPLATE_DT",
      version: "v4",
    };
    const hSuccess = (response) => {
      if (response?.size == 0) {
        setBlurLoading(false);
        setLoaderMessage("");
        showToast(ERROR_MESSAGES?.NO_DATA_FOUND, "error", {
          position: "top-center",
          largeWidth: true,
        });
        setTimeout(() => {
          navigate(APP_END_POINTS?.REQUEST_BENCH);
        }, 2600);
        return;
      }
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");

      link.href = href;
      link.setAttribute(
        "download",
        `${requestHeaderData?.TemplateName}_Mass Change.xlsx`
      );
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);

      setBlurLoading(false);
      setLoaderMessage("");

      setSuccessMsg(true);
      setMessageDialogMessage(
        `${requestHeaderData?.TemplateName}_Mass Change.xlsx has been downloaded successfully.`
      );
      setAlertType("success");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };
    const hError = () => {
      setBlurLoading(false);
      setLoaderMessage("");
      showToast(ERROR_MESSAGES?.ERR_DOWNLOADING_EXCEL, "error", {
        position: "top-center",
      });
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/excel/downloadExcelWithData`,
      "postandgetblob",
      hSuccess,
      hError,
      payload
    );
  };

  const handleEmailDownload = () => {
    setBlurLoading(true);
    onClose();
    let templateKeys =
      Templates[initialPayload?.TemplateName]?.map((item) => item.key) || [];
    let payload = {};
    if (activeTab === 0) {
      payload = {
        materialDetails: [
          templateKeys.reduce((acc, key) => {
            acc[key] = convertedValues?.[key] ? convertedValues?.[key] : "";
            return acc;
          }, {}),
        ],
        templateHeaders: initialPayload?.FieldName
          ? initialPayload.FieldName?.join("$^$")
          : "",
        requestId: RequestId || initialPayload?.RequestId || "",
        templateName: initialPayload?.TemplateName
          ? initialPayload.TemplateName
          : "",
        dtName: "MDG_MAT_CHANGE_TEMPLATE",
        version: "v4",
        rolePrefix: "",
      };
    } else {
      payload = {
        materialDetails: [
          templateKeys.reduce((acc, key) => {
            acc[key] =
              rowsOfMaterialData
                .map((row) => row[key]?.trim())
                .filter((value) => value !== "")
                .join(",") || "";
            return acc;
          }, {}),
        ],
        templateHeaders: initialPayload?.FieldName
          ? initialPayload.FieldName?.join("$^$")
          : "",
        requestId: RequestId || initialPayload?.RequestId || "",
        templateName: initialPayload?.TemplateName
          ? initialPayload.TemplateName
          : "",
        dtName: "MDG_MAT_CHANGE_TEMPLATE",
        version: "v4",
        rolePrefix: "",
      };
    }
    const hSuccess = () => {
      setBlurLoading(false);
      setLoaderMessage("");
      setSuccessMsg(true);
      setMessageDialogMessage(
        `Download has been started. You will get the Excel file via email.`
      );
      setAlertType("success");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };
    const hError = () => {
      setBlurLoading(false);
      setSuccessMsg(true);
      setMessageDialogMessage(
        "Oops! Something went wrong. Please try again later."
      );
      setAlertType("danger");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };
    doAjax(
      `/${destination_MaterialMgmt}/excel/downloadExcelWithDataInMail`,
      "postandgetblob",
      hSuccess,
      hError,
      payload
    );
  };

  const highlightedColumns = columns.map((col) => ({
    ...col,
    renderCell: (params) => {
      const isChanged =
        changedFieldsMap[params.row.CostCenterID]?.[col.field];
      console.log("isChanged", isChanged);
      return (
        <div
          style={{
            backgroundColor: isChanged ? "rgba(255, 229, 100, 0.6)" : "inherit",
            padding: "0 4px",
            borderRadius: 4,
            height: "100%",
            display: "flex",
            alignItems: "center",
          }}
        >
          {params.value}
        </div>
      );
    },
  }));

  const isChangeFieldEmpty = (changedFieldsMap) =>
    changedFieldsMap &&
    Object.values(changedFieldsMap).every(
      (fields) =>
        typeof fields === "object" &&
        fields !== null &&
        Object.keys(fields).length === 0
    );

//   const isEmpty = isChangeFieldEmpty(changedFieldsMap);
//   console.log("isEmpty", isEmpty);

  
  return (
    <div>
      {successMsg && (
        <ReusableSnackBar
          openSnackBar={openSnackbar}
          alertMsg={messageDialogMessage}
          alertType={alertType}
          handleSnackBarClose={handleSnackBarClose}
        />
      )}
      {(requestHeaderData?.TemplateName || downloadClicked) && (
        <>
          {fetchedGeneralLedgerData?.length === 0 && !reqBench && (
            <>
              <Dialog
                open={open}
                TransitionComponent={Transition}
                keepMounted
                onClose={(event, reason) => {
                  if (
                    reason === "backdropClick" ||
                    reason === "escapeKeyDown"
                  ) {
                    return;
                  }
                  handleClose();
                }}
                maxWidth="sm"
                fullWidth
              >
                <Box
                  sx={{
                    backgroundColor: "#e3f2fd",
                    padding: "1rem 1.5rem",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <FeedOutlinedIcon
                    color="primary"
                    sx={{ marginRight: "0.5rem" }}
                  />
                  <Typography variant="h6" component="div" color="primary">
                    {requestHeaderData?.TemplateName} Search Filter(s)
                  </Typography>
                </Box>

                <DialogContent sx={{ padding: "1.5rem 1.5rem 1rem" }}>
                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{
                        key: "chartOfAccount",
                        label: "Chart Of Account",
                      }}
                      dropDownData={{
                        chartOfAccount: [{ code: "ETCN", desc: "ETCN" }],
                      }}
                      selectedValues={{
                        chartOfAccount: dropdown1Value
                          ? [{ code: dropdown1Value }]
                          : [],
                      }}
                      handleSelectionChange={(key, value) => {
                        setDropdown1Value(
                          value.length > 0 ? value[0].code || value[0] : ""
                        );
                      }}
                      formatOptionLabel={(option) => {
                        if (option.code && option.desc) {
                          return `${option.code} - ${option.desc}`;
                        }
                        return option.code || "";
                      }}
                      singleSelect={true}
                      errors={{}}
                    />
                  </Box>
                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{ key: "companyCode", label: "Company Code" }}
                      dropDownData={{ companyCode: dropdownDataCompany || [] }}
                      selectedValues={{
                        companyCode: selectedCompanyCodes.map(
                          (code) =>
                            dropdownDataCompany?.find(
                              (item) => item.code === code
                            ) || { code }
                        ),
                      }}
                      handleSelectAll={(key) => {
                        if (
                          selectedCompanyCodes.length ===
                          dropdownDataCompany?.length
                        ) {
                          setSelectedCompanyCodes([]);
                        } else {
                          setSelectedCompanyCodes(
                            dropdownDataCompany?.map((item) => item.code) || []
                          );
                        }
                      }}
                      handleSelectionChange={(key, value) => {
                        setSelectedCompanyCodes(
                          value.map((item) =>
                            typeof item === "string" ? item : item.code || item
                          )
                        );
                      }}
                      formatOptionLabel={(option) => {
                        if (option.code && option.desc) {
                          return `${option.code} - ${option.desc}`;
                        }
                        return option.code || "";
                      }}
                      isSelectAll={true}
                      errors={{}}
                    />
                  </Box>

                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{ key: "generalLedger", label: "general Ledger" }}
                      dropDownData={{
                        generalLedger: costCenterOptions
                      }}
                      selectedValues={{
                        generalLedger:
                          selectedCostCenters?.map((code) => ({ code })) || [],
                      }}
                      handleSelectAll={(key) => {
                        if (
                          selectedCostCenters?.length ===
                          costCenterOptions?.length
                        ) {
                          setSelectedCostCenters([]);
                        } else {
                          setSelectedCostCenters(costCenterOptions || []);
                        }
                      }}
                      handleSelectionChange={(key, value) => {
                        setSelectedCostCenters(
                          value?.map((item) =>
                            typeof item === "string" ? item : item?.code || item
                          )
                        );
                      }}
                      formatOptionLabel={(option) =>
                        typeof option === "string"
                          ? option
                          : option?.code || option
                      }
                      isSelectAll={true}
                      errors={{}}
                    />
                  </Box>
                </DialogContent>

                <DialogActions
                  sx={{
                    padding: "0.5rem 1.5rem",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <Box sx={{ display: "flex", gap: 1 }}>
                    <Button
                      onClick={handleClose}
                      color="error"
                      variant="outlined"
                      sx={{
                        height: 36,
                        minWidth: "3.5rem",
                        textTransform: "none",
                        borderColor: "#cc3300",
                        fontWeight: 500,
                      }}
                    >
                      Cancel
                    </Button>
                    {requestHeaderData?.RequestType !==
                      REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
                      <Button
                        onClick={handleOk}
                        variant="contained"
                        sx={{
                          height: 36,
                          minWidth: "3.5rem",
                          backgroundColor: "#3B30C8",
                          textTransform: "none",
                          fontWeight: 500,
                          "&:hover": {
                            backgroundColor: "#2c278f",
                          },
                        }}
                      >
                        OK
                      </Button>
                    )}
                    {requestHeaderData?.RequestType ===
                      REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
                      <Button
                        onClick={() => {
                          handleDownloadDialogOpen();
                        }}
                        variant="contained"
                        sx={{
                          height: 36,
                          minWidth: "3.5rem",
                          backgroundColor: "#3B30C8",
                          textTransform: "none",
                          fontWeight: 500,
                          "&:hover": {
                            backgroundColor: "#2c278f",
                          },
                        }}
                      >
                        Download
                      </Button>
                    )}
                  </Box>
                </DialogActions>
              </Dialog>

              <DownloadDialog
                onDownloadTypeChange={onDownloadTypeChange}
                open={openDownloadDialog}
                downloadType={downloadType}
                handleDownloadTypeChange={handleDownloadTypeChange}
                onClose={handleDownloadDialogClose}
              />
              <ReusableBackDrop
                blurLoading={blurLoading}
                loaderMessage={loaderMessage}
              />
            </>
          )}
          {showGrid && (
            <Box sx={{ mt: 4, px: 4 }}>
              <Typography variant="h5" sx={{ fontWeight: 600, mb: 2 }}>
                General ledger List
              </Typography>

              <Paper
                elevation={3}
                sx={{
                  borderRadius: 3,
                  overflow: "hidden",
                  border: "1px solid #e0e0e0",
                  backgroundColor: "#fafbff",
                }}
              >
                <Box sx={{ p: 2 }}>
                  <ReusableDataTable
                    rows={fetchedGeneralLedgerData}
                    columns={columns}
                    pageSize={10}
                    tempheight="50vh"
                    getRowIdValue="id"
                    editMode="row"
                    status_onRowSingleClick
                    callback_onRowSingleClick={handleRowClick}
                    processRowUpdate={processRowUpdate}
                    experimentalFeatures={{ newEditingApi: true }}
                    isCellEditable={(params) =>
                      !["costCenter", "companyCode"].includes(params.field)
                    }
                    getRowClassName={(params) =>
                      selectedRow?.id === params.row.id ? "Mui-selected" : ""
                    }
                  />
                </Box>
              </Paper>

              <Box
                sx={{ display: "flex", justifyContent: "right", mt: 3, gap: 2 }}
              >
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleSaveAsDraft}
                >
                  Save as draft
                </Button>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={validateAllRows}
                >
                  Validate
                </Button>
                <Button
                  variant="contained"
                  color="secondary"
                  onClick={handleSubmitForReview}
                >
                  Submit
                </Button>
              </Box>
            </Box>
          )}
        </>
      )}
      <>
        {fetchReqBenchData.length === 0 && reqBench === "true" && (
          <>
            <Dialog
              open={open}
              TransitionComponent={Transition}
              keepMounted
              onClose={(event, reason) => {
                if (reason === "backdropClick" || reason === "escapeKeyDown") {
                  return;
                }
                handleClose();
              }}
              maxWidth="sm"
              fullWidth
            >
              <Box
                sx={{
                  backgroundColor: "#e3f2fd",
                  padding: "1rem 1.5rem",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <FeedOutlinedIcon
                  color="primary"
                  sx={{ marginRight: "0.5rem" }}
                />
                <Typography variant="h6" component="div" color="primary">
                  {requestHeaderData?.TemplateName} Search Filter(s)
                </Typography>
              </Box>

              <DialogContent sx={{ padding: "1.5rem 1.5rem 1rem" }}>
                <Box sx={{ marginBottom: "1rem" }}>
                  <FilterChangeDropdown
                    param={{
                      key: "controllingArea",
                      label: "Controlling Area",
                    }}
                    dropDownData={{
                      controllingArea: [{ code: "ETCA", desc: "ETCA" }],
                    }}
                    selectedValues={{
                      controllingArea: dropdown1Value
                        ? [{ code: dropdown1Value }]
                        : [],
                    }}
                    handleSelectionChange={(key, value) => {
                      setDropdown1Value(
                        value.length > 0 ? value[0].code || value[0] : ""
                      );
                    }}
                    formatOptionLabel={(option) => {
                      if (option.code && option.desc) {
                        return `${option.code} - ${option.desc}`;
                      }
                      return option.code || "";
                    }}
                    singleSelect={true}
                    errors={{}}
                  />
                </Box>
                <Box sx={{ marginBottom: "1rem" }}>
                  <FilterChangeDropdown
                    param={{ key: "companyCode", label: "Company Code" }}
                    dropDownData={{ companyCode: dropdownDataCompany || [] }}
                    selectedValues={{
                      companyCode: selectedCompanyCodes.map(
                        (code) =>
                          dropdownDataCompany?.find(
                            (item) => item.code === code
                          ) || { code }
                      ),
                    }}
                    handleSelectAll={(key) => {
                      if (
                        selectedCompanyCodes.length ===
                        dropdownDataCompany?.length
                      ) {
                        setSelectedCompanyCodes([]);
                      } else {
                        setSelectedCompanyCodes(
                          dropdownDataCompany?.map((item) => item.code) || []
                        );
                      }
                    }}
                    handleSelectionChange={(key, value) => {
                      setSelectedCompanyCodes(
                        value.map((item) =>
                          typeof item === "string" ? item : item.code || item
                        )
                      );
                    }}
                    formatOptionLabel={(option) => {
                      if (option.code && option.desc) {
                        return `${option.code} - ${option.desc}`;
                      }
                      return option.code || "";
                    }}
                    isSelectAll={true}
                    errors={{}}
                  />
                </Box>

                <Box sx={{ marginBottom: "1rem" }}>
                  <FilterChangeDropdown
                    param={{ key: "costCenter", label: "Cost Center" }}
                    dropDownData={{
                      costCenter:
                        costCenterOptions.map((code) => ({ code })) || [],
                    }}
                    selectedValues={{
                      costCenter:
                        selectedCostCenters.map((code) => ({ code })) || [],
                    }}
                    handleSelectAll={(key) => {
                      if (
                        selectedCostCenters.length ===
                        costCenterOptions.length
                      ) {
                        setSelectedCostCenters([]);
                      } else {
                        setSelectedCostCenters(costCenterOptions || []);
                      }
                    }}
                    handleSelectionChange={(key, value) => {
                      setSelectedCostCenters(
                        value.map((item) =>
                          typeof item === "string" ? item : item.code || item
                        )
                      );
                    }}
                    formatOptionLabel={(option) =>
                      typeof option === "string"
                        ? option
                        : option.code || option
                    }
                    isSelectAll={true}
                    errors={{}}
                  />
                </Box>
              </DialogContent>

              <DialogActions
                sx={{
                  padding: "0.5rem 1.5rem",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <Box sx={{ display: "flex", gap: 1 }}>
                  <Button
                    onClick={handleClose}
                    color="error"
                    variant="outlined"
                    sx={{
                      height: 36,
                      minWidth: "3.5rem",
                      textTransform: "none",
                      borderColor: "#cc3300",
                      fontWeight: 500,
                    }}
                  >
                    Cancel
                  </Button>
                  {requestHeaderData?.RequestType !==
                    REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
                    <Button
                      onClick={handleOk}
                      variant="contained"
                      sx={{
                        height: 36,
                        minWidth: "3.5rem",
                        backgroundColor: "#3B30C8",
                        textTransform: "none",
                        fontWeight: 500,
                        "&:hover": {
                          backgroundColor: "#2c278f",
                        },
                      }}
                    >
                      OK
                    </Button>
                  )}
                  {requestHeaderData?.RequestType ===
                    REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
                    <Button
                      onClick={() => {
                        handleDownloadDialogOpen();
                      }}
                      variant="contained"
                      sx={{
                        height: 36,
                        minWidth: "3.5rem",
                        backgroundColor: "#3B30C8",
                        textTransform: "none",
                        fontWeight: 500,
                        "&:hover": {
                          backgroundColor: "#2c278f",
                        },
                      }}
                    >
                      Download
                    </Button>
                  )}
                </Box>
              </DialogActions>
            </Dialog>

            <DownloadDialog
              onDownloadTypeChange={onDownloadTypeChange}
              open={openDownloadDialog}
              downloadType={downloadType}
              handleDownloadTypeChange={handleDownloadTypeChange}
              onClose={handleDownloadDialogClose}
            />
            <ReusableBackDrop
              blurLoading={blurLoading}
              loaderMessage={loaderMessage}
            />
          </>
        )}
        {reqBench === "true" && (
          <Box sx={{ marginTop: "20px", padding: "16px" }}>
            <Typography variant="h5" gutterBottom>
              Cost Center Lists
            </Typography>
            <Paper
              elevation={4}
              sx={{ p: 0, borderRadius: 2, overflow: "hidden", mt: "50px" }}
            >
              <div>
                <ReusableDataTable
                  // isLoading={loading}
                  rows={fetchReqBenchData}
                  columns={columns}
                  pageSize={10}
                  tempheight={"50vh"}
                  getRowIdValue={"id"}
                  status_onRowSingleClick={true}
                  editMode="cell"
                  callback_onRowSingleClick={handleRowClick}
                  processRowUpdate={processRowUpdateReqBench}
                  experimentalFeatures={{ newEditingApi: true }}
                  isCellEditable={(params) =>
                    !["costCenter", "companyCode"].includes(params.field)
                  }
                  getRowClassName={(params) =>
                    selectedRow?.id === params.row.id ? "Mui-selected" : ""
                  }
                />
              </div>
            </Paper>
            <Box
              sx={{ display: "flex", justifyContent: "right", mt: 3, gap: 2 }}
            >
              <BottomNavGlobal
                handleSaveAsDraft={handleSaveAsDraft}
                handleSubmitForReview={handleSubmitForReview}
                handleSubmitForApprove={handleSubmitForApprove}
                handleSendBack={handleSendBack}
                // handleCorrection={handleCorrection}
                handleRejectAndCancel={handleRejectAndCancel}
                handleValidateAndSyndicate={handleValidateAndSyndicate}
                validateAllRows={validateAllRows}
                // isSaveAsDraftEnabled={isSaveAsDraftEnabled}
                filteredButtons={filteredButtons}
              />
            </Box>
          </Box>
        )}
      </>
    </div>
  );
};

export default RequestDetailsChangeGL;
