import {
  Backdrop,
  CircularProgress,
  BottomNavigation,
  Box,
  Button,
  Checkbox,
  Grid,
  IconButton,
  Paper,
  Stack,
  Tab,
  Tabs,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  FormControl,
  TextField,
  DialogActions,
  Tooltip,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import {
  iconButton_SpacingSmall,
  outermostContainer,
  button_Primary,
  outerContainer_Information,
  outermostContainer_Information,
} from "../../common/commonStyles";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import ReusableTable from "../../common/ReusableTable";
import { useNavigate, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { doAjax } from "../../Common/fetchService";
import {
  destination_CostCenter,
  destination_DocumentManagement,
  destination_GeneralLedger,
} from "../../../destinationVariables";
import ReusableSnackBar from "../../Common/ReusableSnackBar";
import moment from "moment/moment";
import ReusableDialog from "../../Common/ReusableDialog";
import LoadingComponent from "../../Common/LoadingComponent";
import CloseIcon from "@mui/icons-material/Close";
import lookup from "../../../data/lookup.json";
import { setDropDown } from "../../../app/dropDownDataSlice";
import { idGenerator } from "../../../functions";
import ReusableAttachementAndComments from "../../Common/ReusableAttachmentAndComments/ReusableAttachementAndComments";
import AttachFileOutlinedIcon from "@mui/icons-material/AttachFileOutlined";

const CreateMultipleGL = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [value, setValue] = useState("1");
  const [selectedRows, setSelectedRows] = useState([]);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [messageDialogExtra, setMessageDialogExtra] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState(false);
  const [messageDialogSeverity, setMessageDialogSeverity] = useState(false);
  const [messageDialogOK, setMessageDialogOK] = useState(true);
  const [successMsg, setsuccessMsg] = useState(false);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [submitForReviewDisabled, setSubmitForReviewDisabled] = useState(true);
  const [blurLoading, setBlurLoading] = useState(false);
  const [testRun, setTestRun] = useState(false);
  const [openCorrectionDialog, setOpenCorrectionDialog] = useState(false);
  const [remarks, setRemarks] = useState("");
  const [profitValidationError, setProfitValidationErrors] = useState([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [validateFlag, setValidateFlag] = useState(false);
  const [glNumber, setGlNumber] = useState("");
  const [openDialog, setOpenDialog] = useState(false);
  const [directMatchedProfitCenters, setDirectMatchedProfitCenters] = useState([]);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  let multipleCostData = useSelector(
    (state) => state.generalLedger.MultipleGLData
  );
  const location = useLocation();
  const multipleProfitData = useSelector(
    (state) => state.generalLedger.MultipleGLData
  );
  const appSettings = useSelector((state) => state.appSettings);
  let massHandleType = useSelector(
    (state) => state.generalLedger.handleMassMode
  );
  console.log("massHandleType", massHandleType);
  let userData = useSelector((state) => state.userManagement.userData);
  const [apiCount, setApiCount] = useState(0);
  const fetchDynamicApiData = (keyName, endPoint) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: keyName, data: data.body }));
      // setIsLoading(false);
      setApiCount((prev) => prev + 1);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/${endPoint}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getAllLookups = () => {
    lookup?.generalLedger?.map((item) => {
      fetchDynamicApiData(item?.keyName, item?.endPoint);
    });
  };
  const loaderCount = () => {
    if (apiCount == lookup?.generalLedger?.length) {
      setIsLoading(false);
    } else {
      setIsLoading(true);
    }
  };
  useEffect(() => {
    loaderCount();
  }, [apiCount]);
  useEffect(() => {
    getAllLookups();
  }, []);
  useEffect(() => {
    setGlNumber(idGenerator("GL"));
  }, []);
  const initialRows = multipleProfitData?.map((pCenter, index) => {
    const headerData = pCenter;
    const basicData = pCenter?.viewData || {};
    return {
      id: index,
      chartOfAccount: headerData?.ChartOfAccount || "",
      companyCode: headerData?.CompCode || "",
      glAccount: headerData?.GLAccount || "",
      accountType:
        basicData["Type/Description"]["Control in COA"]?.find(
          (field) => field?.fieldName === "Account Type"
        )?.value || "",
      accountGroup:
        basicData["Type/Description"]["Control in COA"]?.find(
          (field) => field?.fieldName === "Account Group"
        )?.value || "",
      // functionalArea:
      //     basicData["Type/Description"]["Detailed Control for P&L Statement Accounts"].find(
      //       (field) => field?.fieldName === "Functional Area"
      //     )?.value||"",
      shortText:
        basicData["Type/Description"]["Description"]?.find(
          (field) => field?.fieldName === "Short Text"
        )?.value || "",
      longText:
        basicData["Type/Description"]["Description"]?.find(
          (field) => field?.fieldName === "Long Text"
        )?.value || "",
      accountCurrency:
        basicData["Control Data"]["Account Control in Company Code"]?.find(
          (field) => field?.fieldName === "Account Currency"
        )?.value || "",
    };
  });
  const columns = [
    {
      field: "glAccount",
      headerName: "GL Account",
      editable: false,
      flex: 1,
      renderCell: (params) => {
        const isDirectMatch = profitValidationError.find(element=> element.generalLedger===params.value);
        console.log(isDirectMatch, "isDirectMatch")
        console.log(params, "params")
  
        if (isDirectMatch && isDirectMatch.code === 400) {
          return (
            <Typography sx={{ fontSize: "12px", color: "red" }}>
              {params.value}
            </Typography>
          );
        } else {
          return (
            <Typography sx={{ fontSize: "12px" }}>
              {params.value}
            </Typography>
          );
        }
      },
    },
    {
      field: "chartOfAccount",
      headerName: "Chart Of Account",
      editable: false,
      flex: 1,
    },
    {
      field: "companyCode",
      headerName: "Company Code",
      editable: false,
      flex: 1,
    },
    {
      field: "accountType",
      headerName: "Account Type",
      editable: false,
      flex: 1,
    },
    {
      field: "accountGroup",
      headerName: "Account Group",
      editable: false,
      flex: 1,
    },
    {
      field: "shortText",
      headerName: "Short Text",
      editable: false,
      flex: 1,
      renderCell: (params) => {
        const isDirectMatch = directMatchedProfitCenters.includes(
          params.row.profitCenterName
        );
        return (
          <Typography
            sx={{ fontSize: "12px", color: isDirectMatch ? "red" : "inherit" }}
          >
            {params.value}
          </Typography>
        );
      },
    },

    {
      field: "longText",
      headerName: "Long Text",
      editable: false,
      flex: 1,
    },
    {
      field: "accountCurrency",
      headerName: "Account Currency",
      editable: false,
      flex: 1,
    },
  ];
 
  const getValueForFieldName = (data, fieldName) => {
    const field = data?.find((field) => field?.fieldName === fieldName);
    return field ? field.value : "";
  };

  var payloadmapping = multipleProfitData.map((x) => {
    return {
    GeneralLedgerID: "",
    Action: massHandleType === "Create" ? "I" : "U",
    RequestID: "",
    TaskStatus: "",
    TaskId: "",
    Remarks:remarks ? remarks : "",
    Info: "",
    CreationId: "",
    EditId: "",
    DeleteId: "",
    MassCreationId: "",
    MassEditId: "",
    MassDeleteId: "",
    RequestType: massHandleType === "Create" ? "Mass Create" : "Mass Change",
    ReqCreatedBy: userData?.user_id,
    ReqCreatedOn: userData?.createdOn
    ? "/Date(" + userData?.createdOn + ")/"
    : "",
    ReqUpdatedOn: "",
    RequestStatus: "",
    Testrun: testRun,
    COA:x?.ChartOfAccount,
    CompanyCode:x?.CompCode,
    CoCodeToExtend: "",
    GLAccount:x?.GLAccount,
    Accounttype: getValueForFieldName(
      x?.viewData["Type/Description"]?.["Control in COA"],
      "Account Type"
    ),
    AccountGroup:getValueForFieldName(
      x?.viewData["Type/Description"]?.["Control in COA"],
      "Account Group"
    ),
    GLname: getValueForFieldName(
      x?.viewData["Type/Description"]?.["Description"],
      "Short Text"
    ),
    Description: getValueForFieldName(
      x?.viewData["Type/Description"]?.["Description"],
      "Long Text"
    ),
    TradingPartner: getValueForFieldName(
      x?.viewData["Type/Description"]?.["Consolidation Data in COA"],
      "Trading Partner"
    ),
    GroupAccNo: getValueForFieldName(
      x?.viewData["Type/Description"]?.["Consolidation Data in COA"],
      "Group Account Number"
    ),
    AccountCurrency: getValueForFieldName(
      x?.viewData["Control Data"]?.["Account Control in Company Code"],
      "Account Currency"
    ),
    Exchangerate:getValueForFieldName(
      x?.viewData["Control Data"]?.["Account Control in Company Code"],
      "Exchange Rate Difference Key"
    ),
    Balanceinlocrcy: getValueForFieldName(
      x?.viewData["Control Data"]?.["Account Control in Company Code"],
      "Only Balance In Local Currency"
    )=== true
    ? "X"
    : "",
    Taxcategory: getValueForFieldName(
      x?.viewData["Control Data"]?.["Account Control in Company Code"],
      "Tax Category"
    ),
    Pstnwotax: getValueForFieldName(
      x?.viewData["Control Data"]?.["Account Control in Company Code"],
     "Posting Without Tax Allowed"
    )=== true
    ? "X"
    : "",
    ReconAcc: getValueForFieldName(
      x?.viewData["Control Data"]?.["Account Control in Company Code"],
      "Recon. Account For Account Type"
    ),
    Valuationgrp: getValueForFieldName(
      x?.viewData["Control Data"]?.["Account Control in Company Code"],
      "Valuation Group"
    ),
    AlterAccno: getValueForFieldName(
      x?.viewData["Control Data"]?.["Account Control in Company Code"],
      "Alternative Account Number"
    ),
    Openitmmanage:getValueForFieldName(
      x?.viewData["Control Data"]?.["Account Management in Company Code"],     
      "Open Item Management"
    )=== true
    ? "X"
    : "",
    Sortkey: getValueForFieldName(
      x?.viewData["Control Data"]?.["Account Management in Company Code"],     
      "Sort Key"
    ),
    CostEleCategory: getValueForFieldName(
      x?.viewData["Control Data"]?.["Account Management in Company Code"],     
      "Sort Key"
    ),
    FieldStsGrp: getValueForFieldName(
      x?.viewData["Create/Bank/Interest"]?.["Control of Document creation in Company Code"],     
      "Field Status Group"
    ),
    PostAuto:getValueForFieldName(
      x?.viewData["Create/Bank/Interest"]?.["Control of Document creation in Company Code"],     
      "Post Automatically Only"
    )=== true
    ? "X"
    : "",
    Supplementautopost:getValueForFieldName(
      x?.viewData["Create/Bank/Interest"]?.["Control of Document creation in Company Code"],     
      "Supplement Auto Postings"
    )=== true
    ? "X"
    : "",
    Planninglevel: getValueForFieldName(
      x?.viewData["Create/Bank/Interest"]?.["Bank/Financial Details in Company Code"],     
      "Planning Level"
    ),
    Relvnttocashflow: getValueForFieldName(
      x?.viewData["Create/Bank/Interest"]?.["Bank/Financial Details in Company Code"],     
      "Relevant To Cash Flows"
    )=== true
    ? "X"
    : "",
    HouseBank:  getValueForFieldName(
      x?.viewData["Create/Bank/Interest"]?.["Bank/Financial Details in Company Code"],     
      "House Bank"
    ),
    AccountId: getValueForFieldName(
      x?.viewData["Create/Bank/Interest"]?.["Bank/Financial Details in Company Code"],     
      "Account ID"
    ),
    Interestindicator:getValueForFieldName(
      x?.viewData["Create/Bank/Interest"]?.["Interest Calculation Information in Company Code"],     
      "Interest Indicator"
    ),
    ICfrequency:getValueForFieldName(
      x?.viewData["Create/Bank/Interest"]?.["Interest Calculation Information in Company Code"],     
      "Interest Calculation Frequency"
    ),
    KeydateofLIC:getValueForFieldName(
      x?.viewData["Create/Bank/Interest"]?.["Interest Calculation Information in Company Code"],     
      "Key Date Of Last Interest Calculation"
    ),
    LastIntrstundate:getValueForFieldName(
      x?.viewData["Create/Bank/Interest"]?.["Interest Calculation Information in Company Code"],     
      "Date Of Last Interest Run"
    ), 
    AccmngExistsys: "",
    Infationkey: "",
    Tolerancegrp: "",
    AuthGroup: "",
    AccountClerk: "",
    ReconAccReady: "",
    PostingBlocked: "",
    PlanningBlocked: "",
  };
});

  const handleSelectionModelChange = (selectedIds) => {
    if (selectedIds.length > 0) {
      setTestRun(true);
      console.log("selectedIds1", selectedIds);
    } else {
      setTestRun(false);
    }
    console.log("selectedIds", selectedIds);
    setSelectedRows(selectedIds);
    // setTestRun(true);
  };
  const handleSnackBarOpen = () => {
    setopenSnackbar(true);
  };

  const handleSnackBarClose = () => {
    if (validateFlag) {
      setopenSnackbar(false);
      setValidateFlag(false);
    } else {
      setopenSnackbar(false);
      navigate("/masterDataCockpit/generalLedger");
    }
  };
  const handleOpenDialog = () => {
    setOpenDialog(true);
  };
  const handleCloseDialog = () => {
    setOpenDialog(false);
  };
  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };
  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };
  const handleMessageDialogNavigate = () => {
    // navigate("/masterDataCockpit/materialMaster/materialSingle");
  };

  const handleSubmitForReview = () => {
    const selectedData = initialRows.filter((_, index) =>
      selectedRows.includes(index)
    );
    console.log("selectedData", selectedData);
    const selectedProfitCenterRows = selectedData.map((x) => ({
      // console.log("Data", x)
      ...payloadmapping[x?.id],
    }));
    let payload = payloadmapping;
    payload = selectedProfitCenterRows;
    console.log("selectedProfitCenterRows", selectedProfitCenterRows);
    const hSuccess = (data) => {
      // setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Mass General Ledger Sent for Review with ID NLM${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setBlurLoading(false);
        // setIsLoading(false);
        const secondApiPayload = {
          artifactId: glNumber,
          createdBy: userData?.emailId,
          artifactType: "GeneralLedger",
          requestId: `NLM${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting the General Ledger for Review "
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setBlurLoading(false);
        // setIsLoading(false);
      }
      handleClose();
      setBlurLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/massAction/generalLedgersSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const handleSubmitForReviewChange = () => {
    // setIsLoading(true);
    const selectedData = initialRows.filter((_, index) =>
      selectedRows.includes(index)
    );
    console.log("selectedData", selectedData);
    const selectedProfitCenterRows = selectedData.map((x) => ({
      // console.log("Data", x)
      ...payloadmapping[x?.id],
    }));
    let payload = payloadmapping;
    payload = selectedProfitCenterRows;
    console.log("selectedProfitCenterRows", selectedProfitCenterRows);
    const hSuccess = (data) => {
      // setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Mass General Ledger Sent for Review with ID CGM${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setBlurLoading(false);
        // setIsLoading(false);
        const secondApiPayload = {
          artifactId: glNumber,
          createdBy: userData?.emailId,
          artifactType: "GeneralLedger",
          requestId: `CGM${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting the General Ledger for Review "
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setBlurLoading(false);
        // setIsLoading(false);
      }
      handleClose();
      setBlurLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/massAction/changeGeneralLedgersSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const onValidateGeneralLedger = () => {
    setBlurLoading(true);
    const selectedData = initialRows.filter((_, index) =>
      selectedRows.includes(index)
    );
    console.log("selectedData", selectedData);
    const selectedProfitCenterRows = selectedData.map((x) => ({
      ...payloadmapping[x?.id],
    }));
    console.log("selectedProfitCenterRows", selectedProfitCenterRows);
    const duplicateCheckPayload = [];
    selectedProfitCenterRows.map((x) => {
      var idk = {
        glName: x?.GLname?.toUpperCase(),
        compCode: x?.CompanyCode,
      };
      duplicateCheckPayload.push(idk);
    });
    console.log("duplicateCheckPayload", duplicateCheckPayload);
    let payload = payloadmapping;
    payload = selectedProfitCenterRows;
    const hSuccess = (data) => {
      // setIsLoading(false);
      if (data.statusCode === 400) {
        setProfitValidationErrors(data.body);
        setDialogOpen(true);
        setBlurLoading(false);
      } else {
        setMessageDialogTitle("Create");
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `All Data has been Validated.General Ledger can be Send for Review`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
        setValidateFlag(true);
        // setBlurLoading(false);

        // Now, make the duplicate check API call
        // Ensure that the conditions for making the duplicate check API call are met
        if (
          duplicateCheckPayload.glName !== "" ||
          duplicateCheckPayload.compCode !== ""
        ) {
          // payloadmapping.Toitem = duplicateCheckPayload.name;
          doAjax(
            `/${destination_GeneralLedger}/alter/fetchGlNameNCompCodeDupliChkMass`,
            "post",
            hDuplicateCheckSuccess,
            hDuplicateCheckError,
            duplicateCheckPayload
          );
        }
      }
      // setBlurLoading(false);
    };

    const hDuplicateCheckSuccess = (data) => {
      console.log("dataaaa", data);
      // Handle success of duplicate check
      if (
        data.body.length === 0 ||
        !data.body.some((item) =>
          duplicateCheckPayload.some(
            (payloadItem) => payloadItem?.glName?.toUpperCase() === item.matches[0]
          )
        )
      ) {
        // No direct match, enable the "Submit for Review" button
        setBlurLoading(false);
        setSubmitForReviewDisabled(false);
        setTestRun(true);
      } else {
        // Handle direct match
        const directMatches = data.body.map(item => item.matches[0]);
        setBlurLoading(false);
        setMessageDialogTitle("Duplicate Check");
        setsuccessMsg(false);
        setMessageDialogMessage(
          `There is a direct match for the General Ledger name.`
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setSubmitForReviewDisabled(true);
        setDirectMatchedProfitCenters(directMatches);
      }
    };

    const hDuplicateCheckError = (error) => {
      // Handle error of duplicate check
      console.log(error);
    };

    const hError = (error) => {
      console.log(error);
    };

    // Call the main API for validation
    doAjax(
      `/${destination_GeneralLedger}/massAction/validateMassGeneralLedger`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const validationColumns = [
    {
      field: "generalLedger",
      headerName: "General Ledger",
      editable: false,
      flex: 1,
      // width: 100,
    },
    {
      field: "error",
      headerName: "Error",
      editable: false,
      flex: 1,
      // width: 400,
    },
  ];
  const validationRows = profitValidationError
  ?.filter((row) => row?.code === 400)
  ?.map((item, index) => {
    if (item.code === 400) {
      return {
        id: index,
        generalLedger: item?.generalLedger,
        error: item?.status?.message,
      };
    }
  });
  console.log("validationRows",validationRows)
  const handleRemarksDialogClose = () => {
    setTestRun(true);
    setOpenCorrectionDialog(false);
  };

  const handleOpenCorrectionDialog = () => {
    setTestRun(false);
    setOpenCorrectionDialog(true);
  };

  const handleRemarks = (e, value) => {
    //setRemarks(e.target.value);
    const newValue = e.target.value;
    if (newValue.length > 0 && newValue[0] === " ") {
      setRemarks(newValue.trimStart());
    } else {
      //let costCenterValue = e.target.value;
      let remarksUpperCase = newValue.toUpperCase();
      setRemarks(remarksUpperCase);
    }
  };
  const handleDialogClose = () => {
    setDialogOpen(false);
  };

  const onCostCenterSubmitRemarks = () => {
    setBlurLoading(true);
    handleRemarksDialogClose();
    if (massHandleType === "Create") {
      handleSubmitForReview();
    } else {
      handleSubmitForReviewChange();
    }
  };

  return (
    <>
      {isLoading === true ? (
        <LoadingComponent />
      ) : (
        <div>
          <Dialog
            hideBackdrop={false}
            elevation={2}
            PaperProps={{
              sx: { boxShadow: "none" },
            }}
            open={openCorrectionDialog}
            onClose={handleRemarksDialogClose}
          >
            {/* <Grid
          container
          sx={{ display: "flex", justifyContent: "space-between" }}
        > */}
            {/* <Grid item> */}
            <DialogTitle
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF40",
                // borderBottom: "1px solid grey",
                display: "flex",
              }}
            >
              <Typography variant="h6">Remarks</Typography>

              <IconButton
                sx={{ width: "max-content" }}
                onClick={handleRemarksDialogClose}
                children={<CloseIcon />}
              />
            </DialogTitle>
            {/* </Grid> */}
            {/* </Grid> */}
            <DialogContent sx={{ padding: ".5rem 1rem" }}>
              <Stack>
                <Box sx={{ minWidth: 400 }}>
                  <FormControl sx={{ height: "auto" }} fullWidth>
                    <TextField
                      sx={{ backgroundColor: "#F5F5F5" }}
                      value={remarks}
                      onChange={handleRemarks}
                      multiline
                      placeholder={"Enter Remarks"}
                      inputProps={{maxLength: 254}}
                    ></TextField>
                  </FormControl>
                </Box>
              </Stack>
              {/* <TextField
            autoFocus
            margin="dense"
            id="name"
            label="Enter Remarks for Correction"
            type="text"
            fullWidth
            variant="standard"
            onChange={handleRemarks}
          /> */}
            </DialogContent>
            <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
              <Button
                sx={{ width: "max-content", textTransform: "capitalize" }}
                onClick={handleRemarksDialogClose}
              >
                Cancel
              </Button>
              <Button
                className="button_primary--normal"
                type="save"
                onClick={onCostCenterSubmitRemarks}
                variant="contained"
              >
                Submit
              </Button>
            </DialogActions>
          </Dialog>
          <ReusableDialog
            dialogState={openMessageDialog}
            openReusableDialog={handleMessageDialogClickOpen}
            closeReusableDialog={handleMessageDialogClose}
            dialogTitle={messageDialogTitle}
            dialogMessage={messageDialogMessage}
            handleDialogConfirm={handleMessageDialogClose}
            dialogOkText={"OK"}
            handleExtraButton={handleMessageDialogNavigate}
            dialogSeverity={messageDialogSeverity}
          />

          {successMsg && (
            <ReusableSnackBar
              openSnackBar={openSnackbar}
              alertMsg={messageDialogMessage}
              handleSnackBarClose={handleSnackBarClose}
            />
          )}

<div style={{ ...outermostContainer, backgroundColor: "#FAFCFF" }}>
            <Grid container sx={outermostContainer_Information}>
              <Grid item md={12} sx={{ display: "flex", marginBottom: "0" }}>
                <Grid item md={11} sx={{ display: "flex" }}>
                  <Grid>
                    <IconButton
                      // onClick={handleBacktoRO}
                      color="primary"
                      aria-label="upload picture"
                      component="label"
                      sx={iconButton_SpacingSmall}
                    >
                      <ArrowCircleLeftOutlinedIcon
                        style={{
                          height: "1em",
                          width: "1em",
                          color: "#000000",
                        }}
                        // sx={{
                        //   fontSize: "1.5em",
                        //   color: "#000000",
                        // }}
                        onClick={() => {
                          navigate("/masterDataCockpit/generalLedger");
                        }}
                      />
                    </IconButton>
                  </Grid>
                  <Grid>
                    {massHandleType === "Create" ? (
                      <Grid item md={12}>
                        <Typography variant="h3">
                          <strong>Create Multiple General Ledger</strong>
                        </Typography>
                        <Typography variant="body2" color="#777">
                          This view creates multiple General Ledger
                        </Typography>
                      </Grid>
                    ) : (
                      <Grid item md={12}>
                        <Typography variant="h3">
                          <strong>Change Multiple General Ledger</strong>
                        </Typography>
                        <Typography variant="body2" color="#777">
                          This view changes multiple General Ledger
                        </Typography>
                      </Grid>
                    )}
                  </Grid>
                </Grid>

                <Grid item md={1} sx={{ display: "flex" }}>
                  <Tooltip title="Upload documents if any" arrow>
                    <IconButton onClick={handleOpenDialog}>
                      <AttachFileOutlinedIcon />
                    </IconButton>
                  </Tooltip>
                </Grid>
                <Dialog
                  hideBackdrop={false}
                  elevation={2}
                  PaperProps={{
                    sx: { boxShadow: "none" },
                  }}
                  open={openDialog}
                  onClose={handleCloseDialog}
                >
                  <DialogTitle
                    sx={{
                      justifyContent: "space-between",
                      alignItems: "center",
                      height: "max-content",
                      padding: ".5rem",
                      paddingLeft: "1rem",
                      backgroundColor: "#EAE9FF40",
                      // borderBottom: "1px solid grey",
                      display: "flex",
                    }}
                  >
                    <Typography variant="h6">Add Attachment</Typography>
                  </DialogTitle>
                  <DialogContent sx={{ padding: ".5rem 1rem" }}>
                    <Stack>
                      <Box sx={{ minWidth: 400 }}>
                        <ReusableAttachementAndComments
                          title="GeneralLedger"
                          useMetaData={false}
                          artifactId={glNumber}
                          artifactName="GeneralLedger"
                        />
                      </Box>
                    </Stack>
                  </DialogContent>
                  <DialogActions>
                    <Button onClick={handleCloseDialog}>Close</Button>
                  </DialogActions>
                </Dialog>
              </Grid>
            </Grid>
            <Grid item sx={{ position: "relative" }}>
              <Stack>
                <ReusableTable
                  isLoading={isLoading}
                  width="100%"
                  title={
                    "General Ledger Master List (" + initialRows.length + ")"
                  }
                  rows={initialRows}
                  columns={columns}
                  pageSize={10}
                  getRowIdValue={"id"}
                  hideFooter={false}
                  checkboxSelection={true}
                  disableSelectionOnClick={true}
                  status_onRowSingleClick={true}
                  onRowsSelectionHandler={handleSelectionModelChange}
                  callback_onRowSingleClick={(params) => {
                    console.log("paramss", params);
                    // Adjust this based on your data structure
                    const glAccount = params.row.glAccount;
                    //condition to send a row data
                    const dataToSend = multipleProfitData.find(
                      (item) => item.GLAccount === glAccount
                    );
                    console.log(dataToSend, "pppp");

                    navigate(
                      `/masterDataCockpit/generalLedger/createMultipleGL/editMultipleGL/${glAccount}`,
                      {
                        state: {
                          rowViewData: dataToSend,
                          selectedRow: params.row,
                        },
                      }
                    );
                  }}
                  // setShowWork={setShowWork}
                  stopPropagation_Column={"action"}
                  status_onRowDoubleClick={true}
                />
              </Stack>
            </Grid>
          </div>
          <Paper
            sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
            elevation={2}
          >
            <BottomNavigation
              className="container_BottomNav"
              showLabels
              sx={{
                display: "flex",

                justifyContent: "flex-end",
              }}
              value={value}
              // onChange={(newValue) => {
              // setValue(newValue);
              // }}
            >
              {/* <Button
                variant="contained"
                size="small"
                sx={{ ...button_Primary, mr: 1 }}
                // onClick={onSaveButtonClick}
                
              >
                Save As Draft
              </Button> */}

              {/* <Button
                variant="contained"
                size="small"
                sx={{ ...button_Primary }}
                // onClick={handleSubmitForReview}
              >
                Submit for Review
              </Button> */}

              <Button
                variant="contained"
                size="small"
                sx={{ ...button_Primary, mr: 1 }}
                onClick={onValidateGeneralLedger}
                disabled={!testRun}
              >
                Validate
              </Button>
              {massHandleType === "Create" ? (
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary }}
                  // disabled={testRun}
                  onClick={handleOpenCorrectionDialog}
                  disabled={submitForReviewDisabled}
                >
                  Submit for Review
                </Button>
              ) : massHandleType === "Change" ? (
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary }}
                  onClick={handleOpenCorrectionDialog}
                  disabled={submitForReviewDisabled}
                >
                  Submit for Review
                </Button>
              ) : (
                ""
              )}
            </BottomNavigation>
          </Paper>
          <Dialog
            open={dialogOpen}
            fullWidth
            onClose={handleDialogClose}
            sx={{
              "&::webkit-scrollbar": {
                width: "1px",
              },
              // paddingBottom:1
            }}
          >
            <DialogTitle
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF40",
                // borderBottom: "1px solid grey",
                display: "flex",
              }}
            >
              <Typography variant="h6" color="red">
                Errors
              </Typography>

              <IconButton
                sx={{ width: "max-content" }}
                onClick={handleDialogClose}
                children={<CloseIcon />}
              />
            </DialogTitle>
            <DialogContent sx={{ padding: ".5rem 1rem" }}>
              {/* <Grid container> */}

              <ReusableTable
                isLoading={isLoading}
                width="100%"
                title={"General Ledger Master List (" + initialRows.length + ")"}
                rows={validationRows}
                columns={validationColumns}
                pageSize={10}
                getRowIdValue={"id"}
                hideFooter={true}
                checkboxSelection={false}
                disableSelectionOnClick={true}
                status_onRowSingleClick={true}
                stopPropagation_Column={"action"}
                status_onRowDoubleClick={true}
              />

              {/* </Grid> */}
            </DialogContent>

            <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
              {/* <Button
            sx={{ width: "max-content", textTransform: "capitalize" }}
            onClick={handleDialogClose}
          >
            Cancel
          </Button>
          <Button
            className="button_primary--normal"
            type="save"
            onClick={handleDialogProceed}
            variant="contained"
          >
            Proceed
          </Button> */}
            </DialogActions>
          </Dialog>
          <Backdrop
            sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
            open={blurLoading}
            // onClick={handleClose}
          >
            <CircularProgress color="inherit" />
          </Backdrop>
        </div>
      )}
    </>
  );
};
export default CreateMultipleGL;