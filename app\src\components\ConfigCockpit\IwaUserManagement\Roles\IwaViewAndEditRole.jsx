import { MFViewandEdit } from '@cw/mfviewandedit';
import { SimpleViewAndEdit } from '@cw/viewandeditrole';
import { Stack } from '@mui/material';
import { useLocation, useNavigate } from 'react-router-dom';
import {APP_END_POINTS} from "@constant/appEndPoints";

const IwaViewAndEditRole = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { roleId, roleVersionNo, action, roleSegment, status } = location.state;
  const isEditMode = action === 'editRole' ? true : false;
  const simpleViewAndEditProps = {
    isEditMode: isEditMode,
    roleVersionNo,
    roleId,
    status,
  };

  const onSimpleViewAndEditClick = action => {
    if (action === 'roleSummary') {
      navigate(APP_END_POINTS.IWA_USER_MANAGEMENT.ROLES_SUMMARY);
    }
    if (action === 'editRole') {
      navigate(APP_END_POINTS.IWA_USER_MANAGEMENT.VIEW_AND_EDIT_ROLE, {
        state: { roleId, roleVersionNo, action, roleSegment, status },
      });
    }
  };

  const MFViewandEditProps = {
    isEdit: isEditMode,
    roleId: roleId,
    roleVersionNo: roleVersionNo,
    status,
  };

  const onMFViewandEditActionClick = action => {
    if (action === 'roleSummary') {
      navigate(APP_END_POINTS.IWA_USER_MANAGEMENT.ROLES_SUMMARY);
    }
    if (action === 'editRole') {
      navigate(APP_END_POINTS.IWA_USER_MANAGEMENT.VIEW_AND_EDIT_ROLE, {
        state: { roleId, roleVersionNo, action, roleSegment, status },
      });
    }
  };

  return (
    <Stack>
      {roleSegment?.includes('Simple') ? (
        <SimpleViewAndEdit
          simpleViewAndEditProps={simpleViewAndEditProps}
          onSimpleViewAndEditClick={onSimpleViewAndEditClick}
        />
      ) : (
        <MFViewandEdit
          MFViewandEditProps={MFViewandEditProps}
          onMFViewandEditActionClick={onMFViewandEditActionClick}
        />
      )}
    </Stack>
  );
};

export default IwaViewAndEditRole;
