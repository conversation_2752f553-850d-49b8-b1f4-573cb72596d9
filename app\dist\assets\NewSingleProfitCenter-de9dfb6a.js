import{s as lt,q as b,r as o,cy as $,cx as Jt,a as t,j as l,T as C,E as p,x as j,G as u,b6 as Gt,ay as Kt,at as Xt,K as B,c0 as H,ai as dt,b4 as ee,B as V,F as _,u as Yt,b as Qt,cn as Zt,cz as yt,bh as Dt,bX as pt,C as en,z as De,V as pe,aF as et,I as le,aG as tt,W as nt,ar as it,X as rt,t as R,cb as tn,cc as nn,al as ot,b1 as rn,cA as on,aD as sn,aE as cn,aB as X,bp as ln,bq as st}from"./index-17b8d91e.js";import{d as dn}from"./ArrowCircleLeftOutlined-2a09f8e2.js";import{d as un}from"./dayjs.min-ce01f2c7.js";import{A as an}from"./AdapterDayjs-1a4a6504.js";import{D as mn}from"./DatePicker-68227989.js";import{C as fn}from"./CompCodeProfitCenter-816b27ce.js";import"./AutoCompleteType-9336ea79.js";import{R as hn}from"./ReusableAttachementAndComments-bab6bbfc.js";import{l as ct}from"./lookup-1dcf10ac.js";import{S as gn,a as Cn,b as xn}from"./Stepper-88e4fb0c.js";import"./isBetween-3aeee754.js";import"./dateViewRenderers-34586552.js";import"./useSlotProps-e34e1e13.js";import"./InputAdornment-5b0053c5.js";import"./CSSTransition-30917e2c.js";import"./useMediaQuery-6a073ac5.js";import"./DesktopDatePicker-07c19cde.js";import"./useMobilePicker-9978caff.js";import"./useChangeLogUpdate-f322f7d1.js";import"./CloudUpload-27b6d63e.js";import"./utilityImages-067c3dc2.js";import"./Add-98854918.js";import"./Delete-9f4d7a45.js";function te(n){var F,e,q,U;const i=lt();let x=b(s=>s.profitCenter.errorFields);var d=n.field.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").replaceAll("%","").split(" ").join("");const f=b(s=>s.profitCenter.singlePCPayload),a=un();o.useEffect(()=>{var s;((s=n.field)==null?void 0:s.fieldType)==="Calendar"&&i($({keyName:d,data:a}))},[]);const v=s=>{const m=P=>{console.log("value",P),i(dt({keyName:"Region",data:P.body}))},T=P=>{console.log(P,"error in dojax")};B(`/${H}/data/getRegionBasedOnCountry?country=${s==null?void 0:s.code}`,"get",m,T)};o.useEffect(()=>{var s,m;(((s=n==null?void 0:n.field)==null?void 0:s.visibility)==="0"||((m=n==null?void 0:n.field)==null?void 0:m.visibility)==="Required")&&i(Jt(d))},[]),console.log("props",n);const S=b(s=>s.AllDropDown.dropDown);if(((F=n.field)==null?void 0:F.fieldType)==="Input")return t(u,{item:!0,md:2,children:n.field.visibility==="Hidden"?null:l(j,{children:[l(C,{variant:"body2",color:"#777",children:[n.field.fieldName,n.field.visibility==="Required"||n.field.visibility==="0"?t("span",{style:{color:"red"},children:"*"}):""]}),t(p,{size:"small",type:n.field.dataType==="QUAN"?"number":"",placeholder:`Enter ${n.field.fieldName}`,inputProps:{maxLength:n.field.maxLength},value:f[d],onChange:(s,m)=>{const T=s.target.value;Object.keys(f).length>0?(console.log("0"),T.length>0&&T[0]===" "?(console.log("1"),i($({keyName:d,data:T.trimStart()}))):(console.log("2"),i($({keyName:d,data:T.toUpperCase()})))):(console.log("3"),i($({keyName:d,data:T.trimStart()})))},required:n.field.visibility==="Required"||n.field.visibility==="0",error:x.includes(d)})]})});if(((e=n.field)==null?void 0:e.fieldType)==="Drop Down")return t(u,{item:!0,md:2,children:n.field.visibility==="Hidden"?null:l(j,{children:[l(C,{variant:"body2",color:"#777",children:[n.field.fieldName,n.field.visibility==="Required"||n.field.visibility==="0"?t("span",{style:{color:"red"},children:"*"}):""]}),t(Gt,{sx:{height:"31px"},fullWidth:!0,size:"small",value:f[d],onChange:(s,m)=>{n.field.fieldName==="Country/Reg."&&v(m),i($({keyName:d,data:m}))},options:S[d]??[],required:n.field.visibility==="0"||n.field.visibility==="Required",getOptionLabel:s=>`${s==null?void 0:s.code} - ${s==null?void 0:s.desc}`,renderOption:(s,m)=>t("li",{...s,children:l(C,{style:{fontSize:12},children:[m==null?void 0:m.code," - ",m==null?void 0:m.desc]})}),renderInput:s=>t(p,{...s,variant:"outlined",placeholder:`Select ${n.field.fieldName}`,error:x.includes(d)})})]})});if(((q=n.field)==null?void 0:q.fieldType)==="Radio Button")return l(u,{item:!0,md:2,children:[l(C,{variant:"body2",color:"#777",children:[n.field.fieldName,n.field.visibility==="Required"||n.field.visibility==="0"?t("span",{style:{color:"red"},children:"*"}):""]}),t(Kt,{sx:{padding:0},error:x.includes(d),checked:f[d]==!0,onChange:s=>{i($({keyName:d,data:s.target.checked}))}})]});if(((U=n.field)==null?void 0:U.fieldType)==="Calendar")return t(u,{item:!0,md:2,children:l(j,{children:[l(C,{variant:"body2",color:"#777",children:[n.field.fieldName,n.field.visibility==="Required"||n.field.visibility==="0"?t("span",{style:{color:"red"},children:"*"}):""]}),t(Xt,{dateAdapter:an,children:t(mn,{slotProps:{textField:{size:"small"}},value:f[d],defaultValue:a,onChange:s=>i($({keyName:d,data:s})),onError:x.includes(d),required:n.field.visibility==="0"||n.field.visibility==="Required"})})]})})}const bn=n=>{let i=Object==null?void 0:Object.entries(n==null?void 0:n.basicDataTabDetails);console.log("basic",i);const[x,d]=o.useState([]);return o.useEffect(()=>{d(i==null?void 0:i.map(f=>{var a,v;return l(u,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ee},children:[t(u,{container:!0,children:t(C,{sx:{fontSize:"12px",fontWeight:"700"},children:f[0]})}),t(V,{children:t(u,{container:!0,spacing:1,children:(v=(a=[...f[1]].filter(S=>(S==null?void 0:S.visibility)!="Hidden"))==null?void 0:a.sort((S,F)=>(S==null?void 0:S.sequenceNo)-(F==null?void 0:F.sequenceNo)))==null?void 0:v.map(S=>t(te,{field:S,dropDownData:n==null?void 0:n.dropDownData}))})})]})}))},[n==null?void 0:n.basicDataTabDetails]),t(_,{children:x})},Sn=n=>{let i=Object==null?void 0:Object.entries(n==null?void 0:n.indicatorsTabDetails);console.log("PROFITCENTER",i);const[x,d]=o.useState([]);return o.useEffect(()=>{d(i==null?void 0:i.map(f=>l(u,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ee},children:[t(u,{container:!0,children:t(C,{sx:{fontSize:"12px",fontWeight:"700"},children:f[0]})}),t(V,{children:t(u,{container:!0,spacing:1,children:[...f[1]].filter(a=>a.visibility!="Hidden").sort((a,v)=>a.sequenceNo-v.sequenceNo).map(a=>t(te,{field:a,dropDownData:n.dropDownData}))})})]})))},[n==null?void 0:n.indicatorsTabDetails]),t(_,{children:x})},Pn=n=>{let i=Object==null?void 0:Object.entries(n==null?void 0:n.addressTabDetails);console.log("basic",i);const[x,d]=o.useState([]);return o.useEffect(()=>{d(i==null?void 0:i.map(f=>l(u,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ee},children:[t(u,{container:!0,children:t(C,{sx:{fontSize:"12px",fontWeight:"700"},children:f[0]})}),t(V,{children:t(u,{container:!0,spacing:1,children:[...f[1]].filter(a=>a.visibility!="Hidden").sort((a,v)=>a.sequenceNo-v.sequenceNo).map(a=>t(te,{field:a,dropDownData:n.dropDownData}))})})]})))},[n==null?void 0:n.addressTabDetails]),t(_,{children:x})},vn=n=>{let i=Object==null?void 0:Object.entries(n==null?void 0:n.communicationTabDetails);console.log("basic",i);const[x,d]=o.useState([]);return o.useEffect(()=>{d(i==null?void 0:i.map(f=>l(u,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ee},children:[t(u,{container:!0,children:t(C,{sx:{fontSize:"12px",fontWeight:"700"},children:f[0]})}),t(V,{children:t(u,{container:!0,spacing:1,children:[...f[1]].filter(a=>a.visibility!="Hidden").sort((a,v)=>a.sequenceNo-v.sequenceNo).map(a=>t(te,{field:a,dropDownData:n.dropDownData}))})})]})))},[n==null?void 0:n.communicationTabDetails]),t(_,{children:x})},Yn=()=>{var Te,ke,we,Ne,Ae,Ee,Re,Fe,Ie,Be,qe,Oe,Me,Le,$e,je,Ve,ze,We,He,_e,Ue,Je,Ge,Ke,Xe,Ye,Qe;o.useState(0);const i=Yt().state;console.log("displaydata",i);const x=Qt(),d=lt(),f=b(r=>r.profitCenter.profitCenterBasicData),a=b(r=>r.profitCenter.profitCenterCompCodes),v=b(r=>r.profitCenter.profitCenterIndicators),S=b(r=>r.profitCenter.profitCenterAddress),F=b(r=>r.profitCenter.profitCenterCommunication);b(r=>r.profitCenter.profitCenterHistory);const e=b(r=>r.profitCenter.singlePCPayload);console.log("payloadData",e);const[q,U]=o.useState(0),[s,m]=o.useState(!1),[T,P]=o.useState(""),[ut,w]=o.useState(!1),[Tn,O]=o.useState(!0),[at,M]=o.useState(!1),[kn,L]=o.useState(!1),[mt,ne]=o.useState(!1),[de,z]=o.useState(!0),[ft,Y]=o.useState(!1),[ht,J]=o.useState(!1),[Q,gt]=o.useState([]),[Ct,ue]=o.useState(!1),[xt,ae]=o.useState(!1),[bt,ie]=o.useState(!0),[re,St]=o.useState(""),[Pt,vt]=o.useState(!1),[G,me]=o.useState(""),[Tt,kt]=o.useState(!1),[wt,fe]=o.useState(!0),[Nt,he]=o.useState(!1),[At,Et]=o.useState("");let N=b(r=>r.userManagement.userData);const K=b(r=>r.AllDropDown.dropDown),ge=["BASIC DATA","COMPANY CODE","INDICATOR","ADDRESS","COMMUNICATION","ATTACHMENTS & COMMENTS"];console.log("isLoading",de);const oe=()=>{ne(!0)},Rt=()=>{xt?(ne(!1),ae(!1)):(ne(!1),x("/masterDataCockpit/profitCenter"))},W=()=>{J(!0)},Ft=()=>{Se()?U(h=>h+1):Pe()},It=()=>{ie(!0),Se()?U(h=>h-1):Pe()},se=()=>{vt(!1)},Ce=(r,h)=>{const c=r.target.value;if(c.length>0&&c[0]===" ")me(c.trimStart());else{let A=c.toUpperCase();me(A)}},xe=()=>{y(),jt()},Bt=b(r=>r.profitCenter.singlePCPayload),qt=b(r=>r.profitCenter.requiredFields),Z=b(r=>r.AllDropDown.dropDown.CompCodeBasedOnControllingArea);console.log("rows",Z);var ce={ProfitCenterID:"",RequestID:"",TaskId:"",Action:"I",TaskStatus:"",ReqCreatedBy:N==null?void 0:N.user_id,ReqCreatedOn:"",RequestStatus:"",Remarks:G||"",CreationId:"",EditId:"",DeleteId:"",MassCreationId:"",MassEditId:"",MassDeleteId:"",RequestType:"Create",MassRequestStatus:"",PrctrName:e!=null&&e.Name?e==null?void 0:e.Name:"",LongText:e!=null&&e.LongText?e==null?void 0:e.LongText:"",InChargeUser:(Te=e==null?void 0:e.UserResponsible)!=null&&Te.code?(ke=e==null?void 0:e.UserResponsible)==null?void 0:ke.code:"",InCharge:e!=null&&e.PersonResponsible?e==null?void 0:e.PersonResponsible:"",Department:e!=null&&e.Department?e==null?void 0:e.Department:"",PrctrHierGrp:(we=e==null?void 0:e.ProfitCtrGroup)!=null&&we.code?(Ne=e==null?void 0:e.ProfitCtrGroup)==null?void 0:Ne.code:"",Segment:(Ae=e==null?void 0:e.Segment)!=null&&Ae.code?(Ee=e==null?void 0:e.Segment)==null?void 0:Ee.code:"",LockInd:(e==null?void 0:e.Lockindicator)===!0?"X":"",Template:(Re=e==null?void 0:e.FormPlanningTemp)!=null&&Re.code?(Fe=e==null?void 0:e.FormPlanningTemp)==null?void 0:Fe.code:"",Title:e!=null&&e.Title?e==null?void 0:e.Title:"",Name1:e!=null&&e.Name1?e==null?void 0:e.Name1:"",Name2:e!=null&&e.Name2?e==null?void 0:e.Name2:"",Name3:e!=null&&e.Name3?e==null?void 0:e.Name3:"",Name4:e!=null&&e.Name4?e==null?void 0:e.Name4:"",Street:e!=null&&e.Street?e==null?void 0:e.Street:"",City:e!=null&&e.City?e==null?void 0:e.City:"",District:e!=null&&e.District?e==null?void 0:e.District:"",Country:(Ie=e==null?void 0:e.CountryReg)!=null&&Ie.code?(Be=e==null?void 0:e.CountryReg)==null?void 0:Be.code:"",Taxjurcode:(qe=e==null?void 0:e.TaxJur)!=null&&qe.code?(Oe=e==null?void 0:e.TaxJur)==null?void 0:Oe.code:"",PoBox:e!=null&&e.POBox?e==null?void 0:e.POBox:"",PostlCode:e!=null&&e.PostalCode?e==null?void 0:e.PostalCode:"",PobxPcd:e!=null&&e.POBoxPCode?e==null?void 0:e.POBoxPCode:"",Region:(Me=e==null?void 0:e.Region)!=null&&Me.code?(Le=e==null?void 0:e.Region)==null?void 0:Le.code:"",Langu:($e=e==null?void 0:e.Language)!=null&&$e.code?(je=e==null?void 0:e.Language)==null?void 0:je.code:"EN",Telephone:e!=null&&e.Telephone1?e==null?void 0:e.Telephone1:"",Telephone2:e!=null&&e.Telephone2?e==null?void 0:e.Telephone2:"",Telebox:e!=null&&e.Telebox?e==null?void 0:e.Telebox:"",Telex:e!=null&&e.Telex?e==null?void 0:e.Telex:"",FaxNumber:e!=null&&e.FaxNumber?e==null?void 0:e.FaxNumber:"",Teletex:e!=null&&e.Teletex?e==null?void 0:e.Teletex:"",Printer:e!=null&&e.Printername?e==null?void 0:e.Printername:"",DataLine:e!=null&&e.Dataline?e==null?void 0:e.Dataline:"",ProfitCenter:`P${(Ve=i==null?void 0:i.companyCode)==null?void 0:Ve.newCompanyCode.code}${(ze=i==null?void 0:i.profitCenterName)==null?void 0:ze.newProfitCenterName}`?`P${(We=i==null?void 0:i.companyCode)==null?void 0:We.newCompanyCode.code}${(He=i==null?void 0:i.profitCenterName)==null?void 0:He.newProfitCenterName}`:"",ControllingArea:(Ue=(_e=i==null?void 0:i.controllingArea)==null?void 0:_e.newControllingArea)!=null&&Ue.code?(Ge=(Je=i==null?void 0:i.controllingArea)==null?void 0:Je.newControllingArea)==null?void 0:Ge.code:"",ValidfromDate:e!=null&&e.AnalysisPeriodFrom?"/Date("+Date.parse(e==null?void 0:e.AnalysisPeriodFrom)+")/":"",ValidtoDate:e!=null&&e.AnalysisPeriodTo?"/Date("+Date.parse(e==null?void 0:e.AnalysisPeriodTo)+")/":"",Testrun:wt,Countryiso:"",LanguIso:"",Logsystem:"",ToCompanycode:Z==null?void 0:Z.map(r=>({CompCodeID:"",CompanyName:r==null?void 0:r.companyName,AssignToPrctr:r.assigned,CompCode:r.companyCodes}))};const[be,Ot]=o.useState(0),Mt=(r,h)=>{const c=E=>{d(dt({keyName:r,data:E.body})),Ot(I=>I+1)},A=E=>{console.log(E)};B(`/${H}/data/${h}`,"get",c,A)},Lt=()=>{var r,h;(h=(r=ct)==null?void 0:r.profitCenter)==null||h.map(c=>{Mt(c==null?void 0:c.keyName,c==null?void 0:c.endPoint)})},$t=()=>{var r,h;be==((h=(r=ct)==null?void 0:r.profitCenter)==null?void 0:h.length)?z(!1):z(!0)};o.useEffect(()=>{$t()},[be]),o.useEffect(()=>{Lt()},[]),o.useEffect(()=>{d(Zt())},[]),o.useEffect(()=>{d(yt(Q))},[Q]),o.useEffect(()=>{St(Dt("PC"))},[]);const Se=()=>ln(Bt,qt,gt),Pe=()=>{ue(!0)},jt=()=>{z(!0);const r=c=>{if(z(!1),c.statusCode===200){m("Create"),P(`Profit Center has been Submitted for review NPS${c.body}`),w("success"),O(!1),M(!0),oe(),L(!0);const A={artifactId:re,createdBy:N==null?void 0:N.emailId,artifactType:"ProfitCenter",requestId:`NPS${c==null?void 0:c.body}`},E=k=>{console.log("Second API success",k)},I=k=>{console.error("Second API error",k)};B(`/${st}/documentManagement/updateDocRequestId`,"post",E,I,A)}else m("Create"),M(!1),P("Creation Failed"),w("danger"),O(!1),L(!0),W();handleClose()},h=c=>{console.log(c)};B(`/${H}/alter/profitCenterSubmitForReview`,"post",r,h,ce)},Vt=()=>{w(!1),w(!1),W(),m("Confirm"),P("Do You Want to Save as Draft ?"),kt(!0),Et("proceed")},zt=()=>{z(!0);const r=c=>{if(z(!1),c.statusCode===200){console.log("success"),J(!1),m("Create"),P(`Profit Center has been saved with creation ID NPS${c.body}`),w("success"),O(!1),M(!0),oe(),L(!0);const A={artifactId:re,createdBy:N==null?void 0:N.emailId,artifactType:"ProfitCenter",requestId:`NPS${c==null?void 0:c.body}`},E=k=>{console.log("Second API success",k)},I=k=>{console.error("Second API error",k)};B(`/${st}/documentManagement/updateDocRequestId`,"post",E,I,A)}else J(!1),m("Save"),M(!1),P("Failed Saving the Data "),w("danger"),O(!1),L(!0),W();handleClose()},h=c=>{console.log(c)};B(`/${H}/alter/profitCenterAsDraft`,"post",r,h,ce)},Wt=()=>{fe(!1),he(!0)},y=()=>{fe(!0),he(!1)},Ht=()=>{var I,k;Y(!0);const r={coArea:((k=(I=i==null?void 0:i.controllingArea)==null?void 0:I.newControllingArea)==null?void 0:k.code)||"",name:e!=null&&e.Name?e==null?void 0:e.Name.toUpperCase():""},h=g=>{var D,Ze,ye;g.statusCode===201?(m("Create"),m("Create"),P("All Data has been Validated. Profit Center can be Sent for Review"),w("success"),O(!1),M(!0),oe(),L(!0),ae(!0),(r.coArea!==""||r.name!=="")&&B(`/${H}/alter/fetchPCDescriptionDupliChk`,"post",c,A,r)):(Y(!1),m("Error"),M(!1),P(`${(D=g==null?void 0:g.body)!=null&&D.message[0]?(Ze=g==null?void 0:g.body)==null?void 0:Ze.message[0]:(ye=g==null?void 0:g.body)==null?void 0:ye.value}`),w("danger"),O(!1),L(!0),W())},c=g=>{g.body.length===0||!g.body.some(D=>D.toUpperCase()===r.name)?(ie(!1),Y(!1)):(Y(!1),m("Duplicate Check"),M(!1),P("There is a direct match for the Profit Center name. Please change the name."),w("danger"),O(!1),L(!0),W(),ie(!0))},A=g=>{console.log(g)},E=g=>{console.log(g)};B(`/${H}/alter/validateSingleProfitCenter`,"post",h,E,ce)},_t=r=>{switch(r){case 0:return t(bn,{basicDataTabDetails:f,dropDownData:K});case 1:return t(fn,{compCodesTabDetails:a,dropDownData:K});case 2:return t(Sn,{indicatorsTabDetails:v,dropDownData:K});case 3:return t(Pn,{addressTabDetails:S,dropDownData:K});case 4:return t(vn,{communicationTabDetails:F,dropDownData:K});case 5:return t(hn,{title:"ProfitCenter",useMetaData:!1,artifactId:re,artifactName:"ProfitCenter"});default:return"Unknown step"}},Ut=()=>{ue(!1)},ve=()=>{J(!1)};return t(_,{children:de===!0?t(pt,{}):l("div",{children:[t(en,{dialogState:ht,openReusableDialog:W,closeReusableDialog:ve,dialogTitle:s,dialogMessage:T,handleDialogConfirm:ve,dialogOkText:"OK",showExtraButton:Tt,dialogSeverity:ut,showCancelButton:!0,handleDialogReject:()=>{J(!1)},handleExtraText:At,handleExtraButton:zt}),Q.length!=0&&t(De,{openSnackBar:Ct,alertMsg:"Please fill the following Field: "+Q.join(", "),handleSnackBarClose:Ut}),at&&t(De,{openSnackBar:mt,alertMsg:T,handleSnackBarClose:Rt}),l(pe,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:Pt,onClose:se,children:[l(et,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(C,{variant:"h6",children:"Remarks"}),t(le,{sx:{width:"max-content"},onClick:se,children:t(tt,{})})]}),t(nt,{sx:{padding:".5rem 1rem"},children:t(j,{children:t(V,{sx:{minWidth:400},children:t(it,{sx:{height:"auto"},fullWidth:!0,children:t(p,{sx:{backgroundColor:"#F5F5F5"},value:G,onChange:Ce,multiline:!0,placeholder:"Enter Remarks",inputProps:{maxLength:254}})})})})}),l(rt,{sx:{display:"flex",justifyContent:"end"},children:[t(R,{sx:{width:"max-content",textTransform:"capitalize"},onClick:se,children:"Cancel"}),t(R,{className:"button_primary--normal",type:"save",onClick:xe,variant:"contained",children:"Submit"})]})]}),l(pe,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:Nt,onClose:y,children:[l(et,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(C,{variant:"h6",children:"Remarks"}),t(le,{sx:{width:"max-content"},onClick:y,children:t(tt,{})})]}),t(nt,{sx:{padding:".5rem 1rem"},children:t(j,{children:t(V,{sx:{minWidth:400},children:t(it,{sx:{height:"auto"},fullWidth:!0,children:t(p,{sx:{backgroundColor:"#F5F5F5"},value:G,onChange:Ce,multiline:!0,placeholder:"Enter Remarks",inputProps:{maxLength:254}})})})})}),l(rt,{sx:{display:"flex",justifyContent:"end"},children:[t(R,{sx:{width:"max-content",textTransform:"capitalize"},onClick:y,children:"Cancel"}),t(R,{className:"button_primary--normal",type:"save",value:G,onClick:xe,variant:"contained",children:"Submit"})]})]}),t(tn,{sx:{color:"#fff",zIndex:r=>r.zIndex.drawer+1},open:ft,children:t(nn,{color:"inherit"})}),t(u,{container:!0,style:{...ot,backgroundColor:"#FAFCFF"},children:l(u,{sx:{width:"inherit"},children:[t(u,{item:!0,md:7,style:{padding:"16px",display:"flex"},children:l(u,{item:!0,md:5,sx:{display:"flex"},children:[t(u,{children:t(le,{color:"primary","aria-label":"upload picture",component:"label",sx:rn,children:t(dn,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{x("/masterDataCockpit/profitCenter"),d(on())}})})}),l(u,{children:[t(C,{variant:"h3",children:t("strong",{children:"Create Profit Center"})}),t(C,{variant:"body2",color:"#777",children:"This view creates a new Profit Center"})]})]})}),t(u,{container:!0,style:{padding:"0 1rem 0 1rem"},children:l(u,{container:!0,sx:ot,children:[t(u,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:l(V,{width:"70%",sx:{marginLeft:"40px",marginBottom:"20px"},children:[t(u,{item:!0,sx:{paddingTop:"2px !important"},children:l(j,{flexDirection:"row",children:[t("div",{style:{width:"10%"},children:t(C,{variant:"body2",color:"#777",children:"Profit Center"})}),l(C,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[": P",(Ke=i==null?void 0:i.companyCode)==null?void 0:Ke.newCompanyCode.code,(Xe=i==null?void 0:i.profitCenterName)==null?void 0:Xe.newProfitCenterName]})]})}),t(u,{item:!0,sx:{paddingTop:"2px !important"},children:l(j,{flexDirection:"row",children:[t("div",{style:{width:"10%"},children:t(C,{variant:"body2",color:"#777",children:"Controlling Area"})}),l(C,{variant:"body2",fontWeight:"bold",children:[":"," ",(Qe=(Ye=i==null?void 0:i.controllingArea)==null?void 0:Ye.newControllingArea)==null?void 0:Qe.code]})]})})]})}),t(u,{container:!0,children:t(gn,{activeStep:q,sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},children:ge.map((r,h)=>t(Cn,{children:t(xn,{sx:{fontWeight:"700"},children:r})},r))})}),t(u,{container:!0,children:_t(q)})]})})]})}),t(sn,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:l(cn,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t(R,{variant:"contained",size:"small",sx:{...X,mr:1},onClick:Vt,children:"Save As Draft"}),t(R,{variant:"contained",size:"small",sx:{...X,mr:1},onClick:It,disabled:q===0,children:"Back"}),q===ge.length-1?l(_,{children:[t(R,{variant:"contained",size:"small",sx:{...X,mr:1},onClick:Ht,children:"Validate"}),t(R,{variant:"contained",size:"small",sx:{...X,mr:1},onClick:Wt,disabled:bt,children:"Submit For Review"})]}):t(R,{variant:"contained",size:"small",sx:{...X,mr:1},onClick:Ft,children:"Next"})]})})]})})};export{Yn as default};
