import React, { useState, useEffect } from "react";
import { <PERSON>ton, Dialog, DialogTitle, DialogContent, DialogActions, Stack, Grid, Box, TextField } from "@mui/material";
import Slide from "@mui/material/Slide";
import IconButton from "@mui/material/IconButton";
import ArrowBackIosIcon from "@mui/icons-material/ArrowBackIos";
import Typography from "@mui/material/Typography";

import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import Card from "@mui/material/Card";
import FiberManualRecordIcon from "@mui/icons-material/FiberManualRecord";
import CardHeader from "@mui/material/CardHeader";
import CardContent from "@mui/material/CardContent";

import "react-draft-wysiwyg/dist/react-draft-wysiwyg.css";

import { doAjax, doCrudApi } from "../utility/serviceRequest";
import Tab from "@mui/material/Tab";
import TabContext from "@mui/lab/TabContext";
import TabList from "@mui/lab/TabList";
import TabPanel from "@mui/lab/TabPanel";
import CardActionArea from "@mui/material/CardActionArea";

import Chip from "@mui/material/Chip";
import Confirmation from "./ConfirmationDialog";
import Snackbar from "@mui/material/Snackbar";
import Alert from "@mui/material/Alert";
import CustomAvatar from "./CustomAvatar";

// const Transition = React.forwardRef(function Transition(
//   props: TransitionProps & {
//     children: React.ReactElement,
//   },
//   ref: React.Ref<unknown>
// ) {
//   return <Slide direction="up" ref={ref} {...props} />;
// });

const ManageGroup = ({ open, onClose,setScenario, ...props }) => {
  // console.log(props?.groupList);
  // console.log(props?.userList);
  const userReducer = useSelector((state) => state.userReducer);
  const [value, setValue] = React.useState("1");
  const [openGroup, setOpenGroup] = React.useState(false);
  const [openUser, setOpenUser] = React.useState(false);
  const [showConfirmation, setShowConfirmation] = React.useState(false);
  const [confirmationMessage, setConfirmationMessage] = React.useState("");
  const [buttonAction, setButtonAction] = React.useState("Cancel");
  const [severity, setSeverity] = useState("success");
  const [emailError, setEmailError] = useState("");

  const [openalert, setOpenalert] = useState(false);
  const [groupData, setGroupData] = useState([]);
  const [getTemplateData, setGetTemplateData] = useState([]);
  const [isLoader, setLoader] = React.useState(false);
  const [selectedRow, setSelectedRow] = useState(null);
  const [userDetails, setUserDetails] = useState(new Map());
  const [groupDetails, setGroupDetails] = useState(new Map());
  const [user, setUser] = useState([]);
  const [group, setGroup] = useState([]);
  const [allGroups, setAllGroups] = useState([]);
  const [associatedTemplate, setAssociatedtemplate] = useState([]);
  const [groupfield, setGroupfield] = useState({
    id: null,
    groupName: "",
    email: "",
    createdBy: userReducer.userData.user_id,
    createdOn: new Date().toISOString(),
    updatedBy: userReducer.userData.user_id,
    updatedOn: new Date().toISOString(),
    // isRowEditable: true,
    // creationType: "new",
  });
  // const groupfield = {
  //   id: null,
  //   groupName: "",
  //   email: "",
  //   createdBy: userReducer.userData.user_id,
  //   createdOn: new Date().toISOString(),
  //   updatedBy: userReducer.userData.user_id,
  //   updatedOn: new Date().toISOString(),
  //   isRowEditable: true,
  //   creationType: "new",
  // };

  // let transFormData = props?.userList.map((ele) => {
  // userDetails.set(ele.emailId, ele.userName);
  // return { code: ele.userName, description: ele.emailId };
  // });
  // setUserDetails(new Map(userDetails));

  // console.log(userDetails);

  const handleToClose = (event, reason) => {
    if ("clickaway" == reason) return;
    setOpenalert(false);
  };

  const openCreateGroup = () => {
    setOpenGroup(true);
  };

  const closeCreateGroup = () => {
    setOpenGroup(false);
  };
  const handleOpenuser = () => {
    setOpenUser(true);
  };

  const closeUser = () => {
    setOpenUser(false);
  };

  const handleClose = () => {
    onClose();
    setScenario('INITIAL')
    // resetDefault();
    // setProcessData([]);
  };
  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  useEffect(() => {
    if (userReducer.applicationName !== "") {
      // getGroups();
      let transformUserData = props?.userList.map((ele) => {
        userDetails.set(ele.emailId, ele.userName);
        return { code: ele.userName, description: ele.emailId };
      });
      setUserDetails(new Map(userDetails));
      // setUser(transformUserData);

      let transformGroupData = props?.groupList.map((ele) => {
        groupDetails.set(ele.id, ele.name);
        return { code: ele.name, description: ele.id };
      });
      setGroupDetails(new Map(groupDetails));
      setGroup(transformGroupData);

      // console.log(userDetails);
      // console.log(user);
    }
  }, [userReducer]);

  // const getGroups=()=>{
  //   setLoader(true)
  //   doAjax(
  //     "/CW_Worktext/v1/mail-definition/getGroups",
  //     "get",
  //     null,
  //     function (oData) {
  //       if(oData.statusCode=== 401 || oData.statusCode=== "401")
  //     {
  //       setShowConfirmation(true);
  //       setButtonAction("Timeout");
  //       setConfirmationMessage("Session Timed Out.Kindly Refresh");
  //     }
  //     if (!oData.data) {
  //       oData.data = [];
  //     } else {
  //       // setGroupData(oData.data.filter((ele) => ele.applicationId === "55"));
  //       setGroupData(oData.data);
  //       setLoader(false);
  //     }
  //     },
  //     function (error) {
  //       setEmailError("Error");
  //       setSeverity("error");
  //       handleClickalert();
  //       setLoader(false);
  //       // setAlert(true);
  //       // setAlertSeverity("error");
  //       // setAlertMessage(error);
  //     },
  //   );
  // }

  // const getTemplates=(groupName)=>{
  //   setLoader(true);
  //   doAjax(
  //     "/CW_Worktext/v1/mail-definition/getUsersByGroupName?groupName="+groupName,
  //     "get",
  //     null,
  //     function (oData) {
  //       if(oData.statusCode=== 401 || oData.statusCode=== "401")
  //     {
  //       setShowConfirmation(true);
  //       setButtonAction("Timeout");
  //       setConfirmationMessage("Session Timed Out.Kindly Refresh");
  //     }
  //     if (!oData.data) {
  //       oData.data = [];
  //     } else {
  //       // setGroupData(oData.data.data.filter((ele) => ele.applicationId === "55"));
  //       setGetTemplateData(oData.data);
  //       setLoader(false);
  //     }
  //     },
  //     function (error) {
  //       setEmailError("Error");
  //       setSeverity("error");
  //       handleClickalert();
  //       setLoader(false);
  //       // setAlert(true);
  //       // setAlertSeverity("error");
  //       // setAlertMessage(error);
  //     },
  //   );
  // };

  const handleClickalert = () => {
    setOpenalert(true);
  };

  const closeConfirmationDialog = (evt) => {
    if (evt === "Timeout") {
      window.location.reload();
    }
    setShowConfirmation(false);
    setConfirmationMessage("");
  };

  const getuser = (userIdList) => {
    let arr;
    if (userIdList.includes(",")) {
      arr = userIdList.split(",");
    } else {
      arr = userIdList.split(";");
    }
    let arr2 = arr.map((ele) => ({ emailId: ele, username: userDetails.get(ele) === undefined ? ele:userDetails.get(ele)  }));
    setUser(arr2);
    // setUser();
  };

  const getAssociatedTemplateData = (groupid) => {
    doCrudApi(
      "fetchAssociatedTemplatesHana",
      [groupid, groupid],

      function (oData) {
        if (oData.statusCode === 401 || oData.statusCode === "401") {
          setShowConfirmation(true);
          setButtonAction("Timeout");
          setConfirmationMessage("Session Timed Out.Kindly Refresh");
        }
        if (oData) setAssociatedtemplate(oData);
        console.log(oData);
        setLoader(false);
      },
      function (error) {
        props?.setAlert(true);
        props?.setAlertSeverity("error");
        props?.setAlertMessage(error);
        setLoader(false);
      }
    );
  };

  const getGroupdefinitiondata = () => {
    setLoader(true);

    doCrudApi(
      "fetchMailGroupingHana",
      [],
      function (oData) {
        if (oData.statusCode === 401 || oData.statusCode === "401") {
          setShowConfirmation(true);
          setButtonAction("Timeout");
          setConfirmationMessage("Session Timed Out.Kindly Refresh");
        }
        if (oData) {
          let transFormData = oData.map((ele) => {
            return { id: ele.id, name: ele.groupName, userIdList: ele.email };
          });
          setGroupData(transFormData);
          let result = transFormData.concat(props?.groupList);
          setAllGroups(result);
          // Array.prototype.push.apply(groupData,props?.groupList);
          // console.log(groupData);
          //   oData.forEach((e) => (e["isRowEditable"] = false));
          // setGroupData(oData);
          // let merged = [...props?.groupList, ...groupData];
          // setAllGroups(merged);
        }
        setLoader(false);
      },
      function (error) {
        setEmailError("Error");
        setSeverity("error");
        handleClickalert();
        setLoader(false);
        // setAlert(true);
        // setAlertSeverity("error");
        // setAlertMessage(error);
      }
    );
  };

  const handleSaveClick = () => {
    setLoader(true);
    let emailData = groupfield.email.split(";");
    // let emailData = groupData[index].email.split(";");
    let nameData = groupfield.groupName;
    if (emailValidator(emailData) && nameValidator(nameData)) {
      // option.isRowEditable = false;
      // let data1 = [...groupfield];
      // setGroupfield(data1);
      let url = "/WorkUtilsServices/v1/mail-group";
      let method = "POST";

      // if (ele.id) {
      //   method = "PATCH";
      // }

      let payload = {
        id: groupfield.id,
        createdBy: groupfield.createdBy,
        createdOn: groupfield.createdOn,
        email: groupfield.email,
        groupName: groupfield.groupName,
        updatedBy: userReducer.userData.user_id,
        updatedOn: new Date().toISOString(),
      };

      doAjax(
        url,
        method,
        payload,
        function (oData) {
          if (oData.statusCode === 401 || oData.statusCode === "401") {
            setShowConfirmation(true);
            setButtonAction("Timeout");
            setConfirmationMessage("Session Timed Out.Kindly Refresh");
          }
          getGroupdefinitiondata();
          if (method === "POST") {
            setEmailError("Saved successfully");
            setSeverity("success");
            handleClickalert();
          } else if (method === "PATCH") {
            setEmailError("Updated successfully");
            setSeverity("success");
            handleClickalert();
          }
          setLoader(false);
          setOpenGroup(false);
        },

        function (error) {
          setEmailError("Error");
          setSeverity("error");
          handleClickalert();
          setLoader(false);
        }
      );
    } else {
      setEmailError("Enter valid Data!");
      setSeverity("error");
      handleClickalert();
      setLoader(false);
    }
  };

  const emailValidator = (emailData) => {
    const reg = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return emailData.every((ele) => {
      return ele.match(reg);
    });
  };

  const nameValidator = (nameData) => {
    const regex = /^[A-Za-z][A-Za-z0-9_]*$/;
    return regex.test(nameData);
  };

  const handlegroupChange = (e, property) => {
    if (property === "groupName") {
      setGroupfield({
        ...groupfield,
        groupName: e.target.value,
      });
    } else {
      setGroupfield({
        ...groupfield,
        email: e.target.value,
      });
    }
  };

  useEffect(() => {
    if (userReducer.applicationName !== "") {
      getGroupdefinitiondata();
      console.log(userReducer.applicationName);
    }
  }, [userReducer]);

  return (
    <div  p={2} className="cwntSetHeight100 cwntSetWidth100">
      {/* <Dialog
        // fullScreen
        open={open}
        onClose={handleClose}
        // maxWidth="lg"
        PaperProps={{ style: { maxWidth: '100vw', // Set your desired 
        width: '100%', // Set your desired width 
        height: '100%', // Set your desired height 
        margin: '0', // Center the dialog horizontally 
        maxHeight:"100vh",
        boxShadow:"none"
        }, 
        }}
        hideBackdrop
        // TransitionComponent={Transition}
      > */}
        {/* <DialogTitle> */}
          <Stack direction="row" spacing={2} alignItems="center" justifyContent="space-between">
            <Stack direction="row" alignItems="center">
              <IconButton aria-label="delete" onClick={handleClose}>
                <ArrowBackIosIcon />
              </IconButton>
              <Typography
                sx={{
                  fontWeight: 600,
                  color: "black !important",
                  fontSize: "20px",
                }}
              >
                Groups
              </Typography>
            </Stack>
            <Stack direction="row" spacing={2} alignItems="center">
              <Button variant="outlined" onClick={openCreateGroup}>
                Create Group
              </Button>
            </Stack>
          </Stack>
        {/* </DialogTitle> */}
        {/* <DialogContent> */}
          <Stack height="100%" width="100%" direction="row" justifyContent="space-around" sx={{ overflow: "hidden", background: "#fff" }}>
            <Paper
              elevation={1}
              sx={{
                width: "35%",
                background: "#fff",
                borderRadius: "12px",
                margin: "1rem 0.5rem 1rem 1rem",
                height: "calc(100%-2rem)",
              }}
            >
              <Stack direction="row" pl={1} pr={1} justifyContent="space-between" alignItems="center" width="100%" height="2.5rem" sx={{ background: "#F1F5FE", borderRadius: "12px" }}>
                <span
                  style={{
                    fontWeight: 500,
                    color: "black ",
                    fontSize: "14px",
                    whiteSpace: "nowrap",
                    fontFamily: `"Roboto", sans-serif !important`,
                  }}
                >
                  Display Name
                </span>
              </Stack>
              <Stack
                pt={1}
                sx={{
                  height: "93%",
                  width: "100%",
                  borderRadius: "5px",
                  overflowY: "scroll",
                  backgroundColor: "white",
                  position: "relative",
                }}
                alignItems="center"
              >
                {/* <Backdrop
            open={isLoader || destinations === null}
            sx={{
              position: "absolute",
              zIndex: (theme) => theme.zIndex.drawer + 1,
            }}
          >
            <BusyLoader color="primary" />
          </Backdrop> */}
                {allGroups?.map((ele, index) => (
                  <Box sx={{ paddingTop: 1, paddingBottom: 2, width: "90%" }}>
                    <Card
                      sx={{ minWidth: 275, background: selectedRow === index ? " linear-gradient(180.76deg, #C1DCFF -113.11%, rgba(255, 255, 255, 0) 198.03%)" : "" }}
                      onClick={() => {
                        getuser(ele.userIdList);
                        getAssociatedTemplateData(ele.id);
                        // getTemplates(ele.name);
                        setSelectedRow(index);
                      }}
                      key={index}
                    >
                      <CardActionArea>
                        <CardHeader
                          avatar={
                            // <Avatar sx={{ bgcolor: "red" }} aria-label="recipe">
                            //   R
                            // </Avatar>
                            <CustomAvatar
                              src=""
                              name={ele.name}
                              sx={{ fontSize: "0.8rem", color: "black" }}
                              // className={classes.avatar}
                            />
                          }
                          title={ele.name}
                          // subheader="4 members"
                        />
                        {/* <CardContent>

        <Typography sx={{ fontSize: 14 }} color="text.secondary" gutterBottom>
          Group 1
        </Typography>
        
      </CardContent> */}
                      </CardActionArea>
                    </Card>
                  </Box>
                ))}
              </Stack>
            </Paper>
            <Paper
              elevation={1}
              sx={{
                width: "64%",
                background: "#fff",
                borderRadius: "12px",
                margin: "1rem 1rem 1rem 0.5rem",
                height: "calc(100%-2rem)",
              }}
            >
              <Stack direction="row" justifyContent="space-between">
                <Typography>Group Details</Typography>
             {userReducer.feature.EMAIL_CONFIG_MANAGE_GROUPS_ADD_USER === "True" &&(   <Button variant="outlined" onClick={handleOpenuser}>
                  {" "}
                  Add User
                </Button>)}
              </Stack>
              <Box sx={{ width: "100%", typography: "body1" }}>
                <TabContext value={value}>
                  <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
                    <TabList onChange={handleChange} aria-label="lab API tabs example">
                      {userReducer.feature.EMAIL_CONFIG_MANAGE_GROUPS_MEMBER_INFO === "True" &&(<Tab label="Member Information" value="1" />)}

                      {userReducer.feature.EMAIL_CONFIG_MANAGE_GROUPS_TEMPLATE_INFO === "True" &&(<Tab label="Associated Template" value="2" />)}
                    </TabList>
                  </Box>
                  <TabPanel value="1">
                    {" "}
                    <TableContainer component={Paper}>
                      <Table sx={{ minWidth: 650 }} aria-label="simple table">
                        <TableHead>
                          <TableRow>
                            <TableCell>User Name</TableCell>
                            <TableCell>Email ID</TableCell>
                          </TableRow>
                        </TableHead>

                        <TableBody>
                          {/* {userDetails.get(key).map((ele,index) =>( */}
                          {/* {props?.userList} */}
                          {/* {props?.groupList.map((ele,index) => ( */}

                          {user?.map((ele) => (
                            <TableRow
                              sx={{
                                "&:last-child td, &:last-child th": { border: 0 },
                              }}
                            >
                              <TableCell component="th" scope="row">
                                {/* {ele.key} */}
                                {ele.username}

                                {/* abc */}
                              </TableCell>
                              <TableCell>
                                {/* {ele.value} */}
                                {/* {ele.userIdList} */}
                                {/* <EMAIL> */}
                                {ele.emailId}
                              </TableCell>
                            </TableRow>
                          ))}

                          {/* ))} */}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </TabPanel>
                  <TabPanel value="2" overflowY="scroll" height="100%">
                    <Grid container direction="row" spacing={2}>
                      {associatedTemplate?.map((ele, index) => (
                        <Grid item xs={6} sm={6} md={6} xl={3}>
                          <Card
                            sx={{ backgroundColor: "#FFFFFF", minHeight: "7rem" , maxWidth:"20rem"}}
                            // onClick={() => handleCardClick(props?.cardData.emailDefinitionId)}
                          >
                            <CardActionArea sx={{ height: "100%" }}>
                              <CardContent>
                                <Stack direction="column" spacing={1}>
                                  <Stack direction="row" width="100%" justifyContent="space-between">
                                    <Typography
                                      // gutterBottom
                                      // component="div"
                                      sx={{
                                        // width: "164px !important",
                                        fontFamily: "Roboto, sans-serif !important",
                                        color: "#000000 !important",
                                        // fontStyle: "normal !important",
                                        fontWeight: "500 !important",
                                        fontSize: "14px !important",
                                        // lineHeight: "16.41px !important",
                                        height: "24px !important",
                                        // left: "12px !important",
                                        // top: "12px !important",
                                      }}
                                    >
                                      {ele.templateName}
                                      {/* Task Collaboration */}
                                    </Typography>
                                    <Stack direction="row">
                                      <IconButton size="small">
                                        <FiberManualRecordIcon
                                          variant="outlined"
                                          sx={{
                                            color: ele.status === "Active" ? "#4CAF50" : ele.status === "Draft" ? "#FFA500" : "#647C90",
                                            fontSize: "0.8rem",
                                          }}
                                        />
                                      </IconButton>
                                      <Typography
                                        sx={{
                                          width: "34px !important",
                                          // fontFamily: "Roboto Regular !important",
                                          color: "#000000 !important ",
                                          // fontStyle: "normal !important",
                                          fontWeight: "400 !important",
                                          fontSize: "12px !important",
                                          // lineHeight: "18.2px !important",
                                          //   color: "#4CAF50",
                                          color: ele.status === "Active" ? "#4CAF50" : ele.status === "Draft" ? "#FFA500" : "#647C90",
                                          display: "flex !important",
                                          height: "24px !important",
                                          alignItems: "center!important",
                                          // letterSpacing: " 0.25 !important",
                                        }}
                                      >
                                        {ele.status}
                                        {/* Active */}
                                      </Typography>
                                    </Stack>
                                  </Stack>

                                  <Stack direction="row" spacing={2}>
                                    <Chip
                                      label={ele.entitydesc}
                                      sx={{
                                        width: "120px !important",
                                        // fontFamily: "Roboto Regular!important",
                                        color: "#072E3A !important ",
                                        // fontStyle: "normal",
                                        fontWeight: "400 !important",
                                        fontSize: "14px !important",
                                        lineHeight: "16.41px !important",
                                        display: "flex",
                                        flexDirection: "row",
                                        justifyContent: "center",
                                        alignItems: "center",
                                        padding: "8px !important",
                                        background: "#E4F1FF !important",
                                        height: "24px !important",
                                        // left: "12px !important",
                                        // top: "60px !important",
                                        borderRadius: "9px !important",
                                      }}
                                    />
                                    <Chip
                                      label={ele.processName}
                                      variant="filled"
                                      sx={{
                                        width: "120px !important",
                                        // fontFamily: "Roboto Regular !important",
                                        color: "#600E59 !important",
                                        // fontStyle: "normal !important",
                                        fontWeight: "400 !important",
                                        fontSize: "14px !important",
                                        lineHeight: "16px !important",
                                        display: "flex",
                                        flexDirection: "row",
                                        justifyContent: "center",
                                        alignItems: "center",
                                        padding: "8px !important",
                                        // color: "#072E3A",
                                        background: "#FBEEFA !important",
                                        height: "24px !important",
                                        // left: "12px !important",
                                        // top: "60px !important",
                                        borderRadius: "9px !important",
                                      }}
                                    />
                                  </Stack>
                                </Stack>
                              </CardContent>
                            </CardActionArea>
                            {/* <CardActions>
  <Button size="small">Share</Button>
  <Button size="small">Learn More</Button>
</CardActions> */}
                          </Card>
                        </Grid>
                      ))}
                    </Grid>
                  </TabPanel>
                </TabContext>
              </Box>
            </Paper>
            {/* <Snackbar
        open={openAlert}
        autoHideDuration={6000}
        onClose={() => setAlert(false)}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={() => setAlert(false)}
          severity={alertSeverity}
          sx={{ width: "100%" }}
          variant="filled"
          elevation={6}
        >
          {alertMessage}
        </Alert>
      </Snackbar>
      <Confirmation
        message={confirmationMessage}
        creationType={buttonAction}
        open={showConfirmation}
        onClose={(evt) => closeConfirmationDialog(evt)}
      />
      <Confirmation message={" Do you want to delete this record "} creationType="delete" open={showConfirmation} onClose={(evt) => closeConfirmationDialog(evt)} /> */}
          </Stack>
        {/* </DialogContent> */}
        {/* <DialogActions>
            <Button onClick={handleClose}>Cancel</Button>
            <Button onClick={handleClose} variant="contained" color="primary">
              Save
            </Button>
          </DialogActions> */}
        <Dialog open={openGroup} onClose={closeCreateGroup}>
          <DialogTitle>Create Group</DialogTitle>
          <DialogContent>
            <Stack>
              <Stack justifyContent="space-between" alignItems="left">
                <span
                  style={{
                    fontWeight: 500,
                    color: "black ",
                    fontSize: "1rem",
                  }}
                >
                  Group Name:
                </span>

                <TextField
                  autoFocus
                  margin="dense"
                  value={groupfield.groupName}
                  id="groupName"
                  sx={{
                    width: "25rem",
                    "& .MuiInputBase-root": {
                      height: 40,
                    },
                  }}
                  // label="Email Address"
                  fullWidth
                  variant="outlined"
                  onChange={(e) => handlegroupChange(e, "groupName")}
                />
              </Stack>

              <Stack justifyContent="space-between" alignItems="left">
                <span
                  style={{
                    fontWeight: 500,
                    color: "black ",
                    fontSize: "1rem",
                  }}
                >
                  Email ID:
                </span>

                <TextField
                  autoFocus
                  margin="dense"
                  id="emailId"
                  value={groupfield?.email}
                  fullWidth
                  variant="outlined"
                  sx={{
                    width: "25rem",
                    "& .MuiInputBase-root": {
                      height: 40,
                    },
                  }}
                  onChange={(e) => handlegroupChange(e, "email")}
                />
              </Stack>
            </Stack>
          </DialogContent>
          <DialogActions>
            <Button onClick={closeCreateGroup}>Cancel</Button>
            <Button variant="contained" onClick={() => handleSaveClick()}>
              Save & Continue{" "}
            </Button>
          </DialogActions>
        </Dialog>
        <Dialog open={openUser} onClose={closeUser}>
          <DialogTitle>Add User</DialogTitle>
          <DialogContent>
            <Stack>
              <Typography sx={{ height: "29px" }}>PO Group</Typography>

              <Stack justifyContent="space-between" alignItems="left">
                <span
                  style={{
                    fontWeight: 500,
                    color: "black ",
                    fontSize: "1rem",
                  }}
                >
                  Select User
                </span>

                <TextField
                  autoFocus
                  margin="dense"
                  id="emailId"
                  fullWidth
                  variant="outlined"
                  sx={{
                    width: "25rem",
                    "& .MuiInputBase-root": {
                      height: 40,
                    },
                  }}
                />
              </Stack>
            </Stack>
          </DialogContent>
          <DialogActions>
            <Button onClick={closeUser} variant="outlined">Cancel</Button>
            <Button variant="contained">Save </Button>
          </DialogActions>
        </Dialog>
      {/* </Dialog> */}
      <Confirmation message={confirmationMessage} creationType={buttonAction} open={showConfirmation} onClose={(evt) => closeConfirmationDialog(evt)} />
      <Snackbar open={openalert} autoHideDuration={6000} onClose={handleToClose} anchorOrigin={{ vertical: "bottom", horizontal: "center" }}>
        <Alert onClose={handleToClose} severity={severity} sx={{ width: "100%" }}>
          {emailError}
        </Alert>
      </Snackbar>
    </div>
  );
};

export default ManageGroup;
