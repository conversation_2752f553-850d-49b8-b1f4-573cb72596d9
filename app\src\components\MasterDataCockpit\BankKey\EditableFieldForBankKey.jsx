import React, { useEffect, useState } from "react";
import {
  St<PERSON>,
  <PERSON><PERSON>graphy,
  Button,
  TextField,
  Grid,
  Autocomplete,
  IconButton,
  FormControlLabel,
  Checkbox,
} from "@mui/material";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import SaveIcon from "@mui/icons-material/Save";
import { DateRangePicker } from "rsuite";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";

import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { setPayload } from "../../../app/editPayloadSlice";
import { setSingleCostCenterPayload } from "../../../app/costCenterTabsSlice";
import { doAjax } from "../../Common/fetchService";
import {
  destination_BankKey,
  destination_CostCenter,
} from "../../../destinationVariables";
import { setDropDown } from "../../../app/dropDownDataSlice";
import {
  setBKRequiredFields,
  setSingleBankKeyPayload,
} from "../../../app/bankKeyTabSlice";
function transformApiData(apiValue, dropDownData) {
  if (Array.isArray(dropDownData)) {
    const matchingOption = dropDownData.find(
      (option) => option.code === apiValue
    );
    return matchingOption || "";
  } else {
    return "";
  }
}
const EditableFieldForBankKey = ({
  label,
  value,
  units,
  data,
  onSave,
  isEditMode,
  visibility,
  length,
  isExtendMode,
  options = [],
  type,
}) => {
  const [editedValue, setEditedValue] = useState(value);
  const [changeStatus, setChangeStatus] = useState(false);
  const [error,setError]=useState(false);
  const dropDownData = useSelector((state) => state.AllDropDown.dropDown);
  const dispatch = useDispatch();
  const transformedValue = transformApiData(editedValue, dropDownData);
  const errorFields = useSelector((state) => state.bankKey.requiredFields);
  console.log("editedValue", label, editedValue);
  console.log("dropdownData", dropDownData);
  console.log("value e", value);
  console.log("label", label);
  console.log("units", units);
  console.log("transformedValue", transformedValue);

  const editField = useSelector((state) => state.edit.payload);

  console.log("editField", editedValue);
  const fieldData = {
    label,
    value: editedValue,
    units,
    type,
  };
  console.log("fieldData", fieldData);
  const handleSave = () => {
    onSave(fieldData);
  };
  useEffect(() => {
    if(isEditMode){
    if (visibility === "0" || visibility === "Required") {
      dispatch(setBKRequiredFields(key));
    }
  }
  }, []);
  let key = label
    .replaceAll("(", "")
    .replaceAll(")", "")
    .replaceAll("/", "")
    .replaceAll("-", "")
    .replaceAll(".", "")
    .split(" ")
    .join("");
  useEffect(() => {
    setEditedValue(value);
  }, [value]);

  const onEdit = (newValue) => {
    dispatch(
      setPayload({
        keyname: key
          .replaceAll("(", "")
          .replaceAll(")", "")
          .replaceAll("/", "")
          .replaceAll("-", "")
          .replaceAll(".", "")
          .split(" ")
          .join(""),
        data: newValue,
      })
    );
  };


  const getRegion1 = (value) => {
    const hSuccess = (data) => {
      console.log("value", value);
      dispatch(setDropDown({ keyName: "Region1", data: data.body }));
    };
    const hError = (error) => {
      console.log(error, "error in dojax");
    };
    doAjax(
      `/${destination_BankKey}/data/getRegionBasedOnCountry?country=${value}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getRegion2 = (value) => {
    // console.log("value",value.code);
    const hSuccess = (data) => {
      // console.log("value",data);
      dispatch(setDropDown({ keyName: "Region2", data: data.body }));
    };
    const hError = (error) => {
      console.log(error, "error in dojax");
    };
    doAjax(
      `/${destination_BankKey}/data/getRegionBasedOnCountry?country=${value}`,
      "get",
      hSuccess,
      hError
    );
  };
  // console.log("editedValue[key] ", dropDownData[key]);
  // console.log("editedValue[key] ", editedValue);
  return (
    <Grid item>
      <Stack>
        {isEditMode ? (
          <>
            <Typography variant="body2" color="#777">
              {label}
              {visibility === "Required" || visibility === "0" ? (
                <span style={{ color: "red" }}>*</span>
              ) : (
                ""
              )}
            </Typography>

            {type === "Drop Down" ? (
              <Autocomplete
                options={dropDownData[key] ?? []}
                value={
                  (data[key] &&
                    dropDownData[key]?.filter(
                      (x) => x.code === data[key]
                    ) &&
                    data[key] &&
                    dropDownData[key]?.filter(
                      (x) => x.code === data[key]
                    )[0]) ||
                  ""
                }
                onChange={(event, newValue, reason) => {
                  
                  if (visibility === "Required" || visibility === "0") {
                    //alert("coming")
                    if (newValue === null) 
                    setError(true);
                  }
                  if (label === "Country 1") {
                    // console.log("eventtttt",event,newValue);
                    getRegion1(newValue?.code);
                  }
                  if (label === "Country 2") {
                    getRegion2(newValue?.code);
                  }
                  if(reason==="clear"){
                    onEdit("");
                  }
                  else{
                    onEdit(newValue?.code);
                  }
                  setEditedValue(newValue?.code);
                  setChangeStatus(true);
                }}
                getOptionLabel={(option) => {
                  return option === "" || option?.code === ""
                    ? ""
                    : `${option?.code} - ${option?.desc}` ?? "";
                  // return option[0]?.code === ""
                  //   ? "Select"
                  //   : `${option && option[0]?.code} - ${option && option[0]?.desc}`;
                }}
                // isOptionEqualToValue={(a,b)=>{ return a.code===b.code}}
                renderOption={(props, option) => {
                  console.log("option vakue", option);
                  return (
                    <li {...props}>
                      <Typography style={{ fontSize: 12 }}>
                        {`${option?.code} - ${option?.desc}`}
                      </Typography>
                    </li>
                  );
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    variant="outlined"
                    placeholder={`Select ${fieldData.label}`}
                    size="small"
                    label={null}
                    error={error}
                  />
                )}
              />
            ) : type === "Input" ? (
              <TextField
                variant="outlined"
                size="small"
                value={data[key].toUpperCase()}
                placeholder={`Enter ${fieldData.label}`}
                inputProps={{ maxLength: length }}
                onChange={(event) => {
                  const newValue = event.target.value;
                      //console.log(visibility,"visibility========");
                      
                      if (newValue.length > 0 && newValue[0] === " ") {
                        onEdit(newValue.trimStart());
                      } else {
                        //let costCenterValue = e.target.value;
                        let costCenterUpperCase = newValue.toUpperCase();
                        onEdit(costCenterUpperCase);
                      }
                      if(visibility === "Required" || visibility === "0"){
                        if(newValue.length <= 0)
                        setError(true)
                      }
                      // onEdit(newValue);
                      setEditedValue(newValue.toUpperCase());
                    }}
                    error={error}
              />
            ) : type === "Calendar" ? (
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  slotProps={{ textField: { size: "small" } }}
                  // value={valueFromPayload[props?.keyName]}
                  placeholder="Select Date Range"
                  // onChange={(newValue) =>
                  //   dispatch(
                  //     setPayload({
                  //       keyName: props.keyName,
                  //       data: "/Date(" + Date.parse(newValue) + ")/",
                  //     })
                  //   )
                  // }
                  // required={
                  //   props.details.visibility === "0" ||
                  //   props.details.visibility === "Required"
                  // }
                />
              </LocalizationProvider>
            ) : type === "Radio Button" ? (
              <Grid item md={2}>
                <Checkbox
                  sx={{ padding: 0 }}
                  checked={data[key]}
                  onChange={(event, newValue) => {
                    onEdit(newValue);
                    setEditedValue(newValue);
                  }}
                />
              </Grid>
            ) : (
              ""
            )}
          </>
        ) : (
          <>
            <>
              <Typography variant="body2" color="#777">
                {label}
                {visibility === "Required" || visibility === "0" ? (
                  <span style={{ color: "red" }}>*</span>
                ) : (
                  ""
                )}
              </Typography>
              <Typography variant="body2" fontWeight="bold">
                {type === "Radio Button" ? (
                  <Checkbox
                    sx={{ padding: 0 }}
                    checked={editedValue}
                    disabled
                  />
                ) : (
                  editedValue
                )}
              </Typography>
            </>
          </>
        )}
      </Stack>
    </Grid>
  );
};

export default EditableFieldForBankKey;
