import{b as oe,s as le,r as s,u as se,q as b,cd as re,j as o,G as n,al as de,a as t,z as ce,T as u,B as k,x as q,b4 as pe,br as he,aE as O,t as m,aB as g,aD as P,K as W,eK as $,eQ as C,bp as ue,I as me,b1 as xe,b8 as G,ai as R}from"./index-17b8d91e.js";import{d as ge}from"./ArrowCircleLeftOutlined-2a09f8e2.js";import{d as fe}from"./EditOutlined-36c8ca4d.js";import{C as ye,d as be}from"./ChangeLog-0f47d713.js";import{E as ke}from"./EditFieldForMassBankKey-ef40d942.js";import{a as Ce,b as ve,S as Se}from"./Stepper-88e4fb0c.js";import"./DatePicker-********.js";import"./dateViewRenderers-********.js";import"./useSlotProps-e34e1e13.js";import"./InputAdornment-5b0053c5.js";import"./CSSTransition-30917e2c.js";import"./useMediaQuery-6a073ac5.js";import"./DesktopDatePicker-07c19cde.js";import"./useMobilePicker-9978caff.js";const qe=()=>{var z,L;const E=oe(),p=le();s.useState({});const[j,K]=s.useState(0),[c,H]=s.useState(!1),[Be,I]=s.useState(!0);s.useState(0),s.useState([]);const[d,f]=s.useState(0),[Q,N]=s.useState(!1);s.useState([]);const[D,J]=s.useState([]),[U,M]=s.useState(!1),h=se();console.log("location",h.state),b(e=>e.initialData.EditMultipleMaterial),b(e=>e.initialData.MultipleMaterial);const y=h.state.tabsData.viewData;console.log(y,"tabsData=====");const i=h.state.rowData;console.log(i,"selectedRowData=================");const X=h.state.requestNumber;let v=b(e=>e.edit.payload);console.log(v,"singleBKPayloadAfterChange");let S=b(e=>e.bankKey.requiredFields);console.log(S,"required_field_for_data");const Y=e=>{const a=r=>{p(R({keyName:"Region",data:r.body}))},l=r=>{console.log(r,"error in dojax")};W(`/${$}/data/getRegionBasedOnCountry?country=${e}`,"get",a,l)},Z=e=>{const a=r=>{p(R({keyName:"Region1",data:r.body}))},l=r=>{console.log(r,"error in dojax")};W(`/${$}/data/getRegionBasedOnCountry?country=${e}`,"get",a,l)};s.useEffect(()=>{var e;Y(i==null?void 0:i.bankCountry),Z((e=y["Address Details"]["Street Address"].find(a=>(a==null?void 0:a.fieldName)==="Country 1"))==null?void 0:e.value)},[c]),s.useEffect(()=>{p(re(_))},[]),console.log(c,"isEditMode");const ee=()=>{H(!0),I(!1)},w=()=>{const e=T();c?e?(f(a=>a-1),p(C())):A():(f(a=>a-1),p(C()))},F=()=>{const e=T();c?e?(f(a=>a+1),p(C())):A():(f(a=>a+1),p(C()))},A=()=>{M(!0)},x=Object.entries(y).filter(e=>typeof e[1]=="object"&&e[1]!=null).map(e=>e[0]);console.log("tabsArray",x,d);const B=Object.entries(y).filter(e=>typeof e[1]=="object"&&e[1]!=null).map(e=>Object.entries(e[1])),_={};B.map(e=>{e.forEach((a,l)=>{a.forEach((r,ie)=>{ie!==0&&r.forEach(V=>{_[V.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("")]=V.value})})})});const te=e=>{N(e)},ae=()=>{N(!0)};console.log(v,S,"========deeced"),console.log(d,"activeStep");const T=()=>ue(v,S,J),ne=()=>{M(!1)};return o("div",{children:[o(n,{container:!0,style:{...de,backgroundColor:"#FAFCFF"},children:[D.length!=0&&t(ce,{openSnackBar:U,alertMsg:"Please fill the following Field: "+D.join(", "),handleSnackBarClose:ne}),o(n,{sx:{width:"inherit"},children:[o(n,{item:!0,md:12,style:{padding:"16px",display:"flex"},children:[t(n,{item:!0,style:{display:"flex",justifyContent:"flex-end"},children:t(me,{color:"primary","aria-label":"upload picture",component:"label",sx:xe,children:t(ge,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{E("/masterDataCockpit/bankKey/createMultipleBankKey")}})})}),o(n,{md:10,children:[t(u,{variant:"h3",children:o("strong",{children:["Multiple Bank Keys: ",i.bankKey," "]})}),t(u,{variant:"body2",color:"#777",children:"This view dispalys detail of Multiple Bank Keys"})]}),(z=h==null?void 0:h.state)!=null&&z.requestNumber?t(n,{md:.5,sx:{display:"flex",justifyContent:"flex-end"},children:t(m,{variant:"outlined",size:"small",sx:G,onClick:ae,title:"Change Log",children:t(be,{sx:{padding:"2px"},fontSize:"small"})})}):t(n,{md:.5,sx:{display:"flex",justifyContent:"flex-end"}}),Q&&t(ye,{open:!0,closeModal:te,requestId:X,requestType:"Mass",pageName:"bankKey",controllingArea:i==null?void 0:i.bankCountry,centerName:i.bankKey}),c?"":t(n,{md:1.5,sx:{display:"flex",justifyContent:"flex-end"},children:t(n,{item:!0,children:o(m,{variant:"outlined",size:"small",sx:G,onClick:ee,children:["Change",t(fe,{sx:{padding:"2px"},fontSize:"small"})]})})})]}),t(n,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:o(k,{width:"70%",sx:{marginLeft:"40px"},children:[t(n,{item:!0,sx:{paddingTop:"2px !important"},children:o(q,{flexDirection:"row",children:[t("div",{style:{width:"15%"},children:t(u,{variant:"body2",color:"#777",children:"Bank Country"})}),o(u,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[": ",i==null?void 0:i.bankCountry]})]})}),t(n,{item:!0,sx:{paddingTop:"2px !important"},children:o(q,{flexDirection:"row",children:[t("div",{style:{width:"15%"},children:t(u,{variant:"body2",color:"#777",children:"Bank Key"})}),o(u,{variant:"body2",fontWeight:"bold",children:[": ",i==null?void 0:i.bankKey]})]})})]})}),o(n,{container:!0,style:{padding:"16px"},children:[t(Se,{activeStep:d,sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},"aria-label":"mui tabs example",children:x.map((e,a)=>t(Ce,{children:t(ve,{sx:{fontWeight:"700"},children:e})},e))}),t(n,{container:!0,children:B&&((L=B[d])==null?void 0:L.map((e,a)=>t(k,{sx:{width:"100%"},children:o(n,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...pe},children:[t(n,{container:!0,children:t(u,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:e[0]})}),t(k,{children:t(k,{sx:{width:"100%"},children:t(he,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:t(n,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:[...e[1]].map(l=>(console.log("inneritem",l),t(ke,{activeTabIndex:d,fieldGroup:e[0],selectedRowData:i.bankKey,pcTabs:x,label:l.fieldName,value:l.value,length:l.maxLength,visibility:l.visibility,onSave:r=>handleFieldSave(l.fieldName,r),isEditMode:c,type:l.fieldType,field:l})))})})})})]})},a)))})]})]})]}),c?t(P,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:o(O,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:j,onChange:e=>{K(e)},children:[t(m,{size:"small",sx:{...g,mr:1},variant:"contained",onClick:()=>{E("/masterDataCockpit/bankKey/createMultipleBankKey")},children:"Save"}),t(m,{variant:"contained",size:"small",sx:{...g,mr:1},onClick:w,disabled:d===0,children:"Back"}),t(m,{variant:"contained",size:"small",sx:{...g,mr:1},onClick:F,disabled:d===x.length-1,children:"Next"})]})}):"",c?"":t(P,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:o(O,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:j,onChange:e=>{K(e)},children:[t(m,{variant:"contained",size:"small",sx:{...g,mr:1},onClick:w,disabled:d===0,children:"Back"}),t(m,{variant:"contained",size:"small",sx:{...g,mr:1},onClick:F,disabled:d===x.length-1,children:"Next"})]})})]})};export{qe as default};
