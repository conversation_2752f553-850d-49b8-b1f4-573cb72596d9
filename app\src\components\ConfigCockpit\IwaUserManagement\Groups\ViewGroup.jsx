import { useDispatch } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import { useSnackbar } from "@hooks/useSnackbar";
import { ViewGroup } from "@cw/viewgroup";
import {APP_END_POINTS} from "@constant/appEndPoints";

const ViewGroupContainer = () => {
    const { groupId } = useParams();
    const navigate = useNavigate();
    const dispatch = useDispatch();
     const { showSnackbar } = useSnackbar();

    const onViewGroupActionClick = (action, _groupId, response) => {
        if (action === "editGroup") {
            if (groupId) {
                navigate(APP_END_POINTS.IWA_USER_MANAGEMENT.EDIT_GROUP);
            } else {
                dispatch(showSnackbar({ message: "Missing groupId for edit action.", type: "warning" }));
            }
        } else if (action === "groupSummary") {
            navigate( APP_END_POINTS.IWA_USER_MANAGEMENT.GROUPS_SUMMARY);
        }

        if (response) {
            showSnackbar(response.message ,"info" );
        }
    };

    const dateTimeConfig = {
        dateFormat: "DD-MMM-YYYY",
        timeFormat: "24hr",
    };

    return (
        <ViewGroup
            groupId={groupId}
            onViewGroupActionClick={onViewGroupActionClick}
            dateTimeConfig={dateTimeConfig}
        />
    );
};

export default ViewGroupContainer;
