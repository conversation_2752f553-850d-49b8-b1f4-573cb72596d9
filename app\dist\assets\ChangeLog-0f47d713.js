import{m as z,n as K,o as J,e2 as c,bQ as de,r as g,q as F,dG as L,e3 as ie,e4 as ue,e5 as oe,a_ as ce,e6 as O,e7 as N,a5 as he,j as C,a as n,a3 as ge,Y as Ce,B as y,x as B,G as R,T as G,h as me,I as U,b1 as Ve,R as Ne,aG as Se,v as pe,w as fe,aC as Pe,e8 as Ae,F as Oe,K as ye,y as Ee,e9 as Me,t as ve,Z as De,$ as _e,a0 as xe,ea as be}from"./index-17b8d91e.js";var E={},Te=K;Object.defineProperty(E,"__esModule",{value:!0});var Ie=E.default=void 0,Fe=Te(z()),<PERSON>=J;Ie=E.default=(0,Fe.default)((0,Le.jsx)("path",{d:"m19.07 4.93-1.41 1.41C19.1 7.79 20 9.79 20 12c0 4.42-3.58 8-8 8s-8-3.58-8-8c0-4.08 3.05-7.44 7-7.93v2.02C8.16 6.57 6 9.03 6 12c0 3.31 2.69 6 6 6s6-2.69 6-6c0-1.66-.67-3.16-1.76-4.24l-1.41 1.41C15.55 9.9 16 10.9 16 12c0 2.21-1.79 4-4 4s-4-1.79-4-4c0-1.86 1.28-3.41 3-3.86v2.14c-.6.35-1 .98-1 1.72 0 1.1.9 2 2 2s2-.9 2-2c0-.74-.4-1.38-1-1.72V2h-1C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10c0-2.76-1.12-5.26-2.93-7.07"}),"TrackChangesTwoTone");var M={},Be=K;Object.defineProperty(M,"__esModule",{value:!0});var Q=M.default=void 0,Re=Be(z()),Ge=J;Q=M.default=(0,Re.default)((0,Ge.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8m4.17-5.24-1.1-1.1c.71-1.33.53-3.01-.59-4.13C13.79 8.84 12.9 8.5 12 8.5c-.03 0-.06.01-.09.01L13 9.6l-1.06 1.06-2.83-2.83L11.94 5 13 6.06l-.96.96c1.27.01 2.53.48 3.5 1.44 1.7 1.71 1.91 4.36.63 6.3m-1.28 1.41L12.06 19 11 17.94l.95-.95c-1.26-.01-2.52-.5-3.48-1.46-1.71-1.71-1.92-4.35-.64-6.29l1.1 1.1c-.71 1.33-.53 3.01.59 4.13.7.7 1.63 1.04 2.56 1.01L11 14.4l1.06-1.06z"}),"ChangeCircleOutlined");var j,k,H,q,$,W,Y;const Ue={[(j=c)==null?void 0:j.LOGISTIC]:{"Logistic Data":{fieldName:["Material","AltUnit","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Alternative Unit of Measure","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]}},[(k=c)==null?void 0:k.MRP]:{"Basic Data":{fieldName:["Material","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},"Plant Data":{fieldName:["Material","Plant","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Plant","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]}},[(H=c)==null?void 0:H.WARE_VIEW_2]:{"Warehouse Data":{fieldName:["Material","WhseNo","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Warehouse","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]}},[(q=c)==null?void 0:q.ITEM_CAT]:{"Item Cat Group":{fieldName:["Material","SalesOrg","DistrChan","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Sales Org","Distribution Channel","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]}},[($=c)==null?void 0:$.SET_DNU]:{Description:{fieldName:["Material","Langu","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Language","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},"Plant Data":{fieldName:["Material","Plant","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Plant","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},"Basic Data":{fieldName:["Material","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},"Sales Data":{fieldName:["Material","SalesOrg","DistrChan","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Sales Org","Distribution Channel","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]}},[(W=c)==null?void 0:W.UPD_DESC]:{"Update Descriptions":{fieldName:["Material","Langu","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Language","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]}},[(Y=c)==null?void 0:Y.CHG_STAT]:{"Plant Data":{fieldName:["Material","Plant","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Plant","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},"Basic Data":{fieldName:["Material","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},"Sales Data":{fieldName:["Material","SalesOrg","DistrChan","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Sales Org","Distribution Channel","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]}}},He=({open:S,closeModal:Z,requestId:v,requestType:X})=>{const{customError:p}=de(),[D,m]=g.useState(!0),[V,w]=g.useState(null),f=F(e=>e.payload.payloadData),h=F(e=>e.payload.dynamicKeyValues),_=f==null?void 0:f.TemplateName,[d,ee]=g.useState(()=>{const e=Ue[_]||{};return Object.keys(e).map(a=>({label:a,columns:e[a],rows:[]}))}),[o,ae]=g.useState(()=>d.length>0?{number:0,label:d[0].label}:{number:0,label:""}),le=(e,a)=>{ae({number:a,label:d[a].label})},ne={position:"absolute",top:"50%",left:"52%",transform:"translate(-50%, -50%)",width:"80%",height:"auto",bgcolor:"#fff",boxShadow:4,p:2},x=()=>{Z(!1)};g.useEffect(()=>{(async()=>{if(S&&!V)try{const a=await te(v,X);w(a)}catch(a){p("Error fetching changelog data:",a)}})()},[S,v]),g.useEffect(()=>{if(V&&o)try{ee(e=>e==null?void 0:e.map(a=>{const i=L(ie,_),r=typeof i=="object"?i[a==null?void 0:a.label]:i,t=L(ue,r),u=oe(V[r],r);return{...a,rows:u==null?void 0:u.map(l=>({id:ce(),...l,Material:O(l==null?void 0:l.ObjectNo,1),SAPValue:N(l==null?void 0:l.SAPValue),PreviousValue:N(l==null?void 0:l.PreviousValue),CurrentValue:N(l==null?void 0:l.CurrentValue),ChangedOn:N(l==null?void 0:l.ChangedOn),...(t==null?void 0:t.length)>0&&{[t[0]]:O(l==null?void 0:l.ObjectNo,2)},...(t==null?void 0:t.length)>1&&{[t[1]]:O(l==null?void 0:l.ObjectNo,3)}}))}}))}catch(e){p(he.CHANGE_LOG_MESSAGE,e)}},[V]);const te=e=>{var r,t,u;m(!0);const a=`/${De}/${(r=_e)==null?void 0:r.CHG_DISPLAY_REQUESTOR.FETCH_CHANGELOG_DATA}`;let i={requestId:(t=h==null?void 0:h.childRequestHeaderData)!=null&&t.ChildRequestId?null:e,ChildRequestId:(u=h==null?void 0:h.childRequestHeaderData)==null?void 0:u.ChildRequestId};return new Promise((l,P)=>{ye(a,"post",s=>{var I;if((s==null?void 0:s.statusCode)===xe.STATUS_200&&((I=s==null?void 0:s.body)==null?void 0:I.length)>0){const re=be(s==null?void 0:s.body);m(!1),l(re)}else m(!1),l([])},s=>{m(!1),p(s),P(s)},i)})},se=new Date,b=new Date;b.setDate(b.getDate()-15);const T={convertJsonToExcel:()=>{const e=d.map(a=>{const i=a.columns.fieldName.map((r,t)=>({header:a.columns.headerName[t],key:r}));return{sheetName:a.label,fileName:`Changelog Data-${Ee(se).format("DD-MMM-YYYY")}`,columns:i,rows:a.rows}});Me(e)},button:()=>n(ve,{sx:{textTransform:"capitalize",position:"absolute",right:0,top:0},onClick:()=>T.convertJsonToExcel(),children:"Download"})};return C(Oe,{children:[D&&n(Ce,{blurLoading:D,loaderMessage:ge.CHANGELOG_LOADING}),n(Ae,{open:S,onClose:x,"aria-labelledby":"modal-modal-title","aria-describedby":"modal-modal-description",children:C(y,{sx:ne,children:[n(B,{children:C(R,{item:!0,md:12,sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[C(y,{sx:{display:"flex",alignItems:"center",gap:"8px"},children:[n(Q,{sx:{color:"black",fontSize:"20px","&:hover":{transform:"rotate(360deg)",transition:"0.9s"},textAlign:"center",marginTop:"4px"}}),n(G,{id:"modal-modal-title",variant:"subtitle1",fontSize:"16px",fontWeight:"bold",sx:{color:"black"},children:"Change Log"})]}),C(y,{sx:{display:"flex",alignItems:"center",gap:"8px"},children:[n(me,{title:"Export Table",children:n(U,{sx:Ve,onClick:T.convertJsonToExcel,children:n(Ne,{iconName:"IosShare"})})}),n(U,{sx:{padding:"0 0 0 5px"},onClick:x,children:n(Se,{})})]})]})}),n(pe,{value:o==null?void 0:o.number,onChange:le,variant:"scrollable",scrollButtons:"auto","aria-label":"modal tabs",children:d==null?void 0:d.map((e,a)=>n(fe,{label:e.label},a))}),n("div",{className:"tab-content",style:{position:"relative",height:"100%",marginTop:16},children:d==null?void 0:d.map((e,a)=>{var i,r,t,u;return(o==null?void 0:o.number)===a&&n(G,{id:`modal-tab-content-${a}`,sx:{mt:1},children:n(R,{item:!0,sx:{position:"relative"},children:n(B,{children:n(Pe,{rows:e==null?void 0:e.rows,columns:(r=(i=e==null?void 0:e.columns)==null?void 0:i.fieldName)==null?void 0:r.map((l,P)=>{var A;return{field:l,headerName:(A=e==null?void 0:e.columns)==null?void 0:A.headerName[P],flex:1,minWidth:100}}),getRowIdValue:"id",pageSize:(u=(t=e==null?void 0:e.columns)==null?void 0:t.fieldName)==null?void 0:u.length,autoHeight:!0,scrollbarSize:10,sx:{"& .MuiDataGrid-row:hover":{backgroundColor:"#EAE9FF40"},backgroundColor:"#fff"}})})})},a)})})]})})]})};export{He as C,Q as a,Ie as d};
