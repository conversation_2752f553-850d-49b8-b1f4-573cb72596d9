import{r as n,bA as s,bB as a,bC as i,a as u,aY as c}from"./index-75c1660a.js";const f=({isLoadingToast:o,type:e,message:t,isToastPromise:l})=>{const r=n.useRef(null);return n.useEffect(()=>{l?o?r.current===null?r.current=s.loading("Please wait...",{autoClose:!1,hideProgressBar:!1,closeOnClick:!1,pauseOnHover:!1,draggable:!1,theme:"dark",position:"top-center",transition:a}):s.update(r.current,{render:"Please wait...",type:"default",isLoading:!0,autoClose:!1,hideProgressBar:!1,closeOnClick:!1,pauseOnHover:!1,draggable:!1,theme:"dark",position:"top-center",transition:a}):(e==="success"||e==="error")&&r.current!==null&&(s.update(r.current,{render:t,type:e,isLoading:!1,autoClose:3e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!0,theme:"dark",position:"top-center",transition:i}),r.current=null):e==="success"?s.success(t,{autoClose:2e3,theme:"dark",position:"top-center",transition:a}):e==="error"&&s.error(t,{autoClose:2e3,theme:"dark",position:"top-center",transition:a})},[o,e,t,l]),u(c,{className:"custom-toast-container",style:{minWidth:"300px",width:"auto",maxWidth:"90%",wordBreak:"break-word"},limit:1})};export{f as R};
