import React, { useState, useEffect, useRef } from 'react';
import {
  Dialog, DialogTitle, DialogContent, DialogActions, AppBar, Toolbar, Typography, Grid,
  Card, CardContent, CardHeader, TextField, Button, List, ListItem, ListItemAvatar,
  ListItemText, Avatar, Chip, Box, Paper, Divider, IconButton,
  colors
} from '@mui/material';
import {
  Message as MessageIcon, People, PersonAdd, Send, Login, Person,
  PersonOutline, Close, Circle
} from '@mui/icons-material';
import { Alert } from '@mui/material';
import { Client } from '@stomp/stompjs';
import SockJS from 'sockjs-client';
import { useSelector } from 'react-redux';
import { CHAT_MESSAGES } from '@constant/enum';
import { baseUrl_Websocket } from '@data/baseUrl';
import { colors as playgroundcolor } from "@constant/colors";

const ModernChatDialog = ({ open, onClose }) => {
  const [currentUserId, setCurrentUserId] = useState(null);
  const [currentUserEmail, setCurrentUserEmail] = useState(null);
  const [currentChatId, setCurrentChatId] = useState(null);
  const [messages, setMessages] = useState([]);
  const [users, setUsers] = useState([]);
  const [messageInput, setMessageInput] = useState('');
  const [chatUserEmail, setChatUserEmail] = useState('');
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [stompClient, setStompClient] = useState(null);
  const [currentSubscription, setCurrentSubscription] = useState(null);
 
  const loginEmail = useSelector((state) => state.userManagement.userData.user_id || '');

  const messageListRef = useRef(null);

 useEffect(() => {
  if(loginEmail)
    loginUser();
  }, [loginEmail]);

  const connectWebSocket = (userId) => {
    setConnectionStatus('connecting');

    if (stompClient?.connected) {
      stompClient.deactivate();
    }

    const client = new Client({
      webSocketFactory: () => new SockJS(`${baseUrl_Websocket}/ws`),
      reconnectDelay: 5000,
      onConnect: () => {
        setConnectionStatus('connected');
        loadUsers(userId);
      },
      onDisconnect: () => setConnectionStatus('disconnected'),
      onStompError: () => setConnectionStatus('disconnected'),
      onWebSocketError: () => setConnectionStatus('disconnected'),
    });

    client.activate();
    setStompClient(client);
  };

  const subscribeToChat = (chatId) => {
    if (currentSubscription) {
      currentSubscription.unsubscribe();
    }

    const topic = `/topic/chat/${chatId}/messages`;
    const subscription = stompClient.subscribe(topic, (message) => {
      try {
        const msg = JSON.parse(message.body);
        if (msg.senderId !== currentUserId?.id) {
          displayMessage(msg, false);
        }
      } catch (error) {
        console.error('Error parsing message:', error);
      }
    });

    setCurrentSubscription(subscription);
  };

  const loginUser = async () => {
    // if (!loginEmail.trim()) return alert(CHAT_MESSAGES.EMAIL);

    try {
      const response = await fetch(`${baseUrl_Websocket}/api/users/${loginEmail}`);

      if (!response.ok) throw new Error(`${CHAT_MESSAGES.NO_USER}`);

      const user = await response.json();

      connectWebSocket(loginEmail);
    } catch (error) {
      console.error(`${CHAT_MESSAGES.ERROR_LOGGING}`, error);
      alert(`${CHAT_MESSAGES.ERROR_LOGGING}` + error.message);
    }
  };

  const loadUsers = async (userId = "") => {
    if (!userId) return;

    try {
      const response = await fetch(`${baseUrl_Websocket}/api/users`);
      if (!response.ok) throw new Error("Failed to load users");

      const allUsers = await response.json();
      console.log("All users:", allUsers);
      const otherUsers = allUsers.filter(user => user.email !== userId);
      setUsers(otherUsers);
      setCurrentUserId(allUsers.find(user => user.email === userId));
    } catch (error) {
      console.error("Error loading users:", error);
    }
  };

  const startChat = async (withUserId, withUserEmail) => {
    if (!loginEmail) return alert("Please login first");

    try {
      const response = await fetch(`${baseUrl_Websocket}/api/chats/individual?user1Id=${currentUserId?.id}&user2Id=${withUserId}`);

      if (!response.ok) throw new Error("Failed to create/load chat");

      const chat = await response.json();
      setCurrentChatId(chat.id);
      setChatUserEmail(withUserEmail);
      setMessages([]);

      if (stompClient?.connected) {
        subscribeToChat(chat.id);
      }

      loadMessages(chat.id);
    } catch (error) {
      console.error("Error starting chat:", error);
      alert("Error starting chat: " + error.message);
    }
  };

  const loadMessages = async (chatId) => {
    try {
      const response = await fetch(`${baseUrl_Websocket}/api/messages/${chatId}`);
      const msgs = await response.json();
      setMessages(msgs);
    } catch (error) {
      console.error(CHAT_MESSAGES.ERROR, error);
    }
  };

  const displayMessage = (message, isLocal) => {
    const newMessage = {
      ...message,
      timestamp: isLocal ? new Date().toISOString() : message.timestamp
    };
    setMessages(prev => [...prev, newMessage]);
  };

  const sendMessage = () => {
    if (!messageInput.trim() || !currentChatId) return;

    const messagingDto = {
      senderId: currentUserId?.id,
      content: messageInput,
      chatId: currentChatId
    };

    const localMessage = {
      senderId: currentUserId?.id,
      content: messageInput,
      timestamp: new Date().toISOString()
    };

    displayMessage(localMessage, true);
    setMessageInput("");

    if (stompClient?.connected) {
      stompClient.publish({
        destination: `/app/chat/${currentChatId}/send`,
        body: JSON.stringify(messagingDto),
      });
    } else {
      alert(CHAT_MESSAGES.WEBSOCKET_DISCONNECTED);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      sendMessage();
    }
  };

  useEffect(() => {
    if (messageListRef.current) {
      messageListRef.current.scrollTop = messageListRef.current.scrollHeight;
    }
  }, [messages]);

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'success';
      case 'connecting': return 'warning';
      default: return 'error';
    }
  };

  const getStatusText = () => {
    switch (connectionStatus) {
      case 'connected': return 'Connected';
      case 'connecting': return 'Connecting...';
      default: return 'Disconnected';
    }
  };
  
return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: { height: '90vh', maxHeight: '800px' }
      }}
    >
      <AppBar position="static" sx={{ background: playgroundcolor.gradient.primary }}>
        <Toolbar>
          <MessageIcon sx={{ mr: 2 }} />
          <Typography variant="h6" component="div" sx={{ flexGrow: 1,color: 'white' }}> 
            Chat
          </Typography>
          <Chip
            icon={<Circle sx={{ fontSize: 12, animation: connectionStatus === 'connecting' ? 'pulse 1s infinite' : 'none' }} />}
            label={getStatusText()}
            color={getStatusColor()}
            variant="outlined"
            sx={{ color: 'white', borderColor: 'rgba(255,255,255,0.5)', mr: 2 }}
          />
          <IconButton color="inherit" onClick={onClose}>
            <Close />
          </IconButton>
        </Toolbar>
      </AppBar>

      <DialogContent sx={{ p: 0, height: '100%', position: 'relative' }}>
        <Grid container sx={{ height: '100%' }}>
          {/* Fixed Sidebar */}
          <Grid 
            item 
            xs={12} 
            md={4} 
            sx={{ 
              borderRight: 1, 
              borderColor: 'divider',
              position: 'absolute',
              top: 0,
              left: 0,
              height: '100%',
              width: { xs: '100%', md: '33.333%' },
              zIndex: 1,
              backgroundColor: 'background.paper'
            }}
          >
            <Box sx={{ p: 2, height: '100%', overflow: 'auto' }}>
              {/* User Management */}
              <Card sx={{ mb: 2 }}>
                <CardContent>
                  <Box sx={{ display: 'flex' }}>
                  </Box>
                  
                  <Alert
                    severity={currentUserEmail ? "success" : "info"}
                    icon={currentUserEmail ? <Person /> : <PersonOutline />}
                    sx={{ mt: 1 }}
                  >
                    {`Logged in as: `}<br></br>
                    {currentUserId?.email ? `${currentUserId?.email}`:``}
                  </Alert>
                </CardContent>
              </Card>

              {/* Available Users */}
              <Card>
                <CardHeader
                  avatar={<People color="primary" />}
                  title="Available Users"
                  titleTypographyProps={{ variant: 'h6' }}
                />
                <CardContent sx={{ p: 0 }}>
                  {users.length === 0 ? (
                    <Box sx={{ p: 3, textAlign: 'center' }}>
                      <People sx={{ fontSize: 48, color: 'grey.300', mb: 2 }} />
                      <Typography variant="h6" color="text.secondary" gutterBottom>
                        {CHAT_MESSAGES.NO_USERS}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {CHAT_MESSAGES.CREATE_LOGIN}
                      </Typography>
                    </Box>
                  ) : (
                    <List>
                      {users.map((user, index) => (
                        <React.Fragment key={user.id}>
                          <ListItem
                            button
                            onClick={() => startChat(user.id, user.email)}
                            sx={{
                              '&:hover': {
                                backgroundColor: 'primary.light',
                                color: 'white'
                              }
                            }}
                          >
                            <ListItemAvatar>
                              <Avatar sx={{ bgcolor: 'primary.main' }}>
                                {user.email.charAt(0).toUpperCase()}
                              </Avatar>
                            </ListItemAvatar>
                            <ListItemText
                              primary={user.email}
                              secondary="Online"
                              secondaryTypographyProps={{ color: 'success.main' }}
                            />
                          </ListItem>
                          {index < users.length - 1 && <Divider />}
                        </React.Fragment>
                      ))}
                    </List>
                  )}
                </CardContent>
              </Card>
            </Box>
          </Grid>

          {/* Chat Section with left margin to account for fixed sidebar */}
          <Grid item xs={12} md={8} sx={{ ml: { xs: 0, md: '33.333%' }, height: '100%' }}>
            {currentChatId ? (
              <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                {/* Chat Header */}
                <Paper
                  elevation={1}
                  sx={{
                    p: 2,
                    background: playgroundcolor.gradient.primary,
                    color: 'white'
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)' }}>
                      {chatUserEmail.charAt(0).toUpperCase()}
                    </Avatar>
                    <Box>
                      <Typography variant="h6">{chatUserEmail}</Typography>
                      <Typography variant="body2" sx={{ opacity: 0.8 }}>
                        {CHAT_MESSAGES.ACTIVE}
                      </Typography>
                    </Box>
                  </Box>
                </Paper>
                
                {/* Messages */}
                <Box
                  ref={messageListRef}
                  sx={{
                    flexGrow: 1,
                    overflow: 'auto',
                    p: 2,
                    backgroundColor: 'grey.50'
                  }}
                >
                  {messages.map((message, index) => {
                    const isFromCurrentUser = message.senderId === currentUserId?.id || message.sender?.id === currentUserId?.id;
                    return (
                      <Box
                        key={index}
                        sx={{
                          display: 'flex',
                          justifyContent: isFromCurrentUser ? 'flex-end' : 'flex-start',
                          mb: 2
                        }}
                      >
                        <Paper
                          elevation={1}
                          sx={{
                            p: 2,
                            maxWidth: '70%',
                            backgroundColor: isFromCurrentUser ? 'primary.main' : 'white',
                            color: isFromCurrentUser ? 'white' : 'text.primary',
                            borderRadius: 2,
                            borderBottomRightRadius: isFromCurrentUser ? 0 : 2,
                            borderBottomLeftRadius: isFromCurrentUser ? 2 : 0
                          }}
                        >
                          <Typography variant="body1" gutterBottom>
                            {message.content}
                          </Typography>
                          <Typography
                            variant="caption"
                            sx={{
                              opacity: 0.7,
                              display: 'block',
                              textAlign: 'right'
                            }}
                          >
                            {new Date(message.timestamp).toLocaleTimeString()}
                          </Typography>
                        </Paper>
                      </Box>
                    );
                  })}
                </Box>
                
                {/* Message Input */}
                <Paper elevation={2} sx={{ p: 2 }}>
                  <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>
                    <TextField
                      fullWidth
                      multiline
                      maxRows={3}
                      placeholder={CHAT_MESSAGES.MESSAGE}
                      value={messageInput}
                      onChange={(e) => setMessageInput(e.target.value)}
                      onKeyPress={handleKeyPress}
                      variant="outlined"
                      size="small"
                    />
                    <IconButton
                      color="primary"
                      onClick={sendMessage}
                      sx={{
                        background: playgroundcolor.gradient.primary,
                        color: 'white',
                        '&:hover': {
                          background: playgroundcolor.gradient.primary
                        }
                      }}
                    >
                      <Send />
                    </IconButton>
                  </Box>
                </Paper>
              </Box>
            ) : (
              <Box
                sx={{
                  height: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  textAlign: 'center',
                  p: 4
                }}
              >
                <Box>
                  <MessageIcon sx={{ fontSize: 64, color: 'grey.300', mb: 3 }} />
                  <Typography variant="h4" gutterBottom color="text.secondary">
                    {CHAT_MESSAGES.WELCOME}
                  </Typography>
                  <Typography variant="body1" color="text.secondary" sx={{ maxWidth: 400 }}>
                    {CHAT_MESSAGES.CREATE_ACC}
                  </Typography>
                </Box>
              </Box>
            )}
          </Grid>
        </Grid>
      </DialogContent>
    </Dialog>
  );
};
export default ModernChatDialog;