import React, { useEffect, useState } from "react";
import {
  Autocomplete,
  Checkbox,
  Grid,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { useDispatch, useSelector } from "react-redux";
import { doAjax } from "../../Common/fetchService";
import {
  destination_BankKey,
  destination_CostCenter,
  destination_GeneralLedger,
} from "../../../destinationVariables";
import { setDropDown } from "../../../app/dropDownDataSlice";
import { setSingleBankKeyPayload } from "../../../app/bankKeyTabSlice";
import {
  setGLRequiredFields,
  setSinglegeneralLedgerPayload,
} from "../../../app/generalLedgerTabSlice";

export default function FilterFieldTypeGL(props) {
  // console.log("error at ff tab",props.errors );
  const dispatch = useDispatch();
  var keyName = props.field.fieldName
    .replaceAll("(", "")
    .replaceAll(")", "")
    .replaceAll("/", "")
    .replaceAll("-", "")
    .replaceAll(".", "")
    .replaceAll("%", "")
    .split(" ")
    .join("");
  let errorFields = useSelector((state) => state.generalLedger.errorFields);
  const valueFromPayload = useSelector(
    (state) => state.generalLedger.singleGLPayload
  );

  const getCostElementCategory = (value) => {
    // console.log("value",value.code);
    const hSuccess = (data) => {
      // console.log("value",data);
      dispatch(
        setDropDown({ keyName: "CostElementCategory", data: data.body })
      );
    };
    const hError = (error) => {
      console.log(error, "error in dojax");
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getCostElementCategory?accountType=${value?.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getRegion = () => {
    // console.log("value",value.code);
    const hSuccess = (data) => {
      // console.log("value",data);
      dispatch(setDropDown({ keyName: "Region", data: data.body }));
    };
    const hError = (error) => {
      console.log(error, "error in dojax");
    };
    doAjax(
      `/${destination_BankKey}/data/getRegionBasedOnCountry?country=${props.country}`,
      "get",
      hSuccess,
      hError
    );
  };

  useEffect(() => {
    if (props.field.fieldName === "Region") {
      getRegion();
    }
  }, []);
  useEffect(() => {
    if (
      props?.field?.visibility === "0" ||
      props?.field?.visibility === "Required"
    ) {
      dispatch(setGLRequiredFields(keyName));
    }
  }, []);

  const dropDownData = useSelector((state) => state.AllDropDown.dropDown);
  if (props.field?.fieldType === "Input") {
    return (
      <Grid item md={2}>
        {props.field.visibility === "Hidden" ? null : (
          <Stack>
            <Typography variant="body2" color="#777">
              {props.field.fieldName}
              {props.field.visibility === "Required" ||
              props.field.visibility === "0" ? (
                <span style={{ color: "red" }}>*</span>
              ) : (
                ""
              )}
            </Typography>
            <TextField
              size="small"
              // disabled
              type={props.field.dataType === "QUAN" ? "number" : ""}
              placeholder={`Enter ${props.field.fieldName}`}
              inputProps={{ maxLength: props.field.maxLength }}
              value={valueFromPayload[keyName]}
              onChange={(event) => {
                const newValue = event.target.value;
                if (newValue.length > 0 && newValue[0] === " ") {
                  dispatch(
                    setSinglegeneralLedgerPayload({
                      keyName: keyName,
                      data: newValue.trimStart(),
                    })
                  );
                } else {
                  let glUpperCase = newValue.toUpperCase();
                  dispatch(
                    setSinglegeneralLedgerPayload({
                      keyName: keyName,
                      data: glUpperCase,
                    })
                  );
                }
              }}
              error={errorFields.includes(keyName)}
              required={
                props.field.visibility === "Required" ||
                props.field.visibility === "0"
              }
            />
          </Stack>
        )}
      </Grid>
    );
  } else if (props.field?.fieldType === "Drop Down") {
    return (
      <Grid item md={2}>
        {props.field.visibility === "Hidden" ? null : (
          <Stack>
            <Typography variant="body2" color="#777">
              {props.field.fieldName}
              {props.field.visibility === "Required" ||
              props.field.visibility === "0" ? (
                <span style={{ color: "red" }}>*</span>
              ) : (
                ""
              )}
            </Typography>
            <Autocomplete
              sx={{ height: "31px" }}
              // disabled
              fullWidth
              size="small"
              value={valueFromPayload[keyName]}
              onChange={(e, value) => {
                if (props.field.fieldName === "Account Type") {
                  getCostElementCategory(value);
                }

                // if(disableArray.filter(item=>item == option.code).length == 0){
                dispatch(
                  setSinglegeneralLedgerPayload({
                    keyName: keyName,
                    data: value,
                  })
                );
                // }
              }}
              disabled={
                props.field.fieldName === "Account Type" ||
                props.field.fieldName === "Account Group"
              }
              options={dropDownData[keyName] ?? []}
              required={
                props.field.visibility === "0" ||
                props.field.visibility === "Required"
              }
              getOptionLabel={(option) => `${option?.code} - ${option?.desc}`}
              renderOption={(props, option) => (
                <li {...props}>
                  <Typography style={{ fontSize: 12 }}>
                    {option?.code} - {option?.desc}
                  </Typography>
                </li>
              )}
              renderInput={(params) => (
                <TextField
                  {...params}
                  variant="outlined"
                  placeholder={`Select ${props.field.fieldName}`}
                  error={errorFields.includes(keyName)}
                />
              )}
            />
          </Stack>
        )}
      </Grid>
    );
  } else if (props.field?.fieldType === "Radio Button") {
    return (
      <Grid item md={2}>
        <Typography variant="body2" color="#777">
          {props.field.fieldName}
          {props.field.visibility === "Required" ||
          props.field.visibility === "0" ? (
            <span style={{ color: "red" }}>*</span>
          ) : (
            ""
          )}
        </Typography>
        <Checkbox
          sx={{ padding: 0 }}
          checked={valueFromPayload[keyName] == true}
          // required={errorFields.includes(props?.keyName)}
          onChange={(e) => {
            dispatch(
              setSinglegeneralLedgerPayload({
                keyName: keyName,
                data: e.target.checked,
              })
            );
          }}
        />
      </Grid>
    );
  } else if (props.field?.fieldType === "Calendar") {
    return (
      <Grid item md={2}>
        <Stack>
          <Typography variant="body2" color="#777">
            {props.field.fieldName}
            {props.field.visibility === "Required" ||
            props.field.visibility === "0" ? (
              <span style={{ color: "red" }}>*</span>
            ) : (
              ""
            )}
          </Typography>

          <LocalizationProvider dateAdapter={AdapterDateFns}>
            {/* <DemoContainer components={["DatePicker"]}> */}
            <DatePicker
              slotProps={{ textField: { size: "small" } }}
              value={valueFromPayload[keyName]}
              maxDate={new Date(9999, 12, 31)}
              onChange={(newValue) =>
                dispatch(
                  setSinglegeneralLedgerPayload({
                    keyName: keyName,
                    data: Date.parse(newValue),
                  })
                )
              }
              required={
                props.field.visibility === "0" ||
                props.field.visibility === "Required"
              }
            />
            {/* </DemoContainer> */}
          </LocalizationProvider>
        </Stack>
      </Grid>
    );
  }
}
