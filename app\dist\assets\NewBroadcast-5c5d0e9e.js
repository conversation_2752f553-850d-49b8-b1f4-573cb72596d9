import{cB as m,bs as ne,T as i,E as se,aw as ie,B as V,t as x,b as ce,r as c,q as de,bQ as le,j as o,a as e,G as d,M as C,x as D,ar as P,C as F,I as ue,aE as pe,aD as me,z as ge,Y as he,K as Ee,N as Te,y as b}from"./index-75c1660a.js";import{d as Ie}from"./ArrowCircleLeftOutlined-f7b52d40.js";import{a as fe,d as _e}from"./SlideshowOutlined-ac670857.js";import{d as Se}from"./CloudUpload-d5d09566.js";import{S as De}from"./SingleSelectDropdown-0d30aa01.js";import{D as G}from"./DatePicker-6e8720de.js";const{VITE_CLIENT_ID:Ne}={VITE_ENV:"dev",VITE_DOCUMENT1_FORSPEEDTEST:"DOC20250515060753250",VITE_DOCUMENT2_FORSPEEDTEST:"DOC20250************",VITE_DOCUMENT3_FORSPEEDTEST:"DOC20250515060825003",VITE_CLIENT_ID:"************-45noron6i0dj5j5l5ljin32fa635ts1m.apps.googleusercontent.com",VITE_DESTINATION_ADMIN:"cw-mdg-admin-dest",VITE_DESTINATION_SERVICE_REQUEST:"cw-scp-serv-req-oauth2-dev",VITE_DESTINATION_PO:"cw-scp-purch-order-oauth2-dev",VITE_DESTINATION_INVOICE:"cw-scp-invoice-oauth2-dev",VITE_DESTINATION_DOCUMENT_MANAGEMENT:"cw-mdg-documentmanagement-dest",VITE_DESTINATION_RETURNS:"cw-scp-returns-oauth2-dev",VITE_DESTINATION_MANAGE_ACCOUNT:"cw-scp-manage-acct-oauth2-dev",VITE_DESTINATION_NOTIFICATION:"cw-scp-notification-oauth2-dev",VITE_DESTINATION_PR:"cw-scp-pr-oauth2-dev",VITE_DESTINATION_IWA:"cw-mdg-iwm-dev",VITE_DESTINATION_IWA_NPI:"cw-mdg-iwa-oauth2-dest",VITE_DESTINATION_SERVICE_ENTRY_SHEET:"cw-scp-ses-oauth2-dev",VITE_DESTINATION_PLANNING_MANAGEMENT:"cw-scp-pfm-oauth2-dev",VITE_DESTINATION_MATERIAL_MGMT:"cw-mdg-materialmanagement-dest",VITE_DESTINATION_AI:"cw-mdg-artificialintelligence-dest",VITE_DESTINATION_WEBSOCKET:"cw-mdg-notification-dest",VITE_DESTINATION_COST_CENTER:"cw-mdg-costcenter-dest",VITE_DESTINATION_PROFIT_CENTER:"cw-mdg-profitcenter-dest",VITE_DESTINATION_BANK_KEY:"cw-mdg-bankkey-dest",VITE_DESTINATION_GENERAL_LEDGER:"cw-mdg-generalledger-dest",VITE_DESTINATION_DASHBOARD:"cw-mdg-dashboard-dev",VITE_DESTINATION_SLA_MGMT:"cw-mdg-slamanagement-dest",VITE_DESTINATION_IDM:"cw-caf-idm-services",VITE_DESTINATION_ITM_JAVA_SERVICES:"ITMJavaServices",VITE_DESTINATION_IWA_NEW:"IWAApi",VITE_URL_ITM_JAVA_SERVICES:"https://cw-mdg-iwm-dev.cfapps.eu10-004.hana.ondemand.com",VITE_BASE_URL_ITM_JAVA_SERVICES:"https://cherryworkproducts-itm-java-dev.cfapps.eu10-004.hana.ondemand.com",VITE_BASE_URL_MESSAGING_SERVICES:"https://messaging.cherryworkproducts.com",VITE_BASE_URL_CRUD_SERVICES:"https://crudservicesdev.cherryworkproducts.com",VITE_BASE_URL_IWASCP_SERVICES:"https://cw-scp-authentication-dev.cfapps.eu10-004.hana.ondemand.com",VITE_URL_MATERIAL_MGMT:"https://cw-mdg-materialmanagement-dev.cfapps.eu10-004.hana.ondemand.com",VITE_URL_AI:"https://cw-mdg-artificialintelligence.cfapps.eu10-004.hana.ondemand.com",VITE_URL_DOCUMENT_MANAGEMENT:"https://cw-mdg-documentmanagement.cfapps.eu10-004.hana.ondemand.com",VITE_URL_WEBSOCKET:"https://cw-mdg-notification.cfapps.eu10-004.hana.ondemand.com",VITE_URL_COST_CENTER:"https://cw-mdg-costcenter-dev.cfapps.eu10-004.hana.ondemand.com",VITE_URL_PROFIT_CENTER:"https://cw-mdg-profitcenter-dev.cfapps.eu10-004.hana.ondemand.com",VITE_URL_BANK_KEY:"https://cw-mdg-bankkey-dev.cfapps.eu10-004.hana.ondemand.com",VITE_URL_GENERAL_LEDGER:"https://cw-mdg-generalledger-dev.cfapps.eu10-004.hana.ondemand.com",VITE_URL_ADMIN:"https://cw-mdg-admin-dev.cfapps.eu10-004.hana.ondemand.com",VITE_URL_IWA_NPI:"https://cw-mdg-authentication-dev.cfapps.eu10-004.hana.ondemand.com",VITE_URL_DASHBOARD:"https://cw-mdg-dashboard-dev.cfapps.eu10-004.hana.ondemand.com",VITE_URL_SLA_MGMT:"https://cw-mdg-slamanagement-dev.cfapps.eu10-004.hana.ondemand.com",VITE_URL_IDM:"https://cw-caf-idm-services.cfapps.eu10-004.hana.ondemand.com",VITE_URL_AUTH_TOKEN:"https://cw-mdg-materialmanagement-dev.cfapps.eu10-004.hana.ondemand.com/authenticate/token",VITE_URL_AUTH_TOKEN_CAF:"https://cw-mdg-materialmanagement-dev.cfapps.eu10-004.hana.ondemand.com/authenticate/tokenCaf",VITE_URL_IWA_NEW:"https://incture-cherrywork-dev-cw-caf-dev-cw-caf-iwa-services.cfapps.eu10-004.hana.ondemand.com",BASE_URL:"/",MODE:"production",DEV:!1,PROD:!0,SSR:!1},ye=Ne,Ae="https://www.googleapis.com/auth/youtube.upload",we=m(ne)(({theme:r})=>({padding:r.spacing(3),marginBottom:r.spacing(2),borderRadius:r.spacing(2),boxShadow:"0 2px 12px rgba(0,0,0,0.08)"})),ve=m(i)(({theme:r})=>({fontSize:"18px",fontWeight:600,color:r.palette.text.primary,marginBottom:r.spacing(2),borderBottom:`2px solid ${r.palette.primary.main}`,paddingBottom:r.spacing(1)})),R=m(se)(({theme:r})=>({"& .MuiOutlinedInput-root":{borderRadius:r.spacing(1)}})),Ce=m(ie)(({theme:r})=>({borderRadius:r.spacing(1),backgroundColor:r.palette.background.paper})),be=m(V)(({theme:r})=>({border:`2px dashed ${r.palette.divider}`,borderRadius:r.spacing(2),padding:r.spacing(3),textAlign:"center",cursor:"pointer",transition:"all 0.3s ease","&:hover":{borderColor:r.palette.primary.main,backgroundColor:r.palette.action.hover}}));m(x)(({theme:r})=>({borderRadius:r.spacing(3),padding:r.spacing(1,3),textTransform:"none",fontWeight:600}));const Pe=()=>{const r=ce(),N=new Date,O=new Date;O.setDate(N.getDate()+7);const[a,M]=c.useState({title:"",description:"",category:"",startDate:N,endDate:O,module:"",files:null,link:""}),[W,E]=c.useState(!1),[Y,B]=c.useState(!1),[z,u]=c.useState(!1),[T,k]=c.useState(!1),[Re,g]=c.useState(!1),[h,L]=c.useState([]),[y,A]=c.useState(!1),[$,I]=c.useState(!1),[j,Ve]=c.useState(""),w=de(t=>t.userManagement.userData),[H,K]=c.useState(null),{customError:f,warn:xe}=le();c.useEffect(()=>{(()=>{if(!window.google){const n=document.createElement("script");n.src="https://accounts.google.com/gsi/client",n.async=!0,n.defer=!0,document.head.appendChild(n)}})()},[]);const q=async()=>new Promise((t,n)=>{window.google?window.google.accounts.oauth2.initTokenClient({client_id:ye,scope:Ae,callback:s=>{s.error?(f("Authentication error:",s.error),n(s.error)):(K(s.access_token),t(s.access_token))}}).requestAccessToken():n("Google API not loaded")}),J=async()=>{try{return H||await q()}catch(t){throw f("Failed to get authentication token:",t),t}},p=(t,n)=>{M(s=>({...s,[t]:n})),h.includes(t)&&L(s=>s.filter(l=>l!==t))},Q=t=>{M(n=>({...n,files:t.target.files}))},X=()=>{const t=[];return["category","module","title","description"].forEach(s=>{(!a[s]||a[s].trim()==="")&&t.push(s)}),a.startDate>=a.endDate&&t.push("dateRange"),L(t),t.length===0},Z=async(t=!1)=>{const n=new FormData;a.files&&[...a.files].forEach(l=>n.append("files",l));const s={broadcastCategory:a.category,broadcastTitle:a.title,startDate:b(a.startDate).format("YYYY-MM-DD HH:mm:ss.000"),endDate:b(a.endDate).format("YYYY-MM-DD HH:mm:ss.000"),description:a.description,module:a.module,createdBy:(w==null?void 0:w.displayName)||"",createdDate:b(N).format("YYYY-MM-DD HH:mm:ss.000"),externalUrl:a.link,...t&&{status:"Draft"}};return n.append("broadcastDetails",JSON.stringify(s)),n},ee=async(t,n)=>{I(!0);const s=new FormData;s.append("file",t),n&&s.append("accessToken",n);const l=await fetch("https://cw-mdg-admin-dev.cfapps.eu10-004.hana.ondemand.com/api/videos/upload",{method:"POST",body:s});if(!l.ok){const v=await l.text();throw new Error(`Upload failed: ${l.status} ${l.statusText} - ${v}`)}return await l.json()},te=async(t=!1)=>{if(!X()){g(!0),u(!0);return}try{let n=null;if(a.category==="Videos"&&!t&&a.files&&a.files.length>0){A(!0);try{n=await J()}catch{A(!1),g(!0),u(!0);return}A(!1);const _=[...a.files].map(S=>ee(S,n));try{const S=await Promise.all(_)}catch{g(!0),u(!0);return}}else I(!0);const s=await Z(t),l=_=>{E(!1),B(!0),I(!1),r("/configCockpit/broadcastConfigurations")},v=_=>{f("Error creating broadcast:",_),g(!0),u(!0),I(!1)};Ee(`/${Te}/broadcastManagement/uploadFiles`,"postformdata",l,v,s)}catch(n){f("Error in submission process:",n),g(!0),u(!0)}},U=()=>{E(!0)},ae=()=>{te(!T)},oe=()=>a.category==="Videos"?".mp4":".jpeg,.jpg,.png",re=()=>a.category==="Videos"?"Only MP4 format supported":"Only PNG, JPEG, JPG formats supported";return o(V,{sx:{padding:2,paddingBottom:10},children:[e(d,{container:!0,sx:{borderRadius:2,marginBottom:2},children:e(d,{container:!0,children:e(d,{item:!0,md:7,style:{padding:"16px",paddingLeft:""},children:o(D,{direction:"row",children:[e(ue,{onClick:()=>r("/configCockpit/broadcastConfigurations"),color:"primary","aria-label":"upload picture",component:"label",children:e(Ie,{sx:{fontSize:"25px",color:"#000000"}})}),o(V,{children:[e(i,{variant:"h5",paddingTop:"0.3rem",fontSize:"20px",children:e("strong",{children:"New Broadcast"})}),e(i,{variant:"body2",color:"#777",fontSize:"12px",children:"This view displays the details of the New Broadcast and allows user to create new one"})]})]})})})}),o(we,{children:[e(ve,{children:"Broadcast Configuration"}),o(d,{container:!0,spacing:3,children:[o(d,{item:!0,xs:12,md:6,lg:3,children:[o(i,{variant:"subtitle2",gutterBottom:!0,children:["Broadcast Category ",e("span",{style:{color:"red"},children:"*"})]}),e(P,{fullWidth:!0,size:"small",children:o(Ce,{value:a.category,onChange:t=>p("category",t.target.value),displayEmpty:!0,error:h.includes("category"),renderValue:t=>t||e(i,{color:"text.secondary",children:"Select Category"}),children:[e(C,{value:"",children:e(i,{color:"text.secondary",children:"Select Category"})}),e(C,{value:"Announcements",children:o(D,{direction:"row",spacing:1,alignItems:"center",children:[e(fe,{color:"primary"}),e(i,{children:"Announcements"})]})}),e(C,{value:"Videos",children:o(D,{direction:"row",spacing:1,alignItems:"center",children:[e(_e,{color:"primary"}),e(i,{children:"Videos"})]})})]})})]}),o(d,{item:!0,xs:12,md:6,lg:3,children:[o(i,{variant:"subtitle2",gutterBottom:!0,children:["Module ",e("span",{style:{color:"red"},children:"*"})]}),e(P,{fullWidth:!0,size:"small",children:e(De,{options:[{code:"Material",desc:""},{code:"Cost Center",desc:""}],value:a.module,onChange:t=>p("module",t==null?void 0:t.code),placeholder:"Select Module",disabled:!1,minWidth:"100%",error:h.includes("module")})})]}),o(d,{item:!0,xs:12,md:6,lg:3,children:[o(i,{variant:"subtitle2",gutterBottom:!0,children:["Start Date ",e("span",{style:{color:"red"},children:"*"})]}),e(G,{size:"sm",placeholder:"Select Start Date",value:a.startDate,onChange:t=>p("startDate",t),format:"dd MMM yyyy",style:{width:"100%",height:"40px"}})]}),o(d,{item:!0,xs:12,md:6,lg:3,children:[o(i,{variant:"subtitle2",gutterBottom:!0,children:["End Date ",e("span",{style:{color:"red"},children:"*"})]}),e(G,{size:"sm",placeholder:"Select End Date",value:a.endDate,onChange:t=>p("endDate",t),format:"dd MMM yyyy",style:{width:"100%",height:"40px"}})]}),o(d,{item:!0,xs:12,children:[o(i,{variant:"subtitle2",gutterBottom:!0,children:["Broadcast Title ",e("span",{style:{color:"red"},children:"*"}),o(i,{component:"span",variant:"caption",color:"text.secondary",children:[" ","(Max 100 characters)"]})]}),e(R,{fullWidth:!0,placeholder:"Enter broadcast title",value:a.title,onChange:t=>p("title",t.target.value),error:h.includes("title"),inputProps:{maxLength:100},helperText:`${a.title.length}/100`})]}),o(d,{item:!0,xs:12,children:[o(i,{variant:"subtitle2",gutterBottom:!0,children:["Broadcast Description ",e("span",{style:{color:"red"},children:"*"}),o(i,{component:"span",variant:"caption",color:"text.secondary",children:[" ","(Max 300 characters)"]})]}),e(R,{fullWidth:!0,multiline:!0,rows:4,placeholder:"Enter broadcast description",value:a.description,onChange:t=>p("description",t.target.value),error:h.includes("description"),inputProps:{maxLength:300},helperText:`${a.description.length}/300`})]}),o(d,{item:!0,xs:12,md:8,children:[o(i,{variant:"subtitle2",gutterBottom:!0,children:["Upload Document",a.category==="Videos"&&e(i,{component:"span",variant:"caption",color:"primary",sx:{ml:1},children:"(Requires Google authentication)"})]}),o(be,{children:[e("input",{accept:oe(),style:{display:"none"},id:"file-upload",multiple:!0,type:"file",onChange:Q}),e("label",{htmlFor:"file-upload",children:o(D,{spacing:1,alignItems:"center",children:[e(Se,{color:"primary",sx:{fontSize:40}}),e(i,{variant:"body2",children:"Click to upload files"}),e(i,{variant:"caption",color:"text.secondary",children:re()}),a.files&&o(i,{variant:"caption",color:"primary",children:[a.files.length," file(s) selected"]})]})})]})]}),o(d,{item:!0,xs:12,md:4,children:[e(i,{variant:"subtitle2",gutterBottom:!0,children:"External URL"}),e(R,{fullWidth:!0,placeholder:"Enter URL (optional)",value:a.link,onChange:t=>p("link",t.target.value),type:"url"})]})]})]}),e(me,{sx:{position:"fixed",bottom:0,left:0,right:0,zIndex:5},elevation:2,children:o(pe,{showLabels:!0,className:"container_BottomNav",sx:{display:"flex",justifyContent:"flex-end"},children:[e(x,{size:"small",variant:"outlined",onClick:()=>{k(!1),U()},className:"btn-mr",sx:{marginRight:1},disabled:y,children:"Save As Draft"}),e(x,{size:"small",variant:"contained",onClick:()=>{k(!0),U()},disabled:y,children:y?"Authenticating...":"Publish"})]})}),e(F,{dialogState:W,closeReusableDialog:()=>E(!1),dialogTitle:"Confirm Broadcast Creation",dialogMessage:`Are you sure you want to ${T?"publish":"save as draft"} this broadcast?${a.category==="Videos"&&T?" You will need to authenticate with Google for video upload.":""}`,handleDialogConfirm:ae,handleDialogReject:()=>E(!1),showCancelButton:!0,dialogCancelText:"Cancel",dialogOkText:T?"Publish":"Save Draft",dialogSeverity:"success"}),e(ge,{openSnackBar:Y,alertMsg:"Broadcast created successfully!",handleSnackBarClose:()=>{B(!1),r("/configCockpit/broadcastManagement")}}),e(F,{dialogState:z,closeReusableDialog:()=>u(!1),dialogTitle:"Validation Error",dialogMessage:"Please fill in all required fields correctly.",handleDialogConfirm:()=>u(!1),dialogOkText:"OK",dialogSeverity:"error"}),e(he,{blurLoading:$,loaderMessage:j})]})};export{Pe as default};
