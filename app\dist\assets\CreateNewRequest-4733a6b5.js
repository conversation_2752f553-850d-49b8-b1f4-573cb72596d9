import{q as se,s as Ro,u as on,a4 as b,K as Xe,bn as yo,$ as We,cW as Gg,ux as $g,bQ as nn,r as d,up as Dl,e2 as Je,uy as V,ai as xo,Z as ve,a0 as Vt,uq as Oh,ur as or,us as pi,aT as pr,aU as Tr,ui as Er,di as qn,a_ as mo,um as sr,un as xh,uo as Fg,m as Bo,n as Go,o as $o,cB as Vn,V as un,l as $e,t as Rt,gg as Wg,h as _s,j as R,aF as Un,a as n,T as mt,uz as jg,I as xs,ar as Li,bb as Sa,B as Le,bc as Sr,bd as qr,uA as Yu,g8 as Xu,W as Lo,X as Do,a5 as hn,ay as Gn,bF as mr,d5 as zg,F as Ss,E as Ln,cc as Yg,b6 as Ul,Q as yh,b as wr,uB as Pl,aN as Lh,nE as Na,uC as wa,aa as Vu,uD as Xg,v as Bl,w as Xn,R as Ti,dm as Ur,Y as An,gs as To,z as Hn,c1 as Ra,b0 as Vi,uE as Ki,uF as Vg,dG as kl,e4 as Dh,dK as Kg,uG as Ph,uH as qh,uI as Ku,cP as Rr,cQ as jn,ag as Ei,uj as hi,pV as Jg,uJ as Ju,ut as Qg,uK as Zg,uL as ep,e as Sn,e3 as tp,x as Ao,ue as ca,uM as Co,uN as gr,g as Uh,A as kh,an as sp,f as Hh,uO as Qu,uP as Ll,uQ as op,aX as Wn,de as ko,uR as Ji,y as zn,at as np,au as rp,aC as Di,uS as Pi,b8 as _a,uT as mi,aB as qi,uU as lp,uV as ip,uW as da,uX as ap,uY as cp,uk as ws,bO as bs,bK as Ia,uZ as dp,aD as kr,bL as va,bP as Ma,bN as Hl,u_ as up,cZ as Ui,bR as P,N as hp,aW as fp,u$ as gp,pS as Bh,aE as pp,v0 as fo,v1 as Gh,C as ki,aG as Gl,dS as Tp,ul as fi,v2 as Ep,v3 as Cr,aY as Hi,a7 as $h,dB as Zu,dz as eh,v4 as mp,bg as Hr,G as Rs,b4 as Oa,v5 as Cp,v6 as Ap,bw as bp,v7 as Lr,a6 as Sp,dg as ua,aI as Fh,v8 as Br,aV as Np,uf as wp,ae as Wh,ug as Rp,uh as _p,v9 as Ci,va as Ip,vb as vp,vc as dr,vd as mn,ve as Qi,vf as Zi,vg as th,vh as ea,vi as ai,dn as Mp,vj as ta,vk as Bi,vl as xa,vm as Op,vn as jh,vo as zh,vp as Yh,vq as nr,vr as xp,dl as yp,dq as Lp,dk as sh,bl as oh,vs as yr,eo as Uo,bi as Xh,vt as $n,uu as Oo,vu as nh,vv as Ai,vw as Dp,vx as Pp,vy as Vh,bM as Kh,vz as xl,eJ as qp,vA as sa,vB as gi,vC as Up,a1 as yn,vD as Jh,vE as kp,vF as Qh,vG as rh,vH as Hp,vI as Bp,bT as Fn,a3 as ya,vJ as Gp,vK as $p,vL as Fp,aH as Wp,vM as lh,bs as hr,br as fr,ge as jp,vN as zp,b7 as Zh,vO as Yp,vP as ih,vQ as Xp,dh as Vp,bq as ah,vR as Kp,vS as Jp,dv as ci,vT as oa,S as As,vU as Qp,e6 as na,e7 as di,b1 as Zp,vV as eT,e8 as tT,e9 as sT,ea as oT,vW as ch,vX as nT,vY as rT,af as lT,vZ as iT,v_ as aT,v$ as cT,w0 as dh,ah as dT,w1 as uT,w2 as hT,bh as fT,w3 as gT,w4 as pT}from"./index-75c1660a.js";import{F as TT}from"./FilterField-a06a27ac.js";import{a as ET,b as ef,c as mT,u as CT}from"./useDisplayDataDto-9973887b.js";import{d as La,F as ha}from"./FilterChangeDropdown-6376893e.js";import{d as Ar,a as AT}from"./DeleteOutlineOutlined-fefa2376.js";import{d as fa}from"./ArrowCircleLeftOutlined-f7b52d40.js";import{S as ro}from"./SingleSelectDropdown-0d30aa01.js";import{d as bT}from"./Add-62a207fb.js";import{D as ST}from"./DatePicker-31fef6b6.js";import{u as Da}from"./useMaterialFieldConfig-3a0b0d62.js";import{d as Gr}from"./Description-1dd6e306.js";import{G as tf}from"./GenericTabs-c9119d1b.js";import sf from"./AdditionalData-43214fb2.js";import{m as of}from"./makeStyles-c2a7efc7.js";import{d as NT,a as wT}from"./AttachFile-455d956a.js";import{M as RT,a as _T,D as IT}from"./UtilDoc-7fb813ce.js";import{R as vT}from"./ReusableAttachementAndComments-682b0475.js";import{T as MT}from"./Timeline-b8b4e1ba.js";import{T as OT,a as xT,b as yT,c as LT,d as DT}from"./TimelineSeparator-6e03ad1b.js";import{d as PT,a as qT}from"./Settings-bf4ffef5.js";import{d as UT}from"./Download-ecbb28d9.js";import{a as kT,d as uh,C as HT}from"./ChangeLog-54fbad38.js";import{d as BT}from"./CloudDownload-81bb13f9.js";import{d as GT}from"./CloudUpload-d5d09566.js";import{A as $T}from"./AttachmentUploadDialog-e237fc29.js";import{a as FT,S as WT}from"./Stepper-2dbfb76b.js";import{S as jT}from"./StepButton-e06eb73a.js";import"./useChangeLogUpdate-7dd0a930.js";import"./AutoCompleteType-e8a3df80.js";import"./dayjs.min-83c0b0e0.js";import"./AdapterDayjs-d1cac0a4.js";import"./isBetween-51871e12.js";import"./dateViewRenderers-dbe02df3.js";import"./useSlotProps-da724f1f.js";import"./InputAdornment-a22e1655.js";import"./CSSTransition-8d766865.js";import"./useMediaQuery-33e0a836.js";import"./useMobilePicker-056b38fc.js";import"./DesktopDatePicker-47a97548.js";import"./useCustomDtCall-bfc3ebdd.js";import"./GenericViewGeneral-149677e8.js";import"./Edit-77a8cc20.js";import"./DeleteOutline-a8808975.js";import"./toConsumableArray-42cf6573.js";/* empty css            */import"./FileDownloadOutlined-329b8f56.js";import"./VisibilityOutlined-a5a8c4d9.js";import"./DeleteOutlined-fe5b7345.js";import"./Slider-c4e5ff46.js";import"./utilityImages-067c3dc2.js";import"./Delete-1d158507.js";import"./clsx-a965ebfb.js";const nf=()=>{const e=se(u=>u.payload.payloadData),t=se(u=>u.applicationConfig),r=se(u=>{var re;return(re=u.userManagement)==null?void 0:re.taskData}),o=Ro(),c=on(),p=new URLSearchParams(c.search).get("RequestType");return{getRequestHeaderTemplate:()=>{var y,O,K;let u={decisionTableId:null,decisionTableName:"MDG_MAT_REQUEST_HEADER_CONFIG",version:"v2",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_SCENARIO":(r==null?void 0:r.ATTRIBUTE_2)===((y=b)==null?void 0:y.FINANCE_COSTING)?(O=b)==null?void 0:O.FINANCE_COSTING:p||(e==null?void 0:e.RequestType)||((K=b)==null?void 0:K.CREATE),"MDG_CONDITIONS.MDG_MAT_REGION":(e==null?void 0:e.Region)||"US","MDG_CONDITIONS.MDG_MODULE":"Material"}],systemFilters:null,systemOrders:null,filterString:null};const re=m=>{var B,ce;if(m.statusCode===200){const f={"Header Data":((ce=(B=m==null?void 0:m.data)==null?void 0:B.result[0])==null?void 0:ce.MDG_MAT_REQUEST_HEADER_CONFIG).sort((Y,z)=>Y.MDG_MAT_SEQUENCE_NO-z.MDG_MAT_SEQUENCE_NO).map(Y=>({fieldName:Y.MDG_MAT_UI_FIELD_NAME,sequenceNo:Y.MDG_MAT_SEQUENCE_NO,fieldType:Y.MDG_MAT_FIELD_TYPE,maxLength:Y.MDG_MAT_MAX_LENGTH,value:Y.MDG_MAT_DEFAULT_VALUE,visibility:Y.MDG_MAT_VISIBILITY,jsonName:Y.MDG_MAT_JSON_FIELD_NAME}))};o(Gg({tab:"Request Header",data:f})),o($g(f))}},ae=m=>{console.log(m)};t.environment==="localhost"?Xe(`/${yo}${We.INVOKE_RULES.LOCAL}`,"post",re,ae,u):Xe(`/${yo}${We.INVOKE_RULES.PROD}`,"post",re,ae,u)}}},rf=()=>{const e=se(g=>g.paginationData),t=se(g=>g.payload.changeFieldRows),r=se(g=>g.payload.whseList),o=se(g=>g.payload.matNoList),c=se(g=>g.payload.plantList),s=se(g=>g.payload.changeFieldRowsDisplay),p=se(g=>g.payload.selectedRows),l=Ro(),{customError:u}=nn(),[re,ae]=d.useState({errorText:!1,errorTextMessage:""}),y=async(g,T)=>{var S,q,A,x,H,k,L,N,U,I,ge,te,He,M,we,de,F,qe,W,Ke,Ds,Gt,Gs,$s,ts,lo,Fs,Us,Kt,fs,Dt,rs,Jt,vs,rt,Be,It,$t,Ns,Ce,dt,ls,ks,is,ss,it,xt,Es;l(Dl(!0));let v,C;return g===((S=Je)==null?void 0:S.LOGISTIC)?(C={materialNo:(T==null?void 0:T[(q=V)==null?void 0:q.MATERIAL_NUM])||"",division:(T==null?void 0:T[(A=V)==null?void 0:A.DIVISION])||"",plant:(T==null?void 0:T[(x=V)==null?void 0:x.PLANT])||"",page:e==null?void 0:e.page,size:e==null?void 0:e.size},v=`/${ve}/${(k=(H=We)==null?void 0:H.CHG_DISPLAY_REQUESTOR)==null?void 0:k.LOGISTIC}`):g===((L=Je)==null?void 0:L.ITEM_CAT)?(C={materialNo:(T==null?void 0:T[(N=V)==null?void 0:N.MATERIAL_NUM])||"",salesOrg:(T==null?void 0:T[(U=V)==null?void 0:U.SALES_ORG])||"",distrChan:(T==null?void 0:T[(I=V)==null?void 0:I.DIST_CHNL])||"",division:(T==null?void 0:T[(ge=V)==null?void 0:ge.DIVISION])||"",page:e==null?void 0:e.page,size:e==null?void 0:e.size},v=`/${ve}/${(He=(te=We)==null?void 0:te.CHG_DISPLAY_REQUESTOR)==null?void 0:He.SALES}`):g===((M=Je)==null?void 0:M.MRP)?(C={materialNo:(T==null?void 0:T[(we=V)==null?void 0:we.MATERIAL_NUM])||"",mrpCtrler:(T==null?void 0:T[(de=V)==null?void 0:de.MRP_CTRLER])||"",plant:(T==null?void 0:T[(F=V)==null?void 0:F.PLANT])||"",division:(T==null?void 0:T[(qe=V)==null?void 0:qe.DIVISION])||"",page:e==null?void 0:e.page,size:e==null?void 0:e.size},v=`/${ve}/${(Ke=(W=We)==null?void 0:W.CHG_DISPLAY_REQUESTOR)==null?void 0:Ke.MRP}`):g===((Ds=Je)==null?void 0:Ds.UPD_DESC)?(C={materialNo:(T==null?void 0:T[(Gt=V)==null?void 0:Gt.MATERIAL_NUM])||"",division:(T==null?void 0:T[(Gs=V)==null?void 0:Gs.DIVISION])||"",plant:(T==null?void 0:T[($s=V)==null?void 0:$s.PLANT])||"",page:e==null?void 0:e.page,size:e==null?void 0:e.size},v=`/${ve}/${(lo=(ts=We)==null?void 0:ts.CHG_DISPLAY_REQUESTOR)==null?void 0:lo.DESC}`):g===((Fs=Je)==null?void 0:Fs.WARE_VIEW_2)?(C={materialNo:(T==null?void 0:T[(Us=V)==null?void 0:Us.MATERIAL_NUM])||"",whseNo:(T==null?void 0:T[(Kt=V)==null?void 0:Kt.WAREHOUSE])||"",plant:(T==null?void 0:T[(fs=V)==null?void 0:fs.PLANT])||"",division:(T==null?void 0:T[(Dt=V)==null?void 0:Dt.DIVISION])||"",page:e==null?void 0:e.page,size:e==null?void 0:e.size},v=`/${ve}/${(Jt=(rs=We)==null?void 0:rs.CHG_DISPLAY_REQUESTOR)==null?void 0:Jt.WAREHOUSE}`):g===((vs=Je)==null?void 0:vs.CHG_STAT)?(C={materialNo:(T==null?void 0:T[(rt=V)==null?void 0:rt.MATERIAL_NUM])||"",salesOrg:(T==null?void 0:T[(Be=V)==null?void 0:Be.SALES_ORG])||"",distrChan:(T==null?void 0:T[(It=V)==null?void 0:It.DIST_CHNL])||"",division:(T==null?void 0:T[($t=V)==null?void 0:$t.DIVISION])||"",page:e==null?void 0:e.page,size:e==null?void 0:e.size},v=`/${ve}/${(Ce=(Ns=We)==null?void 0:Ns.CHG_DISPLAY_REQUESTOR)==null?void 0:Ce.CHG_STATUS}`):g===((dt=Je)==null?void 0:dt.SET_DNU)&&(C={materialNo:(T==null?void 0:T[(ls=V)==null?void 0:ls.MATERIAL_NUM])||"",salesOrg:(T==null?void 0:T[(ks=V)==null?void 0:ks.SALES_ORG])||"",distrChan:(T==null?void 0:T[(is=V)==null?void 0:is.DIST_CHNL])||"",division:(T==null?void 0:T[(ss=V)==null?void 0:ss.DIVISION])||"",plant:(T==null?void 0:T[(it=V)==null?void 0:it.PLANT])||"",page:e==null?void 0:e.page,size:e==null?void 0:e.size},v=`/${ve}/${(Es=(xt=We)==null?void 0:xt.CHG_DISPLAY_REQUESTOR)==null?void 0:Es.SET_DNU}`),new Promise((Ms,Qt)=>{Xe(v,"post",Qe=>{var Nt,ms,Ks,Hs,nt,cs,Js,Ws,Zt;l(Oh(Qe==null?void 0:Qe.totalElements)),(Qe==null?void 0:Qe.totalPages)===1||(Qe==null?void 0:Qe.currentPage)+1===(Qe==null?void 0:Qe.totalPages)?(l(or(Qe==null?void 0:Qe.totalElements)),l(pi(!0))):l(or(((Qe==null?void 0:Qe.currentPage)+1)*(Qe==null?void 0:Qe.pageSize)));const Lt=g===((Nt=Je)==null?void 0:Nt.LOGISTIC)?O(Qe==null?void 0:Qe.body):g===((ms=Je)==null?void 0:ms.ITEM_CAT)?K(Qe==null?void 0:Qe.body):g===((Ks=Je)==null?void 0:Ks.MRP)?m(Qe==null?void 0:Qe.body):g===((Hs=Je)==null?void 0:Hs.UPD_DESC)?Q(Qe==null?void 0:Qe.body):g===((nt=Je)==null?void 0:nt.WARE_VIEW_2)?oe(Qe==null?void 0:Qe.body):g===((cs=Je)==null?void 0:cs.CHG_STAT)?B(Qe==null?void 0:Qe.body):g===((Js=Je)==null?void 0:Js.SET_DNU)?ce(Qe==null?void 0:Qe.body):[];if(Array.isArray(Lt))l(pr([...t,...Lt])),l(Tr({...s,[e==null?void 0:e.page]:Lt}));else if(typeof Lt=="object"&&Lt!==null){const Ue={...t};(Ws=Object==null?void 0:Object.keys(Lt))==null||Ws.forEach(Pt=>{Ue[Pt]=[...Ue[Pt]||[],...Lt[Pt]]}),l(pr(Ue)),l(Tr({...s,[e==null?void 0:e.page]:Lt}))}l(Dl(!1));let Ut;if(Array.isArray(Lt))Ut=Lt==null?void 0:Lt.map(Ue=>Ue==null?void 0:Ue.id),l(Er([...p,...Ut]));else if(typeof Lt=="object"&&Lt!==null){Ut=Object.keys(Lt).reduce((Pt,Ps)=>{var le;return Pt[Ps]=((le=Lt[Ps])==null?void 0:le.map(Ge=>Ge==null?void 0:Ge.id))||[],Pt},{});const Ue={...p};(Zt=Object==null?void 0:Object.keys(Ut))==null||Zt.forEach(Pt=>{Ue[Pt]=[...Ue[Pt]||[],...Ut[Pt]]}),l(Er(Ue))}Ms(Qe==null?void 0:Qe.body)},()=>{var Qe;l(Dl(!1)),Qt(new Error((Qe=qn)==null?void 0:Qe.ERROR_MSG))},C)})},O=g=>{const T=[];let v=1;const C=new Set;return g.forEach(S=>{S.ToLogisticdata.forEach(q=>{C.add(q.Material);const A={...q,id:mo(),slNo:v++,MatlType:(S==null?void 0:S.MatlType)||""};T.push(A)})}),l(sr([...o,...C])),T},K=g=>{const T=[];let v=1;const C=new Set;return g.forEach(S=>{S.Tosalesdata.forEach(q=>{C.add(q.Material);const A={...q,id:mo(),slNo:v++,MatlType:(S==null?void 0:S.MatlType)||""};T.push(A)})}),l(sr([...o,...C])),T},m=g=>{const T={"Basic Data":[],"Plant Data":[]};let v=1,C=1;const S=new Set,q=new Set;return g.forEach(A=>{const{Tomrpupdate:x,ToBasicdata:H,Material:k,MatlType:L}=A;S.add(k),T["Basic Data"].push({...H,id:mo(),slNo:v++,type:"Basic Data",MatlType:L}),x.forEach(N=>{q.add(N==null?void 0:N.Plant),T["Plant Data"].push({...N,id:mo(),slNo:C++,type:"Plant Data"})})}),l(sr([...o,...S])),l(xh([...c,...q])),T},B=g=>{const T={"Basic Data":[],"Plant Data":[],"Sales Data":[]};let v=1,C=1,S=1;const q=new Set;return g.forEach(A=>{const{Tosalesdata:x,ToBasicdata:H,Toplantdata:k,Material:L,MatlType:N}=A;q.add(L),T["Basic Data"].push({...H,id:mo(),slNo:v++,type:"Basic Data",MatlType:N}),k==null||k.forEach(U=>{T["Plant Data"].push({...U,id:mo(),slNo:C++,type:"Plant Data"})}),x==null||x.forEach(U=>{T["Sales Data"].push({...U,id:mo(),slNo:S++,type:"Sales Data"})})}),l(sr([...o,...q])),T},ce=g=>{const T={"Basic Data":[],"Plant Data":[],"Sales Data":[],Description:[]};let v=1,C=1,S=1,q=1;const A=new Set;return g.forEach(x=>{const{Tosalesdata:H,ToBasicdata:k,Toplantdata:L,Tomaterialdescription:N,Material:U,MatlType:I}=x;A.add(U),T["Basic Data"].push({...k,id:mo(),slNo:v++,type:"Basic Data",MatlType:I}),L==null||L.forEach(ge=>{T["Plant Data"].push({...ge,id:mo(),slNo:C++,type:"Plant Data"})}),H==null||H.forEach(ge=>{T["Sales Data"].push({...ge,id:mo(),slNo:S++,type:"Sales Data"})}),N==null||N.forEach(ge=>{T.Description.push({...ge,id:mo(),slNo:q++,type:"Description"})})}),l(sr([...o,...A])),T},oe=g=>{const T=[],v=new Set;let C=1;const S=new Set;g.forEach(A=>{A.ToWarehousedata.forEach(x=>{v.add(x.WhseNo),S.add(x.Material);const H={...x,id:mo(),slNo:C++,MatlType:(A==null?void 0:A.MatlType)||""};T.push(H)})});const q=[...v];return l(Fg(q)),l(sr([...o,...S])),T},Q=g=>{const T=[];let v=1;const C=new Set;return g.forEach(S=>{S.Tomaterialdescription.forEach(q=>{C.add(q.Material);const A={...q,id:mo(),slNo:v++,MatlType:(S==null?void 0:S.MatlType)||""};T.push(A)})}),l(sr([...o,...C])),T};d.useEffect(()=>{(async()=>{if((r==null?void 0:r.length)>0){const T=await f(r);l(xo({keyName:"Unittype1",data:T}))}})()},[r]);const f=async g=>{const T={};for(const v of g){let C={whseNo:v};try{const S=await new Promise(q=>{var A,x;Xe(`/${ve}${(x=(A=We)==null?void 0:A.DEPENDENT_LOOKUPS)==null?void 0:x.UNITTYPE}`,"post",H=>{var k;H.statusCode===((k=Vt)==null?void 0:k.STATUS_200)?q(H==null?void 0:H.body):(u("Failed to fetch data"),q([]))},H=>{u(H),q([])},C)});T[v]=S}catch(S){u(S),T[v]=[]}}return T};d.useEffect(()=>{(async()=>{if((c==null?void 0:c.length)>0){const T=await Y(c);l(xo({keyName:"Spproctype",data:T}));const v=await z(c);l(xo({keyName:"MrpCtrler",data:v}))}})()},[c]);const Y=async g=>{const T={};for(const v of g){let C={plant:v};try{const S=await new Promise(q=>{var A,x;Xe(`/${ve}${(x=(A=We)==null?void 0:A.DATA)==null?void 0:x.GET_SPPROC_TYPE}`,"post",H=>{var k;H.statusCode===((k=Vt)==null?void 0:k.STATUS_200)?q(H==null?void 0:H.body):(u("Failed to fetch data"),q([]))},H=>{u(H),q([])},C)});T[v]=S}catch(S){u(S),T[v]=[]}}return T},z=async g=>{const T={};for(const v of g){let C={plant:v};try{const S=await new Promise(q=>{var A,x;Xe(`/${ve}${(x=(A=We)==null?void 0:A.DATA)==null?void 0:x.GET_MRP_CONTROLLER_BASED_ON_PLANT}`,"post",H=>{var k;H.statusCode===((k=Vt)==null?void 0:k.STATUS_200)?q(H==null?void 0:H.body):(u("Failed to fetch data"),q([]))},H=>{u(H),q([])},C)});T[v]=S}catch(S){u(S),T[v]=[]}}return T};return{fetchDisplayDataRequestor:y,errorState:re}};var Pa={},zT=Go;Object.defineProperty(Pa,"__esModule",{value:!0});var ga=Pa.default=void 0,YT=zT(Bo()),XT=$o;ga=Pa.default=(0,YT.default)((0,XT.jsx)("path",{d:"M11 16h2v2h-2zm1-14C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8m0-14c-2.21 0-4 1.79-4 4h2c0-1.1.9-2 2-2s2 .9 2 2c0 2-3 1.75-3 5h2c0-2.25 3-2.5 3-5 0-2.21-1.79-4-4-4"}),"HelpOutlineTwoTone");const VT=Vn(un)(({theme:e})=>{var t,r,o,c;return{"& .MuiPaper-root":{borderRadius:"12px",boxShadow:"0 4px 20px rgba(0, 0, 0, 0.1)",border:`1px solid ${(r=(t=$e)==null?void 0:t.placeholder)==null?void 0:r.color}`,backgroundColor:(c=(o=$e)==null?void 0:o.primary)==null?void 0:c.white,maxWidth:"600px"}}}),KT=Vn(Rt)(({theme:e})=>{var t,r,o,c;return{borderRadius:"8px",padding:"1.2rem 1rem !important",backgroundColor:(r=(t=$e)==null?void 0:t.primary)==null?void 0:r.lightPlus,"&:hover":{backgroundColor:(c=(o=$e)==null?void 0:o.info)==null?void 0:c.dark,boxShadow:"0 2px 8px rgba(25, 118, 210, 0.3)"},transition:"all 0.2s ease-in-out",textTransform:"none",fontWeight:500}}),hh=Vn(Wg)(({theme:e})=>{var t,r;return{borderRadius:"6px",backgroundColor:(r=(t=$e)==null?void 0:t.secondary)==null?void 0:r.lightYellow,display:"flex",alignItems:"center","& .MuiAlert-icon":{display:"flex",alignItems:"center",justifyContent:"center"},marginTop:"1rem"}}),fh=Vn(_s)({maxWidth:"none"}),lf=({onDownloadTypeChange:e,open:t,downloadType:r,handleDownloadTypeChange:o,onClose:c})=>{var p,l,u,re,ae,y;const s=r==="systemGenerated"?(p=hn)==null?void 0:p.SYSTEM_GENERATED_MSG:(l=hn)==null?void 0:l.EMAIL_DELIVERY_MSG;return R(VT,{open:t,onClose:c,children:[R(Un,{sx:{backgroundColor:(re=(u=$e)==null?void 0:u.success)==null?void 0:re.light,padding:"1rem 1.5rem",borderBottom:`1px solid ${(y=(ae=$e)==null?void 0:ae.primary)==null?void 0:y.whiteSmoke}`,display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[n(mt,{variant:"h6",sx:{fontWeight:600,color:"#333",letterSpacing:"0.2px"},children:"Select Download Option"}),n(xs,{size:"small",onClick:c,children:n(jg,{fontSize:"small"})})]}),n(Lo,{sx:{padding:"1.5rem"},children:R(Li,{component:"fieldset",sx:{width:"100%"},children:[R(Sa,{"aria-label":"download-option",name:"download-option",value:r,onChange:o,sx:{display:"flex",flexDirection:"row",gap:2,alignItems:"center"},children:[R(Le,{sx:{flex:1,padding:"0.4rem",borderRadius:"6px",backgroundColor:r==="systemGenerated"?"#f0f4ff":"#ffffff",border:r==="systemGenerated"?"1px solid #1976d2":"1px solid #e0e0e0",transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:"#f7f9fc"},display:"flex",alignItems:"center",justifyContent:"space-between"},children:[n(Sr,{value:"systemGenerated",control:n(qr,{color:"primary",size:"small"}),label:R(Le,{sx:{display:"flex",alignItems:"center",gap:.5},children:[n(Yu,{sx:{mr:1,color:"#1976d2"}}),n(mt,{sx:{fontWeight:500,color:"#333",fontSize:"0.85rem"},children:"System-Generated"})]}),sx:{flexGrow:1,margin:0}}),n(fh,{title:n("span",{style:{whiteSpace:"nowrap",fontSize:"12px"},children:"Download Excel file instantly"}),arrow:!0,placement:"top",children:n(ga,{sx:{color:"#1976d2",fontSize:"1.1rem",verticalAlign:"middle",mr:1}})})]}),R(Le,{sx:{flex:1,padding:"0.4rem",borderRadius:"8px",backgroundColor:r==="mailGenerated"?"#f0f4ff":"#ffffff",border:r==="mailGenerated"?"1px solid #1976d2":"1px solid #e0e0e0",transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:"#f7f9fc"},display:"flex",alignItems:"center",justifyContent:"space-between"},children:[n(Sr,{value:"mailGenerated",control:n(qr,{color:"primary",size:"small"}),label:R(Le,{sx:{display:"flex",alignItems:"center",gap:.5},children:[n(Xu,{sx:{mr:1,color:"#1976d2"}}),n(mt,{sx:{fontWeight:500,color:"#333",fontSize:"0.85rem"},children:"Mail-Generated"})]}),sx:{flexGrow:1,margin:0}}),n(fh,{title:n("span",{style:{whiteSpace:"nowrap",fontSize:"12px"},children:"Receive the Excel file via email"}),arrow:!0,placement:"top",children:n(ga,{sx:{color:"#1976d2",fontSize:"1.1rem",verticalAlign:"middle",mr:1}})})]})]}),n(hh,{severity:"info",children:n(mt,{sx:{fontSize:"0.9rem",color:"#555"},children:s[0]})}),n(hh,{severity:"info",children:n(mt,{sx:{fontSize:"0.9rem",color:"#555"},children:s[1]})})]})}),n(Do,{sx:{padding:"0 1.5rem 1.5rem"},children:n(KT,{variant:"contained",onClick:e,startIcon:r==="systemGenerated"?n(Yu,{}):n(Xu,{}),children:r==="systemGenerated"?"Download":"Send Email"})})]})},af=({param:e,mandatory:t=!1,dropDownData:r,allDropDownData:o,selectedValues:c,inputState:s,handleSelectAll:p,handleSelectionChange:l,handleMatInputChange:u,handleScroll:re,dropdownRef:ae,errors:y={},formatOptionLabel:O,handlePopoverOpen:K,handlePopoverClose:m,handleMouseEnterPopover:B,handleMouseLeavePopover:ce,isPopoverVisible:oe,popoverId:Q,popoverAnchorEl:f,popoverRef:Y,popoverContent:z,isMaterialNum:g=!1,isLoading:T=!1,isSelectAll:v=!1,singleSelect:C=!1,hasMoreItems:S=!0,totalCount:q=0,loadedCount:A=0})=>{const x=()=>{const L=g?(r==null?void 0:r[e==null?void 0:e.key])||[]:(r==null?void 0:r[e==null?void 0:e.key])||(o==null?void 0:o[e==null?void 0:e.key])||[];return v&&L.length>0&&!C?["Select All",...L]:L},H=()=>{if(!C)return c[e.key]||[];const L=c[e.key];return Array.isArray(L)&&L.length>0?L[0]:null},k=L=>{!S&&g||re(L)};return n(Ul,{multiple:!C,disableListWrap:!0,options:x(),getOptionLabel:L=>typeof L=="string"?L:L==="Select All"?"Select All":O(L),value:C?H():c[e.key]||[],inputValue:g&&!C?s==null?void 0:s.code:void 0,onChange:(L,N)=>{!C&&N.includes("Select All")?p(e.key,x().filter(U=>U!=="Select All")):C?l(e.key,N?[N]:[]):l(e.key,N)},disableCloseOnSelect:!C,ListboxProps:{onScroll:k,ref:ae},renderOption:(L,N,{selected:U})=>{var te;const ge=N==="Select All"?((te=c[e.key])==null?void 0:te.length)===x().filter(He=>He!=="Select All").length:U;return R("li",{...L,style:{display:"flex",alignItems:"center",width:"100%",cursor:"pointer"},children:[!C&&n(Gn,{checked:ge,sx:{marginRight:1}}),typeof N=="string"?n("span",{style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},title:N,children:N}):R("span",{style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},title:`${N==null?void 0:N.code}${N!=null&&N.desc?` - ${N==null?void 0:N.desc}`:""}`,children:[n("strong",{children:N==null?void 0:N.code}),N!=null&&N.desc?` - ${N==null?void 0:N.desc}`:""]})]})},renderTags:(L,N)=>{if(C)return null;const U=L.map(I=>typeof I=="string"?I:O(I)).join("<br />");return L.length>1?R(Ss,{children:[n(mr,{sx:{height:25,fontSize:"0.85rem",".MuiChip-label":{padding:"0 6px"}},label:`${O(L[0])}`,...N({index:0})}),n(mr,{sx:{height:25,fontSize:"0.85rem",".MuiChip-label":{padding:"0 6px"}},label:`+${L.length-1}`,onMouseEnter:I=>K(I,U),onMouseLeave:m}),n(zg,{id:Q,open:oe,anchorEl:f,onClose:m,anchorOrigin:{vertical:"bottom",horizontal:"center"},transformOrigin:{vertical:"top",horizontal:"center"},onMouseEnter:B,onMouseLeave:ce,ref:Y,sx:{"& .MuiPopover-paper":{backgroundColor:$e.primary.whiteSmoke,boxShadow:"0px 4px 8px rgba(0, 0, 0, 0.1)",borderRadius:"8px",padding:"10px",fontSize:"0.875rem",color:$e.blue.main,border:"1px solid #ddd"}},children:n(Le,{sx:{maxHeight:"270px",overflowY:"auto",padding:"5px"},dangerouslySetInnerHTML:{__html:z}})})]}):L.map((I,ge)=>n(mr,{sx:{height:25,fontSize:"0.85rem",".MuiChip-label":{padding:"0 6px"}},label:`${O(I)}`,...N({index:ge})}))},renderInput:L=>{var N,U;return n(Ln,{...L,label:t?R(Ss,{children:[R("strong",{children:["Select ",e.key]})," ",n("span",{style:{color:(U=(N=$e)==null?void 0:N.error)==null?void 0:U.dark},children:"*"})]}):`Select ${e.key}`,variant:"outlined",error:!!y[e.key],helperText:y[e.key],onChange:u||void 0,InputProps:{...L.InputProps,endAdornment:R(Ss,{children:[T?n(Yg,{size:20,sx:{mr:1}}):null,g&&q>0&&R(Le,{component:"span",sx:{mr:1,fontSize:"0.75rem",color:"text.secondary"},children:[A,"/",q]}),L.InputProps.endAdornment]})},sx:{"& .MuiOutlinedInput-root":{borderRadius:"8px",height:50,boxShadow:"0 2px 4px rgba(0, 0, 0, 0.1)"},"& .MuiInputLabel-root":{fontWeight:500}}})}},e.key)},JT=d.forwardRef(function(t,r){return n(yh,{direction:"down",ref:r,...t})}),QT=({open:e,onClose:t,parameters:r,templateName:o,setShowTable:c,allDropDownData:s})=>{var Pe,wt,kt,qs,Ft,ee,ye;const[p,l]=d.useState({}),[u,re]=d.useState({}),[ae,y]=d.useState({}),[O,K]=d.useState(""),[m,B]=d.useState(!1),[ce,oe]=d.useState("success"),[Q,f]=d.useState(!1),[Y,z]=d.useState(""),[g,T]=d.useState(""),[v,C]=d.useState(!1),[S,q]=d.useState("systemGenerated"),[A,x]=d.useState([]),[H,k]=d.useState(""),[L,N]=d.useState(!1),U=se(G=>G.payload.payloadData),I=se(G=>G.request.requestHeader.requestId),ge=se(G=>G.payload.dataLoading),te=se(G=>G.request.salesOrgDTData),[He,M]=d.useState({}),[we,de]=d.useState({[V.MATERIAL_NUM]:!1,[V.PLANT]:!1,[V.SALES_ORG]:!1,[V.DIVISION]:!1,[V.DIST_CHNL]:!1,[V.WAREHOUSE]:!1,[V.STORAGE_LOC]:!1,[V.MRP_CTRLER]:!1}),[F,qe]=d.useState(0),[W,Ke]=d.useState({code:"",desc:""}),[Ds,Gt]=d.useState(null),Gs=d.useRef(null),[$s,ts]=d.useState(!1),[lo,Fs]=d.useState(null),[Us,Kt]=d.useState(""),[fs,Dt]=d.useState(!1),rs=d.useRef(null),Jt=wr(),vs=Ro(),{fetchDisplayDataRequestor:rt}=rf(),[Be,It]=d.useState(0),[$t,Ns]=d.useState(null),[Ce,dt]=d.useState([]),[ls,ks]=d.useState(0),[is,ss]=d.useState(0),it=d.useRef(),xt=()=>{var G;(G=it==null?void 0:it.current)==null||G.click()},Es=(Pe=Pl[U==null?void 0:U.TemplateName])==null?void 0:Pe.map(G=>({field:G.key,headerName:G.key,editable:!0,flex:2})),Ms=200,Qt=d.useCallback(G=>{G.preventDefault();const Te=(G.clipboardData||window.clipboardData).getData("Text").trim().split(`
`).map((je,ie)=>{const ut=je.split("	"),Ye={id:ie+1};return Es.forEach((lt,pt)=>{Ye[lt.field]=ut[pt]||""}),Ye});dt(Te)},[]);d.useEffect(()=>{if(Be===1)return document.addEventListener("paste",Qt),()=>{document.removeEventListener("paste",Qt)}},[Be,Qt]);const as=(G,ue)=>{It(ue),Be===1&&Ns("handlePasteMaterialData")};`& .${Na.tooltip}`+"";const io={convertJsonToExcel:()=>{let G=[];Es==null||Es.forEach(ue=>{ue.headerName.toLowerCase()!=="action"&&!ue.hide&&G.push({header:ue.headerName,key:ue.field})}),Ra({fileName:"Material Data",columns:G,rows:Ce}),ss(1)}},Qe=(G,ue)=>{Fs(G.currentTarget),Kt(ue),Dt(!0)},Lt=()=>{Dt(!1)},Ut=()=>{Dt(!0)},Nt=()=>{Dt(!1)},Ks=!!lo?"custom-popover":void 0,Hs=(G,ue)=>{l(w=>({...w,[G]:ue})),ue.length>0&&y(w=>({...w,[G]:""}))};d.useEffect(()=>{re(cs(p)),vs(wa(cs(p)))},[p]),d.useEffect(()=>{if(Ce){let G=Js(Ce);l(G)}},[Ce]);const nt=(G,ue)=>{var Te;const w=((Te=p[G])==null?void 0:Te.length)===ue.length;l(je=>({...je,[G]:w?[]:ue})),w||y(je=>({...je,[G]:""}))},cs=G=>{const ue={};for(const w in G)G.hasOwnProperty(w)&&(ue[w]=G[w].map(Te=>Te.code).join(","));return ue},Js=G=>{const ue={};return G.forEach(w=>{Object.keys(w).forEach(Te=>{Te!=="id"&&w[Te].trim()!==""&&(ue[Te]||(ue[Te]=[]),ue[Te].push({code:w[Te].trim()}))})}),ue},Ws=G=>{const ue=Ki[G]||[],w=ue==null?void 0:ue.filter(Te=>!u[Te]||u[Te].trim()==="");return w.length>0?(N(!0),k(hn.MANDATORY_FILTER_MD(w.join(", "))),!1):!0},Zt=async()=>{if(Ws(o))try{const G=await rt(o,u);G&&G.length>0?(N(!1),c(!0)):(N(!0),k("No data found for the selected criteria."))}catch{N(!0),k("Error fetching data.")}},Ue=()=>{var je,ie,ut;T("Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience."),K(!0),t();let G=((je=Pl[U==null?void 0:U.TemplateName])==null?void 0:je.map(Ye=>Ye.key))||[],ue={};Be===0?ue={materialDetails:[G.reduce((Ye,lt)=>(Ye[lt]=u!=null&&u[lt]?u==null?void 0:u[lt]:"",Ye),{})],templateHeaders:U!=null&&U.FieldName?(ie=U.FieldName)==null?void 0:ie.join("$^$"):"",requestId:I||(U==null?void 0:U.RequestId)||"",templateName:U!=null&&U.TemplateName?U.TemplateName:"",dtName:"MDG_MAT_CHANGE_TEMPLATE",version:"v5",rolePrefix:""}:ue={materialDetails:[G.reduce((Ye,lt)=>(Ye[lt]=Ce.map(pt=>{var _e;return(_e=pt[lt])==null?void 0:_e.trim()}).filter(pt=>pt!=="").join(",")||"",Ye),{})],templateHeaders:U!=null&&U.FieldName?(ut=U.FieldName)==null?void 0:ut.join("$^$"):"",requestId:I||(U==null?void 0:U.RequestId)||"",templateName:U!=null&&U.TemplateName?U.TemplateName:"",dtName:"MDG_MAT_CHANGE_TEMPLATE",version:"v5",rolePrefix:""};const w=Ye=>{var _e;if((Ye==null?void 0:Ye.size)==0){K(!1),T(""),Vi((_e=hn)==null?void 0:_e.NO_DATA_FOUND,"error",{position:"top-center",largeWidth:!0}),setTimeout(()=>{var Tt;Jt((Tt=To)==null?void 0:Tt.REQUEST_BENCH)},2600);return}const lt=URL.createObjectURL(Ye),pt=document.createElement("a");pt.href=lt,pt.setAttribute("download",`${U.TemplateName}_Mass Change.xlsx`),document.body.appendChild(pt),pt.click(),document.body.removeChild(pt),URL.revokeObjectURL(lt),K(!1),T(""),B(!0),z(`${U.TemplateName}_Mass Change.xlsx has been downloaded successfully.`),oe("success"),Ps(),setTimeout(()=>{var Tt;Jt((Tt=To)==null?void 0:Tt.REQUEST_BENCH)},2600)},Te=()=>{var Ye;K(!1),T(""),Vi((Ye=hn)==null?void 0:Ye.ERR_DOWNLOADING_EXCEL,"error",{position:"top-center"}),setTimeout(()=>{var lt;Jt((lt=To)==null?void 0:lt.REQUEST_BENCH)},2600)};Xe(`/${ve}/excel/downloadExcelWithData`,"postandgetblob",w,Te,ue)},Pt=()=>{var je,ie,ut;K(!0),t();let G=((je=Pl[U==null?void 0:U.TemplateName])==null?void 0:je.map(Ye=>Ye.key))||[],ue={};Be===0?ue={materialDetails:[G.reduce((Ye,lt)=>(Ye[lt]=u!=null&&u[lt]?u==null?void 0:u[lt]:"",Ye),{})],templateHeaders:U!=null&&U.FieldName?(ie=U.FieldName)==null?void 0:ie.join("$^$"):"",requestId:I||(U==null?void 0:U.RequestId)||"",templateName:U!=null&&U.TemplateName?U.TemplateName:"",dtName:"MDG_MAT_CHANGE_TEMPLATE",version:"v4",rolePrefix:""}:ue={materialDetails:[G.reduce((Ye,lt)=>(Ye[lt]=Ce.map(pt=>{var _e;return(_e=pt[lt])==null?void 0:_e.trim()}).filter(pt=>pt!=="").join(",")||"",Ye),{})],templateHeaders:U!=null&&U.FieldName?(ut=U.FieldName)==null?void 0:ut.join("$^$"):"",requestId:I||(U==null?void 0:U.RequestId)||"",templateName:U!=null&&U.TemplateName?U.TemplateName:"",dtName:"MDG_MAT_CHANGE_TEMPLATE",version:"v4",rolePrefix:""};const w=()=>{K(!1),T(""),B(!0),z("Download has been started. You will get the Excel file via email."),oe("success"),Ps(),setTimeout(()=>{var Ye;Jt((Ye=To)==null?void 0:Ye.REQUEST_BENCH)},2600)},Te=()=>{K(!1),B(!0),z("Oops! Something went wrong. Please try again later."),oe("danger"),Ps(),setTimeout(()=>{var Ye;Jt((Ye=To)==null?void 0:Ye.REQUEST_BENCH)},2600)};Xe(`/${ve}/excel/downloadExcelWithDataInMail`,"postandgetblob",w,Te,ue)},Ps=()=>{f(!0)},le=()=>{f(!1)},Ge=()=>{C(!0)},Ae=()=>{C(!1),q("systemGenerated")},Re=G=>{var ue;q((ue=G==null?void 0:G.target)==null?void 0:ue.value)},Ve=()=>{S==="systemGenerated"&&(Ue(),Ae()),S==="mailGenerated"&&(Pt(),Ae())};d.useEffect(()=>{var w;const{[(w=V)==null?void 0:w.MATERIAL_NUM]:G,...ue}=p||{};ue&&Object.keys(ue).length>0&&(Ke({code:"",desc:""}),at("",!0))},[JSON.stringify({...p,[V.MATERIAL_NUM]:void 0})]);const ke=G=>{var Te;const ue=(Te=G.target.value)==null?void 0:Te.toUpperCase();Ke({code:ue,desc:""}),qe(0),Ds&&clearTimeout(Ds);const w=setTimeout(()=>{at(ue,!0)},500);Gt(w)},at=(G="",ue=!1)=>{var Ls,Ys,co,ds,ao,Po,Qs,Mt,Bs,Zs,_o,Fo,Io,uo,os,Wo,to,qo,jo,zo,Yo,Xo,Vo,Ko,pe,ht,ft,Ct,Oe,At,Ht,qt,Yt,us;de(Ee=>({...Ee,[V.MATERIAL_NUM]:!0}));const w=((Ys=p[(Ls=V)==null?void 0:Ls.DIVISION])==null?void 0:Ys.map(Ee=>Ee==null?void 0:Ee.code).join("$^$"))||"",Te=((ds=p[(co=V)==null?void 0:co.PLANT])==null?void 0:ds.map(Ee=>Ee==null?void 0:Ee.code).join("$^$"))||"",je=((Po=p[(ao=V)==null?void 0:ao.SALES_ORG])==null?void 0:Po.map(Ee=>Ee==null?void 0:Ee.code).join("$^$"))||((Qs=te==null?void 0:te.uniqueSalesOrgList)==null?void 0:Qs.map(Ee=>Ee.code).join("$^$"))||"",ie=((Bs=p[(Mt=V)==null?void 0:Mt.DIST_CHNL])==null?void 0:Bs.map(Ee=>Ee==null?void 0:Ee.code).join("$^$"))||"",ut=((_o=p[(Zs=V)==null?void 0:Zs.MRP_CTRLER])==null?void 0:_o.map(Ee=>Ee==null?void 0:Ee.code).join("$^$"))||"",Ye=((Io=p[(Fo=V)==null?void 0:Fo.WAREHOUSE])==null?void 0:Io.map(Ee=>Ee==null?void 0:Ee.code).join("$^$"))||"";let lt="",pt={materialNo:G??"",salesOrg:je,top:Ms,skip:ue?0:F};switch(o){case((uo=Je)==null?void 0:uo.LOGISTIC):lt=(Wo=(os=We)==null?void 0:os.MAT_SEARCH_APIS)==null?void 0:Wo.LOGISTIC,pt={...pt,division:w,plant:Te};break;case((to=Je)==null?void 0:to.MRP):lt=(jo=(qo=We)==null?void 0:qo.MAT_SEARCH_APIS)==null?void 0:jo.MRP,pt={...pt,division:w,plant:Te,mrpCtlr:ut};break;case((zo=Je)==null?void 0:zo.ITEM_CAT):lt=(Xo=(Yo=We)==null?void 0:Yo.MAT_SEARCH_APIS)==null?void 0:Xo.SALES,pt={...pt,division:w,salesOrg:je,distrChan:ie};break;case((Vo=Je)==null?void 0:Vo.WARE_VIEW_2):lt=(pe=(Ko=We)==null?void 0:Ko.MAT_SEARCH_APIS)==null?void 0:pe.WAREHOUSE,pt={...pt,division:w,plant:Te,whseNo:Ye};break;case((ht=Je)==null?void 0:ht.CHG_STAT):lt=(Ct=(ft=We)==null?void 0:ft.MAT_SEARCH_APIS)==null?void 0:Ct.CHG_STATUS,pt={...pt,division:w,salesOrg:je,distrChan:ie};break;case((Oe=Je)==null?void 0:Oe.SET_DNU):lt=(Ht=(At=We)==null?void 0:At.MAT_SEARCH_APIS)==null?void 0:Ht.SET_DNU,pt={...pt,division:w,salesOrg:je,distrChan:ie,plant:Te};break;case((qt=Je)==null?void 0:qt.UPD_DESC):lt=(us=(Yt=We)==null?void 0:Yt.MAT_SEARCH_APIS)==null?void 0:us.DESC,pt={...pt,division:w,plant:Te};break;default:return}const _e=Ee=>{(Ee==null?void 0:Ee.statusCode)===Vt.STATUS_200?((Ee==null?void 0:Ee.count)!==void 0&&ks(Ee==null?void 0:Ee.count),ue?(x(Ee==null?void 0:Ee.body),M(ct=>{var ps;return{...ct,[(ps=V)==null?void 0:ps.MATERIAL_NUM]:Ee.body}}),qe(0)):(x(ct=>[...ct,...Ee==null?void 0:Ee.body]),M(ct=>{var ps,Ts;return{...ct,[(ps=V)==null?void 0:ps.MATERIAL_NUM]:[...ct[(Ts=V)==null?void 0:Ts.MATERIAL_NUM]||[],...Ee.body]}}))):(Ee==null?void 0:Ee.statusCode)===Vt.STATUS_414&&(Vi(Ee==null?void 0:Ee.message,"error"),x([]),M(ct=>{var ps;return{...ct,[(ps=V)==null?void 0:ps.MATERIAL_NUM]:[]}}),ks(0)),de(ct=>({...ct,[V.MATERIAL_NUM]:!1})),ts(!1)},Tt=()=>{ts(!1),de(Ee=>({...Ee,[V.MATERIAL_NUM]:!1}))};ts(!0),Xe(`/${ve}${lt}`,"post",_e,Tt,pt)},xe=G=>{const{scrollTop:ue,scrollHeight:w,clientHeight:Te}=G.target;ue+Te>=w-10&&!$s&&!we[V.MATERIAL_NUM]&&A.length<ls&&qe(je=>je+Ms)};d.useEffect(()=>{F>0&&at(W==null?void 0:W.code,!1)},[F]),d.useEffect(()=>{r==null||r.forEach(G=>{var ue,w;G.key===((ue=V)==null?void 0:ue.SALES_ORG)?M(Te=>({...Te,[G.key]:(te==null?void 0:te.uniqueSalesOrgList)||[]})):G.key===((w=V)==null?void 0:w.PLANT)&&M(Te=>({...Te,[G.key]:(te==null?void 0:te.uniquePlantList)||[]}))})},[r]),d.useEffect(()=>{var G,ue;if(((G=te==null?void 0:te.salesOrgData)==null?void 0:G.length)>0&&!p[(ue=V)==null?void 0:ue.SALES_ORG]){M(Te=>{var je;return{...Te,[(je=V)==null?void 0:je.SALES_ORG]:(te==null?void 0:te.uniqueSalesOrgList)||[]}});const w=Vu(te==null?void 0:te.uniqueSalesOrgList,te);M(Te=>{var je;return{...Te,[(je=V)==null?void 0:je.PLANT]:w}})}},[te]),d.useEffect(()=>{var G,ue,w,Te,je,ie,ut,Ye,lt,pt;if(p[(G=V)==null?void 0:G.SALES_ORG]&&p[(ue=V)==null?void 0:ue.SALES_ORG].length===0&&(p[(w=V)==null?void 0:w.DIST_CHNL]=[],p[(Te=V)==null?void 0:Te.PLANT]=[]),o===((je=Je)==null?void 0:je.SET_DNU)&&(M(_e=>{var Tt;return{..._e,[(Tt=V)==null?void 0:Tt.PLANT]:[]}}),M(_e=>{var Tt;return{..._e,[(Tt=V)==null?void 0:Tt.DIST_CHNL]:[]}})),(o===((ie=Je)==null?void 0:ie.ITEM_CAT)||o===((ut=Je)==null?void 0:ut.CHG_STAT))&&M(_e=>{var Tt;return{..._e,[(Tt=V)==null?void 0:Tt.DIST_CHNL]:[]}}),p[(Ye=V)==null?void 0:Ye.SALES_ORG]&&p[(lt=V)==null?void 0:lt.SALES_ORG].length>0){Bt();const _e=Vu(p[(pt=V)==null?void 0:pt.SALES_ORG],te);M(Tt=>{var Ls;return{...Tt,[(Ls=V)==null?void 0:Ls.PLANT]:_e}})}},[p[(wt=V)==null?void 0:wt.SALES_ORG]]),d.useEffect(()=>{var G,ue,w,Te,je,ie,ut;if(p[(G=V)==null?void 0:G.PLANT]&&p[(ue=V)==null?void 0:ue.PLANT].length===0&&(p[(w=V)==null?void 0:w.MRP_CTRLER]=[],p[(Te=V)==null?void 0:Te.WAREHOUSE]=[],M(Ye=>{var lt;return{...Ye,[(lt=V)==null?void 0:lt.MRP_CTRLER]:[]}}),M(Ye=>{var lt;return{...Ye,[(lt=V)==null?void 0:lt.WAREHOUSE]:[]}})),p[(je=V)==null?void 0:je.PLANT]&&p[(ie=V)==null?void 0:ie.PLANT].length>0){gs();const Ye=Xg(p[(ut=V)==null?void 0:ut.PLANT],te);M(lt=>{var pt;return{...lt,[(pt=V)==null?void 0:pt.WAREHOUSE]:Ye}})}},[p[(kt=V)==null?void 0:kt.PLANT]]);const Bt=()=>{var Te,je,ie;de(ut=>({...ut,[V.DIST_CHNL]:!0}));let G={salesOrg:p[(Te=V)==null?void 0:Te.SALES_ORG]?(ie=p[(je=V)==null?void 0:je.SALES_ORG])==null?void 0:ie.map(ut=>ut==null?void 0:ut.code).join("$^$"):""};const ue=ut=>{M(Ye=>{var lt;return{...Ye,[(lt=V)==null?void 0:lt.DIST_CHNL]:ut.body}}),de(Ye=>({...Ye,[V.DIST_CHNL]:!1}))},w=ut=>{de(Ye=>({...Ye,[V.DIST_CHNL]:!1}))};Xe(`/${ve}${We.DATA.GET_DISTRCHNL}`,"post",ue,w,G)},gs=()=>{var Te,je,ie;de(ut=>({...ut,[V.MRP_CTRLER]:!0}));let G={plant:p[(Te=V)==null?void 0:Te.PLANT]?(ie=p[(je=V)==null?void 0:je.PLANT])==null?void 0:ie.map(ut=>ut==null?void 0:ut.code).join("$^$"):""};const ue=ut=>{M(Ye=>{var lt;return{...Ye,[(lt=V)==null?void 0:lt.MRP_CTRLER]:ut.body}}),de(Ye=>({...Ye,[V.MRP_CTRLER]:!1}))},w=ut=>{de(Ye=>({...Ye,[V.MRP_CTRLER]:!1}))};Xe(`/${ve}${We.DATA.GET_MRP_CONTROLLER_BASED_ON_PLANT}`,"post",ue,w,G)},vt=G=>{var w,Te,je,ie,ut,Ye,lt,pt,_e,Tt,Ls,Ys,co,ds;const ue=ao=>ao.code&&ao.desc?`${ao.code} - ${ao.desc}`:ao.code||"";if(G.key===((w=V)==null?void 0:w.MATERIAL_NUM))return n(af,{param:G,mandatory:(je=(Te=Ki)==null?void 0:Te[o])==null?void 0:je.includes(G==null?void 0:G.key),dropDownData:He,allDropDownData:s,selectedValues:p,inputState:W,handleSelectAll:nt,handleSelectionChange:Hs,handleMatInputChange:ke,handleScroll:xe,dropdownRef:Gs,errors:ae,formatOptionLabel:ue,handlePopoverOpen:Qe,handlePopoverClose:Lt,handleMouseEnterPopover:Ut,handleMouseLeavePopover:Nt,isPopoverVisible:fs,popoverId:Ks,popoverAnchorEl:lo,popoverRef:rs,popoverContent:Us,isMaterialNum:!0,isLoading:we[V.MATERIAL_NUM],singleSelect:(o===((ie=Je)==null?void 0:ie.LOGISTIC)||(U==null?void 0:U.TemplateName)===((ut=Je)==null?void 0:ut.LOGISTIC))&&(U==null?void 0:U.RequestType)===((Ye=b)==null?void 0:Ye.CHANGE),hasMoreItems:A.length<ls,totalCount:ls,loadedCount:A.length});if(G.key===((lt=V)==null?void 0:lt.PLANT)||G.key===((pt=V)==null?void 0:pt.SALES_ORG)||G.key===((_e=V)==null?void 0:_e.MRP_CTRLER)||G.key===((Tt=V)==null?void 0:Tt.DIVISION)||G.key===((Ls=V)==null?void 0:Ls.WAREHOUSE)||G.key===((Ys=V)==null?void 0:Ys.DIST_CHNL))return n(ha,{param:G,mandatory:(ds=(co=Ki)==null?void 0:co[o])==null?void 0:ds.includes(G==null?void 0:G.key),dropDownData:He,allDropDownData:s,selectedValues:p,handleSelectAll:nt,handleSelectionChange:Hs,errors:ae,formatOptionLabel:ue,handlePopoverOpen:Qe,handlePopoverClose:Lt,handleMouseEnterPopover:Ut,handleMouseLeavePopover:Nt,isPopoverVisible:fs,popoverId:Ks,popoverAnchorEl:lo,popoverRef:rs,popoverContent:Us,isMaterialNum:!1,isLoading:we[G.key],isSelectAll:!0})},ze=async G=>{const ue=G.target.files[0];if(!ue)return;const Te=(await Vg(ue)).map((je,ie)=>{const ut={id:ie+1};return Es.forEach((Ye,lt)=>{ut[Ye.field]=je[Ye.field]||""}),ut});dt(Te),G.target.value=null};return R(Ss,{children:[R(un,{open:e,TransitionComponent:JT,keepMounted:!0,onClose:()=>{},maxWidth:Be===1?"md":"sm",fullWidth:!0,children:[R(Le,{sx:{backgroundColor:"#e3f2fd",padding:"1rem 1.5rem",display:"flex",alignItems:"center"},children:[n(La,{color:"primary",sx:{marginRight:"0.5rem"}}),R(mt,{variant:"h6",component:"div",color:"primary",children:[o," Search Filter(s)"]})]}),R(Le,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[R(Bl,{value:Be,onChange:as,sx:{borderBottom:1,borderColor:"divider"},children:[n(Xn,{label:"Search Filter"}),n(Xn,{label:"Copy Material"})]}),Be===1&&R(Le,{sx:{display:"flex",gap:1,marginLeft:"auto",pr:2},children:[n(_s,{title:"Export Table",children:n(xs,{onClick:io.convertJsonToExcel,children:n(Ti,{iconName:"Download"})})}),n(_s,{title:"Upload Excel",disabled:!is,children:n(xs,{onClick:xt,children:n(Ti,{iconName:"Upload"})})}),n("input",{type:"file",accept:".xlsx, .xls",ref:it,style:{display:"none"},onChange:ze})]})]}),R(Lo,{sx:{padding:"1.5rem 1.5rem 1rem"},children:[Be===0&&n(Ss,{children:r==null?void 0:r.map(G=>n(Le,{sx:{marginBottom:"1rem"},children:vt(G)},G.key))}),Be===1&&n(Le,{children:n(Ur,{style:{height:400,width:"100%"},rows:Ce,columns:Es})}),L&&R(mt,{variant:"h6",color:(Ft=(qs=$e)==null?void 0:qs.error)==null?void 0:Ft.dark,children:["* ",H]}),n(An,{blurLoading:ge})]}),R(Do,{sx:{padding:"0.5rem 1.5rem",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[n(mt,{variant:"caption",sx:{color:"text.secondary",fontWeight:"bold"},children:"Note: Please choose other Mandatory fields before selecting Material Number"}),R(Le,{sx:{display:"flex",gap:1},children:[n(Rt,{onClick:()=>{var G,ue;if((U==null?void 0:U.RequestType)===((G=b)==null?void 0:G.CHANGE)){Jt((ue=To)==null?void 0:ue.REQUEST_BENCH),t();return}t()},color:"error",variant:"outlined",sx:{height:36,minWidth:"3.5rem",textTransform:"none",borderColor:"#cc3300",fontWeight:500},children:"Cancel"}),(U==null?void 0:U.RequestType)!==((ee=b)==null?void 0:ee.CHANGE_WITH_UPLOAD)&&n(Rt,{onClick:Zt,variant:"contained",sx:{height:36,minWidth:"3.5rem",backgroundColor:"#3B30C8",textTransform:"none",fontWeight:500,"&:hover":{backgroundColor:"#2c278f"}},children:"OK"}),(U==null?void 0:U.RequestType)===((ye=b)==null?void 0:ye.CHANGE_WITH_UPLOAD)&&n(Rt,{onClick:()=>{Ws(o)&&Ge()},variant:"contained",sx:{height:36,minWidth:"3.5rem",backgroundColor:"#3B30C8",textTransform:"none",fontWeight:500,"&:hover":{backgroundColor:"#2c278f"}},children:"Download"})]})]})]}),n(lf,{onDownloadTypeChange:Ve,open:v,downloadType:S,handleDownloadTypeChange:Re,onClose:Ae}),n(An,{blurLoading:O,loaderMessage:g}),m&&n(Hn,{openSnackBar:Q,alertMsg:Y,alertType:ce,handleSnackBarClose:le})]})},ZT=(e,t,r,o,c,s,p,l,u,re)=>{const ae=Ro();return{handleObjectChangeFieldRows:(O,K,m,B,ce)=>{var z;const oe=e[O].map(g=>(g==null?void 0:g.id)===K?{...g,[m]:B}:g);ae(pr({...e,[O]:oe}));const Q=(z=t==null?void 0:t[r==null?void 0:r.page])==null?void 0:z[O].map(g=>(g==null?void 0:g.id)===K?{...g,[m]:B}:g);ae(Tr({...t,[r==null?void 0:r.page]:{...t==null?void 0:t[r==null?void 0:r.page],[O]:Q}}));const f=kl(o,O),Y=kl(Dh,f);oe==null||oe.forEach(g=>{if((g==null?void 0:g.id)===K){const T={ObjectNo:`${g==null?void 0:g.Material}${(Y==null?void 0:Y.length)>0?`$$${g[Y[0]]}`:""}${(Y==null?void 0:Y.length)>1?`$$${g[Y[1]]}`:""}`,ChangedBy:c.emailId,ChangedOn:Kg,FieldName:ce??m,PreviousValue:u(g,m,O,Y)??"-",SAPValue:u(g,m,O,Y)??"-",CurrentValue:B??"",tableName:O};l(T);const v={RequestId:s||(re==null?void 0:re.slice(3)),changeLogId:(g==null?void 0:g.ChangeLogId)??null},C=[...p,T];Object.entries(o).forEach(([A,x])=>{const H=C.filter(k=>k.tableName===A);H.length>0&&(v[x]||(v[x]=[]),H.forEach(k=>{const{tableName:L,...N}=k;v[x].push(N)}))});const S=Object.values(o);let q={RequestId:v.RequestId,changeLogId:v.changeLogId};S.forEach(A=>{const x=Ph(v,A),{RequestId:H,changeLogId:k,...L}=x;Object.entries(L).forEach(([N,U])=>{q[N]||(q[N]={}),q[N]={...q[N],...U}})}),ae(qh(q))}})}}},Gi=()=>{const e=Ro(),t=on(),{fetchDisplayDataRows:r}=ET(),{fetchDisplayDataRequestor:o}=rf(),{createFCRows:c}=ef(),s=se(z=>{var g;return(g=z.userManagement)==null?void 0:g.taskData}),p=se(z=>z.paginationData),l=se(z=>z.payload.fcRows),u=se(z=>z.payload.changeFieldRowsDisplay),re=se(z=>z.request.materialRows),ae=se(z=>z.payload.payloadData),y=se(z=>z.payload.requestorPayload),O=new URLSearchParams(t.search),K=O.get("RequestId"),m=O.get("RequestType"),B=O.get("reqBench"),ce=t.state,{customError:oe}=nn(),Q=async(z,g)=>{var T,v;if(z===((T=Ku)==null?void 0:T.DISPLAY)){if(u[p==null?void 0:p.page]){(p==null?void 0:p.totalElements)>((p==null?void 0:p.page)+1)*(p==null?void 0:p.size)?e(or(((p==null?void 0:p.page)+1)*(p==null?void 0:p.size))):e(or(p==null?void 0:p.totalElements));return}Y()}else if(z===((v=Ku)==null?void 0:v.REQUESTOR)){if(u[p==null?void 0:p.page]){(p==null?void 0:p.totalElements)>((p==null?void 0:p.page)+1)*(p==null?void 0:p.size)?e(or(((p==null?void 0:p.page)+1)*(p==null?void 0:p.size))):e(or(p==null?void 0:p.totalElements));return}await o(ae==null?void 0:ae.TemplateName,y)}},f=async()=>{p!=null&&p.existingCreatePages.includes(p==null?void 0:p.page)||Y()},Y=()=>{var q,A,x,H;e(Dl(!0));let z={};const g=K,T=Rr(jn.CURRENT_TASK,!0,{}),v=m||(s==null?void 0:s.ATTRIBUTE_2)||(T==null?void 0:T.ATTRIBUTE_2);B?z={massCreationId:ce!=null&&ce.isBifurcated?"":v===b.CREATE||v===b.CREATE_WITH_UPLOAD?g.slice(3):"",massChildCreationId:ce!=null&&ce.isBifurcated&&(v===b.CREATE||v===b.CREATE_WITH_UPLOAD)?g.slice(3):"",massChangeId:ce!=null&&ce.isBifurcated?"":v===b.CHANGE||v===b.CHANGE_WITH_UPLOAD?g.slice(3):"",massExtendId:ce!=null&&ce.isBifurcated?"":v===b.EXTEND||v===b.EXTEND_WITH_UPLOAD?g.slice(3):"",massSchedulingId:ce!=null&&ce.isBifurcated?"":v===b.FINANCE_COSTING?g.slice(3):"",screenName:v===b.FINANCE_COSTING?"":v,dtName:v===b.FINANCE_COSTING?"":(q=Ei)==null?void 0:q.MDG_MAT_MATERIAL_FIELD_CONFIG,version:v===b.FINANCE_COSTING?"":"v2",page:p==null?void 0:p.page,size:v===b.FINANCE_COSTING?100:v===b.CHANGE||v===b.CHANGE_WITH_UPLOAD?10:50,sort:"",ApproverGroup:(s==null?void 0:s.ATTRIBUTE_5)||"",Region:"",massChildSchedulingId:ce!=null&&ce.isBifurcated&&v===b.FINANCE_COSTING?g.slice(3):"",massChildExtendId:ce!=null&&ce.isBifurcated&&(v===b.EXTEND||v===b.EXTEND_WITH_UPLOAD)?g.slice(3):"",massChildChangeId:ce!=null&&ce.isBifurcated&&(v===b.CHANGE||v===b.CHANGE_WITH_UPLOAD)?g.slice(3):""}:z={massCreationId:"",massChangeId:"",massSchedulingId:v===b.FINANCE_COSTING?g.slice(3):"",massExtendId:"",screenName:v===b.FINANCE_COSTING?"":v,dtName:v===b.FINANCE_COSTING?"":(A=Ei)==null?void 0:A.MDG_MAT_MATERIAL_FIELD_CONFIG,version:v===b.FINANCE_COSTING?"":"v2",page:p==null?void 0:p.page,size:v===b.FINANCE_COSTING?100:v===b.CHANGE||v===b.CHANGE_WITH_UPLOAD?10:50,sort:"",ApproverGroup:(s==null?void 0:s.ATTRIBUTE_5)||"",Region:"",massChildCreationId:v===b.CREATE||v===b.CREATE_WITH_UPLOAD?g.slice(3):"",massChildSchedulingId:"",massChildExtendId:v===b.EXTEND||v===b.EXTEND_WITH_UPLOAD?g.slice(3):"",massChildChangeId:v===b.CHANGE||v===b.CHANGE_WITH_UPLOAD?g.slice(3):""};const C=async k=>{var ge,te,He,M,we,de,F,qe;e(Dl(!1));const L=k.body;if(e(Oh(k==null?void 0:k.totalElements)),(k==null?void 0:k.totalPages)===1||(k==null?void 0:k.currentPage)+1===(k==null?void 0:k.totalPages)?(e(or(k==null?void 0:k.totalElements)),e(pi(!0))):e(or(((k==null?void 0:k.currentPage)+1)*(k==null?void 0:k.pageSize))),(s==null?void 0:s.ATTRIBUTE_2)===((ge=b)==null?void 0:ge.CHANGE)||(s==null?void 0:s.ATTRIBUTE_2)===((te=b)==null?void 0:te.CHANGE_WITH_UPLOAD)||m===((He=b)==null?void 0:He.CHANGE_WITH_UPLOAD)||m===((M=b)==null?void 0:M.CHANGE)){e(hi({keyName:"requestHeaderData",data:(we=L[0])==null?void 0:we.Torequestheaderdata})),r(L);return}if((s==null?void 0:s.ATTRIBUTE_2)===((de=b)==null?void 0:de.FINANCE_COSTING)||m===((F=b)==null?void 0:F.FINANCE_COSTING)){const W=await c(L);e(Jg([...l,...W])),e(Ju(p==null?void 0:p.page));return}const N=Qg(L,re);e(Zg({data:N==null?void 0:N.payload}));const U=Object.keys(N==null?void 0:N.payload).filter(W=>!isNaN(Number(W))),I={};U.forEach(W=>{I[W]=N==null?void 0:N.payload[W]}),e(ep((qe=Object.values(I))==null?void 0:qe.map(W=>W.headerData))),e(Ju(p==null?void 0:p.page))},S=k=>{oe(k)};Xe(`/${ve}/${(H=(x=We)==null?void 0:x.CHG_DISPLAY_REQUESTOR)==null?void 0:H.DISPLAY_DTO}`,"post",C,S,z)};return{getNextDisplayDataForChange:Q,getNextDisplayDataForCreate:f}};var qa={},eE=Go;Object.defineProperty(qa,"__esModule",{value:!0});var pa=qa.default=void 0,tE=eE(Bo()),sE=$o;pa=qa.default=(0,tE.default)((0,sE.jsx)("path",{d:"M22 12c0-5.52-4.48-10-10-10S2 6.48 2 12s4.48 10 10 10 10-4.48 10-10M4 12c0-4.42 3.58-8 8-8s8 3.58 8 8-3.58 8-8 8-8-3.58-8-8m12 0-4 4-1.41-1.41L12.17 13H8v-2h4.17l-1.59-1.59L12 8z"}),"ArrowCircleRightOutlined");const oE=({params:e,field:t,isFieldError:r,isFieldDisable:o,isNewRow:c,keyName:s,handleChangeValue:p,handleRemoveError:l,charCount:u,setCharCount:re,isFocused:ae,setIsFocused:y})=>{var oe;const[O,K]=d.useState(e.row[t.jsonName]||""),m=e.row.id,B=u[s]===(t==null?void 0:t.maxLength),ce=Q=>{const f=Q.target.value;K(f),p(e.row,m,t.jsonName,(f==null?void 0:f.toUpperCase())||"",t.viewName,t.fieldName,s),re(Y=>({...Y,[s]:f.length}))};return n(_s,{title:(oe=e.row[t.jsonName])==null?void 0:oe.toUpperCase(),arrow:!0,placement:"top",children:n(Ln,{fullWidth:!0,placeholder:`ENTER ${t.fieldName.toUpperCase()}`,variant:"outlined",size:"small",value:O,disabled:o||!c&&t.visibility===Wn.DISPLAY,inputProps:{maxLength:t.maxLength,style:{textTransform:"uppercase"}},InputProps:{sx:{"& .MuiOutlinedInput-notchedOutline":{borderColor:r?$e.error.dark:void 0},"&.Mui-disabled":{"& input":{WebkitTextFillColor:$e.text.primary,color:$e.text.primary}}}},onFocus:Q=>{Q.stopPropagation(),y({...ae,[s]:!0}),re(f=>({...f,[s]:Q.target.value.length})),r&&l(m,t.fieldName)},onKeyDown:Q=>Q.key===" "&&Q.stopPropagation(),onClick:Q=>Q.stopPropagation(),onChange:ce,onBlur:()=>y({...ae,[s]:!1}),helperText:ae[s]&&(B?"Max Length Reached":`${u[s]||0}/${t.maxLength}`),FormHelperTextProps:{sx:{color:B?$e.error.dark:$e.primary.darkPlus,position:"absolute",bottom:"-20px"}},sx:{"& .MuiInputBase-root":{height:"34px"},"& .MuiOutlinedInput-root":{"&.Mui-focused fieldset":{borderColor:B?$e.error.dark:""}}}})})},nE=e=>{var ls,ks,is,ss,it,xt,Es,Ms,Qt,as,io,Qe,Lt,Ut,Nt,ms,Ks,Hs,nt,cs,Js,Ws,Zt,Ue,Pt,Ps;const{customError:t}=nn(),{getNextDisplayDataForChange:r}=Gi(),o=se(le=>le.tabsData.changeFieldsDT),c=se(le=>le.payload.payloadData),s=o==null?void 0:o["Config Data"],p=se(le=>le.payload.tablesList),l=se(le=>le.payload.changeFieldRows),u=se(le=>le.payload.changeFieldRowsDisplay),re=se(le=>le.payload.changeLogData),ae=se(le=>le.payload.matNoList),y=se(le=>le.payload.newRowIds),O=se(le=>le.AllDropDown.dropDown||{}),K=se(le=>le.payload.dataLoading),m=se(le=>le.payload.errorData),B=se(le=>le.payload.selectedRows),ce=se(le=>{var Ge;return(Ge=le.request.requestHeader)==null?void 0:Ge.requestId}),oe=se(le=>le.userManagement.userData),Q=se(le=>le.userManagement.taskData),f=se(le=>le.paginationData),Y=se(le=>le.payload.templateArray),z=se(le=>le.payload.requestorPayload),[g,T]=d.useState([]),[v,C]=d.useState({}),[S,q]=d.useState({}),[A,x]=d.useState(""),[H,k]=d.useState("success"),[L,N]=d.useState(!1),[U,I]=d.useState(""),ge=se(le=>le.tabsData.dataLoading),[te,He]=d.useState({data:{},isVisible:!1}),M=Ro(),we=on(),de=new URLSearchParams(we.search),F=de.get("reqBench"),qe=de.get("RequestId"),{t:W}=Sn();let Ke=we.state;d.useEffect(()=>{l&&(g==null?void 0:g.length)===0&&T(JSON.parse(JSON.stringify(l)))},[l]);const[Ds,Gt]=d.useState(0),[Gs,$s]=d.useState(10),ts=(le,Ge)=>{Gt(isNaN(Ge)?0:Ge)},lo=le=>{const Ge=le.target.value;$s(Ge),Gt(0)},Fs=()=>{N(!0)},Us=()=>{N(!1)},Kt=()=>{const le=(l==null?void 0:l.length)>0?Object.keys(l[0]):[],Ge=le==null?void 0:le.reduce((Ve,ke)=>(Ve[ke]=ke==="id"?mo():ke==="slNo"?1:"",Ve),{}),Ae=[Ge,...l].map((Ve,ke)=>({...Ve,slNo:ke+1})),Re=[Ge,...u[f==null?void 0:f.page]||[]].map((Ve,ke)=>({...Ve,slNo:ke+1}));M(Qu([Ge==null?void 0:Ge.id,...y])),M(pr(Ae)),M(Tr({...u,[f==null?void 0:f.page]:Re})),M(Er([Ge==null?void 0:Ge.id,...B])),M(Ll(!0)),e==null||e.setCompleted([!0,!1])},fs=kl(tp,c==null?void 0:c.TemplateName),Dt=kl(Dh,fs),rs=(Dt==null?void 0:Dt.length)>1,Jt=(le,Ge)=>{const Ae=g==null?void 0:g.find(Re=>{const Ve=Re.Material===(le==null?void 0:le.Material)&&(Re==null?void 0:Re[Dt[0]])===(le==null?void 0:le[Dt[0]]);return rs?Ve&&(Re==null?void 0:Re[Dt[1]])===(le==null?void 0:le[Dt[1]]):Ve});if(Ae)return Ae[Ge]},vs=(le,Ge,Ae,Re)=>{var ke;const Ve=(ke=g==null?void 0:g[Ae])==null?void 0:ke.find(at=>{let xe=at.Material===(le==null?void 0:le.Material);return(Re==null?void 0:Re.length)>0&&(xe=xe&&(at==null?void 0:at[Re[0]])===(le==null?void 0:le[Re[0]]),(Re==null?void 0:Re.length)>1&&(xe=xe&&(at==null?void 0:at[Re[1]])===(le==null?void 0:le[Re[1]]))),xe});return Ve?Ve[Ge]:"-"},rt=le=>{M(op(le))},{handleObjectChangeFieldRows:Be}=ZT(l,u,f,fs,oe,ce,Y,rt,vs,e==null?void 0:e.RequestId),It=(le,Ge,Ae,Re,Ve,ke)=>{var at,xe,Bt,gs;if(Array.isArray(l)){if(Ae==="AltUnit"||Ae==="Langu"){const ee=lp(le,u==null?void 0:u[f==null?void 0:f.page],ae,Re,c==null?void 0:c.TemplateName);if(ee==="matError"){k("error"),I(W((at=qn)==null?void 0:at.MATL_ERROR_MSG)),Fs();return}else if(ee==="altUnitError"){k("error"),I(W((xe=qn)==null?void 0:xe.ALTUNIT_ERROR_MSG)),Fs();return}else if(ee==="languError"){k("error"),I(W((Bt=qn)==null?void 0:Bt.LANG_ERROR_MSG)),Fs();return}}const vt=l==null?void 0:l.map(ee=>{var ye,G;return(ee==null?void 0:ee.id)===Ge?{...ee,[Ae]:Re,...Ae==="Material"?{...(c==null?void 0:c.TemplateName)===((ye=Je)==null?void 0:ye.UPD_DESC)?{Langu:""}:{},...(c==null?void 0:c.TemplateName)===((G=Je)==null?void 0:G.LOGISTIC)?{AltUnit:""}:{}}:{}}:ee});M(pr(vt));const ze=(gs=u==null?void 0:u[f==null?void 0:f.page])==null?void 0:gs.map(ee=>{var ye,G;return(ee==null?void 0:ee.id)===Ge?{...ee,[Ae]:Re,...Ae==="Material"?{...(c==null?void 0:c.TemplateName)===((ye=Je)==null?void 0:ye.UPD_DESC)?{Langu:""}:{},...(c==null?void 0:c.TemplateName)===((G=Je)==null?void 0:G.LOGISTIC)?{AltUnit:""}:{}}:{}}:ee});M(Tr({...u,[f==null?void 0:f.page]:ze}));const Pe=ip(),wt=ee=>ee!=null&&ee.toString().startsWith("/Date(")&&(ee!=null&&ee.toString().endsWith(")/"))?cp(ee):ee;let kt={ObjectNo:`${le==null?void 0:le.Material}$$${le==null?void 0:le[Dt[0]]}${rs?`$$${le==null?void 0:le[Dt[1]]}`:""}`,ChangedBy:oe.emailId,ChangedOn:Pe.sapFormat,FieldName:ke??Ae,PreviousValue:Jt(le,Ae)??"-",SAPValue:Jt(le,Ae)??"-",CurrentValue:wt(Re)??""};rt(kt);let qs={RequestId:ce||(e==null?void 0:e.RequestId).slice(3),changeLogId:(le==null?void 0:le.ChangeLogId)??null,[fs]:[...Y,kt]};const Ft=Ph(qs,fs);M(qh(Ft))}else typeof l=="object"&&l[Ve]&&Be(Ve,Ge,Ae,Re,ke)},$t=(le,Ge)=>{const Ae={};Object.keys(m).forEach(Re=>{const Ve=m[Re];if(Ve.id===le){const ke=Ve.missingFields.filter(at=>at!==Ge);ke.length>0&&(Ae[Re]={...Ve,missingFields:ke})}else Ae[Re]={...Ve}}),M(da(Ae))},Ns=()=>{var Ae,Re,Ve,ke;const le=te==null?void 0:te.data,Ge=(Ae=le==null?void 0:le.row)==null?void 0:Ae.id;if(Array.isArray(l)){const xe=l.filter(ze=>(ze==null?void 0:ze.id)!==Ge).map((ze,Pe)=>({...ze,slNo:Pe+1}));M(pr(xe));const Bt={...u,[f==null?void 0:f.page]:(Ve=(Re=u[f==null?void 0:f.page])==null?void 0:Re.filter(ze=>(ze==null?void 0:ze.id)!==Ge))==null?void 0:Ve.map((ze,Pe)=>({...ze,slNo:Pe+1}))};M(Tr(Bt));const gs=y==null?void 0:y.filter(ze=>ze!==Ge);M(Qu(gs));const vt=l.find(ze=>ze.id===Ge);if(vt){const ze=`${vt.Material}$$${vt[Dt[0]]}${rs?`$$${vt[Dt[1]]}`:""}`,Pe=JSON.parse(JSON.stringify(re));if((ke=Pe[vt.Material])!=null&&ke[fs]){const wt=Pe[vt.Material][fs].filter(kt=>kt.ObjectNo!==ze&&kt.ObjectNo!==`${vt.Material}$$`);wt.length===0?(delete Pe[vt.Material][fs],Object.keys(Pe[vt.Material]).length===0&&(delete Pe[vt.Material],delete Pe[""])):Pe[vt.Material][fs]=wt}M(ap(Pe))}}He({...te,isVisible:!1})},Ce=(le,Ge)=>{var ke,at,xe,Bt,gs,vt,ze,Pe,wt,kt,qs,Ft,ee,ye,G,ue;const Ae=[{headerName:"Sl. No.",field:"slNo",align:"center",flex:(c==null?void 0:c.TemplateName)===((ke=Je)==null?void 0:ke.LOGISTIC)||(c==null?void 0:c.TemplateName)===((at=Je)==null?void 0:at.MRP)&&le==="Plant Data"?void 0:.1,width:(c==null?void 0:c.TemplateName)===((xe=Je)==null?void 0:xe.LOGISTIC)||(c==null?void 0:c.TemplateName)===((Bt=Je)==null?void 0:Bt.MRP)&&le==="Plant Data"?1:void 0},...Ge.map(w=>{var Te,je,ie,ut,Ye,lt,pt;return{headerName:R("span",{children:[w.fieldName,w.visibility===((Te=Wn)==null?void 0:Te.MANDATORY)&&n("span",{style:{color:(ie=(je=$e)==null?void 0:je.error)==null?void 0:ie.dark,marginLeft:4},children:"*"})]}),field:w.jsonName,flex:(c==null?void 0:c.TemplateName)===((ut=Je)==null?void 0:ut.LOGISTIC)||(c==null?void 0:c.TemplateName)===((Ye=Je)==null?void 0:Ye.MRP)&&(w==null?void 0:w.viewName)==="Plant Data"?void 0:1,width:(c==null?void 0:c.TemplateName)===((lt=Je)==null?void 0:lt.LOGISTIC)||(c==null?void 0:c.TemplateName)===((pt=Je)==null?void 0:pt.MRP)&&(w==null?void 0:w.viewName)==="Plant Data"?200:void 0,renderCell:_e=>{var ao,Po,Qs,Mt,Bs,Zs,_o,Fo,Io,uo,os,Wo,to,qo,jo,zo,Yo,Xo,Vo,Ko,pe,ht,ft,Ct,Oe,At,Ht,qt;const Tt=(ao=Object==null?void 0:Object.values(m))==null?void 0:ao.find(Yt=>{var us;return(Yt==null?void 0:Yt.id)===((us=_e==null?void 0:_e.row)==null?void 0:us.id)}),Ls=`${(Po=_e==null?void 0:_e.row)==null?void 0:Po.id}-${w==null?void 0:w.jsonName}`,Ys=(Qs=Tt==null?void 0:Tt.missingFields)==null?void 0:Qs.includes(w==null?void 0:w.fieldName),co=y==null?void 0:y.includes((Mt=_e==null?void 0:_e.row)==null?void 0:Mt.id),ds=!!(F&&!((Bs=ko)!=null&&Bs.includes(Ke==null?void 0:Ke.reqStatus)));if(w.fieldType===Ji.INPUT)return n(oE,{params:_e,field:w,isFieldError:Ys,isFieldDisable:ds,isNewRow:co,keyName:Ls,handleChangeValue:It,handleRemoveError:$t,charCount:S,setCharCount:q,isFocused:v,setIsFocused:C});if(w.fieldType===Ji.DROPDOWN){const Yt=y==null?void 0:y.includes((Zs=_e==null?void 0:_e.row)==null?void 0:Zs.id),us=(w==null?void 0:w.jsonName)!=="Unittype1"&&(w==null?void 0:w.jsonName)!=="Spproctype"&&(w==null?void 0:w.jsonName)!=="MrpCtrler"?(_o=O==null?void 0:O[w==null?void 0:w.jsonName])==null?void 0:_o.find(Ee=>{var ct;return Ee.code===((ct=_e==null?void 0:_e.row)==null?void 0:ct[w==null?void 0:w.jsonName])}):(w==null?void 0:w.jsonName)==="Spproctype"||(w==null?void 0:w.jsonName)==="MrpCtrler"?(uo=(Io=O==null?void 0:O[w==null?void 0:w.jsonName])==null?void 0:Io[(Fo=_e==null?void 0:_e.row)==null?void 0:Fo.Plant])==null?void 0:uo.find(Ee=>{var ct;return Ee.code===((ct=_e==null?void 0:_e.row)==null?void 0:ct[w==null?void 0:w.jsonName])}):(to=(Wo=O==null?void 0:O[w==null?void 0:w.jsonName])==null?void 0:Wo[(os=_e==null?void 0:_e.row)==null?void 0:os.WhseNo])==null?void 0:to.find(Ee=>{var ct;return Ee.code===((ct=_e==null?void 0:_e.row)==null?void 0:ct[w==null?void 0:w.jsonName])});return n(ro,{options:(w==null?void 0:w.jsonName)==="Unittype1"?(jo=O==null?void 0:O.Unittype1)==null?void 0:jo[(qo=_e==null?void 0:_e.row)==null?void 0:qo.WhseNo]:(w==null?void 0:w.jsonName)==="Spproctype"?(Yo=O==null?void 0:O.Spproctype)==null?void 0:Yo[(zo=_e==null?void 0:_e.row)==null?void 0:zo.Plant]:(w==null?void 0:w.jsonName)==="MrpCtrler"?(Vo=O==null?void 0:O.MrpCtrler)==null?void 0:Vo[(Xo=_e==null?void 0:_e.row)==null?void 0:Xo.Plant]:O!=null&&O[w==null?void 0:w.jsonName]?O==null?void 0:O[w==null?void 0:w.jsonName]:[],value:us||((Ko=_e==null?void 0:_e.row)!=null&&Ko[w==null?void 0:w.jsonName]?{code:(pe=_e==null?void 0:_e.row)==null?void 0:pe[w==null?void 0:w.jsonName],desc:""}:null),onChange:Ee=>{It(_e.row,_e.row.id,w==null?void 0:w.jsonName,Ee==null?void 0:Ee.code,le,w==null?void 0:w.fieldName),Ys&&$t(_e.row.id,w==null?void 0:w.fieldName)},listWidth:150,placeholder:`Select ${w.fieldName}`,disabled:ds?!0:Yt?!1:(w==null?void 0:w.visibility)===((ht=Wn)==null?void 0:ht.DISPLAY),isFieldError:Ys})}else if(w.fieldType===Ji.DATE_FIELD){const Yt=y==null?void 0:y.includes((ft=_e==null?void 0:_e.row)==null?void 0:ft.id),us=(Ct=_e==null?void 0:_e.row)!=null&&Ct[w==null?void 0:w.jsonName]?(()=>{var ct;const Ee=(ct=_e==null?void 0:_e.row)==null?void 0:ct[w==null?void 0:w.jsonName];if(Ee.startsWith("/Date(")&&Ee.endsWith(")/")){const ps=parseInt(Ee.slice(6,-2));return new Date(ps)}return typeof Ee=="string"&&Ee.match(/^\d{4}-\d{2}-\d{2}/)?new Date(Ee):zn(Ee,["YYYY-MM-DD HH:mm:ss.S","DD MMM YYYY HH:mm:ss UTC"]).toDate()})():null;return n(_s,{title:(Oe=_e==null?void 0:_e.row)==null?void 0:Oe[w==null?void 0:w.jsonName],arrow:!0,placement:"top",children:n(np,{dateAdapter:rp,children:n(ST,{disabled:ds?!0:Yt?!1:(w==null?void 0:w.visibility)===((At=Wn)==null?void 0:At.DISPLAY),slotProps:{textField:{size:"small",fullWidth:!0,InputProps:{sx:{"& .MuiOutlinedInput-notchedOutline":{borderColor:Ys?(qt=(Ht=$e)==null?void 0:Ht.error)==null?void 0:qt.dark:void 0}}}}},value:us,onChange:Ee=>{if(Ee){const ct=`/Date(${Date.parse(Ee)})/`;It(_e.row,_e.row.id,w==null?void 0:w.jsonName,ct,le,w==null?void 0:w.fieldName)}else It(_e.row,_e.row.id,w==null?void 0:w.jsonName,null,le,w==null?void 0:w.fieldName);Ys&&$t(_e.row.id,w==null?void 0:w.fieldName)},onError:Ee=>{Ee&&!Ys&&t(hn.DATE_VALIDATION_ERROR,Ee)},maxDate:new Date(9999,11,31)})})})}else return _e.value||"-"}}}),{...(((c==null?void 0:c.TemplateName)===((gs=Je)==null?void 0:gs.LOGISTIC)||(c==null?void 0:c.TemplateName)===((vt=Je)==null?void 0:vt.UPD_DESC))&&!qe||((c==null?void 0:c.TemplateName)===((ze=Je)==null?void 0:ze.LOGISTIC)||(c==null?void 0:c.TemplateName)===((Pe=Je)==null?void 0:Pe.UPD_DESC))&&qe&&((Q==null?void 0:Q.taskDesc)===((wt=ca)==null?void 0:wt.REQUESTOR)||(Ke==null?void 0:Ke.reqStatus)===((kt=Co)==null?void 0:kt.DRAFT)))&&{field:"action",headerName:"Action",flex:(c==null?void 0:c.TemplateName)===((qs=Je)==null?void 0:qs.LOGISTIC)||(c==null?void 0:c.TemplateName)===((Ft=Je)==null?void 0:Ft.MRP)&&(field==null?void 0:field.viewName)==="Plant Data"?void 0:1,width:(c==null?void 0:c.TemplateName)===((ee=Je)==null?void 0:ee.LOGISTIC)||(c==null?void 0:c.TemplateName)===((ye=Je)==null?void 0:ye.MRP)&&(field==null?void 0:field.viewName)==="Plant Data"?200:void 0,align:"center",headerAlign:"center",renderCell:w=>{var Te;return n(Ao,{direction:"row",alignItems:"center",sx:{marginLeft:"0.5rem",magrinRight:"0.5rem"},spacing:.5,children:n(_s,{title:"Delete Row",children:n(xs,{disabled:!(y!=null&&y.includes((Te=w==null?void 0:w.row)==null?void 0:Te.id)),onClick:()=>{He({data:w,isVisible:!0})},color:"error",children:n(Ar,{})})})})}}}],Re=Array.isArray(l)?(u==null?void 0:u[f==null?void 0:f.page])||[]:((G=u==null?void 0:u[f==null?void 0:f.page])==null?void 0:G[le])||[],Ve=Array.isArray(B)?B:B[le];return R("div",{style:{height:400,width:"100%"},children:[n(Di,{paginationLoading:K,rows:Re,rowCount:(Re==null?void 0:Re.length)??0,columns:Ae,getRowIdValue:"id",rowHeight:70,isLoading:K,tempheight:"calc(100vh - 380px)",page:Ds,pageSize:Gs,selectionModel:Ve,onPageChange:ts,onPageSizeChange:lo,onCellEditCommit:It,checkboxSelection:!(F&&!((ue=ko)!=null&&ue.includes(Ke==null?void 0:Ke.reqStatus))),disableSelectionOnClick:!0,showCustomNavigation:!0,hideFooter:!0}),(te==null?void 0:te.isVisible)&&R(Pi,{isOpen:te==null?void 0:te.isVisible,titleIcon:n(Ar,{size:"small",color:"error",sx:{fontSize:"20px"}}),Title:W("Delete Row")+"!",handleClose:()=>He({...te,isVisible:!1}),children:[n(Lo,{sx:{mt:2},children:W(qn.DELETE_MESSAGE)}),R(Do,{children:[n(Rt,{variant:"outlined",size:"small",sx:{..._a},onClick:()=>He({...te,isVisible:!1}),children:W(mi.CANCEL)}),n(Rt,{variant:"contained",size:"small",sx:{...qi},onClick:Ns,children:W(mi.DELETE)})]})]})]})},dt=s&&Object.keys(s);if(d.useEffect(()=>{var le,Ge,Ae;(f==null?void 0:f.page)+1&&((c==null?void 0:c.RequestType)===((le=b)==null?void 0:le.CHANGE)||(c==null?void 0:c.RequestType)===((Ge=b)==null?void 0:Ge.CHANGE_WITH_UPLOAD))&&(qe&&(!z||((Ae=Object==null?void 0:Object.keys(z))==null?void 0:Ae.length)===0)?(r("display"),Gt(0)):(r("requestor"),Gt(0)),A==="prev"?M(pi(!1)):A==="next"&&(f==null?void 0:f.currentElements)>=(f==null?void 0:f.totalElements)&&M(pi(!0)))},[f==null?void 0:f.page]),(dt==null?void 0:dt.length)===1){const le=dt[0],Ge=s[le];return R(Ao,{children:[R(Ao,{direction:"row",justifyContent:"space-between",mb:1.5,children:[((c==null?void 0:c.TemplateName)===((ls=Je)==null?void 0:ls.LOGISTIC)||(c==null?void 0:c.TemplateName)===((ks=Je)==null?void 0:ks.UPD_DESC))&&!qe||((c==null?void 0:c.TemplateName)===((is=Je)==null?void 0:is.LOGISTIC)||(c==null?void 0:c.TemplateName)===((ss=Je)==null?void 0:ss.UPD_DESC))&&qe&&((Q==null?void 0:Q.taskDesc)===((it=ca)==null?void 0:it.REQUESTOR)||(Ke==null?void 0:Ke.reqStatus)===((xt=Co)==null?void 0:xt.DRAFT))?n(Rt,{variant:"contained",color:"primary",onClick:Kt,startIcon:n(bT,{}),sx:{borderRadius:"10px",boxShadow:"0px 4px 8px rgba(0, 0, 0, 0.15)"},children:"Add Row"}):n(Le,{sx:{width:0,height:0}}),R(Le,{sx:{display:"flex",justifyContent:"center",alignItems:"center",backgroundImage:"linear-gradient(180deg,rgb(242, 241, 255) 0%,rgb(255, 255, 255) 100%)",border:"1px solid #E0E0E0",padding:"5px",borderRadius:"10px",mt:-1,boxShadow:"0px 2px 10px rgba(0, 0, 0, 0.08)"},children:[n(_s,{title:"Previous",placement:"top",arrow:!0,children:n(xs,{disabled:(f==null?void 0:f.page)===0||!1,onClick:()=>{x("prev"),M(gr((f==null?void 0:f.page)-1))},children:n(fa,{sx:{color:(f==null?void 0:f.page)===0?(Ms=(Es=$e)==null?void 0:Es.secondary)==null?void 0:Ms.grey:(as=(Qt=$e)==null?void 0:Qt.primary)==null?void 0:as.main,fontSize:"1.5rem",marginRight:"2px"}})})}),R("span",{style:{marginRight:"2px"},children:[n("strong",{style:{color:(Qe=(io=$e)==null?void 0:io.primary)==null?void 0:Qe.main},children:"Materials :"})," ",R("strong",{children:[(f==null?void 0:f.page)*(f==null?void 0:f.size)+1," -"," ",f==null?void 0:f.currentElements]})," ",n("span",{children:"of"})," ",n("strong",{children:f==null?void 0:f.totalElements})]}),n(_s,{title:"Next",placement:"top",arrow:!0,children:n(xs,{disabled:(f==null?void 0:f.currentElements)>=(f==null?void 0:f.totalElements)||!1,onClick:()=>{x("next"),M(gr((f==null?void 0:f.page)+1))},children:n(pa,{sx:{color:(f==null?void 0:f.currentElements)>=(f==null?void 0:f.totalElements)?(Ut=(Lt=$e)==null?void 0:Lt.secondary)==null?void 0:Ut.grey:(ms=(Nt=$e)==null?void 0:Nt.primary)==null?void 0:ms.main,fontSize:"1.5rem"}})})})]})]}),n("div",{children:Ce(le,Ge)}),n(Hn,{openSnackBar:L,alertMsg:U,alertType:H,handleSnackBarClose:Us})]})}return R(Ss,{children:[n(An,{blurLoading:ge}),!ge&&n(Ss,{children:s?R("div",{children:[R(Le,{sx:{display:"flex",justifyContent:"center",alignItems:"center",backgroundImage:"linear-gradient(180deg, rgb(242, 241, 255) 0%, rgb(255, 255, 255) 100%)",border:"1px solid #E0E0E0",borderRadius:"10px",padding:"5px",width:"fit-content",marginLeft:"auto",mt:-1,mb:2,boxShadow:"0px 2px 10px rgba(0, 0, 0, 0.08)"},children:[n(_s,{title:"Previous",placement:"top",arrow:!0,children:n(xs,{disabled:(f==null?void 0:f.page)===0||!1,onClick:()=>{M(gr((f==null?void 0:f.page)-1))},children:n(fa,{sx:{color:(f==null?void 0:f.page)===0?(Hs=(Ks=$e)==null?void 0:Ks.secondary)==null?void 0:Hs.grey:(cs=(nt=$e)==null?void 0:nt.primary)==null?void 0:cs.main,fontSize:"1.5rem",marginRight:"2px"}})})}),R("span",{style:{marginRight:"2px"},children:[n("strong",{style:{color:(Ws=(Js=$e)==null?void 0:Js.primary)==null?void 0:Ws.main},children:"Materials :"})," ",R("strong",{children:[(f==null?void 0:f.page)*(f==null?void 0:f.size)+1," -"," ",f==null?void 0:f.currentElements]})," ",n("span",{children:"of"})," ",n("strong",{children:f==null?void 0:f.totalElements})]}),n(_s,{title:"Next",placement:"top",arrow:!0,children:n(xs,{disabled:(f==null?void 0:f.currentElements)>=(f==null?void 0:f.totalElements)||!1,onClick:()=>{M(gr((f==null?void 0:f.page)+1))},children:n(pa,{sx:{color:(f==null?void 0:f.currentElements)>=(f==null?void 0:f.totalElements)?(Ue=(Zt=$e)==null?void 0:Zt.secondary)==null?void 0:Ue.grey:(Ps=(Pt=$e)==null?void 0:Pt.primary)==null?void 0:Ps.main,fontSize:"1.5rem"}})})})]}),dt==null?void 0:dt.map(le=>p!=null&&p.includes(le)?R(Uh,{sx:{marginBottom:"20px",boxShadow:3},children:[n(kh,{expandIcon:n(sp,{}),"aria-controls":`${le}-content`,id:`${le}-header`,sx:{backgroundImage:"linear-gradient(180deg,rgb(242, 241, 255) 0%,rgb(255, 255, 255) 100%)",padding:"8px 16px","&:hover":{backgroundImage:"linear-gradient(90deg,rgb(242, 242, 255) 0%,rgb(239, 232, 255) 100%)"}},children:n(mt,{variant:"h6",sx:{fontWeight:"bold"},children:le})}),n(Hh,{sx:{height:"calc(100vh - 300px)"},children:Ce(le,s[le])})]},le):null)]}):n(mt,{children:"No data available"})})]})},$i=e=>{const t=se(Q=>Q.payload.changeFieldRows),r=se(Q=>Q.payload.changeLogData),o=se(Q=>Q.request),c=se(Q=>Q.payload),s=se(Q=>Q.payload.dynamicKeyValues),p=se(Q=>Q.payload.selectedRows),l=se(Q=>Q.userManagement.taskData),u=e||(s==null?void 0:s.templateName),re=(Q,f)=>{var Y,z,g,T,v,C;return r[Q]?{RequestId:((T=c==null?void 0:c.payloadData)==null?void 0:T.RequestId)||((v=c==null?void 0:c.changeLogData)==null?void 0:v.RequestId),ChildRequestId:((C=s==null?void 0:s.childRequestHeaderData)==null?void 0:C.ChildRequestId)??null,ChangeLogId:f??null,...r[Q]}:{RequestId:((Y=c==null?void 0:c.payloadData)==null?void 0:Y.RequestId)||((z=c==null?void 0:c.changeLogData)==null?void 0:z.RequestId),ChildRequestId:((g=s==null?void 0:s.childRequestHeaderData)==null?void 0:g.ChildRequestId)??null,ChangeLogId:f??null}},ae=Q=>{var f,Y,z,g,T,v,C,S,q,A,x,H,k,L;if(e===((f=Je)==null?void 0:f.LOGISTIC)||(s==null?void 0:s.templateName)===((Y=Je)==null?void 0:Y.LOGISTIC))return y(Q);if(e===((z=Je)==null?void 0:z.ITEM_CAT)||(s==null?void 0:s.templateName)===((g=Je)==null?void 0:g.ITEM_CAT))return O(Q);if(e===((T=Je)==null?void 0:T.MRP)||(s==null?void 0:s.templateName)===((v=Je)==null?void 0:v.MRP))return K(Q);if(e===((C=Je)==null?void 0:C.UPD_DESC)||(s==null?void 0:s.templateName)===((S=Je)==null?void 0:S.UPD_DESC))return ce(Q);if(e===((q=Je)==null?void 0:q.WARE_VIEW_2)||(s==null?void 0:s.templateName)===((A=Je)==null?void 0:A.WARE_VIEW_2))return oe(Q);if(e===((x=Je)==null?void 0:x.CHG_STAT)||(s==null?void 0:s.templateName)===((H=Je)==null?void 0:H.CHG_STAT))return m(Q);if(e===((k=Je)==null?void 0:k.SET_DNU)||(s==null?void 0:s.templateName)===((L=Je)==null?void 0:L.SET_DNU))return B(Q)},y=Q=>{const f=t.reduce((Y,z)=>{if((p==null?void 0:p.length)!==0&&!(p!=null&&p.includes(z==null?void 0:z.id)))return Y;const g=z==null?void 0:z.Material;return Y[g]||(Y[g]=[]),Y[g].push(z),Y},{});if(Q){const Y=["id","MaterialId","ClientId","slNo","ChangeLogId","MatlType"];return Object.keys(f).map(g=>{var q,A,x,H,k,L,N,U,I,ge,te,He,M,we,de;const T=f[g],{MaterialId:v,ClientId:C,ChangeLogId:S}=T[(T==null?void 0:T.length)-1];return{MaterialId:v,ChangeLogId:S,Material:g,MatlType:((x=(A=f[g])==null?void 0:A[((q=f[g])==null?void 0:q.length)-1])==null?void 0:x.MatlType)||"",Function:"UPD",TaskId:(s==null?void 0:s.otherPayloadData.TaskId)||"",TaskName:(s==null?void 0:s.otherPayloadData.TaskName)||"",creationTime:(s==null?void 0:s.otherPayloadData.CreationTime)||"",dueDate:(s==null?void 0:s.otherPayloadData.DueDate)||"",IsFirstCreate:!1,MassEditId:s==null?void 0:s.otherPayloadData.MassEditId,MassChildEditId:s==null?void 0:s.otherPayloadData.MassChildEditId,TotalIntermediateTasks:s==null?void 0:s.otherPayloadData.TotalIntermediateTasks,IntermediateTaskCount:s==null?void 0:s.otherPayloadData.IntermediateTaskCount,Toclientdata:{ClientId:C,Material:g,Function:"UPD"},Touomdata:T.map(F=>{const qe={...F,Function:"UPD"};return Y.forEach(W=>delete qe[W]),qe}),Tochildrequestheaderdata:{ChildRequestId:((H=s==null?void 0:s.childRequestHeaderData)==null?void 0:H.ChildRequestId)||null,MaterialGroupType:((k=s==null?void 0:s.childRequestHeaderData)==null?void 0:k.MaterialGroupType)||null,TaskId:(l==null?void 0:l.taskId)||null,Comments:(s==null?void 0:s.Comments)||"",TotalIntermediateTasks:((L=s==null?void 0:s.childRequestHeaderData)==null?void 0:L.TotalIntermediateTasks)||null,IntermediateTaskCount:((N=s==null?void 0:s.childRequestHeaderData)==null?void 0:N.IntermediateTaskCount)||null,ReqCreatedBy:((U=s==null?void 0:s.childRequestHeaderData)==null?void 0:U.ReqCreatedBy)||null,ReqCreatedOn:((I=s==null?void 0:s.childRequestHeaderData)==null?void 0:I.ReqCreatedOn)||null,ReqUpdatedOn:((ge=s==null?void 0:s.childRequestHeaderData)==null?void 0:ge.ReqUpdatedOn)||null,RequestType:((te=s==null?void 0:s.childRequestHeaderData)==null?void 0:te.RequestType)||null,RequestPrefix:((He=s==null?void 0:s.childRequestHeaderData)==null?void 0:He.RequestPrefix)||null,RequestDesc:((M=s==null?void 0:s.childRequestHeaderData)==null?void 0:M.RequestDesc)||null,RequestPriority:((we=s==null?void 0:s.childRequestHeaderData)==null?void 0:we.RequestPriority)||null,RequestStatus:((de=s==null?void 0:s.childRequestHeaderData)==null?void 0:de.RequestStatus)||null,CurrentLevel:(l==null?void 0:l.ATTRIBUTE_3)||"",CurrentLevelName:(l==null?void 0:l.ATTRIBUTE_4)||"",ParticularLevel:(s==null?void 0:s.Level)||"-1",TaskName:(l==null?void 0:l.taskDesc)||"",ApproverGroup:(l==null?void 0:l.ATTRIBUTE_5)||""},Torequestheaderdata:(s==null?void 0:s.requestHeaderData)||{},Tomaterialerrordata:(s==null?void 0:s.errorData[g])||{},TemplateName:u,changeLogData:re(g,S),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}else{const Y=["id","slNo","MatlType"];return Object.keys(f).map(g=>{var T,v,C,S,q,A,x,H,k,L,N,U,I,ge,te,He,M,we;return{Touomdata:f[g].map(de=>{const F={...de,Function:"UPD"};return Y.forEach(qe=>delete F[qe]),F}),Torequestheaderdata:{RequestId:(T=o==null?void 0:o.requestHeader)==null?void 0:T.requestId,ReqCreatedBy:(v=o==null?void 0:o.requestHeader)==null?void 0:v.reqCreatedBy,ReqCreatedOn:ws((C=o==null?void 0:o.requestHeader)==null?void 0:C.reqCreatedOn),ReqUpdatedOn:ws((S=o==null?void 0:o.requestHeader)==null?void 0:S.reqCreatedOn),RequestType:(q=o==null?void 0:o.requestHeader)==null?void 0:q.requestType,RequestPriority:(A=o==null?void 0:o.requestHeader)==null?void 0:A.requestPriority,RequestDesc:(x=o==null?void 0:o.requestHeader)==null?void 0:x.requestDesc,RequestStatus:(H=o==null?void 0:o.requestHeader)==null?void 0:H.requestStatus,FirstProd:((k=c==null?void 0:c.payloadData)==null?void 0:k.FirstProductionDate)||null,LaunchDate:((L=c==null?void 0:c.payloadData)==null?void 0:L.LaunchDate)||null,LeadingCat:(N=o==null?void 0:o.requestHeader)==null?void 0:N.leadingCat,Division:(U=o==null?void 0:o.requestHeader)==null?void 0:U.division,Region:(I=o==null?void 0:o.requestHeader)==null?void 0:I.region,TemplateName:(ge=o==null?void 0:o.requestHeader)==null?void 0:ge.templateName,FieldName:(te=o==null?void 0:o.requestHeader)==null?void 0:te.fieldName},Tochildrequestheaderdata:{},Toclientdata:{ClientId:null,Function:"UPD"},Material:g,MatlType:((we=(M=f[g])==null?void 0:M[((He=f[g])==null?void 0:He.length)-1])==null?void 0:we.MatlType)||"",TemplateName:u,IsFirstCreate:!0,Function:"UPD",changeLogData:re(g),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}},O=Q=>{const f=t.reduce((Y,z)=>{if((p==null?void 0:p.length)!==0&&!(p!=null&&p.includes(z==null?void 0:z.id)))return Y;const g=z==null?void 0:z.Material;return Y[g]||(Y[g]=[]),Y[g].push(z),Y},{});if(Q){const Y=["id","MaterialId","ClientId","slNo","ChangeLogId","MatlType"];return Object.keys(f).map(g=>{var S,q,A,x,H,k,L,N,U,I,ge,te,He,M,we,de,F;const T=f[g],{MaterialId:v,ClientId:C}=T[0];return{MaterialId:v,Material:g,MassChildEditId:s==null?void 0:s.otherPayloadData.MassChildEditId,MatlType:((A=(q=f[g])==null?void 0:q[((S=f[g])==null?void 0:S.length)-1])==null?void 0:A.MatlType)||"",Function:"UPD",TaskId:(s==null?void 0:s.otherPayloadData.TaskId)||"",TaskName:(s==null?void 0:s.otherPayloadData.TaskName)||"",creationTime:(s==null?void 0:s.otherPayloadData.CreationTime)||"",dueDate:(s==null?void 0:s.otherPayloadData.DueDate)||"",IsFirstCreate:!1,MassEditId:s==null?void 0:s.otherPayloadData.MassEditId,TotalIntermediateTasks:s==null?void 0:s.otherPayloadData.TotalIntermediateTasks,IntermediateTaskCount:s==null?void 0:s.otherPayloadData.IntermediateTaskCount,Tosalesdata:T.map(qe=>{const W={...qe,Function:"UPD"};return Y.forEach(Ke=>delete W[Ke]),W}),Tochildrequestheaderdata:{ChildRequestId:((x=s==null?void 0:s.childRequestHeaderData)==null?void 0:x.ChildRequestId)||null,MaterialGroupType:((H=s==null?void 0:s.childRequestHeaderData)==null?void 0:H.MaterialGroupType)||null,TaskId:(l==null?void 0:l.taskId)||null,Comments:(s==null?void 0:s.Comments)||"",TotalIntermediateTasks:((k=s==null?void 0:s.childRequestHeaderData)==null?void 0:k.TotalIntermediateTasks)||null,IntermediateTaskCount:((L=s==null?void 0:s.childRequestHeaderData)==null?void 0:L.IntermediateTaskCount)||null,ReqCreatedBy:((N=s==null?void 0:s.childRequestHeaderData)==null?void 0:N.ReqCreatedBy)||null,ReqCreatedOn:((U=s==null?void 0:s.childRequestHeaderData)==null?void 0:U.ReqCreatedOn)||null,ReqUpdatedOn:((I=s==null?void 0:s.childRequestHeaderData)==null?void 0:I.ReqUpdatedOn)||null,RequestType:((ge=s==null?void 0:s.childRequestHeaderData)==null?void 0:ge.RequestType)||null,RequestPrefix:((te=s==null?void 0:s.childRequestHeaderData)==null?void 0:te.RequestPrefix)||null,RequestDesc:((He=s==null?void 0:s.childRequestHeaderData)==null?void 0:He.RequestDesc)||null,RequestPriority:((M=s==null?void 0:s.childRequestHeaderData)==null?void 0:M.RequestPriority)||null,RequestStatus:((we=s==null?void 0:s.childRequestHeaderData)==null?void 0:we.RequestStatus)||null,CurrentLevel:(l==null?void 0:l.ATTRIBUTE_3)||"",CurrentLevelName:(l==null?void 0:l.ATTRIBUTE_4)||"",ParticularLevel:(s==null?void 0:s.Level)||"-1",TaskName:(l==null?void 0:l.taskDesc)||"",ApproverGroup:(l==null?void 0:l.ATTRIBUTE_5)||""},Torequestheaderdata:(s==null?void 0:s.requestHeaderData)||{},Tomaterialerrordata:(s==null?void 0:s.errorData[g])||{},TemplateName:u,changeLogData:re(g,(F=(de=f[g])==null?void 0:de[0])==null?void 0:F.ChangeLogId),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}else{const Y=["id","slNo","MatlType"];return Object.keys(f).map(g=>{var T,v,C,S,q,A,x,H,k,L,N,U,I,ge,te,He,M,we;return{Tosalesdata:f[g].map(de=>{const F={...de,Function:"UPD"};return Y.forEach(qe=>delete F[qe]),F}),Torequestheaderdata:{RequestId:(T=o==null?void 0:o.requestHeader)==null?void 0:T.requestId,ReqCreatedBy:(v=o==null?void 0:o.requestHeader)==null?void 0:v.reqCreatedBy,ReqCreatedOn:ws((C=o==null?void 0:o.requestHeader)==null?void 0:C.reqCreatedOn),ReqUpdatedOn:ws((S=o==null?void 0:o.requestHeader)==null?void 0:S.reqCreatedOn),RequestType:(q=o==null?void 0:o.requestHeader)==null?void 0:q.requestType,RequestPriority:(A=o==null?void 0:o.requestHeader)==null?void 0:A.requestPriority,RequestDesc:(x=o==null?void 0:o.requestHeader)==null?void 0:x.requestDesc,RequestStatus:(H=o==null?void 0:o.requestHeader)==null?void 0:H.requestStatus,FirstProd:((k=c==null?void 0:c.payloadData)==null?void 0:k.FirstProductionDate)||null,LaunchDate:((L=c==null?void 0:c.payloadData)==null?void 0:L.LaunchDate)||null,LeadingCat:(N=o==null?void 0:o.requestHeader)==null?void 0:N.leadingCat,Division:(U=o==null?void 0:o.requestHeader)==null?void 0:U.division,Region:(I=o==null?void 0:o.requestHeader)==null?void 0:I.region,TemplateName:(ge=o==null?void 0:o.requestHeader)==null?void 0:ge.templateName,FieldName:(te=o==null?void 0:o.requestHeader)==null?void 0:te.fieldName},Tochildrequestheaderdata:{},Material:g,MatlType:((we=(M=f[g])==null?void 0:M[((He=f[g])==null?void 0:He.length)-1])==null?void 0:we.MatlType)||"",TemplateName:u,IsFirstCreate:!0,Function:"UPD",changeLogData:re(g),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}},K=Q=>{if(Q){const f={},Y=["id","slNo","type","MaterialId","Plant","ChangeLogId","MatlType"],z=["id","slNo","type","MaterialId","ChangeLogId"];return Object.keys(t).forEach(T=>{t[T].forEach(v=>{const{Material:C,MaterialId:S,ChangeLogId:q}=v;f[C]||(f[C]={Toclientdata:null,Toplantdata:[],MaterialId:S,ChangeLogId:q,MatlType:""});const A={...v};T==="Basic Data"&&!f[C].Toclientdata?(f[C].MatlType=(A==null?void 0:A.MatlType)||"",Y.forEach(x=>delete A[x]),f[C].Toclientdata=A):T==="Plant Data"&&(z.forEach(x=>delete A[x]),f[C].Toplantdata.push(A))})}),Object.keys(f).map(T=>{var v,C,S,q,A,x,H,k,L,N,U,I,ge;return{...f[T],Material:T,Function:"UPD",MassChildEditId:s==null?void 0:s.otherPayloadData.MassChildEditId,TaskId:(s==null?void 0:s.otherPayloadData.TaskId)||"",TaskName:(s==null?void 0:s.otherPayloadData.TaskName)||"",creationTime:(s==null?void 0:s.otherPayloadData.CreationTime)||"",dueDate:(s==null?void 0:s.otherPayloadData.DueDate)||"",IsFirstCreate:!1,MassEditId:s==null?void 0:s.otherPayloadData.MassEditId,TotalIntermediateTasks:s==null?void 0:s.otherPayloadData.TotalIntermediateTasks,IntermediateTaskCount:s==null?void 0:s.otherPayloadData.IntermediateTaskCount,Torequestheaderdata:(s==null?void 0:s.requestHeaderData)||{},Tomaterialerrordata:(s==null?void 0:s.errorData[T])||{},TemplateName:u,Tochildrequestheaderdata:{ChildRequestId:((v=s==null?void 0:s.childRequestHeaderData)==null?void 0:v.ChildRequestId)||null,MaterialGroupType:((C=s==null?void 0:s.childRequestHeaderData)==null?void 0:C.MaterialGroupType)||null,TaskId:(l==null?void 0:l.taskId)||null,Comments:(s==null?void 0:s.Comments)||"",TotalIntermediateTasks:((S=s==null?void 0:s.childRequestHeaderData)==null?void 0:S.TotalIntermediateTasks)||null,IntermediateTaskCount:((q=s==null?void 0:s.childRequestHeaderData)==null?void 0:q.IntermediateTaskCount)||null,ReqCreatedBy:((A=s==null?void 0:s.childRequestHeaderData)==null?void 0:A.ReqCreatedBy)||null,ReqCreatedOn:((x=s==null?void 0:s.childRequestHeaderData)==null?void 0:x.ReqCreatedOn)||null,ReqUpdatedOn:((H=s==null?void 0:s.childRequestHeaderData)==null?void 0:H.ReqUpdatedOn)||null,RequestType:((k=s==null?void 0:s.childRequestHeaderData)==null?void 0:k.RequestType)||null,RequestPrefix:((L=s==null?void 0:s.childRequestHeaderData)==null?void 0:L.RequestPrefix)||null,RequestDesc:((N=s==null?void 0:s.childRequestHeaderData)==null?void 0:N.RequestDesc)||null,RequestPriority:((U=s==null?void 0:s.childRequestHeaderData)==null?void 0:U.RequestPriority)||null,RequestStatus:((I=s==null?void 0:s.childRequestHeaderData)==null?void 0:I.RequestStatus)||null,CurrentLevel:(l==null?void 0:l.ATTRIBUTE_3)||"",CurrentLevelName:(l==null?void 0:l.ATTRIBUTE_4)||"",ParticularLevel:(s==null?void 0:s.Level)||"-1",TaskName:(l==null?void 0:l.taskDesc)||"",ApproverGroup:(l==null?void 0:l.ATTRIBUTE_5)||""},changeLogData:re(T,(ge=f[T])==null?void 0:ge.ChangeLogId),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}else{const f={},Y=["id","slNo","type","Plant","MatlType"],z=["id","slNo","type"];return Object.keys(t).forEach(T=>{t[T].forEach(v=>{const{Material:C}=v;f[C]||(f[C]={Toclientdata:null,Toplantdata:[],MatlType:""});const S={...v};T==="Basic Data"&&!f[C].Toclientdata?(f[C].MatlType=(S==null?void 0:S.MatlType)||"",Y.forEach(q=>delete S[q]),f[C].Toclientdata={...S,Function:"UPD"}):T==="Plant Data"&&(z.forEach(q=>delete S[q]),f[C].Toplantdata.push({...S,Function:"UPD"}))})}),Object.keys(f).map(T=>{var v,C,S,q,A,x,H,k,L,N,U,I,ge,te,He,M,we;return{...f[T],Torequestheaderdata:{RequestId:(v=o==null?void 0:o.requestHeader)==null?void 0:v.requestId,ReqCreatedBy:(C=o==null?void 0:o.requestHeader)==null?void 0:C.reqCreatedBy,ReqCreatedOn:ws((S=o==null?void 0:o.requestHeader)==null?void 0:S.reqCreatedOn),ReqUpdatedOn:ws((q=o==null?void 0:o.requestHeader)==null?void 0:q.reqCreatedOn),RequestType:(A=o==null?void 0:o.requestHeader)==null?void 0:A.requestType,RequestPriority:(x=o==null?void 0:o.requestHeader)==null?void 0:x.requestPriority,RequestDesc:(H=o==null?void 0:o.requestHeader)==null?void 0:H.requestDesc,RequestStatus:(k=o==null?void 0:o.requestHeader)==null?void 0:k.requestStatus,FirstProd:((L=c==null?void 0:c.payloadData)==null?void 0:L.FirstProductionDate)||null,LaunchDate:((N=c==null?void 0:c.payloadData)==null?void 0:N.LaunchDate)||null,LeadingCat:(U=o==null?void 0:o.requestHeader)==null?void 0:U.leadingCat,Division:(I=o==null?void 0:o.requestHeader)==null?void 0:I.division,Region:(ge=o==null?void 0:o.requestHeader)==null?void 0:ge.region,TemplateName:(te=o==null?void 0:o.requestHeader)==null?void 0:te.templateName,FieldName:(He=o==null?void 0:o.requestHeader)==null?void 0:He.fieldName},Tochildrequestheaderdata:{},Material:T,TemplateName:(M=o==null?void 0:o.requestHeader)==null?void 0:M.templateName,IsFirstCreate:!0,Function:"UPD",MassEditId:(we=o==null?void 0:o.requestHeader)==null?void 0:we.requestId,changeLogData:re(T),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}},m=Q=>{if(Q){const f={},Y=["id","slNo","type","MaterialId","Plant","ChangeLogId","MatlType"],z=["id","slNo","type","MaterialId","ChangeLogId"],g=["id","slNo","type","MaterialId","ChangeLogId"];return Object.keys(t).forEach(v=>{t[v].forEach(C=>{const{Material:S,MaterialId:q,ChangeLogId:A}=C;f[S]||(f[S]={Toclientdata:null,Toplantdata:[],Tosalesdata:[],MaterialId:q,ChangeLogId:A,MatlType:""});const x={...C};v==="Basic Data"&&!f[S].Toclientdata?(f[S].MatlType=(x==null?void 0:x.MatlType)||"",Y.forEach(H=>delete x[H]),f[S].Toclientdata=x):v==="Plant Data"?(z.forEach(H=>delete x[H]),f[S].Toplantdata.push(x)):v==="Sales Data"&&(g.forEach(H=>delete x[H]),f[S].Tosalesdata.push(x))})}),Object.keys(f).map(v=>{var C,S,q,A,x,H,k,L,N,U,I,ge,te;return{...f[v],Material:v,MassChildEditId:s==null?void 0:s.otherPayloadData.MassChildEditId,Function:"UPD",TaskId:(s==null?void 0:s.otherPayloadData.TaskId)||"",TaskName:(s==null?void 0:s.otherPayloadData.TaskName)||"",creationTime:(s==null?void 0:s.otherPayloadData.CreationTime)||"",dueDate:(s==null?void 0:s.otherPayloadData.DueDate)||"",IsFirstCreate:!1,MassEditId:s==null?void 0:s.otherPayloadData.MassEditId,TotalIntermediateTasks:s==null?void 0:s.otherPayloadData.TotalIntermediateTasks,IntermediateTaskCount:s==null?void 0:s.otherPayloadData.IntermediateTaskCount,Torequestheaderdata:(s==null?void 0:s.requestHeaderData)||{},Tomaterialerrordata:(s==null?void 0:s.errorData[v])||{},TemplateName:u,Tochildrequestheaderdata:{ChildRequestId:((C=s==null?void 0:s.childRequestHeaderData)==null?void 0:C.ChildRequestId)||null,MaterialGroupType:((S=s==null?void 0:s.childRequestHeaderData)==null?void 0:S.MaterialGroupType)||null,TaskId:(l==null?void 0:l.taskId)||null,Comments:(s==null?void 0:s.Comments)||"",TotalIntermediateTasks:((q=s==null?void 0:s.childRequestHeaderData)==null?void 0:q.TotalIntermediateTasks)||null,IntermediateTaskCount:((A=s==null?void 0:s.childRequestHeaderData)==null?void 0:A.IntermediateTaskCount)||null,ReqCreatedBy:((x=s==null?void 0:s.childRequestHeaderData)==null?void 0:x.ReqCreatedBy)||null,ReqCreatedOn:((H=s==null?void 0:s.childRequestHeaderData)==null?void 0:H.ReqCreatedOn)||null,ReqUpdatedOn:((k=s==null?void 0:s.childRequestHeaderData)==null?void 0:k.ReqUpdatedOn)||null,RequestType:((L=s==null?void 0:s.childRequestHeaderData)==null?void 0:L.RequestType)||null,RequestPrefix:((N=s==null?void 0:s.childRequestHeaderData)==null?void 0:N.RequestPrefix)||null,RequestDesc:((U=s==null?void 0:s.childRequestHeaderData)==null?void 0:U.RequestDesc)||null,RequestPriority:((I=s==null?void 0:s.childRequestHeaderData)==null?void 0:I.RequestPriority)||null,RequestStatus:((ge=s==null?void 0:s.childRequestHeaderData)==null?void 0:ge.RequestStatus)||null,CurrentLevel:(l==null?void 0:l.ATTRIBUTE_3)||"",CurrentLevelName:(l==null?void 0:l.ATTRIBUTE_4)||"",ParticularLevel:(s==null?void 0:s.Level)||"-1",TaskName:(l==null?void 0:l.taskDesc)||"",ApproverGroup:(l==null?void 0:l.ATTRIBUTE_5)||""},changeLogData:re(v,(te=f[v])==null?void 0:te.ChangeLogId),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}else{const f={},Y=["id","slNo","type","Plant","MatlType"],z=["id","slNo","type"],g=["id","slNo","type"];return Object.keys(t).forEach(v=>{t[v].forEach(C=>{const{Material:S}=C;f[S]||(f[S]={Toclientdata:null,Toplantdata:[],Tosalesdata:[],MatlType:""});const q={...C};v==="Basic Data"&&!f[S].Toclientdata?(f[S].MatlType=(q==null?void 0:q.MatlType)||"",Y.forEach(A=>delete q[A]),f[S].Toclientdata={...q,Function:"UPD"}):v==="Plant Data"?(z.forEach(A=>delete q[A]),f[S].Toplantdata.push({...q,Function:"UPD"})):v==="Sales Data"&&(g.forEach(A=>delete q[A]),f[S].Tosalesdata.push({...q,Function:"UPD"}))})}),Object.keys(f).map(v=>{var C,S,q,A,x,H,k,L,N,U,I,ge,te,He,M,we,de;return{...f[v],Torequestheaderdata:{RequestId:(C=o==null?void 0:o.requestHeader)==null?void 0:C.requestId,ReqCreatedBy:(S=o==null?void 0:o.requestHeader)==null?void 0:S.reqCreatedBy,ReqCreatedOn:ws((q=o==null?void 0:o.requestHeader)==null?void 0:q.reqCreatedOn),ReqUpdatedOn:ws((A=o==null?void 0:o.requestHeader)==null?void 0:A.reqCreatedOn),RequestType:(x=o==null?void 0:o.requestHeader)==null?void 0:x.requestType,RequestPriority:(H=o==null?void 0:o.requestHeader)==null?void 0:H.requestPriority,RequestDesc:(k=o==null?void 0:o.requestHeader)==null?void 0:k.requestDesc,RequestStatus:(L=o==null?void 0:o.requestHeader)==null?void 0:L.requestStatus,FirstProd:((N=c==null?void 0:c.payloadData)==null?void 0:N.FirstProductionDate)||null,LaunchDate:((U=c==null?void 0:c.payloadData)==null?void 0:U.LaunchDate)||null,LeadingCat:(I=o==null?void 0:o.requestHeader)==null?void 0:I.leadingCat,Division:(ge=o==null?void 0:o.requestHeader)==null?void 0:ge.division,Region:(te=o==null?void 0:o.requestHeader)==null?void 0:te.region,TemplateName:(He=o==null?void 0:o.requestHeader)==null?void 0:He.templateName,FieldName:(M=o==null?void 0:o.requestHeader)==null?void 0:M.fieldName},Tochildrequestheaderdata:{},Material:v,TemplateName:(we=o==null?void 0:o.requestHeader)==null?void 0:we.templateName,IsFirstCreate:!0,Function:"UPD",MassEditId:(de=o==null?void 0:o.requestHeader)==null?void 0:de.requestId,changeLogData:re(v),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}},B=Q=>{if(Q){const f={},Y=["id","slNo","type","MaterialId","Plant","ChangeLogId","MatlType"],z=["id","slNo","type","MaterialId","ChangeLogId"],g=["id","slNo","type","MaterialId","ChangeLogId"],T=["id","slNo","type","MaterialId","ChangeLogId"];return Object.keys(t).forEach(C=>{t[C].forEach(S=>{const{Material:q,MaterialId:A,ChangeLogId:x}=S;f[q]||(f[q]={Toclientdata:null,Toplantdata:[],Tosalesdata:[],Tomaterialdescription:[],MaterialId:A,ChangeLogId:x,MatlType:""});const H={...S};C==="Basic Data"&&!f[q].Toclientdata?(f[q].MatlType=(H==null?void 0:H.MatlType)||"",Y.forEach(k=>delete H[k]),f[q].Toclientdata=H):C==="Plant Data"?(z.forEach(k=>delete H[k]),f[q].Toplantdata.push(H)):C==="Sales Data"?(g.forEach(k=>delete H[k]),f[q].Tosalesdata.push(H)):C==="Description"&&(T.forEach(k=>delete H[k]),f[q].Tomaterialdescription.push(H))})}),Object.keys(f).map(C=>{var S,q,A,x,H,k,L,N,U,I,ge,te,He;return{...f[C],Material:C,MassChildEditId:s==null?void 0:s.otherPayloadData.MassChildEditId,Function:"UPD",TaskId:(s==null?void 0:s.otherPayloadData.TaskId)||"",TaskName:(s==null?void 0:s.otherPayloadData.TaskName)||"",creationTime:(s==null?void 0:s.otherPayloadData.CreationTime)||"",dueDate:(s==null?void 0:s.otherPayloadData.DueDate)||"",IsFirstCreate:!1,MassEditId:s==null?void 0:s.otherPayloadData.MassEditId,TotalIntermediateTasks:s==null?void 0:s.otherPayloadData.TotalIntermediateTasks,IntermediateTaskCount:s==null?void 0:s.otherPayloadData.IntermediateTaskCount,Torequestheaderdata:(s==null?void 0:s.requestHeaderData)||{},Tomaterialerrordata:(s==null?void 0:s.errorData[C])||{},TemplateName:u,Tochildrequestheaderdata:{ChildRequestId:((S=s==null?void 0:s.childRequestHeaderData)==null?void 0:S.ChildRequestId)||null,MaterialGroupType:((q=s==null?void 0:s.childRequestHeaderData)==null?void 0:q.MaterialGroupType)||null,TaskId:(l==null?void 0:l.taskId)||null,Comments:(s==null?void 0:s.Comments)||"",TotalIntermediateTasks:((A=s==null?void 0:s.childRequestHeaderData)==null?void 0:A.TotalIntermediateTasks)||null,IntermediateTaskCount:((x=s==null?void 0:s.childRequestHeaderData)==null?void 0:x.IntermediateTaskCount)||null,ReqCreatedBy:((H=s==null?void 0:s.childRequestHeaderData)==null?void 0:H.ReqCreatedBy)||null,ReqCreatedOn:((k=s==null?void 0:s.childRequestHeaderData)==null?void 0:k.ReqCreatedOn)||null,ReqUpdatedOn:((L=s==null?void 0:s.childRequestHeaderData)==null?void 0:L.ReqUpdatedOn)||null,RequestType:((N=s==null?void 0:s.childRequestHeaderData)==null?void 0:N.RequestType)||null,RequestPrefix:((U=s==null?void 0:s.childRequestHeaderData)==null?void 0:U.RequestPrefix)||null,RequestDesc:((I=s==null?void 0:s.childRequestHeaderData)==null?void 0:I.RequestDesc)||null,RequestPriority:((ge=s==null?void 0:s.childRequestHeaderData)==null?void 0:ge.RequestPriority)||null,RequestStatus:((te=s==null?void 0:s.childRequestHeaderData)==null?void 0:te.RequestStatus)||null,CurrentLevel:(l==null?void 0:l.ATTRIBUTE_3)||"",CurrentLevelName:(l==null?void 0:l.ATTRIBUTE_4)||"",ParticularLevel:(s==null?void 0:s.Level)||"-1",TaskName:(l==null?void 0:l.taskDesc)||"",ApproverGroup:(l==null?void 0:l.ATTRIBUTE_5)||""},changeLogData:re(C,(He=f[C])==null?void 0:He.ChangeLogId),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}else{const f={},Y=["id","slNo","type","Plant","MatlType"],z=["id","slNo","type"],g=["id","slNo","type"],T=["id","slNo","type"];return Object.keys(t).forEach(C=>{t[C].forEach(S=>{const{Material:q}=S;f[q]||(f[q]={Toclientdata:null,Toplantdata:[],Tosalesdata:[],Tomaterialdescription:[],MatlType:""});const A={...S};C==="Basic Data"&&!f[q].Toclientdata?(f[q].MatlType=(A==null?void 0:A.MatlType)||"",Y.forEach(x=>delete A[x]),f[q].Toclientdata={...A,Function:"UPD"}):C==="Plant Data"?(z.forEach(x=>delete A[x]),f[q].Toplantdata.push({...A,Function:"UPD"})):C==="Sales Data"?(g.forEach(x=>delete A[x]),f[q].Tosalesdata.push({...A,Function:"UPD"})):C==="Description"&&(T.forEach(x=>delete A[x]),f[q].Tomaterialdescription.push({...A,Function:"UPD"}))})}),Object.keys(f).map(C=>{var S,q,A,x,H,k,L,N,U,I,ge,te,He,M,we,de,F;return{...f[C],Torequestheaderdata:{RequestId:(S=o==null?void 0:o.requestHeader)==null?void 0:S.requestId,ReqCreatedBy:(q=o==null?void 0:o.requestHeader)==null?void 0:q.reqCreatedBy,ReqCreatedOn:ws((A=o==null?void 0:o.requestHeader)==null?void 0:A.reqCreatedOn),ReqUpdatedOn:ws((x=o==null?void 0:o.requestHeader)==null?void 0:x.reqCreatedOn),RequestType:(H=o==null?void 0:o.requestHeader)==null?void 0:H.requestType,RequestPriority:(k=o==null?void 0:o.requestHeader)==null?void 0:k.requestPriority,RequestDesc:(L=o==null?void 0:o.requestHeader)==null?void 0:L.requestDesc,RequestStatus:(N=o==null?void 0:o.requestHeader)==null?void 0:N.requestStatus,FirstProd:((U=c==null?void 0:c.payloadData)==null?void 0:U.FirstProductionDate)||null,LaunchDate:((I=c==null?void 0:c.payloadData)==null?void 0:I.LaunchDate)||null,LeadingCat:(ge=o==null?void 0:o.requestHeader)==null?void 0:ge.leadingCat,Division:(te=o==null?void 0:o.requestHeader)==null?void 0:te.division,Region:(He=o==null?void 0:o.requestHeader)==null?void 0:He.region,TemplateName:(M=o==null?void 0:o.requestHeader)==null?void 0:M.templateName,FieldName:(we=o==null?void 0:o.requestHeader)==null?void 0:we.fieldName},Tochildrequestheaderdata:{},Material:C,TemplateName:(de=o==null?void 0:o.requestHeader)==null?void 0:de.templateName,IsFirstCreate:!0,Function:"UPD",MassEditId:(F=o==null?void 0:o.requestHeader)==null?void 0:F.requestId,changeLogData:re(C),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}},ce=Q=>{const f=t.reduce((Y,z)=>{if((p==null?void 0:p.length)!==0&&!(p!=null&&p.includes(z==null?void 0:z.id)))return Y;const g=z==null?void 0:z.Material;return Y[g]||(Y[g]=[]),Y[g].push(z),Y},{});if(Q){const Y=["id","MaterialId","ClientId","slNo","ChangeLogId","MatlType"];return Object.keys(f).map(g=>{var S,q,A,x,H,k,L,N,U,I,ge,te,He,M,we,de,F;const T=f[g],{MaterialId:v,ClientId:C}=T[(T==null?void 0:T.length)-1];return{MaterialId:v,Material:g,MassChildEditId:s==null?void 0:s.otherPayloadData.MassChildEditId,MatlType:((A=(q=f[g])==null?void 0:q[((S=f[g])==null?void 0:S.length)-1])==null?void 0:A.MatlType)||"",Function:"UPD",TaskId:(s==null?void 0:s.otherPayloadData.TaskId)||"",TaskName:(s==null?void 0:s.otherPayloadData.TaskName)||"",creationTime:(s==null?void 0:s.otherPayloadData.CreationTime)||"",dueDate:(s==null?void 0:s.otherPayloadData.DueDate)||"",IsFirstCreate:!1,MassEditId:s==null?void 0:s.otherPayloadData.MassEditId,TotalIntermediateTasks:s==null?void 0:s.otherPayloadData.TotalIntermediateTasks,IntermediateTaskCount:s==null?void 0:s.otherPayloadData.IntermediateTaskCount,Toclientdata:{ClientId:C,Material:g,Function:"UPD"},Tochildrequestheaderdata:{ChildRequestId:((x=s==null?void 0:s.childRequestHeaderData)==null?void 0:x.ChildRequestId)||null,MaterialGroupType:((H=s==null?void 0:s.childRequestHeaderData)==null?void 0:H.MaterialGroupType)||null,TaskId:(l==null?void 0:l.taskId)||null,Comments:(s==null?void 0:s.Comments)||"",TotalIntermediateTasks:((k=s==null?void 0:s.childRequestHeaderData)==null?void 0:k.TotalIntermediateTasks)||null,IntermediateTaskCount:((L=s==null?void 0:s.childRequestHeaderData)==null?void 0:L.IntermediateTaskCount)||null,ReqCreatedBy:((N=s==null?void 0:s.childRequestHeaderData)==null?void 0:N.ReqCreatedBy)||null,ReqCreatedOn:((U=s==null?void 0:s.childRequestHeaderData)==null?void 0:U.ReqCreatedOn)||null,ReqUpdatedOn:((I=s==null?void 0:s.childRequestHeaderData)==null?void 0:I.ReqUpdatedOn)||null,RequestType:((ge=s==null?void 0:s.childRequestHeaderData)==null?void 0:ge.RequestType)||null,RequestPrefix:((te=s==null?void 0:s.childRequestHeaderData)==null?void 0:te.RequestPrefix)||null,RequestDesc:((He=s==null?void 0:s.childRequestHeaderData)==null?void 0:He.RequestDesc)||null,RequestPriority:((M=s==null?void 0:s.childRequestHeaderData)==null?void 0:M.RequestPriority)||null,RequestStatus:((we=s==null?void 0:s.childRequestHeaderData)==null?void 0:we.RequestStatus)||null,CurrentLevel:(l==null?void 0:l.ATTRIBUTE_3)||"",CurrentLevelName:(l==null?void 0:l.ATTRIBUTE_4)||"",ParticularLevel:(s==null?void 0:s.Level)||"-1",TaskName:(l==null?void 0:l.taskDesc)||"",ApproverGroup:(l==null?void 0:l.ATTRIBUTE_5)||""},Tomaterialdescription:T.map(qe=>{const W={...qe,Function:"UPD"};return Y.forEach(Ke=>delete W[Ke]),W}),Torequestheaderdata:(s==null?void 0:s.requestHeaderData)||{},Tomaterialerrordata:(s==null?void 0:s.errorData[g])||{},TemplateName:u,...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""},changeLogData:re(g,(F=(de=f[g])==null?void 0:de[0])==null?void 0:F.ChangeLogId)}})}else{const Y=["id","slNo","MatlType"];return Object.keys(f).map(g=>{var T,v,C,S,q,A,x,H,k,L,N,U,I,ge,te,He,M,we;return{Tomaterialdescription:f[g].map(de=>{const F={...de,Function:"UPD"};return Y.forEach(qe=>delete F[qe]),F}),Torequestheaderdata:{RequestId:(T=o==null?void 0:o.requestHeader)==null?void 0:T.requestId,ReqCreatedBy:(v=o==null?void 0:o.requestHeader)==null?void 0:v.reqCreatedBy,ReqCreatedOn:ws((C=o==null?void 0:o.requestHeader)==null?void 0:C.reqCreatedOn),ReqUpdatedOn:ws((S=o==null?void 0:o.requestHeader)==null?void 0:S.reqCreatedOn),RequestType:(q=o==null?void 0:o.requestHeader)==null?void 0:q.requestType,RequestPriority:(A=o==null?void 0:o.requestHeader)==null?void 0:A.requestPriority,RequestDesc:(x=o==null?void 0:o.requestHeader)==null?void 0:x.requestDesc,RequestStatus:(H=o==null?void 0:o.requestHeader)==null?void 0:H.requestStatus,FirstProd:((k=c==null?void 0:c.payloadData)==null?void 0:k.FirstProductionDate)||null,LaunchDate:((L=c==null?void 0:c.payloadData)==null?void 0:L.LaunchDate)||null,LeadingCat:(N=o==null?void 0:o.requestHeader)==null?void 0:N.leadingCat,Division:(U=o==null?void 0:o.requestHeader)==null?void 0:U.division,Region:(I=o==null?void 0:o.requestHeader)==null?void 0:I.region,TemplateName:(ge=o==null?void 0:o.requestHeader)==null?void 0:ge.templateName,FieldName:(te=o==null?void 0:o.requestHeader)==null?void 0:te.fieldName},Tochildrequestheaderdata:{},Toclientdata:{ClientId:null,Function:"UPD"},Material:g,MatlType:((we=(M=f[g])==null?void 0:M[((He=f[g])==null?void 0:He.length)-1])==null?void 0:we.MatlType)||"",TemplateName:u,IsFirstCreate:!0,Function:"UPD",...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""},changeLogData:re(g)}})}},oe=Q=>{const f=t.reduce((Y,z)=>{if((p==null?void 0:p.length)!==0&&!(p!=null&&p.includes(z==null?void 0:z.id)))return Y;const g=z==null?void 0:z.Material;return Y[g]||(Y[g]=[]),Y[g].push(z),Y},{});if(Q){const Y=["id","MaterialId","ClientId","slNo","ChangeLogId","MatlType"];return Object.keys(f).map(g=>{var C,S,q,A,x,H,k,L,N,U,I,ge,te,He,M,we,de;const T=f[g],{MaterialId:v}=T[0];return{MaterialId:v,Material:g,MassChildEditId:s==null?void 0:s.otherPayloadData.MassChildEditId,MatlType:((q=(S=f[g])==null?void 0:S[((C=f[g])==null?void 0:C.length)-1])==null?void 0:q.MatlType)||"",Function:"UPD",TaskId:(s==null?void 0:s.otherPayloadData.TaskId)||"",TaskName:(s==null?void 0:s.otherPayloadData.TaskName)||"",creationTime:(s==null?void 0:s.otherPayloadData.CreationTime)||"",dueDate:(s==null?void 0:s.otherPayloadData.DueDate)||"",IsFirstCreate:!1,MassEditId:s==null?void 0:s.otherPayloadData.MassEditId,TotalIntermediateTasks:s==null?void 0:s.otherPayloadData.TotalIntermediateTasks,IntermediateTaskCount:s==null?void 0:s.otherPayloadData.IntermediateTaskCount,Towarehousedata:T.map(F=>{const qe={...F,Function:"UPD"};return Y.forEach(W=>delete qe[W]),qe}),Tochildrequestheaderdata:{...s==null?void 0:s.childRequestHeaderData,ChildRequestId:((A=s==null?void 0:s.childRequestHeaderData)==null?void 0:A.ChildRequestId)||null,MaterialGroupType:((x=s==null?void 0:s.childRequestHeaderData)==null?void 0:x.MaterialGroupType)||null,TaskId:(l==null?void 0:l.taskId)||null,Comments:(s==null?void 0:s.Comments)||"",TotalIntermediateTasks:((H=s==null?void 0:s.childRequestHeaderData)==null?void 0:H.TotalIntermediateTasks)||null,IntermediateTaskCount:((k=s==null?void 0:s.childRequestHeaderData)==null?void 0:k.IntermediateTaskCount)||null,ReqCreatedBy:((L=s==null?void 0:s.childRequestHeaderData)==null?void 0:L.ReqCreatedBy)||null,ReqCreatedOn:((N=s==null?void 0:s.childRequestHeaderData)==null?void 0:N.ReqCreatedOn)||null,ReqUpdatedOn:((U=s==null?void 0:s.childRequestHeaderData)==null?void 0:U.ReqUpdatedOn)||null,RequestType:((I=s==null?void 0:s.childRequestHeaderData)==null?void 0:I.RequestType)||null,RequestPrefix:((ge=s==null?void 0:s.childRequestHeaderData)==null?void 0:ge.RequestPrefix)||null,RequestDesc:((te=s==null?void 0:s.childRequestHeaderData)==null?void 0:te.RequestDesc)||null,RequestPriority:((He=s==null?void 0:s.childRequestHeaderData)==null?void 0:He.RequestPriority)||null,RequestStatus:((M=s==null?void 0:s.childRequestHeaderData)==null?void 0:M.RequestStatus)||null,CurrentLevel:(l==null?void 0:l.ATTRIBUTE_3)||"",CurrentLevelName:(l==null?void 0:l.ATTRIBUTE_4)||"",ParticularLevel:(s==null?void 0:s.Level)||"-1",TaskName:(l==null?void 0:l.taskDesc)||"",ApproverGroup:(l==null?void 0:l.ATTRIBUTE_5)||""},Torequestheaderdata:(s==null?void 0:s.requestHeaderData)||{},Tomaterialerrordata:(s==null?void 0:s.errorData[g])||{},TemplateName:u,changeLogData:re(g,(de=(we=f[g])==null?void 0:we[0])==null?void 0:de.ChangeLogId),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}else{const Y=["id","slNo","MatlType"];return Object.keys(f).map(g=>{var T,v,C,S,q,A,x,H,k,L,N,U,I,ge,te,He,M,we;return{Towarehousedata:f[g].map(de=>{const F={...de,Function:"UPD"};return Y.forEach(qe=>delete F[qe]),F}),Torequestheaderdata:{RequestId:(T=o==null?void 0:o.requestHeader)==null?void 0:T.requestId,ReqCreatedBy:(v=o==null?void 0:o.requestHeader)==null?void 0:v.reqCreatedBy,ReqCreatedOn:ws((C=o==null?void 0:o.requestHeader)==null?void 0:C.reqCreatedOn),ReqUpdatedOn:ws((S=o==null?void 0:o.requestHeader)==null?void 0:S.reqCreatedOn),RequestType:(q=o==null?void 0:o.requestHeader)==null?void 0:q.requestType,RequestPriority:(A=o==null?void 0:o.requestHeader)==null?void 0:A.requestPriority,RequestDesc:(x=o==null?void 0:o.requestHeader)==null?void 0:x.requestDesc,RequestStatus:(H=o==null?void 0:o.requestHeader)==null?void 0:H.requestStatus,FirstProd:((k=c==null?void 0:c.payloadData)==null?void 0:k.FirstProductionDate)||null,LaunchDate:((L=c==null?void 0:c.payloadData)==null?void 0:L.LaunchDate)||null,LeadingCat:(N=o==null?void 0:o.requestHeader)==null?void 0:N.leadingCat,Division:(U=o==null?void 0:o.requestHeader)==null?void 0:U.division,Region:(I=o==null?void 0:o.requestHeader)==null?void 0:I.region,TemplateName:(ge=o==null?void 0:o.requestHeader)==null?void 0:ge.templateName,FieldName:(te=o==null?void 0:o.requestHeader)==null?void 0:te.fieldName},Tochildrequestheaderdata:{},Material:g,MatlType:((we=(M=f[g])==null?void 0:M[((He=f[g])==null?void 0:He.length)-1])==null?void 0:we.MatlType)||"",TemplateName:u,IsFirstCreate:!0,Function:"UPD",...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""},changeLogData:re(g)}})}};return{changePayloadForTemplate:ae}};var Ua={},rE=Go;Object.defineProperty(Ua,"__esModule",{value:!0});var cf=Ua.default=void 0,lE=rE(Bo()),iE=$o;cf=Ua.default=(0,lE.default)((0,iE.jsx)("path",{d:"m16 5-1.42 1.42-1.59-1.59V16h-1.98V4.83L9.42 6.42 8 5l4-4zm4 5v11c0 1.1-.9 2-2 2H6c-1.11 0-2-.9-2-2V10c0-1.11.89-2 2-2h3v2H6v11h12V10h-3V8h3c1.1 0 2 .89 2 2"}),"IosShareOutlined");const aE=Vn(bs)(({theme:e})=>({padding:e.spacing(2),border:"none",backgroundColor:"rgba(179, 236, 243, 0.5)"})),cE=Vn(Le)(({theme:e})=>{var t,r;return{backgroundColor:(r=(t=$e)==null?void 0:t.primary)==null?void 0:r.whiteSmoke,padding:e.spacing(1),border:"1px solid #E0E0E0",borderRadius:e.shape.borderRadius,boxShadow:"0px 8px 15px rgba(0, 0, 0, 0.08), 0px 4px 6px rgba(115, 118, 122, 0.5)",minWidth:120,textAlign:"center",fontWeight:"bold",color:e.palette.text.primary}}),dE=Vn(Le)(({theme:e})=>({display:"flex",justifyContent:"space-between",alignItems:"center",paddingLeft:e.spacing(2),backgroundColor:e.palette.grey[100],borderBottom:`1px solid ${e.palette.divider}`})),uE=Vn(Ia)(({theme:e})=>({borderRadius:e.shape.borderRadius,boxShadow:"0 4px 6px rgba(0, 0, 0, 0.1)"})),gh=4,hE=({open:e=!1,onClose:t=()=>{},handleOk:r=()=>{},message:o=""})=>{const c=se(ae=>ae.payload.matNoList||[]),s=(c==null?void 0:c.length)||0,p=d.useMemo(()=>{const ae=[];for(let y=0;y<c.length;y+=gh)ae.push(c.slice(y,y+gh));return ae},[c]),l=()=>{const ae=c==null?void 0:c.map((y,O)=>({id:O+1,material:y}));u.convertJsonToExcel(ae)},u={convertJsonToExcel:ae=>{let y=[];y.push({header:"Material",key:"material"}),Ra({fileName:"Material List",columns:y,rows:ae})}},re=()=>{t()};return R(un,{open:e,onClose:re,maxWidth:"md",PaperProps:{sx:{borderRadius:2,minWidth:"480px",maxHeight:"80vh"}},children:[R(Un,{sx:{display:"flex",alignItems:"center",gap:1,py:2},children:[n(dp,{fontSize:"medium",sx:{color:"0px 4px 6px rgba(115, 118, 122, 0.5)"}}),R(mt,{variant:"h6",fontWeight:"bold",children:["Info: ",o]})]}),R(Lo,{sx:{p:0},children:[R(dE,{children:[R(mt,{variant:"subtitle2",color:"text.secondary",sx:{marginLeft:"15px"},children:["Total Materials: ",n("strong",{children:s})]}),n(xs,{onClick:l,color:"primary",sx:{marginRight:"10px"},title:"Export Excel",children:n(cf,{})})]}),n(Le,{sx:{pt:0,pl:2,pr:2,pb:0},children:n(uE,{component:kr,children:n(va,{children:n(Ma,{children:p.map((ae,y)=>n(Hl,{sx:{"&:last-child td":{borderBottom:0}},children:n(aE,{children:n(Le,{sx:{display:"flex",flexWrap:"wrap",gap:2,justifyContent:"center"},children:ae.map(O=>n(cE,{children:O},O))})})},y))})})})})]}),R(Do,{sx:{p:2,borderTop:1,borderColor:"divider"},children:[n(Rt,{onClick:re,variant:"outlined",color:"warning",sx:{minWidth:100,textTransform:"none",fontWeight:"medium"},children:"Close"}),n(Rt,{onClick:r,variant:"contained",color:"primary",sx:{minWidth:100,textTransform:"none",fontWeight:"medium"},children:"Continue"})]})]})},ka=({initialReqScreen:e,isReqBench:t,remarks:r="",userInput:o="",selectedLevel:c=""})=>{var K;const s=se(m=>m.payload),p=(K=s==null?void 0:s.payloadData)==null?void 0:K.RequestType,l=se(m=>m.userManagement.taskData),u=se(m=>m.request),re=Rr(jn.CURRENT_TASK),ae=typeof re=="string"?JSON.parse(re):re,y=se(m=>m.changeLog.createChangeLogData);function O(m){const B=[];return Object.keys(m).forEach(ce=>{var oe,Q,f,Y,z,g,T,v,C,S,q,A,x,H,k,L,N,U,I,ge,te,He,M,we,de,F,qe,W,Ke,Ds,Gt,Gs,$s,ts,lo,Fs,Us,Kt,fs,Dt,rs,Jt,vs,rt,Be,It,$t,Ns,Ce,dt,ls,ks,is,ss,it,xt,Es,Ms,Qt,as,io,Qe,Lt,Ut,Nt,ms,Ks,Hs,nt,cs,Js,Ws,Zt,Ue,Pt,Ps,le,Ge,Ae,Re,Ve,ke,at,xe,Bt,gs,vt,ze,Pe,wt,kt,qs,Ft,ee,ye,G,ue,w,Te,je,ie,ut,Ye,lt,pt,_e,Tt,Ls,Ys,co,ds,ao,Po,Qs,Mt,Bs,Zs,_o,Fo,Io,uo,os,Wo,to,qo,jo,zo,Yo,Xo,Vo,Ko,pe,ht,ft,Ct,Oe,At,Ht,qt,Yt,us,Ee,ct,ps,Ts,No,Nn,wn,Rn,Dn,ir,ar,E,_,j,ne,Se,Fe,Ze,et,St,gt,Xt,tt,hs,eo,Wt,so,oo,Jo,Tn,cr,vr,Xl,Vr,Vl,Kl,Kr,Jl,Ql,Zl,ei,ti,si,oi,Jr,ni,Qr,Zr,ri,li,el,tl,sl,ol,nl,rl,ll,il,al,cl,dl,ul,hl,fl,gl,pl,Tl,El,i,h,D,$,X,Z,J,he,De,be,Ne,Me,st,yt,jt,Xs,no,ns,vo,cn,dn,_n,In,Qo,vn,Mn,On,Kn,Jn,Qn,Zn,ho,En,Zo,ln,er,tr,Bn,Os,bo,Mo,pc,Tc,Ec,mc,Cc,Ac,bc,Sc,Nc,wc,Rc,_c,Ic,vc,Mc,Oc,xc,yc,Lc,Dc,Pc,qc,Uc,kc,Hc,Bc,Gc,$c,Fc,Wc,jc,zc,Yc,Xc,Vc,Kc,Jc,Qc,Zc,ed,td,sd,od,nd,rd,ld,id,ad,cd,dd,ud,hd,fd,gd,pd,Td,Ed,md,Cd,Ad,bd,Sd,Nd,wd,Rd,_d,Id,vd,Md,Od,xd,yd,Ld,Dd,Pd,qd,Ud,kd,Hd,Bd,Gd,$d,Fd,Wd,jd,zd,Yd,Xd,Vd,Kd,Jd,Qd,Zd,eu,tu,su,ou,nu,ru,lu,iu,au,cu,du,uu,hu,fu,gu,pu,Tu,Eu,mu,Cu,Au,bu;if(ce.includes("-")||/\d/.test(ce)){const a=m[ce];if((oe=a==null?void 0:a.headerData)!=null&&oe.included){const ii=up(((Q=a==null?void 0:a.headerData)==null?void 0:Q.orgData)||[]),Ag=(((Y=(f=a==null?void 0:a.headerData)==null?void 0:f.views)==null?void 0:Y.filter(Ie=>!Ui.includes(Ie)))||[]).join(",").trim(),So=(z=a==null?void 0:a.payloadData)!=null&&z.Sales?Object.values(a.payloadData.Sales):[],Su=new Set,bg=ii.filter(Ie=>{var Ot,es,bt;if(!((Ot=Ie.salesOrg)!=null&&Ot.code)||!((bt=(es=Ie.dc)==null?void 0:es.value)!=null&&bt.code))return!1;const ot=`${Ie.salesOrg.code}-${Ie.dc.value.code}`;return Su.has(ot)?!1:(Su.add(ot),!0)}).map((Ie,ot)=>{var Ot,es,bt,js,me,zs,Et,en,Cs,zt,ml,Cl,Al,bl,Sl,Nl,wl,Rl,_l,Il,vl,Ml,Ol;return{SalesId:p===b.EXTEND&&e?null:((Ot=So[ot])==null?void 0:Ot.SalesId)||"",Function:"INS",Material:((es=a.headerData)==null?void 0:es.materialNumber)||"",SalesOrg:((bt=Ie.salesOrg)==null?void 0:bt.code)||"",DistrChan:((me=(js=Ie.dc)==null?void 0:js.value)==null?void 0:me.code)||"",DelFlag:!1,MatlStats:((zs=So[ot])==null?void 0:zs.MatlStats)||"",RebateGrp:((Et=So[ot])==null?void 0:Et.RebateGrp)||"",CashDisc:((en=So[ot])==null?void 0:en.CashDisc)||!0,SalStatus:((Cs=So[ot])==null?void 0:Cs.SalStatus)||"",DelyUnit:"0.000",ValidFrom:(zt=So[ot])!=null&&zt.ValidFrom?ws((ml=So[ot])==null?void 0:ml.ValidFrom):null,DelyUom:((Cl=So[ot])==null?void 0:Cl.DelyUom)||"",DelygPlnt:((Al=So[ot])==null?void 0:Al.DelygPlnt)||"",MatPrGrp:((bl=So[ot])==null?void 0:bl.MatPrGrp)||"",AcctAssgt:((Sl=So[ot])==null?void 0:Sl.AcctAssgt)||"",MatlGrp4:((Nl=So[ot])==null?void 0:Nl.MatlGrp4)||"",MatlGrp2:((wl=So[ot])==null?void 0:wl.MatlGrp2)||"",MatlGrp5:((Rl=So[ot])==null?void 0:Rl.MatlGrp5)||"",BatchMgmt:((_l=So[ot])==null?void 0:_l.BatchMgmt)||"",Countryori:((Il=So[ot])==null?void 0:Il.Countryori)||"",Depcountry:((vl=So[ot])==null?void 0:vl.Depcountry)||"",SalesUnit:((Ml=So[ot])==null?void 0:Ml.SalesUnit)||"",ItemCat:((Ol=So[ot])==null?void 0:Ol.ItemCat)||""}}),Sg=(g=a==null?void 0:a.payloadData)!=null&&g.Purchasing?Object.entries(a.payloadData.Purchasing):[],Ng=(T=a==null?void 0:a.payloadData)!=null&&T.MRP?Object.entries(a.payloadData.MRP):[],wg=(v=a==null?void 0:a.payloadData)!=null&&v[P.SALES_PLANT]?Object.entries((C=a.payloadData)==null?void 0:C[P.SALES_PLANT]):[],Rg=(S=a==null?void 0:a.payloadData)!=null&&S[P.STORAGE_PLANT]?Object.entries((q=a.payloadData)==null?void 0:q[P.STORAGE_PLANT]):[],_g=(A=a==null?void 0:a.payloadData)!=null&&A.Accounting?Object.entries(a.payloadData.Accounting):[];let Nu=[];if(((L=(k=(H=(x=a==null?void 0:a.payloadData)==null?void 0:x.TaxData)==null?void 0:H.TaxData)==null?void 0:k.TaxDataSet)==null?void 0:L.length)>0){const Ie={};(ge=(I=(U=(N=a==null?void 0:a.payloadData)==null?void 0:N.TaxData)==null?void 0:U.TaxData)==null?void 0:I.TaxDataSet)==null||ge.forEach(ot=>{var bt,js;const Ot=ot.Country;Ie[Ot]||(Ie[Ot]={ControlId:ot.ControlId??null,Function:"INS",Material:((bt=a.headerData)==null?void 0:bt.materialNumber)||"",Depcountry:ot.Country});const es=Object.keys(Ie[Ot]).filter(me=>me.startsWith("TaxType")).length+1;es<=9&&(Ie[Ot][`TaxType${es}`]=ot.TaxType,Ie[Ot][`Taxclass${es}`]=((js=ot.SelectedTaxClass)==null?void 0:js.TaxClass)||"")}),Nu=Object.values(Ie)}const wu=(te=a==null?void 0:a.payloadData)!=null&&te.Costing?Object.entries(a.payloadData.Costing):[],Ig=((He=a==null?void 0:a.headerData)==null?void 0:He.orgData)||[],Ru=new Set,vg=Ig.filter(Ie=>{var Ot,es;if(!((es=(Ot=Ie.plant)==null?void 0:Ot.value)!=null&&es.code))return!1;const ot=Ie.plant.value.code;return Ru.has(ot)?!1:(Ru.add(ot),!0)}).map(Ie=>{var bt,js,me,zs,Et;const ot=(js=(bt=Ie.plant)==null?void 0:bt.value)==null?void 0:js.code,Ot=((me=_g.find(([en])=>en===ot))==null?void 0:me[1])||{},es=((zs=wu.find(([en])=>en===ot))==null?void 0:zs[1])||{};return{...Ot,AccountingId:Ot.AccountingId||es.AccountingId||null,Function:"INS",Material:((Et=a.headerData)==null?void 0:Et.materialNumber)||"",DelFlag:"",PriceCtrl:Ot.PriceCtrl||"",MovingPr:Ot.MovingPr||es.MovingPr||"",StdPrice:Ot.StdPrice||es.StdPrice||"",PriceUnit:Ot.PriceUnit||"",ValClass:Ot.ValClass||"",OrigMat:es.OrigMat===!0||es.OrigMat==="X"||es.OrigMat==="TRUE"?"X":"",ValArea:ot||""}}),Mg=(M=a==null?void 0:a.payloadData)!=null&&M.Warehouse?Object.entries(a.payloadData.Warehouse):[],Og=(F=(we=a.headerData)==null?void 0:we.views)!=null&&F.includes((de=P)==null?void 0:de.WAREHOUSE)?Mg.map(([Ie,ot],Ot)=>{var es;return{WarehouseId:ot.WarehouseId||"",Function:ot.Function||"",Material:((es=a.headerData)==null?void 0:es.materialNumber)||"",DelFlag:ot.DelFlag||!0,WhseNo:Ie||"",SpecMvmt:ot.SpecMvmt||"",LEquip1:ot.LEquip1||"",LeqUnit1:ot.LeqUnit1||"",Unittype1:ot.Unittype1||"",Placement:ot.Placement||""}}):[],Xi=(W=a==null?void 0:a.payloadData)!=null&&W[(qe=P)==null?void 0:qe.STORAGE]?Object.values((Ds=a.payloadData)==null?void 0:Ds[(Ke=P)==null?void 0:Ke.STORAGE]):[],_u=new Set,xg=ii.filter(Ie=>{var Ot,es,bt,js,me,zs,Et,en;if(!((es=(Ot=Ie.plant)==null?void 0:Ot.value)!=null&&es.code)||!((js=(bt=Ie.sloc)==null?void 0:bt.value)!=null&&js.code))return!1;const ot=`${(zs=(me=Ie.plant)==null?void 0:me.value)==null?void 0:zs.code}-${(en=(Et=Ie.sloc)==null?void 0:Et.value)==null?void 0:en.code}`;return _u.has(ot)?!1:(_u.add(ot),!0)}).map((Ie,ot)=>{var Ot,es,bt,js,me,zs,Et,en;return{StorageLocationId:((Ot=Xi[ot])==null?void 0:Ot.StorageLocationId)||"",Plant:((bt=(es=Ie.plant)==null?void 0:es.value)==null?void 0:bt.code)||"",StgeLoc:((me=(js=Ie.sloc)==null?void 0:js.value)==null?void 0:me.code)||"",Material:((zs=a.headerData)==null?void 0:zs.materialNumber)||"",PickgArea:((Et=Xi[ot])==null?void 0:Et.PickgArea)||"",StgeBin:((en=Xi[ot])==null?void 0:en.StgeBin)||""}}),xn=(Gs=(Gt=a.headerData)==null?void 0:Gt.views)!=null&&Gs.includes(P.CLASSIFICATION)?($s=a==null?void 0:a.payloadData)==null?void 0:$s[P.CLASSIFICATION]:{};let Iu=[];xn&&xn.basic&&xn.basic.Classtype&&xn.basic.Classnum&&Array.isArray(xn.classification)&&Iu.push({ClassificationId:((ts=xn==null?void 0:xn.basic)==null?void 0:ts.ClassificationId)||"",Classnum:xn.basic.Classnum,Classtype:xn.basic.Classtype,Object:((lo=a.headerData)==null?void 0:lo.materialNumber)||"",Tochars:(Fs=xn.classification)==null?void 0:Fs.map(Ie=>({CharacteristicsId:(Ie==null?void 0:Ie.CharacteristicsId)||"",Charact:Ie.characteristic,CharactDescr:Ie.description,Tocharvalues:[{ValueChar:Ie.value||""}]}))});const yg=(Us=a==null?void 0:a.payloadData)!=null&&Us[P.WORKSCHEDULING]?Object.entries((Kt=a.payloadData)==null?void 0:Kt[P.WORKSCHEDULING]):[],Lg=(fs=a==null?void 0:a.payloadData)!=null&&fs[P.BOM]?Object.entries((Dt=a.payloadData)==null?void 0:Dt[P.BOM]):[],Dg=(rs=a==null?void 0:a.payloadData)!=null&&rs[P.SOURCE_LIST]?Object.entries((Jt=a.payloadData)==null?void 0:Jt[P.SOURCE_LIST]):[],Pg=(((vs=a==null?void 0:a.headerData)==null?void 0:vs.orgData)||[]).filter((Ie,ot,Ot)=>ot===(Ot==null?void 0:Ot.findIndex(es=>{var bt,js,me,zs;return((js=(bt=es.plant)==null?void 0:bt.value)==null?void 0:js.code)===((zs=(me=Ie==null?void 0:Ie.plant)==null?void 0:me.value)==null?void 0:zs.code)}))).map((Ie,ot)=>{var ml,Cl,Al,bl,Sl,Nl,wl,Rl,_l,Il,vl,Ml,Ol,Mu,Ou,xu,yu,Lu,Du,Pu,qu,Uu,ku,Hu,Bu,Gu,$u,Fu,Wu,ju,zu;const Ot=(Cl=(ml=Ie.plant)==null?void 0:ml.value)==null?void 0:Cl.code,es=(Al=Ie.mrpProfile)==null?void 0:Al.code,bt=((bl=Sg.find(([tn])=>tn===Ot))==null?void 0:bl[1])||{},js=((Sl=wu.find(([tn])=>tn===Ot))==null?void 0:Sl[1])||{},me=((Nl=Ng.find(([tn])=>tn.startsWith(Ot)))==null?void 0:Nl[1])||{},zs=((wl=wg.find(([tn])=>tn===Ot))==null?void 0:wl[1])||{},Et=((Rl=Rg.find(([tn])=>tn===Ot))==null?void 0:Rl[1])||{},en=((_l=yg.find(([tn])=>tn===Ot))==null?void 0:_l[1])||{},Cs=((Il=Lg.find(([tn])=>tn===Ot))==null?void 0:Il[1])||{},zt=((vl=Dg.find(([tn])=>tn===Ot))==null?void 0:vl[1])||{};return{PlantId:p===b.EXTEND&&e?null:((Mu=(Ol=(Ml=a.payloadData)==null?void 0:Ml.Purchasing)==null?void 0:Ol[Ot])==null?void 0:Mu.PlantId)??null,Function:"INS",Material:((Ou=a.headerData)==null?void 0:Ou.materialNumber)||"",Plant:Ot||"",DelFlag:!1,CritPart:!1,PurGroup:(bt==null?void 0:bt.PurGroup)||"",PurStatus:(bt==null?void 0:bt.PurStatus)||"",RoundProf:(me==null?void 0:me.RoundProf)||"",IssueUnit:"",IssueUnitIso:"",Mrpprofile:es||"",MrpType:(me==null?void 0:me.MrpType)||"",MrpCtrler:(me==null?void 0:me.MrpCtrler)||"",PlndDelry:(me==null?void 0:me.PlndDelry)||"",GrPrTime:(me==null?void 0:me.GrPrTime)||"",PeriodInd:(me==null?void 0:me.PeriodInd)||"",Lotsizekey:(me==null?void 0:me.Lotsizekey)||"",ProcType:(me==null?void 0:me.ProcType)||"",Consummode:(me==null?void 0:me.Consummode)||"",FwdCons:(me==null?void 0:me.FwdCons)||"",ReorderPt:(me==null?void 0:me.ReorderPt)||"",MaxStock:(me==null?void 0:me.MaxStock)||"",SafetyStk:(me==null?void 0:me.SafetyStk)||"",Minlotsize:(me==null?void 0:me.Minlotsize)||"",PlanStrgp:(me==null?void 0:me.PlanStrgp)||"",BwdCons:(me==null?void 0:me.BwdCons)||"",Maxlotsize:(me==null?void 0:me.Maxlotsize)||"",FixedLot:(me==null?void 0:me.FixedLot)||"",RoundVal:(me==null?void 0:me.RoundVal)||"",GrpReqmts:(me==null?void 0:me.GrpReqmts)||"",MixedMrp:(me==null?void 0:me.MixedMrp)||"",SmKey:(me==null?void 0:me.SmKey)||"",Backflush:(me==null?void 0:me.Backflush)||"",AssyScarp:(me==null?void 0:me.AssyScarp)||"",Replentime:(me==null?void 0:me.Replentime)||"",PlTiFnce:(me==null?void 0:me.PlTiFnce)||"",ReplacePt:"",IndPostToInspStock:(bt==null?void 0:bt.IndPostToInspStock)===!0||(bt==null?void 0:bt.IndPostToInspStock)==="X"||(bt==null?void 0:bt.IndPostToInspStock)==="TRUE"?"X":"",HtsCode:(bt==null?void 0:bt.HtsCode)||"",CtrlKey:"",BatchMgmt:(me==null?void 0:me.BatchMgmt)||!1,DepReqId:(me==null?void 0:me.DepReqId)||"",SaftyTId:(me==null?void 0:me.SaftyTId)||"",Safetytime:(me==null?void 0:me.Safetytime)||"",Matfrgtgrp:(zs==null?void 0:zs.Matfrgtgrp)||"",Availcheck:(zs==null?void 0:zs.Availcheck)||"",ProfitCtr:(zs==null?void 0:zs.ProfitCtr)||"",Loadinggrp:(zs==null?void 0:zs.Loadinggrp)||"",MinLotSize:(me==null?void 0:me.MinLotSize)||"",MaxLotSize:(me==null?void 0:me.MaxLotSize)||"",FixLotSize:(me==null?void 0:me.FixLotSize)||"",AssyScrap:(me==null?void 0:me.AssyScrap)||"",IssStLoc:(me==null?void 0:me.IssStLoc)||"",SalesView:((yu=a==null?void 0:a.headerData)==null?void 0:yu.views.includes((xu=P)==null?void 0:xu.SALES))||!1,PurchView:((Du=a==null?void 0:a.headerData)==null?void 0:Du.views.includes((Lu=P)==null?void 0:Lu.PURCHASING))||!1,MrpView:((qu=a==null?void 0:a.headerData)==null?void 0:qu.views.includes((Pu=P)==null?void 0:Pu.MRP))||!1,WorkSchedView:((ku=a==null?void 0:a.headerData)==null?void 0:ku.views.includes((Uu=P)==null?void 0:Uu.WORK_SCHEDULING_2))||!1,WarehouseView:((Bu=a==null?void 0:a.headerData)==null?void 0:Bu.views.includes((Hu=P)==null?void 0:Hu.WAREHOUSE))||!1,AccountView:(($u=a==null?void 0:a.headerData)==null?void 0:$u.views.includes((Gu=P)==null?void 0:Gu.ACCOUNTING))||!1,CostView:((Wu=a==null?void 0:a.headerData)==null?void 0:Wu.views.includes((Fu=P)==null?void 0:Fu.COSTING))||!1,ForecastView:!1,PrtView:!1,StorageView:((zu=a==null?void 0:a.headerData)==null?void 0:zu.views.includes((ju=P)==null?void 0:ju.STORAGE))||!1,QualityView:!1,GrProcTime:"",GiProcTime:"",StorageCost:"",LotSizeUom:"",LotSizeUomIso:"",Unlimited:en.Unlimited||"",ProdProf:en.ProdProf||"",VarianceKey:js.VarianceKey||"",PoUnit:"",Spproctype:me.Spproctype||"",CommCode:(bt==null?void 0:bt.CommCode)||"",CommCoUn:(bt==null?void 0:bt.CommCoUn)||"",Countryori:bt==null?void 0:bt.Countryori,LotSize:js.LotSize||"",SlocExprc:me.SlocExprc||"",Inhseprodt:js.Inhseprodt||"",BomUsage:(Cs==null?void 0:Cs.BomUsage)||"",AltBom:(Cs==null?void 0:Cs.AltBom)||"",Category:(Cs==null?void 0:Cs.Category)||"",Component:(Cs==null?void 0:Cs.Component)||"",Quantity:(Cs==null?void 0:Cs.Quantity)||"",CompUom:(Cs==null?void 0:Cs.CompUom)||"",Bvalidfrom:Cs!=null&&Cs.Bvalidfrom?ws(Cs==null?void 0:Cs.Bvalidfrom):ws(new Date().toISOString()),Bvalidto:Cs!=null&&Cs.Bvalidto?ws(Cs==null?void 0:Cs.Bvalidto):ws(new Date().toISOString()),Supplier:(zt==null?void 0:zt.Supplier)||"",PurchaseOrg:(zt==null?void 0:zt.PurchaseOrg)||"",ProcurementPlant:(zt==null?void 0:zt.ProcurementPlant)||"",SOrderUnit:(zt==null?void 0:zt.SOrderUnit)||"",Agreement:(zt==null?void 0:zt.Agreement)||"",AgreementItem:(zt==null?void 0:zt.AgreementItem)||"",FixedSupplySource:(zt==null?void 0:zt.FixedSupplySource)||"",Blocked:(zt==null?void 0:zt.Blocked)||"",SMrp:(zt==null?void 0:zt.SMrp)||"",Slvalidfrom:zt!=null&&zt.Slvalidfrom?ws(zt==null?void 0:zt.Slvalidfrom):ws(new Date().toISOString()),Slvalidto:zt!=null&&zt.Slvalidto?ws(zt==null?void 0:zt.Slvalidto):ws(new Date().toISOString()),CcPhInv:(Et==null?void 0:Et.CcPhInv)||"",CcFixed:(Et==null?void 0:Et.CcFixed)||"",StgePdUn:(Et==null?void 0:Et.StgePdUn)||"",DefaultStockSegment:(Et==null?void 0:Et.DefaultStockSegment)||"",NegStocks:(Et==null?void 0:Et.NegStocks)||"",SernoProf:(Et==null?void 0:Et.SernoProf)||"",DistrProf:(Et==null?void 0:Et.DistrProf)||"",DetermGrp:(Et==null?void 0:Et.DetermGrp)||"",IuidRelevant:(Et==null?void 0:Et.IuidRelevant)||"",UidIea:(Et==null?void 0:Et.UidIea)||"",IuidType:(Et==null?void 0:Et.IuidType)||"",IuidRelevant:(Et==null?void 0:Et.IuidRelevant)||"",SortStockBasedOnSegment:(Et==null?void 0:Et.SortStockBasedOnSegment)||"",SegmentationStrategy:(Et==null?void 0:Et.SegmentationStrategy)||"",IssueUnit:(Et==null?void 0:Et.IssueUnit)||""}}),Mr=(a==null?void 0:a.additionalData)||[],Or=(a==null?void 0:a.unitsOfMeasureData)||[],xr=(a==null?void 0:a.eanData)||[],qg=Mr!=null&&Mr.length?Mr==null?void 0:Mr.map(Ie=>{var ot;return{MaterialDescriptionId:p===b.EXTEND&&e?null:Ie.MaterialDescriptionId||null,Function:"INS",Material:((ot=a.headerData)==null?void 0:ot.materialNumber)||"",Langu:Ie.language||"EN",LanguIso:"",MatlDesc:(Ie==null?void 0:Ie.materialDescription)||"",DelFlag:!1}}):[{MaterialDescriptionId:null,Function:"INS",Material:((rt=a.headerData)==null?void 0:rt.materialNumber)||"",Langu:"EN",LanguIso:"",MatlDesc:((Be=a.headerData)==null?void 0:Be.globalMaterialDescription)||"",DelFlag:!1}],Ug=Or!=null&&Or.length?Or==null?void 0:Or.map(Ie=>{var ot;return{UomId:p===b.EXTEND&&e?null:(Ie==null?void 0:Ie.UomId)||null,Function:"INS",Material:((ot=a.headerData)==null?void 0:ot.materialNumber)||"",AltUnit:(Ie==null?void 0:Ie.aUnit)||"",AltUnitIso:"",Numerator:(Ie==null?void 0:Ie.yValue)||"1",Denominatr:(Ie==null?void 0:Ie.xValue)||"1",EanUpc:(Ie==null?void 0:Ie.eanUpc)||"",EanCat:Ie.eanCategory||"",Length:Ie.length,NetWeight:Ie.netWeight||"",Width:Ie.width,Height:Ie.height,UnitDim:Ie.unitsOfDimension||"",UnitDimIso:"",Volume:Ie.volume||"",Volumeunit:Ie.volumeUnit||"",VolumeunitIso:"",GrossWt:Ie.grossWeight||"",UnitOfWt:Ie.weightUnit||"",UnitOfWtIso:"",DelFlag:!1,SubUom:"",SubUomIso:"",GtinVariant:"",MaterialExternal:null,MaterialGuid:null,MaterialVersion:null,NestingFactor:"",MaximumStacking:null,CapacityUsage:Ie.capacityUsage,EwmCwUomType:"",MaterialLong:null}}):[{UomId:null,Function:"INS",Material:((It=a.headerData)==null?void 0:It.materialNumber)||"",AltUnit:((Ce=(Ns=($t=a==null?void 0:a.payloadData)==null?void 0:$t["Basic Data"])==null?void 0:Ns.basic)==null?void 0:Ce.BaseUom)||"",AltUnitIso:"",Numerator:"1",Denominatr:"1",EanUpc:"",EanCat:"",Length:"",Width:"",Height:"",UnitDim:"",UnitDimIso:"",Volume:((ks=(ls=(dt=a==null?void 0:a.payloadData)==null?void 0:dt["Basic Data"])==null?void 0:ls.basic)==null?void 0:ks.Volume)||"",Volumeunit:((it=(ss=(is=a==null?void 0:a.payloadData)==null?void 0:is["Basic Data"])==null?void 0:ss.basic)==null?void 0:it.VolumeUnit)||"",VolumeunitIso:"",GrossWt:"",UnitOfWt:((Ms=(Es=(xt=a==null?void 0:a.payloadData)==null?void 0:xt["Basic Data"])==null?void 0:Es.basic)==null?void 0:Ms.UnitOfWt)||"",UnitOfWtIso:"",DelFlag:!1,SubUom:"",SubUomIso:"",GtinVariant:"",MaterialExternal:null,MaterialGuid:null,MaterialVersion:null,NestingFactor:"",MaximumStacking:null,CapacityUsage:"",EwmCwUomType:"",MaterialLong:null}],kg=xr!=null&&xr.length?xr==null?void 0:xr.map(Ie=>{var ot;return{EanId:p===b.EXTEND&&e?null:(Ie==null?void 0:Ie.EanId)||null,Function:"INS",Material:((ot=a.headerData)==null?void 0:ot.materialNumber)||"",Unit:(Ie==null?void 0:Ie.altunit)||"",EanUpc:(Ie==null?void 0:Ie.eanUpc)||"",EanCat:(Ie==null?void 0:Ie.eanCategory)||"",MainEan:Ie.MainEan||!1,Au:Ie.au||!1}}):null,vu=new Set;(Qt=a==null?void 0:a.payloadData)!=null&&Qt.Tostroragelocationdata?(as=a==null?void 0:a.payloadData)==null||as.Tostroragelocationdata:(Lt=(io=a.headerData)==null?void 0:io.views)!=null&&Lt.includes((Qe=P)==null?void 0:Qe.STORAGE)&&ii.filter(Ie=>{var Ot,es,bt,js;if(!((es=(Ot=Ie==null?void 0:Ie.plant)==null?void 0:Ot.value)!=null&&es.code)||!((js=(bt=Ie==null?void 0:Ie.sloc)==null?void 0:bt.value)!=null&&js.code))return!1;const ot=`${Ie.plant.value.code}-${Ie.sloc.value.code}`;return vu.has(ot)?!1:(vu.add(ot),!0)}).map(Ie=>{var ot,Ot,es,bt,js;return{Function:"INS",Material:((ot=a==null?void 0:a.headerData)==null?void 0:ot.materialNumber)||"",Plant:((es=(Ot=Ie==null?void 0:Ie.plant)==null?void 0:Ot.value)==null?void 0:es.code)||"",StgeLoc:((js=(bt=Ie==null?void 0:Ie.sloc)==null?void 0:bt.value)==null?void 0:js.code)||""}});const Hg={ChildRequestId:((Ut=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:Ut.ChildRequestId)||null,MaterialGroupType:((Nt=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:Nt.MaterialGroupType)||null,TaskId:(l==null?void 0:l.taskId)||null,Comments:r||o,TotalIntermediateTasks:((ms=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:ms.TotalIntermediateTasks)||null,IntermediateTaskCount:((Ks=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:Ks.IntermediateTaskCount)||null,ReqCreatedBy:((Hs=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:Hs.ReqCreatedBy)||null,ReqCreatedOn:((nt=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:nt.ReqCreatedOn)||null,ReqUpdatedOn:((cs=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:cs.ReqUpdatedOn)||null,RequestType:((Js=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:Js.RequestType)||null,RequestPrefix:((Ws=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:Ws.RequestPrefix)||null,RequestDesc:((Zt=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:Zt.RequestDesc)||null,RequestPriority:((Ue=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:Ue.RequestPriority)||null,RequestStatus:((Pt=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:Pt.RequestStatus)||null,CurrentLevel:(l==null?void 0:l.ATTRIBUTE_3)||"",CurrentLevelName:(l==null?void 0:l.ATTRIBUTE_4)||"",ParticularLevel:c,TaskName:(l==null?void 0:l.taskDesc)||"",ApproverGroup:(l==null?void 0:l.ATTRIBUTE_5)||""},Bg={MaterialId:p===b.EXTEND&&e||p===b.CREATE&&e?null:(l!=null&&l.taskId||t||ae!=null&&ae.ATTRIBUTE_5)&&!ce.includes("-")?Number(ce):null,Flag:"",Function:"INS",Material:((Ps=a==null?void 0:a.headerData)==null?void 0:Ps.materialNumber)||"",MatlType:((Ge=(le=a==null?void 0:a.headerData)==null?void 0:le.materialType)==null?void 0:Ge.code)||((Ae=a==null?void 0:a.headerData)==null?void 0:Ae.materialType)||"",IndSector:((Ve=(Re=a==null?void 0:a.headerData)==null?void 0:Re.industrySector)==null?void 0:Ve.code)||((ke=a==null?void 0:a.headerData)==null?void 0:ke.industrySector)||"",Comments:r||o,ViewNames:Ag,Description:((at=a==null?void 0:a.headerData)==null?void 0:at.globalMaterialDescription)||"",Bom:((xe=a==null?void 0:a.headerData)==null?void 0:xe.Bom)||"",SourceList:((Bt=a==null?void 0:a.headerData)==null?void 0:Bt.sourceList)||"",Pir:((gs=a==null?void 0:a.headerData)==null?void 0:gs.PIR)||"",Uom:(ze=(vt=a==null?void 0:a.headerData)==null?void 0:vt.Uom)!=null&&ze.code?a.headerData.Uom.code:((Pe=a==null?void 0:a.headerData)==null?void 0:Pe.Uom)||"",Category:(kt=(wt=a==null?void 0:a.headerData)==null?void 0:wt.Category)!=null&&kt.code?a.headerData.Category.code:((qs=a==null?void 0:a.headerData)==null?void 0:qs.Category)||"",Relation:(ee=(Ft=a==null?void 0:a.headerData)==null?void 0:Ft.Relation)!=null&&ee.code?a.headerData.Relation.code:((ye=a==null?void 0:a.headerData)==null?void 0:ye.Relation)||"",Usage:((G=a==null?void 0:a.headerData)==null?void 0:G.Usage)||"",CreationDate:l!=null&&l.requestId||t||(ue=m==null?void 0:m.payloadData)!=null&&ue.RequestId?ws((w=m==null?void 0:m.payloadData)==null?void 0:w.ReqCreatedOn):`/Date(${Date.now()}+0000)/`,EditId:null,ExtendId:null,MassCreationId:p===b.CREATE||p===b.CREATE_WITH_UPLOAD?l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(Te=m==null?void 0:m.payloadData)!=null&&Te.RequestId?(je=m==null?void 0:m.payloadData)==null?void 0:je.RequestId:"":null,MassEditId:((ie=a==null?void 0:a.payloadData)==null?void 0:ie.MassEditId)||"",MassExtendId:p===b.EXTEND||p===b.EXTEND_WITH_UPLOAD?l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(ut=m==null?void 0:m.payloadData)!=null&&ut.RequestId?(Ye=m==null?void 0:m.payloadData)==null?void 0:Ye.RequestId:(lt=u==null?void 0:u.requestHeader)!=null&&lt.requestId?(pt=u==null?void 0:u.requestHeader)==null?void 0:pt.requestId:requestId.slice(3):null,TaskId:(l==null?void 0:l.taskId)||null,TaskName:(l==null?void 0:l.taskDesc)||null,TotalIntermediateTasks:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(_e=m==null?void 0:m.payloadData)!=null&&_e.RequestId?(Tt=m==null?void 0:m.payloadData)==null?void 0:Tt.TotalIntermediateTasks:"",IntermediateTaskCount:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(Ls=m==null?void 0:m.payloadData)!=null&&Ls.RequestId?(Ys=m==null?void 0:m.payloadData)==null?void 0:Ys.IntermediateTaskCount:"",BasicView:((ds=a==null?void 0:a.headerData)==null?void 0:ds.views.includes((co=P)==null?void 0:co.BASIC_DATA))||!1,SalesView:((Po=a==null?void 0:a.headerData)==null?void 0:Po.views.includes((ao=P)==null?void 0:ao.SALES))||!1,MrpView:((Mt=a==null?void 0:a.headerData)==null?void 0:Mt.views.includes((Qs=P)==null?void 0:Qs.MRP))||!1,PurchaseView:((Zs=a==null?void 0:a.headerData)==null?void 0:Zs.views.includes((Bs=P)==null?void 0:Bs.PURCHASING))||!1,AccountView:((Fo=a==null?void 0:a.headerData)==null?void 0:Fo.views.includes((_o=P)==null?void 0:_o.ACCOUNTING))||!1,CostView:((uo=a==null?void 0:a.headerData)==null?void 0:uo.views.includes((Io=P)==null?void 0:Io.COSTING))||!1,WorkSchedView:((Wo=a==null?void 0:a.headerData)==null?void 0:Wo.views.includes((os=P)==null?void 0:os.WORK_SCHEDULING_2))||!1,WarehouseView:((qo=a==null?void 0:a.headerData)==null?void 0:qo.views.includes((to=P)==null?void 0:to.WAREHOUSE))||!1,ForecastView:!1,PrtView:!1,StorageView:((zo=a==null?void 0:a.headerData)==null?void 0:zo.views.includes((jo=P)==null?void 0:jo.STORAGE))||!1,QualityView:!1,IsFirstChangeLogCommit:!1,IsMarkedForDeletion:!1,IsFirstCreate:!(l!=null&&l.taskId),creationTime:l!=null&&l.createdOn?ws(l==null?void 0:l.createdOn):null,dueDate:l!=null&&l.criticalDeadline?ws(l==null?void 0:l.criticalDeadline):null,ManufacturerId:(a==null?void 0:a.ManufacturerID)||"",OrgData:ii||[],Toclientdata:{ClientId:((Yo=a==null?void 0:a.headerData)==null?void 0:Yo.clientId)||null,Function:"INS",Material:((Xo=a==null?void 0:a.headerData)==null?void 0:Xo.materialNumber)||"",DelFlag:!0,MatlGroup:((pe=(Ko=(Vo=a==null?void 0:a.payloadData)==null?void 0:Vo["Basic Data"])==null?void 0:Ko.basic)==null?void 0:pe.MatlGroup)||"",Extmatlgrp:((Ct=(ft=(ht=a==null?void 0:a.payloadData)==null?void 0:ht["Basic Data"])==null?void 0:ft.basic)==null?void 0:Ct.Extmatlgrp)||"",OldMatNo:((Ht=(At=(Oe=a==null?void 0:a.payloadData)==null?void 0:Oe["Basic Data"])==null?void 0:At.basic)==null?void 0:Ht.OldMatNo)||"",BaseUom:((us=(Yt=(qt=a==null?void 0:a.payloadData)==null?void 0:qt["Basic Data"])==null?void 0:Yt.basic)==null?void 0:us.BaseUom)||"",Document:(ps=(ct=(Ee=a==null?void 0:a.payloadData)==null?void 0:Ee["Basic Data"])==null?void 0:ct.basic)==null?void 0:ps.Document,DocType:(Nn=(No=(Ts=a==null?void 0:a.payloadData)==null?void 0:Ts["Basic Data"])==null?void 0:No.basic)==null?void 0:Nn.DocType,DocVers:(Dn=(Rn=(wn=a==null?void 0:a.payloadData)==null?void 0:wn["Basic Data"])==null?void 0:Rn.basic)==null?void 0:Dn.DocVers,DocFormat:(E=(ar=(ir=a==null?void 0:a.payloadData)==null?void 0:ir["Basic Data"])==null?void 0:ar.basic)==null?void 0:E.DocFormat,DocChgNo:(ne=(j=(_=a==null?void 0:a.payloadData)==null?void 0:_["Basic Data"])==null?void 0:j.basic)==null?void 0:ne.DocChgNo,PageNo:(Ze=(Fe=(Se=a==null?void 0:a.payloadData)==null?void 0:Se["Basic Data"])==null?void 0:Fe.basic)==null?void 0:Ze.PageNo,NoSheets:(et=a==null?void 0:a.payloadData)==null?void 0:et.NoSheets,ProdMemo:(St=a==null?void 0:a.payloadData)==null?void 0:St.ProdMemo,Pageformat:(gt=a==null?void 0:a.payloadData)==null?void 0:gt.DocFormat,SizeDim:(Xt=a==null?void 0:a.payloadData)==null?void 0:Xt.SizeDim,BaseUomIso:"",BasicMatl:((eo=(hs=(tt=a==null?void 0:a.payloadData)==null?void 0:tt["Basic Data"])==null?void 0:hs.basic)==null?void 0:eo.BasicMatl)||"",StdDescr:(Wt=a==null?void 0:a.payloadData)==null?void 0:Wt.StdDescr,DsnOffice:((Jo=(oo=(so=a==null?void 0:a.payloadData)==null?void 0:so["Basic Data"])==null?void 0:oo.basic)==null?void 0:Jo.DsnOffice)||"",PurValkey:((vr=(cr=(Tn=a==null?void 0:a.payloadData)==null?void 0:Tn["Purchasing-General"])==null?void 0:cr["Purchasing-General"])==null?void 0:vr.PurValkey)||"",NetWeight:(Vl=(Vr=(Xl=a==null?void 0:a.payloadData)==null?void 0:Xl["Basic Data"])==null?void 0:Vr.basic)==null?void 0:Vl.NetWeight,UnitOfWt:((Jl=(Kr=(Kl=a==null?void 0:a.payloadData)==null?void 0:Kl["Basic Data"])==null?void 0:Kr.basic)==null?void 0:Jl.UnitOfWt)||"",TransGrp:(ei=(Zl=(Ql=a==null?void 0:a.payloadData)==null?void 0:Ql["Sales-General"])==null?void 0:Zl["Sales-General"])==null?void 0:ei.TransGrp,XSalStatus:(oi=(si=(ti=a==null?void 0:a.payloadData)==null?void 0:ti["Sales-General"])==null?void 0:si["Sales-General"])==null?void 0:oi.XSalStatus,Svalidfrom:(Qr=(ni=(Jr=a==null?void 0:a.payloadData)==null?void 0:Jr["Sales-General"])==null?void 0:ni["Sales-General"])!=null&&Qr.Svalidfrom?ws((li=(ri=(Zr=a==null?void 0:a.payloadData)==null?void 0:Zr["Sales-General"])==null?void 0:ri["Sales-General"])==null?void 0:li.Svalidfrom):null,Division:(el=m==null?void 0:m.payloadData)!=null&&el.Division?(tl=m==null?void 0:m.payloadData)==null?void 0:tl.Division:((nl=(ol=(sl=a==null?void 0:a.payloadData)==null?void 0:sl["Basic Data"])==null?void 0:ol.basic)==null?void 0:nl.Division)||"",ProdHier:((il=(ll=(rl=a==null?void 0:a.payloadData)==null?void 0:rl["Basic Data"])==null?void 0:ll.basic)==null?void 0:il.ProdHier)||"",CadId:(dl=(cl=(al=a==null?void 0:a.payloadData)==null?void 0:al["Basic Data"])==null?void 0:cl.basic)==null?void 0:dl.CadId,VarOrdUn:(fl=(hl=(ul=a==null?void 0:a.payloadData)==null?void 0:ul["Purchasing-General"])==null?void 0:hl["Purchasing-General"])==null?void 0:fl.VarOrdUn,UnitOfWtIso:"",MatGrpSm:"",Authoritygroup:"",QmProcmnt:"",BatchMgmt:(Tl=(pl=(gl=a==null?void 0:a.payloadData)==null?void 0:gl["Sales-General"])==null?void 0:pl["Sales-General"])==null?void 0:Tl.BatchMgmt,SalStatus:"",Catprofile:"",ShelfLife:"",StorPct:"",Hazmatprof:((h=(i=(El=a==null?void 0:a.payloadData)==null?void 0:El["Basic Data"])==null?void 0:i.basic)==null?void 0:h.Hazmatprof)||"",HighVisc:(X=($=(D=a==null?void 0:a.payloadData)==null?void 0:D["Basic Data"])==null?void 0:$.basic)==null?void 0:X.HighVisc,AppdBRec:"",Pvalidfrom:(he=(J=(Z=a==null?void 0:a.payloadData)==null?void 0:Z["Basic Data"])==null?void 0:J.basic)!=null&&he.Pvalidfrom?ws((Ne=(be=(De=a==null?void 0:a.payloadData)==null?void 0:De["Basic Data"])==null?void 0:be.basic)==null?void 0:Ne.Pvalidfrom):null,EnvtRlvt:"",ProdAlloc:(yt=(st=(Me=a==null?void 0:a.payloadData)==null?void 0:Me["Basic Data"])==null?void 0:st.basic)==null?void 0:yt.ProdAlloc,PeriodIndExpirationDate:"",ParEff:!0,Matcmpllvl:"",GItemCat:((no=(Xs=(jt=a==null?void 0:a.payloadData)==null?void 0:jt["Basic Data"])==null?void 0:Xs.basic)==null?void 0:no.GItemCat)||"",CSalStatus:((cn=(vo=(ns=a==null?void 0:a.payloadData)==null?void 0:ns["Basic Data"])==null?void 0:vo.basic)==null?void 0:cn.CSalStatus)||"",IntlPoPrice:((In=(_n=(dn=a==null?void 0:a.payloadData)==null?void 0:dn["Basic Data"])==null?void 0:_n.basic)==null?void 0:In.IntlPoPrice)||"",PryVendor:((Mn=(vn=(Qo=a==null?void 0:a.payloadData)==null?void 0:Qo["Basic Data"])==null?void 0:vn.basic)==null?void 0:Mn.PryVendor)||"",PlanningArea:((Jn=(Kn=(On=a==null?void 0:a.payloadData)==null?void 0:On["Basic Data"])==null?void 0:Kn.basic)==null?void 0:Jn.PlanningArea)||"",PlanningFactor:((ho=(Zn=(Qn=a==null?void 0:a.payloadData)==null?void 0:Qn["Basic Data"])==null?void 0:Zn.basic)==null?void 0:ho.PlanningFactor)||"",ReturnMatNumber:((ln=(Zo=(En=a==null?void 0:a.payloadData)==null?void 0:En["Basic Data"])==null?void 0:Zo.basic)==null?void 0:ln.ReturnMatNumber)||"",ParentMatNumber:((Bn=(tr=(er=a==null?void 0:a.payloadData)==null?void 0:er["Basic Data"])==null?void 0:tr.basic)==null?void 0:Bn.ParentMatNumber)||"",DiversionControlFlag:((Mo=(bo=(Os=a==null?void 0:a.payloadData)==null?void 0:Os["Basic Data"])==null?void 0:bo.basic)==null?void 0:Mo.DiversionControlFlag)||"",MatGroupPackagingMat:((Ec=(Tc=(pc=a==null?void 0:a.payloadData)==null?void 0:pc["Basic Data"])==null?void 0:Tc.basic)==null?void 0:Ec.MatGroupPackagingMat)||"",HazMatNo:((Ac=(Cc=(mc=a==null?void 0:a.payloadData)==null?void 0:mc[P.STORAGE_GENERAL])==null?void 0:Cc[P.STORAGE_GENERAL])==null?void 0:Ac.HazMatNo)||"",QtyGrGi:((Nc=(Sc=(bc=a==null?void 0:a.payloadData)==null?void 0:bc[P.STORAGE_GENERAL])==null?void 0:Sc[P.STORAGE_GENERAL])==null?void 0:Nc.QtyGrGi)||"",TempConds:((_c=(Rc=(wc=a==null?void 0:a.payloadData)==null?void 0:wc[P.STORAGE_GENERAL])==null?void 0:Rc[P.STORAGE_GENERAL])==null?void 0:_c.TempConds)||"",Container:((Mc=(vc=(Ic=a==null?void 0:a.payloadData)==null?void 0:Ic[P.STORAGE_GENERAL])==null?void 0:vc[P.STORAGE_GENERAL])==null?void 0:Mc.Container)||"",LabelType:((yc=(xc=(Oc=a==null?void 0:a.payloadData)==null?void 0:Oc[P.STORAGE_GENERAL])==null?void 0:xc[P.STORAGE_GENERAL])==null?void 0:yc.LabelType)||"",LabelForm:((Pc=(Dc=(Lc=a==null?void 0:a.payloadData)==null?void 0:Lc[P.STORAGE_GENERAL])==null?void 0:Dc[P.STORAGE_GENERAL])==null?void 0:Pc.LabelForm)||"",AppdBRec:((kc=(Uc=(qc=a==null?void 0:a.payloadData)==null?void 0:qc[P.STORAGE_GENERAL])==null?void 0:Uc[P.STORAGE_GENERAL])==null?void 0:kc.AppdBRec)||"",Minremlife:((Gc=(Bc=(Hc=a==null?void 0:a.payloadData)==null?void 0:Hc[P.STORAGE_GENERAL])==null?void 0:Bc[P.STORAGE_GENERAL])==null?void 0:Gc.Minremlife)||"",ShelfLife:((Wc=(Fc=($c=a==null?void 0:a.payloadData)==null?void 0:$c[P.STORAGE_GENERAL])==null?void 0:Fc[P.STORAGE_GENERAL])==null?void 0:Wc.ShelfLife)||"",PeriodIndExpirationDate:((Yc=(zc=(jc=a==null?void 0:a.payloadData)==null?void 0:jc[P.STORAGE_GENERAL])==null?void 0:zc[P.STORAGE_GENERAL])==null?void 0:Yc.PeriodIndExpirationDate)||"",RoundUpRuleExpirationDate:((Kc=(Vc=(Xc=a==null?void 0:a.payloadData)==null?void 0:Xc[P.STORAGE_GENERAL])==null?void 0:Vc[P.STORAGE_GENERAL])==null?void 0:Kc.RoundUpRuleExpirationDate)||"",StorPct:((Zc=(Qc=(Jc=a==null?void 0:a.payloadData)==null?void 0:Jc[P.STORAGE_GENERAL])==null?void 0:Qc[P.STORAGE_GENERAL])==null?void 0:Zc.StorPct)||"",SledBbd:((sd=(td=(ed=a==null?void 0:a.payloadData)==null?void 0:ed[P.STORAGE_GENERAL])==null?void 0:td[P.STORAGE_GENERAL])==null?void 0:sd.SledBbd)||"",SerializationLevel:((rd=(nd=(od=a==null?void 0:a.payloadData)==null?void 0:od[P.STORAGE_GENERAL])==null?void 0:nd[P.STORAGE_GENERAL])==null?void 0:rd.SerializationLevel)||"",ShelfLifeReqMax:((ad=(id=(ld=a==null?void 0:a.payloadData)==null?void 0:ld[P.STORAGE_GENERAL])==null?void 0:id[P.STORAGE_GENERAL])==null?void 0:ad.ShelfLifeReqMax)||"",ShelfLifeReqMin:((ud=(dd=(cd=a==null?void 0:a.payloadData)==null?void 0:cd[P.STORAGE_GENERAL])==null?void 0:dd[P.STORAGE_GENERAL])==null?void 0:ud.ShelfLifeReqMin)||"",MaturityDur:((gd=(fd=(hd=a==null?void 0:a.payloadData)==null?void 0:hd[P.STORAGE_GENERAL])==null?void 0:fd[P.STORAGE_GENERAL])==null?void 0:gd.MaturityDur)||"",StorPct:((Ed=(Td=(pd=a==null?void 0:a.payloadData)==null?void 0:pd[P.STORAGE_GENERAL])==null?void 0:Td[P.STORAGE_GENERAL])==null?void 0:Ed.StorPct)||"",StorConds:((Ad=(Cd=(md=a==null?void 0:a.payloadData)==null?void 0:md[P.STORAGE_GENERAL])==null?void 0:Cd[P.STORAGE_GENERAL])==null?void 0:Ad.StorConds)||""},Toplantdata:Pg,Tosalesdata:(bd=a==null?void 0:a.headerData)!=null&&bd.views.includes("Sales")?bg:[],Tomaterialdescription:qg,Touomdata:Ug,Toeandata:kg,Tostroragelocationdata:xg,ToClassification:Iu,Tomaterialerrordata:(a==null?void 0:a.Tomaterialerrordata)||{},Toaccountingdata:(Nd=a==null?void 0:a.headerData)!=null&&Nd.views.includes((Sd=P)==null?void 0:Sd.ACCOUNTING)?vg:[],Tocontroldata:(wd=a==null?void 0:a.headerData)!=null&&wd.views.includes("Sales")?Nu:[],Torequestheaderdata:{RequestId:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(Rd=m==null?void 0:m.payloadData)!=null&&Rd.RequestId?(_d=m==null?void 0:m.payloadData)==null?void 0:_d.RequestId:(Id=u==null?void 0:u.requestHeader)!=null&&Id.requestId?(vd=u==null?void 0:u.requestHeader)==null?void 0:vd.requestId:requestId.slice(3),ReqCreatedBy:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(Md=m==null?void 0:m.payloadData)!=null&&Md.RequestId?(Od=m==null?void 0:m.payloadData)==null?void 0:Od.ReqCreatedBy:(xd=u==null?void 0:u.requestHeader)==null?void 0:xd.reqCreatedBy,ReqCreatedOn:ws(l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(yd=m==null?void 0:m.payloadData)!=null&&yd.RequestId?(Ld=m==null?void 0:m.payloadData)==null?void 0:Ld.ReqCreatedOn:(Dd=u==null?void 0:u.requestHeader)==null?void 0:Dd.reqCreatedOn),ReqUpdatedOn:ws(l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(Pd=m==null?void 0:m.payloadData)!=null&&Pd.RequestId?(qd=m==null?void 0:m.payloadData)==null?void 0:qd.ReqUpdatedOn:(Ud=u==null?void 0:u.requestHeader)==null?void 0:Ud.reqCreatedOn),RequestType:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(kd=m==null?void 0:m.payloadData)!=null&&kd.RequestId?(Hd=m==null?void 0:m.payloadData)==null?void 0:Hd.RequestType:(Bd=u==null?void 0:u.requestHeader)==null?void 0:Bd.requestType,Division:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(Gd=m==null?void 0:m.payloadData)!=null&&Gd.RequestId?($d=m==null?void 0:m.payloadData)==null?void 0:$d.Division:(Fd=u==null?void 0:u.requestHeader)==null?void 0:Fd.Division,RequestPriority:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(Wd=m==null?void 0:m.payloadData)!=null&&Wd.RequestId?(jd=m==null?void 0:m.payloadData)==null?void 0:jd.RequestPriority:(zd=u==null?void 0:u.requestHeader)==null?void 0:zd.requestPriority,RequestDesc:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(Yd=m==null?void 0:m.payloadData)!=null&&Yd.RequestId?(Xd=m==null?void 0:m.payloadData)==null?void 0:Xd.RequestDesc:(Vd=u==null?void 0:u.requestHeader)==null?void 0:Vd.requestDesc,RequestStatus:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(Kd=m==null?void 0:m.payloadData)!=null&&Kd.RequestId?(Jd=m==null?void 0:m.payloadData)==null?void 0:Jd.RequestStatus:(Qd=u==null?void 0:u.requestHeader)==null?void 0:Qd.requestStatus,FirstProd:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(Zd=m==null?void 0:m.payloadData)!=null&&Zd.RequestId?(eu=m==null?void 0:m.payloadData)==null?void 0:eu.FirstProd:((tu=s==null?void 0:s.payloadData)==null?void 0:tu.FirstProductionDate)||null,LaunchDate:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(su=m==null?void 0:m.payloadData)!=null&&su.RequestId?(ou=m==null?void 0:m.payloadData)==null?void 0:ou.LaunchDate:((nu=s==null?void 0:s.payloadData)==null?void 0:nu.LaunchDate)||null,LeadingCat:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(ru=m==null?void 0:m.payloadData)!=null&&ru.RequestId?(lu=m==null?void 0:m.payloadData)==null?void 0:lu.LeadingCat:(iu=u==null?void 0:u.requestHeader)==null?void 0:iu.leadingCat,Region:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(au=m==null?void 0:m.payloadData)!=null&&au.RequestId?(cu=m==null?void 0:m.payloadData)==null?void 0:cu.Region:(du=u==null?void 0:u.requestHeader)==null?void 0:du.region,IsBifurcated:!0},Tochildrequestheaderdata:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(uu=m==null?void 0:m.payloadData)!=null&&uu.RequestId?Hg:{},Towarehousedata:Og,changeLogData:y&&Object.keys(y).length>0&&(y[(hu=a.headerData)==null?void 0:hu.materialNumber]||y[(fu=a.headerData)==null?void 0:fu.id])?{RequestId:(y==null?void 0:y.RequestId)||((gu=a==null?void 0:a.changeLogData)==null?void 0:gu.RequestId),ChangeLogId:((pu=a==null?void 0:a.changeLogData)==null?void 0:pu.ChangeLogId)??null,ChildRequestId:((y==null?void 0:y.ChildRequestId)||((Tu=a==null?void 0:a.changeLogData)==null?void 0:Tu.ChildRequestId))??null,...y[(Eu=a.headerData)==null?void 0:Eu.materialNumber],...y[(mu=a.headerData)==null?void 0:mu.id]}:{RequestId:(Cu=a==null?void 0:a.changeLogData)==null?void 0:Cu.RequestId,ChangeLogId:((Au=a==null?void 0:a.changeLogData)==null?void 0:Au.ChangeLogId)??null,ChildRequestId:((bu=a==null?void 0:a.changeLogData)==null?void 0:bu.ChildRequestId)??null}};B.push(Bg)}}}),B}return{createPayloadFromReduxState:O}},ph={handleSubmitForApproval:6,handleSendBack:1,handleReject:3,handleValidate:5,handleSAPSyndication:8,handleValidate1:4,handleSubmitForReview:7,handleCorrection:2},Th={Approve:6,"Send Back":3,"Save As Draft":1,Reject:3,Validate:4,Forward:6,"SAP Syndication":8,Submit:7,Correction:2},ql={REQUEST_HEADER:0,REQUEST_DETAILS:1,ATTACHMENT_AND_COMMENTS:2,PREVIEW:3},po={HANDLE_SEND_BACK:"handleSendBack",HANDLE_VALIDATE1:"handleValidate1",HANDLE_SUBMIT_FOR_APPROVAL:"handleSubmitForApproval",HANDLE_VALIDATE:"handleValidate",HANDLE_SAP_SYNDICATION:"handleSAPSyndication",HANDLE_SUBMIT_FOR_REVIEW:"handleSubmitForReview",HANDLE_CORRECTION:"handleCorrection",HANDLE_SUBMIT:"handleSubmit",HANDLE_DRAFT:"handleDraft"},fE=d.forwardRef((e,t)=>{var v;const[r,o]=d.useState([]),c=se(C=>C.payload),s=((v=c==null?void 0:c.payloadData)==null?void 0:v.TemplateName)||"",[p,l]=d.useState(!1),[u,re]=d.useState(!1),{changePayloadForTemplate:ae}=$i(s),y=se(C=>C.commonFilter.RequestBench),{customError:O}=nn(),K=wr();d.useImperativeHandle(t,()=>({handlePriorityDialogClickOpen:ce}));const m=se(C=>C.paginationData),B=[{field:"id",headerName:"",editable:!1,flex:1,hide:!0},{field:"reqId",headerName:"Request ID",editable:!1,flex:2.5},{field:"module",headerName:"Module",editable:!1,flex:2},{field:"reqType",headerName:"Request Type",editable:!1,flex:1.5},{field:"scheduledBy",headerName:"Scheduled By",editable:!1,flex:4},{field:"scheduledOn",headerName:"Scheduled On",editable:!1,flex:1},{field:"totalObjects",headerName:"Total Objects",editable:!1,flex:1,renderHeader:()=>n(_s,{title:"Objects count scheduled for SAP syndication",arrow:!0,children:n("span",{children:"Request ID"})})},{field:"pendingObjects",headerName:"Pending Objects",editable:!1,flex:1,renderHeader:()=>n(_s,{title:"Objects count pending for SAP syndicated.",arrow:!0,children:n("span",{children:"Request ID"})})},{field:"objectSuccessCount",headerName:"Success Objects",editable:!1,flex:1,renderHeader:()=>n(_s,{title:"Objects count syndicated in SAP",arrow:!0,children:n("span",{children:"Request ID"})})},{field:"objectFailureCount",headerName:"Failure Objects",editable:!1,flex:1,renderHeader:()=>n(_s,{title:"Objects count failed during syndication in SAP",arrow:!0,children:n("span",{children:"Request ID"})})},{field:"retryCount",headerName:"Retry Count",editable:!1,flex:1,renderHeader:()=>n(_s,{title:"Number of times request retriggered.(Max- 3 count, after that it wouldnt be picked & set as status- Retry Count Exceeded)",arrow:!0,children:n("span",{children:"Request ID"})})},{field:"schedulerStatus",headerName:"Scheduler Status",editable:!1,flex:1},{field:"action",headerName:"Action",editable:!1,flex:1}],ce=()=>{oe()},oe=()=>{var x;const C=zn().format("YYYY-MM-DDTHH:mm:ss.SSSZ"),S={modules:"Material,Cost Center,Profit Center,CC-PC Combo,General Ledger",requestTypes:"Create with Upload,Change with Upload,Extend with Upload",statuses:"Scheduler - Failed,Scheduler - Pending,Scheduler - Partially Completed",toDate:zn(y==null?void 0:y.createdOn[1]).utc().format("YYYY-MM-DDTHH:mm:ss")??"",fromDate:zn(y==null?void 0:y.createdOn[0]).utc().format("YYYY-MM-DDTHH:mm:ss")??""},q=H=>{var L,N,U,I;if(H.statusCode===200){let ge=(H==null?void 0:H.data)||[];re(!0);let te=ge==null?void 0:ge.map((F,qe)=>({id:qe,schedulerId:F==null?void 0:F.SchedulerId,reqId:F==null?void 0:F.RequestId,reqType:F==null?void 0:F.RequestType,priority:F==null?void 0:F.Priority,scheduledBy:F==null?void 0:F.ReqScheduledBy,scheduledOn:F==null?void 0:F.ReqScheduledOn,totalObjects:F==null?void 0:F.ObjectCount,pendingObjects:F==null?void 0:F.PendingObjectsCount,retryCount:F==null?void 0:F.RetryCount,module:F==null?void 0:F.Module,objectSuccessCount:F==null?void 0:F.ObjectsSuccessCount,objectFailureCount:F==null?void 0:F.ObjectsFailureCount,updatedOn:F==null?void 0:F.ReqUpdatedOn,schedulerStatus:F==null?void 0:F.SchedulerStatus}));const He=te==null?void 0:te.filter(F=>F.module==="Material"&&F.reqType==="Create with Upload"),M=(He==null?void 0:He.length)>0?Math.max(...He.map(F=>F.priority)):0;var k={id:te.length+1,schedulerId:"",reqId:((L=e.taskData)==null?void 0:L.requestId)||((N=e.taskData)==null?void 0:N.ATTRIBUTE_1)||"",reqType:((U=e==null?void 0:e.taskData)==null?void 0:U.ATTRIBUTE_2)??"Create with Upload",priority:M+1,scheduledBy:((I=e.userData)==null?void 0:I.emailId)??"",scheduledOn:C??"",totalObjects:(m==null?void 0:m.totalElements)||0,pendingObjects:(m==null?void 0:m.totalElements)||0,retryCount:0,module:"Material",objectSuccessCount:0,objectFailureCount:0,updatedOn:C??"",schedulerStatus:"Scheduler - Pending"};const de=[...te,k].sort((F,qe)=>F.module!==qe.module?F.module.localeCompare(qe.module):F.reqType!==qe.reqType?F.reqType.localeCompare(qe.reqType):F.priority-qe.priority);o(de)}else e.setDialogTitle("Error"),e.setSuccessMsg(!1),e.setMessageDialogMessage("Failed Fetching Scheduled Requests. Try Once more."),e.setMessageDialogSeverity("danger"),e.handleMessageDialogClickOpen()},A=H=>{O(H)};Xe(`/${hp}${(x=We.DATA)==null?void 0:x.FETCH_SCHEDULERS_IN_REQ_BENCH}`,"post",q,A,S)},Q=()=>{l(!1),re(!1)},f=C=>{o(C)},Y=C=>{var k;const S=e.requestType===b.CREATE_WITH_UPLOAD?`/${ve}${We.MASS_ACTION.CREATE_MAT_APPROVED}`:e.requestType===b.EXTEND_WITH_UPLOAD?`/${ve}${We.MASS_ACTION.EXTEND_MAT_APPROVED}`:`/${ve}${We.MASS_ACTION.CHANGE_MAT_APPROVED}`;Q(),e.setBlurLoading(!0);let q=(e==null?void 0:e.requestType)===((k=b)==null?void 0:k.CHANGE_WITH_UPLOAD)?ae(!0):e.createPayloadFromReduxState(c);const A=q==null?void 0:q.map(L=>({...L,Tochildrequestheaderdata:{...L==null?void 0:L.Tochildrequestheaderdata,IsScheduled:!0}}));Xe(S,"post",L=>{e.setBlurLoading(!1),L.statusCode===200||L.statusCode===201?(e.setMessageDialogSeverity("success"),e.setSuccessMsg(!0),g()):(e.setDialogTitle("Error"),e.setSuccessMsg(!1),e.setTextInput(!1),e.setMessageDialogMessage((L==null?void 0:L.message)??"Failed Submitting Request"),e.setMessageDialogSeverity("danger"),e.setBlurLoading(!1),e.handleMessageDialogClickOpen())},L=>{console.log("error")},A)};var z=r==null?void 0:r.map((C,S)=>({SchedulerId:(C==null?void 0:C.schedulerId)??"",RequestId:C==null?void 0:C.reqId,RequestType:C==null?void 0:C.reqType,Module:C==null?void 0:C.module,Priority:C==null?void 0:C.priority,ObjectCount:C==null?void 0:C.totalObjects,ObjectsSuccessCount:C==null?void 0:C.objectSuccessCount,ObjectsFailureCount:C==null?void 0:C.objectFailureCount,PendingObjectsCount:C==null?void 0:C.pendingObjects,ReqScheduledBy:C==null?void 0:C.scheduledBy,ReqScheduledOn:C==null?void 0:C.scheduledOn,ReqUpdatedOn:C==null?void 0:C.updatedOn,SchedulerStatus:C==null?void 0:C.schedulerStatus,RetryCount:C==null?void 0:C.retryCount}));const g=()=>{var q,A;const C=x=>{x.statusCode===200?(e.setMessageDialogSeverity("success"),e.setMessageDialogMessage("Request has been submitted and scheduled for Syndication."),e.setBlurLoading(!1),e.setSuccessMsg(!0),e.handleSnackBarOpen(),setTimeout(()=>{K("/masterDataCockpit/materialMaster/materialSingle")},3e3)):(x==null?void 0:x.statusCode)===400?(e.setDialogTitle("Info"),e.setSuccessMsg(!1),e.setMessageDialogMessage(x==null?void 0:x.message),e.setTextInput(!1),e.setMessageDialogSeverity("info"),e.setMessageDialogOK(!1),e.setBlurLoading(!1),e.handleErrorDialogClickOpen(),setTimeout(()=>{K("/requestBench")},3e3)):(e.setDialogTitle("Error"),e.setSuccessMsg(!1),e.setMessageDialogMessage("Failed Scheduling Request, You will be redirected to Request Bench for Scheduling it."),e.setTextInput(!1),e.setMessageDialogSeverity("danger"),e.setMessageDialogOK(!1),e.setBlurLoading(!1),e.handleErrorDialogClickOpen(),setTimeout(()=>{K("/requestBench")},3e3))},S=x=>{console.log(x)};Xe(`/${ve}${(A=(q=We)==null?void 0:q.DATA)==null?void 0:A.UPDATE_PRIORITY}`,"post",C,S,z)},T=Vn(({className:C,...S})=>n(_s,{...S,classes:{popper:C}}))({[`& .${Na.tooltip}`]:{maxWidth:"none"}});return R(un,{open:u,onClose:Q,fullWidth:!0,maxWidth:"xl",PaperProps:{sx:{borderRadius:3,boxShadow:8,backgroundColor:"#ffffff"}},children:[R(Le,{sx:{px:3,py:2,borderBottom:"1px solid #e0e0e0",display:"flex",justifyContent:"space-between",alignItems:"center",backgroundColor:"#F4F6FA",borderTopLeftRadius:12,borderTopRightRadius:12},children:[n(mt,{variant:"h6",fontWeight:600,color:"text.primary",children:"List of Requests Scheduled"}),n(T,{arrow:!0,title:n(mt,{fontSize:12,children:"Here you can prioritize your requests"}),children:n(xs,{size:"small",children:n(fp,{fontSize:"small"})})})]}),n(Lo,{sx:{px:3,py:2},children:n(Le,{sx:{border:"1px solid #e0e0e0",borderRadius:2,overflow:"hidden"},children:n(gp,{columns:B,row:r,onRowUpdate:f,selectionType:"SAPScheduler",showDragIcon:!0})})}),R(Do,{sx:{px:3,py:2,backgroundColor:"#FAFAFA",borderTop:"1px solid #e0e0e0",borderBottomLeftRadius:12,borderBottomRightRadius:12},children:[n(Rt,{onClick:Q,variant:"outlined",color:"primary",sx:{textTransform:"capitalize",minWidth:100},children:"Cancel"}),n(Rt,{onClick:()=>Y(e.currentButtonState),variant:"contained",color:"primary",sx:{textTransform:"capitalize",minWidth:100},children:"Submit"})]})]})}),Wr=e=>{var Ys,co,ds,ao,Po,Qs,Mt,Bs,Zs,_o,Fo,Io,uo,os,Wo,to,qo,jo,zo,Yo,Xo,Vo,Ko;const t=se(pe=>pe.payload),r=se(pe=>pe.payload.dynamicKeyValues),o=se(pe=>pe.payload.changeFieldRows),c=se(pe=>pe.payload.selectedRows),s=(Ys=t==null?void 0:t.payloadData)==null?void 0:Ys.RequestType,[p,l]=d.useState(!1),[u,re]=d.useState("success"),[ae,y]=d.useState(!1),[O,K]=d.useState(""),[m,B]=d.useState(""),[ce,oe]=d.useState(!1),[Q,f]=d.useState(""),[Y,z]=d.useState(!1),[g,T]=d.useState(""),[v,C]=d.useState(""),[S,q]=d.useState(!1),[A,x]=d.useState([]),[H,k]=d.useState([]),[L,N]=d.useState(!1),[U,I]=d.useState(!1),{t:ge}=Sn(),[te,He]=d.useState(!1),[M,we]=d.useState(!1),[de,F]=d.useState(""),[qe,W]=d.useState(!1),[Ke,Ds]=d.useState(!1),[Gt,Gs]=d.useState(""),[$s,ts]=d.useState(""),[lo,Fs]=d.useState(!1),[Us,Kt]=d.useState({}),[fs,Dt]=d.useState(""),[rs,Jt]=d.useState("");let vs=se(pe=>pe.userManagement.userData),rt=se(pe=>pe.userManagement.taskData);const Be=wr(),It=Ro(),$t=on(),Ns=$t.state,Ce=se(pe=>pe.payload.payloadData),dt=se(pe=>pe.payload.requestorPayload),ls=se(pe=>pe.tabsData.changeFieldsDT),ks=(Ce==null?void 0:Ce.TemplateName)||"",{changePayloadForTemplate:is}=$i(ks),ss=new URLSearchParams($t.search.split("?")[1]),it=ss.get("RequestId"),xt=ss.get("reqBench"),Es=Rr(jn.CURRENT_TASK),Ms=typeof Es=="string"?JSON.parse(Es):Es,Qt=!(rt!=null&&rt.taskId||Ms!=null&&Ms.ATTRIBUTE_5)&&!xt,[as,io]=d.useState(!1),{customError:Qe}=nn(),{createFCPayload:Lt}=ef(),{createPayloadFromReduxState:Ut}=ka({initialReqScreen:Qt,isReqBench:xt,remarks:g,userInput:de,selectedLevel:v}),[Nt,ms]=d.useState(!1),[Ks,Hs]=d.useState(!1),[nt,cs]=d.useState(!1),[Js,Ws]=d.useState(""),Zt=((co=t==null?void 0:t.payloadData)==null?void 0:co.RequestType)===b.CREATE_WITH_UPLOAD||((ds=t==null?void 0:t.payloadData)==null?void 0:ds.RequestType)===b.EXTEND_WITH_UPLOAD||((ao=t==null?void 0:t.payloadData)==null?void 0:ao.RequestType)===b.CHANGE_WITH_UPLOAD,{showSnackbar:Ue}=Bh(),Pt=se(pe=>pe.request.tabValue),Ps=200,le=d.useRef(),Ge=()=>{y(!0)},Ae=()=>{y(!1)},Re=()=>{I(!0)},Ve=()=>{I(!1)},ke=()=>{var pe;rs===fo.SAVE?(Ve(),vt()):rs===((pe=fo)==null?void 0:pe.VALIDATE)&&(Ve(),ee())},at=()=>{z(!0)},xe=()=>{T(""),z(!1)},Bt=(pe,ht)=>{const ft=pe.target.value;if(ms(ft.length>=Ps),ft.length>0&&ft[0]===" ")T(ft.trimStart()),It(hi({keyName:"Comments",data:ft.trimStart()}));else{let Ct=ft;T(Ct),It(hi({keyName:"Comments",data:Ct}))}},gs=pe=>{C(pe.target.value),It(hi({keyName:"Level",data:pe.target.value}))},vt=()=>{var Oe,At,Ht,qt,Yt,us,Ee,ct,ps;xe(),oe(!0);var pe;((Oe=t==null?void 0:t.payloadData)==null?void 0:Oe.RequestType)===b.CREATE||((At=t==null?void 0:t.payloadData)==null?void 0:At.RequestType)===b.CREATE_WITH_UPLOAD?rs===fo.SAVE?pe=`/${ve}/massAction/createMaterialSaveAsDraft`:pe=(vs==null?void 0:vs.role)==="Approver"?`/${ve}/massAction/createBasicMaterialsApproved`:`/${ve}/massAction/createMaterialSubmitForReview`:((Ht=t==null?void 0:t.payloadData)==null?void 0:Ht.RequestType)===b.EXTEND_WITH_UPLOAD?rs===fo.SAVE?pe=`/${ve}${We.MASS_ACTION.EXTEND_MATERIAL_SAVE_AS_DRAFT}`:pe=`/${ve}${We.MASS_ACTION.EXTEND_MATERIAL_DIRECT_APPROVED}`:(s===b.CHANGE||s===b.CHANGE_WITH_UPLOAD)&&(rs===fo.SAVE?pe=`/${ve}/massAction/changeMaterialSaveAsDraft`:pe=(vs==null?void 0:vs.role)==="Approver"?`/${ve}/massAction/changeBasicMaterialsApproved`:`/${ve}/massAction/changeMaterialSubmitForReview`);const ht=Ts=>{if(Ts.statusCode>=Vt.STATUS_200&&Ts.statusCode<Vt.STATUS_300){oe(!1);let No;(vs==null?void 0:vs.role)==="Approver"?No=`Material Syndicated successfully in SAP with Material ID : ${Ts==null?void 0:Ts.body.join(", ")}`:rs===fo.SAVE?No=Ts==null?void 0:Ts.message:No=`Request Submitted for Approval with Request ID ${Ts==null?void 0:Ts.body}`,Ue(No,"success"),Ge(),Be("/masterDataCockpit/materialMaster/materialSingle")}else oe(!1),Ue(Ts==null?void 0:Ts.message,"error");Jt("")},ft=Ts=>{Ue(Ts==null?void 0:Ts.message,"error"),oe(!1),Jt("")};var Ct;Ct=((qt=t==null?void 0:t.payloadData)==null?void 0:qt.RequestType)===b.CREATE||((Yt=t==null?void 0:t.payloadData)==null?void 0:Yt.RequestType)===b.CREATE_WITH_UPLOAD||((us=t==null?void 0:t.payloadData)==null?void 0:us.RequestType)===b.EXTEND_WITH_UPLOAD?Ut(t):((Ee=t==null?void 0:t.payloadData)==null?void 0:Ee.RequestType)===b.CHANGE?is(!!xt):((ct=t==null?void 0:t.payloadData)==null?void 0:ct.RequestType)===b.CHANGE_WITH_UPLOAD?is(!0):((ps=t==null?void 0:t.payloadData)==null?void 0:ps.RequestType)===b.CHANGE?Ut(t):[],Xe(pe,"post",ht,ft,Ct)},ze=async pe=>{var ht,ft,Ct,Oe,At;if(((pe==null?void 0:pe.MDG_MAT_DYN_BTN_ACTION_TYPE)==="handleValidate"||(pe==null?void 0:pe.MDG_MAT_DYN_BTN_ACTION_TYPE)==="handleValidate1")&&(((ht=t==null?void 0:t.payloadData)==null?void 0:ht.RequestType)===b.CREATE||((ft=t==null?void 0:t.payloadData)==null?void 0:ft.RequestType)===b.CREATE_WITH_UPLOAD))try{const Ht=await e.validateMaterials();io(Ht)}catch(Ht){Qe(Ht);return}B(""),Ds("success"),Kt(pe),ts(pe.MDG_MAT_DYN_BTN_COMMENT_BOX_NAME),we(pe.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT===((Ct=Wn)==null?void 0:Ct.MANDATORY)),W(pe.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT===((Oe=Wn)==null?void 0:Oe.MANDATORY)||pe.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT==="Optional"),Dt(pe.MDG_MAT_DYN_BTN_ACTION_TYPE),pe.MDG_MAT_DYN_BTN_BUTTON_NAME===fo.SEND_BACK||pe.MDG_MAT_DYN_BTN_BUTTON_NAME===fo.CORRECTION?Hs(!0):Hs(!1),pe.MDG_MAT_DYN_BTN_BUTTON_NAME===fo.SAP_SYNDICATE?cs(!0):cs(!1),pe.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT===((At=Wn)==null?void 0:At.MANDATORY)||pe.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT==="Optional"?_e():ue(pe.MDG_MAT_DYN_BTN_ACTION_TYPE,pe)},Pe=()=>{xe(),oe(!0);const pe=s===b.CREATE||s===b.CREATE_WITH_UPLOAD?`/${ve}/massAction/createMaterialApprovalSubmit`:s===b.EXTEND||s===b.EXTEND_WITH_UPLOAD?`/${ve}/massAction/extendMaterialApprovalSubmit`:`/${ve}/massAction/changeMaterialApprovalSubmit`,ht=Oe=>{Oe.statusCode>=200&&Oe.statusCode<300?(oe(!1),Ue(`Request Submitted for Approval with Request ID ${Oe==null?void 0:Oe.body}`,"success"),Be("/masterDataCockpit/materialMaster/materialSingle")):(oe(!1),Ue(Oe==null?void 0:Oe.message,"error"))},ft=()=>{oe(!1),Ue("Failed Submitting Request.","error")};var Ct;Ct=s===b.CREATE||s===b.EXTEND||s===b.EXTEND_WITH_UPLOAD||s===b.CREATE_WITH_UPLOAD?Ut(t):is(!0),Xe(pe,"post",ht,ft,Ct)},wt=pe=>{var Ht;xe(),oe(!0);var ht=s===b.CREATE||s===b.CREATE_WITH_UPLOAD?`/${ve}/massAction/createMaterialApproved`:s===b.EXTEND||s===b.EXTEND_WITH_UPLOAD?`/${ve}/massAction/extendMaterialApproved`:rt.ATTRIBUTE_2===b.FINANCE_COSTING?`/${ve}/${We.MASS_ACTION.FINANCE_COSTING_APPROVED}`:`/${ve}/massAction/changeMaterialApproved`;const ft=qt=>{qt.statusCode>=200&&qt.statusCode<300?(oe(!1),Ue(pe==null?void 0:pe.MDG_MAT_DYN_BTN_SNACKBAR_SUCC_MSG,"success"),Be("/masterDataCockpit/materialMaster/materialSingle")):(oe(!1),Ue(pe==null?void 0:pe.MDG_MAT_DYN_BTN_SNACKBAR_FAIL_MSG,"error"))},Ct=qt=>{Ue((qt==null?void 0:qt.message)||"Failed Submitting Request.","error"),oe(!1)};var Oe;const At={requestId:(Ht=t==null?void 0:t.payloadData)==null?void 0:Ht.RequestId,taskId:(rt==null?void 0:rt.taskId)||"",taskName:(rt==null?void 0:rt.taskDesc)||"",comments:g||de,creationDate:rt!=null&&rt.createdOn?ws(rt==null?void 0:rt.createdOn):null,dueDate:rt!=null&&rt.criticalDeadline?ws(rt==null?void 0:rt.criticalDeadline):null};Oe=s===b.CREATE||s===b.CREATE_WITH_UPLOAD||s===b.EXTEND||s===b.EXTEND_WITH_UPLOAD?Ut(t):is(!0),Xe(ht,"post",ft,Ct,rt.ATTRIBUTE_2===b.FINANCE_COSTING?At:Oe)},kt=()=>{xe(),oe(!0);const pe=s===b.CREATE||s===b.CREATE_WITH_UPLOAD?`/${ve}${We.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`:s===b.EXTEND||s===b.EXTEND_WITH_UPLOAD?`/${ve}${We.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`:`/${ve}${We.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`,ht=s===b.CREATE||s===b.CREATE_WITH_UPLOAD||s===b.EXTEND||s===b.EXTEND_WITH_UPLOAD?Ut(t):is(!0);Xe(pe,"post",Oe=>{(Oe==null?void 0:Oe.statusCode)===Vt.STATUS_200?(Ue(Oe.message,"success"),It(fi({data:{}})),Be(To.MY_TASK)):Ue(Oe.error,"error"),oe(!1)},Oe=>{Ue(Oe.error,"error"),oe(!1)},ht)},qs=()=>{xe(),oe(!0);const pe=s===b.CREATE||s===b.CREATE_WITH_UPLOAD?`/${ve}${We.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`:s===b.EXTEND||s===b.EXTEND_WITH_UPLOAD?`/${ve}${We.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`:`/${ve}${We.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`,ht=s===b.CREATE||s===b.CREATE_WITH_UPLOAD||s===b.EXTEND||s===b.EXTEND_WITH_UPLOAD?Ut(t):is(!0);Xe(pe,"post",Oe=>{(Oe==null?void 0:Oe.statusCode)===Vt.STATUS_200?(Ue(Oe.message,"success"),It(fi({data:{}})),Be(To.MY_TASK)):Ue(Oe.error,"error"),oe(!1)},Oe=>{Ue(Oe.error,"error"),oe(!1)},ht)},Ft=()=>{xe(),oe(!0);const pe=s===b.CREATE||s===b.CREATE_WITH_UPLOAD?`/${ve}${We.MASS_ACTION.CREATE_MATERIAL_REJECTION}`:s===b.EXTEND||s===b.EXTEND_WITH_UPLOAD?`/${ve}${We.MASS_ACTION.EXTEND_MATERIAL_REJECTION}`:`/${ve}${We.MASS_ACTION.CHANGE_MATERIAL_REJECTION}`,ht=s===b.CREATE||s===b.CREATE_WITH_UPLOAD||s===b.EXTEND||s===b.EXTEND_WITH_UPLOAD?Ut(t):is(!0);Xe(pe,"post",Oe=>{(Oe==null?void 0:Oe.statusCode)===Vt.STATUS_200?(Ue(Oe.message,"success"),It(fi({data:{}})),Be(To.MY_TASK)):Ue(Oe.error,"error"),oe(!1)},Oe=>{Ue(Oe.error,"error"),oe(!1)},ht)},ee=pe=>{oe(!0);const ht=(rt==null?void 0:rt.ATTRIBUTE_2)===b.FINANCE_COSTING?`/${ve}${We.MASS_ACTION.VALIDATE_FINANCE_COSTING}?requestId=${it==null?void 0:it.slice(3)}`:`/${ve}${We.MASS_ACTION.VALIDATE_MATERIAL}`,ft=(rt==null?void 0:rt.ATTRIBUTE_2)===b.FINANCE_COSTING?Lt():s===b.CREATE||s===b.CREATE_WITH_UPLOAD||s===b.EXTEND||s===b.EXTEND_WITH_UPLOAD?Ut(t):s===b.CHANGE||s===b.CHANGE_WITH_UPLOAD?xt&&it?is(!0):!xt&&!it?is(!1):!xt&&it?is(!0):[]:[];Xe(ht,"post",At=>{if(oe(!1),(At==null?void 0:At.statusCode)===Vt.STATUS_200){if(Ue((pe==null?void 0:pe.MDG_MAT_DYN_BTN_SNACKBAR_SUCC_MSG)||(At==null?void 0:At.message),"success"),(Qt||xt)&&(s===b.CHANGE||s===b.CHANGE_WITH_UPLOAD)||s===b.CREATE_WITH_UPLOAD||s===b.EXTEND_WITH_UPLOAD||s===b.EXTEND){Be(To.REQUEST_BENCH);return}Be(To.MY_TASK)}else Ue((pe==null?void 0:pe.MDG_MAT_DYN_BTN_SNACKBAR_FAIL_MSG)||"Validation failed.","error")},()=>{Ue(pe==null?void 0:pe.MDG_MAT_DYN_BTN_SNACKBAR_FAIL_MSG,"error"),oe(!1)},ft)},ye=()=>{xe(),oe(!0);const pe=s===b.CREATE||s===b.CREATE_WITH_UPLOAD?`/${ve}/massAction/createMaterialApprovalSubmit`:s===b.EXTEND||s===b.EXTEND_WITH_UPLOAD?`/${ve}/massAction/extendMaterialSubmitForReview`:`/${ve}/massAction/changeMaterialApprovalSubmit`,ht=Oe=>{Oe.statusCode===Vt.STATUS_200?(oe(!1),Ue(`Request Submitted for Approval with Request ID ${Oe==null?void 0:Oe.body}`,"success"),Ge(),Be("/masterDataCockpit/materialMaster/materialSingle")):Ue(Oe==null?void 0:Oe.message,"error")},ft=Oe=>{Ue((Oe==null?void 0:Oe.error)||"Failed Submitting Request.","error"),oe(!1)};var Ct;Ct=s===b.CREATE||s===b.CREATE_WITH_UPLOAD||s===b.EXTEND||s===b.EXTEND_WITH_UPLOAD?Ut(t):is(!0),Xe(pe,"post",ht,ft,Ct),l(!0),Ge()},G=()=>{xe(),oe(!0);const pe=`/${ve}${We.MASS_ACTION.EXTEND_MATERIAL_SAVE_AS_DRAFT}`,ht=Oe=>{Oe.statusCode===Vt.STATUS_200?(oe(!1),Ue(Oe==null?void 0:Oe.message,"success"),Be(To.REQUEST_BENCH)):(oe(!1),Ue(Oe==null?void 0:Oe.message,"error"))},ft=Oe=>{Ue(Oe==null?void 0:Oe.error,"error"),oe(!1)};let Ct;Ct=Ut(t),Xe(pe,"post",ht,ft,Ct)},ue=(pe,ht)=>{switch(pe){case"handleSubmitForApproval":Pe();break;case"handleSubmitForReview":ye();break;case"handleSendBack":kt();break;case"handleCorrection":qs();break;case"handleReject":Ft();break;case"Validate":w(ht);break;case"handleValidate":w(ht);break;case"handleSAPSyndication":wt(ht);break;case"handleDraft":G();break;case"handleSubmit":ye();break;default:console.log("Unknown action type")}},w=pe=>{var ht,ft;Jt((ht=fo)==null?void 0:ht.VALIDATE),K(ge((ft=qn)==null?void 0:ft.VALIDATE_MSG)),s===b.CREATE||s===b.EXTEND||s===b.CREATE_WITH_UPLOAD||s===b.EXTEND_WITH_UPLOAD||(rt==null?void 0:rt.ATTRIBUTE_2)===b.FINANCE_COSTING?ee(pe):Te(pe)},Te=pe=>{Array.isArray(o)?Ye(pe):typeof o=="object"&&ut(pe)},je=()=>{const pe=ls==null?void 0:ls["Config Data"],ht={};return Object.entries(pe).forEach(([ft,Ct])=>{const Oe=Ct.filter(At=>{var Ht;return At.visibility===((Ht=Wn)==null?void 0:Ht.MANDATORY)}).map(At=>({jsonName:At.jsonName,fieldName:At.fieldName}));if(!(Oe!=null&&Oe.some(At=>At.jsonName==="Material"))){const At=Ct.find(Ht=>Ht.jsonName==="Material");At&&Oe.push({jsonName:At.jsonName,fieldName:At.fieldName})}(Oe==null?void 0:Oe.length)>0&&(ht[ft]=Oe)}),ht},ie=(pe,ht)=>{var ft,Ct;if(Array.isArray(o)){const Oe=ks===((ft=Je)==null?void 0:ft.LOGISTIC)?[...ht,{jsonName:"AltUnit",fieldName:"Alternative Unit of Measure"}]:ks===((Ct=Je)==null?void 0:Ct.UPD_DESC)?[...ht,{jsonName:"Langu",fieldName:"Language"}]:ht,At={};return pe==null||pe.forEach((Ht,qt)=>{var us;const Yt=(us=Oe==null?void 0:Oe.filter(Ee=>!Ht[Ee==null?void 0:Ee.jsonName]||Ht[Ee==null?void 0:Ee.jsonName]===""))==null?void 0:us.map(Ee=>Ee==null?void 0:Ee.fieldName);(Yt==null?void 0:Yt.length)>0&&(At[qt]={id:Ht.id,slNo:Ht.slNo,missingFields:Yt})}),At}else if(typeof o=="object"){let Oe={},At=0;return Object.keys(pe).forEach(Ht=>{pe[Ht].forEach(qt=>{var us;const Yt=(us=ht[Ht])==null?void 0:us.filter(Ee=>!qt[Ee.jsonName]||qt[Ee.jsonName]==="").map(Ee=>Ee.fieldName);Yt.length>0&&(Oe[At]={id:qt.id,slNo:qt.slNo,type:qt.type,missingFields:Yt},At++)})}),Oe}},ut=pe=>{var Oe,At,Ht,qt;const ht=Object.fromEntries(Object.entries(o).map(([Yt,us])=>[Yt,us.filter(Ee=>{var ct;return(ct=c==null?void 0:c[Yt])==null?void 0:ct.includes(Ee.id)})])),ft=je(),Ct=ie(ht,ft);if(It(da(Ct)),Object.keys(Ct).length>0){const Yt=Object.keys(Ct).map(ct=>{var ps,Ts,No;return{"Table Name":(ps=Ct[ct])==null?void 0:ps.type,"Sl. No":(Ts=Ct[ct])==null?void 0:Ts.slNo,"Missing Fields":(No=Ct[ct].missingFields)==null?void 0:No.join(", ")}});N(!0),Ds("danger"),ts("Please Fill All the Mandatory Fields : ");const us=(Oe=Object.keys(Yt[0]))==null?void 0:Oe.map(ct=>({field:ct,headerName:(ct==null?void 0:ct.charAt(0).toUpperCase())+(ct==null?void 0:ct.slice(1)),flex:ct==="Sl. No"?.5:ct==="Missing Fields"?3:1.5,align:"center",headerAlign:"center"}));k(us);const Ee=Yt==null?void 0:Yt.map(ct=>({...ct,id:mo()}));x(Ee),q(!0),_e(),It(Ll(!0))}else{if(s===((At=b)==null?void 0:At.CHANGE)||s===((Ht=b)==null?void 0:Ht.CHANGE_WITH_UPLOAD)){if(!it||it&&dt&&((qt=Object==null?void 0:Object.keys(dt))!=null&&qt.length)){Re();return}ee(pe);return}re("success"),B("Data Validated Successfully"),Ge(),It(Ll(!1)),e==null||e.setCompleted([!0,!0])}},Ye=pe=>{var Ct,Oe,At,Ht;const ht=o==null?void 0:o.filter(qt=>c==null?void 0:c.includes(qt.id)),ft=ie(ht,ls==null?void 0:ls["Mandatory Fields"]);if(It(da(ft)),Object.keys(ft).length>0){const qt=Object.keys(ft).map(Ee=>{var ct,ps;return{"Sl. No":(ct=ft[Ee])==null?void 0:ct.slNo,"Missing Fields":(ps=ft[Ee].missingFields)==null?void 0:ps.join(", ")}});N(!0),Ds("danger"),ts("Please Fill All the Mandatory Fields : ");const Yt=(Ct=Object.keys(qt[0]))==null?void 0:Ct.map(Ee=>({field:Ee,headerName:(Ee==null?void 0:Ee.charAt(0).toUpperCase())+(Ee==null?void 0:Ee.slice(1)),flex:Ee==="Sl. No"?.5:Ee==="Missing Fields"?3:1,align:"center",headerAlign:"center"}));k(Yt);const us=qt==null?void 0:qt.map(Ee=>({...Ee,id:mo()}));x(us),q(!0),_e(),It(Ll(!0))}else{if(s===((Oe=b)==null?void 0:Oe.CHANGE)||s===((At=b)==null?void 0:At.CHANGE_WITH_UPLOAD)){if(!it||it&&dt&&((Ht=Object==null?void 0:Object.keys(dt))!=null&&Ht.length)){Re();return}ee(pe);return}re("success"),B("Data Validated Successfully"),Ge(),It(Ll(!1)),e==null||e.setCompleted([!0,!0])}},lt=()=>{var pe;if(M&&!de){He(!0);return}else Js==="scheduleSyndication"?le!=null&&le.current&&((pe=le==null?void 0:le.current)==null||pe.handlePriorityDialogClickOpen()):ue(fs,Us);pt()},pt=()=>{Fs(!1),F(""),He(!1),we(!1)},_e=()=>{Fs(!0)};function Tt(){at(),Jt("")}const Ls=()=>{Tt()};return R(Ao,{children:[n(kr,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:R(pp,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"space-between",alignItems:"center",gap:1,width:"100%"},children:[(!it||xt&&(Ns==null?void 0:Ns.reqStatus)===((Po=Co)==null?void 0:Po.DRAFT))&&(Ce==null?void 0:Ce.RequestType)===b.CHANGE&&(Ce==null?void 0:Ce.TemplateName)===((Qs=Je)==null?void 0:Qs.SET_DNU)&&n(Le,{sx:{flex:2,marginLeft:"90px"},children:R("span",{children:[n("strong",{children:"Note"}),": All default values for ",n("strong",{children:"Set To DNU"})," template will be fetched after ",n("strong",{children:"Validation"}),"."]})}),R(Le,{sx:{display:"flex",gap:1},children:[s!==b.EXTEND_WITH_UPLOAD&&s!==b.EXTEND&&n(Ss,{children:!it||xt&&((Mt=ko)!=null&&Mt.includes(Ns==null?void 0:Ns.reqStatus))?R(Ss,{children:[n(Rt,{variant:"contained",color:"primary",onClick:()=>{var pe,ht;if(Jt(fo.SAVE),K(ge((pe=qn)==null?void 0:pe.SAVE_AS_DRAFT_MSG)),(Ce==null?void 0:Ce.RequestType)===b.CHANGE&&(!it||it&&dt&&((ht=Object==null?void 0:Object.keys(dt))!=null&&ht.length))){Re();return}at()},children:ge("Save As Draft")}),((Bs=t==null?void 0:t.payloadData)==null?void 0:Bs.RequestType)===b.CREATE&&Pt===ql.REQUEST_DETAILS&&n(_s,{title:Gh.VALIDATE_MANDATORY,children:n(Rt,{variant:"contained",color:"primary",onClick:e==null?void 0:e.validateMaterials,children:ge("Validate")})}),Pt===ql.REQUEST_DETAILS&&((Ce==null?void 0:Ce.RequestType)===b.CHANGE||(Ce==null?void 0:Ce.RequestType)===b.CHANGE_WITH_UPLOAD||(Ce==null?void 0:Ce.RequestType)===b.CREATE_WITH_UPLOAD||(Ce==null?void 0:Ce.RequestType)===b.EXTEND_WITH_UPLOAD)?n(Ss,{children:n(Rt,{variant:"contained",color:"primary",onClick:w,children:ge("Validate")})}):n(Ss,{children:Pt===ql.PREVIEW&&n(Rt,{variant:"contained",color:"primary",onClick:Ls,disabled:(Ce==null?void 0:Ce.RequestType)===((Zs=b)==null?void 0:Zs.CHANGE)||(Ce==null?void 0:Ce.RequestType)===((_o=b)==null?void 0:_o.CHANGE_WITH_UPLOAD)||(Ce==null?void 0:Ce.RequestType)===b.CREATE_WITH_UPLOAD||(Ce==null?void 0:Ce.RequestType)===b.EXTEND_WITH_UPLOAD?(Ce==null?void 0:Ce.RequestStatus)!==((Fo=Co)==null?void 0:Fo.VALIDATED_REQUESTOR):e==null?void 0:e.submitForApprovalDisabled,children:ge("Submit")})})]}):null}),((Ce==null?void 0:Ce.RequestType)===b.EXTEND||(Ce==null?void 0:Ce.RequestType)===b.EXTEND_WITH_UPLOAD&&(!it||xt&&((Io=ko)==null?void 0:Io.includes(Ns==null?void 0:Ns.reqStatus))||!xt&&it)||!xt&&it)&&((uo=e==null?void 0:e.filteredButtons)==null?void 0:uo.map((pe,ht)=>{var Yt,us,Ee,ct,ps,Ts,No,Nn,wn,Rn,Dn;const{MDG_MAT_DYN_BTN_BUTTON_NAME:ft,MDG_MAT_DYN_BTN_BUTTON_STATUS:Ct}=pe,Oe=ft==="SAP Syndication"||ft==="Submit",At=ft==="Forward"||ft==="Submit",Ht=((Yt=r==null?void 0:r.requestHeaderData)==null?void 0:Yt.RequestStatus)==="Validated-MDM"||(Ce==null?void 0:Ce.RequestStatus)==="Validated-MDM"||(Ce==null?void 0:Ce.RequestStatus)==="Validated-Requestor"||((us=r==null?void 0:r.childRequestHeaderData)==null?void 0:us.RequestStatus)==="Validated-MDM"||((Ee=r==null?void 0:r.childRequestHeaderData)==null?void 0:Ee.RequestStatus)==="Validated-Requestor"||((ct=e==null?void 0:e.childRequestHeaderData)==null?void 0:ct.RequestStatus)==="Validated-MDM"||((ps=e==null?void 0:e.childRequestHeaderData)==null?void 0:ps.RequestStatus)==="Validated-Requestor";let qt=Ct==="DISABLED";return Oe&&Ht&&(qt=!1),(At&&((Ce==null?void 0:Ce.RequestType)===((Ts=b)==null?void 0:Ts.CREATE)||(Ce==null?void 0:Ce.RequestType)===((No=b)==null?void 0:No.CREATE_WITH_UPLOAD))&&!(e!=null&&e.submitForApprovalDisabled)||((Ce==null?void 0:Ce.RequestType)===((Nn=b)==null?void 0:Nn.CHANGE)||(Ce==null?void 0:Ce.RequestType)===((wn=b)==null?void 0:wn.CHANGE_WITH_UPLOAD)||(Ce==null?void 0:Ce.RequestType)===((Rn=b)==null?void 0:Rn.EXTEND)||(Ce==null?void 0:Ce.RequestType)===((Dn=b)==null?void 0:Dn.EXTEND_WITH_UPLOAD))&&At&&Ht)&&(qt=!1),n(Rt,{variant:"contained",size:"small",sx:{...qi,mr:1},disabled:qt||ce,onClick:()=>ze(pe),children:pe.MDG_MAT_DYN_BTN_BUTTON_NAME},ht)}))]})]})}),n(ki,{dialogState:lo,openReusableDialog:_e,closeReusableDialog:pt,dialogTitle:$s,dialogMessage:Gt,handleDialogConfirm:lt,dialogOkText:"OK",dialogSeverity:Ke,showCancelButton:!0,showInputText:qe,inputText:de,blurLoading:ce,setInputText:F,mandatoryTextInput:M,remarksError:te,isTable:S,tableColumns:H,tableRows:A,isShowWFLevel:(e==null?void 0:e.showWfLevels)&&Ks,isSyndicationBtn:nt,selectedLevel:v,handleLevelChange:gs,workFlowLevels:e.workFlowLevels,setSyndicationType:Ws,syndicationType:Js,isMassSyndication:Zt}),n(fE,{ref:le,dialogTitle:$s,setDialogTitle:ts,messageDialogMessage:m,setMessageDialogMessage:B,messageDialogSeverity:Ke,setMessageDialogSeverity:Ds,handleMessageDialogClickOpen:_e,blurLoading:ce,setBlurLoading:oe,handleMessageDialogClose:pt,createPayloadFromReduxState:Ut,successMsg:p,setSuccessMsg:l,setTextInput:W,inputText:qe,handleSnackBarOpen:Ge,taskData:rt,userData:vs,currentButtonState:Us,requestType:s}),n(hE,{open:U,onClose:Ve,handleOk:ke,message:O}),m&&n(Hn,{openSnackBar:ae,alertMsg:m,alertType:u,handleSnackBarClose:Ae}),n(An,{blurLoading:ce,loaderMessage:Q}),R(un,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:Y,onClose:xe,maxWidth:"xl",children:[R(Un,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[n(mt,{variant:"h6",children:rs===fo.SAVE?"Save As Draft":"Remarks"}),n(xs,{sx:{width:"max-content"},onClick:xe,children:n(Gl,{})})]}),n(Lo,{sx:{padding:".5rem 1rem"},children:rs!==fo.SAVE?n(Ao,{sx:{marginTop:"16px"},children:n(Le,{sx:{minWidth:400},children:R(Li,{sx:{height:"auto"},fullWidth:!0,children:[n(Ln,{sx:{backgroundColor:"#F5F5F5","& .MuiOutlinedInput-root":{"& fieldset":{borderColor:Nt?(Wo=(os=$e)==null?void 0:os.error)==null?void 0:Wo.dark:"rgba(0, 0, 0, 0.23)"},"&:hover fieldset":{borderColor:Nt?(qo=(to=$e)==null?void 0:to.error)==null?void 0:qo.dark:"rgba(0, 0, 0, 0.23)"},"&.Mui-focused fieldset":{borderColor:Nt?(zo=(jo=$e)==null?void 0:jo.error)==null?void 0:zo.dark:(Xo=(Yo=$e)==null?void 0:Yo.primary)==null?void 0:Xo.dark}}},value:g,onChange:Bt,inputProps:{maxLength:Ps},multiline:!0,placeholder:"Enter Remarks"}),n(Tp,{sx:{textAlign:"right",color:Nt?(Ko=(Vo=$e)==null?void 0:Vo.error)==null?void 0:Ko.dark:"rgba(0, 0, 0, 0.6)",marginTop:"4px"},children:`${(g==null?void 0:g.length)||0}/${Ps}`})]})})}):n(Le,{sx:{margin:"15px"},children:n(mt,{sx:{fontWeight:"200"},children:qn.DRAFT_MESSAGE})})}),R(Do,{sx:{display:"flex",justifyContent:"end"},children:[n(Rt,{sx:{width:"max-content",textTransform:"capitalize"},onClick:xe,children:ge("Cancel")}),n(Rt,{className:"button_primary--normal",type:"save",disabled:ce,onClick:vt,variant:"contained",children:rs===fo.SAVE?"Yes":"Submit"})]})]})]})},df=()=>{const{customError:e}=nn(),[t,r]=d.useState([]),[o,c]=d.useState(!1),s=se(K=>K.userManagement.taskData),[p,l]=d.useState([]),u=se(K=>K.applicationConfig),re=Ro();let ae={handleSubmitForApproval:6,handleSendBack:1,handleReject:3,handleValidate:5,handleSAPSyndication:8,handleIdGenerator:4,handleSubmitForReview:7,handleCorrection:2};const y=Rr(jn.CURRENT_TASK,!0,{});return d.useEffect(()=>{const K=(s==null?void 0:s.taskDesc)||(y==null?void 0:y.taskDesc),m=t==null?void 0:t.filter(ce=>ce.MDG_MAT_DYN_BTN_TASK_NAME===K),B=m==null?void 0:m.sort((ce,oe)=>{const Q=ae[ce.MDG_MAT_DYN_BTN_ACTION_TYPE],f=ae[oe.MDG_MAT_DYN_BTN_ACTION_TYPE];return Q-f});l(B),re(Ep(B)),(B.find(ce=>ce.MDG_MAT_DYN_BTN_BUTTON_NAME===fo.SEND_BACK)||B.find(ce=>ce.MDG_MAT_DYN_BTN_BUTTON_NAME===fo.CORRECTION))&&c(!0)},[t]),{getButtonsDisplay:()=>{let K={decisionTableId:null,decisionTableName:"MDG_MAT_DYN_BUTTON_CONFIG",version:"v3",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_DYN_BTN_MODULE_NAME":"Material","MDG_CONDITIONS.MDG_MAT_DYN_BTN_REQUEST_TYPE":(s==null?void 0:s.ATTRIBUTE_2)||(y==null?void 0:y.ATTRIBUTE_2)}],systemFilters:null,systemOrders:null,filterString:null};const m=ce=>{var oe,Q;if(ce.statusCode===200){let f=(Q=(oe=ce==null?void 0:ce.data)==null?void 0:oe.result[0])==null?void 0:Q.MDG_MAT_DYN_BUTTON_CONFIG;r(f)}},B=ce=>{e(ce)};u.environment==="localhost"?Xe(`/${yo}${We.INVOKE_RULES.LOCAL}`,"post",m,B,K):Xe(`/${yo}${We.INVOKE_RULES.PROD}`,"post",m,B,K)},showWfLevels:o}},Ha=()=>{se(c=>c.payload.payloadData);const e=se(c=>c.applicationConfig);se(c=>{var s;return(s=c.userManagement)==null?void 0:s.taskData}),Ro();const t=on();return new URLSearchParams(t.search).get("RequestType"),{getDynamicWorkflowDT:(c,s,p="NA",l="GROUP-2",u=1)=>new Promise((re,ae)=>{let y={decisionTableId:null,decisionTableName:"MDG_MAT_DYNAMIC_WF_DT",version:"v4",conditions:[{"MDG_CONDITIONS.MDG_MAT_REQUEST_TYPE":c||"","MDG_CONDITIONS.MDG_MAT_REGION":s||"","MDG_CONDITIONS.MDG_MAT_TEMPLATE":p||"NA","MDG_CONDITIONS.MDG_MAT_BIFURCATION_GROUP":l||""}]};const O=m=>{var B,ce;if(m.statusCode===Vt.STATUS_200){let oe=((ce=(B=m==null?void 0:m.data)==null?void 0:B.result[0])==null?void 0:ce.MDG_MAT_DYNAMIC_WF_DT)||[],Q=[];oe==null||oe.forEach(f=>{f.MDG_DYNAMIC_WF_APPROVAL_LEVEL===parseInt(u)&&f.MDG_MAT_SENDBACK_ALLOWED.split(",").map(z=>parseInt(z)).forEach(z=>Q.push(z))}),Q=[...new Set(Q)],re(Q)}else ae(new Error("Failed to fetch workflow levels"))},K=m=>{ae(m)};e.environment==="localhost"?Xe(`/${yo}${We.INVOKE_RULES.LOCAL}`,"post",O,K,y):Xe(`/${yo}${We.INVOKE_RULES.PROD}`,"post",O,K,y)})}},gE=({requestType:e,initialPayload:t,dynamicData:r,taskData:o,singlePayloadData:c})=>{const[s,p]=d.useState([]),[l,u]=d.useState(!1),{getDynamicWorkflowDT:re}=Ha(),{customError:ae}=nn();return d.useEffect(()=>{const y=async()=>{var O,K,m;try{u(!0);const B=(t==null?void 0:t.RequestType)===b.CHANGE||(t==null?void 0:t.RequestType)===b.CHANGE_WITH_UPLOAD?await re(t==null?void 0:t.RequestType,t==null?void 0:t.Region,t==null?void 0:t.TemplateName,(O=r==null?void 0:r.childRequestHeaderData)==null?void 0:O.MaterialGroupType,o==null?void 0:o.ATTRIBUTE_3):await re(t==null?void 0:t.RequestType,t==null?void 0:t.Region,"",(m=(K=c==null?void 0:c[r])==null?void 0:K.Tochildrequestheaderdata)==null?void 0:m.MaterialGroupType,o==null?void 0:o.ATTRIBUTE_3);p(B)}catch(B){ae(B)}finally{u(!1)}};t!=null&&t.RequestType&&(t!=null&&t.Region)&&r&&(o!=null&&o.ATTRIBUTE_3)&&y()},[t==null?void 0:t.Region,r,o==null?void 0:o.ATTRIBUTE_3]),{wfLevels:s,loading:l}},uf=e=>{const[t,r]=d.useState(!0),o=se(C=>C.AllDropDown.dropDown),c=se(C=>C.payload),s=se(C=>C.payload.payloadData),p=s==null?void 0:s.RequestType,[l,u]=d.useState(!1),[re,ae]=d.useState(!1),y=se(C=>C.userManagement.taskData),O=se(C=>C.payload.filteredButtons),K=on(),m=new URLSearchParams(K.search),B=m.get("RequestType"),ce=m.get("RequestId"),oe=se(C=>C.payload.changeFieldRows),Q=se(C=>C.payload.dynamicKeyValues),f=wr(),{getButtonsDisplay:Y,showWfLevels:z}=df(),{wfLevels:g}=gE({initialPayloadRequestType:p,initialPayload:s,dynamicData:Q,taskData:y,singlePayloadData:c}),T=Cr(O,[po.HANDLE_SUBMIT_FOR_APPROVAL,po.HANDLE_SAP_SYNDICATION,po.HANDLE_SUBMIT_FOR_REVIEW]);d.useEffect(()=>{(y!=null&&y.ATTRIBUTE_1||B)&&Y()},[y]),d.useEffect(()=>{((oe==null?void 0:oe.length)!==0&&(oe==null?void 0:oe.length)!==void 0||!v())&&(ae(!0),u(!0))},[oe]);const v=()=>{var C;return(C=Object==null?void 0:Object.values(oe))==null?void 0:C.every(S=>(Array==null?void 0:Array.isArray(S))&&(S==null?void 0:S.length)===0)};return d.useEffect(()=>{e.downloadClicked&&r(!0)},[e.downloadClicked]),R("div",{children:[((s==null?void 0:s.TemplateName)&&(oe&&(oe==null?void 0:oe.length)===0||v())||e.downloadClicked)&&n(QT,{open:t,onClose:()=>{var C;r(!1),e==null||e.setDownloadClicked(!1),ce||f((C=To)==null?void 0:C.REQUEST_BENCH)},parameters:Pl[s==null?void 0:s.TemplateName],templateName:s==null?void 0:s.TemplateName,setShowTable:u,allDropDownData:o,setDownloadClicked:e==null?void 0:e.setDownloadClicked}),(l||re)&&!(e!=null&&e.downloadClicked)&&R(Ss,{children:[n(nE,{setCompleted:e==null?void 0:e.setCompleted,RequestId:ce}),n(Wr,{filteredButtons:T,setCompleted:e==null?void 0:e.setCompleted,showWfLevels:z,workFlowLevels:g})]}),n(Hi,{})]})},pE=({setIsSecondTabEnabled:e,setIsAttachmentTabEnabled:t,requestStatus:r,downloadClicked:o,setDownloadClicked:c})=>{var Js,Ws,Zt,Ue,Pt,Ps,le,Ge;const[s,p]=d.useState({}),[l,u]=d.useState(!1),[re,ae]=d.useState(!1),[y,O]=d.useState("success"),[K,m]=d.useState(!1),[B,ce]=d.useState([]);d.useState(!1);const[oe,Q]=d.useState(),[f,Y]=d.useState({}),[z,g]=d.useState(!1),[T,v]=d.useState("systemGenerated"),[C,S]=d.useState(""),[q,A]=d.useState(""),[x,H]=d.useState([]),[k,L]=d.useState(!1),N=Ro(),U=wr(),I=se(Ae=>Ae.payload.payloadData),ge=se(Ae=>Ae.tabsData.requestHeaderData),te=se(Ae=>Ae.tabsData.changeFieldsDT);let He=se(Ae=>Ae.userManagement.roles);const M=se(Ae=>Ae.payload.payloadData),we=se(Ae=>Ae.userManagement.userData),de=se(Ae=>{var Re,Ve;return(Ve=(Re=Ae.userManagement)==null?void 0:Re.entitiesAndActivities)==null?void 0:Ve.Material}),F=se(Ae=>Ae.request.requestHeader),qe=se(Ae=>Ae.request.salesOrgDTData),W=on(),Ke=new URLSearchParams(W.search),Ds=Ke.get("reqBench"),Gt=Ke.get("RequestId"),{t:Gs}=Sn(),{getRequestHeaderTemplate:$s}=nf(),{getChangeTemplate:ts}=mT(),{fetchOrgData:lo}=Da(),{getDtCall:Fs}=$h(),{customError:Us}=nn(),fs=[{code:"Create",desc:"Create New Material in Application"},{code:"Change",desc:"Modify Existing Material in Application"},{code:"Extend",desc:"Extend Existing Material in Application"},{code:"Create with Upload",desc:"Create New Material with Excel Upload"},{code:"Change with Upload",desc:"Modify Existing Material with Excel Upload"},{code:"Extend with Upload",desc:"Extend Existing Material with Excel Upload"}].filter(Ae=>de==null?void 0:de.includes(Ae.code)),Dt=[{code:"Oncology",desc:""},{code:"Anesthesia/Pain Management",desc:""},{code:"Cardiovascular",desc:""}],rs=[{code:(Js=Je)==null?void 0:Js.LOGISTIC,desc:""},{code:(Ws=Je)==null?void 0:Ws.MRP,desc:""},{code:(Zt=Je)==null?void 0:Zt.WARE_VIEW_2,desc:""},{code:(Ue=Je)==null?void 0:Ue.ITEM_CAT,desc:""},{code:(Pt=Je)==null?void 0:Pt.SET_DNU,desc:""},{code:(Ps=Je)==null?void 0:Ps.UPD_DESC,desc:""},{code:(le=Je)==null?void 0:le.CHG_STAT,desc:""}],Jt=[{code:"High",desc:""},{code:"Medium",desc:""},{code:"Low",desc:""}];N(xo({keyName:(Ge=Zu)==null?void 0:Ge.REQUEST_TYPE,data:fs})),N(xo({keyName:"LeadingCat",data:Dt})),N(xo({keyName:"RequestPriority",data:Jt})),N(xo({keyName:"TemplateName",data:rs})),!Gt&&!Ds&&(N(eh({keyName:"ReqCreatedBy",data:we==null?void 0:we.user_id})),N(eh({keyName:"RequestStatus",data:"DRAFT"})));const vs="Basic Data",[rt,Be]=d.useState([vs]),[It,$t]=d.useState(""),[Ns,Ce]=d.useState(""),[dt,ls]=d.useState(!0);d.useEffect(()=>{N(mp(rt))},[N,rt]);const ks=()=>{var Re,Ve;let Ae=!0;return M&&((Re=ge[Object.keys(ge)])!=null&&Re.length)?(Ve=ge[Object.keys(ge)[0]])==null||Ve.forEach(ke=>{var at;!M[ke.jsonName]&&ke.visibility===((at=Wn)==null?void 0:at.MANDATORY)&&(Ae=!1)}):Ae=!1,Ae};d.useEffect(()=>{M!=null&&M.MatlType&&is(M),ks()},[M]);const is=Ae=>{var ke;const Re=at=>{$t(at.body[0].MaintStatus.split("")),Ce(at.body[0].MaterialType)},Ve=at=>{console.log(at)};Xe(`/${ve}/data/getViewForMaterialType?materialType=${(ke=Ae==null?void 0:Ae.MatlType)==null?void 0:ke.code}`,"get",Re,Ve)},ss=()=>{m(!0)},it=()=>{m(!1)},xt=()=>{var Ae;c(!1),g(!1),v("systemGenerated"),Gt||U((Ae=To)==null?void 0:Ae.REQUEST_BENCH)},Es=Ae=>{var Re;v((Re=Ae==null?void 0:Ae.target)==null?void 0:Re.value)},Ms=()=>{T==="systemGenerated"&&(Qt(),xt()),T==="mailGenerated"&&(as(),xt())},Qt=()=>{S("Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience."),A(!0);let Ae={region:I==null?void 0:I.Region,scenario:I==null?void 0:I.RequestType,matlType:"ALL",dtName:"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",version:"v1",rolePrefix:"",requestId:F!=null&&F.requestId?F==null?void 0:F.requestId:I!=null&&I.RequestId?I==null?void 0:I.RequestId:""};const Re=at=>{if((at==null?void 0:at.size)==0){A(!1),S(""),ae(!0),Q("No data found for the selected criteria."),O("danger"),ss();return}const xe=URL.createObjectURL(at),Bt=document.createElement("a");Bt.href=xe,Bt.setAttribute("download",`${(I==null?void 0:I.RequestType)===b.EXTEND_WITH_UPLOAD?"Mass_Extend.xlsx":"Mass_Create.xlsx"}`),document.body.appendChild(Bt),Bt.click(),document.body.removeChild(Bt),URL.revokeObjectURL(xe),A(!1),S(""),ae(!0),Q(`${I!=null&&I.TemplateName?`${I.TemplateName}_Mass Change`:(I==null?void 0:I.RequestType)===b.EXTEND_WITH_UPLOAD?"Mass_Extend":"Mass_Create"}.xlsx has been downloaded successfully.`),O("success"),ss(),setTimeout(()=>{U("/requestBench")},2600)},Ve=()=>{A(!1)},ke=`/${ve}${(I==null?void 0:I.RequestType)===b.EXTEND_WITH_UPLOAD?We.EXCEL.DOWNLOAD_EXCEL_FOR_EXTEND:We.EXCEL.DOWNLOAD_EXCEL}`;Xe(ke,"postandgetblob",Re,Ve,Ae)},as=()=>{A(!0);let Ae={region:I==null?void 0:I.Region,scenario:I==null?void 0:I.RequestType,matlType:"ALL",dtName:"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",version:"v1",rolePrefix:"",requestId:F!=null&&F.requestId?F==null?void 0:F.requestId:I!=null&&I.RequestId?I==null?void 0:I.RequestId:""};const Re=()=>{var at;A(!1),S(""),ae(!0),Q((at=Sp)==null?void 0:at.DOWNLOAD_MAIL_INITIATED),O("success"),ss(),setTimeout(()=>{var xe;U((xe=To)==null?void 0:xe.REQUEST_BENCH)},2600)},Ve=()=>{var at;A(!1),ae(!0),Q((at=hn)==null?void 0:at.ERR_DOWNLOADING_EXCEL),O("danger"),ss(),setTimeout(()=>{var xe;U((xe=To)==null?void 0:xe.REQUEST_BENCH)},2600)},ke=`/${ve}${(I==null?void 0:I.RequestType)===b.EXTEND_WITH_UPLOAD?We.EXCEL.DOWNLOAD_EXCEL_FOR_EXTEND_MAIL:We.EXCEL.DOWNLOAD_EXCEL_MAIL}`;Xe(ke,"post",Re,Ve,Ae)},io=()=>u(!1),Qe=Ae=>{if(B.includes("Distribution Channel")){const Re=ke=>H(ke==null?void 0:ke.body),Ve=ke=>console.error(ke);Xe(`/${ve}/data/getDistrChan?salesOrg=${Ae.code}`,"get",Re,Ve)}},Lt={orgData:["Plant","Sales Organization","Distribution Channel"].map(Ae=>({info:f[Ae]||{code:"",desc:""},desc:Ae})),selectedViews:{selectedSections:rt}},Ut=(Ae,Re)=>{Y(Ve=>({...Ve,[Ae]:Re})),Ae==="Sales Organization"&&Qe(Re)},Nt=`/Date(${Date.now()})/`,ms=()=>{var xe;let Ae=Cp(M==null?void 0:M.Region,He);N(Ap({...we,role:Ae})),L(!1);const Re=new Date(M==null?void 0:M.ReqCreatedOn).getTime(),Ve={RequestId:F!=null&&F.requestId?F==null?void 0:F.requestId:"",Region:(M==null?void 0:M.Region)||"",MatlType:(M==null?void 0:M.MatlType)||"",ReqCreatedBy:(we==null?void 0:we.user_id)||"",ReqCreatedOn:Re?`/Date(${Re})/`:Nt,ReqUpdatedOn:Re?`/Date(${Re})/`:Nt,RequestType:(M==null?void 0:M.RequestType)||"",RequestDesc:(M==null?void 0:M.RequestDesc)||"",Division:(M==null?void 0:M.Division)||"",RequestStatus:"DRAFT",RequestPriority:(M==null?void 0:M.RequestPriority)||"",LeadingCat:(M==null?void 0:M.LeadingCat)||"",FieldName:((xe=M==null?void 0:M.FieldName)==null?void 0:xe.join("$^$"))||"",TemplateName:(M==null?void 0:M.TemplateName)||""},ke=Bt=>{var gs,vt,ze,Pe;if(ae(!0),Q(`Request Header Created Successfully with request ID ${ua(M==null?void 0:M.RequestType,(gs=Bt==null?void 0:Bt.body)==null?void 0:gs.requestId)}`),O("success"),ss(),N(Fh(Bt.body)),N(Hr({keyName:Zu.REQUEST_ID,data:(vt=Bt==null?void 0:Bt.body)==null?void 0:vt.requestId})),t(!0),ls(!1),N(Br({})),N(Np({})),(I==null?void 0:I.RequestType)===b.CREATE_WITH_UPLOAD||(I==null?void 0:I.RequestType)===b.EXTEND_WITH_UPLOAD){g(!0);return}if((I==null?void 0:I.RequestType)===((ze=b)==null?void 0:ze.CHANGE_WITH_UPLOAD)){L(!0);return}if((I==null?void 0:I.RequestType)===((Pe=b)==null?void 0:Pe.CHANGE)){const wt=wp(te==null?void 0:te["Config Data"],I==null?void 0:I.FieldName,["Material","Plant","Sales Org","Distribution Channel","Warehouse","MRP Controller"]);N(Wh({...te,"Config Data":wt}));const kt=Rp(te==null?void 0:te[I==null?void 0:I.TemplateName],I==null?void 0:I.FieldName);N(_p([...kt]))}setTimeout(()=>{N(Lr(1)),e(!0)},2500)},at=()=>{ae(!0),O("error"),Q("Error occured while saving Request Header"),ss()};Xe(`/${ve}/alter/createRequestHeader`,"post",ke,at,Ve)};d.useEffect(()=>{var Ae;if(o){if((I==null?void 0:I.RequestType)===b.CREATE_WITH_UPLOAD||(I==null?void 0:I.RequestType)===b.EXTEND_WITH_UPLOAD){g(!0);return}if((I==null?void 0:I.RequestType)===((Ae=b)==null?void 0:Ae.CHANGE_WITH_UPLOAD)){L(!0);return}}},[o]);function Ks(Ae){return Ae.every(Re=>Re.info.code&&Re.info.desc)}const Hs=()=>{if(!Ks(Lt.orgData))ae(!0),O("error"),Q("Please choose all mandatory fields"),ss();else{const Re={label:"Attachments & Comments",value:"attachments&comments"},ke=[{label:"General Information",value:"generalInformation"},...rt,Re];Lt.selectedViews=ke,N(bp(Lt)),N(Lr(1)),e(!0)}};d.useEffect(()=>{$s()},[I==null?void 0:I.RequestType]);const nt=(Ae="")=>{var at,xe,Bt,gs;const Re={materialNo:Ae??"",top:500,skip:0,salesOrg:((xe=(at=qe==null?void 0:qe.uniqueSalesOrgList)==null?void 0:at.map(vt=>vt.code))==null?void 0:xe.join("$^$"))||""},Ve=vt=>{(vt==null?void 0:vt.statusCode)===Vt.STATUS_200&&(N(xo({keyName:Ci.RETURN_MAT_NUMBER,data:vt==null?void 0:vt.body})),N(xo({keyName:Ci.PARENT_MAT_NUMBER,data:vt==null?void 0:vt.body})))},ke=vt=>{Us(vt)};Xe(`/${ve}${(gs=(Bt=We)==null?void 0:Bt.DATA)==null?void 0:gs.GET_SEARCH_PARAMS_MATERIAL_NO}`,"post",Ve,ke,Re)};d.useEffect(()=>{qe!=null&&qe.uniqueSalesOrgList&&nt()},[]);const cs=Ae=>{let Re={decisionTableId:null,decisionTableName:Ei.MDG_MAT_REGION_DIVISION_MAPPING,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":Ae||""}]};Fs(Re)};return d.useEffect(()=>{I!=null&&I.Region&&(lo(),cs(I==null?void 0:I.Region))},[I==null?void 0:I.Region]),d.useEffect(()=>{I!=null&&I.TemplateName&&(((I==null?void 0:I.TemplateName)===Je.MRP||(I==null?void 0:I.TemplateName)===Je.WARE_VIEW_2)&&N(Hr({keyName:"FieldName",data:void 0})),ts())},[I==null?void 0:I.TemplateName]),n("div",{children:R(Ao,{spacing:2,children:[Object.entries(ge).map(([Ae,Re])=>R(Rs,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Oa},children:[n(mt,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:Gs(Ae)}),n(Le,{children:n(Rs,{container:!0,spacing:1,children:Re.filter(Ve=>Ve.visibility!=="Hidden").sort((Ve,ke)=>Ve.sequenceNo-ke.sequenceNo).map(Ve=>n(TT,{isHeader:!0,field:Ve,dropDownData:s,disabled:Gt||(F==null?void 0:F.requestId),requestHeader:!0},Ve.id))})}),!Gt&&!(F!=null&&F.requestId)&&n(Le,{sx:{display:"flex",justifyContent:"flex-end",marginTop:"20px"},children:n(Rt,{variant:"contained",color:"primary",disabled:!ks(),onClick:ms,children:Gs("Save Request Header")})}),n(Hi,{})]},Ae)),R(un,{open:l,onClose:io,children:[n(Un,{sx:{backgroundColor:"#EAE9FF"},children:"Select Org Data"}),n(Lo,{children:n(Rs,{container:!0,columnSpacing:1,children:B.map((Ae,Re)=>R(d.Fragment,{children:[n(Rs,{item:!0,md:4,children:R(mt,{children:[Ae,n("span",{style:{color:"red"},children:"*"})]})}),n(Rs,{item:!0,md:8,children:n(Ul,{options:Ae==="Distribution Channel"?x:s[Ae]||[],size:"small",getOptionLabel:Ve=>`${Ve.code} - ${Ve.desc}`,renderOption:(Ve,ke)=>n("li",{...Ve,children:n(mt,{children:`${ke.code} - ${ke.desc}`})}),onChange:(Ve,ke)=>Ut(Ae,ke),renderInput:Ve=>n(Ln,{...Ve,placeholder:`Select ${Ae}`})})})]},Re))})}),R(Do,{children:[n(Rt,{onClick:io,variant:"outlined",children:Gs("Cancel")}),n(Rt,{variant:"contained",onClick:()=>{Hs()},children:Gs("Proceed")})]})]}),k&&n(uf,{downloadClicked:o,setDownloadClicked:c}),n(lf,{onDownloadTypeChange:Ms,open:z,downloadType:T,handleDownloadTypeChange:Es,onClose:xt}),n(An,{blurLoading:q,loaderMessage:C}),re&&n(Hn,{openSnackBar:K,alertMsg:oe,alertType:y,handleSnackBarClose:it})]})})};var Ba={},TE=Go;Object.defineProperty(Ba,"__esModule",{value:!0});var hf=Ba.default=void 0,EE=TE(Bo()),mE=$o;hf=Ba.default=(0,EE.default)((0,mE.jsx)("path",{d:"M22 5.18 10.59 16.6l-4.24-4.24 1.41-1.41 2.83 2.83 10-10zm-2.21 5.04c.13.57.21 1.17.21 1.78 0 4.42-3.58 8-8 8s-8-3.58-8-8 3.58-8 8-8c1.58 0 3.04.46 4.28 1.25l1.44-1.44C16.1 2.67 14.13 2 12 2 6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10c0-1.19-.22-2.33-.6-3.39z"}),"TaskAlt");const yl={plant:[P.ACCOUNTING,P.COSTING,P.MRP,P.SALES,P.PURCHASING,P.FORCASTING,P.WAREHOUSE_MANAGEMENT,P.WORK_SCHEDULING,P.STORAGE_LOCATION_STOCKS,P.PRODUCTION,P.PLANT_STOCKS],salesOrg:[P.SALES],distributionChannel:[P.SALES],storageLocation:[P.MRP,P.STORAGE,P.STORAGE_LOCATION_STOCKS],mrpProfile:[P.MRP],warehouse:[P.WAREHOUSE],storage:[P.STORAGE]},CE=(e,t,r,o,c)=>({checkValidation:(p,l,u,re,ae)=>{var Q,f,Y,z,g,T,v,C,S,q;const y=(Q=e==null?void 0:e[p])==null?void 0:Q.payloadData,O=(f=e==null?void 0:e[p])==null?void 0:f.headerData;(Y=e==null?void 0:e[p])==null||Y.ManufacturerID;const K=(z=e==null?void 0:e.payloadData)==null?void 0:z.Region;if(!(O!=null&&O.materialNumber))return{missingFields:["Material number"],isValid:!1};if(!(O!=null&&O.globalMaterialDescription)||!y)return{missingFields:["Material Description"],isValid:!1};const m=Ip(y[P.BASIC_DATA]);m.Material=O==null?void 0:O.materialNumber,m.MatlDesc=O==null?void 0:O.globalMaterialDescription;const B=t==null?void 0:t.find(A=>{var x;return(A==null?void 0:A[K])&&(A==null?void 0:A[K][(x=O==null?void 0:O.materialType)==null?void 0:x.code])}),ce=B&&B[K]&&((T=B[K][(g=O==null?void 0:O.materialType)==null?void 0:g.code])==null?void 0:T.mandatoryFields),oe=ce==null?void 0:ce[P.BASIC_DATA];if((oe==null?void 0:oe.length)>0){for(const A of oe)if(!m[A==null?void 0:A.jsonName])return{missingFields:vp(oe,m),viewType:P.BASIC_DATA,isValid:!1,plant:[P.BASIC_DATA]}}if(r.includes(P.PURCHASING)){const A=ce==null?void 0:ce[P.PURCHASING];if(A)if(y[P.PURCHASING]){const{validCount:x}=Qi(l,P.PURCHASING),{totalCount:H,allValid:k}=Zi(y[P.PURCHASING],A);if(H===x)if(k){if(!ea(y[P.PURCHASING],A)){const L=dr(l),N=ai(L,y[P.PURCHASING],A);return{missingFields:mn(A,y[P.PURCHASING]),viewType:P.PURCHASING,isValid:!1,plant:N==null?void 0:N.missingFields}}}else{const L=dr(l),N=th(L,y[P.PURCHASING]);return{missingFields:mn(A,y[P.PURCHASING]),viewType:P.PURCHASING,isValid:!1,plant:N}}else{const L=dr(l),N=ai(L,y[P.PURCHASING],A);return{missingFields:mn(A,y[P.PURCHASING]),viewType:P.PURCHASING,isValid:!1,plant:N==null?void 0:N.missingFields}}}else{const x=dr(l);return{missingFields:mn(A,y[P.PURCHASING]),viewType:P.PURCHASING,isValid:!1,plant:x}}}if(r.includes(P.MRP)){const A=ce==null?void 0:ce[P.MRP];if(A){const x=Mp(l);if(y[P.MRP]){const{validCount:H}=Qi(l,P.MRP),{totalCount:k,allValid:L}=Zi(y[P.MRP],A);if(k===H)if(L){if(!ea(y[P.MRP],A)){const N=dr(l),U=ai(N,y[P.MRP],A),I=mn(A,y[P.MRP]),ge=ta(U==null?void 0:U.missingFields,(S=x==null?void 0:x[P.MRP])==null?void 0:S.displayCombinations);return{missingFields:I,viewType:P.MRP,isValid:!1,plant:ge}}}else{const N=dr(l),U=th(N,y[P.MRP]),I=mn(A,y[P.MRP]),ge=ta(U,(C=x==null?void 0:x[P.MRP])==null?void 0:C.displayCombinations);return{missingFields:I,viewType:P.MRP,isValid:!1,plant:ge}}else{const N=dr(l),U=ai(N,y[P.MRP],A),I=mn(A,y[P.MRP]),ge=ta(U==null?void 0:U.missingFields,(q=x==null?void 0:x[P.MRP])==null?void 0:q.displayCombinations);return{missingFields:I,viewType:P.MRP,isValid:!1,plant:ge}}}else return{missingFields:mn(A,y[P.MRP]),viewType:P.MRP,isValid:!1,plant:(v=x==null?void 0:x[P.MRP])==null?void 0:v.displayCombinations}}}if(r.includes(P.SALES)){const A=ce==null?void 0:ce[P.SALES];if(A)if(y[P.SALES]){const{validCount:x}=Qi(l,P.SALES),{totalCount:H,allValid:k}=Zi(y[P.SALES],A);if(H===x)if(k){if(!ea(y[P.SALES],A))return{missingFields:mn(A,y[P.SALES]),viewType:P.SALES,isValid:!1}}else return{missingFields:mn(A,y[P.SALES]),viewType:P.SALES,isValid:!1};else return{missingFields:mn(A,y[P.SALES]),viewType:P.SALES,isValid:!1}}else return{missingFields:mn(A,y[P.SALES]),viewType:P.SALES,isValid:!1}}return{missingFields:null,isValid:!0}}}),ff=()=>{const e=Ro(),{fetchDataAndDispatch:t}=Bi(),r=se(c=>c.payload.valuationClassData||{});return{fetchValuationClassData:c=>{if(!c)return;c in r?e(xo({keyName:Ci.VAL_CLASS,data:r[c]})):t(`/${ve}${We.DATA.GET_VALUATION_CLASS}?matlType=${c}`,Ci.VAL_CLASS)}}};var Ga={},AE=Go;Object.defineProperty(Ga,"__esModule",{value:!0});var bi=Ga.default=void 0,bE=AE(Bo()),SE=$o;bi=Ga.default=(0,bE.default)((0,SE.jsx)("path",{d:"M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12zm-1 4 6 6v10c0 1.1-.9 2-2 2H7.99C6.89 23 6 22.1 6 21l.01-14c0-1.1.89-2 1.99-2zm-1 7h5.5L14 6.5z"}),"FileCopy");const gf=({open:e,onClose:t,title:r,lengthOfOrgRow:o,selectedMaterialPayload:c,materialID:s,orgRows:p})=>{var O,K;const[l,u]=d.useState({}),re=Ro(),ae=()=>{const m=[];return p&&p.length>0&&(p==null||p.forEach((B,ce)=>{var oe,Q,f,Y,z,g,T,v,C,S,q,A,x,H;if(ce!==(o==null?void 0:o.copyFor)){const k=(Q=(oe=B.plant)==null?void 0:oe.value)==null?void 0:Q.code,L=((Y=(f=B.plant)==null?void 0:f.value)==null?void 0:Y.desc)||k,N=(z=B.salesOrg)==null?void 0:z.code,U=((g=B.salesOrg)==null?void 0:g.desc)||N,I=(v=(T=B.dc)==null?void 0:T.value)==null?void 0:v.code,ge=((S=(C=B.dc)==null?void 0:C.value)==null?void 0:S.desc)||I,te=(A=(q=B.warehouse)==null?void 0:q.value)==null?void 0:A.code,He=((H=(x=B.warehouse)==null?void 0:x.value)==null?void 0:H.desc)||te;if(k){let M=`Plant: ${L||"N/A"}`;N&&(M+=` | SalesOrg: ${U||"N/A"}`),I&&(M+=` | DC: ${ge||"N/A"}`),te&&(M+=` | Warehouse: ${He||"N/A"}`);let we=k;N&&(we+=`-${N}`),I&&(we+=`-${I}`),te&&(we+=`-${te}`),m==null||m.push({code:we,desc:M,index:ce,plant:k,salesOrg:N,dc:I,warehouse:te})}}})),m},y=()=>{var Y,z,g,T,v,C,S,q;if(!l.code)return;const m=p[o.copyFor],B=(z=(Y=m==null?void 0:m.plant)==null?void 0:Y.value)==null?void 0:z.code,ce=(g=m==null?void 0:m.salesOrg)==null?void 0:g.code,oe=(v=(T=m==null?void 0:m.dc)==null?void 0:T.value)==null?void 0:v.code,Q=(S=(C=m==null?void 0:m.warehouse)==null?void 0:C.value)==null?void 0:S.code;if(!B)return;const f=JSON.parse(JSON.stringify(c));(q=Object.keys(f))==null||q.forEach(A=>{const x=f[A];if(!(A===P.BASIC_DATA||A===P.SALES_GENERAL||A===P.PURCHASING_GENERAL||A===P.TAX_DATA)&&typeof x=="object"){const H=Object.keys(x);if(A===P.WAREHOUSE){const k=H==null?void 0:H.find(N=>N.includes(l.warehouse)),L=H==null?void 0:H.find(N=>N.includes(Q));if(k&&L&&L!==k){const N=JSON.parse(JSON.stringify(x[k]));delete N.WarehouseId,f[A][L]={...JSON.parse(JSON.stringify(f[A][L]||{})),...N}}}else if(A===P.SALES){const k=`${l.salesOrg}-${l.dc}`,L=`${ce}-${oe}`,N=H==null?void 0:H.find(I=>I===k),U=H==null?void 0:H.find(I=>I===L);if(N&&U&&U!==N){const I=JSON.parse(JSON.stringify(x[N]));delete I.SalesId,f[A][U]={...JSON.parse(JSON.stringify(f[A][U]||{})),...I}}}else{const k=H==null?void 0:H.find(N=>N.includes(l.plant)),L=H==null?void 0:H.find(N=>N.includes(B));if(k&&L&&L!==k){const N=JSON.parse(JSON.stringify(x[k]));N&&(delete N.SalesId,delete N.PlantId,delete N.StorageLocationId,delete N.AccountingId,L&&(f[A][L]={...JSON.parse(JSON.stringify(f[A][L]||{})),...N}))}}}}),re(Op({materialID:s,data:f})),t()};return R(Pi,{isOpen:e,titleIcon:n(Gr,{size:"small",sx:{color:(K=(O=$e)==null?void 0:O.primary)==null?void 0:K.dark,fontSize:"20px"}}),Title:r,handleClose:()=>t(),children:[R(Lo,{sx:{mt:2},children:[n(mt,{sx:{mb:2},children:xa.COPY_ORG_DATA_VALUES}),n(ro,{options:ae(),placeholder:"SELECT SOURCE ORGANIZATION",onChange:m=>u(m),value:l})]}),n(Do,{children:n(Rt,{variant:"contained",size:"small",onClick:()=>y(),children:"Ok"})})]})},pf=()=>{const{fetchDataAndDispatch:e}=Bi();return{fetchTabSpecificData:(r,o)=>{if(o===P.SALES&&r&&r.includes("-")){const[c,s]=r.split("-");c&&e(`/${ve}${We.DATA.GET_DELIVARING_PLANT_BASED_ON_SALES_ORG_AND_DISTCHNL}`,"DelygPlnt","post",{salesOrg:c,distChnl:s},!0)}else if(o===P.PLANT&&r){const c=r;e(`/${ve}${We.DATA.GET_SPPROC_TYPE}`,"Spproctype","post",{plant:c},!0),e(`/${ve}${We.DATA.GET_MRP_CONTROLLER_BASED_ON_PLANT}`,"MrpCtrler","post",{plant:c},!0),e(`/${ve}${We.DATA.GET_PROD_STORAGE_LOCATION_BASED_ON_PLANT}`,"IssStLoc","post",{plant:c},!0),e(`/${ve}${We.DATA.GET_PROCUREMENT_STORAGE_LOCATION_BASED_ON_PLANT}`,"SlocExprc","post",{plant:c},!0),e(`/${ve}${We.DATA.GET_SCHEDULING_MARGIN_KEY_BASED_ON_PLANT}`,"SmKey","post",{plant:c},!0),e(`/${ve}${We.DATA.GET_PROFIT_CENTER_BASED_ON_PLANT}`,"ProfitCtr","post",{plant:c},!0),e(`/${ve}${We.DATA.GET_PRODUCTION_SCHEDULING_PROFILE_BASED_ON_PLANT}`,"ProdProf","post",{plant:c},!0)}else o===P.WAREHOUSE&&r&&e(`/${ve}${We.DATA.GET_PLACEMENT}?wareHouseNo=${r}`,"Placement","get",{plant:r},!0)}}},Tf=({doAjax:e,customError:t,fetchDataAndDispatch:r,destination_MaterialMgmt:o})=>({getContryBasedOnPlant:s=>{const p=u=>{var re;if((u==null?void 0:u.statusCode)===Vt.STATUS_200){const ae=(re=u==null?void 0:u.body[0])==null?void 0:re.code;ae&&(r(`/${o}${We.DATA.GET_COMMODITY_CODE_BASED_ON_COUNTRY}?country=${ae}`,"CommCode","get",{plant:s},!0),r(`/${o}${We.DATA.GET_HTS_CODE}?country=${ae}`,"HtsCode","get",{plant:s},!0))}},l=u=>{t(u)};e(`/${o}${We.DATA.GET_COUNTRY_BASED_ON_PLANT}`,"post",p,l,{plant:s})}});var $a={},NE=Go;Object.defineProperty($a,"__esModule",{value:!0});var Ta=$a.default=void 0,wE=NE(Bo()),RE=$o;Ta=$a.default=(0,wE.default)((0,RE.jsx)("path",{d:"M3 5v4h2V5h4V3H5c-1.1 0-2 .9-2 2m2 10H3v4c0 1.1.9 2 2 2h4v-2H5zm14 4h-4v2h4c1.1 0 2-.9 2-2v-4h-2zm0-16h-4v2h4v4h2V5c0-1.1-.9-2-2-2"}),"CropFree");var Fa={},_E=Go;Object.defineProperty(Fa,"__esModule",{value:!0});var Ea=Fa.default=void 0,IE=_E(Bo()),vE=$o;Ea=Fa.default=(0,IE.default)((0,vE.jsx)("path",{d:"M22 3.41 16.71 8.7 20 12h-8V4l3.29 3.29L20.59 2zM3.41 22l5.29-5.29L12 20v-8H4l3.29 3.29L2 20.59z"}),"CloseFullscreen");/*!
* sweetalert2 v11.22.2
* Released under the MIT License.
*/function Ef(e,t,r){if(typeof e=="function"?e===t:e.has(t))return arguments.length<3?t:r;throw new TypeError("Private element is not present on this object")}function ME(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function Eh(e,t){return e.get(Ef(e,t))}function OE(e,t,r){ME(e,t),t.set(e,r)}function xE(e,t,r){return e.set(Ef(e,t),r),r}const yE=100,_t={},LE=()=>{_t.previousActiveElement instanceof HTMLElement?(_t.previousActiveElement.focus(),_t.previousActiveElement=null):document.body&&document.body.focus()},DE=e=>new Promise(t=>{if(!e)return t();const r=window.scrollX,o=window.scrollY;_t.restoreFocusTimeout=setTimeout(()=>{LE(),t()},yE),window.scrollTo(r,o)}),mf="swal2-",PE=["container","shown","height-auto","iosfix","popup","modal","no-backdrop","no-transition","toast","toast-shown","show","hide","close","title","html-container","actions","confirm","deny","cancel","footer","icon","icon-content","image","input","file","range","select","radio","checkbox","label","textarea","inputerror","input-label","validation-message","progress-steps","active-progress-step","progress-step","progress-step-line","loader","loading","styled","top","top-start","top-end","top-left","top-right","center","center-start","center-end","center-left","center-right","bottom","bottom-start","bottom-end","bottom-left","bottom-right","grow-row","grow-column","grow-fullscreen","rtl","timer-progress-bar","timer-progress-bar-container","scrollbar-measure","icon-success","icon-warning","icon-info","icon-question","icon-error","draggable","dragging"],fe=PE.reduce((e,t)=>(e[t]=mf+t,e),{}),qE=["success","warning","info","question","error"],Si=qE.reduce((e,t)=>(e[t]=mf+t,e),{}),Cf="SweetAlert2:",Wa=e=>e.charAt(0).toUpperCase()+e.slice(1),sn=e=>{console.warn(`${Cf} ${typeof e=="object"?e.join(" "):e}`)},_r=e=>{console.error(`${Cf} ${e}`)},mh=[],UE=e=>{mh.includes(e)||(mh.push(e),sn(e))},Af=(e,t=null)=>{UE(`"${e}" is deprecated and will be removed in the next major release.${t?` Use "${t}" instead.`:""}`)},Fi=e=>typeof e=="function"?e():e,ja=e=>e&&typeof e.toPromise=="function",$l=e=>ja(e)?e.toPromise():Promise.resolve(e),za=e=>e&&Promise.resolve(e)===e,rn=()=>document.body.querySelector(`.${fe.container}`),Fl=e=>{const t=rn();return t?t.querySelector(e):null},gn=e=>Fl(`.${e}`),Is=()=>gn(fe.popup),jr=()=>gn(fe.icon),kE=()=>gn(fe["icon-content"]),bf=()=>gn(fe.title),Ya=()=>gn(fe["html-container"]),Sf=()=>gn(fe.image),Xa=()=>gn(fe["progress-steps"]),Wi=()=>gn(fe["validation-message"]),kn=()=>Fl(`.${fe.actions} .${fe.confirm}`),zr=()=>Fl(`.${fe.actions} .${fe.cancel}`),Ir=()=>Fl(`.${fe.actions} .${fe.deny}`),HE=()=>gn(fe["input-label"]),Yr=()=>Fl(`.${fe.loader}`),Wl=()=>gn(fe.actions),Nf=()=>gn(fe.footer),ji=()=>gn(fe["timer-progress-bar"]),Va=()=>gn(fe.close),BE=`
  a[href],
  area[href],
  input:not([disabled]),
  select:not([disabled]),
  textarea:not([disabled]),
  button:not([disabled]),
  iframe,
  object,
  embed,
  [tabindex="0"],
  [contenteditable],
  audio[controls],
  video[controls],
  summary
`,Ka=()=>{const e=Is();if(!e)return[];const t=e.querySelectorAll('[tabindex]:not([tabindex="-1"]):not([tabindex="0"])'),r=Array.from(t).sort((s,p)=>{const l=parseInt(s.getAttribute("tabindex")||"0"),u=parseInt(p.getAttribute("tabindex")||"0");return l>u?1:l<u?-1:0}),o=e.querySelectorAll(BE),c=Array.from(o).filter(s=>s.getAttribute("tabindex")!=="-1");return[...new Set(r.concat(c))].filter(s=>an(s))},Ja=()=>Yn(document.body,fe.shown)&&!Yn(document.body,fe["toast-shown"])&&!Yn(document.body,fe["no-backdrop"]),zi=()=>{const e=Is();return e?Yn(e,fe.toast):!1},GE=()=>{const e=Is();return e?e.hasAttribute("data-loading"):!1},pn=(e,t)=>{if(e.textContent="",t){const o=new DOMParser().parseFromString(t,"text/html"),c=o.querySelector("head");c&&Array.from(c.childNodes).forEach(p=>{e.appendChild(p)});const s=o.querySelector("body");s&&Array.from(s.childNodes).forEach(p=>{p instanceof HTMLVideoElement||p instanceof HTMLAudioElement?e.appendChild(p.cloneNode(!0)):e.appendChild(p)})}},Yn=(e,t)=>{if(!t)return!1;const r=t.split(/\s+/);for(let o=0;o<r.length;o++)if(!e.classList.contains(r[o]))return!1;return!0},$E=(e,t)=>{Array.from(e.classList).forEach(r=>{!Object.values(fe).includes(r)&&!Object.values(Si).includes(r)&&!Object.values(t.showClass||{}).includes(r)&&e.classList.remove(r)})},fn=(e,t,r)=>{if($E(e,t),!t.customClass)return;const o=t.customClass[r];if(o){if(typeof o!="string"&&!o.forEach){sn(`Invalid type of customClass.${r}! Expected string or iterable object, got "${typeof o}"`);return}ys(e,o)}},Yi=(e,t)=>{if(!t)return null;switch(t){case"select":case"textarea":case"file":return e.querySelector(`.${fe.popup} > .${fe[t]}`);case"checkbox":return e.querySelector(`.${fe.popup} > .${fe.checkbox} input`);case"radio":return e.querySelector(`.${fe.popup} > .${fe.radio} input:checked`)||e.querySelector(`.${fe.popup} > .${fe.radio} input:first-child`);case"range":return e.querySelector(`.${fe.popup} > .${fe.range} input`);default:return e.querySelector(`.${fe.popup} > .${fe.input}`)}},wf=e=>{if(e.focus(),e.type!=="file"){const t=e.value;e.value="",e.value=t}},Rf=(e,t,r)=>{!e||!t||(typeof t=="string"&&(t=t.split(/\s+/).filter(Boolean)),t.forEach(o=>{Array.isArray(e)?e.forEach(c=>{r?c.classList.add(o):c.classList.remove(o)}):r?e.classList.add(o):e.classList.remove(o)}))},ys=(e,t)=>{Rf(e,t,!0)},bn=(e,t)=>{Rf(e,t,!1)},rr=(e,t)=>{const r=Array.from(e.children);for(let o=0;o<r.length;o++){const c=r[o];if(c instanceof HTMLElement&&Yn(c,t))return c}},br=(e,t,r)=>{r===`${parseInt(r)}`&&(r=parseInt(r)),r||parseInt(r)===0?e.style.setProperty(t,typeof r=="number"?`${r}px`:r):e.style.removeProperty(t)},wo=(e,t="flex")=>{e&&(e.style.display=t)},Ho=e=>{e&&(e.style.display="none")},Qa=(e,t="block")=>{e&&new MutationObserver(()=>{jl(e,e.innerHTML,t)}).observe(e,{childList:!0,subtree:!0})},Ch=(e,t,r,o)=>{const c=e.querySelector(t);c&&c.style.setProperty(r,o)},jl=(e,t,r="flex")=>{t?wo(e,r):Ho(e)},an=e=>!!(e&&(e.offsetWidth||e.offsetHeight||e.getClientRects().length)),FE=()=>!an(kn())&&!an(Ir())&&!an(zr()),ma=e=>e.scrollHeight>e.clientHeight,WE=(e,t)=>{let r=e;for(;r&&r!==t;){if(ma(r))return!0;r=r.parentElement}return!1},_f=e=>{const t=window.getComputedStyle(e),r=parseFloat(t.getPropertyValue("animation-duration")||"0"),o=parseFloat(t.getPropertyValue("transition-duration")||"0");return r>0||o>0},Za=(e,t=!1)=>{const r=ji();r&&an(r)&&(t&&(r.style.transition="none",r.style.width="100%"),setTimeout(()=>{r.style.transition=`width ${e/1e3}s linear`,r.style.width="0%"},10))},jE=()=>{const e=ji();if(!e)return;const t=parseInt(window.getComputedStyle(e).width);e.style.removeProperty("transition"),e.style.width="100%";const r=parseInt(window.getComputedStyle(e).width),o=t/r*100;e.style.width=`${o}%`},zE=()=>typeof window>"u"||typeof document>"u",YE=`
 <div aria-labelledby="${fe.title}" aria-describedby="${fe["html-container"]}" class="${fe.popup}" tabindex="-1">
   <button type="button" class="${fe.close}"></button>
   <ul class="${fe["progress-steps"]}"></ul>
   <div class="${fe.icon}"></div>
   <img class="${fe.image}" />
   <h2 class="${fe.title}" id="${fe.title}"></h2>
   <div class="${fe["html-container"]}" id="${fe["html-container"]}"></div>
   <input class="${fe.input}" id="${fe.input}" />
   <input type="file" class="${fe.file}" />
   <div class="${fe.range}">
     <input type="range" />
     <output></output>
   </div>
   <select class="${fe.select}" id="${fe.select}"></select>
   <div class="${fe.radio}"></div>
   <label class="${fe.checkbox}">
     <input type="checkbox" id="${fe.checkbox}" />
     <span class="${fe.label}"></span>
   </label>
   <textarea class="${fe.textarea}" id="${fe.textarea}"></textarea>
   <div class="${fe["validation-message"]}" id="${fe["validation-message"]}"></div>
   <div class="${fe.actions}">
     <div class="${fe.loader}"></div>
     <button type="button" class="${fe.confirm}"></button>
     <button type="button" class="${fe.deny}"></button>
     <button type="button" class="${fe.cancel}"></button>
   </div>
   <div class="${fe.footer}"></div>
   <div class="${fe["timer-progress-bar-container"]}">
     <div class="${fe["timer-progress-bar"]}"></div>
   </div>
 </div>
`.replace(/(^|\n)\s*/g,""),XE=()=>{const e=rn();return e?(e.remove(),bn([document.documentElement,document.body],[fe["no-backdrop"],fe["toast-shown"],fe["has-column"]]),!0):!1},ur=()=>{_t.currentInstance.resetValidationMessage()},VE=()=>{const e=Is(),t=rr(e,fe.input),r=rr(e,fe.file),o=e.querySelector(`.${fe.range} input`),c=e.querySelector(`.${fe.range} output`),s=rr(e,fe.select),p=e.querySelector(`.${fe.checkbox} input`),l=rr(e,fe.textarea);t.oninput=ur,r.onchange=ur,s.onchange=ur,p.onchange=ur,l.oninput=ur,o.oninput=()=>{ur(),c.value=o.value},o.onchange=()=>{ur(),c.value=o.value}},KE=e=>typeof e=="string"?document.querySelector(e):e,JE=e=>{const t=Is();t.setAttribute("role",e.toast?"alert":"dialog"),t.setAttribute("aria-live",e.toast?"polite":"assertive"),e.toast||t.setAttribute("aria-modal","true")},QE=e=>{window.getComputedStyle(e).direction==="rtl"&&ys(rn(),fe.rtl)},ZE=e=>{const t=XE();if(zE()){_r("SweetAlert2 requires document to initialize");return}const r=document.createElement("div");r.className=fe.container,t&&ys(r,fe["no-transition"]),pn(r,YE),r.dataset.swal2Theme=e.theme;const o=KE(e.target);o.appendChild(r),e.topLayer&&(r.setAttribute("popover",""),r.showPopover()),JE(e),QE(o),VE()},ec=(e,t)=>{e instanceof HTMLElement?t.appendChild(e):typeof e=="object"?em(e,t):e&&pn(t,e)},em=(e,t)=>{e.jquery?tm(t,e):pn(t,e.toString())},tm=(e,t)=>{if(e.textContent="",0 in t)for(let r=0;r in t;r++)e.appendChild(t[r].cloneNode(!0));else e.appendChild(t.cloneNode(!0))},sm=(e,t)=>{const r=Wl(),o=Yr();!r||!o||(!t.showConfirmButton&&!t.showDenyButton&&!t.showCancelButton?Ho(r):wo(r),fn(r,t,"actions"),om(r,o,t),pn(o,t.loaderHtml||""),fn(o,t,"loader"))};function om(e,t,r){const o=kn(),c=Ir(),s=zr();!o||!c||!s||(la(o,"confirm",r),la(c,"deny",r),la(s,"cancel",r),nm(o,c,s,r),r.reverseButtons&&(r.toast?(e.insertBefore(s,o),e.insertBefore(c,o)):(e.insertBefore(s,t),e.insertBefore(c,t),e.insertBefore(o,t))))}function nm(e,t,r,o){if(!o.buttonsStyling){bn([e,t,r],fe.styled);return}ys([e,t,r],fe.styled),o.confirmButtonColor&&e.style.setProperty("--swal2-confirm-button-background-color",o.confirmButtonColor),o.denyButtonColor&&t.style.setProperty("--swal2-deny-button-background-color",o.denyButtonColor),o.cancelButtonColor&&r.style.setProperty("--swal2-cancel-button-background-color",o.cancelButtonColor),ra(e),ra(t),ra(r)}function ra(e){const t=window.getComputedStyle(e);if(t.getPropertyValue("--swal2-action-button-focus-box-shadow"))return;const r=t.backgroundColor.replace(/rgba?\((\d+), (\d+), (\d+).*/,"rgba($1, $2, $3, 0.5)");e.style.setProperty("--swal2-action-button-focus-box-shadow",t.getPropertyValue("--swal2-outline").replace(/ rgba\(.*/,` ${r}`))}function la(e,t,r){const o=Wa(t);jl(e,r[`show${o}Button`],"inline-block"),pn(e,r[`${t}ButtonText`]||""),e.setAttribute("aria-label",r[`${t}ButtonAriaLabel`]||""),e.className=fe[t],fn(e,r,`${t}Button`)}const rm=(e,t)=>{const r=Va();r&&(pn(r,t.closeButtonHtml||""),fn(r,t,"closeButton"),jl(r,t.showCloseButton),r.setAttribute("aria-label",t.closeButtonAriaLabel||""))},lm=(e,t)=>{const r=rn();r&&(im(r,t.backdrop),am(r,t.position),cm(r,t.grow),fn(r,t,"container"))};function im(e,t){typeof t=="string"?e.style.background=t:t||ys([document.documentElement,document.body],fe["no-backdrop"])}function am(e,t){t&&(t in fe?ys(e,fe[t]):(sn('The "position" parameter is not valid, defaulting to "center"'),ys(e,fe.center)))}function cm(e,t){t&&ys(e,fe[`grow-${t}`])}var Vs={innerParams:new WeakMap,domCache:new WeakMap};const dm=["input","file","range","select","radio","checkbox","textarea"],um=(e,t)=>{const r=Is();if(!r)return;const o=Vs.innerParams.get(e),c=!o||t.input!==o.input;dm.forEach(s=>{const p=rr(r,fe[s]);p&&(gm(s,t.inputAttributes),p.className=fe[s],c&&Ho(p))}),t.input&&(c&&hm(t),pm(t))},hm=e=>{if(!e.input)return;if(!go[e.input]){_r(`Unexpected type of input! Expected ${Object.keys(go).join(" | ")}, got "${e.input}"`);return}const t=If(e.input);if(!t)return;const r=go[e.input](t,e);wo(t),e.inputAutoFocus&&setTimeout(()=>{wf(r)})},fm=e=>{for(let t=0;t<e.attributes.length;t++){const r=e.attributes[t].name;["id","type","value","style"].includes(r)||e.removeAttribute(r)}},gm=(e,t)=>{const r=Is();if(!r)return;const o=Yi(r,e);if(o){fm(o);for(const c in t)o.setAttribute(c,t[c])}},pm=e=>{if(!e.input)return;const t=If(e.input);t&&fn(t,e,"input")},tc=(e,t)=>{!e.placeholder&&t.inputPlaceholder&&(e.placeholder=t.inputPlaceholder)},zl=(e,t,r)=>{if(r.inputLabel){const o=document.createElement("label"),c=fe["input-label"];o.setAttribute("for",e.id),o.className=c,typeof r.customClass=="object"&&ys(o,r.customClass.inputLabel),o.innerText=r.inputLabel,t.insertAdjacentElement("beforebegin",o)}},If=e=>{const t=Is();if(t)return rr(t,fe[e]||fe.input)},Ni=(e,t)=>{["string","number"].includes(typeof t)?e.value=`${t}`:za(t)||sn(`Unexpected type of inputValue! Expected "string", "number" or "Promise", got "${typeof t}"`)},go={};go.text=go.email=go.password=go.number=go.tel=go.url=go.search=go.date=go["datetime-local"]=go.time=go.week=go.month=(e,t)=>(Ni(e,t.inputValue),zl(e,e,t),tc(e,t),e.type=t.input,e);go.file=(e,t)=>(zl(e,e,t),tc(e,t),e);go.range=(e,t)=>{const r=e.querySelector("input"),o=e.querySelector("output");return Ni(r,t.inputValue),r.type=t.input,Ni(o,t.inputValue),zl(r,e,t),e};go.select=(e,t)=>{if(e.textContent="",t.inputPlaceholder){const r=document.createElement("option");pn(r,t.inputPlaceholder),r.value="",r.disabled=!0,r.selected=!0,e.appendChild(r)}return zl(e,e,t),e};go.radio=e=>(e.textContent="",e);go.checkbox=(e,t)=>{const r=Yi(Is(),"checkbox");r.value="1",r.checked=!!t.inputValue;const o=e.querySelector("span");return pn(o,t.inputPlaceholder||t.inputLabel),r};go.textarea=(e,t)=>{Ni(e,t.inputValue),tc(e,t),zl(e,e,t);const r=o=>parseInt(window.getComputedStyle(o).marginLeft)+parseInt(window.getComputedStyle(o).marginRight);return setTimeout(()=>{if("MutationObserver"in window){const o=parseInt(window.getComputedStyle(Is()).width),c=()=>{if(!document.body.contains(e))return;const s=e.offsetWidth+r(e);s>o?Is().style.width=`${s}px`:br(Is(),"width",t.width)};new MutationObserver(c).observe(e,{attributes:!0,attributeFilter:["style"]})}}),e};const Tm=(e,t)=>{const r=Ya();r&&(Qa(r),fn(r,t,"htmlContainer"),t.html?(ec(t.html,r),wo(r,"block")):t.text?(r.textContent=t.text,wo(r,"block")):Ho(r),um(e,t))},Em=(e,t)=>{const r=Nf();r&&(Qa(r),jl(r,t.footer,"block"),t.footer&&ec(t.footer,r),fn(r,t,"footer"))},mm=(e,t)=>{const r=Vs.innerParams.get(e),o=jr();if(!o)return;if(r&&t.icon===r.icon){bh(o,t),Ah(o,t);return}if(!t.icon&&!t.iconHtml){Ho(o);return}if(t.icon&&Object.keys(Si).indexOf(t.icon)===-1){_r(`Unknown icon! Expected "success", "error", "warning", "info" or "question", got "${t.icon}"`),Ho(o);return}wo(o),bh(o,t),Ah(o,t),ys(o,t.showClass&&t.showClass.icon),window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",vf)},Ah=(e,t)=>{for(const[r,o]of Object.entries(Si))t.icon!==r&&bn(e,o);ys(e,t.icon&&Si[t.icon]),bm(e,t),vf(),fn(e,t,"icon")},vf=()=>{const e=Is();if(!e)return;const t=window.getComputedStyle(e).getPropertyValue("background-color"),r=e.querySelectorAll("[class^=swal2-success-circular-line], .swal2-success-fix");for(let o=0;o<r.length;o++)r[o].style.backgroundColor=t},Cm=e=>`
  ${e.animation?'<div class="swal2-success-circular-line-left"></div>':""}
  <span class="swal2-success-line-tip"></span> <span class="swal2-success-line-long"></span>
  <div class="swal2-success-ring"></div>
  ${e.animation?'<div class="swal2-success-fix"></div>':""}
  ${e.animation?'<div class="swal2-success-circular-line-right"></div>':""}
`,Am=`
  <span class="swal2-x-mark">
    <span class="swal2-x-mark-line-left"></span>
    <span class="swal2-x-mark-line-right"></span>
  </span>
`,bh=(e,t)=>{if(!t.icon&&!t.iconHtml)return;let r=e.innerHTML,o="";t.iconHtml?o=Sh(t.iconHtml):t.icon==="success"?(o=Cm(t),r=r.replace(/ style=".*?"/g,"")):t.icon==="error"?o=Am:t.icon&&(o=Sh({question:"?",warning:"!",info:"i"}[t.icon])),r.trim()!==o.trim()&&pn(e,o)},bm=(e,t)=>{if(t.iconColor){e.style.color=t.iconColor,e.style.borderColor=t.iconColor;for(const r of[".swal2-success-line-tip",".swal2-success-line-long",".swal2-x-mark-line-left",".swal2-x-mark-line-right"])Ch(e,r,"background-color",t.iconColor);Ch(e,".swal2-success-ring","border-color",t.iconColor)}},Sh=e=>`<div class="${fe["icon-content"]}">${e}</div>`,Sm=(e,t)=>{const r=Sf();if(r){if(!t.imageUrl){Ho(r);return}wo(r,""),r.setAttribute("src",t.imageUrl),r.setAttribute("alt",t.imageAlt||""),br(r,"width",t.imageWidth),br(r,"height",t.imageHeight),r.className=fe.image,fn(r,t,"image")}};let sc=!1,Mf=0,Of=0,xf=0,yf=0;const Nm=e=>{e.addEventListener("mousedown",wi),document.body.addEventListener("mousemove",Ri),e.addEventListener("mouseup",_i),e.addEventListener("touchstart",wi),document.body.addEventListener("touchmove",Ri),e.addEventListener("touchend",_i)},wm=e=>{e.removeEventListener("mousedown",wi),document.body.removeEventListener("mousemove",Ri),e.removeEventListener("mouseup",_i),e.removeEventListener("touchstart",wi),document.body.removeEventListener("touchmove",Ri),e.removeEventListener("touchend",_i)},wi=e=>{const t=Is();if(e.target===t||jr().contains(e.target)){sc=!0;const r=Lf(e);Mf=r.clientX,Of=r.clientY,xf=parseInt(t.style.insetInlineStart)||0,yf=parseInt(t.style.insetBlockStart)||0,ys(t,"swal2-dragging")}},Ri=e=>{const t=Is();if(sc){let{clientX:r,clientY:o}=Lf(e);t.style.insetInlineStart=`${xf+(r-Mf)}px`,t.style.insetBlockStart=`${yf+(o-Of)}px`}},_i=()=>{const e=Is();sc=!1,bn(e,"swal2-dragging")},Lf=e=>{let t=0,r=0;return e.type.startsWith("mouse")?(t=e.clientX,r=e.clientY):e.type.startsWith("touch")&&(t=e.touches[0].clientX,r=e.touches[0].clientY),{clientX:t,clientY:r}},Rm=(e,t)=>{const r=rn(),o=Is();if(!(!r||!o)){if(t.toast){br(r,"width",t.width),o.style.width="100%";const c=Yr();c&&o.insertBefore(c,jr())}else br(o,"width",t.width);br(o,"padding",t.padding),t.color&&(o.style.color=t.color),t.background&&(o.style.background=t.background),Ho(Wi()),_m(o,t),t.draggable&&!t.toast?(ys(o,fe.draggable),Nm(o)):(bn(o,fe.draggable),wm(o))}},_m=(e,t)=>{const r=t.showClass||{};e.className=`${fe.popup} ${an(e)?r.popup:""}`,t.toast?(ys([document.documentElement,document.body],fe["toast-shown"]),ys(e,fe.toast)):ys(e,fe.modal),fn(e,t,"popup"),typeof t.customClass=="string"&&ys(e,t.customClass),t.icon&&ys(e,fe[`icon-${t.icon}`])},Im=(e,t)=>{const r=Xa();if(!r)return;const{progressSteps:o,currentProgressStep:c}=t;if(!o||o.length===0||c===void 0){Ho(r);return}wo(r),r.textContent="",c>=o.length&&sn("Invalid currentProgressStep parameter, it should be less than progressSteps.length (currentProgressStep like JS arrays starts from 0)"),o.forEach((s,p)=>{const l=vm(s);if(r.appendChild(l),p===c&&ys(l,fe["active-progress-step"]),p!==o.length-1){const u=Mm(t);r.appendChild(u)}})},vm=e=>{const t=document.createElement("li");return ys(t,fe["progress-step"]),pn(t,e),t},Mm=e=>{const t=document.createElement("li");return ys(t,fe["progress-step-line"]),e.progressStepsDistance&&br(t,"width",e.progressStepsDistance),t},Om=(e,t)=>{const r=bf();r&&(Qa(r),jl(r,t.title||t.titleText,"block"),t.title&&ec(t.title,r),t.titleText&&(r.innerText=t.titleText),fn(r,t,"title"))},Df=(e,t)=>{Rm(e,t),lm(e,t),Im(e,t),mm(e,t),Sm(e,t),Om(e,t),rm(e,t),Tm(e,t),sm(e,t),Em(e,t);const r=Is();typeof t.didRender=="function"&&r&&t.didRender(r),_t.eventEmitter.emit("didRender",r)},xm=()=>an(Is()),Pf=()=>{var e;return(e=kn())===null||e===void 0?void 0:e.click()},ym=()=>{var e;return(e=Ir())===null||e===void 0?void 0:e.click()},Lm=()=>{var e;return(e=zr())===null||e===void 0?void 0:e.click()},Xr=Object.freeze({cancel:"cancel",backdrop:"backdrop",close:"close",esc:"esc",timer:"timer"}),qf=e=>{e.keydownTarget&&e.keydownHandlerAdded&&(e.keydownTarget.removeEventListener("keydown",e.keydownHandler,{capture:e.keydownListenerCapture}),e.keydownHandlerAdded=!1)},Dm=(e,t,r)=>{qf(e),t.toast||(e.keydownHandler=o=>qm(t,o,r),e.keydownTarget=t.keydownListenerCapture?window:Is(),e.keydownListenerCapture=t.keydownListenerCapture,e.keydownTarget.addEventListener("keydown",e.keydownHandler,{capture:e.keydownListenerCapture}),e.keydownHandlerAdded=!0)},Ca=(e,t)=>{var r;const o=Ka();if(o.length){e=e+t,e===-2&&(e=o.length-1),e===o.length?e=0:e===-1&&(e=o.length-1),o[e].focus();return}(r=Is())===null||r===void 0||r.focus()},Uf=["ArrowRight","ArrowDown"],Pm=["ArrowLeft","ArrowUp"],qm=(e,t,r)=>{e&&(t.isComposing||t.keyCode===229||(e.stopKeydownPropagation&&t.stopPropagation(),t.key==="Enter"?Um(t,e):t.key==="Tab"?km(t):[...Uf,...Pm].includes(t.key)?Hm(t.key):t.key==="Escape"&&Bm(t,e,r)))},Um=(e,t)=>{if(!Fi(t.allowEnterKey))return;const r=Yi(Is(),t.input);if(e.target&&r&&e.target instanceof HTMLElement&&e.target.outerHTML===r.outerHTML){if(["textarea","file"].includes(t.input))return;Pf(),e.preventDefault()}},km=e=>{const t=e.target,r=Ka();let o=-1;for(let c=0;c<r.length;c++)if(t===r[c]){o=c;break}e.shiftKey?Ca(o,-1):Ca(o,1),e.stopPropagation(),e.preventDefault()},Hm=e=>{const t=Wl(),r=kn(),o=Ir(),c=zr();if(!t||!r||!o||!c)return;const s=[r,o,c];if(document.activeElement instanceof HTMLElement&&!s.includes(document.activeElement))return;const p=Uf.includes(e)?"nextElementSibling":"previousElementSibling";let l=document.activeElement;if(l){for(let u=0;u<t.children.length;u++){if(l=l[p],!l)return;if(l instanceof HTMLButtonElement&&an(l))break}l instanceof HTMLButtonElement&&l.focus()}},Bm=(e,t,r)=>{e.preventDefault(),Fi(t.allowEscapeKey)&&r(Xr.esc)};var $r={swalPromiseResolve:new WeakMap,swalPromiseReject:new WeakMap};const Gm=()=>{const e=rn();Array.from(document.body.children).forEach(r=>{r.contains(e)||(r.hasAttribute("aria-hidden")&&r.setAttribute("data-previous-aria-hidden",r.getAttribute("aria-hidden")||""),r.setAttribute("aria-hidden","true"))})},kf=()=>{Array.from(document.body.children).forEach(t=>{t.hasAttribute("data-previous-aria-hidden")?(t.setAttribute("aria-hidden",t.getAttribute("data-previous-aria-hidden")||""),t.removeAttribute("data-previous-aria-hidden")):t.removeAttribute("aria-hidden")})},Hf=typeof window<"u"&&!!window.GestureEvent,$m=()=>{if(Hf&&!Yn(document.body,fe.iosfix)){const e=document.body.scrollTop;document.body.style.top=`${e*-1}px`,ys(document.body,fe.iosfix),Fm()}},Fm=()=>{const e=rn();if(!e)return;let t;e.ontouchstart=r=>{t=Wm(r)},e.ontouchmove=r=>{t&&(r.preventDefault(),r.stopPropagation())}},Wm=e=>{const t=e.target,r=rn(),o=Ya();return!r||!o||jm(e)||zm(e)?!1:t===r||!ma(r)&&t instanceof HTMLElement&&!WE(t,o)&&t.tagName!=="INPUT"&&t.tagName!=="TEXTAREA"&&!(ma(o)&&o.contains(t))},jm=e=>e.touches&&e.touches.length&&e.touches[0].touchType==="stylus",zm=e=>e.touches&&e.touches.length>1,Ym=()=>{if(Yn(document.body,fe.iosfix)){const e=parseInt(document.body.style.top,10);bn(document.body,fe.iosfix),document.body.style.top="",document.body.scrollTop=e*-1}},Xm=()=>{const e=document.createElement("div");e.className=fe["scrollbar-measure"],document.body.appendChild(e);const t=e.getBoundingClientRect().width-e.clientWidth;return document.body.removeChild(e),t};let Dr=null;const Vm=e=>{Dr===null&&(document.body.scrollHeight>window.innerHeight||e==="scroll")&&(Dr=parseInt(window.getComputedStyle(document.body).getPropertyValue("padding-right")),document.body.style.paddingRight=`${Dr+Xm()}px`)},Km=()=>{Dr!==null&&(document.body.style.paddingRight=`${Dr}px`,Dr=null)};function Bf(e,t,r,o){zi()?Nh(e,o):(DE(r).then(()=>Nh(e,o)),qf(_t)),Hf?(t.setAttribute("style","display:none !important"),t.removeAttribute("class"),t.innerHTML=""):t.remove(),Ja()&&(Km(),Ym(),kf()),Jm()}function Jm(){bn([document.documentElement,document.body],[fe.shown,fe["height-auto"],fe["no-backdrop"],fe["toast-shown"]])}function lr(e){e=Zm(e);const t=$r.swalPromiseResolve.get(this),r=Qm(this);this.isAwaitingPromise?e.isDismissed||(Yl(this),t(e)):r&&t(e)}const Qm=e=>{const t=Is();if(!t)return!1;const r=Vs.innerParams.get(e);if(!r||Yn(t,r.hideClass.popup))return!1;bn(t,r.showClass.popup),ys(t,r.hideClass.popup);const o=rn();return bn(o,r.showClass.backdrop),ys(o,r.hideClass.backdrop),eC(e,t,r),!0};function Gf(e){const t=$r.swalPromiseReject.get(this);Yl(this),t&&t(e)}const Yl=e=>{e.isAwaitingPromise&&(delete e.isAwaitingPromise,Vs.innerParams.get(e)||e._destroy())},Zm=e=>typeof e>"u"?{isConfirmed:!1,isDenied:!1,isDismissed:!0}:Object.assign({isConfirmed:!1,isDenied:!1,isDismissed:!1},e),eC=(e,t,r)=>{var o;const c=rn(),s=_f(t);typeof r.willClose=="function"&&r.willClose(t),(o=_t.eventEmitter)===null||o===void 0||o.emit("willClose",t),s?tC(e,t,c,r.returnFocus,r.didClose):Bf(e,c,r.returnFocus,r.didClose)},tC=(e,t,r,o,c)=>{_t.swalCloseEventFinishedCallback=Bf.bind(null,e,r,o,c);const s=function(p){if(p.target===t){var l;(l=_t.swalCloseEventFinishedCallback)===null||l===void 0||l.call(_t),delete _t.swalCloseEventFinishedCallback,t.removeEventListener("animationend",s),t.removeEventListener("transitionend",s)}};t.addEventListener("animationend",s),t.addEventListener("transitionend",s)},Nh=(e,t)=>{setTimeout(()=>{var r;typeof t=="function"&&t.bind(e.params)(),(r=_t.eventEmitter)===null||r===void 0||r.emit("didClose"),e._destroy&&e._destroy()})},Fr=e=>{let t=Is();if(t||new xi,t=Is(),!t)return;const r=Yr();zi()?Ho(jr()):sC(t,e),wo(r),t.setAttribute("data-loading","true"),t.setAttribute("aria-busy","true"),t.focus()},sC=(e,t)=>{const r=Wl(),o=Yr();!r||!o||(!t&&an(kn())&&(t=kn()),wo(r),t&&(Ho(t),o.setAttribute("data-button-to-replace",t.className),r.insertBefore(o,t)),ys([e,r],fe.loading))},oC=(e,t)=>{t.input==="select"||t.input==="radio"?aC(e,t):["text","email","number","tel","textarea"].some(r=>r===t.input)&&(ja(t.inputValue)||za(t.inputValue))&&(Fr(kn()),cC(e,t))},nC=(e,t)=>{const r=e.getInput();if(!r)return null;switch(t.input){case"checkbox":return rC(r);case"radio":return lC(r);case"file":return iC(r);default:return t.inputAutoTrim?r.value.trim():r.value}},rC=e=>e.checked?1:0,lC=e=>e.checked?e.value:null,iC=e=>e.files&&e.files.length?e.getAttribute("multiple")!==null?e.files:e.files[0]:null,aC=(e,t)=>{const r=Is();if(!r)return;const o=c=>{t.input==="select"?dC(r,Ii(c),t):t.input==="radio"&&uC(r,Ii(c),t)};ja(t.inputOptions)||za(t.inputOptions)?(Fr(kn()),$l(t.inputOptions).then(c=>{e.hideLoading(),o(c)})):typeof t.inputOptions=="object"?o(t.inputOptions):_r(`Unexpected type of inputOptions! Expected object, Map or Promise, got ${typeof t.inputOptions}`)},cC=(e,t)=>{const r=e.getInput();r&&(Ho(r),$l(t.inputValue).then(o=>{r.value=t.input==="number"?`${parseFloat(o)||0}`:`${o}`,wo(r),r.focus(),e.hideLoading()}).catch(o=>{_r(`Error in inputValue promise: ${o}`),r.value="",wo(r),r.focus(),e.hideLoading()}))};function dC(e,t,r){const o=rr(e,fe.select);if(!o)return;const c=(s,p,l)=>{const u=document.createElement("option");u.value=l,pn(u,p),u.selected=$f(l,r.inputValue),s.appendChild(u)};t.forEach(s=>{const p=s[0],l=s[1];if(Array.isArray(l)){const u=document.createElement("optgroup");u.label=p,u.disabled=!1,o.appendChild(u),l.forEach(re=>c(u,re[1],re[0]))}else c(o,l,p)}),o.focus()}function uC(e,t,r){const o=rr(e,fe.radio);if(!o)return;t.forEach(s=>{const p=s[0],l=s[1],u=document.createElement("input"),re=document.createElement("label");u.type="radio",u.name=fe.radio,u.value=p,$f(p,r.inputValue)&&(u.checked=!0);const ae=document.createElement("span");pn(ae,l),ae.className=fe.label,re.appendChild(u),re.appendChild(ae),o.appendChild(re)});const c=o.querySelectorAll("input");c.length&&c[0].focus()}const Ii=e=>{const t=[];return e instanceof Map?e.forEach((r,o)=>{let c=r;typeof c=="object"&&(c=Ii(c)),t.push([o,c])}):Object.keys(e).forEach(r=>{let o=e[r];typeof o=="object"&&(o=Ii(o)),t.push([r,o])}),t},$f=(e,t)=>!!t&&t.toString()===e.toString(),hC=e=>{const t=Vs.innerParams.get(e);e.disableButtons(),t.input?Ff(e,"confirm"):nc(e,!0)},fC=e=>{const t=Vs.innerParams.get(e);e.disableButtons(),t.returnInputValueOnDeny?Ff(e,"deny"):oc(e,!1)},gC=(e,t)=>{e.disableButtons(),t(Xr.cancel)},Ff=(e,t)=>{const r=Vs.innerParams.get(e);if(!r.input){_r(`The "input" parameter is needed to be set when using returnInputValueOn${Wa(t)}`);return}const o=e.getInput(),c=nC(e,r);r.inputValidator?pC(e,c,t):o&&!o.checkValidity()?(e.enableButtons(),e.showValidationMessage(r.validationMessage||o.validationMessage)):t==="deny"?oc(e,c):nc(e,c)},pC=(e,t,r)=>{const o=Vs.innerParams.get(e);e.disableInput(),Promise.resolve().then(()=>$l(o.inputValidator(t,o.validationMessage))).then(s=>{e.enableButtons(),e.enableInput(),s?e.showValidationMessage(s):r==="deny"?oc(e,t):nc(e,t)})},oc=(e,t)=>{const r=Vs.innerParams.get(e||void 0);r.showLoaderOnDeny&&Fr(Ir()),r.preDeny?(e.isAwaitingPromise=!0,Promise.resolve().then(()=>$l(r.preDeny(t,r.validationMessage))).then(c=>{c===!1?(e.hideLoading(),Yl(e)):e.close({isDenied:!0,value:typeof c>"u"?t:c})}).catch(c=>Wf(e||void 0,c))):e.close({isDenied:!0,value:t})},wh=(e,t)=>{e.close({isConfirmed:!0,value:t})},Wf=(e,t)=>{e.rejectPromise(t)},nc=(e,t)=>{const r=Vs.innerParams.get(e||void 0);r.showLoaderOnConfirm&&Fr(),r.preConfirm?(e.resetValidationMessage(),e.isAwaitingPromise=!0,Promise.resolve().then(()=>$l(r.preConfirm(t,r.validationMessage))).then(c=>{an(Wi())||c===!1?(e.hideLoading(),Yl(e)):wh(e,typeof c>"u"?t:c)}).catch(c=>Wf(e||void 0,c))):wh(e,t)};function vi(){const e=Vs.innerParams.get(this);if(!e)return;const t=Vs.domCache.get(this);Ho(t.loader),zi()?e.icon&&wo(jr()):TC(t),bn([t.popup,t.actions],fe.loading),t.popup.removeAttribute("aria-busy"),t.popup.removeAttribute("data-loading"),t.confirmButton.disabled=!1,t.denyButton.disabled=!1,t.cancelButton.disabled=!1}const TC=e=>{const t=e.popup.getElementsByClassName(e.loader.getAttribute("data-button-to-replace"));t.length?wo(t[0],"inline-block"):FE()&&Ho(e.actions)};function jf(){const e=Vs.innerParams.get(this),t=Vs.domCache.get(this);return t?Yi(t.popup,e.input):null}function zf(e,t,r){const o=Vs.domCache.get(e);t.forEach(c=>{o[c].disabled=r})}function Yf(e,t){const r=Is();if(!(!r||!e))if(e.type==="radio"){const o=r.querySelectorAll(`[name="${fe.radio}"]`);for(let c=0;c<o.length;c++)o[c].disabled=t}else e.disabled=t}function Xf(){zf(this,["confirmButton","denyButton","cancelButton"],!1)}function Vf(){zf(this,["confirmButton","denyButton","cancelButton"],!0)}function Kf(){Yf(this.getInput(),!1)}function Jf(){Yf(this.getInput(),!0)}function Qf(e){const t=Vs.domCache.get(this),r=Vs.innerParams.get(this);pn(t.validationMessage,e),t.validationMessage.className=fe["validation-message"],r.customClass&&r.customClass.validationMessage&&ys(t.validationMessage,r.customClass.validationMessage),wo(t.validationMessage);const o=this.getInput();o&&(o.setAttribute("aria-invalid","true"),o.setAttribute("aria-describedby",fe["validation-message"]),wf(o),ys(o,fe.inputerror))}function Zf(){const e=Vs.domCache.get(this);e.validationMessage&&Ho(e.validationMessage);const t=this.getInput();t&&(t.removeAttribute("aria-invalid"),t.removeAttribute("aria-describedby"),bn(t,fe.inputerror))}const Pr={title:"",titleText:"",text:"",html:"",footer:"",icon:void 0,iconColor:void 0,iconHtml:void 0,template:void 0,toast:!1,draggable:!1,animation:!0,theme:"light",showClass:{popup:"swal2-show",backdrop:"swal2-backdrop-show",icon:"swal2-icon-show"},hideClass:{popup:"swal2-hide",backdrop:"swal2-backdrop-hide",icon:"swal2-icon-hide"},customClass:{},target:"body",color:void 0,backdrop:!0,heightAuto:!0,allowOutsideClick:!0,allowEscapeKey:!0,allowEnterKey:!0,stopKeydownPropagation:!0,keydownListenerCapture:!1,showConfirmButton:!0,showDenyButton:!1,showCancelButton:!1,preConfirm:void 0,preDeny:void 0,confirmButtonText:"OK",confirmButtonAriaLabel:"",confirmButtonColor:void 0,denyButtonText:"No",denyButtonAriaLabel:"",denyButtonColor:void 0,cancelButtonText:"Cancel",cancelButtonAriaLabel:"",cancelButtonColor:void 0,buttonsStyling:!0,reverseButtons:!1,focusConfirm:!0,focusDeny:!1,focusCancel:!1,returnFocus:!0,showCloseButton:!1,closeButtonHtml:"&times;",closeButtonAriaLabel:"Close this dialog",loaderHtml:"",showLoaderOnConfirm:!1,showLoaderOnDeny:!1,imageUrl:void 0,imageWidth:void 0,imageHeight:void 0,imageAlt:"",timer:void 0,timerProgressBar:!1,width:void 0,padding:void 0,background:void 0,input:void 0,inputPlaceholder:"",inputLabel:"",inputValue:"",inputOptions:{},inputAutoFocus:!0,inputAutoTrim:!0,inputAttributes:{},inputValidator:void 0,returnInputValueOnDeny:!1,validationMessage:void 0,grow:!1,position:"center",progressSteps:[],currentProgressStep:void 0,progressStepsDistance:void 0,willOpen:void 0,didOpen:void 0,didRender:void 0,willClose:void 0,didClose:void 0,didDestroy:void 0,scrollbarPadding:!0,topLayer:!1},EC=["allowEscapeKey","allowOutsideClick","background","buttonsStyling","cancelButtonAriaLabel","cancelButtonColor","cancelButtonText","closeButtonAriaLabel","closeButtonHtml","color","confirmButtonAriaLabel","confirmButtonColor","confirmButtonText","currentProgressStep","customClass","denyButtonAriaLabel","denyButtonColor","denyButtonText","didClose","didDestroy","draggable","footer","hideClass","html","icon","iconColor","iconHtml","imageAlt","imageHeight","imageUrl","imageWidth","preConfirm","preDeny","progressSteps","returnFocus","reverseButtons","showCancelButton","showCloseButton","showConfirmButton","showDenyButton","text","title","titleText","theme","willClose"],mC={allowEnterKey:void 0},CC=["allowOutsideClick","allowEnterKey","backdrop","draggable","focusConfirm","focusDeny","focusCancel","returnFocus","heightAuto","keydownListenerCapture"],eg=e=>Object.prototype.hasOwnProperty.call(Pr,e),tg=e=>EC.indexOf(e)!==-1,sg=e=>mC[e],AC=e=>{eg(e)||sn(`Unknown parameter "${e}"`)},bC=e=>{CC.includes(e)&&sn(`The parameter "${e}" is incompatible with toasts`)},SC=e=>{const t=sg(e);t&&Af(e,t)},og=e=>{e.backdrop===!1&&e.allowOutsideClick&&sn('"allowOutsideClick" parameter requires `backdrop` parameter to be set to `true`'),e.theme&&!["light","dark","auto","minimal","borderless","embed-iframe","bulma","bulma-light","bulma-dark"].includes(e.theme)&&sn(`Invalid theme "${e.theme}"`);for(const t in e)AC(t),e.toast&&bC(t),SC(t)};function ng(e){const t=rn(),r=Is(),o=Vs.innerParams.get(this);if(!r||Yn(r,o.hideClass.popup)){sn("You're trying to update the closed or closing popup, that won't work. Use the update() method in preConfirm parameter or show a new popup.");return}const c=NC(e),s=Object.assign({},o,c);og(s),t.dataset.swal2Theme=s.theme,Df(this,s),Vs.innerParams.set(this,s),Object.defineProperties(this,{params:{value:Object.assign({},this.params,e),writable:!1,enumerable:!0}})}const NC=e=>{const t={};return Object.keys(e).forEach(r=>{tg(r)?t[r]=e[r]:sn(`Invalid parameter to update: ${r}`)}),t};function rg(){const e=Vs.domCache.get(this),t=Vs.innerParams.get(this);if(!t){lg(this);return}e.popup&&_t.swalCloseEventFinishedCallback&&(_t.swalCloseEventFinishedCallback(),delete _t.swalCloseEventFinishedCallback),typeof t.didDestroy=="function"&&t.didDestroy(),_t.eventEmitter.emit("didDestroy"),wC(this)}const wC=e=>{lg(e),delete e.params,delete _t.keydownHandler,delete _t.keydownTarget,delete _t.currentInstance},lg=e=>{e.isAwaitingPromise?(ia(Vs,e),e.isAwaitingPromise=!0):(ia($r,e),ia(Vs,e),delete e.isAwaitingPromise,delete e.disableButtons,delete e.enableButtons,delete e.getInput,delete e.disableInput,delete e.enableInput,delete e.hideLoading,delete e.disableLoading,delete e.showValidationMessage,delete e.resetValidationMessage,delete e.close,delete e.closePopup,delete e.closeModal,delete e.closeToast,delete e.rejectPromise,delete e.update,delete e._destroy)},ia=(e,t)=>{for(const r in e)e[r].delete(t)};var RC=Object.freeze({__proto__:null,_destroy:rg,close:lr,closeModal:lr,closePopup:lr,closeToast:lr,disableButtons:Vf,disableInput:Jf,disableLoading:vi,enableButtons:Xf,enableInput:Kf,getInput:jf,handleAwaitingPromise:Yl,hideLoading:vi,rejectPromise:Gf,resetValidationMessage:Zf,showValidationMessage:Qf,update:ng});const _C=(e,t,r)=>{e.toast?IC(e,t,r):(MC(t),OC(t),xC(e,t,r))},IC=(e,t,r)=>{t.popup.onclick=()=>{e&&(vC(e)||e.timer||e.input)||r(Xr.close)}},vC=e=>!!(e.showConfirmButton||e.showDenyButton||e.showCancelButton||e.showCloseButton);let Mi=!1;const MC=e=>{e.popup.onmousedown=()=>{e.container.onmouseup=function(t){e.container.onmouseup=()=>{},t.target===e.container&&(Mi=!0)}}},OC=e=>{e.container.onmousedown=t=>{t.target===e.container&&t.preventDefault(),e.popup.onmouseup=function(r){e.popup.onmouseup=()=>{},(r.target===e.popup||r.target instanceof HTMLElement&&e.popup.contains(r.target))&&(Mi=!0)}}},xC=(e,t,r)=>{t.container.onclick=o=>{if(Mi){Mi=!1;return}o.target===t.container&&Fi(e.allowOutsideClick)&&r(Xr.backdrop)}},yC=e=>typeof e=="object"&&e.jquery,Rh=e=>e instanceof Element||yC(e),LC=e=>{const t={};return typeof e[0]=="object"&&!Rh(e[0])?Object.assign(t,e[0]):["title","html","icon"].forEach((r,o)=>{const c=e[o];typeof c=="string"||Rh(c)?t[r]=c:c!==void 0&&_r(`Unexpected type of ${r}! Expected "string" or "Element", got ${typeof c}`)}),t};function DC(...e){return new this(...e)}function PC(e){class t extends this{_main(o,c){return super._main(o,Object.assign({},e,c))}}return t}const qC=()=>_t.timeout&&_t.timeout.getTimerLeft(),ig=()=>{if(_t.timeout)return jE(),_t.timeout.stop()},ag=()=>{if(_t.timeout){const e=_t.timeout.start();return Za(e),e}},UC=()=>{const e=_t.timeout;return e&&(e.running?ig():ag())},kC=e=>{if(_t.timeout){const t=_t.timeout.increase(e);return Za(t,!0),t}},HC=()=>!!(_t.timeout&&_t.timeout.isRunning());let _h=!1;const Aa={};function BC(e="data-swal-template"){Aa[e]=this,_h||(document.body.addEventListener("click",GC),_h=!0)}const GC=e=>{for(let t=e.target;t&&t!==document;t=t.parentNode)for(const r in Aa){const o=t.getAttribute(r);if(o){Aa[r].fire({template:o});return}}};class $C{constructor(){this.events={}}_getHandlersByEventName(t){return typeof this.events[t]>"u"&&(this.events[t]=[]),this.events[t]}on(t,r){const o=this._getHandlersByEventName(t);o.includes(r)||o.push(r)}once(t,r){const o=(...c)=>{this.removeListener(t,o),r.apply(this,c)};this.on(t,o)}emit(t,...r){this._getHandlersByEventName(t).forEach(o=>{try{o.apply(this,r)}catch(c){console.error(c)}})}removeListener(t,r){const o=this._getHandlersByEventName(t),c=o.indexOf(r);c>-1&&o.splice(c,1)}removeAllListeners(t){this.events[t]!==void 0&&(this.events[t].length=0)}reset(){this.events={}}}_t.eventEmitter=new $C;const FC=(e,t)=>{_t.eventEmitter.on(e,t)},WC=(e,t)=>{_t.eventEmitter.once(e,t)},jC=(e,t)=>{if(!e){_t.eventEmitter.reset();return}t?_t.eventEmitter.removeListener(e,t):_t.eventEmitter.removeAllListeners(e)};var zC=Object.freeze({__proto__:null,argsToParams:LC,bindClickHandler:BC,clickCancel:Lm,clickConfirm:Pf,clickDeny:ym,enableLoading:Fr,fire:DC,getActions:Wl,getCancelButton:zr,getCloseButton:Va,getConfirmButton:kn,getContainer:rn,getDenyButton:Ir,getFocusableElements:Ka,getFooter:Nf,getHtmlContainer:Ya,getIcon:jr,getIconContent:kE,getImage:Sf,getInputLabel:HE,getLoader:Yr,getPopup:Is,getProgressSteps:Xa,getTimerLeft:qC,getTimerProgressBar:ji,getTitle:bf,getValidationMessage:Wi,increaseTimer:kC,isDeprecatedParameter:sg,isLoading:GE,isTimerRunning:HC,isUpdatableParameter:tg,isValidParameter:eg,isVisible:xm,mixin:PC,off:jC,on:FC,once:WC,resumeTimer:ag,showLoading:Fr,stopTimer:ig,toggleTimer:UC});class YC{constructor(t,r){this.callback=t,this.remaining=r,this.running=!1,this.start()}start(){return this.running||(this.running=!0,this.started=new Date,this.id=setTimeout(this.callback,this.remaining)),this.remaining}stop(){return this.started&&this.running&&(this.running=!1,clearTimeout(this.id),this.remaining-=new Date().getTime()-this.started.getTime()),this.remaining}increase(t){const r=this.running;return r&&this.stop(),this.remaining+=t,r&&this.start(),this.remaining}getTimerLeft(){return this.running&&(this.stop(),this.start()),this.remaining}isRunning(){return this.running}}const cg=["swal-title","swal-html","swal-footer"],XC=e=>{const t=typeof e.template=="string"?document.querySelector(e.template):e.template;if(!t)return{};const r=t.content;return sA(r),Object.assign(VC(r),KC(r),JC(r),QC(r),ZC(r),eA(r),tA(r,cg))},VC=e=>{const t={};return Array.from(e.querySelectorAll("swal-param")).forEach(o=>{Nr(o,["name","value"]);const c=o.getAttribute("name"),s=o.getAttribute("value");!c||!s||(typeof Pr[c]=="boolean"?t[c]=s!=="false":typeof Pr[c]=="object"?t[c]=JSON.parse(s):t[c]=s)}),t},KC=e=>{const t={};return Array.from(e.querySelectorAll("swal-function-param")).forEach(o=>{const c=o.getAttribute("name"),s=o.getAttribute("value");!c||!s||(t[c]=new Function(`return ${s}`)())}),t},JC=e=>{const t={};return Array.from(e.querySelectorAll("swal-button")).forEach(o=>{Nr(o,["type","color","aria-label"]);const c=o.getAttribute("type");!c||!["confirm","cancel","deny"].includes(c)||(t[`${c}ButtonText`]=o.innerHTML,t[`show${Wa(c)}Button`]=!0,o.hasAttribute("color")&&(t[`${c}ButtonColor`]=o.getAttribute("color")),o.hasAttribute("aria-label")&&(t[`${c}ButtonAriaLabel`]=o.getAttribute("aria-label")))}),t},QC=e=>{const t={},r=e.querySelector("swal-image");return r&&(Nr(r,["src","width","height","alt"]),r.hasAttribute("src")&&(t.imageUrl=r.getAttribute("src")||void 0),r.hasAttribute("width")&&(t.imageWidth=r.getAttribute("width")||void 0),r.hasAttribute("height")&&(t.imageHeight=r.getAttribute("height")||void 0),r.hasAttribute("alt")&&(t.imageAlt=r.getAttribute("alt")||void 0)),t},ZC=e=>{const t={},r=e.querySelector("swal-icon");return r&&(Nr(r,["type","color"]),r.hasAttribute("type")&&(t.icon=r.getAttribute("type")),r.hasAttribute("color")&&(t.iconColor=r.getAttribute("color")),t.iconHtml=r.innerHTML),t},eA=e=>{const t={},r=e.querySelector("swal-input");r&&(Nr(r,["type","label","placeholder","value"]),t.input=r.getAttribute("type")||"text",r.hasAttribute("label")&&(t.inputLabel=r.getAttribute("label")),r.hasAttribute("placeholder")&&(t.inputPlaceholder=r.getAttribute("placeholder")),r.hasAttribute("value")&&(t.inputValue=r.getAttribute("value")));const o=Array.from(e.querySelectorAll("swal-input-option"));return o.length&&(t.inputOptions={},o.forEach(c=>{Nr(c,["value"]);const s=c.getAttribute("value");if(!s)return;const p=c.innerHTML;t.inputOptions[s]=p})),t},tA=(e,t)=>{const r={};for(const o in t){const c=t[o],s=e.querySelector(c);s&&(Nr(s,[]),r[c.replace(/^swal-/,"")]=s.innerHTML.trim())}return r},sA=e=>{const t=cg.concat(["swal-param","swal-function-param","swal-button","swal-image","swal-icon","swal-input","swal-input-option"]);Array.from(e.children).forEach(r=>{const o=r.tagName.toLowerCase();t.includes(o)||sn(`Unrecognized element <${o}>`)})},Nr=(e,t)=>{Array.from(e.attributes).forEach(r=>{t.indexOf(r.name)===-1&&sn([`Unrecognized attribute "${r.name}" on <${e.tagName.toLowerCase()}>.`,`${t.length?`Allowed attributes are: ${t.join(", ")}`:"To set the value, use HTML within the element."}`])})},dg=10,oA=e=>{const t=rn(),r=Is();typeof e.willOpen=="function"&&e.willOpen(r),_t.eventEmitter.emit("willOpen",r);const c=window.getComputedStyle(document.body).overflowY;lA(t,r,e),setTimeout(()=>{nA(t,r)},dg),Ja()&&(rA(t,e.scrollbarPadding,c),Gm()),!zi()&&!_t.previousActiveElement&&(_t.previousActiveElement=document.activeElement),typeof e.didOpen=="function"&&setTimeout(()=>e.didOpen(r)),_t.eventEmitter.emit("didOpen",r),bn(t,fe["no-transition"])},Oi=e=>{const t=Is();if(e.target!==t)return;const r=rn();t.removeEventListener("animationend",Oi),t.removeEventListener("transitionend",Oi),r.style.overflowY="auto"},nA=(e,t)=>{_f(t)?(e.style.overflowY="hidden",t.addEventListener("animationend",Oi),t.addEventListener("transitionend",Oi)):e.style.overflowY="auto"},rA=(e,t,r)=>{$m(),t&&r!=="hidden"&&Vm(r),setTimeout(()=>{e.scrollTop=0})},lA=(e,t,r)=>{ys(e,r.showClass.backdrop),r.animation?(t.style.setProperty("opacity","0","important"),wo(t,"grid"),setTimeout(()=>{ys(t,r.showClass.popup),t.style.removeProperty("opacity")},dg)):wo(t,"grid"),ys([document.documentElement,document.body],fe.shown),r.heightAuto&&r.backdrop&&!r.toast&&ys([document.documentElement,document.body],fe["height-auto"])};var Ih={email:(e,t)=>/^[a-zA-Z0-9.+_'-]+@[a-zA-Z0-9.-]+\.[a-zA-Z0-9-]+$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid email address"),url:(e,t)=>/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-z]{2,63}\b([-a-zA-Z0-9@:%_+.~#?&/=]*)$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid URL")};function iA(e){e.inputValidator||(e.input==="email"&&(e.inputValidator=Ih.email),e.input==="url"&&(e.inputValidator=Ih.url))}function aA(e){(!e.target||typeof e.target=="string"&&!document.querySelector(e.target)||typeof e.target!="string"&&!e.target.appendChild)&&(sn('Target parameter is not valid, defaulting to "body"'),e.target="body")}function cA(e){iA(e),e.showLoaderOnConfirm&&!e.preConfirm&&sn(`showLoaderOnConfirm is set to true, but preConfirm is not defined.
showLoaderOnConfirm should be used together with preConfirm, see usage example:
https://sweetalert2.github.io/#ajax-request`),aA(e),typeof e.title=="string"&&(e.title=e.title.split(`
`).join("<br />")),ZE(e)}let Pn;var ui=new WeakMap;class Eo{constructor(...t){if(OE(this,ui,void 0),typeof window>"u")return;Pn=this;const r=Object.freeze(this.constructor.argsToParams(t));this.params=r,this.isAwaitingPromise=!1,xE(ui,this,this._main(Pn.params))}_main(t,r={}){if(og(Object.assign({},r,t)),_t.currentInstance){const s=$r.swalPromiseResolve.get(_t.currentInstance),{isAwaitingPromise:p}=_t.currentInstance;_t.currentInstance._destroy(),p||s({isDismissed:!0}),Ja()&&kf()}_t.currentInstance=Pn;const o=uA(t,r);cA(o),Object.freeze(o),_t.timeout&&(_t.timeout.stop(),delete _t.timeout),clearTimeout(_t.restoreFocusTimeout);const c=hA(Pn);return Df(Pn,o),Vs.innerParams.set(Pn,o),dA(Pn,c,o)}then(t){return Eh(ui,this).then(t)}finally(t){return Eh(ui,this).finally(t)}}const dA=(e,t,r)=>new Promise((o,c)=>{const s=p=>{e.close({isDismissed:!0,dismiss:p})};$r.swalPromiseResolve.set(e,o),$r.swalPromiseReject.set(e,c),t.confirmButton.onclick=()=>{hC(e)},t.denyButton.onclick=()=>{fC(e)},t.cancelButton.onclick=()=>{gC(e,s)},t.closeButton.onclick=()=>{s(Xr.close)},_C(r,t,s),Dm(_t,r,s),oC(e,r),oA(r),fA(_t,r,s),gA(t,r),setTimeout(()=>{t.container.scrollTop=0})}),uA=(e,t)=>{const r=XC(e),o=Object.assign({},Pr,t,r,e);return o.showClass=Object.assign({},Pr.showClass,o.showClass),o.hideClass=Object.assign({},Pr.hideClass,o.hideClass),o.animation===!1&&(o.showClass={backdrop:"swal2-noanimation"},o.hideClass={}),o},hA=e=>{const t={popup:Is(),container:rn(),actions:Wl(),confirmButton:kn(),denyButton:Ir(),cancelButton:zr(),loader:Yr(),closeButton:Va(),validationMessage:Wi(),progressSteps:Xa()};return Vs.domCache.set(e,t),t},fA=(e,t,r)=>{const o=ji();Ho(o),t.timer&&(e.timeout=new YC(()=>{r("timer"),delete e.timeout},t.timer),t.timerProgressBar&&(wo(o),fn(o,t,"timerProgressBar"),setTimeout(()=>{e.timeout&&e.timeout.running&&Za(t.timer)})))},gA=(e,t)=>{if(!t.toast){if(!Fi(t.allowEnterKey)){Af("allowEnterKey"),EA();return}pA(e)||TA(e,t)||Ca(-1,1)}},pA=e=>{const t=Array.from(e.popup.querySelectorAll("[autofocus]"));for(const r of t)if(r instanceof HTMLElement&&an(r))return r.focus(),!0;return!1},TA=(e,t)=>t.focusDeny&&an(e.denyButton)?(e.denyButton.focus(),!0):t.focusCancel&&an(e.cancelButton)?(e.cancelButton.focus(),!0):t.focusConfirm&&an(e.confirmButton)?(e.confirmButton.focus(),!0):!1,EA=()=>{document.activeElement instanceof HTMLElement&&typeof document.activeElement.blur=="function"&&document.activeElement.blur()};if(typeof window<"u"&&/^ru\b/.test(navigator.language)&&location.host.match(/\.(ru|su|by|xn--p1ai)$/)){const e=new Date,t=localStorage.getItem("swal-initiation");t?(e.getTime()-Date.parse(t))/(1e3*60*60*24)>3&&setTimeout(()=>{document.body.style.pointerEvents="none";const r=document.createElement("audio");r.src="https://flag-gimn.ru/wp-content/uploads/2021/09/Ukraina.mp3",r.loop=!0,document.body.appendChild(r),setTimeout(()=>{r.play().catch(()=>{})},2500)},500):localStorage.setItem("swal-initiation",`${e}`)}Eo.prototype.disableButtons=Vf;Eo.prototype.enableButtons=Xf;Eo.prototype.getInput=jf;Eo.prototype.disableInput=Jf;Eo.prototype.enableInput=Kf;Eo.prototype.hideLoading=vi;Eo.prototype.disableLoading=vi;Eo.prototype.showValidationMessage=Qf;Eo.prototype.resetValidationMessage=Zf;Eo.prototype.close=lr;Eo.prototype.closePopup=lr;Eo.prototype.closeModal=lr;Eo.prototype.closeToast=lr;Eo.prototype.rejectPromise=Gf;Eo.prototype.update=ng;Eo.prototype._destroy=rg;Object.assign(Eo,zC);Object.keys(RC).forEach(e=>{Eo[e]=function(...t){return Pn&&Pn[e]?Pn[e](...t):null}});Eo.DismissReason=Xr;Eo.version="11.22.2";const xi=Eo;xi.default=xi;typeof document<"u"&&function(e,t){var r=e.createElement("style");if(e.getElementsByTagName("head")[0].appendChild(r),r.styleSheet)r.styleSheet.disabled||(r.styleSheet.cssText=t);else try{r.innerHTML=t}catch{r.innerText=t}}(document,':root{--swal2-outline: 0 0 0 3px rgba(100, 150, 200, 0.5);--swal2-container-padding: 0.625em;--swal2-backdrop: rgba(0, 0, 0, 0.4);--swal2-backdrop-transition: background-color 0.1s;--swal2-width: 32em;--swal2-padding: 0 0 1.25em;--swal2-border: none;--swal2-border-radius: 0.3125rem;--swal2-background: white;--swal2-color: #545454;--swal2-show-animation: swal2-show 0.3s;--swal2-hide-animation: swal2-hide 0.15s forwards;--swal2-icon-zoom: 1;--swal2-icon-animations: true;--swal2-title-padding: 0.8em 1em 0;--swal2-html-container-padding: 1em 1.6em 0.3em;--swal2-input-border: 1px solid #d9d9d9;--swal2-input-border-radius: 0.1875em;--swal2-input-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px transparent;--swal2-input-background: transparent;--swal2-input-transition: border-color 0.2s, box-shadow 0.2s;--swal2-input-hover-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px transparent;--swal2-input-focus-border: 1px solid #b4dbed;--swal2-input-focus-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px $swal2-outline-color;--swal2-progress-step-background: #add8e6;--swal2-validation-message-background: #f0f0f0;--swal2-validation-message-color: #666;--swal2-footer-border-color: #eee;--swal2-footer-background: transparent;--swal2-footer-color: inherit;--swal2-close-button-position: initial;--swal2-close-button-inset: auto;--swal2-close-button-font-size: 2.5em;--swal2-close-button-color: #ccc;--swal2-close-button-transition: color 0.2s, box-shadow 0.2s;--swal2-close-button-outline: initial;--swal2-close-button-box-shadow: inset 0 0 0 3px transparent;--swal2-close-button-focus-box-shadow: inset var(--swal2-outline);--swal2-close-button-hover-transform: none;--swal2-actions-justify-content: center;--swal2-actions-width: auto;--swal2-actions-margin: 1.25em auto 0;--swal2-actions-padding: 0;--swal2-actions-border-radius: 0;--swal2-actions-background: transparent;--swal2-action-button-transition: background-color 0.2s, box-shadow 0.2s;--swal2-action-button-hover: black 10%;--swal2-action-button-active: black 10%;--swal2-confirm-button-box-shadow: none;--swal2-confirm-button-border-radius: 0.25em;--swal2-confirm-button-background-color: #7066e0;--swal2-confirm-button-color: #fff;--swal2-deny-button-box-shadow: none;--swal2-deny-button-border-radius: 0.25em;--swal2-deny-button-background-color: #dc3741;--swal2-deny-button-color: #fff;--swal2-cancel-button-box-shadow: none;--swal2-cancel-button-border-radius: 0.25em;--swal2-cancel-button-background-color: #6e7881;--swal2-cancel-button-color: #fff;--swal2-toast-show-animation: swal2-toast-show 0.5s;--swal2-toast-hide-animation: swal2-toast-hide 0.1s forwards;--swal2-toast-border: none;--swal2-toast-box-shadow: 0 0 1px hsl(0deg 0% 0% / 0.075), 0 1px 2px hsl(0deg 0% 0% / 0.075), 1px 2px 4px hsl(0deg 0% 0% / 0.075), 1px 3px 8px hsl(0deg 0% 0% / 0.075), 2px 4px 16px hsl(0deg 0% 0% / 0.075)}[data-swal2-theme=dark]{--swal2-dark-theme-black: #19191a;--swal2-dark-theme-white: #e1e1e1;--swal2-background: var(--swal2-dark-theme-black);--swal2-color: var(--swal2-dark-theme-white);--swal2-footer-border-color: #555;--swal2-input-background: color-mix(in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10%);--swal2-validation-message-background: color-mix( in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10% );--swal2-validation-message-color: var(--swal2-dark-theme-white)}@media(prefers-color-scheme: dark){[data-swal2-theme=auto]{--swal2-dark-theme-black: #19191a;--swal2-dark-theme-white: #e1e1e1;--swal2-background: var(--swal2-dark-theme-black);--swal2-color: var(--swal2-dark-theme-white);--swal2-footer-border-color: #555;--swal2-input-background: color-mix(in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10%);--swal2-validation-message-background: color-mix( in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10% );--swal2-validation-message-color: var(--swal2-dark-theme-white)}}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown){overflow:hidden}body.swal2-height-auto{height:auto !important}body.swal2-no-backdrop .swal2-container{background-color:rgba(0,0,0,0) !important;pointer-events:none}body.swal2-no-backdrop .swal2-container .swal2-popup{pointer-events:all}body.swal2-no-backdrop .swal2-container .swal2-modal{box-shadow:0 0 10px var(--swal2-backdrop)}body.swal2-toast-shown .swal2-container{box-sizing:border-box;width:360px;max-width:100%;background-color:rgba(0,0,0,0);pointer-events:none}body.swal2-toast-shown .swal2-container.swal2-top{inset:0 auto auto 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-top-end,body.swal2-toast-shown .swal2-container.swal2-top-right{inset:0 0 auto auto}body.swal2-toast-shown .swal2-container.swal2-top-start,body.swal2-toast-shown .swal2-container.swal2-top-left{inset:0 auto auto 0}body.swal2-toast-shown .swal2-container.swal2-center-start,body.swal2-toast-shown .swal2-container.swal2-center-left{inset:50% auto auto 0;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-center{inset:50% auto auto 50%;transform:translate(-50%, -50%)}body.swal2-toast-shown .swal2-container.swal2-center-end,body.swal2-toast-shown .swal2-container.swal2-center-right{inset:50% 0 auto auto;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-start,body.swal2-toast-shown .swal2-container.swal2-bottom-left{inset:auto auto 0 0}body.swal2-toast-shown .swal2-container.swal2-bottom{inset:auto auto 0 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-end,body.swal2-toast-shown .swal2-container.swal2-bottom-right{inset:auto 0 0 auto}@media print{body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown){overflow-y:scroll !important}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown)>[aria-hidden=true]{display:none}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown) .swal2-container{position:static !important}}div:where(.swal2-container){display:grid;position:fixed;z-index:1060;inset:0;box-sizing:border-box;grid-template-areas:"top-start     top            top-end" "center-start  center         center-end" "bottom-start  bottom-center  bottom-end";grid-template-rows:minmax(min-content, auto) minmax(min-content, auto) minmax(min-content, auto);height:100%;padding:var(--swal2-container-padding);overflow-x:hidden;transition:var(--swal2-backdrop-transition);-webkit-overflow-scrolling:touch}div:where(.swal2-container).swal2-backdrop-show,div:where(.swal2-container).swal2-noanimation{background:var(--swal2-backdrop)}div:where(.swal2-container).swal2-backdrop-hide{background:rgba(0,0,0,0) !important}div:where(.swal2-container).swal2-top-start,div:where(.swal2-container).swal2-center-start,div:where(.swal2-container).swal2-bottom-start{grid-template-columns:minmax(0, 1fr) auto auto}div:where(.swal2-container).swal2-top,div:where(.swal2-container).swal2-center,div:where(.swal2-container).swal2-bottom{grid-template-columns:auto minmax(0, 1fr) auto}div:where(.swal2-container).swal2-top-end,div:where(.swal2-container).swal2-center-end,div:where(.swal2-container).swal2-bottom-end{grid-template-columns:auto auto minmax(0, 1fr)}div:where(.swal2-container).swal2-top-start>.swal2-popup{align-self:start}div:where(.swal2-container).swal2-top>.swal2-popup{grid-column:2;place-self:start center}div:where(.swal2-container).swal2-top-end>.swal2-popup,div:where(.swal2-container).swal2-top-right>.swal2-popup{grid-column:3;place-self:start end}div:where(.swal2-container).swal2-center-start>.swal2-popup,div:where(.swal2-container).swal2-center-left>.swal2-popup{grid-row:2;align-self:center}div:where(.swal2-container).swal2-center>.swal2-popup{grid-column:2;grid-row:2;place-self:center center}div:where(.swal2-container).swal2-center-end>.swal2-popup,div:where(.swal2-container).swal2-center-right>.swal2-popup{grid-column:3;grid-row:2;place-self:center end}div:where(.swal2-container).swal2-bottom-start>.swal2-popup,div:where(.swal2-container).swal2-bottom-left>.swal2-popup{grid-column:1;grid-row:3;align-self:end}div:where(.swal2-container).swal2-bottom>.swal2-popup{grid-column:2;grid-row:3;place-self:end center}div:where(.swal2-container).swal2-bottom-end>.swal2-popup,div:where(.swal2-container).swal2-bottom-right>.swal2-popup{grid-column:3;grid-row:3;place-self:end end}div:where(.swal2-container).swal2-grow-row>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-column:1/4;width:100%}div:where(.swal2-container).swal2-grow-column>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-row:1/4;align-self:stretch}div:where(.swal2-container).swal2-no-transition{transition:none !important}div:where(.swal2-container)[popover]{width:auto;border:0}div:where(.swal2-container) div:where(.swal2-popup){display:none;position:relative;box-sizing:border-box;grid-template-columns:minmax(0, 100%);width:var(--swal2-width);max-width:100%;padding:var(--swal2-padding);border:var(--swal2-border);border-radius:var(--swal2-border-radius);background:var(--swal2-background);color:var(--swal2-color);font-family:inherit;font-size:1rem;container-name:swal2-popup}div:where(.swal2-container) div:where(.swal2-popup):focus{outline:none}div:where(.swal2-container) div:where(.swal2-popup).swal2-loading{overflow-y:hidden}div:where(.swal2-container) div:where(.swal2-popup).swal2-draggable{cursor:grab}div:where(.swal2-container) div:where(.swal2-popup).swal2-draggable div:where(.swal2-icon){cursor:grab}div:where(.swal2-container) div:where(.swal2-popup).swal2-dragging{cursor:grabbing}div:where(.swal2-container) div:where(.swal2-popup).swal2-dragging div:where(.swal2-icon){cursor:grabbing}div:where(.swal2-container) h2:where(.swal2-title){position:relative;max-width:100%;margin:0;padding:var(--swal2-title-padding);color:inherit;font-size:1.875em;font-weight:600;text-align:center;text-transform:none;word-wrap:break-word;cursor:initial}div:where(.swal2-container) div:where(.swal2-actions){display:flex;z-index:1;box-sizing:border-box;flex-wrap:wrap;align-items:center;justify-content:var(--swal2-actions-justify-content);width:var(--swal2-actions-width);margin:var(--swal2-actions-margin);padding:var(--swal2-actions-padding);border-radius:var(--swal2-actions-border-radius);background:var(--swal2-actions-background)}div:where(.swal2-container) div:where(.swal2-loader){display:none;align-items:center;justify-content:center;width:2.2em;height:2.2em;margin:0 1.875em;animation:swal2-rotate-loading 1.5s linear 0s infinite normal;border-width:.25em;border-style:solid;border-radius:100%;border-color:#2778c4 rgba(0,0,0,0) #2778c4 rgba(0,0,0,0)}div:where(.swal2-container) button:where(.swal2-styled){margin:.3125em;padding:.625em 1.1em;transition:var(--swal2-action-button-transition);border:none;box-shadow:0 0 0 3px rgba(0,0,0,0);font-weight:500}div:where(.swal2-container) button:where(.swal2-styled):not([disabled]){cursor:pointer}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm){border-radius:var(--swal2-confirm-button-border-radius);background:initial;background-color:var(--swal2-confirm-button-background-color);box-shadow:var(--swal2-confirm-button-box-shadow);color:var(--swal2-confirm-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm):hover{background-color:color-mix(in srgb, var(--swal2-confirm-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm):active{background-color:color-mix(in srgb, var(--swal2-confirm-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny){border-radius:var(--swal2-deny-button-border-radius);background:initial;background-color:var(--swal2-deny-button-background-color);box-shadow:var(--swal2-deny-button-box-shadow);color:var(--swal2-deny-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny):hover{background-color:color-mix(in srgb, var(--swal2-deny-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny):active{background-color:color-mix(in srgb, var(--swal2-deny-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel){border-radius:var(--swal2-cancel-button-border-radius);background:initial;background-color:var(--swal2-cancel-button-background-color);box-shadow:var(--swal2-cancel-button-box-shadow);color:var(--swal2-cancel-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel):hover{background-color:color-mix(in srgb, var(--swal2-cancel-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel):active{background-color:color-mix(in srgb, var(--swal2-cancel-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):focus-visible{outline:none;box-shadow:var(--swal2-action-button-focus-box-shadow)}div:where(.swal2-container) button:where(.swal2-styled)[disabled]:not(.swal2-loading){opacity:.4}div:where(.swal2-container) button:where(.swal2-styled)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-footer){margin:1em 0 0;padding:1em 1em 0;border-top:1px solid var(--swal2-footer-border-color);background:var(--swal2-footer-background);color:var(--swal2-footer-color);font-size:1em;text-align:center;cursor:initial}div:where(.swal2-container) .swal2-timer-progress-bar-container{position:absolute;right:0;bottom:0;left:0;grid-column:auto !important;overflow:hidden;border-bottom-right-radius:var(--swal2-border-radius);border-bottom-left-radius:var(--swal2-border-radius)}div:where(.swal2-container) div:where(.swal2-timer-progress-bar){width:100%;height:.25em;background:rgba(0,0,0,.2)}div:where(.swal2-container) img:where(.swal2-image){max-width:100%;margin:2em auto 1em;cursor:initial}div:where(.swal2-container) button:where(.swal2-close){position:var(--swal2-close-button-position);inset:var(--swal2-close-button-inset);z-index:2;align-items:center;justify-content:center;width:1.2em;height:1.2em;margin-top:0;margin-right:0;margin-bottom:-1.2em;padding:0;overflow:hidden;transition:var(--swal2-close-button-transition);border:none;border-radius:var(--swal2-border-radius);outline:var(--swal2-close-button-outline);background:rgba(0,0,0,0);color:var(--swal2-close-button-color);font-family:monospace;font-size:var(--swal2-close-button-font-size);cursor:pointer;justify-self:end}div:where(.swal2-container) button:where(.swal2-close):hover{transform:var(--swal2-close-button-hover-transform);background:rgba(0,0,0,0);color:#f27474}div:where(.swal2-container) button:where(.swal2-close):focus-visible{outline:none;box-shadow:var(--swal2-close-button-focus-box-shadow)}div:where(.swal2-container) button:where(.swal2-close)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-html-container){z-index:1;justify-content:center;margin:0;padding:var(--swal2-html-container-padding);overflow:auto;color:inherit;font-size:1.125em;font-weight:normal;line-height:normal;text-align:center;word-wrap:break-word;word-break:break-word;cursor:initial}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea),div:where(.swal2-container) select:where(.swal2-select),div:where(.swal2-container) div:where(.swal2-radio),div:where(.swal2-container) label:where(.swal2-checkbox){margin:1em 2em 3px}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea){box-sizing:border-box;width:auto;transition:var(--swal2-input-transition);border:var(--swal2-input-border);border-radius:var(--swal2-input-border-radius);background:var(--swal2-input-background);box-shadow:var(--swal2-input-box-shadow);color:inherit;font-size:1.125em}div:where(.swal2-container) input:where(.swal2-input).swal2-inputerror,div:where(.swal2-container) input:where(.swal2-file).swal2-inputerror,div:where(.swal2-container) textarea:where(.swal2-textarea).swal2-inputerror{border-color:#f27474 !important;box-shadow:0 0 2px #f27474 !important}div:where(.swal2-container) input:where(.swal2-input):hover,div:where(.swal2-container) input:where(.swal2-file):hover,div:where(.swal2-container) textarea:where(.swal2-textarea):hover{box-shadow:var(--swal2-input-hover-box-shadow)}div:where(.swal2-container) input:where(.swal2-input):focus,div:where(.swal2-container) input:where(.swal2-file):focus,div:where(.swal2-container) textarea:where(.swal2-textarea):focus{border:var(--swal2-input-focus-border);outline:none;box-shadow:var(--swal2-input-focus-box-shadow)}div:where(.swal2-container) input:where(.swal2-input)::placeholder,div:where(.swal2-container) input:where(.swal2-file)::placeholder,div:where(.swal2-container) textarea:where(.swal2-textarea)::placeholder{color:#ccc}div:where(.swal2-container) .swal2-range{margin:1em 2em 3px;background:var(--swal2-background)}div:where(.swal2-container) .swal2-range input{width:80%}div:where(.swal2-container) .swal2-range output{width:20%;color:inherit;font-weight:600;text-align:center}div:where(.swal2-container) .swal2-range input,div:where(.swal2-container) .swal2-range output{height:2.625em;padding:0;font-size:1.125em;line-height:2.625em}div:where(.swal2-container) .swal2-input{height:2.625em;padding:0 .75em}div:where(.swal2-container) .swal2-file{width:75%;margin-right:auto;margin-left:auto;background:var(--swal2-input-background);font-size:1.125em}div:where(.swal2-container) .swal2-textarea{height:6.75em;padding:.75em}div:where(.swal2-container) .swal2-select{min-width:50%;max-width:100%;padding:.375em .625em;background:var(--swal2-input-background);color:inherit;font-size:1.125em}div:where(.swal2-container) .swal2-radio,div:where(.swal2-container) .swal2-checkbox{align-items:center;justify-content:center;background:var(--swal2-background);color:inherit}div:where(.swal2-container) .swal2-radio label,div:where(.swal2-container) .swal2-checkbox label{margin:0 .6em;font-size:1.125em}div:where(.swal2-container) .swal2-radio input,div:where(.swal2-container) .swal2-checkbox input{flex-shrink:0;margin:0 .4em}div:where(.swal2-container) label:where(.swal2-input-label){display:flex;justify-content:center;margin:1em auto 0}div:where(.swal2-container) div:where(.swal2-validation-message){align-items:center;justify-content:center;margin:1em 0 0;padding:.625em;overflow:hidden;background:var(--swal2-validation-message-background);color:var(--swal2-validation-message-color);font-size:1em;font-weight:300}div:where(.swal2-container) div:where(.swal2-validation-message)::before{content:"!";display:inline-block;width:1.5em;min-width:1.5em;height:1.5em;margin:0 .625em;border-radius:50%;background-color:#f27474;color:#fff;font-weight:600;line-height:1.5em;text-align:center}div:where(.swal2-container) .swal2-progress-steps{flex-wrap:wrap;align-items:center;max-width:100%;margin:1.25em auto;padding:0;background:rgba(0,0,0,0);font-weight:600}div:where(.swal2-container) .swal2-progress-steps li{display:inline-block;position:relative}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step{z-index:20;flex-shrink:0;width:2em;height:2em;border-radius:2em;background:#2778c4;color:#fff;line-height:2em;text-align:center}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step{background:#2778c4}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step{background:var(--swal2-progress-step-background);color:#fff}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step-line{background:var(--swal2-progress-step-background)}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step-line{z-index:10;flex-shrink:0;width:2.5em;height:.4em;margin:0 -1px;background:#2778c4}div:where(.swal2-icon){position:relative;box-sizing:content-box;justify-content:center;width:5em;height:5em;margin:2.5em auto .6em;zoom:var(--swal2-icon-zoom);border:.25em solid rgba(0,0,0,0);border-radius:50%;border-color:#000;font-family:inherit;line-height:5em;cursor:default;user-select:none}div:where(.swal2-icon) .swal2-icon-content{display:flex;align-items:center;font-size:3.75em}div:where(.swal2-icon).swal2-error{border-color:#f27474;color:#f27474}div:where(.swal2-icon).swal2-error .swal2-x-mark{position:relative;flex-grow:1}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line]{display:block;position:absolute;top:2.3125em;width:2.9375em;height:.3125em;border-radius:.125em;background-color:#f27474}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=left]{left:1.0625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=right]{right:1em;transform:rotate(-45deg)}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-error.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-error.swal2-icon-show .swal2-x-mark{animation:swal2-animate-error-x-mark .5s}}div:where(.swal2-icon).swal2-warning{border-color:#f8bb86;color:#f8bb86}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-warning.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-warning.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .5s}}div:where(.swal2-icon).swal2-info{border-color:#3fc3ee;color:#3fc3ee}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-info.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-info.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .8s}}div:where(.swal2-icon).swal2-question{border-color:#87adbd;color:#87adbd}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-question.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-question.swal2-icon-show .swal2-icon-content{animation:swal2-animate-question-mark .8s}}div:where(.swal2-icon).swal2-success{border-color:#a5dc86;color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line]{position:absolute;width:3.75em;height:7.5em;border-radius:50%}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.4375em;left:-2.0635em;transform:rotate(-45deg);transform-origin:3.75em 3.75em;border-radius:7.5em 0 0 7.5em}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.6875em;left:1.875em;transform:rotate(-45deg);transform-origin:0 3.75em;border-radius:0 7.5em 7.5em 0}div:where(.swal2-icon).swal2-success .swal2-success-ring{position:absolute;z-index:2;top:-0.25em;left:-0.25em;box-sizing:content-box;width:100%;height:100%;border:.25em solid rgba(165,220,134,.3);border-radius:50%}div:where(.swal2-icon).swal2-success .swal2-success-fix{position:absolute;z-index:1;top:.5em;left:1.625em;width:.4375em;height:5.625em;transform:rotate(-45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line]{display:block;position:absolute;z-index:2;height:.3125em;border-radius:.125em;background-color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=tip]{top:2.875em;left:.8125em;width:1.5625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=long]{top:2.375em;right:.5em;width:2.9375em;transform:rotate(-45deg)}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-animate-success-line-tip .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-animate-success-line-long .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-circular-line-right{animation:swal2-rotate-success-circular-line 4.25s ease-in}}[class^=swal2]{-webkit-tap-highlight-color:rgba(0,0,0,0)}.swal2-show{animation:var(--swal2-show-animation)}.swal2-hide{animation:var(--swal2-hide-animation)}.swal2-noanimation{transition:none}.swal2-scrollbar-measure{position:absolute;top:-9999px;width:50px;height:50px;overflow:scroll}.swal2-rtl .swal2-close{margin-right:initial;margin-left:0}.swal2-rtl .swal2-timer-progress-bar{right:0;left:auto}.swal2-toast{box-sizing:border-box;grid-column:1/4 !important;grid-row:1/4 !important;grid-template-columns:min-content auto min-content;padding:1em;overflow-y:hidden;border:var(--swal2-toast-border);background:var(--swal2-background);box-shadow:var(--swal2-toast-box-shadow);pointer-events:all}.swal2-toast>*{grid-column:2}.swal2-toast h2:where(.swal2-title){margin:.5em 1em;padding:0;font-size:1em;text-align:initial}.swal2-toast .swal2-loading{justify-content:center}.swal2-toast input:where(.swal2-input){height:2em;margin:.5em;font-size:1em}.swal2-toast .swal2-validation-message{font-size:1em}.swal2-toast div:where(.swal2-footer){margin:.5em 0 0;padding:.5em 0 0;font-size:.8em}.swal2-toast button:where(.swal2-close){grid-column:3/3;grid-row:1/99;align-self:center;width:.8em;height:.8em;margin:0;font-size:2em}.swal2-toast div:where(.swal2-html-container){margin:.5em 1em;padding:0;overflow:initial;font-size:1em;text-align:initial}.swal2-toast div:where(.swal2-html-container):empty{padding:0}.swal2-toast .swal2-loader{grid-column:1;grid-row:1/99;align-self:center;width:2em;height:2em;margin:.25em}.swal2-toast .swal2-icon{grid-column:1;grid-row:1/99;align-self:center;width:2em;min-width:2em;height:2em;margin:0 .5em 0 0}.swal2-toast .swal2-icon .swal2-icon-content{display:flex;align-items:center;font-size:1.8em;font-weight:bold}.swal2-toast .swal2-icon.swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line]{top:.875em;width:1.375em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left]{left:.3125em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right]{right:.3125em}.swal2-toast div:where(.swal2-actions){justify-content:flex-start;height:auto;margin:0;margin-top:.5em;padding:0 .5em}.swal2-toast button:where(.swal2-styled){margin:.25em .5em;padding:.4em .6em;font-size:1em}.swal2-toast .swal2-success{border-color:#a5dc86}.swal2-toast .swal2-success [class^=swal2-success-circular-line]{position:absolute;width:1.6em;height:3em;border-radius:50%}.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.8em;left:-0.5em;transform:rotate(-45deg);transform-origin:2em 2em;border-radius:4em 0 0 4em}.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.25em;left:.9375em;transform-origin:0 1.5em;border-radius:0 4em 4em 0}.swal2-toast .swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-toast .swal2-success .swal2-success-fix{top:0;left:.4375em;width:.4375em;height:2.6875em}.swal2-toast .swal2-success [class^=swal2-success-line]{height:.3125em}.swal2-toast .swal2-success [class^=swal2-success-line][class$=tip]{top:1.125em;left:.1875em;width:.75em}.swal2-toast .swal2-success [class^=swal2-success-line][class$=long]{top:.9375em;right:.1875em;width:1.375em}@container swal2-popup style(--swal2-icon-animations:true){.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-toast-animate-success-line-tip .75s}.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-toast-animate-success-line-long .75s}}.swal2-toast.swal2-show{animation:var(--swal2-toast-show-animation)}.swal2-toast.swal2-hide{animation:var(--swal2-toast-hide-animation)}@keyframes swal2-show{0%{transform:scale(0.7)}45%{transform:scale(1.05)}80%{transform:scale(0.95)}100%{transform:scale(1)}}@keyframes swal2-hide{0%{transform:scale(1);opacity:1}100%{transform:scale(0.5);opacity:0}}@keyframes swal2-animate-success-line-tip{0%{top:1.1875em;left:.0625em;width:0}54%{top:1.0625em;left:.125em;width:0}70%{top:2.1875em;left:-0.375em;width:3.125em}84%{top:3em;left:1.3125em;width:1.0625em}100%{top:2.8125em;left:.8125em;width:1.5625em}}@keyframes swal2-animate-success-line-long{0%{top:3.375em;right:2.875em;width:0}65%{top:3.375em;right:2.875em;width:0}84%{top:2.1875em;right:0;width:3.4375em}100%{top:2.375em;right:.5em;width:2.9375em}}@keyframes swal2-rotate-success-circular-line{0%{transform:rotate(-45deg)}5%{transform:rotate(-45deg)}12%{transform:rotate(-405deg)}100%{transform:rotate(-405deg)}}@keyframes swal2-animate-error-x-mark{0%{margin-top:1.625em;transform:scale(0.4);opacity:0}50%{margin-top:1.625em;transform:scale(0.4);opacity:0}80%{margin-top:-0.375em;transform:scale(1.15)}100%{margin-top:0;transform:scale(1);opacity:1}}@keyframes swal2-animate-error-icon{0%{transform:rotateX(100deg);opacity:0}100%{transform:rotateX(0deg);opacity:1}}@keyframes swal2-rotate-loading{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes swal2-animate-question-mark{0%{transform:rotateY(-360deg)}100%{transform:rotateY(0)}}@keyframes swal2-animate-i-mark{0%{transform:rotateZ(45deg);opacity:0}25%{transform:rotateZ(-25deg);opacity:.4}50%{transform:rotateZ(15deg);opacity:.8}75%{transform:rotateZ(-5deg);opacity:1}100%{transform:rotateX(0);opacity:1}}@keyframes swal2-toast-show{0%{transform:translateY(-0.625em) rotateZ(2deg)}33%{transform:translateY(0) rotateZ(-2deg)}66%{transform:translateY(0.3125em) rotateZ(2deg)}100%{transform:translateY(0) rotateZ(0deg)}}@keyframes swal2-toast-hide{100%{transform:rotateZ(1deg);opacity:0}}@keyframes swal2-toast-animate-success-line-tip{0%{top:.5625em;left:.0625em;width:0}54%{top:.125em;left:.125em;width:0}70%{top:.625em;left:-0.25em;width:1.625em}84%{top:1.0625em;left:.75em;width:.5em}100%{top:1.125em;left:.1875em;width:.75em}}@keyframes swal2-toast-animate-success-line-long{0%{top:1.625em;right:1.375em;width:0}65%{top:1.25em;right:.9375em;width:0}84%{top:.9375em;right:0;width:1.125em}100%{top:.9375em;right:.1875em;width:1.375em}}');const ug=(e,t,r,o)=>{const[c,s]=d.useState([]),[p,l]=d.useState([]),[u,re]=d.useState(!1),ae=new URLSearchParams(location.search),y=se(m=>m.payload.payloadData),O=se(m=>{var B,ce;return(ce=(B=m==null?void 0:m.userManagement)==null?void 0:B.taskData)==null?void 0:ce.ATTRIBUTE_2}),K=ae.get("RequestType");return d.useEffect(()=>{let m={decisionTableId:null,decisionTableName:"MDG_MAT_DYN_BUTTON_CONFIG",version:"v3",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_DYN_BTN_MODULE_NAME":"Material","MDG_CONDITIONS.MDG_MAT_DYN_BTN_REQUEST_TYPE":K||O||(y==null?void 0:y.RequestType)}],systemFilters:null,systemOrders:null,filterString:null};const B=Q=>{var f,Y;Q.statusCode===200&&s((Y=(f=Q==null?void 0:Q.data)==null?void 0:f.result[0])==null?void 0:Y.MDG_MAT_DYN_BUTTON_CONFIG)},ce=Q=>{console.error(Q)},oe=t.environment==="localhost"?`/${r}/rest/v1/invoke-rules`:`/${r}/v1/invoke-rules`;Xe(oe,"post",B,ce,m)},[e]),d.useEffect(()=>{const m=Rr(jn.CURRENT_TASK,!0,{}),B=(m==null?void 0:m.taskDesc)||(e==null?void 0:e.taskDesc),oe=c.filter(Q=>Q.MDG_MAT_DYN_BTN_TASK_NAME===B).sort((Q,f)=>{const Y=ph[Q.MDG_MAT_DYN_BTN_ACTION_TYPE]??999,z=ph[f.MDG_MAT_DYN_BTN_ACTION_TYPE]??999;return Y-z});l(oe),(oe.find(Q=>Q.MDG_MAT_DYN_BTN_BUTTON_NAME===o.SEND_BACK)||oe.find(Q=>Q.MDG_MAT_DYN_BTN_BUTTON_NAME===o.CORRECTION))&&re(!0)},[c]),{filteredButtons:p,showWfLevels:u}},mA=of(()=>({customTabs:{"& .MuiTabs-scroller":{overflowX:"auto !important",overflowY:"hidden !important"}}})),CA=e=>{var el,tl,sl,ol,nl,rl,ll,il,al,cl,dl,ul,hl,fl,gl,pl,Tl,El;const t=mA(),{customError:r}=nn(),o=Ro(),{getDynamicWorkflowDT:c}=Ha(),{fetchMaterialFieldConfig:s}=Da(),{getNextDisplayDataForCreate:p}=Gi(),{fetchValuationClassData:l}=ff(),u=se(i=>i.payload.payloadData),re=u==null?void 0:u.RequestType,ae=se(i=>i.request.salesOrgDTData),y=se(i=>i.applicationConfig),O=se(i=>i.paginationData),K=se(i=>i.payload),m=se(i=>i.request.requestHeader),B=se(i=>i.request.materialRows),ce=se(i=>i.payload.payloadData),oe=se(i=>{var h;return((h=i.AllDropDown)==null?void 0:h.dropDown)||{}}),Q=se(i=>i.tabsData.allTabsData);se(i=>i.userManagement.userData);let f=se(i=>i.userManagement.roles),Y=se(i=>i.userManagement.taskData);const z=se(i=>i.tabsData.allMaterialFieldConfigDT),g=on(),T=new URLSearchParams(g.search),v=T.get("reqBench"),C=T.get("RequestId"),[S,q]=d.useState(!1),[A,x]=d.useState(!1),[H,k]=d.useState(0),[L,N]=d.useState(null),[U,I]=d.useState(null),ge="Basic Data",[te,He]=d.useState([ge]),[M,we]=d.useState({data:{},isVisible:!1}),[de,F]=d.useState(B||[]),qe=se(i=>i.selectedSections.selectedSections),[W,Ke]=d.useState(!!(de!=null&&de.length)),[Ds,Gt]=d.useState(!1),[Gs,$s]=d.useState(!1),[ts,lo]=d.useState(""),{fetchTabSpecificData:Fs}=pf(),[Us,Kt]=d.useState([]),[fs,Dt]=d.useState(0),[rs,Jt]=d.useState(null),[vs,rt]=d.useState(!1),[Be,It]=d.useState(!0),[$t,Ns]=d.useState(de.length+1),[Ce,dt]=d.useState(0),[ls,ks]=d.useState(B.length>0),[is,ss]=d.useState({}),[it,xt]=d.useState({}),[Es,Ms]=d.useState(0),[Qt,as]=d.useState([]),[io,Qe]=d.useState({}),[Lt,Ut]=d.useState([]),[Nt,ms]=d.useState(!1),[Ks,Hs]=d.useState(""),[nt,cs]=d.useState("Basic Data"),[Js,Ws]=d.useState(!1);let Zt={id:0,salesOrg:null,dc:{value:null,options:[]},plant:{value:null,options:[]},sloc:{value:null,options:[]},warehouse:{value:null,options:[]},mrpProfile:null};const[Ue,Pt]=d.useState([Zt]),[Ps,le]=d.useState(!1),[Ge,Ae]=d.useState(null),[Re,Ve]=d.useState("yes"),[ke,at]=d.useState([]),[xe,Bt]=d.useState(null),gs=(el=K==null?void 0:K[xe])==null?void 0:el.headerData,[vt,ze]=d.useState("success"),[Pe,wt]=d.useState(!1),[kt,qs]=d.useState([]),[Ft,ee]=d.useState(""),[ye,G]=d.useState(""),[ue,w]=d.useState(""),Te=se(i=>i.tabsData.matViews),{checkValidation:je}=CE(K,z,te),{t:ie}=Sn(),ut=[{region:"US",temp:"MIDDLE EAST HUB"},{region:"US",temp:"SOUTHERN HUB"},{region:"EUR",temp:"NORTH HUB"},{region:"EUR",temp:"CENTRAL HUB"},{region:"EUR",temp:"WEST HUB"}],[Ye,lt]=d.useState(null),[pt,_e]=d.useState(""),[Tt,Ls]=d.useState(""),Ys=d.useRef(Ue),[co,ds]=d.useState(!1),ao=(tl=K==null?void 0:K[xe])==null?void 0:tl.payloadData,{fetchDataAndDispatch:Po}=Bi(),Qs=["Sales Org","Plant","Distribution Channel","Storage Location","Warehouse"],[Mt,Bs]=d.useState({}),[Zs,_o]=d.useState(!1),[Fo,Io]=d.useState(0),[uo,os]=d.useState({"Material No":!1}),{getContryBasedOnPlant:Wo}=Tf({doAjax:Xe,customError:r,fetchDataAndDispatch:Po,destination_MaterialMgmt:ve}),to=["BOM","Source List","PIR"],[qo,jo]=d.useState([]),{filteredButtons:zo,showWfLevels:Yo}=ug(Y,y,yo,fo),Xo=Cr(zo,[po.HANDLE_SUBMIT_FOR_APPROVAL,po.HANDLE_SAP_SYNDICATION,po.HANDLE_SUBMIT_FOR_REVIEW]);d.useEffect(()=>{var i,h,D,$,X,Z,J,he,De,be,Ne,Me,st,yt;if(F(B),ks((B==null?void 0:B.length)>0),(B==null?void 0:B.length)>0&&C){Bt((i=B==null?void 0:B[0])==null?void 0:i.id),w((h=B==null?void 0:B[0])==null?void 0:h.materialNumber),j(($=(D=B==null?void 0:B[0])==null?void 0:D.materialType)==null?void 0:$.code),dt(0),cs(P.BASIC_DATA),He((Z=(X=B==null?void 0:B[0])==null?void 0:X.views)!=null&&Z.length?(J=B==null?void 0:B[0])==null?void 0:J.views:[ge]);const jt=jh(K),Xs=zh(jt);let no=JSON.parse(JSON.stringify(Xs));o(Yh(no)),o(Hr({keyName:"selectedMaterialID",data:(he=B==null?void 0:B[0])==null?void 0:he.id})),(Ne=(be=K==null?void 0:K[(De=B==null?void 0:B[0])==null?void 0:De.id])==null?void 0:be.Tochildrequestheaderdata)!=null&&Ne.ChildRequestId&&o(Hr({keyName:"childRequestId",data:(yt=(st=K==null?void 0:K[(Me=B==null?void 0:B[0])==null?void 0:Me.id])==null?void 0:st.Tochildrequestheaderdata)==null?void 0:yt.ChildRequestId}))}},[B]),d.useEffect(()=>{var i,h,D;(i=B==null?void 0:B[0])!=null&&i.materialType&&(Rn((h=B==null?void 0:B[0])==null?void 0:h.materialType),Se({row:B[0]}),nr(B)&&(It(!1),Ke(!1))),B!=null&&B.length&&Ms((D=B==null?void 0:B.at(-1))==null?void 0:D.lineNumber),o(xo({keyName:"VarOrdUn",data:xp}))},[]),d.useEffect(()=>{const i=async()=>{var h,D;try{const $=await c(re,u==null?void 0:u.Region,"",(D=(h=K[xe])==null?void 0:h.Tochildrequestheaderdata)==null?void 0:D.MaterialGroupType,Y==null?void 0:Y.ATTRIBUTE_3);jo($)}catch($){r($)}};re&&(u!=null&&u.Region)&&xe&&(Y!=null&&Y.ATTRIBUTE_3)&&i()},[re,u==null?void 0:u.Region,xe,Y==null?void 0:Y.ATTRIBUTE_3]),d.useEffect(()=>{Re==="no"&&(Bs({}),Ae(null),Jt(null))},[Re]),d.useEffect(()=>{var i,h,D,$,X,Z,J,he,De,be,Ne,Me,st,yt;xe&&(Q!=null&&Q[P.BASIC_DATA])&&(i=K[xe])!=null&&i.headerData.refMaterialData&&!(($=(D=(h=K[xe])==null?void 0:h.payloadData)==null?void 0:D["Basic Data"])!=null&&$.basic)&&Vo((Z=(X=K[xe])==null?void 0:X.headerData)==null?void 0:Z.refMaterialData),(be=(De=(he=(J=K[xe])==null?void 0:J.payloadData)==null?void 0:he[P.CLASSIFICATION])==null?void 0:De.basic)!=null&&be.Classtype&&yp((yt=(st=(Me=(Ne=K[xe])==null?void 0:Ne.payloadData)==null?void 0:Me[P.CLASSIFICATION])==null?void 0:st.basic)==null?void 0:yt.Classtype,o)},[xe,Q]),d.useEffect(()=>{(de==null?void 0:de.length)===0&&Ke(!1)},[de]),d.useEffect(()=>{Ge&&(Ko(Ge==null?void 0:Ge.code,"extended"),Bs(i=>({...i,[V.SALES_ORG]:null})))},[Ge]),d.useEffect(()=>{var i,h,D,$,X,Z,J,he;if(xe&&((h=(i=K[xe])==null?void 0:i.headerData)!=null&&h.materialType)){let De=(D=K[xe])==null?void 0:D.headerData;if(Te&&Te[($=De==null?void 0:De.materialType)==null?void 0:$.code]&&((X=De==null?void 0:De.views)==null?void 0:X.length)<2){const be=(u==null?void 0:u.Region)==="EUR"?((J=Te[(Z=De==null?void 0:De.materialType)==null?void 0:Z.code])==null?void 0:J.filter(Ne=>Ne!==P.WAREHOUSE))||[]:Te[(he=De==null?void 0:De.materialType)==null?void 0:he.code]||[];Ut(be),He(be),ne({id:xe,field:"views",value:be})}}},[Te,xe,(ol=(sl=K[xe])==null?void 0:sl.headerData)==null?void 0:ol.materialType]),d.useEffect(()=>{Mt[V.SALES_ORG]&&(Ct(),Bs(i=>({...i,[V.DIST_CHNL]:null,[V.PLANT]:null})))},[Mt[V.SALES_ORG]]);const Vo=i=>{var D,$,X,Z;(X=Object.keys(($=(D=i==null?void 0:i.copyPayload)==null?void 0:D.payloadData["Basic Data"])==null?void 0:$.basic))==null||X.forEach(J=>{var De,be,Ne;let he=J==="Division"?u==null?void 0:u.Division:Lp(J,(Ne=(be=(De=i==null?void 0:i.copyPayload)==null?void 0:De.payloadData["Basic Data"])==null?void 0:be.basic)==null?void 0:Ne[J],Q["Basic Data"]);o(sh({materialID:xe,viewID:"Basic Data",itemID:"basic",keyName:J,data:he}))});let h=(Z=i==null?void 0:i.copyPayload)==null?void 0:Z.unitsOfMeasureData;if(h!=null&&h.length){let J=[];h==null||h.forEach(he=>{J.push({...he,id:(he==null?void 0:he.id)||J.length+1})}),o(oh({materialID:xe,data:J}))}},Ko=(i,h)=>{const D=X=>{os(Z=>({...Z,"Sales Org":!1})),(X==null?void 0:X.statusCode)===Vt.STATUS_200&&xt(h==="notExtended"?Z=>({...Z,"Sales Org":X.body}):Z=>({...Z,"Sales Org":(X==null?void 0:X.body.length)>0?X.body:[]}))},$=()=>{os(X=>({...X,"Sales Org":!1}))};os(X=>({...X,"Sales Org":!0})),Xe(`/${ve}/data/${h==="notExtended"?"getSalesOrgNotExtended":"getSalesOrgExtended"}?materialNo=${i}&region=${u==null?void 0:u.Region}`,"get",D,$)},pe=(i,h,D)=>{os(J=>({...J,Plant:!0}));const $=J=>{os(he=>({...he,Plant:!1})),(J==null?void 0:J.statusCode)===Vt.STATUS_200&&xt(h==="notExtended"?he=>({...he,Plant:J.body}):he=>({...he,Plant:(J==null?void 0:J.body.length)>0?J.body:[]}))},X=()=>{os(J=>({...J,Plant:!1}))},Z=D?`&salesOrg=${D.code}`:"";Xe(`/${ve}/data/${h==="notExtended"?"getPlantNotExtended":"getPlantExtended"}?materialNo=${i}&region=${u==null?void 0:u.Region}${Z}`,"get",$,X)},ht=(i,h,D)=>{os(J=>({...J,Warehouse:!0}));const $=J=>{os(he=>({...he,Warehouse:!1})),(J==null?void 0:J.statusCode)===Vt.STATUS_200&&xt(h==="notExtended"?he=>({...he,Warehouse:J.body}):he=>({...he,Warehouse:(J==null?void 0:J.body.length)>0?J.body:[]}))},X=()=>{os(J=>({...J,Warehouse:!1}))},Z=D?`&plant=${D.code}`:"";Xe(`/${ve}/data/${h==="notExtended"?"getWarehouseNotExtended":"getWarehouseExtended"}?materialNo=${i}&region=${u==null?void 0:u.Region}${Z}`,"get",$,X)},ft=(i,h,D)=>{os(Z=>({...Z,"Storage Location":!0}));const $=Z=>{os(J=>({...J,"Storage Location":!1})),(Z==null?void 0:Z.statusCode)===Vt.STATUS_200&&xt(J=>{var he;return{...J,[(he=V)==null?void 0:he.STORAGE_LOC]:Z.body||[]}})},X=Z=>{r(Z),os(J=>({...J,"Storage Location":!1}))};Xe(`/${ve}/data/getStorageLocationExtended?plant=${h==null?void 0:h.code}&materialNo=${i}&region=${u==null?void 0:u.Region}&salesOrg=${D==null?void 0:D.code}`,"get",$,X)},Ct=()=>{var D;os($=>({...$,"Distribution Channel":!0}));const i=$=>{os(X=>({...X,"Distribution Channel":!1})),($==null?void 0:$.statusCode)===Vt.STATUS_200&&xt(X=>{var Z;return{...X,[(Z=V)==null?void 0:Z.DIST_CHNL]:$.body&&($==null?void 0:$.body)}})},h=$=>{r($),os(X=>({...X,"Distribution Channel":!1}))};Xe(`/${ve}/data/getDistributionChannelExtended?materialNo=${Ge==null?void 0:Ge.code}&salesOrg=${(D=Mt[V.SALES_ORG])==null?void 0:D.code}`,"get",i,h)};d.useEffect(()=>{["Mrp Profile"].forEach(Jo),(B==null?void 0:B.length)===0&&(re===b.CREATE||re===b.CREATE_WITH_UPLOAD)&&le(!0),Ee()},[]),d.useEffect(()=>{var h,D,$,X,Z,J,he,De,be,Ne,Me,st,yt,jt,Xs,no,ns,vo,cn,dn,_n,In;Ys.current=Ue,Ue.some(Qo=>{var vn,Mn,On;return((vn=Qo==null?void 0:Qo.salesOrg)==null?void 0:vn.code)&&!((On=(Mn=Qo==null?void 0:Qo.dc)==null?void 0:Mn.value)!=null&&On.code)})?ds(!1):(D=(h=Ue[0])==null?void 0:h.salesOrg)!=null&&D.code&&((Z=(X=($=Ue[0])==null?void 0:$.dc)==null?void 0:X.value)!=null&&Z.code)&&((De=(he=(J=Ue[0])==null?void 0:J.plant)==null?void 0:he.value)!=null&&De.code)&&((Me=(Ne=(be=Ue[0])==null?void 0:be.sloc)==null?void 0:Ne.value)!=null&&Me.code||!((Xs=(yt=(st=K[xe])==null?void 0:st.headerData)==null?void 0:yt.views)!=null&&Xs.includes((jt=P)==null?void 0:jt.STORAGE)))&&((vo=(ns=(no=Ue[0])==null?void 0:no.warehouse)==null?void 0:ns.value)!=null&&vo.code||(u==null?void 0:u.Region)==="EUR"||!((In=(dn=(cn=K[xe])==null?void 0:cn.headerData)==null?void 0:dn.views)!=null&&In.includes((_n=P)==null?void 0:_n.WAREHOUSE)))?ds(!0):ds(!1)},[Ue]),d.useEffect(()=>{Ke(!0),It(!0)},[(nl=K[xe])==null?void 0:nl.headerData,(rl=K[xe])==null?void 0:rl.payloadData]),d.useEffect(()=>{var h;let i=(h=K[xe])==null?void 0:h.headerData;(i!=null&&i.Bom||i!=null&&i.sourceList||i!=null&&i.PIR)&&Oe(i)},[(il=(ll=K[xe])==null?void 0:ll.headerData)==null?void 0:il.Bom,(cl=(al=K[xe])==null?void 0:al.headerData)==null?void 0:cl.sourceList,(ul=(dl=K[xe])==null?void 0:dl.headerData)==null?void 0:ul.PIR]);const Oe=i=>{var X,Z;const h={Bom:"BOM",sourceList:"Source List",PIR:"PIR"},D=((Z=(X=Object.keys(h))==null?void 0:X.filter(J=>i==null?void 0:i[J]))==null?void 0:Z.map(J=>h[J]))||[],$=te==null?void 0:te.filter(J=>!(to!=null&&to.includes(J)));He([...$,...D]),ne({id:xe,field:yr.VIEWS,value:[...$,...D]})},At=(i,h="",D)=>new Promise(($,X)=>{var Ne;const Z=[{materialNo:i,requestNo:h||(m==null?void 0:m.requestId)}],J=Me=>{var st;(st=Me==null?void 0:Me.body)!=null&&st.length?(wt(!0),ee(`Duplicate material number ${Me.body[0].split("$^$")[0]} (${Me.body[0].split("$^$")[1]})`),ze("error"),$(!0)):$(!1)},he=Me=>{r(Me),$(!1)};let De=0;Object.keys(K).forEach((Me,st)=>{var yt,jt;(Me.includes("-")||/\d/.test(Me))&&((jt=(yt=K[Me])==null?void 0:yt.headerData)==null?void 0:jt.materialNumber)===i&&De++});let be=0;Object.keys(K).forEach(Me=>{var st,yt;(Me.includes("-")||/\d/.test(Me))&&((yt=(st=K[Me])==null?void 0:st.headerData)==null?void 0:yt.globalMaterialDescription)===D&&be++}),De>1?(wt(!0),ee(`${hn.DUPLICATE_MATERIAL}${i}`),ze("error"),$(!0)):be>1?(wt(!0),ee(`${hn.DUPLICATE_MATERIAL_DESCRIPTION}${D}`),ze("error"),$(!0)):Xe(`/${ve}${(Ne=We.MASS_ACTION)==null?void 0:Ne.MAT_NO_DUPLICATE_CHECK}`,"post",J,he,Z)}),Ht=async()=>{let i=[...de],h=!0;return _e(!0),Ls(Gh.VALIDATING_MATS),new Promise(async(D,$)=>{for(let Z=0;Z<(de==null?void 0:de.length);Z++){const J=de[Z],{missingFields:he,viewType:De,isValid:be,plant:Ne=[]}=je(J.id,(J==null?void 0:J.orgData)||[],!1,!1,!1);if(qs(Ne),be){let Me=!1;be&&(!C||J!=null&&J.isMatNoChanged)&&(Me=await At(J.materialNumber,C,J==null?void 0:J.globalMaterialDescription)),Me&&(h=!1),i=i==null?void 0:i.map(st=>st.id===J.id?{...st,validated:!Me}:st),o(Oo(i))}else{if(h=!1,i=i.map(Me=>Me.id===J.id?{...Me,validated:!1}:Me),o(Oo(i)),(he==null?void 0:he.length)>0){if(ze("error"),typeof he=="object"&&!Array.isArray(he)){const Me=Object.entries(he).map(([st,yt])=>`Combination ${st}: ${yt.join(", ")}`);ee(`Line No ${J.lineNumber} : Please fill all the Mandatory fields in ${De||""}: ${Me.join(" | ")}`)}else ee(`Line No ${J.lineNumber} : Please fill all the Mandatory fields in ${De||""}: ${he.join(", ")}`);wt(!0)}break}}h?D(!0):$(),_e(!1);const X=nr(i);Ke(!X),It(!X)})},qt=i=>{var h,D;if(i){let $=JSON.parse(JSON.stringify(((D=(h=K==null?void 0:K[xe])==null?void 0:h.headerData)==null?void 0:D.calledMrpCodes)||[]))||[];i.forEach((Z,J)=>{var he,De,be,Ne,Me,st,yt,jt,Xs;(he=Z==null?void 0:Z.mrpProfile)!=null&&he.code&&!((Me=(be=(De=K==null?void 0:K[xe])==null?void 0:De.headerData)==null?void 0:be.calledMrpCodes)!=null&&Me.includes((Ne=Z==null?void 0:Z.mrpProfile)==null?void 0:Ne.code))&&(Yt((yt=(st=Z==null?void 0:Z.plant)==null?void 0:st.value)==null?void 0:yt.code,(jt=Z==null?void 0:Z.mrpProfile)==null?void 0:jt.code),$.push((Xs=Z==null?void 0:Z.mrpProfile)==null?void 0:Xs.code))}),o($n({materialID:xe,keyName:"calledMrpCodes",data:$}));const X=de==null?void 0:de.map(Z=>Z.id===xe?{...Z,calledMrpCodes:$}:Z);o(Oo(X))}},Yt=(i,h,D)=>{var J;const $={mrpProfile:h},X=he=>{he.body[0]&&Object.keys(he==null?void 0:he.body[0]).filter(be=>he==null?void 0:he.body[0][be]).forEach(be=>{us(i,be,he==null?void 0:he.body[0][be],P.MRP)})},Z=he=>{r(he)};Xe(`/${ve}${(J=We.MASS_ACTION)==null?void 0:J.MRP_DEFAULT_VALUES}`,"post",X,Z,$)},us=(i,h,D,$)=>{o(sh({materialID:xe||"",keyName:h||"",data:D??null,viewID:$,itemID:i}))},Ee=()=>{let i={decisionTableId:null,decisionTableName:"MDG_MAT_REG_SORG_PLNT_CNTRY_STORE_WH_MAPPING",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":(u==null?void 0:u.Region)||Uo.US}],systemFilters:null,systemOrders:null,filterString:null};const h=$=>{var X,Z;if($.statusCode===Vt.STATUS_200){let J=(Z=(X=$==null?void 0:$.data)==null?void 0:X.result[0])==null?void 0:Z.MDG_MAT_REG_SORG_PLNT_CNTRY_STORE_WH_MAPPING;G(J);let he=[];J==null||J.map(be=>{let Ne={};Ne.code=be.MDG_MAT_SALES_ORG,Ne.desc=be.MDG_MAT_SALES_ORG_DESC,he.push(Ne)});const De=Object.values(he.reduce((be,Ne)=>{const Me=`${Ne.code}-${Ne.desc}`;return be[Me]=Ne,be},{}));xt(be=>({...be,"Sales Organization":De==null?void 0:De.sort((Ne,Me)=>Ne.code-Me.code)}))}},D=$=>{r($)};y.environment==="localhost"?Xe(`/${yo}${We.INVOKE_RULES.LOCAL}`,"post",h,D,i):Xe(`/${yo}${We.INVOKE_RULES.PROD}`,"post",h,D,i)},ct=(i,h,D,$)=>{var Z;let X=JSON.parse(JSON.stringify(Ys.current));if(i==="Sales Organization"){let J=(Z=ye==null?void 0:ye.filter(he=>he.MDG_MAT_SALES_ORG===(h==null?void 0:h.code)))==null?void 0:Z.map(he=>{let De={};return De.code=he.MDG_MAT_PLANT,De.desc=he.MDG_MAT_PLANT_DESC,De});J=J==null?void 0:J.filter((he,De,be)=>De===be.findIndex(Ne=>Ne.code===he.code)),X[D].plant.options=J==null?void 0:J.sort((he,De)=>he.code-De.code),X[D].plant.value=null,X[D].dc.value=null,X[D].sloc.value=null,X[D].salesOrg=h,X[D].warehouse.value=null}else if(i==="plant"){let J=ye==null?void 0:ye.filter(be=>{var Ne;return be.MDG_MAT_SALES_ORG===((Ne=$==null?void 0:$.salesOrg)==null?void 0:Ne.code)&&be.MDG_MAT_PLANT===(h==null?void 0:h.code)}),he=J==null?void 0:J.map(be=>{let Ne={};if(be.MDG_MAT_STORAGE_LOCATION)return Ne.code=be.MDG_MAT_STORAGE_LOCATION,Ne.desc=be.MDG_MAT_STORE_LOC_DESC,Ne}).filter(Boolean),De=J==null?void 0:J.map(be=>be.MDG_MAT_WAREHOUSE?{code:be.MDG_MAT_WAREHOUSE,desc:be.MDG_MAT_WAREHOUSE_DESC}:null).filter(Boolean);X[D].plant.value=h,X[D].sloc.value=null,X[D].warehouse.value=null,X[D].sloc.options=he==null?void 0:he.filter((be,Ne,Me)=>Ne===Me.findIndex(st=>st.code===be.code)),X[D].warehouse.options=De==null?void 0:De.filter((be,Ne,Me)=>Ne===Me.findIndex(st=>st.code===be.code))}Pt(X)};d.useEffect(()=>{nr(B)&&(B!=null&&B.length)||v||C?(e.setCompleted([!0,!0]),e==null||e.setIsAttachmentTabEnabled(!0)):(e.setCompleted([!0,!1]),e==null||e.setIsAttachmentTabEnabled(!1))},[B]);const ps=Es+10,Ts=()=>{var D,$;const i=mo(),h={id:i,included:!0,lineNumber:ps,industrySector:(D=nh)==null?void 0:D.DEFAULT_IND_SECTOR,materialType:(($=ce==null?void 0:ce.MatlType)==null?void 0:$.code)??"",materialNumber:(Ge==null?void 0:Ge.code)||"",globalMaterialDescription:"",Bom:!1,sourceList:!1,PIR:!1,views:"",orgData:"",validated:sa.default,withReference:Re};o(gi({materialID:i,data:h})),o(Oo([...de,h])),Ns($t+1),Ms(ps),ks(!0),Ke(!0),It(!0),He([ge]),Bt(i),j("")},No=()=>{le(!1),(u==null?void 0:u.RequestType)==="Create"?Ts():(u==null?void 0:u.RequestType)==="Change"&&Gt(!0)},Nn=()=>{Ze()},wn=(i="",h=!0)=>{var Z,J;const D={materialNo:i??"",salesOrg:((Z=ae==null?void 0:ae.uniqueSalesOrgList)==null?void 0:Z.map(he=>he.code).join("$^$"))||"",top:500,skip:h?0:fs,matlType:((J=Mt==null?void 0:Mt["Material Type"])==null?void 0:J.code)??""};os(he=>({...he,"Material No":!0}));const $=he=>{(he==null?void 0:he.statusCode)===Vt.STATUS_200&&(he!=null&&he.body)&&Kt(h?he==null?void 0:he.body:De=>[...De,...he==null?void 0:he.body]),rt(!1),os(De=>({...De,"Material No":!1}))},X=()=>{rt(!1),os(he=>({...he,"Material No":!1}))};rt(!0),Xe(`/${ve}/data/getSearchParamsMaterialNo`,"post",$,X,D)},Rn=i=>{const h=$=>{($==null?void 0:$.statusCode)===Vt.STATUS_200&&as($==null?void 0:$.body)},D=$=>{console.error($,"while fetching the validation data of material number")};Xe(`/${ve}/data/getNumberRangeForMaterialType?materialType=${i==null?void 0:i.code}`,"get",h,D)};d.useEffect(()=>{var i;(i=Mt==null?void 0:Mt["Material Type"])!=null&&i.code&&(wn(),Ae(null),Jt(null))},[(hl=Mt==null?void 0:Mt["Material Type"])==null?void 0:hl.code]);const Dn=((fl=Qt==null?void 0:Qt[0])==null?void 0:fl.External)==="X",ir=((gl=Qt==null?void 0:Qt[1])==null?void 0:gl.External)==="X",ar=Qt==null?void 0:Qt.some(i=>i.ExtNAwock==="X"),_=(i=>{const h=new Set;let D=null;i==null||i.forEach(X=>{X.External==="X"&&X.ExtNAwock==="X"?(h.add(`External Number Range: Allowed (${X.FromNumber}-${X.ToNumber})`),h.add("Ext W/O Check: Allowed")):X.External!=="X"&&X.ExtNAwock==="X"?(h.add("Internal Number Range: Allowed"),h.add("Ext W/O Check: Allowed")):X.External==="X"&&X.ExtNAwock!=="X"?(h.add(`External Number Range: Allowed (${X.FromNumber}-${X.ToNumber})`),D="Ext W/O Check: Not Allowed"):X.External!=="X"&&X.ExtNAwock!=="X"&&(h.add("Internal Number Range: Allowed"),D="Ext W/O Check: Not Allowed")});const $=Array.from(h);return D&&$.push(D),$.map((X,Z)=>n("div",{children:n(mt,{children:X})},Z))})(Qt);function j(i){var $;const h=(u==null?void 0:u.Region)||Uo.US;if(!z.some(X=>X[h]&&X[h][i])&&i)s(i,h);else if(!i)o(Br({}));else{const X=z==null?void 0:z.find(Z=>(Z==null?void 0:Z[h])&&(Z==null?void 0:Z[h][i]));X&&o(Br(($=X[h][i])==null?void 0:$.allfields))}i&&l(i)}const ne=i=>{const{id:h,field:D,value:$}=i;let X=de.map(Z=>Z.id===h?{...Z,[D]:$}:Z);Qe({...io,[D]:$}),D===yr.MATERIALTYPE&&(Rn($),He([ge]),Xh([Zt]),o($n({materialID:h,keyName:"views",data:[ge]})),o($n({materialID:h,keyName:"orgData",data:""})),X=X.map(Z=>Z.id===h?{...Z,orgData:"",Bom:!1,sourceList:!1,PIR:!1}:Z),j($==null?void 0:$.code)),D===yr.INCLUDED&&(nr(X)?(Ke(!1),It(!1)):(Ke(!0),It(!0))),D===yr.VIEWS&&(Ke(!0),It(!0)),F(X),o($n({materialID:h,keyName:D,data:$})),o(Oo(X))},Se=i=>{var h,D,$,X,Z,J,he,De,be,Ne,Me,st,yt;Bt(i.row.id),w(i.row.materialNumber),j((D=(h=i==null?void 0:i.row)==null?void 0:h.materialType)==null?void 0:D.code),Ut((u==null?void 0:u.Region)==="EUR"?((Z=Te[(X=($=i==null?void 0:i.row)==null?void 0:$.materialType)==null?void 0:X.code])==null?void 0:Z.filter(jt=>jt!==P.WAREHOUSE))||[]:Te[(he=(J=i==null?void 0:i.row)==null?void 0:J.materialType)==null?void 0:he.code]||[]),He((be=(De=i==null?void 0:i.row)==null?void 0:De.views)!=null&&be.length?(Ne=i.row)==null?void 0:Ne.views:[ge]),Pt((st=(Me=i==null?void 0:i.row)==null?void 0:Me.orgData)!=null&&st.length?(yt=i.row)==null?void 0:yt.orgData:[Zt]),dt(0),cs("Basic Data")},Fe=()=>{$s(!0)},Ze=()=>{$s(!1)},et=(i,h)=>{h==="backdropClick"||h==="escapeKeyDown"||Ws(!1)},St=()=>He(Lt||[ge]),gt=()=>{if(le(!1),Re==="yes")if(ke!=null&&ke.length){let i=[...de];ke==null||ke.forEach(h=>{var J,he;const D=mo();let $=JSON.parse(JSON.stringify(h));$!=null&&$.refMaterialData&&delete $.refMaterialData;let X=JSON.parse(JSON.stringify((J=K==null?void 0:K[h.id])==null?void 0:J.payloadData));$.id=D,$.lineNumber=ps,$.globalMaterialDescription="",$.materialNumber="",$.validated=sa.default,o(gi({materialID:D,data:$,payloadData:X})),i.push($),F(i),o(Oo(i)),Ns($t+1),Ms(ps),ks(!0),Ke(!0),It(!0);let Z=(he=K==null?void 0:K[h.id])==null?void 0:he.unitsOfMeasureData;if(Z!=null&&Z.length){let De=[];Z==null||Z.forEach(be=>{var Ne,Me,st;De.push({...be,eanUpc:"",eanCategory:"",length:"",width:"",height:"",volume:"",grossWeight:"",netWeight:"",eanCategory:(u==null?void 0:u.Region)===((Ne=Uo)==null?void 0:Ne.US)?be==null?void 0:be.EanCat:"",eanUpc:(be==null?void 0:be.EanCat)==="MB"&&(u==null?void 0:u.Region)===((Me=Uo)==null?void 0:Me.US)||(u==null?void 0:u.Region)===((st=Uo)==null?void 0:st.EUR)?"":be==null?void 0:be.EanUpc,id:(be==null?void 0:be.id)||De.length+1})}),o(oh({materialID:D,data:De}))}}),at([])}else Ge&&Xt();else No()},Xt=()=>{var $,X,Z,J,he,De,be;_e(!0);let i={material:Ge==null?void 0:Ge.code,wareHouseNumber:($=Mt==null?void 0:Mt.Warehouse)==null?void 0:$.code,storageLocation:(X=Mt==null?void 0:Mt["Storage Location"])==null?void 0:X.code,salesOrg:(Z=Mt==null?void 0:Mt["Sales Org"])==null?void 0:Z.code,distributionChannel:(J=Mt==null?void 0:Mt["Distribution Channel"])==null?void 0:J.code,valArea:(he=Mt==null?void 0:Mt.Plant)==null?void 0:he.code,plant:(De=Mt==null?void 0:Mt.Plant)==null?void 0:De.code};const h=Ne=>{var Me,st,yt,jt,Xs,no,ns,vo,cn,dn,_n,In,Qo,vn,Mn,On,Kn,Jn,Qn,Zn,ho,En,Zo,ln,er;if(_e(!1),Bs({}),Ne!=null&&Ne.body[0]){rh(Ne==null?void 0:Ne.body,u);let tr=[...de];const Bn=mo();let Os={};Os.id=Bn,Os.included=!0,Os.lineNumber=ps,Os.globalMaterialDescription="",Os.materialType={code:((Me=Ne.body[0])==null?void 0:Me.MatlType)||"",desc:((yt=(st=oe==null?void 0:oe.MatlType)==null?void 0:st.find(bo=>{var Mo;return bo.code===((Mo=Ne.body[0])==null?void 0:Mo.MatlType)}))==null?void 0:yt.desc)||""},Os.industrySector={code:((jt=Ne.body[0])==null?void 0:jt.IndSector)||"",desc:((no=(Xs=oe==null?void 0:oe.IndSector)==null?void 0:Xs.find(bo=>{var Mo;return bo.code===((Mo=Ne.body[0])==null?void 0:Mo.IndSector)}))==null?void 0:no.desc)||""},Os.materialNumber="",Os.Bom=((ns=Ne.body[0])==null?void 0:ns.Bom)||"",Os.Category=(vo=Ne.body[0])!=null&&vo.Category?{code:((cn=Ne.body[0])==null?void 0:cn.Category)||"",desc:((_n=(dn=Hp)==null?void 0:dn.find(bo=>{var Mo;return bo.code===((Mo=Ne.body[0])==null?void 0:Mo.Category)}))==null?void 0:_n.desc)||""}:"",Os.Uom=(In=Ne.body[0])!=null&&In.Uom?{code:((Qo=Ne.body[0])==null?void 0:Qo.Uom)||"",desc:((Mn=(vn=oe==null?void 0:oe.BaseUom)==null?void 0:vn.find(bo=>{var Mo;return bo.code===((Mo=Ne.body[0])==null?void 0:Mo.Uom)}))==null?void 0:Mn.desc)||""}:"",Os.Relation=(On=Ne.body[0])!=null&&On.Relation?{code:((Kn=Ne.body[0])==null?void 0:Kn.Relation)||"",desc:((Qn=(Jn=Bp)==null?void 0:Jn.find(bo=>{var Mo;return bo.code===((Mo=Ne.body[0])==null?void 0:Mo.Relation)}))==null?void 0:Qn.desc)||""}:"",Os.Usage=((Zn=Ne.body[0])==null?void 0:Zn.Usage)||"",Os.views=(En=(((ho=Ne.body[0])==null?void 0:ho.Views)||"").split(",").map(bo=>bo.trim()==="Storage"?P.STORAGE:bo.trim()))==null?void 0:En.filter(bo=>!Ui.includes(bo)),(u==null?void 0:u.Region)===((Zo=Uo)==null?void 0:Zo.EUR)&&(Os.views=((ln=Os==null?void 0:Os.views)==null?void 0:ln.filter(bo=>bo!==P.WAREHOUSE))||[]),Os.validated=sa.default,Os.withReference=Re,Os.refMaterialData=rh(Ne.body,u),o(gi({materialID:Bn,data:Os,payloadData:{}})),tr.push(Os),F(tr),o(Oo(tr)),He(Os==null?void 0:Os.views),Ns($t+1),Ms(ps),ks(!0),Ke(!0),It(!0),j((er=Ne.body[0])==null?void 0:er.MatlType),Bt(Bn)}else _e(!1),wt(!0),ee(hn.NO_MATERIAL_FOUND),ze("warning"),le(!0)},D=Ne=>{r(Ne),_e(!1),le(!0)};Bs({}),Ae(null),Jt(null),Xe(`/${ve}${(be=We.DATA)==null?void 0:be.GET_COPY_MATERIAL}`,"post",h,D,i)},tt=!ko.includes(e==null?void 0:e.requestStatus)||C&&!v,hs=i=>({hasFertRole:i.includes("CA-MDG-MRKTNG-FERT-EUR"),hasSalesRole:i.includes("CA-MDG-MRKTNG-SALES-EUR")}),eo=(i,h,D)=>{var Z;const{hasFertRole:$,hasSalesRole:X}=hs(f);if($&&!X&&(u==null?void 0:u.Region)===Uo.EUR)return(i==null?void 0:i.code)!=="FERT";if(!$&&X&&(u==null?void 0:u.Region)===Uo.EUR)return(i==null?void 0:i.code)==="FERT";if($&&X&&(u==null?void 0:u.Region)===Uo.EUR){const J=h[0];if(D===(J==null?void 0:J.id))return!1;const he=(Z=J==null?void 0:J.materialType)==null?void 0:Z.code;if(he==="FERT")return(i==null?void 0:i.code)!=="FERT";if(he)return(i==null?void 0:i.code)==="FERT"}return!1},Wt=(i,h)=>{var D;xi.fire({title:ie("Are you sure?"),text:ie("Changing the material type will reset all the field values entered!"),icon:"warning",showCancelButton:!0,confirmButtonColor:(D=$e.primary)==null?void 0:D.main,cancelButtonColor:$e.error.red,confirmButtonText:ie("Yes, do it!"),cancelButtonText:ie("Cancel"),reverseButtons:!0}).then($=>{$.isConfirmed&&(o(Up({materialId:h})),ne({id:h,field:"materialType",value:i}))})},so=[{field:"included",headerName:ie("Included"),flex:.5,align:"center",headerAlign:"center",renderCell:i=>n(Gn,{checked:i.row.included,disabled:tt,onChange:h=>ne({id:i.row.id,field:"included",value:h.target.checked})})},{field:"lineNumber",headerName:ie("Line Number"),flex:.5,editable:!0,align:"center",headerAlign:"center"},{field:"industrySector",headerName:ie("Industry Sector"),flex:.7,align:"center",headerAlign:"center",...re===b.CREATE||re===b.CREATE_WITH_UPLOAD?{renderCell:i=>{var h;return n(ro,{options:(oe==null?void 0:oe.IndSector)||[],value:i.row.industrySector||((h=nh)==null?void 0:h.DEFAULT_IND_SECTOR),onChange:D=>ne({id:i.row.id,field:"industrySector",value:D}),placeholder:ie("Select Industry Sector"),disabled:tt,minWidth:"90%",listWidth:235})}}:{editable:!1,renderCell:i=>{var h,D;return((D=(h=K==null?void 0:K[i.row.id])==null?void 0:h.headerData)==null?void 0:D.industrySector)||""}}},{field:"materialType",headerName:ie("Material Type"),flex:.7,align:"center",headerAlign:"center",...re===b.CREATE||re===b.CREATE_WITH_UPLOAD?{renderCell:i=>n(ro,{options:Ai||[],value:i.row.materialType,onChange:h=>{i.row.materialType?Wt(h,i.row.id):ne({id:i.row.id,field:"materialType",value:h})},placeholder:ie("Select Material Type"),disabled:tt,minWidth:"90%",listWidth:235,isOptionDisabled:h=>eo(h,de,i.row.id)})}:{editable:!1,renderCell:i=>{var h,D;return((D=(h=K==null?void 0:K[i.row.id])==null?void 0:h.headerData)==null?void 0:D.materialType)||""}}},{field:"materialNumber",headerName:ie("Material Number"),flex:.7,editable:!1,align:"center",headerAlign:"center",renderHeader:()=>R("span",{children:[ie("Material Number"),n("span",{style:{color:"red"},children:"*"})]}),renderCell:i=>{var Z;const[h,D]=d.useState({[(Z=i==null?void 0:i.row)==null?void 0:Z.id]:i.row.materialNumber}),$=i.row.id,X=J=>{const he=J.target.value.toUpperCase(),De=(u==null?void 0:u.Region)==="US"?he.replace(/[^A-Z0-9-]/g,"").replace(/-{2,}/g,"-"):he.replace(/[^A-Z0-9]/g,"");D(Ne=>({...Ne,[$]:De})),ne({id:i.row.id,field:"materialNumber",value:De});const be=de.map(Ne=>Ne.id===i.row.id?{...Ne,isMatNoChanged:!0,materialNumber:De}:Ne);o(Oo(be))};return n(_s,{title:_,arrow:!0,children:(u==null?void 0:u.RequestType)===b.CREATE||(u==null?void 0:u.RequestType)===b.CREATE_WITH_UPLOAD?n(Ln,{fullWidth:!0,placeholder:ie("ENTER MATERIAL NUMBER"),variant:"outlined",size:"small",name:"material number",value:h[$]||"",onChange:J=>{X(J)},sx:{"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:$e.black.dark,color:$e.black.dark}}},disabled:tt}):i.row.materialNumber})}},{field:"globalMaterialDescription",flex:.7,headerName:ie("Material Description"),renderHeader:()=>R("span",{children:[ie("Material Description"),n("span",{style:{color:"red"},children:"*"})]}),renderCell:i=>{var J,he;const[h,D]=d.useState({[(J=i==null?void 0:i.row)==null?void 0:J.id]:i.row.globalMaterialDescription}),$=i.row.id,X=De=>{const Ne=De.target.value.toUpperCase().replace(/[^A-Z0-9-]/g,"").slice(0,40);D(Me=>({...Me,[$]:Ne})),ne({id:i.row.id,field:"globalMaterialDescription",value:Ne})},Z=(((he=h[$])==null?void 0:he.length)||0)===40;return n(Le,{sx:{display:"flex",alignItems:"center",width:"100%"},children:n(_s,{title:h[$]||"",arrow:!0,placement:"top",children:n(Ln,{fullWidth:!0,variant:"outlined",size:"small",placeholder:ie("ENTER MATERIAL DESCRIPTION"),value:h[$]||"",onChange:X,inputProps:{maxLength:40},sx:{flexGrow:1,"& .MuiOutlinedInput-root":{"& fieldset":{borderColor:Z?$e.error.dark:void 0},"&:hover fieldset":{borderColor:Z?$e.error.dark:void 0},"&.Mui-focused fieldset":{borderColor:Z?$e.error.dark:void 0}}}})})})},align:"center",headerAlign:"center",editable:!1},{field:"Bom",headerName:ie("BOM"),flex:.3,align:"center",headerAlign:"center",renderCell:i=>{var h;return n(Gn,{checked:((h=i.row)==null?void 0:h.Bom)||!1,disabled:tt,onChange:D=>ne({id:i.row.id,field:"Bom",value:D.target.checked})})}},{field:"sourceList",headerName:ie("Source List"),flex:.3,align:"center",headerAlign:"center",renderCell:i=>{var h;return n(Gn,{checked:((h=i.row)==null?void 0:h.sourceList)||!1,disabled:tt,onChange:D=>ne({id:i.row.id,field:"sourceList",value:D.target.checked})})}},{field:"PIR",headerName:ie("PIR"),flex:.3,align:"center",headerAlign:"center",renderCell:i=>{var h;return n(Gn,{checked:((h=i.row)==null?void 0:h.PIR)||!1,disabled:tt,onChange:D=>ne({id:i.row.id,field:"PIR",value:D.target.checked})})}},{...re===b.CREATE||re===b.CREATE_WITH_UPLOAD?{field:"views",headerName:"",flex:.6,align:"center",headerAlign:"center",renderCell:i=>{var h,D,$;return R(Ss,{children:[n(Rt,{variant:"contained",size:"small",disabled:!((h=i==null?void 0:i.row)!=null&&h.materialType),onClick:()=>{var X,Z,J;ms(!0),Hs(i.row.id),He((Z=(X=i==null?void 0:i.row)==null?void 0:X.views)!=null&&Z.length?(J=i.row)==null?void 0:J.views:[ge])},children:ie("Views")}),n(Rt,{variant:"contained",disabled:!((($=(D=i==null?void 0:i.row)==null?void 0:D.views)==null?void 0:$.length)>1),size:"small",sx:{marginLeft:"4px"},onClick:()=>{var X,Z,J;Ws(!0),Hs(i.row.id),Pt((Z=(X=i==null?void 0:i.row)==null?void 0:X.orgData)!=null&&Z.length?(J=i.row)==null?void 0:J.orgData:[Zt])},children:ie("ORG Data")})]})}}:{}},{field:"action",headerName:ie("Action"),flex:.5,align:"center",headerAlign:"center",renderCell:i=>{let h=Dp(i==null?void 0:i.row);const D=async $=>{var Me,st,yt;$.stopPropagation();const{missingFields:X,viewType:Z,isValid:J,plant:he=[]}=je(i.row.id,((Me=i==null?void 0:i.row)==null?void 0:Me.orgData)||[],Dn,ir,ar);if(qs(he),X){if(ze("error"),typeof X=="object"&&!Array.isArray(X)){const jt=Object.entries(X).map(([Xs,no])=>`Combination ${Xs}: ${no.join(", ")}`);ee(`${ie("Line No")} ${i.row.lineNumber} : ${ie("Please fill all the Mandatory fields in")} ${Z||""}: ${jt.join(" | ")}`)}else ee(`${ie("Line No")} ${i.row.lineNumber} : ${ie("Please fill all the Mandatory fields in")} ${Z||""}: ${X.join(", ")}`);wt(!0)}let De=!1;J&&(!C||(st=i.row)!=null&&st.isMatNoChanged)&&(De=await At(i.row.materialNumber,C,(yt=i==null?void 0:i.row)==null?void 0:yt.globalMaterialDescription)),h=J&&!De?"success":"error";const be=de.map(jt=>jt.id===i.row.id?{...jt,validated:J&&!De}:jt);o(Oo(be));const Ne=nr(be);Ke(!Ne),It(!Ne)};return R(Ao,{direction:"row",alignItems:"center",sx:{marginLeft:"0.5rem",magrinRight:"0.5rem"},spacing:.5,children:[n(_s,{title:h==="success"?"Validated Successfully":ie(h==="error"?"Validation Failed":"Click to Validate"),children:n(xs,{onClick:D,color:h==="success"?"success":h==="error"?"error":"default",children:h==="error"?n(Pp,{}):n(hf,{})})}),!tt&&n(_s,{title:ie("Delete Row"),children:n(xs,{onClick:()=>{we({...M,data:i,isVisible:!0})},color:"error",children:n(Ar,{})})})]})}}],oo=(i,h)=>{var D;dt(h),cs(((D=i==null?void 0:i.target)==null?void 0:D.id)==="AdditionalKey"?"Additional Data":te==null?void 0:te[h])},Jo=i=>{const h={"Sales Org":"/getSalesOrg","Mrp Profile":"/getMRPProfile"},D=X=>{const Z=yn(X.body);xt(J=>({...J,[i]:Z}))},$=X=>console.error(X);Xe(`/${ve}/data${h[i]}`,"get",D,$)},Tn=i=>{Jh(i,te,ao,xe,Ue,o,Fn)},cr=i=>{kp(i,te,ao,xe,Ue,o,Fn,P)},vr=(i,h,D)=>($,X)=>{var Me,st,yt;let Z={},J="",he="";D==="Purchasing"||D==="Costing"?(Z={materialNo:h==null?void 0:h.Material,plant:h==null?void 0:h.Plant},he=h==null?void 0:h.Plant,J=`/${ve}/data/displayLimitedPlantData`):D==="Accounting"?(Z={materialNo:h==null?void 0:h.Material,valArea:h==null?void 0:h.ValArea},he=h==null?void 0:h.ValArea,J=`/${ve}/data/displayLimitedAccountingData`):D==="Sales"&&(Z={materialNo:h==null?void 0:h.Material,salesOrg:h==null?void 0:h.SalesOrg,distChnl:h==null?void 0:h.DistrChan},he=`${h==null?void 0:h.SalesOrg}-${h==null?void 0:h.DistrChan}`,J=`/${ve}/data/displayLimitedSalesData`);const De=jt=>{var Xs,no,ns;D==="Purchasing"||D==="Costing"?o(Fn({materialID:xe,viewID:D,itemID:h==null?void 0:h.Plant,data:(Xs=jt==null?void 0:jt.body)==null?void 0:Xs.SpecificPlantDataViewDto[0]})):D==="Accounting"?o(Fn({materialID:xe,viewID:D,itemID:h==null?void 0:h.ValArea,data:(no=jt==null?void 0:jt.body)==null?void 0:no.SpecificAccountingDataViewDto[0]})):D==="Sales"&&o(Fn({materialID:xe,viewID:D,itemID:`${h==null?void 0:h.SalesOrg}-${h==null?void 0:h.DistrChan}`,data:(ns=jt==null?void 0:jt.body)==null?void 0:ns.SpecificSalesDataViewDto[0]}))},be=()=>{};!((yt=(st=(Me=K==null?void 0:K[xe])==null?void 0:Me.payloadData)==null?void 0:st[D])!=null&&yt[he])&&Xe(J,"post",De,be,Z),I(X?i:null)},Xl=()=>Q&&nt&&(Q[nt]||nt==="Additional Data"||to!=null&&to.includes(nt))?nt==="Additional Data"?[n(sf,{disableCheck:v&&!ko.includes(e==null?void 0:e.requestStatus),materialID:xe,selectedMaterialNumber:ue})]:[n(tf,{disabled:v&&!ko.includes(e==null?void 0:e.requestStatus),selectedMaterialNumber:ue,materialID:xe,basicData:is,setBasicData:ss,dropDownData:it,basicDataTabDetails:Q[nt],allTabsData:Q,activeViewTab:nt,selectedViews:te,handleAccordionClick:vr,missingValidationPlant:kt,isDisplay:C||v})]:n(Ss,{}),Vr=i=>{var $,X;const h=((X=($=i==null?void 0:i.target)==null?void 0:$.value)==null?void 0:X.toUpperCase())||"";Jt(null),Dt(0),L&&clearTimeout(L);const D=setTimeout(()=>{wn(h,!0)},500);N(D)},Vl=(i,h)=>{const D=Ge==null?void 0:Ge.code,$=Re==="yes"?"extended":"notExtended";Bs(X=>({...X,[i]:h})),i==="Sales Org"&&h?pe(D,$,h):i==="Plant"&&h&&(ht(D,$,h),ft(D,h,Mt["Sales Org"]),Bs(X=>({...X,"Storage Location":null,Warehouse:null})))},Kl=(i,h,D)=>{i==="Sales Organization"&&(h?(Pt($=>$.map((X,Z)=>Z===D?{...X,salesOrg:h}:X)),Kr(h,D).then($=>{ct(i,h,D)})):ct(i,h,D))},Kr=(i,h,D="",$="")=>new Promise((X,Z)=>{os(be=>({...be,"Distribution Channel":{...be["Distribution Channel"],[h]:!0}}));let J={salesOrg:i==null?void 0:i.code};const he=be=>{os(Me=>({...Me,"Distribution Channel":{...Me["Distribution Channel"],[h]:!1}}));let Ne=JSON.parse(JSON.stringify(D||Ys.current));if(Ne[h].dc.options=yn(be.body),Pt(Ne),Ys.current=Ne,$){o($n({materialID:$==null?void 0:$.id,keyName:"orgData",data:Ne}));let Me=(de==null?void 0:de.length)||[JSON.parse(JSON.stringify($))],st=Me.findIndex(yt=>yt.id===($==null?void 0:$.id));Me[st].orgData=Ne,o(Oo(Me)),X({org:Ne,material:Me[st]})}else X(""),os(Me=>({...Me,"Distribution Channel":{...Me["Distribution Channel"],[h]:!1}}))},De=be=>{console.error(be),os(Ne=>({...Ne,"Distribution Channel":{...Ne["Distribution Channel"],[h]:!1}}))};Xe(`/${ve}/data/getDistrChan`,"post",he,De,J)}),Jl=(i,h)=>{let D=JSON.parse(JSON.stringify(Ue));D[h].dc.value=i,Pt(D)},Ql=i=>{let h=JSON.parse(JSON.stringify(Ue));h.splice(i,1),Pt(h)},Zl=(i,h)=>{let D=JSON.parse(JSON.stringify(Ue));D[h].sloc.value=i,Pt(D)},ei=(i,h)=>{let D=JSON.parse(JSON.stringify(Ue));D[h].mrpProfile=i,Pt(D)},ti=(i,h)=>{let D=JSON.parse(JSON.stringify(Ue));D[h].warehouse.value=i,Pt(D)},si=()=>{let i=JSON.parse(JSON.stringify(Ue));i.push(Zt),Pt(i)},oi=i=>{if(!(i!=null&&i.temp)||(i==null?void 0:i.temp)===(Ye==null?void 0:Ye.temp))return;_e(!0);let h={decisionTableId:null,decisionTableName:"MDG_MAT_ORGDATA_TEMPLATE_CONFIG",version:"v2",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":(u==null?void 0:u.Region)||Uo.US,"MDG_CONDITIONS.MDG_MAT_TEMPLATE":i.temp||""}],systemFilters:null,systemOrders:null,filterString:null};const D=X=>{var Z,J;if(X.statusCode===Vt.STATUS_200){_e(!1);let he=(J=(Z=X==null?void 0:X.data)==null?void 0:Z.result[0])==null?void 0:J.MDG_MAT_ORGDATA_TEMPLATE_CONFIG,De=[];he==null||he.forEach((be,Ne)=>{var no;let Me=JSON.parse(JSON.stringify(Zt));Me.salesOrg={},Me.salesOrg.code=be.MDG_MAT_SALES_ORG,Me.salesOrg.desc=be.MDG_MAT_SALES_ORG_DESC,Me.plant.value={},Me.plant.value.code=be.MDG_MAT_PLANT,Me.plant.value.desc=be.MDG_MAT_PLANT_DESC;let st=(no=ye==null?void 0:ye.filter(ns=>ns.MDG_MAT_SALES_ORG===be.MDG_MAT_SALES_ORG))==null?void 0:no.map(ns=>({code:ns.MDG_MAT_PLANT,desc:ns.MDG_MAT_PLANT_DESC}));st=st==null?void 0:st.filter((ns,vo,cn)=>vo===cn.findIndex(dn=>dn.code===ns.code)),Me.plant.options=st==null?void 0:st.sort((ns,vo)=>ns.code-vo.code);let yt=ye==null?void 0:ye.filter(ns=>ns.MDG_MAT_SALES_ORG===be.MDG_MAT_SALES_ORG&&ns.MDG_MAT_PLANT===be.MDG_MAT_PLANT),jt=yt==null?void 0:yt.map(ns=>({code:ns.MDG_MAT_STORAGE_LOCATION,desc:ns.MDG_MAT_STORE_LOC_DESC})),Xs=yt==null?void 0:yt.map(ns=>ns.MDG_MAT_WAREHOUSE?{code:ns.MDG_MAT_WAREHOUSE,desc:ns.MDG_MAT_WAREHOUSE_DESC}:null).filter(Boolean);be.MDG_MAT_STORAGE_LOCATION&&(Me.sloc.value={},Me.sloc.value.code=be.MDG_MAT_STORAGE_LOCATION,Me.sloc.value.desc=be.MDG_MAT_STORE_LOC_DESC),Me.sloc.options=jt,be.MDG_MAT_WAREHOUSE&&(Me.warehouse.value={},Me.warehouse.value.code=be.MDG_MAT_WAREHOUSE||"",Me.warehouse.value.desc=be.MDG_MAT_WAREHOUSE_DESC||""),Me.warehouse.options=Xs,De.push(Me)}),Ys.current=De,Pt(De),Jr(De,0)}},$=X=>{r(X),_e(!1)};y.environment==="localhost"?Xe(`/${yo}${We.INVOKE_RULES.LOCAL}`,"post",D,$,h):Xe(`/${yo}${We.INVOKE_RULES.PROD}`,"post",D,$,h)},Jr=async(i,h)=>{h<(i==null?void 0:i.length)&&(await Kr(i[h].salesOrg,h),h++,Jr(i,h))},ni=()=>{const i=M==null?void 0:M.data;F(de==null?void 0:de.filter(h=>{var D;return h.id!==((D=i==null?void 0:i.row)==null?void 0:D.id)})),o(Qh(i==null?void 0:i.row.id)),j(""),o(Oo(de==null?void 0:de.filter(h=>{var D;return h.id!==((D=i==null?void 0:i.row)==null?void 0:D.id)}))),de!=null&&de.length?de.filter(h=>{var D,$;return((D=h.params)==null?void 0:D.id)!==(($=i==null?void 0:i.row)==null?void 0:$.id)}).every(h=>h.validated)&&Ke(!1):Ke(!1),we({...M,isVisible:!1})};d.useEffect(()=>{var X,Z,J,he;const i=te==null?void 0:te.includes((X=P)==null?void 0:X.SALES),h=te==null?void 0:te.includes((Z=P)==null?void 0:Z.SALES_PLANT),D=te==null?void 0:te.includes((J=P)==null?void 0:J.STORAGE),$=te==null?void 0:te.includes((he=P)==null?void 0:he.STORAGE_PLANT);i&&!h&&He(De=>{var Me,st;const be=[...De],Ne=be.indexOf((Me=P)==null?void 0:Me.SALES);return be.splice(Ne+1,0,(st=P)==null?void 0:st.SALES_PLANT),be}),D&&!$&&He(De=>{var Me,st;const be=[...De],Ne=be.indexOf((Me=P)==null?void 0:Me.STORAGE);return be.splice(Ne+1,0,(st=P)==null?void 0:st.STORAGE_PLANT),be})},[te]);const Qr=i=>{!i||!Array.isArray(i)||i.forEach(h=>{var D,$,X,Z,J,he,De,be,Ne,Me,st,yt,jt,Xs,no,ns;if(($=(D=h.plant)==null?void 0:D.value)!=null&&$.code){if(Fs((Z=(X=h.plant)==null?void 0:X.value)==null?void 0:Z.code,P.PLANT),(J=h.salesOrg)!=null&&J.code||(De=(he=h.dc)==null?void 0:he.value)!=null&&De.code){const vo=`${((be=h.salesOrg)==null?void 0:be.code)||""}-${((Me=(Ne=h.dc)==null?void 0:Ne.value)==null?void 0:Me.code)||""}`;Fs(vo,P.SALES)}(yt=(st=h.warehouse)==null?void 0:st.value)!=null&&yt.code&&Fs((Xs=(jt=h.warehouse)==null?void 0:jt.value)==null?void 0:Xs.code,P.WAREHOUSE),Wo((ns=(no=h.plant)==null?void 0:no.value)==null?void 0:ns.code)}})};d.useEffect(()=>{if(C){const i=gs==null?void 0:gs.orgData;(i==null?void 0:i.length)>0&&i.some(h=>{var D,$,X,Z,J;return(($=(D=h.plant)==null?void 0:D.value)==null?void 0:$.code)&&(((X=h.salesOrg)==null?void 0:X.code)||((J=(Z=h.dc)==null?void 0:Z.value)==null?void 0:J.code))})&&Qr(i)}},[gs==null?void 0:gs.orgData]);const Zr=i=>{o(gr(i)),k(i)};d.useEffect(()=>{var i,h;(O==null?void 0:O.page)!==0&&(re===((i=b)==null?void 0:i.CREATE_WITH_UPLOAD)||re===((h=b)==null?void 0:h.CREATE))&&p(),k((O==null?void 0:O.page)||0)},[O==null?void 0:O.page]);const ri=()=>{q(!S),A&&x(!1)},li=()=>{x(!A),S&&q(!1)};return R("div",{children:[n("div",{style:{padding:"0",width:"100%",margin:"0",marginTop:"20px"},children:R(Le,{sx:{position:S?"fixed":"relative",top:S?0:"auto",left:S?0:"auto",right:S?0:"auto",bottom:S?0:"auto",width:S?"100vw":"100%",height:S?"100vh":"auto",zIndex:S?1004:void 0,backgroundColor:S?"white":"transparent",padding:S?"20px":"0",display:"flex",flexDirection:"column",boxShadow:S?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[R(Le,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",backgroundColor:"#f5f5f5",borderRadius:"8px 8px 0 0"},children:[n(mt,{variant:"h6",children:ie("Material Data")}),R(Le,{sx:{display:"flex",alignItems:"center",gap:1},children:[R(Rt,{variant:"contained",color:"primary",size:"small",onClick:()=>{re===b.CREATE&&(le(!0),at([]),Ae(null),Bs({}),Kt([]))},disabled:W||tt,children:["+ ",ie("Add")]}),n(_s,{title:ie(S?"Exit Zoom":"Zoom In"),sx:{zIndex:"1009"},children:n(xs,{onClick:ri,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:S?n(Ea,{}):n(Ta,{})})})]})]}),C&&de&&(de==null?void 0:de.length)>0?n("div",{style:{width:"100%",height:"100%",overflowX:"auto"},children:n("div",{style:{height:"100%"},children:n(Ur,{rows:de,columns:so,pageSize:50,autoHeight:!1,page:H,rowCount:(O==null?void 0:O.totalElements)||0,rowsPerPageOptions:[50],onRowClick:Se,onCellEditCommit:ne,onPageChange:i=>Zr(i),pagination:!0,disableSelectionOnClick:!0,getRowClassName:i=>i.id===xe?"selected-row":"",style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%",height:S?"calc(100vh - 150px)":`${Math.min(de.length*50+130,300)}px`,overflow:"auto"},sx:{"& .selected-row":{backgroundColor:"rgb(234 233 255)"}}})})}):n(Ss,{children:n("div",{style:{width:"100%",height:"100%",overflowX:"auto"},children:n("div",{style:{height:"100%"},children:n(Ur,{autoHeight:!1,rows:de,columns:so,pageSize:50,page:H,rowsPerPageOptions:[50],onRowClick:Se,onCellEditCommit:ne,onPageChange:i=>Zr(i),disableSelectionOnClick:!0,getRowClassName:i=>i.id===xe?"selected-row":"",style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%",height:S?"calc(100vh - 150px)":`${Math.min(de.length*50+130,300)}px`,overflow:"auto"},sx:{"& .selected-row":{backgroundColor:"rgb(234 233 255)"}}})})})})]})}),re===b.CREATE||re===b.CREATE_WITH_UPLOAD||Y!=null&&Y.ATTRIBUTE_1?xe&&ls&&(de==null?void 0:de.length)>0&&(qe==null?void 0:qe.length)>0&&Q&&((pl=Object.getOwnPropertyNames(Q))==null?void 0:pl.length)>0&&R(Le,{sx:{position:A?"fixed":"relative",top:A?0:"auto",left:A?0:"auto",right:A?0:"auto",bottom:A?0:"auto",width:A?"100vw":"100%",height:A?"100vh":"auto",zIndex:A?1004:void 0,backgroundColor:A?"white":"transparent",padding:A?"20px":"0",marginTop:"20px",display:"flex",flexDirection:"column",boxShadow:A?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[R(Le,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",borderRadius:"8px 8px 0 0"},children:[n(mt,{variant:"h6",children:ie("View Details")}),n(_s,{title:ie(A?"Exit Zoom":"Zoom In"),sx:{zIndex:"1009"},children:n(xs,{onClick:li,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:A?n(Ea,{}):n(Ta,{})})})]}),R(Le,{sx:{flexGrow:1,display:"flex",flexDirection:"column"},children:[R(Bl,{value:Ce,onChange:oo,className:t.customTabs,"aria-label":"material tabs",sx:{top:0,position:"sticky",zIndex:1e3,backgroundColor:$e.background.container,borderBottom:`1px solid ${$e.border.light}`,"& .MuiTab-root":{minHeight:"48px",textTransform:"none",fontSize:"14px",fontWeight:600,color:$e.black.graphite,"&.Mui-selected":{color:$e.primary.main,fontWeight:700},"&:hover":{color:$e.primary.main,opacity:.8}},"& .MuiTabs-indicator":{backgroundColor:$e.primary.main,height:"3px"}},children:[te&&Ue.length>0&&(te==null?void 0:te.length)>0?te==null?void 0:te.map((i,h)=>n(Xn,{label:ie(i)},h)):n(Ss,{}),n(Xn,{label:ie("Additional Data"),id:"AdditionalKey"},"Additional data")]}),n(Le,{sx:{padding:2,marginTop:2,flexGrow:1,overflow:"auto",height:A?"calc(100vh - 180px)":"auto"},children:(de==null?void 0:de.length)>0&&Xl()}),(!tt||C&&!v||v&&ko.includes(e==null?void 0:e.requestStatus))&&n(Le,{sx:{borderTop:"1px solid #e0e0e0",padding:"16px"},children:n(Wr,{activeTab:Ce,submitForApprovalDisabled:!nr(B),filteredButtons:Xo,validateMaterials:Ht,workFlowLevels:qo,showWfLevels:Yo,childRequestHeaderData:(Tl=K==null?void 0:K[xe])==null?void 0:Tl.Tochildrequestheaderdata})})]})]}):n(Ss,{}),n(ki,{dialogState:Gs,openReusableDialog:Fe,closeReusableDialog:Ze,dialogTitle:"Warning",dialogMessage:ts,showCancelButton:!1,handleOk:Nn,handleDialogConfirm:Ze,dialogOkText:"OK",dialogSeverity:"danger"}),Nt&&n(un,{fullWidth:!0,maxWidth:!1,open:!0,onClose:et,sx:{display:"flex",justifyContent:"center"},disableEscapeKeyDown:!0,children:R(Le,{sx:{width:"600px !important"},children:[R(Un,{sx:{backgroundColor:"#EAE9FF",marginBottom:".5rem"},children:[n(Gr,{style:{height:"20px",width:"20px",marginBottom:"-5px"}}),n("span",{children:ie("Select Views")})]}),n(Lo,{sx:{paddingBottom:".5rem"},children:R(Le,{display:"flex",alignItems:"center",sx:{flex:1,padding:"22px 0px",gap:"5px"},children:[n(Ul,{size:"small",multiple:!0,fullWidth:!0,options:Lt||[],disabled:tt,disableCloseOnSelect:!0,value:(El=te==null?void 0:te.filter(i=>!Vh.includes(i)))==null?void 0:El.filter(i=>!(to!=null&&to.includes(i))),onChange:(i,h)=>{Y!=null&&Y.requestId||(He([ge,...h.filter(D=>D!==ge)]),ne({id:Ks,field:yr.VIEWS,value:h}))},getOptionDisabled:i=>i===ge,renderOption:(i,h,{selected:D})=>R("li",{...i,children:[n(Gn,{checked:D,sx:{marginRight:1}}),h]}),renderTags:(i,h)=>i.map((D,$)=>{const{key:X,...Z}=h({index:$});return n(mr,{label:D,...Z,disabled:D===ge||tt},X)}),renderInput:i=>n(Ln,{...i,label:ie("Select Views")})}),n(Rt,{variant:"contained",size:"small",onClick:()=>St(),disabled:tt,children:ie("Select all")})]})}),n(Do,{children:n(Rt,{onClick:()=>{ms(!1),ne({id:Ks,field:"views",value:te})},variant:"contained",children:ie("Ok")})})]})}),Js&&R(un,{fullWidth:!0,maxWidth:"xl",open:!0,onClose:et,disableEscapeKeyDown:!0,sx:{"& .MuiDialog-paper":{padding:2,borderRadius:2}},children:[R(Un,{sx:{display:"flex",alignItems:"center",gap:1,backgroundColor:"#EAE9FF"},children:[n(Gr,{fontSize:"small"}),n("span",{children:ie("Select Org Data")}),n(Le,{sx:{position:"absolute",right:"7%",width:"15%"},children:n(Ul,{options:ut.filter(i=>i.region===(u==null?void 0:u.Region)),value:Ye,size:"small",disabled:tt,isOptionEqualToValue:(i,h)=>i.region===h.region,onChange:(i,h)=>{lt(h),oi(h)},getOptionLabel:i=>i==null?void 0:i.temp,renderInput:i=>n(Ln,{...i,label:ie("Select Template"),sx:{minWidth:165}}),sx:{"& .MuiAutocomplete-popper":{minWidth:250}}})}),n(xs,{onClick:et,sx:{position:"absolute",right:15},children:n(Gl,{})})]}),n(Lo,{sx:{padding:0},children:n(Ia,{component:kr,children:R(va,{children:[n(Kh,{children:R(Hl,{children:[n(bs,{align:"center",children:ie("S NO.")}),n(bs,{align:"center",children:ie("Sales Org")}),n(bs,{align:"center",children:ie("Distribution Channel")}),n(bs,{align:"center",children:ie("Plant")}),n(bs,{align:"center",children:ie("Storage Location")}),(u==null?void 0:u.Region)!=="EUR"&&n(bs,{align:"center",children:ie("Warehouse")}),n(bs,{align:"center",children:ie("MRP Profile")}),Ue.length>1&&n(bs,{align:"center",children:ie("Action")})]})}),n(Ma,{children:Ue.map((i,h)=>{var D,$,X,Z,J,he,De,be,Ne,Me,st,yt,jt,Xs,no,ns,vo,cn,dn,_n,In,Qo,vn,Mn,On,Kn,Jn,Qn,Zn;return R(Hl,{sx:{padding:"12px",opacity:tt?.5:1,pointerEvents:tt?"none":"auto"},children:[n(bs,{children:n(mt,{variant:"body2",children:h+1})}),n(bs,{children:n(ro,{options:it["Sales Organization"],value:i.salesOrg,onChange:ho=>Kl("Sales Organization",ho,h),placeholder:ie("Select Sales Org"),minWidth:165,listWidth:215,title:((D=i==null?void 0:i.salesOrg)==null?void 0:D.code)+` - ${($=i==null?void 0:i.salesOrg)==null?void 0:$.desc}`})}),n(bs,{children:n(ro,{options:((X=i.dc)==null?void 0:X.options)||[],isLoading:((Z=uo["Distribution Channel"])==null?void 0:Z[h])||!1,value:(J=i.dc)==null?void 0:J.value,onChange:ho=>Jl(ho,h),placeholder:ie("Select DC"),disabled:!xl(yl.distributionChannel,te),minWidth:165,listWidth:215,title:((De=(he=i==null?void 0:i.dc)==null?void 0:he.value)==null?void 0:De.code)+` - ${(Ne=(be=i==null?void 0:i.dc)==null?void 0:be.value)==null?void 0:Ne.desc}`})}),n(bs,{children:n(ro,{options:((Me=i.plant)==null?void 0:Me.options)||[],value:(st=i.plant)==null?void 0:st.value,onChange:ho=>ct("plant",ho,h,i),placeholder:ie("Select Plant"),disabled:!xl(yl.plant,te),minWidth:165,listWidth:215,title:((jt=(yt=i==null?void 0:i.plant)==null?void 0:yt.value)==null?void 0:jt.code)+` - ${(no=(Xs=i==null?void 0:i.plant)==null?void 0:Xs.value)==null?void 0:no.desc}`})}),n(bs,{children:n(ro,{options:(ns=i==null?void 0:i.sloc)==null?void 0:ns.options,value:(vo=i==null?void 0:i.sloc)==null?void 0:vo.value,onChange:ho=>Zl(ho,h),placeholder:ie("Select Sloc"),disabled:!xl(yl.storage,te),minWidth:165,listWidth:215,title:((dn=(cn=i==null?void 0:i.sloc)==null?void 0:cn.value)==null?void 0:dn.code)+` - ${(In=(_n=i==null?void 0:i.sloc)==null?void 0:_n.value)==null?void 0:In.desc}`})}),(u==null?void 0:u.Region)!=="EUR"&&n(bs,{children:n(ro,{options:((Qo=i==null?void 0:i.warehouse)==null?void 0:Qo.options)||[],value:(vn=i==null?void 0:i.warehouse)==null?void 0:vn.value,onChange:ho=>ti(ho,h),disabled:!xl(yl.warehouse,te),placeholder:ie("Select Warehouse"),minWidth:165,listWidth:215,title:((On=(Mn=i==null?void 0:i.warehouse)==null?void 0:Mn.value)==null?void 0:On.code)+` - ${(Jn=(Kn=i==null?void 0:i.warehouse)==null?void 0:Kn.value)==null?void 0:Jn.desc}`})}),n(bs,{children:n(ro,{options:it["Mrp Profile"]||[],value:i.mrpProfile,onChange:ho=>ei(ho,h),placeholder:ie("Select MRP Profile"),disabled:!xl(yl.mrpProfile,te),isOptionDisabled:ho=>{var ln,er;if(h===0)return!1;const En=(er=(ln=Ue[h].plant)==null?void 0:ln.value)==null?void 0:er.code;if(!En)return!1;const Zo=Ue.slice(0,h).find(tr=>{var Bn,Os;return((Os=(Bn=tr.plant)==null?void 0:Bn.value)==null?void 0:Os.code)===En});return Zo&&Zo.mrpProfile?ho.code!==Zo.mrpProfile.code:!1},minWidth:165,listWidth:215,title:((Qn=i==null?void 0:i.mrpProfile)==null?void 0:Qn.code)+` - ${(Zn=i==null?void 0:i.mrpProfile)==null?void 0:Zn.desc}`})}),Ue.length>1&&R(bs,{align:"right",children:[n(xs,{size:"small",color:"primary",onClick:()=>{_o(!0),Io({orgRowLength:Ue.length,copyFor:h});const ho=Ue.filter(En=>{var Zo,ln;return(ln=(Zo=En.plant)==null?void 0:Zo.value)==null?void 0:ln.code}).map(En=>{var Zo,ln;return(ln=(Zo=En.plant)==null?void 0:Zo.value)==null?void 0:ln.code});Re==="yes"&&cr(ho)},style:{display:h===0?"none":"inline-flex"},children:n(bi,{})}),n(xs,{size:"small",color:"error",onClick:()=>Ql(h),children:n(Ar,{})})]})]},h)})})]})})}),R(Do,{sx:{justifyContent:"flex-end",gap:.5},children:[R(Rt,{onClick:si,variant:"contained",disabled:!co||tt,children:["+ ",ie("Add")]}),n(_s,{title:co?"":ie("Please fill all the fields of first row at least"),arrow:!0,children:n("span",{children:n(Rt,{onClick:()=>{var i,h;if(Ws(!1),(h=(i=Ue[0].plant)==null?void 0:i.value)!=null&&h.code){Qr(Ue),ne({id:Ks,field:"orgData",value:Ue}),qt(Ue);const D=de==null?void 0:de.map($=>$.id===xe?{...$,orgData:Ue}:$);if(o(Oo(D)),Re==="no"){const $=Ue.filter(X=>{var Z,J;return(J=(Z=X.plant)==null?void 0:Z.value)==null?void 0:J.code}).map(X=>{var Z,J;return(J=(Z=X.plant)==null?void 0:Z.value)==null?void 0:J.code});$.length>0&&Tn($)}lt(null)}},variant:"contained",disabled:!co||tt,tooltip:co?"":ie("Please fill all the fields of first row at least"),children:ie("Ok")})})})]})]}),Ps&&R(un,{fullWidth:!0,open:!0,maxWidth:"lg",sx:{"&::webkit-scrollbar":{width:"1px"}},children:[n(Un,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF",display:"flex"},children:n(mt,{variant:"h6",children:ie("Add New Material")})}),R(Lo,{sx:{padding:".5rem 1rem",alignItems:"center",justifyContent:"center",margin:"0px 25px"},children:[R(Li,{component:"fieldset",sx:{paddingBottom:"2%"},children:[n(qp,{component:"legend",sx:{padding:"15px 0px",fontWeight:"600",fontSize:"15px"},children:ie("Do You Want To Continue")}),R(Sa,{row:!0,"aria-label":"profit-center-number",name:"profit-center-number",value:Re,onChange:i=>Ve(i.target.value),children:[n(Sr,{value:"yes",control:n(qr,{}),label:ie("With Reference")}),n(Sr,{value:"no",control:n(qr,{}),label:ie("Without Reference")})]})]}),R(Rs,{container:!0,spacing:2,children:[n(Rs,{item:!0,xs:12,children:R(Rs,{container:!0,spacing:2,children:[n(Rs,{item:!0,xs:3,children:n(ro,{options:Ai||[],value:Mt[V.MATERIAL_TYPE]||"",onChange:i=>{Bs(h=>({...h,[V.MATERIAL_TYPE]:i}))},placeholder:ie("Select Material Type"),minWidth:180,listWidth:266,disabled:(ke==null?void 0:ke.length)||Re==="no",getOptionLabel:i=>i!=null&&i.desc?`${i.code} - ${i.desc}`:(i==null?void 0:i.code)||"",renderOption:(i,h)=>R("li",{...i,children:[n("strong",{children:h==null?void 0:h.code}),h!=null&&h.desc?` - ${h==null?void 0:h.desc}`:""]})})}),n(Rs,{item:!0,xs:3,children:n(ro,{options:Us,value:rs||Ge,onChange:i=>{Ae(i),Jt(i),i||Vr(i)},minWidth:180,listWidth:266,placeholder:ie("Select Material"),disabled:(ke==null?void 0:ke.length)||Re==="no",getOptionLabel:i=>i!=null&&i.desc?`${i.code} - ${i.desc}`:(i==null?void 0:i.code)||"",renderOption:(i,h)=>R("li",{...i,children:[n("strong",{children:h==null?void 0:h.code}),h!=null&&h.desc?` - ${h==null?void 0:h.desc}`:""]}),handleInputChange:Vr,sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}},isLoading:uo["Material No"]})}),Qs==null?void 0:Qs.slice(0,2).map(i=>n(Rs,{item:!0,xs:3,children:n(ro,{options:(it==null?void 0:it[i])||[],value:Mt[i]||"",onChange:h=>{Vl(i,h)},placeholder:ie(`Select ${i}`),minWidth:180,listWidth:306,sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}},disabled:(ke==null?void 0:ke.length)||Re==="no",isLoading:uo[i]})},i))]})}),n(Rs,{item:!0,xs:12,children:R(Rs,{container:!0,spacing:2,alignItems:"center",children:[n(Rs,{item:!0,xs:3,children:n(ro,{options:(it==null?void 0:it[Qs[2]])||[],value:Mt[Qs[2]]||"",onChange:i=>{Bs(h=>({...h,[Qs[2]]:i}))},placeholder:ie(`Select ${Qs[2]}`),minWidth:180,listWidth:306,sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}},disabled:(ke==null?void 0:ke.length)||Re==="no",isLoading:uo["Distribution Channel"]===!0})}),Qs==null?void 0:Qs.slice(3).map(i=>n(Rs,{item:!0,xs:3,children:n(ro,{options:(it==null?void 0:it[i])||[],value:Mt[i]||"",onChange:h=>{Bs(D=>({...D,[i]:h}))},placeholder:ie(`Select ${i}`),minWidth:180,listWidth:306,sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}},disabled:(ke==null?void 0:ke.length)||Re==="no",isLoading:uo[i]})},i)),(B==null?void 0:B.length)>0&&R(Ss,{children:[n(Rs,{item:!0,xs:1,sx:{textAlign:"center"},children:n(mt,{variant:"body1",sx:{fontWeight:"bold",color:"gray"},children:"OR"})}),n(Rs,{item:!0,xs:3,children:n(ro,{options:B.map(i=>({...i,code:i.lineNumber,desc:""})),value:ke[0],onChange:i=>{at(i?[i]:[]),Bs({}),Ae(null),Jt(null)},minWidth:180,listWidth:266,placeholder:ie("Select Material Line Number"),disabled:(Ge==null?void 0:Ge.code)||Re==="no",getOptionLabel:i=>i!=null&&i.desc?`${i.code} - ${i.desc}`:(i==null?void 0:i.code)||"",renderOption:(i,h)=>R("li",{...i,children:[n("strong",{children:h==null?void 0:h.code}),h!=null&&h.desc?` - ${h==null?void 0:h.desc}`:""]}),sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}}})})]})]})})]})]}),R(Do,{sx:{display:"flex",justifyContent:"end"},children:[n(Rt,{sx:{width:"max-content",textTransform:"capitalize"},onClick:()=>le(!1),variant:"outlined",children:ie("Cancel")}),n(Rt,{className:"button_primary--normal",type:"save",disabled:!(ke!=null&&ke.length||Ge!=null&&Ge.code)&&Re==="yes",onClick:gt,variant:"contained",children:ie("Proceed")})]})]}),(M==null?void 0:M.isVisible)&&R(Pi,{isOpen:M==null?void 0:M.isVisible,titleIcon:n(Ar,{size:"small",color:"error",sx:{fontSize:"20px"}}),Title:ie("Delete Row")+"!",handleClose:()=>we({...M,isVisible:!1}),children:[n(Lo,{sx:{mt:2},children:ie(qn.DELETE_MESSAGE)}),R(Do,{children:[n(Rt,{variant:"outlined",size:"small",sx:{..._a},onClick:()=>we({...M,isVisible:!1}),children:ie(mi.CANCEL)}),n(Rt,{variant:"contained",size:"small",sx:{...qi},onClick:ni,children:ie(mi.DELETE)})]})]}),Zs&&n(gf,{open:Zs,onClose:()=>_o(!1),title:xa.COPY_ORG_DATA_VALES_HEADING,selectedMaterialPayload:ao,lengthOfOrgRow:Fo,materialID:xe,orgRows:Ue}),Ft&&n(Hn,{openSnackBar:Pe,alertMsg:Ft,alertType:vt,handleSnackBarClose:()=>wt(!1)}),n(An,{blurLoading:pt,loaderMessage:Tt}),n(Hi,{})]})},AA=d.forwardRef(function(t,r){return n(yh,{direction:"down",ref:r,...t})}),bA=({open:e,onClose:t,parameters:r,templateName:o,allDropDownData:c,name:s,onSearch:p})=>{var ze,Pe,wt,kt,qs,Ft;const[l,u]=d.useState({}),[re,ae]=d.useState({}),y=se(ee=>ee.request.salesOrgDTData),[O,K]=d.useState({}),[m,B]=d.useState(""),[ce,oe]=d.useState([]),[Q,f]=d.useState(!1),[Y,z]=d.useState(!1),[g,T]=d.useState("success"),[v,C]=d.useState(!1),[S,q]=d.useState(""),[A,x]=d.useState(""),[H,k]=d.useState(!1),[L,N]=d.useState("systemGenerated"),[U,I]=d.useState([]),[ge,te]=d.useState(""),[He,M]=d.useState(!1),we=se(ee=>ee.payload.payloadData),de=se(ee=>ee.request.requestHeader.requestId),F=se(ee=>ee.payload.dataLoading),[qe,W]=d.useState({}),[Ke,Ds]=d.useState(0),[Gt]=d.useState(200),[Gs,$s]=d.useState(0),[ts,lo]=d.useState({code:"",desc:""}),[Fs,Us]=d.useState(null),Kt=d.useRef(null),[fs,Dt]=d.useState({[V.MATERIAL_NUM]:!1,[V.PLANT]:!1,[V.SALES_ORG]:!1,[V.DIVISION]:!1,[V.DIST_CHNL]:!1,[V.WAREHOUSE]:!1,[V.STORAGE_LOC]:!1,[V.MRP_CTRLER]:!1}),[rs,Jt]=d.useState(null),[vs,rt]=d.useState(""),[Be,It]=d.useState(!1),$t=d.useRef(null),Ns=wr(),Ce=Ro(),[dt,ls]=d.useState(0),[ks,is]=d.useState(null),{customError:ss}=nn(),[it,xt]=d.useState([]),Es=r,Ms=Es==null?void 0:Es.map(ee=>({field:ee.key,headerName:ee.key,editable:!0,flex:2})),Qt=d.useCallback(ee=>{ee.preventDefault();const ue=(ee.clipboardData||window.clipboardData).getData("Text").trim().split(`
`).map((w,Te)=>{const je=w.split("	"),ie={id:Te+1};return Ms.forEach((ut,Ye)=>{ie[ut.field]=je[Ye]||""}),ie});xt(ue)},[]);d.useEffect(()=>{e||(u({}),W({}))},[e]),d.useEffect(()=>{if(dt===1)return document.addEventListener("paste",Qt),()=>{document.removeEventListener("paste",Qt)}},[dt,Qt]);const as=(ee,ye)=>{ls(ye),dt===1&&is("handlePasteMaterialData")},io=Lh(({className:ee,...ye})=>n(_s,{...ye,classes:{popper:ee}}),{target:"e1qkid610"})({[`& .${Na.tooltip}`]:{maxWidth:"none"}},""),Qe={convertJsonToExcel:()=>{let ee=[];Ms==null||Ms.forEach(ye=>{ye.headerName.toLowerCase()!=="action"&&!ye.hide&&ee.push({header:ye.headerName,key:ye.field})}),Ra({fileName:"Material Data",columns:ee,rows:it})}},Lt=(ee,ye)=>{Jt(ee.currentTarget),rt(ye),It(!0)},Ut=()=>{It(!1)},Nt=()=>{It(!0)},ms=()=>{It(!1)},Hs=!!rs?"custom-popover":void 0,nt=(ee,ye)=>{var G;u(ue=>({...ue,[ee]:ye})),ee===V.MATERIAL_TYPE&&(I([]),lo({code:"",desc:""}),Ds(0),(G=ye==null?void 0:ye[0])!=null&&G.code&&Re("",!0,ye)),ye.length>0&&K(ue=>({...ue,[ee]:""}))};d.useEffect(()=>{ae(Js(l)),Ce(wa(Js(l)))},[l]),d.useEffect(()=>{if(it){let ee=Ws(it);u(ee)}},[it]);const cs=(ee,ye)=>{var ue;const G=((ue=l[ee])==null?void 0:ue.length)===ye.length;u(w=>({...w,[ee]:G?[]:ye})),G||K(w=>({...w,[ee]:""}))},Js=ee=>{const ye={};for(const G in ee)ee.hasOwnProperty(G)&&(ye[G]=ee[G].map(ue=>ue.code).join(","));return ye},Ws=ee=>{const ye={};return ee.forEach(G=>{Object.keys(G).forEach(ue=>{ue!=="id"&&G[ue].trim()!==""&&(ye[ue]||(ye[ue]=[]),ye[ue].push({code:G[ue].trim()}))})}),ye},Zt=()=>{var w;x(ya.REPORT_LOADING),B(!0),t();let ee=((w=Pl[we==null?void 0:we.TemplateName])==null?void 0:w.map(Te=>Te.key))||[],ye={};dt===0?ye={materialDetails:[ee.reduce((Te,je)=>(Te[je]=re!=null&&re[je]?re==null?void 0:re[je]:"",Te),{})],templateHeaders:"",requestId:de,templateName:we!=null&&we.TemplateName?we.TemplateName:"",dtName:"MDG_MAT_CHANGE_TEMPLATE",version:"v4",rolePrefix:""}:ye={materialDetails:[ee.reduce((Te,je)=>(Te[je]=it.map(ie=>{var ut;return(ut=ie[je])==null?void 0:ut.trim()}).filter(ie=>ie!=="").join(",")||"",Te),{})],templateHeaders:"",requestId:de,templateName:we!=null&&we.TemplateName?we.TemplateName:"",dtName:"MDG_MAT_CHANGE_TEMPLATE",version:"v4",rolePrefix:""};const G=Te=>{const je=URL.createObjectURL(Te),ie=document.createElement("a");ie.href=je,ie.setAttribute("download",`${we.TemplateName}_Mass Change.xlsx`),document.body.appendChild(ie),ie.click(),document.body.removeChild(ie),URL.revokeObjectURL(je),B(!1),x(""),z(!0),q(`${we.TemplateName}_Mass Change.xlsx has been downloaded successfully.`),T("success"),Ue(),setTimeout(()=>{Ns("/requestBench")},2400)},ue=()=>{B(!1)};Xe(`/${ve}${We.EXCEL.DOWNLOAD_EXCEL_WITH_DATA}`,"postandgetblob",G,ue,ye)},Ue=()=>{C(!0)},Pt=()=>{C(!1)},Ps=()=>{k(!1),N("systemGenerated")},le=ee=>{var ye;N((ye=ee==null?void 0:ee.target)==null?void 0:ye.value)},Ge=()=>{L==="systemGenerated"&&(Zt(),Ps()),L==="mailGenerated"&&Ps()};d.useEffect(()=>{var ee;(ee=l==null?void 0:l[V.MATERIAL_TYPE])!=null&&ee.code&&Re("",!0)},[]);const Ae=ee=>{var ue;const ye=(ue=ee.target.value)==null?void 0:ue.toUpperCase();lo({code:ye,desc:""}),Ds(0),Fs&&clearTimeout(Fs);const G=setTimeout(()=>{var w,Te,je;(Te=(w=l==null?void 0:l[V.MATERIAL_TYPE])==null?void 0:w[0])!=null&&Te.code&&Re(ye,!0,(je=l==null?void 0:l[V.MATERIAL_TYPE])==null?void 0:je.code)},500);Us(G)},Re=(ee="",ye=!1,G)=>{var je,ie,ut,Ye,lt,pt,_e;Dt(Tt=>({...Tt,[V.MATERIAL_NUM]:!0}));const ue={matlType:(((je=G==null?void 0:G[0])==null?void 0:je.code)||((ut=(ie=l==null?void 0:l[V.MATERIAL_TYPE])==null?void 0:ie[0])==null?void 0:ut.code))??"",materialNo:ee??"",top:Gt,skip:ye?0:Ke,salesOrg:((lt=(Ye=y==null?void 0:y.uniqueSalesOrgList)==null?void 0:Ye.map(Tt=>Tt.code))==null?void 0:lt.join("$^$"))||""},w=Tt=>{(Tt==null?void 0:Tt.statusCode)===Vt.STATUS_200&&($s((Tt==null?void 0:Tt.count)||0),ye?(I((Tt==null?void 0:Tt.body)||[]),W(Ls=>({...Ls,[V.MATERIAL_NUM]:Tt.body||[]}))):(I(Ls=>[...Ls,...(Tt==null?void 0:Tt.body)||[]]),W(Ls=>({...Ls,[V.MATERIAL_NUM]:[...Ls[V.MATERIAL_NUM]||[],...Tt.body||[]]}))),Dt(Ls=>({...Ls,[V.MATERIAL_NUM]:!1})))},Te=Tt=>{ss(Tt),Dt(Ls=>({...Ls,[V.MATERIAL_NUM]:!1}))};Xe(`/${ve}${(_e=(pt=We)==null?void 0:pt.DATA)==null?void 0:_e.GET_SEARCH_PARAMS_MATERIAL_NO}`,"post",w,Te,ue)};d.useEffect(()=>{Ke>0&&Re(ts==null?void 0:ts.code,!1)},[Ke]);const Ve=ee=>{var w;const{scrollTop:ye,scrollHeight:G,clientHeight:ue}=ee.target;ye+ue>=G-10&&!fs[V.MATERIAL_NUM]&&((w=qe==null?void 0:qe[V.MATERIAL_NUM])==null?void 0:w.length)<Gs&&Ds(Te=>Te+Gt)};d.useEffect(()=>{r==null||r.forEach(ee=>{var ye,G,ue,w;ee.key===((ye=V)==null?void 0:ye.MRP_CTRLER)&&(c!=null&&c.MrpCtrler)?W(Te=>{var je;return{...Te,[(je=V)==null?void 0:je.MRP_CTRLER]:c.MrpCtrler}}):[(G=V)==null?void 0:G.PLANT,(ue=V)==null?void 0:ue.SALES_ORG,(w=V)==null?void 0:w.WAREHOUSE].includes(ee.key)&&Bt(ee.key)})},[]),d.useEffect(()=>{at()},[]),d.useEffect(()=>{var ee;l[(ee=V)==null?void 0:ee.SALES_ORG]&&at()},[l[(ze=V)==null?void 0:ze.SALES_ORG]]),d.useEffect(()=>{var ee;l[(ee=V)==null?void 0:ee.PLANT]&&xe()},[l[(Pe=V)==null?void 0:Pe.PLANT]]);const ke=async()=>{p(l,"0",ee=>{oe(ee),f(!0),ee&&ee.length>0&&t()})},at=()=>{var ue;Dt(w=>({...w,[V.DIST_CHNL]:!0}));let ee={salesOrg:l[V.SALES_ORG]?(ue=l[V.SALES_ORG])==null?void 0:ue.map(w=>w==null?void 0:w.code).join("$^$"):""};const ye=w=>{W(Te=>({...Te,[V.DIST_CHNL]:w.body})),Ce(xo({keyName:"StoreLoc",data:qe==null?void 0:qe[V.DIST_CHNL]})),Dt(Te=>({...Te,[V.DIST_CHNL]:!1}))},G=w=>{console.error(w),Dt(Te=>({...Te,[V.DIST_CHNL]:!1}))};Xe(`/${ve}/data/getDistrChan`,"post",ye,G,ee)},xe=()=>{var ue,w;Dt(Te=>({...Te,[V.STORAGE_LOC]:!0})),l[V.SALES_ORG]&&((ue=l[V.SALES_ORG])==null||ue.map(Te=>Te==null?void 0:Te.code).join("$^$"));const ee=Te=>{W(je=>({...je,[V.STORAGE_LOC]:Te.body})),Ce(xo({keyName:"DistrChan",data:qe==null?void 0:qe[V.STORAGE_LOC]})),Dt(je=>({...je,[V.STORAGE_LOC]:!1}))},ye=Te=>{console.error(Te),Dt(je=>({...je,[V.STORAGE_LOC]:!1}))},G=(w=l[V.PLANT])==null?void 0:w.map(Te=>Te.code).join(",");Xe(`/${ve}${We.DATA.GET_STORAGE_LOCATION_SET_BASED_ON_PLANT}`,"post",ee,ye,{plant:G})},Bt=ee=>{Dt(w=>({...w,[ee]:!0}));const ye={[V.PLANT]:"/getPlant",[V.SALES_ORG]:"/getSalesOrg",[V.WAREHOUSE]:"/getWareHouseNo"},G=w=>{W(Te=>({...Te,[ee]:w.body})),Ce(xo({keyName:ee,data:w==null?void 0:w.body})),Dt(Te=>({...Te,[ee]:!1}))},ue=w=>{console.log(w),Dt(Te=>({...Te,[ee]:!1}))};Xe(`/${ve}/data${ye[ee]}`,"get",G,ue)},gs=ee=>{var G,ue;const ye=w=>w.code&&w.desc?`${w.code} - ${w.desc}`:w.code||"";if(ee.key===V.MATERIAL_TYPE)return n(ha,{param:ee,dropDownData:{[V.MATERIAL_TYPE]:ee.options},allDropDownData:c,selectedValues:l,inputState:ts,handleSelectAll:cs,handleSelectionChange:nt,dropdownRef:Kt,errors:O,formatOptionLabel:ye,handlePopoverOpen:Lt,handlePopoverClose:Ut,handleMouseEnterPopover:Nt,handleMouseLeavePopover:ms,isPopoverVisible:Be,popoverId:Hs,popoverAnchorEl:rs,popoverRef:$t,popoverContent:vs,isLoading:fs[ee.key],singleSelect:!0});if(ee.key===V.MATERIAL_NUM)return n(af,{param:ee,dropDownData:qe,allDropDownData:c,selectedValues:l,inputState:ts,handleSelectAll:cs,handleSelectionChange:nt,handleMatInputChange:Ae,handleScroll:Ve,dropdownRef:Kt,errors:O,formatOptionLabel:ye,handlePopoverOpen:Lt,handlePopoverClose:Ut,handleMouseEnterPopover:Nt,handleMouseLeavePopover:ms,isPopoverVisible:Be,popoverId:Hs,popoverAnchorEl:rs,popoverRef:$t,popoverContent:vs,isMaterialNum:!0,isLoading:fs[V.MATERIAL_NUM],hasMoreItems:Gs>(((G=qe==null?void 0:qe[V.MATERIAL_NUM])==null?void 0:G.length)||0),totalCount:Gs,loadedCount:((ue=qe==null?void 0:qe[V.MATERIAL_NUM])==null?void 0:ue.length)||0});if(ee.key===V.PLANT||ee.key===V.SALES_ORG||ee.key===V.MRP_CTRLER||ee.key===V.DIVISION||ee.key===V.WAREHOUSE||ee.key===V.DIST_CHNL||ee.key===V.STORAGE_LOC)return n(ha,{param:ee,dropDownData:qe,allDropDownData:c,selectedValues:l,inputState:ts,handleSelectAll:cs,handleSelectionChange:nt,dropdownRef:Kt,errors:O,formatOptionLabel:ye,handlePopoverOpen:Lt,handlePopoverClose:Ut,handleMouseEnterPopover:Nt,handleMouseLeavePopover:ms,isPopoverVisible:Be,popoverId:Hs,popoverAnchorEl:rs,popoverRef:$t,popoverContent:vs,isLoading:fs[ee.key]})},vt=()=>Object.values(l).some(ee=>Array.isArray(ee)&&ee.length>0);return R(Ss,{children:[R(un,{open:e,TransitionComponent:AA,keepMounted:!0,onClose:()=>{},maxWidth:s==="Extend"?"lg":"xs",fullWidth:!0,children:[R(Le,{sx:{backgroundColor:"#e3f2fd",padding:"1rem 1.5rem",display:"flex",alignItems:"center"},children:[n(La,{color:"primary",sx:{marginRight:"0.5rem"}}),R(mt,{variant:"h6",component:"div",color:"primary",children:[o," Search Filter(s)"]})]}),R(Bl,{value:dt,onChange:as,sx:{borderBottom:1,borderColor:"divider"},children:[n(Xn,{label:"Search Filter"}),s!=="Extend"&&n(Xn,{label:R(Le,{display:"flex",alignItems:"center",children:[n("span",{children:"Copy Material"}),dt===1&&n(_s,{title:"Export Table",children:n(xs,{sx:{padding:"4px",width:"28px",height:"28px"},onClick:Qe.convertJsonToExcel,children:n(Ti,{iconName:"IosShare"})})})]})})]}),R(Lo,{sx:{padding:"1.5rem 1.5rem 1rem"},children:[dt===0&&n(Ss,{children:n(Le,{sx:{display:"grid",gridTemplateColumns:"repeat(3, 1fr)",gap:2},children:r==null?void 0:r.map(ee=>n(Le,{sx:{marginBottom:"1rem"},children:gs(ee)},ee.key))})}),dt===1&&n(Le,{children:n(Ur,{style:{height:400,width:"100%"},rows:it,columns:Ms})}),He&&R(mt,{variant:"h6",color:(kt=(wt=$e)==null?void 0:wt.error)==null?void 0:kt.dark,children:["* ",ge]}),n(An,{blurLoading:F})]}),R(Do,{sx:{padding:"0.5rem 1.5rem",display:"flex",justifyContent:"space-between"},children:[n("div",{children:n(mt,{variant:"h6",color:(Ft=(qs=$e)==null?void 0:qs.error)==null?void 0:Ft.dark,children:Q&&(ce==null?void 0:ce.length)===0?hn.DATA_NOT_FOUND_FOR_SEARCH:""})}),R("div",{style:{display:"flex",gap:"8px"},children:[n(Rt,{onClick:t,color:"error",variant:"outlined",sx:{height:36,minWidth:"3.5rem",textTransform:"none",borderColor:"#cc3300",fontWeight:500},children:"Cancel"}),n(Rt,{onClick:ke,variant:"contained",disabled:!vt(),sx:{height:36,minWidth:"3.5rem",backgroundColor:"#3B30C8",textTransform:"none",fontWeight:500,"&:hover":{backgroundColor:"#2c278f"}},children:"OK"})]})]})]}),R(un,{open:H,onClose:Ps,children:[n(Un,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:n(mt,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:"Select Download Option"})}),n(Lo,{children:n(Li,{children:R(Sa,{row:!0,"aria-labelledby":"demo-row-radio-buttons-group-label",name:"row-radio-buttons-group",value:L,onChange:le,children:[n(io,{arrow:!0,placement:"bottom",title:n("span",{style:{whiteSpace:"nowrap",fontSize:"12px",overflow:"hidden",textOverflow:"ellipsis"},children:"Here Excel will be downloaded"}),children:n(Sr,{value:"systemGenerated",control:n(qr,{}),label:"System-Generated"})}),n(io,{arrow:!0,placement:"bottom",title:n("span",{style:{whiteSpace:"nowrap",fontSize:"12px",overflow:"hidden",textOverflow:"ellipsis"},children:"Here Excel will be sent to your email"}),children:n(Sr,{value:"mailGenerated",control:n(qr,{}),label:"Mail-Generated"})})]})})}),n(Do,{children:n(Rt,{variant:"contained",onClick:Ge,children:"OK"})})]}),n(An,{blurLoading:m,loaderMessage:A}),Y&&n(Hn,{openSnackBar:v,alertMsg:S,alertType:g,handleSnackBarClose:Pt})]})},SA=({openSearchMat:e,setOpenSearchMat:t,AddCopiedMaterial:r})=>{const[o,c]=d.useState(!1),s=se(u=>u.AllDropDown.dropDown),p={Extend:[{key:"Material Type",options:Ai},{key:"Material Number",options:[]},{key:"Plant",options:[]},{key:"Sales Org",options:[]},{key:"Distribution Channel",options:[]},{key:"Storage Location",options:[]},{key:"Division",options:[]}]},l=(u,re="0",ae)=>{var m,B,ce,oe,Q,f,Y,z,g,T,v,C;const y={materialNo:((B=(m=u==null?void 0:u["Material Number"])==null?void 0:m.map(S=>S.code))==null?void 0:B.join(","))??"",division:((oe=(ce=u==null?void 0:u.Division)==null?void 0:ce.map(S=>S.code))==null?void 0:oe.join(","))??"",plant:((f=(Q=u==null?void 0:u.Plant)==null?void 0:Q.map(S=>S.code))==null?void 0:f.join(","))??"",salesOrg:((z=(Y=u==null?void 0:u["Sales Org"])==null?void 0:Y.map(S=>S.code))==null?void 0:z.join(","))??"",distrChan:((T=(g=u==null?void 0:u["Distribution Channel"])==null?void 0:g.map(S=>S.code))==null?void 0:T.join(","))??"",storageLocation:((C=(v=u==null?void 0:u["Storage Location"])==null?void 0:v.map(S=>S.code))==null?void 0:C.join(","))??"",top:200,skip:re},O=S=>{var q;if((S==null?void 0:S.statusCode)===Vt.STATUS_200){const A=(q=S==null?void 0:S.body)==null?void 0:q.map(x=>{if(x.Views){const H=x.Views.split(",").map(k=>k.trim()).filter(k=>!Ui.includes(k)).join(",");return{...x,Views:H}}return x});r(A||[]),ae==null||ae(A||[]),c(!1)}},K=()=>{c(!1),ae==null||ae([])};c(!0),Xe(`/${ve}${We.DATA.GET_EXTEND_SEARCH_SET}`,"post",O,K,y)};return R(Ss,{children:[n(bA,{open:e,onClose:()=>t(!1),parameters:p.Extend,onSearch:(u,re,ae)=>l(u,re,ae),templateName:"Extend",name:"Extend",allDropDownData:s,buttonName:"Search"}),n(An,{blurLoading:o})]})};var rc={},NA=Go;Object.defineProperty(rc,"__esModule",{value:!0});var ba=rc.default=void 0,wA=NA(Bo()),vh=$o;ba=rc.default=(0,wA.default)([(0,vh.jsx)("path",{d:"M15.5 5H11l5 7-5 7h4.5l5-7z"},"0"),(0,vh.jsx)("path",{d:"M8.5 5H4l5 7-5 7h4.5l5-7z"},"1")],"DoubleArrow");const lc=(e,t,r,o)=>{const[c,s]=d.useState([]),[p,l]=d.useState([]),u=se(oe=>oe.payload.payloadData),ae=new URLSearchParams(location.search).get("RequestType"),y=se(oe=>{var Q,f;return(f=(Q=oe==null?void 0:oe.userManagement)==null?void 0:Q.taskData)==null?void 0:f.ATTRIBUTE_2}),[O,K]=d.useState(!1),{customError:m}=nn(),B=(u==null?void 0:u.RequestType)||y||ae;d.useEffect(()=>{B&&ce()},[e,B]);const ce=()=>{const oe={decisionTableId:null,decisionTableName:"MDG_MAT_DYN_BUTTON_CONFIG",version:"v3",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_DYN_BTN_MODULE_NAME":"Material","MDG_CONDITIONS.MDG_MAT_DYN_BTN_REQUEST_TYPE":B}],systemFilters:null,systemOrders:null,filterString:null},Q=z=>{var g,T;if(z.statusCode===Vt.STATUS_200){const v=Rr(jn.CURRENT_TASK,!0,{}),C=(e==null?void 0:e.taskDesc)||(v==null?void 0:v.taskDesc),q=(((T=(g=z==null?void 0:z.data)==null?void 0:g.result[0])==null?void 0:T.MDG_MAT_DYN_BUTTON_CONFIG)||[]).filter(x=>(x==null?void 0:x.MDG_MAT_DYN_BTN_TASK_NAME)===(C??ca.INITIATOR));s(q),(q.find(x=>x.MDG_MAT_DYN_BTN_BUTTON_NAME===o.SEND_BACK)||q.find(x=>x.MDG_MAT_DYN_BTN_BUTTON_NAME===o.CORRECTION))&&K(!0)}},f=z=>{m("Dynamic Button Fetch Error:",z)},Y=t.environment==="localhost"?`/${r}${We.INVOKE_RULES.LOCAL}`:`/${r}${We.INVOKE_RULES.PROD}`;Xe(Y,"post",Q,f,oe)};return d.useEffect(()=>{const oe=[...c].sort((Q,f)=>{const Y=Th[Q.MDG_MAT_DYN_BTN_BUTTON_NAME]??999,z=Th[f.MDG_MAT_DYN_BTN_BUTTON_NAME]??999;return Y-z});l(oe)},[c]),{extendFilteredButtons:p,showWfLevels:O}},RA=of(()=>({customTabs:{"& .MuiTabs-scroller":{overflowX:"auto !important",overflowY:"hidden !important"}}})),Cn={NOT_EXTENDED:"notExtended",EXTENDED:"Extended"},_A=e=>{var wn,Rn,Dn,ir,ar;const t=RA(),{customError:r}=nn(),o=Ro(),{fetchMaterialFieldConfig:c}=Da(),{getNextDisplayDataForCreate:s}=Gi(),{fetchValuationClassData:p}=ff(),l=se(E=>E.payload.payloadData),u=l==null?void 0:l.RequestType,re=se(E=>E.applicationConfig),ae=se(E=>E.paginationData),y=se(E=>E.payload),O=se(E=>E.request.materialRows),K=se(E=>{var _;return((_=E.AllDropDown)==null?void 0:_.dropDown)||{}}),m=se(E=>E.tabsData.allTabsData);let B=se(E=>E.userManagement.taskData);const ce=se(E=>E.tabsData.allMaterialFieldConfigDT),oe=on(),Q=new URLSearchParams(oe.search),f=Q.get("RequestId"),Y=Q.get("RequestType"),[z,g]=d.useState(0),[T,v]=d.useState(null),[C,S]=d.useState(null),q="Basic Data",[A,x]=d.useState([q]),[H,k]=d.useState([]),[L,N]=d.useState(O||[]),U=se(E=>E.selectedSections.selectedSections),[I,ge]=d.useState(!1),[te,He]=d.useState(!1),[M,we]=d.useState(""),[de,F]=d.useState([]),[qe,W]=d.useState(0),[Ke,Ds]=d.useState({code:"",desc:""}),[Gt,Gs]=d.useState(!1),{fetchDataAndDispatch:$s}=Bi(),[ts,lo]=d.useState(!0),[Fs,Us]=d.useState(L.length+1),[Kt,fs]=d.useState(0),[Dt,rs]=d.useState(O.length>0),[Jt,vs]=d.useState({}),[rt,Be]=d.useState({}),[It,$t]=d.useState([]),[Ns,Ce]=d.useState({}),[dt,ls]=d.useState([]),[ks,is]=d.useState(!1),[ss,it]=d.useState(""),[xt,Es]=d.useState("Basic Data"),[Ms,Qt]=d.useState(!1),[as,io]=d.useState(null),Qe=se(E=>E.request.salesOrgDTData),Lt=(wn=y==null?void 0:y[as])==null?void 0:wn.headerData,Ut=(l==null?void 0:l.Region)===Uo.EUR?{id:0,salesOrg:null,dc:{value:null,options:[]},plant:{value:null,options:[]},sloc:{value:null,options:[]},mrpProfile:null}:{id:0,salesOrg:null,dc:{value:null,options:[]},plant:{value:null,options:[]},sloc:{value:null,options:[]},warehouse:{value:null,options:[]},mrpProfile:null},[Nt,ms]=d.useState([Ut]),[Ks,Hs]=d.useState([]),[nt,cs]=d.useState({id:1,plant:{value:null,options:[]},salesOrg:null,dc:{value:null,options:[]},sloc:{value:null,options:[]},mrpProfile:null,warehouse:{value:null,options:[]}}),[Js,Ws]=d.useState(!1),[Zt,Ue]=d.useState({}),[Pt,Ps]=d.useState("success"),le=(Rn=y==null?void 0:y[as])==null?void 0:Rn.payloadData,[Ge,Ae]=d.useState(!1),[Re,Ve]=d.useState([]),[ke,at]=d.useState(""),[xe,Bt]=d.useState([]),{getDynamicWorkflowDT:gs}=Ha(),[vt,ze]=d.useState(!1),[Pe,wt]=d.useState(!1),[kt,qs]=d.useState(!1),[Ft,ee]=d.useState(""),[ye,G]=d.useState({"Sales Organization":!1,"Distribution Channel":{},Plant:{},"Storage Location":{},warehouse:{},"Mrp Profile":!1}),[ue,w]=d.useState(!1),[Te,je]=d.useState(0),{fetchTabSpecificData:ie}=pf(),{getContryBasedOnPlant:ut}=Tf({doAjax:Xe,customError:r,fetchDataAndDispatch:$s,destination_MaterialMgmt:ve}),{extendFilteredButtons:Ye,showWfLevels:lt}=lc(B,re,yo,fo),pt=Cr(Ye,[po.HANDLE_SUBMIT_FOR_APPROVAL,po.HANDLE_SAP_SYNDICATION,po.HANDLE_SUBMIT_FOR_REVIEW,po.HANDLE_SUBMIT]),_e=E=>{!E||!Array.isArray(E)||E.forEach(_=>{var j,ne,Se,Fe,Ze,et,St,gt,Xt,tt,hs,eo,Wt,so,oo,Jo;if((ne=(j=_.plant)==null?void 0:j.value)!=null&&ne.code){if(ie((Fe=(Se=_.plant)==null?void 0:Se.value)==null?void 0:Fe.code,P.PLANT),(Ze=_.salesOrg)!=null&&Ze.code||(St=(et=_.dc)==null?void 0:et.value)!=null&&St.code){const Tn=`${((gt=_.salesOrg)==null?void 0:gt.code)||""}-${((tt=(Xt=_.dc)==null?void 0:Xt.value)==null?void 0:tt.code)||""}`;ie(Tn,P.SALES)}(eo=(hs=_.warehouse)==null?void 0:hs.value)!=null&&eo.code&&ie((so=(Wt=_.warehouse)==null?void 0:Wt.value)==null?void 0:so.code,P.WAREHOUSE),ut((Jo=(oo=_.plant)==null?void 0:oo.value)==null?void 0:Jo.code)}})},Tt=E=>{if(!E||!Array.isArray(E))return[];let _=(l==null?void 0:l.Region)===Uo.EUR?E==null?void 0:E.filter(j=>j!==P.WAREHOUSE&&j!==P.WORKSCHEDULING&&j!==P.WORK_SCHEDULING):[...E];return _.sort((j,ne)=>j===P.BASIC_DATA?-1:ne===P.BASIC_DATA?1:0),_},Ls=async()=>{var E,_;try{const j=await gs(u,l==null?void 0:l.Region,"",(_=(E=y[as])==null?void 0:E.Tochildrequestheaderdata)==null?void 0:_.MaterialGroupType,B==null?void 0:B.ATTRIBUTE_3);Bt(j)}catch(j){r(j)}};d.useEffect(()=>{u&&(l!=null&&l.Region)&&as&&(B!=null&&B.ATTRIBUTE_3)&&Ls()},[u,l==null?void 0:l.Region,as,B==null?void 0:B.ATTRIBUTE_3]),d.useEffect(()=>{var E,_,j,ne,Se,Fe,Ze,et,St,gt,Xt,tt,hs,eo,Wt;if(N(O),rs((O==null?void 0:O.length)>0),(O==null?void 0:O.length)>0){io((E=O==null?void 0:O[0])==null?void 0:E.id),Bs(((j=(_=O==null?void 0:O[0])==null?void 0:_.materialType)==null?void 0:j.code)||((ne=O==null?void 0:O[0])==null?void 0:ne.materialType)),fs(0),ee((Se=O==null?void 0:O[0])==null?void 0:Se.materialNumber),Es(P.BASIC_DATA),x((Ze=(Fe=O==null?void 0:O[0])==null?void 0:Fe.views)!=null&&Ze.length?Tt((et=O==null?void 0:O[0])==null?void 0:et.views):Tt([q]));const so=jh(y),oo=zh(so);let Jo=JSON.parse(JSON.stringify(oo));o(Yh(Jo)),o(Hr({keyName:"selectedMaterialID",data:(St=O==null?void 0:O[0])==null?void 0:St.id})),(tt=(Xt=y==null?void 0:y[(gt=O==null?void 0:O[0])==null?void 0:gt.id])==null?void 0:Xt.Tochildrequestheaderdata)!=null&&tt.ChildRequestId&&o(Hr({keyName:"childRequestId",data:(Wt=(eo=y==null?void 0:y[(hs=O==null?void 0:O[0])==null?void 0:hs.id])==null?void 0:eo.Tochildrequestheaderdata)==null?void 0:Wt.ChildRequestId}))}},[O]),d.useEffect(()=>{(L==null?void 0:L.length)===0&&ge(!1)},[L]),d.useEffect(()=>{["Sales Organization","Mrp Profile"].forEach(Xo)},[]),d.useEffect(()=>{if(f){const E=Lt==null?void 0:Lt.orgData;(E==null?void 0:E.length)>0&&E.some(_=>{var j,ne,Se,Fe,Ze;return((ne=(j=_.plant)==null?void 0:j.value)==null?void 0:ne.code)&&(((Se=_.salesOrg)==null?void 0:Se.code)||((Ze=(Fe=_.dc)==null?void 0:Fe.value)==null?void 0:Ze.code))})&&_e(E)}},[Lt==null?void 0:Lt.orgData]),d.useEffect(()=>{var Se,Fe,Ze,et;const E=A==null?void 0:A.includes((Se=P)==null?void 0:Se.SALES),_=A==null?void 0:A.includes((Fe=P)==null?void 0:Fe.SALES_PLANT),j=A==null?void 0:A.includes((Ze=P)==null?void 0:Ze.STORAGE),ne=A==null?void 0:A.includes((et=P)==null?void 0:et.STORAGE_PLANT);E&&!_&&x(St=>{var tt,hs;const gt=[...St],Xt=gt.indexOf((tt=P)==null?void 0:tt.SALES);return gt.splice(Xt+1,0,(hs=P)==null?void 0:hs.SALES_PLANT),gt}),j&&!ne&&x(St=>{var tt,hs;const gt=[...St],Xt=gt.indexOf((tt=P)==null?void 0:tt.STORAGE);return gt.splice(Xt+1,0,(hs=P)==null?void 0:hs.STORAGE_PLANT),gt})},[A]);const Ys=()=>{Io()},co=(E="",_=!1)=>{var Fe,Ze,et,St;const j={materialNo:E??"",top:500,skip:_?0:qe,salesOrg:((Ze=(Fe=Qe==null?void 0:Qe.uniqueSalesOrgList)==null?void 0:Fe.map(gt=>gt.code))==null?void 0:Ze.join("$^$"))||""},ne=gt=>{(gt==null?void 0:gt.statusCode)===Vt.STATUS_200&&(F(_?gt==null?void 0:gt.body:Xt=>[...Xt,...gt==null?void 0:gt.body]),Gs(!1))},Se=()=>{Gs(!1)};Gs(!0),Xe(`/${ve}${(St=(et=We)==null?void 0:et.DATA)==null?void 0:St.GET_SEARCH_PARAMS_MATERIAL_NO}`,"post",ne,Se,j)},ds=!ko.includes(e==null?void 0:e.requestStatus),ao=E=>{const _=ne=>{(ne==null?void 0:ne.statusCode)===Vt.STATUS_200&&$t(ne==null?void 0:ne.body)},j=ne=>{console.error(ne,"while fetching the validation data of material number")};Xe(`/${ve}/data/getNumberRangeForMaterialType?materialType=${E==null?void 0:E.code}`,"get",_,j)};function Po(E){const _=ne=>{var Se;if((ne==null?void 0:ne.statusCode)===Vt.STATUS_200){let Fe=(Se=ne==null?void 0:ne.body)==null?void 0:Se.filter(Ze=>!Ui.includes(Ze));Fe=Fe==null?void 0:Fe.map(Ze=>Ze==="Storage"?P.STORAGE:Ze),(l==null?void 0:l.Region)===Uo.EUR&&(Fe=Fe==null?void 0:Fe.filter(Ze=>Ze!==P.WAREHOUSE&&Ze!==P.WORK_SCHEDULING&&Ze!==P.WORKSCHEDULING)),ls(Fe)}},j=ne=>{r(ne)};Xe(`/${ve}/data/getViewForMaterialType?materialType=${E}`,"get",_,j)}d.useEffect(()=>{co()},[]);const Qs=((Dn=It==null?void 0:It[1])==null?void 0:Dn.External)==="X",Mt=It==null?void 0:It.some(E=>E.ExtNAwock==="X");function Bs(E){var ne;const _=(l==null?void 0:l.Region)||Uo.US;if(!ce.some(Se=>Se[_]&&Se[_][E])&&E)c(E,_);else if(!E)o(Br({}));else{const Se=ce==null?void 0:ce.find(Fe=>(Fe==null?void 0:Fe[_])&&(Fe==null?void 0:Fe[_][E]));Se&&o(Br((ne=Se[_][E])==null?void 0:ne.allfields))}E&&p(E)}const Zs=E=>{const{id:_,field:j,value:ne}=E,Se=L.map(Fe=>Fe.id===_?{...Fe,[j]:ne}:Fe);Ce({...Ns,[j]:ne}),j===yr.MATERIALTYPE&&(ao(ne),Po(ne),x([q]),Xh([Ut]),o($n({materialID:_,keyName:"views",data:[q]})),o($n({materialID:_,keyName:"orgData",data:""})),Bs(ne==null?void 0:ne.code)),N(Se),o($n({materialID:_,keyName:j,data:ne}))},_o=E=>{var _,j,ne,Se,Fe,Ze,et,St,gt;io(E.row.id),Ue(E.row),ee(E.row.materialNumber),ls((_=E==null?void 0:E.row)==null?void 0:_.views),Bs(((ne=(j=E==null?void 0:E.row)==null?void 0:j.materialType)==null?void 0:ne.code)||((Se=E.row)==null?void 0:Se.materialType)),x((Fe=E==null?void 0:E.row)!=null&&Fe.views?(Ze=E.row)==null?void 0:Ze.views:[q]),ms((St=(et=E==null?void 0:E.row)==null?void 0:et.orgData)!=null&&St.length?(gt=E.row)==null?void 0:gt.orgData:[Ut]),fs(0),Es("Basic Data")},Fo=()=>{He(!0)},Io=()=>{He(!1)},uo=(E,_)=>{_==="backdropClick"||_==="escapeKeyDown"||Qt(!1)},os=()=>x(Tt(dt)),Wo=E=>{if(Ws(!1),E!=null&&E.length){let _=[...L];E==null||E.forEach(j=>{var Ze,et,St;const ne=j==null?void 0:j.Material;let Se={...j},Fe=(Ze=y==null?void 0:y[j.id])!=null&&Ze.payloadData?JSON.parse(JSON.stringify((et=y==null?void 0:y[j.id])==null?void 0:et.payloadData)):"";Se.id=ne,Se.globalMaterialDescription="",Se.materialNumber="",Se.included=!0,Se.industrySector=j==null?void 0:j.IndSector,Se.materialType=j==null?void 0:j.MatlType,Se.materialNumber=j==null?void 0:j.Material,Se.globalMaterialDescription=j==null?void 0:j.MaterialDescrption,Se.views=j!=null&&j.Views?(St=j==null?void 0:j.Views.split(","))==null?void 0:St.map(gt=>gt.trim()==="Storage"?P.STORAGE:gt.trim()):[q],Se.validated=!1,_.push(Se),o(gi({materialID:ne,data:Se,payloadData:Fe}))}),k(j=>[...j,..._.map(ne=>({material:ne==null?void 0:ne.Material,views:ne==null?void 0:ne.views}))]),N(_),o(Oo(_)),Us(Fs+1),rs(!0),lo(!0)}},to=[{field:"included",headerName:"Included",flex:.5,align:"center",headerAlign:"center",renderCell:E=>{var _;return E!=null&&E.row?n(Gn,{checked:(_=E==null?void 0:E.row)==null?void 0:_.included,disabled:ds,onChange:j=>{var ne;(ne=E==null?void 0:E.row)!=null&&ne.id&&Zs({id:E.row.id,field:"included",value:j.target.checked})}}):null}},{field:"lineNumber",headerName:"Line Number",flex:.6,editable:u==="Create",align:"center",headerAlign:"center",renderCell:E=>{const j=((O==null?void 0:O.findIndex(ne=>{var Se;return(ne==null?void 0:ne.id)===((Se=E==null?void 0:E.row)==null?void 0:Se.id)}))+1)*10;return n("div",{children:j})}},{field:"industrySector",headerName:"Industry Sector",flex:1,align:"center",headerAlign:"center",renderCell:E=>{var _,j,ne,Se,Fe;return n(ro,{options:(K==null?void 0:K.IndSector)||[],value:(_=E==null?void 0:E.row)==null?void 0:_.industrySector,onChange:Ze=>Zs({id:E.row.id,field:"industrySector",value:Ze}),placeholder:"Select Industry Sector",minWidth:"90%",disabled:!0,listWidth:232,title:`${((ne=(j=E.row)==null?void 0:j.industrySector)==null?void 0:ne.code)||""} - ${((Fe=(Se=E.row)==null?void 0:Se.industrySector)==null?void 0:Fe.desc)||""}`})}},{field:"materialType",headerName:"Material Type",flex:1,align:"center",headerAlign:"center",renderCell:E=>{var _,j,ne,Se,Fe;return n(ro,{options:Ai||[],value:(_=E==null?void 0:E.row)==null?void 0:_.materialType,onChange:Ze=>Zs({id:E.row.id,field:"materialType",value:Ze}),placeholder:"Select Material Type",disabled:!0,minWidth:"90%",listWidth:232,title:`${((ne=(j=E.row)==null?void 0:j.materialType)==null?void 0:ne.code)||""} - ${((Fe=(Se=E.row)==null?void 0:Se.materialType)==null?void 0:Fe.desc)||""}`})}},{field:"materialNumber",headerName:"Material Number",flex:u==="Extend"?.8:1,editable:!(!Qs&&!Mt),align:"center",headerAlign:"center",renderHeader:()=>R("span",{children:["Material Number",n("span",{style:{color:"red"},children:"*"})]}),renderCell:E=>{var _,j;return n(Ss,{children:(l==null?void 0:l.RequestType)===b.EXTEND?n(Ln,{fullWidth:!0,placeholder:"Enter Material Number",variant:"outlined",size:"small",name:"material number",value:(_=E==null?void 0:E.row)==null?void 0:_.materialNumber,sx:{"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:$e.black.dark,color:$e.black.dark}}},onChange:(ne,Se)=>Zs({id:E.row.id,field:"materialNumber",value:Se}),disabled:!Qs&&!Mt}):(j=E==null?void 0:E.row)==null?void 0:j.materialNumber})}},{field:"globalMaterialDescription",flex:u==="Extend"?.8:1,headerName:"Material Description",renderHeader:()=>R("span",{children:["Material Description",n("span",{style:{color:"red"},children:"*"})]}),renderCell:E=>{var _,j;return n(Ss,{children:(l==null?void 0:l.RequestType)===b.EXTEND?n(Ln,{fullWidth:!0,placeholder:"Enter Material Description",variant:"outlined",disabled:!0,size:"small",name:"material description",sx:{"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:$e.black.dark,color:$e.black.dark}}},onChange:(ne,Se)=>Zs({id:E.row.id,field:"globalMaterialDescription",value:Se}),value:(_=E==null?void 0:E.row)==null?void 0:_.globalMaterialDescription}):(j=E==null?void 0:E.row)==null?void 0:j.globalMaterialDescription})},align:"center",headerAlign:"center",editable:!0},{field:"views",headerName:"",flex:u==="Extend"?1.5:1,align:"center",headerAlign:"center",renderCell:E=>R(Ao,{direction:"row",spacing:0,alignItems:"center",children:[n(Rt,{variant:"contained",size:"small",sx:{minWidth:80},onClick:()=>{var _,j;Po(E.row.materialType),is(!0),it(E.row.id),Ue(E.row),x((_=E==null?void 0:E.row)!=null&&_.Views?(j=E==null?void 0:E.row)==null?void 0:j.Views:[q])},children:"Views"}),n(ba,{color:"disabled",fontSize:"small",sx:{mx:.5}}),n(Rt,{variant:"contained",size:"small",sx:{minWidth:100},onClick:()=>{var _,j,ne,Se;Qt(!0),it(E.row.id),ms((j=(_=E==null?void 0:E.row)==null?void 0:_.orgData)!=null&&j.length?(ne=E.row)==null?void 0:ne.orgData:[Ut]),Ue(E.row),qo((Se=E==null?void 0:E.row)==null?void 0:Se.materialNumber,Cn.NOT_EXTENDED),wt(!1)},children:"ORG Data"}),n(ba,{color:"disabled",fontSize:"small",sx:{mx:.5}}),n(_s,{title:"Click after changing Views or ORG Data",children:n(xs,{onClick:()=>{var _,j;Qt(!0),it(E.row.id),wt(!0),Ue(E.row),qo((_=E==null?void 0:E.row)==null?void 0:_.materialNumber,Cn.EXTENDED),cs(Ks.find(ne=>{var Se;return ne.id===((Se=E.row)==null?void 0:Se.id)})||{id:(j=E.row)==null?void 0:j.id,plant:null,salesOrg:null,dc:{value:null,options:[]},sloc:{value:null,options:[]},mrpProfile:null,warehouse:null})},disabled:ds,color:"primary",size:"small",children:n(bi,{})})})]})},{field:"action",headerName:"Action",flex:.9,align:"center",headerAlign:"center",renderCell:E=>n(Ao,{direction:"row",alignItems:"center",sx:{marginLeft:"0.5rem",magrinRight:"0.5rem"},spacing:.5,children:!f&&n(_s,{title:"Delete Row",children:n(xs,{onClick:()=>{N(L.filter(_=>_.id!==E.row.id)),o(Qh(E.row.id)),o(Oo(L.filter(_=>_.id!==E.row.id))),L!=null&&L.length||ge(!1)},color:"error",children:n(Ar,{})})})})}],qo=(E,_)=>{G(Se=>({...Se,"Sales Organization":!0}));const j=Se=>{if((Se==null?void 0:Se.statusCode)===Vt.STATUS_200){let Fe;_===Cn.NOT_EXTENDED?Fe=yn(Se.body):Fe=Se.body.length>0?yn(Se.body):[],Be(Ze=>({...Ze,"Sales Organization":Fe}))}G(Fe=>({...Fe,"Sales Organization":!1}))},ne=()=>{G(Se=>({...Se,"Sales Organization":!1}))};Xe(`/${ve}/data/${_===Cn.NOT_EXTENDED?"getSalesOrgNotExtended":"getSalesOrgExtended"}?materialNo=${E}&region=${l==null?void 0:l.Region}`,"get",j,ne)},jo=(E,_,j,ne)=>{G(et=>({...et,Plant:{...et.Plant,[ne]:!0}}));const Se=et=>{if((et==null?void 0:et.statusCode)===Vt.STATUS_200){let St;_===Cn.NOT_EXTENDED?St=yn(et.body):St=et.body.length>0?yn(et.body||[]):[],Be(gt=>({...gt,Plant:St}))}G(St=>({...St,Plant:{...St.Plant,[ne]:!1}}))},Fe=()=>{G(et=>({...et,Plant:{...et.Plant,[ne]:!1}}))},Ze=j?`&salesOrg=${j.code}`:"";Xe(`/${ve}/data/${_===Cn.NOT_EXTENDED?"getPlantNotExtended":"getPlantExtended"}?materialNo=${E}&region=${l==null?void 0:l.Region}${Ze}`,"get",Se,Fe)},zo=(E,_,j,ne)=>{G(et=>({...et,warehouse:{...et.warehouse,[ne]:!0}}));const Se=et=>{if((et==null?void 0:et.statusCode)===Vt.STATUS_200){let St;_===Cn.NOT_EXTENDED?St=yn(et.body):St=et.body.length>0?yn(et.body||[]):[],Be(gt=>({...gt,warehouse:St}))}G(St=>({...St,warehouse:{...St.warehouse,[ne]:!1}}))},Fe=()=>{G(et=>({...et,warehouse:{...et.warehouse,[ne]:!1}}))},Ze=j?`&plant=${j.code}`:"";Xe(`/${ve}/data/${_===Cn.NOT_EXTENDED?"getWarehouseNotExtended":"getWarehouseExtended"}?materialNo=${E}&region=${l==null?void 0:l.Region}${Ze}`,"get",Se,Fe)},Yo=(E,_)=>{var j;fs(_),Es(((j=E==null?void 0:E.target)==null?void 0:j.id)==="AdditionalKey"?"Additional Data":A==null?void 0:A[_])},Xo=E=>{G(Se=>({...Se,[E]:!0}));const _={"Sales Organization":"/getSalesOrg","Mrp Profile":"/getMRPProfile"},j=Se=>{if((Se==null?void 0:Se.statusCode)===Vt.STATUS_200){const Fe=yn(Se.body);Be(Ze=>({...Ze,[E]:Fe}))}G(Fe=>({...Fe,[E]:!1}))},ne=Se=>{console.error(Se),G(Fe=>({...Fe,[E]:!1}))};Xe(`/${ve}/data${_[E]}`,"get",j,ne)},Vo=E=>{Jh(E,A,le,as,Nt,o,Fn)},Ko=(E,_,j)=>(ne,Se)=>{var tt,hs,eo;let Fe={},Ze="",et="";j==="Purchasing"||j==="Costing"?(Fe={materialNo:_==null?void 0:_.Material,plant:_==null?void 0:_.Plant},et=_==null?void 0:_.Plant,Ze=`/${ve}/data/displayLimitedPlantData`):j==="Accounting"?(Fe={materialNo:_==null?void 0:_.Material,valArea:_==null?void 0:_.ValArea},et=_==null?void 0:_.ValArea,Ze=`/${ve}/data/displayLimitedAccountingData`):j==="Sales"&&(Fe={materialNo:_==null?void 0:_.Material,salesOrg:_==null?void 0:_.SalesOrg,distChnl:_==null?void 0:_.DistrChan},et=`${_==null?void 0:_.SalesOrg}-${_==null?void 0:_.DistrChan}`,Ze=`/${ve}/data/displayLimitedSalesData`);const St=Wt=>{var so,oo,Jo;(Wt==null?void 0:Wt.statusCode)===Vt.STATUS_200&&(j==="Purchasing"||j==="Costing"?o(Fn({materialID:as,viewID:j,itemID:_==null?void 0:_.Plant,data:(so=Wt==null?void 0:Wt.body)==null?void 0:so.SpecificPlantDataViewDto[0]})):j==="Accounting"?o(Fn({materialID:as,viewID:j,itemID:_==null?void 0:_.ValArea,data:(oo=Wt==null?void 0:Wt.body)==null?void 0:oo.SpecificAccountingDataViewDto[0]})):j==="Sales"&&o(Fn({materialID:as,viewID:j,itemID:`${_==null?void 0:_.SalesOrg}-${_==null?void 0:_.DistrChan}`,data:(Jo=Wt==null?void 0:Wt.body)==null?void 0:Jo.SpecificSalesDataViewDto[0]})))},gt=()=>{};!((eo=(hs=(tt=y==null?void 0:y[as])==null?void 0:tt.payloadData)==null?void 0:hs[j])!=null&&eo[et])&&Xe(Ze,"post",St,gt,Fe),S(Se?E:null)},pe=()=>m&&xt&&(m[xt]||xt==="Additional Data")?xt==="Additional Data"?[n(sf,{disabled:ds,materialID:as,selectedMaterialNumber:Ft})]:[n(tf,{disabled:ds,materialID:as,basicData:Jt,setBasicData:vs,dropDownData:rt,allTabsData:m,basicDataTabDetails:m[xt],activeViewTab:xt,selectedViews:A,handleAccordionClick:Ko,missingValidationPlant:Re,selectedMaterialNumber:Ft,callGetCountryBasedonSalesOrg:kt})]:n(Ss,{}),ht=E=>{const _=E.target.value;Ds({code:_,desc:""}),W(0),T&&clearTimeout(T);const j=setTimeout(()=>{co(_,!0)},500);v(j)};d.useEffect(()=>{qe>0&&co(Ke==null?void 0:Ke.code)},[qe]);const ft=(E,_,j)=>{var ne;if(E==="Sales Organization"){Ct(_,j);const Se=(ne=L==null?void 0:L.find(Fe=>Fe.id===ss))==null?void 0:ne.materialNumber;jo(Se,Pe?Cn.EXTENDED:Cn.NOT_EXTENDED,_,j)}},Ct=(E,_,j="",ne="")=>(G(Se=>({...Se,"Distribution Channel":{...Se["Distribution Channel"],[_]:!0}})),new Promise((Se,Fe)=>{var gt;const Ze=Xt=>{if(Xt.statusCode===Vt.STATUS_200){const tt=yn(Xt.body);let hs=JSON.parse(JSON.stringify(j||Nt));Pe?cs(eo=>({...eo,salesOrg:E,dc:{value:null,options:(tt==null?void 0:tt.length)>0?tt:[]}})):(hs[_].salesOrg=E,hs[_].dc.options=tt,ms(hs))}G(tt=>({...tt,"Distribution Channel":{...tt["Distribution Channel"],[_]:!1}})),Se(Xt)},et=Xt=>{G(tt=>({...tt,"Distribution Channel":{...tt["Distribution Channel"],[_]:!1}})),Fe(Xt)};let St=(gt=L==null?void 0:L.find(Xt=>Xt.id===ss))==null?void 0:gt.materialNumber;St&&Xe(`/${ve}/data/${Pe?"getDistributionChannelExtended":"getDistributionChannelNotExtended"}?materialNo=${St}&salesOrg=${E==null?void 0:E.code}`,"get",Ze,et)})),Oe=(E,_)=>{var ne;At(E,_);const j=(ne=L==null?void 0:L.find(Se=>Se.id===ss))==null?void 0:ne.materialNumber;zo(j,Pe?Cn.EXTENDED:Cn.NOT_EXTENDED,E,_)},At=(E,_,j="",ne)=>{var gt;G(Xt=>({...Xt,"Storage Location":{...Xt["Storage Location"],[_]:!0}}));const Se=Xt=>{if(G(tt=>({...tt,"Storage Location":{...tt["Storage Location"],[_]:!1}})),Xt.statusCode===Vt.STATUS_200){const tt=yn(Xt.body);let hs=JSON.parse(JSON.stringify(j||Nt));Pe?cs(eo=>({...eo,plant:{value:E,options:[]},sloc:{value:null,options:(tt==null?void 0:tt.length)>0?tt:[]}})):(hs[_].plant.value=E,hs[_].sloc.options=tt,ms(hs))}if(ne){o($n({materialID:ne==null?void 0:ne.id,keyName:"orgData",data:rowOption}));let tt=(L==null?void 0:L.length)||[JSON.parse(JSON.stringify(ne))],hs=tt.findIndex(eo=>eo.id===(ne==null?void 0:ne.id));tt[hs].orgData=rowOption,o(Oo(tt))}},Fe=Xt=>{console.error(Xt),G(tt=>({...tt,"Storage Location":{...tt["Storage Location"],[_]:!1}}))};let Ze=(gt=L.find(Xt=>Xt.id===ss))==null?void 0:gt.materialNumber;const et=Nt[_],St=et!=null&&et.salesOrg?`&salesOrg=${et.salesOrg.code}`:"";Ze&&Xe(`/${ve}/data/${Pe?"getStorageLocationExtended":"getStorageLocationNotExtended"}?materialNo=${Ze}&region=${l==null?void 0:l.Region}&plant=${E==null?void 0:E.code}${St}`,"get",Se,Fe)},Ht=(E,_)=>{let j=JSON.parse(JSON.stringify(Nt));j[_].dc.value=E,ms(j)},qt=E=>{let _=JSON.parse(JSON.stringify(Nt));_.splice(E,1),ms(_)},Yt=(E,_)=>{let j=JSON.parse(JSON.stringify(Nt));j[_].sloc.value=E,ms(j)},us=(E,_)=>{let j=JSON.parse(JSON.stringify(Nt));j[_].warehouse.value=E,ms(j)},Ee=(E,_)=>{let j=JSON.parse(JSON.stringify(Nt));j[_].mrpProfile=E,ms(j)},ct=()=>{let E=JSON.parse(JSON.stringify(Nt));E.push({id:1,plant:{value:null,options:[]},salesOrg:null,dc:{value:null,options:[]},sloc:{value:null,options:[]},warehouse:{value:null,options:[]},mrpProfile:null}),ms(E)},ps=(E,_,j,ne)=>{var et,St,gt,Xt,tt,hs,eo,Wt,so,oo,Jo;const Se={material:Zt==null?void 0:Zt.materialNumber,wareHouseNumber:((St=(et=E==null?void 0:E.warehouse)==null?void 0:et.value)==null?void 0:St.code)??"",plant:((Xt=(gt=E==null?void 0:E.plant)==null?void 0:gt.value)==null?void 0:Xt.code)??"",salesOrg:((tt=E==null?void 0:E.salesOrg)==null?void 0:tt.code)??"",storageLocation:((eo=(hs=E==null?void 0:E.sloc)==null?void 0:hs.value)==null?void 0:eo.code)??"",distributionChannel:((so=(Wt=E==null?void 0:E.dc)==null?void 0:Wt.value)==null?void 0:so.code)??"",valArea:((Jo=(oo=E==null?void 0:E.plant)==null?void 0:oo.value)==null?void 0:Jo.code)??""},Fe=Tn=>{const cr=Gp(Tn==null?void 0:Tn.body,_,j,ne,Zt),vr=u===b.EXTEND_WITH_UPLOAD||Y===b.EXTEND_WITH_UPLOAD?$p(y,cr):Fp(y,cr);o(Wp({data:vr})),qs(!kt)},Ze=Tn=>{r(Tn)};Xe(`/${ve}${We.DATA.COPY_FROM_MATERIAL_ORG_ELMS_ETEXTEND}`,"post",Fe,Ze,Se)},Ts=[{id:1,plant:{value:null,options:[]},salesOrg:null,dc:{value:null,options:[]},sloc:{value:null,options:[]},warehouse:{value:null,options:[]},mrpProfile:null}],No=E=>{o(gr(E)),g(E)};d.useEffect(()=>{var E,_;(ae==null?void 0:ae.page)!==0&&(u===((E=b)==null?void 0:E.EXTEND_WITH_UPLOAD)||u===((_=b)==null?void 0:_.EXTEND))&&s(),g((ae==null?void 0:ae.page)||0)},[ae==null?void 0:ae.page]);const Nn=Pe?Ts:Nt;return R("div",{children:[n("div",{style:{padding:"0",width:"100%",margin:"0"},children:R("div",{style:{height:300,width:"100%"},children:[n(Le,{sx:{display:"flex",justifyContent:"flex-end",marginBottom:"10px"},children:n(Rt,{variant:"contained",color:"primary",onClick:()=>{ze(!0)},disabled:I||ds||f&&(e==null?void 0:e.requestStatus)!==Co.DRAFT,children:"+ Add"})}),L&&(L==null?void 0:L.length)>0?n(Ss,{children:n("div",{style:{width:"100%",height:"100%",overflowX:"auto"},children:n("div",{style:{height:"100%"},children:n(Ur,{rows:L,columns:to,autoHeight:!1,pageSize:50,page:z,rowsPerPageOptions:[50],rowCount:(ae==null?void 0:ae.totalElements)||0,onRowClick:_o,onCellEditCommit:Zs,onPageChange:E=>No(E),disableSelectionOnClick:!0,getRowClassName:E=>E.id===as?"selected-row":"",style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%",height:`${Math.min(L.length*50+130,300)}px`,overflow:"auto"},sx:{"& .selected-row":{backgroundColor:"rgb(234 233 255)"}}})})})}):n(Ss,{children:n("div",{style:{width:"100%",height:"100%",overflowX:"auto"},children:n("div",{style:{height:"100%"},children:n(Ur,{rows:L,autoHeight:!1,columns:to,pageSize:5,rowsPerPageOptions:[5],page:z,onRowClick:_o,onCellEditCommit:Zs,onPageChange:E=>No(E),disableSelectionOnClick:!0,getRowClassName:E=>E.id===as?"selected-row":"",style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%",height:`${Math.min(L.length*50+130,300)}px`,overflow:"auto"},sx:{"& .selected-row":{backgroundColor:"rgb(234 233 255)"}}})})})})]})}),as&&Dt&&(L==null?void 0:L.length)>0&&U.length>0&&m&&((ir=Object.getOwnPropertyNames(m))==null?void 0:ir.length)>0&&R(Le,{sx:{marginTop:"45px"},children:[R(Bl,{sx:{top:0,position:"sticky",zIndex:1e3,backgroundColor:$e.background.container,borderBottom:`1px solid ${$e.border.light}`,"& .MuiTab-root":{minHeight:"48px",textTransform:"none",fontSize:"14px",fontWeight:600,color:$e.black.graphite,"&.Mui-selected":{color:$e.primary.main,fontWeight:700},"&:hover":{color:$e.primary.main,opacity:.8}},"& .MuiTabs-indicator":{backgroundColor:$e.primary.main,height:"3px"}},value:Kt,onChange:Yo,className:t.customTabs,"aria-label":"material tabs",children:[A&&Nt.length>0&&(A==null?void 0:A.length)>0?A==null?void 0:A.map((E,_)=>n(Xn,{label:E},_)):n(Ss,{}),n(Xn,{label:"Additional Data",id:"AdditionalKey"},"Additional data")]}),(L==null?void 0:L.length)>0&&n(Le,{sx:{padding:2,marginTop:2},children:pe()}),n(Wr,{activeTab:Kt,submitForApprovalDisabled:ts,filteredButtons:pt,workFlowLevels:xe,showWfLevels:lt,childRequestHeaderData:(ar=y==null?void 0:y[as])==null?void 0:ar.Tochildrequestheaderdata})]}),n("div",{}),n(ki,{dialogState:te,openReusableDialog:Fo,closeReusableDialog:Io,dialogTitle:"Warning",dialogMessage:M,showCancelButton:!1,handleOk:Ys,handleDialogConfirm:Io,dialogOkText:"OK",dialogSeverity:"danger"}),ks&&n(un,{fullWidth:!0,maxWidth:!1,open:!0,onClose:uo,sx:{display:"flex",justifyContent:"center"},disableEscapeKeyDown:!0,children:R(Le,{sx:{width:"600px !important"},children:[R(Un,{sx:{backgroundColor:"#EAE9FF",marginBottom:".5rem"},children:[n(Gr,{style:{height:"20px",width:"20px",marginBottom:"-5px"}}),n("span",{children:"Select Views"})]}),n(Lo,{sx:{paddingBottom:".5rem"},children:R(Le,{display:"flex",alignItems:"center",sx:{flex:1,padding:"22px 0px",gap:"5px"},children:[n(Ul,{size:"small",multiple:!0,fullWidth:!0,options:dt,disabled:ds,disableCloseOnSelect:!0,value:A==null?void 0:A.filter(E=>!Vh.includes(E)),onChange:(E,_)=>{x([q,..._.filter(j=>j!==q)]),Zs({id:ss,field:"views",value:_})},getOptionDisabled:E=>E===q,renderOption:(E,_,{selected:j})=>{var Fe;const ne=H.find(Ze=>(Ze==null?void 0:Ze.material)===(Zt==null?void 0:Zt.materialNumber)),Se=((Fe=ne==null?void 0:ne.views)==null?void 0:Fe.includes(_))||!1;return R("li",{...E,children:[n(Gn,{checked:j||_=="Basic Data",sx:{marginRight:1}}),_," ",Se?"(extended)":""]})},renderTags:(E,_)=>E.map((j,ne)=>{var St;const{key:Se,...Fe}=_({index:ne}),Ze=H.find(gt=>(gt==null?void 0:gt.material)===(Zt==null?void 0:Zt.materialNumber)),et=((St=Ze==null?void 0:Ze.views)==null?void 0:St.includes(j))||!1;return n(mr,{label:`${j} ${et?"(extended)":""}`,...Fe,disabled:j===q},Se)}),renderInput:E=>n(Ln,{...E,label:"Select Views"})}),n(Rt,{variant:"contained",disabled:ds,size:"small",onClick:()=>os(),children:"Select all"})]})}),R(Do,{children:[n(Rt,{onClick:()=>{is(!1)},color:"error",variant:"outlined",sx:{height:36,minWidth:"3.5rem",textTransform:"none",borderColor:"#cc3300",fontWeight:500},children:"Cancel"}),n(Rt,{onClick:()=>{is(!1),Zs({id:ss,field:"views",value:A})},variant:"contained",children:"OK"})]})]})}),Ms&&R(un,{fullWidth:!0,maxWidth:"xl",open:!0,onClose:uo,disableEscapeKeyDown:!0,sx:{"& .MuiDialog-paper":{padding:2,borderRadius:2}},children:[R(Un,{sx:{display:"flex",alignItems:"center",gap:1,backgroundColor:"#EAE9FF"},children:[n(Gr,{fontSize:"small"}),Pe?n("span",{children:"Select org data for copy"}):n("span",{children:"Select org data to be extended"}),n(xs,{onClick:uo,sx:{position:"absolute",right:15},children:n(Gl,{})})]}),n(Lo,{sx:{padding:0},children:n(Ia,{component:kr,children:R(va,{children:[n(Kh,{children:R(Hl,{children:[!Pe&&n(bs,{align:"center",children:"S NO."}),n(bs,{align:"center",children:"Sales Org"}),n(bs,{align:"center",children:"Distribution Channel"}),n(bs,{align:"center",children:"Plant"}),n(bs,{align:"center",children:"Storage Location"}),(l==null?void 0:l.Region)!==Uo.EUR&&n(bs,{align:"center",children:"Warehouse"}),n(bs,{align:"center",children:"MRP Profile"}),(Nt==null?void 0:Nt.length)>1&&!Pe&&n(bs,{align:"center",children:"Action"})]})}),n(Ma,{children:Nn==null?void 0:Nn.map((E,_)=>{var j,ne,Se,Fe,Ze,et,St,gt,Xt,tt,hs,eo;return R(Hl,{sx:{padding:"12px"},children:[!Pe&&n(bs,{children:n(mt,{variant:"body2",children:_+1})}),n(bs,{children:n(ro,{options:rt["Sales Organization"],value:Pe?nt==null?void 0:nt.salesOrg:E==null?void 0:E.salesOrg,onChange:Wt=>ft("Sales Organization",Wt,_),placeholder:"Select Sales Org",disabled:ds,isFieldError:!1,minWidth:165,isLoading:ye["Sales Organization"]})}),n(bs,{children:n(ro,{options:Pe?(ne=nt==null?void 0:nt.dc)==null?void 0:ne.options:(j=E.dc)==null?void 0:j.options,value:Pe?(Fe=nt==null?void 0:nt.dc)==null?void 0:Fe.value:(Se=E.dc)==null?void 0:Se.value,onChange:Wt=>Pe?cs(so=>{var oo;return{...so,dc:{value:Wt,options:(oo=nt==null?void 0:nt.dc)==null?void 0:oo.options}}}):Ht(Wt,_),placeholder:"Select DC",disabled:ds,isFieldError:!1,minWidth:165,isLoading:ye["Distribution Channel"][_]})}),n(bs,{children:n(ro,{options:rt.Plant||[],value:Pe?(et=nt==null?void 0:nt.plant)==null?void 0:et.value:(Ze=E.plant)==null?void 0:Ze.value,onChange:Wt=>Oe(Wt,_),placeholder:"Select Plant",disabled:ds,isFieldError:!1,minWidth:165,isLoading:ye.Plant[_]})}),n(bs,{children:n(ro,{options:Pe?(gt=nt==null?void 0:nt.sloc)==null?void 0:gt.options:(St=E==null?void 0:E.sloc)==null?void 0:St.options,value:Pe?(tt=nt==null?void 0:nt.sloc)==null?void 0:tt.value:(Xt=E==null?void 0:E.sloc)==null?void 0:Xt.value,onChange:Wt=>Pe?cs(so=>{var oo;return{...so,sloc:{value:Wt,options:(oo=nt==null?void 0:nt.sloc)==null?void 0:oo.options}}}):Yt(Wt,_),placeholder:"Select Sloc",disabled:ds,isFieldError:!1,minWidth:165,isLoading:ye["Storage Location"][_]})}),(l==null?void 0:l.Region)!==Uo.EUR&&n(bs,{children:n(ro,{options:rt.warehouse||[],value:Pe?(eo=nt==null?void 0:nt.warehouse)==null?void 0:eo.value:(hs=E==null?void 0:E.warehouse)==null?void 0:hs.value,onChange:Wt=>Pe?cs(so=>{var oo;return{...so,warehouse:{value:Wt,options:(oo=nt==null?void 0:nt.warehouse)==null?void 0:oo.options}}}):us(Wt,_),placeholder:"Select Warehouse",disabled:ds,isFieldError:!1,minWidth:165,isLoading:ye.warehouse[_]})}),n(bs,{children:n(ro,{options:rt["Mrp Profile"]||[],value:Pe?nt==null?void 0:nt.mrpProfile:E.mrpProfile,onChange:Wt=>Pe?cs(so=>({...so,mrpProfile:Wt})):Ee(Wt,_),placeholder:"Select MRP Profile",disabled:ds,isFieldError:!1,minWidth:165,isLoading:ye["Mrp Profile"]})}),Nt.length>1&&R(bs,{align:"right",children:[n(xs,{size:"small",color:"primary",disabled:ds,onClick:()=>{w(!0),je({orgRowLength:Nt.length,copyFor:_})},style:{display:_===0?"none":"inline-flex"},children:n(bi,{})}),n(xs,{style:{display:_===0?"none":"inline-flex"},size:"small",color:"error",onClick:()=>qt(_),children:n(Ar,{})})]})]},_)})})]})})}),R(Do,{sx:{justifyContent:"flex-end",gap:.5},children:[!Pe&&n(Rt,{onClick:ct,disabled:ds,variant:"contained",children:"+ Add"}),n(Rt,{onClick:()=>{if(Qt(!1),Nt[0].plant&&(Zs({id:ss,field:"orgData",value:Nt}),!Pe)){_e(Nt);const _=L==null?void 0:L.map(j=>(j==null?void 0:j.id)===ss?{...j,orgData:Nt}:j);o(Oo(_))}const E=Nt.filter(_=>{var j,ne;return(ne=(j=_.plant)==null?void 0:j.value)==null?void 0:ne.code}).map(_=>{var j,ne;return(ne=(j=_.plant)==null?void 0:j.value)==null?void 0:ne.code});E.length>0&&Vo(E),Pe&&(Hs(_=>{const j=_.findIndex(ne=>ne.id===nt.id);return j!==-1?_.map((ne,Se)=>Se===j?{...ne,...nt}:ne):[..._,nt]}),ps(nt,Nt,l,A))},variant:"contained",children:"Ok"})]})]}),ue&&n(gf,{open:ue,onClose:()=>w(!1),title:xa.COPY_ORG_DATA_VALES_HEADING,selectedMaterialPayload:le,lengthOfOrgRow:Te,materialID:as,orgRows:Nt}),ke&&n(Hn,{openSnackBar:Ge,alertMsg:ke,alertType:Pt,handleSnackBarClose:()=>Ae(!1)}),n(SA,{openSearchMat:vt,materialOptions:de,handleMatInputChange:ht,inputState:Ke,setOpenSearchMat:ze,dropDownData:rt,AddCopiedMaterial:Wo}),n(Hi,{})]})},IA=e=>{var o,c;const t=((c=(o=e==null?void 0:e.split("."))==null?void 0:o.pop())==null?void 0:c.toLowerCase())||"",r={fontSize:"small",sx:{mr:1}};switch(t){case"xlsx":case"xls":case"csv":return n(wT,{sx:{color:$e.icon.excel}});case"pdf":return n(Xp,{...r,sx:{color:$e.icon.pdf}});case"doc":case"docx":return n(ih,{...r,sx:{color:$e.icon.doc}});case"ppt":case"pptx":return n(ih,{...r,sx:{color:$e.icon.ppt}});default:return n(Yp,{...r,sx:{color:$e.icon.file}})}},vA=({attachmentsData:e=[],requestIdHeader:t="",pcNumber:r="",disabled:o=!1,requestStatus:c})=>{const[s,p]=d.useState({}),l=se(M=>M.appSettings),u=se(M=>M.payload.dynamicKeyValues),[re,ae]=d.useState([]),[y,O]=d.useState([]),K=Ro(),{t:m}=Sn(),B=se(M=>M.userManagement.taskData),ce=se(M=>M.applicationConfig),oe=se(M=>M.request.materialRows),[Q,f]=d.useState(!1),Y=se(M=>M.payload.payloadData),[z,g]=d.useState(""),T=on(),C=new URLSearchParams(T.search).get("reqBench"),S=T==null?void 0:T.state,{extendFilteredButtons:q}=lc(B,ce,yo,fo);let A;const x=[po.HANDLE_SEND_BACK,po.HANDLE_VALIDATE,po.HANDLE_CORRECTION,po.HANDLE_SUBMIT_FOR_APPROVAL,po.HANDLE_SAP_SYNDICATION,po.HANDLE_SUBMIT_FOR_REVIEW,po.HANDLE_SUBMIT];(Y==null?void 0:Y.RequestType)===b.EXTEND||(Y==null?void 0:Y.RequestType)===b.EXTEND_WITH_UPLOAD?A=Cr(q,x):A=[];const H=se(M=>M.payload.payloadData),k=se(M=>{var we,de;return(de=(we=M==null?void 0:M.payload)==null?void 0:we.dynamicKeyValues)==null?void 0:de.childRequestHeaderData}),L=()=>{f(!0)},N=()=>{f(!1)},U=!ko.includes(c)||t&&!C,I={primary:$e.blue.main,light:$e.text.offWhite,accent:$e.primary.grey,text:$e.text.charcoal,secondaryText:$e.text.greyBlue,background:$e.background.paper},ge=[{field:"id",headerName:"Document ID",flex:1.2,hideable:!1,hidden:!0},{field:"attachmentType",headerName:m("Attachment Type"),flex:1.5,renderCell:M=>{var we;return n(mr,{label:M.value,size:"small",sx:{backgroundColor:(we=$e)==null?void 0:we.reportTile.lightBlue,color:$e.primary.lightPlus,fontWeight:"medium"}})}},{field:"docName",headerName:m("Document Name"),flex:2,renderCell:M=>R(Ao,{direction:"row",spacing:1,alignItems:"center",children:[IA(M.value),n(mt,{variant:"body2",children:M.value})]})},{field:"uploadedOn",headerName:m("Uploaded On"),flex:1,align:"center",headerAlign:"center"},{field:"uploadedBy",headerName:m("Uploaded By"),sortable:!1,flex:1},{field:"view",headerName:m("View"),sortable:!1,filterable:!1,align:"center",headerAlign:"center",renderCell:M=>{var we,de;return n(xs,{size:"small",sx:{color:$e.icon.matView,"&:hover":{backgroundColor:"rgba(2, 136, 209, 0.1)"}},children:n(RT,{index:M.row.id,name:((we=M==null?void 0:M.row)==null?void 0:we.docName)||((de=M==null?void 0:M.row)==null?void 0:de.fileName),documentViewUrl:M.row.documentViewUrl})})}},{field:"action",headerName:m("Action"),sortable:!1,filterable:!1,align:"center",headerAlign:"center",flex:1,renderCell:M=>{var we,de,F,qe,W;return R(Ao,{direction:"row",spacing:0,children:[n(xs,{size:"small",sx:{color:(we=$e)==null?void 0:we.icon.matDownload,"&:hover":{backgroundColor:"rgba(46, 125, 50, 0.1)"}},children:n(_T,{index:M.row.id,name:((de=M==null?void 0:M.row)==null?void 0:de.docName)||((F=M==null?void 0:M.row)==null?void 0:F.fileName)})}),n(xs,{size:"small",sx:{color:(qe=$e)==null?void 0:qe.icon.delete,"&:hover":{backgroundColor:"rgba(211, 47, 47, 0.1)"}},children:n(IT,{index:M.row.id,name:M.row.docName||((W=M==null?void 0:M.row)==null?void 0:W.fileName),setSnackbar:f,setopenSnackbar:f,setMessageDialogMessage:g,handleSnackbarOpen:L,setDownloadLoader:p,DownloadLoader:s})})]})}}];d.useEffect(()=>{te()},[Q]),d.useEffect(()=>{var we,de;if(C&&(S==null?void 0:S.reqStatus)===((we=Co)==null?void 0:we.SYNDICATED_IN_SAP_DIRECT)){let F=[];var M={id:S.requestId,comment:((de=u==null?void 0:u.otherPayloadData)==null?void 0:de.Comments)||"",user:S.createdBy,createdAt:S.changedOnAct,taskName:"Direct Syndication Task",role:"Requestor"};F.push(M),O(F);return}He()},[]);const te=()=>{let M=t,we=qe=>{var W=[];qe.documentDetailDtoList.forEach(Ke=>{var Ds={id:Ke.documentId,docType:Ke.fileType,docName:Ke.fileName,uploadedOn:zn(Ke.docCreationDate).format(l.dateFormat),uploadedBy:Ke.createdBy,attachmentType:Ke.attachmentType,documentViewUrl:Ke.documentViewUrl};K(Vp(Ke==null?void 0:Ke.attachmentType)),W.push(Ds)}),ae(W)};const F=(k==null?void 0:k.ChildRequestId)||(H==null?void 0:H.childRequestId)?`/${ah}/${We.TASK_ACTION_DETAIL.GET_CHILD_DOCS}/${M}`:`/${ah}/${We.TASK_ACTION_DETAIL.GET_DOCS}/${M}`;Xe(F,"get",we)},He=()=>{let M=t,we=F=>{var qe=[];F.body.forEach(W=>{var Ke={id:W.requestId,comment:W.comment,user:W.createdByUser,createdAt:W.updatedAt,taskName:W.taskName,role:W.createdByRole};qe.push(Ke)}),O(qe)},de=F=>{console.log(F)};Xe(`/${ve}/${We.TASK_ACTION_DETAIL.TASKDETAILS_FOR_REQUESTID}?requestId=${M}`,"get",we,de)};return R("div",{children:[n(Rs,{container:!0,spacing:2,sx:{padding:"10px",margin:0},children:R(Rs,{item:!0,md:12,sx:{backgroundColor:$e.primary.white,maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:2,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Oa},children:[n(Rs,{container:!0,sx:{display:"flex",justifyContent:"space-between",flexDirection:"row",alignItems:"center"},children:R(Le,{sx:{display:"flex",justifyContent:"space-between",flexDirection:"row",alignItems:"space-between",width:"100%"},children:[n(mt,{variant:"h6",children:n("strong",{children:m("Attachments")})}),!o&&(e==null?void 0:e.map((M,we)=>n(vT,{title:"Material",useMetaData:!1,artifactId:`${M.MDG_ATTACHMENTS_NAME}_${r}`,artifactName:"MaterialMaster",attachmentType:M.MDG_ATTACHMENTS_NAME,requestId:t,getAttachments:te})))]})}),re.length>0?n(Di,{width:"100%",rows:re,columns:ge,hideFooter:!1,getRowIdValue:"id",autoHeight:!0,disableSelectionOnClick:!0,stopPropagation_Column:"action"}):R(Ao,{alignItems:"center",spacing:2,sx:{py:4},children:[n(NT,{sx:{fontSize:40,color:I.accent,transform:"rotate(90deg)"}}),n(mt,{variant:"body2",color:I.secondaryText,children:m("No Attachments Found")})]}),n("br",{}),R(Le,{sx:{maxWidth:"100%",bgcolor:I.background,borderRadius:"12px",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[n(Ao,{direction:"row",alignItems:"center",spacing:1,mb:3,children:n(mt,{variant:"h6",fontWeight:"bold",color:I.text,children:m("Comments")})}),y.length>0?n(MT,{position:"right",sx:{p:0,m:0,"& .MuiTimelineItem-root:before":{flex:0,padding:0}},children:y.map((M,we)=>R(OT,{sx:{},children:[R(xT,{children:[n(yT,{sx:{bgcolor:I.light,boxShadow:"0 2px 4px rgba(0,0,0,0.1)"},children:n(lh,{size:18,sx:{color:"blue"}})}),n(LT,{sx:{width:"2.2px"}})]}),n(DT,{sx:{py:"12px",px:2},children:n(hr,{variant:"outlined",sx:{borderRadius:"12px",borderColor:I.accent,background:"linear-gradient(135deg, #FFFFFF 0%, #F9FAFB 100%)",transition:"all 0.3s ease"},children:R(fr,{sx:{p:2},children:[R(Ao,{direction:"row",justifyContent:"space-between",alignItems:"center",children:[R(Ao,{direction:"row",spacing:1.5,alignItems:"center",children:[n(jp,{sx:{bgcolor:"#e3f2fd",color:I.primary,boxShadow:"none",width:32,height:32,fontSize:"14px",transition:"all 0.3s ease","&:hover":{transform:"rotate(5deg)"}},children:M.user.charAt(0).toUpperCase()}),n(mt,{fontWeight:"bold",color:I.text,children:M.user}),n(mr,{label:M.taskName,size:"small",sx:{backgroundColor:$e.reportTile.lightBlue,color:$e.primary.lightPlus,fontSize:"12px",borderRadius:"16px"}})]}),R(Ao,{direction:"row",alignItems:"center",spacing:1,children:[n(mt,{variant:"body2",color:I.secondaryText,sx:{fontSize:"12px"},children:zn(M.createdAt).format("MM/DD/YYYY, h:mm A")}),n(xs,{size:"small",sx:{color:I.secondaryText},children:n(zp,{fontSize:"small"})})]})]}),n(Zh,{sx:{my:2,borderColor:I.accent}}),n(mt,{variant:"body2",color:I.text,sx:{my:1,fontSize:"14px",lineHeight:"1.6"},children:M.comment||"-"})]})})})]},M.id))}):R(Ao,{alignItems:"center",spacing:2,sx:{py:4},children:[n(lh,{sx:{fontSize:40,color:I.accent}}),n(mt,{variant:"body2",color:I.secondaryText,children:m("No Remarks found")})]})]}),n("br",{})]})}),(!U||t&&!C||C&&ko.includes(c))&&n(Le,{sx:{borderTop:"1px solid #e0e0e0",padding:"16px"},children:n(Wr,{activeTab:ql.ATTACHMENT_AND_COMMENTS,submitForApprovalDisabled:!nr(oe),filteredButtons:A})}),Q&&n(Hn,{openSnackBar:Q,alertMsg:z,handleSnackBarClose:N})]})},aa=({number:e,label:t})=>R("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",padding:12,background:"#fff",borderRadius:8,boxShadow:"0 5px 15px rgba(0, 0, 0, 0.05)",transition:"transform 0.3s ease",animation:"fadeIn 0.5s ease-out forwards",minHeight:80},children:[n("div",{className:"stat-number",children:e}),n("div",{className:"stat-label",children:t})]}),MA=({data:e})=>{let t=0,r=Object.keys(e).length,o=0,c=0,s=0;const{t:p}=Sn();return Object.values(e).forEach(l=>{var re;const u=l.workflowDetails;s=u==null?void 0:u.totalAverageSLA,t+=2,o+=(u.requestor_sla||0)+(u.mdmApprover_sla||0),c+=2,(re=u.workflowTaskDetailsByLevel)==null||re.forEach(ae=>{var O;const y=(O=Object.values(ae))==null?void 0:O[0];t+=y.length,c+=y.length,y.forEach(K=>{o+=K.taskSla||0})})}),R("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(180px, 1fr))",gap:16,margin:"16px 0",padding:8,borderRadius:8},children:[n(aa,{number:r,label:p("Workflow Groups")}),n(aa,{number:t,label:p("Total Tasks")}),n(aa,{number:s,label:p("Avg SLA (Days)")})]})};var ic={},OA=Go;Object.defineProperty(ic,"__esModule",{value:!0});var hg=ic.default=void 0,xA=OA(Bo()),yA=$o;hg=ic.default=(0,xA.default)((0,yA.jsx)("path",{d:"M7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2M1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12.9-1.63h7.45c.75 0 1.41-.41 1.75-1.03l3.58-6.49c.08-.14.12-.31.12-.48 0-.55-.45-1-1-1H5.21l-.94-2zm16 16c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2"}),"ShoppingCart");var ac={},LA=Go;Object.defineProperty(ac,"__esModule",{value:!0});var yi=ac.default=void 0,DA=LA(Bo()),PA=$o;yi=ac.default=(0,DA.default)((0,PA.jsx)("path",{d:"M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2m-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1m2 14H7v-2h7zm3-4H7v-2h10zm0-4H7V7h10z"}),"Assignment");var cc={},qA=Go;Object.defineProperty(cc,"__esModule",{value:!0});var fg=cc.default=void 0,UA=qA(Bo()),kA=$o;fg=cc.default=(0,UA.default)((0,kA.jsx)("path",{d:"m3.5 18.49 6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L2 16.99z"}),"ShowChart");var dc={},HA=Go;Object.defineProperty(dc,"__esModule",{value:!0});var gg=dc.default=void 0,BA=HA(Bo()),GA=$o;gg=dc.default=(0,BA.default)((0,GA.jsx)("path",{d:"M15 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4m-9-2V7H4v3H1v2h3v3h2v-3h3v-2zm9 4c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4"}),"PersonAdd");var uc={},$A=Go;Object.defineProperty(uc,"__esModule",{value:!0});var pg=uc.default=void 0,FA=$A(Bo()),Mh=$o;pg=uc.default=(0,FA.default)([(0,Mh.jsx)("path",{d:"M14 7h-4c-1.1 0-2 .9-2 2v6h2v7h4v-7h2V9c0-1.1-.9-2-2-2"},"0"),(0,Mh.jsx)("circle",{cx:"12",cy:"4",r:"2"},"1")],"Man");const WA=e=>e.includes("Storage")?n(Kp,{}):e.includes("Buyer")?n(hg,{}):e.includes("Planner")?n(yi,{}):e.includes("Regulatory")?n(Gr,{}):e.includes("Product")?n(PT,{}):e.includes("Plant")?n(fg,{}):e.includes("Data")?n(qT,{}):e.includes("Requestor")?n(gg,{}):e.includes("Steward")?n(Jp,{}):e.includes("Manager")?n(pg,{}):n(yi,{}),jA=({id:e,task:t,onClick:r})=>{const o="#1976d2";return R("div",{id:e,onClick:r,className:"task",style:{borderRadius:12,marginBottom:8,background:"#fff",boxShadow:"0 4px 12px rgba(0, 0, 0, 0.08)",cursor:"pointer",transition:"transform 0.1s ease",maxWidth:230,padding:8,borderLeft:`4px solid ${o}`,position:"relative"},children:[n("div",{style:{position:"absolute",left:-12,top:12,width:16,height:16,borderRadius:"50%",background:"#fff",border:`3px solid ${o}`,boxSizing:"border-box",zIndex:2}}),R("div",{style:{display:"flex",alignItems:"start",gap:8,paddingTop:"16px",paddingBottom:"16px"},children:[n("div",{style:{color:"#1976d2",fontSize:24,display:"flex",alignItems:"center"},children:WA(t.name)}),n("div",{style:{fontWeight:600},children:t.name}),R("div",{style:{marginLeft:"auto",color:"#3498db",fontSize:14,background:"#e1f0fa",paddingLeft:"10px",paddingRight:"10px",borderRadius:"20px"},children:[t.sla,"d"]})]})]})},Tg=({label:e,tasks:t,onTaskClick:r})=>R("div",{style:{display:"flex",flexDirection:"column",alignItems:"stretch",gap:0,padding:12},children:[n("div",{style:{marginBottom:8},children:n("span",{className:"level-label",style:{fontWeight:600,fontSize:16,display:"block",textAlign:"center",border:`${e=="Requestor"&&"2px solid #90EE90"}`},children:e})}),n("div",{style:{display:"flex",flexDirection:"column",gap:8,position:"relative"},children:t.map(o=>n(jA,{id:o.id,task:o,onClick:()=>r(o)},o.id))})]});Tg.propTypes={label:ci.string.isRequired,tasks:ci.arrayOf(ci.object).isRequired,onTaskClick:ci.func.isRequired};const zA=({containerRef:e,sourceTargets:t})=>{const r=d.useRef(null),[o,c]=d.useState({width:0,height:0});return d.useEffect(()=>{const s=()=>{if(e.current){const p=e.current.getBoundingClientRect();c({width:p.width,height:p.height})}};return s(),window.addEventListener("resize",s),()=>window.removeEventListener("resize",s)},[e]),d.useEffect(()=>{if(!e.current||!r.current)return;const s=r.current;s.innerHTML="";const p=document.createElementNS("http://www.w3.org/2000/svg","defs"),l=document.createElementNS("http://www.w3.org/2000/svg","marker");l.setAttribute("id","arrowhead"),l.setAttribute("markerWidth","10"),l.setAttribute("markerHeight","7"),l.setAttribute("refX","9"),l.setAttribute("refY","3.5"),l.setAttribute("orient","auto");const u=document.createElementNS("http://www.w3.org/2000/svg","polygon");u.setAttribute("points","0 0, 10 3.5, 0 7"),u.setAttribute("fill","#3498db"),l.appendChild(u),p.appendChild(l),s.appendChild(p),t.forEach(({fromId:re,toId:ae})=>{const y=document.getElementById(re),O=document.getElementById(ae);if(!y||!O)return;const K=y.getBoundingClientRect(),m=O.getBoundingClientRect(),B=e.current.getBoundingClientRect(),ce=K.right-B.left,oe=K.top+K.height/2-B.top,Q=m.left-B.left,f=m.top+m.height/2-B.top,Y=document.createElementNS("http://www.w3.org/2000/svg","path"),z=Math.max(50,Math.abs(Q-ce)/2),g=`M ${ce},${oe} C ${ce+z},${oe} ${Q-z},${f} ${Q},${f}`;Y.setAttribute("d",g),Y.setAttribute("stroke","#bdc3c7"),Y.setAttribute("stroke-width","2"),Y.setAttribute("fill","none"),Y.setAttribute("marker-end","url(#arrowhead)"),s.appendChild(Y)})},[e,t,o]),n("svg",{ref:r,width:o.width,height:o.height,style:{position:"absolute",top:5,left:1.5,pointerEvents:"none",zIndex:0}})},YA=({groupName:e,groupData:t,onTaskClick:r})=>{var u;const o=d.useRef(null),[c,s]=d.useState([]),p=[],l=(re,ae,y,O)=>({type:re,label:ae,tasks:y.map((K,m)=>({...K,id:`${e}-level${O}-task${m}`}))});return p.push(l("requestor",t.requestorTaskLevelName,[{name:t.requestorTaskName,sla:t.requestor_sla,group:t.requestorTaskGroup,approver:"Requester",level:t.requestorTaskLevelName,status:"Pending"}],0)),(u=t==null?void 0:t.workflowTaskDetailsByLevel)==null||u.forEach((re,ae)=>{const y=Object.keys(re)[0],O=re[y];O.length>0&&p.push(l("workflow",`Level ${O[0].workflowApprovalLevel}: ${O[0].workflowLevelName}`,O.map(K=>({name:K.workflowTaskName,sla:K.taskSla,group:K.workflowTaskGroup,approver:K.taskApprover,level:K.workflowLevelName,status:"In Progress"})),ae+1))}),p.push(l("mdm",t.mdmTaskLevelName,[{name:t.mdmTaskName,sla:t.mdmApprover_sla,group:t.mdmTaskGroup,approver:t.mdmApprover_RecipientUsers,level:t.mdmTaskLevelName,status:"Not Started"}],p.length)),d.useEffect(()=>{const re=[];for(let ae=0;ae<p.length-1;ae++){const y=p[ae],O=p[ae+1];y.tasks.forEach(K=>{O.tasks.length>0&&re.push({fromId:K.id,toId:O.tasks[0].id})})}s(re)},[e]),R("div",{style:{display:"flex",flexDirection:"row",alignItems:"stretch",width:"100%",borderRadius:8,padding:16,minHeight:120,position:"relative",gap:"24px"},ref:o,children:[p==null?void 0:p.map((re,ae)=>n(Tg,{type:re.type,label:re.label,tasks:re.tasks,onTaskClick:r},ae)),n(zA,{containerRef:o,sourceTargets:c})]})};var hc={},XA=Go;Object.defineProperty(hc,"__esModule",{value:!0});var Eg=hc.default=void 0,VA=XA(Bo()),KA=$o;Eg=hc.default=(0,VA.default)((0,KA.jsx)("path",{d:"M7.41 8.59 12 13.17l4.59-4.58L18 10l-6 6-6-6z"}),"KeyboardArrowDown");var fc={},JA=Go;Object.defineProperty(fc,"__esModule",{value:!0});var mg=fc.default=void 0,QA=JA(Bo()),ZA=$o;mg=fc.default=(0,QA.default)((0,ZA.jsx)("path",{d:"M7.41 15.41 12 10.83l4.59 4.58L18 14l-6-6-6 6z"}),"KeyboardArrowUp");const eb=({groupName:e,groupData:t,materialTypes:r,onTaskClick:o})=>{var l,u,re,ae,y,O,K,m;const[c,s]=d.useState(!1),p=()=>s(!c);return d.useEffect(()=>{},[c]),R("div",{style:{border:"1px solid #e0e0e0",borderRadius:8,marginBottom:16,background:"#fff",boxShadow:"0 2px 8px rgba(0,0,0,0.04)",overflow:"hidden",transition:"box-shadow 0.2s"},children:[R("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:"16px",cursor:"pointer",backgroundImage:"linear-gradient(180deg, rgb(242, 241, 255) 0%, rgb(255, 255, 255) 100%)",border:"1px solid #E0E0E0",borderBottom:"1px solid #e0e0e0"},onClick:p,children:[R("div",{children:[R("div",{style:{fontWeight:600,fontSize:18,color:(u=(l=$e)==null?void 0:l.primary)==null?void 0:u.main},children:[e,` (${r.join(", ")})`]}),R("div",{style:{fontSize:14,color:(ae=(re=$e)==null?void 0:re.primary)==null?void 0:ae.main,marginTop:2},children:[t==null?void 0:t.mdmTaskGroup," • ",t==null?void 0:t.mdmTaskLevelName]})]}),n("div",{style:{fontSize:28,color:"#fff",transition:"transform 0.2s",display:"flex",alignItems:"center",justifyContent:"center"},children:c?n(mg,{style:{color:(O=(y=$e)==null?void 0:y.primary)==null?void 0:O.main,fontSize:32}}):n(Eg,{style:{color:(m=(K=$e)==null?void 0:K.primary)==null?void 0:m.main,fontSize:32}})})]}),!c&&n("div",{style:{padding:16,background:"#fff"},children:n(YA,{groupName:e,groupData:t,onTaskClick:o})})]})},tb=e=>{const t=new Date,r=new Date(t);return r.setDate(t.getDate()+e),r.toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"})},sb=({task:e,onClose:t})=>n("div",{className:"modal",onClick:r=>r.target.classList.contains("modal")&&t(),children:R("div",{className:"modal-content",children:[R("div",{className:"modal-header",children:[R("div",{className:"modal-title",style:{display:"flex",alignItems:"center",gap:8},children:[n(yi,{style:{color:"#fff"}}),n("span",{children:e.name})]}),n("div",{className:"modal-close",onClick:t,style:{cursor:"pointer"},children:n(Gl,{style:{color:"#fff"}})})]}),R("div",{className:"modal-body",children:[R("div",{className:"task-detail",children:[n("div",{className:"detail-label",children:"Task Group:"}),n("div",{className:"detail-value",children:e.group})]}),R("div",{className:"task-detail",children:[n("div",{className:"detail-label",children:"Approver:"}),n("div",{className:"detail-value",children:e.approver})]}),R("div",{className:"task-detail",children:[n("div",{className:"detail-label",children:"Level:"}),n("div",{className:"detail-value",children:e.level})]}),R("div",{className:"task-detail",children:[n("div",{className:"detail-label",children:"SLA:"}),n("div",{className:"detail-value",children:R("span",{className:"sla-badge sla-normal",children:[e.sla," days"]})})]}),R("div",{className:"task-detail",children:[n("div",{className:"detail-label",children:"Due Date:"}),n("div",{className:"detail-value",children:tb(e.sla)})]}),R("div",{className:"task-detail",children:[n("div",{className:"detail-label",children:"Status:"}),n("div",{className:"detail-value",children:e.status})]})]})]})}),ob=({data:e})=>{var p;const[t,r]=d.useState(null),{t:o}=Sn(),c=l=>{r(l)},s=()=>{r(null)};return R("div",{children:[n(mt,{align:"left",variant:"h4",component:"h2",gutterBottom:!0,children:o("Workflow Details")}),n(MA,{data:e}),n("div",{className:"workflow-container",children:(p=Object==null?void 0:Object.entries(e))==null?void 0:p.map(([l,u])=>n(eb,{groupName:l,groupData:u==null?void 0:u.workflowDetails,materialTypes:u==null?void 0:u.materialTypes,onTaskClick:c},l))}),t&&n(sb,{task:t,onClose:s})]})};function nb({label:e,checked:t=!1,onChange:r,id:o}){return n(Sr,{control:n(Gn,{id:o,checked:t,onChange:r,sx:{color:"primary.main","&.Mui-checked":{color:"primary.main"}}}),label:e,sx:{ml:0,".MuiFormControlLabel-label":{fontSize:14,color:"#4B5563",cursor:"pointer"}}})}const rb=({initialReqScreen:e,isreqBench:t})=>{const r=on(),c=new URLSearchParams(r.search).get("RequestId"),s=se(z=>z.payload),p=se(z=>z.payload.payloadData),[l,u]=d.useState(!1),[re,ae]=d.useState(),[y,O]=d.useState("success"),[K,m]=d.useState(!1),{t:B}=Sn(),{customError:ce}=nn(),{createPayloadFromReduxState:oe}=ka({initialReqScreen:e,isReqBench:t}),{changePayloadForTemplate:Q}=$i(p==null?void 0:p.TemplateName),f=()=>{u(!1)},Y=()=>{var C,S,q,A,x,H,k,L,N,U,I,ge,te,He,M,we,de;const z=(p==null?void 0:p.RequestType)===((C=b)==null?void 0:C.CHANGE)||(p==null?void 0:p.RequestType)===((S=b)==null?void 0:S.CHANGE_WITH_UPLOAD)?Q(!!c):oe(s),g={materialDetails:z,dtName:oa((A=(q=z[0])==null?void 0:q.Torequestheaderdata)==null?void 0:A.RequestType).dtName,version:oa((H=(x=z[0])==null?void 0:x.Torequestheaderdata)==null?void 0:H.RequestType).version,requestId:((L=(k=z[0])==null?void 0:k.Torequestheaderdata)==null?void 0:L.RequestId)||"",scenario:(I=oa((U=(N=z[0])==null?void 0:N.Torequestheaderdata)==null?void 0:U.RequestType))==null?void 0:I.scenario,templateName:(p==null?void 0:p.RequestType)===((ge=b)==null?void 0:ge.CHANGE)||(p==null?void 0:p.RequestType)===((te=b)==null?void 0:te.CHANGE_WITH_UPLOAD)?(M=(He=z[0])==null?void 0:He.Torequestheaderdata)==null?void 0:M.TemplateName:"",matlType:"ALL",region:((de=(we=z[0])==null?void 0:we.Torequestheaderdata)==null?void 0:de.Region)||""},T=F=>{if((F==null?void 0:F.size)>0){const qe=URL.createObjectURL(F),W=document.createElement("a");W.href=qe,W.setAttribute("download",`Material_Preview_${new Date().getTime()}.xlsx`),document.body.appendChild(W),W.click(),document.body.removeChild(W),URL.revokeObjectURL(qe)}},v=F=>{ce(F),ae(F==null?void 0:F.message),O("error"),u(!0)};Xe(`/${ve}${We.EXCEL.EXPORT_PREVIEW_EXCEL}`,"postandgetblob",T,v,g)};return R(Rs,{item:!0,md:12,sx:{backgroundColor:"white",borderRadius:"8px",border:"1px solid #E0E0E0",boxShadow:"0px 1px 4px rgba(0, 0, 0, 0.1)",p:"10px"},children:[n(mt,{sx:{fontWeight:"bold",mb:"6px"},children:B("Master data details")}),n(Le,{sx:{backgroundColor:"#FAFAFA",borderRadius:"8px",boxShadow:"none"},children:R(Le,{sx:{padding:"8px"},children:[n(mt,{align:"left",variant:"h6",component:"h2",children:B("Please download the excel sheet to view all the material data.")}),R(Le,{sx:{display:"flex",flexDirection:"column",alignItems:"flex-start",gap:1,mt:2},children:[n(Rt,{variant:"contained",startIcon:n(UT,{sx:{fontSize:28,animation:"downloadBounce 2s ease-in-out infinite",filter:"drop-shadow(0 2px 4px rgba(255,255,255,0.3))"}}),onClick:Y,sx:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",position:"relative",overflow:"hidden","&::before":{content:'""',position:"absolute",top:0,left:"-100%",width:"100%",height:"100%",background:"linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)",transition:"left 0.5s"},"&:hover::before":{left:"100%"},"&:hover":{background:"linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)",transform:"translateY(-3px) scale(1.02)",boxShadow:"0 12px 25px rgba(102, 126, 234, 0.4), 0 0 20px rgba(118, 75, 162, 0.3)"},"&:active":{transform:"translateY(-1px) scale(0.98)",boxShadow:"0 6px 15px rgba(102, 126, 234, 0.3)"},transition:"all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)",borderRadius:3,py:1.8,px:3,textTransform:"none",fontSize:"1.1rem",fontWeight:600,boxShadow:"0 8px 20px rgba(102, 126, 234, 0.3), 0 0 15px rgba(118, 75, 162, 0.2)",display:"flex",alignItems:"center",gap:1.5,border:"1px solid rgba(255, 255, 255, 0.1)",backdropFilter:"blur(10px)",color:"#ffffff",letterSpacing:"0.5px",minWidth:"180px","@keyframes downloadBounce":{"0%, 100%":{transform:"translateY(0) rotate(0deg)",filter:"drop-shadow(0 2px 4px rgba(255,255,255,0.3))"},"25%":{transform:"translateY(-3px) rotate(-2deg)",filter:"drop-shadow(0 4px 8px rgba(255,255,255,0.4))"},"50%":{transform:"translateY(-6px) rotate(0deg)",filter:"drop-shadow(0 6px 12px rgba(255,255,255,0.5))"},"75%":{transform:"translateY(-3px) rotate(2deg)",filter:"drop-shadow(0 4px 8px rgba(255,255,255,0.4))"}},"&::after":{content:'""',position:"absolute",top:"50%",left:"50%",width:"0",height:"0",background:"radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%)",borderRadius:"50%",transform:"translate(-50%, -50%)",transition:"width 0.6s, height 0.6s",pointerEvents:"none"},"&:active::after":{width:"300px",height:"300px"}},children:R(Le,{sx:{display:"flex",alignItems:"center",gap:1,position:"relative",zIndex:1},children:[B("Download Excel"),n(Le,{component:"span",sx:{fontSize:"0.8rem",opacity:.8,fontWeight:400,ml:.5},children:"(.xlsx)"})]})}),n(nb,{label:B("I have reviewed all material details."),checked:K,onChange:()=>m(!K)})]})]})}),n(Hn,{openSnackBar:l,alertMsg:re,alertType:y,handleSnackBarClose:f})]})},lb=()=>{const{t:e}=Sn();return R(Le,{sx:{p:3,maxWidth:1400,mx:"auto"},children:[n(mt,{variant:"h6",sx:{mb:3,color:"#666"},children:e("Workflow Details")}),R(Le,{sx:{display:"flex",gap:6,mb:4,justifyContent:"flex-start",maxWidth:600},children:[R(Le,{sx:{textAlign:"center"},children:[n(As,{variant:"text",width:60,height:60,sx:{fontSize:"2.5rem",mx:"auto",bgcolor:"#e3f2fd",borderRadius:1}}),n(As,{variant:"text",width:100,height:20,sx:{mt:1,mx:"auto"}})]}),R(Le,{sx:{textAlign:"center"},children:[n(As,{variant:"text",width:60,height:60,sx:{fontSize:"2.5rem",mx:"auto",bgcolor:"#e3f2fd",borderRadius:1}}),n(As,{variant:"text",width:80,height:20,sx:{mt:1,mx:"auto"}})]}),R(Le,{sx:{textAlign:"center"},children:[n(As,{variant:"text",width:60,height:60,sx:{fontSize:"2.5rem",mx:"auto",bgcolor:"#e3f2fd",borderRadius:1}}),n(As,{variant:"text",width:120,height:20,sx:{mt:1,mx:"auto"}})]})]}),R(Uh,{expanded:!0,sx:{bgcolor:"#37474f",color:"white","&:before":{display:"none"},borderRadius:1,overflow:"hidden"},children:[n(kh,{sx:{bgcolor:"#37474f","& .MuiAccordionSummary-content":{alignItems:"center"}},children:R(Le,{children:[n(As,{variant:"text",width:180,height:24,sx:{bgcolor:"rgba(255,255,255,0.2)"}}),n(As,{variant:"text",width:220,height:16,sx:{bgcolor:"rgba(255,255,255,0.1)",mt:.5}})]})}),R(Hh,{sx:{bgcolor:"#f5f5f5",p:0},children:[n(Le,{sx:{display:"grid",gridTemplateColumns:"repeat(6, 1fr)",gap:2,bgcolor:"#fff",borderBottom:"1px solid #e0e0e0",p:2},children:["Requestor","Level 1: Data Entry","Level 2: Additional Master Data","Level 3: Cost","Level 4: Record Approval","Final Creation"].map((t,r)=>n(Le,{sx:{textAlign:"center"},children:n(As,{variant:"text",width:"80%",height:20,sx:{mx:"auto"}})},r))}),n(Le,{sx:{p:3,bgcolor:"#fafafa"},children:R(Le,{sx:{display:"grid",gridTemplateColumns:"repeat(6, 1fr)",gap:2,alignItems:"flex-start"},children:[n(Le,{sx:{display:"flex",flexDirection:"column",gap:2},children:[1,2,3,4,5,6].map((t,r)=>R(Le,{sx:{position:"relative"},children:[n(hr,{sx:{bgcolor:"#e3f2fd",minHeight:60,border:"2px solid #2196f3",borderRadius:2},children:n(fr,{sx:{p:2},children:R(Le,{sx:{display:"flex",alignItems:"center",gap:1},children:[n(As,{variant:"circular",width:24,height:24}),R(Le,{sx:{flex:1},children:[n(As,{variant:"text",width:"70%",height:16}),n(As,{variant:"text",width:"40%",height:12,sx:{mt:.5}})]}),n(As,{variant:"text",width:30,height:20})]})})}),r<5&&n(Le,{sx:{position:"absolute",right:-8,top:"50%",width:16,height:2,bgcolor:"#2196f3",zIndex:1,transform:"translateY(-50%)"}})]},r))}),n(Le,{sx:{display:"flex",flexDirection:"column",gap:2},children:n(hr,{sx:{bgcolor:"#e3f2fd",minHeight:60,border:"2px solid #2196f3",borderRadius:2},children:n(fr,{sx:{p:2},children:R(Le,{sx:{display:"flex",alignItems:"center",gap:1},children:[n(As,{variant:"circular",width:24,height:24}),R(Le,{sx:{flex:1},children:[n(As,{variant:"text",width:"80%",height:16}),n(As,{variant:"text",width:"50%",height:12,sx:{mt:.5}})]}),n(As,{variant:"text",width:30,height:20})]})})})}),n(Le,{sx:{display:"flex",flexDirection:"column",gap:2},children:[1,2].map((t,r)=>n(hr,{sx:{bgcolor:"#e3f2fd",minHeight:60,border:"2px solid #2196f3",borderRadius:2},children:n(fr,{sx:{p:2},children:R(Le,{sx:{display:"flex",alignItems:"center",gap:1},children:[n(As,{variant:"circular",width:24,height:24}),R(Le,{sx:{flex:1},children:[n(As,{variant:"text",width:"75%",height:16}),n(As,{variant:"text",width:"45%",height:12,sx:{mt:.5}})]}),n(As,{variant:"text",width:30,height:20})]})})},r))}),n(Le,{sx:{display:"flex",flexDirection:"column",gap:2},children:n(hr,{sx:{bgcolor:"#e3f2fd",minHeight:60,border:"2px solid #2196f3",borderRadius:2},children:n(fr,{sx:{p:2},children:R(Le,{sx:{display:"flex",alignItems:"center",gap:1},children:[n(As,{variant:"circular",width:24,height:24}),R(Le,{sx:{flex:1},children:[n(As,{variant:"text",width:"85%",height:16}),n(As,{variant:"text",width:"35%",height:12,sx:{mt:.5}})]}),n(As,{variant:"text",width:30,height:20})]})})})}),n(Le,{sx:{display:"flex",flexDirection:"column",gap:2},children:n(hr,{sx:{bgcolor:"#e3f2fd",minHeight:60,border:"2px solid #2196f3",borderRadius:2},children:n(fr,{sx:{p:2},children:R(Le,{sx:{display:"flex",alignItems:"center",gap:1},children:[n(As,{variant:"circular",width:24,height:24}),R(Le,{sx:{flex:1},children:[n(As,{variant:"text",width:"90%",height:16}),n(As,{variant:"text",width:"55%",height:12,sx:{mt:.5}})]}),n(As,{variant:"text",width:30,height:20})]})})})}),n(Le,{sx:{display:"flex",flexDirection:"column",gap:2},children:n(hr,{sx:{bgcolor:"#f5f5f5",minHeight:60,border:"2px solid #ccc",borderRadius:2},children:n(fr,{sx:{p:2},children:R(Le,{sx:{display:"flex",alignItems:"center",gap:1},children:[n(As,{variant:"circular",width:24,height:24,sx:{bgcolor:"#bdbdbd"}}),R(Le,{sx:{flex:1},children:[n(As,{variant:"text",width:"75%",height:16,sx:{bgcolor:"#bdbdbd"}}),n(As,{variant:"text",width:"40%",height:12,sx:{mt:.5,bgcolor:"#bdbdbd"}})]}),n(As,{variant:"text",width:30,height:20,sx:{bgcolor:"#bdbdbd"}})]})})})})]})})]})]})]})},ib=e=>{var H;const t=se(k=>k.payload),{customError:r}=nn(),o=on(),c=new URLSearchParams(o.search),s=se(k=>k.request.materialRows),p=se(k=>k.userManagement.taskData),l=se(k=>k.applicationConfig),u=c.get("reqBench"),re=c.get("RequestId"),ae=se(k=>k.request.requestHeader),{showSnackbar:y}=Bh(),[O,K]=d.useState(null),[m,B]=d.useState(!1),ce=!(p!=null&&p.taskId)&&!u,oe=se(k=>k.tabsData.requestHeaderData),{t:Q}=Sn(),f=se(k=>k.payload.payloadData),{createPayloadFromReduxState:Y}=ka({initialReqScreen:ce,isReqBench:u}),{changePayloadForTemplate:z}=$i(f==null?void 0:f.TemplateName),g=se(k=>k.payload.filteredButtons),{filteredButtons:T}=ug(p,l,yo,fo),{extendFilteredButtons:v}=lc(p,l,yo,fo),C=f==null?void 0:f.RequestStatus,S=C===Co.DRAFT||C===Co.DRAFT_IN_CAPS||C===Co.VALIDATED_REQUESTOR||C===Co.VALIDATION_FAILED_REQUESTOR||C===Co.UPLOAD_SUCCESSFUL;let q;const A=[po.HANDLE_SEND_BACK,po.HANDLE_VALIDATE,po.HANDLE_CORRECTION];(f==null?void 0:f.RequestType)===b.CREATE||(f==null?void 0:f.RequestType)===b.CREATE_WITH_UPLOAD?q=Cr(T,[...A,po.HANDLE_VALIDATE1]):(f==null?void 0:f.RequestType)===b.EXTEND||(f==null?void 0:f.RequestType)===b.EXTEND_WITH_UPLOAD?q=Cr(v,A):(f==null?void 0:f.RequestType)===b.CHANGE||(f==null?void 0:f.RequestType)===b.CHANGE_WITH_UPLOAD?q=Cr(g,A):q=[];const x=!ko.includes(e==null?void 0:e.requestStatus)||re&&!u;return d.useEffect(()=>{var k,L;if(S){const N=(f==null?void 0:f.RequestType)===((k=b)==null?void 0:k.CHANGE)||(f==null?void 0:f.RequestType)===((L=b)==null?void 0:L.CHANGE_WITH_UPLOAD)?z(!!re):Y(t);B(!0);const U=ge=>{ge.statusCode===Vt.STATUS_200?(K(ge==null?void 0:ge.body),B(!1)):(y(ge==null?void 0:ge.message,"error"),B(!1))},I=ge=>{r(ge),B(!1)};Xe(`/${ve}${We.MASS_ACTION.WORKFLOW_DETAILS_BIFURCATION}`,"post",U,I,N)}},[]),R(Ss,{children:[R(Ao,{spacing:2,children:[Object.entries(oe).map(([k,L])=>R(Rs,{item:!0,md:12,sx:{backgroundColor:"white",borderRadius:"8px",border:"1px solid #E0E0E0",boxShadow:"0px 1px 4px rgba(0, 0, 0, 0.1)",...Oa,pt:"10px"},children:[n(mt,{sx:{fontWeight:"bold",mb:"6px"},children:Q("Request Details")}),n(Le,{sx:{backgroundColor:"#FAFAFA",padding:"10px",pl:"0px",pr:"0px",borderRadius:"8px",boxShadow:"none"},children:n(Rs,{container:!0,spacing:2,children:L.filter(N=>N.visibility!=="Hidden").sort((N,U)=>N.sequenceNo-U.sequenceNo).map(N=>{let U=(ae==null?void 0:ae[N==null?void 0:N.jsonName])||(f==null?void 0:f[N==null?void 0:N.jsonName])||"",I="";return Array.isArray(U)?I=U.join(", "):U instanceof Date||typeof U=="object"&&U instanceof Object&&U.toString().includes("GMT")?I=new Date(U).toLocaleString():I=U,U=I,U&&U!==null&&U!==""&&n(Rs,{item:!0,md:3,children:R("div",{style:{padding:"12px",backgroundColor:"#ffffff",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.1)",transition:"all 0.3s ease"},children:[n(_s,{title:Q(N==null?void 0:N.fieldName)||"Field Name",children:R(mt,{variant:"body1",sx:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%",fontWeight:600,fontSize:"12px",marginBottom:"4px",display:"flex",alignItems:"center"},children:[Q(N==null?void 0:N.fieldName)||"Field Name",((N==null?void 0:N.visibility)==="Required"||(N==null?void 0:N.visibility)==="MANDATORY")&&n("span",{style:{color:"#d32f2f",marginLeft:"2px"},children:"*"})]})}),n(_s,{title:U||"--",children:n("div",{style:{fontSize:"0.8rem",color:"#333333",marginTop:"4px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%"},children:n("span",{style:{fontWeight:500,color:"grey",letterSpacing:"0.5px",wordSpacing:"1px"},children:U||"--"})})})]})},N==null?void 0:N.id)})})})]},k)),n(rb,{initialReqScreen:ce,isreqBench:u}),S&&n(Ss,{children:O&&!m?n(ob,{data:O}):n(lb,{})})]}),(!x||re&&!u||u&&ko.includes(e==null?void 0:e.requestStatus))&&n(Le,{sx:{borderTop:"1px solid #e0e0e0",padding:"16px"},children:n(Wr,{activeTab:ql.PREVIEW,submitForApprovalDisabled:!nr(s),filteredButtons:q,childRequestHeaderData:(H=t==null?void 0:t[f==null?void 0:f.selectedMaterialID])==null?void 0:H.Tochildrequestheaderdata})})]})};var gc={},ab=Go;Object.defineProperty(gc,"__esModule",{value:!0});var Cg=gc.default=void 0,cb=ab(Bo()),db=$o;Cg=gc.default=(0,cb.default)((0,db.jsx)("path",{d:"M12 6c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2m0 9c2.7 0 5.8 1.29 6 2v1H6v-.99c.2-.72 3.3-2.01 6-2.01m0-11C9.79 4 8 5.79 8 8s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4m0 9c-2.67 0-8 1.34-8 4v3h16v-3c0-2.66-5.33-4-8-4"}),"PermIdentityOutlined");const ub={AdditionalData:{fieldName:["Material","AltUnit","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Alternative Unit of Measure","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},BasicData:{fieldName:["Material","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},MRPData:{fieldName:["Material","Plant","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Plant","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},PurchasingData:{fieldName:["Material","Plant","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Plant","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},SalesData:{fieldName:["Material","SalesOrg","DistrChan","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Sales Org","Distribution Channel","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},SalesPlantData:{fieldName:["Material","Plant","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Plant","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},SalesGeneralData:{fieldName:["Material","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},PurchasingGeneralData:{fieldName:["Material","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},WarehouseData:{fieldName:["Material","WhseNo","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Warehouse","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},DescriptionData:{fieldName:["Material","Langu","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Language","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},AdditionalEANData:{fieldName:["Material","AltUnit","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Alternative Unit of Measure","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},CostingData:{fieldName:["Material","Plant","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Plant","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},AccountingData:{fieldName:["Material","Plant","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Plant","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},WorkSchedulingData:{fieldName:["Material","Plant","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Plant","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},TaxClassificationData:{fieldName:["Material","Country","TaxType","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Country","Tax Type","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]}},hb={FinanceCostData:{fieldName:["Material","Plant","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Plant","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]}},fb=({open:e,closeModal:t,requestId:r,requestType:o})=>{const{customError:c}=nn(),[s,p]=d.useState(!0),[l,u]=d.useState(null),re=on(),y=new URLSearchParams(re.search.split("?")[1]).get("RequestId"),[O,K]=d.useState(()=>{var T;const g=y!=null&&y.includes("FCA")?hb:ub;return(T=Object.entries(g))==null?void 0:T.map(([v])=>({label:v,columns:g[v],rows:[]}))}),[m,B]=d.useState(()=>(O==null?void 0:O.length)>0?{number:0,label:O[0].label}:{number:0,label:""}),ce=(g,T)=>{B({number:T,label:O[T].label})},oe={position:"absolute",top:"50%",left:"52%",transform:"translate(-50%, -50%)",width:"80%",height:"auto",bgcolor:"#fff",boxShadow:4,p:2},Q=()=>{t(!1)};d.useEffect(()=>{(async()=>{if(e&&!l)try{const T=await f(r,o);u(T)}catch(T){c(hn.FETCH_CHANGELOG_ERROR,T)}})()},[e,r]),d.useEffect(()=>{if(l)try{K(g=>g==null?void 0:g.map(T=>{const v=kl(Qp,T.label),C=l[T.label]||[];return{...T,rows:C==null?void 0:C.map(S=>({id:mo(),...S,Material:na(S==null?void 0:S.ObjectNo,1),SAPValue:di(S==null?void 0:S.SAPValue),PreviousValue:di(S==null?void 0:S.PreviousValue),CurrentValue:di(S==null?void 0:S.CurrentValue),ChangedOn:di(S==null?void 0:S.ChangedOn),...(v==null?void 0:v.length)>0&&{[v[0]]:na(S==null?void 0:S.ObjectNo,2)},...(v==null?void 0:v.length)>1&&{[v[1]]:na(S==null?void 0:S.ObjectNo,3)}}))}}))}catch(g){c(hn.CHANGE_LOG_MESSAGE,g)}},[l]);const f=g=>{var C;p(!0);const T=`/${ve}/${(C=We)==null?void 0:C.CHG_DISPLAY_REQUESTOR.FETCH_CHANGELOG_DATA}`;let v={ChildRequestId:g};return new Promise((S,q)=>{Xe(T,"post",H=>{var k;if((H==null?void 0:H.statusCode)===Vt.STATUS_200&&((k=H==null?void 0:H.body)==null?void 0:k.length)>0){const L=oT(H==null?void 0:H.body);p(!1),S(L)}else p(!1),S([])},H=>{p(!1),c(H),q(H)},v)})},Y=new Date,z={convertJsonToExcel:()=>{const g=O==null?void 0:O.map(T=>{var C;const v=(C=T==null?void 0:T.columns)==null?void 0:C.fieldName.map((S,q)=>({header:T==null?void 0:T.columns.headerName[q],key:S}));return{sheetName:T==null?void 0:T.label,fileName:y!=null&&y.includes("FCA")?`Finance Costing Changelog Data-${zn(Y).format("DD-MMM-YYYY")}`:`Create Changelog Data-${zn(Y).format("DD-MMM-YYYY")}`,columns:v,rows:T==null?void 0:T.rows}});sT(g)},button:()=>n(Rt,{sx:{textTransform:"capitalize",position:"absolute",right:0,top:0},onClick:()=>z.convertJsonToExcel(),children:"Download"})};return R(Ss,{children:[s&&n(An,{blurLoading:s,loaderMessage:ya.CHANGELOG_LOADING}),n(tT,{open:e,onClose:Q,"aria-labelledby":"modal-modal-title","aria-describedby":"modal-modal-description",children:R(Le,{sx:oe,children:[n(Ao,{children:R(Rs,{item:!0,md:12,sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[R(Le,{sx:{display:"flex",alignItems:"center",gap:"8px"},children:[n(kT,{sx:{color:$e.black.dark,fontSize:"20px","&:hover":{transform:"rotate(360deg)",transition:"0.9s"},textAlign:"center",marginTop:"4px"}}),n(mt,{id:"modal-modal-title",variant:"subtitle1",fontSize:"16px",fontWeight:"bold",sx:{color:$e.black.dark},children:"Change Log"})]}),R(Le,{sx:{display:"flex",alignItems:"center",gap:"8px"},children:[n(_s,{title:"Export Table",children:n(xs,{sx:Zp,onClick:z.convertJsonToExcel,children:n(Ti,{iconName:"IosShare"})})}),n(xs,{sx:{padding:"0 0 0 5px"},onClick:Q,children:n(Gl,{})})]})]})}),n(Bl,{value:m==null?void 0:m.number,onChange:ce,variant:"scrollable",scrollButtons:"auto","aria-label":"modal tabs",children:O==null?void 0:O.map((g,T)=>n(Xn,{label:g.label.replace(eT.ADDING_SPACE," $1").trim()},T))}),n("div",{className:"tab-content",style:{position:"relative",height:"100%",marginTop:16},children:O==null?void 0:O.map((g,T)=>{var v,C,S,q;return(m==null?void 0:m.number)===T&&n(mt,{id:`modal-tab-content-${T}`,sx:{mt:1},children:n(Rs,{item:!0,sx:{position:"relative"},children:n(Ao,{children:n(Di,{rows:g==null?void 0:g.rows,columns:(C=(v=g==null?void 0:g.columns)==null?void 0:v.fieldName)==null?void 0:C.map((A,x)=>{var H;return{field:A,headerName:(H=g==null?void 0:g.columns)==null?void 0:H.headerName[x],flex:1,minWidth:100}}),getRowIdValue:"id",pageSize:(q=(S=g==null?void 0:g.columns)==null?void 0:S.fieldName)==null?void 0:q.length,autoHeight:!0,scrollbarSize:10,sx:{"& .MuiDataGrid-row:hover":{backgroundColor:`${$e.primary.light}40`},backgroundColor:$e.primary.white}})})})},T)})})]})})]})},gb=({handleDownload:e,setEnableDocumentUpload:t,enableDocumentUpload:r,handleUploadMaterial:o})=>{const{t:c}=Sn();return R(Rs,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"12px",border:"1px solid #E0E0E0",mt:1,boxShadow:"0px 8px 24px rgba(48, 38, 185, 0.12)",padding:"1.5rem 2rem"},children:[n(Rs,{container:!0,alignItems:"center",sx:{mb:2},children:n(mt,{sx:{fontSize:"12px",fontWeight:"700"},children:c("Excel Operations")})}),R(Rs,{container:!0,spacing:2,sx:{display:"flex",justifyContent:"center",position:"relative"},children:[n(Rs,{item:!0,xs:12,md:6,sx:{display:"flex",justifyContent:"center"},children:R(kr,{elevation:0,onClick:e,sx:{width:"100%",maxWidth:"280px",height:"200px",borderRadius:"16px",padding:"1.5rem",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",position:"relative",overflow:"hidden",cursor:"pointer",transition:"all 0.3s ease","&:hover":{backgroundColor:"rgba(63, 140, 218, 0.1)"}},children:[n(Le,{sx:{backgroundColor:"rgba(63, 140, 218, 0.2)",borderRadius:"50%",padding:"16px",marginBottom:"16px"},children:n(BT,{sx:{fontSize:64,color:"#1976d2",filter:"drop-shadow(0px 4px 6px rgba(7, 31, 54, 0.3))"}})}),n(mt,{sx:{fontSize:"16px",fontWeight:"600",color:"#0D47A1",mb:1},children:c("Download Excel")}),n(mt,{sx:{fontSize:"12px",color:"#1565C0",textAlign:"center"},children:c("Download Excel if you have not downloaded yet")})]})}),n(Zh,{orientation:"vertical",flexItem:!0,sx:{position:"absolute",height:"80%",top:"10%",left:"50%",display:{xs:"none",md:"block"}}}),n(Rs,{item:!0,xs:12,md:6,sx:{display:"flex",justifyContent:"center"},children:R(kr,{elevation:0,onClick:()=>t(!0),sx:{width:"100%",maxWidth:"280px",height:"200px",borderRadius:"16px",padding:"1.5rem",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",position:"relative",overflow:"hidden",cursor:"pointer",transition:"all 0.3s ease","&:hover":{backgroundColor:"rgba(55, 43, 224, 0.1)"}},children:[n(Le,{sx:{backgroundColor:"rgba(55, 43, 224, 0.2)",borderRadius:"50%",padding:"16px",marginBottom:"16px"},children:n(GT,{sx:{fontSize:64,color:"#3026B9",filter:"drop-shadow(0px 4px 6px rgba(58, 48, 199, 0.4))"}})}),n(mt,{sx:{fontSize:"16px",fontWeight:"600",color:"#3026B9",mb:1},children:c("Upload Excel")}),n(mt,{sx:{fontSize:"12px",color:"#5148C2",textAlign:"center"},children:c("Upload your Excel after doing the necessary changes")})]})})]}),r&&n($T,{artifactId:"",artifactName:"",setOpen:t,handleUpload:o})]})},pb=e=>{var z;const t=Ro(),[r,o]=d.useState(!1),c=se(g=>g.payload.fcRows),s=se(g=>g.payload.unselectedRows),p=se(g=>g.paginationData),l=se(g=>g.payload.filteredButtons),u=se(g=>g.userManagement.taskData),re=se(g=>g.payload.selectedRows),ae=se(g=>g.payload.payloadData),y=(ae==null?void 0:ae.RequestType)||"",{getButtonsDisplay:O}=df(),{getNextDisplayDataForCreate:K}=Gi(),m=on(),ce=new URLSearchParams(m.search).get("reqBench");let oe=m.state;d.useEffect(()=>{u!=null&&u.ATTRIBUTE_1&&O()},[u]),d.useEffect(()=>{var g;if((c==null?void 0:c.length)>0&&!(ce&&!((g=ko)!=null&&g.includes(oe==null?void 0:oe.reqStatus)))){const T=new Set(s.map(C=>C.id)),v=c.filter(C=>!T.has(C.id)).map(C=>C.id);t(Er(v))}return()=>{t(Er([])),t(ch([]))}},[c,t]);const Q=g=>{t(Er(g));const T=c==null?void 0:c.filter(v=>!g.includes(v.id));t(ch(T))},f=[{field:"FinanceCostingId",headerName:"ID",flex:1,hide:!0},{field:"RequestId",headerName:"Req ID",flex:1.7,editable:!1},{field:"RequestType",headerName:"Req Type",flex:1.2,editable:!1},{field:"Requester",headerName:"Requestor",flex:1.6,editable:!1},{field:"CreatedOn",headerName:"Created On(SAP)",flex:1.3,editable:!1,valueFormatter:g=>g.value?zn(g.value).format("DD MMM YYYY"):""},{field:"Material",headerName:"Material Number",flex:1.3,editable:!1},{field:"MatlType",headerName:"Material Type",flex:1,editable:!1},{field:"Plant",headerName:"Plant",flex:1,editable:!1},{field:"FStdPrice",headerName:"Standard Price",flex:1,editable:!1},{field:"IntlPoPrice",headerName:"Initial PO Price",flex:1,editable:!1,valueFormatter:g=>g.value?Number(g.value).toFixed(2):""},{field:"PryVendor",headerName:"Primary Vendor",flex:1.3,editable:!1},{field:"FlagForBOM",headerName:"Flag For BOM",flex:1,editable:!1},{field:"VolInEA",headerName:"Volume EA",flex:1,editable:!1,valueFormatter:g=>g.value?Number(g.value).toFixed(2):""},{field:"VolInCA",headerName:"Volume CA",flex:1,editable:!1,valueFormatter:g=>g.value?Number(g.value).toFixed(2):""},{field:"VolInCAR",headerName:"Volume Carton",flex:1,editable:!1,valueFormatter:g=>g.value?Number(g.value).toFixed(2):""},{field:"NoOfUnitForCA",headerName:"Number Of Unit For CA",flex:1,editable:!1,valueFormatter:g=>g.value?Number(g.value).toFixed(0):""},{field:"NoOfUnitForCT",headerName:"Number Of Unit For CT",flex:1,editable:!1,valueFormatter:g=>g.value?Number(g.value).toFixed(0):""}],Y=g=>{t(gr(g))};return d.useEffect(()=>{var g;(p==null?void 0:p.page)!==0&&y===((g=b)==null?void 0:g.FINANCE_COSTING)&&K()},[p==null?void 0:p.page]),R(Ss,{children:[n(Di,{isLoading:r,module:"FinanceCosting",width:"100%",title:"Finance Costing Details",rows:c,columns:f,getRowIdValue:"id",hideFooter:!1,checkboxSelection:!(ce&&!((z=ko)!=null&&z.includes(oe==null?void 0:oe.reqStatus))),disableSelectionOnClick:!0,tempheight:"calc(100vh - 300px)",selectionModel:re,onRowsSelectionHandler:Q,rowCount:(p==null?void 0:p.totalElements)||0,pageSize:100,onPageChange:g=>Y(g)}),n(Wr,{filteredButtons:l,setCompleted:e==null?void 0:e.setCompleted})]})},ES=()=>{var Hs,nt,cs,Js,Ws,Zt,Ue,Pt,Ps,le,Ge,Ae,Re,Ve,ke,at,xe,Bt,gs,vt;const[e,t]=d.useState(!1),[r,o]=d.useState([]),[c,s]=d.useState(!1),[p,l]=d.useState([]),[u,re]=d.useState(!1),[ae,y]=d.useState(!1),[O,K]=d.useState(""),[m,B]=d.useState(!1),[ce,oe]=d.useState([]),[Q,f]=d.useState(!1),[Y,z]=d.useState(!1),[g,T]=d.useState(""),[v,C]=d.useState(),[S,q]=d.useState(""),[A,x]=d.useState(!1),[H,k]=d.useState(""),[L,N]=d.useState("success"),[U,I]=d.useState(!1),[ge,te]=d.useState(!1),[He,M]=d.useState(!1),[we,de]=d.useState(!1),F=Ro(),qe=se(ze=>ze.applicationConfig),W=se(ze=>ze.payload.payloadData),Ke=se(ze=>{var Pe;return(Pe=ze.request.requestHeader)==null?void 0:Pe.requestId}),Ds=se(ze=>ze.request.requestHeader.requestType),Gt=se(ze=>{var Pe;return(Pe=ze.userManagement)==null?void 0:Pe.taskData}),{getDtCall:Gs,dtData:$s}=$h(),ts=wr(),[lo,Fs]=d.useState(!0),Us=se(ze=>ze.request.tabValue),{t:Kt}=Sn(),{getRequestHeaderTemplate:fs}=nf(),Dt=[Kt("Request Header"),Kt("Material List"),Kt("Attachments & Remarks"),Kt("Preview")],[rs,Jt]=d.useState([!1]),vs=ze=>{F(Lr(ze))},rt=on(),Be=rt.state,$t=new URLSearchParams(rt.search.split("?")[1]).get("RequestId"),Ns=new URLSearchParams(rt.search),Ce=Ns.get("RequestId"),dt=Ns.get("RequestType"),ls=Ns.get("reqBench"),ks=((Hs=rt.state)==null?void 0:Hs.isChildRequest)??(Ce&&!ls)??!1,{getDisplayData:is}=CT(),ss=()=>{f(!0)},it=()=>{f(!1)},xt=()=>{z(!0)},Es=ze=>{z(ze)},Ms=()=>{S==="success"?ts("/requestBench"):it()},Qt=()=>{s(!0)},as=ze=>{let Pe="";dt===b.CREATE_WITH_UPLOAD?Pe="getAllMaterialsFromExcel":dt===b.EXTEND_WITH_UPLOAD?Pe="getAllMaterialsFromExcelForMassExtend":Pe="getAllMaterialsFromExcelForMassChange",k("Initiating Excel Upload"),x(!0);const wt=new FormData;[...ze].forEach(Ft=>wt.append("files",Ft)),wt.append("dtName",dt===b.CREATE_WITH_UPLOAD||dt===b.EXTEND_WITH_UPLOAD?"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG":"MDG_MAT_CHANGE_TEMPLATE"),wt.append("version",dt===b.CREATE_WITH_UPLOAD||dt===b.EXTEND_WITH_UPLOAD?"v1":"v5"),wt.append("requestId",$t?$t.slice(3):""),wt.append("region",W!=null&&W.Region?W==null?void 0:W.Region:"US"),wt.append("matlType","ALL");const kt=Ft=>{var ee;(Ft==null?void 0:Ft.statusCode)===Vt.STATUS_200?(B(!1),x(!1),k(""),ts((ee=To)==null?void 0:ee.REQUEST_BENCH)):(B(!1),x(!1),C(Ft==null?void 0:Ft.message),k(""),N("error"),Ut())},qs=Ft=>{x(!1),C(Ft==null?void 0:Ft.message),k(""),N("error"),Ut()};Xe(`/${ve}/massAction/${Pe}`,"postformdata",kt,qs,wt)};d.useEffect(()=>((async()=>{var Pe;if(Ce){const wt=Rr(jn.CURRENT_TASK,!0,{}),kt=dt||(Gt==null?void 0:Gt.ATTRIBUTE_2)||(wt==null?void 0:wt.ATTRIBUTE_2);await is(Ce,kt,ls,Gt,Be),(dt===b.CHANGE_WITH_UPLOAD&&!((Pe=Be==null?void 0:Be.material)!=null&&Pe.length)||dt===b.CREATE_WITH_UPLOAD||dt===b.EXTEND_WITH_UPLOAD)&&((Be==null?void 0:Be.reqStatus)===Co.DRAFT||(Be==null?void 0:Be.reqStatus)===Co.UPLOAD_FAILED)?(F(Lr(0)),re(!1),y(!1)):(F(Lr(1)),re(!0),y(!0)),te(!0)}else F(Lr(0))})(),()=>{F(Wh([])),F(nT()),F(rT()),F(lT()),F(iT()),F(aT()),F(Br({})),F(fi({data:{}})),F(sr([])),F(xh([])),F(wa({})),F(cT()),F(Er([])),F(pr([])),F(Tr({})),dh(jn.CURRENT_TASK),dh(jn.ROLE)}),[$t,F]);function io(ze){let Pe={decisionTableId:null,decisionTableName:Ei.MDG_MAT_REGION_DIVISION_MAPPING,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":ze}]};Gs(Pe)}d.useEffect(()=>{W!=null&&W.Region&&io(W==null?void 0:W.Region)},[W==null?void 0:W.Region]),d.useEffect(()=>{var ze,Pe;if($s){const kt=[...dT((Pe=(ze=$s==null?void 0:$s.result)==null?void 0:ze[0])==null?void 0:Pe.MDG_MAT_REGION_DIVISION_MAPPING)].sort((qs,Ft)=>qs.code.localeCompare(Ft.code));F(xo({keyName:"Division",data:kt})),Fs(!1),k(ya.DT_LOADING)}},[$s]),d.useEffect(()=>(fs(),Qe(),F(Oo([])),F(xo({keyName:"Region",data:uT})),F(xo({keyName:"DiversionControlFlag",data:hT})),()=>{F(Fh({}))}),[]),d.useEffect(()=>{u&&Jt([!0])},[u]),d.useEffect(()=>{K(fT("MAT"))},[]);const Qe=()=>{let ze={decisionTableId:null,decisionTableName:"MDG_ATTACHMENTS_LIST_DT",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE":"Material","MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO":"Create","MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE":1}],systemFilters:null,systemOrders:null,filterString:null};t(!0);const Pe=kt=>{var qs,Ft,ee,ye;if(t(!1),kt.statusCode===200){let G=(Ft=(qs=kt==null?void 0:kt.data)==null?void 0:qs.result[0])==null?void 0:Ft.MDG_ATTACHMENTS_ACTION_TYPE,ue=[];G==null||G.map((Te,je)=>{var ie={id:je};ue.push(ie)}),l(ue);const w=((ye=(ee=kt==null?void 0:kt.data)==null?void 0:ee.result[0])==null?void 0:ye.MDG_ATTACHMENTS_ACTION_TYPE)||[];oe(w)}},wt=kt=>{console.log(kt)};qe.environment==="localhost"?Xe(`/${yo}/rest/v1/invoke-rules`,"post",Pe,wt,ze):Xe(`/${yo}/v1/invoke-rules`,"post",Pe,wt,ze)},Lt=()=>{var Ft,ee,ye,G,ue,w;const ze=Ce!=null&&Ce.includes("FCA")?We.EXCEL.DOWNLOAD_EXCEL_FINANCE:We.EXCEL.DOWNLOAD_EXCEL_MAT;k("Please wait 1-2 minutes while real-time data are exported into the template. Thank you for your patience."),x(!0);let Pe={massSchedulingId:W==null?void 0:W.RequestId},wt={dtName:(W==null?void 0:W.RequestType)===((Ft=b)==null?void 0:Ft.CHANGE)||(W==null?void 0:W.RequestType)===((ee=b)==null?void 0:ee.CHANGE_WITH_UPLOAD)?"MDG_MAT_CHANGE_TEMPLATE":"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",version:(W==null?void 0:W.RequestType)===((ye=b)==null?void 0:ye.CHANGE)||(W==null?void 0:W.RequestType)===((G=b)==null?void 0:G.CHANGE_WITH_UPLOAD)?"v4":"v1",requestId:(W==null?void 0:W.RequestId)||Ke||"",scenario:(W==null?void 0:W.RequestType)===((ue=b)==null?void 0:ue.CHANGE)||(W==null?void 0:W.RequestType)===((w=b)==null?void 0:w.CHANGE_WITH_UPLOAD)?"Change with Upload":"Create with Upload",templateName:(W==null?void 0:W.TemplateName)||"",region:(W==null?void 0:W.Region)||"",matlType:"ALL"};const kt=Te=>{const je=URL.createObjectURL(Te),ie=document.createElement("a");ie.href=je,ie.setAttribute("download",`${W!=null&&W.TemplateName?W==null?void 0:W.TemplateName:Ce!=null&&Ce.includes("FCA")?b.FINANCE_COSTING:"Mass_Create"}_Data Export.xlsx`),document.body.appendChild(ie),ie.click(),document.body.removeChild(ie),URL.revokeObjectURL(je),x(!1),k(""),C(`${W!=null&&W.TemplateName?W==null?void 0:W.TemplateName:Ce!=null&&Ce.includes("FCA")?b.FINANCE_COSTING:"Mass_Create"}_Data Export.xlsx has been exported successfully.`),N("success"),Ut()},qs=()=>{};Xe(`/${ve}${ze}`,"postandgetblob",kt,qs,Ce!=null&&Ce.includes("FCA")?Pe:wt)},Ut=()=>{I(!0)},Nt=()=>{I(!1)},ms=()=>{var ze,Pe,wt;$t&&!ls?ts((ze=To)==null?void 0:ze.MY_TASK):ls?ts((Pe=To)==null?void 0:Pe.REQUEST_BENCH):!$t&&!ls&&ts((wt=To)==null?void 0:wt.MASTER_DATA)},Ks=()=>{de(!1)};return R(Ss,{children:[lo&&n(An,{blurLoading:A,loaderMessage:H}),R(Le,{sx:{padding:2},children:[R(Rs,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[Ke||$t?R(mt,{variant:"h6",sx:{mb:1,textAlign:"left",display:"flex",alignItems:"center",gap:1},children:[n(Cg,{sx:{fontSize:"1.5rem"}}),Kt("Request Header ID"),": ",n("span",{children:Ke?ua(Ds,Ke):$t})]}):n("div",{style:{flex:1}}),Us===1&&R(Le,{sx:{display:"flex",justifyContent:"flex-end",gap:"1rem"},children:[n(Rt,{variant:"outlined",size:"small",title:Kt("Download Error Report"),disabled:!Ce,onClick:()=>{ts(`/requestBench/errorHistory?RequestId=${Ce||""}`,{state:{display:!0,childRequest:ks}})},color:"primary",children:n(gT,{sx:{padding:"2px"}})}),(W==null?void 0:W.RequestType)===b.CREATE||(W==null?void 0:W.RequestType)===b.EXTEND||(W==null?void 0:W.RequestType)===b.EXTEND_WITH_UPLOAD||(W==null?void 0:W.RequestType)===b.CREATE_WITH_UPLOAD||$t!=null&&$t.includes("FCA")?n(Rt,{variant:"outlined",disabled:!Ce,size:"small",onClick:()=>M(!0),title:$t!=null&&$t.includes("FCA")?Kt("Finance Costing Change Log"):Kt("Create Change Log"),children:n(uh,{sx:{padding:"2px"}})}):n(Rt,{variant:"outlined",disabled:!Ce,size:"small",onClick:xt,title:Kt("Change Log"),children:n(uh,{sx:{padding:"2px"}})}),n(Rt,{variant:"outlined",disabled:!Ce,size:"small",onClick:Lt,title:Kt("Export Excel"),children:n(AT,{sx:{padding:"2px"}})})]}),Y&&n(HT,{open:!0,closeModal:Es,requestId:Ke||$t.slice(3),requestType:W==null?void 0:W.RequestType}),He&&n(fb,{open:!0,closeModal:()=>M(!1),requestId:Ke||$t.slice(3),requestType:W==null?void 0:W.RequestType})]}),(W==null?void 0:W.TemplateName)&&R(mt,{variant:"h6",sx:{mb:1,textAlign:"left",display:"flex",alignItems:"center",gap:1},children:[n(La,{sx:{fontSize:"1.5rem"}}),Kt("Template Name"),": ",n("span",{children:W==null?void 0:W.TemplateName})]}),n(xs,{onClick:()=>{var ze,Pe;if(ls&&!((ze=ko)!=null&&ze.includes(W==null?void 0:W.RequestStatus))){ts((Pe=To)==null?void 0:Pe.REQUEST_BENCH);return}de(!0)},color:"primary","aria-label":"upload picture",component:"label",sx:{left:"-10px"},title:Kt("Back"),children:n(fa,{sx:{fontSize:"25px",color:"#000000"}})}),n(WT,{nonLinear:!0,activeStep:Us,sx:{display:"flex",alignItems:"center",justifyContent:"center",margin:"25px 14%",marginTop:"-35px"},children:Dt.map((ze,Pe)=>n(FT,{children:n(jT,{color:"error",disabled:Pe===1&&!u||Pe===2&&!ae||Pe===3&&!ae,onClick:()=>vs(Pe),sx:{fontSize:"50px",fontWeight:"bold"},children:n("span",{style:{fontSize:"15px",fontWeight:"bold"},children:ze})})},ze))}),n(ki,{dialogState:Q,openReusableDialog:ss,closeReusableDialog:it,dialogTitle:g,dialogMessage:v,handleDialogConfirm:it,dialogOkText:"OK",handleOk:Ms,dialogSeverity:S}),n(An,{blurLoading:A,loaderMessage:H}),Us===0&&R(Ss,{children:[n(pE,{setIsSecondTabEnabled:re,setIsAttachmentTabEnabled:y,requestStatus:Be!=null&&Be.reqStatus?Be==null?void 0:Be.reqStatus:Co.ENABLE_FOR_FIRST_TIME,downloadClicked:c,setDownloadClicked:s}),(dt===b.CHANGE_WITH_UPLOAD||dt===b.CREATE_WITH_UPLOAD||dt===b.EXTEND_WITH_UPLOAD)&&((Be==null?void 0:Be.reqStatus)==Co.DRAFT&&!((nt=Be==null?void 0:Be.material)!=null&&nt.length)||(Be==null?void 0:Be.reqStatus)==Co.UPLOAD_FAILED)&&n(gb,{handleDownload:Qt,setEnableDocumentUpload:B,enableDocumentUpload:m,handleUploadMaterial:as}),((W==null?void 0:W.RequestType)===((cs=b)==null?void 0:cs.CHANGE)||(W==null?void 0:W.RequestType)===((Js=b)==null?void 0:Js.CHANGE_WITH_UPLOAD))&&!Ce&&(W==null?void 0:W.DirectAllowed)!=="X"&&(W==null?void 0:W.DirectAllowed)!==void 0&&R(mt,{sx:{fontSize:"13px",fontWeight:"500",color:(Zt=(Ws=$e)==null?void 0:Ws.error)==null?void 0:Zt.dark,marginTop:"1rem",marginLeft:"0.5rem"},children:[n(Le,{component:"span",sx:{fontWeight:"bold"},children:"Note:"})," ","You are not authorized to Tcode"," ",R(Le,{component:"span",sx:{fontWeight:"bold"},children:[" ","MM02."]})]})]}),Us===1&&((W==null?void 0:W.RequestType)===((Ue=b)==null?void 0:Ue.CREATE)||(Gt==null?void 0:Gt.ATTRIBUTE_2)===((Pt=b)==null?void 0:Pt.CREATE)||dt===((Ps=b)==null?void 0:Ps.CREATE)||dt===((le=b)==null?void 0:le.CREATE_WITH_UPLOAD)?n(CA,{requestStatus:Be!=null&&Be.reqStatus?Be==null?void 0:Be.reqStatus:Co.ENABLE_FOR_FIRST_TIME,mandFields:r,addHardCodeData:ge,setIsAttachmentTabEnabled:y,setCompleted:Jt}):(W==null?void 0:W.RequestType)===((Ge=b)==null?void 0:Ge.EXTEND)||(Gt==null?void 0:Gt.ATTRIBUTE_2)===((Ae=b)==null?void 0:Ae.EXTEND)||(Gt==null?void 0:Gt.ATTRIBUTE_2)===((Re=b)==null?void 0:Re.EXTEND_WITH_UPLOAD)||dt===((Ve=b)==null?void 0:Ve.EXTEND)||dt===((ke=b)==null?void 0:ke.EXTEND_WITH_UPLOAD)?n(_A,{requestStatus:Be!=null&&Be.reqStatus?Be==null?void 0:Be.reqStatus:Co.ENABLE_FOR_FIRST_TIME,mandFields:r,addHardCodeData:ge,setIsAttachmentTabEnabled:y,setCompleted:Jt}):(W==null?void 0:W.RequestType)===((at=b)==null?void 0:at.FINANCE_COSTING)||(Gt==null?void 0:Gt.ATTRIBUTE_2)===((xe=b)==null?void 0:xe.FINANCE_COSTING)||dt===((Bt=b)==null?void 0:Bt.FINANCE_COSTING)?n(pb,{setCompleted:Jt}):n(uf,{setIsAttachmentTabEnabled:!0,setCompleted:Jt,downloadClicked:c,setDownloadClicked:s})),Us===2&&n(vA,{requestStatus:Be!=null&&Be.reqStatus?Be==null?void 0:Be.reqStatus:Co.ENABLE_FOR_FIRST_TIME,attachmentsData:ce,requestIdHeader:Ke?ua(Ds,Ke):$t,pcNumber:O}),Us===3&&n(Le,{sx:{width:"100%",overflow:"auto"},children:n(ib,{requestStatus:Be!=null&&Be.reqStatus?Be==null?void 0:Be.reqStatus:Co.ENABLE_FOR_FIRST_TIME})})]}),n(Hn,{openSnackBar:U,alertMsg:v,alertType:L,handleSnackBarClose:Nt}),we&&R(Pi,{isOpen:we,titleIcon:n(pT,{size:"small",sx:{color:(vt=(gs=$e)==null?void 0:gs.secondary)==null?void 0:vt.amber,fontSize:"20px"}}),Title:Kt("Warning"),handleClose:Ks,children:[n(Lo,{sx:{mt:2},children:Kt(qn.LEAVE_PAGE_MESSAGE)}),R(Do,{children:[n(Rt,{variant:"outlined",size:"small",sx:{..._a},onClick:Ks,children:Kt("No")}),n(Rt,{variant:"contained",size:"small",sx:{...qi},onClick:ms,children:Kt("Yes")})]})]})]})};export{ES as default};
