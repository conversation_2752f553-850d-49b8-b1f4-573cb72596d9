import{m as Br,n as Mr,o as jr,r as c,s as zr,b as qr,u as Or,q as y,bh as _r,cd as $r,K as S,c0 as z,a as n,j as a,b4 as Q,T as d,B as N,br as Z,G as i,aC as un,bt as xn,bs as de,x as D,ab as gn,F as I,bX as Lr,C as Wr,z as fn,V as Cn,aF as bn,I as me,aG as yn,W as Sn,E as vn,X as Pn,t as g,ar as Vr,cb as Hr,cc as Ur,al as Rr,b1 as kn,bm as ve,b8 as R,aD as Pe,aE as ke,aB as F,cn as he,cw as Yr,bp as Kr,ai as Te,bq as Ae}from"./index-75c1660a.js";import{d as Xr}from"./ArrowCircleLeftOutlined-f7b52d40.js";import{d as pe}from"./EditOutlined-6971b85d.js";import{E as ee}from"./EditableFieldForProfitCenter-ec1c5c8a.js";import{C as Jr}from"./CompCodeProfitCenter-d03b994f.js";import{M as Gr,a as Qr}from"./UtilDoc-7fb813ce.js";import{l as Tn}from"./lookup-1dcf10ac.js";import{R as Zr}from"./ReusableAttachementAndComments-682b0475.js";import{T as An}from"./Timeline-b8b4e1ba.js";import{t as Nn,T as In,a as Fn,b as En,c as wn,d as Bn}from"./TimelineSeparator-6e03ad1b.js";import{S as Dr,a as eo,b as no}from"./Stepper-2dbfb76b.js";import"./DatePicker-31fef6b6.js";import"./dateViewRenderers-dbe02df3.js";import"./useSlotProps-da724f1f.js";import"./InputAdornment-a22e1655.js";import"./CSSTransition-8d766865.js";import"./useMediaQuery-33e0a836.js";import"./DesktopDatePicker-47a97548.js";import"./useMobilePicker-056b38fc.js";/* empty css            */import"./FileDownloadOutlined-329b8f56.js";import"./VisibilityOutlined-a5a8c4d9.js";import"./DeleteOutlined-fe5b7345.js";import"./Slider-c4e5ff46.js";import"./CloudUpload-d5d09566.js";import"./utilityImages-067c3dc2.js";import"./Add-62a207fb.js";import"./Delete-1d158507.js";import"./clsx-a965ebfb.js";var Ne={},ro=Mr;Object.defineProperty(Ne,"__esModule",{value:!0});var Mn=Ne.default=void 0,oo=ro(Br()),to=jr;Mn=Ne.default=(0,oo.default)((0,to.jsx)("path",{d:"M22 6c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2zm-2 0-8 5-8-5zm0 12H4V8l8 5 8-5z"}),"MarkunreadOutlined");const $o=()=>{var Je,Ge,Qe,Ze,De,en,nn,rn,on,tn,sn,ln,an,cn,dn,mn;const[C,Ie]=c.useState(!1);c.useState(0);const[Y,jn]=c.useState(!0);c.useState({});const[P,zn]=c.useState([]),[qn,On]=c.useState([]),[b,_n]=c.useState();c.useState([]);const[f,ne]=c.useState(0),[Fe,k]=c.useState(""),[$n,T]=c.useState(!1),[so,E]=c.useState(!0),[Ln,w]=c.useState(!1),[lo,B]=c.useState(!1),[Wn,ue]=c.useState(!1),[Vn,M]=c.useState(!1),[Hn,re]=c.useState(!1),[Un,xe]=c.useState(!1),[Rn,v]=c.useState(!1),[Yn,Ee]=c.useState(!1),[Kn,Xn]=c.useState(!1),[Jn,we]=c.useState(!1),[oe,Gn]=c.useState(""),[Qn,Zn]=c.useState(!1),[Dn,er]=c.useState(""),[te,Be]=c.useState(""),[K,nr]=c.useState([]),[W,io]=c.useState([]),[V,ao]=c.useState([]),[Me,se]=c.useState(!0),[je,ze]=c.useState(!0),[rr,or]=c.useState([]),[qe,tr]=c.useState([]),[sr,Oe]=c.useState(!1),j=zr(),_e=qr(),lr=Or();y(r=>r.appSettings);let ge=y(r=>{var l;return(l=r.userManagement.entitiesAndActivities)==null?void 0:l["Profit Center"]}),h=y(r=>r.userManagement.userData),H=y(r=>{var l;return(l=r==null?void 0:r.initialData)==null?void 0:l.IWMMyTask}),t=lr.state;console.log("profitCenterRowData",t),console.log("testrunStatus",je),y(r=>r.costCenter.singleCCPayload);const fe=y(r=>r.profitCenter.profitCenterViewData),ir=y(r=>r.profitCenter.profitCenterCompCodes),e=y(r=>r.edit.payload);let U=y(r=>r.userManagement.taskData),q=y(r=>r.edit.payload);console.log(q,"singlePCPayloadAfterChange");let $e=y(r=>r.profitCenter.requiredFields);console.log($e,"required_field_for_data");var le={TaskId:U!=null&&U.taskId?U==null?void 0:U.taskId:"",ProfitCenterID:b!=null&&b.profitCenterId?b==null?void 0:b.profitCenterId:"",RequestID:"",Action:"I",TaskStatus:"",ReqCreatedBy:h==null?void 0:h.user_id,ReqCreatedOn:"",RequestStatus:"",CreationId:t!=null&&t.requestId?t==null?void 0:t.requestId:"",EditId:"",DeleteId:"",MassCreationId:"",MassEditId:"",MassDeleteId:"",RequestType:"Create",MassRequestStatus:"",Remarks:te||"",PrctrName:e!=null&&e.Name?e==null?void 0:e.Name:"",LongText:e!=null&&e.LongText?e==null?void 0:e.LongText:"",InChargeUser:e!=null&&e.UserResponsible?e==null?void 0:e.UserResponsible:"",InCharge:e!=null&&e.PersonResponsible?e==null?void 0:e.PersonResponsible:"",Department:e!=null&&e.Department?e==null?void 0:e.Department:"",PrctrHierGrp:e!=null&&e.ProfitCtrGroup?e==null?void 0:e.ProfitCtrGroup:"",Segment:e!=null&&e.Segment?e==null?void 0:e.Segment:"",LockInd:(e==null?void 0:e.Lockindicator)==="true"?"X":"",Template:e!=null&&e.FormPlanningTemp?e==null?void 0:e.FormPlanningTemp:"",Title:e!=null&&e.Title?e==null?void 0:e.Title:"",Name1:e!=null&&e.Name1?e==null?void 0:e.Name1:"",Name2:e!=null&&e.Name2?e==null?void 0:e.Name2:"",Name3:e!=null&&e.Name3?e==null?void 0:e.Name3:"",Name4:e!=null&&e.Name4?e==null?void 0:e.Name4:"",Street:e!=null&&e.Street?e==null?void 0:e.Street:"",City:e!=null&&e.City?e==null?void 0:e.City:"",District:e!=null&&e.District?e==null?void 0:e.District:"",Country:e!=null&&e.CountryReg?e==null?void 0:e.CountryReg:"",Taxjurcode:e!=null&&e.TaxJur?e==null?void 0:e.TaxJur:"",PoBox:e!=null&&e.POBox?e==null?void 0:e.POBox:"",PostlCode:e!=null&&e.PostalCode?e==null?void 0:e.PostalCode:"",PobxPcd:e!=null&&e.POBoxPCode?e==null?void 0:e.POBoxPCode:"",Region:e!=null&&e.Region?e==null?void 0:e.Region:"",Langu:e!=null&&e.Language?e==null?void 0:e.Language:"EN",Telephone:e!=null&&e.Telephone1?e==null?void 0:e.Telephone1:"",Telephone2:e!=null&&e.Telephone2?e==null?void 0:e.Telephone2:"",Telebox:e!=null&&e.Telebox?e==null?void 0:e.Telebox:"",Telex:e!=null&&e.Telex?e==null?void 0:e.Telex:"",FaxNumber:e!=null&&e.FaxNumber?e==null?void 0:e.FaxNumber:"",Teletex:e!=null&&e.Teletex?e==null?void 0:e.Teletex:"",Printer:e!=null&&e.Printername?e==null?void 0:e.Printername:"",DataLine:e!=null&&e.Dataline?e==null?void 0:e.Dataline:"",ProfitCenter:`P${(Ge=(Je=t==null?void 0:t.companyCodeCopy)==null?void 0:Je.newCompanyCodeCopy)==null?void 0:Ge.code}${(Qe=t==null?void 0:t.profitCenterName)==null?void 0:Qe.newProfitCenterName}`?`P${(De=(Ze=t==null?void 0:t.companyCodeCopy)==null?void 0:Ze.newCompanyCodeCopy)==null?void 0:De.code}${(en=t==null?void 0:t.profitCenterName)==null?void 0:en.newProfitCenterName}`:"",ControllingArea:(rn=(nn=t==null?void 0:t.controllingArea)==null?void 0:nn.newControllingArea)!=null&&rn.code?(tn=(on=t==null?void 0:t.controllingArea)==null?void 0:on.newControllingArea)==null?void 0:tn.code:"",ValidfromDate:e!=null&&e.AnalysisPeriodFrom?"/Date("+(e==null?void 0:e.AnalysisPeriodFrom)+")/":"",ValidtoDate:e!=null&&e.AnalysisPeriodTo?"/Date("+(e==null?void 0:e.AnalysisPeriodTo)+")/":"",Testrun:je,Countryiso:"",LanguIso:"",Logsystem:"",ToCompanycode:K==null?void 0:K.map(r=>({CompCodeID:"",CompanyName:r!=null&&r.companyName?r==null?void 0:r.companyName:"",AssignToPrctr:"X",CompCode:r!=null&&r.companyCodes?r==null?void 0:r.companyCodes:""}))};const[Le,ar]=c.useState(0),cr=(r,l)=>{const o=m=>{j(Te({keyName:r,data:m.body})),ar(p=>p+1)},s=m=>{console.log(m)};S(`/${z}/data/${l}`,"get",o,s)},dr=()=>{var r,l;(l=(r=Tn)==null?void 0:r.profitCenter)==null||l.map(o=>{cr(o==null?void 0:o.keyName,o==null?void 0:o.endPoint)})},mr=()=>{var r,l;Le==((l=(r=Tn)==null?void 0:r.profitCenter)==null?void 0:l.length)?M(!1):M(!0)};c.useEffect(()=>{mr()},[Le]),c.useEffect(()=>{Gn(_r("PC"))},[]),c.useEffect(()=>{dr(),ur()},[]),c.useEffect(()=>{Cr(),fe.length!==0&&hr()},[fe]);const We=[{field:"id",headerName:"Document ID",flex:1,hide:!0},{field:"docType",headerName:"Type",flex:1},{field:"docName",headerName:"Document Name",flex:1},{field:"uploadedOn",headerName:"Uploaded On",flex:1,align:"center",headerAlign:"center"},{field:"uploadedBy",headerName:"Uploaded By",sortable:!1,flex:1},{field:"action",headerName:"Action",sortable:!1,filterable:!1,align:"center",headerAlign:"center",flex:1,renderCell:r=>a(I,{children:[n(Gr,{index:r.row.id,name:r.row.docName}),n(Qr,{index:r.row.id,name:r.row.docName})]})}],hr=()=>{let r=P[f];console.log("activeTabName",r,P);let l=Object.entries(fe);console.log("viewDataArray",l);const o={};l.map(s=>{console.log("bottle",s[1]);let m=Object.entries(s[1]);return console.log("notebook",m),m.forEach(p=>{p[1].forEach(u=>{o[u.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("")]=u.value})}),s}),console.log("toSetArray",o),j($r(o))},pr=r=>{console.log("compcode",r);const l=s=>{console.log("value",s),j(Te({keyName:"Region",data:s.body}))},o=s=>{console.log(s,"error in dojax")};S(`/${z}/data/getRegionBasedOnCountry?country=${r}`,"get",l,o)},ur=()=>{var s,m,p,u,x,$,X,J;M(!0);var r={id:"",profitCenter:(m=(s=t==null?void 0:t.profitCenter)==null?void 0:s.newProfitCenter)!=null&&m.code?(u=(p=t==null?void 0:t.profitCenter)==null?void 0:p.newProfitCenter)==null?void 0:u.code:"",controllingArea:($=(x=t==null?void 0:t.controllingArea)==null?void 0:x.newControllingArea)!=null&&$.code?(J=(X=t==null?void 0:t.controllingArea)==null?void 0:X.newControllingArea)==null?void 0:J.code:"",reqStatus:"Approved",screenName:"Create"};console.log("displayPayload",r);const l=L=>{var pn;M(!1);const G=L.body.viewData,Fr=L.body;j(Yr(G));const hn=Object.keys(G);zn(hn);const Er=hn.map(A=>({category:A,data:G[A],setIsEditMode:Ie}));On(Er),fr(G),_n(Fr);let Se=(pn=L==null?void 0:L.body)==null?void 0:pn.viewData["Comp Codes"]["Company Code Assignment for Profit Center"];nr(_.zip(Se[0].value,Se[1].value,Se[2].value).map((A,wr)=>({id:wr,companyCodes:A[0].split("$")[0],companyName:A[1],assigned:A[2]}))),pr(G.Address["Address Data"].find(A=>(A==null?void 0:A.fieldName)==="Country/Reg.").value)},o=L=>{console.log(L)};S(`/${z}/data/displayProfitCenter`,"post",l,o,r)},xr=y(r=>r.profitCenter.requiredFields),gr=()=>{Oe(!1)},Ve=()=>Kr(q,$e,tr);console.log(xr,"requiredFields"),console.log(rr,"error_field_arr");const fr=r=>{let l=[];for(const o in r){if(r.hasOwnProperty(o)){for(const s in r[o])if(r[o].hasOwnProperty(s)){const m=r[o][s];for(const p of m)if(p.visibility==="0"||p.visibility==="Required"){console.log(p.fieldName,"field.fieldName");let u=p.fieldName.replace(/\s/g,"");l.push(u)}}}or(s=>({...s,error_field_arr:l}))}};console.log("dispcomp",K);const ie=()=>{ue(!0)},Cr=()=>{var o,s,m;const r=p=>{j(Te({keyName:"ProfitCtrGroup",data:p.body}))},l=p=>{console.log(p)};S(`/${z}/data/getProfitCtrGroup?controllingArea=${((o=H==null?void 0:H.body)==null?void 0:o.controllingArea)||((m=(s=t==null?void 0:t.controllingArea)==null?void 0:s.newControllingArea)==null?void 0:m.code)}`,"get",r,l)},ae=()=>{Ie(!0),jn(!1)},He=()=>{Oe(!0)},Ce=()=>{se(!0);const r=Ve();C?r?(ne(l=>l-1),j(he())):He():(ne(l=>l-1),j(he()))},be=()=>{const r=Ve();C?r?(ne(l=>l+1),j(he())):He():(ne(l=>l+1),j(he()))},O=()=>{xe(!0)},br=()=>{xe(!1)},yr=()=>{T(!1),O(),v("Confirm"),k("Do You Want to Save as Draft ?"),Zn(!0),er("proceed")},Sr=()=>{const r=o=>{if(M(!1),o.statusCode===200){console.log("success"),v("Create"),k(`Profit Center Saved As Draft with ID NPS${o.body} `),T("success"),E(!1),w(!0),ie(),B(!0);const s={artifactId:oe,createdBy:h==null?void 0:h.emailId,artifactType:"ProfitCenter",requestId:`NPS${o==null?void 0:o.body}`},m=u=>{console.log("Second API success",u)},p=u=>{console.error("Second API error",u)};S(`/${Ae}/documentManagement/updateDocRequestId`,"post",m,p,s)}else v("Error"),w(!1),k("Failed Saving Profit Center"),T("danger"),E(!1),B(!0),O();handleClose()},l=o=>{console.log(o)};S(`/${z}/alter/profitCenterAsDraft`,"post",r,l,le)},vr=()=>{const r=o=>{if(M(!1),o.statusCode===200){console.log("success"),v("Create"),k(`Profit Center Submitted for Review with ID CPR${o.body} `),T("success"),E(!1),w(!0),ie(),B(!0);const s={artifactId:oe,createdBy:h==null?void 0:h.emailId,artifactType:"ProfitCenter",requestId:`CPR${o==null?void 0:o.body}`},m=u=>{console.log("Second API success",u)},p=u=>{console.error("Second API error",u)};S(`/${Ae}/documentManagement/updateDocRequestId`,"post",m,p,s)}else ze(!0),se(!0),v("Error"),w(!1),k("Failed Submitting Profit Center"),T("danger"),E(!1),B(!0),O();handleClose()},l=o=>{console.log(o)};S(`/${z}/alter/changeProfitCenterSubmitForReview`,"post",r,l,le)},Ue=()=>{var p,u;re(!0);const r={coArea:b!=null&&b.controllingArea?b==null?void 0:b.controllingArea:(p=H==null?void 0:H.body)==null?void 0:p.controllingArea,name:q!=null&&q.Name?(u=q==null?void 0:q.Name)==null?void 0:u.toUpperCase():""},l=x=>{var $,X,J;x.statusCode===201?(v("Create"),v("Create"),k("All Data has been Validated. Cost Center can be Sent for Review"),T("success"),E(!1),w(!0),ie(),B(!0),Ee(!0),(r.coArea!==""||r.name!=="")&&S(`/${z}/alter/fetchPCDescriptionDupliChk`,"post",o,s,r)):(re(!1),v("Error"),w(!1),k(`${($=x==null?void 0:x.body)!=null&&$.message[0]?(X=x==null?void 0:x.body)==null?void 0:X.message[0]:(J=x==null?void 0:x.body)==null?void 0:J.value}`),T("danger"),E(!1),B(!0),O())},o=x=>{x.body.length===0||!x.body.some($=>$.toUpperCase()===r.name)?(re(!1),se(!1),ze(!1)):(re(!1),v("Duplicate Check"),w(!1),k("There is a direct match for the Profit Center name. Please change the name."),T("danger"),E(!1),B(!0),O(),se(!0))},s=x=>{console.log(x)},m=x=>{console.log(x)};S(`/${z}/alter/validateSingleProfitCenter`,"post",l,m,le)},Pr=()=>{Yn?(ue(!1),Ee(!1)):(ue(!1),_e("/masterDataCockpit/profitCenter"))},Re=()=>{xe(!1)},Ye=()=>{yr()},kr=()=>{M(!0),vr()},Tr=()=>{},Ar=()=>{M(!0);const r=o=>{if(M(!1),o.statusCode===200){v("Create"),k(`Profit Center has been Submitted for Review NPS${o.body}`),T("success"),E(!1),w(!0),ie(),B(!0),console.log("secondapi");const s={artifactId:oe,createdBy:h==null?void 0:h.emailId,artifactType:"ProfitCenter",requestId:`NPS${o==null?void 0:o.body}`},m=u=>{console.log("Second API success",u)},p=u=>{console.error("Second API error",u)};S(`/${Ae}/documentManagement/updateDocRequestId`,"post",m,p,s)}else v("Create"),w(!1),k("Creation Failed"),T("danger"),E(!1),B(!0),O();handleClose()},l=o=>{console.log(o)};S(`/${z}/alter/profitCenterSubmitForReview`,"post",r,l,le)},Nr=()=>{ce(),Ar()},Ir=()=>{we(!0)},ce=()=>{we(!1)},ye=()=>{Xn(!1)},Ke=(r,l)=>{const o=r.target.value;if(o.length>0&&o[0]===" ")Be(o.trimStart());else{let s=o.toUpperCase();Be(s)}};console.log("factorsarray",P);const Xe=P.map(r=>{const l=qn.filter(o=>{var s;return((s=o.category)==null?void 0:s.split(" ")[0])==(r==null?void 0:r.split(" ")[0])});if(l.length!=0)return{category:r==null?void 0:r.split(" ")[0],data:l[0].data}}).map((r,l)=>{if((r==null?void 0:r.category)=="Basic"&&f==0)return[n(i,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(r.data).map(o=>a(i,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Q},children:[n(d,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),n(N,{sx:{width:"100%"},children:n(Z,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:n(i,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:r.data[o].map(s=>(console.log("fieldDatatttt",s),n(ee,{label:s.fieldName,value:s.value,length:s.maxLength,data:e,visibility:s.visibility,onSave:m=>handleFieldSave(s.fieldName,m),isEditMode:C,type:s.fieldType,field:s})))})})})]},o))},r.category)];if((r==null?void 0:r.category)=="Indicators"&&f==1)return[n(i,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(r.data).map(o=>a(i,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Q},children:[n(d,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),n(N,{sx:{width:"100%"},children:n(Z,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:n(i,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:r.data[o].map(s=>n(ee,{label:s.fieldName,value:s.value,data:e,onSave:m=>handleFieldSave(s.fieldName,m),visibility:s.visibility,isEditMode:C,type:s.fieldType,field:s}))})})})]},o))},r.category)];if((r==null?void 0:r.category)=="Comp"&&f==2)return[n(i,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:n(Jr,{compCodesTabDetails:ir,displayCompCode:K})},r.category)];if((r==null?void 0:r.category)=="Address"&&f==3)return[n(i,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(r.data).map(o=>a(i,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Q},children:[n(d,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),n(N,{sx:{width:"100%"},children:n(Z,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:n(i,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:r.data[o].map(s=>n(ee,{label:s.fieldName,value:s.value,data:e,onSave:m=>handleFieldSave(s.fieldName,m),visibility:s.visibility,isEditMode:C,type:s.fieldType,field:s}))})})})]},o))},r.category)];if((r==null?void 0:r.category)=="Communication"&&f==4)return[n(i,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(r.data).map(o=>a(i,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Q},children:[n(d,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),n(N,{sx:{width:"100%"},children:n(Z,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:n(i,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:r.data[o].map(s=>n(ee,{label:s.fieldName,value:s.value,data:e,onSave:m=>handleFieldSave(s.fieldName,m),visibility:s.visibility,isEditMode:C,type:s.fieldType,field:s}))})})})]},o))},r.category)];if((r==null?void 0:r.category)=="History"&f==5)return[n(i,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(r.data).map(o=>a(i,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Q},children:[n(d,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),n(N,{sx:{width:"100%"},children:n(Z,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:n(i,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:r.data[o].map(s=>n(ee,{label:s.fieldName,value:s.value,data:e,onSave:m=>handleFieldSave(s.fieldName,m),visibility:s.visibility,isEditMode:C,type:s.fieldType,field:s}))})})})]},o))},r.category)];if((r==null?void 0:r.category)=="Attachments"&&f==6)return[n(I,{children:C?a(I,{children:[n(Zr,{title:"ProfitCenter",useMetaData:!1,artifactId:oe,artifactName:"ProfitCenter"}),a(de,{sx:{padding:"1rem 1rem 0rem 1rem"},children:[n(i,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:n(d,{variant:"h6",children:n("strong",{children:"Attachments"})})}),!!V.length&&n(un,{width:"100%",rows:V,columns:We,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action"}),!V.length&&n(d,{variant:"body2",children:"No Attachments Found"}),n("br",{}),n(d,{variant:"h6",children:"Comments"}),!!W.length&&n(An,{sx:{[`& .${Nn.root}:before`]:{flex:0,padding:0}},children:W.map(o=>a(In,{children:[a(Fn,{children:[n(En,{children:n(xn,{sx:{color:"#757575"}})}),n(wn,{})]}),n(Bn,{sx:{py:"12px",px:2},children:n(de,{elevation:0,sx:{border:1,borderColor:"#C4C4C4",borderRadius:"8px",width:"650px"},children:n(N,{sx:{padding:"1rem"},children:a(D,{spacing:1,children:[n(i,{sx:{display:"flex",justifyContent:"space-between"},children:n(d,{sx:{textAlign:"right",color:" #757575",fontWeight:"500",fontSize:"12px"},children:gn(o.createdAt).format("DD MMM YYYY")})}),n(d,{sx:{fontSize:"12px",color:" #757575",fontWeight:"500"},children:o.user}),n(d,{sx:{fontSize:"12px",color:"#1D1D1D",fontWeight:"600"},children:o.comment})]})})})})]}))}),!W.length&&n(d,{variant:"body2",children:"No Comments Found"}),n("br",{})]})]}):a(de,{sx:{padding:"1rem 1rem 0rem 1rem"},children:[n(i,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:n(d,{variant:"h6",children:n("strong",{children:"Attachments"})})}),!!V.length&&n(un,{width:"100%",rows:V,columns:We,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action"}),!V.length&&n(d,{variant:"body2",children:"No Attachments Found"}),n("br",{}),n(d,{variant:"h6",children:"Comments"}),!!W.length&&n(An,{sx:{[`& .${Nn.root}:before`]:{flex:0,padding:0}},children:W.map(o=>a(In,{children:[a(Fn,{children:[n(En,{children:n(xn,{sx:{color:"#757575"}})}),n(wn,{})]}),n(Bn,{sx:{py:"12px",px:2},children:n(de,{elevation:0,sx:{border:1,borderColor:"#C4C4C4",borderRadius:"8px",width:"650px"},children:n(N,{sx:{padding:"1rem"},children:a(D,{spacing:1,children:[n(i,{sx:{display:"flex",justifyContent:"space-between"},children:n(d,{sx:{textAlign:"right",color:" #757575",fontWeight:"500",fontSize:"12px"},children:gn(o.createdAt).format("DD MMM YYYY")})}),n(d,{sx:{fontSize:"12px",color:" #757575",fontWeight:"500"},children:o.user}),n(d,{sx:{fontSize:"12px",color:"#1D1D1D",fontWeight:"600"},children:o.comment})]})})})})]}))}),!W.length&&n(d,{variant:"body2",children:"No Comments Found"}),n("br",{})]})})]});return n(I,{children:Vn===!0?n(Lr,{}):a("div",{style:{backgroundColor:"#FAFCFF"},children:[n(Wr,{dialogState:Un,openReusableDialog:O,closeReusableDialog:Re,dialogTitle:Rn,dialogMessage:Fe,handleDialogConfirm:Re,dialogOkText:"OK",dialogSeverity:$n,showCancelButton:!0,handleDialogReject:br,handleExtraText:Dn,showExtraButton:Qn,handleExtraButton:Sr}),Ln&&n(fn,{openSnackBar:Wn,alertMsg:Fe,handleSnackBarClose:Pr}),qe.length!=0&&n(fn,{openSnackBar:sr,alertMsg:"Please fill the following Field: "+qe.join(", "),handleSnackBarClose:gr}),a(Cn,{open:Kn,onClose:ye,sx:{"&::webkit-scrollbar":{width:"1px"}},fullWidth:!0,children:[a(bn,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[n(d,{variant:"h6",children:"Remarks"}),n(me,{sx:{width:"max-content"},onClick:ye,children:n(yn,{})})]}),n(Sn,{sx:{padding:".5rem 1rem"},children:n(vn,{autoFocus:!0,margin:"dense",id:"name",label:"Enter Remarks for Correction",type:"text",fullWidth:!0,variant:"standard",value:te,onChange:Ke})}),a(Pn,{sx:{display:"flex",justifyContent:"end"},children:[n(g,{sx:{width:"max-content",textTransform:"capitalize"},onClick:ye,children:"Cancel"}),n(g,{className:"button_primary--normal",type:"save",onClick:Tr,variant:"contained",children:"OK"})]})]}),a(Cn,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:Jn,onClose:ce,children:[a(bn,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[n(d,{variant:"h6",children:"Remarks"}),n(me,{sx:{width:"max-content"},onClick:ce,children:n(yn,{})})]}),n(Sn,{sx:{padding:".5rem 1rem"},children:n(D,{children:n(N,{sx:{minWidth:400},children:n(Vr,{sx:{height:"auto"},fullWidth:!0,children:n(vn,{sx:{backgroundColor:"#F5F5F5"},value:te,onChange:Ke,multiline:!0,placeholder:"Enter Remarks",inputProps:{maxLength:254}})})})})}),a(Pn,{sx:{display:"flex",justifyContent:"end"},children:[n(g,{sx:{width:"max-content",textTransform:"capitalize"},onClick:ce,children:"Cancel"}),n(g,{className:"button_primary--normal",type:"save",onClick:Nr,variant:"contained",children:"Submit"})]})]}),n(Hr,{sx:{color:"#fff",zIndex:r=>r.zIndex.drawer+1},open:Hn,children:n(Ur,{color:"inherit"})}),a(i,{container:!0,sx:Rr,children:[a(i,{item:!0,md:12,style:{padding:"16px",display:"flex"},children:[a(i,{md:12,sx:{display:"flex"},children:[n(i,{children:n(me,{color:"primary","aria-label":"upload picture",component:"label",sx:kn,children:n(Xr,{sx:{fontSize:"25px",color:"#000000"},onClick:()=>{_e("/masterDataCockpit/profitCenter")}})})}),a(i,{children:[!(t!=null&&t.requestType)&&C?a(i,{item:!0,md:12,children:[n(d,{variant:"h3",children:n("strong",{children:"Create Profit Center "})}),n(d,{variant:"body2",color:"#777",children:"This view creates new Profit Center from existing Profit Center"})]}):"",C&&(t==null?void 0:t.requestType)==="Change"?a(i,{item:!0,md:12,children:[n(d,{variant:"h3",children:n("strong",{children:"Change Profit Center "})}),n(d,{variant:"body2",color:"#777",children:"This view changes the details of the Profit Center"})]}):"",C&&(t==null?void 0:t.requestType)==="Create"?a(i,{item:!0,md:12,children:[n(d,{variant:"h3",children:n("strong",{children:"Create Profit Center "})}),n(d,{variant:"body2",color:"#777",children:"This view creates a new Profit Center"})]}):"",Y?a(i,{item:!0,md:12,children:[n(d,{variant:"h3",children:n("strong",{children:"Display Profit Center "})}),n(d,{variant:"body2",color:"#777",children:"This view displays the details of the Profit Center"})]}):""]})]}),(t==null?void 0:t.reqStatus)==="Correction Pending"?n(i,{children:n(me,{color:"primary","aria-label":"upload picture",component:"label",sx:kn,children:n(Mn,{sx:{fontSize:"25px",color:"#000000",alignItems:"flex-end"},onClick:()=>{}})})}):"",n(i,{md:3,sx:{display:"flex",justifyContent:"flex-end"},gap:2,children:ve(ge,"Profit Center","ChangePC")&&((h==null?void 0:h.role)==="Super User"&&(t!=null&&t.requestType)&&Y?n(i,{gap:1,sx:{display:"flex"},children:n(i,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:n(I,{children:n(i,{item:!0,children:a(g,{variant:"outlined",size:"small",sx:R,onClick:ae,children:["Fill Details",n(pe,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(h==null?void 0:h.role)==="Finance"&&(t!=null&&t.requestType)&&Y?n(i,{gap:1,sx:{display:"flex"},children:n(i,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:n(I,{children:n(i,{item:!0,children:a(g,{variant:"outlined",size:"small",sx:R,onClick:ae,children:["Fill Details",n(pe,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(h==null?void 0:h.role)==="Super User"&&!(t!=null&&t.requestType)&&Y?n(i,{gap:1,sx:{display:"flex"},children:n(i,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:n(I,{children:n(i,{item:!0,children:a(g,{variant:"outlined",size:"small",sx:R,onClick:ae,children:["Change",n(pe,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(h==null?void 0:h.role)==="Finance"&&!(t!=null&&t.requestType)&&Y?n(i,{gap:1,sx:{display:"flex"},children:n(i,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:n(I,{children:n(i,{item:!0,children:a(g,{variant:"outlined",size:"small",sx:R,onClick:ae,children:["Change",n(pe,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):"")})]}),n(i,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:a(N,{width:"70%",sx:{marginLeft:"40px"},children:[n(i,{item:!0,sx:{paddingTop:"2px !important"},children:a(D,{flexDirection:"row",children:[n("div",{style:{width:"10%"},children:n(d,{variant:"body2",color:"#777",children:"Profit Center"})}),a(d,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[":"," ",`P${(ln=(sn=t==null?void 0:t.companyCodeCopy)==null?void 0:sn.newCompanyCodeCopy)==null?void 0:ln.code}${(an=t==null?void 0:t.profitCenterName)==null?void 0:an.newProfitCenterName}`]})]})}),n(i,{item:!0,sx:{paddingTop:"2px !important"},children:a(D,{flexDirection:"row",children:[n("div",{style:{width:"10%"},children:n(d,{variant:"body2",color:"#777",children:"Controlling Area"})}),a(d,{variant:"body2",fontWeight:"bold",children:[":"," ",(dn=(cn=t==null?void 0:t.controllingArea)==null?void 0:cn.newControllingArea)==null?void 0:dn.code]})]})})]})}),a(i,{container:!0,style:{marginLeft:25},children:[n(Dr,{activeStep:f,sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},"aria-label":"mui tabs example",children:P.map((r,l)=>n(eo,{children:n(no,{sx:{fontWeight:"700"},children:r})},r))}),Xe&&((mn=Xe[f])==null?void 0:mn.map((r,l)=>n(N,{sx:{mb:2,width:"100%"},children:n(d,{variant:"body2",children:r})},l)))]})]}),a(i,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:[ve(ge,"Profit Center","ChangePC")&&(!(t!=null&&t.requestType)&&!C?n(Pe,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:a(ke,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(g,{variant:"contained",size:"small",sx:{...F,mr:1},onClick:Ce,disabled:f===0,children:"Back"}),n(g,{variant:"contained",size:"small",sx:{...F,mr:1},onClick:be,disabled:f===P.length-1,children:"Next"})]})}):""),ve(ge,"Profit Center","ChangePC")&&((h==null?void 0:h.role)==="Super User"&&!(t!=null&&t.requestType)&&C?n(Pe,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:a(ke,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(g,{variant:"contained",size:"small",sx:{button_Outlined:R,mr:1},onClick:Ye,children:"Save As Draft"}),n(g,{variant:"contained",size:"small",sx:{...F,mr:1},onClick:Ce,disabled:f===0,children:"Back"}),f===P.length-1?a(I,{children:[n(g,{variant:"contained",size:"small",sx:{...F,mr:1},onClick:Ue,children:"Validate"}),n(g,{variant:"contained",size:"small",sx:{...F,mr:1},onClick:kr,disabled:Me,children:"Submit For Review"})]}):n(g,{variant:"contained",size:"small",sx:{...F,mr:1},onClick:be,disabled:f===P.length-1,children:"Next"})]})}):(h==null?void 0:h.role)==="Finance"&&!(t!=null&&t.requestType)&&C?n(Pe,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:a(ke,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(g,{variant:"contained",size:"small",sx:{button_Outlined:R,mr:1},onClick:Ye,children:"Save As Draft"}),n(g,{variant:"contained",size:"small",sx:{...F,mr:1},onClick:Ce,disabled:f===0,children:"Back"}),f===P.length-1?a(I,{children:[n(g,{variant:"contained",size:"small",sx:{...F,mr:1},onClick:Ue,children:"Validate"}),n(g,{variant:"contained",size:"small",sx:{...F,mr:1},onClick:Ir,disabled:Me,children:"Submit For Review"})]}):n(g,{variant:"contained",size:"small",sx:{...F,mr:1},onClick:be,disabled:f===P.length-1,children:"Next"})]})}):"")]})]})})};export{$o as default};
