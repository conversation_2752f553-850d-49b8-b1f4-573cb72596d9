import{b as pe,s as he,r as l,u as ue,q as m,cd as fe,j as n,G as i,al as me,a as t,z as ge,T as h,B as k,x as G,b4 as xe,br as Ce,aE as H,t as u,aB as b,aD as I,K as R,c0 as K,cn as B,bp as be,I as ye,b1 as ve,b8 as J,ai as Q}from"./index-17b8d91e.js";import{d as Se}from"./ArrowCircleLeftOutlined-2a09f8e2.js";import{d as ke}from"./EditOutlined-36c8ca4d.js";import{E as Be}from"./EditFieldForMassProfitCenter-fd44dc35.js";import{C as Pe}from"./CompCodeProfitCenter-816b27ce.js";import{C as Ae,d as Ee}from"./ChangeLog-0f47d713.js";import{a as De,b as Ne,S as je}from"./Stepper-88e4fb0c.js";import"./DatePicker-68227989.js";import"./dateViewRenderers-34586552.js";import"./useSlotProps-e34e1e13.js";import"./InputAdornment-5b0053c5.js";import"./CSSTransition-30917e2c.js";import"./useMediaQuery-6a073ac5.js";import"./DesktopDatePicker-07c19cde.js";import"./useMobilePicker-9978caff.js";const Ke=()=>{var q,O;const D=pe(),c=he();l.useState({});const[N,j]=l.useState(0),[f,U]=l.useState(!1),[we,X]=l.useState(!0);l.useState(0);const[Y,Z]=l.useState([]),[d,y]=l.useState(0),[ee,w]=l.useState(!1);l.useState([]);const[$,te]=l.useState([]),[ae,F]=l.useState(!1),p=ue(),oe=m(e=>e.profitCenter.profitCenterCompCodes);console.log("location",p.state),m(e=>e.initialData.EditMultipleMaterial),m(e=>e.initialData.MultipleMaterial);const g=p.state.tabsData.viewData,r=p.state.rowData,ie=p.state.requestNumber,v=g["Comp Codes"]["Company Code Assignment for Profit Center"];m(e=>e.payload);let P=m(e=>e.edit.payload);console.log(P,"singlePCPayloadAfterChange");let A=m(e=>e.profitCenter.requiredFields);console.log(A,"required_field_for_data");const ne=()=>{const e=o=>{c(Q({keyName:"ProfitCtrGroup",data:o.body}))},a=o=>{console.log(o)};R(`/${K}/data/getProfitCtrGroup?controllingArea=${r==null?void 0:r.controllingArea}`,"get",e,a)},re=e=>{console.log("compcode",e);const a=s=>{console.log("value",s),c(Q({keyName:"Region",data:s.body}))},o=s=>{console.log(s,"error in dojax")};R(`/${K}/data/getRegionBasedOnCountry?country=${e}`,"get",a,o)};l.useEffect(()=>{c(fe(L))},[]),l.useEffect(()=>{var e;Z(_.zip(v[0].value,v[1].value,v[2].value).map((a,o)=>{var s,S,C,W;return{id:o,companyCodes:(s=a[0])!=null&&s.split("$$$")[0]?(S=a[0])==null?void 0:S.split("$$$")[0]:"",companyName:(C=a[1])!=null&&C.split("$$$")[0]?(W=a[1])==null?void 0:W.split("$$$")[0]:"",assigned:a[2]?a[2]:""}})),re((e=g.Address["Address Data"].find(a=>(a==null?void 0:a.fieldName)==="Country/Reg."))==null?void 0:e.value),ne()},[]);const le=()=>{U(!0),X(!1)},M=()=>{const e=V();f?e?(y(a=>a-1),c(B())):z():(y(a=>a-1),c(B()))},T=()=>{const e=V();f?e?(y(a=>a+1),c(B())):z():(y(a=>a+1),c(B()))},z=()=>{F(!0)};console.log("datafromprevious",g,v);const x=Object.entries(g).filter(e=>typeof e[1]=="object"&&e[1]!=null).map(e=>e[0]);console.log("tabsArray",x,d);const E=Object.entries(g).filter(e=>typeof e[1]=="object"&&e[1]!=null).map(e=>Object.entries(e[1])),L={};E.map(e=>{e.forEach((a,o)=>{a.forEach((s,S)=>{S!==0&&s.forEach(C=>{L[C.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("")]=C.value})})})});const se=e=>{w(e)},de=()=>{w(!0)};console.log(P,A,"========deeced"),console.log(d,"activeStep");const V=()=>be(P,A,te),ce=()=>{F(!1)};return n("div",{children:[n(i,{container:!0,style:{...me,backgroundColor:"#FAFCFF"},children:[$.length!=0&&t(ge,{openSnackBar:ae,alertMsg:"Please fill the following Field: "+$.join(", "),handleSnackBarClose:ce}),n(i,{sx:{width:"inherit"},children:[n(i,{item:!0,md:12,style:{padding:"16px",display:"flex"},children:[t(i,{item:!0,style:{display:"flex",justifyContent:"flex-end"},children:t(ye,{color:"primary","aria-label":"upload picture",component:"label",sx:ve,children:t(Se,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{D("/masterDataCockpit/profitCenter/createMultipleProfitCenter")}})})}),n(i,{md:10,children:[t(h,{variant:"h3",children:n("strong",{children:["Multiple Profit Centers: ",r.profitCenter," "]})}),t(h,{variant:"body2",color:"#777",children:"This view dispalys detail of Multiple Profit Centers"})]}),(q=p==null?void 0:p.state)!=null&&q.requestNumber?t(i,{md:.5,sx:{display:"flex",justifyContent:"flex-end"},children:t(u,{variant:"outlined",size:"small",sx:J,onClick:de,title:"Change Log",children:t(Ee,{sx:{padding:"2px"},fontSize:"small"})})}):t(i,{md:.5,sx:{display:"flex",justifyContent:"flex-end"}}),ee&&t(Ae,{open:!0,closeModal:se,requestId:ie,requestType:"Mass",pageName:"profitCenter",controllingArea:r.controllingArea,centerName:r.costCenter}),f?"":t(i,{md:1.5,sx:{display:"flex",justifyContent:"flex-end"},children:t(i,{item:!0,children:n(u,{variant:"outlined",size:"small",sx:J,onClick:le,children:["Change",t(ke,{sx:{padding:"2px"},fontSize:"small"})]})})})]}),t(i,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:n(k,{width:"70%",sx:{marginLeft:"40px"},children:[t(i,{item:!0,sx:{paddingTop:"2px !important"},children:n(G,{flexDirection:"row",children:[t("div",{style:{width:"15%"},children:t(h,{variant:"body2",color:"#777",children:"Profit Center"})}),n(h,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[": ",r==null?void 0:r.profitCenter]})]})}),t(i,{item:!0,sx:{paddingTop:"2px !important"},children:n(G,{flexDirection:"row",children:[t("div",{style:{width:"15%"},children:t(h,{variant:"body2",color:"#777",children:"Controlling Area"})}),n(h,{variant:"body2",fontWeight:"bold",children:[": ",r==null?void 0:r.controllingArea]})]})})]})}),n(i,{container:!0,style:{padding:"16px"},children:[t(je,{activeStep:d,sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},"aria-label":"mui tabs example",children:x.map((e,a)=>t(De,{children:t(Ne,{sx:{fontWeight:"700"},children:e})},e))}),t(i,{container:!0,children:E&&((O=E[d])==null?void 0:O.map((e,a)=>d===2?t(Pe,{compCodesTabDetails:oe,displayCompCode:Y}):t(k,{sx:{width:"100%"},children:n(i,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...xe},children:[t(i,{container:!0,children:t(h,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:e[0]})}),t(k,{children:t(k,{sx:{width:"100%"},children:t(Ce,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:t(i,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:[...e[1]].map(o=>(console.log("inneritem",o),t(Be,{activeTabIndex:d,fieldGroup:e[0],selectedRowData:r.profitCenter,pcTabs:x,label:o.fieldName,value:o.value,length:o.maxLength,visibility:o.visibility,onSave:s=>handleFieldSave(o.fieldName,s),isEditMode:f,type:o.fieldType,field:o})))})})})})]})},a)))})]})]})]}),f?t(I,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:n(H,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:N,onChange:e=>{j(e)},children:[t(u,{size:"small",sx:{...b,mr:1},variant:"contained",onClick:()=>{D("/masterDataCockpit/profitCenter/createMultipleProfitCenter")},children:"Save"}),t(u,{variant:"contained",size:"small",sx:{...b,mr:1},onClick:M,disabled:d===0,children:"Back"}),t(u,{variant:"contained",size:"small",sx:{...b,mr:1},onClick:T,disabled:d===x.length-1,children:"Next"})]})}):"",f?"":t(I,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:n(H,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:N,onChange:e=>{j(e)},children:[t(u,{variant:"contained",size:"small",sx:{...b,mr:1},onClick:M,disabled:d===0,children:"Back"}),t(u,{variant:"contained",size:"small",sx:{...b,mr:1},onClick:T,disabled:d===x.length-1,children:"Next"})]})})]})};export{Ke as default};
