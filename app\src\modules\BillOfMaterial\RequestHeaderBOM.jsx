import React, { useEffect } from "react";
import { Box, Button, Grid, Stack, Typography } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import FilterField from "@components/Common/ReusableFilterBox/FilterField";
import { useLocation } from "react-router-dom";
import { container_Padding } from "@components/Common/commonStyles";
import useLang from "@hooks/useLang";
import { ToastContainer } from "react-toastify";
import {
  REQUEST_HEADER_FILED_NAMES,
  REQUEST_PRIORITY,
  REQUEST_TYPE_OPTIONS,
  VISIBILITY_TYPE,
} from "@constant/enum";
import { setDropDown } from "@app/dropDownDataSlice";
import { setMultipleMaterialPayloadKey, setPayload } from "@app/payloadSlice";
import useGenericDtCall from "@hooks/useGenericDtCall";
import { DECISION_TABLE_NAME } from "@constant/enum";
import { setHeaderFieldsBOM } from "./bomSlice";
import { TEMPLATE_KEYS, TEMPLATE_NAMES_BOM } from "@constant/changeTemplates";
import useMaterialChangeFieldConfig from "@hooks/useMaterialChangeFieldConfig";
import { destination_BOM } from "../../destinationVariables";
import { doAjax } from "@components/Common/fetchService";
import { END_POINTS } from "@constant/apiEndPoints";

const RequestHeaderBOM = ({
  setIsSecondTabEnabled,
  setIsAttachmentTabEnabled,
}) => {
  const { getDtCall, dtData } = useGenericDtCall();
  const { t } = useLang();
  const requestHeaderFields = useSelector((state) => state.bom.headerFieldsBOM);
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const isreqBench = queryParams.get("reqBench");
  const isWorkspace = queryParams.get("RequestId");
  const requestHeaderData = useSelector((state) => state.request.requestHeader);
  const userData = useSelector((state) => state.userManagement.userData);
  const payloadFields = useSelector((state) => state.payload.payloadData);
  const { getChangeTemplate } = useMaterialChangeFieldConfig();
  const dispatch = useDispatch();
  dispatch(setDropDown({ keyName: "RequestPriority", data: REQUEST_PRIORITY }));
  dispatch(setDropDown({ keyName: "TemplateName", data: TEMPLATE_NAMES_BOM }));
  dispatch(
    setDropDown({
      keyName: REQUEST_HEADER_FILED_NAMES?.REQUEST_TYPE,
      data: REQUEST_TYPE_OPTIONS,
    })
  );
  if (!isWorkspace && !isreqBench) {
    dispatch(
      setMultipleMaterialPayloadKey({
        keyName: "ReqCreatedBy",
        data: userData?.user_id,
      })
    );
    dispatch(
      setMultipleMaterialPayloadKey({ keyName: "RequestStatus", data: "DRAFT" })
    );
  }
  useEffect(() => {
    fetchHeaderFieldsFromDt();
  }, [payloadFields?.RequestType]);

  useEffect(() => {
    if (payloadFields?.TemplateName) {
      if (
        payloadFields?.TemplateName === TEMPLATE_KEYS.MRP ||
        payloadFields?.TemplateName === TEMPLATE_KEYS.WARE_VIEW_2
      ) {
        dispatch(setPayload({ keyName: "FieldName", data: undefined }));
      }
      getChangeTemplate();
    }
  }, [payloadFields?.TemplateName]);

  useEffect(() => {
    if (dtData) {
      let responseData = dtData?.result[0]?.MDG_MAT_REQUEST_HEADER_CONFIG;
      const formattedData = responseData
        .sort((a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO)
        .map((item) => ({
          fieldName: item.MDG_MAT_UI_FIELD_NAME,
          sequenceNo: item.MDG_MAT_SEQUENCE_NO,
          fieldType: item.MDG_MAT_FIELD_TYPE,
          maxLength: item.MDG_MAT_MAX_LENGTH,
          value: item.MDG_MAT_DEFAULT_VALUE,
          visibility: item.MDG_MAT_VISIBILITY,
          jsonName: item.MDG_MAT_JSON_FIELD_NAME,
        }));

      const requestHeaderObj = { "Header Data": formattedData };
      dispatch(setHeaderFieldsBOM(requestHeaderObj));
    }
  }, [dtData]);
  const checkAllFieldsFilled = () => {
    let allFilled = true;
    if (
      payloadFields &&
      requestHeaderFields[Object.keys(requestHeaderFields)]?.length
    ) {
      requestHeaderFields[Object.keys(requestHeaderFields)[0]]?.forEach(
        (reqst) => {
          if (
            !payloadFields[reqst.jsonName] &&
            reqst.visibility === VISIBILITY_TYPE?.MANDATORY
          ) {
            allFilled = false;
          }
        }
      );
    } else {
      allFilled = false;
    }
    return allFilled;
  };

  const fetchHeaderFieldsFromDt = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: DECISION_TABLE_NAME.MDG_MAT_REQUEST_HEADER_CONFIG,
      version: "v2",
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_SCENARIO":
            payloadFields?.RequestType || "Create",
          "MDG_CONDITIONS.MDG_MODULE": "BOM",
        },
      ],
    };
    getDtCall(payload);
  };

  const handleButtonClick = () => {
    const epochTime = new Date(payloadFields?.ReqCreatedOn).getTime();
    const currentDate = `/Date(${Date.now()})/`;
    const payload = {
      RequestId: requestHeaderData?.requestId
        ? requestHeaderData?.requestId
        : "",
      ReqCreatedBy: userData?.user_id || "",
      ReqCreatedOn: epochTime ? `/Date(${epochTime})/` : currentDate,
      ReqUpdatedOn: epochTime ? `/Date(${epochTime})/` : currentDate,
      RequestType: payloadFields?.RequestType || "",
      RequestPrefix: "string",
      RequestPriority: payloadFields?.RequestPriority || "",
      RequestDesc: payloadFields?.RequestDesc || "",
      RequestStatus: "DRAFT",
      TemplateName: payloadFields?.TemplateName || "",
      FieldName: payloadFields?.FieldName?.join("$^$") || "",
      Region: payloadFields?.Region || "",
      filterDetails: "string",
      SapSystem: "string",
      IsBifurcated: true,
      IncompleteChildTasks: "string",
    };
    const hSuccess = (data) => {
      //   setSuccessMsg(true);
      //   setMessageDialogMessage(`Request Header Created Successfully with request ID ${appendPrefixByJavaKey(payloadFields?.RequestType, data?.body?.requestId)}`);
      //   setAlertType("success");
      //   handleSnackBarOpen();
      //   dispatch(setRequestHeader(data.body));
      //   setIsAttachmentTabEnabled(true);
      //   setDisableProceed(false);
      //   dispatch(updateAllTabsData({}))
      //   dispatch(setTaskData({}))
      //   if (initialPayload?.RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || initialPayload?.RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) {
      //     setOpenDownloadDialog(true);
      //     return
      //   }
      //   if (initialPayload?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD) {
      //     setDialogOpen(true);
      //     return;
      //   }
      //   if (initialPayload?.RequestType === REQUEST_TYPE?.CHANGE) {
      //     const filteredConfig = filterConfigData(changeData?.["Config Data"], initialPayload?.FieldName, ["Material", "Plant", "Sales Org", "Distribution Channel", "Warehouse", "MRP Controller"]);
      //     dispatch(changeTemplateDT({ ...changeData, "Config Data": filteredConfig }));
      //     const filteredTableNames = filterFieldNameData(changeData?.[initialPayload?.TemplateName], initialPayload?.FieldName)
      //     dispatch(setChangeTableData([...filteredTableNames]));
      //   }
      //   // if (initialPayload?.RequestType?.code === "Change") {
      //   setTimeout(() => {
      //     dispatch(setTabValue(1));
      //     setIsSecondTabEnabled(true);
      //   }, 2500);
    };
    const hError = () => {
      //   setSuccessMsg(true);
      //   setAlertType("error");
      //   setMessageDialogMessage("Error occured while saving Request Header");
      //   handleSnackBarOpen();
    };

    doAjax(
      `/${destination_BOM}${END_POINTS.MASS_ACTION.CREATE_BOM_REQUEST}`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  return (
    <div>
      <Stack spacing={2}>
        {Object.entries(requestHeaderFields).map(([key, fields]) => (
          <Grid
            item
            md={12}
            key={key}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              borderRadius: "8px",
              border: "1px solid #E0E0E0",
              mt: 0.25,
              boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
              ...container_Padding,
            }}
          >
            <Typography
              sx={{
                fontSize: "12px",
                fontWeight: "700",
                paddingBottom: "10px",
              }}
            >
              {t(key)}
            </Typography>
            <Box>
              <Grid container spacing={1}>
                {fields
                  .filter((field) => field.visibility !== "Hidden")
                  .sort((a, b) => a.sequenceNo - b.sequenceNo)
                  .map((innerItem) => (
                    <FilterField
                      isHeader={true}
                      key={innerItem.id}
                      field={innerItem}
                      dropDownData={{}}
                      disabled={isWorkspace || requestHeaderData?.requestId}
                      requestHeader={true}
                    />
                  ))}
              </Grid>
            </Box>
            {!isWorkspace && !requestHeaderData?.requestId && (
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "flex-end",
                  marginTop: "20px",
                }}
              >
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleButtonClick}
                  disabled={!checkAllFieldsFilled()}
                >
                  {t("Save Request Header")}
                </Button>
              </Box>
            )}
            <ToastContainer />
          </Grid>
        ))}
      </Stack>
    </div>
  );
};

export default RequestHeaderBOM;
