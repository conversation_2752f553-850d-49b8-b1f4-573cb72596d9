import {
  Box,
  Grid,
  IconButton,
  Tabs,
  Typography,
  Stack,
  TextField,
  Checkbox,
  Autocomplete,
  Paper,
  BottomNavigation,
  Button,
  CardContent,
  Stepper,
  Step,
  StepLabel,
} from "@mui/material";
import Tab from "@mui/material/Tab";
import React, { useState, useEffect } from "react";
import {
  iconButton_SpacingSmall,
  outermostContainer,
  container_Padding,
  outerContainer_Information,
  button_Primary,
  outermostContainer_Information,
  button_Outlined,
} from "../../Common/commonStyles";
import { useSelector, useDispatch } from "react-redux";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import { useNavigate, useLocation } from "react-router-dom";
// import EditableField from "./EditFieldForDisplay";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import EditableFieldForMassCostCenter from "./EditFieldForMassCostCenter";
import moment from "moment";
import { destination_CostCenter } from "../../../destinationVariables";
import { doAjax } from "../../Common/fetchService";
import { setDropDown } from "../../../app/dropDownDataSlice";
// import { setEditMultipleMaterial } from "../../app/initialDataSlice";
// import EditableFieldForMassMaterial from "./EditFieldForMassMaterial";
import ChangeLog from "../../Changelog/ChangeLog";
import TrackChangesTwoToneIcon from "@mui/icons-material/TrackChangesTwoTone";
import { setPayloadWhole } from "../../../app/editPayloadSlice";
import { clearCostCenter,clearSingleCostCenter } from "../../../app/costCenterTabsSlice";
import { formValidator } from "../../../functions";
import ReusableSnackBar from "../../Common/ReusableSnackBar";
const EditMultipleCostCenter = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [dropDownData, setDropDownData] = useState({});
  const [value, setValue] = useState(0);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isDisplayMode, setIsDisplayMode] = useState(true);
  const [activeStep, setActiveStep] = useState(0);
  const [activeTab, setActiveTab] = useState(0);
  const [isChangeLogopen, setisChangeLogopen] = useState(false);
  const [formValidationErrorItems, setFormValidationErrorItems] = useState([]); //chiranjit
  const [openSnackbarValidation, setOpenSnackbarValidation] = useState(false); 
  const location = useLocation();
  const ChangesInMaterialDetail = useSelector(
    (state) => state.initialData.EditMultipleMaterial
  );
  const costCenterData = useSelector(
    (state) => state.costCenter.MultipleCostCenterData
  );
  console.log(costCenterData,"costCenterData")
  const appSettings = useSelector((state) => state.appSettings);
  // const massControllingArea = useSelector((state)=>state.costcenter.controllingArea)
  const tabsData = location.state.tabsData.viewData;
  console.log("tabsData", tabsData);
  const selectedRowData = location.state.rowData;
  const requestNumber = location.state.requestNumber;
  const PayloadData = useSelector((state) => state.payload);
  let singleCCPayloadAfterChange = useSelector((state) => state.edit.payload);
  let requiredFieldTabWise= useSelector((state) => state.costCenter.requiredFields);
  console.log(singleCCPayloadAfterChange,requiredFieldTabWise,"required_field_for_data")

  const onEdit = () => {
    setIsEditMode(true);
    setIsDisplayMode(false);
  };

  const handleChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  const handleBack = () => {
    //setActiveStep((prevActiveStep) => prevActiveStep - 1);
    //setSubmitForReviewDisabled(true);
    const isValidation = handleCheckValidationError();
    if(isEditMode){
      if (isValidation) {
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
        dispatch(clearSingleCostCenter());
      } else {
        handleSnackBarOpenValidation();
      }
    }else{
      setActiveStep((prevActiveStep) => prevActiveStep - 1);
      dispatch(clearSingleCostCenter());
    }
  
  
  };
  const handleNext = () => {
    //setActiveStep((prevActiveStep) => prevActiveStep + 1);
    const isValidation = handleCheckValidationError();
    if(isEditMode){
      if (isValidation) {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
        dispatch(clearSingleCostCenter());
      } else {
        handleSnackBarOpenValidation();
      }
    }else{
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
      dispatch(clearSingleCostCenter());
    }

  };
  const handleSnackBarOpenValidation = () => {
    setOpenSnackbarValidation(true);
  };
  // console.log("select", MultipleMaterial.filter((item)=>item.Description === selectedRowData.description)[0]);

  let activeRow = {};
  let activeIndex = -1;
  for (let index = 0; index < costCenterData?.tableData?.length; index++) {
    if (
      costCenterData.tableData[index].Description ===
      selectedRowData.description
    ) {
      activeRow = costCenterData.tableData[index];
      activeIndex = index;
      break;
    }
  }
  const getHierarchyArea = (value) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "HierarchyArea", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getHierarchyArea?controllingArea=${value}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCompanyCode = (value) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CompanyCode", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getCompanyCodeBasedOnControllingArea?controllingArea=${value}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getProfitCenter = (value) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ProfitCenter", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getProfitCenterAsPerControllingArea?controllingArea=${value}`,
      "get",
      hSuccess,
      hError
    );
  };
  useEffect(() => {
    getHierarchyArea(selectedRowData.controllingArea);
    getCompanyCode(selectedRowData.controllingArea);
    getProfitCenter(selectedRowData.controllingArea);
  }, []);

 
  const tabsArray = Object.entries(tabsData)
    .filter((item) => typeof item[1] == "object" && item[1] != null)
    .map((item) => item[0]);

  const tabContents = Object.entries(tabsData)
    .filter((item) => typeof item[1] == "object" && item[1] != null)
    .map((item) => Object.entries(item[1]));
  console.log(tabContents,"tabContents")
  const tempHash = {};
    const gridArr = tabContents.map(item => {
      item.forEach((eachTab, i) => {
        eachTab.forEach((eachItem, i) => {
          if (i !== 0) {
            eachItem.forEach(fieldItem => {
              tempHash[fieldItem.fieldName
              .replaceAll("(", "")
              .replaceAll(")", "")
              .replaceAll("/", "")
              .replaceAll("-", "")
              .replaceAll(".", "")
              .split(" ")
              .join("")] =  fieldItem.value;
            });
          }
        });
      });
  });

  useEffect(() => {
    dispatch(setPayloadWhole(tempHash));
  }, []);
  console.log(tempHash,"tempHash")
  console.log("activeTab", tabContents);
  const handleClosemodalData = (data) => {
    setisChangeLogopen(data);
  };
  const openChangeLog = () => {
    setisChangeLogopen(true);
  };
  console.log(singleCCPayloadAfterChange,requiredFieldTabWise,"eror_arr")
  const handleCheckValidationError = () => {
    //chiranjit
    return formValidator(
      singleCCPayloadAfterChange,
      requiredFieldTabWise,
      setFormValidationErrorItems
    );
  };
  const handleSnackBarCloseValidation = () => {
    setOpenSnackbarValidation(false);
  };
  return (
    <div>
      <Grid
        container
        style={{
          ...outermostContainer_Information,
          backgroundColor: "#FAFCFF",
        }}
      >
         {formValidationErrorItems.length != 0 && (
            <ReusableSnackBar
              openSnackBar={openSnackbarValidation}
              alertMsg={
                "Please fill the following Field: " +
                formValidationErrorItems.join(", ")
              }
              handleSnackBarClose={handleSnackBarCloseValidation}
            />
          )}
        <Grid sx={{ width: "inherit" }}>
          <Grid item md={12} style={{ padding: "16px", display: "flex" }}>
            {/* <Grid  sx={{ display: "flex" }}> */}
            <Grid item  style={{ display: "flex", justifyContent: "flex-end" }}>
              <IconButton
                // onClick={handleBacktoRO}
                color="primary"
                aria-label="upload picture"
                component="label"
                sx={iconButton_SpacingSmall}
              >
                <ArrowCircleLeftOutlinedIcon
                  style={{ height: "1em", width: "1em", color: "#000000" }}
                  onClick={() => {
                    navigate(
                      "/masterDataCockpit/costCenter/createMultipleCostCenter"
                    );
                    // dispatch(clearPayload());
                    // dispatch(clearOrgData());
                  }}
                />
              </IconButton>
            </Grid>
            <Grid md={10}>
              <Typography variant="h3">
                <strong>Cost Center: {selectedRowData.costCenter} </strong>
              </Typography>
              <Typography variant="body2" color="#777">
                This view creates a new Cost Center
              </Typography>
            </Grid>
            {location?.state?.requestNumber ?
            <Grid md={1} sx={{ display: "flex", justifyContent: "flex-end" }}>
              <Button
                variant="outlined"
                size="small"
                sx={button_Outlined}
                onClick={openChangeLog}
                title="Change Log"
              >
                <TrackChangesTwoToneIcon
                  sx={{ padding: "2px" }}
                  fontSize="small"
                />
              </Button>
            </Grid>:<Grid md={1} sx={{ display: "flex", justifyContent: "flex-end" }}></Grid>}
            
            {isChangeLogopen && (
              <ChangeLog
                open={true}
                closeModal={handleClosemodalData}
                requestId={requestNumber}
                requestType={"Mass"}
                pageName={"costCenter"}
                controllingArea={selectedRowData.controllingArea}
                centerName={selectedRowData.costCenter}
              />
            )}
            {!isEditMode ? (
              <Grid md={1} sx={{ display: "flex", justifyContent: "flex-end" }}>
                <Grid item>
                  <Button
                    variant="outlined"
                    size="small"
                    sx={button_Outlined}
                    onClick={onEdit}
                  >
                    Change
                    <EditOutlinedIcon
                      sx={{ padding: "2px" }}
                      fontSize="small"
                    />
                  </Button>
                </Grid>
              </Grid>
            ) : (
              ""
            )}
            {/* </Grid> */}
          </Grid>
          <Grid container display="flex" flexDirection="row" flexWrap="nowrap">
            <Box width="70%" sx={{ marginLeft: "40px" }}>
              <Grid item sx={{ paddingTop: "2px !important" }}>
                <Stack flexDirection="row">
                  <div style={{ width: "15%" }}>
                    <Typography variant="body2" color="#777">
                      Cost Center
                    </Typography>
                  </div>
                  <Typography
                    variant="body2"
                    fontWeight="bold"
                    justifyContent="flex-start"
                  >
                    : {selectedRowData.costCenter}
                  </Typography>
                </Stack>
              </Grid>

              <Grid item sx={{ paddingTop: "2px !important" }}>
                <Stack flexDirection="row">
                  <div style={{ width: "15%" }}>
                    <Typography variant="body2" color="#777">
                      Controlling Area
                    </Typography>
                  </div>
                  <Typography variant="body2" fontWeight="bold">
                    : {selectedRowData.controllingArea}
                  </Typography>
                </Stack>
              </Grid>
              <Grid item sx={{ paddingTop: "2px !important" }}>
                <Stack flexDirection="row">
                  <div style={{ width: "15%" }}>
                    <Typography variant="body2" color="#777">
                      Valid From
                    </Typography>
                  </div>
                  <Typography variant="body2" fontWeight="bold">
                    :{" "}
                    {moment(selectedRowData.validFrom).format(
                      appSettings?.dateFormat
                    )}
                  </Typography>
                </Stack>
              </Grid>
              <Grid item sx={{ paddingTop: "2px !important" }}>
                <Stack flexDirection="row">
                  <div style={{ width: "15%" }}>
                    <Typography variant="body2" color="#777">
                      Valid To
                    </Typography>
                  </div>
                  <Typography variant="body2" fontWeight="bold">
                    :{" "}
                    {moment(selectedRowData.validTo).format(
                      appSettings?.dateFormat
                    )}
                  </Typography>
                </Stack>
              </Grid>
            </Box>
          </Grid>

          <Grid container style={{ padding: "16px" }}>
            <Stepper
              activeStep={activeStep}
              sx={{
                background: "#FFFFFF",
                borderBottom: "1px solid #BDBDBD",
                width: "100%",
                height: "48px",
              }}
              aria-label="mui tabs example"
            >
              {tabsArray.map((factor, index) => (
                <Step key={factor}>
                  <StepLabel sx={{ fontWeight: "700" }}>{factor}</StepLabel>
                </Step>
              ))}
            </Stepper>

            <Grid container>
              {tabContents &&
                tabContents[activeStep]?.map((item, index) => {
                  return (
                    <Box key={index} sx={{ width: "100%" }}>
                      <Grid
                        item
                        md={12}
                        sx={{
                          backgroundColor: "white",
                          maxHeight: "max-content",
                          height: "max-content",
                          borderRadius: "8px",
                          border: "1px solid #E0E0E0",
                          mt: 0.25,
                          boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                          ...container_Padding,
                          // ...container_columnGap,
                        }}
                      >
                        <Grid container>
                          <Typography
                            sx={{
                              fontSize: "12px",
                              fontWeight: "700",
                              margin: "0px !important",
                            }}
                          >
                            {item[0]}
                          </Typography>
                        </Grid>
                        <Box>
                          <Box sx={{ width: "100%" }}>
                            <CardContent
                              sx={{
                                padding: "0",
                                paddingBottom: "0 !important",
                                paddingTop: "10px !important",
                              }}
                            >
                              <Grid
                                container
                                style={{
                                  display: "grid",
                                  gridTemplateColumns: "repeat(6,1fr)",
                                  gap: "15px",
                                }}
                                justifyContent="space-between"
                                alignItems="flex-start"
                                md={12}
                              >
                                {[...item[1]].map((innerItem) => {
                                  // console.log("inneritem", item[1]);
                                  return (
                                    <EditableFieldForMassCostCenter
                                      // key={field.fieldName}
                                      activeTabIndex={activeStep}
                                      fieldGroup={item[0]}
                                      selectedRowData={
                                        selectedRowData.costCenter
                                      }
                                      ccTabs={tabsArray}
                                      visibility={innerItem.visibility}
                                      label={innerItem.fieldName}
                                      value={innerItem.value}
                                      length={innerItem.maxLength}
                                      onSave={(newValue) =>
                                        handleFieldSave(
                                          innerItem.fieldName,
                                          newValue
                                        )
                                      }
                                      isEditMode={isEditMode}
                                      // isExtendMode={isExtendMode}
                                      type={innerItem.fieldType}
                                      field={innerItem} // Update the type as needed
                                    />
                                  );
                                })}
                              </Grid>
                            </CardContent>
                          </Box>
                        </Box>
                      </Grid>
                      {/* <h1>{cardContent[0]}</h1>
                    {cardContent[1].map((item)=>{
                      return(<p>{item.fieldName}</p>)
                    })} */}
                    </Box>
                  );
                })}
            </Grid>
          </Grid>
        </Grid>
      </Grid>
      {isEditMode ? (
        <Paper
          sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
          elevation={2}
        >
          <BottomNavigation
            className="container_BottomNav"
            showLabels
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              gap: 1,
            }}
            value={value}
            onChange={(newValue) => {
              setValue(newValue);
            }}
          >
            <Button
              size="small"
              sx={{ ...button_Primary, mr: 1 }}
              variant="contained"
              onClick={() => {
                navigate(
                  "/masterDataCockpit/costCenter/createMultipleCostCenter"
                );
              }}
            >
              Save
            </Button>
            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary, mr: 1 }}
              onClick={handleBack}
              disabled={activeStep === 0}
            >
              Back
            </Button>
            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary, mr: 1 }}
              onClick={handleNext}
              disabled={activeStep === tabsArray.length - 1 ? true : false}
            >
              Next
            </Button>
          </BottomNavigation>
        </Paper>
      ) : (
        ""
      )}
      {!isEditMode ? (
        <Paper
          sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
          elevation={2}
        >
          <BottomNavigation
            className="container_BottomNav"
            showLabels
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              gap: 1,
            }}
            value={value}
            onChange={(newValue) => {
              setValue(newValue);
            }}
          >
            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary, mr: 1 }}
              onClick={handleBack}
              disabled={activeStep === 0}
            >
              Back
            </Button>
            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary, mr: 1 }}
              onClick={handleNext}
              disabled={activeStep === tabsArray.length - 1 ? true : false}
            >
              Next
            </Button>
          </BottomNavigation>
        </Paper>
      ) : (
        ""
      )}
    </div>
  );
};

export default EditMultipleCostCenter;
