import{r as a,q as R,s as Ta,b as Fa,p as me,aV as Ma,bW as Pa,K as x,a as s,bX as Ia,j as n,C as Oa,aj as Ra,ak as fe,G as i,al as La,bY as Wa,T as d,bZ as Ba,h as zt,I as ee,b1 as Tt,b_ as Va,R as pe,am as _a,g as Ua,A as Ha,an as Ya,f as qa,aq as F,ar as S,E as b,b6 as E,aw as Ga,ax as Ka,M as xe,ay as Ja,t as $,b8 as Xa,aB as Za,az as Qa,aC as ja,bm as el,aD as ye,aE as tl,aJ as Ve,aK as _e,aL as Ue,aM as He,V as Ft,aF as Mt,aG as Pt,W as It,at as Se,au as be,X as Ot,b7 as sl,F as al,P as q,b$ as f,c0 as ll,O as Rt,c1 as rl,ab as Ye,c2 as Ae,ai as I,c3 as nl,c4 as ol,c5 as cl,c6 as il,c7 as dl,c8 as ul,a_ as Lt,c9 as hl,ca as Cl}from"./index-17b8d91e.js";import"./utilityImages-067c3dc2.js";import"./react-dropzone-uploader-f23f38ba.js";/* empty css            */import{A as gl}from"./AttachmentUploadDialog-d151e36a.js";import{I as ml}from"./InputAdornment-5b0053c5.js";import{D as ve}from"./DatePicker-68227989.js";import"./CloudUpload-27b6d63e.js";import"./Delete-9f4d7a45.js";import"./dateViewRenderers-34586552.js";import"./useSlotProps-e34e1e13.js";import"./CSSTransition-30917e2c.js";import"./useMediaQuery-6a073ac5.js";import"./DesktopDatePicker-07c19cde.js";import"./useMobilePicker-9978caff.js";const Wl=()=>{var At,vt,Dt;a.useState(!1);const[Wt,Bt]=a.useState(!1),[Vt,W]=a.useState("");R(e=>e.appSettings.Format),a.useState(!1),a.useState([]);const g=R(e=>e.AllDropDown.dropDown),h=Ta(),te=Fa(),[qe,Ge]=a.useState(0),[De,se]=a.useState(0),[$e,Ee]=a.useState(10),[Ke,ae]=a.useState(0),[_t,Ut]=a.useState(!0),[Je,le]=a.useState(!1),[Ht,re]=a.useState(!1),[Xe,Yt]=a.useState(!1);a.useState(!1);const[qt,Gt]=a.useState([]),Kt=(e,t)=>{se(t)};let Jt=R(e=>{var t;return(t=e.userManagement.entitiesAndActivities)==null?void 0:t["Display Material"]}),B=R(e=>e.userManagement.userData);const Xt=e=>{const t=e.target.value;Ee(t),se(0),ae(0)},Zt=48,Qt=8,jt={PaperProps:{style:{maxHeight:Zt*4.5+Qt,width:250}}};a.useEffect(()=>{(parseInt(De)+1)*parseInt($e)>=parseInt(Ke)+1e3&&(ge(Ke+1e3),ae(e=>e+1e3))},[De,$e]);var es=new Date,ts=new Date("December 31, 9999 01:15:00");a.useState(!1);const[ss,O]=a.useState(!1),[as,ne]=a.useState(!1),[ls,rs]=a.useState("1"),[V,Ze]=a.useState([]);me.useState(""),a.useState(!1),a.useState("");const[fl,ns]=a.useState("");a.useState(!0);const[pl,Qe]=a.useState(!1),[xl,je]=a.useState(!0);a.useState([]),a.useState([]),a.useState([]),a.useState([]),a.useState(!0),a.useState([]),a.useState([]),a.useState(!1);const[G,et]=a.useState([]),[yl,os]=a.useState([]),[v,tt]=a.useState({});a.useState([]),a.useState([]),a.useState(!1),a.useState([]);const[st,cs]=a.useState([]);a.useState([]);const[is,at]=a.useState(!1),[ds,lt]=a.useState(!1);a.useState(!0),a.useState("sm");const[us,K]=a.useState(!1),[hs,rt]=a.useState(!1),[M,_]=a.useState(""),[D,oe]=a.useState(""),[ce,nt]=a.useState(es),[we,ot]=a.useState(ts),[A,ie]=a.useState(""),[P,ct]=a.useState(""),[it,Cs]=a.useState(""),[L,gs]=a.useState(""),[ms,J]=a.useState(!1),[fs,de]=a.useState(!1),[dt,ut]=a.useState(!1),[ht,ps]=a.useState([]),[Ne,ke]=a.useState(!1);a.useState(!1);const X=me.useRef(null),[xs,ys]=a.useState(0),[ze,Te]=a.useState(!1),[Fe,Me]=a.useState(!1),Z=me.useRef(null),Q=me.useRef(null),[Ss,bs]=a.useState(0),[As,vs]=a.useState(0),[Sl,Ds]=a.useState(!0),[bl,$s]=a.useState(!1);a.useState(!1);const Es=R(e=>e.costCenter.handleMassMode),Pe=["Create Multiple","Upload Template ","Download Template "],Ie=["Create Single","With Copy","Without Copy"],Oe=["Change Multiple","Upload Template","Download Template"],c=R(e=>e.commonFilter.CostCenter),ue=R(e=>e.commonSearchBar.CostCenter),he=R(e=>{var t;return(t=e==null?void 0:e.AllDropDown)==null?void 0:t.dropDown}),ws=()=>{at(!0)},Ns=()=>{lt(!0)},C={companyCode:{newCompanyCode:D},costCenterName:{newCostCenterName:M},controllingAreaData:{newControllingArea:A},controllingAreaDataCopy:{newControllingAreaCopyFrom:P},costCenter:{newCostCenter:L},validFromDate:{newValidFromDate:ce},validToDate:{newValidToDate:we}},ks=()=>{var m,T,w,N,k;let e=(T=(m=C==null?void 0:C.controllingAreaData)==null?void 0:m.newControllingArea)==null?void 0:T.code,t=(N=(w=C==null?void 0:C.companyCode)==null?void 0:w.newCompanyCode)==null?void 0:N.code,r=(k=C==null?void 0:C.costCenterName)==null?void 0:k.newCostCenterName;if(console.log(P,it,r,"selectedCostCenterName"),(A==null?void 0:A.code)===void 0||(A==null?void 0:A.code)===""||(D==null?void 0:D.code)===void 0||(D==null?void 0:D.code)===""||M===void 0||M===""){le(!1),J(!0),$s(!0);return}else{if(M.length!==6){le(!0),J(!1);return}else le(!1);J(!1)}let l=e.concat("$$",t,r);console.log("sendNewCostCenterData",c),O(!0);const o=p=>{O(!1),p.body.length>0?ut(!0):te("/masterDataCockpit/costCenter/newSingleCostCenter",{state:C})},u=p=>{console.log(p)};x(`/${f}/alter/fetchCoAreaCCDupliChk?ctrlAreaCCToCheck=${l}`,"get",o,u)},zs=()=>{var m,T,w,N,k;if(console.log(P,it,"newControllingAreaCopyFrom?.code "),(A==null?void 0:A.code)===void 0||(A==null?void 0:A.code)===""||(D==null?void 0:D.code)===void 0||(D==null?void 0:D.code)===""||M===void 0||M===""||(P==null?void 0:P.code)===void 0||(P==null?void 0:P.code)===""||(L==null?void 0:L.code)===void 0||(L==null?void 0:L.code)===""){re(!1),de(!0);return}else{if(M.length!==6){re(!0),de(!1);return}else re(!1);de(!1)}let e=(m=C==null?void 0:C.controllingAreaData)==null?void 0:m.newControllingArea.code,t=(T=C==null?void 0:C.companyCode)==null?void 0:T.newCompanyCode.code,r=(w=C==null?void 0:C.costCenterName)==null?void 0:w.newCostCenterName,l=e.concat("$$",t,r);console.log((N=C==null?void 0:C.costCenter)==null?void 0:N.newCostCenter,"sendNewCostCenterData=========="),console.log((k=C==null?void 0:C.costCenterName)==null?void 0:k.newCostCenterName,"sendNewCostCenterData=========="),O(!0);const o=p=>{var z,y;O(!1),console.log("dupli",p),p.body.length>0?ut(!0):te(`/masterDataCockpit/costCenter/displayCopyCostCenter/${(y=(z=C==null?void 0:C.costCenter)==null?void 0:z.newCostCenter)==null?void 0:y.code}`,{state:C})},u=p=>{console.log(p)};x(`/${f}/alter/fetchCoAreaCCDupliChk?ctrlAreaCCToCheck=${l}`,"get",o,u)},Ts=()=>{ks()},Fs=()=>{zs()},Re=()=>{at(!1),J(!1),Ut(!0),le(!1),Yt(!1),ie(""),oe(""),_("")},Le=()=>{J(!1),de(!1),re(!1),ie(""),oe(""),_(""),ct(""),Cs(""),lt(!1)},Ms=e=>{Q.current&&Q.current.contains(e.target)||Me(t=>!t)},Ps=e=>{if(e.target.value!==null){var t=e.target.value;let r={...c,costCenterName:t};h(q({module:"CostCenter",filterData:r}))}},Is=(e,t)=>{{var r=t;let l={...c,controllingArea:r};h(q({module:"CostCenter",filterData:l})),Gs(l),qs(l),Js(l)}},Os=(e,t)=>{{var r=t;let l={...c,companyCode:r};h(q({module:"CostCenter",filterData:l}))}},Rs=(e,t)=>{{var r=t;let l={...c,profitCenter:r};h(q({module:"CostCenter",filterData:l}))}},Ls=(e,t)=>{{var r=t;let l={...c,hierarchyArea:r};h(q({module:"CostCenter",filterData:l}))}},Ws=(e,t)=>{{var r=t;let l={...c,costCenterCategory:r};h(q({module:"CostCenter",filterData:l}))}};let Bs={"Person Responsible":`/${f}/data/getSalesOrg`,"Business Area":`/${f}/data/getBusinessArea`,"Functional Area":`/${f}/data/getFunctionalArea`};const Vs=e=>{const t=e.target.value;et(t),os([]),console.log("selected field",e.target.value),t.forEach(async r=>{const l=Bs[r];la(l)})},_s=[{title:"Person Responsible"},{title:"Business Area"},{title:"Functional Area"}],Us={"Task ID":"taskId",Status:"status",SalesOrganization:"salesOrg",Division:"division",OldMaterialNumber:"oldMaterialNumber","Lab/Office":"labOffice","Transportation Group":"transportationGroup","Batch management":"batchManagement","Person Responsible":"personResponsible","Business Area":"businessArea","Functional Area":"functionalArea"},Hs=()=>{const e=r=>{h(I({keyName:"CostCenterCategory",data:r.body}))},t=r=>{console.log(r)};x(`/${f}/data/getCostCenterCategory`,"get",e,t)},Ys=()=>{const e=r=>{h(I({keyName:"CostCenter",data:r.body}))},t=r=>{console.log(r)};x(`/${f}/data/getCostCenter`,"get",e,t)},qs=e=>{var l;const t=o=>{h(I({keyName:"ProfitCenterSearch",data:o.body}))},r=o=>{console.log(o)};x(`/${f}/data/getProfitCenterAsPerControllingArea?controllingArea=${(l=e.controllingArea)==null?void 0:l.code}`,"get",t,r)},Gs=e=>{var l;console.log("CA",e);const t=o=>{h(I({keyName:"HierarchyAreaSearch",data:o.body})),console.log("data",o)},r=o=>{console.log(o)};x(`/${f}/data/getHierarchyArea?controllingArea=${(l=e.controllingArea)==null?void 0:l.code}`,"get",t,r)},Ks=()=>{const e=r=>{h(I({keyName:"ControllingArea",data:r.body}))},t=r=>{console.log(r)};x(`/${f}/data/getControllingArea`,"get",e,t)},Js=e=>{var l;const t=o=>{h(I({keyName:"CompCodeSearch",data:o.body}))},r=o=>{console.log(o)};x(`/${ll}/data/getCompCode?contrllingArea=${(l=e==null?void 0:e.controllingArea)==null?void 0:l.code}`,"get",t,r)},Ct=e=>{const t=l=>{h(I({keyName:"CompanyCode",data:l.body}))},r=l=>{console.log(l)};x(`/${f}/data/getCompanyCodeBasedOnControllingArea?controllingArea=${e.code}`,"get",t,r)},Xs=e=>{const t=l=>{h(I({keyName:"CompanyCode",data:l.body}))},r=l=>{console.log(l)};x(`/${f}/data/getCompanyCodeBasedOnControllingArea?controllingArea=${e.code}`,"get",t,r)},Zs=e=>{const t=l=>{h(I({keyName:"CompCodeCopy",data:l.body}))},r=l=>{console.log(l)};x(`/${f}/data/getCostCenterBasedOnCOA?controllingArea=${e.code}`,"get",t,r)},Qs=()=>{let e="Basic Data";const t=l=>{h(nl(l.body))},r=l=>{console.log(l)};x(`/${f}/data/getViewFieldDetails?viewName=${e}`,"get",t,r)},js=()=>{let e="Control";const t=l=>{h(ol(l.body))},r=l=>{console.log(l)};x(`/${f}/data/getViewFieldDetails?viewName=${e}`,"get",t,r)},ea=()=>{let e="Templates";const t=l=>{h(cl(l.body))},r=l=>{console.log(l)};x(`/${f}/data/getViewFieldDetails?viewName=${e}`,"get",t,r)},ta=()=>{let e="Address";const t=l=>{h(il(l.body))},r=l=>{console.log(l)};x(`/${f}/data/getViewFieldDetails?viewName=${e}`,"get",t,r)},sa=()=>{let e="Communication";const t=l=>{h(dl(l.body))},r=l=>{console.log(l)};x(`/${f}/data/getViewFieldDetails?viewName=${e}`,"get",t,r)},aa=()=>{let e="History";const t=l=>{h(ul(l.body))},r=l=>{console.log(l)};x(`/${f}/data/getViewFieldDetails?viewName=${e}`,"get",t,r)};a.useEffect(()=>{Hs(),Qs(),js(),ea(),ta(),sa(),aa(),Ys(),Ks(),h(Ma({})),h(Pa())},[]);const la=e=>{const t=l=>{const o=l.body;cs(o)};console.log(st,"dinamicoptions=================="),x(e,"get",t,l=>{console.log(l)})},ra=()=>{ns("")},na=e=>{console.log("rmSearchForm",ue),ne(!0),e||(se(0),Ee(10),ae(0));let t={costCenterName:"",costCenter:(ue==null?void 0:ue.number)??"",controllingArea:"",companyCode:"",profitCenter:"",heirarchyArea:"",costCenterCategory:"",createdBy:"",fromDate:"",toDate:"",personResponsible:"",businessArea:"",functionalArea:"",top:1e3,skip:e??0};const r=o=>{var w,N,k,p;var u=[];for(let z=0;z<((N=(w=o==null?void 0:o.body)==null?void 0:w.list)==null?void 0:N.length);z++){var m=(k=o==null?void 0:o.body)==null?void 0:k.list[z];{var T={id:Lt(),description:m.Description,controllingArea:m.controllingArea,companyCode:m.CompanyCode,profitCenter:m.ProfitCenter,hierarchyArea:m.HeirarchyArea,costCenterCategory:m.CCtrCategory,costCenter:m.costCenter,CostCenterName:m.CostCenterName};u.push(T)}}Ze(u),ne(!1),gt(u.length),Ge((p=o==null?void 0:o.body)==null?void 0:p.count)};let l=o=>{console.log(o)};x(`/${f}/data/getCostCentersBasedOnAdditionalParams`,"post",r,l,t)},We=new Date,Ce=new Date;Ce.setDate(Ce.getDate()-15),a.useState([Ce,We]),a.useState([Ce,We]),console.log("newcontrollingarea",A);const ge=e=>{var o,u,m,T,w,N,k;console.log("rmSearchForm",c),ne(!0),e||(se(0),Ee(10),ae(0)),console.log(v,"filterFieldData============");let t={costCenterName:(c==null?void 0:c.costCenterName)??"",costCenter:"",controllingArea:((o=c==null?void 0:c.controllingArea)==null?void 0:o.code)??"",companyCode:((u=c==null?void 0:c.companyCode)==null?void 0:u.code)??"",profitCenter:((m=c==null?void 0:c.profitCenter)==null?void 0:m.code)??"",heirarchyArea:((T=c==null?void 0:c.hierarchyArea)==null?void 0:T.code)??"",costCenterCategory:((w=c==null?void 0:c.costCenterCategory)==null?void 0:w.code)??"",createdBy:"",fromDate:"",toDate:"",personResponsible:(v==null?void 0:v["Person Responsible"])??"",businessArea:((N=v==null?void 0:v["Business Area"])==null?void 0:N.code)??"",functionalArea:((k=v==null?void 0:v["Functional Area"])==null?void 0:k.code)??"",top:1e3,skip:e??0};const r=p=>{var Et,wt,Nt,kt;console.log("data",p.body.list);var z=[];for(let j=0;j<((wt=(Et=p==null?void 0:p.body)==null?void 0:Et.list)==null?void 0:wt.length);j++){var y=(Nt=p==null?void 0:p.body)==null?void 0:Nt.list[j];console.log("hshshsh",y);var $t={id:Lt(),description:y.Description,controllingArea:y.controllingArea,companyCode:y.CompanyCode,profitCenter:y.ProfitCenter,hierarchyArea:y.HeirarchyArea,costCenterCategory:y.CCtrCategory,costCenter:y.costCenter,CostCenterName:y==null?void 0:y.CostCenterName,businessArea:y.BusinessArea!==""?`${y.BusinessArea}`:"Not Available",functionalArea:y.FunctionalArea!==""?`${y.FunctionalArea}`:"Not Available",personResponsible:y.PersonResponsible!==""?`${y.PersonResponsible}`:"Not Available"};z.push($t)}console.log("tempobj",$t),console.log("tempObH",y),z.sort((j,za)=>Ye(j.createdOn,"DD MMM YYYY HH:mm")-Ye(za.createdOn,"DD MMM YYYY HH:mm")),Ze(z.reverse()),ne(!1),gt(z.length),Ge((kt=p==null?void 0:p.body)==null?void 0:kt.count)},l=p=>{console.log(p)};x(`/${f}/data/getCostCentersBasedOnAdditionalParams`,"post",r,l,t)};a.useState([]),a.useState([]),a.useState(null),a.useState(null);const[Al,gt]=a.useState(0);a.useState(!1),a.useState(!1),a.useState(!1),a.useState(!1),a.useState(!1),a.useState(!1),a.useState(""),a.useState("");const[oa,mt]=a.useState(!1),[ca,U]=a.useState(""),[ft,H]=a.useState(),Y=()=>{mt(!0)},pt=()=>{mt(!1)};a.useState(null);const ia=()=>{Bt(!0)};a.useState(null),a.useState(null);const da=()=>{h(Rt({module:"CostCenter"}))},ua=e=>{O(!0),console.log(e);const t=new FormData;if([...e].forEach(u=>t.append("files",u)),Es==="Change")var r=`/${f}/massAction/getAllCostCenterFromExcelForMassChange`;else var r=`/${f}/massAction/getAllCostCenterFromExcel`;x(r,"postformdata",u=>{var m;O(!1),console.log(u,"example"),h(hl((m=u==null?void 0:u.body)==null?void 0:m.controllingArea)),u.statusCode===200?(K(!1),h(Cl(u==null?void 0:u.body)),U("Create"),H(`${e.name} has been Uploaded Succesfully`),W("success"),je(!1),rt(!0),ia(),Qe(!0),te("/masterDataCockpit/costCenter/createMultipleCostCenter")):(K(!1),U("Error"),rt(!1),H("Error Uploading Cost Center Excel"),W("danger"),je(!1),Qe(!0),Y()),ma()},u=>{console.log(u)},t)},ha=e=>{console.log("newselection",e),ps(e);let t=Be.map(o=>o.field);const r=V.filter(o=>e.includes(o.id));let l=[];r.map(o=>{console.log("sssssss",o);let u={};t.forEach(m=>{console.log("yyyyy",o[m]),o[m]!==null&&(u[m]=o[m]||"")}),l.push(u),Gt(l),console.log("requiredArrayDetails",l)})};function Ca(){h(Rt({module:"CostCenter"})),ge()}a.useState([]),a.useState([]);const[vl,ga]=a.useState(!1);a.useState(null),a.useState(null),a.useState([]);const ma=()=>{ga(!1)};a.useState(null),a.useState("");const Be=[{field:"costCenter",headerName:"Cost Center",editable:!1,flex:1},{field:"CostCenterName",headerName:"Cost Center Name",editable:!1,flex:1},{field:"description",headerName:"Description",editable:!1,flex:1},{field:"controllingArea",headerName:"Controlling Area",editable:!1,flex:1},{field:"companyCode",headerName:"Company Code",editable:!1,flex:1},{field:"profitCenter",headerName:"Profit Center",editable:!1,flex:1},{field:"hierarchyArea",headerName:"Hierarchy Area",editable:!1,flex:1},{field:"costCenterCategory",headerName:"Cost Center Category",editable:!1,flex:1}],fa=G.map(e=>{const t=Us[e];return t?{field:t,headerName:e,editable:!1,flex:1}:null}).filter(e=>e!==null),pa=[...Be,...fa];a.useEffect(()=>{ge()},[]);let xa=a.useRef(null);const xt={convertJsonToExcel:()=>{let e=[];Be.forEach(t=>{t.headerName.toLowerCase()!=="action"&&!t.hide&&e.push({header:t.headerName,key:t.field})}),rl({fileName:`Cost Center Data-${Ye(We).format("DD-MMM-YYYY")}`,columns:e,rows:V})},button:()=>s($,{sx:{textTransform:"capitalize",position:"absolute",right:0,top:0},onClick:()=>xt.convertJsonToExcel(),children:"Download"})},ya=()=>{let e=r=>{const l=URL.createObjectURL(r),o=document.createElement("a");o.href=l,o.setAttribute("download","Cost Center_Mass Create.xls"),document.body.appendChild(o),o.click(),document.body.removeChild(o),URL.revokeObjectURL(l),Y(),U("Success"),H("Cost Center_Mass Create.xls has been downloaded successfully"),W("success")},t=r=>{r.message&&(Y(),U("Error"),H(`${r.message}`),W("danger"))};x(`/${f}/excel/downloadExcel`,"getblobfile",e,t)},Sa=()=>{var e=qt.map(l=>({costCenter:l.costCenter,controllingArea:l.controllingArea}));console.log("downloadPayload",e);let t=l=>{O(!1);const o=URL.createObjectURL(l),u=document.createElement("a");u.href=o,u.setAttribute("download","Cost Center_Mass Change.xls"),document.body.appendChild(u),u.click(),document.body.removeChild(u),URL.revokeObjectURL(o),Y(),U("Success"),H("Cost Center_Mass Change.xls has been downloaded successfully"),W("success")},r=l=>{l.message&&(Y(),U("Error"),H(`${l.message}`),W("danger"))};x(`/${f}/excel/downloadExcelWithData`,"postandgetblob",t,r,e)},ba=()=>{ws()},Aa=()=>{Ns()},va=()=>{K(!0),Ae("Create"),h(Ae("Create"))},Da=()=>{ke(e=>!e)},yt=(e,t)=>{t!==0&&(ys(t),ke(!1),t===1?va():t===2&&(h(Ae("Create")),ya()))},$a=e=>{X.current&&X.current.contains(e.target)||ke(!1)},Ea=()=>{Te(e=>!e)},wa=()=>{Me(e=>!e)},Na=e=>{Z.current&&Z.current.contains(e.target)||Te(!1)},St=(e,t)=>{console.log("indexx",t),t!==0&&(bs(t),Te(!1),t===1?ka():t===2&&(ht.length>0?(console.log("selectedRows",ht),O(!0),Ds(!1),Sa()):console.log("Please select at least one row to download Excel.")))},bt=(e,t)=>{t!==0&&(vs(t),Me(!1),t===1?Aa():t===2&&ba())},ka=()=>{K(!0),h(Ae("Change"))};return console.log("costCenterName",ce),s(al,{children:ss===!0?s(Ia,{}):n("div",{ref:xa,children:[s(Oa,{dialogState:oa,openReusableDialog:Y,closeReusableDialog:pt,dialogTitle:ca,dialogMessage:ft,handleDialogConfirm:pt,dialogOkText:"OK",dialogSeverity:Vt}),hs&&s(ReusableSnackBar,{openSnackBar:Wt,alertMsg:ft,handleSnackBarClose}),s("div",{style:{...Ra,backgroundColor:"#FAFCFF"},children:n(fe,{spacing:1,children:[n(i,{container:!0,sx:La,children:[n(i,{item:!0,md:5,sx:Wa,children:[s(d,{variant:"h3",children:s("strong",{children:"Cost Center"})}),s(d,{variant:"body2",color:"#777",children:"This view displays the list of Cost Centers"})]}),s(i,{item:!0,md:7,sx:{display:"flex"},children:n(i,{container:!0,direction:"row",justifyContent:"flex-end",alignItems:"center",spacing:0,children:[s(Ba,{title:"Search for multiple Cost Center numbers separated by comma",handleSearchAction:()=>na(),module:"CostCenter",keyName:"number",message:"Search Cost Center ",clearSearchBar:ra}),s(zt,{title:"Reload",children:s(ee,{sx:Tt,children:s(Va,{sx:{"&:hover":{transform:"rotate(360deg)",transition:"0.9s"}},onClick:Ca})})}),s(zt,{title:"Export Table",children:s(ee,{sx:Tt,onClick:xt.convertJsonToExcel,children:s(pe,{iconName:"IosShare"})})})]})})]}),s(i,{container:!0,sx:_a,children:s(i,{item:!0,md:12,children:n(Ua,{className:"filter-accordian",children:[s(Ha,{expandIcon:s(Ya,{}),"aria-controls":"panel1a-content",id:"panel1a-header",sx:{minHeight:"2rem !important",margin:"0px !important"},children:s(d,{sx:{fontWeight:"700"},children:"Search Cost Center"})}),n(qa,{sx:{padding:"0.5rem 1rem 0.5rem"},children:[n(i,{container:!0,rowSpacing:1,spacing:2,justifyContent:"space-between",alignItems:"center",children:[n(i,{container:!0,spacing:1,sx:{padding:"0rem 1rem 0.5rem"},children:[n(i,{item:!0,md:2,children:[s(d,{sx:F,children:"Cost Center Name"}),s(S,{size:"small",fullWidth:!0,children:s(b,{sx:{fontSize:"12px !important"},fullWidth:!0,size:"small",value:c==null?void 0:c.costCenterName,onChange:Ps,placeholder:"Enter Cost Center Name"})})]}),n(i,{item:!0,md:2,children:[s(d,{sx:F,children:"Controlling Area"}),s(S,{size:"small",fullWidth:!0,children:s(E,{sx:{height:"31px"},fullWidth:!0,size:"small",value:c==null?void 0:c.controllingArea,onChange:Is,options:(he==null?void 0:he.ControllingArea)??[],getOptionLabel:e=>e!=null&&e.code?`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??"":"",renderOption:(e,t)=>s("li",{...e,children:s(d,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>s(b,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"Select Controlling Area"})})})]}),n(i,{item:!0,md:2,children:[s(d,{sx:F,children:"Company Code"}),s(S,{fullWidth:!0,size:"small",children:s(E,{sx:{height:"31px"},fullWidth:!0,size:"small",onChange:Os,options:(g==null?void 0:g.CompCodeSearch)??[],getOptionLabel:e=>e!=null&&e.code?`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??"":"",value:c==null?void 0:c.companyCode,renderOption:(e,t)=>s("li",{...e,children:s(d,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>s(b,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"Select Company Code"})})})]}),n(i,{item:!0,md:2,children:[s(d,{sx:F,children:"Profit Center"}),s(S,{fullWidth:!0,size:"small",children:s(E,{sx:{height:"31px"},fullWidth:!0,size:"small",placeholder:"Select Profit Center",value:c==null?void 0:c.profitCenter,onChange:Rs,options:he.ProfitCenterSearch??[],getOptionLabel:e=>e!=null&&e.code?`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??"":"",renderOption:(e,t)=>s("li",{...e,children:s(d,{style:{fontSize:12},children:`${t==null?void 0:t.code} - ${t==null?void 0:t.desc}`})}),renderInput:e=>s(b,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"Select Profit Center"})})})]}),n(i,{item:!0,md:2,children:[s(d,{sx:F,children:"Hierarchy Area"}),s(S,{fullWidth:!0,size:"small",children:s(E,{sx:{height:"31px"},fullWidth:!0,size:"small",onChange:Ls,options:(g==null?void 0:g.HierarchyAreaSearch)??[],getOptionLabel:e=>e!=null&&e.code?`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??"":"",value:c==null?void 0:c.hierarchyArea,renderOption:(e,t)=>s("li",{...e,children:s(d,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>s(b,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"Select Hierarchy Area"})})})]}),n(i,{item:!0,md:2,children:[s(d,{sx:F,children:"Cost Center Category"}),s(S,{fullWidth:!0,size:"small",children:s(E,{sx:{height:"31px"},fullWidth:!0,size:"small",onChange:Ws,options:(g==null?void 0:g.CostCenterCategory)??[],getOptionLabel:e=>e!=null&&e.code?`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??"":"",value:c==null?void 0:c.costCenterCategory,renderOption:(e,t)=>s("li",{...e,children:s(d,{style:{fontSize:12},children:`${t==null?void 0:t.code} - ${t==null?void 0:t.desc}`})}),renderInput:e=>s(b,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"Select Cost Center Category"})})})]}),n(i,{item:!0,md:2,children:[s(d,{sx:F,children:"Add New Filters"}),s(S,{children:s(Ga,{sx:{font_Small:F,height:"31px",fontSize:"12px",width:"200px"},size:"small",multiple:!0,limitTags:2,value:G,onChange:Vs,renderValue:e=>e.join(", "),MenuProps:{MenuProps:jt},endAdornment:G.length>0&&s(ml,{position:"end",children:s(ee,{size:"small",onClick:()=>et([]),"aria-label":"Clear selections",children:s(Ka,{})})}),children:_s.map(e=>n(xe,{value:e.title,children:[s(Ja,{checked:G.indexOf(e.title)>-1}),e.title]},e.title))})}),s(i,{style:{display:"flex",justifyContent:"space-around"}})]})]}),s(i,{container:!0,sx:{flexDirection:"row",padding:"0rem 1rem 0.5rem"},gap:1,children:G.map((e,t)=>e==="Person Responsible"?s(i,{item:!0,children:n(fe,{children:[s(d,{sx:{fontSize:"12px"},children:e}),s(b,{sx:{font_Small:F,height:"31px",fontSize:"12px",width:"200px"},size:"small",fullWidth:!0,onChange:(r,l)=>tt({...v,[e]:r.target.value}),placeholder:`Enter ${e}`,value:v[e]})]})}):s(i,{item:!0,children:n(fe,{children:[s(d,{sx:{fontSize:"12px"},children:e}),s(E,{sx:{font_Small:F,height:"31px",fontSize:"12px",width:"200px"},size:"small",options:st??[],getOptionLabel:r=>`${r.code} - ${r.desc}`,placeholder:`Enter ${e}`,value:v[e],onChange:(r,l)=>tt({...v,[e]:l}),renderInput:r=>s(b,{sx:{fontSize:"12px !important"},...r,size:"small",placeholder:`Enter ${e}`,variant:"outlined"})})]})}))})]}),s(i,{container:!0,style:{display:"flex",justifyContent:"flex-end"},children:n(i,{item:!0,style:{display:"flex",justifyContent:"space-around"},children:[s($,{variant:"outlined",sx:Xa,onClick:da,children:"Clear"}),s($,{variant:"contained",sx:{...Za,...Qa},onClick:()=>ge(),children:"Search"})]})})]})]})})}),s(i,{item:!0,sx:{position:"relative"},children:s(fe,{children:s(ja,{isLoading:as,module:"CostCenter",width:"100%",title:"List of Cost Centers ("+qe+")",rows:V,columns:pa,page:De,pageSize:$e,rowCount:qe??(V==null?void 0:V.length)??0,onPageChange:Kt,onPageSizeChange:Xt,getRowIdValue:"id",hideFooter:!0,checkboxSelection:!0,disableSelectionOnClick:!0,status_onRowSingleClick:!0,onRowsSelectionHandler:ha,callback_onRowSingleClick:e=>{const t=e.row.costCenter.slice(0,10);console.log("materialNumber",t),te(`/masterDataCockpit/costCenter/displayCostCenter/${t}`,{state:e.row})},stopPropagation_Column:"action",status_onRowDoubleClick:!0,showCustomNavigation:!0})})}),el(Jt,"Cost Center","CreateCC")&&(B==null?void 0:B.role)==="Super User"||(B==null?void 0:B.role)==="Finance"?s(ye,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:n(tl,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:ls,onChange:e=>{rs(e)},children:[n(Ve,{variant:"contained",ref:Q,"aria-label":"split button",children:[s($,{size:"small",variant:"contained",onClick:()=>bt(Ie[0],0),children:Ie[0]}),s($,{size:"small","aria-controls":Fe?"split-button-menu":void 0,"aria-expanded":Fe?"true":void 0,"aria-label":"select action","aria-haspopup":"menu",onClick:wa,children:s(pe,{iconName:"ArrowDropUp",iconColor:"#FFFFFF"})})]}),s(_e,{sx:{zIndex:1},open:Fe,anchorEl:Q.current,placement:"top-end",children:s(ye,{style:{width:(At=Q.current)==null?void 0:At.clientWidth},children:s(Ue,{onClickAway:Ms,children:s(He,{id:"split-button-menu",autoFocusItem:!0,children:Ie.slice(1).map((e,t)=>s(xe,{selected:t===As-1,onClick:()=>bt(e,t+1),children:e},e))})})})}),n(Ve,{variant:"contained",ref:X,"aria-label":"split button",children:[s($,{size:"small",onClick:()=>yt(Pe[0],0),sx:{cursor:"default"},children:Pe[0]}),s($,{size:"small","aria-controls":Ne?"split-button-menu":void 0,"aria-expanded":Ne?"true":void 0,"aria-label":"select action","aria-haspopup":"menu",onClick:Da,children:s(pe,{iconName:"ArrowDropUp",iconColor:"#FFFFFF"})})]}),s(_e,{sx:{zIndex:1},open:Ne,anchorEl:X.current,placement:"top-end",children:s(ye,{style:{width:(vt=X.current)==null?void 0:vt.clientWidth},children:s(Ue,{onClickAway:$a,children:s(He,{id:"split-button-menu",autoFocusItem:!0,children:Pe.slice(1).map((e,t)=>s(xe,{selected:t===xs-1,onClick:()=>yt(e,t+1),children:e},e))})})})}),n(Ve,{variant:"contained",ref:Z,"aria-label":"split button",children:[s($,{size:"small",onClick:()=>St(Oe[0],0),sx:{cursor:"default"},children:Oe[0]}),s($,{size:"small","aria-controls":ze?"split-button-menu":void 0,"aria-expanded":ze?"true":void 0,"aria-label":"select action","aria-haspopup":"menu",onClick:Ea,children:s(pe,{iconName:"ArrowDropUp",iconColor:"#FFFFFF"})})]}),s(_e,{sx:{zIndex:1},open:ze,anchorEl:Z.current,placement:"top-end",children:s(ye,{style:{width:(Dt=Z.current)==null?void 0:Dt.clientWidth},children:s(Ue,{onClickAway:Na,children:s(He,{id:"split-button-menu",autoFocusItem:!0,children:Oe.slice(1).map((e,t)=>s(xe,{selected:t===Ss-1,onClick:()=>St(e,t+1),children:e},e))})})})}),n(Ft,{open:is,onClose:Re,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[n(Mt,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[s(d,{variant:"h6",children:"New Cost Center"}),s(ee,{sx:{width:"max-content"},onClick:Re,children:s(Pt,{})})]}),n(It,{sx:{padding:".5rem 1rem"},children:[n(i,{container:!0,spacing:3,children:[n(i,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[n(d,{children:["Controlling Area",s("span",{style:{color:"red"},children:"*"})]}),s(S,{fullWidth:!0,sx:{margin:".5em 0px"},children:s(E,{sx:{height:"42px"},required:"true",value:A,size:"small",onChange:(e,t)=>{ie(t),Ct(t)},options:(g==null?void 0:g.ControllingArea)??[],getOptionLabel:e=>e!=null&&e.code?`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??"":"",renderOption:(e,t)=>s("li",{...e,children:s(d,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>s(b,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT CONTROLLING AREA"})})})]}),n(i,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[n(d,{children:["Cost Center",s("span",{style:{color:"red"},children:"*"})]}),n(S,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px",flexDirection:"row"},children:[n(i,{md:5,children:[s(E,{sx:{height:"42px"},required:"true",size:"small",onChange:(e,t)=>{oe(t)},options:(g==null?void 0:g.CompanyCode)??[],getOptionLabel:e=>`${e==null?void 0:e.code}-${e==null?void 0:e.desc}`,renderOption:(e,t)=>s("li",{...e,children:s(d,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>s(b,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT COMPANY CODE",error:Xe})}),Xe&&s(d,{variant:"caption",color:"error",children:"Please Select Any value"})]}),n(i,{md:7,children:[s(b,{sx:{fontSize:"12px !important",height:"40px"},fullWidth:!0,size:"small",value:M,onChange:e=>{const t=e.target.value;if(/^[a-zA-Z0-9\-/'#&]*$/.test(t))if(t.length>0&&t[0]===" ")_(t.trimStart());else{let r=t.toUpperCase();_(r)}},inputProps:{length:6,maxLength:6,style:{textTransform:"uppercase"}},placeholder:"Enter Cost Center",error:Je,required:!0}),Je&&s(d,{variant:"caption",color:"error",children:"Cost Center must be 10 digits"})]})]})]}),n(i,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[n(d,{children:["Valid From",s("span",{style:{color:"red"},children:"*"})]}),s(S,{fullWidth:!0,sx:{margin:".5em 0px"},children:s(Se,{dateAdapter:be,children:s(ve,{slotProps:{textField:{size:"small"}},value:ce,onChange:e=>nt(e)})})})]}),n(i,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[n(d,{children:["Valid To",s("span",{style:{color:"red"},children:"*"})]}),s(S,{fullWidth:!0,sx:{margin:".5em 0px"},children:s(Se,{dateAdapter:be,children:s(ve,{slotProps:{textField:{size:"small"}},value:we,onChange:e=>ot(e),maxDate:new Date(9999,12,31)})})})]})]}),ms&&s(i,{children:s(d,{style:{color:"red"},children:"Please Enter Mandatory Fields"})}),dt&&s(i,{children:s(d,{style:{color:"red"},children:"*The Cost Center with Controlling Area already exists. Please enter different Cost Center or Controlling Area"})})]}),n(Ot,{sx:{display:"flex",justifyContent:"end"},children:[s($,{sx:{width:"max-content",textTransform:"capitalize"},onClick:Re,children:"Cancel"}),s($,{className:"button_primary--normal",type:"save",onClick:Ts,variant:"contained",disabled:!_t,children:"Proceed"})]})]}),n(Ft,{open:ds,onClose:Le,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[n(Mt,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[s(d,{variant:"h6",children:"New Cost Center"}),s(ee,{sx:{width:"max-content"},onClick:Le,children:s(Pt,{})})]}),n(It,{sx:{padding:".5rem 1rem"},children:[n(i,{container:!0,spacing:3,children:[n(i,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[n(d,{children:["Controlling Area",s("span",{style:{color:"red"},children:"*"})]}),s(S,{fullWidth:!0,sx:{margin:".5em 0px"},children:s(E,{sx:{height:"42px"},required:"true",size:"small",onChange:(e,t)=>{ie(t),Ct(t),Xs(t)},options:(g==null?void 0:g.ControllingArea)??[],getOptionLabel:e=>`${e==null?void 0:e.code}-${e==null?void 0:e.desc}`,renderOption:(e,t)=>s("li",{...e,children:s(d,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>s(b,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT CONTROLLING AREA"})})})]}),n(i,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[n(d,{children:["Cost Center",s("span",{style:{color:"red"},children:"*"})]}),n(S,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px",flexDirection:"row"},children:[s(i,{md:5,children:s(E,{sx:{height:"42px"},required:"true",size:"small",onChange:(e,t)=>{oe(t)},options:(g==null?void 0:g.CompanyCode)??[],getOptionLabel:e=>`${e==null?void 0:e.code}-${e==null?void 0:e.desc}`,renderOption:(e,t)=>s("li",{...e,children:s(d,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>s(b,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT COMPANY CODE"})})}),n(i,{md:7,children:[s(b,{sx:{fontSize:"12px !important",height:"40px"},fullWidth:!0,size:"small",value:M,onChange:e=>{const t=e.target.value;if(t.length>0&&t[0]===" ")_(t.trimStart());else{let r=t.toUpperCase();_(r)}},inputProps:{length:6,maxLength:6,style:{textTransform:"uppercase"}},placeholder:"Enter Cost Center",required:!0}),Ht&&s(d,{variant:"caption",color:"error",children:"Cost Center must be 10 digits"})]})]})]}),n(i,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[n(d,{children:["Valid From",s("span",{style:{color:"red"},children:"*"})]}),s(S,{fullWidth:!0,sx:{margin:".5em 0px"},children:s(Se,{dateAdapter:be,children:s(ve,{slotProps:{textField:{size:"small"}},value:ce,onChange:e=>nt(e)})})})]}),n(i,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[n(d,{children:["Valid To",s("span",{style:{color:"red"},children:"*"})]}),s(S,{fullWidth:!0,sx:{margin:".5em 0px"},children:s(Se,{dateAdapter:be,children:s(ve,{slotProps:{textField:{size:"small"}},value:we,onChange:e=>ot(e),maxDate:new Date(9999,12,31)})})})]}),s(sl,{sx:{width:"100%",marginLeft:"2%"},children:s("b",{children:"Copy From"})}),n(i,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[n(d,{children:["Controlling Area",s("span",{style:{color:"red"},children:"*"})]}),s(S,{fullWidth:!0,sx:{margin:".5em 0px"},children:s(E,{sx:{height:"42px"},required:"true",size:"small",onChange:(e,t)=>{ct(t),Zs(t)},options:(g==null?void 0:g.ControllingArea)??[],getOptionLabel:e=>`${e==null?void 0:e.code}-${e==null?void 0:e.desc}`,renderOption:(e,t)=>s("li",{...e,children:s(d,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>s(b,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT CONTROLLING AREA"})})})]}),n(i,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[n(d,{children:["Cost Center",s("span",{style:{color:"red"},children:"*"})]}),s(S,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px",flexDirection:"row"},children:s(i,{md:12,children:s(E,{sx:{height:"42px"},required:"true",size:"small",onChange:(e,t)=>{gs(t)},options:(g==null?void 0:g.CompCodeCopy)??[],getOptionLabel:e=>`${e==null?void 0:e.code}-${e==null?void 0:e.desc}`,renderOption:(e,t)=>s("li",{...e,children:s(d,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>s(b,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT COST CENTER"})})})})]})]}),fs&&s(i,{children:s(d,{style:{color:"red"},children:"Please Enter Mandatory Fields"})}),dt&&s(i,{children:s(d,{style:{color:"red"},children:"*The Cost Center with Controlling Area already exists. Please enter different Cost Center or Controlling Area"})})]}),n(Ot,{sx:{display:"flex",justifyContent:"end"},children:[s($,{sx:{width:"max-content",textTransform:"capitalize"},onClick:Le,children:"Cancel"}),s($,{className:"button_primary--normal",type:"save",onClick:Fs,variant:"contained",children:"Proceed"})]})]}),us&&s(gl,{artifactId:"",artifactName:"",setOpen:K,handleUpload:ua})]})}):""]})})]})})};export{Wl as default};
