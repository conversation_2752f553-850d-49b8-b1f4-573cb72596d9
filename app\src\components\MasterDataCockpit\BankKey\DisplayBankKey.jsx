import React, { useEffect, useState } from "react";
import ReusableSnackBar from "../../Common/ReusableSnackBar";
import {
  BottomNavigation,
  Box,
  Button,
  CardContent,
  Grid,
  IconButton,
  Paper,
  Stack,
  Tab,
  Tabs,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  FormControl,
  TextField,
  Autocomplete,
  Stepper,
  Step,
  StepLabel,
  CircularProgress,
  Backdrop,
  Card,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import {
  DateField,
  DatePicker,
  DesktopDatePicker,
  DesktopDateTimePicker,
  LocalizationProvider,
} from "@mui/x-date-pickers";
import {
  Timeline,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineItem,
  TimelineSeparator,
  timelineItemClasses,
} from "@mui/lab";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import {
  button_Outlined,
  iconButton_SpacingSmall,
  container_Padding,
  outermostContainer_Information,
  button_Primary,
} from "../../common/commonStyles";

import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import { checkIwaAccess, formValidator, idGenerator } from "../../../functions";
import { useDispatch, useSelector } from "react-redux";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import { useLocation, useNavigate } from "react-router-dom";
import {
  destination_BankKey,
  destination_CostCenter,
  destination_DocumentManagement,
} from "../../../destinationVariables";
import { doAjax } from "../../Common/fetchService";
// import { setDropDown } from "../../../app/dropDownDataSlice";
import moment from "moment/moment";
import ReusableDialog from "../../Common/ReusableDialog";
import EditableFieldForBankKey from "./EditableFieldForBankKey";
import lookup from "../../../data/lookup.json";
import ReusableAttachementAndComments from "../../Common/ReusableAttachmentAndComments/ReusableAttachementAndComments";
import ReusableTable from "../../Common/ReusableTable";
import { MatDownload, MatView } from "../../DocumentManagement/UtilDoc";
import { setDropDown } from "../../../app/dropDownDataSlice";
import { clearBankKey, setBankKeyViewData } from "../../../app/bankKeyTabSlice";
import { setPayloadWhole } from "../../../app/editPayloadSlice";
import LoadingComponent from "../../Common/LoadingComponent";
import ChangeLog from "../../Changelog/ChangeLog";
import TrackChangesTwoToneIcon from "@mui/icons-material/TrackChangesTwoTone";
import { CheckCircleOutlineOutlined } from "@mui/icons-material";
// import { CheckCircleOutlineOutlined } from "@mui/icons-material";

const DisplayBankKey = () => {
  const [isEditMode, setIsEditMode] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [isDisplayMode, setIsDisplayMode] = useState(true);
  const [responseFromAPI, setResponseFromAPI] = useState({});
  const [factorsArray, setFactorsArray] = useState([]);
  const [activeStep, setActiveStep] = useState(0);
  // const [compCode, setCompanyCode] = useState();
  const [bankKeyDetails, setBankKeyDetails] = useState([]);
  const [iDs, setIds] = useState();
  const [dupliDialog, setDupliDialog] = useState(false);
  const [value, setValue] = useState([]);
  const [messageDialogTitle, setMessageDialogTitle] = useState(false);
  const [successMsg, setsuccessMsg] = useState(false);
  const [dialogType, setDialogType] = useState("");
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [messageDialogSeverity, setMessageDialogSeverity] = useState(false);
  const [messageDialogOK, setMessageDialogOK] = useState(true);
  const [messageDialogExtra, setMessageDialogExtra] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [blurLoading, setBlurLoading] = useState(false);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [validateFlag, setValidateFlag] = useState(false);
  const [remarks, setRemarks] = useState("");
  const [submitForReviewDisabled, setSubmitForReviewDisabled] = useState(true);
  const [bkNumber, setBKNumber] = useState("");
  const [attachments, setAttachments] = useState([]);
  const [comments, setComments] = useState([]);
  const [openCorrectionDialog, setOpenCorrectionDialog] = useState(false);
  const [testrunStatus, setTestrunStatus] = useState(true);
  const [displayApiError, setDisplayApiError] = useState(false);
  const [formValidationErrorItems, setFormValidationErrorItems] = useState([]);
  const [openSnackbarValidation, setOpenSnackbarValidation] = useState(false);
  const [openCreateDialog, setOpenCreateDialog] = useState(false);
  const [openRemarkDialog, setOpenRemarkDialog] = useState(false);
  const [isChangeLogopen, setisChangeLogopen] = useState(false);
  const [remarksValidationError,setRemarksValidationError]=useState(false)
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const appSettings = useSelector((state) => state.appSettings);
  let iwaAccessData = useSelector(
    (state) => state.userManagement.entitiesAndActivities?.["Bank Key"]
  );
  let taskData = useSelector((state) => state?.initialData?.IWMMyTask);
  let userData = useSelector((state) => state.userManagement.userData);
  let bankKeyRowData = location.state; 
  let taskRowDetails = useSelector((state) => state.userManagement.taskData);
  const singleBKPayload = useSelector((state) => state.edit.payload);
  let singleBKPayloadAfterChange = useSelector((state) => state.edit.payload);
  let requiredFieldTabWise = useSelector(
    (state) => state.bankKey.requiredFields
  );
  const bankKeyViewData = useSelector((state) => state.bankKey.bankKeyViewData);
  console.log("ccroewdata", singleBKPayload);
  console.log("bankKeyRowData", bankKeyRowData);
  console.log("Remarks", iDs);
  const handleChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const attachmentColumns = [
    {
      field: "id",
      headerName: "Document ID",
      flex: 1,
      hide: true,
    },
    {
      field: "docType",
      headerName: "Type",
      flex: 1,
    },
    {
      field: "docName",
      headerName: "Document Name",
      flex: 1,
    },
    {
      field: "uploadedOn",
      headerName: "Uploaded On",
      flex: 1,
      align: "center",
      headerAlign: "center",
    },
    {
      field: "uploadedBy",
      headerName: "Uploaded By",
      sortable: false,
      flex: 1,
    },
    {
      field: "action",
      headerName: "Action",
      sortable: false,
      filterable: false,
      align: "center",
      headerAlign: "center",
      flex: 1,
      renderCell: (cellValues) => {
        return (
          <>
            <MatView index={cellValues.row.id} name={cellValues.row.docName} />
            <MatDownload
              index={cellValues.row.id}
              name={cellValues.row.docName}
            />
          </>
        );
      },
    },
  ];

//   const getAttachments = () => {
//     setBlurLoading(true);
//     let requestId = taskRowDetails?.subject
//       ? taskRowDetails?.subject
//       : bankKeyRowData?.requestId;
//     console.log("REQiD", requestId);
//     let hSuccess = (data) => {
//       setBlurLoading(false);
//       var attachmentRows = [];
//       data.documentDetailDtoList.forEach((doc) => {
//         var tempRow = {
//           id: doc.documentId,
//           docType: doc.fileType,
//           docName: doc.fileName,
//           uploadedOn: moment(doc.docCreationDate).format(appSettings.date),
//           uploadedBy: doc.createdBy,
//         };
//         if (true) attachmentRows.push(tempRow);
//       });
//       setAttachments(attachmentRows);
//     };
//     // invoiceHeaderData?.extInvNum &&
//     doAjax(
//       `/${destination_DocumentManagement}/documentManagement/getDocByRequestId/${requestId}`,
//       "get",
//       hSuccess
//     );
//   };
//  console.log('attachments', attachments)
//   const getComments = () => {
//     setBlurLoading(true);
//     let requestId = taskRowDetails?.subject
//       ? taskRowDetails?.subject
//       : bankKeyRowData?.requestId;
//     let hSuccess = (data) => {
//       console.log("commentsdata", data);
//       setBlurLoading(false);
//       var commentRows = [];
//       data.body.forEach((cmt) => {
//         var tempRow = {
//           id: cmt.requestId,
//           comment: cmt.comment,
//           user: cmt.createdByUser,
//           createdAt: cmt.updatedAt,
//         };
//         commentRows.push(tempRow);
//       });
//       setComments(commentRows);
//       console.log("commentrows", commentRows);
//     };

//     let hError = (error) => {
//       console.log(error);
//     };
//     // invoiceHeaderData?.extInvNum &&
//     doAjax(
//       `/${destination_BankKey}/activitylog/fetchTaskDetailsForRequestId?requestId=${requestId}`,
//       "get",
//       hSuccess,
//       hError
//     );
//   };


const getAttachments = () => {
  let requestId = taskRowDetails?.subject
  ? taskRowDetails?.subject
  : bankKeyRowData?.requestId;

  let hSuccess = (data) => {
    //alert(data.documentDetailDtoList)
    console.log(data.documentDetailDtoList, "data.documentDetailDtoList");
    var attachmentRows = [];
    data.documentDetailDtoList.forEach((doc) => {
      console.log(data.documentDetailDtoList, "data.");
      var tempRow = {
        id: doc.documentId,
        docType: doc.fileType,
        docName: doc.fileName,
        uploadedOn: moment(doc.docCreationDate).format(appSettings.date),
        uploadedBy: doc.createdBy,
      };
      console.log(tempRow, "tempRow");
      attachmentRows.push(tempRow);
    });
    setAttachments(attachmentRows);
  };
  // invoiceHeaderData?.extInvNum &&
  doAjax(
    `/${destination_DocumentManagement}/documentManagement/getDocByRequestId/${requestId}`,
    "get",
    hSuccess
  );
};
const getComments = () => {
  let requestId = taskRowDetails?.subject
      ? taskRowDetails?.subject
      : bankKeyRowData?.requestId;
  let hSuccess = (data) => {
    console.log("commentsdata", data);

    var commentRows = [];
    data.body.forEach((cmt) => {
      var tempRow = {
        id: cmt.requestId,
        comment: cmt.comment,
        user: cmt.createdByUser,
        createdAt: cmt.updatedAt,
      };
      commentRows.push(tempRow);
    });
    setComments(commentRows);
    console.log("commentrows", commentRows.length);
  };

  let hError = (error) => {
    console.log(error);
  };
  // invoiceHeaderData?.extInvNum &&
  doAjax(
    `/${destination_BankKey}/activitylog/fetchTaskDetailsForRequestId?requestId=${requestId}`,
    "get",
    hSuccess,
    hError
  );
};
console.log("activeTabName", factorsArray);
  // let datenew  = "21 Feb 2024";
  // console.log('datenew', Date.parse(datenew))
  const handleSetEditedPayload = () => {
    let activeTabName = factorsArray[activeStep];

    let viewDataArray = Object.entries(bankKeyViewData);

    // viewDataArray.push(costCenterViewData);

    console.log("viewDataArray", viewDataArray);
    const toSetArray = {};
    viewDataArray.map((item) => {
      console.log("bottle", item[1]);
      let temp = Object.entries(item[1]); //Basic Data\
      console.log("notebook", temp);
      temp.forEach((fieldGroup) => {
        fieldGroup[1].forEach((field) => {
          toSetArray[
            field.fieldName
              .replaceAll("(", "")
              .replaceAll(")", "")
              .replaceAll("/", "")
              .replaceAll("-", "")
              .replaceAll(".", "")
              .split(" ")
              .join("")
          ] = field.value;
        });
      });
      return item;
    });
    console.log("toSetArray", toSetArray);
    dispatch(setPayloadWhole(toSetArray));
  };

  useEffect(() => {
    if (bankKeyViewData.length === 0) {
      return;
    }
    handleSetEditedPayload();
  }, [bankKeyViewData]);

  var payload = {
    AddressDto: {
      AddressID: iDs?.AddressId ? iDs?.AddressId : "",
      // AddressID: "",
      Title: singleBKPayload?.Title ? singleBKPayload?.Title : "",
      Name: singleBKPayload?.Name ? singleBKPayload?.Name : "",
      Name2: singleBKPayload?.Name1 ? singleBKPayload?.Name1 : "",
      Name3: singleBKPayload?.Name2 ? singleBKPayload?.Name2 : "",
      Name4: singleBKPayload?.Name3 ? singleBKPayload?.Name3 : "",
      Sort1: singleBKPayload?.SearchTerm1 ? singleBKPayload?.SearchTerm1 : "",
      Sort2: singleBKPayload?.SearchTerm2 ? singleBKPayload?.SearchTerm2 : "",
      BuildLong: singleBKPayload?.BuildingCode
        ? singleBKPayload?.BuildingCode
        : "",
      RoomNo: singleBKPayload?.RoomNumber ? singleBKPayload?.RoomNumber : "",
      Floor: singleBKPayload?.Floor ? singleBKPayload?.Floor : "",
      COName: singleBKPayload?.co ? singleBKPayload?.co : "",
      StrSuppl1: singleBKPayload?.Street1 ? singleBKPayload?.Street1 : "",
      StrSuppl2: singleBKPayload?.Street2 ? singleBKPayload?.Street2 : "",
      Street: singleBKPayload?.Street3 ? singleBKPayload?.Street3 : "",
      HouseNo: singleBKPayload?.HouseNumber ? singleBKPayload?.HouseNumber : "",
      HouseNo2: singleBKPayload?.Supplement ? singleBKPayload?.Supplement : "",
      StrSuppl3: singleBKPayload?.Street4 ? singleBKPayload?.Street4 : "",
      Location: singleBKPayload?.Street5 ? singleBKPayload?.Street5 : "",
      District: singleBKPayload?.District ? singleBKPayload?.District : "",
      HomeCity: singleBKPayload?.OtherCity ? singleBKPayload?.OtherCity : "",
      PostlCod1: singleBKPayload?.PostalCode ? singleBKPayload?.PostalCode : "",
      PostlCod2: singleBKPayload?.PostalCode1
        ? singleBKPayload?.PostalCode1
        : "",
      PostlCod3: singleBKPayload?.CompanyPostCd
        ? singleBKPayload?.CompanyPostCd
        : "",
      PoBox: singleBKPayload?.POBox ? singleBKPayload?.POBox : "",
      PoBoxCit: singleBKPayload?.POBoxCity ? singleBKPayload?.POBoxCity : "",
      PoBoxReg: singleBKPayload?.Region2?.code
        ? singleBKPayload?.Region2?.code
        : singleBKPayload?.Region2
        ? singleBKPayload?.Region2
        : "",
      PoboxCtry: singleBKPayload?.Country2?.code
        ? singleBKPayload?.Country2?.code
        : singleBKPayload?.Country2
        ? singleBKPayload?.Country2
        : "",
      Country: singleBKPayload?.Country1?.code
        ? singleBKPayload?.Country1?.code
        : singleBKPayload?.Country1
        ? singleBKPayload?.Country1
        : "",
      TimeZone: singleBKPayload?.TimeZone?.code ? singleBKPayload?.TimeZone?.code : singleBKPayload?.TimeZone ? singleBKPayload?.TimeZone : "",
      Taxjurcode: singleBKPayload?.TaxJurisdiction?.code
        ? singleBKPayload?.TaxJurisdiction?.code
        : singleBKPayload?.TaxJurisdiction
        ? singleBKPayload?.TaxJurisdiction
        : "",
      Transpzone: singleBKPayload?.TransportZone?.code
        ? singleBKPayload?.TransportZone?.code
        : singleBKPayload?.TransportZone
        ? singleBKPayload?.TransportZone
        : "",
      Regiogroup: singleBKPayload?.StructureGroup?.code
        ? singleBKPayload?.StructureGroup?.code
        : singleBKPayload?.StructureGroup
        ? singleBKPayload?.StructureGroup
        : "",
      DontUseS: singleBKPayload?.Undeliverable?.code
        ? singleBKPayload?.Undeliverable?.code
        : singleBKPayload?.Undeliverable
        ? singleBKPayload?.Undeliverable
        : "",
      DontUseP: singleBKPayload?.Undeliverable1?.code
        ? singleBKPayload?.Undeliverable1?.code
        : singleBKPayload?.Undeliverable1
        ? singleBKPayload?.Undeliverable1
        : "",
      PoWONo: singleBKPayload?.POBoxwoNo === true ? "X" : "",
      DeliServType: singleBKPayload?.DelvryServType
        ? singleBKPayload?.DelvryServType
        : "",
      DeliServNumber: singleBKPayload?.DeliveryServiceNo
        ? singleBKPayload?.DeliveryServiceNo
        : "",
      Township: singleBKPayload?.Township ? singleBKPayload?.Township : "",
      Langu: singleBKPayload?.Language?.code
        ? singleBKPayload?.Language?.code
        : singleBKPayload?.Language
        ? singleBKPayload?.Language
        : "",
      Tel1Numbr: singleBKPayload?.Telephone ? singleBKPayload?.Telephone : "",
      Tel1Ext: singleBKPayload?.Extension ? singleBKPayload?.Extension : "",
      FaxNumber: singleBKPayload?.Fax ? singleBKPayload?.Fax : "",
      MobilePhone: singleBKPayload?.MobilePhone
        ? singleBKPayload?.MobilePhone
        : "",
      FaxExtens: singleBKPayload?.Extension1 ? singleBKPayload?.Extension1 : "",
      EMail: singleBKPayload?.EMailAddress ? singleBKPayload?.EMailAddress : "",
      AdrNotes: singleBKPayload?.Notes ? singleBKPayload?.Notes : "",
      Region: singleBKPayload?.Region1?.code
        ? singleBKPayload?.Region1?.code
        : singleBKPayload?.Region1
        ? singleBKPayload?.Region1
        : "",
      PoBoxLobby: singleBKPayload?.PoBoxLobby
        ? singleBKPayload?.PoBoxLobby
        : "",
    },
    BankKeyID: iDs?.BankKeyId ? iDs?.BankKeyId : "",
    // BankKeyID: "",
    ReqCreatedBy: userData?.user_id,
    ReqCreatedOn: taskRowDetails?.createdOn
      ? "/Date(" + taskRowDetails?.createdOn + ")/"
      : bankKeyRowData?.createdOn
      ? "/Date(" + Date.parse(bankKeyRowData?.createdOn) + ")/"
      : "",
    RequestStatus: bankKeyRowData?.reqStatus ? bankKeyRowData?.reqStatus : "",
    CreationId:
      taskRowDetails?.processDesc === "Create"
        ? taskRowDetails?.subject.slice(3)
        : bankKeyRowData?.requestType === "Create"
        ? bankKeyRowData?.requestId?.slice(3)
        : "",
    EditId:
      taskRowDetails?.processDesc === "Change"
        ? taskRowDetails?.subject.slice(3)
        : bankKeyRowData?.requestType === "Change"
        ? bankKeyRowData?.requestId?.slice(3)
        : "",
    DeleteId: "",
    MassCreationId: "",
    MassEditId: "",
    MassDeleteId: "",
    RequestType:
      bankKeyRowData?.requestType === "Create"
        ? "Create"
        : bankKeyRowData?.requestType === "Change"
        ? "Change"
        : taskRowDetails?.processDesc === "Create"
        ? "Create"
        : taskRowDetails?.processDesc === "Change"
        ? "Change"
        : "Change",
    TaskId: taskRowDetails?.taskId ? taskRowDetails?.taskId : "",
    Remarks: remarks ? remarks : "",
    Action:
      bankKeyRowData?.requestType === "Create"
        ? "I"
        : bankKeyRowData?.requestType === "Change"
        ? "U"
        : taskRowDetails?.processDesc === "Create"
        ? "I"
        : taskRowDetails?.processDesc === "Change"
        ? "U"
        : "U",
    Validation: testrunStatus === true ? "X" : "",
    BankCtry: bankKeyRowData?.bankCtryReg
      ? bankKeyRowData?.bankCtryReg
      : iDs?.BankCtry
      ? iDs?.BankCtry
      : "",
    BankKey: bankKeyRowData?.bankKey
      ? bankKeyRowData?.bankKey
      : iDs?.BankKey
      ? iDs?.BankKey
      : "",
    BankName: singleBKPayload?.BankName ? singleBKPayload?.BankName : "",
    BankRegion: singleBKPayload?.Region?.code
      ? singleBKPayload?.Region?.code
      : singleBKPayload?.Region
      ? singleBKPayload?.Region
      : "",
    BankStreet: singleBKPayload?.Street ? singleBKPayload?.Street : "",
    City: singleBKPayload?.City ? singleBKPayload?.City : "",
    BankBranch: singleBKPayload?.BankBranch ? singleBKPayload?.BankBranch : "",
    SwiftCode: singleBKPayload?.SWIFTBIC ? singleBKPayload?.SWIFTBIC : "",
    BankGroup: singleBKPayload?.BankGroup ? singleBKPayload?.BankGroup : "",
    PobkCurac: singleBKPayload?.PostbankAcct === true ? "X" : "",
    BankNo: singleBKPayload?.BankNumber ? singleBKPayload?.BankNumber : "",
  };

  const getRegion = (value) => {
    console.log("valueeeeeeeeeeee",value);
    const hSuccess = (data) => {
      console.log("121212", data);
      dispatch(
        setDropDown({
          keyName: "Region",
          data: data.body,
        })
      );
    };
    const hError = (error) => {
      console.log(error, "error in dojax");
    };
    doAjax(
      `/${destination_BankKey}/data/getRegionBasedOnCountry?country=${value}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getRegion1 = (value) => {
    const hSuccess = (data) => {
      console.log("value", value);
      dispatch(setDropDown({ keyName: "Region1", data: data.body }));
    };
    const hError = (error) => {
      console.log(error, "error in dojax");
    };
    doAjax(
      `/${destination_BankKey}/data/getRegionBasedOnCountry?country=${value}`,
      "get",
      hSuccess,
      hError
    );
  };

  // Loader and lookup for independent apis start
  const [apiCount, setApiCount] = useState(0);
  const fetchDynamicApiData = (keyName, endPoint) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: keyName, data: data.body }));
      // setIsLoading(false);
      setApiCount((prev) => prev + 1);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_BankKey}/data/${endPoint}`, "get", hSuccess, hError);
  };
  const getAllLookups = () => {
    lookup?.bankKey?.map((item) => {
      fetchDynamicApiData(item?.keyName, item?.endPoint);
    });
  };
  const loaderCount = () => {
    if (apiCount == lookup?.bankKey?.length) {
      setBlurLoading(false);
    } else {
      setBlurLoading(true);
    }
  };
  useEffect(() => {
    loaderCount();
  }, [apiCount]);

  // Loader and lookup for independent apis end

  useEffect(() => {
    getAllLookups();
    getAttachments();
    getComments();
  }, []);


  useEffect(() => {
    setBKNumber(idGenerator("BK"));
  }, []);

  console.log("taskData", taskData);
  console.log("taskRowDetails", taskRowDetails);
  const getBankKeyDisplayData = () => {
    setIsLoading(true);
    var payload = taskData?.body?.id
      ? {
          id: taskData?.body?.id ? taskData?.body?.id : "",
          bankKey: taskData?.body?.bankKey ? taskData?.body?.bankKey : "",
          bankCtry: taskData?.body?.bankCtry,
          reqStatus: taskData?.body?.reqStatus,
          screenName:
            taskRowDetails?.processDesc === "Create" ? "Create" : "Change",
        }
      : {
          id: bankKeyRowData?.reqStatus ? bankKeyRowData?.id : "",
          bankKey: bankKeyRowData?.bankKey ? bankKeyRowData?.bankKey : "",
          bankCtry: bankKeyRowData?.bankCtryReg
            ? bankKeyRowData?.bankCtryReg
            : "",
          reqStatus: bankKeyRowData?.reqStatus
            ? bankKeyRowData?.reqStatus
            : "Approved",
          screenName: bankKeyRowData?.requestType
            ? bankKeyRowData?.requestType
            : "Change",
        };

    console.log("payload", payload);
    const hSuccess = (data) => {
      // data.statusCode = 300;
      if(data.statusCode === 200){
        setIsLoading(false);
        getRegion(data?.body?.BankCtry)
        getRegion1(data?.body?.viewData?.["Address Details"]?.["Street Address"]?.find((field) => field?.fieldName === "Country 1").value)
        console.log("dataaaaaaa", data.body);
        // getRegion(data?.body?.BankCtry);
        const responseBody = data.body.viewData;
        const responseIDs = data.body;
        dispatch(setBankKeyViewData(responseBody));
        // getCurrency(
        //   responseBody["Basic Data"]["Basic Data"].find(
        //     (field) => field?.fieldName === "Comp Code"
        //   ).value
        // );

        const categoryKeys = Object.keys(responseBody);
        console.log("categorykeys", categoryKeys);

        if(!bankKeyRowData?.requestType && !taskRowDetails?.processDesc && !isEditMode)
        {
          setFactorsArray(categoryKeys.slice(0,-1));
        }
        else{
          setFactorsArray(categoryKeys);
        }
        
        console.log("factorsarrayyyyy",factorsArray);
        const attachment = ["Attachment & Documents"];
        factorsArray.concat(attachment);
        const mappedData = categoryKeys.map((category) => ({
          category,
          data: responseBody[category],
        }));
        setBankKeyDetails(mappedData);
        setIds(responseIDs);
        // dispatch(setSingleBankKeyPayload(data));
      } else {
        setIsLoading(false);
        // debugger
        // setBlurLoading(false);
        setDisplayApiError(true);
        setsuccessMsg(false);
        setMessageDialogTitle("Error");
        setMessageDialogMessage("Unable to fetch data of Bank Key");
        setDialogType("danger");
        setMessageDialogSeverity("danger");
        // setMessageDialogExtra(true);
        setMessageDialogOK(true);
        // debugger
        setOpenMessageDialog(true);
        
      }
      
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_BankKey}/data/displayBankKey`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  useEffect(() => {
    getBankKeyDisplayData();
    // getRegion(bankKeyRowData?.bankCtryReg?bankKeyRowData?.bankCtryReg:iDs?.BankCtry?iDs?.BankCtry:"");
  }, []);

  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };
  const handleMessageDialogNavigate = () => {
    navigate("/masterDataCockpit/bankKey");
  };
  const handleSnackBarClose = () => {
    if (validateFlag) {
      setopenSnackbar(false);
      setValidateFlag(false);
    } else {
      setopenSnackbar(false);
      navigate("/masterDataCockpit/bankKey");
    }
  };
  const handleCheckValidationError = () => {
    return formValidator(
      singleBKPayloadAfterChange,
      requiredFieldTabWise,
      setFormValidationErrorItems
    );
  };
  console.log("formvalidation", formValidationErrorItems);

  const handleBack = () => {
    const isValidation = handleCheckValidationError();
    if (isValidation) {
      setActiveStep((prevActiveStep) => prevActiveStep - 1);
      dispatch(clearBankKey());
    } else {
      handleSnackBarOpenValidation();
    }
  };
  const handleNext = () => {
    const isValidation = handleCheckValidationError();
    if (isValidation) {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
      dispatch(clearBankKey());
    } else {
      handleSnackBarOpenValidation();
    }
  };

  const duplicateCheck = () => {
    setBlurLoading(true);
    let selectedControllingArea = bankKeyRowData?.controllingArea;
    let selectedCostCenterName = bankKeyRowData?.costCenter?.split(" ")[0];
    let result = "";
    // let result = selectedControllingArea.concat("$$",selectedCostCenterName);
    // console.log("sendNewCostCenterData", result);
    // let ctrlAreaCCToCheck = (sendNewCostCenterData?.controllingAreaData?.newControllingArea).join(sendNewCostCenterData?.costCenterName?.newCostCenterName)
    // console.log("ctrlAreaCCToCheck",(sendNewCostCenterData?.controllingAreaData?.newControllingArea).join(sendNewCostCenterData?.costCenterName?.newCostCenterName));
    const hSuccess = (data) => {
      setBlurLoading(false);
      console.log("dupli", data);
      if (data.body) {
        // setIsEditMode(true);
        // setIsDisplayMode(false);
      } else {
        setDupliDialog(true);
      }
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/alter/fetchCoAreaCCDupliChk?ctrlAreaCCToCheck=${result}`,
      "get",
      hSuccess,
      hError
    );
  };
  const onEdit = () => {
    setIsEditMode(true);
    factorsArray.push("Attachments & Comments")
    setIsDisplayMode(false);
  };
  const onBankKeySubmitChange = () => {
    handleBankKeySubmitChange();
  };
  const onBankKeySubmitCreate = () => {
    handleBankKeySubmitCreate();
  };

  const onBankKeyReviewChange = () => {
    setIsLoading(true);
    handleBankKeyReviewChange();
    setTestrunStatus(false);
  };
  const onBankKeyReviewCreate = () => {
    handleBankKeyReviewCreate();
    setTestrunStatus(false);
  };

  const onBankKeySaveAsDraftChange = () => {
    setIsLoading(true);
    handleBankKeySaveAsDraftChange();
  };

  const onBankKeySaveAsDraft = () => {
    setIsLoading(true);
    handleBankKeySaveAsDraft();
  };

  const onBankKeyCorrectionChange = () => {
    handleBankKeyCorrectionChange();
  };
  const onCostCenterCorrectionCreate = () => {
    handleCostCenterCorrectionCreate();
  };

  const onBankKeyApproveChange = () => {
    handleBankKeyApproveChange();
  };
  const onBankKeyApproveCreate = () => {
    handleBankKeyApproveCreate();
  };
  const onCostCenterRereview = () => {
    handleCostCenterRereview();
  };
  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };
  const handleSnackBarOpen = () => {
    setopenSnackbar(true);
  };
  const onValidateBankKey = () => {
    setBlurLoading(true);
    const hSuccess = (data) => {
      setBlurLoading(false);
      if (data.statusCode === 201) {
        setSubmitForReviewDisabled(false);
        setMessageDialogTitle("Create");
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `All Data has been Validated. Bank Key can be Send for Review`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setBlurLoading(false);
        setValidateFlag(true);
        setTestrunStatus(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        // setMessageDialogMessage(`${data.body.message[0]}`);
        if (data.body.message.length > 0) {
          setMessageDialogMessage(data.body.message[0]);
        } else {
          setMessageDialogMessage(data.body.value);
        }
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setBlurLoading(false);
        
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_BankKey}/alter/validateSingleBankKey`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const onBankKeyCorrection = () => {
    if (
      userData?.role === "MDM Steward" &&
      (bankKeyRowData?.requestType === "Create" ||
        taskRowDetails?.processDesc === "Create")
    ) {
      handleCorrectionMDMCreate();
    } else if (
      userData?.role === "MDM Steward" &&
      (bankKeyRowData?.requestType === "Change" ||
        taskRowDetails?.processDesc === "Change")
    ) {
      handleCorrectionMDMChange();
    } else if (
      userData?.role === "Approver" &&
      (bankKeyRowData?.requestType === "Create" ||
        taskRowDetails?.processDesc === "Create")
    ) {
      handleCorrectionApproverCreate();
    } else if (
      userData?.role === "Approver" &&
      (bankKeyRowData?.requestType === "Change" ||
        taskRowDetails?.processDesc === "Change")
    ) {
      handleCorrectionApproverChange();
    }
  };
  const handleCorrectionMDMCreate = () => {
    setIsLoading(true);
    const hSuccess = (data) => {
      // setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Bank Key Submitted for Correction with ID NBS${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Bank Key for Correction");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    console.log("remarkssssssssss", remarks);
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_BankKey}/alter/bankKeySendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const handleCorrectionMDMChange = () => {
    setIsLoading(true);
    const hSuccess = (data) => {
      // setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Bank Key Submitted for Correction with ID CBS${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Bank Key for Correction");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    console.log("hsdfjgdh", payload);
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_BankKey}/alter/changeBankKeySendForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCorrectionApproverCreate = () => {
    setIsLoading(true);
    const hSuccess = (data) => {
      // setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Bank Key Submitted for Correction with ID NBS${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Bank Key for Correction");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_BankKey}/alter/bankKeySendForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const handleCorrectionApproverChange = () => {
    setIsLoading(true);
    const hSuccess = (data) => {
      // setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Bank Key Submitted for Correction with ID CBS${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Bank Key for Correction");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    console.log("remarksssaaaa", remarks);
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_BankKey}/alter/changeBankKeySendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const onSubmitForApprovalButtonClick = () => {
    // const isValidation = handleCheckValidationError();
    // if (isValidation) {
    setIsLoading(true);
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Change");
        setMessageDialogMessage(
          `Bank Key has been submitted for approval CBS${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);

        const secondApiPayload = {
          artifactId: bkNumber,
          createdBy: userData?.emailId,
          artifactType: "BankKey",
          requestId: `CBS${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
        navigate("/masterDataCockpit/bankKey");
      } else {
        setMessageDialogTitle("Change");
        setsuccessMsg(false);
        setMessageDialogMessage("Change Failed");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_BankKey}/alter/changeBankKeyApprovalSubmit`,
      "post",
      hSuccess,
      hError,
      payload
    );
    // } else {
    //   handleSnackBarOpenValidation();
    // }
  };

  const handleCreateDialog = () => {
    setTestrunStatus(false);
    setOpenCreateDialog(true);
  };
  const handleCreateDialogClose = () => {
    setRemarks("");
    setTestrunStatus(true);
    setOpenCreateDialog(false);
  };
  const tabContents = factorsArray
    .map((item) => {
      // const ddata = Object.entries(allTabs).filter((i) => {
      //   return reference[i[0]]?.split(" ")[0] == item?.split(" ")[0];
      // })[0]?.[1];

      const mdata = bankKeyDetails.filter(
        (ii) => ii.category?.split(" ")[0] == item?.split(" ")[0]
      );
      if (mdata.length != 0) {
        return { category: item?.split(" ")[0], data: mdata[0].data };
      }
      // return { category: item?.split(" ")[0], data: ddata };
    })
    .map((categoryData, index) => {
      console.log("categoryData", categoryData?.category);
      if (categoryData?.category == "Bank" && activeStep == 0) {
        return [
          <Grid
            key={categoryData.category}
            container
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              // borderRadius: "8px",
              // border: "1px solid #E0E0E0",
              mt: 1,
              // boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
              // padding: "10px",
              mb: 1,
            }}
          >
            {Object.keys(categoryData.data).map((fieldGroup) => (
              <Grid
                key={fieldGroup}
                item
                md={12}
                sx={{
                  backgroundColor: "white",
                  maxHeight: "max-content",
                  height: "max-content",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                  mt: 0.25,
                  boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                  ...container_Padding,
                  // ...container_columnGap,
                }}
              >
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                    margin: "0px !important",
                  }}
                >
                  {fieldGroup}
                </Typography>
                <Box sx={{ width: "100%" }}>
                  <CardContent
                    sx={{
                      padding: "0",
                      paddingBottom: "0 !important",
                      paddingTop: "10px !important",
                    }}
                  >
                    <Grid
                      container
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(6,1fr)",
                        gap: "15px",
                      }}
                      justifyContent="space-between"
                      alignItems="flex-start"
                      md={12}
                    >
                      {categoryData.data[fieldGroup].map((field) => {
                        console.log("fieldDatatttt", field);
                        return (
                          <EditableFieldForBankKey
                            data={singleBKPayload}
                            // key={field.fieldName}
                            length={field.maxLength}
                            label={field.fieldName}
                            value={field.value}
                            visibility={field.visibility}
                            onSave={(newValue) =>
                              handleFieldSave(field.fieldName, newValue)
                            }
                            isEditMode={isEditMode}
                            // isExtendMode={isExtendMode}
                            type={field.fieldType}
                            field={field} // Update the type as needed
                          />
                        );
                      })}
                    </Grid>
                  </CardContent>
                </Box>
              </Grid>
            ))}
          </Grid>,
        ];
      } else if (categoryData?.category == "Address" && activeStep == 1) {
        return [
          <Grid
            key={categoryData.category}
            container
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              mt: 1,
              mb: 1,
            }}
          >
            {Object.keys(categoryData.data).map((fieldGroup) => (
              <Grid
                key={fieldGroup}
                item
                md={12}
                sx={{
                  backgroundColor: "white",
                  maxHeight: "max-content",
                  height: "max-content",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                  mt: 0.25,
                  boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                  ...container_Padding,
                  // ...container_columnGap,
                }}
              >
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                    margin: "0px !important",
                  }}
                >
                  {fieldGroup}
                </Typography>
                <Box sx={{ width: "100%" }}>
                  <CardContent>
                    <Grid
                      container
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(6,1fr)",
                        gap: "15px",
                      }}
                      justifyContent="space-between"
                      alignItems="flex-start"
                      md={12}
                    >
                      {categoryData.data[fieldGroup].map((field) => (
                        <EditableFieldForBankKey
                          data={singleBKPayload}
                          key={field.fieldName}
                          label={field.fieldName}
                          value={field?.value}
                          onSave={(newValue) =>
                            handleFieldSave(field.fieldName, newValue)
                          }
                          isEditMode={isEditMode}
                          type={field.fieldType} // Update the type as needed
                        />
                      ))}
                    </Grid>
                  </CardContent>
                </Box>
              </Grid>
            ))}
          </Grid>,
        ];
      } else if (categoryData?.category == "Attachments" && activeStep == 2) {
        return [
          <>
            {!isEditMode ? (
              <Card sx={{ padding: "1rem 1rem 0rem 1rem" }}>
                <Grid
                  container
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                  }}
                >
                  <Typography variant="h6">
                    <strong>Attachments</strong>
                  </Typography>
                </Grid>
                {Boolean(attachments.length) && (
                  <ReusableTable
                    width="100%"
                    rows={attachments}
                    columns={attachmentColumns}
                    hideFooter={false}
                    getRowIdValue={"id"}
                    disableSelectionOnClick={true}
                    stopPropagation_Column={"action"}
                  />
                )}
                {!Boolean(attachments.length) && (
                  <Typography variant="body2">No Attachments Found</Typography>
                )}
                <br />
                <Typography variant="h6">Comments</Typography>
                {Boolean(comments.length) && (
                  <Timeline
                    sx={{
                      [`& .${timelineItemClasses.root}:before`]: {
                        flex: 0,
                        padding: 0,
                      },
                    }}
                  >
                    {comments.map((comment) => (
                      <TimelineItem>
                        <TimelineSeparator>
                          <TimelineDot>
                            <CheckCircleOutlineOutlined
                              sx={{ color: "#757575" }}
                            />
                          </TimelineDot>
                          <TimelineConnector />
                        </TimelineSeparator>
                        <TimelineContent sx={{ py: "12px", px: 2 }}>
                          <Card
                            elevation={0}
                            sx={{
                              border: 1,
                              borderColor: "#C4C4C4",
                              borderRadius: "8px",
                              width: "650px",
                            }}
                          >
                            <Box sx={{ padding: "1rem" }}>
                              <Stack spacing={1}>
                                <Grid
                                  sx={{
                                    display: "flex",
                                    justifyContent: "space-between",
                                  }}
                                >
                                  <Typography
                                    sx={{
                                      textAlign: "right",
                                      color: " #757575",
                                      fontWeight: "500",
                                      fontSize: "12px",
                                    }}
                                  >
                                    {moment(comment.createdAt).format(
                                      "DD MMM YYYY"
                                    )}
                                  </Typography>
                                </Grid>

                                <Typography
                                  sx={{
                                    fontSize: "12px",

                                    color: " #757575",
                                    fontWeight: "500",
                                  }}
                                >
                                  {comment.user}
                                </Typography>
                                <Typography
                                  sx={{
                                    fontSize: "12px",
                                    color: "#1D1D1D",
                                    fontWeight: "600",
                                  }}
                                >
                                  {comment.comment}
                                </Typography>
                              </Stack>
                            </Box>
                          </Card>
                        </TimelineContent>
                      </TimelineItem>
                    ))}
                  </Timeline>
                )}
                {!Boolean(comments.length) && (
                  <Typography variant="body2">No Comments Found</Typography>
                )}
                <br />
              </Card>
            ) : (
              <>
                <ReusableAttachementAndComments
                  title="BankKey"
                  useMetaData={false}
                  artifactId={bkNumber}
                  artifactName="BankKey"
                />
                <Card sx={{ padding: "1rem 1rem 0rem 1rem" }}>
                  <Grid
                    container
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                    }}
                  >
                    <Typography variant="h6">
                      <strong>Attachments</strong>
                    </Typography>
                  </Grid>
                  {Boolean(attachments.length) && (
                    <ReusableTable
                      width="100%"
                      rows={attachments}
                      columns={attachmentColumns}
                      hideFooter={false}
                      getRowIdValue={"id"}
                      disableSelectionOnClick={true}
                      stopPropagation_Column={"action"}
                    />
                  )}
                  {!Boolean(attachments.length) && (
                    <Typography variant="body2">
                      No Attachments Found
                    </Typography>
                  )}
                  <br />
                  <Typography variant="h6">Comments</Typography>
                  {Boolean(comments.length) && (
                    <Timeline
                      sx={{
                        [`& .${timelineItemClasses.root}:before`]: {
                          flex: 0,
                          padding: 0,
                        },
                      }}
                    >
                      {comments.map((comment) => (
                        <TimelineItem>
                          <TimelineSeparator>
                            <TimelineDot>
                              <CheckCircleOutlineOutlined
                                sx={{ color: "#757575" }}
                              />
                            </TimelineDot>
                            <TimelineConnector />
                          </TimelineSeparator>
                          <TimelineContent sx={{ py: "12px", px: 2 }}>
                            <Card
                              elevation={0}
                              sx={{
                                border: 1,
                                borderColor: "#C4C4C4",
                                borderRadius: "8px",
                                width: "650px",
                              }}
                            >
                              <Box sx={{ padding: "1rem" }}>
                                <Stack spacing={1}>
                                  <Grid
                                    sx={{
                                      display: "flex",
                                      justifyContent: "space-between",
                                    }}
                                  >
                                    <Typography
                                      sx={{
                                        textAlign: "right",
                                        color: " #757575",
                                        fontWeight: "500",
                                        fontSize: "12px",
                                      }}
                                    >
                                      {moment(comment.createdAt).format(
                                        "DD MMM YYYY"
                                      )}
                                    </Typography>
                                  </Grid>

                                  <Typography
                                    sx={{
                                      fontSize: "12px",

                                      color: " #757575",
                                      fontWeight: "500",
                                    }}
                                  >
                                    {comment.user}
                                  </Typography>
                                  <Typography
                                    sx={{
                                      fontSize: "12px",
                                      color: "#1D1D1D",
                                      fontWeight: "600",
                                    }}
                                  >
                                    {comment.comment}
                                  </Typography>
                                </Stack>
                              </Box>
                            </Card>
                          </TimelineContent>
                        </TimelineItem>
                      ))}
                    </Timeline>
                  )}
                  {!Boolean(comments.length) && (
                    <Typography variant="body2">No Comments Found</Typography>
                  )}
                  <br />
                </Card>
              </>
            )}
          </>,
        ];
      }
    });

  console.log("tabcontents", tabContents);
  const handleBankKeySubmitChange = () => {
    setIsLoading(true);
    const hSuccess = (data) => {
      // setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Bank Key Submitted for Approval with ID CBS${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
        navigate("/masterDataCockpit/bankKey");
      } else {
        setMessageDialogTitle("Approve");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Bank Key");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_BankKey}/alter/changeBankKeyApprovalSubmit`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleBankKeySubmitCreate = () => {
    setIsLoading(true);
    const hSuccess = (data) => {
      // setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Bank Key Submitted for Approval with ID NBS${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
        navigate("/masterDataCockpit/bankKey");
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Bank Key");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_BankKey}/alter/bankKeyApprovalSubmit`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleBankKeyReviewChange = () => {
    // setIsLoading(true);
    const hSuccess = (data) => {
      // setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Bank Key Submitted For Review with ID CBS${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsEditMode(false);
        setIsDisplayMode(true);
        setIsLoading(false);

        const secondApiPayload = {
          artifactId: bkNumber,
          createdBy: userData?.emailId,
          artifactType: "BankKey",
          requestId: `CBS${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
        navigate("/masterDataCockpit/bankKey");
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Bank Key");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
        setOpenRemarkDialog(false);
        //setDupliDialogo chiranjit
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_BankKey}/alter/changeBankKeySubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleBankKeyReviewCreate = () => {
    setIsLoading(true);
    const hSuccess = (data) => {
      // setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Bank Key Submitted for Review with ID NBS${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);

        const secondApiPayload = {
          artifactId: bkNumber,
          createdBy: userData?.emailId,
          artifactType: "BankKey",
          requestId: `NBS${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
        navigate("/masterDataCockpit/bankKey");
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Saving the Data");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
        setTestrunStatus(true);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_BankKey}/alter/bankKeySubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleBankKeySaveAsDraftChange = () => {
    // setIsLoading();
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Bank Key Saved As Draft with ID CBS${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
        const secondApiPayload = {
          artifactId: bkNumber,
          createdBy: userData?.emailId,
          artifactType: "BankKey",
          requestId: `CBS${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
        navigate("/masterDataCockpit/bankKey");
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Saving Bank Key");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_BankKey}/alter/changeBankKeyAsDraft`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleBankKeySaveAsDraft = () => {
    // setIsLoading();
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Bank Key Saved As Draft with ID NBS${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
        const secondApiPayload = {
          artifactId: bkNumber,
          createdBy: userData?.emailId,
          artifactType: "BankKey",
          requestId: `NBS${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
        navigate("/masterDataCockpit/bankKey");
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Saving Bank Key");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_BankKey}/alter/bankKeyAsDraft`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleBankKeyCorrectionChange = () => {
    setIsLoading(true);
    const hSuccess = (data) => {
      // setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Bank Key submitted for Correction with ID NBS${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
        const secondApiPayload = {
          artifactId: bkNumber,
          createdBy: userData?.emailId,
          artifactType: "BankKey",
          requestId: `NBS${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting for Correction");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_BankKey}/alter/changeBankKeySendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCostCenterCorrectionCreate = () => {
    setIsLoading(true);
    const hSuccess = (data) => {
      // setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Bank Key submitted for Correction with ID NBS${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
        const secondApiPayload = {
          artifactId: bkNumber,
          createdBy: userData?.emailId,
          artifactType: "BankKey",
          requestId: `NBS${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting for Correction");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_BankKey}/alter/bankKeySendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleBankKeyApproveChange = () => {
    setIsLoading(true);
    const hSuccess = (data) => {
      // setIsLoading();
      if (data.statusCode === 201) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(`${data.message}`);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
        navigate("/masterDataCockpit/bankKey");
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Approving Bank Key");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_BankKey}/alter/changeBankKeyApproved`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleBankKeyApproveCreate = () => {
    setIsLoading(true);
    const hSuccess = (data) => {
      // setIsLoading();
      if (data.statusCode === 201) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(`${data.message}`);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
        navigate("/masterDataCockpit/bankKey");
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Approving the Bank Key");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_BankKey}/alter/createBankKeyApproved`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCostCenterRereview = () => {
    setIsLoading(true);
    console.log("apicallllllllll");
    const hSuccess = (data) => {
      setIsLoading(false);
      setMessageDialogMessage(
        `Create id generated for Data Owners CBS${data.body}`
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_BankKey}/alter/bankKeySendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleOpenCorrectionDialog = () => {
    setTestrunStatus(false);
    setOpenCorrectionDialog(true);
  };
  const handleCorrectionDialogClose = () => {
    setRemarks("");
    setTestrunStatus(true);
    setOpenCorrectionDialog(false);
  };
  const handleOpenRemarkDialog = () => {
    setTestrunStatus(false);
    setOpenRemarkDialog(true);
  };
  const handleRemarksDialogClose = () => {
    setRemarksValidationError(false);
    setTestrunStatus(true);
    setOpenRemarkDialog(false);
  };
  const handleWarningDialogClose = () => {
    setOpenMessageDialog(false);
  };
  //
  const onBankKeySubmitRemarks = () => {
    //alert("coming")
    if (
      userData?.role === "Finance" &&
      (bankKeyRowData?.requestType === "Create" ||
        taskRowDetails?.processDesc === "Create") &&
      isEditMode
    ) {
      if (remarks.length <= 0){
        setRemarksValidationError(true)
        //setOpenRemarkDialog(false);
      }else{
        setRemarksValidationError(false)
        onBankKeyReviewCreate();
      }      
    } else if (
      userData?.role === "MDM Steward" &&
      (bankKeyRowData?.requestType === "Create" ||
        taskRowDetails?.processDesc === "Create") &&
      !isEditMode
    ) {
      onBankKeySubmitCreate();
    } else if (
      userData?.role === "Approver" &&
      (bankKeyRowData?.requestType === "Create" ||
        taskRowDetails?.processDesc === "Create") &&
      !isEditMode
    ) {
      onBankKeyApproveCreate();
    } else if (
      userData?.role === "Finance" &&
      !bankKeyRowData?.requestType && //for change from master table
      isEditMode
    ) {
      if (remarks.length <= 0){
        setRemarksValidationError(true)
        //setOpenRemarkDialog(false);
      }else{
        setRemarksValidationError(false)
        onBankKeyReviewChange();
      }
      
    } else if (
      userData?.role === "Finance" &&
      (bankKeyRowData?.requestType === "Change" ||
        taskRowDetails?.processDesc === "Change") &&
      isEditMode
    ) {
      onBankKeyReviewChange();
    } else if (
      userData?.role === "MDM Steward" &&
      (bankKeyRowData?.requestType === "Change" ||
        taskRowDetails?.processDesc === "Change") &&
      !isEditMode
    ) {
      onBankKeySubmitChange();
    } else if (
      userData?.role === "Approver" &&
      (bankKeyRowData?.requestType === "Change" ||
        taskRowDetails?.processDesc === "Change") &&
      !isEditMode
    ) {
      onBankKeyApproveChange();
    }
  };

  const handleRemarks = (e, value) => {
    //setRemarks(e.target.value);
    const newValue = e.target.value;
    if (newValue.length > 0 && newValue[0] === " ") {
      setRemarks(newValue.trimStart());
    } else {
      //let costCenterValue = e.target.value;
      let remarksUpperCase = newValue.toUpperCase();
      setRemarks(remarksUpperCase);
    }
  };
  const handleSnackBarOpenValidation = () => {
    setOpenSnackbarValidation(true);
  };
  const handleSnackBarCloseValidation = () => {
    setOpenSnackbarValidation(false);
  };
  const handleClosemodalData = (data) => {
    setisChangeLogopen(data);
  };
  const openChangeLog = () => {
    setisChangeLogopen(true);
  };
  console.log(bankKeyRowData?.bankKey, "bankKeyRowData?.bankKey");
  return (
    <>
      {isLoading === true ? (
        <LoadingComponent />
      ) : (
        <div style={{ backgroundColor: "#FAFCFF" }}>
          <ReusableDialog
            apiError={displayApiError}
            dialogState={openMessageDialog}
            openReusableDialog={handleMessageDialogClickOpen}
            closeReusableDialog={handleMessageDialogClose}
            dialogTitle={messageDialogTitle}
            dialogMessage={messageDialogMessage}
            handleDialogConfirm={handleMessageDialogClose}
            dialogOkText={"OK"}
            handleExtraButton={handleMessageDialogNavigate}
            dialogSeverity={messageDialogSeverity}
            showCancelButton={true}
            // showExtraButton={handleE}
            handleDialogReject={handleWarningDialogClose}
          />

          {successMsg && (
            <ReusableSnackBar
              openSnackBar={openSnackbar}
              alertMsg={messageDialogMessage}
              handleSnackBarClose={handleSnackBarClose}
            />
          )}
          {formValidationErrorItems.length != 0 && (
            <ReusableSnackBar
              openSnackBar={openSnackbarValidation}
              alertMsg={
                "Please fill the following Field: " +
                formValidationErrorItems.join(", ")
              }
              handleSnackBarClose={handleSnackBarCloseValidation}
            />
          )}

          <Dialog
            open={openCorrectionDialog}
            onClose={handleCorrectionDialogClose}
            hideBackdrop={false}
            elevation={2}
            PaperProps={{
              sx: { boxShadow: "none" },
            }}
          >
            <DialogTitle
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF40",
                display: "flex",
              }}
            >
              <Typography variant="h6">REMARKS</Typography>

              <IconButton
                sx={{ width: "max-content" }}
                onClick={handleCorrectionDialogClose}
                children={<CloseIcon />}
              />
            </DialogTitle>
            <DialogContent sx={{ padding: ".5rem 1rem" }}>
              <Stack>
                <Box sx={{ minWidth: 400 }}>
                  <FormControl sx={{ height: "auto" }} fullWidth>
                    <TextField
                      sx={{ backgroundColor: "#F5F5F5" }}
                      onChange={handleRemarks}
                      multiline
                      value={remarks}
                      placeholder={"ENTER REMARKS"}
                      inputProps={{maxLength: 254}}
                    ></TextField>
                  </FormControl>
                  {remarksValidationError && (
                      <Grid>
                        <Typography style={{ color: "red" }}>
                            Please Enter Remarks
                        </Typography>
                      </Grid>
                  )}
                </Box>
              </Stack>
            </DialogContent>
            <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
              <Button
                sx={{ width: "max-content", textTransform: "capitalize" }}
                onClick={handleCorrectionDialogClose}
              >
                Cancel
              </Button>
              <Button
                className="button_primary--normal"
                type="save"
                onClick={onBankKeyCorrection}
                variant="contained"
              >
                Submit
              </Button>
            </DialogActions>
          </Dialog>

          <Backdrop
            sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
            open={blurLoading}
            // onClick={handleClose}
          >
            <CircularProgress color="inherit" />
          </Backdrop>

          <Grid container sx={outermostContainer_Information}>
            <Grid item md={12} style={{ padding: "16px", display: "flex" }}>
              <Grid md={9} sx={{ display: "flex" }}>
                <Grid>
                  <IconButton
                    // onClick={handleBacktoRO}
                    color="primary"
                    aria-label="upload picture"
                    component="label"
                    sx={iconButton_SpacingSmall}
                  >
                    <ArrowCircleLeftOutlinedIcon
                      sx={{
                        fontSize: "25px",
                        color: "#000000",
                      }}
                      onClick={() => {
                        navigate("/masterDataCockpit/bankKey");
                      }}
                    />
                  </IconButton>
                </Grid>
                <Grid>
                  {isEditMode ? (
                    <Grid item md={12}>
                      <Typography variant="h3">
                        <strong>Change Bank Key: </strong>
                      </Typography>

                      <Typography variant="body2" color="#777">
                        This view edits the details of the Bank Key
                      </Typography>
                    </Grid>
                  ) : (
                    ""
                  )}
                  {/* {isExtendMode ? (
                <Grid item md={9}>
                  <Typography variant="h3">
                    <strong>Extend Material: {materialData.reqId} </strong>
                  </Typography>

                  <Typography variant="body2" color="#777">
                    This view extends the details of the materials
                  </Typography>
                </Grid>
              ) : (
                ""
              )} */}
                  {isDisplayMode ? (
                    <Grid item md={12}>
                      <Typography variant="h3">
                        <strong>Display Bank Key </strong>
                      </Typography>

                      <Typography variant="body2" color="#777">
                        This view displays the details of the Bank Key
                      </Typography>
                    </Grid>
                  ) : (
                    ""
                  )}
                </Grid>
              </Grid>
              <Grid
                md={3}
                sx={{ display: "flex", justifyContent: "flex-end" }}
                gap={2}
              >
                {bankKeyRowData?.requestId || taskRowDetails?.processDesc ? (
                  <Grid>
                    <Button
                      variant="outlined"
                      size="small"
                      sx={button_Outlined}
                      onClick={openChangeLog}
                      title="Change Log"
                    >
                      <TrackChangesTwoToneIcon
                        sx={{ padding: "2px" }}
                        fontSize="small"
                      />
                    </Button>
                  </Grid>
                ) : (
                  ""
                )}

                {isChangeLogopen && (
                  <ChangeLog
                    open={true}
                    closeModal={handleClosemodalData}
                    requestId={
                      bankKeyRowData?.requestId
                        ? bankKeyRowData?.requestId
                        : taskRowDetails?.subject
                    }
                    requestType={
                      bankKeyRowData?.requestType
                        ? bankKeyRowData?.requestType
                        : taskData?.body?.processDesc
                    }
                    pageName={"bankKey"}
                    controllingArea={
                      bankKeyRowData?.bankCtryReg
                        ? bankKeyRowData?.bankCtryReg
                        : iDs?.BankCtry
                    }
                    centerName={
                      bankKeyRowData?.bankKey
                        ? bankKeyRowData?.bankKey
                        : iDs?.BankKey
                    }
                  />
                )}
                {checkIwaAccess(iwaAccessData, "Bank Key", "ChangeBK") &&
                  (userData?.role === "Super User" &&
                  bankKeyRowData?.requestType && taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
                  isDisplayMode ? (
                    <Grid gap={1} sx={{ display: "flex" }}>
                      <Grid
                        gap={1}
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                        }}
                      >
                        <>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={onEdit}
                            >
                              Fill Details
                              <EditOutlinedIcon
                                sx={{ padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                        </>
                      </Grid>
                    </Grid>
                  ) : userData?.role === "Finance" &&
                    bankKeyRowData?.requestType &&
                    isDisplayMode ? (
                    <Grid gap={1} sx={{ display: "flex" }}>
                      <Grid
                        gap={1}
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                        }}
                      >
                        <>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={onEdit}
                            >
                              Fill Details
                              <EditOutlinedIcon
                                sx={{ padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                        </>
                      </Grid>
                    </Grid>
                  ) : userData?.role === "Super User" &&
                    !bankKeyRowData?.requestType &&
                    isDisplayMode ? (
                    <Grid gap={1} sx={{ display: "flex" }}>
                      <Grid
                        gap={1}
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                        }}
                      >
                        <>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={onEdit}
                            >
                              Change
                              <EditOutlinedIcon
                                sx={{ padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                        </>
                      </Grid>
                    </Grid>
                  ) : userData?.role === "Finance" &&
                    !bankKeyRowData?.requestType &&
                    isDisplayMode ? (
                    <Grid gap={1} sx={{ display: "flex" }}>
                      <Grid
                        gap={1}
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                        }}
                      >
                        <>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={onEdit}
                            >
                              Change
                              <EditOutlinedIcon
                                sx={{ padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                        </>
                      </Grid>
                    </Grid>
                  ) : (
                    ""
                  ))}
              </Grid>
            </Grid>
            <Grid
              container
              display="flex"
              flexDirection="row"
              flexWrap="nowrap"
            >
              <Box width="70%" sx={{ marginLeft: "40px" }}>
                <Grid item sx={{ paddingTop: "2px !important" }}>
                  <Stack flexDirection="row">
                    <div style={{ width: "20%" }}>
                      <Typography variant="body2" color="#777">
                        Bank Country
                      </Typography>
                    </div>
                    <Typography
                      variant="body2"
                      fontWeight="bold"
                      justifyContent="flex-start"
                    >
                      :{" "}
                      {bankKeyRowData?.BankCtry
                        ? bankKeyRowData?.BankCtry
                        : iDs?.BankCtry
                        ? iDs?.BankCtry
                        : ""}
                    </Typography>
                  </Stack>
                </Grid>

                <Grid item sx={{ paddingTop: "2px !important" }}>
                  <Stack flexDirection="row">
                    <div style={{ width: "20%" }}>
                      <Typography variant="body2" color="#777">
                        Bank Key
                      </Typography>
                    </div>
                    <Typography variant="body2" fontWeight="bold">
                      :{" "}
                      {bankKeyRowData?.bankKey
                        ? bankKeyRowData?.bankKey
                        : iDs?.BankKey
                        ? iDs?.BankKey
                        : ""}
                    </Typography>
                  </Stack>
                </Grid>
                {/* <Grid item sx={{ paddingTop: "2px !important" }}>
              <Stack flexDirection="row">
                <div style={{ width: "20%" }}>
                  <Typography variant="body2" color="#777">
                    Bank Key Name
                  </Typography>
                </div>
                <Typography variant="body2" fontWeight="bold">
                  :{" "}
                  {bankKeyRowData?.costCenterName
                    ? bankKeyRowData?.costCenterName
                    : costCenterDetails[0]?.data?.Names[0]?.value
                    ? costCenterDetails[0]?.data?.Names[0]?.value
                    : ""}
                </Typography>
                <Typography variant="body2" fontWeight="bold"></Typography>
              </Stack>
            </Grid> */}
              </Box>
              {/* <Box width="70%" sx={{ marginLeft: "40px" }}>
            <Grid item sx={{ paddingTop: "2px !important" }}>
              <Stack flexDirection="row">
                <div style={{ width: "15%" }}>
                  <Typography variant="body2" color="#777">
                    Valid From
                  </Typography>
                </div>
                <Typography variant="body2" fontWeight="bold">
                  : {moment(iDs?.validFrom).format(appSettings?.dateFormat)}
                </Typography>
              </Stack>
            </Grid>
            <Grid item sx={{ paddingTop: "2px !important" }}>
              <Stack flexDirection="row">
                <div style={{ width: "15%" }}>
                  <Typography variant="body2" color="#777">
                    Valid To
                  </Typography>
                </div>
                <Typography variant="body2" fontWeight="bold">
                  : {moment(iDs?.validTo).format(appSettings?.dateFormat)}
                </Typography>
                <Typography variant="body2" fontWeight="bold"></Typography>
              </Stack>
            </Grid>
          </Box> */}
            </Grid>

            {/* <Dialog
          open={correctionPopup}
          onClose={handleClose}
          sx={{ display: "flex", justifyContent: "center" }}
        >
          <Box sx={{ width: "600px !important" }}>
            <DialogTitle>
              <DescriptionIcon
                style={{
                  height: "20px",
                  width: "20px",
                  marginBottom: "-5px",
                }}
              />
              <span>Enter Comments for Correction</span>
            </DialogTitle>
            <DialogContent>
              <Grid container columnSpacing={1}>
                <textarea />
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCommentClose}>Cancel</Button>
              <Button onClick={commentAction}>OK</Button>
            </DialogActions>
          </Box>
        </Dialog> */}

            <Grid container style={{ marginLeft: 25 }}>
              <Stepper
                activeStep={activeStep}
                // onChange={handleC/hange}
                // variant="scrollable"
                sx={{
                  background: "#FFFFFF",
                  borderBottom: "1px solid #BDBDBD",
                  width: "100%",
                  height: "48px",
                }}
                aria-label="mui tabs example"
              >
              {factorsArray.map((factor, index) => (
                  <Step key={factor}>
                    <StepLabel sx={{ fontWeight: "700" }}>{factor}</StepLabel>
                  </Step>
                ))
              }
                
              </Stepper>

              {/* Display the cards of the currently active tab */}
              {tabContents &&
                tabContents[activeStep]?.map((cardContent, index) => (
                  <Box key={index} sx={{ mb: 2, width: "100%" }}>
                    <Typography variant="body2">{cardContent}</Typography>
                  </Box>
                ))}
            </Grid>
          </Grid>

          <Grid
            gap={1}
            sx={{ display: "flex", justifyContent: "space-between" }}
          >
            {checkIwaAccess(iwaAccessData, "Bank Key", "ChangeBK") &&
              (!bankKeyRowData?.requestType && taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" && !isEditMode ? (
                <Paper
                  sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                  elevation={2}
                >
                  <BottomNavigation
                    className="container_BottomNav"
                    showLabels
                    sx={{ display: "flex", justifyContent: "flex-end" }}
                  >
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleBack}
                      disabled={activeStep === 0}
                    >
                      Back
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleNext}
                      disabled={
                        activeStep === factorsArray.length - 1 ? true : false
                      }
                    >
                      Next
                    </Button>
                  </BottomNavigation>
                </Paper>
              ) : (
                ""
              ))}

            {checkIwaAccess(iwaAccessData, "Bank Key", "ChangeBK") &&
              (userData?.role === "Super User" &&
              !bankKeyRowData?.requestType && taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
              isEditMode ? (
                <Paper
                  sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                  elevation={2}
                >
                  <BottomNavigation
                    className="container_BottomNav"
                    showLabels
                    sx={{ display: "flex", justifyContent: "flex-end" }}
                  >
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ button_Outlined, mr: 1 }}
                      onClick={onBankKeySaveAsDraftChange}
                    >
                      Save As Draft
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleBack}
                      disabled={activeStep === 0}
                    >
                      Back
                    </Button>

                    {activeStep === factorsArray.length - 1 ? (
                      <>
                        <Button
                          variant="contained"
                          size="small"
                          sx={{ ...button_Primary, mr: 1 }}
                          onClick={onValidateBankKey}
                          disabled={activeStep === 0}
                        >
                          Validate
                        </Button>
                        <Button
                          variant="contained"
                          size="small"
                          sx={{ ...button_Primary, mr: 1 }}
                          onClick={handleCreateDialog}
                          disabled={submitForReviewDisabled}
                        >
                          Submit For Review
                        </Button>
                      </>
                    ) : (
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Primary, mr: 1 }}
                        onClick={handleNext}
                        disabled={
                          activeStep === factorsArray.length - 1 ? true : false
                        }
                      >
                        Next
                      </Button>
                    )}
                  </BottomNavigation>
                </Paper>
              ) : userData?.role === "Finance" &&
                !bankKeyRowData?.requestType && taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&//for change from master table
                isEditMode ? (
                <Paper
                  sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                  elevation={2}
                >
                  <BottomNavigation
                    className="container_BottomNav"
                    showLabels
                    sx={{ display: "flex", justifyContent: "flex-end" }}
                  >
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ button_Outlined, mr: 1 }}
                      onClick={onBankKeySaveAsDraftChange}
                    >
                      Save As Draft
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleBack}
                      disabled={activeStep === 0}
                    >
                      Back
                    </Button>
                    {activeStep === factorsArray.length - 1 ? (
                      <>
                        <Button
                          variant="contained"
                          size="small"
                          sx={{ ...button_Primary, mr: 1 }}
                          onClick={onValidateBankKey}
                        >
                          Validate
                        </Button>
                        <Button
                          variant="contained"
                          size="small"
                          sx={{ ...button_Primary, mr: 1 }}
                          onClick={handleOpenRemarkDialog}
                          disabled={submitForReviewDisabled}
                        >
                          Submit For Review
                        </Button>
                      </>
                    ) : (
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Primary, mr: 1 }}
                        onClick={handleNext}
                        disabled={
                          activeStep === factorsArray.length - 1 ? true : false
                        }
                      >
                        Next
                      </Button>
                    )}
                  </BottomNavigation>
                </Paper>
              ) : (
                ""
              ))}

            {checkIwaAccess(iwaAccessData, "Bank Key", "ChangeBK") &&
              (userData?.role === "Super User" &&
              bankKeyRowData?.requestType === "Create" &&
              !isEditMode ? (
                <Paper
                  sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                  elevation={2}
                >
                  <BottomNavigation
                    className="container_BottomNav"
                    showLabels
                    sx={{ display: "flex", justifyContent: "flex-end" }}
                  >
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={onValidateBankKey}
                    >
                      Validate
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ button_Outlined, mr: 1 }}
                      onClick={onBankKeyApproveCreate}
                      disabled={testrunStatus}
                    >
                      Approve
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleCreateDialog}
                    >
                      Submit For Approval
                    </Button>
                    {/* <Button
                  variant="outlined"
                  size="small"
                  sx={button_Outlined}
                  onClick={onCostCenterCorrectionCreate}
                >
                  Correction
                  <EditOutlinedIcon
                    sx={{ marginLeft: "5px", padding: "2px" }}
                    fontSize="small"
                  />
                </Button> */}
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleBack}
                      disabled={activeStep === 0}
                    >
                      Back
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleNext}
                      disabled={
                        activeStep === factorsArray.length - 1 ? true : false
                      }
                    >
                      Next
                    </Button>
                  </BottomNavigation>
                </Paper>
              ) : userData?.role === "Super User" &&
                bankKeyRowData?.requestType === "Change" &&
                !isEditMode ? (
                <Paper
                  sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                  elevation={2}
                >
                  <BottomNavigation
                    className="container_BottomNav"
                    showLabels
                    sx={{ display: "flex", justifyContent: "flex-end" }}
                  >
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={onValidateBankKey}
                    >
                      Validate
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ button_Outlined, mr: 1 }}
                      onClick={onBankKeyApproveChange}
                      disabled={testrunStatus}
                    >
                      Approve
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleCreateDialog}
                    >
                      Submit For Approval
                    </Button>
                    {/* <Button
                  variant="outlined"
                  size="small"
                  sx={button_Outlined}
                  onClick={onBankKeyCorrectionChange}
                >
                  Correction
                  <EditOutlinedIcon
                    sx={{ marginLeft: "5px", padding: "2px" }}
                    fontSize="small"
                  />
                </Button> */}
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleBack}
                      disabled={activeStep === 0}
                    >
                      Back
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleNext}
                      disabled={
                        activeStep === factorsArray.length - 1 ? true : false
                      }
                    >
                      Next
                    </Button>
                  </BottomNavigation>
                </Paper>
              ) : userData?.role === "MDM Steward" &&
                (bankKeyRowData?.requestType === "Create" ||
                  taskRowDetails?.processDesc === "Create") && taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
                !isEditMode ? (
                <Paper
                  sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                  elevation={2}
                >
                  <BottomNavigation
                    className="container_BottomNav"
                    showLabels
                    sx={{ display: "flex", justifyContent: "flex-end" }}
                  >
                    <Button
                      variant="outlined"
                      size="small"
                      sx={{ button_Outlined, mr: 1 }}
                      onClick={handleOpenCorrectionDialog}
                    >
                      Correction
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleOpenRemarkDialog}
                    >
                      Submit For Approval
                    </Button>
                    {/* <Button
                  variant="outlined"
                  size="small"
                  sx={button_Outlined}
                  onClick={onCostCenterCorrectionCreate}
                >
                  Correction
                  <EditOutlinedIcon
                    sx={{ marginLeft: "5px", padding: "2px" }}
                    fontSize="small"
                  />
                </Button> */}
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleBack}
                      disabled={activeStep === 0}
                    >
                      Back
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleNext}
                      disabled={
                        activeStep === factorsArray.length - 1 ? true : false
                      }
                    >
                      Next
                    </Button>
                  </BottomNavigation>
                </Paper>
              ) : userData?.role === "Finance" &&
                (bankKeyRowData?.requestType === "Create" ||
                  taskRowDetails?.processDesc === "Create") && taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
                !isEditMode ? (
                <Paper
                  sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                  elevation={2}
                >
                  <BottomNavigation
                    className="container_BottomNav"
                    showLabels
                    sx={{ display: "flex", justifyContent: "flex-end" }}
                  >
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleBack}
                      disabled={activeStep === 0}
                    >
                      Back
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleNext}
                      disabled={
                        activeStep === factorsArray.length - 1 ? true : false
                      }
                    >
                      Next
                    </Button>
                  </BottomNavigation>
                </Paper>
              ) : userData?.role === "Finance" &&
                (bankKeyRowData?.requestType === "Change" ||
                  taskRowDetails?.processDesc === "Change") && taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
                !isEditMode ? (
                <Paper
                  sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                  elevation={2}
                >
                  <BottomNavigation
                    className="container_BottomNav"
                    showLabels
                    sx={{ display: "flex", justifyContent: "flex-end" }}
                  >
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleBack}
                      disabled={activeStep === 0}
                    >
                      Back
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleNext}
                      disabled={
                        activeStep === factorsArray.length - 1 ? true : false
                      }
                    >
                      Next
                    </Button>
                  </BottomNavigation>
                </Paper>
              ) : userData?.role === "MDM Steward" &&
                (bankKeyRowData?.requestType === "Change" ||
                  taskRowDetails?.processDesc === "Change") && taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
                !isEditMode ? (
                <Paper
                  sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                  elevation={2}
                >
                  <BottomNavigation
                    className="container_BottomNav"
                    showLabels
                    sx={{ display: "flex", justifyContent: "flex-end" }}
                  >
                    <Button
                      variant="outlined"
                      size="small"
                      sx={{ button_Outlined, mr: 1 }}
                      onClick={handleOpenCorrectionDialog}
                    >
                      Correction
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleOpenRemarkDialog}
                    >
                      Submit For Approval
                    </Button>
                    {/* <Button
                  variant="outlined"
                  size="small"
                  sx={button_Outlined}
                  onClick={onBankKeyCorrectionChange}
                >
                  Correction
                  <EditOutlinedIcon
                    sx={{ marginLeft: "5px", padding: "2px" }}
                    fontSize="small"
                  />
                </Button> */}
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleBack}
                      disabled={activeStep === 0}
                    >
                      Back
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleNext}
                      disabled={
                        activeStep === factorsArray.length - 1 ? true : false
                      }
                    >
                      Next
                    </Button>
                  </BottomNavigation>
                </Paper>
              ) : userData?.role === "Approver" &&
                (bankKeyRowData?.requestType === "Create" ||
                  taskRowDetails?.processDesc === "Create") && taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
                !isEditMode ? (
                <Paper
                  sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                  elevation={2}
                >
                  <BottomNavigation
                    className="container_BottomNav"
                    showLabels
                    sx={{ display: "flex", justifyContent: "flex-end" }}
                  >
                    <Button
                      variant="outlined"
                      size="small"
                      sx={{ button_Outlined, mr: 1 }}
                      onClick={handleOpenCorrectionDialog}
                    >
                      Correction
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={onValidateBankKey}
                    >
                      Validate
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ button_Outlined, mr: 1 }}
                      onClick={handleOpenRemarkDialog}
                      disabled={testrunStatus}
                    >
                      Approve
                    </Button>
                    {/* <Button
                  variant="outlined"
                  size="small"
                  sx={button_Outlined}
                  onClick={onCostCenterCorrectionCreate}
                >
                  Correction
                  <EditOutlinedIcon
                    sx={{ marginLeft: "5px", padding: "2px" }}
                    fontSize="small"
                  />
                </Button> */}
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleBack}
                      disabled={activeStep === 0}
                    >
                      Back
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleNext}
                      disabled={
                        activeStep === factorsArray.length - 1 ? true : false
                      }
                    >
                      Next
                    </Button>
                  </BottomNavigation>
                </Paper>
              ) : userData?.role === "Approver" &&
                (bankKeyRowData?.requestType === "Change" ||
                  taskRowDetails?.processDesc === "Change") && taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
                !isEditMode ? (
                <Paper
                  sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                  elevation={2}
                >
                  <BottomNavigation
                    className="container_BottomNav"
                    showLabels
                    sx={{ display: "flex", justifyContent: "flex-end" }}
                  >
                    <Button
                      variant="outlined"
                      size="small"
                      sx={{ button_Outlined, mr: 1 }}
                      onClick={handleOpenCorrectionDialog}
                    >
                      Correction
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={onValidateBankKey}
                    >
                      Validate
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ button_Outlined, mr: 1 }}
                      onClick={handleOpenRemarkDialog}
                      disabled={testrunStatus}
                    >
                      Approve
                    </Button>
                    {/* <Button
                  variant="outlined"
                  size="small"
                  sx={button_Outlined}
                  onClick={onBankKeyCorrectionChange}
                >
                  Correction
                  <EditOutlinedIcon
                    sx={{ marginLeft: "5px", padding: "2px" }}
                    fontSize="small"
                  />
                </Button> */}
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleBack}
                      disabled={activeStep === 0}
                    >
                      Back
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleNext}
                      disabled={
                        activeStep === factorsArray.length - 1 ? true : false
                      }
                    >
                      Next
                    </Button>
                  </BottomNavigation>
                </Paper>
              ) : userData?.role === "Super User" &&
                bankKeyRowData?.requestType === "Create" &&
                isEditMode ? (
                <Paper
                  sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                  elevation={2}
                >
                  <BottomNavigation
                    className="container_BottomNav"
                    showLabels
                    sx={{ display: "flex", justifyContent: "flex-end" }}
                  >
                    {/* <Button
                  variant="outlined"
                  size="small"
                  sx={button_Outlined}
                  onClick={onCostCenterSaveAsDraft}
                >
                  Save As Draft
                </Button> */}
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleBack}
                      disabled={activeStep === 0}
                    >
                      Back
                    </Button>
                    {activeStep === factorsArray.length - 1 ? (
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Primary, mr: 1 }}
                        onClick={onBankKeyReviewCreate}
                      >
                        Submit For Review
                      </Button>
                    ) : (
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Primary, mr: 1 }}
                        onClick={handleNext}
                        disabled={
                          activeStep === factorsArray.length - 1 ? true : false
                        }
                      >
                        Next
                      </Button>
                    )}
                  </BottomNavigation>
                </Paper>
              ) : userData?.role === "Super User" &&
                bankKeyRowData?.requestType === "Change" &&
                isEditMode ? (
                <Paper
                  sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                  elevation={2}
                >
                  <BottomNavigation
                    className="container_BottomNav"
                    showLabels
                    sx={{ display: "flex", justifyContent: "flex-end" }}
                  >
                    {/* <Button
                  variant="outlined"
                  size="small"
                  sx={button_Outlined}
                  onClick={onCostCenterSaveAsDraft}
                >
                  Save As Draft
                </Button> */}
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleBack}
                      disabled={activeStep === 0}
                    >
                      Back
                    </Button>
                    {activeStep === factorsArray.length - 1 ? (
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Primary, mr: 1 }}
                        onClick={onBankKeyReviewChange}
                      >
                        Submit For Review
                      </Button>
                    ) : (
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Primary, mr: 1 }}
                        onClick={handleNext}
                        disabled={
                          activeStep === factorsArray.length - 1 ? true : false
                        }
                      >
                        Next
                      </Button>
                    )}
                  </BottomNavigation>
                </Paper>
              ) : userData?.role === "Finance" &&
                (bankKeyRowData?.requestType === "Create" ||
                  taskRowDetails?.processDesc === "Create") && taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
                isEditMode ? (
                <Paper
                  sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                  elevation={2}
                >
                  <BottomNavigation
                    className="container_BottomNav"
                    showLabels
                    sx={{ display: "flex", justifyContent: "flex-end" }}
                  >
                    <Button
                      variant="outlined"
                      size="small"
                      sx={{ button_Outlined, mr: 1 }}
                      onClick={onBankKeySaveAsDraft}
                    >
                      Save As Draft
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleBack}
                      disabled={activeStep === 0}
                    >
                      Back
                    </Button>
                    {activeStep === factorsArray.length - 1 ? (
                      <>
                        <Button
                          variant="contained"
                          size="small"
                          sx={{ ...button_Primary, mr: 1 }}
                          onClick={onValidateBankKey}
                        >
                          Validate
                        </Button>
                        <Button
                          variant="contained"
                          size="small"
                          sx={{ ...button_Primary, mr: 1 }}
                          onClick={handleOpenRemarkDialog}
                          disabled={submitForReviewDisabled}
                        >
                          Submit For Review
                        </Button>
                      </>
                    ) : (
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Primary, mr: 1 }}
                        onClick={handleNext}
                        disabled={
                          activeStep === factorsArray.length - 1 ? true : false
                        }
                      >
                        Next
                      </Button>
                    )}
                  </BottomNavigation>
                </Paper>
              ) : userData?.role === "Finance" &&
                (bankKeyRowData?.requestType === "Change" ||
                  taskRowDetails?.processDesc === "Change") && taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
                isEditMode ? (
                <Paper
                  sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                  elevation={2}
                >
                  <BottomNavigation
                    className="container_BottomNav"
                    showLabels
                    sx={{ display: "flex", justifyContent: "flex-end" }}
                  >
                    <Button
                      variant="outlined"
                      size="small"
                      sx={{ button_Outlined, mr: 1 }}
                      onClick={onBankKeySaveAsDraftChange}
                    >
                      Save As Draft
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleBack}
                      disabled={activeStep === 0}
                    >
                      Back
                    </Button>
                    {activeStep === factorsArray.length - 1 ? (
                      <>
                        <Button
                          variant="contained"
                          size="small"
                          sx={{ ...button_Primary, mr: 1 }}
                          onClick={onValidateBankKey}
                        >
                          Validate
                        </Button>
                        <Button
                          variant="contained"
                          size="small"
                          sx={{ ...button_Primary, mr: 1 }}
                          onClick={handleOpenRemarkDialog}
                          disabled={submitForReviewDisabled}
                        >
                          Submit For Review
                        </Button>
                      </>
                    ) : (
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Primary, mr: 1 }}
                        onClick={handleNext}
                        disabled={
                          activeStep === factorsArray.length - 1 ? true : false
                        }
                      >
                        Next
                      </Button>
                    )}
                  </BottomNavigation>
                </Paper>
              ) : (
                ""
              ))}
          </Grid>
          <Dialog
            hideBackdrop={false}
            elevation={2}
            PaperProps={{
              sx: { boxShadow: "none" },
            }}
            open={openCreateDialog}
            onClose={handleCreateDialogClose}
          >
            {/* <Grid
          container
          sx={{ display: "flex", justifyContent: "space-between" }}
        > */}
            {/* <Grid item> */}
            <DialogTitle
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF40",
                // borderBottom: "1px solid grey",
                display: "flex",
              }}
            >
              <Typography variant="h6">REMARKS</Typography>

              <IconButton
                sx={{ width: "max-content" }}
                onClick={handleCreateDialogClose}
                children={<CloseIcon />}
              />
            </DialogTitle>
            {/* </Grid> */}
            {/* </Grid> */}
            <DialogContent sx={{ padding: ".5rem 1rem" }}>
              <Stack>
                <Box sx={{ minWidth: 400 }}>
                  <FormControl sx={{ height: "auto" }} fullWidth>
                    <TextField
                      sx={{ backgroundColor: "#F5F5F5" }}
                      value={remarks}
                      onChange={handleRemarks}
                      multiline
                      placeholder={"ENTER REMARKS"}
                      inputProps={{maxLength: 254}}
                    ></TextField>
                  </FormControl>
                  {remarksValidationError && (
                      <Grid>
                        <Typography style={{ color: "red" }}>
                            Please Enter Remarks
                        </Typography>
                      </Grid>
                  )}
                </Box>
              </Stack>
              {/* <TextField
            autoFocus
            margin="dense"
            id="name"
            label="Enter Remarks for Correction"
            type="text"
            fullWidth
            variant="standard"
            onChange={handleRemarks}
          /> */}
            </DialogContent>
            <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
              <Button
                sx={{ width: "max-content", textTransform: "capitalize" }}
                onClick={handleCreateDialogClose}
              >
                Cancel
              </Button>
              <Button
                className="button_primary--normal"
                type="save"
                onClick={onSubmitForApprovalButtonClick}
                variant="contained"
              >
                Submit
              </Button>
            </DialogActions>
          </Dialog>

          <Dialog
            hideBackdrop={false}
            elevation={2}
            PaperProps={{
              sx: { boxShadow: "none" },
            }}
            open={openRemarkDialog}
            onClose={handleRemarksDialogClose}
          >
            {/* <Grid
          container
          sx={{ display: "flex", justifyContent: "space-between" }}
        > */}
            {/* <Grid item> */}
            <DialogTitle
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF40",
                // borderBottom: "1px solid grey",
                display: "flex",
              }}
            >
              <Typography variant="h6">REMARKS</Typography>

              <IconButton
                sx={{ width: "max-content" }}
                onClick={handleRemarksDialogClose}
                children={<CloseIcon />}
              />
            </DialogTitle>
            {/* </Grid> */}
            {/* </Grid> */}
            <DialogContent sx={{ padding: ".5rem 1rem" }}>
              <Stack>
                <Box sx={{ minWidth: 400 }}>
                  <FormControl sx={{ height: "auto" }} fullWidth>
                    <TextField
                      sx={{ backgroundColor: "#F5F5F5" }}
                      value={remarks}
                      onChange={handleRemarks}
                      multiline
                      placeholder={"ENTER REMARKS"}
                      inputProps={{maxLength: 254}}
                    ></TextField>
                  </FormControl>
                  {remarksValidationError && (
                      <Grid>
                        <Typography style={{ color: "red" }}>
                            Please Enter Remarks
                        </Typography>
                      </Grid>
                  )}
                </Box>
              </Stack>
              {/* <TextField
            autoFocus
            margin="dense"
            id="name"
            label="Enter Remarks for Correction"
            type="text"
            fullWidth
            variant="standard"
            onChange={handleRemarks}
          /> */}
            </DialogContent>
            <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
              <Button
                sx={{ width: "max-content", textTransform: "capitalize" }}
                onClick={handleRemarksDialogClose}
              >
                Cancel
              </Button>
              <Button
                className="button_primary--normal"
                type="save"
                onClick={onBankKeySubmitRemarks}
                variant="contained"
              >
                Submit
              </Button>
            </DialogActions>
          </Dialog>

          <Dialog
            open={dupliDialog}
            // onClose={handleDialogClose}
            sx={{
              "&::webkit-scrollbar": {
                width: "1px",
              },
            }}
          >
            <DialogTitle
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF40",
                // borderBottom: "1px solid grey",
                display: "flex",
              }}
            >
              <Typography variant="h6">New Bank Key</Typography>

              {/* <IconButton
                    sx={{ width: "max-content" }}
                    onClick={handleDialogClose}
                    children={<CloseIcon />}
                  /> */}
            </DialogTitle>
            <DialogContent sx={{ padding: ".5rem 1rem" }}>
              <Grid container spacing={1}>
                <Grid
                  item
                  md={6}
                  sx={{
                    width: "100%",
                    marginTop: ".5rem",
                  }}
                >
                  <Typography>
                    Bank Key
                    <span style={{ color: "red" }}>*</span>
                  </Typography>
                  <FormControl
                    fullWidth
                    sx={{ margin: ".5em 0px", minWidth: "250px" }}
                  >
                    <TextField
                      sx={{ fontSize: "12px !important", height: "31px" }}
                      fullWidth
                      size="small"
                      // value={rmSearchForm?.changedBy}
                      // onChange={(e, value) => {
                      //   setNewCostCenterName(e.target.value);
                      // }}
                      placeholder="Enter Bank Key Name"
                      // error={newCostCenterName === "" ? true : false}
                      required={true}
                    />
                  </FormControl>
                </Grid>

                <Grid
                  item
                  md={6}
                  sx={{
                    width: "100%",
                    marginTop: ".5rem",
                  }}
                >
                  <Typography>
                    Valid From
                    <span style={{ color: "red" }}>*</span>
                  </Typography>

                  <FormControl fullWidth sx={{ margin: ".5em 0px" }}>
                    <LocalizationProvider dateAdapter={AdapterDateFns}>
                      {/* <DemoContainer components={["DatePicker"]}> */}
                      <DatePicker
                        slotProps={{ textField: { size: "small" } }}
                        // value={newValidFromDate}
                        // onChange={(value) => setNewValidFromDate(value)}
                      />

                      {/* </DemoContainer> */}
                    </LocalizationProvider>
                  </FormControl>
                </Grid>
                <Grid
                  item
                  md={6}
                  sx={{
                    width: "100%",
                    marginTop: ".5rem",
                  }}
                >
                  <Typography>
                    Valid To
                    <span style={{ color: "red" }}>*</span>
                  </Typography>

                  <FormControl fullWidth sx={{ margin: ".5em 0px" }}>
                    <LocalizationProvider dateAdapter={AdapterDateFns}>
                      {/* <DemoContainer components={["DatePicker"]}> */}
                      <DatePicker
                        slotProps={{ textField: { size: "small" } }}
                        // value={newValidToDate}
                        // onChange={(value) => setNewValidToDate(value)}
                      />
                      {/* </DemoContainer> */}
                    </LocalizationProvider>
                  </FormControl>
                </Grid>
              </Grid>

              {/* {isValidationError && (
                    <Grid>
                      <Typography style={{ color: "red" }}>
                        Please Enter Mandatory Fields
                      </Typography>
                    </Grid>
                  )} */}
            </DialogContent>

            <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
              <Button
                sx={{ width: "max-content", textTransform: "capitalize" }}
                // onClick={handleDialogClose}
              >
                Cancel
              </Button>
              <Button
                className="button_primary--normal"
                type="save"
                // onClick={handleDialogProceed}
                variant="contained"
              >
                Proceed
              </Button>
            </DialogActions>
          </Dialog>
        </div>
      )}
    </>
  );
};

export default DisplayBankKey;
