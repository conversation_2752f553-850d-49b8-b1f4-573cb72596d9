import { UserSummary } from '@cw/usersummary';
import { Stack } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import {APP_END_POINTS} from "@constant/appEndPoints";


const IwaUsersSummary = () => {
  const navigate = useNavigate();
  const isVisible = {
    importUsers: true,
    viewUser: true,
    editUser: true,
    quickAdd: true,
    detailedAdd: true,
  };

  const onUserSummaryActionClick = (action, userId) => {
    switch (action) {
      case 'view':
        if (userId) {
          navigate(APP_END_POINTS.IWA_USER_MANAGEMENT.VIEW_USER);
        }
        break;
      case 'edit':
        if (userId) {
          navigate(APP_END_POINTS.IWA_USER_MANAGEMENT.EDIT_USER);
        }
        break;
      case 'quickadduser':
        navigate(APP_END_POINTS.IWA_USER_MANAGEMENT.QUICK_CREATE_USER);
        break;
      case 'adduser':
        navigate(APP_END_POINTS.IWA_USER_MANAGEMENT.CREATE_USERS);
        break;
      default:
        break;
    }
  };
   const dateTimeConfig = {
        dateFormat: "DD-MMM-YYYY",
        timeFormat: "24hr",
    };

  return (
    <Stack>
      <UserSummary {...isVisible} onUserSummaryActionClick={onUserSummaryActionClick} dateTimeConfig={dateTimeConfig} app={"IWA"}/>
    </Stack>
  );
};

export default IwaUsersSummary;

