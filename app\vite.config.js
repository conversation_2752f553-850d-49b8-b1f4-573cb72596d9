import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";

const createProxy = (prefix, target, rewritePrefix = "/rest") => ({
  target,
  changeOrigin: true,
  rewrite: (path) => path.replace(new RegExp(`^${prefix}`), rewritePrefix),
});

// ✅ Your hardcoded tokens (keep short here for readability)
const IWA_TOKEN = `Bearer ********************************************************************************************************************************************************************************************************************************************************************************.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CZml160klwZEtTHnCkybPChDwU9DdxEW_v4bkl31aTUnsriXYVJfrsUvL94cJxycPbsLunxMfvu6bJGxai8tYUbDayL2exnwUj_qTPEiKNjrjJzPHIInoXT510lmFgWE3v6H5vdzBXbv_99DLbLGTDWzxFApBMCgZGyJBJlZRlp54jUFw606TZwxeHjPQO7pWSEepWtcFQJyFwr0PE5oKxxp5OuOXzdwU0pp0IBdcMn10JjGZ8WsejReR5Lt4Yu-x5KqtN_N7u7rYzpIDTlKod4HirhpgVTgEHgyuMykUvHHFfBiNsJjtx5m-nEnl-mihZI0Ea4T7t9FXF38ZkpXXw`;
const IDM_TOKEN = `Bearer eyJhbGciOiJSUzI1NiIsImprdSI6Imh0dHBzOi8vaW5jdHVyZS1jaGVycnl3b3JrLWRldi5hdXRoZW50aWNhdGlvbi5ldTEwLmhhbmEub25kZW1hbmQuY29tL3Rva2VuX2tleXMiLCJraWQiOiJkZWZhdWx0LWp3dC1rZXktMTY4NjE5NzU3MyIsInR5cCI6IkpXVCIsImppZCI6ICJiWHJ3dlhJYzdnNzVBNUpDK1ozZVdDRGFYblpoblRicjdBdnRiYVhRcjRvPSJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.O_8jkIb2iufo0C6Y8QB6zWtNvIoXmyGoYvHdvVkto3mABRg10OJQlnaNx3CzYkVLKGDqmZaL1h024NBeB4vaRiJwYZnaRYKsHJafEx-fpL0PlaIErPBOShtQqMmRbXGBsogkWJ2C4R8EsrLTOtdnb7Gq4Tim66BLN7bWFd2Ur6aK4ajUKHE30IVe7LGsD8cxOPCFHvIvV7csfYe0GwAtqIyBcYhr-bt_x-XonEPwZhRVVb2_mAQXl0W5fp_Geen2QRtxNNItYwqHwP-Vu3SeIegCMgnBj-jmMjizHSPAWWi31DwUB6wgxbBM5KppLPVA_EvBwzG4KJt654x3h0Jlow`;

export default defineConfig({
  optimizeDeps: {
    include: ["@emotion/react", "@emotion/styled", "@mui/material/Tooltip"],
  },
  plugins: [
    react({
      jsxImportSource: "@emotion/react",
      babel: {
        plugins: ["@emotion/babel-plugin"],
      },
    }),
  ],
  resolve: {
    alias: {
      "@components": path.resolve(__dirname, "src/components"),
      "@constant": path.resolve(__dirname, "src/constant"),
      "@data": path.resolve(__dirname, "src/data"),
      "@helper": path.resolve(__dirname, "src/helper"),
      "@hooks": path.resolve(__dirname, "src/hooks"),
      "@utilityImages": path.resolve(__dirname, "src/utilityImages"),
      "@app": path.resolve(__dirname, "src/app"),
      "@material": path.resolve(__dirname, "src/modules/material"),
      "@slice": path.resolve(__dirname, "src/app"),
      "@costCenter" : path.resolve(__dirname,"src/modules/costCenter"),
      "@profitCenter" : path.resolve(__dirname,"src/modules/profitCenter"),
      "@generalLedger" : path.resolve(__dirname,"src/modules/generalLedger"),
      "@costCenterGroup" : path.resolve(__dirname,"src/modules/costCenterGroup"),
      "@profitCenterGroup" : path.resolve(__dirname,"src/modules/profitCenterGroup"),
      "@costElementGroup" : path.resolve(__dirname,"src/modules/costElementGroup"),
      "@bankKey" : path.resolve(__dirname,"src/modules/bankKey"),
      "@BillOfMaterial" : path.resolve(__dirname,"src/modules/BillOfMaterial"),
      "@InternalOrder" : path.resolve(__dirname,"src/modules/InternalOrder")

    },
  },
  server: {
    host: "0.0.0.0",
    port: 5173,
    open: true,
    proxy: {
      "/IWAApi": {
        target: "https://incture-cherrywork-dev-cw-caf-dev-cw-caf-iwa-services.cfapps.eu10-004.hana.ondemand.com",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/IWAApi/, ""),
        secure: true,
        headers: {
          Authorization: IWA_TOKEN,
        },
      },
       "/cw-caf-idm-services": {
        ...createProxy(
          "/cw-caf-idm-services",
          "https://cw-caf-idm-services.cfapps.eu10-004.hana.ondemand.com/",
          "/rest"
        ),
        headers: {
          Authorization: IDM_TOKEN,
        },
      },
      "/WorkUtilsServices": {
        ...createProxy(
          "/WorkUtilsServices",
          "https://cw-caf-idm-services.cfapps.eu10-004.hana.ondemand.com/"
        ),
        headers: {
          Authorization: IDM_TOKEN,
        },
      },
      "/WorkRulesServices": {
        ...createProxy(
          "/WorkRulesServices",
          "https://cw-caf-idm-services.cfapps.eu10-004.hana.ondemand.com/"
        ),
        headers: {
          Authorization: IDM_TOKEN,
        },
      },
      "/WorkRuleEngineServices": {
        ...createProxy(
          "/WorkRuleEngineServices",
          "https://cw-caf-idm-services.cfapps.eu10-004.hana.ondemand.com/"
        ),
        headers: {
          Authorization: IDM_TOKEN,
        },
      },
    },
  },
});
