import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";

const createProxy = (prefix, target, rewritePrefix = "/rest") => ({
  target,
  changeOrigin: true,
  rewrite: (path) => path.replace(new RegExp(`^${prefix}`), rewritePrefix),
});

// ✅ Your hardcoded tokens (keep short here for readability)
const IWA_TOKEN = `Bearer ********************************************************************************************************************************************************************************************************************************************************************************.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QfG6yz7oOYyIIHhAFSxCVpY3tQ8b7BqFNcr4b_3-tkxJyjkpgUj63SP2Qww89n-MzZsn8aPmUtpDuF-Me2gL4pdF-7EC6_MXBpi3gizFcuUQyNFXMTyPHUJxwvFeaa0Wafesltr4MI8wxrcu2-Z-wbsxmeOKPP93sv-YJek-WoqS5ukWHbNfmaWAYgSZCllisiEcJvN24EsP_hBU-41J0Iv1-99fmnrj8Z1w4x2EfC5C8YJiA4mRprZ1xQYZu3eCxnCtfyPdyi3y71kVvqSTqS8r0_XA4I4Qd1FTJw6u4YpPfAJ_N7fxndH81J1u8nkaJOz0JYJd4kbQM8FPg97SbQ`;
const IDM_TOKEN = `Bearer ********************************************************************************************************************************************************************************************************************************************************************************.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QfG6yz7oOYyIIHhAFSxCVpY3tQ8b7BqFNcr4b_3-tkxJyjkpgUj63SP2Qww89n-MzZsn8aPmUtpDuF-Me2gL4pdF-7EC6_MXBpi3gizFcuUQyNFXMTyPHUJxwvFeaa0Wafesltr4MI8wxrcu2-Z-wbsxmeOKPP93sv-YJek-WoqS5ukWHbNfmaWAYgSZCllisiEcJvN24EsP_hBU-41J0Iv1-99fmnrj8Z1w4x2EfC5C8YJiA4mRprZ1xQYZu3eCxnCtfyPdyi3y71kVvqSTqS8r0_XA4I4Qd1FTJw6u4YpPfAJ_N7fxndH81J1u8nkaJOz0JYJd4kbQM8FPg97SbQ`;

export default defineConfig({
  optimizeDeps: {
    include: ["@emotion/react", "@emotion/styled", "@mui/material/Tooltip"],
  },
  plugins: [
    react({
      jsxImportSource: "@emotion/react",
      babel: {
        plugins: ["@emotion/babel-plugin"],
      },
    }),
  ],
  resolve: {
    alias: {
      "@components": path.resolve(__dirname, "src/components"),
      "@constant": path.resolve(__dirname, "src/constant"),
      "@data": path.resolve(__dirname, "src/data"),
      "@helper": path.resolve(__dirname, "src/helper"),
      "@hooks": path.resolve(__dirname, "src/hooks"),
      "@utilityImages": path.resolve(__dirname, "src/utilityImages"),
      "@app": path.resolve(__dirname, "src/app"),
    },
  },
  server: {
    host: "0.0.0.0",
    port: 5173,
    open: true,
    proxy: {
      "/IWAApi": {
        target: "https://incture-cherrywork-dev-cw-caf-dev-cw-caf-iwa-services.cfapps.eu10-004.hana.ondemand.com",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/IWAApi/, ""),
        secure: false,
        headers: {
          Authorization: IWA_TOKEN,
        },
      },
       "/cw-caf-idm-services": {
        ...createProxy(
          "/cw-caf-idm-services",
          "https://cw-caf-idm-services.cfapps.eu10-004.hana.ondemand.com/",
          "/rest"
        ),
        headers: {
          Authorization: IDM_TOKEN,
        },
      },
      "/WorkUtilsServices": {
        ...createProxy(
          "/WorkUtilsServices",
          "https://cw-caf-idm-services.cfapps.eu10-004.hana.ondemand.com/"
        ),
        headers: {
          Authorization: IDM_TOKEN,
        },
      },
      "/WorkRulesServices": {
        ...createProxy(
          "/WorkRulesServices",
          "https://cw-caf-idm-services.cfapps.eu10-004.hana.ondemand.com/"
        ),
        headers: {
          Authorization: IDM_TOKEN,
        },
      },
      "/WorkRuleEngineServices": {
        ...createProxy(
          "/WorkRuleEngineServices",
          "https://cw-caf-idm-services.cfapps.eu10-004.hana.ondemand.com/"
        ),
        headers: {
          Authorization: IDM_TOKEN,
        },
      },
    },
  },
});
