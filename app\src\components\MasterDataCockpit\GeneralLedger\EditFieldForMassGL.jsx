import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  TextField,
  Grid,
  Autocomplete,
  IconButton,
  FormControlLabel,
  Checkbox,
} from "@mui/material";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import SaveIcon from "@mui/icons-material/Save";
import { DateRangePicker } from "rsuite";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";

import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { setPayload } from "../../../app/editPayloadSlice";
import { setSingleCostCenterPayload } from "../../../app/costCenterTabsSlice";
import { doAjax } from "../../Common/fetchService";
import { destination_CostCenter } from "../../../destinationVariables";
import { setDropDown } from "../../../app/dropDownDataSlice";
import moment from "moment/moment";
import { setMultipleProfitCenterData } from "../../../app/profitCenterTabsSlice";
import { setGLRequiredFields, setMultipleGLData } from "../../../app/generalLedgerTabSlice";

function transformApiData(apiValue, dropDownData) {
  if (Array.isArray(dropDownData)) {
    const matchingOption = dropDownData.find(
      (option) => option.code === apiValue
    );
    return matchingOption || "";
  } else {
    return "";
  }
}
const EditFieldForMassGL = ({
  label,
  value,
  length,
  units,
  onSave,
  fieldGroup,
  isEditMode,
  activeTabIndex,
  visibility,
  isExtendMode,
  pcTabs,
  selectedRowData,
  options = [],
  type,
}) => {
  const [editedValue, setEditedValue] = useState(value);
  const [changeStatus, setChangeStatus] = useState(false);
  const dropDownData = useSelector((state) => state.AllDropDown.dropDown);
  const profitCenterData = useSelector(
    (state) => state.generalLedger.MultipleGLData
  );
  const dispatch = useDispatch();
  const transformedValue = transformApiData(editedValue, dropDownData);
  // const location = useLocation();
  const editField = useSelector((state) => state.edit.payload);
  let activeRow = {};
  let activeIndex = -1;

  for (let index = 0; index < profitCenterData?.length; index++) {
    // console.log("ccdata", costCenterData.tableData[index]);
    if (profitCenterData[index]?.GLAccount === selectedRowData) {
      activeRow = profitCenterData[index];
      activeIndex = index;
      break;
    }
  }
  console.log('selectedrowdata', selectedRowData, profitCenterData)
  let activeTabName = pcTabs[activeTabIndex];
  console.log("activerow", activeIndex, activeRow, activeTabName);
  const getValueForFieldName = (data, fieldName) => {
    const field = data?.find((field) => field?.fieldName === fieldName);
    return field ? field.value : "";
  };
  const profitCenterInnerData = profitCenterData[activeIndex];
  let key = label
    .replaceAll("(", "")
    .replaceAll(")", "")
    .replaceAll("/", "")
    .replaceAll("-", "")
    .replaceAll(".", "")
    .split(" ")
    .join("");
  useEffect(() => {
    setEditedValue(value);
  }, [value]);
  useEffect(() => {
    if (
      visibility === "0" ||
      visibility === "Required"
    ) {
      //alert("coming");
      console.log(visibility,"visibility")
      dispatch(setGLRequiredFields(key));
    }
  }, [activeTabName]);

  const fieldData = {
    label,
    value: editedValue,
    units,
    type,
  };
  console.log("fieldData", fieldData, profitCenterInnerData);
  const handleSave = () => {
    onSave(fieldData);
  };

  const onEdit = (label, newValue) => {
    console.log("fieldGroup", fieldGroup, label, newValue);
    dispatch(
      setPayload({
        keyname: key
          .replaceAll("(", "")
          .replaceAll(")", "")
          .replaceAll("/", "")
          .replaceAll("-", "")
          .replaceAll(".", "")
          .split(" ")
          .join(""),
        data: newValue,
      })
    );
    let changedData = profitCenterData?.map((item, index) => {
      let activeTabName = pcTabs[activeTabIndex];
      if (index === activeIndex) {
        let temp0 = item.viewData;
        let temp = item.viewData[activeTabName]; //Basic Data
        console.log("temp", temp);
        let temp2 = item.viewData[activeTabName][fieldGroup]; //names
        console.log("temp2", temp2);
        return {
          ...item,
          viewData: {
            ...temp0,
            [activeTabName]: {
              ...temp,
              [fieldGroup]: temp2?.map((innerItem) => {
                if (innerItem.fieldName === label) {
                  return { ...innerItem, value: newValue };
                  // console.log('...inneritem', ...innerItem)
                } else {
                  return innerItem;
                }
              }),
            },
          },
        };
      } else {
        return item;
      }
    });
    console.log("changedData", changedData);
    dispatch(setMultipleGLData(changedData));
  };

  const getCurrency = (newValue) => {
    console.log("compcode", newValue);
    const hSuccess = (data) => {
      console.log("value", data);
      dispatch(
        setPayload({
          keyname: "Currency",
          data: "",
        })
      );
      dispatch(setDropDown({ keyName: "Currency", data: data.body }));
    };
    const hError = (error) => {
      console.log(error, "error in dojax");
    };
    doAjax(
      `/${destination_CostCenter}/data/getCurrency?companyCode=${newValue?.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getRegion = (newValue) => {
    console.log("countryyyyy", newValue);
    const hSuccess = (data) => {
      console.log("value", data);
      dispatch(
        setPayload({
          keyname: "Region",
          data: "",
        })
      );
      dispatch(setDropDown({ keyName: "Region", data: data.body }));
    };
    const hError = (error) => {
      console.log(error, "error in dojax");
    };
    doAjax(
      `/${destination_CostCenter}/data/getRegionBasedOnCountry?country=${newValue?.code}`,
      "get",
      hSuccess,
      hError
    );
  };

  useEffect(() => {
    if (label === "Key Of Last Interest Calc" || label === "Date Of Last Interest Run") {
      setEditedValue(parseInt(value.replace("/Date(", "").replace(")/", "")));
    }
  }, [value]);

  console.log("editedValue[key] ", dropDownData[key]);
  console.log("editedValue[key] ", editedValue);
  return (
    <Grid item>
      <Stack>
        {isEditMode ? (
          <>
            <Typography variant="body2" color="#777">
              {label}{" "}
              {visibility === "Required" || visibility === "0" ? (
                <span style={{ color: "red" }}>*</span>
              ) : (
                ""
              )}
            </Typography>
            {type === "Drop Down" ? (
              <Autocomplete
                options={dropDownData[key] ?? []}
                value={
                  (getValueForFieldName(
                    profitCenterInnerData.viewData[activeTabName][fieldGroup],
                    label
                  ) &&
                    dropDownData[key]?.filter(
                      (x) =>
                        x.code ===
                        getValueForFieldName(
                          profitCenterInnerData.viewData[activeTabName][
                            fieldGroup
                          ],
                          label
                        )
                    ) &&
                    dropDownData[key]?.filter(
                      (x) =>
                        x.code ===
                        getValueForFieldName(
                          profitCenterInnerData.viewData[activeTabName][
                            fieldGroup
                          ],
                          label
                        )
                    )[0]) ||
                  ""
                }
                onChange={(event, newValue) => {
                  if (label === "Comp Code") {
                    getCurrency(newValue);
                  }
                  if (label === "Country/Reg") {
                    getRegion(newValue);
                  }
                  onEdit(label, newValue?.code);
                  console.log("newValue", newValue);
                  setEditedValue(newValue.code);
                  setChangeStatus(true);
                  console.log("keys", key);
                }}
                getOptionLabel={(option) => {
                  console.log("optionn", option);
                  return option === "" || option?.code === ""
                    ? ""
                    : `${option?.code} - ${option?.desc}` ?? "";
                }}
                // isOptionEqualToValue={(a,b)=>{ return a.code===b.code}}
                renderOption={(props, option) => {
                  console.log("option vakue", option);
                  return (
                    <li {...props}>
                      <Typography style={{ fontSize: 12 }}>
                        {`${option?.code} - ${option?.desc}`}
                      </Typography>
                    </li>
                  );
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    variant="outlined"
                    placeholder={`Select ${fieldData.label}`}
                    size="small"
                    label={null}
                  />
                )}
              />
            ) : type === "Input" ? (
              <TextField
                variant="outlined"
                size="small"
                value={getValueForFieldName(
                  profitCenterInnerData.viewData[activeTabName][fieldGroup],
                  label
                ).toUpperCase()}
                placeholder={`ENTER ${fieldData.label.toUpperCase()}`}
                inputProps={{maxLength: length}}
                onChange={(event) => {
                  // const newValue = event.target.value;
                  console.log("event", event.target.value);
                  //onEdit(label, event.target.value);
                  // setEditedValue(newValue);
                  const newValue = event.target.value;
                  if (newValue.length > 0 && newValue[0] === " ") {//
                    onEdit(label, newValue.trimStart());
                  } else {
                    //let costCenterValue = e.target.value;
                    let glUpperCase = newValue.toUpperCase();
                    onEdit(label, glUpperCase);
                  }
                }}
              />
            ) : type === "Calendar" ? (
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  slotProps={{ textField: { size: "small" } }}
                  value={editedValue}
                  placeholder="Select Date Range"
                  onChange={(newValue) => {
                    onEdit(newValue);
                    setEditedValue(newValue);
                  }}
                  // required={
                  //   props.details.visibility === "0" ||
                  //   props.details.visibility === "Required"
                  // }
                />
              </LocalizationProvider>
            ) : type === "Radio Button" ? (
              <Grid item md={2}>
                <Checkbox
                  sx={{ padding: 0 }}
                  checked={getValueForFieldName(
                    profitCenterInnerData.viewData[activeTabName][fieldGroup],
                    label
                  ) == true}
                  onChange={(e) => {
                    console.log('oncheckbox', label, e.target.checked)
                    onEdit(label, e.target.checked);
                    // setEditedValue(newValue);
                  }}
                />
              </Grid>
            ) : (
              ""
            )}
          </>
        ) : (
          <>
            <>
              <Typography variant="body2" color="#777">
                {label}{" "}
                {visibility === "Required" || visibility === "0" ? (
                  <span style={{ color: "red" }}>*</span>
                ) : (
                  ""
                )}
              </Typography>
              <Typography variant="body2" fontWeight="bold">
                {label === "Analysis Period From" ||
                label === "Analysis Period To"
                  ? moment(editedValue).format("DD MMM YYYY")
                  : editedValue}
                {type === "Radio Button" ? (
                  <Checkbox
                    sx={{ padding: 0 }}
                    checked={editedValue}
                    disabled
                  />
                ) : (
                  ""
                )}
              </Typography>
            </>
          </>
        )}
      </Stack>
    </Grid>
  );
};

export default EditFieldForMassGL;
