import{r as d,s as an,b as dn,u as un,q as M,bh as pn,a as n,j as u,b4 as Z,T as C,B as $,br as w,G as a,z as lt,V as mn,aF as hn,X as xn,al as Cn,bm as xe,x as oe,aE as se,t as h,aB as I,aD as ce,F as z,b8 as W,K as b,eV as v,cd as fn,eU as Ce,bq as fe,C as bn,aG as Sn,I as at,ar as vn,E as An,W as yn,b1 as In,ai as H,f3 as Tn,bp as kn,ab as Fn}from"./index-17b8d91e.js";import{l as En}from"./lookup-1dcf10ac.js";import{d as Nn}from"./ArrowCircleLeftOutlined-2a09f8e2.js";import{d as re}from"./EditOutlined-36c8ca4d.js";import{R as gn}from"./ReusableAttachementAndComments-bab6bbfc.js";import{C as qn,d as Bn}from"./ChangeLog-0f47d713.js";/* empty css            */import{E as G}from"./EditableFieldForGL-e4ab886c.js";import{a as On,b as jn,S as Mn}from"./Stepper-88e4fb0c.js";import"./CloudUpload-27b6d63e.js";import"./utilityImages-067c3dc2.js";import"./Add-98854918.js";import"./Delete-9f4d7a45.js";import"./DatePicker-68227989.js";import"./dateViewRenderers-34586552.js";import"./useSlotProps-e34e1e13.js";import"./InputAdornment-5b0053c5.js";import"./CSSTransition-30917e2c.js";import"./useMediaQuery-6a073ac5.js";import"./DesktopDatePicker-07c19cde.js";import"./useMobilePicker-9978caff.js";const lo=()=>{var We,He,_e,Ke,Ue,Xe,Ye,Je,Qe,Ze,we,Ge,Re,Pe,De,Le,et,tt,nt,ot;const[F,be]=d.useState(!1);d.useState(0);const[K,dt]=d.useState(!0);d.useState({});const[E,ut]=d.useState([]),[f,ie]=d.useState(0),[Se,pt]=d.useState([]),[S,mt]=d.useState();d.useState(!1),d.useState([]);const[ht,A]=d.useState(!1),[xt,g]=d.useState(!1),[ve,T]=d.useState(""),[Ct,k]=d.useState(!1),[$n,q]=d.useState(!0),[zn,B]=d.useState(!1),[Vn,_]=d.useState(!1),[ft,le]=d.useState(!1),[bt,ae]=d.useState(!1),[St,Ae]=d.useState(!1),[R,ye]=d.useState(""),[Wn,vt]=d.useState([]);d.useState(!1);const[Hn,At]=d.useState([]),[yt,Ie]=d.useState(!1),[It,Te]=d.useState(!0),[_n,U]=d.useState(!1),[ke,X]=d.useState(!0),[Tt,O]=d.useState(!1),[kt,Fe]=d.useState(!1),[Ft,Ee]=d.useState(""),[Ne,ge]=d.useState(""),[qe,Et]=d.useState([]),[de,Nt]=d.useState(""),[gt,Be]=d.useState(!1),y=an(),Oe=dn(),qt=un(),Bt=M(o=>o.appSettings);let ue=M(o=>{var i;return(i=o.userManagement.entitiesAndActivities)==null?void 0:i["General Ledger"]}),N=M(o=>{var i;return(i=o==null?void 0:o.initialData)==null?void 0:i.IWMMyTask}),m=M(o=>o.userManagement.userData),t=qt.state,Ot=M(o=>o.generalLedger.requiredFields),l=M(o=>o.userManagement.taskData);const e=M(o=>o.edit.payload);console.log("ccroewdata",e);const pe=M(o=>o.generalLedger.generalLedgerViewData);console.log("generalLedgerRowData",t),console.log("costCenterDetails",Se),console.log(R,"Remarks");const jt=()=>{Be(!1)};console.log("taskData",N),console.log("taskRowDetails",l),console.log("glids",S);const Y=()=>{Te(!0),Ie(!1)},Mt=(o,i)=>{const s=r=>{y(H({keyName:o,data:r.body})),setApiCount(x=>x+1)},c=r=>{console.log(r)};b(`/${v}/data/${i}`,"get",s,c)},$t=()=>{var o,i;(i=(o=En)==null?void 0:o.generalLedger)==null||i.map(s=>{Mt(s==null?void 0:s.keyName,s==null?void 0:s.endPoint)})},zt=()=>{var s,c;const o=r=>{y(H({keyName:"TaxCategory",data:r.body}))},i=r=>{console.log(r)};b(`/${v}/data/getTaxCategory?companyCode=${(c=(s=t==null?void 0:t.copyFromCompCode)==null?void 0:s.newCompanyCodeToCopyFrom)==null?void 0:c.code}`,"get",o,i)},Vt=()=>{var s,c;const o=r=>{y(H({keyName:"HouseBank",data:r.body}))},i=r=>{console.log(r)};b(`/${v}/data/getHouseBank?companyCode=${(c=(s=t==null?void 0:t.copyFromCompCode)==null?void 0:s.newCompanyCodeToCopyFrom)==null?void 0:c.code}`,"get",o,i)},Wt=()=>{var s,c;const o=r=>{y(H({keyName:"FieldStatusGroup",data:r.body}))},i=r=>{console.log(r)};b(`/${v}/data/getFieldStatusGroup?companyCode=${(c=(s=t==null?void 0:t.copyFromCompCode)==null?void 0:s.newCompanyCodeToCopyFrom)==null?void 0:c.code}`,"get",o,i)};d.useEffect(()=>{Nt(pn("GL"))},[]);const Ht=()=>{var s,c;const o=r=>{y(H({keyName:"GroupAccountNumber",data:r.body}))},i=r=>{console.log(r)};b(`/${v}/data/getGroupAccountNumber?chartAccount=${(c=(s=t==null?void 0:t.chartOfAccounts)==null?void 0:s.newChartOfAccount)==null?void 0:c.code}`,"get",o,i)},_t=()=>{var s,c;const o=r=>{y(H({keyName:"AlternativeAccountNumber",data:r.body}))},i=r=>{console.log(r)};b(`/${v}/data/getAlternativeAccountNumber?chartAccount=${(c=(s=t==null?void 0:t.chartOfAccounts)==null?void 0:s.newChartOfAccount)==null?void 0:c.code}`,"get",o,i)},Kt=()=>{var s,c;const o=r=>{y(H({keyName:"AccountGroup",data:r.body}))},i=r=>{console.log(r)};b(`/${v}/data/getAccountGroupCodeDesc?chartAccount=${(c=(s=t==null?void 0:t.chartOfAccounts)==null?void 0:s.newChartOfAccount)==null?void 0:c.code}`,"get",o,i)};var P={GeneralLedgerID:S!=null&&S.GeneralLedgeId?S==null?void 0:S.GeneralLedgeId:"",Action:"I",RequestID:"",TaskStatus:"",TaskId:l!=null&&l.taskId?l==null?void 0:l.taskId:"",Remarks:R||"",Info:"",CreationId:(l==null?void 0:l.processDesc)==="Create"?l==null?void 0:l.subject.slice(3):(t==null?void 0:t.requestType)==="Create"?t==null?void 0:t.requestId.slice(3):"",EditId:(l==null?void 0:l.processDesc)==="Change"?l==null?void 0:l.subject.slice(3):(t==null?void 0:t.requestType)==="Change"?t==null?void 0:t.requestId.slice(3):"",DeleteId:"",MassCreationId:"",MassEditId:"",MassDeleteId:"",RequestType:"Create",ReqCreatedBy:m==null?void 0:m.user_id,ReqCreatedOn:"",ReqUpdatedOn:"",RequestStatus:"",Testrun:It===!0,COA:(He=(We=t==null?void 0:t.chartOfAccounts)==null?void 0:We.newChartOfAccount)!=null&&He.code?(Ke=(_e=t==null?void 0:t.chartOfAccounts)==null?void 0:_e.newChartOfAccount)==null?void 0:Ke.code:"",CompanyCode:(Xe=(Ue=t==null?void 0:t.copyFromCompCode)==null?void 0:Ue.newCompanyCodeToCopyFrom)!=null&&Xe.code?(Je=(Ye=t==null?void 0:t.copyFromCompCode)==null?void 0:Ye.newCompanyCodeToCopyFrom)==null?void 0:Je.code:"",CoCodeToExtend:"",GLAccount:t!=null&&t.newGLAccount?t==null?void 0:t.newGLAccount:"",Accounttype:e!=null&&e.AccountType?e==null?void 0:e.AccountType:"",AccountGroup:e!=null&&e.AccountGroup?e==null?void 0:e.AccountGroup:"",GLname:e!=null&&e.ShortText?e==null?void 0:e.ShortText:"",Description:e!=null&&e.LongText?e==null?void 0:e.LongText:"",TradingPartner:e!=null&&e.TradingPartner?e==null?void 0:e.TradingPartner:"",GroupAccNo:e!=null&&e.GroupAccountNumber?e==null?void 0:e.GroupAccountNumber:"121100",AccountCurrency:e!=null&&e.AccountCurrency?e==null?void 0:e.AccountCurrency:"",Exchangerate:e!=null&&e.ExchangeRateDifferenceKey?e==null?void 0:e.ExchangeRateDifferenceKey:"",Balanceinlocrcy:(e==null?void 0:e.OnlyBalanceInLocalCurrency)==="true"?"X":"",Taxcategory:e!=null&&e.TaxCategory?e==null?void 0:e.TaxCategory:"",Pstnwotax:(e==null?void 0:e.PostingWithoutTaxAllowed)==="true"?"X":"",ReconAcc:e!=null&&e.ReconAccountForAccountType?e==null?void 0:e.ReconAccountForAccountType:"",Valuationgrp:e!=null&&e.ValuationGroup?e==null?void 0:e.ValuationGroup:"",AlterAccno:e!=null&&e.AlternativeAccountNumber?e==null?void 0:e.AlternativeAccountNumber:"",Openitmmanage:(e==null?void 0:e.OpenItemManagement)==="true"?"X":"",Sortkey:e!=null&&e.SortKey?e==null?void 0:e.SortKey:"",CostEleCategory:e!=null&&e.CostElementCategory?e==null?void 0:e.CostElementCategory:"",FieldStsGrp:e!=null&&e.FieldStatusGroup?e==null?void 0:e.FieldStatusGroup:"",PostAuto:(e==null?void 0:e.PostAutomaticallyOnly)==="true"?"X":"",Supplementautopost:(e==null?void 0:e.SupplementAutoPostings)==="true"?"X":"",Planninglevel:e!=null&&e.PlanningLevel?e==null?void 0:e.PlanningLevel:"",Relvnttocashflow:(e==null?void 0:e.RelevantToCashFlows)==="true"?"X":"",HouseBank:e!=null&&e.HouseBank?e==null?void 0:e.HouseBank:"",AccountId:e!=null&&e.AccountID?e==null?void 0:e.AccountID:"",Interestindicator:e!=null&&e.InterestIndicator?e==null?void 0:e.InterestIndicator:"",ICfrequency:e!=null&&e.InterestCalculationFrequency?e==null?void 0:e.InterestCalculationFrequency:"",KeydateofLIC:e!=null&&e.KeyDateOfLastIntCalc?e==null?void 0:e.KeyDateOfLastIntCalc:"",LastIntrstundate:e!=null&&e.DateOfLastInterestRun?e==null?void 0:e.DateOfLastInterestRun:"",AccmngExistsys:"",Infationkey:"",Tolerancegrp:"",AuthGroup:"",AccountClerk:"",ReconAccReady:"",PostingBlocked:"",PlanningBlocked:""};const Ut=()=>{E[f];let o=Object.entries(pe);const i={};o.map(s=>(Object.entries(s[1]).forEach(r=>{r[1].forEach(x=>{i[x.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("")]=x.value})}),s)),y(fn(i))},Xt=()=>{var c,r,x,p,V,J,Q,st;var o={id:"",glAccount:(r=(c=t==null?void 0:t.copyFromGlAccount)==null?void 0:c.newGLAccountCopyFrom)!=null&&r.code?(p=(x=t==null?void 0:t.copyFromGlAccount)==null?void 0:x.newGLAccountCopyFrom)==null?void 0:p.code:"",compCode:(J=(V=t==null?void 0:t.copyFromCompCode)==null?void 0:V.newCompanyCodeToCopyFrom)!=null&&J.code?(st=(Q=t==null?void 0:t.copyFromCompCode)==null?void 0:Q.newCompanyCodeToCopyFrom)==null?void 0:st.code:"",reqStatus:"Approved",screenName:"Create"};const i=ne=>{const he=ne.body.viewData,rn=ne.body;y(Tn(he));const ct=Object.keys(he);ut(ct);const ln=["Attachment & Documents"];E.concat(ln);const rt=ct.map(it=>({category:it,data:he[it],setIsEditMode:be}));pt(rt),mt(rn),console.log("mappedData",rt,S)},s=ne=>{console.log(ne)};b(`/${v}/data/displayGeneralLedger`,"post",i,s,o)};console.log("ID",S),d.useEffect(()=>{Xt(),zt(),Wt(),Vt(),Kt(),_t(),Ht(),$t()},[]),d.useEffect(()=>{pe.length!==0&&Ut()},[pe]);const je=()=>{le(!1)},Yt=()=>{St?(ae(!1),Ae(!1)):(ae(!1),Oe("/masterDataCockpit/generalLedger"))},Me=()=>{Be(!0)},$e=()=>kn(e,Ot,Et),D=()=>{X(!0);const o=$e();F?o?(ie(i=>i-1),y(Ce())):Me():(ie(i=>i-1),y(Ce()))},L=()=>{$e()?(ie(i=>i+1),y(Ce())):Me()},ee=()=>{be(!0),dt(!1)},Jt=o=>{Fe(o)},Qt=()=>{Y()},Zt=()=>{Y(),sn()},j=()=>{le(!0)},te=()=>{ae(!0)},wt=()=>{Fe(!0)},ze=()=>{var x;U(!0);var o={glName:e!=null&&e.ShortText?(x=e==null?void 0:e.ShortText)==null?void 0:x.toUpperCase():"",compCode:t!=null&&t.compCode?t==null?void 0:t.compCode:S!=null&&S.CompCode?S==null?void 0:S.CompCode:""};const i=p=>{var V,J,Q;p.statusCode===201?(A("Create"),console.log("success"),A("Create"),T("All Data has been Validated. General Ledger can be Send for Review"),X(!1),O(!1),k("success"),q(!1),g(!0),te(),B(!0),Ae(!0),U(!1)):(U(!1),A("Error"),g(!1),T(`${(V=p==null?void 0:p.body)!=null&&V.message[0]?(J=p==null?void 0:p.body)==null?void 0:J.message[0]:(Q=p==null?void 0:p.body)==null?void 0:Q.value}`),O(!1),k("danger"),q(!1),B(!0),j(),X(!0)),handleClose()},s=p=>{console.log(p)},c=p=>{p.body.length===0||!p.body.some(V=>V.toUpperCase()===o)?(U(!1),X(!1),b(`/${v}/alter/validateSingleGeneralLedger`,"post",i,s,P)):(U(!1),A("Duplicate Check"),g(!1),T("There is a direct match for the Short Text. Please change the Short Text."),O(!1),k("danger"),q(!1),B(!0),j(),X(!0))},r=p=>{console.log(p)};b(`/${v}/alter/fetchGlNameNCompCodeDupliChk`,"post",c,r,o)},Gt=()=>{Zt()},Rt=()=>{let o=l!=null&&l.subject?l==null?void 0:l.subject:t==null?void 0:t.requestId,i=s=>{var c=[];s.documentDetailDtoList.forEach(r=>{var x={id:r.documentId,docType:r.fileType,docName:r.fileName,uploadedOn:Fn(r.docCreationDate).format(Bt.date),uploadedBy:r.createdBy};c.push(x)}),vt(c)};b(`/${fe}/documentManagement/getDocByRequestId/${o}`,"get",i)},Pt=()=>{let o=l!=null&&l.subject?l==null?void 0:l.subject:t==null?void 0:t.requestId,i=c=>{console.log("commentsdata",c);var r=[];c.commentDtoList.forEach(x=>{var p={id:x.commentId,comment:x.comment,user:x.createdBy};r.push(p)}),At(r)},s=c=>{console.log(c)};b(`/${v}/activitylog/fetchTaskDetailsForRequestId?requestId=${o}`,"get",i,s)},Dt=()=>{Lt()},Lt=()=>{k(!1),j(),A("Confirm"),T("Do You Want to Save as Draft ?"),O(!0),Ee("proceed"),ge("Create")},en=()=>{tn()},tn=()=>{k(!1),j(),A("Confirm"),T("Do You Want to Save as Draft?"),O(!0),Ee("proceed"),ge("Change")},nn=()=>{if(console.log(Ne,"dialogType"),me(),_(!0),Ne==="Change"){const o=s=>{if(_(!1),s.statusCode===200){console.log("success"),A("Create"),T(`General Ledger Saved As Draft with ID CLS${s.body} `),O(!1),k("success"),q(!1),g(!0),te(),B(!0);const c={artifactId:de,createdBy:m==null?void 0:m.emailId,artifactType:"GeneralLedger",requestId:`CLS${s==null?void 0:s.body}`},r=p=>{console.log("Second API success",p)},x=p=>{console.error("Second API error",p)};b(`/${fe}/documentManagement/updateDocRequestId`,"post",r,x,c)}else A("Error"),g(!1),T("Failed Saving General Ledger"),O(!1),k("danger"),q(!1),B(!0),j();handleClose()},i=s=>{console.log(s)};b(`/${v}/alter/changeGeneralLedgerAsDraft`,"post",o,i,P)}else{const o=s=>{if(me(),_(!1),s.statusCode===200){console.log("success"),A("Create"),T(`Cost Center Saved As Draft with ID NLS${s.body} `),O(!1),k("success"),q(!1),g(!0),te(),B(!0);const c={artifactId:de,createdBy:m==null?void 0:m.emailId,artifactType:"GeneralLedger",requestId:`NCS${s==null?void 0:s.body}`},r=p=>{console.log("Second API success",p)},x=p=>{console.error("Second API error",p)};b(`/${fe}/documentManagement/updateDocRequestId`,"post",r,x,c)}else A("Error"),g(!1),T("Failed Saving General Ledger"),O(!1),k("danger"),q(!1),B(!0),j();handleClose()},i=s=>{console.log(s)};b(`/${v}/alter/generalLedgerAsDraft`,"post",o,i,P)}},on=()=>{Te(!1),Ie(!0)};d.useEffect(()=>{Rt(),Pt()},[]);const Ve=E.map(o=>{const i=Se.filter(s=>{var c;return((c=s.category)==null?void 0:c.split(" ")[0])==(o==null?void 0:o.split(" ")[0])});if(i.length!=0)return{category:o==null?void 0:o.split(" ")[0],data:i[0].data}}).map((o,i)=>{if(console.log("categoryy",o.category),(o==null?void 0:o.category)=="Type/Description"&&f==0)return[n(a,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(o.data).map(s=>u(a,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Z},children:[n(C,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:s}),n($,{sx:{width:"100%"},children:n(w,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:n(a,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:o.data[s].map(c=>(console.log("fieldDatatttt",c),n(G,{length:c.maxLength,label:c.fieldName,value:c.value,visibility:c.visibility,onSave:r=>handleFieldSave(c.fieldName,r),data:e,isEditMode:F,type:c.fieldType,field:c,taskRequestId:t==null?void 0:t.requestId,generalLedgerRowData:t})))})})})]},s))},o.category)];if((o==null?void 0:o.category)=="Control"&&f==1)return[n(a,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(o.data).map(s=>u(a,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Z},children:[n(C,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:s}),n($,{sx:{width:"100%"},children:n(w,{children:n(a,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:o.data[s].map(c=>n(G,{label:c.fieldName,value:(c==null?void 0:c.value)==="X",onSave:r=>handleFieldSave(c.fieldName,r),isEditMode:F,type:c.fieldType,taskRequestId:t==null?void 0:t.requestId,visibility:c.visibility,data:e,generalLedgerRowData:t},c.fieldName))})})})]},s))},o.category)];if((o==null?void 0:o.category)=="Create/Bank/Interest"&&f==2)return[n(a,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(o.data).map(s=>u(a,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Z},children:[n(C,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:s}),n($,{sx:{width:"100%"},children:n(w,{children:n(a,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:o.data[s].map(c=>n(G,{label:c.fieldName,value:c.value,onSave:r=>handleFieldSave(c.fieldName,r),isEditMode:F,type:c.fieldType,taskRequestId:t==null?void 0:t.requestId,data:e,generalLedgerRowData:t},c.fieldName))})})})]},s))},o.category)];if((o==null?void 0:o.category)=="Keyword/Translation"&&f==3)return[n(a,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(o.data).map(s=>u(a,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Z},children:[n(C,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:s}),n($,{sx:{width:"100%"},children:n(w,{children:n(a,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:o.data[s].map(c=>n(G,{label:c.fieldName,value:c.value,onSave:r=>handleFieldSave(c.fieldName,r),isEditMode:F,type:c.fieldType,taskRequestId:t==null?void 0:t.requestId,visibility:c.visibility,data:e,generalLedgerRowData:t},c.fieldName))})})})]},s))},o.category)];if((o==null?void 0:o.category)=="Information"&&f==4)return[n(a,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(o.data).map(s=>u(a,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Z},children:[n(C,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:s}),n($,{sx:{width:"100%"},children:n(w,{children:n(a,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:o.data[s].map(c=>n(G,{label:c.fieldName,value:c.value,onSave:r=>handleFieldSave(c.fieldName,r),isEditMode:F,type:c.fieldType,taskRequestId:t==null?void 0:t.requestId,visibility:c.visibility,data:e,generalLedgerRowData:t},c.fieldName))})})})]},s))},o.category)];if((o==null?void 0:o.category)=="Attachments")return[n(gn,{title:"GeneralLedger",useMetaData:!1,artifactId:de,artifactName:"GeneralLedger"})]}),sn=()=>{const o=s=>{_(),s.statusCode===200?(console.log("success"),A("Create"),T(`General Ledger Submitted for Review with ID NLS${s.body} `),k("success"),q(!1),g(!0),te(),B(!0),_(!1)):(A("Error"),g(!1),T("Failed Saving the Data"),k("danger"),q(!1),B(!0),j(),_(!1)),handleClose()},i=s=>{console.log(s)};b(`/${v}/alter/generalLedgerSubmitForReview`,"post",o,i,P)},cn=(o,i)=>{const s=o.target.value;if(s.length>0&&s[0]===" ")ye(s.trimStart());else{let c=s.toUpperCase();ye(c)}},me=()=>{le(!1)};return u("div",{style:{backgroundColor:"#FAFCFF"},children:[n(bn,{dialogState:ft,openReusableDialog:j,closeReusableDialog:je,dialogTitle:ht,dialogMessage:ve,handleDialogConfirm:je,dialogOkText:"OK",showExtraButton:Tt,showCancelButton:!0,dialogSeverity:Ct,handleDialogReject:me,handleExtraText:Ft,handleExtraButton:nn}),xt&&n(lt,{openSnackBar:bt,alertMsg:ve,handleSnackBarClose:Yt}),qe.length!=0&&n(lt,{openSnackBar:gt,alertMsg:"Please fill the following Field: "+qe.join(", "),handleSnackBarClose:jt}),u(mn,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:yt,onClose:Y,children:[u(hn,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[n(C,{variant:"h6",children:"Remarks"}),n(at,{sx:{width:"max-content"},onClick:Y,children:n(Sn,{})})]}),n(yn,{sx:{padding:".5rem 1rem"},children:n(oe,{children:n($,{sx:{minWidth:400},children:n(vn,{sx:{height:"auto"},fullWidth:!0,children:n(An,{sx:{backgroundColor:"#F5F5F5"},value:R,onChange:cn,multiline:!0,placeholder:"Enter Remarks"})})})})}),u(xn,{sx:{display:"flex",justifyContent:"end"},children:[n(h,{sx:{width:"max-content",textTransform:"capitalize"},onClick:Y,children:"Cancel"}),n(h,{className:"button_primary--normal",type:"save",onClick:Gt,variant:"contained",children:"Submit"})]})]}),u(a,{container:!0,sx:Cn,children:[u(a,{item:!0,md:12,style:{padding:"16px",display:"flex"},children:[u(a,{md:9,sx:{display:"flex"},children:[n(a,{children:n(at,{color:"primary","aria-label":"upload picture",component:"label",sx:In,children:n(Nn,{sx:{fontSize:"25px",color:"#000000"},onClick:()=>{Oe("/masterDataCockpit/generalLedger")}})})}),u(a,{children:[F?(l==null?void 0:l.processDesc)==="Create"||(t==null?void 0:t.requestType)==="Create"?n(z,{children:u(a,{item:!0,md:12,children:[n(C,{variant:"h3",children:n("strong",{children:"Create General Ledger "})}),n(C,{variant:"body2",color:"#777",children:"This view edits the details of the General Ledger"})]})}):n(z,{children:u(a,{item:!0,md:12,children:[n(C,{variant:"h3",children:n("strong",{children:"Create General Ledger "})}),n(C,{variant:"body2",color:"#777",children:"This view edits the details of the General Ledger"})]})}):"",K?u(a,{item:!0,md:12,children:[n(C,{variant:"h3",children:n("strong",{children:"Display General Ledger "})}),n(C,{variant:"body2",color:"#777",children:"This view displays the details of the General Ledger"})]}):""]})]}),u(a,{md:3,sx:{display:"flex",justifyContent:"flex-end"},gap:2,children:[t!=null&&t.requestId||l!=null&&l.processDesc?n(a,{children:n(h,{variant:"outlined",size:"small",sx:W,onClick:wt,title:"Change Log",children:n(Bn,{sx:{padding:"2px"},fontSize:"small"})})}):"",kt&&n(qn,{open:!0,closeModal:Jt,requestId:t!=null&&t.requestId?t==null?void 0:t.requestId:l==null?void 0:l.subject,requestType:t!=null&&t.requestType?t==null?void 0:t.requestType:(Qe=N==null?void 0:N.body)==null?void 0:Qe.processDesc,pageName:"generalLedger",controllingArea:t!=null&&t.controllingArea?t==null?void 0:t.controllingArea:(Ze=N==null?void 0:N.body)==null?void 0:Ze.controllingArea,centerName:t!=null&&t.costCenter?t==null?void 0:t.costCenter:(we=N==null?void 0:N.body)==null?void 0:we.costCenter}),xe(ue,"General Ledger","ChangeGL")&&((m==null?void 0:m.role)==="Super User"&&(t!=null&&t.requestType)&&K?n(a,{gap:1,sx:{display:"flex"},children:n(a,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:n(z,{children:n(a,{item:!0,children:u(h,{variant:"outlined",size:"small",sx:W,onClick:ee,children:["Fill Details",n(re,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(m==null?void 0:m.role)==="Finance"&&(t!=null&&t.requestType||l!=null&&l.processDesc)&&K?n(a,{gap:1,sx:{display:"flex"},children:n(a,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:n(z,{children:n(a,{item:!0,children:u(h,{variant:"outlined",size:"small",sx:W,onClick:ee,children:["Fill Details",n(re,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(m==null?void 0:m.role)==="Super User"&&!(t!=null&&t.requestType)&&K?n(a,{gap:1,sx:{display:"flex"},children:n(a,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:n(z,{children:n(a,{item:!0,children:u(h,{variant:"outlined",size:"small",sx:W,onClick:ee,children:["Change",n(re,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(m==null?void 0:m.role)==="Finance"&&!(t!=null&&t.requestType)&&K?n(a,{gap:1,sx:{display:"flex"},children:n(a,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:n(z,{children:n(a,{item:!0,children:u(h,{variant:"outlined",size:"small",sx:W,onClick:ee,children:["Change",n(re,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):"")]})]}),n(a,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:u($,{width:"100%",sx:{marginLeft:"40px"},children:[n(a,{item:!0,sx:{paddingTop:"2px !important"},children:u(oe,{flexDirection:"row",children:[n("div",{style:{width:"10%"},children:n(C,{variant:"body2",color:"#777",children:"Chart of Accounts"})}),u(C,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[":"," ",(Re=(Ge=t==null?void 0:t.chartOfAccounts)==null?void 0:Ge.newChartOfAccount)!=null&&Re.code?(De=(Pe=t==null?void 0:t.chartOfAccounts)==null?void 0:Pe.newChartOfAccount)==null?void 0:De.code:""]})]})}),n(a,{item:!0,sx:{paddingTop:"2px !important"},children:u(oe,{flexDirection:"row",children:[n("div",{style:{width:"10%"},children:n(C,{variant:"body2",color:"#777",children:"Company Code"})}),u(C,{variant:"body2",fontWeight:"bold",children:[":"," ",(et=(Le=t==null?void 0:t.copyFromCompCode)==null?void 0:Le.newCompanyCodeToCopyFrom)!=null&&et.code?(nt=(tt=t==null?void 0:t.copyFromCompCode)==null?void 0:tt.newCompanyCodeToCopyFrom)==null?void 0:nt.code:""]})]})}),n(a,{item:!0,sx:{paddingTop:"2px !important"},children:u(oe,{flexDirection:"row",children:[n("div",{style:{width:"10%"},children:n(C,{variant:"body2",color:"#777",children:"G/L Account"})}),u(C,{variant:"body2",fontWeight:"bold",children:[":"," ",t!=null&&t.newGLAccount?t==null?void 0:t.newGLAccount:""]})]})})]})}),u(a,{container:!0,style:{marginLeft:25},children:[n(Mn,{activeStep:f,sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},"aria-label":"mui tabs example",children:E.map((o,i)=>n(On,{children:n(jn,{sx:{fontWeight:"700"},children:o})},o))}),Ve&&((ot=Ve[f])==null?void 0:ot.map((o,i)=>n($,{sx:{mb:2,width:"100%"},children:n(C,{variant:"body2",children:o})},i)))]})]}),u(a,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:[xe(ue,"General Ledger","ChangeGL")&&(!(t!=null&&t.requestType)&&!F?n(ce,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:u(se,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(h,{variant:"contained",size:"small",sx:{...I,mr:1},onClick:D,disabled:f===0,children:"Back"}),n(h,{variant:"contained",size:"small",sx:{...I,mr:1},onClick:L,disabled:f===E.length-1,children:"Next"})]})}):n(ce,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:u(se,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(h,{variant:"contained",size:"small",sx:{...I,mr:1},onClick:D,disabled:f===0,children:"Back"}),n(h,{variant:"contained",size:"small",sx:{...I,mr:1},onClick:L,disabled:f===E.length-1,children:"Next"})]})})),xe(ue,"General Ledger","ChangeGL")&&((m==null?void 0:m.role)==="Super User"&&!(t!=null&&t.requestType)&&F?n(ce,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:u(se,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(h,{variant:"contained",size:"small",sx:{...I,mr:1},onClick:en,children:"Save As Draft"}),n(h,{variant:"contained",size:"small",sx:{...I,mr:1},onClick:D,disabled:f===0,children:"Back"}),f===E.length-1?u(z,{children:[n(h,{variant:"contained",size:"small",sx:{...W,mr:1},onClick:ze,children:"Validate"}),n(h,{variant:"contained",size:"small",sx:{...I,mr:1},onClick:Qt,disabled:ke,children:"Submit For Review"})]}):n(h,{variant:"contained",size:"small",sx:{...I,mr:1},onClick:L,disabled:f===E.length-1,children:"Next"})]})}):(m==null?void 0:m.role)==="Finance"&&!(t!=null&&t.requestType)&&F?n(ce,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:u(se,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(h,{variant:"contained",size:"small",sx:{...I,mr:1},onClick:Dt,children:"Save As Draft"}),n(h,{variant:"contained",size:"small",sx:{...I,mr:1},onClick:D,disabled:f===0,children:"Back"}),f===E.length-1?u(z,{children:[n(h,{variant:"contained",size:"small",sx:{...W,mr:1},onClick:ze,children:"Validate"}),n(h,{variant:"contained",size:"small",sx:{...I,mr:1},onClick:on,disabled:ke,children:"Submit For Review"})]}):n(h,{variant:"contained",size:"small",sx:{...I,mr:1},onClick:L,disabled:f===E.length-1,children:"Next"})]})}):"")]})]})};export{lo as default};
