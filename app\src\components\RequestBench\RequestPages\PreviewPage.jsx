import { container_Padding } from '@components/Common/commonStyles';
import { Box, Grid, Stack, Typography, Tooltip } from '@mui/material';
import { useSelector } from 'react-redux';
import { API_CODE, REQUEST_STATUS, REQUEST_TYPE } from '@constant/enum';
import { useLocation } from 'react-router-dom';
import usePayloadCreation from "@hooks/usePayloadCreation";
import { useEffect, useState } from 'react';
import { doAjax } from '../../Common/fetchService';
import { destination_MaterialMgmt, destination_ProfitCenter_Mass } from '../../../destinationVariables';
import { END_POINTS } from '@constant/apiEndPoints';
import useLogger from '@hooks/useLogger';
import WorkflowDashboard from "@components/RequestHistory/workFlow/WorkflowDashboard";
// import PreviewDownloadCard from './RequestPages/PreviewDownloadCard';
import WorkflowSkeletonLoader from '@components/common/ui/skelton/WorkflowSkeletonLoader';
import useChangePayloadCreation from '@hooks/useChangePayloadCreation';

const PreviewPage = ({ children }) => {
  const payloadData = useSelector((state) => state.payload);
  const { customError } = useLogger();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const taskData = useSelector((state) => state.userManagement.taskData);
  const isreqBench = queryParams.get("reqBench");
  const RequestId = queryParams.get("RequestId");
  const requestDetails = useSelector((state) => state.request.requestHeader);
  const [workFlowDetails, setWorkFlowDetails] = useState(null)
  const [isLoading, setIsLoading] = useState(false)
  const initialReqScreen = !Boolean(taskData?.taskId) && !isreqBench;
  const requestHeaderDetails = useSelector(
    (state) => state.tabsData.requestHeaderData
  );
  const initialPayload = useSelector((state) => state.payload.payloadData);
  const { createPayloadFromReduxState } = usePayloadCreation({ initialReqScreen, isreqBench });
  const { changePayloadForTemplate } = useChangePayloadCreation(initialPayload?.TemplateName);
  const requestStatus = initialPayload?.RequestStatus;
  const requestStatusCheck = (requestStatus === REQUEST_STATUS.DRAFT || requestStatus === REQUEST_STATUS.DRAFT_IN_CAPS || requestStatus === REQUEST_STATUS.VALIDATED_REQUESTOR || requestStatus === REQUEST_STATUS.VALIDATION_FAILED_REQUESTOR)

  useEffect(() => {
    if(requestStatusCheck){
      const payloadForBifurcation =
      initialPayload?.RequestType === REQUEST_TYPE?.CHANGE || initialPayload?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD
        ? RequestId ? changePayloadForTemplate(true) : changePayloadForTemplate(false)
        : createPayloadFromReduxState(payloadData)

      setIsLoading(true)
      const hSuccess = (data) => {
        if (data.statusCode === API_CODE.STATUS_200) {
          setWorkFlowDetails(data?.body)
          setIsLoading(false)
        }
      }
      const hError = (error) => {
        customError(error)
        setIsLoading(false)
      }

      doAjax(`/${destination_ProfitCenter_Mass}massAction/workflow-details/bifurcation`,
         "post",
          hSuccess, 
          hError,
        payloadForBifurcation);
    }
  }, [payloadData]);

  return (
    <Stack spacing={2}>
      {Object.entries(requestHeaderDetails).map(([key, fields]) => (
        <Grid
          item
          md={12}
          key={key}
          sx={{
            backgroundColor: "white",
            borderRadius: "8px",
            border: "1px solid #E0E0E0",
            boxShadow: "0px 1px 4px rgba(0, 0, 0, 0.1)",
            ...container_Padding,
            pt: '10px'
          }}
        >
          {/* Section Title */}
          <Typography
            sx={{
              fontWeight: "bold",
              mb: "6px",
            }}
          >
            Request Details
          </Typography>

          {/* Fields Container */}
          <Box
            sx={{
              backgroundColor: "#FAFAFA",
              padding: "10px",
              pl: '0px',
              pr: '0px',
              borderRadius: "8px",
              boxShadow: "none",
            }}
          >
            <Grid container spacing={2}>
              {fields
                .filter((field) => field.visibility !== "Hidden")
                .sort((a, b) => a.sequenceNo - b.sequenceNo)
                .map((innerItem) => {
                  let value =
                    requestDetails?.[innerItem?.jsonName] ||
                    initialPayload?.[innerItem?.jsonName] ||
                    "";
                  let formattedValue = "";

                  if (Array.isArray(value)) {
                    formattedValue = value.join(", ");
                  } else if (value instanceof Date || (typeof value === "object" && value instanceof Object && value.toString().includes("GMT"))) {
                    formattedValue = new Date(value).toLocaleString(); // Converts Date to readable format
                  } else {
                    formattedValue = value;
                  }
                  value = formattedValue;
                  return (
                    value && value !== null &&
                    value !== "" && (
                      <Grid item md={3} key={innerItem?.id}>
                        <div
                          style={{
                            padding: "12px",
                            backgroundColor: "#ffffff",
                            borderRadius: "8px",
                            boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
                            // margin: "16px 0",
                            transition: "all 0.3s ease",
                          }}
                        >
                          {/* Field Name with Tooltip */}
                          <Tooltip title={innerItem?.fieldName || "Field Name"}>
                            <Typography
                              variant="body1"
                              sx={{
                                whiteSpace: "nowrap",
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                maxWidth: "100%",
                                fontWeight: 600,
                                fontSize: "12px",
                                marginBottom: "4px",
                                display: "flex",
                                alignItems: "center",
                              }}
                            >
                              {innerItem?.fieldName || "Field Name"}
                              {(innerItem?.visibility === "Required" ||
                                innerItem?.visibility === "MANDATORY") && (
                                  <span
                                    style={{ color: "#d32f2f", marginLeft: "2px" }}
                                  >
                                    *
                                  </span>
                                )}
                            </Typography>
                          </Tooltip>

                          {/* Value with Tooltip */}
                          <Tooltip title={value || "--"}>
                            <div
                              style={{
                                fontSize: "0.8rem",
                                color: "#333333",
                                marginTop: "4px",
                                whiteSpace: "nowrap",
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                maxWidth: "100%",
                              }}
                            >
                              <span
                                style={{
                                  fontWeight: 500,
                                  color: "grey",
                                  letterSpacing: "0.5px",
                                  wordSpacing: "1px",
                                }}
                              >
                                {value || "--"}
                              </span>
                            </div>
                          </Tooltip>
                        </div>
                      </Grid>
                    )
                  );
                })}
            </Grid>
          </Box>
        </Grid>
      ))}
     {/* <PreviewDownloadCard initialReqScreen={initialReqScreen} isreqBench={isreqBench}/> */}
     
     {requestStatusCheck && (
      <>
      {workFlowDetails && !isLoading ? (
        <WorkflowDashboard data={workFlowDetails} />
          ) : (
            <WorkflowSkeletonLoader />
        )}
      </>
     )}
      <Grid>
        {children}
      </Grid>
    </Stack>
  )
}

export default PreviewPage
