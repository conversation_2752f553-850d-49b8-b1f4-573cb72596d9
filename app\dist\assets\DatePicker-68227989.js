import{r as b,cE as a,dv as e,dr as h,cI as O,o as p}from"./index-17b8d91e.js";import{b as k,u as g,W as i,E as M,Y as T,s as x,_ as j,v,$ as w,a1 as R}from"./dateViewRenderers-34586552.js";import{u as E}from"./useMediaQuery-6a073ac5.js";import{u as C,D as _,a as A}from"./DesktopDatePicker-07c19cde.js";import{r as F}from"./useSlotProps-e34e1e13.js";import{u as S}from"./useMobilePicker-9978caff.js";const m=b.forwardRef(function(n,t){var u,d;const s=k(),r=g(),o=C(n,"MuiMobileDatePicker"),l=a({day:i,month:i,year:i},o.viewRenderers),c=a({},o,{viewRenderers:l,format:M(r,o,!1),slots:a({field:_},o.slots),slotProps:a({},o.slotProps,{field:P=>{var f;return a({},F((f=o.slotProps)==null?void 0:f.field,P),T(o),{ref:t})},toolbar:a({hidden:!1},(u=o.slotProps)==null?void 0:u.toolbar)})}),{renderPicker:y}=S({props:c,valueManager:x,valueType:"date",getOpenDialogAriaText:j({utils:r,formatKey:"fullDate",contextTranslation:s.openDatePickerDialogue,propsTranslation:(d=c.localeText)==null?void 0:d.openDatePickerDialogue}),validator:v});return y()});m.propTypes={autoFocus:e.bool,className:e.string,closeOnSelect:e.bool,dayOfWeekFormatter:e.func,defaultValue:e.object,disabled:e.bool,disableFuture:e.bool,disableHighlightToday:e.bool,disableOpenPicker:e.bool,disablePast:e.bool,displayWeekNumber:e.bool,enableAccessibleFieldDOMStructure:e.any,fixedWeekNumber:e.number,format:e.string,formatDensity:e.oneOf(["dense","spacious"]),inputRef:w,label:e.node,loading:e.bool,localeText:e.object,maxDate:e.object,minDate:e.object,monthsPerRow:e.oneOf([3,4]),name:e.string,onAccept:e.func,onChange:e.func,onClose:e.func,onError:e.func,onMonthChange:e.func,onOpen:e.func,onSelectedSectionsChange:e.func,onViewChange:e.func,onYearChange:e.func,open:e.bool,openTo:e.oneOf(["day","month","year"]),orientation:e.oneOf(["landscape","portrait"]),readOnly:e.bool,reduceAnimations:e.bool,referenceDate:e.object,renderLoading:e.func,selectedSections:e.oneOfType([e.oneOf(["all","day","empty","hours","meridiem","minutes","month","seconds","weekDay","year"]),e.number]),shouldDisableDate:e.func,shouldDisableMonth:e.func,shouldDisableYear:e.func,showDaysOutsideCurrentMonth:e.bool,slotProps:e.object,slots:e.object,sx:e.oneOfType([e.arrayOf(e.oneOfType([e.func,e.object,e.bool])),e.func,e.object]),timezone:e.string,value:e.object,view:e.oneOf(["day","month","year"]),viewRenderers:e.shape({day:e.func,month:e.func,year:e.func}),views:e.arrayOf(e.oneOf(["day","month","year"]).isRequired),yearsOrder:e.oneOf(["asc","desc"]),yearsPerRow:e.oneOf([3,4])};const V=["desktopModeMediaQuery"],N=b.forwardRef(function(n,t){const s=h({props:n,name:"MuiDatePicker"}),{desktopModeMediaQuery:r=R}=s,o=O(s,V);return E(r,{defaultMatches:!0})?p.jsx(A,a({ref:t},o)):p.jsx(m,a({ref:t},o))});export{N as D};
