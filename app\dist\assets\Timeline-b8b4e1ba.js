import{cB as T,r,dr as f,cI as d,cE as a,o as l}from"./index-75c1660a.js";import{g,a as C,c as R,b as h}from"./clsx-a965ebfb.js";import{e as c,f as v}from"./TimelineSeparator-6e03ad1b.js";function w(o){return g("MuiTimeline",o)}C("MuiTimeline",["root","positionLeft","positionRight","positionAlternate","positionAlternateReverse"]);const y=["position","className"],M=o=>{const{position:e,classes:s}=o,t={root:["root",e&&c(e)]};return h(t,w,s)},j=T("ul",{name:"MuiTimeline",slot:"Root",overridesResolver:(o,e)=>{const{ownerState:s}=o;return[e.root,s.position&&e[c(s.position)]]}})({display:"flex",flexDirection:"column",padding:"6px 16px",flexGrow:1}),P=r.forwardRef(function(e,s){const t=f({props:e,name:"MuiTimeline"}),{position:i="right",className:m}=t,p=d(t,y),n=a({},t,{position:i}),u=M(n),x=r.useMemo(()=>({position:i}),[i]);return l.jsx(v.Provider,{value:x,children:l.jsx(j,a({className:R(u.root,m),ownerState:n,ref:s},p))})}),S=P;export{S as T};
