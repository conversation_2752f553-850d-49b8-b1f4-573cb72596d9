import{m as A,n as I,o as j,b as B,r as m,a as e,bn as h,gs as L,B as y,q as S,e as O,K as P,y as C,aj as U,j as c,G as W,bY as $,T as R,bK as q,aD as G,bL as H,bM as K,bN as M,bO as o,bP as V,x as z}from"./index-75c1660a.js";import{i as Y}from"./index-e2f5b037.js";import"./react-beautiful-dnd.esm-db50900e.js";import"./useMediaQuery-33e0a836.js";import"./DialogContentText-ef8524b5.js";import"./CardMedia-f3120f7c.js";import"./Container-754d6379.js";import"./InputAdornment-a22e1655.js";import"./ListItemButton-f13df81b.js";import"./Slider-c4e5ff46.js";import"./Stepper-2dbfb76b.js";import"./StepButton-e06eb73a.js";import"./ToggleButtonGroup-63ceda7a.js";import"./index-257abd9f.js";import"./toConsumableArray-42cf6573.js";import"./Check-1e790252.js";import"./clsx-a965ebfb.js";import"./Add-62a207fb.js";import"./DeleteOutline-a8808975.js";import"./Delete-1d158507.js";import"./asyncToGenerator-88583e02.js";import"./DeleteOutlineOutlined-fefa2376.js";import"./FileDownloadOutlined-329b8f56.js";import"./AddOutlined-0d3405f9.js";import"./EditOutlined-6971b85d.js";import"./Edit-77a8cc20.js";import"./index.esm-93e9b0e6.js";import"./makeStyles-c2a7efc7.js";import"./useSlotProps-da724f1f.js";import"./Settings-bf4ffef5.js";import"./VisibilityOutlined-a5a8c4d9.js";import"./index-19916fa2.js";import"./FiberManualRecord-1a0d6be5.js";import"./dayjs.min-83c0b0e0.js";import"./CheckBox-09a94074.js";import"./DeleteOutlined-fe5b7345.js";var x={},w=I;Object.defineProperty(x,"__esModule",{value:!0});var F=x.default=void 0,J=w(A()),Q=j;F=x.default=(0,J.default)((0,Q.jsx)("path",{d:"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75zM20.71 5.63l-2.34-2.34a.9959.9959 0 0 0-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83c.39-.39.39-1.02 0-1.41"}),"ModeEditOutline");const X=t=>{var u;const l=B(),g=[{Description:"",Name:"WorkRulesServices",URL:h},{Description:"",Name:"CW_Worktext",URL:h},{Description:"",Name:"WorkRuleEngineServices",URL:h},{Description:"",Name:"WorkUtilsServices",URL:h}],p=()=>{var d,a;({...t.DTdetails},t.setShowDT(!1)),l((a=(d=L)==null?void 0:d.BUSINESS_RULES)==null?void 0:a.AUTHORING)},b={headerColors_C:"#DFEAFB",headerColors_A:"#FFF7E2",headerColors_P:"#EEEEEE",headerColors_S:"#EEEEEE",textColor:"rgb(66, 66, 66)",hoveredTextColor:"rgb(25, 118, 210)",fontFamily:"inherit"},f=m.useMemo(()=>e(Y.Authoring,{colors:b,translationDataObjects:[],Dtdetails:t==null?void 0:t.DTdetails,destinations:g,NavBackHandler:p,dateFormat:"dd-MMM-yyyy",disableExistingRows:!1}),[(u=t.DTdetails)==null?void 0:u.DTid]);return e(y,{children:f})},Z=X;function Le(){const[t,l]=m.useState({}),[g,p]=m.useState(!1),[b,f]=m.useState([]),u="",d="baaf87db-3f78-44c8-8c78-32bec835f6ff",a=S(i=>i==null?void 0:i.userManagement),n=a==null?void 0:a.userData,s=S(i=>i.appSettings),{t:r}=O(),E=s==null?void 0:s.dateFormat,T=s==null?void 0:s.timeFormat;m.useEffect(()=>{P(h+`/v1/application/hierarchy?app=${d}&isAuthoring=true&mode=DT`,"get",N,_)},[]);const N=i=>{f(i.data[0].childs[0].childs);let D=i.data[0],k=D.childs[0];l({...t,RMSid:D.id,RSid:k.id})};let _=i=>{console.log("Error Fetching Filter Data in this API",i)};const v=i=>{l({userDetails:{displayName:n==null?void 0:n.displayName,emailId:n==null?void 0:n.emailId},RMSid:t==null?void 0:t.RMSid,RSid:t==null?void 0:t.RSid,DTid:i.id,applicationId:d,ruleName:i.name,version:i.version,token:u.replace("Bearer ","")}),p(!0)};return console.log(C(1702891324188).format(`${E},${T}`)),e("div",{style:{...U,backgroundColor:"#FAFCFF"},children:c(z,{spacing:1,children:[c(W,{item:!0,md:5,sx:$,children:[e(R,{variant:"h3",children:e("strong",{children:r("Authoring")})}),e(R,{variant:"body2",color:"#777",children:r("This view displays the list of rules set for the application")})]}),e(y,{className:"content",sx:{margin:"25px"},children:g?t&&e(Z,{DTdetails:t,setDTdetails:l,setShowDT:p}):e(q,{component:G,sx:{boxShadow:"none",border:"1px solid #e0e0e0"},children:c(H,{sx:{minWidth:650},"aria-label":"simple table",children:[e(K,{sx:{background:"#f5f5f5"},children:c(M,{sx:{"& .MuiTableCell-root":{height:"52px",padding:"0px 16px",fontWeight:600}},children:[e(o,{children:r("Decision Table Name")}),e(o,{align:"right",children:r("Version")}),e(o,{align:"right",children:r("Modified By")}),e(o,{align:"right",children:r("Modified On")}),e(o,{align:"right",children:r("Status")}),e(o,{align:"right",children:r("Action")})]})}),e(V,{children:b.map(i=>c(M,{sx:{"&:last-child td, &:last-child th":{border:0},cursor:"pointer","& .MuiTableCell-root":{borderBottom:"1px solid #e0e0e0 !important",height:"52px",padding:"0px 16px"}},onClick:()=>v(i),children:[e(o,{component:"th",scope:"row",children:i.name}),e(o,{align:"right",children:i.version}),e(o,{align:"right",children:i.updatedBy}),e(o,{align:"right",children:C(i.updatedOn).format(`${E},${T}`)}),e(o,{align:"right",children:i.status}),e(o,{align:"right",children:e(F,{onClick:()=>{v(i)}})})]},i.id))})]})})})]})})}export{Le as default};
