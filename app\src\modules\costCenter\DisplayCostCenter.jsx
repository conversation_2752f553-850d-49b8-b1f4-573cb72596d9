import {
  Backdrop,
  BottomNavigation,
  Box,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  FormControlLabel,
  Grid,
  IconButton,
  Link,
  Paper,
  Radio,
  RadioGroup,
  Stack,
  Step,
  StepLabel,
  <PERSON>per,
  Tab,
  Tabs,
  TextField,
  Typography,
} from "@mui/material";

import React, { useEffect, useState } from "react";
import {
  iconButton_SpacingSmall,
  button_Primary,
  container_Padding,
  button_Outlined,
  outermostContainer_Information,
} from "../../Common/commonStyles";
import CloseIcon from "@mui/icons-material/Close";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import { useLocation, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { doAjax } from "../../Common/fetchService";
import {
  destination_CostCenter_Mass,
  destination_DocumentManagement,
  destination_IDM,
} from "../../../destinationVariables";
import { setDropDown } from "../../../app/dropDownDataSlice";
import ReusableDialog from "../../Common/ReusableDialog";
import ReusableSnackBar from "../../Common/ReusableSnackBar";
import {
  clearCostCenter,
  setCCErrorFields,
  setCostCenterAddressTab,
  setCostCenterBasicDataTab,
  setCostCenterCommunicationTab,
  setCostCenterControlTab,
  setCostCenterHistoryTab,
  setCostCenterTemplatesTab,
  setCostCenterViewData,
  setSingleCostCenterPayload,
  setCCRequiredFieldsGI,
  setCCRequiredFields,
  setWholeData
} from "../../../app/costCenterTabsSlice";
import ReusableAttachementAndComments from "../../Common/ReusableAttachmentAndComments/ReusableAttachementAndComments";
import { formValidator, idGenerator,  checkIwaAccess } from "../../../functions";
import lookup from "../../../data/lookup.json";
import LoadingComponent from "../../Common/LoadingComponent";
import moment from "moment";
import LinkIcon from "@mui/icons-material/Link";
// import { setCCRequiredFields } from "../../../../app/costCenterTabsSlice";
import { setPayloadWhole,setGeneralInformation } from "../../../app/editPayloadSlice";

import ReusableTable from "../../EmailConfiguration/component/ReusableTable";
import {  
Timeline,
TimelineConnector,
TimelineContent,
TimelineDot,
TimelineItem,
TimelineSeparator,
timelineItemClasses, } from "@mui/lab";
import { CheckCircleOutlineOutlined } from "@mui/icons-material";
import { MatDownload, MatView } from "../../DocumentManagement/UtilDoc";
import TrackChangesTwoToneIcon from "@mui/icons-material/TrackChangesTwoTone";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import ChangeLog from "../../Changelog/ChangeLog";
import { clearGeneralLedger, clearSingleGLPayloadGI } from "../../../app/generalLedgerTabSlice";
import { clearCostCenterPayload, setSingleCostCenterETPayloadGI } from "../../../app/costCenterTabSliceET";
import { clearProfitCenterPayload } from "../../../app/profitCenterTabsSlice";




const DisplayCostCenter = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [dropDownData, setDropDownData] = useState({});
  const location = useLocation();
  const displayData = location.state;
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [messageDialogTitle, setMessageDialogTitle] = useState("");
  const [successMsg, setsuccessMsg] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [messageDialogSeverity, setMessageDialogSeverity] = useState(false);
  const [messageDialogOK, setMessageDialogOK] = useState(true);
  const [messageDialogExtra, setMessageDialogExtra] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [blurLoading, setBlurLoading] = useState(false);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [validateFlag, setValidateFlag] = useState(false);
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [openCorrectionDialog, setOpenCorrectionDialog] = useState(false);
  const [submitForReviewDisabled, setSubmitForReviewDisabled] = useState(true);
  const [remarks, setRemarks] = useState("");
  const [ccNumber, setCcNumber] = useState("");
  const [requestId, setRequestId] = useState("");
  const [handleExtrabutton, setHandleExtrabutton] = useState(false);
  const [handleExtraText, setHandleExtraText] = useState("");
  const [formValidationErrorItems, setFormValidationErrorItems] = useState([]);
  const [openSnackbarValidation, setOpenSnackbarValidation] = useState(false);
  const [testrunStatus, setTestrunStatus] = useState(true);
  const [openCreateDialog, setOpenCreateDialog] = useState(false);
  const [ruleData, setRuleData] = useState([]);
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const appSettings = useSelector((state) => state.appSettings);
  // const [questions, setQuestions] = useState([]);
  const [attachmentsData, setAttachmentsData] = useState([]);
  const [tabsArray, setTabsArray] = useState([]);
  const [iDs, setIds] = useState();
  const [costCenterDetails, setCostCenterDetails] = useState([]);
  const [factorsArray, setFactorsArray] = useState([]);
  const artifactId = useSelector((state) => state.initialData.ArtifactId);
  const [costCenterNameinRow, setCostCenterNameinRow] = useState([]);
  const [isEditMode, setIsEditMode] = useState(false);
  const distinctArtifactId = [...new Set(artifactId)];
  const [dispCompCode, setDispCompCode] = useState([]);
  const [attachments, setAttachments] = useState([]);
  const [comments, setComments] = useState([]);
  const [isChangeLogopen, setisChangeLogopen] = useState(false);
  const [openRemarkDialog, setOpenRemarkDialog] = useState(false);
  const [isDisplayMode, setIsDisplayMode] = useState(true);
  const [errorFields, setErrorsFields] = useState("");
  const [isRolePresent, setIsRolePresent] = useState(false);
  console.log(distinctArtifactId, "distinctArtifactId");
  console.log("tabsarray", tabsArray);
  console.log("displayData", displayData);
  console.log("factorsArray", factorsArray);
  console.log("idsresonse", iDs);
  console.log("isEditMode", isEditMode);

  let demoData ={
    "statusCode": 200,
    "message": "Display Cost Center fetched succesfully",
    "body": {
        "General Information": null,
        "viewData": {
            "General Information": "",
            "Basic Data": {
                "Names": [
                    {
                        "fieldName": "Short Description",
                        "sequenceNo": 1,
                        "fieldType": "Input",
                        "maxLength": 20,
                        "dataType": "String",
                        "viewName": "Basic Data",
                        "cardName": "Names",
                        "cardSeq": 1,
                        "visibility": "Display",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": "STA0422115"
                    },
                    {
                        "fieldName": "Long Description",
                        "sequenceNo": 2,
                        "fieldType": "Input",
                        "maxLength": 40,
                        "dataType": "String",
                        "viewName": "Basic Data",
                        "cardName": "Names",
                        "cardSeq": 1,
                        "visibility": "Display",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": "LNA0422115"
                    }
                ],
                "Basic Data": [
                    {
                        "fieldName": "CC User Responsible",
                        "sequenceNo": 1,
                        "fieldType": "Input",
                        "maxLength": 12,
                        "dataType": "String",
                        "viewName": "Basic Data",
                        "cardName": "Basic Data",
                        "cardSeq": 2,
                        "visibility": "Display",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": ""
                    },
                    {
                        "fieldName": "CC Person Responsible",
                        "sequenceNo": 2,
                        "fieldType": "Input",
                        "maxLength": 20,
                        "dataType": "String",
                        "viewName": "Basic Data",
                        "cardName": "Basic Data",
                        "cardSeq": 2,
                        "visibility": "Display",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": "VTJENNINGS"
                    },
                    {
                        "fieldName": "CC Department",
                        "sequenceNo": 3,
                        "fieldType": "Input",
                        "maxLength": 12,
                        "dataType": "String",
                        "viewName": "Basic Data",
                        "cardName": "Basic Data",
                        "cardSeq": 2,
                        "visibility": "Hidden",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": ""
                    },
                    {
                        "fieldName": "Cost Center Category",
                        "sequenceNo": 4,
                        "fieldType": "Drop Down",
                        "maxLength": 1,
                        "dataType": "String",
                        "viewName": "Basic Data",
                        "cardName": "Basic Data",
                        "cardSeq": 2,
                        "visibility": "Display",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": "O"
                    },
                    {
                        "fieldName": "Comp Code",
                        "sequenceNo": 5,
                        "fieldType": "Drop Down",
                        "maxLength": 4,
                        "dataType": "String",
                        "viewName": "Basic Data",
                        "cardName": "Basic Data",
                        "cardSeq": 2,
                        "visibility": "Display",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": "2015"
                    },
                    {
                        "fieldName": "Hierarchy Area",
                        "sequenceNo": 6,
                        "fieldType": "Drop Down",
                        "maxLength": 12,
                        "dataType": "String",
                        "viewName": "Basic Data",
                        "cardName": "Basic Data",
                        "cardSeq": 2,
                        "visibility": "Display",
                        "defaultValue": "ET_CCA",
                        "oldValue": "",
                        "value": "ET_CCA"
                    },
                    {
                        "fieldName": "Business Area",
                        "sequenceNo": 7,
                        "fieldType": "Drop Down",
                        "maxLength": 4,
                        "dataType": "String",
                        "viewName": "Basic Data",
                        "cardName": "Basic Data",
                        "cardSeq": 2,
                        "visibility": "Hidden",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": ""
                    },
                    {
                        "fieldName": "Functional Area",
                        "sequenceNo": 8,
                        "fieldType": "Drop Down",
                        "maxLength": 4,
                        "dataType": "String",
                        "viewName": "Basic Data",
                        "cardName": "Basic Data",
                        "cardSeq": 2,
                        "visibility": "Display",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": ""
                    },
                    {
                        "fieldName": "Currency",
                        "sequenceNo": 9,
                        "fieldType": "Drop Down",
                        "maxLength": 5,
                        "dataType": "String",
                        "viewName": "Basic Data",
                        "cardName": "Basic Data",
                        "cardSeq": 2,
                        "visibility": "Display",
                        "defaultValue": "USD",
                        "oldValue": "",
                        "value": "USD"
                    },
                    {
                        "fieldName": "Profit Center",
                        "sequenceNo": 10,
                        "fieldType": "Drop Down",
                        "maxLength": 10,
                        "dataType": "String",
                        "viewName": "Basic Data",
                        "cardName": "Basic Data",
                        "cardSeq": 2,
                        "visibility": "Display",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": "P201510000"
                    }
                ]
            },
            "Control": {
                "Control": [
                    {
                        "fieldName": "Record Quantity",
                        "sequenceNo": 1,
                        "fieldType": "Radio Button",
                        "maxLength": 1,
                        "dataType": "Boolean",
                        "viewName": "Control",
                        "cardName": "Control",
                        "cardSeq": 1,
                        "visibility": "Hidden",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": false
                    },
                    {
                        "fieldName": "Actual Primary Costs",
                        "sequenceNo": 2,
                        "fieldType": "Radio Button",
                        "maxLength": 1,
                        "dataType": "Boolean",
                        "viewName": "Control",
                        "cardName": "Control",
                        "cardSeq": 1,
                        "visibility": "Display",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": false
                    },
                    {
                        "fieldName": "Plan Primary Costs",
                        "sequenceNo": 3,
                        "fieldType": "Radio Button",
                        "maxLength": 1,
                        "dataType": "Boolean",
                        "viewName": "Control",
                        "cardName": "Control",
                        "cardSeq": 1,
                        "visibility": "Display",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": false
                    },
                    {
                        "fieldName": "Commitment Update",
                        "sequenceNo": 4,
                        "fieldType": "Radio Button",
                        "maxLength": 1,
                        "dataType": "Boolean",
                        "viewName": "Control",
                        "cardName": "Control",
                        "cardSeq": 1,
                        "visibility": "Display",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": false
                    },
                    {
                        "fieldName": "Act. Secondary Costs",
                        "sequenceNo": 5,
                        "fieldType": "Radio Button",
                        "maxLength": 1,
                        "dataType": "Boolean",
                        "viewName": "Control",
                        "cardName": "Control",
                        "cardSeq": 1,
                        "visibility": "Display",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": false
                    },
                    {
                        "fieldName": "Plan Secondary Costs",
                        "sequenceNo": 6,
                        "fieldType": "Radio Button",
                        "maxLength": 1,
                        "dataType": "Boolean",
                        "viewName": "Control",
                        "cardName": "Control",
                        "cardSeq": 1,
                        "visibility": "Display",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": false
                    },
                    {
                        "fieldName": "Actual Revenue",
                        "sequenceNo": 7,
                        "fieldType": "Radio Button",
                        "maxLength": 1,
                        "dataType": "Boolean",
                        "viewName": "Control",
                        "cardName": "Control",
                        "cardSeq": 1,
                        "visibility": "Display",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": true
                    },
                    {
                        "fieldName": "Plan Revenue",
                        "sequenceNo": 8,
                        "fieldType": "Radio Button",
                        "maxLength": 1,
                        "dataType": "Boolean",
                        "viewName": "Control",
                        "cardName": "Control",
                        "cardSeq": 1,
                        "visibility": "Display",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": true
                    }
                ]
            },
            "Templates": {
                "Formula planning": [
                    {
                        "fieldName": "Acty-Indep. Form Plng Temp",
                        "sequenceNo": 1,
                        "fieldType": "Drop Down",
                        "maxLength": 10,
                        "dataType": "String",
                        "viewName": "Templates",
                        "cardName": "Formula planning",
                        "cardSeq": 1,
                        "visibility": "Hidden",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": ""
                    },
                    {
                        "fieldName": "Acty-Dep. Form Plng Temp",
                        "sequenceNo": 2,
                        "fieldType": "Drop Down",
                        "maxLength": 10,
                        "dataType": "String",
                        "viewName": "Templates",
                        "cardName": "Formula planning",
                        "cardSeq": 1,
                        "visibility": "Hidden",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": ""
                    }
                ],
                "Activity and Business Process Allocation": [
                    {
                        "fieldName": "Acty-Indep. Alloc Temp",
                        "sequenceNo": 1,
                        "fieldType": "Drop Down",
                        "maxLength": 10,
                        "dataType": "String",
                        "viewName": "Templates",
                        "cardName": "Activity and Business Process Allocation",
                        "cardSeq": 2,
                        "visibility": "Hidden",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": ""
                    },
                    {
                        "fieldName": "Acty-Dep. Alloc Template",
                        "sequenceNo": 2,
                        "fieldType": "Drop Down",
                        "maxLength": 10,
                        "dataType": "String",
                        "viewName": "Templates",
                        "cardName": "Activity and Business Process Allocation",
                        "cardSeq": 2,
                        "visibility": "Hidden",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": ""
                    }
                ],
                "Actual Statistical Key Figures": [
                    {
                        "fieldName": "Templ.: Act. Stat. Key Figure",
                        "sequenceNo": 1,
                        "fieldType": "Drop Down",
                        "maxLength": 10,
                        "dataType": "String",
                        "viewName": "Templates",
                        "cardName": "Actual Statistical Key Figures",
                        "cardSeq": 3,
                        "visibility": "Hidden",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": ""
                    },
                    {
                        "fieldName": "Templ.: Act. Stat. Key Figure",
                        "sequenceNo": 2,
                        "fieldType": "Drop Down",
                        "maxLength": 10,
                        "dataType": "String",
                        "viewName": "Templates",
                        "cardName": "Actual Statistical Key Figures",
                        "cardSeq": 3,
                        "visibility": "Hidden",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": ""
                    },
                    {
                        "fieldName": "Costing Sheet",
                        "sequenceNo": 3,
                        "fieldType": "Drop Down",
                        "maxLength": 6,
                        "dataType": "String",
                        "viewName": "Templates",
                        "cardName": "Actual Statistical Key Figures",
                        "cardSeq": 3,
                        "visibility": "Hidden",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": ""
                    }
                ]
            },
            "Address": {
                "Address Data": [
                    {
                        "fieldName": "Country/Reg",
                        "sequenceNo": 1,
                        "fieldType": "Drop Down",
                        "maxLength": 3,
                        "dataType": "String",
                        "viewName": "Address",
                        "cardName": "Address Data",
                        "cardSeq": 1,
                        "visibility": "Display",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": "US"
                    },
                    {
                        "fieldName": "Street",
                        "sequenceNo": 2,
                        "fieldType": "Input",
                        "maxLength": 35,
                        "dataType": "String",
                        "viewName": "Address",
                        "cardName": "Address Data",
                        "cardSeq": 1,
                        "visibility": "Display",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": "*** *** ***"
                    },
                    {
                        "fieldName": "Location",
                        "sequenceNo": 3,
                        "fieldType": "Input",
                        "maxLength": 35,
                        "dataType": "String",
                        "viewName": "Address",
                        "cardName": "Address Data",
                        "cardSeq": 1,
                        "visibility": "Display",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": "HOUSTON"
                    },
                    {
                        "fieldName": "Postal Code",
                        "sequenceNo": 4,
                        "fieldType": "Input",
                        "maxLength": 10,
                        "dataType": "String",
                        "viewName": "Address",
                        "cardName": "Address Data",
                        "cardSeq": 1,
                        "visibility": "Display",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": "77002-6803"
                    },
                    {
                        "fieldName": "Region",
                        "sequenceNo": 5,
                        "fieldType": "Drop Down",
                        "maxLength": 3,
                        "dataType": "String",
                        "viewName": "Address",
                        "cardName": "Address Data",
                        "cardSeq": 1,
                        "visibility": "Display",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": "TX"
                    },
                    {
                        "fieldName": "Name 1",
                        "sequenceNo": 6,
                        "fieldType": "Input",
                        "maxLength": 35,
                        "dataType": "String",
                        "viewName": "Address",
                        "cardName": "Address Data",
                        "cardSeq": 1,
                        "visibility": "Display",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": "*** *** ***"
                    },
                    {
                        "fieldName": "Name 2",
                        "sequenceNo": 7,
                        "fieldType": "Input",
                        "maxLength": 35,
                        "dataType": "String",
                        "viewName": "Address",
                        "cardName": "Address Data",
                        "cardSeq": 1,
                        "visibility": "Display",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": "*** *** ***"
                    },
                    {
                        "fieldName": "Name 3",
                        "sequenceNo": 8,
                        "fieldType": "Input",
                        "maxLength": 35,
                        "dataType": "String",
                        "viewName": "Address",
                        "cardName": "Address Data",
                        "cardSeq": 1,
                        "visibility": "Display",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": "*** *** ***"
                    },
                    {
                        "fieldName": "Name 4",
                        "sequenceNo": 9,
                        "fieldType": "Input",
                        "maxLength": 35,
                        "dataType": "String",
                        "viewName": "Address",
                        "cardName": "Address Data",
                        "cardSeq": 1,
                        "visibility": "Display",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": "*** *** ***"
                    },
                    {
                        "fieldName": "Title",
                        "sequenceNo": 10,
                        "fieldType": "Input",
                        "maxLength": 15,
                        "dataType": "String",
                        "viewName": "Address",
                        "cardName": "Address Data",
                        "cardSeq": 1,
                        "visibility": "Hidden",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": ""
                    },
                    {
                        "fieldName": "District",
                        "sequenceNo": 11,
                        "fieldType": "Input",
                        "maxLength": 35,
                        "dataType": "String",
                        "viewName": "Address",
                        "cardName": "Address Data",
                        "cardSeq": 1,
                        "visibility": "Display",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": ""
                    },
                    {
                        "fieldName": "Jurisdiction",
                        "sequenceNo": 12,
                        "fieldType": "Drop Down",
                        "maxLength": 15,
                        "dataType": "String",
                        "viewName": "Address",
                        "cardName": "Address Data",
                        "cardSeq": 1,
                        "visibility": "Display",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": "4420114400"
                    },
                    {
                        "fieldName": "PO Box Post Cod",
                        "sequenceNo": 13,
                        "fieldType": "Input",
                        "maxLength": 10,
                        "dataType": "String",
                        "viewName": "Address",
                        "cardName": "Address Data",
                        "cardSeq": 1,
                        "visibility": "Hidden",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": ""
                    },
                    {
                        "fieldName": "PO Box",
                        "sequenceNo": 14,
                        "fieldType": "Input",
                        "maxLength": 10,
                        "dataType": "String",
                        "viewName": "Address",
                        "cardName": "Address Data",
                        "cardSeq": 1,
                        "visibility": "Hidden",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": ""
                    }
                ]
            },
            "Communication": {
                "Communication Data": [
                    {
                        "fieldName": "Language Key",
                        "sequenceNo": 1,
                        "fieldType": "Drop Down",
                        "maxLength": 2,
                        "dataType": "String",
                        "viewName": "Communication",
                        "cardName": "Communication Data",
                        "cardSeq": 1,
                        "visibility": "Hidden",
                        "defaultValue": "EN",
                        "oldValue": "",
                        "value": "EN"
                    },
                    {
                        "fieldName": "Telephone 1",
                        "sequenceNo": 2,
                        "fieldType": "Input",
                        "maxLength": 16,
                        "dataType": "String",
                        "viewName": "Communication",
                        "cardName": "Communication Data",
                        "cardSeq": 1,
                        "visibility": "Hidden",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": ""
                    },
                    {
                        "fieldName": "Telephone 2",
                        "sequenceNo": 3,
                        "fieldType": "Input",
                        "maxLength": 16,
                        "dataType": "String",
                        "viewName": "Communication",
                        "cardName": "Communication Data",
                        "cardSeq": 1,
                        "visibility": "Hidden",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": ""
                    },
                    {
                        "fieldName": "Telebox Number",
                        "sequenceNo": 4,
                        "fieldType": "Input",
                        "maxLength": 15,
                        "dataType": "String",
                        "viewName": "Communication",
                        "cardName": "Communication Data",
                        "cardSeq": 1,
                        "visibility": "Hidden",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": ""
                    },
                    {
                        "fieldName": "Telex Number",
                        "sequenceNo": 5,
                        "fieldType": "Input",
                        "maxLength": 30,
                        "dataType": "String",
                        "viewName": "Communication",
                        "cardName": "Communication Data",
                        "cardSeq": 1,
                        "visibility": "Hidden",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": ""
                    },
                    {
                        "fieldName": "Fax Number",
                        "sequenceNo": 6,
                        "fieldType": "Input",
                        "maxLength": 31,
                        "dataType": "String",
                        "viewName": "Communication",
                        "cardName": "Communication Data",
                        "cardSeq": 1,
                        "visibility": "Hidden",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": ""
                    },
                    {
                        "fieldName": "Teletex Number",
                        "sequenceNo": 7,
                        "fieldType": "Input",
                        "maxLength": 30,
                        "dataType": "String",
                        "viewName": "Communication",
                        "cardName": "Communication Data",
                        "cardSeq": 1,
                        "visibility": "Hidden",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": ""
                    },
                    {
                        "fieldName": "Printer Destination",
                        "sequenceNo": 8,
                        "fieldType": "Input",
                        "maxLength": 4,
                        "dataType": "String",
                        "viewName": "Communication",
                        "cardName": "Communication Data",
                        "cardSeq": 1,
                        "visibility": "Hidden",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": ""
                    },
                    {
                        "fieldName": "Data Line",
                        "sequenceNo": 9,
                        "fieldType": "Input",
                        "maxLength": 14,
                        "dataType": "String",
                        "viewName": "Communication",
                        "cardName": "Communication Data",
                        "cardSeq": 1,
                        "visibility": "Hidden",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": ""
                    }
                ]
            },
            "Additional Data": {
                "Additional Data": [
                    {
                        "fieldName": "FERC Indicator",
                        "sequenceNo": 1,
                        "fieldType": "Drop Down",
                        "maxLength": 20,
                        "dataType": "String",
                        "viewName": "Additional Data",
                        "cardName": "Additional Data",
                        "cardSeq": 1,
                        "visibility": "Display",
                        "defaultValue": "",
                        "oldValue": "",
                        "value": "NO REG"
                    }
                ]
            },
            "Attachments & Comments": ""
        },
        "controllingArea": "ETCA",
        "costCenterHeaderId": null,
        "costCenterErrorId": 0,
        "costCenter": "2015942114",
        "costCenterId": null,
        "validFrom": "/Date(-2208988800000)/",
        "validTo": "/Date(253402214400000)/",
        "Info": null,
        "IsRequestLvlErrorPresent": false,
        "IsObjectLvlErrorPresent": false,
        "generalInfoId": null,
        "TotalIntermediateTasks": 0,
        "requestStatus": "Approved"
    },
    "screenName": "Display"
  } 
 
  const questions = useSelector((state) => state.edit.generalInformation);
  // const questions = useSelector((state) => state.costCenterET.singleETCCPayloadGI);
  let requiredFieldsGI = useSelector(
    (state) => state.costCenter.requiredFieldsGI
  );
  const singleCCPayload = useSelector(
    (state) => state.costCenter.singleCCPayload
  );
  const basicDataTabDetails = useSelector(
    (state) => state.costCenter.costCenterBasicData
  );
  const controlTabDetails = useSelector(
    (state) => state.costCenter.costCenterControl
  );
  const templatesTabDetails = useSelector(
    (state) => state.costCenter.costCenterTemplate
  );
  const addressTabDetails = useSelector(
    (state) => state.costCenter.costCenterAddress
  );
  const communicationTabDetails = useSelector(
    (state) => state.costCenter.costCenterCommunication
  );
  const historyTabDetails = useSelector(
    (state) => state.costCenter.costCenterHistory
  );
  const requiredFields = useSelector(
    (state) => state.costCenter.requiredFields
  );
  let singleCCPayloadAfterChange = useSelector((state) => state.edit.payload);
  console.log("controlTabDetails", singleCCPayload);
  console.log("singleCCPayloadAfterChange", singleCCPayloadAfterChange);
  let userData = useSelector((state) => state?.userManagement?.userData);
let taskData = useSelector((state) => state?.initialData?.IWMMyTask);
let costCenterRowData = location.state;
let taskRowDetails = useSelector((state) => state?.userManagement?.taskData);
  const [activeStep, setActiveStep] = useState(0);
  const payloadFields = useSelector(
    (state) => state.costCenter.singleCCPayload
  );
  const costCenterViewData = useSelector(
    (state) => state.costCenter.costCenterViewData
  );
  let iwaAccessData = useSelector(
    (state) => state?.userManagement?.entitiesAndActivities?.["Cost Center"]
  );
  let singleGLPayloadMain = useSelector((state) => state?.costCenterET?.singleETCCPayloadGI)

  const maskingRoleIDM = useSelector(
    (state) => state.applicationConfig.maskingRolesIDM
  );
  const maskingRoleApplication = useSelector(
    (state) => state.applicationConfig.maskingRolesApplication
  );


  const editedData = useSelector((state) => state.edit.payload);
  let selectedControllingArea = displayData?.controllingAreaData;
  const steps = ["General Information"];
  const stepsAttachments = ["Attachments & Comments"];
  const dynamicSteps = [...steps, ...tabsArray, ...stepsAttachments];
  const [pageName, setPageName] = useState("");
  console.log("fhskthrkdhtr", costCenterViewData);






  const handleDropDownChangeGIQuestion = (event,newValue,type,label) => {


    dispatch(
      setSingleCostCenterETPayloadGI({
        keyName: label
          .replaceAll("(", "")
          .replaceAll(")", "")
          .replaceAll("/", "")
          .replaceAll("-", "")
          .replaceAll(".", "")
          .split(" ")
          .join(""),
        data: newValue?.MDG_LOOKUP_CODE || ''
      })
    )
  };
  const handleRadioChangeGIQuestion = (event) => {


    dispatch(
      setSingleCostCenterETPayloadGI({
        keyName: event.target.name
          .replaceAll("(", "")
          .replaceAll(")", "")
          .replaceAll("/", "")
          .replaceAll("-", "")
          .replaceAll(".", "")
          .split(" ")
          .join(""),
        data: event.target.value,
      })
    )
  };
  const handleRadioChangeGIQuestionTextField = (event) => {


    dispatch(
      setSingleCostCenterETPayloadGI({
        keyName: event.target.name
          .replaceAll("(", "")
          .replaceAll(")", "")
          .replaceAll("/", "")
          .replaceAll("-", "")
          .replaceAll(".", "")
          .split(" ")
          .join(""),
        data: event.target.value.toUpperCase(),
      })
    )
  };

  var payload = {
    IsSunoco: taskData?.body?.isSunoco==="true"?true:false,
    IsSunocoCCPC: taskData?.body?.isSunocoCCPC==="true"?true:false,
    TaskId: taskRowDetails?.taskId ? taskRowDetails?.taskId : "",
    CostCenterHeaderID: iDs?.costCenterHeaderId?iDs?.costCenterHeaderId:"",
    ControllingArea: taskData?.controllingArea?taskData?.controllingArea:"",
    // displayData?.controllingAreaData ?? "",
    Testrun: testrunStatus,
    Action: "I",
    Remarks: remarks ? remarks : "",
    ReqCreatedBy: taskRowDetails?.createdBy ? taskRowDetails?.createdBy : userData?.emailId? userData?.emailId : "",
    ReqCreatedOn: taskData?.body?.creationDate
    ? taskData?.body?.creationDate
    : costCenterRowData?.reqCreatedOn
    ? costCenterRowData?.reqCreatedOn
    : "",
    RequestStatus:taskData?.body?.reqStatus?taskData?.body?.reqStatus:"",
    CreationId:
    taskRowDetails?.requestType === "Create"
      ? taskRowDetails?.requestId.slice(8)
      : costCenterRowData?.requestType === "Create"
      ? costCenterRowData?.requestId.slice(8)
      : "",
    EditId:  taskRowDetails?.requestType === "Change"
    ? taskRowDetails?.requestId.slice(8)
    : costCenterRowData?.requestType === "Change"
    ? costCenterRowData?.requestId.slice(8)
    : "",
    DeleteId: "",
    MassCreationId: "",
    MassEditId: "",
    MassDeleteId: "",
    RequestType: "",
    MassRequestStatus: "",
    GeneralInfoID: "",


    RequestPriority: iDs?.["General Information"]?.["Choose Priority Level"]?iDs?.["General Information"]?.["Choose Priority Level"]:"",
    BusinessJustification: iDs?.["General Information"]?.["Business Justification"]?iDs?.["General Information"]?.["Business Justification"]:"",
    //  singleCCPayloadAfterChange?.BusinessJustification?singleCCPayloadAfterChange?.BusinessJustification:"",
    SAPorJEErrorCheck: iDs?.["Did you receive a SAP or JE Error?"]==="true"?true:false,
    FERCIndicator: iDs?.["Additional Data"]?.["FERC Indicator"]?iDs?.["Additional Data"]?.["FERC Indicator"]:"",
    BusinessSegment: iDs?.["General Information"]?.["Business Segment"]?iDs?.["General Information"]?.["Business Segment"]:"",
    HierarchyRegion: iDs?.["General Information"]?.["Hierarchy Region"]?.code ?iDs?.["General Information"]?.["Hierarchy Region"]?.code :"",
    AAMNumber: iDs?.["General Information"]?.["AAM Number"]?iDs?.["General Information"]?.["AAM Number"]:"",

    
    Toitem: [
      {
        CostCenterID: iDs?.costCenterId?iDs?.costCenterId:taskData?.body?.id?taskData?.body?.id:"",
        Costcenter: taskData?.body?.costCenter ? taskData?.body?.costCenter:"",
        ValidFrom: payloadFields?.ValidFrom ? "/Date(" +
            Date.parse(payloadFields?.ValidFrom) +
            ")/"
          : "",
        ValidTo: payloadFields?.ValidTo ? "/Date(" +
        Date.parse(payloadFields?.ValidTo) +
        ")/"
      : "",
        //   ValidfromDate: singleCCPayload?.ValidFrom
        //   ? `/Date(${moment(singleCCPayload?.ValidFrom,"DD/MM/YYYY").unix()})/`
        //   : "",
        // ValidtoDate: singleCCPayload?.ValidTo
        //   ? `/Date(${moment(singleCCPayload?.ValidTo,"DD/MM/YYYY").unix()})/`
        //   : "",
        PersonInCharge: singleCCPayloadAfterChange?.CCPersonResponsible?singleCCPayloadAfterChange?.CCPersonResponsible:"",
        CostcenterType: singleCCPayloadAfterChange?.CostCenterCategory?singleCCPayloadAfterChange?.CostCenterCategory:"",
        // CostctrHierGrp: singleCCPayload?.HierarchyArea?.code
        //   ? singleCCPayload?.HierarchyArea?.code
        //   : "",
        CostctrHierGrp: singleCCPayloadAfterChange?.HierarchyArea?singleCCPayloadAfterChange?.HierarchyArea:"",
        BusArea: singleCCPayloadAfterChange?.BusinessArea?singleCCPayloadAfterChange?.BusinessArea:"",
        CompCode: singleCCPayloadAfterChange?.CompCode?singleCCPayloadAfterChange?.CompCode:"",
        Currency: singleCCPayloadAfterChange?.Currency?singleCCPayloadAfterChange?.Currency:"",
        ProfitCtr: singleCCPayloadAfterChange?.ProfitCenter?singleCCPayloadAfterChange?.ProfitCenter:"",
        Name: singleCCPayloadAfterChange?.ShortDescription?singleCCPayloadAfterChange?.ShortDescription:"",
        Descript: singleCCPayloadAfterChange?.LongDescription?singleCCPayloadAfterChange?.LongDescription:"",
        PersonInChargeUser: singleCCPayloadAfterChange?.CCUserResponsible?singleCCPayloadAfterChange?.CCUserResponsible:"",
        // PersonInChargeUser: singleCCPayload?.PersonResponsible?singleCCPayload?.PersonResponsible:"" ,
        RecordQuantity: singleCCPayloadAfterChange?.RecordQuantity===true?"X":"",
        LockIndActualPrimaryCosts: singleCCPayloadAfterChange?.ActualPrimaryCosts===true?"X":"",
        // singleCCPayload?.ActualPrimaryCosts === true ? "X" : "",
        LockIndPlanPrimaryCosts: singleCCPayloadAfterChange?.PlanPrimaryCosts===true?"X":"",
        // singleCCPayload?.PlanPrimaryCosts === true ? "X" : "",
        LockIndActSecondaryCosts: singleCCPayloadAfterChange?.ActSecondaryCosts===true?"X":"",
        // singleCCPayload?.ActsecondaryCosts === true ? "X" : "",
        LockIndPlanSecondaryCosts: singleCCPayloadAfterChange?.PlanSecondaryCosts===true?"X":"",
        // singleCCPayload?.PlanSecondaryCosts === true ? "X" : "",
        LockIndActualRevenues: singleCCPayloadAfterChange?.ActualRevenue===true?"X":"",
        // singleCCPayload?.ActualRevenue === true ? "X" : "",
        LockIndPlanRevenues: singleCCPayloadAfterChange?.PlanRevenue===true?"X":"",
        // singleCCPayload?.PlanRevenue === true ? "X" : "",
        LockIndCommitmentUpdate:singleCCPayloadAfterChange?.CommitmentUpdate===true?"X":"",
        // RecordQuantity: singleCCPayload?.RecordQuantity?singleCCPayload?.RecordQuantity:"false" ,
        // LockIndActualPrimaryCosts: singleCCPayload?.ActualPrimaryCosts?singleCCPayload?.ActualPrimaryCosts:"false" ,
        // LockIndPlanPrimaryCosts: singleCCPayload?.PlanPrimaryCosts?singleCCPayload?.PlanPrimaryCosts:"false" ,
        // LockIndActSecondaryCosts: singleCCPayload?.ActsecondaryCosts?singleCCPayload?.ActsecondaryCosts:"false" ,
        // LockIndPlanSecondaryCosts: singleCCPayload?.PlanSecondaryCosts?singleCCPayload?.PlanSecondaryCosts:"false" ,
        // LockIndActualRevenues: singleCCPayload?.ActualRevenue?singleCCPayload?.ActualRevenue:"false" ,
        // LockIndPlanRevenues: singleCCPayload?.PlanRevenue?singleCCPayload?.PlanRevenue:"false" ,
        // LockIndCommitmentUpdate:singleCCPayload?.CommitmentUpdate?singleCCPayload?.CommitmentUpdate:"false" ,
        ConditionTableUsage: singleCCPayloadAfterChange?.ConditionTableUsage?singleCCPayloadAfterChange?.ConditionTableUsage:"",
        Application: singleCCPayloadAfterChange?.Application?singleCCPayloadAfterChange?.Application:"",
        CstgSheet: singleCCPayloadAfterChange?.CostingSheet?singleCCPayloadAfterChange?.CostingSheet:"",
        ActyIndepTemplate: singleCCPayloadAfterChange?.ActyIndepAllocTemp?singleCCPayloadAfterChange?.ActyIndepAllocTemp:"",
        ActyDepTemplate: singleCCPayloadAfterChange?.ActyDepAllocTemplate?singleCCPayloadAfterChange?.ActyDepAllocTemplate:"",
        AddrTitle: singleCCPayloadAfterChange?.Title?singleCCPayloadAfterChange?.Title:"",
        AddrName1: singleCCPayloadAfterChange?.Name1?singleCCPayloadAfterChange?.Name1:"",
        AddrName2: singleCCPayloadAfterChange?.Name2?singleCCPayloadAfterChange?.Name2:"",
        AddrName3: singleCCPayloadAfterChange?.Name3?singleCCPayloadAfterChange?.Name3:"",
        AddrName4: singleCCPayloadAfterChange?.Name4?singleCCPayloadAfterChange?.Name4:"",
        AddrStreet: singleCCPayloadAfterChange?.Street?singleCCPayloadAfterChange?.Street:"",
        AddrCity: singleCCPayloadAfterChange?.Location?singleCCPayloadAfterChange?.Location:"",
        AddrDistrict: singleCCPayloadAfterChange?.District?singleCCPayloadAfterChange?.District:"",
        AddrCountry: singleCCPayloadAfterChange?.CountryReg?singleCCPayloadAfterChange?.CountryReg:"",
        AddrCountryIso: singleCCPayloadAfterChange?.AddrCountryIso?singleCCPayloadAfterChange?.AddrCountryIso:"",
        AddrTaxjurcode: singleCCPayloadAfterChange?.AddrTaxjurcode?singleCCPayloadAfterChange?.AddrTaxjurcode:"",
        AddrPoBox: singleCCPayloadAfterChange?.POBox?singleCCPayloadAfterChange?.POBox:"",
        AddrPostlCode: singleCCPayloadAfterChange?.PostalCode?singleCCPayloadAfterChange?.PostalCode:"",
        AddrPobxPcd: singleCCPayloadAfterChange?.POBoxPostCod?singleCCPayloadAfterChange?.POBoxPostCod:"",
        AddrRegion: singleCCPayloadAfterChange?.Region?singleCCPayloadAfterChange?.Region:"",
        TelcoLangu: singleCCPayloadAfterChange?.TelcoLangu?singleCCPayloadAfterChange?.TelcoLangu:"",
        // TelcoLanguIso: singleCCPayload?.LanguageKey?.code
        //   ? singleCCPayload?.LanguageKey?.code
        //   : "",
        TelcoLanguIso: singleCCPayloadAfterChange?.LanguageKey?singleCCPayloadAfterChange?.LanguageKey:"",
        TelcoTelephone: singleCCPayloadAfterChange?.Telephone1?singleCCPayloadAfterChange?.Telephone1:"",
        TelcoTelephone2: singleCCPayloadAfterChange?.Telephone2?singleCCPayloadAfterChange?.Telephone2:"",
        TelcoTelebox: singleCCPayloadAfterChange?.TeleboxNumber?singleCCPayloadAfterChange?.TeleboxNumber:"",
        TelcoTelex: singleCCPayloadAfterChange?.TelexNumber?singleCCPayloadAfterChange?.TelexNumber:"",
        TelcoFaxNumber: singleCCPayloadAfterChange?.FaxNumber?singleCCPayloadAfterChange?.FaxNumber:"",
        TelcoTeletex: singleCCPayloadAfterChange?.TeletexNumber?singleCCPayloadAfterChange?.TeletexNumber:"",
        TelcoPrinter: singleCCPayloadAfterChange?.PrinterDestination?singleCCPayloadAfterChange?.PrinterDestination:"",
        TelcoDataLine: singleCCPayloadAfterChange?.DataLine?singleCCPayloadAfterChange?.DataLine:"",
        ActyDepTemplateAllocCc: singleCCPayloadAfterChange?.ActyDepAllocTemplate?singleCCPayloadAfterChange?.ActyDepAllocTemplate:"",
        ActyDepTemplateSk: singleCCPayloadAfterChange?.ActyDepTemplateSk?singleCCPayloadAfterChange?.ActyDepTemplateSk:"",
        ActyIndepTemplateAllocCc: singleCCPayloadAfterChange?.ActyIndepAllocTemp?singleCCPayloadAfterChange?.ActyIndepAllocTemp:"",
        ActyIndepTemplateSk: singleCCPayloadAfterChange?.ActyIndepTemplateSk?singleCCPayloadAfterChange?.ActyIndepTemplateSk:"",
        AvcActive: singleCCPayloadAfterChange?.AvcActive?singleCCPayloadAfterChange?.AvcActive:false,
        AvcProfile: singleCCPayloadAfterChange?.AvcProfile?singleCCPayloadAfterChange?.AvcProfile:"",
        BudgetCarryingCostCtr: singleCCPayloadAfterChange?.BudgetCarryingCostCtr?singleCCPayloadAfterChange?.BudgetCarryingCostCtr:"",
        CurrencyIso: singleCCPayloadAfterChange?.CurrencyIso?singleCCPayloadAfterChange?.CurrencyIso:"",
        Department: singleCCPayloadAfterChange?.CCDepartment?singleCCPayloadAfterChange?.CCDepartment:"",
        FuncArea: singleCCPayloadAfterChange?.FunctionalArea?singleCCPayloadAfterChange?.FunctionalArea:"",
        FuncAreaFixAssigned: singleCCPayloadAfterChange?.FuncAreaFixAssigned?singleCCPayloadAfterChange?.FuncAreaFixAssigned:"",
        FuncAreaLong: singleCCPayloadAfterChange?.FuncArea?singleCCPayloadAfterChange?.FuncArea:"",
        Fund: singleCCPayloadAfterChange?.Fund?singleCCPayloadAfterChange?.Fund:"",
        FundFixAssigned: singleCCPayloadAfterChange?.FundFixAssigned?singleCCPayloadAfterChange?.FundFixAssigned:"",
        GrantFixAssigned: singleCCPayloadAfterChange?.GrantFixAssigned?singleCCPayloadAfterChange?.GrantFixAssigned:"",
        GrantId: singleCCPayloadAfterChange?.GrantId?singleCCPayloadAfterChange?.GrantId:"",
        JvEquityTyp: singleCCPayloadAfterChange?.JvEquityTyp?singleCCPayloadAfterChange?.JvEquityTyp:"",
        JvJibcl: singleCCPayloadAfterChange?.JvJibcl?singleCCPayloadAfterChange?.JvJibcl:"",
        JvJibsa: singleCCPayloadAfterChange?.JvJibsa?singleCCPayloadAfterChange?.JvJibsa:"",
        JvOtype: singleCCPayloadAfterChange?.JvOtype?singleCCPayloadAfterChange?.JvOtype:"",
        JvRecInd: singleCCPayloadAfterChange?.JvRecInd?singleCCPayloadAfterChange?.JvRecInd:"",
        JvVenture: singleCCPayloadAfterChange?.JvVenture?singleCCPayloadAfterChange?.JvVenture:"",
        Logsystem: singleCCPayloadAfterChange?.Logsystem?singleCCPayloadAfterChange?.Logsystem:"",
      },
    ],
  };


  const attachmentColumns = [
    {
      field: "id",
      headerName: "Document ID",
      flex: 1,
      hide: true,
    },
    // {
    //   field: "docType",
    //   headerName: "Type",
    //   flex: 1,
    // },
    {
      field: "docName",
      headerName: "Document Name",
      flex: 1,
    },
    {
      field: "uploadedOn",
      headerName: "Uploaded On",
      flex: 1,
      align: "center",
      headerAlign: "center",
    },
    {
      field: "uploadedBy",
      headerName: "Uploaded By",
      sortable: false,
      flex: 1,
    },
    {
      field: "action",
      headerName: "Action",
      sortable: false,
      filterable: false,
      align: "center",
      headerAlign: "center",
      flex: 1,
      renderCell: (cellValues) => {
        return (
          <>
            {/* <MatView index={cellValues.row.id} name={cellValues.row.docName} /> */}
            <MatDownload
              index={cellValues.row.id}
              name={cellValues.row.docName}
            />
          </>
        );
      },
    },
  ];

  // function removeHiddenAndEmptyObjects(obj) {
  //   console.log("objjjj",obj);
  //   for (let prop in obj) {
  //     console.log("dfssgf",prop);
  //     if (obj.hasOwnProperty(prop)) {
  //       if (Array.isArray(obj[prop])) {
  //         console.log("dskjhfdhsg",Array.isArray(obj[prop]));
  //         // If property is an array, iterate over its elements
  //         obj[prop] = obj[prop].filter((item) => item.visibility !== "Hidden");
  //         console.log("aufds",obj[prop]);
  //         if (obj[prop].length === 0) {
  //           // Remove the property if the array is empty
  //           delete obj[prop];
  //         }
  //       } else if (typeof obj[prop] === "object") {
  //         // If property is an object, recursively call the function
  //         obj[prop] = removeHiddenAndEmptyObjects(obj[prop]);
  //         if (Object.keys(obj[prop]).length === 0) {
  //           // Remove the property if the object is empty
  //           delete obj[prop];
  //         }
  //       }
  //     }
  //   }
  //   return obj;
  // }
  function removeHiddenAndEmptyObjects(obj) {
    // Helper function to determine if an object is empty
    const isEmpty = (obj) => Object.keys(obj).length === 0;

    // Recursively process the input object
    const processObject = (input) => {
      if (Array.isArray(input)) {
        // Filter out hidden items in arrays
        const filteredArray = input.filter(
          (item) => item.visibility !== "Hidden"
        );
        return filteredArray.length > 0 ? filteredArray : undefined;
      } else if (typeof input === "object" && input !== null) {
        // Create a new object to avoid mutation
        const result = {};
        for (let prop in input) {
          if (input.hasOwnProperty(prop)) {
            const processedValue = processObject(input[prop]);
            if (processedValue !== undefined) {
              result[prop] = processedValue;
            }
          }
        }
        return isEmpty(result) ? undefined : result;
      } else {
        // Return the value if it's neither an array nor an object
        return input;
      }
    };

    return processObject(obj) || {};
  }
  const getCostCenterDisplayData = () => {
    // setTableLoading(true);
    var payload =  {
        id: costCenterRowData?.reqStatus ? costCenterRowData?.id : "",
        requestId: costCenterRowData?.requestId
          ? costCenterRowData?.requestId?.slice(8)
          : "",
        costCenter: costCenterRowData?.costCenter
          ? costCenterRowData?.costCenter
          : "",
        controllingArea: costCenterRowData?.controllingArea
          ? costCenterRowData?.controllingArea
          : "",
        reqStatus: costCenterRowData?.reqStatus
          ? costCenterRowData?.reqStatus
          : "Approved",
        screenName: costCenterRowData?.requestType
          ? costCenterRowData?.requestType
          : "Display",
        dtName: "MDG_CC_FIELD_CONFIG",
        version: "v2",
        };

    console.log(payload, "payload");
    const hSuccess = (data) => {
      // let data = demoData;
      console.log("result",data)
      if (data.statusCode === 200) {
        // setIsLoading(false);
        const responseBody = data.body.viewData;
        const responseIDs = data.body;
        setDisplayDData(data?.body);
        setIds(responseIDs);
        dispatch(setWholeData(data?.body));
        let getjsonWhichHaveValue = removeHiddenAndEmptyObjects(responseBody);
        console.log("dgfdsfjgds",getjsonWhichHaveValue);
        dispatch(setCostCenterViewData(getjsonWhichHaveValue));
        // console.log(responseBody,"dcdcdc");
        // setCostCenterNameinRow(
        //   getValueForFieldName(
        //     responseBody["Basic Data"]?.["General Data"],
        //     "Name"
        //   )
        // );
        for (const key in data.body["General Information"]) {
          console.log(
            `${key}: ${data.body["General Information"][key]}`,
            "keydatapair"
          );
          dispatch(
            setSingleCostCenterETPayloadGI({
              keyName: key
                .replaceAll("(", "")
                .replaceAll(")", "")
                .replaceAll("/", "")
                .replaceAll("-", "")
                .replaceAll(".", "")
                .split(" ")
                .join(""),
              data:
                data?.body["General Information"][key] === "false"
                  ? "No"
                  : data?.body["General Information"][key] === "true"
                  ? "Yes"
                  : data.body["General Information"][key],
            })
          );
        }
        const categoryKeys = Object.keys(getjsonWhichHaveValue);
        console.log("categoryKeys",categoryKeys)
        const filteredKeys = categoryKeys.filter(key => {
          const value = responseBody[key];
          // Check if the value is not empty, considering both objects and strings
          return value && (typeof value !== 'object' || Object.keys(value).length > 0);
        });
     console.log("filteredKeys",filteredKeys);
        setFactorsArray(categoryKeys);
        const mappedData = filteredKeys?.map((category) => ({
          category,
          data: getjsonWhichHaveValue[category],
          setIsEditMode,
        }));

        setCostCenterDetails(mappedData);
        checkErrorFields(responseBody);

        getRegion(
          responseBody["Address"]["Address Data"].find(
            (field) => field?.fieldName === "Country/Reg."
          )
        );
      } else {
        setDisplayApiError(true);
        setsuccessMsg(false);
        setMessageDialogTitle("Error");
        setMessageDialogMessage("Unable to fetch data of Cost Center");
        setDialogType("danger");
        setMessageDialogSeverity("danger");
        // setMessageDialogExtra(true);
        setMessageDialogOK(true);
        setOpenMessageDialog(true);
      }
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/displayCostCenter`,
      "post",
      hSuccess,
      hError,
      payload
    );
    setPageName(payload.screenName);
  };
  const handleSnackBarOpen = () => {
    setopenSnackbar(true);
  };
  const handleSnackBarCloseValidation = () => {
    setOpenSnackbarValidation(false);
  };
  const handleCheckValidationError = () => {
    console.log(payloadFields, requiredFields, "PR");
    return formValidator(
      payloadFields,
      requiredFields,
      setFormValidationErrorItems
    );
  };
  // const handleCheckValidationErrorGI = () => {
  //   console.log('validationerror',singleGLPayloadMain, requiredFieldsGI)
  //       return formValidator(
  //         singleGLPayloadMain,
  //         requiredFieldsGI,
  //         setFormValidationErrorItems
  //       );
  //     };
      const getRegion = (value) => {
        console.log("compcode", value);
        const hSuccess = (data) => {
          console.log("value", data);
          dispatch(setDropDown({ keyName: "Region", data: data.body }));
        };
        const hError = (error) => {
          console.log(error, "error in dojax");
        };
        doAjax(
          `/${destination_CostCenter_Mass}/data/getRegionBasedOnCountry?country=${value}`,
          "get",
          hSuccess,
          hError
        );
      };
      const checkErrorFields = (viewData) => {
        //chiranjit
        //console.log(viewData,"viewData===============")
        let error_field_arr = [];
        for (const section in viewData) {
          if (viewData.hasOwnProperty(section)) {
            for (const fieldGroup in viewData[section]) {
              if (viewData[section].hasOwnProperty(fieldGroup)) {
                const fields = viewData[section][fieldGroup];
                //console.log(fields,"fieldsview_data")
                for (const field of fields) {
                  if (field.visibility === "0" || field.visibility === "Required") {
                    console.log(field.fieldName, "field.fieldName");
                    let required_field_name = field.fieldName.replace(/\s/g, "");
                    error_field_arr.push(required_field_name);
                  }
                }
              }
            }
          }
          setErrorsFields((prevState) => ({
            ...prevState,
            error_field_arr,
          }));
        }
        //return result;
      };
 
  const getCompanyCodeBasedOnControllingArea = (selectedControllingArea) => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({
          keyName: "CompCode",
          data: data.body,
        })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCompanyCodeBasedOnControllingArea?controllingArea=${selectedControllingArea}&rolePrefix=ETP`,
      "get",
      hSuccess,
      hError
    );
  };
 
 

  useEffect(() => {
    // Extract the role from data2
    console.log("maskingRoleIDM",maskingRoleIDM);
    console.log("maskingRoleApplication",maskingRoleApplication);
    const roleExists = maskingRoleIDM?.some(role =>
      maskingRoleApplication?.includes(role.MDG_MASKING_ROLES)
    );
  //   const roleExists = maskingRoleIDM?.MDG_MASKING_ACTION_TYPE?.some(action =>
  //     maskingRoleApplication.includes(action.MDG_MASKING_ROLES)
  // );
  console.log('check role',roleExists)
  if(maskingRoleApplication?.includes("Z4S:ALL:RTR:FI:UNMASK_FIN_MD")){
    setIsRolePresent(false);  
  }else{
    setIsRolePresent(roleExists);
  }
  }, [maskingRoleIDM, maskingRoleApplication]);

 

  // Loader and lookup for independent apis end
  const getGITemplate = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_GI_CCPC_QUESTIONS",
      version: "v4",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_GI_MODULE": "CC ET",
          "MDG_CONDITIONS.MDG_GI_SCENARIO": "Create",
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    setIsLoading(true);
    // const formData = new FormData();

    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        // const questionsData =
        //   data?.data?.result[0]?.MDG_GI_QUESTIONS_ACTION_TYPE || [];
        const GIData =
          data?.data?.result[0]?.MDG_GI_QUESTIONS_ACTION_TYPE || [];
        const questionsData = GIData.sort(
          (a, b) => a.MDG_SEQUENCE_NO - b.MDG_SEQUENCE_NO
        );
          questionsData?.map((question) => {
            if (question?.MDG_GI_INPUT_OPTION === 'Radio Button') {
              if (question?.MDG_GI_VISIBILITY === "Mandatory") {
  
                console.log("insidevisibilitytest");
                dispatch(setCCRequiredFieldsGI(question.MDG_GI_QUESTION_TYPE
                  .replaceAll("(", "")
                  .replaceAll(")", "")
                  .replaceAll("/", "")
                  .replaceAll("-", "")
                  .replaceAll(".", "")
                  .split(" ")
                  .join(""),))
              }
              if (question?.MDG_GI_QUESTION_TYPE !== "Choose Priority Level") {
                dispatch(
                  setSingleCostCenterETPayloadGI({
                    keyName: question.MDG_GI_QUESTION_TYPE
                      .replaceAll("(", "")
                      .replaceAll(")", "")
                      .replaceAll("/", "")
                      .replaceAll("-", "")
                      .replaceAll(".", "")
                      .split(" ")
                      .join(""),
                    data: "No",
                  })
                )
              } else {
                dispatch(
                  setSingleCostCenterETPayloadGI({
                    keyName: question.MDG_GI_QUESTION_TYPE
                      .replaceAll("(", "")
                      .replaceAll(")", "")
                      .replaceAll("/", "")
                      .replaceAll("-", "")
                      .replaceAll(".", "")
                      .split(" ")
                      .join(""),
                    data: "Medium",
                  })
                )
              }
            } else {
              if (question?.MDG_GI_VISIBILITY === "Mandatory") {
                console.log("insidevisibility");
                dispatch(setCCRequiredFieldsGI(question.MDG_GI_QUESTION_TYPE
                  .replaceAll("(", "")
                  .replaceAll(")", "")
                  .replaceAll("/", "")
                  .replaceAll("-", "")
                  .replaceAll(".", "")
                  .split(" ")
                  .join(""),));
              }
              dispatch(
                setSingleCostCenterETPayloadGI({
                  keyName: question.MDG_GI_QUESTION_TYPE
                    .replaceAll("(", "")
                    .replaceAll(")", "")
                    .replaceAll("/", "")
                    .replaceAll("-", "")
                    .replaceAll(".", "")
                    .split(" ")
                    .join(""),
                  data:singleGLPayloadMain[question.MDG_GI_QUESTION_TYPE
                    .replaceAll("(", "")
                    .replaceAll(")", "")
                    .replaceAll("/", "")
                    .replaceAll("-", "")
                    .replaceAll(".", "")
                    .split(" ")
                    .join("")] ?? "" ,
                }))
            }
  
  
          })
  
          dispatch(setGeneralInformation(questionsData));
      }
    };

    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(
        `/${destination_IDM}/rest/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      doAjax(
        `/${destination_IDM}/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };
  const handleSetEditedPayload = () => {
    let activeTabName = factorsArray[activeStep];
    console.log("activeTabName", activeTabName, factorsArray);
    let viewDataArray = Object.entries(costCenterViewData);
    console.log("viewDataArray", viewDataArray);
    const toSetArray = {};
    viewDataArray?.map((item) => {
      console.log("bottle", item[1]);
      let temp = Object.entries(item[1]);
      console.log("notebook", temp);
      temp.forEach((fieldGroup) => {
        fieldGroup[1].forEach((field) => {
          toSetArray[
            field.fieldName
              .replaceAll("(", "")
              .replaceAll(")", "")
              .replaceAll("/", "")
              .replaceAll("-", "")
              .replaceAll(".", "")
              .split(" ")
              .join("")
          ] = field.value;
        });
      });
      return item;
    });
    console.log("toSetArray", toSetArray);
    dispatch(setPayloadWhole(toSetArray));
  };
  useEffect(() => {
    // getCreateTemplate();
    getCostCenterDisplayData();
    getGITemplate();
    getAttachmentsIDM();
    getAttachments();
    getComments();
    getUserResponsible();
    // getCostCenterCategory();
    getCostCenter();
    getHistoryCostCenter();
    getHierarchyArea();
    getCompanyCodeBasedOnControllingArea(selectedControllingArea);
    getProfitCenter();
    // getBusinessArea();
    // getFunctionalArea();
    // getCostingSheet();
    // getJurisdiction();
    // getCountryOrRegion();
    // getLanguageKey();
    // dispatch(clearCostCenter());
  }, []);
  useEffect(() => {
    setCcNumber(idGenerator("CC"));
  }, []);
  useEffect(() => {
    if (costCenterViewData?.length === 0) {
      return;
    }
    handleSetEditedPayload();
  }, [costCenterViewData]);
  const onEdit = () => {
    // duplicateCheck();
    // dispatch(setEditMode(true));
    setIsEditMode(true);
    setIsDisplayMode(false);
  };
  const handleSnackBarOpenValidation = () => {
    setOpenSnackbarValidation(true);
  };
      const handleNext = () => {
        const isValidation = handleCheckValidationError();

        if (isEditMode) {
          if (isValidation) {
            setActiveStep((prevActiveStep) => prevActiveStep + 1);
            // dispatch(clearCostCenter());
          } else {
            handleSnackBarOpenValidation();
          }
        } else {
          setActiveStep((prevActiveStep) => prevActiveStep + 1);
          // dispatch(clearCostCenter());
        }
      };
      const handleBack = () => {
        setSubmitForReviewDisabled(true);
        const isValidation = handleCheckValidationError();
    
        if (isEditMode) {
          if (isValidation) {
            setActiveStep((prevActiveStep) => prevActiveStep - 1);
            // dispatch(clearCostCenter());
          } else {
            handleSnackBarOpenValidation();
          }
        } else {
          setActiveStep((prevActiveStep) => prevActiveStep - 1);
          // dispatch(clearCostCenter());
        }
      };

      const [displayDData, setDisplayDData] = useState({});
      const onRevealClick = () => {
        const payload = displayDData;
        setBlurLoading(true);
    
        const hSuccess = (data) => {
          setBlurLoading(false);
          const responseBody = data.body.viewData;
          const responseIDs = data.body;
    
          for (const key in data.body["General Information"]) {
            dispatch(
              setSinglegeneralLedgerPayloadGI({
                keyName: key
                  .replaceAll("(", "")
                  .replaceAll(")", "")
                  .replaceAll("/", "")
                  .replaceAll("-", "")
                  .replaceAll(".", "")
                  .split(" ")
                  .join(""),
                data:
                  data?.body["General Information"][key] === "0"
                    ? "No"
                    : data?.body["General Information"][key] === "1"
                    ? "Yes"
                    : data.body["General Information"][key],
              })
            );
          }
    
          let viewDataArray = Object.entries(responseBody);
          const toSetArray = {};
          viewDataArray?.map((item) => {
            let temp = Object.entries(item[1]);
            temp.forEach((fieldGroup) => {
              fieldGroup[1].forEach((field) => {
                toSetArray[
                  field.fieldName
                    .replaceAll("(", "")
                    .replaceAll(")", "")
                    .replaceAll("/", "")
                    .replaceAll("-", "")
                    .replaceAll(".", "")
                    .split(" ")
                    .join("")
                ] = field.value;
              });
            });
            return item;
          });
          dispatch(setPayloadWhole(toSetArray));
    
          let getjsonWhichHaveValue = removeHiddenAndEmptyObjects(responseBody);
          dispatch(setCostCenterViewData(getjsonWhichHaveValue));
    
          const categoryKeys = Object.keys(getjsonWhichHaveValue);
          setFactorsArray(categoryKeys);
    
          const mappedData = categoryKeys?.map((category) => ({
            category,
            data: getjsonWhichHaveValue[category],
          }));
          setCostCenterDetails(mappedData);
    
          dispatch(setHeaderdataCC(responseIDs));
        };
    
        const hError = (error) => {
          console.log(error);
          setBlurLoading(false);
        };
    
        doAjax(
          `/${destination_CostCenter_Mass}/data/unMask`,
          "post",
          hSuccess,
          hError,
          payload
        );
      };


      const handleMessageDialogClickOpen = () => {
        setOpenMessageDialog(true);
      }; 
      const handleCostCenterSubmitForApprovalCreate = () => {
        const hSuccess = (data) => {
          setIsLoading(false);
          if (data.statusCode === 200) {
            console.log("success");
            setMessageDialogTitle("Create");
            setMessageDialogMessage(
              `Cost Center Submitted for Approval with ID ECSN${data.body} `
            );
            setMessageDialogSeverity("success");
            setMessageDialogOK(false);
            setsuccessMsg(true);
            handleSnackBarOpen();
            setMessageDialogExtra(true);
            // setIsLoading(false);
          } else {
            setMessageDialogTitle("Error");
            setsuccessMsg(false);
            setMessageDialogMessage("Failed Submitting Cost Center");
            setMessageDialogSeverity("danger");
            setMessageDialogOK(false);
            setMessageDialogExtra(true);
            handleMessageDialogClickOpen();
            setTestrunStatus(true);
            // setIsLoading(false);
          }
          handleClose();
        };
        const hError = (error) => {
          console.log(error);
        };
        doAjax(
          `/${destination_CostCenter_Mass}/alter/costCenterApprovalSubmit`,
          "post",
          hSuccess,
          hError,
          payload
        );
      };
      const handleCostCenterReview = () => {
        const hSuccess = (data) => {
          setMessageDialogMessage(
            `Create id generated for Data Owners ECSC${data.body} `
          );
        };
        const hError = (error) => {
          console.log(error);
        };
        doAjax(
          // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
          `/${destination_CostCenter_Mass}/alter/costCenterSendForReview`,
          "post",
          hSuccess,
          hError,
          payload
        );
      };
      const handleCostCenterApproveCreate = () => {
        const hSuccess = (data) => {
          setIsLoading(false);
          if (data.statusCode === 201) {
            console.log("success");
            setMessageDialogTitle("Create");
            setMessageDialogMessage(`${data.message} `);
            setMessageDialogSeverity("success");
            setMessageDialogOK(false);
            setsuccessMsg(true);
            handleSnackBarOpen();
            setMessageDialogExtra(true);
            // setIsLoading(false);
          } else {
            setMessageDialogTitle("Error");
            setsuccessMsg(false);
            setMessageDialogMessage("Failed Approving Cost Center");
            setMessageDialogSeverity("danger");
            setMessageDialogOK(false);
            setMessageDialogExtra(true);
            handleMessageDialogClickOpen();
            setTestrunStatus(true);
            // setIsLoading(false);
          }
          handleClose();
        };
        const hError = (error) => {
          console.log(error);
        };
        doAjax(
          // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
          `/${destination_CostCenter_Mass}/alter/createCostCenterApproved`,
          "post",
          hSuccess,
          hError,
          payload
        );
      };
      const handleCostCenterCorrectionCreate = () => {
        console.log("apicallllllllll");
        const hSuccess = (data) => {
          setMessageDialogMessage(
            `Cost Center has been Sent for Correction CMS${data.body}`
          );
        };
        const hError = (error) => {
          console.log(error);
        };
        doAjax(
          // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
          `/${destination_CostCenter_Mass}/alter/costCenterSendForCorrection`,
          "post",
          hSuccess,
          hError,
          payload
        );
      };
      const handleCostCenterReviewCreate = () => {
        const hSuccess = (data) => {
          setIsLoading(false);
          if (data.statusCode === 200) {
            console.log("success");
            setMessageDialogTitle("Create");
            setMessageDialogMessage(
              `Cost Center Submitted for Review with ID NPS${data.body} `
            );
            setMessageDialogSeverity("success");
            setMessageDialogOK(false);
            setsuccessMsg(true);
            handleSnackBarOpen();
            setMessageDialogExtra(true);
            // setIsLoading(false);
            const secondApiPayload = {
              artifactId: ccNumber,
              createdBy: userData?.emailId,
              artifactType: "CostCenter",
              requestId: `ECSN${data?.body}`,
            };
            const secondApiSuccess = (secondApiData) => {
              console.log("Second API success", secondApiData);
              // Handle success for the second API if needed
            };
    
            const secondApiError = (secondApiError) => {
              console.error("Second API error", secondApiError);
              // Handle error for the second API if needed
            };
            // {requestId&&
            doAjax(
              `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
              "post",
              secondApiSuccess,
              secondApiError,
              secondApiPayload
            );
          } else {
            setMessageDialogTitle("Error");
            setsuccessMsg(false);
            setMessageDialogMessage("Failed Submitting Cost Center");
            setMessageDialogSeverity("danger");
            setMessageDialogOK(false);
            setMessageDialogExtra(true);
            handleMessageDialogClickOpen();
            setTestrunStatus(true);
            // setIsLoading(false);
          }
          handleClose();
        };
        const hError = (error) => {
          console.log(error);
        };
        doAjax(
          // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
          `/${destination_CostCenter_Mass}/alter/costCenterSubmitForReview`,
          "post",
          hSuccess,
          hError,
          payload
        );
      };
      const handleCostCenterSaveAsDraftChange = () => {
        setMessageDialogSeverity(false);
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Confirm");
        setMessageDialogMessage(`Do You Want to Save as Draft ?`);
        setHandleExtrabutton(true);
        setHandleExtraText("proceed");
        setDialogType("Change");
      };
      const handleCostCenterSaveAsDraftCreate = () => {
        setMessageDialogSeverity(false);
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Confirm");
        setMessageDialogMessage(`Do You Want to Save as Draft ?`);
        setHandleExtrabutton(true);
        setHandleExtraText("proceed");
        setDialogType("Create");
      };
      const handleProceedbutton = () => {
        setIsLoading(true);
        const hSuccess = (data) => {
          handleMessageDialogClose();
          setIsLoading(false);
          if (data.statusCode === 200) {
            console.log("success");
            setMessageDialogTitle("Create");
            setMessageDialogMessage(
              `Cost Center has been Saved with ID ECSN${data.body}`
            );
            setHandleExtrabutton(false);
            setMessageDialogSeverity("success");
            setMessageDialogOK(false);
            setsuccessMsg(true);
            handleSnackBarOpen();
            setMessageDialogExtra(true);
            distinctArtifactId?.map((artifactId) => {
              console.log(artifactId, "artifactId=====");
              const secondApiPayload = {
                artifactId: artifactId,
                createdBy: userData?.emailId,
                artifactType: "CostCenter",
                requestId: `ECSN${data?.body}`,
              };
              const secondApiSuccess = (secondApiData) => {
                console.log("Second API success", secondApiData);
                // Handle success for the second API if needed
              };
    
              const secondApiError = (secondApiError) => {
                console.error("Second API error", secondApiError);
                // Handle error for the second API if needed
              };
              // {requestId&&
              doAjax(
                `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
                "post",
                secondApiSuccess,
                secondApiError,
                secondApiPayload
              );
            });
    
            const secondApiError = (secondApiError) => {
              console.error("Second API error", secondApiError);
              // Handle error for the second API if needed
            };
            // {requestId&&
            doAjax(
              `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
              "post",
              secondApiSuccess,
              secondApiError,
              secondApiPayload
            );
          } else {
            setMessageDialogTitle("Save");
            setsuccessMsg(false);
            setMessageDialogMessage("Failed Saving the Data");
            setHandleExtrabutton(false);
            setMessageDialogSeverity("danger");
            setMessageDialogOK(false);
            setMessageDialogExtra(true);
            handleMessageDialogClickOpen();
            //setIsLoading(false);
          }
          handleClose();
        };
        const hError = (error) => {
          console.log(error);
        };
        doAjax(
          `/${destination_CostCenter_Mass}/alter/costCenterAsDraft`,
          "post",
          hSuccess,
          hError,
          payload
        );
      };
      const handleCostCenterReviewChange = () => {
        const hSuccess = (data) => {
          setIsLoading(false);
          if (data.statusCode === 200) {
            console.log("success");
            setMessageDialogTitle("Create");
            setMessageDialogMessage(
              `Cost Center Submitted for Review with ID CPS${data.body} `
            );
            setHandleExtrabutton(false);
            setMessageDialogSeverity("success");
            setMessageDialogOK(false);
            setsuccessMsg(true);
            handleSnackBarOpen();
            setMessageDialogExtra(true);
            // setIsLoading(false);
            const secondApiPayload = {
              artifactId: ccNumber,
              createdBy: userData?.emailId,
              artifactType: "CostCenter",
              requestId: `ECSC${data?.body}`,
            };
            const secondApiSuccess = (secondApiData) => {
              console.log("Second API success", secondApiData);
              // Handle success for the second API if needed
            };
    
            const secondApiError = (secondApiError) => {
              console.error("Second API error", secondApiError);
              // Handle error for the second API if needed
            };
            // {requestId&&
            doAjax(
              `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
              "post",
              secondApiSuccess,
              secondApiError,
              secondApiPayload
            );
          } else {
            setMessageDialogTitle("Error");
            setsuccessMsg(false);
            setMessageDialogMessage("Failed Submitting Cost Center");
            setHandleExtrabutton(false);
            setMessageDialogSeverity("danger");
            setMessageDialogOK(false);
            setMessageDialogExtra(true);
            handleMessageDialogClickOpen();
            setTestrunStatus(true);
            // setIsLoading(false);
          }
          handleClose();
        };
        const hError = (error) => {
          console.log(error);
        };
        doAjax(
          // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
          `/${destination_CostCenter_Mass}/alter/changeCostCenterSubmitForReview`,
          "post",
          hSuccess,
          hError,
          payload
        );
      };
      const handleCostCenterApproveChange = () => {
        const hSuccess = (data) => {
          setIsLoading(false);
          if (data.statusCode === 201) {
            console.log("success");
            setMessageDialogTitle("Create");
            setMessageDialogMessage(`${data.message} `);
            setHandleExtrabutton(false);
            setMessageDialogSeverity("success");
            setMessageDialogOK(false);
            setsuccessMsg(true);
            handleSnackBarOpen();
            setMessageDialogExtra(true);
            // setIsLoading(false);
          } else {
            setMessageDialogTitle("Error");
            setsuccessMsg(false);
            setMessageDialogMessage("Failed Saving Cost Center");
            setHandleExtrabutton(false);
            setMessageDialogSeverity("danger");
            setMessageDialogOK(false);
            setMessageDialogExtra(true);
            handleMessageDialogClickOpen();
            setTestrunStatus(true);
            // setIsLoading(false);
          }
          handleClose();
        };
        const hError = (error) => {
          console.log(error);
        };
        doAjax(
          // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
          `/${destination_CostCenter_Mass}/alter/changeCostCenterApproved`,
          "post",
          hSuccess,
          hError,
          payload
        );
      };
      const handleCostCenterSubmitChange = () => {
        const hSuccess = (data) => {
          setIsLoading(false);
          if (data.statusCode === 200) {
            console.log("success");
            setMessageDialogTitle("Create");
            setMessageDialogMessage(
              `Cost Center Submitted for Approval with ID ECSC${data.body} `
            );
            setHandleExtrabutton(false);
            setMessageDialogSeverity("success");
            setMessageDialogOK(false);
            setsuccessMsg(true);
            handleSnackBarOpen();
            setMessageDialogExtra(true);
            // setIsLoading(false);
          } else {
            setMessageDialogTitle("Error");
            setsuccessMsg(false);
            setMessageDialogMessage("Failed Submitting Cost Center");
            setHandleExtrabutton(false);
            setMessageDialogSeverity("danger");
            setMessageDialogOK(false);
            setMessageDialogExtra(true);
            handleMessageDialogClickOpen();
            setTestrunStatus(true);
            // setIsLoading(false);
          }
          handleClose();
        };
        const hError = (error) => {
          console.log(error);
        };
        doAjax(
          // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
          `/${destination_CostCenter_Mass}/alter/changeCostCenterApprovalSubmit`,
          "post",
          hSuccess,
          hError,
          payload
        );
      };
      const handleCostCenterCorrectionChange = () => {
        console.log("apicallllllllll");
        const hSuccess = (data) => {
          setMessageDialogMessage(
            `Create id generated for Data Owners ECSC${data.body}`
          );
        };
        const hError = (error) => {
          console.log(error);
        };
        doAjax(
          // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
          `/${destination_CostCenter_Mass}/alter/changeCostCenterSendForCorrection`,
          "post",
          hSuccess,
          hError,
          payload
        );
      };
      const onValidateCostCenterApprover = () => {
        setBlurLoading(true);
        const hSuccess = (data) => {
          if (data.statusCode === 201) {
            // Handle success
            setBlurLoading(false);
            setMessageDialogTitle("Create");
            setMessageDialogTitle("Create");
            setMessageDialogMessage(
              `All Data has been Validated. Cost Center can be Sent for Review`
            );
            setHandleExtrabutton(false);
            setMessageDialogSeverity("success");
            setMessageDialogOK(false);
            setsuccessMsg(true);
            handleSnackBarOpen();
            setMessageDialogExtra(true);
            // setIsLoading(false);
            setValidateFlag(true);
            setSubmitForReviewDisabled(false);
          } else {
            // Handle error
            setBlurLoading(false);
            setMessageDialogTitle("Error");
            setsuccessMsg(false);
            if(data?.body?.message?.length != 0){
              const content = (
                <Typography component="div">
                  <ul>
                    {data?.body?.message.map((item, index) => (
                      <li key={index}>
                        {item}
                      </li>
                    ))}
                  </ul>
                </Typography>
              );
              setMessageDialogMessage(content);
            }else{
              const content  = data.body.value
              setMessageDialogMessage(content);
            }
            setHandleExtrabutton(false);
            setMessageDialogSeverity("danger");
            setMessageDialogOK(false);
            setMessageDialogExtra(true);
            handleMessageDialogClickOpen();
            // setIsLoading(false);
          }
        };
        const hError = (error) => {
          console.log(error);
        };
    
        // Call the main API for validation
        doAjax(
          `/${destination_CostCenter_Mass}/alter/validateCostCenter`,
          "post",
          hSuccess,
          hError,
          payload
        );
      };
      const onValidateCostCenter = () => {
        setBlurLoading(true);
        // Define duplicateCheckPayload outside of the onValidateCostCenter function
        const duplicateCheckPayload = {
          coArea: displayData?.controllingAreaData || "",
          name: singleCCPayload?.ShortDescription ? singleCCPayload?.ShortDescription.toUpperCase() : "",
        };
        const compCodeDuplicateCheckPayload = {
          compCode: displayData?.companyCode?.newCompanyCode?.code || "",
          description: singleCCPayload?.Description
            ? singleCCPayload?.Description.toUpperCase()
            : "",
        };
    
        const hSuccess = (data) => {
          // setIsLoading(false);
          if (data.statusCode === 201) {
            // Handle success
            setMessageDialogTitle("Create");
            setMessageDialogTitle("Create");
            setMessageDialogMessage(
              `All Data has been Validated. Cost Center can be Sent for Review`
            );
            setHandleExtrabutton(false);
            setMessageDialogSeverity("success");
            setMessageDialogOK(false);
            setsuccessMsg(true);
            handleSnackBarOpen();
            setMessageDialogExtra(true);
            // setIsLoading(false);
            setValidateFlag(true);
    
            // Now, make the duplicate check API call
            // Ensure that the conditions for making the duplicate check API call are met
            if (
              duplicateCheckPayload.coArea !== "" ||
              duplicateCheckPayload.name !== ""
            ) {
              // Description Duplicate check Start
    
              // const hDuplicateCheckSuccess = (data) => {
              //   // Handle success of duplicate check
              //   if (
              //     data.body.length === 0 ||
              //     !data.body.some(
              //       (item) => item.toUpperCase() === duplicateCheckPayload.name
              //     )
              //   ) {
              //     // No direct match, enable the "Submit for Review" button
              //     setSubmitForReviewDisabled(false);
              //     setBlurLoading(false);
              //     if (
              //       compCodeDuplicateCheckPayload.compCode !== "" ||
              //       compCodeDuplicateCheckPayload.description !== ""
              //     ) {
              //       const hCompCodeDuplicateCheckSuccess = (data) => {
              //         // Handle success of Company Code duplicate check
              //         if (
              //           data.body.length === 0 ||
              //           !data.body.some(
              //             (item) =>
              //               item.toUpperCase() ===
              //               compCodeDuplicateCheckPayload.description
              //           )
              //         ) {
              //           // No direct match, continue with the process// Here you can place the logic to proceed after validation
              //           setMessageDialogMessage(
              //             `All Data has been Validated. Profit Center can be Sent for Review`
              //           );
              //         } else {
              //           // Handle direct match// Set appropriate error message or take necessary action
              //           setMessageDialogMessage(
              //             `There is a direct match for the Profit Center Description. Please change the Description.`
              //           );
              //         }
              //       };
              //       const hCompCodeDuplicateCheckError = (error) => {
              //         // Handle error of Company Code duplicate check
              //         console.log(error);
              //       };
              //       doAjax(
              //         `/${destination_CostCenter_Mass}/alter/fetchCompCodeDescriptionDupliChk`,
              //         "post",
              //         hCompCodeDuplicateCheckSuccess,
              //         hCompCodeDuplicateCheckError,
              //         compCodeDuplicateCheckPayload
              //       );
              //     }
              //   } else {
              //     // Handle direct match
              //     setBlurLoading(false);
              //     setMessageDialogTitle("Duplicate Check");
              //     setsuccessMsg(false);
              //     setMessageDialogMessage(
              //       `There is a direct match for the Profit Center name. Please change the name.`
              //     );
              //     setMessageDialogSeverity("danger");
              //     setMessageDialogOK(false);
              //     setMessageDialogExtra(true);
              //     handleMessageDialogClickOpen();
              //     setSubmitForReviewDisabled(true);
              //   }
              // };
    
              // Description Duplicate check End
    
              doAjax(
                `/${destination_CostCenter_Mass}/alter/fetchCCDescriptionDupliChk`,
                "post",
                hDuplicateCheckSuccess,
                hDuplicateCheckError,
                duplicateCheckPayload
              );
            }
          } else {
            // Handle error
            setMessageDialogTitle("Error");
            setsuccessMsg(false);
            if(data?.body?.message?.length != 0){
              const content = (
                <Typography component="div">
                  <ul>
                    {data?.body?.message.map((item, index) => (
                      <li key={index}>
                        {item}
                      </li>
                    ))}
                  </ul>
                </Typography>
              );
              setMessageDialogMessage(content);
            }else{
              const content  = data.body.value
              setMessageDialogMessage(content);
            }
            setHandleExtrabutton(false);
            setMessageDialogSeverity("danger");
            setMessageDialogOK(false);
            setMessageDialogExtra(true);
            handleMessageDialogClickOpen();
            setBlurLoading(false);
            // setIsLoading(false);
          }
        };
    
        const hDuplicateCheckSuccess = (data) => {
          // Handle success of duplicate check
          if (
            data.body.length === 0 ||
            !data.body.some(
              (item) => item.toUpperCase() === duplicateCheckPayload.name
            )
          ) {
            // No direct match, enable the "Submit for Review" button
            setBlurLoading(false);
            setSubmitForReviewDisabled(false);
          } else {
            // Handle direct match
            setBlurLoading(false);
            setMessageDialogTitle("Duplicate Check");
            setsuccessMsg(false);
            setMessageDialogMessage(
              `There is a direct match for the Cost Center name. Please change the name.`
            );
            setHandleExtrabutton(false);
            setMessageDialogSeverity("danger");
            setMessageDialogOK(false);
            setMessageDialogExtra(true);
            handleMessageDialogClickOpen();
            setSubmitForReviewDisabled(true);
          }
        };
    
        const hDuplicateCheckError = (error) => {
          // Handle error of duplicate check
          console.log(error);
        };
    
        const hError = (error) => {
          console.log(error);
        };
    
        // Call the main API for validation
        doAjax(
          `/${destination_CostCenter_Mass}/alter/validateCostCenter`,
          "post",
          hSuccess,
          hError,
          payload
        );
        // } else {
        //   handleSnackBarOpenValidation();
        // }
      };
      const handleSnackBarClose = () => {
        if (validateFlag) {
          setopenSnackbar(false);
          setValidateFlag(false);
        } else {
          setopenSnackbar(false);
          navigate("/masterDataCockpit/costCenter");
        }
      };
      const handleMessageDialogClose = () => {
        setOpenMessageDialog(false);
      };  
      const onCostCenterApproveCreate = () => {
        setIsLoading(true);
        handleCostCenterApproveCreate();
      };
      const onCostCenterSubmitCreate = () => {
        setIsLoading(true);
        handleCostCenterSubmitForApprovalCreate();
      };
      const onCostCenterReviewCreate = () => {
        setIsLoading(true);
        handleCostCenterReviewCreate();
      };
      const onCostCenterSaveAsDraftChange = () => {
        handleCostCenterSaveAsDraftChange();
      };
      const onCostCenterSaveAsDraftCreate = () => {
        handleCostCenterSaveAsDraftCreate();
      };
      const onCostCenterApproveChange = () => {
        setIsLoading(true);
        handleCostCenterApproveChange();
      };
      const onCostCenterReviewChange = () => {
        setIsLoading(true);
        handleCostCenterReviewChange();
      };
      const onCostCenterSubmitChange = () => {
        setIsLoading(true);
        handleCostCenterSubmitChange();
      };
      const onCostCenterCorrection = () => {
        if (
          userData?.role === "MDM Steward" &&
          (costCenterRowData?.requestType === "Create" ||
            taskRowDetails?.requestType === "Create")
        ) {
          setIsLoading(true);
          handleCorrectionMDMCreate();
        } else if (
          userData?.role === "MDM Steward" &&
          (costCenterRowData?.requestType === "Change" ||
            taskRowDetails?.requestType === "Change")
        ) {
          setIsLoading(true);
          handleCorrectionMDMChange();
        } else if (
          userData?.role === "Approver" &&
          (costCenterRowData?.requestType === "Create" ||
            taskRowDetails?.requestType === "Create")
        ) {
          setIsLoading(true);
          handleCorrectionApproverCreate();
        } else if (
          userData?.role === "Approver" &&
          (costCenterRowData?.requestType === "Change" ||
            taskRowDetails?.requestType === "Change")
        ) {
          setIsLoading(true);
          handleCorrectionApproverChange();
        }
      };
      const handleCorrectionMDMCreate = () => {
        const hSuccess = (data) => {
          setIsLoading(false);
          if (data.statusCode === 200) {
            console.log("success");
            setMessageDialogTitle("Create");
            setMessageDialogMessage(
              `Cost Center Submitted for Correction with ID ECSN${data.body}`
            );
            setHandleExtrabutton(false);
            setMessageDialogSeverity("success");
            setMessageDialogOK(false);
            setsuccessMsg(true);
            handleSnackBarOpen();
            setMessageDialogExtra(true);
            // setIsLoading(false);
          } else {
            setMessageDialogTitle("Error");
            setsuccessMsg(false);
            setMessageDialogMessage(
              "Failed Submitting Cost Center for Correction"
            );
            setHandleExtrabutton(false);
            setMessageDialogSeverity("danger");
            setMessageDialogOK(false);
            setMessageDialogExtra(true);
            handleMessageDialogClickOpen();
            // setIsLoading(false);
          }
          handleCorrectionDialogClose();
        };
        const hError = (error) => {
          console.log(error);
        };
        doAjax(
          // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
          `/${destination_CostCenter_Mass}/alter/costCenterSendForCorrection`,
          "post",
          hSuccess,
          hError,
          payload
        );
      };
      const handleCorrectionApproverCreate = () => {
        const hSuccess = (data) => {
          setIsLoading(false);
          if (data.statusCode === 200) {
            console.log("success");
            setMessageDialogTitle("Create");
            setMessageDialogMessage(
              `Cost Center Submitted for Cost with ID ECSN${data.body}`
            );
            setHandleExtrabutton(false);
            setMessageDialogSeverity("success");
            setMessageDialogOK(false);
            setsuccessMsg(true);
            handleSnackBarOpen();
            setMessageDialogExtra(true);
            // setIsLoading(false);
          } else {
            setMessageDialogTitle("Error");
            setsuccessMsg(false);
            setMessageDialogMessage(
              "Failed Submitting Cost Center for Correction"
            );
            setHandleExtrabutton(false);
            setMessageDialogSeverity("danger");
            setMessageDialogOK(false);
            setMessageDialogExtra(true);
            handleMessageDialogClickOpen();
            // setIsLoading(false);
          }
          handleCorrectionDialogClose();
        };
        const hError = (error) => {
          console.log(error);
        };
        doAjax(
          // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
          `/${destination_CostCenter_Mass}/alter/costCenterSendForReview`,
          "post",
          hSuccess,
          hError,
          payload
        );
      };
      const handleCorrectionApproverChange = () => {
        const hSuccess = (data) => {
          setIsLoading(false);
          if (data.statusCode === 200) {
            console.log("success");
            setMessageDialogTitle("Create");
            setMessageDialogMessage(
              `Cost Center Submitted for Correction with ID ECSC${data.body}`
            );
            setHandleExtrabutton(false);
            setMessageDialogSeverity("success");
            setMessageDialogOK(false);
            setsuccessMsg(true);
            handleSnackBarOpen();
            setMessageDialogExtra(true);
            // setIsLoading(false);
          } else {
            setMessageDialogTitle("Error");
            setsuccessMsg(false);
            setMessageDialogMessage(
              "Failed Submitting Cost Center for Correction"
            );
            setHandleExtrabutton(false);
            setMessageDialogSeverity("danger");
            setMessageDialogOK(false);
            setMessageDialogExtra(true);
            handleMessageDialogClickOpen();
            // setIsLoading(false);
          }
          handleCorrectionDialogClose();
        };
        const hError = (error) => {
          console.log(error);
        };
        doAjax(
          // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
          `/${destination_CostCenter_Mass}/alter/changeCostCenterSendForReview`,
          "post",
          hSuccess,
          hError,
          payload
        );
      };
      const handleOpenCorrectionDialog = () => {
        setOpenCorrectionDialog(true);
      };
      const handleCorrectionDialogClose = () => {
        setRemarks("");
        setTestrunStatus(true);
        setOpenCorrectionDialog(false);
      };
      const handleRemarks = (e, value) => {
        //setRemarks(e.target.value);
        const newValue = e.target.value;
        if (newValue.length > 0 && newValue[0] === " ") {
          setRemarks(newValue.trimStart());
        } else {
          //let costCenterValue = e.target.value;
          let remarksUpperCase = newValue;
          console.log("remarksss", remarksUpperCase);
          setRemarks(remarksUpperCase);
        }
      };

  const handleClosemodalData = (data) => {
    setisChangeLogopen(data);
  };
  const openChangeLog = () => {
    setisChangeLogopen(true);
  };


  // const getCostCenterBasicDetails = () => {
  //   let viewName = "Basic Data";
  //   const hSuccess = (data) => {
  //     dispatch(setCostCenterBasicDataTab(data.body));
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(
  //     `/${destination_CostCenter_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
  //     "get",
  //     hSuccess,
  //     hError
  //   );
  // };
  // const getControlCostCenter = () => {
  //   let viewName = "Control";
  //   const hSuccess = (data) => {
  //     dispatch(setCostCenterControlTab(data.body));
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(
  //     `/${destination_CostCenter_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
  //     "get",
  //     hSuccess,
  //     hError
  //   );
  // };
  // const getTemplatesCostCenter = () => {
  //   let viewName = "Templates";
  //   const hSuccess = (data) => {
  //     dispatch(setCostCenterTemplatesTab(data.body));
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(
  //     `/${destination_CostCenter_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
  //     "get",
  //     hSuccess,
  //     hError
  //   );
  // };
  // const getAddressCostCenter = () => {
  //   let viewName = "Address";
  //   const hSuccess = (data) => {
  //     dispatch(setCostCenterAddressTab(data.body));
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(
  //     `/${destination_CostCenter_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
  //     "get",
  //     hSuccess,
  //     hError
  //   );
  // };
  // const getCommunicationCostCenter = () => {
  //   let viewName = "Communication";
  //   const hSuccess = (data) => {
  //     dispatch(setCostCenterCommunicationTab(data.body));
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(
  //     `/${destination_CostCenter_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
  //     "get",
  //     hSuccess,
  //     hError
  //   );
  // };








  const getHistoryCostCenter = () => {
    let viewName = "History";
    const hSuccess = (data) => {
      dispatch(setCostCenterHistoryTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getUserResponsible = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "UserResponsible", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getUserResponsible`,
      "get",
      hSuccess,
      hError
    );
  };
  // const getCostCenterCategory = () => {
  //   const hSuccess = (data) => {
  //     dispatch(setDropDown({ keyName: "CostCenterCategory", data: data.body }));
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(
  //     `/${destination_CostCenter_Mass}/data/getCostCenterCategory`,
  //     "get",
  //     hSuccess,
  //     hError
  //   );
  // };
  const getCostCenter = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CostCenter", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCostCenter`,
      "get",
      hSuccess,
      hError
    );
  };
  const getBusinessArea = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "BusinessArea", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getBusinessArea`,
      "get",
      hSuccess,
      hError
    );
  };
  const getFunctionalArea = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "FunctionalArea", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getFunctionalArea`,
      "get",
      hSuccess,
      hError
    );
  };

  const getCostingSheet = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CostingSheet", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCostingSheet`,
      "get",
      hSuccess,
      hError
    );
  };

  const getCountryOrRegion = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CountryReg", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCountry`,
      "get",
      hSuccess,
      hError
    );
  };
  const getJurisdiction = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "Jurisdiction", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getJurisdiction`,
      "get",
      hSuccess,
      hError
    );
  };
  const getLanguageKey = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "LanguageKey", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getLanguageKey`,
      "get",
      hSuccess,
      hError
    );
  };
  console.log(
    "blck",
    singleCCPayload?.BlockingStatus?.includes("Plan Revenue")
  );







  function groupBy(array, key) {
    return array.reduce((result, currentValue) => {
      (result[currentValue[key]] = result[currentValue[key]] || []).push(
        currentValue
      );
      return result;
    }, {});
  }

  const getFieldsWhichhaveDefaultValue = (groupedFields) => {
    //console.log(groupedFields,"groupedFields=")
    //alert("coming")
    let hashofDfaultDataArr = [];
    groupedFields.forEach((field) => {
      //console.log(field.MDG_PC_DEFAULT_VALUE,"defaultValueFields====")
      //let hashofDfaultData={}
      if (
        (field?.MDG_CC_VISIBILITY === "Hidden" ||
          field?.MDG_CC_VISIBILITY === "Display") &&
        field?.MDG_CC_JSON_FIELD_NAME !== undefined &&
        field?.MDG_CC_JSON_FIELD_NAME !== null
      ) {
        //console.log("coming===")
        let hashofDfaultData = {};
        if (
          field.MDG_CC_DEFAULT_VALUE !== undefined &&
          field.MDG_CC_DEFAULT_VALUE !== null
        ) {
          hashofDfaultData[field.MDG_CC_JSON_FIELD_NAME] =
            field.MDG_CC_DEFAULT_VALUE;
          dispatch(
            setSingleCostCenterPayload({
              keyName: field.MDG_CC_JSON_FIELD_NAME,
              data: field.MDG_CC_DEFAULT_VALUE,
            })
          );
          hashofDfaultDataArr.push(hashofDfaultData);
        }

        //console.log(hashofDfaultData,"hashofDfaultDataobj")
      }
      //hashofDfaultDataArr.push(hashofDfaultData)
      console.log(hashofDfaultDataArr, "hashofDfaultDataArr===");
      //return hashofDfaultDataArr;
    });
    return hashofDfaultDataArr;
  };

  const getCreateTemplate = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_CC_FIELD_CONFIG",
      version: "v2",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_CC_SCENARIO": "Create",
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    setIsLoading(true);
    // const formData = new FormData();
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        let responseData =
          data?.data?.result[0]?.MDG_CC_FIELD_DETAILS_ACTION_TYPE;
        let sortedData = responseData.sort(
          (a, b) => a.MDG_CC_VIEW_SEQUENCE - b.MDG_CC_VIEW_SEQUENCE
        );
        const groupedFields = groupBy(sortedData, "MDG_CC_VIEW_NAME");
        const defaultValueFields = getFieldsWhichhaveDefaultValue(responseData);
        let view_data_hash = {};
        console.log(groupedFields, "groupedFields");
        Object.entries(groupedFields).forEach(([key, value]) => {
          let groupedFieldsDataCardNameWise = groupBy(
            value,
            "MDG_CC_CARD_NAME"
          );
          //setTabsArray((Object.keys(groupedFields)).filter(item => item !== "Initial Screen"));

          console.log(tabsArray, "tabsArraytet");
          let cardNameHash = {};
          Object.entries(groupedFieldsDataCardNameWise).forEach(
            ([cardkey, cardvalue]) => {
              console.log(cardkey, "cardkey====");
              let cardName = [];
              cardvalue?.map((item) => {
                let each_hashkey = {};
                each_hashkey["fieldName"] = item.MDG_CC_UI_FIELD_NAME;
                each_hashkey["sequenceNo"] = item.MDG_CC_SEQUENCE_NO;
                each_hashkey["fieldType"] = item.MDG_CC_FIELD_TYPE;
                each_hashkey["maxLength"] = item.MDG_CC_MAX_LENGTH;
                each_hashkey["dataType"] = item.MDG_CC_DATA_TYPE;
                each_hashkey["viewName"] = item.MDG_CC_VIEW_NAME;
                each_hashkey["cardName"] = item.MDG_CC_CARD_NAME;
                each_hashkey["cardSeq"] = item.MDG_CC_CARD_SEQUENCE;
                each_hashkey["value"] = item.MDG_CC_DEFAULT_VALUE;
                each_hashkey["visibility"] = item.MDG_CC_VISIBILITY;
                cardName.push(each_hashkey);
              });
              cardNameHash[cardkey] = cardName;
              console.log(cardNameHash, "cardNameHash===");
            }
          );
          view_data_hash[key] = cardNameHash;
        });
        let getjsonWhichHaveValue = removeHiddenAndEmptyObjects(view_data_hash);
        console.log("getjsonWhichHaveValue",getjsonWhichHaveValue)
        let tabsData = Object.keys(getjsonWhichHaveValue).filter(
          (item) => item !== "Initial Screen"
        );
        console.log('tabsData', tabsData)
        setTabsArray(tabsData);
        let main_viewHash = {};
        main_viewHash["viewData"] = getjsonWhichHaveValue;
        tabsData?.map((items) => {
          console.log(items, "items===");
          if (items === "Basic Data") {
            //dispatch(setProfitCenterBasicDataTab(main_viewHash?.viewData?.["Basic Data"]))
            dispatch(
              setCostCenterBasicDataTab(main_viewHash?.viewData?.["Basic Data"])
            );
          } else if (items === "Control") {
            dispatch(
              setCostCenterControlTab(main_viewHash?.viewData?.["Control"])
            );
          } else if (items === "Templates") {
            //console.log("indicatorcominghh")
            dispatch(
              setCostCenterTemplatesTab(main_viewHash?.viewData?.["Templates"])
            );
          } else if (items === "Communication") {
            dispatch(
              setCostCenterCommunicationTab(
                main_viewHash?.viewData?.["Communication"]
              )
            );
          } else if (items === "Address") {
            dispatch(
              setCostCenterAddressTab(main_viewHash?.viewData?.["Address"])
            );
          } else if (items === "Additional Data") {
            // dispatch(
            //   setAdditionalDataTab(main_viewHash?.viewData?.["Additional Data"])
            // );
          }
        });
      
      } else {
        // setMessageDialogTitle("Create");
        // setsuccessMsg(false);
        // setMessageDialogMessage("Creation Failed");
        // setHandleExtrabutton(false);
        // setMessageDialogSeverity("danger");
        // setMessageDialogOK(false);
        // setMessageDialogExtra(true);
        // handleMessageDialogClickOpen();
        // handleCreateDialogClose();
        // setIsLoading(false);
      }
      handleClose();
    };

    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(
        `/${destination_IDM}/rest/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      doAjax(
        `/${destination_IDM}/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };
  const getAttachmentsIDM = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_ATTACHMENTS_LIST_DT",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE": "ET Cost Center",
          "MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO": "Create",
          "MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE": 1,
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    setIsLoading(true);
    // const formData = new FormData();
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        let responseData = data?.data?.result[0]?.MDG_ATTACHMENTS_ACTION_TYPE;
        let templateData = [];
        responseData?.map((element, index) => {
          console.log("element", element);

          var tempRow = {
            id: index,
            // templateName: element?.MDG_CHANGE_TEMPLATE_NAME,
            // templateData: element?.MDG_CHANGE_TEMPLATE_FIELD_LIST,
          };
          templateData.push(tempRow);
        });
        setRuleData(templateData);
        const attachmentNames =
          data?.data?.result[0]?.MDG_ATTACHMENTS_ACTION_TYPE || [];

        // Update state with attachment names
        setAttachmentsData(attachmentNames);
      } else {
        // setMessageDialogTitle("Create");
        // setsuccessMsg(false);
        // setMessageDialogMessage("Creation Failed");
        // setHandleExtrabutton(false);
        // setMessageDialogSeverity("danger");
        // setMessageDialogOK(false);
        // setMessageDialogExtra(true);
        // handleMessageDialogClickOpen();
        // handleCreateDialogClose();
        // setIsLoading(false);
      }
      handleClose();
    };

    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(
        `/${destination_IDM}/rest/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      doAjax(
        `/${destination_IDM}/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };

  const getAttachments = () => {
    let requestId = taskRowDetails?.requestId
      ? taskRowDetails?.requestId
      : costCenterRowData?.requestId;

    let hSuccess = (data) => {
      //alert(data.documentDetailDtoList)
      console.log(data.documentDetailDtoList, "data.documentDetailDtoList");
      var attachmentRows = [];
      data.documentDetailDtoList.forEach((doc) => {
        console.log(data.documentDetailDtoList, "data.");
        var tempRow = {
          id: doc.documentId,
          docType: doc.fileType,
          docName: doc.fileName,
          uploadedOn: moment(doc.docCreationDate).format(appSettings.date),
          uploadedBy: doc.createdBy,
        };
        console.log(tempRow, "tempRow");
        attachmentRows.push(tempRow);
      });
      setAttachments(attachmentRows);
    };
    // invoiceHeaderData?.extInvNum &&
    doAjax(
      `/${destination_DocumentManagement}/documentManagement/getDocByRequestId/${requestId}`,
      "get",
      hSuccess
    );
  };
  const getComments = () => {
    let requestId = taskRowDetails?.requestId
      ? taskRowDetails?.requestId
      : costCenterRowData?.requestId;
    let hSuccess = (data) => {
      console.log("commentsdata", data);

      var commentRows = [];
      data.body.forEach((cmt) => {
        var tempRow = {
          id: cmt.requestId,
          comment: cmt.comment,
          user: cmt.createdByUser,
          createdAt: cmt.updatedAt,
        };
        commentRows.push(tempRow);
      });
      setComments(commentRows);
      console.log("commentrows", commentRows.length);
    };

    let hError = (error) => {
      console.log(error);
    };
    // invoiceHeaderData?.extInvNum &&
    doAjax(
      `/${destination_CostCenter_Mass}/activitylog/fetchTaskDetailsForRequestId?requestId=${requestId}`,
      "get",
      hSuccess,
      hError
    );
  };





  const getHierarchyArea = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "HierarchyArea", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getHierarchyArea?controllingArea=${displayData?.controllingAreaData}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getValueForFieldName = (data, fieldName) => {
    //chiranjit
    console.log("getvalueforfieldname", data, fieldName);
    const field = data?.find((field) => field?.fieldName === fieldName);
    return field ? field.value : "";
  };

  const getProfitCenter = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ProfitCenter", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getProfitCenterAsPerControllingArea?controllingArea=${displayData?.controllingAreaData}`,
      "get",
      hSuccess,
      hError
    );
  };




  // const onValidateCostCenterApprover = () => {
  //   setBlurLoading(true);
  //   const hSuccess = (data) => {
  //     if (data.statusCode === 201) {
  //       // Handle success
  //       setBlurLoading(false);
  //       setMessageDialogTitle("Create");
  //       setMessageDialogTitle("Create");
  //       setMessageDialogMessage(
  //         `All Data has been Validated. Cost Center can be Sent for Review`
  //       );
  //       setHandleExtrabutton(false);
  //       setMessageDialogSeverity("success");
  //       setMessageDialogOK(false);
  //       setsuccessMsg(true);
  //       handleSnackBarOpen();
  //       setMessageDialogExtra(true);
  //       // setIsLoading(false);
  //       setValidateFlag(true);
  //       setSubmitForReviewDisabled(false);
  //     } else {
  //       // Handle error
  //       setBlurLoading(false);
  //       setMessageDialogTitle("Error");
  //       setsuccessMsg(false);
  //       setMessageDialogMessage(
  //         `${
  //           data?.body?.message[0] ? data?.body?.message[0] : data?.body?.value
  //         }`
  //       );
  //       setHandleExtrabutton(false);
  //       setMessageDialogSeverity("danger");
  //       setMessageDialogOK(false);
  //       setMessageDialogExtra(true);
  //       handleMessageDialogClickOpen();
  //       // setIsLoading(false);
  //     }
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };

  //   // Call the main API for validation
  //   doAjax(
  //     `/${destination_CostCenter_Mass}/alter/validateSingleCostCenter`,
  //     "post",
  //     hSuccess,
  //     hError,
  //     payload
  //   );
  // };






  const onCostCenterCorrectionChange = () => {
    handleCostCenterCorrectionChange();
  };

  const handleMessageDialogNavigate = () => {
    // navigate("/masterDataCockpitNew/costCenter");
  };



  console.log("payloadFields", payloadFields);
  console.log("requiredFields", requiredFields);
  useEffect(() => {
    console.log("formvaaaa", formValidationErrorItems);
    dispatch(setCCErrorFields(formValidationErrorItems));
  }, [formValidationErrorItems]);


  const onSubmitForReviewButtonClick = () => {
    setIsLoading(true);
    // const formData = new FormData();
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Cost Center has been Submitted for review ECSN${data.body}`
        );
        setHandleExtrabutton(false);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        handleRemarksDialogClose();
        // setIsLoading(false);

        // Make the second API call
        distinctArtifactId?.map((artifactId) => {
          console.log(artifactId, "artifactId=====");
          const secondApiPayload = {
            artifactId: artifactId,
            createdBy: userData?.emailId,
            artifactType: "CostCenter",
            requestId: `ECSN${data?.body}`,
          };
          const secondApiSuccess = (secondApiData) => {
            console.log("Second API success", secondApiData);
            // Handle success for the second API if needed
          };

          const secondApiError = (secondApiError) => {
            console.error("Second API error", secondApiError);
            // Handle error for the second API if needed
          };
          // {requestId&&
          doAjax(
            `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
            "post",
            secondApiSuccess,
            secondApiError,
            secondApiPayload
          );
        });
        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
        // }
      } else {
        setMessageDialogTitle("Create");
        setsuccessMsg(false);
        setMessageDialogMessage("Creation Failed");
        setHandleExtrabutton(false);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        handleCreateDialogClose();
        // setIsLoading(false);
      }
      handleClose();
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_CostCenter_Mass}/alter/costCenterSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const onSaveAsDraftButtonClick = () => {
    setMessageDialogSeverity(false);
    handleMessageDialogClickOpen();
    setMessageDialogTitle("Confirm");
    setMessageDialogMessage(`Do You Want to Save as Draft ?`);
    setHandleExtrabutton(true);
    setHandleExtraText("proceed");
  };
  console.log(handleExtrabutton, "setHandleExtrabutton");






  const onCostCenterSubmitRemarks = () => {
    handleCreateDialogClose();
    onSubmitForReviewButtonClick();
  };

  const handleCreateDialog = () => {
    setTestrunStatus(false);
    setOpenCreateDialog(true);
  };

  const handleRemarksDialogClose = () => {
    setRemarks("");
    setTestrunStatus(true);
    setOpenRemarkDialog(false);
  };
  const handleOpenRemarkDialog = () => {
    setTestrunStatus(false);
    setOpenRemarkDialog(true);
  };




  console.log("factorsarray", factorsArray);
  console.log("costCenterDetails", costCenterDetails);
  const filteredFactorsArray = factorsArray.filter(
    (factor) =>
      factor !== "General Information" && factor !== "Attachments & Comments"
  );
  const tabContents = filteredFactorsArray
  ?.map((item) => {
    const mdata = costCenterDetails.filter(
      (ii) => ii.category?.split(" ")[0] == item?.split(" ")[0]
    );
    if (mdata.length != 0) {
      return { category: item?.split(" ")[0], data: mdata[0].data };
    }
    // return { category: item?.split(" ")[0], data: ddata };
  })
  ?.map((categoryData, index) => {
    console.log(categoryData?.category, "categoryData");
    // if (categoryData?.category === "General" && activeStep === 0) {
    //   return [
    //     <>
    //       {userData?.role === "Finance" ? (
    //         <Grid container spacing={2}>
    //           {questions.map((question, index) => (
    //             <Grid item md={12} key={index}>
    //               <Grid
    //                 container
    //                 sx={{
    //                   backgroundColor: "white",
    //                   maxHeight: "max-content",
    //                   height: "max-content",
    //                   borderRadius: "8px",
    //                   border: "1px solid #E0E0E0",
    //                   mt: 0.25,
    //                   boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
    //                   padding: "16px",
    //                 }}
    //               >
    //                 <Stack sx={{ width: "100%" }}>
    //                   <Typography
    //                     sx={{
    //                       fontSize: "12px",
    //                       fontWeight: "700",
    //                     }}
    //                   >
    //                     {question.MDG_GI_QUESTION_TYPE}
    //                     {question.MDG_GI_VISIBILITY === "Mandatory" ? (
    //                       <span style={{ color: "red" }}>*</span>
    //                     ) : (
    //                       ""
    //                     )}
    //                   </Typography>

    //                   {question.MDG_GI_INPUT_OPTION === "Radio Button" ? (
    //                     <RadioGroup
    //                       aria-labelledby={`radio-group-label-${index}`}
    //                       defaultValue=""
    //                       //name={`radio-group-${index}`}
    //                       value={
    //                         singleGLPayloadMain[
    //                           question.MDG_GI_QUESTION_TYPE.replaceAll(
    //                             "(",
    //                             ""
    //                           )
    //                             .replaceAll(")", "")
    //                             .replaceAll("/", "")
    //                             .replaceAll("-", "")
    //                             .replaceAll(".", "")
    //                             .split(" ")
    //                             .join("")
    //                         ]
    //                       }
    //                       name={question.MDG_GI_QUESTION_TYPE}
    //                       row
    //                       onChange={handleRadioChangeGIQuestion}
    //                       disabled={!isEditMode}
    //                     >
    //                       {question.MDG_GI_INPUT_VALUE.split(",").map(
    //                         (option, optIndex) => (
    //                           <FormControlLabel
    //                             key={optIndex}
    //                             value={option}
    //                             disabled={!isEditMode}
    //                             control={<Radio />}
    //                             label={option}
    //                           />
    //                         )
    //                       )}
    //                     </RadioGroup>
    //                   ) : 
    //                   question.MDG_GI_INPUT_OPTION === 'Dropdown' ? (
    //                     <Grid item md={2}>
    //                     {console.log(dropDownData[question.MDG_GI_QUESTION_TYPE],question.MDG_GI_QUESTION_TYPE, "hjkl")}
    //                         <Autocomplete
    //                           sx={{ height: "31px" }}
    //                           // disabled
    //                           fullWidth
    //                           size="small"
    //                           disabled={!isEditMode}
    //                           value={
    //                             dropDownData[question.MDG_GI_QUESTION_TYPE]?.find(
    //                               (x) =>
    //                                 x.MDG_LOOKUP_CODE ===
    //                                 singleGLPayloadMain[
    //                                   question.MDG_GI_QUESTION_TYPE.replaceAll("(", "")
    //                                     .replaceAll(")", "")
    //                                     .replaceAll("/", "")
    //                                     .replaceAll("-", "")
    //                                     .replaceAll(".", "")
    //                                     .split(" ")
    //                                     .join("")
    //                                 ]
    //                             ) || null
    //                           }
              
    //                           // onChange={handleRadioChangeGIQuestion()}
    //                           onChange={(event, newValue) => handleDropDownChangeGIQuestion(event, newValue, 'Dropdown', question.MDG_GI_QUESTION_TYPE)}
    //                           // onChange={(event, newValue) => {
    //                           //   let label = question.MDG_GI_QUESTION_TYPE;
    //                           //   onEditGI(label, newValue?.MDG_LOOKUP_CODE);
    //                           // handleCheckValidationErrorGI();}}
    //                           options={dropDownData[question.MDG_GI_QUESTION_TYPE] ?? []}
    //                           getOptionLabel={(option) => `${option?.MDG_LOOKUP_CODE} `}
    //                           renderOption={(props, option) => (
    //                             <li {...props}>
    //                               <Typography style={{ fontSize: 12 }}>
    //                                 {option?.MDG_LOOKUP_CODE} 
    //                               </Typography>
    //                             </li>
    //                           )}
    //                           renderInput={(params) => (
    //                             <TextField
    //                               {...params}
    //                               variant="outlined"
    //                               placeholder="Please Enter..."
    //                               // error={errorFields.includes(keyName)}
    //                             />
    //                           )}
    //                         />
    //                   </Grid>
    //                   ):   
    //                   (
    //                     <TextField
    //                       fullWidth
    //                       placeholder="Please Enter..."
    //                       multiline
    //                       disabled={!isEditMode}
    //                       name={question.MDG_GI_QUESTION_TYPE}
    //                       value={
    //                         singleGLPayloadMain[
    //                           question.MDG_GI_QUESTION_TYPE.replaceAll(
    //                             "(",
    //                             ""
    //                           )
    //                             .replaceAll(")", "")
    //                             .replaceAll("/", "")
    //                             .replaceAll("-", "")
    //                             .replaceAll(".", "")
    //                             .split(" ")
    //                             .join("")
    //                         ]
    //                       }
    //                       onChange={handleRadioChangeGIQuestionTextField}
    //                     />
    //                   )}
    //                 </Stack>
    //               </Grid>
    //             </Grid>
    //           ))}
    //         </Grid>
    //       ) : (
    //         <Grid container spacing={2}>
    //           {questions.map((question, index) => (
    //             <Grid item md={12} key={index}>
    //               <Grid
    //                 container
    //                 sx={{
    //                   backgroundColor: "white",
    //                   maxHeight: "max-content",
    //                   height: "max-content",
    //                   borderRadius: "8px",
    //                   border: "1px solid #E0E0E0",
    //                   mt: 0.25,
    //                   boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
    //                   padding: "16px",
    //                 }}
    //               >
    //                 <Stack sx={{ width: "100%" }}>
    //                   <Typography
    //                     sx={{
    //                       fontSize: "12px",
    //                       fontWeight: "700",
    //                     }}
    //                   >
    //                     {question.MDG_GI_QUESTION_TYPE}
    //                     {question.MDG_GI_VISIBILITY === "Mandatory" ? (
    //                       <span style={{ color: "red" }}>*</span>
    //                     ) : (
    //                       ""
    //                     )}
    //                   </Typography>

    //                   {question.MDG_GI_INPUT_OPTION === "Radio Button" ? (
    //                     <RadioGroup
    //                       aria-labelledby={`radio-group-label-${index}`}
    //                       defaultValue=""
    //                       //name={`radio-group-${index}`}
    //                       value={
    //                         singleGLPayloadMain[
    //                           question.MDG_GI_QUESTION_TYPE.replaceAll(
    //                             "(",
    //                             ""
    //                           )
    //                             .replaceAll(")", "")
    //                             .replaceAll("/", "")
    //                             .replaceAll("-", "")
    //                             .replaceAll(".", "")
    //                             .split(" ")
    //                             .join("")
    //                         ]
    //                       }
    //                       name={question.MDG_GI_QUESTION_TYPE}
    //                       row
    //                       onChange={handleRadioChangeGIQuestion}
    //                     >
    //                       {question.MDG_GI_INPUT_VALUE.split(",").map(
    //                         (option, optIndex) => (
    //                           <FormControlLabel
    //                             key={optIndex}
    //                             disabled={!isEditMode}
    //                             value={option}
    //                             control={<Radio disabled />}
    //                             label={option}
    //                           />
    //                         )
    //                       )}
    //                     </RadioGroup>
    //                   ) : 
    //                   question.MDG_GI_INPUT_OPTION === 'Dropdown' ? (
    //                     <Grid item md={2}>
    //                     {console.log(dropDownData[question.MDG_GI_QUESTION_TYPE],question.MDG_GI_QUESTION_TYPE, "hjkl")}
    //                         <Autocomplete
    //                           sx={{ height: "31px" }}
    //                           // disabled
    //                           fullWidth
    //                           size="small"
    //                           // value={valueFromPayload[keyName]}
    //                           // value = {singleGLPayloadMain?.[question.MDG_GI_QUESTION_TYPE]}
    //                           value={
    //                             dropDownData[question.MDG_GI_QUESTION_TYPE]?.find(
    //                               (x) =>
    //                                 x.MDG_LOOKUP_CODE ===
    //                                 singleGLPayloadMain[
    //                                   question.MDG_GI_QUESTION_TYPE.replaceAll("(", "")
    //                                     .replaceAll(")", "")
    //                                     .replaceAll("/", "")
    //                                     .replaceAll("-", "")
    //                                     .replaceAll(".", "")
    //                                     .split(" ")
    //                                     .join("")
    //                                 ]
    //                             ) || null
    //                           }
              
    //                           // onChange={handleRadioChangeGIQuestion()}
    //                           onChange={(event, newValue) => handleDropDownChangeGIQuestion(event, newValue, 'Dropdown', question.MDG_GI_QUESTION_TYPE)}
    //                           // onChange={(event, newValue) => {
    //                           //   let label = question.MDG_GI_QUESTION_TYPE;
    //                           //   onEditGI(label, newValue?.MDG_LOOKUP_CODE);
    //                           // handleCheckValidationErrorGI();}}
    //                           options={dropDownData[question.MDG_GI_QUESTION_TYPE] ?? []}
    //                           getOptionLabel={(option) => `${option?.MDG_LOOKUP_CODE} `}
    //                           renderOption={(props, option) => (
    //                             <li {...props}>
    //                               <Typography style={{ fontSize: 12 }}>
    //                                 {option?.MDG_LOOKUP_CODE} 
    //                               </Typography>
    //                             </li>
    //                           )}
    //                           renderInput={(params) => (
    //                             <TextField
    //                               {...params}
    //                               variant="outlined"
    //                               placeholder="Please Enter..."
    //                               // error={errorFields.includes(keyName)}
    //                             />
    //                           )}
    //                         />
    //                   </Grid>
    //                   ):   
    //                   (
    //                     <TextField
    //                       fullWidth
    //                       placeholder="Please Enter..."
    //                       multiline
    //                       disabled={!isEditMode}
    //                       name={question.MDG_GI_QUESTION_TYPE}
    //                       value={
    //                         singleGLPayloadMain[
    //                           question.MDG_GI_QUESTION_TYPE.replaceAll(
    //                             "(",
    //                             ""
    //                           )
    //                             .replaceAll(")", "")
    //                             .replaceAll("/", "")
    //                             .replaceAll("-", "")
    //                             .replaceAll(".", "")
    //                             .split(" ")
    //                             .join("")
    //                         ]
    //                       }
    //                       onChange={handleRadioChangeGIQuestion}
    //                     />
    //                   )}
    //                 </Stack>
    //               </Grid>
    //             </Grid>
    //           ))}
    //         </Grid>
    //       )}
    //     </>,
    //   ];
    // } else 
    if (categoryData?.category == "Basic") {
      return [
        <Grid
          key={categoryData.category}
          container
          item
          md={12}
          sx={{
            backgroundColor: "white",
            maxHeight: "max-content",
            height: "max-content",
            mt: 1,
            mb: 1,
          }}
        >
          {Object.keys(categoryData.data)?.map((fieldGroup) => (
            <Grid
              key={fieldGroup}
              item
              md={12}
              sx={{
                backgroundColor: "white",
                maxHeight: "max-content",
                height: "max-content",
                borderRadius: "8px",
                border: "1px solid #E0E0E0",
                mt: 0.25,
                boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                ...container_Padding,
              }}
            >
              <Typography
                sx={{
                  fontSize: "12px",
                  fontWeight: "700",
                  margin: "0px !important",
                }}
              >
                {fieldGroup}
              </Typography>
              <Box sx={{ width: "100%" }}>
                <CardContent
                  sx={{
                    padding: "0",
                    paddingBottom: "0 !important",
                    paddingTop: "10px !important",
                  }}
                >
                  <Grid
                    container
                    style={{
                      display: "grid",
                      gridTemplateColumns: "repeat(6,1fr)",
                      gap: "15px",
                    }}
                    justifyContent="space-between"
                    alignItems="flex-start"
                    md={12}
                  >
                    {categoryData.data[fieldGroup]?.map((field) => {
                      console.log("fieldDatatttt", field);
                      return (
                        <EditableFieldForCostCenterSunoco
                          label={field.fieldName}
                          value={field.value}
                          length={field.maxLength}
                          data={editedData}
                          displayData={displayData}
                          pcData={iDs}
                          visibility={field.visibility}
                          onSave={(newValue) =>
                            handleFieldSave(field.fieldName, newValue)
                          }
                          isEditMode={isEditMode}
                          type={field.fieldType}
                          field={field}
                          taskRequestId={costCenterRowData?.requestId}
                          moduleName={"ETCC"}
                        />
                      );
                    })}
                  </Grid>
                </CardContent>
              </Box>
            </Grid>
          ))}
        </Grid>,
      ];
    } else if (categoryData?.category == "Control") {
      return [
        <Grid
          key={categoryData.category}
          container
          item
          md={12}
          sx={{
            backgroundColor: "white",
            maxHeight: "max-content",
            height: "max-content",
            mt: 1,
            mb: 1,
          }}
        >
          {Object.keys(categoryData.data)?.map((fieldGroup) => (
            <Grid
              key={fieldGroup}
              item
              md={12}
              sx={{
                backgroundColor: "white",
                maxHeight: "max-content",
                height: "max-content",
                borderRadius: "8px",
                border: "1px solid #E0E0E0",
                mt: 0.25,
                boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                ...container_Padding,
              }}
            >
              <Typography
                sx={{
                  fontSize: "12px",
                  fontWeight: "700",
                  margin: "0px !important",
                }}
              >
                {fieldGroup}
              </Typography>
              <Box sx={{ width: "100%" }}>
                <CardContent
                  sx={{
                    padding: "0",
                    paddingBottom: "0 !important",
                    paddingTop: "10px !important",
                  }}
                >
                  <Grid
                    container
                    style={{
                      display: "grid",
                      gridTemplateColumns: "repeat(6,1fr)",
                      gap: "15px",
                    }}
                    justifyContent="space-between"
                    alignItems="flex-start"
                    md={12}
                  >
                    {categoryData.data[fieldGroup]?.map((field) => {
                      return (
                        <EditableFieldForCostCenterSunoco
                          label={field.fieldName}
                          value={field.value}
                          length={field.maxLength}
                          data={editedData}
                          visibility={field.visibility}
                          onSave={(newValue) =>
                            handleFieldSave(field.fieldName, newValue)
                          }
                          isEditMode={isEditMode}
                          type={field.fieldType}
                          field={field}
                          taskRequestId={costCenterRowData?.requestId}
                        />
                      );
                    })}
                  </Grid>
                </CardContent>
              </Box>
            </Grid>
          ))}
        </Grid>,
      ];
    } else if (categoryData?.category == "Templates") {
      return [
        <Grid
          key={categoryData.category}
          container
          item
          md={12}
          sx={{
            backgroundColor: "white",
            maxHeight: "max-content",
            height: "max-content",
            mt: 1,
            mb: 1,
          }}
        >
          {Object.keys(categoryData.data)?.map((fieldGroup) => (
            <Grid
              key={fieldGroup}
              item
              md={12}
              sx={{
                backgroundColor: "white",
                maxHeight: "max-content",
                height: "max-content",
                borderRadius: "8px",
                border: "1px solid #E0E0E0",
                mt: 0.25,
                boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                ...container_Padding,
              }}
            >
              <Typography
                sx={{
                  fontSize: "12px",
                  fontWeight: "700",
                  margin: "0px !important",
                }}
              >
                {fieldGroup}
              </Typography>
              <Box sx={{ width: "100%" }}>
                <CardContent
                  sx={{
                    padding: "0",
                    paddingBottom: "0 !important",
                    paddingTop: "10px !important",
                  }}
                >
                   {categoryData.data[fieldGroup]?.map((field) => {
                      return (
                        <EditableFieldForCostCenterSunoco
                          label={field.fieldName}
                          value={field.value}
                          length={field.maxLength}
                          data={editedData}
                          visibility={field.visibility}
                          onSave={(newValue) =>
                            handleFieldSave(field.fieldName, newValue)
                          }
                          isEditMode={isEditMode}
                          type={field.fieldType}
                          field={field}
                          taskRequestId={costCenterRowData?.requestId}
                        />
                      );
                    })}
                </CardContent>
              </Box>
            </Grid>
          ))}
        </Grid>,
      ];
    } else if (categoryData?.category == "Address") {
      return [
        <Grid
          key={categoryData.category}
          container
          item
          md={12}
          sx={{
            backgroundColor: "white",
            maxHeight: "max-content",
            height: "max-content",
            mt: 1,
            mb: 1,
          }}
        >
          {Object.keys(categoryData.data)?.map((fieldGroup) => (
            <Grid
              key={fieldGroup}
              item
              md={12}
              sx={{
                backgroundColor: "white",
                maxHeight: "max-content",
                height: "max-content",
                borderRadius: "8px",
                border: "1px solid #E0E0E0",
                mt: 0.25,
                boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                ...container_Padding,
              }}
            >
              <Typography
                sx={{
                  fontSize: "12px",
                  fontWeight: "700",
                  margin: "0px !important",
                }}
              >
                {fieldGroup}
              </Typography>
              <Box sx={{ width: "100%" }}>
                <CardContent
                  sx={{
                    padding: "0",
                    paddingBottom: "0 !important",
                    paddingTop: "10px !important",
                  }}
                >
                  <Grid
                    container
                    style={{
                      display: "grid",
                      gridTemplateColumns: "repeat(6,1fr)",
                      gap: "15px",
                    }}
                    justifyContent="space-between"
                    alignItems="flex-start"
                    md={12}
                  >
                    {categoryData.data[fieldGroup]?.map((field) => {
                      return (
                        <EditableFieldForCostCenterSunoco
                          label={field.fieldName}
                          value={field.value}
                          length={field.maxLength}
                          data={editedData}
                          visibility={field.visibility}
                          onSave={(newValue) =>
                            handleFieldSave(field.fieldName, newValue)
                          }
                          isEditMode={isEditMode}
                          type={field.fieldType}
                          field={field}
                          taskRequestId={costCenterRowData?.requestId}
                        />
                      );
                    })}
                  </Grid>
                </CardContent>
              </Box>
            </Grid>
          ))}
        </Grid>,
      ];
    } else if (categoryData?.category == "Communication") {
      return [
        <Grid
          key={categoryData.category}
          container
          item
          md={12}
          sx={{
            backgroundColor: "white",
            maxHeight: "max-content",
            height: "max-content",
            mt: 1,
            mb: 1,
          }}
        >
          {Object.keys(categoryData.data)?.map((fieldGroup) => (
            <Grid
              key={fieldGroup}
              item
              md={12}
              sx={{
                backgroundColor: "white",
                maxHeight: "max-content",
                height: "max-content",
                borderRadius: "8px",
                border: "1px solid #E0E0E0",
                mt: 0.25,
                boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                ...container_Padding,
              }}
            >
              <Typography
                sx={{
                  fontSize: "12px",
                  fontWeight: "700",
                  margin: "0px !important",
                }}
              >
                {fieldGroup}
              </Typography>
              <Box sx={{ width: "100%" }}>
                <CardContent
                  sx={{
                    padding: "0",
                    paddingBottom: "0 !important",
                    paddingTop: "10px !important",
                  }}
                >
                  <Grid
                    container
                    style={{
                      display: "grid",
                      gridTemplateColumns: "repeat(6,1fr)",
                      gap: "15px",
                    }}
                    justifyContent="space-between"
                    alignItems="flex-start"
                    md={12}
                  >
                    {categoryData.data[fieldGroup]?.map((field) => {
                      return (
                        <EditableFieldForCostCenterSunoco
                          label={field.fieldName}
                          value={field.value}
                          length={field.maxLength}
                          data={editedData}
                          visibility={field.visibility}
                          onSave={(newValue) =>
                            handleFieldSave(field.fieldName, newValue)
                          }
                          isEditMode={isEditMode}
                          type={field.fieldType}
                          field={field}
                          taskRequestId={costCenterRowData?.requestId}
                        />
                      );
                    })}
                  </Grid>
                </CardContent>
              </Box>
            </Grid>
          ))}
        </Grid>,
      ];
    } else if (categoryData?.category == "Additional") {
      return [
        <Grid
          key={categoryData.category}
          container
          item
          md={12}
          sx={{
            backgroundColor: "white",
            maxHeight: "max-content",
            height: "max-content",
            mt: 1,
            mb: 1,
          }}
        >
          {Object.keys(categoryData.data)?.map((fieldGroup) => (
            <Grid
              key={fieldGroup}
              item
              md={12}
              sx={{
                backgroundColor: "white",
                maxHeight: "max-content",
                height: "max-content",
                borderRadius: "8px",
                border: "1px solid #E0E0E0",
                mt: 0.25,
                boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                ...container_Padding,
              }}
            >
              <Typography
                sx={{
                  fontSize: "12px",
                  fontWeight: "700",
                  margin: "0px !important",
                }}
              >
                {fieldGroup}
              </Typography>
              <Box sx={{ width: "100%" }}>
                <CardContent
                  sx={{
                    padding: "0",
                    paddingBottom: "0 !important",
                    paddingTop: "10px !important",
                  }}
                >
                  <Grid
                    container
                    style={{
                      display: "grid",
                      gridTemplateColumns: "repeat(6,1fr)",
                      gap: "15px",
                    }}
                    justifyContent="space-between"
                    alignItems="flex-start"
                    md={12}
                  >
                    {categoryData.data[fieldGroup]?.map((field) => {
                      return (
                        <EditableFieldForCostCenterSunoco
                          label={field.fieldName}
                          value={field.value}
                          length={field.maxLength}
                          data={editedData}
                          visibility={field.visibility}
                          onSave={(newValue) =>
                            handleFieldSave(field.fieldName, newValue)
                          }
                          isEditMode={isEditMode}
                          type={field.fieldType}
                          field={field}
                          taskRequestId={costCenterRowData?.requestId}
                        />
                      );
                    })}
                  </Grid>
                </CardContent>
              </Box>
            </Grid>
          ))}
        </Grid>,
      ];
    } else if (categoryData?.category == "Attachments") {
      return [
        <>
        {isEditMode ? (
          <>
          {attachmentsData?.map((attachment, index) => (
            <Grid
              key={index}
              item
              md={12}
              sx={{
                backgroundColor: "white",
                maxHeight: "max-content",
                height: "max-content",
                borderRadius: "8px",
                border: "1px solid #E0E0E0",
                mt: 0.25,
                boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                ...container_Padding,
                // ...container_columnGap,
              }}
            >
              <Grid container>
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                  }}
                >
                  {attachment.MDG_ATTACHMENTS_NAME}
                </Typography>
              </Grid>

              <Grid container>
                <Grid item>
                  <ReusableAttachementAndComments
                    title="CostCenter"
                    useMetaData={false}
                    artifactId={`${attachment.MDG_ATTACHMENTS_NAME}_${ccNumber}`}
                    artifactName="CostCenter"
                    attachmentType={attachment.MDG_ATTACHMENTS_NAME}
                  />
                </Grid>
              </Grid>
            </Grid>
          ))}
        </>
          
        ) : (
          <Card sx={{ padding: "1rem 1rem 0rem 1rem" }}>
          <Grid
            container
            sx={{
              display: "flex",
              justifyContent: "space-between",
            }}
          >
            <Typography variant="h6">
              <strong>Attachments</strong>
            </Typography>
          </Grid>
          {Boolean(attachments.length) && (
            <ReusableTable
              width="100%"
              rows={attachments}
              columns={attachmentColumns}
              hideFooter={false}
              getRowIdValue={"id"}
              disableSelectionOnClick={true}
              stopPropagation_Column={"action"}
            />
          )}
          {!Boolean(attachments.length) && (
            <Typography variant="body2">No Attachments Found</Typography>
          )}
          <br />
          <Typography variant="h6">Comments</Typography>
          {Boolean(comments.length) && (
            <Timeline
              sx={{
                [`& .${timelineItemClasses.root}:before`]: {
                  flex: 0,
                  padding: 0,
                },
              }}
            >
              {comments?.map((comment) => (
                <TimelineItem>
                  <TimelineSeparator>
                    <TimelineDot>
                      <CheckCircleOutlineOutlined
                        sx={{ color: "#757575" }}
                      />
                    </TimelineDot>
                    <TimelineConnector />
                  </TimelineSeparator>
                  <TimelineContent sx={{ py: "12px", px: 2 }}>
                    <Card
                      elevation={0}
                      sx={{
                        border: 1,
                        borderColor: "#C4C4C4",
                        borderRadius: "8px",
                        width: "650px",
                      }}
                    >
                      <Box sx={{ padding: "1rem" }}>
                        <Stack spacing={1}>
                          <Grid
                            sx={{
                              display: "flex",
                              justifyContent: "space-between",
                            }}
                          >
                            <Typography
                              sx={{
                                textAlign: "right",
                                color: " #757575",
                                fontWeight: "500",
                                fontSize: "12px",
                              }}
                            >
                              {moment(comment.createdAt).format(
                                "DD MMM YYYY"
                              )}
                            </Typography>
                          </Grid>

                          <Typography
                            sx={{
                              fontSize: "12px",

                              color: " #757575",
                              fontWeight: "500",
                            }}
                          >
                            {comment.user}
                          </Typography>
                          <Typography
                            sx={{
                              fontSize: "12px",
                              color: "#1D1D1D",
                              fontWeight: "600",
                              wordWrap: "break-word",
                            }}
                          >
                            {comment.comment}
                          </Typography>
                        </Stack>
                      </Box>
                    </Card>
                  </TimelineContent>
                </TimelineItem>
              ))}
            </Timeline>
          )}
          {!Boolean(comments.length) && (
            <Typography variant="body2">No Comments Found</Typography>
          )}
          <br />
        </Card>
         )}
    </>
      ];
    }
  });

  const handleWarningDialogClose = () => {
    setOpenMessageDialog(false);
  };

  const handleCreateDialogClose = () => {
    setTestrunStatus(true);
    setOpenCreateDialog(false);
  };

  // console.log("dontrollingarwa", displayData.validToDate?.newValidToDate);
  return (
    <>
      {/* {isLoading === true ? (
        <LoadingComponent />
      ) : ( */}
      <div>
        <ReusableDialog
          dialogState={openMessageDialog}
          openReusableDialog={handleMessageDialogClickOpen}
          closeReusableDialog={handleMessageDialogClose}
          dialogTitle={messageDialogTitle}
          dialogMessage={messageDialogMessage}
          handleDialogConfirm={handleMessageDialogClose}
          dialogOkText={"OK"}
          showExtraButton={handleExtrabutton}
          //handleExtraButton={handleMessageDialogNavigate}
          showCancelButton={true}
          dialogSeverity={messageDialogSeverity}
          handleDialogReject={handleWarningDialogClose}
          handleExtraText={handleExtraText}
          handleExtraButton={handleProceedbutton}
        />

        {successMsg && (
          <ReusableSnackBar
            openSnackBar={openSnackbar}
            alertMsg={messageDialogMessage}
            handleSnackBarClose={handleSnackBarClose}
          />
        )}

        {formValidationErrorItems.length != 0 && (
          <ReusableSnackBar
            openSnackBar={openSnackbarValidation}
            alertMsg={
              "Please enter the following Field: " +
              formValidationErrorItems.join(", ")
            }
            handleSnackBarClose={handleSnackBarCloseValidation}
          />
        )}

        <Dialog
          hideBackdrop={false}
          elevation={2}
          PaperProps={{
            sx: { boxShadow: "none" },
          }}
          open={openRemarkDialog}
          onClose={handleRemarksDialogClose}
        >
          <DialogTitle
            sx={{
              justifyContent: "space-between",
              alignItems: "center",
              height: "max-content",
              padding: ".5rem",
              paddingLeft: "1rem",
              backgroundColor: "#EAE9FF40",
              // borderBottom: "1px solid grey",
              display: "flex",
            }}
          >
            <Typography variant="h6">Remarks</Typography>

            <IconButton
              sx={{ width: "max-content" }}
              onClick={handleRemarksDialogClose}
              children={<CloseIcon />}
            />
          </DialogTitle>

          <DialogContent sx={{ padding: ".5rem 1rem" }}>
            <Stack>
              <Box sx={{ minWidth: 400 }}>
                <FormControl sx={{ height: "auto" }} fullWidth>
                  <TextField
                    sx={{ backgroundColor: "#F5F5F5" }}
                    value={remarks}
                    onChange={handleRemarks}
                    multiline
                    placeholder={"Enter Remarks"}
                    inputProps={{ maxLength: 200 }}
                  ></TextField>
                </FormControl>
              </Box>
            </Stack>
          </DialogContent>
          <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
            <Button
              sx={{ width: "max-content", textTransform: "capitalize" }}
              onClick={handleRemarksDialogClose}
            >
              Cancel
            </Button>
            <Button
              className="button_primary--normal"
              type="save"
              onClick={onCostCenterSubmitRemarks}
              variant="contained"
            >
              Submit
            </Button>
          </DialogActions>
        </Dialog>

        <Dialog
          hideBackdrop={false}
          elevation={2}
          PaperProps={{
            sx: { boxShadow: "none" },
          }}
          open={openCreateDialog}
          onClose={handleCreateDialogClose}
        >
          {/* <Grid
          container
          sx={{ display: "flex", justifyContent: "space-between" }}
        > */}
          {/* <Grid item> */}
          <DialogTitle
            sx={{
              justifyContent: "space-between",
              alignItems: "center",
              height: "max-content",
              padding: ".5rem",
              paddingLeft: "1rem",
              backgroundColor: "#EAE9FF40",
              // borderBottom: "1px solid grey",
              display: "flex",
            }}
          >
            <Typography variant="h6">Remarks</Typography>

            <IconButton
              sx={{ width: "max-content" }}
              onClick={handleCreateDialogClose}
              children={<CloseIcon />}
            />
          </DialogTitle>
          {/* </Grid> */}
          {/* </Grid> */}
          <DialogContent sx={{ padding: ".5rem 1rem" }}>
            <Stack>
              <Box sx={{ minWidth: 400 }}>
                <FormControl sx={{ height: "auto" }} fullWidth>
                  <TextField
                    sx={{ backgroundColor: "#F5F5F5" }}
                    value={remarks}
                    onChange={handleRemarks}
                    multiline
                    placeholder={"Enter Remarks"}
                    inputProps={{ maxLength: 254 }}
                  ></TextField>
                </FormControl>
              </Box>
            </Stack>
            {/* <TextField
            autoFocus
            margin="dense"
            id="name"
            label="Enter Remarks for Correction"
            type="text"
            fullWidth
            variant="standard"
            onChange={handleRemarks}
          /> */}
          </DialogContent>
          <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
            <Button
              sx={{ width: "max-content", textTransform: "capitalize" }}
              onClick={handleCreateDialogClose}
            >
              Cancel
            </Button>
            <Button
              className="button_primary--normal"
              type="save"
              onClick={onCostCenterSubmitRemarks}
              variant="contained"
            >
              Submit
            </Button>
          </DialogActions>
        </Dialog>

        <Backdrop
          sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
          open={blurLoading}
          // onClick={handleClose}
        >
          <CircularProgress color="inherit" />
        </Backdrop>

        <Grid
          container
          style={{
            ...outermostContainer_Information,
            backgroundColor: "#FAFCFF",
          }}
        >
          <Grid sx={{ width: "inherit" }}>
            <Grid item md={12} style={{ padding: "16px", display: "flex" }}>
              <Grid item md={10} sx={{ display: "flex" }}>
                <Grid>
                  <IconButton
                    // onClick={handleBacktoRO}
                    color="primary"
                    aria-label="upload picture"
                    component="label"
                    sx={iconButton_SpacingSmall}
                  >
                    <ArrowCircleLeftOutlinedIcon
                      style={{
                        height: "1em",
                        width: "1em",
                        color: "#000000",
                      }}
                      onClick={() => {
                        navigate("/masterDataCockpit/costCenter");
                        dispatch(clearPayload());
                        dispatch(clearOrgData());
                        dispatch(clearTaskData())
                        dispatch(clearProfitCenterPayloadGI())
                        dispatch(clearCostCenterPayload())
                        dispatch(clearSingleGLPayloadGI())
                        dispatch(clearProfitCenterPayload())
                        dispatch(clearCostCenter())
                      }}
                    />
                  </IconButton>
                </Grid>
                <Grid>
                  <Typography variant="h3">
                    <strong>Display ET Cost Center</strong>
                  </Typography>
                  <Typography variant="body2" color="#777">
                  This view displays the details of ET Cost Center
                  </Typography>
                </Grid>
              </Grid>
              <Grid
            md={3}
            sx={{ display: "flex", justifyContent: "flex-end" }}
            gap={2}
          >
              {userData?.role === "Finance" && //when request type exist as create and change
                (costCenterRowData?.requestType || taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN") && 
               !isEditMode ? (
                <Grid gap={1} sx={{ display: "flex" }}>
                    <Grid
                      gap={1}
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                      }}
                    >
                      <>                    
                        <Grid item>
                          <Button
                            variant="outlined"
                            size="small"
                            sx={button_Outlined}
                            onClick={onEdit}
                          >
                            Change
                            <EditOutlinedIcon
                              sx={{ padding: "2px" }}
                              fontSize="small"
                            />
                          </Button>
                        </Grid>                          
                      </>
                    </Grid>
                  </Grid>
              ):""
          }
            {costCenterRowData?.requestId || taskRowDetails?.requestType ? (
              <Grid>
                <Button
                  variant="outlined"
                  size="small"
                  sx={button_Outlined}
                  onClick={openChangeLog}
                  title="Change Log"
                >
                  <TrackChangesTwoToneIcon
                    sx={{ padding: "2px" }}
                    fontSize="small"
                  />
                </Button>
              </Grid>
            ) : (
              ""
            )}
              {isChangeLogopen && (
                <ChangeLog
                  open={true}
                  closeModal={handleClosemodalData}
                  moduleName={"ET Cost Center Change Log"}
                  requestId={
                    costCenterRowData?.requestId
                      ? costCenterRowData?.requestId
                      : taskRowDetails?.requestId
                  }
                  requestType={
                    costCenterRowData?.requestType
                      ? costCenterRowData?.requestType
                      : taskData?.body?.requestType
                  }
                  pageName={"costCenter"}
                  controllingArea={
                    costCenterRowData?.controllingArea
                      ? costCenterRowData?.controllingArea
                      : taskData?.body?.controllingArea
                  }
                  centerName={
                    costCenterRowData?.costCenter
                      ? costCenterRowData?.costCenter
                      : taskData?.body?.costCenter
                  }
                />
              )}
             
            </Grid>
              {/* <Grid item md={2} sx={{ display: "flex", justifyContent: "end" }}>
                <Button
                  variant="outlined"
                  size="small"
                  sx={button_Outlined}
                  // onClick={openChangeLog}
                  title="Attachment"
                >
                  <Link
                    href="https://sunocoinc.sharepoint.com/teams/et-acct-sapfmd/Sunoco%20Alternate%20PC%20Hierarchy/Forms/AllItems.aspx?id=%2Fteams%2Fet%2Dacct%2Dsapfmd%2FSunoco%20Alternate%20PC%20Hierarchy"
                    target="_blank"
                    rel="noopener noreferrer"
                    color="inherit"
                  >
                    Go to SharePoint
                  </Link>
                  <LinkIcon sx={{ padding: "2px" }} fontSize="small" />
                </Button>
              </Grid> */}
            </Grid>
            {/* ... Your other content ... */}
            <Grid container style={{ padding: "0 1rem 0 1rem" }}>
              <Grid container sx={outermostContainer_Information}>
                <Grid
                  container
                  display="flex"
                  flexDirection="row"
                  flexWrap="nowrap"
                >
                  <Grid
                    item
                    md={10}
                    // width="85%"
                    sx={{ marginLeft: "40px", marginBottom: "20px" }}
                  >
                    <Grid item sx={{ paddingTop: "2px !important" }}>
                      <Stack flexDirection="row">
                        <div style={{ width: "12%" }}>
                          <Typography variant="body2" color="#777">
                            Cost Center
                          </Typography>
                        </div>
                        <Typography
                          variant="body2"
                          fontWeight="bold"
                          justifyContent="flex-start"
                        >
                          : {displayData?.costCenter? displayData?.costCenter:taskData?.costCenter?taskdata?.costCenter:""}
                          {/* {displayData?.costCenterName?.newCostCenterName} */}
                        </Typography>
                      </Stack>
                    </Grid>

                    <Grid item sx={{ paddingTop: "2px !important" }}>
                        <Stack flexDirection="row">
                          <div style={{ width: "12%" }}>
                            <Typography variant="body2" color="#777">
                              Controlling Area
                            </Typography>
                          </div>
                          <Typography variant="body2" fontWeight="bold">
                            :{" "}
                            {
                              iDs?.controllingArea?iDs?.controllingArea:""
                            }
                          </Typography>
                        </Stack>
                      </Grid>
                    <Grid item sx={{ paddingTop: "2px !important" }}>
                      <Stack flexDirection="row">
                        <div style={{ width: "12%" }}>
                          <Typography variant="body2" color="#777">
                            Valid From
                          </Typography>
                        </div>
                        <Typography variant="body2" fontWeight="bold">
                          :{" "}
                          {moment(
                           iDs?.validFrom
                          ).format(appSettings?.dateFormat)}
                        </Typography>
                      </Stack>
                    </Grid>
                    <Grid item sx={{ paddingTop: "2px !important" }}>
                      <Stack flexDirection="row">
                        <div style={{ width: "12%" }}>
                          <Typography variant="body2" color="#777">
                            Valid To
                          </Typography>
                        </div>
                        <Typography variant="body2" fontWeight="bold">
                          :{" "}
                          {moment(
                            iDs?.validTo
                          ).format(appSettings?.dateFormat)}
                        </Typography>
                      </Stack>
                    </Grid>
                  </Grid>
                </Grid>
                <Grid container>
                  <Stepper
                    activeStep={activeStep}
                    sx={{
                      background: "#FFFFFF",
                      borderBottom: "1px solid #BDBDBD",
                      width: "100%",
                      height: "48px",
                    }}
                  >
                   {filteredFactorsArray?.map((factor, index) => (
                <Step key={factor}>
                  <StepLabel sx={{ fontWeight: "700" }}>{factor}</StepLabel>
                </Step>
              ))}
                  </Stepper>
                  {/* {tabContents &&
              tabContents[activeStep]?.map((cardContent, index) => (
                <Box key={index} sx={{ mb: 2, width: "100%" }}>
                  <Typography variant="body2">{cardContent}</Typography>
                </Box>
              ))} */}
                </Grid>

                {/* <Grid container>{getStepContent(activeStep)}</Grid> */}
              </Grid>
            </Grid>
          </Grid>
        </Grid>
   
          <Grid
            gap={1}
            sx={{ display: "flex", justifyContent: "space-between" }}
          >
            {/* {checkIwaAccess(iwaAccessData, "Profit Center", "ChangePC") && */}
              {/* (!profitCenterRowData?.requestType && !isEditMode ? ( */}
                <Paper
                  sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                  elevation={2}
                >
                  <BottomNavigation
                    className="container_BottomNav"
                    showLabels
                    sx={{ display: "flex", justifyContent: "flex-end" }}
                  >
                  {isRolePresent ? (
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary, mr: 1 }}
                  onClick={onRevealClick}
                  // disabled={activeStep === 0}
                >
                  Reveal
                </Button>
              ) : (
                ""
              )}
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleBack}
                      disabled={activeStep === 0}
                    >
                      Back
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleNext}
                      disabled={
                        activeStep === filteredFactorsArray.length - 1 ? true : false
                      }
                    >
                      Next
                    </Button>
                  </BottomNavigation>
                </Paper>
          
              {/* } */}

          </Grid>
      </div>
      {/* )} */}
    </>
  );
};

export default DisplayCostCenter;
