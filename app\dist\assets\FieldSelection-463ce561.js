import{b as ye,r as s,j as y,a as e,b3 as Ce,z as xe,am as ke,G as v,g as Y,A as Z,T as N,f as J,an as De,aE as Fe,t as Me,aD as me,K as Q,Z as X,ck as ve,s as je,F as fe,aj as Ae,ak as qe,al as Ie,bY as We,bZ as He,h as be,I as pe,b1 as Se,b_ as we,cl as Le,v as Ve,w as _e,B as ze,cm as Ke}from"./index-75c1660a.js";import{R as Ee}from"./ReusableFieldCatalog-2b30c987.js";const $e=({})=>{const h=ye();s.useState("");const[C,c]=s.useState(null),[D,F]=s.useState([]),[u,E]=s.useState({}),[Re,ee]=s.useState(null),[T,j]=s.useState({}),[se,te]=s.useState({}),[x,A]=s.useState({}),[ae,q]=s.useState(!1),[ie,oe]=s.useState(!1),[le,I]=s.useState(!1),[W,H]=s.useState(""),[ne,w]=s.useState(!1),[Be,L]=s.useState(!1),[Oe,V]=s.useState(!0),[ce,_]=s.useState(!1),[Ne,R]=s.useState(!1),z=()=>{h("/masterDataCockpit/materialMaster/createMaterialDetail")},de=()=>{h("/masterDataCockpit/materialMaster/createMaterialDetail")},re=()=>{setOpen(!1)},ge=()=>{q(!1),h("/masterDataCockpit/materialMaster/materialSingle")},P=()=>{q(!0)},U=()=>{oe(!0)},he=()=>{const r=b=>{const o=[],M=[];Object.keys(b.body).map(t=>{const g=b.body[t];Object.keys(g).map(a=>{const n=b.body[t][a];if(Array.isArray(n)){let p={heading:a,fields:n.map(l=>l.fieldName),viewName:t,fieldVisibility:n.map(l=>({fieldName:l.fieldName,visibility:l.visibility}))};o.push(p),console.log(o,"hello"),n.forEach(l=>{console.log("Field Name:",l.fieldName),console.log("Is Required:",l.Required),l.Required==="true"&&M.push(l.fieldName)})}})}),F(o),console.log("Required Fields:",M);const k={},O={},i={};o.forEach(t=>{const{heading:g,fields:a,viewName:n,fieldVisibility:p}=t;k[n]||(k[n]={heading:n,subheadings:[]}),k[n].subheadings.push({heading:g,fields:a}),p.forEach(l=>{let B=l.visibility==="Required"?"Mandatory":l.visibility==="Hidden"?"Hide":l.visibility==="0"?"0":"Optional";O[l.fieldName]=B,l.visibility==="0"&&(i[l.fieldName]=!0)})}),E(k),A(O),te(i),j(i),console.log(k,"Fieldset")},m=b=>{console.log(b)};Q(`/${X}/data/getFieldCatalogueDetails?screenName=Change`,"get",r,m)};s.useEffect(()=>{he()},[]);const G=()=>{let r={};Object.keys(u).forEach(o=>{u[o].subheadings.forEach(k=>{const{heading:O,fields:i}=k;i.forEach(t=>{if(x[t]!=="0"&&T[t]){const g=x[t]==="Mandatory"?"Required":x[t]==="Hide"?"Hidden":"Optional";r[o]||(r[o]=[]),r[o].some(n=>n.fieldName===t)||r[o].push({fieldName:t,cardName:O,viewName:o,visibility:g,screenName:"Change"})}})})});const m=o=>{console.log(o,"example"),R(),o.statusCode===200?(console.log("success"),I("Submit"),H("Field Catalog has been submitted successfully"),w("success"),V(!1),_(!0),P(),L(!0),R(!1)):(I("Submit"),_(!1),H("Submission Failed"),w("danger"),V(!1),L(!0),U(),R(!1)),re()},b=o=>{console.log(o)};Object.keys(r).forEach(o=>{const M=r[o];M.length>0?Q(`/${X}/alter/changeVisibility`,"post",m,b,M):console.log(`No payload data to send for viewName: ${o}`)}),dispatch(ve())};return y("div",{children:[e(Ce,{dialogState:ie,openReusableDialog:U,closeReusableDialog:z,dialogTitle:le,dialogMessage:W,handleDialogConfirm:z,dialogOkText:"OK",handleExtraButton:de,dialogSeverity:ne}),ce&&e(xe,{openSnackBar:ae,alertMsg:W,handleSnackBarClose:ge}),e(v,{container:!0,sx:ke,children:e(v,{item:!0,md:12,children:Object.keys(u).map(r=>y(Y,{sx:{mb:2},className:"filter-accordion",children:[e(Z,{sx:{backgroundColor:"#f5f5f5"},children:e(N,{sx:{fontWeight:"700",margin:"0px !important"},children:r})}),e(J,{children:u[r].subheadings.map((m,b)=>y(Y,{sx:{mb:2},children:[e(Z,{expandIcon:e(De,{}),sx:{backgroundColor:"#F1F0FF"},children:e(N,{sx:{fontWeight:"700",margin:"0px !important",fontSize:"14px"},children:m.heading})}),e(J,{children:e("div",{sx:{fontSize:"25px"},children:e(Ee,{fields:m.fields,heading:m.heading,childCheckedStates:T,setChildCheckedStates:j,childRadioValues:x,setChildRadioValues:A,onSubmitButtonClick:()=>G(),mandatoryFields:D,DisabledChildCheck:se})})})]},b))})]},r))})}),e(me,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:e(Fe,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},value:C,onChange:(r,m)=>{ee(D[m]),c(m)},children:e(Me,{size:"small",variant:"contained",onClick:G,children:"Submit"})})})]})},Pe=()=>{const h=document.getElementsByTagName("HTML")[0],C=document.getElementsByTagName("BODY")[0];let c=h.clientWidth,D=C.clientWidth;const F=document.getElementById("e-invoice-export"),u=F.scrollWidth-F.clientWidth;u>F.clientWidth&&(c+=u,D+=u),h.style.width=c+"px",C.style.width=D+"px",Ke(F).then(E=>E.toDataURL("image/png",1)).then(E=>{Ue(E,"FieldCatalog.png"),h.style.width=null,C.style.width=null})},Ue=(h,C)=>{const c=window.document.createElement("a");c.href=h,c.download=C,(document.body||document.documentElement).appendChild(c),typeof c.click=="function"?c.click():(c.target="_blank",c.dispatchEvent(new MouseEvent("click",{view:window,bubbles:!0,cancelable:!0}))),URL.revokeObjectURL(c.href),c.remove()},Ze=({})=>{const h=ye();s.useState("");const[C,c]=s.useState(null),[D,F]=s.useState([]),[u,E]=s.useState({}),[Re,ee]=s.useState(null),[T,j]=s.useState({}),[se,te]=s.useState({}),[x,A]=s.useState({}),[ae,q]=s.useState(!1),[ie,oe]=s.useState(!1),[le,I]=s.useState(!1),[W,H]=s.useState(""),[ne,w]=s.useState(!1),[Be,L]=s.useState(!1),[Oe,V]=s.useState(!0),[ce,_]=s.useState(!1),[Ne,R]=s.useState(!1),[z,de]=s.useState(0),re=["For Create","For Change"],ge=je(),P=()=>{h("/masterDataCockpit/materialMaster/createMaterialDetail")},U=()=>{h("/masterDataCockpit/materialMaster/createMaterialDetail")},he=()=>{setOpen(!1)},G=()=>{q(!1),h("/masterDataCockpit/materialMaster/materialSingle")},r=()=>{q(!0)},m=()=>{oe(!0)},b=()=>{const i=g=>{const a=[],n=[];Object.keys(g.body).map(f=>{const K=g.body[f];Object.keys(K).map($=>{const S=g.body[f][$];if(Array.isArray(S)){let ue={heading:$,fields:S.map(d=>d.fieldName),viewName:f,fieldVisibility:S.map(d=>({fieldName:d.fieldName,visibility:d.visibility}))};a.push(ue),console.log(a,"hello"),S.forEach(d=>{console.log("Field Name:",d.fieldName),console.log("Is Required:",d.Required),d.Required==="true"&&n.push(d.fieldName)})}})}),F(a),console.log("Required Fields:",n);const p={},l={},B={};a.forEach(f=>{const{heading:K,fields:$,viewName:S,fieldVisibility:ue}=f;p[S]||(p[S]={heading:S,subheadings:[]}),p[S].subheadings.push({heading:K,fields:$}),ue.forEach(d=>{let Te=d.visibility==="Required"?"Mandatory":d.visibility==="Hidden"?"Hide":d.visibility==="0"?"0":"Optional";l[d.fieldName]=Te,d.visibility==="0"&&(B[d.fieldName]=!0)})}),E(p),A(l),te(B),j(B),console.log(p,"Fieldset")},t=g=>{console.log(g)};Q(`/${X}/data/getFieldCatalogueDetails?screenName=Create`,"get",i,t)};s.useEffect(()=>{b()},[]);const o=()=>{console.log("Clicked");let i={};Object.keys(u).forEach(a=>{u[a].subheadings.forEach(p=>{const{heading:l,fields:B}=p;B.forEach(f=>{if(x[f]!=="0"&&T[f]){const K=x[f]==="Mandatory"?"Required":x[f]==="Hide"?"Hidden":"Optional";i[a]||(i[a]=[]),i[a].some(S=>S.fieldName===f)||i[a].push({fieldName:f,cardName:l,viewName:a,visibility:K,screenName:"Create"})}})})});const t=a=>{console.log(a,"example"),R(),a.statusCode===200?(console.log("success"),I("Submit"),H("Field Catalog has been submitted successfully"),w("success"),V(!1),_(!0),r(),L(!0),R(!1)):(I("Submit"),_(!1),H("Submission Failed"),w("danger"),V(!1),L(!0),m(),R(!1)),he()},g=a=>{console.log(a)};Object.keys(i).forEach(a=>{const n=i[a];n.length>0?Q(`/${X}/alter/changeVisibility`,"post",t,g,n):console.log(`No payload data to send for viewName: ${a}`)}),ge(ve())},M=[[e(fe,{children:y(v,{container:!0,sx:ke,children:[e(v,{item:!0,md:12,children:Object.keys(u).map(i=>y(Y,{sx:{mb:2},className:"filter-accordion",children:[e(Z,{sx:{backgroundColor:"#f5f5f5"},children:e(N,{sx:{fontWeight:"700",margin:"0px !important"},children:i})}),e(J,{children:u[i].subheadings.map((t,g)=>y(Y,{sx:{mb:2},children:[e(Z,{expandIcon:e(De,{}),sx:{backgroundColor:"#F1F0FF"},children:e(N,{sx:{fontWeight:"700",margin:"0px !important",fontSize:"14px"},children:t.heading})}),e(J,{children:e("div",{sx:{fontSize:"25px"},children:e(Ee,{fields:t.fields,heading:t.heading,childCheckedStates:T,setChildCheckedStates:j,childRadioValues:x,setChildRadioValues:A,onSubmitButtonClick:()=>o(),mandatoryFields:D,DisabledChildCheck:se})})})]},g))})]},i))}),e(me,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:e(Fe,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},value:C,onChange:(i,t)=>{ee(D[t]),c(t)},children:e(Me,{size:"small",variant:"contained",onClick:o,children:"Submit"})})})]})})],[e(fe,{children:e($e,{})})]],k=(i,t)=>{de(t)};s.useState(""),s.useState([]);function O(){}return y("div",{children:[e(Ce,{dialogState:ie,openReusableDialog:m,closeReusableDialog:P,dialogTitle:le,dialogMessage:W,handleDialogConfirm:P,dialogOkText:"OK",handleExtraButton:U,dialogSeverity:ne}),ce&&e(xe,{openSnackBar:ae,alertMsg:W,handleSnackBarClose:G}),e("div",{style:{...Ae,backgroundColor:"#FAFCFF"},children:y(qe,{spacing:1,children:[y(v,{container:!0,sx:Ie,children:[y(v,{item:!0,md:5,sx:We,children:[e(N,{variant:"h3",children:e("strong",{children:"Field Configurations"})}),e(N,{variant:"body2",color:"#777",children:"This view displays the setiings for configuring the Fields"})]}),e(v,{item:!0,md:7,sx:{display:"flex"},children:y(v,{container:!0,direction:"row",justifyContent:"flex-end",alignItems:"center",spacing:0,children:[e(He,{title:"Search for fields in different views",module:"FieldSelection",keyName:"string",message:"Search for fields in different views"}),e(be,{title:"Reload",children:e(pe,{sx:Se,children:e(we,{sx:{"&:hover":{transform:"rotate(360deg)",transition:"0.9s"}},onClick:O})})}),e(be,{title:"Export",children:e(pe,{sx:Se,children:e(Le,{onClick:Pe})})})]})})]}),e(me,{children:e(Ve,{value:z,onChange:k,variant:"scrollable",sx:{background:"#FFF",borderBottom:"1px solid #BDBDBD",width:"100%"},"aria-label":"mui tabs example",children:re.map((i,t)=>e(_e,{sx:{fontSize:"12px",fontWeight:"700"},label:i},t))})}),M[z].map((i,t)=>e(ze,{children:i},t))]})})]})};export{Ze as default};
