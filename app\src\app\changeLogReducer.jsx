import { createSlice } from "@reduxjs/toolkit";
import { handleChangeLogData } from '@helper/helper';

const initialState = {
    createPayloadCopyForChangeLog: [],
    createTemplateArray: [],
    createChangeLogData: {},
}

export const changeLogSlice = createSlice({
    name: "changeLog",
    initialState,
    reducers: {
      setCreatePayloadCopyForChangeLog:(state, action) => {
        state.createPayloadCopyForChangeLog = action.payload
      },
      setCreateTemplateArray: (state, action) => {
        const obj = action.payload;
        const index = state.createTemplateArray.findIndex(
          item => item.ObjectNo === obj.ObjectNo && item.FieldName === obj.FieldName
        );
        
        if (index !== -1) {
          state.createTemplateArray[index] = { ...state.createTemplateArray[index], ...obj };
        } else {
          state.createTemplateArray.push(obj);
        }
      },
      clearCreateTemplateArray: (state) => {
        state.createTemplateArray = initialState.createTemplateArray;
      },
      setCreateChangeLogData: (state, action) => {
        state.createChangeLogData = handleChangeLogData(state.createChangeLogData, action.payload);
      },
      clearCreateChangeLogData: (state) => {
        state.createChangeLogData = initialState.createChangeLogData;
      }
    },
})

export const {
    setCreatePayloadCopyForChangeLog,
    setCreateTemplateArray,
    clearCreateTemplateArray,
    setCreateChangeLogData,
    clearCreateChangeLogData
} = changeLogSlice.actions;

export const changeLogReducer = changeLogSlice.reducer;
