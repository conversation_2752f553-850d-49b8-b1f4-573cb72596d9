import { useDispatch } from 'react-redux';
import { setChangeFieldRows, setChangeFieldRowsDisplay, setChangeLogData } from '@app/payloadslice';
import { CURRENT_DATE, getObjectValue, segregateChangeLogData } from '@helper/helper' 
import { CHANGE_TEMPLATES_FIELD_IDENTIFICATION } from '@constant/changeTemplates';

export const useChangeFieldRows = (
  changeFieldRows,
  changeFieldRowsDisplay,
  paginationData,
  ManipulatedTemplateName,
  userMangmentData,
  requestIdHeader,
  templateArray,
  updateTemplateArray,
  getThePreviousValuesForGroup,
  requestId
) => {
  const dispatch = useDispatch();
  const handleObjectChangeFieldRows = (key, id, field, value, fieldName) => {
    const updatedKeyRows = changeFieldRows[key].map((row) =>
      row?.id === id ? { ...row, [field]: value } : row
    );
    dispatch(setChangeFieldRows({ ...changeFieldRows, [key]: updatedKeyRows }));
    const updatedKeyRowsDisplay = changeFieldRowsDisplay?.[paginationData?.page]?.[key].map((row) =>
      row?.id === id ? { ...row, [field]: value } : row
    );
    dispatch(setChangeFieldRowsDisplay({ ...changeFieldRowsDisplay, [paginationData?.page]: { ...changeFieldRowsDisplay?.[paginationData?.page], [key]: updatedKeyRowsDisplay } }));
    const templateTableName = getObjectValue(ManipulatedTemplateName, key);
    const secondParameterForDifferentiationInCaseGroup = getObjectValue(
      CHANGE_TEMPLATES_FIELD_IDENTIFICATION,
      templateTableName
    );

    updatedKeyRows?.forEach((row) => {
      if (row?.id === id) {
        const changedField = {
          ObjectNo: `${row?.Material}${
            secondParameterForDifferentiationInCaseGroup?.length > 0 
              ? `$$${row[secondParameterForDifferentiationInCaseGroup[0]]}` 
              : ''
          }${
            secondParameterForDifferentiationInCaseGroup?.length > 1 
              ? `$$${row[secondParameterForDifferentiationInCaseGroup[1]]}` 
              : ''
          }`,
          ChangedBy: userMangmentData.emailId,
          ChangedOn: CURRENT_DATE,
          FieldName: fieldName ?? field,
          PreviousValue: getThePreviousValuesForGroup(row, field, key, secondParameterForDifferentiationInCaseGroup) ?? "-",
          SAPValue: getThePreviousValuesForGroup(row, field, key, secondParameterForDifferentiationInCaseGroup) ?? "-",
          CurrentValue: value ?? '',
          tableName: key,
        };
        
        updateTemplateArray(changedField);
        const changeLogPayload = {
          RequestId: requestIdHeader ? requestIdHeader : requestId?.slice(3),
          changeLogId: row?.ChangeLogId ?? null
        };

        const updatedTemplateArray = [...templateArray, changedField];

        Object.entries(ManipulatedTemplateName).forEach(([tableKey, templateValue]) => {
          const matchingObjects = updatedTemplateArray.filter(
            item => item.tableName === tableKey
          );

          if (matchingObjects.length > 0) {
            if (!changeLogPayload[templateValue]) {
              changeLogPayload[templateValue] = [];
            }

            matchingObjects.forEach(obj => {
              const { tableName, ...objWithoutTableName } = obj;
              changeLogPayload[templateValue].push(objWithoutTableName);
            });
          }
        });

        const templateNames = Object.values(ManipulatedTemplateName);
        let finalChangeLogDataPayload = {
          RequestId: changeLogPayload.RequestId,
          changeLogId: changeLogPayload.changeLogId
        };

        templateNames.forEach(templateName => {
          const templateResult = segregateChangeLogData(changeLogPayload, templateName);
          const { RequestId, changeLogId, ...materialData } = templateResult;
          
          Object.entries(materialData).forEach(([material, data]) => {
            if (!finalChangeLogDataPayload[material]) {
              finalChangeLogDataPayload[material] = {};
            }
            finalChangeLogDataPayload[material] = {
              ...finalChangeLogDataPayload[material],
              ...data
            };
          });
        });
        dispatch(setChangeLogData(finalChangeLogDataPayload));
      }
    });
  };

  return { handleObjectChangeFieldRows };
};
