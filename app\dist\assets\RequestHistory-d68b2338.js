import{m as ks,n as Os,o as Zt,r as A,a as f,G as B,j as C,aD as kt,B as P,S as $,x as Y,dU as Lo,y as zt,l as T,T as V,w7 as Fo,w8 as Bo,w9 as ko,wa as Oo,wb as xe,F as rt,cB as Ro,bF as Wt,wc as ze,gh as No,wd as jo,dV as Io,t as ve,z as _o,b7 as $o,we as Wo,h as Tt,ge as Me,wf as Uo,wg as Rs,wh as zo,u as Ko,q as ui,bh as Yo,cP as Go,b as Ho,w0 as Xo,V as hi,aF as di,ar as fi,aC as De,W as pi,aj as Zo,al as Qo,bY as Jo,I as Mt,b1 as qo,e8 as ta,cQ as mi,K as ue,Z as Ve,$ as tt,wi as ea,bq as na,aG as gi,C as ia,b$ as yi,c0 as xi,eV as vi,eK as bi,b0 as sa,a5 as ra}from"./index-75c1660a.js";import{d as oa}from"./AttachFileOutlined-872f8e38.js";import{d as aa}from"./ArrowCircleLeftOutlined-f7b52d40.js";import{a as la}from"./UtilDoc-7fb813ce.js";import{u as ca}from"./useMediaQuery-33e0a836.js";/* empty css            */import"./FileDownloadOutlined-329b8f56.js";import"./VisibilityOutlined-a5a8c4d9.js";import"./DeleteOutlined-fe5b7345.js";import"./Slider-c4e5ff46.js";var dn={},ua=Os;Object.defineProperty(dn,"__esModule",{value:!0});var Ns=dn.default=void 0,ha=ua(ks()),da=Zt;Ns=dn.default=(0,ha.default)((0,da.jsx)("path",{d:"M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2m0 4-8 5-8-5V6l8 5 8-5z"}),"Email");const js=A.createContext({});function fa(t){const e=A.useRef(null);return e.current===null&&(e.current=t()),e.current}const fn=typeof window<"u",pa=fn?A.useLayoutEffect:A.useEffect,pn=A.createContext(null);function mn(t,e){t.indexOf(e)===-1&&t.push(e)}function gn(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const ct=(t,e,n)=>n>e?e:n<t?t:n;let yn=()=>{};const ut={},Is=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t);function _s(t){return typeof t=="object"&&t!==null}const $s=t=>/^0[^.\s]+$/u.test(t);function xn(t){let e;return()=>(e===void 0&&(e=t()),e)}const it=t=>t,ma=(t,e)=>n=>e(t(n)),se=(...t)=>t.reduce(ma),Qt=(t,e,n)=>{const i=e-t;return i===0?1:(n-t)/i};class vn{constructor(){this.subscriptions=[]}add(e){return mn(this.subscriptions,e),()=>gn(this.subscriptions,e)}notify(e,n,i){const s=this.subscriptions.length;if(s)if(s===1)this.subscriptions[0](e,n,i);else for(let o=0;o<s;o++){const r=this.subscriptions[o];r&&r(e,n,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const ot=t=>t*1e3,at=t=>t/1e3;function Ws(t,e){return e?t*(1e3/e):0}const Us=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t,ga=1e-7,ya=12;function xa(t,e,n,i,s){let o,r,a=0;do r=e+(n-e)/2,o=Us(r,i,s)-t,o>0?n=r:e=r;while(Math.abs(o)>ga&&++a<ya);return r}function re(t,e,n,i){if(t===e&&n===i)return it;const s=o=>xa(o,0,1,t,n);return o=>o===0||o===1?o:Us(s(o),e,i)}const zs=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,Ks=t=>e=>1-t(1-e),Ys=re(.33,1.53,.69,.99),bn=Ks(Ys),Gs=zs(bn),Hs=t=>(t*=2)<1?.5*bn(t):.5*(2-Math.pow(2,-10*(t-1))),Tn=t=>1-Math.sin(Math.acos(t)),Xs=Ks(Tn),Zs=zs(Tn),va=re(.42,0,1,1),ba=re(0,0,.58,1),Qs=re(.42,0,.58,1),Ta=t=>Array.isArray(t)&&typeof t[0]!="number",Js=t=>Array.isArray(t)&&typeof t[0]=="number",Sa={linear:it,easeIn:va,easeInOut:Qs,easeOut:ba,circIn:Tn,circInOut:Zs,circOut:Xs,backIn:bn,backInOut:Gs,backOut:Ys,anticipate:Hs},Ca=t=>typeof t=="string",Ti=t=>{if(Js(t)){yn(t.length===4);const[e,n,i,s]=t;return re(e,n,i,s)}else if(Ca(t))return Sa[t];return t},he=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],Si={value:null,addProjectionMetrics:null};function Aa(t,e){let n=new Set,i=new Set,s=!1,o=!1;const r=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1},l=0;function c(h){r.has(h)&&(u.schedule(h),t()),l++,h(a)}const u={schedule:(h,d=!1,m=!1)=>{const b=m&&s?n:i;return d&&r.add(h),b.has(h)||b.add(h),h},cancel:h=>{i.delete(h),r.delete(h)},process:h=>{if(a=h,s){o=!0;return}s=!0,[n,i]=[i,n],n.forEach(c),e&&Si.value&&Si.value.frameloop[e].push(l),l=0,n.clear(),s=!1,o&&(o=!1,u.process(h))}};return u}const wa=40;function qs(t,e){let n=!1,i=!0;const s={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,r=he.reduce((S,D)=>(S[D]=Aa(o,e?D:void 0),S),{}),{setup:a,read:l,resolveKeyframes:c,preUpdate:u,update:h,preRender:d,render:m,postRender:x}=r,b=()=>{const S=ut.useManualTiming?s.timestamp:performance.now();n=!1,ut.useManualTiming||(s.delta=i?1e3/60:Math.max(Math.min(S-s.timestamp,wa),1)),s.timestamp=S,s.isProcessing=!0,a.process(s),l.process(s),c.process(s),u.process(s),h.process(s),d.process(s),m.process(s),x.process(s),s.isProcessing=!1,n&&e&&(i=!1,t(b))},y=()=>{n=!0,i=!0,s.isProcessing||t(b)};return{schedule:he.reduce((S,D)=>{const E=r[D];return S[D]=(F,I=!1,L=!1)=>(n||y(),E.schedule(F,I,L)),S},{}),cancel:S=>{for(let D=0;D<he.length;D++)r[he[D]].cancel(S)},state:s,steps:r}}const{schedule:R,cancel:pt,state:K,steps:Le}=qs(typeof requestAnimationFrame<"u"?requestAnimationFrame:it,!0);let pe;function Ea(){pe=void 0}const Q={now:()=>(pe===void 0&&Q.set(K.isProcessing||ut.useManualTiming?K.timestamp:performance.now()),pe),set:t=>{pe=t,queueMicrotask(Ea)}},tr=t=>e=>typeof e=="string"&&e.startsWith(t),Sn=tr("--"),Pa=tr("var(--"),Cn=t=>Pa(t)?Ma.test(t.split("/*")[0].trim()):!1,Ma=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Nt={test:t=>typeof t=="number",parse:parseFloat,transform:t=>t},Jt={...Nt,transform:t=>ct(0,1,t)},de={...Nt,default:1},Kt=t=>Math.round(t*1e5)/1e5,An=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function Da(t){return t==null}const Va=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,wn=(t,e)=>n=>!!(typeof n=="string"&&Va.test(n)&&n.startsWith(t)||e&&!Da(n)&&Object.prototype.hasOwnProperty.call(n,e)),er=(t,e,n)=>i=>{if(typeof i!="string")return i;const[s,o,r,a]=i.match(An);return{[t]:parseFloat(s),[e]:parseFloat(o),[n]:parseFloat(r),alpha:a!==void 0?parseFloat(a):1}},La=t=>ct(0,255,t),Fe={...Nt,transform:t=>Math.round(La(t))},St={test:wn("rgb","red"),parse:er("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:i=1})=>"rgba("+Fe.transform(t)+", "+Fe.transform(e)+", "+Fe.transform(n)+", "+Kt(Jt.transform(i))+")"};function Fa(t){let e="",n="",i="",s="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),i=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),i=t.substring(3,4),s=t.substring(4,5),e+=e,n+=n,i+=i,s+=s),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(i,16),alpha:s?parseInt(s,16)/255:1}}const Ke={test:wn("#"),parse:Fa,transform:St.transform},oe=t=>({test:e=>typeof e=="string"&&e.endsWith(t)&&e.split(" ").length===1,parse:parseFloat,transform:e=>`${e}${t}`}),ft=oe("deg"),lt=oe("%"),M=oe("px"),Ba=oe("vh"),ka=oe("vw"),Ci=(()=>({...lt,parse:t=>lt.parse(t)/100,transform:t=>lt.transform(t*100)}))(),Dt={test:wn("hsl","hue"),parse:er("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:i=1})=>"hsla("+Math.round(t)+", "+lt.transform(Kt(e))+", "+lt.transform(Kt(n))+", "+Kt(Jt.transform(i))+")"},W={test:t=>St.test(t)||Ke.test(t)||Dt.test(t),parse:t=>St.test(t)?St.parse(t):Dt.test(t)?Dt.parse(t):Ke.parse(t),transform:t=>typeof t=="string"?t:t.hasOwnProperty("red")?St.transform(t):Dt.transform(t),getAnimatableNone:t=>{const e=W.parse(t);return e.alpha=0,W.transform(e)}},Oa=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function Ra(t){var e,n;return isNaN(t)&&typeof t=="string"&&(((e=t.match(An))==null?void 0:e.length)||0)+(((n=t.match(Oa))==null?void 0:n.length)||0)>0}const nr="number",ir="color",Na="var",ja="var(",Ai="${}",Ia=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function qt(t){const e=t.toString(),n=[],i={color:[],number:[],var:[]},s=[];let o=0;const a=e.replace(Ia,l=>(W.test(l)?(i.color.push(o),s.push(ir),n.push(W.parse(l))):l.startsWith(ja)?(i.var.push(o),s.push(Na),n.push(l)):(i.number.push(o),s.push(nr),n.push(parseFloat(l))),++o,Ai)).split(Ai);return{values:n,split:a,indexes:i,types:s}}function sr(t){return qt(t).values}function rr(t){const{split:e,types:n}=qt(t),i=e.length;return s=>{let o="";for(let r=0;r<i;r++)if(o+=e[r],s[r]!==void 0){const a=n[r];a===nr?o+=Kt(s[r]):a===ir?o+=W.transform(s[r]):o+=s[r]}return o}}const _a=t=>typeof t=="number"?0:W.test(t)?W.getAnimatableNone(t):t;function $a(t){const e=sr(t);return rr(t)(e.map(_a))}const mt={test:Ra,parse:sr,createTransformer:rr,getAnimatableNone:$a};function Be(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+(e-t)*6*n:n<1/2?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function Wa({hue:t,saturation:e,lightness:n,alpha:i}){t/=360,e/=100,n/=100;let s=0,o=0,r=0;if(!e)s=o=r=n;else{const a=n<.5?n*(1+e):n+e-n*e,l=2*n-a;s=Be(l,a,t+1/3),o=Be(l,a,t),r=Be(l,a,t-1/3)}return{red:Math.round(s*255),green:Math.round(o*255),blue:Math.round(r*255),alpha:i}}function be(t,e){return n=>n>0?e:t}const N=(t,e,n)=>t+(e-t)*n,ke=(t,e,n)=>{const i=t*t,s=n*(e*e-i)+i;return s<0?0:Math.sqrt(s)},Ua=[Ke,St,Dt],za=t=>Ua.find(e=>e.test(t));function wi(t){const e=za(t);if(!e)return!1;let n=e.parse(t);return e===Dt&&(n=Wa(n)),n}const Ei=(t,e)=>{const n=wi(t),i=wi(e);if(!n||!i)return be(t,e);const s={...n};return o=>(s.red=ke(n.red,i.red,o),s.green=ke(n.green,i.green,o),s.blue=ke(n.blue,i.blue,o),s.alpha=N(n.alpha,i.alpha,o),St.transform(s))},Ye=new Set(["none","hidden"]);function Ka(t,e){return Ye.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}function Ya(t,e){return n=>N(t,e,n)}function En(t){return typeof t=="number"?Ya:typeof t=="string"?Cn(t)?be:W.test(t)?Ei:Xa:Array.isArray(t)?or:typeof t=="object"?W.test(t)?Ei:Ga:be}function or(t,e){const n=[...t],i=n.length,s=t.map((o,r)=>En(o)(o,e[r]));return o=>{for(let r=0;r<i;r++)n[r]=s[r](o);return n}}function Ga(t,e){const n={...t,...e},i={};for(const s in n)t[s]!==void 0&&e[s]!==void 0&&(i[s]=En(t[s])(t[s],e[s]));return s=>{for(const o in i)n[o]=i[o](s);return n}}function Ha(t,e){const n=[],i={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){const o=e.types[s],r=t.indexes[o][i[o]],a=t.values[r]??0;n[s]=a,i[o]++}return n}const Xa=(t,e)=>{const n=mt.createTransformer(e),i=qt(t),s=qt(e);return i.indexes.var.length===s.indexes.var.length&&i.indexes.color.length===s.indexes.color.length&&i.indexes.number.length>=s.indexes.number.length?Ye.has(t)&&!s.values.length||Ye.has(e)&&!i.values.length?Ka(t,e):se(or(Ha(i,s),s.values),n):be(t,e)};function ar(t,e,n){return typeof t=="number"&&typeof e=="number"&&typeof n=="number"?N(t,e,n):En(t)(t,e)}const Za=t=>{const e=({timestamp:n})=>t(n);return{start:(n=!0)=>R.update(e,n),stop:()=>pt(e),now:()=>K.isProcessing?K.timestamp:Q.now()}},lr=(t,e,n=10)=>{let i="";const s=Math.max(Math.round(e/n),2);for(let o=0;o<s;o++)i+=Math.round(t(o/(s-1))*1e4)/1e4+", ";return`linear(${i.substring(0,i.length-2)})`},Te=2e4;function Pn(t){let e=0;const n=50;let i=t.next(e);for(;!i.done&&e<Te;)e+=n,i=t.next(e);return e>=Te?1/0:e}function Qa(t,e=100,n){const i=n({...t,keyframes:[0,e]}),s=Math.min(Pn(i),Te);return{type:"keyframes",ease:o=>i.next(s*o).value/e,duration:at(s)}}const Ja=5;function cr(t,e,n){const i=Math.max(e-Ja,0);return Ws(n-t(i),e-i)}const j={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},Oe=.001;function qa({duration:t=j.duration,bounce:e=j.bounce,velocity:n=j.velocity,mass:i=j.mass}){let s,o,r=1-e;r=ct(j.minDamping,j.maxDamping,r),t=ct(j.minDuration,j.maxDuration,at(t)),r<1?(s=c=>{const u=c*r,h=u*t,d=u-n,m=Ge(c,r),x=Math.exp(-h);return Oe-d/m*x},o=c=>{const h=c*r*t,d=h*n+n,m=Math.pow(r,2)*Math.pow(c,2)*t,x=Math.exp(-h),b=Ge(Math.pow(c,2),r);return(-s(c)+Oe>0?-1:1)*((d-m)*x)/b}):(s=c=>{const u=Math.exp(-c*t),h=(c-n)*t+1;return-Oe+u*h},o=c=>{const u=Math.exp(-c*t),h=(n-c)*(t*t);return u*h});const a=5/t,l=el(s,o,a);if(t=ot(t),isNaN(l))return{stiffness:j.stiffness,damping:j.damping,duration:t};{const c=Math.pow(l,2)*i;return{stiffness:c,damping:r*2*Math.sqrt(i*c),duration:t}}}const tl=12;function el(t,e,n){let i=n;for(let s=1;s<tl;s++)i=i-t(i)/e(i);return i}function Ge(t,e){return t*Math.sqrt(1-e*e)}const nl=["duration","bounce"],il=["stiffness","damping","mass"];function Pi(t,e){return e.some(n=>t[n]!==void 0)}function sl(t){let e={velocity:j.velocity,stiffness:j.stiffness,damping:j.damping,mass:j.mass,isResolvedFromDuration:!1,...t};if(!Pi(t,il)&&Pi(t,nl))if(t.visualDuration){const n=t.visualDuration,i=2*Math.PI/(n*1.2),s=i*i,o=2*ct(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:j.mass,stiffness:s,damping:o}}else{const n=qa(t);e={...e,...n,mass:j.mass},e.isResolvedFromDuration=!0}return e}function Se(t=j.visualDuration,e=j.bounce){const n=typeof t!="object"?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:i,restDelta:s}=n;const o=n.keyframes[0],r=n.keyframes[n.keyframes.length-1],a={done:!1,value:o},{stiffness:l,damping:c,mass:u,duration:h,velocity:d,isResolvedFromDuration:m}=sl({...n,velocity:-at(n.velocity||0)}),x=d||0,b=c/(2*Math.sqrt(l*u)),y=r-o,g=at(Math.sqrt(l/u)),v=Math.abs(y)<5;i||(i=v?j.restSpeed.granular:j.restSpeed.default),s||(s=v?j.restDelta.granular:j.restDelta.default);let S;if(b<1){const E=Ge(g,b);S=F=>{const I=Math.exp(-b*g*F);return r-I*((x+b*g*y)/E*Math.sin(E*F)+y*Math.cos(E*F))}}else if(b===1)S=E=>r-Math.exp(-g*E)*(y+(x+g*y)*E);else{const E=g*Math.sqrt(b*b-1);S=F=>{const I=Math.exp(-b*g*F),L=Math.min(E*F,300);return r-I*((x+b*g*y)*Math.sinh(L)+E*y*Math.cosh(L))/E}}const D={calculatedDuration:m&&h||null,next:E=>{const F=S(E);if(m)a.done=E>=h;else{let I=E===0?x:0;b<1&&(I=E===0?ot(x):cr(S,E,F));const L=Math.abs(I)<=i,U=Math.abs(r-F)<=s;a.done=L&&U}return a.value=a.done?r:F,a},toString:()=>{const E=Math.min(Pn(D),Te),F=lr(I=>D.next(E*I).value,E,30);return E+"ms "+F},toTransition:()=>{}};return D}Se.applyToOptions=t=>{const e=Qa(t,100,Se);return t.ease=e.ease,t.duration=ot(e.duration),t.type="keyframes",t};function He({keyframes:t,velocity:e=0,power:n=.8,timeConstant:i=325,bounceDamping:s=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:c=.5,restSpeed:u}){const h=t[0],d={done:!1,value:h},m=L=>a!==void 0&&L<a||l!==void 0&&L>l,x=L=>a===void 0?l:l===void 0||Math.abs(a-L)<Math.abs(l-L)?a:l;let b=n*e;const y=h+b,g=r===void 0?y:r(y);g!==y&&(b=g-h);const v=L=>-b*Math.exp(-L/i),S=L=>g+v(L),D=L=>{const U=v(L),X=S(L);d.done=Math.abs(U)<=c,d.value=d.done?g:X};let E,F;const I=L=>{m(d.value)&&(E=L,F=Se({keyframes:[d.value,x(d.value)],velocity:cr(S,L,d.value),damping:s,stiffness:o,restDelta:c,restSpeed:u}))};return I(0),{calculatedDuration:null,next:L=>{let U=!1;return!F&&E===void 0&&(U=!0,D(L),I(L)),E!==void 0&&L>=E?F.next(L-E):(!U&&D(L),d)}}}function rl(t,e,n){const i=[],s=n||ut.mix||ar,o=t.length-1;for(let r=0;r<o;r++){let a=s(t[r],t[r+1]);if(e){const l=Array.isArray(e)?e[r]||it:e;a=se(l,a)}i.push(a)}return i}function ol(t,e,{clamp:n=!0,ease:i,mixer:s}={}){const o=t.length;if(yn(o===e.length),o===1)return()=>e[0];if(o===2&&e[0]===e[1])return()=>e[1];const r=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=rl(e,i,s),l=a.length,c=u=>{if(r&&u<t[0])return e[0];let h=0;if(l>1)for(;h<t.length-2&&!(u<t[h+1]);h++);const d=Qt(t[h],t[h+1],u);return a[h](d)};return n?u=>c(ct(t[0],t[o-1],u)):c}function al(t,e){const n=t[t.length-1];for(let i=1;i<=e;i++){const s=Qt(0,e,i);t.push(N(n,1,s))}}function ll(t){const e=[0];return al(e,t.length-1),e}function cl(t,e){return t.map(n=>n*e)}function ul(t,e){return t.map(()=>e||Qs).splice(0,t.length-1)}function Yt({duration:t=300,keyframes:e,times:n,ease:i="easeInOut"}){const s=Ta(i)?i.map(Ti):Ti(i),o={done:!1,value:e[0]},r=cl(n&&n.length===e.length?n:ll(e),t),a=ol(r,e,{ease:Array.isArray(s)?s:ul(e,s)});return{calculatedDuration:t,next:l=>(o.value=a(l),o.done=l>=t,o)}}const hl=t=>t!==null;function Mn(t,{repeat:e,repeatType:n="loop"},i,s=1){const o=t.filter(hl),a=s<0||e&&n!=="loop"&&e%2===1?0:o.length-1;return!a||i===void 0?o[a]:i}const dl={decay:He,inertia:He,tween:Yt,keyframes:Yt,spring:Se};function ur(t){typeof t.type=="string"&&(t.type=dl[t.type])}class Dn{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,n){return this.finished.then(e,n)}}const fl=t=>t/100;class Vn extends Dn{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{var i,s;const{motionValue:n}=this.options;n&&n.updatedAt!==Q.now()&&this.tick(Q.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),(s=(i=this.options).onStop)==null||s.call(i))},this.options=e,this.initAnimation(),this.play(),e.autoplay===!1&&this.pause()}initAnimation(){const{options:e}=this;ur(e);const{type:n=Yt,repeat:i=0,repeatDelay:s=0,repeatType:o,velocity:r=0}=e;let{keyframes:a}=e;const l=n||Yt;l!==Yt&&typeof a[0]!="number"&&(this.mixKeyframes=se(fl,ar(a[0],a[1])),a=[0,100]);const c=l({...e,keyframes:a});o==="mirror"&&(this.mirroredGenerator=l({...e,keyframes:[...a].reverse(),velocity:-r})),c.calculatedDuration===null&&(c.calculatedDuration=Pn(c));const{calculatedDuration:u}=c;this.calculatedDuration=u,this.resolvedDuration=u+s,this.totalDuration=this.resolvedDuration*(i+1)-s,this.generator=c}updateTime(e){const n=Math.round(e-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=n}tick(e,n=!1){const{generator:i,totalDuration:s,mixKeyframes:o,mirroredGenerator:r,resolvedDuration:a,calculatedDuration:l}=this;if(this.startTime===null)return i.next(0);const{delay:c=0,keyframes:u,repeat:h,repeatType:d,repeatDelay:m,type:x,onUpdate:b,finalKeyframe:y}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-s/this.speed,this.startTime)),n?this.currentTime=e:this.updateTime(e);const g=this.currentTime-c*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?g<0:g>s;this.currentTime=Math.max(g,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=s);let S=this.currentTime,D=i;if(h){const L=Math.min(this.currentTime,s)/a;let U=Math.floor(L),X=L%1;!X&&L>=1&&(X=1),X===1&&U--,U=Math.min(U,h+1),!!(U%2)&&(d==="reverse"?(X=1-X,m&&(X-=m/a)):d==="mirror"&&(D=r)),S=ct(0,1,X)*a}const E=v?{done:!1,value:u[0]}:D.next(S);o&&(E.value=o(E.value));let{done:F}=E;!v&&l!==null&&(F=this.playbackSpeed>=0?this.currentTime>=s:this.currentTime<=0);const I=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&F);return I&&x!==He&&(E.value=Mn(u,this.options,y,this.speed)),b&&b(E.value),I&&this.finish(),E}then(e,n){return this.finished.then(e,n)}get duration(){return at(this.calculatedDuration)}get time(){return at(this.currentTime)}set time(e){var n;e=ot(e),this.currentTime=e,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),(n=this.driver)==null||n.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(Q.now());const n=this.playbackSpeed!==e;this.playbackSpeed=e,n&&(this.time=at(this.currentTime))}play(){var s,o;if(this.isStopped)return;const{driver:e=Za,startTime:n}=this.options;this.driver||(this.driver=e(r=>this.tick(r))),(o=(s=this.options).onPlay)==null||o.call(s);const i=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=i):this.holdTime!==null?this.startTime=i-this.holdTime:this.startTime||(this.startTime=n??i),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(Q.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){var e,n;this.notifyFinished(),this.teardown(),this.state="finished",(n=(e=this.options).onComplete)==null||n.call(e)}cancel(){var e,n;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),(n=(e=this.options).onCancel)==null||n.call(e)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){var n;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),(n=this.driver)==null||n.stop(),e.observe(this)}}function pl(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}const Ct=t=>t*180/Math.PI,Xe=t=>{const e=Ct(Math.atan2(t[1],t[0]));return Ze(e)},ml={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:Xe,rotateZ:Xe,skewX:t=>Ct(Math.atan(t[1])),skewY:t=>Ct(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},Ze=t=>(t=t%360,t<0&&(t+=360),t),Mi=Xe,Di=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),Vi=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),gl={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Di,scaleY:Vi,scale:t=>(Di(t)+Vi(t))/2,rotateX:t=>Ze(Ct(Math.atan2(t[6],t[5]))),rotateY:t=>Ze(Ct(Math.atan2(-t[2],t[0]))),rotateZ:Mi,rotate:Mi,skewX:t=>Ct(Math.atan(t[4])),skewY:t=>Ct(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function Qe(t){return t.includes("scale")?1:0}function Je(t,e){if(!t||t==="none")return Qe(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let i,s;if(n)i=gl,s=n;else{const a=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=ml,s=a}if(!s)return Qe(e);const o=i[e],r=s[1].split(",").map(xl);return typeof o=="function"?o(r):r[o]}const yl=(t,e)=>{const{transform:n="none"}=getComputedStyle(t);return Je(n,e)};function xl(t){return parseFloat(t.trim())}const jt=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],It=(()=>new Set(jt))(),Li=t=>t===Nt||t===M,vl=new Set(["x","y","z"]),bl=jt.filter(t=>!vl.has(t));function Tl(t){const e=[];return bl.forEach(n=>{const i=t.getValue(n);i!==void 0&&(e.push([n,i.get()]),i.set(n.startsWith("scale")?1:0))}),e}const At={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>Je(e,"x"),y:(t,{transform:e})=>Je(e,"y")};At.translateX=At.x;At.translateY=At.y;const wt=new Set;let qe=!1,tn=!1,en=!1;function hr(){if(tn){const t=Array.from(wt).filter(i=>i.needsMeasurement),e=new Set(t.map(i=>i.element)),n=new Map;e.forEach(i=>{const s=Tl(i);s.length&&(n.set(i,s),i.render())}),t.forEach(i=>i.measureInitialState()),e.forEach(i=>{i.render();const s=n.get(i);s&&s.forEach(([o,r])=>{var a;(a=i.getValue(o))==null||a.set(r)})}),t.forEach(i=>i.measureEndState()),t.forEach(i=>{i.suspendedScrollY!==void 0&&window.scrollTo(0,i.suspendedScrollY)})}tn=!1,qe=!1,wt.forEach(t=>t.complete(en)),wt.clear()}function dr(){wt.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(tn=!0)})}function Sl(){en=!0,dr(),hr(),en=!1}class Ln{constructor(e,n,i,s,o,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=n,this.name=i,this.motionValue=s,this.element=o,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(wt.add(this),qe||(qe=!0,R.read(dr),R.resolveKeyframes(hr))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:e,name:n,element:i,motionValue:s}=this;if(e[0]===null){const o=s==null?void 0:s.get(),r=e[e.length-1];if(o!==void 0)e[0]=o;else if(i&&n){const a=i.readValue(n,r);a!=null&&(e[0]=a)}e[0]===void 0&&(e[0]=r),s&&o===void 0&&s.set(e[0])}pl(e)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),wt.delete(this)}cancel(){this.state==="scheduled"&&(wt.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const Cl=t=>t.startsWith("--");function Al(t,e,n){Cl(e)?t.style.setProperty(e,n):t.style[e]=n}const wl=xn(()=>window.ScrollTimeline!==void 0),El={};function Pl(t,e){const n=xn(t);return()=>El[e]??n()}const fr=Pl(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),Ut=([t,e,n,i])=>`cubic-bezier(${t}, ${e}, ${n}, ${i})`,Fi={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Ut([0,.65,.55,1]),circOut:Ut([.55,0,1,.45]),backIn:Ut([.31,.01,.66,-.59]),backOut:Ut([.33,1.53,.69,.99])};function pr(t,e){if(t)return typeof t=="function"?fr()?lr(t,e):"ease-out":Js(t)?Ut(t):Array.isArray(t)?t.map(n=>pr(n,e)||Fi.easeOut):Fi[t]}function Ml(t,e,n,{delay:i=0,duration:s=300,repeat:o=0,repeatType:r="loop",ease:a="easeOut",times:l}={},c=void 0){const u={[e]:n};l&&(u.offset=l);const h=pr(a,s);Array.isArray(h)&&(u.easing=h);const d={delay:i,duration:s,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:o+1,direction:r==="reverse"?"alternate":"normal"};return c&&(d.pseudoElement=c),t.animate(u,d)}function mr(t){return typeof t=="function"&&"applyToOptions"in t}function Dl({type:t,...e}){return mr(t)&&fr()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}class Vl extends Dn{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;const{element:n,name:i,keyframes:s,pseudoElement:o,allowFlatten:r=!1,finalKeyframe:a,onComplete:l}=e;this.isPseudoElement=!!o,this.allowFlatten=r,this.options=e,yn(typeof e.type!="string");const c=Dl(e);this.animation=Ml(n,i,s,c,o),c.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!o){const u=Mn(s,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(u):Al(n,i,u),this.animation.cancel()}l==null||l(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){var e,n;(n=(e=this.animation).finish)==null||n.call(e)}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:e}=this;e==="idle"||e==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){var e,n;this.isPseudoElement||(n=(e=this.animation).commitStyles)==null||n.call(e)}get duration(){var n,i;const e=((i=(n=this.animation.effect)==null?void 0:n.getComputedTiming)==null?void 0:i.call(n).duration)||0;return at(Number(e))}get time(){return at(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=ot(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:n}){var i;return this.allowFlatten&&((i=this.animation.effect)==null||i.updateTiming({easing:"linear"})),this.animation.onfinish=null,e&&wl()?(this.animation.timeline=e,it):n(this)}}const gr={anticipate:Hs,backInOut:Gs,circInOut:Zs};function Ll(t){return t in gr}function Fl(t){typeof t.ease=="string"&&Ll(t.ease)&&(t.ease=gr[t.ease])}const Bi=10;class Bl extends Vl{constructor(e){Fl(e),ur(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){const{motionValue:n,onUpdate:i,onComplete:s,element:o,...r}=this.options;if(!n)return;if(e!==void 0){n.set(e);return}const a=new Vn({...r,autoplay:!1}),l=ot(this.finishedTime??this.time);n.setWithVelocity(a.sample(l-Bi).value,a.sample(l).value,Bi),a.stop()}}const ki=(t,e)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(mt.test(t)||t==="0")&&!t.startsWith("url("));function kl(t){const e=t[0];if(t.length===1)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}function Ol(t,e,n,i){const s=t[0];if(s===null)return!1;if(e==="display"||e==="visibility")return!0;const o=t[t.length-1],r=ki(s,e),a=ki(o,e);return!r||!a?!1:kl(t)||(n==="spring"||mr(n))&&i}function yr(t){return _s(t)&&"offsetHeight"in t}const Rl=new Set(["opacity","clipPath","filter","transform"]),Nl=xn(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function jl(t){var c;const{motionValue:e,name:n,repeatDelay:i,repeatType:s,damping:o,type:r}=t;if(!yr((c=e==null?void 0:e.owner)==null?void 0:c.current))return!1;const{onUpdate:a,transformTemplate:l}=e.owner.getProps();return Nl()&&n&&Rl.has(n)&&(n!=="transform"||!l)&&!a&&!i&&s!=="mirror"&&o!==0&&r!=="inertia"}const Il=40;class _l extends Dn{constructor({autoplay:e=!0,delay:n=0,type:i="keyframes",repeat:s=0,repeatDelay:o=0,repeatType:r="loop",keyframes:a,name:l,motionValue:c,element:u,...h}){var x;super(),this.stop=()=>{var b,y;this._animation&&(this._animation.stop(),(b=this.stopTimeline)==null||b.call(this)),(y=this.keyframeResolver)==null||y.cancel()},this.createdAt=Q.now();const d={autoplay:e,delay:n,type:i,repeat:s,repeatDelay:o,repeatType:r,name:l,motionValue:c,element:u,...h},m=(u==null?void 0:u.KeyframeResolver)||Ln;this.keyframeResolver=new m(a,(b,y,g)=>this.onKeyframesResolved(b,y,d,!g),l,c,u),(x=this.keyframeResolver)==null||x.scheduleResolve()}onKeyframesResolved(e,n,i,s){this.keyframeResolver=void 0;const{name:o,type:r,velocity:a,delay:l,isHandoff:c,onUpdate:u}=i;this.resolvedAt=Q.now(),Ol(e,o,r,a)||((ut.instantAnimations||!l)&&(u==null||u(Mn(e,i,n))),e[0]=e[e.length-1],i.duration=0,i.repeat=0);const d={startTime:s?this.resolvedAt?this.resolvedAt-this.createdAt>Il?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:n,...i,keyframes:e},m=!c&&jl(d)?new Bl({...d,element:d.motionValue.owner.current}):new Vn(d);m.finished.then(()=>this.notifyFinished()).catch(it),this.pendingTimeline&&(this.stopTimeline=m.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=m}get finished(){return this._animation?this.animation.finished:this._finished}then(e,n){return this.finished.finally(e).then(()=>{})}get animation(){var e;return this._animation||((e=this.keyframeResolver)==null||e.resume(),Sl()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var e;this._animation&&this.animation.cancel(),(e=this.keyframeResolver)==null||e.cancel()}}const $l=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Wl(t){const e=$l.exec(t);if(!e)return[,];const[,n,i,s]=e;return[`--${n??i}`,s]}function xr(t,e,n=1){const[i,s]=Wl(t);if(!i)return;const o=window.getComputedStyle(e).getPropertyValue(i);if(o){const r=o.trim();return Is(r)?parseFloat(r):r}return Cn(s)?xr(s,e,n+1):s}function Fn(t,e){return(t==null?void 0:t[e])??(t==null?void 0:t.default)??t}const vr=new Set(["width","height","top","left","right","bottom",...jt]),Ul={test:t=>t==="auto",parse:t=>t},br=t=>e=>e.test(t),Tr=[Nt,M,lt,ft,ka,Ba,Ul],Oi=t=>Tr.find(br(t));function zl(t){return typeof t=="number"?t===0:t!==null?t==="none"||t==="0"||$s(t):!0}const Kl=new Set(["brightness","contrast","saturate","opacity"]);function Yl(t){const[e,n]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[i]=n.match(An)||[];if(!i)return t;const s=n.replace(i,"");let o=Kl.has(e)?1:0;return i!==n&&(o*=100),e+"("+o+s+")"}const Gl=/\b([a-z-]*)\(.*?\)/gu,nn={...mt,getAnimatableNone:t=>{const e=t.match(Gl);return e?e.map(Yl).join(" "):t}},Ri={...Nt,transform:Math.round},Hl={rotate:ft,rotateX:ft,rotateY:ft,rotateZ:ft,scale:de,scaleX:de,scaleY:de,scaleZ:de,skew:ft,skewX:ft,skewY:ft,distance:M,translateX:M,translateY:M,translateZ:M,x:M,y:M,z:M,perspective:M,transformPerspective:M,opacity:Jt,originX:Ci,originY:Ci,originZ:M},Bn={borderWidth:M,borderTopWidth:M,borderRightWidth:M,borderBottomWidth:M,borderLeftWidth:M,borderRadius:M,radius:M,borderTopLeftRadius:M,borderTopRightRadius:M,borderBottomRightRadius:M,borderBottomLeftRadius:M,width:M,maxWidth:M,height:M,maxHeight:M,top:M,right:M,bottom:M,left:M,padding:M,paddingTop:M,paddingRight:M,paddingBottom:M,paddingLeft:M,margin:M,marginTop:M,marginRight:M,marginBottom:M,marginLeft:M,backgroundPositionX:M,backgroundPositionY:M,...Hl,zIndex:Ri,fillOpacity:Jt,strokeOpacity:Jt,numOctaves:Ri},Xl={...Bn,color:W,backgroundColor:W,outlineColor:W,fill:W,stroke:W,borderColor:W,borderTopColor:W,borderRightColor:W,borderBottomColor:W,borderLeftColor:W,filter:nn,WebkitFilter:nn},Sr=t=>Xl[t];function Cr(t,e){let n=Sr(t);return n!==nn&&(n=mt),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Zl=new Set(["auto","none","0"]);function Ql(t,e,n){let i=0,s;for(;i<t.length&&!s;){const o=t[i];typeof o=="string"&&!Zl.has(o)&&qt(o).values.length&&(s=t[i]),i++}if(s&&n)for(const o of e)t[o]=Cr(n,s)}class Jl extends Ln{constructor(e,n,i,s,o){super(e,n,i,s,o,!0)}readKeyframes(){const{unresolvedKeyframes:e,element:n,name:i}=this;if(!n||!n.current)return;super.readKeyframes();for(let l=0;l<e.length;l++){let c=e[l];if(typeof c=="string"&&(c=c.trim(),Cn(c))){const u=xr(c,n.current);u!==void 0&&(e[l]=u),l===e.length-1&&(this.finalKeyframe=c)}}if(this.resolveNoneKeyframes(),!vr.has(i)||e.length!==2)return;const[s,o]=e,r=Oi(s),a=Oi(o);if(r!==a)if(Li(r)&&Li(a))for(let l=0;l<e.length;l++){const c=e[l];typeof c=="string"&&(e[l]=parseFloat(c))}else At[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:e,name:n}=this,i=[];for(let s=0;s<e.length;s++)(e[s]===null||zl(e[s]))&&i.push(s);i.length&&Ql(e,i,n)}measureInitialState(){const{element:e,unresolvedKeyframes:n,name:i}=this;if(!e||!e.current)return;i==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=At[i](e.measureViewportBox(),window.getComputedStyle(e.current)),n[0]=this.measuredOrigin;const s=n[n.length-1];s!==void 0&&e.getValue(i,s).jump(s,!1)}measureEndState(){var a;const{element:e,name:n,unresolvedKeyframes:i}=this;if(!e||!e.current)return;const s=e.getValue(n);s&&s.jump(this.measuredOrigin,!1);const o=i.length-1,r=i[o];i[o]=At[n](e.measureViewportBox(),window.getComputedStyle(e.current)),r!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=r),(a=this.removedTransforms)!=null&&a.length&&this.removedTransforms.forEach(([l,c])=>{e.getValue(l).set(c)}),this.resolveNoneKeyframes()}}function ql(t,e,n){if(t instanceof EventTarget)return[t];if(typeof t=="string"){let i=document;e&&(i=e.current);const s=(n==null?void 0:n[t])??i.querySelectorAll(t);return s?Array.from(s):[]}return Array.from(t)}const Ar=(t,e)=>e&&typeof t=="number"?e.transform(t):t,Ni=30,tc=t=>!isNaN(parseFloat(t));class ec{constructor(e,n={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(i,s=!0)=>{var r,a;const o=Q.now();if(this.updatedAt!==o&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(i),this.current!==this.prev&&((r=this.events.change)==null||r.notify(this.current),this.dependents))for(const l of this.dependents)l.dirty();s&&((a=this.events.renderRequest)==null||a.notify(this.current))},this.hasAnimated=!1,this.setCurrent(e),this.owner=n.owner}setCurrent(e){this.current=e,this.updatedAt=Q.now(),this.canTrackVelocity===null&&e!==void 0&&(this.canTrackVelocity=tc(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,n){this.events[e]||(this.events[e]=new vn);const i=this.events[e].add(n);return e==="change"?()=>{i(),R.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,n){this.passiveEffect=e,this.stopPassiveEffect=n}set(e,n=!0){!n||!this.passiveEffect?this.updateAndNotify(e,n):this.passiveEffect(e,this.updateAndNotify)}setWithVelocity(e,n,i){this.set(n),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-i}jump(e,n=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var e;(e=this.events.change)==null||e.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const e=Q.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||e-this.updatedAt>Ni)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,Ni);return Ws(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(e){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=e(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var e,n;(e=this.dependents)==null||e.clear(),(n=this.events.destroy)==null||n.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Ot(t,e){return new ec(t,e)}const{schedule:kn,cancel:gd}=qs(queueMicrotask,!1),st={x:!1,y:!1};function wr(){return st.x||st.y}function nc(t){return t==="x"||t==="y"?st[t]?null:(st[t]=!0,()=>{st[t]=!1}):st.x||st.y?null:(st.x=st.y=!0,()=>{st.x=st.y=!1})}function Er(t,e){const n=ql(t),i=new AbortController,s={passive:!0,...e,signal:i.signal};return[n,s,()=>i.abort()]}function ji(t){return!(t.pointerType==="touch"||wr())}function ic(t,e,n={}){const[i,s,o]=Er(t,n),r=a=>{if(!ji(a))return;const{target:l}=a,c=e(l,a);if(typeof c!="function"||!l)return;const u=h=>{ji(h)&&(c(h),l.removeEventListener("pointerleave",u))};l.addEventListener("pointerleave",u,s)};return i.forEach(a=>{a.addEventListener("pointerenter",r,s)}),o}const Pr=(t,e)=>e?t===e?!0:Pr(t,e.parentElement):!1,On=t=>t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1,sc=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function rc(t){return sc.has(t.tagName)||t.tabIndex!==-1}const me=new WeakSet;function Ii(t){return e=>{e.key==="Enter"&&t(e)}}function Re(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}const oc=(t,e)=>{const n=t.currentTarget;if(!n)return;const i=Ii(()=>{if(me.has(n))return;Re(n,"down");const s=Ii(()=>{Re(n,"up")}),o=()=>Re(n,"cancel");n.addEventListener("keyup",s,e),n.addEventListener("blur",o,e)});n.addEventListener("keydown",i,e),n.addEventListener("blur",()=>n.removeEventListener("keydown",i),e)};function _i(t){return On(t)&&!wr()}function ac(t,e,n={}){const[i,s,o]=Er(t,n),r=a=>{const l=a.currentTarget;if(!_i(a))return;me.add(l);const c=e(l,a),u=(m,x)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",d),me.has(l)&&me.delete(l),_i(m)&&typeof c=="function"&&c(m,{success:x})},h=m=>{u(m,l===window||l===document||n.useGlobalTarget||Pr(l,m.target))},d=m=>{u(m,!1)};window.addEventListener("pointerup",h,s),window.addEventListener("pointercancel",d,s)};return i.forEach(a=>{(n.useGlobalTarget?window:a).addEventListener("pointerdown",r,s),yr(a)&&(a.addEventListener("focus",c=>oc(c,s)),!rc(a)&&!a.hasAttribute("tabindex")&&(a.tabIndex=0))}),o}function Mr(t){return _s(t)&&"ownerSVGElement"in t}function lc(t){return Mr(t)&&t.tagName==="svg"}const G=t=>!!(t&&t.getVelocity),cc=[...Tr,W,mt],uc=t=>cc.find(br(t)),Dr=A.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});function hc(t=!0){const e=A.useContext(pn);if(e===null)return[!0,null];const{isPresent:n,onExitComplete:i,register:s}=e,o=A.useId();A.useEffect(()=>{if(t)return s(o)},[t]);const r=A.useCallback(()=>t&&i&&i(o),[o,i,t]);return!n&&i?[!1,r]:[!0]}const Vr=A.createContext({strict:!1}),$i={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Rt={};for(const t in $i)Rt[t]={isEnabled:e=>$i[t].some(n=>!!e[n])};function dc(t){for(const e in t)Rt[e]={...Rt[e],...t[e]}}const fc=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Ce(t){return t.startsWith("while")||t.startsWith("drag")&&t!=="draggable"||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||fc.has(t)}let Lr=t=>!Ce(t);function pc(t){typeof t=="function"&&(Lr=e=>e.startsWith("on")?!Ce(e):t(e))}try{pc(require("@emotion/is-prop-valid").default)}catch{}function mc(t,e,n){const i={};for(const s in t)s==="values"&&typeof t.values=="object"||(Lr(s)||n===!0&&Ce(s)||!e&&!Ce(s)||t.draggable&&s.startsWith("onDrag"))&&(i[s]=t[s]);return i}function gc(t){if(typeof Proxy>"u")return t;const e=new Map,n=(...i)=>t(...i);return new Proxy(n,{get:(i,s)=>s==="create"?t:(e.has(s)||e.set(s,t(s)),e.get(s))})}const we=A.createContext({});function Ee(t){return t!==null&&typeof t=="object"&&typeof t.start=="function"}function te(t){return typeof t=="string"||Array.isArray(t)}const Rn=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Nn=["initial",...Rn];function Pe(t){return Ee(t.animate)||Nn.some(e=>te(t[e]))}function Fr(t){return!!(Pe(t)||t.variants)}function yc(t,e){if(Pe(t)){const{initial:n,animate:i}=t;return{initial:n===!1||te(n)?n:void 0,animate:te(i)?i:void 0}}return t.inherit!==!1?e:{}}function xc(t){const{initial:e,animate:n}=yc(t,A.useContext(we));return A.useMemo(()=>({initial:e,animate:n}),[Wi(e),Wi(n)])}function Wi(t){return Array.isArray(t)?t.join(" "):t}const vc=Symbol.for("motionComponentSymbol");function Vt(t){return t&&typeof t=="object"&&Object.prototype.hasOwnProperty.call(t,"current")}function bc(t,e,n){return A.useCallback(i=>{i&&t.onMount&&t.onMount(i),e&&(i?e.mount(i):e.unmount()),n&&(typeof n=="function"?n(i):Vt(n)&&(n.current=i))},[e])}const jn=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Tc="framerAppearId",Br="data-"+jn(Tc),kr=A.createContext({});function Sc(t,e,n,i,s){var b,y;const{visualElement:o}=A.useContext(we),r=A.useContext(Vr),a=A.useContext(pn),l=A.useContext(Dr).reducedMotion,c=A.useRef(null);i=i||r.renderer,!c.current&&i&&(c.current=i(t,{visualState:e,parent:o,props:n,presenceContext:a,blockInitialAnimation:a?a.initial===!1:!1,reducedMotionConfig:l}));const u=c.current,h=A.useContext(kr);u&&!u.projection&&s&&(u.type==="html"||u.type==="svg")&&Cc(c.current,n,s,h);const d=A.useRef(!1);A.useInsertionEffect(()=>{u&&d.current&&u.update(n,a)});const m=n[Br],x=A.useRef(!!m&&!((b=window.MotionHandoffIsComplete)!=null&&b.call(window,m))&&((y=window.MotionHasOptimisedAnimation)==null?void 0:y.call(window,m)));return pa(()=>{u&&(d.current=!0,window.MotionIsMounted=!0,u.updateFeatures(),kn.render(u.render),x.current&&u.animationState&&u.animationState.animateChanges())}),A.useEffect(()=>{u&&(!x.current&&u.animationState&&u.animationState.animateChanges(),x.current&&(queueMicrotask(()=>{var g;(g=window.MotionHandoffMarkAsComplete)==null||g.call(window,m)}),x.current=!1))}),u}function Cc(t,e,n,i){const{layoutId:s,layout:o,drag:r,dragConstraints:a,layoutScroll:l,layoutRoot:c,layoutCrossfade:u}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:Or(t.parent)),t.projection.setOptions({layoutId:s,layout:o,alwaysMeasureLayout:!!r||a&&Vt(a),visualElement:t,animationType:typeof o=="string"?o:"both",initialPromotionConfig:i,crossfade:u,layoutScroll:l,layoutRoot:c})}function Or(t){if(t)return t.options.allowProjection!==!1?t.projection:Or(t.parent)}function Ac({preloadedFeatures:t,createVisualElement:e,useRender:n,useVisualState:i,Component:s}){t&&dc(t);function o(a,l){let c;const u={...A.useContext(Dr),...a,layoutId:wc(a)},{isStatic:h}=u,d=xc(a),m=i(a,h);if(!h&&fn){Ec();const x=Pc(u);c=x.MeasureLayout,d.visualElement=Sc(s,m,u,e,x.ProjectionNode)}return Zt.jsxs(we.Provider,{value:d,children:[c&&d.visualElement?Zt.jsx(c,{visualElement:d.visualElement,...u}):null,n(s,a,bc(m,d.visualElement,l),m,h,d.visualElement)]})}o.displayName=`motion.${typeof s=="string"?s:`create(${s.displayName??s.name??""})`}`;const r=A.forwardRef(o);return r[vc]=s,r}function wc({layoutId:t}){const e=A.useContext(js).id;return e&&t!==void 0?e+"-"+t:t}function Ec(t,e){A.useContext(Vr).strict}function Pc(t){const{drag:e,layout:n}=Rt;if(!e&&!n)return{};const i={...e,...n};return{MeasureLayout:e!=null&&e.isEnabled(t)||n!=null&&n.isEnabled(t)?i.MeasureLayout:void 0,ProjectionNode:i.ProjectionNode}}const ee={};function Mc(t){for(const e in t)ee[e]=t[e],Sn(e)&&(ee[e].isCSSVariable=!0)}function Rr(t,{layout:e,layoutId:n}){return It.has(t)||t.startsWith("origin")||(e||n!==void 0)&&(!!ee[t]||t==="opacity")}const Dc={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Vc=jt.length;function Lc(t,e,n){let i="",s=!0;for(let o=0;o<Vc;o++){const r=jt[o],a=t[r];if(a===void 0)continue;let l=!0;if(typeof a=="number"?l=a===(r.startsWith("scale")?1:0):l=parseFloat(a)===0,!l||n){const c=Ar(a,Bn[r]);if(!l){s=!1;const u=Dc[r]||r;i+=`${u}(${c}) `}n&&(e[r]=c)}}return i=i.trim(),n?i=n(e,s?"":i):s&&(i="none"),i}function In(t,e,n){const{style:i,vars:s,transformOrigin:o}=t;let r=!1,a=!1;for(const l in e){const c=e[l];if(It.has(l)){r=!0;continue}else if(Sn(l)){s[l]=c;continue}else{const u=Ar(c,Bn[l]);l.startsWith("origin")?(a=!0,o[l]=u):i[l]=u}}if(e.transform||(r||n?i.transform=Lc(e,t.transform,n):i.transform&&(i.transform="none")),a){const{originX:l="50%",originY:c="50%",originZ:u=0}=o;i.transformOrigin=`${l} ${c} ${u}`}}const _n=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Nr(t,e,n){for(const i in e)!G(e[i])&&!Rr(i,n)&&(t[i]=e[i])}function Fc({transformTemplate:t},e){return A.useMemo(()=>{const n=_n();return In(n,e,t),Object.assign({},n.vars,n.style)},[e])}function Bc(t,e){const n=t.style||{},i={};return Nr(i,n,t),Object.assign(i,Fc(t,e)),i}function kc(t,e){const n={},i=Bc(t,e);return t.drag&&t.dragListener!==!1&&(n.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=t.drag===!0?"none":`pan-${t.drag==="x"?"y":"x"}`),t.tabIndex===void 0&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=i,n}const Oc={offset:"stroke-dashoffset",array:"stroke-dasharray"},Rc={offset:"strokeDashoffset",array:"strokeDasharray"};function Nc(t,e,n=1,i=0,s=!0){t.pathLength=1;const o=s?Oc:Rc;t[o.offset]=M.transform(-i);const r=M.transform(e),a=M.transform(n);t[o.array]=`${r} ${a}`}function jr(t,{attrX:e,attrY:n,attrScale:i,pathLength:s,pathSpacing:o=1,pathOffset:r=0,...a},l,c,u){if(In(t,a,c),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};const{attrs:h,style:d}=t;h.transform&&(d.transform=h.transform,delete h.transform),(d.transform||h.transformOrigin)&&(d.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),d.transform&&(d.transformBox=(u==null?void 0:u.transformBox)??"fill-box",delete h.transformBox),e!==void 0&&(h.x=e),n!==void 0&&(h.y=n),i!==void 0&&(h.scale=i),s!==void 0&&Nc(h,s,o,r,!1)}const Ir=()=>({..._n(),attrs:{}}),_r=t=>typeof t=="string"&&t.toLowerCase()==="svg";function jc(t,e,n,i){const s=A.useMemo(()=>{const o=Ir();return jr(o,e,_r(i),t.transformTemplate,t.style),{...o.attrs,style:{...o.style}}},[e]);if(t.style){const o={};Nr(o,t.style,t),s.style={...o,...s.style}}return s}const Ic=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function $n(t){return typeof t!="string"||t.includes("-")?!1:!!(Ic.indexOf(t)>-1||/[A-Z]/u.test(t))}function _c(t=!1){return(n,i,s,{latestValues:o},r)=>{const l=($n(n)?jc:kc)(i,o,r,n),c=mc(i,typeof n=="string",t),u=n!==A.Fragment?{...c,...l,ref:s}:{},{children:h}=i,d=A.useMemo(()=>G(h)?h.get():h,[h]);return A.createElement(n,{...u,children:d})}}function Ui(t){const e=[{},{}];return t==null||t.values.forEach((n,i)=>{e[0][i]=n.get(),e[1][i]=n.getVelocity()}),e}function Wn(t,e,n,i){if(typeof e=="function"){const[s,o]=Ui(i);e=e(n!==void 0?n:t.custom,s,o)}if(typeof e=="string"&&(e=t.variants&&t.variants[e]),typeof e=="function"){const[s,o]=Ui(i);e=e(n!==void 0?n:t.custom,s,o)}return e}function ge(t){return G(t)?t.get():t}function $c({scrapeMotionValuesFromProps:t,createRenderState:e},n,i,s){return{latestValues:Wc(n,i,s,t),renderState:e()}}const $r=t=>(e,n)=>{const i=A.useContext(we),s=A.useContext(pn),o=()=>$c(t,e,i,s);return n?o():fa(o)};function Wc(t,e,n,i){const s={},o=i(t,{});for(const d in o)s[d]=ge(o[d]);let{initial:r,animate:a}=t;const l=Pe(t),c=Fr(t);e&&c&&!l&&t.inherit!==!1&&(r===void 0&&(r=e.initial),a===void 0&&(a=e.animate));let u=n?n.initial===!1:!1;u=u||r===!1;const h=u?a:r;if(h&&typeof h!="boolean"&&!Ee(h)){const d=Array.isArray(h)?h:[h];for(let m=0;m<d.length;m++){const x=Wn(t,d[m]);if(x){const{transitionEnd:b,transition:y,...g}=x;for(const v in g){let S=g[v];if(Array.isArray(S)){const D=u?S.length-1:0;S=S[D]}S!==null&&(s[v]=S)}for(const v in b)s[v]=b[v]}}}return s}function Un(t,e,n){var o;const{style:i}=t,s={};for(const r in i)(G(i[r])||e.style&&G(e.style[r])||Rr(r,t)||((o=n==null?void 0:n.getValue(r))==null?void 0:o.liveStyle)!==void 0)&&(s[r]=i[r]);return s}const Uc={useVisualState:$r({scrapeMotionValuesFromProps:Un,createRenderState:_n})};function Wr(t,e,n){const i=Un(t,e,n);for(const s in t)if(G(t[s])||G(e[s])){const o=jt.indexOf(s)!==-1?"attr"+s.charAt(0).toUpperCase()+s.substring(1):s;i[o]=t[s]}return i}const zc={useVisualState:$r({scrapeMotionValuesFromProps:Wr,createRenderState:Ir})};function Kc(t,e){return function(i,{forwardMotionProps:s}={forwardMotionProps:!1}){const r={...$n(i)?zc:Uc,preloadedFeatures:t,useRender:_c(s),createVisualElement:e,Component:i};return Ac(r)}}function ne(t,e,n){const i=t.getProps();return Wn(i,e,n!==void 0?n:i.custom,t)}const sn=t=>Array.isArray(t);function Yc(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,Ot(n))}function Gc(t){return sn(t)?t[t.length-1]||0:t}function Hc(t,e){const n=ne(t,e);let{transitionEnd:i={},transition:s={},...o}=n||{};o={...o,...i};for(const r in o){const a=Gc(o[r]);Yc(t,r,a)}}function Xc(t){return!!(G(t)&&t.add)}function rn(t,e){const n=t.getValue("willChange");if(Xc(n))return n.add(e);if(!n&&ut.WillChange){const i=new ut.WillChange("auto");t.addValue("willChange",i),i.add(e)}}function Ur(t){return t.props[Br]}const Zc=t=>t!==null;function Qc(t,{repeat:e,repeatType:n="loop"},i){const s=t.filter(Zc),o=e&&n!=="loop"&&e%2===1?0:s.length-1;return!o||i===void 0?s[o]:i}const Jc={type:"spring",stiffness:500,damping:25,restSpeed:10},qc=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),tu={type:"keyframes",duration:.8},eu={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},nu=(t,{keyframes:e})=>e.length>2?tu:It.has(t)?t.startsWith("scale")?qc(e[1]):Jc:eu;function iu({when:t,delay:e,delayChildren:n,staggerChildren:i,staggerDirection:s,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:c,...u}){return!!Object.keys(u).length}const zn=(t,e,n,i={},s,o)=>r=>{const a=Fn(i,t)||{},l=a.delay||i.delay||0;let{elapsed:c=0}=i;c=c-ot(l);const u={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-c,onUpdate:d=>{e.set(d),a.onUpdate&&a.onUpdate(d)},onComplete:()=>{r(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:s};iu(a)||Object.assign(u,nu(t,u)),u.duration&&(u.duration=ot(u.duration)),u.repeatDelay&&(u.repeatDelay=ot(u.repeatDelay)),u.from!==void 0&&(u.keyframes[0]=u.from);let h=!1;if((u.type===!1||u.duration===0&&!u.repeatDelay)&&(u.duration=0,u.delay===0&&(h=!0)),(ut.instantAnimations||ut.skipAnimations)&&(h=!0,u.duration=0,u.delay=0),u.allowFlatten=!a.type&&!a.ease,h&&!o&&e.get()!==void 0){const d=Qc(u.keyframes,a);if(d!==void 0){R.update(()=>{u.onUpdate(d),u.onComplete()});return}}return a.isSync?new Vn(u):new _l(u)};function su({protectedKeys:t,needsAnimating:e},n){const i=t.hasOwnProperty(n)&&e[n]!==!0;return e[n]=!1,i}function zr(t,e,{delay:n=0,transitionOverride:i,type:s}={}){let{transition:o=t.getDefaultTransition(),transitionEnd:r,...a}=e;i&&(o=i);const l=[],c=s&&t.animationState&&t.animationState.getState()[s];for(const u in a){const h=t.getValue(u,t.latestValues[u]??null),d=a[u];if(d===void 0||c&&su(c,u))continue;const m={delay:n,...Fn(o||{},u)},x=h.get();if(x!==void 0&&!h.isAnimating&&!Array.isArray(d)&&d===x&&!m.velocity)continue;let b=!1;if(window.MotionHandoffAnimation){const g=Ur(t);if(g){const v=window.MotionHandoffAnimation(g,u,R);v!==null&&(m.startTime=v,b=!0)}}rn(t,u),h.start(zn(u,h,d,t.shouldReduceMotion&&vr.has(u)?{type:!1}:m,t,b));const y=h.animation;y&&l.push(y)}return r&&Promise.all(l).then(()=>{R.update(()=>{r&&Hc(t,r)})}),l}function on(t,e,n={}){var l;const i=ne(t,e,n.type==="exit"?(l=t.presenceContext)==null?void 0:l.custom:void 0);let{transition:s=t.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(s=n.transitionOverride);const o=i?()=>Promise.all(zr(t,i,n)):()=>Promise.resolve(),r=t.variantChildren&&t.variantChildren.size?(c=0)=>{const{delayChildren:u=0,staggerChildren:h,staggerDirection:d}=s;return ru(t,e,c,u,h,d,n)}:()=>Promise.resolve(),{when:a}=s;if(a){const[c,u]=a==="beforeChildren"?[o,r]:[r,o];return c().then(()=>u())}else return Promise.all([o(),r(n.delay)])}function ru(t,e,n=0,i=0,s=0,o=1,r){const a=[],l=t.variantChildren.size,c=(l-1)*s,u=typeof i=="function",h=u?d=>i(d,l):o===1?(d=0)=>d*s:(d=0)=>c-d*s;return Array.from(t.variantChildren).sort(ou).forEach((d,m)=>{d.notify("AnimationStart",e),a.push(on(d,e,{...r,delay:n+(u?0:i)+h(m)}).then(()=>d.notify("AnimationComplete",e)))}),Promise.all(a)}function ou(t,e){return t.sortNodePosition(e)}function au(t,e,n={}){t.notify("AnimationStart",e);let i;if(Array.isArray(e)){const s=e.map(o=>on(t,o,n));i=Promise.all(s)}else if(typeof e=="string")i=on(t,e,n);else{const s=typeof e=="function"?ne(t,e,n.custom):e;i=Promise.all(zr(t,s,n))}return i.then(()=>{t.notify("AnimationComplete",e)})}function Kr(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let i=0;i<n;i++)if(e[i]!==t[i])return!1;return!0}const lu=Nn.length;function Yr(t){if(!t)return;if(!t.isControllingVariants){const n=t.parent?Yr(t.parent)||{}:{};return t.props.initial!==void 0&&(n.initial=t.props.initial),n}const e={};for(let n=0;n<lu;n++){const i=Nn[n],s=t.props[i];(te(s)||s===!1)&&(e[i]=s)}return e}const cu=[...Rn].reverse(),uu=Rn.length;function hu(t){return e=>Promise.all(e.map(({animation:n,options:i})=>au(t,n,i)))}function du(t){let e=hu(t),n=zi(),i=!0;const s=l=>(c,u)=>{var d;const h=ne(t,u,l==="exit"?(d=t.presenceContext)==null?void 0:d.custom:void 0);if(h){const{transition:m,transitionEnd:x,...b}=h;c={...c,...b,...x}}return c};function o(l){e=l(t)}function r(l){const{props:c}=t,u=Yr(t.parent)||{},h=[],d=new Set;let m={},x=1/0;for(let y=0;y<uu;y++){const g=cu[y],v=n[g],S=c[g]!==void 0?c[g]:u[g],D=te(S),E=g===l?v.isActive:null;E===!1&&(x=y);let F=S===u[g]&&S!==c[g]&&D;if(F&&i&&t.manuallyAnimateOnMount&&(F=!1),v.protectedKeys={...m},!v.isActive&&E===null||!S&&!v.prevProp||Ee(S)||typeof S=="boolean")continue;const I=fu(v.prevProp,S);let L=I||g===l&&v.isActive&&!F&&D||y>x&&D,U=!1;const X=Array.isArray(S)?S:[S];let yt=X.reduce(s(g),{});E===!1&&(yt={});const{prevResolvedValues:le={}}=v,Yn={...le,...yt},ce=z=>{L=!0,d.has(z)&&(U=!0,d.delete(z)),v.needsAnimating[z]=!0;const J=t.getValue(z);J&&(J.liveStyle=!1)};for(const z in Yn){const J=yt[z],_t=le[z];if(m.hasOwnProperty(z))continue;let Et=!1;sn(J)&&sn(_t)?Et=!Kr(J,_t):Et=J!==_t,Et?J!=null?ce(z):d.add(z):J!==void 0&&d.has(z)?ce(z):v.protectedKeys[z]=!0}v.prevProp=S,v.prevResolvedValues=yt,v.isActive&&(m={...m,...yt}),i&&t.blockInitialAnimation&&(L=!1),L&&(!(F&&I)||U)&&h.push(...X.map(z=>({animation:z,options:{type:g}})))}if(d.size){const y={};if(typeof c.initial!="boolean"){const g=ne(t,Array.isArray(c.initial)?c.initial[0]:c.initial);g&&g.transition&&(y.transition=g.transition)}d.forEach(g=>{const v=t.getBaseTarget(g),S=t.getValue(g);S&&(S.liveStyle=!0),y[g]=v??null}),h.push({animation:y})}let b=!!h.length;return i&&(c.initial===!1||c.initial===c.animate)&&!t.manuallyAnimateOnMount&&(b=!1),i=!1,b?e(h):Promise.resolve()}function a(l,c){var h;if(n[l].isActive===c)return Promise.resolve();(h=t.variantChildren)==null||h.forEach(d=>{var m;return(m=d.animationState)==null?void 0:m.setActive(l,c)}),n[l].isActive=c;const u=r(l);for(const d in n)n[d].protectedKeys={};return u}return{animateChanges:r,setActive:a,setAnimateFunction:o,getState:()=>n,reset:()=>{n=zi(),i=!0}}}function fu(t,e){return typeof e=="string"?e!==t:Array.isArray(e)?!Kr(e,t):!1}function vt(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function zi(){return{animate:vt(!0),whileInView:vt(),whileHover:vt(),whileTap:vt(),whileDrag:vt(),whileFocus:vt(),exit:vt()}}class gt{constructor(e){this.isMounted=!1,this.node=e}update(){}}class pu extends gt{constructor(e){super(e),e.animationState||(e.animationState=du(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();Ee(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:n}=this.node.prevProps||{};e!==n&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),(e=this.unmountControls)==null||e.call(this)}}let mu=0;class gu extends gt{constructor(){super(...arguments),this.id=mu++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:n}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===i)return;const s=this.node.animationState.setActive("exit",!e);n&&!e&&s.then(()=>{n(this.id)})}mount(){const{register:e,onExitComplete:n}=this.node.presenceContext||{};n&&n(this.id),e&&(this.unmount=e(this.id))}unmount(){}}const yu={animation:{Feature:pu},exit:{Feature:gu}};function ie(t,e,n,i={passive:!0}){return t.addEventListener(e,n,i),()=>t.removeEventListener(e,n)}function ae(t){return{point:{x:t.pageX,y:t.pageY}}}const xu=t=>e=>On(e)&&t(e,ae(e));function Gt(t,e,n,i){return ie(t,e,xu(n),i)}function Gr({top:t,left:e,right:n,bottom:i}){return{x:{min:e,max:n},y:{min:t,max:i}}}function vu({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function bu(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),i=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:i.y,right:i.x}}const Hr=1e-4,Tu=1-Hr,Su=1+Hr,Xr=.01,Cu=0-Xr,Au=0+Xr;function H(t){return t.max-t.min}function wu(t,e,n){return Math.abs(t-e)<=n}function Ki(t,e,n,i=.5){t.origin=i,t.originPoint=N(e.min,e.max,t.origin),t.scale=H(n)/H(e),t.translate=N(n.min,n.max,t.origin)-t.originPoint,(t.scale>=Tu&&t.scale<=Su||isNaN(t.scale))&&(t.scale=1),(t.translate>=Cu&&t.translate<=Au||isNaN(t.translate))&&(t.translate=0)}function Ht(t,e,n,i){Ki(t.x,e.x,n.x,i?i.originX:void 0),Ki(t.y,e.y,n.y,i?i.originY:void 0)}function Yi(t,e,n){t.min=n.min+e.min,t.max=t.min+H(e)}function Eu(t,e,n){Yi(t.x,e.x,n.x),Yi(t.y,e.y,n.y)}function Gi(t,e,n){t.min=e.min-n.min,t.max=t.min+H(e)}function Xt(t,e,n){Gi(t.x,e.x,n.x),Gi(t.y,e.y,n.y)}const Hi=()=>({translate:0,scale:1,origin:0,originPoint:0}),Lt=()=>({x:Hi(),y:Hi()}),Xi=()=>({min:0,max:0}),_=()=>({x:Xi(),y:Xi()});function nt(t){return[t("x"),t("y")]}function Ne(t){return t===void 0||t===1}function an({scale:t,scaleX:e,scaleY:n}){return!Ne(t)||!Ne(e)||!Ne(n)}function bt(t){return an(t)||Zr(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function Zr(t){return Zi(t.x)||Zi(t.y)}function Zi(t){return t&&t!=="0%"}function Ae(t,e,n){const i=t-n,s=e*i;return n+s}function Qi(t,e,n,i,s){return s!==void 0&&(t=Ae(t,s,i)),Ae(t,n,i)+e}function ln(t,e=0,n=1,i,s){t.min=Qi(t.min,e,n,i,s),t.max=Qi(t.max,e,n,i,s)}function Qr(t,{x:e,y:n}){ln(t.x,e.translate,e.scale,e.originPoint),ln(t.y,n.translate,n.scale,n.originPoint)}const Ji=.999999999999,qi=1.0000000000001;function Pu(t,e,n,i=!1){const s=n.length;if(!s)return;e.x=e.y=1;let o,r;for(let a=0;a<s;a++){o=n[a],r=o.projectionDelta;const{visualElement:l}=o.options;l&&l.props.style&&l.props.style.display==="contents"||(i&&o.options.layoutScroll&&o.scroll&&o!==o.root&&Bt(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,Qr(t,r)),i&&bt(o.latestValues)&&Bt(t,o.latestValues))}e.x<qi&&e.x>Ji&&(e.x=1),e.y<qi&&e.y>Ji&&(e.y=1)}function Ft(t,e){t.min=t.min+e,t.max=t.max+e}function ts(t,e,n,i,s=.5){const o=N(t.min,t.max,s);ln(t,e,n,o,i)}function Bt(t,e){ts(t.x,e.x,e.scaleX,e.scale,e.originX),ts(t.y,e.y,e.scaleY,e.scale,e.originY)}function Jr(t,e){return Gr(bu(t.getBoundingClientRect(),e))}function Mu(t,e,n){const i=Jr(t,n),{scroll:s}=e;return s&&(Ft(i.x,s.offset.x),Ft(i.y,s.offset.y)),i}const qr=({current:t})=>t?t.ownerDocument.defaultView:null,es=(t,e)=>Math.abs(t-e);function Du(t,e){const n=es(t.x,e.x),i=es(t.y,e.y);return Math.sqrt(n**2+i**2)}class to{constructor(e,n,{transformPagePoint:i,contextWindow:s=window,dragSnapToOrigin:o=!1,distanceThreshold:r=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const d=Ie(this.lastMoveEventInfo,this.history),m=this.startEvent!==null,x=Du(d.offset,{x:0,y:0})>=this.distanceThreshold;if(!m&&!x)return;const{point:b}=d,{timestamp:y}=K;this.history.push({...b,timestamp:y});const{onStart:g,onMove:v}=this.handlers;m||(g&&g(this.lastMoveEvent,d),this.startEvent=this.lastMoveEvent),v&&v(this.lastMoveEvent,d)},this.handlePointerMove=(d,m)=>{this.lastMoveEvent=d,this.lastMoveEventInfo=je(m,this.transformPagePoint),R.update(this.updatePoint,!0)},this.handlePointerUp=(d,m)=>{this.end();const{onEnd:x,onSessionEnd:b,resumeAnimation:y}=this.handlers;if(this.dragSnapToOrigin&&y&&y(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const g=Ie(d.type==="pointercancel"?this.lastMoveEventInfo:je(m,this.transformPagePoint),this.history);this.startEvent&&x&&x(d,g),b&&b(d,g)},!On(e))return;this.dragSnapToOrigin=o,this.handlers=n,this.transformPagePoint=i,this.distanceThreshold=r,this.contextWindow=s||window;const a=ae(e),l=je(a,this.transformPagePoint),{point:c}=l,{timestamp:u}=K;this.history=[{...c,timestamp:u}];const{onSessionStart:h}=n;h&&h(e,Ie(l,this.history)),this.removeListeners=se(Gt(this.contextWindow,"pointermove",this.handlePointerMove),Gt(this.contextWindow,"pointerup",this.handlePointerUp),Gt(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),pt(this.updatePoint)}}function je(t,e){return e?{point:e(t.point)}:t}function ns(t,e){return{x:t.x-e.x,y:t.y-e.y}}function Ie({point:t},e){return{point:t,delta:ns(t,eo(e)),offset:ns(t,Vu(e)),velocity:Lu(e,.1)}}function Vu(t){return t[0]}function eo(t){return t[t.length-1]}function Lu(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,i=null;const s=eo(t);for(;n>=0&&(i=t[n],!(s.timestamp-i.timestamp>ot(e)));)n--;if(!i)return{x:0,y:0};const o=at(s.timestamp-i.timestamp);if(o===0)return{x:0,y:0};const r={x:(s.x-i.x)/o,y:(s.y-i.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function Fu(t,{min:e,max:n},i){return e!==void 0&&t<e?t=i?N(e,t,i.min):Math.max(t,e):n!==void 0&&t>n&&(t=i?N(n,t,i.max):Math.min(t,n)),t}function is(t,e,n){return{min:e!==void 0?t.min+e:void 0,max:n!==void 0?t.max+n-(t.max-t.min):void 0}}function Bu(t,{top:e,left:n,bottom:i,right:s}){return{x:is(t.x,n,s),y:is(t.y,e,i)}}function ss(t,e){let n=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,i]=[i,n]),{min:n,max:i}}function ku(t,e){return{x:ss(t.x,e.x),y:ss(t.y,e.y)}}function Ou(t,e){let n=.5;const i=H(t),s=H(e);return s>i?n=Qt(e.min,e.max-i,t.min):i>s&&(n=Qt(t.min,t.max-s,e.min)),ct(0,1,n)}function Ru(t,e){const n={};return e.min!==void 0&&(n.min=e.min-t.min),e.max!==void 0&&(n.max=e.max-t.min),n}const cn=.35;function Nu(t=cn){return t===!1?t=0:t===!0&&(t=cn),{x:rs(t,"left","right"),y:rs(t,"top","bottom")}}function rs(t,e,n){return{min:os(t,e),max:os(t,n)}}function os(t,e){return typeof t=="number"?t:t[e]||0}const ju=new WeakMap;class Iu{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=_(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=e}start(e,{snapToCursor:n=!1,distanceThreshold:i}={}){const{presenceContext:s}=this.visualElement;if(s&&s.isPresent===!1)return;const o=h=>{const{dragSnapToOrigin:d}=this.getProps();d?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(ae(h).point)},r=(h,d)=>{const{drag:m,dragPropagation:x,onDragStart:b}=this.getProps();if(m&&!x&&(this.openDragLock&&this.openDragLock(),this.openDragLock=nc(m),!this.openDragLock))return;this.latestPointerEvent=h,this.latestPanInfo=d,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),nt(g=>{let v=this.getAxisMotionValue(g).get()||0;if(lt.test(v)){const{projection:S}=this.visualElement;if(S&&S.layout){const D=S.layout.layoutBox[g];D&&(v=H(D)*(parseFloat(v)/100))}}this.originPoint[g]=v}),b&&R.postRender(()=>b(h,d)),rn(this.visualElement,"transform");const{animationState:y}=this.visualElement;y&&y.setActive("whileDrag",!0)},a=(h,d)=>{this.latestPointerEvent=h,this.latestPanInfo=d;const{dragPropagation:m,dragDirectionLock:x,onDirectionLock:b,onDrag:y}=this.getProps();if(!m&&!this.openDragLock)return;const{offset:g}=d;if(x&&this.currentDirection===null){this.currentDirection=_u(g),this.currentDirection!==null&&b&&b(this.currentDirection);return}this.updateAxis("x",d.point,g),this.updateAxis("y",d.point,g),this.visualElement.render(),y&&y(h,d)},l=(h,d)=>{this.latestPointerEvent=h,this.latestPanInfo=d,this.stop(h,d),this.latestPointerEvent=null,this.latestPanInfo=null},c=()=>nt(h=>{var d;return this.getAnimationState(h)==="paused"&&((d=this.getAxisMotionValue(h).animation)==null?void 0:d.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new to(e,{onSessionStart:o,onStart:r,onMove:a,onSessionEnd:l,resumeAnimation:c},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,distanceThreshold:i,contextWindow:qr(this.visualElement)})}stop(e,n){const i=e||this.latestPointerEvent,s=n||this.latestPanInfo,o=this.isDragging;if(this.cancel(),!o||!s||!i)return;const{velocity:r}=s;this.startAnimation(r);const{onDragEnd:a}=this.getProps();a&&R.postRender(()=>a(i,s))}cancel(){this.isDragging=!1;const{projection:e,animationState:n}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(e,n,i){const{drag:s}=this.getProps();if(!i||!fe(e,s,this.currentDirection))return;const o=this.getAxisMotionValue(e);let r=this.originPoint[e]+i[e];this.constraints&&this.constraints[e]&&(r=Fu(r,this.constraints[e],this.elastic[e])),o.set(r)}resolveConstraints(){var o;const{dragConstraints:e,dragElastic:n}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(o=this.visualElement.projection)==null?void 0:o.layout,s=this.constraints;e&&Vt(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&i?this.constraints=Bu(i.layoutBox,e):this.constraints=!1,this.elastic=Nu(n),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&nt(r=>{this.constraints!==!1&&this.getAxisMotionValue(r)&&(this.constraints[r]=Ru(i.layoutBox[r],this.constraints[r]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:n}=this.getProps();if(!e||!Vt(e))return!1;const i=e.current,{projection:s}=this.visualElement;if(!s||!s.layout)return!1;const o=Mu(i,s.root,this.visualElement.getTransformPagePoint());let r=ku(s.layout.layoutBox,o);if(n){const a=n(vu(r));this.hasMutatedConstraints=!!a,a&&(r=Gr(a))}return r}startAnimation(e){const{drag:n,dragMomentum:i,dragElastic:s,dragTransition:o,dragSnapToOrigin:r,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},c=nt(u=>{if(!fe(u,n,this.currentDirection))return;let h=l&&l[u]||{};r&&(h={min:0,max:0});const d=s?200:1e6,m=s?40:1e7,x={type:"inertia",velocity:i?e[u]:0,bounceStiffness:d,bounceDamping:m,timeConstant:750,restDelta:1,restSpeed:10,...o,...h};return this.startAxisValueAnimation(u,x)});return Promise.all(c).then(a)}startAxisValueAnimation(e,n){const i=this.getAxisMotionValue(e);return rn(this.visualElement,e),i.start(zn(e,i,0,n,this.visualElement,!1))}stopAnimation(){nt(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){nt(e=>{var n;return(n=this.getAxisMotionValue(e).animation)==null?void 0:n.pause()})}getAnimationState(e){var n;return(n=this.getAxisMotionValue(e).animation)==null?void 0:n.state}getAxisMotionValue(e){const n=`_drag${e.toUpperCase()}`,i=this.visualElement.getProps(),s=i[n];return s||this.visualElement.getValue(e,(i.initial?i.initial[e]:void 0)||0)}snapToCursor(e){nt(n=>{const{drag:i}=this.getProps();if(!fe(n,i,this.currentDirection))return;const{projection:s}=this.visualElement,o=this.getAxisMotionValue(n);if(s&&s.layout){const{min:r,max:a}=s.layout.layoutBox[n];o.set(e[n]-N(r,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:n}=this.getProps(),{projection:i}=this.visualElement;if(!Vt(n)||!i||!this.constraints)return;this.stopAnimation();const s={x:0,y:0};nt(r=>{const a=this.getAxisMotionValue(r);if(a&&this.constraints!==!1){const l=a.get();s[r]=Ou({min:l,max:l},this.constraints[r])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),nt(r=>{if(!fe(r,e,null))return;const a=this.getAxisMotionValue(r),{min:l,max:c}=this.constraints[r];a.set(N(l,c,s[r]))})}addListeners(){if(!this.visualElement.current)return;ju.set(this.visualElement,this);const e=this.visualElement.current,n=Gt(e,"pointerdown",l=>{const{drag:c,dragListener:u=!0}=this.getProps();c&&u&&this.start(l)}),i=()=>{const{dragConstraints:l}=this.getProps();Vt(l)&&l.current&&(this.constraints=this.resolveRefConstraints())},{projection:s}=this.visualElement,o=s.addEventListener("measure",i);s&&!s.layout&&(s.root&&s.root.updateScroll(),s.updateLayout()),R.read(i);const r=ie(window,"resize",()=>this.scalePositionWithinConstraints()),a=s.addEventListener("didUpdate",({delta:l,hasLayoutChanged:c})=>{this.isDragging&&c&&(nt(u=>{const h=this.getAxisMotionValue(u);h&&(this.originPoint[u]+=l[u].translate,h.set(h.get()+l[u].translate))}),this.visualElement.render())});return()=>{r(),n(),o(),a&&a()}}getProps(){const e=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:i=!1,dragPropagation:s=!1,dragConstraints:o=!1,dragElastic:r=cn,dragMomentum:a=!0}=e;return{...e,drag:n,dragDirectionLock:i,dragPropagation:s,dragConstraints:o,dragElastic:r,dragMomentum:a}}}function fe(t,e,n){return(e===!0||e===t)&&(n===null||n===t)}function _u(t,e=10){let n=null;return Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x"),n}class $u extends gt{constructor(e){super(e),this.removeGroupControls=it,this.removeListeners=it,this.controls=new Iu(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||it}unmount(){this.removeGroupControls(),this.removeListeners()}}const as=t=>(e,n)=>{t&&R.postRender(()=>t(e,n))};class Wu extends gt{constructor(){super(...arguments),this.removePointerDownListener=it}onPointerDown(e){this.session=new to(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:qr(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:n,onPan:i,onPanEnd:s}=this.node.getProps();return{onSessionStart:as(e),onStart:as(n),onMove:i,onEnd:(o,r)=>{delete this.session,s&&R.postRender(()=>s(o,r))}}}mount(){this.removePointerDownListener=Gt(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const ye={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function ls(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const $t={correct:(t,e)=>{if(!e.target)return t;if(typeof t=="string")if(M.test(t))t=parseFloat(t);else return t;const n=ls(t,e.target.x),i=ls(t,e.target.y);return`${n}% ${i}%`}},Uu={correct:(t,{treeScale:e,projectionDelta:n})=>{const i=t,s=mt.parse(t);if(s.length>5)return i;const o=mt.createTransformer(t),r=typeof s[0]!="number"?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;s[0+r]/=a,s[1+r]/=l;const c=N(a,l,.5);return typeof s[2+r]=="number"&&(s[2+r]/=c),typeof s[3+r]=="number"&&(s[3+r]/=c),o(s)}};let cs=!1;class zu extends A.Component{componentDidMount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:i,layoutId:s}=this.props,{projection:o}=e;Mc(Ku),o&&(n.group&&n.group.add(o),i&&i.register&&s&&i.register(o),cs&&o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),ye.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:n,visualElement:i,drag:s,isPresent:o}=this.props,{projection:r}=i;return r&&(r.isPresent=o,cs=!0,s||e.layoutDependency!==n||n===void 0||e.isPresent!==o?r.willUpdate():this.safeToRemove(),e.isPresent!==o&&(o?r.promote():r.relegate()||R.postRender(()=>{const a=r.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),kn.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:i}=this.props,{projection:s}=e;s&&(s.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function no(t){const[e,n]=hc(),i=A.useContext(js);return Zt.jsx(zu,{...t,layoutGroup:i,switchLayoutGroup:A.useContext(kr),isPresent:e,safeToRemove:n})}const Ku={borderRadius:{...$t,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:$t,borderTopRightRadius:$t,borderBottomLeftRadius:$t,borderBottomRightRadius:$t,boxShadow:Uu};function Yu(t,e,n){const i=G(t)?t:Ot(t);return i.start(zn("",i,e,n)),i.animation}const Gu=(t,e)=>t.depth-e.depth;class Hu{constructor(){this.children=[],this.isDirty=!1}add(e){mn(this.children,e),this.isDirty=!0}remove(e){gn(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(Gu),this.isDirty=!1,this.children.forEach(e)}}function Xu(t,e){const n=Q.now(),i=({timestamp:s})=>{const o=s-n;o>=e&&(pt(i),t(o-e))};return R.setup(i,!0),()=>pt(i)}const io=["TopLeft","TopRight","BottomLeft","BottomRight"],Zu=io.length,us=t=>typeof t=="string"?parseFloat(t):t,hs=t=>typeof t=="number"||M.test(t);function Qu(t,e,n,i,s,o){s?(t.opacity=N(0,n.opacity??1,Ju(i)),t.opacityExit=N(e.opacity??1,0,qu(i))):o&&(t.opacity=N(e.opacity??1,n.opacity??1,i));for(let r=0;r<Zu;r++){const a=`border${io[r]}Radius`;let l=ds(e,a),c=ds(n,a);if(l===void 0&&c===void 0)continue;l||(l=0),c||(c=0),l===0||c===0||hs(l)===hs(c)?(t[a]=Math.max(N(us(l),us(c),i),0),(lt.test(c)||lt.test(l))&&(t[a]+="%")):t[a]=c}(e.rotate||n.rotate)&&(t.rotate=N(e.rotate||0,n.rotate||0,i))}function ds(t,e){return t[e]!==void 0?t[e]:t.borderRadius}const Ju=so(0,.5,Xs),qu=so(.5,.95,it);function so(t,e,n){return i=>i<t?0:i>e?1:n(Qt(t,e,i))}function fs(t,e){t.min=e.min,t.max=e.max}function et(t,e){fs(t.x,e.x),fs(t.y,e.y)}function ps(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function ms(t,e,n,i,s){return t-=e,t=Ae(t,1/n,i),s!==void 0&&(t=Ae(t,1/s,i)),t}function th(t,e=0,n=1,i=.5,s,o=t,r=t){if(lt.test(e)&&(e=parseFloat(e),e=N(r.min,r.max,e/100)-r.min),typeof e!="number")return;let a=N(o.min,o.max,i);t===o&&(a-=e),t.min=ms(t.min,e,n,a,s),t.max=ms(t.max,e,n,a,s)}function gs(t,e,[n,i,s],o,r){th(t,e[n],e[i],e[s],e.scale,o,r)}const eh=["x","scaleX","originX"],nh=["y","scaleY","originY"];function ys(t,e,n,i){gs(t.x,e,eh,n?n.x:void 0,i?i.x:void 0),gs(t.y,e,nh,n?n.y:void 0,i?i.y:void 0)}function xs(t){return t.translate===0&&t.scale===1}function ro(t){return xs(t.x)&&xs(t.y)}function vs(t,e){return t.min===e.min&&t.max===e.max}function ih(t,e){return vs(t.x,e.x)&&vs(t.y,e.y)}function bs(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function oo(t,e){return bs(t.x,e.x)&&bs(t.y,e.y)}function Ts(t){return H(t.x)/H(t.y)}function Ss(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class sh{constructor(){this.members=[]}add(e){mn(this.members,e),e.scheduleRender()}remove(e){if(gn(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(e){const n=this.members.findIndex(s=>e===s);if(n===0)return!1;let i;for(let s=n;s>=0;s--){const o=this.members[s];if(o.isPresent!==!1){i=o;break}}return i?(this.promote(i),!0):!1}promote(e,n){const i=this.lead;if(e!==i&&(this.prevLead=i,this.lead=e,e.show(),i)){i.instance&&i.scheduleRender(),e.scheduleRender(),e.resumeFrom=i,n&&(e.resumeFrom.preserveOpacity=!0),i.snapshot&&(e.snapshot=i.snapshot,e.snapshot.latestValues=i.animationValues||i.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:s}=e.options;s===!1&&i.hide()}}exitAnimationComplete(){this.members.forEach(e=>{const{options:n,resumingFrom:i}=e;n.onExitComplete&&n.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function rh(t,e,n){let i="";const s=t.x.translate/e.x,o=t.y.translate/e.y,r=(n==null?void 0:n.z)||0;if((s||o||r)&&(i=`translate3d(${s}px, ${o}px, ${r}px) `),(e.x!==1||e.y!==1)&&(i+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:c,rotate:u,rotateX:h,rotateY:d,skewX:m,skewY:x}=n;c&&(i=`perspective(${c}px) ${i}`),u&&(i+=`rotate(${u}deg) `),h&&(i+=`rotateX(${h}deg) `),d&&(i+=`rotateY(${d}deg) `),m&&(i+=`skewX(${m}deg) `),x&&(i+=`skewY(${x}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return(a!==1||l!==1)&&(i+=`scale(${a}, ${l})`),i||"none"}const _e=["","X","Y","Z"],oh=1e3;let ah=0;function $e(t,e,n,i){const{latestValues:s}=e;s[t]&&(n[t]=s[t],e.setStaticValue(t,0),i&&(i[t]=0))}function ao(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const n=Ur(e);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:s,layoutId:o}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",R,!(s||o))}const{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&ao(i)}function lo({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:i,resetTransform:s}){return class{constructor(r={},a=e==null?void 0:e()){this.id=ah++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(uh),this.nodes.forEach(ph),this.nodes.forEach(mh),this.nodes.forEach(hh)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=r,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new Hu)}addEventListener(r,a){return this.eventHandlers.has(r)||this.eventHandlers.set(r,new vn),this.eventHandlers.get(r).add(a)}notifyListeners(r,...a){const l=this.eventHandlers.get(r);l&&l.notify(...a)}hasListeners(r){return this.eventHandlers.has(r)}mount(r){if(this.instance)return;this.isSVG=Mr(r)&&!lc(r),this.instance=r;const{layoutId:a,layout:l,visualElement:c}=this.options;if(c&&!c.current&&c.mount(r),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(l||a)&&(this.isLayoutDirty=!0),t){let u,h=0;const d=()=>this.root.updateBlockedByResize=!1;R.read(()=>{h=window.innerWidth}),t(r,()=>{const m=window.innerWidth;m!==h&&(h=m,this.root.updateBlockedByResize=!0,u&&u(),u=Xu(d,250),ye.hasAnimatedSinceResize&&(ye.hasAnimatedSinceResize=!1,this.nodes.forEach(ws)))})}a&&this.root.registerSharedNode(a,this),this.options.animate!==!1&&c&&(a||l)&&this.addEventListener("didUpdate",({delta:u,hasLayoutChanged:h,hasRelativeLayoutChanged:d,layout:m})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const x=this.options.transition||c.getDefaultTransition()||bh,{onLayoutAnimationStart:b,onLayoutAnimationComplete:y}=c.getProps(),g=!this.targetLayout||!oo(this.targetLayout,m),v=!h&&d;if(this.options.layoutRoot||this.resumeFrom||v||h&&(g||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const S={...Fn(x,"layout"),onPlay:b,onComplete:y};(c.shouldReduceMotion||this.options.layoutRoot)&&(S.delay=0,S.type=!1),this.startAnimation(S),this.setAnimationOrigin(u,v)}else h||ws(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=m})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const r=this.getStack();r&&r.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),pt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(gh),this.animationId++)}getTransformTemplate(){const{visualElement:r}=this.options;return r&&r.getProps().transformTemplate}willUpdate(r=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&ao(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let u=0;u<this.path.length;u++){const h=this.path[u];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const c=this.getTransformTemplate();this.prevTransformTemplateValue=c?c(this.latestValues,""):void 0,this.updateSnapshot(),r&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Cs);return}if(this.animationId<=this.animationCommitId){this.nodes.forEach(As);return}this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(fh),this.nodes.forEach(lh),this.nodes.forEach(ch)):this.nodes.forEach(As),this.clearAllSnapshots();const a=Q.now();K.delta=ct(0,1e3/60,a-K.timestamp),K.timestamp=a,K.isProcessing=!0,Le.update.process(K),Le.preRender.process(K),Le.render.process(K),K.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,kn.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(dh),this.sharedNodes.forEach(yh)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,R.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){R.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!H(this.snapshot.measuredBox.x)&&!H(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const r=this.layout;this.layout=this.measure(!1),this.layoutCorrected=_(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,r?r.layoutBox:void 0)}updateScroll(r="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===r&&(a=!1),a&&this.instance){const l=i(this.instance);this.scroll={animationId:this.root.animationId,phase:r,isRoot:l,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:l}}}resetTransform(){if(!s)return;const r=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!ro(this.projectionDelta),l=this.getTransformTemplate(),c=l?l(this.latestValues,""):void 0,u=c!==this.prevTransformTemplateValue;r&&this.instance&&(a||bt(this.latestValues)||u)&&(s(this.instance,c),this.shouldResetTransform=!1,this.scheduleRender())}measure(r=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return r&&(l=this.removeTransform(l)),Th(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){var c;const{visualElement:r}=this.options;if(!r)return _();const a=r.measureViewportBox();if(!(((c=this.scroll)==null?void 0:c.wasRoot)||this.path.some(Sh))){const{scroll:u}=this.root;u&&(Ft(a.x,u.offset.x),Ft(a.y,u.offset.y))}return a}removeElementScroll(r){var l;const a=_();if(et(a,r),(l=this.scroll)!=null&&l.wasRoot)return a;for(let c=0;c<this.path.length;c++){const u=this.path[c],{scroll:h,options:d}=u;u!==this.root&&h&&d.layoutScroll&&(h.wasRoot&&et(a,r),Ft(a.x,h.offset.x),Ft(a.y,h.offset.y))}return a}applyTransform(r,a=!1){const l=_();et(l,r);for(let c=0;c<this.path.length;c++){const u=this.path[c];!a&&u.options.layoutScroll&&u.scroll&&u!==u.root&&Bt(l,{x:-u.scroll.offset.x,y:-u.scroll.offset.y}),bt(u.latestValues)&&Bt(l,u.latestValues)}return bt(this.latestValues)&&Bt(l,this.latestValues),l}removeTransform(r){const a=_();et(a,r);for(let l=0;l<this.path.length;l++){const c=this.path[l];if(!c.instance||!bt(c.latestValues))continue;an(c.latestValues)&&c.updateSnapshot();const u=_(),h=c.measurePageBox();et(u,h),ys(a,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,u)}return bt(this.latestValues)&&ys(a,this.latestValues),a}setTargetDelta(r){this.targetDelta=r,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(r){this.options={...this.options,...r,crossfade:r.crossfade!==void 0?r.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==K.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(r=!1){var d;const a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);const l=!!this.resumingFrom||this!==a;if(!(r||l&&this.isSharedProjectionDirty||this.isProjectionDirty||(d=this.parent)!=null&&d.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:u,layoutId:h}=this.options;if(!(!this.layout||!(u||h))){if(this.resolvedRelativeTargetAt=K.timestamp,!this.targetDelta&&!this.relativeTarget){const m=this.getClosestProjectingParent();m&&m.layout&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=_(),this.relativeTargetOrigin=_(),Xt(this.relativeTargetOrigin,this.layout.layoutBox,m.layout.layoutBox),et(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=_(),this.targetWithTransforms=_()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),Eu(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):et(this.target,this.layout.layoutBox),Qr(this.target,this.targetDelta)):et(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const m=this.getClosestProjectingParent();m&&!!m.resumingFrom==!!this.resumingFrom&&!m.options.layoutScroll&&m.target&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=_(),this.relativeTargetOrigin=_(),Xt(this.relativeTargetOrigin,this.target,m.target),et(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||an(this.parent.latestValues)||Zr(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var x;const r=this.getLead(),a=!!this.resumingFrom||this!==r;let l=!0;if((this.isProjectionDirty||(x=this.parent)!=null&&x.isProjectionDirty)&&(l=!1),a&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(l=!1),this.resolvedRelativeTargetAt===K.timestamp&&(l=!1),l)return;const{layout:c,layoutId:u}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(c||u))return;et(this.layoutCorrected,this.layout.layoutBox);const h=this.treeScale.x,d=this.treeScale.y;Pu(this.layoutCorrected,this.treeScale,this.path,a),r.layout&&!r.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(r.target=r.layout.layoutBox,r.targetWithTransforms=_());const{target:m}=r;if(!m){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(ps(this.prevProjectionDelta.x,this.projectionDelta.x),ps(this.prevProjectionDelta.y,this.projectionDelta.y)),Ht(this.projectionDelta,this.layoutCorrected,m,this.latestValues),(this.treeScale.x!==h||this.treeScale.y!==d||!Ss(this.projectionDelta.x,this.prevProjectionDelta.x)||!Ss(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",m))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(r=!0){var a;if((a=this.options.visualElement)==null||a.scheduleRender(),r){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=Lt(),this.projectionDelta=Lt(),this.projectionDeltaWithTransform=Lt()}setAnimationOrigin(r,a=!1){const l=this.snapshot,c=l?l.latestValues:{},u={...this.latestValues},h=Lt();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const d=_(),m=l?l.source:void 0,x=this.layout?this.layout.source:void 0,b=m!==x,y=this.getStack(),g=!y||y.members.length<=1,v=!!(b&&!g&&this.options.crossfade===!0&&!this.path.some(vh));this.animationProgress=0;let S;this.mixTargetDelta=D=>{const E=D/1e3;Es(h.x,r.x,E),Es(h.y,r.y,E),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Xt(d,this.layout.layoutBox,this.relativeParent.layout.layoutBox),xh(this.relativeTarget,this.relativeTargetOrigin,d,E),S&&ih(this.relativeTarget,S)&&(this.isProjectionDirty=!1),S||(S=_()),et(S,this.relativeTarget)),b&&(this.animationValues=u,Qu(u,c,this.latestValues,E,v,g)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=E},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(r){var a,l,c;this.notifyListeners("animationStart"),(a=this.currentAnimation)==null||a.stop(),(c=(l=this.resumingFrom)==null?void 0:l.currentAnimation)==null||c.stop(),this.pendingAnimation&&(pt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=R.update(()=>{ye.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=Ot(0)),this.currentAnimation=Yu(this.motionValue,[0,1e3],{...r,velocity:0,isSync:!0,onUpdate:u=>{this.mixTargetDelta(u),r.onUpdate&&r.onUpdate(u)},onStop:()=>{},onComplete:()=>{r.onComplete&&r.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const r=this.getStack();r&&r.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(oh),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const r=this.getLead();let{targetWithTransforms:a,target:l,layout:c,latestValues:u}=r;if(!(!a||!l||!c)){if(this!==r&&this.layout&&c&&co(this.options.animationType,this.layout.layoutBox,c.layoutBox)){l=this.target||_();const h=H(this.layout.layoutBox.x);l.x.min=r.target.x.min,l.x.max=l.x.min+h;const d=H(this.layout.layoutBox.y);l.y.min=r.target.y.min,l.y.max=l.y.min+d}et(a,l),Bt(a,u),Ht(this.projectionDeltaWithTransform,this.layoutCorrected,a,u)}}registerSharedNode(r,a){this.sharedNodes.has(r)||this.sharedNodes.set(r,new sh),this.sharedNodes.get(r).add(a);const c=a.options.initialPromotionConfig;a.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(a):void 0})}isLead(){const r=this.getStack();return r?r.lead===this:!0}getLead(){var a;const{layoutId:r}=this.options;return r?((a=this.getStack())==null?void 0:a.lead)||this:this}getPrevLead(){var a;const{layoutId:r}=this.options;return r?(a=this.getStack())==null?void 0:a.prevLead:void 0}getStack(){const{layoutId:r}=this.options;if(r)return this.root.sharedNodes.get(r)}promote({needsReset:r,transition:a,preserveFollowOpacity:l}={}){const c=this.getStack();c&&c.promote(this,l),r&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const r=this.getStack();return r?r.relegate(this):!1}resetSkewAndRotation(){const{visualElement:r}=this.options;if(!r)return;let a=!1;const{latestValues:l}=r;if((l.z||l.rotate||l.rotateX||l.rotateY||l.rotateZ||l.skewX||l.skewY)&&(a=!0),!a)return;const c={};l.z&&$e("z",r,c,this.animationValues);for(let u=0;u<_e.length;u++)$e(`rotate${_e[u]}`,r,c,this.animationValues),$e(`skew${_e[u]}`,r,c,this.animationValues);r.render();for(const u in c)r.setStaticValue(u,c[u]),this.animationValues&&(this.animationValues[u]=c[u]);r.scheduleRender()}applyProjectionStyles(r,a){if(!this.instance||this.isSVG)return;if(!this.isVisible){r.visibility="hidden";return}const l=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,r.visibility="",r.opacity="",r.pointerEvents=ge(a==null?void 0:a.pointerEvents)||"",r.transform=l?l(this.latestValues,""):"none";return}const c=this.getLead();if(!this.projectionDelta||!this.layout||!c.target){this.options.layoutId&&(r.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,r.pointerEvents=ge(a==null?void 0:a.pointerEvents)||""),this.hasProjected&&!bt(this.latestValues)&&(r.transform=l?l({},""):"none",this.hasProjected=!1);return}r.visibility="";const u=c.animationValues||c.latestValues;this.applyTransformsToTarget();let h=rh(this.projectionDeltaWithTransform,this.treeScale,u);l&&(h=l(u,h)),r.transform=h;const{x:d,y:m}=this.projectionDelta;r.transformOrigin=`${d.origin*100}% ${m.origin*100}% 0`,c.animationValues?r.opacity=c===this?u.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:u.opacityExit:r.opacity=c===this?u.opacity!==void 0?u.opacity:"":u.opacityExit!==void 0?u.opacityExit:0;for(const x in ee){if(u[x]===void 0)continue;const{correct:b,applyTo:y,isCSSVariable:g}=ee[x],v=h==="none"?u[x]:b(u[x],c);if(y){const S=y.length;for(let D=0;D<S;D++)r[y[D]]=v}else g?this.options.visualElement.renderState.vars[x]=v:r[x]=v}this.options.layoutId&&(r.pointerEvents=c===this?ge(a==null?void 0:a.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(r=>{var a;return(a=r.currentAnimation)==null?void 0:a.stop()}),this.root.nodes.forEach(Cs),this.root.sharedNodes.clear()}}}function lh(t){t.updateLayout()}function ch(t){var n;const e=((n=t.resumeFrom)==null?void 0:n.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){const{layoutBox:i,measuredBox:s}=t.layout,{animationType:o}=t.options,r=e.source!==t.layout.source;o==="size"?nt(h=>{const d=r?e.measuredBox[h]:e.layoutBox[h],m=H(d);d.min=i[h].min,d.max=d.min+m}):co(o,e.layoutBox,i)&&nt(h=>{const d=r?e.measuredBox[h]:e.layoutBox[h],m=H(i[h]);d.max=d.min+m,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[h].max=t.relativeTarget[h].min+m)});const a=Lt();Ht(a,i,e.layoutBox);const l=Lt();r?Ht(l,t.applyTransform(s,!0),e.measuredBox):Ht(l,i,e.layoutBox);const c=!ro(a);let u=!1;if(!t.resumeFrom){const h=t.getClosestProjectingParent();if(h&&!h.resumeFrom){const{snapshot:d,layout:m}=h;if(d&&m){const x=_();Xt(x,e.layoutBox,d.layoutBox);const b=_();Xt(b,i,m.layoutBox),oo(x,b)||(u=!0),h.options.layoutRoot&&(t.relativeTarget=b,t.relativeTargetOrigin=x,t.relativeParent=h)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:l,layoutDelta:a,hasLayoutChanged:c,hasRelativeLayoutChanged:u})}else if(t.isLead()){const{onExitComplete:i}=t.options;i&&i()}t.options.transition=void 0}function uh(t){t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function hh(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function dh(t){t.clearSnapshot()}function Cs(t){t.clearMeasurements()}function As(t){t.isLayoutDirty=!1}function fh(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function ws(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function ph(t){t.resolveTargetDelta()}function mh(t){t.calcProjection()}function gh(t){t.resetSkewAndRotation()}function yh(t){t.removeLeadSnapshot()}function Es(t,e,n){t.translate=N(e.translate,0,n),t.scale=N(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function Ps(t,e,n,i){t.min=N(e.min,n.min,i),t.max=N(e.max,n.max,i)}function xh(t,e,n,i){Ps(t.x,e.x,n.x,i),Ps(t.y,e.y,n.y,i)}function vh(t){return t.animationValues&&t.animationValues.opacityExit!==void 0}const bh={duration:.45,ease:[.4,0,.1,1]},Ms=t=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),Ds=Ms("applewebkit/")&&!Ms("chrome/")?Math.round:it;function Vs(t){t.min=Ds(t.min),t.max=Ds(t.max)}function Th(t){Vs(t.x),Vs(t.y)}function co(t,e,n){return t==="position"||t==="preserve-aspect"&&!wu(Ts(e),Ts(n),.2)}function Sh(t){var e;return t!==t.root&&((e=t.scroll)==null?void 0:e.wasRoot)}const Ch=lo({attachResizeListener:(t,e)=>ie(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),We={current:void 0},uo=lo({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!We.current){const t=new Ch({});t.mount(window),t.setOptions({layoutScroll:!0}),We.current=t}return We.current},resetTransform:(t,e)=>{t.style.transform=e!==void 0?e:"none"},checkIsScrollRoot:t=>window.getComputedStyle(t).position==="fixed"}),Ah={pan:{Feature:Wu},drag:{Feature:$u,ProjectionNode:uo,MeasureLayout:no}};function Ls(t,e,n){const{props:i}=t;t.animationState&&i.whileHover&&t.animationState.setActive("whileHover",n==="Start");const s="onHover"+n,o=i[s];o&&R.postRender(()=>o(e,ae(e)))}class wh extends gt{mount(){const{current:e}=this.node;e&&(this.unmount=ic(e,(n,i)=>(Ls(this.node,i,"Start"),s=>Ls(this.node,s,"End"))))}unmount(){}}class Eh extends gt{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch{e=!0}!e||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=se(ie(this.node.current,"focus",()=>this.onFocus()),ie(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function Fs(t,e,n){const{props:i}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&i.whileTap&&t.animationState.setActive("whileTap",n==="Start");const s="onTap"+(n==="End"?"":n),o=i[s];o&&R.postRender(()=>o(e,ae(e)))}class Ph extends gt{mount(){const{current:e}=this.node;e&&(this.unmount=ac(e,(n,i)=>(Fs(this.node,i,"Start"),(s,{success:o})=>Fs(this.node,s,o?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const un=new WeakMap,Ue=new WeakMap,Mh=t=>{const e=un.get(t.target);e&&e(t)},Dh=t=>{t.forEach(Mh)};function Vh({root:t,...e}){const n=t||document;Ue.has(n)||Ue.set(n,{});const i=Ue.get(n),s=JSON.stringify(e);return i[s]||(i[s]=new IntersectionObserver(Dh,{root:t,...e})),i[s]}function Lh(t,e,n){const i=Vh(e);return un.set(t,n),i.observe(t),()=>{un.delete(t),i.unobserve(t)}}const Fh={some:0,all:1};class Bh extends gt{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:n,margin:i,amount:s="some",once:o}=e,r={root:n?n.current:void 0,rootMargin:i,threshold:typeof s=="number"?s:Fh[s]},a=l=>{const{isIntersecting:c}=l;if(this.isInView===c||(this.isInView=c,o&&!c&&this.hasEnteredView))return;c&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",c);const{onViewportEnter:u,onViewportLeave:h}=this.node.getProps(),d=c?u:h;d&&d(l)};return Lh(this.node.current,r,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:e,prevProps:n}=this.node;["amount","margin","root"].some(kh(e,n))&&this.startObserver()}unmount(){}}function kh({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}const Oh={inView:{Feature:Bh},tap:{Feature:Ph},focus:{Feature:Eh},hover:{Feature:wh}},Rh={layout:{ProjectionNode:uo,MeasureLayout:no}},hn={current:null},ho={current:!1};function Nh(){if(ho.current=!0,!!fn)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>hn.current=t.matches;t.addEventListener("change",e),e()}else hn.current=!1}const jh=new WeakMap;function Ih(t,e,n){for(const i in e){const s=e[i],o=n[i];if(G(s))t.addValue(i,s);else if(G(o))t.addValue(i,Ot(s,{owner:t}));else if(o!==s)if(t.hasValue(i)){const r=t.getValue(i);r.liveStyle===!0?r.jump(s):r.hasAnimated||r.set(s)}else{const r=t.getStaticValue(i);t.addValue(i,Ot(r!==void 0?r:s,{owner:t}))}}for(const i in n)e[i]===void 0&&t.removeValue(i);return e}const Bs=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class _h{scrapeMotionValuesFromProps(e,n,i){return{}}constructor({parent:e,props:n,presenceContext:i,reducedMotionConfig:s,blockInitialAnimation:o,visualState:r},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Ln,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const d=Q.now();this.renderScheduledAt<d&&(this.renderScheduledAt=d,R.render(this.render,!1,!0))};const{latestValues:l,renderState:c}=r;this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=c,this.parent=e,this.props=n,this.presenceContext=i,this.depth=e?e.depth+1:0,this.reducedMotionConfig=s,this.options=a,this.blockInitialAnimation=!!o,this.isControllingVariants=Pe(n),this.isVariantNode=Fr(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);const{willChange:u,...h}=this.scrapeMotionValuesFromProps(n,{},this);for(const d in h){const m=h[d];l[d]!==void 0&&G(m)&&m.set(l[d],!1)}}mount(e){this.current=e,jh.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,i)=>this.bindToMotionValue(i,n)),ho.current||Nh(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:hn.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),pt(this.notifyUpdate),pt(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features){const n=this.features[e];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(e,n){this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();const i=It.has(e);i&&this.onBindTransform&&this.onBindTransform();const s=n.on("change",a=>{this.latestValues[e]=a,this.props.onUpdate&&R.preRender(this.notifyUpdate),i&&this.projection&&(this.projection.isTransformDirty=!0)}),o=n.on("renderRequest",this.scheduleRender);let r;window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,e,n)),this.valueSubscriptions.set(e,()=>{s(),o(),r&&r(),n.owner&&n.stop()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}updateFeatures(){let e="animation";for(e in Rt){const n=Rt[e];if(!n)continue;const{isEnabled:i,Feature:s}=n;if(!this.features[e]&&s&&i(this.props)&&(this.features[e]=new s(this)),this.features[e]){const o=this.features[e];o.isMounted?o.update():(o.mount(),o.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):_()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,n){this.latestValues[e]=n}update(e,n){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let i=0;i<Bs.length;i++){const s=Bs[i];this.propEventSubscriptions[s]&&(this.propEventSubscriptions[s](),delete this.propEventSubscriptions[s]);const o="on"+s,r=e[o];r&&(this.propEventSubscriptions[s]=this.on(s,r))}this.prevMotionValues=Ih(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(e),()=>n.variantChildren.delete(e)}addValue(e,n){const i=this.values.get(e);n!==i&&(i&&this.removeValue(e),this.bindToMotionValue(e,n),this.values.set(e,n),this.latestValues[e]=n.get())}removeValue(e){this.values.delete(e);const n=this.valueSubscriptions.get(e);n&&(n(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,n){if(this.props.values&&this.props.values[e])return this.props.values[e];let i=this.values.get(e);return i===void 0&&n!==void 0&&(i=Ot(n===null?void 0:n,{owner:this}),this.addValue(e,i)),i}readValue(e,n){let i=this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options);return i!=null&&(typeof i=="string"&&(Is(i)||$s(i))?i=parseFloat(i):!uc(i)&&mt.test(n)&&(i=Cr(e,n)),this.setBaseTarget(e,G(i)?i.get():i)),G(i)?i.get():i}setBaseTarget(e,n){this.baseTarget[e]=n}getBaseTarget(e){var o;const{initial:n}=this.props;let i;if(typeof n=="string"||typeof n=="object"){const r=Wn(this.props,n,(o=this.presenceContext)==null?void 0:o.custom);r&&(i=r[e])}if(n&&i!==void 0)return i;const s=this.getBaseTargetFromProps(this.props,e);return s!==void 0&&!G(s)?s:this.initialValues[e]!==void 0&&i===void 0?void 0:this.baseTarget[e]}on(e,n){return this.events[e]||(this.events[e]=new vn),this.events[e].add(n)}notify(e,...n){this.events[e]&&this.events[e].notify(...n)}}class fo extends _h{constructor(){super(...arguments),this.KeyframeResolver=Jl}sortInstanceNodePosition(e,n){return e.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(e,n){return e.style?e.style[n]:void 0}removeValueFromRenderState(e,{vars:n,style:i}){delete n[e],delete i[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;G(e)&&(this.childSubscription=e.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function po(t,{style:e,vars:n},i,s){const o=t.style;let r;for(r in e)o[r]=e[r];s==null||s.applyProjectionStyles(o,i);for(r in n)o.setProperty(r,n[r])}function $h(t){return window.getComputedStyle(t)}class Wh extends fo{constructor(){super(...arguments),this.type="html",this.renderInstance=po}readValueFromInstance(e,n){var i;if(It.has(n))return(i=this.projection)!=null&&i.isProjecting?Qe(n):yl(e,n);{const s=$h(e),o=(Sn(n)?s.getPropertyValue(n):s[n])||0;return typeof o=="string"?o.trim():o}}measureInstanceViewportBox(e,{transformPagePoint:n}){return Jr(e,n)}build(e,n,i){In(e,n,i.transformTemplate)}scrapeMotionValuesFromProps(e,n,i){return Un(e,n,i)}}const mo=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Uh(t,e,n,i){po(t,e,void 0,i);for(const s in e.attrs)t.setAttribute(mo.has(s)?s:jn(s),e.attrs[s])}class zh extends fo{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=_}getBaseTargetFromProps(e,n){return e[n]}readValueFromInstance(e,n){if(It.has(n)){const i=Sr(n);return i&&i.default||0}return n=mo.has(n)?n:jn(n),e.getAttribute(n)}scrapeMotionValuesFromProps(e,n,i){return Wr(e,n,i)}build(e,n,i){jr(e,n,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(e,n,i,s){Uh(e,n,i,s)}mount(e){this.isSVGTag=_r(e.tagName),super.mount(e)}}const Kh=(t,e)=>$n(t)?new zh(e):new Wh(e,{allowProjection:t!==A.Fragment}),Yh=Kc({...yu,...Oh,...Ah,...Rh},Kh),Gh=gc(Yh);var Kn={},Hh=Os;Object.defineProperty(Kn,"__esModule",{value:!0});var go=Kn.default=void 0,Xh=Hh(ks()),Zh=Zt;go=Kn.default=(0,Xh.default)((0,Zh.jsx)("path",{d:"M21.99 4c0-1.1-.89-2-1.99-2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4zM20 4v13.17L18.83 16H4V4zM6 12h12v2H6zm0-3h12v2H6zm0-3h12v2H6z"}),"CommentOutlined");const Qh=()=>f(P,{sx:{mb:2,p:1,border:"1px solid #E2E8F0",borderRadius:2,bgcolor:"#F8FAFC"},children:C(P,{sx:{display:"flex",alignItems:"center",mb:1},children:[f(P,{sx:{mr:1},children:f($,{variant:"circular",width:24,height:24})}),C(P,{sx:{width:"100%"},children:[f($,{variant:"text",width:"40%"}),C(P,{sx:{display:"flex",alignItems:"center",mt:1},children:[f($,{variant:"rectangular",width:120,height:8,sx:{mr:1}}),f($,{variant:"text",width:"20%"})]})]})]})}),Jh=()=>f(kt,{elevation:1,sx:{p:2,mb:3,border:"1px solid #E2E8F0",borderRadius:3,bgcolor:"#FFFFFF",boxShadow:"0 2px 6px rgba(0,0,0,0.03)"},children:C(Y,{spacing:2,children:[C(Y,{direction:"row",alignItems:"center",spacing:2,children:[f($,{variant:"text",width:"30%"}),f($,{variant:"rectangular",width:2,height:24}),C(Y,{direction:"row",alignItems:"center",spacing:1,children:[f($,{variant:"circular",width:24,height:24}),f($,{variant:"text",width:80}),f($,{variant:"text",width:120})]})]}),C(P,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[f($,{variant:"text",width:"25%"}),f($,{variant:"rectangular",width:80,height:24})]}),C(B,{container:!0,spacing:3,children:[C(B,{item:!0,xs:12,md:6,children:[f($,{variant:"text",width:"20%"}),f(Y,{direction:"row",spacing:1,children:f($,{variant:"rectangular",width:200,height:32})})]}),C(B,{item:!0,xs:12,md:6,children:[f($,{variant:"text",width:"20%"}),C(P,{sx:{display:"flex",alignItems:"center"},children:[f($,{variant:"circular",width:24,height:24,sx:{mr:1}}),C(P,{children:[f($,{variant:"text",width:150}),f($,{variant:"text",width:100})]})]})]})]})]})}),yo=()=>f(B,{container:!0,spacing:2,children:f(B,{item:!0,xs:12,children:C(B,{container:!0,spacing:2,children:[f(B,{item:!0,xs:12,md:4,lg:3,children:f(kt,{elevation:0,sx:{p:2,border:"1px solid #E2E8F0",borderRadius:3,bgcolor:"#F9FAFB"},children:[...Array(5)].map((t,e)=>f(Qh,{},e))})}),f(B,{item:!0,xs:12,md:8,lg:9,children:C(kt,{elevation:0,sx:{p:2,border:"1px solid #E2E8F0",borderRadius:3,bgcolor:"#FFFFFF"},children:[C(P,{sx:{display:"flex",justifyContent:"space-between",mb:2},children:[C(P,{children:[f($,{variant:"text",width:200}),f($,{variant:"text",width:100})]}),f($,{variant:"rectangular",width:100,height:36})]}),[...Array(1)].map((t,e)=>f(Jh,{},e))]})})]})})}),qh=({item:t,initiator:e=!1})=>{var b,y,g;const n=Lo(),i=ca(n.breakpoints.down("sm")),s=zt(t.createdAt),o=e?zt(t.updatedAt):zt(t.completedAt),r=o.diff(s,"minutes"),a=s.format("MMM D, YYYY"),l=s.format("h:mm A"),c=o.format("MMM D, YYYY"),u=o.format("h:mm A"),h=e?t.updatedAt:t.completedAt,d=i?16:20,m=i?10:12,x=i?1.5:2;return C(rt,{children:[f(P,{sx:{px:2,py:1.5,borderBottom:"1px solid #E2E8F0",borderTop:{xs:"1px solid #E2E8F0",md:"none"},borderRadius:"8px",display:"flex",alignItems:"center",justifyContent:"space-between",bgcolor:(y=(b=T)==null?void 0:b.chip)==null?void 0:y.background},children:C(V,{variant:"subtitle2",sx:{fontWeight:600,color:(g=T)==null?void 0:g.text.greyishBlue,display:"flex",alignItems:"center"},children:[f(Fo,{sx:{fontSize:18,mr:1}}),"Task Timeline"]})}),C(P,{sx:{position:"relative",ml:x,pb:2,width:"100%",maxWidth:"100%",overflow:"hidden",pt:2},children:[f(P,{sx:{position:"absolute",left:d/2-1,top:d+4,height:h?`calc(100% - ${d*2.5}px)`:`${d*4}px`,width:2,bgcolor:h?"#16A34A":"#F97316",zIndex:0}}),C(P,{sx:{position:"relative",mb:3,display:"flex",alignItems:"center"},children:[f(P,{sx:{width:d,height:d,borderRadius:"50%",bgcolor:"#0284C7",display:"flex",justifyContent:"center",alignItems:"center",zIndex:1,flexShrink:0},children:f(Bo,{sx:{color:"#FFFFFF",fontSize:m}})}),C(P,{sx:{ml:1.5,overflow:"hidden"},children:[f(V,{variant:i?"caption":"body2",color:"#0284C7",fontWeight:600,noWrap:!0,children:"Started"}),C(V,{variant:"caption",color:"text.secondary",sx:{display:"block",textOverflow:"ellipsis"},children:[a," ",l]})]})]}),C(P,{sx:{position:"relative",mb:h?3:0,display:"flex",alignItems:"center"},children:[f(P,{sx:{width:d,height:d,borderRadius:"50%",bgcolor:h?"#059669":"#F97316",display:"flex",justifyContent:"center",alignItems:"center",zIndex:1,flexShrink:0},children:h?f(ko,{sx:{color:"#FFFFFF",fontSize:m}}):f(Oo,{sx:{color:"#FFFFFF",fontSize:m}})}),f(P,{sx:{ml:1.5,display:"flex",alignItems:"center",bgcolor:h?"#F0FDF4":"#FFF7ED",borderRadius:1.5,px:1,py:.5,border:h?"1px solid #D1FAE5":"1px solid #FFEDD5",maxWidth:"100%"},children:f(V,{variant:"caption",sx:{color:h?"#059669":"#EA580C",fontWeight:600,whiteSpace:"nowrap"},children:h?`${r} min`:"Pending"})})]}),h&&C(P,{sx:{position:"relative",display:"flex",alignItems:"center"},children:[f(P,{sx:{width:d,height:d,borderRadius:"50%",bgcolor:"#16A34A",display:"flex",justifyContent:"center",alignItems:"center",zIndex:1,flexShrink:0},children:f(xe,{sx:{color:"#FFFFFF",fontSize:m}})}),C(P,{sx:{ml:1.5,overflow:"hidden"},children:[f(V,{variant:i?"caption":"body2",color:"#16A34A",fontWeight:600,noWrap:!0,children:"Completed"}),C(V,{variant:"caption",color:"text.secondary",sx:{display:"block",textOverflow:"ellipsis"},children:[c," ",u]})]})]})]})]})},w={COMPLETED:"COMPLETED",CANCELED:"CANCELED",READY:"READY",PENDING:"PENDING"},td=Ro(Wt)(({status:t})=>({fontWeight:600,fontSize:"0.7rem",height:"24px",borderRadius:"12px",backgroundColor:t===w.COMPLETED?T.success.completedBackground:t===w.CANCELED?T.background.canceled:t===w.READY?T.warning.light:T.background.subtle,color:t===w.COMPLETED?T.success.completedDark:t===w.CANCELED?T.error.deepRed:t===w.READY?T.warning.orange:T.text.darkGrey,border:t===w.COMPLETED?`1px solid ${T.success.pale}`:t===w.CANCELED?`1px solid ${T.error.pale}`:t===w.READY?`1px solid ${T.warning.orange}`:`1px solid ${T.border.light}`,"& .MuiChip-label":{letterSpacing:"1.4px"}})),ed=({item:t,index:e,setOpenSnackbar:n})=>{var i,s,o,r,a,l,c,u,h,d;return f(Gh.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.4,delay:e*.1},children:f(kt,{elevation:0,sx:{pl:2,mb:2,borderRadius:"12px",border:"1px solid",borderColor:t.status===w.COMPLETED?T.success.pale:t.status===w.CANCELED?T.error.pale:T.border.cardBorder,backgroundColor:T.basic.white,position:"relative",overflow:"hidden",transition:"all 0.3s ease-in-out","&::before":{content:'""',position:"absolute",left:0,top:0,bottom:0,width:"5px",backgroundColor:t.status===w.COMPLETED?T.success.deepGreen:t.status===w.CANCELED?T.error.deepRed:t.status===w.READY?T.warning.orange:T.neutral[400]}},children:C(P,{sx:{display:"flex",flexDirection:"row"},children:[C(P,{sx:{width:"80%",pr:2,pt:2},children:[C(Y,{direction:"row",alignItems:"center",spacing:1,children:[f(V,{variant:"h6",sx:{fontSize:"1.125rem",fontWeight:700,color:T.text.darkBlue,pl:1,display:"flex",alignItems:"center"},children:t.subject}),f($o,{orientation:"vertical",flexItem:!0,sx:{height:20,borderColor:T.border.cardBorder}}),C(Y,{direction:"row",alignItems:"center",spacing:.5,children:[f(Wo,{fontSize:"small",sx:{color:T.text.darkGrey,pb:"2px"}}),f(V,{variant:"body2",sx:{fontWeight:500,color:T.text.greyishBlue,fontSize:"0.805rem"},children:"Initiated by"}),f(V,{variant:"body2",sx:{fontWeight:600,color:T.info.dark,fontSize:"0.755rem",letterSpacing:"0.5px"},children:t.createdBy})]})]}),f(P,{sx:{pt:2},children:C(B,{container:!0,spacing:3,alignItems:"flex-start",children:[C(B,{item:!0,xs:12,md:4,children:[f(V,{sx:{fontWeight:600,color:T.text.darkBlue,fontSize:"0.755rem",mb:1},children:(t==null?void 0:t.status)==="READY"?"Recipient Users":(t==null?void 0:t.status)==="RESERVED"?"Claimed by":(t==null?void 0:t.status)==="COMPLETED"?"Completed by":"Recipient Users"}),f(Y,{direction:"row",flexWrap:"wrap",useFlexGap:!0,gap:1,children:(t==null?void 0:t.status)==="COMPLETED"&&(t!=null&&t.processor)?f(Tt,{title:t==null?void 0:t.processor,children:f(Wt,{label:t==null?void 0:t.processor,avatar:f(Me,{sx:{width:18,height:18,bgcolor:T.background.subtle,color:T.text.darkBlue,fontSize:"0.55rem"},children:(i=t==null?void 0:t.processor)==null?void 0:i.split(" ").map(m=>m[0]).join("").toUpperCase()}),sx:{bgcolor:"#F9FAFB",color:"#334155",fontSize:"0.775rem","& .MuiChip-label":{px:1},boxShadow:"0 1px 2px rgba(0,0,0,0.04)",border:"1px solid #E2E8F0"}})}):f(rt,{children:((s=t==null?void 0:t.recipientUsers)==null?void 0:s.length)>1?C(rt,{children:[f(Tt,{title:t==null?void 0:t.recipientUsers[0],children:f(Wt,{label:t==null?void 0:t.recipientUsers[0],avatar:f(Me,{sx:{width:18,height:18,bgcolor:T.background.subtle,color:T.text.darkBlue,fontSize:"0.55rem"},children:(o=t==null?void 0:t.recipientUsers[0])==null?void 0:o.split(" ").map(m=>m[0]).join("").toUpperCase()}),sx:{bgcolor:"#F9FAFB",color:"#334155",fontSize:"0.775rem","& .MuiChip-label":{px:1},boxShadow:"0 1px 2px rgba(0,0,0,0.04)",border:"1px solid #E2E8F0"}})}),f(Tt,{title:f(Y,{spacing:.5,children:(r=t==null?void 0:t.recipientUsers)==null?void 0:r.slice(1).map((m,x)=>f(V,{variant:"caption",color:"inherit",children:m},x))}),arrow:!0,placement:"top",children:f(Wt,{label:`+ ${((a=t==null?void 0:t.recipientUsers)==null?void 0:a.length)-1}`,sx:{bgcolor:"#F9FAFB",color:"#334155",fontSize:"0.775rem","& .MuiChip-label":{px:1},boxShadow:"0 1px 2px rgba(0,0,0,0.04)",border:"1px solid #E2E8F0",cursor:"pointer"}})})]}):(l=t==null?void 0:t.recipientUsers)==null?void 0:l.map((m,x)=>f(Tt,{title:m,children:f(Wt,{label:m,avatar:f(Me,{sx:{width:18,height:18,bgcolor:T.background.subtle,color:T.text.darkBlue,fontSize:"0.55rem"},children:m==null?void 0:m.split(" ").map(b=>b[0]).join("").toUpperCase()}),sx:{bgcolor:"#F9FAFB",color:"#334155",fontSize:"0.775rem","& .MuiChip-label":{px:1},boxShadow:"0 1px 2px rgba(0,0,0,0.04)",border:"1px solid #E2E8F0"}})},x))})})]}),f(B,{item:!0,xs:12,md:2,children:C(P,{children:[f(V,{variant:"caption",sx:{color:"#64748B",fontWeight:600,fontSize:"0.75rem",display:"flex",alignItems:"center",justifyContent:"flex-start",mb:1},children:C(P,{sx:{display:"flex",alignItems:"center"},children:[f(go,{sx:{fontSize:14,mr:.5}}),"Remarks"]})}),f(P,{sx:{maxHeight:120,overflowY:"auto",display:"flex",flexWrap:"wrap",gap:.5,p:.5,"&::-webkit-scrollbar":{height:"4px"},"&::-webkit-scrollbar-thumb":{background:T.box.scrollBackground,borderRadius:"4px"}},children:(u=(c=t==null?void 0:t.attributesDtos)==null?void 0:c[0])!=null&&u.remarks?f(Uo,{text:(d=(h=t==null?void 0:t.attributesDtos)==null?void 0:h[0])==null?void 0:d.remarks,maxChars:20,variant:"body2",color:"text.secondary",sx:{fontSize:"0.75rem"}}):f(V,{variant:"body2",color:"text.secondary",sx:{fontSize:"0.75rem",fontStyle:"italic"},children:"No remarks available"})})]})}),((t==null?void 0:t.status)===w.COMPLETED&&(t==null?void 0:t.processor)||(t==null?void 0:t.status)===w.CANCELED)&&C(B,{item:!0,xs:12,md:6,children:[f(V,{sx:{fontWeight:600,color:T.text.darkBlue,fontSize:"0.755rem"},children:"Status Info"}),C(P,{sx:{display:"flex",alignItems:"center",border:`1px solid ${T.border.cardBorder}`,borderRadius:"10px",p:2,bgcolor:T.chip.background,gap:2},children:[f(P,{sx:{display:"flex",alignItems:"center",justifyContent:"center",width:36,height:36,borderRadius:"50%",bgcolor:t.status===w.COMPLETED?"#D1FAE5":t.status===w.CANCELED?"#FEE2E2":"#E2E8F0",color:t.status===w.COMPLETED?"#059669":t.status===w.CANCELED?"#DC2626":"#64748B"},children:(t==null?void 0:t.status)===w.COMPLETED?f(xe,{fontSize:"small"}):f(ze,{fontSize:"small"})}),C(P,{children:[f(V,{variant:"body2",sx:{fontWeight:500,color:"#334155",mb:.5},children:t.status===w.COMPLETED?C(rt,{children:["Completed by"," ",f(P,{component:"span",sx:{color:"#1D4ED8",fontWeight:500},children:t.processor})]}):t.status===w.CANCELED?f(rt,{children:"Canceled"}):""}),f(V,{variant:"caption",sx:{color:"#64748B",fontSize:"0.75rem"},children:new Date(t.completedAt||t.createdAt).toLocaleString("en-US",{dateStyle:"medium",timeStyle:"short"})})]})]})]}),(t==null?void 0:t.status)===w.READY&&f(B,{item:!0,xs:12,children:C(P,{sx:{display:"flex",alignItems:"center",justifyContent:"center",py:1.5,px:2.5,borderRadius:"10px",backgroundColor:T.warning.pale,border:`1px dashed ${T.warning.orange}`,color:`${T.warning.orange} !important`,fontSize:"0.9375rem",letterSpacing:"0.5px",fontWeight:500,mt:1,mb:1},children:[f(Rs,{fontSize:"small",sx:{mr:1.5}}),"In Progress - Awaiting Completion"]})})]})})]}),f(P,{sx:{width:"20%",pl:2},children:f(qh,{item:t})})]})})})},nd=async t=>{if(!t)return[];const e=[];let n={};if(Array.isArray(t))t.forEach((i,s)=>{const o=Object.keys(i)[0];n[o]=s,(i[o]||[]).forEach(a=>{var h,d,m,x,b;const l=(d=(h=a==null?void 0:a.attributesDtos)==null?void 0:h[0])==null?void 0:d.currentLevel,c=((x=(m=a==null?void 0:a.attributesDtos)==null?void 0:m[0])==null?void 0:x.currentLevelName)||o;let u=(a==null?void 0:a.status)||w.PENDING;e.push({id:a.id,level:parseInt(l,10),levelName:c,status:u,subject:((b=a.subject)==null?void 0:b.replace(/ & \d+$/,""))||"",createdBy:a.createdBy,createdAt:a.createdAt,performedBy:a.processor,completedAt:a.completedAt,comment:a.description,recipients:a.recipientUsers||[],originalLevelKey:o,displayOrder:s})})});else if(t&&typeof t=="object"){const i=Object.keys(t);i.forEach((s,o)=>{n[s]=o}),i.forEach(s=>{(t[s]||[]).forEach(r=>{var u,h,d,m,x;const a=(h=(u=r==null?void 0:r.attributesDtos)==null?void 0:u[0])==null?void 0:h.currentLevel,l=((m=(d=r==null?void 0:r.attributesDtos)==null?void 0:d[0])==null?void 0:m.currentLevelName)||s;let c=(r==null?void 0:r.status)||w.PENDING;e.push({id:r.id,level:parseInt(a,10),levelName:l,status:c,subject:((x=r.subject)==null?void 0:x.replace(/ & \d+$/,""))||"",createdBy:r.createdBy,createdAt:r.createdAt,performedBy:r.processor,completedAt:r.completedAt,comment:r.description,recipients:r.recipientUsers||[],originalLevelKey:s,displayOrder:n[s]})})})}return e},id=t=>!t||t.length===0?0:t.filter(e=>e.status!==w.READY&&e.status!==w.PENDING).length,sd=t=>!t||(t==null?void 0:t.length)===0?w.PENDING:(t==null?void 0:t.every(s=>s.status===w.CANCELED))?w.CANCELED:(t==null?void 0:t.some(s=>s.status===w.READY))?w.READY:(t==null?void 0:t.every(s=>s.status===w.COMPLETED))?w.COMPLETED:w.PENDING,rd=({data:t,childRequestID:e})=>{const[n,i]=A.useState([]),[s,o]=A.useState(!1),[r,a]=A.useState(!0);A.useEffect(()=>{(n==null?void 0:n.length)==0?a(!0):a(!1)},[n]);const c=(()=>!t||!Array.isArray(t)||t.length===0?[]:t==null?void 0:t.map((v,S)=>{const[D,E]=Object.entries(v)[0];return{levelName:D,items:Array.isArray(E)?E:[],status:sd(Array.isArray(E)?E:[]),completedCount:id(Array.isArray(E)?E:[]),totalCount:Array.isArray(E)?E.length:0,originalLevelNumber:S,displayOrder:S}}))();if(t&&Object.keys(t).length>0){const g=Object.keys(t),v={};g.forEach((S,D)=>{const E=parseInt(S,10);v[E]=D}),c.sort((S,D)=>{const E=v[S.originalLevelNumber]!==void 0?v[S.originalLevelNumber]:999,F=v[D.originalLevelNumber]!==void 0?v[D.originalLevelNumber]:999;return E-F})}const[u,h]=A.useState(null);A.useEffect(()=>{var g;c!=null&&c.length&&u===null&&h((g=c[c.length-1])==null?void 0:g.originalLevelNumber)},[c,u]);const d=A.useRef({});A.useEffect(()=>{(async()=>{const v=await nd(t);i(v)})()},[t]);const m=g=>{h(g)},x=g=>{switch(g){case w.COMPLETED:return"Completed";case w.CANCELED:return"Canceled";case w.READY:return"Ready";default:return"Pending"}},b=g=>{switch(g){case w.COMPLETED:return f(xe,{fontSize:"small",sx:{color:T.success.deepGreen}});case w.CANCELED:return f(ze,{fontSize:"small",sx:{color:T.error.deepRed}});case w.READY:return f(Rs,{fontSize:"small",sx:{color:T.warning.orange}});default:return f(zo,{fontSize:"small",sx:{color:T.text.darkGrey}})}},y=c.find(g=>g.originalLevelNumber===u);return A.useEffect(()=>{u&&d.current[u]&&d.current[u].scrollIntoView({behavior:"smooth",block:"center"})},[u]),r===!0?f(yo,{}):C(P,{sx:{pt:2,height:"78vh",overflow:"hidden !important",display:"flex",flexDirection:"column",justifyContent:"flex-start"},children:[f(B,{container:!0,children:C(B,{container:!0,sx:{border:"1px solid #E2E8F0",overflow:"hidden",height:"100%",borderRadius:2,position:"relative",backgroundColor:"#FFFFFF"},children:[f(B,{item:!0,xs:12,md:4,lg:2,children:f(kt,{elevation:0,sx:{p:0,overflow:"hidden",height:"100%",backgroundColor:"#FFFFFF"},children:C(P,{sx:{p:2,borderRight:"1px solid #E2E8F0",borderRadius:2,bgcolor:"#F8FAFC",height:"73vh",overflowY:"auto",overflowX:"hidden","&::-webkit-scrollbar":{width:"3.5px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"#CBD5E1"},"&::-webkit-scrollbar-track":{backgroundColor:"#F1F5F9"}},children:["  ",(c==null?void 0:c.length)>0&&c.map((g,v)=>{const S=u===g.originalLevelNumber,D=v>0&&c[v-1].status===w.COMPLETED;return g.status===w.READY||(g.status,w.PENDING),C(P,{ref:E=>d.current[g.originalLevelNumber]=E,sx:{mb:2,position:"relative"},children:[C(P,{onClick:()=>m(g.originalLevelNumber),sx:{display:"flex",alignItems:"center",borderRadius:2,px:2,py:2,height:"auto",bgcolor:g.status===w.COMPLETED?S?T.success.completedBackground:T.success.pale:g.status===w.CANCELED?S?T.background.canceled:T.error.pale:g.status===w.READY?S?T.warning.light:T.warning.pale:T.background.subtle,border:S?"2px solid":"1px solid",borderColor:g.status===w.COMPLETED?T.success.deepGreen:g.status===w.CANCELED?T.error.deepRed:g.status===w.READY?T.warning.orange:T.border.cardBorder,cursor:"pointer",transition:"all 0.2s ease",boxShadow:S?"0 0 0 2px rgba(59, 130, 246, 0.2)":"none",position:"relative","&:hover":{bgcolor:g.status===w.COMPLETED?"#D1FAE5":g.status===w.CANCELED?"#FEE2E2":g.status===w.READY?"#FFEDD5":"#E2E8F0"}},children:[f(P,{sx:{width:40,height:40,borderRadius:"50%",display:"flex",justifyContent:"center",alignItems:"center",mr:2,bgcolor:g.status===w.COMPLETED?"#ECFDF5":g.status===w.CANCELED?"#FEF2F2":g.status===w.READY?"#FFF7ED":"#F1F5F9",border:"2px solid",borderColor:g.status===w.COMPLETED?"#10B981":g.status===w.CANCELED?"#EF4444":g.status===w.READY?"orange":"#CBD5E1",color:g.status===w.COMPLETED?"#059669":g.status===w.CANCELED?"#DC2626":g.status===w.READY?"orange":"#64748B",boxShadow:S?"0 0 0 2px rgba(59, 130, 246, 0.15)":"none",transform:S?"scale(1.1)":"scale(1)",transition:"all 0.2s ease"},children:g.status===w.COMPLETED?f(xe,{fontSize:"small"}):g.status===w.CANCELED?f(ze,{fontSize:"small"}):b(g.status)}),C(P,{sx:{flex:1},children:[f(V,{variant:"subtitle1",sx:{fontWeight:600,color:"#334155",fontSize:"0.95rem"},children:(g==null?void 0:g.levelName)||"undefined"}),f(P,{sx:{display:"flex",alignItems:"center"}}),C(P,{sx:{mt:.7,height:"30px"},children:[f(No,{variant:"determinate",value:g.completedCount/g.totalCount*100,sx:{borderRadius:6,height:.07,backgroundColor:"#e6e0d3","& .MuiLinearProgress-bar":{backgroundColor:"#3B82F6"}}},v),C(V,{variant:"caption",sx:{mt:.5,color:"#475569"},children:[g.completedCount," / ",g.totalCount," completed"]})]})]}),S&&f(P,{sx:{position:"absolute",right:12,display:"flex",alignItems:"center",justifyContent:"center",width:24,height:24,borderRadius:"50%",bgcolor:T.primary.main,color:T.basic.white,boxShadow:"0 2px 4px rgba(0,0,0,0.1)",animation:"fadeIn 0.3s ease-in-out","@keyframes fadeIn":{"0%":{opacity:0,transform:"scale(0.8)"},"100%":{opacity:1,transform:"scale(1)"}}},children:f(jo,{fontSize:"small",sx:{fontSize:12}})})]}),v<c.length-1&&f(P,{sx:{position:"absolute",left:35,height:37,top:96,bottom:-18,width:2,bgcolor:g.status===w.COMPLETED?T.success.deepGreen:T.border.cardBorder,zIndex:0}})]},g.originalLevelNumber)})]})})}),f(B,{item:!0,xs:12,md:8,lg:10,children:f(Io,{in:!0,timeout:300,children:f(kt,{elevation:0,sx:{display:"flex",flexDirection:"column",height:"100%",overflow:"hidden"},children:y&&C(rt,{children:[C(P,{sx:{p:3,borderBottom:"1px solid #F1F5F9",bgcolor:"#F8FAFC",display:"flex",justifyContent:"space-between",alignItems:"center",flexShrink:0},children:[C(P,{children:[C(P,{sx:{display:"flex",alignItems:"center"},children:[f(V,{variant:"h6",sx:{fontWeight:600,color:"#334155"},children:(y==null?void 0:y.levelName)||"undefined"}),f(td,{label:x(y.status),status:y.status,size:"small",sx:{ml:2}})]}),C(V,{variant:"body2",sx:{color:"#334155",mt:1},children:["Child Request ID: ",e]}),C(V,{variant:"body2",sx:{mt:1,color:"#64748B"},children:[y.items.length," ",y.items.length===1?"activity":"activities"]})]}),C(P,{sx:{display:"flex",alignItems:"center"},children:[c.length>1&&c.findIndex(g=>g.originalLevelNumber===u)>0&&f(ve,{size:"small",onClick:()=>{const g=c.findIndex(v=>v.originalLevelNumber===u);g>0&&m(c[g-1].originalLevelNumber)},variant:"outlined",sx:{mr:1,borderColor:"#CBD5E1",color:"#64748B","&:hover":{borderColor:"#94A3B8",bgcolor:"#F8FAFC"}},children:"Previous Level"}),c.length>1&&c.findIndex(g=>g.originalLevelNumber===u)<c.length-1&&f(ve,{size:"small",onClick:()=>{const g=c.findIndex(v=>v.originalLevelNumber===u);g<c.length-1&&m(c[g+1].originalLevelNumber)},variant:"contained",sx:{bgcolor:"#3B82F6","&:hover":{bgcolor:"#2563EB"}},children:"Next Level"})]})]}),C(P,{sx:{p:3,flexGrow:1,height:"70px",overflowY:"auto"},children:[y.items.map((g,v)=>f(ed,{item:g,index:v,setOpenSnackbar:o},g.id)),y.items.length===0&&f(P,{sx:{textAlign:"center",py:8,color:"#94A3B8"},children:f(V,{variant:"body1",children:"No activities found for this level"})})]},y.levelNumber)]})})})})]})}),f(_o,{openSnackBar:s,alertMsg:"Task ID Copied Successfully",alertType:"Success",handleSnackBarClose:()=>o(!1)})]})},xd=()=>{var Zn,Qn,Jn,qn,ti,ei,ni,ii,si,ri,oi,ai,li,ci;const[t,e]=A.useState(!0),i=Ko().state,[s,o]=A.useState([]),[r,a]=A.useState(""),[l,c]=A.useState([]),[u,h]=A.useState([]),[d,m]=A.useState(""),[x,b]=A.useState(!1),[y,g]=A.useState(""),[v,S]=A.useState([]),[D,E]=A.useState(!1),[F,I]=A.useState(!1),[L,U]=A.useState(!1),[X,yt]=A.useState(""),[le,Yn]=A.useState(),[ce,xo]=A.useState("");let xt=ui(k=>{var Z;return(Z=k==null?void 0:k.userManagement)==null?void 0:Z.taskData});const z=()=>{Xn(!0),E(!0)},J=()=>{b(!1)},_t=()=>{U(!0)},Et=()=>{U(!1),Gn(-1)},vo={overflow:"scroll",position:"absolute",top:"50%",left:"52%",transform:"translate(-50%, -50%)",width:"70%",height:"70%",bgcolor:(Qn=(Zn=T)==null?void 0:Zn.primary)==null?void 0:Qn.white,boxShadow:4,p:1};A.useEffect(()=>{a(Yo("CC"))},[]);const Pt=Go(mi.REQUEST_BENCH_TASK,!0,{}),bo=`${Pt==null?void 0:Pt.prefix}${Pt==null?void 0:Pt.childRequestIds}`,p=ui(k=>{var Z,q;return(q=(Z=k.commonSearchBar)==null?void 0:Z.RequestHistory)==null?void 0:q.reqId})||bo,Gn=Ho(),To=()=>{e(!1),c([]);const k=q=>{o(q==null?void 0:q.body),e(!1)},Z=()=>{e(!1)};ue(`/${Ve}/${tt.TASK_ACTION_DETAIL.FETCH_REQUEST_HISTORY}?requestId=${p}`,"get",k,Z)},So=()=>{c([]);let k="";p!=null&&p.includes("NME")||p!=null&&p.includes("CME")||p!=null&&p.includes("EME")||p!=null&&p.includes("NMA")||p!=null&&p.includes("CMA")||p!=null&&p.includes("EMA")||p!=null&&p.includes("FCA")?k=`/${Ve}/${tt.TASK_ACTION_DETAIL.FETCH_DETAILS_WORKFLOW}?requestId=${p}`:p!=null&&p.includes("NCS")||p!=null&&p.includes("CCS")||p!=null&&p.includes("NCM")||p!=null&&p.includes("CCM")?k=`/${yi}/${tt.TASK_ACTION_DETAIL.FETCH_DETAILS_WORKFLOW}?requestId=${p}`:p!=null&&p.includes("NPS")||p!=null&&p.includes("CPS")||p!=null&&p.includes("NPM")||p!=null&&p.includes("CPM")?k=`/${xi}/${tt.TASK_ACTION_DETAIL.FETCH_DETAILS_WORKFLOW}?requestId=${p}`:p!=null&&p.includes("NLS")||p!=null&&p.includes("CLS")||p!=null&&p.includes("ELS")||p!=null&&p.includes("NLM")||p!=null&&p.includes("CLM")||p!=null&&p.includes("ELM")?k=`/${vi}/${tt.TASK_ACTION_DETAIL.FETCH_DETAILS_WORKFLOW}?requestId=${p}`:(p!=null&&p.includes("NBS")||p!=null&&p.includes("CBS")||p!=null&&p.includes("EBS")||p!=null&&p.includes("NBM")||p!=null&&p.includes("CBM")||p!=null&&p.includes("EBM"))&&(k=`/${bi}/${tt.TASK_ACTION_DETAIL.FETCH_DETAILS_WORKFLOW}?requestId=${p}`),ue(k,"get",O=>{var dt;h(O==null?void 0:O.body[0]),I(!0),m((dt=O==null?void 0:O.body[0])==null?void 0:dt.comment)},()=>{var O;sa((O=ra)==null?void 0:O.ERROR_FETCHING_DATA,"error")})},Co=[{field:"id",headerName:"ID",flex:1,hide:!0},{field:"createdAt",headerName:"Notification Date",flex:.5,renderCell:k=>f(V,{sx:{fontSize:"12px"},children:zt(k.row.createdAt).format("DD MMM YYYY")})},{field:"subject",headerName:"Subject",flex:2},{field:"actions",align:"center",flex:1,headerAlign:"center",headerName:"Actions",sortable:!1,renderCell:k=>f("div",{children:f(Tt,{title:"View Mail Body",children:f(Mt,{onClick:()=>{g(ht.find(Z=>Z.id==k.row.id))},children:f(ea,{})})})})}],Ao=()=>{b(!1)},wo=()=>{b(!0)},Hn=[{field:"id",headerName:"Document ID",flex:1,hide:!0},{field:"docName",headerName:"Document Name",flex:1},{field:"uploadedOn",headerName:"Uploaded On",flex:1,align:"center",headerAlign:"center"},{field:"uploadedBy",headerName:"Uploaded By",sortable:!1,flex:1},{field:"attachmentType",headerName:"Attachment Type",sortable:!1,flex:1},{field:"action",headerName:"Action",sortable:!1,filterable:!1,align:"center",headerAlign:"center",width:75,renderCell:k=>f(rt,{children:f(la,{index:k.row.id,name:k.row.docName})})}],Eo=()=>{xt!=null&&xt.requestId&&(xt==null||xt.requestId);let k=Z=>{var q=[];Z.documentDetailDtoList.forEach(O=>{var dt={id:O.documentId,docType:O.fileType,docName:O.fileName,uploadedOn:O.docCreationDate,uploadedBy:O.createdBy,attachmentType:O.attachmentType};q.push(dt)}),S(q)};ue(`/${na}/${tt.TASK_ACTION_DETAIL.GET_DOCS}/${p}`,"get",k)},[Po,Xn]=A.useState(!1),Mo=()=>{Xn(!1),E(!1)},[ht,Do]=A.useState([]),Vo=k=>{wo();let Z=dt=>{Do(dt==null?void 0:dt.body)},q=()=>{},O="";p!=null&&p.includes("NME")||p!=null&&p.includes("CME")||p!=null&&p.includes("EME")||p!=null&&p.includes("NMA")||p!=null&&p.includes("CMA")||p!=null&&p.includes("EMA")||p!=null&&p.includes("FCA")?O=`/${Ve}/${tt.TASK_ACTION_DETAIL.FETCH_MAILS}?requestId=${p}`:p!=null&&p.includes("NCS")||p!=null&&p.includes("CCS")||p!=null&&p.includes("NCM")||p!=null&&p.includes("CCM")?O=`${yi}/${tt.TASK_ACTION_DETAIL.FETCH_MAILS}?requestId=${p}`:p!=null&&p.includes("NPS")||p!=null&&p.includes("CPS")||p!=null&&p.includes("NPM")||p!=null&&p.includes("CPM")?O=`${xi}/${tt.TASK_ACTION_DETAIL.FETCH_MAILS}?requestId=${p}`:p!=null&&p.includes("NLS")||p!=null&&p.includes("CLS")||p!=null&&p.includes("ELS")||p!=null&&p.includes("NLM")||p!=null&&p.includes("CLM")||p!=null&&p.includes("ELM")?O=`${vi}/${tt.TASK_ACTION_DETAIL.FETCH_MAILS}?requestId=${p}`:(p!=null&&p.includes("NBS")||p!=null&&p.includes("CBS")||p!=null&&p.includes("EBS")||p!=null&&p.includes("NBM")||p!=null&&p.includes("CBM")||p!=null&&p.includes("EBM"))&&(O=`${bi}/${tt.TASK_ACTION_DETAIL.FETCH_MAILS}?requestId=${p}`),O&&ue(O,"get",Z,q)};return A.useEffect(()=>(To(),So(),Eo(),()=>{Xo(mi.REQUEST_BENCH_TASK)}),[]),C("div",{id:"container_outermost",children:[C(hi,{fullWidth:!0,hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:Po,sx:{"& .MuiDialog-container":{"& .MuiPaper-root":{width:"100%",maxWidth:"max-content"}}},children:[C(di,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:(qn=(Jn=T)==null?void 0:Jn.background)==null?void 0:qn.header,display:"flex"},children:[f(V,{variant:"h6",children:"Attachments: "}),f(Mt,{sx:{width:"max-content"},onClick:Mo,children:f(gi,{})})]}),f(pi,{sx:{padding:".5rem 1rem"},children:f(Y,{children:f(P,{sx:{minWidth:800},children:C(fi,{sx:{height:"auto"},fullWidth:!0,children:[!!(v!=null&&v.length)&&f(De,{width:"800px",rows:v,columns:Hn,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action"}),!(v!=null&&v.length)&&f(V,{variant:"body2",children:"No Attachments Found"})]})})})})]}),C(hi,{open:x,onClose:J,hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},children:[C(di,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:(ei=(ti=T)==null?void 0:ti.background)==null?void 0:ei.header,display:"flex"},children:[" ",f(V,{variant:"h6",children:"Attachments"}),f(Mt,{sx:{width:"max-content"},onClick:J,children:f(gi,{})})]}),f(pi,{sx:{padding:".5rem 1rem"},children:f(Y,{children:f(P,{sx:{minWidth:800},children:C(fi,{sx:{height:"auto"},fullWidth:!0,children:[!!v.length&&f(De,{width:"70vw",rows:v,columns:Hn,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action"}),!v.length&&f(V,{variant:"body2",children:"No Attachments Found"})]})})})})]}),f("div",{className:"purchaseOrder",style:{...Zo,backgroundColor:(ii=(ni=T)==null?void 0:ni.background)==null?void 0:ii.container},children:f(Y,{spacing:1,children:C(rt,{children:[C(B,{container:!0,sx:Qo,children:[C(B,{item:!0,md:6,sx:{outerContainer_Information:Jo,display:"flex"},children:[f(B,{children:f(Mt,{color:"primary","aria-label":"upload picture",component:"label",sx:qo,children:f(aa,{sx:{fontSize:"25px",color:(ri=(si=T)==null?void 0:si.basic)==null?void 0:ri.black},onClick:()=>{Gn(-1)}})})}),C(B,{children:[f(V,{variant:"h3",children:f("strong",{children:"Request History"})}),f(V,{variant:"body2",color:T.secondary.grey,children:"This view displays the history of a Request"})]})]}),f(B,{item:!0,md:6,sx:{display:"flex"},children:C(B,{container:!0,direction:"row",justifyContent:"flex-end",alignItems:"center",spacing:0,children:[f(Tt,{title:"Mail",children:f(Mt,{children:f(Ns,{onClick:Vo})})}),f(Tt,{title:"Uploaded Attachments",arrow:!0,children:f(Mt,{onClick:z,sx:{"&:active":{backgroundColor:"rgba(17,52,166,0.3)",color:`${(ai=(oi=T)==null?void 0:oi.basic)==null?void 0:ai.black}`},backgroundColor:D?"rgba(17,52,166,0.3)":"transparent"},children:f(oa,{})})})]})})]}),f(B,{container:!0,spacing:1,children:f(ta,{open:x,onClose:Ao,"aria-labelledby":"modal-modal-title","aria-describedby":"modal-modal-description",children:f(P,{sx:vo,children:y!=""?f(rt,{children:(ht==null?void 0:ht.length)==0?C(Y,{justifyItems:"center",alignItems:"center",mt:5,children:[f(V,{variant:"h3",color:(ci=(li=T)==null?void 0:li.basic)==null?void 0:ci.black,children:"No Mail Found for this Request ID"}),f(ve,{size:"small",variant:"contained",sx:{marginRight:"1rem"},onClick:()=>{g("")},children:"Close"})]}):f(rt,{children:C(B,{container:!0,sx:{height:"100%",p:2},children:[f(B,{item:!0,xs:12,children:C(P,{sx:{border:`1px solid ${T.basic.black}`,borderRadius:"8px",width:"100%",height:"100%",boxSizing:"border-box",display:"flex",flexDirection:"column",backgroundColor:T.basic.white},p:3,children:[f(V,{variant:"h6",sx:{fontSize:"18px",fontWeight:"600",color:T.text.primary,mb:2},children:(y==null?void 0:y.subject)||"No Subject"}),f(P,{sx:{mb:2},children:C(Y,{spacing:1.5,children:[C(Y,{direction:"row",spacing:1,alignItems:"flex-start",children:[f(V,{sx:{color:T.text.secondary,width:"40px",flexShrink:0,fontSize:"13px",fontWeight:"500"},children:"To:"}),f(V,{sx:{flex:1,fontSize:"13px",color:T.text.charcoal,wordBreak:"break-word"},children:y==null?void 0:y.toParticipant})]}),C(Y,{direction:"row",spacing:1,alignItems:"flex-start",children:[f(V,{sx:{color:T.text.secondary,width:"40px",flexShrink:0,fontSize:"13px",fontWeight:"500"},children:"Cc:"}),f(V,{sx:{flex:1,fontSize:"13px",color:T.text.charcoal,wordBreak:"break-word"},children:y==null?void 0:y.ccParticipant})]}),C(Y,{direction:"row",spacing:1,alignItems:"flex-start",children:[f(V,{sx:{color:T.text.secondary,width:"40px",flexShrink:0,fontSize:"13px",fontWeight:"500"},children:"From:"}),f(V,{sx:{flex:1,fontSize:"13px",color:T.text.charcoal,wordBreak:"break-word"},children:y==null?void 0:y.fromUser})]}),f(V,{sx:{fontSize:"12px",color:T.text.secondary,borderBottom:`1px solid ${T.border.light}`,pb:2},children:zt(y==null?void 0:y.createdAt).format("DD MMM YYYY hh:mm:ss a")})]})}),f(P,{sx:{flexGrow:1,overflowY:"auto",backgroundColor:T.background.default,borderRadius:"4px",p:2,minHeight:"200px","& *":{fontFamily:"inherit"}},children:f("div",{dangerouslySetInnerHTML:{__html:y==null?void 0:y.content}})})]})}),f(B,{item:!0,xs:12,sx:{display:"flex",justifyContent:"flex-end",mt:2},children:f(ve,{size:"medium",variant:"contained",onClick:()=>g(""),sx:{minWidth:"100px",textTransform:"none",boxShadow:"none","&:hover":{boxShadow:"none"}},children:"Close"})})]})})}):f(rt,{children:f(De,{rows:ht,columns:Co,pageSize:10,getRowIdValue:"id",hideFooter:!1,title:`Email List (${ht==null?void 0:ht.length})`})})})})}),C(V,{variant:"h6",sx:{mt:2,mb:2},children:["Parent Request ID: ",i]}),(s==null?void 0:s.length)>0?f(rd,{data:s,childRequestID:p}):f(yo,{})]})})}),f(ia,{dialogState:L,openReusableDialog:_t,closeReusableDialog:Et,dialogTitle:X,dialogMessage:le,handleDialogConfirm:Et,dialogOkText:"OK",dialogSeverity:ce})]})};export{xd as default};
