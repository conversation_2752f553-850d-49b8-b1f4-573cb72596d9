const StatCardPC = ({ number, label }) => (
  <div
    style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      padding: 12,
      background: '#fff',
      borderRadius: 8,
      boxShadow: "0 5px 15px rgba(0, 0, 0, 0.05)",
      transition: "transform 0.3s ease",
      animation: "fadeIn 0.5s ease-out forwards",
      minHeight: 80
    }}
  >
    <div className="stat-number">{number}</div>
    <div className="stat-label">{label}</div>
  </div>
);

export default StatCardPC;
