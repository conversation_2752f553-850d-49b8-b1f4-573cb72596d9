import{r as w,a as d,S as me,T as q,p as M,d4 as ye,d6 as he,s as ue,u as fe,q as R,e as ge,aX as E,dx as Ne,dA as Se,dk as O,x as xe,j as T,l as f,dy as ke,h as Ie,b6 as be,dB as P,E as Ee,F as Te,G as Ae,dw as De}from"./index-75c1660a.js";import{u as ve}from"./useChangeLogUpdate-7dd0a930.js";const Re=({fallback:e="--",variant:m="text",delay:S=3e3,sx:k={fontSize:"1rem"}})=>{const[L,g]=w.useState(!0);return w.useEffect(()=>{const y=setTimeout(()=>{g(!1)},S);return()=>clearTimeout(y)},[S]),L?d(me,{variant:m,sx:k}):d(q,{component:"span",sx:k,children:e})},de=2,ce=M.createContext({}),we=M.forwardRef((e,m)=>{const S=M.useContext(ce);return d("div",{ref:m,...e,...S})}),Le=M.forwardRef(function(m,S){const{children:k,...L}=m,g=[];k.forEach(u=>{g.push(u)});const y=g.length,A=he.HEIGHT,I=()=>y>8?8*A:g.length*A;return d("div",{ref:S,children:d(ce.Provider,{value:L,children:d(ye,{itemData:g,height:I()+2*de,width:"100%",outerElementType:we,innerElementType:"ul",itemSize:A,overscanCount:5,itemCount:y,children:({data:u,index:l,style:C})=>{const i=u[l],N={...C,top:C.top+de};return d("li",{style:{...N,listStyle:"none"},children:i})}})})})});function qe(e){var Y,$,B,U,W,z,H,j,F,G,Q,X,J,Z,K,V,s,r,o,p,ee,te,ae;const m=ue(),{updateChangeLog:S}=ve(),k=fe(),g=new URLSearchParams(k.search).get("RequestId"),y=R(a=>a.payload.payloadData),A=k.pathname.includes("DisplayMaterialSAPView");R(a=>a.userManagement.roles);const{t:I}=ge(),u=R(a=>a.payload||{}),l=R(a=>{var t;return((t=a.AllDropDown)==null?void 0:t.dropDown)||{}}),C=R(a=>{var t;return((t=a.payload)==null?void 0:t.errorFields)||[]}),[i,N]=w.useState(null),c=((U=(B=($=(Y=u==null?void 0:u[e==null?void 0:e.materialID])==null?void 0:Y.payloadData)==null?void 0:$[e==null?void 0:e.viewName])==null?void 0:B[e==null?void 0:e.plantData])==null?void 0:U[e==null?void 0:e.keyName])??((W=u==null?void 0:u.payloadData)==null?void 0:W[e==null?void 0:e.keyName])??(((z=e==null?void 0:e.details)==null?void 0:z.fieldPriority)==="ApplyBus"||e!=null&&e.isRequestHeader?(H=e==null?void 0:e.details)==null?void 0:H.value:null);w.useEffect(()=>{var a,t,h;(((a=e==null?void 0:e.details)==null?void 0:a.visibility)===E.MANDATORY||((t=e==null?void 0:e.details)==null?void 0:t.visibility)==="Required")&&m(Ne((e==null?void 0:e.keyName)||"")),((h=e==null?void 0:e.details)==null?void 0:h.visibility)===E.DISPLAY&&m(Se(e==null?void 0:e.keyName))},[m,(j=e==null?void 0:e.details)==null?void 0:j.visibility,e==null?void 0:e.keyName]),w.useEffect(()=>{var a,t,h,x,ie,le;if(c!=null&&c!=="")if(c!=null&&c.code)N(c),O({materialID:(e==null?void 0:e.materialID)||"",keyName:(e==null?void 0:e.keyName)||"",data:c,viewID:e==null?void 0:e.viewName,itemID:e==null?void 0:e.plantData});else if(l!=null&&l[e==null?void 0:e.keyName]||(a=l==null?void 0:l[e==null?void 0:e.keyName])!=null&&a[e==null?void 0:e.plantData]){if(!Array.isArray(l==null?void 0:l[e==null?void 0:e.keyName])&&!Array.isArray((t=l==null?void 0:l[e==null?void 0:e.keyName])==null?void 0:t[e==null?void 0:e.plantData])){N(null);return}const n=(h=l[e==null?void 0:e.keyName])!=null&&h.length?(x=l[e==null?void 0:e.keyName])==null?void 0:x.find(b=>{var D,v;return((D=b==null?void 0:b.code)==null?void 0:D.trim())===((v=c==null?void 0:c.toString())==null?void 0:v.trim())}):(le=(ie=l[e==null?void 0:e.keyName])==null?void 0:ie[e==null?void 0:e.plantData])==null?void 0:le.find(b=>{var D,v;return((D=b==null?void 0:b.code)==null?void 0:D.trim())===((v=c==null?void 0:c.toString())==null?void 0:v.trim())});n?(N({code:n==null?void 0:n.code,desc:n==null?void 0:n.desc}),m(O({materialID:(e==null?void 0:e.materialID)||"",keyName:(e==null?void 0:e.keyName)||"",data:{code:n==null?void 0:n.code,desc:n==null?void 0:n.desc},viewID:e==null?void 0:e.viewName,itemID:e==null?void 0:e.plantData}))):(N(null),m(O({materialID:(e==null?void 0:e.materialID)||"",keyName:(e==null?void 0:e.keyName)||"",data:null,viewID:e==null?void 0:e.viewName,itemID:e==null?void 0:e.plantData})))}else N(null);else N(null)},[c]);const ne=(a,t)=>{var h,x;N(t),m(O({materialID:(e==null?void 0:e.materialID)||"",keyName:(e==null?void 0:e.keyName)||"",data:t??null,viewID:e==null?void 0:e.viewName,itemID:e==null?void 0:e.plantData})),g&&!De.includes(y==null?void 0:y.RequestStatus)&&S({materialID:e==null?void 0:e.selectedMaterialNumber,viewName:e==null?void 0:e.viewName,plantData:e==null?void 0:e.plantData,fieldName:(h=e==null?void 0:e.details)==null?void 0:h.fieldName,jsonName:(x=e==null?void 0:e.details)==null?void 0:x.jsonName,currentValue:`${t==null?void 0:t.code}-${(t==null?void 0:t.desc)??""}`,requestId:y==null?void 0:y.RequestId,childRequestId:g})},_=(F=e==null?void 0:e.details)==null?void 0:F.jsonName;return d(Ae,{item:!0,md:2,sx:{marginBottom:"12px !important"},children:((G=e==null?void 0:e.details)==null?void 0:G.visibility)==="Hidden"?null:d(xe,{children:A?T("div",{style:{padding:"16px",backgroundColor:f.primary.white,borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.1)",margin:"16px 0",transition:"all 0.3s ease"},children:[T(q,{variant:"body1",style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%",fontWeight:600,fontSize:"12px",marginBottom:"4px",display:"flex",alignItems:"center"},title:I((Q=e==null?void 0:e.details)==null?void 0:Q.fieldName),children:[I((X=e==null?void 0:e.details)==null?void 0:X.fieldName)||"Field Name",(((J=e==null?void 0:e.details)==null?void 0:J.visibility)===E.REQUIRED||((Z=e==null?void 0:e.details)==null?void 0:Z.visibility)===ke.MANDATORY)&&d("span",{style:{color:f.error.darkRed,marginLeft:"5px",fontSize:"1.1rem"},children:"*"})]}),d("div",{style:{fontSize:"0.8rem",color:f.black.dark,marginTop:"4px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",width:"100%",cursor:"pointer"},children:i!=null&&i.code||i!=null&&i.desc?d(Ie,{title:i!=null&&i.code?`${i==null?void 0:i.code} - ${(i==null?void 0:i.desc)||""}`:"--",arrow:!0,children:T("span",{children:[d("strong",{style:{fontWeight:600,color:f.secondary.grey,marginRight:"6px",letterSpacing:"0.5px",wordSpacing:"1px"},children:i==null?void 0:i.code}),(i==null?void 0:i.desc)&&T("span",{style:{fontWeight:500,color:f.secondary.grey,letterSpacing:"0.5px",wordSpacing:"1px"},children:["- ",i==null?void 0:i.desc]})]})}):d(Re,{fallback:"--"})})]}):T(Te,{children:[T(q,{variant:"body2",color:f.secondary.grey,sx:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%"},title:I((K=e==null?void 0:e.details)==null?void 0:K.fieldName),children:[I((V=e==null?void 0:e.details)==null?void 0:V.fieldName)||"Field Name",(((s=e==null?void 0:e.details)==null?void 0:s.visibility)==="Required"||((r=e==null?void 0:e.details)==null?void 0:r.visibility)===E.MANDATORY)&&d("span",{style:{color:f.error.dark},children:"*"})]}),d(be,{sx:{height:"31px","& .MuiAutocomplete-listbox":{padding:0,"& .MuiAutocomplete-option":{paddingLeft:"16px",paddingTop:"4px",paddingBottom:"4px",justifyContent:"flex-start"}},"& .MuiAutocomplete-option":{display:"flex",alignItems:"center",minHeight:"36px"},"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:f.black.dark,color:f.black.dark},backgroundColor:f.hover.light}},fullWidth:!0,disabled:(e==null?void 0:e.disabled)||((o=e.details)==null?void 0:o.visibility)===E.DISPLAY,size:"small",value:i,onChange:ne,options:(p=l==null?void 0:l[e==null?void 0:e.keyName])!=null&&p.length?l==null?void 0:l[e==null?void 0:e.keyName]:((ee=l==null?void 0:l[e==null?void 0:e.keyName])==null?void 0:ee[e==null?void 0:e.plantData])||[],required:((te=e==null?void 0:e.details)==null?void 0:te.visibility)===E.MANDATORY||((ae=e==null?void 0:e.details)==null?void 0:ae.visibility)==="Required",ListboxComponent:Le,getOptionLabel:a=>a!=null&&a.desc?`${(a==null?void 0:a.code)||""} - ${(a==null?void 0:a.desc)||""}`:`${(a==null?void 0:a.code)||""}`,renderOption:(a,t)=>d(q,{...a,component:"li",style:{fontSize:12,padding:"8px 16px",width:"100%",cursor:"pointer",display:"flex",alignItems:"start"},title:`${_===P.REQUEST_TYPE?"":t==null?void 0:t.code}${t!=null&&t.desc&&_!==P.REQUEST_TYPE?` - ${t==null?void 0:t.desc}`:`${t==null?void 0:t.desc}`}`,children:T("span",{style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:[d("strong",{children:t==null?void 0:t.code}),t!=null&&t.desc&&_!==P.REQUEST_TYPE?` - ${t==null?void 0:t.desc}`:""]})}),renderInput:a=>{var t,h,x;return d(Ee,{...a,variant:"outlined",placeholder:e!=null&&e.disabled||((t=e.details)==null?void 0:t.visibility)===E.DISPLAY?"":I(`SELECT ${((x=(h=e==null?void 0:e.details)==null?void 0:h.fieldName)==null?void 0:x.toUpperCase())||""}`),error:C.includes((e==null?void 0:e.keyName)||""),InputProps:{...a.InputProps}})}})]})})})}export{qe as A,Re as S};
