import{o as t}from"./index-17b8d91e.js";import{i as n}from"./Close-b4045724.js";import{i as a}from"./Dropdown-7f10d6c3.js";import{g as l,G as m,N as u,X as p}from"./Button-f51b50ce.js";const d=(e,i)=>({small:{"& .MuiInputBase-root":{minHeight:"1.5rem"},"& .MuiAutocomplete-input":{minHeight:"0.5rem",alignContent:"center"},...i&&{"& .MuiInputBase-input":{minHeight:"1.25rem"}}},medium:{"& .MuiInputBase-root":{minHeight:"2rem"},"& .MuiAutocomplete-input":{minHeight:"1rem",alignContent:"center"}},large:{"& .MuiInputBase-root":{minHeight:"2.25rem"},"& .MuiAutocomplete-input":{minHeight:"1.5rem",alignContent:"center"}}})[e],s=l({components:{MuiAutocomplete:{styleOverrides:{option:{overflow:"hidden",color:"var(--text-primary)",textOverflow:"ellipsis",fontSize:"0.875rem",fontWeight:400,lineHeight:"normal",fontFamily:"inherit",backgroundColor:"var(--background-default)","&:hover":{backgroundColor:"var(--background-read-only)",fontWeight:500},'&[aria-selected="true"]':{backgroundColor:"var(--primary-light) !important"},"&. Mui-selected":{backgroundColor:"var(--primary-light)"}},groupLabel:{color:"var(--text-primary)",textOverflow:"ellipsis",fontSize:"0.875rem",fontWeight:500,lineHeight:"normal",fontFamily:"inherit"},paper:{fontFamily:"inherit"},listbox:{padding:0}}}}}),g=m(u,{shouldForwardProp:e=>e!=="customSize"})(({customSize:e,multiple:i,readOnly:o,disabled:r})=>({"& .MuiOutlinedInput-root":{paddingRight:"1rem !important",...o===!0&&{backgroundColor:"var(--background-read-only)"},...r===!0&&{backgroundColor:"var(--background-disabled)",color:"var(--text-disabled)"}},"& .MuiAutocomplete-input":{paddingRight:"1.5rem !important"},"& .MuiAutocomplete-clearIcon":{padding:"0.5rem"},"& .MuiChip-root":{"& .MuiChip-label":{padding:"0rem "}},...d(e,i)}));function v({size:e="medium",...i}){return t.jsx(p,{theme:s,children:t.jsx(g,{...i,size:e==="large"?"medium":e,customSize:e,popupIcon:t.jsx(a,{size:"xsmall"}),clearIcon:t.jsx(n,{size:"xsmall"})})})}export{v};
