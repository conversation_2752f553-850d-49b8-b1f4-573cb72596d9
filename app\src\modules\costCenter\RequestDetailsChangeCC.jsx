import React, { useState, useEffect, forwardRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Button,
  Select,
  Tooltip,
  MenuItem,
  FormControl,
  Slide,
  tooltipClasses,
  InputLabel,
  Typography,
  IconButton,
  Box,
  Paper,
  Checkbox,
  ListItemText,
  TextField,
  Grid,
} from "@mui/material";
import { destination_CostCenter_Mass } from "../../destinationVariables";
import { doAjax } from "../../components/Common/fetchService";
import { useLocation, useNavigate } from "react-router-dom";
import ReusableSnackBar from "../../components/Common/ReusableSnackBar";
import DownloadDialog from "../../components/Common/DownloadDialog";
import FeedOutlinedIcon from "@mui/icons-material/FeedOutlined";
import { ERROR_MESSAGES, MODULE_MAP } from "@constant/enum";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import { REQUEST_TYPE } from "@constant/enum";
import {
  transformCostCenterResponseChange,
  showToast,
  changePayloadForCC,
} from "../../functions";
import FilterChangeDropdown from "../../components/Common/ui/dropdown/FilterChangeDropdown";
import { APP_END_POINTS } from "@constant/appEndPoints";
import useButtonDTConfig from "@hooks/useButtonDTConfig";
import BottomNavGlobal from "./../../components/RequestBench/RequestPages/BottomNavGlobal";
import { updateReqBenchRowCc } from "@app/costCenterTabsSlice";
import ReusableDataTable from "../../components/Common/ReusableTable";
import {
  setChangedFieldsMapCc,
  setFetchedCostCenterDataCc,
  setFetchReqBenchDataCc,
  setOriginalCostCenterDataCc,
  setOriginalReqBenchDataCc,
  updateCostCenterRowCc,
} from "@app/costCenterTabsSlice";
import SingleSelectDropdown from "@components/Common/ui/dropdown/SingleSelectDropdown";
import ObjectLockDialog from "@components/Common/ObjectLockDialog";
import { getFieldsForTemplate } from "@helper/fieldHelper";
const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="down" ref={ref} {...props} />;
});
const RequestDetailsChangeCC = ({
  reqBench,
  requestId,
  apiResponses,
  downloadClicked,
  setDownloadClicked,
}) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const {
    fetchedCostCenterData,
    originalCostCenterData,
    fetchReqBenchDataCC,
    originalReqBenchData,
    changedFieldsMap,
  } = useSelector((state) => state.costCenter);
  const requestHeaderData = useSelector(
    (state) => state.costCenter.payload.requestHeaderData
  );
  const initialPayload = useSelector((state) => state.request.requestHeader);
  const filteredButtons = useSelector((state) => state.payload.filteredButtons);
  const task = useSelector((state) => state?.userManagement.taskData);
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const RequestId = queryParams.get("RequestId");
  const isreqBench = queryParams.get("reqBench");
  const [open, setOpen] = useState(true);
  const [selectedControllingArea, setSelectedControllingArea] = useState("");
  const [selectedRow, setSelectedRow] = useState(null);
  const [dropdownDataCompany, setDropdownDataCompany] = useState([]);
  const [openDownloadDialog, setOpenDownloadDialog] = useState(false);
  const [selectedCompanyCodes, setSelectedCompanyCodes] = useState([]);
  const [costCenterOptions, setCostCenterOptions] = useState([]);
  const [selectedCostCenters, setSelectedCostCenters] = useState([]);
  const [successMsg, setSuccessMsg] = useState(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [blurLoading, setBlurLoading] = useState("");
  const [loaderMessage, setLoaderMessage] = useState("");
  const [downloadType, setDownloadType] = useState("systemGenerated");
  const [dropdownDataCountry, setDropdownDataCountry] = useState([]);
  const [dropdownDataRegion, setDropdownDataRegion] = useState([]);
  const [dropdownDataSegment, setDropdownDataSegment] = useState([]);
  const [dropdownAamnum, setDropdwnAamnum] = useState([]);
  const [dropdownDataCostCenterType, setDropdownDataCostCenterType] = useState(
    []
  );
  const [dropdownDataFunctionalArea, setDropdownDataFunctionalArea] = useState(
    []
  );
  const [costcenterResponse, setCostcenterResponse] = useState([]);
  const [openSnackBar, setOpenSnackBar] = useState(false);
  const [alertMsg, setAlertMsg] = useState("");
  const [alertType, setAlertType] = useState("success");
  const [isLoading, setIsLoading] = useState(false);
  const [duplicateFieldsData, setDuplicateFieldsData] = useState([]);
  const [showTableInDialog, setShowTableInDialog] = useState(false);
  const { getButtonsDisplayGlobal } = useButtonDTConfig();
  const lockIndicatorMap = useSelector(
    (state) => state.payload.lockIndicatorData
  );
  const requestBenchData = location.state;
  const isButtonEnabled = requestBenchData?.reqStatus === "Validated-Requestor";
  const requestHeaderSlice = useSelector(
    (state) => state.request.requestHeader
  );
  const dropdownFields = {
    segment: dropdownDataSegment,
    country: dropdownDataCountry,
    region: dropdownDataRegion,
    pcaamnum: dropdownAamnum,
    CostcenterType: dropdownDataCostCenterType,
  };
  const [showGrid, setShowGrid] = useState(false);
  const changeFieldDataRaw = useSelector(
    (state) => state?.payload?.changeFieldSelectiondata
  );
  const changeFieldData = Array.isArray(changeFieldDataRaw)
    ? changeFieldDataRaw
    : [];
  const fieldNameListRaw = requestHeaderData?.FieldName;
  const reqBenchFieldName = apiResponses?.[0]?.Torequestheaderdata?.FieldName;
  const rawFieldName = reqBench ? reqBenchFieldName : fieldNameListRaw;
  const fieldNameList = Array.isArray(rawFieldName)
    ? rawFieldName.map((f) => f.trim())
    : typeof rawFieldName === "string"
    ? rawFieldName.split(",").map((f) => f.trim())
    : [];
  const { allFields, mandatoryFields, headerFields, fieldMetaMap } =
    getFieldsForTemplate(changeFieldData, fieldNameList);
  useEffect(() => {
    if (task?.ATTRIBUTE_1 || RequestId) {
      getButtonsDisplayGlobal("ET CC", "MDG_DYN_BTN_DT", "v2");
    }
  }, [task]);
  // Function to handle opening and closing the popup
  const handleClose = () => {
    setOpen(false);
    setDownloadClicked(false);
    navigate("/requestbench");
  };
  const handleOk = (params) => {
    getObjectLock(params);
  };
  // Dropdown change handlers
  const allColumns = [
    {
      field: "included",
      headerName: "Included",
      width: 100,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => <Checkbox checked={true} disabled={false} />,
    },
    {
      field: "lineNumber",
      headerName: "Sl No",
      width: 100,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        const rowIndex = (
          reqBench ? fetchReqBenchDataCC : fetchedCostCenterData
        ).findIndex((row) => row.id === params.row.id);
        return <div>{rowIndex + 1}</div>;
      },
    },
    {
      field: "controllingArea",
      headerName: "Controlling Area",
      width: 150,
      editable: false,
    },
    {
      field: "compCode",
      headerName: "Company Codes",
      width: 150,
      editable: false,
      renderCell: (params) => (
        <span
          style={{ color: "#9e9e9e", pointerEvents: "none", cursor: "default" }}
        >
          {params.value || ""}
        </span>
      ),
    },
    {
      field: "costCenter",
      headerName: "Cost Center",
      width: 150,
      editable: false,
      renderCell: (params) => (
        <span
          style={{ color: "#9e9e9e", pointerEvents: "none", cursor: "default" }}
        >
          {params.value || ""}
        </span>
      ),
    },
    {
      field: "FuncAreaLong",
      headerName: "Functional Area",
      width: 250,
      editable: false,
      renderCell: (params) => {
        const field = params.field;
        const options = dropdownDataFunctionalArea || [];
        const selectedValue =
          options.find(
            (item) =>
              item.code === params.row[field] || item.desc === params.row[field]
          ) || null;
        return (
          <SingleSelectDropdown
            options={options}
            value={selectedValue}
            onChange={(selectedOption) => {
              const newValue = selectedOption?.code || "";
              const updatedRow = {
                ...params.row,
                [field]: newValue,
              };
              if (reqBench) {
                dispatch(updateReqBenchRowCc(updatedRow));
              } else {
                dispatch(updateCostCenterRowCc(updatedRow));
              }
            }}
            placeholder={`Select ${params.colDef.headerName}`}
            disabled={false}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "name",
      headerName: "Short Description",
      width: 200,
      editable: false,
    },
    {
      field: "description",
      headerName: "Long Description",
      width: 250,
      editable: false,
    },
    {
      field: "userResponsible",
      headerName: "CC User Responsible",
      width: 250,
      editable: false,
    },
    {
      field: "personResponsible",
      headerName: "CC Person Responsible",
      width: 250,
      editable: false,
    },
    {
      field: "blockingStatus",
      headerName: "Blocking Status",
      width: 250,
      editable: false,
      renderCell: (params) => {
        if (requestHeaderData?.TemplateName === "All Other Fields") {
          return "Unblock";
        } else if (requestHeaderData?.TemplateName === "Block") {
          return "Block";
        }
      },
    },
    { field: "createdBy", headerName: "Created By", width: 150 },
    {
      field: "validFrom",
      headerName: "Valid From",
      width: 150,
    },
    { field: "validTo", headerName: "Valid To", width: 150 },
    { field: "city", headerName: "City", width: 150, editable: true },
    {
      field: "country",
      headerName: "Country/Reg.",
      width: 150,
      editable: true,
    },
    { field: "street", headerName: "Street", width: 150, editable: true },
    { field: "pocode", headerName: "Postal Code", width: 150, editable: true },
    { field: "region", headerName: "Region", width: 150, editable: true },
    { field: "name1", headerName: "Name 1", width: 150, editable: true },
    { field: "name2", headerName: "Name 2", width: 150, editable: true },
    { field: "name3", headerName: "Name 3", width: 150, editable: true },
    { field: "name4", headerName: "Name 4", width: 150, editable: true },
  ];
  const fixedColumns = allColumns.slice(0, 4);
  const dynamicColumns = allColumns
    .slice(4)
    .filter((col) => allFields.includes(col.headerName?.trim()))
    .map((col) => {
      const trimmedHeader = col.headerName?.trim();
      const isMandatory = mandatoryFields.includes(trimmedHeader);
      const isEditableField = [
        "description",
        "name",
        "userResponsible",
        "costCenterName",
        "personResponsible",
        "location",
        "street",
        "pocode",
        "name1",
        "name2",
        "name3",
        "name4",
      ].includes(col.field);
      const normalizedField = col.field?.toLowerCase();
      const fieldMeta = fieldMetaMap[normalizedField] || {};
      const maxLength = fieldMeta.maxLength;
      return {
        ...col,
        headerName: trimmedHeader,
        editable: true,
        renderHeader: () =>
          isMandatory ? (
            <span>
              {trimmedHeader} <span style={{ color: "red" }}>*</span>
            </span>
          ) : (
            trimmedHeader
          ),
        ...(isEditableField && {
          renderCell: (params) => (
            <TextField
              value={params.value || ""}
              onChange={(e) =>
                params.api.setEditCellValue({
                  id: params.id,
                  field: params.field,
                  value: e.target.value.toUpperCase(),
                })
              }
              variant="outlined"
              size="small"
              fullWidth
            />
          ),
          renderEditCell: (params) => {
            const currentLength = (params.value || "").length;
            return (
              <TextField
                value={params.value || ""}
                onChange={(e) =>
                  params.api.setEditCellValue({
                    id: params.id,
                    field: params.field,
                    value: e.target.value.toUpperCase(),
                  })
                }
                variant="outlined"
                size="small"
                fullWidth
                placeholder={`Enter ${trimmedHeader}`}
                inputProps={{ maxLength }}
                helperText={`${currentLength}/${maxLength}`}
              />
            );
          },
        }),
      };
    });
  const columns = [...fixedColumns, ...dynamicColumns];
  const processRowUpdate = (newRow) => {
    dispatch(updateCostCenterRowCc(newRow));
    return newRow;
  };
  const processRowUpdateReqBench = (newRow) => {
    dispatch(updateReqBenchRowCc(newRow));
    return newRow;
  };
  const handleRowClick = (params) => {
    setSelectedRow(params.row);
  };
  useEffect(() => {
    if (selectedControllingArea && selectedCompanyCodes.length > 0) {
      fetchCostCenters();
    }
  }, [selectedControllingArea, selectedCompanyCodes]);
  useEffect(() => {
    getCompanyCode();
    getCostCenterCategory();
    getFunctionalArea();
  }, []);
  const getCompanyCode = () => {
    const hSuccess = (data) => {
      setDropdownDataCompany(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "CompanyCode", data: data.body },
      });
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCompanyCodeBasedOnControllingArea?controllingArea=ETCA&rolePrefix=ETP`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCostCenterCategory = () => {
    const hSuccess = (data) => {
      setDropdownDataCostCenterType(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "CostcenterType", data: data.body },
      });
    };
    const hError = (error) => {
      console.error(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCostCenterCategory`,
      "get",
      hSuccess,
      hError
    );
  };
  const getFunctionalArea = () => {
    const hSuccess = (data) => {
      setDropdownDataFunctionalArea(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "FuncAreaLong", data: data.body },
      });
    };
    const hError = (error) => {
      console.error(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getFunctionalArea`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCountryData = () => {
    const hSuccess = (data) => {
      setDropdownDataCountry(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "Country", data: data.body },
      });
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCountryOrReg`,
      "get",
      hSuccess,
      hError
    );
  };
  useEffect(() => {
    getCountryData();
  }, []);
  const fetchCostCenters = () => {
    const controllingArea = `${selectedControllingArea}`;
    selectedCompanyCodes.forEach((companyCode) => {
      const payload = {
        controllingArea,
        companyCode,
        top: "100",
        skip: "0",
      };
      doAjax(
        `/${destination_CostCenter_Mass}/data/getCostCentersNo`,
        "post",
        (data) => {
          if (Array.isArray(data.body?.list)) {
            const newCostCenters = data.body.list
              .map((item) => item.code)
              .filter(
                (code, index, self) => code && self.indexOf(code) === index
              );
            setCostCenterOptions((prev) => [
              ...new Set([...prev, ...newCostCenters]),
            ]);
          }
        },
        (err) => console.error("Cost Center fetch failed", err),
        payload
      );
    });
  };
  const transformCostCenterData = (data) => {
    return data.map((item) => {
      const shouldUnblock =
        item?.controlTabDto?.LockIndActualPrimaryCosts ||
        item?.controlTabDto?.LockIndPlanPrimaryCosts ||
        item?.controlTabDto?.LockIndActSecondaryCosts ||
        item?.controlTabDto?.LockIndPlanSecondaryCosts ||
        item?.controlTabDto?.LockIndActualRevenues ||
        item?.controlTabDto?.LockIndPlanRevenues;
      const existingBlockingStatus = shouldUnblock ? "Block" : "Unblock";
      return {
        id: item.costCenter,
        costCenterHeaderID: item?.costCenterHeaderId,
        costCenterErrorId: item?.costCenterErrorId,
        costCenter: item.costCenter,
        controllingArea: item.controllingArea,
        CostcenterType: item?.basicDataTabDto?.CostcenterType,
        FuncAreaLong: item?.basicDataTabDto?.FuncAreaLong,
        currency: item?.basicDataTabDto?.Currency,
        profitCtr: item?.basicDataTabDto?.ProfitCtr,
        compCode: item?.basicDataTabDto?.CompCode,
        name: item?.basicDataTabDto?.Name,
        description: item.basicDataTabDto?.Descript || "",
        userResponsible: item.basicDataTabDto?.PersonInCharge || "",
        personResponsible: item.basicDataTabDto?.PersonInChargeUser || "",
        createdBy: item.historyTabDto?.ReqCreatedBy || "",
        validFrom: item?.fromValid || "",
        validTo: item?.toValid || "",
        city: item.addressTabDto?.City || "",
        street: item.addressTabDto?.Street || "",
        country: item.addressTabDto?.Country || "",
        addrRegion: item.addressTabDto?.AddrRegion || "", // renamed from "region"
        regio: item.addressTabDto?.Regio || "", // renamed from "region"
        name1: item.addressTabDto?.Name1 || "",
        name2: item.addressTabDto?.Name2 || "",
        name3: item.addressTabDto?.Name3 || "",
        name4: item.addressTabDto?.Name4 || "",
        lockIndActualPrimaryCosts: getLockIndicatorValue(
          item?.basicDataTabDto?.CostcenterType,
          requestHeaderData?.TemplateName,
          existingBlockingStatus,
          "Actual Primary Costs",
          item?.controlTabDto?.LockIndActualPrimaryCosts
        ),
        lockIndPlanPrimaryCosts: getLockIndicatorValue(
          item?.basicDataTabDto?.CostcenterType,
          requestHeaderData?.TemplateName,
          existingBlockingStatus,
          "Plan Primary Costs",
          item?.controlTabDto?.LockIndPlanPrimaryCosts
        ),
        lockIndActSecondaryCosts: getLockIndicatorValue(
          item?.basicDataTabDto?.CostcenterType,
          requestHeaderData?.TemplateName,
          existingBlockingStatus,
          "Actual Secondary Costs",
          item?.controlTabDto?.LockIndActSecondaryCosts
        ),
        lockIndPlanSecondaryCosts: getLockIndicatorValue(
          item?.basicDataTabDto?.CostcenterType,
          requestHeaderData?.TemplateName,
          existingBlockingStatus,
          "Plan Secondary Costs",
          item?.controlTabDto?.LockIndPlanSecondaryCosts
        ),
        lockIndActualRevenues: getLockIndicatorValue(
          item?.basicDataTabDto?.CostcenterType,
          requestHeaderData?.TemplateName,
          existingBlockingStatus,
          "Actual Revenue",
          item?.controlTabDto?.LockIndActualRevenues
        ),
        lockIndPlanRevenues: getLockIndicatorValue(
          item?.basicDataTabDto?.CostcenterType,
          requestHeaderData?.TemplateName,
          existingBlockingStatus,
          "Plan Revenue",
          item?.controlTabDto?.LockIndPlanRevenues
        ),
      };
    });
  };
  const getObjectLock = (params) => {
    if (!selectedCostCenters.length || !selectedControllingArea) return;
    const objectLockPayload = selectedCostCenters?.map((cc) => ({
      controllingArea: selectedControllingArea,
      costCenter: cc,
      changedFieldsToCheck: initialPayload?.fieldName,
    }));
    const successHandler = (response) => {
      const hasError = response?.some((item) => item?.statusCode !== 200);
      if (!hasError) {
        if (params === "OK") {
          setOpen(false);
          setShowGrid(true);
          fetchCostCenterDetails(); // 🔥 Call the new API here
        } else if (params === "Download") {
          handleDownloadDialogOpen();
        }
      } else {
        const filteredData = response.filter((item) => item.statusCode === 400);
        let duplicateFieldsArr = [];
        filteredData?.forEach((item, index) => {
          const dataHash = {
            id: `${item?.body?.costCenter}_${index}`, // ✅ UNIQUE ID
            objectNo: item?.body?.costCenter,
            reqId: item?.body?.matchingRequests
              ?.map((req) => req?.matchingRequestHeaderId)
              ?.filter(Boolean),
            childReqId: item?.body?.matchingRequests
              ?.map((req) => req?.matchingChildHeaderIdsSet)
              ?.filter(Boolean),
            requestedBy: item?.body?.matchingRequests
              ?.map((req) => req?.RequestCreatedBy)
              ?.filter(Boolean),
          };
          duplicateFieldsArr.push(dataHash);
        });
        setDuplicateFieldsData(duplicateFieldsArr);
        setShowTableInDialog(true);
      }
    };
    const errorHandler = (err) => {
      console.error("Failed to fetch cost center details", err);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/alter/checkDuplicateCCRequest`,
      "post",
      successHandler,
      errorHandler,
      objectLockPayload
    );
  };
  const fetchCostCenterDetails = () => {
    if (!selectedCostCenters.length || !selectedControllingArea) return;
    const payload = {
      coAreaCCs: selectedCostCenters.map((cc) => ({
        costCenter: cc,
        controllingArea: selectedControllingArea,
      })),
    };
    const successHandler = (data) => {
      const rawData = data?.body || [];
      setCostcenterResponse(rawData);
      const transformed = transformCostCenterData(rawData);
      dispatch(setFetchedCostCenterDataCc(transformed));
      dispatch(setOriginalCostCenterDataCc(transformed));
    };
    const errorHandler = (err) => {
      console.error("Failed to fetch cost center details", err);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCostCentersData`,
      "post",
      successHandler,
      errorHandler,
      payload
    );
  };
  useEffect(() => {
    if (
      reqBench === "true" &&
      Array.isArray(apiResponses) &&
      apiResponses.length > 0
    ) {
      const transformedData = transformCostCenterResponseChange(apiResponses);
      dispatch(setFetchReqBenchDataCc(transformedData));
      dispatch(setOriginalReqBenchDataCc(transformedData));
    }
  }, [apiResponses, reqBench]);
  useEffect(() => {
    if (downloadClicked) {
      setOpen(true);
    }
  }, [downloadClicked]);

  const parsedData = (apiResponses ?? []).map((item) => {
    let changedFields = {};
    if (typeof item.changedFields === "object" && item.changedFields !== null) {
      changedFields = item.changedFields;
    } else if (typeof item.ChangedFields === "string") {
      try {
        changedFields = JSON.parse(item.ChangedFields);
      } catch {
        changedFields = {};
      }
    }
    const { changedFields: _, ChangedFields, ...rest } = item;
    return {
      ...rest,
      changedFields,
    };
  });
  useEffect(() => {
    if (!parsedData || parsedData.length === 0) return;
    const newChangedFieldsMap = {};
    parsedData.forEach((row) => {
      newChangedFieldsMap[row.CostCenterID] = row.changedFields || {};
    });
    dispatch(setChangedFieldsMapCc(newChangedFieldsMap));
  }, [apiResponses]);
  const getLockIndicatorValue = (
    CCCategory,
    TemplateName,
    ExistingBlockingStatus,
    LockIndicator,
    ExistingLockIndicatorValue
  ) => {
    if (TemplateName === "Block") {
      return "X";
    } else if (TemplateName === "All Other Fields") {
      if (ExistingBlockingStatus === "Unblock") {
        return ExistingLockIndicatorValue === true ? "X" : ""; // Return the existing value for Unblock
      } else if (ExistingBlockingStatus === "Block") {
        const match = lockIndicatorMap.find((obj) =>
          obj.hasOwnProperty(CCCategory)
        );
        if (match) {
          const indicators = match[CCCategory];
          if (indicators.includes(LockIndicator)) {
            return "X"; // Block if the LockIndicator is in the list for the category
          }
        }
      }
    } else if (TemplateName === "CC Category & FERC Indicator Change") {
      debugger;
      if (ExistingBlockingStatus === "Unblock") {
        const match = lockIndicatorMap.find((obj) =>
          obj.hasOwnProperty(CCCategory)
        );
        if (match) {
          const indicators = match[CCCategory];
          if (indicators.includes(LockIndicator)) {
            return "X"; // Block if the LockIndicator is in the list for the category
          }
        }
      } else if (ExistingBlockingStatus === "Block") {
        return "X"; // Block all indicators in this case
      }
    }
    return ""; // Default case if no conditions match
  };
  const allData = reqBench ? fetchReqBenchDataCC : fetchedCostCenterData;
  const payload = changePayloadForCC(
    requestHeaderSlice,
    initialPayload,
    task,
    isreqBench,
    fetchReqBenchDataCC,
    fetchedCostCenterData
  );
  const handleSaveAsDraft = () => {
    const Payload = payload;
    const hSuccess = (data) => {
      setIsLoading(false);
      setAlertType("success");
      setAlertMsg("Cost Centers change submission for save as draft initiated");
      setOpenSnackBar(true);
      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };
    const hError = (error) => {
      console.log("Errordata", error);
      setIsLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving the draft.");
      setOpenSnackBar(true);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/massAction/changeCostCentersSaveAsDraft`,
      "POST",
      hSuccess,
      hError,
      Payload
    );
  };
  const handleSendBack = () => {
    const Payload = payload;
    const hSuccess = (data) => {
      setIsLoading(false);
      setAlertType("success");
      setAlertMsg("Cost Centers change submission for save as draft initiated");
      setOpenSnackBar(true);
      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };
    const hError = (error) => {
      setIsLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving the draft.");
      setOpenSnackBar(true);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/massAction/changeCostCentersSaveAsDraft`,
      "POST",
      hSuccess,
      hError,
      Payload
    );
  };
  const handleRejectAndCancel = () => {
    const Payload = payload;
    const hSuccess = (data) => {
      setIsLoading(false);
      setAlertType("success");
      setAlertMsg("Cost Centers change submission for save as draft initiated");
      setOpenSnackBar(true);
      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };
    const hError = (error) => {
      console.log("Errordata", error);
      setIsLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving the draft.");
      setOpenSnackBar(true);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/massAction/changeCostCentersSaveAsDraft`,
      "POST",
      hSuccess,
      hError,
      Payload
    );
  };
  const handleSubmitForReview = () => {
    const Payload = payload;
    const hSuccess = (data) => {
      setIsLoading(false);
      setAlertType("success");
      setAlertMsg("Cost Centers submit for review successful");
      setOpenSnackBar(true);
      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };
    const hError = (error) => {
      console.log("Errordata", error);
      setIsLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving submit for review.");
      setOpenSnackBar(true);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/massAction/changeCostCentersSubmitForReview`,
      "POST",
      hSuccess,
      hError,
      Payload
    );
  };
  const handleSubmitForApprove = () => {
    const Payload = payload;
    const hSuccess = (data) => {
      setIsLoading(false);
      setAlertType("success");
      setAlertMsg("Cost Centers successfuly Approved");
      setOpenSnackBar(true);
      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };
    const hError = (error) => {
      console.log("Errordata", error);
      setIsLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving submit for review.");
      setOpenSnackBar(true);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/massAction/changeCostCentersApprovalSubmit`,
      "POST",
      hSuccess,
      hError,
      Payload
    );
  };
  const validateAllRows = () => {
    const Payload = payload;
    const hSuccess = (data) => {
      setIsLoading(false);
      setAlertType("success");
      setAlertMsg("Cost Centers change submission for save as draft initiated");
      setOpenSnackBar(true);
      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };
    const hError = (error) => {
      console.log("Errordata", error);
      setIsLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving the draft.");
      setOpenSnackBar(true);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/massAction/validateMassCostCenter`,
      "POST",
      hSuccess,
      hError,
      Payload
    );
  };
  const handleValidateAndSyndicate = (type) => {
    const Payload = fetchReqBenchDataCC.map((pcData) => {
      return {
        requestInProcess: "",
        ProfitCenterID: pcData.ProfitCenterID || "",
        TemplateName: requestHeaderData?.TemplateName || "",
        TemplateHeaders: "",
        IsScheduled: false,
        Action: "",
        RequestID: pcData.RequestID || "",
        TaskStatus: null,
        TaskId: task?.taskId,
        ReqCreatedBy: pcData?.createdBy || "",
        ReqCreatedOn: pcData?.historyTabDto?.ReqCreatedOn || "",
        RequestStatus: requestHeaderData?.requestStatus || "",
        CreationId: "",
        EditId: "",
        DeleteId: "",
        MassCreationId: "",
        MassEditId: requestId || "",
        MassDeleteId: "",
        ProfitCenterErrorID: pcData.ProfitCenterErrorID || "",
        RequestType: requestHeaderData?.requestType || "",
        MassRequestStatus: "",
        Remarks: null,
        TempLockRemarks: null,
        Info: "",
        ChangedFields: "",
        PrctrName: pcData?.profitCenterName || "",
        LongText: pcData?.description || "",
        InChargeUser: pcData?.userResponsible || "",
        InCharge: pcData?.personResponsible || "",
        Department: pcData?.Department || "",
        PrctrHierGrp: pcData?.PrctrHierGrp || "",
        Segment: pcData?.segment || "",
        LockInd: pcData?.indicatorsTabDto?.LockIndicator || false,
        Template: "",
        Title: pcData?.addressTabDto?.Title || "",
        Name1: pcData?.name1 || "",
        Name2: pcData?.name2 || "",
        Name3: pcData?.name3 || "",
        Name4: pcData?.name4 || "",
        Street: pcData?.street || "",
        City: pcData?.city || "",
        District: pcData?.district || "",
        Country: pcData?.country || "",
        Taxjurcode: pcData?.TaxJurisdiction || "",
        PoBox: pcData?.PoBox || "",
        PostalCode: pcData?.PostalCode || "",
        PobxPcd: pcData?.PobxPcd || "",
        Regio: pcData?.Regio || "",
        Language: pcData?.communicationTabDto?.Language || "",
        Telephone: pcData?.communicationTabDto?.Telephone || "",
        Telephone2: pcData?.communicationTabDto?.Telephone2 || "",
        Telebox: pcData?.communicationTabDto?.Telebox || "",
        Telex: pcData?.communicationTabDto?.Telex || "",
        FaxNumber: pcData?.communicationTabDto?.FaxNumber || "",
        Teletex: pcData?.communicationTabDto?.Teletex || "",
        Printer: pcData?.communicationTabDto?.Printer || "",
        DataLine: pcData?.communicationTabDto?.DataLine || "",
        ProfitCenter: pcData?.profitCenter || "",
        COArea: pcData?.controllingArea || "ETCA",
        ValidfromDate:
          pcData?.basicDataTabDto?.ValidfromDate || "/Date(-2208988800000)/",
        ValidtoDate:
          pcData?.basicDataTabDto?.ValidtoDate || "/Date(253402214400000)/",
        Testrun: null,
        IsFirstSynCompleted: false,
        TempLockIsSelectedForSyn: false,
        SelectedByRequestorToDisplay: false,
        IsSunoco: false,
        Countryiso: "",
        LanguIso: "",
        Logsystem: "",
        GeneralInfoID: null,
        RequestPriority: requestHeaderData?.requestPriority || "",
        BusinessJustification: null,
        SAPorJEErrorCheck: null,
        BusinessSegment: "CRUDE",
        HierarchyRegion: null,
        PCAAMNumber: pcData?.basicDataTabDto?.PCAAMNumber || "",
        ValidationDoneBy: "MDM Approval",
        TotalIntermediateTasks: pcData?.totalIntermediateTasks ?? "",
        ToCompanycode: [
          {
            CompCodeID: pcData.CompCodeID,
            CompanyCode: pcData.companyCode || "",
            CompanyName: "",
            AssignToPrctr: "",
            Venture: "",
            RecInd: "",
            EquityTyp: "",
            JvOtype: "",
            JvJibcl: "",
            JvJibsa: "",
          },
        ],
        Torequestheaderdata: {
          RequestId: requestHeaderData?.RequestId || "",
          ReqCreatedBy: requestHeaderData?.ReqCreatedBy || "",
          ReqCreatedOn:
            requestHeaderData?.ReqCreatedOn || "/Date(1743998564967)/",
          ReqUpdatedOn:
            requestHeaderData?.ReqUpdatedOn || "/Date(1743998564967)/",
          RequestType: requestHeaderData?.RequestType || "",
          RequestPrefix: requestHeaderData?.RequestPrefix || "",
          RequestPriority: requestHeaderData?.RequestPriority || "",
          RequestDesc: requestHeaderData?.RequestDesc || "",
          RequestStatus: "Draft",
          FirstProd: "",
          LaunchDate: "",
          LeadingCat: "Anesthesia/Pain Management",
          Division: "00",
          TemplateName: "",
          FieldName: "",
          Region: "",
          FilterDetails: null,
          IsBifurcated: null,
        },
        TochangeLogData: {
          ChangeLogData: null,
          ChangeLogId: pcData.ChangeLogId || "",
          RequestHeaderId: "",
          RequestId: "",
        },
        ToProfitCenterErrorData: {
          ProfitCenterErrorId: pcData.ProfitCenterErrorID || "",
          RequestId: pcData.RequestID || "",
          ProfitCenter: pcData?.profitCenter || "",
          CompanyCode: pcData?.companyCode || "",
          Segment: pcData?.basicDataTabDto?.Segment || "",
          PcAamNumber: pcData?.basicDataTabDto?.PCAAMNumber || "",
          ControllingArea: pcData?.controllingArea || "",
          SapMessage: "",
          ObjectSapError: "",
          ObjectDbError: "",
          ObjectExcelError: "",
          ShortDescSapError: "",
          ShortDescDbError: "",
          ShortDescExcelError: "",
          LongDescSapError: "",
          LongDescDbError: "",
          LongDescExcelError: "",
          AddrValidationError: "",
          PersonResponsibleError: "",
          DmsAttachmentErrorStatus: "",
          SapAttachmentErrorStatus: "",
          RemovalNodeErrorStatus: "",
          AddNodeErrorStatus: "",
        },
        ToGeneralInfoData: {
          GeneralInfoId: pcData.GeneralInfoId || "",
          RequestPriority: "",
          BusinessJustification: "",
          SAPorJEErrorCheck: "",
          BusinessSegment: "",
          Region: "",
        },
      };
    });
    const hSuccess = (data) => {
      setIsLoading(false);
      setAlertType("success");
      setAlertMsg("Cost Centers submit for review successful");
      setOpenSnackBar(true);
      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };
    const hError = (error) => {
      console.log("Errordata", error);
      setIsLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving submit for review.");
      setOpenSnackBar(true);
    };
    doAjax(
      type === "validate"
        ? `/${destination_CostCenter_Mass}/massAction/validateMassCostCenter`
        : `/${destination_CostCenter_Mass}/massAction/changeCostCentersApproved`,
      "POST",
      hSuccess,
      hError,
      Payload
    );
  };
  const handleSnackBarOpen = () => {
    setOpenSnackbar(true);
  };
  const handleSnackBarClose = () => {
    setOpenSnackbar(false);
  };
  const handleDownloadDialogOpen = () => {
    setOpenDownloadDialog(true);
  };
  const handleDownloadDialogClose = () => {
    setOpenDownloadDialog(false);
    setDownloadType("systemGenerated");
  };
  const handleDownloadTypeChange = (event) => {
    setDownloadType(event?.target?.value);
  };
  const onDownloadTypeChange = () => {
    if (downloadType === "systemGenerated") {
      handleDownload();
      handleDownloadDialogClose();
    }
    if (downloadType === "mailGenerated") {
      handleEmailDownload();
      handleDownloadDialogClose();
    }
  };
  const handleDownload = () => {
    setLoaderMessage(
      "Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience."
    );
    setBlurLoading(true);
    setOpen(false);
    setDownloadClicked(false);
    if (!RequestId) {
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    }
    let payload = {
      coAreaCCs: selectedCostCenters.map((cc) => ({
        costCenter: cc,
        controllingArea: selectedControllingArea,
      })),
      requestId:
        requestHeaderData?.RequestId || initialPayload?.requestId || "",
      templateHeaders: requestHeaderData?.FieldName
        ? requestHeaderData.FieldName?.join("$^$")
        : "",
      templateName: requestHeaderData?.TemplateName
        ? requestHeaderData.TemplateName
        : "",
      dtName: "MDG_CHANGE_TEMPLATE_DT",
      version: "v6",
    };
    const hSuccess = (response) => {
      if (response?.size == 0) {
        setBlurLoading(false);
        setLoaderMessage("");
        showToast(ERROR_MESSAGES?.NO_DATA_FOUND, "error", {
          position: "top-center",
          largeWidth: true,
        });
        setTimeout(() => {
          navigate(APP_END_POINTS?.REQUEST_BENCH);
        }, 2600);
        return;
      }
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");
      link.href = href;
      link.setAttribute(
        "download",
        `${requestHeaderData?.TemplateName}_Mass Change.xlsx`
      );
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
      setBlurLoading(false);
      setLoaderMessage("");
      setSuccessMsg(true);
      setMessageDialogMessage(
        `${requestHeaderData?.TemplateName}_Mass Change.xlsx has been downloaded successfully.`
      );
      setAlertType("success");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };
    const hError = () => {
      setBlurLoading(false);
      setLoaderMessage("");
      showToast(ERROR_MESSAGES?.ERR_DOWNLOADING_EXCEL, "error", {
        position: "top-center",
      });
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/excel/downloadExcelWithData`,
      "postandgetblob",
      hSuccess,
      hError,
      payload
    );
  };
  const handleEmailDownload = () => {
    setBlurLoading(true);
    onClose();
    let templateKeys =
      Templates[initialPayload?.TemplateName]?.map((item) => item.key) || [];
    let payload = {};
    if (activeTab === 0) {
      payload = {
        materialDetails: [
          templateKeys.reduce((acc, key) => {
            acc[key] = convertedValues?.[key] ? convertedValues?.[key] : "";
            return acc;
          }, {}),
        ],
        templateHeaders: initialPayload?.FieldName
          ? initialPayload.FieldName?.join("$^$")
          : "",
        requestId: RequestId || initialPayload?.RequestId || "",
        templateName: initialPayload?.TemplateName
          ? initialPayload.TemplateName
          : "",
        dtName: "MDG_MAT_CHANGE_TEMPLATE",
        version: "v4",
        rolePrefix: "",
      };
    } else {
      payload = {
        materialDetails: [
          templateKeys.reduce((acc, key) => {
            acc[key] =
              rowsOfMaterialData
                .map((row) => row[key]?.trim())
                .filter((value) => value !== "")
                .join(",") || "";
            return acc;
          }, {}),
        ],
        templateHeaders: initialPayload?.FieldName
          ? initialPayload.FieldName?.join("$^$")
          : "",
        requestId: RequestId || initialPayload?.RequestId || "",
        templateName: initialPayload?.TemplateName
          ? initialPayload.TemplateName
          : "",
        dtName: "MDG_MAT_CHANGE_TEMPLATE",
        version: "v4",
        rolePrefix: "",
      };
    }
    const hSuccess = () => {
      setBlurLoading(false);
      setLoaderMessage("");
      setSuccessMsg(true);
      setMessageDialogMessage(
        `Download has been started. You will get the Excel file via email.`
      );
      setAlertType("success");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };
    const hError = () => {
      setBlurLoading(false);
      setSuccessMsg(true);
      setMessageDialogMessage(
        "Oops! Something went wrong. Please try again later."
      );
      setAlertType("danger");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };
    doAjax(
      `/${destination_MaterialMgmt}/excel/downloadExcelWithDataInMail`,
      "postandgetblob",
      hSuccess,
      hError,
      payload
    );
  };
  const highlightedColumns = columns.map((col) => ({
    ...col,
    renderCell: (params) => {
      const isChanged = changedFieldsMap[params.row.CostCenterID]?.[col.field];
      return (
        <div
          style={{
            backgroundColor: isChanged ? "rgba(255, 229, 100, 0.6)" : "inherit",
            padding: "0 4px",
            borderRadius: 4,
            height: "100%",
            display: "flex",
            alignItems: "center",
          }}
        >
          {params.value}
        </div>
      );
    },
  }));
  const isChangeFieldEmpty = (changedFieldsMap) =>
    changedFieldsMap &&
    Object.values(changedFieldsMap).every(
      (fields) =>
        typeof fields === "object" &&
        fields !== null &&
        Object.keys(fields).length === 0
    );
  return (
    <div>
      {successMsg && (
        <ReusableSnackBar
          openSnackBar={openSnackbar}
          alertMsg={messageDialogMessage}
          alertType={alertType}
          handleSnackBarClose={handleSnackBarClose}
        />
      )}
      {(requestHeaderData?.TemplateName || downloadClicked) && (
        <>
          {fetchedCostCenterData.length === 0 && !reqBench && (
            <>
              <Dialog
                open={open}
                TransitionComponent={Transition}
                keepMounted
                onClose={(event, reason) => {
                  if (
                    reason === "backdropClick" ||
                    reason === "escapeKeyDown"
                  ) {
                    return;
                  }
                  handleClose();
                }}
                maxWidth="sm"
                fullWidth
              >
                <Box
                  sx={{
                    backgroundColor: "#e3f2fd",
                    padding: "1rem 1.5rem",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <FeedOutlinedIcon
                    color="primary"
                    sx={{ marginRight: "0.5rem" }}
                  />
                  <Typography variant="h6" component="div" color="primary">
                    {/* {requestHeaderData?.TemplateName} Search Filter(s) */}
                    Select Cost Center for Change
                  </Typography>
                </Box>
                <DialogContent sx={{ padding: "1.5rem 1.5rem 1rem" }}>
                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{
                        key: "controllingArea",
                        label: "Controlling Area",
                      }}
                      dropDownData={{
                        controllingArea: [{ code: "ETCA", desc: "ETCA" }],
                      }}
                      selectedValues={{
                        controllingArea: selectedControllingArea
                          ? [{ code: selectedControllingArea }]
                          : [],
                      }}
                      handleSelectionChange={(key, value) => {
                        setSelectedControllingArea(
                          value.length > 0 ? value[0].code || value[0] : ""
                        );
                      }}
                      formatOptionLabel={(option) => {
                        if (option.code && option.desc) {
                          return `${option.code} - ${option.desc}`;
                        }
                        return option.code || "";
                      }}
                      singleSelect={true}
                      errors={{}}
                    />
                  </Box>
                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{ key: "companyCode", label: "Company Code" }}
                      dropDownData={{ companyCode: dropdownDataCompany || [] }}
                      selectedValues={{
                        companyCode: selectedCompanyCodes.map(
                          (code) =>
                            dropdownDataCompany?.find(
                              (item) => item.code === code
                            ) || { code }
                        ),
                      }}
                      handleSelectAll={(key) => {
                        if (
                          selectedCompanyCodes.length ===
                          dropdownDataCompany?.length
                        ) {
                          setSelectedCompanyCodes([]);
                        } else {
                          setSelectedCompanyCodes(
                            dropdownDataCompany?.map((item) => item.code) || []
                          );
                        }
                      }}
                      handleSelectionChange={(key, value) => {
                        setSelectedCompanyCodes(
                          value.map((item) =>
                            typeof item === "string" ? item : item.code || item
                          )
                        );
                      }}
                      formatOptionLabel={(option) => {
                        if (option.code && option.desc) {
                          return `${option.code} - ${option.desc}`;
                        }
                        return option.code || "";
                      }}
                      isSelectAll={true}
                      errors={{}}
                    />
                  </Box>
                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{ key: "costCenter", label: "Cost Center" }}
                      dropDownData={{
                        costCenter:
                          costCenterOptions.map((code) => ({ code })) || [],
                      }}
                      selectedValues={{
                        costCenter:
                          selectedCostCenters.map((code) => ({ code })) || [],
                      }}
                      handleSelectAll={(key) => {
                        if (
                          selectedCostCenters.length ===
                          costCenterOptions.length
                        ) {
                          setSelectedCostCenters([]);
                        } else {
                          setSelectedCostCenters(costCenterOptions || []);
                        }
                      }}
                      handleSelectionChange={(key, value) => {
                        setSelectedCostCenters(
                          value.map((item) =>
                            typeof item === "string" ? item : item.code || item
                          )
                        );
                      }}
                      formatOptionLabel={(option) =>
                        typeof option === "string"
                          ? option
                          : option.code || option
                      }
                      isSelectAll={true}
                      errors={{}}
                    />
                  </Box>
                </DialogContent>
                <DialogActions
                  sx={{
                    padding: "0.5rem 1.5rem",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <Box sx={{ display: "flex", gap: 1 }}>
                    <Button
                      onClick={handleClose}
                      color="error"
                      variant="outlined"
                      sx={{
                        height: 36,
                        minWidth: "3.5rem",
                        textTransform: "none",
                        borderColor: "#cc3300",
                        fontWeight: 500,
                      }}
                    >
                      Cancel
                    </Button>
                    {requestHeaderData?.RequestType !==
                      REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
                      <Button
                        onClick={() => {
                          handleOk("OK");
                        }}
                        variant="contained"
                        sx={{
                          height: 36,
                          minWidth: "3.5rem",
                          backgroundColor: "#3B30C8",
                          textTransform: "none",
                          fontWeight: 500,
                          "&:hover": {
                            backgroundColor: "#2c278f",
                          },
                        }}
                      >
                        OK
                      </Button>
                    )}
                    {requestHeaderData?.RequestType ===
                      REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
                      <Button
                        onClick={() => {
                          handleOk("Download");
                        }}
                        variant="contained"
                        sx={{
                          height: 36,
                          minWidth: "3.5rem",
                          backgroundColor: "#3B30C8",
                          textTransform: "none",
                          fontWeight: 500,
                          "&:hover": {
                            backgroundColor: "#2c278f",
                          },
                        }}
                      >
                        Download
                      </Button>
                    )}
                  </Box>
                </DialogActions>
              </Dialog>
              <DownloadDialog
                onDownloadTypeChange={onDownloadTypeChange}
                open={openDownloadDialog}
                downloadType={downloadType}
                handleDownloadTypeChange={handleDownloadTypeChange}
                onClose={handleDownloadDialogClose}
              />
              <ReusableBackDrop
                blurLoading={blurLoading}
                loaderMessage={loaderMessage}
              />
            </>
          )}
          {showGrid && (
            <Box sx={{ mt: 4, px: 4 }}>
              <Typography variant="h5" sx={{ fontWeight: 600, mb: 2 }}>
                Cost Center List
              </Typography>
              <Paper
                elevation={3}
                sx={{
                  borderRadius: 3,
                  overflow: "hidden",
                  border: "1px solid #e0e0e0",
                  backgroundColor: "#fafbff",
                }}
              >
                <Box sx={{ p: 2 }}>
                  <ReusableDataTable
                    rows={fetchedCostCenterData}
                    columns={columns}
                    pageSize={10}
                    tempheight="50vh"
                    getRowIdValue="id"
                    editMode="row"
                    status_onRowSingleClick
                    callback_onRowSingleClick={handleRowClick}
                    processRowUpdate={processRowUpdate}
                    experimentalFeatures={{ newEditingApi: true }}
                    isCellEditable={(params) =>
                      !["costCenter", "companyCode"].includes(params.field)
                    }
                    getRowClassName={(params) =>
                      selectedRow?.id === params.row.id ? "Mui-selected" : ""
                    }
                  />
                </Box>
              </Paper>
              <Box
                sx={{ display: "flex", justifyContent: "right", mt: 3, gap: 2 }}
              >
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleSaveAsDraft}
                >
                  Save as draft
                </Button>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={validateAllRows}
                >
                  Validate
                </Button>
                <Button
                  variant="contained"
                  color="secondary"
                  onClick={handleSubmitForReview}
                  disabled={!isButtonEnabled}
                >
                  Submit
                </Button>
              </Box>
            </Box>
          )}
        </>
      )}
      {showTableInDialog && (
        <ObjectLockDialog
          duplicateFieldsArr={duplicateFieldsData}
          moduleName={MODULE_MAP?.["CC"]}
          open={showTableInDialog}
          onClose={() => setShowTableInDialog(false)}
        />
      )}
      <>
        {fetchReqBenchDataCC.length === 0 && reqBench === "true" && (
          <>
            <Dialog
              open={open}
              TransitionComponent={Transition}
              keepMounted
              onClose={(event, reason) => {
                if (reason === "backdropClick" || reason === "escapeKeyDown") {
                  return;
                }
                handleClose();
              }}
              maxWidth="sm"
              fullWidth
            >
              <Box
                sx={{
                  backgroundColor: "#e3f2fd",
                  padding: "1rem 1.5rem",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <FeedOutlinedIcon
                  color="primary"
                  sx={{ marginRight: "0.5rem" }}
                />
                <Typography variant="h6" component="div" color="primary">
                  {requestHeaderData?.TemplateName} Search Filter(s)
                </Typography>
              </Box>

              <DialogContent sx={{ padding: "1.5rem 1.5rem 1rem" }}>
                <Box sx={{ marginBottom: "1rem" }}>
                  <FilterChangeDropdown
                    param={{
                      key: "controllingArea",
                      label: "Controlling Area",
                    }}
                    dropDownData={{
                      controllingArea: [{ code: "ETCA", desc: "ETCA" }],
                    }}
                    selectedValues={{
                      controllingArea: selectedControllingArea
                        ? [{ code: selectedControllingArea }]
                        : [],
                    }}
                    handleSelectionChange={(key, value) => {
                      setSelectedControllingArea(
                        value.length > 0 ? value[0].code || value[0] : ""
                      );
                    }}
                    formatOptionLabel={(option) => {
                      if (option.code && option.desc) {
                        return `${option.code} - ${option.desc}`;
                      }
                      return option.code || "";
                    }}
                    singleSelect={true}
                    errors={{}}
                  />
                </Box>
                <Box sx={{ marginBottom: "1rem" }}>
                  <FilterChangeDropdown
                    param={{ key: "companyCode", label: "Company Code" }}
                    dropDownData={{ companyCode: dropdownDataCompany || [] }}
                    selectedValues={{
                      companyCode: selectedCompanyCodes.map(
                        (code) =>
                          dropdownDataCompany?.find(
                            (item) => item.code === code
                          ) || { code }
                      ),
                    }}
                    handleSelectAll={(key) => {
                      if (
                        selectedCompanyCodes.length ===
                        dropdownDataCompany?.length
                      ) {
                        setSelectedCompanyCodes([]);
                      } else {
                        setSelectedCompanyCodes(
                          dropdownDataCompany?.map((item) => item.code) || []
                        );
                      }
                    }}
                    handleSelectionChange={(key, value) => {
                      setSelectedCompanyCodes(
                        value.map((item) =>
                          typeof item === "string" ? item : item.code || item
                        )
                      );
                    }}
                    formatOptionLabel={(option) => {
                      if (option.code && option.desc) {
                        return `${option.code} - ${option.desc}`;
                      }
                      return option.code || "";
                    }}
                    isSelectAll={true}
                    errors={{}}
                  />
                </Box>
                <Box sx={{ marginBottom: "1rem" }}>
                  <FilterChangeDropdown
                    param={{ key: "costCenter", label: "Cost Center" }}
                    dropDownData={{
                      costCenter:
                        costCenterOptions.map((code) => ({ code })) || [],
                    }}
                    selectedValues={{
                      costCenter:
                        selectedCostCenters.map((code) => ({ code })) || [],
                    }}
                    handleSelectAll={(key) => {
                      if (
                        selectedCostCenters.length === costCenterOptions.length
                      ) {
                        setSelectedCostCenters([]);
                      } else {
                        setSelectedCostCenters(costCenterOptions || []);
                      }
                    }}
                    handleSelectionChange={(key, value) => {
                      setSelectedCostCenters(
                        value.map((item) =>
                          typeof item === "string" ? item : item.code || item
                        )
                      );
                    }}
                    formatOptionLabel={(option) =>
                      typeof option === "string"
                        ? option
                        : option.code || option
                    }
                    isSelectAll={true}
                    errors={{}}
                  />
                </Box>
              </DialogContent>
              <DialogActions
                sx={{
                  padding: "0.5rem 1.5rem",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <Box sx={{ display: "flex", gap: 1 }}>
                  <Button
                    onClick={handleClose}
                    color="error"
                    variant="outlined"
                    sx={{
                      height: 36,
                      minWidth: "3.5rem",
                      textTransform: "none",
                      borderColor: "#cc3300",
                      fontWeight: 500,
                    }}
                  >
                    Cancel
                  </Button>
                  {requestHeaderData?.RequestType !==
                    REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
                    <Button
                      onClick={handleOk}
                      variant="contained"
                      sx={{
                        height: 36,
                        minWidth: "3.5rem",
                        backgroundColor: "#3B30C8",
                        textTransform: "none",
                        fontWeight: 500,
                        "&:hover": {
                          backgroundColor: "#2c278f",
                        },
                      }}
                    >
                      OK
                    </Button>
                  )}
                  {requestHeaderData?.RequestType ===
                    REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
                    <Button
                      onClick={() => {
                        handleDownloadDialogOpen();
                      }}
                      variant="contained"
                      sx={{
                        height: 36,
                        minWidth: "3.5rem",
                        backgroundColor: "#3B30C8",
                        textTransform: "none",
                        fontWeight: 500,
                        "&:hover": {
                          backgroundColor: "#2c278f",
                        },
                      }}
                    >
                      Download
                    </Button>
                  )}
                </Box>
              </DialogActions>
            </Dialog>
            <DownloadDialog
              onDownloadTypeChange={onDownloadTypeChange}
              open={openDownloadDialog}
              downloadType={downloadType}
              handleDownloadTypeChange={handleDownloadTypeChange}
              onClose={handleDownloadDialogClose}
            />
            <ReusableBackDrop
              blurLoading={blurLoading}
              loaderMessage={loaderMessage}
            />
          </>
        )}
        {reqBench === "true" && (
          <Box sx={{ marginTop: "20px", padding: "16px" }}>
            <Typography variant="h5" gutterBottom>
              Cost Center Lists
            </Typography>
            <Paper
              elevation={4}
              sx={{ p: 0, borderRadius: 2, overflow: "hidden", mt: "50px" }}
            >
              <div>
                <ReusableDataTable
                  rows={fetchReqBenchDataCC}
                  columns={columns}
                  pageSize={10}
                  tempheight={"50vh"}
                  getRowIdValue={"id"}
                  status_onRowSingleClick={true}
                  editMode="cell"
                  callback_onRowSingleClick={handleRowClick}
                  processRowUpdate={processRowUpdateReqBench}
                  experimentalFeatures={{ newEditingApi: true }}
                  isCellEditable={(params) =>
                    !["costCenter", "companyCode"].includes(params.field)
                  }
                  getRowClassName={(params) =>
                    selectedRow?.id === params.row.id ? "Mui-selected" : ""
                  }
                />
              </div>
            </Paper>
            <Box
              sx={{ display: "flex", justifyContent: "right", mt: 3, gap: 2 }}
            >
              <BottomNavGlobal
                handleSaveAsDraft={handleSaveAsDraft}
                handleSubmitForReview={handleSubmitForReview}
                handleSubmitForApprove={handleSubmitForApprove}
                handleSendBack={handleSendBack}
                handleRejectAndCancel={handleRejectAndCancel}
                handleValidateAndSyndicate={handleValidateAndSyndicate}
                validateAllRows={validateAllRows}
                filteredButtons={filteredButtons}
              />
            </Box>
          </Box>
        )}
      </>
    </div>
  );
};
export default RequestDetailsChangeCC;
