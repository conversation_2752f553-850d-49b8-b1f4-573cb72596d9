import{r as h,q as x,s as N,u as O,bI as $,a as e,x as b,j as i,T as n,b6 as H,E as W,at as q,au as U,ay as Z,F as E,G as r,aZ as J,b as K,al as Q,I as X,b1 as Y,t as I,b8 as G,B as C,v as ee,w as te,b4 as ae,br as ie,bU as le,aE as oe,aD as re}from"./index-17b8d91e.js";import{d as ne}from"./ArrowCircleLeftOutlined-2a09f8e2.js";import{d as de}from"./EditOutlined-36c8ca4d.js";import{D as se}from"./DatePicker-68227989.js";import"./dateViewRenderers-34586552.js";import"./useSlotProps-e34e1e13.js";import"./InputAdornment-5b0053c5.js";import"./CSSTransition-30917e2c.js";import"./useMediaQuery-6a073ac5.js";import"./DesktopDatePicker-07c19cde.js";import"./useMobilePicker-9978caff.js";function ce(d,s){return Array.isArray(s)&&s.find(u=>u.code===d)||""}const pe=({label:d,value:s,fieldGroup:g,units:u,onSave:D,isEditMode:S,isExtendMode:F,selectedRowData:k,options:T=[],type:c})=>{var z;const[o,f]=h.useState(s),[v,l]=h.useState(!1),p=x(t=>t.AllDropDown.dropDown),w=N(),P=ce(o,p);console.log("dropdownData",o),console.log("value e",s),console.log("label",d),console.log("units",u),console.log("transformedValue",P),O();const M=x(t=>t.initialData.MultipleMaterial),_=x(t=>t.edit.payload);x(t=>t.initialData.MultipleMaterial[0].Description);let j=-1;for(let t=0;t<M.length;t++)if(M[t].Description===k){M[t],j=t;break}console.log("editField",_),console.log("fieldData",{label:d,value:o,units:u,type:c});let y=d.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("");h.useEffect(()=>{f(s)},[s]);const A=(t,a)=>{w(J(M.map((m,L)=>{if(L==j){let R=m["Basic Data"],V=m["Basic Data"][g];return{...m,"Basic Data":{...R,[g]:V.map(B=>B.fieldName===t?{...B,value:a}:B)}}}else return m})))};return h.useEffect(()=>{console.log("lkey",y),console.log("data",s),w($({keyname:y.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""),data:s||""}))},[]),console.log("editedValue[key] ",p[y]),console.log("editedValue[key] ",o),e(r,{item:!0,children:e(b,{children:S||F?i(E,{children:[e(n,{variant:"body2",color:"#777",children:d}),c==="Drop Down"?e(H,{options:p[y]??[],value:o&&((z=p[y])==null?void 0:z.filter(t=>t.code===o))||"",onChange:(t,a)=>{A(d,a.code),console.log("newValue",a),f(a.code),l(!0),console.log("keys",y)},getOptionLabel:t=>{var a,m;return console.log("optionoptionoption",t),t===""?"":`${t&&((a=t[0])==null?void 0:a.code)} - ${t&&((m=t[0])==null?void 0:m.desc)}`},renderOption:(t,a)=>(console.log("option vakue",a),e("li",{...t,children:e(n,{style:{fontSize:12},children:`${a==null?void 0:a.code} - ${a==null?void 0:a.desc}`})})),renderInput:t=>e(W,{...t,variant:"outlined",size:"small",label:null})}):c==="Input"?e(W,{variant:"outlined",size:"small",value:o,onChange:t=>{const a=t.target.value;A(d,a),f(a)}}):c==="Calendar"?e(q,{dateAdapter:U,children:e(se,{slotProps:{textField:{size:"small"}},placeholder:"Select Date Range"})}):c==="Radio Button"?e(Z,{sx:{borderRadius:"0 !important"},checked:o,onChange:(t,a)=>{A(d,a),f(a)}}):""]}):e(E,{children:i(E,{children:[e(n,{variant:"body2",color:"#777",children:d}),i(n,{variant:"body2",fontWeight:"bold",children:[o," ",u]})]})})})})},Ce=()=>{const d=K(),s=N();h.useState({});const[g,u]=h.useState(0),[D,S]=h.useState(!1),[F,k]=h.useState(!0),T=O();h.useState(!1),x(l=>l.initialData.EditMultipleMaterial);const c=x(l=>l.initialData.MultipleMaterial),o=T.state;x(l=>l.payload);const f=()=>{S(!0),le(),k(!1)};for(let l=0;l<c.length;l++)if(c[l].Description===o.description){c[l];break}const v=c.filter(l=>l.Description===o.description)[0]["Basic Data"];return console.log(v,"lololol"),i("div",{children:[e(r,{container:!0,style:{...Q,backgroundColor:"#FAFCFF"},children:i(r,{sx:{width:"inherit"},children:[i(r,{item:!0,md:12,style:{padding:"16px",display:"flex"},children:[e(r,{item:!0,style:{display:"flex",justifyContent:"flex-end"},children:e(X,{color:"primary","aria-label":"upload picture",component:"label",sx:Y,children:e(ne,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{setTimeout(()=>{d(-1)},1e3),s(clearPayload()),s(clearOrgData())}})})}),i(r,{md:8,children:[e(n,{variant:"h3",children:i("strong",{children:["Multiple Material : ",o.description," "]})}),e(n,{variant:"body2",color:"#777",children:"This view displays details of uploaded material"})]}),D?"":e(r,{md:4,sx:{display:"flex",justifyContent:"flex-end"},children:e(r,{item:!0,children:i(I,{variant:"outlined",size:"small",sx:G,onClick:f,children:["Change",e(de,{sx:{padding:"2px"},fontSize:"small"})]})})})]}),i(r,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:[i(C,{width:"70%",sx:{marginLeft:"40px"},children:[e(r,{item:!0,sx:{paddingTop:"2px !important"},children:i(b,{flexDirection:"row",children:[e("div",{style:{width:"15%"},children:e(n,{variant:"body2",color:"#777",children:"Material"})}),i(n,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[": ",o.material]})]})}),e(r,{item:!0,sx:{paddingTop:"2px !important"},children:i(b,{flexDirection:"row",children:[e("div",{style:{width:"15%"},children:e(n,{variant:"body2",color:"#777",children:"Material Type"})}),i(n,{variant:"body2",fontWeight:"bold",children:[": ",o.materialType]})]})}),e(r,{item:!0,sx:{paddingTop:"2px !important"},children:i(b,{flexDirection:"row",children:[e("div",{style:{width:"15%"},children:e(n,{variant:"body2",color:"#777",children:"Description"})}),i(n,{variant:"body2",fontWeight:"bold",children:[": ",o.description]})]})}),e(r,{item:!0,sx:{paddingTop:"2px !important"},children:i(b,{flexDirection:"row",children:[e("div",{style:{width:"15%"},children:e(n,{variant:"body2",color:"#777",children:"Industry Sector"})}),i(n,{variant:"body2",fontWeight:"bold",children:[": ",o.industrySector]})]})})]}),e(C,{width:"30%",sx:{marginLeft:"40px"},children:e(r,{item:!0,children:i(b,{flexDirection:"row",children:[e(n,{variant:"body2",color:"#777",style:{width:"30%"}}),e(n,{variant:"body2",fontWeight:"bold",sx:{width:"8%",textAlign:"center"}}),e(n,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start"})]})})})]}),i(r,{container:!0,style:{padding:"16px"},children:[e(C,{sx:{borderBottom:1,borderColor:"divider"},children:e(ee,{value:g,variant:"scrollable",sx:{background:"#FFF",borderBottom:"1px solid #BDBDBD",width:"100%"},"aria-label":"mui tabs example",children:e(te,{sx:{fontSize:"12px",fontWeight:"700"},label:"Basic Data"},0)})}),e(r,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(v).map(l=>i(r,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ae},children:[e(n,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:l}),e(C,{sx:{width:"100%"},children:e(ie,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:e(r,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:v[l].map(p=>e(pe,{fieldGroup:l,selectedRowData:o.description,label:p.fieldName,value:p.value,onSave:w=>handleFieldSave(p.fieldName,w),isEditMode:D,type:p.fieldType,field:p}))})})})]},l))},v)]})]})}),D?e(re,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:e(oe,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:g,onChange:l=>{u(l)},children:e(I,{size:"small",variant:"contained",onClick:()=>{d("/masterDataCockpit/materialMaster/massMaterialTable")},children:"Save"})})}):""]})};export{Ce as default};
