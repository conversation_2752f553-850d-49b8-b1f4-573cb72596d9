import{b as Se,r as s,j as y,a as e,b3 as ye,am as Ce,G as E,g as G,A as Y,T as O,f as Z,an as xe,aE as ke,t as Fe,aD as ue,K as J,eK as Q,ck as De,F as fe,aj as Ne,ak as Te,al as je,bY as Ae,bZ as qe,h as me,I as be,b1 as pe,b_ as Ie,cl as We,v as He,w as Ke,B as we,cm as Le,z as Ve}from"./index-75c1660a.js";import{R as ve}from"./ReusableFieldCatalog-2b30c987.js";const _e=({})=>{const u=Se();s.useState("");const[C,d]=s.useState(null),[F,D]=s.useState([]),[f,B]=s.useState({}),[Me,X]=s.useState(null),[N,T]=s.useState({}),[ee,se]=s.useState({}),[x,j]=s.useState({}),[te,A]=s.useState(!1),[ae,ie]=s.useState(!1),[oe,q]=s.useState(!1),[I,W]=s.useState(""),[le,H]=s.useState(!1),[Ee,K]=s.useState(!1),[Be,w]=s.useState(!0),[ne,L]=s.useState(!1),[Re,R]=s.useState(!1),V=()=>{u("/masterDataCockpit/materialMaster/createMaterialDetail")},ce=()=>{u("/masterDataCockpit/materialMaster/createMaterialDetail")},de=()=>{setOpen(!1)},$=()=>{A(!1),u("/masterDataCockpit/materialMaster/materialSingle")},re=()=>{A(!0)},P=()=>{ie(!0)},ge=()=>{const r=b=>{const n=[],v=[];Object.keys(b.body).map(o=>{const t=b.body[o];Object.keys(t).map(p=>{const c=b.body[o][p];if(Array.isArray(c)){let M={heading:p,fields:c.map(l=>l.fieldName),viewName:o,fieldVisibility:c.map(l=>({fieldName:l.fieldName,visibility:l.visibility}))};n.push(M),console.log(n,"hello"),c.forEach(l=>{console.log("Field Name:",l.fieldName),console.log("Is Required:",l.Required),l.Required==="true"&&v.push(l.fieldName)})}})}),D(n),console.log("Required Fields:",v);const k={},a={},i={};n.forEach(o=>{const{heading:t,fields:p,viewName:c,fieldVisibility:M}=o;k[c]||(k[c]={heading:c,subheadings:[]}),k[c].subheadings.push({heading:t,fields:p}),M.forEach(l=>{let h=l.visibility==="Required"?"Mandatory":l.visibility==="Hidden"?"Hide":l.visibility==="0"?"0":"Optional";a[l.fieldName]=h,l.visibility==="0"&&(i[l.fieldName]=!0)})}),B(k),j(a),se(i),T(i),console.log(k,"Fieldset")},m=b=>{console.log(b)};J(`/${Q}/data/getFieldCatalogueDetails?screenName=Change`,"get",r,m)};s.useEffect(()=>{ge()},[]);const U=()=>{console.log("helloooo");let r={};Object.keys(f).forEach(n=>{f[n].subheadings.forEach(k=>{const{heading:a,fields:i}=k;i.forEach(o=>{if(x[o]!=="0"&&N[o]){const t=x[o]==="Mandatory"?"Required":x[o]==="Hide"?"Hidden":"Optional";r[n]||(r[n]=[]),r[n].some(c=>c.fieldName===o)||r[n].push({fieldName:o,cardName:a,viewName:n,visibility:t,screenName:"Change"})}})})});const m=n=>{console.log(n,"example"),R(),n.statusCode===200?(console.log("success"),q("Submit"),W("Field Catalog has been submitted successfully"),H("success"),w(!1),L(!0),re(),K(!0),R(!1)):(q("Submit"),L(!1),W("Submission Failed"),H("danger"),w(!1),K(!0),P(),R(!1)),de()},b=n=>{console.log(n)};Object.keys(r).forEach(n=>{const v=r[n];v.length>0?J(`/${Q}/alter/changeVisibility`,"post",m,b,v):console.log(`No payload data to send for viewName: ${n}`)}),dispatch(De())};return y("div",{children:[e(ye,{dialogState:ae,openReusableDialog:P,closeReusableDialog:V,dialogTitle:oe,dialogMessage:I,handleDialogConfirm:V,dialogOkText:"OK",handleExtraButton:ce,dialogSeverity:le}),ne&&e(ReusableSnackBar,{openSnackBar:te,alertMsg:I,handleSnackBarClose:$}),e(E,{container:!0,sx:Ce,children:e(E,{item:!0,md:12,children:Object.keys(f).map(r=>y(G,{sx:{mb:2},className:"filter-accordion",children:[e(Y,{sx:{backgroundColor:"#f5f5f5"},children:e(O,{sx:{fontWeight:"700",margin:"0px !important"},children:r})}),e(Z,{children:f[r].subheadings.map((m,b)=>y(G,{sx:{mb:2},children:[e(Y,{expandIcon:e(xe,{}),sx:{backgroundColor:"#F1F0FF"},children:e(O,{sx:{fontWeight:"700",margin:"0px !important",fontSize:"14px"},children:m.heading})}),e(Z,{children:e("div",{sx:{fontSize:"25px"},children:e(ve,{fields:m.fields,heading:m.heading,childCheckedStates:N,setChildCheckedStates:T,childRadioValues:x,setChildRadioValues:j,onSubmitButtonClick:()=>U(),mandatoryFields:F,DisabledChildCheck:ee})})})]},b))})]},r))})}),e(ue,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:e(ke,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},value:C,onChange:(r,m)=>{X(F[m]),d(m)},children:e(Fe,{size:"small",variant:"contained",onClick:U,children:"Submit"})})})]})},ze=()=>{const u=document.getElementsByTagName("HTML")[0],C=document.getElementsByTagName("BODY")[0];let d=u.clientWidth,F=C.clientWidth;const D=document.getElementById("e-invoice-export"),f=D.scrollWidth-D.clientWidth;f>D.clientWidth&&(d+=f,F+=f),u.style.width=d+"px",C.style.width=F+"px",Le(D).then(B=>B.toDataURL("image/png",1)).then(B=>{$e(B,"FieldCatalog.png"),u.style.width=null,C.style.width=null})},$e=(u,C)=>{const d=window.document.createElement("a");d.href=u,d.download=C,(document.body||document.documentElement).appendChild(d),typeof d.click=="function"?d.click():(d.target="_blank",d.dispatchEvent(new MouseEvent("click",{view:window,bubbles:!0,cancelable:!0}))),URL.revokeObjectURL(d.href),d.remove()},Ge=({})=>{const u=Se();s.useState("");const[C,d]=s.useState(null),[F,D]=s.useState([]),[f,B]=s.useState({}),[Me,X]=s.useState(null),[N,T]=s.useState({}),[ee,se]=s.useState({}),[x,j]=s.useState({}),[te,A]=s.useState(!1),[ae,ie]=s.useState(!1),[oe,q]=s.useState(!1),[I,W]=s.useState(""),[le,H]=s.useState(!1),[Ee,K]=s.useState(!1),[Be,w]=s.useState(!0),[ne,L]=s.useState(!1),[Re,R]=s.useState(!1),[V,ce]=s.useState(0),de=["For Create","For Change"],$=()=>{u("/masterDataCockpit/materialMaster/createMaterialDetail")},re=()=>{u("/masterDataCockpit/materialMaster/createMaterialDetail")},P=()=>{setOpen(!1)},ge=()=>{A(!1),u("/masterDataCockpit/costCenter")},U=()=>{A(!0)},r=()=>{ie(!0)},m=()=>{const a=o=>{const t=[],p=[];Object.keys(o.body).map(h=>{const _=o.body[h];Object.keys(_).map(z=>{const S=o.body[h][z];if(Array.isArray(S)){let he={heading:z,fields:S.map(g=>g.fieldName),viewName:h,fieldVisibility:S.map(g=>({fieldName:g.fieldName,visibility:g.visibility}))};t.push(he),console.log(t,"hello"),S.forEach(g=>{console.log("Field Name:",g.fieldName),console.log("Is Required:",g.Required),g.Required==="true"&&p.push(g.fieldName)})}})}),D(t),console.log("Required Fields:",p);const c={},M={},l={};t.forEach(h=>{const{heading:_,fields:z,viewName:S,fieldVisibility:he}=h;c[S]||(c[S]={heading:S,subheadings:[]}),c[S].subheadings.push({heading:_,fields:z}),he.forEach(g=>{let Oe=g.visibility==="Required"?"Mandatory":g.visibility==="Hidden"?"Hide":g.visibility==="0"?"0":"Optional";M[g.fieldName]=Oe,g.visibility==="0"&&(l[g.fieldName]=!0)})}),B(c),j(M),se(l),T(l),console.log(c,"Fieldset")},i=o=>{console.log(o)};J(`/${Q}/data/getFieldCatalogueDetails?screenName=Create`,"get",a,i)};s.useEffect(()=>{m()},[]);const b=()=>{let a={};Object.keys(f).forEach(t=>{f[t].subheadings.forEach(c=>{const{heading:M,fields:l}=c;l.forEach(h=>{if(x[h]!=="0"&&N[h]){const _=x[h]==="Mandatory"?"Required":x[h]==="Hide"?"Hidden":"Optional";a[t]||(a[t]=[]),a[t].some(S=>S.fieldName===h)||a[t].push({fieldName:h,cardName:M,viewName:t,visibility:_,screenName:"Create"})}})})});const i=t=>{console.log(t,"example"),R(),t.statusCode===200?(console.log("success"),q("Submit"),W("Field Catalog has been submitted successfully"),H("success"),w(!1),L(!0),U(),K(!0),R(!1)):(q("Submit"),L(!1),W("Submission Failed"),H("danger"),w(!1),K(!0),r(),R(!1)),P()},o=t=>{console.log(t)};Object.keys(a).forEach(t=>{const p=a[t];p.length>0?J(`/${Q}/alter/changeVisibility`,"post",i,o,p):console.log(`No payload data to send for viewName: ${t}`)}),dispatch(De())},n=[[e(fe,{children:e(E,{container:!0,sx:Ce,children:e(E,{item:!0,md:12,children:Object.keys(f).map(a=>y(G,{sx:{mb:2},className:"filter-accordion",children:[e(Y,{sx:{backgroundColor:"#f5f5f5"},children:e(O,{sx:{fontWeight:"700",margin:"0px !important"},children:a})}),e(Z,{children:f[a].subheadings.map((i,o)=>y(G,{sx:{mb:2},children:[e(Y,{expandIcon:e(xe,{}),sx:{backgroundColor:"#F1F0FF"},children:e(O,{sx:{fontWeight:"700",margin:"0px !important",fontSize:"14px"},children:i.heading})}),e(Z,{children:e("div",{sx:{fontSize:"25px"},children:e(ve,{fields:i.fields,heading:i.heading,childCheckedStates:N,setChildCheckedStates:T,childRadioValues:x,setChildRadioValues:j,onSubmitButtonClick:()=>b(),mandatoryFields:F,DisabledChildCheck:ee})})})]},o))})]},a))})})})],[e(fe,{children:e(_e,{})})]],v=(a,i)=>{ce(i)};s.useState(""),s.useState([]);function k(){}return y("div",{children:[e(ye,{dialogState:ae,openReusableDialog:r,closeReusableDialog:$,dialogTitle:oe,dialogMessage:I,handleDialogConfirm:$,dialogOkText:"OK",handleExtraButton:re,dialogSeverity:le}),ne&&e(Ve,{openSnackBar:te,alertMsg:I,handleSnackBarClose:ge}),e("div",{style:{...Ne,backgroundColor:"#FAFCFF"},children:y(Te,{spacing:1,children:[y(E,{container:!0,sx:je,children:[y(E,{item:!0,md:5,sx:Ae,children:[e(O,{variant:"h3",children:e("strong",{children:"Field Configurations"})}),e(O,{variant:"body2",color:"#777",children:"This view displays the setiings for configuring the Fields"})]}),e(E,{item:!0,md:7,sx:{display:"flex"},children:y(E,{container:!0,direction:"row",justifyContent:"flex-end",alignItems:"center",spacing:0,children:[e(qe,{title:"Search for fields in different views",module:"FieldSelection",keyName:"string",message:"Search for fields in different views"}),e(me,{title:"Reload",children:e(be,{sx:pe,children:e(Ie,{sx:{"&:hover":{transform:"rotate(360deg)",transition:"0.9s"}},onClick:k})})}),e(me,{title:"Export",children:e(be,{sx:pe,children:e(We,{onClick:ze})})})]})})]}),e(ue,{children:e(He,{value:V,onChange:v,variant:"scrollable",sx:{background:"#FFF",borderBottom:"1px solid #BDBDBD",width:"100%"},"aria-label":"mui tabs example",children:de.map((a,i)=>e(Ke,{sx:{fontSize:"12px",fontWeight:"700"},label:a},i))})}),n[V].map((a,i)=>e(we,{children:a},i)),e(ue,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:e(ke,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},value:C,onChange:(a,i)=>{X(F[i]),d(i)},children:e(Fe,{size:"small",variant:"contained",onClick:b,children:"Submit"})})})]})})]})};export{Ge as default};
