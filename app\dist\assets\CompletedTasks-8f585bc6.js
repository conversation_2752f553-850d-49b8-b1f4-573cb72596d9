import{q as r,s as S,b as E,r as c,a as l,F as k,pW as D,uv as v,gs as F,K as n,gl as p}from"./index-17b8d91e.js";import{W as b,c as f,u as Q}from"./propData-c3ca5892.js";import{r as y,a as J}from"./userData-010d5077.js";import"./Workflow-7210ba18.js";import"./react-beautiful-dnd.esm-0e527744.js";import"./useMediaQuery-6a073ac5.js";import"./DialogContentText-631f3833.js";import"./CardMedia-bfb247e7.js";import"./Container-d04f3413.js";import"./InputAdornment-5b0053c5.js";import"./ListItemButton-1f7a8ca3.js";import"./Slider-3eb7e770.js";import"./Stepper-88e4fb0c.js";import"./StepButton-34497717.js";import"./ToggleButtonGroup-cf875764.js";import"./index.esm-be84327e.js";import"./makeStyles-213dac7f.js";import"./toConsumableArray-c7e4bd84.js";import"./asyncToGenerator-88583e02.js";import"./DatePicker-e260eb3e.js";import"./Timeline-bd0ec33e.js";import"./dayjs.min-ce01f2c7.js";import"./isBetween-3aeee754.js";import"./CSSTransition-30917e2c.js";function ri(){let a=r(i=>{var e;return(e=i.userManagement)==null?void 0:e.userData});r(i=>{var e;return(e=i.userManagement)==null?void 0:e.groups});const m=r(i=>i.applicationConfig);let N=S();const I=E(),[d,V]=c.useState(null),[U,L]=c.useState(null),R={APPLICATION_NAME:"1784",CRUD_API_ENV:"itm",DB_TYPE:"hana",SERVICE_BASE_URL:[{Description:"",Name:"ITMJavaServices",URL:"https://ca-gbd-ca-caf-cw-caf-iwm.cfapps.us10-001.hana.ondemand.com"},{Description:"",Name:"ConfigServer",URL:"https://cherryworkproducts-config-server.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"WorkNetServices",URL:"https://cherryworkproducts-worknet-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"CrudApiServices",URL:"https://cw-caf-crudapi-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"WorkFormsServices",URL:"https://cherrywork-wf-java-qas.cfapps.eu10-004.hana.ondemand.com/workforms"},{Description:"",Name:"NotificationServices",URL:"https://cherryworkproducts-messaging-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"ITMGraphServices",URL:"https://cherrywork-btp-qas-dashboard.cfapps.eu10-004.hana.ondemand.com"},{Description:"Native Workflow Services",Name:"NativeWorkflowServices",URL:"https://cherryworkproducts-custom-wf-qas.cfapps.eu10-004.hana.ondemand.com"},{Description:"Native Workflow UI URL",Name:"NativeWorkflowUiUrl",URL:"https://cherrywork-native-workflow-qas.cfapps.eu10-004.hana.ondemand.com/native-ui"},{Description:"",Name:"OnboardingServices",URL:"https://cherryworkproducts-itm-java-qas.cfapps.eu10-004.hana.ondemand.com"}]},u=i=>{N(v({module:"RequestHistory",filterData:{reqId:i==null?void 0:i.ATTRIBUTE_1}})),I(F.REQUEST_HISTORY)},h=()=>{console.log("fetchFilterView")},w=()=>{console.log("clearFilterView")},T=()=>{n(`/${p}/api/v1/usersMDG/getUsersMDG`,"get",i=>{var e=i.data,s=e==null?void 0:e.map(t=>({...t,userId:t==null?void 0:t.emailId})),o={...i,data:s};V(o)})},W=()=>{n(`/${p}/api/v1/groupsMDG/getAllGroupsMDG`,"get",i=>{var e=i.data,s=e==null?void 0:e.map(t=>({...t,groupName:t==null?void 0:t.name})),o={...i,data:s};L(o)})},M=(i,e)=>{console.log("Success flag.",i),console.log("Task Payload.",e)};return c.useEffect(()=>{T(),W()},[]),l("div",{style:{width:"calc(100vw - 105px)",height:"calc(100vh-130px)"},className:"workspaceOverride",children:l(k,{children:l(b,{token:"eyJhbGciOiJSUzI1NiIsImprdSI6Imh0dHBzOi8vY2EtZ2JkLmF1dGhlbnRpY2F0aW9uLnVzMTAuaGFuYS5vbmRlbWFuZC5jb20vdG9rZW5fa2V5cyIsImtpZCI6ImRlZmF1bHQtand0LWtleS1kZjk5ODA5MzZhIiwidHlwIjoiSldUIiwiamlkIjogIlNMVzNNUjQ5UDZTMGJwWVJNOWJJZVQ3Z0Q1aDh2SkVpM1ZrOFVEeUVhY3c9In0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.UiGLR24hLDpu661TnyEfCOSMqY5SAjZTwuJbPANHuTXVwZEZwU0y6G3kMkRFn-9NgvxNEkTwIY2vjipbsFvahSnQHeze4doZlQDTU4lP2m_pGCBsXNF3R8kgpdTt_zFPbAwpK0xNz7dA79vVL-J0cPrFhqEWxf6wLQf4L--o150tb69-dFCsOfPP_O6Kuaw5DMvtfHm9Toe4RhBSVJj1zKrhGn-M-7rNl1wUDuc0WTvJeB7yMHz3Q7MtigXj8bdTtjuH_dyLqLVUdnNPnz8HY9EjShPCqAuBtfiqs_Tb167DZyeiaqJCDg5ZmLKtrKQZUdBGuAvMq2ZgkFktjMh72g",configData:f,destinationData:R,userData:{...a,user_id:a==null?void 0:a.emailId},userPermissions:Q,userList:y(d),groupList:J(U),useWorkAccess:m.environment==="localhost",useConfigServerDestination:m.environment==="localhost",inboxTypeKey:"MY_COMPLETED_TASKS",workspaceLabel:"Completed Tasks",subInboxTypeKey:null,onTaskClick:u,onActionComplete:M,workspaceFiltersByAPIDriven:!1,isFilterView:!1,savedFilterViewData:[],selectedFilterView:null,fetchFilterViewList:h,clearFilterView:w,cachingBaseUrl:D,selectedTabId:null,externalSystems:[]})})})}export{ri as default};
