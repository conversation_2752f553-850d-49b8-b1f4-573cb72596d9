import React from "react";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DemoContainer } from "@mui/x-date-pickers/internals/demo";
import DeleteIcon from "@mui/icons-material/Delete";
import DownloadIcon from "@mui/icons-material/Download";
import CloseIcon from "@mui/icons-material/Close";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import ReusableAttachementAndComments from "../../Common/ReusableAttachmentAndComments/ReusableAttachementAndComments";
import {
  InfoOutlined,
  IosShare,
  Refresh,
  TuneOutlined,
} from "@mui/icons-material";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import ReusableIcon from "../../Common/ReusableIcon";
import {
  Button,
  Checkbox,
  Grid,
  Paper,
  IconButton,
  Typography,
  TextField,
  Box,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Popper,
  BottomNavigation,
  ListItemText,
  InputLabel,
  tooltipClasses,
  Card,
  CardContent,
  OutlinedInput,
  Autocomplete,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  ButtonGroup,
  ClickAwayListener,
  MenuList,
  Divider,
  FormControlLabel,
} from "@mui/material";
import FileUpload from "../../Common/FileUpload";
import moment from "moment/moment";
import { Stack } from "@mui/system";
import Select from "@mui/material/Select";
import { FormControl, MenuItem } from "@mui/material";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import ForwardToInboxOutlinedIcon from "@mui/icons-material/ForwardToInboxOutlined";
import CheckCircleOutlineOutlinedIcon from "@mui/icons-material/CheckCircleOutlineOutlined";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import SearchBar from "../../Common/SearchBar";
import ReusableDialog from "../../Common/ReusableDialog";
import AddIcon from "@mui/icons-material/Add";
// import { ViewDetailsIcon } from "../../Common/icons";
import { useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import TrackChangesIcon from "@mui/icons-material/TrackChanges";
import styled from "@emotion/styled";
import html2canvas from "html2canvas";
import {
  commonFilterClear,
  commonFilterUpdate,
} from "../../../app/commonFilterSlice";
import { v4 as uuidv4 } from "uuid";
import {
  button_Marginleft,
  button_Outlined,
  button_Primary,
  container_filter,
  container_table,
  font_Small,
  iconButton_SpacingSmall,
  outerContainer_Information,
  outermostContainer,
  outermostContainer_Information,
} from "../../Common/commonStyles";

import {
  destination_BankKey,
  destination_CostCenter,
  destination_GeneralLedger,
  destination_MaterialMgmt,
} from "../../../destinationVariables";
import { doAjax } from "../../Common/fetchService";
import ClearIcon from "@mui/icons-material/Clear";
import ReusableTable from "../../common/ReusableTable";

import { setDropDown } from "../../../app/dropDownDataSlice";
import { DateRangePicker } from "@mui/lab";
import { Fragment } from "react";

import { MatDownload } from "../../DocumentManagement/UtilDoc";
import AttachmentUploadDialog from "../../Common/AttachmentUploadDialog";
import {
  clearGeneralLedger,
  setHandleMassMode,
  setMultipleGLData,
  setSinglegeneralLedgerPayload,
  setgeneralLedgerControlData,
  setgeneralLedgerCreateBankIntrest,
  setgeneralLedgerInformation,
  setgeneralLedgerKeywordTranslation,
  setgeneralLedgerTypeDescription,
} from "../../../app/generalLedgerTabSlice";
import { setTaskData } from "../../../app/userManagementSlice";
import { checkIwaAccess, saveExcel } from "../../../functions";
import { setCheckBox } from "../../../app/editPayloadSlice";
const HtmlTooltip = styled(({ className, ...props }) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: "#f5f5f9",
    color: "rgba(0, 0, 0, 0.87)",
    maxWidth: 250,
    border: "1px solid #dadde9",
  },
}));
const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};
const exportAsPicture = () => {
  const html = document.getElementsByTagName("HTML")[0];
  const body = document.getElementsByTagName("BODY")[0];
  let htmlWidth = html.clientWidth;
  let bodyWidth = body.clientWidth;

  const data = document.getElementById("e-invoice-export"); //CHANGE THIS ID WITH ID OF OUTERMOST DIV CONTAINER
  const newWidth = data.scrollWidth - data.clientWidth;

  if (newWidth > data.clientWidth) {
    htmlWidth += newWidth;
    bodyWidth += newWidth;
  }

  html.style.width = htmlWidth + "px";
  body.style.width = bodyWidth + "px";

  html2canvas(data)
    .then((canvas) => {
      return canvas.toDataURL("image/png", 1.0);
    })
    .then((image) => {
      saveAs(image, "E-InvoiceReport.png"); //CHANGE THE NAME OF THE FILE
      html.style.width = null;
      body.style.width = null;
    });
};

const saveAs = (blob, fileName) => {
  const elem = window.document.createElement("a");
  elem.href = blob;
  elem.download = fileName;
  (document.body || document.documentElement).appendChild(elem);
  if (typeof elem.click === "function") {
    elem.click();
  } else {
    elem.target = "_blank";
    elem.dispatchEvent(
      new MouseEvent("click", {
        view: window,
        bubbles: true,
        cancelable: true,
      })
    );
  }
  URL.revokeObjectURL(elem.href);
  elem.remove();
};
const GeneralLedger = () => {
  const [snackbar, setSnackbar] = useState(false);
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [messageDialogSeverity, setMessageDialogSeverity] = useState("");
  const [newChartOfAccount, setNewChartOfAccount] = useState("");
  const [newCompanyCode, setNewCompanyCode] = useState("");
  const [newGLAccount, setNewGLAccount] = useState("");
  const appSettings = useSelector((state) => state.appSettings["Format"]);
  const dropdownData = useSelector((state) => state.AllDropDown.dropDown);
  const [newGeneralLedgerValid, setNewGeneralLedgerValid] = useState(false);
  const [isValidationError, setIsValidationError] = useState(false);
  const [checkValidationGeneralLedger, setCheckValidationGeneralLedger] =
    useState(false);
  const [tableLoading, setTableLoading] = useState(false);
  const [newAccountType, setNewAccountType] = useState("");
  const [newAccountGroup, setNewAccountGroup] = useState("");
  const [newGeneralLedgerValidWithCopy, setNewGeneralLedgerValidWithCopy] =
    useState(false);
  const [numberRangeError, setNumberRangeError] = useState(false);
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [skip, setSkip] = useState(0);
  const [newCompanyCodeToCopyFrom, setNewCompanyCodeToCopyFrom] = useState("");
  const [selectedMassChangeRowData, setSelectedMassChangeRowData] = useState(
    []
  );
  const [openDialog, setOpenDialog] = useState(false);
  const handleMassModePC = useSelector(
    (state) => state.generalLedger.handleMassMode
  );
  let iwaAccessData = useSelector(
    (state) => state.userManagement.entitiesAndActivities?.["Display Material"]
  );
  let userData = useSelector((state) => state.userManagement.userData);
  console.log("handleMassModePC", handleMassModePC);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const ITEM_HEIGHT = 48;
  const ITEM_PADDING_TOP = 8;
  const MenuProps = {
    PaperProps: {
      style: {
        maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
        width: 250,
      },
    },
  };
  // console.log("newAccountGroup", newAccountGroup, newAccountType);

  const HtmlTooltip = styled(({ className, ...props }) => (
    <Tooltip {...props} classes={{ popper: className }} />
  ))(({ theme }) => ({
    [`& .${tooltipClasses.tooltip}`]: {
      backgroundColor: "#f5f5f9",

      color: "rgba(0, 0, 0, 0.87)",

      maxWidth: 250,

      border: "1px solid #dadde9",
    },
  }));
  const [Status_ServiceReqForm, setStatus_ServiceReqForm] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [value, setValue] = useState(null);
  const ariaLabel = { "aria-label": "description" };
  const [rmDataRows, setRmDataRows] = useState([]);
  const [UserName, setUserName] = React.useState("");
  const [openSnackBaraccept, setOpenSnackBaraccept] = useState(false);
  const [confirmingid, setConfirmingid] = useState("");
  const [materialNumber, setMaterialNumber] = useState("");
  const [confirmStatus, setConfirmStatus] = useState(true);
  const [messageDialogExtra, setMessageDialogExtra] = useState(false);
  const [messageDialogOK, setMessageDialogOK] = useState(true);
  const [companyCodeSet, setCompanyCodeSet] = useState([]);
  const [plantCodeSet, setPlantCodeSet] = useState([]);
  const [vendorDetailsSet, setVendorDetailsSet] = useState([]);
  const [taskstatusSet, setTasksttusSet] = useState([]);
  const [disableButton, setDisableButton] = useState(true);
  const [selectedRow, setSelectedRow] = useState([]);
  const [selectedDetails, setSelectedDetails] = useState([]);
  const [downloadError, setdownloadError] = useState(false);
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [displayedFields, setDisplayedFields] = useState([]);
  const [filterFieldData, setFilterFieldData] = useState({});
  const [matType, setMatType] = useState([]);
  const [matGroup, setMatGroup] = useState([]);
  const [viewDetailpage, setViewDetailpage] = useState(false);
  const [matNumber, setMatNumber] = useState([]);
  const [dynamicOptions, setDynamicOptions] = useState([]);
  const [plantForWarehouse, setPlantForWarehouse] = useState([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [fullWidth, setFullWidth] = useState(true);
  const [maxWidth, setMaxWidth] = useState("sm");
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [selectedIndexChange, setSelectedIndexChange] = useState(0);
  const [enableDocumentUpload, setEnableDocumentUpload] = useState(false);
  const [openButtonChange, setOpenButtonChange] = useState(false);
  const [successMsg, setsuccessMsg] = useState(false);
  const [dialogOpenCreate, setDialogOpenCreate] = useState(false);
  const [checkValidationCostCenter, setCheckValidationCostCenter] =
    useState(false);
  const [newGLAccountCopyFrom, setNewGLAccountCopyFrom] = useState("");
  const anchorRef = React.useRef(null);
  const anchorRefChange = React.useRef(null);
  const [openButton, setOpenButton] = useState(false);
  const [isValidationErrorwithCopy, setIsValidationErrorwithCopy] =
    useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [count, setCount] = useState(0);
  const [tableRows, setTableRows] = useState([]);
  const rmSearchForm = useSelector(
    (state) => state.commonFilter["GeneralLedger"]
  );
  const formcontroller_SearchBar = useSelector(
    (state) => state.commonSearchBar["GeneralLedger"]
  );
  const sendNewGeneralLedgerData = {
    chartOfAccounts: { newChartOfAccount },
    companyCode: { newCompanyCode },
    accountType: { newAccountType },
    accountGroup: { newAccountGroup },
    newGLAccount,
    copyFromCompCode: { newCompanyCodeToCopyFrom },
    copyFromGlAccount: { newGLAccountCopyFrom },
  };
  const handleDialogClickOpen = () => {
    setDialogOpen(true);
  };
  console.log(
    "newGLAccount",
    typeof newGLAccount,
    typeof newAccountGroup?.FromAcct
  );
  // console.log("sendNewGeneralLedgerData", sendNewGeneralLedgerData);

  const checkForNameAndCompCodeDuplicateCheck = () => {
    let result = newGLAccount.concat("$$", newCompanyCode?.code);
    setIsLoading(true);
    const hSuccess = (data) => {
      setIsLoading(false);

      if (data.body.length > 0) {
        setCheckValidationGeneralLedger(true);
      } else {
        dispatch(
          setSinglegeneralLedgerPayload({
            keyName: "AccountType",
            data: newAccountType,
          })
        );
        dispatch(
          setSinglegeneralLedgerPayload({
            keyName: "AccountGroup",
            data: {
              code: newAccountGroup?.AccountGroup,
              desc: newAccountGroup?.Description,
            },
          })
        );
        navigate(`/masterDataCockpit/generalLedger/newSingleGeneralLedger`, {
          state: sendNewGeneralLedgerData,
        });
      }
    };
    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger}/alter/fetchGlAccountNCompCodeDupliChk?glAccountNCompCode=${result}`,
      "get",
      hSuccess,
      hError
    );
  };
  const duplicateCheck = () => {
    if (
      newChartOfAccount.code === undefined ||
      newChartOfAccount.code === "" ||
      newCompanyCode?.code === undefined ||
      newCompanyCode?.code === "" ||
      newAccountType?.code === undefined ||
      newAccountType?.code === "" ||
      newAccountGroup?.AccountGroup === undefined ||
      newAccountGroup?.AccountGroup === "" ||
      newGLAccount === undefined ||
      newGLAccount === ""
    ) {
      setNewGeneralLedgerValid(false);
      setIsValidationError(true);
      setNumberRangeError(false);
      return;
    } else {
      if (newGLAccount.length !== 10) {
        setNumberRangeError(false);
        setNewGeneralLedgerValid(true);
        setIsValidationError(false);
        return;
      } else {
        let GlNumber = Number(newGLAccount); //**********
        let minNumber = Number(newAccountGroup?.FromAcct); //**********  ""
        let maxNumber = Number(newAccountGroup?.ToAcct); //**********   "ZZZZZZZZZZ"
        console.log("check", minNumber, maxNumber, isNaN(maxNumber));
        if (
          minNumber == 0 ||
          isNaN(minNumber) ||
          maxNumber == 0 ||
          isNaN(maxNumber)
        ) {
          console.log(
            "inside if condition",
            Number(minNumber),
            Number(maxNumber)
          );
          setNumberRangeError(false);
          setIsValidationError(false);
          setNewGeneralLedgerValid(false);
          checkForNameAndCompCodeDuplicateCheck();
        } else {
          if (GlNumber >= minNumber && GlNumber <= maxNumber) {
            console.log("inside else then if for number check");
            setNumberRangeError(false);
            setIsValidationError(false);
            setNewGeneralLedgerValid(false);
            checkForNameAndCompCodeDuplicateCheck();
          } else {
            console.log("inside else condition");
            setNumberRangeError(true);
            setIsValidationError(false);
            setNewGeneralLedgerValid(false);
          }
          setNewGeneralLedgerValid(false);
        }
        setIsValidationError(false);
      }
    }
  };
  const handleDialogProceed = () => {
    duplicateCheck();
  };

  const handleDialogCloseCreate = () => {
    setIsValidationError(false);
    setIsValidationErrorwithCopy(false);
    setNewGeneralLedgerValidWithCopy(false);
    // setNewControllingArea('');
    // setNewComapnyCode('');
    // setNewCostCenterName('')
    // setNewControllingAreaCopyFrom('')
    // setNewCostCenterCopyFrom('')
    setDialogOpenCreate(false);
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
    setNumberRangeError(false);
    setIsValidationError(false);
    setNewGeneralLedgerValid(false);
  };
  const handleToggle = () => {
    setOpenButton((prevOpen) => !prevOpen);
  };
  const handleToggleChange = () => {
    setOpenButtonChange((prevOpen) => !prevOpen);
  };
  const handleCloseButton = (event) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }
    setOpenButton(false);
  };
  const handleCloseButtonChange = (event) => {
    if (
      anchorRefChange.current &&
      anchorRefChange.current.contains(event.target)
    ) {
      return;
    }
    setOpenButtonChange(false);
  };
  const options = ["Create Multiple", "Upload Template ", "Download Template "];
  const optionsChange = [
    "Change Multiple",
    "Upload Template ",
    "Download Template ",
  ];
  const [selectedIndexCreate, setSelectedIndexCreate] = useState(0);
  const optionsCreateSingle = ["Create Single", "With Copy", "Without Copy"];
  const anchorRefCreate = React.useRef(null);
  const [openButtonCreate, setOpenButtonCreate] = useState(false);
  const handleToggleCreate = () => {
    setOpenButtonCreate((prevOpen) => !prevOpen);
  };

  const handleDialogProceedWithCopy = () => {
    duplicateCheckWithCopy();
  };
  const duplicateCheckWithCopy = () => {
    if (
      newChartOfAccount.code === undefined ||
      newChartOfAccount.code === "" ||
      newCompanyCode?.code === undefined ||
      newCompanyCode?.code === "" ||
      newGLAccount === undefined ||
      newGLAccount === "" ||
      newCompanyCodeToCopyFrom?.code === undefined ||
      newCompanyCodeToCopyFrom?.code === "" ||
      newGLAccountCopyFrom?.code === undefined ||
      newGLAccountCopyFrom?.code === ""
    ) {
      setNewGeneralLedgerValid(false);
      setIsValidationErrorwithCopy(true);
      return;
    } else {
      if (newGLAccount.length !== 10) {
        setNewGeneralLedgerValid(true);
        setIsValidationErrorwithCopy(false);
        return;
      } else {
        setNewGeneralLedgerValid(false);
      }
      setIsValidationErrorwithCopy(false);
    }
    // console.log("result1");
    let result = newGLAccount.concat("$$", newCompanyCode?.code);
    // console.log("result", result);
    setIsLoading(true);
    const hSuccess = (data) => {
      setIsLoading(false);

      if (data.body.length > 0) {
        setCheckValidationGeneralLedger(true);
      } else {
        // console.log("displayfirst");
        navigate(`/masterDataCockpit/generalLedger/displayCopyGeneralLedger`, {
          state: sendNewGeneralLedgerData,
        });
        // navigate(`/masterDataCockpit/generalLedger/displayCopyGeneralLedger/123`);
      }
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/alter/fetchGlAccountNCompCodeDupliChk?glAccountNCompCode=${result}`,
      "get",
      hSuccess,
      hError
    );
  };
  const handleDialogClickOpenWithCopy = () => {
    setDialogOpenCreate(true);
  };

  const handleDialogCloseCreateSingle = (event) => {
    if (
      anchorRefCreate.current &&
      anchorRefCreate.current.contains(event.target)
    ) {
      return;
    }
    //setDialogOpenCreate(false);
    setOpenButtonCreate((prevOpen) => !prevOpen);
  };
  const handleClickCreate = (option, index) => {
    // dispatch(setHandleMassMode("Change"));
    if (index !== 0) {
      setSelectedIndexCreate(index);
      setOpenButtonCreate(false);
      if (index === 1) {
        handleCreateSingleWithCopy();
      } else if (index === 2) {
        handleCreateSingleWithoutCopy();
      }
    }
  };
  const handleCreateSingleWithoutCopy = () => {
    handleDialogClickOpen();
  };
  const handleCreateSingleWithCopy = () => {
    handleDialogClickOpenWithCopy();
  };
  const handleSnackBarOpen = () => {
    setopenSnackbar(true);
  };

  const getAccountGroupForSearch = (value) => {
    // console.log("called");
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "AccountGroupSearch", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getAccountGroupCodeDesc?chartAccount=${value}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCompanyCodeForSearch = (value) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CompanyCodeSearch", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getCompanyCode?chartAccount=${value}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getGroupAccountNumberForSearch = (value) => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "GroupAccountNumberSearch", data: data.body })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getGroupAccountNumber?chartAccount=${value}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getGLAccountForSearch = (value) => {
    // console.log("called");
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "GLAccountForSearch", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getGLBasedOnCompanyCode?companyCode=${value}`,
      "get",
      hSuccess,
      hError
    );
  };

  const handleChartOfAccount = (e, value) => {
    if (true) {
      var tempChartOfAccount = value;

      let tempFilterData = {
        ...rmSearchForm,
        chartOfAccount: tempChartOfAccount,
      };
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    }
    getAccountGroupForSearch(value?.code);
    getCompanyCodeForSearch(value?.code);
    getGroupAccountNumberForSearch(value?.code);
  };
  const handleCompanyCode = (e, value) => {
    if (true) {
      var tempCompanyCode = value;

      let tempFilterData = {
        ...rmSearchForm,
        companyCode: tempCompanyCode,
      };
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    }
    getGLAccountForSearch(value?.code);
  };
  const handleGLAccount = (e, value) => {
    if (true) {
      var tempGlAccount = value;

      let tempFilterData = {
        ...rmSearchForm,
        glAccount: tempGlAccount,
      };
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    }
  };

  const handleGlAccountType = (e, value) => {
    if (true) {
      var tempGlAccountType = value;

      let tempFilterData = {
        ...rmSearchForm,
        glAccountType: tempGlAccountType,
      };
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleShortText = (e) => {
    if (e.target.value !== null) {
      var tempShortText = e.target.value;
      // console.log("shorttextvalue", tempShortText);
      let tempFilterData = {
        ...rmSearchForm,
        shortText: tempShortText,
      };
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleAccountGroup = (e, value) => {
    if (true) {
      var tempAccountGroup = value;

      let tempFilterData = {
        ...rmSearchForm,
        accountGroup: tempAccountGroup,
      };
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleFunctionalArea = (e, value) => {
    if (true) {
      var tempFunctionalArea = value;

      let tempFilterData = {
        ...rmSearchForm,
        functionalArea: tempFunctionalArea,
      };
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleGlAccountText = (e, value) => {
    if (true) {
      var tempGlAccountText = value;

      let tempFilterData = {
        ...rmSearchForm,
        glAccountText: tempGlAccountText,
      };
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleGroupAccountNumber = (e, value) => {
    if (true) {
      var tempGroupAccountNumber = value;

      let tempFilterData = {
        ...rmSearchForm,
        groupAccountNumber: tempGroupAccountNumber,
      };
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleAccountCurrency = (e, value) => {
    if (true) {
      var tempAccountCurrency = value;

      let tempFilterData = {
        ...rmSearchForm,
        accountCurrency: tempAccountCurrency,
      };
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleTaxCategory = (e, value) => {
    if (true) {
      var tempTaxCategory = value;

      let tempFilterData = {
        ...rmSearchForm,
        taxCategory: tempTaxCategory,
      };
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    }
  };

  const handleCreatedBy = (e, value) => {
    if (true) {
      var tempCreatedBy = value;

      let tempFilterData = {
        ...rmSearchForm,
        createdBy: tempCreatedBy,
      };
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleChangedBy = (e, value) => {
    if (true) {
      var tempChangedBy = value;

      let tempFilterData = {
        ...rmSearchForm,
        changedBy: tempChangedBy,
      };
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    }
  };
  const uploadExcel = (file) => {
    // console.log(file);
    const formData = new FormData();
    [...file].forEach((item) => formData.append("files", item));
    if (handleMassModePC === "Change") {
      var uploadUrl = `/${destination_GeneralLedger}/massAction/getAllGeneralLedgerFromExcelForMassChange`;
    } else {
      var uploadUrl = `/${destination_GeneralLedger}/massAction/getAllGeneralLedgerFromExcel`;
    }
    const hSuccess = (data) => {
      // console.log(data, "example");
      setIsLoading();
      if (data.statusCode === 200) {
        setEnableDocumentUpload(false);
        // dispatch(setControllingArea(data?.body?.controllingArea));
        dispatch(setMultipleGLData(data?.body));
        setMessageDialogTitle("Create");
        setMessageDialogMessage(`${file.name} has been Uploaded Succesfully`);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
        navigate(`/masterDataCockpit/generalLedger/createMultipleGL`);
      } else {
        setEnableDocumentUpload(false);
        setMessageDialogTitle("Create");
        setsuccessMsg(false);
        setMessageDialogMessage("Creation Failed");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(uploadUrl, "postformdata", hSuccess, hError, formData);
  };
  // const handleSelection = (event) => {
  //   const selectedItems = event.target.value;
  //   fetchOptionsForDynamicFilter(dynamicDataApis[selectedItems])
  //   setSelectedOptions(selectedItems);
  //   setDisplayedFields([]);
  // };

  let dynamicDataApis = {
    "Person Responsible": `/${destination_MaterialMgmt}/data/getSalesOrg`,
    "Business Area ": `/${destination_MaterialMgmt}/data/getDivision`,
    "Functional Area": `/${destination_MaterialMgmt}/data/getLaboratoryDesignOffice`,
    // "Transportation Group" : `/${destination_MaterialMgmt}/data/getTransportationGroup`,
    // "Batch Management" : `/${destination_MaterialMgmt}/data/getBatchManagement`,
    // "Old Material Number" : `/${destination_MaterialMgmt}/data/getOldMaterialNo`,
  };

  const handleSelection = (event) => {
    const selectedItems = event.target.value;
    setSelectedOptions(selectedItems);
    setDisplayedFields([]);
    // console.log("selected field", event.target.value);

    selectedItems.forEach(async (selectedItem) => {
      const apiEndpoint = dynamicDataApis[selectedItem];
      fetchOptionsForDynamicFilter(apiEndpoint);
    });
  };
  const handleSelectionModelChange = (newSelection) => {
    console.log("newselection", newSelection);
    setSelectedRows(newSelection);
    let filterValueColumns = columns.map((t) => t.field);
    const selectedRowsDetails = rmDataRows.filter((row) =>
      newSelection.includes(row.id)
    );
    let requiredArrayDetails = [];
    selectedRowsDetails.map((s) => {
      console.log("sssssss", s);
      let requiredObject = {};
      filterValueColumns.forEach((y) => {
        console.log("yyyyy", s[y]);
        if (s[y] !== null) {
          requiredObject[y] = s[y] || "";
        }
      });
      requiredArrayDetails.push(requiredObject);
      setSelectedMassChangeRowData(requiredArrayDetails);
      console.log("requiredArrayDetails", requiredArrayDetails);
    });
  };
  const handleAddFields = () => {
    const numSelected = selectedOptions.length;
    const newFields = Array.from({ length: numSelected }, (_, index) => ({
      id: index,
      value: "",
    }));
    setDisplayedFields(newFields);
  };

  const handleFieldChange = (fieldId, value) => {
    setDisplayedFields(
      (selectedOptions) => selectedOptions.map((option) => option)
      // prevFields.map((field) => (field.id === fieldId ? { ...field, value } : field))
    );
  };
  const items = [
    { title: "Field Status Group" },
    // { title: "Warehouse Number" },
    // { title: "Storage Location" },
    { title: "Group Chart Of Accounts" },
    { title: "Chart Of Accounts" },
    { title: "Controlling Area" },
    // { title: "Status"},
    // { title: "Lab/Office" },
    // { title: "Transportation Group" },
    // { title: "Batch management" },
    // Add more options as needed
  ];
  const titleToFieldMapping = {
    "Task ID": "taskId",
    Status: "status",
    SalesOrganization: "salesOrg",
    Division: "division",
    OldMaterialNumber: "oldMaterialNumber",
    "Lab/Office": "labOffice",
    "Transportation Group": "transportationGroup",
    "Batch management": "batchManagement",
    // Add more mappings as needed
  };
  // let [rmSearchForm, setRmSearchForm] = useState({
  //   companyCode: "",
  //   vendorNo: "",
  //   paymentStatus: "",
  // });
  //Checked PO rows

  const getGLControlData = () => {
    let viewName = "Control Data";
    const hSuccess = (data) => {
      dispatch(setgeneralLedgerControlData(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getGLCreateBankInterest = () => {
    let viewName = "Create/Bank/Interest";
    const hSuccess = (data) => {
      dispatch(setgeneralLedgerCreateBankIntrest(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getGLTypeDescription = () => {
    let viewName = "Type/Description";
    const hSuccess = (data) => {
      dispatch(setgeneralLedgerTypeDescription(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getGLInformation = () => {
    let viewName = "Information";
    const hSuccess = (data) => {
      dispatch(setgeneralLedgerInformation(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getGLKeywordTranslation = () => {
    let viewName = "Keyword/Translation";
    const hSuccess = (data) => {
      dispatch(setgeneralLedgerKeywordTranslation(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getGLAccountType = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "AccountType", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getGLAccountType`,
      "get",
      hSuccess,
      hError
    );
  };
  const getTradingPartner = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "TradingPartner", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getTradingPartner`,
      "get",
      hSuccess,
      hError
    );
  };
  const getBusinessArea = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "BusinessArea", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getBusinessArea`,
      "get",
      hSuccess,
      hError
    );
  };
  const getFunctionalArea = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "FunctionalArea", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getFunctionalArea`,
      "get",
      hSuccess,
      hError
    );
  };
  const getProfitCenter = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ProfitCenter", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getProfitCenter`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCostingSheet = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CostingSheet", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getCostingSheet`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCountryOrRegion = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CountryReg", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_CostCenter}/data/getCountryOrReg`,
      "get",
      hSuccess,
      hError
    );
  };
  const getJurisdiction = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "Jurisdiction", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getJurisdiction`,
      "get",
      hSuccess,
      hError
    );
  };
  const getRegion = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "Region", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getRegion`,
      "get",
      hSuccess,
      hError
    );
  };
  const getLanguageKey = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "LanguageKey", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getLanguageKey`,
      "get",
      hSuccess,
      hError
    );
  };

  const getChartOfAccounts = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ChartOfAccounts", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getChartOfAccounts`,
      "get",
      hSuccess,
      hError
    );
  };
  const getReconAccountForAccountType = () => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "ReconAccountForAccountType", data: data.body })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getReconAccountForAccountType`,
      "get",
      hSuccess,
      hError
    );
  };
  const getSortKey = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "SortKey", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getSortKey`,
      "get",
      hSuccess,
      hError
    );
  };
  const getPlanningLevel = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "PlanningLevel", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getPlanningLevel`,
      "get",
      hSuccess,
      hError
    );
  };
  const getInternalUOM = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "InternalUOM", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getInternalUOM`,
      "get",
      hSuccess,
      hError
    );
  };
  const getLanguage = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "Language", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getLanguage`,
      "get",
      hSuccess,
      hError
    );
  };

  const getInterestIndicator = (value) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "InterestIndicator", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getInterestIndicator`,
      "get",
      hSuccess,
      hError
    );
  };
  const getInterestCalculationFrequency = (value) => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({
          keyName: "InterestCalculationFrequency",
          data: data.body,
        })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getInterestCalculationFreq`,
      "get",
      hSuccess,
      hError
    );
  };
  const getGlAccountBasedOnCompanyCode = (value) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "GlAccountCompCode", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getGLBasedOnCompanyCode?companyCode=${value?.code}`,
      "get",
      hSuccess,
      hError
    );
  };

  useEffect(() => {
    getBusinessArea();
    getFunctionalArea();
    getProfitCenter();
    getCostingSheet();
    getCountryOrRegion();
    getJurisdiction();
    getRegion();
    getLanguageKey();
    getGLControlData();
    getGLCreateBankInterest();
    getGLTypeDescription();
    getGLInformation();
    getGLKeywordTranslation();
    getGLAccountType();
    getTradingPartner();
    getChartOfAccounts();
    getReconAccountForAccountType();
    getSortKey();
    getPlanningLevel();
    getInternalUOM();
    getLanguage();
    getInterestIndicator();
    getInterestCalculationFrequency();
    dispatch(setTaskData({}));
    dispatch(clearGeneralLedger());
  }, []);
  // const fetchOptionsForDynamicFilter = (apiEndpoint) => {
  //   const hSuccess = (data) => {
  //     setDynamicOptions({
  //       ...dynamicOptions,
  //       [apiEndpoint]: data.body,

  //     });
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(apiEndpoint, "get", hSuccess, hError);
  // };

  const fetchOptionsForDynamicFilter = (apiEndpoint) => {
    const hSuccess = (data) => {
      // console.log("dataaaaaaaa", data.body);
      setDynamicOptions([...dynamicOptions, data.body]);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(apiEndpoint, "get", hSuccess, hError);
  };

  const clearSearchBar = () => {
    setMaterialNumber("");
  };
  const getGlAccountNoGlobalSearch = (fetchSkip) => {
    setTableLoading(true);
    if (!fetchSkip) {
      setPage(0);
      setPageSize(10);
      setSkip(0);
    }
    let payload = {
      companyCode: "",
      glAccount: formcontroller_SearchBar?.number ?? "",
      accountType: "",
      createdBy: "",
      glName: "",
      accountGroup: "",
      skip: 0,
      top: 1000,
    };

    const hSuccess = (data) => {
      // console.log("inside hsuccess");
      var rows = [];
      for (let index = 0; index < data?.body?.list?.length; index++) {
        var tempObj = data?.body?.list[index];
        // console.log("inserted");
        // console.log("hshshsh", tempObj);
        // if (tempObj["MaterialNo"]) {
        var tempRow = {
          id: uuidv4(),
          chartOfAccount: tempObj?.COA !== "" ? tempObj?.COA : "NA",
          glAccountType:
            tempObj?.Accounttype !== "" ? tempObj?.Accounttype : "NA",
          accountGroup:
            tempObj?.AccountGroup !== "" ? tempObj?.AccountGroup : "NA",
          compCode: tempObj?.CompanyCode !== "" ? tempObj?.CompanyCode : "NA",
          glAccount: tempObj?.GLAccount !== "" ? tempObj?.GLAccount : "NA",
          // functionalArea: `${tempObj["Materialtype"]} - ${tempObj["MaterialTypeDesc"]}`,
          // glAccountText: tempObj["MaterialDescrption"],
          groupAccountNumber:
            tempObj?.GroupAccNo !== "" ? tempObj?.GroupAccNo : "NA",
          // accountCurrency: tempObj.Division,
          // taxCategory: tempObj.OldMaterialNumber,
          createdBy: tempObj?.CreatedBy !== "" ? tempObj?.CreatedBy : "NA",
          // changedBy: tempObj.TrnsportGroup,
          // createdOn: tempObj.SalesOrg,
        };
        rows.push(tempRow);
        // }
      }
      // console.log("tempobj", tempRow);
      rows.sort(
        (a, b) =>
          moment(a.createdOn, "DD MMM YYYY HH:mm") -
          moment(b.createdOn, "DD MMM YYYY HH:mm")
      );
      setRmDataRows(rows.reverse());
      setTableLoading(false);
      // setIsLoading(false);
      setroCount(rows.length);
      setCount(data?.body?.count);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getGeneralLedgersBasedOnAdditionalParams`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  /* Setting Default Dates */
  const presentDate = new Date();
  const backDate = new Date();
  backDate.setDate(backDate.getDate() - 15);

  const [date, setDate] = useState([backDate, presentDate]);
  const [date1, setDate1] = useState([backDate, presentDate]);

  const handleDate = (e) => {
    // if (e !== null) setDate(e.reverse());
    if (e !== null) {
      var createdOn = e.reverse();
      dispatch(
        commonFilterUpdate({
          module: "MaterialMaster",
          filterData: {
            ...rmSearchForm,
            createdOn: createdOn,
          },
        })
      );
    }
  };

  const handleDate1 = (e) => {
    if (e !== null) setDate1(e.reverse());
  };

  const handleSnackBarClickaccept = () => {
    setOpenSnackBaraccept(true);
  };

  const handleSnackBarCloseaccept = (event, reason) => {
    if (reason === "clickaway") {
      return;
    }

    setOpenSnackBaraccept(false);
  };

  const handleUserName = (e) => {
    setUserName(e.target.value);
  };
  // Get Filter Data
  const getFilter = (fetchSkip) => {
    setTableLoading(true);
    if (!fetchSkip) {
      setPage(0);
      setPageSize(10);
      setSkip(0);
    }
    let payload = {
      companyCode: rmSearchForm?.companyCode?.code ?? "",
      glAccount: rmSearchForm?.glAccount?.code ?? "",
      accountType: rmSearchForm?.glAccountType?.code ?? "",
      createdBy: "",
      glName: rmSearchForm?.shortText ?? "",
      accountGroup: rmSearchForm?.accountGroup?.code ?? "",
      skip: 0,
      top: 1000,
    };
    const hSuccess = (data) => {
      // console.log("inside hsuccess");
      var rows = [];
      for (let index = 0; index < data?.body?.list?.length; index++) {
        var tempObj = data?.body?.list[index];
        // console.log("inserted");
        // console.log("hshshsh", tempObj);
        // if (tempObj["MaterialNo"]) {
        var tempRow = {
          id: uuidv4(),
          chartOfAccount: tempObj?.COA !== "" ? tempObj?.COA : "NA",
          glAccountType:
            tempObj?.Accounttype !== "" ? tempObj?.Accounttype : "NA",
          accountGroup:
            tempObj?.AccountGroup !== "" ? tempObj?.AccountGroup : "NA",
          compCode: tempObj?.CompanyCode !== "" ? tempObj?.CompanyCode : "NA",
          glAccount: tempObj?.GLAccount !== "" ? tempObj?.GLAccount : "NA",
          // functionalArea: `${tempObj["Materialtype"]} - ${tempObj["MaterialTypeDesc"]}`,
          // glAccountText: tempObj["MaterialDescrption"],
          groupAccountNumber:
            tempObj?.GroupAccNo !== "" ? tempObj?.GroupAccNo : "NA",
          // accountCurrency: tempObj.Division,
          // taxCategory: tempObj.OldMaterialNumber,
          createdBy: tempObj?.CreatedBy !== "" ? tempObj?.CreatedBy : "NA",
          // changedBy: tempObj.TrnsportGroup,
          // createdOn: tempObj.SalesOrg,
        };
        rows.push(tempRow);
        // }
      }
      // console.log("tempobj", tempRow);
      rows.sort(
        (a, b) =>
          moment(a.createdOn, "DD MMM YYYY HH:mm") -
          moment(b.createdOn, "DD MMM YYYY HH:mm")
      );
      setRmDataRows(rows.reverse());
      setTableLoading(false);
      // setIsLoading(false);
      setroCount(rows.length);
      setCount(data?.body?.count);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getGeneralLedgersBasedOnAdditionalParams`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  // console.log("rmdatarows", rmDataRows);
  useEffect(() => {
    if ((parseInt(page) + 1) * parseInt(pageSize) >= parseInt(skip) + 1000) {
      getFilter(skip + 1000);
      setSkip((prev) => prev + 1000);
    }
  }, [page, pageSize]);
  const [userList, setUserList] = useState([]);

  // const getUserList = () => {
  //   var formData = new FormData();
  //   formData.append("module", "PR");
  //   formData.append("compCode", `${userData?.companyCode} - ${userData?.companyName}`);
  //   const hSuccess = (data) => {
  //     setUserList(data.map((user) => `${user.displayName} - ${user.emailId}`));
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(
  //     `/${destination_PR}/users/get-created-by`,
  //     "postformdata",
  //     hSuccess,
  //     hError,
  //     formData
  //   );
  // };

  // useEffect(() => {
  //   getUserList()
  // }, [])
  const moduleFilterData = [
    {
      type: "singleSelect",
      filterName: "company",
      // filterData: masterData?.companyCode,
      filterTitle: "Company",
    },
    {
      type: "singleSelect",
      filterName: "vendor",
      filterData: vendorDetailsSet,
      filterTitle: "Supplier",
    },
    {
      type: "text",
      filterName: "poNum",
      filterTitle: "PO Number",
    },
    // {
    //   type: "multiSelect",
    //   filterName: "poStatus",
    //   filterData: keyword["Return Status"],
    //   filterTitle: "Return Status",
    // },
    {
      type: "autoComplete",
      filterName: "createdBy",
      filterData: userList,
      filterTitle: "Created By",
    },
    {
      type: "singleSelectKV",
      filterName: "returnType",
      filterData: {
        "Debit Note": "Debit Note",
        Replacement: "Replacement",
      },
      filterTitle: "Return Type",
    },
    {
      type: "singleSelect",
      filterName: "plant",
      filterData: plantCodeSet,
      filterTitle: "Plant",
    },
    {
      type: "dateRange",
      filterName: "createdOn",
      filterTitle: "Return Request Date",
    },
  ];

  const [confirmation, setconfirmation] = useState([]);
  const [confirmationText, setConfirmationText] = useState(null);
  const [poHeader, setPoHeader] = useState(null);
  const [roCount, setroCount] = useState(0);
  const [showBtmNav, setShowBtmNav] = useState(false);
  const [opendialog, setOpendialog] = useState(false);
  const [openSnackbarDialog, setOpenSnackbarDialog] = useState(false);
  const [opendialog2, setOpendialog2] = useState(false);
  const [opendialog3, setOpendialog3] = useState(false);
  const [openforwarddialog, setOpenforwarddialog] = useState(false);
  const [rejectInputText, setRejectInputText] = useState("");
  const [acceptInputText, setAcceptInputText] = useState("");
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState("");
  const [messageDialogMessage, setMessageDialogMessage] = useState();
  const handleSnackbarClose = () => {
    setopenSnackbar(false);
  };

  const handleReject = () => {
    setMessageDialogTitle("Success");
    setMessageDialogMessage("Comment Posted");
    setMessageDialogSeverity("success");
    handleMessageDialogClickOpen();
  };
  const handleAccept = () => {
    setMessageDialogTitle("Success");
    setMessageDialogMessage("Comment Posted");
    setMessageDialogSeverity("success");
    handleMessageDialogClickOpen();
  };

  const handleOpendialog = (id) => {
    setID(id);
    fetchPOHeader(id);
    setOpendialog(true);
  };

  const handleClosedialog = () => {
    setOpendialog(false);
  };
  const handleOpendialog2 = (id) => {
    setID(id);
    fetchPOHeader(id);
    setOpendialog2(true);
  };
  const handleClosedialog2 = () => {
    setOpendialog2(false);
  };

  const handleOpendialog3 = (id) => {
    setOpendialog3(true);
    setConfirmingid(id);
    fetchPOHeader(id);
  };
  const handleClosedialog3 = () => {
    setOpendialog3(false);
  };
  const handleOpenforwarddialog = () => {
    setOpenforwarddialog(true);
  };

  const handleCloseforwarddialog = () => {
    setOpenforwarddialog(false);
  };
  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };

  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };

  const [anchorEl_Preset, setAnchorEl] = useState(null);
  const openAnchor = Boolean(anchorEl_Preset);

  const handleClose_Preset = () => {
    setPresetName("");
    setAnchorEl(null);
  };

  const [presets, setPresets] = useState(null);
  const [presetName, setPresetName] = useState(null);

  const handleClear = () => {
    // setMaterialFilterDetails({
    //   number: null,
    //   type: null,
    //   description: "",
    //   group: null,
    //   plant: null,
    //   createdBy: "",
    //   createdOn: [null, null],
    // });
    dispatch(commonFilterClear({ module: "GeneralLedger" }));
  };
  const onRowsSelectionHandler = (ids) => {
    //Selected Columns stored here
    const selectedRowsData = ids.map((id) =>
      rmDataRows.find((row) => row.id === id)
    );
    var compCodes = selectedRowsData.map((row) => row.company);
    var companySet = new Set(compCodes);
    var vendors = selectedRowsData.map((row) => row.vendor);
    var vendorSet = new Set(vendors);
    var paymentTerms = selectedRowsData.map((row) => row.paymentTerm);
    var paymentTermsSet = new Set(paymentTerms);
    if (selectedRowsData.length > 0) {
      if (companySet.size === 1) {
        if (vendorSet.size === 1) {
          if (paymentTermsSet.size !== 1) {
            setDisableButton(true);
            setMessageDialogTitle("Error");
            setMessageDialogMessage(
              "Invoice cannot be generated for vendors with different payment terms"
            );
            setMessageDialogSeverity("danger");
            handleMessageDialogClickOpen();
          } else setDisableButton(false);
        } else {
          setDisableButton(true);
          setMessageDialogTitle("Error");
          setMessageDialogMessage(
            "Invoice cannot be generated for multiple suppliers"
          );
          setMessageDialogSeverity("danger");
          handleMessageDialogClickOpen();
        }
      } else {
        setDisableButton(true);
        setMessageDialogTitle("Error");
        setMessageDialogMessage(
          "Invoice cannot be generated for multiple companies"
        );
        setMessageDialogSeverity("danger");
        handleMessageDialogClickOpen();
      }
    } else {
      setDisableButton(true); //Enable the Create E-Invoice button when at least one row is selected and no two companys or vendors are same
    }
    setSelectedRow(ids); //Setting the ids(PO Numbers) of selected rows
    setSelectedDetails(selectedRowsData); //Setting the entire data of a selected row
  };
  function refreshPage() {
    getFilter();
  }

  const [company, setCompany] = useState([]);
  const [Companyid, setCompanyid] = useState([]);

  // let { poId } = useParams();
  const [open, setOpen] = useState(false);
  const [matAnchorEl, setMatAnchorEl] = useState(null);
  const [materialDetails, setMaterialDetails] = useState(null);
  const [itemDataRows, setItemDataRows] = useState([]);

  // const handleMatCodeClick = (event) => {
  //   if (materialDetails !== null) setMaterialDetails(null);
  //   else fetchMaterialDetails(event.target.innerText);
  //   setMatAnchorEl(matAnchorEl ? null : event.currentTarget);
  // };

  const handlePODetailsClick = (event) => {
    setOpendialog3(true);
  };

  const matOpen = Boolean(matAnchorEl);
  const popperId = matOpen ? "simple-popper" : undefined;

  const handleClickOpen = () => {
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
  };

  const [poNum, setPONum] = useState(null);
  const fetchPOHeader = (id) => {
    var formData = new FormData();
    if (id) formData.append("extReturnId", id);
    const hSuccess = (data) => {
      if (data) {
        setPoHeader(data);
        setPONum(data[0]["poNumber"] ?? "");
      }
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_Returns}/returnsHeader/getReturnsPreview`,
      "postformdata",
      hSuccess,
      hError,
      formData
    );
  };
  // const fetchMaterialDetails = (code) => {
  //   const hSuccess = (data) => {
  //     if (data.response != "null") setMaterialDetails(data.response[0]);
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(
  //     `/${destination_Po}/Odata/materialCode/code/${code}`,
  //     "get",
  //     hSuccess,
  //     hError
  //   );
  // };

  const [id, setID] = useState("");
  const columns = [
    {
      field: "chartOfAccount",
      headerName: "Chart Of Account",
      editable: false, //text
      flex: 1,
    },
    {
      field: "compCode",
      headerName: "Company Code",
      editable: false, //text
      flex: 1,
    },
    {
      field: "glAccount",
      headerName: "G/L Account",
      editable: false, //text
      flex: 1,
    },
    {
      field: "glAccountType",
      headerName: "G/L Account Type",
      editable: false, //dd
      flex: 1,
    },
    {
      field: "accountGroup",
      headerName: "Account Group",
      editable: false, //text
      flex: 1,
    },
    // {
    //   field: "functionalArea",
    //   headerName: "Functional Area",
    //   editable: false, //dd
    //   flex: 1,
    // },
    // {
    //   field: "glAccountText",
    //   headerName: "G/L Account Text",
    //   editable: false, //dd
    //   flex: 1,
    // },
    {
      field: "groupAccountNumber",
      headerName: "Group Account Number", //dd
      editable: false,
      flex: 1,
    },
    // {
    //   field: "accountCurrency",
    //   headerName: "Account Currency", //dd
    //   editable: false,
    //   flex: 1,
    // },
    // {
    //   field: "taxCategory",
    //   headerName: "Tax Category", //dd
    //   editable: false,
    //   flex: 1,
    // },
    {
      field: "createdBy",
      headerName: "Created By",
      editable: false,
      flex: 1,
    },
    {
      field: "actions",
      align: "center",
      flex: 1, // Use flex for responsive width
      headerAlign: "center",
      headerName: "Actions",
      sortable: false,
      renderCell: (params) => (
        <div>
          <Tooltip title="Extend">
            <IconButton
              aria-label="View Metadata"
              onClick={() => {
                console.log("paramsaction", params.row);
                getCompanyCodeBasedOnChartOfAccountForExtend(
                  params?.row?.chartOfAccount
                );
                handleOpenDialog(params.row);
              }}
            >
              <AddIcon />
            </IconButton>
          </Tooltip>
        </div>
      ),
    },
    // {
    //   field: "changedBy",
    //   headerName: "Changed By",
    //   editable: false,
    //   flex: 1,
    // },
    // {
    //   field: "createdOn",
    //   headerName: "Created On",
    //   editable: false,
    //   flex: 1,
    // },

    // {
    //   field: "action",
    //   headerName: "Action",
    //   sortable: false,
    //   filterable: false,
    //   flex: 1,
    //   align: "center",
    //   headerAlign: "center",
    //   renderCell: (cellValues) => {
    //     return (
    //       <div className={cellValues.row.materialNumber}>
    //         <Tooltip title="View">
    //           <IconButton
    //             sx={iconButton_SpacingSmall}
    //             onClick={() => handleOnClick(cellValues.row.materialNumber)}
    //           >
    //             {ViewDetailsIcon}
    //           </IconButton>
    //         </Tooltip>
    //         {/* <Tooltip title="PO Flow">
    //           <IconButton
    //             sx={iconButton_SpacingSmall}
    //             onClick={() => handleClickOpenPOFlow(cellValues.row.poNum)}
    //           >
    //             <TrackChangesIcon />
    //           </IconButton>
    //         </Tooltip> */}
    //         {/* <Tooltip title="Forward">
    //           <IconButton onClick={handleOpenforwarddialog}>
    //             <ForwardToInboxOutlinedIcon className="wbActionIcon" />
    //           </IconButton>
    //         </Tooltip> */}
    //         {/* <Dialog
    //         hideBackdrop={true}
    //         elevation={2}
    //         PaperProps={{
    //           sx: { boxShadow: "none",minHeight:"8rem"},
    //         }}
    //         open={openforwarddialog}
    //         onClose={handleCloseforwarddialog}>

    //             <Grid
    //             container
    //             sx={{
    //               padding: "0",
    //             }}
    //           >
    //             <Grid item md={11}>
    //               <DialogTitle
    //                 sx={{ fontSize: "1rem", fontWeight: "500" }}
    //               >
    //               Forward Task To
    //               </DialogTitle>
    //               </Grid>
    //               <Grid item md={1}
    //               style={{
    //                 margin: "auto",
    //                 display: "flex",
    //                 justifyContent: "flex-end",
    //                 paddingRight: "1rem",
    //               }}>
    //               <IconButton
    //                 sx={{ padding: "16px", marginLeft: "9rem" }}
    //                 disableRipple
    //               >
    //                 <CloseIcon onClick={handleCloseforwarddialog} />
    //               </IconButton>
    //             </Grid>
    //             </Grid>
    //   <Autocomplete className="autocomplete" sx={{width:"95%", padding:"0px"}}
    //     multiple
    //     id="tags-standard"
    //     options={topEmails}
    //     getOptionLabel={(option) => option.title}
    //     renderInput={(params) => (
    //       <TextField size="small"  sx={{marginLeft:"0.5rem"}}
    //         {...params}
    //         variant="outlined"
    //         placeholder="Search User Name or Email ID"
    //       />
    //     )}
    //   />
    //   </Dialog> */}
    //       </div>
    //     );
    //   },
    // },
  ];
  // console.log("rmsearchform", rmSearchForm);
  const dynamicFilterColumns = selectedOptions
    .map((option) => {
      const field = titleToFieldMapping[option]; // Get the corresponding field from the mapping
      if (!field) {
        return null; // Handle the case when the field doesn't exist in the mapping
      }
      return {
        field: field, // Use the field name from the mapping
        headerName: option,
        editable: false,
        flex: 1,
      };
    })
    .filter((column) => column !== null); // Remove any null columns

  const allColumns = [
    ...columns,
    ...dynamicFilterColumns,
    // Other fixed and dynamic columns as needed
  ];
  const capitalize = (str) => {
    //  str.map((str)=>{
    const arr = str.split(" ");
    for (var i = 0; i < arr.length; i++) {
      arr[i] = arr[i].charAt(0) + arr[i].slice(1).toLowerCase();
    }

    const str2 = arr.join(" ");
    return str2;
    //  })
  };

  useEffect(() => {
    getFilter();
    // functions_PresetFilter.getFilterPresets();
  }, []);
  // useEffect(() => {
  //   if ((rmSearchForm?.company).length) {
  //     getVendorDetails();
  //     getPlantCodeSet()
  //   }
  // }, [rmSearchForm?.company]);

  // let serviceRequestForm_Component = new createServiceRequestForm(Status_ServiceReqForm, setStatus_ServiceReqForm)
  // <-- Function for taking screenshot (Export button) -->
  let ref_elementForExport = useRef(null);
  let exportAsPicture = () => {
    setTimeout(() => {
      captureScreenShot("Material-Single");
    }, 100);
  };

  const handleDownloadTemplate = async () => {
    var downloadPayload = selectedMassChangeRowData.map((x) => {
      return {
        compCode: x.compCode,
        glAccount: x.glAccount,
      };
    });
    console.log("downloadPayload", downloadPayload);
    let hSuccess = (response) => {
      setIsLoading(false);
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");
      link.href = href;
      link.setAttribute("download", `GeneraL Ledger_Mass Change.xls`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
      handleMessageDialogClickOpen();
      setMessageDialogTitle("Success");

      setMessageDialogMessage(
        `GeneraL Ledger_Mass Change.xls has been downloaded successfully`
      );

      setMessageDialogSeverity("success");
    };
    let hError = (error) => {
      if (error.message) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(`${error.message}`);
        setMessageDialogSeverity("danger");
      }
    };
    doAjax(
      `/${destination_GeneralLedger}/excel/downloadExcelWithData`,
      "postandgetblob",
      hSuccess,
      hError,
      downloadPayload
    );
  };
  const handleCreateMultiple = () => {
    setEnableDocumentUpload(true);
    setHandleMassMode("Create");
    dispatch(setHandleMassMode("Create"));
  };

  const handleClick = (option, index) => {
    if (index !== 0) {
      setSelectedIndex(index);
      setOpenButton(false);
      if (index === 1) {
        handleCreateMultiple();
      } else if (index === 2) {
        handleDownloadCreate();
      }
    }
  };
  const handleClickChange = (option, index) => {
    if (index !== 0) {
      setSelectedIndexChange(index);
      setOpenButtonChange(false);
      if (index === 1) {
        handleChangeMultiple();
      } else if (index === 2) {
        handleDownloadTemplate();
      }
    }
  };
  const handleChangeMultiple = () => {
    setEnableDocumentUpload(true);
    dispatch(setHandleMassMode("Change"));
  };
  const handleDownloadCreate = async () => {
    let hSuccess = (response) => {
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");
      link.href = href;
      link.setAttribute("download", `GeneraL Ledger_Mass Create`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
      handleMessageDialogClickOpen();
      setMessageDialogTitle("Success");
      setMessageDialogMessage(
        `General Ledger_Mass Create.xls has been downloaded successfully`
      );
      setMessageDialogSeverity("success");
    };
    let hError = (error) => {
      if (error.message) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(`${error.message}`);
        setMessageDialogSeverity("danger");
      }
    };
    doAjax(
      `/${destination_GeneralLedger}/excel/downloadExcel`,
      "getblobfile",
      hSuccess,
      hError
      // downloadPayload
    );
  };
  const handleChangeDownload = () => {
    var downloadPayload = selectedMassChangeRowData.map((x) => {
      return {
        compCode: x.compCode,
        glAccount: x.glAccount,
      };
    });
    console.log("downloadPayload", downloadPayload);
    let hSuccess = (response) => {
      setIsLoading(false);
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");
      link.href = href;
      link.setAttribute("download", `${name}`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
      handleMessageDialogClickOpen();
      setMessageDialogTitle("Success");

      setMessageDialogMessage(
        `GeneraL Ledger_Mass Change.xls has been downloaded successfully`
      );

      setMessageDialogSeverity("success");
    };
    let hError = (error) => {
      if (error.message) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(`${error.message}`);
        setMessageDialogSeverity("danger");
      }
    };
    doAjax(
      `/${destination_GeneralLedger}/excel/downloadExcelWithData`,
      "postandgetblob",
      hSuccess,
      hError,
      downloadPayload
    );
  };

  const handleCloseButtonCeateMultiple = (event) => {
    if (
      anchorRefChange.current &&
      anchorRefChange.current.contains(event.target)
    ) {
      return;
    }
    //setOpenButtonChange(false);
    setOpenButton((prevOpen) => !prevOpen);
  };
  const getCompanyCodeBasedOnChartOfAccount = (value) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CompanyCode", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getCompanyCode?chartAccount=${value?.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getGroupAccountNumber = (value) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "GroupAccountNumber", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getGroupAccountNumber?chartAccount=${value?.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getAccountCurrency = (value) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "AccountCurrency", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getAccountCurrency?companyCode=${value?.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getTaxCategory = (value) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "TaxCategory", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getTaxCategory?companyCode=${value?.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getAlternativeAccountNumber = (value) => {
    // console.log("alter", value);
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "AlternativeAccountNumber", data: data.body })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getAlternativeAccountNumber?chartAccount=${value?.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getFieldStatusGroup = (value) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "FieldStatusGroup", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getFieldStatusGroup?companyCode=${value?.code}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getAccountGroup = (value) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "AccountGroupDialog", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getAccountGroup?chartAccount=${value?.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getHouseBank = (value) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "HouseBank", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getHouseBank?companyCode=${value?.code}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getCostElementCategory = (value) => {
    // console.log("value",value.code);
    const hSuccess = (data) => {
      // console.log("value",data);
      dispatch(
        setDropDown({ keyName: "CostElementCategory", data: data.body })
      );
    };
    const hError = (error) => {
      console.log(error, "error in dojax");
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getCostElementCategory?accountType=${value?.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCompanyCodeBasedOnChartOfAccountForExtend = (value) => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "CompanyCodeForExtend", data: data.body })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getCompanyCode?chartAccount=${value}`,
      "get",
      hSuccess,
      hError
    );
  };
  const companyCodes = [
    { code: "0001", desc: "Incture Company Code" },
    { code: "1000", desc: "Incture Company Code" },
    { code: "1010", desc: "Company Code 1010" },
    { code: "1710", desc: "Company Code 1710" },
    { code: "2500", desc: "Incture orissa cocd" },
    { code: "AE01", desc: "AE01 & CO" },
    { code: "BBSR", desc: "BBSR & co." },
    { code: "HN10", desc: "Incture Company Code" },
    { code: "I00X", desc: "Industry X - template CC" },
    { code: "JIN1", desc: "Jindal India Pvt Ltd" },
    { code: "PA10", desc: "Panama Digital PVT LTD" },
    { code: "SDS$", desc: "XYZ Chemicals" },
    { code: "SDS4", desc: "XYZC chmicals" },
    { code: "TAAJ", desc: "TAAJ Hotels" },
  ];

  const [selectedCodes, setSelectedCodes] = React.useState([]);

  const handleCheckboxChange = (event) => {
    const code = event.target.value;
    if (event.target.checked) {
      setSelectedCodes((prevSelected) => [...prevSelected, code]);
    } else {
      setSelectedCodes((prevSelected) =>
        prevSelected.filter((item) => item !== code)
      );
    }
  };

  const handleConfirm = () => {
    alert("Selected company codes: " + selectedCodes.join(", "));
  };
  console.log("selectedcodes", selectedCodes);

  const handlePageSizeChange = (event) => {
    const newPageSize = event.target.value;
    setPageSize(newPageSize);
    setPage(0);
    setSkip(0);
  };
  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };
  //for search lookups

  const functions_ExportAsExcel = {
    convertJsonToExcel: () => {
      let excelColumns = [];
      columns.forEach((item) => {
        if (item.headerName.toLowerCase() !== "action" && !item.hide) {
          excelColumns.push({ header: item.headerName, key: item.field });
        }
      });
      saveExcel({
        fileName: `General Ledger Data-${moment(presentDate).format(
          "DD-MMM-YYYY"
        )}`,
        columns: excelColumns,
        rows: rmDataRows,
      });
    },
    button: () => {
      return (
        <Button
          sx={{
            textTransform: "capitalize",
            position: "absolute",
            right: 0,
            top: 0,
          }}
          onClick={() => functions_ExportAsExcel.convertJsonToExcel()}
        >
          Download
        </Button>
      );
    },
  };
  const handleOpenDialog = (rows) => {
    // console.log(rows,"target")
    setOpenDialog(true);
    setTableRows(rows);
    setTableRows((rows) => ({
      ...rows,
      requestType: "Extend",
    }));
    // getListofData(); // Fetch data when the dialog is opened
  };
  const handleCloseDialog = () => {
    setOpenDialog(false);
  };
  const handleApply = () => {
    //   setTableRows(rows => ({
    //     ...rows,
    //     requestType: "extend"
    // }));
    dispatch(setCheckBox(selectedCodes));
    navigate(
      `/masterDataCockpit/generalLedger/displayGeneralLedger/${"1212"}`,
      {
        state: tableRows,
      }
    );
    console.log(tableRows, "tableRows");
  };

  return (
    <div ref={ref_elementForExport}>
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        sx={{
          "&::webkit-scrollbar": {
            width: "1px",
          },
        }}
      >
        <DialogTitle
          sx={{
            justifyContent: "space-between",
            alignItems: "center",
            height: "max-content",
            padding: ".5rem",
            paddingLeft: "1rem",
            backgroundColor: "#EAE9FF40",
            // borderBottom: "1px solid grey",
            display: "flex",
          }}
        >
          <Grid sx={{ display: "block" }}>
            <Typography variant="h6">Select Company Code(s)</Typography>
            <Typography variant="body2" color="#777">
              To extend this General Ledger
            </Typography>
          </Grid>
          <IconButton
            sx={{ width: "max-content" }}
            onClick={handleCloseDialog}
            children={<CloseIcon />}
          />
        </DialogTitle>
        <DialogContent
          sx={{
            padding: ".5rem 1rem",
            maxHeight: 400,
            maxWidth: 400,
            overflowY: "auto",
          }}
        >
          <Grid container>
            {companyCodes.map((code) => (
              <FormControlLabel
                key={code.code}
                control={
                  <Checkbox
                    checked={selectedCodes.includes(code.code)}
                    onChange={handleCheckboxChange}
                    value={code.code}
                  />
                }
                label={`${code.desc} (${code.code})`}
              />
            ))}
          </Grid>
        </DialogContent>
        <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
          <Button
            sx={{ width: "max-content", textTransform: "capitalize" }}
            onClick={handleCloseDialog}
          >
            Cancel
          </Button>
          <Button
            className="button_primary--normal"
            type="save"
            onClick={handleApply}
            variant="contained"
            // disabled={!buttonDisabledForSingleWithoutCopy}
          >
            Apply
          </Button>
        </DialogActions>
      </Dialog>
      <ReusableDialog
        dialogState={openMessageDialog}
        openReusableDialog={handleMessageDialogClickOpen}
        closeReusableDialog={handleMessageDialogClose}
        dialogTitle={messageDialogTitle}
        dialogMessage={messageDialogMessage}
        handleDialogConfirm={handleMessageDialogClose}
        dialogOkText={"OK"}
        // handleExtraButton={handleMessageDialogNavigate}
        dialogSeverity={messageDialogSeverity}
      />
      <div style={{ ...outermostContainer, backgroundColor: "#FAFCFF" }}>
        <Stack spacing={1}>
          {/* Information */}
          <Grid container sx={outermostContainer_Information}>
            <Grid item md={5} sx={outerContainer_Information}>
              <Typography variant="h3">
                <strong>General Ledger</strong>
              </Typography>
              <Typography variant="body2" color="#777">
                This view displays the list of General Ledgers
              </Typography>
            </Grid>
            <Grid item md={7} sx={{ display: "flex" }}>
              <Grid
                container
                direction="row"
                justifyContent="flex-end"
                alignItems="center"
                spacing={0}
              >
                <SearchBar
                  title="Search for multiple General Ledger numbers separated by comma"
                  handleSearchAction={getGlAccountNoGlobalSearch}
                  module="GeneralLedger"
                  keyName="number"
                  message={"Search General Ledger "}
                  clearSearchBar={clearSearchBar}
                />

                {/* <Tooltip title="Table Configuration">
                <IconButton  sx={iconButton_SpacingSmall}>
                  <TuneOutlined />
                </IconButton>
              </Tooltip> */}

                <Tooltip title="Reload">
                  <IconButton sx={iconButton_SpacingSmall}>
                    <Refresh
                      sx={{
                        "&:hover": {
                          transform: "rotate(360deg)",
                          transition: "0.9s",
                        },
                      }}
                      onClick={refreshPage}
                    />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Export">
                  <IconButton sx={iconButton_SpacingSmall}>
                    <IosShare
                      onClick={functions_ExportAsExcel.convertJsonToExcel}
                    />
                  </IconButton>
                </Tooltip>
              </Grid>
            </Grid>
          </Grid>
          <Grid container sx={container_filter}>
            <Grid item md={12}>
              <Accordion className="filter-accordian">
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  aria-controls="panel1a-content"
                  id="panel1a-header"
                  sx={{
                    minHeight: "2rem !important",
                    margin: "0px !important",
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: "700",
                    }}
                  >
                    Search General Ledger
                  </Typography>
                </AccordionSummary>
                <AccordionDetails sx={{ padding: "0.5rem 1rem 0.5rem" }}>
                  <Grid
                    container
                    rowSpacing={1}
                    spacing={2}
                    justifyContent="space-between"
                    alignItems="center"
                    // sx={{ marginBottom: "0.5rem" }}
                  >
                    <Grid
                      container
                      spacing={1}
                      sx={{ padding: "0rem 1rem 0.5rem" }}
                    >
                      <Grid item md={2}>
                        <Typography sx={font_Small}>
                          Chart Of Account
                        </Typography>
                        <FormControl size="small" fullWidth>
                          <Autocomplete
                            sx={{ height: "31px" }}
                            fullWidth
                            size="small"
                            // onClear={handleClearType}
                            value={rmSearchForm?.chartOfAccount}
                            onChange={handleChartOfAccount}
                            // onChange={(e) => handleMatTypeChange(e)}
                            options={dropdownData?.ChartOfAccounts ?? []}
                            getOptionLabel={(option) => {
                              if (option?.code)
                                return (
                                  `${option?.code} - ${option?.desc}` ?? ""
                                );
                              else return "";
                            }}
                            renderOption={(props, option) => (
                              <li {...props}>
                                <Typography style={{ fontSize: 12 }}>
                                  {`${option?.code} - ${option?.desc}`}
                                </Typography>
                              </li>
                            )}
                            renderInput={(params) => (
                              <TextField
                                sx={{ fontSize: "12px !important" }}
                                {...params}
                                variant="outlined"
                                placeholder="Select Chart Of Account"
                              />
                            )}
                          />
                        </FormControl>
                      </Grid>
                      <Grid item md={2}>
                        <Typography sx={font_Small}>Company Code</Typography>
                        <FormControl fullWidth size="small">
                          <Autocomplete
                            sx={{ height: "31px" }}
                            fullWidth
                            size="small"
                            value={rmSearchForm?.companyCode}
                            onChange={handleCompanyCode}
                            // onChange={(e) => handleMatTypeChange(e)}
                            options={dropdownData?.CompanyCodeSearch ?? []}
                            getOptionLabel={(option) => {
                              if (option?.code)
                                return (
                                  `${option?.code} - ${option?.desc}` ?? ""
                                );
                              else return "";
                            }}
                            renderOption={(props, option) => (
                              <li {...props}>
                                <Typography style={{ fontSize: 12 }}>
                                  {`${option?.code} - ${option?.desc}`}
                                </Typography>
                              </li>
                            )}
                            renderInput={(params) => (
                              <TextField
                                sx={{ fontSize: "12px !important" }}
                                {...params}
                                variant="outlined"
                                placeholder="Select Company Code"
                              />
                            )}
                          />
                        </FormControl>
                      </Grid>
                      <Grid item md={2}>
                        <Typography sx={font_Small}>G/L Account</Typography>
                        <FormControl size="small" fullWidth>
                          <Autocomplete
                            sx={{ height: "31px" }}
                            fullWidth
                            size="small"
                            // onClear={handleClearType}
                            value={rmSearchForm?.glAccount}
                            onChange={handleGLAccount}
                            // onChange={(e) => handleMatTypeChange(e)}
                            options={dropdownData?.GLAccountForSearch ?? []}
                            getOptionLabel={(option) => {
                              if (option?.code)
                                return (
                                  `${option?.code} - ${option?.desc}` ?? ""
                                );
                              else return "";
                            }}
                            renderOption={(props, option) => (
                              <li {...props}>
                                <Typography style={{ fontSize: 12 }}>
                                  {`${option?.code} - ${option?.desc}`}
                                </Typography>
                              </li>
                            )}
                            renderInput={(params) => (
                              <TextField
                                sx={{ fontSize: "12px !important" }}
                                {...params}
                                variant="outlined"
                                placeholder="Select Chart Of Account"
                              />
                            )}
                          />
                        </FormControl>
                      </Grid>
                      <Grid item md={2}>
                        <Typography sx={font_Small}>
                          G/L Account Type
                        </Typography>
                        <FormControl size="small" fullWidth>
                          <Autocomplete
                            sx={{ height: "31px" }}
                            fullWidth
                            size="small"
                            // onClear={handleClearType}
                            value={rmSearchForm?.glAccountType}
                            onChange={handleGlAccountType}
                            // onChange={(e) => handleMatTypeChange(e)}
                            options={dropdownData?.AccountType ?? []}
                            getOptionLabel={(option) => {
                              if (option?.code)
                                return (
                                  `${option?.code} - ${option?.desc}` ?? ""
                                );
                              else return "";
                            }}
                            renderOption={(props, option) => (
                              <li {...props}>
                                <Typography style={{ fontSize: 12 }}>
                                  {`${option?.code} - ${option?.desc}`}
                                </Typography>
                              </li>
                            )}
                            renderInput={(params) => (
                              <TextField
                                sx={{ fontSize: "12px !important" }}
                                {...params}
                                variant="outlined"
                                placeholder="Select G/L Account Type"
                              />
                            )}
                          />
                        </FormControl>
                      </Grid>
                      <Grid item md={2}>
                        <Typography sx={font_Small}>Account Group</Typography>
                        <FormControl size="small" fullWidth>
                          <Autocomplete
                            sx={{ height: "31px" }}
                            fullWidth
                            size="small"
                            // onClear={handleClearType}
                            value={rmSearchForm?.accountGroup}
                            onChange={handleAccountGroup}
                            // onChange={(e) => handleMatTypeChange(e)}
                            options={dropdownData?.AccountGroupSearch ?? []}
                            getOptionLabel={(option) => {
                              if (option?.code)
                                return (
                                  `${option?.code} - ${option?.desc}` ?? ""
                                );
                              else return "";
                            }}
                            renderOption={(props, option) => (
                              <li {...props}>
                                <Typography style={{ fontSize: 12 }}>
                                  {`${option?.code} - ${option?.desc}`}
                                </Typography>
                              </li>
                            )}
                            renderInput={(params) => (
                              <TextField
                                sx={{ fontSize: "12px !important" }}
                                {...params}
                                variant="outlined"
                                placeholder="Select Account Group"
                              />
                            )}
                          />
                        </FormControl>
                      </Grid>
                      {/* <Grid item md={2}>
                        <Typography sx={font_Small}>Functional Area</Typography>
                        <TextField
                          sx={{ fontSize: "12px !important" }}
                          size="small"
                          fullWidth
                          onChange={handleFunctionalArea}
                          placeholder="Enter Functional Area"
                          value={rmSearchForm?.description}
                        />
                      </Grid> */}
                      <Grid item md={2}>
                        <Typography sx={font_Small}>Short Text</Typography>
                        <FormControl fullWidth size="small">
                          <TextField
                            sx={{ fontSize: "12px !important" }}
                            size="small"
                            fullWidth
                            onChange={handleShortText}
                            placeholder="Enter Short Text"
                            value={rmSearchForm?.shortText}
                          />
                          {/* <Select
                            placeholder={"Select Material Group"}
                            select
                            sx={{font_Small, height:"31px", fontSize:"12px", width:"100%"}}
                            size="small"
                            value={matGroup}
                            name={matGroup}
                            onChange={(e) => handleMatGroupChange(e)}
                            displayEmpty={true}
                          // disabled={Object.keys(props?.filterData ?? {})?.length < 2}
                          >
                            <MenuItem sx={font_Small} value={""}>
                              <div style={{ color: "#C1C1C1" }}>Select Material Group { } </div>
                            </MenuItem>
                            {Object.keys(matGroupData).map((key) => (
                              <MenuItem value={key} key={key}>
                                {key + " - " + matGroupData[key]}
                              </MenuItem>
                            ))}
                          </Select> */}
                        </FormControl>
                      </Grid>
                      <Grid item md={2}>
                        <Typography sx={font_Small}>
                          Group Account Number
                        </Typography>
                        <FormControl fullWidth size="small">
                          <Autocomplete
                            sx={{ height: "31px" }}
                            fullWidth
                            size="small"
                            value={rmSearchForm?.groupAccountNumber}
                            onChange={handleGroupAccountNumber}
                            // onChange={(e) => handleMatTypeChange(e)}
                            options={
                              dropdownData?.GroupAccountNumberSearch ?? []
                            }
                            getOptionLabel={(option) => {
                              if (option?.code)
                                return (
                                  `${option?.code} - ${option?.desc}` ?? ""
                                );
                              else return "";
                            }}
                            renderOption={(props, option) => (
                              <li {...props}>
                                <Typography style={{ fontSize: 12 }}>
                                  {`${option?.code} - ${option?.desc}`}
                                </Typography>
                              </li>
                            )}
                            renderInput={(params) => (
                              <TextField
                                sx={{ fontSize: "12px !important" }}
                                {...params}
                                variant="outlined"
                                placeholder="Select Group Account Number"
                              />
                            )}
                          />
                        </FormControl>
                      </Grid>
                      {/* <Grid item md={2}>
                        <Typography sx={font_Small}>
                          Account Currency
                        </Typography>
                        <FormControl fullWidth size="small">
                          <Autocomplete
                            sx={{ height: "31px" }}
                            fullWidth
                            size="small"
                            onChange={handleAccountCurrency}
                            // onChange={(e) => handleMatTypeChange(e)}
                            options={plantCodeSet ?? []}
                            getOptionLabel={(option) => {
                              if (option?.code)
                                return (
                                  `${option?.code} - ${option?.desc}` ?? ""
                                );
                              else return "";
                            }}
                            value={rmSearchForm?.plant}
                            renderOption={(props, option) => (
                              <li {...props}>
                                <Typography style={{ fontSize: 12 }}>
                                  {`${option?.code} - ${option?.desc}`}
                                </Typography>
                              </li>
                            )}
                            renderInput={(params) => (
                              <TextField
                                sx={{ fontSize: "12px !important" }}
                                {...params}
                                variant="outlined"
                                placeholder="Select Account Currency"
                              />
                            )}
                          />
                        </FormControl>
                      </Grid> */}
                      {/* <Grid item md={2}>
                        <Typography sx={font_Small}>Tax Category</Typography>
                        <FormControl fullWidth size="small">
                          <Autocomplete
                            sx={{ height: "31px" }}
                            fullWidth
                            size="small"
                            onChange={handleTaxCategory}
                            // onChange={(e) => handleMatTypeChange(e)}
                            options={plantCodeSet ?? []}
                            getOptionLabel={(option) => {
                              if (option?.code)
                                return (
                                  `${option?.code} - ${option?.desc}` ?? ""
                                );
                              else return "";
                            }}
                            value={rmSearchForm?.plant}
                            renderOption={(props, option) => (
                              <li {...props}>
                                <Typography style={{ fontSize: 12 }}>
                                  {`${option?.code} - ${option?.desc}`}
                                </Typography>
                              </li>
                            )}
                            renderInput={(params) => (
                              <TextField
                                sx={{ fontSize: "12px !important" }}
                                {...params}
                                variant="outlined"
                                placeholder="Select Tax Category"
                              />
                            )}
                          />
                        </FormControl>
                      </Grid> */}
                      <Grid item md={2}>
                        <Typography sx={font_Small}>Created By</Typography>
                        <TextField
                          sx={{ fontSize: "12px !important" }}
                          fullWidth
                          size="small"
                          value={rmSearchForm?.createdBy}
                          onChange={handleCreatedBy}
                          placeholder="Enter Created By"
                        />
                      </Grid>
                      {/* <Grid item md={2}>
                        <Typography sx={font_Small}>Changed By</Typography>
                        <TextField
                          sx={{ fontSize: "12px !important" }}
                          fullWidth
                          size="small"
                          value={rmSearchForm?.changedBy}
                          onChange={handleChangedBy}
                          placeholder="Enter Changed By"
                        />
                      </Grid> */}
                      {/* <Grid item md={2}>
                        <Typography sx={font_Small}>Created On</Typography>
                        <FormControl fullWidth sx={{ padding: 0 }}>
                          <LocalizationProvider dateAdapter={AdapterDateFns}>
                            <DateRange
                              onChange={(e) => handleDate(e)}
                              // onChange={(e) => handleMatTypeChange(e)
                              value={rmSearchForm?.createdOn}
                            />
                            
                          </LocalizationProvider>
                        </FormControl>
                      </Grid> */}
                      {/* dynamic filter// */}
                      <Grid item md={2}>
                        <Typography sx={font_Small}>Add New Filters</Typography>
                        <FormControl>
                          <Select
                            sx={{
                              font_Small,
                              height: "31px",
                              fontSize: "12px",
                              width: "200px",
                            }}
                            // fullWidth
                            size="small"
                            multiple
                            limitTags={2}
                            value={selectedOptions}
                            onChange={handleSelection}
                            renderValue={(selected) => selected.join(", ")}
                            MenuProps={{
                              MenuProps,
                            }}
                            endAdornment={
                              selectedOptions.length > 0 && (
                                <InputAdornment position="end">
                                  <IconButton
                                    size="small"
                                    onClick={() => setSelectedOptions([])}
                                    aria-label="Clear selections"
                                  >
                                    <ClearIcon />
                                  </IconButton>
                                </InputAdornment>
                              )
                            }
                          >
                            {items.map((option) => (
                              <MenuItem key={option.title} value={option.title}>
                                <Checkbox
                                  checked={
                                    selectedOptions.indexOf(option.title) > -1
                                  }
                                />
                                {option.title}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                        <Grid
                          style={{
                            display: "flex",
                            justifyContent: "space-around",
                          }}
                        ></Grid>
                      </Grid>
                    </Grid>
                    <Grid
                      container
                      sx={{ flexDirection: "row", padding: "0rem 1rem 0.5rem" }}
                      gap={1}
                    >
                      {selectedOptions.map((option, i) => {
                        // if (option !== "Created Date" || option !== "Plant") {
                        return (
                          <Grid item>
                            <Stack>
                              <Typography sx={{ fontSize: "12px" }}>
                                {option}
                              </Typography>
                              <Autocomplete
                                sx={font_Small}
                                size="small"
                                key={option[i]}
                                options={dynamicOptions ?? []}
                                getOptionLabel={(option, i) =>
                                  `${option[i]?.code} - ${option[i]?.desc}`
                                }
                                placeholder={`Enter ${option}`}
                                value={filterFieldData[option]}
                                onChange={(event, newValue) =>
                                  setFilterFieldData({
                                    ...filterFieldData,
                                    [option]: newValue,
                                  })
                                }
                                renderInput={(params) => (
                                  <TextField
                                    sx={{ fontSize: "12px !important" }}
                                    {...params}
                                    size="small"
                                    placeholder={`Enter ${option}`}
                                    variant="outlined"
                                    // sx={font_Small}
                                  />
                                )}
                              />
                            </Stack>
                          </Grid>
                        );
                        // } if (option === "Created Date") {
                        //   return (
                        //     <Stack>

                        //       <DatePicker/>

                        //     </Stack>
                        //   )
                        // }
                      })}
                      {/* //clear search button according to UX */}
                      {/* <Grid
                          item
                          style={{
                            width:"100%",
                            display: "flex",
                            justifyContent: "space-around",
                          }}
                        >
                          <Button
                            
                            sx={{ fontSize: "12px",  width:"129px", backgroundColor:" #7575751A", color:"#757575" }}
                            onClick={handleClear}
                          >
                            Clear
                          </Button>

                          <Button
                            
                            sx={{ ...button_Marginleft, fontSize: "12px",  width:"129px", backgroundColor:"#F7F5FF"  }}
                          // onClick={getFilter}
                          >
                            Search
                          </Button>
                        </Grid> */}
                    </Grid>
                  </Grid>
                  <Grid
                    container
                    style={{
                      display: "flex",
                      justifyContent: "flex-end",
                    }}
                  >
                    <Grid
                      item
                      style={{
                        display: "flex",
                        justifyContent: "space-around",
                      }}
                    >
                      <Button
                        variant="outlined"
                        sx={button_Outlined}
                        onClick={handleClear}
                      >
                        Clear
                      </Button>
                      {/* <PresetV3
                        anchorEl={anchorEl_Preset}
                        setAnchorEl={setAnchorEl}
                        open={openAnchor}
                        handleClose={handleClose_Preset}
                        presets={presets}
                        setPresets={setPresets}
                        presetName={presetName}
                        setPresetName={setPresetName}
                        deletePreset={functions_PresetFilter.deletePreset}
                        saveFilterPreset={functions_PresetFilter.saveFilterPreset}
                        setPresetFilter={functions_PresetFilter.setPresetFilter}
                        setFilterDefault={functions_PresetFilter.setFilterDefault}
                        handleSearch={() => { }}
                      /> */}
                      {/* <PresetV3
                        moduleName={"ReturnOrder"}
                        handleSearch={getFilter}
                        PresetMethod={PresetMethod}

                        PresetObj={PresetObj}
                      /> */}

                      <Button
                        variant="contained"
                        sx={{ ...button_Primary, ...button_Marginleft }}
                        onClick={getFilter}
                      >
                        Search
                      </Button>
                    </Grid>
                  </Grid>
                </AccordionDetails>
              </Accordion>
            </Grid>
          </Grid>

          <Grid item sx={{ position: "relative" }}>
            <Stack>
              <ReusableTable
                isLoading={tableLoading}
                module={"GeneralLedger"}
                width="100%"
                title={"List of General Ledgers (" + count + ")"}
                rows={rmDataRows}
                columns={allColumns}
                page={page}
                pageSize={pageSize}
                rowCount={count ?? rmDataRows?.length ?? 0}
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                getRowIdValue={"id"}
                hideFooter={true}
                checkboxSelection={true}
                disableSelectionOnClick={true}
                status_onRowSingleClick={true}
                onRowsSelectionHandler={handleSelectionModelChange}
                callback_onRowDoubleClick={(params) => {
                  const generalLedger = params.row.glAccount; // Adjust this based on your data structure
                  navigate(
                    `/masterDataCockpit/generalLedger/displayGeneralLedger/${generalLedger}`,
                    {
                      state: params.row,
                    }
                  );
                }}
                // setShowWork={setShowWork}
                stopPropagation_Column={"action"}
                status_onRowDoubleClick={true}
                showCustomNavigation={true}
              />
              {/* {viewDetailpage && <SingleMaterialDetail />} */}
            </Stack>
          </Grid>
          {/* {
            showBtmNav && */}
            {checkIwaAccess(iwaAccessData, "General Ledger", "CreateGL") && 
          userData?.role === "Super User" ||  userData?.role === "Finance" ? 
          (
          <Paper
            sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
            elevation={2}
          >
            <BottomNavigation
              className="container_BottomNav"
              showLabels
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                gap: 1,
              }}
              value={value}
              onChange={(newValue) => {
                setValue(newValue);
              }}
            >
              <ButtonGroup
                variant="contained"
                ref={anchorRefCreate}
                aria-label="split button"
              >
                <Button
                  size="small"
                  variant="contained"
                  // sx={{ cursor: "default" }}
                  onClick={() => handleClickCreate(optionsCreateSingle[0], 0)}
                  // onClick={handleDialogClickOpen}
                >
                  {optionsCreateSingle[0]}
                </Button>
                <Button
                  size="small"
                  aria-controls={
                    openButtonCreate ? "split-button-menu" : undefined
                  }
                  aria-expanded={openButtonCreate ? "true" : undefined}
                  aria-label="select action"
                  aria-haspopup="menu"
                  onClick={handleToggleCreate}
                >
                  <ReusableIcon
                    iconName={"ArrowDropUp"}
                    iconColor={"#FFFFFF"}
                  />
                </Button>
              </ButtonGroup>
              <Popper
                sx={{
                  zIndex: 1,
                }}
                open={openButtonCreate}
                anchorEl={anchorRefCreate.current}
                placement={"top-end"}
                // transition
              >
                <Paper style={{ width: anchorRefCreate.current?.clientWidth }}>
                  <ClickAwayListener
                    onClickAway={handleDialogCloseCreateSingle}
                  >
                    <MenuList id="split-button-menu" autoFocusItem>
                      {optionsCreateSingle.slice(1).map((option, index) => (
                        <MenuItem
                          // autoFocusItem
                          key={option}
                          selected={index === selectedIndexCreate - 1}
                          onClick={() => handleClickCreate(option, index + 1)}
                        >
                          {option}
                        </MenuItem>
                      ))}
                    </MenuList>
                  </ClickAwayListener>
                </Paper>
              </Popper>

              <Dialog
                open={dialogOpen}
                onClose={handleDialogClose}
                sx={{
                  "&::webkit-scrollbar": {
                    width: "1px",
                  },
                }}
              >
                <DialogTitle
                  sx={{
                    justifyContent: "space-between",
                    alignItems: "center",
                    height: "max-content",
                    padding: ".5rem",
                    paddingLeft: "1rem",
                    backgroundColor: "#EAE9FF40",
                    // borderBottom: "1px solid grey",
                    display: "flex",
                  }}
                >
                  <Typography variant="h6">New General Ledger</Typography>

                  <IconButton
                    sx={{ width: "max-content" }}
                    onClick={handleDialogClose}
                    children={<CloseIcon />}
                  />
                </DialogTitle>
                <DialogContent sx={{ padding: ".5rem 1rem" }}>
                  <Grid container spacing={3}>
                    <Grid
                      item
                      md={6}
                      sx={{ width: "100%", marginTop: ".5rem" }}
                    >
                      <Typography>
                        Chart Of Accounts
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                      <FormControl
                        fullWidth
                        sx={{ margin: ".5em 0px", minWidth: "250px" }}
                      >
                        <Autocomplete
                          sx={{ height: "42px" }}
                          required="true"
                          size="small"
                          onChange={(e, value) => {
                            setNewChartOfAccount(value);
                            getCompanyCodeBasedOnChartOfAccount(value);
                            getGroupAccountNumber(value);
                            getAlternativeAccountNumber(value);
                            getAccountGroup(value);
                          }}
                          options={dropdownData?.ChartOfAccounts ?? []}
                          getOptionLabel={(option) =>
                            `${option?.code} - ${option?.desc}`
                          }
                          // value={rmSearchForm?.plant}
                          renderOption={(props, option) => (
                            <li {...props}>
                              <Typography style={{ fontSize: 12 }}>
                                {option?.code} - {option?.desc}
                              </Typography>
                            </li>
                          )}
                          error={newChartOfAccount === "" ? true : false}
                          renderInput={(params) => (
                            <TextField
                              sx={{ fontSize: "12px !important" }}
                              {...params}
                              variant="outlined"
                              placeholder="SELECT CHART OF ACCOUNTS"
                            />
                          )}
                        />
                      </FormControl>
                    </Grid>
                    <Grid
                      item
                      md={6}
                      sx={{ width: "100%", marginTop: ".5rem" }}
                    >
                      <Typography>
                        Company Code
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                      <FormControl
                        fullWidth
                        sx={{ margin: ".5em 0px", minWidth: "250px" }}
                      >
                        <Autocomplete
                          sx={{ height: "42px" }}
                          required="true"
                          size="small"
                          onChange={(e, value) => {
                            setNewCompanyCode(value);
                            getAccountCurrency(value);
                            getTaxCategory(value);
                            getFieldStatusGroup(value);
                            getHouseBank(value);
                          }}
                          options={dropdownData?.CompanyCode ?? []}
                          getOptionLabel={(option) =>
                            `${option?.code} - ${option?.desc}`
                          }
                          // value={rmSearchForm?.plant}
                          renderOption={(props, option) => (
                            <li {...props}>
                              <Typography style={{ fontSize: 12 }}>
                                {option?.code} - {option?.desc}
                              </Typography>
                            </li>
                          )}
                          error={newCompanyCode === "" ? true : false}
                          renderInput={(params) => (
                            <TextField
                              sx={{ fontSize: "12px !important" }}
                              {...params}
                              variant="outlined"
                              placeholder="SELECT COMPANY CODE"
                            />
                          )}
                        />
                      </FormControl>
                    </Grid>
                    <Grid
                      item
                      md={6}
                      sx={{ width: "100%", marginTop: ".5rem" }}
                    >
                      <Typography>
                        Account Type
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                      <FormControl
                        fullWidth
                        sx={{ margin: ".5em 0px", minWidth: "250px" }}
                      >
                        <Autocomplete
                          sx={{ height: "42px" }}
                          required="true"
                          size="small"
                          onChange={(e, value) => {
                            setNewAccountType(value);
                            getCostElementCategory(value);
                          }}
                          options={dropdownData?.AccountType ?? []}
                          getOptionLabel={(option) =>
                            `${option?.code} - ${option?.desc}`
                          }
                          // value={rmSearchForm?.plant}
                          renderOption={(props, option) => (
                            <li {...props}>
                              <Typography style={{ fontSize: 12 }}>
                                {option?.code} - {option?.desc}
                              </Typography>
                            </li>
                          )}
                          error={newAccountGroup === "" ? true : false}
                          renderInput={(params) => (
                            <TextField
                              sx={{ fontSize: "12px !important" }}
                              {...params}
                              variant="outlined"
                              placeholder="SELECT ACCOUNT TYPE"
                            />
                          )}
                        />
                      </FormControl>
                    </Grid>
                    <Grid
                      item
                      md={6}
                      sx={{ width: "100%", marginTop: ".5rem" }}
                    >
                      <Typography>
                        Account Group
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                      <FormControl
                        fullWidth
                        sx={{ margin: ".5em 0px", minWidth: "250px" }}
                      >
                        <Autocomplete
                          sx={{ height: "42px" }}
                          required="true"
                          size="small"
                          onChange={(e, value) => {
                            setNewAccountGroup(value);
                          }}
                          options={dropdownData?.AccountGroupDialog ?? []}
                          getOptionLabel={(option) =>
                            `${option?.AccountGroup} - ${option?.Description}`
                          }
                          // value={rmSearchForm?.plant}
                          renderOption={(props, option) => (
                            <li {...props}>
                              <Typography style={{ fontSize: 12 }}>
                                {option?.AccountGroup} - {option?.Description}
                              </Typography>
                            </li>
                          )}
                          error={newAccountGroup === "" ? true : false}
                          renderInput={(params) => (
                            <TextField
                              sx={{ fontSize: "12px !important" }}
                              {...params}
                              variant="outlined"
                              placeholder="SELECT ACCOUNT GROUP"
                            />
                          )}
                        />
                      </FormControl>
                    </Grid>

                    <Grid
                      item
                      md={6}
                      sx={{
                        width: "100%",
                        marginTop: ".5rem",
                        marginRight: "5rem",
                      }}
                    >
                      <Typography>
                        GL Account
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                      <FormControl
                        fullWidth
                        sx={{ margin: ".5em 0px", minWidth: "250px" }}
                      >
                        {/* <Tooltip title="Please enter GL Account between Assigned Range" describeChild > */}
                        <TextField
                          sx={{ fontSize: "12px !important", height: "40px" }}
                          fullWidth
                          size="small"
                          value={newGLAccount}
                          onChange={(e) => {
                            let glAccountValue = e.target.value;
                            if (
                              glAccountValue.length > 0 &&
                              glAccountValue[0] === " "
                            ) {
                              setNewGLAccount(glAccountValue.trimStart());
                            } else {
                              let glAccountUpperCase =
                                glAccountValue.toUpperCase();
                              setNewGLAccount(glAccountUpperCase);
                            }
                          }}
                          inputProps={{
                            maxLength: 10,
                            style: { textTransform: "uppercase" },
                          }}
                          placeholder={`${
                            newAccountGroup
                              ? newAccountGroup?.FromAcct
                              : "Enter GL Account"
                          } - ${
                            newAccountGroup ? newAccountGroup?.ToAcct : ""
                          }`}
                          // error={newCostCenterName === "" ? true : false}
                          required={true}
                        />
                        {/* </Tooltip> */}
                        {newGeneralLedgerValid && (
                          <Typography color="red">
                            *General Ledger must be 10 digits
                          </Typography>
                        )}
                        {numberRangeError && (
                          <Typography color="red">
                            *Please Enter GL Account Number within Assigned
                            Range
                          </Typography>
                        )}
                      </FormControl>
                    </Grid>
                  </Grid>
                  {isValidationError && (
                    <Grid>
                      <Typography style={{ color: "red" }}>
                        *Please Enter Mandatory Fields
                      </Typography>
                    </Grid>
                  )}
                  {checkValidationGeneralLedger && (
                    <Grid>
                      <Typography style={{ color: "red" }}>
                        *The GL Account with Company Code already exists. Please
                        enter different GL Account or Company Code
                      </Typography>
                    </Grid>
                  )}
                </DialogContent>

                <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
                  <Button
                    sx={{ width: "max-content", textTransform: "capitalize" }}
                    onClick={handleDialogClose}
                  >
                    Cancel
                  </Button>
                  <Button
                    className="button_primary--normal"
                    type="save"
                    onClick={handleDialogProceed}
                    variant="contained"
                  >
                    Proceed
                  </Button>
                </DialogActions>
              </Dialog>

              <Dialog
                open={dialogOpenCreate}
                onClose={handleDialogCloseCreate}
                sx={{
                  "&::webkit-scrollbar": {
                    width: "1px",
                  },
                }}
              >
                <DialogTitle
                  sx={{
                    justifyContent: "space-between",
                    alignItems: "center",
                    height: "max-content",
                    padding: ".5rem",
                    paddingLeft: "1rem",
                    backgroundColor: "#EAE9FF40",
                    // borderBottom: "1px solid grey",
                    display: "flex",
                  }}
                >
                  <Typography variant="h6">New General Ledger</Typography>

                  <IconButton
                    sx={{ width: "max-content" }}
                    onClick={handleDialogCloseCreate}
                    children={<CloseIcon />}
                  />
                </DialogTitle>
                <DialogContent sx={{ padding: ".5rem 1rem" }}>
                  <Grid container spacing={3}>
                    <Grid
                      item
                      md={6}
                      sx={{ width: "100%", marginTop: ".5rem" }}
                    >
                      <Typography>
                        Chart Of Accounts
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                      <FormControl
                        fullWidth
                        sx={{ margin: ".5em 0px", minWidth: "250px" }}
                      >
                        <Autocomplete
                          sx={{ height: "42px" }}
                          required="true"
                          size="small"
                          onChange={(e, value) => {
                            setNewChartOfAccount(value);
                            getCompanyCodeBasedOnChartOfAccount(value);
                            getGroupAccountNumber(value);
                            getAlternativeAccountNumber(value);
                            getAccountGroup(value);
                          }}
                          options={dropdownData?.ChartOfAccounts ?? []}
                          getOptionLabel={(option) =>
                            `${option?.code} - ${option?.desc}`
                          }
                          // value={rmSearchForm?.plant}
                          renderOption={(props, option) => (
                            <li {...props}>
                              <Typography style={{ fontSize: 12 }}>
                                {option?.code} - {option?.desc}
                              </Typography>
                            </li>
                          )}
                          error={newChartOfAccount === "" ? true : false}
                          renderInput={(params) => (
                            <TextField
                              sx={{ fontSize: "12px !important" }}
                              {...params}
                              variant="outlined"
                              placeholder="SELECT CHART OF ACCOUNTS"
                            />
                          )}
                        />
                      </FormControl>
                    </Grid>
                    <Grid
                      item
                      md={6}
                      sx={{ width: "100%", marginTop: ".5rem" }}
                    >
                      <Typography>
                        Company Code
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                      <FormControl
                        fullWidth
                        sx={{ margin: ".5em 0px", minWidth: "250px" }}
                      >
                        <Autocomplete
                          sx={{ height: "42px" }}
                          required="true"
                          size="small"
                          onChange={(e, value) => {
                            setNewCompanyCode(value);
                            getAccountCurrency(value);
                            getTaxCategory(value);
                            getFieldStatusGroup(value);
                            getHouseBank(value);
                          }}
                          options={dropdownData?.CompanyCode ?? []}
                          getOptionLabel={(option) =>
                            `${option?.code} - ${option?.desc}`
                          }
                          // value={rmSearchForm?.plant}
                          renderOption={(props, option) => (
                            <li {...props}>
                              <Typography style={{ fontSize: 12 }}>
                                {option?.code} - {option?.desc}
                              </Typography>
                            </li>
                          )}
                          error={newCompanyCode === "" ? true : false}
                          renderInput={(params) => (
                            <TextField
                              sx={{ fontSize: "12px !important" }}
                              {...params}
                              variant="outlined"
                              placeholder="SELECT COMPANY CODE"
                            />
                          )}
                        />
                      </FormControl>
                    </Grid>
                    <Grid
                      item
                      md={6}
                      sx={{
                        width: "100%",
                        marginTop: ".5rem",
                        marginRight: "5rem",
                      }}
                    >
                      <Typography>
                        GL Account
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                      <FormControl
                        fullWidth
                        sx={{ margin: ".5em 0px", minWidth: "250px" }}
                      >
                        <TextField
                          sx={{ fontSize: "12px !important", height: "40px" }}
                          fullWidth
                          // maxLength={6}
                          // minLength={5}
                          size="small"
                          value={newGLAccount}
                          onChange={(e) => {
                            let glAccountValue = e.target.value;
                            if (
                              glAccountValue.length > 0 &&
                              glAccountValue[0] === " "
                            ) {
                              setNewGLAccount(glAccountValue.trimStart());
                            } else {
                              let glAccountUpperCase =
                                glAccountValue.toUpperCase();
                              setNewGLAccount(glAccountUpperCase);
                            }
                          }}
                          inputProps={{
                            maxLength: 10,
                            style: { textTransform: "uppercase" },
                          }}
                          placeholder="ENTER GL ACCOUNT"
                          required={true}
                        />
                        {newGeneralLedgerValid && (
                          <Typography color="red">
                            *General Ledger must be 10 digits
                          </Typography>
                        )}
                      </FormControl>
                    </Grid>

                    <Divider sx={{ width: "100%", marginLeft: "2%" }}>
                      <b>Copy From</b>
                    </Divider>
                    <Grid
                      item
                      md={6}
                      sx={{ width: "100%", marginTop: ".5rem" }}
                    >
                      <Typography>
                        Company Code
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                      <FormControl
                        fullWidth
                        sx={{ margin: ".5em 0px", minWidth: "250px" }}
                      >
                        <Autocomplete
                          sx={{ height: "42px" }}
                          required="true"
                          size="small"
                          onChange={(e, value) => {
                            // console.log("companycode3232", value);
                            setNewCompanyCodeToCopyFrom(value);
                            getGlAccountBasedOnCompanyCode(value);
                            getAccountCurrency(value);
                            getTaxCategory(value);
                            getFieldStatusGroup(value);
                            getHouseBank(value);
                          }}
                          options={dropdownData?.CompanyCode ?? []}
                          getOptionLabel={(option) =>
                            `${option?.code} - ${option?.desc}`
                          }
                          // value={rmSearchForm?.plant}
                          renderOption={(props, option) => (
                            <li {...props}>
                              <Typography style={{ fontSize: 12 }}>
                                {option?.code} - {option?.desc}
                              </Typography>
                            </li>
                          )}
                          error={newCompanyCodeToCopyFrom === "" ? true : false}
                          renderInput={(params) => (
                            <TextField
                              sx={{ fontSize: "12px !important" }}
                              {...params}
                              variant="outlined"
                              placeholder="SELECT COMPANY CODE"
                            />
                          )}
                        />
                      </FormControl>
                    </Grid>
                    <Grid
                      item
                      md={6}
                      sx={{
                        width: "100%",
                        marginTop: ".5rem",
                      }}
                    >
                      <Typography>
                        GL Account
                        <span style={{ color: "red" }}>*</span>
                      </Typography>

                      <FormControl
                        fullWidth
                        sx={{
                          margin: ".5em 0px",
                          minWidth: "250px",
                          flexDirection: "row",
                        }}
                      >
                        <Grid md={12}>
                          <Autocomplete
                            sx={{ height: "42px" }}
                            required="true"
                            size="small"
                            onChange={(e, value) => {
                              setNewGLAccountCopyFrom(value);
                            }}
                            options={dropdownData?.GlAccountCompCode ?? []}
                            getOptionLabel={(option) =>
                              `${option?.code}-${option?.desc}`
                            }
                            // value={rmSearchForm?.plant}
                            renderOption={(props, option) => (
                              <li {...props}>
                                <Typography style={{ fontSize: 12 }}>
                                  {`${option?.code}-${option?.desc}`}
                                </Typography>
                              </li>
                            )}
                            // error={newControllingArea === "" ? true : false}
                            renderInput={(params) => (
                              <TextField
                                sx={{ fontSize: "12px !important" }}
                                {...params}
                                variant="outlined"
                                placeholder="SELECT GL ACCOUNT"
                              />
                            )}
                          />
                        </Grid>
                      </FormControl>
                    </Grid>
                  </Grid>

                  {isValidationErrorwithCopy && (
                    <Grid>
                      <Typography style={{ color: "red" }}>
                        *Please Enter Mandatory Fields
                      </Typography>
                    </Grid>
                  )}
                  {checkValidationGeneralLedger && (
                    <Grid>
                      <Typography style={{ color: "red" }}>
                        *The GL Account with Company Code already exists. Please
                        enter different GL Account or Company Code
                      </Typography>
                    </Grid>
                  )}
                </DialogContent>

                <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
                  <Button
                    sx={{ width: "max-content", textTransform: "capitalize" }}
                    onClick={handleDialogCloseCreate}
                  >
                    Cancel
                  </Button>
                  <Button
                    className="button_primary--normal"
                    type="save"
                    onClick={handleDialogProceedWithCopy}
                    variant="contained"
                  >
                    Proceed
                  </Button>
                </DialogActions>
              </Dialog>

              <ButtonGroup
                variant="contained"
                ref={anchorRef}
                aria-label="split button"
              >
                <Button
                  size="small"
                  onClick={() => handleClick(options[0], 0)}
                  sx={{ cursor: "default" }}
                >
                  {options[0]}
                </Button>
                <Button
                  size="small"
                  aria-controls={openButton ? "split-button-menu" : undefined}
                  aria-expanded={openButton ? "true" : undefined}
                  aria-label="select action"
                  aria-haspopup="menu"
                  onClick={handleToggle}
                >
                  <ReusableIcon
                    iconName={"ArrowDropUp"}
                    iconColor={"#FFFFFF"}
                  />
                </Button>
              </ButtonGroup>
              <Popper
                sx={{
                  zIndex: 1,
                }}
                open={openButton}
                anchorEl={anchorRef.current}
                placement={"top-end"}
              >
                <Paper style={{ width: anchorRef.current?.clientWidth }}>
                  <ClickAwayListener onClickAway={handleCloseButton}>
                    <MenuList id="split-button-menu" autoFocusItem>
                      {options.slice(1).map((option, index) => (
                        <MenuItem
                          key={option}
                          selected={index === selectedIndex - 1}
                          onClick={() => handleClick(option, index + 1)}
                        >
                          {option}
                        </MenuItem>
                      ))}
                    </MenuList>
                  </ClickAwayListener>
                </Paper>
              </Popper>
              <ButtonGroup
                variant="contained"
                ref={anchorRefChange}
                aria-label="split button"
              >
                <Button
                  size="small"
                  onClick={() => handleClickChange(optionsChange[0], 0)}
                  sx={{ cursor: "default" }}
                >
                  {optionsChange[0]}
                </Button>
                <Button
                  size="small"
                  aria-controls={
                    openButtonChange ? "split-button-menu" : undefined
                  }
                  aria-expanded={openButtonChange ? "true" : undefined}
                  aria-label="select action"
                  aria-haspopup="menu"
                  onClick={handleToggleChange}
                >
                  <ReusableIcon
                    iconName={"ArrowDropUp"}
                    iconColor={"#FFFFFF"}
                  />
                </Button>
              </ButtonGroup>
              <Popper
                sx={{
                  zIndex: 1,
                }}
                open={openButtonChange}
                anchorEl={anchorRefChange.current}
                placement={"top-end"}
              >
                <Paper style={{ width: anchorRefChange.current?.clientWidth }}>
                  <ClickAwayListener onClickAway={handleCloseButtonChange}>
                    <MenuList id="split-button-menu" autoFocusItem>
                      {optionsChange.slice(1).map((option, index) => (
                        <MenuItem
                          key={option}
                          selected={index === selectedIndexChange - 1}
                          onClick={() => handleClickChange(option, index + 1)}
                        >
                          {option}
                        </MenuItem>
                      ))}
                    </MenuList>
                  </ClickAwayListener>
                </Paper>
              </Popper>

              {enableDocumentUpload && (
                <AttachmentUploadDialog
                  artifactId=""
                  artifactName=""
                  setOpen={setEnableDocumentUpload}
                  handleUpload={uploadExcel}
                />
              )}
            </BottomNavigation>
          </Paper>
          ):""
            }
        </Stack>
      </div>
    </div>
  );
};

export default GeneralLedger;
