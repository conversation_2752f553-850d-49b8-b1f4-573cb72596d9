import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  bomData: null,
  loading: false,
  error: null,
  tabValue:0,
  headerFieldsBOM: {},
};

const bomSlice = createSlice({
  name: "bom",
  initialState,
  reducers: {
    setBomData: (state, action) => {
      state.bomData = action.payload;
    },
    clearHeaderFieldsBOM: (state) => {
      state.headerFieldsBOM = {};
    },
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    setTabValue: (state, action) => {
      state.tabValue = action.payload;
    },
    setHeaderFieldsBOM: (state, action) => {
      state.headerFieldsBOM = action.payload;
    },
  },
});

export const { setBomData, clearHeaderFieldsBOM, setLoading, setError, clearError, setTabValue, setHeaderFieldsBOM } = bomSlice.actions;
export default bomSlice.reducer; 