import{b as Se,s as je,r as s,q as p,u as Ae,cd as Be,j as a,G as r,al as Me,a as n,z as Ee,T as f,B as M,x as oe,b4 as Fe,br as we,aE as ie,t as g,aB as E,aD as le,eQ as F,K as Te,c0 as _e,bp as qe,I as Ne,b1 as De,b8 as ae,ai as ze}from"./index-17b8d91e.js";import{d as Le}from"./EditOutlined-36c8ca4d.js";import{d as Ve}from"./ArrowCircleLeftOutlined-2a09f8e2.js";import{C as ke,d as Pe}from"./ChangeLog-0f47d713.js";import"./dayjs.min-ce01f2c7.js";import{E as We}from"./EditFieldForMassBankKey-ef40d942.js";import{a as Ie,b as Oe,S as Ke}from"./Stepper-88e4fb0c.js";import"./DatePicker-********.js";import"./dateViewRenderers-********.js";import"./useSlotProps-e34e1e13.js";import"./InputAdornment-5b0053c5.js";import"./CSSTransition-30917e2c.js";import"./useMediaQuery-6a073ac5.js";import"./DesktopDatePicker-07c19cde.js";import"./useMobilePicker-9978caff.js";const rt=()=>{var K,$,G,H,Q,J,R,U,X,Y,Z,ee,te;const _=Se(),m=je();s.useState({});const[q,N]=s.useState(0);s.useState([]);const[y,re]=s.useState(!1),[$e,se]=s.useState(!0);s.useState([]),s.useState([]);const[de,Ge]=s.useState([]),[u,S]=s.useState(0);s.useState(),p(e=>e.tabsData);const[ce,D]=s.useState(!1),[z,pe]=s.useState([]),[ue,L]=s.useState(!1),j=Ae(),b=j.state.tabsData;console.log(j.state,"state");const l=j.state.rowData,o=j.state.requestbenchRowData;let d=p(e=>{var i;return(i=e==null?void 0:e.initialData)==null?void 0:i.IWMMyTask});console.log(d,"taskData_in_mass"),console.log(o,"========massBKRowData==========="),console.log("selectedrowdata",l,b),p(e=>e.payload);let V=p(e=>e.edit.payload),w=p(e=>e.bankKey.requiredFields);console.log(w,"required_field_for_data");const he=p(e=>e.profitCenter.profitCenterCompCodes);let t=p(e=>e.userManagement.taskData),fe=p(e=>e.profitCenter.MultipleProfitCenterData),T=p(e=>e.userManagement.userData);console.log(fe,"profitCenterMultiple"),console.log(t,"task_in_mass========================="),console.log(t==null?void 0:t.processDesc,"task?.processDesc"),console.log(t==null?void 0:t.subject,"task?.subject");let x="",C="",v="";(t==null?void 0:t.processDesc)==="Mass Change"?(x=t!=null&&t.subject?(K=t==null?void 0:t.subject)==null?void 0:K.slice(3):o==null?void 0:o.requestId.slice(3),C=($=d==null?void 0:d.body)!=null&&$.controllingArea?"":l.controllingArea,v=(G=t==null?void 0:t.body)!=null&&G.profitCenter?"":l.profitCenter):(t==null?void 0:t.processDesc)==="Mass Create"?(x=t!=null&&t.subject?(H=t==null?void 0:t.subject)==null?void 0:H.slice(3):o==null?void 0:o.requestId.slice(3),C=(Q=d==null?void 0:d.body)!=null&&Q.controllingArea?"":l.controllingArea,v=(J=t==null?void 0:t.body)!=null&&J.profitCenter?"":l.profitCenter):(o==null?void 0:o.requestType)==="Mass Create"?(x=t!=null&&t.subject?(R=t==null?void 0:t.subject)==null?void 0:R.slice(3):o==null?void 0:o.requestId.slice(3),C=(U=d==null?void 0:d.body)!=null&&U.controllingArea?"":l.controllingArea,v=(X=t==null?void 0:t.body)!=null&&X.profitCenter?"":l.profitCenter):(o==null?void 0:o.requestType)==="Mass Change"&&(x=t!=null&&t.subject?(Y=t==null?void 0:t.subject)==null?void 0:Y.slice(3):o==null?void 0:o.requestId.slice(3),C=(Z=d==null?void 0:d.body)!=null&&Z.controllingArea?"":l.controllingArea,v=(ee=t==null?void 0:t.body)!=null&&ee.profitCenter?"":l.profitCenter);const ge=(e,i)=>{setActiveTab(i)},k=()=>{const e=O();y?e?(S(i=>i-1),m(F())):W():(S(i=>i-1),m(F()))},P=()=>{const e=O();y?e?(S(i=>i+1),m(F())):W():(S(i=>i+1),m(F()))},W=()=>{L(!0)},A=Object.entries(b==null?void 0:b.viewData).filter(e=>typeof e[1]=="object"&&e[1]!=null).map(e=>e[0]),B=Object.entries(b.viewData).filter(e=>typeof e[1]=="object"&&e[1]!=null).map(e=>Object.entries(e[1])),I={};B.map(e=>{e.forEach((i,c)=>{i.forEach((h,ve)=>{ve!==0&&h.forEach(ne=>{I[ne.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("")]=ne.value})})})});const me=()=>{re(!0),se(!1)},be=e=>{D(e)},xe=()=>{D(!0)},ye=e=>{console.log("compcode",e);const i=h=>{console.log("value",h),m(ze({keyName:"Region",data:h.body}))},c=h=>{console.log(h,"error in dojax")};Te(`/${_e}/data/getRegionBasedOnCountry?country=${e}`,"get",i,c)};s.useEffect(()=>{m(Be(I))},[]),s.useEffect(()=>{ye(l.bankCountry)},[]),console.log(V,w,"requiredFieldTabWise");const O=()=>qe(V,w,pe),Ce=()=>{L(!1)};return console.log(x,C,v,"================"),console.log("tabcontents",b),a("div",{children:[a(r,{container:!0,style:{...Me,backgroundColor:"#FAFCFF"},children:[z.length!=0&&n(Ee,{openSnackBar:ue,alertMsg:"Please fill the following Field: "+z.join(", "),handleSnackBarClose:Ce}),a(r,{sx:{width:"inherit"},children:[a(r,{item:!0,md:12,style:{padding:"16px",display:"flex"},children:[n(r,{style:{display:"flex",justifyContent:"flex-end"},children:n(Ne,{color:"primary","aria-label":"upload picture",component:"label",sx:De,children:n(Ve,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{_(-1)}})})}),a(r,{md:10,children:[n(f,{variant:"h3",children:a("strong",{children:["Multiple Bank  Key : ",l.bankKey," "]})}),n(f,{variant:"body2",color:"#777",children:"This view displays details of uploaded Bank Key"})]}),n(r,{md:1,sx:{display:"flex",justifyContent:"flex-end",marginRight:"4px"},children:n(g,{variant:"outlined",size:"small",sx:ae,onClick:xe,title:"Chnage Log",children:n(Pe,{sx:{padding:"2px"},fontSize:"small"})})}),ce&&n(ke,{open:!0,closeModal:be,requestId:x,requestType:"Mass",pageName:"bankKey",controllingArea:l.bankCountry,centerName:l.bankKey}),y?"":(T==null?void 0:T.role)==="Finance"?n(r,{md:1,sx:{display:"flex",justifyContent:"flex-end"},children:n(r,{item:!0,children:a(g,{variant:"outlined",size:"small",sx:ae,onClick:me,children:["Change",n(Le,{sx:{padding:"2px"},fontSize:"small"})]})})}):""]}),n(r,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:a(M,{width:"70%",sx:{marginLeft:"40px"},children:[n(r,{item:!0,sx:{paddingTop:"2px !important"},children:a(oe,{flexDirection:"row",children:[n("div",{style:{width:"15%"},children:n(f,{variant:"body2",color:"#777",children:"Bank Key"})}),a(f,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[": ",l.bankKey]})]})}),n(r,{item:!0,sx:{paddingTop:"2px !important"},children:a(oe,{flexDirection:"row",children:[n("div",{style:{width:"15%"},children:n(f,{variant:"body2",color:"#777",children:"Bank Country"})}),a(f,{variant:"body2",fontWeight:"bold",children:[": ",l.bankCountry]})]})})]})}),a(r,{container:!0,style:{padding:"16px"},children:[n(Ke,{activeStep:u,onChange:ge,variant:"scrollable",sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},"aria-label":"mui tabs example",children:A.map((e,i)=>n(Ie,{children:n(Oe,{sx:{fontWeight:"700"},children:e})},e))}),n(r,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:B&&((te=B[u])==null?void 0:te.map((e,i)=>u===2?n(CompCodesProfitCenter,{compCodesTabDetails:he,displayCompCode:de}):n(M,{sx:{width:"100%"},children:a(r,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Fe},children:[n(r,{container:!0,children:n(f,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:e[0]})}),n(M,{children:n(M,{sx:{width:"100%"},children:n(we,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:n(r,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:[...e[1]].map(c=>n(We,{activeTabIndex:u,fieldGroup:e[0],selectedRowData:l.bankKey,pcTabs:A,label:c.fieldName,value:c.value,length:c.maxLength,visibility:c.visibility,onSave:h=>handleFieldSave(c.fieldName,h),isEditMode:y,type:c.fieldType,field:c}))})})})})]})},i)))},B)]})]})]}),y?n(le,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:a(ie,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:q,onChange:e=>{N(e)},children:[n(g,{size:"small",variant:"contained",onClick:()=>{_(-1)},children:"Save"}),n(g,{variant:"contained",size:"small",sx:{...E,mr:1},onClick:k,disabled:u===0,children:"Back"}),n(g,{variant:"contained",size:"small",sx:{...E,mr:1},onClick:P,disabled:u===A.length-1,children:"Next"})]})}):n(le,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:a(ie,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:q,onChange:e=>{N(e)},children:[n(g,{variant:"contained",size:"small",sx:{...E,mr:1},onClick:k,disabled:u===0,children:"Back"}),n(g,{variant:"contained",size:"small",sx:{...E,mr:1},onClick:P,disabled:u===A.length-1,children:"Next"})]})})]})};export{rt as default};
