const e=[{keyName:"BaseUnit",endPoint:"getBaseUOM"},{keyName:"Division",endPoint:"getDivision"},{keyName:"MaterialGroup",endPoint:"getMaterialGroup"},{keyName:"MaterialGroupPackagingMaterials",endPoint:"getMatGrpPack"}],n=[{keyName:"BaseUnit",endPoint:"getBaseUOM"},{keyName:"Division",endPoint:"getDivision"},{keyName:"MaterialGroup",endPoint:"getMaterialGroup"},{keyName:"ExtMatlGroup",endPoint:"getExMaterialGroup"},{keyName:"ProdHier",endPoint:"getProdHier"},{keyName:"CrossPlantMaterialStatus",endPoint:"getXPlant"},{keyName:"GeneralItemCategorGroup",endPoint:"getGenItemCatGroup"},{keyName:"WeightUnit",endPoint:"getWeightUnit"},{keyName:"VolumeUnit",endPoint:"getVolumeUnit"},{keyName:"CategoryOfInternationalArticleNumberEAN",endPoint:"getEanCategory"},{keyName:"ANPCode",endPoint:"getANPCode"},{keyName:"HandlingIndicator",endPoint:"getLogHandlingInd"},{keyName:"WarehouseMaterialGroup",endPoint:"getWHMaterialGroup"},{keyName:"SerialNumberProfile",endPoint:"getSerialNumberProfile"},{keyName:"QuarantinePeriod",endPoint:"getBaseUOM"},{keyName:"QualityInspectionGroup",endPoint:"getQualInspGrp"},{keyName:"HandlingUnitType",endPoint:"getHandlingUnitType"}],t=[{"Basic Data":[{keyName:"BaseUnit",endPoint:"getBaseUOM"},{keyName:"Division",endPoint:"getDivision"},{keyName:"MaterialGroup",endPoint:"getMaterialGroup"},{keyName:"ExtMatlGroup",endPoint:"getExMaterialGroup"},{keyName:"ProductHierarchy",endPoint:"getProdHierarchy"},{keyName:"CrossPlantMaterialStatus",endPoint:"getXPlant"},{keyName:"GeneralItemCategorGroup",endPoint:"getGenItemCatGroup"},{keyName:"WeightUnit",endPoint:"getWeightUnit"},{keyName:"VolumeUnit",endPoint:"getVolumeUnit"},{keyName:"CategoryOfInternationalArticleNumberEAN",endPoint:"getEanCategory"},{keyName:"ANPCode",endPoint:"getANPCode"},{keyName:"HandlingIndicator",endPoint:"getLogHandlingInd"},{keyName:"WarehouseMaterialGroup",endPoint:"getWHMaterialGroup"},{keyName:"SerialNumberProfile",endPoint:"getSerialNumberProfile"},{keyName:"QuarantinePeriod",endPoint:"getBaseUOM"},{keyName:"QualityInspectionGroup",endPoint:"getQualInspGrp"},{keyName:"HandlingUnitType",endPoint:"getHandlingUnitType"}]},{Sales:[{keyName:"SalesUnit",endPoint:"getSalesUnit"},{keyName:"UnitofMeasureGrp",endPoint:"getUnitofMeasureGrp"},{keyName:"Xdistrchainstatus",endPoint:"getXDistChainStatus"},{keyName:"DChainspecstatus",endPoint:"getDChainSpecStatus"},{keyName:"DeliveryUnit",endPoint:"getBaseUOM"},{keyName:"RoundingProfile",endPoint:"getRoundingProfile"},{keyName:"MaterialStatisticsGroup",endPoint:"getMaterialStatisticsGroup"},{keyName:"MaterialPriceGroup",endPoint:"getMaterialPriceGroup"},{keyName:"VolumeRebateGroup",endPoint:"getVolumeRebateGroup"},{keyName:"AccountAssignmentGroupMaterial",endPoint:"getAccountAssignmentGrpMaterial"},{keyName:"ItemCat",endPoint:"getGenItemCatGroup"},{keyName:"PricingRefMaterial",endPoint:"getPricingRefMaterial"},{keyName:"ComissionGroup",endPoint:"getComissionGroup"},{keyName:"TranspGroup",endPoint:"getTransportationGroup"}]}],a=[{keyName:"UserResponsible",endPoint:"getUserResponsible"},{keyName:"CostCenterCategory",endPoint:"getCostCenterCategory"},{keyName:"CostingSheet",endPoint:"getCostingSheet"},{keyName:"CountryReg",endPoint:"getCountry"},{keyName:"Jurisdiction",endPoint:"getJurisdiction"},{keyName:"BusinessArea",endPoint:"getBusinessArea"},{keyName:"FunctionalArea",endPoint:"getFunctionalArea"},{keyName:"LanguageKey",endPoint:"getLanguageKey"},{keyName:"ActyIndepFormPlngTemp",endPoint:"getActyIndepFormPln"},{keyName:"ActyDepFormPlngTemp",endPoint:"getActyDepFormPlng"},{keyName:"ActyIndepAllocTemp",endPoint:"getActyIndepAllocTem"},{keyName:"ActyDepAllocTemplate",endPoint:"getActydepAllocTemp"},{keyName:"Templ:ActStatKeyFigure",endPoint:"getTemplActStatKeyFigure"}],i=[{keyName:"UserResponsible",endPoint:"getUserResponsible"},{keyName:"Segment",endPoint:"getSegment"},{keyName:"FormPlanningTemp",endPoint:"getFormPlanningTemp"},{keyName:"CountryReg",endPoint:"getCountryOrReg"},{keyName:"TaxJur",endPoint:"getJurisdiction"},{keyName:"Language",endPoint:"getLanguageKey"}],o=[{keyName:"TimeZone",endPoint:"getTimezone"},{keyName:"CountryReg",endPoint:"getCountry"},{keyName:"Country",endPoint:"getCountry"},{keyName:"Country1",endPoint:"getCountry"},{keyName:"Country2",endPoint:"getCountry"},{keyName:"TaxJurisdiction",endPoint:"getTaxJurisdiction"},{keyName:"Language",endPoint:"getLanguageKey"},{keyName:"TransportZone",endPoint:"getTransportZone"},{keyName:"Undeliverable",endPoint:"getUndeliverable"},{keyName:"Undeliverable1",endPoint:"getUndeliverable"},{keyName:"DeliveryServiceType",endPoint:"getDeliverySrvType"},{keyName:"StructureGroup",endPoint:"getStructureGroup"}],r=[{keyName:"Language",endPoint:"getLanguageKey"},{keyName:"TradingPartner",endPoint:"getTradingPartner"},{keyName:"SortKey",endPoint:"getSortKey"},{keyName:"PlanningLevel",endPoint:"getPlanningLevel"},{keyName:"PlanningLevel",endPoint:"getPlanningLevel"},{keyName:"InternalUOM",endPoint:"getInternalUOM"},{keyName:"InterestIndicator",endPoint:"getInterestIndicator"},{keyName:"InterestCalculationFrequency",endPoint:"getInterestCalculationFreq"},{keyName:"AccountType",endPoint:"getGLAccountType"},{keyName:"FunctionalArea",endPoint:"getFunctionalArea"},{keyName:"ChartOfAccounts",endPoint:"getChartOfAccounts"},{keyName:"AccountCurrency",endPoint:"getAccountCurrency"},{keyName:"ExchangeRateDiffKey",endPoint:"getExchangeRateDiffKey"},{keyName:"ReconAccountForAccountType",endPoint:"getReconAccountForAccountType"}],g={newMaterial:e,createMaterial:n,changeMaterial:t,costCenter:a,profitCenter:i,bankKey:o,generalLedger:r};export{g as l};
