import React, { lazy } from 'react';
import { Route } from 'react-router-dom';

const MaterialMaster = lazy(() => import('../../components/MasterDataCockpit/MaterialMaster'));
const MassMaterialTable = lazy(() => import('../../components/MasterDataCockpit/MassMaterialTable'));
const CreateMaterialDetail = lazy(() => import('../../components/MasterDataCockpit/CreateMaterialDetail'));
const DisplayMaterialDetail = lazy(() => import('../../components/MasterDataCockpit/DisplayMaterialDetail'));
const NewMaterial = lazy(() => import('../../components/MasterDataCockpit/NewMaterial'));
const AdditionalData = lazy(() => import('../../components/MasterDataCockpit/AdditionalData'));
const DisplayAdditionalData = lazy(() => import('../../components/MasterDataCockpit/DisplayAdditionalData'));
const ExtendMaterial = lazy(() => import('../../components/MasterDataCockpit/ExtendMaterial'));
const DisplayMaterialSAPView = lazy(() => import('../../components/MasterDataCockpit/DisplayMaterialSAPView'));
const EditMultipleMaterial = lazy(() => import('../../components/MasterDataCockpit/EditMultipleMaterial'));
const DisplayMultipleMaterialRequestBench = lazy(() => import('../../components/MasterDataCockpit/DisplayMultipleMaterialRequestBench'));

const CostCenter = lazy(() => import('../../components/MasterDataCockpit/CostCenter/CostCenter'));
const DisplayCostCenter = lazy(() => import('../../components/MasterDataCockpit/CostCenter/DisplayCostCenter'));
const DisplayCopyCostCenter = lazy(() => import('../../components/MasterDataCockpit/CostCenter/DisplayCopyCostCenter'));
const CreateMultipleCostCenter = lazy(() => import('../../components/MasterDataCockpit/CostCenter/CreateMultipleCostCenter'));
const EditMultipleCostCenter = lazy(() => import('../../components/MasterDataCockpit/CostCenter/EditMultipleCostCenter'));
const NewSingleCostCenter = lazy(() => import('../../components/MasterDataCockpit/CostCenter/NewSingleCostCenter'));
const DisplayMultipleCostCenterRequestBench = lazy(() => import('../../components/MasterDataCockpit/CostCenter/DisplayMultipleCostCenterRequestBench'));
const MassCostCenterTableRequestBench = lazy(() => import('../../components/MasterDataCockpit/CostCenter/MassCostCenterTableRequestBench'));
const FieldConfigurationCostCenter = lazy(() => import('../../components/MasterDataCockpit/CostCenter/FieldConfigurationCostCenter'));

const ProfitCenter = lazy(() => import('../../components/MasterDataCockpit/ProfitCenter/ProfitCenter'));
const DisplayProfitCenter = lazy(() => import('../../components/MasterDataCockpit/ProfitCenter/DisplayProfitCenter'));
const DisplayCopyProfitCenter = lazy(() => import('../../components/MasterDataCockpit/ProfitCenter/DisplayCopyProfitCenter'));
const CreateMultipleProfitCenter = lazy(() => import('../../components/MasterDataCockpit/ProfitCenter/CreateMultipleProfitCenter'));
const EditMultipleProfitCenter = lazy(() => import('../../components/MasterDataCockpit/ProfitCenter/EditMultipleProfitCenter'));
const NewSingleProfitCenter = lazy(() => import('../../components/MasterDataCockpit/ProfitCenter/NewSingleProfitCenter'));
const DisplayMultipleProfitCenterRequestBench = lazy(() => import('../../components/MasterDataCockpit/ProfitCenter/DisplayMultipleProfitCenterRequestBench'));
const MassProfitCenterTableRequestBench = lazy(() => import('../../components/MasterDataCockpit/ProfitCenter/MassProfitCenterTableRequestBench'));
const FieldConfigurationProfitCenter = lazy(() => import('../../components/MasterDataCockpit/ProfitCenter/FieldConfigurationProfitCenter'));

const BankKey = lazy(() => import('../../components/MasterDataCockpit/BankKey/BankKey'));
const DisplayBankKey = lazy(() => import('../../components/MasterDataCockpit/BankKey/DisplayBankKey'));
const CreateMultipleBankKey = lazy(() => import('../../components/MasterDataCockpit/BankKey/CreateMultipleBankKey'));
const EditMultipleBankKey = lazy(() => import('../../components/MasterDataCockpit/BankKey/editMultipleBankKey'));
const NewSingleBankKey = lazy(() => import('../../components/MasterDataCockpit/BankKey/NewSingleBankKey'));
const DisplayMultiplebankkeyRequestBench = lazy(() => import('../../components/MasterDataCockpit/BankKey/displayMultiplebankKeyRequestBench'));
const MassBKTableRequestBench = lazy(() => import('../../components/MasterDataCockpit/BankKey/MassBKTableRequestBench'));
const FieldConfigurationForCreateBankKey = lazy(() => import('../../components/MasterDataCockpit/BankKey/FieldCatalogue/FieldConfigurationForCreateBankKey'));

const GeneralLedger = lazy(() => import('../../components/MasterDataCockpit/GeneralLedger/GeneralLedger'));
const DisplayGeneralLedger = lazy(() => import('../../components/MasterDataCockpit/GeneralLedger/DisplayGeneralLedger'));
const DisplayCopyGeneralLedger = lazy(() => import('../../components/MasterDataCockpit/GeneralLedger/DisplayCopyGeneralLedger'));
const CreateMultipleGL = lazy(() => import('../../components/MasterDataCockpit/GeneralLedger/CreateMultipleGL'));
const EditMultipleGL = lazy(() => import('../../components/MasterDataCockpit/GeneralLedger/EditMultipleGL'));
const NewSingleGeneralLedger = lazy(() => import('../../components/MasterDataCockpit/GeneralLedger/NewSingleGeneralLedger'));
const DisplayMultipleGLRequestBench = lazy(() => import('../../components/MasterDataCockpit/GeneralLedger/DisplayMultipleGLRequestBench'));
const MassGLTableRequestBench = lazy(() => import('../../components/MasterDataCockpit/GeneralLedger/MassGLTableRequestBench'));
const FieldConfigurationGeneralLedger = lazy(() => import('../../components/MasterDataCockpit/GeneralLedger/FieldCatalogue/FieldConfigurationForCreateGL'));

export const MasterDataRoutes = [
  <Route path="/masterDataCockpit/materialMaster/materialSingle" element={<MaterialMaster />} />,
  <Route path="/masterDataCockpit/materialMaster/massMaterialTable" element={<MassMaterialTable />} />,
  <Route path="/masterDataCockpit/materialMaster/createMaterialDetail" element={<CreateMaterialDetail />} />,
  <Route path="/masterDataCockpit/materialMaster/displayMaterialDetail/:reqId" element={<DisplayMaterialDetail />} />,
  <Route path="/masterDataCockpit/materialMaster/newMaterial" element={<NewMaterial />} />,
  <Route path="/masterDataCockpit/materialMaster/createMaterialDetail/additionalData" element={<AdditionalData />} />,
  <Route path="/masterDataCockpit/materialMaster/displayMaterialDetail/displayAdditionalData" element={<DisplayAdditionalData />} />,
  <Route path="/masterDataCockpit/materialMaster/displayMaterialDetail/extend/:reqId" element={<ExtendMaterial />} />,
  <Route path="/masterDataCockpit/materialMaster/DisplayMaterialSAPView/:matNo" element={<DisplayMaterialSAPView />} />,
  <Route path="/masterDataCockpit/materialMaster/editMultipleMaterial/:description" element={<EditMultipleMaterial />} />,
  <Route path="/masterDataCockpit/materialMaster/displayMultipleMaterialRequestBench/:description" element={<DisplayMultipleMaterialRequestBench />} />,

  <Route path="/masterDataCockpit/costCenter" element={<CostCenter />} />,
  <Route path="/masterDataCockpit/costCenter/displayCostCenter/:reqId" element={<DisplayCostCenter />} />,
  <Route path="/masterDataCockpit/costCenter/displayCopyCostCenter/:reqId" element={<DisplayCopyCostCenter />} />,
  <Route path="/masterDataCockpit/costCenter/createMultipleCostCenter" element={<CreateMultipleCostCenter />} />,
  <Route path="/masterDataCockpit/costCenter/createMultipleCostCenter/editMultipleCostCenter/:costCenter" element={<EditMultipleCostCenter />} />,
  <Route path="/masterDataCockpit/costCenter/newSingleCostCenter" element={<NewSingleCostCenter />} />,
  <Route path="/masterDataCockpit/costCenter/displayMultipleCostCenterRequestBench/:costCenter" element={<DisplayMultipleCostCenterRequestBench />} />,
  <Route path="/masterDataCockpit/costCenter/massCostCenterTableRequestBench" element={<MassCostCenterTableRequestBench />} />,
  <Route path="/configCockpit/fieldConfiguration/costCenter" element={<FieldConfigurationCostCenter />} />,

  <Route path="/masterDataCockpit/profitCenter" element={<ProfitCenter />} />,
  <Route path="/masterDataCockpit/profitCenter/displayProfitCenter/:reqId" element={<DisplayProfitCenter />} />,
  <Route path="/masterDataCockpit/profitCenter/displayCopyProfitCenter/:reqId" element={<DisplayCopyProfitCenter />} />,
  <Route path="/masterDataCockpit/profitCenter/createMultipleProfitCenter" element={<CreateMultipleProfitCenter />} />,
  <Route path="/masterDataCockpit/profitCenter/createMultipleProfitCenter/editMultipleProfitCenter/:profitCenter" element={<EditMultipleProfitCenter />} />,
  <Route path="/masterDataCockpit/profitCenter/newSingleProfitCenter" element={<NewSingleProfitCenter />} />,
  <Route path="/masterDataCockpit/profitCenter/massProfitCenterTableRequestBench" element={<MassProfitCenterTableRequestBench />} />,
  <Route path="/masterDataCockpit/profitCenter/massProfitCenterTableRequestBench/displayMultipleProfitCenterRequestBench/:profitCenter" element={<DisplayMultipleProfitCenterRequestBench />} />,
  <Route path="/configCockpit/fieldConfiguration/profitCenter" element={<FieldConfigurationProfitCenter />} />,

  <Route path="/masterDataCockpit/bankKey" element={<BankKey />} />,
  <Route path="/masterDataCockpit/bankKey/displayBankKey/:reqId" element={<DisplayBankKey />} />,
  <Route path="/masterDataCockpit/bankKey/createMultipleBankKey" element={<CreateMultipleBankKey />} />,
  <Route path="/masterDataCockpit/bankKey/createMultipleBankKey/editMultipleBankkey/:bankKey" element={<EditMultipleBankKey />} />,
  <Route path="/masterDataCockpit/bankKey/newSingleBankKey" element={<NewSingleBankKey />} />,
  <Route path="/masterDataCockpit/bankKey/massBKTableRequestBench" element={<MassBKTableRequestBench />} />,
  <Route path="/masterDataCockpit/bankkey/massBKTableRequestBench/displayMultipleBankKeyRequestBench/:bankKey" element={<DisplayMultiplebankkeyRequestBench />} />,
  <Route path="/configCockpit/fieldConfiguration/bankKey" element={<FieldConfigurationForCreateBankKey />} />,

  <Route path="/masterDataCockpit/generalLedger" element={<GeneralLedger />} />,
  <Route path="/masterDataCockpit/generalLedger/displayGeneralLedger/:reqId" element={<DisplayGeneralLedger />} />,
  <Route path="/masterDataCockpit/generalLedger/displayCopyGeneralLedger" element={<DisplayCopyGeneralLedger />} />,
  <Route path="/masterDataCockpit/generalLedger/createMultipleGL" element={<CreateMultipleGL />} />,
  <Route path="/masterDataCockpit/generalLedger/createMultipleGL/editMultipleGL/:costCenter" element={<EditMultipleGL />} />,
  <Route path="/masterDataCockpit/generalLedger/newSingleGeneralLedger" element={<NewSingleGeneralLedger />} />,
  <Route path="/masterDataCockpit/generalLedger/massGLTableRequestBench" element={<MassGLTableRequestBench />} />,
  <Route path="/masterDataCockpit/generalLedger/massGLTableRequestBench/displayMultipleGLRequestBench/:glAccount" element={<DisplayMultipleGLRequestBench />} />,
  <Route path="/configCockpit/fieldConfiguration/generalLedger" element={<FieldConfigurationGeneralLedger />} />
];