import{r as x,s as cn,b as an,u as dn,q as H,bh as mn,K as N,b$ as I,a as t,j as l,b4 as ce,T as p,B as w,br as ae,G as d,aC as Ht,bt as Kt,bs as Se,x as X,ab as Ae,F as K,bX as hn,C as pn,z as Xt,V as Ce,aF as ke,I as Te,aG as We,W as Ne,ar as G,E as de,X as Fe,t as a,cb as un,cc as xn,al as fn,b1 as bn,bm as ye,b8 as W,aD as O,aE as V,aB as f,b6 as gn,at as Jt,au as Yt,cd as vn,bW as Ie,bp as Sn,ce as An,ai as te,bq as Oe}from"./index-75c1660a.js";import{d as Cn}from"./ArrowCircleLeftOutlined-f7b52d40.js";import{d as Ee}from"./EditOutlined-6971b85d.js";import{E as me}from"./EditableFieldForCostCenter-286ad82c.js";import{R as kn}from"./ReusableAttachementAndComments-682b0475.js";import{M as Tn,a as Nn}from"./UtilDoc-7fb813ce.js";import{l as Qt}from"./lookup-1dcf10ac.js";import{T as Zt,a as Gt,b as Dt,c as Rt,d as Pt}from"./TimelineSeparator-6e03ad1b.js";import{S as Fn,a as yn,b as In}from"./Stepper-2dbfb76b.js";import{D as er}from"./DatePicker-31fef6b6.js";import"./CloudUpload-d5d09566.js";import"./utilityImages-067c3dc2.js";import"./Add-62a207fb.js";import"./Delete-1d158507.js";/* empty css            */import"./FileDownloadOutlined-329b8f56.js";import"./VisibilityOutlined-a5a8c4d9.js";import"./DeleteOutlined-fe5b7345.js";import"./Slider-c4e5ff46.js";import"./clsx-a965ebfb.js";import"./dateViewRenderers-dbe02df3.js";import"./useSlotProps-da724f1f.js";import"./InputAdornment-a22e1655.js";import"./CSSTransition-8d766865.js";import"./useMediaQuery-33e0a836.js";import"./DesktopDatePicker-47a97548.js";import"./useMobilePicker-056b38fc.js";const ao=()=>{var dt,mt,ht,pt,ut,xt,ft,bt,gt,vt,St,At,Ct,kt,Tt,Nt,Ft,yt,It,Et,Bt,zt,$t,jt,qt,Mt,Lt,Wt,Ot,Vt,_t;const[F,Ve]=x.useState(!1);x.useState(0);const[re,tr]=x.useState(!0);x.useState({});const[T,rr]=x.useState([]),[h,he]=x.useState(0),[_e,nr]=x.useState([]),[z,or]=x.useState(),[ir,En]=x.useState(!1);x.useState([]);const[sr,b]=x.useState(!1),[lr,S]=x.useState(!1),[we,g]=x.useState(""),[cr,v]=x.useState(!1),[Bn,A]=x.useState(!0),[zn,C]=x.useState(!1),[ar,k]=x.useState(!0),[dr,pe]=x.useState(!1),[mr,Be]=x.useState(!1),[hr,ze]=x.useState(!1),[pr,Ue]=x.useState(!1),[J,He]=x.useState(""),[D,$n]=x.useState([]),[ur,Ke]=x.useState(!1),[$e,je]=x.useState(!0),[xr,Xe]=x.useState(!1),[R,jn]=x.useState([]),[fr,qe]=x.useState(!1),[br,ne]=x.useState(!0),[gr,Je]=x.useState(!1),[vr,Sr]=x.useState(""),[ue,Ar]=x.useState(""),[qn,Cr]=x.useState([]),[Ye,kr]=x.useState([]),[Tr,Qe]=x.useState(!1),_=cn(),Ze=an(),Nr=dn(),P=H(n=>n.appSettings);let xe=H(n=>{var s;return(s=n.userManagement.entitiesAndActivities)==null?void 0:s["Cost Center"]}),Fr=H(n=>{var s;return(s=n==null?void 0:n.initialData)==null?void 0:s.IWMMyTask}),c=H(n=>n.userManagement.userData),r=Nr.state,m=H(n=>n.userManagement.taskData),yr=H(n=>n.costCenter.requiredFields);const e=H(n=>n.edit.payload);let Ir=H(n=>n.edit.payload);console.log("ccroewdata",new Date(`${z==null?void 0:z.ValidFrom} GMT-0000`).toUTCString()),console.log("costCenterDetails",_e),console.log(J,"Remarks");const oe=H(n=>{var s;return(s=n.costCenter)==null?void 0:s.costCenterViewData});H(n=>n.costCenter.requiredFields),console.log("costCenterViewData",oe);var j={TaskId:m!=null&&m.taskId?m==null?void 0:m.taskId:"",CostCenterHeaderID:z!=null&&z.costCenterHeaderId?z==null?void 0:z.costCenterHeaderId:"",ControllingArea:(mt=(dt=r==null?void 0:r.controllingAreaData)==null?void 0:dt.newControllingArea)!=null&&mt.code?(pt=(ht=r==null?void 0:r.controllingAreaData)==null?void 0:ht.newControllingArea)==null?void 0:pt.code:"",Testrun:br,Action:"I",ReqCreatedBy:c==null?void 0:c.user_id,ReqCreatedOn:"",RequestStatus:"",CreationId:m!=null&&m.subject?m==null?void 0:m.subject.slice(3):"",EditId:"",DeleteId:"",MassCreationId:"",MassEditId:"",MassDeleteId:"",RequestType:"Create",MassRequestStatus:"",Remarks:J||"",Toitem:[{CostCenterID:z!=null&&z.costCenterId?z==null?void 0:z.costCenterId:"",ValidFrom:(ut=r==null?void 0:r.validFromDate)!=null&&ut.newValidFromDate?"/Date("+Date.parse((xt=r==null?void 0:r.validFromDate)==null?void 0:xt.newValidFromDate)+")/":"",ValidTo:(ft=r==null?void 0:r.validToDate)!=null&&ft.newValidToDate?"/Date("+Date.parse((bt=r==null?void 0:r.validToDate)==null?void 0:bt.newValidToDate)+")/":"",Costcenter:`${(vt=(gt=r==null?void 0:r.companyCode)==null?void 0:gt.newCompanyCode)==null?void 0:vt.code}${(St=r==null?void 0:r.costCenterName)==null?void 0:St.newCostCenterName}`?`${(Ct=(At=r==null?void 0:r.companyCode)==null?void 0:At.newCompanyCode)==null?void 0:Ct.code}${(kt=r==null?void 0:r.costCenterName)==null?void 0:kt.newCostCenterName}`:"",PersonInCharge:e!=null&&e.PersonResponsible?e==null?void 0:e.PersonResponsible:"",CostcenterType:e!=null&&e.CostCenterCategory?e==null?void 0:e.CostCenterCategory:"",CostctrHierGrp:"TUK1-PRODU",BusArea:e!=null&&e.BusinessArea?e==null?void 0:e.BusinessArea:"",CompCode:(Nt=(Tt=r==null?void 0:r.companyCode)==null?void 0:Tt.newCompanyCode)!=null&&Nt.code?(yt=(Ft=r==null?void 0:r.companyCode)==null?void 0:Ft.newCompanyCode)==null?void 0:yt.code:"TUK1",Currency:e!=null&&e.Currency?e==null?void 0:e.Currency:"",ProfitCtr:e!=null&&e.ProfitCenter?e==null?void 0:e.ProfitCenter:"",Name:e!=null&&e.Name?e==null?void 0:e.Name:"",Descript:e!=null&&e.Description?e==null?void 0:e.Description:"",PersonInChargeUser:e!=null&&e.UserResponsible?e==null?void 0:e.UserResponsible:"",RecordQuantity:(e==null?void 0:e.RecordQuantity)===!0?"X":"",LockIndActualPrimaryCosts:(e==null?void 0:e.ActualPrimaryCosts)===!0?"X":"",LockIndPlanPrimaryCosts:(e==null?void 0:e.PlanPrimaryCosts)===!0?"X":"",LockIndActSecondaryCosts:(e==null?void 0:e.ActsecondaryCosts)===!0?"X":"",LockIndPlanSecondaryCosts:(e==null?void 0:e.PlanSecondaryCosts)===!0?"X":"",LockIndActualRevenues:(e==null?void 0:e.ActualRevenue)===!0?"X":"",LockIndPlanRevenues:(e==null?void 0:e.PlanRevenue)===!0?"X":"",LockIndCommitmentUpdate:(e==null?void 0:e.CommitmentUpdate)===!0?"X":"",ConditionTableUsage:"",Application:"",CstgSheet:e!=null&&e.CostingSheet?e==null?void 0:e.CostingSheet:"",ActyIndepTemplate:e!=null&&e.ActyIndepFromPlngTemp?e==null?void 0:e.ActyIndepFromPlngTemp:"",ActyDepTemplate:e!=null&&e.ActyDepFromPlngTemp?e==null?void 0:e.ActyDepFromPlngTemp:"",AddrTitle:e!=null&&e.Title?e==null?void 0:e.Title:"",AddrName1:e!=null&&e.Name1?e==null?void 0:e.Name1:"",AddrName2:e!=null&&e.Name2?e==null?void 0:e.Name2:"",AddrName3:e!=null&&e.Name3?e==null?void 0:e.Name3:"",AddrName4:e!=null&&e.Name4?e==null?void 0:e.Name4:"",AddrStreet:e!=null&&e.Street?e==null?void 0:e.Street:"",AddrCity:e!=null&&e.Location?e==null?void 0:e.Location:"",AddrDistrict:e!=null&&e.District?e==null?void 0:e.District:"",AddrCountry:e!=null&&e.CountryReg?e==null?void 0:e.CountryReg:"",AddrCountryIso:"",AddrTaxjurcode:e!=null&&e.Jurisdiction?e==null?void 0:e.Jurisdiction:"",AddrPoBox:e!=null&&e.POBox?e==null?void 0:e.POBox:"",AddrPostlCode:e!=null&&e.PostalCode?e==null?void 0:e.PostalCode:"",AddrPobxPcd:e!=null&&e.POBoxPostCod?e==null?void 0:e.POBoxPostCod:"",AddrRegion:e!=null&&e.Region?e==null?void 0:e.Region:"",TelcoLangu:"",TelcoLanguIso:e!=null&&e.LanguageKey?e==null?void 0:e.LanguageKey:"",TelcoTelephone:e!=null&&e.Telephone1?e==null?void 0:e.Telephone1:"",TelcoTelephone2:e!=null&&e.Telephone2?e==null?void 0:e.Telephone2:"",TelcoTelebox:e!=null&&e.TeleboxNumber?e==null?void 0:e.TeleboxNumber:"",TelcoTelex:e!=null&&e.TelexNumber?e==null?void 0:e.TelexNumber:"",TelcoFaxNumber:e!=null&&e.FaxNumber?e==null?void 0:e.FaxNumber:"",TelcoTeletex:e!=null&&e.TeletexNumber?e==null?void 0:e.TeletexNumber:"",TelcoPrinter:e!=null&&e.PrinterDestination?e==null?void 0:e.PrinterDestination:"",TelcoDataLine:e!=null&&e.DataLine?e==null?void 0:e.DataLine:"",ActyDepTemplateAllocCc:e!=null&&e.ActyDepAllocTemp?e==null?void 0:e.ActyDepAllocTemp:"",ActyDepTemplateSk:e!=null&&e.TempActStatKeyFigure?e==null?void 0:e.TempActStatKeyFigure:"",ActyIndepTemplateAllocCc:e!=null&&e.ActyIndepAllocTemp?e==null?void 0:e.ActyIndepAllocTemp:"",ActyIndepTemplateSk:e!=null&&e.TempActStatKeyFigure?e==null?void 0:e.TempActStatKeyFigure:"",AvcActive:!1,AvcProfile:"",BudgetCarryingCostCtr:"",CurrencyIso:"",Department:e!=null&&e.Department?e==null?void 0:e.Department:"",FuncArea:e!=null&&e.FunctionalArea?e==null?void 0:e.FunctionalArea:"",FuncAreaFixAssigned:"",FuncAreaLong:"",Fund:"",FundFixAssigned:"",GrantFixAssigned:"",GrantId:"",JvEquityTyp:"",JvJibcl:"",JvJibsa:"",JvOtype:"",JvRecInd:"",JvVenture:"",Logsystem:""}]};console.log("taskData",Fr),console.log("taskRowDetails",m);const Er=()=>{Qe(!1)},Ge=()=>Sn(Ir,yr,kr),Br=n=>{console.log("compcode",n);const s=i=>{console.log("value",i),_(te({keyName:"Region",data:i.body}))},o=i=>{console.log(i,"error in dojax")};N(`/${I}/data/getRegionBasedOnCountry?country=${n}`,"get",s,o)},zr=n=>{let s=[];for(const o in n){if(n.hasOwnProperty(o)){for(const i in n[o])if(n[o].hasOwnProperty(i)){const u=n[o][i];for(const y of u)if(y.visibility==="0"||y.visibility==="Required"){console.log(y.fieldName,"field.fieldName");let B=y.fieldName.replace(/\s/g,"");s.push(B)}}}Cr(i=>({...i,error_field_arr:s}))}},$r=()=>{var i,u,y,B,ie,se,E,Z,le;var n={id:r!=null&&r.reqStatus?r==null?void 0:r.id:"",costCenter:(u=(i=r==null?void 0:r.costCenter)==null?void 0:i.newCostCenter)!=null&&u.code?(B=(y=r==null?void 0:r.costCenter)==null?void 0:y.newCostCenter)==null?void 0:B.code:r!=null&&r.costCenter?(ie=r==null?void 0:r.costCenter)==null?void 0:ie.split(" ")[0]:"",controllingArea:(E=(se=r==null?void 0:r.controllingAreaDataCopy)==null?void 0:se.newControllingAreaCopyFrom)!=null&&E.code?(le=(Z=r==null?void 0:r.controllingAreaDataCopy)==null?void 0:Z.newControllingAreaCopyFrom)==null?void 0:le.code:"",reqStatus:r!=null&&r.reqStatus?r==null?void 0:r.reqStatus:"Approved",screenName:"Create"};const s=Y=>{const ee=Y.body.viewData,sn=Y.body;console.log("ccdata",Y.body),_(An(ee)),Vr(ee["Basic Data"]["Basic Data"].find(U=>(U==null?void 0:U.fieldName)==="Company Code").value),Br(ee.Address["Address Data"].find(U=>(U==null?void 0:U.fieldName)==="Country/Reg").value);const wt=Object.keys(ee);rr(wt);const ln=["Attachment & Documents"];T.concat(ln);const Ut=wt.map(U=>({category:U,data:ee[U],setIsEditMode:Ve}));nr(Ut),zr(ee),or(sn),console.log("mappedData",Ut,z)},o=Y=>{console.log(Y)};N(`/${I}/data/displayCostCenter`,"post",s,o,n)},jr=()=>{T[h],console.log("costCenterViewData",oe);let n=Object.entries(oe);console.log("viewDataArray",n);const s={};n.map(o=>{console.log("bottle",o[1]);let i=Object.entries(o[1]);return console.log("notebook",i),i.forEach(u=>{u[1].forEach(y=>{s[y.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("")]=y.value})}),o}),console.log("toSetArray",s),_(vn(s))},[De,qr]=x.useState(0),Mr=(n,s)=>{const o=u=>{_(te({keyName:n,data:u.body})),qr(y=>y+1)},i=u=>{console.log(u)};N(`/${I}/data/${s}`,"get",o,i)},Lr=()=>{var n,s;(s=(n=Qt)==null?void 0:n.costCenter)==null||s.map(o=>{Mr(o==null?void 0:o.keyName,o==null?void 0:o.endPoint)})},Wr=()=>{var n,s;De==((s=(n=Qt)==null?void 0:n.costCenter)==null?void 0:s.length)?k(!1):k(!0)};x.useEffect(()=>{Wr()},[De]),x.useEffect(()=>{Ar(mn("CC"))},[]),x.useEffect(()=>{Lr(),$r(),_r(),wr(),Ur()},[]),x.useEffect(()=>{oe.length!==0&&jr()},[oe]);const Me=()=>{Be(!1)},Or=()=>{pr?(ze(!1),Ue(!1)):(ze(!1),Ze("/masterDataCockpit/costCenter"))},q=()=>{je(!0);const n=Ge();F?n?(he(s=>s-1),_(Ie())):Re():(he(s=>s-1),_(Ie()))},M=()=>{const n=Ge();F?n?(he(s=>s+1),_(Ie())):Re():(he(s=>s+1),_(Ie()))},Re=()=>{Qe(!0)},Vr=n=>{console.log("compcode",n);const s=i=>{console.log("value",i),_(te({keyName:"Currency",data:i.body}))},o=i=>{console.log(i,"error in dojax")};N(`/${I}/data/getCurrency?companyCode=${n}`,"get",s,o)},_r=()=>{var o,i;const n=u=>{_(te({keyName:"HierarchyArea",data:u.body}))},s=u=>{console.log(u)};N(`/${I}/data/getHierarchyArea?controllingArea=${(i=(o=r==null?void 0:r.controllingAreaDataCopy)==null?void 0:o.newControllingAreaCopyFrom)==null?void 0:i.code}`,"get",n,s)},wr=()=>{var o,i;const n=u=>{_(te({keyName:"CompCode",data:u.body}))},s=u=>{console.log(u)};N(`/${I}/data/getCompanyCodeBasedOnControllingArea?controllingArea=${(i=(o=r==null?void 0:r.controllingAreaDataCopy)==null?void 0:o.newControllingAreaCopyFrom)==null?void 0:i.code}`,"get",n,s)},Ur=()=>{var o,i;const n=u=>{_(te({keyName:"ProfitCenter",data:u.body}))},s=u=>{console.log(u)};N(`/${I}/data/getProfitCenterAsPerControllingArea?controllingArea=${(i=(o=r==null?void 0:r.controllingAreaDataCopy)==null?void 0:o.newControllingAreaCopyFrom)==null?void 0:i.code}`,"get",n,s)},fe=()=>{Ve(!0),tr(!1)},Pe=()=>{k(!0),Qr()},et=()=>{k(!0),Zr()},tt=()=>{k(!0),Gr()},rt=()=>{k(!0),Dr()},nt=()=>{Rr()},ot=()=>{k(!0),en()},it=()=>{k(!0),tn()},$=()=>{Be(!0)},L=()=>{ze(!0)},st=()=>{var y,B,ie,se;pe(!0);const n={coArea:(B=(y=r==null?void 0:r.controllingAreaData)==null?void 0:y.newControllingArea)!=null&&B.code?(se=(ie=r==null?void 0:r.controllingAreaData)==null?void 0:ie.newControllingArea)==null?void 0:se.code:"",name:e!=null&&e.Name?e==null?void 0:e.Name.toUpperCase():""},s=E=>{var Z,le,Y;E.statusCode===201?(b("Create"),b("Create"),g("All Data has been Validated. Cost Center can be Sent for Review"),v("success"),A(!1),S(!0),L(),C(!0),Ue(!0),(n.coArea!==""||n.name!=="")&&N(`/${I}/alter/fetchCCDescriptionDupliChk`,"post",o,i,n)):(b("Error"),S(!1),g(`${(Z=E==null?void 0:E.body)!=null&&Z.message[0]?(le=E==null?void 0:E.body)==null?void 0:le.message[0]:(Y=E==null?void 0:E.body)==null?void 0:Y.value}`),v("danger"),A(!1),C(!0),$(),pe(!1))},o=E=>{E.body.length===0||!E.body.some(Z=>Z.toUpperCase()===n.name)?(pe(!1),je(!1),ne(!1)):(pe(!1),b("Duplicate Check"),S(!1),g("There is a direct match for the Cost Center name. Please change the name."),v("danger"),A(!1),C(!0),$(),je(!0))},i=E=>{console.log(E)},u=E=>{console.log(E)};N(`/${I}/alter/validateCostCenter`,"post",s,u,j)},Hr=()=>{(c==null?void 0:c.role)==="MDM Steward"&&((r==null?void 0:r.requestType)==="Create"||(m==null?void 0:m.processDesc)==="Create")?(k(!0),Kr()):(c==null?void 0:c.role)==="MDM Steward"&&((r==null?void 0:r.requestType)==="Change"||(m==null?void 0:m.processDesc)==="Change")?(k(!0),Xr()):(c==null?void 0:c.role)==="Approver"&&((r==null?void 0:r.requestType)==="Create"||(m==null?void 0:m.processDesc)==="Create")?(k(!0),Jr()):(c==null?void 0:c.role)==="Approver"&&((r==null?void 0:r.requestType)==="Change"||(m==null?void 0:m.processDesc)==="Change")&&(k(!0),Yr())},Kr=()=>{const n=o=>{k(!1),o.statusCode===200?(console.log("success"),b("Create"),g(`Cost Center Submitted for Correction with ID NCR${o.body}`),v("success"),A(!1),S(!0),L(),C(!0)):(b("Error"),S(!1),g("Failed Submitting Cost Center for Correction"),v("danger"),A(!1),C(!0),$()),Q()},s=o=>{console.log(o)};console.log("remarkssssssssss",J),N(`/${I}/alter/costCenterSendForCorrection`,"post",n,s,j)},Xr=()=>{const n=o=>{k(!1),o.statusCode===200?(console.log("success"),b("Create"),g(`Cost Center Submitted for Correction with ID NCR${o.body}`),v("success"),A(!1),S(!0),L(),C(!0)):(b("Error"),S(!1),g("Failed Submitting Cost Center for Correction"),v("danger"),A(!1),C(!0),$()),Q()},s=o=>{console.log(o)};console.log("hsdfjgdh",j),N(`/${I}/alter/changeCostCenterSendForCorrection`,"post",n,s,j)},Jr=()=>{const n=o=>{k(!1),o.statusCode===200?(console.log("success"),b("Create"),g(`Cost Center Submitted for Correction with ID NCR${o.body}`),v("success"),A(!1),S(!0),L(),C(!0)):(b("Error"),S(!1),g("Failed Submitting Cost Center for Correction"),v("danger"),A(!1),C(!0),$()),Q()},s=o=>{console.log(o)};N(`/${I}/alter/costCenterSendForReview`,"post",n,s,j)},Yr=()=>{const n=o=>{k(!1),o.statusCode===200?(console.log("success"),b("Create"),g(`Cost Center Submitted for Correction with ID NCR${o.body}`),v("success"),A(!1),S(!0),L(),C(!0)):(b("Error"),S(!1),g("Failed Submitting Cost Center for Correction"),v("danger"),A(!1),C(!0),$()),Q()},s=o=>{console.log(o)};console.log("remarksssaaaa",J),N(`/${I}/alter/changeCostCenterSendForReview`,"post",n,s,j)},lt=[{field:"id",headerName:"Document ID",flex:1,hide:!0},{field:"docType",headerName:"Type",flex:1},{field:"docName",headerName:"Document Name",flex:1},{field:"uploadedOn",headerName:"Uploaded On",flex:1,align:"center",headerAlign:"center"},{field:"uploadedBy",headerName:"Uploaded By",sortable:!1,flex:1},{field:"action",headerName:"Action",sortable:!1,filterable:!1,align:"center",headerAlign:"center",flex:1,renderCell:n=>l(K,{children:[t(Tn,{index:n.row.id,name:n.row.docName}),t(Nn,{index:n.row.id,name:n.row.docName})]})}],ct=T.map(n=>{const s=_e.filter(o=>{var i;return((i=o.category)==null?void 0:i.split(" ")[0])==(n==null?void 0:n.split(" ")[0])});if(s.length!=0)return{category:n==null?void 0:n.split(" ")[0],data:s[0].data}}).map((n,s)=>{if((n==null?void 0:n.category)=="Basic"&&h==0)return[t(d,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(n.data).map(o=>l(d,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ce},children:[t(p,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),t(w,{sx:{width:"100%"},children:t(ae,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:t(d,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:n.data[o].map(i=>(console.log("fieldDatatttt",i),t(me,{length:i.maxLength,label:i.fieldName,data:e,value:i.value,visibility:i.visibility,onSave:u=>handleFieldSave(i.fieldName,u),isEditMode:F,type:i.fieldType,field:i,taskRequestId:r==null?void 0:r.requestId})))})})})]},o))},n.category)];if((n==null?void 0:n.category)=="Control"&&h==1)return[t(d,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(n.data).map(o=>l(d,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ce},children:[t(p,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),t(w,{sx:{width:"100%"},children:t(ae,{children:t(d,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:n.data[o].map(i=>t(me,{data:e,label:i.fieldName,value:(i==null?void 0:i.value)==="X",onSave:u=>handleFieldSave(i.fieldName,u),visibility:i.visibility,isEditMode:F,type:i.fieldType,taskRequestId:r==null?void 0:r.requestId},i.fieldName))})})})]},o))},n.category)];if((n==null?void 0:n.category)=="Templates"&&h==2)return[t(d,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(n.data).map(o=>l(d,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ce},children:[t(p,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),t(w,{sx:{width:"100%"},children:t(ae,{children:t(d,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:n.data[o].map(i=>t(me,{label:i.fieldName,data:e,value:i.value,onSave:u=>handleFieldSave(i.fieldName,u),isEditMode:F,visibility:i.visibility,type:i.fieldType,taskRequestId:r==null?void 0:r.requestId},i.fieldName))})})})]},o))},n.category)];if((n==null?void 0:n.category)=="Address"&&h==3)return[t(d,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(n.data).map(o=>l(d,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ce},children:[t(p,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),t(w,{sx:{width:"100%"},children:t(ae,{children:t(d,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:n.data[o].map(i=>t(me,{label:i.fieldName,data:e,value:i.value,onSave:u=>handleFieldSave(i.fieldName,u),isEditMode:F,type:i.fieldType,visibility:i.visibility,taskRequestId:r==null?void 0:r.requestId},i.fieldName))})})})]},o))},n.category)];if((n==null?void 0:n.category)=="Communication"&&h==4)return[t(d,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(n.data).map(o=>l(d,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ce},children:[t(p,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),t(w,{sx:{width:"100%"},children:t(ae,{children:t(d,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:n.data[o].map(i=>t(me,{label:i.fieldName,data:e,value:i.value,onSave:u=>handleFieldSave(i.fieldName,u),isEditMode:F,visibility:i.visibility,type:i.fieldType,taskRequestId:r==null?void 0:r.requestId},i.fieldName))})})})]},o))},n.category)];if((n==null?void 0:n.category)=="Attachments"&&h==5)return[t(K,{children:F?l(K,{children:[t(kn,{title:"CostCenter",useMetaData:!1,artifactId:ue,artifactName:"CostCenter"}),l(Se,{sx:{padding:"1rem 1rem 0rem 1rem"},children:[t(d,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:t(p,{variant:"h6",children:t("strong",{children:"Attachments"})})}),!!D.length&&t(Ht,{width:"100%",rows:D,columns:lt,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action"}),!D.length&&t(p,{variant:"body2",children:"No Attachments Found"}),t("br",{}),t(p,{variant:"h6",children:"Comments"}),!!R.length&&t(Timeline,{sx:{[`& .${timelineItemClasses.root}:before`]:{flex:0,padding:0}},children:R.map(o=>l(Zt,{children:[l(Gt,{children:[t(Dt,{children:t(Kt,{sx:{color:"#757575"}})}),t(Rt,{})]}),t(Pt,{sx:{py:"12px",px:2},children:t(Se,{elevation:0,sx:{border:1,borderColor:"#C4C4C4",borderRadius:"8px",width:"650px"},children:t(w,{sx:{padding:"1rem"},children:l(X,{spacing:1,children:[t(d,{sx:{display:"flex",justifyContent:"space-between"},children:t(p,{sx:{textAlign:"right",color:" #757575",fontWeight:"500",fontSize:"12px"},children:Ae(o.createdAt).format("DD MMM YYYY")})}),t(p,{sx:{fontSize:"12px",color:" #757575",fontWeight:"500"},children:o.user}),t(p,{sx:{fontSize:"12px",color:"#1D1D1D",fontWeight:"600"},children:o.comment})]})})})})]}))}),!R.length&&t(p,{variant:"body2",children:"No Comments Found"}),t("br",{})]})]}):l(Se,{sx:{padding:"1rem 1rem 0rem 1rem"},children:[t(d,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:t(p,{variant:"h6",children:t("strong",{children:"Attachments"})})}),!!D.length&&t(Ht,{width:"100%",rows:D,columns:lt,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action"}),!D.length&&t(p,{variant:"body2",children:"No Attachments Found"}),t("br",{}),t(p,{variant:"h6",children:"Comments"}),!!R.length&&t(Timeline,{sx:{[`& .${timelineItemClasses.root}:before`]:{flex:0,padding:0}},children:R.map(o=>l(Zt,{children:[l(Gt,{children:[t(Dt,{children:t(Kt,{sx:{color:"#757575"}})}),t(Rt,{})]}),t(Pt,{sx:{py:"12px",px:2},children:t(Se,{elevation:0,sx:{border:1,borderColor:"#C4C4C4",borderRadius:"8px",width:"650px"},children:t(w,{sx:{padding:"1rem"},children:l(X,{spacing:1,children:[t(d,{sx:{display:"flex",justifyContent:"space-between"},children:t(p,{sx:{textAlign:"right",color:" #757575",fontWeight:"500",fontSize:"12px"},children:Ae(o.createdAt).format("DD MMM YYYY")})}),t(p,{sx:{fontSize:"12px",color:" #757575",fontWeight:"500"},children:o.user}),t(p,{sx:{fontSize:"12px",color:"#1D1D1D",fontWeight:"600"},children:o.comment})]})})})})]}))}),!R.length&&t(p,{variant:"body2",children:"No Comments Found"}),t("br",{})]})})]}),Qr=()=>{const n=o=>{k(!1),o.statusCode===200?(console.log("success"),b("Create"),g(`Cost Center Submitted for Approval with ID CCR${o.body}`),v("success"),A(!1),S(!0),L(),C(!0)):(b("Approve"),S(!1),g("Failed Submitting Cost Center"),v("danger"),A(!1),C(!0),$()),handleClose()},s=o=>{console.log(o)};N(`/${I}/alter/changeCostCenterApprovalSubmit`,"post",n,s,j)},Zr=()=>{const n=o=>{k(!1),o.statusCode===200?(console.log("success"),b("Create"),g(`Cost Center Submitted for Approval with ID NCR${o.body}`),v("success"),A(!1),S(!0),L(),C(!0)):(b("Error"),S(!1),g("Failed Submitting Cost Center"),v("danger"),A(!1),C(!0),$()),handleClose()},s=o=>{console.log(o)};N(`/${I}/alter/costCenterApprovalSubmit`,"post",n,s,j)},Gr=()=>{const n=o=>{if(k(!1),o.statusCode===200){console.log("success"),b("Create"),g(`Cost Center Submitted For Review with ID NCS${o.body} `),v("success"),A(!1),S(!0),L(),C(!0);const i={artifactId:ue,createdBy:c==null?void 0:c.emailId,artifactType:"CostCenter",requestId:`NCS${o==null?void 0:o.body}`},u=B=>{console.log("Second API success",B)},y=B=>{console.error("Second API error",B)};N(`/${Oe}/documentManagement/updateDocRequestId`,"post",u,y,i)}else b("Error"),S(!1),g("Failed Submitting Cost Center"),v("danger"),A(!1),C(!0),$();handleClose()},s=o=>{console.log(o)};N(`/${I}/alter/costCenterSubmitForReview`,"post",n,s,j)},Dr=()=>{const n=o=>{k(!1),o.statusCode===200?(console.log("success"),b("Create"),g(`Cost Center Submitted for Review with ID NCR${o.body} `),v("success"),A(!1),S(!0),L(),C(!0)):(b("Error"),S(!1),g("Failed Saving the Data"),v("danger"),A(!1),C(!0),$()),handleClose()},s=o=>{console.log(o)};N(`/${I}/alter/costCenterSubmitForReview`,"post",n,s,j)},Rr=()=>{v(!1),$(),b("Confirm"),g("Do You Want to Save as Draft ?"),qe(!0),Sr("proceed")},Pr=()=>{Me(),k(!0);const n=o=>{if(k(!1),o.statusCode===200){console.log("success"),b("Create"),g(`Cost Center Saved As Draft with ID NCS${o.body} `),v("success"),A(!1),S(!0),L(),C(!0);const i={artifactId:ue,createdBy:c==null?void 0:c.emailId,artifactType:"CostCenter",requestId:`NCS${o==null?void 0:o.body}`},u=B=>{console.log("Second API success",B)},y=B=>{console.error("Second API error",B)};N(`/${Oe}/documentManagement/updateDocRequestId`,"post",u,y,i)}else b("Error"),S(!1),g("Failed Saving Cost Center"),v("danger"),A(!1),C(!0),$();handleClose()},s=o=>{console.log(o)};N(`/${I}/alter/costCenterAsDraft`,"post",n,s,j)},en=()=>{const n=o=>{k(!1),o.statusCode===201?(console.log("success"),b("Create"),g(`${o.message}`),v("success"),A(!1),S(!0),L(),C(!0)):(b("Error"),S(!1),g("Failed Approving Cost Center"),v("danger"),A(!1),C(!0),$()),handleClose()},s=o=>{console.log(o)};N(`/${I}/alter/changeCostCenterApproved`,"post",n,s,j)},tn=()=>{const n=o=>{k(!1),o.statusCode===201?(console.log("success"),b("Create"),g(`${o.message}`),v("success"),A(!1),S(!0),L(),C(!0)):(b("Error"),S(!1),g("Failed Approving the Cost Center"),v("danger"),A(!1),C(!0),$()),handleClose()},s=o=>{console.log(o)};N(`/${I}/alter/createCostCenterApproved`,"post",n,s,j)},rn=()=>{Je(!0)},be=()=>{Je(!1)},ge=()=>{ne(!1),Ke(!0)},Q=()=>{ne(!0),Ke(!1)},Le=(n,s)=>{const o=n.target.value;if(o.length>0&&o[0]===" ")He(o.trimStart());else{let i=o.toUpperCase();He(i)}},ve=()=>{ne(!0),Xe(!1)},nn=()=>{ne(!1),Xe(!0)},on=()=>{k(!0);const n=o=>{if(k(!1),o.statusCode===200){b("Create"),g(`Cost Center has been Submitted for review NCS${o.body}`),qe(!1),v("success"),A(!1),S(!0),L(),C(!0),ve();const i={artifactId:ue,createdBy:c==null?void 0:c.emailId,artifactType:"CostCenter",requestId:`NCS${o==null?void 0:o.body}`},u=B=>{console.log("Second API success",B)},y=B=>{console.error("Second API error",B)};N(`/${Oe}/documentManagement/updateDocRequestId`,"post",u,y,i)}else b("Create"),S(!1),g("Creation Failed"),qe(!1),v("danger"),A(!1),C(!0),$();handleClose()},s=o=>{console.log(o)};N(`/${I}/alter/costCenterSubmitForReview`,"post",n,s,j)},at=()=>{be(),on()};return console.log("factorsarray",T),t(K,{children:ar===!0?t(hn,{}):l("div",{style:{backgroundColor:"#FAFCFF"},children:[t(pn,{dialogState:mr,openReusableDialog:$,closeReusableDialog:Me,dialogTitle:sr,dialogMessage:we,handleDialogConfirm:Me,dialogOkText:"OK",dialogSeverity:cr,showCancelButton:!0,handleDialogReject:()=>{Be(!1)},handleExtraText:vr,handleExtraButton:Pr,showExtraButton:fr}),lr&&t(Xt,{openSnackBar:hr,alertMsg:we,handleSnackBarClose:Or}),Ye.length!=0&&t(Xt,{openSnackBar:Tr,alertMsg:"Please fill the following Field: "+Ye.join(", "),handleSnackBarClose:Er}),l(Ce,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:ur,onClose:Q,children:[t(d,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:t(d,{item:!0,children:l(ke,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(p,{variant:"h6",children:"Remarks"}),t(Te,{sx:{width:"max-content"},onClick:Q,children:t(We,{})})]})})}),t(Ne,{sx:{padding:".5rem 1rem"},children:t(X,{children:t(w,{sx:{minWidth:400},children:t(G,{sx:{height:"auto"},fullWidth:!0,children:t(de,{sx:{backgroundColor:"#F5F5F5"},onChange:Le,multiline:!0,value:J,placeholder:"Enter Remarks for Correction",inputProps:{maxLength:254}})})})})}),l(Fe,{sx:{display:"flex",justifyContent:"end"},children:[t(a,{sx:{width:"max-content",textTransform:"capitalize"},onClick:Q,children:"Cancel"}),t(a,{className:"button_primary--normal",type:"save",onClick:Hr,variant:"contained",children:"Submit"})]})]}),l(Ce,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:xr,onClose:ve,children:[l(ke,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(p,{variant:"h6",children:"Remarks"}),t(Te,{sx:{width:"max-content"},onClick:ve,children:t(We,{})})]}),t(Ne,{sx:{padding:".5rem 1rem"},children:t(X,{children:t(w,{sx:{minWidth:400},children:t(G,{sx:{height:"auto"},fullWidth:!0,children:t(de,{sx:{backgroundColor:"#F5F5F5"},value:J,onChange:Le,multiline:!0,placeholder:"Enter Remarks",inputProps:{maxLength:254}})})})})}),l(Fe,{sx:{display:"flex",justifyContent:"end"},children:[t(a,{sx:{width:"max-content",textTransform:"capitalize"},onClick:ve,children:"Cancel"}),t(a,{className:"button_primary--normal",type:"save",onClick:at,variant:"contained",children:"Submit"})]})]}),l(Ce,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:gr,onClose:be,children:[l(ke,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(p,{variant:"h6",children:"Remarks"}),t(Te,{sx:{width:"max-content"},onClick:be,children:t(We,{})})]}),t(Ne,{sx:{padding:".5rem 1rem"},children:t(X,{children:t(w,{sx:{minWidth:400},children:t(G,{sx:{height:"auto"},fullWidth:!0,children:t(de,{sx:{backgroundColor:"#F5F5F5"},onChange:Le,value:J,multiline:!0,placeholder:"Enter Remarks",inputProps:{maxLength:254}})})})})}),l(Fe,{sx:{display:"flex",justifyContent:"end"},children:[t(a,{sx:{width:"max-content",textTransform:"capitalize"},onClick:be,children:"Cancel"}),t(a,{className:"button_primary--normal",type:"save",onClick:at,variant:"contained",children:"Submit"})]})]}),t(un,{sx:{color:"#fff",zIndex:n=>n.zIndex.drawer+1},open:dr,children:t(xn,{color:"inherit"})}),l(d,{container:!0,sx:fn,children:[l(d,{item:!0,md:12,style:{padding:"16px",display:"flex"},children:[l(d,{md:9,sx:{display:"flex"},children:[t(d,{children:t(Te,{color:"primary","aria-label":"upload picture",component:"label",sx:bn,children:t(Cn,{sx:{fontSize:"25px",color:"#000000"},onClick:()=>{Ze("/masterDataCockpit/costCenter")}})})}),l(d,{children:[F?l(d,{item:!0,md:12,children:[t(p,{variant:"h3",children:t("strong",{children:"Create Cost Center: "})}),t(p,{variant:"body2",color:"#777",children:"This view creates Cost Center from existing Cost Center"})]}):"",re?l(d,{item:!0,md:12,children:[t(p,{variant:"h3",children:t("strong",{children:"Display Cost Center "})}),t(p,{variant:"body2",color:"#777",children:"This view displays the details of the Cost Center"})]}):""]})]}),t(d,{md:3,sx:{display:"flex",justifyContent:"flex-end"},gap:2,children:ye(xe,"Cost Center","ChangeCC")&&((c==null?void 0:c.role)==="Super User"&&(r!=null&&r.requestType)&&re?t(d,{gap:1,sx:{display:"flex"},children:t(d,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:t(K,{children:t(d,{item:!0,children:l(a,{variant:"outlined",size:"small",sx:W,onClick:fe,children:["Fill Details",t(Ee,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(c==null?void 0:c.role)==="Finance"&&(r!=null&&r.requestType)&&re?t(d,{gap:1,sx:{display:"flex"},children:t(d,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:t(K,{children:t(d,{item:!0,children:l(a,{variant:"outlined",size:"small",sx:W,onClick:fe,children:["Fill Details",t(Ee,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(c==null?void 0:c.role)==="Super User"&&!(r!=null&&r.requestType)&&re?t(d,{gap:1,sx:{display:"flex"},children:t(d,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:t(K,{children:t(d,{item:!0,children:l(a,{variant:"outlined",size:"small",sx:W,onClick:fe,children:["Change",t(Ee,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(c==null?void 0:c.role)==="Finance"&&!(r!=null&&r.requestType)&&re?t(d,{gap:1,sx:{display:"flex"},children:t(d,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:t(K,{children:t(d,{item:!0,children:l(a,{variant:"outlined",size:"small",sx:W,onClick:fe,children:["Change",t(Ee,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):"")})]}),l(d,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:[l(d,{item:!0,md:10,sx:{marginLeft:"40px"},children:[t(d,{item:!0,sx:{paddingTop:"2px !important"},children:l(X,{flexDirection:"row",children:[t("div",{style:{width:"12%"},children:t(p,{variant:"body2",color:"#777",children:"Cost Center"})}),l(p,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[":"," ",`${(Et=(It=r==null?void 0:r.companyCode)==null?void 0:It.newCompanyCode)==null?void 0:Et.code}${(Bt=r==null?void 0:r.costCenterName)==null?void 0:Bt.newCostCenterName}`?`${($t=(zt=r==null?void 0:r.companyCode)==null?void 0:zt.newCompanyCode)==null?void 0:$t.code}${(jt=r==null?void 0:r.costCenterName)==null?void 0:jt.newCostCenterName}`:""]})]})}),t(d,{item:!0,sx:{paddingTop:"2px !important"},children:l(X,{flexDirection:"row",children:[t("div",{style:{width:"12%"},children:t(p,{variant:"body2",color:"#777",children:"Controlling Area"})}),l(p,{variant:"body2",fontWeight:"bold",children:[":"," ",(Mt=(qt=r==null?void 0:r.controllingAreaData)==null?void 0:qt.newControllingArea)!=null&&Mt.code?(Wt=(Lt=r==null?void 0:r.controllingAreaData)==null?void 0:Lt.newControllingArea)==null?void 0:Wt.code:""]})]})})]}),l(d,{item:!0,md:2,sx:{marginLeft:"40px"},children:[t(d,{item:!0,sx:{paddingTop:"2px !important"},children:l(X,{flexDirection:"row",children:[t("div",{style:{width:"50%"},children:t(p,{variant:"body2",color:"#777",children:"Valid From"})}),l(p,{variant:"body2",fontWeight:"bold",children:[":"," ",Ae((Ot=r==null?void 0:r.validFromDate)==null?void 0:Ot.newValidFromDate).format(P==null?void 0:P.dateFormat)]})]})}),t(d,{item:!0,sx:{paddingTop:"2px !important"},children:l(X,{flexDirection:"row",children:[t("div",{style:{width:"50%"},children:t(p,{variant:"body2",color:"#777",children:"Valid To"})}),l(p,{variant:"body2",fontWeight:"bold",children:[":"," ",Ae((Vt=r==null?void 0:r.validToDate)==null?void 0:Vt.newValidToDate).format(P==null?void 0:P.dateFormat)]}),t(p,{variant:"body2",fontWeight:"bold"})]})})]})]}),l(d,{container:!0,style:{marginLeft:25},children:[t(Fn,{activeStep:h,sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},"aria-label":"mui tabs example",children:T.map((n,s)=>t(yn,{children:t(In,{sx:{fontWeight:"700"},children:n})},n))}),ct&&((_t=ct[h])==null?void 0:_t.map((n,s)=>t(w,{sx:{mb:2,width:"100%"},children:t(p,{variant:"body2",children:n})},s)))]})]}),l(d,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:[ye(xe,"Cost Center","ChangeCC")&&(!(r!=null&&r.requestType)&&!F?t(O,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:l(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:q,disabled:h===0,children:"Back"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:M,disabled:h===T.length-1,children:"Next"})]})}):""),ye(xe,"Cost Center","ChangeCC")&&((c==null?void 0:c.role)==="Super User"&&!(r!=null&&r.requestType)&&F?t(O,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:l(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t(a,{variant:"contained",size:"small",submit:!0,for:!0,review:!0,sx:{...f,mr:1},onClick:nt,children:"Save As Draft"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:q,disabled:h===0,children:"Back"}),h===T.length-1?l(K,{children:[t(a,{variant:"outlined",size:"small",sx:{button_Outlined:W,mr:1},onClick:st,children:"Validate"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:nn,children:"Submit For Review"})]}):t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:M,disabled:h===T.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Finance"&&!(r!=null&&r.requestType)&&F?t(O,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:l(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:nt,children:"Save As Draft"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:q,disabled:h===0,children:"Back"}),h===T.length-1?l(K,{children:[t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:st,children:"Validate"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:rn,disabled:$e,children:"Submit For Review"})]}):t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:M,disabled:h===T.length-1,children:"Next"})]})}):""),ye(xe,"Cost Center","ChangeCC")&&((c==null?void 0:c.role)==="Super User"&&(r==null?void 0:r.requestType)==="Create"&&!F?t(O,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:l(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t(a,{variant:"contained",size:"small",sx:{button_Outlined:W,mr:1},onClick:it,children:"Approve"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:et,children:"Submit For Approval"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:q,disabled:h===0,children:"Back"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:M,disabled:h===T.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Super User"&&(r==null?void 0:r.requestType)==="Change"&&!F?t(O,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:l(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t(a,{variant:"contained",size:"small",sx:{button_Outlined:W,mr:1},onClick:ot,children:"Approve"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:Pe,children:"Submit For Approval"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:q,disabled:h===0,children:"Back"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:M,disabled:h===T.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="MDM Steward"&&((r==null?void 0:r.requestType)==="Create"||(m==null?void 0:m.processDesc)==="Create")&&!F?t(O,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:l(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t(a,{variant:"outlined",size:"small",sx:{button_Outlined:W,mr:1},onClick:ge,children:"Correction"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:et,children:"Submit For Approval"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:q,disabled:h===0,children:"Back"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:M,disabled:h===T.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="MDM Steward"&&((r==null?void 0:r.requestType)==="Change"||(m==null?void 0:m.processDesc)==="Change")&&!F?t(O,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:l(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t(a,{variant:"outlined",size:"small",sx:{button_Outlined:W,mr:1},onClick:ge,children:"Correction"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:Pe,children:"Submit For Approval"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:q,disabled:h===0,children:"Back"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:M,disabled:h===T.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Approver"&&((r==null?void 0:r.requestType)==="Create"||(m==null?void 0:m.processDesc)==="Create")&&!F?t(O,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:l(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t(a,{variant:"outlined",size:"small",sx:{button_Outlined:W,mr:1},onClick:ge,children:"Correction"}),t(a,{variant:"contained",size:"small",sx:{button_Outlined:W,mr:1},onClick:it,children:"Approve"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:q,disabled:h===0,children:"Back"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:M,disabled:h===T.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Approver"&&((r==null?void 0:r.requestType)==="Change"||(m==null?void 0:m.processDesc)==="Change")&&!F?t(O,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:l(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t(a,{variant:"outlined",size:"small",sx:{button_Outlined:W,mr:1},onClick:ge,children:"Correction"}),t(a,{variant:"contained",size:"small",sx:{button_Outlined:W,mr:1},onClick:ot,children:"Approve"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:q,disabled:h===0,children:"Back"}),t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:M,disabled:h===T.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Super User"&&(r==null?void 0:r.requestType)==="Create"&&F?t(O,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:l(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:q,disabled:h===0,children:"Back"}),h===T.length-1?t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:rt,children:"Submit For Review"}):t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:M,disabled:h===T.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Super User"&&(r==null?void 0:r.requestType)==="Change"&&F?t(O,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:l(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:q,disabled:h===0,children:"Back"}),h===T.length-1?t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:tt,children:"Submit For Review"}):t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:M,disabled:h===T.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Finance"&&((r==null?void 0:r.requestType)==="Create"||(m==null?void 0:m.processDesc)==="Create")&&F?t(O,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:l(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:q,disabled:h===0,children:"Back"}),h===T.length-1?t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:rt,disabled:$e,children:"Submit For Review"}):t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:M,disabled:h===T.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Finance"&&((r==null?void 0:r.requestType)==="Change"||(m==null?void 0:m.processDesc)==="Change")&&F?t(O,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:l(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:q,disabled:h===0,children:"Back"}),h===T.length-1?t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:tt,disabled:$e,children:"Submit For Review"}):t(a,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:M,disabled:h===T.length-1,children:"Next"})]})}):"")]}),l(Ce,{open:ir,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[t(ke,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:t(p,{variant:"h6",children:"New Cost Center"})}),t(Ne,{sx:{padding:".5rem 1rem"},children:l(d,{container:!0,spacing:1,children:[l(d,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[l(p,{children:["Cost Center",t("span",{style:{color:"red"},children:"*"})]}),t(G,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px"},children:t(de,{sx:{fontSize:"12px !important",height:"31px"},fullWidth:!0,size:"small",placeholder:"Enter Cost Center Name",required:!0})})]}),l(d,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[l(p,{children:["Controlling Area",t("span",{style:{color:"red"},children:"*"})]}),t(G,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px"},children:t(gn,{sx:{height:"42px"},required:"true",size:"small",renderInput:n=>t(de,{sx:{fontSize:"12px !important"},...n,variant:"outlined",placeholder:"Select Cost Center"})})})]}),l(d,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[l(p,{children:["Valid From",t("span",{style:{color:"red"},children:"*"})]}),t(G,{fullWidth:!0,sx:{margin:".5em 0px"},children:t(Jt,{dateAdapter:Yt,children:t(er,{slotProps:{textField:{size:"small"}}})})})]}),l(d,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[l(p,{children:["Valid To",t("span",{style:{color:"red"},children:"*"})]}),t(G,{fullWidth:!0,sx:{margin:".5em 0px"},children:t(Jt,{dateAdapter:Yt,children:t(er,{slotProps:{textField:{size:"small"}}})})})]})]})}),l(Fe,{sx:{display:"flex",justifyContent:"end"},children:[t(a,{sx:{width:"max-content",textTransform:"capitalize"},children:"Cancel"}),t(a,{className:"button_primary--normal",type:"save",variant:"contained",children:"Proceed"})]})]})]})})};export{ao as default};
