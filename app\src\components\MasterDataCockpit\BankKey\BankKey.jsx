import React from "react";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import DownloadIcon from "@mui/icons-material/Download";
import CloseIcon from "@mui/icons-material/Close";

import { IosShare, Refresh } from "@mui/icons-material";
import {
  Button,
  Checkbox,
  Grid,
  Paper,
  IconButton,
  Typography,
  TextField,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  BottomNavigation,
  tooltipClasses,
  Autocomplete,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  ButtonGroup,
  Popper,
  ClickAwayListener,
  MenuList,
  Divider,
  Backdrop,
  CircularProgress
} from "@mui/material";
import {
  DateField,
  DatePicker,
  DesktopDatePicker,
  DesktopDateTimePicker,
  LocalizationProvider,
} from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import FileUpload from "../../Common/FileUpload";
import moment from "moment/moment";
import { Stack } from "@mui/system";
import Select from "@mui/material/Select";
import { FormControl, MenuItem } from "@mui/material";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import SearchBar from "../../Common/SearchBar";
import ReusableDialog from "../../Common/ReusableDialog";
import { useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import styled from "@emotion/styled";
import html2canvas from "html2canvas";
import {
  commonFilterClear,
  commonFilterUpdate,
} from "../../../app/commonFilterSlice";
import { v4 as uuidv4 } from "uuid";
import {
  button_Marginleft,
  button_Outlined,
  button_Primary,
  container_filter,
  font_Small,
  iconButton_SpacingSmall,
  outerContainer_Information,
  outermostContainer,
  outermostContainer_Information,
} from "../../Common/commonStyles";
import {
  destination_BankKey,
  destination_CostCenter,
  destination_MaterialMgmt,
  destination_ProfitCenter,
} from "../../../destinationVariables";
import { doAjax } from "../../Common/fetchService";
import ClearIcon from "@mui/icons-material/Clear";
import ReusableTable from "../../common/ReusableTable";
import { setDropDown } from "../../../app/dropDownDataSlice";
import { checkIwaAccess, saveExcel } from "../../../functions";

// import "./masterDataCockpit.css";
import {
  setCostCenterAddressTab,
  setCostCenterBasicDataTab,
  setCostCenterCommunicationTab,
  setCostCenterControlTab,
  setCostCenterHistoryTab,
  setCostCenterTemplatesTab,
} from "../../../app/costCenterTabsSlice";
import {
  setBankKeyAddressData,
  setBankKeyBankData,
  setMultipleBankKeyData,
  setHandleMassMode,
} from "../../../app/bankKeyTabSlice";
import DateRange from "../../Common/DateRangePicker";
import ReusableIcon from "../../Common/ReusableIcon";
import AttachmentUploadDialog from "../../Common/AttachmentUploadDialog";
import LoadingComponent from "../../Common/LoadingComponent";
const BankKey = () => {
  const [snackbar, setSnackbar] = useState(false);
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [messageDialogSeverity, setMessageDialogSeverity] = useState("");
  const appSettings = useSelector((state) => state.appSettings["Format"]);
  const [newBankKey, setNewBankKey] = useState("");
  const [newBankCtryReg, setNewBankCtryReg] = useState("");
  const [newBankCountryCopyFrom, setNewBankCountryCopyFrom] = useState("");
  const [bankCountryDialog, setBankCountryDialog] = useState("");
  const [countyValid, setCountryValid] = useState(false);
  const [bankKeyValid, setBankKeyValid] = useState(false);
  const [successMsg, setsuccessMsg] = useState(false);
  const [tableLoading, setTableLoading] = useState(false);
  const [blurLoading, setBlurLoading] = useState(false);
  // let userData = useSelector((state) => state.userManagement.userData);
  // let iwaAccessData = useSelector(
  //   (state) => state.userManagement.entitiesAndActivities?.["Return Order"]
  // );
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [count, setCount] = useState(0);
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [skip, setSkip] = useState(0);
  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };
  const handlePageSizeChange = (event) => {
    const newPageSize = event.target.value;
    setPageSize(newPageSize);
    setPage(0);
    setSkip(0);
  };
  const ITEM_HEIGHT = 48;
  const ITEM_PADDING_TOP = 8;
  const MenuProps = {
    PaperProps: {
      style: {
        maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
        width: 250,
      },
    },
  };
  const handleOnClick = (materialNumber) => {
    navigate(
      "/masterDataCockpit/materialMaster/displayMaterialDetail/" +
      materialNumber
    );
  };

  const [isLoading, setIsLoading] = useState(false);
  const [value, setValue] = useState(null);
  const ariaLabel = { "aria-label": "description" };
  const [rmDataRows, setRmDataRows] = useState([]);
  const [UserName, setUserName] = React.useState("");
  const [openSnackBaraccept, setOpenSnackBaraccept] = useState(false);
  const [confirmingid, setConfirmingid] = useState("");
  const [materialNumber, setMaterialNumber] = useState("");
  const [confirmStatus, setConfirmStatus] = useState(true);
  const [messageDialogExtra, setMessageDialogExtra] = useState(false);
  const [messageDialogOK, setMessageDialogOK] = useState(true);
  const [companyCodeSet, setCompanyCodeSet] = useState([]);
  const [plantCodeSet, setPlantCodeSet] = useState([]);
  const [vendorDetailsSet, setVendorDetailsSet] = useState([]);
  const [taskstatusSet, setTasksttusSet] = useState([]);
  const [disableButton, setDisableButton] = useState(true);
  const [selectedRow, setSelectedRow] = useState([]);
  const [selectedDetails, setSelectedDetails] = useState([]);
  const [downloadError, setdownloadError] = useState(false);
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [displayedFields, setDisplayedFields] = useState([]);
  const [filterFieldData, setFilterFieldData] = useState({});
  const [ctrRegion, setCtrRegion] = useState([]);
  const [matGroup, setMatGroup] = useState([]);
  const [viewDetailpage, setViewDetailpage] = useState(false);
  const [matNumber, setMatNumber] = useState([]);
  const [dynamicOptions, setDynamicOptions] = useState([]);
  const [plantForWarehouse, setPlantForWarehouse] = useState([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogOpenCreate, setDialogOpenCreate] = useState(false);
  const [fullWidth, setFullWidth] = useState(true);
  const [maxWidth, setMaxWidth] = useState("sm");
  const [enableDocumentUpload, setEnableDocumentUpload] = useState(false);
  const [newCityRegion, setNewCityRegion] = useState("");
  const [isValidationError, setIsValidationError] = useState(false);
  const [checkValidationBankKey, setCheckValidationBankKey] = useState(false);
  // const [handleMassMode, setHandleMassMode] = useState("");
  const [openButton, setOpenButton] = useState(false);
  const anchorRefCreate = React.useRef(null);
  const anchorRef = React.useRef(null);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [openButtonCreate, setOpenButtonCreate] = useState(false);
  const [openButtonChange, setOpenButtonChange] = useState(false);
  const anchorRefChange = React.useRef(null);
  const [selectedIndexCreate, setSelectedIndexCreate] = useState(0);
  const [selectedIndexChange, setSelectedIndexChange] = useState(0);
  const dropdownData = useSelector((state) => state.AllDropDown.dropDown);
  const [newBankKeyValid, setNewBankKeyValid] = useState(false)

  console.log("dropdownData", dropdownData);
  const options = ["Create Multiple", "Upload Template ", "Download Template "];
  // const optionsCreateSingle = ["Create Single", "With Copy", "Without Copy"];
  const optionsChange = ["Change Multiple", "Upload Template ", "Download Template "];
  const rmSearchForm = useSelector((state) => state.commonFilter["BankKey"]);
  const formcontroller_SearchBar = useSelector(
    (state) => state.commonSearchBar["BankKey"]
  );
  console.log("formcontroller_SearchBar", formcontroller_SearchBar);
  console.log("rmSearchForm", rmSearchForm);
  const dropDownData = useSelector((state) => state?.AllDropDown?.dropDown);

  let iwaAccessData = useSelector(
    (state) => state.userManagement.entitiesAndActivities?.["Bank Key"]
  );
  let userData = useSelector((state) => state.userManagement.userData);
  const handleMassModeBK = useSelector(
    (state) => state.bankKey.handleMassMode
  );

  const handleDialogClickOpen = () => {
    setDialogOpen(true);
  };
  const handleDialogClickOpenWithCopy = () => {
    setDialogOpenCreate(true);
  };
  const handleDialogProceed = () => {
    if ((newBankCtryReg?.code === undefined || newBankCtryReg?.code === '') || (newBankKey === undefined || newBankKey === '')) { //chiranjit
      setNewBankKeyValid(false)
      setIsValidationError(true)
      return;
    }
    else {
      // if (newBankKey.length !== 15) {
      //   setNewBankKeyValid(true)
      //   setIsValidationError(false)
      //   return;
      //   //duplicateCheck()
      // } else {

        setNewBankKeyValid(false)
        duplicateCheck();
      // }
      setIsValidationError(false)
    } 

  };
  const handleDialogClose = () => {
    setDialogOpen(false);
    setCountryValid(false);
  };
  const handleBankName = (e) => {
    if (e.target.value !== null) {
      var tempBankName = e.target.value;

      let tempFilterData = {
        ...rmSearchForm,
        bankName: tempBankName,
      };
      dispatch(
        commonFilterUpdate({
          module: "BankKey",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleBankKey = (e) => {
    if (e.target.value !== null) {
      var tempBankKey = e.target.value;

      let tempFilterData = {
        ...rmSearchForm,
        bankKey: tempBankKey,
      };
      dispatch(
        commonFilterUpdate({
          module: "BankKey",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleBankBranch = (e) => {
    if (e.target.value !== null) {
      var tempBankBranch = e.target.value;

      let tempFilterData = {
        ...rmSearchForm,
        bankBranch: tempBankBranch,
      };
      dispatch(
        commonFilterUpdate({
          module: "BankKey",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleSwiftBic = (e) => {
    if (e.target.value !== null) {
      var tempSwiftBic = e.target.value;

      let tempFilterData = {
        ...rmSearchForm,
        swiftBic: tempSwiftBic,
      };
      dispatch(
        commonFilterUpdate({
          module: "BankKey",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleBankNumber = (e) => {
    if (e.target.value !== null) {
      var tempBankNumbdr = e.target.value;

      let tempFilterData = {
        ...rmSearchForm,
        bankNumber: tempBankNumbdr,
      };
      dispatch(
        commonFilterUpdate({
          module: "BankKey",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleCtrRegion = (e, value) => {
    if (true) {
      var tempCtrRegion = value;

      let tempFilterData = {
        ...rmSearchForm,
        bankCtrRegion: tempCtrRegion,
      };
      dispatch(
        commonFilterUpdate({
          module: "BankKey",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleCreatedBy = (e, value) => {
    if (true) {
      var tempCreatedBy = e.target.value;

      let tempFilterData = {
        ...rmSearchForm,
        createdBy: tempCreatedBy,
      };
      dispatch(
        commonFilterUpdate({
          module: "BankKey",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleChangedBy = (e, value) => {
    if (true) {
      var tempChangedBy = value;

      let tempFilterData = {
        ...rmSearchForm,
        changedBy: tempChangedBy,
      };
      dispatch(
        commonFilterUpdate({
          module: "BankKey",
          filterData: tempFilterData,
        })
      );
    }
  };
  const sendNewBankKeyData = {
    bankKey: { newBankKey },
    bankCtryReg: { newBankCtryReg },
    // validFromDate: { newValidFromDate },
    // validToDate: { newValidToDate },
  };
  const functions_ExportAsExcel = {
    convertJsonToExcel: () => {
      let excelColumns = [];
      columns.forEach((item) => {
        if (item.headerName.toLowerCase() !== "action" && !item.hide) {
          excelColumns.push({ header: item.headerName, key: item.field });
        }
      });
      saveExcel({
        fileName: `Bank Key Data-${moment(presentDate).format("DD-MMM-YYYY")}`,
        columns: excelColumns,
        rows: rmDataRows,
      });
    },
    button: () => {
      return (
        <Button
          sx={{
            textTransform: "capitalize",
            position: "absolute",
            right: 0,
            top: 0,
          }}
          onClick={() => functions_ExportAsExcel.convertJsonToExcel()}
        >
          Download
        </Button>
      );
    },
  };
  const duplicateCheck = () => {
    setBlurLoading(true);
    let selectedBankKey = sendNewBankKeyData?.bankKey?.newBankKey;
    let selectedBankCtryReg =
      sendNewBankKeyData?.bankCtryReg?.newBankCtryReg?.code;
    let result = selectedBankKey.concat("$$", selectedBankCtryReg )
    console.log("sendNewBankKeyData", sendNewBankKeyData);
    // let ctrlAreaCCToCheck = (sendNewCostCenterData?.controllingAreaData?.newControllingArea).join(sendNewCostCenterData?.costCenterName?.newCostCenterName)
    // console.log("ctrlAreaCCToCheck",(sendNewCostCenterData?.controllingAreaData?.newControllingArea).join(sendNewCostCenterData?.costCenterName?.newCostCenterName));
    const hSuccess = (data) => {
      
      console.log("dupli", data);
      if (data.body.length > 0) {
        setBlurLoading(false);
        setCheckValidationBankKey(true);
      } else {
        navigate("/masterDataCockpit/bankKey/newSingleBankKey", {
          state: sendNewBankKeyData,
        });
      }
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_BankKey}/alter/fetchBankKeyCountryDupliChk?bankKeyCountryToCheck=${result}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCountry = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CountryReg", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_BankKey}/data/getCountry`, "get", hSuccess, hError);
  };
  useEffect(() => {
    if ((parseInt(page) + 1) * parseInt(pageSize) >= parseInt(skip) + 1000) {
      getFilter(skip + 1000);
      setSkip((prev) => prev + 1000);
    }
  }, [page, pageSize]);
  useEffect(() => {
    // getUserResponsible();
    // getFunctionalArea();
    getCountry();
    // getJurisdiction();
    // getRegion();
    // getLanguageKey();
    // getTimezone();
    // getTransportZone();
    // getUndeliverable();
    // getHierarchyArea();
    // getCompanyCode();
  }, []);
  let dynamicDataApis = {
    "Person Responsible": `/${destination_MaterialMgmt}/data/getSalesOrg`,
    "Business Area ": `/${destination_MaterialMgmt}/data/getDivision`,
    "Functional Area": `/${destination_MaterialMgmt}/data/getLaboratoryDesignOffice`,
    // "Transportation Group" : `/${destination_MaterialMgmt}/data/getTransportationGroup`,
    // "Batch Management" : `/${destination_MaterialMgmt}/data/getBatchManagement`,
    // "Old Material Number" : `/${destination_MaterialMgmt}/data/getOldMaterialNo`,
  };

  const handleSelection = (event) => {
    const selectedItems = event.target.value;
    setSelectedOptions(selectedItems);
    setDisplayedFields([]);
    console.log("selected field", event.target.value);

    selectedItems.forEach(async (selectedItem) => {
      const apiEndpoint = dynamicDataApis[selectedItem];
      fetchOptionsForDynamicFilter(apiEndpoint);
    });
  };

  const handleAddFields = () => {
    const numSelected = selectedOptions.length;
    const newFields = Array.from({ length: numSelected }, (_, index) => ({
      id: index,
      value: "",
    }));
    setDisplayedFields(newFields);
  };

  const handleFieldChange = (fieldId, value) => {
    setDisplayedFields(
      (selectedOptions) => selectedOptions.map((option) => option)
      // prevFields.map((field) => (field.id === fieldId ? { ...field, value } : field))
    );
  };
  const items = [
    { title: "Created On" },
  ];
  const titleToFieldMapping = {
    "Task ID": "taskId",
    Status: "status",
    SalesOrganization: "salesOrg",
    Division: "division",
    OldMaterialNumber: "oldMaterialNumber",
    "Lab/Office": "labOffice",
    "Transportation Group": "transportationGroup",
    "Batch management": "batchManagement",
    // Add more mappings as needed
  };
  // let [rmSearchForm, setRmSearchForm] = useState({
  //   companyCode: "",
  //   vendorNo: "",
  //   paymentStatus: "",
  // });
  //Checked PO rows

  const getBankKeyBankData = () => {
    let viewName = "Bank Details";
    const hSuccess = (data) => {
      dispatch(setBankKeyBankData(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_BankKey}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getBankKeyAddressData = () => {
    let viewName = "Address Details";
    const hSuccess = (data) => {
      dispatch(setBankKeyAddressData(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_BankKey}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };

  useEffect(() => {
    getBankKeyBankData();
    getBankKeyAddressData();
  }, []);

  const fetchOptionsForDynamicFilter = (apiEndpoint) => {
    const hSuccess = (data) => {
      console.log("dataaaaaaaa", data.body);
      setDynamicOptions([...dynamicOptions, data.body]);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(apiEndpoint, "get", hSuccess, hError);
  };

  const clearSearchBar = () => {
    setMaterialNumber("");
  };
  const getBankKeyGlobalSearch = (fetchSkip) => {
    console.log("hhhfhfhfhf");
    setTableLoading(true);
    // setTableLoading(true);
    if (!fetchSkip) {
      setPage(0);
      setPageSize(10);
      setSkip(0);
    }
    let payload = {
      bankCountry: "",
      bankKey: formcontroller_SearchBar?.number ?? "",
      bankName: "",
      swiftCode: "",
      bankNumber: "",
      region: "",
      branch: "",
      createdBy: "",
      top: 1000,
      skip: fetchSkip ?? 0,
    };
console.log("payload", payload);
    const hSuccess = (data) => {
      console.log("data", data.body.list);
      var rows = [];
      for (let index = 0; index < data?.body?.list?.length; index++) {
        var tempObj = data?.body.list[index];
        console.log("hshshsh", tempObj);
        // if (tempObj["MaterialNo"]) {
        var tempRow = {
          id: uuidv4(),
          bankCtryReg:
            tempObj?.BankCountry !== ""
              ? tempObj?.BankCountry
              : "Not Available",
          bankKey:
            tempObj?.BankInternalID !== ""
              ? tempObj?.BankInternalID
              : "Not Available",
          bankName:
            tempObj?.BankName !== "" ? tempObj?.BankName : "Not Available",
          bankBranch:
            tempObj?.Branch !== "" ? tempObj?.Branch : "Not Available",
          swiftBic:
            tempObj?.SWIFTCode !== "" ? tempObj?.SWIFTCode : "Not Available",
          bankNumber:
            tempObj?.BankNumber !== "" ? tempObj?.BankNumber : "Not Available",
          createdBy:
            tempObj?.CreatedByUser !== ""
              ? tempObj?.CreatedByUser
              : "Not Available",
          changedBy:
            tempObj?.changedBy !== "" ? tempObj?.changedBy : "Not Available",
          // createdOn: moment(tempObj.CreationDate).format(appSettings?.dateFormat),
          createdOn: moment(tempObj.CreationDate).format("DD MMM YYYY"),
        };
        rows.push(tempRow);
        // }
      }
      console.log("tempobj", tempRow);
      console.log("tempObH", tempObj);
      rows.sort(
        (a, b) =>
          moment(a.createdOn, "DD MMM YYYY HH:mm") -
          moment(b.createdOn, "DD MMM YYYY HH:mm")
      );
      setRmDataRows(rows);
      setTableLoading(false);
      setroCount(rows.length);
      setCount(data?.body?.count);
    };
    let hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_BankKey}/data/getBankKeysBasedOnAdditionalParams`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  /* Setting Default Dates */
  const presentDate = new Date();
  const backDate = new Date();
  backDate.setDate(backDate.getDate() - 15);

  const [date, setDate] = useState([backDate, presentDate]);
  const [date1, setDate1] = useState([backDate, presentDate]);

  const handleDate = (e) => {
    // if (e !== null) setDate(e.reverse());
    if (e !== null) {
      var createdOn = e.reverse();
      dispatch(
        commonFilterUpdate({
          module: "MaterialMaster",
          filterData: {
            ...rmSearchForm,
            createdOn: createdOn,
          },
        })
      );
    }
  };

  const handleDate1 = (e) => {
    if (e !== null) setDate1(e.reverse());
  };

  const handleSnackBarClickaccept = () => {
    setOpenSnackBaraccept(true);
  };

  const handleSnackBarCloseaccept = (event, reason) => {
    if (reason === "clickaway") {
      return;
    }

    setOpenSnackBaraccept(false);
  };

  const handleUserName = (e) => {
    setUserName(e.target.value);
  };
  // Get Filter Data
  const getFilter = (fetchSkip) => {
    console.log("called");
    setTableLoading(true);
    if (!fetchSkip) {
      setPage(0);
      setPageSize(10);
      setSkip(0);
    }
    let payload = {
      bankCountry: rmSearchForm?.bankCtrRegion?.code ?? "",
      bankKey: formcontroller_SearchBar?.number ?? "",
      bankName: rmSearchForm?.bankName ?? "",
      swiftCode: rmSearchForm?.swiftBic ?? "",
      bankNumber: rmSearchForm?.bankNumber ?? "",
      region: "",
      branch: rmSearchForm?.bankBranch ?? "",
      createdBy: rmSearchForm?.createdBy ?? "",
      top: 1000,
      skip: fetchSkip ?? 0,
    };
    const hSuccess = (data) => {
      console.log("data", data.body.list);
      var rows = [];
      for (let index = 0; index < data?.body?.list?.length; index++) {
        var tempObj = data?.body.list[index];
        console.log("hshshsh", tempObj);
        // if (tempObj["MaterialNo"]) {
        var tempRow = {
          id: uuidv4(),
          bankCtryReg:
            tempObj?.BankCountry !== ""
              ? tempObj?.BankCountry
              : "Not Available",
          bankKey:
            tempObj?.BankInternalID !== ""
              ? tempObj?.BankInternalID
              : "Not Available",
          bankName:
            tempObj?.BankName !== "" ? tempObj?.BankName : "Not Available",
          bankBranch:
            tempObj?.Branch !== "" ? tempObj?.Branch : "Not Available",
          swiftBic:
            tempObj?.SWIFTCode !== "" ? tempObj?.SWIFTCode : "Not Available",
          bankNumber:
            tempObj?.BankNumber !== "" ? tempObj?.BankNumber : "Not Available",
          createdBy:
            tempObj?.CreatedByUser !== ""
              ? tempObj?.CreatedByUser
              : "Not Available",
          changedBy:
            tempObj?.changedBy !== "" ? tempObj?.changedBy : "Not Available",
          // createdOn: moment(tempObj.CreationDate).format(appSettings?.dateFormat),
          createdOn: moment(tempObj.CreationDate).format("DD MMM YYYY"),
        };
        rows.push(tempRow);
        // }
      }
      console.log("tempobj", tempRow);
      console.log("tempObH", tempObj);
      // rows.sort(
      //   (a, b) =>
      //     moment(a.createdOn, "DD MMM YYYY HH:mm") -
      //     moment(b.createdOn, "DD MMM YYYY HH:mm")
      // );
      setRmDataRows(rows);
      setTableLoading(false);
      setroCount(rows.length);
      setCount(data?.body?.count);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_BankKey}/data/getBankKeysBasedOnAdditionalParams`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const [userList, setUserList] = useState([]);

  const moduleFilterData = [
    {
      type: "singleSelect",
      filterName: "company",
      // filterData: masterData?.companyCode,
      filterTitle: "Company",
    },
    {
      type: "singleSelect",
      filterName: "vendor",
      filterData: vendorDetailsSet,
      filterTitle: "Supplier",
    },
    {
      type: "text",
      filterName: "poNum",
      filterTitle: "PO Number",
    },
    {
      type: "autoComplete",
      filterName: "createdBy",
      filterData: userList,
      filterTitle: "Created By",
    },
    {
      type: "singleSelectKV",
      filterName: "returnType",
      filterData: {
        "Debit Note": "Debit Note",
        Replacement: "Replacement",
      },
      filterTitle: "Return Type",
    },
    {
      type: "singleSelect",
      filterName: "plant",
      filterData: plantCodeSet,
      filterTitle: "Plant",
    },
    {
      type: "dateRange",
      filterName: "createdOn",
      filterTitle: "Return Request Date",
    },
  ];

  const [confirmation, setconfirmation] = useState([]);
  const [confirmationText, setConfirmationText] = useState(null);
  const [poHeader, setPoHeader] = useState(null);
  const [roCount, setroCount] = useState(0);
  const [showBtmNav, setShowBtmNav] = useState(false);
  const [opendialog, setOpendialog] = useState(false);
  const [openSnackbarDialog, setOpenSnackbarDialog] = useState(false);
  const [opendialog2, setOpendialog2] = useState(false);
  const [opendialog3, setOpendialog3] = useState(false);
  const [openforwarddialog, setOpenforwarddialog] = useState(false);
  const [rejectInputText, setRejectInputText] = useState("");
  const [acceptInputText, setAcceptInputText] = useState("");
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState("");
  const [messageDialogMessage, setMessageDialogMessage] = useState();

  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };

  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };

  const [anchorEl_Preset, setAnchorEl] = useState(null);
  const openAnchor = Boolean(anchorEl_Preset);

  const handleClose_Preset = () => {
    setPresetName("");
    setAnchorEl(null);
  };

  const [presets, setPresets] = useState(null);
  const [presetName, setPresetName] = useState(null);

  const handleClear = () => {
    dispatch(commonFilterClear({ module: "BankKey" }));
  };
  const onRowsSelectionHandler = (ids) => {
    //Selected Columns stored here
    const selectedRowsData = ids.map((id) =>
      rmDataRows.find((row) => row.id === id)
    );
    var compCodes = selectedRowsData.map((row) => row.company);
    var companySet = new Set(compCodes);
    var vendors = selectedRowsData.map((row) => row.vendor);
    var vendorSet = new Set(vendors);
    var paymentTerms = selectedRowsData.map((row) => row.paymentTerm);
    var paymentTermsSet = new Set(paymentTerms);
    if (selectedRowsData.length > 0) {
      if (companySet.size === 1) {
        if (vendorSet.size === 1) {
          if (paymentTermsSet.size !== 1) {
            setDisableButton(true);
            setMessageDialogTitle("Error");
            setMessageDialogMessage(
              "Invoice cannot be generated for vendors with different payment terms"
            );
            setMessageDialogSeverity("danger");
            handleMessageDialogClickOpen();
          } else setDisableButton(false);
        } else {
          setDisableButton(true);
          setMessageDialogTitle("Error");
          setMessageDialogMessage(
            "Invoice cannot be generated for multiple suppliers"
          );
          setMessageDialogSeverity("danger");
          handleMessageDialogClickOpen();
        }
      } else {
        setDisableButton(true);
        setMessageDialogTitle("Error");
        setMessageDialogMessage(
          "Invoice cannot be generated for multiple companies"
        );
        setMessageDialogSeverity("danger");
        handleMessageDialogClickOpen();
      }
    } else {
      setDisableButton(true); //Enable the Create E-Invoice button when at least one row is selected and no two companys or vendors are same
    }
    setSelectedRow(ids); //Setting the ids(PO Numbers) of selected rows
    setSelectedDetails(selectedRowsData); //Setting the entire data of a selected row
  };
  function refreshPage() {
    getFilter();
  }

  const [company, setCompany] = useState([]);
  const [Companyid, setCompanyid] = useState([]);

  // let { poId } = useParams();
  const [open, setOpen] = useState(false);
  const [matAnchorEl, setMatAnchorEl] = useState(null);
  const [materialDetails, setMaterialDetails] = useState(null);
  const [itemDataRows, setItemDataRows] = useState([]);

  // const handleMatCodeClick = (event) => {
  //   if (materialDetails !== null) setMaterialDetails(null);
  //   else fetchMaterialDetails(event.target.innerText);
  //   setMatAnchorEl(matAnchorEl ? null : event.currentTarget);
  // };

  const handlePODetailsClick = (event) => {
    setOpendialog3(true);
  };

  const matOpen = Boolean(matAnchorEl);
  const popperId = matOpen ? "simple-popper" : undefined;

  const handleClickOpen = () => {
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
  };

  const [poNum, setPONum] = useState(null);
  const fetchPOHeader = (id) => {
    var formData = new FormData();
    if (id) formData.append("extReturnId", id);
    const hSuccess = (data) => {
      if (data) {
        setPoHeader(data);
        setPONum(data[0]["poNumber"] ?? "");
      }
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_Returns}/returnsHeader/getReturnsPreview`,
      "postformdata",
      hSuccess,
      hError,
      formData
    );
  };
  // const fetchMaterialDetails = (code) => {
  //   const hSuccess = (data) => {
  //     if (data.response != "null") setMaterialDetails(data.response[0]);
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(
  //     `/${destination_Po}/Odata/materialCode/code/${code}`,
  //     "get",
  //     hSuccess,
  //     hError
  //   );
  // };

  const [id, setID] = useState("");
  const columns = [
    {
      field: "bankCtryReg",
      headerName: "Bank Country",
      editable: false, //dd

      flex: 1,
    },
    {
      field: "bankKey",
      headerName: "Bank key",
      editable: false, //text
      flex: 1,
    },
    {
      field: "bankName",
      headerName: "Bank Name",
      editable: false, //dd
      flex: 1,
    },
    {
      field: "bankBranch",
      headerName: "Bank Branch",
      editable: false, //dd
      flex: 1,
    },
    {
      field: "swiftBic",
      headerName: "SWIFT/BIC", //dd
      editable: false,
      flex: 1,
    },
    {
      field: "bankNumber",
      headerName: "Bank Number", //dd
      editable: false,
      flex: 1,
    },

    {
      field: "createdBy",
      headerName: "Created By",
      editable: false,
      flex: 1,
    },
    {
      field: "createdOn",
      headerName: "Created On",
      editable: false,
      flex: 1,
    },

    // {
    //   field: "action",
    //   headerName: "Action",
    //   sortable: false,
    //   filterable: false,
    //   flex: 1,
    //   align: "center",
    //   headerAlign: "center",
    //   renderCell: (cellValues) => {
    //     return (
    //       <div className={cellValues.row.materialNumber}>
    //         <Tooltip title="View">
    //           <IconButton
    //             sx={iconButton_SpacingSmall}
    //             onClick={() => handleOnClick(cellValues.row.materialNumber)}
    //           >
    //             {ViewDetailsIcon}
    //           </IconButton>
    //         </Tooltip>
    //         {/* <Tooltip title="PO Flow">
    //           <IconButton
    //             sx={iconButton_SpacingSmall}
    //             onClick={() => handleClickOpenPOFlow(cellValues.row.poNum)}
    //           >
    //             <TrackChangesIcon />
    //           </IconButton>
    //         </Tooltip> */}
    //         {/* <Tooltip title="Forward">
    //           <IconButton onClick={handleOpenforwarddialog}>
    //             <ForwardToInboxOutlinedIcon className="wbActionIcon" />
    //           </IconButton>
    //         </Tooltip> */}
    //         {/* <Dialog
    //         hideBackdrop={true}
    //         elevation={2}
    //         PaperProps={{
    //           sx: { boxShadow: "none",minHeight:"8rem"},
    //         }}
    //         open={openforwarddialog}
    //         onClose={handleCloseforwarddialog}>

    //             <Grid
    //             container
    //             sx={{
    //               padding: "0",
    //             }}
    //           >
    //             <Grid item md={11}>
    //               <DialogTitle
    //                 sx={{ fontSize: "1rem", fontWeight: "500" }}
    //               >
    //               Forward Task To
    //               </DialogTitle>
    //               </Grid>
    //               <Grid item md={1}
    //               style={{
    //                 margin: "auto",
    //                 display: "flex",
    //                 justifyContent: "flex-end",
    //                 paddingRight: "1rem",
    //               }}>
    //               <IconButton
    //                 sx={{ padding: "16px", marginLeft: "9rem" }}
    //                 disableRipple
    //               >
    //                 <CloseIcon onClick={handleCloseforwarddialog} />
    //               </IconButton>
    //             </Grid>
    //             </Grid>
    //   <Autocomplete className="autocomplete" sx={{width:"95%", padding:"0px"}}
    //     multiple
    //     id="tags-standard"
    //     options={topEmails}
    //     getOptionLabel={(option) => option.title}
    //     renderInput={(params) => (
    //       <TextField size="small"  sx={{marginLeft:"0.5rem"}}
    //         {...params}
    //         variant="outlined"
    //         placeholder="Search User Name or Email ID"
    //       />
    //     )}
    //   />
    //   </Dialog> */}
    //       </div>
    //     );
    //   },
    // },
  ];
  const dynamicFilterColumns = selectedOptions
    .map((option) => {
      const field = titleToFieldMapping[option]; // Get the corresponding field from the mapping
      if (!field) {
        return null; // Handle the case when the field doesn't exist in the mapping
      }
      return {
        field: field, // Use the field name from the mapping
        headerName: option,
        editable: false,
        flex: 1,
      };
    })
    .filter((column) => column !== null); // Remove any null columns

  const allColumns = [
    ...columns,
    ...dynamicFilterColumns,
    // Other fixed and dynamic columns as needed
  ];
  const capitalize = (str) => {
    //  str.map((str)=>{
    const arr = str.split(" ");
    for (var i = 0; i < arr.length; i++) {
      arr[i] = arr[i].charAt(0) + arr[i].slice(1).toLowerCase();
    }

    const str2 = arr.join(" ");
    return str2;
    //  })
  };

  const handleSnackBarOpen = () => {
    setopenSnackbar(true);
  };

  const uploadExcel = (file) => {
    setIsLoading(true);
    console.log(file);
    const formData = new FormData();
    [...file].forEach((item) => formData.append("files", item));
    console.log(handleMassModeBK, "handleMassModeBK")
    if (handleMassModeBK === 'Change') {
      var uploadUrl = `/${destination_BankKey}/massAction/getAllBankKeyFromExcelForMassChange`;
    } else {
      //var uploadUrl = `/${destination_ProfitCenter}/massAction/getAllProfitCenterFromExcel`;
      var uploadUrl = `/${destination_BankKey}/massAction/getAllBankKeyFromExcel`;
    }
    const hSuccess = (data) => {
      console.log(data, "example");
      setIsLoading(false);
      if (data.statusCode === 200) {
        setEnableDocumentUpload(false);
        // dispatch(setControllingArea(data?.body?.controllingArea));
        dispatch(setMultipleBankKeyData(data?.body));
        setMessageDialogTitle("Create");
        setMessageDialogMessage(`${file.name} has been Uploaded Succesfully`);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
        //navigate(`/masterDataCockpit/profitCenter/createMultipleProfitCenter`);
        navigate(`/masterDataCockpit/bankKey/createMultipleBankKey`);
      } else {
        setEnableDocumentUpload(false);
        setMessageDialogTitle("Create");
        setsuccessMsg(false);
        setMessageDialogMessage("Creation Failed");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(uploadUrl, "postformdata", hSuccess, hError, formData);
  };

  useEffect(() => {
    getFilter();
    // functions_PresetFilter.getFilterPresets();
  }, []);
  // useEffect(() => {
  //   if ((rmSearchForm?.company).length) {
  //     getVendorDetails();
  //     getPlantCodeSet()
  //   }
  // }, [rmSearchForm?.company]);

  // let serviceRequestForm_Component = new createServiceRequestForm(Status_ServiceReqForm, setStatus_ServiceReqForm)
  // <-- Function for taking screenshot (Export button) -->
  let ref_elementForExport = useRef(null);
  let exportAsPicture = () => {
    setTimeout(() => {
      captureScreenShot("Material-Single");
    }, 100);
  };

  // <-- Functions for exporting as excel -->
  // let xlsx = require("json-as-xlsx")
  // let excelColumns = []
  // columns.forEach((item) => {
  //   if (item.headerName.toLowerCase() !== 'action' && !item.hide) {
  //     excelColumns.push({ label: item.headerName, value: item.field })
  //   }
  // })
  // const functions_ExportAsExcel = {
  //   data: [
  //     {
  //       sheet: "Return Workbench",
  //       columns: excelColumns,
  //       content: rmDataRows,
  //     }
  //   ],
  //   settings: {
  //     fileName: `Return_WorkbenchDatasheet-${moment(presentDate).format('DD-MMM-YYYY')}`, // Name of the resulting spreadsheet
  //     extraLength: 3, // A bigger number means that columns will be wider
  //     writeMode: "writeFile", // The available parameters are 'WriteFile' and 'write'. This setting is optional.
  //     writeOptions: {}, // Style options from https://docs.sheetjs.com/docs/api/write-options
  //     RTL: false, // Display the columns from right-to-left (the default value is false)
  //   },
  //   convertJsonToExcel: () => {
  //     xlsx(functions_ExportAsExcel.data, functions_ExportAsExcel.settings)
  //   },
  //   button: () => {
  //     // return (
  //     //   <Button sx={{textTransform:'capitalize', position:'absolute', right:0, top:0}} onClick={()=> functions_ExportAsExcel.convertJsonToExcel()}>Download</Button>
  //     // )
  //   }
  // }

  const handleDownloadCreate = async () => {
    let hSuccess = (response) => {
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");
      link.href = href;
      link.setAttribute("download", `Bank Key_Mass Create.xls`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
      handleMessageDialogClickOpen();
      setMessageDialogTitle("Success");
      setMessageDialogMessage(
        `Bank Key_Mass Create.xls has been downloaded successfully`
      );
      setMessageDialogSeverity("success");
    };
    let hError = (error) => {
      if (error.message) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(`${error.message}`);
        setMessageDialogSeverity("danger");
      }
    };
    doAjax(
      `/${destination_BankKey}/excel/downloadExcel`,
      "getblobfile",
      hSuccess,
      hError
    );
  };

  const handleDownloadTemplate = async () => {
    var downloadPayload = selectedDetails.map((x) => {
      return {
        bankCtry: x?.bankCtryReg??'',
        bankKey: x?.bankKey?? '',
      };
    });
    console.log("downloadPayload", downloadPayload);
    let hSuccess = (response) => {
      setIsLoading(false);
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");
      link.href = href;
      link.setAttribute("download", `Bank Key_Mass Change.xls`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
      handleMessageDialogClickOpen();
      setMessageDialogTitle("Success");
      setMessageDialogMessage(
        `Bank Key_Mass Change.xls has been downloaded successfully`
      );
      setMessageDialogSeverity("success");
    };
    let hError = (error) => {
      if (error.message) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(`${error.message}`);
        setMessageDialogSeverity("danger");
      }
    };
    doAjax(
      `/${destination_BankKey}/excel/downloadExcelWithData`,
      "postandgetblob",
      hSuccess,
      hError,
      downloadPayload
    );
  };
  const handleCreateSingleWithCopy = () => {
    handleDialogClickOpenWithCopy();
  };
  const handleCreateSingleWithoutCopy = () => {
    handleDialogClickOpen();
  };
  const handleCreateMultiple = () => {
    setEnableDocumentUpload(true);
    dispatch(setHandleMassMode("Create"));
  };
  const handleToggle = () => {
    setOpenButton((prevOpen) => !prevOpen);
  };
  const handleClick = (option, index) => {
    if (index !== 0) {
      setSelectedIndex(index);
      setOpenButton(false);
      if (index === 1) {
        handleCreateMultiple();
      } else if (index === 2) {
        handleDownloadCreate();
      }
    }
  };
  const handleCloseButton = (event) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }
    setOpenButton(false);
  };

  const handleDialogCloseCreate = () => {
    setDialogOpenCreate(false);
  }

  const handleDialogCloseCreateSingle = (event) => {
    if (
      anchorRefCreate.current &&
      anchorRefCreate.current.contains(event.target)
    ) {
      return;
    }
    //setDialogOpenCreate(false);
    setOpenButtonCreate((prevOpen) => !prevOpen);
  };

  const handleToggleCreate = () => {
    setOpenButtonCreate((prevOpen) => !prevOpen);
  };
  const handleToggleChange = () => {
    setOpenButtonChange((prevOpen) => !prevOpen);
  };
  const handleCloseButtonChange = (event) => {
    if (
      anchorRefChange.current &&
      anchorRefChange.current.contains(event.target)
    ) {
      return;
    }
    setOpenButtonChange(false);
  };


  const handleClickCreate = (option, index) => {
    // dispatch(setHandleMassMode("Change"));
    if (index !== 0) {
      setSelectedIndexCreate(index);
      setOpenButtonCreate(false);
      if (index === 1) {
        handleCreateSingleWithCopy();
      } else if (index === 2) {
        handleCreateSingleWithoutCopy();
      }
    }
  };
  const handleClickChange = (option, index) => {
    if (index !== 0) {
      setSelectedIndexChange(index);
      setOpenButtonChange(false);
      if (index === 1) {
        handleChangeMultiple();
      } else if (index === 2) {
        handleDownloadTemplate();
      }
    }
  };
  const handleChangeMultiple = () => {
    setEnableDocumentUpload(true);
    dispatch(setHandleMassMode("Change"));
  };

  return (
    <>
       {
        isLoading===true?
        <LoadingComponent/>
        :
        <div ref={ref_elementForExport}>
        <Backdrop
            sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 999999 }}
            open={blurLoading}
            // onClick={handleClose}
          >
            <CircularProgress color="inherit" />
          </Backdrop>
      <ReusableDialog
        dialogState={openMessageDialog}
        openReusableDialog={handleMessageDialogClickOpen}
        closeReusableDialog={handleMessageDialogClose}
        dialogTitle={messageDialogTitle}
        dialogMessage={messageDialogMessage}
        handleDialogConfirm={handleMessageDialogClose}
        dialogOkText={"OK"}
        // handleExtraButton={handleMessageDialogNavigate}
        dialogSeverity={messageDialogSeverity}
      />
      <div style={{ ...outermostContainer, backgroundColor: "#FAFCFF" }}>
        <Stack spacing={1}>
          {/* Information */}
          <Grid container sx={outermostContainer_Information}>
            <Grid item md={5} sx={outerContainer_Information}>
              <Typography variant="h3">
                <strong>Bank Key</strong>
              </Typography>
              <Typography variant="body2" color="#777">
                This view displays the list of Bank Keys
              </Typography>
            </Grid>
            <Grid item md={7} sx={{ display: "flex" }}>
              <Grid
                container
                direction="row"
                justifyContent="flex-end"
                alignItems="center"
                spacing={0}
              >
                <SearchBar
                  title="Search for multiple Bank Key numbers separated by comma"
                  handleSearchAction={()=>getBankKeyGlobalSearch()}
                  module="BankKey"
                  keyName="number"
                  message={"Search Bank Key "}
                  clearSearchBar={clearSearchBar}
                />

                <Tooltip title="Reload">
                  <IconButton sx={iconButton_SpacingSmall}>
                    <Refresh
                      sx={{
                        "&:hover": {
                          transform: "rotate(360deg)",
                          transition: "0.9s",
                        },
                      }}
                      onClick={refreshPage}
                    />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Export Table">
                  <IconButton
                    sx={iconButton_SpacingSmall}
                    onClick={functions_ExportAsExcel.convertJsonToExcel}
                  >
                    <ReusableIcon iconName={"IosShare"} />
                  </IconButton>
                </Tooltip>
              </Grid>
            </Grid>
          </Grid>
          <Grid container sx={container_filter}>
            <Grid item md={12}>
              <Accordion className="filter-accordian">
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  aria-controls="panel1a-content"
                  id="panel1a-header"
                  sx={{
                    minHeight: "2rem !important",
                    margin: "0px !important",
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: "700",
                    }}
                  >
                    Search Bank Key
                  </Typography>
                </AccordionSummary>
                <AccordionDetails sx={{ padding: "0.5rem 1rem 0.5rem" }}>
                  <Grid
                    container
                    rowSpacing={1}
                    spacing={2}
                    justifyContent="space-between"
                    alignItems="center"
                  // sx={{ marginBottom: "0.5rem" }}
                  >
                    <Grid
                      container
                      spacing={1}
                      sx={{ padding: "0rem 1rem 0.5rem" }}
                    >
                      {/* <Grid item md={2}>
                        <Typography sx={{ font_Small }}>Material Number</Typography>
                        <FormControl size="small" fullWidth>
                          <Autocomplete sx={{ height: "31px" }}
                            fullWidth
                            size="small"
                            disableCloseOnSelect
                            value={materialFilterDetails?.number}
                            onChange={(e, value) => {
                              setMaterialFilterDetails({ ...materialFilterDetails, number: value });
                            }}
                            options={["112233", "445566"]}
                            renderInput={(params) => (
                              <TextField
                                {...params}
                                variant="outlined"
                                placeholder="Enter Number"
                              />
                            )}
                          />
                        </FormControl>
                      </Grid> */}
                      <Grid item md={2}>
                        <Typography sx={font_Small}>Bank Country</Typography>
                        <FormControl size="small" fullWidth>
                          <Autocomplete
                            sx={{ height: "31px" }}
                            fullWidth
                            size="small"
                            value={rmSearchForm?.bankCtrRegion}
                            onChange={handleCtrRegion}
                            options={dropDownData?.CountryReg ?? []}
                            getOptionLabel={(option) => {
                              if (option?.code)
                                return (
                                  `${option?.code} - ${option?.desc}` ?? ""
                                );
                              else return "";
                            }}
                            renderOption={(props, option) => (
                              <li {...props}>
                                <Typography style={{ fontSize: 12 }}>
                                  {`${option?.code} - ${option?.desc}`}
                                </Typography>
                              </li>
                            )}
                            renderInput={(params) => (
                              <TextField
                                sx={{ fontSize: "12px !important" }}
                                {...params}
                                variant="outlined"
                                placeholder="Select Country"
                              />
                            )}
                          />
                        </FormControl>
                      </Grid>
                      {/* <Grid item md={2}>
                        <Typography sx={font_Small}>Bank Key</Typography>
                        <TextField
                          sx={{ fontSize: "12px !important" }}
                          size="small"
                          fullWidth
                          onChange={handleBankKey}
                          placeholder="Enter Bank Key"
                          value={rmSearchForm?.bankKey}
                        />
                      </Grid> */}
                      <Grid item md={2}>
                        <Typography sx={font_Small}>Bank Name</Typography>
                        <TextField
                          sx={{ fontSize: "12px !important" }}
                          size="small"
                          fullWidth
                          onChange={handleBankName}
                          placeholder="Enter Bank Name"
                          value={rmSearchForm?.bankName}
                        />
                      </Grid>
                      <Grid item md={2}>
                        <Typography sx={font_Small}>Bank Branch</Typography>
                        <TextField
                          sx={{ fontSize: "12px !important" }}
                          size="small"
                          fullWidth
                          onChange={handleBankBranch}
                          placeholder="Enter Bank Branch"
                          value={rmSearchForm?.bankBranch}
                        />
                      </Grid>
                      <Grid item md={2}>
                        <Typography sx={font_Small}>SWIFT/BIC</Typography>
                        <TextField
                          sx={{ fontSize: "12px !important" }}
                          size="small"
                          fullWidth
                          onChange={handleSwiftBic}
                          placeholder="Enter Swift/BIC"
                          value={rmSearchForm?.swiftBic}
                        />
                      </Grid>
                      <Grid item md={2}>
                        <Typography sx={font_Small}>Bank Number</Typography>
                        <TextField
                          sx={{ fontSize: "12px !important" }}
                          size="small"
                          fullWidth
                          onChange={handleBankNumber}
                          placeholder="Enter Bank Number"
                          value={rmSearchForm?.bankNumber}
                        />
                      </Grid>
                      <Grid item md={2}>
                        <Typography sx={font_Small}>Created By</Typography>
                        <TextField
                          sx={{ fontSize: "12px !important" }}
                          fullWidth
                          size="small"
                          value={rmSearchForm?.createdBy}
                          onChange={handleCreatedBy}
                          placeholder="Enter Created By"
                        />
                      </Grid>
                      {/* <Grid item md={2}>
                        <Typography sx={font_Small}>Changed By</Typography>
                        <TextField
                          sx={{ fontSize: "12px !important" }}
                          fullWidth
                          size="small"
                          value={rmSearchForm?.changedBy}
                          onChange={handleChangedBy}
                          placeholder="Enter Changed By"
                        />
                      </Grid>
                      <Grid item md={2}>
                        <Typography sx={font_Small}>Created On</Typography>
                        <FormControl fullWidth sx={{ padding: 0 }}>
                          <LocalizationProvider dateAdapter={AdapterDateFns}>
                            <DateRange
                              onChange={(e) => handleDate(e)}
                              // onChange={(e) => handleMatTypeChange(e)
                              value={rmSearchForm?.createdOn}
                            />
                          </LocalizationProvider>
                        </FormControl>
                      </Grid> */}
                      {/* dynamic filter// */}
                      <Grid item md={2}>
                        <Typography sx={font_Small}>Add New Filters</Typography>
                        <FormControl>
                          <Select
                            sx={{
                              font_Small,
                              height: "31px",
                              fontSize: "12px",
                              width: "200px",
                            }}
                            // fullWidth
                            size="small"
                            multiple
                            limitTags={2}
                            value={selectedOptions}
                            onChange={handleSelection}
                            renderValue={(selected) => selected.join(", ")}
                            MenuProps={{
                              MenuProps,
                            }}
                            endAdornment={
                              selectedOptions.length > 0 && (
                                <InputAdornment position="end">
                                  <IconButton
                                    size="small"
                                    onClick={() => setSelectedOptions([])}
                                    aria-label="Clear selections"
                                  >
                                    <ClearIcon />
                                  </IconButton>
                                </InputAdornment>
                              )
                            }
                          >
                            {/* {items.map((option) => (
                              <MenuItem key={option.title} value={option.title}>
                                <Checkbox
                                  checked={
                                    selectedOptions.indexOf(option.title) > -1
                                  }
                                />
                                {option.title}
                              </MenuItem>
                            ))} */}
                          </Select>
                        </FormControl>
                        <Grid
                          style={{
                            display: "flex",
                            justifyContent: "space-around",
                          }}
                        ></Grid>
                      </Grid>
                    </Grid>
                    <Grid
                      container
                      sx={{ flexDirection: "row", padding: "0rem 1rem 0.5rem" }}
                      gap={1}
                    >
                      {selectedOptions.map((option, i) => {
                        // if (option !== "Created Date" || option !== "Plant") {
                        return (
                          <Grid item>
                            <Stack>
                              <Typography sx={{ fontSize: "12px" }}>
                                {option}
                              </Typography>
                              <Autocomplete
                                sx={font_Small}
                                size="small"
                                key={option[i]}
                                options={dynamicOptions ?? []}
                                getOptionLabel={(option, i) =>
                                  `${option[i]?.code} - ${option[i]?.desc}`
                                }
                                placeholder={`Enter ${option}`}
                                value={filterFieldData[option]}
                                onChange={(event, newValue) =>
                                  setFilterFieldData({
                                    ...filterFieldData,
                                    [option]: newValue,
                                  })
                                }
                                renderInput={(params) => (
                                  <TextField
                                    sx={{ fontSize: "12px !important" }}
                                    {...params}
                                    size="small"
                                    placeholder={`Enter ${option}`}
                                    variant="outlined"
                                  // sx={font_Small}
                                  />
                                )}
                              />
                            </Stack>
                          </Grid>
                        );
                        // } if (option === "Created Date") {
                        //   return (
                        //     <Stack>

                        //       <DatePicker/>

                        //     </Stack>
                        //   )
                        // }
                      })}
                      {/* //clear search button according to UX */}
                      {/* <Grid
                          item
                          style={{
                            width:"100%",
                            display: "flex",
                            justifyContent: "space-around",
                          }}
                        >
                          <Button
                            
                            sx={{ fontSize: "12px",  width:"129px", backgroundColor:" #7575751A", color:"#757575" }}
                            onClick={handleClear}
                          >
                            Clear
                          </Button>

                          <Button
                            
                            sx={{ ...button_Marginleft, fontSize: "12px",  width:"129px", backgroundColor:"#F7F5FF"  }}
                          // onClick={getFilter}
                          >
                            Search
                          </Button>
                        </Grid> */}
                    </Grid>
                  </Grid>
                  <Grid
                    container
                    style={{
                      display: "flex",
                      justifyContent: "flex-end",
                    }}
                  >
                    <Grid
                      item
                      style={{
                        display: "flex",
                        justifyContent: "space-around",
                      }}
                    >
                      <Button
                        variant="outlined"
                        sx={button_Outlined}
                        onClick={handleClear}
                      >
                        Clear
                      </Button>
                      {/* <PresetV3
                        anchorEl={anchorEl_Preset}
                        setAnchorEl={setAnchorEl}
                        open={openAnchor}
                        handleClose={handleClose_Preset}
                        presets={presets}
                        setPresets={setPresets}
                        presetName={presetName}
                        setPresetName={setPresetName}
                        deletePreset={functions_PresetFilter.deletePreset}
                        saveFilterPreset={functions_PresetFilter.saveFilterPreset}
                        setPresetFilter={functions_PresetFilter.setPresetFilter}
                        setFilterDefault={functions_PresetFilter.setFilterDefault}
                        handleSearch={() => { }}
                      /> */}
                      {/* <PresetV3
                        moduleName={"ReturnOrder"}
                        handleSearch={getFilter}
                        PresetMethod={PresetMethod}

                        PresetObj={PresetObj}
                      /> */}

                      <Button
                        variant="contained"
                        sx={{ ...button_Primary, ...button_Marginleft }}
                        onClick={() => getFilter()}
                      >
                        Search
                      </Button>
                    </Grid>
                  </Grid>
                </AccordionDetails>
              </Accordion>
            </Grid>
          </Grid>

          <Grid item sx={{ position: "relative" }}>
            <Stack>
              <ReusableTable
                isLoading={tableLoading}
                module={"BankKey"}
                width="100%"
                title={"List of Bank Keys (" + roCount + ")"}
                rows={rmDataRows}
                columns={allColumns}
                page={page}
                pageSize={10}
                rowCount={count ?? rmDataRows?.length ?? 0}
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                getRowIdValue={"id"}
                hideFooter={true}
                checkboxSelection={true}
                disableSelectionOnClick={true}
                status_onRowSingleClick={true}
                onRowsSelectionHandler={onRowsSelectionHandler}
                callback_onRowSingleClick={(params) => {
                  const bankKey = params.row.bankKey; // Adjust this based on your data structure
                  navigate(
                    `/masterDataCockpit/bankKey/displayBankKey/${bankKey}`,
                    {
                      state: params.row,
                    }
                  );
                }}
                // setShowWork={setShowWork}
                stopPropagation_Column={"action"}
                status_onRowDoubleClick={true}
                showCustomNavigation={true}
              />
              {/* {viewDetailpage && <SingleMaterialDetail />} */}
            </Stack>
          </Grid>
          {/* {
            showBtmNav && */}
          {checkIwaAccess(iwaAccessData, "Bank Key", "CreateBK") && 
            userData?.role === "Super User" || userData?.role === "Finance" ?
            ( 
          <Paper
            sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
            elevation={2}
          >
            <BottomNavigation
              className="container_BottomNav"
              showLabels
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                gap: 1,
              }}
              value={value}
              onChange={(newValue) => {
                setValue(newValue);
              }}
            >
            <Button
             size="small"
            variant="contained"
            onClick={handleDialogClickOpen}
            >
Create Single
            </Button>
              {/* <ButtonGroup
                variant="contained"
                ref={anchorRefCreate}
                aria-label="split button"
              >
                <Button
                  size="small"
                  variant="contained"
                  // onClick={handleClickOpenCreateInvoice}
                  onClick={() => handleClickCreate(optionsCreateSingle[0],0)}
                  // onClick={handleDialogClickOpen}
                >
                  {optionsCreateSingle[0]}
                </Button>
                <Button
                  size="small"
                  aria-controls={
                    openButtonCreate ? "split-button-menu" : undefined
                  }
                  aria-expanded={openButtonCreate ? "true" : undefined}
                  aria-label="select action"
                  aria-haspopup="menu"
                  onClick={handleToggleCreate}
                >
                  <ReusableIcon
                    iconName={"ArrowDropUp"}
                    iconColor={"#FFFFFF"}
                  />
                </Button>
              </ButtonGroup> */}
              {/* <Popper
                sx={{
                  zIndex: 1,
                }}
                open={openButtonCreate}
                anchorEl={anchorRefCreate.current}
                placement={"top-end"}
              // transition
              >
                <Paper style={{ width: anchorRefCreate.current?.clientWidth }}>
                  <ClickAwayListener
                    onClickAway={handleDialogCloseCreateSingle}
                  >
                    <MenuList id="split-button-menu" autoFocusItem>
                      {optionsCreateSingle.slice(1).map((option, index) => (
                        <MenuItem
                          // autoFocusItem
                          key={option}
                          selected={index === selectedIndexCreate - 1}
                          onClick={() => handleClickCreate(option, index + 1)}
                        >
                          {option}
                        </MenuItem>
                      ))}
                    </MenuList>
                  </ClickAwayListener>
                </Paper>
              </Popper> */}
              <Dialog
                // fullWidth={fullWidth}

                // maxWidth={maxWidth}

                open={dialogOpen}
                onClose={handleDialogClose}
                sx={{
                  "&::webkit-scrollbar": {
                    width: "1px",
                  },
                }}
              >
                <DialogTitle
                  sx={{
                    justifyContent: "space-between",
                    alignItems: "center",
                    height: "max-content",
                    padding: ".5rem",
                    paddingLeft: "1rem",
                    backgroundColor: "#EAE9FF40",
                    // borderBottom: "1px solid grey",
                    display: "flex",
                  }}
                >
                  <Typography variant="h6">New Bank Key</Typography>

                  <IconButton
                    sx={{ width: "max-content" }}
                    onClick={handleDialogClose}
                    children={<CloseIcon />}
                  />
                </DialogTitle>

                <DialogContent sx={{ padding: ".5rem 1rem" }}>
                  <Grid container>
                    <Grid
                      item
                      md={5}
                      sx={{
                        width: "100%",

                        marginTop: ".5rem",

                        marginRight: "5rem",
                      }}
                    >
                      <Typography>
                        Bank Country
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                      <FormControl
                        fullWidth
                        sx={{ margin: ".5em 0px", minWidth: "250px" }}
                      >
                        <Autocomplete
                          sx={{ height: "31px" }}
                          fullWidth
                          size="small"
                          onChange={(e, value) => {
                            setNewBankCtryReg(value);
                          }}
                          options={dropDownData?.CountryReg ?? []}
                          getOptionLabel={(option) => {
                            if (option?.code)
                              return `${option?.code} - ${option?.desc}` ?? "";
                            else return "";
                          }}
                          // value={rmSearchForm?.costCenterCategory}
                          renderOption={(props, option) => (
                            <li {...props}>
                              <Typography style={{ fontSize: 12 }}>
                                {`${option?.code} - ${option?.desc}`}
                              </Typography>
                            </li>
                          )}
                          renderInput={(params) => (
                            <TextField
                              sx={{ fontSize: "12px !important" }}
                              {...params}
                              variant="outlined"
                              placeholder="SELECT BANK COUNTRY"
                              error={countyValid}
                            />
                          )}
                        />
                      </FormControl>
                      {countyValid && (
                        <Typography variant="caption" color="error">
                          Please Select a Country.
                        </Typography>
                      )}
                    </Grid>

                    <Grid
                      item
                      md={5}
                      sx={{ width: "100%", marginTop: ".5rem" }}
                    >
                      <Typography>
                        Bank Key
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                      <FormControl
                        fullWidth
                        sx={{ margin: ".5em 0px", minWidth: "250px" }}
                      >
                        <TextField
                          // className={classes.input}
                          sx={{ fontSize: "12px !important", height: "31px" }}
                          fullWidth
                          size="small"
                          value={newBankKey}
                          onChange={(e) => {
                            let bankKeyValue = e.target.value;
                            let bankKeyUpperCase = bankKeyValue.toUpperCase();
                            setNewBankKey(bankKeyUpperCase);
                          }}
                          inputProps={{
                            maxLength: 15,
                            style: { textTransform: "uppercase" },
                          }}
                          placeholder="Enter Bank Key"
                          // error={newCostCenterName === "" ? true : false}
                          required={true}
                          // error={newBankKeyValid}
                        />
                      </FormControl>
                      {/* {newBankKeyValid && (
                        <Typography variant="caption" color="error">
                          Please enter a Bank Key.
                        </Typography>
                      )} */}
                    </Grid>
                  </Grid>
                  {isValidationError && (
                    <Grid>
                      <Typography style={{ color: "red" }}>
                        Please Enter Mandatory Fields
                      </Typography>
                    </Grid>
                  )}
                  {checkValidationBankKey && (
                    <Grid>
                      <Typography style={{ color: "red" }}>
                        *The Bank Key with this Country already exist. Please
                        enter different Bank Key or Country
                      </Typography>
                    </Grid>
                  )}
                </DialogContent>

                <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
                  <Button
                    sx={{ width: "max-content", textTransform: "capitalize" }}
                    onClick={handleDialogClose}
                  >
                    Cancel
                  </Button>

                  <Button
                    className="button_primary--normal"
                    type="save"
                    onClick={handleDialogProceed}
                    variant="contained"
                  >
                    Proceed
                  </Button>

                  {/* {console.log(ValidationErrorList)} */}
                </DialogActions>
              </Dialog>
             
              <ButtonGroup
                variant="contained"
                ref={anchorRef}
                aria-label="split button"
              >
                <Button
                  size="small"
                  onClick={() => handleClick(options[0], 0)}
                  sx={{ cursor: "default" }}
                >
                  {options[0]}
                </Button>
                <Button
                  size="small"
                  aria-controls={openButton ? "split-button-menu" : undefined}
                  aria-expanded={openButton ? "true" : undefined}
                  aria-label="select action"
                  aria-haspopup="menu"
                  onClick={handleToggle}
                >
                  <ReusableIcon
                    iconName={"ArrowDropUp"}
                    iconColor={"#FFFFFF"}
                  />
                </Button>
              </ButtonGroup>
              <Popper
                sx={{
                  zIndex: 1,
                }}
                open={openButton}
                anchorEl={anchorRef.current}
                placement={"top-end"}
              >
                <Paper style={{ width: anchorRef.current?.clientWidth }}>
                  <ClickAwayListener onClickAway={handleCloseButton}>
                    <MenuList id="split-button-menu" autoFocusItem>
                      {options.slice(1).map((option, index) => (
                        <MenuItem
                          key={option}
                          selected={index === selectedIndex - 1}
                          onClick={() => handleClick(option, index + 1)}
                        >
                          {option}
                        </MenuItem>
                      ))}
                    </MenuList>
                  </ClickAwayListener>
                </Paper>
              </Popper>
              <ButtonGroup
                variant="contained"
                ref={anchorRefChange}
                aria-label="split button"
              >
                <Button
                  size="small"
                  onClick={() => handleClickChange(optionsChange[0], 0)}
                  sx={{ cursor: "default" }}
                >
                  {optionsChange[0]}
                </Button>
                <Button
                  size="small"
                  aria-controls={
                    openButtonChange ? "split-button-menu" : undefined
                  }
                  aria-expanded={openButtonChange ? "true" : undefined}
                  aria-label="select action"
                  aria-haspopup="menu"
                  onClick={handleToggleChange}
                >
                  <ReusableIcon
                    iconName={"ArrowDropUp"}
                    iconColor={"#FFFFFF"}
                  />
                </Button>
              </ButtonGroup>
              <Popper
                sx={{
                  zIndex: 1,
                }}
                open={openButtonChange}
                anchorEl={anchorRefChange.current}
                placement={"top-end"}
              >
                <Paper style={{ width: anchorRefChange.current?.clientWidth }}>
                  <ClickAwayListener onClickAway={handleCloseButtonChange}>
                    <MenuList id="split-button-menu" autoFocusItem>
                      {optionsChange.slice(1).map((option, index) => (
                        <MenuItem
                          key={option}
                          selected={index === selectedIndexChange - 1}
                          onClick={() => handleClickChange(option, index + 1)}
                        >
                          {option}
                        </MenuItem>
                      ))}
                    </MenuList>
                  </ClickAwayListener>
                </Paper>
              </Popper>

              {enableDocumentUpload && (
                <AttachmentUploadDialog
                  artifactId=""
                  artifactName=""
                  setOpen={setEnableDocumentUpload}
                  handleUpload={uploadExcel}
                />
              )}
            </BottomNavigation>
          </Paper>
          ):""
          }
        </Stack>
      </div>
    </div>
       }
    </>
   
  );
};

export default BankKey;
