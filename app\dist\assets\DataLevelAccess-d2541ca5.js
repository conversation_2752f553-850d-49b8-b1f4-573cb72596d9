import{r as p,o as i}from"./index-17b8d91e.js";import{i as m}from"./index-903d9ad4.js";import"./react-beautiful-dnd.esm-0e527744.js";import"./useMediaQuery-6a073ac5.js";import"./DialogContentText-631f3833.js";import"./CardMedia-bfb247e7.js";import"./Container-d04f3413.js";import"./InputAdornment-5b0053c5.js";import"./ListItemButton-1f7a8ca3.js";import"./Slider-3eb7e770.js";import"./Stepper-88e4fb0c.js";import"./StepButton-34497717.js";import"./ToggleButtonGroup-cf875764.js";import"./index-8303770a.js";import"./toConsumableArray-c7e4bd84.js";import"./Check-29d766a7.js";import"./clsx-a965ebfb.js";import"./Add-98854918.js";import"./DeleteOutline-30de3a49.js";import"./Delete-9f4d7a45.js";import"./asyncToGenerator-88583e02.js";import"./DeleteOutlineOutlined-d5f371f3.js";import"./FileDownloadOutlined-c800f30b.js";import"./AddOutlined-5a7e5250.js";import"./EditOutlined-36c8ca4d.js";import"./Edit-1992ad62.js";import"./index.esm-be84327e.js";import"./makeStyles-213dac7f.js";import"./useSlotProps-e34e1e13.js";import"./Settings-286513ab.js";import"./VisibilityOutlined-315d5644.js";import"./index-d861d92d.js";import"./FiberManualRecord-8d9b9f72.js";import"./dayjs.min-ce01f2c7.js";import"./CheckBox-2bbb2661.js";import"./DeleteOutlined-888bfc33.js";let e=class extends p.Component{constructor(r){super(r),this.state={hasError:!1}}static getDerivedStateFromError(){return{hasError:!0}}componentDidCatch(r,o){console.error("Error caught in ErrorBoundary:",r,o)}render(){return this.state.hasError?i.jsx("div",{children:"Error fetching data"}):this.props.children}};const M=({roleDetails:t,destinations:r})=>i.jsx(e,{children:t&&i.jsx(m.TextRules,{orchestration:!1,ruleDetails:t,destinations:r,saveHandler:o=>console.log(o),translationDataObjects:[]})});export{M as default};
