import{s as Ua,u as ce,q as Da,r as y,dk as aa,a as t,a3 as se,T as U,j as S,l as K,an as ie,A as le,bK as ue,aD as he,bL as Te,bM as me,bN as Ja,bO as ua,bP as oe,f as ne,g as de,bR as i,dl as Ae,K as La,Z as va,$ as Oa,dm as Ee,V as De,aF as be,W as Ne,E as Ma,M as Se,X as Ce,t as pa,B as Sa,a0 as ae,I as Ie,bQ as xe,e as _e,dn as re,a4 as F,dp as Na,dq as Pa,b4 as wa,G as la,aX as ee,F as Ea,cc as Ge,a5 as te}from"./index-75c1660a.js";import{F as Ra}from"./FilterField-a06a27ac.js";import{u as fe}from"./useCustomDtCall-bfc3ebdd.js";import{u as ge}from"./useChangeLogUpdate-7dd0a930.js";import{S as ye}from"./SingleSelectDropdown-0d30aa01.js";import{G as Va}from"./GenericViewGeneral-149677e8.js";import{d as Pe}from"./Edit-77a8cc20.js";const Re="1",Le=({materialID:a,selectedMaterialNumber:C})=>{var B,oa;const z=Ua(),m=ce(),o=new URLSearchParams(m.search).get("RequestId"),A=Da(n=>n.payload.payloadData),$=Da(n=>{var f,L,v,P;return((P=(v=(L=(f=n.payload[a])==null?void 0:f.payloadData)==null?void 0:L.TaxData)==null?void 0:v.TaxData)==null?void 0:P.TaxDataSet)||[]}),{updateChangeLog:xa}=ge(),Ta=y.useRef(!1);y.useEffect(()=>{if(!$.length||Ta.current)return;let n=!1;const f=$.map(L=>{var v;if(!L.SelectedTaxClass){const P=(v=L.options)==null?void 0:v.find(ea=>ea.code===Re);if(P)return n=!0,{...L,SelectedTaxClass:{TaxClass:P.code,TaxClassDesc:P.desc}}}return L});n&&(z(aa({materialID:a,viewID:"TaxData",itemID:"TaxData",keyName:"TaxDataSet",data:f})),Ta.current=!0)},[$,z,a]);const ma=(n,f,L,v)=>{const P=[...$];P[n]={...P[n],SelectedTaxClass:f},z(aa({materialID:a,viewID:"TaxData",itemID:"TaxData",keyName:"TaxDataSet",data:P})),o&&xa({materialID:C,viewName:i.TAX_DATA,plantData:`${L}-${v}`,fieldName:"Tax Class",jsonName:"SelectedTaxClass",currentValue:f.TaxClass,requestId:A==null?void 0:A.RequestId,childRequestId:o})};return $.length===0?t(U,{sx:{textAlign:"center",marginTop:"10px"},children:(B=se)==null?void 0:B.TAXDATA_LOADING}):S(de,{sx:{marginBottom:"20px",boxShadow:3,borderRadius:"10px",borderColor:(oa=K)==null?void 0:oa.primary.white},children:[t(le,{expandIcon:t(ie,{}),sx:{backgroundColor:K.primary.whiteSmoke,borderRadius:"10px",padding:"8px 16px","&:hover":{backgroundColor:K.hover.hoverbg}},children:t(U,{variant:"h6",sx:{fontWeight:"bold"},children:"Tax Classification"})}),t(ne,{children:S(ue,{component:he,children:[t(U,{variant:"h6",sx:{p:1,fontWeight:"bold",textAlign:"center"},children:"Tax Data"}),S(Te,{children:[t(me,{children:S(Ja,{sx:{backgroundColor:"#f5f5f5"},children:[t(ua,{sx:{fontWeight:"bold"},children:"Country"}),t(ua,{sx:{fontWeight:"bold"},children:"Tax Type"}),t(ua,{sx:{fontWeight:"bold"},children:"Tax Class"}),t(ua,{sx:{fontWeight:"bold"},children:"Description"})]})}),t(oe,{children:$.map(({Country:n,TaxType:f,options:L=[],SelectedTaxClass:v},P)=>{const ea=v?{code:v.TaxClass,desc:v.TaxClassDesc}:{code:"",desc:""};return S(Ja,{children:[t(ua,{sx:{fontWeight:"bold"},children:n}),t(ua,{sx:{fontWeight:"bold"},children:f}),t(ua,{children:t(ye,{options:L,value:ea,onChange:na=>{const ba=na?{TaxClass:na.code,TaxClassDesc:na.desc}:null;ma(P,ba,n,f)},placeholder:"SELECT TAX CLASS",minWidth:200})}),t(ua,{children:ea.desc})]},`${n}-${f}`)})})]})]})})]},"Tax_Classification")},ve=a=>{var ea,na,ba,_a,j,ra,Ga,fa,ga,da,ya;const[C,z]=y.useState(!1),[m,ha]=y.useState(null),o=Da(h=>h.payload),[A,$]=y.useState(((ba=(na=(ea=o==null?void 0:o[a.materialID])==null?void 0:ea.payloadData)==null?void 0:na.Classification)==null?void 0:ba.classification)||[]),[xa,Ta]=y.useState([]),ma=Ua();y.useEffect(()=>{var h,r,l,e,u,c,d,s;(e=(l=(r=(h=o==null?void 0:o[a.materialID])==null?void 0:h.payloadData)==null?void 0:r.Classification)==null?void 0:l.basic)!=null&&e.Classtype&&Ae((s=(d=(c=(u=o==null?void 0:o[a.materialID])==null?void 0:u.payloadData)==null?void 0:c.Classification)==null?void 0:d.basic)==null?void 0:s.Classtype,ma)},[(Ga=(ra=(j=(_a=o==null?void 0:o[a.materialID])==null?void 0:_a.payloadData)==null?void 0:j.Classification)==null?void 0:ra.basic)==null?void 0:Ga.Classtype]),y.useEffect(()=>{var h,r,l,e,u,c,d,s,E,D,I,x,b;(e=(l=(r=(h=o==null?void 0:o[a.materialID])==null?void 0:h.payloadData)==null?void 0:r.Classification)==null?void 0:l.basic)!=null&&e.Classnum&&(!(A!=null&&A.length)||A!=null&&A.length&&((u=A[0])==null?void 0:u.className)!=((E=(s=(d=(c=o==null?void 0:o[a.materialID])==null?void 0:c.payloadData)==null?void 0:d.Classification)==null?void 0:s.basic)==null?void 0:E.Classnum))&&B((b=(x=(I=(D=o==null?void 0:o[a.materialID])==null?void 0:D.payloadData)==null?void 0:I.Classification)==null?void 0:x.basic)==null?void 0:b.Classnum)},[(ya=(da=(ga=(fa=o==null?void 0:o[a.materialID])==null?void 0:fa.payloadData)==null?void 0:ga.Classification)==null?void 0:da.basic)==null?void 0:ya.Classnum]),y.useEffect(()=>{ma(aa({materialID:(a==null?void 0:a.materialID)||"",keyName:"",data:A??null,viewID:a==null?void 0:a.activeViewTab,itemID:"classification"}))},[A]);const B=h=>{const r=e=>{if((e==null?void 0:e.statusCode)===ae.STATUS_200){console.log("characteristics",e==null?void 0:e.body);const u=e.body.map((c,d)=>({id:d+1,characteristic:c.code,description:c.desc,value:"",className:h}));$(u)}},l=e=>{console.error(e)};La(`/${va}${Oa.DATA.GET_CHARACTERISTICS_BY_CLASS}?className=${h}`,"get",r,l)},oa=h=>{ha(h),z(!0),n(h.characteristic)},n=h=>{const r=e=>{(e==null?void 0:e.statusCode)===ae.STATUS_200&&Ta(e.body)},l=e=>{console.error(e)};La(`/${va}${Oa.DATA.GET_CHARACTERISTIC_VALUES}?characteristics=${h}`,"get",r,l)},f=()=>{z(!1),ha(null)},L=h=>{ha(r=>({...r,value:h.target.value}))},v=()=>{$(h=>h.map(r=>r.id===m.id?{...r,value:m.value}:r)),z(!1)},P=[{field:"characteristic",headerName:"Characteristic",flex:1,headerClassName:"super-app-theme--header",renderHeader:()=>t(U,{variant:"body2",fontWeight:"bold",children:"Characteristic"})},{field:"description",headerName:"Description",flex:2,headerClassName:"super-app-theme--header",renderHeader:()=>t(U,{variant:"body2",fontWeight:"bold",children:"Description"})},{field:"value",flex:1,headerAlign:"left",align:"left",headerClassName:"super-app-theme--header",renderHeader:()=>S(Sa,{sx:{display:"flex",alignItems:"center"},children:[t(U,{variant:"body2",fontWeight:"bold",children:"Value"}),t(U,{color:"error",sx:{ml:.5},children:"*"})]}),renderCell:h=>t("span",{children:h.value&&String(h.value).trim()!==""?h.value:"--"})},{field:"actions",headerName:"Actions",width:100,sortable:!1,headerClassName:"super-app-theme--header",renderHeader:()=>t(U,{variant:"body2",fontWeight:"bold",children:"Actions"}),renderCell:h=>t(Ie,{color:"primary",size:"small",onClick:()=>oa(h.row),children:t(Pe,{})})}];return S(Sa,{sx:{backgroundColor:"white",border:`1px solid ${K.hover.hoverbg}`,borderRadius:"8px",boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",px:3,py:2,mb:3,mt:2},children:[t(U,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:a==null?void 0:a.characteristicDetails[0]}),A.length>0?S("div",{style:{width:"100%",height:A.length*53+56},children:[" ",t(Ee,{rows:A,columns:P,hideFooter:!0,disableColumnMenu:!0,disableSelectionOnClick:!0,sx:{fontFamily:"'Roboto','Helvetica','Arial',sans-serif",fontSize:"0.875rem","& .MuiDataGrid-columnHeaders":{backgroundColor:"#f3f3fc",borderBottom:"1px solid #dcdcdc"},"& .MuiDataGrid-columnHeaderTitle":{fontWeight:600,fontSize:"0.875rem"},"& .MuiDataGrid-cell":{color:"#333",fontSize:"0.875rem"}}})]}):t(U,{variant:"body2",sx:{color:"#888"},children:"No characteristic data available."}),S(De,{open:C,onClose:f,fullWidth:!0,maxWidth:"sm",children:[t(be,{children:"Edit Entry"}),S(Ne,{children:[t(Ma,{margin:"dense",label:"Characteristic",fullWidth:!0,value:(m==null?void 0:m.characteristic)||"",disabled:!0}),t(Ma,{margin:"dense",label:"Description",fullWidth:!0,value:(m==null?void 0:m.description)||"",disabled:!0}),t(Ma,{margin:"dense",label:"Value",fullWidth:!0,select:!0,value:(m==null?void 0:m.value)||"",onChange:L,children:xa.map(h=>t(Se,{value:h.code,children:h.code},h.code))})]}),S(Ce,{children:[t(pa,{onClick:f,color:"secondary",children:"Cancel"}),t(pa,{onClick:v,variant:"contained",children:"Save"})]})]})]})},We=a=>{var h,r;const C=Da(l=>l.payload),z=Da(l=>{var e;return(e=l==null?void 0:l.request)==null?void 0:e.materialRows}),m=(r=(h=C==null?void 0:C[a.materialID])==null?void 0:h.headerData)==null?void 0:r.orgData;Da(l=>l.userManagement.taskData);const[ha,o]=y.useState({}),A=Da(l=>{var e;return(e=l.payload.payloadData)==null?void 0:e.RequestType}),[$,xa]=y.useState([]),[Ta,ma]=y.useState([]),B=Ua(),{getDtCall:oa,dtData:n}=fe(),{customError:f}=xe(),{t:L}=_e();function v(l,e){const u={plant:"Plant",salesOrg:"SalesOrg",dc:"Distribution Channel",sloc:"Storage Location",mrpProfile:"MRP Profile",warehouse:"Warehouse"},c=l.split("-");return e.map((d,s)=>{const E=u[d],D=c[s]||"N/A";return`${E} - ${D}`}).join(", ")}const P=l=>[...new Set(l)].join("$^$"),ea=l=>{const e=new Map;return l.forEach(({CountryName:u,Country:c})=>{e.set(c,u)}),Array.from(e,([u,c])=>({Country:u,CountryName:c}))},na=l=>l.map(({Country:e})=>e).join("$^$"),ba=l=>{const e=`/${va}${Oa.TAX_DATA.GET_COUNTRY_SALESORG}`;La(e,"post",s=>{const E=s==null?void 0:s.body,D=ea(E),I=na(D);_a(I)},s=>{f(te.NO_DATA_AVAILABLE)},{salesOrg:l})},_a=l=>{const e=`/${va}${Oa.TAX_DATA.GET_TAX_COUNTRY}`;La(e,"post",s=>{var O,M,Y,Q;const E=s==null?void 0:s.body,D=((Q=(Y=(M=(O=C[a==null?void 0:a.materialID])==null?void 0:O.payloadData)==null?void 0:M.TaxData)==null?void 0:Y.TaxData)==null?void 0:Q.TaxDataSet)||[],I={},x=E.filter(N=>N.TaxType);x.forEach(({TaxClass:N,TaxClassDesc:k})=>{I[N]=k});const b=D.map(N=>{const k=x.filter(T=>T.TaxType===N.TaxType&&T.Country===N.Country).map(T=>({code:T.TaxClass,desc:T.TaxClassDesc}));let _=N.SelectedTaxClass;return _&&I[_.TaxClass]&&(_={..._,TaxClassDesc:I[_.TaxClass]}),{...N,options:k,SelectedTaxClass:_}});x.forEach(({TaxType:N,SequenceNo:k,Country:_,TaxClass:T,TaxClassDesc:g})=>{if(!b.some(w=>w.TaxType===N&&w.Country===_)){const w=x.filter(H=>H.TaxType===N&&H.Country===_).map(H=>({code:H.TaxClass,desc:H.TaxClassDesc}));b.push({TaxType:N,SequenceNo:k,Country:_,options:w,SelectedTaxClass:null})}}),B(aa({materialID:(a==null?void 0:a.materialID)||"",keyName:"TaxDataSet",data:b,viewID:"TaxData",itemID:"TaxData"}))},s=>{f(te.NO_DATA_AVAILABLE)},{country:l})},j=z==null?void 0:z.find(l=>(l==null?void 0:l.id)===a.materialID);y.useEffect(()=>{var l,e,u,c,d,s,E,D,I;if(m){const x=!!((e=(l=C[a.materialID])==null?void 0:l.headerData)!=null&&e.refMaterialData),b=re(m,(u=C==null?void 0:C[a.materialID])==null?void 0:u.payloadData,a==null?void 0:a.materialID,B);o(b),!x&&!a.isDisplay&&b.hasOwnProperty(i.SALES)&&((c=a==null?void 0:a.selectedViews)!=null&&c.includes(i.SALES))&&b[i.SALES].reduxCombinations.forEach((O,M)=>{A!==F.EXTEND&&Ga({comb:O,dt:Na.SALES_DIV_PRICE_MAPPING},m[M])}),(!x&&((d=a==null?void 0:a.selectedViews)!=null&&d.includes(i.SALES))||(s=a==null?void 0:a.selectedViews)!=null&&s.includes(i.ACCOUNTING)||(E=a==null?void 0:a.selectedViews)!=null&&E.includes(i.COSTING))&&m.forEach((O,M)=>{A!==F.EXTEND&&!a.isDisplay&&fa({combinations:b,index:M,dt:Na.REG_PLNT_INSPSTK_MAPPING},O)}),x&&ra(b,(I=(D=C[a.materialID])==null?void 0:D.headerData)==null?void 0:I.refMaterialData)}else o({})},[m]),y.useEffect(()=>{if(m){const l=[...new Set(m==null?void 0:m.map(u=>{var c;return(c=u.salesOrg)==null?void 0:c.code}))],e=P(l);ba(e)}},[m,a==null?void 0:a.callGetCountryBasedonSalesOrg]),y.useEffect(()=>{var l,e,u,c,d,s,E,D,I,x,b,O,M,Y,Q,N,k,_,T,g,R,w,H,ca,sa,ta,X,Aa,Ca,Ia,G,W,q,Z,J,ka,Ha,Wa,qa,Ba,$a,ja,Xa,Fa,Ka,za,Ya,Qa,Za;if(n){if(((l=n.customParam)==null?void 0:l.dt)===Na.SALES_DIV_PRICE_MAPPING&&((e=a==null?void 0:a.selectedViews)!=null&&e.includes(i.SALES))){const ia=(c=Object.keys((u=n==null?void 0:n.data)==null?void 0:u.result[0]))!=null&&c.length?(D=(E=(s=(d=n==null?void 0:n.data)==null?void 0:d.result)==null?void 0:s[0])==null?void 0:E.MDG_MAT_SALESDIV_PRCICEGRP_MAPPING[0])==null?void 0:D.MDG_MAT_MATERIAL_PRICING_GROUP:"";A!==F.EXTEND&&A!==F.CREATE_WITH_UPLOAD&&ia&&da((I=n.customParam)==null?void 0:I.comb,"MatPrGrp",ia,"Sales")}else if(((x=n.customParam)==null?void 0:x.dt)===Na.REG_PLNT_INSPSTK_MAPPING){let ia=(b=n.customParam)==null?void 0:b.combinations,V=(O=n.customParam)==null?void 0:O.org;if(ia!=null&&ia.hasOwnProperty(i.SALES)&&((M=a==null?void 0:a.selectedViews)!=null&&M.includes(i.SALES))){const p=(Q=Object.keys((Y=n==null?void 0:n.data)==null?void 0:Y.result[0]))!=null&&Q.length?(T=(_=(k=(N=n==null?void 0:n.data)==null?void 0:N.result)==null?void 0:k[0])==null?void 0:_.MDG_MAT_REG_PLNT_INSPSTK_ITMCATGRP_MAPPING[0])==null?void 0:T.MDG_MAT_ITEM_CAT_GROUP:"";A!==F.EXTEND&&A!==F.CREATE_WITH_UPLOAD&&p&&da(((g=V==null?void 0:V.salesOrg)==null?void 0:g.code)+"-"+((w=(R=V==null?void 0:V.dc)==null?void 0:R.value)==null?void 0:w.code),"ItemCat",p,"Sales")}if(ia.hasOwnProperty(i.PURCHASING)&&((H=a==null?void 0:a.selectedViews)!=null&&H.includes(i.PURCHASING))){const p=(sa=Object.keys((ca=n==null?void 0:n.data)==null?void 0:ca.result[0]))!=null&&sa.length?(Ca=(Aa=(X=(ta=n==null?void 0:n.data)==null?void 0:ta.result)==null?void 0:X[0])==null?void 0:Aa.MDG_MAT_REG_PLNT_INSPSTK_ITMCATGRP_MAPPING[0])==null?void 0:Ca.MDG_MAT_POST_TO_INSP_STOCK:"";A!==F.EXTEND&&A!==F.CREATE_WITH_UPLOAD&&p&&da((G=(Ia=V==null?void 0:V.plant)==null?void 0:Ia.value)==null?void 0:G.code,"IndPostToInspStock",p,"Purchasing")}if(ia.hasOwnProperty(i.ACCOUNTING)&&((W=a==null?void 0:a.selectedViews)!=null&&W.includes(i.ACCOUNTING))){const p=(Z=Object.keys((q=n==null?void 0:n.data)==null?void 0:q.result[0]))!=null&&Z.length?(Wa=(Ha=(ka=(J=n==null?void 0:n.data)==null?void 0:J.result)==null?void 0:ka[0])==null?void 0:Ha.MDG_MAT_REG_PLNT_INSPSTK_ITMCATGRP_MAPPING[0])==null?void 0:Wa.MDG_MAT_PRICE_UNIT:"";A!==F.EXTEND&&A!==F.CREATE_WITH_UPLOAD&&p&&da((Ba=(qa=V==null?void 0:V.plant)==null?void 0:qa.value)==null?void 0:Ba.code,"PriceUnit",p,"Accounting")}if(ia.hasOwnProperty(i.COSTING)&&(($a=a==null?void 0:a.selectedViews)!=null&&$a.includes(i.COSTING))){const p=(Xa=Object.keys((ja=n==null?void 0:n.data)==null?void 0:ja.result[0]))!=null&&Xa.length?(Ya=(za=(Ka=(Fa=n==null?void 0:n.data)==null?void 0:Fa.result)==null?void 0:Ka[0])==null?void 0:za.MDG_MAT_REG_PLNT_INSPSTK_ITMCATGRP_MAPPING[0])==null?void 0:Ya.MDG_MAT_COSTING_LOT_SIZE:"";A!==F.EXTEND&&A!==F.CREATE_WITH_UPLOAD&&p&&da((Za=(Qa=V==null?void 0:V.plant)==null?void 0:Qa.value)==null?void 0:Za.code,"Lotsizekey",p,"Costing")}}}},[n]);const ra=(l,e)=>{var c;let u=(c=C[a.materialID])==null?void 0:c.payloadData;Object.keys(l).forEach(d=>{var E;let s=(E=l[d])==null?void 0:E.reduxCombinations;s==null||s.forEach(D=>{var I,x,b,O,M,Y,Q,N,k,_,T,g,R,w,H,ca,sa,ta,X,Aa,Ca,Ia;d!==i.BASIC_DATA&&((I=e==null?void 0:e.copyPayload)!=null&&I.payloadData[d])&&!((x=u[d])!=null&&x[D])&&(a!=null&&a.allTabsData[d])&&((O=Object.keys((b=e==null?void 0:e.copyPayload)==null?void 0:b.payloadData[d]))==null||O.forEach(G=>{var q;let W=Pa(G,(q=e==null?void 0:e.copyPayload)==null?void 0:q.payloadData[d][G],a==null?void 0:a.allTabsData[d]);W&&B(aa({materialID:a==null?void 0:a.materialID,viewID:d,itemID:D,keyName:G,data:W}))}),d===i.SALES&&(B(aa({materialID:a==null?void 0:a.materialID,viewID:i.TAX_DATA,itemID:i.TAX_DATA,data:(Q=(Y=(M=e==null?void 0:e.copyPayload)==null?void 0:M.payloadData)==null?void 0:Y.TaxData)==null?void 0:Q.TaxData})),(T=Object.keys((_=(k=(N=e==null?void 0:e.copyPayload)==null?void 0:N.payloadData)==null?void 0:k[i.SALES_GENERAL])==null?void 0:_[i.SALES_GENERAL]))==null||T.forEach(G=>{var q,Z,J;let W=Pa(G,(J=(Z=(q=e==null?void 0:e.copyPayload)==null?void 0:q.payloadData[i.SALES_GENERAL])==null?void 0:Z[i.SALES_GENERAL])==null?void 0:J[G],a==null?void 0:a.allTabsData[i.SALES_GENERAL]);W&&B(aa({materialID:a==null?void 0:a.materialID,viewID:i.SALES_GENERAL,itemID:i.SALES_GENERAL,keyName:G,data:W}))})),d===i.PURCHASING&&((R=(g=e==null?void 0:e.copyPayload)==null?void 0:g.payloadData)!=null&&R[i.PURCHASING_GENERAL])&&((ca=Object.keys((H=(w=e==null?void 0:e.copyPayload)==null?void 0:w.payloadData[i.PURCHASING_GENERAL])==null?void 0:H[i.PURCHASING_GENERAL]))==null||ca.forEach(G=>{var q,Z,J;let W=Pa(G,(J=(Z=(q=e==null?void 0:e.copyPayload)==null?void 0:q.payloadData[i.PURCHASING_GENERAL])==null?void 0:Z[i.PURCHASING_GENERAL])==null?void 0:J[G],a==null?void 0:a.allTabsData[i.PURCHASING_GENERAL]);W&&B(aa({materialID:a==null?void 0:a.materialID,viewID:i.PURCHASING_GENERAL,itemID:i.PURCHASING_GENERAL,keyName:G,data:W}))})),d===i.STORAGE&&((X=(ta=(sa=e==null?void 0:e.copyPayload)==null?void 0:sa.payloadData)==null?void 0:ta[i.STORAGE_GENERAL])!=null&&X[i.STORAGE_GENERAL])&&((Ia=Object.keys((Ca=(Aa=e==null?void 0:e.copyPayload)==null?void 0:Aa.payloadData[i.STORAGE_GENERAL])==null?void 0:Ca[i.STORAGE_GENERAL]))==null||Ia.forEach(G=>{var q,Z,J;let W=Pa(G,(J=(Z=(q=e==null?void 0:e.copyPayload)==null?void 0:q.payloadData[i.STORAGE_GENERAL])==null?void 0:Z[i.STORAGE_GENERAL])==null?void 0:J[G],a==null?void 0:a.allTabsData[i.STORAGE_GENERAL]);W&&B(aa({materialID:a==null?void 0:a.materialID,viewID:i.STORAGE_GENERAL,itemID:i.STORAGE_GENERAL,keyName:G,data:W}))})))})})},Ga=(l,e)=>{var c,d;let u={decisionTableId:null,decisionTableName:Na.SALES_DIV_PRICE_MAPPING,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_MAT_SALES_ORG":(c=e==null?void 0:e.salesOrg)==null?void 0:c.code,"MDG_CONDITIONS.MDG_MAT_DIVISION":(d=C==null?void 0:C.payloadData)==null?void 0:d.Division}]};l.org=e,oa(u,l)},fa=(l,e)=>{var c,d,s;let u={decisionTableId:null,decisionTableName:Na.REG_PLNT_INSPSTK_MAPPING,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":(c=C==null?void 0:C.payloadData)==null?void 0:c.Region,"MDG_CONDITIONS.MDG_MAT_PLANT":(s=(d=e==null?void 0:e.plant)==null?void 0:d.value)==null?void 0:s.code}]};l.org=e,oa(u,l)};y.useEffect(()=>{let l=m!=null&&m.length?m==null?void 0:m.map(()=>!1):[];ma(l)},[a.activeViewTab,m]);const ga=(l,e,u)=>(c,d)=>{ma(s=>({...s,[u]:d}))},da=(l,e,u,c)=>{B(aa({materialID:(a==null?void 0:a.materialID)||"",keyName:e||"",data:u??null,viewID:c,itemID:l}))},ya=y.useMemo(()=>{var I,x,b,O,M,Y,Q,N,k,_;const l=ha[a.activeViewTab]||{},{displayCombinations:e=[],reduxCombinations:u=[],requiredKeys:c=[]}=l,d=Object.entries((a==null?void 0:a.basicDataTabDetails)||{}),s=(I=a.allTabsData)!=null&&I.hasOwnProperty(i.SALES_GENERAL)?Object.entries(a.allTabsData[i.SALES_GENERAL]):[],E=(x=a.allTabsData)!=null&&x.hasOwnProperty(i.PURCHASING_GENERAL)?Object.entries(a.allTabsData[i.PURCHASING_GENERAL]):[],D=(b=a.allTabsData)!=null&&b.hasOwnProperty(i.STORAGE_GENERAL)?Object.entries(a.allTabsData[i.STORAGE_GENERAL]):[];return a.activeViewTab==="Basic Data"?d.map(T=>{var g;return S(la,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:`1px solid ${(g=a==null?void 0:a.missingValidationPlant)!=null&&g.includes(i.BASIC_DATA)&&!(j!=null&&j.validated)?K.error.dark:K.hover.hoverbg} `,mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...wa},children:[t(la,{container:!0,children:t(U,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:T[0]})}),t(Sa,{children:t(la,{container:!0,spacing:1,children:[...T[1]].filter(R=>R.visibility!=="Hidden").sort((R,w)=>R.sequenceNo-w.sequenceNo).map(R=>t(Ra,{disabled:a==null?void 0:a.disabled,field:R,dropDownData:a.dropDownData,materialID:a==null?void 0:a.materialID,selectedMaterialNumber:a==null?void 0:a.selectedMaterialNumber,viewName:a==null?void 0:a.activeViewTab,plantData:"basic"},R.fieldName))})})]},T[0])}):a.activeViewTab==="Classification"?S(Ea,{children:[S(la,{md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:`1px solid ${(O=a==null?void 0:a.missingValidationPlant)!=null&&O.includes(i.BASIC_DATA)&&!(j!=null&&j.validated)?K.error.dark:K.hover.hoverbg} `,mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...wa},children:[t(la,{container:!0,children:t(U,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:(M=d[0])==null?void 0:M[0]})}),t(Sa,{children:t(la,{container:!0,spacing:1,children:[...(Y=d[0])==null?void 0:Y[1]].filter(T=>T.visibility!==ee.HIDDEN1).sort((T,g)=>T.sequenceNo-g.sequenceNo).map(T=>t(Ea,{children:(T==null?void 0:T.visibility)==ee.HIDDEN?t(Ra,{classNum:a==null?void 0:a.classNum,disabled:a==null?void 0:a.disabled,field:T,dropDownData:a.dropDownData,materialID:a==null?void 0:a.materialID,selectedMaterialNumber:a==null?void 0:a.selectedMaterialNumber,viewName:a==null?void 0:a.activeViewTab,plantData:"basic",matType:a==null?void 0:a.matType},T.fieldName):t(Ra,{classNum:a==null?void 0:a.classNum,disabled:a==null?void 0:a.disabled,field:T,dropDownData:a.dropDownData,materialID:a==null?void 0:a.materialID,selectedMaterialNumber:a==null?void 0:a.selectedMaterialNumber,viewName:a==null?void 0:a.activeViewTab,plantData:"basic",matType:a==null?void 0:a.matType},T.fieldName)}))})})]},(Q=d[0])==null?void 0:Q[0]),t(ve,{characteristicDetails:d[1],materialID:a.materialID,selectedMaterialNumber:a==null?void 0:a.selectedMaterialNumber,classNum:a==null?void 0:a.classNum,disabled:a.disabled,dropDownData:a.dropDownData,activeViewTab:a.activeViewTab})]}):e.length?S(Ea,{children:[a.activeViewTab===i.SALES&&S(Ea,{children:[t(Le,{materialID:a.materialID,selectedMaterialNumber:a==null?void 0:a.selectedMaterialNumber}),(s==null?void 0:s.length)>0&&t(Va,{materialID:a.materialID,selectedMaterialNumber:a==null?void 0:a.selectedMaterialNumber,GeneralFields:s,disabled:a.disabled,dropDownData:a.dropDownData,viewName:(N=i)==null?void 0:N.SALES_GENERAL})]}),a.activeViewTab===i.PURCHASING&&S(Ea,{children:[" ",(E==null?void 0:E.length)>0&&t(Va,{materialID:a.materialID,selectedMaterialNumber:a==null?void 0:a.selectedMaterialNumber,GeneralFields:E,disabled:a.disabled,dropDownData:a.dropDownData,viewName:(k=i)==null?void 0:k.PURCHASING_GENERAL})]}),a.activeViewTab===i.STORAGE&&S(Ea,{children:[" ",(D==null?void 0:D.length)>0&&t(Va,{materialID:a.materialID,selectedMaterialNumber:a==null?void 0:a.selectedMaterialNumber,GeneralFields:D,disabled:a.disabled,dropDownData:a.dropDownData,viewName:(_=i)==null?void 0:_.STORAGE_GENERAL})]}),e.map((T,g)=>{var R,w,H,ca,sa;return S(de,{sx:{marginBottom:"20px",boxShadow:3,borderRadius:"10px",borderColor:(R=a==null?void 0:a.missingValidationPlant)!=null&&R.includes(T)&&!(j!=null&&j.validated)?(H=(w=K)==null?void 0:w.error)==null?void 0:H.dark:(ca=K)==null?void 0:ca.primary.white},onChange:ga(T,c,g),expanded:Ta[g],children:[t(le,{expandIcon:t(ie,{}),sx:{backgroundColor:K.primary.whiteSmoke,borderRadius:"10px",padding:"8px 16px","&:hover":{backgroundColor:K.hover.hoverbg}},children:t(U,{variant:"h6",sx:{fontWeight:"bold"},children:v(T,c)})}),t(ne,{children:((sa=$[g])==null?void 0:sa.value)===1?S(Sa,{sx:{display:"flex",alignItems:"center",justifyContent:"center",minHeight:"200px"},children:[" ",t(Ge,{})]}):d.map(ta=>S(la,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...wa},children:[t(la,{container:!0,children:t(U,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:L(ta[0])})}),t(Sa,{children:t(la,{container:!0,spacing:1,children:[...ta[1]].filter(X=>X.visibility!=="Hidden").sort((X,Aa)=>X.sequenceNo-Aa.sequenceNo).map(X=>t(Ra,{disabled:a==null?void 0:a.disabled,field:X,dropDownData:a.dropDownData,materialID:a==null?void 0:a.materialID,selectedMaterialNumber:a==null?void 0:a.selectedMaterialNumber,viewName:a==null?void 0:a.activeViewTab,plantData:u[g]},X.fieldName))})})]},ta[0]))})]},g)})]}):t(U,{variant:"body2",sx:{margin:"20px",color:"gray"},children:"No Org Data selected."})},[ha,a.activeViewTab,a.basicDataTabDetails,$,a.materialID,a.missingValidationPlant,Ta]);return t(Ea,{children:ya})};export{We as G};
