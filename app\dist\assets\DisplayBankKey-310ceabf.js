import{r as C,q as G,s as qt,eP as Ln,a as r,x as ee,j as i,T as g,b6 as Un,E as me,at as rr,au as tr,G as x,ay as ut,F as V,bI as yn,K as k,eK as j,ai as Se,b as _n,u as Wn,bh as Vn,b4 as ft,B as D,br as Ct,aC as xt,bt as St,bs as ze,ab as Ke,bX as Hn,C as Zn,z as gt,V as qe,aF as Me,I as je,aG as er,W as $e,ar as he,X as Oe,t as l,cb as Yn,cc as Gn,al as Xn,b1 as Jn,b8 as $,bm as Le,aD as _,aE as W,aB as f,bq as pe,cd as Qn,eQ as vt,eR as Bn,bp as wn}from"./index-17b8d91e.js";import{d as Rn}from"./ArrowCircleLeftOutlined-2a09f8e2.js";import{d as Ue}from"./EditOutlined-36c8ca4d.js";import{D as nr}from"./DatePicker-68227989.js";import{l as bt}from"./lookup-1dcf10ac.js";import{R as Dn}from"./ReusableAttachementAndComments-bab6bbfc.js";import{M as Pn,a as Kn}from"./UtilDoc-d76e2af6.js";import{d as eo,C as ro}from"./ChangeLog-0f47d713.js";import{T as At}from"./Timeline-36ba02ac.js";import{t as Nt,T as Tt,a as Et,b as Ft,c as It,d as kt}from"./TimelineSeparator-0839d5e3.js";import{S as to,a as no,b as oo}from"./Stepper-88e4fb0c.js";import"./dateViewRenderers-34586552.js";import"./useSlotProps-e34e1e13.js";import"./InputAdornment-5b0053c5.js";import"./CSSTransition-30917e2c.js";import"./useMediaQuery-6a073ac5.js";import"./DesktopDatePicker-07c19cde.js";import"./useMobilePicker-9978caff.js";import"./CloudUpload-27b6d63e.js";import"./utilityImages-067c3dc2.js";import"./Add-98854918.js";import"./Delete-9f4d7a45.js";/* empty css            */import"./FileDownloadOutlined-c800f30b.js";import"./VisibilityOutlined-315d5644.js";import"./DeleteOutlined-888bfc33.js";import"./Slider-3eb7e770.js";import"./clsx-a965ebfb.js";function so(v,J){return Array.isArray(J)&&J.find(X=>X.code===v)||""}const zt=({label:v,value:J,units:B,data:X,onSave:I,isEditMode:ue,visibility:p,length:ge,isExtendMode:or,options:sr=[],type:u})=>{var T,We;const[P,se]=C.useState(J),[Mt,ye]=C.useState(!1),[N,ve]=C.useState(!1),A=G(a=>a.AllDropDown.dropDown),fe=qt(),_e=so(P,A);G(a=>a.bankKey.requiredFields),console.log("editedValue",v,P),console.log("dropdownData",A),console.log("value e",J),console.log("label",v),console.log("units",B),console.log("transformedValue",_e),G(a=>a.edit.payload),console.log("editField",P);const ce={label:v,value:P,units:B,type:u};console.log("fieldData",ce),C.useEffect(()=>{ue&&(p==="0"||p==="Required")&&fe(Ln(S))},[]);let S=v.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("");C.useEffect(()=>{se(J)},[J]);const re=a=>{fe(yn({keyname:S.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""),data:a}))},F=a=>{const b=w=>{console.log("value",a),fe(Se({keyName:"Region1",data:w.body}))},m=w=>{console.log(w,"error in dojax")};k(`/${j}/data/getRegionBasedOnCountry?country=${a}`,"get",b,m)},cr=a=>{const b=w=>{fe(Se({keyName:"Region2",data:w.body}))},m=w=>{console.log(w,"error in dojax")};k(`/${j}/data/getRegionBasedOnCountry?country=${a}`,"get",b,m)};return r(x,{item:!0,children:r(ee,{children:ue?i(V,{children:[i(g,{variant:"body2",color:"#777",children:[v,p==="Required"||p==="0"?r("span",{style:{color:"red"},children:"*"}):""]}),u==="Drop Down"?r(Un,{options:A[S]??[],value:X[S]&&((T=A[S])==null?void 0:T.filter(a=>a.code===X[S]))&&X[S]&&((We=A[S])==null?void 0:We.filter(a=>a.code===X[S])[0])||"",onChange:(a,b,m)=>{(p==="Required"||p==="0")&&b===null&&ve(!0),v==="Country 1"&&F(b==null?void 0:b.code),v==="Country 2"&&cr(b==null?void 0:b.code),re(m==="clear"?"":b==null?void 0:b.code),se(b==null?void 0:b.code),ye(!0)},getOptionLabel:a=>a===""||(a==null?void 0:a.code)===""?"":`${a==null?void 0:a.code} - ${a==null?void 0:a.desc}`??"",renderOption:(a,b)=>(console.log("option vakue",b),r("li",{...a,children:r(g,{style:{fontSize:12},children:`${b==null?void 0:b.code} - ${b==null?void 0:b.desc}`})})),renderInput:a=>r(me,{...a,variant:"outlined",placeholder:`Select ${ce.label}`,size:"small",label:null,error:N})}):u==="Input"?r(me,{variant:"outlined",size:"small",value:X[S].toUpperCase(),placeholder:`Enter ${ce.label}`,inputProps:{maxLength:ge},onChange:a=>{const b=a.target.value;if(b.length>0&&b[0]===" ")re(b.trimStart());else{let m=b.toUpperCase();re(m)}(p==="Required"||p==="0")&&b.length<=0&&ve(!0),se(b.toUpperCase())},error:N}):u==="Calendar"?r(rr,{dateAdapter:tr,children:r(nr,{slotProps:{textField:{size:"small"}},placeholder:"Select Date Range"})}):u==="Radio Button"?r(x,{item:!0,md:2,children:r(ut,{sx:{padding:0},checked:X[S],onChange:(a,b)=>{re(b),se(b)}})}):""]}):r(V,{children:i(V,{children:[i(g,{variant:"body2",color:"#777",children:[v,p==="Required"||p==="0"?r("span",{style:{color:"red"},children:"*"}):""]}),r(g,{variant:"body2",fontWeight:"bold",children:u==="Radio Button"?r(ut,{sx:{padding:0},checked:P,disabled:!0}):P})]})})})})},Lo=()=>{var vr,br,Ar,Nr,Tr,Er,Fr,Ir,kr,zr,qr,Mr,jr,$r,Or,Lr,Ur,yr,_r,Wr,Vr,Hr,Zr,Yr,Gr,Xr,Jr,Qr,Br,wr,Rr,Dr,Pr,Kr,et,rt,tt,nt,ot,st;const[v,J]=C.useState(!1);C.useState(0);const[B,X]=C.useState(!0);C.useState({});const[I,ue]=C.useState([]),[p,ge]=C.useState(0),[or,sr]=C.useState([]),[u,P]=C.useState(),[se,Mt]=C.useState(!1);C.useState([]);const[ye,N]=C.useState(!1),[ve,A]=C.useState(!1),[fe,_e]=C.useState(""),[ce,S]=C.useState(""),[re,F]=C.useState(!1),[cr,T]=C.useState(!0),[We,a]=C.useState(!1),[b,m]=C.useState(!1),[w,be]=C.useState(!1),[jt,Ae]=C.useState(!1),[$t,Ve]=C.useState(!1),[Ot,ir]=C.useState(!1),[R,Ne]=C.useState(""),[Te,Lt]=C.useState(!0),[ie,Ut]=C.useState(""),[le,yt]=C.useState([]),[de,_t]=C.useState([]),[Wt,lr]=C.useState(!1),[Ce,Q]=C.useState(!0),[Vt,Ht]=C.useState(!1),[He,Zt]=C.useState([]),[Yt,dr]=C.useState(!1),[Gt,ar]=C.useState(!1),[Xt,Ze]=C.useState(!1),[Jt,hr]=C.useState(!1),[Ye,xe]=C.useState(!1),te=qt(),Z=_n(),Qt=Wn(),Bt=G(s=>s.appSettings);let Ee=G(s=>{var d;return(d=s.userManagement.entitiesAndActivities)==null?void 0:d["Bank Key"]}),z=G(s=>{var d;return(d=s==null?void 0:s.initialData)==null?void 0:d.IWMMyTask}),c=G(s=>s.userManagement.userData),t=Qt.state,n=G(s=>s.userManagement.taskData);const e=G(s=>s.edit.payload);let wt=G(s=>s.edit.payload),Rt=G(s=>s.bankKey.requiredFields);const Ge=G(s=>s.bankKey.bankKeyViewData);console.log("ccroewdata",e),console.log("bankKeyRowData",t),console.log("Remarks",u);const pr=[{field:"id",headerName:"Document ID",flex:1,hide:!0},{field:"docType",headerName:"Type",flex:1},{field:"docName",headerName:"Document Name",flex:1},{field:"uploadedOn",headerName:"Uploaded On",flex:1,align:"center",headerAlign:"center"},{field:"uploadedBy",headerName:"Uploaded By",sortable:!1,flex:1},{field:"action",headerName:"Action",sortable:!1,filterable:!1,align:"center",headerAlign:"center",flex:1,renderCell:s=>i(V,{children:[r(Pn,{index:s.row.id,name:s.row.docName}),r(Kn,{index:s.row.id,name:s.row.docName})]})}],Dt=()=>{let s=n!=null&&n.subject?n==null?void 0:n.subject:t==null?void 0:t.requestId,d=o=>{console.log(o.documentDetailDtoList,"data.documentDetailDtoList");var h=[];o.documentDetailDtoList.forEach(E=>{console.log(o.documentDetailDtoList,"data.");var q={id:E.documentId,docType:E.fileType,docName:E.fileName,uploadedOn:Ke(E.docCreationDate).format(Bt.date),uploadedBy:E.createdBy};console.log(q,"tempRow"),h.push(q)}),yt(h)};k(`/${pe}/documentManagement/getDocByRequestId/${s}`,"get",d)},Pt=()=>{let s=n!=null&&n.subject?n==null?void 0:n.subject:t==null?void 0:t.requestId,d=h=>{console.log("commentsdata",h);var E=[];h.body.forEach(q=>{var M={id:q.requestId,comment:q.comment,user:q.createdByUser,createdAt:q.updatedAt};E.push(M)}),_t(E),console.log("commentrows",E.length)},o=h=>{console.log(h)};k(`/${j}/activitylog/fetchTaskDetailsForRequestId?requestId=${s}`,"get",d,o)};console.log("activeTabName",I);const Kt=()=>{I[p];let s=Object.entries(Ge);console.log("viewDataArray",s);const d={};s.map(o=>{console.log("bottle",o[1]);let h=Object.entries(o[1]);return console.log("notebook",h),h.forEach(E=>{E[1].forEach(q=>{d[q.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("")]=q.value})}),o}),console.log("toSetArray",d),te(Qn(d))};C.useEffect(()=>{Ge.length!==0&&Kt()},[Ge]);var O={AddressDto:{AddressID:u!=null&&u.AddressId?u==null?void 0:u.AddressId:"",Title:e!=null&&e.Title?e==null?void 0:e.Title:"",Name:e!=null&&e.Name?e==null?void 0:e.Name:"",Name2:e!=null&&e.Name1?e==null?void 0:e.Name1:"",Name3:e!=null&&e.Name2?e==null?void 0:e.Name2:"",Name4:e!=null&&e.Name3?e==null?void 0:e.Name3:"",Sort1:e!=null&&e.SearchTerm1?e==null?void 0:e.SearchTerm1:"",Sort2:e!=null&&e.SearchTerm2?e==null?void 0:e.SearchTerm2:"",BuildLong:e!=null&&e.BuildingCode?e==null?void 0:e.BuildingCode:"",RoomNo:e!=null&&e.RoomNumber?e==null?void 0:e.RoomNumber:"",Floor:e!=null&&e.Floor?e==null?void 0:e.Floor:"",COName:e!=null&&e.co?e==null?void 0:e.co:"",StrSuppl1:e!=null&&e.Street1?e==null?void 0:e.Street1:"",StrSuppl2:e!=null&&e.Street2?e==null?void 0:e.Street2:"",Street:e!=null&&e.Street3?e==null?void 0:e.Street3:"",HouseNo:e!=null&&e.HouseNumber?e==null?void 0:e.HouseNumber:"",HouseNo2:e!=null&&e.Supplement?e==null?void 0:e.Supplement:"",StrSuppl3:e!=null&&e.Street4?e==null?void 0:e.Street4:"",Location:e!=null&&e.Street5?e==null?void 0:e.Street5:"",District:e!=null&&e.District?e==null?void 0:e.District:"",HomeCity:e!=null&&e.OtherCity?e==null?void 0:e.OtherCity:"",PostlCod1:e!=null&&e.PostalCode?e==null?void 0:e.PostalCode:"",PostlCod2:e!=null&&e.PostalCode1?e==null?void 0:e.PostalCode1:"",PostlCod3:e!=null&&e.CompanyPostCd?e==null?void 0:e.CompanyPostCd:"",PoBox:e!=null&&e.POBox?e==null?void 0:e.POBox:"",PoBoxCit:e!=null&&e.POBoxCity?e==null?void 0:e.POBoxCity:"",PoBoxReg:(vr=e==null?void 0:e.Region2)!=null&&vr.code?(br=e==null?void 0:e.Region2)==null?void 0:br.code:e!=null&&e.Region2?e==null?void 0:e.Region2:"",PoboxCtry:(Ar=e==null?void 0:e.Country2)!=null&&Ar.code?(Nr=e==null?void 0:e.Country2)==null?void 0:Nr.code:e!=null&&e.Country2?e==null?void 0:e.Country2:"",Country:(Tr=e==null?void 0:e.Country1)!=null&&Tr.code?(Er=e==null?void 0:e.Country1)==null?void 0:Er.code:e!=null&&e.Country1?e==null?void 0:e.Country1:"",TimeZone:(Fr=e==null?void 0:e.TimeZone)!=null&&Fr.code?(Ir=e==null?void 0:e.TimeZone)==null?void 0:Ir.code:e!=null&&e.TimeZone?e==null?void 0:e.TimeZone:"",Taxjurcode:(kr=e==null?void 0:e.TaxJurisdiction)!=null&&kr.code?(zr=e==null?void 0:e.TaxJurisdiction)==null?void 0:zr.code:e!=null&&e.TaxJurisdiction?e==null?void 0:e.TaxJurisdiction:"",Transpzone:(qr=e==null?void 0:e.TransportZone)!=null&&qr.code?(Mr=e==null?void 0:e.TransportZone)==null?void 0:Mr.code:e!=null&&e.TransportZone?e==null?void 0:e.TransportZone:"",Regiogroup:(jr=e==null?void 0:e.StructureGroup)!=null&&jr.code?($r=e==null?void 0:e.StructureGroup)==null?void 0:$r.code:e!=null&&e.StructureGroup?e==null?void 0:e.StructureGroup:"",DontUseS:(Or=e==null?void 0:e.Undeliverable)!=null&&Or.code?(Lr=e==null?void 0:e.Undeliverable)==null?void 0:Lr.code:e!=null&&e.Undeliverable?e==null?void 0:e.Undeliverable:"",DontUseP:(Ur=e==null?void 0:e.Undeliverable1)!=null&&Ur.code?(yr=e==null?void 0:e.Undeliverable1)==null?void 0:yr.code:e!=null&&e.Undeliverable1?e==null?void 0:e.Undeliverable1:"",PoWONo:(e==null?void 0:e.POBoxwoNo)===!0?"X":"",DeliServType:e!=null&&e.DelvryServType?e==null?void 0:e.DelvryServType:"",DeliServNumber:e!=null&&e.DeliveryServiceNo?e==null?void 0:e.DeliveryServiceNo:"",Township:e!=null&&e.Township?e==null?void 0:e.Township:"",Langu:(_r=e==null?void 0:e.Language)!=null&&_r.code?(Wr=e==null?void 0:e.Language)==null?void 0:Wr.code:e!=null&&e.Language?e==null?void 0:e.Language:"",Tel1Numbr:e!=null&&e.Telephone?e==null?void 0:e.Telephone:"",Tel1Ext:e!=null&&e.Extension?e==null?void 0:e.Extension:"",FaxNumber:e!=null&&e.Fax?e==null?void 0:e.Fax:"",MobilePhone:e!=null&&e.MobilePhone?e==null?void 0:e.MobilePhone:"",FaxExtens:e!=null&&e.Extension1?e==null?void 0:e.Extension1:"",EMail:e!=null&&e.EMailAddress?e==null?void 0:e.EMailAddress:"",AdrNotes:e!=null&&e.Notes?e==null?void 0:e.Notes:"",Region:(Vr=e==null?void 0:e.Region1)!=null&&Vr.code?(Hr=e==null?void 0:e.Region1)==null?void 0:Hr.code:e!=null&&e.Region1?e==null?void 0:e.Region1:"",PoBoxLobby:e!=null&&e.PoBoxLobby?e==null?void 0:e.PoBoxLobby:""},BankKeyID:u!=null&&u.BankKeyId?u==null?void 0:u.BankKeyId:"",ReqCreatedBy:c==null?void 0:c.user_id,ReqCreatedOn:n!=null&&n.createdOn?"/Date("+(n==null?void 0:n.createdOn)+")/":t!=null&&t.createdOn?"/Date("+Date.parse(t==null?void 0:t.createdOn)+")/":"",RequestStatus:t!=null&&t.reqStatus?t==null?void 0:t.reqStatus:"",CreationId:(n==null?void 0:n.processDesc)==="Create"?n==null?void 0:n.subject.slice(3):(t==null?void 0:t.requestType)==="Create"?(Zr=t==null?void 0:t.requestId)==null?void 0:Zr.slice(3):"",EditId:(n==null?void 0:n.processDesc)==="Change"?n==null?void 0:n.subject.slice(3):(t==null?void 0:t.requestType)==="Change"?(Yr=t==null?void 0:t.requestId)==null?void 0:Yr.slice(3):"",DeleteId:"",MassCreationId:"",MassEditId:"",MassDeleteId:"",RequestType:(t==null?void 0:t.requestType)==="Create"?"Create":(t==null?void 0:t.requestType)==="Change"?"Change":(n==null?void 0:n.processDesc)==="Create"?"Create":((n==null?void 0:n.processDesc)==="Change","Change"),TaskId:n!=null&&n.taskId?n==null?void 0:n.taskId:"",Remarks:R||"",Action:(t==null?void 0:t.requestType)==="Create"?"I":(t==null?void 0:t.requestType)==="Change"?"U":(n==null?void 0:n.processDesc)==="Create"?"I":((n==null?void 0:n.processDesc)==="Change","U"),Validation:Ce===!0?"X":"",BankCtry:t!=null&&t.bankCtryReg?t==null?void 0:t.bankCtryReg:u!=null&&u.BankCtry?u==null?void 0:u.BankCtry:"",BankKey:t!=null&&t.bankKey?t==null?void 0:t.bankKey:u!=null&&u.BankKey?u==null?void 0:u.BankKey:"",BankName:e!=null&&e.BankName?e==null?void 0:e.BankName:"",BankRegion:(Gr=e==null?void 0:e.Region)!=null&&Gr.code?(Xr=e==null?void 0:e.Region)==null?void 0:Xr.code:e!=null&&e.Region?e==null?void 0:e.Region:"",BankStreet:e!=null&&e.Street?e==null?void 0:e.Street:"",City:e!=null&&e.City?e==null?void 0:e.City:"",BankBranch:e!=null&&e.BankBranch?e==null?void 0:e.BankBranch:"",SwiftCode:e!=null&&e.SWIFTBIC?e==null?void 0:e.SWIFTBIC:"",BankGroup:e!=null&&e.BankGroup?e==null?void 0:e.BankGroup:"",PobkCurac:(e==null?void 0:e.PostbankAcct)===!0?"X":"",BankNo:e!=null&&e.BankNumber?e==null?void 0:e.BankNumber:""};const en=s=>{console.log("valueeeeeeeeeeee",s);const d=h=>{console.log("121212",h),te(Se({keyName:"Region",data:h.body}))},o=h=>{console.log(h,"error in dojax")};k(`/${j}/data/getRegionBasedOnCountry?country=${s}`,"get",d,o)},rn=s=>{const d=h=>{console.log("value",s),te(Se({keyName:"Region1",data:h.body}))},o=h=>{console.log(h,"error in dojax")};k(`/${j}/data/getRegionBasedOnCountry?country=${s}`,"get",d,o)},[mr,tn]=C.useState(0),nn=(s,d)=>{const o=E=>{te(Se({keyName:s,data:E.body})),tn(q=>q+1)},h=E=>{console.log(E)};k(`/${j}/data/${d}`,"get",o,h)},on=()=>{var s,d;(d=(s=bt)==null?void 0:s.bankKey)==null||d.map(o=>{nn(o==null?void 0:o.keyName,o==null?void 0:o.endPoint)})},sn=()=>{var s,d;mr==((d=(s=bt)==null?void 0:s.bankKey)==null?void 0:d.length)?be(!1):be(!0)};C.useEffect(()=>{sn()},[mr]),C.useEffect(()=>{on(),Dt(),Pt()},[]),C.useEffect(()=>{Ut(Vn("BK"))},[]),console.log("taskData",z),console.log("taskRowDetails",n);const cn=()=>{var h,E,q,M,ct,it,lt;m(!0);var s=(h=z==null?void 0:z.body)!=null&&h.id?{id:(E=z==null?void 0:z.body)!=null&&E.id?(q=z==null?void 0:z.body)==null?void 0:q.id:"",bankKey:(M=z==null?void 0:z.body)!=null&&M.bankKey?(ct=z==null?void 0:z.body)==null?void 0:ct.bankKey:"",bankCtry:(it=z==null?void 0:z.body)==null?void 0:it.bankCtry,reqStatus:(lt=z==null?void 0:z.body)==null?void 0:lt.reqStatus,screenName:(n==null?void 0:n.processDesc)==="Create"?"Create":"Change"}:{id:t!=null&&t.reqStatus?t==null?void 0:t.id:"",bankKey:t!=null&&t.bankKey?t==null?void 0:t.bankKey:"",bankCtry:t!=null&&t.bankCtryReg?t==null?void 0:t.bankCtryReg:"",reqStatus:t!=null&&t.reqStatus?t==null?void 0:t.reqStatus:"Approved",screenName:t!=null&&t.requestType?t==null?void 0:t.requestType:"Change"};console.log("payload",s);const d=Y=>{var dt,at,ht,pt,mt;if(Y.statusCode===200){m(!1),en((dt=Y==null?void 0:Y.body)==null?void 0:dt.BankCtry),rn((mt=(pt=(ht=(at=Y==null?void 0:Y.body)==null?void 0:at.viewData)==null?void 0:ht["Address Details"])==null?void 0:pt["Street Address"])==null?void 0:mt.find(ae=>(ae==null?void 0:ae.fieldName)==="Country 1").value),console.log("dataaaaaaa",Y.body);const Pe=Y.body.viewData,jn=Y.body;te(Bn(Pe));const ke=Object.keys(Pe);console.log("categorykeys",ke),!(t!=null&&t.requestType)&&!(n!=null&&n.processDesc)&&!v?ue(ke.slice(0,-1)):ue(ke),console.log("factorsarrayyyyy",I);const $n=["Attachment & Documents"];I.concat($n);const On=ke.map(ae=>({category:ae,data:Pe[ae]}));sr(On),P(jn)}else m(!1),Ht(!0),A(!1),N("Error"),S("Unable to fetch data of Bank Key"),_e("danger"),F("danger"),T(!0),Ae(!0)},o=Y=>{console.log(Y)};k(`/${j}/data/displayBankKey`,"post",d,o,s)};C.useEffect(()=>{cn()},[]);const ur=()=>{Ae(!1)},ln=()=>{Z("/masterDataCockpit/bankKey")},dn=()=>{Ot?(Ve(!1),ir(!1)):(Ve(!1),Z("/masterDataCockpit/bankKey"))},fr=()=>wn(wt,Rt,Zt);console.log("formvalidation",He);const L=()=>{fr()?(ge(d=>d-1),te(vt())):gr()},U=()=>{fr()?(ge(d=>d+1),te(vt())):gr()},Fe=()=>{J(!0),I.push("Attachments & Comments"),X(!1)},an=()=>{gn()},hn=()=>{vn()},Xe=()=>{m(!0),bn(),Q(!1)},Cr=()=>{An(),Q(!1)},Je=()=>{m(!0),Nn()},pn=()=>{m(!0),Tn()},xr=()=>{En()},Sr=()=>{Fn()},y=()=>{Ae(!0)},H=()=>{Ve(!0)},K=()=>{be(!0);const s=o=>{be(!1),o.statusCode===201?(Lt(!1),N("Create"),console.log("success"),N("Create"),S("All Data has been Validated. Bank Key can be Send for Review"),F("success"),T(!1),A(!0),H(),a(!0),ir(!0),Q(!1)):(N("Error"),A(!1),o.body.message.length>0?S(o.body.message[0]):S(o.body.value),F("danger"),T(!1),a(!0),y()),handleClose()},d=o=>{console.log(o)};k(`/${j}/alter/validateSingleBankKey`,"post",s,d,O)},mn=()=>{(c==null?void 0:c.role)==="MDM Steward"&&((t==null?void 0:t.requestType)==="Create"||(n==null?void 0:n.processDesc)==="Create")?un():(c==null?void 0:c.role)==="MDM Steward"&&((t==null?void 0:t.requestType)==="Change"||(n==null?void 0:n.processDesc)==="Change")?fn():(c==null?void 0:c.role)==="Approver"&&((t==null?void 0:t.requestType)==="Create"||(n==null?void 0:n.processDesc)==="Create")?Cn():(c==null?void 0:c.role)==="Approver"&&((t==null?void 0:t.requestType)==="Change"||(n==null?void 0:n.processDesc)==="Change")&&xn()},un=()=>{m(!0);const s=o=>{o.statusCode===200?(console.log("success"),N("Create"),S(`Bank Key Submitted for Correction with ID NBS${o.body}`),F("success"),T(!1),A(!0),H(),a(!0),m(!1)):(N("Error"),A(!1),S("Failed Submitting Bank Key for Correction"),F("danger"),T(!1),a(!0),y(),m(!1)),ne()},d=o=>{console.log(o)};console.log("remarkssssssssss",R),k(`/${j}/alter/bankKeySendForCorrection`,"post",s,d,O)},fn=()=>{m(!0);const s=o=>{o.statusCode===200?(console.log("success"),N("Create"),S(`Bank Key Submitted for Correction with ID CBS${o.body}`),F("success"),T(!1),A(!0),H(),a(!0),m(!1)):(N("Error"),A(!1),S("Failed Submitting Bank Key for Correction"),F("danger"),T(!1),a(!0),y(),m(!1)),ne()},d=o=>{console.log(o)};console.log("hsdfjgdh",O),k(`/${j}/alter/changeBankKeySendForReview`,"post",s,d,O)},Cn=()=>{m(!0);const s=o=>{o.statusCode===200?(console.log("success"),N("Create"),S(`Bank Key Submitted for Correction with ID NBS${o.body}`),F("success"),T(!1),A(!0),H(),a(!0),m(!1)):(N("Error"),A(!1),S("Failed Submitting Bank Key for Correction"),F("danger"),T(!1),a(!0),y(),m(!1)),ne()},d=o=>{console.log(o)};k(`/${j}/alter/bankKeySendForReview`,"post",s,d,O)},xn=()=>{m(!0);const s=o=>{o.statusCode===200?(console.log("success"),N("Create"),S(`Bank Key Submitted for Correction with ID CBS${o.body}`),F("success"),T(!1),A(!0),H(),a(!0),m(!1)):(N("Error"),A(!1),S("Failed Submitting Bank Key for Correction"),F("danger"),T(!1),a(!0),y(),m(!1)),ne()},d=o=>{console.log(o)};console.log("remarksssaaaa",R),k(`/${j}/alter/changeBankKeySendForCorrection`,"post",s,d,O)},Sn=()=>{m(!0);const s=o=>{if(m(!1),o.statusCode===200){console.log("success"),N("Change"),S(`Bank Key has been submitted for approval CBS${o.body}`),F("success"),T(!1),A(!0),H(),a(!0),m(!1);const h={artifactId:ie,createdBy:c==null?void 0:c.emailId,artifactType:"BankKey",requestId:`CBS${o==null?void 0:o.body}`},E=M=>{console.log("Second API success",M)},q=M=>{console.error("Second API error",M)};k(`/${pe}/documentManagement/updateDocRequestId`,"post",E,q,h),Z("/masterDataCockpit/bankKey")}else N("Change"),A(!1),S("Change Failed"),F("danger"),T(!1),a(!0),y(),m(!1);handleClose()},d=o=>{console.log(o)};k(`/${j}/alter/changeBankKeyApprovalSubmit`,"post",s,d,O)},Qe=()=>{Q(!1),ar(!0)},Be=()=>{Ne(""),Q(!0),ar(!1)},we=I.map(s=>{const d=or.filter(o=>{var h;return((h=o.category)==null?void 0:h.split(" ")[0])==(s==null?void 0:s.split(" ")[0])});if(d.length!=0)return{category:s==null?void 0:s.split(" ")[0],data:d[0].data}}).map((s,d)=>{if(console.log("categoryData",s==null?void 0:s.category),(s==null?void 0:s.category)=="Bank"&&p==0)return[r(x,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(s.data).map(o=>i(x,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ft},children:[r(g,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),r(D,{sx:{width:"100%"},children:r(Ct,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:r(x,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:s.data[o].map(h=>(console.log("fieldDatatttt",h),r(zt,{data:e,length:h.maxLength,label:h.fieldName,value:h.value,visibility:h.visibility,onSave:E=>handleFieldSave(h.fieldName,E),isEditMode:v,type:h.fieldType,field:h})))})})})]},o))},s.category)];if((s==null?void 0:s.category)=="Address"&&p==1)return[r(x,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(s.data).map(o=>i(x,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ft},children:[r(g,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),r(D,{sx:{width:"100%"},children:r(Ct,{children:r(x,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:s.data[o].map(h=>r(zt,{data:e,label:h.fieldName,value:h==null?void 0:h.value,onSave:E=>handleFieldSave(h.fieldName,E),isEditMode:v,type:h.fieldType},h.fieldName))})})})]},o))},s.category)];if((s==null?void 0:s.category)=="Attachments"&&p==2)return[r(V,{children:v?i(V,{children:[r(Dn,{title:"BankKey",useMetaData:!1,artifactId:ie,artifactName:"BankKey"}),i(ze,{sx:{padding:"1rem 1rem 0rem 1rem"},children:[r(x,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:r(g,{variant:"h6",children:r("strong",{children:"Attachments"})})}),!!le.length&&r(xt,{width:"100%",rows:le,columns:pr,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action"}),!le.length&&r(g,{variant:"body2",children:"No Attachments Found"}),r("br",{}),r(g,{variant:"h6",children:"Comments"}),!!de.length&&r(At,{sx:{[`& .${Nt.root}:before`]:{flex:0,padding:0}},children:de.map(o=>i(Tt,{children:[i(Et,{children:[r(Ft,{children:r(St,{sx:{color:"#757575"}})}),r(It,{})]}),r(kt,{sx:{py:"12px",px:2},children:r(ze,{elevation:0,sx:{border:1,borderColor:"#C4C4C4",borderRadius:"8px",width:"650px"},children:r(D,{sx:{padding:"1rem"},children:i(ee,{spacing:1,children:[r(x,{sx:{display:"flex",justifyContent:"space-between"},children:r(g,{sx:{textAlign:"right",color:" #757575",fontWeight:"500",fontSize:"12px"},children:Ke(o.createdAt).format("DD MMM YYYY")})}),r(g,{sx:{fontSize:"12px",color:" #757575",fontWeight:"500"},children:o.user}),r(g,{sx:{fontSize:"12px",color:"#1D1D1D",fontWeight:"600"},children:o.comment})]})})})})]}))}),!de.length&&r(g,{variant:"body2",children:"No Comments Found"}),r("br",{})]})]}):i(ze,{sx:{padding:"1rem 1rem 0rem 1rem"},children:[r(x,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:r(g,{variant:"h6",children:r("strong",{children:"Attachments"})})}),!!le.length&&r(xt,{width:"100%",rows:le,columns:pr,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action"}),!le.length&&r(g,{variant:"body2",children:"No Attachments Found"}),r("br",{}),r(g,{variant:"h6",children:"Comments"}),!!de.length&&r(At,{sx:{[`& .${Nt.root}:before`]:{flex:0,padding:0}},children:de.map(o=>i(Tt,{children:[i(Et,{children:[r(Ft,{children:r(St,{sx:{color:"#757575"}})}),r(It,{})]}),r(kt,{sx:{py:"12px",px:2},children:r(ze,{elevation:0,sx:{border:1,borderColor:"#C4C4C4",borderRadius:"8px",width:"650px"},children:r(D,{sx:{padding:"1rem"},children:i(ee,{spacing:1,children:[r(x,{sx:{display:"flex",justifyContent:"space-between"},children:r(g,{sx:{textAlign:"right",color:" #757575",fontWeight:"500",fontSize:"12px"},children:Ke(o.createdAt).format("DD MMM YYYY")})}),r(g,{sx:{fontSize:"12px",color:" #757575",fontWeight:"500"},children:o.user}),r(g,{sx:{fontSize:"12px",color:"#1D1D1D",fontWeight:"600"},children:o.comment})]})})})})]}))}),!de.length&&r(g,{variant:"body2",children:"No Comments Found"}),r("br",{})]})})]});console.log("tabcontents",we);const gn=()=>{m(!0);const s=o=>{o.statusCode===200?(console.log("success"),N("Create"),S(`Bank Key Submitted for Approval with ID CBS${o.body}`),F("success"),T(!1),A(!0),H(),a(!0),m(!1),Z("/masterDataCockpit/bankKey")):(N("Approve"),A(!1),S("Failed Submitting Bank Key"),F("danger"),T(!1),a(!0),y(),m(!1)),handleClose()},d=o=>{console.log(o)};k(`/${j}/alter/changeBankKeyApprovalSubmit`,"post",s,d,O)},vn=()=>{m(!0);const s=o=>{o.statusCode===200?(console.log("success"),N("Create"),S(`Bank Key Submitted for Approval with ID NBS${o.body}`),F("success"),T(!1),A(!0),H(),a(!0),m(!1),Z("/masterDataCockpit/bankKey")):(N("Error"),A(!1),S("Failed Submitting Bank Key"),F("danger"),T(!1),a(!0),y(),m(!1)),handleClose()},d=o=>{console.log(o)};k(`/${j}/alter/bankKeyApprovalSubmit`,"post",s,d,O)},bn=()=>{const s=o=>{if(o.statusCode===200){console.log("success"),N("Create"),S(`Bank Key Submitted For Review with ID CBS${o.body} `),F("success"),T(!1),A(!0),H(),a(!0),J(!1),X(!0),m(!1);const h={artifactId:ie,createdBy:c==null?void 0:c.emailId,artifactType:"BankKey",requestId:`CBS${o==null?void 0:o.body}`},E=M=>{console.log("Second API success",M)},q=M=>{console.error("Second API error",M)};k(`/${pe}/documentManagement/updateDocRequestId`,"post",E,q,h),Z("/masterDataCockpit/bankKey")}else N("Error"),A(!1),S("Failed Submitting Bank Key"),F("danger"),T(!1),a(!0),y(),m(!1),Ze(!1);handleClose()},d=o=>{console.log(o)};k(`/${j}/alter/changeBankKeySubmitForReview`,"post",s,d,O)},An=()=>{m(!0);const s=o=>{if(o.statusCode===200){console.log("success"),N("Create"),S(`Bank Key Submitted for Review with ID NBS${o.body} `),F("success"),T(!1),A(!0),H(),a(!0),m(!1);const h={artifactId:ie,createdBy:c==null?void 0:c.emailId,artifactType:"BankKey",requestId:`NBS${o==null?void 0:o.body}`},E=M=>{console.log("Second API success",M)},q=M=>{console.error("Second API error",M)};k(`/${pe}/documentManagement/updateDocRequestId`,"post",E,q,h),Z("/masterDataCockpit/bankKey")}else N("Error"),A(!1),S("Failed Saving the Data"),F("danger"),T(!1),a(!0),y(),m(!1),Q(!0);handleClose()},d=o=>{console.log(o)};k(`/${j}/alter/bankKeySubmitForReview`,"post",s,d,O)},Nn=()=>{const s=o=>{if(m(!1),o.statusCode===200){console.log("success"),N("Create"),S(`Bank Key Saved As Draft with ID CBS${o.body} `),F("success"),T(!1),A(!0),H(),a(!0),m(!1);const h={artifactId:ie,createdBy:c==null?void 0:c.emailId,artifactType:"BankKey",requestId:`CBS${o==null?void 0:o.body}`},E=M=>{console.log("Second API success",M)},q=M=>{console.error("Second API error",M)};k(`/${pe}/documentManagement/updateDocRequestId`,"post",E,q,h),Z("/masterDataCockpit/bankKey")}else N("Error"),A(!1),S("Failed Saving Bank Key"),F("danger"),T(!1),a(!0),y(),m(!1);handleClose()},d=o=>{console.log(o)};k(`/${j}/alter/changeBankKeyAsDraft`,"post",s,d,O)},Tn=()=>{const s=o=>{if(m(!1),o.statusCode===200){console.log("success"),N("Create"),S(`Bank Key Saved As Draft with ID NBS${o.body} `),F("success"),T(!1),A(!0),H(),a(!0),m(!1);const h={artifactId:ie,createdBy:c==null?void 0:c.emailId,artifactType:"BankKey",requestId:`NBS${o==null?void 0:o.body}`},E=M=>{console.log("Second API success",M)},q=M=>{console.error("Second API error",M)};k(`/${pe}/documentManagement/updateDocRequestId`,"post",E,q,h),Z("/masterDataCockpit/bankKey")}else N("Error"),A(!1),S("Failed Saving Bank Key"),F("danger"),T(!1),a(!0),y(),m(!1);handleClose()},d=o=>{console.log(o)};k(`/${j}/alter/bankKeyAsDraft`,"post",s,d,O)},En=()=>{m(!0);const s=o=>{o.statusCode===201?(console.log("success"),N("Create"),S(`${o.message}`),F("success"),T(!1),A(!0),H(),a(!0),m(!1),Z("/masterDataCockpit/bankKey")):(N("Error"),A(!1),S("Failed Approving Bank Key"),F("danger"),T(!1),a(!0),y(),m(!1)),handleClose()},d=o=>{console.log(o)};k(`/${j}/alter/changeBankKeyApproved`,"post",s,d,O)},Fn=()=>{m(!0);const s=o=>{o.statusCode===201?(console.log("success"),N("Create"),S(`${o.message}`),F("success"),T(!1),A(!0),H(),a(!0),m(!1),Z("/masterDataCockpit/bankKey")):(N("Error"),A(!1),S("Failed Approving the Bank Key"),F("danger"),T(!1),a(!0),y(),m(!1)),handleClose()},d=o=>{console.log(o)};k(`/${j}/alter/createBankKeyApproved`,"post",s,d,O)},Ie=()=>{Q(!1),lr(!0)},ne=()=>{Ne(""),Q(!0),lr(!1)},oe=()=>{Q(!1),Ze(!0)},Re=()=>{xe(!1),Q(!0),Ze(!1)},In=()=>{Ae(!1)},kn=()=>{(c==null?void 0:c.role)==="Finance"&&((t==null?void 0:t.requestType)==="Create"||(n==null?void 0:n.processDesc)==="Create")&&v?R.length<=0?xe(!0):(xe(!1),Cr()):(c==null?void 0:c.role)==="MDM Steward"&&((t==null?void 0:t.requestType)==="Create"||(n==null?void 0:n.processDesc)==="Create")&&!v?hn():(c==null?void 0:c.role)==="Approver"&&((t==null?void 0:t.requestType)==="Create"||(n==null?void 0:n.processDesc)==="Create")&&!v?Sr():(c==null?void 0:c.role)==="Finance"&&!(t!=null&&t.requestType)&&v?R.length<=0?xe(!0):(xe(!1),Xe()):(c==null?void 0:c.role)==="Finance"&&((t==null?void 0:t.requestType)==="Change"||(n==null?void 0:n.processDesc)==="Change")&&v?Xe():(c==null?void 0:c.role)==="MDM Steward"&&((t==null?void 0:t.requestType)==="Change"||(n==null?void 0:n.processDesc)==="Change")&&!v?an():(c==null?void 0:c.role)==="Approver"&&((t==null?void 0:t.requestType)==="Change"||(n==null?void 0:n.processDesc)==="Change")&&!v&&xr()},De=(s,d)=>{const o=s.target.value;if(o.length>0&&o[0]===" ")Ne(o.trimStart());else{let h=o.toUpperCase();Ne(h)}},gr=()=>{dr(!0)},zn=()=>{dr(!1)},qn=s=>{hr(s)},Mn=()=>{hr(!0)};return console.log(t==null?void 0:t.bankKey,"bankKeyRowData?.bankKey"),r(V,{children:b===!0?r(Hn,{}):i("div",{style:{backgroundColor:"#FAFCFF"},children:[r(Zn,{apiError:Vt,dialogState:jt,openReusableDialog:y,closeReusableDialog:ur,dialogTitle:ye,dialogMessage:ce,handleDialogConfirm:ur,dialogOkText:"OK",handleExtraButton:ln,dialogSeverity:re,showCancelButton:!0,handleDialogReject:In}),ve&&r(gt,{openSnackBar:$t,alertMsg:ce,handleSnackBarClose:dn}),He.length!=0&&r(gt,{openSnackBar:Yt,alertMsg:"Please fill the following Field: "+He.join(", "),handleSnackBarClose:zn}),i(qe,{open:Wt,onClose:ne,hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},children:[i(Me,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[r(g,{variant:"h6",children:"REMARKS"}),r(je,{sx:{width:"max-content"},onClick:ne,children:r(er,{})})]}),r($e,{sx:{padding:".5rem 1rem"},children:r(ee,{children:i(D,{sx:{minWidth:400},children:[r(he,{sx:{height:"auto"},fullWidth:!0,children:r(me,{sx:{backgroundColor:"#F5F5F5"},onChange:De,multiline:!0,value:R,placeholder:"ENTER REMARKS",inputProps:{maxLength:254}})}),Ye&&r(x,{children:r(g,{style:{color:"red"},children:"Please Enter Remarks"})})]})})}),i(Oe,{sx:{display:"flex",justifyContent:"end"},children:[r(l,{sx:{width:"max-content",textTransform:"capitalize"},onClick:ne,children:"Cancel"}),r(l,{className:"button_primary--normal",type:"save",onClick:mn,variant:"contained",children:"Submit"})]})]}),r(Yn,{sx:{color:"#fff",zIndex:s=>s.zIndex.drawer+1},open:w,children:r(Gn,{color:"inherit"})}),i(x,{container:!0,sx:Xn,children:[i(x,{item:!0,md:12,style:{padding:"16px",display:"flex"},children:[i(x,{md:9,sx:{display:"flex"},children:[r(x,{children:r(je,{color:"primary","aria-label":"upload picture",component:"label",sx:Jn,children:r(Rn,{sx:{fontSize:"25px",color:"#000000"},onClick:()=>{Z("/masterDataCockpit/bankKey")}})})}),i(x,{children:[v?i(x,{item:!0,md:12,children:[r(g,{variant:"h3",children:r("strong",{children:"Change Bank Key: "})}),r(g,{variant:"body2",color:"#777",children:"This view edits the details of the Bank Key"})]}):"",B?i(x,{item:!0,md:12,children:[r(g,{variant:"h3",children:r("strong",{children:"Display Bank Key "})}),r(g,{variant:"body2",color:"#777",children:"This view displays the details of the Bank Key"})]}):""]})]}),i(x,{md:3,sx:{display:"flex",justifyContent:"flex-end"},gap:2,children:[t!=null&&t.requestId||n!=null&&n.processDesc?r(x,{children:r(l,{variant:"outlined",size:"small",sx:$,onClick:Mn,title:"Change Log",children:r(eo,{sx:{padding:"2px"},fontSize:"small"})})}):"",Jt&&r(ro,{open:!0,closeModal:qn,requestId:t!=null&&t.requestId?t==null?void 0:t.requestId:n==null?void 0:n.subject,requestType:t!=null&&t.requestType?t==null?void 0:t.requestType:(Jr=z==null?void 0:z.body)==null?void 0:Jr.processDesc,pageName:"bankKey",controllingArea:t!=null&&t.bankCtryReg?t==null?void 0:t.bankCtryReg:u==null?void 0:u.BankCtry,centerName:t!=null&&t.bankKey?t==null?void 0:t.bankKey:u==null?void 0:u.BankKey}),Le(Ee,"Bank Key","ChangeBK")&&((c==null?void 0:c.role)==="Super User"&&(t!=null&&t.requestType)&&((Qr=n==null?void 0:n.itmStatus)==null?void 0:Qr.toUpperCase())!=="OPEN"&&B?r(x,{gap:1,sx:{display:"flex"},children:r(x,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:r(V,{children:r(x,{item:!0,children:i(l,{variant:"outlined",size:"small",sx:$,onClick:Fe,children:["Fill Details",r(Ue,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(c==null?void 0:c.role)==="Finance"&&(t!=null&&t.requestType)&&B?r(x,{gap:1,sx:{display:"flex"},children:r(x,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:r(V,{children:r(x,{item:!0,children:i(l,{variant:"outlined",size:"small",sx:$,onClick:Fe,children:["Fill Details",r(Ue,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(c==null?void 0:c.role)==="Super User"&&!(t!=null&&t.requestType)&&B?r(x,{gap:1,sx:{display:"flex"},children:r(x,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:r(V,{children:r(x,{item:!0,children:i(l,{variant:"outlined",size:"small",sx:$,onClick:Fe,children:["Change",r(Ue,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(c==null?void 0:c.role)==="Finance"&&!(t!=null&&t.requestType)&&B?r(x,{gap:1,sx:{display:"flex"},children:r(x,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:r(V,{children:r(x,{item:!0,children:i(l,{variant:"outlined",size:"small",sx:$,onClick:Fe,children:["Change",r(Ue,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):"")]})]}),r(x,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:i(D,{width:"70%",sx:{marginLeft:"40px"},children:[r(x,{item:!0,sx:{paddingTop:"2px !important"},children:i(ee,{flexDirection:"row",children:[r("div",{style:{width:"20%"},children:r(g,{variant:"body2",color:"#777",children:"Bank Country"})}),i(g,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[":"," ",t!=null&&t.BankCtry?t==null?void 0:t.BankCtry:u!=null&&u.BankCtry?u==null?void 0:u.BankCtry:""]})]})}),r(x,{item:!0,sx:{paddingTop:"2px !important"},children:i(ee,{flexDirection:"row",children:[r("div",{style:{width:"20%"},children:r(g,{variant:"body2",color:"#777",children:"Bank Key"})}),i(g,{variant:"body2",fontWeight:"bold",children:[":"," ",t!=null&&t.bankKey?t==null?void 0:t.bankKey:u!=null&&u.BankKey?u==null?void 0:u.BankKey:""]})]})})]})}),i(x,{container:!0,style:{marginLeft:25},children:[r(to,{activeStep:p,sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},"aria-label":"mui tabs example",children:I.map((s,d)=>r(no,{children:r(oo,{sx:{fontWeight:"700"},children:s})},s))}),we&&((Br=we[p])==null?void 0:Br.map((s,d)=>r(D,{sx:{mb:2,width:"100%"},children:r(g,{variant:"body2",children:s})},d)))]})]}),i(x,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:[Le(Ee,"Bank Key","ChangeBK")&&(!(t!=null&&t.requestType)&&((wr=n==null?void 0:n.itmStatus)==null?void 0:wr.toUpperCase())!=="OPEN"&&!v?r(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(W,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:L,disabled:p===0,children:"Back"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:U,disabled:p===I.length-1,children:"Next"})]})}):""),Le(Ee,"Bank Key","ChangeBK")&&((c==null?void 0:c.role)==="Super User"&&!(t!=null&&t.requestType)&&((Rr=n==null?void 0:n.itmStatus)==null?void 0:Rr.toUpperCase())!=="OPEN"&&v?r(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(W,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[r(l,{variant:"contained",size:"small",sx:{button_Outlined:$,mr:1},onClick:Je,children:"Save As Draft"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:L,disabled:p===0,children:"Back"}),p===I.length-1?i(V,{children:[r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:K,disabled:p===0,children:"Validate"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:Qe,disabled:Te,children:"Submit For Review"})]}):r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:U,disabled:p===I.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Finance"&&!(t!=null&&t.requestType)&&((Dr=n==null?void 0:n.itmStatus)==null?void 0:Dr.toUpperCase())!=="OPEN"&&v?r(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(W,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[r(l,{variant:"contained",size:"small",sx:{button_Outlined:$,mr:1},onClick:Je,children:"Save As Draft"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:L,disabled:p===0,children:"Back"}),p===I.length-1?i(V,{children:[r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:K,children:"Validate"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:oe,disabled:Te,children:"Submit For Review"})]}):r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:U,disabled:p===I.length-1,children:"Next"})]})}):""),Le(Ee,"Bank Key","ChangeBK")&&((c==null?void 0:c.role)==="Super User"&&(t==null?void 0:t.requestType)==="Create"&&!v?r(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(W,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:K,children:"Validate"}),r(l,{variant:"contained",size:"small",sx:{button_Outlined:$,mr:1},onClick:Sr,disabled:Ce,children:"Approve"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:Qe,children:"Submit For Approval"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:L,disabled:p===0,children:"Back"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:U,disabled:p===I.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Super User"&&(t==null?void 0:t.requestType)==="Change"&&!v?r(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(W,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:K,children:"Validate"}),r(l,{variant:"contained",size:"small",sx:{button_Outlined:$,mr:1},onClick:xr,disabled:Ce,children:"Approve"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:Qe,children:"Submit For Approval"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:L,disabled:p===0,children:"Back"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:U,disabled:p===I.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="MDM Steward"&&((t==null?void 0:t.requestType)==="Create"||(n==null?void 0:n.processDesc)==="Create")&&((Pr=n==null?void 0:n.itmStatus)==null?void 0:Pr.toUpperCase())!=="OPEN"&&!v?r(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(W,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[r(l,{variant:"outlined",size:"small",sx:{button_Outlined:$,mr:1},onClick:Ie,children:"Correction"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:oe,children:"Submit For Approval"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:L,disabled:p===0,children:"Back"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:U,disabled:p===I.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Finance"&&((t==null?void 0:t.requestType)==="Create"||(n==null?void 0:n.processDesc)==="Create")&&((Kr=n==null?void 0:n.itmStatus)==null?void 0:Kr.toUpperCase())!=="OPEN"&&!v?r(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(W,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:L,disabled:p===0,children:"Back"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:U,disabled:p===I.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Finance"&&((t==null?void 0:t.requestType)==="Change"||(n==null?void 0:n.processDesc)==="Change")&&((et=n==null?void 0:n.itmStatus)==null?void 0:et.toUpperCase())!=="OPEN"&&!v?r(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(W,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:L,disabled:p===0,children:"Back"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:U,disabled:p===I.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="MDM Steward"&&((t==null?void 0:t.requestType)==="Change"||(n==null?void 0:n.processDesc)==="Change")&&((rt=n==null?void 0:n.itmStatus)==null?void 0:rt.toUpperCase())!=="OPEN"&&!v?r(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(W,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[r(l,{variant:"outlined",size:"small",sx:{button_Outlined:$,mr:1},onClick:Ie,children:"Correction"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:oe,children:"Submit For Approval"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:L,disabled:p===0,children:"Back"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:U,disabled:p===I.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Approver"&&((t==null?void 0:t.requestType)==="Create"||(n==null?void 0:n.processDesc)==="Create")&&((tt=n==null?void 0:n.itmStatus)==null?void 0:tt.toUpperCase())!=="OPEN"&&!v?r(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(W,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[r(l,{variant:"outlined",size:"small",sx:{button_Outlined:$,mr:1},onClick:Ie,children:"Correction"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:K,children:"Validate"}),r(l,{variant:"contained",size:"small",sx:{button_Outlined:$,mr:1},onClick:oe,disabled:Ce,children:"Approve"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:L,disabled:p===0,children:"Back"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:U,disabled:p===I.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Approver"&&((t==null?void 0:t.requestType)==="Change"||(n==null?void 0:n.processDesc)==="Change")&&((nt=n==null?void 0:n.itmStatus)==null?void 0:nt.toUpperCase())!=="OPEN"&&!v?r(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(W,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[r(l,{variant:"outlined",size:"small",sx:{button_Outlined:$,mr:1},onClick:Ie,children:"Correction"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:K,children:"Validate"}),r(l,{variant:"contained",size:"small",sx:{button_Outlined:$,mr:1},onClick:oe,disabled:Ce,children:"Approve"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:L,disabled:p===0,children:"Back"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:U,disabled:p===I.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Super User"&&(t==null?void 0:t.requestType)==="Create"&&v?r(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(W,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:L,disabled:p===0,children:"Back"}),p===I.length-1?r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:Cr,children:"Submit For Review"}):r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:U,disabled:p===I.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Super User"&&(t==null?void 0:t.requestType)==="Change"&&v?r(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(W,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:L,disabled:p===0,children:"Back"}),p===I.length-1?r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:Xe,children:"Submit For Review"}):r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:U,disabled:p===I.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Finance"&&((t==null?void 0:t.requestType)==="Create"||(n==null?void 0:n.processDesc)==="Create")&&((ot=n==null?void 0:n.itmStatus)==null?void 0:ot.toUpperCase())!=="OPEN"&&v?r(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(W,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[r(l,{variant:"outlined",size:"small",sx:{button_Outlined:$,mr:1},onClick:pn,children:"Save As Draft"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:L,disabled:p===0,children:"Back"}),p===I.length-1?i(V,{children:[r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:K,children:"Validate"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:oe,disabled:Te,children:"Submit For Review"})]}):r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:U,disabled:p===I.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Finance"&&((t==null?void 0:t.requestType)==="Change"||(n==null?void 0:n.processDesc)==="Change")&&((st=n==null?void 0:n.itmStatus)==null?void 0:st.toUpperCase())!=="OPEN"&&v?r(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(W,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[r(l,{variant:"outlined",size:"small",sx:{button_Outlined:$,mr:1},onClick:Je,children:"Save As Draft"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:L,disabled:p===0,children:"Back"}),p===I.length-1?i(V,{children:[r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:K,children:"Validate"}),r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:oe,disabled:Te,children:"Submit For Review"})]}):r(l,{variant:"contained",size:"small",sx:{...f,mr:1},onClick:U,disabled:p===I.length-1,children:"Next"})]})}):"")]}),i(qe,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:Gt,onClose:Be,children:[i(Me,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[r(g,{variant:"h6",children:"REMARKS"}),r(je,{sx:{width:"max-content"},onClick:Be,children:r(er,{})})]}),r($e,{sx:{padding:".5rem 1rem"},children:r(ee,{children:i(D,{sx:{minWidth:400},children:[r(he,{sx:{height:"auto"},fullWidth:!0,children:r(me,{sx:{backgroundColor:"#F5F5F5"},value:R,onChange:De,multiline:!0,placeholder:"ENTER REMARKS",inputProps:{maxLength:254}})}),Ye&&r(x,{children:r(g,{style:{color:"red"},children:"Please Enter Remarks"})})]})})}),i(Oe,{sx:{display:"flex",justifyContent:"end"},children:[r(l,{sx:{width:"max-content",textTransform:"capitalize"},onClick:Be,children:"Cancel"}),r(l,{className:"button_primary--normal",type:"save",onClick:Sn,variant:"contained",children:"Submit"})]})]}),i(qe,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:Xt,onClose:Re,children:[i(Me,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[r(g,{variant:"h6",children:"REMARKS"}),r(je,{sx:{width:"max-content"},onClick:Re,children:r(er,{})})]}),r($e,{sx:{padding:".5rem 1rem"},children:r(ee,{children:i(D,{sx:{minWidth:400},children:[r(he,{sx:{height:"auto"},fullWidth:!0,children:r(me,{sx:{backgroundColor:"#F5F5F5"},value:R,onChange:De,multiline:!0,placeholder:"ENTER REMARKS",inputProps:{maxLength:254}})}),Ye&&r(x,{children:r(g,{style:{color:"red"},children:"Please Enter Remarks"})})]})})}),i(Oe,{sx:{display:"flex",justifyContent:"end"},children:[r(l,{sx:{width:"max-content",textTransform:"capitalize"},onClick:Re,children:"Cancel"}),r(l,{className:"button_primary--normal",type:"save",onClick:kn,variant:"contained",children:"Submit"})]})]}),i(qe,{open:se,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[r(Me,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:r(g,{variant:"h6",children:"New Bank Key"})}),r($e,{sx:{padding:".5rem 1rem"},children:i(x,{container:!0,spacing:1,children:[i(x,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[i(g,{children:["Bank Key",r("span",{style:{color:"red"},children:"*"})]}),r(he,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px"},children:r(me,{sx:{fontSize:"12px !important",height:"31px"},fullWidth:!0,size:"small",placeholder:"Enter Bank Key Name",required:!0})})]}),i(x,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[i(g,{children:["Valid From",r("span",{style:{color:"red"},children:"*"})]}),r(he,{fullWidth:!0,sx:{margin:".5em 0px"},children:r(rr,{dateAdapter:tr,children:r(nr,{slotProps:{textField:{size:"small"}}})})})]}),i(x,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[i(g,{children:["Valid To",r("span",{style:{color:"red"},children:"*"})]}),r(he,{fullWidth:!0,sx:{margin:".5em 0px"},children:r(rr,{dateAdapter:tr,children:r(nr,{slotProps:{textField:{size:"small"}}})})})]})]})}),i(Oe,{sx:{display:"flex",justifyContent:"end"},children:[r(l,{sx:{width:"max-content",textTransform:"capitalize"},children:"Cancel"}),r(l,{className:"button_primary--normal",type:"save",variant:"contained",children:"Proceed"})]})]})]})})};export{Lo as default};
