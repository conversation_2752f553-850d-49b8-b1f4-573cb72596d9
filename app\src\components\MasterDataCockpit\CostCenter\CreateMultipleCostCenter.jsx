import {
  Backdrop,
  BottomNavigation,
  Box,
  Button,
  Card,
  Checkbox,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  Paper,
  Stack,
  Tab,
  Tabs,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import {
  iconButton_SpacingSmall,
  outermostContainer,
  button_Primary,
  outerContainer_Information,
  outermostContainer_Information,
} from "../../common/commonStyles";
import CloseIcon from "@mui/icons-material/Close";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import ReusableTable from "../../common/ReusableTable";
import { useNavigate, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { doAjax } from "../../Common/fetchService";
import {
  destination_CostCenter,
  destination_DocumentManagement,
} from "../../../destinationVariables";
import ReusableSnackBar from "../../Common/ReusableSnackBar";
import moment from "moment/moment";
import ReusableDialog from "../../Common/ReusableDialog";
import LoadingComponent from "../../Common/LoadingComponent";
import { setDropDown } from "../../../app/dropDownDataSlice";
import lookup from "../../../data/lookup.json";
import ReusableAttachementAndComments from "../../Common/ReusableAttachmentAndComments/ReusableAttachementAndComments";
import { idGenerator } from "../../../functions";
import AttachFileOutlinedIcon from "@mui/icons-material/AttachFileOutlined";

const CreateMultipleCostCenter = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [blurLoading, setBlurLoading] = useState(false);
  const [value, setValue] = useState("1");
  const [selectedRows, setSelectedRows] = useState([]);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [messageDialogExtra, setMessageDialogExtra] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState(false);
  const [messageDialogSeverity, setMessageDialogSeverity] = useState(false);
  const [messageDialogOK, setMessageDialogOK] = useState(true);
  const [successMsg, setsuccessMsg] = useState(false);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [submitForReviewDisabled, setSubmitForReviewDisabled] = useState(true);
  const [testRun, setTestRun] = useState(false);
  const [validateFlag, setValidateFlag] = useState(false);
  const [remarks, setRemarks] = useState("");
  const [openDialog, setOpenDialog] = useState(false);
  const [openCorrectionDialog, setOpenCorrectionDialog] = useState(false);
  const [selectedPayloadData, setSelectedPayloadData] = useState([]);
  const [costValidationError, setCostValidationErrors] = useState([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [directMatchedProfitCenters, setDirectMatchedProfitCenters] = useState(
    []
  );
  const [ccNumber, setCcNumber] = useState("");
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [newPayload, setNewPayload] = useState({});
  let multipleCostData = useSelector(
    (state) => state.costCenter.MultipleCostCenterData
  );
  const location = useLocation();
  let massHandleType = useSelector((state) => state.costCenter.handleMassMode);
  const appSettings = useSelector((state) => state.appSettings);
  console.log("multipleCostData", multipleCostData);
  let multipleControllingArea = useSelector(
    (state) => state.costCenter.controllingArea
  );
  let userData = useSelector((state) => state.userManagement.userData);
  // const row = multipleCostData?.tableData.map((item) => {
  //   console.log("type", typeof item, item);
  //   if (typeof item === "object") {
  //     console.log("item", item);
  //     let arrayObj = Object.entries(item);
  //     console.log("aray", arrayObj, arrayObj.length);
  //     let tempArray = [];
  //     for (let index = 0; index < arrayObj.length; index++) {
  //       console.log("binay", index, arrayObj[index], typeof arrayObj[index][1]);
  //       if (
  //         typeof arrayObj[index][1] === "object" &&
  //         arrayObj[index][1] != null
  //       ) {
  //         console.log("binay p", arrayObj[index][1]);
  //         tempArray.push(Object.entries(arrayObj[index][1]));
  //       }
  //     }
  //     return tempArray;
  //   } else {
  //     return null;
  //   }
  // });

  const handleSnackBarOpen = () => {
    setopenSnackbar(true);
  };
  const handleOpenDialog = () => {
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };
  const getValueForFieldName = (data, fieldName) => {
    // console.log("getvalueforfieldname", data, fieldName);
    const field = data?.find((field) => field?.fieldName === fieldName);
    return field ? field.value : "";
  };

  // Loader and lookup for independent apis start
  const [apiCount, setApiCount] = useState(0);
  const fetchDynamicApiData = (keyName, endPoint) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: keyName, data: data.body }));
      // setIsLoading(false);
      setApiCount((prev) => prev + 1);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/${endPoint}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getAllLookups = () => {
    lookup?.costCenter?.map((item) => {
      fetchDynamicApiData(item?.keyName, item?.endPoint);
    });
  };
  const loaderCount = () => {
    if (apiCount == lookup?.costCenter?.length) {
      setIsLoading(false);
      console.log("apiCount", apiCount);
    } else {
      setIsLoading(true);
    }
  };

  useEffect(() => {
    setCcNumber(idGenerator("CC"));
  }, []);
  useEffect(() => {
    loaderCount();
  }, [apiCount]);

  // Loader and lookup for independent apis end

  useEffect(() => {
    getAllLookups();
  }, []);

  var payloadmapping = Object.keys(newPayload)?.map((y) => {
    console.log("payloadmapping", y);
    return {
      CostCenterHeaderID: "",
      ControllingArea: y ?? "", //tzus
      Testrun: testRun,
      Action: massHandleType === "Create" ? "I" : "U",
      ReqCreatedBy: userData?.user_id ? userData?.user_id : "",
      ReqCreatedOn: userData?.createdOn
        ? "/Date(" + userData?.createdOn + ")/"
        : "",
      RequestStatus: "",
      CreationId: "",
      EditId: "",
      DeleteId: "",
      MassCreationId: "",
      MassEditId: "",
      Remarks: remarks,
      MassDeleteId: "",
      RequestType: massHandleType === "Create" ? "Mass Create" : "Mass Change",
      MassRequestStatus: "",
      Toitem: newPayload[y].map((x) => {
        console.log("x", x);

        return {
          CostCenterID: "",
          Costcenter: x?.costCenter ? x?.costCenter : "",
          ValidFrom: x?.validFrom ? x?.validFrom : "",
          ValidTo: x?.validTo ? x?.validTo : "",
          PersonInCharge: getValueForFieldName(
            x?.["viewData"]?.["Basic Data"]?.["Basic Data"],
            "Person Responsible"
          ),
          CostcenterType: getValueForFieldName(
            x?.["viewData"]?.["Basic Data"]?.["Basic Data"],
            "Cost Center Category"
          ),
          CostctrHierGrp: getValueForFieldName(
            x?.["viewData"]?.["Basic Data"]?.["Basic Data"],
            "Hierarchy Area"
          ),
          BusArea: getValueForFieldName(
            x?.["viewData"]?.["Basic Data"]?.["Basic Data"],
            "Business Area"
          ),
          CompCode: getValueForFieldName(
            x?.["viewData"]?.["Basic Data"]?.["Basic Data"],
            "Company Code"
          ),
          Currency: getValueForFieldName(
            x?.["viewData"]?.["Basic Data"]?.["Basic Data"],
            "Currency"
          ),
          ProfitCtr: getValueForFieldName(
            x?.["viewData"]?.["Basic Data"]?.["Basic Data"],
            "Profit Center"
          ),
          Name: getValueForFieldName(
            x?.["viewData"]?.["Basic Data"]?.["Names"],
            "Name"
          ),
          Descript: getValueForFieldName(
            x?.["viewData"]?.["Basic Data"]?.["Names"],
            "Description"
          ),
          PersonInChargeUser: getValueForFieldName(
            x?.["viewData"]?.["Basic Data"]?.["Basic Data"],
            "User Responsible"
          ),
          RecordQuantity:
            getValueForFieldName(
              x?.["viewData"]?.["Control"]?.["Control"],
              "Record Quantity"
            ) === true
              ? "X"
              : "",
          LockIndActualPrimaryCosts:
            getValueForFieldName(
              x?.["viewData"]?.["Control"]?.["Control"],
              "Actual Primary Costs"
            ) === true
              ? "X"
              : "",
          LockIndPlanPrimaryCosts:
            getValueForFieldName(
              x?.["viewData"]?.["Control"]?.["Control"],
              "Plan Primary Costs"
            ) === true
              ? "X"
              : "",
          LockIndActSecondaryCosts:
            getValueForFieldName(
              x?.["viewData"]?.["Control"]?.["Control"],
              "Act. secondary Costs"
            ) === true
              ? "X"
              : "",
          LockIndPlanSecondaryCosts:
            getValueForFieldName(
              x?.["viewData"]?.["Control"]?.["Control"],
              "Plan Secondary Costs"
            ) === true
              ? "X"
              : "",
          LockIndActualRevenues:
            getValueForFieldName(
              x?.["viewData"]?.["Control"]?.["Control"],
              "Actual Revenue"
            ) === true
              ? "X"
              : "",
          LockIndPlanRevenues:
            getValueForFieldName(
              x?.["viewData"]?.["Control"]?.["Control"],
              "Plan Revenue"
            ) === true
              ? "X"
              : "",
          LockIndCommitmentUpdate:
            getValueForFieldName(
              x?.["viewData"]?.["Control"]?.["Control"],
              "Commitment Update"
            ) === true
              ? "X"
              : "",
          ConditionTableUsage: "",
          Application: "",
          CstgSheet: getValueForFieldName(
            x?.["viewData"]?.["Templates"]?.["Overhead rates"],
            "Costing Sheet"
          ),
          ActyIndepTemplate: getValueForFieldName(
            x?.["viewData"]?.["Templates"]?.["Formula planning"],
            "Acty-Indep. Form Plng Temp"
          ),
          ActyDepTemplate: getValueForFieldName(
            x?.["viewData"]?.["Templates"]?.["Formula planning"],
            "Acty-Dep. Form Plng Temp"
          ),
          AddrTitle: getValueForFieldName(
            x?.["viewData"]?.["Address"]?.["Address Data"],
            "Title"
          ),
          AddrName1: getValueForFieldName(
            x?.["viewData"]?.["Address"]?.["Address Data"],
            "Name 1"
          ),
          AddrName2: getValueForFieldName(
            x?.["viewData"]?.["Address"]?.["Address Data"],
            "Name 2"
          ),
          AddrName3: getValueForFieldName(
            x?.["viewData"]?.["Address"]?.["Address Data"],
            "Name 3"
          ),
          AddrName4: getValueForFieldName(
            x?.["viewData"]?.["Address"]?.["Address Data"],
            "Name 4"
          ),
          AddrStreet: getValueForFieldName(
            x?.["viewData"]?.["Address"]?.["Address Data"],
            "Street"
          ),
          AddrCity: getValueForFieldName(
            x?.["viewData"]?.["Address"]?.["Address Data"],
            "Location"
          ),
          AddrDistrict: getValueForFieldName(
            x?.["viewData"]?.["Address"]?.["Address Data"],
            "District"
          ),
          AddrCountry: getValueForFieldName(
            x?.["viewData"]?.["Address"]?.["Address Data"],
            "Country/Reg"
          ),
          AddrCountryIso: "",
          AddrTaxjurcode: getValueForFieldName(
            x?.["viewData"]?.["Address"]?.["Address Data"],
            "Jurisdiction"
          ),
          AddrPoBox: getValueForFieldName(
            x?.["viewData"]?.["Address"]?.["Address Data"],
            "PO Box"
          ),
          AddrPostlCode: getValueForFieldName(
            x?.["viewData"]?.["Address"]?.["Address Data"],
            "Postal Code"
          ),
          AddrPobxPcd: getValueForFieldName(
            x?.["viewData"]?.["Address"]?.["Address Data"],
            "PO Box Post Cod"
          ),
          AddrRegion: getValueForFieldName(
            x?.["viewData"]?.["Address"]?.["Address Data"],
            "Region"
          ),
          TelcoLangu: "",
          TelcoLanguIso: getValueForFieldName(
            x?.["viewData"]?.["Communication"]?.["Communication Data"],
            "Language Key"
          ),
          TelcoTelephone: getValueForFieldName(
            x?.["viewData"]?.["Communication"]?.["Communication Data"],
            "Telephone 1"
          ),
          TelcoTelephone2: getValueForFieldName(
            x?.["viewData"]?.["Communication"]?.["Communication Data"],
            "Telephone 2"
          ),
          TelcoTelebox: getValueForFieldName(
            x?.["viewData"]?.["Communication"]?.["Communication Data"],
            "Telebox Number"
          ),
          TelcoTelex: getValueForFieldName(
            x?.["viewData"]?.["Communication"]?.["Communication Data"],
            "Telex Number"
          ),
          TelcoFaxNumber: getValueForFieldName(
            x?.["viewData"]?.["Communication"]?.["Communication Data"],
            "Fax Number"
          ),
          TelcoTeletex: getValueForFieldName(
            x?.["viewData"]?.["Communication"]?.["Communication Data"],
            "Teletex Number"
          ),
          TelcoPrinter: getValueForFieldName(
            x?.["viewData"]?.["Communication"]?.["Communication Data"],
            "Printer Destination"
          ),
          TelcoDataLine: getValueForFieldName(
            x?.["viewData"]?.["Communication"]?.["Communication Data"],
            "Data Line"
          ),
          ActyDepTemplateAllocCc: getValueForFieldName(
            x?.["viewData"]?.["Templates"]?.[
              "Activity and Business Process Allocation"
            ],
            "Acty-Dep. Alloc Template"
          ),
          ActyDepTemplateSk: getValueForFieldName(
            x?.["viewData"]?.["Templates"]?.["Actual Statistical Key Figures"],
            "Templ.: Act. Stat. Key Figure"
          ),
          ActyIndepTemplateAllocCc: getValueForFieldName(
            x?.["viewData"]?.["Templates"]?.[
              "Activity and Business Process Allocation"
            ],
            "Acty-Indep. Alloc Temp"
          ),
          ActyIndepTemplateSk: getValueForFieldName(
            x?.["viewData"]?.["Templates"]?.["Actual Statistical Key Figures"],
            "Templ.: Act. Stat. Key Figure"
          ),
          AvcActive: false,
          AvcProfile: "",
          BudgetCarryingCostCtr: "",
          CurrencyIso: "",
          Department: getValueForFieldName(
            x?.["viewData"]?.["Basic Data"]?.["Basic Data"],
            "Department"
          ),
          FuncArea: getValueForFieldName(
            x?.["viewData"]?.["Basic Data"]?.["Basic Data"],
            "Functional Area"
          ),
          FuncAreaFixAssigned: "",
          FuncAreaLong: "",
          Fund: "",
          FundFixAssigned: "",
          GrantFixAssigned: "",
          GrantId: "",
          JvEquityTyp: "",
          JvJibcl: "",
          JvJibsa: "",
          JvOtype: "",
          JvRecInd: "",
          JvVenture: "",
          Logsystem: "",
        };
      }),
    };
  });
  console.log("rishav", payloadmapping);
  const initialRows = multipleCostData?.map((cCenter, index) => {
    const headerData = cCenter;
    const basicData = cCenter?.viewData?.["Basic Data"] || {};
    return {
      id: index,
      costCenter: headerData?.costCenter,
      name:
        basicData["Names"].find((field) => field?.fieldName === "Name")
          ?.value || "NA",
      controllingArea: headerData?.controllingArea,
      description:
        basicData["Names"].find((field) => field?.fieldName === "Description")
          ?.value || "NA",
      personResponsible:
        basicData["Basic Data"].find(
          (field) => field?.fieldName === "Person Responsible"
        )?.value || "NA",
      companyCode:
        basicData["Basic Data"].find(
          (field) => field?.fieldName === "Company Code"
        )?.value || "NA",
      profitCenter:
        basicData["Basic Data"].find(
          (field) => field?.fieldName === "Profit Center"
        )?.value || "NA",
      costCenterCategory:
        basicData["Basic Data"].find(
          (field) => field?.fieldName === "Cost Center Category"
        )?.value || "NA",
      validFrom: headerData?.validFrom,
      validTo: headerData?.validTo,
    };
  });

  const handleSelectionModelChange = (selectedIds) => {
    setSelectedPayloadData(selectedIds);
    if (selectedIds.length > 0) {
      setTestRun(true);
      console.log("selectedIds1", selectedIds);
    } else {
      setTestRun(false);
    }
    console.log("selectedIds", selectedIds);
    setSelectedRows(selectedIds);
    // setTestRun(true);
  };

  const handleSnackBarClose = () => {
    if (validateFlag) {
      setopenSnackbar(false);
      setValidateFlag(false);
    } else {
      setopenSnackbar(false);
      navigate("/masterDataCockpit/costCenter");
    }
  };
  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };
  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };
  const handleMessageDialogNavigate = () => {};

  useEffect(() => {
    const tempObj = {};
    const tempArray = multipleCostData?.filter((item, index) =>
      selectedPayloadData?.includes(index)
    );
    tempArray.forEach((element) => {
      if (element?.controllingArea in tempObj) {
        tempObj[element.controllingArea].push(element);
      } else {
        tempObj[element.controllingArea] = [element];
      }
    });
    setNewPayload(tempObj);
    console.log("temparray", tempArray, tempObj);
  }, [selectedPayloadData]);

  const handleSubmitForReviewCreate = () => {
    setBlurLoading(true);
    const selectedData = initialRows.filter((_, index) =>
      selectedRows.includes(index)
    );

    // const selectedCostCenterRows = selectedData.map((x) => ({
    //   ...payloadmapping.Toitem[x?.id],
    // }));
    // let payload = payloadmapping;
    // payload.Toitem = selectedCostCenterRows;

    const hSuccess = (data) => {
      setBlurLoading(false);
      // setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Mass Cost Center Sent for Review with ID NCM${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        // setIsLoading(false);
        setMessageDialogExtra(true);
        const secondApiPayload = {
          artifactId: ccNumber,
          createdBy: userData?.emailId,
          artifactType: "CostCenter",
          requestId: `NCM${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting the Cost Center for Review "
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      // setBlurLoading(false);
    };
    const hError = (error) => {
      console.log("error");
    };
    doAjax(
      `/${destination_CostCenter}/massAction/costCentersSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payloadmapping
    );
  };

  const handleSubmitForReviewChange = () => {
    setBlurLoading(true);
    const selectedData = initialRows.filter((_, index) =>
      selectedRows.includes(index)
    );
    // console.log("selectedData", selectedData);
    // const selectedCostCenterRows = selectedData.map((x) => ({
    //   ...payloadmapping.Toitem[x?.id],
    // }));
    // let payload = payloadmapping;
    // payload.Toitem = selectedCostCenterRows;

    const hSuccess = (data) => {
      setBlurLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Mass Cost Center Change Sent for Review with ID CCM${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        const secondApiPayload = {
          artifactId: ccNumber,
          createdBy: userData?.emailId,
          artifactType: "CostCenter",
          requestId: `CCM${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting the Mass Cost Center for Review "
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
    };
    const hError = (error) => {
      console.log("error");
    };
    doAjax(
      `/${destination_CostCenter}/massAction/changeCostCentersSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payloadmapping
    );
  };

  const checkCCNameEditorNot = (duplicateCCNameViewData, duplicateCCName) => {
    return duplicateCCNameViewData.every(
      (element) => !duplicateCCName.includes(element)
    );
  };

  const onValidateCostCenter = () => {
    setBlurLoading(true);
    const selectedData = initialRows.filter((_, index) =>
      selectedRows.includes(index)
    );
    console.log("selectedData", selectedData);

    // const selectedProfitCenterRows = selectedData.map((x) => ({
    //   ...payloadmapping.Toitem[x?.id],
    // }));
    // console.log("selectedProfitCenterRows", selectedProfitCenterRows);
    const duplicateCheckPayload = [];
    const duplicateCCName = [];
    selectedData.map((x) => {
      console.log("ios", x);
      var idk = {
        coArea: x?.controllingArea ?? "",
        name: x?.name ?? "",
      };
      duplicateCheckPayload.push(idk);
      duplicateCCName.push(x?.name.toUpperCase());
    });

    console.log("duplicateCheckPayload", duplicateCheckPayload);
    const duplicateCCNameViewData = [];
    multipleCostData.map((ccObject) => {
      //duplicateCCNameViewData.push(ccName.costCenterName)
      ccObject?.viewData?.["Basic Data"]?.["Names"].map((field) => {
        if (field.fieldName === "Name") {
          duplicateCCNameViewData.push(field.value.toUpperCase());
        }
      });
      //duplicateCCNameViewData
    });
    console.log(duplicateCCNameViewData, duplicateCCName, "arrayofviewand");
    let isuserEditCCName = checkCCNameEditorNot(
      duplicateCCNameViewData,
      duplicateCCName
    );
    // let payload = payloadmapping;
    // payload.Toitem = selectedProfitCenterRows;
    const hSuccess = (data) => {
      // setIsLoading(false);
      if (data.statusCode === 400) {
        setCostValidationErrors(data.body);
        setDialogOpen(true);
        setBlurLoading(false);
      } else {
        // Handle success
        setMessageDialogTitle("Create");
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `All Data has been Validated. Cost Center can be Sent for Review`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
        setValidateFlag(true);

        // Now, make the duplicate check API call
        // Ensure that the conditions for making the duplicate check API call are met
        if (
          duplicateCheckPayload.coArea !== "" ||
          duplicateCheckPayload.name !== ""
        ) {
          // payloadmapping.Toitem = duplicateCheckPayload.name;
          // if (massHandleType !== "Create" && !isuserEditCCName){
          //   setBlurLoading(false)
          // }else{
          doAjax(
            `/${destination_CostCenter}/alter/fetchCCDescriptionsDupliChk`,
            "post",
            hDuplicateCheckSuccess,
            hDuplicateCheckError,
            duplicateCheckPayload
          );
          //}
        }
      }
    };

    const hDuplicateCheckSuccess = (data) => {
      console.log("dataaaa", data);
      // Handle success of duplicate check
      // if (data.body?.length === 0 || !data.body.some(item => item.toUpperCase() === duplicateCheckPayload.name)) {
      if (
        data.body.length === 0 ||
        !data.body.some((item) =>
          duplicateCheckPayload.some(
            (payloadItem) => payloadItem.name.toUpperCase() === item.description
          )
        )
      ) {
        // No direct match, enable the "Submit for Review" button
        setBlurLoading(false);
        setSubmitForReviewDisabled(false);
        setTestRun(true);
      } else {
        // Handle direct match
        setBlurLoading(false);
        setMessageDialogTitle("Duplicate Check");
        setsuccessMsg(false);
        setMessageDialogMessage(
          `There is a direct match for the Cost Center name.`
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setSubmitForReviewDisabled(true);
        setDirectMatchedProfitCenters(directMatches);
      }
    };

    const hDuplicateCheckError = (error) => {
      // Handle error of duplicate check
      console.log(error);
    };

    const hError = (error) => {
      console.log(error);
    };

    // Call the main API for validation
    doAjax(
      `/${destination_CostCenter}/massAction/validateMassCostCenter`,
      "post",
      hSuccess,
      hError,
      payloadmapping
    );
  };

  const onCostCenterSubmitRemarks = () => {
    // setBlurLoading(true);
    handleRemarksDialogClose();
    if (massHandleType === "Create") {
      // setIsLoading(true);
      handleSubmitForReviewCreate();
    } else {
      // setIsLoading(true);
      handleSubmitForReviewChange();
    }
  };
  const handleRemarks = (e, value) => {
    //setRemarks(e.target.value);
    const newValue = e.target.value;
    if (newValue.length > 0 && newValue[0] === " ") {
      setRemarks(newValue.trimStart());
    } else {
      //let costCenterValue = e.target.value;
      let remarksUpperCase = newValue.toUpperCase();
      setRemarks(remarksUpperCase);
    }
  };
  // const onValidateCostCenter = () => {
  //   const selectedData = initialRows.filter((_, index) =>
  //   selectedRows.includes(index)
  // );
  // const selectedCostCenterRows = selectedData.map((x) => ({
  //   ...payloadmapping.Toitem[x?.id],
  // }));
  // console.log("selectedCostCenterRows", selectedCostCenterRows[0].Costcenter);

  // const duplicateCheckPayload = {
  //   coArea: multipleControllingArea,
  //   name: "TZUS",
  // };
  // console.log("duplicateCheckPayload", duplicateCheckPayload);
  // let payload = payloadmapping;
  // payload.Toitem = selectedCostCenterRows;
  //   const hSuccess = (data) => {
  //     setIsLoading();
  //     if (data.statusCode === 400) {
  //       // Handle error
  //       setMessageDialogTitle("Error");
  //       setsuccessMsg(false);
  //       setMessageDialogMessage(`${data?.body?.message[0] ? data?.body?.message[0] : data?.body?.value}`);
  //       setMessageDialogSeverity("danger");
  //       setMessageDialogOK(false);
  //       setMessageDialogExtra(true);
  //       handleMessageDialogClickOpen();
  //       setIsLoading(false);
  //     } else {
  //       // Handle success
  //       setMessageDialogTitle("Create");
  //       setMessageDialogTitle("Create");
  //       setMessageDialogMessage(
  //         `All Data has been Validated. Cost Center can be Sent for Review`
  //       );
  //       setMessageDialogSeverity("success");
  //       setMessageDialogOK(false);
  //       setsuccessMsg(true);
  //       handleSnackBarOpen();
  //       setMessageDialogExtra(true);
  //       setIsLoading(false);
  //       // setValidateFlag(true);

  //       // Now, make the duplicate check API call
  //       // Ensure that the conditions for making the duplicate check API call are met
  //       if (duplicateCheckPayload.coArea !== "" ||  duplicateCheckPayload.name.length > 0) {
  //         payloadmapping.Toitem = duplicateCheckPayload.name;
  //         doAjax(
  //           `/${destination_CostCenter}/alter/fetchCCDescriptionDupliChk`,
  //           "post",
  //           hDuplicateCheckSuccess,
  //           hDuplicateCheckError,
  //           duplicateCheckPayload
  //         );
  //       }
  //     }
  //   };

  //   const hDuplicateCheckSuccess = (data) => {
  //     console.log("dataaaa",data)
  //     // Handle success of duplicate check
  //     data.body.map((item) =>
  //     {if(duplicateCheckPayload.name.includes(item)){
  //       setMessageDialogTitle("Duplicate Check");
  //       setMessageDialogMessage(
  //         `There is a direct match for the Cost Center name.`
  //       );
  //       setMessageDialogSeverity("danger");
  //       setMessageDialogOK(false);
  //       setMessageDialogExtra(true);
  //       handleMessageDialogClickOpen();
  //       setSubmitForReviewDisabled(true);
  //     }
  //     else{
  //       setSubmitForReviewDisabled(false)
  //     }}
  //     );
  //   };

  //   const hDuplicateCheckError = (error) => {
  //     // Handle error of duplicate check
  //     console.log(error);
  //   };

  //   const hError = (error) => {
  //     console.log(error);
  //   };

  //   // Call the main API for validation
  //   doAjax(
  //     `/${destination_CostCenter}/alter/validateCostCenter`,
  //     "post",
  //     hSuccess,
  //     hError,
  //     payload
  //   );
  // };

  const columns = [
    {
      field: "costCenter",
      headerName: "Cost Center",
      editable: false,
      flex: 1,
      renderCell: (params) => {
        const isDirectMatch = costValidationError.find(
          (element) => element.costCenter === params.value
        );
        console.log(isDirectMatch, "isDirectMatch");
        console.log(params, "params");

        if (isDirectMatch && isDirectMatch.code === 400) {
          return (
            <Typography sx={{ fontSize: "12px", color: "red" }}>
              {params.value}
            </Typography>
          );
        } else {
          return (
            <Typography sx={{ fontSize: "12px" }}>{params.value}</Typography>
          );
        }
      },
    },
    {
      field: "controllingArea",
      headerName: "Controlling Area",
      editable: false,
      flex: 1,
    },
    {
      field: "name",
      headerName: "Name",
      editable: false,
      flex: 1,
      renderCell: (params) => {
        const isDirectMatch = directMatchedProfitCenters.includes(
          params.row.profitCenterName
        );
        return (
          <Typography
            sx={{ fontSize: "12px", color: isDirectMatch ? "red" : "inherit" }}
          >
            {params.value}
          </Typography>
        );
      },
    },
    {
      field: "description",
      headerName: "Description",
      editable: false,
      flex: 1,
    },
    {
      field: "personResponsible",
      headerName: "Person Responsible",
      editable: false,
      flex: 1,
    },
    {
      field: "companyCode",
      headerName: "Company Code",
      editable: false,
      flex: 1,
    },
    {
      field: "profitCenter",
      headerName: "Profit Center",
      editable: false,
      flex: 1,
    },
    {
      field: "costCenterCategory",
      headerName: "Cost Center Category",
      editable: false,
      flex: 1,
    },

    {
      field: "validFrom",
      headerName: "Valid From",
      editable: false,
      flex: 1,
      renderCell: (params) => {
        return (
          <Typography sx={{ fontSize: "12px" }}>
            {moment(params.row.validFrom).format(appSettings?.dateFormat)}
          </Typography>
        );
      },
    },
    {
      field: "validTo",
      headerName: "Valid To",
      editable: false,
      flex: 1,
      renderCell: (params) => {
        return (
          <Typography sx={{ fontSize: "12px" }}>
            {moment(params.row.validTo).format(appSettings?.dateFormat)}
          </Typography>
        );
      },
    },
  ];

  const handleRemarksDialogClose = () => {
    setTestRun(true);
    setOpenCorrectionDialog(false);
  };

  const handleOpenCorrectionDialog = () => {
    setTestRun(false);
    setOpenCorrectionDialog(true);
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
  };

  const validationColumns = [
    {
      field: "costCenter",
      headerName: "Cost Center",
      editable: false,
      flex: 1,
      // width: 100,
    },
    {
      field: "error",
      headerName: "Error",
      editable: false,
      flex: 1,
      // width: 400,
    },
  ];

  const validationRows = costValidationError
    ?.filter((row) => row?.code === 400)
    ?.map((item, index) => {
      if (item.code === 400) {
        return {
          id: index,
          costCenter: item?.costCenter,
          error: item?.status?.message,
        };
      }
    });
  return (
    <>
      {/* {isLoading === true ? (
        <LoadingComponent />
      ) : ( */}
      <div>
        <Dialog
          hideBackdrop={false}
          elevation={2}
          PaperProps={{
            sx: { boxShadow: "none" },
          }}
          open={openCorrectionDialog}
          onClose={handleRemarksDialogClose}
        >
          <DialogTitle
            sx={{
              justifyContent: "space-between",
              alignItems: "center",
              height: "max-content",
              padding: ".5rem",
              paddingLeft: "1rem",
              backgroundColor: "#EAE9FF40",
              display: "flex",
            }}
          >
            <Typography variant="h6">Remarks</Typography>

            <IconButton
              sx={{ width: "max-content" }}
              onClick={handleRemarksDialogClose}
              children={<CloseIcon />}
            />
          </DialogTitle>
          <DialogContent sx={{ padding: ".5rem 1rem" }}>
            <Stack>
              <Box sx={{ minWidth: 400 }}>
                <FormControl sx={{ height: "auto" }} fullWidth>
                  <TextField
                    sx={{ backgroundColor: "#F5F5F5" }}
                    inputProps={{
                      style: { textTransform: "uppercase" },
                      maxLength: 254,
                    }}
                    value={remarks}
                    onChange={handleRemarks}
                    multiline
                    placeholder={"Enter Remarks"}
                    inputProps={{maxLength: 254}}
                  ></TextField>
                </FormControl>
              </Box>
            </Stack>
          </DialogContent>
          <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
            <Button
              sx={{ width: "max-content", textTransform: "capitalize" }}
              onClick={handleRemarksDialogClose}
            >
              Cancel
            </Button>
            <Button
              className="button_primary--normal"
              type="save"
              onClick={onCostCenterSubmitRemarks}
              variant="contained"
            >
              Submit
            </Button>
          </DialogActions>
        </Dialog>

        <Dialog
          open={dialogOpen}
          fullWidth
          onClose={handleDialogClose}
          sx={{
            "&::webkit-scrollbar": {
              width: "1px",
            },
            // paddingBottom:1
          }}
        >
          <DialogTitle
            sx={{
              justifyContent: "space-between",
              alignItems: "center",
              height: "max-content",
              padding: ".5rem",
              paddingLeft: "1rem",
              backgroundColor: "#EAE9FF40",
              // borderBottom: "1px solid grey",
              display: "flex",
            }}
          >
            <Typography variant="h6" color="red">
              Errors
            </Typography>

            <IconButton
              sx={{ width: "max-content" }}
              onClick={handleDialogClose}
              children={<CloseIcon />}
            />
          </DialogTitle>
          <DialogContent sx={{ padding: ".5rem 1rem" }}>
            {/* <Grid container> */}

            {validationRows && (
              <ReusableTable
                isLoading={isLoading}
                width="100%"
                // title={"Profit Center Master List (" + initialRows.length + ")"}
                rows={validationRows}
                columns={validationColumns}
                pageSize={10}
                getRowIdValue={"id"}
                hideFooter={true}
                checkboxSelection={false}
                disableSelectionOnClick={true}
                status_onRowSingleClick={true}
                stopPropagation_Column={"action"}
                status_onRowDoubleClick={true}
              />
            )}

            {/* </Grid> */}
          </DialogContent>

          <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
            {/* <Button
            sx={{ width: "max-content", textTransform: "capitalize" }}
            onClick={handleDialogClose}
          >
            Cancel
          </Button>
          <Button
            className="button_primary--normal"
            type="save"
            onClick={handleDialogProceed}
            variant="contained"
          >
            Proceed
          </Button> */}
          </DialogActions>
        </Dialog>
        <ReusableDialog
          dialogState={openMessageDialog}
          openReusableDialog={handleMessageDialogClickOpen}
          closeReusableDialog={handleMessageDialogClose}
          dialogTitle={messageDialogTitle}
          dialogMessage={messageDialogMessage}
          handleDialogConfirm={handleMessageDialogClose}
          dialogOkText={"OK"}
          handleExtraButton={handleMessageDialogNavigate}
          dialogSeverity={messageDialogSeverity}
        />

        {successMsg && (
          <ReusableSnackBar
            openSnackBar={openSnackbar}
            alertMsg={messageDialogMessage}
            handleSnackBarClose={handleSnackBarClose}
          />
        )}

        <div style={{ ...outermostContainer, backgroundColor: "#FAFCFF" }}>
          <Grid container sx={outermostContainer_Information}>
            <Grid item md={12} sx={{ display: "flex", marginBottom: "0" }}>
              <Grid item md={11} sx={{ display: "flex" }}>
                <Grid>
                  <IconButton
                    // onClick={handleBacktoRO}
                    color="primary"
                    aria-label="upload picture"
                    component="label"
                    sx={iconButton_SpacingSmall}
                  >
                    <ArrowCircleLeftOutlinedIcon
                      style={{
                        height: "1em",
                        width: "1em",
                        color: "#000000",
                      }}
                      // sx={{
                      //   fontSize: "1.5em",
                      //   color: "#000000",
                      // }}
                      onClick={() => {
                        navigate("/masterDataCockpit/costCenter");
                      }}
                    />
                  </IconButton>
                </Grid>
                <Grid>
                  {massHandleType === "Create" ? (
                    <Grid item md={12}>
                      <Typography variant="h3">
                        <strong>Create Multiple Cost Centers</strong>
                      </Typography>
                      <Typography variant="body2" color="#777">
                        This view creates multiple Cost Centers
                      </Typography>
                    </Grid>
                  ) : (
                    <Grid item md={12}>
                      <Typography variant="h3">
                        <strong>Change Multiple Cost Centers</strong>
                      </Typography>
                      <Typography variant="body2" color="#777">
                        This view changes multiple Cost Centers
                      </Typography>
                    </Grid>
                  )}
                </Grid>
              </Grid>
              <Grid item md={1} sx={{ display: "flex" }}>
                <Tooltip title="Upload documents if any" arrow>
                  <IconButton onClick={handleOpenDialog}>
                    <AttachFileOutlinedIcon />
                  </IconButton>
                </Tooltip>
              </Grid>
              <Dialog
                hideBackdrop={false}
                elevation={2}
                PaperProps={{
                  sx: { boxShadow: "none" },
                }}
                open={openDialog}
                onClose={handleCloseDialog}
              >
                <DialogTitle
                  sx={{
                    justifyContent: "space-between",
                    alignItems: "center",
                    height: "max-content",
                    padding: ".5rem",
                    paddingLeft: "1rem",
                    backgroundColor: "#EAE9FF40",
                    // borderBottom: "1px solid grey",
                    display: "flex",
                  }}
                >
                  <Typography variant="h6">Add Attachment</Typography>
                </DialogTitle>
                <DialogContent sx={{ padding: ".5rem 1rem" }}>
                  <Stack>
                    <Box sx={{ minWidth: 400 }}>
                      <ReusableAttachementAndComments
                        title="CostCenter"
                        useMetaData={false}
                        artifactId={ccNumber}
                        artifactName="CostCenter"
                      />
                    </Box>
                  </Stack>
                </DialogContent>
                <DialogActions>
                  <Button onClick={handleCloseDialog}>Close</Button>
                </DialogActions>
              </Dialog>
            </Grid>
          </Grid>

          <Grid item sx={{ position: "relative" }}>
            <Stack>
              <ReusableTable
                isLoading={isLoading}
                width="100%"
                title={"Cost Center Master List (" + initialRows.length + ")"}
                rows={initialRows}
                columns={columns}
                pageSize={10}
                getRowIdValue={"id"}
                hideFooter={false}
                checkboxSelection={true}
                disableSelectionOnClick={true}
                status_onRowSingleClick={true}
                onRowsSelectionHandler={handleSelectionModelChange}
                callback_onRowSingleClick={(params) => {
                  console.log("paramss", params);
                  // Adjust this based on your data structure
                  const costCenter = params.row.costCenter;
                  //condition to send a row data
                  const dataToSend = multipleCostData.find(
                    (item) => item.costCenter === costCenter
                  );
                  navigate(
                    `/masterDataCockpit/costCenter/createMultipleCostCenter/editMultipleCostCenter/${costCenter}`,
                    {
                      state: {
                        tabsData: dataToSend,
                        rowData: params.row,
                      },
                    }
                  );
                }}
                // setShowWork={setShowWork}
                stopPropagation_Column={"action"}
                status_onRowDoubleClick={true}
              />
            </Stack>
          </Grid>
        </div>
        <Paper
          sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
          elevation={2}
        >
          <BottomNavigation
            className="container_BottomNav"
            showLabels
            sx={{
              display: "flex",

              justifyContent: "flex-end",
            }}
            value={value}
            // onChange={(newValue) => {
            // setValue(newValue);
            // }}
          >
            {/* <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary, mr: 1 }}
              // onClick={onSaveButtonClick}
              
            >
              Save As Draft
            </Button> */}

            {/* <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary }}
              // onClick={handleSubmitForReview}
            >
              Submit for Review
            </Button> */}

            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary, mr: 1 }}
              onClick={onValidateCostCenter}
              disabled={!testRun}
            >
              Validate
            </Button>
            {massHandleType === "Create" ? (
              <Button
                variant="contained"
                size="small"
                sx={{ ...button_Primary }}
                onClick={handleOpenCorrectionDialog}
                // disabled={testRun}
                disabled={submitForReviewDisabled}
              >
                Submit for Review
              </Button>
            ) : massHandleType === "Change" ? (
              <Button
                variant="contained"
                size="small"
                sx={{ ...button_Primary }}
                onClick={handleOpenCorrectionDialog}
                disabled={submitForReviewDisabled}
              >
                Submit for Review
              </Button>
            ) : (
              ""
            )}
          </BottomNavigation>
        </Paper>
      </div>
      {/* )} */}
      <Backdrop
        sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
        open={blurLoading}
        // onClick={handleClose}
      >
        <CircularProgress color="inherit" />
      </Backdrop>
    </>
  );
};
export default CreateMultipleCostCenter;
