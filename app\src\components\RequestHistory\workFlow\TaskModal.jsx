import AssignmentIcon from '@mui/icons-material/Assignment';
import CloseIcon from '@mui/icons-material/Close';

const calculateDueDate = (slaDays) => {
  const now = new Date();
  const due = new Date(now);
  due.setDate(now.getDate() + slaDays);
  return due.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
};

const TaskModal = ({ task, onClose }) => {
  return (
    <div className="modal" onClick={(e) => e.target.classList.contains('modal') && onClose()}>
      <div className="modal-content">
        <div className="modal-header">
          <div className="modal-title" style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <AssignmentIcon style={{ color: '#fff' }} />
            <span>{task.name}</span>
          </div>
          <div className="modal-close" onClick={onClose} style={{ cursor: 'pointer' }}>
            <CloseIcon style={{ color: '#fff' }} />
          </div>
        </div>
        <div className="modal-body">
          <div className="task-detail">
            <div className="detail-label">Task Group:</div>
            <div className="detail-value">{task.group}</div>
          </div>
          <div className="task-detail">
            <div className="detail-label">Approver:</div>
            <div className="detail-value">{task.approver}</div>
          </div>
          <div className="task-detail">
            <div className="detail-label">Level:</div>
            <div className="detail-value">{task.level}</div>
          </div>
          <div className="task-detail">
            <div className="detail-label">SLA:</div>
            <div className="detail-value">
              <span className="sla-badge sla-normal">{task.sla} days</span>
            </div>
          </div>
          <div className="task-detail">
            <div className="detail-label">Due Date:</div>
            <div className="detail-value">{calculateDueDate(task.sla)}</div>
          </div>
          <div className="task-detail">
            <div className="detail-label">Status:</div>
            <div className="detail-value">{task.status}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TaskModal;
