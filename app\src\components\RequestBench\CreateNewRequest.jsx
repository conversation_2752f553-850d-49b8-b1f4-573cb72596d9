import { useEffect, useState } from "react";
import { <PERSON>, <PERSON>ton, Grid, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, StepButton, IconButton, DialogContent, DialogActions } from "@mui/material";
import RequestHeader from "./RequestPages/RequestHeader";
import { doAjax } from "../Common/fetchService";
import { destination_IDM, destination_MaterialMgmt } from "../../destinationVariables";
import { useDispatch, useSelector } from "react-redux";
import RequestDetails from "./RequestPages/RequestDetails";
import RequestDetailsForExtend from "./RequestPages/RequestDetailsForExtend";
import { clearTemplateArray, clearChangeLogData,setChangeFieldRows, setChangeFieldRowsDisplay, resetPayloadData, setMatlNoData, setRequestorPayload, clearDynamicKeyValue, updateSelectedRows, setPlantData } from "../../app/payloadslice";
import { idGenerator } from "../../functions";
import AttachmentsCommentsTab from "./RequestPages/AttachmentsCommentsTab";
import { setMaterialRows, setRequestHeader, setTabValue } from "../../app/requestDataSlice";
import { useLocation, useNavigate } from "react-router-dom";
import PreviewPage from "./PreviewPage";
import ArrowCircleLeftOutlined from "@mui/icons-material/ArrowCircleLeftOutlined";
import SummarizeOutlinedIcon from "@mui/icons-material/SummarizeOutlined";
import PermIdentityOutlinedIcon from "@mui/icons-material/PermIdentityOutlined";
import FeedOutlinedIcon from "@mui/icons-material/FeedOutlined";
import RequestDetailsForChange from "./RequestPages/RequestDetailsForChange";
import useMaterialRequestHeaderConfig from "@hooks/useMaterialRequestHeaderConfig";
import TrackChangesTwoToneIcon from "@mui/icons-material/TrackChangesTwoTone";
import FileUploadOutlinedIcon from "@mui/icons-material/FileUploadOutlined";
import ReusableDialog from "@components/Common/ReusableDialog";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import { appendPrefixByJavaKey, clearLocalStorageItem, convertKeysName, getLocalStorage, } from "@helper/helper";
import ChangeLog from "@components/Changelog/ChangeLog";
import useGenericDtCall from "@hooks/useGenericDtCall";
import { DECISION_TABLE_NAME, DIALOUGE_BOX_MESSAGES, ENABLE_STATUSES, LOADING_MESSAGE, REGION, DIVERSION_CONTROL_FLAG, REQUEST_STATUS, REQUEST_TYPE, LOCAL_STORAGE_KEYS, API_CODE } from "@constant/enum";
import { setDropDown } from "@app/dropDownDataSlice";
import ReusableSnackBar from "@components/Common/ReusableSnackBar";
import { changeTemplateDT, clearMaterialFieldConfig, updateAllTabsData } from "@app/tabsDetailsSlice";
import { clearCreateChangeLogData, clearCreateTemplateArray } from "@app/changeLogReducer";
import CreateChangeLog from "../createChangeLog/CreateChangeLog";
import { colors } from "@constant/colors";
import CustomDialog from "@components/Common/ui/CustomDialog";
import { WarningOutlined } from "@mui/icons-material";
import { button_Outlined, button_Primary } from "../Common/commonStyles.jsx";
import { APP_END_POINTS } from "@constant/appEndPoints";
import ExcelOperationsCard from "../Common/ExcelOperationsCard"
import RequestDetailsForFC from "./RequestPages/RequestDetailsForFC";
import { END_POINTS } from "@constant/apiEndPoints";
import useDisplayData from "@hooks/useDisplayDataDto";
import useLang from "@hooks/useLang";

const CreateNewRequest = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [mandFields, setMandFields] = useState([]);
  const [downloadClicked, setDownloadClicked] = useState(false);
  const [ruleData, setRuleData] = useState([]);
  const [isSecondTabEnabled, setIsSecondTabEnabled] = useState(false);
  const [isAttachmentTabEnabled, setIsAttachmentTabEnabled] = useState(false);
  const [pcNumber, setPcNumber] = useState("");
  const [enableDocumentUpload, setEnableDocumentUpload] = useState(false);
  const [attachmentsData, setAttachmentsData] = useState([]);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [isChangeLogopen, setisChangeLogopen] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState("");
  const [messageDialogMessage, setMessageDialogMessage] = useState();
  const [messageDialogSeverity, setMessageDialogSeverity] = useState("");
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [alertType, setAlertType] = useState("success");
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [addHardCodeData, setAddHardCodeData] = useState(false);
  const [createChangeLogIsOpen, setCreateChangeLogIsOpen] = useState(false);
  const [isDialogVisible, setisDialogVisible] = useState(false);
  const dispatch = useDispatch();
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const payloadData = useSelector((state) => state.payload.payloadData);
  const requestIdHeader = useSelector((state) => state.request.requestHeader?.requestId);
  const requestType = useSelector((state) => state.request.requestHeader.requestType);
  const taskData = useSelector((state) => state.userManagement?.taskData);
  const { getDtCall, dtData } = useGenericDtCall();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const tabValue = useSelector((state) => state.request.tabValue);
  const { t } = useLang();

  const { getRequestHeaderTemplate } = useMaterialRequestHeaderConfig();
  const steps = [t("Request Header"), t("Material List"), t("Attachments & Remarks"),t("Preview")];
  const [completed, setCompleted] = useState([false]);

  const handleTabChange = (index) => {
    dispatch(setTabValue(index));
  };

  const location = useLocation();
  const rowData = location.state;
  const isChildRequest = location.state?.isChildRequest ?? false;
  const urlSearchParams = new URLSearchParams(location.search.split("?")[1]);
  const requestId = urlSearchParams.get("RequestId");

  const queryParams = new URLSearchParams(location.search);
  const RequestId = queryParams.get("RequestId");
  const RequestType = queryParams.get("RequestType");
  const reqBench = queryParams.get("reqBench");
  const { 
    getDisplayData,  
  } = useDisplayData();

  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };

  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };

  const openChangeLog = () => {
    setisChangeLogopen(true);
  };

  const handleClosemodalData = (data) => {
    setisChangeLogopen(data);
  };

  const handleOk = () => {
    if (messageDialogSeverity === "success") {
      navigate(`/requestBench`);
    } else {
      handleMessageDialogClose();
    }
  };

  const handleDownload = () => {
    setDownloadClicked(true);
  }
  const handleUploadMaterial = (file) => {
    let url = "";
    if (RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) {
      url = "getAllMaterialsFromExcel";
    } else if (RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) {
      url = "getAllMaterialsFromExcelForMassExtend";
    } else {
      url = "getAllMaterialsFromExcelForMassChange";
    }
    setLoaderMessage("Initiating Excel Upload");
    setBlurLoading(true);
    const formData = new FormData();
    [...file].forEach((item) => formData.append("files", item));
    formData.append("dtName", (RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) ? "MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG" : "MDG_MAT_CHANGE_TEMPLATE");
    formData.append("version", (RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) ? "v1" : "v5");
    formData.append("requestId", requestId ? requestId.slice(3) : "");
    formData.append("region", payloadData?.Region ? payloadData?.Region : "US");
    formData.append("matlType", "ALL");

    const hSuccess = (data) => {
      if (data?.statusCode === API_CODE.STATUS_200) {
        setEnableDocumentUpload(false);
        setBlurLoading(false);
        setLoaderMessage("");
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      } else {
        setEnableDocumentUpload(false);
        setBlurLoading(false);
        setMessageDialogMessage(data?.message);
        setLoaderMessage("");
        setAlertType("error");
        handleSnackBarOpen();
      }
    };
    const hError = (error) => {
      setBlurLoading(false);
      setMessageDialogMessage(error?.message);
      setLoaderMessage("");
      setAlertType("error");
      handleSnackBarOpen();
    };

    doAjax(`/${destination_MaterialMgmt}/massAction/${url}`, "postformdata", hSuccess, hError, formData);
  };

  useEffect(() => {
    const loadData = async () => {
      if (RequestId) {
        const savedTask = getLocalStorage(LOCAL_STORAGE_KEYS.CURRENT_TASK, true, {});
        const effectiveRequestType = RequestType || taskData?.ATTRIBUTE_2 || savedTask?.ATTRIBUTE_2 ;
        await getDisplayData(RequestId,effectiveRequestType,reqBench,taskData,rowData);
        if (((RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD && !rowData?.material?.length) || RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) && (rowData?.reqStatus === REQUEST_STATUS.DRAFT || rowData?.reqStatus === REQUEST_STATUS.UPLOAD_FAILED)) {
          dispatch(setTabValue(0));
          setIsSecondTabEnabled(false);
          setIsAttachmentTabEnabled(false);
        } else {
          dispatch(setTabValue(1));
          setIsSecondTabEnabled(true);
          setIsAttachmentTabEnabled(true);
        }
        setAddHardCodeData(true);
      } else {
        dispatch(setTabValue(0));
      }
    };
    
    loadData();
    return () => {
      dispatch(changeTemplateDT([]));
      dispatch(clearChangeLogData());
      dispatch(clearTemplateArray());
      dispatch(clearMaterialFieldConfig());
      dispatch(clearCreateTemplateArray());
      dispatch(clearCreateChangeLogData());
      dispatch(updateAllTabsData({}));
      dispatch(resetPayloadData({ data: {} }));
      dispatch(setMatlNoData([]))
      dispatch(setPlantData([]))
      dispatch(setRequestorPayload({}));
      dispatch(clearDynamicKeyValue());
      dispatch(updateSelectedRows([]));
      dispatch(setChangeFieldRows([]));
      dispatch(setChangeFieldRowsDisplay({}));
      clearLocalStorageItem(LOCAL_STORAGE_KEYS.CURRENT_TASK);
      clearLocalStorageItem(LOCAL_STORAGE_KEYS.ROLE);
    };
  }, [requestId, dispatch]);

  function callDivisionDtCall(region) {
    let payload = {
      decisionTableId: null,
      decisionTableName: DECISION_TABLE_NAME.MDG_MAT_REGION_DIVISION_MAPPING,
      version: "v1",
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_REGION": region,
        },
      ],
    };
    getDtCall(payload);
  }

  useEffect(() => {
    if(payloadData?.Region) {
      callDivisionDtCall(payloadData?.Region);
    }
  }, [payloadData?.Region]);

  useEffect(() => {
    if (dtData) {
      const convertedData = convertKeysName(dtData?.result?.[0]?.MDG_MAT_REGION_DIVISION_MAPPING);
      const sortedData = [...convertedData].sort((a, b) => a.code.localeCompare(b.code));
      dispatch(setDropDown({ keyName: "Division", data: sortedData }));
      setLoading(false);
      setLoaderMessage(LOADING_MESSAGE.DT_LOADING);
    }
  }, [dtData]);

  useEffect(() => {
    getRequestHeaderTemplate();
    getAttachmentsIDM();
    // dispatch(setTabValue(0))
    dispatch(setMaterialRows([]));
    dispatch(setDropDown({ keyName: "Region", data: REGION }));
    dispatch(setDropDown({ keyName: "DiversionControlFlag", data: DIVERSION_CONTROL_FLAG }));
    return () => {
      dispatch(setRequestHeader({}));
    };
  }, []);

  useEffect(() => {
    if (isSecondTabEnabled) {
      setCompleted([true]);
    }
  }, [isSecondTabEnabled]);

  useEffect(() => {
    setPcNumber(idGenerator("MAT"));
  }, []);
  const getAttachmentsIDM = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_ATTACHMENTS_LIST_DT",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE": "Material",
          "MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO": "Create",
          "MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE": 1,
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    setIsLoading(true);
    // const formData = new FormData();
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        let responseData = data?.data?.result[0]?.MDG_ATTACHMENTS_ACTION_TYPE;
        let templateData = [];
        responseData?.map((element, index) => {
          var tempRow = {
            id: index,
            // templateName: element?.MDG_CHANGE_TEMPLATE_NAME,
            // templateData: element?.MDG_CHANGE_TEMPLATE_FIELD_LIST,
          };
          templateData.push(tempRow);
        });
        setRuleData(templateData);
        const attachmentNames = data?.data?.result[0]?.MDG_ATTACHMENTS_ACTION_TYPE || [];
        setAttachmentsData(attachmentNames);
      }
    };

    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(`/${destination_IDM}/rest/v1/invoke-rules`, "post", hSuccess, hError, payload);
    } else {
      doAjax(`/${destination_IDM}/v1/invoke-rules`, "post", hSuccess, hError, payload);
    }
  };

  const handleExportTemplateExcel = () => {
    
    const url = RequestId?.includes("FCA") ? END_POINTS.EXCEL.DOWNLOAD_EXCEL_FINANCE : END_POINTS.EXCEL.DOWNLOAD_EXCEL_MAT;
    setLoaderMessage("Please wait 1-2 minutes while real-time data are exported into the template. Thank you for your patience.");
    setBlurLoading(true);
    let financePayload = {
      "massSchedulingId": payloadData?.RequestId,
    };
    let payload = {
      dtName: payloadData?.RequestType === REQUEST_TYPE?.CHANGE || payloadData?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD ? "MDG_MAT_CHANGE_TEMPLATE" : "MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",
      version: payloadData?.RequestType === REQUEST_TYPE?.CHANGE || payloadData?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD ? "v4" : "v1",
      requestId: payloadData?.RequestId || requestIdHeader || "",
      scenario: payloadData?.RequestType === REQUEST_TYPE?.CHANGE || payloadData?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD ? "Change with Upload" : "Create with Upload",
      templateName: payloadData?.TemplateName || "",
      region: payloadData?.Region || "",
      matlType: "ALL",
    };
    // NOTE: TO BE UNCOMMENTED WHEN PAYLOAD NEEDS CHANGE

    // if (!reqBench && !RequestId) {
    //   const result = changePayloadForTemplate(false)
    //   payload = { ...payload, materials: result }
    // }
    // else {
    //   const result = changePayloadForTemplate(true)
    //   payload = { ...payload, materials: result }
    // }

    const hSuccess = (response) => {
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");

      link.href = href;
      link.setAttribute("download", `${payloadData?.TemplateName ? payloadData?.TemplateName : RequestId?.includes("FCA") ? REQUEST_TYPE.FINANCE_COSTING : "Mass_Create"}_Data Export.xlsx`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);

      setBlurLoading(false);
      setLoaderMessage("");
      setMessageDialogMessage(`${payloadData?.TemplateName ? payloadData?.TemplateName : RequestId?.includes("FCA") ? REQUEST_TYPE.FINANCE_COSTING : "Mass_Create"}_Data Export.xlsx has been exported successfully.`);
      setAlertType("success");
      handleSnackBarOpen();
    };
    const hError = () => {};
    doAjax(`/${destination_MaterialMgmt}${url}`, "postandgetblob", hSuccess, hError, RequestId?.includes("FCA") ? financePayload : payload);
  };

  const handleSnackBarOpen = () => {
    setOpenSnackbar(true);
  };

  const handleSnackBarClose = () => {
    setOpenSnackbar(false);
  };

  const handleYes = () => {
    if(requestId && !reqBench) {
      navigate(APP_END_POINTS?.MY_TASK);
    }
    else if(reqBench) {
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    }
    else if(!requestId && !reqBench) {
      navigate(APP_END_POINTS?.MASTER_DATA);
    }
  };

  const handleCancel = () => {
    setisDialogVisible(false)
  };
  return (
    <>
      {loading && <ReusableBackDrop blurLoading={blurLoading} loaderMessage={loaderMessage} />}
      <Box sx={{ padding: 2 }}>
        <Grid sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
          {requestIdHeader || requestId ? (
            <Typography variant="h6" sx={{ mb: 1, textAlign: "left", display: "flex", alignItems: "center", gap: 1 }}>
              <PermIdentityOutlinedIcon sx={{ fontSize: "1.5rem" }} />
              {t("Request Header ID")}: <span>{requestIdHeader ? appendPrefixByJavaKey(requestType, requestIdHeader) : requestId}</span>
            </Typography>
          ) : (
            <div style={{ flex: 1 }} />
          )}

          {tabValue === 1 && (
            <Box sx={{ display: "flex", justifyContent: "flex-end", gap: "1rem" }}>
              <Button
                variant="outlined"
                size="small"
                title={t("Download Error Report")}
                disabled={!RequestId}
                onClick={() => {
                  navigate(`/requestBench/errorHistory?RequestId=${RequestId ? RequestId : ""}`, { state: { display: true , childRequest: isChildRequest } });
                }}
                color="primary"
              >
                <SummarizeOutlinedIcon sx={{ padding: "2px" }} />
              </Button>
              {payloadData?.RequestType === REQUEST_TYPE.CREATE || payloadData?.RequestType === REQUEST_TYPE.EXTEND || payloadData?.RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD ||
               payloadData?.RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || requestId?.includes("FCA") ? (
                <Button variant="outlined" disabled={!RequestId} size="small" onClick={() => setCreateChangeLogIsOpen(true)} title={requestId?.includes("FCA") ? t("Finance Costing Change Log") : t("Create Change Log")}>
                  <TrackChangesTwoToneIcon sx={{ padding: "2px" }} />
                </Button>
              ) : (
                <Button variant="outlined" disabled={!RequestId} size="small" onClick={openChangeLog} title={t("Change Log")}>
                  <TrackChangesTwoToneIcon sx={{ padding: "2px" }} />
                </Button>
              )}
              <Button variant="outlined" disabled={!RequestId} size="small" onClick={handleExportTemplateExcel} title={t("Export Excel")}>
                <FileUploadOutlinedIcon sx={{ padding: "2px" }} />
              </Button>
            </Box>
          )}

          {isChangeLogopen && <ChangeLog open={true} closeModal={handleClosemodalData} requestId={requestIdHeader || requestId.slice(3)} requestType={payloadData?.RequestType} />}
          {createChangeLogIsOpen && <CreateChangeLog open={true} closeModal={() => setCreateChangeLogIsOpen(false)} requestId={requestIdHeader || requestId.slice(3)} requestType={payloadData?.RequestType} />}
        </Grid>

        {payloadData?.TemplateName && (
          <Typography variant="h6" sx={{ mb: 1, textAlign: "left", display: "flex", alignItems: "center", gap: 1 }}>
            <FeedOutlinedIcon sx={{ fontSize: "1.5rem" }} />
            {t("Template Name")}: <span>{payloadData?.TemplateName}</span>
          </Typography>
        )}

        <IconButton
          onClick={() => {
            if(reqBench && !ENABLE_STATUSES?.includes(payloadData?.RequestStatus)) {
              navigate(APP_END_POINTS?.REQUEST_BENCH);
              return;
            }
            setisDialogVisible(true)
          }}
          color="primary"
          aria-label="upload picture"
          component="label"
          sx={{left: "-10px",}}
          title={t("Back")}
        >
          <ArrowCircleLeftOutlined
            sx={{ fontSize: "25px", color: "#000000" }}
          />
        </IconButton>

        <Stepper nonLinear activeStep={tabValue} sx={{ display: "flex", alignItems: "center", justifyContent: "center", margin: "25px 14%", marginTop: "-35px" }}>
          {steps.map((label, index) => (
            <Step key={label} completed={completed[index]}>
              <StepButton color="error" disabled={
                      (index === 1 && !isSecondTabEnabled) ||
                      (index === 2 && !isAttachmentTabEnabled)||
                      (index===3 && !isAttachmentTabEnabled)
                    } onClick={() => handleTabChange(index)} sx={{ fontSize: "50px", fontWeight: "bold" }}>
                <span style={{ fontSize: "15px", fontWeight: "bold" }}>{label}</span>
              </StepButton>
            </Step>
          ))}
        </Stepper>

        <ReusableDialog
          dialogState={openMessageDialog}
          openReusableDialog={handleMessageDialogClickOpen}
          closeReusableDialog={handleMessageDialogClose}
          dialogTitle={messageDialogTitle}
          dialogMessage={messageDialogMessage}
          handleDialogConfirm={handleMessageDialogClose}
          dialogOkText={"OK"}
          handleOk={handleOk}
          // handleExtraButton={handleMessageDialogNavigate}
          dialogSeverity={messageDialogSeverity}
        />

        <ReusableBackDrop blurLoading={blurLoading} loaderMessage={loaderMessage} />

        {tabValue === 0 && (
          <>
            <RequestHeader setIsSecondTabEnabled={setIsSecondTabEnabled} setIsAttachmentTabEnabled={setIsAttachmentTabEnabled} requestStatus={rowData?.reqStatus ? rowData?.reqStatus : REQUEST_STATUS.ENABLE_FOR_FIRST_TIME} downloadClicked={downloadClicked} setDownloadClicked={setDownloadClicked} />
            {(RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD || RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) && ((rowData?.reqStatus == REQUEST_STATUS.DRAFT && !rowData?.material?.length) || rowData?.reqStatus == REQUEST_STATUS.UPLOAD_FAILED) && (
              <ExcelOperationsCard
                handleDownload={handleDownload}
                setEnableDocumentUpload={setEnableDocumentUpload}
                enableDocumentUpload={enableDocumentUpload}
                handleUploadMaterial={handleUploadMaterial}
              />
            )}
            {(payloadData?.RequestType === REQUEST_TYPE?.CHANGE || payloadData?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD) && !RequestId && payloadData?.DirectAllowed !== "X" && payloadData?.DirectAllowed !== undefined && (
              <Typography
                sx={{
                  fontSize: "13px",
                  fontWeight: "500",
                  color: colors?.error?.dark,
                  marginTop: "1rem",
                  marginLeft: "0.5rem",
                }}
              >
                <Box component="span" sx={{ fontWeight: "bold" }}>
                  Note:
                </Box>{" "}
                You are not authorized to Tcode{" "}
                <Box component="span" sx={{ fontWeight: "bold" }}>
                  {" "}
                  MM02.
                </Box>
              </Typography>
            )}
          </>
        )}

        {tabValue === 1 &&
          (payloadData?.RequestType === REQUEST_TYPE?.CREATE || taskData?.ATTRIBUTE_2 === REQUEST_TYPE?.CREATE || RequestType === REQUEST_TYPE?.CREATE || RequestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD  ? (
            <RequestDetails requestStatus={rowData?.reqStatus ? rowData?.reqStatus : REQUEST_STATUS.ENABLE_FOR_FIRST_TIME} mandFields={mandFields} addHardCodeData={addHardCodeData} setIsAttachmentTabEnabled={setIsAttachmentTabEnabled} setCompleted={setCompleted} />
          ) : payloadData?.RequestType === REQUEST_TYPE?.EXTEND || taskData?.ATTRIBUTE_2 === REQUEST_TYPE?.EXTEND || taskData?.ATTRIBUTE_2 === REQUEST_TYPE?.EXTEND_WITH_UPLOAD || RequestType === REQUEST_TYPE?.EXTEND || RequestType === REQUEST_TYPE?.EXTEND_WITH_UPLOAD ? (
            <RequestDetailsForExtend requestStatus={rowData?.reqStatus ? rowData?.reqStatus : REQUEST_STATUS.ENABLE_FOR_FIRST_TIME} mandFields={mandFields} addHardCodeData={addHardCodeData} setIsAttachmentTabEnabled={setIsAttachmentTabEnabled} setCompleted={setCompleted} />
          ) : payloadData?.RequestType === REQUEST_TYPE?.FINANCE_COSTING || taskData?.ATTRIBUTE_2 === REQUEST_TYPE?.FINANCE_COSTING || RequestType === REQUEST_TYPE?.FINANCE_COSTING ? (
            <RequestDetailsForFC setCompleted={setCompleted}/>
          ): (
            <RequestDetailsForChange setIsAttachmentTabEnabled={true} setCompleted={setCompleted} downloadClicked={downloadClicked} setDownloadClicked={setDownloadClicked}/>
          ))}
        {tabValue === 2 && <AttachmentsCommentsTab requestStatus={rowData?.reqStatus ? rowData?.reqStatus : REQUEST_STATUS.ENABLE_FOR_FIRST_TIME} attachmentsData={attachmentsData} requestIdHeader={requestIdHeader ? appendPrefixByJavaKey(requestType, requestIdHeader) : requestId} pcNumber={pcNumber} />}
        {tabValue === 3 && 
            <Box
            sx={{
              width: "100%",
              overflow: "auto", // Ensure all content fits inside
            }}
          >
            <PreviewPage requestStatus={rowData?.reqStatus ? rowData?.reqStatus : REQUEST_STATUS.ENABLE_FOR_FIRST_TIME}>
              
            </PreviewPage>
          </Box>
        }
      </Box>
      {<ReusableSnackBar openSnackBar={openSnackbar} alertMsg={messageDialogMessage} alertType={alertType} handleSnackBarClose={handleSnackBarClose} />}
      {isDialogVisible && (
        <CustomDialog isOpen={isDialogVisible} titleIcon={<WarningOutlined size="small" sx={{ color: colors?.secondary?.amber, fontSize: "20px" }} />} Title={t("Warning")} handleClose={handleCancel}>
          <DialogContent sx={{ mt: 2 }}>{t(DIALOUGE_BOX_MESSAGES.LEAVE_PAGE_MESSAGE)}</DialogContent>
          <DialogActions>
            <Button variant="outlined" size="small" sx={{ ...button_Outlined }} onClick={handleCancel}>
              {t("No")}
            </Button>
            <Button variant="contained" size="small" sx={{ ...button_Primary }} onClick={handleYes}>
              {t("Yes")}
            </Button>
          </DialogActions>
        </CustomDialog>
      )}
    </>
  );
};

export default CreateNewRequest;
