import{b as ue,s as he,r as u,u as me,q as S,cd as ge,j as s,G as d,al as fe,a as t,z as ye,T as m,B as E,x as M,b4 as xe,br as be,aE as K,t as f,aB as A,aD as U,K as y,eV as x,eU as B,bp as Ce,I as Se,b1 as ve,b8 as Q,ai as b}from"./index-75c1660a.js";import{d as Ae}from"./ArrowCircleLeftOutlined-f7b52d40.js";import{d as Ne}from"./EditOutlined-6971b85d.js";import{E as Ee}from"./EditFieldForMassGL-24e72905.js";import{C as Be,d as Ge}from"./ChangeLog-54fbad38.js";import{a as Te,b as Fe,S as je}from"./Stepper-2dbfb76b.js";import"./DatePicker-31fef6b6.js";import"./dateViewRenderers-dbe02df3.js";import"./useSlotProps-da724f1f.js";import"./InputAdornment-a22e1655.js";import"./CSSTransition-8d766865.js";import"./useMediaQuery-33e0a836.js";import"./DesktopDatePicker-47a97548.js";import"./useMobilePicker-056b38fc.js";const Ke=()=>{var H,I;const O=ue(),p=he();u.useState({});const[L,$]=u.useState(0),[C,X]=u.useState(!1),[Me,Y]=u.useState(!0);u.useState(0),u.useState([]);const[h,N]=u.useState(0),[Z,V]=u.useState(!1),[_,R]=u.useState([]),[D,z]=u.useState(!1),g=me();S(e=>e.profitCenter.profitCenterCompCodes),console.log("location",g.state),S(e=>e.initialData.EditMultipleMaterial);let o=S(e=>{var i;return(i=e==null?void 0:e.initialData)==null?void 0:i.IWMMyTask}),c=g.state.rowViewData;const k=g.state.rowViewData.viewData,r=g.state.selectedRow;console.log("generalLedgerRowDataaaaa",r);const ee=g.state.requestNumber;let G=S(e=>e.edit.payload);console.log(G,"singleGLPayloadAfterChange");let T=S(e=>e.generalLedger.requiredFields);console.log(T,"required_field_for_data"),S(e=>e.payload);const te=()=>{var n,a;const e=l=>{p(b({keyName:"TaxCategory",data:l.body}))},i=l=>{console.log(l)};y(`/${x}/data/getTaxCategory?companyCode=${(n=o==null?void 0:o.body)!=null&&n.CompCode?(a=o==null?void 0:o.body)==null?void 0:a.CompCode:c==null?void 0:c.compCode}`,"get",e,i)},oe=()=>{var n,a;const e=l=>{p(b({keyName:"HouseBank",data:l.body}))},i=l=>{console.log(l)};y(`/${x}/data/getHouseBank?companyCode=${(n=o==null?void 0:o.body)!=null&&n.CompCode?(a=o==null?void 0:o.body)==null?void 0:a.CompCode:c==null?void 0:c.CompCode}`,"get",e,i)},ne=()=>{var n,a;const e=l=>{p(b({keyName:"FieldStatusGroup",data:l.body}))},i=l=>{console.log(l)};y(`/${x}/data/getFieldStatusGroup?companyCode=${(n=o==null?void 0:o.body)!=null&&n.CompCode?(a=o==null?void 0:o.body)==null?void 0:a.CompCode:c==null?void 0:c.CompCode}`,"get",e,i)},ie=()=>{var n,a;const e=l=>{p(b({keyName:"GroupAccountNumber",data:l.body}))},i=l=>{console.log(l)};y(`/${x}/data/getGroupAccountNumber?chartAccount=${(n=o==null?void 0:o.body)!=null&&n.chartOfAccount?(a=o==null?void 0:o.body)==null?void 0:a.chartOfAccount:c==null?void 0:c.ChartOfAccount}`,"get",e,i)},le=()=>{var n,a;const e=l=>{p(b({keyName:"AlternativeAccountNumber",data:l.body}))},i=l=>{console.log(l)};y(`/${x}/data/getAlternativeAccountNumber?chartAccount=${(n=o==null?void 0:o.body)!=null&&n.chartOfAccount?(a=o==null?void 0:o.body)==null?void 0:a.chartOfAccount:c==null?void 0:c.ChartOfAccount}`,"get",e,i)},ae=()=>{var n,a;const e=l=>{p(b({keyName:"AccountGroup",data:l.body}))},i=l=>{console.log(l)};y(`/${x}/data/getAccountGroupCodeDesc?chartAccount=${(n=o==null?void 0:o.body)!=null&&n.chartOfAccount?(a=o==null?void 0:o.body)==null?void 0:a.chartOfAccount:c==null?void 0:c.ChartOfAccount}`,"get",e,i)},re=()=>{var n,a;const e=l=>{p(b({keyName:"CostElementCategory",data:l.body}))},i=l=>{console.log(l)};y(`/${x}/data/getCostElementCategory?accountType=${(n=o==null?void 0:o.body)!=null&&n.accountType?(a=o==null?void 0:o.body)==null?void 0:a.accountType:r==null?void 0:r.accountType}`,"get",e,i)};u.useEffect(()=>{te(),ne(),oe(),ae(),le(),ie(),re()},[]),u.useEffect(()=>{p(ge(j))},[]);const ce=()=>{X(!0),Y(!1)},q=()=>{const e=P();C?e?(N(i=>i-1),p(B())):w():(N(i=>i-1),p(B()))},W=()=>{const e=P();C?e?(N(i=>i+1),p(B())):w():(N(i=>i+1),p(B()))},w=()=>{z(!0)},se=e=>{V(e)},de=()=>{V(!0)},v=Object.entries(k).filter(e=>typeof e[1]=="object"&&e[1]!=null).map(e=>e[0]);console.log("tabsArray",v,h);const F=Object.entries(k).filter(e=>typeof e[1]=="object"&&e[1]!=null).map(e=>Object.entries(e[1])),j={};F.map(e=>{e.forEach((i,n)=>{i.forEach((a,l)=>{l!==0&&a.forEach(J=>{j[J.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("")]=J.value})})})}),console.log(JSON.stringify(j),"temphash"),console.log(G,T,"========deeced"),console.log(h,"activeStep");const P=()=>Ce(G,T,R),pe=()=>{z(!1)};return s("div",{children:[s(d,{container:!0,style:{...fe,backgroundColor:"#FAFCFF"},children:[_.length!=0&&t(ye,{openSnackBar:D,alertMsg:"Please fill the following Field: "+_.join(", "),handleSnackBarClose:pe}),s(d,{sx:{width:"inherit"},children:[s(d,{item:!0,md:12,style:{padding:"16px",display:"flex"},children:[t(d,{item:!0,style:{display:"flex",justifyContent:"flex-end"},children:t(Se,{color:"primary","aria-label":"upload picture",component:"label",sx:ve,children:t(Ae,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{O("/masterDataCockpit/generalLedger/createMultipleGL")}})})}),s(d,{md:8,children:[t(m,{variant:"h3",children:s("strong",{children:["Multiple General Ledgers: ",r==null?void 0:r.glAccount," "]})}),t(m,{variant:"body2",color:"#777",children:"This view displays the detail of Multiple General Ledgers"})]}),(H=g==null?void 0:g.state)!=null&&H.requestNumber?t(d,{md:.5,sx:{display:"flex",justifyContent:"flex-end"},children:t(f,{variant:"outlined",size:"small",sx:Q,onClick:de,title:"Change Log",children:t(Ge,{sx:{padding:"2px"},fontSize:"small"})})}):t(d,{md:.5,sx:{display:"flex",justifyContent:"flex-end"}}),Z&&t(Be,{open:!0,closeModal:se,requestId:ee,requestType:"Mass",pageName:"generalLedger",controllingArea:r.companyCode,centerName:r.glAccount}),C?"":t(d,{md:4,sx:{display:"flex",justifyContent:"flex-end"},children:t(d,{item:!0,children:s(f,{variant:"outlined",size:"small",sx:Q,onClick:ce,children:["Change",t(Ne,{sx:{padding:"2px"},fontSize:"small"})]})})})]}),t(d,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:s(E,{width:"70%",sx:{marginLeft:"40px"},children:[t(d,{item:!0,sx:{paddingTop:"2px !important"},children:s(M,{flexDirection:"row",children:[t("div",{style:{width:"15%"},children:t(m,{variant:"body2",color:"#777",children:"General Ledger Account"})}),s(m,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[": ",r==null?void 0:r.glAccount]})]})}),t(d,{item:!0,sx:{paddingTop:"2px !important"},children:s(M,{flexDirection:"row",children:[t("div",{style:{width:"15%"},children:t(m,{variant:"body2",color:"#777",children:"Company Code"})}),s(m,{variant:"body2",fontWeight:"bold",children:[": ",r==null?void 0:r.companyCode]})]})}),t(d,{item:!0,sx:{paddingTop:"2px !important"},children:s(M,{flexDirection:"row",children:[t("div",{style:{width:"15%"},children:t(m,{variant:"body2",color:"#777",children:"Chart of Account"})}),s(m,{variant:"body2",fontWeight:"bold",children:[": ",r==null?void 0:r.chartOfAccount]})]})})]})}),s(d,{container:!0,style:{padding:"16px"},children:[t(je,{activeStep:h,sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},"aria-label":"mui tabs example",children:v.map((e,i)=>t(Te,{children:t(Fe,{sx:{fontWeight:"700"},children:e})},e))}),t(d,{container:!0,children:F&&((I=F[h])==null?void 0:I.map((e,i)=>t(E,{sx:{width:"100%"},children:s(d,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...xe},children:[t(d,{container:!0,children:t(m,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:e[0]})}),t(E,{children:t(E,{sx:{width:"100%"},children:t(be,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:t(d,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:[...e[1]].map(n=>(console.log("inneritem",n),t(Ee,{activeTabIndex:h,fieldGroup:e[0],selectedRowData:r==null?void 0:r.glAccount,pcTabs:v,label:n.fieldName,value:n.value,length:n.maxLength,visibility:n.visibility,onSave:a=>handleFieldSave(n.fieldName,a),isEditMode:C,type:n.fieldType,field:n})))})})})})]})},i)))})]})]})]}),C?t(U,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:s(K,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:L,onChange:e=>{$(e)},children:[t(f,{size:"small",sx:{...A,mr:1},variant:"contained",onClick:()=>{O("/masterDataCockpit/generalLedger/createMultipleGL")},children:"Save"}),t(f,{variant:"contained",size:"small",sx:{...A,mr:1},onClick:q,disabled:h===0,children:"Back"}),t(f,{variant:"contained",size:"small",sx:{...A,mr:1},onClick:W,disabled:h===v.length-1,children:"Next"})]})}):"",C?"":t(U,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:s(K,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:L,onChange:e=>{$(e)},children:[t(f,{variant:"contained",size:"small",sx:{...A,mr:1},onClick:q,disabled:h===0,children:"Back"}),t(f,{variant:"contained",size:"small",sx:{...A,mr:1},onClick:W,disabled:h===v.length-1,children:"Next"})]})})]})};export{Ke as default};
