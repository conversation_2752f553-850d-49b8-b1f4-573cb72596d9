import {
    Box,
    Grid,
    Typography,
  } from "@mui/material";
  import React, { useEffect, useState } from "react";
  import { container_Padding, container_columnGap } from "../../Common/commonStyles";
  import FilterField from "../../Common/ReusableFilterBox/FilterField";
import FilterFieldTypeCC from "../CostCenter/FilterFieldTypeCC";
  
  const CommunicationCostCenter = (props) => {
    let filterFields = Object?.entries(props?.communicationTabDetails);
    console.log("basic", filterFields);
    const [communicationJsx, setCommunicationJsx] = useState([]);
   
    useEffect(() => {
        setCommunicationJsx(
        filterFields?.map((item) => {
          return (
            <Grid
              item
              md={12}
              sx={{
                backgroundColor: "white",
                maxHeight: "max-content",
                height: "max-content",
                borderRadius: "8px",
                border: "1px solid #E0E0E0",
                mt: 0.25,
                boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                ...container_Padding,
                // ...container_columnGap,
              }}
            >
              <Grid container>
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                  }}
                >
                  {item[0]}
                </Typography>
              </Grid>
              <Box>
                <Grid container spacing={1}>
                  {[...item[1]]
                    .filter((x) => x.visibility != "Hidden")
                    .sort((a, b) => a.sequenceNo - b.sequenceNo)
                    .map((innerItem) => {
                      return (
                        <FilterFieldTypeCC
                          field={innerItem}
                          dropDownData={props.dropDownData}
                        />
                      );
                    })}
                </Grid>
              </Box>
            </Grid>
          );
        })
      );
    }, [props?.communicationTabDetails]);
  
    return <>{communicationJsx}</>;
  };
  
  export default CommunicationCostCenter;
  