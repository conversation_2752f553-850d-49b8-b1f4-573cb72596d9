import { doAjax } from '@components/Common/fetchService';
import { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { destination_IDM } from '../destinationVariables';
import { END_POINTS } from '@constant/apiEndPoints';
import { updateFilteredButtons } from '../app/payloadslice';
import useLogger from "./useLogger";
import { getLocalStorage } from '@helper/helper';
import { LOCAL_STORAGE_KEYS } from '@constant/enum';

const useButtonDTConfig = (config = {}) => {
    const { customError } = useLogger();
    const [buttonsIDM, setButtonsIDM] = useState([]);
    const taskData = useSelector((state) => state.userManagement.taskData);
    console.log("taskdata",taskData)
    const [filteredButtons, setFilteredButtons] = useState([]);
    const applicationConfig = useSelector((state) => state.applicationConfig);
    const dispatch = useDispatch();

    let buttonPriority = {
      handleSubmitForApproval: 6,
      handleSendBack: 1,
      handleReject: 3,
      handleValidate: 5,
      handleSAPSyndication: 8,
      handleIdGenerator: 4,
      handleSubmitForReview: 7,
      handleCorrection: 2,
    };

    const savedTask = getLocalStorage(LOCAL_STORAGE_KEYS.CURRENT_TASK, true, {});

    useEffect(() => {
      const effectiveTaskDesc = taskData?.taskDesc || savedTask?.taskDesc;
        const filtered = buttonsIDM?.filter(
            (button) => button.MDG_DYN_BTN_TASK_NAME === effectiveTaskDesc
        );
        const sortedButtonArr = filtered?.sort((a, b) => {
            const priorityA = buttonPriority[a.MDG_DYN_BTN_ACTION_TYPE];
            const priorityB = buttonPriority[b.MDG_DYN_BTN_ACTION_TYPE];
            return priorityA - priorityB;
        });
        setFilteredButtons(sortedButtonArr);
        dispatch(updateFilteredButtons(sortedButtonArr))
    }, [buttonsIDM]);

    const getButtonsDisplayGlobal = (module, dtName, version) => {
        let payload = {
          decisionTableId: null,
          decisionTableName: dtName ?? "MDG_MAT_DYN_BUTTON_CONFIG",
          version: version ?? "v3",
          rulePolicy: null,
          validityDate: null,
          conditions: [
            {
              "MDG_CONDITIONS.MDG_DYN_BTN_MODULE_NAME": module,
              "MDG_CONDITIONS.MDG_DYN_BTN_REQUEST_TYPE": taskData?.ATTRIBUTE_2 || savedTask?.ATTRIBUTE_2,
            },
          ],
          systemFilters: null,
          systemOrders: null,
          filterString: null,
        };
    
        const hSuccess = (data) => {
          if (data.statusCode === 200) {
            let responseData = data?.data?.result[0]?.MDG_DYN_BTN_ACTION_TYPE;
            setButtonsIDM(responseData);
          }
        };
    
        const hError = (error) => {
           customError(error);
        };
    
        if (applicationConfig.environment === "localhost") {
          doAjax(`/${destination_IDM}${END_POINTS.INVOKE_RULES.LOCAL}`, "post", hSuccess, hError, payload);
        } else {
          doAjax(`/${destination_IDM}${END_POINTS.INVOKE_RULES.PROD}`, "post", hSuccess, hError, payload);
        }

      };
    
    return { getButtonsDisplayGlobal };
};

export default useButtonDTConfig;