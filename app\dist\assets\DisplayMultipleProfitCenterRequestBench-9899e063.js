import{b as Fe,s as Ne,r as c,q as h,u as _e,cd as $e,j as a,G as s,al as De,a as o,z as ze,T as m,B as T,x as se,b4 as Le,br as Ve,aE as ce,t as C,aB as w,aD as de,cn as F,K as pe,c0 as ue,bp as Ie,I as Pe,b1 as We,b8 as he,ai as ge}from"./index-75c1660a.js";import{d as Oe}from"./EditOutlined-6971b85d.js";import{d as Ge}from"./ArrowCircleLeftOutlined-f7b52d40.js";import{C as He,d as Ke}from"./ChangeLog-54fbad38.js";import"./dayjs.min-83c0b0e0.js";import{E as Je}from"./EditFieldForMassProfitCenter-3473a42b.js";import{C as Qe}from"./CompCodeProfitCenter-d03b994f.js";import{a as Re,b as Ue,S as Xe}from"./Stepper-2dbfb76b.js";import"./DatePicker-31fef6b6.js";import"./dateViewRenderers-dbe02df3.js";import"./useSlotProps-da724f1f.js";import"./InputAdornment-a22e1655.js";import"./CSSTransition-8d766865.js";import"./useMediaQuery-33e0a836.js";import"./DesktopDatePicker-47a97548.js";import"./useMobilePicker-056b38fc.js";const gt=()=>{var R,U,X,Y,Z,k,ee,te,oe,ne,le,ie,re;const V=Fe(),f=Ne();c.useState({});const[I,P]=c.useState(0);c.useState([]);const[b,fe]=c.useState(!1),[Ye,me]=c.useState(!0);c.useState([]),c.useState([]);const[Ce,xe]=c.useState([]),[g,B]=c.useState(0);c.useState(),h(e=>e.tabsData);const[be,W]=c.useState(!1),[O,ye]=c.useState([]),[ve,G]=c.useState(!1),N=_e(),y=h(e=>e.profitCenter.MultipleProfitCenterRequestBench),u=N.state.tabsData,i=N.state.rowData,l=N.state.requestbenchRowData;let d=h(e=>{var n;return(n=e==null?void 0:e.initialData)==null?void 0:n.IWMMyTask});console.log(d,"taskData_in_mass"),console.log(l,"========massProfitRowData==========="),console.log("selectedrowdata",i,u);const q=u.viewData["Comp Codes"]["Company Code Assignment for Profit Center"];console.log("commpcodedata",q),h(e=>e.payload);let $=h(e=>e.edit.payload);console.log($,"singlePCPayloadAfterChange");let D=h(e=>e.profitCenter.requiredFields);console.log(D,"required_field_for_data");const Se=h(e=>e.profitCenter.profitCenterCompCodes);let t=h(e=>e.userManagement.taskData),Ae=h(e=>e.profitCenter.MultipleProfitCenterData),z=h(e=>e.userManagement.userData);console.log(Ae,"profitCenterMultiple"),console.log(t,"task_in_mass========================="),console.log(t==null?void 0:t.processDesc,"task?.processDesc"),console.log(t==null?void 0:t.subject,"task?.subject"),console.log(l==null?void 0:l.requestId,"massProfitRowData?.requestId"),console.log(l==null?void 0:l.requestType,"massProfitRowData?.requestType");let x="",v="",S="";(t==null?void 0:t.processDesc)==="Mass Change"?(x=t!=null&&t.subject?(R=t==null?void 0:t.subject)==null?void 0:R.slice(3):l==null?void 0:l.requestId.slice(3),v=(U=d==null?void 0:d.body)!=null&&U.controllingArea?"":i.controllingArea,S=(X=t==null?void 0:t.body)!=null&&X.profitCenter?"":i.profitCenter):(t==null?void 0:t.processDesc)==="Mass Create"?(x=t!=null&&t.subject?(Y=t==null?void 0:t.subject)==null?void 0:Y.slice(3):l==null?void 0:l.requestId.slice(3),v=(Z=d==null?void 0:d.body)!=null&&Z.controllingArea?"":i.controllingArea,S=(k=t==null?void 0:t.body)!=null&&k.profitCenter?"":i.profitCenter):(l==null?void 0:l.requestType)==="Mass Create"?(x=t!=null&&t.subject?(ee=t==null?void 0:t.subject)==null?void 0:ee.slice(3):l==null?void 0:l.requestId.slice(3),v=(te=d==null?void 0:d.body)!=null&&te.controllingArea?"":i.controllingArea,S=(oe=t==null?void 0:t.body)!=null&&oe.profitCenter?"":i.profitCenter):(l==null?void 0:l.requestType)==="Mass Change"&&(x=t!=null&&t.subject?(ne=t==null?void 0:t.subject)==null?void 0:ne.slice(3):l==null?void 0:l.requestId.slice(3),v=(le=d==null?void 0:d.body)!=null&&le.controllingArea?"":i.controllingArea,S=(ie=t==null?void 0:t.body)!=null&&ie.profitCenter?"":i.profitCenter);for(let e=0;e<(y==null?void 0:y.length);e++)if(y[e].profitCenter===i.profitCenter){y[e];break}const je=(e,n)=>{setActiveTab(n)},H=()=>{const e=Q();b?e?(B(n=>n-1),f(F())):J():(B(n=>n-1),f(F()))},K=()=>{const e=Q();b?e?(B(n=>n+1),f(F())):J():(B(n=>n+1),f(F()))},J=()=>{G(!0)},M=Object.entries(u==null?void 0:u.viewData).filter(e=>typeof e[1]=="object"&&e[1]!=null).map(e=>e[0]),A=Object.entries(u.viewData).filter(e=>typeof e[1]=="object"&&e[1]!=null).map(e=>Object.entries(e[1])),L={};A.map(e=>{e.forEach((n,r)=>{n.forEach((p,E)=>{E!==0&&p.forEach(j=>{L[j.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("")]=j.value})})})}),console.log("tabcontentsnow",A,L);const Be=()=>{fe(!0),me(!1)},qe=e=>{W(e)},Me=()=>{W(!0)},Ee=()=>{const e=r=>{f(ge({keyName:"ProfitCtrGroup",data:r.body}))},n=r=>{console.log(r)};pe(`/${ue}/data/getProfitCtrGroup?controllingArea=${i.controllingArea}`,"get",e,n)},Te=e=>{console.log("compcode",e);const n=p=>{console.log("value",p),f(ge({keyName:"Region",data:p.body}))},r=p=>{console.log(p,"error in dojax")};pe(`/${ue}/data/getRegionBasedOnCountry?country=${e}`,"get",n,r)};c.useEffect(()=>{f($e(L))},[]),c.useEffect(()=>{var e;xe(_.zip(q[0].value,q[1].value,q[2].value).map((n,r)=>{var p,E,j,ae;return{id:r,companyCodes:(p=n[0])!=null&&p.split("$$$")[0]?(E=n[0])==null?void 0:E.split("$$$")[0]:"",companyName:(j=n[1])!=null&&j.split("$$$")[0]?(ae=n[1])==null?void 0:ae.split("$$$")[0]:"",assigned:n[2]?n[2]:""}})),Ee(),Te((e=u==null?void 0:u.viewData.Address["Address Data"].find(n=>(n==null?void 0:n.fieldName)==="Country/Reg."))==null?void 0:e.value)},[]),console.log($,D,"requiredFieldTabWise");const Q=()=>Ie($,D,ye),we=()=>{G(!1)};return console.log(x,v,S,"================"),console.log("tabcontents",u),a("div",{children:[a(s,{container:!0,style:{...De,backgroundColor:"#FAFCFF"},children:[O.length!=0&&o(ze,{openSnackBar:ve,alertMsg:"Please fill the following Field: "+O.join(", "),handleSnackBarClose:we}),a(s,{sx:{width:"inherit"},children:[a(s,{item:!0,md:12,style:{padding:"16px",display:"flex"},children:[o(s,{style:{display:"flex",justifyContent:"flex-end"},children:o(Pe,{color:"primary","aria-label":"upload picture",component:"label",sx:We,children:o(Ge,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{V(-1)}})})}),a(s,{md:10,children:[o(m,{variant:"h3",children:a("strong",{children:["Multiple Profit Center : ",i.profitCenter," "]})}),o(m,{variant:"body2",color:"#777",children:"This view displays details of uploaded Profit Center"})]}),o(s,{md:1,sx:{display:"flex",justifyContent:"flex-end",marginRight:"4px"},children:o(C,{variant:"outlined",size:"small",sx:he,onClick:Me,title:"Chnage Log",children:o(Ke,{sx:{padding:"2px"},fontSize:"small"})})}),be&&o(He,{open:!0,closeModal:qe,requestId:x,requestType:"Mass",pageName:"profitCenter",controllingArea:i.controllingArea,centerName:i.profitCenter}),b?"":(z==null?void 0:z.role)==="Finance"?o(s,{md:1,sx:{display:"flex",justifyContent:"flex-end"},children:o(s,{item:!0,children:a(C,{variant:"outlined",size:"small",sx:he,onClick:Be,children:["Change",o(Oe,{sx:{padding:"2px"},fontSize:"small"})]})})}):""]}),o(s,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:a(T,{width:"70%",sx:{marginLeft:"40px"},children:[o(s,{item:!0,sx:{paddingTop:"2px !important"},children:a(se,{flexDirection:"row",children:[o("div",{style:{width:"15%"},children:o(m,{variant:"body2",color:"#777",children:"Profit Center"})}),a(m,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[": ",i.profitCenter]})]})}),o(s,{item:!0,sx:{paddingTop:"2px !important"},children:a(se,{flexDirection:"row",children:[o("div",{style:{width:"15%"},children:o(m,{variant:"body2",color:"#777",children:"Controlling Area"})}),a(m,{variant:"body2",fontWeight:"bold",children:[": ",i.controllingArea]})]})})]})}),a(s,{container:!0,style:{padding:"16px"},children:[o(Xe,{activeStep:g,onChange:je,variant:"scrollable",sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},"aria-label":"mui tabs example",children:M.map((e,n)=>o(Re,{children:o(Ue,{sx:{fontWeight:"700"},children:e})},e))}),o(s,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:A&&((re=A[g])==null?void 0:re.map((e,n)=>g===2?o(Qe,{compCodesTabDetails:Se,displayCompCode:Ce}):o(T,{sx:{width:"100%"},children:a(s,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Le},children:[o(s,{container:!0,children:o(m,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:e[0]})}),o(T,{children:o(T,{sx:{width:"100%"},children:o(Ve,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:o(s,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:[...e[1]].map(r=>(console.log("inneritem",e[1]),o(Je,{activeTabIndex:g,fieldGroup:e[0],selectedRowData:i.profitCenter,pcTabs:M,label:r.fieldName,value:r.value,length:r.maxLength,visibility:r.visibility,onSave:p=>handleFieldSave(r.fieldName,p),isEditMode:b,type:r.fieldType,field:r})))})})})})]})},n)))},A)]})]})]}),b?o(de,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:a(ce,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:I,onChange:e=>{P(e)},children:[o(C,{size:"small",variant:"contained",onClick:()=>{V(-1)},children:"Save"}),o(C,{variant:"contained",size:"small",sx:{...w,mr:1},onClick:H,disabled:g===0,children:"Back"}),o(C,{variant:"contained",size:"small",sx:{...w,mr:1},onClick:K,disabled:g===M.length-1,children:"Next"})]})}):o(de,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:a(ce,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:I,onChange:e=>{P(e)},children:[o(C,{variant:"contained",size:"small",sx:{...w,mr:1},onClick:H,disabled:g===0,children:"Back"}),o(C,{variant:"contained",size:"small",sx:{...w,mr:1},onClick:K,disabled:g===M.length-1,children:"Next"})]})})]})};export{gt as default};
