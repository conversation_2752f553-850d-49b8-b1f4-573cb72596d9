const {
    VITE_BASE_URL_ITM_JAVA_SERVICES,
    VITE_BASE_URL_MESSAGING_SERVICES,
    VITE_BASE_URL_CRUD_SERVICES,
    VITE_BASE_URL_IWASCP_SERVICES,
    VITE_URL_MATERIAL_MGMT,
    VITE_URL_AI,
    VITE_URL_DOCUMENT_MANAGEMENT,
    VITE_URL_WEBSOCKET,
    VITE_URL_ADMIN,
} = import.meta.env;

let baseUrl_Notification = VITE_URL_WEBSOCKET
let baseUrl_Messaging = VITE_BASE_URL_MESSAGING_SERVICES
let baseUrl_CrudService = VITE_BASE_URL_CRUD_SERVICES
let baseUrl_IWASCP = VITE_BASE_URL_IWASCP_SERVICES
let baseUrl_DocumentManagement = VITE_URL_DOCUMENT_MANAGEMENT
let baseUrl_ITMJava = VITE_BASE_URL_ITM_JAVA_SERVICES
let baseUrl_MaterialManagement = VITE_URL_MATERIAL_MGMT
let baseUrl_Websocket = VITE_URL_WEBSOCKET
let baseUrl_AI = VITE_URL_AI
let baseUrl_Admin = VITE_URL_ADMIN
export {
    baseUrl_Notification,
    baseUrl_Messaging,
    baseUrl_CrudService,
    baseUrl_IWASCP,
    baseUrl_DocumentManagement,
    baseUrl_MaterialManagement,
    baseUrl_ITMJava,
    baseUrl_Admin,
    baseUrl_Websocket,
    baseUrl_AI
}