import{r as v,q as $,s as V,f4 as ee,a as r,x as te,j as A,T as E,b6 as ae,E as B,at as oe,au as re,G as K,ay as G,F as j,ab as se,bI as N,eX as ce,K as W,b$ as X,ai as _}from"./index-75c1660a.js";import{D as ne}from"./DatePicker-31fef6b6.js";function le(a,n){return Array.isArray(n)&&n.find(R=>R.code===a)||""}const pe=({label:a,value:n,length:M,units:R,onSave:de,fieldGroup:l,isEditMode:H,activeTabIndex:O,visibility:d,isExtendMode:ie,pcTabs:P,selectedRowData:q,options:ge=[],type:m})=>{var I,T,U;const[i,C]=v.useState(n),[he,J]=v.useState(!1),y=$(e=>e.AllDropDown.dropDown),c=$(e=>e.generalLedger.MultipleGLData),g=V();le(i,y),$(e=>e.edit.payload);let z={},k=-1;for(let e=0;e<(c==null?void 0:c.length);e++)if(((I=c[e])==null?void 0:I.GLAccount)===q){z=c[e],k=e;break}console.log("selectedrowdata",q,c);let h=P[O];console.log("activerow",k,z,h);const D=(e,t)=>{const s=e==null?void 0:e.find(o=>(o==null?void 0:o.fieldName)===t);return s?s.value:""},p=c[k];let u=a.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("");v.useEffect(()=>{C(n)},[n]),v.useEffect(()=>{(d==="0"||d==="Required")&&(console.log(d,"visibility"),g(ee(u)))},[h]);const S={label:a,value:i,units:R,type:m};console.log("fieldData",S,p);const f=(e,t)=>{console.log("fieldGroup",l,e,t),g(N({keyname:u.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""),data:t}));let s=c==null?void 0:c.map((o,w)=>{let F=P[O];if(w===k){let b=o.viewData,Y=o.viewData[F];console.log("temp",Y);let x=o.viewData[F][l];return console.log("temp2",x),{...o,viewData:{...b,[F]:{...Y,[l]:x==null?void 0:x.map(L=>L.fieldName===e?{...L,value:t}:L)}}}}else return o});console.log("changedData",s),g(ce(s))},Q=e=>{console.log("compcode",e);const t=o=>{console.log("value",o),g(N({keyname:"Currency",data:""})),g(_({keyName:"Currency",data:o.body}))},s=o=>{console.log(o,"error in dojax")};W(`/${X}/data/getCurrency?companyCode=${e==null?void 0:e.code}`,"get",t,s)},Z=e=>{console.log("countryyyyy",e);const t=o=>{console.log("value",o),g(N({keyname:"Region",data:""})),g(_({keyName:"Region",data:o.body}))},s=o=>{console.log(o,"error in dojax")};W(`/${X}/data/getRegionBasedOnCountry?country=${e==null?void 0:e.code}`,"get",t,s)};return v.useEffect(()=>{(a==="Key Of Last Interest Calc"||a==="Date Of Last Interest Run")&&C(parseInt(n.replace("/Date(","").replace(")/","")))},[n]),console.log("editedValue[key] ",y[u]),console.log("editedValue[key] ",i),r(K,{item:!0,children:r(te,{children:H?A(j,{children:[A(E,{variant:"body2",color:"#777",children:[a," ",d==="Required"||d==="0"?r("span",{style:{color:"red"},children:"*"}):""]}),m==="Drop Down"?r(ae,{options:y[u]??[],value:D(p.viewData[h][l],a)&&((T=y[u])==null?void 0:T.filter(e=>e.code===D(p.viewData[h][l],a)))&&((U=y[u])==null?void 0:U.filter(e=>e.code===D(p.viewData[h][l],a))[0])||"",onChange:(e,t)=>{a==="Comp Code"&&Q(t),a==="Country/Reg"&&Z(t),f(a,t==null?void 0:t.code),console.log("newValue",t),C(t.code),J(!0),console.log("keys",u)},getOptionLabel:e=>(console.log("optionn",e),e===""||(e==null?void 0:e.code)===""?"":`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??""),renderOption:(e,t)=>(console.log("option vakue",t),r("li",{...e,children:r(E,{style:{fontSize:12},children:`${t==null?void 0:t.code} - ${t==null?void 0:t.desc}`})})),renderInput:e=>r(B,{...e,variant:"outlined",placeholder:`Select ${S.label}`,size:"small",label:null})}):m==="Input"?r(B,{variant:"outlined",size:"small",value:D(p.viewData[h][l],a).toUpperCase(),placeholder:`ENTER ${S.label.toUpperCase()}`,inputProps:{maxLength:M},onChange:e=>{console.log("event",e.target.value);const t=e.target.value;if(t.length>0&&t[0]===" ")f(a,t.trimStart());else{let s=t.toUpperCase();f(a,s)}}}):m==="Calendar"?r(oe,{dateAdapter:re,children:r(ne,{slotProps:{textField:{size:"small"}},value:i,placeholder:"Select Date Range",onChange:e=>{f(e),C(e)}})}):m==="Radio Button"?r(K,{item:!0,md:2,children:r(G,{sx:{padding:0},checked:D(p.viewData[h][l],a)==!0,onChange:e=>{console.log("oncheckbox",a,e.target.checked),f(a,e.target.checked)}})}):""]}):r(j,{children:A(j,{children:[A(E,{variant:"body2",color:"#777",children:[a," ",d==="Required"||d==="0"?r("span",{style:{color:"red"},children:"*"}):""]}),A(E,{variant:"body2",fontWeight:"bold",children:[a==="Analysis Period From"||a==="Analysis Period To"?se(i).format("DD MMM YYYY"):i,m==="Radio Button"?r(G,{sx:{padding:0},checked:i,disabled:!0}):""]})]})})})})};export{pe as E};
