import { Box, Grid, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import {
  container_Padding,
  container_columnGap,
} from "../../../Common/commonStyles";
import FilterFieldTypeGL from "../FilterFieldTypeGL";
const ControlDataTab = (props) => {
  let filterFields = Object?.entries(props?.controlDataTabDetails);
  const [controlDataJsx, setControlDataJsx] = useState([]);

  useEffect(() => {
    setControlDataJsx(
      filterFields?.map((item) => {
        return (
          <Grid
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              borderRadius: "8px",
              border: "1px solid #E0E0E0",
              mt: 0.25,
              boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
              ...container_Padding,
            }}
          >
            <Grid container>
              <Typography
                sx={{
                  fontSize: "12px",
                  fontWeight: "700",
                }}
              >
                {item[0]}
              </Typography>
            </Grid>
            <Box>
              <Grid container spacing={1}>
                {[...item[1]]
                  ?.filter((x) => x?.visibility != "Hidden")
                  ?.sort((a, b) => a?.sequenceNo - b?.sequenceNo)
                  ?.map((innerItem) => {
                    return (
                      <FilterFieldTypeGL
                        field={innerItem}
                        dropDownData={props?.dropDownData}
                        country={props.country}
                      />
                    );
                  })}
              </Grid>
            </Box>
          </Grid>
        );
      })
    );
  }, [props?.basicDataTabDetails]);
  return <>{controlDataJsx}</>;
};

export default ControlDataTab;
