import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  dropDown: {},
  isOdataApiCalled:false,
};

export const generalLedgerDropDownSlice = createSlice({
  name: "costCenterAllDropDown",
  initialState,
  reducers: {
    setDropDown: (state, action) => {
      state.dropDown[action.payload.keyName] = action.payload.data;
    },
    setOdataApiCall:(state,action) => {
      state.isOdataApiCalled = action.payload;
    }
  },
});

export const { setDropDown, setDependentDropdown, setOdataApiCall } = generalLedgerDropDownSlice.actions;

export default generalLedgerDropDownSlice.reducer;
