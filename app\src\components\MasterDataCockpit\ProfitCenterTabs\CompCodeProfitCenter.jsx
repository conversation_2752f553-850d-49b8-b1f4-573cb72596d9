import { Box, Checkbox, Grid, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import { container_Padding } from "../../Common/commonStyles";
import ReusableTable from "../../Common/ReusableTable";
import { useDispatch, useSelector } from "react-redux";
import { setDropDown } from "../../../app/dropDownDataSlice";

const CompCodesProfitCenter = ({
  compCodesTabDetails = [],
  displayCompCode = [],
}) => {
  let filterFields = Object?.entries(compCodesTabDetails);
  // console.log("basic", filterFields);
  const [compcodejsx, setCompCodeJsx] = useState([]);
  const dispatch = useDispatch();
  const compCodesDropdown = useSelector(
    (state) => state.AllDropDown.dropDown.CompCodeBasedOnControllingArea ?? []
  );

  console.log(
    compCodesTabDetails,
    displayCompCode,
    compCodesDropdown,
    "props1234"
  );
  // console.log('props.compcodes',props?.displayCompCode)
  // console.log("computer", props.displayCompCode.map((x)=>x.assigned==='true'))

  const columns = [
    {
      field: "id",
      headerName: "ID",
      type: "text",
      hide: "true",
      editable: "false",
    },
    {
      field: "companyCodes",
      headerName: "Company Codes",
      width: 350,
      editable: "false",
    },
    {
      field: "companyName",
      headerName: "Company Name",
      type: "text",
      editable: "false",
      width: 350,
    },
    {
      field: "assigned",
      headerName: "Assigned",
      width: 350,
      editable: "true",
      renderCell: (params) => {
        console.log("params", params);
        return (
          <Checkbox
            sx={{ padding: 0 }}
            // defaultChecked
            checked={
              // displayCompCode?.map((x) => x.assigned === "X") ||
              params.row.assigned == "X"
            }
            // checked={true}
            onChange={(e) => {
              console.log("dddd", compCodesDropdown, e.target.checked);
              let count = 0;

              let rowsData = compCodesDropdown?.map((x) => {
                if (x.id === params.row.id) {
                  count++;
                  return { ...x, assigned: x.assigned == "" ? "X" : "" };
                }
                return x;
              });
              console.log("finalcount", count);
              dispatch(
                setDropDown({
                  keyName: "CompCodeBasedOnControllingArea",
                  data: rowsData,
                })
              );
            }}
          />
        );
      },
    },
  ];

  return (
    <>
      {filterFields?.map((item) => {
        return (
          <Grid
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              borderRadius: "8px",
              border: "1px solid #E0E0E0",
              mt: 0.25,
              boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
              ...container_Padding,
              // ...container_columnGap,
            }}
          >
            <Grid container>
              <Typography
                sx={{
                  fontSize: "12px",
                  fontWeight: "700",
                }}
              >
                {item[0]}
              </Typography>
            </Grid>
            <Box>
              <ReusableTable
                width="100%"
                rows={
                  displayCompCode[0] ? displayCompCode : compCodesDropdown ?? []
                }
                columns={columns}
                getRowIdValue={"id"}
                hideFooter={true}
                checkboxSelection={false}
                // experimentalFeatures={{ newEditingApi: true }}
              />
            </Box>
          </Grid>
        );
      })}
    </>
  );
};

export default CompCodesProfitCenter;
