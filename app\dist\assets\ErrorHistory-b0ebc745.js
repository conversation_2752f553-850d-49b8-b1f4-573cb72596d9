import{m as Te,n as Pe,o as ke,r as a,u as se,bQ as ie,K as q,Z as j,$ as G,j as n,a as e,aD as ve,bL as le,bM as ae,bN as y,bO as u,bP as Ie,bF as ce,w5 as de,h as B,T as p,a5 as he,bK as pe,o4 as ue,B as k,a0 as $e,s as Oe,b as _e,aj as Ne,l,x as P,G as M,al as Me,bY as Be,I as H,w6 as Le,b_ as ze,cl as We,bZ as Ae,f9 as De,bs as ee,br as re,bX as He,F as qe,t as oe,c1 as je,a$ as Ge}from"./index-17b8d91e.js";import{d as Fe}from"./Description-22845efe.js";import{a as Ue,T as te}from"./ToggleButtonGroup-cf875764.js";var F={},Ye=Pe;Object.defineProperty(F,"__esModule",{value:!0});var ge=F.default=void 0,Je=Ye(Te()),Ke=ke;ge=F.default=(0,Je.default)((0,Ke.jsx)("path",{d:"M16.59 7.58 10 14.17l-3.59-3.58L5 12l5 5 8-8zM12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8"}),"CheckCircleOutline");const ne=b=>(b.includes("Duplicate"),"error"),Ve=()=>{const[b,h]=a.useState(0),[R,L]=a.useState(5),[f,T]=a.useState([]),U=se(),I=new URLSearchParams(U.search.split("?")[1]).get("RequestId"),[g,$]=a.useState(""),{customError:C}=ie(),O=(s,m)=>h(m),z=s=>{L(parseInt(s.target.value,10)),h(0)},x=f==null?void 0:f.slice(b*R,b*R+R);a.useEffect(()=>{W()},[]);const W=()=>{const s=i=>{(i==null?void 0:i.statusCode)===$e.STATUS_200?T(i==null?void 0:i.body):$(i==null?void 0:i.message)},m=i=>{C(i),$(i==null?void 0:i.message)};q(`/${j}/${G.ERROR_HISTORY.EXCEL_ERROR_HISTORY}?requestId=${I.slice(3)}`,"get",s,m)};return n(k,{sx:{mt:2,display:"flex",flexDirection:"column",height:"100%"},children:[e(pe,{component:ve,elevation:3,sx:{borderRadius:2,maxHeight:"56vh",overflow:"auto"},children:n(le,{stickyHeader:!0,children:[e(ae,{children:e(y,{children:["Sheet Name","Error Type","Row","Column","Error Details"].map(s=>e(u,{sx:{minWidth:"150px",fontWeight:"bold"},children:s},s))})}),e(Ie,{children:(x==null?void 0:x.length)>0?x==null?void 0:x.map((s,m)=>n(y,{children:[e(u,{children:s.sheetName}),e(u,{children:e(ce,{icon:ne(s.errorType)==="error"?e(de,{fontSize:"small",sx:{color:"#dee3e2 !important"}}):e(ge,{fontSize:"small",sx:{color:"#dee3e2 !important"}}),label:s.errorType,color:ne(s.errorType),size:"small",sx:{fontSize:"0.65rem",fontWeight:500,height:27,borderRadius:"99px",color:"#dee3e2 !important",p:.3}})}),e(u,{children:s.rowNumber}),e(u,{children:s.columnNumber}),e(u,{children:e(B,{title:s.details,arrow:!0,children:e(p,{variant:"body2",Wrap:!0,sx:{flex:1,overflow:"hidden",textOverflow:"ellipsis"},children:s.errorDetails})})})]},m)):e(y,{children:e(u,{colSpan:5,align:"center",children:g||he.NO_ERROR_FOUND})})})]})}),e(ue,{rowsPerPageOptions:[5,10,25],count:(f==null?void 0:f.length)??0,rowsPerPage:R,page:b,onPageChange:O,onRowsPerPageChange:z,sx:{mt:1}})]})},er=()=>{var X,Z;const{customError:b}=ie(),[h,R]=a.useState("sap"),L=(r,o)=>{o!==null&&R(o)},f=Oe(),T=se(),v=new URLSearchParams(T.search.split("?")[1]).get("RequestId"),I=_e(),[g,$]=a.useState([]),[C,O]=a.useState([]),[z,x]=a.useState(!1),W=((X=T.state)==null?void 0:X.display)??!1,s=((Z=T.state)==null?void 0:Z.childRequest)??!1,m=()=>{const r=t=>{var c;$(t==null?void 0:t.body),O(t==null?void 0:t.body),ye((t==null?void 0:t.count)??((c=t==null?void 0:t.body)==null?void 0:c.length)),x(!1),f(Ge({module:"ErrorHistory"}))},o=t=>{b(t),x(!1)};x(!0),s?q(`/${j}/${G.ERROR_HISTORY.ERROR_LOG_CHILD}?childRequestId=${v.slice(3)}`,"get",r,o):q(`/${j}/${G.ERROR_HISTORY.ERROR_LOG}?requestId=${v.slice(3)}`,"get",r,o)},[i,xe]=a.useState({}),me=r=>{xe(o=>({...o,[r]:!o[r]}))};a.useEffect(()=>{m()},[]);const A=r=>{let o=r==null?void 0:r.sapMessage;if(!o&&(r!=null&&r.materialDuplicateError))if(r.materialDuplicateError.includes("$^$")){const[w,N]=r.materialDuplicateError.split("$^$");o=`${w} - This material is already being processed in request ${N}`}else o=r.materialDuplicateError;if(!o&&!(r!=null&&r.materialDuplicateError))return null;const t=typeof o=="string"&&/<[a-z][\s\S]*>/i.test(o),c=o==="Validated Successfully"||o==="Syndicated In SAP",d=r==null?void 0:r.materialNo;return e(ee,{variant:"outlined",sx:{width:"100%",minHeight:100,backgroundColor:`${l.basic.lighterGrey}`,borderRadius:2,boxShadow:"0px 2px 6px rgba(0, 0, 0, 0.08)",padding:1,margin:"10px 0",overflow:"hidden"},children:n(re,{sx:{padding:1},children:[n(k,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1.5,children:[n(p,{fontWeight:"bold",color:c?"success.main":"error.main",sx:{fontSize:"0.9rem"},children:["Material No. ",d]}),e(ce,{label:c?"Success":"Error",color:c?"success":"error",size:"small",sx:{fontWeight:"medium",height:"21px",fontSize:"0.65rem"}})]}),o?n(k,{sx:{mt:1,position:"relative"},children:[t?e("div",{dangerouslySetInnerHTML:{__html:o},style:{fontSize:"0.75rem",lineHeight:1.1,wordBreak:"break-word",overflowWrap:"break-word",width:"100%",overflow:"hidden",display:"-webkit-box",WebkitLineClamp:i[d]?"unset":1,WebkitBoxOrient:"vertical",textOverflow:"ellipsis",whiteSpace:"normal",mb:2}}):e(p,{variant:"body2",color:"text.secondary",sx:{fontSize:"0.75rem",lineHeight:1.1,wordBreak:"break-word",overflowWrap:"break-word",width:"100%",overflow:"hidden",display:"-webkit-box",WebkitLineClamp:i[d]?"unset":1,WebkitBoxOrient:"vertical",textOverflow:"ellipsis",whiteSpace:"normal",mb:2},children:o}),e(oe,{size:"small",onClick:()=>me(d),sx:{fontSize:"0.75rem",minWidth:"auto",textTransform:"none",color:"primary.main",position:"absolute",bottom:"-30px",left:"-10px"},children:i[d]?"Show Less":"Show More"})]}):e(p,{variant:"body2",color:"text.secondary",sx:{fontSize:"0.875rem",mt:1},children:"No Error Found!"})]})})},fe=r=>{if(r){const o=r.toString().toLowerCase(),t=g.filter(c=>{var d;return(d=c==null?void 0:c.materialNo)==null?void 0:d.toString().toLowerCase().includes(o)});O(t)}else m()},_=[{field:"sapMessage",headerName:"SAP Error Log",align:"center",headerAlign:"center",flex:3,renderCell:r=>e(k,{sx:{width:"100%"},children:A(r==null?void 0:r.row)})}],be=r=>{Re(parseInt(r.target.value,10)),J(0)},we=(r,o)=>{J(o)},[Se,ye]=a.useState(0),[Y,J]=a.useState(0),[D,Re]=a.useState(10),K=Y*D,Ce=K+D,E=C==null?void 0:C.slice(K,Ce),Ee=()=>C.every(r=>!(r!=null&&r.sapMessage)&&!(r!=null&&r.materialDuplicateError)),V={convertJsonToExcel:()=>{let r=[];[{field:"materialNo",headerName:"Material Number"},{field:"sapMessage",headerName:"SAP Error Log"}].forEach(t=>{t.headerName.toLowerCase()!=="action"&&!t.hide&&r.push({header:t.headerName,key:t.field})}),je({fileName:"Material Error Logsheet",columns:r,rows:g})},button:()=>e(oe,{sx:{textTransform:"capitalize",position:"absolute",right:0,top:0},onClick:()=>V.convertJsonToExcel(),children:"Download"})};return e("div",{id:"container_outermost",children:e("div",{className:"purchaseOrder",style:{...Ne,backgroundColor:`${l.primary.veryLight}`},children:e(P,{spacing:1,children:n(qe,{children:[n(M,{container:!0,sx:Me,alignItems:"center",children:[e(M,{item:!0,md:6,sx:{display:"flex",alignItems:"center",...Be},children:n(P,{direction:"row",spacing:2,alignItems:"center",children:[e(H,{onClick:()=>{I(W?-1:"/requestBench")},color:"primary","aria-label":"upload picture",component:"label",children:e(Le,{sx:{fontSize:"25px",color:"#000000"}})}),n(P,{children:[e(p,{variant:"h3",children:n("strong",{children:["Error History - ",v]})}),e(p,{variant:"body2",color:"#777",children:"This view displays the error history of a Request"})]})]})}),(g==null?void 0:g.length)>0&&e(M,{item:!0,md:6,sx:{display:"flex",justifyContent:"flex-end",alignItems:"center"},children:n(M,{container:!0,direction:"row",justifyContent:"flex-end",alignItems:"center",spacing:0,marginBottom:"10px",children:[e(B,{slotProps:{tooltip:{sx:{fontSize:"0.9em"}}},title:"Reload",placement:"bottom",arrow:!0,children:e(H,{onClick:m,children:e(ze,{})})}),e(B,{slotProps:{tooltip:{sx:{fontSize:"0.9em"}}},title:"Export Table",placement:"bottom",arrow:!0,children:e(H,{onClick:V.convertJsonToExcel,children:e(We,{})})}),e(B,{slotProps:{tooltip:{sx:{fontSize:"0.9em"}}},title:"Search",children:e(Ae,{title:"Type Material Number to know its status",handleSearchAction:r=>fe(r),keyName:"errorHistorySearch",message:"Search",module:"ErrorHistory",clearSearchBar:()=>m()})})]})})]}),n(P,{sx:{padding:"16px",pb:"0 !important",width:"100%",maxWidth:"100%",...De},children:[n(Ue,{value:h,exclusive:!0,onChange:L,sx:{width:"40%","& .MuiToggleButton-root":{borderRadius:"0 !important"},"& .MuiToggleButton-root:first-of-type":{borderTopLeftRadius:"8px !important",borderBottomLeftRadius:"8px !important"},"& .MuiToggleButton-root:last-of-type":{borderTopRightRadius:"8px !important",borderBottomRightRadius:"8px !important"}},children:[n(te,{value:"sap",sx:{flex:1,p:1,color:h==="sap"?`${l.reportTile.blue}`:`${l.primary.grey}`,backgroundColor:h==="sap"?`${l.reportTile.lightBlue}`:"transparent","&:hover":{backgroundColor:`${l.reportTile.lightBlue}`}},children:[e(de,{sx:{fontSize:18,mr:1}}),"SAP Error Log"]}),n(te,{value:"excel",sx:{flex:1,p:1,color:h==="excel"?`${l.error.critical}`:`${l.primary.grey}`,backgroundColor:h==="excel"?`${l.reportTile.lightred}`:"transparent","&:hover":{backgroundColor:`${l.reportTile.lightred}`}},children:[e(Fe,{sx:{fontSize:18,mr:1}}),"Excel Upload Error"]})]}),h==="sap"&&(g==null?void 0:g.length)>0?n("div",{id:"container_outermost",style:{backgroundColor:`${l.basic.white}`,borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.1)",mt:2},children:[e(pe,{style:{marginTop:"16px",border:"1px solid #e0e0e0",borderRadius:"8px",height:"calc(100vh - 289px)",overflow:"auto",backgroundColor:`${l.basic.white}`},children:n(le,{stickyHeader:!0,children:[e(ae,{children:e(y,{children:_.map(r=>e(u,{style:{minWidth:r.minWidth,backgroundColor:`${l.background.default}`,padding:"12px 16px",borderBottom:"1px solid #e0e0e0",fontWeight:600,color:`${l.black.light}`},children:e("strong",{children:r.headerName})},r.field))})}),E.length>0?Ee()?e(y,{children:e(u,{colSpan:_.length,sx:{padding:"24px",textAlign:"center",borderBottom:"none",color:"#777"},children:e(p,{sx:{whiteSpace:"wrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:320,fontSize:"0.875rem",fontWeight:500},children:he.NO_DATA_AVAILABLE})})}):Array(Math.ceil(E.length/2)).fill().map((r,o)=>{const t=o*2,c=t+1,d=E[t],w=c<E.length?E[c]:null,N=A(d),Q=w?A(w):null;return!N&&!Q?null:e(y,{sx:{"&:hover":{backgroundColor:`${l.basic.lighterGrey}`},transition:"background-color 0.2s ease-in-out"},children:_.map(S=>e(u,{sx:{padding:"18px 18px",borderBottom:"1px solid #e0e0e0",color:`${l.black.light}`},children:n(k,{sx:{display:"flex",flexDirection:"row",justifyContent:E.length>=2?"space-evenly":"flex-start",gap:2},children:[N&&e(p,{sx:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",width:"100%",fontSize:"0.875rem",fontWeight:500},children:S.renderCell?S.renderCell({row:d}):d[S.field]}),Q&&w&&e(p,{sx:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",width:"100%",fontSize:"0.875rem",color:"#666"},children:S.renderCell?S.renderCell({row:w}):w[S.field]})]})},S.field))},t)}):e(y,{children:e(u,{colSpan:_.length,sx:{padding:"24px",textAlign:"center",borderBottom:"none",color:"#777"},children:e(p,{sx:{whiteSpace:"wrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:320,fontSize:"0.875rem",fontWeight:500},children:"No data found"})})})]})}),e(ue,{component:"div",count:Se,page:Y,onPageChange:we,rowsPerPage:D,onRowsPerPageChange:be,style:{borderTop:"1px solid #e0e0e0",backgroundColor:`${l.basic.white}`,padding:"8px 16px"}})]}):h!=="excel"&&e(ee,{sx:{width:"100%",marginTop:"16px",border:"1px solid #e0e0e0",borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.1)"},children:e(re,{style:{padding:"24px",mt:1},children:e(P,{spacing:2,alignItems:"center",children:e(p,{variant:"h5",style:{color:`${l.black.light}`,fontWeight:500},children:"No Data Found for this Request ID"})})})}),h==="excel"&&e(Ve,{})]}),z&&e(He,{})]})})})})};export{er as default};
