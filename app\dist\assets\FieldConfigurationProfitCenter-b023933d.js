import{b as Se,r as s,j as y,a as e,b3 as ye,z as Ce,am as xe,G as E,g as G,A as Y,T as O,f as Z,an as ke,aE as Fe,t as De,aD as ue,K as J,c0 as Q,ck as Ne,s as Te,F as fe,aj as je,ak as Ae,al as qe,bY as Ie,bZ as We,h as me,I as be,b1 as pe,b_ as He,cl as we,v as Le,w as Ve,B as _e,cm as ze}from"./index-75c1660a.js";import{R as ve}from"./ReusableFieldCatalog-2b30c987.js";const Pe=({})=>{const u=Se();s.useState("");const[C,d]=s.useState(null),[F,D]=s.useState([]),[f,R]=s.useState({}),[Me,X]=s.useState(null),[N,T]=s.useState({}),[ee,se]=s.useState({}),[x,j]=s.useState({}),[te,A]=s.useState(!1),[ae,ie]=s.useState(!1),[oe,q]=s.useState(!1),[I,W]=s.useState(""),[le,H]=s.useState(!1),[Ee,w]=s.useState(!1),[Re,L]=s.useState(!0),[ne,V]=s.useState(!1),[Be,B]=s.useState(!1),_=()=>{u("/masterDataCockpit/profitCenter")},ce=()=>{u("/masterDataCockpit/profitCenter")},de=()=>{setOpen(!1)},K=()=>{A(!1),u("/masterDataCockpit/profitCenter")},re=()=>{A(!0)},$=()=>{ie(!0)},ge=()=>{const r=b=>{const n=[],v=[];Object.keys(b.body).map(o=>{const t=b.body[o];Object.keys(t).map(p=>{const c=b.body[o][p];if(Array.isArray(c)){let M={heading:p,fields:c.map(l=>l.fieldName),viewName:o,fieldVisibility:c.map(l=>({fieldName:l.fieldName,visibility:l.visibility}))};n.push(M),console.log(n,"hello"),c.forEach(l=>{console.log("Field Name:",l.fieldName),console.log("Is Required:",l.Required),l.Required==="true"&&v.push(l.fieldName)})}})}),D(n),console.log("Required Fields:",v);const k={},a={},i={};n.forEach(o=>{const{heading:t,fields:p,viewName:c,fieldVisibility:M}=o;k[c]||(k[c]={heading:c,subheadings:[]}),k[c].subheadings.push({heading:t,fields:p}),M.forEach(l=>{let h=l.visibility==="Required"?"Mandatory":l.visibility==="Hidden"?"Hide":l.visibility==="0"?"0":"Optional";a[l.fieldName]=h,l.visibility==="0"&&(i[l.fieldName]=!0)})}),R(k),j(a),se(i),T(i),console.log(k,"Fieldset")},m=b=>{console.log(b)};J(`/${Q}/data/getFieldCatalogueDetails?screenName=Change`,"get",r,m)};s.useEffect(()=>{ge()},[]);const U=()=>{console.log("helloooo");let r={};Object.keys(f).forEach(n=>{f[n].subheadings.forEach(k=>{const{heading:a,fields:i}=k;i.forEach(o=>{if(x[o]!=="0"&&N[o]){const t=x[o]==="Mandatory"?"Required":x[o]==="Hide"?"Hidden":"Optional";r[n]||(r[n]=[]),r[n].some(c=>c.fieldName===o)||r[n].push({fieldName:o,cardName:a,viewName:n,visibility:t,screenName:"Change"})}})})});const m=n=>{console.log(n,"example"),B(),n.statusCode===200?(console.log("success"),q("Submit"),W("Field Catalog has been submitted successfully"),H("success"),L(!1),V(!0),re(),w(!0),B(!1)):(q("Submit"),V(!1),W("Submission Failed"),H("danger"),L(!1),w(!0),$(),B(!1)),de()},b=n=>{console.log(n)};Object.keys(r).forEach(n=>{const v=r[n];v.length>0?J(`/${Q}/alter/changeVisibility`,"post",m,b,v):console.log(`No payload data to send for viewName: ${n}`)}),dispatch(Ne())};return y("div",{children:[e(ye,{dialogState:ae,openReusableDialog:$,closeReusableDialog:_,dialogTitle:oe,dialogMessage:I,handleDialogConfirm:_,dialogOkText:"OK",handleExtraButton:ce,dialogSeverity:le}),ne&&e(Ce,{openSnackBar:te,alertMsg:I,handleSnackBarClose:K}),e(E,{container:!0,sx:xe,children:e(E,{item:!0,md:12,children:Object.keys(f).map(r=>y(G,{sx:{mb:2},className:"filter-accordion",children:[e(Y,{sx:{backgroundColor:"#f5f5f5"},children:e(O,{sx:{fontWeight:"700",margin:"0px !important"},children:r})}),e(Z,{children:f[r].subheadings.map((m,b)=>y(G,{sx:{mb:2},children:[e(Y,{expandIcon:e(ke,{}),sx:{backgroundColor:"#F1F0FF"},children:e(O,{sx:{fontWeight:"700",margin:"0px !important",fontSize:"14px"},children:m.heading})}),e(Z,{children:e("div",{sx:{fontSize:"25px"},children:e(ve,{fields:m.fields,heading:m.heading,childCheckedStates:N,setChildCheckedStates:T,childRadioValues:x,setChildRadioValues:j,onSubmitButtonClick:()=>U(),mandatoryFields:F,DisabledChildCheck:ee})})})]},b))})]},r))})}),e(ue,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:e(Fe,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},value:C,onChange:(r,m)=>{X(F[m]),d(m)},children:e(De,{size:"small",variant:"contained",onClick:U,children:"Submit"})})})]})},Ke=()=>{const u=document.getElementsByTagName("HTML")[0],C=document.getElementsByTagName("BODY")[0];let d=u.clientWidth,F=C.clientWidth;const D=document.getElementById("e-invoice-export"),f=D.scrollWidth-D.clientWidth;f>D.clientWidth&&(d+=f,F+=f),u.style.width=d+"px",C.style.width=F+"px",ze(D).then(R=>R.toDataURL("image/png",1)).then(R=>{$e(R,"FieldCatalog.png"),u.style.width=null,C.style.width=null})},$e=(u,C)=>{const d=window.document.createElement("a");d.href=u,d.download=C,(document.body||document.documentElement).appendChild(d),typeof d.click=="function"?d.click():(d.target="_blank",d.dispatchEvent(new MouseEvent("click",{view:window,bubbles:!0,cancelable:!0}))),URL.revokeObjectURL(d.href),d.remove()},Ye=({})=>{const u=Se();s.useState("");const[C,d]=s.useState(null),[F,D]=s.useState([]),[f,R]=s.useState({}),[Me,X]=s.useState(null),[N,T]=s.useState({}),[ee,se]=s.useState({}),[x,j]=s.useState({}),[te,A]=s.useState(!1),[ae,ie]=s.useState(!1),[oe,q]=s.useState(!1),[I,W]=s.useState(""),[le,H]=s.useState(!1),[Ee,w]=s.useState(!1),[Re,L]=s.useState(!0),[ne,V]=s.useState(!1),[Be,B]=s.useState(!1),[_,ce]=s.useState(0),de=["For Create","For Change"];Te();const K=()=>{u("/masterDataCockpit/materialMaster/createMaterialDetail")},re=()=>{u("/masterDataCockpit/materialMaster/createMaterialDetail")},$=()=>{setOpen(!1)},ge=()=>{A(!1),u("/masterDataCockpit/profitCenter")},U=()=>{A(!0)},r=()=>{ie(!0)},m=()=>{const a=o=>{const t=[],p=[];Object.keys(o.body).map(h=>{const z=o.body[h];Object.keys(z).map(P=>{const S=o.body[h][P];if(Array.isArray(S)){let he={heading:P,fields:S.map(g=>g.fieldName),viewName:h,fieldVisibility:S.map(g=>({fieldName:g.fieldName,visibility:g.visibility}))};t.push(he),console.log(t,"hello"),S.forEach(g=>{console.log("Field Name:",g.fieldName),console.log("Is Required:",g.Required),g.Required==="true"&&p.push(g.fieldName)})}})}),D(t),console.log("Required Fields:",p);const c={},M={},l={};t.forEach(h=>{const{heading:z,fields:P,viewName:S,fieldVisibility:he}=h;c[S]||(c[S]={heading:S,subheadings:[]}),c[S].subheadings.push({heading:z,fields:P}),he.forEach(g=>{let Oe=g.visibility==="Required"?"Mandatory":g.visibility==="Hidden"?"Hide":g.visibility==="0"?"0":"Optional";M[g.fieldName]=Oe,g.visibility==="0"&&(l[g.fieldName]=!0)})}),R(c),j(M),se(l),T(l),console.log(c,"Fieldset")},i=o=>{console.log(o)};J(`/${Q}/data/getFieldCatalogueDetails?screenName=Create`,"get",a,i)};s.useEffect(()=>{m()},[]);const b=()=>{let a={};Object.keys(f).forEach(t=>{f[t].subheadings.forEach(c=>{const{heading:M,fields:l}=c;l.forEach(h=>{if(x[h]!=="0"&&N[h]){const z=x[h]==="Mandatory"?"Required":x[h]==="Hide"?"Hidden":"Optional";a[t]||(a[t]=[]),a[t].some(S=>S.fieldName===h)||a[t].push({fieldName:h,cardName:M,viewName:t,visibility:z,screenName:"Create"})}})})});const i=t=>{console.log(t,"example"),B(),t.statusCode===200?(console.log("success"),q("Submit"),W("Field Catalog has been submitted successfully"),H("success"),L(!1),V(!0),U(),w(!0),B(!1)):(q("Submit"),V(!1),W("Submission Failed"),H("danger"),L(!1),w(!0),r(),B(!1)),$()},o=t=>{console.log(t)};Object.keys(a).forEach(t=>{const p=a[t];p.length>0?J(`/${Q}/alter/changeVisibility`,"post",i,o,p):console.log(`No payload data to send for viewName: ${t}`)})},n=[[e(fe,{children:y(E,{container:!0,sx:xe,children:[e(E,{item:!0,md:12,children:Object.keys(f).map(a=>y(G,{sx:{mb:2},className:"filter-accordion",children:[e(Y,{sx:{backgroundColor:"#f5f5f5"},children:e(O,{sx:{fontWeight:"700",margin:"0px !important"},children:a})}),e(Z,{children:f[a].subheadings.map((i,o)=>y(G,{sx:{mb:2},children:[e(Y,{expandIcon:e(ke,{}),sx:{backgroundColor:"#F1F0FF"},children:e(O,{sx:{fontWeight:"700",margin:"0px !important",fontSize:"14px"},children:i.heading})}),e(Z,{children:e("div",{sx:{fontSize:"25px"},children:e(ve,{fields:i.fields,heading:i.heading,childCheckedStates:N,setChildCheckedStates:T,childRadioValues:x,setChildRadioValues:j,onSubmitButtonClick:()=>b(),mandatoryFields:F,DisabledChildCheck:ee})})})]},o))})]},a))}),e(ue,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:e(Fe,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},value:C,onChange:(a,i)=>{X(F[i]),d(i)},children:e(De,{size:"small",variant:"contained",onClick:b,children:"Submit"})})})]})})],[e(fe,{children:e(Pe,{})})]],v=(a,i)=>{ce(i)};s.useState(""),s.useState([]);function k(){}return y("div",{children:[e(ye,{dialogState:ae,openReusableDialog:r,closeReusableDialog:K,dialogTitle:oe,dialogMessage:I,handleDialogConfirm:K,dialogOkText:"OK",handleExtraButton:re,dialogSeverity:le}),ne&&e(Ce,{openSnackBar:te,alertMsg:I,handleSnackBarClose:ge}),e("div",{style:{...je,backgroundColor:"#FAFCFF"},children:y(Ae,{spacing:1,children:[y(E,{container:!0,sx:qe,children:[y(E,{item:!0,md:5,sx:Ie,children:[e(O,{variant:"h3",children:e("strong",{children:"Field Configurations"})}),e(O,{variant:"body2",color:"#777",children:"This view displays the setiings for configuring the Fields"})]}),e(E,{item:!0,md:7,sx:{display:"flex"},children:y(E,{container:!0,direction:"row",justifyContent:"flex-end",alignItems:"center",spacing:0,children:[e(We,{title:"Search for fields in different views",module:"FieldSelection",keyName:"string",message:"Search for fields in different views"}),e(me,{title:"Reload",children:e(be,{sx:pe,children:e(He,{sx:{"&:hover":{transform:"rotate(360deg)",transition:"0.9s"}},onClick:k})})}),e(me,{title:"Export",children:e(be,{sx:pe,children:e(we,{onClick:Ke})})})]})})]}),e(ue,{children:e(Le,{value:_,onChange:v,variant:"scrollable",sx:{background:"#FFF",borderBottom:"1px solid #BDBDBD",width:"100%"},"aria-label":"mui tabs example",children:de.map((a,i)=>e(Ve,{sx:{fontSize:"12px",fontWeight:"700"},label:a},i))})}),n[_].map((a,i)=>e(_e,{children:a},i))]})})]})};export{Ye as default};
