import{o as e}from"./index-75c1660a.js";import{G as t,P as x}from"./Button-c2ace85e.js";const o=t(x,{shouldForwardProp:i=>i!=="customSize"})(({customSize:i})=>({display:"flex",alignItems:"center",color:"var(--text-secondary)",borderRadius:"4px",...i==="xsmall"&&{height:"20px",width:"20px",padding:"4px"},...i==="small"&&{width:"24px",height:"24px",padding:"4px"},...i==="medium"&&{height:"28px",width:"28px",padding:"5px"},...i==="large"&&{height:"32px",width:"32px",padding:"6px"},...i==="xlarge"&&{height:"36px",width:"36px",padding:"6px"},...i==="xxlarge"&&{height:"40px",width:"40px",padding:"6px"},"&: hover":{"& svg":{color:"var(--primary-main)"},backgroundColor:"var(--primary-light)"}}));function h({children:i,size:r="medium",...d}){return e.jsx(o,{id:"icon-button",customSize:r,...d,children:i})}export{h as e};
