import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getLocalStorage, groupBy, removeHiddenAndEmptyObjects, transformStructureForAllTabsData } from "@helper/helper";
import { basicDataTabs, salesDataTabs, purchasingDataTabs, mrpDataTabs, accountingDataTabs, setAllTabsData, setMaterialFieldConfig, setMatViews } from "@app/tabsDetailsSlice";
import { destination_IDM } from "../destinationVariables";
import { doAjax } from "@components/Common/fetchService";
import { API_CODE, EXCLUDED_VIEWS, LOCAL_STORAGE_KEYS, REQUEST_TYPE, TASK_NAME, VISIBILITY_TYPE } from "@constant/enum";
import { END_POINTS } from "@constant/apiEndPoints";
import useLogger from "./useLogger";
import { useLocation } from "react-router-dom";
import { setUniqueSalesOrgData } from "@app/requestDataSlice";
import { MATERIAL_VIEWS } from "../constant/enum";

const transformMaterialFieldConfigData = (responseData) => {
  let mandatoryFields = {};
  let sortedData = responseData?.sort((a, b) => a.MDG_MAT_VIEW_SEQUENCE - b.MDG_MAT_VIEW_SEQUENCE);
  const groupedFields = groupBy(sortedData, "MDG_MAT_VIEW_NAME");

  let view_data_array = [];
  Object.entries(groupedFields).forEach(([viewName, fields]) => {
    let groupedFieldsDataCardNameWise = groupBy(fields, "MDG_MAT_CARD_NAME");
    let cards = [];
    Object.entries(groupedFieldsDataCardNameWise).forEach(([cardName, cardFields]) => {
      cardFields.sort((a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO);

      let cardDetails = cardFields.map((item) => ({
        fieldName: item.MDG_MAT_UI_FIELD_NAME,
        sequenceNo: item.MDG_MAT_SEQUENCE_NO,
        fieldType: item.MDG_MAT_FIELD_TYPE,
        maxLength: item.MDG_MAT_MAX_LENGTH,
        dataType: item.MDG_MAT_DATA_TYPE,
        viewName: item.MDG_MAT_VIEW_NAME,
        cardName: item.MDG_MAT_CARD_NAME,
        cardSeq: item.MDG_MAT_CARD_SEQUENCE,
        value: item.MDG_MAT_DEFAULT_VALUE,
        visibility: item.MDG_MAT_VISIBILITY,
        jsonName: item.MDG_MAT_JSON_FIELD_NAME,
        fieldPriority: item.MDG_MAT_MATERIAL_FLD_PRT,
      }));

      cards.push({
        cardName,
        cardSeq: cardFields[0].MDG_MAT_CARD_SEQUENCE,
        cardDetails,
      });
    });

    cards.sort((a, b) => a.cardSeq - b.cardSeq);
    view_data_array.push({ viewName, cards });
  });

  let filteredData = removeHiddenAndEmptyObjects(view_data_array);
  let transformedData = {};
  filteredData.forEach((view) => {
    let cardData = {};
    view.cards.forEach((card) => {
      cardData[card.cardName] = card.cardDetails;
      if (view.viewName !== "Request Header") {
        card.cardDetails.forEach((detail) => {
          if (detail.visibility === VISIBILITY_TYPE.MANDATORY) {
            if (!mandatoryFields[detail.viewName]) {
              mandatoryFields[detail.viewName] = [];
            }
            mandatoryFields[detail.viewName].push({
              jsonName: detail?.jsonName,
              fieldName: detail?.fieldName,
            });
          }
        });
      }
    });
    transformedData[view.viewName] = cardData;
  });
  return { transformedData, mandatoryFields };
};

const useMaterialFieldConfig = () => {
  const dispatch = useDispatch();
  const { customError } = useLogger();
  const initialPayload = useSelector((state) => state.payload?.payloadData);
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const { userData, taskData } = useSelector((state) => state.userManagement);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const location = useLocation();
  const isDisplaySAPViewPresent = window.location.href.includes("DisplayMaterialSAPView");

  const taskDataString = getLocalStorage(LOCAL_STORAGE_KEYS.CURRENT_TASK);
  let finalTaskData = null;
  finalTaskData = typeof taskDataString === "string" ? JSON.parse(taskDataString) : taskDataString;
  let workflowGroup = finalTaskData?.ATTRIBUTE_5;
  const getTemplateData = async (materialType, region) => {
    if (initialPayload?.RequestType || isDisplaySAPViewPresent) {
      const payload = {
        decisionTableId: null,
        decisionTableName: "MDG_MAT_MATERIAL_FIELD_CONFIG",
        version: "v4",
        rulePolicy: null,
        validityDate: null,
        conditions: [
          {
            "MDG_CONDITIONS.MDG_MAT_SCENARIO": initialPayload?.RequestType || "Display",

            "MDG_CONDITIONS.MDG_MAT_MATERIAL_TYPE": isDisplaySAPViewPresent ? materialType.split("-")[0] : materialType || "VERP",

            "MDG_CONDITIONS.MDG_MAT_REGION": initialPayload?.Region || "US",

            "MDG_CONDITIONS.MDG_MAT_GROUP_ROLE": isDisplaySAPViewPresent ? "Z_MAT_REQ_DISPLAY" : taskData.ATTRIBUTE_5 ? workflowGroup : "Z_MAT_REQ_INITIATE",
          },
        ],
        systemFilters: null,
        systemOrders: null,
        filterString: null,
      };

      const hSuccess = (data) => {
        if (data.statusCode === API_CODE.STATUS_200) {
          if (Array.isArray(data?.data?.result) && data?.data?.result.every((item) => Object.keys(item).length !== 0)) {
            let responseData = data?.data?.result[0]?.MDG_MAT_MATERIAL_FIELD_CONFIG;
            const { transformedData, mandatoryFields } = transformMaterialFieldConfigData(responseData);
            let mtabsData = Object.keys(transformedData);
            const materialTbasData = mtabsData.map((items) => {
              if (items === "Basic Data") {
                dispatch(basicDataTabs(transformedData["Basic Data"]));
              } else if (items === "Sales") {
                dispatch(salesDataTabs(transformedData["Sales"]));
              } else if (items === "Purchasing") {
                dispatch(purchasingDataTabs(transformedData["Purchasing"]));
              } else if (items === "MRP") {
                dispatch(mrpDataTabs(transformedData["MRP"]));
              } else if (items === "Accounting") {
                dispatch(accountingDataTabs(transformedData["Accounting"]));
              }
              if (items !== "Request Header") {
                dispatch(setAllTabsData({ tab: items, data: transformedData[items] }));
              }
              return { [items]: transformedData[items] };
            });
            dispatch(
              setMaterialFieldConfig({
                [initialPayload?.Region || region || "US"]: {
                  [materialType]: {
                    allfields: transformStructureForAllTabsData(materialTbasData),
                    mandatoryFields: mandatoryFields,
                  },
                },
              })
            );
            let views = [...new Set(responseData?.sort((a, b) => a.MDG_MAT_VIEW_SEQUENCE - b.MDG_MAT_VIEW_SEQUENCE)?.map((conf) => conf.MDG_MAT_VIEW_NAME))];
            views = [...views.filter((view) => !EXCLUDED_VIEWS.includes(view)), ...([MATERIAL_VIEWS.CLASSIFICATION])];
            dispatch(setMatViews({ matType: materialType, views }));
          } else {
            dispatch(
              setMaterialFieldConfig({
                [initialPayload?.Region || region || "US"]: {
                  [materialType]: {},
                },
              })
            );
          }
          setLoading(false);
        }
      };

      const hError = (error) => {
        customError(error);
        setError(error);
        setLoading(false);
      };

      const url = applicationConfig.environment === "localhost" ? `/${destination_IDM}${END_POINTS.INVOKE_RULES.LOCAL}` : `/${destination_IDM}${END_POINTS.INVOKE_RULES.PROD}`;
      doAjax(url, "post", hSuccess, hError, payload);
    }
  };
  const fetchMaterialFieldConfig = (materialType, role, region) => {
    setLoading(true);
    try {
      getTemplateData(materialType, role, region);
    } catch (err) {
      setError(err);
      setLoading(false);
    }
  };

  const getTemplateDataExtend = async (materialType, region) => {
    if (initialPayload?.RequestType) {
      const payload = {
        decisionTableId: null,
        decisionTableName: "MDG_MAT_EXTEND_TEMPLATE_DT",
        version: "v2",
        rulePolicy: null,
        validityDate: null,
        conditions: [
          {
            "MDG_CONDITIONS.MDG_MAT_SCENARIO": "Extend",
            "MDG_CONDITIONS.MDG_MAT_REGION": "US",
            "MDG_CONDITIONS.MDG_MAT_GROUP_ROLE": "CA-MDG-MD-TEAM-US",
          },
        ],
        systemFilters: null,
        systemOrders: null,
        filterString: null,
      };

      const hSuccess = (data) => {
        if (data.statusCode === API_CODE.STATUS_200) {
          if (Array.isArray(data?.data?.result) && data?.data?.result.every((item) => Object.keys(item).length !== 0)) {
            let responseData = data?.data?.result[0]?.MDG_MAT_EXTEND_TEMPLATE_DT;
            const { transformedData, mandatoryFields } = transformMaterialFieldConfigData(responseData);
            let mtabsData = Object.keys(transformedData);
            const materialTbasData = mtabsData.map((items) => {
              if (items === "Basic Data") {
                dispatch(basicDataTabs(transformedData["Basic Data"]));
              } else if (items === "Sales") {
                dispatch(salesDataTabs(transformedData["Sales"]));
              } else if (items === "Purchasing") {
                dispatch(purchasingDataTabs(transformedData["Purchasing"]));
              } else if (items === "MRP") {
                dispatch(mrpDataTabs(transformedData["MRP"]));
              } else if (items === "Accounting") {
                dispatch(accountingDataTabs(transformedData["Accounting"]));
              }
              if (items !== "Request Header") {
                dispatch(setAllTabsData({ tab: items, data: transformedData[items] }));
              }
              return { [items]: transformedData[items] };
            });
            dispatch(
              setMaterialFieldConfig({
                [initialPayload?.Region || region || "US"]: {
                  [materialType]: {
                    allfields: transformStructureForAllTabsData(materialTbasData),
                    mandatoryFields: mandatoryFields,
                  },
                },
              })
            );

            let views = [...new Set(responseData?.sort((a, b) => a.MDG_MAT_VIEW_SEQUENCE - b.MDG_MAT_VIEW_SEQUENCE)?.map((conf) => conf.MDG_MAT_VIEW_NAME))];
            views = views.filter((view) => !EXCLUDED_VIEWS.includes(view));
            dispatch(setMatViews({ matType: materialType, views }));
          } else {
            dispatch(
              setMaterialFieldConfig({
                [initialPayload?.Region || region || "US"]: {
                  [materialType]: {},
                },
              })
            );
          }
          setLoading(false);
        }
      };

      const hError = (error) => {
        customError(error);
        setError(error);
        setLoading(false);
      };

      const url = applicationConfig.environment === "localhost" ? `/${destination_IDM}${END_POINTS.INVOKE_RULES.LOCAL}` : `/${destination_IDM}${END_POINTS.INVOKE_RULES.PROD}`;
      doAjax(url, "post", hSuccess, hError, payload);
    }
  };

  const fetchMaterialFieldConfigExtend = (materialType, region) => {
    setLoading(true);
    try {
      getTemplateDataExtend(materialType, region);
    } catch (err) {
      setError(err);
      setLoading(false);
    }
  };

  const getOrgDataFromDT = (region) => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_MAT_REG_SORG_PLNT_CNTRY_STORE_WH_MAPPING",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_REGION": "US" || region?.code || initialPayload?.Region,
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };

    const hSuccess = (data) => {
      if (data.statusCode === API_CODE.STATUS_200) {
        let responseData = data?.data?.result[0]?.MDG_MAT_REG_SORG_PLNT_CNTRY_STORE_WH_MAPPING;
        const uniqueSalesOrgs = Array.from(
          new Map(
            responseData.map((item) => [
              item.MDG_MAT_SALES_ORG,
              {
                code: item.MDG_MAT_SALES_ORG,
                desc: item.MDG_MAT_SALES_ORG_DESC,
              },
            ])
          ).values()
        );
        dispatch(
          setUniqueSalesOrgData({
            keyName: "uniqueSalesOrgList",
            data: uniqueSalesOrgs,
          })
        );
        dispatch(setUniqueSalesOrgData({ keyName: "salesOrgData", data: responseData }));
      }
    };

    const hError = (error) => {
      customError(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(`/${destination_IDM}${END_POINTS.INVOKE_RULES.LOCAL}`, "post", hSuccess, hError, payload);
    } else {
      doAjax(`/${destination_IDM}${END_POINTS.INVOKE_RULES.PROD}`, "post", hSuccess, hError, payload);
    }
  };

  const fetchOrgData = (region) => {
    setLoading(true);
    try {
      getOrgDataFromDT(region);
    } catch (err) {
      setError(err);
      setLoading(false);
    }
  };
  return {
    loading,
    error,
    fetchMaterialFieldConfig,
    fetchMaterialFieldConfigExtend,
    fetchOrgData,
  };
};

export default useMaterialFieldConfig;
