
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { setEntitiesAndActivities } from "../app/userManagementSlice";
import { doAjax } from "../components/Common/fetchService";
import { LOADING_MESSAGE } from "../constant/enum";
import { destination_MaterialMgmt } from "../destinationVariables";

// Fallback entities for localhost environment
const fallbackEntities = {
  DisplayProfitCenter: ["Approve", "Back", "Change", "Correction", "Fill Details", "Next", "Submit For Approval", "Validate"],
  ChangeProfitCenter: ["Back", "Next", "Submit For Review", "Validate", "Save As Draft"],
  CreateBankKey: ["Back", "Next", "Submit Fopr Review", "Validate", "Save As Draft"],
  DisplayBankKey: ["Approve", "Back", "Change", "Correction", "Fill Details", "Next", "Submit For Approval", "Validate"],
  ChangeBankKey: ["Back", "Next", "Submit For Review", "Validate", "Save As Draft"],
  "Config Cockpit": ["Email Template Configurations", "SLA Configurations", "User Management", "Application Configuration", "Business Rules", "Broadcast Configurations", "Document Configurations"],
  "Request Bench": ["Create New Request", "Request Details", "Request Tracker"],
  "Master Data Cockpit": ["Material Master", "Cost Center", "Profit Center", "Bank Key", "General Ledger"],
  "Data Cleanse": ["Material Master", "Cost Center", "Profit Center", "Bank Key", "General Ledger"],
  "Health Monitor": ["API Health", "System Status"],
  "Dashboard": ["View Dashboard", "Configure Widgets"]
};

const useModuleAccess = (destination_IWA_NEW) => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const applicationConfig = useSelector((state) => state.applicationConfig);

  const fetchModuleAccess = () => {
    setLoading(true);
    setLoaderMessage(LOADING_MESSAGE.LOADING_MODULES);

    // In local environment, set fallback data immediately but still try API calls
    const isLocalEnv = ["localhost", "127.0.0.1"].includes(applicationConfig.environment);
    if (isLocalEnv) {
      dispatch(setEntitiesAndActivities(fallbackEntities));
      setLoading(false);
      setLoaderMessage("");
      // Continue to try API calls in background - don't return here
    }

    const url = `/${destination_MaterialMgmt}/iwa/user/module-feature-access?iwaAppId=MDG`;

    doAjax(
      url,
      "get",
      (res) => {
        const modules = res?.data;
        const cleaned = {};

        for (const moduleName in modules) {
          cleaned[moduleName] = Object.keys(modules[moduleName]).filter(
            (feature) => modules[moduleName][feature] === true
          );
        }

        // Update with real data from API (this will override fallback data)
        dispatch(setEntitiesAndActivities(cleaned));
        if (!isLocalEnv) {
          setLoading(false);
          setLoaderMessage("");
        }
      },
      (err) => {
        console.error("Failed to fetch module access:", err);
        // Use fallback data even in non-local environments if API fails
        if (!isLocalEnv) {
          dispatch(setEntitiesAndActivities(fallbackEntities));
          setLoading(false);
          setLoaderMessage("");
        }
        // In local env, fallback data is already set
      }
    );
  };

  return {
    fetchModuleAccess,
    loading,
    loaderMessage,
  };
};

export default useModuleAccess;
