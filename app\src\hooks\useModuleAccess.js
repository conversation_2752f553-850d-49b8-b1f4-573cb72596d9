
import { useState } from "react";
import { useDispatch } from "react-redux";
import { setEntitiesAndActivities } from "../app/userManagementSlice";
import { doAjax } from "../components/Common/fetchService";
import { LOADING_MESSAGE } from "../constant/enum";
import { destination_MaterialMgmt } from "../destinationVariables";

const useModuleAccess = (destination_IWA_NEW) => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");

  const fetchModuleAccess = () => {
    setLoading(true);
    setLoaderMessage(LOADING_MESSAGE.LOADING_MODULES);

    const url = `/${destination_MaterialMgmt}/iwa/user/module-feature-access?iwaAppId=MDG`;

    doAjax(
      url,
      "get",
      (res) => {
        const modules = res?.data;
        const cleaned = {};

        for (const moduleName in modules) {
          cleaned[moduleName] = Object.keys(modules[moduleName]).filter(
            (feature) => modules[moduleName][feature] === true
          );
        }

        dispatch(setEntitiesAndActivities(cleaned));
        setLoading(false);
        setLoaderMessage("");
      },
      (err) => {
        console.error("Failed to fetch module access:", err);
        setLoading(false);
        setLoaderMessage("");
      }
    );
  };

  return {
    fetchModuleAccess,
    loading,
    loaderMessage,
  };
};

export default useModuleAccess;
