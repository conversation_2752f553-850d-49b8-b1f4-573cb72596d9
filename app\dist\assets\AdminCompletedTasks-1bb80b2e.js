import{q as t,s as h,b as L,r as s,a as o,F as k,pW as R,aV as w}from"./index-75c1660a.js";import{W as S,c as W,u as C}from"./propData-c6bd838d.js";import"./Workflow-e3fdf3c7.js";import"./react-beautiful-dnd.esm-db50900e.js";import"./useMediaQuery-33e0a836.js";import"./DialogContentText-ef8524b5.js";import"./CardMedia-f3120f7c.js";import"./Container-754d6379.js";import"./InputAdornment-a22e1655.js";import"./ListItemButton-f13df81b.js";import"./Slider-c4e5ff46.js";import"./Stepper-2dbfb76b.js";import"./StepButton-e06eb73a.js";import"./ToggleButtonGroup-63ceda7a.js";import"./index.esm-93e9b0e6.js";import"./makeStyles-c2a7efc7.js";import"./toConsumableArray-42cf6573.js";import"./asyncToGenerator-88583e02.js";import"./DatePicker-6e8720de.js";import"./Timeline-5c068db1.js";import"./dayjs.min-83c0b0e0.js";import"./isBetween-51871e12.js";import"./CSSTransition-8d766865.js";function x(){let a=t(e=>{var i;return(i=e.userManagement)==null?void 0:i.userData});t(e=>{var i;return(i=e.userManagement)==null?void 0:i.groups});const r=t(e=>e.applicationConfig);let l=h();const I=L();s.useState(null),s.useState(null);const[m,T]=s.useState(null),p={APPLICATION_NAME:"1784",CRUD_API_ENV:"itm",DB_TYPE:"hana",SERVICE_BASE_URL:[{Description:"",Name:"ITMJavaServices",URL:"https://cherryworkproducts-itm-java-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"ConfigServer",URL:"https://cherryworkproducts-config-server.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"WorkNetServices",URL:"https://cherryworkproducts-worknet-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"CrudApiServices",URL:"https://cw-caf-crudapi-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"WorkFormsServices",URL:"https://cherrywork-wf-java-qas.cfapps.eu10-004.hana.ondemand.com/workforms"},{Description:"",Name:"NotificationServices",URL:"https://cherryworkproducts-messaging-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"ITMGraphServices",URL:"https://cherrywork-btp-qas-dashboard.cfapps.eu10-004.hana.ondemand.com"},{Description:"Native Workflow Services",Name:"NativeWorkflowServices",URL:"https://cherryworkproducts-custom-wf-qas.cfapps.eu10-004.hana.ondemand.com"},{Description:"Native Workflow UI URL",Name:"NativeWorkflowUiUrl",URL:"https://cherrywork-native-workflow-qas.cfapps.eu10-004.hana.ondemand.com/native-ui"},{Description:"",Name:"OnboardingServices",URL:"https://cherryworkproducts-itm-java-qas.cfapps.eu10-004.hana.ondemand.com"}]},N={DateTimeFormat:{dateTimeFormat:"DD MMM YYYY||HH:mm",timeZone:"Asia/Calcutta"}},d=e=>{var c,n;l(w(e));var i={PRC:"/purchaseRequest/workbench/singleworkbench/",POC:"/purchaseOrder/confirmationTracker/taskDetail/",INC:"/invoices/workbench/singleInvoice/",RTC:"/ReturnManagement/SingleReturnWorkbench/",SEC:"/serviceSheet/workbench/singleServiceWorkbench/",PFC:"/planningManagement/singlePlanningTask/"};i[(c=e==null?void 0:e.taskDesc)==null?void 0:c.slice(0,4)]&&I(`${i[(n=e==null?void 0:e.taskDesc)==null?void 0:n.slice(0,4)]}${e==null?void 0:e.taskDesc}`)},V=()=>{console.log("fetchFilterView")},U=()=>{console.log("clearFilterView")},u=(e,i)=>{console.log("Success flag.",e),console.log("Task Payload.",i)};return o("div",{style:{width:"calc(100vw - 105px)",height:"calc(100vh-130px)"},className:"workspaceOverride",children:o(k,{children:o(S,{token:"eyJhbGciOiJSUzI1NiIsImprdSI6Imh0dHBzOi8vY2EtZ2JkLmF1dGhlbnRpY2F0aW9uLnVzMTAuaGFuYS5vbmRlbWFuZC5jb20vdG9rZW5fa2V5cyIsImtpZCI6ImRlZmF1bHQtand0LWtleS1kZjk5ODA5MzZhIiwidHlwIjoiSldUIiwiamlkIjogInVyQWRCVkJPN3VkV0FPMmFVaHp1QTRZU0V4aEE5TU96L05rTHF0UzkvR0E9In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.TomSTo1o_CU1U8ExR7p5kJOZmgQirKjQSsoyUKgSBHcyMZ7ZoUB3DiZSDANEeMYnYOv__ZcyBwnY4JmIBSTvQDiWrasSnlcd2rwAx6oVREZCH3lanUM3qQd0CuAKnJMWghGmj6_XSaBnJc2Kulk8LAusknZ87EpK1EbBby1Ajrua6LafLahVkaIj4KnQkHlIa4cSXbAGVXnqAecvpX7rUI1wmwcnE1f4az3oCFoNWC7LK_pF74pZoie6yTBP4s7aQyoOwk6q_ayJdVgMjvodvE-6h01nbEw-3oRlu3YgwZOTSib5apz8upBrRJ6DdKIinfas5Q_VAXXywRjWq5wswQ",configData:W,destinationData:p,userData:{...a,user_id:a==null?void 0:a.userName},userPreferences:N,userPermissions:C,userList:{},groupList:{},userListBySystem:m,useWorkAccess:r.environment==="localhost",useConfigServerDestination:r.environment==="localhost",inboxTypeKey:"ADMIN_COMPLETED_TASKS",workspaceLabel:"Admin Completed Tasks",workspaceFiltersByAPIDriven:!0,subInboxTypeKey:"COMPLETED",cachingBaseUrl:R,onTaskClick:d,onActionComplete:u,selectedFilterView:null,isFilterView:!1,fetchFilterViewList:V,savedFilterViewData:[],clearFilterView:U,filterViewList:[],selectedTabId:null,userProcess:[]})})})}export{x as default};
