import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  generalLedgerTypeDescription: {},
  generalLedgerControlData: {},
  generalLedgerCreateBankInterest: {},
  generalLedgerKeywordTranslation: {},
  generalLedgerInformation: {},
  singleGLPayload: {},
  generalLedgerViewData:[],
  requiredFields: [],
  errorFields: [],
  MultipleGLRequestBench:[],
  handleMassMode: "",
  MultipleGLData:[],
};

export const generalLedgerTabSlice = createSlice({
  name: "generalLedger",
  initialState,
  reducers: {
    setgeneralLedgerTypeDescription: (state, action) => {
      state.generalLedgerTypeDescription = action.payload;
    },
    setgeneralLedgerControlData: (state, action) => {
      state.generalLedgerControlData = action.payload;
    },
    setgeneralLedgerCreateBankIntrest: (state, action) => {
      state.generalLedgerCreateBankInterest = action.payload;
    },
    setgeneralLedgerKeywordTranslation: (state, action) => {
      state.generalLedgerKeywordTranslation = action.payload;
    },
    setgeneralLedgerInformation: (state, action) => {
      state.generalLedgerInformation = action.payload;
    },
  
    setSinglegeneralLedgerPayload: (state, action) => {
      state.singleGLPayload[action.payload.keyName] = action.payload.data;
      return state;
    },
    setGeneralLedgerViewData: (state, action) => {
      state.generalLedgerViewData = action.payload;
    },
    setMultipleGLRequestBench(state, action) {
      state.MultipleGLRequestBench = action.payload;
      return state;
    },
    setMultipleGLData(state, action) {
      console.log("mulAction", action);
      state.MultipleGLData = action.payload;
    },
    clearProfitCenterPayload: (state) => {
      state.singleGLPayload = {};
    },
    setGLRequiredFields: (state, action) => {
      if (
        state.requiredFields.findIndex((item) => item == action.payload) == -1
      ) {
        state.requiredFields.push(action.payload);
      }
      return state;
    },
    setGLErrorFields: (state, action) => {
      state.errorFields = action.payload;
      return state;
    },
    setHandleMassMode(state, action) {
      state.handleMassMode = action.payload;
    },
    clearGeneralLedger: (state) => {
      state.singleGLPayload = {}
      state.requiredFields = []
      state.errorFields = []
      
    },
  },
});

// Action creators are generated for each case reducer function
export const {
  setgeneralLedgerTypeDescription,
  setgeneralLedgerControlData,
  setgeneralLedgerCreateBankIntrest,
  setgeneralLedgerKeywordTranslation,
  setgeneralLedgerInformation,
  setSinglegeneralLedgerPayload,
  setGeneralLedgerViewData,
  setHandleMassMode,
  setMultipleGLRequestBench,
  setMultipleGLData,
  setGLRequiredFields,
  setGLErrorFields,
  clearGeneralLedger
  
  
} = generalLedgerTabSlice.actions;

export const generalLedgerReducer = generalLedgerTabSlice.reducer;
