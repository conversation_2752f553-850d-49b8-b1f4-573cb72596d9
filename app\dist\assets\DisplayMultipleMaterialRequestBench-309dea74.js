import{b as V,s as K,r as d,q as u,u as P,bV as j,a as t,j as a,b4 as Z,T as n,B as m,br as $,G as r,al as G,I as J,b1 as Q,x,v as U,w as X,K as Y,Z as ee,aE as te,t as ae,aD as ie}from"./index-75c1660a.js";import{d as re}from"./ArrowCircleLeftOutlined-f7b52d40.js";import{E as ne}from"./EditFieldForDisplay-bcafb50b.js";import"./dayjs.min-83c0b0e0.js";import"./DatePicker-31fef6b6.js";import"./dateViewRenderers-dbe02df3.js";import"./useSlotProps-da724f1f.js";import"./InputAdornment-a22e1655.js";import"./CSSTransition-8d766865.js";import"./useMediaQuery-33e0a836.js";import"./DesktopDatePicker-47a97548.js";import"./useMobilePicker-056b38fc.js";const ve=()=>{var S;const v=V(),w=K();d.useState({});const[I,E]=d.useState(0),[F,W]=d.useState([]),[N,oe]=d.useState(!1);d.useState(!0);let[g,k]=d.useState([]);const[C,A]=d.useState([]),[M,q]=d.useState(0),[le,R]=d.useState(),_=u(e=>e.tabsData),L={basicData:"Basic Data"},D=P();u(e=>e.initialData.EditMultipleMaterial);const y=u(e=>e.initialData.MultipleMaterialRequestBench);u(e=>{var l;return(l=e==null?void 0:e.initialData)==null?void 0:l.IWMMyTask});let o=u(e=>e.userManagement.taskData);const h=D.state.rowData;console.log("rowData",h),console.log("task",o),D.state.requestNumber,u(e=>e.payload);for(let e=0;e<y.length;e++)if(y[e].Description===h.description){y[e];break}const z=(e,l)=>{q(l)},H=()=>{var s,p;let e={};(o==null?void 0:o.processDesc)==="Mass Change"?e={massCreationId:"",massChangeId:(s=o==null?void 0:o.subject)==null?void 0:s.slice(3),screenName:"Change"}:(o==null?void 0:o.processDesc)==="Mass Create"&&(e={massCreationId:(p=o==null?void 0:o.subject)==null?void 0:p.slice(3),massChangeId:"",screenName:"Create"}),console.log(e);const l=i=>{W(i==null?void 0:i.body[0]);const T=i.body[0].viewData;R(i.body[0].IDs);const f=Object.keys(T);console.log("categorry",f),k(f);const O=f.map(B=>({category:B,data:T[B]}));A(O),console.log("materialDetails",C),w(j(i==null?void 0:i.body))},c=i=>{console.log(i)};Y(`/${ee}/data/displayMassMaterial`,"post",l,c,e)};console.log("factorsArray",g),d.useEffect(()=>{H(),w(j(F))},[]);const b=g.map(e=>{var s;const l=(s=Object.entries(_).filter(p=>{var i;return((i=L[p[0]])==null?void 0:i.split(" ")[0])==(e==null?void 0:e.split(" ")[0])})[0])==null?void 0:s[1],c=C.filter(p=>{var i;return((i=p.category)==null?void 0:i.split(" ")[0])==(e==null?void 0:e.split(" ")[0])});return c.length!==0?{category:e==null?void 0:e.split(" ")[0],data:c[0].data}:{category:e==null?void 0:e.split(" ")[0],data:l}}).map((e,l)=>(console.log("categorydata",e),(e==null?void 0:e.category)=="Basic"?[t(r,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(e.data).map(c=>a(r,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Z},children:[t(n,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:c}),t(m,{sx:{width:"100%"},children:t($,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:t(r,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:e.data[c].map(s=>t(ne,{label:s.fieldName,value:s.value,onSave:p=>handleFieldSave(s.fieldName,p),type:s.fieldType,field:s}))})})})]},c))},e.category)]:null));return console.log(b,"lololol"),a("div",{children:[t(r,{container:!0,style:{...G,backgroundColor:"#FAFCFF"},children:a(r,{sx:{width:"inherit"},children:[a(r,{item:!0,md:12,style:{padding:"16px",display:"flex"},children:[t(r,{item:!0,style:{display:"flex",justifyContent:"flex-end"},children:t(J,{color:"primary","aria-label":"upload picture",component:"label",sx:Q,children:t(re,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{v("/RequestBench")}})})}),a(r,{md:8,children:[t(n,{variant:"h3",children:a("strong",{children:["Multiple Material : ",h.description," "]})}),t(n,{variant:"body2",color:"#777",children:"This view displays details of uploaded material"})]})]}),a(r,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:[a(m,{width:"70%",sx:{marginLeft:"40px"},children:[t(r,{item:!0,sx:{paddingTop:"2px !important"},children:a(x,{flexDirection:"row",children:[t("div",{style:{width:"15%"},children:t(n,{variant:"body2",color:"#777",children:"Material"})}),a(n,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[": ",h.material]})]})}),t(r,{item:!0,sx:{paddingTop:"2px !important"},children:a(x,{flexDirection:"row",children:[t("div",{style:{width:"15%"},children:t(n,{variant:"body2",color:"#777",children:"Material Type"})}),a(n,{variant:"body2",fontWeight:"bold",children:[": ",h.materialType]})]})}),t(r,{item:!0,sx:{paddingTop:"2px !important"},children:a(x,{flexDirection:"row",children:[t("div",{style:{width:"15%"},children:t(n,{variant:"body2",color:"#777",children:"Description"})}),a(n,{variant:"body2",fontWeight:"bold",children:[": ",h.description]})]})}),t(r,{item:!0,sx:{paddingTop:"2px !important"},children:a(x,{flexDirection:"row",children:[t("div",{style:{width:"15%"},children:t(n,{variant:"body2",color:"#777",children:"Industry Sector"})}),a(n,{variant:"body2",fontWeight:"bold",children:[": ",h.industrySector]})]})})]}),t(m,{width:"30%",sx:{marginLeft:"40px"},children:t(r,{item:!0,children:a(x,{flexDirection:"row",children:[t(n,{variant:"body2",color:"#777",style:{width:"30%"}}),t(n,{variant:"body2",fontWeight:"bold",sx:{width:"8%",textAlign:"center"}}),t(n,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start"})]})})})]}),a(r,{container:!0,style:{padding:"16px"},children:[t(m,{sx:{borderBottom:1,borderColor:"divider"},children:t(U,{value:M,onChange:z,variant:"scrollable",sx:{background:"#FFF",borderBottom:"1px solid #BDBDBD",width:"100%"},"aria-label":"mui tabs example",children:g.map((e,l)=>t(X,{sx:{fontSize:"12px",fontWeight:"700"},label:e},l))})}),t(r,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:b&&((S=b[M])==null?void 0:S.map((e,l)=>t(m,{sx:{mb:2,width:"100%"},children:t(n,{variant:"body2",children:e})},l)))},b)]})]})}),N?t(ie,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:t(te,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:I,onChange:e=>{E(e)},children:t(ae,{size:"small",variant:"contained",onClick:()=>{v("/masterDataCockpit/materialMaster/massMaterialTable")},children:"Save"})})}):""]})};export{ve as default};
