import React from "react";
import { Route } from "react-router-dom";
const MyTasks = React.lazy(() => import("../../components/ITMWorkbench/MyTasks"));
const AdminTasks = React.lazy(() => import("../../components/ITMWorkbench/AdminTasks"));
const CompletedTasks = React.lazy(() => import("../../components/ITMWorkbench/CompletedTasks"));
const AdminCompletedTasks = React.lazy(() => import("../../components/ITMWorkbench/AdminCompletedTasks"));

export const WorkspaceRoutes = [
  <Route path="/workspace/MyTasks" element={<MyTasks />} />,
  <Route path="/workspace/AdminTasks" element={<AdminTasks />} />,
  <Route path="/workspace/CompletedTasks" element={<CompletedTasks />} />,
  <Route path="/workspace/AdminCompletedTasks" element={<AdminCompletedTasks />} />
];