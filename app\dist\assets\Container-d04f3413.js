import{cE as c,l$ as W,m0 as k,pN as m,m1 as v,r as R,cI as $,o as G,m4 as S,m5 as M,m6 as P,cB as T,db as y,cH as N}from"./index-17b8d91e.js";const j=["className","component","disableGutters","fixed","maxWidth","classes"],L=W(),z=k("div",{name:"MuiContainer",slot:"Root",overridesResolver:(a,e)=>{const{ownerState:o}=a;return[e.root,e[`maxWidth${m(String(o.maxWidth))}`],o.fixed&&e.fixed,o.disableGutters&&e.disableGutters]}}),E=a=>v({props:a,name:"MuiContainer",defaultTheme:L}),U=(a,e)=>{const o=i=>P(e,i),{classes:p,fixed:u,disableGutters:l,maxWidth:t}=a,s={root:["root",t&&`maxWidth${m(String(t))}`,u&&"fixed",l&&"disableGutters"]};return M(s,o,p)};function _(a={}){const{createStyledComponent:e=z,useThemeProps:o=E,componentName:p="MuiContainer"}=a,u=e(({theme:t,ownerState:s})=>c({width:"100%",marginLeft:"auto",boxSizing:"border-box",marginRight:"auto",display:"block"},!s.disableGutters&&{paddingLeft:t.spacing(2),paddingRight:t.spacing(2),[t.breakpoints.up("sm")]:{paddingLeft:t.spacing(3),paddingRight:t.spacing(3)}}),({theme:t,ownerState:s})=>s.fixed&&Object.keys(t.breakpoints.values).reduce((i,n)=>{const d=n,r=t.breakpoints.values[d];return r!==0&&(i[t.breakpoints.up(d)]={maxWidth:`${r}${t.breakpoints.unit}`}),i},{}),({theme:t,ownerState:s})=>c({},s.maxWidth==="xs"&&{[t.breakpoints.up("xs")]:{maxWidth:Math.max(t.breakpoints.values.xs,444)}},s.maxWidth&&s.maxWidth!=="xs"&&{[t.breakpoints.up(s.maxWidth)]:{maxWidth:`${t.breakpoints.values[s.maxWidth]}${t.breakpoints.unit}`}}));return R.forwardRef(function(s,i){const n=o(s),{className:d,component:r="div",disableGutters:b=!1,fixed:f=!1,maxWidth:C="lg"}=n,g=$(n,j),x=c({},n,{component:r,disableGutters:b,fixed:f,maxWidth:C}),h=U(x,p);return G.jsx(u,c({as:r,ownerState:x,className:S(h.root,d),ref:i},g))})}const w=_({createStyledComponent:T("div",{name:"MuiContainer",slot:"Root",overridesResolver:(a,e)=>{const{ownerState:o}=a;return[e.root,e[`maxWidth${y(String(o.maxWidth))}`],o.fixed&&e.fixed,o.disableGutters&&e.disableGutters]}}),useThemeProps:a=>N({props:a,name:"MuiContainer"})}),B=w;export{B as C,_ as c};
