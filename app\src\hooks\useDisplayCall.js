import { doAjax } from '@components/Common/fetchService';
import React, { useState } from 'react'
import { useSelector } from 'react-redux';
import { useDispatch } from 'react-redux';
import { useLocation, useNavigate } from "react-router-dom";
import { pushDisplayPayload, setDataLoading, setDynamicKeyValue, setFCRows } from '../app/payloadslice';
import { destination_MaterialMgmt } from '../destinationVariables';
import { updateCurrentCount, updateExistingCreatePages, updateNextButtonStatus, updatePage, updateTotalCount } from "../app/paginationSlice"
import useChangeMaterialRows from './useChangeMaterialRows';
import useChangeMaterialRowsRequestor from './useChangeMaterialRowsRequestor';
import { DECISION_TABLE_NAME, LOCAL_STORAGE_KEYS, PAGE_TYPE, REQUEST_TYPE } from '@constant/enum';
import { END_POINTS } from '@constant/apiEndPoints';
import useLogger from './useLogger';
import { transformApiResponseToReduxPayload } from '../functions';
import { pushMaterialRows, setMaterialRows } from '@app/requestDataSlice';
import useFinanceCostingRows from './useFinanceCostingRows';
import { getLocalStorage } from '@helper/helper';


const useDisplayCall = () => {
    const dispatch = useDispatch();
    const location = useLocation();
    const { fetchDisplayDataRows } = useChangeMaterialRows();
    const { fetchDisplayDataRequestor } = useChangeMaterialRowsRequestor();
    const { createFCRows } = useFinanceCostingRows();
    const taskData = useSelector((state) => state.userManagement?.taskData);
    const paginationData = useSelector((state) => state.paginationData);
    const prevFcRows = useSelector((state) => state.payload.fcRows);
    const changeFieldRowsDisplay = useSelector((state) => state.payload.changeFieldRowsDisplay);
    const storedRows = useSelector((state) => state.request.materialRows);
    const initialPayload = useSelector((state) => state.payload.payloadData);
    const requestorPayload = useSelector((state) => state.payload.requestorPayload);
    const queryParams = new URLSearchParams(location.search);
    const RequestId = queryParams.get('RequestId');
    const RequestType = queryParams.get('RequestType');
    const reqBench = queryParams.get('reqBench');
    const rowData = location.state;
    const {customError} = useLogger()
    const getNextDisplayDataForChange = async (type, action) => {   
        if(type === PAGE_TYPE?.DISPLAY) {
            if(changeFieldRowsDisplay[paginationData?.page]) {
                if(paginationData?.totalElements > (paginationData?.page+1) * paginationData?.size) {
                   dispatch(updateCurrentCount((paginationData?.page+1) * paginationData?.size))
                } else {
                   dispatch(updateCurrentCount(paginationData?.totalElements))
                }
                return;
            }
            getDisplaydata();
        }
        else if(type === PAGE_TYPE?.REQUESTOR) {
            if(changeFieldRowsDisplay[paginationData?.page]) {
                if(paginationData?.totalElements > (paginationData?.page+1) * paginationData?.size) {
                    dispatch(updateCurrentCount((paginationData?.page+1) * paginationData?.size))
                } else {
                    dispatch(updateCurrentCount(paginationData?.totalElements))
                }
                return;
            }
            const result = await fetchDisplayDataRequestor(initialPayload?.TemplateName, requestorPayload);
        }
    }
    const getNextDisplayDataForCreate = async () => {
        if(paginationData?.existingCreatePages.includes(paginationData?.page)) {
            return;
        }
        getDisplaydata();
    }
    const getDisplaydata = () => {
    dispatch(setDataLoading(true));
    let payload = {};
    const idToUse = RequestId;
    const savedTask = getLocalStorage(LOCAL_STORAGE_KEYS.CURRENT_TASK, true, {});
    const effectiveRequestType = RequestType || taskData?.ATTRIBUTE_2 || savedTask?.ATTRIBUTE_2;

    if (reqBench) {
        payload = {
            massCreationId: !rowData?.isBifurcated ? ((effectiveRequestType === REQUEST_TYPE.CREATE || effectiveRequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) ? idToUse.slice(3) : "") : "",
            massChildCreationId: rowData?.isBifurcated ? ((effectiveRequestType === REQUEST_TYPE.CREATE || effectiveRequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) ? idToUse.slice(3) : "") : "",
            massChangeId: !rowData?.isBifurcated ? ((effectiveRequestType === REQUEST_TYPE.CHANGE || effectiveRequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD) ? idToUse.slice(3) : "") : "",
            massExtendId: !rowData?.isBifurcated ?((effectiveRequestType === REQUEST_TYPE.EXTEND || effectiveRequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) ? idToUse.slice(3) : "") : "",
            massSchedulingId: !rowData?.isBifurcated ? (effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING ? idToUse.slice(3): "") : "",
            screenName: effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING ? "" : effectiveRequestType,
            dtName: effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING ? "" : DECISION_TABLE_NAME?.MDG_MAT_MATERIAL_FIELD_CONFIG,
            version: effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING ? "" : "v2",
            page: paginationData?.page,
            size: effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING ? 100 : (effectiveRequestType === REQUEST_TYPE.CHANGE || effectiveRequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD) ? 10 : 50,
            sort: "",
            ApproverGroup: taskData?.ATTRIBUTE_5 || "",
            Region: "",
            massChildSchedulingId: rowData?.isBifurcated ? (effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING ? idToUse.slice(3): "") : "",
            massChildExtendId: rowData?.isBifurcated ?((effectiveRequestType === REQUEST_TYPE.EXTEND || effectiveRequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) ? idToUse.slice(3) : "") : "",
            massChildChangeId: rowData?.isBifurcated ? ((effectiveRequestType === REQUEST_TYPE.CHANGE || effectiveRequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD) ? idToUse.slice(3) : "") : "",
        };
    }
    else {
        payload = {
            massCreationId: "",
            massChangeId: "",
            massSchedulingId: effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING ? idToUse.slice(3) : "",
            massExtendId: "",
            screenName: effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING ? "" : effectiveRequestType,
            dtName: effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING ? "" : DECISION_TABLE_NAME?.MDG_MAT_MATERIAL_FIELD_CONFIG,
            version: effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING ? "" :"v2",
            page: paginationData?.page,
            size: effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING ? 100 :(effectiveRequestType === REQUEST_TYPE.CHANGE || effectiveRequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD) ? 10 : 50,
            sort: "",
            ApproverGroup: taskData?.ATTRIBUTE_5 || "",
            Region: "",
            massChildCreationId: effectiveRequestType === REQUEST_TYPE.CREATE || effectiveRequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD ? idToUse.slice(3) : "",
            massChildSchedulingId: "",
            massChildExtendId: effectiveRequestType === REQUEST_TYPE.EXTEND || effectiveRequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD ? idToUse.slice(3) : "",
            massChildChangeId: effectiveRequestType === REQUEST_TYPE.CHANGE || effectiveRequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD ? idToUse.slice(3) : "",
        };
    }

    const hSuccess = async(data) => {
        dispatch(setDataLoading(false));
        const apiResponse = data.body;
        dispatch(updateTotalCount(data?.totalElements))
        if(data?.totalPages === 1 || data?.currentPage+1 === data?.totalPages) {
        dispatch(updateCurrentCount(data?.totalElements))
        dispatch(updateNextButtonStatus(true))
        }
        else {
        dispatch(updateCurrentCount((data?.currentPage+1) * data?.pageSize))
        }
        if (taskData?.ATTRIBUTE_2 === REQUEST_TYPE?.CHANGE || taskData?.ATTRIBUTE_2 === REQUEST_TYPE?.CHANGE_WITH_UPLOAD || RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD || RequestType === REQUEST_TYPE?.CHANGE) {
        dispatch(
            setDynamicKeyValue({
                keyName: "requestHeaderData",
                data: apiResponse[0]?.Torequestheaderdata
            })
        )
        fetchDisplayDataRows(apiResponse);
        return;
        }

        if (taskData?.ATTRIBUTE_2 === REQUEST_TYPE?.FINANCE_COSTING || RequestType === REQUEST_TYPE?.FINANCE_COSTING) {
            const fcRows = await createFCRows(apiResponse)
            dispatch(setFCRows([...prevFcRows, ...fcRows]));
            dispatch(updateExistingCreatePages(paginationData?.page));
            return;
        }

        const transformedPayload = transformApiResponseToReduxPayload(apiResponse, storedRows);
        dispatch(pushDisplayPayload({ data: transformedPayload?.payload }));
        const numericKeys = Object.keys(transformedPayload?.payload).filter((key) => !isNaN(Number(key)));
        const extractedData = {};
        numericKeys.forEach((key) => {
        extractedData[key] = transformedPayload?.payload[key];
        });
        dispatch(pushMaterialRows(Object.values(extractedData)?.map((item) => item.headerData)));
        dispatch(updateExistingCreatePages(paginationData?.page));
    };

    const hError = (error) => {
        customError(error);
    };

    doAjax(`/${destination_MaterialMgmt}/${END_POINTS?.CHG_DISPLAY_REQUESTOR?.DISPLAY_DTO}`, "post", hSuccess, hError, payload);
    };
    
    
    return { getNextDisplayDataForChange, getNextDisplayDataForCreate };
}

export default useDisplayCall