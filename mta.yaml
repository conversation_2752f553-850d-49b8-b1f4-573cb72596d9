ID: mdg
_schema-version: "3.1"
parameters:
  deploy_mode: html5-repo
version: 1.0.2
modules:
  - name: cw-mdg
    type: approuter.nodejs
    path: approuter
    properties:
      SEND_XFRAMEOPTIONS: false
    parameters:
      disk-quota: 256M
      memory: 256M
    requires:
      - name: mdg_html5_repo_runtime
      - name: cw-mdg-xsuaa
      - name: cw-mdg-services
  - name: cw-mdg-uideployer
    type: com.sap.html5.application-content
    path: html5Deployer
    parameters:
        config:
            sizeLimit: 16
    requires:
      - name: mdg_html5_repo_host
    build-parameters:
      requires:
        - name: mdg
          artifacts:
            - "./*"
          target-path: resources/app
  - name: mdg
    type: html5
    path: app
    build-parameters:
      builder: custom
      commands:
        - npm install -f
      supported-platforms: []
      build-result: dist
resources:
  - name: mdg_html5_repo_runtime
    parameters:
      service-plan: app-runtime
      service: html5-apps-repo
    type: org.cloudfoundry.managed-service
  - name: mdg_html5_repo_host
    parameters:
      service-plan: app-host
      service: html5-apps-repo
      config:
        sizeLimit: 100
    type: org.cloudfoundry.managed-service
  - name: cw-mdg-xsuaa
    parameters:
      path: ./xs-security.json
      service-plan: application
      service: xsuaa
    type: org.cloudfoundry.managed-service
  - name: cw-mdg-services
    parameters:
      service-plan: lite
      service: destination
    type: org.cloudfoundry.managed-service
