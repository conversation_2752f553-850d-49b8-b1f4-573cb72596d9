import { EditUser } from "@cw/edituser";
import { useNavigate, useParams } from "react-router-dom";
import { useSnackbar } from "@hooks/useSnackbar";
import {APP_END_POINTS} from "@constant/appEndPoints";

const IwaEditUser = () => {
  const { showSnackbar } = useSnackbar();
  const { userId } = useParams();
  const navigate = useNavigate();

  const editUserNavigate = (action, userId, response) => {
    if (action === "home") {
      navigate(APP_END_POINTS.IWA_USER_MANAGEMENT.USERS_SUMMARY);
    }
    if (response) {
      if (response?.status === "success" || response?.status === "SUCCESS" || response?.status === "Success") {
        showSnackbar(response.message, "info");
        return;
      }
      showSnackbar(response?.err?.data?.message, "error");
    }
  };

  return (
    <div>
      <EditUser userId={userId} editUserNavigate={editUserNavigate} />
    </div>
  );
};

export default IwaEditUser;
