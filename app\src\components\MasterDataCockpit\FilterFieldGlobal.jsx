import {
  <PERSON>rid,
  <PERSON>ack,
  TextField,
  Typography,
} from "@mui/material";
import { useSelector, useDispatch } from "react-redux";
import InputTypeGlobal from "./GlobalComponents/InputTypeGlobal";
import AutocompleteTypeGlobal from "./GlobalComponents/AutoCompleteTypeGlobal";
import MultiSelectTypeGlobal from "./GlobalComponents/MultiSelectTypeGlobal";
import RadioTypeGlobal from "./GlobalComponents/RadioTypeGlobal";
import DateTypeGlobal from "./GlobalComponents/DateTypeGlobal";
import { setMultipleMaterialPayloadKey, setPayload } from "../../app/payloadSlice";
import { useEffect } from "react";
import { useLocation } from "react-router-dom";
import { colors } from "../../constant/colors";
import { updateModuleFieldData } from "@app/profitCenterTabsSlice";
import { updateModuleFieldDataCC } from "../../app/costCenterTabsSlice";
import { updateModuleFieldDataGL } from "@app/generalLedgerTabSlice";
import { setRequestHeaderPayloadDataPCG } from "@app/hierarchyDataSlice";

const FilterFieldGlobal = (props) => {
  const dispatch = useDispatch();
  const location = useLocation();
  const payloadState = useSelector((state) => state.payload);
  const payloadStatePC = useSelector((state) => state.profitCenter.payload.requestHeaderData);
  const payloadStateGL = useSelector((state) => state.generalLedger.payload.requestHeaderData);

  console.log(payloadStateGL,"payloadStateGL")

  const payloadStateCC = useSelector((state) => state.costCenter.payload.requestHeaderData);
  const payloadStatePCG = useSelector((state) => state.hierarchyData.requestHeaderData);
  let taskData = useSelector((state) => state.userManagement.taskData);
  const queryParams = new URLSearchParams(location.search);
  const isWorkspace = queryParams.get("RequestId");
  const { field, disabled, dropDownData, uniqueId, viewName, plantData, module } = props;
  const userData = useSelector((state) => state.userManagement.userData);

  const handleChange = (value) => {
    if (location.pathname.includes("material")) {
      if (plantData) {
        dispatch(
          setMultipleMaterialPayloadKey({
            key: field.fieldName,
            value: value,
            viewName: viewName,
            plantData: plantData,
          })
        );
      } else {
        dispatch(
          setPayload({
            key: field.fieldName,
            value: value,
          })
        );
      }
    }
  };

  useEffect(() =>{
    if(!taskData?.requestId){
    if((props?.field?.fieldName === "Created On") || (props?.field?.fieldName === "Updated On")){
      const currentDate = new Date();
      if(module === "CostCenter"){
        dispatch(
          updateModuleFieldDataCC({
            keyName: props.field.jsonName,
            data: currentDate,
          })
        );
      }
      else if(module === "GeneralLedger"){
        dispatch(
          updateModuleFieldDataGL({
            keyName: props.field.jsonName,
            data: currentDate,
          })
        );
      }
      else if(module === "PCG"){
        dispatch(
          setRequestHeaderPayloadDataPCG({
            keyName: props.field.jsonName,
            data: currentDate,
          })
        );
      }
      else {
        dispatch(
          updateModuleFieldData({
            keyName: props.field.jsonName,
            data: currentDate,
          })
        );
      }
      
    }
  }
  },[])

  if (props?.field?.fieldName === "Created By") {
      return (
        <Grid item md={2}>
          <Stack>
            <Typography variant="body2" color="#777" sx={{whiteSpace: 'nowrap',overflow: 'hidden',textOverflow: 'ellipsis',maxWidth: '100%'}} title={props.field.fieldName}>
              {props.field.fieldName}
            </Typography>
            <TextField

              value={!isWorkspace? userData?.emailId : payloadState?.payloadData?.ReqCreatedBy || payloadStatePC?.ReqCreatedBy || payloadStateCC?.ReqCreatedBy || payloadStateGL?.ReqCreatedBy || payloadStatePCG?.ReqCreatedBy}
              title={!isWorkspace? userData?.emailId : payloadState?.payloadData?.ReqCreatedBy || payloadStatePC?.ReqCreatedBy || payloadStateCC?.ReqCreatedBy || payloadStateGL?.ReqCreatedBy || payloadStatePCG?.ReqCreatedBy}
              size="small"
              disabled={
                userData?.emailId ? true
                  : false
              }
              sx={{
                cursor: "not-allowed", 
                "& .MuiInputBase-root": {
                  height: "34px",
                },
                "& .MuiInputBase-input": {
                  cursor: "not-allowed",
                },
                '& .MuiInputBase-root.Mui-disabled': {
                  '& > input': {
                    WebkitTextFillColor: colors.black.dark,
                    color: colors.black.dark,
                  },
                  backgroundColor: colors.hover.light,
                }
              }}
            
            />
          </Stack>
        </Grid>
      );
    }
    else if(props?.field?.fieldName === "Created On") {
      const currentDate = new Date();
      const day = String(currentDate.getDate()).padStart(2, "0"); 
      const month = String(currentDate.getMonth() + 1).padStart(2, "0"); 
      const year = currentDate.getFullYear();
      
      const formattedDate = `${day}-${month}-${year}`;
      return (
        <Grid item md={2}>
          <Stack>
            <Typography variant="body2" color="#777" sx={{whiteSpace: 'nowrap',overflow: 'hidden',textOverflow: 'ellipsis',maxWidth: '100%'}} title={props.field?.fieldName}>
              {props?.field?.fieldName}
            </Typography>
            <TextField
              size="small"
              value={formattedDate} 
              disabled={true}
              sx={{
                cursor: "not-allowed",
                "& .MuiInputBase-root": {
                  height: "34px",
                },
                "& .MuiInputBase-input": {
                  cursor: "not-allowed",
                },
              '& .MuiInputBase-root.Mui-disabled': {
                  '& > input': {
                    WebkitTextFillColor: colors.black.dark,
                    color: colors.black.dark,
                  },
                  backgroundColor: colors.hover.light,
                }
              }}
            />
          </Stack>
        </Grid>
      );
    }
    else if(props?.field?.fieldName === "Updated On") {
      const currentDate = new Date();
      const day = String(currentDate.getDate()).padStart(2, "0"); 
      const month = String(currentDate.getMonth() + 1).padStart(2, "0"); 
      const year = currentDate.getFullYear();
      const formattedDate = `${day}-${month}-${year}`; 
      return (
        <Grid item md={2}>
          <Stack>
            <Typography variant="body2" color="#777" sx={{whiteSpace: 'nowrap',overflow: 'hidden',textOverflow: 'ellipsis',maxWidth: '100%'}} title={props.field?.fieldName}>
              {props?.field?.fieldName}
            </Typography>
            <TextField
              size="small"
              value={formattedDate} 
              disabled={true}
              sx={{
                cursor: "not-allowed",
                "& .MuiInputBase-root": {
                  height: "34px",
                },
                "& .MuiInputBase-input": {
                  cursor: "not-allowed",
                },
              '& .MuiInputBase-root.Mui-disabled': {
                  '& > input': {
                    WebkitTextFillColor: colors.black.dark,
                    color: colors.black.dark,
                  },
                  backgroundColor: colors.hover.light,
                }
              }}
            />
          </Stack>
        </Grid>
      );
    }

  const renderField = () => {
    switch (field.fieldType) {
      case "Input":
        return (
          <InputTypeGlobal
            uniqueId={uniqueId}
            field={field}
            disabled={disabled}
            handleChange={handleChange}
            selectedRow={props?.selectedRow}
            module={props?.module}
          />
        );
      case "Auto":
        return (
          <InputTypeGlobal
            uniqueId={uniqueId}
            field={field}
            disabled={disabled}
            handleChange={handleChange}
            selectedRow={props?.selectedRow}
            module={props?.module}
          />
        );
      case "Disable Input":
        return (
          <InputTypeGlobal
            uniqueId={uniqueId}
            field={field}
            disabled={true}
            handleChange={handleChange}
            selectedRow={props?.selectedRow}
            module={props?.module}
          />
        );
      case "Drop Down":
        return (
          <AutocompleteTypeGlobal
            uniqueId={uniqueId}
            field={field}
            disabled={disabled}
            dropDownData={dropDownData}
            handleChange={handleChange}
            selectedRow={props?.selectedRow}
            module={props?.module}
          />
        );
      case "Multi Select":
        return (
          <MultiSelectTypeGlobal
            uniqueId={uniqueId}
            field={field}
            disabled={disabled}
            dropDownData={dropDownData}
            handleChange={handleChange}
            selectedRow={props?.selectedRow}
            module={props?.module}
          />
        );
      case "Radio Button":
        return (
          <RadioTypeGlobal
            uniqueId={uniqueId}
            field={field}
            disabled={disabled}
            handleChange={handleChange}
            selectedRow={props?.selectedRow}
            module={props?.module}
          />
        );
      case "Calendar":
        return (
          <DateTypeGlobal
            uniqueId={uniqueId}
            field={field}
            disabled={disabled}
            handleChange={handleChange}
            selectedRow={props?.selectedRow}
            module={props?.module}
          />
        );
      default:
        return null;
    }
  };

  return (
    <>
        {renderField()}
    </>
  );
};

export default FilterFieldGlobal;