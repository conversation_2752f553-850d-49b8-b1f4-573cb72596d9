export const CHATBOT_CONFIG = {
  // Text Content
  TEXT: {
    ASSISTANT_NAME: "MDG Assistant",
    STATUS_ONLINE: "Online",
    STATUS_OFFLINE: "Offline",
    STATUS_CONNECTING: "Connecting...",
    INITIAL_MESSAGE: "Hello! I'm your AI assistant. How can I help you today? ✨",
    TYPING_INDICATOR: "<PERSON><PERSON> is typing...",
    INPUT_PLACEHOLDER: "Type your message...",
    CONNECTION_ERROR: "I'm sorry, I'm currently experiencing connection issues. Please try again later.",
    UNREAD_COUNT_MAX: "99+",
    
    // API Response Messages
    GNL_QUERY_SUCCESS: 'I apologize, but I couldn\'t process your request at the moment.',
    <PERSON>NL_QUERY_FAILED: 'I\'m sorry, I\'m experiencing technical difficulties. Please try again later.',
    DB_QUERY_SUCCESS: "Following is the data fetched from our database",
    DB_QUERY_FAILED: 'I\'m sorry, I\'m unable to fetch any relevant data corresponding to your query. Please try again.',
    NAV_QUERY_SUCCESS: "Following is the link to navigate to",
    NAV_QUERY_FAILED: 'I apologize, but I couldn\'t process your navigation request at the moment.',
    REQ_QUERY_SUCCESS: "Request processed successfully!",
    REQ_QUERY_FAILED: 'I apologize, but I couldn\'t process your request creation at the moment.',
    PROCESS_FAILED: 'I apologize, but I encountered an error while processing your request. Please try again.',
    
    // Action Labels
    CLEAR_CHAT: "Clear Chat",
    GO_BACK: "Go Back",
    MINIMIZE: "Minimize",
    MAXIMIZE: "Maximize",
    CLOSE: "Close",
    SEND: "Send",
    NAVIGATE: "Navigate",
    VIEW_TABLE: "View Table",
    
    // Mode Welcome Messages
    WELCOME_REQUESTS: 'Welcome to Request Creation! I can help you create various types of requests. What would you like to create?',
    WELCOME_GENERAL: 'Hello! I\'m here to answer your general questions. What would you like to know?',
    WELCOME_NAVIGATION: 'Hi! I can help you navigate to different pages in the application. Where would you like to go?',
    WELCOME_DATABASE: 'Welcome to Database Query mode! I can help you retrieve data from our database. What information are you looking for?'
  },

  CARD_TEXTS: {
    REQ_QUERY: {
      TITLE: 'Create Requests',
      DESCRIPTION: 'Generate and manage various types of requests'
    },
    GNL_QUERY: {
      TITLE: 'General Questions',
      DESCRIPTION: 'Ask me anything and get instant answers'
    },
    NAV_QUERY: {
      TITLE: 'Navigate Pages',
      DESCRIPTION: 'Quick navigation to different sections'
    },
    DB_QUERY: {
      TITLE: 'Database Query',
      DESCRIPTION: 'Retrieve and analyze data efficiently'
    }
  },

  // Dimensions
  DIMENSIONS: {
    CHAT_WIDTH: 430,
    CHAT_HEIGHT_OPEN: 600,
    CHAT_HEIGHT_MINIMIZED: 80,
    MESSAGES_HEIGHT: 410,
    AVATAR_SIZE: 32,
    SMALL_AVATAR_SIZE: 28,
    FAB_SIZE: 56,
    CARD_MIN_HEIGHT: 120,
    INPUT_MAX_HEIGHT: 120,
    HEADER_HEIGHT: 70,
    TAB_HEIGHT: 48,
    ACTION_BAR_HEIGHT: 40
  },

  // Colors
  COLORS: {
    // Primary Colors
    PRIMARY: '#667eea',
    PRIMARY_DARK: '#5a67d8',
    PRIMARY_LIGHT: '#f093fb',
    SECONDARY: '#764ba2',
    
    // Status Colors
    SUCCESS: '#48bb78',
    DANGER: '#f56565',
    WARNING: '#ed8936',
    INFO: '#4299e1',
    
    // Background Colors
    CHAT_BACKGROUND: '#ffffff',
    MESSAGES_BACKGROUND: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
    HEADER_BACKGROUND: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    BOT_MESSAGE_BG: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',
    USER_MESSAGE_BG: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    INPUT_BACKGROUND: '#fafcff',
    CARD_BACKGROUND: '#ffffff',
    
    // Text Colors
    TEXT_PRIMARY: '#2d3748',
    TEXT_SECONDARY: '#718096',
    TEXT_WHITE: '#ffffff',
    TEXT_MUTED: 'rgba(255, 255, 255, 0.8)',
    TEXT_DISABLED: '#a0aec0',
    
    // Border Colors
    BORDER_LIGHT: 'rgba(103, 126, 234, 0.1)',
    BORDER_MEDIUM: 'rgba(103, 126, 234, 0.2)',
    BORDER_STRONG: 'rgba(103, 126, 234, 0.4)',
    DIVIDER: 'rgba(0, 0, 0, 0.12)',
    
    // State Colors
    HOVER_OVERLAY: 'rgba(103, 126, 234, 0.1)',
    ACTIVE_OVERLAY: 'rgba(103, 126, 234, 0.2)',
    FOCUS_RING: 'rgba(103, 126, 234, 0.3)'
  },

  // Animation & Timing
  ANIMATION: {
    TRANSITION_DURATION: '0.3s',
    QUICK_TRANSITION: '0.2s',
    SLOW_TRANSITION: '0.5s',
    BOUNCE_DURATION: '2s',
    PULSE_DURATION: '1.5s',
    TYPING_DELAY: 1500,
    RECONNECT_DELAY: 3000,
    SCROLL_DURATION: 300,
    FADE_DURATION: 400,
    ZOOM_DURATION: 250
  },

  // Spacing (Material-UI spacing units)
  SPACING: {
    XS: 0.5,    // 4px
    SM: 1,      // 8px
    MD: 2,      // 16px
    LG: 3,      // 24px
    XL: 4,      // 32px
    XXL: 5      // 40px
  },

  // Border Radius
  RADIUS: {
    NONE: 0,
    SMALL: 1,      // 4px
    MEDIUM: 2,     // 8px
    LARGE: 3,      // 12px
    XLARGE: 4,     // 16px
    CIRCLE: '50%',
    MESSAGE: '18px',
    MESSAGE_SENDER: '18px 18px 4px 18px',
    MESSAGE_RECEIVER: '18px 18px 18px 4px',
    CARD: '12px',
    BUTTON: '8px'
  },

  // Shadows (Material-UI elevation system)
  SHADOWS: {
    NONE: 'none',
    LIGHT: '0 2px 8px rgba(103, 126, 234, 0.1)',
    MEDIUM: '0 4px 20px rgba(103, 126, 234, 0.15)',
    HEAVY: '0 8px 32px rgba(103, 126, 234, 0.2)',
    EXTREME: '0 12px 48px rgba(103, 126, 234, 0.25)',
    FAB: '0 6px 25px rgba(103, 126, 234, 0.4)',
    GLOW: '0 0 20px rgba(103, 126, 234, 0.3)',
    CARD_HOVER: '0 8px 25px rgba(103, 126, 234, 0.2)',
    INSET: 'inset 0 2px 4px rgba(103, 126, 234, 0.1)'
  },

  // Typography
  TYPOGRAPHY: {
    FONT_SIZE: {
      TINY: '10px',
      SMALL: '12px',
      REGULAR: '13px',
      MEDIUM: '14px',
      LARGE: '16px',
      XLARGE: '18px',
      TITLE: '20px'
    },
    FONT_WEIGHT: {
      LIGHT: 300,
      NORMAL: 400,
      MEDIUM: 500,
      SEMIBOLD: 600,
      BOLD: 700,
      EXTRA_BOLD: 800
    },
    LINE_HEIGHT: {
      TIGHT: 1.2,
      NORMAL: 1.4,
      RELAXED: 1.6,
      LOOSE: 1.8
    }
  },

  // Component Positions
  POSITION: {
    BOTTOM: 24,
    LEFT: 17,
    RIGHT: 24,
    TOP: 24,
    Z_INDEX: 1300,
    Z_INDEX_MODAL: 1400,
    Z_INDEX_TOOLTIP: 1500
  },

  // WebSocket Configuration
  WEBSOCKET: {
    RECONNECT_ATTEMPTS: 3,
    RECONNECT_INTERVAL: 3000,
    HEARTBEAT_INTERVAL: 30000,
    CONNECTION_TIMEOUT: 10000
  },

  // Scrollbar Styles
  SCROLLBAR: {
    WIDTH: '6px',
    TRACK_COLOR: '#f1f3f4',
    THUMB_COLOR: '#c1c8cd',
    THUMB_HOVER_COLOR: '#a8b2ba',
    BORDER_RADIUS: '3px'
  },

  // Message Limits & Constraints
  LIMITS: {
    MAX_MESSAGE_ROWS: 4,
    MAX_MESSAGE_WIDTH: '75%',
    MAX_MESSAGE_LENGTH: 1000,
    UNREAD_COUNT_MAX: 99,
    MAX_HISTORY_MESSAGES: 50,
    TYPING_TIMEOUT: 5000,
    API_TIMEOUT: 30000
  },

  // Breakpoints for responsive design
  BREAKPOINTS: {
    MOBILE: 480,
    TABLET: 768,
    DESKTOP: 1024,
    WIDE: 1200
  },

  // Feature Flags
  FEATURES: {
    ENABLE_VOICE: false,
    ENABLE_FILE_UPLOAD: false,
    ENABLE_EMOJI: true,
    ENABLE_MARKDOWN: true,
    ENABLE_TYPING_INDICATOR: true,
    ENABLE_READ_RECEIPTS: false,
    ENABLE_OFFLINE_MODE: false
  }
};

// Icon configurations
export const CHATBOT_ICONS = {
  SIZES: {
    TINY: 12,
    SMALL: 16,
    MEDIUM: 20,
    LARGE: 24,
    XLARGE: 28,
    HUGE: 32
  },
  COLORS: {
    PRIMARY: CHATBOT_CONFIG.COLORS.PRIMARY,
    SECONDARY: CHATBOT_CONFIG.COLORS.SECONDARY,
    SUCCESS: CHATBOT_CONFIG.COLORS.SUCCESS,
    WARNING: CHATBOT_CONFIG.COLORS.WARNING,
    DANGER: CHATBOT_CONFIG.COLORS.DANGER,
    MUTED: CHATBOT_CONFIG.COLORS.TEXT_SECONDARY
  }
};

// Page mapping for navigation
export const PAGE_MAP = {
  "/dashboard": "dashboard",
  "/requestBench": "request bench",
  "/": "home",
  "/documentManagement": "document management",
  "/configCockpit": "config cockpit",
  "/workspace/MyTasks": "open tasks",
  "/masterDataCockpit/materialMaster/material": "material master data",
  "/configCockpit/businessRules/authoring": "authoring",
  "/requestBench/createRequest": "create request for material",
};

// Query modes and their configurations
export const QUERY_MODES = {
  GNL_QUERY: 'general',
  DB_QUERY: 'database', 
  NAV_QUERY: 'navigateTo',
  REQ_QUERY: 'createRequest'
};

// Mode-specific configurations
export const MODE_CONFIG = {
  [QUERY_MODES.GNL_QUERY]: {
    name: 'General Questions',
    description: 'Ask me anything and get instant answers',
    color: CHATBOT_CONFIG.COLORS.PRIMARY,
    welcomeMessage: CHATBOT_CONFIG.TEXT.WELCOME_GENERAL
  },
  [QUERY_MODES.DB_QUERY]: {
    name: 'Database Query',
    description: 'Retrieve and analyze data efficiently', 
    color: CHATBOT_CONFIG.COLORS.SECONDARY,
    welcomeMessage: CHATBOT_CONFIG.TEXT.WELCOME_DATABASE
  },
  [QUERY_MODES.NAV_QUERY]: {
    name: 'Navigate Pages',
    description: 'Quick navigation to different sections',
    color: CHATBOT_CONFIG.COLORS.WARNING,
    welcomeMessage: CHATBOT_CONFIG.TEXT.WELCOME_NAVIGATION
  },
  [QUERY_MODES.REQ_QUERY]: {
    name: 'Create Requests',
    description: 'Generate and manage various types of requests',
    color: CHATBOT_CONFIG.COLORS.SUCCESS,
    welcomeMessage: CHATBOT_CONFIG.TEXT.WELCOME_REQUESTS
  }
};

// API Endpoints (if needed for reference)
export const API_ENDPOINTS = {
  GENERAL_QUERY: '/api/chatbot/general',
  DATABASE_QUERY: '/api/chatbot/database',
  NAVIGATION_QUERY: '/api/chatbot/navigation',
  REQUEST_QUERY: '/api/chatbot/request'
};

// Error codes and messages
export const ERROR_CODES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  API_ERROR: 'API_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
};

export const ERROR_MESSAGES = {
  [ERROR_CODES.NETWORK_ERROR]: 'Network connection error. Please check your internet connection.',
  [ERROR_CODES.API_ERROR]: 'Server error occurred. Please try again later.',
  [ERROR_CODES.VALIDATION_ERROR]: 'Invalid input provided. Please check your message.',
  [ERROR_CODES.TIMEOUT_ERROR]: 'Request timed out. Please try again.',
  [ERROR_CODES.UNKNOWN_ERROR]: 'An unexpected error occurred. Please try again.'
};

// Theme variants (for future theme switching)
export const THEME_VARIANTS = {
  LIGHT: 'light',
  DARK: 'dark',
  AUTO: 'auto'
};

// Default export for easier importing
export default {
  CHATBOT_CONFIG,
  CHATBOT_ICONS,
  PAGE_MAP,
  QUERY_MODES,
  MODE_CONFIG,
  API_ENDPOINTS,
  ERROR_CODES,
  ERROR_MESSAGES,
  THEME_VARIANTS
};