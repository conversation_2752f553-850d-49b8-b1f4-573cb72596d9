import { MATERIAL_VIEWS,REGION_CODE } from "@constant/enum";
import { checkAllKeys, checkObjectAndCount, checkOrgsKeys, convertForBasicDataMandatoryFields, filterMrpCombination, filterNullValues, generateUniqueCombinations, getMissingElements, getMissingValues, getMissingValuesForBasic, getPlantCodes } from "@helper/helper";

const useValidation = (singlePayloadData, allMaterialFieldConfigDT, selectedViews, setSelectedMaterialID, currentRowData) => {
  const checkValidation = (materialId, orgRow, isInternalRangeAllowed, isExternalRangeAllowed, isExtWOCheckAllowed) => {
    //setSelectedMaterialID(materialId);
    const payloadData = singlePayloadData?.[materialId]?.payloadData;
    const headerData = singlePayloadData?.[materialId]?.headerData;
    const ManufacturerID =singlePayloadData?.[materialId]?.ManufacturerID;
    const region = singlePayloadData?.payloadData?.Region;

      if (!headerData?.materialNumber) {
        return { missingFields: ["Material number"], isValid: false };
      }

    if (!headerData?.globalMaterialDescription || !payloadData) {
      return { missingFields: ["Material Description"], isValid: false };
    }

    // if(!ManufacturerID && singlePayloadData?.payloadData?.Region===REGION_CODE?.EUR){
    //   return { missingFields: ["Manufacturer ID(EAN)"], isValid: false };
    // }

    // const eanData = singlePayloadData?.[materialId]?.eanData;
    // if(eanData?.length > 1&&(singlePayloadData?.payloadData?.Region === REGION_CODE?.EUR)) {
    //   const hasInvalidEan = eanData.slice(1).some(row => row.eanUpc && !row.eanUpc.includes(ManufacturerID));
    //   if(hasInvalidEan) {
    //     return { missingFields: ["EAN/UPC must contain Manufacturer ID"], isValid: false };
    //   }
    // }

    const onlyBasicDataFields = convertForBasicDataMandatoryFields(payloadData[MATERIAL_VIEWS.BASIC_DATA]);
    onlyBasicDataFields["Material"] = headerData?.materialNumber;
    onlyBasicDataFields["MatlDesc"] = headerData?.globalMaterialDescription;

    const gotMatConfigFieldFromRedux = allMaterialFieldConfigDT?.find((materialTypes) => materialTypes?.[region] && materialTypes?.[region][headerData?.materialType?.code]);
    const allViewsMandatoryFields = gotMatConfigFieldFromRedux && gotMatConfigFieldFromRedux[region] && gotMatConfigFieldFromRedux[region][headerData?.materialType?.code]?.mandatoryFields;

    // Check Mandatory Fields for Basic Data View
    const mandatoryFieldsOfBasicData = allViewsMandatoryFields?.[MATERIAL_VIEWS.BASIC_DATA];
    if (mandatoryFieldsOfBasicData?.length > 0) {
      for (const field of mandatoryFieldsOfBasicData) {
        if (!onlyBasicDataFields[field?.jsonName]) {
          const missingFields = getMissingValuesForBasic(mandatoryFieldsOfBasicData, onlyBasicDataFields);
          return { missingFields, viewType: MATERIAL_VIEWS.BASIC_DATA, isValid: false, plant: [MATERIAL_VIEWS.BASIC_DATA] };
        }
      }
    }

    // Check Mandatory Fields for Purchasing View
    if (selectedViews.includes(MATERIAL_VIEWS.PURCHASING)) {
      const mandatoryFieldsOfPurchasing = allViewsMandatoryFields?.[MATERIAL_VIEWS.PURCHASING];
      if (mandatoryFieldsOfPurchasing) {
        if (!payloadData[MATERIAL_VIEWS.PURCHASING]) {
          const plantCodes = getPlantCodes(orgRow);
          const missingFields = getMissingValues(mandatoryFieldsOfPurchasing, payloadData[MATERIAL_VIEWS.PURCHASING]);
          return { missingFields, viewType: MATERIAL_VIEWS.PURCHASING, isValid: false, plant: plantCodes };
        } else {
          const { validCount } = checkOrgsKeys(orgRow, MATERIAL_VIEWS.PURCHASING);
          const { totalCount, allValid } = checkObjectAndCount(payloadData[MATERIAL_VIEWS.PURCHASING],mandatoryFieldsOfPurchasing);
          if (totalCount === validCount) {
            if (!allValid) {
              const plantCodes = getPlantCodes(orgRow);
              const missingOrg = filterNullValues(plantCodes, payloadData[MATERIAL_VIEWS.PURCHASING]);
              const missingFields = getMissingValues(mandatoryFieldsOfPurchasing, payloadData[MATERIAL_VIEWS.PURCHASING]);
              return { missingFields, viewType: MATERIAL_VIEWS.PURCHASING, isValid: false, plant: missingOrg };
            } else {
              if (!checkAllKeys(payloadData[MATERIAL_VIEWS.PURCHASING], mandatoryFieldsOfPurchasing)) {
                const plantCodes = getPlantCodes(orgRow);
                const missingOrg = getMissingElements(plantCodes, payloadData[MATERIAL_VIEWS.PURCHASING], mandatoryFieldsOfPurchasing);
                const missingFields = getMissingValues(mandatoryFieldsOfPurchasing, payloadData[MATERIAL_VIEWS.PURCHASING]);
                return { missingFields, viewType: MATERIAL_VIEWS.PURCHASING, isValid: false, plant: missingOrg?.missingFields };
              }
            }
          } else {
            const plantCodes = getPlantCodes(orgRow);
            const missingOrg = getMissingElements(plantCodes, payloadData[MATERIAL_VIEWS.PURCHASING], mandatoryFieldsOfPurchasing);
            const missingFields = getMissingValues(mandatoryFieldsOfPurchasing, payloadData[MATERIAL_VIEWS.PURCHASING]);
            return { missingFields, viewType: MATERIAL_VIEWS.PURCHASING, isValid: false, plant: missingOrg?.missingFields };
          }
        }
      }
    }

    // Check Mandatory Fields for MRP View
    if (selectedViews.includes(MATERIAL_VIEWS.MRP)) {
        const mandatoryFieldsOfMRP = allViewsMandatoryFields?.[MATERIAL_VIEWS.MRP];
        if (mandatoryFieldsOfMRP) {
            const combinationMrp =  generateUniqueCombinations(orgRow)
            if (!payloadData[MATERIAL_VIEWS.MRP]) {
                const missingFields = getMissingValues(mandatoryFieldsOfMRP, payloadData[MATERIAL_VIEWS.MRP]);
                return { missingFields, viewType: MATERIAL_VIEWS.MRP, isValid: false, plant: combinationMrp?.[MATERIAL_VIEWS.MRP]?.displayCombinations };
            } else {
                const { validCount } = checkOrgsKeys(orgRow, MATERIAL_VIEWS.MRP);
                const { totalCount, allValid } = checkObjectAndCount(payloadData[MATERIAL_VIEWS.MRP],mandatoryFieldsOfMRP);
                if (totalCount === validCount) {
                    if (!allValid) {
                        const plantCodes = getPlantCodes(orgRow);
                        const missingOrg = filterNullValues(plantCodes, payloadData[MATERIAL_VIEWS.MRP]);
                        const missingFields = getMissingValues(mandatoryFieldsOfMRP, payloadData[MATERIAL_VIEWS.MRP]);
                        const mappedMrp =  filterMrpCombination(missingOrg,combinationMrp?.[MATERIAL_VIEWS.MRP]?.displayCombinations)
                        return { missingFields, viewType: MATERIAL_VIEWS.MRP, isValid: false, plant: mappedMrp };
                    } else {
                        if (!checkAllKeys(payloadData[MATERIAL_VIEWS.MRP], mandatoryFieldsOfMRP)) {
                            const plantCodes = getPlantCodes(orgRow);
                            const missingOrg = getMissingElements(plantCodes, payloadData[MATERIAL_VIEWS.MRP], mandatoryFieldsOfMRP);
                            const missingFields = getMissingValues(mandatoryFieldsOfMRP, payloadData[MATERIAL_VIEWS.MRP]);
                            const mappedMrp =  filterMrpCombination(missingOrg?.missingFields,combinationMrp?.[MATERIAL_VIEWS.MRP]?.displayCombinations)
                            return { missingFields, viewType: MATERIAL_VIEWS.MRP, isValid: false, plant: mappedMrp };
                        }
                    }
                } else {
                    const plantCodes = getPlantCodes(orgRow);
                    const missingOrg = getMissingElements(plantCodes, payloadData[MATERIAL_VIEWS.MRP], mandatoryFieldsOfMRP);
                    const missingFields = getMissingValues(mandatoryFieldsOfMRP, payloadData[MATERIAL_VIEWS.MRP]);
                    const mappedMrp =  filterMrpCombination(missingOrg?.missingFields,combinationMrp?.[MATERIAL_VIEWS.MRP]?.displayCombinations)
                    return { missingFields, viewType: MATERIAL_VIEWS.MRP, isValid: false, plant: mappedMrp };
                }
            }
        }
    }
    

    // Check Mandatory Fields for Sales View
    if (selectedViews.includes(MATERIAL_VIEWS.SALES)) {
      const mandatoryFieldsOfSales = allViewsMandatoryFields?.[MATERIAL_VIEWS.SALES];
      if (mandatoryFieldsOfSales) {
        if (!payloadData[MATERIAL_VIEWS.SALES]) {
          const missingFields = getMissingValues(mandatoryFieldsOfSales, payloadData[MATERIAL_VIEWS.SALES]);
          return { missingFields, viewType: MATERIAL_VIEWS.SALES, isValid: false };
        } else {
          const { validCount } = checkOrgsKeys(orgRow,MATERIAL_VIEWS.SALES);
          const { totalCount, allValid } = checkObjectAndCount(payloadData[MATERIAL_VIEWS.SALES],mandatoryFieldsOfSales);
          if (totalCount === validCount) {
            if (!allValid) {
              const missingFields = getMissingValues(mandatoryFieldsOfSales, payloadData[MATERIAL_VIEWS.SALES]);
              return { missingFields, viewType: MATERIAL_VIEWS.SALES, isValid: false };
            } else {
              if (!checkAllKeys(payloadData[MATERIAL_VIEWS.SALES], mandatoryFieldsOfSales)) {
                const missingFields = getMissingValues(mandatoryFieldsOfSales, payloadData[MATERIAL_VIEWS.SALES]);
                return { missingFields, viewType: MATERIAL_VIEWS.SALES, isValid: false };
              }
            }
          } else {
            const missingFields = getMissingValues(mandatoryFieldsOfSales, payloadData[MATERIAL_VIEWS.SALES]);
            return { missingFields, viewType: MATERIAL_VIEWS.SALES, isValid: false };
          }
        }
      }
    }
    return { missingFields: null, isValid: true };
  };

  return { checkValidation };
};

export default useValidation;
