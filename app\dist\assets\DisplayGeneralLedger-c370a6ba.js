import{r as y,s as fr,b as br,u as vr,q as R,bh as Sr,a as n,j as a,b4 as xe,T as A,B as Y,br as fe,G as C,aC as Mn,bt as jn,bs as Ne,x as D,ab as We,F as U,bX as yr,C as Ar,z as Un,V as Vn,aF as Wn,I as Ge,aG as Gn,W as _n,ar as kn,E as Hn,X as Kn,t as d,cb as Er,cc as gr,al as Tr,b1 as Nr,b8 as O,bm as Ie,aD as _,aE as k,aB as x,K as N,eV as z,cd as Ir,eU as qe,bq as ae,ai as ee,f3 as qr,bp as Fr}from"./index-17b8d91e.js";import{l as Xn}from"./lookup-1dcf10ac.js";import{d as zr}from"./ArrowCircleLeftOutlined-2a09f8e2.js";import{d as Fe}from"./EditOutlined-36c8ca4d.js";import{R as Br}from"./ReusableAttachementAndComments-bab6bbfc.js";import{d as Or,C as $r}from"./ChangeLog-0f47d713.js";import{M as Mr,a as jr}from"./UtilDoc-d76e2af6.js";import{E as be}from"./EditableFieldForGL-e4ab886c.js";import{T as Yn}from"./Timeline-36ba02ac.js";import{t as Jn,T as Qn,a as Zn,b as Pn,c as Ln,d as wn}from"./TimelineSeparator-0839d5e3.js";import{S as Ur,a as Vr,b as Wr}from"./Stepper-88e4fb0c.js";import"./CloudUpload-27b6d63e.js";import"./utilityImages-067c3dc2.js";import"./Add-98854918.js";import"./Delete-9f4d7a45.js";/* empty css            */import"./FileDownloadOutlined-c800f30b.js";import"./VisibilityOutlined-315d5644.js";import"./DeleteOutlined-888bfc33.js";import"./Slider-3eb7e770.js";import"./DatePicker-68227989.js";import"./dateViewRenderers-34586552.js";import"./useSlotProps-e34e1e13.js";import"./InputAdornment-5b0053c5.js";import"./CSSTransition-30917e2c.js";import"./useMediaQuery-6a073ac5.js";import"./DesktopDatePicker-07c19cde.js";import"./useMobilePicker-9978caff.js";import"./clsx-a965ebfb.js";const fo=()=>{var an,pn,hn,un,mn,Cn,xn,fn,bn,vn,Sn,yn,An,En,gn,Tn,Nn,In,qn,Fn,zn,Bn,On;const[I,_e]=y.useState(!1);y.useState(0);const[pe,Rn]=y.useState(!0);y.useState({});const[F,Dn]=y.useState([]),[f,ve]=y.useState(0),[ke,et]=y.useState([]),[u,nt]=y.useState();y.useState(!1),y.useState([]);const[tt,b]=y.useState(!1),[rt,E]=y.useState(!1),[He,v]=y.useState(""),[ot,S]=y.useState(!1),[Gr,g]=y.useState(!0),[_r,T]=y.useState(!1),[st,m]=y.useState(!1),[ct,ze]=y.useState(!1),[it,Be]=y.useState(!1),[lt,Oe]=y.useState(!1),[ne,Se]=y.useState(""),[se,dt]=y.useState([]),[at,Ke]=y.useState(!1),[ce,pt]=y.useState([]),[ht,Xe]=y.useState(!1),[ut,$e]=y.useState(!0),[mt,Z]=y.useState(!1),[J,P]=y.useState(!0),[Ct,K]=y.useState(!1),[xt,Ye]=y.useState(!1),[ft,Me]=y.useState(""),[Je,bt]=y.useState(0),[Qe,je]=y.useState(""),[vt,St]=y.useState(""),[Ze,yt]=y.useState([]),[ie,At]=y.useState(""),[Et,Pe]=y.useState(!1),H=fr(),Le=br(),gt=vr(),we=R(s=>s.appSettings);let ye=R(s=>{var i;return(i=s.userManagement.entitiesAndActivities)==null?void 0:i["General Ledger"]}),p=R(s=>{var i;return(i=s==null?void 0:s.initialData)==null?void 0:i.IWMMyTask}),c=R(s=>s.userManagement.userData),e=gt.state,Tt=R(s=>s.generalLedger.requiredFields),Ae=R(s=>s.edit.selectedCheckBox);console.log("extended",Ae);let t=R(s=>s.userManagement.taskData);const o=R(s=>s.edit.payload);console.log("ccroewdata",o);const te=R(s=>s.generalLedger.generalLedgerViewData);console.log(te,"generalLedgerViewData"),console.log("generalLedgerRowData",e),console.log("costCenterDetails",ke),console.log(ne,"Remarks");const Nt=()=>{Pe(!1)};console.log("taskData",p),console.log("taskRowDetails",t),console.log("glids",u);const X=()=>{Se(""),$e(!0),Xe(!1)},It=(s,i)=>{const r=h=>{H(ee({keyName:s,data:h.body})),bt(B=>B+1)},l=h=>{console.log(h)};N(`/${z}/data/${i}`,"get",r,l)},qt=()=>{var s,i;(i=(s=Xn)==null?void 0:s.generalLedger)==null||i.map(r=>{It(r==null?void 0:r.keyName,r==null?void 0:r.endPoint)})},Ft=()=>{var r,l;const s=h=>{H(ee({keyName:"TaxCategory",data:h.body}))},i=h=>{console.log(h)};N(`/${z}/data/getTaxCategory?companyCode=${(r=p==null?void 0:p.body)!=null&&r.compCode?(l=p==null?void 0:p.body)==null?void 0:l.compCode:e==null?void 0:e.compCode}`,"get",s,i)},zt=()=>{var r,l;const s=h=>{H(ee({keyName:"HouseBank",data:h.body}))},i=h=>{console.log(h)};N(`/${z}/data/getHouseBank?companyCode=${(r=p==null?void 0:p.body)!=null&&r.compCode?(l=p==null?void 0:p.body)==null?void 0:l.compCode:e==null?void 0:e.compCode}`,"get",s,i)},Bt=()=>{var r,l;const s=h=>{H(ee({keyName:"FieldStatusGroup",data:h.body}))},i=h=>{console.log(h)};N(`/${z}/data/getFieldStatusGroup?companyCode=${(r=p==null?void 0:p.body)!=null&&r.compCode?(l=p==null?void 0:p.body)==null?void 0:l.compCode:e==null?void 0:e.compCode}`,"get",s,i)},Ot=()=>{var r,l;const s=h=>{H(ee({keyName:"GroupAccountNumber",data:h.body}))},i=h=>{console.log(h)};N(`/${z}/data/getGroupAccountNumber?chartAccount=${(r=p==null?void 0:p.body)!=null&&r.coa?(l=p==null?void 0:p.body)==null?void 0:l.coa:e==null?void 0:e.chartOfAccount}`,"get",s,i)},$t=()=>{var r,l;const s=h=>{H(ee({keyName:"AlternativeAccountNumber",data:h.body}))},i=h=>{console.log(h)};N(`/${z}/data/getAlternativeAccountNumber?chartAccount=${(r=p==null?void 0:p.body)!=null&&r.coa?(l=p==null?void 0:p.body)==null?void 0:l.coa:e==null?void 0:e.chartOfAccount}`,"get",s,i)},Mt=()=>{var r,l;const s=h=>{H(ee({keyName:"AccountGroup",data:h.body}))},i=h=>{console.log(h)};N(`/${z}/data/getAccountGroupCodeDesc?chartAccount=${(r=p==null?void 0:p.body)!=null&&r.coa?(l=p==null?void 0:p.body)==null?void 0:l.coa:e==null?void 0:e.chartOfAccount}`,"get",s,i)};y.useEffect(()=>{At(Sr("GL"))},[]);var j={GeneralLedgerID:u!=null&&u.GeneralLedgeId?u==null?void 0:u.GeneralLedgeId:"",Action:(e==null?void 0:e.requestType)==="Create"?"I":(e==null?void 0:e.requestType)==="Change"?"U":(t==null?void 0:t.processDesc)==="Create"?"I":(t==null?void 0:t.processDesc)==="Change"?"U":(e==null?void 0:e.requestType)==="Extend"||(t==null?void 0:t.processDesc)==="Extend"||(e==null?void 0:e.requestType)==="Extend"?"I":"U",RequestID:"",TaskStatus:"",TaskId:t!=null&&t.taskId?t==null?void 0:t.taskId:"",Remarks:ne||"",Info:"",CreationId:(t==null?void 0:t.processDesc)==="Create"?t==null?void 0:t.subject.slice(3):(e==null?void 0:e.requestType)==="Create"?e==null?void 0:e.requestId.slice(3):"",EditId:(t==null?void 0:t.processDesc)==="Change"?t==null?void 0:t.subject.slice(3):(e==null?void 0:e.requestType)==="Change"?e==null?void 0:e.requestId.slice(3):"",ExtendId:(t==null?void 0:t.processDesc)==="Extend"?t==null?void 0:t.subject.slice(3):(e==null?void 0:e.requestType)==="Extend"&&e.requestId?e==null?void 0:e.requestId.slice(3):"",MassExtendId:"",DeleteId:"",MassCreationId:"",MassEditId:"",MassDeleteId:"",RequestType:(e==null?void 0:e.requestType)==="Create"?"Create":(e==null?void 0:e.requestType)==="Change"?"Change":(e==null?void 0:e.requestType)==="Extend"?"Extend":(t==null?void 0:t.processDesc)==="Create"?"Create":(t==null?void 0:t.processDesc)==="Change"?"Change":((t==null?void 0:t.processDesc)==="Extend","Extend"),ReqCreatedBy:c==null?void 0:c.user_id,ReqCreatedOn:t!=null&&t.createdOn?"/Date("+(t==null?void 0:t.createdOn)+")/":e!=null&&e.createdOn?"/Date("+Date.parse(e==null?void 0:e.createdOn)+")/":"",ReqUpdatedOn:"",RequestStatus:"",Testrun:ut===!0,COA:e!=null&&e.chartOfAccount?e==null?void 0:e.chartOfAccount:u!=null&&u.ChartOfAccount?u==null?void 0:u.ChartOfAccount:"",CompanyCode:e!=null&&e.compCode?e==null?void 0:e.compCode:u!=null&&u.CompCode?u==null?void 0:u.CompCode:"",CoCodeToExtend:Ae[0]?Ae.join(","):u!=null&&u.CompanyCodesExtendedTo?u==null?void 0:u.CompanyCodesExtendedTo:"",GLAccount:e!=null&&e.glAccount?e==null?void 0:e.glAccount:u!=null&&u.GLAccount?u==null?void 0:u.GLAccount:"",Accounttype:o!=null&&o.AccountType?o==null?void 0:o.AccountType:"",AccountGroup:o!=null&&o.AccountGroup?o==null?void 0:o.AccountGroup:"",GLname:o!=null&&o.ShortText?o==null?void 0:o.ShortText:"",Description:o!=null&&o.LongText?o==null?void 0:o.LongText:"",TradingPartner:o!=null&&o.TradingPartner?o==null?void 0:o.TradingPartner:"",GroupAccNo:o!=null&&o.GroupAccountNumber?o==null?void 0:o.GroupAccountNumber:"121100",AccountCurrency:o!=null&&o.AccountCurrency?o==null?void 0:o.AccountCurrency:"",Exchangerate:o!=null&&o.ExchangeRateDifferenceKey?o==null?void 0:o.ExchangeRateDifferenceKey:"",Balanceinlocrcy:(o==null?void 0:o.OnlyBalanceInLocalCurrency)===!0?"X":"",Taxcategory:o!=null&&o.TaxCategory?o==null?void 0:o.TaxCategory:"",Pstnwotax:(o==null?void 0:o.PostingWithoutTaxAllowed)===!0?"X":"",ReconAcc:o!=null&&o.ReconAccountForAccountType?o==null?void 0:o.ReconAccountForAccountType:"",Valuationgrp:o!=null&&o.ValuationGroup?o==null?void 0:o.ValuationGroup:"",AlterAccno:o!=null&&o.AlternativeAccountNumber?o==null?void 0:o.AlternativeAccountNumber:"",Openitmmanage:(o==null?void 0:o.OpenItemManagement)===!0?"X":"",Sortkey:o!=null&&o.SortKey?o==null?void 0:o.SortKey:"",CostEleCategory:o!=null&&o.CostElementCategory?o==null?void 0:o.CostElementCategory:"",FieldStsGrp:o!=null&&o.FieldStatusGroup?o==null?void 0:o.FieldStatusGroup:"",PostAuto:(o==null?void 0:o.PostAutomaticallyOnly)===!0?"X":"",Supplementautopost:(o==null?void 0:o.SupplementAutoPostings)===!0?"X":"",Planninglevel:o!=null&&o.PlanningLevel?o==null?void 0:o.PlanningLevel:"",Relvnttocashflow:(o==null?void 0:o.RelevantToCashFlows)===!0?"X":"",HouseBank:o!=null&&o.HouseBank?o==null?void 0:o.HouseBank:"",AccountId:o!=null&&o.AccountID?o==null?void 0:o.AccountID:"",Interestindicator:o!=null&&o.InterestIndicator?o==null?void 0:o.InterestIndicator:"",ICfrequency:o!=null&&o.InterestCalculationFrequency?o==null?void 0:o.InterestCalculationFrequency:"",KeydateofLIC:o!=null&&o.KeyDateOfLastIntCalc?o==null?void 0:o.KeyDateOfLastIntCalc:"",LastIntrstundate:o!=null&&o.DateOfLastInterestRun?o==null?void 0:o.DateOfLastInterestRun:"",AccmngExistsys:"",Infationkey:"",Tolerancegrp:"",AuthGroup:"",AccountClerk:"",ReconAccReady:"",PostingBlocked:"",PlanningBlocked:""};const jt=()=>{let s=F[f];console.log("activeTabName",s,F);let i=Object.entries(te);console.log("viewDataArray",i);const r={};i.map(l=>{console.log("bottle",l[1]);let h=Object.entries(l[1]);return console.log("notebook",h),h.forEach(B=>{B[1].forEach(q=>{(q==null?void 0:q.fieldType)==="Calendar"?r[q.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("")]=parseInt(q.value.replace("/Date(","").replace(")/","")):r[q.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("")]=q.value})}),l}),console.log("toSetArray",r),H(Ir(r))},Ut=s=>{console.log("compcode",s);const i=l=>{console.log("value",l),H(ee({keyName:"CostElementCategory",data:l.body}))},r=l=>{console.log(l,"error in dojax")};N(`/${z}/data/getCostElementCategory?accountType=${s}`,"get",i,r)},Vt=()=>{var l,h,B,q,ue,me,$;var s=(l=p==null?void 0:p.body)!=null&&l.id?{id:(h=p==null?void 0:p.body)!=null&&h.id?(B=p==null?void 0:p.body)==null?void 0:B.id:"",glAccount:(q=p==null?void 0:p.body)!=null&&q.glAccount?(ue=p==null?void 0:p.body)==null?void 0:ue.glAccount:"",compCode:(me=p==null?void 0:p.body)==null?void 0:me.compCode,reqStatus:($=p==null?void 0:p.body)==null?void 0:$.reqStatus,screenName:(t==null?void 0:t.processDesc)==="Create"?"Create":"Change"}:{id:e!=null&&e.reqStatus?e==null?void 0:e.id:"",glAccount:e!=null&&e.glAccount?e==null?void 0:e.glAccount:"",compCode:e!=null&&e.compCode?e==null?void 0:e.compCode:"",reqStatus:e!=null&&e.reqStatus?e==null?void 0:e.reqStatus:"Approved",screenName:(e==null?void 0:e.requestType)==="Extend"||(e==null?void 0:e.requestType)==="Create"?"Create":((e==null?void 0:e.requestType)==="Change","Change")};const i=w=>{const oe=w.body.viewData,Te=w.body;H(qr(oe));const Ce=Object.keys(oe);Dn(Ce);const xr=["Attachment & Documents"];F.concat(xr);const $n=Ce.map(de=>({category:de,data:oe[de],setIsEditMode:_e}));Ut(oe["Type/Description"]["Control in COA"].find(de=>(de==null?void 0:de.fieldName)==="Account Type").value),et($n),nt(Te),console.log("mappedData",$n,u)},r=w=>{console.log(w)};N(`/${z}/data/displayGeneralLedger`,"post",i,r,s),St(s.screenName)};console.log("ID",u),y.useEffect(()=>{Vt(),Ft(),Bt(),zt(),Mt(),$t(),Ot(),qt()},[]),y.useEffect(()=>{te.length!==0&&jt()},[te]);const Re=()=>{ze(!1)},Wt=()=>{lt?(Be(!1),Oe(!1)):(Be(!1),Le("/masterDataCockpit/generalLedger"))},De=()=>{Pe(!0)},en=()=>Fr(o,Tt,yt),W=()=>{P(!0);const s=en();I?s?(ve(i=>i-1),H(qe())):De():(ve(i=>i-1),H(qe()))},G=()=>{const s=en();I?s?(ve(i=>i+1),H(qe())):De():(ve(i=>i+1),H(qe()))},Ee=()=>{_e(!0),Rn(!1)},Gt=s=>{Ye(s)},nn=()=>{m(!0),X(),ir()},_t=()=>{m(!0),X(),lr()},tn=()=>{m(!0),X(),dr()},ge=()=>{m(!0),X(),ar()},kt=()=>{m(!0),X(),pr()},rn=()=>{m(!0),X(),hr()},on=()=>{m(!0),X(),ur()},Ht=()=>{m(!0),X(),mr()},sn=()=>{m(!0),X(),Cr()},M=()=>{ze(!0)},V=()=>{Be(!0)},Kt=()=>{Ye(!0)},he=()=>{Z(!0);const s=r=>{var l,h,B;r.statusCode===201?(b("Create"),b("Create"),v("All Data has been Validated. General Ledger can be Send for Review"),P(!1),K(!1),S("success"),g(!1),E(!0),V(),T(!0),Oe(!0),P(!1),Z(!1)):(Z(!1),b("Error"),E(!1),v(`${(l=r==null?void 0:r.body)!=null&&l.message[0]?(h=r==null?void 0:r.body)==null?void 0:h.message[0]:(B=r==null?void 0:r.body)==null?void 0:B.value}`),K(!1),S("danger"),g(!1),T(!0),M(),P(!0))},i=r=>{console.log(r)};N(`/${z}/alter/validateSingleGeneralLedger`,"post",s,i,j)},re=()=>{var q,ue,me;Z(!0);var s={glName:o!=null&&o.ShortText?(q=o==null?void 0:o.ShortText)==null?void 0:q.toUpperCase():"",compCode:e!=null&&e.compCode?e==null?void 0:e.compCode:u!=null&&u.CompCode?u==null?void 0:u.CompCode:""};let i="";(ue=te==null?void 0:te["Type/Description"])==null||ue.Description.map($=>{($==null?void 0:$.fieldName)==="Short Text"&&(i=$==null?void 0:$.value)}),console.log((me=s==null?void 0:s.glName)==null?void 0:me.toUpperCase(),i==null?void 0:i.toUpperCase(),"checkingElement");const r=$=>{var w,oe,Te,Ce;$.statusCode===201?(b("Create"),b("Create"),v("All Data has been Validated. General Ledger can be Send for Review"),P(!1),K(!1),S("success"),g(!1),E(!0),V(),T(!0),Oe(!0),(s.compCode!==""||s.glName!=="")&&(P(!1),((w=s==null?void 0:s.glName)==null?void 0:w.toUpperCase())===(i==null?void 0:i.toUpperCase())&&vt==="Change"?Z(!1):N(`/${z}/alter/fetchGlNameNCompCodeDupliChk`,"post",h,B,s)),Z(!1)):(Z(!1),b("Error"),E(!1),v(`${(oe=$==null?void 0:$.body)!=null&&oe.message[0]?(Te=$==null?void 0:$.body)==null?void 0:Te.message[0]:(Ce=$==null?void 0:$.body)==null?void 0:Ce.value}`),K(!1),S("danger"),g(!1),T(!0),M(),P(!0)),handleClose()},l=$=>{console.log($)},h=$=>{$.body.length===0||!$.body.some(w=>w.toUpperCase()===s.glName)?(Z(!1),P(!1),$e(!1)):(Z(!1),b("Duplicate Check"),E(!1),v("There is a direct match for the Short Text. Please change the Short Text."),K(!1),S("danger"),g(!1),T(!0),M(),P(!0))},B=$=>{console.log($)};N(`/${z}/alter/validateSingleGeneralLedger`,"post",r,l,j)},Xt=()=>{(c==null?void 0:c.role)==="MDM Steward"&&((e==null?void 0:e.requestType)==="Create"||(t==null?void 0:t.processDesc)==="Create")?(m(!0),Jt()):(c==null?void 0:c.role)==="MDM Steward"&&((e==null?void 0:e.requestType)==="Change"||(t==null?void 0:t.processDesc)==="Change")?(m(!0),Qt()):(c==null?void 0:c.role)==="MDM Steward"&&((e==null?void 0:e.requestType)==="Extend"||(t==null?void 0:t.processDesc)==="Extend")?(m(!0),Zt()):(c==null?void 0:c.role)==="Approver"&&((e==null?void 0:e.requestType)==="Create"||(t==null?void 0:t.processDesc)==="Create")?(m(!0),Pt()):(c==null?void 0:c.role)==="Approver"&&((e==null?void 0:e.requestType)==="Change"||(t==null?void 0:t.processDesc)==="Change")?(m(!0),Lt()):(c==null?void 0:c.role)==="Approver"&&((e==null?void 0:e.requestType)==="Extend"||(t==null?void 0:t.processDesc)==="Extend")&&(m(!0),wt())},Yt=()=>{(c==null?void 0:c.role)==="Finance"&&((e==null?void 0:e.requestType)==="Create"||(t==null?void 0:t.processDesc)==="Create")&&I?rn():(c==null?void 0:c.role)==="MDM Steward"&&((e==null?void 0:e.requestType)==="Create"||(t==null?void 0:t.processDesc)==="Create")&&!I?tn():(c==null?void 0:c.role)==="Approver"&&((e==null?void 0:e.requestType)==="Create"||(t==null?void 0:t.processDesc)==="Create")&&!I?sn():(c==null?void 0:c.role)==="Finance"&&!(e!=null&&e.requestType)&&I||(c==null?void 0:c.role)==="Finance"&&((e==null?void 0:e.requestType)==="Change"||(t==null?void 0:t.processDesc)==="Change")&&I?ge():(c==null?void 0:c.role)==="MDM Steward"&&((e==null?void 0:e.requestType)==="Change"||(t==null?void 0:t.processDesc)==="Change")&&!I?nn():(c==null?void 0:c.role)==="Approver"&&((e==null?void 0:e.requestType)==="Change"||(t==null?void 0:t.processDesc)==="Change")&&!I?on():(c==null?void 0:c.role)==="Finance"&&((e==null?void 0:e.requestType)==="Extend"||(t==null?void 0:t.processDesc)==="Extend")&&I?kt():(c==null?void 0:c.role)==="MDM Steward"&&((e==null?void 0:e.requestType)==="Extend"||(t==null?void 0:t.processDesc)==="Extend")&&!I?_t():(c==null?void 0:c.role)==="Approver"&&((e==null?void 0:e.requestType)==="Extend"||(t==null?void 0:t.processDesc)==="Extend")&&!I&&Ht()},Jt=()=>{const s=r=>{m(!1),r.statusCode===200?(console.log("success"),b("Create"),v(`General Ledger Submitted for Correction with ID NLS${r.body}`),S("success"),g(!1),E(!0),V(),T(!0)):(b("Error"),E(!1),v("Failed Submitting General Ledger for Correction"),S("danger"),g(!1),T(!0),M(),m(!1)),L()},i=r=>{console.log(r)};console.log("remarkssssssssss",ne),N(`/${z}/alter/generalLedgerSendForCorrection`,"post",s,i,j)},Qt=()=>{const s=r=>{m(!1),r.statusCode===200?(console.log("success"),b("Create"),v(`General Ledger Submitted for Correction with ID CLS${r.body}`),S("success"),g(!1),E(!0),V(),T(!0)):(b("Error"),E(!1),v("Failed Submitting General Ledger for Correction"),S("danger"),g(!1),T(!0),M(),m(!1)),L()},i=r=>{console.log(r)};console.log("hsdfjgdh",j),N(`/${z}/alter/changeGeneralLedgerSendForCorrection`,"post",s,i,j)},Zt=()=>{const s=r=>{m(!1),r.statusCode===200?(console.log("success"),b("Create"),v(`General Ledger Submitted for Correction with ID ELS${r.body}`),S("success"),g(!1),E(!0),V(),T(!0)):(b("Error"),E(!1),v("Failed Submitting General Ledger for Correction"),S("danger"),g(!1),T(!0),M(),m(!1)),L()},i=r=>{console.log(r)};console.log("hsdfjgdh",j),N(`/${z}/alter/extendGeneralLedgerSendForCorrection`,"post",s,i,j)},Pt=()=>{const s=r=>{m(!1),r.statusCode===200?(console.log("success"),b("Create"),v(`General Ledger Submitted for Correction with ID NLS${r.body}`),S("success"),g(!1),E(!0),V(),T(!0)):(b("Error"),E(!1),v("Failed Submitting General Ledger for Correction"),S("danger"),g(!1),T(!0),M(),m(!1)),L()},i=r=>{console.log(r)};N(`/${z}/alter/generalLedgerSendForReview`,"post",s,i,j)},Lt=()=>{const s=r=>{m(!1),r.statusCode===200?(console.log("success"),b("Create"),v(`General Ledger Submitted for Correction with ID CLS${r.body}`),S("success"),g(!1),E(!0),V(),T(!0)):(b("Error"),E(!1),v("Failed Submitting General Ledger for Correction"),S("danger"),g(!1),T(!0),M(),m(!1)),L()},i=r=>{console.log(r)};console.log("remarksssaaaa",ne),N(`/${z}/alter/changeGeneralLedgerSendForReview`,"post",s,i,j)},wt=()=>{const s=r=>{m(!1),r.statusCode===200?(console.log("success"),b("Create"),v(`General Ledger Submitted for Correction with ID ELS${r.body}`),S("success"),g(!1),E(!0),V(),T(!0)):(b("Error"),E(!1),v("Failed Submitting General Ledger for Correction"),S("danger"),g(!1),T(!0),M(),m(!1)),L()},i=r=>{console.log(r)};N(`/${z}/alter/extendGeneralLedgerSendForReview`,"post",s,i,j)},cn=[{field:"id",headerName:"Document ID",flex:1,hide:!0},{field:"docType",headerName:"Type",flex:1},{field:"docName",headerName:"Document Name",flex:1},{field:"uploadedOn",headerName:"Uploaded On",flex:1,align:"center",headerAlign:"center"},{field:"uploadedBy",headerName:"Uploaded By",sortable:!1,flex:1},{field:"action",headerName:"Action",sortable:!1,filterable:!1,align:"center",headerAlign:"center",flex:1,renderCell:s=>a(U,{children:[n(Mr,{index:s.row.id,name:s.row.docName}),n(jr,{index:s.row.id,name:s.row.docName})]})}],Rt=()=>{let s=t!=null&&t.subject?t==null?void 0:t.subject:e==null?void 0:e.requestId,i=r=>{var l=[];r.documentDetailDtoList.forEach(h=>{var B={id:h.documentId,docType:h.fileType,docName:h.fileName,uploadedOn:We(h.docCreationDate).format(we.date),uploadedBy:h.createdBy};l.push(B)}),dt(l)};N(`/${ae}/documentManagement/getDocByRequestId/${s}`,"get",i)},Dt=()=>{let s=t!=null&&t.subject?t==null?void 0:t.subject:e==null?void 0:e.requestId,i=l=>{console.log("commentsdata",l);var h=[];l.body.forEach(B=>{var q={id:B.requestId,comment:B.comment,user:B.createdByUser,createdAt:B.updatedAt};h.push(q)}),pt(h)},r=l=>{console.log(l)};N(`/${z}/activitylog/fetchTaskDetailsForRequestId?requestId=${s}`,"get",i,r)},er=()=>{nr()},nr=()=>{S(!1),M(),b("Confirm"),v("Do You Want to Save as Draft ?"),K(!0),Me("proceed"),je("Create")},Ue=()=>{tr()},tr=()=>{S(!1),M(),b("Confirm"),v("Do You Want to Save as Draft?"),K(!0),Me("proceed"),je("Change")},rr=()=>{or()},or=()=>{S(!1),M(),b("Confirm"),v("Do You Want to Save as Draft?"),K(!0),Me("proceed"),je("Extend")},sr=()=>{if(console.log(Qe,"dialogType"),Ve(),m(!0),Qe==="Change"){const s=r=>{if(m(!1),r.statusCode===200){console.log("success"),b("Create"),v(`General Ledger Saved As Draft with ID CLS${r.body} `),K(!1),S("success"),g(!1),E(!0),V(),T(!0);const l={artifactId:ie,createdBy:c==null?void 0:c.emailId,artifactType:"GeneralLedger",requestId:`CLS${r==null?void 0:r.body}`},h=q=>{console.log("Second API success",q)},B=q=>{console.error("Second API error",q)};N(`/${ae}/documentManagement/updateDocRequestId`,"post",h,B,l)}else b("Error"),E(!1),v("Failed Saving General Ledger"),K(!1),S("danger"),g(!1),T(!0),M();handleClose()},i=r=>{console.log(r)};N(`/${z}/alter/changeGeneralLedgerAsDraft`,"post",s,i,j)}else{const s=r=>{if(Ve(),m(!1),r.statusCode===200){console.log("success"),b("Create"),v(`Cost Center Saved As Draft with ID NLS${r.body} `),K(!1),S("success"),g(!1),E(!0),V(),T(!0);const l={artifactId:ie,createdBy:c==null?void 0:c.emailId,artifactType:"GeneralLedger",requestId:`NCS${r==null?void 0:r.body}`},h=q=>{console.log("Second API success",q)},B=q=>{console.error("Second API error",q)};N(`/${ae}/documentManagement/updateDocRequestId`,"post",h,B,l)}else b("Error"),E(!1),v("Failed Saving General Ledger"),K(!1),S("danger"),g(!1),T(!0),M();handleClose()},i=r=>{console.log(r)};N(`/${z}/alter/generalLedgerAsDraft`,"post",s,i,j)}},Q=()=>{$e(!1),Xe(!0)},cr=()=>{var s,i;Je==((i=(s=Xn)==null?void 0:s.generalLedger)==null?void 0:i.length)?m(!1):m(!0)};y.useEffect(()=>{Rt(),Dt()},[]),y.useEffect(()=>{cr()},[Je]);const ln=F.map(s=>{const i=ke.filter(r=>{var l;return((l=r.category)==null?void 0:l.split(" ")[0])==(s==null?void 0:s.split(" ")[0])});if(i.length!=0)return{category:s==null?void 0:s.split(" ")[0],data:i[0].data}}).map((s,i)=>{if(console.log("categoryy",s.category),(s==null?void 0:s.category)=="Type/Description"&&f==0)return[n(C,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(s.data).map(r=>a(C,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...xe},children:[n(A,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:r}),n(Y,{sx:{width:"100%"},children:n(fe,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:n(C,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:s.data[r].map(l=>(console.log("fieldDatatttt",l),n(be,{length:l.maxLength,label:l.fieldName,value:l.value,visibility:l.visibility,onSave:h=>handleFieldSave(l.fieldName,h),data:o,isEditMode:I,type:l.fieldType,field:l,taskRequestId:e==null?void 0:e.requestId,generalLedgerRowData:e})))})})})]},r))},s.category)];if((s==null?void 0:s.category)=="Control"&&f==1)return[n(C,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(s.data).map(r=>a(C,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...xe},children:[n(A,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:r}),n(Y,{sx:{width:"100%"},children:n(fe,{children:n(C,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:s.data[r].map(l=>n(be,{label:l.fieldName,value:(l==null?void 0:l.value)==="X",onSave:h=>handleFieldSave(l.fieldName,h),isEditMode:I,type:l.fieldType,taskRequestId:e==null?void 0:e.requestId,visibility:l.visibility,data:o,generalLedgerRowData:e},l.fieldName))})})})]},r))},s.category)];if((s==null?void 0:s.category)=="Create/Bank/Interest"&&f==2)return[n(C,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(s.data).map(r=>a(C,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...xe},children:[n(A,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:r}),n(Y,{sx:{width:"100%"},children:n(fe,{children:n(C,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:s.data[r].map(l=>n(be,{label:l.fieldName,value:l.value,onSave:h=>handleFieldSave(l.fieldName,h),isEditMode:I,type:l.fieldType,taskRequestId:e==null?void 0:e.requestId,data:o,generalLedgerRowData:e},l.fieldName))})})})]},r))},s.category)];if((s==null?void 0:s.category)=="Keyword/Translation"&&f==3)return[n(C,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(s.data).map(r=>a(C,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...xe},children:[n(A,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:r}),n(Y,{sx:{width:"100%"},children:n(fe,{children:n(C,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:s.data[r].map(l=>n(be,{label:l.fieldName,value:l.value,onSave:h=>handleFieldSave(l.fieldName,h),isEditMode:I,type:l.fieldType,taskRequestId:e==null?void 0:e.requestId,visibility:l.visibility,data:o,generalLedgerRowData:e},l.fieldName))})})})]},r))},s.category)];if((s==null?void 0:s.category)=="Information"&&f==4)return[n(C,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(s.data).map(r=>a(C,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...xe},children:[n(A,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:r}),n(Y,{sx:{width:"100%"},children:n(fe,{children:n(C,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:s.data[r].map(l=>n(be,{label:l.fieldName,value:l.value,onSave:h=>handleFieldSave(l.fieldName,h),isEditMode:I,type:l.fieldType,taskRequestId:e==null?void 0:e.requestId,visibility:l.visibility,data:o,generalLedgerRowData:e},l.fieldName))})})})]},r))},s.category)];if((s==null?void 0:s.category)=="Attachments")return[n(U,{children:I?a(U,{children:[n(Br,{title:"GeneralLedger",useMetaData:!1,artifactId:ie,artifactName:"GeneralLedger"}),a(Ne,{sx:{padding:"1rem 1rem 0rem 1rem"},children:[n(C,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:n(A,{variant:"h6",children:n("strong",{children:"Attachments"})})}),!!se.length&&n(Mn,{width:"100%",rows:se,columns:cn,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action"}),!se.length&&n(A,{variant:"body2",children:"No Attachments Found"}),n("br",{}),n(A,{variant:"h6",children:"Comments"}),!!ce.length&&n(Yn,{sx:{[`& .${Jn.root}:before`]:{flex:0,padding:0}},children:ce.map(r=>a(Qn,{children:[a(Zn,{children:[n(Pn,{children:n(jn,{sx:{color:"#757575"}})}),n(Ln,{})]}),n(wn,{sx:{py:"12px",px:2},children:n(Ne,{elevation:0,sx:{border:1,borderColor:"#C4C4C4",borderRadius:"8px",width:"650px"},children:n(Y,{sx:{padding:"1rem"},children:a(D,{spacing:1,children:[n(C,{sx:{display:"flex",justifyContent:"space-between"},children:n(A,{sx:{textAlign:"right",color:" #757575",fontWeight:"500",fontSize:"12px"},children:We(r.createdAt).format("DD MMM YYYY")})}),n(A,{sx:{fontSize:"12px",color:" #757575",fontWeight:"500"},children:r.user}),n(A,{sx:{fontSize:"12px",color:"#1D1D1D",fontWeight:"600"},children:r.comment})]})})})})]}))}),!ce.length&&n(A,{variant:"body2",children:"No Comments Found"}),n("br",{})]})]}):a(Ne,{sx:{padding:"1rem 1rem 0rem 1rem"},children:[n(C,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:n(A,{variant:"h6",children:n("strong",{children:"Attachments"})})}),!!se.length&&n(Mn,{width:"100%",rows:se,columns:cn,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action"}),!se.length&&n(A,{variant:"body2",children:"No Attachments Found"}),n("br",{}),n(A,{variant:"h6",children:"Comments"}),!!ce.length&&n(Yn,{sx:{[`& .${Jn.root}:before`]:{flex:0,padding:0}},children:ce.map(r=>a(Qn,{children:[a(Zn,{children:[n(Pn,{children:n(jn,{sx:{color:"#757575"}})}),n(Ln,{})]}),n(wn,{sx:{py:"12px",px:2},children:n(Ne,{elevation:0,sx:{border:1,borderColor:"#C4C4C4",borderRadius:"8px",width:"650px"},children:n(Y,{sx:{padding:"1rem"},children:a(D,{spacing:1,children:[n(C,{sx:{display:"flex",justifyContent:"space-between"},children:n(A,{sx:{textAlign:"right",color:" #757575",fontWeight:"500",fontSize:"14px"},children:We(r.createdAt).format(we.date)})}),n(A,{sx:{fontSize:"14px",color:" #757575",fontWeight:"500"},children:r.user}),n(A,{sx:{fontSize:"14px",color:"#1D1D1D",fontWeight:"600"},children:r.comment})]})})})})]}))}),!ce.length&&n(A,{variant:"body2",children:"No Comments Found"}),n("br",{})]})})]}),ir=()=>{const s=r=>{m(),r.statusCode===200?(console.log("success"),b("Create"),v(`General Ledger Submitted for Approval with ID CLS${r.body}`),S("success"),g(!1),E(!0),V(),T(!0),m(!1)):(b("Approve"),E(!1),v("Failed Submitting General Ledger"),S("danger"),g(!1),T(!0),M(),m(!1)),handleClose()},i=r=>{console.log(r)};N(`/${z}/alter/changeGeneralLedgerApprovalSubmit`,"post",s,i,j)},lr=()=>{const s=r=>{m(),r.statusCode===200?(console.log("success"),b("Create"),v(`General Ledger Submitted for Approval with ID ELS${r.body}`),S("success"),g(!1),E(!0),V(),T(!0),m(!1)):(b("Approve"),E(!1),v("Failed Submitting General Ledger"),S("danger"),g(!1),T(!0),M(),m(!1)),handleClose()},i=r=>{console.log(r)};N(`/${z}/alter/extendGeneralLedgerApprovalSubmit`,"post",s,i,j)},dr=()=>{const s=r=>{m(),r.statusCode===200?(console.log("success"),b("Create"),v(`General Ledger Submitted for Approval with ID NLS${r.body}`),S("success"),g(!1),E(!0),V(),T(!0),m(!1)):(b("Error"),E(!1),v("Failed Submitting General Ledger"),S("danger"),g(!1),T(!0),M(),m(!1)),handleClose()},i=r=>{console.log(r)};N(`/${z}/alter/generalLedgerApprovalSubmit`,"post",s,i,j)},ar=()=>{const s=r=>{if(m(),r.statusCode===200){console.log("success"),b("Create"),v(`General Ledger Submitted For Review with ID CLS${r.body} `),S("success"),g(!1),E(!0),V(),T(!0),m(!1);const l={artifactId:ie,createdBy:c==null?void 0:c.emailId,artifactType:"GeneralLedger",requestId:`CLS${r==null?void 0:r.body}`},h=q=>{console.log("Second API success",q)},B=q=>{console.error("Second API error",q)};N(`/${ae}/documentManagement/updateDocRequestId`,"post",h,B,l)}else b("Error"),E(!1),v("Failed Submitting General Ledger"),S("danger"),g(!1),T(!0),M(),m(!1);handleClose()},i=r=>{console.log(r)};N(`/${z}/alter/changeGeneralLedgerSubmitForReview`,"post",s,i,j)},pr=()=>{const s=r=>{if(m(),r.statusCode===200){console.log("success"),b("Create"),v(`General Ledger Submitted For Review with ID ELS${r.body} `),S("success"),g(!1),E(!0),V(),T(!0),m(!1);const l={artifactId:ie,createdBy:c==null?void 0:c.emailId,artifactType:"GeneralLedger",requestId:`ELS${r==null?void 0:r.body}`},h=q=>{console.log("Second API success",q)},B=q=>{console.error("Second API error",q)};N(`/${ae}/documentManagement/updateDocRequestId`,"post",h,B,l)}else b("Error"),E(!1),v("Failed Submitting General Ledger"),S("danger"),g(!1),T(!0),M(),m(!1);handleClose()},i=r=>{console.log(r)};N(`/${z}/alter/extendGeneralLedgerSubmitForReview`,"post",s,i,j)},hr=()=>{const s=r=>{if(m(!1),r.statusCode===200){console.log("success"),b("Create"),v(`General Ledger Submitted for Review with ID NLS${r.body} `),S("success"),g(!1),E(!0),V(),T(!0),m(!1);const l={artifactId:ie,createdBy:c==null?void 0:c.emailId,artifactType:"GeneralLedger",requestId:`NLS${r==null?void 0:r.body}`},h=q=>{console.log("Second API success",q)},B=q=>{console.error("Second API error",q)};N(`/${ae}/documentManagement/updateDocRequestId`,"post",h,B,l)}else b("Error"),E(!1),v("Failed Saving the Data"),S("danger"),g(!1),T(!0),M(),m(!1);handleClose()},i=r=>{console.log(r)};N(`/${z}/alter/generalLedgerSubmitForReview`,"post",s,i,j)},ur=()=>{const s=r=>{m(),r.statusCode===201?(console.log("success"),b("Create"),v(`${r.message}`),S("success"),g(!1),E(!0),V(),T(!0),m(!1)):(b("Error"),E(!1),v("Failed Approving General Ledger"),S("danger"),g(!1),T(!0),M(),m(!1)),handleClose()},i=r=>{console.log(r)};N(`/${z}/alter/changeGeneralLedgerApproved`,"post",s,i,j)},mr=()=>{const s=r=>{m(),r.statusCode===201?(console.log("success"),b("Create"),v(`${r.message}`),S("success"),g(!1),E(!0),V(),T(!0),m(!1)):(b("Error"),E(!1),v("Failed Approving General Ledger"),S("danger"),g(!1),T(!0),M(),m(!1)),handleClose()},i=r=>{console.log(r)};N(`/${z}/alter/extendGeneralLedgerApproved`,"post",s,i,j)},Cr=()=>{const s=r=>{m(),r.statusCode===201?(console.log("success"),b("Create"),v(`${r.message}`),S("success"),g(!1),E(!0),V(),T(!0),m(!1)):(b("Error"),E(!1),v("Failed Approving the General Ledger"),S("danger"),g(!1),T(!0),M(),m(!1)),handleClose()},i=r=>{console.log(r)};N(`/${z}/alter/createGeneralLedgerApproved`,"post",s,i,j)},le=()=>{Ke(!0)},L=()=>{Se(""),Ke(!1)},dn=s=>{const i=s.target.value;if(i.length>0&&i[0]===" ")Se(i.trimStart());else{let r=i.toUpperCase();Se(r)}},Ve=()=>{ze(!1)};return console.log("factorsarray",F),n(U,{children:st===!0?n(yr,{}):a("div",{style:{backgroundColor:"#FAFCFF"},children:[n(Ar,{dialogState:ct,openReusableDialog:M,closeReusableDialog:Re,dialogTitle:tt,dialogMessage:He,handleDialogConfirm:Re,dialogOkText:"OK",showExtraButton:Ct,showCancelButton:!0,dialogSeverity:ot,handleDialogReject:Ve,handleExtraText:ft,handleExtraButton:sr}),rt&&n(Un,{openSnackBar:it,alertMsg:He,handleSnackBarClose:Wt}),Ze.length!=0&&n(Un,{openSnackBar:Et,alertMsg:"Please fill the following Field: "+Ze.join(", "),handleSnackBarClose:Nt}),a(Vn,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:at,onClose:L,children:[a(Wn,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[n(A,{variant:"h6",children:"Remarks"}),n(Ge,{sx:{width:"max-content"},onClick:L,children:n(Gn,{})})]}),n(_n,{sx:{padding:".5rem 1rem"},children:n(D,{children:n(Y,{sx:{minWidth:400},children:n(kn,{sx:{height:"auto"},fullWidth:!0,children:n(Hn,{sx:{backgroundColor:"#F5F5F5"},onChange:dn,value:ne,multiline:!0,placeholder:"Enter Remarks for Correction",inputProps:{maxLength:254}})})})})}),a(Kn,{sx:{display:"flex",justifyContent:"end"},children:[n(d,{sx:{width:"max-content",textTransform:"capitalize"},onClick:L,children:"Cancel"}),n(d,{className:"button_primary--normal",type:"save",onClick:Xt,variant:"contained",children:"Submit"})]})]}),a(Vn,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:ht,onClose:X,children:[a(Wn,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[n(A,{variant:"h6",children:"Remarks"}),n(Ge,{sx:{width:"max-content"},onClick:X,children:n(Gn,{})})]}),n(_n,{sx:{padding:".5rem 1rem"},children:n(D,{children:n(Y,{sx:{minWidth:400},children:n(kn,{sx:{height:"auto"},fullWidth:!0,children:n(Hn,{sx:{backgroundColor:"#F5F5F5"},value:ne.toUpperCase(),onChange:dn,multiline:!0,placeholder:"Enter Remarks",inputProps:{maxLength:254}})})})})}),a(Kn,{sx:{display:"flex",justifyContent:"end"},children:[n(d,{sx:{width:"max-content",textTransform:"capitalize"},onClick:X,children:"Cancel"}),n(d,{className:"button_primary--normal",type:"save",onClick:Yt,variant:"contained",children:"Submit"})]})]}),n(Er,{sx:{color:"#fff",zIndex:s=>s.zIndex.drawer+1},open:mt,children:n(gr,{color:"inherit"})}),a(C,{container:!0,sx:Tr,children:[a(C,{item:!0,md:12,style:{padding:"16px",display:"flex"},children:[a(C,{md:9,sx:{display:"flex"},children:[n(C,{children:n(Ge,{color:"primary","aria-label":"upload picture",component:"label",sx:Nr,children:n(zr,{sx:{fontSize:"25px",color:"#000000"},onClick:()=>{Le("/masterDataCockpit/generalLedger")}})})}),a(C,{children:[I?(t==null?void 0:t.processDesc)==="Create"||(e==null?void 0:e.requestType)==="Create"?n(U,{children:a(C,{item:!0,md:12,children:[n(A,{variant:"h3",children:n("strong",{children:"Create General Ledger "})}),n(A,{variant:"body2",color:"#777",children:"This view edits the details of the General Ledger"})]})}):(t==null?void 0:t.processDesc)==="Change"||(e==null?void 0:e.requestType)==="Change"?n(U,{children:a(C,{item:!0,md:12,children:[n(A,{variant:"h3",children:n("strong",{children:"Change General Ledger "})}),n(A,{variant:"body2",color:"#777",children:"This view edits the details of the General Ledger"})]})}):(t==null?void 0:t.processDesc)==="Extend"||(e==null?void 0:e.requestType)==="Extend"?n(U,{children:a(C,{item:!0,md:12,children:[n(A,{variant:"h3",children:n("strong",{children:"Extend General Ledger "})}),n(A,{variant:"body2",color:"#777",children:"This view edits the details of the General Ledger"})]})}):n(U,{children:a(C,{item:!0,md:12,children:[n(A,{variant:"h3",children:n("strong",{children:"Change General Ledger "})}),n(A,{variant:"body2",color:"#777",children:"This view edits the details of the General Ledger"})]})}):"",pe?a(C,{item:!0,md:12,children:[n(A,{variant:"h3",children:n("strong",{children:"Display General Ledger "})}),n(A,{variant:"body2",color:"#777",children:"This view displays the details of the General Ledger"})]}):""]})]}),a(C,{md:3,sx:{display:"flex",justifyContent:"flex-end"},gap:2,children:[e!=null&&e.requestId||t!=null&&t.processDesc?n(C,{children:n(d,{variant:"outlined",size:"small",sx:O,onClick:Kt,title:"Change Log",children:n(Or,{sx:{padding:"2px"},fontSize:"small"})})}):"",xt&&n($r,{open:!0,closeModal:Gt,requestId:e!=null&&e.requestId?e==null?void 0:e.requestId:t==null?void 0:t.subject,requestType:e!=null&&e.requestType?e==null?void 0:e.requestType:(an=p==null?void 0:p.body)==null?void 0:an.processDesc,pageName:"generalLedger",controllingArea:e!=null&&e.controllingArea?e==null?void 0:e.controllingArea:(pn=p==null?void 0:p.body)==null?void 0:pn.compCode,centerName:e!=null&&e.costCenter?e==null?void 0:e.costCenter:(hn=p==null?void 0:p.body)==null?void 0:hn.glAccount}),Ie(ye,"General Ledger","ChangeGL")&&((c==null?void 0:c.role)==="Super User"&&(e!=null&&e.requestType)&&((un=t==null?void 0:t.itmStatus)==null?void 0:un.toUpperCase())!=="OPEN"&&pe?n(C,{gap:1,sx:{display:"flex"},children:n(C,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:n(U,{children:n(C,{item:!0,children:a(d,{variant:"outlined",size:"small",sx:O,onClick:Ee,children:["Fill Details",n(Fe,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(c==null?void 0:c.role)==="Finance"&&(e!=null&&e.requestType||t!=null&&t.processDesc)&&((mn=t==null?void 0:t.itmStatus)==null?void 0:mn.toUpperCase())!=="OPEN"&&pe?n(C,{gap:1,sx:{display:"flex"},children:n(C,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:n(U,{children:n(C,{item:!0,children:a(d,{variant:"outlined",size:"small",sx:O,onClick:Ee,children:["Fill Details",n(Fe,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(c==null?void 0:c.role)==="Super User"&&!(e!=null&&e.requestType)&&((Cn=t==null?void 0:t.itmStatus)==null?void 0:Cn.toUpperCase())!=="OPEN"&&pe?n(C,{gap:1,sx:{display:"flex"},children:n(C,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:n(U,{children:n(C,{item:!0,children:a(d,{variant:"outlined",size:"small",sx:O,onClick:Ee,children:["Change",n(Fe,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(c==null?void 0:c.role)==="Finance"&&!(e!=null&&e.requestType)&&((xn=t==null?void 0:t.itmStatus)==null?void 0:xn.toUpperCase())!=="OPEN"&&pe?n(C,{gap:1,sx:{display:"flex"},children:n(C,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:n(U,{children:n(C,{item:!0,children:a(d,{variant:"outlined",size:"small",sx:O,onClick:Ee,children:["Change",n(Fe,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):"")]})]}),n(C,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:a(Y,{width:"100%",sx:{marginLeft:"40px"},children:[n(C,{item:!0,sx:{paddingTop:"2px !important"},children:a(D,{flexDirection:"row",children:[n("div",{style:{width:"15%"},children:n(A,{variant:"body2",color:"#777",children:"Chart of Accounts"})}),a(A,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[":"," ",e!=null&&e.chartOfAccount?e==null?void 0:e.chartOfAccount:u!=null&&u.ChartOfAccount?u==null?void 0:u.ChartOfAccount:""]})]})}),(e==null?void 0:e.requestType)!="Extend"?n(C,{item:!0,sx:{paddingTop:"2px !important"},children:a(D,{flexDirection:"row",children:[n("div",{style:{width:"15%"},children:n(A,{variant:"body2",color:"#777",children:"Company Code"})}),a(A,{variant:"body2",fontWeight:"bold",children:[":"," ",e!=null&&e.compCode?e==null?void 0:e.compCode:u!=null&&u.CompCode?u==null?void 0:u.CompCode:""]})]})}):n(U,{children:n(C,{item:!0,sx:{paddingTop:"2px !important"},children:a(D,{flexDirection:"row",children:[n("div",{style:{width:"15%"},children:n(A,{variant:"body2",color:"#777",children:"Reference Company Code"})}),a(A,{variant:"body2",fontWeight:"bold",children:[":"," ",e!=null&&e.compCode?e==null?void 0:e.compCode:u!=null&&u.CompCode?u==null?void 0:u.CompCode:""]})]})})}),n(C,{item:!0,sx:{paddingTop:"2px !important"},children:a(D,{flexDirection:"row",children:[n("div",{style:{width:"15%"},children:n(A,{variant:"body2",color:"#777",children:"G/L Account"})}),a(A,{variant:"body2",fontWeight:"bold",children:[":"," ",e!=null&&e.glAccount?e==null?void 0:e.glAccount:u!=null&&u.GLAccount?u==null?void 0:u.GLAccount:""]})]})}),n(C,{item:!0,sx:{paddingTop:"2px !important"},children:a(D,{flexDirection:"row",children:[n("div",{style:{width:"15%"},children:n(A,{variant:"body2",color:"#777",children:"Extended Company Code"})}),a(A,{variant:"body2",fontWeight:"bold",children:[": ",Ae.join(", ")]})]})})]})}),a(C,{container:!0,style:{marginLeft:25},children:[n(Ur,{activeStep:f,sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},"aria-label":"mui tabs example",children:F.map((s,i)=>n(Vr,{children:n(Wr,{sx:{fontWeight:"700"},children:s})},s))}),ln&&((fn=ln[f])==null?void 0:fn.map((s,i)=>n(Y,{sx:{mb:2,width:"100%"},children:n(A,{variant:"body2",children:s})},i)))]})]}),a(C,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:[Ie(ye,"General Ledger","ChangeGL")&&(!(e!=null&&e.requestType)&&!I?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:a(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})}):n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:a(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})})),Ie(ye,"General Ledger","ChangeGL")&&((c==null?void 0:c.role)==="Super User"&&!(e!=null&&e.requestType)&&((bn=t==null?void 0:t.itmStatus)==null?void 0:bn.toUpperCase())!=="OPEN"&&I?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:a(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t!=null&&t.taskId?"":n(d,{variant:"contained",size:"small",sx:{button_Outlined:O,mr:1},onClick:Ue,children:"Save As Draft"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),f===F.length-1?a(U,{children:[n(d,{variant:"contained",size:"small",sx:{...O,mr:1},onClick:re,children:"Validate"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:ge,disabled:J,children:"Submit For Review"})]}):n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Finance"&&!(e!=null&&e.requestType)&&((vn=t==null?void 0:t.itmStatus)==null?void 0:vn.toUpperCase())!=="OPEN"&&I?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:a(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t!=null&&t.taskId?"":n(d,{variant:"contained",size:"small",sx:{button_Outlined:O,mr:1},onClick:Ue,children:"Save As Draft"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),f===F.length-1?a(U,{children:[n(d,{variant:"contained",size:"small",sx:{...O,mr:1},onClick:re,children:"Validate"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:Q,disabled:J,children:"Submit For Review"})]}):n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})}):""),Ie(ye,"General Ledger","ChangeGL")&&((c==null?void 0:c.role)==="Super User"&&(e==null?void 0:e.requestType)==="Create"&&((Sn=t==null?void 0:t.itmStatus)==null?void 0:Sn.toUpperCase())!=="OPEN"&&!I?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:a(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:he,children:"Validate"}),n(d,{variant:"contained",size:"small",sx:{button_Outlined:O,mr:1},onClick:sn,disabled:J,children:"Approve"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:tn,children:"Submit For Approval"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Super User"&&(e==null?void 0:e.requestType)==="Change"&&((yn=t==null?void 0:t.itmStatus)==null?void 0:yn.toUpperCase())!=="OPEN"&&!I?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:a(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:he,children:"Validate"}),n(d,{variant:"contained",size:"small",sx:{button_Outlined:O,mr:1},onClick:on,disabled:J,children:"Approve"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:nn,children:"Submit For Approval"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="MDM Steward"&&((e==null?void 0:e.requestType)==="Create"||(t==null?void 0:t.processDesc)==="Create")&&((An=t==null?void 0:t.itmStatus)==null?void 0:An.toUpperCase())!=="OPEN"&&!I?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:a(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(d,{variant:"outlined",size:"small",sx:{button_Outlined:O,mr:1},onClick:le,children:"Correction"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:Q,children:"Submit For Approval"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="MDM Steward"&&((e==null?void 0:e.requestType)==="Change"||(t==null?void 0:t.processDesc)==="Change")&&((En=t==null?void 0:t.itmStatus)==null?void 0:En.toUpperCase())!=="OPEN"&&!I?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:a(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(d,{variant:"outlined",size:"small",sx:{button_Outlined:O,mr:1},onClick:le,children:"Correction"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:Q,children:"Submit For Approval"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Approver"&&((e==null?void 0:e.requestType)==="Create"||(t==null?void 0:t.processDesc)==="Create")&&((gn=t==null?void 0:t.itmStatus)==null?void 0:gn.toUpperCase())!=="OPEN"&&!I?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:a(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(d,{variant:"outlined",size:"small",sx:{button_Outlined:O,mr:1},onClick:le,children:"Correction"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:he,children:"Validate"}),n(d,{variant:"contained",size:"small",sx:{button_Outlined:O,mr:1},onClick:Q,disabled:J,children:"Approve"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Approver"&&((e==null?void 0:e.requestType)==="Change"||(t==null?void 0:t.processDesc)==="Change")&&((Tn=t==null?void 0:t.itmStatus)==null?void 0:Tn.toUpperCase())!=="OPEN"&&!I?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:a(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(d,{variant:"outlined",size:"small",sx:{button_Outlined:O,mr:1},onClick:le,children:"Correction"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:he,children:"Validate"}),n(d,{variant:"contained",size:"small",sx:{button_Outlined:O,mr:1},onClick:Q,disabled:J,children:"Approve"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Approver"&&((e==null?void 0:e.requestType)==="Extend"||(t==null?void 0:t.processDesc)==="Extend")&&((Nn=t==null?void 0:t.itmStatus)==null?void 0:Nn.toUpperCase())!=="OPEN"&&!I?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:a(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(d,{variant:"outlined",size:"small",sx:{button_Outlined:O,mr:1},onClick:le,children:"Correction"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:he,children:"Validate"}),n(d,{variant:"contained",size:"small",sx:{button_Outlined:O,mr:1},onClick:Q,children:"Approve"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Super User"&&(e==null?void 0:e.requestType)==="Create"&&((In=t==null?void 0:t.itmStatus)==null?void 0:In.toUpperCase())!=="OPEN"&&I?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:a(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),f===F.length-1?a(U,{children:[n(d,{variant:"contained",size:"small",sx:{...O,mr:1},onClick:re,children:"Validate"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:rn,disabled:J,children:"Submit For Review"})]}):n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Super User"&&(e==null?void 0:e.requestType)==="Change"&&((qn=t==null?void 0:t.itmStatus)==null?void 0:qn.toUpperCase())!=="OPEN"&&I?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:a(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),f===F.length-1?a(U,{children:[n(d,{variant:"contained",size:"small",sx:{...O,mr:1},onClick:re,children:"Validate"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:ge,disabled:J,children:"Submit For Review"})]}):n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Finance"&&((e==null?void 0:e.requestType)==="Create"||(t==null?void 0:t.processDesc)==="Create")&&((Fn=t==null?void 0:t.itmStatus)==null?void 0:Fn.toUpperCase())!=="OPEN"&&I?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:a(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t!=null&&t.taskId?"":n(d,{variant:"contained",size:"small",sx:{button_Outlined:O,mr:1},onClick:er,children:"Save As Draft"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),f===F.length-1?a(U,{children:[n(d,{variant:"contained",size:"small",sx:{...O,mr:1},onClick:re,children:"Validate"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:Q,disabled:J,children:"Submit For Review"})]}):n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Finance"&&((e==null?void 0:e.requestType)==="Change"||(t==null?void 0:t.processDesc)==="Change")&&((zn=t==null?void 0:t.itmStatus)==null?void 0:zn.toUpperCase())!=="OPEN"&&I?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:a(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t!=null&&t.taskId?"":n(d,{variant:"contained",size:"small",sx:{button_Outlined:O,mr:1},onClick:Ue,children:"Save As Draft"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),f===F.length-1?a(U,{children:[n(d,{variant:"contained",size:"small",sx:{...O,mr:1},onClick:re,children:"Validate"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:Q,disabled:J,children:"Submit For Review"})]}):n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="MDM Steward"&&((e==null?void 0:e.requestType)==="Extend"||(t==null?void 0:t.processDesc)==="Extend")&&((Bn=t==null?void 0:t.itmStatus)==null?void 0:Bn.toUpperCase())!=="OPEN"&&!I?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:a(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(d,{variant:"outlined",size:"small",sx:{button_Outlined:O,mr:1},onClick:le,children:"Correction"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:Q,children:"Submit For Approval"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})}):(c==null?void 0:c.role)==="Finance"&&((e==null?void 0:e.requestType)==="Extend"||(t==null?void 0:t.processDesc)==="Extend")&&((On=t==null?void 0:t.itmStatus)==null?void 0:On.toUpperCase())!=="OPEN"&&I?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:a(k,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t!=null&&t.taskId?"":n(d,{variant:"contained",size:"small",sx:{button_Outlined:O,mr:1},onClick:rr,children:"Save As Draft"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:W,disabled:f===0,children:"Back"}),f===F.length-1?a(U,{children:[n(d,{variant:"contained",size:"small",sx:{...O,mr:1},onClick:re,children:"Validate"}),n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:Q,children:"Submit For Review"})]}):n(d,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:G,disabled:f===F.length-1,children:"Next"})]})}):"")]})]})})};export{fo as default};
