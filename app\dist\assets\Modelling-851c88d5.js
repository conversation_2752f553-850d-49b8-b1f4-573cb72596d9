import{q as a,r as l,a as m,B as p,bn as o}from"./index-75c1660a.js";import{i as c}from"./index-e2f5b037.js";import"./react-beautiful-dnd.esm-db50900e.js";import"./useMediaQuery-33e0a836.js";import"./DialogContentText-ef8524b5.js";import"./CardMedia-f3120f7c.js";import"./Container-754d6379.js";import"./InputAdornment-a22e1655.js";import"./ListItemButton-f13df81b.js";import"./Slider-c4e5ff46.js";import"./Stepper-2dbfb76b.js";import"./StepButton-e06eb73a.js";import"./ToggleButtonGroup-63ceda7a.js";import"./index-257abd9f.js";import"./toConsumableArray-42cf6573.js";import"./Check-1e790252.js";import"./clsx-a965ebfb.js";import"./Add-62a207fb.js";import"./DeleteOutline-a8808975.js";import"./Delete-1d158507.js";import"./asyncToGenerator-88583e02.js";import"./DeleteOutlineOutlined-fefa2376.js";import"./FileDownloadOutlined-329b8f56.js";import"./AddOutlined-0d3405f9.js";import"./EditOutlined-6971b85d.js";import"./Edit-77a8cc20.js";import"./index.esm-93e9b0e6.js";import"./makeStyles-c2a7efc7.js";import"./useSlotProps-da724f1f.js";import"./Settings-bf4ffef5.js";import"./VisibilityOutlined-a5a8c4d9.js";import"./index-19916fa2.js";import"./FiberManualRecord-1a0d6be5.js";import"./dayjs.min-83c0b0e0.js";import"./CheckBox-09a94074.js";import"./DeleteOutlined-fe5b7345.js";const V=d=>{const e=[{Description:"",Name:"WorkRulesServices",URL:o},{Description:"",Name:"CW_Worktext",URL:o},{Description:"",Name:"WorkRuleEngineServices",URL:o},{Description:"",Name:"WorkUtilsServices",URL:o}],r=a(t=>t==null?void 0:t.userManagement),i=r==null?void 0:r.userData;let s={emailId:i==null?void 0:i.emailId,user_id:i==null?void 0:i.user_id};const n=l.useMemo(()=>m(c.Modelling,{translationDataObjects:[],userDetails:s,destinations:e}),[i]);return m(p,{className:"content",sx:{padding:"8px 16px"},children:m(p,{sx:{height:"100%","& .MuiSnackbar-root":{left:"50% !important"}},children:n})})};export{V as default};
