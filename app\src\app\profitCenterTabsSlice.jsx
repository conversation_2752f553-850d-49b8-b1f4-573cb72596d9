import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  profitCenterBasicData: {},
  profitCenterCompCodes: {},
  profitCenterIndicators: {},
  profitCenterAddress: {},
  profitCenterCommunication: {},
  profitCenterHistory: {},
  profitCenterViewData: [],
  singlePCPayload: {},
  requiredFields: [],
  errorFields: [],
  EditMultipleCostCenter:{},
  MultipleProfitCenterData: [],
  MultipleProfitCenterRequestBench:[],
  companyCode: [
    {
      id: 1,
      companyCodes: "",
      companyName: "",
      assigned: "",
    },
  ],
  handleMassMode:""
};

export const profitCenterTabSlice = createSlice({
  name: "profitCenter",
  initialState,
  reducers: {
    setProfitCenterBasicDataTab: (state, action) => {
      state.profitCenterBasicData = action.payload;
    },
    setProfitCenterCompCodesTab: (state, action) => {
      state.profitCenterCompCodes = action.payload;
    },
    setProfitCenterIndicatorsTab: (state, action) => {
      state.profitCenterIndicators = action.payload;
    },
    setProfitCenterAddressTab: (state, action) => {
      state.profitCenterAddress = action.payload;
    },
    setProfitCenterCommunicationTab: (state, action) => {
      state.profitCenterCommunication = action.payload;
    },
    setProfitCenterHistoryTab: (state, action) => {
      state.profitCenterHistory = action.payload;
    },
    setProfitCenterViewData: (state, action) => {
      state.profitCenterViewData = action.payload;
    },
    setSingleProfitCenterPayload: (state, action) => {
      state.singlePCPayload[action.payload.keyName] = action.payload.data;
      return state;
    },
    clearProfitCenterPayload: (state) => {
      state.singlePCPayload = {};
    },
    setPCRequiredFields: (state, action) => {
      if (
        state.requiredFields.findIndex((item) => item == action.payload) == -1
      ) {
        state.requiredFields.push(action.payload);
      }
      return state;
    },
    setPCErrorFields: (state, action) => {
      state.errorFields = action.payload;
      return state;
    },
    setCompanyCode: (state, action) => {
      state.companyCode = action.payload;
      return state;
    },
    setEditCostCenter(state, action) {
      state.EditMultipleCostCenter = {
        ...state.EditMultipleCostCenter,
        ...action.payload,
      };
    },
    setMultipleProfitCenterData(state, action) {
      state.MultipleProfitCenterData = action.payload;
      return state;
    },
    setMultipleProfitCenterRequestBench(state, action) {
      state.MultipleProfitCenterRequestBench = action.payload;
      return state;
    },
    setHandleMassMode(state,action){
      state.handleMassMode = action.payload
    },
    clearProfitCenter :(state) =>{
      state.singlePCPayload = {}
      state.errorFields = []
      state.requiredFields = []
      state.EditMultipleCostCenter = {}
      state.companyCode = initialState.companyCode
    },

  },
});

// Action creators are generated for each case reducer function
export const {
  setProfitCenterBasicDataTab,
  setProfitCenterCompCodesTab,
  setProfitCenterIndicatorsTab,
  setProfitCenterAddressTab,
  setProfitCenterCommunicationTab,
  setProfitCenterHistoryTab,
  setSingleProfitCenterPayload,
  clearProfitCenterPayload,
  setPCRequiredFields,
  setPCErrorFields,
  setCompanyCode,
  setMultipleProfitCenterData,
  setMultipleProfitCenterRequestBench,
  setHandleMassMode,
  clearProfitCenter,
  setProfitCenterViewData
} = profitCenterTabSlice.actions;

export const profitCenterReducer = profitCenterTabSlice.reducer;
