import{q as c,bQ as J,s as O,b as X,r as i,pS as j,pU as A,aS as E,aT as $,aU as Z,aV as I,pV as P,j as B,a as T,pW as G,K as _,pX as Q,pY as D,a0 as k,pZ as z,p_ as H,a5 as x,Z as K,cQ as ee,C as ae}from"./index-17b8d91e.js";import{c as se,u as oe,g as ie,W as re}from"./propData-c3ca5892.js";import{u as te}from"./useDisplayDataDto-fdea4a92.js";import"./react-beautiful-dnd.esm-0e527744.js";import"./useMediaQuery-6a073ac5.js";import"./DialogContentText-631f3833.js";import"./CardMedia-bfb247e7.js";import"./Container-d04f3413.js";import"./InputAdornment-5b0053c5.js";import"./ListItemButton-1f7a8ca3.js";import"./Slider-3eb7e770.js";import"./Stepper-88e4fb0c.js";import"./StepButton-34497717.js";import"./ToggleButtonGroup-cf875764.js";import"./index.esm-be84327e.js";import"./makeStyles-213dac7f.js";import"./toConsumableArray-c7e4bd84.js";import"./asyncToGenerator-88583e02.js";import"./DatePicker-e260eb3e.js";import"./Timeline-bd0ec33e.js";import"./dayjs.min-ce01f2c7.js";import"./isBetween-3aeee754.js";import"./CSSTransition-30917e2c.js";function We(){let n=c(a=>a.userManagement.userData);const e=c(a=>a.userManagement.taskData),{customLog:p}=J(),h=c(a=>a.appSettings.language),l=c(a=>a.applicationConfig);let o=O();const S=X();i.useState({});const[U,ne]=i.useState(null),[M,u]=i.useState(!1),[g,y]=i.useState(""),[N,R]=i.useState(),[f,q]=i.useState(""),{getDisplayData:v}=te(),{showSnackbar:C}=j(),L={APPLICATION_NAME:"1784",CRUD_API_ENV:"itm",DB_TYPE:"hana",SERVICE_BASE_URL:[{Description:"",Name:"ITMJavaServices",URL:"https://cherryworkproducts-itm-java-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"ConfigServer",URL:"https://cherryworkproducts-config-server.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"WorkNetServices",URL:"https://cherryworkproducts-worknet-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"CrudApiServices",URL:"https://cw-caf-crudapi-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"WorkFormsServices",URL:"https://cherrywork-wf-java-qas.cfapps.eu10-004.hana.ondemand.com/workforms"},{Description:"",Name:"NotificationServices",URL:"https://cherryworkproducts-messaging-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"ITMGraphServices",URL:"https://cherrywork-btp-qas-dashboard.cfapps.eu10-004.hana.ondemand.com"},{Description:"Native Workflow Services",Name:"NativeWorkflowServices",URL:"https://cherryworkproducts-custom-wf-qas.cfapps.eu10-004.hana.ondemand.com"},{Description:"Native Workflow UI URL",Name:"NativeWorkflowUiUrl",URL:"https://cherrywork-native-workflow-qas.cfapps.eu10-004.hana.ondemand.com/native-ui"},{Description:"",Name:"OnboardingServices",URL:"https://cherryworkproducts-itm-java-qas.cfapps.eu10-004.hana.ondemand.com"}]},V={DateTimeFormat:{dateTimeFormat:"DD MMM YYYY||HH:mm",timeZone:"Asia/Calcutta"}};`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`;const W=a=>{const r={eventId:"TASK_FORWARDING",taskName:a==null?void 0:a.forwardedTasks.map(s=>s.taskDesc).join(","),requestId:a==null?void 0:a.forwardedTasks.map(s=>`${s.ATTRIBUTE_1}`).join(","),recipientGroup:a==null?void 0:a.recipientUsers.map(s=>s.ownerId).join(","),flowType:a==null?void 0:a.forwardedTasks.map(s=>s.ATTRIBUTE_2).join(",")},t=s=>{p(s)};_(`/${K}/mail/sendMail`,"post",t,r)},d=()=>{u(!0)},m=()=>{u(!1)},F=async a=>{var r;Q(ee.CURRENT_TASK,a);try{if((a==null?void 0:a.taskNature)==="Single-User"||(a==null?void 0:a.taskNature)!=="Single-User"&&(a==null?void 0:a.itmStatus)!=="Open"){if(o(I(a)),(a==null?void 0:a.processDisplayName)==="Material")if(!(a!=null&&a.ATTRIBUTE_1))C(D.FETCHING_REQUEST_ID,"info");else if(!(a!=null&&a.ATTRIBUTE_2))C(D.FETCHING_REQUEST_TYPE,"info");else{const t=await v(a==null?void 0:a.ATTRIBUTE_1,a==null?void 0:a.ATTRIBUTE_2,null,a,null);(t==null?void 0:t.statusCode)===k.STATUS_200&&S(`/requestBench/createRequest?RequestId=${(a==null?void 0:a.ATTRIBUTE_1)||(a==null?void 0:a.requestId)}&RequestType=${(a==null?void 0:a.ATTRIBUTE_2)||z(a==null?void 0:a.requestId)}`)}o(H({url:window.location.pathname,module:"ITMWorkbench"}))}else R("Kindly claim the task before proceeding"),y("Claim Task"),q("info"),d()}catch{p((r=x)==null?void 0:r.ERROR_SET_ROLE)}},b=()=>{console.log("fetchFilterView")},Y=()=>{console.log("clearFilterView")},w=(a,r)=>{console.log("Success flag.",a),console.log("Task Payload.",r)};return i.useEffect(()=>{A({}),o(E()),o($([])),o(Z({})),o(I({})),o(P([]))},[]),B("div",{style:{width:"calc(100vw - 105px)",height:"calc(100vh-130px)"},className:"workspaceOverride",children:[T(re,{token:"********************************************************************************************************************************************************************************************************************************************************************************.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.b0BWWn_rlTWe3h1Trx-MN3nDcZIU9i9HCr1y2h4bed4quLD6yHN2O9xzmQunvA6DQz0K898LxjHI9nFVCXs8m_7Aas00UiJ-vCi3vhwqLAOB77IlNbBV_OufjMShrkzmr2VHpyHpjVHg5RbJj6zFHleXxXXmQJkiefcaNf-5l-n6Tqxad16V9wHkKaMIFLAbx1tKjNyCPq-yX9otIE7gByKFPG_ijmJ15u_kMuGZgE6fQ5tbNRjNB-HlsDDvmXDJ9866G6j8q_YIYcuUO6yX083v4hwwaY4Y-5PqYMo7v1mIdK1i0UjcAl9853So4JcHcV5iLl0waC8ht5nO85oVFg",configData:se,destinationData:L,userData:{...n,user_id:n==null?void 0:n.emailId},userPreferences:V,userPermissions:oe,userList:{},groupList:{},languageTranslationData:ie(h),userListBySystem:U,useWorkAccess:l.environment==="localhost",useConfigServerDestination:l.environment==="localhost",inboxTypeKey:"MY_TASKS",workspaceLabel:"Open Tasks",workspaceFiltersByAPIDriven:!1,subInboxTypeKey:null,cachingBaseUrl:G,onTaskClick:F,onActionComplete:w,selectedFilterView:null,isFilterView:!1,fetchFilterViewList:b,savedFilterViewData:[],clearFilterView:Y,filterViewList:[],selectedTabId:null,forwardTaskData:W,userProcess:[]}),T(ae,{dialogState:M,openReusableDialog:d,closeReusableDialog:m,dialogTitle:g,dialogMessage:N,handleDialogConfirm:m,dialogOkText:"OK",dialogSeverity:f})]})}export{We as default};
