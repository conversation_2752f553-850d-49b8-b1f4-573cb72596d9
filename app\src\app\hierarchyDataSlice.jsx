import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  treeData: [],
  // Main storage
  NodeList: [],
  TagList: [],
  DescList: [],

  // Movement tracking
  ReplaceNodesList: [],
  ReplaceTagList: [],

  // Deletion tracking
  DeleteNodeList: [],

  // Description editing
  EditDescList: [],
  requestHeaderData: {},
  changeLog:[]

};

const hierarchyDataSlice = createSlice({
  name: "hierarchyData",
  initialState,
  reducers: {
    setDisplayDataHierarchy(state, action) {
      state = action.payload.data;
      return state;
    },
    setTreeData(state, action) {
      state.treeData = action.payload;
    },
    setRequestHeaderPayloadDataPCG(state, action) {
      const { keyName, data } = action.payload;
      if (keyName) {
        state.requestHeaderData[keyName] = data?.code
          ? data?.code
          : data
          ? data
          : "";
      } else {
        state.requestHeaderData = action.payload;
      }
    },

    // Node operations
    addNode(state, action) {
      const { parentNode, newNode } = action.payload;
      state.NodeList?.push(`${parentNode}$$${newNode}`);
    },
    removeTag(state, action) {
      const { tagPath } = action.payload;

      // 1. Remove from TagList
      state.TagList = state.TagList.filter((tag) => tag !== tagPath);

      // 2. Add to ReplaceTagList to track removal
      state.ReplaceTagList.push(tagPath);
    },

    deleteNode(state, action) {
      const { nodePath } = action.payload;
      console.log('check nodepath', nodePath)
      state.NodeList = state.NodeList.filter((node) => node !== nodePath);
      state.TagList = state.TagList.filter(
        (tag) => !tag.startsWith(`${nodePath}$$`)
      );
      state.DescList = state.DescList.filter(
        (desc) => !desc.startsWith(`${nodePath}$$`)
      );
      state.DeleteNodeList.push(nodePath);
    },

    // Tag operations
    addTag(state, action) {
      const { nodePath, tag } = action.payload;
      state.TagList.push(`${nodePath}$$${tag}`);
    },

    // Description operations
    addDescription(state, action) {
      const { nodePath, description } = action.payload;
      const nodeExists = state.NodeList?.some((node) => node === nodePath);
      if (nodeExists) {
        state.DescList.push(`${nodePath}$$${description}`);
      }
    },

    editDescription(state, action) {
      const { nodePath, newDescription } = action.payload;
      const newDesc = `${nodePath}$$${newDescription}`;
      const existingIndex = state.DescList.findIndex((d) =>
        d.startsWith(`${nodePath}$$`)
      );

      if (existingIndex >= 0) {
        state.DescList[existingIndex] = newDesc;
        state.EditDescList.push(newDesc);
      } else {
        state.DescList.push(newDesc);
      }
    },

    // Movement operations
    selectNodeForMove(state, action) {
      const { parentNode, selectedNode } = action.payload;
      state.ReplaceNodesList.push(`${parentNode}$$${selectedNode}`);
    },

    completeNodeMove(state, action) {
      const { newParentNode, movedNode } = action.payload;
      const newPath = `${newParentNode}$$${movedNode}`;

      // 1. Remove the old node path from NodeList
      state.NodeList = state.NodeList.filter(
        (node) => !node.endsWith(`$$${movedNode}`)
      );

      // 2. Add the new path
      state.NodeList.push(newPath);
    },

    selectTagForMove(state, action) {
      const { nodePath, tag } = action.payload;
      state.ReplaceTagList.push(`${nodePath}$$${tag}`);
    },

    completeTagMove(state, action) {
      const { newNodePath, tag } = action.payload;
      const newTagPath = `${newNodePath}$$${tag}`;

      // Remove old tag and add new one
      state.TagList = state.TagList.filter((t) => !t.endsWith(`$$${tag}`));
      state.TagList.push(newTagPath);

    
    },
     updateToChangeLog: (state, action) => {
      const { id, type, description,updatedBy,updatedOn } = action.payload;
      if(type) {
        state.changeLog.push({ id, type, description,updatedBy,updatedOn });
      }
      else {
        state.changeLog = action.payload;
      }
      
    },

    // Utility function to clear all data
    resetHierarchyData(state) {
      return initialState;
    },
  },
});

export const {
  setDisplayDataHierarchy,
  setTreeData,
  setRequestHeaderPayloadDataPCG,
  addNode,
  removeTag,
  deleteNode,
  addTag,
  addDescription,
  editDescription,
  selectNodeForMove,
  completeNodeMove,
  selectTagForMove,
  completeTagMove,
  resetHierarchyData,
  updateToChangeLog
} = hierarchyDataSlice.actions;

export const hierarchyReducer = hierarchyDataSlice.reducer;
