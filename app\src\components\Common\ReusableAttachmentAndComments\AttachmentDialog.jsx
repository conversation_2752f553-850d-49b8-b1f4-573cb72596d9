import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alogT<PERSON><PERSON>,
  Ty<PERSON><PERSON>,
  Stack,
  Button,
  IconButton,
  Backdrop,
  CircularProgress,
} from "@mui/material";
import { CloudUpload } from "@mui/icons-material";
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import React, { useEffect, useState } from "react";
import { v4 as uuidv4 } from "uuid";
import utilityImages from "../../../utilityImages";
import AddIcon from "@mui/icons-material/Add";
import CloseIcon from "@mui/icons-material/Close";
import DeleteIcon from '@mui/icons-material/Delete';
import ReusableSnackBar from "../ReusableSnackBar";


import { Box } from "@mui/system";
import {
  destination_DocumentManagement,
} from "../../../destinationVariables";
import { doAjax } from "../fetchService";
import { useDispatch, useSelector } from "react-redux";
import {setArtifactId, setAttachmentType} from "../../../app/initialDataSlice";
import { colors } from "@constant/colors";
import { END_POINTS } from "@constant/apiEndPoints";
import { DIALOUGE_BOX_MESSAGES, ENABLE_STATUSES, ERROR_MESSAGES, REQUEST_TYPE } from "@constant/enum";
import { useLocation } from "react-router-dom";
import useLang from "@hooks/useLang";
import { appendPrefixByJavaKey } from "@helper/helper";

const AttachmentDialog = ({
  sidebyside=false,
  getAttachmentshook=()=>{},
  processName = "",
  artifactId = "",
  artifactName = "",
  poNumber,
  isAnAttachment,
  promptAction_Functions, 
  attachmentType,
}) => {
  const requestType = useSelector(
    (state) => state.request?.requestHeader?.requestType
  );
  const requestTypeFromTask = useSelector(
    (state) => state?.userManagement?.taskData?.ATTRIBUTE_2
  );
  const requestTypeFromPayload = useSelector((state) => state.payload?.payloadData?.RequestType);
 
  const [isDragOver, setIsDragOver] = useState(false);
  const [blurLoading, setBlurLoading] = useState(false);
  const [docList, setDocList] = useState([]);
  const [attachmentData, setattachmentData] = useState([]);
  const [attachmentOpenStatus, setattachmentOpenStatus] = useState(false);
  const [metaDataOptions, setmetaDataOptions] = useState(["Others"]);
  const [fileSizeError, setFileSizeError] = useState(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [alertType, setAlertType] = useState("success");
  const dispatch = useDispatch();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const isreqBench = queryParams.get("reqBench");
  let userData = useSelector((state) => state.userManagement.userData);
  const payloadData = useSelector((state) => state.payload.payloadData);
  const changePayloadData = useSelector((state) => state?.payload?.dynamicKeyValues?.childRequestHeaderData)
  const { t } = useLang();

  let colorsObj = {
    other: colors.attachmentDialog.other,
    "order change": colors.attachmentDialog.orderChange,
  };
  let getColor = (tag) => {
    if (colorsObj[tag]) {
      return colorsObj[tag];
    }
    return colors.attachmentDialog.orderChange;
  };
  let handleCloseAttachment = () => {
    setattachmentOpenStatus(false);
    setDocList([]);
  };
  const handleSnackBarClose = () => {
    setOpenSnackbar(false);
    setMessageDialogMessage("");
  };
  let handleOpenAttachment = () => {
    setattachmentOpenStatus(true);
  };
  let getFileContainer = (data) => {
    return (
      <>
        {data?.map((item,index) => {
          return (
            <div
              key={index}
              style={{
                minWidth: "5rem",
                minHeight: "6rem",
                height: "6.5rem",
                width: "5.4rem",
                borderRadius: ".5rem",
                margin: "0 .5rem",
                border: `1px solid ${getColor(item.metaData?.toLowerCase())}`,
                overflow: "hidden",
                boxShadow: "0px 6px 6px -3px rgba(0,0,0,0.2)",
                position: "relative",
              }}
            >
              <Box
                sx={{
                  height: "1rem",
                  backgroundColor: getColor(item.metaData?.toLowerCase()),
                  display: "flex",
                  justifyContent: "center",
                  overflow: "hidden",
                }}
              >
                <Typography
                  sx={{ fontSize: "10px", width: "100%", textAlign: "center" }}
                >
                  {item?.metaData}
                </Typography>
              </Box>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  padding: "0.6rem",
                }}
              >
                <img
                  style={{
                    width: "1.6rem",
                    height: "1.6rem",
                  }}
                  src={utilityImages[item?.name?.split(".")[1] || item?.fileName?.split(".")[1]]}
                ></img>
              </Box>
              <Box sx={{ overflow: "hidden" }}>
                <Typography
                  variant="body2"
                  sx={{
                    padding: ".3rem",
                    color: "grey",
                    textAlign: "center",
                    textOverflow: "ellipsis",
                    overflow: "hidden",
                    whiteSpace: "noWrap",
                    height: "2rem",
                  }}
                >
                  {item?.name || item?.fileName}
                </Typography>
              </Box>
            </div>
          );
        })}
      </>
    );
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = () => {
    setIsDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    if (docList.length + files.length > 5) {
      promptAction_Functions.handleOpenPromptBox("Warning", {
        title: "Warning",
        message: ERROR_MESSAGES.NUMBER_OF_FILES_LIMIT,
        severity: "warning",
        cancelButton: false,
      });
      return;
    }
    handleOnChangeFile(files);
  };
  const addFile = () => {
    let addFileButton = document.getElementById("fileButton");
    addFileButton.click();
  };
  const handleOnChangeFile = (files) => {
    let filesWithId = [];
    files.forEach((item) => {
      item.id = uuidv4();
      filesWithId.push(item);
    });
    setDocList((prev) => [...prev, ...filesWithId]);
  };

  const getAttachments = () => {
    let hSuccess = (data) => {
      let attachmentRows = [];
      data?.documentDetailDtoList?.forEach((doc) => {
        if (true) {
          attachmentRows.push({
            id: doc.documentId,
            docType: doc.fileType,
            name: doc?.fileName,
            uploadedBy: doc.createdBy,
            metaData: doc.metadataTag ?? "",
          });
        }
      });
     
      setattachmentData(attachmentRows);
    }; 
    if (artifactName === "RequestBench") {
      const hasAnyChildRequestId = changePayloadData?.ChildRequestId || payloadData?.childRequestId;
      const url = hasAnyChildRequestId
        ? `/${destination_DocumentManagement}/${END_POINTS.TASK_ACTION_DETAIL.GET_CHILD_DOCS}/${artifactId}`
        : `/${destination_DocumentManagement}/${END_POINTS.TASK_ACTION_DETAIL.GET_DOCS}/${artifactId}`;
      doAjax(
        url,
        "get",
        hSuccess
      );
    } 
   
  };
  const effectiveRequestType = requestType || requestTypeFromTask || requestTypeFromPayload
  let handleAdd = () => {
    if (!fileSizeError && docList[0]) {
      let fileData = [...docList];
      
      const handleAttachmentsSubmit = () => {
        setBlurLoading(true);
        const formData = new FormData();
        let metaDataIdsMap = {};
        metaDataOptions?.forEach((item) => {
          metaDataIdsMap[item.metadataTag] = item.id;
        });
        let fileNameArray = [];
        fileData?.forEach((item) => {
          let tFile = { ...item };
          tFile.name = item.name
          formData.append(
            "files",
            item,
            item.name
          );
          fileNameArray.push(item.name);
        });
        
        dispatch(setArtifactId(artifactId));
        const { RequestId,RequestType,childRequestId: payloadChildRequestId} = payloadData || {};
          const { ChildRequestId } = changePayloadData || {};
          const isChangeType = [REQUEST_TYPE.CHANGE, REQUEST_TYPE.CHANGE_WITH_UPLOAD].includes(RequestType);
          const formattedRequestId = RequestId
            ? appendPrefixByJavaKey(effectiveRequestType, RequestId)
            : null;
          const formattedChildRequestId = isChangeType
            ? ChildRequestId
              ? appendPrefixByJavaKey(effectiveRequestType, ChildRequestId)
              : null
            : payloadChildRequestId
              ? appendPrefixByJavaKey(effectiveRequestType, payloadChildRequestId)
              : null;
          const doc = {
            artifactId,
            createdBy: userData?.emailId,
            artifactType: artifactName,
            attachmentType,
            requestId: formattedRequestId,
            childRequestId: formattedChildRequestId,
            fileName: fileNameArray.join("$^$"),
            requestType: effectiveRequestType,
          };
        if (isAnAttachment) {
          doc.isAnAttachment = isAnAttachment;
        }
        if (poNumber && artifactName !== "Purchase Order") {

          doc.poNumber = poNumber;
        }
        formData.append("doc", JSON.stringify(doc));
        let hSuccess = (data) => {
          dispatch(setAttachmentType(attachmentType));
          if (data.responseMessage.status === "Failure") {
            setBlurLoading(false);
            setAlertType("error");
            setMessageDialogMessage(`${t(DIALOUGE_BOX_MESSAGES.UPLOAD_FAILED)}`);
            setOpenSnackbar(true);
          } else if (data.responseMessage.status === "Success" || data.responseMessage.status === "Partial Success") {
            getAttachmentshook();
            setAlertType("success");
            setMessageDialogMessage(`Documents for ${artifactName} ${artifactId} uploaded successfully `);
            setOpenSnackbar(true);
            handleCloseAttachment();
            setBlurLoading(false);
            setattachmentData((prev) => [...prev, ...docList]);
          }
        };
        let hError = (error) => {
          setBlurLoading(false);
          setAlertType("error");
          setMessageDialogMessage(`${t(DIALOUGE_BOX_MESSAGES.ALT_UPLOAD_ERROR)}`);
          setOpenSnackbar(true);
        };
        doAjax(
          `/${destination_DocumentManagement}/${END_POINTS.DMS_API.UPLOAD_DOCUMENT}`,
          "postformdata",
          hSuccess,
          hError,
          formData
        );
      };

      handleAttachmentsSubmit();
    
      return;
    }
    //if there are no file to upload
    if (!docList[0]) {
      promptAction_Functions.handleOpenPromptBox("Warning", {
        title: "Warning",
        message: t(`Please add some files to upload`),
        severity: "warning",
        cancelButton: false,
      });
    }
  };
  let handleCancel = () => {
    handleCloseAttachment();
  };
  let deleteAttachment = (id) => {
    let data = docList.filter((i) => i.id !== id);
    setDocList(data);
  };
  let checkFileSize = () => {
    let error = false;
    let size = 0;
    docList.forEach((i) => {
      size += i.length;
    });
    if (size > 5000000000) {
      promptAction_Functions.handleOpenPromptBox("ERROR", {
        title: "Warning",
        message: t(`Files size excceded`),
        severity: "warning",
        cancelButton: false,
      });
      setFileSizeError(true);
    } else {
      setFileSizeError(false);
    }
  };
 
  useEffect(() => {
    checkFileSize();
  }, [docList]);
  useEffect(() => {
    getAttachments();
  },   []);

  return (
    <>
      <Box>
        {/* Attachments Section */}
        <Box 
          sx={{ 
            display: 'flex', 
            flexDirection: 'row', 
          }}
        >
          {
            sidebyside===false ?
            <>
            <Box
            sx={{
              display: "flex",
              flexDirection: "row",
              margin: "1rem 0",
              maxWidth: "50vw",
              overflowX: "auto",
              position: "relative",
              minHeight: "7rem",
            }}
          >
            {attachmentData[0] && getFileContainer(attachmentData)}
          </Box>
        
         
          
          <Box
            onClick={handleOpenAttachment}
            style={{
              width: "5rem",
              maxWidth: "5rem",
              height: "6.5rem",
              borderRadius: ".5rem",
              backgroundColor: "#eae9ff",
              padding: "1rem",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              margin: "1rem .5rem",
              boxShadow:
                "0px 2px 4px -1px rgba(0,0,0,0.2),0px 4px 5px 0px rgba(0,0,0,0.14),0px 1px 10px 0px rgba(0,0,0,0.12)",
              cursor: "pointer",
            }}
          >
            <AddIcon color="primary" sx={{ fontSize: 24 }} />
          </Box>
          </>
          :
          <Box 
      display="flex" 
      justifyContent="center" 
      alignItems="center" 
      sx={{mr:1}}
    >
      <Button
        variant="contained"
        component="label"
        onClick={handleOpenAttachment}
        startIcon={<CloudUpload />}
        disabled={isreqBench && !ENABLE_STATUSES?.includes(payloadData?.RequestStatus)}
        sx={{
          backgroundColor: "#1976d2",
          color: "#fff",
          textTransform: "capitalize",
          borderRadius: "5px",
          padding: "10px 20px",
          "&:hover": {
            backgroundColor: "#1565c0",
          },
          boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.2)",
        }}
      >
        {t("Upload File")}
      </Button>
    </Box>
          }
        </Box>

        {/* Attachment Dialog */}
        {attachmentOpenStatus && (
          <Dialog
            fullWidth
            maxWidth="sm"
            open={true}
            onClose={handleCloseAttachment}
            sx={{
              '& .MuiDialog-paper': {
                borderRadius: '12px',
                padding: '1rem',
              },
              overflow:'hidden'
            }}
          >
            <DialogTitle sx={{ padding: '1rem 1.5rem' }}>
              <Typography variant="h6" sx={{ fontWeight: 500 }}>
                {t("Add New Attachment")}
              </Typography>
              <IconButton
          aria-label="close"
          onClick={handleCancel}
          sx={(theme) => ({
            position: 'absolute',
            right: 12,
            top: 10,
            color: theme.palette.grey[500],
          })}
        >
          <CloseIcon />
        </IconButton>
            </DialogTitle>
            
            <DialogContent sx={{ padding: '1.5rem',overflow:'hidden' }}>
              <Box
                className={`dropzone ${isDragOver ? "dragover" : ""}`}
                sx={{
                  width: '100%',
                  border: `2px dashed ${isDragOver ? '#3b30c8' : '#d0d5dd'}`,
                  borderRadius: '8px',
                  padding: '2rem',
                  backgroundColor: isDragOver ? '#f8f9ff' : '#fafbff',
                  transition: 'all 0.3s ease',
                  cursor: 'pointer',
                  minHeight: '200px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                <Stack alignItems="center" spacing={1}>
                  <CloudUploadIcon sx={{ fontSize: 48, color: '#3b30c8' }} />
                  <Typography variant="body1" sx={{ color: '#344054' }}>
                    {t("Drag and drop files")}
                  </Typography>
                  <Typography
                    variant="body2"
                    color="primary"
                    sx={{ 
                      cursor: 'pointer', 
                      textDecoration: 'underline',
                      '&:hover': { color: '#3b30c8' }
                    }}
                    onClick={addFile}
                  >
                    {t("or click to upload")}
                  </Typography>
                </Stack>
                
                <input
                  id="fileButton"
                  multiple
                  accept=".jpeg, .jpg, .xls, .xlsx, .docx, .pdf"
                  type="file"
                  name="files"
                  onChange={(e) => {
                    const files = Array.from(e.target.files);
                    if (docList.length + files.length > 5) {
                      promptAction_Functions.handleOpenPromptBox("Warning", {
                        title: t("Warning"),
                        message: ERROR_MESSAGES.NUMBER_OF_FILES_LIMIT,
                        severity: "warning",
                        cancelButton: false,
                      });
                      return;
                    }
                    handleOnChangeFile(files);
                  }}
                  style={{ display: "none" }}
                />
              </Box>

              {docList[0] && (
                <Box
                  sx={{
                    maxHeight: '14rem',
                    overflowY: 'auto',
                    marginTop: '1.5rem',
                    padding: '1rem',
                    backgroundColor: colors.primary.white,
                    borderRadius: '8px',
                    border: '1px solid #eee',
                  
                    '&::-webkit-scrollbar-thumb': {
                      backgroundColor: '#888',
                      borderRadius: '3px',
                    }
                  }}
                >
                  {docList?.map((i, index) => (
                    <Box
                      key={index}
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        padding: '0.75rem',
                        borderRadius: '6px',
                        backgroundColor: colors.primary.white,
                        marginBottom: '0.5rem',
                        border: '1px solid #eee',
                        '&:hover': {
                          backgroundColor: '#f9fafb',
                        }
                      }}
                    >

                      <img
                        style={{ width: '24px', height: '24px', marginRight: '0.75rem' }}
                        src={utilityImages[i.name?.split(".")[1]]}
                      />
                      <Typography variant="body1" sx={{ flexGrow: 1 }}>
                        {i.name}
                      </Typography>
        
                      {fileSizeError ?(
                        <Typography variant="body2" color="error" sx={{ marginLeft: '1rem' }}>
                          {parseFloat(i.size / 1000000).toFixed(2)} MB
                        </Typography>
                      ):    <Typography
                      sx={{
                        marginLeft: "auto",
                        marginRight: "10%",
                      }}
                      color={"gray"}
                    >
                      {parseFloat(i.size / 1000000).toFixed(2)} MB
                    </Typography>}
                      <IconButton
                        id={`closeBtn-${i.id}`}
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          deleteAttachment(i.id);
                        }}
                        sx={{
                          marginLeft: '0.5rem',
                          opacity: 0.8,
                         
                        }}
                      >
                        <DeleteIcon fontSize="small" color="error" />
                      </IconButton>
                    </Box>
                  ))}
                  
                </Box>
              )}

              <Stack
                direction="row"
                spacing={2}
                sx={{
                  marginTop: '1.5rem',
                  justifyContent: 'flex-end',
                  alignItems: 'center'
                }}
              >
                <Typography variant="body2" color="error">
                  *{t("Max file size 500 MB")}
                </Typography>
                <Typography variant="body2" sx={{ color: colors.placeholder.dark }}>
                  {t("Maximum 5 files allowed")}
                </Typography>
                <Button
                  variant="contained"
                  onClick={handleAdd}
                  sx={{ 
                    borderRadius: '6px',
                    padding: '0.5rem 1.5rem'
                  }}
                >
                  {t("Upload")}
                </Button>
              </Stack>
            </DialogContent>
          </Dialog>
        )}

        <Backdrop
          sx={{ color: colors.primary.white, zIndex: 1400 }}
          open={blurLoading}
        >
          <CircularProgress color="inherit" />
        </Backdrop>
      </Box>
      <ReusableSnackBar
        openSnackBar={openSnackbar}
        alertMsg={messageDialogMessage}
        alertType={alertType}
        handleSnackBarClose={handleSnackBarClose}
      />
    </>
  );
};

export default AttachmentDialog;
