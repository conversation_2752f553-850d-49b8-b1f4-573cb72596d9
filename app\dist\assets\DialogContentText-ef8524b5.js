import{d7 as g,d8 as h,cB as p,cC as b,r as f,cH as A,cI as x,o as u,cE as C,cM as d,cN as m,T as v,cD as T}from"./index-75c1660a.js";function D(o){return g("MuiCardActionArea",o)}const R=h("MuiCardActionArea",["root","focusVisible","focusHighlight"]),l=R,w=["children","className","focusVisibleClassName"],N=o=>{const{classes:s}=o;return m({root:["root"],focusHighlight:["focusHighlight"]},D,s)},H=p(b,{name:"MuiCardActionArea",slot:"Root",overridesResolver:(o,s)=>s.root})(({theme:o})=>({display:"block",textAlign:"inherit",borderRadius:"inherit",width:"100%",[`&:hover .${l.focusHighlight}`]:{opacity:(o.vars||o).palette.action.hoverOpacity,"@media (hover: none)":{opacity:0}},[`&.${l.focusVisible} .${l.focusHighlight}`]:{opacity:(o.vars||o).palette.action.focusOpacity}})),M=p("span",{name:"MuiCardActionArea",slot:"FocusHighlight",overridesResolver:(o,s)=>s.focusHighlight})(({theme:o})=>({overflow:"hidden",pointerEvents:"none",position:"absolute",top:0,right:0,bottom:0,left:0,borderRadius:"inherit",opacity:0,backgroundColor:"currentcolor",transition:o.transitions.create("opacity",{duration:o.transitions.duration.short})})),$=f.forwardRef(function(s,e){const t=A({props:s,name:"MuiCardActionArea"}),{children:i,className:a,focusVisibleClassName:r}=t,y=x(t,w),n=t,c=N(n);return u.jsxs(H,C({className:d(c.root,a),focusVisibleClassName:d(r,c.focusVisible),ref:e,ownerState:n},y,{children:[i,u.jsx(M,{className:c.focusHighlight,ownerState:n})]}))}),_=$;function S(o){return g("MuiDialogContentText",o)}const U=h("MuiDialogContentText",["root"]),B=U,V=["children","className"],j=o=>{const{classes:s}=o,t=m({root:["root"]},S,s);return C({},s,t)},E=p(v,{shouldForwardProp:o=>T(o)||o==="classes",name:"MuiDialogContentText",slot:"Root",overridesResolver:(o,s)=>s.root})({}),F=f.forwardRef(function(s,e){const t=A({props:s,name:"MuiDialogContentText"}),{className:i}=t,a=x(t,V),r=j(a);return u.jsx(E,C({component:"p",variant:"body1",color:"text.secondary",ref:e,ownerState:a,className:d(r.root,i)},t,{classes:r}))}),k=F;export{_ as C,k as D,D as a,l as c,B as d,S as g};
