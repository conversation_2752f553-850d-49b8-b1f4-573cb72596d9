import{r,b as qa,u as Wa,s as Ga,q as _,bh as Ha,eQ as Xa,j as u,a,V as X,aF as Z,X as J,aj as Za,x as Q,aC as oa,G as h,F as Ja,T as p,bX as Qa,aG as na,I as L,B as la,ar as Ya,E as xa,W as Y,t as B,C as es,z as as,al as ss,b1 as ts,h as os,aE as ns,aB as x,aD as ls,cc as is,cb as rs,K as A,eK as V,ai as ds,bq as ia}from"./index-75c1660a.js";import{d as cs}from"./ArrowCircleLeftOutlined-f7b52d40.js";import{l as ra}from"./lookup-1dcf10ac.js";import{d as us}from"./AttachFileOutlined-872f8e38.js";import{R as ms}from"./ReusableAttachementAndComments-682b0475.js";import"./CloudUpload-d5d09566.js";import"./utilityImages-067c3dc2.js";import"./Add-62a207fb.js";import"./Delete-1d158507.js";const As=()=>{var he;const[F,da]=r.useState(!1),[ca,D]=r.useState(!1),[ua,gs]=r.useState("1"),[$,ma]=r.useState([]),[ga,y]=r.useState(!1),[Da,ee]=r.useState(!1),[ha,w]=r.useState(!1),[Ds,R]=r.useState(!1),[pa,j]=r.useState(!1),[ka,ae]=r.useState(!1),[se,N]=r.useState(""),[Ca,P]=r.useState(!1),[hs,K]=r.useState(!0),[fa,te]=r.useState(!1);r.useState([]);const[oe,Sa]=r.useState(!0),[ya,ne]=r.useState(!1),[ba,le]=r.useState(!1),[z,va]=r.useState(""),[M,ie]=r.useState(""),[Ba,E]=r.useState(!1);r.useState([]);const[I,Aa]=r.useState([]),[wa,U]=r.useState(!1);r.useState([]);const[Ra,Na]=r.useState(!0),q=qa();Wa();const re=Ga(),b=_(e=>e.bankKey.MultipleBankKeyData);_(e=>e.appSettings);let f=_(e=>e.bankKey.handleMassMode);console.log("massHandleType",f);let k=_(e=>e.userManagement.userData);const[de,Pa]=r.useState(0),Ka=(e,o)=>{const t=c=>{re(ds({keyName:e,data:c.body})),Pa(n=>n+1)},l=c=>{console.log(c)};A(`/${V}/data/${o}`,"get",t,l)},Ma=()=>{var e,o;(o=(e=ra)==null?void 0:e.bankKey)==null||o.map(t=>{Ka(t==null?void 0:t.keyName,t==null?void 0:t.endPoint)})},Ta=()=>{var e,o;de==((o=(e=ra)==null?void 0:e.bankkey)==null?void 0:o.length)&&da(!1)};r.useEffect(()=>{Ta()},[de]),r.useEffect(()=>{va(Ha("BK"))},[]),r.useEffect(()=>{Ma(),re(Xa())},[]);const W=()=>{j(!0)},Fa=()=>{le(!0)},ce=()=>{le(!1)},G=()=>{ae(!0)},ue=()=>{ae(!1)},Ea=()=>{},Ia=()=>{Da?(j(!1),ee(!1)):(j(!1),q("/masterDataCockpit/bankKey"))},s=(e,o)=>{console.log("getvalueforfieldname",e,o);const t=e==null?void 0:e.find(l=>(l==null?void 0:l.fieldName)===o);return t?t.value:""},me=(e,o)=>{const t=e==null?void 0:e.find(l=>(l==null?void 0:l.fieldName)===o);return console.log(t.value,"field.value"),t.value===!0?"X":""},Oa=e=>{e.length>0?(E(!0),console.log("selectedIds1",e)):E(!1),console.log("selectedIds",e),ma(e)},_a=[{field:"bankKey",headerName:"Bank key",editable:!1,flex:1,renderCell:e=>{const o=I.find(t=>t.bankKey===e.value);return console.log(o,"isDirectMatch"),console.log(e,"params"),o&&o.code===400?a(p,{sx:{fontSize:"12px",color:"red"},children:e.value}):a(p,{sx:{fontSize:"12px"},children:e.value})}},{field:"bankName",headerName:"Bank Name",editable:!1,flex:1},{field:"bankCountry",headerName:"Bank Country",editable:!1,flex:1},{field:"Region",headerName:"Region",editable:!1,flex:1},{field:"City",headerName:"City",editable:!1,flex:1},{field:"bankNumber",headerName:"Bank Number",editable:!1,flex:1},{field:"bankBranch",headerName:"Bank Branch",editable:!1,flex:1},{field:"swiftBic",headerName:"Swift/Bic",editable:!1,flex:1}],v=b==null?void 0:b.map((e,o)=>{var c,n,i,m,C,g,T;const t=e,l=((c=e==null?void 0:e.viewData)==null?void 0:c["Bank Details"])||{};return console.log(l,"basicData"),{id:o,bankKey:t==null?void 0:t.BankKey,bankCountry:t==null?void 0:t.BankCtry,bankName:((n=l.Address.find(d=>(d==null?void 0:d.fieldName)==="Bank Name"))==null?void 0:n.value)||"",Region:((i=l.Address.find(d=>(d==null?void 0:d.fieldName)==="Region"))==null?void 0:i.value)||"",City:((m=l.Address.find(d=>(d==null?void 0:d.fieldName)==="City"))==null?void 0:m.value)||"",bankNumber:((C=l["Control Data"].find(d=>(d==null?void 0:d.fieldName)==="Bank Number"))==null?void 0:C.value)||"",bankBranch:((g=l.Address.find(d=>(d==null?void 0:d.fieldName)==="Bank Branch"))==null?void 0:g.value)||"",swiftBic:((T=l["Control Data"].find(d=>(d==null?void 0:d.fieldName)==="SWIFT/BIC"))==null?void 0:T.value)||""}});console.log(v,"initialRows===="),console.log(b,"multipleBankKeyData");var S=b.map(e=>{var o,t,l,c,n,i,m,C,g,T,d,pe,ke,Ce,fe,Se,ye,be,ve,Be,Ae,we,Re,Ne,Pe,Ke,Me,Te,Fe,Ee,Ie,Oe,_e,Le,Ve,$e,je,ze,Ue,qe,We,Ge,He,Xe,Ze,Je,Qe,Ye,xe,ea,aa,sa,ta;return console.log("samsung",e),console.log((o=e==null?void 0:e.viewData["Address Details"])==null?void 0:o["PO Box Address"],"powmodata"),{AddressDto:{languCr:"",AddressID:"",Title:e!=null&&e.Title?e==null?void 0:e.Title:"",Name:s((t=e==null?void 0:e.viewData["Address Details"])==null?void 0:t.Name,"Name"),Name2:s((l=e==null?void 0:e.viewData["Address Details"])==null?void 0:l.Name,"Name 1"),Name3:s((c=e==null?void 0:e.viewData["Address Details"])==null?void 0:c.Name,"Name 2"),Name4:s((n=e==null?void 0:e.viewData["Address Details"])==null?void 0:n.Name,"Name 3"),Sort1:s((i=e==null?void 0:e.viewData["Address Details"])==null?void 0:i["Search Terms"],"Search Term 1"),Sort2:s((m=e==null?void 0:e.viewData["Address Details"])==null?void 0:m["Search Terms"],"Search Term 2"),BuildLong:s((C=e==null?void 0:e.viewData["Address Details"])==null?void 0:C["Street Address"],"Building Code"),RoomNo:s((g=e==null?void 0:e.viewData["Address Details"])==null?void 0:g["Street Address"],"Room Number"),Floor:s((T=e==null?void 0:e.viewData["Address Details"])==null?void 0:T["Street Address"],"Floor"),COName:s((d=e==null?void 0:e.viewData["Address Details"])==null?void 0:d["Street Address"],"c/o"),StrSuppl1:s((pe=e==null?void 0:e.viewData["Address Details"])==null?void 0:pe["Street Address"],"Street 1"),StrSuppl2:s((ke=e==null?void 0:e.viewData["Address Details"])==null?void 0:ke["Street Address"],"Street 2"),Street:s((Ce=e==null?void 0:e.viewData["Address Details"])==null?void 0:Ce["Street Address"],"Street 3"),HouseNo:s((fe=e==null?void 0:e.viewData["Address Details"])==null?void 0:fe["Street Address"],"House Number"),HouseNo2:s((Se=e==null?void 0:e.viewData["Address Details"])==null?void 0:Se["Street Address"],"Supplement"),StrSuppl3:s((ye=e==null?void 0:e.viewData["Address Details"])==null?void 0:ye["Street Address"],"Street 4"),Location:s((be=e==null?void 0:e.viewData["Address Details"])==null?void 0:be["Street Address"],"Street 5"),District:s((ve=e==null?void 0:e.viewData["Address Details"])==null?void 0:ve["Street Address"],"District"),HomeCity:s((Be=e==null?void 0:e.viewData["Address Details"])==null?void 0:Be["Street Address"],"Other City"),PostlCod1:s((Ae=e==null?void 0:e.viewData["Address Details"])==null?void 0:Ae["Street Address"],"Postal Code"),PostlCod2:e!=null&&e.PostalCode1?e==null?void 0:e.PostalCode1:"",PostlCod3:e!=null&&e.CompanyPostCd?e==null?void 0:e.CompanyPostCd:"",PoBox:e!=null&&e.POBox?e==null?void 0:e.POBox:"",PoBoxCit:s((we=e==null?void 0:e.viewData["Address Details"])==null?void 0:we["PO Box Address"],"PO Box City"),PoBoxReg:s((Re=e==null?void 0:e.viewData["Address Details"])==null?void 0:Re["PO Box Address"],"Region 2"),PoboxCtry:s((Ne=e==null?void 0:e.viewData["Address Details"])==null?void 0:Ne["PO Box Address"],"Country 2"),Country:s((Pe=e==null?void 0:e.viewData["Address Details"])==null?void 0:Pe["Street Address"],"Country 1"),TimeZone:s((Ke=e==null?void 0:e.viewData["Address Details"])==null?void 0:Ke["Street Address"],"Time Zone"),Taxjurcode:s((Me=e==null?void 0:e.viewData["Address Details"])==null?void 0:Me["Street Address"],"Tax Jurisdiction"),Transpzone:s((Te=e==null?void 0:e.viewData["Address Details"])==null?void 0:Te["Street Address"],"Transport Zone"),Regiogroup:s((Fe=e==null?void 0:e.viewData["Address Details"])==null?void 0:Fe["Street Address"],"Structure Group"),DontUseS:s((Ee=e==null?void 0:e.viewData["Address Details"])==null?void 0:Ee["Street Address"],"Undeliverable"),DontUseP:s((Ie=e==null?void 0:e.viewData["Address Details"])==null?void 0:Ie["PO Box Address"],"Undeliverable 1"),PoWONo:me((Oe=e==null?void 0:e.viewData["Address Details"])==null?void 0:Oe["PO Box Address"],"PO Box w/o No."),DeliServType:s((_e=e==null?void 0:e.viewData["Address Details"])==null?void 0:_e["PO Box Address"],"Delivery Service Type"),DeliServNumber:s((Le=e==null?void 0:e.viewData["Address Details"])==null?void 0:Le["PO Box Address"],"Delivery Service No."),Township:s((Ve=e==null?void 0:e.viewData["Address Details"])==null?void 0:Ve["PO Box Address"],"Township"),Langu:s(($e=e==null?void 0:e.viewData["Address Details"])==null?void 0:$e.Communication,"Language"),Tel1Numbr:s((je=e==null?void 0:e.viewData["Address Details"])==null?void 0:je.Communication,"Telephone"),Tel1Ext:s((ze=e==null?void 0:e.viewData["Address Details"])==null?void 0:ze.Communication,"Extension"),MobilePhone:s((Ue=e==null?void 0:e.viewData["Address Details"])==null?void 0:Ue.Communication,"Mobile Phone"),FaxNumber:s((qe=e==null?void 0:e.viewData["Address Details"])==null?void 0:qe.Communication,"Fax"),FaxExtens:s((We=e==null?void 0:e.viewData["Address Details"])==null?void 0:We.Communication,"Extension 1"),EMail:s((Ge=e==null?void 0:e.viewData["Address Details"])==null?void 0:Ge.Communication,"E-Mail Address"),AdrNotes:s((He=e==null?void 0:e.viewData["Address Details"])==null?void 0:He.Communication,"Notes"),Region:s((Xe=e==null?void 0:e.viewData["Address Details"])==null?void 0:Xe["Street Address"],"Region 1"),PoBoxLobby:e!=null&&e.PoBoxLobby?e==null?void 0:e.PoBoxLobby:""},BankKeyID:"",ReqCreatedBy:k==null?void 0:k.user_id,ReqCreatedOn:"",RequestStatus:"",CreationId:"",EditId:"",DeleteId:"",MassCreationId:"",MassEditId:"",MassDeleteId:"",RequestType:f==="Create"?"Mass Create":"Mass Change",TaskId:"",Remarks:M||"",Action:f==="Create"?"I":"U",Validation:Ra===!0?"X":"",BankCtry:e==null?void 0:e.BankCtry,BankKey:e==null?void 0:e.BankKey,BankName:s((Ze=e==null?void 0:e.viewData["Bank Details"])==null?void 0:Ze.Address,"Bank Name"),BankRegion:s((Je=e==null?void 0:e.viewData["Bank Details"])==null?void 0:Je.Address,"Region"),BankStreet:s((Qe=e==null?void 0:e.viewData["Bank Details"])==null?void 0:Qe.Address,"Street"),City:s((Ye=e==null?void 0:e.viewData["Bank Details"])==null?void 0:Ye.Address,"City"),BankBranch:s((xe=e==null?void 0:e.viewData["Bank Details"])==null?void 0:xe.Address,"Bank Branch"),SwiftCode:s((ea=e==null?void 0:e.viewData["Bank Details"])==null?void 0:ea["Control Data"],"SWIFT/BIC"),BankGroup:s((aa=e==null?void 0:e.viewData["Bank Details"])==null?void 0:aa["Control Data"],"Bank Group"),PobkCurac:me((sa=e==null?void 0:e.viewData["Bank Details"])==null?void 0:sa["Control Data"],"Postbank Acct"),BankNo:s((ta=e==null?void 0:e.viewData["Bank Details"])==null?void 0:ta["Control Data"],"Bank Number")}});console.log(S,"payloadmapping====");const La=()=>{const e=v.filter((n,i)=>$.includes(i));console.log("selectedData",e);const o=e.map(n=>({...S[n==null?void 0:n.id]}));let t=S;t=o,console.log("selectedBankKeyRows",o);const l=n=>{if(n.statusCode===200){console.log("success"),y("Create"),N(`Mass Bank Key Sent for Review with ID NBM${n.body}`),P("success"),K(!1),w(!0),W(),R(!0),D(!1);const i={artifactId:z,createdBy:k==null?void 0:k.emailId,artifactType:"BankKey",requestId:`NBM${n==null?void 0:n.body}`},m=g=>{console.log("Second API success",g)},C=g=>{console.error("Second API error",g)};A(`/${ia}/documentManagement/updateDocRequestId`,"post",m,C,i)}else y("Error"),w(!1),N("Failed Submitting the Bank key for Review "),P("danger"),K(!1),R(!0),G(),D(!1);handleClose(),D(!1)},c=n=>{console.log(n)};A(`/${V}/massAction/bankKeysSubmitForReview`,"post",l,c,t)},Va=()=>{D(!0);const e=v.filter((i,m)=>$.includes(m));console.log("selectedData",e);const o=e.map(i=>({...S[i==null?void 0:i.id]}));console.log("selectedBankKeyRows",o);const t=[];o.map(i=>{var m={bankkey:i==null?void 0:i.Add,country:i==null?void 0:i.bankCountry};t.push(m)}),console.log("duplicateCheckPayload",t),console.log("duplicateCheckPayload",t);let l=S;l=o;const c=i=>{i.statusCode===400?(Aa(i.body),te(!0),D(!1)):(y("Create"),console.log("success"),y("Create"),N("All Data has been Validated. Bank Key can be Send for Review"),P("success"),K(!1),w(!0),W(),R(!0),ee(!0),D(!1),Sa(!1),Na(!1))},n=i=>{console.log(i)};A(`/${V}/massAction/validateMassBankKey`,"post",c,n,l)},$a=()=>{const e=v.filter((n,i)=>$.includes(i));console.log("selectedData",e);const o=e.map(n=>({...S[n==null?void 0:n.id]}));let t=S;t=o,console.log("selectedbankKeyRows",o);const l=n=>{if(n.statusCode===200){console.log("success"),y("Create"),N(`Mass Bank Key Sent for Review with ID CBM${n.body}`),P("success"),K(!1),w(!0),W(),R(!0),D(!1);const i={artifactId:z,createdBy:k==null?void 0:k.emailId,artifactType:"BankKey",requestId:`CBM${n==null?void 0:n.body}`},m=g=>{console.log("Second API success",g)},C=g=>{console.error("Second API error",g)};A(`/${ia}/documentManagement/updateDocRequestId`,"post",m,C,i)}else y("Error"),w(!1),N("Failed Submitting the Bank Key for Review "),P("danger"),K(!1),R(!0),G(),D(!1);handleClose(),D(!1)},c=n=>{console.log(n)};A(`/${V}/massAction/changeBankKeysSubmitForReview`,"post",l,c,t)},ge=()=>{te(!1)},ja=()=>{console.log(M.length,"remarks.length"),M.length<=0?U(!0):(U(!1),D(!0),O(),f==="Create"?La():$a())},za=(e,o)=>{console.log(e.target.value.toUpperCase(),"uppercase letter");const t=e.target.value.toUpperCase();if(t.length>0&&t[0]===" ")ie(t.trimStart());else{let l=t.toUpperCase();console.log(l,"remarksUpperCase"),ie(l)}},Ua=[{field:"id",headerName:"ID",hide:!0,editable:!1,flex:1},{field:"bankKey",headerName:"Bank Key",editable:!1,flex:1},{field:"error",headerName:"Error",editable:!1,flex:1}],H=(he=I==null?void 0:I.filter(e=>(e==null?void 0:e.code)===400))==null?void 0:he.map((e,o)=>{var t;if(e.code===400)return{id:o,bankKey:e==null?void 0:e.bankKey,error:(t=e==null?void 0:e.status)==null?void 0:t.message}});console.log("validationRows",H);const O=()=>{U(!1),E(!0),ne(!1)},De=()=>{E(!1),ne(!0)};return console.log(F,"isloading"),u(Ja,{children:[F===!0?a(Qa,{}):u("div",{children:[u(X,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:ya,onClose:O,children:[u(Z,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[a(p,{variant:"h6",children:"REMARKS"}),a(L,{sx:{width:"max-content"},onClick:O,children:a(na,{})})]}),a(Y,{sx:{padding:".5rem 1rem"},children:a(Q,{children:u(la,{sx:{minWidth:400},children:[a(Ya,{sx:{height:"auto"},fullWidth:!0,children:a(xa,{sx:{backgroundColor:"#F5F5F5"},onChange:za,value:M,multiline:!0,placeholder:"ENTER REMARKS",inputProps:{maxLength:254}})}),wa&&a(h,{children:a(p,{style:{color:"red"},children:"Please Enter Remarks"})})]})})}),u(J,{sx:{display:"flex",justifyContent:"end"},children:[a(B,{sx:{width:"max-content",textTransform:"capitalize"},onClick:O,children:"Cancel"}),a(B,{className:"button_primary--normal",type:"save",onClick:ja,variant:"contained",children:"Submit"})]})]}),a(es,{dialogState:ka,openReusableDialog:G,closeReusableDialog:ue,dialogTitle:ga,dialogMessage:se,handleDialogConfirm:ue,dialogOkText:"OK",handleExtraButton:Ea,dialogSeverity:Ca}),ha&&a(as,{openSnackBar:pa,alertMsg:se,handleSnackBarClose:Ia}),u("div",{style:{...Za,backgroundColor:"#FAFCFF"},children:[a(h,{container:!0,sx:ss,children:u(h,{item:!0,md:12,sx:{display:"flex",marginBottom:"0"},children:[u(h,{item:!0,md:11,sx:{display:"flex"},children:[a(h,{children:a(L,{color:"primary","aria-label":"upload picture",component:"label",sx:ts,children:a(cs,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{q("/masterDataCockpit/bankKey")}})})}),a(h,{children:f==="Create"?u(h,{item:!0,md:12,children:[a(p,{variant:"h3",children:a("strong",{children:"Create Multiple Bank Keys"})}),a(p,{variant:"body2",color:"#777",children:"This view creates multiple Bank Keys"})]}):u(h,{item:!0,md:12,children:[a(p,{variant:"h3",children:a("strong",{children:"Change Multiple Bank Keys"})}),a(p,{variant:"body2",color:"#777",children:"This view changes multiple bank Keys"})]})})]}),a(h,{item:!0,md:1,sx:{display:"flex"},children:a(os,{title:"Upload documents if any",arrow:!0,children:a(L,{onClick:Fa,children:a(us,{})})})}),u(X,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:ba,onClose:ce,children:[a(Z,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:a(p,{variant:"h6",children:"Add Attachment"})}),a(Y,{sx:{padding:".5rem 1rem"},children:a(Q,{children:a(la,{sx:{minWidth:400},children:a(ms,{title:"BankKey",useMetaData:!1,artifactId:z,artifactName:"BankKey"})})})}),a(J,{children:a(B,{onClick:ce,children:"Close"})})]})]})}),a(h,{item:!0,sx:{position:"relative"},children:a(Q,{children:a(oa,{isLoading:F,width:"100%",title:"Bank Key Master List ("+v.length+")",rows:v,columns:_a,pageSize:10,getRowIdValue:"id",hideFooter:!1,checkboxSelection:!0,disableSelectionOnClick:!0,status_onRowSingleClick:!0,onRowsSelectionHandler:Oa,callback_onRowSingleClick:e=>{console.log("paramss",e);const o=e.row.bankKey,t=b.find(l=>l.BankKey===o);q(`/masterDataCockpit/bankKey/createMultipleBankKey/editMultipleBankKey/${o}`,{state:{tabsData:t,rowData:e.row}})},stopPropagation_Column:"action",status_onRowDoubleClick:!0})})})]}),a(ls,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:u(ns,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},value:ua,children:[a(B,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:Va,disabled:!Ba,children:"Validate"}),f==="Create"?a(B,{variant:"contained",size:"small",sx:{...x},onClick:De,disabled:oe,children:"Submit for Review"}):f==="Change"?a(B,{variant:"contained",size:"small",sx:{...x},onClick:De,disabled:oe,children:"Submit for Review"}):""]})}),u(X,{open:fa,fullWidth:!0,onClose:ge,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[u(Z,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[a(p,{variant:"h6",color:"red",children:"Errors"}),a(L,{sx:{width:"max-content"},onClick:ge,children:a(na,{})})]}),a(Y,{sx:{padding:".5rem 1rem"},children:H&&a(oa,{isLoading:F,width:"100%",rows:H,columns:Ua,pageSize:10,getRowIdValue:"id",hideFooter:!0,checkboxSelection:!1,disableSelectionOnClick:!0,status_onRowSingleClick:!0,stopPropagation_Column:"action",status_onRowDoubleClick:!0})}),a(J,{sx:{display:"flex",justifyContent:"end"}})]})]}),a(rs,{sx:{color:"#fff",zIndex:e=>e.zIndex.drawer+1},open:ca,children:a(is,{color:"inherit"})})]})};export{As as default};
