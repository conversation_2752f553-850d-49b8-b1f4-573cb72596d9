import{s as D,q as l,dC as N,dD as b,dE as v,dF as S,dG as C,dH as U,dI as m,dJ as G,dK as R,dL as j,dM as H,dN as $}from"./index-17b8d91e.js";const I=()=>{const g=D(),s=l(a=>a.userManagement.userData),o=l(a=>a.changeLog.createPayloadCopyForChangeLog||[]),p=l(a=>a.changeLog.createTemplateArray);return{updateChangeLog:({materialID:a,viewName:t,plantData:u,fieldName:n,jsonName:d,currentValue:L,requestId:h,childRequestId:r,isDescriptionData:A=!1,isUnitOfMeasure:y=!1,isAdditionalEAN:i=!1,uomId:P=null,eanId:V=null,language:E})=>{let e;A?e=N(a,d,o,E):y?e=b(a,P,n,o):i?e=v(a,V,n,o):e=S(a,t,u,d,o);const F=C(U,t),T=m(u,F),f=C(G,t),c={ObjectNo:`${a}${T}`,ChangedBy:s==null?void 0:s.emailId,ChangedOn:R,FieldName:n,PreviousValue:e,CurrentValue:L,SAPValue:e,tableName:f};g(j(c));const O=[...p,c],_=H(O);let M={RequestId:h,changeLogId:null,ChildRequestId:r==null?void 0:r.slice(3),..._};g($(M))}}};export{I as u};
