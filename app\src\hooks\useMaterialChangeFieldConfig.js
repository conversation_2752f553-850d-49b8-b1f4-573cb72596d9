import { changeTemplateDT, setDataLoading } from '@app/tabsDetailsSlice';
import { doAjax } from '@components/Common/fetchService';
import React from 'react'
import { useSelector } from 'react-redux';
import { useDispatch } from 'react-redux';
import { destination_IDM } from '../destinationVariables';
import { END_POINTS } from '@constant/apiEndPoints';
import { API_CODE, LOCAL_STORAGE_KEYS, REGION_CODE, REQUEST_TYPE, TASK_NAME } from '@constant/enum';
import { setDropDown } from "../app/dropDownDataSlice"
import useLogger from './useLogger';
import { filterConfigData, filterFieldNameData, getLocalStorage } from '@helper/helper';
import { setChangeTableData } from '@app/payloadSlice';
import { useLocation } from 'react-router-dom';

const useMaterialChangeFieldConfig = () => {
    const { customError } = useLogger();
    const initialPayload = useSelector((state) => state.payload.payloadData);
    const applicationConfig = useSelector((state) => state.applicationConfig);
    const taskDataString = getLocalStorage(LOCAL_STORAGE_KEYS.CURRENT_TASK);
    let finalTaskData = null;
    finalTaskData = typeof taskDataString === "string" ? JSON.parse(taskDataString) : taskDataString;
    let workflowGroup = finalTaskData?.ATTRIBUTE_5;
    const dispatch = useDispatch();
    const location = useLocation();
    const queryParams = new URLSearchParams(location.search);
    const taskData = useSelector((state) => state.userManagement.taskData);
    const isreqBench = queryParams.get("reqBench");
    const RequestId = queryParams.get("RequestId");
    const getChangeTemplate = (reqHeaderData, apiData) => {
      dispatch(setDataLoading(true))
        let payload = {
          decisionTableId: null,
          decisionTableName: "MDG_MAT_CHANGE_TEMPLATE",
          version: "v6",
          rulePolicy: null,
          validityDate: null,
          conditions: [
            {
              "MDG_CONDITIONS.MDG_MAT_TEMPLATE": initialPayload?.TemplateName || reqHeaderData?.TemplateName,
              "MDG_CONDITIONS.MDG_MAT_REGION": initialPayload?.Region || reqHeaderData?.Region || REGION_CODE?.US,
              "MDG_CONDITIONS.MDG_MAT_GROUP_ROLE": (RequestId && !isreqBench) ? taskData?.ATTRIBUTE_5 || workflowGroup : TASK_NAME?.REQ_INITIATE,
              "MDG_CONDITIONS.MDG_MAT_SCENARIO": initialPayload?.RequestType || reqHeaderData?.RequestType || REQUEST_TYPE?.CHANGE,
            },
          ],
          systemFilters: null,
          systemOrders: null,
          filterString: null,
        };
        // setIsLoading(true);
        const hSuccess = (data) => {
          if (data.statusCode === API_CODE?.STATUS_200) {
            dispatch(setDataLoading(false))
            let responseData = data?.data?.result[0]?.MDG_MAT_CHANGE_TEMPLATE;
            let template = initialPayload?.TemplateName || reqHeaderData?.TemplateName || ""
            // Step 1: Sort and map the data
            const formattedData = responseData
              ?.filter(item => item.MDG_MAT_VIEW_NAME !== "Header")
              ?.sort((a, b) => a.MDG_MAT_FIELD_SEQUENCE - b.MDG_MAT_FIELD_SEQUENCE)
              ?.map((item) => ({
                fieldName: item.MDG_MAT_UI_FIELD_NAME,
                viewName: item.MDG_MAT_VIEW_NAME,
                sequenceNo: item.MDG_MAT_FIELD_SEQUENCE,
                fieldType: item.MDG_MAT_FIELD_TYPE,
                maxLength: item.MDG_MAT_MAX_LENGTH,
                value: item.MDG_MAT_DEFAULT_VALUE,
                visibility: item.MDG_MAT_VISIBILITY,
                jsonName: item.MDG_MAT_JSON_FIELD_NAME,
                templateVisibility: item.MDG_MAT_TEMPLATE_SELECTIVITY,
              }));
        
            let changeFieldNames = [];
        
            const configData = formattedData?.reduce((acc, item) => {
              if (
                item.fieldName !== "Material" &&
                item.fieldName !== "Plant" &&
                item.fieldName !== "Warehouse" &&
                item.fieldName !== "Sales Org" &&
                item.fieldName !== "Distribution Channel" &&
                item.fieldName !== "MRP Controller"
              ) {
                changeFieldNames.push({ code: item.fieldName, desc: "" });
              }
            
              const viewName = item.viewName;
              if (!acc[viewName]) {
                acc[viewName] = [];
              }
              acc[viewName].push(item);
              return acc;
            }, {});
            
            // Sort the object keys alphabetically
            const sortedConfigData = Object?.keys(configData)
              ?.sort()
              ?.reduce((sortedAcc, key) => {
                sortedAcc[key] = configData[key];
                return sortedAcc;
              }, {});

            const mandatoryFields = [];
            formattedData?.forEach((item) => {
              if (item?.visibility === "Mandatory") {
                mandatoryFields.push({
                  jsonName: item?.jsonName,
                  fieldName: item?.fieldName,
                });
              }
            });
        
            const changeFieldsObj = {
              [template]: formattedData,
              "Config Data": sortedConfigData,
              "Field Selectivity": formattedData[0]?.templateVisibility,
              "FieldNames": changeFieldNames,
              "Mandatory Fields": [{ jsonName: "Material", fieldName: "Material" }, ...mandatoryFields],
            };
        
            // Dispatch the final structure
            dispatch(setDropDown({ keyName: "FieldName", data: changeFieldsObj?.FieldNames }))
            dispatch(changeTemplateDT(changeFieldsObj));

            if (Object?.keys(apiData).length > 0) {
              const filteredConfig = filterConfigData(
                changeFieldsObj?.["Config Data"],
                apiData?.Torequestheaderdata?.FieldName?.split("$^$"),
                ["Material", "Plant", "Sales Org", "Distribution Channel", "Warehouse", "MRP Controller"]
              );
              dispatch(changeTemplateDT({ ...changeFieldsObj, "Config Data": filteredConfig }));
              const filteredTableNames = filterFieldNameData(changeFieldsObj?.[template || apiData?.Torequestheaderdata?.TemplateName], apiData?.Torequestheaderdata?.FieldName?.split("$^$"))
              dispatch(setChangeTableData([...filteredTableNames]));
            }
          } else {
            dispatch(setDataLoading(false))
            customError("Failed to fetch data");
          }
        };
        
        const hError = (error) => {
          customError(error);
        };
        if (applicationConfig.environment === "localhost") {
          doAjax(`/${destination_IDM}${END_POINTS.INVOKE_RULES.LOCAL}`, "post", hSuccess, hError, payload);
        } else {
          doAjax(`/${destination_IDM}${END_POINTS.INVOKE_RULES.PROD}`, "post", hSuccess, hError, payload);
        }
      };
    
    return { getChangeTemplate };
}

export default useMaterialChangeFieldConfig