import {
  Backdrop,
  BottomNavigation,
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  Paper,
  Stack,
  Step,
  StepLabel,
  Stepper,
  Tab,
  Tabs,
  TextField,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  iconButton_SpacingSmall,
  button_Primary,
  button_Outlined,
  outermostContainer_Information,
} from "../../common/commonStyles";
import CloseIcon from "@mui/icons-material/Close";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import BasicDataProfitCenter from "../ProfitCenterTabs/BasicDataProfitCenter";
import CompCodesProfitCenter from "../ProfitCenterTabs/CompCodeProfitCenter";
import IndicatorsProfitCenter from "../ProfitCenterTabs/IndicatorsProfitCenter";
import { useLocation, useNavigate } from "react-router-dom";

import AddressProfitCenter from "../ProfitCenterTabs/AddressProfitCenter";
import CommunicationProfitCenter from "../ProfitCenterTabs/CommunicationProfitCenter";
import HistoryProfitCenter from "../ProfitCenterTabs/HistoryProfitCenter";
import { doAjax } from "../../Common/fetchService";
import {
  destination_DocumentManagement,
  destination_ProfitCenter,
} from "../../../destinationVariables";
import ReusableSnackBar from "../../Common/ReusableSnackBar";
import {
  clearProfitCenter,
  clearProfitCenterPayload,
  setPCErrorFields,
} from "../../../app/profitCenterTabsSlice";
import { formValidator, idGenerator } from "../../../functions";
import ReusableDialog from "../../Common/ReusableDialog";
import ReusableAttachementAndComments from "../../Common/ReusableAttachmentAndComments/ReusableAttachementAndComments";
import { setDropDown } from "../../../app/dropDownDataSlice";
import lookup from "../../../data/lookup.json";
import LoadingComponent from "../../Common/LoadingComponent";

const NewSingleProfitCenter = () => {
  const [activeTab, setActiveTab] = useState(0);
  // const [dropDownData, setDropDownData] = useState({});
  const location = useLocation();
  const displayData = location.state;
  console.log("displaydata", displayData);
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const basicDataTabDetails = useSelector(
    (state) => state.profitCenter.profitCenterBasicData
  );
  const compCodesTabDetails = useSelector(
    (state) => state.profitCenter.profitCenterCompCodes
  );
  const indicatorsTabDetails = useSelector(
    (state) => state.profitCenter.profitCenterIndicators
  );
  const addressTabDetails = useSelector(
    (state) => state.profitCenter.profitCenterAddress
  );
  const communicationTabDetails = useSelector(
    (state) => state.profitCenter.profitCenterCommunication
  );
  const historyTabDetails = useSelector(
    (state) => state.profitCenter.profitCenterHistory
  );
  const payloadData = useSelector(
    (state) => state.profitCenter.singlePCPayload
  );
  console.log("payloadData", payloadData);
  const [activeStep, setActiveStep] = useState(0);
  const [messageDialogTitle, setMessageDialogTitle] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [messageDialogSeverity, setMessageDialogSeverity] = useState(false);
  const [messageDialogOK, setMessageDialogOK] = useState(true);
  const [successMsg, setsuccessMsg] = useState(false);
  const [messageDialogExtra, setMessageDialogExtra] = useState(false);
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [blurLoading, setBlurLoading] = useState(false);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [formValidationErrorItems, setFormValidationErrorItems] = useState([]);
  const [openSnackbarValidation, setOpenSnackbarValidation] = useState(false);
  const [validateFlag, setValidateFlag] = useState(false);
  const [submitForReviewDisabled, setSubmitForReviewDisabled] = useState(true);
  const [pcNumber, setPcNumber] = useState("");
  const [openCorrectionDialog, setOpenCorrectionDialog] = useState(false);
  const [remarks, setRemarks] = useState("");
  const [handleExtrabutton, setandleExtrabutton] = useState(false);
  const [testrunStatus, setTestrunStatus] = useState(true);
  const [openCreateDialog, setOpenCreateDialog] = useState(false);
  const [handleExtraText, setHandleExtraText] = useState("");
  let userData = useSelector((state) => state.userManagement.userData);
  const dropDownData = useSelector((state) => state.AllDropDown.dropDown);
  const steps = [
    "BASIC DATA",
    "COMPANY CODE",
    "INDICATOR",
    "ADDRESS",
    "COMMUNICATION",
    // "HISTORY",
    "ATTACHMENTS & COMMENTS",
  ];

  console.log("isLoading", isLoading);
  const handleSnackBarOpen = () => {
    setopenSnackbar(true);
  };
  const handleSnackBarClose = () => {
    if (validateFlag) {
      setopenSnackbar(false);
      setValidateFlag(false);
    } else {
      setopenSnackbar(false);
      navigate("/masterDataCockpit/profitCenter");
    }
  };
  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };

  const handleNext = () => {
    const isValidation = handleCheckValidationError();
    if (isValidation) {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
    } else {
      handleSnackBarOpenValidation();
    }
  };

  const handleBack = () => {
    setSubmitForReviewDisabled(true);
    const isValidation = handleCheckValidationError();
    if (isValidation) {
      setActiveStep((prevActiveStep) => prevActiveStep - 1);
    } else {
      handleSnackBarOpenValidation();
    }
  };
  const handleRemarksDialogClose = () => {
    setOpenCorrectionDialog(false);
  };
  const handleRemarks = (e, value) => {
    //setRemarks(e.target.value);
    const newValue = e.target.value;
    if (newValue.length > 0 && newValue[0] === " ") {
      setRemarks(newValue.trimStart());
    } else {
      //let costCenterValue = e.target.value;
      let remarksUpperCase = newValue.toUpperCase();
      setRemarks(remarksUpperCase);
    }
  };
  const onProfitCenterSubmitRemarks = () => {
    handleCreateDialogClose();
    onSubmitForReviewButtonClick();
  };
  const payloadFields = useSelector(
    (state) => state.profitCenter.singlePCPayload
  );
  const requiredFields = useSelector(
    (state) => state.profitCenter.requiredFields
  );

  // const [rows, setrows] = useState(
  //   dropDownData?.CompCodeBasedOnControllingArea.map((x,idx) => {
  //     return { id:idx , companyCodes: x.code, companyName: "", assigned: "X" };
  //   })
  // );

  // console.log("rows", rows);
  const comapanyCodeID = "";

  const rows = useSelector(
    (state) => state.AllDropDown.dropDown.CompCodeBasedOnControllingArea
  );
  console.log("rows", rows);

  var payload = {
    ProfitCenterID: "",
    RequestID: "",
    TaskId: "",
    Action: "I",
    TaskStatus: "",
    ReqCreatedBy: userData?.user_id,
    ReqCreatedOn: "",
    RequestStatus: "",
    Remarks: remarks ? remarks : "",
    CreationId: "",
    EditId: "",
    DeleteId: "",
    MassCreationId: "",
    MassEditId: "",
    MassDeleteId: "",
    RequestType: "Create",
    MassRequestStatus: "",
    PrctrName: payloadData?.Name ? payloadData?.Name : "",
    LongText: payloadData?.LongText ? payloadData?.LongText : "",
    InChargeUser: payloadData?.UserResponsible?.code
      ? payloadData?.UserResponsible?.code
      : "",
    InCharge: payloadData?.PersonResponsible
      ? payloadData?.PersonResponsible
      : "",
    Department: payloadData?.Department ? payloadData?.Department : "",
    PrctrHierGrp: payloadData?.ProfitCtrGroup?.code
      ? payloadData?.ProfitCtrGroup?.code
      : "",
    Segment: payloadData?.Segment?.code ? payloadData?.Segment?.code : "",
    LockInd: payloadData?.Lockindicator === true ? "X" : "",
    Template: payloadData?.FormPlanningTemp?.code
      ? payloadData?.FormPlanningTemp?.code
      : "",
    Title: payloadData?.Title ? payloadData?.Title : "",
    Name1: payloadData?.Name1 ? payloadData?.Name1 : "",
    Name2: payloadData?.Name2 ? payloadData?.Name2 : "",
    Name3: payloadData?.Name3 ? payloadData?.Name3 : "",
    Name4: payloadData?.Name4 ? payloadData?.Name4 : "",
    Street: payloadData?.Street ? payloadData?.Street : "",
    City: payloadData?.City ? payloadData?.City : "",
    District: payloadData?.District ? payloadData?.District : "",
    Country: payloadData?.CountryReg?.code ? payloadData?.CountryReg?.code : "",
    Taxjurcode: payloadData?.TaxJur?.code ? payloadData?.TaxJur?.code : "",
    PoBox: payloadData?.POBox ? payloadData?.POBox : "",
    PostlCode: payloadData?.PostalCode ? payloadData?.PostalCode : "",
    PobxPcd: payloadData?.POBoxPCode ? payloadData?.POBoxPCode : "",
    Region: payloadData?.Region?.code ? payloadData?.Region?.code : "",
    Langu: payloadData?.Language?.code ? payloadData?.Language?.code : "EN",
    Telephone: payloadData?.Telephone1 ? payloadData?.Telephone1 : "",
    Telephone2: payloadData?.Telephone2 ? payloadData?.Telephone2 : "",
    Telebox: payloadData?.Telebox ? payloadData?.Telebox : "",
    Telex: payloadData?.Telex ? payloadData?.Telex : "",
    FaxNumber: payloadData?.FaxNumber ? payloadData?.FaxNumber : "",
    Teletex: payloadData?.Teletex ? payloadData?.Teletex : "",
    Printer: payloadData?.Printername ? payloadData?.Printername : "",
    DataLine: payloadData?.Dataline ? payloadData?.Dataline : "",
    ProfitCenter:
      `P${displayData?.companyCode?.newCompanyCode.code}${displayData?.profitCenterName?.newProfitCenterName}`
        ? `P${displayData?.companyCode?.newCompanyCode.code}${displayData?.profitCenterName?.newProfitCenterName}`
        : "",
    ControllingArea: displayData?.controllingArea?.newControllingArea?.code
      ? displayData?.controllingArea?.newControllingArea?.code
      : "",
    ValidfromDate: payloadData?.AnalysisPeriodFrom
      ? "/Date(" + Date.parse(payloadData?.AnalysisPeriodFrom) + ")/"
      : "",
    ValidtoDate: payloadData?.AnalysisPeriodTo
      ? "/Date(" + Date.parse(payloadData?.AnalysisPeriodTo) + ")/"
      : "",
    Testrun: testrunStatus,
    Countryiso: "",
    LanguIso: "",
    Logsystem: "",
    ToCompanycode: rows?.map((item) => {
      return {
        CompCodeID: "",
        CompanyName: item?.companyName,
        AssignToPrctr: item.assigned,
        CompCode: item.companyCodes,
      };
    }),
  };

  // Loader and lookup for independent apis start
  const [apiCount, setApiCount] = useState(0);
  const fetchDynamicApiData = (keyName, endPoint) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: keyName, data: data.body }));
      // setIsLoading(false);
      setApiCount((prev) => prev + 1);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/data/${endPoint}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getAllLookups = () => {
    lookup?.profitCenter?.map((item) => {
      fetchDynamicApiData(item?.keyName, item?.endPoint);
    });
  };
  const loaderCount = () => {
    if (apiCount == lookup?.profitCenter?.length) {
      setIsLoading(false);
    } else {
      setIsLoading(true);
    }
  };
  useEffect(() => {
    loaderCount();
  }, [apiCount]);

  // Loader and lookup for independent apis end

  useEffect(() => {
    getAllLookups();
  }, []);
  useEffect(() => {
    dispatch(clearProfitCenter());
  }, []);

  useEffect(() => {
    dispatch(setPCErrorFields(formValidationErrorItems));
  }, [formValidationErrorItems]);
  useEffect(() => {
    setPcNumber(idGenerator("PC"));
  }, []);
  const handleCheckValidationError = () => {
    return formValidator(
      payloadFields,
      requiredFields,
      setFormValidationErrorItems
    );
  };
  const handleSnackBarOpenValidation = () => {
    setOpenSnackbarValidation(true);
  };
  const onSubmitForReviewButtonClick = () => {
    setIsLoading(true);
    // const formData = new FormData();
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Profit Center has been Submitted for review NPS${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // handleRemarksDialogClose();
        // setIsLoading(false);

        // Make the second API call

        const secondApiPayload = {
          artifactId: pcNumber,
          createdBy: userData?.emailId,
          artifactType: "ProfitCenter",
          requestId: `NPS${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
        // }
      } else {
        setMessageDialogTitle("Create");
        setsuccessMsg(false);
        setMessageDialogMessage("Creation Failed");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleClose();
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter}/alter/profitCenterSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  // const onSubmitForReviewButtonClick = () => {
  //   const hSuccess = (data) => {
  //     setIsLoading();
  //     if (data.statusCode === 201) {
  //       console.log("success");
  //       setMessageDialogTitle("Create");
  //       setMessageDialogMessage(
  //         `Profit Center has been submitted for review NPS${data.body}`
  //       );
  //       setMessageDialogSeverity("success");
  //       setMessageDialogOK(false);
  //       setsuccessMsg(true);
  //       handleSnackBarOpen();
  //       setMessageDialogExtra(true);
  //       setIsLoading(false);
  //     } else {
  //       setMessageDialogTitle("Create");
  //       setsuccessMsg(false);
  //       setMessageDialogMessage("Creation Failed");
  //       setMessageDialogSeverity("danger");
  //       setMessageDialogOK(false);
  //       setMessageDialogExtra(true);
  //       handleMessageDialogClickOpen();
  //       setIsLoading(false);
  //     }
  //     handleClose();
  //   };

  //   const hError = (error) => {
  //     console.log(error);
  //   };

  //   doAjax(
  //     `/${destination_ProfitCenter}/alter/profitCenterSubmitForReview`,
  //     "post",
  //     hSuccess,
  //     hError,
  //     payload
  //   );
  // };
  const onSaveAsDraftButtonClick = () => {
    //
    setMessageDialogSeverity(false);
    setMessageDialogSeverity(false);
    handleMessageDialogClickOpen();
    setMessageDialogTitle("Confirm");
    setMessageDialogMessage(`Do You Want to Save as Draft ?`);
    setandleExtrabutton(true);
    setHandleExtraText("proceed");
  };
  const handleExtrbutton = () => {
    setIsLoading(true);
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setOpenMessageDialog(false);
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Profit Center has been saved with creation ID NPS${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        const secondApiPayload = {
          artifactId: pcNumber,
          createdBy: userData?.emailId,
          artifactType: "ProfitCenter",
          requestId: `NPS${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
      } else {
        setOpenMessageDialog(false);
        setMessageDialogTitle("Save");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Saving the Data ");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/alter/profitCenterAsDraft`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCreateDialog = () => {
    setTestrunStatus(false);
    setOpenCreateDialog(true);
  };
  const handleCreateDialogClose = () => {
    setTestrunStatus(true);
    setOpenCreateDialog(false);
  };
  const handleOpenCorrectionDialog = () => {
    setOpenCorrectionDialog(true);
  };
  // const onValidateProfitCenter = () => {
  //   const hSuccess = (data) => {
  //     setIsLoading();
  //     if (data.statusCode === 400) {
  //       setMessageDialogTitle("Error");
  //       setsuccessMsg(false);
  //       setMessageDialogMessage(`${data.body.message[0]}`);
  //       setMessageDialogSeverity("danger");
  //       setMessageDialogOK(false);
  //       setMessageDialogExtra(true);
  //       handleMessageDialogClickOpen();
  //       setIsLoading(false);
  //     } else {
  //       setMessageDialogTitle("Create");
  //       console.log("success");
  //       setMessageDialogTitle("Create");
  //       setMessageDialogMessage(
  //         `All Data has been Validated. Profit Center can be Send for Review`
  //       );
  //       setMessageDialogSeverity("success");
  //       setMessageDialogOK(false);
  //       setsuccessMsg(true);
  //       handleSnackBarOpen();
  //       setMessageDialogExtra(true);
  //       setIsLoading(false);
  //       setValidateFlag(true);
  //     }
  //     handleClose();
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(
  //     `/${destination_ProfitCenter}/alter/validateSingleProfitCenter`,
  //     "post",
  //     hSuccess,
  //     hError,
  //     payload
  //   );
  // };
  const onValidateProfitCenter = () => {
    setBlurLoading(true);
    // Define duplicateCheckPayload outside of the onValidateCostCenter function
    const duplicateCheckPayload = {
      coArea: displayData?.controllingArea?.newControllingArea?.code || "",
      name: payloadData?.Name ? payloadData?.Name.toUpperCase() : "",
    };
    const hSuccess = (data) => {
      if (data.statusCode === 201) {
        // Handle success
        setMessageDialogTitle("Create");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `All Data has been Validated. Profit Center can be Sent for Review`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
        setValidateFlag(true);

        // Now, make the duplicate check API call
        // Ensure that the conditions for making the duplicate check API call are met
        if (
          duplicateCheckPayload.coArea !== "" ||
          duplicateCheckPayload.name !== ""
        ) {
          doAjax(
            `/${destination_ProfitCenter}/alter/fetchPCDescriptionDupliChk`,
            "post",
            hDuplicateCheckSuccess,
            hDuplicateCheckError,
            duplicateCheckPayload
          );
        }
      } else {
        // Handle error
        setBlurLoading(false);
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        // if(data.body.message ? )
        setMessageDialogMessage(
          `${
            data?.body?.message[0] ? data?.body?.message[0] : data?.body?.value
          }`
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
    };

    const hDuplicateCheckSuccess = (data) => {
      // Handle success of duplicate check
      if (
        data.body.length === 0 ||
        !data.body.some(
          (item) => item.toUpperCase() === duplicateCheckPayload.name
        )
      ) {
        // No direct match, enable the "Submit for Review" button
        setSubmitForReviewDisabled(false);
        setBlurLoading(false);
      } else {
        // Handle direct match
        setBlurLoading(false);
        setMessageDialogTitle("Duplicate Check");
        setsuccessMsg(false);
        setMessageDialogMessage(
          `There is a direct match for the Profit Center name. Please change the name.`
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setSubmitForReviewDisabled(true);
      }
    };

    const hDuplicateCheckError = (error) => {
      // Handle error of duplicate check
      console.log(error);
    };

    const hError = (error) => {
      console.log(error);
    };

    // Call the main API for validation
    doAjax(
      `/${destination_ProfitCenter}/alter/validateSingleProfitCenter`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const getStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <BasicDataProfitCenter
            basicDataTabDetails={basicDataTabDetails}
            dropDownData={dropDownData}
          />
        );
      case 1:
        return (
          <CompCodesProfitCenter
            compCodesTabDetails={compCodesTabDetails}
            // companyCodeBasedOnControllingArea = {}
            dropDownData={dropDownData}
            // rows={rows}
            // setrows={setrows}
          />
        );
      case 2:
        return (
          <IndicatorsProfitCenter
            indicatorsTabDetails={indicatorsTabDetails}
            dropDownData={dropDownData}
          />
        );
      case 3:
        return (
          <AddressProfitCenter
            addressTabDetails={addressTabDetails}
            dropDownData={dropDownData}
          />
        );
      case 4:
        return (
          <CommunicationProfitCenter
            communicationTabDetails={communicationTabDetails}
            dropDownData={dropDownData}
          />
        );
      // case 5:
      //   return (
      //     <HistoryProfitCenter
      //       historyTabDetails={historyTabDetails}
      //       dropDownData={dropDownData}
      //     />
      //   );
      case 5:
        return (
          <ReusableAttachementAndComments
            title="ProfitCenter"
            useMetaData={false}
            artifactId={pcNumber}
            artifactName="ProfitCenter"
          />
        );
      default:
        return "Unknown step";
    }
  };

  const handleSnackBarCloseValidation = () => {
    setOpenSnackbarValidation(false);
    // navigate("/masterDataCockpit/materialMaster/materialSingle");
  };
  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };
  const handleMessageDialogNavigate = () => {
    // navigate("/masterDataCockpit/materialMaster/materialSingle");
  };
  const handleWarningDialogClose = () => {
    setOpenMessageDialog(false);
  };

  return (
    <>
      {isLoading === true ? (
        <LoadingComponent />
      ) : (
        <div>
          <ReusableDialog
            dialogState={openMessageDialog}
            openReusableDialog={handleMessageDialogClickOpen}
            closeReusableDialog={handleMessageDialogClose}
            dialogTitle={messageDialogTitle}
            dialogMessage={messageDialogMessage}
            handleDialogConfirm={handleMessageDialogClose}
            dialogOkText={"OK"}
            showExtraButton={handleExtrabutton}
            dialogSeverity={messageDialogSeverity}
            showCancelButton={true}
            handleDialogReject={handleWarningDialogClose}
            handleExtraText={handleExtraText}
            handleExtraButton={handleExtrbutton}
          />

          {formValidationErrorItems.length != 0 && (
            <ReusableSnackBar
              openSnackBar={openSnackbarValidation}
              alertMsg={
                "Please fill the following Field: " +
                formValidationErrorItems.join(", ")
              }
              handleSnackBarClose={handleSnackBarCloseValidation}
            />
          )}
          {successMsg && (
            <ReusableSnackBar
              openSnackBar={openSnackbar}
              alertMsg={messageDialogMessage}
              handleSnackBarClose={handleSnackBarClose}
            />
          )}

          <Dialog
            hideBackdrop={false}
            elevation={2}
            PaperProps={{
              sx: { boxShadow: "none" },
            }}
            open={openCorrectionDialog}
            onClose={handleRemarksDialogClose}
          >
            {/* <Grid
          container
          sx={{ display: "flex", justifyContent: "space-between" }}
        > */}
            {/* <Grid item> */}
            <DialogTitle
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF40",
                // borderBottom: "1px solid grey",
                display: "flex",
              }}
            >
              <Typography variant="h6">Remarks</Typography>

              <IconButton
                sx={{ width: "max-content" }}
                onClick={handleRemarksDialogClose}
                children={<CloseIcon />}
              />
            </DialogTitle>
            {/* </Grid> */}
            {/* </Grid> */}
            <DialogContent sx={{ padding: ".5rem 1rem" }}>
              <Stack>
                <Box sx={{ minWidth: 400 }}>
                  <FormControl sx={{ height: "auto" }} fullWidth>
                    <TextField
                      sx={{ backgroundColor: "#F5F5F5" }}
                      value={remarks}
                      onChange={handleRemarks}
                      multiline
                      placeholder={"Enter Remarks"}
                      inputProps={{maxLength: 254}}
                    ></TextField>
                  </FormControl>
                </Box>
              </Stack>
              {/* <TextField
            autoFocus
            margin="dense"
            id="name"
            label="Enter Remarks for Correction"
            type="text"
            fullWidth
            variant="standard"
            onChange={handleRemarks}
          /> */}
            </DialogContent>
            <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
              <Button
                sx={{ width: "max-content", textTransform: "capitalize" }}
                onClick={handleRemarksDialogClose}
              >
                Cancel
              </Button>
              <Button
                className="button_primary--normal"
                type="save"
                onClick={onProfitCenterSubmitRemarks}
                variant="contained"
              >
                Submit
              </Button>
            </DialogActions>
          </Dialog>

          <Dialog
            hideBackdrop={false}
            elevation={2}
            PaperProps={{
              sx: { boxShadow: "none" },
            }}
            open={openCreateDialog}
            onClose={handleCreateDialogClose}
          >
            {/* <Grid
          container
          sx={{ display: "flex", justifyContent: "space-between" }}
        > */}
            {/* <Grid item> */}
            <DialogTitle
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF40",
                // borderBottom: "1px solid grey",
                display: "flex",
              }}
            >
              <Typography variant="h6">Remarks</Typography>

              <IconButton
                sx={{ width: "max-content" }}
                onClick={handleCreateDialogClose}
                children={<CloseIcon />}
              />
            </DialogTitle>
            {/* </Grid> */}
            {/* </Grid> */}
            <DialogContent sx={{ padding: ".5rem 1rem" }}>
              <Stack>
                <Box sx={{ minWidth: 400 }}>
                  <FormControl sx={{ height: "auto" }} fullWidth>
                    <TextField
                      sx={{ backgroundColor: "#F5F5F5" }}
                      // value={inputText}
                      value={remarks}
                      onChange={handleRemarks}
                      multiline
                      placeholder={"Enter Remarks"}
                      inputProps={{maxLength: 254}}
                    ></TextField>
                  </FormControl>
                </Box>
              </Stack>
              {/* <TextField
            autoFocus
            margin="dense"
            id="name"
            label="Enter Remarks for Correction"
            type="text"
            fullWidth
            variant="standard"
            onChange={handleRemarks}
          /> */}
            </DialogContent>
            <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
              <Button
                sx={{ width: "max-content", textTransform: "capitalize" }}
                onClick={handleCreateDialogClose}
              >
                Cancel
              </Button>
              <Button
                className="button_primary--normal"
                type="save"
                value={remarks}
                onClick={onProfitCenterSubmitRemarks}
                variant="contained"
              >
                Submit
              </Button>
            </DialogActions>
          </Dialog>

          <Backdrop
            sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
            open={blurLoading}
            // onClick={handleClose}
          >
            <CircularProgress color="inherit" />
          </Backdrop>

          <Grid
            container
            style={{
              ...outermostContainer_Information,
              backgroundColor: "#FAFCFF",
            }}
          >
            <Grid sx={{ width: "inherit" }}>
              <Grid item md={7} style={{ padding: "16px", display: "flex" }}>
                <Grid item md={5} sx={{ display: "flex" }}>
                  <Grid>
                    <IconButton
                      // onClick={handleBacktoRO}
                      color="primary"
                      aria-label="upload picture"
                      component="label"
                      sx={iconButton_SpacingSmall}
                    >
                      <ArrowCircleLeftOutlinedIcon
                        style={{
                          height: "1em",
                          width: "1em",
                          color: "#000000",
                        }}
                        onClick={() => {
                          navigate("/masterDataCockpit/profitCenter");
                          dispatch(clearProfitCenterPayload());
                        }}
                      />
                    </IconButton>
                  </Grid>
                  <Grid>
                    <Typography variant="h3">
                      <strong>Create Profit Center</strong>
                    </Typography>
                    <Typography variant="body2" color="#777">
                      This view creates a new Profit Center
                    </Typography>
                  </Grid>
                </Grid>
              </Grid>
              {/* ... Your other content ... */}
              <Grid container style={{ padding: "0 1rem 0 1rem" }}>
                <Grid container sx={outermostContainer_Information}>
                  <Grid
                    container
                    display="flex"
                    flexDirection="row"
                    flexWrap="nowrap"
                  >
                    <Box
                      width="70%"
                      sx={{ marginLeft: "40px", marginBottom: "20px" }}
                    >
                      <Grid item sx={{ paddingTop: "2px !important" }}>
                        <Stack flexDirection="row">
                          <div style={{ width: "10%" }}>
                            <Typography variant="body2" color="#777">
                              Profit Center
                            </Typography>
                          </div>
                          <Typography
                            variant="body2"
                            fontWeight="bold"
                            justifyContent="flex-start"
                          >
                            : P{displayData?.companyCode?.newCompanyCode.code}
                            {displayData?.profitCenterName?.newProfitCenterName}
                          </Typography>
                        </Stack>
                      </Grid>

                      <Grid item sx={{ paddingTop: "2px !important" }}>
                        <Stack flexDirection="row">
                          <div style={{ width: "10%" }}>
                            <Typography variant="body2" color="#777">
                              Controlling Area
                            </Typography>
                          </div>
                          <Typography variant="body2" fontWeight="bold">
                            :{" "}
                            {
                              displayData?.controllingArea?.newControllingArea
                                ?.code
                            }
                          </Typography>
                        </Stack>
                      </Grid>
                    </Box>
                  </Grid>
                  <Grid container>
                    <Stepper
                      activeStep={activeStep}
                      sx={{
                        background: "#FFFFFF",
                        borderBottom: "1px solid #BDBDBD",
                        width: "100%",
                        height: "48px",
                      }}
                    >
                      {steps.map((label, index) => (
                        <Step key={label}>
                          <StepLabel sx={{ fontWeight: "700" }}>
                            {label}
                          </StepLabel>
                        </Step>
                      ))}
                    </Stepper>
                  </Grid>

                  <Grid container>{getStepContent(activeStep)}</Grid>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
          <Paper
            sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
            elevation={2}
          >
            <BottomNavigation
              className="container_BottomNav"
              showLabels
              sx={{ display: "flex", justifyContent: "flex-end" }}
            >
              <Button
                variant="contained"
                size="small"
                sx={{ ...button_Primary, mr: 1 }}
                onClick={onSaveAsDraftButtonClick}
                // disabled={activeStep === 0}
              >
                Save As Draft
              </Button>
              <Button
                variant="contained"
                size="small"
                sx={{ ...button_Primary, mr: 1 }}
                onClick={handleBack}
                disabled={activeStep === 0}
              >
                Back
              </Button>
              {activeStep === steps.length - 1 ? (
                <>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={onValidateProfitCenter}
                  >
                    Validate
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleCreateDialog}
                    disabled={submitForReviewDisabled}
                  >
                    Submit For Review
                  </Button>
                </>
              ) : (
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary, mr: 1 }}
                  onClick={handleNext}
                >
                  Next
                </Button>
              )}
            </BottomNavigation>
          </Paper>
        </div>
      )}
    </>
  );
};

export default NewSingleProfitCenter;
