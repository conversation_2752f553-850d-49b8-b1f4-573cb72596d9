import React from "react";
import "./App.css";

const IframeComponent = (props) => {
  let url = props?.uri;
  if (props?.driveType === 1) {
    //Google Drive
    let uri = encodeURIComponent(props?.uri);
    url = `https://drive.google.com/viewerng/viewer?url=${uri}?pid=explorer&efh=false&a=v&chrome=false&embedded=true`;
  } else if (props?.driveType === 2) {
    //Microsoft Office
    url = `https://view.officeapps.live.com/op/embed.aspx?src=${props?.uri}`;
  } else {
    //Images
    url = props?.uri;
  }

  return (
    <>
       <div className="Iframe-container">
        <div className="Iframe-content">
          <iframe src={url} width="100%" height="650px" frameborder="0" >
            This is an embedded{" "}
            <a target="_blank" rel="noreferrer" href="http://office.com">
              Microsoft Office
            </a>{" "}
            document, powered by{" "}
            <a target="_blank" rel="noreferrer" href="http://office.com/webapps">
              Office Online
            </a>
            .
          </iframe>
      </div>
      </div>
    </>
  );
};

export default IframeComponent;
