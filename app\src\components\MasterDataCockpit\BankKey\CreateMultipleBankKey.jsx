import {
    Backdrop,
    BottomNavigation,
    Box,
    Button,
    Checkbox,
    CircularProgress,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    FormControl,
    Grid,
    IconButton,
    Paper,
    Stack,
    Tab,
    Tabs,
    TextField,
    Tooltip,
    Typography,
  } from "@mui/material";
  import React, { useEffect, useState } from "react";
  import CloseIcon from "@mui/icons-material/Close";
  import {
    iconButton_SpacingSmall,
    outermostContainer,
    button_Primary,
    outerContainer_Information,
    outermostContainer_Information,
  } from "../../common/commonStyles";
  import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
  import ReusableTable from "../../common/ReusableTable";
  import { useNavigate, useLocation } from "react-router-dom";
  import {
      destination_BankKey,
    destination_DocumentManagement,
  } from "../../../destinationVariables";
  import { useDispatch, useSelector } from "react-redux";
  import { doAjax } from "../../Common/fetchService";
  import ReusableDialog from "../../Common/ReusableDialog";
  import ReusableSnackBar from "../../Common/ReusableSnackBar";
  import moment from "moment/moment";
  import lookup from "../../../data/lookup.json";
  import LoadingComponent from "../../Common/LoadingComponent";
  import { setDropDown } from "../../../app/dropDownDataSlice";
  import { idGenerator } from "../../../functions";
  import AttachFileOutlinedIcon from "@mui/icons-material/AttachFileOutlined";
  import ReusableAttachementAndComments from "../../Common/ReusableAttachmentAndComments/ReusableAttachementAndComments";
import { clearBankKey } from "../../../app/bankKeyTabSlice";
  
  const CreateMultipleBankKey = () => {
    const [isLoading, setIsLoading] = useState(false);
    const [blurLoading, setBlurLoading] = useState(false);
    const [value, setValue] = useState("1");
    const [selectedRows, setSelectedRows] = useState([]);
    const [messageDialogTitle, setMessageDialogTitle] = useState(false);
    const [validateFlag, setValidateFlag] = useState(false);
    const [successMsg, setsuccessMsg] = useState(false);
    const [messageDialogExtra, setMessageDialogExtra] = useState(false);
    const [openSnackbar, setopenSnackbar] = useState(false);
    const [openMessageDialog, setOpenMessageDialog] = useState(false);
    const [messageDialogMessage, setMessageDialogMessage] = useState("");
    const [messageDialogSeverity, setMessageDialogSeverity] = useState(false);
    const [messageDialogOK, setMessageDialogOK] = useState(true);
    const [dialogOpen, setDialogOpen] = useState(false);
    const [bankValidationError, setBankValidationErrors] = useState([]);
    const [submitForReviewDisabled, setSubmitForReviewDisabled] = useState(true);
    const [openCorrectionDialog, setOpenCorrectionDialog] = useState(false);
    const [openDialog, setOpenDialog] = useState(false);
    const [bkNumber, setBkNumber] = useState("");
    const [remarks, setRemarks] = useState("");
    const [testRun, setTestRun] = useState(false);
    const [profitValidationError, setProfitValidationErrors] = useState([]);
    const [bankKeyValidationError, setBankKeyValidationErrors] = useState([]);
    const [remarksValidationError,setRemarksValidationError]=useState(false)
    const [directMatchedBankKeys, setDirectMatchedBankKeys] = useState(
      []
    );
    const [testrunStatus, setTestrunStatus] = useState(true);
    const navigate = useNavigate();
    const location = useLocation();
    const dispatch = useDispatch();
    const multipleBankKeyData = useSelector(
      (state) => state.bankKey.MultipleBankKeyData
    );
  
    const appSettings = useSelector((state) => state.appSettings);
    let massHandleType = useSelector(
      (state) => state.bankKey.handleMassMode
    );
    console.log("massHandleType", massHandleType);
    let userData = useSelector((state) => state.userManagement.userData);

    // Loader and lookup for independent apis start
    const [apiCount, setApiCount] = useState(0);
    const fetchDynamicApiData = (keyName, endPoint) => {
      const hSuccess = (data) => {
        dispatch(setDropDown({ keyName: keyName, data: data.body }));
        // setIsLoading(false);
        setApiCount((prev) => prev + 1);
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        `/${destination_BankKey}/data/${endPoint}`,
        "get",
        hSuccess,
        hError
      );
    };
    const getAllLookups = () => {
      lookup?.bankKey?.map((item) => {
        fetchDynamicApiData(item?.keyName, item?.endPoint);
      });
    };
    const loaderCount = () => {
      if (apiCount == lookup?.bankkey?.length) {
        setIsLoading(false);
      } else {
        //setIsLoading(true);
      }
    };
    useEffect(() => {
      loaderCount();
    }, [apiCount]);
  
    // Loader and lookup for independent apis end
  
    useEffect(() => {
      setBkNumber(idGenerator("BK"));
    }, []);
  
    useEffect(() => {
      getAllLookups();
      dispatch(clearBankKey())
    }, []);
  
    //console.log("multipleProfitData", multipleProfitData);
    const handleSnackBarOpen = () => {
      setopenSnackbar(true);
    };
    const handleOpenDialog = () => {
      setOpenDialog(true);
    };
    const handleCloseDialog = () => {
      setOpenDialog(false);
    };
    const handleMessageDialogClickOpen = () => {
      setOpenMessageDialog(true);
    };
    const handleMessageDialogClose = () => {
      setOpenMessageDialog(false);
    };
    const handleMessageDialogNavigate = () => {
      // navigate("/masterDataCockpit/materialMaster/materialSingle");
    };
    const handleSnackBarClose = () => {
      if (validateFlag) {
        setopenSnackbar(false);
        setValidateFlag(false);
      } else {
        setopenSnackbar(false);
        navigate("/masterDataCockpit/bankKey");
      }
    };
  
    const getValueForFieldName = (data, fieldName) => {
      console.log("getvalueforfieldname", data, fieldName);
      const field = data?.find((field) => field?.fieldName === fieldName);
      return field ? field.value : "";
    };
    const getValueForFieldNameasTrueBlank = (data, fieldName) => {
      //console.log("getvalueforfieldname", data, fieldName);
      const field = data?.find((field) => field?.fieldName === fieldName);
      console.log(field.value,"field.value")
      return field.value === true ? "X" : "";
    };

  
    const handleSelectionModelChange = (selectedIds) => {
      if (selectedIds.length > 0) {
        setTestRun(true);
        console.log("selectedIds1", selectedIds);
      } else {
        setTestRun(false);
      }
      console.log("selectedIds", selectedIds);
      setSelectedRows(selectedIds);
      // setTestRun(true);
    };
  
    /*let compcodeData =
      multipleProfitData[0]?.viewData?.["Comp Codes"]?.[
        "Company Code Assignment for Profit Center"
      ];*/
      const columns = [
        {
          field: "bankKey",
          headerName: "Bank key",
          editable: false,
          flex: 1,
          renderCell: (params) => {
            const isDirectMatch = bankKeyValidationError.find(element=> element.bankKey===params.value);
            console.log(isDirectMatch, "isDirectMatch")
            console.log(params, "params")
      
            if (isDirectMatch && isDirectMatch.code === 400) {
              return (
                <Typography sx={{ fontSize: "12px", color: "red" }}>
                  {params.value}
                </Typography>
              );
            } else {
              return (
                <Typography sx={{ fontSize: "12px" }}>
                  {params.value}
                </Typography>
              );
            }
          },
        },
        {
          field: "bankName",
          headerName: "Bank Name",
          editable: false,
          flex: 1,
        },
        {
          field: "bankCountry",
          headerName: "Bank Country",
          editable: false,
          flex: 1,
        },
        {
          field: "Region",
          headerName: "Region",
          editable: false,
          flex: 1,
        },
        {
          field: "City",
          headerName: "City",
          editable: false,
          flex: 1,
        },
    
        {
          field: "bankNumber",
          headerName: "Bank Number",
          editable: false,
          flex: 1,
          
        },
        {
          field: "bankBranch",
          headerName: "Bank Branch",
          editable: false,
          flex: 1,
        },
      {
          field: "swiftBic",
          headerName: "Swift/Bic",
          editable: false,
          flex: 1,
        },
      ];
    const initialRows = multipleBankKeyData?.map((bKey, index) => {
      const headerData = bKey;
      const basicData = bKey?.viewData?.["Bank Details"] || {};
      console.log(basicData,"basicData")
      return {
        id: index,
        bankKey: headerData?.BankKey,
        bankCountry: headerData?.BankCtry,
        bankName:
          basicData["Address"].find((field) => field?.fieldName === "Bank Name")
            ?.value || "",
        Region:
          basicData["Address"].find(
            (field) => field?.fieldName === "Region"
          )?.value || "",
        City:
          basicData["Address"].find(
            (field) => field?.fieldName === "City"
          )?.value || "",
        bankNumber:
          basicData["Control Data"].find(
            (field) => field?.fieldName === "Bank Number"
          )?.value || "",
        bankBranch:
          basicData["Address"].find(
            (field) => field?.fieldName === "Bank Branch"
          )?.value || "",
        swiftBic:
          basicData["Control Data"].find(
            (field) => field?.fieldName === "SWIFT/BIC"
          )?.value || "",
      };
    });

    console.log(initialRows,"initialRows====")
  
    /*let tocompcode = _.zip(
      compcodeData[0].value,
      compcodeData[1].value,
      compcodeData[2].value
    ).map((x) => {
      return {
        CompCodeID: 0,
        CompCode: x[0],
        CompanyName: x[1],
        AssignToPrctr: x[2] == true ? "X" : "",
        Venture: "",
        RecInd: "",
        EquityTyp: "",
        JvOtype: "",
        JvJibcl: "",
        JvJibsa: "",
      };
    });*/
    console.log(multipleBankKeyData,"multipleBankKeyData")
   
    var payloadmapping = multipleBankKeyData.map((x) => {
      console.log("samsung", x);
      console.log(x?.viewData["Address Details"]?.["PO Box Address"],"powmodata");
      return {
          AddressDto: {
            languCr: "",
            AddressID: "",
            Title: x?.Title ? x?.Title : "",
            Name: getValueForFieldName(
              x?.viewData["Address Details"]?.["Name"],
              "Name"
            ),
            Name2: getValueForFieldName(
              x?.viewData["Address Details"]?.["Name"],
              "Name 1"
            ),
            Name3: getValueForFieldName(
              x?.viewData["Address Details"]?.["Name"],
              "Name 2"
            ),
            Name4: getValueForFieldName(
              x?.viewData["Address Details"]?.["Name"],
              "Name 3"
            ),
            Sort1: getValueForFieldName(
              x?.viewData["Address Details"]?.["Search Terms"],
              "Search Term 1"
            ),
            Sort2: getValueForFieldName(
              x?.viewData["Address Details"]?.["Search Terms"],
              "Search Term 2"
            ),
            BuildLong: getValueForFieldName(
              x?.viewData["Address Details"]?.["Street Address"],
              "Building Code"
            ),
            RoomNo: getValueForFieldName(
              x?.viewData["Address Details"]?.["Street Address"],
              "Room Number"
            ),
            Floor: getValueForFieldName(
              x?.viewData["Address Details"]?.["Street Address"],
              "Floor"
            ),
            
            COName: getValueForFieldName(
              x?.viewData["Address Details"]?.["Street Address"],
              "c/o"
            ),
            StrSuppl1: getValueForFieldName(
              x?.viewData["Address Details"]?.["Street Address"],
              "Street 1"
            ),
            StrSuppl2: getValueForFieldName(
              x?.viewData["Address Details"]?.["Street Address"],
              "Street 2"
            ),
            Street: getValueForFieldName(
              x?.viewData["Address Details"]?.["Street Address"],
              "Street 3"
            ),
            HouseNo: getValueForFieldName(
              x?.viewData["Address Details"]?.["Street Address"],
              "House Number"
            ),
            HouseNo2:getValueForFieldName(
              x?.viewData["Address Details"]?.["Street Address"],
              "Supplement"
            ),
            StrSuppl3: getValueForFieldName(
              x?.viewData["Address Details"]?.["Street Address"],
              "Street 4"
            ),
            Location: getValueForFieldName(
              x?.viewData["Address Details"]?.["Street Address"],
              "Street 5"
            ),
            District: getValueForFieldName(
              x?.viewData["Address Details"]?.["Street Address"],
              "District"
            ),
            HomeCity: getValueForFieldName(
              x?.viewData["Address Details"]?.["Street Address"],
              "Other City"
            ),
            PostlCod1: getValueForFieldName(
              x?.viewData["Address Details"]?.["Street Address"],
              "Postal Code"
            ),
            PostlCod2: x?.PostalCode1 ? x?.PostalCode1 : "",
            PostlCod3: x?.CompanyPostCd
              ? x?.CompanyPostCd
              : "",
            PoBox: x?.POBox ? x?.POBox : "",
            PoBoxCit:getValueForFieldName(
              x?.viewData["Address Details"]?.["PO Box Address"],
              "PO Box City"
            ),
            PoBoxReg: getValueForFieldName(
              x?.viewData["Address Details"]?.["PO Box Address"],
              "Region 2"
            ),
            PoboxCtry: getValueForFieldName(
              x?.viewData["Address Details"]?.["PO Box Address"],
              "Country 2"
            ),
            Country: getValueForFieldName(
              x?.viewData["Address Details"]?.["Street Address"],
              "Country 1"
            ),
            TimeZone: getValueForFieldName(
              x?.viewData["Address Details"]?.["Street Address"],
              "Time Zone"
            ),
            Taxjurcode: getValueForFieldName(
              x?.viewData["Address Details"]?.["Street Address"],
              "Tax Jurisdiction"
            ),
            Transpzone: getValueForFieldName(
              x?.viewData["Address Details"]?.["Street Address"],
              "Transport Zone"
            ),
            Regiogroup: getValueForFieldName(
              x?.viewData["Address Details"]?.["Street Address"],
              "Structure Group"
            ),
            DontUseS: getValueForFieldName(
              x?.viewData["Address Details"]?.["Street Address"],
              "Undeliverable"
            ),
            DontUseP: getValueForFieldName(
              x?.viewData["Address Details"]?.["PO Box Address"],
              "Undeliverable 1"
            ),
            PoWONo:getValueForFieldNameasTrueBlank(
              x?.viewData["Address Details"]?.["PO Box Address"],
              "PO Box w/o No."
            ),
            DeliServType: getValueForFieldName(
              x?.viewData["Address Details"]?.["PO Box Address"],
              "Delivery Service Type"
            ),
            DeliServNumber: getValueForFieldName(
              x?.viewData["Address Details"]?.["PO Box Address"],
              "Delivery Service No."
            ),
            Township: getValueForFieldName(
              x?.viewData["Address Details"]?.["PO Box Address"],
              "Township"
            ),
            Langu: getValueForFieldName(
              x?.viewData["Address Details"]?.["Communication"],
              "Language"
            ),
            Tel1Numbr: getValueForFieldName(
              x?.viewData["Address Details"]?.["Communication"],
              "Telephone"
            ),
            Tel1Ext: getValueForFieldName(
              x?.viewData["Address Details"]?.["Communication"],
              "Extension"
            ),
            MobilePhone: getValueForFieldName(
              x?.viewData["Address Details"]?.["Communication"],
              "Mobile Phone"
            ),
            FaxNumber: getValueForFieldName(
              x?.viewData["Address Details"]?.["Communication"],
              "Fax"
            ),
            FaxExtens: getValueForFieldName(
              x?.viewData["Address Details"]?.["Communication"],
              "Extension 1"
            ),
            EMail: getValueForFieldName(
              x?.viewData["Address Details"]?.["Communication"],
              "E-Mail Address"
            ),
            AdrNotes: getValueForFieldName(
              x?.viewData["Address Details"]?.["Communication"],
              "Notes"
            ),
            Region: getValueForFieldName(
              x?.viewData["Address Details"]?.["Street Address"],
              "Region 1"
            ),
            
            PoBoxLobby: x?.PoBoxLobby
              ? x?.PoBoxLobby
              : "",
            
          },
          BankKeyID:"",
          ReqCreatedBy: userData?.user_id,
          ReqCreatedOn: "",
          RequestStatus: "",
          CreationId: "",
          EditId: "",
          DeleteId: "",
          MassCreationId: "",
          MassEditId: "",
          MassDeleteId: "",
          RequestType: massHandleType === "Create" ? "Mass Create" : "Mass Change",
          TaskId: "",
          Remarks: remarks ? remarks : "",
          Action: massHandleType === "Create" ? "I" : "U",
          Validation: testrunStatus === true ? "X" : "",
          BankCtry: x?.BankCtry,
          BankKey: x?.BankKey,
          BankName: getValueForFieldName(
            x?.viewData["Bank Details"]?.["Address"],
            "Bank Name"
          ),
          BankRegion:getValueForFieldName(
            x?.viewData["Bank Details"]?.["Address"],
            "Region"
          ),
          BankStreet: getValueForFieldName(
            x?.viewData["Bank Details"]?.["Address"],
            "Street"
          ),
          City: getValueForFieldName(
            x?.viewData["Bank Details"]?.["Address"],
            "City"
          ),
          BankBranch: getValueForFieldName(
            x?.viewData["Bank Details"]?.["Address"],
            "Bank Branch"
          ),
          SwiftCode: getValueForFieldName(
            x?.viewData["Bank Details"]?.["Control Data"],
            "SWIFT/BIC"
          ),
          BankGroup: getValueForFieldName(
            x?.viewData["Bank Details"]?.["Control Data"],
            "Bank Group"
          ),
          PobkCurac:getValueForFieldNameasTrueBlank(
            x?.viewData["Bank Details"]?.["Control Data"],
            "Postbank Acct"
          ),
          BankNo: getValueForFieldName(
            x?.viewData["Bank Details"]?.["Control Data"],
            "Bank Number"
          ),
        }
    });
    
    console.log(payloadmapping,"payloadmapping====")
    const handleSubmitForReview = () => {
      const selectedData = initialRows.filter((_, index) =>
        selectedRows.includes(index)
      );
      console.log("selectedData", selectedData);
      const selectedBankKeyRows = selectedData.map((x) => ({
        // console.log("Data", x)
        ...payloadmapping[x?.id],
      }));
      let payload = payloadmapping;
      payload = selectedBankKeyRows;
      console.log("selectedBankKeyRows", selectedBankKeyRows);
      const hSuccess = (data) => {
        // setIsLoading(false);
        if (data.statusCode === 200) {
          console.log("success");
          setMessageDialogTitle("Create");
          setMessageDialogMessage(
            `Mass Bank Key Sent for Review with ID NBM${data.body}`
          );
          setMessageDialogSeverity("success");
          setMessageDialogOK(false);
          setsuccessMsg(true);
          handleSnackBarOpen();
          setMessageDialogExtra(true);
          setBlurLoading(false);
          // setIsLoading(false);
          const secondApiPayload = {
            artifactId: bkNumber,
            createdBy: userData?.emailId,
            artifactType: "BankKey",
            requestId: `NBM${data?.body}`,
          };
          const secondApiSuccess = (secondApiData) => {
            console.log("Second API success", secondApiData);
            // Handle success for the second API if needed
          };
  
          const secondApiError = (secondApiError) => {
            console.error("Second API error", secondApiError);
            // Handle error for the second API if needed
          };
          // {requestId&&
          doAjax(
            `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
            "post",
            secondApiSuccess,
            secondApiError,
            secondApiPayload
          );
        } else {
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          setMessageDialogMessage(
            "Failed Submitting the Bank key for Review "
          );
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          setBlurLoading(false);
          // setIsLoading(false);
        }
        handleClose();
        setBlurLoading(false);
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        //`/${destination_ProfitCenter}/massAction/profitCentersSubmitForReview`,
        `/${destination_BankKey}/massAction/bankKeysSubmitForReview`,
        "post",
        hSuccess,
        hError,
        payload
      );
    };
    const onValidateBankKey = () => {
      //alert("coming")
      setBlurLoading(true);
      const selectedData = initialRows.filter((_, index) =>
        selectedRows.includes(index)
      );
      console.log("selectedData", selectedData);
      const selectedBankKeyRows = selectedData.map((x) => ({
        ...payloadmapping[x?.id],
      }));
      console.log("selectedBankKeyRows", selectedBankKeyRows);
      const duplicateCheckPayload = [];
      selectedBankKeyRows.map((x) => {
        var idk = {
          bankkey: x?.["Add"],
          country: x?.bankCountry,
        };
        duplicateCheckPayload.push(idk);
      });
      console.log("duplicateCheckPayload", duplicateCheckPayload);
      
      console.log("duplicateCheckPayload", duplicateCheckPayload);
      let payload = payloadmapping;
      payload = selectedBankKeyRows;
      const hSuccess = (data) => {
        // setIsLoading(false);
        if (data.statusCode === 400) {
          setBankKeyValidationErrors(data.body);
          setDialogOpen(true);
          setBlurLoading(false);
        } else {
          setMessageDialogTitle("Create");
          console.log("success");
          setMessageDialogTitle("Create");
          setMessageDialogMessage(
            `All Data has been Validated. Bank Key can be Send for Review`
          );
          setMessageDialogSeverity("success");
          setMessageDialogOK(false);
          setsuccessMsg(true);
          handleSnackBarOpen();
          setMessageDialogExtra(true);
          // setIsLoading(false);
          setValidateFlag(true);
          setBlurLoading(false);
          setSubmitForReviewDisabled(false); //chiranjit for now
          setTestrunStatus(false)
          // Now, make the duplicate check API call
          // Ensure that the conditions for making the duplicate check API call are met
          /*if (
            duplicateCheckPayload.bankCountry !== "" ||
            duplicateCheckPayload.bankKey !== ""
          ) {
            // payloadmapping.Toitem = duplicateCheckPayload.name;
            doAjax(
              `/${destination_BankKey}/alter/fetchBankKeyCountryDupliChkMass`,
              "post",
              hDuplicateCheckSuccess,
              hDuplicateCheckError,
              duplicateCheckPayload
            );
          }*/
        }
        // setBlurLoading(false);
      };
  
      const hDuplicateCheckSuccess = (data) => {
        console.log("dataaaa", data);
        // Handle success of duplicate check
        if (
          data.body.length === 0 ||
          !data.body.some((item) =>
            duplicateCheckPayload.some(
              (payloadItem) => payloadItem.name.toUpperCase() === item.matches[0]
            )
          )
        ) {
          // No direct match, enable the "Submit for Review" button
          setBlurLoading(false);
          setSubmitForReviewDisabled(false);
          setTestRun(true);
        } else {
          // Handle direct match
          const directMatches = data.body.map(item => item.matches[0]);
          setBlurLoading(false);
          setMessageDialogTitle("Duplicate Check");
          setsuccessMsg(false);
          setMessageDialogMessage(
            `There is a direct match for the Bank Key name.`
          );
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          setSubmitForReviewDisabled(true);
        }
      };
  
      const hDuplicateCheckError = (error) => {
        // Handle error of duplicate check
        console.log(error);
      };
  
      const hError = (error) => {
        console.log(error);
      };
  
      // Call the main API for validation
      doAjax(
        `/${destination_BankKey}/massAction/validateMassBankKey`,
        "post",
        hSuccess,
        hError,
        payload
      );
    };
  
    // const onValidateCostCenter = () => {
    // const onValidateCostCenter = () => {
    //   const selectedData = initialRows.filter((_, index) =>
    //     selectedRows.includes(index)
    //   );
    //   console.log("selectedData", selectedData);
    //   const selectedProfitCenterRows = selectedData.map((x) => ({
    //     ...payloadmapping[x?.id],
    //   }));
    //   let payload = payloadmapping;
    //   payload = selectedProfitCenterRows;
    //   // setTestRun(true);
    //   console.log("selectedProfitCenterRows", selectedProfitCenterRows);
    //   const hSuccess = (data) => {
    //     setIsLoading();
    //     if (data.statusCode === 400) {
    //       setProfitValidationErrors(data.body);
    //       setDialogOpen(true);
  
    //     } else {
    //       setTestRun(false);
    //       setMessageDialogTitle("Create");
    //       console.log("success");
    //       setMessageDialogTitle("Create");
    //       setMessageDialogMessage(
    //         `All Data has been Validated. Profit Center can be Send for Review`
    //       );
    //       setMessageDialogSeverity("success");
    //       setMessageDialogOK(false);
    //       setsuccessMsg(true);
    //       handleSnackBarOpen();
    //       setMessageDialogExtra(true);
    //       setIsLoading(false);
    //       setValidateFlag(true);
  
    //     }
    //   };
    //   const hError = (error) => {
    //     console.log(error);
    //   };
    //   doAjax(
    //     `/${destination_ProfitCenter}/massAction/validateMassProfitCenter`,
    //     "post",
    //     hSuccess,
    //     hError,
    //     payload
    //   );
    // };
  
    const handleSubmitForReviewChange = () => {
      // setIsLoading(true);
      const selectedData = initialRows.filter((_, index) =>
        selectedRows.includes(index)
      );
      console.log("selectedData", selectedData);
      const selectedbankKeyRows = selectedData.map((x) => ({
        // console.log("Data", x)
        ...payloadmapping[x?.id],
      }));
      let payload = payloadmapping;
      payload = selectedbankKeyRows;
      console.log("selectedbankKeyRows", selectedbankKeyRows);
      const hSuccess = (data) => {
        // setIsLoading(false);
        if (data.statusCode === 200) {
          console.log("success");
          setMessageDialogTitle("Create");
          setMessageDialogMessage(
            `Mass Bank Key Sent for Review with ID CBM${data.body}`
          );
          setMessageDialogSeverity("success");
          setMessageDialogOK(false);
          setsuccessMsg(true);
          handleSnackBarOpen();
          setMessageDialogExtra(true);
          setBlurLoading(false);
          // setIsLoading(false);
          const secondApiPayload = {
            artifactId: bkNumber,
            createdBy: userData?.emailId,
            artifactType: "BankKey",
            requestId: `CBM${data?.body}`,
          };
          const secondApiSuccess = (secondApiData) => {
            console.log("Second API success", secondApiData);
            // Handle success for the second API if needed
          };
  
          const secondApiError = (secondApiError) => {
            console.error("Second API error", secondApiError);
            // Handle error for the second API if needed
          };
          // {requestId&&
          doAjax(
            `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
            "post",
            secondApiSuccess,
            secondApiError,
            secondApiPayload
          );
        } else {
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          setMessageDialogMessage(
            "Failed Submitting the Bank Key for Review "
          );
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          setBlurLoading(false);
          // setIsLoading(false);
        }
        handleClose();
        setBlurLoading(false);
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        //`/${destination_BankKey}/massAction/changeProfitCentersSubmitForReview`,
        `/${destination_BankKey}/massAction/changeBankKeysSubmitForReview`,  
        "post",
        hSuccess,
        hError,
        payload
      );
    };
  
    const handleDialogClose = () => {
      setDialogOpen(false);
    };
    const onBankKeySubmitRemarks = () => {
      console.log(remarks.length,"remarks.length")
      if (remarks.length <= 0){
        setRemarksValidationError(true)
      }else{
        setRemarksValidationError(false)
        setBlurLoading(true);
        handleRemarksDialogClose();
        if (massHandleType === "Create") {
          handleSubmitForReview();
        } else {
          handleSubmitForReviewChange();
        }
      }
      
      
    };
    const handleRemarks = (e, value) => {
      //setRemarks(e.target.value);
      console.log(e.target.value.toUpperCase(),"uppercase letter")
      const newValue = e.target.value.toUpperCase();
      if (newValue.length > 0 && newValue[0] === " ") {
        setRemarks(newValue.trimStart());
        //alert("1")
      } else {
        //let costCenterValue = e.target.value;
        //alert("2")
        let remarksUpperCase = newValue.toUpperCase();
        console.log(remarksUpperCase,"remarksUpperCase")
        setRemarks(remarksUpperCase);
      }
    };
    // const handleDialogProceed = () => {};
  
    const validationColumns = [
      {
        field: "id",
        headerName: "ID",
        hide: true,
        editable: false,
        flex: 1,
        // width: 100,
      },
      {
        field: "bankKey",
        headerName: "Bank Key",
        editable: false,
        flex: 1,
        // width: 100,
      },
      {
        field: "error",
        headerName: "Error",
        editable: false,
        flex: 1,
        // width: 400,
      },
    ];
    //console.log("profitValidationError", profitValidationError);
    const validationRows = bankKeyValidationError
    ?.filter((row) => row?.code === 400)
    ?.map((item, index) => {
      if (item.code === 400) {
        return {
          id: index,
          bankKey: item?.bankKey,
          error: item?.status?.message,
        };
      }
    });
    console.log("validationRows",validationRows)
    const handleRemarksDialogClose = () => {
      setRemarksValidationError(false);
      setTestRun(true);
      setOpenCorrectionDialog(false);
    };
  
    const handleOpenCorrectionDialog = () => {
      setTestRun(false);
      setOpenCorrectionDialog(true);
    };
    console.log(isLoading,"isloading")
    return (
      <>
        {isLoading === true ? (
          <LoadingComponent />
        ) : (
          <div>
            <Dialog
              hideBackdrop={false}
              elevation={2}
              PaperProps={{
                sx: { boxShadow: "none" },
              }}
              open={openCorrectionDialog}
              onClose={handleRemarksDialogClose}
            >
              {/* <Grid
            container
            sx={{ display: "flex", justifyContent: "space-between" }}
          > */}
              {/* <Grid item> */}
              <DialogTitle
                sx={{
                  justifyContent: "space-between",
                  alignItems: "center",
                  height: "max-content",
                  padding: ".5rem",
                  paddingLeft: "1rem",
                  backgroundColor: "#EAE9FF40",
                  // borderBottom: "1px solid grey",
                  display: "flex",
                }}
              >
                <Typography variant="h6">REMARKS</Typography>
  
                <IconButton
                  sx={{ width: "max-content" }}
                  onClick={handleRemarksDialogClose}
                  children={<CloseIcon />}
                />
              </DialogTitle>
              {/* </Grid> */}
              {/* </Grid> */}
              <DialogContent sx={{ padding: ".5rem 1rem" }}>
                <Stack>
                  <Box sx={{ minWidth: 400 }}>
                    <FormControl sx={{ height: "auto" }} fullWidth>
                    <TextField
                      sx={{ backgroundColor: "#F5F5F5" }}
                      onChange={handleRemarks}
                      value={remarks}
                      multiline
                      placeholder={"ENTER REMARKS"}
                      inputProps={{maxLength: 254}}
                    ></TextField>
                    </FormControl>
                    {remarksValidationError && (
                      <Grid>
                        <Typography style={{ color: "red" }}>
                            Please Enter Remarks
                        </Typography>
                      </Grid>
                    )}
                  </Box>
                </Stack>
                {/* <TextField
              autoFocus
              margin="dense"
              id="name"
              label="Enter Remarks for Correction"
              type="text"
              fullWidth
              variant="standard"
              onChange={handleRemarks}
            /> */}
              </DialogContent>
              <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
                <Button
                  sx={{ width: "max-content", textTransform: "capitalize" }}
                  onClick={handleRemarksDialogClose}
                >
                  Cancel
                </Button>
                <Button
                  className="button_primary--normal"
                  type="save"
                  onClick={onBankKeySubmitRemarks}
                  variant="contained"
                >
                  Submit
                </Button>
              </DialogActions>
            </Dialog>
  
            <ReusableDialog
              dialogState={openMessageDialog}
              openReusableDialog={handleMessageDialogClickOpen}
              closeReusableDialog={handleMessageDialogClose}
              dialogTitle={messageDialogTitle}
              dialogMessage={messageDialogMessage}
              handleDialogConfirm={handleMessageDialogClose}
              dialogOkText={"OK"}
              handleExtraButton={handleMessageDialogNavigate}
              dialogSeverity={messageDialogSeverity}
            />
  
            {successMsg && (
              <ReusableSnackBar
                openSnackBar={openSnackbar}
                alertMsg={messageDialogMessage}
                handleSnackBarClose={handleSnackBarClose}
              />
            )}
  
            <div style={{ ...outermostContainer, backgroundColor: "#FAFCFF" }}>
              <Grid container sx={outermostContainer_Information}>
                <Grid item md={12} sx={{ display: "flex", marginBottom: "0" }}>
                  <Grid item md={11} sx={{ display: "flex" }}>
                    <Grid>
                      <IconButton
                        // onClick={handleBacktoRO}
                        color="primary"
                        aria-label="upload picture"
                        component="label"
                        sx={iconButton_SpacingSmall}
                      >
                        <ArrowCircleLeftOutlinedIcon
                          style={{
                            height: "1em",
                            width: "1em",
                            color: "#000000",
                          }}
                          // sx={{
                          //   fontSize: "1.5em",
                          //   color: "#000000",
                          // }}
                          onClick={() => {
                            navigate("/masterDataCockpit/bankKey");
                          }}
                        />
                      </IconButton>
                    </Grid>
                    <Grid>
                      {massHandleType === "Create" ? (
                        <Grid item md={12}>
                          <Typography variant="h3">
                            <strong>Create Multiple Bank Keys</strong>
                          </Typography>
                          <Typography variant="body2" color="#777">
                            This view creates multiple Bank Keys
                          </Typography>
                        </Grid>
                      ) : (
                        <Grid item md={12}>
                          <Typography variant="h3">
                            <strong>Change Multiple Bank Keys</strong>
                          </Typography>
                          <Typography variant="body2" color="#777">
                            This view changes multiple bank Keys
                          </Typography>
                        </Grid>
                      )}
                    </Grid>
                  </Grid>
  
                  <Grid item md={1} sx={{ display: "flex" }}>
                    <Tooltip title="Upload documents if any" arrow>
                      <IconButton onClick={handleOpenDialog}>
                        <AttachFileOutlinedIcon />
                      </IconButton>
                    </Tooltip>
                  </Grid>
                  <Dialog
                    hideBackdrop={false}
                    elevation={2}
                    PaperProps={{
                      sx: { boxShadow: "none" },
                    }}
                    open={openDialog}
                    onClose={handleCloseDialog}
                  >
                    <DialogTitle
                      sx={{
                        justifyContent: "space-between",
                        alignItems: "center",
                        height: "max-content",
                        padding: ".5rem",
                        paddingLeft: "1rem",
                        backgroundColor: "#EAE9FF40",
                        // borderBottom: "1px solid grey",
                        display: "flex",
                      }}
                    >
                      <Typography variant="h6">Add Attachment</Typography>
                    </DialogTitle>
                    <DialogContent sx={{ padding: ".5rem 1rem" }}>
                      <Stack>
                        <Box sx={{ minWidth: 400 }}>
                          <ReusableAttachementAndComments
                            title="BankKey"
                            useMetaData={false}
                            artifactId={bkNumber}
                            artifactName="BankKey"
                          />
                        </Box>
                      </Stack>
                    </DialogContent>
                    <DialogActions>
                      <Button onClick={handleCloseDialog}>Close</Button>
                    </DialogActions>
                  </Dialog>
                </Grid>
              </Grid>
              <Grid item sx={{ position: "relative" }}>
                <Stack>
                  <ReusableTable
                    isLoading={isLoading}
                    width="100%"
                    title={
                      "Bank Key Master List (" + initialRows.length + ")"
                    }
                    rows={initialRows}
                    columns={columns}
                    pageSize={10}
                    getRowIdValue={"id"}
                    hideFooter={false}
                    checkboxSelection={true}
                    disableSelectionOnClick={true}
                    status_onRowSingleClick={true}
                    onRowsSelectionHandler={handleSelectionModelChange}
                    callback_onRowSingleClick={(params) => {
                      console.log("paramss", params);
                      const bankKey = params.row.bankKey;
                      const dataToSend = multipleBankKeyData.find(
                        (item) => item.BankKey === bankKey
                      );
                      navigate(
                        `/masterDataCockpit/bankKey/createMultipleBankKey/editMultipleBankKey/${bankKey}`,
                        {
                          state: { tabsData: dataToSend, rowData: params.row },
                        }
                      );
                    }}
                    stopPropagation_Column={"action"}
                    status_onRowDoubleClick={true}
                  />
                </Stack>
              </Grid>
            </div>
            <Paper
              sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
              elevation={2}
            >
              <BottomNavigation
                className="container_BottomNav"
                showLabels
                sx={{
                  display: "flex",
  
                  justifyContent: "flex-end",
                }}
                value={value}
                // onChange={(newValue) => {
                // setValue(newValue);
                // }}
              >
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary, mr: 1 }}
                  onClick={onValidateBankKey}
                  disabled={!testRun}
                >
                  Validate
                </Button>
  
                {massHandleType === "Create" ? (
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary }}
                    // disabled={testRun}
                    onClick={handleOpenCorrectionDialog}
                    disabled={submitForReviewDisabled}
                  >
                    Submit for Review
                  </Button>
                ) : massHandleType === "Change" ? (
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary }}
                    onClick={handleOpenCorrectionDialog}
                    disabled={submitForReviewDisabled}
                  >
                    Submit for Review
                  </Button>
                ) : (
                  ""
                )}
              </BottomNavigation>
            </Paper>
            <Dialog
              open={dialogOpen}
              fullWidth
              onClose={handleDialogClose}
              sx={{
                "&::webkit-scrollbar": {
                  width: "1px",
                },
                // paddingBottom:1
              }}
            >
              <DialogTitle
                sx={{
                  justifyContent: "space-between",
                  alignItems: "center",
                  height: "max-content",
                  padding: ".5rem",
                  paddingLeft: "1rem",
                  backgroundColor: "#EAE9FF40",
                  // borderBottom: "1px solid grey",
                  display: "flex",
                }}
              >
                <Typography variant="h6" color="red">
                  Errors
                </Typography>
  
                <IconButton
                  sx={{ width: "max-content" }}
                  onClick={handleDialogClose}
                  children={<CloseIcon />}
                />
              </DialogTitle>
              <DialogContent sx={{ padding: ".5rem 1rem" }}>
                {/* <Grid container> */}
  
                {validationRows && (
                <ReusableTable
                  isLoading={isLoading}
                  width="100%"
                  // title={"Profit Center Master List (" + initialRows.length + ")"}
                  rows={validationRows}
                  columns={validationColumns}
                  pageSize={10}
                  getRowIdValue={"id"}
                  hideFooter={true}
                  checkboxSelection={false}
                  disableSelectionOnClick={true}
                  status_onRowSingleClick={true}
                  stopPropagation_Column={"action"}
                  status_onRowDoubleClick={true}
                />
              )}
  
                {/* </Grid> */}
              </DialogContent>
  
              <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
                {/* <Button
              sx={{ width: "max-content", textTransform: "capitalize" }}
              onClick={handleDialogClose}
            >
              Cancel
            </Button>
            <Button
              className="button_primary--normal"
              type="save"
              onClick={handleDialogProceed}
              variant="contained"
            >
              Proceed
            </Button> */}
              </DialogActions>
            </Dialog>
          </div>
        )}
        <Backdrop
          sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
          open={blurLoading}
          // onClick={handleClose}
        >
          <CircularProgress color="inherit" />
        </Backdrop>
      </>
    );
  };
  
  export default CreateMultipleBankKey;
  
