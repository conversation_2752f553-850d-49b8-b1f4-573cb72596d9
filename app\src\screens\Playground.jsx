import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  <PERSON>po<PERSON>,
  Divider,
  Avatar,
  Chip,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  TextField,
  InputAdornment,
  Tabs,
  Tab,
  Button,
  Switch,
  FormControlLabel,
  useTheme
} from '@mui/material';
import {
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Security as SecurityIcon,
  SupervisedUserCircle as SupervisedUserCircleIcon,
  Refresh as RefreshIcon,
  Close as CloseIcon
} from '@mui/icons-material';

// Sample users data
const usersData = [
  {
    id: "USR-1001",
    name: "<PERSON>",
    email: "<EMAIL>",
    department: "Engineering",
    status: "Active",
    lastLogin: "2025-03-10T14:32:45",
    avatar: "https://randomuser.me/api/portraits/men/32.jpg",
    roles: [
      { id: 1, name: "Developer", description: "Access to development tools and environments" },
      { id: 2, name: "Project Manager", description: "Can manage project settings and team assignments" }
    ],
    features: [
      { id: 1, name: "Code Repository", access: true, category: "Development" },
      { id: 2, name: "CI/CD Pipeline", access: true, category: "Development" },
      { id: 3, name: "User Administration", access: false, category: "Administration" },
      { id: 4, name: "Billing", access: false, category: "Finance" },
      { id: 5, name: "Reports", access: true, category: "Analytics" },
      { id: 6, name: "API Gateway", access: true, category: "Development" }
    ]
  },
  {
    id: "USR-1002",
    name: "Sarah Williams",
    email: "<EMAIL>",
    department: "Marketing",
    status: "Active",
    lastLogin: "2025-03-11T09:15:22",
    avatar: "https://randomuser.me/api/portraits/women/44.jpg",
    roles: [
      { id: 3, name: "Marketing Manager", description: "Manage marketing campaigns and content" }
    ],
    features: [
      { id: 1, name: "Code Repository", access: false, category: "Development" },
      { id: 2, name: "CI/CD Pipeline", access: false, category: "Development" },
      { id: 3, name: "User Administration", access: false, category: "Administration" },
      { id: 4, name: "Billing", access: true, category: "Finance" },
      { id: 5, name: "Reports", access: true, category: "Analytics" },
      { id: 6, name: "API Gateway", access: false, category: "Development" }
    ]
  },
  {
    id: "USR-1003",
    name: "Michael Chen",
    email: "<EMAIL>",
    department: "IT Administration",
    status: "Inactive",
    lastLogin: "2025-02-28T16:45:30",
    avatar: "https://randomuser.me/api/portraits/men/67.jpg",
    roles: [
      { id: 4, name: "System Administrator", description: "Full system access and configuration" },
      { id: 5, name: "Security Officer", description: "Manage security policies and audits" }
    ],
    features: [
      { id: 1, name: "Code Repository", access: true, category: "Development" },
      { id: 2, name: "CI/CD Pipeline", access: true, category: "Development" },
      { id: 3, name: "User Administration", access: true, category: "Administration" },
      { id: 4, name: "Billing", access: true, category: "Finance" },
      { id: 5, name: "Reports", access: true, category: "Analytics" },
      { id: 6, name: "API Gateway", access: true, category: "Development" }
    ]
  },
  {
    id: "USR-1004",
    name: "Jessica Lee",
    email: "<EMAIL>",
    department: "Finance",
    status: "Active",
    lastLogin: "2025-03-09T11:22:33",
    avatar: "https://randomuser.me/api/portraits/women/22.jpg",
    roles: [
      { id: 6, name: "Finance Manager", description: "Access to financial data and reporting" }
    ],
    features: [
      { id: 1, name: "Code Repository", access: false, category: "Development" },
      { id: 2, name: "CI/CD Pipeline", access: false, category: "Development" },
      { id: 3, name: "User Administration", access: false, category: "Administration" },
      { id: 4, name: "Billing", access: true, category: "Finance" },
      { id: 5, name: "Reports", access: true, category: "Analytics" },
      { id: 6, name: "API Gateway", access: false, category: "Development" }
    ]
  }
];

// Function to format date
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};

// Function to get initials from name
const getInitials = (name) => {
  return name
    .split(' ')
    .map(word => word[0])
    .join('')
    .toUpperCase()
    .substring(0, 2);
};

// Function to generate consistent colors based on name
const stringToColor = (string) => {
  let hash = 0;
  for (let i = 0; i < string.length; i++) {
    hash = string.charCodeAt(i) + ((hash << 5) - hash);
  }
  
  let color = '#';
  for (let i = 0; i < 3; i++) {
    const value = (hash >> (i * 8)) & 0xFF;
    color += (`00${value.toString(16)}`).slice(-2);
  }
  
  return color;
};

const UserManagementScreen = () => {
  const theme = useTheme();
  const [tabValue, setTabValue] = useState(0);
  const [featureFilter, setFeatureFilter] = useState('');
  const [userFilter, setUserFilter] = useState('');
  const [selectedUser, setSelectedUser] = useState(null);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleFeatureFilterChange = (event) => {
    setFeatureFilter(event.target.value);
  };

  const handleUserFilterChange = (event) => {
    setUserFilter(event.target.value);
  };

  const handleUserSelect = (user) => {
    setSelectedUser(user);
  };

  const handleCloseDetail = () => {
    setSelectedUser(null);
  };

  // Filter users based on search
  const filteredUsers = usersData.filter(user =>
    user.name.toLowerCase().includes(userFilter.toLowerCase()) ||
    user.email.toLowerCase().includes(userFilter.toLowerCase()) ||
    user.department.toLowerCase().includes(userFilter.toLowerCase())
  );

  // Filter features based on search if a user is selected
  const filteredFeatures = selectedUser ? selectedUser.features.filter(feature =>
    feature.name.toLowerCase().includes(featureFilter.toLowerCase()) ||
    feature.category.toLowerCase().includes(featureFilter.toLowerCase())
  ) : [];

  return (
    <Box sx={{ width: '100%', height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ p: 3, borderBottom: `1px solid ${theme.palette.divider}` }}>
        <Typography variant="h4" component="h1" fontWeight="500">
          User Management
        </Typography>
      </Box>
      
      {/* Main content area */}
      <Box sx={{ display: 'flex', flex: 1, overflow: 'hidden' }}>
        {/* Users List (Always visible, full width when no user selected, half width when user selected) */}
        <Box 
          sx={{ 
            width: selectedUser ? '50%' : '100%',
            transition: 'width 0.3s ease',
            p: 3, 
            borderRight: selectedUser ? `1px solid ${theme.palette.divider}` : 'none',
            overflow: 'auto',
            height: '100%'
          }}
        >
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <TextField
              size="small"
              placeholder="Search users"
              value={userFilter}
              onChange={handleUserFilterChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{ width: 300 }}
            />
            <Button 
              variant="contained" 
              startIcon={<AddIcon />}
              color="primary"
              sx={{ borderRadius: 2 }}
            >
              Add New User
            </Button>
          </Box>

          <TableContainer component={Paper} sx={{ borderRadius: 2, boxShadow: theme.shadows[2] }}>
            <Table sx={{ minWidth: 650 }} aria-label="users table">
              <TableHead sx={{ backgroundColor: theme.palette.action.hover }}>
                <TableRow>
                  <TableCell><Typography fontWeight="bold">User</Typography></TableCell>
                  <TableCell><Typography fontWeight="bold">Department</Typography></TableCell>
                  <TableCell><Typography fontWeight="bold">Status</Typography></TableCell>
                  <TableCell><Typography fontWeight="bold">Last Login</Typography></TableCell>
                  <TableCell align="right"><Typography fontWeight="bold">Actions</Typography></TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredUsers.map((user) => (
                  <TableRow 
                    key={user.id}
                    hover
                    onClick={() => handleUserSelect(user)}
                    sx={{ 
                      cursor: 'pointer',
                      backgroundColor: selectedUser && selectedUser.id === user.id ? 
                        theme.palette.action.selected : 'inherit'
                    }}
                  >
                    <TableCell component="th" scope="row">
                      <Box display="flex" alignItems="center">
                        <Avatar sx={{ 
                          mr: 2, 
                          width: 40, 
                          height: 40,
                          bgcolor: stringToColor(user.name)
                        }}>
                          {getInitials(user.name)}
                        </Avatar>
                        <Box>
                          <Typography fontWeight="medium">{user.name}</Typography>
                          <Typography variant="body2" color="text.secondary">{user.email}</Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>{user.department}</TableCell>
                    <TableCell>
                      <Chip
                        label={user.status}
                        size="small"
                        color={user.status === "Active" ? "success" : "default"}
                      />
                    </TableCell>
                    <TableCell>{formatDate(user.lastLogin)}</TableCell>
                    <TableCell align="right">
                      <IconButton color="primary" size="small" onClick={(e) => {
                        e.stopPropagation();
                        handleUserSelect(user);
                      }}>
                        <EditIcon />
                      </IconButton>
                      <IconButton color="error" size="small" onClick={(e) => {
                        e.stopPropagation();
                        // Delete logic would go here
                      }}>
                        <DeleteIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>

        {/* User Detail Panel (Visible only when a user is selected) */}
        {selectedUser && (
          <Box 
            sx={{ 
              width: '50%', 
              p: 3, 
              overflow: 'auto',
              height: '100%',
              position: 'relative'
            }}
          >
            <IconButton 
              sx={{ 
                position: 'absolute', 
                top: theme.spacing(2), 
                right: theme.spacing(2),
                zIndex: 1
              }}
              onClick={handleCloseDetail}
            >
              <CloseIcon />
            </IconButton>

            {/* User info card */}
            <Card sx={{ mb: 4, borderRadius: 2, overflow: 'visible', boxShadow: theme.shadows[2] }}>
              <Box sx={{ p: 3, display: 'flex', alignItems: 'flex-start' }}>
                <Avatar
                  sx={{ 
                    width: 80, 
                    height: 80, 
                    mr: 3, 
                    border: `2px solid ${theme.palette.primary.main}`,
                    bgcolor: stringToColor(selectedUser.name),
                    fontSize: '2rem'
                  }}
                >
                  {getInitials(selectedUser.name)}
                </Avatar>
                <Box sx={{ flexGrow: 1 }}>
                  <Box display="flex" justifyContent="space-between" alignItems="center">
                    <Box>
                      <Typography variant="h5" component="h2" fontWeight="500">
                        {selectedUser.name}
                      </Typography>
                      <Typography variant="body1" color="text.secondary" gutterBottom>
                        {selectedUser.email}
                      </Typography>
                    </Box>
                    <Box>
                      <Chip
                        label={selectedUser.status}
                        color={selectedUser.status === "Active" ? "success" : "default"}
                        sx={{ fontWeight: "medium" }}
                      />
                      <IconButton color="primary" sx={{ ml: 1 }}>
                        <EditIcon />
                      </IconButton>
                    </Box>
                  </Box>

                  <Grid container spacing={3} sx={{ mt: 1 }}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" color="text.secondary">
                        Department
                      </Typography>
                      <Typography variant="body1">
                        {selectedUser.department}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" color="text.secondary">
                        Last Login
                      </Typography>
                      <Typography variant="body1">
                        {formatDate(selectedUser.lastLogin)}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" color="text.secondary">
                        User ID
                      </Typography>
                      <Typography variant="body1">
                        {selectedUser.id}
                      </Typography>
                    </Grid>
                  </Grid>
                </Box>
              </Box>
            </Card>

            {/* Tabs for Roles and Features */}
            <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
              <Tabs 
                value={tabValue} 
                onChange={handleTabChange} 
                aria-label="user management tabs"
                textColor="primary"
                indicatorColor="primary"
              >
                <Tab 
                  icon={<SupervisedUserCircleIcon />} 
                  iconPosition="start" 
                  label="Roles" 
                  id="tab-0" 
                />
                <Tab 
                  icon={<SecurityIcon />} 
                  iconPosition="start" 
                  label="Feature Access" 
                  id="tab-1" 
                />
              </Tabs>
            </Box>

            {/* Roles tab content */}
            <div role="tabpanel" hidden={tabValue !== 0}>
              {tabValue === 0 && (
                <Box>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                    <Typography variant="h6" component="h3">
                      Assigned Roles
                    </Typography>
                    <Button 
                      variant="contained" 
                      startIcon={<AddIcon />} 
                      color="primary"
                      sx={{ borderRadius: 2 }}
                    >
                      Assign Role
                    </Button>
                  </Box>
                  
                  <TableContainer component={Paper} sx={{ borderRadius: 2, boxShadow: theme.shadows[2] }}>
                    <Table aria-label="roles table">
                      <TableHead sx={{ backgroundColor: theme.palette.action.hover }}>
                        <TableRow>
                          <TableCell><Typography fontWeight="bold">Role Name</Typography></TableCell>
                          <TableCell><Typography fontWeight="bold">Description</Typography></TableCell>
                          <TableCell align="right"><Typography fontWeight="bold">Actions</Typography></TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {selectedUser.roles.map((role) => (
                          <TableRow key={role.id}>
                            <TableCell component="th" scope="row">
                              <Typography fontWeight="medium">{role.name}</Typography>
                            </TableCell>
                            <TableCell>{role.description}</TableCell>
                            <TableCell align="right">
                              <IconButton color="error" size="small">
                                <DeleteIcon />
                              </IconButton>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Box>
              )}
            </div>

            {/* Features tab content */}
            <div role="tabpanel" hidden={tabValue !== 1}>
              {tabValue === 1 && (
                <Box>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                    <Typography variant="h6" component="h3">
                      Feature Permissions
                    </Typography>
                    <TextField
                      size="small"
                      placeholder="Search features"
                      value={featureFilter}
                      onChange={handleFeatureFilterChange}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <SearchIcon />
                          </InputAdornment>
                        ),
                      }}
                      sx={{ width: 250, borderRadius: 2 }}
                    />
                  </Box>
                  
                  <TableContainer component={Paper} sx={{ borderRadius: 2, boxShadow: theme.shadows[2] }}>
                    <Table aria-label="features table">
                      <TableHead sx={{ backgroundColor: theme.palette.action.hover }}>
                        <TableRow>
                          <TableCell><Typography fontWeight="bold">Feature Name</Typography></TableCell>
                          <TableCell><Typography fontWeight="bold">Category</Typography></TableCell>
                          <TableCell align="center"><Typography fontWeight="bold">Access Status</Typography></TableCell>
                          <TableCell align="right"><Typography fontWeight="bold">Toggle Access</Typography></TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {filteredFeatures.map((feature) => (
                          <TableRow key={feature.id}>
                            <TableCell component="th" scope="row">
                              <Typography fontWeight="medium">{feature.name}</Typography>
                            </TableCell>
                            <TableCell>
                              <Chip 
                                label={feature.category} 
                                size="small" 
                                variant="outlined" 
                                color="primary"
                              />
                            </TableCell>
                            <TableCell align="center">
                              {feature.access ? (
                                <Box display="flex" alignItems="center" justifyContent="center">
                                  <CheckCircleIcon color="success" fontSize="small" sx={{ mr: 0.5 }} />
                                  <Typography variant="body2" color="success.main">Granted</Typography>
                                </Box>
                              ) : (
                                <Box display="flex" alignItems="center" justifyContent="center">
                                  <CancelIcon color="error" fontSize="small" sx={{ mr: 0.5 }} />
                                  <Typography variant="body2" color="error.main">Denied</Typography>
                                </Box>
                              )}
                            </TableCell>
                            <TableCell align="right">
                              <FormControlLabel
                                control={<Switch checked={feature.access} color="primary" />}
                                label=""
                              />
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Box>
              )}
            </div>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default UserManagementScreen;