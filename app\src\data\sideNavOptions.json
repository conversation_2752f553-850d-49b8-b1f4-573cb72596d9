{"configuration": {"moreOptions": 6}, "data": [{"id": 1, "optionName": "Dashboard", "displayName": "Dashboard", "icon": "Dashboard", "navigateTo": "/", "childOptions": []}, {"id": 2, "optionName": "Workspace", "displayName": "Workspace", "icon": "GroupWork", "navigateTo": "/workspace/", "childOptions": [{"id": 1, "optionName": "My Tasks", "displayName": "My Tasks", "icon": "PendingActions", "navigateTo": "/workspace/MyTasks", "childOptions": []}, {"id": 2, "optionName": "Completed Tasks", "displayName": "Completed Tasks ", "icon": "Task", "navigateTo": "/workspace/CompletedTasks", "childOptions": []}]}, {"id": 3, "optionName": "Master Data", "displayName": "Master Data", "icon": "Dataset", "navigateTo": "/masterDataCockpit/", "childOptions": [{"id": 1, "optionName": "Material", "displayName": "Material", "icon": "ShoppingCart", "navigateTo": "/masterDataCockpit/materialMaster/material", "childOptions": []}, {"id": 2, "optionName": "Bill Of Material", "displayName": "BOM", "icon": "LocalAtm", "navigateTo": "/masterDataCockpit/billOfMaterial", "childOptions": []}, {"id": 3, "optionName": "Cost Center", "displayName": "Cost Center ", "icon": "TrendingDown", "navigateTo": "/masterDataCockpit/costCenter", "childOptions": []}, {"id": 4, "optionName": "Profit Center", "displayName": "Profit Center", "icon": "TrendingUp", "navigateTo": "/masterDataCockpit/profitCenter", "childOptions": []}, {"id": 5, "optionName": "Bank Key", "displayName": "Bank Key", "icon": "AccountBalance", "navigateTo": "/masterDataCockpit/bankKey", "childOptions": []}, {"id": 6, "optionName": "General <PERSON><PERSON>", "displayName": "General <PERSON><PERSON>", "icon": "Assignment", "navigateTo": "/masterDataCockpit/generalLedger", "childOptions": []}]}, {"id": 4, "optionName": "Request Bench", "displayName": "Request Bench", "icon": "Task", "navigateTo": "/RequestBench", "childOptions": []}, {"id": 5, "optionName": "Request History", "displayName": "Request History", "icon": "History", "navigateTo": "/RequestHistory", "childOptions": []}, {"id": 6, "optionName": "Config <PERSON>", "displayName": "Config <PERSON>", "icon": "Settings", "navigateTo": "/configCockpit", "childOptions": [{"id": 1, "optionName": "Field Configurations", "displayName": "Field Configurations", "icon": "Settings", "navigateTo": "/configCockpit", "childOptions": [{"id": 1, "optionName": "Material", "displayName": "Material", "icon": "ShoppingCart", "navigateTo": "/configCockpit/fieldSelection", "childOptions": []}, {"id": 2, "optionName": "Cost Center", "displayName": "Cost Center", "icon": "TrendingDown", "navigateTo": "/configCockpit/fieldConfiguration/costCenter", "childOptions": []}, {"id": 2, "optionName": "Profit Center", "displayName": "Profit Center", "icon": "TrendingUp", "navigateTo": "/configCockpit/fieldConfiguration/profitCenter", "childOptions": []}, {"id": 2, "optionName": "Bank Key", "displayName": "Bank Key", "icon": "AccountBalance", "navigateTo": "/configCockpit/fieldConfiguration/bankKey", "childOptions": []}]}, {"id": 2, "optionName": "SLA Configurations", "displayName": "SLA Configurations", "icon": "AccessAlarm", "navigateTo": "/configCockpit/SLAManagement", "childOptions": []}, {"id": 3, "optionName": "Email Template Configurations", "displayName": "Email Template Configurations", "icon": "MailOutline", "navigateTo": "/configCockpit/EmailTemplateManagement", "childOptions": []}, {"id": 4, "optionName": "User Management", "displayName": "User Management", "icon": "Person", "navigateTo": "/configCockpit/userManagement?", "childOptions": []}]}]}