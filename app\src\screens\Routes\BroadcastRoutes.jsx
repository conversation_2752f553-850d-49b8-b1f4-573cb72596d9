import React from "react";
import { Route } from "react-router-dom";
const BroadcastHome = React.lazy(() => import("../../components/BroadcastManagement/BroadcastHome"));
const NewBroadcast = React.lazy(() => import("../../components/BroadcastManagement/NewBroadcast"));
const EditBroadcast = React.lazy(() => import("../../components/BroadcastManagement/EditBroadcast"));
const ViewBroadcast = React.lazy(() => import("../../components/BroadcastManagement/ViewBroadcast"));

export const BroadcastRoutes = [
  <Route path="/configCockpit/broadcastConfigurations" element={<BroadcastHome />} />,
  <Route path="/configCockpit/broadcastConfigurations/newBroadcast" element={<NewBroadcast />} />,
  <Route path="/configCockpit/broadcastConfigurations/editBroadcast" element={<EditBroadcast />} />,
  <Route path="/configCockpit/broadcastConfigurations/viewBroadcast" element={<ViewBroadcast />} />
];
