import{r as n,a as e,j as r,aF as Qe,T as w,B as Ge,ar as g,aw as R,M as l,I as Pe,aG as Ke,b7 as ze,W as Ze,G as o,wU as c,at as we,au as Be,av as ke,X as Xe,gg as Je,t as K,V as et,F as z,K as B,Z as k,$ as H,as as tt,b as rt,a8 as nt,s as lt,pS as st,cB as F,g as at,l as E,A as it,aj as ot,x as dt,an as ct,ao as ut,f as ht,E as pt,ax as Ct,aA as mt,aC as ft,y as he,uM as pe,h as Fe,gs as gt,wV as St,al as xt,aE as Et,aD as At,Y as _t,bF as Tt,wW as It}from"./index-75c1660a.js";import{d as yt}from"./CloudDownload-81bb13f9.js";import{L as P}from"./LargeDropdown-701bd2f7.js";import{S as He}from"./SingleSelectDropdown-0d30aa01.js";const Rt=({open:W,onClose:Ce,handleSubmit:me=()=>{},title:$=c.CREATE_REQUEST,submitButtonText:fe=c.INITIATE_REQUEST,isLoading:v=!1})=>{var Oe;const[U,D]=n.useState("material"),[ge,A]=n.useState([]),[Z,X]=n.useState([]),[q,J]=n.useState([]),[Y,ee]=n.useState([]),[De,S]=n.useState(null),[C,b]=n.useState({createdOn:null,top:{code:"10",desc:""}}),[j,Se]=n.useState([]),[te,M]=n.useState([]),[re,O]=n.useState([]),[xe,y]=n.useState(!1),[_,Ee]=n.useState(null),[V,Ae]=n.useState([]),[_e,ne]=n.useState([]),[Te,Ie]=n.useState([]),[Q,N]=n.useState([]),[ye,le]=n.useState([]),[se,ae]=n.useState([]),[Re,ie]=n.useState([]),[be,oe]=n.useState([]),[de,G]=n.useState(""),ce=[{code:"material",desc:"Material"},{code:"cost_center",desc:"Cost Center"},{code:"profit_center",desc:"Profit Center"},{code:"general_ledger",desc:"General Ledger"},{code:"hierarchy",desc:"Hierarchy"}],t=s=>({material:[{desc:"Rule-1",code:"MAT_MATERIAL_FIELD_CONFIG"},{desc:"Rule-2",code:"MAT_MATERIAL_TABLE_FIELD_CONFIG"},{desc:"Rule-3",code:"MAT_MATERIAL_COLUMN_FIELD_CONFIG"},{desc:"Rule-4",code:"MAT_MATERIAL_SEARCHSCREEN_FIELD_CONFIG"},{desc:"Rule-5",code:"MAT_MATERIAL_PLANT_FIELD_CONFIG"}],cost_center:[{code:"1",desc:"CC_COST_CENTER_CONFIG"},{code:"2",desc:"CC_BUDGET_CONFIG"},{code:"3",desc:"CC_ALLOCATION_CONFIG"}],profit_center:[{code:"1",desc:"PC_PROFIT_CENTER_CONFIG"},{code:"2",desc:"PC_REVENUE_CONFIG"},{code:"3",desc:"PC_ANALYSIS_CONFIG"}],general_ledger:[{code:"1",desc:"GL_ACCOUNT_CONFIG"},{code:"2",desc:"GL_POSTING_CONFIG"},{code:"3",desc:"GL_BALANCE_CONFIG"}],hierarchy:[{code:"1",desc:"HIE_STRUCTURE_CONFIG"},{code:"2",desc:"HIE_LEVEL_CONFIG"},{code:"3",desc:"HIE_RELATIONSHIP_CONFIG"}]})[s]||[],i=()=>{var m,f,T,I;const s=[];return(!Y||Y.length===0)&&s.push((m=c)==null?void 0:m.BR_FAIL),(f=C==null?void 0:C.top)!=null&&f.code||s.push((T=c)==null?void 0:T.NO_FAIL),C!=null&&C.createdOn||s.push((I=c)==null?void 0:I.CO_FAIL),s},u={fontSize:"12px",fontWeight:500},a=({children:s,sx:m,required:f=!1})=>r(w,{sx:{...u,marginBottom:"4px",...m},children:[s,f&&e("span",{style:{color:"red",marginLeft:"2px"},children:"*"})]}),d=()=>{var f,T;y(!0);const s=I=>{O(I.body),y(!1)},m=I=>{y(!1),console.log(I)};B(`/${k}${(T=(f=H)==null?void 0:f.DATA_CLEANSE_APIS)==null?void 0:T.GET_MATERIAL_GRP}`,"get",s,m)},p=(s="")=>{var I,Ne;y(!0);let m={materialNo:s,salesOrg:"",top:200,skip:0};const f=Le=>{y(!1),Se(Le.body)},T=Le=>{console.log(Le)};B(`/${k}${(Ne=(I=H)==null?void 0:I.DATA_CLEANSE_APIS)==null?void 0:Ne.SEARCH_MAT_NO}`,"post",f,T,m)},h=()=>{var f,T;y(!0);const s=I=>{y(!1),M(I.body)},m=I=>{y(!1)};B(`/${k}${(T=(f=H)==null?void 0:f.DATA_CLEANSE_APIS)==null?void 0:T.GET_MAT_TYPE}`,"get",s,m)},x=()=>{Ae([{code:"1",desc:"Cost Center 1"},{code:"2",desc:"Cost Center 2"},{code:"3",desc:"Cost Center 3"}])},L=()=>{ne([{code:"1",desc:"Profit Center 1"},{code:"2",desc:"Profit Center 2"},{code:"3",desc:"Profit Center 3"}])},ue=()=>{Ie([{code:"1",desc:"GL Account 1"},{code:"2",desc:"GL Account 2"},{code:"3",desc:"GL Account 3"}])},We=()=>{N([{code:"1",desc:"Hierarchy Level 1"},{code:"2",desc:"Hierarchy Level 2"},{code:"3",desc:"Hierarchy Level 3"}])},$e=s=>{const m=s.target.value;if(_&&clearTimeout(_),m.length>=4){const f=setTimeout(()=>{p(m)},500);Ee(f)}},Ue=s=>{b({...C,createdOn:s})},qe=(s,m)=>{if(m!==null){const f=m;let T={...C,[s]:f};b(T)}},Ye=s=>{switch(D(s),ve(),s){case"material":d(),h();break;case"cost_center":x();break;case"profit_center":L();break;case"general_ledger":ue();break;case"hierarchy":We();break}},ve=()=>{A([]),X([]),J([]),ee([]),S(null),le([]),ae([]),ie([]),oe([]),b({createdOn:null,top:{code:"10",desc:""}}),G("")},Me=()=>{ve(),Ce(),D("material"),G("")},je=[{code:"10",desc:""},{code:"20",desc:""},{code:"50",desc:""},{code:"100",desc:""},{code:"200",desc:""},{code:"500",desc:""},{code:"1000",desc:""}],Ve=()=>{switch(U){case"material":return r(z,{children:[r(o,{item:!0,xs:12,md:6,children:[e(a,{children:c.MATERIAL}),e(g,{size:"small",fullWidth:!0,children:e(tt,{matGroup:j,selectedMaterialGroup:ge,setSelectedMaterialGroup:A,isDropDownLoading:xe,placeholder:c.SELECT_MATERIAL,onInputChange:$e,minCharacters:4})})]}),r(o,{item:!0,xs:12,md:6,children:[e(a,{children:c.MATERIAL_TYPE}),e(P,{matGroup:te,selectedMaterialGroup:Z,setSelectedMaterialGroup:X,placeholder:c.SELECT_MATERIAL_TYPE})]}),r(o,{item:!0,xs:12,md:6,children:[e(a,{children:c.MATERIAL_GROUP}),e(P,{matGroup:re,selectedMaterialGroup:q,setSelectedMaterialGroup:J,placeholder:c.SELECT_MATERIAL_GROUP})]})]});case"cost_center":return r(z,{children:[r(o,{item:!0,xs:12,md:6,children:[e(a,{children:"Cost Center"}),e(P,{matGroup:V,selectedMaterialGroup:ye,setSelectedMaterialGroup:le,placeholder:c.SELECT_COST_CENTER})]}),r(o,{item:!0,xs:12,md:6,children:[e(a,{children:c.CATEGORY}),e(g,{size:"small",fullWidth:!0,children:r(R,{value:"",onChange:()=>{},displayEmpty:!0,sx:{fontSize:"12px"},MenuProps:{disablePortal:!1,PaperProps:{style:{zIndex:1302,maxHeight:200}}},children:[e(l,{value:"",disabled:!0,children:"Select Category"}),e(l,{value:"production",children:"Production"}),e(l,{value:"administration",children:"Administration"}),e(l,{value:"sales",children:"Sales & Marketing"})]})})]}),r(o,{item:!0,xs:12,md:6,children:[e(a,{children:"Budget Range"}),e(g,{size:"small",fullWidth:!0,children:r(R,{value:"",onChange:()=>{},displayEmpty:!0,sx:{fontSize:"12px"},MenuProps:{disablePortal:!1,PaperProps:{style:{zIndex:1302,maxHeight:200}}},children:[e(l,{value:"",disabled:!0,children:"Select Budget Range"}),e(l,{value:"0-10000",children:"0 - 10,000"}),e(l,{value:"10000-50000",children:"10,000 - 50,000"}),e(l,{value:"50000+",children:"50,000+"})]})})]})]});case"profit_center":return r(z,{children:[r(o,{item:!0,xs:12,md:6,children:[e(a,{children:"Profit Center"}),e(P,{matGroup:_e,selectedMaterialGroup:se,setSelectedMaterialGroup:ae,placeholder:"Select Profit Center"})]}),r(o,{item:!0,xs:12,md:6,children:[e(a,{children:"Revenue Type"}),e(g,{size:"small",fullWidth:!0,children:r(R,{value:"",onChange:()=>{},displayEmpty:!0,sx:{fontSize:"12px"},MenuProps:{disablePortal:!1,PaperProps:{style:{zIndex:1302,maxHeight:200}}},children:[e(l,{value:"",disabled:!0,children:"Select Revenue Type"}),e(l,{value:"primary",children:"Primary Revenue"}),e(l,{value:"secondary",children:"Secondary Revenue"}),e(l,{value:"other",children:"Other Revenue"})]})})]}),r(o,{item:!0,xs:12,md:6,children:[e(a,{children:"Profit Margin Range"}),e(g,{size:"small",fullWidth:!0,children:r(R,{value:"",onChange:()=>{},displayEmpty:!0,sx:{fontSize:"12px"},MenuProps:{disablePortal:!1,PaperProps:{style:{zIndex:1302,maxHeight:200}}},children:[e(l,{value:"",disabled:!0,children:"Select Margin Range"}),e(l,{value:"0-10",children:"0% - 10%"}),e(l,{value:"10-25",children:"10% - 25%"}),e(l,{value:"25+",children:"25%+"})]})})]})]});case"general_ledger":return r(z,{children:[r(o,{item:!0,xs:12,md:6,children:[e(a,{children:"GL Account"}),e(P,{matGroup:Te,selectedMaterialGroup:Re,setSelectedMaterialGroup:ie,placeholder:"Select GL Account"})]}),r(o,{item:!0,xs:12,md:6,children:[e(a,{children:"Account Type"}),e(g,{size:"small",fullWidth:!0,children:r(R,{value:"",onChange:()=>{},displayEmpty:!0,sx:{fontSize:"12px"},MenuProps:{disablePortal:!1,PaperProps:{style:{zIndex:1302,maxHeight:200}}},children:[e(l,{value:"",disabled:!0,children:"Select Account Type"}),e(l,{value:"asset",children:"Asset"}),e(l,{value:"liability",children:"Liability"}),e(l,{value:"equity",children:"Equity"}),e(l,{value:"revenue",children:"Revenue"}),e(l,{value:"expense",children:"Expense"})]})})]}),r(o,{item:!0,xs:12,md:6,children:[e(a,{children:"Balance Type"}),e(g,{size:"small",fullWidth:!0,children:r(R,{value:"",onChange:()=>{},displayEmpty:!0,sx:{fontSize:"12px"},MenuProps:{disablePortal:!1,PaperProps:{style:{zIndex:1302,maxHeight:200}}},children:[e(l,{value:"",disabled:!0,children:"Select Balance Type"}),e(l,{value:"debit",children:"Debit Balance"}),e(l,{value:"credit",children:"Credit Balance"}),e(l,{value:"zero",children:"Zero Balance"})]})})]})]});case"hierarchy":return r(z,{children:[r(o,{item:!0,xs:12,md:6,children:[e(a,{children:"Hierarchy Level"}),e(P,{matGroup:Q,selectedMaterialGroup:be,setSelectedMaterialGroup:oe,placeholder:"Select Hierarchy Level"})]}),r(o,{item:!0,xs:12,md:6,children:[e(a,{children:"Parent Node"}),e(g,{size:"small",fullWidth:!0,children:r(R,{value:"",onChange:()=>{},displayEmpty:!0,sx:{fontSize:"12px"},MenuProps:{disablePortal:!1,PaperProps:{style:{zIndex:1302,maxHeight:200}}},children:[e(l,{value:"",disabled:!0,children:"Select Parent Node"}),e(l,{value:"root",children:"Root"}),e(l,{value:"branch1",children:"Branch 1"}),e(l,{value:"branch2",children:"Branch 2"})]})})]}),r(o,{item:!0,xs:12,md:6,children:[e(a,{children:"Node Type"}),e(g,{size:"small",fullWidth:!0,children:r(R,{value:"",onChange:()=>{},displayEmpty:!0,sx:{fontSize:"12px"},MenuProps:{disablePortal:!1,PaperProps:{style:{zIndex:1302,maxHeight:200}}},children:[e(l,{value:"",disabled:!0,children:"Select Node Type"}),e(l,{value:"parent",children:"Parent Node"}),e(l,{value:"child",children:"Child Node"}),e(l,{value:"leaf",children:"Leaf Node"})]})})]})]});default:return null}};return n.useEffect(()=>{W&&(d(),h())},[W]),n.useEffect(()=>()=>{_&&clearTimeout(_)},[_]),e(z,{children:r(et,{open:W,onClose:Me,maxWidth:"md",fullWidth:!0,PaperProps:{sx:{borderRadius:2,minHeight:"400px",overflow:"visible"}},sx:{"& .MuiDialog-container":{overflow:"visible"},"& .MuiDialog-paper":{overflow:"visible"}},children:[r(Qe,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",pb:1},children:[e(w,{variant:"h6",component:"div",children:$}),r(Ge,{sx:{display:"flex",alignItems:"center",gap:2},children:[e(g,{size:"small",sx:{minWidth:150},children:e(R,{value:U,onChange:s=>Ye(s.target.value),sx:{fontSize:"12px"},MenuProps:{disablePortal:!1,PaperProps:{style:{zIndex:1302,maxHeight:200}}},children:ce.map(s=>e(l,{value:s.code,children:s.desc},s.code))})}),e(Pe,{edge:"end",color:"inherit",onClick:Me,"aria-label":"close",children:e(Ke,{})})]})]}),e(ze,{}),e(Ze,{sx:{pt:3,overflow:"visible"},children:r(o,{container:!0,spacing:3,children:[Ve(),r(o,{item:!0,xs:12,md:6,children:[e(a,{required:!0,children:c.BUSINESS_RULE}),e(P,{matGroup:t(U),selectedMaterialGroup:Y,setSelectedMaterialGroup:ee,placeholder:c.SELECT})]}),r(o,{item:!0,xs:12,md:6,children:[e(a,{required:!0,children:c.NO_OBJECTS}),e(g,{size:"small",fullWidth:!0,children:e(He,{options:je,value:(Oe=C==null?void 0:C.top)==null?void 0:Oe.code,onChange:s=>qe("top",s),placeholder:"Select Number of Objects",disabled:!1,minWidth:"90%",listWidth:210})})]}),r(o,{item:!0,xs:12,md:6,children:[e(a,{required:!0,children:"Created On"}),e(g,{fullWidth:!0,sx:{padding:0},children:e(we,{dateAdapter:Be,children:e(ke,{handleDate:Ue,date:C==null?void 0:C.createdOn})})})]})]})}),e(ze,{}),r(Xe,{sx:{p:2,gap:1,flexDirection:"column"},children:[de&&e(Je,{severity:"error",sx:{width:"100%",mb:1},children:de}),r(Ge,{sx:{display:"flex",gap:1,width:"100%",justifyContent:"flex-end"},children:[e(K,{onClick:ve,variant:"outlined",color:"primary",disabled:v,children:"Clear"}),e(K,{onClick:Me,variant:"outlined",color:"secondary",disabled:v,children:"Cancel"}),e(K,{onClick:()=>{const s=i();if(s.length>0){G(s.join(", "));return}G(""),me(C)},variant:"contained",color:"primary",disabled:v,sx:{minWidth:"120px"},children:v?"Creating...":fe})]})]})]})})},Pt=()=>{var ce;const[W,Ce]=n.useState(null),[me,$]=n.useState(!1),[fe,v]=n.useState(""),[U,D]=n.useState(""),ge=rt(),[A,Z]=n.useState({createdOn:null}),[X,q]=n.useState(!1),[J,Y]=n.useState(!1),[ee,De]=n.useState(""),[S,C]=n.useState([]),[b,j]=n.useState([]),[Se,te]=n.useState(0),[M,re]=n.useState(0),[O,xe]=n.useState((ce=nt)==null?void 0:ce.TOP_SKIP),y=lt(),{showSnackbar:_}=st(),Ee=()=>{setSelectedCreatedBy([]),setSelectedMaterial([]),setSelectedDivision([]),setSelectedReqType([]),setSelectedOptions([]);let t={...rbSearchForm,reqPriority:""};y(commonFilterUpdate({module:"RequestBench",filterData:t})),y(commonFilterClear({module:"RequestBench",days:7})),setClearClicked(!0)},V={fontSize:"12px",fontWeight:500},Ae=F(at)(({theme:t})=>({marginTop:"0px !important",border:`1px solid ${E.primary.border}`,borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.05)","&:not(:last-child)":{borderBottom:0},"&:before":{display:"none"}})),_e=F(it)(({theme:t})=>({minHeight:"2rem !important",margin:"0px !important",backgroundColor:E.primary.ultraLight,borderRadius:"8px 8px 0 0",transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:`${E.primary.light}20`}})),ne=F(K)({borderRadius:"4px",padding:"4px 12px",textTransform:"none",fontSize:"0.875rem"}),Te=F(o)({padding:"0.75rem",gap:"0.5rem"}),Ie=F(o)({display:"flex",justifyContent:"flex-end",paddingRight:"0.75rem",paddingBottom:"0.75rem",paddingTop:"0rem",gap:"0.5rem"}),Q=F(w)({fontSize:"0.75rem",color:E.primary.dark,marginBottom:"0.25rem",fontWeight:500});n.useEffect(()=>{N()},[]),n.useEffect(()=>j([...S]),[S]);const N=(t="")=>{var d,p;const i={page:t==="pagination"?M:0,size:t==="pagination"?O:100,orderBy:"creationDate"};D(!0);const u=h=>{C((h==null?void 0:h.body)||[]),D(!1)},a=h=>{C([]),D(!1)};B(`/${k}${(p=(d=H)==null?void 0:d.DATA_CLEANSE_APIS)==null?void 0:p.CLEANSING_REQ}`,"post",u,a,i)},ye=t=>{var d,p;$(!0);let i={requestId:t};const u=h=>{var ue;const x=URL.createObjectURL(h),L=document.createElement("a");L.href=x,L.setAttribute("download",`${t}_Data Cleanse.pdf`),document.body.appendChild(L),L.click(),document.body.removeChild(L),URL.revokeObjectURL(x),$(!1),v(""),_(`${t}${(ue=c)==null?void 0:ue.EXPORT_SUCCESS}`,"success")},a=()=>{$(!1),v(""),_(`Failed exporting ${t}_Data Cleanse.pdf`,"error")};B(`/${k}${(p=(d=H)==null?void 0:d.DATA_CLEANSE_APIS)==null?void 0:p.DOWNLOAD_PDF}`,"postandgetblob",u,a,i)},le=[{code:"Material",desc:""},{code:"Cost Center",desc:""},{code:"Profit Center",desc:""},{code:"General Ledger",desc:""},{code:"Hierarchy",desc:""}],se=t=>{var d,p,h;let i={RequestId:"",ObjectCount:0,UserId:"<EMAIL>",InitiatedOn:he(new Date).format("YYYY-MM-DDThh:mm:ss")??"",Status:"In-Progress",Top:((d=t==null?void 0:t.top)==null?void 0:d.code)||"10",Skip:"0",FromDate:he(t==null?void 0:t.createdOn[0]).format("YYYY-MM-DDThh:mm:ss")??"",ToDate:he(t==null?void 0:t.createdOn[1]).format("YYYY-MM-DDThh:mm:ss")??"",DtName:"MDG_MAT_MATERIAL_FIELD_CONFIG",DtVersion:"v4",DtRegion:"US",DtScenario:"Create",DtMaterialType:"FERT"};const u=x=>{_(x==null?void 0:x.message,"success"),N(),q(!1)},a=x=>{_(x.message||c.ERROR,"error")};B(`/${k}${(h=(p=H)==null?void 0:p.DATA_CLEANSE_APIS)==null?void 0:h.INITIATE_DATA_QUALITY_CHECK}`,"post",u,a,[i])},ae=(t,i)=>({field:t,headerName:i,editable:!1,flex:1.4,renderCell:u=>e(Tt,{sx:{justifyContent:"flex-start",borderRadius:"4px",color:"#000",width:"100%",minWidth:"4.6rem",fontSize:"12px",background:E.statusColorMap[u.row.status.toLowerCase().replace(/[^a-z0-9]/gi,"")]||E.statusColorMap.default},label:u.row.status})}),Re=t=>{Z({...A,createdOn:t})},ie=t=>{if(t.target.value!==null){const i=t.target.name,u=t.target.value;let a={...A,[i]:u};Z(a)}},be=t=>{if(!t){j([...S]),te(S==null?void 0:S.length);return}const i=S==null?void 0:S.filter(u=>{var p;let a=!1,d=Object.keys(u);for(let h=0;h<d.length&&(a=u[d[h]]?(u==null?void 0:u[d==null?void 0:d[h]])&&((p=u==null?void 0:u[d==null?void 0:d[h]].toString().toLowerCase())==null?void 0:p.indexOf(t==null?void 0:t.toLowerCase()))!=-1:!1,!a);h++);return a});j([...i]),te(i==null?void 0:i.length)},oe=t=>{const i=t.target.value;xe(i),re(0)},de=(t,i)=>{re(isNaN(i)?0:i)};n.useEffect(()=>{M!==0&&O*(M+1)>S.length&&S.length%O===0&&N("pagination")},[M]);const G=[{field:"requestId",headerName:"Request ID",flex:1.5},{field:"module",headerName:"Module",flex:1,renderCell:t=>t.row.module||"Material"},{field:"userId",headerName:"Initiated By",flex:1},{field:"creationDate",headerName:"Initiated On",flex:1,renderCell:t=>{var i;return he((i=t==null?void 0:t.row)==null?void 0:i.initiatedOn).format("YYYY-MM-DD hh:mm")??""}},{field:"objectCount",headerName:"Object Count",flex:1},ae(It,"Status"),{field:"actions",headerName:"Actions",flex:1,headerAlign:"center",align:"center",renderCell:t=>{var i,u,a;return r("div",{children:[e(Fe,{title:"Report",children:e(Pe,{disabled:((i=t==null?void 0:t.row)==null?void 0:i.status)===`${pe.DRAFT}`,onClick:()=>{var d,p,h;if((d=t==null?void 0:t.row)!=null&&d.objectCount){ye((p=t==null?void 0:t.row)==null?void 0:p.requestId);return}_((h=c)==null?void 0:h.FAILED_FETCHING_DATA,"error")},children:e(yt,{sx:{color:((u=t==null?void 0:t.row)==null?void 0:u.status)===`${pe.DRAFT}`?"#808080":"#ffd93f"}})})}),e(Fe,{title:"View Requests",children:e(Pe,{disabled:((a=t==null?void 0:t.row)==null?void 0:a.status)===`${pe.DRAFT}`,onClick:()=>{var d,p,h,x;if((d=t==null?void 0:t.row)!=null&&d.objectCount){ge(`${(p=gt)==null?void 0:p.DATA_CHECK}?requestId=${(h=t==null?void 0:t.row)==null?void 0:h.requestId}`,{state:t==null?void 0:t.row});return}_((x=c)==null?void 0:x.FAILED_FETCHING_DATA,"error")},children:e(St,{sx:{fontSize:"20px",color:t.row.status===pe.DRAFT?"#808080":`${E.blue.indigo}`}})})})]})}}];return r("div",{style:{...ot,backgroundColor:"#FAFCFF"},children:[r(dt,{spacing:1,children:[e(o,{container:!0,mt:0,sx:xt,children:r(o,{item:!0,md:4,children:[e(w,{variant:"h3",children:e("strong",{children:c.DATA_CLEANSE})}),e(w,{variant:"body2",color:"#777",children:"This view displays the list of Data Cleanse Requests"})]})}),e(o,{container:!0,sx:{paddingBottom:"10px"},children:e(o,{item:!0,md:12,children:r(Ae,{expanded:J,onChange:(t,i)=>Y(i),sx:{mb:2},children:[r(_e,{expandIcon:e(ct,{sx:{fontSize:"1.25rem",color:E.primary.main}}),"aria-controls":"panel1a-content",id:"panel1a-header",children:[e(ut,{sx:{fontSize:"1.25rem",marginRight:1,color:E.primary.main}}),e(w,{sx:{fontSize:"0.875rem",fontWeight:600,color:E.primary.dark},children:c.FILTER})]}),r(ht,{sx:{padding:0},children:[e(Te,{children:r(o,{container:!0,rowSpacing:1,spacing:2,alignItems:"center",sx:{padding:"0rem 1rem 0.5rem"},children:[r(o,{item:!0,md:2,children:[e(Q,{sx:V,children:"Module"}),e(g,{size:"small",fullWidth:!0,children:e(He,{options:le,value:ee,onChange:t=>{De(t)},placeholder:"Select Module",disabled:!1,minWidth:"90%",listWidth:210})})]}),r(o,{item:!0,md:2,children:[e(Q,{sx:V,children:"Request ID"}),e(pt,{name:"requestId",fullWidth:!0,variant:"outlined",placeholder:"Request ID",size:"small",InputLabelProps:{shrink:!0},onChange:ie,value:A==null?void 0:A.requestId})]}),r(o,{item:!0,md:2,children:[e(Q,{sx:V,children:"Created On"}),e(g,{fullWidth:!0,sx:{padding:0},children:e(we,{dateAdapter:Be,children:e(ke,{handleDate:Re,date:A==null?void 0:A.createdOn})})})]})]})}),r(Ie,{children:[e(ne,{variant:"outlined",size:"small",startIcon:e(Ct,{sx:{fontSize:"1rem"}}),onClick:Ee,sx:{borderColor:E.primary.main,color:E.primary.main},children:"Clear"}),e(ne,{variant:"contained",size:"small",startIcon:e(mt,{sx:{fontSize:"1rem"}}),onClick:()=>se(A),sx:{backgroundColor:E.primary.main},children:"Search"})]})]})]})})}),e(ft,{isLoading:U,module:"DataCleanse",width:"100%",title:"Data Cleanse Requests",rows:b||[],columns:G,onSearch:t=>be(t),onRefresh:N,page:M,showSearch:!0,showRefresh:!0,pageSize:O,rowCount:Se??(b==null?void 0:b.length)??0,onPageChange:de,onPageSizeChange:oe,getRowIdValue:"id",hideFooter:!0,showCustomNavigation:!0})]}),e(At,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:e(Et,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:W,onChange:t=>{Ce(t)},children:e(K,{size:"small",variant:"contained",onClick:()=>{q(!0)},children:c.CLEANSE_REQUEST})})}),e(Rt,{open:X,onClose:()=>q(!1),handleSubmit:se,title:c.NEW_REQUEST,submitButtonText:c.INITIATE_REQUEST,isLoading:!1}),e(_t,{blurLoading:me,loaderMessage:fe})]})};export{Pt as default};
