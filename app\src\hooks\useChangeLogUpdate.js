import { useDispatch, useSelector } from "react-redux";
import { 
  CURRENT_DATE, 
  getObjectValue, 
  getPreviousValueFromPayload,
  getPreviousValueForAdditionalData,
  getPreviousValueForUnitOfMeasure,
  splitPlantData, 
  transformChangeLogArray, 
  getPreviousValueForEanData
} from "@helper/helper";
import { CREATE_CHANGE_LOG_MANDATORY_FIELDS, CREATE_VIEWS_MANIPULATION } from "@constant/changeTemplates";
import { setCreateChangeLogData, setCreateTemplateArray } from "@app/changeLogReducer";

export const useChangeLogUpdate = () => {
  const dispatch = useDispatch();
  const userMangmentData = useSelector((state) => state.userManagement.userData);
  const createPayloadCopyForChangeLog = useSelector((state) => state.changeLog.createPayloadCopyForChangeLog || []);
  const createTemplateArray = useSelector((state) => state.changeLog.createTemplateArray);
  const updateChangeLog = ({
    materialID,
    viewName,
    plantData,
    fieldName,
    jsonName,
    currentValue,
    requestId,
    childRequestId,
    isDescriptionData = false,
    isUnitOfMeasure = false,
    isAdditionalEAN = false,
    uomId = null,
    eanId = null,
    language,
  }) => {
    let previousValue;
    if (isDescriptionData) {
      previousValue = getPreviousValueForAdditionalData(
        materialID,
        jsonName,
        createPayloadCopyForChangeLog,
        language
      );
    } else if (isUnitOfMeasure) {
      previousValue = getPreviousValueForUnitOfMeasure(
        materialID,
        uomId,
        fieldName,
        createPayloadCopyForChangeLog,
        viewName,
        plantData,
      );
    } else if(isAdditionalEAN){
      previousValue = getPreviousValueForEanData(
        materialID,
        eanId,
        fieldName,
        createPayloadCopyForChangeLog,
      );
    }
    else {
      previousValue = getPreviousValueFromPayload(
        materialID,
        viewName,
        plantData,
        jsonName,
        createPayloadCopyForChangeLog
      );
    }
    const objectValues = getObjectValue(CREATE_CHANGE_LOG_MANDATORY_FIELDS, viewName);
    const otherMandatoryFields = splitPlantData(plantData, objectValues);
    const ManipulatedViewName = getObjectValue(CREATE_VIEWS_MANIPULATION, viewName);
    const changeFields = {
      ObjectNo: `${materialID}${otherMandatoryFields}`,
      ChangedBy: userMangmentData?.emailId,
      ChangedOn: CURRENT_DATE,
      FieldName: fieldName,
      PreviousValue: previousValue,
      CurrentValue: currentValue,
      SAPValue: previousValue,
      tableName: ManipulatedViewName
    };
  
    dispatch(setCreateTemplateArray(changeFields));
    const updatedCreateTemplateArray = [...createTemplateArray, changeFields];
    const finalChangeLogDataPayload = transformChangeLogArray(updatedCreateTemplateArray);
    let changeLogPayload = {
      RequestId: requestId,
      changeLogId: null,
      ChildRequestId:childRequestId?.slice(3),
      ...finalChangeLogDataPayload
    };
    dispatch(setCreateChangeLogData(changeLogPayload));
  };

  return { updateChangeLog };
};
