import{r as d,b as Do,s as Bo,u as No,q as W,bh as To,eQ as Fo,a as t,bX as wo,j as p,V as J,aF as Q,T as S,I as X,aG as he,W as x,x as ee,B as se,ar as Ds,E as Bs,X as oe,t as A,G as F,aC as ge,cb as Io,cc as Eo,z as qo,C as Po,aj as Oo,al as $o,b1 as Lo,h as _o,F as $,bs as Ns,bt as zo,ab as Ts,bm as jo,aD as Vo,aE as Ro,b8 as ne,aB as L,K as k,bq as Ce,eK as N,eO as Wo,ai as Uo}from"./index-75c1660a.js";import{d as Ko}from"./ArrowCircleLeftOutlined-f7b52d40.js";import{l as Fs}from"./lookup-1dcf10ac.js";import{M as Go,a as Ho}from"./UtilDoc-7fb813ce.js";import{d as Xo}from"./AttachFileOutlined-872f8e38.js";import{R as Yo}from"./ReusableAttachementAndComments-682b0475.js";import{T as Zo}from"./Timeline-5c068db1.js";import{t as Jo,T as Qo,a as xo,b as en,c as sn,d as on}from"./TimelineSeparator-6e03ad1b.js";/* empty css            */import"./FileDownloadOutlined-329b8f56.js";import"./VisibilityOutlined-a5a8c4d9.js";import"./DeleteOutlined-fe5b7345.js";import"./Slider-c4e5ff46.js";import"./CloudUpload-d5d09566.js";import"./utilityImages-067c3dc2.js";import"./Add-62a207fb.js";import"./Delete-1d158507.js";import"./clsx-a965ebfb.js";const Nn=()=>{const[q,w]=d.useState(!0),[ws,nn]=d.useState("1"),[z,Is]=d.useState([]);d.useState([]);const te=Do(),re=Bo(),n=No().state,[fe,C]=d.useState(""),[Es,ae]=d.useState(!1),[rn,f]=d.useState(!1),[qs,g]=d.useState(!1),[Ps,m]=d.useState(!1),[an,y]=d.useState(!0),[Os,b]=d.useState(!1),[$s,me]=d.useState(!1),[Ls,ye]=d.useState(!1),[_s,be]=d.useState(!1),[j,Se]=d.useState(""),[ve,D]=d.useState(!1),[Ae,Me]=d.useState(!0),[ke,zs]=d.useState(!0),[ln,De]=d.useState(!0),[js,Be]=d.useState(!1),[le,Vs]=d.useState([]),[ie,Rs]=d.useState([]),[Ws,ce]=d.useState(!1),[Us,M]=d.useState(!1),[de,Ks]=d.useState(""),[Gs,ue]=d.useState(!1),[Y,Ne]=d.useState([]),[Hs,Te]=d.useState(!0),[Xs,U]=d.useState(!1);console.log(n,"massBKRowData"),console.log("mouse",z);let s=W(e=>e.userManagement.taskData),o=W(e=>e.userManagement.userData),Ys=W(e=>{var l;return(l=e.userManagement.entitiesAndActivities)==null?void 0:l["Bank Key"]});const Zs=W(e=>e.appSettings),V=W(e=>e.bankKey.MultipleBankKeyData);W(e=>e.profitCenter.MultipleBankKey);const I=()=>{ae(!0)},Js=()=>{Ws?(ae(!1),ce(!1)):(ae(!1),te("/masterDataCockpit/BankKey"))},P=()=>{me(!0)},Fe=()=>{me(!1)},Qs=()=>{},xs=()=>{Be(!0)},we=()=>{Be(!1)},eo=[{field:"id",headerName:"Document ID",flex:1,hide:!0},{field:"docType",headerName:"Type",flex:1},{field:"docName",headerName:"Document Name",flex:1},{field:"uploadedOn",headerName:"Uploaded On",flex:1,align:"center",headerAlign:"center"},{field:"uploadedBy",headerName:"Uploaded By",sortable:!1,flex:1},{field:"action",headerName:"Action",sortable:!1,filterable:!1,align:"center",headerAlign:"center",flex:1,renderCell:e=>p($,{children:[t(Go,{index:e.row.id,name:e.row.docName}),t(Ho,{index:e.row.id,name:e.row.docName})]})}],so=()=>{let e=s!=null&&s.subject?s==null?void 0:s.subject:n==null?void 0:n.requestId,l=a=>{var r=[];a.documentDetailDtoList.forEach(c=>{var u={id:c.documentId,docType:c.fileType,docName:c.fileName,uploadedOn:Ts(c.docCreationDate).format(Zs.date),uploadedBy:c.createdBy};r.push(u)}),Vs(r)};k(`/${Ce}/documentManagement/getDocByRequestId/${e}`,"get",l)},oo=()=>{let e=s!=null&&s.subject?s==null?void 0:s.subject:n==null?void 0:n.requestId,l=r=>{console.log("commentsdata",r);var c=[];r.body.forEach(u=>{var B={id:u.requestId,comment:u.comment,user:u.createdByUser,createdAt:u.updatedAt};c.push(B)}),Rs(c),console.log("commentrows",c)},a=r=>{console.log(r)};k(`/${N}/activitylog/fetchTaskDetailsForRequestId?requestId=${e}`,"get",l,a)};d.useEffect(()=>{so(),oo(),Ks(To("BK"))},[]);const i=(e,l)=>{const a=e==null?void 0:e.find(r=>(r==null?void 0:r.fieldName)===l);return a?a.value:""},Ie=(e,l)=>{const a=e==null?void 0:e.find(r=>(r==null?void 0:r.fieldName)===l);return console.log(a.value,"field.value"),a.value===!0?"X":""},no=()=>{var r,c,u,B;console.log(s==null?void 0:s.processDesc,n==null?void 0:n.requestType,"sdfkhdgkf"),w(!0);let e={};(s==null?void 0:s.processDesc)==="Mass Change"?e={massCreationId:"",massChangeId:s!=null&&s.subject?(r=s==null?void 0:s.subject)==null?void 0:r.slice(3):n==null?void 0:n.requestId.slice(3),screenName:"Change"}:(s==null?void 0:s.processDesc)==="Mass Create"?e={massCreationId:s!=null&&s.subject?(c=s==null?void 0:s.subject)==null?void 0:c.slice(3):n==null?void 0:n.requestId.slice(3),massChangeId:"",screenName:"Create"}:(n==null?void 0:n.requestType)==="Mass Create"?e={massCreationId:(u=n==null?void 0:n.requestId)==null?void 0:u.slice(3),massChangeId:"",screenName:"Create"}:(n==null?void 0:n.requestType)==="Mass Change"&&(e={massCreationId:"",massChangeId:(B=n==null?void 0:n.requestId)==null?void 0:B.slice(3),screenName:"Change"});const l=v=>{w(!1),v.body&&re(Wo(v==null?void 0:v.body))},a=v=>{console.log(v)};k(`/${N}/data/displayMassBankKey`,"post",l,a,e),console.log(e,"payload for display bank key")},[pe,to]=d.useState(0),ro=(e,l)=>{const a=c=>{re(Uo({keyName:e,data:c.body})),to(u=>u+1)},r=c=>{console.log(c)};k(`/${N}/data/${l}`,"get",a,r)},ao=()=>{var e,l;console.log("Calleddddd"),(l=(e=Fs)==null?void 0:e.bankKey)==null||l.map(a=>{ro(a==null?void 0:a.keyName,a==null?void 0:a.endPoint)})},lo=()=>{var e,l;console.log("apiCount",pe),pe==((l=(e=Fs)==null?void 0:e.bankKey)==null?void 0:l.length)?w(!1):w(!0)};d.useEffect(()=>{lo()},[pe]),d.useEffect(()=>{if(V.length===0)no();else return},[]),d.useEffect(()=>{ao(),re(Fo())},[]);const io=e=>{e.length>0?(D(!0),console.log("selectedIds1",e)):D(!1),console.log("selectedIds",e),Is(e)},E=V==null?void 0:V.map((e,l)=>{var c,u,B,v,K,G,H;const a=e,r=((c=e==null?void 0:e.viewData)==null?void 0:c["Bank Details"])||{};return console.log(r,a,"basicData"),{id:l,bankKey:a==null?void 0:a.BankKey,bankCountry:a==null?void 0:a.BankCtry,bankName:((u=r.Address.find(h=>(h==null?void 0:h.fieldName)==="Bank Name"))==null?void 0:u.value)||"",Region:((B=r.Address.find(h=>(h==null?void 0:h.fieldName)==="Region"))==null?void 0:B.value)||"",City:((v=r.Address.find(h=>(h==null?void 0:h.fieldName)==="City"))==null?void 0:v.value)||"",bankNumber:((K=r["Control Data"].find(h=>(h==null?void 0:h.fieldName)==="Bank Number"))==null?void 0:K.value)||"",bankBranch:((G=r.Address.find(h=>(h==null?void 0:h.fieldName)==="Bank Branch"))==null?void 0:G.value)||"",swiftBic:((H=r["Control Data"].find(h=>(h==null?void 0:h.fieldName)==="SWIFT/BIC"))==null?void 0:H.value)||""}});console.log(E,"initialRows====");const co=[{field:"bankKey",headerName:"Bank key",editable:!1,flex:1,renderCell:e=>{const l=Y.find(a=>a.bankKey===e.value);return console.log(l,"isDirectMatch"),console.log(e,"params"),l&&l.code===400?t(S,{sx:{fontSize:"12px",color:"red"},children:e.value}):t(S,{sx:{fontSize:"12px"},children:e.value})}},{field:"bankName",headerName:"Bank Name",editable:!1,flex:1},{field:"bankCountry",headerName:"Bank Country",editable:!1,flex:1},{field:"Region",headerName:"Region",editable:!1,flex:1},{field:"City",headerName:"City",editable:!1,flex:1},{field:"bankNumber",headerName:"Bank Number",editable:!1,flex:1},{field:"bankBranch",headerName:"Bank Branch",editable:!1,flex:1},{field:"swiftBic",headerName:"Swift/Bic",editable:!1,flex:1}];console.log(s==null?void 0:s.createdOn,"created_on_task",s);var T=V.map(e=>{var l,a,r,c,u,B,v,K,G,H,h,$e,Le,_e,ze,je,Ve,Re,We,Ue,Ke,Ge,He,Xe,Ye,Ze,Je,Qe,xe,es,ss,os,ns,ts,rs,as,ls,is,cs,ds,us,ps,hs,gs,Cs,fs,ms,ys,bs,Ss,vs,As,Ms,ks;return console.log("samsung",e),{AddressDto:{AddressID:(e==null?void 0:e.AddressId)??"",Title:e!=null&&e.Title?e==null?void 0:e.Title:"",Name:i((l=e==null?void 0:e.viewData["Address Details"])==null?void 0:l.Name,"Name"),Name2:i((a=e==null?void 0:e.viewData["Address Details"])==null?void 0:a.Name,"Name 1"),Name3:i((r=e==null?void 0:e.viewData["Address Details"])==null?void 0:r.Name,"Name 2"),Name4:i((c=e==null?void 0:e.viewData["Address Details"])==null?void 0:c.Name,"Name 3"),Sort1:i((u=e==null?void 0:e.viewData["Address Details"])==null?void 0:u["Search Terms"],"Search Term 1"),Sort2:i((B=e==null?void 0:e.viewData["Address Details"])==null?void 0:B["Search Terms"],"Search Term 2"),BuildLong:i((v=e==null?void 0:e.viewData["Address Details"])==null?void 0:v["Street Address"],"Building Code"),RoomNo:i((K=e==null?void 0:e.viewData["Address Details"])==null?void 0:K["Street Address"],"Room Number"),Floor:i((G=e==null?void 0:e.viewData["Address Details"])==null?void 0:G["Street Address"],"Floor"),COName:i((H=e==null?void 0:e.viewData["Address Details"])==null?void 0:H["Street Address"],"c/o"),StrSuppl1:i((h=e==null?void 0:e.viewData["Address Details"])==null?void 0:h["Street Address"],"Street 1"),StrSuppl2:i(($e=e==null?void 0:e.viewData["Address Details"])==null?void 0:$e["Street Address"],"Street 2"),Street:i((Le=e==null?void 0:e.viewData["Address Details"])==null?void 0:Le["Street Address"],"Street 3"),HouseNo:i((_e=e==null?void 0:e.viewData["Address Details"])==null?void 0:_e["Street Address"],"House Number"),HouseNo2:i((ze=e==null?void 0:e.viewData["Address Details"])==null?void 0:ze["Street Address"],"Supplement"),StrSuppl3:i((je=e==null?void 0:e.viewData["Address Details"])==null?void 0:je["Street Address"],"Street 4"),Location:i((Ve=e==null?void 0:e.viewData["Address Details"])==null?void 0:Ve["Street Address"],"Street 5"),District:i((Re=e==null?void 0:e.viewData["Address Details"])==null?void 0:Re["Street Address"],"District"),HomeCity:i((We=e==null?void 0:e.viewData["Address Details"])==null?void 0:We["Street Address"],"Other City"),PostlCod1:i((Ue=e==null?void 0:e.viewData["Address Details"])==null?void 0:Ue["Street Address"],"Postal Code"),PostlCod2:e!=null&&e.PostalCode1?e==null?void 0:e.PostalCode1:"",PostlCod3:e!=null&&e.CompanyPostCd?e==null?void 0:e.CompanyPostCd:"",PoBox:e!=null&&e.POBox?e==null?void 0:e.POBox:"",PoBoxCit:i((Ke=e==null?void 0:e.viewData["Address Details"])==null?void 0:Ke["PO Box Address"],"PO Box City"),PoBoxReg:i((Ge=e==null?void 0:e.viewData["Address Details"])==null?void 0:Ge["PO Box Address"],"Region 2"),PoboxCtry:i((He=e==null?void 0:e.viewData["Address Details"])==null?void 0:He["PO Box Address"],"Country 2"),Country:i((Xe=e==null?void 0:e.viewData["Address Details"])==null?void 0:Xe["Street Address"],"Country 1"),TimeZone:i((Ye=e==null?void 0:e.viewData["Address Details"])==null?void 0:Ye["Street Address"],"Time Zone"),Taxjurcode:i((Ze=e==null?void 0:e.viewData["Address Details"])==null?void 0:Ze["Street Address"],"Tax Jurisdiction"),Transpzone:i((Je=e==null?void 0:e.viewData["Address Details"])==null?void 0:Je["Street Address"],"Transport Zone"),Regiogroup:i((Qe=e==null?void 0:e.viewData["Address Details"])==null?void 0:Qe["Street Address"],"Structure Group"),DontUseS:i((xe=e==null?void 0:e.viewData["Address Details"])==null?void 0:xe["Street Address"],"Undeliverable"),DontUseP:i((es=e==null?void 0:e.viewData["Address Details"])==null?void 0:es["PO Box Address"],"Undeliverable 1"),PoWONo:Ie((ss=e==null?void 0:e.viewData["Address Details"])==null?void 0:ss["PO Box Address"],"PO Box w/o No."),DeliServType:i((os=e==null?void 0:e.viewData["Address Details"])==null?void 0:os["PO Box Address"],"Delivery Service Type"),DeliServNumber:i((ns=e==null?void 0:e.viewData["Address Details"])==null?void 0:ns["PO Box Address"],"Delivery Service No."),Township:i((ts=e==null?void 0:e.viewData["Address Details"])==null?void 0:ts["PO Box Address"],"Township"),Langu:i((rs=e==null?void 0:e.viewData["Address Details"])==null?void 0:rs.Communication,"Language"),Tel1Numbr:i((as=e==null?void 0:e.viewData["Address Details"])==null?void 0:as.Communication,"Telephone"),Tel1Ext:i((ls=e==null?void 0:e.viewData["Address Details"])==null?void 0:ls.Communication,"Extension"),FaxNumber:i((is=e==null?void 0:e.viewData["Address Details"])==null?void 0:is.Communication,"Fax"),FaxExtens:i((cs=e==null?void 0:e.viewData["Address Details"])==null?void 0:cs.Communication,"Extension 1"),MobilePhone:i((ds=e==null?void 0:e.viewData["Address Details"])==null?void 0:ds.Communication,"Mobile Phone"),EMail:i((us=e==null?void 0:e.viewData["Address Details"])==null?void 0:us.Communication,"E-Mail Address"),AdrNotes:i((ps=e==null?void 0:e.viewData["Address Details"])==null?void 0:ps.Communication,"Notes"),Region:i((hs=e==null?void 0:e.viewData["Address Details"])==null?void 0:hs["Street Address"],"Region 1"),PoBoxLobby:e!=null&&e.PoBoxLobby?e==null?void 0:e.PoBoxLobby:""},BankKeyID:(e==null?void 0:e.BankKeyId)??"",ReqCreatedBy:o==null?void 0:o.user_id,ReqCreatedOn:s!=null&&s.createdOn?"/Date("+(s==null?void 0:s.createdOn)+")/":"/Date("+Date.parse(n==null?void 0:n.createdOn)+")/",RequestStatus:"",CreationId:"",EditId:"",DeleteId:"",MassCreationId:(s==null?void 0:s.processDesc)==="Mass Create"?(gs=s==null?void 0:s.subject)==null?void 0:gs.slice(3):(n==null?void 0:n.requestType)==="Mass Create"?n==null?void 0:n.requestId.slice(3):"",MassEditId:(s==null?void 0:s.processDesc)==="Mass Change"?(Cs=s==null?void 0:s.subject)==null?void 0:Cs.slice(3):(n==null?void 0:n.requestType)==="Mass Change"?n==null?void 0:n.requestId.slice(3):"",MassDeleteId:"",RequestType:(s==null?void 0:s.processDesc)==="Mass Create"?"Mass Create":(s==null?void 0:s.processDesc)==="Mass Change"||(n==null?void 0:n.requestType)==="Mass Change"?"Mass Change":(n==null?void 0:n.requestType)==="Mass Create"?"Mass Create":"",TaskId:s!=null&&s.taskId?s==null?void 0:s.taskId:"",Remarks:j||"",Action:(s==null?void 0:s.processDesc)==="Mass Create"?"I":(s==null?void 0:s.processDesc)==="Mass Change"||(n==null?void 0:n.requestType)==="Mass Change"?"U":(n==null?void 0:n.requestType)==="Mass Create"?"I":"",Validation:Hs===!0?"X":"",BankCtry:e==null?void 0:e.BankCtry,BankKey:e==null?void 0:e.BankKey,BankName:i((fs=e==null?void 0:e.viewData["Bank Details"])==null?void 0:fs.Address,"Bank Name"),BankRegion:i((ms=e==null?void 0:e.viewData["Bank Details"])==null?void 0:ms.Address,"Region"),BankStreet:i((ys=e==null?void 0:e.viewData["Bank Details"])==null?void 0:ys.Address,"Street"),City:i((bs=e==null?void 0:e.viewData["Bank Details"])==null?void 0:bs.Address,"City"),BankBranch:i((Ss=e==null?void 0:e.viewData["Bank Details"])==null?void 0:Ss.Address,"Bank Branch"),SwiftCode:i((vs=e==null?void 0:e.viewData["Bank Details"])==null?void 0:vs["Control Data"],"SWIFT/BIC"),BankGroup:i((As=e==null?void 0:e.viewData["Bank Details"])==null?void 0:As["Control Data"],"Bank Group"),PobkCurac:Ie((Ms=e==null?void 0:e.viewData["Bank Details"])==null?void 0:Ms["Control Data"],"Postbank Acct"),BankNo:i((ks=e==null?void 0:e.viewData["Bank Details"])==null?void 0:ks["Control Data"],"Bank Number")}});console.log("massBKRowData",n);const uo=()=>{const e=T;console.log("isLoading1",q),console.log("paylaod",e);const l=r=>{M(!1),r.statusCode===200?(console.log("success"),g("Create"),C(`Mass Bank Key Submitted for Approval with ID NBM${r.body}`),m("success"),y(!1),b(!0),I(),f(!0),D(!0)):(g("Error"),b(!1),C("Failed Submitting the Mass Bank Key for Approval  "),m("danger"),y(!1),f(!0),P(),D(!0)),handleClose()},a=r=>{console.log(r)};k(`/${N}/massAction/bankKeysApprovalSubmit`,"post",l,a,e)},po=()=>{E.filter((r,c)=>z.includes(c));const e=T;console.log("paylaod",e),console.log("isLoading2",q);const l=r=>{M(!1),D(!0),r.statusCode===200?(console.log("success"),g("Create"),C(`Mass Bank Keys Change Submitted for Approval with ID CBM${r.body}`),m("success"),y(!1),b(!0),I(),f(!0)):(g("Error"),b(!1),C("Failed Submitting Mass Bank Key Data for Approval"),m("danger"),y(!1),f(!0),P())},a=r=>{console.log("error")};k(`/${N}/massAction/changeBankKeysApprovalSubmit`,"post",l,a,e)},ho=()=>{E.filter((r,c)=>z.includes(c));const e=T;console.log("paylaod",e),console.log("isLoading3",q);const l=r=>{M(!1),r.statusCode===201?(console.log("success"),g("Create"),C("Mass Bank Key Approved & SAP Syndication Completed"),m("success"),y(!1),b(!0),I(),f(!0),D(!0)):(g("Error"),b(!1),C("Failed Submitting the Bank key for Approval "),m("danger"),y(!1),f(!0),P(),D(!0)),handleClose()},a=r=>{console.log(r)};k(`/${N}/massAction/createBankKeysApproved`,"post",l,a,e)},go=()=>{const e=T;console.log("paylaod",e),console.log("isLoading4",q);const l=r=>{M(!1),D(!0),r.statusCode===201?(console.log("success"),g("Create"),C("Mass Bank Key Change Approved & SAP Syndication Completed"),m("success"),y(!1),b(!0),I(),f(!0)):(g("Error"),b(!1),C("Failed Approving Mass Bank Key Change"),m("danger"),y(!1),f(!0),P())},a=r=>{console.log("error")};k(`/${N}/massAction/changeBankKeysApproved`,"post",l,a,e)},Co=()=>{const e=T;console.log("paylaod",e),console.log("isLoading5",q);const l=r=>{if(M(!1),D(!0),r.statusCode===200){console.log("success"),g("Create"),C(`Mass Bank Key Submitted for Review with ID NBM${r.body}`),m("success"),y(!1),b(!0),I(),f(!0);const c={artifactId:de,createdBy:o==null?void 0:o.emailId,artifactType:"BankKey",requestId:`NBM${r==null?void 0:r.body}`},u=v=>{console.log("Second API success",v)},B=v=>{console.error("Second API error",v)};k(`/${Ce}/documentManagement/updateDocRequestId`,"post",u,B,c)}else g("Error"),b(!1),C("Failed Submitting the Mass Bank Key for Review  "),m("danger"),y(!1),f(!0),P();handleClose()},a=r=>{console.log(r)};k(`/${N}/massAction/bankKeysSubmitForReview`,"post",l,a,e)},fo=()=>{const e=T;console.log("paylaod",e),console.log("isLoading5",q);const l=r=>{if(M(!1),D(!0),r.statusCode===200){console.log("success"),g("Create"),C(`Mass Bank Key Submitted for Review with ID CBM${r.body}`),m("success"),y(!1),b(!0),I(),f(!0);const c={artifactId:de,createdBy:o==null?void 0:o.emailId,artifactType:"BankKey",requestId:`CBM${r==null?void 0:r.body}`},u=v=>{console.log("Second API success",v)},B=v=>{console.error("Second API error",v)};k(`/${Ce}/documentManagement/updateDocRequestId`,"post",u,B,c)}else g("Error"),b(!1),C("Failed Submitting the Mass Bank Key for Review  "),m("danger"),y(!1),f(!0),P();handleClose()},a=r=>{console.log(r)};k(`/${N}/massAction/changeBankKeysSubmitForReview`,"post",l,a,e)},Ee=(e,l)=>{const a=e.target.value;if(a.length>0&&a[0]===" ")Se(a.trimStart());else{let r=a.toUpperCase();Se(r)}},_=()=>{U(!1),D(!0),be(!1)},mo=()=>{(o==null?void 0:o.role)==="MDM Steward"&&(s==null?void 0:s.processDesc)==="Mass Create"||(o==null?void 0:o.role)==="MDM Steward"&&(n==null?void 0:n.requestType)==="Mass Create"?(M(!0),_(),uo()):(o==null?void 0:o.role)==="Approver"&&s.processDesc==="Mass Create"||(o==null?void 0:o.role)==="Approver"&&(n==null?void 0:n.requestType)==="Mass Create"?(M(!0),_(),ho()):(o==null?void 0:o.role)==="Finance"&&s.processDesc==="Mass Create"||(o==null?void 0:o.role)==="Finance"&&(n==null?void 0:n.requestType)==="Mass Create"?j.length<=0?U(!0):(U(!1),M(!0),_(),Co()):(o==null?void 0:o.role)==="MDM Steward"&&(s==null?void 0:s.processDesc)==="Mass Change"||(o==null?void 0:o.role)==="MDM Steward"&&(n==null?void 0:n.requestType)==="Mass Change"?(M(!0),_(),po()):(o==null?void 0:o.role)==="Approver"&&s.processDesc==="Mass Change"||(o==null?void 0:o.role)==="Approver"&&(n==null?void 0:n.requestType)==="Mass Change"?(M(!0),_(),go()):((o==null?void 0:o.role)==="Finance"&&(s==null?void 0:s.processDesc)==="Mass Change"||(o==null?void 0:o.role)==="Finance"&&(n==null?void 0:n.requestType)==="Mass Change")&&(j.length<=0?U(!0):(U(!1),M(!0),_(),fo()))},R=()=>{D(!1),be(!0)},O=()=>{ye(!1)},Z=()=>{D(!1),ye(!0)},yo=()=>{console.log("isLoading6",q),E.filter((r,c)=>z.includes(c));const e=T,l=r=>{w(!1),D(!0),r.statusCode===200?(console.log("success"),g("Create"),C(`Bank Key Submitted for Correction with ID NBS${r.body}`),m("success"),y(!1),b(!0),I(),f(!0)):(g("Error"),b(!1),C("Failed Submitting Bank Key for Correction"),m("danger"),y(!1),f(!0),P()),O()},a=r=>{console.log(r)};console.log("remarkssssssssss",j),k(`/${N}/massAction/bankKeysSendForCorrection`,"post",l,a,e)},bo=()=>{E.filter((r,c)=>z.includes(c));const e=T,l=r=>{w(!1),D(!0),r.statusCode===200?(console.log("success"),g("Create"),C(`Bank key Submitted for Correction with ID CBM${r.body}`),m("success"),y(!1),b(!0),I(),f(!0)):(g("Error"),b(!1),C("Failed Submitting Bank Key for Correction"),m("danger"),y(!1),f(!0),P()),O()},a=r=>{console.log(r)};console.log("remarkssssssssss",j),k(`/${N}/massAction/changeBankKeysSendForCorrection`,"post",l,a,e)},So=()=>{console.log("isLoading7",q),E.filter((r,c)=>z.includes(c));const e=T,l=r=>{w(!1),D(!0),r.statusCode===200?(console.log("success"),g("Create"),C(`Bank key Submitted for Correction with ID NBM${r.body}`),m("success"),y(!1),b(!0),I(),f(!0)):(g("Error"),b(!1),C("Failed Submitting Bank Key for Correction"),m("danger"),y(!1),f(!0),P()),O()},a=r=>{console.log(r)};k(`/${N}/massAction/bankKeysSendForReview`,"post",l,a,e)},vo=()=>{E.filter((r,c)=>z.includes(c));const e=T,l=r=>{w(!1),D(!0),r.statusCode===200?(console.log("success"),g("Create"),C(`Bank key Submitted for Correction with ID CBM${r.body}`),m("success"),y(!1),b(!0),I(),f(!0)):(g("Error"),b(!1),C("Failed Submitting Bank key for Correction"),m("danger"),y(!1),f(!0),P()),O()},a=r=>{console.log(r)};k(`/${N}/massAction/changeBankKeysSendForCorrection`,"post",l,a,e)},Ao=()=>{(o==null?void 0:o.role)==="MDM Steward"&&s.processDesc==="Mass Create"||(o==null?void 0:o.role)==="MDM Steward"&&(n==null?void 0:n.requestType)==="Mass Create"?(w(!0),O(),yo()):(o==null?void 0:o.role)==="Approver"&&(s==null?void 0:s.processDesc)==="Mass Create"||(o==null?void 0:o.role)==="Approver"&&(n==null?void 0:n.requestType)==="Mass Create"?(w(!0),O(),So()):(o==null?void 0:o.role)==="MDM Steward"&&s.processDesc==="Mass Change"||(o==null?void 0:o.role)==="MDM Steward"&&(n==null?void 0:n.requestType)==="Mass Change"?(O(),bo()):((o==null?void 0:o.role)==="Approver"&&(s==null?void 0:s.processDesc)==="Mass Change"||(o==null?void 0:o.role)==="Approver"&&(n==null?void 0:n.requestType)==="Mass Change")&&(O(),vo())},qe=()=>{M(!0);let e=T;const l=r=>{r.statusCode===400?(Ne(r.body),Me(!0),ue(!0),M(!1)):(Me(!1),De(!1),g("Create"),console.log("success"),g("Create"),C("All Data has been Validated. Bank Key can be Sent for Review"),m("success"),y(!1),b(!0),I(),f(!0),w(!1),ce(!0),M(!1),Te(!1))},a=r=>{console.log(r)};k(`/${N}/massAction/validateMassBankKey`,"post",l,a,e)},Pe=()=>{M(!0);const e=E.filter((u,B)=>z.includes(B));console.log("selectedData",e);const l=e.map(u=>({...T[u==null?void 0:u.id]}));let a=T;a=l;const r=u=>{u.statusCode===400?(Ne(u.body),ue(!0),M(!1)):(De(!1),g("Create"),console.log("success"),g("Create"),C("All Data has been Validated. Bank Key can be Sent for Review"),m("success"),y(!1),b(!0),I(),f(!0),w(!1),ce(!0),M(!1),zs(!1),Te(!1))},c=u=>{console.log(u)};k(`/${N}/massAction/validateMassBankKey`,"post",r,c,a)},Oe=()=>{ue(!1)},Mo=[{field:"id",headerName:"ID",hide:!0,editable:!1,flex:1},{field:"bankKey",headerName:"Bank Key",editable:!1,flex:1},{field:"error",headerName:"Error",editable:!1,flex:1}],ko=Y==null?void 0:Y.map((e,l)=>{var a;if(console.log("erroritem",e),e.code===400)return{id:l,bankKey:e==null?void 0:e.bankKey,error:(a=e==null?void 0:e.status)==null?void 0:a.message}});return t($,{children:q===!0?t(wo,{}):p("div",{style:{backgroundColor:"#FAFCFF"},children:[p(J,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:Ls,onClose:O,children:[p(Q,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(S,{variant:"h6",children:"REMARKS"}),t(X,{sx:{width:"max-content"},onClick:O,children:t(he,{})})]}),t(x,{sx:{padding:".5rem 1rem"},children:t(ee,{children:t(se,{sx:{minWidth:400},children:t(Ds,{sx:{height:"auto"},fullWidth:!0,children:t(Bs,{sx:{backgroundColor:"#F5F5F5"},onChange:Ee,value:j,multiline:!0,placeholder:"ENTER REMARKS FOR CORRECTION",inputProps:{maxLength:254}})})})})}),p(oe,{sx:{display:"flex",justifyContent:"end"},children:[t(A,{sx:{width:"max-content",textTransform:"capitalize"},onClick:O,children:"Cancel"}),t(A,{className:"button_primary--normal",type:"save",onClick:Ao,variant:"contained",children:"Submit"})]})]}),p(J,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:_s,onClose:_,children:[p(Q,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(S,{variant:"h6",children:"REMARKS"}),t(X,{sx:{width:"max-content"},onClick:_,children:t(he,{})})]}),t(x,{sx:{padding:".5rem 1rem"},children:t(ee,{children:p(se,{sx:{minWidth:400},children:[t(Ds,{sx:{height:"auto"},fullWidth:!0,children:t(Bs,{sx:{backgroundColor:"#F5F5F5"},onChange:Ee,value:j,multiline:!0,placeholder:"ENTER REMARKS",inputProps:{maxLength:254}})}),Xs&&t(F,{children:t(S,{style:{color:"red"},children:"Please Enter Remarks"})})]})})}),p(oe,{sx:{display:"flex",justifyContent:"end"},children:[t(A,{sx:{width:"max-content",textTransform:"capitalize"},onClick:_,children:"Cancel"}),t(A,{className:"button_primary--normal",type:"save",onClick:mo,variant:"contained",children:"Submit"})]})]}),p(J,{open:Gs,fullWidth:!0,onClose:Oe,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[p(Q,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(S,{variant:"h6",color:"red",children:"Errors"}),t(X,{sx:{width:"max-content"},onClick:Oe,children:t(he,{})})]}),t(x,{sx:{padding:".5rem 1rem"},children:t(ge,{isLoading:q,width:"100%",rows:ko,columns:Mo,pageSize:10,getRowId:"id",hideFooter:!0,checkboxSelection:!1,disableSelectionOnClick:!0,status_onRowSingleClick:!0,stopPropagation_Column:"action",status_onRowDoubleClick:!0})}),t(oe,{sx:{display:"flex",justifyContent:"end"}})]}),t(Io,{sx:{color:"#fff",zIndex:e=>e.zIndex.drawer+1},open:Us,children:t(Eo,{color:"inherit"})}),Os&&t(qo,{openSnackBar:Es,alertMsg:fe,handleSnackBarClose:Js}),t(Po,{dialogState:$s,openReusableDialog:P,closeReusableDialog:Fe,dialogTitle:qs,dialogMessage:fe,handleDialogConfirm:Fe,dialogOkText:"OK",handleExtraButton:Qs,dialogSeverity:Ps}),p("div",{style:{...Oo,backgroundColor:"#FAFCFF"},children:[t(F,{container:!0,sx:$o,children:p(F,{item:!0,md:12,sx:{display:"flex",marginBottom:"0"},children:[p(F,{item:!0,md:11,sx:{display:"flex"},children:[t(F,{children:t(X,{color:"primary","aria-label":"upload picture",component:"label",sx:Lo,children:t(Ko,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{te(-1)}})})}),(s==null?void 0:s.processDesc)==="Mass Create"?p(F,{children:[t(S,{variant:"h3",children:t("strong",{children:"Create Multiple Bank Key"})}),t(S,{variant:"body2",color:"#777",children:"This view displays list of Bank keys"})]}):(n==null?void 0:n.requestType)==="Mass Create"?p(F,{children:[t(S,{variant:"h3",children:t("strong",{children:"Create Multiple Bank Key"})}),t(S,{variant:"body2",color:"#777",children:"This view displays list of Bank Keys"})]}):(n==null?void 0:n.requestType)==="Mass Change"?p(F,{children:[t(S,{variant:"h3",children:t("strong",{children:"Change Multiple Bank Key"})}),t(S,{variant:"body2",color:"#777",children:"This view displays list of Bank Keys"})]}):(s==null?void 0:s.processDesc)==="Mass Change"?p(F,{children:[t(S,{variant:"h3",children:t("strong",{children:"Change Multiple Bank Key"})}),t(S,{variant:"body2",color:"#777",children:"This view displays list of Bank Keys"})]}):""]}),t(F,{item:!0,md:1,sx:{display:"flex"},children:t(_o,{title:"Uploaded documents",arrow:!0,children:t(X,{onClick:xs,children:t(Xo,{})})})}),p(J,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none",width:"100%"}},open:js,onClose:we,children:[t(Q,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:(o==null?void 0:o.role)==="Finance"?t($,{children:t(S,{variant:"h6",children:"Add Attachment"})}):""}),t(x,{sx:{padding:".5rem 1rem"},children:p(Ns,{sx:{padding:"1rem 1rem 0rem 1rem"},children:[(o==null?void 0:o.role)==="Finance"?t($,{children:t(ee,{children:t(se,{sx:{minWidth:400},children:t(Yo,{title:"BankKey",useMetaData:!1,artifactId:de,artifactName:"BankKey"})})})}):"",t(F,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:t(S,{variant:"h6",children:t("strong",{children:"Attachments"})})}),!!le.length&&t(ge,{width:"50%",rows:le,columns:eo,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action"}),!le.length&&t(S,{variant:"body2",children:"No Attachments Found"}),t("br",{}),t(S,{variant:"h6",children:"Comments"}),!!ie.length&&t(Zo,{sx:{[`& .${Jo.root}:before`]:{flex:0,padding:0}},children:ie.map(e=>p(Qo,{children:[p(xo,{children:[t(en,{children:t(zo,{sx:{color:"#757575"}})}),t(sn,{})]}),t(on,{sx:{py:"12px",px:2},children:t(Ns,{elevation:0,sx:{border:1,borderColor:"#C4C4C4",borderRadius:"8px",width:"650px"},children:t(se,{sx:{padding:"1rem"},children:p(ee,{spacing:1,children:[t(F,{sx:{display:"flex",justifyContent:"space-between"},children:t(S,{sx:{textAlign:"right",color:" #757575",fontWeight:"500",fontSize:"12px"},children:Ts(e.createdAt).format("DD MMM YYYY")})}),t(S,{sx:{fontSize:"12px",color:" #757575",fontWeight:"500"},children:e.user}),t(S,{sx:{fontSize:"12px",color:"#1D1D1D",fontWeight:"600"},children:e.comment})]})})})})]}))}),!ie.length&&t(S,{variant:"body2",children:"No Comments Found"}),t("br",{})]})}),t(oe,{children:t(A,{onClick:we,children:"Close"})})]})]})}),t(F,{item:!0,sx:{position:"relative"},children:t(ge,{isLoading:q,width:"100%",title:"Mass Bank Key List ("+(E==null?void 0:E.length)+")",rows:E,columns:co,pageSize:10,getRowIdValue:"id",hideFooter:!1,checkboxSelection:(o==null?void 0:o.role)==="Finance"&&(s==null?void 0:s.processDesc)==="Mass Create"||(o==null?void 0:o.role)==="Finance"&&(n==null?void 0:n.requestType)==="Mass Create"||(o==null?void 0:o.role)==="Finance"&&(s==null?void 0:s.processDesc)==="Mass Change"||(o==null?void 0:o.role)==="Finance"&&(n==null?void 0:n.requestType)==="Mass Change",disableSelectionOnClick:!0,status_onRowSingleClick:!0,onRowsSelectionHandler:io,callback_onRowSingleClick:e=>{console.log("paramss",e);const l=e.row.bankKey;console.log(V,"multipleBankKeyData=====");const a=V.find(r=>r.BankKey===l);console.log(a,"yo"),te(`/masterDataCockpit/bankkey/massBKTableRequestBench/displayMultipleBankKeyRequestBench/${l}`,{state:{rowData:e.row,requestNumber:n==null?void 0:n.requestId.slice(3),tabsData:a,requestbenchRowData:n}})},stopPropagation_Column:"action",status_onRowDoubleClick:!0})})]}),jo(Ys,"Bank Key","ChangeBK")?t(Vo,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:p(Ro,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},value:ws,children:[(o==null?void 0:o.role)==="MDM Steward"&&(s==null?void 0:s.processDesc)==="Mass Create"||(o==null?void 0:o.role)==="MDM Steward"&&(n==null?void 0:n.requestType)==="Mass Create"?p($,{children:[t(A,{variant:"outlined",size:"small",sx:{button_Outlined:ne,mr:1},onClick:Z,children:"Correction"}),t(A,{variant:"contained",size:"small",sx:{...L},onClick:R,children:"Submit For Approval"})]}):(o==null?void 0:o.role)==="Approver"&&(s==null?void 0:s.processDesc)==="Mass Create"||(o==null?void 0:o.role)==="Approver"&&(n==null?void 0:n.requestType)==="Mass Create"?p($,{children:[t(A,{variant:"outlined",size:"small",sx:{button_Outlined:ne,mr:1},onClick:Z,children:"Correction"}),t(A,{variant:"contained",size:"small",sx:{...L,mr:1},onClick:qe,children:"Validate"}),t(A,{variant:"contained",size:"small",sx:{...L},onClick:R,disabled:Ae,children:"Approve"})]}):(o==null?void 0:o.role)==="Finance"&&(s==null?void 0:s.processDesc)==="Mass Create"||(o==null?void 0:o.role)==="Finance"&&(n==null?void 0:n.requestType)==="Mass Create"?p($,{children:[t(A,{variant:"contained",size:"small",sx:{...L,mr:1},onClick:Pe,disabled:!ve,children:"Validate"}),t(A,{variant:"contained",size:"small",sx:{...L},onClick:R,disabled:ke,children:"Submit For Review"})]}):"",(o==null?void 0:o.role)==="MDM Steward"&&(s==null?void 0:s.processDesc)==="Mass Change"||(o==null?void 0:o.role)==="MDM Steward"&&(n==null?void 0:n.requestType)==="Mass Change"?p($,{children:[t(A,{variant:"outlined",size:"small",sx:{button_Outlined:ne,mr:1},onClick:Z,children:"Correction"}),t(A,{variant:"contained",size:"small",sx:{...L},onClick:R,children:"Submit For Approval"})]}):(o==null?void 0:o.role)==="Approver"&&(s==null?void 0:s.processDesc)==="Mass Change"||(o==null?void 0:o.role)==="Approver"&&(n==null?void 0:n.requestType)==="Mass Change"?p($,{children:[t(A,{variant:"outlined",size:"small",sx:{button_Outlined:ne,mr:1},onClick:Z,children:"Correction"}),t(A,{variant:"contained",size:"small",sx:{...L,mr:1},onClick:qe,children:"Validate"}),t(A,{variant:"contained",size:"small",sx:{...L},onClick:R,disabled:Ae,children:"Approve"})]}):(o==null?void 0:o.role)==="Finance"&&(s==null?void 0:s.processDesc)==="Mass Change"||(o==null?void 0:o.role)==="Finance"&&(n==null?void 0:n.requestType)==="Mass Change"?p($,{children:[t(A,{variant:"contained",size:"small",sx:{...L,mr:1},onClick:Pe,disabled:!ve,children:"Validate"}),t(A,{variant:"contained",size:"small",sx:{...L},onClick:R,disabled:ke,children:"Submit For Review"})]}):""]})}):""]})})};export{Nn as default};
