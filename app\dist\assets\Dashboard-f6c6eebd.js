import{pQ as Oe,o as f,r as x,cB as _e,g as Mt,l as z,A as It,q as Z,e as xe,P as ie,s as wt,a as o,G as N,j as O,an as Lt,ao as Tt,T as U,f as kt,aq as ge,wo as Be,ar as De,at as Ot,au as _t,av as Nt,F as Ce,t as ue,b8 as Kt,O as Vt,aB as e2,wp as t2,dU as n2,br as jt,x as be,I as Ne,wq as a2,wr as r2,bs as Bt,V as Gt,aF as Ft,aw as Ge,ws as i2,M as Se,wt as s2,uz as o2,W as Ut,wu as $,wv as l2,p as Fe,cc as qe,B as W,f7 as c2,f8 as d2,f6 as u2,m as h2,n as f2,K as te,ww as le,y as we,Y as p2,a3 as m2,i as g2,$ as me,fZ as C2,v as b2,w as tt,L as nt,b7 as at,c as rt,d as it,o2 as Ue,X as x2,bQ as v2,a5 as de,bn as st}from"./index-17b8d91e.js";import{F as y2}from"./FilterField-727c53f0.js";import{e as ot}from"./TableContainer-652963eb.js";import{f as He}from"./Typography-ea689a44.js";import{G as Ht,K as E2,O as S2,Q as D2,R as A2,T as P2,U as R2,W as M2,Y as I2,y as w2,P as lt,$ as L2,a as ct,b as dt,_ as Pe,v as re,Z as ut,a0 as T2,a1 as k2,a2 as ht}from"./Button-f51b50ce.js";import{c as O2}from"./CircularProgress-85d03f44.js";import{m as se}from"./Stack-f0923c3b.js";import{u as _2}from"./useMediaQuery-6a073ac5.js";import{L as N2}from"./LargeDropdown-a9f64e3a.js";import{f as K2}from"./Card-0cf04058.js";import"./useChangeLogUpdate-f322f7d1.js";import"./AutoCompleteType-9336ea79.js";import"./dayjs.min-ce01f2c7.js";import"./AdapterDayjs-1a4a6504.js";import"./isBetween-3aeee754.js";import"./dateViewRenderers-34586552.js";import"./useSlotProps-e34e1e13.js";import"./InputAdornment-5b0053c5.js";import"./CSSTransition-30917e2c.js";import"./useMobilePicker-9978caff.js";const V2=e=>f.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"none",...e,children:f.jsxs("g",{id:"Icons /General",children:[f.jsx("path",{d:"M7.62109 2.5H3.45443C2.99419 2.5 2.62109 2.8731 2.62109 3.33333V9.16667C2.62109 9.6269 2.99419 10 3.45443 10H7.62109C8.08133 10 8.45443 9.6269 8.45443 9.16667V3.33333C8.45443 2.8731 8.08133 2.5 7.62109 2.5Z",stroke:"currentColor",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"}),f.jsx("path",{d:"M16.7871 2.5H12.6204C12.1602 2.5 11.7871 2.8731 11.7871 3.33333V5.83333C11.7871 6.29357 12.1602 6.66667 12.6204 6.66667H16.7871C17.2473 6.66667 17.6204 6.29357 17.6204 5.83333V3.33333C17.6204 2.8731 17.2473 2.5 16.7871 2.5Z",stroke:"currentColor",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"}),f.jsx("path",{d:"M11.3691 14.0996V13.4004C11.3691 12.9872 11.6877 12.6455 12.0813 12.6455C12.7597 12.6455 13.0371 12.137 12.696 11.5132C12.5011 11.1557 12.6173 10.6909 12.9583 10.4843L13.6068 10.0909C13.9029 9.90421 14.2852 10.0155 14.4613 10.3293L14.5026 10.4048C14.8399 11.0285 15.3946 11.0285 15.7357 10.4048L15.7769 10.3293C15.9531 10.0155 16.3354 9.90421 16.6315 10.0909L17.2799 10.4843C17.621 10.6909 17.7372 11.1557 17.5423 11.5132C17.2012 12.137 17.4786 12.6455 18.157 12.6455C18.5468 12.6455 18.8691 12.9832 18.8691 13.4004V14.0996C18.8691 14.5128 18.5506 14.8545 18.157 14.8545C17.4786 14.8545 17.2012 15.363 17.5423 15.9868C17.7372 16.3483 17.621 16.8091 17.2799 17.0157L16.6315 17.4091C16.3354 17.5958 15.9531 17.4845 15.7769 17.1707L15.7357 17.0952C15.3984 16.4715 14.8437 16.4715 14.5026 17.0952L14.4613 17.1707C14.2852 17.4845 13.9029 17.5958 13.6068 17.4091L12.9583 17.0157C12.6173 16.8091 12.5011 16.3443 12.696 15.9868C13.0371 15.363 12.7597 14.8545 12.0813 14.8545C11.6877 14.8545 11.3691 14.5128 11.3691 14.0996Z",stroke:"currentColor",strokeWidth:"1.25",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),f.jsx("path",{d:"M15.7441 13.75C15.7441 14.0952 15.4643 14.375 15.1191 14.375C14.774 14.375 14.4941 14.0952 14.4941 13.75C14.4941 13.4048 14.774 13.125 15.1191 13.125C15.4643 13.125 15.7441 13.4048 15.7441 13.75Z",stroke:"currentColor",strokeWidth:"1.25",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),f.jsx("path",{d:"M7.62109 13.3333H3.45443C2.99419 13.3333 2.62109 13.7063 2.62109 14.1666V16.6666C2.62109 17.1268 2.99419 17.4999 3.45443 17.4999H7.62109C8.08133 17.4999 8.45443 17.1268 8.45443 16.6666V14.1666C8.45443 13.7063 8.08133 13.3333 7.62109 13.3333Z",stroke:"currentColor",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"})]})}),j2=Oe(V2),B2=e=>f.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"none",...e,children:f.jsx("g",{id:"Icons /General",children:f.jsx("path",{d:"M17.5 12.5V15.8333C17.5 16.2754 17.3244 16.6993 17.0118 17.0118C16.6993 17.3244 16.2754 17.5 15.8333 17.5H4.16667C3.72464 17.5 3.30072 17.3244 2.98816 17.0118C2.67559 16.6993 2.5 16.2754 2.5 15.8333V12.5M5.83333 8.33333L10 12.5M10 12.5L14.1667 8.33333M10 12.5V2.5",stroke:"currentColor",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"})})}),G2=Oe(B2),F2=e=>f.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"none",...e,children:f.jsx("g",{id:"Icons /General",children:f.jsx("path",{d:"M10.0001 13.3333V17.5M13.3334 11.6667V17.5M16.6667 8.33333V17.5M18.3334 2.5L11.1284 9.705C11.0897 9.7438 11.0437 9.77459 10.9931 9.79559C10.9425 9.8166 10.8882 9.82741 10.8334 9.82741C10.7786 9.82741 10.7243 9.8166 10.6737 9.79559C10.6231 9.77459 10.5771 9.7438 10.5384 9.705L7.79508 6.96167C7.71694 6.88355 7.61098 6.83967 7.5005 6.83967C7.39001 6.83967 7.28405 6.88355 7.20591 6.96167L1.66675 12.5M3.33341 15V17.5M6.66675 11.6667V17.5",stroke:"currentColor",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"})})}),U2=Oe(F2),H2=e=>f.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"none",...e,children:f.jsx("g",{id:"Icons /General",children:f.jsx("path",{d:"M2.5 7.5H17.5M2.5 12.5H17.5M7.5 7.5V17.5M12.5 7.5V17.5M4.16667 2.5H15.8333C16.7538 2.5 17.5 3.24619 17.5 4.16667V15.8333C17.5 16.7538 16.7538 17.5 15.8333 17.5H4.16667C3.24619 17.5 2.5 16.7538 2.5 15.8333V4.16667C2.5 3.24619 3.24619 2.5 4.16667 2.5Z",stroke:"currentColor",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"})})}),z2=Oe(H2),Z2=Ht(E2)({"& .MuiSnackbarContent-root":{maxInlineSize:"fit-content",minWidth:"0",padding:"0.75rem 1rem",gap:"1rem",boxShadow:"none",backgroundColor:"var(--text-primary)"},"& .MuiSnackbarContent-message":{display:"flex",alignItems:"center",minWidth:"0",lineHeight:"1.5rem",fontWeight:400,fontSize:"0.875rem",color:"var(--contrast-text)",padding:"0"},"& .MuiSnackbarContent-action":{paddingLeft:"0"}});function Y2({icon:e,message:t,...a}){return f.jsx(Z2,{...a,message:f.jsxs("span",{style:{display:"flex",alignItems:"center"},children:[e&&f.jsx("span",{style:{marginRight:"0.5rem",display:"inline-flex"},children:e}),t]})})}const q2=Ht(S2)({borderRadius:"0.25rem",boxShadow:"var(--shadow-1)",display:"flex",overflow:"auto",flexDirection:"column",justifyContent:"center",alignItems:"flex-start",alignSelf:"stretch",fontFamily:"inherit"}),W2=function({open:e,...t}){return f.jsx(q2,{open:e,...t})};function X2(e){return f.jsx(D2,{...e})}const Q2="_customMenuItem_y78mz_25",$2={customMenuItem:Q2},Ke=({children:e,classes:t,component:a,dense:n=!1,disableGutters:s=!1,divider:r=!1,focusVisibleClassName:l,selected:d=!1,sx:p,...c})=>f.jsx(A2,{classes:t,component:a,dense:n,disableGutters:s,divider:r,focusVisibleClassName:l,selected:d,sx:{display:"flex",padding:"0.75rem 0.5rem",alignItems:"center",gap:"0.625rem",alignSelf:"stretch",color:"var(--text-primary)",overflow:"hidden",textOverflow:"ellipsis",fontSize:"0.875rem",fontFamily:"inherit",fontWeight:"400",lineHeight:"normal",letterSpacing:"0.01094rem","&:hover":{backgroundColor:"var(--background-read-only)"},"& .Mui-selected":{backgroundColor:"var(--primary-light)"},...p},...c,className:$2.customMenuItem,children:e});function J2(e){return f.jsx(P2,{...e})}function en(e){return f.jsx(R2,{...e})}function tn(e){return f.jsx(M2,{...e})}function nn(e){return f.jsx(I2,{...e})}const an="_kpiCard_1n248_1",rn="_kpiCardContent_1n248_23",sn="_loadingSkeleton_1n248_33",Ve={kpiCard:an,kpiCardContent:rn,loadingSkeleton:sn},ft=["#c7cae8","#fac4e0","#f8d7ba","#e7c7ea","#efdcd4","#d0c5ed","#ffc9bf","#bbdcf6","#fadd9c","#cce6c9"];let pt=0;const on=()=>{const e=ft[pt%ft.length];return pt+=1,e},ln=e=>{var t;const{value:a,graphName:n,isLoading:s,clickEvent:r,KPIColor:l}=e,[d,p]=x.useState(null),[c,g]=x.useState(!1),[m,S]=x.useState(!1),A=x.useRef(on()),D=w=>{p(w.currentTarget),g(h=>!h)},R=(w,h)=>{h!=="clickaway"&&S(!1)},M=w=>{if(r!=null&&r.allow)switch(r.type){case"option":D(w);break;case"navigation":r.navigate?r.navigate({...e,value:a}):S(!0);break;default:r.callBack?r.callBack(e):S(!0)}},P=c&&!!d,I=P?"transition-popper":void 0;return f.jsxs(K2,{className:Ve.kpiCard,sx:{backgroundColor:l??A.current},children:[f.jsx(J2,{onClick:M,children:f.jsx(en,{children:f.jsxs(se,{className:Ve.kpiCardContent,children:[s?f.jsx(se,{alignItems:"center",children:f.jsx(X2,{variant:"rectangular",className:Ve.loadingSkeleton})}):f.jsx(se,{alignItems:"center",children:f.jsx(He,{variant:"h5Medium",sx:{color:"var(--kpi-card-text)"},children:a||0})}),f.jsx(He,{variant:"body2",sx:{color:"var(--kpi-card-text)"},children:n||"Test"})]})})}),((t=r==null?void 0:r.options)==null?void 0:t.length)>0&&f.jsx(se,{children:f.jsx(nn,{id:I,open:P,anchorEl:d,placement:"bottom",sx:{zIndex:1e3},children:({TransitionProps:w})=>f.jsx(tn,{...w,timeout:350,children:f.jsx(f.Fragment,{children:r.options.map(h=>f.jsx(w2,{variant:"soft",style:{width:"100%",justifyContent:"start",color:"var(--kpi-card-text)"},startIcon:h.icon||null,onClick:y=>h.callBack(y,e),children:h.label},h.label))})})})}),f.jsx(Y2,{open:m,autoHideDuration:6e3,onClose:R,message:"Please provide navigate attribute, info."})]})},cn="data:image/png;base64,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",Le={MIXED:{chartKey:"line",stacked:!1,horizontal:!1,library:"APEX",isEnabled:!1,nonConvertible:["STACK_BAR","STACK_COLUMN","PIE","DONUT","STACK_AREA","STACK_LINE"]},BAR:{chartKey:"bar",stacked:!1,horizontal:!0,library:"APEX",isEnabled:!0,nonConvertible:["STACK_BAR","STACK_COLUMN","PIE","DONUT","STACK_AREA","STACK_LINE"]},COLUMN:{chartKey:"bar",stacked:!1,horizontal:!1,library:"APEX",isEnabled:!0,nonConvertible:["STACK_BAR","STACK_COLUMN","PIE","DONUT","STACK_AREA","STACK_LINE"]},LINE:{chartKey:"line",stacked:!1,horizontal:!1,library:"APEX",isEnabled:!0,nonConvertible:["STACK_BAR","STACK_COLUMN","PIE","DONUT","STACK_AREA","STACK_LINE"]},AREA:{chartKey:"area",stacked:!1,horizontal:!1,library:"APEX",isEnabled:!0,nonConvertible:["STACK_BAR","STACK_COLUMN","PIE","DONUT","STACK_AREA","STACK_LINE"]},STACK_COLUMN:{chartKey:"bar",stacked:!0,horizontal:!1,library:"APEX",isEnabled:!0,nonConvertible:["LINE","AREA","COLUMN","BAR","PIE","DONUT"]},STACK_BAR:{chartKey:"bar",stacked:!0,horizontal:!0,library:"APEX",isEnabled:!0,nonConvertible:["LINE","AREA","COLUMN","BAR","PIE","DONUT"]},STACK_AREA:{chartKey:"area",stacked:!0,horizontal:!1,library:"APEX",isEnabled:!0,nonConvertible:["LINE","AREA","COLUMN","BAR","PIE","DONUT"]},STACK_LINE:{chartKey:"line",stacked:!0,horizontal:!1,library:"APEX",isEnabled:!0,nonConvertible:["LINE","AREA","COLUMN","BAR","PIE","DONUT"]},DONUT:{chartKey:"donut",stacked:null,horizontal:null,library:"APEX",isEnabled:!0,nonConvertible:["STACK_BAR","STACK_COLUMN","STACK_AREA","STACK_LINE","LINE","AREA","COLUMN","BAR"]},PIE:{chartKey:"pie",stacked:null,horizontal:null,library:"APEX",isEnabled:!0,nonConvertible:["STACK_BAR","STACK_COLUMN","STACK_AREA","STACK_LINE","LINE","AREA","COLUMN","BAR"]}},We=["#7f86ca","#f1a957","#8bc882","#61b0ee","#fd78b6","#ceae9f","#cb7ad5","#ff826f","#f2c276"];function dn(e,t,a,n){var s,r,l,d,p,c,g,m,S,A;const D=structuredClone(t==null?void 0:t.data);(D==null?void 0:D.length)===1&&!((s=D==null?void 0:D[0])!=null&&s.name)&&(D[0].name=((r=t==null?void 0:t.graphDetails)==null?void 0:r.yTitle)||"");let R=!1,M=!1;const P=(l=Le)==null?void 0:l[e],I={chart:{type:P==null?void 0:P.chartKey,stacked:(P==null?void 0:P.stacked)??!1,fontFamily:"Roboto",width:"400px",height:"400px",toolbar:{show:!1},animations:{enabled:!0,speed:800,dynamicAnimation:{enabled:!0,speed:600}},events:{zoomed:function(){R=!0},click:function(w,h,y){var j,K,V;if(R)return R=!1,!1;if(M)return M=!1,!1;if(n!=null&&n.onClick){const L=(K=(j=y==null?void 0:y.config)==null?void 0:j.series)==null?void 0:K[y==null?void 0:y.seriesIndex],F=(V=L==null?void 0:L.data)==null?void 0:V[y==null?void 0:y.dataPointIndex];L&&F&&(n==null||n.onClick({selectedDataPoint:F,name:L==null?void 0:L.name,type:L==null?void 0:L.type,chartKey:e,dataPointIndex:y==null?void 0:y.dataPointIndex,seriesIndex:y==null?void 0:y.seriesIndex,values:t},w,h,y))}},dataPointSelection:function(w,h,y){var j,K;if(n!=null&&n.onClick){const V=(j=y==null?void 0:y.series)==null?void 0:j[y==null?void 0:y.seriesIndex],L=(K=V==null?void 0:V.data)==null?void 0:K[y==null?void 0:y.dataPointIndex];V&&L&&(M=!0,n==null||n.onClick({selectedDataPoint:L,name:V==null?void 0:V.name,type:V==null?void 0:V.type,chartKey:e,dataPointIndex:y==null?void 0:y.dataPointIndex,seriesIndex:y==null?void 0:y.seriesIndex,values:t},w,h,y))}}}},fill:{opacity:1,type:"solid"},legend:{show:!0,position:"bottom",fontSize:"10px",fontFamily:"Roboto",fontWeight:400,height:40,labels:{colors:"var(--text-secondary)"},markers:{size:5,strokeWidth:0,shape:"circle"}},plotOptions:{bar:{horizontal:(P==null?void 0:P.horizontal)||!1,borderRadius:1,columnWidth:"40%",borderRadiusApplication:"end",borderRadiusWhenStacked:"last",distributed:!(P!=null&&P.stacked||((d=t==null?void 0:t.data)==null?void 0:d.length)>1)}},xaxis:{type:"category",title:{text:P!=null&&P.horizontal?(p=t==null?void 0:t.graphDetails)==null?void 0:p.yTitle:((c=t==null?void 0:t.graphDetails)==null?void 0:c.xTitle)||((g=t==null?void 0:t.graphDetails)==null?void 0:g.xTitle),offsetY:(P==null?void 0:P.chartKey)==="bar"&&P!=null&&P.horizontal?24:6,style:{fontSize:"0.75rem",fontWeight:400,color:"var(--text-primary)"}},axisBorder:{show:!1},axisTicks:{show:!0,height:10,color:"var(--divider-secondary)"},labels:{show:!0,offsetY:0,rotate:-45,rotateAlways:!1,hideOverlappingLabels:!0,trim:!0,style:{fontSize:"0.625rem",fontWeight:400,colors:"var(--text-secondary)",fontFamily:"Roboto"}}},yaxis:{title:{text:P!=null&&P.horizontal?(m=t==null?void 0:t.graphDetails)==null?void 0:m.xTitle:((S=t==null?void 0:t.graphDetails)==null?void 0:S.yTitle)||((A=t==null?void 0:t.graphDetails)==null?void 0:A.yTitle),style:{fontSize:"0.75rem",fontWeight:400,color:"var(--text-primary)"}},labels:{style:{fontSize:"0.625rem",fontWeight:400,colors:"var(--text-secondary)",fontFamily:"Roboto"}},axisBorder:{show:!0,color:"var(--divider-secondary)"},axisTicks:{show:!0,width:6,color:"var(--divider-secondary)"}},colors:a??We,dataLabels:{enabled:!1},tooltip:{enabled:!0,followCursor:!0,marker:{show:!0}},stroke:{show:!0,width:3,lineCap:"square",curve:"smooth"},grid:{show:!0,borderColor:"var(--divider-secondary)",strokeDashArray:0,position:"back",xaxis:{lines:{show:!0}},yaxis:{lines:{show:!0}}}};return{series:D||[],options:I}}function un(e,t,a,n){var s,r;const l=(s=Le)==null?void 0:s[e],d={chart:{type:l==null?void 0:l.chartKey,toolbar:{show:!1},fontFamily:"Roboto",events:{dataPointSelection:function(p,c,g){var m,S,A,D,R,M,P;if(n!=null&&n.onClick){const I=(A=(S=(m=g==null?void 0:g.w)==null?void 0:m.config)==null?void 0:S.labels)==null?void 0:A[g==null?void 0:g.dataPointIndex],w=(M=(R=(D=g==null?void 0:g.w)==null?void 0:D.config)==null?void 0:R.series)==null?void 0:M[g==null?void 0:g.dataPointIndex];I&&w&&(n==null||n.onClick({selectedDataPoint:{x:I,y:w,id:(P=t.id)==null?void 0:P[g==null?void 0:g.dataPointIndex]},name:I,type:l==null?void 0:l.chartKey,chartKey:e,dataPointIndex:g==null?void 0:g.dataPointIndex,seriesIndex:g==null?void 0:g.seriesIndex,values:t},p,c,g))}}}},legend:{show:!0,position:"bottom",fontSize:"10px",fontFamily:"Roboto",fontWeight:400,height:40,labels:{colors:"var(--text-secondary)"},markers:{size:5,strokeWidth:0,shape:"circle"}},plotOptions:{pie:{startAngle:0,endAngle:360,expandOnClick:!0,offsetX:0,offsetY:0,customScale:1,dataLabels:{offset:0,minAngleToShowLabel:20},donut:{size:"50%"}}},tooltip:{enabled:!0,followCursor:!0,marker:{show:!0}},dataLabels:{enabled:!1},labels:(t==null?void 0:t.label)||[],colors:a??We};return{series:((r=t==null?void 0:t.series)==null?void 0:r.map(Number))||(t==null?void 0:t.series)||[],options:d}}function mt({showDownload:e=!1,showGraphName:t=!1,graphColor:a=We,values:n,isLoading:s=!1,error:r=!1,isTable:l=!1}){var d,p,c,g,m,S,A;const D=(d=n==null?void 0:n.graphDetails)==null?void 0:d.chartType,R=x.useRef(null),M=x.useRef(null),P=C=>{var i,u,G;let E;return D==="PIE"||D==="DONUT"?E=un(D,C,a):E=dn(D,C,a),(i=R.current)!=null&&i.chart&&(R.current.chart.updateOptions((u=I==null?void 0:I.graphData)==null?void 0:u.options),R.current.chart.updateSeries((G=I==null?void 0:I.graphData)==null?void 0:G.series)),E},[I,w]=x.useState({chartKey:(c=(p=Le)==null?void 0:p[D])==null?void 0:c.chartKey,graphData:P(n)}),[h,y]=x.useState("graph"),[j,K]=x.useState(null);x.useEffect(()=>{w(C=>{var i,u;return{...C,chartKey:(u=(i=Le)==null?void 0:i[D])==null?void 0:u.chartKey,graphData:P(n)}})},[s,n]);const V=()=>y(h==="graph"?"table":"graph"),L=C=>K(C.currentTarget),F=()=>K(null),T=!!j,_=C=>{M.current&&(C==="png"?T2:k2)(M.current,{backgroundColor:"white",cacheBust:!0}).then(i=>{var u;return ht.saveAs(i,`${(u=n==null?void 0:n.graphDetails)==null?void 0:u.graphName}.${C}`)}).catch(i=>console.error(`Error generating ${C}:`,i))},B=()=>{var C;let i="";return D==="PIE"||D==="DONUT"?(i=`Measure,Value
`,n==null||n.series.forEach((u,G)=>{i+=`${n==null?void 0:n.label[G]},${u}
`})):(i="Categories,"+(n==null?void 0:n.data.map(u=>u.name).join(","))+`
`,(C=n==null?void 0:n.data[0])==null||C.data.forEach((u,G)=>{i+=u.x+","+(n==null?void 0:n.data.map(E=>{var b;return(b=E.data[G])==null?void 0:b.y}).join(","))+`
`})),i},v=()=>{var C;const i=B(),u=new Blob([i],{type:"text/csv;charset=utf-8;"});ht.saveAs(u,`${(C=n==null?void 0:n.graphDetails)==null?void 0:C.graphName}.csv`)};return f.jsxs(se,{sx:{maxWidth:"100%",maxHeight:"100%",margin:"1rem"},children:[f.jsxs(se,{sx:{display:"flex",flexDirection:"row",justifyContent:"space-between",alignItems:"center",margin:"0rem 1rem"},children:[t?f.jsx(He,{variant:"subtitle2",children:(g=n==null?void 0:n.graphDetails)==null?void 0:g.graphName}):null,f.jsxs("div",{style:{justifyContent:"center",display:"flex",flexDirection:"row",alignItems:"center"},children:[l&&f.jsx(lt,{onClick:V,"aria-label":h==="graph"?"Switch to Table View":"Switch to Graph View",children:h==="graph"?f.jsx(z2,{size:"xsmall"}):f.jsx(U2,{size:"xsmall"})}),e?f.jsxs("div",{children:[f.jsx(lt,{"aria-label":"download",onClick:L,children:f.jsx(G2,{size:"xsmall"})}),f.jsxs(W2,{anchorEl:j,open:T,onClose:F,children:[h==="graph"?f.jsxs(f.Fragment,{children:[f.jsx(Ke,{sx:{width:"5rem"},onClick:()=>_("svg"),children:"SVG"}),f.jsx(Ke,{sx:{width:"5rem"},onClick:()=>_("png"),children:"PNG"})]}):null,f.jsx(Ke,{sx:{width:"5rem"},onClick:v,children:"CSV"})]})]}):null]})]}),s?f.jsx(se,{sx:{alignItems:"center",height:"400px",justifyContent:"space-evenly"},children:f.jsx(O2,{})}):r?f.jsx(se,{sx:{alignItems:"center",height:"400px",justifyContent:"space-evenly"},children:f.jsx("img",{src:cn,style:{margin:"6rem"},alt:"Graph failed to load",height:"200px",width:"200px"})}):h==="graph"?f.jsx("div",{ref:M,children:f.jsx(L2,{options:(m=I==null?void 0:I.graphData)==null?void 0:m.options,series:(S=I==null?void 0:I.graphData)==null?void 0:S.series,type:I==null?void 0:I.chartKey,height:D==="PIE"||D==="DONUT"?415:400})}):D==="PIE"||D==="DONUT"?f.jsx(ot,{sx:{height:"415px",overflow:"auto"},children:f.jsxs(ct,{size:"small",children:[f.jsx(dt,{children:f.jsxs(Pe,{children:[f.jsx(re,{children:"Measure"}),f.jsx(re,{children:"Value"})]})}),f.jsx(ut,{children:n==null?void 0:n.series.map((C,i)=>{var u;const G=(u=n==null?void 0:n.label)==null?void 0:u[i];return f.jsxs(Pe,{children:[f.jsx(re,{children:G}),f.jsx(re,{children:typeof C=="object"?JSON.stringify(C):C})]},G||`row-${i}`)})})]})}):f.jsx(ot,{sx:{height:"415px",overflow:"auto"},children:f.jsxs(ct,{size:"small",children:[f.jsx(dt,{children:f.jsxs(Pe,{children:[f.jsx(re,{children:"Categories"}),n==null?void 0:n.data.map(C=>f.jsx(re,{children:C.name},C.name))]})}),f.jsx(ut,{children:(A=n==null?void 0:n.data[0])==null?void 0:A.data.map(C=>f.jsxs(Pe,{children:[f.jsx(re,{children:C.x}),n==null?void 0:n.data.map(i=>{var u;return f.jsx(re,{children:(u=i.data.find(G=>G.x===C.x))==null?void 0:u.y},`${i.name}-${C.x}`)})]},C.x))})]})})]})}const hn=_e(Mt)(({theme:e})=>({marginTop:"0px !important",border:`1px solid ${z.primary.border}`,borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.05)","&:not(:last-child)":{borderBottom:0},"&:before":{display:"none"}})),fn=_e(It)(({theme:e})=>({minHeight:"2rem !important",margin:"0px !important",backgroundColor:z.primary.ultraLight,borderRadius:"8px 8px 0 0",transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:`${z.primary.light}20`}})),pn=({handleSearch:e,company:t,supplier:a,clearFunction:n})=>{const s=Z(i=>i.commonFilter.RequestBench),r=Z(i=>i.commonFilter.Dashboard),{t:l}=xe(),[d,p]=x.useState({}),[c,g]=x.useState({});x.useState([]),x.useState([]),x.useState([]);const[m,S]=x.useState([]),[A,D]=x.useState([]),[R,M]=x.useState([]);x.useState("defaultOption");const[P,I]=x.useState([]),[w,h]=x.useState([]),[y,j]=x.useState([...r.dashboardDate]);Z(i=>i.masterData),x.useEffect(()=>{var i=w.map(G=>G).join("$^$");let u={...s,selectedRegion:i};L(ie({module:"Dashboard",filterData:u}))},[w]),x.useEffect(()=>{let i="";i={...r,dashBoardModuleName:P},L(ie({module:"Dashboard",filterData:i}))},[P]),x.useEffect(()=>{let i=[];R==null||R.map(u=>{i.push(u==null?void 0:u.code)}),L(ie({module:"Dashboard",filterData:{...r,selectedusersId:i}}))},[R]),x.useEffect(()=>{let i=[];A==null||A.map(u=>{i.push(u==null?void 0:u.lable)}),L(ie({module:"Dashboard",filterData:{...r,selectedRequestStatus:i}}))},[A]),x.useEffect(()=>{let i=[];m==null||m.map(u=>{i.push(u==null?void 0:u.lable)}),L(ie({module:"Dashboard",filterData:{...r,selectedRequestType:i}}))},[m]);const K=new Date,V=new Date;V.setDate(V.getDate()-7);const L=wt();Z(i=>i.userManagement.userData),x.useState([...r.dashboardDate]);const F=Z(i=>i.appSettings),T=i=>{L(ie({module:"Dashboard",filterData:{...r,dashboardDate:i}}))};x.useEffect(()=>{if(r!=null&&r.dashboardDate){const i=new Date(r==null?void 0:r.dashboardDate[0]),u=new Date(r==null?void 0:r.dashboardDate[1]);j([i,u])}},[r==null?void 0:r.dashboardDate]);const _=[{type:"singleSelect",filterName:"companyCode",filterData:d,filterTitle:"Company Code"},{type:"singleSelect",filterName:"vendorNo",filterData:c,filterTitle:"Business Partner"},{type:"dateRange",filterName:"dashboardDate",filterTitle:"Date Range"}],B=["Material","Profit Center","Cost Center","General Ledger"],v=["US","EUR"],C=()=>{w.length===(v==null?void 0:v.length)?h([]):h(v)};return o(Ce,{children:o(N,{item:!0,md:12,children:O(hn,{defaultExpanded:!1,children:[O(fn,{expandIcon:o(Lt,{sx:{fontSize:"1.25rem",color:z.primary.main}}),"aria-controls":"panel1a-content",id:"panel1a-header",children:[o(Tt,{sx:{fontSize:"1.25rem",marginRight:1,color:z.primary.main}}),o(U,{sx:{fontSize:"0.875rem",fontWeight:600,color:z.primary.dark},children:l("Filter Dashboard")})]}),O(kt,{children:[O(N,{container:!0,rowSpacing:1,spacing:2,children:[O(N,{item:!0,md:2,children:[o(U,{sx:ge,children:l("Module")}),o(Be,{options:[...B.filter(i=>i!=="Select All").sort((i,u)=>i.localeCompare(u))],value:P,onChange:i=>{var u;i.length>0&&((u=i[i.length-1])==null?void 0:u.label)==="Select All"?handleSelectAllModule():I(i)},placeholder:"Select Module Name"})]}),O(N,{item:!0,md:2,children:[o(U,{sx:ge,children:l("Region")}),o(Be,{options:[...v.filter(i=>i!=="Select All").sort((i,u)=>i.localeCompare(u))],value:w,onChange:i=>{var u;i.length>0&&((u=i[i.length-1])==null?void 0:u.label)==="Select All"?C():h(i)},placeholder:"Select Region"})]}),O(N,{item:!0,md:2,children:[o(U,{sx:ge,children:l("Date Range")}),o(De,{fullWidth:!0,sx:{padding:0,height:"37px"},children:o(Ot,{dateAdapter:_t,children:o(Nt,{handleDate:T,cleanDate:!1,date:y})})})]}),_==null?void 0:_.map(i=>i!=null&&i.hideFilter?o(Ce,{}):o(N,{item:!0,md:3,children:o(y2,{type:i.type,filterName:i.filterName,filterData:i.filterData,moduleName:"Dashboard",onChangeFilter:i.onChangeFilter,filterTitle:i.filterTitle})},i.filterTitle))]}),o(N,{container:!0,style:{display:"flex",justifyContent:"flex-end"},children:O(N,{item:!0,style:{display:"flex",justifyContent:"space-around"},children:[o(ue,{variant:"outlined",sx:{...Kt},onClick:()=>{j([V,K]),D([]),S([]),h([]),M([]),I([]),n(),L(Vt({module:"Dashboard",days:F.range})),L(ie({module:"Dashboard",filterData:{...r,selectedRequestType:[],selectedRequestStatus:[],selectedusersId:[],selectedRegion:"",dashboardDate:[V,K]}}))},children:l("Clear")}),o(ue,{variant:"contained",sx:{...e2,...t2},onClick:e,children:l("Apply")})]})})]})]})})})},mn=()=>o(N,{md:12,sx:{paddingTop:"1%"},children:o(pn,{})}),gn="data:image/svg+xml,%3csvg%20width='350'%20height='230'%20viewBox='0%200%20350%20230'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M137.416%2059.0001L198.175%2059.1176L223.314%2084.3395L223.181%20143.452C220.557%20142.408%20217.695%20141.835%20214.699%20141.835C202.011%20141.835%20191.725%20152.121%20191.725%20164.809C191.725%20177.379%20201.821%20187.592%20214.346%20187.78L137.158%20187.632C136.084%20187.631%20135.021%20187.419%20134.029%20187.006C133.038%20186.594%20132.137%20185.99%20131.379%20185.229C130.621%20184.468%20130.021%20183.565%20129.613%20182.572C129.205%20181.579%20128.996%20180.514%20129%20179.441L129.219%2067.144C129.221%2066.0707%20129.436%2065.0085%20129.849%2064.018C130.263%2063.0276%20130.867%2062.1285%20131.629%2061.372C132.39%2060.6156%20133.293%2060.0167%20134.286%2059.6097C135.279%2059.2027%20136.343%2058.9955%20137.416%2059.0001ZM214.965%20187.781C215.045%20187.781%20215.125%20187.78%20215.205%20187.777C215.125%20187.779%20215.045%20187.78%20214.965%20187.781Z'%20fill='url(%23paint0_linear_12419_6513)'/%3e%3cpath%20d='M198.175%2059.1176L198.53%2058.7646L198.383%2058.618L198.176%2058.6176L198.175%2059.1176ZM137.416%2059.0001L137.414%2059.5001L137.415%2059.5001L137.416%2059.0001ZM223.314%2084.3395L223.814%2084.3406L223.815%2084.1334L223.669%2083.9865L223.314%2084.3395ZM223.181%20143.452L222.996%20143.916L223.679%20144.188L223.681%20143.453L223.181%20143.452ZM214.346%20187.78L214.345%20188.28L214.354%20187.28L214.346%20187.78ZM137.158%20187.632L137.159%20187.132H137.159L137.158%20187.632ZM131.379%20185.229L131.734%20184.876H131.734L131.379%20185.229ZM129.613%20182.572L129.15%20182.762L129.613%20182.572ZM129%20179.441L129.5%20179.442L129.5%20179.442L129%20179.441ZM129.219%2067.144L128.719%2067.1428L128.719%2067.143L129.219%2067.144ZM129.849%2064.018L129.388%2063.8254H129.388L129.849%2064.018ZM131.629%2061.372L131.981%2061.7267H131.981L131.629%2061.372ZM134.286%2059.6097L134.476%2060.0724L134.286%2059.6097ZM214.965%20187.781L214.959%20187.281L214.965%20188.281L214.965%20187.781ZM215.205%20187.777L215.22%20188.277L215.194%20187.277L215.205%20187.777ZM198.176%2058.6176L137.417%2058.5001L137.415%2059.5001L198.174%2059.6176L198.176%2058.6176ZM223.669%2083.9865L198.53%2058.7646L197.821%2059.4705L222.96%2084.6925L223.669%2083.9865ZM223.681%20143.453L223.814%2084.3406L222.814%2084.3384L222.681%20143.451L223.681%20143.453ZM214.699%20142.335C217.63%20142.335%20220.43%20142.896%20222.996%20143.916L223.366%20142.987C220.683%20141.921%20217.759%20141.335%20214.699%20141.335V142.335ZM192.225%20164.809C192.225%20152.397%20202.287%20142.335%20214.699%20142.335V141.335C201.734%20141.335%20191.225%20151.845%20191.225%20164.809H192.225ZM214.354%20187.28C202.101%20187.096%20192.225%20177.106%20192.225%20164.809H191.225C191.225%20177.653%20201.54%20188.088%20214.339%20188.28L214.354%20187.28ZM137.157%20188.132L214.345%20188.28L214.347%20187.28L137.159%20187.132L137.157%20188.132ZM133.838%20187.468C134.89%20187.906%20136.018%20188.131%20137.158%20188.132L137.159%20187.132C136.151%20187.131%20135.152%20186.932%20134.221%20186.545L133.838%20187.468ZM131.025%20185.582C131.829%20186.39%20132.785%20187.031%20133.838%20187.468L134.221%20186.545C133.29%20186.158%20132.445%20185.591%20131.734%20184.876L131.025%20185.582ZM129.15%20182.762C129.584%20183.816%20130.221%20184.775%20131.025%20185.582L131.734%20184.876C131.022%20184.162%20130.459%20183.314%20130.075%20182.382L129.15%20182.762ZM128.5%20179.439C128.496%20180.579%20128.717%20181.708%20129.15%20182.762L130.075%20182.382C129.692%20181.449%20129.497%20180.45%20129.5%20179.442L128.5%20179.439ZM128.719%2067.143L128.5%20179.44L129.5%20179.442L129.719%2067.145L128.719%2067.143ZM129.388%2063.8254C128.949%2064.8765%20128.722%2066.0038%20128.719%2067.1428L129.719%2067.1451C129.721%2066.1376%20129.922%2065.1404%20130.31%2064.2107L129.388%2063.8254ZM131.276%2061.0173C130.468%2061.8201%20129.826%2062.7743%20129.388%2063.8254L130.31%2064.2107C130.699%2063.2809%20131.266%2062.4368%20131.981%2061.7267L131.276%2061.0173ZM134.097%2059.1471C133.043%2059.579%20132.084%2060.2146%20131.276%2061.0173L131.981%2061.7267C132.696%2061.0166%20133.544%2060.4544%20134.476%2060.0724L134.097%2059.1471ZM137.418%2058.5001C136.279%2058.4953%20135.151%2058.7151%20134.097%2059.1471L134.476%2060.0724C135.408%2059.6903%20136.407%2059.4958%20137.414%2059.5001L137.418%2058.5001ZM214.965%20188.281C215.05%20188.281%20215.135%20188.28%20215.22%20188.277L215.189%20187.278C215.114%20187.28%20215.039%20187.281%20214.964%20187.281L214.965%20188.281ZM215.194%20187.277C215.116%20187.279%20215.037%20187.28%20214.959%20187.281L214.97%20188.281C215.052%20188.28%20215.134%20188.279%20215.216%20188.277L215.194%20187.277Z'%20fill='white'/%3e%3cpath%20d='M63.3267%2078.8821L61.5771%2074.0909L62.5083%2075.1425V73.5061L64.4931%2076.5479L62.7435%2065.6732L65.0762%2071.6339L65.3066%2066.3759L65.6594%2068.1302L66.125%2062.1646L66.9385%2071.285L68.1048%2068.1302L67.8745%2072.2187L71.0207%2062.8673L69.5064%2074.5577L71.7215%2072.9214L70.4376%2075.0246L72.0695%2073.855L70.2121%2079.2359L63.4443%2079.3538'%20fill='url(%23paint1_linear_12419_6513)'/%3e%3cpath%20d='M70.8099%2090.6904H63.5177L61.5525%2078.027H72.7701L70.8099%2090.6904Z'%20fill='%23F0EDF7'/%3e%3cpath%20d='M90.7654%2090.6069H53.9712V188.759H90.7654V90.6069Z'%20fill='white'%20stroke='%23EBEBF2'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M88.2612%2093.8255H56.1079V117.467H88.2612V93.8255Z'%20stroke='%23EBEBF2'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M78.6118%20103.501H65.7524V107.801H78.6118V103.501Z'%20fill='%23ADA9BF'%20stroke='%23EBEBF2'%20stroke-miterlimit='10'/%3e%3cpath%20d='M88.2612%20121.413H56.1079V145.054H88.2612V121.413Z'%20stroke='%23EBEBF2'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M78.6118%20131.084H65.7524V135.383H78.6118V131.084Z'%20fill='%23ADA9BF'%20stroke='%23EBEBF2'%20stroke-miterlimit='10'/%3e%3cpath%20d='M88.2612%20148.99H56.1079V172.631H88.2612V148.99Z'%20stroke='%23EBEBF2'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M78.6118%20158.661H65.7524V162.961H78.6118V158.661Z'%20fill='%23ADA9BF'%20stroke='%23EBEBF2'%20stroke-miterlimit='10'/%3e%3cpath%20d='M54.7358%20141.025C54.2163%20140.946%2053.6871%20140.926%2053.1578%20140.97C53.0941%20140.97%2053.099%20141.079%2053.1578%20141.084C53.6772%20141.084%2054.2016%20141.059%2054.7211%20141.084C54.7407%20141.084%2054.7505%20141.059%2054.7456%20141.044C54.7456%20141.039%2054.7407%20141.029%2054.7358%20141.025Z'%20fill='white'/%3e%3cpath%20d='M49.046%20151.319C48.208%20151.334%2047.3847%20151.501%2046.6055%20151.806V151.845C47.4288%20151.649%2048.2276%20151.57%2049.0411%20151.432C49.0705%20151.418%2049.0852%20151.383%2049.0754%20151.349C49.0705%20151.334%2049.0607%20151.324%2049.046%20151.319Z'%20fill='white'/%3e%3cpath%20d='M47.3406%20147.762C46.9926%20147.762%2046.6447%20147.791%2046.3065%20147.855C46.2918%20147.86%2046.282%20147.88%2046.2869%20147.899C46.2869%20147.909%2046.2967%20147.914%2046.3065%20147.919C46.6545%20147.939%2046.9975%20147.919%2047.3406%20147.875C47.37%20147.875%2047.3945%20147.85%2047.3945%20147.821C47.3945%20147.791%2047.37%20147.767%2047.3406%20147.767V147.762Z'%20fill='white'/%3e%3cpath%20d='M272.355%20163.806C272.497%20160.115%20272.448%20156.415%20272.203%20152.73C271.659%20145.118%20270.502%20137.56%20268.738%20130.135C267.773%20125.958%20266.582%20121.845%20265.342%20117.742C265.303%20117.614%20265.107%20117.649%20265.141%20117.781C268.978%20132.258%20272.124%20147.192%20271.904%20162.238C271.845%20166.44%20271.355%20170.582%20270.958%20174.769C270.958%20174.882%20271.13%20174.926%20271.154%20174.813C271.943%20171.221%20272.203%20167.467%20272.35%20163.811L272.355%20163.806Z'%20fill='%23EBEBEB'/%3e%3cpath%20d='M265.141%20117.762C265.141%20117.762%20259.334%20132.543%20260.632%20136.002C261.931%20139.462%20264.612%20140.204%20264.612%20140.204C264.612%20140.204%20261.059%20145.29%20263.543%20150.061C266.028%20154.833%20271.541%20154.607%20271.924%20160.017C271.914%20160.042%20272.218%20137.428%20265.141%20117.762Z'%20fill='url(%23paint2_linear_12419_6513)'/%3e%3cpath%20d='M271.281%20150.705C271.291%20150.681%20271.291%20150.651%20271.281%20150.627C270.522%20140.533%20268.63%20130.563%20265.651%20120.892C265.651%20120.867%20265.592%20120.872%20265.602%20120.892C266.753%20125.221%20267.768%20129.565%20268.65%20133.934C267.871%20133.192%20266.915%20132.661%20265.876%20132.381C265.876%20132.381%20265.847%20132.405%20265.876%20132.41C266.969%20132.813%20267.949%20133.467%20268.743%20134.322C268.91%20135.182%20269.076%20136.037%20269.233%20136.897C267.934%20135.442%20266.136%20134.72%20264.332%20134.032C264.322%20134.037%20264.318%20134.052%20264.322%20134.061C264.322%20134.066%20264.327%20134.071%20264.332%20134.071C266.209%20134.833%20268.076%20135.821%20269.351%20137.442C269.792%20139.894%20270.179%20142.356%20270.517%20144.823C269.581%20143.84%20268.375%20143.147%20267.052%20142.833C267.008%20142.833%20266.988%20142.882%20267.028%20142.892C268.385%20143.295%20269.596%20144.081%20270.517%20145.162H270.541C270.654%20146.027%20270.767%20146.897%20270.87%20147.771C270.144%20147.157%20269.326%20146.661%20268.444%20146.307C268.419%20146.307%20268.405%20146.332%20268.444%20146.342C269.346%20146.735%20270.169%20147.28%20270.88%20147.958H270.909C271.002%20148.789%20271.1%20149.614%20271.183%20150.445C269.875%20148.857%20268.179%20147.644%20266.258%20146.916C266.244%20146.916%20266.229%20146.931%20266.229%20146.946C266.229%20146.961%20266.244%20146.975%20266.258%20146.975C268.155%20147.811%20269.821%20149.098%20271.11%20150.72C271.139%20150.754%20271.188%20150.769%20271.232%20150.759C271.36%20152.032%20271.473%20153.305%20271.566%20154.577C271.566%20154.597%20271.595%20154.597%20271.595%20154.577C271.497%20153.295%20271.394%20152.002%20271.281%20150.695V150.705Z'%20fill='white'/%3e%3cpath%20d='M267.317%20146.577C266.508%20146.111%20265.621%20145.806%20264.695%20145.678C264.666%20145.678%20264.661%20145.722%20264.695%20145.727C265.597%20145.909%20266.469%20146.214%20267.288%20146.636C267.322%20146.656%20267.351%20146.592%20267.317%20146.577Z'%20fill='white'/%3e%3cpath%20d='M287.62%20136.263C285.165%20137.963%20282.911%20139.939%20280.897%20142.155C278.824%20144.499%20277.221%20147.221%20276.187%20150.174C273.982%20156.332%20272.796%20162.808%20272.673%20169.349C272.6%20172.971%20272.948%20176.587%20273.707%20180.13C273.722%20180.184%20273.776%20180.219%20273.83%20180.209C273.879%20180.194%20273.913%20180.145%20273.908%20180.096C272.963%20173.845%20272.997%20167.486%20274.016%20161.246C275.016%20155.133%20276.579%20148.479%20280.456%20143.511C282.568%20140.803%20285.2%20138.676%20287.802%20136.474C287.939%20136.351%20287.763%20136.165%20287.62%20136.263Z'%20fill='%23EBEBEB'/%3e%3cpath%20d='M273.526%20164.941C274.923%20162.676%20276.565%20160.568%20278.427%20158.661C281.333%20155.747%20283.778%20153.678%20284.161%20151.752C284.543%20149.826%20281.25%20148.405%20281.25%20148.405C281.25%20148.405%20285.268%20149.133%20286.371%20147.437C287.473%20145.747%20288.003%20135.943%20288.003%20135.943C288.003%20135.943%20280.691%20141.182%20277.28%20148.523C273.869%20155.865%20273.531%20164.941%20273.531%20164.941H273.526Z'%20fill='url(%23paint3_linear_12419_6513)'/%3e%3cpath%20d='M285.469%20138.459C282.391%20141.486%20279.294%20144.72%20277.471%20148.705C276.849%20150.081%20276.31%20151.486%20275.854%20152.926C275.761%20153.216%20275.668%20153.506%20275.579%20153.796C275.574%20153.811%20275.574%20153.826%20275.579%20153.84C274.717%20156.818%20274.026%20159.84%20273.501%20162.897C273.501%20162.916%20273.526%20162.921%20273.531%20162.897C273.849%20161.369%20274.192%20159.84%20274.585%20158.287C274.599%20158.287%20274.614%20158.287%20274.629%20158.287C275.236%20158.027%20275.878%20157.865%20276.535%20157.801C276.579%20157.801%20276.574%20157.722%20276.535%20157.722C275.888%20157.752%20275.246%20157.87%20274.629%20158.071C274.981%20156.686%20275.369%20155.3%20275.8%20153.934C277.221%20153.59%20278.677%20153.398%20280.137%20153.364M280.137%20153.339C278.706%20153.27%20277.27%20153.393%20275.868%20153.703C275.947%20153.472%20276.025%20153.236%20276.099%20153.005C277.05%20152.779%20278.015%20152.612%20278.985%20152.514V152.484C278.035%20152.504%20277.089%20152.617%20276.158%20152.828C276.54%20151.649%20276.986%20150.484%20277.461%20149.354C277.858%20148.43%20278.319%20147.536%20278.843%20146.681C279.613%20146.489%20280.397%20146.346%20281.186%20146.263V146.224C280.451%20146.17%20279.706%20146.243%20278.995%20146.44C279.377%20145.816%20279.784%20145.206%20280.22%20144.612C281.862%20144.248%20283.523%20143.909%20285.204%20144.229C285.219%20144.224%20285.224%20144.209%20285.219%20144.194C285.219%20144.189%20285.209%20144.184%20285.204%20144.179C283.607%20143.757%20282.024%20143.998%20280.436%20144.312C280.607%20144.081%20280.789%20143.855%20280.965%20143.629C281.95%20143.324%20282.979%20143.157%20284.013%20143.143M284.013%20143.108C283.053%20143.02%20282.087%20143.108%20281.156%20143.364C282.514%20141.673%20284.008%20140.086%20285.493%20138.518C285.596%20138.43%20285.542%20138.391%20285.469%20138.459L284.013%20143.108Z'%20fill='white'/%3e%3cpath%20d='M252.086%20146.661C254.467%20147.472%20256.746%20148.548%20258.883%20149.875C261.103%20151.29%20263.024%20153.118%20264.548%20155.265C267.753%20159.737%20270.203%20164.7%20271.806%20169.968C272.703%20172.882%20273.252%20175.894%20273.453%20178.936C273.443%20178.985%20273.394%20179.015%20273.35%20179.005C273.315%20179%20273.291%20178.971%20273.281%20178.936C272.61%20173.658%20271.115%20168.518%20268.856%20163.708C266.636%20158.99%20263.837%20153.973%20259.554%20150.848C257.221%20149.147%20254.605%20148.032%20251.988%20146.857C251.85%20146.813%20251.943%20146.617%20252.081%20146.661H252.086Z'%20fill='%23EBEBEB'/%3e%3cpath%20d='M270.1%20166.602C268.444%20165.093%20266.631%20163.772%20264.69%20162.656C261.666%20160.971%20259.211%20159.865%20258.456%20158.396C257.707%20156.926%20260.039%20155.015%20260.039%20155.015C260.039%20155.015%20256.952%20156.543%20255.673%20155.418C254.394%20154.292%20251.703%20146.499%20251.703%20146.499C251.703%20146.499%20258.829%20149.039%20263.274%20154.189C267.719%20159.344%20270.096%20166.602%20270.096%20166.602H270.1Z'%20fill='url(%23paint4_linear_12419_6513)'/%3e%3cpath%20d='M269.728%20164.99C266.165%20158.283%20261.407%20152.214%20254.899%20148.229C256.599%20149.324%20258.197%20150.577%20259.667%20151.973C258.339%20151.899%20257.021%20152.179%20255.835%20152.784C255.825%20152.789%20255.82%20152.803%20255.825%20152.813C255.825%20152.818%20255.83%20152.823%20255.835%20152.823C257.094%20152.346%20258.432%20152.12%20259.775%20152.155C259.79%20152.155%20259.804%20152.155%20259.819%20152.155C260.049%20152.371%20260.275%20152.597%20260.505%20152.823C259.814%20152.853%20259.128%20152.946%20258.457%20153.093V153.123C259.167%20152.985%20259.892%20152.926%20260.618%20152.941C261.809%20154.15%20262.936%20155.423%20263.98%20156.759C263.499%20156.69%20263.014%20156.725%20262.549%20156.862C262.534%20156.862%20262.524%20156.872%20262.524%20156.887C262.524%20156.902%20262.534%20156.912%20262.549%20156.912C263.053%20156.853%20263.563%20156.848%20264.073%20156.892C264.269%20157.143%20264.455%20157.388%20264.656%20157.654C263.2%20157.56%20261.74%20157.703%20260.334%20158.071C260.309%20158.071%20260.334%20158.111%20260.334%20158.106C261.804%20157.796%20263.303%20157.683%20264.803%20157.762C264.984%20158.002%20265.156%20158.248%20265.337%20158.494C264.47%20158.474%20263.602%20158.572%20262.764%20158.794C262.75%20158.799%20262.745%20158.813%20262.75%20158.828C262.75%20158.833%20262.759%20158.838%20262.764%20158.843C263.641%20158.656%20264.538%20158.582%20265.43%20158.636C266.19%20159.693%20266.93%20160.779%20267.636%20161.87C267.253%20161.801%20266.861%20161.826%20266.494%20161.939M266.494%20161.988C266.9%20161.958%20267.312%20161.978%20267.719%20162.047C268.356%20163.034%20268.964%20164.032%20269.557%20165.044C269.65%20165.143%20269.787%20165.079%20269.738%20164.99L266.499%20161.983L266.494%20161.988Z'%20fill='white'/%3e%3cpath%20d='M264.185%20159.428C263.484%20159.378%20262.779%20159.467%20262.112%20159.683C262.112%20159.683%20262.112%20159.717%20262.112%20159.713C262.793%20159.595%20263.485%20159.526%20264.175%20159.496C264.225%20159.496%20264.234%20159.428%20264.185%20159.423V159.428Z'%20fill='white'/%3e%3cpath%20d='M279.294%20188.784H263.21L265.504%20171.934L266.087%20167.614H276.413L277.001%20171.934L279.294%20188.784Z'%20fill='%23F0EDF7'/%3e%3cpath%20d='M277.001%20171.934H265.504L266.087%20167.614H276.413L277.001%20171.934Z'%20fill='%23D9D3EA'/%3e%3cpath%20d='M277.893%20166.12H264.612V169.781H277.893V166.12Z'%20fill='%23F0EDF7'/%3e%3cpath%20d='M19%20188.509L56.25%20188.391L93.5%20188.346L168%20188.263L242.5%20188.346L279.75%20188.391L317%20188.509L279.75%20188.631L242.5%20188.671L168%20188.754L93.5%20188.671L56.25%20188.627L19%20188.509Z'%20fill='%23D9D9F4'/%3e%3cg%20opacity='0.93'%3e%3cpath%20d='M97.0649%2041C89.2974%2041%2083%2047.3145%2083%2055.1032C83%2062.8919%2089.2974%2069.2064%2097.0649%2069.2064C104.832%2069.2064%20111.13%2062.8919%20111.13%2055.1032C111.13%2047.3145%20104.832%2041%2097.0649%2041ZM97.0649%2066.4349C90.8215%2066.4349%2085.764%2061.3587%2085.7689%2055.0983C85.7689%2048.8378%2090.8313%2043.7666%2097.0747%2043.7715C103.313%2043.7715%20108.371%2048.8477%20108.371%2055.1032C108.381%2061.3587%20103.333%2066.4398%2097.0943%2066.4496C97.0845%2066.4496%2097.0747%2066.4496%2097.0649%2066.4496V66.4349Z'%20fill='%23F0EDF7'/%3e%3cpath%20d='M98.0694%2055.1179C98.0842%2055.6732%2097.6431%2056.1351%2097.0893%2056.1499C96.5355%2056.1646%2096.0749%2055.7224%2096.0602%2055.1671C96.0455%2054.6118%2096.4865%2054.1499%2097.0403%2054.1351C97.0501%2054.1351%2097.055%2054.1351%2097.0648%2054.1351C97.6088%2054.1351%2098.0547%2054.5725%2098.0694%2055.1179Z'%20fill='%23F0EDF7'/%3e%3cpath%20d='M93.6982%2060.2334L97.065%2055.1179L102.755%2059.8403'%20stroke='%23F0EDF7'%20stroke-miterlimit='10'/%3e%3c/g%3e%3cpath%20d='M128.944%2062.5124L120.215%2059.6301'%20stroke='black'/%3e%3cpath%20d='M132.995%2059.1223L134.128%2050'%20stroke='black'/%3e%3cpath%20d='M130.394%2059.4708L123.799%2053.068'%20stroke='black'/%3e%3cpath%20d='M151.766%20124.273C155.074%20124.075%20157.582%20121.01%20157.367%20117.426C157.152%20113.843%20154.297%20111.098%20150.989%20111.296C147.681%20111.494%20145.174%20114.56%20145.389%20118.143C145.603%20121.727%20148.459%20124.471%20151.766%20124.273Z'%20fill='%23263238'/%3e%3cpath%20d='M200.679%20121.335C203.986%20121.137%20206.494%20118.072%20206.279%20114.488C206.065%20110.905%20203.209%20108.161%20199.902%20108.359C196.594%20108.557%20194.086%20111.622%20194.301%20115.206C194.515%20118.789%20197.371%20121.533%20200.679%20121.335Z'%20fill='%23263238'/%3e%3cpath%20d='M185.42%20123.26L185.029%20122.64C184.995%20122.584%20181.333%20116.941%20174.674%20117.34C168.615%20117.703%20167.43%20123.292%20167.404%20123.531L167.264%20124.24L165.774%20123.991L165.909%20123.277C165.905%20123.209%20167.307%20116.33%20174.587%20115.894C182.16%20115.441%20186.164%20121.624%20186.331%20121.89L186.723%20122.505L185.42%20123.26Z'%20fill='%23263238'/%3e%3cpath%20opacity='0.22'%20d='M197.565%2060L197.533%2077.0053C197.531%2078.0775%20197.739%2079.1396%20198.147%2080.1312C198.555%2081.1227%20199.154%2082.0242%20199.911%2082.7841C200.667%2083.544%20201.566%2084.1475%20202.555%2084.56C203.545%2084.9726%20204.606%2085.1861%20205.678%2085.1885L222.704%2085.222L197.565%2060Z'%20fill='%23407BFF'/%3e%3cpath%20d='M232%20182L226.978%20176.978'%20stroke='%234B5768'%20stroke-width='4'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M214.065%20179.13C222.386%20179.13%20229.13%20172.386%20229.13%20164.065C229.13%20155.745%20222.386%20149%20214.065%20149C205.745%20149%20199%20155.745%20199%20164.065C199%20172.386%20205.745%20179.13%20214.065%20179.13Z'%20stroke='%234B5768'%20stroke-width='4'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cdefs%3e%3clinearGradient%20id='paint0_linear_12419_6513'%20x1='180.324'%20y1='57.9809'%20x2='171.927'%20y2='189.862'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23CACAFC'/%3e%3cstop%20offset='1'%20stop-color='%237D7DD6'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint1_linear_12419_6513'%20x1='61.5771'%20y1='70.7592'%20x2='72.0695'%20y2='70.7592'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23FFEFE9'/%3e%3cstop%20offset='0.34'%20stop-color='%23E1E1D0'/%3e%3cstop%20offset='0.76'%20stop-color='%23C3D3B8'/%3e%3cstop%20offset='0.99'%20stop-color='%23B8CEAF'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint2_linear_12419_6513'%20x1='260.446'%20y1='138.892'%20x2='271.924'%20y2='138.892'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23FFEFE9'/%3e%3cstop%20offset='0.34'%20stop-color='%23E1E1D0'/%3e%3cstop%20offset='0.76'%20stop-color='%23C3D3B8'/%3e%3cstop%20offset='0.99'%20stop-color='%23B8CEAF'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint3_linear_12419_6513'%20x1='273.526'%20y1='150.44'%20x2='287.998'%20y2='150.44'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23FFEFE9'/%3e%3cstop%20offset='0.34'%20stop-color='%23E1E1D0'/%3e%3cstop%20offset='0.76'%20stop-color='%23C3D3B8'/%3e%3cstop%20offset='0.99'%20stop-color='%23B8CEAF'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint4_linear_12419_6513'%20x1='251.708'%20y1='156.548'%20x2='270.1'%20y2='156.548'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23FFEFE9'/%3e%3cstop%20offset='0.34'%20stop-color='%23E1E1D0'/%3e%3cstop%20offset='0.76'%20stop-color='%23C3D3B8'/%3e%3cstop%20offset='0.99'%20stop-color='%23B8CEAF'/%3e%3c/linearGradient%3e%3c/defs%3e%3c/svg%3e",gt=e=>z.colorPalleteDashboard[e]||z.colorPalleteDashboard.default,Cn=e=>e===$.PIE||e===$.DONUT,Ct=e=>l2.includes(e),bt=(e,t)=>{var a,n;if(!e)return e;if(e.data&&Array.isArray(e.data)){const s=((n=(a=e.data[0])==null?void 0:a.data)==null?void 0:n.map(l=>l.x))||[],r=e.data.map(l=>({name:l.name,data:l.data.map(d=>d.y)}));return{...e,categories:s,series:r,isStacked:Ct(t),graphDetails:{...e.graphDetails,chartType:t}}}return{...e,isStacked:Ct(t),graphDetails:{...e.graphDetails,chartType:t}}},bn=({title:e,data:t=[],handleOpenDialog:a=()=>{},dragHandleProps:n={}})=>{var I,w;const s=n2();_2(s.breakpoints.up("md"));const[r,l]=x.useState(!1),[d,p]=x.useState(null),{t:c}=xe(),g=(I=t==null?void 0:t.graphDetails)==null?void 0:I.chartType;x.useMemo(()=>{r&&!d&&p(g)},[r,g,d]);const m=h=>!!h&&Object.keys(h).length>0,S=()=>{l(!0),p(g)},A=()=>{l(!1),p(null)},D=h=>{p(h.target.value)},R=x.useMemo(()=>{var y;if(!d||!t)return t;const h=bt(t,d);return(y=h==null?void 0:h.graphDetails)!=null&&y.graphName&&(h.graphDetails.graphName=c(h.graphDetails.graphName)),h},[t,d,c]),M=()=>{var j,K;if(!m(t))return P();const h=bt(t,g);(j=h==null?void 0:h.graphDetails)!=null&&j.graphName&&(h.graphDetails.graphName=c(h.graphDetails.graphName));const y=gt((K=h==null?void 0:h.graphDetails)==null?void 0:K.colorPallete);return o(mt,{values:h,isTable:!0,showDownload:!0,showGraphName:!0,graphColor:y})},P=()=>O("div",{style:{textAlign:"center"},children:[o("img",{alt:c("No Data Found"),style:{height:"250px"},src:gn}),o(U,{variant:"h6",style:{marginTop:"10px"},children:c("No Data Found")})]});return O(Ce,{children:[o(Bt,{sx:{borderRadius:"10px",boxShadow:1,height:"auto",display:"flex",flexDirection:"column"},children:O(jt,{children:[O(be,{justifyContent:"flex-end",alignItems:"center",direction:"row",spacing:1,children:[o(Ne,{size:"small",onClick:S,title:"Maximize",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:o(a2,{fontSize:"small"})}),o(Ne,{...n,size:"small",style:{cursor:"grab"},children:o(r2,{})})]}),M()]})}),O(Gt,{open:r,onClose:A,fullWidth:!0,maxWidth:"lg",PaperProps:{sx:{height:{xs:"100vh",sm:"100vh",md:"80vh",lg:"80vh",xl:"80vh"},maxHeight:{xs:"100vh",sm:"100vh",md:"80vh",lg:"80vh",xl:"80vh"},borderRadius:"10px",margin:{xs:"0",md:"24px"}}},children:[O(Ft,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"16px 24px",borderBottom:`1px solid ${z.dialog.borderBottom}`},children:[O(be,{direction:"row",spacing:2,alignItems:"center",children:[o(U,{variant:"h6",children:(w=t==null?void 0:t.graphDetails)!=null&&w.graphName?c(t.graphDetails.graphName):c("Chart View")}),m(t)&&o(De,{size:"small",sx:{minWidth:120},children:o(Ge,{value:d||"",onChange:D,displayEmpty:!0,variant:"outlined",sx:{height:"32px"},children:Cn(g)?i2.map(h=>o(Se,{value:h.value,children:h.label},h.value)):s2.map(h=>o(Se,{value:h.value,children:h.label},h.value))})})]}),o(Ne,{onClick:A,size:"small",children:o(o2,{})})]}),o(Ut,{sx:{display:"flex",flexDirection:"column",padding:"24px",height:"calc(100% - 64px)",overflow:"hidden"},children:o("div",{style:{flex:1,display:"flex",flexDirection:"column",height:"100%",overflow:"hidden"},children:m(t)?(()=>{var y;const h=gt((y=R==null?void 0:R.graphDetails)==null?void 0:y.colorPallete);return o(mt,{values:R,isTable:!0,showDownload:!0,showGraphName:!0,height:"100%",graphColor:h})})():P()})})]})]})},xn=({cards:e=[],loading:t})=>{const[a,n]=x.useState(e);Fe.useEffect(()=>{n(e)},[e]);const s=r=>{if(!r.destination)return;const l=Array.from(a),[d]=l.splice(r.source.index,1);l.splice(r.destination.index,0,d),n(l)};return t?o(W,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:o(qe,{})}):a.length?o(u2,{onDragEnd:s,children:o(c2,{droppableId:"charts",direction:"horizontal",children:r=>O(N,{container:!0,spacing:2,...r.droppableProps,ref:r.innerRef,children:[a.map((l,d)=>o(d2,{draggableId:l.id.toString(),index:d,children:p=>{var c;return o(N,{item:!0,xs:12,md:6,lg:4,ref:p.innerRef,...p.draggableProps,children:o(bn,{title:(c=l.graphDetails)==null?void 0:c.graphName,data:l,dragHandleProps:p.dragHandleProps})})}},l.id)),r.placeholder]})})}):o(W,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:o(U,{variant:"h6",color:"text.secondary",children:"No charts available. Please configure your dashboard."})})};var Xe={},vn=f2;Object.defineProperty(Xe,"__esModule",{value:!0});var zt=Xe.default=void 0,yn=vn(h2()),En=f;zt=Xe.default=(0,yn.default)((0,En.jsx)("path",{d:"m20.41 8.41-4.83-4.83c-.37-.37-.88-.58-1.41-.58H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V9.83c0-.53-.21-1.04-.59-1.42M7 7h7v2H7zm10 10H7v-2h10zm0-4H7v-2h10z"}),"TextSnippet");const Sn={display:"flex",justifyContent:"space-between",alignItems:"center",borderBottom:"1px solid #eee",padding:"15px 0",marginBottom:"10px",backgroundColor:"#f9f9f9",borderRadius:"8px",paddingLeft:"15px",paddingRight:"15px",boxShadow:"0 2px 4px rgba(0,0,0,0.1)",transition:"transform 0.2s, box-shadow 0.2s","&:hover":{transform:"translateY(-2px)",boxShadow:"0 4px 8px rgba(0,0,0,0.2)"}},Dn={display:"flex",justifyContent:"center",alignItems:"center",gap:1,fontWeight:"500",color:"#555"};z.primary.white,z.tab.background;const An={marginLeft:"10px",transition:"all 0.3s ease-in-out","&:hover":{backgroundColor:"#1976d2",color:"#fff",transform:"scale(1.1)"}},xt=["#4dc9f6","#f67019","#8549ba","#f53050","#537bc4","#acc236","#166a8f","#00a950","#58595b","#ff6384","#36a2eb","#ffce56","#cc65fe","#ff9f40","#8c564b","#e377c2","#7f7f7f","#bcbd22","#17becf"],Pn=_e(Mt)(({theme:e})=>({marginTop:"0px !important",border:`1px solid ${z.primary.border}`,borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.05)","&:not(:last-child)":{borderBottom:0},"&:before":{display:"none"}})),Rn=_e(It)(({theme:e})=>({minHeight:"2rem !important",margin:"0px !important",backgroundColor:z.primary.ultraLight,borderRadius:"8px 8px 0 0",transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:`${z.primary.light}20`}})),Mn=({reportConfig:e=[],kpiReportPrefs:t=[],loading:a})=>{var E;const n=Z(b=>b.commonFilter.Reports),s=Z(b=>b.commonFilter.RequestBench),r=Z(b=>b.AllDropDown.dropDown),[l,d]=x.useState([]),[p,c]=x.useState([]),[g,m]=x.useState([...n.reportDate]),[S,A]=x.useState([]),[D,R]=x.useState([]),[M,P]=x.useState([]),[I,w]=x.useState(!1),[h,y]=x.useState(""),j=wt(),K=Z(b=>b.userManagement.userData),V=(K==null?void 0:K.user_id)||"",{t:L}=xe();x.useEffect(()=>{if(n!=null&&n.reportDate){const b=new Date(n==null?void 0:n.reportDate[0]),k=new Date(n==null?void 0:n.reportDate[1]);m([b,k])}},[n==null?void 0:n.reportDate]),x.useEffect(()=>{C(),v()},[]);const F=["US","EUR"],[T,_]=x.useState([]),B=()=>{_(T.length===F.length?[]:F)},v=()=>{te(`/${le}/GraphConfig/getReqStatuses`,"get",b=>P((b==null?void 0:b.body)||[]),()=>{})},C=()=>{te(`/${le}/GraphConfig/getReqTypes`,"get",b=>R((b==null?void 0:b.body)||[]),()=>{})},i=(b,k,ne)=>{y(m2.REPORT_LOADING),w(!0),te(`/${le}/excel${b}`,"postandgetblob",J=>{const ae=URL.createObjectURL(J),X=document.createElement("a");X.href=ae,X.setAttribute("download",k),document.body.appendChild(X),X.click(),document.body.removeChild(X),URL.revokeObjectURL(ae),w(!1),y("")},()=>{w(!1),y("")},ne)},u=b=>{j(ie({module:"Reports",filterData:{...n,reportDate:b}}))},G=()=>{_([]),A([]),d([]),c([]);const b=new Date,k=new Date;k.setDate(k.getDate()-3650),m([k,b]),j(Vt({module:"Reports"}))};return O(Ce,{children:[o(N,{item:!0,md:12,children:O(Pn,{defaultExpanded:!1,sx:{marginTop:"5px !important",marginLeft:"25px !important",marginRight:"20px !important"},children:[O(Rn,{expandIcon:o(Lt,{sx:{fontSize:"1.25rem",color:z.primary.main}}),"aria-controls":"panel1a-content",id:"panel1a-header",children:[o(Tt,{sx:{fontSize:"1.25rem",marginRight:1,color:z.primary.main}}),o(U,{sx:{fontSize:"0.875rem",fontWeight:600,color:z.primary.dark},children:L("Filter Reports")})]}),O(kt,{children:[O(N,{container:!0,rowSpacing:1,spacing:2,children:[O(N,{item:!0,md:2,children:[o(U,{sx:ge,children:L("Date Range")}),o(De,{fullWidth:!0,sx:{padding:0,height:"37px"},children:o(Ot,{dateAdapter:_t,children:o(Nt,{handleDate:u,cleanDate:!1,date:g})})})]}),O(N,{item:!0,md:2,children:[o(U,{sx:ge,children:L("Region")}),o(Be,{options:[...(E=F==null?void 0:F.filter(b=>b!=="Select All"))==null?void 0:E.sort((b,k)=>typeof b=="string"&&typeof k=="string"?b.localeCompare(k):0)],value:T,onChange:b=>{var k;b.length>0&&((k=b[b.length-1])==null?void 0:k.label)==="Select All"?B():_(b)},placeholder:"Select Region"})]}),O(N,{item:!0,md:2,children:[o(U,{sx:ge,children:L("Division")}),o(N2,{matGroup:(r==null?void 0:r.Division)??[],selectedMaterialGroup:l,setSelectedMaterialGroup:b=>{if(!b||b.length===0){d([]);return}d(b)},placeholder:"Select Division"})]})]}),o(N,{container:!0,style:{display:"flex",justifyContent:"flex-end"},children:o(N,{item:!0,style:{display:"flex",justifyContent:"space-around"},children:o(ue,{variant:"outlined",sx:{...Kt},onClick:G,children:L("Clear")})})})]})]})}),o(N,{item:!0,md:12,children:o(be,{justifyContent:"space-between",direction:"row",children:o(N,{container:!0,spacing:2,children:o(N,{item:!0,md:12,children:o(Bt,{sx:{borderRadius:"10px",boxShadow:4,height:{xs:"calc(70vh - 100px)",md:"calc(75vh - 100px)",lg:"calc(80vh - 130px)"},marginLeft:"25px",marginTop:"20px",marginRight:"20px"},children:O(jt,{children:[o(U,{variant:"h6",sx:{fontWeight:"bold",marginBottom:"20px"},children:L("Application Report List")}),O(be,{spacing:2,sx:{height:{xs:"calc(70vh - 160px)",md:"calc(75vh - 160px)",lg:"calc(80vh - 190px)"},overflowY:"auto",overflowX:"hidden","&::-webkit-scrollbar":{width:"4px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"rgba(0,0,0,0.2)",borderRadius:"4px"}},children:[a&&o(W,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:o(qe,{})}),o(N,{container:!0,spacing:1,children:e.filter(b=>{const k=t.find(ne=>ne.KpiId===b.MDG_KPI_ID);return(k==null?void 0:k.KpiVisibility)===!0&&(k==null?void 0:k.IsActive)===!0}).sort((b,k)=>+b.MDG_KPI_GRAPH_SEQUENCE-+k.MDG_KPI_GRAPH_SEQUENCE).map((b,k)=>{var J,ae,X,ve,ye;const ne={FromDate:we(((J=n==null?void 0:n.reportDate)==null?void 0:J[0])??((ae=s==null?void 0:s.createdOn)==null?void 0:ae[0])).format("YYYY-MM-DD"),ToDate:we(((X=n==null?void 0:n.reportDate)==null?void 0:X[1])??((ve=s==null?void 0:s.createdOn)==null?void 0:ve[1])).format("YYYY-MM-DD"),Requestor:"",KpiId:b.MDG_KPI_ID,Module:"Material",UserId:V,Priority:"",Region:T.join(","),ReqType:S.join(","),ReqStatus:p.join(","),GraphType:b.MDG_KPI_GRAPH_TYPE||"",KpiName:b.MDG_KPI_NAME||"",ColPallet:b.MDG_KPI_COLOR_PALLET||"",GraphColumn:((ye=b.MDG_KPI_GRAPH_COLUMN)==null?void 0:ye.toLowerCase())||"",GraphSequence:b.MDG_KPI_GRAPH_SEQUENCE||""};return o(N,{item:!0,xs:6,sx:{paddingRight:"8px"},children:O(W,{sx:{...Sn,width:"100%",boxSizing:"border-box"},children:[O(U,{variant:"body1",sx:{...Dn,whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:[o(zt,{sx:{color:xt[k%xt.length],marginRight:"4px"}}),L(b.MDG_KPI_NAME)]}),o(ue,{variant:"outlined",sx:An,onClick:()=>i(b.MDG_KPI_ENDPOINT,`${b.MDG_KPI_NAME}.xlsx`,ne),children:L("Download")})]})},b.MDG_KPI_ID)})})]})]})})})})})}),o(p2,{blurLoading:I,loaderMessage:h})]})},Y={Open:"Open",PendingConfirmation:"PendingConfirmation",Confirmed:"Confirmed",Delivered:"Delivered",Blocked:"Blocked",ProductionInProgress:"ProductionInProgress",ProductionCompleted:"ProductionCompleted",PendingASN:"PendingASN",OverdueShipment:"OverdueShipment",PendingReturns:"PendingReturns",OnTimeDelivery:"OnTimeDelivery",OrderFillRate:"OrderFillRate",ReadyToINV:"ReadyToINV",ReadyToPostINV:"ReadyToPostINV",PostedINV:"PostedINV",PaidINV:"PaidINV",UnpaidINV:"UnpaidINV",RejectedINV:"RejectedINV",MaterialQuantity:"MaterialQuantity",MaterialValue:"MaterialValue",POStatus:"POStatus",INVStatus:"INVStatus",PaymentStatus:"PaymentStatus",ProductionStatus:"ProductionStatus",SRStatus:"SRStatus",SRPriority:"SRPriority",DeliveryDelay:"DeliveryDelay",PlanningTask:"PlanningTask",PendingAck:"PendingAck",PendingConsumption:"PendingConsumption",PendingPlanning:"PendingPlanning",SubmiitedAck:"SubmiitedAck",ConfirmationSubmitted:"ConfirmationSubmitted",SubmittedASN:"SubmittedASN",SubmittedConsumption:"SubmittedConsumption",ConsumptionSummary:"ConsumptionSummary",ConsumptionByPO:"ConsumptionByPO",ConsumptionByASN:"ConsumptionByASN",ConsumptionByMaterial:"ConsumptionByMaterial",MaterialGroup:"MaterialGroup",PlanningTable:"PlanningTable",Change:"Change",Create:"Create",Extend:"Extend",MassExtend:"MassExtend",MassChange:"MassChange",MassCreate:"MassCreate",pieStatus:"pieStatus",ExtendTable:"ExtendTable",ExtendTableHeader:"ExtendTableHeader",OnboardBar:"OnboardBar",FormToSupplier:"FormToSupplier",FinanceReview:"FinanceReview",ProcurementLeadReview:"ProcurementLeadReview",BuyerReview:"BuyerReview",ComplianceReview:"ComplianceReview",Completed:"Completed",dashboardDate:"dashboardDate",CycleTime:"CycleTime",BottleNeck:"BottleNeck",BottleNeckTable:"BottleNeckTable",BottleNeckTable2:"BottleNeckTable2",BottleNeckGraph:"BottleNeckGraph",ReviewPending:"ReviewPending",Approved:"Approved",CorrectionPending:"CorrectionPending",ApprovalPending:"ApprovalPending",PlanningTaskContent:"PlanningTaskContent",PlanningTaskLength:"PlanningTaskLength",PlanningTaskHeader:"PlanningTaskHeader",requestItemLength:"requestItemLength",requestTypeGraph:"requestTypeGraph",BasedOnGroupGraph:"BasedOnGroupGraph",topFiveSlaBreached:"topFiveSlaBreached",slaRequestType:"slaRequestType",slaRequestTypeContent:"slaRequestTypeContent",selectedRequestTypeSLATable:"selectedRequestTypeSLATable",selectedRequestTypeRole:"selectedRequestTypeRole"};/*!
 * @kurkle/color v0.3.4
 * https://github.com/kurkle/color#readme
 * (c) 2024 Jukka Kurkela
 * Released under the MIT License
 */function Ae(e){return e+.5|0}const oe=(e,t,a)=>Math.max(Math.min(e,a),t);function Ee(e){return oe(Ae(e*2.55),0,255)}function ce(e){return oe(Ae(e*255),0,255)}function ee(e){return oe(Ae(e/2.55)/100,0,1)}function vt(e){return oe(Ae(e*100),0,100)}const q={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},ze=[..."0123456789ABCDEF"],In=e=>ze[e&15],wn=e=>ze[(e&240)>>4]+ze[e&15],Re=e=>(e&240)>>4===(e&15),Ln=e=>Re(e.r)&&Re(e.g)&&Re(e.b)&&Re(e.a);function Tn(e){var t=e.length,a;return e[0]==="#"&&(t===4||t===5?a={r:255&q[e[1]]*17,g:255&q[e[2]]*17,b:255&q[e[3]]*17,a:t===5?q[e[4]]*17:255}:(t===7||t===9)&&(a={r:q[e[1]]<<4|q[e[2]],g:q[e[3]]<<4|q[e[4]],b:q[e[5]]<<4|q[e[6]],a:t===9?q[e[7]]<<4|q[e[8]]:255})),a}const kn=(e,t)=>e<255?t(e):"";function On(e){var t=Ln(e)?In:wn;return e?"#"+t(e.r)+t(e.g)+t(e.b)+kn(e.a,t):void 0}const _n=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function Zt(e,t,a){const n=t*Math.min(a,1-a),s=(r,l=(r+e/30)%12)=>a-n*Math.max(Math.min(l-3,9-l,1),-1);return[s(0),s(8),s(4)]}function Nn(e,t,a){const n=(s,r=(s+e/60)%6)=>a-a*t*Math.max(Math.min(r,4-r,1),0);return[n(5),n(3),n(1)]}function Kn(e,t,a){const n=Zt(e,1,.5);let s;for(t+a>1&&(s=1/(t+a),t*=s,a*=s),s=0;s<3;s++)n[s]*=1-t-a,n[s]+=t;return n}function Vn(e,t,a,n,s){return e===s?(t-a)/n+(t<a?6:0):t===s?(a-e)/n+2:(e-t)/n+4}function Qe(e){const a=e.r/255,n=e.g/255,s=e.b/255,r=Math.max(a,n,s),l=Math.min(a,n,s),d=(r+l)/2;let p,c,g;return r!==l&&(g=r-l,c=d>.5?g/(2-r-l):g/(r+l),p=Vn(a,n,s,g,r),p=p*60+.5),[p|0,c||0,d]}function $e(e,t,a,n){return(Array.isArray(t)?e(t[0],t[1],t[2]):e(t,a,n)).map(ce)}function Je(e,t,a){return $e(Zt,e,t,a)}function jn(e,t,a){return $e(Kn,e,t,a)}function Bn(e,t,a){return $e(Nn,e,t,a)}function Yt(e){return(e%360+360)%360}function Gn(e){const t=_n.exec(e);let a=255,n;if(!t)return;t[5]!==n&&(a=t[6]?Ee(+t[5]):ce(+t[5]));const s=Yt(+t[2]),r=+t[3]/100,l=+t[4]/100;return t[1]==="hwb"?n=jn(s,r,l):t[1]==="hsv"?n=Bn(s,r,l):n=Je(s,r,l),{r:n[0],g:n[1],b:n[2],a}}function Fn(e,t){var a=Qe(e);a[0]=Yt(a[0]+t),a=Je(a),e.r=a[0],e.g=a[1],e.b=a[2]}function Un(e){if(!e)return;const t=Qe(e),a=t[0],n=vt(t[1]),s=vt(t[2]);return e.a<255?`hsla(${a}, ${n}%, ${s}%, ${ee(e.a)})`:`hsl(${a}, ${n}%, ${s}%)`}const yt={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},Et={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function Hn(){const e={},t=Object.keys(Et),a=Object.keys(yt);let n,s,r,l,d;for(n=0;n<t.length;n++){for(l=d=t[n],s=0;s<a.length;s++)r=a[s],d=d.replace(r,yt[r]);r=parseInt(Et[l],16),e[d]=[r>>16&255,r>>8&255,r&255]}return e}let Me;function zn(e){Me||(Me=Hn(),Me.transparent=[0,0,0,0]);const t=Me[e.toLowerCase()];return t&&{r:t[0],g:t[1],b:t[2],a:t.length===4?t[3]:255}}const Zn=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function Yn(e){const t=Zn.exec(e);let a=255,n,s,r;if(t){if(t[7]!==n){const l=+t[7];a=t[8]?Ee(l):oe(l*255,0,255)}return n=+t[1],s=+t[3],r=+t[5],n=255&(t[2]?Ee(n):oe(n,0,255)),s=255&(t[4]?Ee(s):oe(s,0,255)),r=255&(t[6]?Ee(r):oe(r,0,255)),{r:n,g:s,b:r,a}}}function qn(e){return e&&(e.a<255?`rgba(${e.r}, ${e.g}, ${e.b}, ${ee(e.a)})`:`rgb(${e.r}, ${e.g}, ${e.b})`)}const je=e=>e<=.0031308?e*12.92:Math.pow(e,1/2.4)*1.055-.055,fe=e=>e<=.04045?e/12.92:Math.pow((e+.055)/1.055,2.4);function Wn(e,t,a){const n=fe(ee(e.r)),s=fe(ee(e.g)),r=fe(ee(e.b));return{r:ce(je(n+a*(fe(ee(t.r))-n))),g:ce(je(s+a*(fe(ee(t.g))-s))),b:ce(je(r+a*(fe(ee(t.b))-r))),a:e.a+a*(t.a-e.a)}}function Ie(e,t,a){if(e){let n=Qe(e);n[t]=Math.max(0,Math.min(n[t]+n[t]*a,t===0?360:1)),n=Je(n),e.r=n[0],e.g=n[1],e.b=n[2]}}function qt(e,t){return e&&Object.assign(t||{},e)}function St(e){var t={r:0,g:0,b:0,a:255};return Array.isArray(e)?e.length>=3&&(t={r:e[0],g:e[1],b:e[2],a:255},e.length>3&&(t.a=ce(e[3]))):(t=qt(e,{r:0,g:0,b:0,a:1}),t.a=ce(t.a)),t}function Xn(e){return e.charAt(0)==="r"?Yn(e):Gn(e)}class Te{constructor(t){if(t instanceof Te)return t;const a=typeof t;let n;a==="object"?n=St(t):a==="string"&&(n=Tn(t)||zn(t)||Xn(t)),this._rgb=n,this._valid=!!n}get valid(){return this._valid}get rgb(){var t=qt(this._rgb);return t&&(t.a=ee(t.a)),t}set rgb(t){this._rgb=St(t)}rgbString(){return this._valid?qn(this._rgb):void 0}hexString(){return this._valid?On(this._rgb):void 0}hslString(){return this._valid?Un(this._rgb):void 0}mix(t,a){if(t){const n=this.rgb,s=t.rgb;let r;const l=a===r?.5:a,d=2*l-1,p=n.a-s.a,c=((d*p===-1?d:(d+p)/(1+d*p))+1)/2;r=1-c,n.r=255&c*n.r+r*s.r+.5,n.g=255&c*n.g+r*s.g+.5,n.b=255&c*n.b+r*s.b+.5,n.a=l*n.a+(1-l)*s.a,this.rgb=n}return this}interpolate(t,a){return t&&(this._rgb=Wn(this._rgb,t._rgb,a)),this}clone(){return new Te(this.rgb)}alpha(t){return this._rgb.a=ce(t),this}clearer(t){const a=this._rgb;return a.a*=1-t,this}greyscale(){const t=this._rgb,a=Ae(t.r*.3+t.g*.59+t.b*.11);return t.r=t.g=t.b=a,this}opaquer(t){const a=this._rgb;return a.a*=1+t,this}negate(){const t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return Ie(this._rgb,2,t),this}darken(t){return Ie(this._rgb,2,-t),this}saturate(t){return Ie(this._rgb,1,t),this}desaturate(t){return Ie(this._rgb,1,-t),this}rotate(t){return Fn(this._rgb,t),this}}function Wt(e){return new Te(e)}const pe=(e,t)=>{var a=t===void 0?.5:1-t;return Wt(e).alpha(a).rgbString()},Dt={BUPA:{product:!0,TabPanel:[{uid:0,name:"Status by Scenario",icon:"HowToReg",required:!0},{uid:1,name:"Bottleneck",icon:"RunningWithErrors",required:!0}],Tiles:[,{uid:0,name:"Create",count:Y.Create,required:!0,width:2,type:"RBEC",status:"Create",color:pe("#4dc9f6",.7),borderColor:"#4dc9f6"},{uid:0,name:"Change",count:Y.Change,required:!0,width:2,type:"RBEC",status:"Change",color:pe("#f6d55c",.7),borderColor:"#f6d55c"},{uid:0,name:"Extend",count:Y.Extend,required:!0,width:2,type:"RBEC",status:"Extend",color:pe("#537bc4",.7),borderColor:"#537bc4"},{uid:0,name:"Create With Upload",count:Y.MassCreate,required:!0,width:2,type:"RBEC",status:"Mass Create",color:pe("#00a950",.7),borderColor:"#00a950"},{uid:0,name:"Change With Upload",count:Y.MassChange,required:!0,width:2,type:"RBEC",status:"Mass Change",color:pe("#8549ba",.7),borderColor:"#8549ba"},{uid:0,name:"Extend With Upload",count:Y.MassExtend,required:!0,width:2,type:"RBEC",status:"Mass Extend",color:pe("#ff6384",.7),borderColor:"#ff6384"}],Graphs:[{uid:1,id:1,name:"Time Log Based on Roles",count:Y.OnboardBar,required:!0,stackedBar:!0,isStacked:!0,xaxis:"status",yaxis:"statusCount",yaxisHeader:"Requests",type:"po",width:12},{uid:0,id:1,name:"Status ",count:Y.pieStatus,required:!0,xaxis:"status",yaxis:"statusCount",type:"RBEC",isPie:!0,width:6},{uid:0,id:0,name:"Extend Table",count:Y.ExtendTable,required:!0,width:6,isTable:!0},{uid:2,id:1010,name:"Extend Table",count:Y.BottleNeckTable,required:!0,width:6,isTable2:!0,isTable:!1},{uid:2,id:1011,name:"Extend Table",count:Y.BottleNeckTable2,required:!0,width:6,isTable3:!0,isTable2:!1,isTable:!1},{uid:2,id:1091,name:"",count:Y.BottleNeckGraph,required:!0,width:12,isTable3:!1,isTable2:!1,isTable:!1,isgroup:!0}]}};var et={},Ze={exports:{}};(function(e,t){(function(a,n){n(t)})(g2,function(a){var n=function(){return n=Object.assign||function(r){for(var l,d=1,p=arguments.length;d<p;d++)for(var c in l=arguments[d])Object.prototype.hasOwnProperty.call(l,c)&&(r[c]=l[c]);return r},n.apply(this,arguments)},s=function(){function r(l,d,p){var c=this;this.endVal=d,this.options=p,this.version="2.9.0",this.defaults={startVal:0,decimalPlaces:0,duration:2,useEasing:!0,useGrouping:!0,useIndianSeparators:!1,smartEasingThreshold:999,smartEasingAmount:333,separator:",",decimal:".",prefix:"",suffix:"",enableScrollSpy:!1,scrollSpyDelay:200,scrollSpyOnce:!1},this.finalEndVal=null,this.useEasing=!0,this.countDown=!1,this.error="",this.startVal=0,this.paused=!0,this.once=!1,this.count=function(g){c.startTime||(c.startTime=g);var m=g-c.startTime;c.remaining=c.duration-m,c.useEasing?c.countDown?c.frameVal=c.startVal-c.easingFn(m,0,c.startVal-c.endVal,c.duration):c.frameVal=c.easingFn(m,c.startVal,c.endVal-c.startVal,c.duration):c.frameVal=c.startVal+(c.endVal-c.startVal)*(m/c.duration);var S=c.countDown?c.frameVal<c.endVal:c.frameVal>c.endVal;c.frameVal=S?c.endVal:c.frameVal,c.frameVal=Number(c.frameVal.toFixed(c.options.decimalPlaces)),c.printValue(c.frameVal),m<c.duration?c.rAF=requestAnimationFrame(c.count):c.finalEndVal!==null?c.update(c.finalEndVal):c.options.onCompleteCallback&&c.options.onCompleteCallback()},this.formatNumber=function(g){var m,S,A,D,R=g<0?"-":"";m=Math.abs(g).toFixed(c.options.decimalPlaces);var M=(m+="").split(".");if(S=M[0],A=M.length>1?c.options.decimal+M[1]:"",c.options.useGrouping){D="";for(var P=3,I=0,w=0,h=S.length;w<h;++w)c.options.useIndianSeparators&&w===4&&(P=2,I=1),w!==0&&I%P==0&&(D=c.options.separator+D),I++,D=S[h-w-1]+D;S=D}return c.options.numerals&&c.options.numerals.length&&(S=S.replace(/[0-9]/g,function(y){return c.options.numerals[+y]}),A=A.replace(/[0-9]/g,function(y){return c.options.numerals[+y]})),R+c.options.prefix+S+A+c.options.suffix},this.easeOutExpo=function(g,m,S,A){return S*(1-Math.pow(2,-10*g/A))*1024/1023+m},this.options=n(n({},this.defaults),p),this.formattingFn=this.options.formattingFn?this.options.formattingFn:this.formatNumber,this.easingFn=this.options.easingFn?this.options.easingFn:this.easeOutExpo,this.el=typeof l=="string"?document.getElementById(l):l,d=d??this.parse(this.el.innerHTML),this.startVal=this.validateValue(this.options.startVal),this.frameVal=this.startVal,this.endVal=this.validateValue(d),this.options.decimalPlaces=Math.max(this.options.decimalPlaces),this.resetDuration(),this.options.separator=String(this.options.separator),this.useEasing=this.options.useEasing,this.options.separator===""&&(this.options.useGrouping=!1),this.el?this.printValue(this.startVal):this.error="[CountUp] target is null or undefined",typeof window<"u"&&this.options.enableScrollSpy&&(this.error?console.error(this.error,l):(window.onScrollFns=window.onScrollFns||[],window.onScrollFns.push(function(){return c.handleScroll(c)}),window.onscroll=function(){window.onScrollFns.forEach(function(g){return g()})},this.handleScroll(this)))}return r.prototype.handleScroll=function(l){if(l&&window&&!l.once){var d=window.innerHeight+window.scrollY,p=l.el.getBoundingClientRect(),c=p.top+window.pageYOffset,g=p.top+p.height+window.pageYOffset;g<d&&g>window.scrollY&&l.paused?(l.paused=!1,setTimeout(function(){return l.start()},l.options.scrollSpyDelay),l.options.scrollSpyOnce&&(l.once=!0)):(window.scrollY>g||c>d)&&!l.paused&&l.reset()}},r.prototype.determineDirectionAndSmartEasing=function(){var l=this.finalEndVal?this.finalEndVal:this.endVal;this.countDown=this.startVal>l;var d=l-this.startVal;if(Math.abs(d)>this.options.smartEasingThreshold&&this.options.useEasing){this.finalEndVal=l;var p=this.countDown?1:-1;this.endVal=l+p*this.options.smartEasingAmount,this.duration=this.duration/2}else this.endVal=l,this.finalEndVal=null;this.finalEndVal!==null?this.useEasing=!1:this.useEasing=this.options.useEasing},r.prototype.start=function(l){this.error||(this.options.onStartCallback&&this.options.onStartCallback(),l&&(this.options.onCompleteCallback=l),this.duration>0?(this.determineDirectionAndSmartEasing(),this.paused=!1,this.rAF=requestAnimationFrame(this.count)):this.printValue(this.endVal))},r.prototype.pauseResume=function(){this.paused?(this.startTime=null,this.duration=this.remaining,this.startVal=this.frameVal,this.determineDirectionAndSmartEasing(),this.rAF=requestAnimationFrame(this.count)):cancelAnimationFrame(this.rAF),this.paused=!this.paused},r.prototype.reset=function(){cancelAnimationFrame(this.rAF),this.paused=!0,this.resetDuration(),this.startVal=this.validateValue(this.options.startVal),this.frameVal=this.startVal,this.printValue(this.startVal)},r.prototype.update=function(l){cancelAnimationFrame(this.rAF),this.startTime=null,this.endVal=this.validateValue(l),this.endVal!==this.frameVal&&(this.startVal=this.frameVal,this.finalEndVal==null&&this.resetDuration(),this.finalEndVal=null,this.determineDirectionAndSmartEasing(),this.rAF=requestAnimationFrame(this.count))},r.prototype.printValue=function(l){var d;if(this.el){var p=this.formattingFn(l);!((d=this.options.plugin)===null||d===void 0)&&d.render?this.options.plugin.render(this.el,p):this.el.tagName==="INPUT"?this.el.value=p:this.el.tagName==="text"||this.el.tagName==="tspan"?this.el.textContent=p:this.el.innerHTML=p}},r.prototype.ensureNumber=function(l){return typeof l=="number"&&!isNaN(l)},r.prototype.validateValue=function(l){var d=Number(l);return this.ensureNumber(d)?d:(this.error="[CountUp] invalid start or end value: ".concat(l),null)},r.prototype.resetDuration=function(){this.startTime=null,this.duration=1e3*Number(this.options.duration),this.remaining=this.duration},r.prototype.parse=function(l){var d=function(m){return m.replace(/([.,'  ])/g,"\\$1")},p=d(this.options.separator),c=d(this.options.decimal),g=l.replace(new RegExp(p,"g"),"").replace(new RegExp(c,"g"),".");return parseFloat(g)},r}();a.CountUp=s})})(Ze,Ze.exports);var Qn=Ze.exports;Object.defineProperty(et,"__esModule",{value:!0});var H=x,$n=Qn;function Jn(e,t){var a=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(a!=null){var n,s,r,l,d=[],p=!0,c=!1;try{if(r=(a=a.call(e)).next,t===0){if(Object(a)!==a)return;p=!1}else for(;!(p=(n=r.call(a)).done)&&(d.push(n.value),d.length!==t);p=!0);}catch(g){c=!0,s=g}finally{try{if(!p&&a.return!=null&&(l=a.return(),Object(l)!==l))return}finally{if(c)throw s}}return d}}function At(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(s){return Object.getOwnPropertyDescriptor(e,s).enumerable})),a.push.apply(a,n)}return a}function ke(e){for(var t=1;t<arguments.length;t++){var a=arguments[t]!=null?arguments[t]:{};t%2?At(Object(a),!0).forEach(function(n){n0(e,n,a[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):At(Object(a)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(a,n))})}return e}function e0(e,t){if(typeof e!="object"||!e)return e;var a=e[Symbol.toPrimitive];if(a!==void 0){var n=a.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function t0(e){var t=e0(e,"string");return typeof t=="symbol"?t:String(t)}function n0(e,t,a){return t=t0(t),t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function Ye(){return Ye=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},Ye.apply(this,arguments)}function a0(e,t){if(e==null)return{};var a={},n=Object.keys(e),s,r;for(r=0;r<n.length;r++)s=n[r],!(t.indexOf(s)>=0)&&(a[s]=e[s]);return a}function Xt(e,t){if(e==null)return{};var a=a0(e,t),n,s;if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(s=0;s<r.length;s++)n=r[s],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}function r0(e,t){return i0(e)||Jn(e,t)||s0(e,t)||o0()}function i0(e){if(Array.isArray(e))return e}function s0(e,t){if(e){if(typeof e=="string")return Pt(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);if(a==="Object"&&e.constructor&&(a=e.constructor.name),a==="Map"||a==="Set")return Array.from(e);if(a==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return Pt(e,t)}}function Pt(e,t){(t==null||t>e.length)&&(t=e.length);for(var a=0,n=new Array(t);a<t;a++)n[a]=e[a];return n}function o0(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var l0=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u"?H.useLayoutEffect:H.useEffect;function Q(e){var t=H.useRef(e);return l0(function(){t.current=e}),H.useCallback(function(){for(var a=arguments.length,n=new Array(a),s=0;s<a;s++)n[s]=arguments[s];return t.current.apply(void 0,n)},[])}var c0=function(t,a){var n=a.decimal,s=a.decimals,r=a.duration,l=a.easingFn,d=a.end,p=a.formattingFn,c=a.numerals,g=a.prefix,m=a.separator,S=a.start,A=a.suffix,D=a.useEasing,R=a.useGrouping,M=a.useIndianSeparators,P=a.enableScrollSpy,I=a.scrollSpyDelay,w=a.scrollSpyOnce,h=a.plugin;return new $n.CountUp(t,d,{startVal:S,duration:r,decimal:n,decimalPlaces:s,easingFn:l,formattingFn:p,numerals:c,separator:m,prefix:g,suffix:A,plugin:h,useEasing:D,useIndianSeparators:M,useGrouping:R,enableScrollSpy:P,scrollSpyDelay:I,scrollSpyOnce:w})},d0=["ref","startOnMount","enableReinitialize","delay","onEnd","onStart","onPauseResume","onReset","onUpdate"],u0={decimal:".",separator:",",delay:null,prefix:"",suffix:"",duration:2,start:0,decimals:0,startOnMount:!0,enableReinitialize:!0,useEasing:!0,useGrouping:!0,useIndianSeparators:!1},Qt=function(t){var a=Object.fromEntries(Object.entries(t).filter(function(L){var F=r0(L,2),T=F[1];return T!==void 0})),n=H.useMemo(function(){return ke(ke({},u0),a)},[t]),s=n.ref,r=n.startOnMount,l=n.enableReinitialize,d=n.delay,p=n.onEnd,c=n.onStart,g=n.onPauseResume,m=n.onReset,S=n.onUpdate,A=Xt(n,d0),D=H.useRef(),R=H.useRef(),M=H.useRef(!1),P=Q(function(){return c0(typeof s=="string"?s:s.current,A)}),I=Q(function(L){var F=D.current;if(F&&!L)return F;var T=P();return D.current=T,T}),w=Q(function(){var L=function(){return I(!0).start(function(){p==null||p({pauseResume:h,reset:y,start:K,update:j})})};d&&d>0?R.current=setTimeout(L,d*1e3):L(),c==null||c({pauseResume:h,reset:y,update:j})}),h=Q(function(){I().pauseResume(),g==null||g({reset:y,start:K,update:j})}),y=Q(function(){I().el&&(R.current&&clearTimeout(R.current),I().reset(),m==null||m({pauseResume:h,start:K,update:j}))}),j=Q(function(L){I().update(L),S==null||S({pauseResume:h,reset:y,start:K})}),K=Q(function(){y(),w()}),V=Q(function(L){r&&(L&&y(),w())});return H.useEffect(function(){M.current?l&&V(!0):(M.current=!0,V())},[l,M,V,d,t.start,t.suffix,t.prefix,t.duration,t.separator,t.decimals,t.decimal,t.formattingFn]),H.useEffect(function(){return function(){y()}},[y]),{start:K,pauseResume:h,reset:y,update:j,getCountUp:I}},h0=["className","redraw","containerProps","children","style"],f0=function(t){var a=t.className,n=t.redraw,s=t.containerProps,r=t.children,l=t.style,d=Xt(t,h0),p=H.useRef(null),c=H.useRef(!1),g=Qt(ke(ke({},d),{},{ref:p,startOnMount:typeof r!="function"||t.delay===0,enableReinitialize:!1})),m=g.start,S=g.reset,A=g.update,D=g.pauseResume,R=g.getCountUp,M=Q(function(){m()}),P=Q(function(h){t.preserveValue||S(),A(h)}),I=Q(function(){if(typeof t.children=="function"&&!(p.current instanceof Element)){console.error(`Couldn't find attached element to hook the CountUp instance into! Try to attach "containerRef" from the render prop to a an Element, eg. <span ref={containerRef} />.`);return}R()});H.useEffect(function(){I()},[I]),H.useEffect(function(){c.current&&P(t.end)},[t.end,P]);var w=n&&t;return H.useEffect(function(){n&&c.current&&M()},[M,n,w]),H.useEffect(function(){!n&&c.current&&M()},[M,n,t.start,t.suffix,t.prefix,t.duration,t.separator,t.decimals,t.decimal,t.className,t.formattingFn]),H.useEffect(function(){c.current=!0},[]),typeof r=="function"?r({countUpRef:p,start:m,reset:S,update:A,pauseResume:D,getCountUp:R}):H.createElement("span",Ye({className:a,ref:p,style:l},s),typeof t.start<"u"?R().formattingFn(t.start):"")};et.default=f0;et.useCountUp=Qt;const p0=()=>{var g;const e=(((g=C2)==null?void 0:g.system)==="CHWSCP",Dt.BUPA),t=e.Tiles.reduce((m,S)=>(m[S.count]=!0,m),{}),{t:a}=xe(),n=e.Tiles.reduce((m,S)=>(m[S.count]=0,m),{}),s=Z(m=>m==null?void 0:m.commonFilter.Dashboard),r=Z(m=>m.commonFilter.RequestBench);x.useState(t),x.useState(n);const[l,d]=x.useState([]),p=(m,S)=>{var A=S===void 0?.5:1-S;return Wt(m).alpha(A).rgbString()};x.useEffect(()=>{let m=new FormData;m.append("fromDate",we(s!=null&&s.dashboardDate[0]?s==null?void 0:s.dashboardDate[0]:r==null?void 0:r.createdOn[0]).format("YYYY-MM-DD")+" 00:00:00"),m.append("toDate",we(s!=null&&s.dashboardDate[1]?s==null?void 0:s.dashboardDate[1]:r==null?void 0:r.createdOn[1]).format("YYYY-MM-DD")+" 00:00:00"),m.append("module","Material"),m.append("userId","");const S=D=>{d(D.body)},A=()=>{};te(`/${le}${me.DASHBOARD_APIS.KPI_CARDS}`,"postformdata",S,A,m)},[]);const c={Create:p("#4dc9f6",.7),Change:p("#f6d55c",.7),Extend:p("#537bc4",.7),"Create With Upload":p("#00a950",.7),"Change With Upload":p("#8549ba",.7),"Extend With Upload":p("#ff6384",.7)};return o(N,{container:!0,spacing:2,sx:{mt:2},children:l==null?void 0:l.map(m=>o(Ce,{children:o(N,{item:!0,xs:12,sm:6,md:4,lg:2,children:o(ln,{events:{allow:!0,type:"option"},value:m==null?void 0:m.statusCount,graphName:a(m==null?void 0:m.status),KPIColor:c[m.status]})})}))})},m0=[{value:"Pallet 1",label:"Pallet 1"},{value:"Pastel",label:"Pastel"},{value:"Vibrant",label:"Vibrant"},{value:"Dark",label:"Dark"},{value:"Default",label:"Default"}],g0=[{value:$.BAR,label:"Bar"},{value:$.COLUMN,label:"Column"},{value:$.LINE,label:"Line"},{value:$.AREA,label:"Area"},{value:$.STACK_COLUMN,label:"Stacked Column"}],C0=[{value:$.PIE,label:"Pie"},{value:$.DONUT,label:"Donut"}],b0=e=>e===$.PIE||e===$.DONUT;function Rt(e){const{children:t,value:a,index:n,...s}=e;return o("div",{role:"tabpanel",hidden:a!==n,id:`dashboard-tabpanel-${n}`,"aria-labelledby":`dashboard-tab-${n}`,...s,children:a===n&&o(W,{sx:{p:1},children:t})})}const x0=({open:e,onClose:t,onSave:a,decisionTableConfig:n,userPreferences:s,reportConfig:r})=>{const[l,d]=x.useState(0),[p,c]=x.useState([]),[g,m]=x.useState([]),[S,A]=x.useState(!0),[D,R]=x.useState(!1),[M,P]=x.useState({}),[I,w]=x.useState({}),{t:h}=xe(),y=Z(v=>v.userManagement.userData),j=(y==null?void 0:y.user_id)||"",K=(v,C)=>{d(C)},V=x.useCallback(()=>{if((n==null?void 0:n.length)>0){const v=n.map(i=>{var G;const u=s==null?void 0:s.find(E=>E.KpiId===i.MDG_KPI_ID);return{id:i.MDG_KPI_ID,prefId:(u==null?void 0:u.Id)||null,name:i.MDG_KPI_NAME,enabled:String(i.MDG_KPI_VISIBILITY).toLowerCase()==="true",userEnabled:u?u.KpiVisibility===!0&&u.IsActive===!0:!1,sequence:i.MDG_KPI_GRAPH_SEQUENCE,chartType:(u==null?void 0:u.KpiChartType)||i.MDG_KPI_GRAPH_TYPE,column:(G=i.MDG_KPI_GRAPH_COLUMN)==null?void 0:G.toLowerCase(),colorPallet:(u==null?void 0:u.KpiColPallet)||i.MDG_KPI_COLOR_PALLET}}),C=r.map(i=>{const u=s==null?void 0:s.find(G=>G.KpiId===i.MDG_KPI_ID);return{id:i.MDG_KPI_ID,prefId:(u==null?void 0:u.Id)||null,name:i.MDG_KPI_NAME,enabled:["true","enabled"].includes(String(i.MDG_KPI_VISIBILITY).toLowerCase()),userEnabled:u?u.KpiVisibility===!0&&u.IsActive===!0:!0}});c(v),m(C),P({}),w({}),A(!1)}},[n,s]);x.useEffect(()=>{e?(A(!0),V()):d(0)},[e,V]);const L=v=>{c(C=>C.map(i=>i.id===v?{...i,userEnabled:!i.userEnabled}:i)),P(C=>({...C,[v]:!0}))},F=v=>{m(C=>C.map(i=>i.id===v?{...i,userEnabled:!i.userEnabled}:i)),w(C=>({...C,[v]:!0}))},T=(v,C)=>{c(i=>i.map(u=>u.id===v?{...u,chartType:C}:u)),P(i=>({...i,[v]:!0}))},_=(v,C)=>{c(i=>i.map(u=>u.id===v?{...u,colorPallet:C}:u)),P(i=>({...i,[v]:!0}))},B=async()=>{R(!0);try{const v=Object.keys(M),C=Object.keys(I);if(v.length===0&&C.length===0){t();return}const i=p.filter(E=>v.includes(E.id)).map(E=>({Id:E.prefId,UserId:j,KpiId:E.id,KpiChartType:E.chartType,KpiChartName:E.name,KpiColPallet:E.colorPallet,KpiSequence:Number(E.sequence),KpiColumn:E.column,KpiVisibility:E.userEnabled,IsActive:E.userEnabled,KpiType:"KPI Metrics"})),u=g.filter(E=>C.includes(E.id)).map(E=>({Id:E.prefId,UserId:j,KpiId:E.id,KpiChartType:"REPORT",KpiChartName:E.name,KpiColPallet:"",KpiSequence:0,KpiColumn:"",KpiVisibility:E.userEnabled,IsActive:E.userEnabled,KpiType:"KPI Reports"})),G=[...i,...u];await new Promise((E,b)=>{te(`/${le}${me.DASHBOARD_APIS.SAVE_USER_CONFIG}`,"post",E,b,G)}),a?a():t()}catch{t()}finally{R(!1)}};return O(Gt,{open:e,onClose:t,fullWidth:!0,maxWidth:"md",children:[o(Ft,{children:h("Manage Dashboard")}),o(Ut,{dividers:!0,children:S?o(W,{sx:{display:"flex",justifyContent:"center",p:3},children:o(qe,{})}):O(Ce,{children:[o(W,{sx:{borderBottom:1,borderColor:"divider",mb:2},children:O(b2,{value:l,onChange:K,"aria-label":"dashboard management tabs",children:[o(tt,{label:h("KPI Metrics"),id:"dashboard-tab-0","aria-controls":"dashboard-tabpanel-0"}),o(tt,{label:h("KPI Reports"),id:"dashboard-tab-1","aria-controls":"dashboard-tabpanel-1"})]})}),o(Rt,{value:l,index:0,children:o(nt,{children:p.map((v,C)=>O(Fe.Fragment,{children:[C>0&&o(at,{}),o(rt,{children:O(N,{container:!0,spacing:2,alignItems:"center",children:[o(N,{item:!0,xs:4,children:o(it,{primary:o(U,{variant:"body1",sx:{fontWeight:v.enabled?"normal":"light",color:v.enabled?"text.primary":"text.disabled"},children:h(v.name)}),secondary:o(U,{variant:"body2",color:"text.secondary",sx:{fontStyle:v.enabled?"normal":"italic",color:v.enabled?"text.secondary":"text.disabled"},children:`${h("Column")}: ${v.column}`})})}),o(N,{item:!0,xs:3,children:o(De,{fullWidth:!0,size:"small",children:o(Ge,{value:v.chartType,onChange:i=>T(v.id,i.target.value),displayEmpty:!0,children:b0(v.chartType)?C0.map(i=>o(Se,{value:i.value,children:i.label},i.value)):g0.map(i=>o(Se,{value:i.value,children:i.label},i.value))})})}),o(N,{item:!0,xs:3,children:o(De,{fullWidth:!0,size:"small",children:o(Ge,{value:v.colorPallet||"default",onChange:i=>_(v.id,i.target.value),renderValue:i=>i||"Select Palette",children:m0.map(i=>o(Se,{value:i.value,children:i.label},i.value))})})}),o(N,{item:!0,xs:2,sx:{textAlign:"right"},children:o(Ue,{edge:"end",checked:v.userEnabled,onChange:()=>L(v.id),disabled:!v.enabled})})]})})]},v.id))})}),o(Rt,{value:l,index:1,children:o(nt,{children:g.map((v,C)=>O(Fe.Fragment,{children:[C>0&&o(at,{}),o(rt,{children:O(N,{container:!0,spacing:2,alignItems:"center",children:[o(N,{item:!0,xs:9,children:o(it,{primary:o(U,{variant:"body1",sx:{fontWeight:v.enabled?"normal":"light",color:v.enabled?"text.primary":"text.disabled"},children:h(v.name)})})}),o(N,{item:!0,xs:3,sx:{textAlign:"right"},children:o(Ue,{edge:"end",checked:v.userEnabled,onChange:()=>F(v.id),disabled:!v.enabled})})]})})]},v.id))})})]})}),O(x2,{children:[o(ue,{onClick:t,disabled:D,children:h("Cancel")}),o(ue,{onClick:B,variant:"contained",color:"primary",disabled:D||Object.keys(M).length===0&&Object.keys(I).length===0,children:h(D?"Saving...":"Save Changes")})]})]})},$t=e=>["First","Second","Third"][e%3],v0=(e,t,a)=>{var g,m,S,A,D;if(!e||typeof e!="object"||Array.isArray(e))return null;const n=(g=e==null?void 0:e.graphDetails)==null?void 0:g.chartType,s=(S=(m=n==null?void 0:n.toString())==null?void 0:m.trim())==null?void 0:S.toUpperCase(),r=((A=e==null?void 0:e.graphDetails)==null?void 0:A.graphName)||"Untitled Chart",l=((D=e==null?void 0:e.graphDetails)==null?void 0:D.color)||"Pallet 1",d=(e==null?void 0:e.Sequence)||0,p=(e==null?void 0:e.id)||t,c=(e==null?void 0:e.column)||$t(p);if(!s||!r)return null;if(["PIE","DONUT"].includes(s)){const R=e==null?void 0:e.label,M=e==null?void 0:e.series;return!Array.isArray(R)||!Array.isArray(M)?null:{id:p,column:c,graphDetails:{chartType:s,graphName:r,colorPallete:l,KpiSequence:d},label:R,series:M}}return Array.isArray(e==null?void 0:e.data)?{id:p,column:c,graphDetails:{chartType:s,graphName:r,colorPallete:l,KpiSequence:d},data:e.data}:null},y0=(e=0)=>{const{customError:t}=v2(),[a,n]=x.useState([]),[s,r]=x.useState(!0),[l,d]=x.useState([]),[p,c]=x.useState([]),[g,m]=x.useState([]),[S,A]=x.useState({}),[D,R]=x.useState([]),M=Z(T=>{var _;return((_=T.commonFilter)==null?void 0:_.Dashboard)||{}}),P=Z(T=>T.applicationConfig),I=Z(T=>T.userManagement.userData),w=(I==null?void 0:I.user_id)||"",h=x.useRef(!1),y=x.useRef(!1),j=x.useCallback(async()=>{if(!y.current){y.current=!0,r(!0),h.current=!1;try{const T=await K("KPI Metrics"),_=await K("KPI Reports"),B=await V(T,"KPI Metrics"),v=await V(_,"KPI Reports"),C=await L(T,B);R(v),n(C),c(_),m([...B,...v]),h.current=!0}catch(T){t(de.DASHBOARD_REFRESH_FAILED,T)}finally{y.current=!1,r(!1)}}},[]);x.useEffect(()=>{e>0&&j()},[e,j]);const K=async(T="KPI Metrics")=>{const _={decisionTableId:null,decisionTableName:"MDG_MAT_DYNAMIC_DASHBOARD_DT",version:"v3",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":"US","MDG_CONDITIONS.MDG_MAT_ROLE":"MDM Steward","MDG_CONDITIONS.MDG_KPI_TYPE":T}]},B=P.environment==="localhost"?`/${st}${me.INVOKE_RULES.LOCAL}`:`/${st}${me.INVOKE_RULES.PROD}`;try{const v=await new Promise((C,i)=>{te(B,"post",u=>{var E,b,k;const G=((k=(b=(E=u==null?void 0:u.data)==null?void 0:E.result)==null?void 0:b[0])==null?void 0:k.MDG_DYNAMIC_DASHBOARD_ACTION_TYPE)||[];C(G)},i,_)});return T==="KPI Metrics"?d(v):c(v),v}catch(v){return t(T==="KPI Metrics"?de.DECISION_TABLE_FETCH_ERROR:de.REPORT_CONFIG_FETCH_ERROR,v),[]}},V=async(T,_)=>{try{let B=await new Promise(v=>{te(`/${le}${me.DASHBOARD_APIS.FETCH_USER_CONFIG}?userId=${w}&kpiType=${encodeURIComponent(_)}`,"get",C=>v((C==null?void 0:C.body)||[]),()=>v([]))});if(!B.length&&T.length){const v=T.map(C=>{var i;return{Id:null,UserId:w,KpiId:C.MDG_KPI_ID,KpiChartType:_==="KPI Metrics"?C.MDG_KPI_GRAPH_TYPE:null,KpiChartName:C.MDG_KPI_NAME,KpiColPallet:_==="KPI Metrics"?C.MDG_KPI_COLOR_PALLET:null,KpiSequence:Number(C.MDG_KPI_GRAPH_SEQUENCE),KpiColumn:(i=C.MDG_KPI_GRAPH_COLUMN)==null?void 0:i.toLowerCase(),KpiVisibility:!0,IsActive:!0,KpiType:_}});await new Promise((C,i)=>{te(`/${le}${me.DASHBOARD_APIS.SAVE_USER_CONFIG}`,"post",C,i,v)}),B=v}return B}catch(B){return t(de.USER_PREFERENCES_FETCH_ERROR,B),[]}},L=async(T,_)=>{if(!T.length)return[];try{const B={};T.forEach(E=>{E.MDG_KPI_ID&&E.MDG_KPI_ENDPOINT&&(B[E.MDG_KPI_ID]=E.MDG_KPI_ENDPOINT.replace(/^\//,""))});const v=T.filter(E=>["TRUE","ENABLED"].includes((E.MDG_KPI_VISIBILITY||"").toString().toUpperCase())).map(E=>E.MDG_KPI_ID),C=_.filter(E=>E.KpiVisibility===!0&&E.IsActive===!0).map(E=>E.KpiId),i=C.length>0?v.filter(E=>C.includes(E)):v,u={},G=await Promise.all(i.map(E=>{var X,ve,ye;const b=_.find(he=>he.KpiId===E),k=T.find(he=>he.MDG_KPI_ID===E),ne=B[E];if(!ne)return Promise.resolve(null);const J=parseInt(E.split("_")[1])-1,ae={FromDate:"2024-01-01",ToDate:"2025-12-31",Requestor:"",KpiId:E,Module:"Material",UserId:w,Priority:"",Region:(M==null?void 0:M.selectedRegion)||"",ReqType:((X=M==null?void 0:M.selectedRequestType)==null?void 0:X.join(","))||"",ReqStatus:((ve=M==null?void 0:M.selectedRequestStatus)==null?void 0:ve.join(","))||"",GraphType:(b==null?void 0:b.KpiChartType)||(k==null?void 0:k.MDG_KPI_GRAPH_TYPE)||"",KpiName:(b==null?void 0:b.KpiChartName)||(k==null?void 0:k.MDG_KPI_NAME)||"",ColPallet:(b==null?void 0:b.KpiColPallet)||(k==null?void 0:k.MDG_KPI_COLOR_PALLET)||"",GraphColumn:(b==null?void 0:b.KpiColumn)||((ye=k==null?void 0:k.MDG_KPI_GRAPH_COLUMN)==null?void 0:ye.toLowerCase())||$t(J),GraphSequence:(b==null?void 0:b.KpiSequence)||Number(k==null?void 0:k.MDG_KPI_GRAPH_SEQUENCE)||J+1};return u[E]=ae,new Promise(he=>{te(`/${le}/counts/${ne}`,"post",Jt=>he(v0(Jt.body,J+1,J)),()=>he(null),ae)})}));return A(u),G.filter(Boolean)}catch(B){return t(de.GRAPH_DATA_FETCH_ERROR,B),[]}},F=async()=>{if(!(y.current||h.current)){y.current=!0,r(!0);try{const T=await K("KPI Metrics"),_=await K("KPI Reports"),B=await V(T,"KPI Metrics"),v=await V(_,"KPI Reports"),C=await L(T,B);n(C),R(v),c(_),m([...B,...v]),h.current=!0}catch(T){t(de.DASHBOARD_INIT_FAILED,T)}finally{y.current=!1,r(!1)}}};return x.useEffect(()=>(F(),()=>{}),[w]),x.useEffect(()=>{(async()=>{if(!(!h.current||y.current)){y.current=!0,r(!0);try{const _=await L(l,g);n(_)}catch(_){t(de.FILTER_CHANGE_UPDATE_FAILED,_)}finally{y.current=!1,r(!1)}}})()},[M]),{cards:a,reportConfig:p,loading:s,decisionTableConfig:l,userPreferences:g,kpiPayloadMap:S,kpiReportPrefs:D,refreshDashboard:j}},F0=()=>{const[e,t]=x.useState(!1),[a,n]=x.useState(!1),[s,r]=x.useState(0),{cards:l,loading:d,decisionTableConfig:p,userPreferences:c,reportConfig:g,kpiReportPrefs:m,refreshDashboard:S}=y0(s),{t:A}=xe(),D=x.useCallback(()=>{r(R=>R+1),n(!1)},[]);return O(W,{sx:{height:"100vh",overflow:"hidden",display:"flex",flexDirection:"column"},children:[O(W,{sx:{position:"sticky",top:0,bgcolor:"background.default",p:2},children:[O(be,{direction:"row",justifyContent:"space-between",alignItems:"center",children:[O(W,{children:[o(U,{variant:"h3",children:o("strong",{children:A("Dashboard")})}),o(U,{variant:"body2",color:"text.secondary",children:A("This view displays various metrics related to Master Data")})]}),O(be,{direction:"row",alignItems:"center",spacing:1,children:[o(ue,{variant:"outlined",startIcon:o(j2,{}),color:"primary",onClick:()=>n(!0),size:"small",sx:{mr:"20px !important"},children:A("Manage Dashboard")}),o(U,{variant:"body2",children:A("KPI Metrics")}),o(Ue,{checked:e,onChange:()=>t(R=>!R),color:"primary"}),o(U,{variant:"body2",children:A("KPI Reports")})]})]}),!e&&O(W,{mt:2,children:[o(mn,{}),o(p0,{})]})]}),o(W,{sx:{flex:1,overflowY:"auto",p:2},children:e?o(Mn,{reportConfig:g,kpiReportPrefs:m,loading:d},`reports-${s}`):o(xn,{cards:l,loading:d},`charts-${s}`)}),o(x0,{open:a,onClose:()=>n(!1),onSave:D,decisionTableConfig:p,userPreferences:c,reportConfig:g})]})};export{F0 as default};
