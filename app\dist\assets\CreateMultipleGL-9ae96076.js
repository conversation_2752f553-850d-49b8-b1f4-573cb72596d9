import{r,s as mt,b as ht,q as O,u as ft,bh as Dt,a as t,bX as yt,j as m,V as J,aF as Q,T as D,I as V,aG as Fe,W as Y,x as Z,B as Ne,ar as vt,E as St,X as x,t as G,C as At,z as wt,aj as bt,G as y,al as kt,b1 as Lt,h as Rt,aC as Pe,aD as It,aE as Tt,aB as ee,cb as Mt,cc as Gt,F as Bt,K as w,eV as E,ai as Ft,bq as Oe}from"./index-17b8d91e.js";import{d as Nt}from"./ArrowCircleLeftOutlined-2a09f8e2.js";import{l as Ee}from"./lookup-1dcf10ac.js";import{R as Pt}from"./ReusableAttachementAndComments-bab6bbfc.js";import{d as Ot}from"./AttachFileOutlined-e3968291.js";import"./CloudUpload-27b6d63e.js";import"./utilityImages-067c3dc2.js";import"./Add-98854918.js";import"./Delete-9f4d7a45.js";const Qt=()=>{var De;const[z,te]=r.useState(!1),[_e,Et]=r.useState("1"),[j,$e]=r.useState([]),[ae,b]=r.useState(""),[qe,K]=r.useState(!1),[_t,k]=r.useState(!1),[Ve,S]=r.useState(!1),[ze,L]=r.useState(!1),[$t,R]=r.useState(!0),[je,I]=r.useState(!1),[Ke,oe]=r.useState(!1),[ne,se]=r.useState(!0),[Xe,h]=r.useState(!1),[le,B]=r.useState(!1),[Ue,re]=r.useState(!1),[X,ie]=r.useState(""),[_,We]=r.useState([]),[He,ce]=r.useState(!1),[Je,de]=r.useState(!1),[U,Qe]=r.useState(""),[Ye,ue]=r.useState(!1),[Ze,xe]=r.useState([]),et=mt(),W=ht();O(e=>e.generalLedger.MultipleGLData),ft();const F=O(e=>e.generalLedger.MultipleGLData);O(e=>e.appSettings);let A=O(e=>e.generalLedger.handleMassMode);console.log("massHandleType",A);let u=O(e=>e.userManagement.userData);const[ge,tt]=r.useState(0),at=(e,o)=>{const a=g=>{et(Ft({keyName:e,data:g.body})),tt(n=>n+1)},i=g=>{console.log(g)};w(`/${E}/data/${o}`,"get",a,i)},ot=()=>{var e,o;(o=(e=Ee)==null?void 0:e.generalLedger)==null||o.map(a=>{at(a==null?void 0:a.keyName,a==null?void 0:a.endPoint)})},nt=()=>{var e,o;ge==((o=(e=Ee)==null?void 0:e.generalLedger)==null?void 0:o.length)?te(!1):te(!0)};r.useEffect(()=>{nt()},[ge]),r.useEffect(()=>{ot()},[]),r.useEffect(()=>{Qe(Dt("GL"))},[]);const T=F==null?void 0:F.map((e,o)=>{var g,n,C,f,s,d,p,v,N,P;const a=e,i=(e==null?void 0:e.viewData)||{};return{id:o,chartOfAccount:(a==null?void 0:a.ChartOfAccount)||"",companyCode:(a==null?void 0:a.CompCode)||"",glAccount:(a==null?void 0:a.GLAccount)||"",accountType:((n=(g=i["Type/Description"]["Control in COA"])==null?void 0:g.find(c=>(c==null?void 0:c.fieldName)==="Account Type"))==null?void 0:n.value)||"",accountGroup:((f=(C=i["Type/Description"]["Control in COA"])==null?void 0:C.find(c=>(c==null?void 0:c.fieldName)==="Account Group"))==null?void 0:f.value)||"",shortText:((d=(s=i["Type/Description"].Description)==null?void 0:s.find(c=>(c==null?void 0:c.fieldName)==="Short Text"))==null?void 0:d.value)||"",longText:((v=(p=i["Type/Description"].Description)==null?void 0:p.find(c=>(c==null?void 0:c.fieldName)==="Long Text"))==null?void 0:v.value)||"",accountCurrency:((P=(N=i["Control Data"]["Account Control in Company Code"])==null?void 0:N.find(c=>(c==null?void 0:c.fieldName)==="Account Currency"))==null?void 0:P.value)||""}}),st=[{field:"glAccount",headerName:"GL Account",editable:!1,flex:1,renderCell:e=>{const o=_.find(a=>a.generalLedger===e.value);return console.log(o,"isDirectMatch"),console.log(e,"params"),o&&o.code===400?t(D,{sx:{fontSize:"12px",color:"red"},children:e.value}):t(D,{sx:{fontSize:"12px"},children:e.value})}},{field:"chartOfAccount",headerName:"Chart Of Account",editable:!1,flex:1},{field:"companyCode",headerName:"Company Code",editable:!1,flex:1},{field:"accountType",headerName:"Account Type",editable:!1,flex:1},{field:"accountGroup",headerName:"Account Group",editable:!1,flex:1},{field:"shortText",headerName:"Short Text",editable:!1,flex:1,renderCell:e=>{const o=Ze.includes(e.row.profitCenterName);return t(D,{sx:{fontSize:"12px",color:o?"red":"inherit"},children:e.value})}},{field:"longText",headerName:"Long Text",editable:!1,flex:1},{field:"accountCurrency",headerName:"Account Currency",editable:!1,flex:1}],l=(e,o)=>{const a=e==null?void 0:e.find(i=>(i==null?void 0:i.fieldName)===o);return a?a.value:""};var M=F.map(e=>{var o,a,i,g,n,C,f,s,d,p,v,N,P,c,ye,ve,Se,Ae,we,be,ke,Le,Re,Ie,Te,Me,Ge,Be;return{GeneralLedgerID:"",Action:A==="Create"?"I":"U",RequestID:"",TaskStatus:"",TaskId:"",Remarks:X||"",Info:"",CreationId:"",EditId:"",DeleteId:"",MassCreationId:"",MassEditId:"",MassDeleteId:"",RequestType:A==="Create"?"Mass Create":"Mass Change",ReqCreatedBy:u==null?void 0:u.user_id,ReqCreatedOn:u!=null&&u.createdOn?"/Date("+(u==null?void 0:u.createdOn)+")/":"",ReqUpdatedOn:"",RequestStatus:"",Testrun:le,COA:e==null?void 0:e.ChartOfAccount,CompanyCode:e==null?void 0:e.CompCode,CoCodeToExtend:"",GLAccount:e==null?void 0:e.GLAccount,Accounttype:l((o=e==null?void 0:e.viewData["Type/Description"])==null?void 0:o["Control in COA"],"Account Type"),AccountGroup:l((a=e==null?void 0:e.viewData["Type/Description"])==null?void 0:a["Control in COA"],"Account Group"),GLname:l((i=e==null?void 0:e.viewData["Type/Description"])==null?void 0:i.Description,"Short Text"),Description:l((g=e==null?void 0:e.viewData["Type/Description"])==null?void 0:g.Description,"Long Text"),TradingPartner:l((n=e==null?void 0:e.viewData["Type/Description"])==null?void 0:n["Consolidation Data in COA"],"Trading Partner"),GroupAccNo:l((C=e==null?void 0:e.viewData["Type/Description"])==null?void 0:C["Consolidation Data in COA"],"Group Account Number"),AccountCurrency:l((f=e==null?void 0:e.viewData["Control Data"])==null?void 0:f["Account Control in Company Code"],"Account Currency"),Exchangerate:l((s=e==null?void 0:e.viewData["Control Data"])==null?void 0:s["Account Control in Company Code"],"Exchange Rate Difference Key"),Balanceinlocrcy:l((d=e==null?void 0:e.viewData["Control Data"])==null?void 0:d["Account Control in Company Code"],"Only Balance In Local Currency")===!0?"X":"",Taxcategory:l((p=e==null?void 0:e.viewData["Control Data"])==null?void 0:p["Account Control in Company Code"],"Tax Category"),Pstnwotax:l((v=e==null?void 0:e.viewData["Control Data"])==null?void 0:v["Account Control in Company Code"],"Posting Without Tax Allowed")===!0?"X":"",ReconAcc:l((N=e==null?void 0:e.viewData["Control Data"])==null?void 0:N["Account Control in Company Code"],"Recon. Account For Account Type"),Valuationgrp:l((P=e==null?void 0:e.viewData["Control Data"])==null?void 0:P["Account Control in Company Code"],"Valuation Group"),AlterAccno:l((c=e==null?void 0:e.viewData["Control Data"])==null?void 0:c["Account Control in Company Code"],"Alternative Account Number"),Openitmmanage:l((ye=e==null?void 0:e.viewData["Control Data"])==null?void 0:ye["Account Management in Company Code"],"Open Item Management")===!0?"X":"",Sortkey:l((ve=e==null?void 0:e.viewData["Control Data"])==null?void 0:ve["Account Management in Company Code"],"Sort Key"),CostEleCategory:l((Se=e==null?void 0:e.viewData["Control Data"])==null?void 0:Se["Account Management in Company Code"],"Sort Key"),FieldStsGrp:l((Ae=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:Ae["Control of Document creation in Company Code"],"Field Status Group"),PostAuto:l((we=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:we["Control of Document creation in Company Code"],"Post Automatically Only")===!0?"X":"",Supplementautopost:l((be=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:be["Control of Document creation in Company Code"],"Supplement Auto Postings")===!0?"X":"",Planninglevel:l((ke=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:ke["Bank/Financial Details in Company Code"],"Planning Level"),Relvnttocashflow:l((Le=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:Le["Bank/Financial Details in Company Code"],"Relevant To Cash Flows")===!0?"X":"",HouseBank:l((Re=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:Re["Bank/Financial Details in Company Code"],"House Bank"),AccountId:l((Ie=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:Ie["Bank/Financial Details in Company Code"],"Account ID"),Interestindicator:l((Te=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:Te["Interest Calculation Information in Company Code"],"Interest Indicator"),ICfrequency:l((Me=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:Me["Interest Calculation Information in Company Code"],"Interest Calculation Frequency"),KeydateofLIC:l((Ge=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:Ge["Interest Calculation Information in Company Code"],"Key Date Of Last Interest Calculation"),LastIntrstundate:l((Be=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:Be["Interest Calculation Information in Company Code"],"Date Of Last Interest Run"),AccmngExistsys:"",Infationkey:"",Tolerancegrp:"",AuthGroup:"",AccountClerk:"",ReconAccReady:"",PostingBlocked:"",PlanningBlocked:""}});const lt=e=>{e.length>0?(B(!0),console.log("selectedIds1",e)):B(!1),console.log("selectedIds",e),$e(e)},H=()=>{K(!0)},rt=()=>{Je?(K(!1),de(!1)):(K(!1),W("/masterDataCockpit/generalLedger"))},it=()=>{ue(!0)},Ce=()=>{ue(!1)},$=()=>{oe(!0)},pe=()=>{oe(!1)},ct=()=>{},dt=()=>{const e=T.filter((n,C)=>j.includes(C));console.log("selectedData",e);const o=e.map(n=>({...M[n==null?void 0:n.id]}));let a=M;a=o,console.log("selectedProfitCenterRows",o);const i=n=>{if(n.statusCode===200){console.log("success"),S("Create"),b(`Mass General Ledger Sent for Review with ID NLM${n.body}`),L("success"),R(!1),I(!0),H(),k(!0),h(!1);const C={artifactId:U,createdBy:u==null?void 0:u.emailId,artifactType:"GeneralLedger",requestId:`NLM${n==null?void 0:n.body}`},f=d=>{console.log("Second API success",d)},s=d=>{console.error("Second API error",d)};w(`/${Oe}/documentManagement/updateDocRequestId`,"post",f,s,C)}else S("Error"),I(!1),b("Failed Submitting the General Ledger for Review "),L("danger"),R(!1),k(!0),$(),h(!1);handleClose(),h(!1)},g=n=>{console.log(n)};w(`/${E}/massAction/generalLedgersSubmitForReview`,"post",i,g,a)},ut=()=>{const e=T.filter((n,C)=>j.includes(C));console.log("selectedData",e);const o=e.map(n=>({...M[n==null?void 0:n.id]}));let a=M;a=o,console.log("selectedProfitCenterRows",o);const i=n=>{if(n.statusCode===200){console.log("success"),S("Create"),b(`Mass General Ledger Sent for Review with ID CGM${n.body}`),L("success"),R(!1),I(!0),H(),k(!0),h(!1);const C={artifactId:U,createdBy:u==null?void 0:u.emailId,artifactType:"GeneralLedger",requestId:`CGM${n==null?void 0:n.body}`},f=d=>{console.log("Second API success",d)},s=d=>{console.error("Second API error",d)};w(`/${Oe}/documentManagement/updateDocRequestId`,"post",f,s,C)}else S("Error"),I(!1),b("Failed Submitting the General Ledger for Review "),L("danger"),R(!1),k(!0),$(),h(!1);handleClose(),h(!1)},g=n=>{console.log(n)};w(`/${E}/massAction/changeGeneralLedgersSubmitForReview`,"post",i,g,a)},gt=()=>{h(!0);const e=T.filter((s,d)=>j.includes(d));console.log("selectedData",e);const o=e.map(s=>({...M[s==null?void 0:s.id]}));console.log("selectedProfitCenterRows",o);const a=[];o.map(s=>{var p;var d={glName:(p=s==null?void 0:s.GLname)==null?void 0:p.toUpperCase(),compCode:s==null?void 0:s.CompanyCode};a.push(d)}),console.log("duplicateCheckPayload",a);let i=M;i=o;const g=s=>{s.statusCode===400?(We(s.body),ce(!0),h(!1)):(S("Create"),console.log("success"),S("Create"),b("All Data has been Validated.General Ledger can be Send for Review"),L("success"),R(!1),I(!0),H(),k(!0),de(!0),(a.glName!==""||a.compCode!=="")&&w(`/${E}/alter/fetchGlNameNCompCodeDupliChkMass`,"post",n,C,a))},n=s=>{if(console.log("dataaaa",s),s.body.length===0||!s.body.some(d=>a.some(p=>{var v;return((v=p==null?void 0:p.glName)==null?void 0:v.toUpperCase())===d.matches[0]})))h(!1),se(!1),B(!0);else{const d=s.body.map(p=>p.matches[0]);h(!1),S("Duplicate Check"),I(!1),b("There is a direct match for the General Ledger name."),L("danger"),R(!1),k(!0),$(),se(!0),xe(d)}},C=s=>{console.log(s)},f=s=>{console.log(s)};w(`/${E}/massAction/validateMassGeneralLedger`,"post",g,f,i)},Ct=[{field:"generalLedger",headerName:"General Ledger",editable:!1,flex:1},{field:"error",headerName:"Error",editable:!1,flex:1}],me=(De=_==null?void 0:_.filter(e=>(e==null?void 0:e.code)===400))==null?void 0:De.map((e,o)=>{var a;if(e.code===400)return{id:o,generalLedger:e==null?void 0:e.generalLedger,error:(a=e==null?void 0:e.status)==null?void 0:a.message}});console.log("validationRows",me);const q=()=>{B(!0),re(!1)},he=()=>{B(!1),re(!0)},pt=(e,o)=>{const a=e.target.value;if(a.length>0&&a[0]===" ")ie(a.trimStart());else{let i=a.toUpperCase();ie(i)}},fe=()=>{ce(!1)};return t(Bt,{children:z===!0?t(yt,{}):m("div",{children:[m(J,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:Ue,onClose:q,children:[m(Q,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(D,{variant:"h6",children:"Remarks"}),t(V,{sx:{width:"max-content"},onClick:q,children:t(Fe,{})})]}),t(Y,{sx:{padding:".5rem 1rem"},children:t(Z,{children:t(Ne,{sx:{minWidth:400},children:t(vt,{sx:{height:"auto"},fullWidth:!0,children:t(St,{sx:{backgroundColor:"#F5F5F5"},value:X,onChange:pt,multiline:!0,placeholder:"Enter Remarks",inputProps:{maxLength:254}})})})})}),m(x,{sx:{display:"flex",justifyContent:"end"},children:[t(G,{sx:{width:"max-content",textTransform:"capitalize"},onClick:q,children:"Cancel"}),t(G,{className:"button_primary--normal",type:"save",onClick:()=>{h(!0),q(),A==="Create"?dt():ut()},variant:"contained",children:"Submit"})]})]}),t(At,{dialogState:Ke,openReusableDialog:$,closeReusableDialog:pe,dialogTitle:Ve,dialogMessage:ae,handleDialogConfirm:pe,dialogOkText:"OK",handleExtraButton:ct,dialogSeverity:ze}),je&&t(wt,{openSnackBar:qe,alertMsg:ae,handleSnackBarClose:rt}),m("div",{style:{...bt,backgroundColor:"#FAFCFF"},children:[t(y,{container:!0,sx:kt,children:m(y,{item:!0,md:12,sx:{display:"flex",marginBottom:"0"},children:[m(y,{item:!0,md:11,sx:{display:"flex"},children:[t(y,{children:t(V,{color:"primary","aria-label":"upload picture",component:"label",sx:Lt,children:t(Nt,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{W("/masterDataCockpit/generalLedger")}})})}),t(y,{children:A==="Create"?m(y,{item:!0,md:12,children:[t(D,{variant:"h3",children:t("strong",{children:"Create Multiple General Ledger"})}),t(D,{variant:"body2",color:"#777",children:"This view creates multiple General Ledger"})]}):m(y,{item:!0,md:12,children:[t(D,{variant:"h3",children:t("strong",{children:"Change Multiple General Ledger"})}),t(D,{variant:"body2",color:"#777",children:"This view changes multiple General Ledger"})]})})]}),t(y,{item:!0,md:1,sx:{display:"flex"},children:t(Rt,{title:"Upload documents if any",arrow:!0,children:t(V,{onClick:it,children:t(Ot,{})})})}),m(J,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:Ye,onClose:Ce,children:[t(Q,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:t(D,{variant:"h6",children:"Add Attachment"})}),t(Y,{sx:{padding:".5rem 1rem"},children:t(Z,{children:t(Ne,{sx:{minWidth:400},children:t(Pt,{title:"GeneralLedger",useMetaData:!1,artifactId:U,artifactName:"GeneralLedger"})})})}),t(x,{children:t(G,{onClick:Ce,children:"Close"})})]})]})}),t(y,{item:!0,sx:{position:"relative"},children:t(Z,{children:t(Pe,{isLoading:z,width:"100%",title:"General Ledger Master List ("+T.length+")",rows:T,columns:st,pageSize:10,getRowIdValue:"id",hideFooter:!1,checkboxSelection:!0,disableSelectionOnClick:!0,status_onRowSingleClick:!0,onRowsSelectionHandler:lt,callback_onRowSingleClick:e=>{console.log("paramss",e);const o=e.row.glAccount,a=F.find(i=>i.GLAccount===o);console.log(a,"pppp"),W(`/masterDataCockpit/generalLedger/createMultipleGL/editMultipleGL/${o}`,{state:{rowViewData:a,selectedRow:e.row}})},stopPropagation_Column:"action",status_onRowDoubleClick:!0})})})]}),t(It,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:m(Tt,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},value:_e,children:[t(G,{variant:"contained",size:"small",sx:{...ee,mr:1},onClick:gt,disabled:!le,children:"Validate"}),A==="Create"?t(G,{variant:"contained",size:"small",sx:{...ee},onClick:he,disabled:ne,children:"Submit for Review"}):A==="Change"?t(G,{variant:"contained",size:"small",sx:{...ee},onClick:he,disabled:ne,children:"Submit for Review"}):""]})}),m(J,{open:He,fullWidth:!0,onClose:fe,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[m(Q,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(D,{variant:"h6",color:"red",children:"Errors"}),t(V,{sx:{width:"max-content"},onClick:fe,children:t(Fe,{})})]}),t(Y,{sx:{padding:".5rem 1rem"},children:t(Pe,{isLoading:z,width:"100%",title:"General Ledger Master List ("+T.length+")",rows:me,columns:Ct,pageSize:10,getRowIdValue:"id",hideFooter:!0,checkboxSelection:!1,disableSelectionOnClick:!0,status_onRowSingleClick:!0,stopPropagation_Column:"action",status_onRowDoubleClick:!0})}),t(x,{sx:{display:"flex",justifyContent:"end"}})]}),t(Mt,{sx:{color:"#fff",zIndex:e=>e.zIndex.drawer+1},open:Xe,children:t(Gt,{color:"inherit"})})]})})};export{Qt as default};
