/* .MuiTextField-root ::placeholder{
    font-size: 14px !important;
    font-style: italic;
} */
/* html{
    font-size: 14px;
} */

/* .css-z7uhs0-MuiStepConnector-line {
  margin-top: 50% !important;
} */
/* .dropzone {
  transition: background-color 0.3s ease;
  transition: border 0.3s ease;

}

.dropzone.dragover {
  background-color: #f0f0f0;
}
.dragover{
  border: 2px dashed #3b30c8 !important;
}

.icon {
  font-size: 60px;
  margin-bottom: 10px;
  transition: transform 0.3s ease;
}

/* .dropzone.dragover .icon {
  transform: rotate(360deg);
} */

p {
  margin: 0;
  transition: opacity 0.3s ease;
}

.dropzone.dragover p {
  opacity: 0;
}

.rs-btn-primary{
  background-color: #3b30c8 !important;
}
.MuiButton-contained:disabled {
  background-color:#636363 !important;
  color: white !important;
}
.rs-calendar-table-cell-selected .rs-calendar-table-cell-content {
background-color: #3b30c8 !important;
color: #fff !important;
}
.rs-btn-link{
  color: #3b30c8 !important;
}
.PrivateSwipeArea-root {
  width: 0 !important; 
}

.rs-btn:disabled, .rs-btn.rs-btn-disabled{
  background-color:#636363 !important;
  color: white !important;
}
.MuiButton-contained{
  background-color:#3b30c8 !important;
  min-width: max-content !important;
  padding: 6px 12px !important;
  text-transform: capitalize !important;
  height: 2rem !important;
  color: white !important;

}
.MuiButton-outlined{
  min-width: max-content !important;
  padding: 6px 12px !important;
  text-transform: capitalize !important;
  height: 2rem !important;
}
.btn-mr{
  margin-right: .5rem !important;
}
.btn-ml{
  margin-left: .5rem !important;
}
.MuiButton-text{
  text-transform: none !important;
    height: 2rem !important;
  padding: 6px 12px !important;
  min-width: max-content !important;

}
.css-1q57m1p-MuiPaper-root-MuiSnackbarContent-root {
  background-color: #333333 !important;
}

.css-1poimk-MuiPaper-root-MuiMenu-paper-MuiPaper-root-MuiPopover-paper {
  max-height: 17rem !important;
}

.css-78trlr-MuiButtonBase-root-MuiIconButton-root {
  padding: 4px !important;
}

.rs-picker-toggle,
.rs-picker-daterange {
  width: 100% !important;
  height:100% !important;
}
.rs-picker-menu{
	z-index: 1444 !important;
}
.container_outermost {
  padding: 1.5rem 1rem;
  padding-bottom: 0px;
  background-color: #fafcff;
  box-sizing: border-box;
}

.container_outer--information {
  padding-top: 0px;
}
.container_outermost--information {
  margin-bottom: 1rem;
}
.information--header {
  margin-bottom: 0.5rem;
}
.container_filter {
  margin: 0px !important;
  margin-bottom: 1.5rem !important;
}
.container_outermost--header {
  padding-top: 0px;
}
.container_table {
  margin: 0px !important;
}
.container_table--header {
  margin-bottom: 1rem;
}
.content_overflow {
  padding: 0;
  margin: 0;
  max-height: max-content;
  background-color: #fafcff;
  overflow-y: auto;
}
.container_outer {
  padding: 0px 1rem;
}
.container_BottomNav {
  padding: .5rem 1rem;
  z-index: 3 !important;
  display: flex;
  align-items: center;
  justify-content: end !important;
}
.container_outermost-withoutScroll {
  padding: 0.5rem 0.5px;
  padding-bottom: 0px;

  background-color: #fafcff;
  box-sizing: border-box;
}
.pointer_cursor:hover {
  cursor: pointer;
}
.button_primary--normal {
  background-color: #3b30c8 !important;
  min-width: max-content !important;
  padding: 6px 12px !important;
  text-transform: capitalize !important;
  height: 2rem;
  margin-left: 0px 1rem !important;
}
.button_primary--large {
  padding: 10px 18px;
  text-transform: capitalize !important;
}
.button_outlined--normal {
  min-width: max-content !important;
  padding: 6px 12px !important;
  text-transform: capitalize !important;
  height: 2rem !important;
}
.button_margin-left {
  margin-left: 0.5rem !important;
}
.button_margin-right {
  margin-right: 0.5rem !important;
}
.iconButton_spacing--small {
  padding: 0.25rem !important;
  height: max-content !important;
}
.icon_margin-left {
  margin-left: 0.5rem !important;
}
.icon_margin-right {
  margin-right: 0.5rem !important;
}
.search_size--normal {
  height: 2rem;
}
.font--small {
  font-size: 14px !important;
}
::placeholder {
  font-size: 14px;
}
.css-o4b71y-MuiAccordionSummary-content {
  margin: 0px;
}
.MuiAccordionSummary-content {
  margin: 8px 0px !important;
}
.MuiBadge .css-1v9b394-MuiBadge-badge {
  z-index: 0;
}

.loading:after {
  overflow: hidden;
  display: inline-block;
  vertical-align: bottom;
  -webkit-animation: ellipsis steps(4, end) 900ms infinite;
  animation: ellipsis steps(4, end) 900ms infinite;
  content: "\2026"; /* ascii code for the ellipsis character */
  width: 0px;
}

@keyframes ellipsis {
  to {
    width: 1.25em;
  }
}

@-webkit-keyframes ellipsis {
  to {
    width: 1.25em;
  }
}

.filter-accordian {
  min-height: "1rem";
  margin-top: "0px !important";
  border: "1px solid";
  border-color: "#E0E0E0";
}
.filter-accordian:not(:last-child) {
  border-bottom: 0;
}
.filter-accordian:before {
  display: "none";
}

.footerOptions {
  display: "flex";
  justify-content: "flex-end";
  padding-top: "0.7rem";
  padding-bottom: "0.7rem";
}

.footerOptionsButtons {
  text-transform: "none";
  background-color: "#3B30C8";
  margin-right: "1.8rem";
}

.css-1ntv6e5-MuiSnackbar-root {
  top: 23px;
  left: 50%;
  right: auto;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
}
.MuiTabs-root button:hover {
  background-color: rgb(239, 238, 251, 0.5);
}
.ReturnHeader .MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input {
  padding: 0px !important;
}

.rs-picker-daterange-menu {
  z-index: 10000000000; /* added a large random number, you can choose it more carefully based on your use-case.*/
}
/* ITM Workspace Artifact */
.workspaceOverride .detailContainerOverride {
  height: calc(100vh - 24rem) !important;
  overflow: hidden !important;
}

.workspaceOverride .detailContainerOverride #idScroll {
  height: calc(100vh - 23.6rem) !important;
}

.workspaceOverride .detailContainerOverrideWithoutSelection {
  height: calc(100vh - 16.1rem) !important;
}

.workspaceOverride .detailContainerOverrideWithoutSelection #idScroll {
  height: calc(100vh - 19.6rem) !important;
}
.css-1asemsw-MuiAutocomplete-root .MuiOutlinedInput-root.MuiInputBase-sizeSmall {
 height: 2.25em !important;
}
.css-1asemsw-MuiAutocomplete-root .MuiOutlinedInput-root.MuiInputBase-sizeSmall .MuiAutocomplete-input {
  padding-top: 0px !important;
}
.rs-picker-toggle .rs-picker-toggle-placeholder {
  font-size: 12px;
}
.rs-picker-default .rs-picker-toggle.rs-btn {
  height: 2.1em
}
.css-19qh8xo-MuiInputBase-input-MuiOutlinedInput-input {
  font-size: 12px !important;
}
.css-ojdh9f-MuiButtonBase-root-MuiMenuItem-root {
  font-size: 12px !important;
}
/* .css-1ox7ene-MuiButtonBase-root-MuiFab-root {
  z-index: 0 !important;
} */

















.abc {
  padding-bottom: 6px !important;
  padding-top: 6px !important;
}
.dropzone {
  transition: background-color 0.3s ease;
  transition: border 0.3s ease;

}

.dropzone.dragover {
  background-color: #ecebfa33;
}
.dragover{
  border: 1px dashed #3b30c8 !important;
}

.icon {
  font-size: 60px;
  margin-bottom: 10px;
  transition: transform 0.3s ease;
}

/* .dropzone.dragover .icon {
  transform: rotate(360deg);
} */

p {
  margin: 0;
  transition: opacity 0.3s ease;
}

.dropzone.dragover p {
  opacity: 0;
}

.rs-btn-primary{
  background-color: #3b30c8 !important;
}
.MuiButton-contained:disabled {
  background-color:#636363 !important;
  color: white !important;
}
.rs-calendar-table-cell-selected .rs-calendar-table-cell-content {
background-color: #3b30c8 !important;
color: #fff !important;
}
.rs-btn-link{
  color: #3b30c8 !important;
}
.rs-btn:disabled, .rs-btn.rs-btn-disabled{
  background-color:#636363 !important;
  color: white !important;
}
.MuiButton-contained{
  background-color:#3b30c8 !important;
  min-width: max-content !important;
  padding: 6px 12px !important;
  text-transform: capitalize !important;
  height: 2rem !important;
  color: white !important;

}
.MuiButton-outlined{
  min-width: max-content !important;
  padding: 6px 12px !important;
  text-transform: capitalize !important;
  height: 2rem !important;
}
.btn-mr{
  margin-right: .5rem !important;
}
.btn-ml{
  margin-left: .5rem !important;
}
.MuiButton-text{
  text-transform: none !important;
    height: 2rem !important;
  padding: 6px 12px !important;
  min-width: max-content !important;

}
.css-1q57m1p-MuiPaper-root-MuiSnackbarContent-root {
  background-color: #333333 !important;
}

.css-1poimk-MuiPaper-root-MuiMenu-paper-MuiPaper-root-MuiPopover-paper {
  max-height: 17rem !important;
}

.css-78trlr-MuiButtonBase-root-MuiIconButton-root {
  padding: 4px !important;
}

.rs-picker-toggle,
.rs-picker-daterange {
  width: 100% !important;
}
.rs-picker-menu{
    z-index: 1444 !important;
}
.container_outermost {
  padding: 1.5rem 1rem;
  padding-bottom: 0px;
  background-color: #fafcff;
  box-sizing: border-box;
}

.container_outer--information {
  padding-top: 0px;
}
.container_outermost--information {
  margin-bottom: 1rem;
}
.information--header {
  margin-bottom: 0.5rem;
}
.container_filter {
  margin: 0px !important;
  margin-bottom: 1.5rem !important;
}
.container_outermost--header {
  padding-top: 0px;
}
.container_table {
  margin: 0px !important;
}
.container_table--header {
  margin-bottom: 1rem;
}
.content_overflow {
  padding: 0;
  margin: 0;
  max-height: max-content;
  background-color: #fafcff;
  overflow-y: auto;
}
.container_outer {
  padding: 0px 1rem;
}
.container_BottomNav {
  padding: .5rem 1rem;
  z-index: 3 !important;
  display: flex;
  align-items: center;
  justify-content: end !important;
}
.container_outermost-withoutScroll {
  padding: 0.5rem 0.5px;
  padding-bottom: 0px;

  background-color: #fafcff;
  box-sizing: border-box;
}
.pointer_cursor:hover {
  cursor: pointer;
}
.button_primary--normal {
  background-color: #3b30c8 !important;
  min-width: max-content !important;
  padding: 6px 12px !important;
  text-transform: capitalize !important;
  height: 2rem;
  margin-left: 0px 1rem !important;
}
.button_primary--large {
  padding: 10px 18px;
  text-transform: capitalize !important;
}
.button_outlined--normal {
  min-width: max-content !important;
  padding: 6px 12px !important;
  text-transform: capitalize !important;
  height: 2rem !important;
}
.button_margin-left {
  margin-left: 0.5rem !important;
}
.button_margin-right {
  margin-right: 0.5rem !important;
}
.iconButton_spacing--small {
  padding: 0.25rem !important;
  height: max-content !important;
}
.icon_margin-left {
  margin-left: 0.5rem !important;
}
.icon_margin-right {
  margin-right: 0.5rem !important;
}
.search_size--normal {
  height: 2rem;
}
.font--small {
  font-size: 14px !important;
}
::placeholder {
  font-size: 14px;
}
.css-o4b71y-MuiAccordionSummary-content {
  margin: 0px;
}
.MuiAccordionSummary-content {
  margin: 8px 0px !important;
}
.MuiBadge .css-1v9b394-MuiBadge-badge {
  z-index: 0;
}

.loading:after {
  overflow: hidden;
  display: inline-block;
  vertical-align: bottom;
  -webkit-animation: ellipsis steps(4, end) 900ms infinite;
  animation: ellipsis steps(4, end) 900ms infinite;
  content: "\2026"; /* ascii code for the ellipsis character */
  width: 0px;
}

@keyframes ellipsis {
  to {
    width: 1.25em;
  }
}

@-webkit-keyframes ellipsis {
  to {
    width: 1.25em;
  }
}

.filter-accordian {
  min-height: "1rem";
  margin-top: "0px !important";
  border: "1px solid";
  border-color: "#E0E0E0";
}
.filter-accordian:not(:last-child) {
  border-bottom: 0;
}
.filter-accordian:before {
  display: "none";
}

.footerOptions {
  display: "flex";
  justify-content: "flex-end";
  padding-top: "0.7rem";
  padding-bottom: "0.7rem";
}

.footerOptionsButtons {
  text-transform: "none";
  background-color: "#3B30C8";
  margin-right: "1.8rem";
}

.css-1ntv6e5-MuiSnackbar-root {
  top: 23px;
  left: 50%;
  right: auto;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
}
.MuiTabs-root button:hover {
  background-color: rgb(239, 238, 251, 0.5);
}
.ReturnHeader .MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input {
  padding: 0px !important;
}

.rs-picker-daterange-menu {
  z-index: 10000000000; /* added a large random number, you can choose it more carefully based on your use-case.*/
}
/* ITM Workspace Artifact */
.workspaceOverride .detailContainerOverride {
  height: calc(100vh - 24rem) !important;
  overflow: hidden !important;
}

.workspaceOverride .detailContainerOverride #idScroll {
  height: calc(100vh - 23.6rem) !important;
}

.workspaceOverride .detailContainerOverrideWithoutSelection {
  height: calc(100vh - 16.1rem) !important;
}

.workspaceOverride .detailContainerOverrideWithoutSelection #idScroll {
  height: calc(100vh - 19.6rem) !important;
}
::-webkit-scrollbar-track{
    box-shadow: inset 0px 0px 2px rgba(128, 128, 128, 0.555);
    border-radius: 100px;


   }
   ::-webkit-scrollbar-track:horizontal{
       box-shadow: inset 0px 0px 2px rgba(128, 128, 128, 0.555);
       border-radius: 100px;


      }
   ::-webkit-scrollbar{
       width: 5px;

   }
   ::-webkit-scrollbar:horizontal{
       /* width: 5px; */
       height: 5px;


   }
   ::-webkit-scrollbar-thumb{
       background-color: rgba(107, 100, 100, 0.664);

       border-radius: 100px;

   }   
   ::-webkit-scrollbar-thumb:horizontal{
       background-color: rgba(107, 100, 100, 0.664);

       border-radius: 100px;

   }   
   ::-webkit-scrollbar-thumb:hover{
       background-color: rgba(146, 145, 145, 0.541);

       border-radius: 100px;

   }   
   .css-1jbbcbn-MuiDataGrid-columnHeaderTitle {
       white-space: pre-wrap !important;
       line-height: 1.2;
   }
   
   .css-13cfz5n-MuiDataGrid-root .MuiDataGrid-row:not(.MuiDataGrid-row--dynamicHeight)>.MuiDataGrid-cell{
    overflow: none !important;
   }
   .css-12rcprn-MuiTypography-root{
    font-size: 12px !important;
   }
   .Toastify__toast-theme--dark{
    background-color: #777777 !important;
   }
  
   /* .blur-background {
    filter: blur(5px);
    pointer-events: none; 
  }
  .reusable-dialog {
    z-index: 1300; 
    position: relative;
  } */
  .custom-toast-container {
    top: 30px !important;
  }