{"name": "cw-mdg", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:qa": "vite --mode qa", "dev:prod": "vite --mode prod", "build:qa": "vite build --mode qa", "build:prod": "vite build --mode prod", "build": "cross-env NODE_OPTIONS=--max-old-space-size=8192 vite build", "lint": "eslint src --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@cw/cherrywork-iwm-workspace": "file:cw-cherrywork-iwm-workspace-0.3.68-beta-1 1.tgz", "@cw/adduser": "file:cw-adduser-1.0.5.tgz", "@cw/createrole": "file:cw-createrole-1.0.21.tgz", "@cw/edituser": "file:cw-edituser-1.0.14.tgz", "@cw/mfviewandedit": "file:cw-mfviewandedit-1.0.21.tgz", "@cw/quickadduser": "file:cw-quickadduser-1.0.3.tgz", "@cw/rolesummary": "file:cw-rolesummary-1.0.10.tgz", "@cw/usersummary": "file:cw-usersummary-1.0.7.tgz", "@cw/viewandeditrole": "file:cw-viewandeditrole-1.0.18.tgz", "@cw/viewuser": "file:cw-viewuser-1.0.11.tgz", "@cw/viewgroup": "file:cw-viewgroup-1.0.9.tgz", "@cw/creategroup": "file:cw-creategroup-1.0.8.tgz", "@cw/editgroup": "file:cw-editgroup-1.0.7.tgz", "@cw/groupsummary": "file:cw-groupsummary-1.0.9.tgz", "@cw/idm_qa": "file:cw-idm_qa-0.0.40.tgz", "@cw/rds": "^0.1.9-RC.2", "@cw/workutils-email-config": "^0.2.7", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@kurkle/color": "^0.3.4", "@microsoft/fetch-event-source": "^2.0.1", "@mui/icons-material": "^5.16.7", "@mui/lab": "^5.0.0-alpha.136", "@mui/material": "^5.16.7", "@mui/styles": "^5.10.5", "@mui/x-charts": "^7.27.1", "@mui/x-data-grid": "^5.17.2", "@mui/x-date-pickers": "^7.14.0", "@progress/kendo-react-pdf": "^7.2.3", "@reduxjs/toolkit": "^1.9.5", "@rsuite/icons": "^1.3.2", "@stomp/stompjs": "^7.0.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/html2canvas": "^1.0.0", "axios": "^1.4.0", "chart.js": "^4.4.8", "cross-env": "^7.0.3", "date-fns": "^2.30.0", "dom-to-image": "^2.6.0", "exceljs": "^4.3.0", "file-saver": "^2.0.5", "framer-motion": "^12.5.0", "html2canvas": "^1.4.1", "json-as-xlsx": "^2.5.3", "lucide-react": "^0.511.0", "material-react-table": "^2.13.2", "moment": "^2.29.4", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.3.0", "react-countup": "^6.5.3", "react-csv": "^2.2.2", "react-dom": "^18.2.0", "react-draft-wysiwyg": "^1.15.0", "react-dropzone-uploader": "^2.11.0", "react-hook-form": "^7.53.2", "react-i18next": "^15.5.2", "react-material-ui-carousel": "^3.4.2", "react-player": "^2.12.0", "react-redux": "^8.1.1", "react-router-dom": "^6.14.1", "react-scripts": "5.0.1", "react-to-pdf": "^1.0.1", "react-toastify": "^10.0.5", "react-window": "^1.8.11", "react-youtube": "^10.1.0", "recharts": "^2.8.0", "rsuite": "^5.37.3", "sockjs-client": "^1.6.1", "sweetalert2": "^11.22.0", "web-vitals": "^2.1.4"}, "devDependencies": {"@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "@types/sockjs-client": "^1.5.4", "@vitejs/plugin-react": "^4.0.1", "eslint": "^8.44.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.1", "vite": "^4.4.0"}, "overrides": {"@cw/adduser": {"@mui/icons-material": "5.11.16", "@mui/material": "5.11.16"}, "@cw/createrole": {"@mui/icons-material": "5.11.16", "@mui/material": "5.11.16"}, "@cw/edituser": {"@mui/icons-material": "5.11.16", "@mui/material": "5.11.16"}, "@cw/mfviewandedit": {"@mui/icons-material": "5.11.16", "@mui/material": "5.11.16"}, "@cw/quickadduser": {"@mui/icons-material": "5.11.16", "@mui/material": "5.11.16"}, "@cw/rolesummary": {"@mui/icons-material": "5.11.16", "@mui/material": "5.11.16"}, "@cw/usersummary": {"@mui/icons-material": "5.11.16", "@mui/material": "5.11.16"}, "@cw/viewandeditrole": {"@mui/icons-material": "5.11.16", "@mui/material": "5.11.16"}, "@cw/viewuser": {"@mui/icons-material": "5.11.16", "@mui/material": "5.11.16"}}}