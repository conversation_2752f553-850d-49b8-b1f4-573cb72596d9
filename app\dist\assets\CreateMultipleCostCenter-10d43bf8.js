import{r as l,b as ja,s as Ja,q as M,u as Ka,bh as Xa,j as m,V as G,aF as H,a as o,X as W,aj as Ga,x as Q,aC as aa,G as f,F as Ha,T as g,ab as oa,aG as sa,I as z,B as na,ar as Wa,E as Qa,W as Y,t as P,C as Ya,z as Za,al as xa,b1 as eo,h as to,aE as ao,aB as Z,aD as oo,cc as so,cb as no,K as w,b$ as E,ai as ro,bq as ra}from"./index-75c1660a.js";import{d as io}from"./ArrowCircleLeftOutlined-f7b52d40.js";import{l as ia}from"./lookup-1dcf10ac.js";import{R as lo}from"./ReusableAttachementAndComments-682b0475.js";import{d as co}from"./AttachFileOutlined-872f8e38.js";import"./CloudUpload-d5d09566.js";import"./utilityImages-067c3dc2.js";import"./Add-62a207fb.js";import"./Delete-1d158507.js";const bo=()=>{var fe,ve;const[x,ee]=l.useState(!0),[la,v]=l.useState(!1),[ca,uo]=l.useState("1"),[q,da]=l.useState([]),[te,b]=l.useState(""),[ua,U]=l.useState(!1),[Co,S]=l.useState(!1),[Ca,A]=l.useState(!1),[ma,T]=l.useState(!1),[mo,k]=l.useState(!0),[pa,N]=l.useState(!1),[ga,ae]=l.useState(!1),[oe,se]=l.useState(!0),[ne,B]=l.useState(!1),[ha,re]=l.useState(!1),[ie,le]=l.useState(""),[Da,ce]=l.useState(!1),[fa,de]=l.useState(!1),[L,va]=l.useState([]),[_,Aa]=l.useState([]),[ya,ue]=l.useState(!1),[wa,ba]=l.useState([]),[j,Sa]=l.useState(""),J=ja(),Ta=Ja(),[Ce,ka]=l.useState({});let D=M(t=>t.costCenter.MultipleCostCenterData);Ka();let R=M(t=>t.costCenter.handleMassMode);const F=M(t=>t.appSettings);console.log("multipleCostData",D),M(t=>t.costCenter.controllingArea);let c=M(t=>t.userManagement.userData);const K=()=>{U(!0)},Na=()=>{ce(!0)},me=()=>{ce(!1)},s=(t,e)=>{const a=t==null?void 0:t.find(n=>(n==null?void 0:n.fieldName)===e);return a?a.value:""},[X,Ra]=l.useState(0),Fa=(t,e)=>{const a=d=>{Ta(ro({keyName:t,data:d.body})),Ra(p=>p+1)},n=d=>{console.log(d)};w(`/${E}/data/${e}`,"get",a,n)},Pa=()=>{var t,e;(e=(t=ia)==null?void 0:t.costCenter)==null||e.map(a=>{Fa(a==null?void 0:a.keyName,a==null?void 0:a.endPoint)})},Ba=()=>{var t,e;X==((e=(t=ia)==null?void 0:t.costCenter)==null?void 0:e.length)?(ee(!1),console.log("apiCount",X)):ee(!0)};l.useEffect(()=>{Sa(Xa("CC"))},[]),l.useEffect(()=>{Ba()},[X]),l.useEffect(()=>{Pa()},[]);var O=(fe=Object.keys(Ce))==null?void 0:fe.map(t=>(console.log("payloadmapping",t),{CostCenterHeaderID:"",ControllingArea:t??"",Testrun:ne,Action:R==="Create"?"I":"U",ReqCreatedBy:c!=null&&c.user_id?c==null?void 0:c.user_id:"",ReqCreatedOn:c!=null&&c.createdOn?"/Date("+(c==null?void 0:c.createdOn)+")/":"",RequestStatus:"",CreationId:"",EditId:"",DeleteId:"",MassCreationId:"",MassEditId:"",Remarks:ie,MassDeleteId:"",RequestType:R==="Create"?"Mass Create":"Mass Change",MassRequestStatus:"",Toitem:Ce[t].map(e=>{var a,n,d,p,u,y,r,C,h,i,Ae,ye,we,be,Se,Te,ke,Ne,Re,Fe,Pe,Be,Ie,Me,Ee,Le,_e,Oe,Ve,$e,ze,qe,Ue,je,Je,Ke,Xe,Ge,He,We,Qe,Ye,Ze,xe,et,tt,at,ot,st,nt,rt,it,lt,ct,dt,ut,Ct,mt,pt,gt,ht,Dt,ft,vt,At,yt,wt,bt,St,Tt,kt,Nt,Rt,Ft,Pt,Bt,It,Mt,Et,Lt,_t,Ot,Vt,$t,zt,qt,Ut,jt,Jt,Kt,Xt,Gt,Ht,Wt,Qt,Yt,Zt,xt,ea,ta;return console.log("x",e),{CostCenterID:"",Costcenter:e!=null&&e.costCenter?e==null?void 0:e.costCenter:"",ValidFrom:e!=null&&e.validFrom?e==null?void 0:e.validFrom:"",ValidTo:e!=null&&e.validTo?e==null?void 0:e.validTo:"",PersonInCharge:s((n=(a=e==null?void 0:e.viewData)==null?void 0:a["Basic Data"])==null?void 0:n["Basic Data"],"Person Responsible"),CostcenterType:s((p=(d=e==null?void 0:e.viewData)==null?void 0:d["Basic Data"])==null?void 0:p["Basic Data"],"Cost Center Category"),CostctrHierGrp:s((y=(u=e==null?void 0:e.viewData)==null?void 0:u["Basic Data"])==null?void 0:y["Basic Data"],"Hierarchy Area"),BusArea:s((C=(r=e==null?void 0:e.viewData)==null?void 0:r["Basic Data"])==null?void 0:C["Basic Data"],"Business Area"),CompCode:s((i=(h=e==null?void 0:e.viewData)==null?void 0:h["Basic Data"])==null?void 0:i["Basic Data"],"Company Code"),Currency:s((ye=(Ae=e==null?void 0:e.viewData)==null?void 0:Ae["Basic Data"])==null?void 0:ye["Basic Data"],"Currency"),ProfitCtr:s((be=(we=e==null?void 0:e.viewData)==null?void 0:we["Basic Data"])==null?void 0:be["Basic Data"],"Profit Center"),Name:s((Te=(Se=e==null?void 0:e.viewData)==null?void 0:Se["Basic Data"])==null?void 0:Te.Names,"Name"),Descript:s((Ne=(ke=e==null?void 0:e.viewData)==null?void 0:ke["Basic Data"])==null?void 0:Ne.Names,"Description"),PersonInChargeUser:s((Fe=(Re=e==null?void 0:e.viewData)==null?void 0:Re["Basic Data"])==null?void 0:Fe["Basic Data"],"User Responsible"),RecordQuantity:s((Be=(Pe=e==null?void 0:e.viewData)==null?void 0:Pe.Control)==null?void 0:Be.Control,"Record Quantity")===!0?"X":"",LockIndActualPrimaryCosts:s((Me=(Ie=e==null?void 0:e.viewData)==null?void 0:Ie.Control)==null?void 0:Me.Control,"Actual Primary Costs")===!0?"X":"",LockIndPlanPrimaryCosts:s((Le=(Ee=e==null?void 0:e.viewData)==null?void 0:Ee.Control)==null?void 0:Le.Control,"Plan Primary Costs")===!0?"X":"",LockIndActSecondaryCosts:s((Oe=(_e=e==null?void 0:e.viewData)==null?void 0:_e.Control)==null?void 0:Oe.Control,"Act. secondary Costs")===!0?"X":"",LockIndPlanSecondaryCosts:s(($e=(Ve=e==null?void 0:e.viewData)==null?void 0:Ve.Control)==null?void 0:$e.Control,"Plan Secondary Costs")===!0?"X":"",LockIndActualRevenues:s((qe=(ze=e==null?void 0:e.viewData)==null?void 0:ze.Control)==null?void 0:qe.Control,"Actual Revenue")===!0?"X":"",LockIndPlanRevenues:s((je=(Ue=e==null?void 0:e.viewData)==null?void 0:Ue.Control)==null?void 0:je.Control,"Plan Revenue")===!0?"X":"",LockIndCommitmentUpdate:s((Ke=(Je=e==null?void 0:e.viewData)==null?void 0:Je.Control)==null?void 0:Ke.Control,"Commitment Update")===!0?"X":"",ConditionTableUsage:"",Application:"",CstgSheet:s((Ge=(Xe=e==null?void 0:e.viewData)==null?void 0:Xe.Templates)==null?void 0:Ge["Overhead rates"],"Costing Sheet"),ActyIndepTemplate:s((We=(He=e==null?void 0:e.viewData)==null?void 0:He.Templates)==null?void 0:We["Formula planning"],"Acty-Indep. Form Plng Temp"),ActyDepTemplate:s((Ye=(Qe=e==null?void 0:e.viewData)==null?void 0:Qe.Templates)==null?void 0:Ye["Formula planning"],"Acty-Dep. Form Plng Temp"),AddrTitle:s((xe=(Ze=e==null?void 0:e.viewData)==null?void 0:Ze.Address)==null?void 0:xe["Address Data"],"Title"),AddrName1:s((tt=(et=e==null?void 0:e.viewData)==null?void 0:et.Address)==null?void 0:tt["Address Data"],"Name 1"),AddrName2:s((ot=(at=e==null?void 0:e.viewData)==null?void 0:at.Address)==null?void 0:ot["Address Data"],"Name 2"),AddrName3:s((nt=(st=e==null?void 0:e.viewData)==null?void 0:st.Address)==null?void 0:nt["Address Data"],"Name 3"),AddrName4:s((it=(rt=e==null?void 0:e.viewData)==null?void 0:rt.Address)==null?void 0:it["Address Data"],"Name 4"),AddrStreet:s((ct=(lt=e==null?void 0:e.viewData)==null?void 0:lt.Address)==null?void 0:ct["Address Data"],"Street"),AddrCity:s((ut=(dt=e==null?void 0:e.viewData)==null?void 0:dt.Address)==null?void 0:ut["Address Data"],"Location"),AddrDistrict:s((mt=(Ct=e==null?void 0:e.viewData)==null?void 0:Ct.Address)==null?void 0:mt["Address Data"],"District"),AddrCountry:s((gt=(pt=e==null?void 0:e.viewData)==null?void 0:pt.Address)==null?void 0:gt["Address Data"],"Country/Reg"),AddrCountryIso:"",AddrTaxjurcode:s((Dt=(ht=e==null?void 0:e.viewData)==null?void 0:ht.Address)==null?void 0:Dt["Address Data"],"Jurisdiction"),AddrPoBox:s((vt=(ft=e==null?void 0:e.viewData)==null?void 0:ft.Address)==null?void 0:vt["Address Data"],"PO Box"),AddrPostlCode:s((yt=(At=e==null?void 0:e.viewData)==null?void 0:At.Address)==null?void 0:yt["Address Data"],"Postal Code"),AddrPobxPcd:s((bt=(wt=e==null?void 0:e.viewData)==null?void 0:wt.Address)==null?void 0:bt["Address Data"],"PO Box Post Cod"),AddrRegion:s((Tt=(St=e==null?void 0:e.viewData)==null?void 0:St.Address)==null?void 0:Tt["Address Data"],"Region"),TelcoLangu:"",TelcoLanguIso:s((Nt=(kt=e==null?void 0:e.viewData)==null?void 0:kt.Communication)==null?void 0:Nt["Communication Data"],"Language Key"),TelcoTelephone:s((Ft=(Rt=e==null?void 0:e.viewData)==null?void 0:Rt.Communication)==null?void 0:Ft["Communication Data"],"Telephone 1"),TelcoTelephone2:s((Bt=(Pt=e==null?void 0:e.viewData)==null?void 0:Pt.Communication)==null?void 0:Bt["Communication Data"],"Telephone 2"),TelcoTelebox:s((Mt=(It=e==null?void 0:e.viewData)==null?void 0:It.Communication)==null?void 0:Mt["Communication Data"],"Telebox Number"),TelcoTelex:s((Lt=(Et=e==null?void 0:e.viewData)==null?void 0:Et.Communication)==null?void 0:Lt["Communication Data"],"Telex Number"),TelcoFaxNumber:s((Ot=(_t=e==null?void 0:e.viewData)==null?void 0:_t.Communication)==null?void 0:Ot["Communication Data"],"Fax Number"),TelcoTeletex:s(($t=(Vt=e==null?void 0:e.viewData)==null?void 0:Vt.Communication)==null?void 0:$t["Communication Data"],"Teletex Number"),TelcoPrinter:s((qt=(zt=e==null?void 0:e.viewData)==null?void 0:zt.Communication)==null?void 0:qt["Communication Data"],"Printer Destination"),TelcoDataLine:s((jt=(Ut=e==null?void 0:e.viewData)==null?void 0:Ut.Communication)==null?void 0:jt["Communication Data"],"Data Line"),ActyDepTemplateAllocCc:s((Kt=(Jt=e==null?void 0:e.viewData)==null?void 0:Jt.Templates)==null?void 0:Kt["Activity and Business Process Allocation"],"Acty-Dep. Alloc Template"),ActyDepTemplateSk:s((Gt=(Xt=e==null?void 0:e.viewData)==null?void 0:Xt.Templates)==null?void 0:Gt["Actual Statistical Key Figures"],"Templ.: Act. Stat. Key Figure"),ActyIndepTemplateAllocCc:s((Wt=(Ht=e==null?void 0:e.viewData)==null?void 0:Ht.Templates)==null?void 0:Wt["Activity and Business Process Allocation"],"Acty-Indep. Alloc Temp"),ActyIndepTemplateSk:s((Yt=(Qt=e==null?void 0:e.viewData)==null?void 0:Qt.Templates)==null?void 0:Yt["Actual Statistical Key Figures"],"Templ.: Act. Stat. Key Figure"),AvcActive:!1,AvcProfile:"",BudgetCarryingCostCtr:"",CurrencyIso:"",Department:s((xt=(Zt=e==null?void 0:e.viewData)==null?void 0:Zt["Basic Data"])==null?void 0:xt["Basic Data"],"Department"),FuncArea:s((ta=(ea=e==null?void 0:e.viewData)==null?void 0:ea["Basic Data"])==null?void 0:ta["Basic Data"],"Functional Area"),FuncAreaFixAssigned:"",FuncAreaLong:"",Fund:"",FundFixAssigned:"",GrantFixAssigned:"",GrantId:"",JvEquityTyp:"",JvJibcl:"",JvJibsa:"",JvOtype:"",JvRecInd:"",JvVenture:"",Logsystem:""}})}));console.log("rishav",O);const I=D==null?void 0:D.map((t,e)=>{var d,p,u,y,r,C,h;const a=t,n=((d=t==null?void 0:t.viewData)==null?void 0:d["Basic Data"])||{};return{id:e,costCenter:a==null?void 0:a.costCenter,name:((p=n.Names.find(i=>(i==null?void 0:i.fieldName)==="Name"))==null?void 0:p.value)||"NA",controllingArea:a==null?void 0:a.controllingArea,description:((u=n.Names.find(i=>(i==null?void 0:i.fieldName)==="Description"))==null?void 0:u.value)||"NA",personResponsible:((y=n["Basic Data"].find(i=>(i==null?void 0:i.fieldName)==="Person Responsible"))==null?void 0:y.value)||"NA",companyCode:((r=n["Basic Data"].find(i=>(i==null?void 0:i.fieldName)==="Company Code"))==null?void 0:r.value)||"NA",profitCenter:((C=n["Basic Data"].find(i=>(i==null?void 0:i.fieldName)==="Profit Center"))==null?void 0:C.value)||"NA",costCenterCategory:((h=n["Basic Data"].find(i=>(i==null?void 0:i.fieldName)==="Cost Center Category"))==null?void 0:h.value)||"NA",validFrom:a==null?void 0:a.validFrom,validTo:a==null?void 0:a.validTo}}),Ia=t=>{va(t),t.length>0?(B(!0),console.log("selectedIds1",t)):B(!1),console.log("selectedIds",t),da(t)},Ma=()=>{ha?(U(!1),re(!1)):(U(!1),J("/masterDataCockpit/costCenter"))},V=()=>{ae(!0)},pe=()=>{ae(!1)},Ea=()=>{};l.useEffect(()=>{const t={},e=D==null?void 0:D.filter((a,n)=>L==null?void 0:L.includes(n));e.forEach(a=>{(a==null?void 0:a.controllingArea)in t?t[a.controllingArea].push(a):t[a.controllingArea]=[a]}),ka(t),console.log("temparray",e,t)},[L]);const La=()=>{v(!0),I.filter((a,n)=>q.includes(n));const t=a=>{if(v(!1),a.statusCode===200){console.log("success"),A("Create"),b(`Mass Cost Center Sent for Review with ID NCM${a.body}`),T("success"),k(!1),N(!0),K(),S(!0);const n={artifactId:j,createdBy:c==null?void 0:c.emailId,artifactType:"CostCenter",requestId:`NCM${a==null?void 0:a.body}`},d=u=>{console.log("Second API success",u)},p=u=>{console.error("Second API error",u)};w(`/${ra}/documentManagement/updateDocRequestId`,"post",d,p,n)}else A("Error"),N(!1),b("Failed Submitting the Cost Center for Review "),T("danger"),k(!1),S(!0),V()},e=a=>{console.log("error")};w(`/${E}/massAction/costCentersSubmitForReview`,"post",t,e,O)},_a=()=>{v(!0),I.filter((a,n)=>q.includes(n));const t=a=>{if(v(!1),a.statusCode===200){console.log("success"),A("Create"),b(`Mass Cost Center Change Sent for Review with ID CCM${a.body}`),T("success"),k(!1),N(!0),K(),S(!0);const n={artifactId:j,createdBy:c==null?void 0:c.emailId,artifactType:"CostCenter",requestId:`CCM${a==null?void 0:a.body}`},d=u=>{console.log("Second API success",u)},p=u=>{console.error("Second API error",u)};w(`/${ra}/documentManagement/updateDocRequestId`,"post",d,p,n)}else A("Error"),N(!1),b("Failed Submitting the Mass Cost Center for Review "),T("danger"),k(!1),S(!0),V()},e=a=>{console.log("error")};w(`/${E}/massAction/changeCostCentersSubmitForReview`,"post",t,e,O)},Oa=(t,e)=>t.every(a=>!e.includes(a)),Va=()=>{v(!0);const t=I.filter((r,C)=>q.includes(C));console.log("selectedData",t);const e=[],a=[];t.map(r=>{console.log("ios",r);var C={coArea:(r==null?void 0:r.controllingArea)??"",name:(r==null?void 0:r.name)??""};e.push(C),a.push(r==null?void 0:r.name.toUpperCase())}),console.log("duplicateCheckPayload",e);const n=[];D.map(r=>{var C,h;(h=(C=r==null?void 0:r.viewData)==null?void 0:C["Basic Data"])==null||h.Names.map(i=>{i.fieldName==="Name"&&n.push(i.value.toUpperCase())})}),console.log(n,a,"arrayofviewand"),Oa(n,a);const d=r=>{r.statusCode===400?(Aa(r.body),ue(!0),v(!1)):(A("Create"),console.log("success"),A("Create"),b("All Data has been Validated. Cost Center can be Sent for Review"),T("success"),k(!1),N(!0),K(),S(!0),re(!0),(e.coArea!==""||e.name!=="")&&w(`/${E}/alter/fetchCCDescriptionsDupliChk`,"post",p,u,e))},p=r=>{console.log("dataaaa",r),r.body.length===0||!r.body.some(C=>e.some(h=>h.name.toUpperCase()===C.description))?(v(!1),se(!1),B(!0)):(v(!1),A("Duplicate Check"),N(!1),b("There is a direct match for the Cost Center name."),T("danger"),k(!1),S(!0),V(),se(!0),ba(directMatches))},u=r=>{console.log(r)},y=r=>{console.log(r)};w(`/${E}/massAction/validateMassCostCenter`,"post",d,y,O)},$a=()=>{$(),R==="Create"?La():_a()},za=(t,e)=>{const a=t.target.value;if(a.length>0&&a[0]===" ")le(a.trimStart());else{let n=a.toUpperCase();le(n)}},qa=[{field:"costCenter",headerName:"Cost Center",editable:!1,flex:1,renderCell:t=>{const e=_.find(a=>a.costCenter===t.value);return console.log(e,"isDirectMatch"),console.log(t,"params"),e&&e.code===400?o(g,{sx:{fontSize:"12px",color:"red"},children:t.value}):o(g,{sx:{fontSize:"12px"},children:t.value})}},{field:"controllingArea",headerName:"Controlling Area",editable:!1,flex:1},{field:"name",headerName:"Name",editable:!1,flex:1,renderCell:t=>{const e=wa.includes(t.row.profitCenterName);return o(g,{sx:{fontSize:"12px",color:e?"red":"inherit"},children:t.value})}},{field:"description",headerName:"Description",editable:!1,flex:1},{field:"personResponsible",headerName:"Person Responsible",editable:!1,flex:1},{field:"companyCode",headerName:"Company Code",editable:!1,flex:1},{field:"profitCenter",headerName:"Profit Center",editable:!1,flex:1},{field:"costCenterCategory",headerName:"Cost Center Category",editable:!1,flex:1},{field:"validFrom",headerName:"Valid From",editable:!1,flex:1,renderCell:t=>o(g,{sx:{fontSize:"12px"},children:oa(t.row.validFrom).format(F==null?void 0:F.dateFormat)})},{field:"validTo",headerName:"Valid To",editable:!1,flex:1,renderCell:t=>o(g,{sx:{fontSize:"12px"},children:oa(t.row.validTo).format(F==null?void 0:F.dateFormat)})}],$=()=>{B(!0),de(!1)},ge=()=>{B(!1),de(!0)},he=()=>{ue(!1)},Ua=[{field:"costCenter",headerName:"Cost Center",editable:!1,flex:1},{field:"error",headerName:"Error",editable:!1,flex:1}],De=(ve=_==null?void 0:_.filter(t=>(t==null?void 0:t.code)===400))==null?void 0:ve.map((t,e)=>{var a;if(t.code===400)return{id:e,costCenter:t==null?void 0:t.costCenter,error:(a=t==null?void 0:t.status)==null?void 0:a.message}});return m(Ha,{children:[m("div",{children:[m(G,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:fa,onClose:$,children:[m(H,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[o(g,{variant:"h6",children:"Remarks"}),o(z,{sx:{width:"max-content"},onClick:$,children:o(sa,{})})]}),o(Y,{sx:{padding:".5rem 1rem"},children:o(Q,{children:o(na,{sx:{minWidth:400},children:o(Wa,{sx:{height:"auto"},fullWidth:!0,children:o(Qa,{sx:{backgroundColor:"#F5F5F5"},inputProps:{style:{textTransform:"uppercase"},maxLength:254},value:ie,onChange:za,multiline:!0,placeholder:"Enter Remarks",inputProps:{maxLength:254}})})})})}),m(W,{sx:{display:"flex",justifyContent:"end"},children:[o(P,{sx:{width:"max-content",textTransform:"capitalize"},onClick:$,children:"Cancel"}),o(P,{className:"button_primary--normal",type:"save",onClick:$a,variant:"contained",children:"Submit"})]})]}),m(G,{open:ya,fullWidth:!0,onClose:he,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[m(H,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[o(g,{variant:"h6",color:"red",children:"Errors"}),o(z,{sx:{width:"max-content"},onClick:he,children:o(sa,{})})]}),o(Y,{sx:{padding:".5rem 1rem"},children:De&&o(aa,{isLoading:x,width:"100%",rows:De,columns:Ua,pageSize:10,getRowIdValue:"id",hideFooter:!0,checkboxSelection:!1,disableSelectionOnClick:!0,status_onRowSingleClick:!0,stopPropagation_Column:"action",status_onRowDoubleClick:!0})}),o(W,{sx:{display:"flex",justifyContent:"end"}})]}),o(Ya,{dialogState:ga,openReusableDialog:V,closeReusableDialog:pe,dialogTitle:Ca,dialogMessage:te,handleDialogConfirm:pe,dialogOkText:"OK",handleExtraButton:Ea,dialogSeverity:ma}),pa&&o(Za,{openSnackBar:ua,alertMsg:te,handleSnackBarClose:Ma}),m("div",{style:{...Ga,backgroundColor:"#FAFCFF"},children:[o(f,{container:!0,sx:xa,children:m(f,{item:!0,md:12,sx:{display:"flex",marginBottom:"0"},children:[m(f,{item:!0,md:11,sx:{display:"flex"},children:[o(f,{children:o(z,{color:"primary","aria-label":"upload picture",component:"label",sx:eo,children:o(io,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{J("/masterDataCockpit/costCenter")}})})}),o(f,{children:R==="Create"?m(f,{item:!0,md:12,children:[o(g,{variant:"h3",children:o("strong",{children:"Create Multiple Cost Centers"})}),o(g,{variant:"body2",color:"#777",children:"This view creates multiple Cost Centers"})]}):m(f,{item:!0,md:12,children:[o(g,{variant:"h3",children:o("strong",{children:"Change Multiple Cost Centers"})}),o(g,{variant:"body2",color:"#777",children:"This view changes multiple Cost Centers"})]})})]}),o(f,{item:!0,md:1,sx:{display:"flex"},children:o(to,{title:"Upload documents if any",arrow:!0,children:o(z,{onClick:Na,children:o(co,{})})})}),m(G,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:Da,onClose:me,children:[o(H,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:o(g,{variant:"h6",children:"Add Attachment"})}),o(Y,{sx:{padding:".5rem 1rem"},children:o(Q,{children:o(na,{sx:{minWidth:400},children:o(lo,{title:"CostCenter",useMetaData:!1,artifactId:j,artifactName:"CostCenter"})})})}),o(W,{children:o(P,{onClick:me,children:"Close"})})]})]})}),o(f,{item:!0,sx:{position:"relative"},children:o(Q,{children:o(aa,{isLoading:x,width:"100%",title:"Cost Center Master List ("+I.length+")",rows:I,columns:qa,pageSize:10,getRowIdValue:"id",hideFooter:!1,checkboxSelection:!0,disableSelectionOnClick:!0,status_onRowSingleClick:!0,onRowsSelectionHandler:Ia,callback_onRowSingleClick:t=>{console.log("paramss",t);const e=t.row.costCenter,a=D.find(n=>n.costCenter===e);J(`/masterDataCockpit/costCenter/createMultipleCostCenter/editMultipleCostCenter/${e}`,{state:{tabsData:a,rowData:t.row}})},stopPropagation_Column:"action",status_onRowDoubleClick:!0})})})]}),o(oo,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:m(ao,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},value:ca,children:[o(P,{variant:"contained",size:"small",sx:{...Z,mr:1},onClick:Va,disabled:!ne,children:"Validate"}),R==="Create"?o(P,{variant:"contained",size:"small",sx:{...Z},onClick:ge,disabled:oe,children:"Submit for Review"}):R==="Change"?o(P,{variant:"contained",size:"small",sx:{...Z},onClick:ge,disabled:oe,children:"Submit for Review"}):""]})})]}),o(no,{sx:{color:"#fff",zIndex:t=>t.zIndex.drawer+1},open:la,children:o(so,{color:"inherit"})})]})};export{bo as default};
