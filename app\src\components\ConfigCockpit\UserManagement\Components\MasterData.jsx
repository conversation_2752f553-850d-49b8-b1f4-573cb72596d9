import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>b<PERSON>anel } from "@mui/lab";
import { Tab, Box, Typography, Stack, Grid } from "@mui/material";
import React, { useEffect } from "react";
import { useState } from "react";
// import {
//   outerContainer_Information,
//   outermostContainer,
//   outermostContainer_Information,
// } from "";
import Systems from "./Systems/Applications";
import Groups from "./Systems/Groups";
import Roles from "./Systems/Roles";
import Users from "./Systems/Users";
import { checkIwaAccess } from "../../../../functions";
import { useSelector } from "react-redux";
import UsersWorkbench from "./userProfileManagement/UsersWorkbench";
import AppsIcon from "@mui/icons-material/Apps";
import PeopleOutlinedIcon from "@mui/icons-material/PeopleOutlined";
import PersonOutlinedIcon from "@mui/icons-material/PersonOutlined";
import { useParams, useSearchParams } from "react-router-dom";
import { getAllActivities, getAllApplications, getAllEntities, getAllRoleTemplates, getAllRoles, getAllUsers, getExternalIdpUsers, getInternalIdpUsers } from "../Action/action";
import { setActivities, setApplications, setEntities, setExternalIdpUsers, setInternalIdpUsers, setRoleTemplates, setRoles, setUsers } from "../../../../app/userManagementSlice";
import { useDispatch } from "react-redux";
import { outerContainer_Information, outermostContainer, outermostContainer_Information } from "../../../Common/commonStyles";

export default function UserManagement() {
  let [params, setParams] = useSearchParams();
  let iwaAccessData = useSelector(
    (state) => state?.userManagement?.entitiesAndActivities?.["User Management"]
  );
  let dispatch = useDispatch()
  const [value, setValue] = useState(
    iwaAccessData?.includes("Systems")
      ? "systems"
      : iwaAccessData?.includes("Groups")
      ? "groups"
      : iwaAccessData?.includes("Roles")
      ? "roles"
      : "users"
  );
  const handleChange = (event, newValue) => {
    setParams({ component: newValue });
  };
  useEffect(() => {
    if (params.get("component")) setValue(params.get("component"));
  }, [params]);
  useEffect(() => {
    getAllUsers(
      () => {
        // setLoad(true);
      },
      (data) => {
        dispatch(setUsers(data.data || []));  
        // setLoad(false);
      },
      (err) => {
        // setLoad(false);
      }
    );
    getExternalIdpUsers(
      () => {
        // setLoad(true);
      },
      (data) => {
        dispatch(setExternalIdpUsers(data.data || []));
        // setLoad(false);
      },
      (err) => {
        // setLoad(false);
      }
    );
    getInternalIdpUsers(
      () => {
        // setLoad(true);
      },
      (data) => {
        dispatch(setInternalIdpUsers(data.data || []));
        // setLoad(false);
      },
      (err) => {
        // setLoad(false);
      }
    );
    getAllRoles(
      () => {
        // setLoad(true);
      },
      (data) => {
        dispatch(setRoles(data?.data || []));
        // setLoad(false);
      },
      (err) => {
        // setLoad(false);
      }
    );
    getAllRoleTemplates(
      () => {
        // setLoad(true);
      },
      (data) => {
        dispatch(setRoleTemplates(data?.data || []));
        // setLoad(false);
      },
      (err) => {
        // setLoad(false);
      }
    );
    getAllApplications(
      () => {
        // setLoad(true);
      },
      (data) => {
        dispatch(setApplications(data?.data || []));
        // setLoad(false);
      },
      (err) => {
        // setLoad(false);
      }
    );
    getAllEntities(
      () => {
        // setLoad(true);
      },
      (data) => {
        dispatch(setEntities(data?.data || []));
        // setLoad(false);
      },
      (err) => {
        // setLoad(false);
      }
    );
    getAllActivities(
      () => {
        // setLoad(true);
      },
      (data) => {
        dispatch(setActivities(data?.data || []));
        // setLoad(false);
      },
      (err) => {
        // setLoad(false);
      }
    );
    // getAllGroups(
    //   () => {
    //     // setLoad(true);
    //   },
    //   (data) => {
    //     dispatch(setGroups(data?.data || []));
    //     // setLoad(false);
    //   },
    //   (err) => {
    //     setLoad(false);
    //   }
    // );
  }, []);
  return (
    <div className="printScreen" style={outermostContainer}>
      <Stack >
        {/* INFORMATION */}

        <Grid container sx={outermostContainer_Information}>
          <Grid item md={5} sx={outerContainer_Information}>
            <Typography variant="h3">
              <strong>User Management</strong>
            </Typography>
            <Typography variant="body2" color="#777">
              This view displays the settings for managing the Users and their
              Access
            </Typography>
          </Grid>
        </Grid>
      
            <TabContext value={value}>
              <Box
                sx={{
                  borderBottom: 1,
                  borderColor: "divider",
                  // padding: "0px 12px 0px",
                  marginBottom:'1rem'
                }}
              >
                <TabList
                  onChange={handleChange}
                  aria-label="basic tabs example"
                >
                  {checkIwaAccess(
                    iwaAccessData,
                    "User Management",
                    "Systems"
                  ) && (
                    <Tab
                      label={
                        <Stack
                          direction="row"
                          sx={{
                            alignItems: "center",
                          }}
                        >
                          <AppsIcon sx={{ fontSize: "15px" }} />
                          <Typography
                            variant="body1"
                            ml={1}
                            sx={{ fontWeight: 600, fontSize: "14px" }}
                          >
                            Systems
                          </Typography>
                        </Stack>
                      }
                      value="systems"
                      sx={{ textTransform: "none", fontWeight: "bold" }}
                    />
                  )}
                  {checkIwaAccess(
                    iwaAccessData,
                    "User Management",
                    "Groups"
                  ) && (
                    <Tab
                      label={
                        <Stack
                          direction="row"
                          sx={{
                            alignItems: "center",
                          }}
                        >
                          <AppsIcon sx={{ fontSize: "15px" }} />
                          <Typography
                            variant="body1"
                            ml={1}
                            sx={{ fontWeight: 600, fontSize: "14px" }}
                          >
                            Groups
                          </Typography>
                        </Stack>
                      }
                      value="groups"
                      sx={{ textTransform: "none", fontWeight: "bold" }}
                    />
                  )}
                  {checkIwaAccess(
                    iwaAccessData,
                    "User Management",
                    "Roles"
                  ) && (
                    <Tab
                      label={
                        <Stack
                          direction="row"
                          sx={{
                            alignItems: "center",
                          }}
                        >
                          <PeopleOutlinedIcon sx={{ fontSize: "15px" }} />
                          <Typography
                            variant="body1"
                            ml={1}
                            sx={{ fontWeight: 600, fontSize: "14px" }}
                          >
                            Roles
                          </Typography>
                        </Stack>
                      }
                      value="roles"
                      sx={{ textTransform: "none", fontWeight: "bold" }}
                    />
                  )}
                  {checkIwaAccess(
                    iwaAccessData,
                    "User Management",
                    "Users"
                  ) && (
                    <Tab
                      label={
                        <Stack
                          direction="row"
                          sx={{
                            alignItems: "center",
                          }}
                        >
                          <PersonOutlinedIcon sx={{ fontSize: "15px" }} />
                          <Typography
                            variant="body1"
                            ml={1}
                            sx={{ fontWeight: 600, fontSize: "14px" }}
                          >
                            Users
                          </Typography>
                        </Stack>
                      }
                      value="users"
                      sx={{ textTransform: "none", fontWeight: "bold" }}
                    />
                  )}
                  {checkIwaAccess(
                    iwaAccessData,
                    "User Management",
                    "User Workbench"
                  ) && (
                    // <Tab
                    // label={
                    //   <Stack direction='row' sx={{
                    //     alignItems:'center',
                    //   }}>
                    //     <WorkspacesOutlinedIcon sx={{fontSize:'15px'}}/>
                    //   <Typography
                    //     variant="body1"
                    //     ml={1}
                    //     sx={{ fontWeight: 600, fontSize: "14px" }}
                    //   >
                    //     Change Tracker
                    //   </Typography>
                    //   </Stack>
                    // }
                    //   value="userswb"
                    //   sx={{ textTransform: "none", fontWeight: "bold" }}
                    // />
                    <></>
                  )}
                </TabList>
              </Box>
              {checkIwaAccess(iwaAccessData, "User Management", "Systems") && (
                <TabPanel
                  value={"systems"}
                  sx={{ padding: "0px" }}
                  // className="ConfigCockpit-lineItem"
                >
                  <Stack spacing={1}>
                    <Systems />
                  </Stack>
                </TabPanel>
              )}
              {checkIwaAccess(iwaAccessData, "User Management", "Groups") && (
                <TabPanel
                  value={"groups"}
                  sx={{ padding: "0px" }}
                  className="ConfigCockpit-lineItem"
                >
                  <Stack spacing={1}>
                    <Groups />
                  </Stack>
                </TabPanel>
              )}
              {checkIwaAccess(
                iwaAccessData,
                "User Management",
                "User Workbench"
              ) && (
                <TabPanel
                  value={"userswb"}
                  sx={{ padding: "0px" }}
                  className="ConfigCockpit-lineItem"
                >
                  <Stack spacing={1}>
                    <UsersWorkbench />
                  </Stack>
                </TabPanel>
              )}
              {/* <TabPanel
          value={"groups"}
          sx={{ padding: "0px" }}
          className="ConfigCockpit-lineItem"
        >
          <Stack spacing={1}>
            <Groups /> 
          </Stack>
        </TabPanel> */}
              {checkIwaAccess(iwaAccessData, "User Management", "Roles") && (
                <TabPanel
                  value={"roles"}
                  sx={{ padding: "0px" }}
                  className="ConfigCockpit-lineItem"
                >
                  <Stack spacing={1}>
                    <Roles />
                  </Stack>
                </TabPanel>
              )}
              {checkIwaAccess(iwaAccessData, "User Management", "Users") && (
                <TabPanel
                  value={"users"}
                  sx={{ padding: "0px" }}
                  className="ConfigCockpit-lineItem"
                >
                  <Stack spacing={1}>
                    <Users />
                  </Stack>
                </TabPanel>
              )}
            </TabContext>
          
        
      </Stack>
    </div>
  );
}
