import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  payload: {},
  responseFromAPI: {},
  description: {},
  selectedCheckBox: [],
};

export const editPayloadSlice = createSlice({
  name: "edit",
  initialState,
  reducers: {
    setPayload: (state, action) => {
      state.payload[action.payload.keyname] = action.payload.data;
      return state;
    },
    setResponseFromAPI: (state, action) => {
      state.responseFromAPI = action.payload;
    },
    descriptionHeader: (state, action) => {
      state.description = action.payload;
    },
    setPayloadWhole: (state, action) => {
      state.payload = action.payload;
      return state;
    },
    setCheckBox: (state, action) => {
      //For GL Extend
      state.selectedCheckBox = action.payload;
    },
  },
});

// Action creators are generated for each case reducer function
export const {
  setPayload,
  setResponseFromAPI,
  descriptionHeader,
  setPayloadWhole,
  setCheckBox,
} = editPayloadSlice.actions;

export const editPayloadReducer = editPayloadSlice.reducer;
