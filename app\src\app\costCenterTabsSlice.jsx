import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  costCenterBasicData: {},
  costCenterControl: {},
  costCenterTemplate: {},
  costCenterAddress: {},
  costCenterCommunication: {},
  costCenterHistory: {},
  costCenterViewData: [],
  singleCCPayload: {},
  requiredFields: [],
  errorFields: [],
  MultipleCostCenterData: [],
  controllingArea: {},
  MultipleCostCenterRequestBench: [],
  handleMassMode: "",
};

export const costCenterTabSlice = createSlice({
  name: "costCenter",
  initialState,
  reducers: {
    setCostCenterBasicDataTab: (state, action) => {
      state.costCenterBasicData = action.payload;
    },
    setCostCenterControlTab: (state, action) => {
      state.costCenterControl = action.payload;
    },
    setCostCenterTemplatesTab: (state, action) => {
      state.costCenterTemplate = action.payload;
    },
    setCostCenterAddressTab: (state, action) => {
      state.costCenterAddress = action.payload;
    },
    setCostCenterCommunicationTab: (state, action) => {
      state.costCenterCommunication = action.payload;
    },
    setCostCenterHistoryTab: (state, action) => {
      state.costCenterHistory = action.payload;
    },
    setCostCenterViewData: (state, action) => {
      state.costCenterViewData = action.payload;
    },
    setSingleCostCenterPayload: (state, action) => {
      state.singleCCPayload[action.payload.keyName] = action.payload.data;
      return state;
    },
    setMultipleCostCenterData(state, action) {
      state.MultipleCostCenterData = action.payload;
      return state;
    },
    setCCRequiredFields: (state, action) => {
      if (
        state.requiredFields.findIndex((item) => item == action.payload) == -1
      ) {
        state.requiredFields.push(action.payload);
      }
      return state;
    },
    setCCErrorFields: (state, action) => {
      state.errorFields = action.payload;
      return state;
    },
    setControllingArea(state, action) {
      state.controllingArea = action.payload;
      return state;
    },
    setMultipleCostCenterRequestBench(state, action) {
      state.MultipleCostCenterRequestBench = action.payload;
      return state;
    },
    setHandleMassMode(state, action) {
      state.handleMassMode = action.payload;
    },
    clearCostCenter: (state, action) => {
      state.requiredFields = []
      state.errorFields = []
      state.singleCCPayload = {};
      state.MultipleCostCenterData = [];
      state.controllingArea = {};
      state.MultipleCostCenterRequestBench = [];
    },
    clearSingleCostCenter: (state, action) => {
      state.requiredFields = []
      state.singleCCPayload = {};
    },
  },
});

// Action creators are generated for each case reducer function
export const {
  setCostCenterBasicDataTab,
  setCostCenterControlTab,
  setCostCenterTemplatesTab,
  setCostCenterAddressTab,
  setCostCenterCommunicationTab,
  setCostCenterHistoryTab,
  setCostCenterViewData,
  setSingleCostCenterPayload,
  setMultipleCostCenterData,
  setControllingArea,
  setMultipleCostCenterRequestBench,
  setHandleMassMode,
  clearCostCenter,
  clearSingleCostCenter,
  setCCErrorFields,
  setCCRequiredFields,
} = costCenterTabSlice.actions;

export const costCenterReducer = costCenterTabSlice.reducer;
