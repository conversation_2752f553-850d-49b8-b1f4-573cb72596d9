import{b as je,s as Le,r as p,q as g,u as Te,cd as Fe,j as s,G as u,al as Ge,a as r,z as Me,T as m,B as L,x as q,b4 as qe,br as Oe,aE as ie,t as b,aB as T,aD as de,eU as F,K as x,eV as f,bp as $e,I as Ve,b1 as ze,b8 as ae,ai as C}from"./index-17b8d91e.js";import{d as _e}from"./EditOutlined-36c8ca4d.js";import{d as We}from"./ArrowCircleLeftOutlined-2a09f8e2.js";import{C as Ie,d as He}from"./ChangeLog-0f47d713.js";import{E as Ke}from"./EditFieldForMassGL-51a063c1.js";import{a as Ue,b as Je,S as Qe}from"./Stepper-88e4fb0c.js";import"./DatePicker-68227989.js";import"./dateViewRenderers-34586552.js";import"./useSlotProps-e34e1e13.js";import"./InputAdornment-5b0053c5.js";import"./CSSTransition-30917e2c.js";import"./useMediaQuery-6a073ac5.js";import"./DesktopDatePicker-07c19cde.js";import"./useMobilePicker-9978caff.js";const io=()=>{var Y,Z,w,P,R,k,D,ee,oe,ne,te,re,le;const O=je(),h=Le();p.useState({});const[$,V]=p.useState(0);p.useState([]);const[A,se]=p.useState(!1),[Xe,ue]=p.useState(!0);p.useState([]),p.useState([]),p.useState([]);const[y,N]=p.useState(0);p.useState(),g(e=>e.tabsData);const[pe,z]=p.useState(!1),[_,he]=p.useState([]),[ge,W]=p.useState(!1),G=Te(),v=g(e=>e.generalLedger.MultipleGLRequestBench),S=G.state.tabsData,n=G.state.rowData,a=G.state.requestbenchRowData;let o=g(e=>{var l;return(l=e==null?void 0:e.initialData)==null?void 0:l.IWMMyTask});g(e=>e.payload);let I=g(e=>e.edit.payload),H=g(e=>e.generalLedger.requiredFields);console.log(o==null?void 0:o.body,"taskData"),g(e=>e.profitCenter.profitCenterCompCodes);let t=g(e=>e.userManagement.taskData);g(e=>e.generalLedger.MultipleGLData);let M=g(e=>e.userManagement.userData),E="";(t==null?void 0:t.processDesc)==="Mass Change"?(E=t!=null&&t.subject?(Y=t==null?void 0:t.subject)==null?void 0:Y.slice(3):a==null?void 0:a.requestId.slice(3),(Z=o==null?void 0:o.body)!=null&&Z.companyCode||n.controllingArea,(w=t==null?void 0:t.body)!=null&&w.glAccount||n.profitCenter):(t==null?void 0:t.processDesc)==="Mass Create"?(E=t!=null&&t.subject?(P=t==null?void 0:t.subject)==null?void 0:P.slice(3):a==null?void 0:a.requestId.slice(3),(R=o==null?void 0:o.body)!=null&&R.companyCode||n.controllingArea,(k=t==null?void 0:t.body)!=null&&k.glAccount||n.glAccount):(a==null?void 0:a.requestType)==="Mass Create"?(E=t!=null&&t.subject?(D=t==null?void 0:t.subject)==null?void 0:D.slice(3):a==null?void 0:a.requestId.slice(3),(ee=o==null?void 0:o.body)!=null&&ee.companyCode||n.companyCode,(oe=t==null?void 0:t.body)!=null&&oe.glAccount||n.glAccount):(a==null?void 0:a.requestType)==="Mass Change"&&(E=t!=null&&t.subject?(ne=t==null?void 0:t.subject)==null?void 0:ne.slice(3):a==null?void 0:a.requestId.slice(3),(te=o==null?void 0:o.body)!=null&&te.companyCode||n.companyCode,(re=t==null?void 0:t.body)!=null&&re.glAccount||n.glAccount);for(let e=0;e<(v==null?void 0:v.length);e++)if(v[e].GLAccount===n.glAccount){v[e];break}const me=(e,l)=>{setActiveTab(l)},K=()=>{const e=X();A?e?(N(l=>l-1),h(F())):J():(N(l=>l-1),h(F()))},U=()=>{const e=X();A?e?(N(l=>l+1),h(F())):J():(N(l=>l+1),h(F()))},J=()=>{W(!0)},B=Object.entries(S==null?void 0:S.viewData).filter(e=>typeof e[1]=="object"&&e[1]!=null).map(e=>e[0]),j=Object.entries(S.viewData).filter(e=>typeof e[1]=="object"&&e[1]!=null).map(e=>Object.entries(e[1])),Q={};j.map(e=>{e.forEach((l,c)=>{l.forEach((d,i)=>{i!==0&&d.forEach(ce=>{Q[ce.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("")]=ce.value})})})});const ye=()=>{var c,d;const e=i=>{h(C({keyName:"TaxCategory",data:i.body}))},l=i=>{console.log(i)};x(`/${f}/data/getTaxCategory?companyCode=${(c=o==null?void 0:o.body)!=null&&c.CompCode?(d=o==null?void 0:o.body)==null?void 0:d.CompCode:n==null?void 0:n.companyCode}`,"get",e,l)},be=()=>{var c,d;const e=i=>{h(C({keyName:"HouseBank",data:i.body}))},l=i=>{console.log(i)};x(`/${f}/data/getHouseBank?companyCode=${(c=o==null?void 0:o.body)!=null&&c.CompCode?(d=o==null?void 0:o.body)==null?void 0:d.CompCode:n==null?void 0:n.companyCode}`,"get",e,l)},xe=()=>{var c,d;const e=i=>{h(C({keyName:"FieldStatusGroup",data:i.body}))},l=i=>{console.log(i)};x(`/${f}/data/getFieldStatusGroup?companyCode=${(c=o==null?void 0:o.body)!=null&&c.CompCode?(d=o==null?void 0:o.body)==null?void 0:d.CompCode:n==null?void 0:n.companyCode}`,"get",e,l)},fe=()=>{var c,d;const e=i=>{h(C({keyName:"GroupAccountNumber",data:i.body}))},l=i=>{console.log(i)};x(`/${f}/data/getGroupAccountNumber?chartAccount=${(c=o==null?void 0:o.body)!=null&&c.chartOfAccount?(d=o==null?void 0:o.body)==null?void 0:d.chartOfAccount:n==null?void 0:n.chartOfAccount}`,"get",e,l)},Ce=()=>{var c,d;const e=i=>{h(C({keyName:"AlternativeAccountNumber",data:i.body}))},l=i=>{console.log(i)};x(`/${f}/data/getAlternativeAccountNumber?chartAccount=${(c=o==null?void 0:o.body)!=null&&c.chartOfAccount?(d=o==null?void 0:o.body)==null?void 0:d.chartOfAccount:n==null?void 0:n.chartOfAccount}`,"get",e,l)},Ae=()=>{var c,d;const e=i=>{h(C({keyName:"AccountGroup",data:i.body}))},l=i=>{console.log(i)};x(`/${f}/data/getAccountGroupCodeDesc?chartAccount=${(c=o==null?void 0:o.body)!=null&&c.chartOfAccount?(d=o==null?void 0:o.body)==null?void 0:d.chartOfAccount:n==null?void 0:n.chartOfAccount}`,"get",e,l)},ve=()=>{var c,d;const e=i=>{h(C({keyName:"CostElementCategory",data:i.body}))},l=i=>{console.log(i)};x(`/${f}/data/getCostElementCategory?accountType=${(c=o==null?void 0:o.body)!=null&&c.accountType?(d=o==null?void 0:o.body)==null?void 0:d.accountType:n==null?void 0:n.accountType}`,"get",e,l)};p.useEffect(()=>{ye(),xe(),be(),Ae(),Ce(),fe(),ve()},[]);const Se=()=>{se(!0),ue(!1)},Ee=e=>{z(e)},Ne=()=>{z(!0)};p.useEffect(()=>{h(Fe(Q))},[]),console.log(I,H,"requiredFieldTabWise");const X=()=>$e(I,H,he),Be=()=>{W(!1)};return console.log("tabcontents",S),s("div",{children:[s(u,{container:!0,style:{...Ge,backgroundColor:"#FAFCFF"},children:[_.length!=0&&r(Me,{openSnackBar:ge,alertMsg:"Please fill the following Field: "+_.join(", "),handleSnackBarClose:Be}),s(u,{sx:{width:"inherit"},children:[s(u,{item:!0,md:12,style:{padding:"16px",display:"flex"},children:[r(u,{style:{display:"flex",justifyContent:"flex-end"},children:r(Ve,{color:"primary","aria-label":"upload picture",component:"label",sx:ze,children:r(We,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{O(-1)}})})}),s(u,{md:10,children:[r(m,{variant:"h3",children:s("strong",{children:["Multiple General Ledgers: ",n.glAccount," "]})}),r(m,{variant:"body2",color:"#777",children:"This view displays the detail of Multiple General Ledgers"})]}),r(u,{md:1,sx:{display:"flex",justifyContent:"flex-end",marginRight:"4px"},children:r(b,{variant:"outlined",size:"small",sx:ae,onClick:Ne,title:"Chnage Log",children:r(He,{sx:{padding:"2px"},fontSize:"small"})})}),pe&&r(Ie,{open:!0,closeModal:Ee,requestId:E,requestType:"Mass",pageName:"generalLedger",controllingArea:n.companyCode,centerName:n.glAccount}),A?"":(M==null?void 0:M.role)==="Finance"?r(u,{md:1,sx:{display:"flex",justifyContent:"flex-end"},children:r(u,{item:!0,children:s(b,{variant:"outlined",size:"small",sx:ae,onClick:Se,children:["Change",r(_e,{sx:{padding:"2px"},fontSize:"small"})]})})}):""]}),r(u,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:s(L,{width:"70%",sx:{marginLeft:"40px"},children:[r(u,{item:!0,sx:{paddingTop:"2px !important"},children:s(q,{flexDirection:"row",children:[r("div",{style:{width:"15%"},children:r(m,{variant:"body2",color:"#777",children:"General Ledger Account"})}),s(m,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[": ",n==null?void 0:n.glAccount]})]})}),r(u,{item:!0,sx:{paddingTop:"2px !important"},children:s(q,{flexDirection:"row",children:[r("div",{style:{width:"15%"},children:r(m,{variant:"body2",color:"#777",children:"Company Code"})}),s(m,{variant:"body2",fontWeight:"bold",children:[": ",n==null?void 0:n.companyCode]})]})}),r(u,{item:!0,sx:{paddingTop:"2px !important"},children:s(q,{flexDirection:"row",children:[r("div",{style:{width:"15%"},children:r(m,{variant:"body2",color:"#777",children:"Chart of Account"})}),s(m,{variant:"body2",fontWeight:"bold",children:[": ",n==null?void 0:n.chartOfAccount]})]})})]})}),s(u,{container:!0,style:{padding:"16px"},children:[r(Qe,{activeStep:y,onChange:me,variant:"scrollable",sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},"aria-label":"mui tabs example",children:B.map((e,l)=>r(Ue,{children:r(Je,{sx:{fontWeight:"700"},children:e})},e))}),r(u,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:j&&((le=j[y])==null?void 0:le.map((e,l)=>r(L,{sx:{width:"100%"},children:s(u,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...qe},children:[r(u,{container:!0,children:r(m,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:e[0]})}),r(L,{children:r(L,{sx:{width:"100%"},children:r(Oe,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:r(u,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:[...e[1]].map(c=>(console.log("inneritem",e[1]),r(Ke,{activeTabIndex:y,fieldGroup:e[0],selectedRowData:n.glAccount,pcTabs:B,label:c.fieldName,value:c.value,length:c.maxLength,visibility:c.visibility,onSave:d=>handleFieldSave(c.fieldName,d),isEditMode:A,type:c.fieldType,field:c})))})})})})]})},l)))},j)]})]})]}),A?r(de,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:s(ie,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:$,onChange:e=>{V(e)},children:[r(b,{size:"small",variant:"contained",onClick:()=>{O(-1)},children:"Save"}),r(b,{variant:"contained",size:"small",sx:{...T,mr:1},onClick:K,disabled:y===0,children:"Back"}),r(b,{variant:"contained",size:"small",sx:{...T,mr:1},onClick:U,disabled:y===B.length-1,children:"Next"})]})}):r(de,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:s(ie,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:$,onChange:e=>{V(e)},children:[r(b,{variant:"contained",size:"small",sx:{...T,mr:1},onClick:K,disabled:y===0,children:"Back"}),r(b,{variant:"contained",size:"small",sx:{...T,mr:1},onClick:U,disabled:y===B.length-1,children:"Next"})]})})]})};export{io as default};
