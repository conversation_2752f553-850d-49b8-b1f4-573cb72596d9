import {
  Box,
  Grid,
  IconButton,
  Tabs,
  Typography,
  Stack,
  TextField,
  Checkbox,
  Autocomplete,
  Paper,
  BottomNavigation,
  Button,
  CardContent,
  Stepper,
  Step,
  StepLabel,
} from "@mui/material";
import Tab from "@mui/material/Tab";
import React, { useState, useEffect } from "react";
import {
  iconButton_SpacingSmall,
  outermostContainer,
  container_Padding,
  outerContainer_Information,
  button_Primary,
  outermostContainer_Information,
  button_Outlined,
} from "../../Common/commonStyles";
import { useSelector, useDispatch } from "react-redux";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import { useNavigate, useLocation } from "react-router-dom";
// import EditableField from "./EditFieldForDisplay";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import EditFieldForMassProfitCenter from "./EditFieldForMassProfitCenter";
import CompCodesProfitCenter from "../ProfitCenterTabs/CompCodeProfitCenter";
import { doAjax } from "../../Common/fetchService";
import { destination_ProfitCenter } from "../../../destinationVariables";
import { setDropDown } from "../../../app/dropDownDataSlice";
import ChangeLog from "../../Changelog/ChangeLog";
import TrackChangesTwoToneIcon from '@mui/icons-material/TrackChangesTwoTone';
import { clearProfitCenter } from "../../../app/profitCenterTabsSlice";
import { setPayloadWhole } from "../../../app/editPayloadSlice";
import { formValidator } from "../../../functions";
import ReusableSnackBar from "../../Common/ReusableSnackBar";
import { gridColumnGroupsLookupSelector } from "@mui/x-data-grid";
// import { setEditMultipleMaterial } from "../../app/initialDataSlice";
// import EditableFieldForMassMaterial from "./EditFieldForMassMaterial";

const EditMultipleProfitCenter = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [dropDownData, setDropDownData] = useState({});
  const [value, setValue] = useState(0);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isDisplayMode, setIsDisplayMode] = useState(true);
  const [activeTab, setActiveTab] = useState(0);
  const [dispCompCode, setDispCompCode] = useState([]);
  const [activeStep, setActiveStep] = useState(0);
  const [isChangeLogopen, setisChangeLogopen] = useState(false);
  const [errorsFields, setErrorsFields] = useState([]); //chiranjit
  const [formValidationErrorItems, setFormValidationErrorItems] = useState([]); //chiranjit
  const [openSnackbarValidation, setOpenSnackbarValidation] = useState(false); //chiranjit

  const location = useLocation();
  const compCodesTabDetails = useSelector(
    (state) => state.profitCenter.profitCenterCompCodes
  );
  console.log("location", location.state);
  const ChangesInMaterialDetail = useSelector(
    (state) => state.initialData.EditMultipleMaterial
  );
  const MultipleMaterial = useSelector(
    (state) => state.initialData.MultipleMaterial
  );
  const tabsData = location.state.tabsData.viewData;
  const selectedRowData = location.state.rowData;
  const requestNumber = location.state.requestNumber;
  const compcodeData =
    tabsData["Comp Codes"]["Company Code Assignment for Profit Center"];
  const PayloadData = useSelector((state) => state.payload);
  
  let singlePCPayloadAfterChange = useSelector((state) => state.edit.payload);
  console.log(singlePCPayloadAfterChange, "singlePCPayloadAfterChange");
  //console.log(multipleProfitCenterData, "singlePCPayloadAfterChange");
  let requiredFieldTabWise= useSelector((state) => state.profitCenter.requiredFields);
  console.log(requiredFieldTabWise,"required_field_for_data") //chiranjit

  const getProfitCenterGroupBasedOnControllingArea = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ProfitCtrGroup", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/data/getProfitCtrGroup?controllingArea=${selectedRowData?.controllingArea}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getRegion = (value) => {
    console.log("compcode", value);
    const hSuccess = (data) => {
      console.log("value", data);
      dispatch(setDropDown({ keyName: "Region", data: data.body }));
    };
    const hError = (error) => {
      console.log(error, "error in dojax");
    };
    doAjax(
      `/${destination_ProfitCenter}/data/getRegionBasedOnCountry?country=${value}`,
      "get",
      hSuccess,
      hError
    );
  };

  useEffect(()=>{
    //console.log(tabContents,"tabContentsinuseEffect")
    
    //console.log(gridArr,"gridArr");
    dispatch(setPayloadWhole(tempHash));
  },[])




  useEffect(() => {
    setDispCompCode(
      _.zip(
        compcodeData[0].value,
        compcodeData[1].value,
        compcodeData[2].value
      ).map((item, index) => {
        return {
          id: index,
          companyCodes: item[0]?.split("$$$")[0]
          ? item[0]?.split("$$$")[0]
          : "",
          companyName: item[1]?.split("$$$")[0]
          ? item[1]?.split("$$$")[0]
          : "",
          assigned: item[2] ? item[2] : "",
        };
      })
    );
    getRegion(tabsData["Address"]["Address Data"].find(
      (field) => field?.fieldName === "Country/Reg."
    )?.value)

    getProfitCenterGroupBasedOnControllingArea();
  }, []);

  const onEdit = () => {
    setIsEditMode(true);
    setIsDisplayMode(false);
  };
  const handleBack = () => {
    const isValidation = handleCheckValidationError();
    if(isEditMode){
      if (isValidation) {
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
        dispatch(clearProfitCenter());
      } else {
        handleSnackBarOpenValidation();
      }
    }else{
      setActiveStep((prevActiveStep) => prevActiveStep - 1);
      dispatch(clearProfitCenter());
    }
  };
  const handleNext = () => {
    const isValidation = handleCheckValidationError();
    if(isEditMode){
      if (isValidation) {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
        dispatch(clearProfitCenter());
      } else {
        handleSnackBarOpenValidation();
      }
    }else{
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
      dispatch(clearProfitCenter());
    }
  };
  const handleSnackBarOpenValidation = () => {
    setOpenSnackbarValidation(true);
  };
  
  console.log("datafromprevious", tabsData, compcodeData);
  const handleChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const tabsArray = Object.entries(tabsData)
    .filter((item) => typeof item[1] == "object" && item[1] != null)
    .map((item) => item[0]);
  console.log("tabsArray", tabsArray,activeStep);
  const tabContents = Object.entries(tabsData)
    .filter((item) => typeof item[1] == "object" && item[1] != null)
    .map((item) => Object.entries(item[1]));

  const tempHash = {};
    const gridArr = tabContents.map(item => {
      item.forEach((eachTab, i) => {
        eachTab.forEach((eachItem, i) => {
          if (i !== 0) {
            eachItem.forEach(fieldItem => {
              tempHash[fieldItem.fieldName.
               replaceAll("(", "")
              .replaceAll(")", "")
              .replaceAll("/", "")
              .replaceAll("-", "")
              .replaceAll(".", "")
              .split(" ")
              .join("")] =  fieldItem.value;
            });
          }
        });
      });
    });
    //console.log(JSON.stringify(tempHash),"temphash");
  const handleClosemodalData = (data) => { 
    setisChangeLogopen(data);
  }
  const openChangeLog = () => { 
    setisChangeLogopen(true);
  }
  console.log(singlePCPayloadAfterChange,requiredFieldTabWise,"========deeced")
  console.log(activeStep,"activeStep")
  const handleCheckValidationError = () => {
    
    return formValidator(
      singlePCPayloadAfterChange,
      requiredFieldTabWise,
      setFormValidationErrorItems
    );
  };
  const handleSnackBarCloseValidation = () => {
    setOpenSnackbarValidation(false);
  };
  //console.log(requiredFields, "requiredFields");
  return (
    <div>
      <Grid
        container
        style={{
          ...outermostContainer_Information,
          backgroundColor: "#FAFCFF",
        }}
      >
        {formValidationErrorItems.length != 0 && (
            <ReusableSnackBar
              openSnackBar={openSnackbarValidation}
              alertMsg={
                "Please fill the following Field: " +
                formValidationErrorItems.join(", ")
              }
              handleSnackBarClose={handleSnackBarCloseValidation}
            />
          )}
        <Grid sx={{ width: "inherit" }}>
          <Grid item md={12} style={{ padding: "16px", display: "flex" }}>
            {/* <Grid  sx={{ display: "flex" }}> */}

            <Grid item style={{ display: "flex", justifyContent: "flex-end" }}>
              <IconButton
                // onClick={handleBacktoRO}
                color="primary"
                aria-label="upload picture"
                component="label"
                sx={iconButton_SpacingSmall}
              >
                <ArrowCircleLeftOutlinedIcon
                  style={{ height: "1em", width: "1em", color: "#000000" }}
                  onClick={() => {
                    navigate(
                      "/masterDataCockpit/profitCenter/createMultipleProfitCenter"
                    );
                  }}
                />
              </IconButton>
            </Grid>

            <Grid md={10}>
              <Typography variant="h3">
                <strong>Multiple Profit Centers: {selectedRowData.profitCenter} </strong>
              </Typography>
              <Typography variant="body2" color="#777">
                This view dispalys detail of Multiple Profit Centers
              </Typography>
            </Grid>
            {location?.state?.requestNumber ?
            <Grid md={.5} sx={{ display: "flex", justifyContent: "flex-end" }}>
              <Button
                variant="outlined"
                size="small"
                sx={button_Outlined}
                onClick={openChangeLog}
                title="Change Log"
              >

                <TrackChangesTwoToneIcon
                sx={{ padding: "2px" }}
                fontSize="small"
                />
              </Button>
            </Grid>:<Grid md={.5} sx={{ display: "flex", justifyContent: "flex-end" }}></Grid>}
            {isChangeLogopen && <ChangeLog
            open={true}
            closeModal={handleClosemodalData}
            requestId={requestNumber}
            requestType={"Mass"}
            pageName={"profitCenter"}
            controllingArea={selectedRowData.controllingArea}
            centerName={selectedRowData.costCenter}
            />}

            {!isEditMode ? (
              <Grid md={1.5} sx={{ display: "flex", justifyContent: "flex-end" }}>
                <Grid item>
                  <Button
                    variant="outlined"
                    size="small"
                    sx={button_Outlined}
                    onClick={onEdit}
                  >
                    Change
                    <EditOutlinedIcon
                      sx={{ padding: "2px" }}
                      fontSize="small"
                    />
                  </Button>
                </Grid>
              </Grid>
            ) : (
              ""
            )}
        
          </Grid>
          <Grid container display="flex" flexDirection="row" flexWrap="nowrap">
            <Box width="70%" sx={{ marginLeft: "40px" }}>
              <Grid item sx={{ paddingTop: "2px !important" }}>
                <Stack flexDirection="row">
                  <div style={{ width: "15%" }}>
                    <Typography variant="body2" color="#777">
                      Profit Center
                    </Typography>
                  </div>
                  <Typography
                    variant="body2"
                    fontWeight="bold"
                    justifyContent="flex-start"
                  >
                    : {selectedRowData?.profitCenter}
                  </Typography>
                </Stack>
              </Grid>

              <Grid item sx={{ paddingTop: "2px !important" }}>
                <Stack flexDirection="row">
                  <div style={{ width: "15%" }}>
                    <Typography variant="body2" color="#777">
                      Controlling Area
                    </Typography>
                  </div>
                  <Typography variant="body2" fontWeight="bold">
                    : {selectedRowData?.controllingArea}
                  </Typography>
                </Stack>
              </Grid>
             
            </Box>
          </Grid>

          <Grid container style={{ padding: "16px" }}>
            <Stepper
              activeStep={activeStep}
              sx={{
                background: "#FFFFFF",
                borderBottom: "1px solid #BDBDBD",
                width: "100%",
                height: "48px",
              }}
              aria-label="mui tabs example"
            >
              {tabsArray.map((factor, index) => (
                <Step key={factor}>
                  <StepLabel sx={{ fontWeight: "700" }}>{factor}</StepLabel>
                </Step>
              ))}
            </Stepper>
            <Grid container>
              {tabContents &&
                tabContents[activeStep]?.map((item, index) => {
                  return activeStep === 2 ? (
                    <CompCodesProfitCenter
                      compCodesTabDetails={compCodesTabDetails}
                      displayCompCode={dispCompCode}
                    />
                  ) : (
                    <Box key={index} sx={{ width: "100%" }}>
                      <Grid
                        item
                        md={12}
                        sx={{
                          backgroundColor: "white",
                          maxHeight: "max-content",
                          height: "max-content",
                          borderRadius: "8px",
                          border: "1px solid #E0E0E0",
                          mt: 0.25,
                          boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                          ...container_Padding,
                          // ...container_columnGap,
                        }}
                      >
                        <Grid container>
                          <Typography
                            sx={{
                              fontSize: "12px",
                              fontWeight: "700",
                              margin: "0px !important",
                            }}
                          >
                            {item[0]}
                          </Typography>
                        </Grid>
                        <Box>
                          <Box sx={{ width: "100%" }}>
                            <CardContent
                              sx={{
                                padding: "0",
                                paddingBottom: "0 !important",
                                paddingTop: "10px !important",
                              }}
                            >
                              <Grid
                                container
                                style={{
                                  display: "grid",
                                  gridTemplateColumns: "repeat(6,1fr)",
                                  gap: "15px",
                                }}
                                justifyContent="space-between"
                                alignItems="flex-start"
                                md={12}
                              >
                                {[...item[1]].map((innerItem) => {
                                  console.log("inneritem", innerItem);
                                  return (
                                    <EditFieldForMassProfitCenter
                                      activeTabIndex={activeStep}
                                      fieldGroup={item[0]}
                                      selectedRowData={
                                        selectedRowData.profitCenter
                                      }
                                      
                                      pcTabs={tabsArray}
                                      label={innerItem.fieldName}
                                      value={innerItem.value}
                                      length={innerItem.maxLength}
                                      visibility={innerItem.visibility}
                                      onSave={(newValue) =>
                                        handleFieldSave(
                                          innerItem.fieldName,
                                          newValue
                                        )
                                      }
                                      isEditMode={isEditMode}
                                      // isExtendMode={isExtendMode}
                                      type={innerItem.fieldType}
                                      field={innerItem} // Update the type as needed
                                    />
                                  );
                                })}
                              </Grid>
                            </CardContent>
                          </Box>
                        </Box>
                      </Grid>
                      {/* <h1>{cardContent[0]}</h1>
                    {cardContent[1].map((item)=>{
                      return(<p>{item.fieldName}</p>)
                    })} */}
                    </Box>
                  );
                })}
            </Grid>
          </Grid>

        </Grid>
      </Grid>

      {isEditMode ? (
        <Paper
          sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
          elevation={2}
        >
          <BottomNavigation
            className="container_BottomNav"
            showLabels
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              gap: 1,
            }}
            value={value}
            onChange={(newValue) => {
              setValue(newValue);
            }}
          >
             <Button
              size="small"
              sx={{ ...button_Primary, mr: 1 }}
              variant="contained"
              onClick={() => {
                navigate(
                  "/masterDataCockpit/profitCenter/createMultipleProfitCenter"
                );
              }}
            >
              Save
            </Button>
            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary, mr: 1 }}
              onClick={handleBack}
              disabled={activeStep === 0}
            >
              Back
            </Button>
            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary, mr: 1 }}
              onClick={handleNext}
              disabled={activeStep === tabsArray.length - 1 ? true : false}
            >
              Next
            </Button>
           
          </BottomNavigation>
        </Paper>
      ) : (
        ""
      )}
      {!isEditMode ? (
        <Paper
          sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
          elevation={2}
        >
          <BottomNavigation
            className="container_BottomNav"
            showLabels
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              gap: 1,
            }}
            value={value}
            onChange={(newValue) => {
              setValue(newValue);
            }}
          >
            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary, mr: 1 }}
              onClick={handleBack}
              disabled={activeStep === 0}
            >
              Back
            </Button>
            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary, mr: 1 }}
              onClick={handleNext}
              disabled={activeStep === tabsArray.length - 1 ? true : false}
            >
              Next
            </Button>
            
          </BottomNavigation>
        </Paper>
      ) : (
        ""
      )}
    </div>
  );
};

export default EditMultipleProfitCenter;
