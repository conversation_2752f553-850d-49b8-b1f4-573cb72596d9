import{d$ as m,e0 as h,cI as x,p as C,e1 as N,cE as $}from"./index-75c1660a.js";function A(e,n){return e.classList?!!n&&e.classList.contains(n):(" "+(e.className.baseVal||e.className)+" ").indexOf(" "+n+" ")!==-1}function _(e,n){e.classList?e.classList.add(n):A(e,n)||(typeof e.className=="string"?e.className=e.className+" "+n:e.setAttribute("class",(e.className&&e.className.baseVal||"")+" "+n))}function g(e,n){return e.replace(new RegExp("(^|\\s)"+n+"(?:\\s|$)","g"),"$1").replace(/\s+/g," ").replace(/^\s*|\s*$/g,"")}function L(e,n){e.classList?e.classList.remove(n):typeof e.className=="string"?e.className=g(e.className,n):e.setAttribute("class",g(e.className&&e.className.baseVal||"",n))}var S=function(n,p){return n&&p&&p.split(" ").forEach(function(s){return _(n,s)})},u=function(n,p){return n&&p&&p.split(" ").forEach(function(s){return L(n,s)})},d=function(e){m(n,e);function n(){for(var s,i=arguments.length,l=new Array(i),o=0;o<i;o++)l[o]=arguments[o];return s=e.call.apply(e,[this].concat(l))||this,s.appliedClasses={appear:{},enter:{},exit:{}},s.onEnter=function(a,r){var t=s.resolveArguments(a,r),c=t[0],v=t[1];s.removeClasses(c,"exit"),s.addClass(c,v?"appear":"enter","base"),s.props.onEnter&&s.props.onEnter(a,r)},s.onEntering=function(a,r){var t=s.resolveArguments(a,r),c=t[0],v=t[1],f=v?"appear":"enter";s.addClass(c,f,"active"),s.props.onEntering&&s.props.onEntering(a,r)},s.onEntered=function(a,r){var t=s.resolveArguments(a,r),c=t[0],v=t[1],f=v?"appear":"enter";s.removeClasses(c,f),s.addClass(c,f,"done"),s.props.onEntered&&s.props.onEntered(a,r)},s.onExit=function(a){var r=s.resolveArguments(a),t=r[0];s.removeClasses(t,"appear"),s.removeClasses(t,"enter"),s.addClass(t,"exit","base"),s.props.onExit&&s.props.onExit(a)},s.onExiting=function(a){var r=s.resolveArguments(a),t=r[0];s.addClass(t,"exit","active"),s.props.onExiting&&s.props.onExiting(a)},s.onExited=function(a){var r=s.resolveArguments(a),t=r[0];s.removeClasses(t,"exit"),s.addClass(t,"exit","done"),s.props.onExited&&s.props.onExited(a)},s.resolveArguments=function(a,r){return s.props.nodeRef?[s.props.nodeRef.current,a]:[a,r]},s.getClassNames=function(a){var r=s.props.classNames,t=typeof r=="string",c=t&&r?r+"-":"",v=t?""+c+a:r[a],f=t?v+"-active":r[a+"Active"],E=t?v+"-done":r[a+"Done"];return{baseClassName:v,activeClassName:f,doneClassName:E}},s}var p=n.prototype;return p.addClass=function(i,l,o){var a=this.getClassNames(l)[o+"ClassName"],r=this.getClassNames("enter"),t=r.doneClassName;l==="appear"&&o==="done"&&t&&(a+=" "+t),o==="active"&&i&&h(i),a&&(this.appliedClasses[l][o]=a,S(i,a))},p.removeClasses=function(i,l){var o=this.appliedClasses[l],a=o.base,r=o.active,t=o.done;this.appliedClasses[l]={},a&&u(i,a),r&&u(i,r),t&&u(i,t)},p.render=function(){var i=this.props;i.classNames;var l=x(i,["classNames"]);return C.createElement(N,$({},l,{onEnter:this.onEnter,onEntered:this.onEntered,onEntering:this.onEntering,onExit:this.onExit,onExiting:this.onExiting,onExited:this.onExited}))},n}(C.Component);d.defaultProps={classNames:""};d.propTypes={};const w=d;export{w as C};
