import{d7 as P,d8 as y,cB as A,cE as p,r as b,cH as C,cI as L,d9 as j,o as a,da as $,cM as z,T as R,db as u,cN as M}from"./index-75c1660a.js";function N(n){return P("MuiInputAdornment",n)}const T=y("MuiInputAdornment",["root","filled","standard","outlined","positionStart","positionEnd","disablePointerEvents","hiddenLabel","sizeSmall"]),f=T;var g;const F=["children","className","component","disablePointerEvents","disableTypography","position","variant"],S=(n,t)=>{const{ownerState:e}=n;return[t.root,t[`position${u(e.position)}`],e.disablePointerEvents===!0&&t.disablePointerEvents,t[e.variant]]},U=n=>{const{classes:t,disablePointerEvents:e,hiddenLabel:o,position:s,size:r,variant:l}=n,d={root:["root",e&&"disablePointerEvents",s&&`position${u(s)}`,l,o&&"hiddenLabel",r&&`size${u(r)}`]};return M(d,N,t)},_=A("div",{name:"MuiInputAdornment",slot:"Root",overridesResolver:S})(({theme:n,ownerState:t})=>p({display:"flex",height:"0.01em",maxHeight:"2em",alignItems:"center",whiteSpace:"nowrap",color:(n.vars||n).palette.action.active},t.variant==="filled"&&{[`&.${f.positionStart}&:not(.${f.hiddenLabel})`]:{marginTop:16}},t.position==="start"&&{marginRight:8},t.position==="end"&&{marginLeft:8},t.disablePointerEvents===!0&&{pointerEvents:"none"})),w=b.forwardRef(function(t,e){const o=C({props:t,name:"MuiInputAdornment"}),{children:s,className:r,component:l="div",disablePointerEvents:d=!1,disableTypography:x=!1,position:m,variant:v}=o,E=L(o,F),i=j()||{};let c=v;v&&i.variant,i&&!c&&(c=i.variant);const h=p({},o,{hiddenLabel:i.hiddenLabel,size:i.size,disablePointerEvents:d,position:m,variant:c}),I=U(h);return a.jsx($.Provider,{value:null,children:a.jsx(_,p({as:l,ownerState:h,className:z(I.root,r),ref:e},E,{children:typeof s=="string"&&!x?a.jsx(R,{color:"text.secondary",children:s}):a.jsxs(b.Fragment,{children:[m==="start"?g||(g=a.jsx("span",{className:"notranslate",children:"​"})):null,s]})}))})}),H=w;export{H as I,N as g,f as i};
