import{d7 as g,d8 as f,cB as x,cE as c,r as y,cH as I,cI as N,o as b,cM as v,cN as E}from"./index-17b8d91e.js";function h(e){return g("MuiCardMedia",e)}const O=f("MuiCardMedia",["root","media","img"]),U=O,R=["children","className","component","image","src","style"],S=e=>{const{classes:s,isMediaComponent:t,isImageComponent:o}=e;return E({root:["root",t&&"media",o&&"img"]},h,s)},k=x("div",{name:"MuiCardMedia",slot:"Root",overridesResolver:(e,s)=>{const{ownerState:t}=e,{isMediaComponent:o,isImageComponent:a}=t;return[s.root,o&&s.media,a&&s.img]}})(({ownerState:e})=>c({display:"block",backgroundSize:"cover",backgroundRepeat:"no-repeat",backgroundPosition:"center"},e.isMediaComponent&&{width:"100%"},e.isImageComponent&&{objectFit:"cover"})),P=["video","audio","picture","iframe","img"],_=["picture","img"],j=y.forwardRef(function(s,t){const o=I({props:s,name:"MuiCardMedia"}),{children:a,className:l,component:i="div",image:n,src:p,style:d}=o,C=N(o,R),r=P.indexOf(i)!==-1,u=!r&&n?c({backgroundImage:`url("${n}")`},d):d,m=c({},o,{component:i,isMediaComponent:r,isImageComponent:_.indexOf(i)!==-1}),M=S(m);return b.jsx(k,c({className:v(M.root,l),as:i,role:!r&&n?"img":void 0,ref:t,style:u,ownerState:m,src:r?n||p:void 0},C,{children:a}))}),$=j;export{$ as C,U as c,h as g};
