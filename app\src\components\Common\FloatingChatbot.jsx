import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Paper,
  Fab,
  Typography,
  TextField,
  IconButton,
  Avatar,
  Chip,
  Fade,
  Slide,
  Divider,
  CircularProgress,
  Zoom,
  useTheme,
  alpha,
  Button,
  Card,
  CardContent,
  Tabs,
  Tab,
  Grid
} from '@mui/material';
import {
  Close as CloseIcon,
  Send as SendIcon,
  Minimize as MinimizeIcon,
  SmartToy as BotIcon,
  Person as PersonIcon,
  AutoAwesome as SparkleIcon,
  Psychology as PsychologyIcon,
  QuestionAnswer as GeneralIcon,
  ForkRight as NavigateIcon,
  Storage as DatabaseIcon,
  ArrowBack as BackIcon,
  Polyline as CreateIcon,
  ClearAll as ClearIcon,
  Android
} from '@mui/icons-material';
import ReplyIcon from '@mui/icons-material/Reply';
import { useSelector } from 'react-redux';
import { CHATBOT_CONFIG, CHATBOT_ICONS } from '@constant/chatbotConstants';
import { destination_AI } from "../../destinationVariables";
import { doAjax } from './fetchService';
import { useLocation, useNavigate } from 'react-router-dom';
import { PAGE_MAP, QUERY_MODES } from '../../constant/chatbotConstants';
import { API_CODE } from '../../constant/enum';
import { END_POINTS } from "../../constant/apiEndPoints"
import ReusableDialogTable from './ReusableDialogTable';
import botImage from "../../utilityImages/botImage.png";
import userIcon from "../../utilityImages/userIcon.png";
import { colors } from '@constant/colors';

const FloatingChatbot = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [currentMode, setCurrentMode] = useState(null);
  const [activeTab, setActiveTab] = useState(0);
  const [showCards, setShowCards] = useState(true);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const [tableDialogOpen, setTableDialogOpen] = useState(false);
  const [tableData, setTableData] = useState({ columns: [], rows: [] });
  
  // Separate message states for each mode
  const [messagesState, setMessagesState] = useState({
    [QUERY_MODES.REQ_QUERY]: [{
      id: 1,
      text: CHATBOT_CONFIG?.TEXT?.WELCOME_REQUESTS,
      sender: 'bot',
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    }],
    [QUERY_MODES.GNL_QUERY]: [{
      id: 1,
      text: CHATBOT_CONFIG?.TEXT?.WELCOME_GENERAL,
      sender: 'bot',
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    }],
    [QUERY_MODES.NAV_QUERY]: [{
      id: 1,
      text: CHATBOT_CONFIG?.TEXT?.WELCOME_NAVIGATION,
      sender: 'bot',
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    }],
    [QUERY_MODES.DB_QUERY]: [{
      id: 1,
      text: CHATBOT_CONFIG?.TEXT?.WELCOME_DATABASE,
      sender: 'bot',
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    }]
  });
  
  // Add request context state
  const [requestContext, setRequestContext] = useState({
    originalQuery: null,
    previousAiResponse: null,
    userCorrection: null,
    isFirstIteration: true
  });
  
  const messagesEndRef = useRef(null);
  const userData = useSelector((state) => state.userManagement?.userData);
  const navigate = useNavigate();
  const location = useLocation();

  // Get current messages based on active mode
  const getCurrentMessages = () => {
    if (!currentMode) return [];
    return messagesState[currentMode] || [];
  };

  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messagesState, currentMode]);

  // Card data configuration
  const cardData = [
    {
      id: QUERY_MODES.REQ_QUERY,
      title: CHATBOT_CONFIG.CARD_TEXTS.REQ_QUERY.TITLE,
      description: CHATBOT_CONFIG.CARD_TEXTS.REQ_QUERY.DESCRIPTION,
      icon: <CreateIcon sx={{ fontSize: 40, color: colors?.primary?.white }} />,
      color: CHATBOT_CONFIG.COLORS.SUCCESS,
      backgroundColor: CHATBOT_CONFIG.COLORS.SUCCESS,
      textColor: colors?.primary?.white
    },
    {
      id: QUERY_MODES.GNL_QUERY,
      title: CHATBOT_CONFIG.CARD_TEXTS.GNL_QUERY.TITLE,
      description: CHATBOT_CONFIG.CARD_TEXTS.GNL_QUERY.DESCRIPTION,
      icon: <GeneralIcon sx={{ fontSize: 40, color: colors?.primary?.white }} />,
      color: CHATBOT_CONFIG.COLORS.PRIMARY,
      backgroundColor: CHATBOT_CONFIG.COLORS.PRIMARY,
      textColor: colors?.primary?.white
    },
    {
      id: QUERY_MODES.NAV_QUERY,
      title: CHATBOT_CONFIG.CARD_TEXTS.NAV_QUERY.TITLE,
      description: CHATBOT_CONFIG.CARD_TEXTS.NAV_QUERY.DESCRIPTION,
      icon: <NavigateIcon sx={{ fontSize: 40, color: colors?.primary?.white }} />,
      color: CHATBOT_CONFIG.COLORS.WARNING,
      backgroundColor: CHATBOT_CONFIG.COLORS.WARNING,
      textColor: colors?.primary?.white
    },
    {
      id: QUERY_MODES.DB_QUERY,
      title: CHATBOT_CONFIG.CARD_TEXTS.DB_QUERY.TITLE,
      description: CHATBOT_CONFIG.CARD_TEXTS.DB_QUERY.DESCRIPTION,
      icon: <DatabaseIcon sx={{ fontSize: 40, color: colors?.primary?.white }} />,
      color: CHATBOT_CONFIG.COLORS.SECONDARY,
      backgroundColor: CHATBOT_CONFIG.COLORS.SECONDARY,
      textColor: colors?.primary?.white
    }
  ];

  const tabConfig = [
    { mode: QUERY_MODES.REQ_QUERY, label: 'Requests', icon: <CreateIcon /> },
    { mode: QUERY_MODES.GNL_QUERY, label: 'General', icon: <GeneralIcon /> },
    { mode: QUERY_MODES.NAV_QUERY, label: 'Navigate', icon: <NavigateIcon /> },
    { mode: QUERY_MODES.DB_QUERY, label: 'Database', icon: <DatabaseIcon /> }
  ];

  // API handlers (keeping existing functionality)
  const handleGeneralQuery = async (message) => {
    const payload = {
      "queryText": message,
      "topK": 1
    };

    try {
      const response = await new Promise((resolve, reject) => {
        const handleSuccess = (data) => {
          resolve(data.results[0]?.description || data.message || CHATBOT_CONFIG?.TEXT?.GNL_QUERY_SUCCESS);
        };

        const handleError = (error) => {
          reject(new Error(CHATBOT_CONFIG?.TEXT?.GNL_QUERY_FAILED));
        };

        doAjax(
          `/${destination_AI}${END_POINTS?.CHATBOT_QUERY_APIS?.GNL_QUERY_API}`,
          'post',
          handleSuccess,
          handleError,
          payload
        );
      });

      return response;
    } catch (error) {
      return CHATBOT_CONFIG?.TEXT?.GNL_QUERY_FAILED;
    }
  };

  const handleDatabaseQuery = async (message) => {
    const payload = {
      "naturalLanguageQuery": message,
      "schema": "CW-MM-DEV",
      "maxResults": 100,
      "validateOnly": false,
      "includeExplanation": true
    };

    try {
      const response = await new Promise((resolve, reject) => {
        const handleSuccess = (data) => {
          if (data?.data && Array.isArray(data.data) && data.data.length > 0) {
            const firstObject = data?.data[0] || {};
            const columns = Object.keys(firstObject)
              ?.filter(key => {
                const value = firstObject[key];
                return value !== null && value !== undefined && value !== '';
              })
              .map(key => ({
                field: key,
                headerName: key.replace(/_/g, ' ').toUpperCase(),
                width: 200,
                headerAlign: "center",
                align: "center",
                fontWeight: 800
              }));

            const response = {
              isDb: true,
              columns,
              rows: data.data,
              message: CHATBOT_CONFIG?.TEXT?.DB_QUERY_SUCCESS
            };
            resolve(response);
          } else {
            resolve({
              isDb: false,
              message: data.message || CHATBOT_CONFIG?.TEXT?.DB_QUERY_FAILED
            });
          }
        };

        const handleError = (error) => {
          reject(new Error(CHATBOT_CONFIG?.TEXT?.DB_QUERY_FAILED));
        };

        doAjax(
          `/${destination_AI}${END_POINTS?.CHATBOT_QUERY_APIS?.DB_QUERY_API}`,
          'post',
          handleSuccess,
          handleError,
          payload
        );
      });

      return response;
    } catch (error) {
      return CHATBOT_CONFIG?.TEXT?.DB_QUERY_FAILED;
    }
  };

  const handleNavigationQuery = async (message) => {
    const currentPage = getCurrentPageFromUrl();

    const payload = {
      "userId": userData?.emailId || "",
      "context": message,
      "currentPage": currentPage
    };

    try {
      const response = await new Promise((resolve, reject) => {
        const handleSuccess = (data) => {
          if (data?.statusCode === API_CODE?.STATUS_200) {
            const route = data?.body?.route;
            const targetPage = data.body.targetPage;
            let navigationResponse = {};

            if (route) {
              navigationResponse = {
                message: `${CHATBOT_CONFIG?.TEXT?.NAV_QUERY_SUCCESS} ${targetPage}`,
                route: route,
                targetPage: targetPage
              };
            } else {
              navigationResponse = {
                message: `${data?.body?.message}`,
              };
            }

            resolve(navigationResponse);
          } else {
            resolve({
              message: data.message || CHATBOT_CONFIG?.TEXT?.NAV_QUERY_FAILED,
              route: null,
              targetPage: null
            });
          }
        };

        const handleError = (error) => {
          reject(new Error(CHATBOT_CONFIG?.TEXT?.NAV_QUERY_FAILED));
        };

        doAjax(
          `/${destination_AI}${END_POINTS?.CHATBOT_QUERY_APIS?.NAV_QUERY_API}`,
          'post',
          handleSuccess,
          handleError,
          payload
        );
      });

      return response;
    } catch (error) {
      return {
        message: CHATBOT_CONFIG?.TEXT?.NAV_QUERY_FAILED,
        route: null,
        targetPage: null
      };
    }
  };

  const handleRequestQuery = async (message) => {
    let payload;

    if (requestContext?.isFirstIteration) {
      payload = {
        "originalQuery": message,
        "previousAiResponse": null,
        "userCorrection": null,
        "iterationCount": 0
      };
    } else {
      payload = {
        "originalQuery": requestContext.userCorrection || requestContext.originalQuery,
        "previousAiResponse": requestContext.previousAiResponse,
        "userCorrection": message,
        "iterationCount": 0
      };
    }

    try {
      const response = await new Promise((resolve, reject) => {
        const handleSuccess = (data) => {
          if (data?.response) {
            let requestResponse = {};

            requestResponse = {
              ...data,
              message: data?.response
            };

            resolve(requestResponse);
          } else {
            resolve({
              ...data,
              message: data?.response,
              requiresUserInput: false
            });
          }
        };

        const handleError = (error) => {
          reject(new Error(CHATBOT_CONFIG?.TEXT?.NAV_QUERY_FAILED));
        };

        doAjax(
          `/${destination_AI}${END_POINTS?.CHATBOT_QUERY_APIS?.REQ_QUERY_API}`,
          'post',
          handleSuccess,
          handleError,
          payload
        );
      });

      return response;
    } catch (error) {
      return {
        message: CHATBOT_CONFIG?.TEXT?.NAV_QUERY_FAILED,
        route: null,
        targetPage: null,
        requiresUserInput: false
      };
    }
  };

  const getCurrentPageFromUrl = () => {
    const currentPath = location.pathname;
    return PAGE_MAP[currentPath] || 'dashboard';
  };

  // Enhanced Robot Icon Component
  const RobotIcon = () => (
  <Box
    sx={{
      position: 'relative',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      width: '30px',
      height: '30px',
    }}
  >
    <img
      src={botImage}
      alt=""
      style={{ 
        width: "75px", 
        height: "75px", 
        opacity: 0.9, 
        borderRadius: "50%",
        position: 'relative',
        marginLeft: "15px",
        zIndex: 2,
        animation: `jump ${CHATBOT_CONFIG.ANIMATION.PULSE_DURATION || '2s'} infinite ease-in-out`
      }}
    />
  </Box>
);

  // Handle card selection
  const handleCardSelect = (modeId) => {
    setCurrentMode(modeId);
    setShowCards(false);
    setActiveTab(tabConfig?.findIndex(tab => tab.mode === modeId));
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    setCurrentMode(tabConfig[newValue].mode);
  };

  // Send message function
  const sendMessage = async () => {
    if (!inputMessage.trim() || !currentMode) return;

    const userMessage = {
      id: Date.now(),
      text: inputMessage,
      sender: 'user',
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };

    // Add message to current mode's messages
    setMessagesState(prev => ({
      ...prev,
      [currentMode]: [...prev[currentMode], userMessage]
    }));

    const messageToProcess = inputMessage;
    setInputMessage('');
    setIsTyping(true);

    try {
      let botResponse;
      if (currentMode === QUERY_MODES?.GNL_QUERY) {
        botResponse = await handleGeneralQuery(messageToProcess);
      } else if (currentMode === QUERY_MODES?.DB_QUERY) {
        botResponse = await handleDatabaseQuery(messageToProcess);
      } else if (currentMode === QUERY_MODES?.NAV_QUERY) {
        botResponse = await handleNavigationQuery(messageToProcess);
      } else if (currentMode === QUERY_MODES?.REQ_QUERY) {
        botResponse = await handleRequestQuery(messageToProcess);

        // Handle request context updates
        if (botResponse?.requiresUserInput) {
          setRequestContext(prev => ({
            originalQuery: prev.isFirstIteration ? messageToProcess : (prev.userCorrection || prev.originalQuery),
            previousAiResponse: botResponse.message,
            userCorrection: prev.isFirstIteration ? null : messageToProcess,
            isFirstIteration: false
          }));
        } else {
          setRequestContext({
            originalQuery: null,
            previousAiResponse: null,
            userCorrection: null,
            isFirstIteration: true
          });
        }
      }

      const botMessage = {
        id: Date.now() + 1,
        text: currentMode === QUERY_MODES?.NAV_QUERY ? botResponse?.message : (botResponse?.message || botResponse),
        sender: 'bot',
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
        ...(currentMode === QUERY_MODES?.NAV_QUERY && botResponse?.route && {
          routePath: botResponse.route,
          targetPage: botResponse.targetPage
        }),
        ...(currentMode === QUERY_MODES?.DB_QUERY && botResponse?.isDb && {
          isDb: botResponse.isDb,
          columns: botResponse.columns,
          rows: botResponse.rows
        })
      };

      setMessagesState(prev => ({
        ...prev,
        [currentMode]: [...prev[currentMode], botMessage]
      }));
    } catch (error) {
      const errorMessage = {
        id: Date.now() + 1,
        text: CHATBOT_CONFIG?.TEXT?.PROCESS_FAILED,
        sender: 'bot',
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      };
      setMessagesState(prev => ({
        ...prev,
        [currentMode]: [...prev[currentMode], errorMessage]
      }));
    } finally {
      setIsTyping(false);
    }
  };

  // Clear chat for current mode
  const clearChat = () => {
    if (!currentMode) return;
    
    const initialMessage = {
      id: 1,
      text: currentMode === QUERY_MODES.REQ_QUERY ? CHATBOT_CONFIG.TEXT.WELCOME_REQUESTS :
            currentMode === QUERY_MODES.GNL_QUERY ? CHATBOT_CONFIG.TEXT.WELCOME_GENERAL :
            currentMode === QUERY_MODES.NAV_QUERY ? CHATBOT_CONFIG.TEXT.WELCOME_NAVIGATION :
             CHATBOT_CONFIG.TEXT.WELCOME_DATABASE,
      sender: 'bot',
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };

    setMessagesState(prev => ({
      ...prev,
      [currentMode]: [initialMessage]
    }));
  };

  // Go back to cards
  const goBackToCards = () => {
    setShowCards(true);
    setCurrentMode(null);
    setActiveTab(0);
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const toggleChat = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      setUnreadCount(0);
      setIsMinimized(false);
    }
  };

  const toggleMinimize = () => {
    setIsMinimized(!isMinimized);
  };

  // Render message function
  const renderMessage = (message) => {
    const isBot = message.sender === 'bot';

    const handleNavigation = () => {
      if (message.routePath) {
        navigate(message.routePath);
      }
    };

    return (
      <Fade in={true} key={message.id} timeout={500}>
        <Box
          sx={{
            display: 'flex',
            justifyContent: isBot ? 'flex-start' : 'flex-end',
            mb: CHATBOT_CONFIG.SPACING.MD,
            alignItems: 'flex-end'
          }}
        >
          {isBot && (
            <Box sx={{ mr: CHATBOT_CONFIG.SPACING.SM }}>
                <img
                  src={botImage}
                  alt=""
                  style={{ 
                    width: "55px", 
                    height: "55px", 
                    opacity: 0.9, 
                    borderRadius: "50%",
                    position: 'relative',
                    zIndex: 2,
                    marginLeft: "-10px",
                  }}
                />
            </Box>
          )}
          <Box
            sx={{
              maxWidth: CHATBOT_CONFIG.LIMITS.MAX_MESSAGE_WIDTH,
              background: isBot ? CHATBOT_CONFIG.COLORS.BOT_MESSAGE_BG : CHATBOT_CONFIG.COLORS.USER_MESSAGE_BG,
              color: isBot ? CHATBOT_CONFIG.COLORS.TEXT_PRIMARY : CHATBOT_CONFIG.COLORS.TEXT_WHITE,
              px: CHATBOT_CONFIG.SPACING.MD,
              py: CHATBOT_CONFIG.SPACING.SM,
              borderRadius: isBot ? CHATBOT_CONFIG.RADIUS.MESSAGE_RECEIVER : CHATBOT_CONFIG.RADIUS.MESSAGE_SENDER,
              wordWrap: 'break-word',
              boxShadow: CHATBOT_CONFIG.SHADOWS.LIGHT,
              position: 'relative',
              '&::before': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: isBot ? 'none' : `linear-gradient(135deg, ${alpha('#fff', 0.1)} 0%, ${alpha('#fff', 0.05)} 100%)`,
                borderRadius: 'inherit',
                pointerEvents: 'none'
              }
            }}
          >
            <Typography
              variant="body2"
              sx={{
                fontSize: CHATBOT_CONFIG.TYPOGRAPHY.FONT_SIZE.REGULAR,
                lineHeight: 1.4,
                fontWeight: CHATBOT_CONFIG.TYPOGRAPHY.FONT_WEIGHT.NORMAL
              }}
            >
              {message.text}
            </Typography>

            {/* Navigation Link - only for bot messages with routePath */}
            {isBot && message.routePath && (
              <Box sx={{ mt: CHATBOT_CONFIG.SPACING.SM }}>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={handleNavigation}
                  sx={{
                    textTransform: 'none',
                    borderColor: CHATBOT_CONFIG.COLORS.PRIMARY,
                    color: CHATBOT_CONFIG.COLORS.PRIMARY,
                    fontSize: CHATBOT_CONFIG.TYPOGRAPHY.FONT_SIZE.SMALL,
                    borderRadius: CHATBOT_CONFIG.RADIUS.SMALL,
                    '&:hover': {
                      backgroundColor: CHATBOT_CONFIG.COLORS.PRIMARY,
                      color: CHATBOT_CONFIG.COLORS.CHAT_BACKGROUND,
                      transform: 'translateY(-1px)',
                      boxShadow: CHATBOT_CONFIG.SHADOWS.LIGHT
                    },
                    transition: 'all 0.2s ease'
                  }}
                >
                  <ReplyIcon sx={{ fontSize: 20, mr: 1 }} />
                  Click here to navigate
                </Button>
              </Box>
            )}
            {isBot && message?.isDb && message?.columns && message?.rows && (
              <Box sx={{ mt: CHATBOT_CONFIG.SPACING.SM }}>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => {
                    setTableData({ columns: message?.columns, rows: message?.rows });
                    setTableDialogOpen(true);
                  }}
                  sx={{
                    textTransform: 'none',
                    borderColor: CHATBOT_CONFIG.COLORS.SECONDARY,
                    color: CHATBOT_CONFIG.COLORS.SECONDARY,
                    fontSize: CHATBOT_CONFIG.TYPOGRAPHY.FONT_SIZE.SMALL,
                    borderRadius: CHATBOT_CONFIG.RADIUS.SMALL,
                    '&:hover': {
                      backgroundColor: CHATBOT_CONFIG.COLORS.SECONDARY,
                      color: CHATBOT_CONFIG.COLORS.CHAT_BACKGROUND,
                      transform: 'translateY(-1px)',
                      boxShadow: CHATBOT_CONFIG.SHADOWS.LIGHT
                    },
                    transition: 'all 0.2s ease'
                  }}
                >
                  <DatabaseIcon sx={{ fontSize: 20, mr: 1 }} />
                  Click here to display the Table
                </Button>
              </Box>
            )}

            <Typography
              variant="caption"
              sx={{
                fontSize: CHATBOT_CONFIG.TYPOGRAPHY.FONT_SIZE.TINY,
                opacity: 0.7,
                display: 'block',
                textAlign: 'right',
                mt: CHATBOT_CONFIG.SPACING.XS,
                fontWeight: CHATBOT_CONFIG.TYPOGRAPHY.FONT_WEIGHT.MEDIUM
              }}
            >
              {message.timestamp}
            </Typography>
          </Box>
          {!isBot && (
            <Box sx={{ ml: CHATBOT_CONFIG.SPACING.SM }}>
              <img
                  src={userIcon}
                  alt=""
                  style={{ 
                    width: "35px", 
                    height: "35px", 
                    opacity: 0.9, 
                    borderRadius: "50%",
                    position: 'relative',
                    zIndex: 2,
                  }}
                />
            </Box>
          )}
        </Box>
      </Fade>
    );
  };

  // Render cards view
  const renderCards = () => (
    <Box sx={{ p: CHATBOT_CONFIG.SPACING.MD, background: CHATBOT_CONFIG.COLORS.MESSAGES_BACKGROUND, }}>
      <Typography
        variant="h6"
        sx={{
          mt: CHATBOT_CONFIG.SPACING.MD,
          mb: CHATBOT_CONFIG.SPACING.LG,
          textAlign: 'center',
          color: CHATBOT_CONFIG.COLORS.TEXT_PRIMARY,
          fontWeight: CHATBOT_CONFIG.TYPOGRAPHY.FONT_WEIGHT.SEMIBOLD,
        }}
      >
        How can I assist you today?
      </Typography>
      <Grid container spacing={2}>
        {cardData?.map((card) => (
          <Grid item xs={6} key={card.id}>
            <Card
              sx={{
                cursor: 'pointer',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                background: card.backgroundColor,
                border: 'none',
                borderRadius: '12px',
                minHeight: '120px',
                display: 'flex',
                alignItems: 'center',
                position: 'relative',
                overflow: 'hidden',
                '&:hover': {
                  transform: 'translateY(-6px)',
                  boxShadow: `0 12px 28px ${alpha(card.color, 0.3)}`,
                  '&::before': {
                    opacity: 1
                  }
                },
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
                  opacity: 0,
                  transition: 'opacity 0.3s ease'
                }
              }}
              onClick={() => handleCardSelect(card.id)}
            >
              <CardContent 
                sx={{ 
                  textAlign: 'center', 
                  py: 2.5, 
                  px: 2,
                  position: 'relative',
                  zIndex: 1,
                  width: '100%'
                }}
              >
                <Box sx={{ mb: 1.5 }}>
                  {card.icon}
                </Box>
                <Typography
                  variant="subtitle2"
                  sx={{
                    fontWeight: CHATBOT_CONFIG.TYPOGRAPHY.FONT_WEIGHT.BOLD,
                    color: card.textColor,
                    mb: 0.5,
                    fontSize: '14px',
                    letterSpacing: '0.5px'
                  }}
                >
                  {card.title}
                </Typography>
                <Typography
                  variant="caption"
                  sx={{
                    color: alpha(card.textColor, 0.9),
                    fontSize: '11px',
                    lineHeight: 1.3,
                    fontWeight: CHATBOT_CONFIG.TYPOGRAPHY.FONT_WEIGHT.MEDIUM
                  }}
                >
                  {card.description}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );

  return (
    <Box
      sx={{
        position: 'fixed',
        bottom: CHATBOT_CONFIG.POSITION.BOTTOM,
        left: CHATBOT_CONFIG.POSITION.LEFT,
        zIndex: CHATBOT_CONFIG.POSITION.Z_INDEX,
      }}
    >
      {/* Enhanced Chat Window */}
      <Slide direction="up" in={isOpen} mountOnEnter unmountOnExit>
        <Paper
          elevation={0}
          sx={{
            width: CHATBOT_CONFIG.DIMENSIONS.CHAT_WIDTH,
            height: isMinimized ? CHATBOT_CONFIG.DIMENSIONS.CHAT_HEIGHT_MINIMIZED : CHATBOT_CONFIG.DIMENSIONS.CHAT_HEIGHT_OPEN,
            borderRadius: CHATBOT_CONFIG.RADIUS.LARGE,
            overflow: 'hidden',
            boxShadow: CHATBOT_CONFIG.SHADOWS.HEAVY,
            border: `1px solid ${CHATBOT_CONFIG.COLORS.BORDER_LIGHT}`,
            mb: CHATBOT_CONFIG.SPACING.MD,
            transition: `height ${CHATBOT_CONFIG.ANIMATION.TRANSITION_DURATION} ease`,
            background: CHATBOT_CONFIG.COLORS.MESSAGES_BACKGROUND,
            backdropFilter: 'blur(10px)',
          }}
        >
          {/* Enhanced Header */}
          <Box
            sx={{
              background: CHATBOT_CONFIG.COLORS.HEADER_BACKGROUND,
              color: CHATBOT_CONFIG.COLORS.TEXT_WHITE,
              p: CHATBOT_CONFIG.SPACING.MD,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              position: 'relative',
              '&::after': {
                content: '""',
                position: 'absolute',
                background: `linear-gradient(90deg, ${alpha(CHATBOT_CONFIG.COLORS.PRIMARY, 0.1)} 0%, ${alpha(CHATBOT_CONFIG.COLORS.SECONDARY, 0.1)} 100%)`
              }
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <img
                  src={botImage}
                  alt=""
                  style={{ 
                    width: "55px", 
                    height: "55px", 
                    opacity: 0.9, 
                    borderRadius: "50%",
                    position: 'relative',
                    zIndex: 2,
                    marginLeft: "-10px",
                    animation: `jump ${CHATBOT_CONFIG.ANIMATION.PULSE_DURATION || '2s'} infinite ease-in-out`
                  }}
                />
              <Box>
                <Typography
                  variant="subtitle2"
                  sx={{
                    fontWeight: CHATBOT_CONFIG.TYPOGRAPHY.FONT_WEIGHT.SEMIBOLD,
                    fontSize: CHATBOT_CONFIG.TYPOGRAPHY.FONT_SIZE.MEDIUM
                  }}
                >
                  {CHATBOT_CONFIG.TEXT.ASSISTANT_NAME}
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <Box
                    sx={{
                      width: 6,
                      height: 6,
                      borderRadius: '50%',
                      bgcolor: CHATBOT_CONFIG.COLORS.SUCCESS,
                      animation: `pulse ${CHATBOT_CONFIG.ANIMATION.PULSE_DURATION} infinite`
                    }}
                  />
                  <Typography
                    variant="caption"
                    sx={{
                      fontSize: CHATBOT_CONFIG.TYPOGRAPHY.FONT_SIZE.SMALL,
                      color: CHATBOT_CONFIG.COLORS.TEXT_MUTED
                    }}
                  >
                    {CHATBOT_CONFIG.TEXT.STATUS_ONLINE}
                  </Typography>
                </Box>
              </Box>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <IconButton
                onClick={toggleMinimize}
                sx={{
                  color: CHATBOT_CONFIG.COLORS.TEXT_WHITE,
                  '&:hover': {
                    bgcolor: alpha(CHATBOT_CONFIG.COLORS.TEXT_WHITE, 0.1)
                  }
                }}
              >
                <MinimizeIcon sx={{ fontSize: CHATBOT_ICONS.SIZES.MEDIUM }} />
              </IconButton>
              <IconButton
                onClick={toggleChat}
                sx={{
                  color: CHATBOT_CONFIG.COLORS.TEXT_WHITE,
                  '&:hover': {
                    bgcolor: alpha(CHATBOT_CONFIG.COLORS.TEXT_WHITE, 0.1)
                  }
                }}
              >
                <CloseIcon sx={{ fontSize: CHATBOT_ICONS.SIZES.MEDIUM }} />
              </IconButton>
            </Box>
          </Box>

          {/* Content Area */}
          {!isMinimized && (
            <Box sx={{ height: CHATBOT_CONFIG.DIMENSIONS.MESSAGES_HEIGHT, display: 'flex', flexDirection: 'column' }}>
              {showCards ? (
                renderCards()
              ) : (
                <>
                  {/* Tabs */}
                  <Box sx={{ borderBottom: 1, borderColor: 'divider', bgcolor: 'background.paper' }}>
                    <Tabs
                      value={activeTab}
                      onChange={handleTabChange}
                      sx={{
                        '& .MuiTab-root': {
                          minWidth: 'auto',
                          minHeight: '40px',
                          marginTop: '7px',
                          fontSize: CHATBOT_CONFIG.TYPOGRAPHY.FONT_SIZE.SMALL,
                          fontWeight: CHATBOT_CONFIG.TYPOGRAPHY.FONT_WEIGHT.MEDIUM,
                          textTransform: 'none',
                          color: CHATBOT_CONFIG.COLORS.TEXT_SECONDARY,
                          '&.Mui-selected': {
                            color: CHATBOT_CONFIG.COLORS.PRIMARY
                          }
                        }
                      }}
                    >
                      {tabConfig?.map((tab, index) => (
                        <Tab
                          key={tab.mode}
                          label={tab.label}
                          icon={React.cloneElement(tab.icon, { sx: { fontSize: CHATBOT_ICONS.SIZES.SMALL } })}
                          iconPosition="start"
                        />
                      ))}
                    </Tabs>
                  </Box>

                  {/* Header Actions */}
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      px: CHATBOT_CONFIG.SPACING.MD,
                      py: CHATBOT_CONFIG.SPACING.SM,
                      bgcolor: 'background.paper',
                      borderBottom: `1px solid ${CHATBOT_CONFIG.COLORS.BORDER_LIGHT}`
                    }}
                  >
                    <IconButton
                      onClick={goBackToCards}
                      size="small"
                      sx={{
                        color: CHATBOT_CONFIG.COLORS.TEXT_SECONDARY,
                        '&:hover': {
                          color: CHATBOT_CONFIG.COLORS.PRIMARY,
                          bgcolor: alpha(CHATBOT_CONFIG.COLORS.PRIMARY, 0.1)
                        }
                      }}
                    >
                      <BackIcon sx={{ fontSize: CHATBOT_ICONS.SIZES.MEDIUM }} />
                    </IconButton>
                    <Typography
                      variant="body2"
                      sx={{
                        color: CHATBOT_CONFIG.COLORS.TEXT_PRIMARY,
                        fontWeight: CHATBOT_CONFIG.TYPOGRAPHY.FONT_WEIGHT.MEDIUM,
                        fontSize: CHATBOT_CONFIG.TYPOGRAPHY.FONT_SIZE.REGULAR
                      }}
                    >
                      {tabConfig[activeTab]?.label}
                    </Typography>
                    <IconButton
                      onClick={clearChat}
                      size="small"
                      sx={{
                        color: CHATBOT_CONFIG.COLORS.TEXT_SECONDARY,
                        '&:hover': {
                          color: CHATBOT_CONFIG.COLORS.DANGER,
                          bgcolor: alpha(CHATBOT_CONFIG.COLORS.DANGER, 0.1)
                        }
                      }}
                    >
                      <ClearIcon sx={{ fontSize: CHATBOT_ICONS.SIZES.MEDIUM }} />
                    </IconButton>
                  </Box>

                  {/* Messages Container */}
                  <Box
                    sx={{
                      flex: 1,
                      overflowY: 'auto',
                      p: CHATBOT_CONFIG.SPACING.MD,
                      background: CHATBOT_CONFIG.COLORS.MESSAGES_BACKGROUND,
                      '&::-webkit-scrollbar': {
                        width: CHATBOT_CONFIG.SCROLLBAR.WIDTH
                      },
                      '&::-webkit-scrollbar-track': {
                        background: CHATBOT_CONFIG.SCROLLBAR.TRACK_COLOR,
                        borderRadius: CHATBOT_CONFIG.SCROLLBAR.BORDER_RADIUS
                      },
                      '&::-webkit-scrollbar-thumb': {
                        background: CHATBOT_CONFIG.SCROLLBAR.THUMB_COLOR,
                        borderRadius: CHATBOT_CONFIG.SCROLLBAR.BORDER_RADIUS,
                        '&:hover': {
                          background: CHATBOT_CONFIG.SCROLLBAR.THUMB_HOVER_COLOR
                        }
                      }
                    }}
                  >
                    {getCurrentMessages().map(renderMessage)}
                    
                    {/* Typing Indicator */}
                    {isTyping && (
                      <Fade in={true}>
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: CHATBOT_CONFIG.SPACING.SM,
                            mb: CHATBOT_CONFIG.SPACING.MD
                          }}
                        >
                          <img
                            src={botImage}
                            alt=""
                            style={{ 
                              width: "55px", 
                              height: "55px", 
                              opacity: 0.9, 
                              borderRadius: "50%",
                              position: 'relative',
                              zIndex: 2,
                              marginLeft: "-10px",
                            }}
                          />

                          <Box
                            sx={{
                              background: CHATBOT_CONFIG.COLORS.BOT_MESSAGE_BG,
                              px: CHATBOT_CONFIG.SPACING.MD,
                              py: CHATBOT_CONFIG.SPACING.SM,
                              borderRadius: CHATBOT_CONFIG.RADIUS.MESSAGE_RECEIVER,
                              display: 'flex',
                              alignItems: 'center',
                              gap: 0.5
                            }}
                          >
                            <CircularProgress size={12} thickness={4} />
                            <Typography
                              variant="body2"
                              sx={{
                                fontSize: CHATBOT_CONFIG.TYPOGRAPHY.FONT_SIZE.SMALL,
                                color: CHATBOT_CONFIG.COLORS.TEXT_SECONDARY,
                                ml: 1
                              }}
                            >
                              {CHATBOT_CONFIG.TEXT.TYPING_INDICATOR}
                            </Typography>
                          </Box>
                        </Box>
                      </Fade>
                    )}
                    <div ref={messagesEndRef} />
                  </Box>

                  {/* Input Area */}
                  <Box
                    sx={{
                      p: CHATBOT_CONFIG.SPACING.MD,
                      bgcolor: CHATBOT_CONFIG.COLORS.INPUT_BACKGROUND,
                      borderTop: `1px solid ${CHATBOT_CONFIG.COLORS.BORDER_LIGHT}`,
                      marginBottom: "-100px"
                    }}
                  >
                    <Box sx={{ display: 'flex', gap: CHATBOT_CONFIG.SPACING.SM, alignItems: 'flex-end' }}>
                      <TextField
                        fullWidth
                        multiline
                        maxRows={CHATBOT_CONFIG.LIMITS.MAX_MESSAGE_ROWS}
                        value={inputMessage}
                        onChange={(e) => setInputMessage(e.target.value)}
                        onKeyPress={handleKeyPress}
                        placeholder={CHATBOT_CONFIG.TEXT.INPUT_PLACEHOLDER}
                        variant="outlined"
                        size="small"
                        disabled={isTyping}
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            borderRadius: CHATBOT_CONFIG.RADIUS.MEDIUM,
                            backgroundColor: CHATBOT_CONFIG.COLORS.CHAT_BACKGROUND,
                            '& fieldset': {
                              borderColor: CHATBOT_CONFIG.COLORS.BORDER_LIGHT
                            },
                            '&:hover fieldset': {
                              borderColor: CHATBOT_CONFIG.COLORS.BORDER_MEDIUM
                            },
                            '&.Mui-focused fieldset': {
                              borderColor: CHATBOT_CONFIG.COLORS.PRIMARY
                            }
                          },
                          '& .MuiInputBase-input': {
                            fontSize: CHATBOT_CONFIG.TYPOGRAPHY.FONT_SIZE.REGULAR
                          }
                        }}
                      />
                      <Zoom in={inputMessage.trim().length > 0}>
                        <IconButton
                          onClick={sendMessage}
                          disabled={!inputMessage.trim() || isTyping}
                          sx={{
                            bgcolor: CHATBOT_CONFIG.COLORS.PRIMARY,
                            color: CHATBOT_CONFIG.COLORS.CHAT_BACKGROUND,
                            '&:hover': {
                              bgcolor: CHATBOT_CONFIG.COLORS.PRIMARY_DARK,
                              transform: 'scale(1.05)'
                            },
                            '&:disabled': {
                              bgcolor: CHATBOT_CONFIG.COLORS.TEXT_SECONDARY,
                              color: CHATBOT_CONFIG.COLORS.CHAT_BACKGROUND
                            },
                            transition: 'all 0.2s ease'
                          }}
                        >
                          <SendIcon sx={{ fontSize: CHATBOT_ICONS.SIZES.MEDIUM }} />
                        </IconButton>
                      </Zoom>
                    </Box>
                  </Box>
                </>
              )}
            </Box>
          )}
        </Paper>
      </Slide>

      {/* Enhanced Floating Action Button */}
      <Zoom in={!isOpen}>
        <Fab
          onClick={toggleChat}
          sx={{
            background: CHATBOT_CONFIG.COLORS.HEADER_BACKGROUND,
            color: CHATBOT_CONFIG.COLORS.TEXT_WHITE,
            width: CHATBOT_CONFIG.DIMENSIONS.FAB_SIZE,
            height: CHATBOT_CONFIG.DIMENSIONS.FAB_SIZE,
            boxShadow: CHATBOT_CONFIG.SHADOWS.FAB,
            position: 'relative',
            '&:hover': {
              transform: 'scale(1.1)',
              boxShadow: CHATBOT_CONFIG.SHADOWS.GLOW
            },
            '&:active': {
              transform: 'scale(0.95)'
            },
            transition: 'all 0.3s ease',
            marginBottom: '10px'
          }}
        >
          <RobotIcon size={CHATBOT_ICONS.SIZES.LARGE} />
          {unreadCount > 0 && (
            <Chip
              label={unreadCount > CHATBOT_CONFIG.LIMITS.UNREAD_COUNT_MAX ? CHATBOT_CONFIG.TEXT.UNREAD_COUNT_MAX : unreadCount}
              size="small"
              sx={{
                position: 'absolute',
                top: -8,
                right: -8,
                bgcolor: CHATBOT_CONFIG.COLORS.DANGER,
                color: CHATBOT_CONFIG.COLORS.TEXT_WHITE,
                fontSize: CHATBOT_CONFIG.TYPOGRAPHY.FONT_SIZE.TINY,
                fontWeight: CHATBOT_CONFIG.TYPOGRAPHY.FONT_WEIGHT.BOLD,
                minWidth: 20,
                height: 20,
                '& .MuiChip-label': {
                  px: 0.5
                }
              }}
            />
          )}
        </Fab>
      </Zoom>

      {/* Table Dialog */}
      {
        tableDialogOpen && (
          <ReusableDialogTable
            tableDialogOpen = {tableDialogOpen}
            onClose={() => setTableDialogOpen(false)}
            tableData={tableData}
          />
        )
      }

      {/* CSS Animations */}
      <style jsx global>{`
        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.5; }
          100% { opacity: 1; }
        }
        
        @keyframes bounce {
          0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
          }
          40% {
            transform: translateY(-10px);
          }
          60% {
            transform: translateY(-5px);
          }
        }
        
        @keyframes jump {
          0%, 100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-8px);
          }
        }
      `}</style>
    </Box>
  );
};

export default FloatingChatbot;