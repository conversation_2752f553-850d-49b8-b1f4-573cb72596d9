import {
  Backdrop,
  BottomNavi<PERSON>,
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  Paper,
  Stack,
  Step,
  StepLabel,
  Stepper,
  Tab,
  Tabs,
  TextField,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import {
  iconButton_SpacingSmall,
  button_Primary,
  button_Outlined,
  outermostContainer_Information,
} from "../../common/commonStyles";
import CloseIcon from "@mui/icons-material/Close";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import BasicDataCostCenter from "../CostCenterTabs/BasicDataCostCenter";
import { useLocation, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import ControlCostCenter from "../CostCenterTabs/ControlCostCenter";
import TemplatesCostCenter from "../CostCenterTabs/TemplatesCostCenter";
import AddressCostCenter from "../CostCenterTabs/AddressCostCenter";
import CommunicationCostCenter from "../CostCenterTabs/CommunicationCostCenter";
import HistoryCostCenter from "../CostCenterTabs/HistoryCostCenter";
import { doAjax } from "../../Common/fetchService";
import {
  destination_CostCenter,
  destination_DocumentManagement,
} from "../../../destinationVariables";
import { setDropDown } from "../../../app/dropDownDataSlice";
import ReusableDialog from "../../Common/ReusableDialog";
import ReusableSnackBar from "../../Common/ReusableSnackBar";
import {
  clearCostCenter,
  setCCErrorFields,
  setCostCenterAddressTab,
  setCostCenterBasicDataTab,
  setCostCenterCommunicationTab,
  setCostCenterControlTab,
  setCostCenterHistoryTab,
  setCostCenterTemplatesTab,
} from "../../../app/costCenterTabsSlice";
import ReusableAttachementAndComments from "../../Common/ReusableAttachmentAndComments/ReusableAttachementAndComments";
import { formValidator, idGenerator } from "../../../functions";
import lookup from "../../../data/lookup.json";
import LoadingComponent from "../../Common/LoadingComponent";
import moment from "moment";

const NewSingleCostCenter = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [dropDownData, setDropDownData] = useState({});
  const location = useLocation();
  const displayData = location.state;
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [messageDialogTitle, setMessageDialogTitle] = useState(false);
  const [successMsg, setsuccessMsg] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [messageDialogSeverity, setMessageDialogSeverity] = useState(false);
  const [messageDialogOK, setMessageDialogOK] = useState(true);
  const [messageDialogExtra, setMessageDialogExtra] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [blurLoading, setBlurLoading] = useState(false);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [validateFlag, setValidateFlag] = useState(false);
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [openCorrectionDialog, setOpenCorrectionDialog] = useState(false);
  const [submitForReviewDisabled, setSubmitForReviewDisabled] = useState(true);
  const [remarks, setRemarks] = useState("");
  const [ccNumber, setCcNumber] = useState("");
  const [requestId, setRequestId] = useState("");
  const [handleExtrabutton, setHandleExtrabutton] = useState(false);
  const [handleExtraText, setHandleExtraText] = useState("");
  const [formValidationErrorItems, setFormValidationErrorItems] = useState([]);
  const [openSnackbarValidation, setOpenSnackbarValidation] = useState(false);
  const [testrunStatus, setTestrunStatus] = useState(true);
  const [openCreateDialog, setOpenCreateDialog] = useState(false);
  const appSettings = useSelector((state) => state.appSettings);
  const singleCCPayload = useSelector(
    (state) => state.costCenter.singleCCPayload
  );
  const basicDataTabDetails = useSelector(
    (state) => state.costCenter.costCenterBasicData
  );
  const controlTabDetails = useSelector(
    (state) => state.costCenter.costCenterControl
  );
  const templatesTabDetails = useSelector(
    (state) => state.costCenter.costCenterTemplate
  );
  const addressTabDetails = useSelector(
    (state) => state.costCenter.costCenterAddress
  );
  const communicationTabDetails = useSelector(
    (state) => state.costCenter.costCenterCommunication
  );
  const historyTabDetails = useSelector(
    (state) => state.costCenter.costCenterHistory
  );
  const requiredFields = useSelector(
    (state) => state.costCenter.requiredFields
  );
  console.log("controlTabDetails", singleCCPayload.RecordQuantity);
  let userData = useSelector((state) => state.userManagement.userData);

  const [activeStep, setActiveStep] = useState(0);
  const payloadFields = useSelector(
    (state) => state.costCenter.singleCCPayload
  );
 
  const steps = [
    "BASIC DATA",
    "CONTROL",
    "TEMPLATES",
    "ADDRESS",
    "COMMUNICATION",
    // "HISTORY",
    "ATTACHMENTS & COMMENTS",
  ];

  const handleNext = () => {
    const isValidation = handleCheckValidationError();
    if (isValidation) {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
    } else {
      handleSnackBarOpenValidation();
    }
  };

  const handleBack = () => {
    setSubmitForReviewDisabled(true);
    const isValidation = handleCheckValidationError();
    if (isValidation) {
      setActiveStep((prevActiveStep) => prevActiveStep - 1);
    } else {
      handleSnackBarOpenValidation();
    }
  };
  const handleRemarksDialogClose = () => {
    setTestrunStatus(true);
    setOpenCorrectionDialog(false);
  };
  const getCostCenterBasicDetails = () => {
    let viewName = "Basic Data";
    const hSuccess = (data) => {
      dispatch(setCostCenterBasicDataTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getControlCostCenter = () => {
    let viewName = "Control";
    const hSuccess = (data) => {
      dispatch(setCostCenterControlTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getTemplatesCostCenter = () => {
    let viewName = "Templates";
    const hSuccess = (data) => {
      dispatch(setCostCenterTemplatesTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getAddressCostCenter = () => {
    let viewName = "Address";
    const hSuccess = (data) => {
      dispatch(setCostCenterAddressTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCommunicationCostCenter = () => {
    let viewName = "Communication";
    const hSuccess = (data) => {
      dispatch(setCostCenterCommunicationTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getHistoryCostCenter = () => {
    let viewName = "History";
    const hSuccess = (data) => {
      dispatch(setCostCenterHistoryTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getUserResponsible = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "UserResponsible", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getUserResponsible`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCostCenterCategory = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CostCenterCategory", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getCostCenterCategory`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCostCenter = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CostCenter", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getCostCenter`,
      "get",
      hSuccess,
      hError
    );
  };
  const getBusinessArea = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "BusinessArea", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getBusinessArea`,
      "get",
      hSuccess,
      hError
    );
  };
  const getFunctionalArea = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "FunctionalArea", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getFunctionalArea`,
      "get",
      hSuccess,
      hError
    );
  };

  const getCostingSheet = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CostingSheet", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getCostingSheet`,
      "get",
      hSuccess,
      hError
    );
  };

  const getCountryOrRegion = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CountryReg", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getCountry`,
      "get",
      hSuccess,
      hError
    );
  };
  const getJurisdiction = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "Jurisdiction", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getJurisdiction`,
      "get",
      hSuccess,
      hError
    );
  };
  const getLanguageKey = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "LanguageKey", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getLanguageKey`,
      "get",
      hSuccess,
      hError
    );
  };

  const handleRemarks = (e, value) => {
    //setRemarks(e.target.value);
    const newValue = e.target.value;
    if (newValue.length > 0 && newValue[0] === " ") {
      setRemarks(newValue.trimStart());
    } else {
      //let costCenterValue = e.target.value;
      let remarksUpperCase = newValue.toUpperCase();
      setRemarks(remarksUpperCase);
    }
  };
  var payload = {
    CostCenterHeaderID: "",
    ControllingArea: displayData?.controllingAreaData?.newControllingArea?.code
      ? displayData?.controllingAreaData?.newControllingArea?.code
      : "",
    Testrun: testrunStatus,
    Action: "I",
    Remarks: remarks ? remarks : "",
    ReqCreatedBy: userData?.user_id,
    ReqCreatedOn: "",
    RequestStatus: "",
    CreationId: "",
    EditId: "",
    DeleteId: "",
    MassCreationId: "",
    MassEditId: "",
    MassDeleteId: "",
    RequestType: "Create",
    MassRequestStatus: "",
    Toitem: [
      {
        CostCenterID: "",
        Costcenter:
          `${displayData?.companyCode?.newCompanyCode.code}${displayData?.costCenterName?.newCostCenterName}`
            ? `${displayData?.companyCode?.newCompanyCode.code}${displayData?.costCenterName?.newCostCenterName}`
            : "",
        ValidFrom: displayData?.validFromDate?.newValidFromDate
          ? "/Date(" +
            Date.parse(displayData?.validFromDate?.newValidFromDate) +
            ")/"
          : "",
        ValidTo: displayData?.validToDate?.newValidToDate
          ? "/Date(" +
            Date.parse(displayData?.validToDate?.newValidToDate) +
            ")/"
          : "",
        PersonInCharge: singleCCPayload?.PersonResponsible
          ? singleCCPayload?.PersonResponsible
          : "",
        CostcenterType: singleCCPayload?.CostCenterCategory?.code
          ? singleCCPayload?.CostCenterCategory?.code
          : "",
        CostctrHierGrp: singleCCPayload?.HierarchyArea?.code
          ? singleCCPayload?.HierarchyArea?.code
          : "",
        // CostctrHierGrp:"TUK1-PRODU" ,
        BusArea: singleCCPayload?.BusinessArea?.code
          ? singleCCPayload?.BusinessArea?.code
          : "",
        CompCode: singleCCPayload?.CompanyCode?.code
          ? singleCCPayload?.CompanyCode?.code
          : "",
        Currency: singleCCPayload?.Currency?.code
          ? singleCCPayload?.Currency?.code
          : "",
        ProfitCtr: singleCCPayload?.ProfitCenter?.code
          ? singleCCPayload?.ProfitCenter?.code
          : "",
        Name: singleCCPayload?.Name ? singleCCPayload?.Name : "",
        Descript: singleCCPayload?.Description
          ? singleCCPayload?.Description
          : "",
        PersonInChargeUser: singleCCPayload?.UserResponsible?.code
          ? singleCCPayload?.UserResponsible?.code
          : "",
        // PersonInChargeUser: singleCCPayload?.PersonResponsible?singleCCPayload?.PersonResponsible:"" ,
        RecordQuantity: singleCCPayload?.RecordQuantity === true ? "X" : "",
        LockIndActualPrimaryCosts:
          singleCCPayload?.ActualPrimaryCosts === true ? "X" : "",
        LockIndPlanPrimaryCosts:
          singleCCPayload?.PlanPrimaryCosts === true ? "X" : "",
        LockIndActSecondaryCosts:
          singleCCPayload?.ActsecondaryCosts === true ? "X" : "",
        LockIndPlanSecondaryCosts:
          singleCCPayload?.PlanSecondaryCosts === true ? "X" : "",
        LockIndActualRevenues:
          singleCCPayload?.ActualRevenue === true ? "X" : "",
        LockIndPlanRevenues: singleCCPayload?.PlanRevenue === true ? "X" : "",
        LockIndCommitmentUpdate:
          singleCCPayload?.CommitmentUpdate === true ? "X" : "",
        // RecordQuantity: singleCCPayload?.RecordQuantity?singleCCPayload?.RecordQuantity:"false" ,
        // LockIndActualPrimaryCosts: singleCCPayload?.ActualPrimaryCosts?singleCCPayload?.ActualPrimaryCosts:"false" ,
        // LockIndPlanPrimaryCosts: singleCCPayload?.PlanPrimaryCosts?singleCCPayload?.PlanPrimaryCosts:"false" ,
        // LockIndActSecondaryCosts: singleCCPayload?.ActsecondaryCosts?singleCCPayload?.ActsecondaryCosts:"false" ,
        // LockIndPlanSecondaryCosts: singleCCPayload?.PlanSecondaryCosts?singleCCPayload?.PlanSecondaryCosts:"false" ,
        // LockIndActualRevenues: singleCCPayload?.ActualRevenue?singleCCPayload?.ActualRevenue:"false" ,
        // LockIndPlanRevenues: singleCCPayload?.PlanRevenue?singleCCPayload?.PlanRevenue:"false" ,
        // LockIndCommitmentUpdate:singleCCPayload?.CommitmentUpdate?singleCCPayload?.CommitmentUpdate:"false" ,
        ConditionTableUsage: "",
        Application: "",
        CstgSheet: singleCCPayload?.CostingSheet?.code
          ? singleCCPayload?.CostingSheet?.code
          : "",
        ActyIndepTemplate: singleCCPayload?.ActyIndepFromPlngTemp?.code
          ? singleCCPayload?.ActyIndepFromPlngTemp?.code
          : "",
        ActyDepTemplate: singleCCPayload?.ActyDepFromPlngTemp?.code
          ? singleCCPayload?.ActyDepFromPlngTemp?.code
          : "",
        AddrTitle: singleCCPayload?.Title ? singleCCPayload?.Title : "",
        AddrName1: singleCCPayload?.Name1 ? singleCCPayload?.Name1 : "",
        AddrName2: singleCCPayload?.Name2 ? singleCCPayload?.Name2 : "",
        AddrName3: singleCCPayload?.Name3 ? singleCCPayload?.Name3 : "",
        AddrName4: singleCCPayload?.Name4 ? singleCCPayload?.Name4 : "",
        AddrStreet: singleCCPayload?.Street ? singleCCPayload?.Street : "",
        AddrCity: singleCCPayload?.Location ? singleCCPayload?.Location : "",
        AddrDistrict: singleCCPayload?.District
          ? singleCCPayload?.District
          : "",
        AddrCountry: singleCCPayload?.CountryReg?.code
          ? singleCCPayload?.CountryReg?.code
          : "",
        AddrCountryIso: "",
        AddrTaxjurcode: singleCCPayload?.Jurisdiction?.code
          ? singleCCPayload?.Jurisdiction?.code
          : "",
        AddrPoBox: singleCCPayload?.POBox ? singleCCPayload?.POBox : "",
        AddrPostlCode: singleCCPayload?.PostalCode
          ? singleCCPayload?.PostalCode
          : "",
        AddrPobxPcd: singleCCPayload?.POBoxPostCod
          ? singleCCPayload?.POBoxPostCod
          : "",
        AddrRegion: singleCCPayload?.Region?.code
          ? singleCCPayload?.Region?.code
          : "",
        TelcoLangu: "",
        TelcoLanguIso: singleCCPayload?.LanguageKey?.code
          ? singleCCPayload?.LanguageKey?.code
          : "",
        TelcoTelephone: singleCCPayload?.Telephone1
          ? singleCCPayload?.Telephone1
          : "",
        TelcoTelephone2: singleCCPayload?.Telephone2
          ? singleCCPayload?.Telephone2
          : "",
        TelcoTelebox: singleCCPayload?.TeleboxNumber
          ? singleCCPayload?.TeleboxNumber
          : "",
        TelcoTelex: singleCCPayload?.TelexNumber
          ? singleCCPayload?.TelexNumber
          : "",
        TelcoFaxNumber: singleCCPayload?.FaxNumber
          ? singleCCPayload?.FaxNumber
          : "",
        TelcoTeletex: singleCCPayload?.TeletexNumber
          ? singleCCPayload?.TeletexNumber
          : "",
        TelcoPrinter: singleCCPayload?.PrinterDestination
          ? singleCCPayload?.PrinterDestination
          : "",
        TelcoDataLine: singleCCPayload?.DataLine
          ? singleCCPayload?.DataLine
          : "",
        ActyDepTemplateAllocCc: singleCCPayload?.ActyDepAllocTemp?.code
          ? singleCCPayload?.ActyDepAllocTemp?.code
          : "",
        ActyDepTemplateSk: singleCCPayload?.TempActStatKeyFigure?.code
          ? singleCCPayload?.TempActStatKeyFigure?.code
          : "",
        ActyIndepTemplateAllocCc: singleCCPayload?.ActyIndepAllocTemp?.code
          ? singleCCPayload?.ActyIndepAllocTemp?.code
          : "",
        ActyIndepTemplateSk: singleCCPayload?.TempActStatKeyFigure?.code
          ? singleCCPayload?.TempActStatKeyFigure?.code
          : "",
        AvcActive: false,
        AvcProfile: "",
        BudgetCarryingCostCtr: "",
        CurrencyIso: "",
        Department: singleCCPayload?.Department
          ? singleCCPayload?.Department
          : "",
        FuncArea: singleCCPayload?.FunctionalArea?.code
          ? singleCCPayload?.FunctionalArea?.code
          : "",
        FuncAreaFixAssigned: "",
        FuncAreaLong: "",
        Fund: "",
        FundFixAssigned: "",
        GrantFixAssigned: "",
        GrantId: "",
        JvEquityTyp: "",
        JvJibcl: "",
        JvJibsa: "",
        JvOtype: "",
        JvRecInd: "",
        JvVenture: "",
        Logsystem: "",
      },
    ],
  };

  // Loader and lookup for independent apis start
  const [apiCount, setApiCount] = useState(0);
  const fetchDynamicApiData = (keyName, endPoint) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: keyName, data: data.body }));
      // setIsLoading(false);
      setApiCount((prev) => prev + 1);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/${endPoint}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getAllLookups = () => {
    lookup?.costCenter?.map((item) => {
      fetchDynamicApiData(item?.keyName, item?.endPoint);
    });
  };
  const loaderCount = () => {
    if (apiCount == lookup?.costCenter?.length) {
      setIsLoading(false);
    } else {
      setIsLoading(true);
    }
  };
  useEffect(() => {
    loaderCount();
  }, [apiCount]);

  // Loader and lookup for independent apis end

  useEffect(() => {
    getAllLookups();
    getCostCenterBasicDetails();
    getControlCostCenter();
    getTemplatesCostCenter();
    getAddressCostCenter();
    getCommunicationCostCenter();
    getUserResponsible();
    getCostCenterCategory();
    getCostCenter();
    getHistoryCostCenter();
    getHierarchyArea();
    // getCompanyCode();
    getProfitCenter();
    // getBusinessArea();
    // getFunctionalArea();
    // getCostingSheet();
    // getJurisdiction();
    // getCountryOrRegion();
    // getLanguageKey();
    dispatch(clearCostCenter());
  }, []);
  useEffect(() => {
    setCcNumber(idGenerator("CC"));
  }, []);
  const getHierarchyArea = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "HierarchyArea", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getHierarchyArea?controllingArea=${displayData?.controllingAreaData?.newControllingArea?.code}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getProfitCenter = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ProfitCenter", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getProfitCenterAsPerControllingArea?controllingArea=${displayData?.controllingAreaData?.newControllingArea?.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const handleSnackBarOpen = () => {
    setopenSnackbar(true);
  };
  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };
  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
    // navigate("/masterDataCockpit/costCenter");
  };
  const handleSnackBarClose = () => {
    if (validateFlag) {
      setopenSnackbar(false);
      setValidateFlag(false);
    } else {
      setopenSnackbar(false);
      navigate("/masterDataCockpit/costCenter");
    }
  };
  const handleMessageDialogNavigate = () => {
    // navigate("/masterDataCockpit/costCenter");
  };

  const handleSnackBarOpenValidation = () => {
    setOpenSnackbarValidation(true);
  };

  const handleCheckValidationError = () => {
    return formValidator(
      payloadFields,
      requiredFields,
      setFormValidationErrorItems
    );
  };

  useEffect(() => {
    dispatch(setCCErrorFields(formValidationErrorItems));
  }, [formValidationErrorItems]);

  const onSubmitForReviewButtonClick = () => {
    setIsLoading(true);
    // const formData = new FormData();
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Cost Center has been Submitted for review NCS${data.body}`
        );
        setHandleExtrabutton(false);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        handleRemarksDialogClose();
        // setIsLoading(false);

        // Make the second API call

        const secondApiPayload = {
          artifactId: ccNumber,
          createdBy: userData?.emailId,
          artifactType: "CostCenter",
          requestId: `NCS${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
        // }
      } else {
        setMessageDialogTitle("Create");
        setsuccessMsg(false);
        setMessageDialogMessage("Creation Failed");
        setHandleExtrabutton(false);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        handleCreateDialogClose();
        // setIsLoading(false);
      }
      handleClose();
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_CostCenter}/alter/costCenterSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const onSaveAsDraftButtonClick = () => {
    setMessageDialogSeverity(false);
    handleMessageDialogClickOpen();
    setMessageDialogTitle("Confirm");
    setMessageDialogMessage(`Do You Want to Save as Draft ?`);
    setHandleExtrabutton(true);
    setHandleExtraText("proceed");
  };
  console.log(handleExtrabutton, "setHandleExtrabutton");
  const handleProceedbutton = () => {
    setIsLoading(true);
    const hSuccess = (data) => {
      handleMessageDialogClose();
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Cost Center has been Saved with ID NCS${data.body}`
        );
        setHandleExtrabutton(false);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        const secondApiPayload = {
          artifactId: ccNumber,
          createdBy: userData?.emailId,
          artifactType: "CostCenter",
          requestId: `NCS${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
      } else {
        setMessageDialogTitle("Save");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Saving the Data");
        setHandleExtrabutton(false);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        //setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/alter/costCenterAsDraft`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const onValidateCostCenter = () => {
    setBlurLoading(true);
    // Define duplicateCheckPayload outside of the onValidateCostCenter function
    const duplicateCheckPayload = {
      coArea: displayData?.controllingAreaData?.newControllingArea?.code || "",
      name: singleCCPayload?.Name ? singleCCPayload?.Name.toUpperCase() : "",
    };

    // const isValidation = handleCheckValidationError();
    // if (isValidation) {
    const hSuccess = (data) => {
      // setIsLoading(false);
      if (data.statusCode === 201) {
        // Handle success
        setMessageDialogTitle("Create");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `All Data has been Validated. Cost Center can be Sent for Review`
        );
        setHandleExtrabutton(false);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
        setValidateFlag(true);

        // Now, make the duplicate check API call
        // Ensure that the conditions for making the duplicate check API call are met
        if (
          duplicateCheckPayload.coArea !== "" ||
          duplicateCheckPayload.name !== ""
        ) {
          doAjax(
            `/${destination_CostCenter}/alter/fetchCCDescriptionDupliChk`,
            "post",
            hDuplicateCheckSuccess,
            hDuplicateCheckError,
            duplicateCheckPayload
          );
        }
      } else {
        // Handle error
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          `${
            data?.body?.message[0] ? data?.body?.message[0] : data?.body?.value
          }`
        );
        setHandleExtrabutton(false);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setBlurLoading(false);
        // setIsLoading(false);
        
      }
    };

    const hDuplicateCheckSuccess = (data) => {
      // Handle success of duplicate check
      if (
        data.body.length === 0 ||
        !data.body.some(
          (item) => item.toUpperCase() === duplicateCheckPayload.name
        )
      ) {
        // No direct match, enable the "Submit for Review" button
        setBlurLoading(false);
        setSubmitForReviewDisabled(false);
      } else {
        // Handle direct match
        setBlurLoading(false);
        setMessageDialogTitle("Duplicate Check");
        setsuccessMsg(false);
        setMessageDialogMessage(
          `There is a direct match for the Cost Center name. Please change the name.`
        );
        setHandleExtrabutton(false);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setSubmitForReviewDisabled(true);
      }
    };

    const hDuplicateCheckError = (error) => {
      // Handle error of duplicate check
      console.log(error);
    };

    const hError = (error) => {
      console.log(error);
    };

    // Call the main API for validation
    doAjax(
      `/${destination_CostCenter}/alter/validateCostCenter`,
      "post",
      hSuccess,
      hError,
      payload
    );
    // } else {
    //   handleSnackBarOpenValidation();
    // }
  };

  const onCostCenterSubmitRemarks = () => {
    handleCreateDialogClose();
    onSubmitForReviewButtonClick();
  };

  const handleCreateDialog = () => {
    setTestrunStatus(false);
    setOpenCreateDialog(true);
  };


  const handleOpenCorrectionDialog = () => {
    setOpenCorrectionDialog(true);
  };

  const handleSnackBarCloseValidation = () => {
    setOpenSnackbarValidation(false);
  };

  const getStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <BasicDataCostCenter
            basicDataTabDetails={basicDataTabDetails}
            dropDownData={dropDownData}
          />
        );
      case 1:
        return (
          <ControlCostCenter
            controlTabDetails={controlTabDetails}
            dropDownData={dropDownData}
          />
        );
      case 2:
        return (
          <TemplatesCostCenter
            templatesTabDetails={templatesTabDetails}
            dropDownData={dropDownData}
          />
        );
      case 3:
        return (
          <AddressCostCenter
            addressTabDetails={addressTabDetails}
            dropDownData={dropDownData}
          />
        );
      case 4:
        return (
          <CommunicationCostCenter
            communicationTabDetails={communicationTabDetails}
            dropDownData={dropDownData}
          />
        );
      case 5:
        return (
          // <HistoryCostCenter
          //   historyTabDetails={historyTabDetails}
          //   dropDownData={dropDownData}
          // />
          <ReusableAttachementAndComments
            title="CostCenter"
            useMetaData={false}
            artifactId={ccNumber}
            artifactName="CostCenter"
          />
        );
      default:
        return "Unknown step";
    }
  };

  const handleWarningDialogClose = () => {
    setOpenMessageDialog(false);
  };

  const handleCreateDialogClose = () => {
    setTestrunStatus(true);
    setOpenCreateDialog(false);
  };

  // console.log("dontrollingarwa", displayData.validToDate?.newValidToDate);
  return (
    <>
      {isLoading === true ? (
        <LoadingComponent />
      ) : (
        <div>
          <ReusableDialog
            dialogState={openMessageDialog}
            openReusableDialog={handleMessageDialogClickOpen}
            closeReusableDialog={handleMessageDialogClose}
            dialogTitle={messageDialogTitle}
            dialogMessage={messageDialogMessage}
            handleDialogConfirm={handleMessageDialogClose}
            dialogOkText={"OK"}
            showExtraButton={handleExtrabutton}
            //handleExtraButton={handleMessageDialogNavigate}
            showCancelButton={true}
            dialogSeverity={messageDialogSeverity}
            handleDialogReject={handleWarningDialogClose}
            handleExtraText={handleExtraText}
            handleExtraButton={handleProceedbutton}
          />

          {successMsg && (
            <ReusableSnackBar
              openSnackBar={openSnackbar}
              alertMsg={messageDialogMessage}
              handleSnackBarClose={handleSnackBarClose}
            />
          )}

          {formValidationErrorItems.length != 0 && (
            <ReusableSnackBar
              openSnackBar={openSnackbarValidation}
              alertMsg={
                "Please fill the following Field: " +
                formValidationErrorItems.join(", ")
              }
              handleSnackBarClose={handleSnackBarCloseValidation}
            />
          )}

          <Dialog
            hideBackdrop={false}
            elevation={2}
            PaperProps={{
              sx: { boxShadow: "none" },
            }}
            open={openCorrectionDialog}
            onClose={handleRemarksDialogClose}
          >
            <DialogTitle
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF40",
                // borderBottom: "1px solid grey",
                display: "flex",
              }}
            >
              <Typography variant="h6">Remarks</Typography>

              <IconButton
                sx={{ width: "max-content" }}
                onClick={handleRemarksDialogClose}
                children={<CloseIcon />}
              />
            </DialogTitle>

            <DialogContent sx={{ padding: ".5rem 1rem" }}>
              <Stack>
                <Box sx={{ minWidth: 400 }}>
                  <FormControl sx={{ height: "auto" }} fullWidth>
                    <TextField
                      sx={{ backgroundColor: "#F5F5F5" }}
                      value={remarks}
                      onChange={handleRemarks}
                      multiline
                      placeholder={"Enter Remarks"}
                      inputProps={{maxLength: 254}}
                    ></TextField>
                  </FormControl>
                </Box>
              </Stack>
            </DialogContent>
            <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
              <Button
                sx={{ width: "max-content", textTransform: "capitalize" }}
                onClick={handleRemarksDialogClose}
              >
                Cancel
              </Button>
              <Button
                className="button_primary--normal"
                type="save"
                onClick={onCostCenterSubmitRemarks}
                variant="contained"
              >
                Submit
              </Button>
            </DialogActions>
          </Dialog>

          <Dialog
            hideBackdrop={false}
            elevation={2}
            PaperProps={{
              sx: { boxShadow: "none" },
            }}
            open={openCreateDialog}
            onClose={handleCreateDialogClose}
          >
            {/* <Grid
          container
          sx={{ display: "flex", justifyContent: "space-between" }}
        > */}
            {/* <Grid item> */}
            <DialogTitle
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF40",
                // borderBottom: "1px solid grey",
                display: "flex",
              }}
            >
              <Typography variant="h6">Remarks</Typography>

              <IconButton
                sx={{ width: "max-content" }}
                onClick={handleCreateDialogClose}
                children={<CloseIcon />}
              />
            </DialogTitle>
            {/* </Grid> */}
            {/* </Grid> */}
            <DialogContent sx={{ padding: ".5rem 1rem" }}>
              <Stack>
                <Box sx={{ minWidth: 400 }}>
                  <FormControl sx={{ height: "auto" }} fullWidth>
                    <TextField
                      sx={{ backgroundColor: "#F5F5F5" }}
                      // value={inputText}
                      value={remarks}
                      onChange={handleRemarks}
                      multiline
                      placeholder={"Enter Remarks"}
                      inputProps={{maxLength: 254}}
                    ></TextField>
                  </FormControl>
                </Box>
              </Stack>
              {/* <TextField
            autoFocus
            margin="dense"
            id="name"
            label="Enter Remarks for Correction"
            type="text"
            fullWidth
            variant="standard"
            onChange={handleRemarks}
          /> */}
            </DialogContent>
            <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
              <Button
                sx={{ width: "max-content", textTransform: "capitalize" }}
                onClick={handleCreateDialogClose}
              >
                Cancel
              </Button>
              <Button
                className="button_primary--normal"
                type="save"
                onClick={onCostCenterSubmitRemarks}
                variant="contained"
              >
                Submit
              </Button>
            </DialogActions>
          </Dialog>

          <Backdrop
            sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
            open={blurLoading}
            // onClick={handleClose}
          >
            <CircularProgress color="inherit" />
          </Backdrop>

          <Grid
            container
            style={{
              ...outermostContainer_Information,
              backgroundColor: "#FAFCFF",
            }}
          >
            <Grid sx={{ width: "inherit" }}>
              <Grid item md={7} style={{ padding: "16px", display: "flex" }}>
                <Grid item md={5} sx={{ display: "flex" }}>
                  <Grid>
                    <IconButton
                      // onClick={handleBacktoRO}
                      color="primary"
                      aria-label="upload picture"
                      component="label"
                      sx={iconButton_SpacingSmall}
                    >
                      <ArrowCircleLeftOutlinedIcon
                        style={{
                          height: "1em",
                          width: "1em",
                          color: "#000000",
                        }}
                        onClick={() => {
                          navigate("/masterDataCockpit/costCenter");
                          dispatch(clearPayload());
                          dispatch(clearOrgData());
                        }}
                      />
                    </IconButton>
                  </Grid>
                  <Grid>
                    <Typography variant="h3">
                      <strong>Create Cost Center</strong>
                    </Typography>
                    <Typography variant="body2" color="#777">
                      This view creates a new Cost Center
                    </Typography>
                  </Grid>
                </Grid>
              </Grid>
              {/* ... Your other content ... */}
              <Grid container style={{ padding: "0 1rem 0 1rem" }}>
                <Grid container sx={outermostContainer_Information}>
                  <Grid
                    container
                    display="flex"
                    flexDirection="row"
                    flexWrap="nowrap"
                  >
                    <Grid
                      item
                      md={10}
                      // width="85%"
                      sx={{ marginLeft: "40px", marginBottom: "20px" }}
                    >
                      <Grid item sx={{ paddingTop: "2px !important" }}>
                        <Stack flexDirection="row">
                          <div style={{ width: "12%" }}>
                            <Typography variant="body2" color="#777">
                              Cost Center
                            </Typography>
                          </div>
                          <Typography
                            variant="body2"
                            fontWeight="bold"
                            justifyContent="flex-start"
                          >
                            : {displayData?.companyCode?.newCompanyCode.code}
                            {displayData?.costCenterName?.newCostCenterName}
                          </Typography>
                        </Stack>
                      </Grid>

                      <Grid item sx={{ paddingTop: "2px !important" }}>
                        <Stack flexDirection="row">
                          <div style={{ width: "12%" }}>
                            <Typography variant="body2" color="#777">
                              Controlling Area
                            </Typography>
                          </div>
                          <Typography variant="body2" fontWeight="bold">
                            :{" "}
                            {
                              displayData?.controllingAreaData
                                ?.newControllingArea?.code
                            }
                          </Typography>
                        </Stack>
                      </Grid>
                    </Grid>
                    <Grid
                      // width="15%"
                      item
                      md={2}
                      sx={{ marginLeft: "40px", marginBottom: "20px" }}
                    >
                      <Grid item sx={{ paddingTop: "2px !important" }}>
                        <Stack flexDirection="row">
                          <div style={{ width: "50%" }}>
                            <Typography variant="body2" color="#777">
                              Valid From
                            </Typography>
                          </div>
                          <Typography variant="body2" fontWeight="bold">
                            :{" "}
                            {moment(
                              displayData?.validFromDate?.newValidFromDate
                            ).format(appSettings?.dateFormat)}
                          </Typography>
                        </Stack>
                      </Grid>
                      <Grid item sx={{ paddingTop: "2px !important" }}>
                        <Stack flexDirection="row">
                          <div style={{ width: "50%" }}>
                            <Typography variant="body2" color="#777">
                              Valid To
                            </Typography>
                          </div>
                          <Typography variant="body2" fontWeight="bold">
                            :{" "}
                            {moment(
                              displayData?.validToDate?.newValidToDate
                            ).format(appSettings?.dateFormat)}
                          </Typography>
                        </Stack>
                      </Grid>
                    </Grid>
                  </Grid>
                  <Grid container>
                    <Stepper
                      activeStep={activeStep}
                      sx={{
                        background: "#FFFFFF",
                        borderBottom: "1px solid #BDBDBD",
                        width: "100%",
                        height: "48px",
                      }}
                    >
                      {steps.map((label, index) => (
                        <Step key={label}>
                          <StepLabel sx={{ fontWeight: "700" }}>
                            {label}
                          </StepLabel>
                        </Step>
                      ))}
                    </Stepper>
                  </Grid>

                  <Grid container>{getStepContent(activeStep)}</Grid>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
          <Paper
            sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
            elevation={2}
          >
            <BottomNavigation
              className="container_BottomNav"
              showLabels
              sx={{ display: "flex", justifyContent: "flex-end" }}
            >
              <Button
                variant="contained"
                size="small"
                sx={{ ...button_Primary, mr: 1 }}
                onClick={onSaveAsDraftButtonClick}
                // disabled={activeStep === 0}
              >
                Save As Draft
              </Button>
              <Button
                variant="contained"
                size="small"
                sx={{ ...button_Primary, mr: 1 }}
                onClick={handleBack}
                disabled={activeStep === 0}
              >
                Back
              </Button>
              {activeStep === steps.length - 1 ? (
                <>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={onValidateCostCenter}
                  >
                    Validate
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleCreateDialog}
                    disabled={submitForReviewDisabled}
                  >
                    Submit For Review
                  </Button>
                </>
              ) : (
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary, mr: 1 }}
                  onClick={handleNext}
                >
                  Next
                </Button>
              )}
            </BottomNavigation>
          </Paper>
        </div>
      )}
    </>
  );
};

export default NewSingleCostCenter;
