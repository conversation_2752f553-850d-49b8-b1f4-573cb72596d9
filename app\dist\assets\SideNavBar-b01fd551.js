import{r as y,u as B,j as h,a as t,D as j,B as D,L as T,b as L,c as P,R as k,d as F,e as Y,T as A,A as J,f as K,g as Q,F as U,h as $}from"./index-75c1660a.js";import{L as I}from"./ListItemButton-f13df81b.js";function V({navState:e,handleDrawer:x,drawerData:m,entitesAndActivities:b,handleMoreDrawer:a,mode:c,onClickNavDrawerItem:d}){const n=r=>{d(r),x("close"),a("close")};return t(j,{sx:{"& .MuiDrawer-root":{position:"absolute"},"& .MuiPaper-root":{position:"absolute",background:c==="dark"?"#170E5E":"#ffffff"},zIndex:1e3,left:"90px",top:"64px"},anchor:"left",open:e,onClose:x,children:t(D,{sx:{minWidth:"250px"},role:"presentation",color:"#170E5E",children:t(T,{children:m==null?void 0:m.subModule.map(r=>{if(r.isAccessible&&r.isSideOption&&b[m.module.iwaName].includes(r.iwaName))return t(G,{onClickNavigateTo:n,option:r,handleDrawer:x,mode:c},r.iwaName)})})})})}function X({moreNavState:e,handleMoreDrawer:x,entitesAndActivities:m,handleDrawer:b,sideNavList:a,setDrawerData:c,updateSideNav:d,mode:n,onClickMoreNavDrawerItem:r}){var o,z;const w=L(),s=B(),l=i=>{r(i),w(i),b("close")};return t(j,{sx:{"& .MuiDrawer-root":{position:"absolute"},"& .MuiPaper-root":{position:"absolute",background:n==="dark"?"#170E5E":"#ffffff"},zIndex:1e3,left:"90px",top:"64px"},anchor:"left",open:e,onClose:()=>x("close"),children:t(D,{sx:{minWidth:"250px"},role:"presentation",color:"#170E5E",children:t(T,{children:(z=(o=a==null?void 0:a.data)==null?void 0:o.slice(a.configuration.moreOptions,a.data.length))==null?void 0:z.map(i=>{if(i.isAccessible&&i.isSideOption&&m[i.iwaName])return t(P,{disablePadding:!0,children:h(I,{className:"sideNavButton",sx:{borderBottom:"1px solid #efefef","&:hover":{backgroundColor:"#EAE9FF"}},style:s.pathname.includes(`/${i.routePath}`)?R:{},onClick:()=>{i.childItems.filter(M=>M.isAccessible&&M.isSideOption).length?(b("open"),c({module:i,subModule:i.childItems})):l(i.routePath),d(i),x("close")},children:[t(k,{isSelected:s.pathname.includes(`/${i.routePath}`),mode:n,iconName:i.icon,iconSize:"20px !important"}),t(F,{primaryTypographyProps:{fontSize:"12px !important"},className:"sideNavText",sx:{fontSize:"0.1rem",color:s.pathname.includes(`/${i.routePath}`)?"#3730c7":n==="dark"?"#ffffff":"#000000",display:"block",textAlign:"left",autofocus:"false",textDecoration:"none",paddingLeft:"1rem !important",width:"max-content"},children:i.displayName})]},i.id)},i.iwaName)})})})})}const R={backgroundColor:"#EAE9FF",borderRadius:"4px"},G=({option:e,popupState:x,onClickNavigateTo:m,handleDrawer:b,mode:a})=>{const c=B();L();const{t:d}=Y(),n=e.childItems.filter(s=>s.isAccessible&&s.isSideOption&&c.pathname.includes(`${s.routePath}`)).length!==0,[r,w]=y.useState(n);if(e.isAccessible&&e.isSideOption)return e.childItems.filter(s=>s.isAccessible&&s.isSideOption).length===0?t(P,{disablePadding:!0,sx:c.pathname.includes(`${e.routePath}`)?{...R}:{},children:t(I,{className:"sideNavButton",sx:{width:"100%",maxWidth:"240px",display:"flex",flexDirection:"column",alignItems:"flex-start",padding:"8px 16px !important",borderBottom:"1px solid #efefef","&:hover .descriptionText":{opacity:1,maxHeight:"100px",transform:"translateY(0px)"}},onClick:()=>{m(e.routePath)},children:h(D,{display:"flex",flexDirection:"column",children:[h(D,{display:"flex",alignItems:"center",children:[t(k,{isSelected:c.pathname.includes(`${e.routePath}`),iconName:e.icon,iconSize:"20px !important",mode:a}),t(F,{primaryTypographyProps:{fontSize:"12px !important"},className:"sideNavText",sx:{color:c.pathname.includes(`${e.routePath}`)?"#3730c7":a==="dark"?"#ffffff":"#000000",paddingLeft:"1rem !important",textAlign:"left",width:"80%"},children:d(e.displayName)})]}),t(A,{className:"descriptionText",fontSize:"10px",color:"text.secondary",sx:{paddingLeft:"2.2rem",opacity:0,maxHeight:0,transform:"translateY(-5px)",transition:"opacity 0.3s ease, transform 0.3s ease, max-height 0.3s ease",overflow:"hidden",whiteSpace:"normal",wordBreak:"break-word",width:"80%",maxWidth:"100%",overflowWrap:"break-word"},children:e!=null&&e.description?d(e.description):""})]})},e.id)}):t(P,{disablePadding:!0,children:h(Q,{elevation:0,expanded:r,onClick:()=>w(!r),sx:{border:0,"&::before":{backgroundColor:"none !important"},position:"relative !important",width:"100%",margin:"0px !important"},children:[h(J,{sx:{backgroundColor:n?"#EAE9FF !important":"",borderBottom:"1px solid #efefef",borderRadius:"4px","&:hover":{backgroundColor:"#EAE9FF"},margin:"0px 0px 0px 0px !important",paddingBottom:"4px !important",paddingTop:"4px !important"},expandIcon:t(k,{mode:a,isSelected:n,iconName:"ExpandMore"}),children:[t(k,{isSelected:n,iconName:e.icon,iconSize:"20px !important",mode:a}),t(F,{primaryTypographyProps:{fontSize:"12px !important"},className:"sideNavText",sx:{color:n?"#3730c7":"#000000",textAlign:"left",textDecoration:"none",paddingLeft:"1rem !important",margin:"0px !important"},primary:h(D,{children:[t(A,{fontSize:"12px",fontWeight:500,children:d(e.displayName)}),(e==null?void 0:e.description)&&t(A,{className:"descriptionText",fontSize:"10px",color:"text.secondary",sx:{opacity:0,maxHeight:0,transform:"translateY(-5px)",transition:"opacity 0.3s ease, transform 0.3s ease, max-height 0.3s ease",overflow:"hidden",whiteSpace:"normal",wordBreak:"break-word",maxWidth:"80%",".MuiAccordionSummary-root:hover &":{opacity:1,maxHeight:"100px",maxWidth:"80%",transform:"translateY(0)"}},children:d(e.description)})]})})]}),h(K,{sx:{padding:"0px !important"},children:[t(T,{disablePadding:!0,children:e==null?void 0:e.childItems.map(s=>t("div",{children:t(G,{onClickNavigateTo:m,option:s,mode:a,handleDrawer:b})},s))}),t(A,{className:"descriptionText",fontSize:"10px",color:"text.secondary",sx:{paddingLeft:"2.2rem",opacity:0,maxHeight:0,transform:"translateY(-5px)",transition:"opacity 0.3s ease, transform 0.3s ease, max-height 0.3s ease",overflow:"hidden",whiteSpace:"normal",wordBreak:"break-word",width:"80%",maxWidth:"100%",overflowWrap:"break-word"},children:e!=null&&e.description?d(e.description):""})]})]})})},Z=({option:e,handleDrawer:x,updateSideNav:m,setDrawerData:b,handleMoreDrawer:a,mode:c,onClickNavigateNavListItem:d})=>{const n=B(),{t:r}=Y(),w=o=>{d(o),x("close"),a("close")},l=(o=>o==="/"?n.pathname===o:n.pathname.includes(o))(e.routePath);if(e.isAccessible&&e.isSideOption)return e.childItems.filter(o=>o.isAccessible&&o.isSideOption).length!==0?t(I,{className:"sideNavButton",onClick:()=>{x(),a("close"),m(e),b({module:e,subModule:e.childItems})},sx:{padding:"0.1px 0 !important",marginBottom:"3px !important",borderRadius:l?"8px !important":"",backgroundColor:l?"#EAE9FF !important":""},children:t($,{title:e!=null&&e.description?r(e.description):"",arrow:!0,placement:"right",componentsProps:{tooltip:{sx:{background:"linear-gradient(135deg, #e3f2fd, #ffffff)",color:"#1a237e",fontSize:"13px",fontWeight:500,border:"1px solid #3730c7",borderRadius:"6px",padding:"6px 12px",boxShadow:"0px 4px 10px rgba(0, 0, 0, 0.1)"},arrow:{sx:{color:"#e3f2fd"}}}},children:h(P,{disablePadding:!0,sx:{color:"#000000",display:"block",justifyContent:"center !important",textAlign:"center",textDecoration:"none",padding:"0px"},className:"sideNavItem",children:[t(k,{isSelected:l,iconName:e.icon,mode:c}),t(A,{display:"block",className:"sideNavText",sx:{fontSize:"11px !important",justifyContent:"center !important",flexGrow:1,textAlign:"center !important",textDecoration:"none",color:l?"#3730c7":c==="dark"?"#ffffff":"#000000",fontWeight:l?"700 !important":"500 !important",borderRadius:l?"8px !important":"0px !important"},children:r(e.displayName)})]})})}):t(I,{className:"sideNavButton",selected:l,style:l?R:{},onClick:()=>{w(e.routePath)},sx:{padding:"0",justifyContent:"center !important",marginBottom:"3px !important"},children:t($,{title:e!=null&&e.description?r(e.description):"",arrow:!0,placement:"right",componentsProps:{tooltip:{sx:{background:"linear-gradient(135deg, #e3f2fd, #ffffff)",color:"#1a237e",fontSize:"13px",fontWeight:500,border:"1px solid #3730c7",borderRadius:"6px",padding:"6px 12px",boxShadow:"0px 4px 10px rgba(0, 0, 0, 0.1)"},arrow:{sx:{color:"#e3f2fd"}}}},children:h(P,{disablePadding:!0,sx:{color:"#000000",display:"block",justifyContent:"center !important",textAlign:"center",autofocus:"false",textDecoration:"none",padding:"0px"},className:"sideNavItem",children:[t(k,{isSelected:l,iconName:e.icon,mode:c}),t(A,{display:"block",className:"sideNavText",sx:{fontSize:"11px !important",justifyContent:"center !important",flexGrow:1,textAlign:"center !important",textDecoration:"none",color:l?"#3730c7":c==="dark"?"#ffffff":"#000000",fontWeight:l?"700":"500",borderRadius:l?"8px":"0px"},children:r(e.displayName)})]})})},e.id)};function te(e){const{onClickNavigateNavListItem:x,onClickNavDrawerItem:m,onClickMoreNavDrawerItem:b}=e,[a,c]=y.useState([]),d=B();y.useEffect(()=>{e!=null&&e.sideNavOptions&&c(e==null?void 0:e.sideNavOptions)},[e==null?void 0:e.sideNavOptions]);const n=g=>{var p,C,E;var S={};if(g?S=[g]:S=(p=a==null?void 0:a.data)==null?void 0:p.filter(u=>{if(u.routePath!=="/")return d.pathname.includes(u.routePath)}),S&&((C=S[0])==null?void 0:C.id)>a.configuration.moreOptions){var v=[],N=1;(E=a==null?void 0:a.data)==null||E.map(u=>{var H;var f={};u.id!==((H=S[0])==null?void 0:H.id)&&(u.id===a.configuration.moreOptions?(f={...S[0],id:N},N=N+1,v.push(f),f={...u,id:N},v.push(f)):(f={...u,id:N},v.push(f)),N=N+1)}),v.sort((u,f)=>u.id-f.id),c({...a,data:v})}};y.useEffect(()=>{n()},[d]);const r=({handleDrawer:g,setDrawerData:S,entitesAndActivities:v,handleMoreDrawer:N,mode:p})=>{var C,E,u;return t(U,{children:h(T,{className:"drawerList",children:[a&&((C=a==null?void 0:a.data)==null?void 0:C.slice(0,a.configuration.moreOptions).map(f=>{if(f.isAccessible&&f.isSideOption&&v[f.iwaName])return t(Z,{handleDrawer:g,option:f,updateSideNav:n,setDrawerData:S,handleMoreDrawer:N,mode:p,onClickNavigateNavListItem:x},f.iwaName)})),((E=a==null?void 0:a.data)==null?void 0:E.length)>((u=a==null?void 0:a.configuration)==null?void 0:u.moreOptions)&&t(I,{className:"sideNavButton",onClick:()=>{g("close"),N("open")},sx:{padding:"0 !important"},children:h(P,{disablePadding:!0,sx:{color:"#000000",display:"block",textAlign:"center",autofocus:"false",textDecoration:"none"},className:"sideNavItem",children:[t(k,{mode:p,iconName:"MoreHoriz"}),t(A,{className:"clsasaassd",display:"block",sx:{fontSize:11,justifyContent:"center",flexGrow:1,textAlign:"center !important",textDecoration:"none",color:window.location.pathname==="/supplier/more"||p==="dark"?"#ffffff":"#000000",fontWeight:window.location.pathname==="/supplier/more"?"700":"500"},children:"More"})]})})]})})},[w,s]=y.useState(!1),[l,o]=y.useState(!1),[z,i]=y.useState({module:{},subModule:[]}),[M,q]=y.useState({module:{},subModule:[]}),W=g=>{s(g==="open"?!0:g==="close"?!1:!w)},O=g=>{o(g==="open"?!0:g==="close"?!1:!w)};return h("div",{className:"sideNavbar",children:[t(j,{className:"drawerBase",variant:"permanent",sx:{"& .MuiPaper-root":{background:(e==null?void 0:e.mode)==="dark"?"#170E5E":"#ffffff"}},children:(e==null?void 0:e.entitesAndActivities)&&t(r,{setDrawerData:i,handleDrawer:W,entitesAndActivities:e==null?void 0:e.entitesAndActivities,setMoreDrawerData:q,handleMoreDrawer:O,mode:e==null?void 0:e.mode})}),(e==null?void 0:e.entitesAndActivities)&&t(V,{navState:w,handleDrawer:W,drawerData:z,entitesAndActivities:e==null?void 0:e.entitesAndActivities,handleMoreDrawer:O,mode:e==null?void 0:e.mode,onClickNavDrawerItem:m}),(e==null?void 0:e.entitesAndActivities)&&t(X,{moreNavState:l,handleMoreDrawer:O,entitesAndActivities:e==null?void 0:e.entitesAndActivities,handleDrawer:W,sideNavList:a,setDrawerData:i,updateSideNav:n,mode:e==null?void 0:e.mode,onClickMoreNavDrawerItem:b})]})}export{te as default};
