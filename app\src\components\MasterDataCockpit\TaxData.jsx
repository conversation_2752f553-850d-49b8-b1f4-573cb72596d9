import React, { useEffect, useRef } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useSelector, useDispatch } from "react-redux";
import { updateMaterialData } from "../../app/payloadslice";
import {
  DEFAULT_VALUES,
  LOADING_MESSAGE,
  MATERIAL_VIEWS,
} from "@constant/enum";
import { colors } from "@constant/colors";
import { useChangeLogUpdate } from "@hooks/useChangeLogUpdate";
import { useLocation } from "react-router-dom";
import SingleSelectDropDown from "@components/Common/ui/dropdown/SingleSelectDropdown";

const DEFAULT_CODE = "1"; 

const TaxDataTable = ({ materialID, selectedMaterialNumber }) => {
  const dispatch = useDispatch();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const requestId = queryParams.get("RequestId");
  const initialPayload = useSelector((state) => state.payload.payloadData);

  const taxData = useSelector(
    (state) =>
      state.payload[materialID]?.payloadData?.TaxData?.TaxData?.TaxDataSet ||
      []
  );
  const { updateChangeLog } = useChangeLogUpdate();

  const defaultValueSet = useRef(false);

  useEffect(() => {
    if (!taxData.length || defaultValueSet.current) return;

    let hasUpdates = false;

    const updatedTaxData = taxData.map((row) => {
      if (!row.SelectedTaxClass) {
        const defaultOption = row.options?.find(
          (opt) => opt.code === DEFAULT_CODE
        );
        if (defaultOption) {
          hasUpdates = true;
          return {
            ...row,
            SelectedTaxClass: {
              TaxClass: defaultOption.code,
              TaxClassDesc: defaultOption.desc,
            },
          };
        }
      }
      return row;
    });

    if (hasUpdates) {
      dispatch(
        updateMaterialData({
          materialID,
          viewID: "TaxData",
          itemID: "TaxData",
          keyName: "TaxDataSet",
          data: updatedTaxData,
        })
      );
      defaultValueSet.current = true;
    }
  }, [taxData, dispatch, materialID]);

  const handleTaxClassChange = (index, newValue, country, taxType) => {
    const updatedData = [...taxData];
    updatedData[index] = {
      ...updatedData[index],
      SelectedTaxClass: newValue,
    };

    dispatch(
      updateMaterialData({
        materialID,
        viewID: "TaxData",
        itemID: "TaxData",
        keyName: "TaxDataSet",
        data: updatedData,
      })
    );

    if (requestId) {
      updateChangeLog({
        materialID: selectedMaterialNumber,
        viewName: MATERIAL_VIEWS.TAX_DATA,
        plantData: `${country}-${taxType}`,
        fieldName: "Tax Class",
        jsonName: "SelectedTaxClass",
        currentValue: newValue.TaxClass,
        requestId: initialPayload?.RequestId,
        childRequestId:requestId
      });
    }
  };

  if (taxData.length === 0) {
    return (
      <Typography sx={{ textAlign: "center", marginTop: "10px" }}>
        {LOADING_MESSAGE?.TAXDATA_LOADING}
      </Typography>
    );
  }

  return (
    <Accordion
      sx={{
        marginBottom: "20px",
        boxShadow: 3,
        borderRadius: "10px",
        borderColor: colors?.primary.white,
      }}
      key="Tax_Classification"
    >
      <AccordionSummary
        expandIcon={<ExpandMoreIcon />}
        sx={{
          backgroundColor: colors.primary.whiteSmoke,
          borderRadius: "10px",
          padding: "8px 16px",
          "&:hover": { backgroundColor: colors.hover.hoverbg },
        }}
      >
        <Typography variant="h6" sx={{ fontWeight: "bold" }}>
          Tax Classification
        </Typography>
      </AccordionSummary>
      <AccordionDetails>
        <TableContainer component={Paper}>
          <Typography
            variant="h6"
            sx={{ p: 1, fontWeight: "bold", textAlign: "center" }}
          >
            Tax Data
          </Typography>
          <Table>
            <TableHead>
              <TableRow sx={{ backgroundColor: "#f5f5f5" }}>
                <TableCell sx={{ fontWeight: "bold" }}>Country</TableCell>
                <TableCell sx={{ fontWeight: "bold" }}>Tax Type</TableCell>
                <TableCell sx={{ fontWeight: "bold" }}>Tax Class</TableCell>
                <TableCell sx={{ fontWeight: "bold" }}>Description</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {taxData.map(
                ({ Country, TaxType, options = [], SelectedTaxClass }, index) => {
                  const selectedOption = SelectedTaxClass
                    ? {
                        code: SelectedTaxClass.TaxClass,
                        desc: SelectedTaxClass.TaxClassDesc,
                      }
                    : { code: "", desc: "" };

                  return (
                    <TableRow key={`${Country}-${TaxType}`}>
                      <TableCell sx={{ fontWeight: "bold" }}>{Country}</TableCell>
                      <TableCell sx={{ fontWeight: "bold" }}>{TaxType}</TableCell>
                      <TableCell>
                        <SingleSelectDropDown
                          options={options}
                          value={selectedOption}
                          onChange={(newValue) => {
                            const mappedValue = newValue
                              ? {
                                  TaxClass: newValue.code,
                                  TaxClassDesc: newValue.desc,
                                }
                              : null;
                            handleTaxClassChange(index, mappedValue, Country, TaxType);
                          }}
                          placeholder="SELECT TAX CLASS"
                          minWidth={200}
                        />
                      </TableCell>
                      <TableCell>{selectedOption.desc}</TableCell>
                    </TableRow>
                  );
                }
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </AccordionDetails>
    </Accordion>
  );
};

export default TaxDataTable;
