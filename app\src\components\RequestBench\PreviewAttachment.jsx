import { doAjax } from '@components/Common/fetchService';
import { appendPrefixByJavaKey } from '@helper/helper';
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';
import { Box, Typography, IconButton, Grid } from '@mui/material';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import { container_Padding } from '@components/Common/commonStyles';

const destination_DocumentManagement = 'cw-mdg-documentmanagement-dest';

const PreviewAttachment = () => {
  const requestIdHeader = useSelector(
    (state) => state.request.requestHeader?.requestId
  );
  const requestType = useSelector(
    (state) => state.request.requestHeader.requestType
  );
  const location = useLocation();
  const urlSearchParams = new URLSearchParams(location.search.split('?')[1]);
  const requestId = urlSearchParams.get('RequestId');
  const requestIdAttach = requestIdHeader
    ? appendPrefixByJavaKey(requestType, requestIdHeader)
    : requestId;

  const [docurl, setDocurl] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    const hSuccess = (data) => {
      const attachmentRows = data.documentDetailDtoList.map((doc) => ({
        id: doc.documentId,
        docType: doc.fileType,
        docName: doc.fileName,
        attachmentType: doc.attachmentType,
        documentViewUrl: doc.documentViewUrl,
      }));
      setDocurl(attachmentRows);
    };

    doAjax(
      `/${destination_DocumentManagement}/documentManagement/getDocByRequestId/${requestIdAttach}`,
      'get',
      hSuccess,
      () => {}
    );
  }, [requestIdAttach]);

  const handlePrev = () => {
    setCurrentIndex((prevIndex) => (prevIndex > 0 ? prevIndex - 1 : docurl.length - 1));
  };

  const handleNext = () => {
    setCurrentIndex((prevIndex) => (prevIndex < docurl.length - 1 ? prevIndex + 1 : 0));
  };

  const currentDoc = docurl[currentIndex];
  const supportedFormats = ['pdf', 'png', 'jpg', 'jpeg', 'xlsx'];
  const getExtension = (docName) => docName.split('.').pop().toLowerCase();
  const getPreviewUrl = (documentViewUrl) => {
    if (!documentViewUrl) return null;
    if (typeof documentViewUrl === 'string') {
        return `https://docs.google.com/viewer?url=${encodeURIComponent(documentViewUrl)}&embedded=true`;

    }
    return URL.createObjectURL(documentViewUrl);
  };

  const renderPreview = () => {
    if (!currentDoc) {
      return (
        <Box sx={{ height: '600px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <Typography variant="body1" color="textSecondary">
            No documents available
          </Typography>
        </Box>
      );
    }

    const { id, docName, documentViewUrl } = currentDoc;
    const extension = getExtension(docName);
    const isImage = ['png', 'jpg', 'jpeg'].includes(extension);
    const isSupported = supportedFormats.includes(extension);
    const previewUrl = getPreviewUrl(documentViewUrl);

    if (!isSupported) {
      return (
        <Box sx={{ height: '600px', display: 'flex', alignItems: 'center', justifyContent: 'center', flexDirection: 'column' }}>
          <Typography variant="body1" sx={{ fontWeight: 500, mb: 1 }}>
            {docName}
          </Typography>
          <Typography variant="body2" color="textSecondary">
            Preview not available for this format
          </Typography>
        </Box>
      );
    }

    if (isImage) {
      return (
        <Box sx={{ height: '600px', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <Typography variant="body1" sx={{ fontWeight: 500, mb: 1 }}>
            {docName}
          </Typography>
          <img
            src={typeof documentViewUrl === 'string' ? documentViewUrl : URL.createObjectURL(documentViewUrl)}
            style={{
              maxWidth: '100%',
              maxHeight: '550px',
              display: 'block',
              borderRadius: '4px',
              objectFit: 'contain',
            }}
            alt="File Preview"
          />
        </Box>
      );
    }

    return (
      <Box sx={{ height: '600px', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
        <Typography variant="body1" sx={{ fontWeight: 500, mb: 1 }}>
          {docName}
        </Typography>
        <iframe
          src={previewUrl}
          title={docName}
          style={{
            width: '100%',
            height: '550px',
            border: 'none',
           filter: "invert(0)",
            borderRadius: '4px',
          }}
        />
      </Box>
    );
  };

  return (
    docurl.length>0 &&
    <Grid
      item
      md={12}
      sx={{
        backgroundColor: 'white',
        borderRadius: '8px',
        border: '1px solid #E0E0E0',
        boxShadow: '0px 1px 4px rgba(0, 0, 0, 0.1)',
        ...container_Padding,
        pt: '10px',
      }}
    >
      <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
        Attached Documents
      </Typography>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        {docurl.length > 1 && <IconButton onClick={handlePrev}><ArrowBackIosIcon /></IconButton>}
        <Box sx={{ flexGrow: 1, textAlign: 'center', overflow: 'hidden' }}>{renderPreview()}</Box>
        {docurl.length > 1 && <IconButton onClick={handleNext}><ArrowForwardIosIcon /></IconButton>}
      </Box>
      {docurl.length > 1 && (
        <Typography variant="caption" sx={{ mt: 1, display: 'block', textAlign: 'center' }}>
          {currentIndex + 1} of {docurl.length}
        </Typography>
      )}
    </Grid>
  );
};

export default PreviewAttachment;
