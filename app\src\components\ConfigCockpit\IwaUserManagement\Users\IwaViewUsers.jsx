import { ViewUser } from "@cw/viewuser";
import { Stack } from "@mui/material";
import { useNavigate, useParams } from "react-router-dom";
import { useSnackbar } from "../../../../hooks/useSnackbar";
import { APP_END_POINTS } from "../../../../constant/appEndPoints";
import { DOC_SNACKBAR_MESSAGES } from "../../../../constant/enum";

const IwaViewUsers = () => {
  const { userId } = useParams();
  const navigate = useNavigate();
  const { showSnackbar } = useSnackbar();

  const viewUserNavigate = (action, userId, response) => {
    if (action === "edit") {
      if (userId) {
        navigate(APP_END_POINTS.IWA_USER_MANAGEMENT.EDIT_USER);
      } else {
        showSnackbar(DOC_SNACKBAR_MESSAGES?.MISSING_USER, "warning");
      }
    } else if (action === "home") {
      navigate(APP_END_POINTS.IWA_USER_MANAGEMENT.USERS_SUMMARY);
    }
    if (response) {
      showSnackbar(response.message, "info");
    }
  };

  return (
    <Stack>
      <ViewUser userId={userId} viewUserNavigate={viewUserNavigate} />
    </Stack>
  );
};

export default IwaViewUsers;
