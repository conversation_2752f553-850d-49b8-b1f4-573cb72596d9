import{r as L,fU as P,fV as tt,i as X,k as rt,cB as C,bs as at,T as l,E as ot,aw as it,B as M,bF as nt,u as st,b as lt,j as n,a as i,G as g,x as $,M as se,ar as ct,h as dt,I as le,K as pt,N as ce,e8 as ut}from"./index-75c1660a.js";import{d as yt}from"./ArrowCircleLeftOutlined-f7b52d40.js";import{a as de,d as pe}from"./SlideshowOutlined-ac670857.js";import{l as ht}from"./index-d550e3b0.js";import{c as be,r as ft}from"./memoize-one.esm-164bff9c.js";import{r as ge}from"./index-19916fa2.js";import{M as _t}from"./UtilDoc-7fb813ce.js";import{V as mt}from"./icons-ef587d6e.js";import{D as ue}from"./DatePicker-6e8720de.js";var Pt=Object.create,H=Object.defineProperty,bt=Object.getOwnPropertyDescriptor,gt=Object.getOwnPropertyNames,vt=Object.getPrototypeOf,wt=Object.prototype.hasOwnProperty,Ot=(e,t)=>{for(var r in t)H(e,r,{get:t[r],enumerable:!0})},ve=(e,t,r,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of gt(t))!wt.call(e,a)&&a!==r&&H(e,a,{get:()=>t[a],enumerable:!(o=bt(t,a))||o.enumerable});return e},te=(e,t,r)=>(r=e!=null?Pt(vt(e)):{},ve(t||!e||!e.__esModule?H(r,"default",{value:e,enumerable:!0}):r,e)),Tt=e=>ve(H({},"__esModule",{value:!0}),e),we={};Ot(we,{callPlayer:()=>Bt,getConfig:()=>Nt,getSDK:()=>Ut,isBlobUrl:()=>Ht,isMediaStream:()=>jt,lazy:()=>Ct,omit:()=>Vt,parseEndTime:()=>Lt,parseStartTime:()=>Mt,queryString:()=>kt,randomString:()=>$t,supportsWebKitPresentationMode:()=>zt});var z=Tt(we),Dt=te(L),St=te(ht),Et=te(be);const Ct=e=>Dt.default.lazy(async()=>{const t=await e();return typeof t.default=="function"?t:t.default}),At=/[?&#](?:start|t)=([0-9hms]+)/,Rt=/[?&#]end=([0-9hms]+)/,Z=/(\d+)(h|m|s)/g,xt=/^\d+$/;function Oe(e,t){if(e instanceof Array)return;const r=e.match(t);if(r){const o=r[1];if(o.match(Z))return It(o);if(xt.test(o))return parseInt(o)}}function It(e){let t=0,r=Z.exec(e);for(;r!==null;){const[,o,a]=r;a==="h"&&(t+=parseInt(o,10)*60*60),a==="m"&&(t+=parseInt(o,10)*60),a==="s"&&(t+=parseInt(o,10)),r=Z.exec(e)}return t}function Mt(e){return Oe(e,At)}function Lt(e){return Oe(e,Rt)}function $t(){return Math.random().toString(36).substr(2,5)}function kt(e){return Object.keys(e).map(t=>`${t}=${e[t]}`).join("&")}function Y(e){return window[e]?window[e]:window.exports&&window.exports[e]?window.exports[e]:window.module&&window.module.exports&&window.module.exports[e]?window.module.exports[e]:null}const R={},Ut=function(t,r,o=null,a=()=>!0,s=St.default){const w=Y(r);return w&&a(w)?Promise.resolve(w):new Promise((O,T)=>{if(R[t]){R[t].push({resolve:O,reject:T});return}R[t]=[{resolve:O,reject:T}];const S=b=>{R[t].forEach(E=>E.resolve(b))};if(o){const b=window[o];window[o]=function(){b&&b(),S(Y(r))}}s(t,b=>{b?(R[t].forEach(E=>E.reject(b)),R[t]=null):o||S(Y(r))})})};function Nt(e,t){return(0,Et.default)(t.config,e.config)}function Vt(e,...t){const r=[].concat(...t),o={},a=Object.keys(e);for(const s of a)r.indexOf(s)===-1&&(o[s]=e[s]);return o}function Bt(e,...t){if(!this.player||!this.player[e]){let r=`ReactPlayer: ${this.constructor.displayName} player could not call %c${e}%c – `;return this.player?this.player[e]||(r+="The method was not available"):r+="The player was not available",console.warn(r,"font-weight: bold",""),null}return this.player[e](...t)}function jt(e){return typeof window<"u"&&typeof window.MediaStream<"u"&&e instanceof window.MediaStream}function Ht(e){return/^blob:/.test(e)}function zt(e=document.createElement("video")){const t=/iPhone|iPod/.test(navigator.userAgent)===!1;return e.webkitSupportsPresentationMode&&typeof e.webkitSetPresentationMode=="function"&&t}var re=Object.defineProperty,Ft=Object.getOwnPropertyDescriptor,Wt=Object.getOwnPropertyNames,Kt=Object.prototype.hasOwnProperty,Xt=(e,t)=>{for(var r in t)re(e,r,{get:t[r],enumerable:!0})},Yt=(e,t,r,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of Wt(t))!Kt.call(e,a)&&a!==r&&re(e,a,{get:()=>t[a],enumerable:!(o=Ft(t,a))||o.enumerable});return e},qt=e=>Yt(re({},"__esModule",{value:!0}),e),Te={};Xt(Te,{AUDIO_EXTENSIONS:()=>ae,DASH_EXTENSIONS:()=>Ne,FLV_EXTENSIONS:()=>Ve,HLS_EXTENSIONS:()=>ie,MATCH_URL_DAILYMOTION:()=>Le,MATCH_URL_FACEBOOK:()=>Ce,MATCH_URL_FACEBOOK_WATCH:()=>Ae,MATCH_URL_KALTURA:()=>Ue,MATCH_URL_MIXCLOUD:()=>$e,MATCH_URL_MUX:()=>Ee,MATCH_URL_SOUNDCLOUD:()=>De,MATCH_URL_STREAMABLE:()=>Re,MATCH_URL_TWITCH_CHANNEL:()=>Me,MATCH_URL_TWITCH_VIDEO:()=>Ie,MATCH_URL_VIDYARD:()=>ke,MATCH_URL_VIMEO:()=>Se,MATCH_URL_WISTIA:()=>xe,MATCH_URL_YOUTUBE:()=>G,VIDEO_EXTENSIONS:()=>oe,canPlay:()=>Zt});var Jt=qt(Te),ye=z;const G=/(?:youtu\.be\/|youtube(?:-nocookie|education)?\.com\/(?:embed\/|v\/|watch\/|watch\?v=|watch\?.+&v=|shorts\/|live\/))((\w|-){11})|youtube\.com\/playlist\?list=|youtube\.com\/user\//,De=/(?:soundcloud\.com|snd\.sc)\/[^.]+$/,Se=/vimeo\.com\/(?!progressive_redirect).+/,Ee=/stream\.mux\.com\/(?!\w+\.m3u8)(\w+)/,Ce=/^https?:\/\/(www\.)?facebook\.com.*\/(video(s)?|watch|story)(\.php?|\/).+$/,Ae=/^https?:\/\/fb\.watch\/.+$/,Re=/streamable\.com\/([a-z0-9]+)$/,xe=/(?:wistia\.(?:com|net)|wi\.st)\/(?:medias|embed)\/(?:iframe\/)?([^?]+)/,Ie=/(?:www\.|go\.)?twitch\.tv\/videos\/(\d+)($|\?)/,Me=/(?:www\.|go\.)?twitch\.tv\/([a-zA-Z0-9_]+)($|\?)/,Le=/^(?:(?:https?):)?(?:\/\/)?(?:www\.)?(?:(?:dailymotion\.com(?:\/embed)?\/video)|dai\.ly)\/([a-zA-Z0-9]+)(?:_[\w_-]+)?(?:[\w.#_-]+)?/,$e=/mixcloud\.com\/([^/]+\/[^/]+)/,ke=/vidyard.com\/(?:watch\/)?([a-zA-Z0-9-_]+)/,Ue=/^https?:\/\/[a-zA-Z]+\.kaltura.(com|org)\/p\/([0-9]+)\/sp\/([0-9]+)00\/embedIframeJs\/uiconf_id\/([0-9]+)\/partner_id\/([0-9]+)(.*)entry_id.([a-zA-Z0-9-_].*)$/,ae=/\.(m4a|m4b|mp4a|mpga|mp2|mp2a|mp3|m2a|m3a|wav|weba|aac|oga|spx)($|\?)/i,oe=/\.(mp4|og[gv]|webm|mov|m4v)(#t=[,\d+]+)?($|\?)/i,ie=/\.(m3u8)($|\?)/i,Ne=/\.(mpd)($|\?)/i,Ve=/\.(flv)($|\?)/i,Q=e=>{if(e instanceof Array){for(const t of e)if(typeof t=="string"&&Q(t)||Q(t.src))return!0;return!1}return(0,ye.isMediaStream)(e)||(0,ye.isBlobUrl)(e)?!0:ae.test(e)||oe.test(e)||ie.test(e)||Ne.test(e)||Ve.test(e)},Zt={youtube:e=>e instanceof Array?e.every(t=>G.test(t)):G.test(e),soundcloud:e=>De.test(e)&&!ae.test(e),vimeo:e=>Se.test(e)&&!oe.test(e)&&!ie.test(e),mux:e=>Ee.test(e),facebook:e=>Ce.test(e)||Ae.test(e),streamable:e=>Re.test(e),wistia:e=>xe.test(e),twitch:e=>Ie.test(e)||Me.test(e),dailymotion:e=>Le.test(e),mixcloud:e=>$e.test(e),vidyard:e=>ke.test(e),kaltura:e=>Ue.test(e),file:Q};var ne=Object.defineProperty,Gt=Object.getOwnPropertyDescriptor,Qt=Object.getOwnPropertyNames,er=Object.prototype.hasOwnProperty,tr=(e,t)=>{for(var r in t)ne(e,r,{get:t[r],enumerable:!0})},rr=(e,t,r,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of Qt(t))!er.call(e,a)&&a!==r&&ne(e,a,{get:()=>t[a],enumerable:!(o=Gt(t,a))||o.enumerable});return e},ar=e=>rr(ne({},"__esModule",{value:!0}),e),Be={};tr(Be,{default:()=>ir});var or=ar(Be),m=z,f=Jt,ir=[{key:"youtube",name:"YouTube",canPlay:f.canPlay.youtube,lazyPlayer:(0,m.lazy)(()=>P(()=>import("./YouTube-427a4303.js").then(e=>e.Y),["assets/YouTube-427a4303.js","assets/index-75c1660a.js","assets/index-a517bf54.css"]))},{key:"soundcloud",name:"SoundCloud",canPlay:f.canPlay.soundcloud,lazyPlayer:(0,m.lazy)(()=>P(()=>import("./SoundCloud-1de51cfa.js").then(e=>e.S),["assets/SoundCloud-1de51cfa.js","assets/index-75c1660a.js","assets/index-a517bf54.css"]))},{key:"vimeo",name:"Vimeo",canPlay:f.canPlay.vimeo,lazyPlayer:(0,m.lazy)(()=>P(()=>import("./Vimeo-879587c0.js").then(e=>e.V),["assets/Vimeo-879587c0.js","assets/index-75c1660a.js","assets/index-a517bf54.css"]))},{key:"mux",name:"Mux",canPlay:f.canPlay.mux,lazyPlayer:(0,m.lazy)(()=>P(()=>import("./Mux-2fef4ceb.js").then(e=>e.M),["assets/Mux-2fef4ceb.js","assets/index-75c1660a.js","assets/index-a517bf54.css"]))},{key:"facebook",name:"Facebook",canPlay:f.canPlay.facebook,lazyPlayer:(0,m.lazy)(()=>P(()=>import("./Facebook-2cb29796.js").then(e=>e.F),["assets/Facebook-2cb29796.js","assets/index-75c1660a.js","assets/index-a517bf54.css"]))},{key:"streamable",name:"Streamable",canPlay:f.canPlay.streamable,lazyPlayer:(0,m.lazy)(()=>P(()=>import("./Streamable-9ec8f5f6.js").then(e=>e.S),["assets/Streamable-9ec8f5f6.js","assets/index-75c1660a.js","assets/index-a517bf54.css"]))},{key:"wistia",name:"Wistia",canPlay:f.canPlay.wistia,lazyPlayer:(0,m.lazy)(()=>P(()=>import("./Wistia-f8cfb6d6.js").then(e=>e.W),["assets/Wistia-f8cfb6d6.js","assets/index-75c1660a.js","assets/index-a517bf54.css"]))},{key:"twitch",name:"Twitch",canPlay:f.canPlay.twitch,lazyPlayer:(0,m.lazy)(()=>P(()=>import("./Twitch-b613dc26.js").then(e=>e.T),["assets/Twitch-b613dc26.js","assets/index-75c1660a.js","assets/index-a517bf54.css"]))},{key:"dailymotion",name:"DailyMotion",canPlay:f.canPlay.dailymotion,lazyPlayer:(0,m.lazy)(()=>P(()=>import("./DailyMotion-70e3014d.js").then(e=>e.D),["assets/DailyMotion-70e3014d.js","assets/index-75c1660a.js","assets/index-a517bf54.css"]))},{key:"mixcloud",name:"Mixcloud",canPlay:f.canPlay.mixcloud,lazyPlayer:(0,m.lazy)(()=>P(()=>import("./Mixcloud-a6638f83.js").then(e=>e.M),["assets/Mixcloud-a6638f83.js","assets/index-75c1660a.js","assets/index-a517bf54.css"]))},{key:"vidyard",name:"Vidyard",canPlay:f.canPlay.vidyard,lazyPlayer:(0,m.lazy)(()=>P(()=>import("./Vidyard-64ecc64a.js").then(e=>e.V),["assets/Vidyard-64ecc64a.js","assets/index-75c1660a.js","assets/index-a517bf54.css"]))},{key:"kaltura",name:"Kaltura",canPlay:f.canPlay.kaltura,lazyPlayer:(0,m.lazy)(()=>P(()=>import("./Kaltura-67bde4e6.js").then(e=>e.K),["assets/Kaltura-67bde4e6.js","assets/index-75c1660a.js","assets/index-a517bf54.css"]))},{key:"file",name:"FilePlayer",canPlay:f.canPlay.file,canEnablePIP:e=>f.canPlay.file(e)&&(document.pictureInPictureEnabled||(0,m.supportsWebKitPresentationMode)())&&!f.AUDIO_EXTENSIONS.test(e),lazyPlayer:(0,m.lazy)(()=>P(()=>import("./FilePlayer-ced4f1d3.js").then(e=>e.F),["assets/FilePlayer-ced4f1d3.js","assets/index-75c1660a.js","assets/index-a517bf54.css"]))}],nr=Object.create,F=Object.defineProperty,sr=Object.getOwnPropertyDescriptor,lr=Object.getOwnPropertyNames,cr=Object.getPrototypeOf,dr=Object.prototype.hasOwnProperty,pr=(e,t)=>{for(var r in t)F(e,r,{get:t[r],enumerable:!0})},je=(e,t,r,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of lr(t))!dr.call(e,a)&&a!==r&&F(e,a,{get:()=>t[a],enumerable:!(o=sr(t,a))||o.enumerable});return e},ur=(e,t,r)=>(r=e!=null?nr(cr(e)):{},je(t||!e||!e.__esModule?F(r,"default",{value:e,enumerable:!0}):r,e)),yr=e=>je(F({},"__esModule",{value:!0}),e),He={};pr(He,{defaultProps:()=>_r,propTypes:()=>fr});var ze=yr(He),hr=ur(tt);const{string:u,bool:_,number:x,array:q,oneOfType:k,shape:v,object:h,func:p,node:he}=hr.default,fr={url:k([u,q,h]),playing:_,loop:_,controls:_,volume:x,muted:_,playbackRate:x,width:k([u,x]),height:k([u,x]),style:h,progressInterval:x,playsinline:_,pip:_,stopOnUnmount:_,light:k([_,u,h]),playIcon:he,previewTabIndex:x,previewAriaLabel:u,fallback:he,oEmbedUrl:u,wrapper:k([u,p,v({render:p.isRequired})]),config:v({soundcloud:v({options:h}),youtube:v({playerVars:h,embedOptions:h,onUnstarted:p}),facebook:v({appId:u,version:u,playerId:u,attributes:h}),dailymotion:v({params:h}),vimeo:v({playerOptions:h,title:u}),mux:v({attributes:h,version:u}),file:v({attributes:h,tracks:q,forceVideo:_,forceAudio:_,forceHLS:_,forceSafariHLS:_,forceDisableHls:_,forceDASH:_,forceFLV:_,hlsOptions:h,hlsVersion:u,dashVersion:u,flvVersion:u}),wistia:v({options:h,playerId:u,customControls:q}),mixcloud:v({options:h}),twitch:v({options:h,playerId:u}),vidyard:v({options:h})}),onReady:p,onStart:p,onPlay:p,onPause:p,onBuffer:p,onBufferEnd:p,onEnded:p,onError:p,onDuration:p,onSeek:p,onPlaybackRateChange:p,onPlaybackQualityChange:p,onProgress:p,onClickPreview:p,onEnablePIP:p,onDisablePIP:p},y=()=>{},_r={playing:!1,loop:!1,controls:!1,volume:null,muted:!1,playbackRate:1,width:"640px",height:"360px",style:{},progressInterval:1e3,playsinline:!1,pip:!1,stopOnUnmount:!0,light:!1,fallback:null,wrapper:"div",previewTabIndex:0,previewAriaLabel:"",oEmbedUrl:"https://noembed.com/embed?url={url}",config:{soundcloud:{options:{visual:!0,buying:!1,liking:!1,download:!1,sharing:!1,show_comments:!1,show_playcount:!1}},youtube:{playerVars:{playsinline:1,showinfo:0,rel:0,iv_load_policy:3,modestbranding:1},embedOptions:{},onUnstarted:y},facebook:{appId:"1309697205772819",version:"v3.3",playerId:null,attributes:{}},dailymotion:{params:{api:1,"endscreen-enable":!1}},vimeo:{playerOptions:{autopause:!1,byline:!1,portrait:!1,title:!1},title:null},mux:{attributes:{},version:"2"},file:{attributes:{},tracks:[],forceVideo:!1,forceAudio:!1,forceHLS:!1,forceDASH:!1,forceFLV:!1,hlsOptions:{},hlsVersion:"1.1.4",dashVersion:"3.1.3",flvVersion:"1.5.0",forceDisableHls:!1},wistia:{options:{},playerId:null,customControls:null},mixcloud:{options:{hide_cover:1}},twitch:{options:{},playerId:null},vidyard:{options:{}}},onReady:y,onStart:y,onPlay:y,onPause:y,onBuffer:y,onBufferEnd:y,onEnded:y,onError:y,onDuration:y,onSeek:y,onPlaybackRateChange:y,onPlaybackQualityChange:y,onProgress:y,onClickPreview:y,onEnablePIP:y,onDisablePIP:y};var mr=Object.create,V=Object.defineProperty,Pr=Object.getOwnPropertyDescriptor,br=Object.getOwnPropertyNames,gr=Object.getPrototypeOf,vr=Object.prototype.hasOwnProperty,wr=(e,t,r)=>t in e?V(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,Or=(e,t)=>{for(var r in t)V(e,r,{get:t[r],enumerable:!0})},Fe=(e,t,r,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of br(t))!vr.call(e,a)&&a!==r&&V(e,a,{get:()=>t[a],enumerable:!(o=Pr(t,a))||o.enumerable});return e},We=(e,t,r)=>(r=e!=null?mr(gr(e)):{},Fe(t||!e||!e.__esModule?V(r,"default",{value:e,enumerable:!0}):r,e)),Tr=e=>Fe(V({},"__esModule",{value:!0}),e),d=(e,t,r)=>(wr(e,typeof t!="symbol"?t+"":t,r),r),Ke={};Or(Ke,{default:()=>W});var Dr=Tr(Ke),fe=We(L),Sr=We(ge),Xe=ze,Er=z;const Cr=5e3;class W extends fe.Component{constructor(){super(...arguments),d(this,"mounted",!1),d(this,"isReady",!1),d(this,"isPlaying",!1),d(this,"isLoading",!0),d(this,"loadOnReady",null),d(this,"startOnPlay",!0),d(this,"seekOnPlay",null),d(this,"onDurationCalled",!1),d(this,"handlePlayerMount",t=>{if(this.player){this.progress();return}this.player=t,this.player.load(this.props.url),this.progress()}),d(this,"getInternalPlayer",t=>this.player?this.player[t]:null),d(this,"progress",()=>{if(this.props.url&&this.player&&this.isReady){const t=this.getCurrentTime()||0,r=this.getSecondsLoaded(),o=this.getDuration();if(o){const a={playedSeconds:t,played:t/o};r!==null&&(a.loadedSeconds=r,a.loaded=r/o),(a.playedSeconds!==this.prevPlayed||a.loadedSeconds!==this.prevLoaded)&&this.props.onProgress(a),this.prevPlayed=a.playedSeconds,this.prevLoaded=a.loadedSeconds}}this.progressTimeout=setTimeout(this.progress,this.props.progressFrequency||this.props.progressInterval)}),d(this,"handleReady",()=>{if(!this.mounted)return;this.isReady=!0,this.isLoading=!1;const{onReady:t,playing:r,volume:o,muted:a}=this.props;t(),!a&&o!==null&&this.player.setVolume(o),this.loadOnReady?(this.player.load(this.loadOnReady,!0),this.loadOnReady=null):r&&this.player.play(),this.handleDurationCheck()}),d(this,"handlePlay",()=>{this.isPlaying=!0,this.isLoading=!1;const{onStart:t,onPlay:r,playbackRate:o}=this.props;this.startOnPlay&&(this.player.setPlaybackRate&&o!==1&&this.player.setPlaybackRate(o),t(),this.startOnPlay=!1),r(),this.seekOnPlay&&(this.seekTo(this.seekOnPlay),this.seekOnPlay=null),this.handleDurationCheck()}),d(this,"handlePause",t=>{this.isPlaying=!1,this.isLoading||this.props.onPause(t)}),d(this,"handleEnded",()=>{const{activePlayer:t,loop:r,onEnded:o}=this.props;t.loopOnEnded&&r&&this.seekTo(0),r||(this.isPlaying=!1,o())}),d(this,"handleError",(...t)=>{this.isLoading=!1,this.props.onError(...t)}),d(this,"handleDurationCheck",()=>{clearTimeout(this.durationCheckTimeout);const t=this.getDuration();t?this.onDurationCalled||(this.props.onDuration(t),this.onDurationCalled=!0):this.durationCheckTimeout=setTimeout(this.handleDurationCheck,100)}),d(this,"handleLoaded",()=>{this.isLoading=!1})}componentDidMount(){this.mounted=!0}componentWillUnmount(){clearTimeout(this.progressTimeout),clearTimeout(this.durationCheckTimeout),this.isReady&&this.props.stopOnUnmount&&(this.player.stop(),this.player.disablePIP&&this.player.disablePIP()),this.mounted=!1}componentDidUpdate(t){if(!this.player)return;const{url:r,playing:o,volume:a,muted:s,playbackRate:w,pip:O,loop:T,activePlayer:S,disableDeferredLoading:b}=this.props;if(!(0,Sr.default)(t.url,r)){if(this.isLoading&&!S.forceLoad&&!b&&!(0,Er.isMediaStream)(r)){console.warn(`ReactPlayer: the attempt to load ${r} is being deferred until the player has loaded`),this.loadOnReady=r;return}this.isLoading=!0,this.startOnPlay=!0,this.onDurationCalled=!1,this.player.load(r,this.isReady)}!t.playing&&o&&!this.isPlaying&&this.player.play(),t.playing&&!o&&this.isPlaying&&this.player.pause(),!t.pip&&O&&this.player.enablePIP&&this.player.enablePIP(),t.pip&&!O&&this.player.disablePIP&&this.player.disablePIP(),t.volume!==a&&a!==null&&this.player.setVolume(a),t.muted!==s&&(s?this.player.mute():(this.player.unmute(),a!==null&&setTimeout(()=>this.player.setVolume(a)))),t.playbackRate!==w&&this.player.setPlaybackRate&&this.player.setPlaybackRate(w),t.loop!==T&&this.player.setLoop&&this.player.setLoop(T)}getDuration(){return this.isReady?this.player.getDuration():null}getCurrentTime(){return this.isReady?this.player.getCurrentTime():null}getSecondsLoaded(){return this.isReady?this.player.getSecondsLoaded():null}seekTo(t,r,o){if(!this.isReady){t!==0&&(this.seekOnPlay=t,setTimeout(()=>{this.seekOnPlay=null},Cr));return}if(r?r==="fraction":t>0&&t<1){const s=this.player.getDuration();if(!s){console.warn("ReactPlayer: could not seek using fraction – duration not yet available");return}this.player.seekTo(s*t,o);return}this.player.seekTo(t,o)}render(){const t=this.props.activePlayer;return t?fe.default.createElement(t,{...this.props,onMount:this.handlePlayerMount,onReady:this.handleReady,onPlay:this.handlePlay,onPause:this.handlePause,onEnded:this.handleEnded,onLoaded:this.handleLoaded,onError:this.handleError}):null}}d(W,"displayName","Player");d(W,"propTypes",Xe.propTypes);d(W,"defaultProps",Xe.defaultProps);var Ar=Object.create,B=Object.defineProperty,Rr=Object.getOwnPropertyDescriptor,xr=Object.getOwnPropertyNames,Ir=Object.getPrototypeOf,Mr=Object.prototype.hasOwnProperty,Lr=(e,t,r)=>t in e?B(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,$r=(e,t)=>{for(var r in t)B(e,r,{get:t[r],enumerable:!0})},Ye=(e,t,r,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of xr(t))!Mr.call(e,a)&&a!==r&&B(e,a,{get:()=>t[a],enumerable:!(o=Rr(t,a))||o.enumerable});return e},j=(e,t,r)=>(r=e!=null?Ar(Ir(e)):{},Ye(t||!e||!e.__esModule?B(r,"default",{value:e,enumerable:!0}):r,e)),kr=e=>Ye(B({},"__esModule",{value:!0}),e),c=(e,t,r)=>(Lr(e,typeof t!="symbol"?t+"":t,r),r),qe={};$r(qe,{createReactPlayer:()=>Wr});var Ur=kr(qe),I=j(L),Nr=j(be),J=j(ft),_e=j(ge),N=ze,Je=z,Vr=j(Dr);const Br=(0,Je.lazy)(()=>P(()=>import("./Preview-5c159bc7.js").then(e=>e.P),["assets/Preview-5c159bc7.js","assets/index-75c1660a.js","assets/index-a517bf54.css"])),jr=typeof window<"u"&&window.document&&typeof document<"u",Hr=typeof X<"u"&&X.window&&X.window.document,zr=Object.keys(N.propTypes),Fr=jr||Hr?I.Suspense:()=>null,U=[],Wr=(e,t)=>{var r;return r=class extends I.Component{constructor(){super(...arguments),c(this,"state",{showPreview:!!this.props.light}),c(this,"references",{wrapper:o=>{this.wrapper=o},player:o=>{this.player=o}}),c(this,"handleClickPreview",o=>{this.setState({showPreview:!1}),this.props.onClickPreview(o)}),c(this,"showPreview",()=>{this.setState({showPreview:!0})}),c(this,"getDuration",()=>this.player?this.player.getDuration():null),c(this,"getCurrentTime",()=>this.player?this.player.getCurrentTime():null),c(this,"getSecondsLoaded",()=>this.player?this.player.getSecondsLoaded():null),c(this,"getInternalPlayer",(o="player")=>this.player?this.player.getInternalPlayer(o):null),c(this,"seekTo",(o,a,s)=>{if(!this.player)return null;this.player.seekTo(o,a,s)}),c(this,"handleReady",()=>{this.props.onReady(this)}),c(this,"getActivePlayer",(0,J.default)(o=>{for(const a of[...U,...e])if(a.canPlay(o))return a;return t||null})),c(this,"getConfig",(0,J.default)((o,a)=>{const{config:s}=this.props;return Nr.default.all([N.defaultProps.config,N.defaultProps.config[a]||{},s,s[a]||{}])})),c(this,"getAttributes",(0,J.default)(o=>(0,Je.omit)(this.props,zr))),c(this,"renderActivePlayer",o=>{if(!o)return null;const a=this.getActivePlayer(o);if(!a)return null;const s=this.getConfig(o,a.key);return I.default.createElement(Vr.default,{...this.props,key:a.key,ref:this.references.player,config:s,activePlayer:a.lazyPlayer||a,onReady:this.handleReady})})}shouldComponentUpdate(o,a){return!(0,_e.default)(this.props,o)||!(0,_e.default)(this.state,a)}componentDidUpdate(o){const{light:a}=this.props;!o.light&&a&&this.setState({showPreview:!0}),o.light&&!a&&this.setState({showPreview:!1})}renderPreview(o){if(!o)return null;const{light:a,playIcon:s,previewTabIndex:w,oEmbedUrl:O,previewAriaLabel:T}=this.props;return I.default.createElement(Br,{url:o,light:a,playIcon:s,previewTabIndex:w,previewAriaLabel:T,oEmbedUrl:O,onClick:this.handleClickPreview})}render(){const{url:o,style:a,width:s,height:w,fallback:O,wrapper:T}=this.props,{showPreview:S}=this.state,b=this.getAttributes(o),E=typeof T=="string"?this.references.wrapper:void 0;return I.default.createElement(T,{ref:E,style:{...a,width:s,height:w},...b},I.default.createElement(Fr,{fallback:O},S?this.renderPreview(o):this.renderActivePlayer(o)))}},c(r,"displayName","ReactPlayer"),c(r,"propTypes",N.propTypes),c(r,"defaultProps",N.defaultProps),c(r,"addCustomPlayer",o=>{U.push(o)}),c(r,"removeCustomPlayers",()=>{U.length=0}),c(r,"canPlay",o=>{for(const a of[...U,...e])if(a.canPlay(o))return!0;return!1}),c(r,"canEnablePIP",o=>{for(const a of[...U,...e])if(a.canEnablePIP&&a.canEnablePIP(o))return!0;return!1}),r};var Kr=Object.create,K=Object.defineProperty,Xr=Object.getOwnPropertyDescriptor,Yr=Object.getOwnPropertyNames,qr=Object.getPrototypeOf,Jr=Object.prototype.hasOwnProperty,Zr=(e,t)=>{for(var r in t)K(e,r,{get:t[r],enumerable:!0})},Ze=(e,t,r,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of Yr(t))!Jr.call(e,a)&&a!==r&&K(e,a,{get:()=>t[a],enumerable:!(o=Xr(t,a))||o.enumerable});return e},Gr=(e,t,r)=>(r=e!=null?Kr(qr(e)):{},Ze(t||!e||!e.__esModule?K(r,"default",{value:e,enumerable:!0}):r,e)),Qr=e=>Ze(K({},"__esModule",{value:!0}),e),Ge={};Zr(Ge,{default:()=>aa});var ea=Qr(Ge),ee=Gr(or),ta=Ur;const ra=ee.default[ee.default.length-1];var aa=(0,ta.createReactPlayer)(ee.default,ra);const oa=rt(ea),ia=C(at)(({theme:e})=>({padding:e.spacing(3),marginBottom:e.spacing(2),borderRadius:e.spacing(2),boxShadow:"0 2px 12px rgba(0,0,0,0.08)"})),na=C(l)(({theme:e})=>({fontSize:"18px",fontWeight:600,color:e.palette.text.primary,marginBottom:e.spacing(2),borderBottom:`2px solid ${e.palette.primary.main}`,paddingBottom:e.spacing(1)})),me=C(ot)(({theme:e})=>({"& .MuiOutlinedInput-root":{borderRadius:e.spacing(1),backgroundColor:e.palette.grey[50]},"& .MuiOutlinedInput-input.Mui-disabled":{WebkitTextFillColor:e.palette.text.primary}})),sa=C(it)(({theme:e})=>({borderRadius:e.spacing(1),backgroundColor:e.palette.grey[50],"& .MuiSelect-select.Mui-disabled":{WebkitTextFillColor:e.palette.text.primary}})),Pe=C(M)(({theme:e})=>({backgroundColor:e.palette.grey[50],padding:e.spacing(2),borderRadius:e.spacing(1),border:`1px solid ${e.palette.divider}`})),la=C(nt)(({status:e})=>({fontSize:"14px",fontWeight:500,borderRadius:"8px",minWidth:"100px",backgroundColor:e==="Active"?"#e8f5e8":e==="Draft"?"#fff3e0":e==="Archived"?"#f3f4f6":"#e3f2fd",color:e==="Active"?"#2e7d32":e==="Draft"?"#ef6c00":e==="Archived"?"#616161":"#1976d2",border:`1px solid ${e==="Active"?"#c8e6c9":e==="Draft"?"#ffcc02":e==="Archived"?"#e0e0e0":"#bbdefb"}`})),ca=C(M)(({theme:e})=>({backgroundColor:e.palette.grey[50],padding:e.spacing(1.5),borderRadius:e.spacing(1),border:`1px solid ${e.palette.divider}`,minHeight:"40px",display:"flex",alignItems:"center"})),da=()=>{const e=st(),r=new URLSearchParams(e.search).get("BroadcastId"),o=lt(),[a,s]=L.useState({title:"",description:"",category:"",startDate:new Date,endDate:new Date,module:"",fileName:"",fileBase64:"",status:"",link:""}),[w,O]=L.useState(!1),T=`/${ce}/broadcastManagement/showBroadcastById/${r}`,S={position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",width:"80%",maxWidth:"800px",bgcolor:"background.paper",borderRadius:2,boxShadow:24,p:2},b=()=>{const A=D=>{s({title:D.broadcastDetailsDto.broadcastTitle,description:D.broadcastDetailsDto.description,category:D.broadcastDetailsDto.broadcastCategory,startDate:new Date(D.broadcastDetailsDto.startDate),endDate:new Date(D.broadcastDetailsDto.endDate),module:D.broadcastDetailsDto.module||"N/A",fileName:D.broadcastDetailsDto.fileName,fileBase64:D.broadcastDetailsDto.fileBase64,status:D.broadcastDetailsDto.status,link:D.broadcastDetailsDto.externalUrl})},et=D=>{console.error("Error fetching broadcast details:",D)};pt(`/${ce}/broadcastManagement/getBroadcastDetailsById/${r}`,"get",A,et)},E=()=>{O(!0)},Qe=A=>{switch(A){case"Announcements":return i(de,{color:"primary"});case"Videos":return i(pe,{color:"primary"});default:return null}};return L.useEffect(()=>{b()},[r]),n(M,{sx:{padding:2,paddingBottom:4},children:[i(g,{container:!0,sx:{borderRadius:2,marginBottom:2},children:i(g,{container:!0,children:i(g,{item:!0,md:7,style:{padding:"16px",paddingLeft:""},children:n($,{direction:"row",children:[i(le,{onClick:()=>o("/configCockpit/broadcastConfigurations"),color:"primary","aria-label":"back",children:i(yt,{sx:{fontSize:"25px",color:"#000000"}})}),n(M,{children:[i(l,{variant:"h5",paddingTop:"0.3rem",fontSize:"20px",children:n("strong",{children:["Broadcast Details: ",r]})}),i(l,{variant:"body2",color:"#777",fontSize:"12px",children:"This view displays the details of the broadcast"})]})]})})})}),n(ia,{children:[i(na,{children:"Broadcast Information"}),n(g,{container:!0,spacing:3,children:[n(g,{item:!0,xs:12,md:6,lg:3,children:[i(l,{variant:"subtitle2",gutterBottom:!0,children:"Broadcast Category"}),i(ct,{fullWidth:!0,size:"small",children:n(sa,{value:a.category,disabled:!0,displayEmpty:!0,renderValue:A=>n($,{direction:"row",spacing:1,alignItems:"center",children:[Qe(A),i(l,{children:A||"Not specified"})]}),children:[i(se,{value:"Announcements",children:n($,{direction:"row",spacing:1,alignItems:"center",children:[i(de,{color:"primary"}),i(l,{children:"Announcements"})]})}),i(se,{value:"Videos",children:n($,{direction:"row",spacing:1,alignItems:"center",children:[i(pe,{color:"primary"}),i(l,{children:"Videos"})]})})]})})]}),n(g,{item:!0,xs:12,md:6,lg:3,children:[i(l,{variant:"subtitle2",gutterBottom:!0,children:"Module"}),i(Pe,{children:i(l,{variant:"body2",children:a.module||"Not specified"})})]}),n(g,{item:!0,xs:12,md:6,lg:3,children:[i(l,{variant:"subtitle2",gutterBottom:!0,children:"Start Date"}),i(ue,{size:"sm",value:a.startDate,format:"dd MMM yyyy",style:{width:"100%",height:"40px"},disabled:!0})]}),n(g,{item:!0,xs:12,md:6,lg:3,children:[i(l,{variant:"subtitle2",gutterBottom:!0,children:"End Date"}),i(ue,{size:"sm",value:a.endDate,format:"dd MMM yyyy",style:{width:"100%",height:"40px"},disabled:!0})]}),n(g,{item:!0,xs:12,md:6,lg:3,children:[i(l,{variant:"subtitle2",gutterBottom:!0,children:"Status"}),i(la,{status:a.status,label:a.status||"Unknown"})]}),n(g,{item:!0,xs:12,children:[n(l,{variant:"subtitle2",gutterBottom:!0,children:["Broadcast Title",n(l,{component:"span",variant:"caption",color:"text.secondary",children:[" ","(",a.title.length,"/100 characters)"]})]}),i(me,{fullWidth:!0,value:a.title,disabled:!0,placeholder:"No title provided"})]}),n(g,{item:!0,xs:12,children:[n(l,{variant:"subtitle2",gutterBottom:!0,children:["Broadcast Description",n(l,{component:"span",variant:"caption",color:"text.secondary",children:[" ","(",a.description.length,"/300 characters)"]})]}),i(me,{fullWidth:!0,multiline:!0,rows:4,value:a.description,disabled:!0,placeholder:"No description provided"})]}),n(g,{item:!0,xs:12,md:8,children:[i(l,{variant:"subtitle2",gutterBottom:!0,children:"Attached Document"}),i(Pe,{children:a.fileName?n($,{direction:"row",alignItems:"center",spacing:1,children:[i(l,{variant:"body2",children:a.fileName}),a.category==="Videos"?i(dt,{title:"View Video",children:i(le,{size:"small",onClick:E,children:mt})}):i(_t,{index:r,name:a.fileName,isBroadcast:!0})]}):i(l,{variant:"body2",color:"text.secondary",children:"No document attached"})})]}),n(g,{item:!0,xs:12,md:4,children:[i(l,{variant:"subtitle2",gutterBottom:!0,children:"External URL"}),i(ca,{children:a.link?i(l,{variant:"body2",component:"a",href:a.link,target:"_blank",rel:"noopener noreferrer",sx:{color:"primary.main",textDecoration:"none","&:hover":{textDecoration:"underline"},overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:a.link}):i(l,{variant:"body2",color:"text.secondary",children:"No external link provided"})})]})]})]}),i(ut,{open:w,onClose:()=>O(!1),"aria-labelledby":"video-modal-title",children:n(M,{sx:S,children:[i(l,{variant:"h6",component:"h2",gutterBottom:!0,children:"Video Preview"}),i(M,{sx:{position:"relative",paddingTop:"56.25%"},children:i(oa,{url:T,controls:!0,width:"100%",height:"100%",style:{position:"absolute",top:0,left:0}})})]})})]})},ga=Object.freeze(Object.defineProperty({__proto__:null,default:da},Symbol.toStringTag,{value:"Module"}));export{ga as V,Jt as p,z as u};
