import{d7 as f,d8 as g,cB as b,cC as M,cE as a,r as n,cH as j,cI as v,ed as w,o as l,cM as E,cN as N}from"./index-17b8d91e.js";import{d as y,c as P,b as U}from"./Stepper-88e4fb0c.js";function $(t){return f("MuiStepButton",t)}const L=g("MuiStepButton",["root","horizontal","vertical","touchRipple"]),u=L,_=["children","className","icon","optional"],z=t=>{const{classes:e,orientation:o}=t;return N({root:["root",o],touchRipple:["touchRipple"]},$,e)},D=b(M,{name:"MuiStepButton",slot:"Root",overridesResolver:(t,e)=>{const{ownerState:o}=t;return[{[`& .${u.touchRipple}`]:e.touchRipple},e.root,e[o.orientation]]}})(({ownerState:t})=>a({width:"100%",padding:"24px 16px",margin:"-24px -16px",boxSizing:"content-box"},t.orientation==="vertical"&&{justifyContent:"flex-start",padding:"8px",margin:"-8px"},{[`& .${u.touchRipple}`]:{color:"rgba(0, 0, 0, 0.3)"}})),H=n.forwardRef(function(e,o){const s=j({props:e,name:"MuiStepButton"}),{children:i,className:d,icon:x,optional:h}=s,S=v(s,_),{disabled:m,active:B}=n.useContext(y),{orientation:R}=n.useContext(P),c=a({},s,{orientation:R}),r=z(c),p={icon:x,optional:h},C=w(i,["StepLabel"])?n.cloneElement(i,p):l.jsx(U,a({},p,{children:i}));return l.jsx(D,a({focusRipple:!0,disabled:m,TouchRippleProps:{className:r.touchRipple},className:E(r.root,d),ref:o,ownerState:c,"aria-current":B?"step":void 0},S,{children:C}))}),W=H;export{W as S,$ as g,u as s};
