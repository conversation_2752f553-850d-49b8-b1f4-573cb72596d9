import{r as h,q as S,s as ge,u as ue,cf as ee,a as e,x as H,j as n,T as u,b6 as Ce,E as se,at as xe,au as ye,ay as ie,F as te,G as s,bI as be,ca as ve,K as U,b$ as V,ai as K,b as De,cd as Se,al as Ae,z as ke,B as Q,y as ce,b4 as Ee,br as Fe,aE as de,t as T,aB as W,aD as he,cg as X,bp as we,I as Ne,b1 as Be,b8 as pe}from"./index-17b8d91e.js";import{d as je}from"./ArrowCircleLeftOutlined-2a09f8e2.js";import{d as Re}from"./EditOutlined-36c8ca4d.js";import{D as Te}from"./DatePicker-68227989.js";import{C as Me,d as _e}from"./ChangeLog-0f47d713.js";import{a as $e,b as ze,S as Pe}from"./Stepper-88e4fb0c.js";import"./dateViewRenderers-34586552.js";import"./useSlotProps-e34e1e13.js";import"./InputAdornment-5b0053c5.js";import"./CSSTransition-30917e2c.js";import"./useMediaQuery-6a073ac5.js";import"./DesktopDatePicker-07c19cde.js";import"./useMobilePicker-9978caff.js";function Le(l,p){return Array.isArray(p)&&p.find(b=>b.code===l)||""}const qe=({label:l,value:p,length:$,fieldGroup:b,isEditMode:A,visibility:k,isExtendMode:me,selectedRowData:Y,type:x,activeTabIndex:w,ccTabs:I})=>{var q;const[v,M]=h.useState(p),[ae,Z]=h.useState(!1),_=S(a=>a.AllDropDown.dropDown),d=S(a=>a.costCenter.MultipleCostCenterData),m=ge();Le(v,_),ue(),S(a=>a.edit.payload);let D={},E=-1;for(let a=0;a<(d==null?void 0:d.length);a++)if(d[a].costCenter===Y){D=d[a],E=a;break}let i=I[w];console.log("activerow",E,D,i);const z=(a,o)=>{const y=a==null?void 0:a.find(r=>(r==null?void 0:r.fieldName)===o);return y?y.value:""},f=d[E];console.log(f,"costCenterInnerData"),h.useEffect(()=>{(k==="0"||k==="Required")&&(i==="Basic Data"?(console.log("active_tab_coming"),m(ee(N)),m(ee(["Name"]))):m(ee(N)))},[i]);const F=(a,o)=>{console.log("label",a,o),m(be({keyname:N.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""),data:o}));let y=d==null?void 0:d.map((r,B)=>{let j=I[w];if(B===E){let G=r==null?void 0:r.viewData,J=r==null?void 0:r.viewData[j];console.log("temp",J);let R=r==null?void 0:r.viewData[j][b];return console.log("temp2",R),{...r,viewData:{...G,[j]:{...J,[b]:R==null?void 0:R.map(O=>O.fieldName===a?{...O,value:o}:O)}}}}else return r});m(ve(y))};let N=l.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("");h.useEffect(()=>{M(p)},[p]);const P=a=>{const o=r=>{console.log("value",r),m(K({keyName:"Region",data:r.body}))},y=r=>{console.log(r,"error in dojax")};U(`/${V}/data/getRegionBasedOnCountry?country=${a}`,"get",o,y)},L=a=>{console.log("compcode1",a);const o=r=>{console.log(r,"data145"),m(K({keyName:"Currency",data:r.body}))},y=r=>{console.log(r,"error in dojax")};U(`/${V}/data/getCurrency?companyCode=${a}`,"get",o,y)};return h.useEffect(()=>{l==="Country/Reg"&&P(v),l==="Company Code"&&L(v)},[]),e(s,{item:!0,children:e(H,{children:A?n(te,{children:[n(u,{variant:"body2",color:"#777",children:[l," ",k==="Required"||k==="0"?e("span",{style:{color:"red"},children:"*"}):""]}),x==="Drop Down"?e(Ce,{options:_[N]??[],value:(z(f==null?void 0:f.viewData[i][b],l)&&((q=_[N])==null?void 0:q.filter(a=>a.code===z(f==null?void 0:f.viewData[i][b],l))))[0]||"",onChange:(a,o)=>{l==="Country/Reg"&&P(o.code),l==="Comp Code"&&L(o.code),F(l,o==null?void 0:o.code),M(o==null?void 0:o.code),Z(!0)},getOptionLabel:a=>a===""||(a==null?void 0:a.code)===""?"":`${a==null?void 0:a.code} - ${a==null?void 0:a.desc}`??"",renderOption:(a,o)=>(console.log("option vakue",o),e("li",{...a,children:e(u,{style:{fontSize:12},children:`${o==null?void 0:o.code} - ${o==null?void 0:o.desc}`})})),renderInput:a=>e(se,{...a,variant:"outlined",size:"small",label:null,placeholder:l})}):x==="Input"?e(se,{variant:"outlined",size:"small",value:z(f==null?void 0:f.viewData[i][b],l).toUpperCase(),inputProps:{maxLength:$},onChange:a=>{console.log("event",a.target.value);const o=a.target.value;if(o.length>0&&o[0]===" ")F(l,o.trimStart());else{let y=o.toUpperCase();F(l,y)}},placeholder:l}):x==="Calendar"?e(xe,{dateAdapter:ye,children:e(Te,{slotProps:{textField:{size:"small"}},placeholder:"Select Date Range"})}):x==="Radio Button"?e(ie,{sx:{borderRadius:"0 !important"},checked:v,onChange:(a,o)=>{F(l,o),M(o)}}):""]}):e(te,{children:n(te,{children:[n(u,{variant:"body2",color:"#777",children:[l," ",k==="Required"||k==="0"?e("span",{style:{color:"red"},children:"*"}):""]}),e(u,{variant:"body2",fontWeight:"bold",children:x==="Radio Button"?e(ie,{sx:{padding:0},checked:v,disabled:!0}):v})]})})})})},et=()=>{var oe,re,ne;const l=De(),p=ge();h.useState({});const[$,b]=h.useState(0),[A,k]=h.useState(!1),[me,Y]=h.useState(!0),[x,w]=h.useState(0);h.useState(0);const[I,v]=h.useState(!1),[M,ae]=h.useState([]),[Z,_]=h.useState(!1),d=ue();S(t=>t.initialData.EditMultipleMaterial);const m=S(t=>t.costCenter.MultipleCostCenterData);console.log(m,"costCenterData");const D=S(t=>t.appSettings),E=d.state.tabsData.viewData;console.log("tabsData",E);const i=d.state.rowData,z=d.state.requestNumber;S(t=>t.payload);let f=S(t=>t.edit.payload),F=S(t=>t.costCenter.requiredFields);console.log(f,F,"required_field_for_data");const N=()=>{k(!0),Y(!1)},P=()=>{const t=R();A?t?(w(c=>c-1),p(X())):q():(w(c=>c-1),p(X()))},L=()=>{const t=R();A?t?(w(c=>c+1),p(X())):q():(w(c=>c+1),p(X()))},q=()=>{_(!0)};for(let t=0;t<((oe=m==null?void 0:m.tableData)==null?void 0:oe.length);t++)if(m.tableData[t].Description===i.description){m.tableData[t];break}const a=t=>{const c=g=>{p(K({keyName:"HierarchyArea",data:g.body}))},C=g=>{console.log(g)};U(`/${V}/data/getHierarchyArea?controllingArea=${t}`,"get",c,C)},o=t=>{const c=g=>{p(K({keyName:"CompanyCode",data:g.body}))},C=g=>{console.log(g)};U(`/${V}/data/getCompanyCodeBasedOnControllingArea?controllingArea=${t}`,"get",c,C)},y=t=>{const c=g=>{p(K({keyName:"ProfitCenter",data:g.body}))},C=g=>{console.log(g)};U(`/${V}/data/getProfitCenterAsPerControllingArea?controllingArea=${t}`,"get",c,C)};h.useEffect(()=>{a(i.controllingArea),o(i.controllingArea),y(i.controllingArea)},[]);const r=Object.entries(E).filter(t=>typeof t[1]=="object"&&t[1]!=null).map(t=>t[0]),B=Object.entries(E).filter(t=>typeof t[1]=="object"&&t[1]!=null).map(t=>Object.entries(t[1]));console.log(B,"tabContents");const j={};B.map(t=>{t.forEach((c,C)=>{c.forEach((g,fe)=>{fe!==0&&g.forEach(le=>{j[le.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("")]=le.value})})})}),h.useEffect(()=>{p(Se(j))},[]),console.log(j,"tempHash"),console.log("activeTab",B);const G=t=>{v(t)},J=()=>{v(!0)};console.log(f,F,"eror_arr");const R=()=>we(f,F,ae),O=()=>{_(!1)};return n("div",{children:[n(s,{container:!0,style:{...Ae,backgroundColor:"#FAFCFF"},children:[M.length!=0&&e(ke,{openSnackBar:Z,alertMsg:"Please fill the following Field: "+M.join(", "),handleSnackBarClose:O}),n(s,{sx:{width:"inherit"},children:[n(s,{item:!0,md:12,style:{padding:"16px",display:"flex"},children:[e(s,{item:!0,style:{display:"flex",justifyContent:"flex-end"},children:e(Ne,{color:"primary","aria-label":"upload picture",component:"label",sx:Be,children:e(je,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{l("/masterDataCockpit/costCenter/createMultipleCostCenter")}})})}),n(s,{md:10,children:[e(u,{variant:"h3",children:n("strong",{children:["Cost Center: ",i.costCenter," "]})}),e(u,{variant:"body2",color:"#777",children:"This view creates a new Cost Center"})]}),(re=d==null?void 0:d.state)!=null&&re.requestNumber?e(s,{md:1,sx:{display:"flex",justifyContent:"flex-end"},children:e(T,{variant:"outlined",size:"small",sx:pe,onClick:J,title:"Change Log",children:e(_e,{sx:{padding:"2px"},fontSize:"small"})})}):e(s,{md:1,sx:{display:"flex",justifyContent:"flex-end"}}),I&&e(Me,{open:!0,closeModal:G,requestId:z,requestType:"Mass",pageName:"costCenter",controllingArea:i.controllingArea,centerName:i.costCenter}),A?"":e(s,{md:1,sx:{display:"flex",justifyContent:"flex-end"},children:e(s,{item:!0,children:n(T,{variant:"outlined",size:"small",sx:pe,onClick:N,children:["Change",e(Re,{sx:{padding:"2px"},fontSize:"small"})]})})})]}),e(s,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:n(Q,{width:"70%",sx:{marginLeft:"40px"},children:[e(s,{item:!0,sx:{paddingTop:"2px !important"},children:n(H,{flexDirection:"row",children:[e("div",{style:{width:"15%"},children:e(u,{variant:"body2",color:"#777",children:"Cost Center"})}),n(u,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[": ",i.costCenter]})]})}),e(s,{item:!0,sx:{paddingTop:"2px !important"},children:n(H,{flexDirection:"row",children:[e("div",{style:{width:"15%"},children:e(u,{variant:"body2",color:"#777",children:"Controlling Area"})}),n(u,{variant:"body2",fontWeight:"bold",children:[": ",i.controllingArea]})]})}),e(s,{item:!0,sx:{paddingTop:"2px !important"},children:n(H,{flexDirection:"row",children:[e("div",{style:{width:"15%"},children:e(u,{variant:"body2",color:"#777",children:"Valid From"})}),n(u,{variant:"body2",fontWeight:"bold",children:[":"," ",ce(i.validFrom).format(D==null?void 0:D.dateFormat)]})]})}),e(s,{item:!0,sx:{paddingTop:"2px !important"},children:n(H,{flexDirection:"row",children:[e("div",{style:{width:"15%"},children:e(u,{variant:"body2",color:"#777",children:"Valid To"})}),n(u,{variant:"body2",fontWeight:"bold",children:[":"," ",ce(i.validTo).format(D==null?void 0:D.dateFormat)]})]})})]})}),n(s,{container:!0,style:{padding:"16px"},children:[e(Pe,{activeStep:x,sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},"aria-label":"mui tabs example",children:r.map((t,c)=>e($e,{children:e(ze,{sx:{fontWeight:"700"},children:t})},t))}),e(s,{container:!0,children:B&&((ne=B[x])==null?void 0:ne.map((t,c)=>e(Q,{sx:{width:"100%"},children:n(s,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Ee},children:[e(s,{container:!0,children:e(u,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:t[0]})}),e(Q,{children:e(Q,{sx:{width:"100%"},children:e(Fe,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:e(s,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:[...t[1]].map(C=>e(qe,{activeTabIndex:x,fieldGroup:t[0],selectedRowData:i.costCenter,ccTabs:r,visibility:C.visibility,label:C.fieldName,value:C.value,length:C.maxLength,onSave:g=>handleFieldSave(C.fieldName,g),isEditMode:A,type:C.fieldType,field:C}))})})})})]})},c)))})]})]})]}),A?e(he,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:n(de,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:$,onChange:t=>{b(t)},children:[e(T,{size:"small",sx:{...W,mr:1},variant:"contained",onClick:()=>{l("/masterDataCockpit/costCenter/createMultipleCostCenter")},children:"Save"}),e(T,{variant:"contained",size:"small",sx:{...W,mr:1},onClick:P,disabled:x===0,children:"Back"}),e(T,{variant:"contained",size:"small",sx:{...W,mr:1},onClick:L,disabled:x===r.length-1,children:"Next"})]})}):"",A?"":e(he,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:n(de,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:$,onChange:t=>{b(t)},children:[e(T,{variant:"contained",size:"small",sx:{...W,mr:1},onClick:P,disabled:x===0,children:"Back"}),e(T,{variant:"contained",size:"small",sx:{...W,mr:1},onClick:L,disabled:x===r.length-1,children:"Next"})]})})]})};export{et as default};
