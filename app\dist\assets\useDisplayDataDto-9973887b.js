import{bQ as Ee,q as V,cP as Je,cQ as je,s as Ie,u as ke,ud as Ce,eo as pe,ue as es,a4 as i,K as te,bn as be,$ as oe,a0 as ae,ai as ne,ae as xe,uf as ss,ug as ts,uh as ls,r as ce,e2 as ee,aT as ve,aU as Ke,ui as Ye,uj as se,uk as Xe,ul as Ze,a_ as W,um as Qe,un as os,uo as as,Z as ue,di as cs,pS as ds,up as Ne,uq as is,ur as we,us as ns,pV as us,ut as Ts,aH as Cs,uu as Ns,a5 as ze}from"./index-75c1660a.js";const Es=()=>{const{customError:u}=Ee(),N=V(_=>_.payload.payloadData),B=V(_=>_.applicationConfig),U=Je(je.CURRENT_TASK);let m=null;m=typeof U=="string"?JSON.parse(U):U;let r=m==null?void 0:m.ATTRIBUTE_5;const g=Ie(),x=ke(),q=new URLSearchParams(x.search),X=V(_=>_.userManagement.taskData),H=q.get("reqBench"),Z=q.get("RequestId");return{getChangeTemplate:(_,E)=>{var D,z,c;g(Ce(!0));let I={decisionTableId:null,decisionTableName:"MDG_MAT_CHANGE_TEMPLATE",version:"v6",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_TEMPLATE":(N==null?void 0:N.TemplateName)||(_==null?void 0:_.TemplateName),"MDG_CONDITIONS.MDG_MAT_REGION":(N==null?void 0:N.Region)||(_==null?void 0:_.Region)||((D=pe)==null?void 0:D.US),"MDG_CONDITIONS.MDG_MAT_GROUP_ROLE":Z&&!H?(X==null?void 0:X.ATTRIBUTE_5)||r:(z=es)==null?void 0:z.REQ_INITIATE,"MDG_CONDITIONS.MDG_MAT_SCENARIO":(N==null?void 0:N.RequestType)||(_==null?void 0:_.RequestType)||((c=i)==null?void 0:c.CHANGE)}],systemFilters:null,systemOrders:null,filterString:null};const w=K=>{var e,o,t,n,s,l,a,C,d,h,M,L,S;if(K.statusCode===((e=ae)==null?void 0:e.STATUS_200)){g(Ce(!1));let O=(t=(o=K==null?void 0:K.data)==null?void 0:o.result[0])==null?void 0:t.MDG_MAT_CHANGE_TEMPLATE,P=(N==null?void 0:N.TemplateName)||(_==null?void 0:_.TemplateName)||"";const y=(s=(n=O==null?void 0:O.filter(T=>T.MDG_MAT_VIEW_NAME!=="Header"))==null?void 0:n.sort((T,A)=>T.MDG_MAT_FIELD_SEQUENCE-A.MDG_MAT_FIELD_SEQUENCE))==null?void 0:s.map(T=>({fieldName:T.MDG_MAT_UI_FIELD_NAME,viewName:T.MDG_MAT_VIEW_NAME,sequenceNo:T.MDG_MAT_FIELD_SEQUENCE,fieldType:T.MDG_MAT_FIELD_TYPE,maxLength:T.MDG_MAT_MAX_LENGTH,value:T.MDG_MAT_DEFAULT_VALUE,visibility:T.MDG_MAT_VISIBILITY,jsonName:T.MDG_MAT_JSON_FIELD_NAME,templateVisibility:T.MDG_MAT_TEMPLATE_SELECTIVITY}));let F=[];const J=y==null?void 0:y.reduce((T,A)=>{A.fieldName!=="Material"&&A.fieldName!=="Plant"&&A.fieldName!=="Warehouse"&&A.fieldName!=="Sales Org"&&A.fieldName!=="Distribution Channel"&&A.fieldName!=="MRP Controller"&&F.push({code:A.fieldName,desc:""});const Q=A.viewName;return T[Q]||(T[Q]=[]),T[Q].push(A),T},{}),p=(a=(l=Object==null?void 0:Object.keys(J))==null?void 0:l.sort())==null?void 0:a.reduce((T,A)=>(T[A]=J[A],T),{}),j=[];y==null||y.forEach(T=>{(T==null?void 0:T.visibility)==="Mandatory"&&j.push({jsonName:T==null?void 0:T.jsonName,fieldName:T==null?void 0:T.fieldName})});const G={[P]:y,"Config Data":p,"Field Selectivity":(C=y[0])==null?void 0:C.templateVisibility,FieldNames:F,"Mandatory Fields":[{jsonName:"Material",fieldName:"Material"},...j]};if(g(ne({keyName:"FieldName",data:G==null?void 0:G.FieldNames})),g(xe(G)),(Object==null?void 0:Object.keys(E).length)>0){const T=ss(G==null?void 0:G["Config Data"],(h=(d=E==null?void 0:E.Torequestheaderdata)==null?void 0:d.FieldName)==null?void 0:h.split("$^$"),["Material","Plant","Sales Org","Distribution Channel","Warehouse","MRP Controller"]);g(xe({...G,"Config Data":T}));const A=ts(G==null?void 0:G[P||((M=E==null?void 0:E.Torequestheaderdata)==null?void 0:M.TemplateName)],(S=(L=E==null?void 0:E.Torequestheaderdata)==null?void 0:L.FieldName)==null?void 0:S.split("$^$"));g(ls([...A]))}}else g(Ce(!1)),u("Failed to fetch data")},k=K=>{u(K)};B.environment==="localhost"?te(`/${be}${oe.INVOKE_RULES.LOCAL}`,"post",w,k,I):te(`/${be}${oe.INVOKE_RULES.PROD}`,"post",w,k,I)}}},Is=()=>{const u=Ie(),N=V(e=>e.userManagement.taskData),B=V(e=>e.paginationData),U=V(e=>e.payload.changeFieldRows),m=V(e=>e.payload.changeFieldRowsDisplay),r=V(e=>e.payload.dynamicKeyValues),g=V(e=>e.payload.selectedRows),x=V(e=>e.payload.whseList),q=V(e=>e.payload.plantList),X=V(e=>e.payload.matNoList),{customError:H}=Ee(),Z=e=>{var a,C,d,h,M,L,S,O,P,y,F,J,p,j,G,T,A,Q,f,b,de,le,v,ie,he,re,ge,_e,Ae,Me,Se,fe,ye,me,Le,Oe,Re,De,Pe,Fe,Ge,Ue,qe,Be,He,We;const o=((a=e[0])==null?void 0:a.TemplateName)===((C=ee)==null?void 0:C.LOGISTIC)?$(e):((d=e[0])==null?void 0:d.TemplateName)===((h=ee)==null?void 0:h.ITEM_CAT)?_(e):((M=e[0])==null?void 0:M.TemplateName)===((L=ee)==null?void 0:L.MRP)?E(e):((S=e[0])==null?void 0:S.TemplateName)===((O=ee)==null?void 0:O.UPD_DESC)?k(e):((P=e[0])==null?void 0:P.TemplateName)===((y=ee)==null?void 0:y.WARE_VIEW_2)?D(e):((F=e[0])==null?void 0:F.TemplateName)===((J=ee)==null?void 0:J.CHG_STAT)?I(e):((p=e[0])==null?void 0:p.TemplateName)===((j=ee)==null?void 0:j.SET_DNU)?w(e):[];if(Array.isArray(o))u(ve([...U,...o])),u(Ke({...m,[B==null?void 0:B.page]:o}));else if(typeof o=="object"&&o!==null){const R={...U};(G=Object==null?void 0:Object.keys(o))==null||G.forEach(Y=>{R[Y]=[...R[Y]||[],...o[Y]]}),u(ve(R)),u(Ke({...m,[B==null?void 0:B.page]:o}))}let t;if(Array.isArray(o))t=o.map(R=>R==null?void 0:R.id),u(Ye([...g,...t]));else if(typeof o=="object"&&o!==null){t=Object.keys(o).reduce((Y,Ve)=>{var $e;return Y[Ve]=(($e=o[Ve])==null?void 0:$e.map(Te=>Te==null?void 0:Te.id))||[],Y},{});const R={...g};(T=Object==null?void 0:Object.keys(t))==null||T.forEach(Y=>{R[Y]=[...R[Y]||[],...t[Y]]}),u(Ye(R))}u(se({keyName:"requestHeaderData",data:(A=e[0])==null?void 0:A.Torequestheaderdata})),u(se({keyName:"childRequestHeaderData",data:(Q=e[0])==null?void 0:Q.Tochildrequestheaderdata})),u(se({keyName:"changeLogData",data:(f=e[0])==null?void 0:f.changeLogData})),u(se({keyName:"templateName",data:(b=e[0])==null?void 0:b.TemplateName}));const n={};e==null||e.forEach(R=>{n[R==null?void 0:R.Material]=R==null?void 0:R.Tomaterialerrordata}),u(se({keyName:"errorData",data:{...(r==null?void 0:r.errorData)||{},...n}}));const s={};s.IntermediateTaskCount=(de=e[0])==null?void 0:de.IntermediateTaskCount,s.TotalIntermediateTasks=(le=e[0])==null?void 0:le.TotalIntermediateTasks,s.MassEditId=(v=e[0])==null?void 0:v.MassEditId,s.MassChildEditId=(ie=e[0])==null?void 0:ie.MassChildEditId,s.Comments=((he=e[0])==null?void 0:he.Comments)||"",s.TaskId=N==null?void 0:N.taskId,s.TaskName=N==null?void 0:N.taskDesc,s.CreationTime=N!=null&&N.createdOn?Xe(N==null?void 0:N.createdOn):null,s.DueDate=N!=null&&N.criticalDeadline?Xe(N==null?void 0:N.criticalDeadline):null,u(se({keyName:"otherPayloadData",data:s}));const l={ReqCreatedBy:(ge=(re=e[0])==null?void 0:re.Torequestheaderdata)==null?void 0:ge.ReqCreatedBy,RequestStatus:(Ae=(_e=e[0])==null?void 0:_e.Torequestheaderdata)==null?void 0:Ae.RequestStatus,Region:(Se=(Me=e[0])==null?void 0:Me.Torequestheaderdata)==null?void 0:Se.Region,ReqCreatedOn:new Date().toISOString(),ReqUpdatedOn:new Date().toISOString(),RequestType:(ye=(fe=e[0])==null?void 0:fe.Torequestheaderdata)==null?void 0:ye.RequestType,RequestDesc:(Le=(me=e[0])==null?void 0:me.Torequestheaderdata)==null?void 0:Le.RequestDesc,RequestPriority:(Re=(Oe=e[0])==null?void 0:Oe.Torequestheaderdata)==null?void 0:Re.RequestPriority,LeadingCat:(Pe=(De=e[0])==null?void 0:De.Torequestheaderdata)==null?void 0:Pe.LeadingCat,RequestId:(Ge=(Fe=e[0])==null?void 0:Fe.Torequestheaderdata)==null?void 0:Ge.RequestId,TemplateName:(qe=(Ue=e[0])==null?void 0:Ue.Torequestheaderdata)==null?void 0:qe.TemplateName,FieldName:(We=(He=(Be=e[0])==null?void 0:Be.Torequestheaderdata)==null?void 0:He.FieldName)==null?void 0:We.split("$^$")};u(Ze({data:l}))},$=e=>{const o=[];let t=1;const n=new Set;return e.forEach(s=>{s.Touomdata.forEach(l=>{var C,d;n.add(l.Material);const a={...l,Material:s==null?void 0:s.Material,MaterialId:s==null?void 0:s.MaterialId,ClientId:(C=s==null?void 0:s.Toclientdata)==null?void 0:C.ClientId,id:W(),slNo:t++,MatlType:(s==null?void 0:s.MatlType)||"",ChangeLogId:((d=s==null?void 0:s.changeLogData)==null?void 0:d.ChangeLogId)??null};o.push(a)})}),u(Qe([...X,...n])),o},_=e=>{const o=[];let t=1;return e.forEach(n=>{n.Tosalesdata.forEach(s=>{var a,C;const l={...s,Material:n==null?void 0:n.Material,MaterialId:n==null?void 0:n.MaterialId,ClientId:(a=n==null?void 0:n.Toclientdata)==null?void 0:a.ClientId,id:W(),slNo:t++,MatlType:(n==null?void 0:n.MatlType)||"",ChangeLogId:((C=n==null?void 0:n.changeLogData)==null?void 0:C.ChangeLogId)??null};o.push(l)})}),o},E=e=>{const o={"Basic Data":[],"Plant Data":[]};let t=1,n=1;const s=new Set;return e.forEach(l=>{var L;const{Toplantdata:a,Toclientdata:C,Material:d,MaterialId:h,MatlType:M}=l;o["Basic Data"].push({...C,id:W(),slNo:t++,type:"Basic Data",Material:d,MaterialId:h,Function:"UPD",MatlType:M,ChangeLogId:((L=l==null?void 0:l.changeLogData)==null?void 0:L.ChangeLogId)??null}),a==null||a.forEach(S=>{var O;s.add(S==null?void 0:S.Plant),o["Plant Data"].push({...S,id:W(),Material:d,slNo:n++,type:"Plant Data",MaterialId:h,Function:"UPD",ChangeLogId:((O=l==null?void 0:l.changeLogData)==null?void 0:O.ChangeLogId)??null})})}),u(os([...q,...s])),o},I=e=>{const o={"Basic Data":[],"Plant Data":[],"Sales Data":[]};let t=1,n=1,s=1;return e.forEach(l=>{var S;const{Toplantdata:a,Toclientdata:C,Tosalesdata:d,Material:h,MaterialId:M,MatlType:L}=l;o["Basic Data"].push({...C,id:W(),slNo:t++,type:"Basic Data",Material:h,MaterialId:M,Function:"UPD",MatlType:L,ChangeLogId:((S=l==null?void 0:l.changeLogData)==null?void 0:S.ChangeLogId)??null}),a==null||a.forEach(O=>{var P;o["Plant Data"].push({...O,id:W(),Material:h,slNo:n++,type:"Plant Data",MaterialId:M,Function:"UPD",ChangeLogId:((P=l==null?void 0:l.changeLogData)==null?void 0:P.ChangeLogId)??null})}),d==null||d.forEach(O=>{var P;o["Sales Data"].push({...O,id:W(),Material:h,slNo:s++,type:"Sales Data",MaterialId:M,Function:"UPD",ChangeLogId:((P=l==null?void 0:l.changeLogData)==null?void 0:P.ChangeLogId)??null})})}),o},w=e=>{const o={"Basic Data":[],"Plant Data":[],"Sales Data":[],Description:[]};let t=1,n=1,s=1,l=1;return e.forEach(a=>{var P;const{Toplantdata:C,Toclientdata:d,Tosalesdata:h,Tomaterialdescription:M,Material:L,MaterialId:S,MatlType:O}=a;o["Basic Data"].push({...d,id:W(),slNo:t++,type:"Basic Data",Material:L,MaterialId:S,Function:"UPD",MatlType:O,ChangeLogId:((P=a==null?void 0:a.changeLogData)==null?void 0:P.ChangeLogId)??null}),C==null||C.forEach(y=>{var F;o["Plant Data"].push({...y,id:W(),Material:L,slNo:n++,type:"Plant Data",MaterialId:S,Function:"UPD",ChangeLogId:((F=a==null?void 0:a.changeLogData)==null?void 0:F.ChangeLogId)??null})}),h==null||h.forEach(y=>{var F;o["Sales Data"].push({...y,id:W(),Material:L,slNo:s++,type:"Sales Data",MaterialId:S,Function:"UPD",ChangeLogId:((F=a==null?void 0:a.changeLogData)==null?void 0:F.ChangeLogId)??null})}),M==null||M.forEach(y=>{var F;o.Description.push({...y,id:W(),Material:L,slNo:l++,type:"Description",MaterialId:S,Function:"UPD",ChangeLogId:((F=a==null?void 0:a.changeLogData)==null?void 0:F.ChangeLogId)??null})})}),o},k=e=>{const o=[];let t=1;const n=new Set;return e.forEach(s=>{s.Tomaterialdescription.forEach(l=>{var C,d;n.add(l.Material);const a={...l,Material:s==null?void 0:s.Material,MaterialId:s==null?void 0:s.MaterialId,ClientId:(C=s==null?void 0:s.Toclientdata)==null?void 0:C.ClientId,id:W(),slNo:t++,MatlType:(s==null?void 0:s.MatlType)||"",ChangeLogId:((d=s==null?void 0:s.changeLogData)==null?void 0:d.ChangeLogId)??null};o.push(a)})}),u(Qe([...X,...n])),o},D=e=>{const o=[],t=new Set;let n=1;e.forEach(l=>{l.Towarehousedata.forEach(a=>{var d,h;t.add(a.WhseNo);const C={...a,Material:l==null?void 0:l.Material,MaterialId:l==null?void 0:l.MaterialId,ClientId:(d=l==null?void 0:l.Toclientdata)==null?void 0:d.ClientId,id:W(),slNo:n++,MatlType:(l==null?void 0:l.MatlType)||"",ChangeLogId:((h=l==null?void 0:l.changeLogData)==null?void 0:h.ChangeLogId)??null};o.push(C)})});const s=[...t];return u(as(s)),o};ce.useEffect(()=>{(async()=>{if((x==null?void 0:x.length)>0){const o=await z(x);u(ne({keyName:"Unittype1",data:o}))}})()},[x]);const z=async e=>{const o={};for(const t of e){let n={whseNo:t};try{const s=await new Promise(l=>{var a,C;te(`/${ue}${(C=(a=oe)==null?void 0:a.DEPENDENT_LOOKUPS)==null?void 0:C.UNITTYPE}`,"post",d=>{var h,M;d.statusCode===((h=ae)==null?void 0:h.STATUS_200)?l(d==null?void 0:d.body):(H((M=cs)==null?void 0:M.ERROR_MSG),l([]))},d=>{H(d),l([])},n)});o[t]=s}catch(s){H(s),o[t]=[]}}return o};ce.useEffect(()=>{(async()=>{if((q==null?void 0:q.length)>0){const o=await c(q);u(ne({keyName:"Spproctype",data:o}));const t=await K(q);u(ne({keyName:"MrpCtrler",data:t}))}})()},[q]);const c=async e=>{const o={};for(const t of e){let n={plant:t};try{const s=await new Promise(l=>{var a,C;te(`/${ue}${(C=(a=oe)==null?void 0:a.DATA)==null?void 0:C.GET_SPPROC_TYPE}`,"post",d=>{var h;d.statusCode===((h=ae)==null?void 0:h.STATUS_200)?l(d==null?void 0:d.body):(H("Failed to fetch data"),l([]))},d=>{H(d),l([])},n)});o[t]=s}catch(s){H(s),o[t]=[]}}return o},K=async e=>{const o={};for(const t of e){let n={plant:t};try{const s=await new Promise(l=>{var a,C;te(`/${ue}${(C=(a=oe)==null?void 0:a.DATA)==null?void 0:C.GET_MRP_CONTROLLER_BASED_ON_PLANT}`,"post",d=>{var h;d.statusCode===((h=ae)==null?void 0:h.STATUS_200)?l(d==null?void 0:d.body):(H("Failed to fetch data"),l([]))},d=>{H(d),l([])},n)});o[t]=s}catch(s){H(s),o[t]=[]}}return o};return{fetchDisplayDataRows:Z}},hs=()=>{const u=V(U=>U.payload.unselectedRows||[]);return{createFCRows:U=>U==null?void 0:U.flatMap(r=>r.Toplantdata.map(g=>({...g,FinanceCostingId:r.FinanceCostingId,MassSchedulingId:r.MassSchedulingId,RequestType:r.RequestType,RequestId:r.RequestId,Requester:r.Requester,CreatedOn:r.CreatedOn,Material:r.Material,MatlType:r.MatlType,IntlPoPrice:r.IntlPoPrice,PryVendor:r.PryVendor,FlagForBOM:r.FlagForBOM,VolInEA:r.VolInEA,VolInCA:r.VolInCA,VolInCAR:r.VolInCAR,NoOfUnitForCA:r.NoOfUnitForCA,NoOfUnitForCT:r.NoOfUnitForCT,Torequestheaderdata:r.Torequestheaderdata,Tomaterialerrordata:r.Tomaterialerrordata,id:g==null?void 0:g.FinancePlantId}))),createFCPayload:()=>{const U=u,m=new Map;return U.forEach(g=>{const{FinancePlantId:x,Material:q,IsDeleted:X,Plant:H,FPurStatus:Z,FStdPrice:$,id:_,...E}=g,I={FinancePlantId:x,Material:q,IsDeleted:!0,Plant:H,FPurStatus:Z,FStdPrice:$};m.has(g.FinanceCostingId)?m.get(g.FinanceCostingId).Toplantdata.push(I):m.set(g.FinanceCostingId,{...E,Material:g==null?void 0:g.Material,Toplantdata:[I]})}),Array.from(m.values())}}},rs=()=>{const u=Ie(),[N,B]=ce.useState(!1),[U,m]=ce.useState(null),{getChangeTemplate:r}=Es(),{fetchDisplayDataRows:g}=Is(),{createFCRows:x}=hs(),{customError:q}=Ee(),{showSnackbar:X}=ds();return{getDisplayData:ce.useCallback(async(Z,$,_,E,I)=>new Promise((w,k)=>{B(!0),m(null),u(Ne(!0));const D=Z,z=Je(je.CURRENT_TASK,!0,{}),c=$||(E==null?void 0:E.ATTRIBUTE_2)||(z==null?void 0:z.ATTRIBUTE_2);let K=_?{massCreationId:I!=null&&I.isBifurcated?"":c===i.CREATE||c===i.CREATE_WITH_UPLOAD?D.slice(3):"",massChildCreationId:I!=null&&I.isBifurcated&&(c===i.CREATE||c===i.CREATE_WITH_UPLOAD)?D.slice(3):"",massChangeId:I!=null&&I.isBifurcated?"":c===i.CHANGE||c===i.CHANGE_WITH_UPLOAD?D.slice(3):"",massExtendId:I!=null&&I.isBifurcated?"":c===i.EXTEND||c===i.EXTEND_WITH_UPLOAD?D.slice(3):"",massSchedulingId:I!=null&&I.isBifurcated?"":c===i.FINANCE_COSTING?D.slice(3):"",screenName:c===i.FINANCE_COSTING?"":c,dtName:c===i.FINANCE_COSTING?"":"MDG_MAT_MATERIAL_FIELD_CONFIG",version:c===i.FINANCE_COSTING?"":"v2",page:0,size:c===i.FINANCE_COSTING?100:c===i.CHANGE||c===i.CHANGE_WITH_UPLOAD?10:50,sort:"",ApproverGroup:E==null?void 0:E.ATTRIBUTE_5,Region:"",massChildSchedulingId:I!=null&&I.isBifurcated&&c===i.FINANCE_COSTING?D.slice(3):"",massChildExtendId:I!=null&&I.isBifurcated&&(c===i.EXTEND||c===i.EXTEND_WITH_UPLOAD)?D.slice(3):"",massChildChangeId:I!=null&&I.isBifurcated&&(c===i.CHANGE||c===i.CHANGE_WITH_UPLOAD)?D.slice(3):""}:{massCreationId:"",massChangeId:"",massSchedulingId:c===i.FINANCE_COSTING||c==="Finance Costing"?D.slice(3):"",massExtendId:"",screenName:c==="MASS_CREATE"||c==="Mass Create"||c===i.CREATE?i.CREATE:c===i.FINANCE_COSTING?"":i.CHANGE,dtName:c===i.FINANCE_COSTING?"":"MDG_MAT_MATERIAL_FIELD_CONFIG",version:c===i.FINANCE_COSTING?"":"v2",page:0,size:c===i.FINANCE_COSTING||$===i.FINANCE_COSTING?100:$===i.CHANGE||$===i.CHANGE_WITH_UPLOAD||c===i.CHANGE||c===i.CHANGE_WITH_UPLOAD?10:50,sort:"",ApproverGroup:E==null?void 0:E.ATTRIBUTE_5,Region:"",massChildCreationId:c==="MASS_CREATE"||c==="Mass Create"||c===i.CREATE||c===i.CREATE_WITH_UPLOAD?D.slice(3):"",massChildSchedulingId:"",massChildExtendId:c===i.EXTEND||c===i.EXTEND_WITH_UPLOAD?D.slice(3):"",massChildChangeId:c==="MASS_CHANGE"||c==="Mass Change"||c===i.CHANGE||c===i.CHANGE_WITH_UPLOAD?D.slice(3):""};const e=async t=>{var n,s,l,a,C,d,h,M,L,S,O,P,y,F,J,p,j,G,T,A,Q;try{if((t==null?void 0:t.statusCode)===ae.STATUS_200){u(Ne(!1)),B(!1);const f=t.body;if(u(is(t==null?void 0:t.totalElements)),(t==null?void 0:t.totalPages)===1||(t==null?void 0:t.currentPage)+1===(t==null?void 0:t.totalPages)?(u(we(t==null?void 0:t.totalElements)),u(ns(!0))):u(we(((t==null?void 0:t.currentPage)+1)*(t==null?void 0:t.pageSize))),(E==null?void 0:E.ATTRIBUTE_2)===i.CHANGE||(E==null?void 0:E.ATTRIBUTE_2)===i.CHANGE_WITH_UPLOAD||$===i.CHANGE_WITH_UPLOAD||$===i.CHANGE){u(se({keyName:"requestHeaderData",data:(n=f[0])==null?void 0:n.Torequestheaderdata})),r(((s=f[0])==null?void 0:s.Torequestheaderdata)||"",f[0]||{}),g(f),w(t);return}if($===i.FINANCE_COSTING||(E==null?void 0:E.ATTRIBUTE_2)===i.FINANCE_COSTING){const v={ReqCreatedBy:(a=(l=f[0])==null?void 0:l.Torequestheaderdata)==null?void 0:a.ReqCreatedBy,RequestStatus:(d=(C=f[0])==null?void 0:C.Torequestheaderdata)==null?void 0:d.RequestStatus,Region:(M=(h=f[0])==null?void 0:h.Torequestheaderdata)==null?void 0:M.Region,ReqCreatedOn:new Date().toISOString(),ReqUpdatedOn:new Date().toISOString(),RequestType:(S=(L=f[0])==null?void 0:L.Torequestheaderdata)==null?void 0:S.RequestType,RequestDesc:(P=(O=f[0])==null?void 0:O.Torequestheaderdata)==null?void 0:P.RequestDesc,RequestPriority:(F=(y=f[0])==null?void 0:y.Torequestheaderdata)==null?void 0:F.RequestPriority,LeadingCat:(p=(J=f[0])==null?void 0:J.Torequestheaderdata)==null?void 0:p.LeadingCat,RequestId:(G=(j=f[0])==null?void 0:j.Torequestheaderdata)==null?void 0:G.RequestId,TemplateName:(A=(T=f[0])==null?void 0:T.Torequestheaderdata)==null?void 0:A.TemplateName};u(Ze({data:v}));const ie=await x(f);u(us(ie)),w(t);return}const b=await Ts(f);await u(Cs({data:b==null?void 0:b.payload}));const de=Object.keys(b==null?void 0:b.payload).filter(v=>!isNaN(Number(v))),le={};de.forEach(v=>{le[v]=b==null?void 0:b.payload[v]}),u(Ns((Q=Object.values(le))==null?void 0:Q.map(v=>v.headerData))),w(t)}else X(t==null?void 0:t.message,"error")}catch(f){q(ze.ERROR_GET_DISPLAY_DATA),m(f),B(!1),k(f)}},o=t=>{q(ze.ERROR_FETCHING_DATA),m(t),B(!1),u(Ne(!1)),k(t)};te(`/${ue}/data/displayMassMaterialDTO`,"post",e,o,K)}),[u]),loading:N,error:U,clearError:()=>m(null)}},_s=rs;export{Is as a,hs as b,Es as c,_s as u};
