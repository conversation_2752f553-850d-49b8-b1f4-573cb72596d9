import{q as t,s as h,b as S,r as s,pU as M,a as n,pW as y,aV as N,p_ as L}from"./index-75c1660a.js";import{W as R,c as V,u as f}from"./propData-c6bd838d.js";import{r as k,a as g}from"./userData-010d5077.js";import"./react-beautiful-dnd.esm-db50900e.js";import"./useMediaQuery-33e0a836.js";import"./DialogContentText-ef8524b5.js";import"./CardMedia-f3120f7c.js";import"./Container-754d6379.js";import"./InputAdornment-a22e1655.js";import"./ListItemButton-f13df81b.js";import"./Slider-c4e5ff46.js";import"./Stepper-2dbfb76b.js";import"./StepButton-e06eb73a.js";import"./ToggleButtonGroup-63ceda7a.js";import"./index.esm-93e9b0e6.js";import"./makeStyles-c2a7efc7.js";import"./toConsumableArray-42cf6573.js";import"./asyncToGenerator-88583e02.js";import"./DatePicker-6e8720de.js";import"./Timeline-5c068db1.js";import"./dayjs.min-83c0b0e0.js";import"./isBetween-51871e12.js";import"./CSSTransition-8d766865.js";function se(){let a=t(r=>r.userManagement.userData);const e=t(r=>r.userManagement.taskData),o=t(r=>r.applicationConfig);let i=h();const c=S(),[p,w]=s.useState(null),[C,F]=s.useState(null);s.useState({});const[u,q]=s.useState(null),l={APPLICATION_NAME:"1784",CRUD_API_ENV:"itm",DB_TYPE:"hana",SERVICE_BASE_URL:[{Description:"",Name:"ITMJavaServices",URL:"https://ca-gbd-ca-caf-cw-mdg-iwm.cfapps.us10-001.hana.ondemand.com"},{Description:"",Name:"ConfigServer",URL:"https://cherryworkproducts-config-server.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"WorkNetServices",URL:"https://cherryworkproducts-worknet-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"CrudApiServices",URL:"https://cw-caf-crudapi-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"WorkFormsServices",URL:"https://cherrywork-wf-java-qas.cfapps.eu10-004.hana.ondemand.com/workforms"},{Description:"",Name:"NotificationServices",URL:"https://cherryworkproducts-messaging-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"ITMGraphServices",URL:"https://cherrywork-btp-qas-dashboard.cfapps.eu10-004.hana.ondemand.com"},{Description:"Native Workflow Services",Name:"NativeWorkflowServices",URL:"https://cherryworkproducts-custom-wf-qas.cfapps.eu10-004.hana.ondemand.com"},{Description:"Native Workflow UI URL",Name:"NativeWorkflowUiUrl",URL:"https://cherrywork-native-workflow-qas.cfapps.eu10-004.hana.ondemand.com/native-ui"},{Description:"",Name:"OnboardingServices",URL:"https://cherryworkproducts-itm-java-qas.cfapps.eu10-004.hana.ondemand.com"}]},d={DateTimeFormat:{dateTimeFormat:"DD MMM YYYY||HH:mm",timeZone:"Asia/Calcutta"}};`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`;const m=r=>{i(N(r)),(r==null?void 0:r.processDisplayName)==="Material"&&c(`/requestBench/createRequest?RequestId=${r==null?void 0:r.ATTRIBUTE_1}`),i(L({url:window.location.pathname,module:"ITMWorkbench"})),console.log("task",r)},I=()=>{console.log("fetchFilterView")},U=()=>{console.log("clearFilterView")},D=(r,T)=>{console.log("Success flag.",r),console.log("Task Payload.",T)};return s.useEffect(()=>{M({})},[]),console.log("userdata",k(p),g(C)),n("div",{style:{width:"calc(100vw - 105px)",height:"calc(100vh-130px)"},className:"workspaceOverride",children:n(R,{token:"eyJhbGciOiJSUzI1NiIsImprdSI6Imh0dHBzOi8vY2EtZ2JkLmF1dGhlbnRpY2F0aW9uLnVzMTAuaGFuYS5vbmRlbWFuZC5jb20vdG9rZW5fa2V5cyIsImtpZCI6ImRlZmF1bHQtand0LWtleS1kZjk5ODA5MzZhIiwidHlwIjoiSldUIiwiamlkIjogIitJQzR1dUlwdm93ODJFT2xZZVhwRlJDbGxsR0RpL0t5Mkh4V0Jsck55KzA9In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.tu2ojptF8tG6SU4Yk6JTmF6h6J3hAcoCCzs26kz6H-P1MhYS2qChrYsGWSXffm3wNXuA3elPcXbQ4rnrpPiGZ47I7pfAm7T7d3gw6E-9bYqs8eBoPC5QtRdFCzvMAbZYKXbyCL3v35nXkZsbVHDnXXR-L7hBRejjy-a9wuJ54E5CmNo9j9eMlP0nTL9aBfmW6DvhPK6xn-T_RyiSZwS8RkLk39DZ7FZdLRURGLOj_yGymWgMIIUG0kUkPFGBuNSLhw_kNase6EqEUFljcISOEgM8rNZ8P0Fd3zEwL-O2zuV9VKoPO8442Z80Bl_b6FIVXsIwzCtOaCcNWcYsNTxCfQ",configData:V,destinationData:l,userData:{...a,user_id:a==null?void 0:a.emailId},userPreferences:d,userPermissions:f,userList:{},groupList:{},userListBySystem:u,useWorkAccess:o.environment==="localhost",useConfigServerDestination:o.environment==="localhost",inboxTypeKey:"ADMIN_TASKS",workspaceLabel:"Admin Tasks",workspaceFiltersByAPIDriven:!0,subInboxTypeKey:null,cachingBaseUrl:y,onTaskClick:m,onActionComplete:D,selectedFilterView:null,isFilterView:!1,fetchFilterViewList:I,savedFilterViewData:[],clearFilterView:U,filterViewList:[],selectedTabId:null,userProcess:[]})})}export{se as default};
