import{r as d,b as lo,s as ao,u as co,q as Y,bh as io,a as r,bX as uo,j as g,V as ne,aF as oe,T as v,I as x,aG as me,W as se,x as te,B as re,ar as ln,E as an,X as le,t as T,aC as fe,cb as po,cc as go,z as Co,C as ho,aj as mo,G as k,al as fo,b1 as yo,h as So,F as _,bs as cn,bt as bo,ab as dn,bm as Ao,aD as Mo,aE as vo,b8 as ae,aB as z,K as I,bq as H,eV as G,eX as Io,ai as Lo}from"./index-75c1660a.js";import{d as To}from"./ArrowCircleLeftOutlined-f7b52d40.js";import{l as un}from"./lookup-1dcf10ac.js";import{R as Go}from"./ReusableAttachementAndComments-682b0475.js";import{d as Fo}from"./AttachFileOutlined-872f8e38.js";import{T as Eo}from"./Timeline-5c068db1.js";import{t as No,T as qo,a as ko,b as Bo,c as wo,d as Do}from"./TimelineSeparator-6e03ad1b.js";import"./CloudUpload-d5d09566.js";import"./utilityImages-067c3dc2.js";import"./Add-62a207fb.js";import"./Delete-1d158507.js";import"./clsx-a965ebfb.js";const xo=()=>{var Oe,$e,_e,ze,je,Ue,Ve,We;const[B,$]=d.useState(!0),[pn,Oo]=d.useState("1"),[U,gn]=d.useState([]);d.useState([]);const ce=lo(),ye=ao(),o=co().state,[Se,m]=d.useState(""),[Cn,ie]=d.useState(!1),[_o,f]=d.useState(!1),[hn,h]=d.useState(!1),[mn,y]=d.useState(!1),[zo,S]=d.useState(!0),[fn,b]=d.useState(!1),[yn,be]=d.useState(!1),[Sn,Ae]=d.useState(!1),[bn,Me]=d.useState(!1),[W,ve]=d.useState(""),[de,J]=d.useState(!0),[Ie,Le]=d.useState(!0);d.useState(!0);const[An,Te]=d.useState(!0),[Mn,Ge]=d.useState(!1),[ue,vn]=d.useState([]),[pe,In]=d.useState([]),[Ln,ge]=d.useState(!1),[Tn,L]=d.useState(!1),[K,Gn]=d.useState(""),[Fn,Ce]=d.useState(!1),[P,Fe]=d.useState([]),[En,Nn]=d.useState([]);console.log("mouse",U);let s=Y(e=>e.userManagement.taskData),n=Y(e=>e.userManagement.userData),qn=Y(e=>{var a;return(a=e.userManagement.entitiesAndActivities)==null?void 0:a["General Ledger"]}),he=Y(e=>e.userManagement.taskData);const kn=Y(e=>e.appSettings),X=Y(e=>e.generalLedger.MultipleGLData),[Ee,Bn]=d.useState(0),wn=(e,a)=>{const l=c=>{ye(Lo({keyName:e,data:c.body})),Bn(u=>u+1)},t=c=>{console.log(c)};I(`/${G}/data/${a}`,"get",l,t)},Dn=()=>{var e,a;(a=(e=un)==null?void 0:e.generalLedger)==null||a.map(l=>{wn(l==null?void 0:l.keyName,l==null?void 0:l.endPoint)})},On=()=>{var e,a;Ee==((a=(e=un)==null?void 0:e.generalLedger)==null?void 0:a.length)?$(!1):$(!0)};d.useEffect(()=>{On()},[Ee]),d.useEffect(()=>{Dn()},[]);const N=()=>{ie(!0)},$n=()=>{Ln?(ie(!1),ge(!1)):(ie(!1),ce("/masterDataCockpit/generalLedger"))},q=()=>{be(!0)},Ne=()=>{be(!1)},_n=()=>{},zn=()=>{Ge(!0)},qe=()=>{Ge(!1)},jn=[{field:"id",headerName:"Document ID",flex:1,hide:!0},{field:"docType",headerName:"Type",flex:1},{field:"docName",headerName:"Document Name",flex:1},{field:"uploadedOn",headerName:"Uploaded On",flex:1,align:"center",headerAlign:"center"},{field:"uploadedBy",headerName:"Uploaded By",sortable:!1,flex:1},{field:"action",headerName:"Action",sortable:!1,filterable:!1,align:"center",headerAlign:"center",flex:1,renderCell:e=>g(_,{children:[r(MatView,{index:e.row.id,name:e.row.docName}),r(MatDownload,{index:e.row.id,name:e.row.docName})]})}],Un=()=>{let e=s!=null&&s.subject?s==null?void 0:s.subject:o==null?void 0:o.requestId,a=l=>{var t=[];l.documentDetailDtoList.forEach(c=>{var u={id:c.documentId,docType:c.fileType,docName:c.fileName,uploadedOn:dn(c.docCreationDate).format(kn.date),uploadedBy:c.createdBy};t.push(u)}),vn(t)};I(`/${H}/documentManagement/getDocByRequestId/${e}`,"get",a)},Vn=()=>{let e=s!=null&&s.subject?s==null?void 0:s.subject:o==null?void 0:o.requestId,a=t=>{console.log("commentsdata",t);var c=[];t.body.forEach(u=>{var A={id:u.requestId,comment:u.comment,user:u.createdByUser,createdAt:u.updatedAt};c.push(A)}),In(c),console.log("commentrows",c)},l=t=>{console.log(t)};I(`/${G}/activitylog/fetchTaskDetailsForRequestId?requestId=${e}`,"get",a,l)};d.useEffect(()=>{Un(),Vn(),Gn(io("GL"))},[]);const Wn=()=>{var t,c,u,A;console.log("sdfkhdgkf"),$(!0);let e={};(s==null?void 0:s.processDesc)==="Mass Change"?e={massCreationId:"",massExtendId:"",massChangeId:s!=null&&s.subject?(t=s==null?void 0:s.subject)==null?void 0:t.slice(3):o==null?void 0:o.requestId.slice(3),screenName:"Change"}:(s==null?void 0:s.processDesc)==="Mass Create"?e={massCreationId:s!=null&&s.subject?(c=s==null?void 0:s.subject)==null?void 0:c.slice(3):o==null?void 0:o.requestId.slice(3),massChangeId:"",massExtendId:"",screenName:"Create"}:(o==null?void 0:o.requestType)==="Mass Create"?e={massCreationId:(u=o==null?void 0:o.requestId)==null?void 0:u.slice(3),massChangeId:"",massExtendId:"",screenName:"Create"}:(o==null?void 0:o.requestType)==="Mass Change"&&(e={massCreationId:"",massExtendId:"",massChangeId:(A=o==null?void 0:o.requestId)==null?void 0:A.slice(3),screenName:"Change"});const a=i=>{$(!1),i.body&&ye(Io(i==null?void 0:i.body))},l=i=>{console.log(i)};I(`/${G}/data/displayMassGeneralLedger`,"post",a,l,e)};d.useEffect(()=>{if(X.length===0)Wn();else return},[]);const w=X==null?void 0:X.map((e,a)=>{var c,u,A,i,p,E,O,V,Q,Z;const l=e,t=(e==null?void 0:e.viewData)||{};return{id:a,chartOfAccount:(l==null?void 0:l.ChartOfAccount)||"",companyCode:(l==null?void 0:l.CompCode)||"",glAccount:(l==null?void 0:l.GLAccount)||"",accountType:((u=(c=t["Type/Description"]["Control in COA"])==null?void 0:c.find(M=>(M==null?void 0:M.fieldName)==="Account Type"))==null?void 0:u.value)||"",accountGroup:((i=(A=t["Type/Description"]["Control in COA"])==null?void 0:A.find(M=>(M==null?void 0:M.fieldName)==="Account Group"))==null?void 0:i.value)||"",shortText:((E=(p=t["Type/Description"].Description)==null?void 0:p.find(M=>(M==null?void 0:M.fieldName)==="Short Text"))==null?void 0:E.value)||"",longText:((V=(O=t["Type/Description"].Description)==null?void 0:O.find(M=>(M==null?void 0:M.fieldName)==="Long Text"))==null?void 0:V.value)||"",accountCurrency:((Z=(Q=t["Control Data"]["Account Control in Company Code"])==null?void 0:Q.find(M=>(M==null?void 0:M.fieldName)==="Account Currency"))==null?void 0:Z.value)||""}}),Kn=[{field:"glAccount",headerName:"GL Account",editable:!1,flex:1,renderCell:e=>{const a=P.find(l=>l.generalLedger===e.value);return console.log(a,"isDirectMatch"),console.log(e,"params"),a&&a.code===400?r(v,{sx:{fontSize:"12px",color:"red"},children:e.value}):r(v,{sx:{fontSize:"12px"},children:e.value})}},{field:"chartOfAccount",headerName:"Chart Of Account",editable:!1,flex:1},{field:"companyCode",headerName:"Company Code",editable:!1,flex:1},{field:"accountType",headerName:"Account Type",editable:!1,flex:1},{field:"accountGroup",headerName:"Account Group",editable:!1,flex:1},{field:"shortText",headerName:"Short Text",editable:!1,flex:1,renderCell:e=>{const a=En.includes(e.row.profitCenterName);return r(v,{sx:{fontSize:"12px",color:a?"red":"inherit"},children:e.value})}},{field:"longText",headerName:"Long Text",editable:!1,flex:1},{field:"accountCurrency",headerName:"Account Currency",editable:!1,flex:1}],Xn=e=>{e.length>0?(J(!0),console.log("selectedIds1",e)):J(!1),console.log("selectedIds",e),gn(e)},C=(e,a)=>{const l=e==null?void 0:e.find(t=>(t==null?void 0:t.fieldName)===a);return l?l.value:""};var F=X.map(e=>{var a,l,t,c,u,A,i,p,E,O,V,Q,Z,M,Ke,Xe,Re,Ye,He,Je,Qe,Ze,xe,Pe,en,nn,on,sn,tn,rn;return console.log(e,"xx"),{GeneralLedgerID:(e==null?void 0:e.GeneralLedgeId)??"",Action:(s==null?void 0:s.processDesc)==="Mass Create"?"I":(s==null?void 0:s.processDesc)==="Mass Change"||(o==null?void 0:o.requestType)==="Mass Change"?"U":(o==null?void 0:o.requestType)==="Mass Create"?"I":"",RequestID:"",TaskStatus:"",TaskId:s!=null&&s.taskId?s==null?void 0:s.taskId:"",Remarks:W||"",Info:"",CreationId:"",EditId:"",DeleteId:"",MassCreationId:(s==null?void 0:s.processDesc)==="Mass Create"?(a=s==null?void 0:s.subject)==null?void 0:a.slice(3):(o==null?void 0:o.requestType)==="Mass Create"?o==null?void 0:o.requestId.slice(3):"",MassEditId:(s==null?void 0:s.processDesc)==="Mass Change"?(l=s==null?void 0:s.subject)==null?void 0:l.slice(3):(o==null?void 0:o.requestType)==="Mass Change"?o==null?void 0:o.requestId.slice(3):"",MassDeleteId:"",RequestType:(s==null?void 0:s.processDesc)==="Mass Create"?"Mass Create":(s==null?void 0:s.processDesc)==="Mass Change"||(o==null?void 0:o.requestType)==="Mass Change"?"Mass Change":(o==null?void 0:o.requestType)==="Mass Create"?"Mass Create":"",ReqCreatedBy:(n==null?void 0:n.user_id)??"",ReqCreatedOn:s!=null&&s.createdOn?"/Date("+(s==null?void 0:s.createdOn)+")/":"",ReqUpdatedOn:"",RequestStatus:"",Testrun:de,COA:(e==null?void 0:e.ChartOfAccount)??"",CompanyCode:(e==null?void 0:e.CompCode)??"",CoCodeToExtend:"",GLAccount:(e==null?void 0:e.GLAccount)??"",Accounttype:C((t=e==null?void 0:e.viewData["Type/Description"])==null?void 0:t["Control in COA"],"Account Type"),AccountGroup:C((c=e==null?void 0:e.viewData["Type/Description"])==null?void 0:c["Control in COA"],"Account Group"),GLname:C((u=e==null?void 0:e.viewData["Type/Description"])==null?void 0:u.Description,"Short Text"),Description:C((A=e==null?void 0:e.viewData["Type/Description"])==null?void 0:A.Description,"Long Text"),TradingPartner:C((i=e==null?void 0:e.viewData["Type/Description"])==null?void 0:i["Consolidation Data in COA"],"Trading Partner"),GroupAccNo:C((p=e==null?void 0:e.viewData["Type/Description"])==null?void 0:p["Consolidation Data in COA"],"Group Account Number"),AccountCurrency:C((E=e==null?void 0:e.viewData["Control Data"])==null?void 0:E["Account Control in Company Code"],"Account Currency"),Exchangerate:C((O=e==null?void 0:e.viewData["Control Data"])==null?void 0:O["Account Control in Company Code"],"Exchange Rate Difference Key"),Balanceinlocrcy:C((V=e==null?void 0:e.viewData["Control Data"])==null?void 0:V["Account Control in Company Code"],"Only Balance In Local Currency")===!0?"X":"",Taxcategory:C((Q=e==null?void 0:e.viewData["Control Data"])==null?void 0:Q["Account Control in Company Code"],"Tax Category"),Pstnwotax:C((Z=e==null?void 0:e.viewData["Control Data"])==null?void 0:Z["Account Control in Company Code"],"Posting Without Tax Allowed")===!0?"X":"",ReconAcc:C((M=e==null?void 0:e.viewData["Control Data"])==null?void 0:M["Account Control in Company Code"],"Recon. Account For Account Type"),Valuationgrp:C((Ke=e==null?void 0:e.viewData["Control Data"])==null?void 0:Ke["Account Control in Company Code"],"Valuation Group"),AlterAccno:C((Xe=e==null?void 0:e.viewData["Control Data"])==null?void 0:Xe["Account Control in Company Code"],"Alternative Account Number"),Openitmmanage:C((Re=e==null?void 0:e.viewData["Control Data"])==null?void 0:Re["Account Management in Company Code"],"Open Item Management")===!0?"X":"",Sortkey:C((Ye=e==null?void 0:e.viewData["Control Data"])==null?void 0:Ye["Account Management in Company Code"],"Sort Key"),CostEleCategory:C((He=e==null?void 0:e.viewData["Control Data"])==null?void 0:He["Account Management in Company Code"],"Sort Key"),FieldStsGrp:C((Je=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:Je["Control of Document creation in Company Code"],"Field Status Group"),PostAuto:C((Qe=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:Qe["Control of Document creation in Company Code"],"Post Automatically Only")===!0?"X":"",Supplementautopost:C((Ze=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:Ze["Control of Document creation in Company Code"],"Supplement Auto Postings")===!0?"X":"",Planninglevel:C((xe=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:xe["Bank/Financial Details in Company Code"],"Planning Level"),Relvnttocashflow:C((Pe=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:Pe["Bank/Financial Details in Company Code"],"Relevant To Cash Flows")===!0?"X":"",HouseBank:C((en=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:en["Bank/Financial Details in Company Code"],"House Bank"),AccountId:C((nn=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:nn["Bank/Financial Details in Company Code"],"Account ID"),Interestindicator:C((on=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:on["Interest Calculation Information in Company Code"],"Interest Indicator"),ICfrequency:C((sn=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:sn["Interest Calculation Information in Company Code"],"Interest Calculation Frequency"),KeydateofLIC:C((tn=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:tn["Interest Calculation Information in Company Code"],"Key Date Of Last Interest Calculation"),LastIntrstundate:C((rn=e==null?void 0:e.viewData["Create/Bank/Interest"])==null?void 0:rn["Interest Calculation Information in Company Code"],"Date Of Last Interest Run"),AccmngExistsys:"",Infationkey:"",Tolerancegrp:"",AuthGroup:"",AccountClerk:"",ReconAccReady:"",PostingBlocked:"",PlanningBlocked:""}});console.log(F,"payloadmapping");const Rn=()=>{const e=F;console.log("isLoading1",B),console.log("paylaod",e);const a=t=>{L(!1),t.statusCode===200?(console.log("success"),h("Create"),m(`Mass General Ledger Submitted for Approval with ID NLM${t.body}`),y("success"),S(!1),b(!0),N(),f(!0)):(h("Error"),b(!1),m("Failed Submitting the Mass General Ledger for Approval  "),y("danger"),S(!1),f(!0),q()),handleClose()},l=t=>{console.log(t)};I(`/${G}/massAction/generalLedgersApprovalSubmit`,"post",a,l,e)},Yn=()=>{w.filter((t,c)=>U.includes(c));const e=F;console.log("paylaod",e),console.log("isLoading2",B);const a=t=>{L(!1),t.statusCode===200?(console.log("success"),h("Create"),m(`Mass General Ledger Change Submitted for Approval with ID CGM${t.body}`),y("success"),S(!1),b(!0),N(),f(!0)):(h("Error"),b(!1),m("Failed Submitting Mass General Ledger Data for Approval"),y("danger"),S(!1),f(!0),q())},l=t=>{console.log("error")};I(`/${G}/massAction/changeGeneralLedgersApprovalSubmit`,"post",a,l,e)},Hn=()=>{w.filter((t,c)=>U.includes(c));const e=F;console.log("paylaod",e),console.log("isLoading3",B);const a=t=>{L(!1),t.statusCode===201?(console.log("success"),h("Create"),m("Mass General Ledger Approved & SAP Syndication Completed"),y("success"),S(!1),b(!0),N(),f(!0)):(h("Error"),b(!1),m("Failed Submitting the General Ledger for Approval "),y("danger"),S(!1),f(!0),q()),handleClose()},l=t=>{console.log(t)};I(`/${G}/massAction/createGeneralLedgersApproved`,"post",a,l,e)},Jn=()=>{const e=F;console.log("paylaod",e),console.log("isLoading4",B);const a=t=>{L(!1),t.statusCode===201?(console.log("success"),h("Create"),m("Mass General Ledger Change Approved & SAP Syndication Completed"),y("success"),S(!1),b(!0),N(),f(!0)):(h("Error"),b(!1),m("Failed Approving Mass General Ledger Change"),y("danger"),S(!1),f(!0),q())},l=t=>{console.log("error")};I(`/${G}/massAction/changeGeneralLedgersApproved`,"post",a,l,e)},Qn=()=>{const e=F;console.log("paylaod",e),console.log("isLoading5",B);const a=t=>{if(L(!1),t.statusCode===200){console.log("success"),h("Create"),m(`Mass General Ledger Submitted for Review with ID NLM${t.body}`),y("success"),S(!1),b(!0),N(),f(!0);const c={artifactId:K,createdBy:n==null?void 0:n.emailId,artifactType:"GeneralLedger",requestId:`NLM${t==null?void 0:t.body}`},u=i=>{console.log("Second API success",i)},A=i=>{console.error("Second API error",i)};I(`/${H}/documentManagement/updateDocRequestId`,"post",u,A,c)}else h("Error"),b(!1),m("Failed Submitting the Mass General Ledger for Review"),y("danger"),S(!1),f(!0),q();handleClose()},l=t=>{console.log(t)};I(`/${G}/massAction/generalLedgersSubmitForReview`,"post",a,l,e)},Zn=()=>{const e=F;console.log("paylaod",e),console.log("isLoading5",B);const a=t=>{if(L(!1),t.statusCode===200){console.log("success"),h("Create"),m(`Mass General Ledger Submitted for Review with ID CGM${t.body}`),y("success"),S(!1),b(!0),N(),f(!0);const c={artifactId:K,createdBy:n==null?void 0:n.emailId,artifactType:"GeneralLedger",requestId:`CGM${t==null?void 0:t.body}`},u=i=>{console.log("Second API success",i)},A=i=>{console.error("Second API error",i)};I(`/${H}/documentManagement/updateDocRequestId`,"post",u,A,c)}else h("Error"),b(!1),m("Failed Submitting the Mass General Ledger for Review  "),y("danger"),S(!1),f(!0),q();handleClose()},l=t=>{console.log(t)};I(`/${G}/massAction/changeGeneralLedgersSubmitForReview`,"post",a,l,e)},ke=(e,a)=>{const l=e.target.value;if(l.length>0&&l[0]===" ")ve(l.trimStart());else{let t=l.toUpperCase();ve(t)}},j=()=>{J(!0),Me(!1)},xn=()=>{(n==null?void 0:n.role)==="MDM Steward"&&s.processDesc==="Mass Create"||(n==null?void 0:n.role)==="MDM Steward"&&(o==null?void 0:o.requestType)==="Mass Create"?(L(!0),j(),Rn()):(n==null?void 0:n.role)==="Approver"&&s.processDesc==="Mass Create"||(n==null?void 0:n.role)==="Approver"&&(o==null?void 0:o.requestType)==="Mass Create"?(L(!0),j(),Hn()):(n==null?void 0:n.role)==="Finance"&&s.processDesc==="Mass Create"||(n==null?void 0:n.role)==="Finance"&&(o==null?void 0:o.requestType)==="Mass Create"?(L(!0),j(),Qn()):(n==null?void 0:n.role)==="MDM Steward"&&s.processDesc==="Mass Change"||(n==null?void 0:n.role)==="MDM Steward"&&(o==null?void 0:o.requestType)==="Mass Change"?(L(!0),j(),Yn()):(n==null?void 0:n.role)==="Approver"&&s.processDesc==="Mass Change"||(n==null?void 0:n.role)==="Approver"&&(o==null?void 0:o.requestType)==="Mass Change"?(L(!0),j(),Jn()):((n==null?void 0:n.role)==="Finance"&&s.processDesc==="Mass Change"||(n==null?void 0:n.role)==="Finance"&&(o==null?void 0:o.requestType)==="Mass Change")&&(L(!0),j(),Zn())},R=()=>{J(!1),Me(!0)},D=()=>{Ae(!1)},ee=()=>{J(!1),Ae(!0)},Pn=()=>{console.log("isLoading6",B),w.filter((t,c)=>U.includes(c));const e=F,a=t=>{if($(!1),t.statusCode===200){console.log("success"),h("Create"),m(`General Ledger Submitted for Correction with ID NLS${t.body}`),y("success"),S(!1),b(!0),N(),f(!0);const c={artifactId:K,createdBy:n==null?void 0:n.emailId,artifactType:"GeneralLedger",requestId:`NLS${t==null?void 0:t.body}`},u=i=>{console.log("Second API success",i)},A=i=>{console.error("Second API error",i)};I(`/${H}/documentManagement/updateDocRequestId`,"post",u,A,c)}else h("Error"),b(!1),m("Failed Submitting General Ledger for Correction"),y("danger"),S(!1),f(!0),q();D()},l=t=>{console.log(t)};console.log("remarkssssssssss",W),I(`/${G}/massAction/generalLedgersSendForCorrection`,"post",a,l,e)},eo=()=>{w.filter((t,c)=>U.includes(c));const e=F,a=t=>{if($(!1),t.statusCode===200){console.log("success"),h("Create"),m(`General Ledger Submitted for Correction with ID NLM${t.body}`),y("success"),S(!1),b(!0),N(),f(!0);const c={artifactId:K,createdBy:n==null?void 0:n.emailId,artifactType:"GeneralLedger",requestId:`NLM${t==null?void 0:t.body}`},u=i=>{console.log("Second API success",i)},A=i=>{console.error("Second API error",i)};I(`/${H}/documentManagement/updateDocRequestId`,"post",u,A,c)}else h("Error"),b(!1),m("Failed Submitting General Ledger for Correction"),y("danger"),S(!1),f(!0),q();D()},l=t=>{console.log(t)};console.log("remarkssssssssss",W),I(`/${G}/massAction/changeGeneralLedgersSendForCorrection`,"post",a,l,e)},no=()=>{console.log("isLoading7",B),w.filter((t,c)=>U.includes(c));const e=F,a=t=>{if($(!1),t.statusCode===200){console.log("success"),h("Create"),m(`General Ledger Submitted for Correction with ID NLM${t.body}`),y("success"),S(!1),b(!0),N(),f(!0);const c={artifactId:K,createdBy:n==null?void 0:n.emailId,artifactType:"GeneralLedger",requestId:`NLM${t==null?void 0:t.body}`},u=i=>{console.log("Second API success",i)},A=i=>{console.error("Second API error",i)};I(`/${H}/documentManagement/updateDocRequestId`,"post",u,A,c)}else h("Error"),b(!1),m("Failed Submitting General Ledger for Correction"),y("danger"),S(!1),f(!0),q();D()},l=t=>{console.log(t)};I(`/${G}/massAction/generalLedgersSendForReview`,"post",a,l,e)},oo=()=>{w.filter((t,c)=>U.includes(c));const e=F,a=t=>{$(!1),t.statusCode===200?(console.log("success"),h("Create"),m(`General Ldger Submitted for Correction with ID NGS${t.body}`),y("success"),S(!1),b(!0),N(),f(!0)):(h("Error"),b(!1),m("Failed Submitting General Ledger for Correction"),y("danger"),S(!1),f(!0),q()),D()},l=t=>{console.log(t)};I(`/${G}/massAction/changeGeneralLedgersSendForReview`,"post",a,l,e)},so=()=>{(n==null?void 0:n.role)==="MDM Steward"&&s.processDesc==="Mass Create"||(n==null?void 0:n.role)==="MDM Steward"&&(o==null?void 0:o.requestType)==="Mass Create"?($(!0),D(),Pn()):(n==null?void 0:n.role)==="Approver"&&s.processDesc==="Mass Create"||(n==null?void 0:n.role)==="Approver"&&(o==null?void 0:o.requestType)==="Mass Create"?($(!0),D(),no()):(n==null?void 0:n.role)==="MDM Steward"&&s.processDesc==="Mass Change"||(n==null?void 0:n.role)==="MDM Steward"&&(o==null?void 0:o.requestType)==="Mass Change"?(D(),eo()):((n==null?void 0:n.role)==="Approver"&&s.processDesc==="Mass Change"||(n==null?void 0:n.role)==="Approver"&&(o==null?void 0:o.requestType)==="Mass Change")&&(D(),oo())},Be=()=>{L(!0);let e=F;const a=t=>{t.statusCode===400?(Fe(t.body),Ce(!0),Te(!0),L(!1)):(Te(!1),h("Create"),console.log("success"),h("Create"),m("All Data has been Validated.General Ledger can be Send for Review"),y("success"),S(!1),b(!0),N(),f(!0),ge(!0),L(!1))},l=t=>{console.log(t)};I(`/${G}/massAction/validateMassGeneralLedger`,"post",a,l,e)},we=()=>{L(!0);const e=w.filter((p,E)=>U.includes(E));console.log("selectedData",e);const a=e.map(p=>({...F[p==null?void 0:p.id]}));console.log("selectedProfitCenterRows",a);const l=[];a.map(p=>{var E={glName:p==null?void 0:p.GLname.toUpperCase(),compCode:p==null?void 0:p.CompanyCode};l.push(E)}),console.log("duplicateCheckPayload",l);let t=F;t=a;const c=p=>{p.statusCode===400?(Fe(p.body),Ce(!0),L(!1)):(h("Create"),console.log("success"),h("Create"),m("All Data has been Validated.General Ledger can be Send for Review"),y("success"),S(!1),b(!0),N(),f(!0),ge(!0),(l.glName!==""||l.compCode!=="")&&I(`/${G}/alter/fetchGlNameNCompCodeDupliChkMass`,"post",u,A,l))},u=p=>{if(console.log("dataaaa",p),p.body.length===0||!p.body.some(E=>l.some(O=>{var V;return((V=O==null?void 0:O.glName)==null?void 0:V.toUpperCase())===E.description})))L(!1),Le(!1);else{const E=p.body.map(O=>O.description);L(!1),h("Duplicate Check"),b(!1),m("There is a direct match for the General Ledger name."),y("danger"),S(!1),f(!0),q(),Le(!0),Nn(E)}},A=p=>{console.log(p)},i=p=>{console.log(p)};I(`/${G}/massAction/validateMassGeneralLedger`,"post",c,i,t)},De=()=>{Ce(!1)},to=[{field:"generalLedger",headerName:"General Ledger",editable:!1,flex:1},{field:"error",headerName:"Error",editable:!1,flex:1}],ro=(Oe=P==null?void 0:P.filter(e=>(e==null?void 0:e.code)===400))==null?void 0:Oe.map((e,a)=>{var l;if(e.code===400)return{id:a,generalLedger:e==null?void 0:e.generalLedger,error:(l=e==null?void 0:e.status)==null?void 0:l.message}});return r(_,{children:B===!0?r(uo,{}):g("div",{style:{backgroundColor:"#FAFCFF"},children:[g(ne,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:Sn,onClose:D,children:[g(oe,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[r(v,{variant:"h6",children:"Remarks"}),r(x,{sx:{width:"max-content"},onClick:D,children:r(me,{})})]}),r(se,{sx:{padding:".5rem 1rem"},children:r(te,{children:r(re,{sx:{minWidth:400},children:r(ln,{sx:{height:"auto"},fullWidth:!0,children:r(an,{sx:{backgroundColor:"#F5F5F5"},onChange:ke,value:W,multiline:!0,placeholder:"Enter Remarks for Correction",inputProps:{maxLength:254}})})})})}),g(le,{sx:{display:"flex",justifyContent:"end"},children:[r(T,{sx:{width:"max-content",textTransform:"capitalize"},onClick:D,children:"Cancel"}),r(T,{className:"button_primary--normal",type:"save",onClick:so,variant:"contained",children:"Submit"})]})]}),g(ne,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:bn,onClose:j,children:[g(oe,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[r(v,{variant:"h6",children:"Remarks"}),r(x,{sx:{width:"max-content"},onClick:j,children:r(me,{})})]}),r(se,{sx:{padding:".5rem 1rem"},children:r(te,{children:r(re,{sx:{minWidth:400},children:r(ln,{sx:{height:"auto"},fullWidth:!0,children:r(an,{sx:{backgroundColor:"#F5F5F5"},onChange:ke,value:W,multiline:!0,placeholder:"Enter Remarks",inputProps:{maxLength:254}})})})})}),g(le,{sx:{display:"flex",justifyContent:"end"},children:[r(T,{sx:{width:"max-content",textTransform:"capitalize"},onClick:j,children:"Cancel"}),r(T,{className:"button_primary--normal",type:"save",onClick:xn,variant:"contained",children:"Submit"})]})]}),g(ne,{open:Fn,fullWidth:!0,onClose:De,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[g(oe,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[r(v,{variant:"h6",color:"red",children:"Errors"}),r(x,{sx:{width:"max-content"},onClick:De,children:r(me,{})})]}),r(se,{sx:{padding:".5rem 1rem"},children:r(fe,{isLoading:B,width:"100%",rows:ro,columns:to,pageSize:10,getRowIdValue:"id",hideFooter:!0,checkboxSelection:!1,disableSelectionOnClick:!0,status_onRowSingleClick:!0,stopPropagation_Column:"action",status_onRowDoubleClick:!0})}),r(le,{sx:{display:"flex",justifyContent:"end"}})]}),r(po,{sx:{color:"#fff",zIndex:e=>e.zIndex.drawer+1},open:Tn,children:r(go,{color:"inherit"})}),fn&&r(Co,{openSnackBar:Cn,alertMsg:Se,handleSnackBarClose:$n}),r(ho,{dialogState:yn,openReusableDialog:q,closeReusableDialog:Ne,dialogTitle:hn,dialogMessage:Se,handleDialogConfirm:Ne,dialogOkText:"OK",handleExtraButton:_n,dialogSeverity:mn}),g("div",{style:{...mo,backgroundColor:"#FAFCFF"},children:[r(k,{container:!0,sx:fo,children:g(k,{item:!0,md:12,sx:{display:"flex",marginBottom:"0"},children:[g(k,{item:!0,md:11,sx:{display:"flex"},children:[r(k,{children:r(x,{color:"primary","aria-label":"upload picture",component:"label",sx:yo,children:r(To,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{ce(-1)}})})}),s.processDesc==="Mass Create"?g(k,{children:[r(v,{variant:"h3",children:r("strong",{children:"Create Multiple General Ledger"})}),r(v,{variant:"body2",color:"#777",children:"This view displays list of General Ledgers"})]}):(o==null?void 0:o.requestType)==="Mass Create"?g(k,{children:[r(v,{variant:"h3",children:r("strong",{children:"Create Multiple General Ledger"})}),r(v,{variant:"body2",color:"#777",children:"This view displays list of General Ledgers"})]}):(o==null?void 0:o.requestType)==="Mass Change"?g(k,{children:[r(v,{variant:"h3",children:r("strong",{children:"Change Multiple General Ledger"})}),r(v,{variant:"body2",color:"#777",children:"This view displays list of General Ledgers"})]}):(s==null?void 0:s.processDesc)==="Mass Change"?g(k,{children:[r(v,{variant:"h3",children:r("strong",{children:"Change Multiple General Ledger"})}),r(v,{variant:"body2",color:"#777",children:"This view displays list of General Ledgers"})]}):""]}),r(k,{item:!0,md:1,sx:{display:"flex"},children:r(So,{title:"Uploaded documents",arrow:!0,children:r(x,{onClick:zn,children:r(Fo,{})})})}),g(ne,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:Mn,onClose:qe,children:[r(oe,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:(n==null?void 0:n.role)==="Finance"?r(_,{children:r(v,{variant:"h6",children:"Add Attachment"})}):""}),r(se,{sx:{padding:".5rem 1rem"},children:g(cn,{sx:{padding:"1rem 1rem 0rem 1rem"},children:[(n==null?void 0:n.role)==="Finance"?r(_,{children:r(te,{children:r(re,{sx:{minWidth:400},children:r(Go,{title:"GeneralLedger",useMetaData:!1,artifactId:K,artifactName:"GeneralLedger"})})})}):"",r(k,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:r(v,{variant:"h6",children:r("strong",{children:"Attachments"})})}),!!ue.length&&r(fe,{width:"100%",rows:ue,columns:jn,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action"}),!ue.length&&r(v,{variant:"body2",children:"No Attachments Found"}),r("br",{}),r(v,{variant:"h6",children:"Comments"}),!!pe.length&&r(Eo,{sx:{[`& .${No.root}:before`]:{flex:0,padding:0}},children:pe.map(e=>g(qo,{children:[g(ko,{children:[r(Bo,{children:r(bo,{sx:{color:"#757575"}})}),r(wo,{})]}),r(Do,{sx:{py:"12px",px:2},children:r(cn,{elevation:0,sx:{border:1,borderColor:"#C4C4C4",borderRadius:"8px",width:"650px"},children:r(re,{sx:{padding:"1rem"},children:g(te,{spacing:1,children:[r(k,{sx:{display:"flex",justifyContent:"space-between"},children:r(v,{sx:{textAlign:"right",color:" #757575",fontWeight:"500",fontSize:"12px"},children:dn(e.createdAt).format("DD MMM YYYY")})}),r(v,{sx:{fontSize:"12px",color:" #757575",fontWeight:"500"},children:e.user}),r(v,{sx:{fontSize:"12px",color:"#1D1D1D",fontWeight:"600"},children:e.comment})]})})})})]}))}),!pe.length&&r(v,{variant:"body2",children:"No Comments Found"}),r("br",{})]})}),r(le,{children:r(T,{onClick:qe,children:"Close"})})]})]})}),r(k,{item:!0,sx:{position:"relative"},children:r(fe,{isLoading:B,width:"100%",title:"Mass General Ledger List ("+(w==null?void 0:w.length)+")",rows:w,columns:Kn,pageSize:10,getRowIdValue:"id",hideFooter:!1,checkboxSelection:(n==null?void 0:n.role)==="Finance"&&s.processDesc==="Mass Create"||(n==null?void 0:n.role)==="Finance"&&(o==null?void 0:o.requestType)==="Mass Create"||(n==null?void 0:n.role)==="Finance"&&s.processDesc==="Mass Change"||(n==null?void 0:n.role)==="Finance"&&(o==null?void 0:o.requestType)==="Mass Change",disableSelectionOnClick:!0,status_onRowSingleClick:!0,onRowsSelectionHandler:Xn,callback_onRowSingleClick:e=>{const a=e.row.glAccount,l=X.find(t=>t.GLAccount===a);console.log(l,"yo"),ce(`/masterDataCockpit/generalLedger/massGLTableRequestBench/displayMultipleGLRequestBench/${a}`,{state:{rowData:e.row,requestNumber:o==null?void 0:o.requestId.slice(3),tabsData:l,requestbenchRowData:o}})},stopPropagation_Column:"action",status_onRowDoubleClick:!0})})]}),Ao(qn,"General Ledger","ChangeGL")&&(($e=he==null?void 0:he.itmStatus)==null?void 0:$e.toUpperCase())!=="OPEN"?r(Mo,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:g(vo,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},value:pn,children:[((n==null?void 0:n.role)==="MDM Steward"&&s.processDesc==="Mass Create"||(n==null?void 0:n.role)==="MDM Steward"&&(o==null?void 0:o.requestType)==="Mass Create")&&((_e=s==null?void 0:s.itmStatus)==null?void 0:_e.toUpperCase())!=="OPEN"?g(_,{children:[r(T,{variant:"outlined",size:"small",sx:{button_Outlined:ae,mr:1},onClick:ee,children:"Correction"}),r(T,{variant:"contained",size:"small",sx:{...z},onClick:R,children:"Submit For Approval"})]}):((n==null?void 0:n.role)==="Approver"&&s.processDesc==="Mass Create"||(n==null?void 0:n.role)==="Approver"&&(o==null?void 0:o.requestType)==="Mass Create")&&((ze=s==null?void 0:s.itmStatus)==null?void 0:ze.toUpperCase())!=="OPEN"?g(_,{children:[r(T,{variant:"outlined",size:"small",sx:{button_Outlined:ae,mr:1},onClick:ee,children:"Correction"}),r(T,{variant:"contained",size:"small",sx:{...z,mr:1},onClick:Be,children:"Validate"}),r(T,{variant:"contained",size:"small",sx:{...z},onClick:R,disabled:An,children:"Approve"})]}):((n==null?void 0:n.role)==="Finance"&&s.processDesc==="Mass Create"||(n==null?void 0:n.role)==="Finance"&&(o==null?void 0:o.requestType)==="Mass Create")&&((je=s==null?void 0:s.itmStatus)==null?void 0:je.toUpperCase())!=="OPEN"?g(_,{children:[r(T,{variant:"contained",size:"small",sx:{...z,mr:1},onClick:we,disabled:!de,children:"Validate"}),r(T,{variant:"contained",size:"small",sx:{...z},onClick:R,disabled:Ie,children:"Submit For Review"})]}):"",((n==null?void 0:n.role)==="MDM Steward"&&s.processDesc==="Mass Change"||(n==null?void 0:n.role)==="MDM Steward"&&(o==null?void 0:o.requestType)==="Mass Change")&&((Ue=s==null?void 0:s.itmStatus)==null?void 0:Ue.toUpperCase())!=="OPEN"?g(_,{children:[r(T,{variant:"outlined",size:"small",sx:{button_Outlined:ae,mr:1},onClick:ee,children:"Correction"}),r(T,{variant:"contained",size:"small",sx:{...z},onClick:R,children:"Submit For Approval"})]}):((n==null?void 0:n.role)==="Approver"&&s.processDesc==="Mass Change"||(n==null?void 0:n.role)==="Approver"&&(o==null?void 0:o.requestType)==="Mass Change")&&((Ve=s==null?void 0:s.itmStatus)==null?void 0:Ve.toUpperCase())!=="OPEN"?g(_,{children:[r(T,{variant:"outlined",size:"small",sx:{button_Outlined:ae,mr:1},onClick:ee,children:"Correction"}),r(T,{variant:"contained",size:"small",sx:{...z,mr:1},onClick:Be,children:"Validate"}),r(T,{variant:"contained",size:"small",sx:{...z},onClick:R,children:"Approve"})]}):((n==null?void 0:n.role)==="Finance"&&s.processDesc==="Mass Change"||(n==null?void 0:n.role)==="Finance"&&(o==null?void 0:o.requestType)==="Mass Change")&&((We=s==null?void 0:s.itmStatus)==null?void 0:We.toUpperCase())!=="OPEN"?g(_,{children:[r(T,{variant:"contained",size:"small",sx:{...z,mr:1},onClick:we,disabled:!de,children:"Validate"}),r(T,{variant:"contained",size:"small",sx:{...z},onClick:R,disabled:Ie,children:"Submit For Review"})]}):""]})}):""]})})};export{xo as default};
