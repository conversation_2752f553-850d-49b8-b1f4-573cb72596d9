const d=s=>{let l={};return s==null||s.data.map((e,o)=>{l[e.id]=e}),l},u=s=>{var p;console.log(s);let l={},e=[],o=[];return(p=s==null?void 0:s.data)==null||p.forEach(r=>{r.displayName?e.push(r):o.push(r)}),console.log(o),e==null||e.sort((r,n)=>{var c;return(c=r==null?void 0:r.displayName)==null?void 0:c.localeCompare(n==null?void 0:n.displayName)}),e.concat(o),console.log(e),e==null||e.map(r=>{l[r.userId]=r}),console.log(l),l};export{d as a,u as r};
