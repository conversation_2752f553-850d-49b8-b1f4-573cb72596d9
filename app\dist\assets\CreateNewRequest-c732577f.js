import{q as oe,s as Ro,u as tn,a4 as b,K as Xe,bn as yo,$ as Fe,cW as Pg,ux as qg,bQ as sn,r as d,up as yl,e2 as Je,uy as X,ai as xo,Z as Re,a0 as Jt,uq as wh,ur as tr,us as pi,aT as gr,aU as pr,ui as Tr,di as Dn,a_ as Co,um as er,un as Rh,uo as Ug,m as Bo,n as Go,o as $o,cB as zn,V as cn,l as Be,t as It,gg as kg,h as Ms,j as R,aF as Pn,a as n,T as mt,uz as Hg,I as Ds,ar as Li,bb as Sa,B as ye,bc as Sr,bd as Pr,uA as $u,g8 as Fu,W as Lo,X as Do,a5 as dn,ay as Hn,bF as Er,d5 as Bg,F as Ss,E as xn,cc as Gg,b6 as Pl,Q as _h,b as wr,uB as Ll,aN as Ih,nE as Na,uC as wa,aa as Wu,uD as $g,v as Hl,w as jn,R as Ti,dm as qr,Y as Cn,gs as Eo,z as Un,c1 as Ra,b0 as Vi,uE as Ki,uF as Fg,dG as ql,e4 as vh,dK as Wg,uG as Mh,uH as Oh,uI as ju,cP as Bl,cQ as mr,ag as Ei,uj as hi,pV as jg,uJ as zu,ut as zg,uK as Yg,uL as Xg,e as bn,e3 as Vg,x as bo,ue as ca,uM as Ao,uN as fr,g as xh,A as yh,an as Kg,f as Lh,uO as Yu,uP as xl,uQ as Jg,aX as $n,de as ko,uR as Ji,y as Fn,at as Qg,au as Zg,aC as Di,uS as Pi,b8 as _a,uT as mi,aB as qi,uU as ep,uV as tp,uW as da,uX as sp,uY as op,uk as Is,bO as bs,bK as Ia,uZ as np,aD as Ur,bL as va,bP as Ma,bN as Ul,u_ as rp,cZ as Ui,bR as P,N as lp,aW as ip,u$ as ap,pS as Dh,aE as cp,v0 as go,v1 as Ph,C as ki,aG as Gl,dS as dp,ul as fi,v2 as up,v3 as Cr,aY as Hi,a7 as qh,dB as hp,dz as Xu,v4 as fp,bg as kl,G as vs,b4 as Oa,v5 as gp,v6 as pp,bw as Tp,v7 as yr,a6 as Ep,dg as ua,aI as Uh,v8 as kr,aV as mp,uf as Cp,ae as kh,ug as Ap,uh as bp,v9 as Ci,va as Sp,vb as Np,vc as cr,vd as En,ve as Qi,vf as Zi,vg as Vu,vh as ea,vi as ai,dn as wp,vj as ta,vk as Bi,vl as xa,vm as Rp,vn as Hh,vo as Bh,vp as Gh,vq as sr,vr as _p,dl as Ip,dq as vp,dk as Ku,bl as Ju,vs as xr,eo as Uo,bi as $h,vt as Bn,uu as Oo,vu as Qu,vv as Ai,vw as Mp,vx as Op,vy as Fh,bM as Wh,vz as Ml,eJ as xp,vA as sa,vB as gi,vC as yp,a1 as On,vD as jh,vE as Lp,vF as zh,vG as Zu,vH as Dp,vI as Pp,bT as Gn,a3 as ya,vJ as qp,vK as Up,vL as kp,aH as Hp,vM as eh,bs as ur,br as hr,ge as Bp,vN as Gp,b7 as Yh,vO as $p,vP as th,vQ as Fp,dh as Wp,bq as sh,vR as jp,vS as zp,dv as ci,vT as oa,S as As,vU as Yp,e6 as na,e7 as di,b1 as Xp,vV as Vp,e8 as Kp,e9 as Jp,ea as Qp,vW as oh,vX as Zp,vY as eT,af as tT,vZ as sT,v_ as oT,v$ as nT,w0 as nh,ah as rT,w1 as lT,w2 as iT,bh as aT,w3 as cT,w4 as dT}from"./index-17b8d91e.js";import{F as uT}from"./FilterField-727c53f0.js";import{a as hT,b as Xh,c as fT,u as gT}from"./useDisplayDataDto-fdea4a92.js";import{d as La,F as ha}from"./FilterChangeDropdown-4ee8f82d.js";import{d as Ar,a as pT}from"./DeleteOutlineOutlined-d5f371f3.js";import{d as fa}from"./ArrowCircleLeftOutlined-2a09f8e2.js";import{S as lo}from"./SingleSelectDropdown-29664b58.js";import{d as TT}from"./Add-98854918.js";import{D as ET}from"./DatePicker-68227989.js";import{u as Da}from"./useMaterialFieldConfig-2e883586.js";import{d as Hr}from"./Description-22845efe.js";import{G as Vh}from"./GenericTabs-50c357bb.js";import Kh from"./AdditionalData-070972f8.js";import{m as Jh}from"./makeStyles-213dac7f.js";import{d as mT,a as CT}from"./AttachFile-c72e1c56.js";import{M as AT,a as bT,D as ST}from"./UtilDoc-d76e2af6.js";import{R as NT}from"./ReusableAttachementAndComments-bab6bbfc.js";import{T as wT}from"./Timeline-36ba02ac.js";import{T as RT,a as _T,b as IT,c as vT,d as MT}from"./TimelineSeparator-0839d5e3.js";import{d as OT,a as xT}from"./Settings-286513ab.js";import{d as yT}from"./Download-796d54cf.js";import{a as LT,d as rh,C as DT}from"./ChangeLog-0f47d713.js";import{d as PT}from"./CloudDownload-26349cbd.js";import{d as qT}from"./CloudUpload-27b6d63e.js";import{A as UT}from"./AttachmentUploadDialog-d151e36a.js";import{a as kT,S as HT}from"./Stepper-88e4fb0c.js";import{S as BT}from"./StepButton-34497717.js";import"./useChangeLogUpdate-f322f7d1.js";import"./AutoCompleteType-9336ea79.js";import"./dayjs.min-ce01f2c7.js";import"./AdapterDayjs-1a4a6504.js";import"./isBetween-3aeee754.js";import"./dateViewRenderers-34586552.js";import"./useSlotProps-e34e1e13.js";import"./InputAdornment-5b0053c5.js";import"./CSSTransition-30917e2c.js";import"./useMediaQuery-6a073ac5.js";import"./useMobilePicker-9978caff.js";import"./DesktopDatePicker-07c19cde.js";import"./useCustomDtCall-15db5725.js";import"./GenericViewGeneral-68ecd4bc.js";import"./Edit-1992ad62.js";import"./DeleteOutline-30de3a49.js";import"./toConsumableArray-c7e4bd84.js";/* empty css            */import"./FileDownloadOutlined-c800f30b.js";import"./VisibilityOutlined-315d5644.js";import"./DeleteOutlined-888bfc33.js";import"./Slider-3eb7e770.js";import"./utilityImages-067c3dc2.js";import"./Delete-9f4d7a45.js";import"./clsx-a965ebfb.js";const Qh=()=>{const e=oe(u=>u.payload.payloadData),t=oe(u=>u.applicationConfig),r=oe(u=>{var se;return(se=u.userManagement)==null?void 0:se.taskData}),o=Ro(),c=tn(),m=new URLSearchParams(c.search).get("RequestType");return{getRequestHeaderTemplate:()=>{var q,g,V;let u={decisionTableId:null,decisionTableName:"MDG_MAT_REQUEST_HEADER_CONFIG",version:"v2",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_SCENARIO":(r==null?void 0:r.ATTRIBUTE_2)===((q=b)==null?void 0:q.FINANCE_COSTING)?(g=b)==null?void 0:g.FINANCE_COSTING:m||(e==null?void 0:e.RequestType)||((V=b)==null?void 0:V.CREATE),"MDG_CONDITIONS.MDG_MAT_REGION":(e==null?void 0:e.Region)||"US","MDG_CONDITIONS.MDG_MODULE":"Material"}],systemFilters:null,systemOrders:null,filterString:null};const se=Q=>{var H,he;if(Q.statusCode===200){const f={"Header Data":((he=(H=Q==null?void 0:Q.data)==null?void 0:H.result[0])==null?void 0:he.MDG_MAT_REQUEST_HEADER_CONFIG).sort((z,j)=>z.MDG_MAT_SEQUENCE_NO-j.MDG_MAT_SEQUENCE_NO).map(z=>({fieldName:z.MDG_MAT_UI_FIELD_NAME,sequenceNo:z.MDG_MAT_SEQUENCE_NO,fieldType:z.MDG_MAT_FIELD_TYPE,maxLength:z.MDG_MAT_MAX_LENGTH,value:z.MDG_MAT_DEFAULT_VALUE,visibility:z.MDG_MAT_VISIBILITY,jsonName:z.MDG_MAT_JSON_FIELD_NAME}))};o(Pg({tab:"Request Header",data:f})),o(qg(f))}},ie=Q=>{console.log(Q)};t.environment==="localhost"?Xe(`/${yo}${Fe.INVOKE_RULES.LOCAL}`,"post",se,ie,u):Xe(`/${yo}${Fe.INVOKE_RULES.PROD}`,"post",se,ie,u)}}},Zh=()=>{const e=oe(p=>p.paginationData),t=oe(p=>p.payload.changeFieldRows),r=oe(p=>p.payload.whseList),o=oe(p=>p.payload.matNoList),c=oe(p=>p.payload.plantList),s=oe(p=>p.payload.changeFieldRowsDisplay),m=oe(p=>p.payload.selectedRows),l=Ro(),{customError:u}=sn(),[se,ie]=d.useState({errorText:!1,errorTextMessage:""}),q=async(p,T)=>{var S,L,A,O,k,U,x,N,D,I,ge,ee,Ue,M,Se,ce,$,qe,F,Ke,Us,Wt,$s,Fs,ss,io,Ws,ks,Qt,fs,Ht,ns,Zt,xs,lt,He,Rt,Ys,Bt,Me,it,Ut,Ns,ds,rs,at,yt,ws;l(yl(!0));let v,C;return p===((S=Je)==null?void 0:S.LOGISTIC)?(C={materialNo:(T==null?void 0:T[(L=X)==null?void 0:L.MATERIAL_NUM])||"",division:(T==null?void 0:T[(A=X)==null?void 0:A.DIVISION])||"",plant:(T==null?void 0:T[(O=X)==null?void 0:O.PLANT])||"",page:e==null?void 0:e.page,size:e==null?void 0:e.size},v=`/${Re}/${(U=(k=Fe)==null?void 0:k.CHG_DISPLAY_REQUESTOR)==null?void 0:U.LOGISTIC}`):p===((x=Je)==null?void 0:x.ITEM_CAT)?(C={materialNo:(T==null?void 0:T[(N=X)==null?void 0:N.MATERIAL_NUM])||"",salesOrg:(T==null?void 0:T[(D=X)==null?void 0:D.SALES_ORG])||"",distrChan:(T==null?void 0:T[(I=X)==null?void 0:I.DIST_CHNL])||"",division:(T==null?void 0:T[(ge=X)==null?void 0:ge.DIVISION])||"",page:e==null?void 0:e.page,size:e==null?void 0:e.size},v=`/${Re}/${(Ue=(ee=Fe)==null?void 0:ee.CHG_DISPLAY_REQUESTOR)==null?void 0:Ue.SALES}`):p===((M=Je)==null?void 0:M.MRP)?(C={materialNo:(T==null?void 0:T[(Se=X)==null?void 0:Se.MATERIAL_NUM])||"",mrpCtrler:(T==null?void 0:T[(ce=X)==null?void 0:ce.MRP_CTRLER])||"",plant:(T==null?void 0:T[($=X)==null?void 0:$.PLANT])||"",division:(T==null?void 0:T[(qe=X)==null?void 0:qe.DIVISION])||"",page:e==null?void 0:e.page,size:e==null?void 0:e.size},v=`/${Re}/${(Ke=(F=Fe)==null?void 0:F.CHG_DISPLAY_REQUESTOR)==null?void 0:Ke.MRP}`):p===((Us=Je)==null?void 0:Us.UPD_DESC)?(C={materialNo:(T==null?void 0:T[(Wt=X)==null?void 0:Wt.MATERIAL_NUM])||"",division:(T==null?void 0:T[($s=X)==null?void 0:$s.DIVISION])||"",plant:(T==null?void 0:T[(Fs=X)==null?void 0:Fs.PLANT])||"",page:e==null?void 0:e.page,size:e==null?void 0:e.size},v=`/${Re}/${(io=(ss=Fe)==null?void 0:ss.CHG_DISPLAY_REQUESTOR)==null?void 0:io.DESC}`):p===((Ws=Je)==null?void 0:Ws.WARE_VIEW_2)?(C={materialNo:(T==null?void 0:T[(ks=X)==null?void 0:ks.MATERIAL_NUM])||"",whseNo:(T==null?void 0:T[(Qt=X)==null?void 0:Qt.WAREHOUSE])||"",plant:(T==null?void 0:T[(fs=X)==null?void 0:fs.PLANT])||"",division:(T==null?void 0:T[(Ht=X)==null?void 0:Ht.DIVISION])||"",page:e==null?void 0:e.page,size:e==null?void 0:e.size},v=`/${Re}/${(Zt=(ns=Fe)==null?void 0:ns.CHG_DISPLAY_REQUESTOR)==null?void 0:Zt.WAREHOUSE}`):p===((xs=Je)==null?void 0:xs.CHG_STAT)?(C={materialNo:(T==null?void 0:T[(lt=X)==null?void 0:lt.MATERIAL_NUM])||"",salesOrg:(T==null?void 0:T[(He=X)==null?void 0:He.SALES_ORG])||"",distrChan:(T==null?void 0:T[(Rt=X)==null?void 0:Rt.DIST_CHNL])||"",division:(T==null?void 0:T[(Ys=X)==null?void 0:Ys.DIVISION])||"",page:e==null?void 0:e.page,size:e==null?void 0:e.size},v=`/${Re}/${(Me=(Bt=Fe)==null?void 0:Bt.CHG_DISPLAY_REQUESTOR)==null?void 0:Me.CHG_STATUS}`):p===((it=Je)==null?void 0:it.SET_DNU)&&(C={materialNo:(T==null?void 0:T[(Ut=X)==null?void 0:Ut.MATERIAL_NUM])||"",salesOrg:(T==null?void 0:T[(Ns=X)==null?void 0:Ns.SALES_ORG])||"",distrChan:(T==null?void 0:T[(ds=X)==null?void 0:ds.DIST_CHNL])||"",division:(T==null?void 0:T[(rs=X)==null?void 0:rs.DIVISION])||"",plant:(T==null?void 0:T[(at=X)==null?void 0:at.PLANT])||"",page:e==null?void 0:e.page,size:e==null?void 0:e.size},v=`/${Re}/${(ws=(yt=Fe)==null?void 0:yt.CHG_DISPLAY_REQUESTOR)==null?void 0:ws.SET_DNU}`),new Promise((Hs,os)=>{Xe(v,"post",Ye=>{var Lt,ps,Js,Bs,st,Ts,to,St,es;l(wh(Ye==null?void 0:Ye.totalElements)),(Ye==null?void 0:Ye.totalPages)===1||(Ye==null?void 0:Ye.currentPage)+1===(Ye==null?void 0:Ye.totalPages)?(l(tr(Ye==null?void 0:Ye.totalElements)),l(pi(!0))):l(tr(((Ye==null?void 0:Ye.currentPage)+1)*(Ye==null?void 0:Ye.pageSize)));const Mt=p===((Lt=Je)==null?void 0:Lt.LOGISTIC)?g(Ye==null?void 0:Ye.body):p===((ps=Je)==null?void 0:ps.ITEM_CAT)?V(Ye==null?void 0:Ye.body):p===((Js=Je)==null?void 0:Js.MRP)?Q(Ye==null?void 0:Ye.body):p===((Bs=Je)==null?void 0:Bs.UPD_DESC)?J(Ye==null?void 0:Ye.body):p===((st=Je)==null?void 0:st.WARE_VIEW_2)?ne(Ye==null?void 0:Ye.body):p===((Ts=Je)==null?void 0:Ts.CHG_STAT)?H(Ye==null?void 0:Ye.body):p===((to=Je)==null?void 0:to.SET_DNU)?he(Ye==null?void 0:Ye.body):[];if(Array.isArray(Mt))l(gr([...t,...Mt])),l(pr({...s,[e==null?void 0:e.page]:Mt}));else if(typeof Mt=="object"&&Mt!==null){const ot={...t};(St=Object==null?void 0:Object.keys(Mt))==null||St.forEach(Dt=>{ot[Dt]=[...ot[Dt]||[],...Mt[Dt]]}),l(gr(ot)),l(pr({...s,[e==null?void 0:e.page]:Mt}))}l(yl(!1));let gs;if(Array.isArray(Mt))gs=Mt==null?void 0:Mt.map(ot=>ot==null?void 0:ot.id),l(Tr([...m,...gs]));else if(typeof Mt=="object"&&Mt!==null){gs=Object.keys(Mt).reduce((Dt,Rs)=>{var ae;return Dt[Rs]=((ae=Mt[Rs])==null?void 0:ae.map($e=>$e==null?void 0:$e.id))||[],Dt},{});const ot={...m};(es=Object==null?void 0:Object.keys(gs))==null||es.forEach(Dt=>{ot[Dt]=[...ot[Dt]||[],...gs[Dt]]}),l(Tr(ot))}Hs(Ye==null?void 0:Ye.body)},()=>{var Ye;l(yl(!1)),os(new Error((Ye=Dn)==null?void 0:Ye.ERROR_MSG))},C)})},g=p=>{const T=[];let v=1;const C=new Set;return p.forEach(S=>{S.ToLogisticdata.forEach(L=>{C.add(L.Material);const A={...L,id:Co(),slNo:v++,MatlType:(S==null?void 0:S.MatlType)||""};T.push(A)})}),l(er([...o,...C])),T},V=p=>{const T=[];let v=1;const C=new Set;return p.forEach(S=>{S.Tosalesdata.forEach(L=>{C.add(L.Material);const A={...L,id:Co(),slNo:v++,MatlType:(S==null?void 0:S.MatlType)||""};T.push(A)})}),l(er([...o,...C])),T},Q=p=>{const T={"Basic Data":[],"Plant Data":[]};let v=1,C=1;const S=new Set,L=new Set;return p.forEach(A=>{const{Tomrpupdate:O,ToBasicdata:k,Material:U,MatlType:x}=A;S.add(U),T["Basic Data"].push({...k,id:Co(),slNo:v++,type:"Basic Data",MatlType:x}),O.forEach(N=>{L.add(N==null?void 0:N.Plant),T["Plant Data"].push({...N,id:Co(),slNo:C++,type:"Plant Data"})})}),l(er([...o,...S])),l(Rh([...c,...L])),T},H=p=>{const T={"Basic Data":[],"Plant Data":[],"Sales Data":[]};let v=1,C=1,S=1;const L=new Set;return p.forEach(A=>{const{Tosalesdata:O,ToBasicdata:k,Toplantdata:U,Material:x,MatlType:N}=A;L.add(x),T["Basic Data"].push({...k,id:Co(),slNo:v++,type:"Basic Data",MatlType:N}),U==null||U.forEach(D=>{T["Plant Data"].push({...D,id:Co(),slNo:C++,type:"Plant Data"})}),O==null||O.forEach(D=>{T["Sales Data"].push({...D,id:Co(),slNo:S++,type:"Sales Data"})})}),l(er([...o,...L])),T},he=p=>{const T={"Basic Data":[],"Plant Data":[],"Sales Data":[],Description:[]};let v=1,C=1,S=1,L=1;const A=new Set;return p.forEach(O=>{const{Tosalesdata:k,ToBasicdata:U,Toplantdata:x,Tomaterialdescription:N,Material:D,MatlType:I}=O;A.add(D),T["Basic Data"].push({...U,id:Co(),slNo:v++,type:"Basic Data",MatlType:I}),x==null||x.forEach(ge=>{T["Plant Data"].push({...ge,id:Co(),slNo:C++,type:"Plant Data"})}),k==null||k.forEach(ge=>{T["Sales Data"].push({...ge,id:Co(),slNo:S++,type:"Sales Data"})}),N==null||N.forEach(ge=>{T.Description.push({...ge,id:Co(),slNo:L++,type:"Description"})})}),l(er([...o,...A])),T},ne=p=>{const T=[],v=new Set;let C=1;const S=new Set;p.forEach(A=>{A.ToWarehousedata.forEach(O=>{v.add(O.WhseNo),S.add(O.Material);const k={...O,id:Co(),slNo:C++,MatlType:(A==null?void 0:A.MatlType)||""};T.push(k)})});const L=[...v];return l(Ug(L)),l(er([...o,...S])),T},J=p=>{const T=[];let v=1;const C=new Set;return p.forEach(S=>{S.Tomaterialdescription.forEach(L=>{C.add(L.Material);const A={...L,id:Co(),slNo:v++,MatlType:(S==null?void 0:S.MatlType)||""};T.push(A)})}),l(er([...o,...C])),T};d.useEffect(()=>{(async()=>{if((r==null?void 0:r.length)>0){const T=await f(r);l(xo({keyName:"Unittype1",data:T}))}})()},[r]);const f=async p=>{const T={};for(const v of p){let C={whseNo:v};try{const S=await new Promise(L=>{var A,O;Xe(`/${Re}${(O=(A=Fe)==null?void 0:A.DEPENDENT_LOOKUPS)==null?void 0:O.UNITTYPE}`,"post",k=>{var U;k.statusCode===((U=Jt)==null?void 0:U.STATUS_200)?L(k==null?void 0:k.body):(u("Failed to fetch data"),L([]))},k=>{u(k),L([])},C)});T[v]=S}catch(S){u(S),T[v]=[]}}return T};d.useEffect(()=>{(async()=>{if((c==null?void 0:c.length)>0){const T=await z(c);l(xo({keyName:"Spproctype",data:T}));const v=await j(c);l(xo({keyName:"MrpCtrler",data:v}))}})()},[c]);const z=async p=>{const T={};for(const v of p){let C={plant:v};try{const S=await new Promise(L=>{var A,O;Xe(`/${Re}${(O=(A=Fe)==null?void 0:A.DATA)==null?void 0:O.GET_SPPROC_TYPE}`,"post",k=>{var U;k.statusCode===((U=Jt)==null?void 0:U.STATUS_200)?L(k==null?void 0:k.body):(u("Failed to fetch data"),L([]))},k=>{u(k),L([])},C)});T[v]=S}catch(S){u(S),T[v]=[]}}return T},j=async p=>{const T={};for(const v of p){let C={plant:v};try{const S=await new Promise(L=>{var A,O;Xe(`/${Re}${(O=(A=Fe)==null?void 0:A.DATA)==null?void 0:O.GET_MRP_CONTROLLER_BASED_ON_PLANT}`,"post",k=>{var U;k.statusCode===((U=Jt)==null?void 0:U.STATUS_200)?L(k==null?void 0:k.body):(u("Failed to fetch data"),L([]))},k=>{u(k),L([])},C)});T[v]=S}catch(S){u(S),T[v]=[]}}return T};return{fetchDisplayDataRequestor:q,errorState:se}};var Pa={},GT=Go;Object.defineProperty(Pa,"__esModule",{value:!0});var ga=Pa.default=void 0,$T=GT(Bo()),FT=$o;ga=Pa.default=(0,$T.default)((0,FT.jsx)("path",{d:"M11 16h2v2h-2zm1-14C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8m0-14c-2.21 0-4 1.79-4 4h2c0-1.1.9-2 2-2s2 .9 2 2c0 2-3 1.75-3 5h2c0-2.25 3-2.5 3-5 0-2.21-1.79-4-4-4"}),"HelpOutlineTwoTone");const WT=zn(cn)(({theme:e})=>{var t,r,o,c;return{"& .MuiPaper-root":{borderRadius:"12px",boxShadow:"0 4px 20px rgba(0, 0, 0, 0.1)",border:`1px solid ${(r=(t=Be)==null?void 0:t.placeholder)==null?void 0:r.color}`,backgroundColor:(c=(o=Be)==null?void 0:o.primary)==null?void 0:c.white,maxWidth:"600px"}}}),jT=zn(It)(({theme:e})=>{var t,r,o,c;return{borderRadius:"8px",padding:"1.2rem 1rem !important",backgroundColor:(r=(t=Be)==null?void 0:t.primary)==null?void 0:r.lightPlus,"&:hover":{backgroundColor:(c=(o=Be)==null?void 0:o.info)==null?void 0:c.dark,boxShadow:"0 2px 8px rgba(25, 118, 210, 0.3)"},transition:"all 0.2s ease-in-out",textTransform:"none",fontWeight:500}}),lh=zn(kg)(({theme:e})=>{var t,r;return{borderRadius:"6px",backgroundColor:(r=(t=Be)==null?void 0:t.secondary)==null?void 0:r.lightYellow,display:"flex",alignItems:"center","& .MuiAlert-icon":{display:"flex",alignItems:"center",justifyContent:"center"},marginTop:"1rem"}}),ih=zn(Ms)({maxWidth:"none"}),ef=({onDownloadTypeChange:e,open:t,downloadType:r,handleDownloadTypeChange:o,onClose:c})=>{var m,l,u,se,ie,q;const s=r==="systemGenerated"?(m=dn)==null?void 0:m.SYSTEM_GENERATED_MSG:(l=dn)==null?void 0:l.EMAIL_DELIVERY_MSG;return R(WT,{open:t,onClose:c,children:[R(Pn,{sx:{backgroundColor:(se=(u=Be)==null?void 0:u.success)==null?void 0:se.light,padding:"1rem 1.5rem",borderBottom:`1px solid ${(q=(ie=Be)==null?void 0:ie.primary)==null?void 0:q.whiteSmoke}`,display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[n(mt,{variant:"h6",sx:{fontWeight:600,color:"#333",letterSpacing:"0.2px"},children:"Select Download Option"}),n(Ds,{size:"small",onClick:c,children:n(Hg,{fontSize:"small"})})]}),n(Lo,{sx:{padding:"1.5rem"},children:R(Li,{component:"fieldset",sx:{width:"100%"},children:[R(Sa,{"aria-label":"download-option",name:"download-option",value:r,onChange:o,sx:{display:"flex",flexDirection:"row",gap:2,alignItems:"center"},children:[R(ye,{sx:{flex:1,padding:"0.4rem",borderRadius:"6px",backgroundColor:r==="systemGenerated"?"#f0f4ff":"#ffffff",border:r==="systemGenerated"?"1px solid #1976d2":"1px solid #e0e0e0",transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:"#f7f9fc"},display:"flex",alignItems:"center",justifyContent:"space-between"},children:[n(Sr,{value:"systemGenerated",control:n(Pr,{color:"primary",size:"small"}),label:R(ye,{sx:{display:"flex",alignItems:"center",gap:.5},children:[n($u,{sx:{mr:1,color:"#1976d2"}}),n(mt,{sx:{fontWeight:500,color:"#333",fontSize:"0.85rem"},children:"System-Generated"})]}),sx:{flexGrow:1,margin:0}}),n(ih,{title:n("span",{style:{whiteSpace:"nowrap",fontSize:"12px"},children:"Download Excel file instantly"}),arrow:!0,placement:"top",children:n(ga,{sx:{color:"#1976d2",fontSize:"1.1rem",verticalAlign:"middle",mr:1}})})]}),R(ye,{sx:{flex:1,padding:"0.4rem",borderRadius:"8px",backgroundColor:r==="mailGenerated"?"#f0f4ff":"#ffffff",border:r==="mailGenerated"?"1px solid #1976d2":"1px solid #e0e0e0",transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:"#f7f9fc"},display:"flex",alignItems:"center",justifyContent:"space-between"},children:[n(Sr,{value:"mailGenerated",control:n(Pr,{color:"primary",size:"small"}),label:R(ye,{sx:{display:"flex",alignItems:"center",gap:.5},children:[n(Fu,{sx:{mr:1,color:"#1976d2"}}),n(mt,{sx:{fontWeight:500,color:"#333",fontSize:"0.85rem"},children:"Mail-Generated"})]}),sx:{flexGrow:1,margin:0}}),n(ih,{title:n("span",{style:{whiteSpace:"nowrap",fontSize:"12px"},children:"Receive the Excel file via email"}),arrow:!0,placement:"top",children:n(ga,{sx:{color:"#1976d2",fontSize:"1.1rem",verticalAlign:"middle",mr:1}})})]})]}),n(lh,{severity:"info",children:n(mt,{sx:{fontSize:"0.9rem",color:"#555"},children:s[0]})}),n(lh,{severity:"info",children:n(mt,{sx:{fontSize:"0.9rem",color:"#555"},children:s[1]})})]})}),n(Do,{sx:{padding:"0 1.5rem 1.5rem"},children:n(jT,{variant:"contained",onClick:e,startIcon:r==="systemGenerated"?n($u,{}):n(Fu,{}),children:r==="systemGenerated"?"Download":"Send Email"})})]})},tf=({param:e,mandatory:t=!1,dropDownData:r,allDropDownData:o,selectedValues:c,inputState:s,handleSelectAll:m,handleSelectionChange:l,handleMatInputChange:u,handleScroll:se,dropdownRef:ie,errors:q={},formatOptionLabel:g,handlePopoverOpen:V,handlePopoverClose:Q,handleMouseEnterPopover:H,handleMouseLeavePopover:he,isPopoverVisible:ne,popoverId:J,popoverAnchorEl:f,popoverRef:z,popoverContent:j,isMaterialNum:p=!1,isLoading:T=!1,isSelectAll:v=!1,singleSelect:C=!1,hasMoreItems:S=!0,totalCount:L=0,loadedCount:A=0})=>{const O=()=>{const x=p?(r==null?void 0:r[e==null?void 0:e.key])||[]:(r==null?void 0:r[e==null?void 0:e.key])||(o==null?void 0:o[e==null?void 0:e.key])||[];return v&&x.length>0&&!C?["Select All",...x]:x},k=()=>{if(!C)return c[e.key]||[];const x=c[e.key];return Array.isArray(x)&&x.length>0?x[0]:null},U=x=>{!S&&p||se(x)};return n(Pl,{multiple:!C,disableListWrap:!0,options:O(),getOptionLabel:x=>typeof x=="string"?x:x==="Select All"?"Select All":g(x),value:C?k():c[e.key]||[],inputValue:p&&!C?s==null?void 0:s.code:void 0,onChange:(x,N)=>{!C&&N.includes("Select All")?m(e.key,O().filter(D=>D!=="Select All")):C?l(e.key,N?[N]:[]):l(e.key,N)},disableCloseOnSelect:!C,ListboxProps:{onScroll:U,ref:ie},renderOption:(x,N,{selected:D})=>{var ee;const ge=N==="Select All"?((ee=c[e.key])==null?void 0:ee.length)===O().filter(Ue=>Ue!=="Select All").length:D;return R("li",{...x,style:{display:"flex",alignItems:"center",width:"100%",cursor:"pointer"},children:[!C&&n(Hn,{checked:ge,sx:{marginRight:1}}),typeof N=="string"?n("span",{style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},title:N,children:N}):R("span",{style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},title:`${N==null?void 0:N.code}${N!=null&&N.desc?` - ${N==null?void 0:N.desc}`:""}`,children:[n("strong",{children:N==null?void 0:N.code}),N!=null&&N.desc?` - ${N==null?void 0:N.desc}`:""]})]})},renderTags:(x,N)=>{if(C)return null;const D=x.map(I=>typeof I=="string"?I:g(I)).join("<br />");return x.length>1?R(Ss,{children:[n(Er,{sx:{height:25,fontSize:"0.85rem",".MuiChip-label":{padding:"0 6px"}},label:`${g(x[0])}`,...N({index:0})}),n(Er,{sx:{height:25,fontSize:"0.85rem",".MuiChip-label":{padding:"0 6px"}},label:`+${x.length-1}`,onMouseEnter:I=>V(I,D),onMouseLeave:Q}),n(Bg,{id:J,open:ne,anchorEl:f,onClose:Q,anchorOrigin:{vertical:"bottom",horizontal:"center"},transformOrigin:{vertical:"top",horizontal:"center"},onMouseEnter:H,onMouseLeave:he,ref:z,sx:{"& .MuiPopover-paper":{backgroundColor:Be.primary.whiteSmoke,boxShadow:"0px 4px 8px rgba(0, 0, 0, 0.1)",borderRadius:"8px",padding:"10px",fontSize:"0.875rem",color:Be.blue.main,border:"1px solid #ddd"}},children:n(ye,{sx:{maxHeight:"270px",overflowY:"auto",padding:"5px"},dangerouslySetInnerHTML:{__html:j}})})]}):x.map((I,ge)=>n(Er,{sx:{height:25,fontSize:"0.85rem",".MuiChip-label":{padding:"0 6px"}},label:`${g(I)}`,...N({index:ge})}))},renderInput:x=>{var N,D;return n(xn,{...x,label:t?R(Ss,{children:[R("strong",{children:["Select ",e.key]})," ",n("span",{style:{color:(D=(N=Be)==null?void 0:N.error)==null?void 0:D.dark},children:"*"})]}):`Select ${e.key}`,variant:"outlined",error:!!q[e.key],helperText:q[e.key],onChange:u||void 0,InputProps:{...x.InputProps,endAdornment:R(Ss,{children:[T?n(Gg,{size:20,sx:{mr:1}}):null,p&&L>0&&R(ye,{component:"span",sx:{mr:1,fontSize:"0.75rem",color:"text.secondary"},children:[A,"/",L]}),x.InputProps.endAdornment]})},sx:{"& .MuiOutlinedInput-root":{borderRadius:"8px",height:50,boxShadow:"0 2px 4px rgba(0, 0, 0, 0.1)"},"& .MuiInputLabel-root":{fontWeight:500}}})}},e.key)},zT=d.forwardRef(function(t,r){return n(_h,{direction:"down",ref:r,...t})}),YT=({open:e,onClose:t,parameters:r,templateName:o,setShowTable:c,allDropDownData:s})=>{var Pe,_t,jt,_s,Ft,te,xe;const[m,l]=d.useState({}),[u,se]=d.useState({}),[ie,q]=d.useState({}),[g,V]=d.useState(""),[Q,H]=d.useState(!1),[he,ne]=d.useState("success"),[J,f]=d.useState(!1),[z,j]=d.useState(""),[p,T]=d.useState(""),[v,C]=d.useState(!1),[S,L]=d.useState("systemGenerated"),[A,O]=d.useState([]),[k,U]=d.useState(""),[x,N]=d.useState(!1),D=oe(B=>B.payload.payloadData),I=oe(B=>B.request.requestHeader.requestId),ge=oe(B=>B.payload.dataLoading),ee=oe(B=>B.request.salesOrgDTData),[Ue,M]=d.useState({}),[Se,ce]=d.useState({[X.MATERIAL_NUM]:!1,[X.PLANT]:!1,[X.SALES_ORG]:!1,[X.DIVISION]:!1,[X.DIST_CHNL]:!1,[X.WAREHOUSE]:!1,[X.STORAGE_LOC]:!1,[X.MRP_CTRLER]:!1}),[$,qe]=d.useState(0),[F,Ke]=d.useState({code:"",desc:""}),[Us,Wt]=d.useState(null),$s=d.useRef(null),[Fs,ss]=d.useState(!1),[io,Ws]=d.useState(null),[ks,Qt]=d.useState(""),[fs,Ht]=d.useState(!1),ns=d.useRef(null),Zt=wr(),xs=Ro(),{fetchDisplayDataRequestor:lt}=Zh(),[He,Rt]=d.useState(0),[Ys,Bt]=d.useState(null),[Me,it]=d.useState([]),[Ut,Ns]=d.useState(0),[ds,rs]=d.useState(0),at=d.useRef(),yt=()=>{var B;(B=at==null?void 0:at.current)==null||B.click()},ws=(Pe=Ll[D==null?void 0:D.TemplateName])==null?void 0:Pe.map(B=>({field:B.key,headerName:B.key,editable:!0,flex:2})),Hs=200,os=d.useCallback(B=>{B.preventDefault();const Te=(B.clipboardData||window.clipboardData).getData("Text").trim().split(`
`).map((We,le)=>{const dt=We.split("	"),ze={id:le+1};return ws.forEach((Qe,Ct)=>{ze[Qe.field]=dt[Ct]||""}),ze});it(Te)},[]);d.useEffect(()=>{if(He===1)return document.addEventListener("paste",os),()=>{document.removeEventListener("paste",os)}},[He,os]);const ls=(B,de)=>{Rt(de),He===1&&Bt("handlePasteMaterialData")};`& .${Na.tooltip}`+"";const ao={convertJsonToExcel:()=>{let B=[];ws==null||ws.forEach(de=>{de.headerName.toLowerCase()!=="action"&&!de.hide&&B.push({header:de.headerName,key:de.field})}),Ra({fileName:"Material Data",columns:B,rows:Me}),rs(1)}},Ye=(B,de)=>{Ws(B.currentTarget),Qt(de),Ht(!0)},Mt=()=>{Ht(!1)},gs=()=>{Ht(!0)},Lt=()=>{Ht(!1)},Js=!!io?"custom-popover":void 0,Bs=(B,de)=>{l(w=>({...w,[B]:de})),de.length>0&&q(w=>({...w,[B]:""}))};d.useEffect(()=>{se(Ts(m)),xs(wa(Ts(m)))},[m]),d.useEffect(()=>{if(Me){let B=to(Me);l(B)}},[Me]);const st=(B,de)=>{var Te;const w=((Te=m[B])==null?void 0:Te.length)===de.length;l(We=>({...We,[B]:w?[]:de})),w||q(We=>({...We,[B]:""}))},Ts=B=>{const de={};for(const w in B)B.hasOwnProperty(w)&&(de[w]=B[w].map(Te=>Te.code).join(","));return de},to=B=>{const de={};return B.forEach(w=>{Object.keys(w).forEach(Te=>{Te!=="id"&&w[Te].trim()!==""&&(de[Te]||(de[Te]=[]),de[Te].push({code:w[Te].trim()}))})}),de},St=B=>{const de=Ki[B]||[],w=de==null?void 0:de.filter(Te=>!u[Te]||u[Te].trim()==="");return w.length>0?(N(!0),U(dn.MANDATORY_FILTER_MD(w.join(", "))),!1):!0},es=async()=>{if(St(o))try{const B=await lt(o,u);B&&B.length>0?(N(!1),c(!0)):(N(!0),U("No data found for the selected criteria."))}catch{N(!0),U("Error fetching data.")}},ot=()=>{var We,le,dt;T("Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience."),V(!0),t();let B=((We=Ll[D==null?void 0:D.TemplateName])==null?void 0:We.map(ze=>ze.key))||[],de={};He===0?de={materialDetails:[B.reduce((ze,Qe)=>(ze[Qe]=u!=null&&u[Qe]?u==null?void 0:u[Qe]:"",ze),{})],templateHeaders:D!=null&&D.FieldName?(le=D.FieldName)==null?void 0:le.join("$^$"):"",requestId:I||(D==null?void 0:D.RequestId)||"",templateName:D!=null&&D.TemplateName?D.TemplateName:"",dtName:"MDG_MAT_CHANGE_TEMPLATE",version:"v5",rolePrefix:""}:de={materialDetails:[B.reduce((ze,Qe)=>(ze[Qe]=Me.map(Ct=>{var _e;return(_e=Ct[Qe])==null?void 0:_e.trim()}).filter(Ct=>Ct!=="").join(",")||"",ze),{})],templateHeaders:D!=null&&D.FieldName?(dt=D.FieldName)==null?void 0:dt.join("$^$"):"",requestId:I||(D==null?void 0:D.RequestId)||"",templateName:D!=null&&D.TemplateName?D.TemplateName:"",dtName:"MDG_MAT_CHANGE_TEMPLATE",version:"v5",rolePrefix:""};const w=ze=>{var _e;if((ze==null?void 0:ze.size)==0){V(!1),T(""),Vi((_e=dn)==null?void 0:_e.NO_DATA_FOUND,"error",{position:"top-center",largeWidth:!0}),setTimeout(()=>{var ut;Zt((ut=Eo)==null?void 0:ut.REQUEST_BENCH)},2600);return}const Qe=URL.createObjectURL(ze),Ct=document.createElement("a");Ct.href=Qe,Ct.setAttribute("download",`${D.TemplateName}_Mass Change.xlsx`),document.body.appendChild(Ct),Ct.click(),document.body.removeChild(Ct),URL.revokeObjectURL(Qe),V(!1),T(""),H(!0),j(`${D.TemplateName}_Mass Change.xlsx has been downloaded successfully.`),ne("success"),Rs(),setTimeout(()=>{var ut;Zt((ut=Eo)==null?void 0:ut.REQUEST_BENCH)},2600)},Te=()=>{var ze;V(!1),T(""),Vi((ze=dn)==null?void 0:ze.ERR_DOWNLOADING_EXCEL,"error",{position:"top-center"}),setTimeout(()=>{var Qe;Zt((Qe=Eo)==null?void 0:Qe.REQUEST_BENCH)},2600)};Xe(`/${Re}/excel/downloadExcelWithData`,"postandgetblob",w,Te,de)},Dt=()=>{var We,le,dt;V(!0),t();let B=((We=Ll[D==null?void 0:D.TemplateName])==null?void 0:We.map(ze=>ze.key))||[],de={};He===0?de={materialDetails:[B.reduce((ze,Qe)=>(ze[Qe]=u!=null&&u[Qe]?u==null?void 0:u[Qe]:"",ze),{})],templateHeaders:D!=null&&D.FieldName?(le=D.FieldName)==null?void 0:le.join("$^$"):"",requestId:I||(D==null?void 0:D.RequestId)||"",templateName:D!=null&&D.TemplateName?D.TemplateName:"",dtName:"MDG_MAT_CHANGE_TEMPLATE",version:"v4",rolePrefix:""}:de={materialDetails:[B.reduce((ze,Qe)=>(ze[Qe]=Me.map(Ct=>{var _e;return(_e=Ct[Qe])==null?void 0:_e.trim()}).filter(Ct=>Ct!=="").join(",")||"",ze),{})],templateHeaders:D!=null&&D.FieldName?(dt=D.FieldName)==null?void 0:dt.join("$^$"):"",requestId:I||(D==null?void 0:D.RequestId)||"",templateName:D!=null&&D.TemplateName?D.TemplateName:"",dtName:"MDG_MAT_CHANGE_TEMPLATE",version:"v4",rolePrefix:""};const w=()=>{V(!1),T(""),H(!0),j("Download has been started. You will get the Excel file via email."),ne("success"),Rs(),setTimeout(()=>{var ze;Zt((ze=Eo)==null?void 0:ze.REQUEST_BENCH)},2600)},Te=()=>{V(!1),H(!0),j("Oops! Something went wrong. Please try again later."),ne("danger"),Rs(),setTimeout(()=>{var ze;Zt((ze=Eo)==null?void 0:ze.REQUEST_BENCH)},2600)};Xe(`/${Re}/excel/downloadExcelWithDataInMail`,"postandgetblob",w,Te,de)},Rs=()=>{f(!0)},ae=()=>{f(!1)},$e=()=>{C(!0)},me=()=>{C(!1),L("systemGenerated")},Ne=B=>{var de;L((de=B==null?void 0:B.target)==null?void 0:de.value)},Ve=()=>{S==="systemGenerated"&&(ot(),me()),S==="mailGenerated"&&(Dt(),me())};d.useEffect(()=>{var w;const{[(w=X)==null?void 0:w.MATERIAL_NUM]:B,...de}=m||{};de&&Object.keys(de).length>0&&(Ke({code:"",desc:""}),ct("",!0))},[JSON.stringify({...m,[X.MATERIAL_NUM]:void 0})]);const Oe=B=>{var Te;const de=(Te=B.target.value)==null?void 0:Te.toUpperCase();Ke({code:de,desc:""}),qe(0),Us&&clearTimeout(Us);const w=setTimeout(()=>{ct(de,!0)},500);Wt(w)},ct=(B="",de=!1)=>{var ys,Xs,uo,us,co,Po,Qs,Ot,Gs,Zs,_o,Fo,Io,ho,is,Wo,so,qo,jo,zo,Yo,Xo,pe,ht,ft,At,ve,Nt,$t,kt,as,qs,gt,Gt;ce(De=>({...De,[X.MATERIAL_NUM]:!0}));const w=((Xs=m[(ys=X)==null?void 0:ys.DIVISION])==null?void 0:Xs.map(De=>De==null?void 0:De.code).join("$^$"))||"",Te=((us=m[(uo=X)==null?void 0:uo.PLANT])==null?void 0:us.map(De=>De==null?void 0:De.code).join("$^$"))||"",We=((Po=m[(co=X)==null?void 0:co.SALES_ORG])==null?void 0:Po.map(De=>De==null?void 0:De.code).join("$^$"))||((Qs=ee==null?void 0:ee.uniqueSalesOrgList)==null?void 0:Qs.map(De=>De.code).join("$^$"))||"",le=((Gs=m[(Ot=X)==null?void 0:Ot.DIST_CHNL])==null?void 0:Gs.map(De=>De==null?void 0:De.code).join("$^$"))||"",dt=((_o=m[(Zs=X)==null?void 0:Zs.MRP_CTRLER])==null?void 0:_o.map(De=>De==null?void 0:De.code).join("$^$"))||"",ze=((Io=m[(Fo=X)==null?void 0:Fo.WAREHOUSE])==null?void 0:Io.map(De=>De==null?void 0:De.code).join("$^$"))||"";let Qe="",Ct={materialNo:B??"",salesOrg:We,top:Hs,skip:de?0:$};switch(o){case((ho=Je)==null?void 0:ho.LOGISTIC):Qe=(Wo=(is=Fe)==null?void 0:is.MAT_SEARCH_APIS)==null?void 0:Wo.LOGISTIC,Ct={...Ct,division:w,plant:Te};break;case((so=Je)==null?void 0:so.MRP):Qe=(jo=(qo=Fe)==null?void 0:qo.MAT_SEARCH_APIS)==null?void 0:jo.MRP,Ct={...Ct,division:w,plant:Te,mrpCtlr:dt};break;case((zo=Je)==null?void 0:zo.ITEM_CAT):Qe=(Xo=(Yo=Fe)==null?void 0:Yo.MAT_SEARCH_APIS)==null?void 0:Xo.SALES,Ct={...Ct,division:w,salesOrg:We,distrChan:le};break;case((pe=Je)==null?void 0:pe.WARE_VIEW_2):Qe=(ft=(ht=Fe)==null?void 0:ht.MAT_SEARCH_APIS)==null?void 0:ft.WAREHOUSE,Ct={...Ct,division:w,plant:Te,whseNo:ze};break;case((At=Je)==null?void 0:At.CHG_STAT):Qe=(Nt=(ve=Fe)==null?void 0:ve.MAT_SEARCH_APIS)==null?void 0:Nt.CHG_STATUS,Ct={...Ct,division:w,salesOrg:We,distrChan:le};break;case(($t=Je)==null?void 0:$t.SET_DNU):Qe=(as=(kt=Fe)==null?void 0:kt.MAT_SEARCH_APIS)==null?void 0:as.SET_DNU,Ct={...Ct,division:w,salesOrg:We,distrChan:le,plant:Te};break;case((qs=Je)==null?void 0:qs.UPD_DESC):Qe=(Gt=(gt=Fe)==null?void 0:gt.MAT_SEARCH_APIS)==null?void 0:Gt.DESC,Ct={...Ct,division:w,plant:Te};break;default:return}const _e=De=>{(De==null?void 0:De.statusCode)===Jt.STATUS_200?((De==null?void 0:De.count)!==void 0&&Ns(De==null?void 0:De.count),de?(O(De==null?void 0:De.body),M(pt=>{var ms;return{...pt,[(ms=X)==null?void 0:ms.MATERIAL_NUM]:De.body}}),qe(0)):(O(pt=>[...pt,...De==null?void 0:De.body]),M(pt=>{var ms,gn;return{...pt,[(ms=X)==null?void 0:ms.MATERIAL_NUM]:[...pt[(gn=X)==null?void 0:gn.MATERIAL_NUM]||[],...De.body]}}))):(De==null?void 0:De.statusCode)===Jt.STATUS_414&&(Vi(De==null?void 0:De.message,"error"),O([]),M(pt=>{var ms;return{...pt,[(ms=X)==null?void 0:ms.MATERIAL_NUM]:[]}}),Ns(0)),ce(pt=>({...pt,[X.MATERIAL_NUM]:!1})),ss(!1)},ut=()=>{ss(!1),ce(De=>({...De,[X.MATERIAL_NUM]:!1}))};ss(!0),Xe(`/${Re}${Qe}`,"post",_e,ut,Ct)},ke=B=>{const{scrollTop:de,scrollHeight:w,clientHeight:Te}=B.target;de+Te>=w-10&&!Fs&&!Se[X.MATERIAL_NUM]&&A.length<Ut&&qe(We=>We+Hs)};d.useEffect(()=>{$>0&&ct(F==null?void 0:F.code,!1)},[$]),d.useEffect(()=>{r==null||r.forEach(B=>{var de,w;B.key===((de=X)==null?void 0:de.SALES_ORG)?M(Te=>({...Te,[B.key]:(ee==null?void 0:ee.uniqueSalesOrgList)||[]})):B.key===((w=X)==null?void 0:w.PLANT)&&M(Te=>({...Te,[B.key]:(ee==null?void 0:ee.uniquePlantList)||[]}))})},[r]),d.useEffect(()=>{var B,de;if(((B=ee==null?void 0:ee.salesOrgData)==null?void 0:B.length)>0&&!m[(de=X)==null?void 0:de.SALES_ORG]){M(Te=>{var We;return{...Te,[(We=X)==null?void 0:We.SALES_ORG]:(ee==null?void 0:ee.uniqueSalesOrgList)||[]}});const w=Wu(ee==null?void 0:ee.uniqueSalesOrgList,ee);M(Te=>{var We;return{...Te,[(We=X)==null?void 0:We.PLANT]:w}})}},[ee]),d.useEffect(()=>{var B,de,w,Te,We,le,dt,ze,Qe,Ct;if(m[(B=X)==null?void 0:B.SALES_ORG]&&m[(de=X)==null?void 0:de.SALES_ORG].length===0&&(m[(w=X)==null?void 0:w.DIST_CHNL]=[],m[(Te=X)==null?void 0:Te.PLANT]=[]),o===((We=Je)==null?void 0:We.SET_DNU)&&(M(_e=>{var ut;return{..._e,[(ut=X)==null?void 0:ut.PLANT]:[]}}),M(_e=>{var ut;return{..._e,[(ut=X)==null?void 0:ut.DIST_CHNL]:[]}})),(o===((le=Je)==null?void 0:le.ITEM_CAT)||o===((dt=Je)==null?void 0:dt.CHG_STAT))&&M(_e=>{var ut;return{..._e,[(ut=X)==null?void 0:ut.DIST_CHNL]:[]}}),m[(ze=X)==null?void 0:ze.SALES_ORG]&&m[(Qe=X)==null?void 0:Qe.SALES_ORG].length>0){Vt();const _e=Wu(m[(Ct=X)==null?void 0:Ct.SALES_ORG],ee);M(ut=>{var ys;return{...ut,[(ys=X)==null?void 0:ys.PLANT]:_e}})}},[m[(_t=X)==null?void 0:_t.SALES_ORG]]),d.useEffect(()=>{var B,de,w,Te,We,le,dt;if(m[(B=X)==null?void 0:B.PLANT]&&m[(de=X)==null?void 0:de.PLANT].length===0&&(m[(w=X)==null?void 0:w.MRP_CTRLER]=[],m[(Te=X)==null?void 0:Te.WAREHOUSE]=[],M(ze=>{var Qe;return{...ze,[(Qe=X)==null?void 0:Qe.MRP_CTRLER]:[]}}),M(ze=>{var Qe;return{...ze,[(Qe=X)==null?void 0:Qe.WAREHOUSE]:[]}})),m[(We=X)==null?void 0:We.PLANT]&&m[(le=X)==null?void 0:le.PLANT].length>0){Es();const ze=$g(m[(dt=X)==null?void 0:dt.PLANT],ee);M(Qe=>{var Ct;return{...Qe,[(Ct=X)==null?void 0:Ct.WAREHOUSE]:ze}})}},[m[(jt=X)==null?void 0:jt.PLANT]]);const Vt=()=>{var Te,We,le;ce(dt=>({...dt,[X.DIST_CHNL]:!0}));let B={salesOrg:m[(Te=X)==null?void 0:Te.SALES_ORG]?(le=m[(We=X)==null?void 0:We.SALES_ORG])==null?void 0:le.map(dt=>dt==null?void 0:dt.code).join("$^$"):""};const de=dt=>{M(ze=>{var Qe;return{...ze,[(Qe=X)==null?void 0:Qe.DIST_CHNL]:dt.body}}),ce(ze=>({...ze,[X.DIST_CHNL]:!1}))},w=dt=>{ce(ze=>({...ze,[X.DIST_CHNL]:!1}))};Xe(`/${Re}${Fe.DATA.GET_DISTRCHNL}`,"post",de,w,B)},Es=()=>{var Te,We,le;ce(dt=>({...dt,[X.MRP_CTRLER]:!0}));let B={plant:m[(Te=X)==null?void 0:Te.PLANT]?(le=m[(We=X)==null?void 0:We.PLANT])==null?void 0:le.map(dt=>dt==null?void 0:dt.code).join("$^$"):""};const de=dt=>{M(ze=>{var Qe;return{...ze,[(Qe=X)==null?void 0:Qe.MRP_CTRLER]:dt.body}}),ce(ze=>({...ze,[X.MRP_CTRLER]:!1}))},w=dt=>{ce(ze=>({...ze,[X.MRP_CTRLER]:!1}))};Xe(`/${Re}${Fe.DATA.GET_MRP_CONTROLLER_BASED_ON_PLANT}`,"post",de,w,B)},Pt=B=>{var w,Te,We,le,dt,ze,Qe,Ct,_e,ut,ys,Xs,uo,us;const de=co=>co.code&&co.desc?`${co.code} - ${co.desc}`:co.code||"";if(B.key===((w=X)==null?void 0:w.MATERIAL_NUM))return n(tf,{param:B,mandatory:(We=(Te=Ki)==null?void 0:Te[o])==null?void 0:We.includes(B==null?void 0:B.key),dropDownData:Ue,allDropDownData:s,selectedValues:m,inputState:F,handleSelectAll:st,handleSelectionChange:Bs,handleMatInputChange:Oe,handleScroll:ke,dropdownRef:$s,errors:ie,formatOptionLabel:de,handlePopoverOpen:Ye,handlePopoverClose:Mt,handleMouseEnterPopover:gs,handleMouseLeavePopover:Lt,isPopoverVisible:fs,popoverId:Js,popoverAnchorEl:io,popoverRef:ns,popoverContent:ks,isMaterialNum:!0,isLoading:Se[X.MATERIAL_NUM],singleSelect:(o===((le=Je)==null?void 0:le.LOGISTIC)||(D==null?void 0:D.TemplateName)===((dt=Je)==null?void 0:dt.LOGISTIC))&&(D==null?void 0:D.RequestType)===((ze=b)==null?void 0:ze.CHANGE),hasMoreItems:A.length<Ut,totalCount:Ut,loadedCount:A.length});if(B.key===((Qe=X)==null?void 0:Qe.PLANT)||B.key===((Ct=X)==null?void 0:Ct.SALES_ORG)||B.key===((_e=X)==null?void 0:_e.MRP_CTRLER)||B.key===((ut=X)==null?void 0:ut.DIVISION)||B.key===((ys=X)==null?void 0:ys.WAREHOUSE)||B.key===((Xs=X)==null?void 0:Xs.DIST_CHNL))return n(ha,{param:B,mandatory:(us=(uo=Ki)==null?void 0:uo[o])==null?void 0:us.includes(B==null?void 0:B.key),dropDownData:Ue,allDropDownData:s,selectedValues:m,handleSelectAll:st,handleSelectionChange:Bs,errors:ie,formatOptionLabel:de,handlePopoverOpen:Ye,handlePopoverClose:Mt,handleMouseEnterPopover:gs,handleMouseLeavePopover:Lt,isPopoverVisible:fs,popoverId:Js,popoverAnchorEl:io,popoverRef:ns,popoverContent:ks,isMaterialNum:!1,isLoading:Se[B.key],isSelectAll:!0})},je=async B=>{const de=B.target.files[0];if(!de)return;const Te=(await Fg(de)).map((We,le)=>{const dt={id:le+1};return ws.forEach((ze,Qe)=>{dt[ze.field]=We[ze.field]||""}),dt});it(Te),B.target.value=null};return R(Ss,{children:[R(cn,{open:e,TransitionComponent:zT,keepMounted:!0,onClose:()=>{},maxWidth:He===1?"md":"sm",fullWidth:!0,children:[R(ye,{sx:{backgroundColor:"#e3f2fd",padding:"1rem 1.5rem",display:"flex",alignItems:"center"},children:[n(La,{color:"primary",sx:{marginRight:"0.5rem"}}),R(mt,{variant:"h6",component:"div",color:"primary",children:[o," Search Filter(s)"]})]}),R(ye,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[R(Hl,{value:He,onChange:ls,sx:{borderBottom:1,borderColor:"divider"},children:[n(jn,{label:"Search Filter"}),n(jn,{label:"Copy Material"})]}),He===1&&R(ye,{sx:{display:"flex",gap:1,marginLeft:"auto",pr:2},children:[n(Ms,{title:"Export Table",children:n(Ds,{onClick:ao.convertJsonToExcel,children:n(Ti,{iconName:"Download"})})}),n(Ms,{title:"Upload Excel",disabled:!ds,children:n(Ds,{onClick:yt,children:n(Ti,{iconName:"Upload"})})}),n("input",{type:"file",accept:".xlsx, .xls",ref:at,style:{display:"none"},onChange:je})]})]}),R(Lo,{sx:{padding:"1.5rem 1.5rem 1rem"},children:[He===0&&n(Ss,{children:r==null?void 0:r.map(B=>n(ye,{sx:{marginBottom:"1rem"},children:Pt(B)},B.key))}),He===1&&n(ye,{children:n(qr,{style:{height:400,width:"100%"},rows:Me,columns:ws})}),x&&R(mt,{variant:"h6",color:(Ft=(_s=Be)==null?void 0:_s.error)==null?void 0:Ft.dark,children:["* ",k]}),n(Cn,{blurLoading:ge})]}),R(Do,{sx:{padding:"0.5rem 1.5rem",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[n(mt,{variant:"caption",sx:{color:"text.secondary",fontWeight:"bold"},children:"Note: Please choose other Mandatory fields before selecting Material Number"}),R(ye,{sx:{display:"flex",gap:1},children:[n(It,{onClick:()=>{var B,de;if((D==null?void 0:D.RequestType)===((B=b)==null?void 0:B.CHANGE)){Zt((de=Eo)==null?void 0:de.REQUEST_BENCH),t();return}t()},color:"error",variant:"outlined",sx:{height:36,minWidth:"3.5rem",textTransform:"none",borderColor:"#cc3300",fontWeight:500},children:"Cancel"}),(D==null?void 0:D.RequestType)!==((te=b)==null?void 0:te.CHANGE_WITH_UPLOAD)&&n(It,{onClick:es,variant:"contained",sx:{height:36,minWidth:"3.5rem",backgroundColor:"#3B30C8",textTransform:"none",fontWeight:500,"&:hover":{backgroundColor:"#2c278f"}},children:"OK"}),(D==null?void 0:D.RequestType)===((xe=b)==null?void 0:xe.CHANGE_WITH_UPLOAD)&&n(It,{onClick:()=>{St(o)&&$e()},variant:"contained",sx:{height:36,minWidth:"3.5rem",backgroundColor:"#3B30C8",textTransform:"none",fontWeight:500,"&:hover":{backgroundColor:"#2c278f"}},children:"Download"})]})]})]}),n(ef,{onDownloadTypeChange:Ve,open:v,downloadType:S,handleDownloadTypeChange:Ne,onClose:me}),n(Cn,{blurLoading:g,loaderMessage:p}),Q&&n(Un,{openSnackBar:J,alertMsg:z,alertType:he,handleSnackBarClose:ae})]})},XT=(e,t,r,o,c,s,m,l,u,se)=>{const ie=Ro();return{handleObjectChangeFieldRows:(g,V,Q,H,he)=>{var j;const ne=e[g].map(p=>(p==null?void 0:p.id)===V?{...p,[Q]:H}:p);ie(gr({...e,[g]:ne}));const J=(j=t==null?void 0:t[r==null?void 0:r.page])==null?void 0:j[g].map(p=>(p==null?void 0:p.id)===V?{...p,[Q]:H}:p);ie(pr({...t,[r==null?void 0:r.page]:{...t==null?void 0:t[r==null?void 0:r.page],[g]:J}}));const f=ql(o,g),z=ql(vh,f);ne==null||ne.forEach(p=>{if((p==null?void 0:p.id)===V){const T={ObjectNo:`${p==null?void 0:p.Material}${(z==null?void 0:z.length)>0?`$$${p[z[0]]}`:""}${(z==null?void 0:z.length)>1?`$$${p[z[1]]}`:""}`,ChangedBy:c.emailId,ChangedOn:Wg,FieldName:he??Q,PreviousValue:u(p,Q,g,z)??"-",SAPValue:u(p,Q,g,z)??"-",CurrentValue:H??"",tableName:g};l(T);const v={RequestId:s||(se==null?void 0:se.slice(3)),changeLogId:(p==null?void 0:p.ChangeLogId)??null},C=[...m,T];Object.entries(o).forEach(([A,O])=>{const k=C.filter(U=>U.tableName===A);k.length>0&&(v[O]||(v[O]=[]),k.forEach(U=>{const{tableName:x,...N}=U;v[O].push(N)}))});const S=Object.values(o);let L={RequestId:v.RequestId,changeLogId:v.changeLogId};S.forEach(A=>{const O=Mh(v,A),{RequestId:k,changeLogId:U,...x}=O;Object.entries(x).forEach(([N,D])=>{L[N]||(L[N]={}),L[N]={...L[N],...D}})}),ie(Oh(L))}})}}},Gi=()=>{const e=Ro(),t=tn(),{fetchDisplayDataRows:r}=hT(),{fetchDisplayDataRequestor:o}=Zh(),{createFCRows:c}=Xh(),s=oe(j=>{var p;return(p=j.userManagement)==null?void 0:p.taskData}),m=oe(j=>j.paginationData),l=oe(j=>j.payload.fcRows),u=oe(j=>j.payload.changeFieldRowsDisplay),se=oe(j=>j.request.materialRows),ie=oe(j=>j.payload.payloadData),q=oe(j=>j.payload.requestorPayload),g=new URLSearchParams(t.search),V=g.get("RequestId"),Q=g.get("RequestType"),H=g.get("reqBench"),he=t.state,{customError:ne}=sn(),J=async(j,p)=>{var T,v;if(j===((T=ju)==null?void 0:T.DISPLAY)){if(u[m==null?void 0:m.page]){(m==null?void 0:m.totalElements)>((m==null?void 0:m.page)+1)*(m==null?void 0:m.size)?e(tr(((m==null?void 0:m.page)+1)*(m==null?void 0:m.size))):e(tr(m==null?void 0:m.totalElements));return}z()}else if(j===((v=ju)==null?void 0:v.REQUESTOR)){if(u[m==null?void 0:m.page]){(m==null?void 0:m.totalElements)>((m==null?void 0:m.page)+1)*(m==null?void 0:m.size)?e(tr(((m==null?void 0:m.page)+1)*(m==null?void 0:m.size))):e(tr(m==null?void 0:m.totalElements));return}await o(ie==null?void 0:ie.TemplateName,q)}},f=async()=>{m!=null&&m.existingCreatePages.includes(m==null?void 0:m.page)||z()},z=()=>{var L,A,O,k;e(yl(!0));let j={};const p=V,T=Bl(mr.CURRENT_TASK,!0,{}),v=Q||(s==null?void 0:s.ATTRIBUTE_2)||(T==null?void 0:T.ATTRIBUTE_2);H?j={massCreationId:he!=null&&he.isBifurcated?"":v===b.CREATE||v===b.CREATE_WITH_UPLOAD?p.slice(3):"",massChildCreationId:he!=null&&he.isBifurcated&&(v===b.CREATE||v===b.CREATE_WITH_UPLOAD)?p.slice(3):"",massChangeId:he!=null&&he.isBifurcated?"":v===b.CHANGE||v===b.CHANGE_WITH_UPLOAD?p.slice(3):"",massExtendId:he!=null&&he.isBifurcated?"":v===b.EXTEND||v===b.EXTEND_WITH_UPLOAD?p.slice(3):"",massSchedulingId:he!=null&&he.isBifurcated?"":v===b.FINANCE_COSTING?p.slice(3):"",screenName:v===b.FINANCE_COSTING?"":v,dtName:v===b.FINANCE_COSTING?"":(L=Ei)==null?void 0:L.MDG_MAT_MATERIAL_FIELD_CONFIG,version:v===b.FINANCE_COSTING?"":"v2",page:m==null?void 0:m.page,size:v===b.FINANCE_COSTING?100:v===b.CHANGE||v===b.CHANGE_WITH_UPLOAD?10:50,sort:"",ApproverGroup:(s==null?void 0:s.ATTRIBUTE_5)||"",Region:"",massChildSchedulingId:he!=null&&he.isBifurcated&&v===b.FINANCE_COSTING?p.slice(3):"",massChildExtendId:he!=null&&he.isBifurcated&&(v===b.EXTEND||v===b.EXTEND_WITH_UPLOAD)?p.slice(3):"",massChildChangeId:he!=null&&he.isBifurcated&&(v===b.CHANGE||v===b.CHANGE_WITH_UPLOAD)?p.slice(3):""}:j={massCreationId:"",massChangeId:"",massSchedulingId:v===b.FINANCE_COSTING?p.slice(3):"",massExtendId:"",screenName:v===b.FINANCE_COSTING?"":v,dtName:v===b.FINANCE_COSTING?"":(A=Ei)==null?void 0:A.MDG_MAT_MATERIAL_FIELD_CONFIG,version:v===b.FINANCE_COSTING?"":"v2",page:m==null?void 0:m.page,size:v===b.FINANCE_COSTING?100:v===b.CHANGE||v===b.CHANGE_WITH_UPLOAD?10:50,sort:"",ApproverGroup:(s==null?void 0:s.ATTRIBUTE_5)||"",Region:"",massChildCreationId:v===b.CREATE||v===b.CREATE_WITH_UPLOAD?p.slice(3):"",massChildSchedulingId:"",massChildExtendId:v===b.EXTEND||v===b.EXTEND_WITH_UPLOAD?p.slice(3):"",massChildChangeId:v===b.CHANGE||v===b.CHANGE_WITH_UPLOAD?p.slice(3):""};const C=async U=>{var ge,ee,Ue,M,Se,ce,$,qe;e(yl(!1));const x=U.body;if(e(wh(U==null?void 0:U.totalElements)),(U==null?void 0:U.totalPages)===1||(U==null?void 0:U.currentPage)+1===(U==null?void 0:U.totalPages)?(e(tr(U==null?void 0:U.totalElements)),e(pi(!0))):e(tr(((U==null?void 0:U.currentPage)+1)*(U==null?void 0:U.pageSize))),(s==null?void 0:s.ATTRIBUTE_2)===((ge=b)==null?void 0:ge.CHANGE)||(s==null?void 0:s.ATTRIBUTE_2)===((ee=b)==null?void 0:ee.CHANGE_WITH_UPLOAD)||Q===((Ue=b)==null?void 0:Ue.CHANGE_WITH_UPLOAD)||Q===((M=b)==null?void 0:M.CHANGE)){e(hi({keyName:"requestHeaderData",data:(Se=x[0])==null?void 0:Se.Torequestheaderdata})),r(x);return}if((s==null?void 0:s.ATTRIBUTE_2)===((ce=b)==null?void 0:ce.FINANCE_COSTING)||Q===(($=b)==null?void 0:$.FINANCE_COSTING)){const F=await c(x);e(jg([...l,...F])),e(zu(m==null?void 0:m.page));return}const N=zg(x,se);e(Yg({data:N==null?void 0:N.payload}));const D=Object.keys(N==null?void 0:N.payload).filter(F=>!isNaN(Number(F))),I={};D.forEach(F=>{I[F]=N==null?void 0:N.payload[F]}),e(Xg((qe=Object.values(I))==null?void 0:qe.map(F=>F.headerData))),e(zu(m==null?void 0:m.page))},S=U=>{ne(U)};Xe(`/${Re}/${(k=(O=Fe)==null?void 0:O.CHG_DISPLAY_REQUESTOR)==null?void 0:k.DISPLAY_DTO}`,"post",C,S,j)};return{getNextDisplayDataForChange:J,getNextDisplayDataForCreate:f}};var qa={},VT=Go;Object.defineProperty(qa,"__esModule",{value:!0});var pa=qa.default=void 0,KT=VT(Bo()),JT=$o;pa=qa.default=(0,KT.default)((0,JT.jsx)("path",{d:"M22 12c0-5.52-4.48-10-10-10S2 6.48 2 12s4.48 10 10 10 10-4.48 10-10M4 12c0-4.42 3.58-8 8-8s8 3.58 8 8-3.58 8-8 8-8-3.58-8-8m12 0-4 4-1.41-1.41L12.17 13H8v-2h4.17l-1.59-1.59L12 8z"}),"ArrowCircleRightOutlined");const QT=({params:e,field:t,isFieldError:r,isFieldDisable:o,isNewRow:c,keyName:s,handleChangeValue:m,handleRemoveError:l,charCount:u,setCharCount:se,isFocused:ie,setIsFocused:q})=>{var ne;const[g,V]=d.useState(e.row[t.jsonName]||""),Q=e.row.id,H=u[s]===(t==null?void 0:t.maxLength),he=J=>{const f=J.target.value;V(f),m(e.row,Q,t.jsonName,(f==null?void 0:f.toUpperCase())||"",t.viewName,t.fieldName,s),se(z=>({...z,[s]:f.length}))};return n(Ms,{title:(ne=e.row[t.jsonName])==null?void 0:ne.toUpperCase(),arrow:!0,placement:"top",children:n(xn,{fullWidth:!0,placeholder:`ENTER ${t.fieldName.toUpperCase()}`,variant:"outlined",size:"small",value:g,disabled:o||!c&&t.visibility===$n.DISPLAY,inputProps:{maxLength:t.maxLength,style:{textTransform:"uppercase"}},InputProps:{sx:{"& .MuiOutlinedInput-notchedOutline":{borderColor:r?Be.error.dark:void 0},"&.Mui-disabled":{"& input":{WebkitTextFillColor:Be.text.primary,color:Be.text.primary}}}},onFocus:J=>{J.stopPropagation(),q({...ie,[s]:!0}),se(f=>({...f,[s]:J.target.value.length})),r&&l(Q,t.fieldName)},onKeyDown:J=>J.key===" "&&J.stopPropagation(),onClick:J=>J.stopPropagation(),onChange:he,onBlur:()=>q({...ie,[s]:!1}),helperText:ie[s]&&(H?"Max Length Reached":`${u[s]||0}/${t.maxLength}`),FormHelperTextProps:{sx:{color:H?Be.error.dark:Be.primary.darkPlus,position:"absolute",bottom:"-20px"}},sx:{"& .MuiInputBase-root":{height:"34px"},"& .MuiOutlinedInput-root":{"&.Mui-focused fieldset":{borderColor:H?Be.error.dark:""}}}})})},ZT=e=>{var Ut,Ns,ds,rs,at,yt,ws,Hs,os,ls,ao,Ye,Mt,gs,Lt,ps,Js,Bs,st,Ts,to,St,es,ot,Dt,Rs;const{customError:t}=sn(),{getNextDisplayDataForChange:r}=Gi(),o=oe(ae=>ae.tabsData.changeFieldsDT),c=oe(ae=>ae.payload.payloadData),s=o==null?void 0:o["Config Data"],m=oe(ae=>ae.payload.tablesList),l=oe(ae=>ae.payload.changeFieldRows),u=oe(ae=>ae.payload.changeFieldRowsDisplay),se=oe(ae=>ae.payload.changeLogData),ie=oe(ae=>ae.payload.matNoList),q=oe(ae=>ae.payload.newRowIds),g=oe(ae=>ae.AllDropDown.dropDown||{}),V=oe(ae=>ae.payload.dataLoading),Q=oe(ae=>ae.payload.errorData),H=oe(ae=>ae.payload.selectedRows),he=oe(ae=>{var $e;return($e=ae.request.requestHeader)==null?void 0:$e.requestId}),ne=oe(ae=>ae.userManagement.userData),J=oe(ae=>ae.userManagement.taskData),f=oe(ae=>ae.paginationData),z=oe(ae=>ae.payload.templateArray),j=oe(ae=>ae.payload.requestorPayload),[p,T]=d.useState([]),[v,C]=d.useState({}),[S,L]=d.useState({}),[A,O]=d.useState(""),[k,U]=d.useState("success"),[x,N]=d.useState(!1),[D,I]=d.useState(""),ge=oe(ae=>ae.tabsData.dataLoading),[ee,Ue]=d.useState({data:{},isVisible:!1}),M=Ro(),Se=tn(),ce=new URLSearchParams(Se.search),$=ce.get("reqBench"),qe=ce.get("RequestId"),{t:F}=bn();let Ke=Se.state;d.useEffect(()=>{l&&(p==null?void 0:p.length)===0&&T(JSON.parse(JSON.stringify(l)))},[l]);const[Us,Wt]=d.useState(0),[$s,Fs]=d.useState(10),ss=(ae,$e)=>{Wt(isNaN($e)?0:$e)},io=ae=>{const $e=ae.target.value;Fs($e),Wt(0)},Ws=()=>{N(!0)},ks=()=>{N(!1)},Qt=()=>{const ae=(l==null?void 0:l.length)>0?Object.keys(l[0]):[],$e=ae==null?void 0:ae.reduce((Ve,Oe)=>(Ve[Oe]=Oe==="id"?Co():Oe==="slNo"?1:"",Ve),{}),me=[$e,...l].map((Ve,Oe)=>({...Ve,slNo:Oe+1})),Ne=[$e,...u[f==null?void 0:f.page]||[]].map((Ve,Oe)=>({...Ve,slNo:Oe+1}));M(Yu([$e==null?void 0:$e.id,...q])),M(gr(me)),M(pr({...u,[f==null?void 0:f.page]:Ne})),M(Tr([$e==null?void 0:$e.id,...H])),M(xl(!0)),e==null||e.setCompleted([!0,!1])},fs=ql(Vg,c==null?void 0:c.TemplateName),Ht=ql(vh,fs),ns=(Ht==null?void 0:Ht.length)>1,Zt=(ae,$e)=>{const me=p==null?void 0:p.find(Ne=>{const Ve=Ne.Material===(ae==null?void 0:ae.Material)&&(Ne==null?void 0:Ne[Ht[0]])===(ae==null?void 0:ae[Ht[0]]);return ns?Ve&&(Ne==null?void 0:Ne[Ht[1]])===(ae==null?void 0:ae[Ht[1]]):Ve});if(me)return me[$e]},xs=(ae,$e,me,Ne)=>{var Oe;const Ve=(Oe=p==null?void 0:p[me])==null?void 0:Oe.find(ct=>{let ke=ct.Material===(ae==null?void 0:ae.Material);return(Ne==null?void 0:Ne.length)>0&&(ke=ke&&(ct==null?void 0:ct[Ne[0]])===(ae==null?void 0:ae[Ne[0]]),(Ne==null?void 0:Ne.length)>1&&(ke=ke&&(ct==null?void 0:ct[Ne[1]])===(ae==null?void 0:ae[Ne[1]]))),ke});return Ve?Ve[$e]:"-"},lt=ae=>{M(Jg(ae))},{handleObjectChangeFieldRows:He}=XT(l,u,f,fs,ne,he,z,lt,xs,e==null?void 0:e.RequestId),Rt=(ae,$e,me,Ne,Ve,Oe)=>{var ct,ke,Vt,Es;if(Array.isArray(l)){if(me==="AltUnit"||me==="Langu"){const te=ep(ae,u==null?void 0:u[f==null?void 0:f.page],ie,Ne,c==null?void 0:c.TemplateName);if(te==="matError"){U("error"),I(F((ct=Dn)==null?void 0:ct.MATL_ERROR_MSG)),Ws();return}else if(te==="altUnitError"){U("error"),I(F((ke=Dn)==null?void 0:ke.ALTUNIT_ERROR_MSG)),Ws();return}else if(te==="languError"){U("error"),I(F((Vt=Dn)==null?void 0:Vt.LANG_ERROR_MSG)),Ws();return}}const Pt=l==null?void 0:l.map(te=>{var xe,B;return(te==null?void 0:te.id)===$e?{...te,[me]:Ne,...me==="Material"?{...(c==null?void 0:c.TemplateName)===((xe=Je)==null?void 0:xe.UPD_DESC)?{Langu:""}:{},...(c==null?void 0:c.TemplateName)===((B=Je)==null?void 0:B.LOGISTIC)?{AltUnit:""}:{}}:{}}:te});M(gr(Pt));const je=(Es=u==null?void 0:u[f==null?void 0:f.page])==null?void 0:Es.map(te=>{var xe,B;return(te==null?void 0:te.id)===$e?{...te,[me]:Ne,...me==="Material"?{...(c==null?void 0:c.TemplateName)===((xe=Je)==null?void 0:xe.UPD_DESC)?{Langu:""}:{},...(c==null?void 0:c.TemplateName)===((B=Je)==null?void 0:B.LOGISTIC)?{AltUnit:""}:{}}:{}}:te});M(pr({...u,[f==null?void 0:f.page]:je}));const Pe=tp(),_t=te=>te!=null&&te.toString().startsWith("/Date(")&&(te!=null&&te.toString().endsWith(")/"))?op(te):te;let jt={ObjectNo:`${ae==null?void 0:ae.Material}$$${ae==null?void 0:ae[Ht[0]]}${ns?`$$${ae==null?void 0:ae[Ht[1]]}`:""}`,ChangedBy:ne.emailId,ChangedOn:Pe.sapFormat,FieldName:Oe??me,PreviousValue:Zt(ae,me)??"-",SAPValue:Zt(ae,me)??"-",CurrentValue:_t(Ne)??""};lt(jt);let _s={RequestId:he||(e==null?void 0:e.RequestId).slice(3),changeLogId:(ae==null?void 0:ae.ChangeLogId)??null,[fs]:[...z,jt]};const Ft=Mh(_s,fs);M(Oh(Ft))}else typeof l=="object"&&l[Ve]&&He(Ve,$e,me,Ne,Oe)},Ys=(ae,$e)=>{const me={};Object.keys(Q).forEach(Ne=>{const Ve=Q[Ne];if(Ve.id===ae){const Oe=Ve.missingFields.filter(ct=>ct!==$e);Oe.length>0&&(me[Ne]={...Ve,missingFields:Oe})}else me[Ne]={...Ve}}),M(da(me))},Bt=()=>{var me,Ne,Ve,Oe;const ae=ee==null?void 0:ee.data,$e=(me=ae==null?void 0:ae.row)==null?void 0:me.id;if(Array.isArray(l)){const ke=l.filter(je=>(je==null?void 0:je.id)!==$e).map((je,Pe)=>({...je,slNo:Pe+1}));M(gr(ke));const Vt={...u,[f==null?void 0:f.page]:(Ve=(Ne=u[f==null?void 0:f.page])==null?void 0:Ne.filter(je=>(je==null?void 0:je.id)!==$e))==null?void 0:Ve.map((je,Pe)=>({...je,slNo:Pe+1}))};M(pr(Vt));const Es=q==null?void 0:q.filter(je=>je!==$e);M(Yu(Es));const Pt=l.find(je=>je.id===$e);if(Pt){const je=`${Pt.Material}$$${Pt[Ht[0]]}${ns?`$$${Pt[Ht[1]]}`:""}`,Pe=JSON.parse(JSON.stringify(se));if((Oe=Pe[Pt.Material])!=null&&Oe[fs]){const _t=Pe[Pt.Material][fs].filter(jt=>jt.ObjectNo!==je&&jt.ObjectNo!==`${Pt.Material}$$`);_t.length===0?(delete Pe[Pt.Material][fs],Object.keys(Pe[Pt.Material]).length===0&&(delete Pe[Pt.Material],delete Pe[""])):Pe[Pt.Material][fs]=_t}M(sp(Pe))}}Ue({...ee,isVisible:!1})},Me=(ae,$e)=>{var Oe,ct,ke,Vt,Es,Pt,je,Pe,_t,jt,_s,Ft,te,xe,B,de;const me=[{headerName:"Sl. No.",field:"slNo",align:"center",flex:(c==null?void 0:c.TemplateName)===((Oe=Je)==null?void 0:Oe.LOGISTIC)||(c==null?void 0:c.TemplateName)===((ct=Je)==null?void 0:ct.MRP)&&ae==="Plant Data"?void 0:.1,width:(c==null?void 0:c.TemplateName)===((ke=Je)==null?void 0:ke.LOGISTIC)||(c==null?void 0:c.TemplateName)===((Vt=Je)==null?void 0:Vt.MRP)&&ae==="Plant Data"?1:void 0},...$e.map(w=>{var Te,We,le,dt,ze,Qe,Ct;return{headerName:R("span",{children:[w.fieldName,w.visibility===((Te=$n)==null?void 0:Te.MANDATORY)&&n("span",{style:{color:(le=(We=Be)==null?void 0:We.error)==null?void 0:le.dark,marginLeft:4},children:"*"})]}),field:w.jsonName,flex:(c==null?void 0:c.TemplateName)===((dt=Je)==null?void 0:dt.LOGISTIC)||(c==null?void 0:c.TemplateName)===((ze=Je)==null?void 0:ze.MRP)&&(w==null?void 0:w.viewName)==="Plant Data"?void 0:1,width:(c==null?void 0:c.TemplateName)===((Qe=Je)==null?void 0:Qe.LOGISTIC)||(c==null?void 0:c.TemplateName)===((Ct=Je)==null?void 0:Ct.MRP)&&(w==null?void 0:w.viewName)==="Plant Data"?200:void 0,renderCell:_e=>{var co,Po,Qs,Ot,Gs,Zs,_o,Fo,Io,ho,is,Wo,so,qo,jo,zo,Yo,Xo,pe,ht,ft,At,ve,Nt,$t,kt,as,qs;const ut=(co=Object==null?void 0:Object.values(Q))==null?void 0:co.find(gt=>{var Gt;return(gt==null?void 0:gt.id)===((Gt=_e==null?void 0:_e.row)==null?void 0:Gt.id)}),ys=`${(Po=_e==null?void 0:_e.row)==null?void 0:Po.id}-${w==null?void 0:w.jsonName}`,Xs=(Qs=ut==null?void 0:ut.missingFields)==null?void 0:Qs.includes(w==null?void 0:w.fieldName),uo=q==null?void 0:q.includes((Ot=_e==null?void 0:_e.row)==null?void 0:Ot.id),us=!!($&&!((Gs=ko)!=null&&Gs.includes(Ke==null?void 0:Ke.reqStatus)));if(w.fieldType===Ji.INPUT)return n(QT,{params:_e,field:w,isFieldError:Xs,isFieldDisable:us,isNewRow:uo,keyName:ys,handleChangeValue:Rt,handleRemoveError:Ys,charCount:S,setCharCount:L,isFocused:v,setIsFocused:C});if(w.fieldType===Ji.DROPDOWN){const gt=q==null?void 0:q.includes((Zs=_e==null?void 0:_e.row)==null?void 0:Zs.id),Gt=(w==null?void 0:w.jsonName)!=="Unittype1"&&(w==null?void 0:w.jsonName)!=="Spproctype"&&(w==null?void 0:w.jsonName)!=="MrpCtrler"?(_o=g==null?void 0:g[w==null?void 0:w.jsonName])==null?void 0:_o.find(De=>{var pt;return De.code===((pt=_e==null?void 0:_e.row)==null?void 0:pt[w==null?void 0:w.jsonName])}):(w==null?void 0:w.jsonName)==="Spproctype"||(w==null?void 0:w.jsonName)==="MrpCtrler"?(ho=(Io=g==null?void 0:g[w==null?void 0:w.jsonName])==null?void 0:Io[(Fo=_e==null?void 0:_e.row)==null?void 0:Fo.Plant])==null?void 0:ho.find(De=>{var pt;return De.code===((pt=_e==null?void 0:_e.row)==null?void 0:pt[w==null?void 0:w.jsonName])}):(so=(Wo=g==null?void 0:g[w==null?void 0:w.jsonName])==null?void 0:Wo[(is=_e==null?void 0:_e.row)==null?void 0:is.WhseNo])==null?void 0:so.find(De=>{var pt;return De.code===((pt=_e==null?void 0:_e.row)==null?void 0:pt[w==null?void 0:w.jsonName])});return n(lo,{options:(w==null?void 0:w.jsonName)==="Unittype1"?(jo=g==null?void 0:g.Unittype1)==null?void 0:jo[(qo=_e==null?void 0:_e.row)==null?void 0:qo.WhseNo]:(w==null?void 0:w.jsonName)==="Spproctype"?(Yo=g==null?void 0:g.Spproctype)==null?void 0:Yo[(zo=_e==null?void 0:_e.row)==null?void 0:zo.Plant]:(w==null?void 0:w.jsonName)==="MrpCtrler"?(pe=g==null?void 0:g.MrpCtrler)==null?void 0:pe[(Xo=_e==null?void 0:_e.row)==null?void 0:Xo.Plant]:g!=null&&g[w==null?void 0:w.jsonName]?g==null?void 0:g[w==null?void 0:w.jsonName]:[],value:Gt||((ht=_e==null?void 0:_e.row)!=null&&ht[w==null?void 0:w.jsonName]?{code:(ft=_e==null?void 0:_e.row)==null?void 0:ft[w==null?void 0:w.jsonName],desc:""}:null),onChange:De=>{Rt(_e.row,_e.row.id,w==null?void 0:w.jsonName,De==null?void 0:De.code,ae,w==null?void 0:w.fieldName),Xs&&Ys(_e.row.id,w==null?void 0:w.fieldName)},listWidth:150,placeholder:`Select ${w.fieldName}`,disabled:us?!0:gt?!1:(w==null?void 0:w.visibility)===((At=$n)==null?void 0:At.DISPLAY),isFieldError:Xs})}else if(w.fieldType===Ji.DATE_FIELD){const gt=q==null?void 0:q.includes((ve=_e==null?void 0:_e.row)==null?void 0:ve.id),Gt=(Nt=_e==null?void 0:_e.row)!=null&&Nt[w==null?void 0:w.jsonName]?(()=>{var pt;const De=(pt=_e==null?void 0:_e.row)==null?void 0:pt[w==null?void 0:w.jsonName];if(De.startsWith("/Date(")&&De.endsWith(")/")){const ms=parseInt(De.slice(6,-2));return new Date(ms)}return typeof De=="string"&&De.match(/^\d{4}-\d{2}-\d{2}/)?new Date(De):Fn(De,["YYYY-MM-DD HH:mm:ss.S","DD MMM YYYY HH:mm:ss UTC"]).toDate()})():null;return n(Ms,{title:($t=_e==null?void 0:_e.row)==null?void 0:$t[w==null?void 0:w.jsonName],arrow:!0,placement:"top",children:n(Qg,{dateAdapter:Zg,children:n(ET,{disabled:us?!0:gt?!1:(w==null?void 0:w.visibility)===((kt=$n)==null?void 0:kt.DISPLAY),slotProps:{textField:{size:"small",fullWidth:!0,InputProps:{sx:{"& .MuiOutlinedInput-notchedOutline":{borderColor:Xs?(qs=(as=Be)==null?void 0:as.error)==null?void 0:qs.dark:void 0}}}}},value:Gt,onChange:De=>{if(De){const pt=`/Date(${Date.parse(De)})/`;Rt(_e.row,_e.row.id,w==null?void 0:w.jsonName,pt,ae,w==null?void 0:w.fieldName)}else Rt(_e.row,_e.row.id,w==null?void 0:w.jsonName,null,ae,w==null?void 0:w.fieldName);Xs&&Ys(_e.row.id,w==null?void 0:w.fieldName)},onError:De=>{De&&!Xs&&t(dn.DATE_VALIDATION_ERROR,De)},maxDate:new Date(9999,11,31)})})})}else return _e.value||"-"}}}),{...(((c==null?void 0:c.TemplateName)===((Es=Je)==null?void 0:Es.LOGISTIC)||(c==null?void 0:c.TemplateName)===((Pt=Je)==null?void 0:Pt.UPD_DESC))&&!qe||((c==null?void 0:c.TemplateName)===((je=Je)==null?void 0:je.LOGISTIC)||(c==null?void 0:c.TemplateName)===((Pe=Je)==null?void 0:Pe.UPD_DESC))&&qe&&((J==null?void 0:J.taskDesc)===((_t=ca)==null?void 0:_t.REQUESTOR)||(Ke==null?void 0:Ke.reqStatus)===((jt=Ao)==null?void 0:jt.DRAFT)))&&{field:"action",headerName:"Action",flex:(c==null?void 0:c.TemplateName)===((_s=Je)==null?void 0:_s.LOGISTIC)||(c==null?void 0:c.TemplateName)===((Ft=Je)==null?void 0:Ft.MRP)&&(field==null?void 0:field.viewName)==="Plant Data"?void 0:1,width:(c==null?void 0:c.TemplateName)===((te=Je)==null?void 0:te.LOGISTIC)||(c==null?void 0:c.TemplateName)===((xe=Je)==null?void 0:xe.MRP)&&(field==null?void 0:field.viewName)==="Plant Data"?200:void 0,align:"center",headerAlign:"center",renderCell:w=>{var Te;return n(bo,{direction:"row",alignItems:"center",sx:{marginLeft:"0.5rem",magrinRight:"0.5rem"},spacing:.5,children:n(Ms,{title:"Delete Row",children:n(Ds,{disabled:!(q!=null&&q.includes((Te=w==null?void 0:w.row)==null?void 0:Te.id)),onClick:()=>{Ue({data:w,isVisible:!0})},color:"error",children:n(Ar,{})})})})}}}],Ne=Array.isArray(l)?(u==null?void 0:u[f==null?void 0:f.page])||[]:((B=u==null?void 0:u[f==null?void 0:f.page])==null?void 0:B[ae])||[],Ve=Array.isArray(H)?H:H[ae];return R("div",{style:{height:400,width:"100%"},children:[n(Di,{paginationLoading:V,rows:Ne,rowCount:(Ne==null?void 0:Ne.length)??0,columns:me,getRowIdValue:"id",rowHeight:70,isLoading:V,tempheight:"calc(100vh - 380px)",page:Us,pageSize:$s,selectionModel:Ve,onPageChange:ss,onPageSizeChange:io,onCellEditCommit:Rt,checkboxSelection:!($&&!((de=ko)!=null&&de.includes(Ke==null?void 0:Ke.reqStatus))),disableSelectionOnClick:!0,showCustomNavigation:!0,hideFooter:!0}),(ee==null?void 0:ee.isVisible)&&R(Pi,{isOpen:ee==null?void 0:ee.isVisible,titleIcon:n(Ar,{size:"small",color:"error",sx:{fontSize:"20px"}}),Title:F("Delete Row")+"!",handleClose:()=>Ue({...ee,isVisible:!1}),children:[n(Lo,{sx:{mt:2},children:F(Dn.DELETE_MESSAGE)}),R(Do,{children:[n(It,{variant:"outlined",size:"small",sx:{..._a},onClick:()=>Ue({...ee,isVisible:!1}),children:F(mi.CANCEL)}),n(It,{variant:"contained",size:"small",sx:{...qi},onClick:Bt,children:F(mi.DELETE)})]})]})]})},it=s&&Object.keys(s);if(d.useEffect(()=>{var ae,$e,me;(f==null?void 0:f.page)+1&&((c==null?void 0:c.RequestType)===((ae=b)==null?void 0:ae.CHANGE)||(c==null?void 0:c.RequestType)===(($e=b)==null?void 0:$e.CHANGE_WITH_UPLOAD))&&(qe&&(!j||((me=Object==null?void 0:Object.keys(j))==null?void 0:me.length)===0)?(r("display"),Wt(0)):(r("requestor"),Wt(0)),A==="prev"?M(pi(!1)):A==="next"&&(f==null?void 0:f.currentElements)>=(f==null?void 0:f.totalElements)&&M(pi(!0)))},[f==null?void 0:f.page]),(it==null?void 0:it.length)===1){const ae=it[0],$e=s[ae];return R(bo,{children:[R(bo,{direction:"row",justifyContent:"space-between",mb:1.5,children:[((c==null?void 0:c.TemplateName)===((Ut=Je)==null?void 0:Ut.LOGISTIC)||(c==null?void 0:c.TemplateName)===((Ns=Je)==null?void 0:Ns.UPD_DESC))&&!qe||((c==null?void 0:c.TemplateName)===((ds=Je)==null?void 0:ds.LOGISTIC)||(c==null?void 0:c.TemplateName)===((rs=Je)==null?void 0:rs.UPD_DESC))&&qe&&((J==null?void 0:J.taskDesc)===((at=ca)==null?void 0:at.REQUESTOR)||(Ke==null?void 0:Ke.reqStatus)===((yt=Ao)==null?void 0:yt.DRAFT))?n(It,{variant:"contained",color:"primary",onClick:Qt,startIcon:n(TT,{}),sx:{borderRadius:"10px",boxShadow:"0px 4px 8px rgba(0, 0, 0, 0.15)"},children:"Add Row"}):n(ye,{sx:{width:0,height:0}}),R(ye,{sx:{display:"flex",justifyContent:"center",alignItems:"center",backgroundImage:"linear-gradient(180deg,rgb(242, 241, 255) 0%,rgb(255, 255, 255) 100%)",border:"1px solid #E0E0E0",padding:"5px",borderRadius:"10px",mt:-1,boxShadow:"0px 2px 10px rgba(0, 0, 0, 0.08)"},children:[n(Ms,{title:"Previous",placement:"top",arrow:!0,children:n(Ds,{disabled:(f==null?void 0:f.page)===0||!1,onClick:()=>{O("prev"),M(fr((f==null?void 0:f.page)-1))},children:n(fa,{sx:{color:(f==null?void 0:f.page)===0?(Hs=(ws=Be)==null?void 0:ws.secondary)==null?void 0:Hs.grey:(ls=(os=Be)==null?void 0:os.primary)==null?void 0:ls.main,fontSize:"1.5rem",marginRight:"2px"}})})}),R("span",{style:{marginRight:"2px"},children:[n("strong",{style:{color:(Ye=(ao=Be)==null?void 0:ao.primary)==null?void 0:Ye.main},children:"Materials :"})," ",R("strong",{children:[(f==null?void 0:f.page)*(f==null?void 0:f.size)+1," -"," ",f==null?void 0:f.currentElements]})," ",n("span",{children:"of"})," ",n("strong",{children:f==null?void 0:f.totalElements})]}),n(Ms,{title:"Next",placement:"top",arrow:!0,children:n(Ds,{disabled:(f==null?void 0:f.currentElements)>=(f==null?void 0:f.totalElements)||!1,onClick:()=>{O("next"),M(fr((f==null?void 0:f.page)+1))},children:n(pa,{sx:{color:(f==null?void 0:f.currentElements)>=(f==null?void 0:f.totalElements)?(gs=(Mt=Be)==null?void 0:Mt.secondary)==null?void 0:gs.grey:(ps=(Lt=Be)==null?void 0:Lt.primary)==null?void 0:ps.main,fontSize:"1.5rem"}})})})]})]}),n("div",{children:Me(ae,$e)}),n(Un,{openSnackBar:x,alertMsg:D,alertType:k,handleSnackBarClose:ks})]})}return R(Ss,{children:[n(Cn,{blurLoading:ge}),!ge&&n(Ss,{children:s?R("div",{children:[R(ye,{sx:{display:"flex",justifyContent:"center",alignItems:"center",backgroundImage:"linear-gradient(180deg, rgb(242, 241, 255) 0%, rgb(255, 255, 255) 100%)",border:"1px solid #E0E0E0",borderRadius:"10px",padding:"5px",width:"fit-content",marginLeft:"auto",mt:-1,mb:2,boxShadow:"0px 2px 10px rgba(0, 0, 0, 0.08)"},children:[n(Ms,{title:"Previous",placement:"top",arrow:!0,children:n(Ds,{disabled:(f==null?void 0:f.page)===0||!1,onClick:()=>{M(fr((f==null?void 0:f.page)-1))},children:n(fa,{sx:{color:(f==null?void 0:f.page)===0?(Bs=(Js=Be)==null?void 0:Js.secondary)==null?void 0:Bs.grey:(Ts=(st=Be)==null?void 0:st.primary)==null?void 0:Ts.main,fontSize:"1.5rem",marginRight:"2px"}})})}),R("span",{style:{marginRight:"2px"},children:[n("strong",{style:{color:(St=(to=Be)==null?void 0:to.primary)==null?void 0:St.main},children:"Materials :"})," ",R("strong",{children:[(f==null?void 0:f.page)*(f==null?void 0:f.size)+1," -"," ",f==null?void 0:f.currentElements]})," ",n("span",{children:"of"})," ",n("strong",{children:f==null?void 0:f.totalElements})]}),n(Ms,{title:"Next",placement:"top",arrow:!0,children:n(Ds,{disabled:(f==null?void 0:f.currentElements)>=(f==null?void 0:f.totalElements)||!1,onClick:()=>{M(fr((f==null?void 0:f.page)+1))},children:n(pa,{sx:{color:(f==null?void 0:f.currentElements)>=(f==null?void 0:f.totalElements)?(ot=(es=Be)==null?void 0:es.secondary)==null?void 0:ot.grey:(Rs=(Dt=Be)==null?void 0:Dt.primary)==null?void 0:Rs.main,fontSize:"1.5rem"}})})})]}),it==null?void 0:it.map(ae=>m!=null&&m.includes(ae)?R(xh,{sx:{marginBottom:"20px",boxShadow:3},children:[n(yh,{expandIcon:n(Kg,{}),"aria-controls":`${ae}-content`,id:`${ae}-header`,sx:{backgroundImage:"linear-gradient(180deg,rgb(242, 241, 255) 0%,rgb(255, 255, 255) 100%)",padding:"8px 16px","&:hover":{backgroundImage:"linear-gradient(90deg,rgb(242, 242, 255) 0%,rgb(239, 232, 255) 100%)"}},children:n(mt,{variant:"h6",sx:{fontWeight:"bold"},children:ae})}),n(Lh,{sx:{height:"calc(100vh - 300px)"},children:Me(ae,s[ae])})]},ae):null)]}):n(mt,{children:"No data available"})})]})},$i=e=>{const t=oe(J=>J.payload.changeFieldRows),r=oe(J=>J.payload.changeLogData),o=oe(J=>J.request),c=oe(J=>J.payload),s=oe(J=>J.payload.dynamicKeyValues),m=oe(J=>J.payload.selectedRows),l=oe(J=>J.userManagement.taskData),u=e||(s==null?void 0:s.templateName),se=(J,f)=>{var z,j,p,T,v,C;return r[J]?{RequestId:((T=c==null?void 0:c.payloadData)==null?void 0:T.RequestId)||((v=c==null?void 0:c.changeLogData)==null?void 0:v.RequestId),ChildRequestId:((C=s==null?void 0:s.childRequestHeaderData)==null?void 0:C.ChildRequestId)??null,ChangeLogId:f??null,...r[J]}:{RequestId:((z=c==null?void 0:c.payloadData)==null?void 0:z.RequestId)||((j=c==null?void 0:c.changeLogData)==null?void 0:j.RequestId),ChildRequestId:((p=s==null?void 0:s.childRequestHeaderData)==null?void 0:p.ChildRequestId)??null,ChangeLogId:f??null}},ie=J=>{var f,z,j,p,T,v,C,S,L,A,O,k,U,x;if(e===((f=Je)==null?void 0:f.LOGISTIC)||(s==null?void 0:s.templateName)===((z=Je)==null?void 0:z.LOGISTIC))return q(J);if(e===((j=Je)==null?void 0:j.ITEM_CAT)||(s==null?void 0:s.templateName)===((p=Je)==null?void 0:p.ITEM_CAT))return g(J);if(e===((T=Je)==null?void 0:T.MRP)||(s==null?void 0:s.templateName)===((v=Je)==null?void 0:v.MRP))return V(J);if(e===((C=Je)==null?void 0:C.UPD_DESC)||(s==null?void 0:s.templateName)===((S=Je)==null?void 0:S.UPD_DESC))return he(J);if(e===((L=Je)==null?void 0:L.WARE_VIEW_2)||(s==null?void 0:s.templateName)===((A=Je)==null?void 0:A.WARE_VIEW_2))return ne(J);if(e===((O=Je)==null?void 0:O.CHG_STAT)||(s==null?void 0:s.templateName)===((k=Je)==null?void 0:k.CHG_STAT))return Q(J);if(e===((U=Je)==null?void 0:U.SET_DNU)||(s==null?void 0:s.templateName)===((x=Je)==null?void 0:x.SET_DNU))return H(J)},q=J=>{const f=t.reduce((z,j)=>{if((m==null?void 0:m.length)!==0&&!(m!=null&&m.includes(j==null?void 0:j.id)))return z;const p=j==null?void 0:j.Material;return z[p]||(z[p]=[]),z[p].push(j),z},{});if(J){const z=["id","MaterialId","ClientId","slNo","ChangeLogId","MatlType"];return Object.keys(f).map(p=>{var L,A,O,k,U,x,N,D,I,ge,ee,Ue,M,Se,ce;const T=f[p],{MaterialId:v,ClientId:C,ChangeLogId:S}=T[(T==null?void 0:T.length)-1];return{MaterialId:v,ChangeLogId:S,Material:p,MatlType:((O=(A=f[p])==null?void 0:A[((L=f[p])==null?void 0:L.length)-1])==null?void 0:O.MatlType)||"",Function:"UPD",TaskId:(s==null?void 0:s.otherPayloadData.TaskId)||"",TaskName:(s==null?void 0:s.otherPayloadData.TaskName)||"",creationTime:(s==null?void 0:s.otherPayloadData.CreationTime)||"",dueDate:(s==null?void 0:s.otherPayloadData.DueDate)||"",IsFirstCreate:!1,MassEditId:s==null?void 0:s.otherPayloadData.MassEditId,MassChildEditId:s==null?void 0:s.otherPayloadData.MassChildEditId,TotalIntermediateTasks:s==null?void 0:s.otherPayloadData.TotalIntermediateTasks,IntermediateTaskCount:s==null?void 0:s.otherPayloadData.IntermediateTaskCount,Toclientdata:{ClientId:C,Material:p,Function:"UPD"},Touomdata:T.map($=>{const qe={...$,Function:"UPD"};return z.forEach(F=>delete qe[F]),qe}),Tochildrequestheaderdata:{ChildRequestId:((k=s==null?void 0:s.childRequestHeaderData)==null?void 0:k.ChildRequestId)||null,MaterialGroupType:((U=s==null?void 0:s.childRequestHeaderData)==null?void 0:U.MaterialGroupType)||null,TaskId:(l==null?void 0:l.taskId)||null,Comments:(s==null?void 0:s.Comments)||"",TotalIntermediateTasks:((x=s==null?void 0:s.childRequestHeaderData)==null?void 0:x.TotalIntermediateTasks)||null,IntermediateTaskCount:((N=s==null?void 0:s.childRequestHeaderData)==null?void 0:N.IntermediateTaskCount)||null,ReqCreatedBy:((D=s==null?void 0:s.childRequestHeaderData)==null?void 0:D.ReqCreatedBy)||null,ReqCreatedOn:((I=s==null?void 0:s.childRequestHeaderData)==null?void 0:I.ReqCreatedOn)||null,ReqUpdatedOn:((ge=s==null?void 0:s.childRequestHeaderData)==null?void 0:ge.ReqUpdatedOn)||null,RequestType:((ee=s==null?void 0:s.childRequestHeaderData)==null?void 0:ee.RequestType)||null,RequestPrefix:((Ue=s==null?void 0:s.childRequestHeaderData)==null?void 0:Ue.RequestPrefix)||null,RequestDesc:((M=s==null?void 0:s.childRequestHeaderData)==null?void 0:M.RequestDesc)||null,RequestPriority:((Se=s==null?void 0:s.childRequestHeaderData)==null?void 0:Se.RequestPriority)||null,RequestStatus:((ce=s==null?void 0:s.childRequestHeaderData)==null?void 0:ce.RequestStatus)||null,CurrentLevel:(l==null?void 0:l.ATTRIBUTE_3)||"",CurrentLevelName:(l==null?void 0:l.ATTRIBUTE_4)||"",ParticularLevel:(s==null?void 0:s.Level)||"-1",TaskName:(l==null?void 0:l.taskDesc)||"",ApproverGroup:(l==null?void 0:l.ATTRIBUTE_5)||""},Torequestheaderdata:(s==null?void 0:s.requestHeaderData)||{},Tomaterialerrordata:(s==null?void 0:s.errorData[p])||{},TemplateName:u,changeLogData:se(p,S),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}else{const z=["id","slNo","MatlType"];return Object.keys(f).map(p=>{var T,v,C,S,L,A,O,k,U,x,N,D,I,ge,ee,Ue,M,Se;return{Touomdata:f[p].map(ce=>{const $={...ce,Function:"UPD"};return z.forEach(qe=>delete $[qe]),$}),Torequestheaderdata:{RequestId:(T=o==null?void 0:o.requestHeader)==null?void 0:T.requestId,ReqCreatedBy:(v=o==null?void 0:o.requestHeader)==null?void 0:v.reqCreatedBy,ReqCreatedOn:Is((C=o==null?void 0:o.requestHeader)==null?void 0:C.reqCreatedOn),ReqUpdatedOn:Is((S=o==null?void 0:o.requestHeader)==null?void 0:S.reqCreatedOn),RequestType:(L=o==null?void 0:o.requestHeader)==null?void 0:L.requestType,RequestPriority:(A=o==null?void 0:o.requestHeader)==null?void 0:A.requestPriority,RequestDesc:(O=o==null?void 0:o.requestHeader)==null?void 0:O.requestDesc,RequestStatus:(k=o==null?void 0:o.requestHeader)==null?void 0:k.requestStatus,FirstProd:((U=c==null?void 0:c.payloadData)==null?void 0:U.FirstProductionDate)||null,LaunchDate:((x=c==null?void 0:c.payloadData)==null?void 0:x.LaunchDate)||null,LeadingCat:(N=o==null?void 0:o.requestHeader)==null?void 0:N.leadingCat,Division:(D=o==null?void 0:o.requestHeader)==null?void 0:D.division,Region:(I=o==null?void 0:o.requestHeader)==null?void 0:I.region,TemplateName:(ge=o==null?void 0:o.requestHeader)==null?void 0:ge.templateName,FieldName:(ee=o==null?void 0:o.requestHeader)==null?void 0:ee.fieldName},Tochildrequestheaderdata:{},Toclientdata:{ClientId:null,Function:"UPD"},Material:p,MatlType:((Se=(M=f[p])==null?void 0:M[((Ue=f[p])==null?void 0:Ue.length)-1])==null?void 0:Se.MatlType)||"",TemplateName:u,IsFirstCreate:!0,Function:"UPD",changeLogData:se(p),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}},g=J=>{const f=t.reduce((z,j)=>{if((m==null?void 0:m.length)!==0&&!(m!=null&&m.includes(j==null?void 0:j.id)))return z;const p=j==null?void 0:j.Material;return z[p]||(z[p]=[]),z[p].push(j),z},{});if(J){const z=["id","MaterialId","ClientId","slNo","ChangeLogId","MatlType"];return Object.keys(f).map(p=>{var S,L,A,O,k,U,x,N,D,I,ge,ee,Ue,M,Se,ce,$;const T=f[p],{MaterialId:v,ClientId:C}=T[0];return{MaterialId:v,Material:p,MassChildEditId:s==null?void 0:s.otherPayloadData.MassChildEditId,MatlType:((A=(L=f[p])==null?void 0:L[((S=f[p])==null?void 0:S.length)-1])==null?void 0:A.MatlType)||"",Function:"UPD",TaskId:(s==null?void 0:s.otherPayloadData.TaskId)||"",TaskName:(s==null?void 0:s.otherPayloadData.TaskName)||"",creationTime:(s==null?void 0:s.otherPayloadData.CreationTime)||"",dueDate:(s==null?void 0:s.otherPayloadData.DueDate)||"",IsFirstCreate:!1,MassEditId:s==null?void 0:s.otherPayloadData.MassEditId,TotalIntermediateTasks:s==null?void 0:s.otherPayloadData.TotalIntermediateTasks,IntermediateTaskCount:s==null?void 0:s.otherPayloadData.IntermediateTaskCount,Tosalesdata:T.map(qe=>{const F={...qe,Function:"UPD"};return z.forEach(Ke=>delete F[Ke]),F}),Tochildrequestheaderdata:{ChildRequestId:((O=s==null?void 0:s.childRequestHeaderData)==null?void 0:O.ChildRequestId)||null,MaterialGroupType:((k=s==null?void 0:s.childRequestHeaderData)==null?void 0:k.MaterialGroupType)||null,TaskId:(l==null?void 0:l.taskId)||null,Comments:(s==null?void 0:s.Comments)||"",TotalIntermediateTasks:((U=s==null?void 0:s.childRequestHeaderData)==null?void 0:U.TotalIntermediateTasks)||null,IntermediateTaskCount:((x=s==null?void 0:s.childRequestHeaderData)==null?void 0:x.IntermediateTaskCount)||null,ReqCreatedBy:((N=s==null?void 0:s.childRequestHeaderData)==null?void 0:N.ReqCreatedBy)||null,ReqCreatedOn:((D=s==null?void 0:s.childRequestHeaderData)==null?void 0:D.ReqCreatedOn)||null,ReqUpdatedOn:((I=s==null?void 0:s.childRequestHeaderData)==null?void 0:I.ReqUpdatedOn)||null,RequestType:((ge=s==null?void 0:s.childRequestHeaderData)==null?void 0:ge.RequestType)||null,RequestPrefix:((ee=s==null?void 0:s.childRequestHeaderData)==null?void 0:ee.RequestPrefix)||null,RequestDesc:((Ue=s==null?void 0:s.childRequestHeaderData)==null?void 0:Ue.RequestDesc)||null,RequestPriority:((M=s==null?void 0:s.childRequestHeaderData)==null?void 0:M.RequestPriority)||null,RequestStatus:((Se=s==null?void 0:s.childRequestHeaderData)==null?void 0:Se.RequestStatus)||null,CurrentLevel:(l==null?void 0:l.ATTRIBUTE_3)||"",CurrentLevelName:(l==null?void 0:l.ATTRIBUTE_4)||"",ParticularLevel:(s==null?void 0:s.Level)||"-1",TaskName:(l==null?void 0:l.taskDesc)||"",ApproverGroup:(l==null?void 0:l.ATTRIBUTE_5)||""},Torequestheaderdata:(s==null?void 0:s.requestHeaderData)||{},Tomaterialerrordata:(s==null?void 0:s.errorData[p])||{},TemplateName:u,changeLogData:se(p,($=(ce=f[p])==null?void 0:ce[0])==null?void 0:$.ChangeLogId),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}else{const z=["id","slNo","MatlType"];return Object.keys(f).map(p=>{var T,v,C,S,L,A,O,k,U,x,N,D,I,ge,ee,Ue,M,Se;return{Tosalesdata:f[p].map(ce=>{const $={...ce,Function:"UPD"};return z.forEach(qe=>delete $[qe]),$}),Torequestheaderdata:{RequestId:(T=o==null?void 0:o.requestHeader)==null?void 0:T.requestId,ReqCreatedBy:(v=o==null?void 0:o.requestHeader)==null?void 0:v.reqCreatedBy,ReqCreatedOn:Is((C=o==null?void 0:o.requestHeader)==null?void 0:C.reqCreatedOn),ReqUpdatedOn:Is((S=o==null?void 0:o.requestHeader)==null?void 0:S.reqCreatedOn),RequestType:(L=o==null?void 0:o.requestHeader)==null?void 0:L.requestType,RequestPriority:(A=o==null?void 0:o.requestHeader)==null?void 0:A.requestPriority,RequestDesc:(O=o==null?void 0:o.requestHeader)==null?void 0:O.requestDesc,RequestStatus:(k=o==null?void 0:o.requestHeader)==null?void 0:k.requestStatus,FirstProd:((U=c==null?void 0:c.payloadData)==null?void 0:U.FirstProductionDate)||null,LaunchDate:((x=c==null?void 0:c.payloadData)==null?void 0:x.LaunchDate)||null,LeadingCat:(N=o==null?void 0:o.requestHeader)==null?void 0:N.leadingCat,Division:(D=o==null?void 0:o.requestHeader)==null?void 0:D.division,Region:(I=o==null?void 0:o.requestHeader)==null?void 0:I.region,TemplateName:(ge=o==null?void 0:o.requestHeader)==null?void 0:ge.templateName,FieldName:(ee=o==null?void 0:o.requestHeader)==null?void 0:ee.fieldName},Tochildrequestheaderdata:{},Material:p,MatlType:((Se=(M=f[p])==null?void 0:M[((Ue=f[p])==null?void 0:Ue.length)-1])==null?void 0:Se.MatlType)||"",TemplateName:u,IsFirstCreate:!0,Function:"UPD",changeLogData:se(p),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}},V=J=>{if(J){const f={},z=["id","slNo","type","MaterialId","Plant","ChangeLogId","MatlType"],j=["id","slNo","type","MaterialId","ChangeLogId"];return Object.keys(t).forEach(T=>{t[T].forEach(v=>{const{Material:C,MaterialId:S,ChangeLogId:L}=v;f[C]||(f[C]={Toclientdata:null,Toplantdata:[],MaterialId:S,ChangeLogId:L,MatlType:""});const A={...v};T==="Basic Data"&&!f[C].Toclientdata?(f[C].MatlType=(A==null?void 0:A.MatlType)||"",z.forEach(O=>delete A[O]),f[C].Toclientdata=A):T==="Plant Data"&&(j.forEach(O=>delete A[O]),f[C].Toplantdata.push(A))})}),Object.keys(f).map(T=>{var v,C,S,L,A,O,k,U,x,N,D,I,ge;return{...f[T],Material:T,Function:"UPD",MassChildEditId:s==null?void 0:s.otherPayloadData.MassChildEditId,TaskId:(s==null?void 0:s.otherPayloadData.TaskId)||"",TaskName:(s==null?void 0:s.otherPayloadData.TaskName)||"",creationTime:(s==null?void 0:s.otherPayloadData.CreationTime)||"",dueDate:(s==null?void 0:s.otherPayloadData.DueDate)||"",IsFirstCreate:!1,MassEditId:s==null?void 0:s.otherPayloadData.MassEditId,TotalIntermediateTasks:s==null?void 0:s.otherPayloadData.TotalIntermediateTasks,IntermediateTaskCount:s==null?void 0:s.otherPayloadData.IntermediateTaskCount,Torequestheaderdata:(s==null?void 0:s.requestHeaderData)||{},Tomaterialerrordata:(s==null?void 0:s.errorData[T])||{},TemplateName:u,Tochildrequestheaderdata:{ChildRequestId:((v=s==null?void 0:s.childRequestHeaderData)==null?void 0:v.ChildRequestId)||null,MaterialGroupType:((C=s==null?void 0:s.childRequestHeaderData)==null?void 0:C.MaterialGroupType)||null,TaskId:(l==null?void 0:l.taskId)||null,Comments:(s==null?void 0:s.Comments)||"",TotalIntermediateTasks:((S=s==null?void 0:s.childRequestHeaderData)==null?void 0:S.TotalIntermediateTasks)||null,IntermediateTaskCount:((L=s==null?void 0:s.childRequestHeaderData)==null?void 0:L.IntermediateTaskCount)||null,ReqCreatedBy:((A=s==null?void 0:s.childRequestHeaderData)==null?void 0:A.ReqCreatedBy)||null,ReqCreatedOn:((O=s==null?void 0:s.childRequestHeaderData)==null?void 0:O.ReqCreatedOn)||null,ReqUpdatedOn:((k=s==null?void 0:s.childRequestHeaderData)==null?void 0:k.ReqUpdatedOn)||null,RequestType:((U=s==null?void 0:s.childRequestHeaderData)==null?void 0:U.RequestType)||null,RequestPrefix:((x=s==null?void 0:s.childRequestHeaderData)==null?void 0:x.RequestPrefix)||null,RequestDesc:((N=s==null?void 0:s.childRequestHeaderData)==null?void 0:N.RequestDesc)||null,RequestPriority:((D=s==null?void 0:s.childRequestHeaderData)==null?void 0:D.RequestPriority)||null,RequestStatus:((I=s==null?void 0:s.childRequestHeaderData)==null?void 0:I.RequestStatus)||null,CurrentLevel:(l==null?void 0:l.ATTRIBUTE_3)||"",CurrentLevelName:(l==null?void 0:l.ATTRIBUTE_4)||"",ParticularLevel:(s==null?void 0:s.Level)||"-1",TaskName:(l==null?void 0:l.taskDesc)||"",ApproverGroup:(l==null?void 0:l.ATTRIBUTE_5)||""},changeLogData:se(T,(ge=f[T])==null?void 0:ge.ChangeLogId),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}else{const f={},z=["id","slNo","type","Plant","MatlType"],j=["id","slNo","type"];return Object.keys(t).forEach(T=>{t[T].forEach(v=>{const{Material:C}=v;f[C]||(f[C]={Toclientdata:null,Toplantdata:[],MatlType:""});const S={...v};T==="Basic Data"&&!f[C].Toclientdata?(f[C].MatlType=(S==null?void 0:S.MatlType)||"",z.forEach(L=>delete S[L]),f[C].Toclientdata={...S,Function:"UPD"}):T==="Plant Data"&&(j.forEach(L=>delete S[L]),f[C].Toplantdata.push({...S,Function:"UPD"}))})}),Object.keys(f).map(T=>{var v,C,S,L,A,O,k,U,x,N,D,I,ge,ee,Ue,M,Se;return{...f[T],Torequestheaderdata:{RequestId:(v=o==null?void 0:o.requestHeader)==null?void 0:v.requestId,ReqCreatedBy:(C=o==null?void 0:o.requestHeader)==null?void 0:C.reqCreatedBy,ReqCreatedOn:Is((S=o==null?void 0:o.requestHeader)==null?void 0:S.reqCreatedOn),ReqUpdatedOn:Is((L=o==null?void 0:o.requestHeader)==null?void 0:L.reqCreatedOn),RequestType:(A=o==null?void 0:o.requestHeader)==null?void 0:A.requestType,RequestPriority:(O=o==null?void 0:o.requestHeader)==null?void 0:O.requestPriority,RequestDesc:(k=o==null?void 0:o.requestHeader)==null?void 0:k.requestDesc,RequestStatus:(U=o==null?void 0:o.requestHeader)==null?void 0:U.requestStatus,FirstProd:((x=c==null?void 0:c.payloadData)==null?void 0:x.FirstProductionDate)||null,LaunchDate:((N=c==null?void 0:c.payloadData)==null?void 0:N.LaunchDate)||null,LeadingCat:(D=o==null?void 0:o.requestHeader)==null?void 0:D.leadingCat,Division:(I=o==null?void 0:o.requestHeader)==null?void 0:I.division,Region:(ge=o==null?void 0:o.requestHeader)==null?void 0:ge.region,TemplateName:(ee=o==null?void 0:o.requestHeader)==null?void 0:ee.templateName,FieldName:(Ue=o==null?void 0:o.requestHeader)==null?void 0:Ue.fieldName},Tochildrequestheaderdata:{},Material:T,TemplateName:(M=o==null?void 0:o.requestHeader)==null?void 0:M.templateName,IsFirstCreate:!0,Function:"UPD",MassEditId:(Se=o==null?void 0:o.requestHeader)==null?void 0:Se.requestId,changeLogData:se(T),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}},Q=J=>{if(J){const f={},z=["id","slNo","type","MaterialId","Plant","ChangeLogId","MatlType"],j=["id","slNo","type","MaterialId","ChangeLogId"],p=["id","slNo","type","MaterialId","ChangeLogId"];return Object.keys(t).forEach(v=>{t[v].forEach(C=>{const{Material:S,MaterialId:L,ChangeLogId:A}=C;f[S]||(f[S]={Toclientdata:null,Toplantdata:[],Tosalesdata:[],MaterialId:L,ChangeLogId:A,MatlType:""});const O={...C};v==="Basic Data"&&!f[S].Toclientdata?(f[S].MatlType=(O==null?void 0:O.MatlType)||"",z.forEach(k=>delete O[k]),f[S].Toclientdata=O):v==="Plant Data"?(j.forEach(k=>delete O[k]),f[S].Toplantdata.push(O)):v==="Sales Data"&&(p.forEach(k=>delete O[k]),f[S].Tosalesdata.push(O))})}),Object.keys(f).map(v=>{var C,S,L,A,O,k,U,x,N,D,I,ge,ee;return{...f[v],Material:v,MassChildEditId:s==null?void 0:s.otherPayloadData.MassChildEditId,Function:"UPD",TaskId:(s==null?void 0:s.otherPayloadData.TaskId)||"",TaskName:(s==null?void 0:s.otherPayloadData.TaskName)||"",creationTime:(s==null?void 0:s.otherPayloadData.CreationTime)||"",dueDate:(s==null?void 0:s.otherPayloadData.DueDate)||"",IsFirstCreate:!1,MassEditId:s==null?void 0:s.otherPayloadData.MassEditId,TotalIntermediateTasks:s==null?void 0:s.otherPayloadData.TotalIntermediateTasks,IntermediateTaskCount:s==null?void 0:s.otherPayloadData.IntermediateTaskCount,Torequestheaderdata:(s==null?void 0:s.requestHeaderData)||{},Tomaterialerrordata:(s==null?void 0:s.errorData[v])||{},TemplateName:u,Tochildrequestheaderdata:{ChildRequestId:((C=s==null?void 0:s.childRequestHeaderData)==null?void 0:C.ChildRequestId)||null,MaterialGroupType:((S=s==null?void 0:s.childRequestHeaderData)==null?void 0:S.MaterialGroupType)||null,TaskId:(l==null?void 0:l.taskId)||null,Comments:(s==null?void 0:s.Comments)||"",TotalIntermediateTasks:((L=s==null?void 0:s.childRequestHeaderData)==null?void 0:L.TotalIntermediateTasks)||null,IntermediateTaskCount:((A=s==null?void 0:s.childRequestHeaderData)==null?void 0:A.IntermediateTaskCount)||null,ReqCreatedBy:((O=s==null?void 0:s.childRequestHeaderData)==null?void 0:O.ReqCreatedBy)||null,ReqCreatedOn:((k=s==null?void 0:s.childRequestHeaderData)==null?void 0:k.ReqCreatedOn)||null,ReqUpdatedOn:((U=s==null?void 0:s.childRequestHeaderData)==null?void 0:U.ReqUpdatedOn)||null,RequestType:((x=s==null?void 0:s.childRequestHeaderData)==null?void 0:x.RequestType)||null,RequestPrefix:((N=s==null?void 0:s.childRequestHeaderData)==null?void 0:N.RequestPrefix)||null,RequestDesc:((D=s==null?void 0:s.childRequestHeaderData)==null?void 0:D.RequestDesc)||null,RequestPriority:((I=s==null?void 0:s.childRequestHeaderData)==null?void 0:I.RequestPriority)||null,RequestStatus:((ge=s==null?void 0:s.childRequestHeaderData)==null?void 0:ge.RequestStatus)||null,CurrentLevel:(l==null?void 0:l.ATTRIBUTE_3)||"",CurrentLevelName:(l==null?void 0:l.ATTRIBUTE_4)||"",ParticularLevel:(s==null?void 0:s.Level)||"-1",TaskName:(l==null?void 0:l.taskDesc)||"",ApproverGroup:(l==null?void 0:l.ATTRIBUTE_5)||""},changeLogData:se(v,(ee=f[v])==null?void 0:ee.ChangeLogId),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}else{const f={},z=["id","slNo","type","Plant","MatlType"],j=["id","slNo","type"],p=["id","slNo","type"];return Object.keys(t).forEach(v=>{t[v].forEach(C=>{const{Material:S}=C;f[S]||(f[S]={Toclientdata:null,Toplantdata:[],Tosalesdata:[],MatlType:""});const L={...C};v==="Basic Data"&&!f[S].Toclientdata?(f[S].MatlType=(L==null?void 0:L.MatlType)||"",z.forEach(A=>delete L[A]),f[S].Toclientdata={...L,Function:"UPD"}):v==="Plant Data"?(j.forEach(A=>delete L[A]),f[S].Toplantdata.push({...L,Function:"UPD"})):v==="Sales Data"&&(p.forEach(A=>delete L[A]),f[S].Tosalesdata.push({...L,Function:"UPD"}))})}),Object.keys(f).map(v=>{var C,S,L,A,O,k,U,x,N,D,I,ge,ee,Ue,M,Se,ce;return{...f[v],Torequestheaderdata:{RequestId:(C=o==null?void 0:o.requestHeader)==null?void 0:C.requestId,ReqCreatedBy:(S=o==null?void 0:o.requestHeader)==null?void 0:S.reqCreatedBy,ReqCreatedOn:Is((L=o==null?void 0:o.requestHeader)==null?void 0:L.reqCreatedOn),ReqUpdatedOn:Is((A=o==null?void 0:o.requestHeader)==null?void 0:A.reqCreatedOn),RequestType:(O=o==null?void 0:o.requestHeader)==null?void 0:O.requestType,RequestPriority:(k=o==null?void 0:o.requestHeader)==null?void 0:k.requestPriority,RequestDesc:(U=o==null?void 0:o.requestHeader)==null?void 0:U.requestDesc,RequestStatus:(x=o==null?void 0:o.requestHeader)==null?void 0:x.requestStatus,FirstProd:((N=c==null?void 0:c.payloadData)==null?void 0:N.FirstProductionDate)||null,LaunchDate:((D=c==null?void 0:c.payloadData)==null?void 0:D.LaunchDate)||null,LeadingCat:(I=o==null?void 0:o.requestHeader)==null?void 0:I.leadingCat,Division:(ge=o==null?void 0:o.requestHeader)==null?void 0:ge.division,Region:(ee=o==null?void 0:o.requestHeader)==null?void 0:ee.region,TemplateName:(Ue=o==null?void 0:o.requestHeader)==null?void 0:Ue.templateName,FieldName:(M=o==null?void 0:o.requestHeader)==null?void 0:M.fieldName},Tochildrequestheaderdata:{},Material:v,TemplateName:(Se=o==null?void 0:o.requestHeader)==null?void 0:Se.templateName,IsFirstCreate:!0,Function:"UPD",MassEditId:(ce=o==null?void 0:o.requestHeader)==null?void 0:ce.requestId,changeLogData:se(v),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}},H=J=>{if(J){const f={},z=["id","slNo","type","MaterialId","Plant","ChangeLogId","MatlType"],j=["id","slNo","type","MaterialId","ChangeLogId"],p=["id","slNo","type","MaterialId","ChangeLogId"],T=["id","slNo","type","MaterialId","ChangeLogId"];return Object.keys(t).forEach(C=>{t[C].forEach(S=>{const{Material:L,MaterialId:A,ChangeLogId:O}=S;f[L]||(f[L]={Toclientdata:null,Toplantdata:[],Tosalesdata:[],Tomaterialdescription:[],MaterialId:A,ChangeLogId:O,MatlType:""});const k={...S};C==="Basic Data"&&!f[L].Toclientdata?(f[L].MatlType=(k==null?void 0:k.MatlType)||"",z.forEach(U=>delete k[U]),f[L].Toclientdata=k):C==="Plant Data"?(j.forEach(U=>delete k[U]),f[L].Toplantdata.push(k)):C==="Sales Data"?(p.forEach(U=>delete k[U]),f[L].Tosalesdata.push(k)):C==="Description"&&(T.forEach(U=>delete k[U]),f[L].Tomaterialdescription.push(k))})}),Object.keys(f).map(C=>{var S,L,A,O,k,U,x,N,D,I,ge,ee,Ue;return{...f[C],Material:C,MassChildEditId:s==null?void 0:s.otherPayloadData.MassChildEditId,Function:"UPD",TaskId:(s==null?void 0:s.otherPayloadData.TaskId)||"",TaskName:(s==null?void 0:s.otherPayloadData.TaskName)||"",creationTime:(s==null?void 0:s.otherPayloadData.CreationTime)||"",dueDate:(s==null?void 0:s.otherPayloadData.DueDate)||"",IsFirstCreate:!1,MassEditId:s==null?void 0:s.otherPayloadData.MassEditId,TotalIntermediateTasks:s==null?void 0:s.otherPayloadData.TotalIntermediateTasks,IntermediateTaskCount:s==null?void 0:s.otherPayloadData.IntermediateTaskCount,Torequestheaderdata:(s==null?void 0:s.requestHeaderData)||{},Tomaterialerrordata:(s==null?void 0:s.errorData[C])||{},TemplateName:u,Tochildrequestheaderdata:{ChildRequestId:((S=s==null?void 0:s.childRequestHeaderData)==null?void 0:S.ChildRequestId)||null,MaterialGroupType:((L=s==null?void 0:s.childRequestHeaderData)==null?void 0:L.MaterialGroupType)||null,TaskId:(l==null?void 0:l.taskId)||null,Comments:(s==null?void 0:s.Comments)||"",TotalIntermediateTasks:((A=s==null?void 0:s.childRequestHeaderData)==null?void 0:A.TotalIntermediateTasks)||null,IntermediateTaskCount:((O=s==null?void 0:s.childRequestHeaderData)==null?void 0:O.IntermediateTaskCount)||null,ReqCreatedBy:((k=s==null?void 0:s.childRequestHeaderData)==null?void 0:k.ReqCreatedBy)||null,ReqCreatedOn:((U=s==null?void 0:s.childRequestHeaderData)==null?void 0:U.ReqCreatedOn)||null,ReqUpdatedOn:((x=s==null?void 0:s.childRequestHeaderData)==null?void 0:x.ReqUpdatedOn)||null,RequestType:((N=s==null?void 0:s.childRequestHeaderData)==null?void 0:N.RequestType)||null,RequestPrefix:((D=s==null?void 0:s.childRequestHeaderData)==null?void 0:D.RequestPrefix)||null,RequestDesc:((I=s==null?void 0:s.childRequestHeaderData)==null?void 0:I.RequestDesc)||null,RequestPriority:((ge=s==null?void 0:s.childRequestHeaderData)==null?void 0:ge.RequestPriority)||null,RequestStatus:((ee=s==null?void 0:s.childRequestHeaderData)==null?void 0:ee.RequestStatus)||null,CurrentLevel:(l==null?void 0:l.ATTRIBUTE_3)||"",CurrentLevelName:(l==null?void 0:l.ATTRIBUTE_4)||"",ParticularLevel:(s==null?void 0:s.Level)||"-1",TaskName:(l==null?void 0:l.taskDesc)||"",ApproverGroup:(l==null?void 0:l.ATTRIBUTE_5)||""},changeLogData:se(C,(Ue=f[C])==null?void 0:Ue.ChangeLogId),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}else{const f={},z=["id","slNo","type","Plant","MatlType"],j=["id","slNo","type"],p=["id","slNo","type"],T=["id","slNo","type"];return Object.keys(t).forEach(C=>{t[C].forEach(S=>{const{Material:L}=S;f[L]||(f[L]={Toclientdata:null,Toplantdata:[],Tosalesdata:[],Tomaterialdescription:[],MatlType:""});const A={...S};C==="Basic Data"&&!f[L].Toclientdata?(f[L].MatlType=(A==null?void 0:A.MatlType)||"",z.forEach(O=>delete A[O]),f[L].Toclientdata={...A,Function:"UPD"}):C==="Plant Data"?(j.forEach(O=>delete A[O]),f[L].Toplantdata.push({...A,Function:"UPD"})):C==="Sales Data"?(p.forEach(O=>delete A[O]),f[L].Tosalesdata.push({...A,Function:"UPD"})):C==="Description"&&(T.forEach(O=>delete A[O]),f[L].Tomaterialdescription.push({...A,Function:"UPD"}))})}),Object.keys(f).map(C=>{var S,L,A,O,k,U,x,N,D,I,ge,ee,Ue,M,Se,ce,$;return{...f[C],Torequestheaderdata:{RequestId:(S=o==null?void 0:o.requestHeader)==null?void 0:S.requestId,ReqCreatedBy:(L=o==null?void 0:o.requestHeader)==null?void 0:L.reqCreatedBy,ReqCreatedOn:Is((A=o==null?void 0:o.requestHeader)==null?void 0:A.reqCreatedOn),ReqUpdatedOn:Is((O=o==null?void 0:o.requestHeader)==null?void 0:O.reqCreatedOn),RequestType:(k=o==null?void 0:o.requestHeader)==null?void 0:k.requestType,RequestPriority:(U=o==null?void 0:o.requestHeader)==null?void 0:U.requestPriority,RequestDesc:(x=o==null?void 0:o.requestHeader)==null?void 0:x.requestDesc,RequestStatus:(N=o==null?void 0:o.requestHeader)==null?void 0:N.requestStatus,FirstProd:((D=c==null?void 0:c.payloadData)==null?void 0:D.FirstProductionDate)||null,LaunchDate:((I=c==null?void 0:c.payloadData)==null?void 0:I.LaunchDate)||null,LeadingCat:(ge=o==null?void 0:o.requestHeader)==null?void 0:ge.leadingCat,Division:(ee=o==null?void 0:o.requestHeader)==null?void 0:ee.division,Region:(Ue=o==null?void 0:o.requestHeader)==null?void 0:Ue.region,TemplateName:(M=o==null?void 0:o.requestHeader)==null?void 0:M.templateName,FieldName:(Se=o==null?void 0:o.requestHeader)==null?void 0:Se.fieldName},Tochildrequestheaderdata:{},Material:C,TemplateName:(ce=o==null?void 0:o.requestHeader)==null?void 0:ce.templateName,IsFirstCreate:!0,Function:"UPD",MassEditId:($=o==null?void 0:o.requestHeader)==null?void 0:$.requestId,changeLogData:se(C),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}},he=J=>{const f=t.reduce((z,j)=>{if((m==null?void 0:m.length)!==0&&!(m!=null&&m.includes(j==null?void 0:j.id)))return z;const p=j==null?void 0:j.Material;return z[p]||(z[p]=[]),z[p].push(j),z},{});if(J){const z=["id","MaterialId","ClientId","slNo","ChangeLogId","MatlType"];return Object.keys(f).map(p=>{var S,L,A,O,k,U,x,N,D,I,ge,ee,Ue,M,Se,ce,$;const T=f[p],{MaterialId:v,ClientId:C}=T[(T==null?void 0:T.length)-1];return{MaterialId:v,Material:p,MassChildEditId:s==null?void 0:s.otherPayloadData.MassChildEditId,MatlType:((A=(L=f[p])==null?void 0:L[((S=f[p])==null?void 0:S.length)-1])==null?void 0:A.MatlType)||"",Function:"UPD",TaskId:(s==null?void 0:s.otherPayloadData.TaskId)||"",TaskName:(s==null?void 0:s.otherPayloadData.TaskName)||"",creationTime:(s==null?void 0:s.otherPayloadData.CreationTime)||"",dueDate:(s==null?void 0:s.otherPayloadData.DueDate)||"",IsFirstCreate:!1,MassEditId:s==null?void 0:s.otherPayloadData.MassEditId,TotalIntermediateTasks:s==null?void 0:s.otherPayloadData.TotalIntermediateTasks,IntermediateTaskCount:s==null?void 0:s.otherPayloadData.IntermediateTaskCount,Toclientdata:{ClientId:C,Material:p,Function:"UPD"},Tochildrequestheaderdata:{ChildRequestId:((O=s==null?void 0:s.childRequestHeaderData)==null?void 0:O.ChildRequestId)||null,MaterialGroupType:((k=s==null?void 0:s.childRequestHeaderData)==null?void 0:k.MaterialGroupType)||null,TaskId:(l==null?void 0:l.taskId)||null,Comments:(s==null?void 0:s.Comments)||"",TotalIntermediateTasks:((U=s==null?void 0:s.childRequestHeaderData)==null?void 0:U.TotalIntermediateTasks)||null,IntermediateTaskCount:((x=s==null?void 0:s.childRequestHeaderData)==null?void 0:x.IntermediateTaskCount)||null,ReqCreatedBy:((N=s==null?void 0:s.childRequestHeaderData)==null?void 0:N.ReqCreatedBy)||null,ReqCreatedOn:((D=s==null?void 0:s.childRequestHeaderData)==null?void 0:D.ReqCreatedOn)||null,ReqUpdatedOn:((I=s==null?void 0:s.childRequestHeaderData)==null?void 0:I.ReqUpdatedOn)||null,RequestType:((ge=s==null?void 0:s.childRequestHeaderData)==null?void 0:ge.RequestType)||null,RequestPrefix:((ee=s==null?void 0:s.childRequestHeaderData)==null?void 0:ee.RequestPrefix)||null,RequestDesc:((Ue=s==null?void 0:s.childRequestHeaderData)==null?void 0:Ue.RequestDesc)||null,RequestPriority:((M=s==null?void 0:s.childRequestHeaderData)==null?void 0:M.RequestPriority)||null,RequestStatus:((Se=s==null?void 0:s.childRequestHeaderData)==null?void 0:Se.RequestStatus)||null,CurrentLevel:(l==null?void 0:l.ATTRIBUTE_3)||"",CurrentLevelName:(l==null?void 0:l.ATTRIBUTE_4)||"",ParticularLevel:(s==null?void 0:s.Level)||"-1",TaskName:(l==null?void 0:l.taskDesc)||"",ApproverGroup:(l==null?void 0:l.ATTRIBUTE_5)||""},Tomaterialdescription:T.map(qe=>{const F={...qe,Function:"UPD"};return z.forEach(Ke=>delete F[Ke]),F}),Torequestheaderdata:(s==null?void 0:s.requestHeaderData)||{},Tomaterialerrordata:(s==null?void 0:s.errorData[p])||{},TemplateName:u,...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""},changeLogData:se(p,($=(ce=f[p])==null?void 0:ce[0])==null?void 0:$.ChangeLogId)}})}else{const z=["id","slNo","MatlType"];return Object.keys(f).map(p=>{var T,v,C,S,L,A,O,k,U,x,N,D,I,ge,ee,Ue,M,Se;return{Tomaterialdescription:f[p].map(ce=>{const $={...ce,Function:"UPD"};return z.forEach(qe=>delete $[qe]),$}),Torequestheaderdata:{RequestId:(T=o==null?void 0:o.requestHeader)==null?void 0:T.requestId,ReqCreatedBy:(v=o==null?void 0:o.requestHeader)==null?void 0:v.reqCreatedBy,ReqCreatedOn:Is((C=o==null?void 0:o.requestHeader)==null?void 0:C.reqCreatedOn),ReqUpdatedOn:Is((S=o==null?void 0:o.requestHeader)==null?void 0:S.reqCreatedOn),RequestType:(L=o==null?void 0:o.requestHeader)==null?void 0:L.requestType,RequestPriority:(A=o==null?void 0:o.requestHeader)==null?void 0:A.requestPriority,RequestDesc:(O=o==null?void 0:o.requestHeader)==null?void 0:O.requestDesc,RequestStatus:(k=o==null?void 0:o.requestHeader)==null?void 0:k.requestStatus,FirstProd:((U=c==null?void 0:c.payloadData)==null?void 0:U.FirstProductionDate)||null,LaunchDate:((x=c==null?void 0:c.payloadData)==null?void 0:x.LaunchDate)||null,LeadingCat:(N=o==null?void 0:o.requestHeader)==null?void 0:N.leadingCat,Division:(D=o==null?void 0:o.requestHeader)==null?void 0:D.division,Region:(I=o==null?void 0:o.requestHeader)==null?void 0:I.region,TemplateName:(ge=o==null?void 0:o.requestHeader)==null?void 0:ge.templateName,FieldName:(ee=o==null?void 0:o.requestHeader)==null?void 0:ee.fieldName},Tochildrequestheaderdata:{},Toclientdata:{ClientId:null,Function:"UPD"},Material:p,MatlType:((Se=(M=f[p])==null?void 0:M[((Ue=f[p])==null?void 0:Ue.length)-1])==null?void 0:Se.MatlType)||"",TemplateName:u,IsFirstCreate:!0,Function:"UPD",...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""},changeLogData:se(p)}})}},ne=J=>{const f=t.reduce((z,j)=>{if((m==null?void 0:m.length)!==0&&!(m!=null&&m.includes(j==null?void 0:j.id)))return z;const p=j==null?void 0:j.Material;return z[p]||(z[p]=[]),z[p].push(j),z},{});if(J){const z=["id","MaterialId","ClientId","slNo","ChangeLogId","MatlType"];return Object.keys(f).map(p=>{var C,S,L,A,O,k,U,x,N,D,I,ge,ee,Ue,M,Se,ce;const T=f[p],{MaterialId:v}=T[0];return{MaterialId:v,Material:p,MassChildEditId:s==null?void 0:s.otherPayloadData.MassChildEditId,MatlType:((L=(S=f[p])==null?void 0:S[((C=f[p])==null?void 0:C.length)-1])==null?void 0:L.MatlType)||"",Function:"UPD",TaskId:(s==null?void 0:s.otherPayloadData.TaskId)||"",TaskName:(s==null?void 0:s.otherPayloadData.TaskName)||"",creationTime:(s==null?void 0:s.otherPayloadData.CreationTime)||"",dueDate:(s==null?void 0:s.otherPayloadData.DueDate)||"",IsFirstCreate:!1,MassEditId:s==null?void 0:s.otherPayloadData.MassEditId,TotalIntermediateTasks:s==null?void 0:s.otherPayloadData.TotalIntermediateTasks,IntermediateTaskCount:s==null?void 0:s.otherPayloadData.IntermediateTaskCount,Towarehousedata:T.map($=>{const qe={...$,Function:"UPD"};return z.forEach(F=>delete qe[F]),qe}),Tochildrequestheaderdata:{...s==null?void 0:s.childRequestHeaderData,ChildRequestId:((A=s==null?void 0:s.childRequestHeaderData)==null?void 0:A.ChildRequestId)||null,MaterialGroupType:((O=s==null?void 0:s.childRequestHeaderData)==null?void 0:O.MaterialGroupType)||null,TaskId:(l==null?void 0:l.taskId)||null,Comments:(s==null?void 0:s.Comments)||"",TotalIntermediateTasks:((k=s==null?void 0:s.childRequestHeaderData)==null?void 0:k.TotalIntermediateTasks)||null,IntermediateTaskCount:((U=s==null?void 0:s.childRequestHeaderData)==null?void 0:U.IntermediateTaskCount)||null,ReqCreatedBy:((x=s==null?void 0:s.childRequestHeaderData)==null?void 0:x.ReqCreatedBy)||null,ReqCreatedOn:((N=s==null?void 0:s.childRequestHeaderData)==null?void 0:N.ReqCreatedOn)||null,ReqUpdatedOn:((D=s==null?void 0:s.childRequestHeaderData)==null?void 0:D.ReqUpdatedOn)||null,RequestType:((I=s==null?void 0:s.childRequestHeaderData)==null?void 0:I.RequestType)||null,RequestPrefix:((ge=s==null?void 0:s.childRequestHeaderData)==null?void 0:ge.RequestPrefix)||null,RequestDesc:((ee=s==null?void 0:s.childRequestHeaderData)==null?void 0:ee.RequestDesc)||null,RequestPriority:((Ue=s==null?void 0:s.childRequestHeaderData)==null?void 0:Ue.RequestPriority)||null,RequestStatus:((M=s==null?void 0:s.childRequestHeaderData)==null?void 0:M.RequestStatus)||null,CurrentLevel:(l==null?void 0:l.ATTRIBUTE_3)||"",CurrentLevelName:(l==null?void 0:l.ATTRIBUTE_4)||"",ParticularLevel:(s==null?void 0:s.Level)||"-1",TaskName:(l==null?void 0:l.taskDesc)||"",ApproverGroup:(l==null?void 0:l.ATTRIBUTE_5)||""},Torequestheaderdata:(s==null?void 0:s.requestHeaderData)||{},Tomaterialerrordata:(s==null?void 0:s.errorData[p])||{},TemplateName:u,changeLogData:se(p,(ce=(Se=f[p])==null?void 0:Se[0])==null?void 0:ce.ChangeLogId),...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""}}})}else{const z=["id","slNo","MatlType"];return Object.keys(f).map(p=>{var T,v,C,S,L,A,O,k,U,x,N,D,I,ge,ee,Ue,M,Se;return{Towarehousedata:f[p].map(ce=>{const $={...ce,Function:"UPD"};return z.forEach(qe=>delete $[qe]),$}),Torequestheaderdata:{RequestId:(T=o==null?void 0:o.requestHeader)==null?void 0:T.requestId,ReqCreatedBy:(v=o==null?void 0:o.requestHeader)==null?void 0:v.reqCreatedBy,ReqCreatedOn:Is((C=o==null?void 0:o.requestHeader)==null?void 0:C.reqCreatedOn),ReqUpdatedOn:Is((S=o==null?void 0:o.requestHeader)==null?void 0:S.reqCreatedOn),RequestType:(L=o==null?void 0:o.requestHeader)==null?void 0:L.requestType,RequestPriority:(A=o==null?void 0:o.requestHeader)==null?void 0:A.requestPriority,RequestDesc:(O=o==null?void 0:o.requestHeader)==null?void 0:O.requestDesc,RequestStatus:(k=o==null?void 0:o.requestHeader)==null?void 0:k.requestStatus,FirstProd:((U=c==null?void 0:c.payloadData)==null?void 0:U.FirstProductionDate)||null,LaunchDate:((x=c==null?void 0:c.payloadData)==null?void 0:x.LaunchDate)||null,LeadingCat:(N=o==null?void 0:o.requestHeader)==null?void 0:N.leadingCat,Division:(D=o==null?void 0:o.requestHeader)==null?void 0:D.division,Region:(I=o==null?void 0:o.requestHeader)==null?void 0:I.region,TemplateName:(ge=o==null?void 0:o.requestHeader)==null?void 0:ge.templateName,FieldName:(ee=o==null?void 0:o.requestHeader)==null?void 0:ee.fieldName},Tochildrequestheaderdata:{},Material:p,MatlType:((Se=(M=f[p])==null?void 0:M[((Ue=f[p])==null?void 0:Ue.length)-1])==null?void 0:Se.MatlType)||"",TemplateName:u,IsFirstCreate:!0,Function:"UPD",...(s==null?void 0:s.Comments)&&{Comments:(s==null?void 0:s.Comments)||""},changeLogData:se(p)}})}};return{changePayloadForTemplate:ie}};var Ua={},eE=Go;Object.defineProperty(Ua,"__esModule",{value:!0});var sf=Ua.default=void 0,tE=eE(Bo()),sE=$o;sf=Ua.default=(0,tE.default)((0,sE.jsx)("path",{d:"m16 5-1.42 1.42-1.59-1.59V16h-1.98V4.83L9.42 6.42 8 5l4-4zm4 5v11c0 1.1-.9 2-2 2H6c-1.11 0-2-.9-2-2V10c0-1.11.89-2 2-2h3v2H6v11h12V10h-3V8h3c1.1 0 2 .89 2 2"}),"IosShareOutlined");const oE=zn(bs)(({theme:e})=>({padding:e.spacing(2),border:"none",backgroundColor:"rgba(179, 236, 243, 0.5)"})),nE=zn(ye)(({theme:e})=>{var t,r;return{backgroundColor:(r=(t=Be)==null?void 0:t.primary)==null?void 0:r.whiteSmoke,padding:e.spacing(1),border:"1px solid #E0E0E0",borderRadius:e.shape.borderRadius,boxShadow:"0px 8px 15px rgba(0, 0, 0, 0.08), 0px 4px 6px rgba(115, 118, 122, 0.5)",minWidth:120,textAlign:"center",fontWeight:"bold",color:e.palette.text.primary}}),rE=zn(ye)(({theme:e})=>({display:"flex",justifyContent:"space-between",alignItems:"center",paddingLeft:e.spacing(2),backgroundColor:e.palette.grey[100],borderBottom:`1px solid ${e.palette.divider}`})),lE=zn(Ia)(({theme:e})=>({borderRadius:e.shape.borderRadius,boxShadow:"0 4px 6px rgba(0, 0, 0, 0.1)"})),ah=4,iE=({open:e=!1,onClose:t=()=>{},handleOk:r=()=>{},message:o=""})=>{const c=oe(ie=>ie.payload.matNoList||[]),s=(c==null?void 0:c.length)||0,m=d.useMemo(()=>{const ie=[];for(let q=0;q<c.length;q+=ah)ie.push(c.slice(q,q+ah));return ie},[c]),l=()=>{const ie=c==null?void 0:c.map((q,g)=>({id:g+1,material:q}));u.convertJsonToExcel(ie)},u={convertJsonToExcel:ie=>{let q=[];q.push({header:"Material",key:"material"}),Ra({fileName:"Material List",columns:q,rows:ie})}},se=()=>{t()};return R(cn,{open:e,onClose:se,maxWidth:"md",PaperProps:{sx:{borderRadius:2,minWidth:"480px",maxHeight:"80vh"}},children:[R(Pn,{sx:{display:"flex",alignItems:"center",gap:1,py:2},children:[n(np,{fontSize:"medium",sx:{color:"0px 4px 6px rgba(115, 118, 122, 0.5)"}}),R(mt,{variant:"h6",fontWeight:"bold",children:["Info: ",o]})]}),R(Lo,{sx:{p:0},children:[R(rE,{children:[R(mt,{variant:"subtitle2",color:"text.secondary",sx:{marginLeft:"15px"},children:["Total Materials: ",n("strong",{children:s})]}),n(Ds,{onClick:l,color:"primary",sx:{marginRight:"10px"},title:"Export Excel",children:n(sf,{})})]}),n(ye,{sx:{pt:0,pl:2,pr:2,pb:0},children:n(lE,{component:Ur,children:n(va,{children:n(Ma,{children:m.map((ie,q)=>n(Ul,{sx:{"&:last-child td":{borderBottom:0}},children:n(oE,{children:n(ye,{sx:{display:"flex",flexWrap:"wrap",gap:2,justifyContent:"center"},children:ie.map(g=>n(nE,{children:g},g))})})},q))})})})})]}),R(Do,{sx:{p:2,borderTop:1,borderColor:"divider"},children:[n(It,{onClick:se,variant:"outlined",color:"warning",sx:{minWidth:100,textTransform:"none",fontWeight:"medium"},children:"Close"}),n(It,{onClick:r,variant:"contained",color:"primary",sx:{minWidth:100,textTransform:"none",fontWeight:"medium"},children:"Continue"})]})]})},ka=({initialReqScreen:e,isReqBench:t,remarks:r="",userInput:o="",selectedLevel:c=""})=>{var q;const s=oe(g=>g.payload),m=(q=s==null?void 0:s.payloadData)==null?void 0:q.RequestType,l=oe(g=>g.userManagement.taskData),u=oe(g=>g.request),se=oe(g=>g.changeLog.createChangeLogData);function ie(g){const V=[];return Object.keys(g).forEach(Q=>{var H,he,ne,J,f,z,j,p,T,v,C,S,L,A,O,k,U,x,N,D,I,ge,ee,Ue,M,Se,ce,$,qe,F,Ke,Us,Wt,$s,Fs,ss,io,Ws,ks,Qt,fs,Ht,ns,Zt,xs,lt,He,Rt,Ys,Bt,Me,it,Ut,Ns,ds,rs,at,yt,ws,Hs,os,ls,ao,Ye,Mt,gs,Lt,ps,Js,Bs,st,Ts,to,St,es,ot,Dt,Rs,ae,$e,me,Ne,Ve,Oe,ct,ke,Vt,Es,Pt,je,Pe,_t,jt,_s,Ft,te,xe,B,de,w,Te,We,le,dt,ze,Qe,Ct,_e,ut,ys,Xs,uo,us,co,Po,Qs,Ot,Gs,Zs,_o,Fo,Io,ho,is,Wo,so,qo,jo,zo,Yo,Xo,pe,ht,ft,At,ve,Nt,$t,kt,as,qs,gt,Gt,De,pt,ms,gn,yn,Sn,Nn,Yn,rr,lr,ir,E,_,W,re,Ae,Ge,Ze,et,wt,Tt,Kt,tt,hs,eo,zt,oo,no,Vo,pn,ar,Ir,Xl,Yr,Vl,Kl,Xr,Jl,Ql,Zl,ei,ti,si,oi,Vr,ni,Kr,Jr,ri,li,Qr,Zr,el,tl,sl,ol,nl,rl,ll,il,al,cl,dl,ul,hl,fl,gl,pl,i,h,y,G,Y,Z,K,ue,Le,Ce,be,Ie,nt,qt,Yt,Vs,ro,cs,vo,ln,an,wn,Rn,Ko,_n,In,vn,Xn,Vn,Kn,Jn,fo,Tn,Jo,nn,Qn,Zn,kn,Ls,So,Mo,pc,Tc,Ec,mc,Cc,Ac,bc,Sc,Nc,wc,Rc,_c,Ic,vc,Mc,Oc,xc,yc,Lc,Dc,Pc,qc,Uc,kc,Hc,Bc,Gc,$c,Fc,Wc,jc,zc,Yc,Xc,Vc,Kc,Jc,Qc,Zc,ed,td,sd,od,nd,rd,ld,id,ad,cd,dd,ud,hd,fd,gd,pd,Td,Ed,md,Cd,Ad,bd,Sd,Nd,wd,Rd,_d,Id,vd,Md,Od,xd,yd,Ld,Dd,Pd,qd,Ud,kd,Hd,Bd,Gd,$d,Fd,Wd,jd,zd,Yd,Xd,Vd,Kd,Jd,Qd,Zd,eu,tu,su,ou,nu,ru,lu,iu,au,cu,du,uu,hu,fu,gu,pu,Tu;if(Q.includes("-")||/\d/.test(Q)){const a=g[Q];if((H=a==null?void 0:a.headerData)!=null&&H.included){const ii=rp(((he=a==null?void 0:a.headerData)==null?void 0:he.orgData)||[]),gg=(((J=(ne=a==null?void 0:a.headerData)==null?void 0:ne.views)==null?void 0:J.filter(we=>!Ui.includes(we)))||[]).join(",").trim(),No=(f=a==null?void 0:a.payloadData)!=null&&f.Sales?Object.values(a.payloadData.Sales):[],Eu=new Set,pg=ii.filter(we=>{var xt,ts,bt;if(!((xt=we.salesOrg)!=null&&xt.code)||!((bt=(ts=we.dc)==null?void 0:ts.value)!=null&&bt.code))return!1;const rt=`${we.salesOrg.code}-${we.dc.value.code}`;return Eu.has(rt)?!1:(Eu.add(rt),!0)}).map((we,rt)=>{var xt,ts,bt,js,Ee,zs,Et,Qo,Cs,Xt,Tl,El,ml,Cl,Al,bl,Sl,Nl,wl,Rl,_l,Il,vl;return{SalesId:m===b.EXTEND&&e?null:((xt=No[rt])==null?void 0:xt.SalesId)||"",Function:"INS",Material:((ts=a.headerData)==null?void 0:ts.materialNumber)||"",SalesOrg:((bt=we.salesOrg)==null?void 0:bt.code)||"",DistrChan:((Ee=(js=we.dc)==null?void 0:js.value)==null?void 0:Ee.code)||"",DelFlag:!1,MatlStats:((zs=No[rt])==null?void 0:zs.MatlStats)||"",RebateGrp:((Et=No[rt])==null?void 0:Et.RebateGrp)||"",CashDisc:((Qo=No[rt])==null?void 0:Qo.CashDisc)||!0,SalStatus:((Cs=No[rt])==null?void 0:Cs.SalStatus)||"",DelyUnit:"0.000",ValidFrom:(Xt=No[rt])!=null&&Xt.ValidFrom?Is((Tl=No[rt])==null?void 0:Tl.ValidFrom):null,DelyUom:((El=No[rt])==null?void 0:El.DelyUom)||"",DelygPlnt:((ml=No[rt])==null?void 0:ml.DelygPlnt)||"",MatPrGrp:((Cl=No[rt])==null?void 0:Cl.MatPrGrp)||"",AcctAssgt:((Al=No[rt])==null?void 0:Al.AcctAssgt)||"",MatlGrp4:((bl=No[rt])==null?void 0:bl.MatlGrp4)||"",MatlGrp2:((Sl=No[rt])==null?void 0:Sl.MatlGrp2)||"",MatlGrp5:((Nl=No[rt])==null?void 0:Nl.MatlGrp5)||"",BatchMgmt:((wl=No[rt])==null?void 0:wl.BatchMgmt)||"",Countryori:((Rl=No[rt])==null?void 0:Rl.Countryori)||"",Depcountry:((_l=No[rt])==null?void 0:_l.Depcountry)||"",SalesUnit:((Il=No[rt])==null?void 0:Il.SalesUnit)||"",ItemCat:((vl=No[rt])==null?void 0:vl.ItemCat)||""}}),Tg=(z=a==null?void 0:a.payloadData)!=null&&z.Purchasing?Object.entries(a.payloadData.Purchasing):[],Eg=(j=a==null?void 0:a.payloadData)!=null&&j.MRP?Object.entries(a.payloadData.MRP):[],mg=(p=a==null?void 0:a.payloadData)!=null&&p[P.SALES_PLANT]?Object.entries((T=a.payloadData)==null?void 0:T[P.SALES_PLANT]):[],Cg=(v=a==null?void 0:a.payloadData)!=null&&v[P.STORAGE_PLANT]?Object.entries((C=a.payloadData)==null?void 0:C[P.STORAGE_PLANT]):[],Ag=(S=a==null?void 0:a.payloadData)!=null&&S.Accounting?Object.entries(a.payloadData.Accounting):[];let mu=[];if(((k=(O=(A=(L=a==null?void 0:a.payloadData)==null?void 0:L.TaxData)==null?void 0:A.TaxData)==null?void 0:O.TaxDataSet)==null?void 0:k.length)>0){const we={};(D=(N=(x=(U=a==null?void 0:a.payloadData)==null?void 0:U.TaxData)==null?void 0:x.TaxData)==null?void 0:N.TaxDataSet)==null||D.forEach(rt=>{var bt,js;const xt=rt.Country;we[xt]||(we[xt]={ControlId:rt.ControlId??null,Function:"INS",Material:((bt=a.headerData)==null?void 0:bt.materialNumber)||"",Depcountry:rt.Country});const ts=Object.keys(we[xt]).filter(Ee=>Ee.startsWith("TaxType")).length+1;ts<=9&&(we[xt][`TaxType${ts}`]=rt.TaxType,we[xt][`Taxclass${ts}`]=((js=rt.SelectedTaxClass)==null?void 0:js.TaxClass)||"")}),mu=Object.values(we)}const Cu=(I=a==null?void 0:a.payloadData)!=null&&I.Costing?Object.entries(a.payloadData.Costing):[],bg=((ge=a==null?void 0:a.headerData)==null?void 0:ge.orgData)||[],Au=new Set,Sg=bg.filter(we=>{var xt,ts;if(!((ts=(xt=we.plant)==null?void 0:xt.value)!=null&&ts.code))return!1;const rt=we.plant.value.code;return Au.has(rt)?!1:(Au.add(rt),!0)}).map(we=>{var bt,js,Ee,zs,Et;const rt=(js=(bt=we.plant)==null?void 0:bt.value)==null?void 0:js.code,xt=((Ee=Ag.find(([Qo])=>Qo===rt))==null?void 0:Ee[1])||{},ts=((zs=Cu.find(([Qo])=>Qo===rt))==null?void 0:zs[1])||{};return{...xt,AccountingId:xt.AccountingId||ts.AccountingId||null,Function:"INS",Material:((Et=a.headerData)==null?void 0:Et.materialNumber)||"",DelFlag:"",PriceCtrl:xt.PriceCtrl||"",MovingPr:xt.MovingPr||ts.MovingPr||"",StdPrice:xt.StdPrice||ts.StdPrice||"",PriceUnit:xt.PriceUnit||"",ValClass:xt.ValClass||"",OrigMat:ts.OrigMat===!0||ts.OrigMat==="X"||ts.OrigMat==="TRUE"?"X":"",ValArea:rt||""}}),Ng=(ee=a==null?void 0:a.payloadData)!=null&&ee.Warehouse?Object.entries(a.payloadData.Warehouse):[],wg=(Se=(Ue=a.headerData)==null?void 0:Ue.views)!=null&&Se.includes((M=P)==null?void 0:M.WAREHOUSE)?Ng.map(([we,rt],xt)=>{var ts;return{WarehouseId:rt.WarehouseId||"",Function:rt.Function||"",Material:((ts=a.headerData)==null?void 0:ts.materialNumber)||"",DelFlag:rt.DelFlag||!0,WhseNo:we||"",SpecMvmt:rt.SpecMvmt||"",LEquip1:rt.LEquip1||"",LeqUnit1:rt.LeqUnit1||"",Unittype1:rt.Unittype1||"",Placement:rt.Placement||""}}):[],Xi=($=a==null?void 0:a.payloadData)!=null&&$[(ce=P)==null?void 0:ce.STORAGE]?Object.values((F=a.payloadData)==null?void 0:F[(qe=P)==null?void 0:qe.STORAGE]):[],bu=new Set,Rg=ii.filter(we=>{var xt,ts,bt,js,Ee,zs,Et,Qo;if(!((ts=(xt=we.plant)==null?void 0:xt.value)!=null&&ts.code)||!((js=(bt=we.sloc)==null?void 0:bt.value)!=null&&js.code))return!1;const rt=`${(zs=(Ee=we.plant)==null?void 0:Ee.value)==null?void 0:zs.code}-${(Qo=(Et=we.sloc)==null?void 0:Et.value)==null?void 0:Qo.code}`;return bu.has(rt)?!1:(bu.add(rt),!0)}).map((we,rt)=>{var xt,ts,bt,js,Ee,zs,Et,Qo;return{StorageLocationId:((xt=Xi[rt])==null?void 0:xt.StorageLocationId)||"",Plant:((bt=(ts=we.plant)==null?void 0:ts.value)==null?void 0:bt.code)||"",StgeLoc:((Ee=(js=we.sloc)==null?void 0:js.value)==null?void 0:Ee.code)||"",Material:((zs=a.headerData)==null?void 0:zs.materialNumber)||"",PickgArea:((Et=Xi[rt])==null?void 0:Et.PickgArea)||"",StgeBin:((Qo=Xi[rt])==null?void 0:Qo.StgeBin)||""}}),Mn=(Us=(Ke=a.headerData)==null?void 0:Ke.views)!=null&&Us.includes(P.CLASSIFICATION)?(Wt=a==null?void 0:a.payloadData)==null?void 0:Wt[P.CLASSIFICATION]:{};let Su=[];Mn&&Mn.basic&&Mn.basic.Classtype&&Mn.basic.Classnum&&Array.isArray(Mn.classification)&&Su.push({ClassificationId:(($s=Mn==null?void 0:Mn.basic)==null?void 0:$s.ClassificationId)||"",Classnum:Mn.basic.Classnum,Classtype:Mn.basic.Classtype,Object:((Fs=a.headerData)==null?void 0:Fs.materialNumber)||"",Tochars:(ss=Mn.classification)==null?void 0:ss.map(we=>({CharacteristicsId:(we==null?void 0:we.CharacteristicsId)||"",Charact:we.characteristic,CharactDescr:we.description,Tocharvalues:[{ValueChar:we.value||""}]}))});const _g=(io=a==null?void 0:a.payloadData)!=null&&io[P.WORKSCHEDULING]?Object.entries((Ws=a.payloadData)==null?void 0:Ws[P.WORKSCHEDULING]):[],Ig=(ks=a==null?void 0:a.payloadData)!=null&&ks[P.BOM]?Object.entries((Qt=a.payloadData)==null?void 0:Qt[P.BOM]):[],vg=(fs=a==null?void 0:a.payloadData)!=null&&fs[P.SOURCE_LIST]?Object.entries((Ht=a.payloadData)==null?void 0:Ht[P.SOURCE_LIST]):[],Mg=(((ns=a==null?void 0:a.headerData)==null?void 0:ns.orgData)||[]).filter((we,rt,xt)=>rt===(xt==null?void 0:xt.findIndex(ts=>{var bt,js,Ee,zs;return((js=(bt=ts.plant)==null?void 0:bt.value)==null?void 0:js.code)===((zs=(Ee=we==null?void 0:we.plant)==null?void 0:Ee.value)==null?void 0:zs.code)}))).map((we,rt)=>{var Tl,El,ml,Cl,Al,bl,Sl,Nl,wl,Rl,_l,Il,vl,wu,Ru,_u,Iu,vu,Mu,Ou,xu,yu,Lu,Du,Pu,qu,Uu,ku,Hu,Bu,Gu;const xt=(El=(Tl=we.plant)==null?void 0:Tl.value)==null?void 0:El.code,ts=(ml=we.mrpProfile)==null?void 0:ml.code,bt=((Cl=Tg.find(([Zo])=>Zo===xt))==null?void 0:Cl[1])||{},js=((Al=Cu.find(([Zo])=>Zo===xt))==null?void 0:Al[1])||{},Ee=((bl=Eg.find(([Zo])=>Zo.startsWith(xt)))==null?void 0:bl[1])||{},zs=((Sl=mg.find(([Zo])=>Zo===xt))==null?void 0:Sl[1])||{},Et=((Nl=Cg.find(([Zo])=>Zo===xt))==null?void 0:Nl[1])||{},Qo=((wl=_g.find(([Zo])=>Zo===xt))==null?void 0:wl[1])||{},Cs=((Rl=Ig.find(([Zo])=>Zo===xt))==null?void 0:Rl[1])||{},Xt=((_l=vg.find(([Zo])=>Zo===xt))==null?void 0:_l[1])||{};return{PlantId:m===b.EXTEND&&e?null:((wu=(vl=(Il=a.payloadData)==null?void 0:Il.Purchasing)==null?void 0:vl[xt])==null?void 0:wu.PlantId)??null,Function:"INS",Material:((Ru=a.headerData)==null?void 0:Ru.materialNumber)||"",Plant:xt||"",DelFlag:!1,CritPart:!1,PurGroup:(bt==null?void 0:bt.PurGroup)||"",PurStatus:(bt==null?void 0:bt.PurStatus)||"",RoundProf:(Ee==null?void 0:Ee.RoundProf)||"",IssueUnit:"",IssueUnitIso:"",Mrpprofile:ts||"",MrpType:(Ee==null?void 0:Ee.MrpType)||"",MrpCtrler:(Ee==null?void 0:Ee.MrpCtrler)||"",PlndDelry:(Ee==null?void 0:Ee.PlndDelry)||"",GrPrTime:(Ee==null?void 0:Ee.GrPrTime)||"",PeriodInd:(Ee==null?void 0:Ee.PeriodInd)||"",Lotsizekey:(Ee==null?void 0:Ee.Lotsizekey)||"",ProcType:(Ee==null?void 0:Ee.ProcType)||"",Consummode:(Ee==null?void 0:Ee.Consummode)||"",FwdCons:(Ee==null?void 0:Ee.FwdCons)||"",ReorderPt:(Ee==null?void 0:Ee.ReorderPt)||"",MaxStock:(Ee==null?void 0:Ee.MaxStock)||"",SafetyStk:(Ee==null?void 0:Ee.SafetyStk)||"",Minlotsize:(Ee==null?void 0:Ee.Minlotsize)||"",PlanStrgp:(Ee==null?void 0:Ee.PlanStrgp)||"",BwdCons:(Ee==null?void 0:Ee.BwdCons)||"",Maxlotsize:(Ee==null?void 0:Ee.Maxlotsize)||"",FixedLot:(Ee==null?void 0:Ee.FixedLot)||"",RoundVal:(Ee==null?void 0:Ee.RoundVal)||"",GrpReqmts:(Ee==null?void 0:Ee.GrpReqmts)||"",MixedMrp:(Ee==null?void 0:Ee.MixedMrp)||"",SmKey:(Ee==null?void 0:Ee.SmKey)||"",Backflush:(Ee==null?void 0:Ee.Backflush)||"",AssyScarp:(Ee==null?void 0:Ee.AssyScarp)||"",Replentime:(Ee==null?void 0:Ee.Replentime)||"",PlTiFnce:(Ee==null?void 0:Ee.PlTiFnce)||"",ReplacePt:"",IndPostToInspStock:(bt==null?void 0:bt.IndPostToInspStock)===!0||(bt==null?void 0:bt.IndPostToInspStock)==="X"||(bt==null?void 0:bt.IndPostToInspStock)==="TRUE"?"X":"",HtsCode:(bt==null?void 0:bt.HtsCode)||"",CtrlKey:"",BatchMgmt:(Ee==null?void 0:Ee.BatchMgmt)||!1,DepReqId:(Ee==null?void 0:Ee.DepReqId)||"",SaftyTId:(Ee==null?void 0:Ee.SaftyTId)||"",Safetytime:(Ee==null?void 0:Ee.Safetytime)||"",Matfrgtgrp:(zs==null?void 0:zs.Matfrgtgrp)||"",Availcheck:(zs==null?void 0:zs.Availcheck)||"",ProfitCtr:(zs==null?void 0:zs.ProfitCtr)||"",Loadinggrp:(zs==null?void 0:zs.Loadinggrp)||"",MinLotSize:(Ee==null?void 0:Ee.MinLotSize)||"",MaxLotSize:(Ee==null?void 0:Ee.MaxLotSize)||"",FixLotSize:(Ee==null?void 0:Ee.FixLotSize)||"",AssyScrap:(Ee==null?void 0:Ee.AssyScrap)||"",IssStLoc:(Ee==null?void 0:Ee.IssStLoc)||"",SalesView:((Iu=a==null?void 0:a.headerData)==null?void 0:Iu.views.includes((_u=P)==null?void 0:_u.SALES))||!1,PurchView:((Mu=a==null?void 0:a.headerData)==null?void 0:Mu.views.includes((vu=P)==null?void 0:vu.PURCHASING))||!1,MrpView:((xu=a==null?void 0:a.headerData)==null?void 0:xu.views.includes((Ou=P)==null?void 0:Ou.MRP))||!1,WorkSchedView:((Lu=a==null?void 0:a.headerData)==null?void 0:Lu.views.includes((yu=P)==null?void 0:yu.WORK_SCHEDULING_2))||!1,WarehouseView:((Pu=a==null?void 0:a.headerData)==null?void 0:Pu.views.includes((Du=P)==null?void 0:Du.WAREHOUSE))||!1,AccountView:((Uu=a==null?void 0:a.headerData)==null?void 0:Uu.views.includes((qu=P)==null?void 0:qu.ACCOUNTING))||!1,CostView:((Hu=a==null?void 0:a.headerData)==null?void 0:Hu.views.includes((ku=P)==null?void 0:ku.COSTING))||!1,ForecastView:!1,PrtView:!1,StorageView:((Gu=a==null?void 0:a.headerData)==null?void 0:Gu.views.includes((Bu=P)==null?void 0:Bu.STORAGE))||!1,QualityView:!1,GrProcTime:"",GiProcTime:"",StorageCost:"",LotSizeUom:"",LotSizeUomIso:"",Unlimited:Qo.Unlimited||"",ProdProf:Qo.ProdProf||"",VarianceKey:js.VarianceKey||"",PoUnit:"",Spproctype:Ee.Spproctype||"",CommCode:(bt==null?void 0:bt.CommCode)||"",CommCoUn:(bt==null?void 0:bt.CommCoUn)||"",Countryori:bt==null?void 0:bt.Countryori,LotSize:js.LotSize||"",SlocExprc:Ee.SlocExprc||"",Inhseprodt:js.Inhseprodt||"",BomUsage:(Cs==null?void 0:Cs.BomUsage)||"",AltBom:(Cs==null?void 0:Cs.AltBom)||"",Category:(Cs==null?void 0:Cs.Category)||"",Component:(Cs==null?void 0:Cs.Component)||"",Quantity:(Cs==null?void 0:Cs.Quantity)||"",CompUom:(Cs==null?void 0:Cs.CompUom)||"",Bvalidfrom:Cs!=null&&Cs.Bvalidfrom?Is(Cs==null?void 0:Cs.Bvalidfrom):Is(new Date().toISOString()),Bvalidto:Cs!=null&&Cs.Bvalidto?Is(Cs==null?void 0:Cs.Bvalidto):Is(new Date().toISOString()),Supplier:(Xt==null?void 0:Xt.Supplier)||"",PurchaseOrg:(Xt==null?void 0:Xt.PurchaseOrg)||"",ProcurementPlant:(Xt==null?void 0:Xt.ProcurementPlant)||"",SOrderUnit:(Xt==null?void 0:Xt.SOrderUnit)||"",Agreement:(Xt==null?void 0:Xt.Agreement)||"",AgreementItem:(Xt==null?void 0:Xt.AgreementItem)||"",FixedSupplySource:(Xt==null?void 0:Xt.FixedSupplySource)||"",Blocked:(Xt==null?void 0:Xt.Blocked)||"",SMrp:(Xt==null?void 0:Xt.SMrp)||"",Slvalidfrom:Xt!=null&&Xt.Slvalidfrom?Is(Xt==null?void 0:Xt.Slvalidfrom):Is(new Date().toISOString()),Slvalidto:Xt!=null&&Xt.Slvalidto?Is(Xt==null?void 0:Xt.Slvalidto):Is(new Date().toISOString()),CcPhInv:(Et==null?void 0:Et.CcPhInv)||"",CcFixed:(Et==null?void 0:Et.CcFixed)||"",StgePdUn:(Et==null?void 0:Et.StgePdUn)||"",DefaultStockSegment:(Et==null?void 0:Et.DefaultStockSegment)||"",NegStocks:(Et==null?void 0:Et.NegStocks)||"",SernoProf:(Et==null?void 0:Et.SernoProf)||"",DistrProf:(Et==null?void 0:Et.DistrProf)||"",DetermGrp:(Et==null?void 0:Et.DetermGrp)||"",IuidRelevant:(Et==null?void 0:Et.IuidRelevant)||"",UidIea:(Et==null?void 0:Et.UidIea)||"",IuidType:(Et==null?void 0:Et.IuidType)||"",IuidRelevant:(Et==null?void 0:Et.IuidRelevant)||"",SortStockBasedOnSegment:(Et==null?void 0:Et.SortStockBasedOnSegment)||"",SegmentationStrategy:(Et==null?void 0:Et.SegmentationStrategy)||"",IssueUnit:(Et==null?void 0:Et.IssueUnit)||""}}),vr=(a==null?void 0:a.additionalData)||[],Mr=(a==null?void 0:a.unitsOfMeasureData)||[],Or=(a==null?void 0:a.eanData)||[],Og=vr!=null&&vr.length?vr==null?void 0:vr.map(we=>{var rt;return{MaterialDescriptionId:m===b.EXTEND&&e?null:we.MaterialDescriptionId||null,Function:"INS",Material:((rt=a.headerData)==null?void 0:rt.materialNumber)||"",Langu:we.language||"EN",LanguIso:"",MatlDesc:(we==null?void 0:we.materialDescription)||"",DelFlag:!1}}):[{MaterialDescriptionId:null,Function:"INS",Material:((Zt=a.headerData)==null?void 0:Zt.materialNumber)||"",Langu:"EN",LanguIso:"",MatlDesc:((xs=a.headerData)==null?void 0:xs.globalMaterialDescription)||"",DelFlag:!1}],xg=Mr!=null&&Mr.length?Mr==null?void 0:Mr.map(we=>{var rt;return{UomId:m===b.EXTEND&&e?null:(we==null?void 0:we.UomId)||null,Function:"INS",Material:((rt=a.headerData)==null?void 0:rt.materialNumber)||"",AltUnit:(we==null?void 0:we.aUnit)||"",AltUnitIso:"",Numerator:(we==null?void 0:we.yValue)||"1",Denominatr:(we==null?void 0:we.xValue)||"1",EanUpc:(we==null?void 0:we.eanUpc)||"",EanCat:we.eanCategory||"",Length:we.length,NetWeight:we.netWeight||"",Width:we.width,Height:we.height,UnitDim:we.unitsOfDimension||"",UnitDimIso:"",Volume:we.volume||"",Volumeunit:we.volumeUnit||"",VolumeunitIso:"",GrossWt:we.grossWeight||"",UnitOfWt:we.weightUnit||"",UnitOfWtIso:"",DelFlag:!1,SubUom:"",SubUomIso:"",GtinVariant:"",MaterialExternal:null,MaterialGuid:null,MaterialVersion:null,NestingFactor:"",MaximumStacking:null,CapacityUsage:we.capacityUsage,EwmCwUomType:"",MaterialLong:null}}):[{UomId:null,Function:"INS",Material:((lt=a.headerData)==null?void 0:lt.materialNumber)||"",AltUnit:((Ys=(Rt=(He=a==null?void 0:a.payloadData)==null?void 0:He["Basic Data"])==null?void 0:Rt.basic)==null?void 0:Ys.BaseUom)||"",AltUnitIso:"",Numerator:"1",Denominatr:"1",EanUpc:"",EanCat:"",Length:"",Width:"",Height:"",UnitDim:"",UnitDimIso:"",Volume:((it=(Me=(Bt=a==null?void 0:a.payloadData)==null?void 0:Bt["Basic Data"])==null?void 0:Me.basic)==null?void 0:it.Volume)||"",Volumeunit:((ds=(Ns=(Ut=a==null?void 0:a.payloadData)==null?void 0:Ut["Basic Data"])==null?void 0:Ns.basic)==null?void 0:ds.VolumeUnit)||"",VolumeunitIso:"",GrossWt:"",UnitOfWt:((yt=(at=(rs=a==null?void 0:a.payloadData)==null?void 0:rs["Basic Data"])==null?void 0:at.basic)==null?void 0:yt.UnitOfWt)||"",UnitOfWtIso:"",DelFlag:!1,SubUom:"",SubUomIso:"",GtinVariant:"",MaterialExternal:null,MaterialGuid:null,MaterialVersion:null,NestingFactor:"",MaximumStacking:null,CapacityUsage:"",EwmCwUomType:"",MaterialLong:null}],yg=Or!=null&&Or.length?Or==null?void 0:Or.map(we=>{var rt;return{EanId:m===b.EXTEND&&e?null:(we==null?void 0:we.EanId)||null,Function:"INS",Material:((rt=a.headerData)==null?void 0:rt.materialNumber)||"",Unit:(we==null?void 0:we.altunit)||"",EanUpc:(we==null?void 0:we.eanUpc)||"",EanCat:(we==null?void 0:we.eanCategory)||"",MainEan:we.MainEan||!1,Au:we.au||!1}}):null,Nu=new Set;(ws=a==null?void 0:a.payloadData)!=null&&ws.Tostroragelocationdata?(Hs=a==null?void 0:a.payloadData)==null||Hs.Tostroragelocationdata:(ao=(os=a.headerData)==null?void 0:os.views)!=null&&ao.includes((ls=P)==null?void 0:ls.STORAGE)&&ii.filter(we=>{var xt,ts,bt,js;if(!((ts=(xt=we==null?void 0:we.plant)==null?void 0:xt.value)!=null&&ts.code)||!((js=(bt=we==null?void 0:we.sloc)==null?void 0:bt.value)!=null&&js.code))return!1;const rt=`${we.plant.value.code}-${we.sloc.value.code}`;return Nu.has(rt)?!1:(Nu.add(rt),!0)}).map(we=>{var rt,xt,ts,bt,js;return{Function:"INS",Material:((rt=a==null?void 0:a.headerData)==null?void 0:rt.materialNumber)||"",Plant:((ts=(xt=we==null?void 0:we.plant)==null?void 0:xt.value)==null?void 0:ts.code)||"",StgeLoc:((js=(bt=we==null?void 0:we.sloc)==null?void 0:bt.value)==null?void 0:js.code)||""}});const Lg={ChildRequestId:((Ye=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:Ye.ChildRequestId)||null,MaterialGroupType:((Mt=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:Mt.MaterialGroupType)||null,TaskId:(l==null?void 0:l.taskId)||null,Comments:r||o,TotalIntermediateTasks:((gs=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:gs.TotalIntermediateTasks)||null,IntermediateTaskCount:((Lt=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:Lt.IntermediateTaskCount)||null,ReqCreatedBy:((ps=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:ps.ReqCreatedBy)||null,ReqCreatedOn:((Js=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:Js.ReqCreatedOn)||null,ReqUpdatedOn:((Bs=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:Bs.ReqUpdatedOn)||null,RequestType:((st=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:st.RequestType)||null,RequestPrefix:((Ts=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:Ts.RequestPrefix)||null,RequestDesc:((to=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:to.RequestDesc)||null,RequestPriority:((St=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:St.RequestPriority)||null,RequestStatus:((es=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:es.RequestStatus)||null,CurrentLevel:(l==null?void 0:l.ATTRIBUTE_3)||"",CurrentLevelName:(l==null?void 0:l.ATTRIBUTE_4)||"",ParticularLevel:c,TaskName:(l==null?void 0:l.taskDesc)||"",ApproverGroup:(l==null?void 0:l.ATTRIBUTE_5)||""},Dg={MaterialId:m===b.EXTEND&&e||m===b.CREATE&&e?null:(l!=null&&l.taskId||t)&&!Q.includes("-")?Number(Q):null,Flag:"",Function:"INS",Material:((ot=a==null?void 0:a.headerData)==null?void 0:ot.materialNumber)||"",MatlType:((Rs=(Dt=a==null?void 0:a.headerData)==null?void 0:Dt.materialType)==null?void 0:Rs.code)||((ae=a==null?void 0:a.headerData)==null?void 0:ae.materialType)||"",IndSector:((me=($e=a==null?void 0:a.headerData)==null?void 0:$e.industrySector)==null?void 0:me.code)||((Ne=a==null?void 0:a.headerData)==null?void 0:Ne.industrySector)||"",Comments:r||o,ViewNames:gg,Description:((Ve=a==null?void 0:a.headerData)==null?void 0:Ve.globalMaterialDescription)||"",Bom:((Oe=a==null?void 0:a.headerData)==null?void 0:Oe.Bom)||"",SourceList:((ct=a==null?void 0:a.headerData)==null?void 0:ct.sourceList)||"",Pir:((ke=a==null?void 0:a.headerData)==null?void 0:ke.PIR)||"",Uom:(Es=(Vt=a==null?void 0:a.headerData)==null?void 0:Vt.Uom)!=null&&Es.code?a.headerData.Uom.code:((Pt=a==null?void 0:a.headerData)==null?void 0:Pt.Uom)||"",Category:(Pe=(je=a==null?void 0:a.headerData)==null?void 0:je.Category)!=null&&Pe.code?a.headerData.Category.code:((_t=a==null?void 0:a.headerData)==null?void 0:_t.Category)||"",Relation:(_s=(jt=a==null?void 0:a.headerData)==null?void 0:jt.Relation)!=null&&_s.code?a.headerData.Relation.code:((Ft=a==null?void 0:a.headerData)==null?void 0:Ft.Relation)||"",Usage:((te=a==null?void 0:a.headerData)==null?void 0:te.Usage)||"",CreationDate:l!=null&&l.requestId||t||(xe=g==null?void 0:g.payloadData)!=null&&xe.RequestId?Is((B=g==null?void 0:g.payloadData)==null?void 0:B.ReqCreatedOn):`/Date(${Date.now()}+0000)/`,EditId:null,ExtendId:null,MassCreationId:m===b.CREATE||m===b.CREATE_WITH_UPLOAD?l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(de=g==null?void 0:g.payloadData)!=null&&de.RequestId?(w=g==null?void 0:g.payloadData)==null?void 0:w.RequestId:"":null,MassEditId:((Te=a==null?void 0:a.payloadData)==null?void 0:Te.MassEditId)||"",MassExtendId:m===b.EXTEND||m===b.EXTEND_WITH_UPLOAD?l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(We=g==null?void 0:g.payloadData)!=null&&We.RequestId?(le=g==null?void 0:g.payloadData)==null?void 0:le.RequestId:(dt=u==null?void 0:u.requestHeader)!=null&&dt.requestId?(ze=u==null?void 0:u.requestHeader)==null?void 0:ze.requestId:requestId.slice(3):null,TaskId:(l==null?void 0:l.taskId)||null,TaskName:(l==null?void 0:l.taskDesc)||null,TotalIntermediateTasks:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(Qe=g==null?void 0:g.payloadData)!=null&&Qe.RequestId?(Ct=g==null?void 0:g.payloadData)==null?void 0:Ct.TotalIntermediateTasks:"",IntermediateTaskCount:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(_e=g==null?void 0:g.payloadData)!=null&&_e.RequestId?(ut=g==null?void 0:g.payloadData)==null?void 0:ut.IntermediateTaskCount:"",BasicView:((Xs=a==null?void 0:a.headerData)==null?void 0:Xs.views.includes((ys=P)==null?void 0:ys.BASIC_DATA))||!1,SalesView:((us=a==null?void 0:a.headerData)==null?void 0:us.views.includes((uo=P)==null?void 0:uo.SALES))||!1,MrpView:((Po=a==null?void 0:a.headerData)==null?void 0:Po.views.includes((co=P)==null?void 0:co.MRP))||!1,PurchaseView:((Ot=a==null?void 0:a.headerData)==null?void 0:Ot.views.includes((Qs=P)==null?void 0:Qs.PURCHASING))||!1,AccountView:((Zs=a==null?void 0:a.headerData)==null?void 0:Zs.views.includes((Gs=P)==null?void 0:Gs.ACCOUNTING))||!1,CostView:((Fo=a==null?void 0:a.headerData)==null?void 0:Fo.views.includes((_o=P)==null?void 0:_o.COSTING))||!1,WorkSchedView:((ho=a==null?void 0:a.headerData)==null?void 0:ho.views.includes((Io=P)==null?void 0:Io.WORK_SCHEDULING_2))||!1,WarehouseView:((Wo=a==null?void 0:a.headerData)==null?void 0:Wo.views.includes((is=P)==null?void 0:is.WAREHOUSE))||!1,ForecastView:!1,PrtView:!1,StorageView:((qo=a==null?void 0:a.headerData)==null?void 0:qo.views.includes((so=P)==null?void 0:so.STORAGE))||!1,QualityView:!1,IsFirstChangeLogCommit:!1,IsMarkedForDeletion:!1,IsFirstCreate:!(l!=null&&l.taskId),creationTime:l!=null&&l.createdOn?Is(l==null?void 0:l.createdOn):null,dueDate:l!=null&&l.criticalDeadline?Is(l==null?void 0:l.criticalDeadline):null,ManufacturerId:(a==null?void 0:a.ManufacturerID)||"",OrgData:ii||[],Toclientdata:{ClientId:((jo=a==null?void 0:a.headerData)==null?void 0:jo.clientId)||null,Function:"INS",Material:((zo=a==null?void 0:a.headerData)==null?void 0:zo.materialNumber)||"",DelFlag:!0,MatlGroup:((pe=(Xo=(Yo=a==null?void 0:a.payloadData)==null?void 0:Yo["Basic Data"])==null?void 0:Xo.basic)==null?void 0:pe.MatlGroup)||"",Extmatlgrp:((At=(ft=(ht=a==null?void 0:a.payloadData)==null?void 0:ht["Basic Data"])==null?void 0:ft.basic)==null?void 0:At.Extmatlgrp)||"",OldMatNo:(($t=(Nt=(ve=a==null?void 0:a.payloadData)==null?void 0:ve["Basic Data"])==null?void 0:Nt.basic)==null?void 0:$t.OldMatNo)||"",BaseUom:((qs=(as=(kt=a==null?void 0:a.payloadData)==null?void 0:kt["Basic Data"])==null?void 0:as.basic)==null?void 0:qs.BaseUom)||"",Document:(De=(Gt=(gt=a==null?void 0:a.payloadData)==null?void 0:gt["Basic Data"])==null?void 0:Gt.basic)==null?void 0:De.Document,DocType:(gn=(ms=(pt=a==null?void 0:a.payloadData)==null?void 0:pt["Basic Data"])==null?void 0:ms.basic)==null?void 0:gn.DocType,DocVers:(Nn=(Sn=(yn=a==null?void 0:a.payloadData)==null?void 0:yn["Basic Data"])==null?void 0:Sn.basic)==null?void 0:Nn.DocVers,DocFormat:(lr=(rr=(Yn=a==null?void 0:a.payloadData)==null?void 0:Yn["Basic Data"])==null?void 0:rr.basic)==null?void 0:lr.DocFormat,DocChgNo:(_=(E=(ir=a==null?void 0:a.payloadData)==null?void 0:ir["Basic Data"])==null?void 0:E.basic)==null?void 0:_.DocChgNo,PageNo:(Ae=(re=(W=a==null?void 0:a.payloadData)==null?void 0:W["Basic Data"])==null?void 0:re.basic)==null?void 0:Ae.PageNo,NoSheets:(Ge=a==null?void 0:a.payloadData)==null?void 0:Ge.NoSheets,ProdMemo:(Ze=a==null?void 0:a.payloadData)==null?void 0:Ze.ProdMemo,Pageformat:(et=a==null?void 0:a.payloadData)==null?void 0:et.DocFormat,SizeDim:(wt=a==null?void 0:a.payloadData)==null?void 0:wt.SizeDim,BaseUomIso:"",BasicMatl:((tt=(Kt=(Tt=a==null?void 0:a.payloadData)==null?void 0:Tt["Basic Data"])==null?void 0:Kt.basic)==null?void 0:tt.BasicMatl)||"",StdDescr:(hs=a==null?void 0:a.payloadData)==null?void 0:hs.StdDescr,DsnOffice:((oo=(zt=(eo=a==null?void 0:a.payloadData)==null?void 0:eo["Basic Data"])==null?void 0:zt.basic)==null?void 0:oo.DsnOffice)||"",PurValkey:((pn=(Vo=(no=a==null?void 0:a.payloadData)==null?void 0:no["Purchasing-General"])==null?void 0:Vo["Purchasing-General"])==null?void 0:pn.PurValkey)||"",NetWeight:(Xl=(Ir=(ar=a==null?void 0:a.payloadData)==null?void 0:ar["Basic Data"])==null?void 0:Ir.basic)==null?void 0:Xl.NetWeight,UnitOfWt:((Kl=(Vl=(Yr=a==null?void 0:a.payloadData)==null?void 0:Yr["Basic Data"])==null?void 0:Vl.basic)==null?void 0:Kl.UnitOfWt)||"",TransGrp:(Ql=(Jl=(Xr=a==null?void 0:a.payloadData)==null?void 0:Xr["Sales-General"])==null?void 0:Jl["Sales-General"])==null?void 0:Ql.TransGrp,XSalStatus:(ti=(ei=(Zl=a==null?void 0:a.payloadData)==null?void 0:Zl["Sales-General"])==null?void 0:ei["Sales-General"])==null?void 0:ti.XSalStatus,Svalidfrom:(Vr=(oi=(si=a==null?void 0:a.payloadData)==null?void 0:si["Sales-General"])==null?void 0:oi["Sales-General"])!=null&&Vr.Svalidfrom?Is((Jr=(Kr=(ni=a==null?void 0:a.payloadData)==null?void 0:ni["Sales-General"])==null?void 0:Kr["Sales-General"])==null?void 0:Jr.Svalidfrom):null,Division:(ri=g==null?void 0:g.payloadData)!=null&&ri.Division?(li=g==null?void 0:g.payloadData)==null?void 0:li.Division:((el=(Zr=(Qr=a==null?void 0:a.payloadData)==null?void 0:Qr["Basic Data"])==null?void 0:Zr.basic)==null?void 0:el.Division)||"",ProdHier:((ol=(sl=(tl=a==null?void 0:a.payloadData)==null?void 0:tl["Basic Data"])==null?void 0:sl.basic)==null?void 0:ol.ProdHier)||"",CadId:(ll=(rl=(nl=a==null?void 0:a.payloadData)==null?void 0:nl["Basic Data"])==null?void 0:rl.basic)==null?void 0:ll.CadId,VarOrdUn:(cl=(al=(il=a==null?void 0:a.payloadData)==null?void 0:il["Purchasing-General"])==null?void 0:al["Purchasing-General"])==null?void 0:cl.VarOrdUn,UnitOfWtIso:"",MatGrpSm:"",Authoritygroup:"",QmProcmnt:"",BatchMgmt:(hl=(ul=(dl=a==null?void 0:a.payloadData)==null?void 0:dl["Sales-General"])==null?void 0:ul["Sales-General"])==null?void 0:hl.BatchMgmt,SalStatus:"",Catprofile:"",ShelfLife:"",StorPct:"",Hazmatprof:((pl=(gl=(fl=a==null?void 0:a.payloadData)==null?void 0:fl["Basic Data"])==null?void 0:gl.basic)==null?void 0:pl.Hazmatprof)||"",HighVisc:(y=(h=(i=a==null?void 0:a.payloadData)==null?void 0:i["Basic Data"])==null?void 0:h.basic)==null?void 0:y.HighVisc,AppdBRec:"",Pvalidfrom:(Z=(Y=(G=a==null?void 0:a.payloadData)==null?void 0:G["Basic Data"])==null?void 0:Y.basic)!=null&&Z.Pvalidfrom?Is((Le=(ue=(K=a==null?void 0:a.payloadData)==null?void 0:K["Basic Data"])==null?void 0:ue.basic)==null?void 0:Le.Pvalidfrom):null,EnvtRlvt:"",ProdAlloc:(Ie=(be=(Ce=a==null?void 0:a.payloadData)==null?void 0:Ce["Basic Data"])==null?void 0:be.basic)==null?void 0:Ie.ProdAlloc,PeriodIndExpirationDate:"",ParEff:!0,Matcmpllvl:"",GItemCat:((Yt=(qt=(nt=a==null?void 0:a.payloadData)==null?void 0:nt["Basic Data"])==null?void 0:qt.basic)==null?void 0:Yt.GItemCat)||"",CSalStatus:((cs=(ro=(Vs=a==null?void 0:a.payloadData)==null?void 0:Vs["Basic Data"])==null?void 0:ro.basic)==null?void 0:cs.CSalStatus)||"",IntlPoPrice:((an=(ln=(vo=a==null?void 0:a.payloadData)==null?void 0:vo["Basic Data"])==null?void 0:ln.basic)==null?void 0:an.IntlPoPrice)||"",PryVendor:((Ko=(Rn=(wn=a==null?void 0:a.payloadData)==null?void 0:wn["Basic Data"])==null?void 0:Rn.basic)==null?void 0:Ko.PryVendor)||"",PlanningArea:((vn=(In=(_n=a==null?void 0:a.payloadData)==null?void 0:_n["Basic Data"])==null?void 0:In.basic)==null?void 0:vn.PlanningArea)||"",PlanningFactor:((Kn=(Vn=(Xn=a==null?void 0:a.payloadData)==null?void 0:Xn["Basic Data"])==null?void 0:Vn.basic)==null?void 0:Kn.PlanningFactor)||"",ReturnMatNumber:((Tn=(fo=(Jn=a==null?void 0:a.payloadData)==null?void 0:Jn["Basic Data"])==null?void 0:fo.basic)==null?void 0:Tn.ReturnMatNumber)||"",ParentMatNumber:((Qn=(nn=(Jo=a==null?void 0:a.payloadData)==null?void 0:Jo["Basic Data"])==null?void 0:nn.basic)==null?void 0:Qn.ParentMatNumber)||"",DiversionControlFlag:((Ls=(kn=(Zn=a==null?void 0:a.payloadData)==null?void 0:Zn["Basic Data"])==null?void 0:kn.basic)==null?void 0:Ls.DiversionControlFlag)||"",MatGroupPackagingMat:((pc=(Mo=(So=a==null?void 0:a.payloadData)==null?void 0:So["Basic Data"])==null?void 0:Mo.basic)==null?void 0:pc.MatGroupPackagingMat)||"",HazMatNo:((mc=(Ec=(Tc=a==null?void 0:a.payloadData)==null?void 0:Tc[P.STORAGE_GENERAL])==null?void 0:Ec[P.STORAGE_GENERAL])==null?void 0:mc.HazMatNo)||"",QtyGrGi:((bc=(Ac=(Cc=a==null?void 0:a.payloadData)==null?void 0:Cc[P.STORAGE_GENERAL])==null?void 0:Ac[P.STORAGE_GENERAL])==null?void 0:bc.QtyGrGi)||"",TempConds:((wc=(Nc=(Sc=a==null?void 0:a.payloadData)==null?void 0:Sc[P.STORAGE_GENERAL])==null?void 0:Nc[P.STORAGE_GENERAL])==null?void 0:wc.TempConds)||"",Container:((Ic=(_c=(Rc=a==null?void 0:a.payloadData)==null?void 0:Rc[P.STORAGE_GENERAL])==null?void 0:_c[P.STORAGE_GENERAL])==null?void 0:Ic.Container)||"",LabelType:((Oc=(Mc=(vc=a==null?void 0:a.payloadData)==null?void 0:vc[P.STORAGE_GENERAL])==null?void 0:Mc[P.STORAGE_GENERAL])==null?void 0:Oc.LabelType)||"",LabelForm:((Lc=(yc=(xc=a==null?void 0:a.payloadData)==null?void 0:xc[P.STORAGE_GENERAL])==null?void 0:yc[P.STORAGE_GENERAL])==null?void 0:Lc.LabelForm)||"",AppdBRec:((qc=(Pc=(Dc=a==null?void 0:a.payloadData)==null?void 0:Dc[P.STORAGE_GENERAL])==null?void 0:Pc[P.STORAGE_GENERAL])==null?void 0:qc.AppdBRec)||"",Minremlife:((Hc=(kc=(Uc=a==null?void 0:a.payloadData)==null?void 0:Uc[P.STORAGE_GENERAL])==null?void 0:kc[P.STORAGE_GENERAL])==null?void 0:Hc.Minremlife)||"",ShelfLife:(($c=(Gc=(Bc=a==null?void 0:a.payloadData)==null?void 0:Bc[P.STORAGE_GENERAL])==null?void 0:Gc[P.STORAGE_GENERAL])==null?void 0:$c.ShelfLife)||"",PeriodIndExpirationDate:((jc=(Wc=(Fc=a==null?void 0:a.payloadData)==null?void 0:Fc[P.STORAGE_GENERAL])==null?void 0:Wc[P.STORAGE_GENERAL])==null?void 0:jc.PeriodIndExpirationDate)||"",RoundUpRuleExpirationDate:((Xc=(Yc=(zc=a==null?void 0:a.payloadData)==null?void 0:zc[P.STORAGE_GENERAL])==null?void 0:Yc[P.STORAGE_GENERAL])==null?void 0:Xc.RoundUpRuleExpirationDate)||"",StorPct:((Jc=(Kc=(Vc=a==null?void 0:a.payloadData)==null?void 0:Vc[P.STORAGE_GENERAL])==null?void 0:Kc[P.STORAGE_GENERAL])==null?void 0:Jc.StorPct)||"",SledBbd:((ed=(Zc=(Qc=a==null?void 0:a.payloadData)==null?void 0:Qc[P.STORAGE_GENERAL])==null?void 0:Zc[P.STORAGE_GENERAL])==null?void 0:ed.SledBbd)||"",SerializationLevel:((od=(sd=(td=a==null?void 0:a.payloadData)==null?void 0:td[P.STORAGE_GENERAL])==null?void 0:sd[P.STORAGE_GENERAL])==null?void 0:od.SerializationLevel)||"",ReqMaxShLife:((ld=(rd=(nd=a==null?void 0:a.payloadData)==null?void 0:nd[P.STORAGE_GENERAL])==null?void 0:rd[P.STORAGE_GENERAL])==null?void 0:ld.ReqMaxShLife)||"",MaturationTime:((cd=(ad=(id=a==null?void 0:a.payloadData)==null?void 0:id[P.STORAGE_GENERAL])==null?void 0:ad[P.STORAGE_GENERAL])==null?void 0:cd.MaturationTime)||"",StorPct:((hd=(ud=(dd=a==null?void 0:a.payloadData)==null?void 0:dd[P.STORAGE_GENERAL])==null?void 0:ud[P.STORAGE_GENERAL])==null?void 0:hd.StorPct)||"",StorConds:((pd=(gd=(fd=a==null?void 0:a.payloadData)==null?void 0:fd[P.STORAGE_GENERAL])==null?void 0:gd[P.STORAGE_GENERAL])==null?void 0:pd.StorConds)||""},Toplantdata:Mg,Tosalesdata:(Td=a==null?void 0:a.headerData)!=null&&Td.views.includes("Sales")?pg:[],Tomaterialdescription:Og,Touomdata:xg,Toeandata:yg,Tostroragelocationdata:Rg,ToClassification:Su,Tomaterialerrordata:(a==null?void 0:a.Tomaterialerrordata)||{},Toaccountingdata:(md=a==null?void 0:a.headerData)!=null&&md.views.includes((Ed=P)==null?void 0:Ed.ACCOUNTING)?Sg:[],Tocontroldata:(Cd=a==null?void 0:a.headerData)!=null&&Cd.views.includes("Sales")?mu:[],Torequestheaderdata:{RequestId:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(Ad=g==null?void 0:g.payloadData)!=null&&Ad.RequestId?(bd=g==null?void 0:g.payloadData)==null?void 0:bd.RequestId:(Sd=u==null?void 0:u.requestHeader)!=null&&Sd.requestId?(Nd=u==null?void 0:u.requestHeader)==null?void 0:Nd.requestId:requestId.slice(3),ReqCreatedBy:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(wd=g==null?void 0:g.payloadData)!=null&&wd.RequestId?(Rd=g==null?void 0:g.payloadData)==null?void 0:Rd.ReqCreatedBy:(_d=u==null?void 0:u.requestHeader)==null?void 0:_d.reqCreatedBy,ReqCreatedOn:Is(l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(Id=g==null?void 0:g.payloadData)!=null&&Id.RequestId?(vd=g==null?void 0:g.payloadData)==null?void 0:vd.ReqCreatedOn:(Md=u==null?void 0:u.requestHeader)==null?void 0:Md.reqCreatedOn),ReqUpdatedOn:Is(l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(Od=g==null?void 0:g.payloadData)!=null&&Od.RequestId?(xd=g==null?void 0:g.payloadData)==null?void 0:xd.ReqUpdatedOn:(yd=u==null?void 0:u.requestHeader)==null?void 0:yd.reqCreatedOn),RequestType:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(Ld=g==null?void 0:g.payloadData)!=null&&Ld.RequestId?(Dd=g==null?void 0:g.payloadData)==null?void 0:Dd.RequestType:(Pd=u==null?void 0:u.requestHeader)==null?void 0:Pd.requestType,Division:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(qd=g==null?void 0:g.payloadData)!=null&&qd.RequestId?(Ud=g==null?void 0:g.payloadData)==null?void 0:Ud.Division:(kd=u==null?void 0:u.requestHeader)==null?void 0:kd.Division,RequestPriority:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(Hd=g==null?void 0:g.payloadData)!=null&&Hd.RequestId?(Bd=g==null?void 0:g.payloadData)==null?void 0:Bd.RequestPriority:(Gd=u==null?void 0:u.requestHeader)==null?void 0:Gd.requestPriority,RequestDesc:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||($d=g==null?void 0:g.payloadData)!=null&&$d.RequestId?(Fd=g==null?void 0:g.payloadData)==null?void 0:Fd.RequestDesc:(Wd=u==null?void 0:u.requestHeader)==null?void 0:Wd.requestDesc,RequestStatus:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(jd=g==null?void 0:g.payloadData)!=null&&jd.RequestId?(zd=g==null?void 0:g.payloadData)==null?void 0:zd.RequestStatus:(Yd=u==null?void 0:u.requestHeader)==null?void 0:Yd.requestStatus,FirstProd:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(Xd=g==null?void 0:g.payloadData)!=null&&Xd.RequestId?(Vd=g==null?void 0:g.payloadData)==null?void 0:Vd.FirstProd:((Kd=s==null?void 0:s.payloadData)==null?void 0:Kd.FirstProductionDate)||null,LaunchDate:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(Jd=g==null?void 0:g.payloadData)!=null&&Jd.RequestId?(Qd=g==null?void 0:g.payloadData)==null?void 0:Qd.LaunchDate:((Zd=s==null?void 0:s.payloadData)==null?void 0:Zd.LaunchDate)||null,LeadingCat:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(eu=g==null?void 0:g.payloadData)!=null&&eu.RequestId?(tu=g==null?void 0:g.payloadData)==null?void 0:tu.LeadingCat:(su=u==null?void 0:u.requestHeader)==null?void 0:su.leadingCat,Region:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(ou=g==null?void 0:g.payloadData)!=null&&ou.RequestId?(nu=g==null?void 0:g.payloadData)==null?void 0:nu.Region:(ru=u==null?void 0:u.requestHeader)==null?void 0:ru.region,IsBifurcated:!0},Tochildrequestheaderdata:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||t||(lu=g==null?void 0:g.payloadData)!=null&&lu.RequestId?Lg:{},Towarehousedata:wg,changeLogData:se&&Object.keys(se).length>0&&(se[(iu=a.headerData)==null?void 0:iu.materialNumber]||se[(au=a.headerData)==null?void 0:au.id])?{RequestId:(se==null?void 0:se.RequestId)||((cu=a==null?void 0:a.changeLogData)==null?void 0:cu.RequestId),ChangeLogId:((du=a==null?void 0:a.changeLogData)==null?void 0:du.ChangeLogId)??null,ChildRequestId:((se==null?void 0:se.ChildRequestId)||((uu=a==null?void 0:a.changeLogData)==null?void 0:uu.ChildRequestId))??null,...se[(hu=a.headerData)==null?void 0:hu.materialNumber],...se[(fu=a.headerData)==null?void 0:fu.id]}:{RequestId:(gu=a==null?void 0:a.changeLogData)==null?void 0:gu.RequestId,ChangeLogId:((pu=a==null?void 0:a.changeLogData)==null?void 0:pu.ChangeLogId)??null,ChildRequestId:((Tu=a==null?void 0:a.changeLogData)==null?void 0:Tu.ChildRequestId)??null}};V.push(Dg)}}}),V}return{createPayloadFromReduxState:ie}},ch={handleSubmitForApproval:6,handleSendBack:1,handleReject:3,handleValidate:5,handleSAPSyndication:8,handleValidate1:4,handleSubmitForReview:7,handleCorrection:2},dh={Approve:6,"Send Back":3,"Save As Draft":1,Reject:3,Validate:4,Forward:6,"SAP Syndication":8,Submit:7,Correction:2},Dl={REQUEST_HEADER:0,REQUEST_DETAILS:1,ATTACHMENT_AND_COMMENTS:2,PREVIEW:3},To={HANDLE_SEND_BACK:"handleSendBack",HANDLE_VALIDATE1:"handleValidate1",HANDLE_SUBMIT_FOR_APPROVAL:"handleSubmitForApproval",HANDLE_VALIDATE:"handleValidate",HANDLE_SAP_SYNDICATION:"handleSAPSyndication",HANDLE_SUBMIT_FOR_REVIEW:"handleSubmitForReview",HANDLE_CORRECTION:"handleCorrection",HANDLE_SUBMIT:"handleSubmit",HANDLE_DRAFT:"handleDraft"},aE=d.forwardRef((e,t)=>{var v;const[r,o]=d.useState([]),c=oe(C=>C.payload),s=((v=c==null?void 0:c.payloadData)==null?void 0:v.TemplateName)||"",[m,l]=d.useState(!1),[u,se]=d.useState(!1),{changePayloadForTemplate:ie}=$i(s),q=oe(C=>C.commonFilter.RequestBench),{customError:g}=sn(),V=wr();d.useImperativeHandle(t,()=>({handlePriorityDialogClickOpen:he}));const Q=oe(C=>C.paginationData),H=[{field:"id",headerName:"",editable:!1,flex:1,hide:!0},{field:"reqId",headerName:"Request ID",editable:!1,flex:2.5},{field:"module",headerName:"Module",editable:!1,flex:2},{field:"reqType",headerName:"Request Type",editable:!1,flex:1.5},{field:"scheduledBy",headerName:"Scheduled By",editable:!1,flex:4},{field:"scheduledOn",headerName:"Scheduled On",editable:!1,flex:1},{field:"totalObjects",headerName:"Total Objects",editable:!1,flex:1,renderHeader:()=>n(Ms,{title:"Objects count scheduled for SAP syndication",arrow:!0,children:n("span",{children:"Request ID"})})},{field:"pendingObjects",headerName:"Pending Objects",editable:!1,flex:1,renderHeader:()=>n(Ms,{title:"Objects count pending for SAP syndicated.",arrow:!0,children:n("span",{children:"Request ID"})})},{field:"objectSuccessCount",headerName:"Success Objects",editable:!1,flex:1,renderHeader:()=>n(Ms,{title:"Objects count syndicated in SAP",arrow:!0,children:n("span",{children:"Request ID"})})},{field:"objectFailureCount",headerName:"Failure Objects",editable:!1,flex:1,renderHeader:()=>n(Ms,{title:"Objects count failed during syndication in SAP",arrow:!0,children:n("span",{children:"Request ID"})})},{field:"retryCount",headerName:"Retry Count",editable:!1,flex:1,renderHeader:()=>n(Ms,{title:"Number of times request retriggered.(Max- 3 count, after that it wouldnt be picked & set as status- Retry Count Exceeded)",arrow:!0,children:n("span",{children:"Request ID"})})},{field:"schedulerStatus",headerName:"Scheduler Status",editable:!1,flex:1},{field:"action",headerName:"Action",editable:!1,flex:1}],he=()=>{ne()},ne=()=>{var O;const C=Fn().format("YYYY-MM-DDTHH:mm:ss.SSSZ"),S={modules:"Material,Cost Center,Profit Center,CC-PC Combo,General Ledger",requestTypes:"Create with Upload,Change with Upload,Extend with Upload",statuses:"Scheduler - Failed,Scheduler - Pending,Scheduler - Partially Completed",toDate:Fn(q==null?void 0:q.createdOn[1]).utc().format("YYYY-MM-DDTHH:mm:ss")??"",fromDate:Fn(q==null?void 0:q.createdOn[0]).utc().format("YYYY-MM-DDTHH:mm:ss")??""},L=k=>{var x,N,D,I;if(k.statusCode===200){let ge=(k==null?void 0:k.data)||[];se(!0);let ee=ge==null?void 0:ge.map(($,qe)=>({id:qe,schedulerId:$==null?void 0:$.SchedulerId,reqId:$==null?void 0:$.RequestId,reqType:$==null?void 0:$.RequestType,priority:$==null?void 0:$.Priority,scheduledBy:$==null?void 0:$.ReqScheduledBy,scheduledOn:$==null?void 0:$.ReqScheduledOn,totalObjects:$==null?void 0:$.ObjectCount,pendingObjects:$==null?void 0:$.PendingObjectsCount,retryCount:$==null?void 0:$.RetryCount,module:$==null?void 0:$.Module,objectSuccessCount:$==null?void 0:$.ObjectsSuccessCount,objectFailureCount:$==null?void 0:$.ObjectsFailureCount,updatedOn:$==null?void 0:$.ReqUpdatedOn,schedulerStatus:$==null?void 0:$.SchedulerStatus}));const Ue=ee==null?void 0:ee.filter($=>$.module==="Material"&&$.reqType==="Create with Upload"),M=(Ue==null?void 0:Ue.length)>0?Math.max(...Ue.map($=>$.priority)):0;var U={id:ee.length+1,schedulerId:"",reqId:((x=e.taskData)==null?void 0:x.requestId)||((N=e.taskData)==null?void 0:N.ATTRIBUTE_1)||"",reqType:((D=e==null?void 0:e.taskData)==null?void 0:D.ATTRIBUTE_2)??"Create with Upload",priority:M+1,scheduledBy:((I=e.userData)==null?void 0:I.emailId)??"",scheduledOn:C??"",totalObjects:(Q==null?void 0:Q.totalElements)||0,pendingObjects:(Q==null?void 0:Q.totalElements)||0,retryCount:0,module:"Material",objectSuccessCount:0,objectFailureCount:0,updatedOn:C??"",schedulerStatus:"Scheduler - Pending"};const ce=[...ee,U].sort(($,qe)=>$.module!==qe.module?$.module.localeCompare(qe.module):$.reqType!==qe.reqType?$.reqType.localeCompare(qe.reqType):$.priority-qe.priority);o(ce)}else e.setDialogTitle("Error"),e.setSuccessMsg(!1),e.setMessageDialogMessage("Failed Fetching Scheduled Requests. Try Once more."),e.setMessageDialogSeverity("danger"),e.handleMessageDialogClickOpen()},A=k=>{g(k)};Xe(`/${lp}${(O=Fe.DATA)==null?void 0:O.FETCH_SCHEDULERS_IN_REQ_BENCH}`,"post",L,A,S)},J=()=>{l(!1),se(!1)},f=C=>{o(C)},z=C=>{var U;const S=e.requestType===b.CREATE_WITH_UPLOAD?`/${Re}${Fe.MASS_ACTION.CREATE_MAT_APPROVED}`:e.requestType===b.EXTEND_WITH_UPLOAD?`/${Re}${Fe.MASS_ACTION.EXTEND_MAT_APPROVED}`:`/${Re}${Fe.MASS_ACTION.CHANGE_MAT_APPROVED}`;J(),e.setBlurLoading(!0);let L=(e==null?void 0:e.requestType)===((U=b)==null?void 0:U.CHANGE_WITH_UPLOAD)?ie(!0):e.createPayloadFromReduxState(c);const A=L==null?void 0:L.map(x=>({...x,Tochildrequestheaderdata:{...x==null?void 0:x.Tochildrequestheaderdata,IsScheduled:!0}}));Xe(S,"post",x=>{e.setBlurLoading(!1),x.statusCode===200||x.statusCode===201?(e.setMessageDialogSeverity("success"),e.setSuccessMsg(!0),p()):(e.setDialogTitle("Error"),e.setSuccessMsg(!1),e.setTextInput(!1),e.setMessageDialogMessage((x==null?void 0:x.message)??"Failed Submitting Request"),e.setMessageDialogSeverity("danger"),e.setBlurLoading(!1),e.handleMessageDialogClickOpen())},x=>{console.log("error")},A)};var j=r==null?void 0:r.map((C,S)=>({SchedulerId:(C==null?void 0:C.schedulerId)??"",RequestId:C==null?void 0:C.reqId,RequestType:C==null?void 0:C.reqType,Module:C==null?void 0:C.module,Priority:C==null?void 0:C.priority,ObjectCount:C==null?void 0:C.totalObjects,ObjectsSuccessCount:C==null?void 0:C.objectSuccessCount,ObjectsFailureCount:C==null?void 0:C.objectFailureCount,PendingObjectsCount:C==null?void 0:C.pendingObjects,ReqScheduledBy:C==null?void 0:C.scheduledBy,ReqScheduledOn:C==null?void 0:C.scheduledOn,ReqUpdatedOn:C==null?void 0:C.updatedOn,SchedulerStatus:C==null?void 0:C.schedulerStatus,RetryCount:C==null?void 0:C.retryCount}));const p=()=>{var L,A;const C=O=>{O.statusCode===200?(e.setMessageDialogSeverity("success"),e.setMessageDialogMessage("Request has been submitted and scheduled for Syndication."),e.setBlurLoading(!1),e.setSuccessMsg(!0),e.handleSnackBarOpen(),setTimeout(()=>{V("/masterDataCockpit/materialMaster/materialSingle")},3e3)):(O==null?void 0:O.statusCode)===400?(e.setDialogTitle("Info"),e.setSuccessMsg(!1),e.setMessageDialogMessage(O==null?void 0:O.message),e.setTextInput(!1),e.setMessageDialogSeverity("info"),e.setMessageDialogOK(!1),e.setBlurLoading(!1),e.handleErrorDialogClickOpen(),setTimeout(()=>{V("/requestBench")},3e3)):(e.setDialogTitle("Error"),e.setSuccessMsg(!1),e.setMessageDialogMessage("Failed Scheduling Request, You will be redirected to Request Bench for Scheduling it."),e.setTextInput(!1),e.setMessageDialogSeverity("danger"),e.setMessageDialogOK(!1),e.setBlurLoading(!1),e.handleErrorDialogClickOpen(),setTimeout(()=>{V("/requestBench")},3e3))},S=O=>{console.log(O)};Xe(`/${Re}${(A=(L=Fe)==null?void 0:L.DATA)==null?void 0:A.UPDATE_PRIORITY}`,"post",C,S,j)},T=zn(({className:C,...S})=>n(Ms,{...S,classes:{popper:C}}))({[`& .${Na.tooltip}`]:{maxWidth:"none"}});return R(cn,{open:u,onClose:J,fullWidth:!0,maxWidth:"xl",PaperProps:{sx:{borderRadius:3,boxShadow:8,backgroundColor:"#ffffff"}},children:[R(ye,{sx:{px:3,py:2,borderBottom:"1px solid #e0e0e0",display:"flex",justifyContent:"space-between",alignItems:"center",backgroundColor:"#F4F6FA",borderTopLeftRadius:12,borderTopRightRadius:12},children:[n(mt,{variant:"h6",fontWeight:600,color:"text.primary",children:"List of Requests Scheduled"}),n(T,{arrow:!0,title:n(mt,{fontSize:12,children:"Here you can prioritize your requests"}),children:n(Ds,{size:"small",children:n(ip,{fontSize:"small"})})})]}),n(Lo,{sx:{px:3,py:2},children:n(ye,{sx:{border:"1px solid #e0e0e0",borderRadius:2,overflow:"hidden"},children:n(ap,{columns:H,row:r,onRowUpdate:f,selectionType:"SAPScheduler",showDragIcon:!0})})}),R(Do,{sx:{px:3,py:2,backgroundColor:"#FAFAFA",borderTop:"1px solid #e0e0e0",borderBottomLeftRadius:12,borderBottomRightRadius:12},children:[n(It,{onClick:J,variant:"outlined",color:"primary",sx:{textTransform:"capitalize",minWidth:100},children:"Cancel"}),n(It,{onClick:()=>z(e.currentButtonState),variant:"contained",color:"primary",sx:{textTransform:"capitalize",minWidth:100},children:"Submit"})]})]})}),$r=e=>{var ut,ys,Xs,uo,us,co,Po,Qs,Ot,Gs,Zs,_o,Fo,Io,ho,is,Wo,so,qo,jo,zo,Yo,Xo;const t=oe(pe=>pe.payload),r=oe(pe=>pe.payload.dynamicKeyValues),o=oe(pe=>pe.payload.changeFieldRows),c=oe(pe=>pe.payload.selectedRows),s=(ut=t==null?void 0:t.payloadData)==null?void 0:ut.RequestType,[m,l]=d.useState(!1),[u,se]=d.useState("success"),[ie,q]=d.useState(!1),[g,V]=d.useState(""),[Q,H]=d.useState(""),[he,ne]=d.useState(!1),[J,f]=d.useState(""),[z,j]=d.useState(!1),[p,T]=d.useState(""),[v,C]=d.useState(""),[S,L]=d.useState(!1),[A,O]=d.useState([]),[k,U]=d.useState([]),[x,N]=d.useState(!1),[D,I]=d.useState(!1),{t:ge}=bn(),[ee,Ue]=d.useState(!1),[M,Se]=d.useState(!1),[ce,$]=d.useState(""),[qe,F]=d.useState(!1),[Ke,Us]=d.useState(!1),[Wt,$s]=d.useState(""),[Fs,ss]=d.useState(""),[io,Ws]=d.useState(!1),[ks,Qt]=d.useState({}),[fs,Ht]=d.useState(""),[ns,Zt]=d.useState("");let xs=oe(pe=>pe.userManagement.userData),lt=oe(pe=>pe.userManagement.taskData);const He=wr(),Rt=Ro(),Ys=tn(),Bt=Ys.state,Me=oe(pe=>pe.payload.payloadData),it=oe(pe=>pe.payload.requestorPayload),Ut=oe(pe=>pe.tabsData.changeFieldsDT),Ns=(Me==null?void 0:Me.TemplateName)||"",{changePayloadForTemplate:ds}=$i(Ns),rs=new URLSearchParams(Ys.search.split("?")[1]),at=rs.get("RequestId"),yt=rs.get("reqBench"),ws=!(lt!=null&&lt.taskId)&&!yt,[Hs,os]=d.useState(!1),{customError:ls}=sn(),{createFCPayload:ao}=Xh(),{createPayloadFromReduxState:Ye}=ka({initialReqScreen:ws,isReqBench:yt,remarks:p,userInput:ce,selectedLevel:v}),[Mt,gs]=d.useState(!1),[Lt,ps]=d.useState(!1),[Js,Bs]=d.useState(!1),[st,Ts]=d.useState(""),to=((ys=t==null?void 0:t.payloadData)==null?void 0:ys.RequestType)===b.CREATE_WITH_UPLOAD||((Xs=t==null?void 0:t.payloadData)==null?void 0:Xs.RequestType)===b.EXTEND_WITH_UPLOAD||((uo=t==null?void 0:t.payloadData)==null?void 0:uo.RequestType)===b.CHANGE_WITH_UPLOAD,{showSnackbar:St}=Dh(),es=oe(pe=>pe.request.tabValue),ot=200,Dt=d.useRef(),Rs=()=>{q(!0)},ae=()=>{q(!1)},$e=()=>{I(!0)},me=()=>{I(!1)},Ne=()=>{var pe;ns===go.SAVE?(me(),Vt()):ns===((pe=go)==null?void 0:pe.VALIDATE)&&(me(),_s())},Ve=()=>{j(!0)},Oe=()=>{T(""),j(!1)},ct=(pe,ht)=>{const ft=pe.target.value;if(gs(ft.length>=ot),ft.length>0&&ft[0]===" ")T(ft.trimStart()),Rt(hi({keyName:"Comments",data:ft.trimStart()}));else{let At=ft;T(At),Rt(hi({keyName:"Comments",data:At}))}},ke=pe=>{C(pe.target.value),Rt(hi({keyName:"Level",data:pe.target.value}))},Vt=()=>{var ve,Nt,$t,kt,as,qs,gt,Gt,De;Oe(),ne(!0);var pe;((ve=t==null?void 0:t.payloadData)==null?void 0:ve.RequestType)===b.CREATE||((Nt=t==null?void 0:t.payloadData)==null?void 0:Nt.RequestType)===b.CREATE_WITH_UPLOAD?ns===go.SAVE?pe=`/${Re}/massAction/createMaterialSaveAsDraft`:pe=(xs==null?void 0:xs.role)==="Approver"?`/${Re}/massAction/createBasicMaterialsApproved`:`/${Re}/massAction/createMaterialSubmitForReview`:(($t=t==null?void 0:t.payloadData)==null?void 0:$t.RequestType)===b.EXTEND_WITH_UPLOAD?ns===go.SAVE?pe=`/${Re}${Fe.MASS_ACTION.EXTEND_MATERIAL_SAVE_AS_DRAFT}`:pe=`/${Re}${Fe.MASS_ACTION.EXTEND_MATERIAL_DIRECT_APPROVED}`:(s===b.CHANGE||s===b.CHANGE_WITH_UPLOAD)&&(ns===go.SAVE?pe=`/${Re}/massAction/changeMaterialSaveAsDraft`:pe=(xs==null?void 0:xs.role)==="Approver"?`/${Re}/massAction/changeBasicMaterialsApproved`:`/${Re}/massAction/changeMaterialSubmitForReview`);const ht=pt=>{if(pt.statusCode>=Jt.STATUS_200&&pt.statusCode<Jt.STATUS_300){ne(!1);let ms;(xs==null?void 0:xs.role)==="Approver"?ms=`Material Syndicated successfully in SAP with Material ID : ${pt==null?void 0:pt.body.join(", ")}`:ns===go.SAVE?ms=pt==null?void 0:pt.message:ms=`Request Submitted for Approval with Request ID ${pt==null?void 0:pt.body}`,St(ms,"success"),Rs(),He("/masterDataCockpit/materialMaster/materialSingle")}else ne(!1),St(pt==null?void 0:pt.message,"error");Zt("")},ft=pt=>{St(pt==null?void 0:pt.message,"error"),ne(!1),Zt("")};var At;At=((kt=t==null?void 0:t.payloadData)==null?void 0:kt.RequestType)===b.CREATE||((as=t==null?void 0:t.payloadData)==null?void 0:as.RequestType)===b.CREATE_WITH_UPLOAD||((qs=t==null?void 0:t.payloadData)==null?void 0:qs.RequestType)===b.EXTEND_WITH_UPLOAD?Ye(t):((gt=t==null?void 0:t.payloadData)==null?void 0:gt.RequestType)===b.CHANGE?ds(!!yt):((Gt=t==null?void 0:t.payloadData)==null?void 0:Gt.RequestType)===b.CHANGE_WITH_UPLOAD?ds(!0):((De=t==null?void 0:t.payloadData)==null?void 0:De.RequestType)===b.CHANGE?Ye(t):[],Xe(pe,"post",ht,ft,At)},Es=async pe=>{var ht,ft,At,ve,Nt;if(((pe==null?void 0:pe.MDG_MAT_DYN_BTN_ACTION_TYPE)==="handleValidate"||(pe==null?void 0:pe.MDG_MAT_DYN_BTN_ACTION_TYPE)==="handleValidate1")&&(((ht=t==null?void 0:t.payloadData)==null?void 0:ht.RequestType)===b.CREATE||((ft=t==null?void 0:t.payloadData)==null?void 0:ft.RequestType)===b.CREATE_WITH_UPLOAD))try{const $t=await e.validateMaterials();os($t)}catch($t){ls($t);return}H(""),Us("success"),Qt(pe),ss(pe.MDG_MAT_DYN_BTN_COMMENT_BOX_NAME),Se(pe.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT===((At=$n)==null?void 0:At.MANDATORY)),F(pe.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT===((ve=$n)==null?void 0:ve.MANDATORY)||pe.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT==="Optional"),Ht(pe.MDG_MAT_DYN_BTN_ACTION_TYPE),pe.MDG_MAT_DYN_BTN_BUTTON_NAME===go.SEND_BACK||pe.MDG_MAT_DYN_BTN_BUTTON_NAME===go.CORRECTION?ps(!0):ps(!1),pe.MDG_MAT_DYN_BTN_BUTTON_NAME===go.SAP_SYNDICATE?Bs(!0):Bs(!1),pe.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT===((Nt=$n)==null?void 0:Nt.MANDATORY)||pe.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT==="Optional"?Qe():xe(pe.MDG_MAT_DYN_BTN_ACTION_TYPE,pe)},Pt=()=>{Oe(),ne(!0);const pe=s===b.CREATE||s===b.CREATE_WITH_UPLOAD?`/${Re}/massAction/createMaterialApprovalSubmit`:s===b.EXTEND||s===b.EXTEND_WITH_UPLOAD?`/${Re}/massAction/extendMaterialApprovalSubmit`:`/${Re}/massAction/changeMaterialApprovalSubmit`,ht=ve=>{ve.statusCode>=200&&ve.statusCode<300?(ne(!1),St(`Request Submitted for Approval with Request ID ${ve==null?void 0:ve.body}`,"success"),He("/masterDataCockpit/materialMaster/materialSingle")):(ne(!1),St(ve==null?void 0:ve.message,"error"))},ft=()=>{ne(!1),St("Failed Submitting Request.","error")};var At;At=s===b.CREATE||s===b.EXTEND||s===b.EXTEND_WITH_UPLOAD||s===b.CREATE_WITH_UPLOAD?Ye(t):ds(!0),Xe(pe,"post",ht,ft,At)},je=pe=>{var $t;Oe(),ne(!0);var ht=s===b.CREATE||s===b.CREATE_WITH_UPLOAD?`/${Re}/massAction/createMaterialApproved`:s===b.EXTEND||s===b.EXTEND_WITH_UPLOAD?`/${Re}/massAction/extendMaterialApproved`:lt.ATTRIBUTE_2===b.FINANCE_COSTING?`/${Re}/${Fe.MASS_ACTION.FINANCE_COSTING_APPROVED}`:`/${Re}/massAction/changeMaterialApproved`;const ft=kt=>{kt.statusCode>=200&&kt.statusCode<300?(ne(!1),St(pe==null?void 0:pe.MDG_MAT_DYN_BTN_SNACKBAR_SUCC_MSG,"success"),He("/masterDataCockpit/materialMaster/materialSingle")):(ne(!1),St(pe==null?void 0:pe.MDG_MAT_DYN_BTN_SNACKBAR_FAIL_MSG,"error"))},At=kt=>{St((kt==null?void 0:kt.message)||"Failed Submitting Request.","error"),ne(!1)};var ve;const Nt={requestId:($t=t==null?void 0:t.payloadData)==null?void 0:$t.RequestId,taskId:(lt==null?void 0:lt.taskId)||"",taskName:(lt==null?void 0:lt.taskDesc)||"",comments:p||ce,creationDate:lt!=null&&lt.createdOn?Is(lt==null?void 0:lt.createdOn):null,dueDate:lt!=null&&lt.criticalDeadline?Is(lt==null?void 0:lt.criticalDeadline):null};ve=s===b.CREATE||s===b.CREATE_WITH_UPLOAD||s===b.EXTEND||s===b.EXTEND_WITH_UPLOAD?Ye(t):ds(!0),Xe(ht,"post",ft,At,lt.ATTRIBUTE_2===b.FINANCE_COSTING?Nt:ve)},Pe=()=>{Oe(),ne(!0);const pe=s===b.CREATE||s===b.CREATE_WITH_UPLOAD?`/${Re}${Fe.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`:s===b.EXTEND||s===b.EXTEND_WITH_UPLOAD?`/${Re}${Fe.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`:`/${Re}${Fe.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`,ht=s===b.CREATE||s===b.CREATE_WITH_UPLOAD||s===b.EXTEND||s===b.EXTEND_WITH_UPLOAD?Ye(t):ds(!0);Xe(pe,"post",ve=>{(ve==null?void 0:ve.statusCode)===Jt.STATUS_200?(St(ve.message,"success"),Rt(fi({data:{}})),He(Eo.MY_TASK)):St(ve.error,"error"),ne(!1)},ve=>{St(ve.error,"error"),ne(!1)},ht)},_t=()=>{Oe(),ne(!0);const pe=s===b.CREATE||s===b.CREATE_WITH_UPLOAD?`/${Re}${Fe.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`:s===b.EXTEND||s===b.EXTEND_WITH_UPLOAD?`/${Re}${Fe.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`:`/${Re}${Fe.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`,ht=s===b.CREATE||s===b.CREATE_WITH_UPLOAD||s===b.EXTEND||s===b.EXTEND_WITH_UPLOAD?Ye(t):ds(!0);Xe(pe,"post",ve=>{(ve==null?void 0:ve.statusCode)===Jt.STATUS_200?(St(ve.message,"success"),Rt(fi({data:{}})),He(Eo.MY_TASK)):St(ve.error,"error"),ne(!1)},ve=>{St(ve.error,"error"),ne(!1)},ht)},jt=()=>{Oe(),ne(!0);const pe=s===b.CREATE||s===b.CREATE_WITH_UPLOAD?`/${Re}${Fe.MASS_ACTION.CREATE_MATERIAL_REJECTION}`:s===b.EXTEND||s===b.EXTEND_WITH_UPLOAD?`/${Re}${Fe.MASS_ACTION.EXTEND_MATERIAL_REJECTION}`:`/${Re}${Fe.MASS_ACTION.CHANGE_MATERIAL_REJECTION}`,ht=s===b.CREATE||s===b.CREATE_WITH_UPLOAD||s===b.EXTEND||s===b.EXTEND_WITH_UPLOAD?Ye(t):ds(!0);Xe(pe,"post",ve=>{(ve==null?void 0:ve.statusCode)===Jt.STATUS_200?(St(ve.message,"success"),Rt(fi({data:{}})),He(Eo.MY_TASK)):St(ve.error,"error"),ne(!1)},ve=>{St(ve.error,"error"),ne(!1)},ht)},_s=pe=>{ne(!0);const ht=(lt==null?void 0:lt.ATTRIBUTE_2)===b.FINANCE_COSTING?`/${Re}${Fe.MASS_ACTION.VALIDATE_FINANCE_COSTING}?requestId=${at==null?void 0:at.slice(3)}`:`/${Re}${Fe.MASS_ACTION.VALIDATE_MATERIAL}`,ft=(lt==null?void 0:lt.ATTRIBUTE_2)===b.FINANCE_COSTING?ao():s===b.CREATE||s===b.CREATE_WITH_UPLOAD||s===b.EXTEND||s===b.EXTEND_WITH_UPLOAD?Ye(t):s===b.CHANGE||s===b.CHANGE_WITH_UPLOAD?yt&&at?ds(!0):!yt&&!at?ds(!1):!yt&&at?ds(!0):[]:[];Xe(ht,"post",Nt=>{if(ne(!1),(Nt==null?void 0:Nt.statusCode)===Jt.STATUS_200){if(St((pe==null?void 0:pe.MDG_MAT_DYN_BTN_SNACKBAR_SUCC_MSG)||(Nt==null?void 0:Nt.message),"success"),(ws||yt)&&(s===b.CHANGE||s===b.CHANGE_WITH_UPLOAD)||s===b.CREATE_WITH_UPLOAD||s===b.EXTEND_WITH_UPLOAD||s===b.EXTEND){He(Eo.REQUEST_BENCH);return}He(Eo.MY_TASK)}else St((pe==null?void 0:pe.MDG_MAT_DYN_BTN_SNACKBAR_FAIL_MSG)||"Validation failed.","error")},()=>{St(pe==null?void 0:pe.MDG_MAT_DYN_BTN_SNACKBAR_FAIL_MSG,"error"),ne(!1)},ft)},Ft=()=>{Oe(),ne(!0);const pe=s===b.CREATE||s===b.CREATE_WITH_UPLOAD?`/${Re}/massAction/createMaterialApprovalSubmit`:s===b.EXTEND||s===b.EXTEND_WITH_UPLOAD?`/${Re}/massAction/extendMaterialSubmitForReview`:`/${Re}/massAction/changeMaterialApprovalSubmit`,ht=ve=>{ve.statusCode===Jt.STATUS_200?(ne(!1),St(`Request Submitted for Approval with Request ID ${ve==null?void 0:ve.body}`,"success"),Rs(),He("/masterDataCockpit/materialMaster/materialSingle")):St(ve==null?void 0:ve.message,"error")},ft=ve=>{St((ve==null?void 0:ve.error)||"Failed Submitting Request.","error"),ne(!1)};var At;At=s===b.CREATE||s===b.CREATE_WITH_UPLOAD||s===b.EXTEND||s===b.EXTEND_WITH_UPLOAD?Ye(t):ds(!0),Xe(pe,"post",ht,ft,At),l(!0),Rs()},te=()=>{Oe(),ne(!0);const pe=`/${Re}${Fe.MASS_ACTION.EXTEND_MATERIAL_SAVE_AS_DRAFT}`,ht=ve=>{ve.statusCode===Jt.STATUS_200?(ne(!1),St(ve==null?void 0:ve.message,"success"),He(Eo.REQUEST_BENCH)):(ne(!1),St(ve==null?void 0:ve.message,"error"))},ft=ve=>{St(ve==null?void 0:ve.error,"error"),ne(!1)};let At;At=Ye(t),Xe(pe,"post",ht,ft,At)},xe=(pe,ht)=>{switch(pe){case"handleSubmitForApproval":Pt();break;case"handleSubmitForReview":Ft();break;case"handleSendBack":Pe();break;case"handleCorrection":_t();break;case"handleReject":jt();break;case"Validate":B(ht);break;case"handleValidate":B(ht);break;case"handleSAPSyndication":je(ht);break;case"handleDraft":te();break;case"handleSubmit":Ft();break;default:console.log("Unknown action type")}},B=pe=>{var ht,ft;Zt((ht=go)==null?void 0:ht.VALIDATE),V(ge((ft=Dn)==null?void 0:ft.VALIDATE_MSG)),s===b.CREATE||s===b.EXTEND||s===b.CREATE_WITH_UPLOAD||s===b.EXTEND_WITH_UPLOAD||(lt==null?void 0:lt.ATTRIBUTE_2)===b.FINANCE_COSTING?_s(pe):de(pe)},de=pe=>{Array.isArray(o)?le(pe):typeof o=="object"&&We(pe)},w=()=>{const pe=Ut==null?void 0:Ut["Config Data"],ht={};return Object.entries(pe).forEach(([ft,At])=>{const ve=At.filter(Nt=>{var $t;return Nt.visibility===(($t=$n)==null?void 0:$t.MANDATORY)}).map(Nt=>({jsonName:Nt.jsonName,fieldName:Nt.fieldName}));if(!(ve!=null&&ve.some(Nt=>Nt.jsonName==="Material"))){const Nt=At.find($t=>$t.jsonName==="Material");Nt&&ve.push({jsonName:Nt.jsonName,fieldName:Nt.fieldName})}(ve==null?void 0:ve.length)>0&&(ht[ft]=ve)}),ht},Te=(pe,ht)=>{var ft,At;if(Array.isArray(o)){const ve=Ns===((ft=Je)==null?void 0:ft.LOGISTIC)?[...ht,{jsonName:"AltUnit",fieldName:"Alternative Unit of Measure"}]:Ns===((At=Je)==null?void 0:At.UPD_DESC)?[...ht,{jsonName:"Langu",fieldName:"Language"}]:ht,Nt={};return pe==null||pe.forEach(($t,kt)=>{var qs;const as=(qs=ve==null?void 0:ve.filter(gt=>!$t[gt==null?void 0:gt.jsonName]||$t[gt==null?void 0:gt.jsonName]===""))==null?void 0:qs.map(gt=>gt==null?void 0:gt.fieldName);(as==null?void 0:as.length)>0&&(Nt[kt]={id:$t.id,slNo:$t.slNo,missingFields:as})}),Nt}else if(typeof o=="object"){let ve={},Nt=0;return Object.keys(pe).forEach($t=>{pe[$t].forEach(kt=>{var qs;const as=(qs=ht[$t])==null?void 0:qs.filter(gt=>!kt[gt.jsonName]||kt[gt.jsonName]==="").map(gt=>gt.fieldName);as.length>0&&(ve[Nt]={id:kt.id,slNo:kt.slNo,type:kt.type,missingFields:as},Nt++)})}),ve}},We=pe=>{var ve,Nt,$t,kt;const ht=Object.fromEntries(Object.entries(o).map(([as,qs])=>[as,qs.filter(gt=>{var Gt;return(Gt=c==null?void 0:c[as])==null?void 0:Gt.includes(gt.id)})])),ft=w(),At=Te(ht,ft);if(Rt(da(At)),Object.keys(At).length>0){const as=Object.keys(At).map(Gt=>{var De,pt,ms;return{"Table Name":(De=At[Gt])==null?void 0:De.type,"Sl. No":(pt=At[Gt])==null?void 0:pt.slNo,"Missing Fields":(ms=At[Gt].missingFields)==null?void 0:ms.join(", ")}});N(!0),Us("danger"),ss("Please Fill All the Mandatory Fields : ");const qs=(ve=Object.keys(as[0]))==null?void 0:ve.map(Gt=>({field:Gt,headerName:(Gt==null?void 0:Gt.charAt(0).toUpperCase())+(Gt==null?void 0:Gt.slice(1)),flex:Gt==="Sl. No"?.5:Gt==="Missing Fields"?3:1.5,align:"center",headerAlign:"center"}));U(qs);const gt=as==null?void 0:as.map(Gt=>({...Gt,id:Co()}));O(gt),L(!0),Qe(),Rt(xl(!0))}else{if(s===((Nt=b)==null?void 0:Nt.CHANGE)||s===(($t=b)==null?void 0:$t.CHANGE_WITH_UPLOAD)){if(!at||at&&it&&((kt=Object==null?void 0:Object.keys(it))!=null&&kt.length)){$e();return}_s(pe);return}se("success"),H("Data Validated Successfully"),Rs(),Rt(xl(!1)),e==null||e.setCompleted([!0,!0])}},le=pe=>{var At,ve,Nt,$t;const ht=o==null?void 0:o.filter(kt=>c==null?void 0:c.includes(kt.id)),ft=Te(ht,Ut==null?void 0:Ut["Mandatory Fields"]);if(Rt(da(ft)),Object.keys(ft).length>0){const kt=Object.keys(ft).map(gt=>{var Gt,De;return{"Sl. No":(Gt=ft[gt])==null?void 0:Gt.slNo,"Missing Fields":(De=ft[gt].missingFields)==null?void 0:De.join(", ")}});N(!0),Us("danger"),ss("Please Fill All the Mandatory Fields : ");const as=(At=Object.keys(kt[0]))==null?void 0:At.map(gt=>({field:gt,headerName:(gt==null?void 0:gt.charAt(0).toUpperCase())+(gt==null?void 0:gt.slice(1)),flex:gt==="Sl. No"?.5:gt==="Missing Fields"?3:1,align:"center",headerAlign:"center"}));U(as);const qs=kt==null?void 0:kt.map(gt=>({...gt,id:Co()}));O(qs),L(!0),Qe(),Rt(xl(!0))}else{if(s===((ve=b)==null?void 0:ve.CHANGE)||s===((Nt=b)==null?void 0:Nt.CHANGE_WITH_UPLOAD)){if(!at||at&&it&&(($t=Object==null?void 0:Object.keys(it))!=null&&$t.length)){$e();return}_s(pe);return}se("success"),H("Data Validated Successfully"),Rs(),Rt(xl(!1)),e==null||e.setCompleted([!0,!0])}},dt=()=>{var pe;if(M&&!ce){Ue(!0);return}else st==="scheduleSyndication"?Dt!=null&&Dt.current&&((pe=Dt==null?void 0:Dt.current)==null||pe.handlePriorityDialogClickOpen()):xe(fs,ks);ze()},ze=()=>{Ws(!1),$(""),Ue(!1),Se(!1)},Qe=()=>{Ws(!0)};function Ct(){Ve(),Zt("")}const _e=()=>{Ct()};return R(bo,{children:[n(Ur,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:R(cp,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"space-between",alignItems:"center",gap:1,width:"100%"},children:[(!at||yt&&(Bt==null?void 0:Bt.reqStatus)===((us=Ao)==null?void 0:us.DRAFT))&&(Me==null?void 0:Me.RequestType)===b.CHANGE&&(Me==null?void 0:Me.TemplateName)===((co=Je)==null?void 0:co.SET_DNU)&&n(ye,{sx:{flex:2,marginLeft:"90px"},children:R("span",{children:[n("strong",{children:"Note"}),": All default values for ",n("strong",{children:"Set To DNU"})," template will be fetched after ",n("strong",{children:"Validation"}),"."]})}),R(ye,{sx:{display:"flex",gap:1},children:[s!==b.EXTEND_WITH_UPLOAD&&s!==b.EXTEND&&n(Ss,{children:!at||yt&&((Po=ko)!=null&&Po.includes(Bt==null?void 0:Bt.reqStatus))?R(Ss,{children:[n(It,{variant:"contained",color:"primary",onClick:()=>{var pe,ht;if(Zt(go.SAVE),V(ge((pe=Dn)==null?void 0:pe.SAVE_AS_DRAFT_MSG)),(Me==null?void 0:Me.RequestType)===b.CHANGE&&(!at||at&&it&&((ht=Object==null?void 0:Object.keys(it))!=null&&ht.length))){$e();return}Ve()},children:ge("Save As Draft")}),((Qs=t==null?void 0:t.payloadData)==null?void 0:Qs.RequestType)===b.CREATE&&es===Dl.REQUEST_DETAILS&&n(Ms,{title:Ph.VALIDATE_MANDATORY,children:n(It,{variant:"contained",color:"primary",onClick:e==null?void 0:e.validateMaterials,children:ge("Validate")})}),es===Dl.REQUEST_DETAILS&&((Me==null?void 0:Me.RequestType)===b.CHANGE||(Me==null?void 0:Me.RequestType)===b.CHANGE_WITH_UPLOAD||(Me==null?void 0:Me.RequestType)===b.CREATE_WITH_UPLOAD||(Me==null?void 0:Me.RequestType)===b.EXTEND_WITH_UPLOAD)?n(Ss,{children:n(It,{variant:"contained",color:"primary",onClick:B,children:ge("Validate")})}):n(Ss,{children:es===Dl.PREVIEW&&n(It,{variant:"contained",color:"primary",onClick:_e,disabled:(Me==null?void 0:Me.RequestType)===((Ot=b)==null?void 0:Ot.CHANGE)||(Me==null?void 0:Me.RequestType)===((Gs=b)==null?void 0:Gs.CHANGE_WITH_UPLOAD)||(Me==null?void 0:Me.RequestType)===b.CREATE_WITH_UPLOAD||(Me==null?void 0:Me.RequestType)===b.EXTEND_WITH_UPLOAD?(Me==null?void 0:Me.RequestStatus)!==((Zs=Ao)==null?void 0:Zs.VALIDATED_REQUESTOR):e==null?void 0:e.submitForApprovalDisabled,children:ge("Submit")})})]}):null}),((Me==null?void 0:Me.RequestType)===b.EXTEND||(Me==null?void 0:Me.RequestType)===b.EXTEND_WITH_UPLOAD&&(!at||yt&&((_o=ko)==null?void 0:_o.includes(Bt==null?void 0:Bt.reqStatus))||!yt&&at)||!yt&&at)&&((Fo=e==null?void 0:e.filteredButtons)==null?void 0:Fo.map((pe,ht)=>{var as,qs,gt,Gt,De,pt,ms,gn,yn,Sn,Nn;const{MDG_MAT_DYN_BTN_BUTTON_NAME:ft,MDG_MAT_DYN_BTN_BUTTON_STATUS:At}=pe,ve=ft==="SAP Syndication"||ft==="Submit",Nt=ft==="Forward"||ft==="Submit",$t=((as=r==null?void 0:r.requestHeaderData)==null?void 0:as.RequestStatus)==="Validated-MDM"||(Me==null?void 0:Me.RequestStatus)==="Validated-MDM"||(Me==null?void 0:Me.RequestStatus)==="Validated-Requestor"||((qs=r==null?void 0:r.childRequestHeaderData)==null?void 0:qs.RequestStatus)==="Validated-MDM"||((gt=r==null?void 0:r.childRequestHeaderData)==null?void 0:gt.RequestStatus)==="Validated-Requestor"||((Gt=e==null?void 0:e.childRequestHeaderData)==null?void 0:Gt.RequestStatus)==="Validated-MDM"||((De=e==null?void 0:e.childRequestHeaderData)==null?void 0:De.RequestStatus)==="Validated-Requestor";let kt=At==="DISABLED";return ve&&$t&&(kt=!1),(Nt&&((Me==null?void 0:Me.RequestType)===((pt=b)==null?void 0:pt.CREATE)||(Me==null?void 0:Me.RequestType)===((ms=b)==null?void 0:ms.CREATE_WITH_UPLOAD))&&!(e!=null&&e.submitForApprovalDisabled)||((Me==null?void 0:Me.RequestType)===((gn=b)==null?void 0:gn.CHANGE)||(Me==null?void 0:Me.RequestType)===((yn=b)==null?void 0:yn.CHANGE_WITH_UPLOAD)||(Me==null?void 0:Me.RequestType)===((Sn=b)==null?void 0:Sn.EXTEND)||(Me==null?void 0:Me.RequestType)===((Nn=b)==null?void 0:Nn.EXTEND_WITH_UPLOAD))&&Nt&&$t)&&(kt=!1),n(It,{variant:"contained",size:"small",sx:{...qi,mr:1},disabled:kt||he,onClick:()=>Es(pe),children:pe.MDG_MAT_DYN_BTN_BUTTON_NAME},ht)}))]})]})}),n(ki,{dialogState:io,openReusableDialog:Qe,closeReusableDialog:ze,dialogTitle:Fs,dialogMessage:Wt,handleDialogConfirm:dt,dialogOkText:"OK",dialogSeverity:Ke,showCancelButton:!0,showInputText:qe,inputText:ce,blurLoading:he,setInputText:$,mandatoryTextInput:M,remarksError:ee,isTable:S,tableColumns:k,tableRows:A,isShowWFLevel:(e==null?void 0:e.showWfLevels)&&Lt,isSyndicationBtn:Js,selectedLevel:v,handleLevelChange:ke,workFlowLevels:e.workFlowLevels,setSyndicationType:Ts,syndicationType:st,isMassSyndication:to}),n(aE,{ref:Dt,dialogTitle:Fs,setDialogTitle:ss,messageDialogMessage:Q,setMessageDialogMessage:H,messageDialogSeverity:Ke,setMessageDialogSeverity:Us,handleMessageDialogClickOpen:Qe,blurLoading:he,setBlurLoading:ne,handleMessageDialogClose:ze,createPayloadFromReduxState:Ye,successMsg:m,setSuccessMsg:l,setTextInput:F,inputText:qe,handleSnackBarOpen:Rs,taskData:lt,userData:xs,currentButtonState:ks,requestType:s}),n(iE,{open:D,onClose:me,handleOk:Ne,message:g}),Q&&n(Un,{openSnackBar:ie,alertMsg:Q,alertType:u,handleSnackBarClose:ae}),n(Cn,{blurLoading:he,loaderMessage:J}),R(cn,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:z,onClose:Oe,maxWidth:"xl",children:[R(Pn,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[n(mt,{variant:"h6",children:ns===go.SAVE?"Save As Draft":"Remarks"}),n(Ds,{sx:{width:"max-content"},onClick:Oe,children:n(Gl,{})})]}),n(Lo,{sx:{padding:".5rem 1rem"},children:ns!==go.SAVE?n(bo,{sx:{marginTop:"16px"},children:n(ye,{sx:{minWidth:400},children:R(Li,{sx:{height:"auto"},fullWidth:!0,children:[n(xn,{sx:{backgroundColor:"#F5F5F5","& .MuiOutlinedInput-root":{"& fieldset":{borderColor:Mt?(ho=(Io=Be)==null?void 0:Io.error)==null?void 0:ho.dark:"rgba(0, 0, 0, 0.23)"},"&:hover fieldset":{borderColor:Mt?(Wo=(is=Be)==null?void 0:is.error)==null?void 0:Wo.dark:"rgba(0, 0, 0, 0.23)"},"&.Mui-focused fieldset":{borderColor:Mt?(qo=(so=Be)==null?void 0:so.error)==null?void 0:qo.dark:(zo=(jo=Be)==null?void 0:jo.primary)==null?void 0:zo.dark}}},value:p,onChange:ct,inputProps:{maxLength:ot},multiline:!0,placeholder:"Enter Remarks"}),n(dp,{sx:{textAlign:"right",color:Mt?(Xo=(Yo=Be)==null?void 0:Yo.error)==null?void 0:Xo.dark:"rgba(0, 0, 0, 0.6)",marginTop:"4px"},children:`${(p==null?void 0:p.length)||0}/${ot}`})]})})}):n(ye,{sx:{margin:"15px"},children:n(mt,{sx:{fontWeight:"200"},children:Dn.DRAFT_MESSAGE})})}),R(Do,{sx:{display:"flex",justifyContent:"end"},children:[n(It,{sx:{width:"max-content",textTransform:"capitalize"},onClick:Oe,children:ge("Cancel")}),n(It,{className:"button_primary--normal",type:"save",disabled:he,onClick:Vt,variant:"contained",children:ns===go.SAVE?"Yes":"Submit"})]})]})]})},of=()=>{const{customError:e}=sn(),[t,r]=d.useState([]),[o,c]=d.useState(!1),s=oe(V=>V.userManagement.taskData),[m,l]=d.useState([]),u=oe(V=>V.applicationConfig),se=Ro();let ie={handleSubmitForApproval:6,handleSendBack:1,handleReject:3,handleValidate:5,handleSAPSyndication:8,handleIdGenerator:4,handleSubmitForReview:7,handleCorrection:2};const q=Bl(mr.CURRENT_TASK,!0,{});return d.useEffect(()=>{const V=(s==null?void 0:s.taskDesc)||(q==null?void 0:q.taskDesc),Q=t==null?void 0:t.filter(he=>he.MDG_MAT_DYN_BTN_TASK_NAME===V),H=Q==null?void 0:Q.sort((he,ne)=>{const J=ie[he.MDG_MAT_DYN_BTN_ACTION_TYPE],f=ie[ne.MDG_MAT_DYN_BTN_ACTION_TYPE];return J-f});l(H),se(up(H)),(H.find(he=>he.MDG_MAT_DYN_BTN_BUTTON_NAME===go.SEND_BACK)||H.find(he=>he.MDG_MAT_DYN_BTN_BUTTON_NAME===go.CORRECTION))&&c(!0)},[t]),{getButtonsDisplay:()=>{let V={decisionTableId:null,decisionTableName:"MDG_MAT_DYN_BUTTON_CONFIG",version:"v3",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_DYN_BTN_MODULE_NAME":"Material","MDG_CONDITIONS.MDG_MAT_DYN_BTN_REQUEST_TYPE":(s==null?void 0:s.ATTRIBUTE_2)||(q==null?void 0:q.ATTRIBUTE_2)}],systemFilters:null,systemOrders:null,filterString:null};const Q=he=>{var ne,J;if(he.statusCode===200){let f=(J=(ne=he==null?void 0:he.data)==null?void 0:ne.result[0])==null?void 0:J.MDG_MAT_DYN_BUTTON_CONFIG;r(f)}},H=he=>{e(he)};u.environment==="localhost"?Xe(`/${yo}${Fe.INVOKE_RULES.LOCAL}`,"post",Q,H,V):Xe(`/${yo}${Fe.INVOKE_RULES.PROD}`,"post",Q,H,V)},showWfLevels:o}},Ha=()=>{oe(c=>c.payload.payloadData);const e=oe(c=>c.applicationConfig);oe(c=>{var s;return(s=c.userManagement)==null?void 0:s.taskData}),Ro();const t=tn();return new URLSearchParams(t.search).get("RequestType"),{getDynamicWorkflowDT:(c,s,m="NA",l="GROUP-2",u=1)=>new Promise((se,ie)=>{let q={decisionTableId:null,decisionTableName:"MDG_MAT_DYNAMIC_WF_DT",version:"v4",conditions:[{"MDG_CONDITIONS.MDG_MAT_REQUEST_TYPE":c||"","MDG_CONDITIONS.MDG_MAT_REGION":s||"","MDG_CONDITIONS.MDG_MAT_TEMPLATE":m||"NA","MDG_CONDITIONS.MDG_MAT_BIFURCATION_GROUP":l||""}]};const g=Q=>{var H,he;if(Q.statusCode===Jt.STATUS_200){let ne=((he=(H=Q==null?void 0:Q.data)==null?void 0:H.result[0])==null?void 0:he.MDG_MAT_DYNAMIC_WF_DT)||[],J=[];ne==null||ne.forEach(f=>{f.MDG_DYNAMIC_WF_APPROVAL_LEVEL===parseInt(u)&&f.MDG_MAT_SENDBACK_ALLOWED.split(",").map(j=>parseInt(j)).forEach(j=>J.push(j))}),J=[...new Set(J)],se(J)}else ie(new Error("Failed to fetch workflow levels"))},V=Q=>{ie(Q)};e.environment==="localhost"?Xe(`/${yo}${Fe.INVOKE_RULES.LOCAL}`,"post",g,V,q):Xe(`/${yo}${Fe.INVOKE_RULES.PROD}`,"post",g,V,q)})}},cE=({requestType:e,initialPayload:t,dynamicData:r,taskData:o,singlePayloadData:c})=>{const[s,m]=d.useState([]),[l,u]=d.useState(!1),{getDynamicWorkflowDT:se}=Ha(),{customError:ie}=sn();return d.useEffect(()=>{const q=async()=>{var g,V,Q;try{u(!0);const H=(t==null?void 0:t.RequestType)===b.CHANGE||(t==null?void 0:t.RequestType)===b.CHANGE_WITH_UPLOAD?await se(t==null?void 0:t.RequestType,t==null?void 0:t.Region,t==null?void 0:t.TemplateName,(g=r==null?void 0:r.childRequestHeaderData)==null?void 0:g.MaterialGroupType,o==null?void 0:o.ATTRIBUTE_3):await se(t==null?void 0:t.RequestType,t==null?void 0:t.Region,"",(Q=(V=c==null?void 0:c[r])==null?void 0:V.Tochildrequestheaderdata)==null?void 0:Q.MaterialGroupType,o==null?void 0:o.ATTRIBUTE_3);m(H)}catch(H){ie(H)}finally{u(!1)}};t!=null&&t.RequestType&&(t!=null&&t.Region)&&r&&(o!=null&&o.ATTRIBUTE_3)&&q()},[t==null?void 0:t.Region,r,o==null?void 0:o.ATTRIBUTE_3]),{wfLevels:s,loading:l}},nf=e=>{const[t,r]=d.useState(!0),o=oe(C=>C.AllDropDown.dropDown),c=oe(C=>C.payload),s=oe(C=>C.payload.payloadData),m=s==null?void 0:s.RequestType,[l,u]=d.useState(!1),[se,ie]=d.useState(!1),q=oe(C=>C.userManagement.taskData),g=oe(C=>C.payload.filteredButtons),V=tn(),Q=new URLSearchParams(V.search),H=Q.get("RequestType"),he=Q.get("RequestId"),ne=oe(C=>C.payload.changeFieldRows),J=oe(C=>C.payload.dynamicKeyValues),f=wr(),{getButtonsDisplay:z,showWfLevels:j}=of(),{wfLevels:p}=cE({initialPayloadRequestType:m,initialPayload:s,dynamicData:J,taskData:q,singlePayloadData:c}),T=Cr(g,[To.HANDLE_SUBMIT_FOR_APPROVAL,To.HANDLE_SAP_SYNDICATION,To.HANDLE_SUBMIT_FOR_REVIEW]);d.useEffect(()=>{(q!=null&&q.ATTRIBUTE_1||H)&&z()},[q]),d.useEffect(()=>{((ne==null?void 0:ne.length)!==0&&(ne==null?void 0:ne.length)!==void 0||!v())&&(ie(!0),u(!0))},[ne]);const v=()=>{var C;return(C=Object==null?void 0:Object.values(ne))==null?void 0:C.every(S=>(Array==null?void 0:Array.isArray(S))&&(S==null?void 0:S.length)===0)};return d.useEffect(()=>{e.downloadClicked&&r(!0)},[e.downloadClicked]),R("div",{children:[((s==null?void 0:s.TemplateName)&&(ne&&(ne==null?void 0:ne.length)===0||v())||e.downloadClicked)&&n(YT,{open:t,onClose:()=>{var C;r(!1),e==null||e.setDownloadClicked(!1),he||f((C=Eo)==null?void 0:C.REQUEST_BENCH)},parameters:Ll[s==null?void 0:s.TemplateName],templateName:s==null?void 0:s.TemplateName,setShowTable:u,allDropDownData:o,setDownloadClicked:e==null?void 0:e.setDownloadClicked}),(l||se)&&!(e!=null&&e.downloadClicked)&&R(Ss,{children:[n(ZT,{setCompleted:e==null?void 0:e.setCompleted,RequestId:he}),n($r,{filteredButtons:T,setCompleted:e==null?void 0:e.setCompleted,showWfLevels:j,workFlowLevels:p})]}),n(Hi,{})]})},dE=({setIsSecondTabEnabled:e,setIsAttachmentTabEnabled:t,requestStatus:r,downloadClicked:o,setDownloadClicked:c})=>{var to,St,es,ot,Dt,Rs,ae,$e;const[s,m]=d.useState({}),[l,u]=d.useState(!1),[se,ie]=d.useState(!1),[q,g]=d.useState("success"),[V,Q]=d.useState(!1),[H,he]=d.useState([]);d.useState(!1);const[ne,J]=d.useState(),[f,z]=d.useState({}),[j,p]=d.useState(!1),[T,v]=d.useState("systemGenerated"),[C,S]=d.useState(""),[L,A]=d.useState(""),[O,k]=d.useState([]),[U,x]=d.useState(!1),N=Ro(),D=wr(),I=oe(me=>me.payload.payloadData),ge=oe(me=>me.tabsData.requestHeaderData),ee=oe(me=>me.tabsData.changeFieldsDT);let Ue=oe(me=>me.userManagement.roles);const M=oe(me=>me.payload.payloadData),Se=oe(me=>me.userManagement.userData),ce=oe(me=>{var Ne,Ve;return(Ve=(Ne=me.userManagement)==null?void 0:Ne.entitiesAndActivities)==null?void 0:Ve.Material}),$=oe(me=>me.request.requestHeader),qe=oe(me=>me.request.salesOrgDTData),F=tn(),Ke=new URLSearchParams(F.search),Us=Ke.get("reqBench"),Wt=Ke.get("RequestId"),{t:$s}=bn(),{getRequestHeaderTemplate:Fs}=Qh(),{getChangeTemplate:ss}=fT(),{fetchOrgData:io}=Da(),{getDtCall:Ws}=qh(),{customError:ks}=sn(),fs=[{code:"Create",desc:"Create New Material in Application"},{code:"Change",desc:"Modify Existing Material in Application"},{code:"Extend",desc:"Extend Existing Material in Application"},{code:"Create with Upload",desc:"Create New Material with Excel Upload"},{code:"Change with Upload",desc:"Modify Existing Material with Excel Upload"},{code:"Extend with Upload",desc:"Extend Existing Material with Excel Upload"}].filter(me=>ce==null?void 0:ce.includes(me.code)),Ht=[{code:"Oncology",desc:""},{code:"Anesthesia/Pain Management",desc:""},{code:"Cardiovascular",desc:""}],ns=[{code:(to=Je)==null?void 0:to.LOGISTIC,desc:""},{code:(St=Je)==null?void 0:St.MRP,desc:""},{code:(es=Je)==null?void 0:es.WARE_VIEW_2,desc:""},{code:(ot=Je)==null?void 0:ot.ITEM_CAT,desc:""},{code:(Dt=Je)==null?void 0:Dt.SET_DNU,desc:""},{code:(Rs=Je)==null?void 0:Rs.UPD_DESC,desc:""},{code:(ae=Je)==null?void 0:ae.CHG_STAT,desc:""}],Zt=[{code:"High",desc:""},{code:"Medium",desc:""},{code:"Low",desc:""}];N(xo({keyName:($e=hp)==null?void 0:$e.REQUEST_TYPE,data:fs})),N(xo({keyName:"LeadingCat",data:Ht})),N(xo({keyName:"RequestPriority",data:Zt})),N(xo({keyName:"TemplateName",data:ns})),!Wt&&!Us&&(N(Xu({keyName:"ReqCreatedBy",data:Se==null?void 0:Se.user_id})),N(Xu({keyName:"RequestStatus",data:"DRAFT"})));const xs="Basic Data",[lt,He]=d.useState([xs]),[Rt,Ys]=d.useState(""),[Bt,Me]=d.useState(""),[it,Ut]=d.useState(!0);d.useEffect(()=>{N(fp(lt))},[N,lt]);const Ns=()=>{var Ne,Ve;let me=!0;return M&&((Ne=ge[Object.keys(ge)])!=null&&Ne.length)?(Ve=ge[Object.keys(ge)[0]])==null||Ve.forEach(Oe=>{var ct;!M[Oe.jsonName]&&Oe.visibility===((ct=$n)==null?void 0:ct.MANDATORY)&&(me=!1)}):me=!1,me};d.useEffect(()=>{M!=null&&M.MatlType&&ds(M),Ns()},[M]);const ds=me=>{var Oe;const Ne=ct=>{Ys(ct.body[0].MaintStatus.split("")),Me(ct.body[0].MaterialType)},Ve=ct=>{console.log(ct)};Xe(`/${Re}/data/getViewForMaterialType?materialType=${(Oe=me==null?void 0:me.MatlType)==null?void 0:Oe.code}`,"get",Ne,Ve)},rs=()=>{Q(!0)},at=()=>{Q(!1)},yt=()=>{var me;c(!1),p(!1),v("systemGenerated"),Wt||D((me=Eo)==null?void 0:me.REQUEST_BENCH)},ws=me=>{var Ne;v((Ne=me==null?void 0:me.target)==null?void 0:Ne.value)},Hs=()=>{T==="systemGenerated"&&(os(),yt()),T==="mailGenerated"&&(ls(),yt())},os=()=>{S("Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience."),A(!0);let me={region:I==null?void 0:I.Region,scenario:I==null?void 0:I.RequestType,matlType:"ALL",dtName:"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",version:"v1",rolePrefix:"",requestId:$!=null&&$.requestId?$==null?void 0:$.requestId:I!=null&&I.RequestId?I==null?void 0:I.RequestId:""};const Ne=ct=>{if((ct==null?void 0:ct.size)==0){A(!1),S(""),ie(!0),J("No data found for the selected criteria."),g("danger"),rs();return}const ke=URL.createObjectURL(ct),Vt=document.createElement("a");Vt.href=ke,Vt.setAttribute("download",`${(I==null?void 0:I.RequestType)===b.EXTEND_WITH_UPLOAD?"Mass_Extend.xlsx":"Mass_Create.xlsx"}`),document.body.appendChild(Vt),Vt.click(),document.body.removeChild(Vt),URL.revokeObjectURL(ke),A(!1),S(""),ie(!0),J(`${I!=null&&I.TemplateName?`${I.TemplateName}_Mass Change`:(I==null?void 0:I.RequestType)===b.EXTEND_WITH_UPLOAD?"Mass_Extend":"Mass_Create"}.xlsx has been downloaded successfully.`),g("success"),rs(),setTimeout(()=>{D("/requestBench")},2600)},Ve=()=>{A(!1)},Oe=`/${Re}${(I==null?void 0:I.RequestType)===b.EXTEND_WITH_UPLOAD?Fe.EXCEL.DOWNLOAD_EXCEL_FOR_EXTEND:Fe.EXCEL.DOWNLOAD_EXCEL}`;Xe(Oe,"postandgetblob",Ne,Ve,me)},ls=()=>{A(!0);let me={region:I==null?void 0:I.Region,scenario:I==null?void 0:I.RequestType,matlType:"ALL",dtName:"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",version:"v1",rolePrefix:"",requestId:$!=null&&$.requestId?$==null?void 0:$.requestId:I!=null&&I.RequestId?I==null?void 0:I.RequestId:""};const Ne=()=>{var ct;A(!1),S(""),ie(!0),J((ct=Ep)==null?void 0:ct.DOWNLOAD_MAIL_INITIATED),g("success"),rs(),setTimeout(()=>{var ke;D((ke=Eo)==null?void 0:ke.REQUEST_BENCH)},2600)},Ve=()=>{var ct;A(!1),ie(!0),J((ct=dn)==null?void 0:ct.ERR_DOWNLOADING_EXCEL),g("danger"),rs(),setTimeout(()=>{var ke;D((ke=Eo)==null?void 0:ke.REQUEST_BENCH)},2600)},Oe=`/${Re}${(I==null?void 0:I.RequestType)===b.EXTEND_WITH_UPLOAD?Fe.EXCEL.DOWNLOAD_EXCEL_FOR_EXTEND_MAIL:Fe.EXCEL.DOWNLOAD_EXCEL_MAIL}`;Xe(Oe,"post",Ne,Ve,me)},ao=()=>u(!1),Ye=me=>{if(H.includes("Distribution Channel")){const Ne=Oe=>k(Oe==null?void 0:Oe.body),Ve=Oe=>console.error(Oe);Xe(`/${Re}/data/getDistrChan?salesOrg=${me.code}`,"get",Ne,Ve)}},Mt={orgData:["Plant","Sales Organization","Distribution Channel"].map(me=>({info:f[me]||{code:"",desc:""},desc:me})),selectedViews:{selectedSections:lt}},gs=(me,Ne)=>{z(Ve=>({...Ve,[me]:Ne})),me==="Sales Organization"&&Ye(Ne)},Lt=`/Date(${Date.now()})/`,ps=()=>{var ke;let me=gp(M==null?void 0:M.Region,Ue);N(pp({...Se,role:me})),x(!1);const Ne=new Date(M==null?void 0:M.ReqCreatedOn).getTime(),Ve={RequestId:$!=null&&$.requestId?$==null?void 0:$.requestId:"",Region:(M==null?void 0:M.Region)||"",MatlType:(M==null?void 0:M.MatlType)||"",ReqCreatedBy:(Se==null?void 0:Se.user_id)||"",ReqCreatedOn:Ne?`/Date(${Ne})/`:Lt,ReqUpdatedOn:Ne?`/Date(${Ne})/`:Lt,RequestType:(M==null?void 0:M.RequestType)||"",RequestDesc:(M==null?void 0:M.RequestDesc)||"",Division:(M==null?void 0:M.Division)||"",RequestStatus:"DRAFT",RequestPriority:(M==null?void 0:M.RequestPriority)||"",LeadingCat:(M==null?void 0:M.LeadingCat)||"",FieldName:((ke=M==null?void 0:M.FieldName)==null?void 0:ke.join("$^$"))||"",TemplateName:(M==null?void 0:M.TemplateName)||""},Oe=Vt=>{var Es,Pt,je;if(ie(!0),J(`Request Header Created Successfully with request ID ${ua(M==null?void 0:M.RequestType,(Es=Vt==null?void 0:Vt.body)==null?void 0:Es.requestId)}`),g("success"),rs(),N(Uh(Vt.body)),t(!0),Ut(!1),N(kr({})),N(mp({})),(I==null?void 0:I.RequestType)===b.CREATE_WITH_UPLOAD||(I==null?void 0:I.RequestType)===b.EXTEND_WITH_UPLOAD){p(!0);return}if((I==null?void 0:I.RequestType)===((Pt=b)==null?void 0:Pt.CHANGE_WITH_UPLOAD)){x(!0);return}if((I==null?void 0:I.RequestType)===((je=b)==null?void 0:je.CHANGE)){const Pe=Cp(ee==null?void 0:ee["Config Data"],I==null?void 0:I.FieldName,["Material","Plant","Sales Org","Distribution Channel","Warehouse","MRP Controller"]);N(kh({...ee,"Config Data":Pe}));const _t=Ap(ee==null?void 0:ee[I==null?void 0:I.TemplateName],I==null?void 0:I.FieldName);N(bp([..._t]))}setTimeout(()=>{N(yr(1)),e(!0)},2500)},ct=()=>{ie(!0),g("error"),J("Error occured while saving Request Header"),rs()};Xe(`/${Re}/alter/createRequestHeader`,"post",Oe,ct,Ve)};d.useEffect(()=>{var me;if(o){if((I==null?void 0:I.RequestType)===b.CREATE_WITH_UPLOAD||(I==null?void 0:I.RequestType)===b.EXTEND_WITH_UPLOAD){p(!0);return}if((I==null?void 0:I.RequestType)===((me=b)==null?void 0:me.CHANGE_WITH_UPLOAD)){x(!0);return}}},[o]);function Js(me){return me.every(Ne=>Ne.info.code&&Ne.info.desc)}const Bs=()=>{if(!Js(Mt.orgData))ie(!0),g("error"),J("Please choose all mandatory fields"),rs();else{const Ne={label:"Attachments & Comments",value:"attachments&comments"},Oe=[{label:"General Information",value:"generalInformation"},...lt,Ne];Mt.selectedViews=Oe,N(Tp(Mt)),N(yr(1)),e(!0)}};d.useEffect(()=>{Fs()},[I==null?void 0:I.RequestType]);const st=(me="")=>{var ct,ke,Vt,Es;const Ne={materialNo:me??"",top:500,skip:0,salesOrg:((ke=(ct=qe==null?void 0:qe.uniqueSalesOrgList)==null?void 0:ct.map(Pt=>Pt.code))==null?void 0:ke.join("$^$"))||""},Ve=Pt=>{(Pt==null?void 0:Pt.statusCode)===Jt.STATUS_200&&(N(xo({keyName:Ci.RETURN_MAT_NUMBER,data:Pt==null?void 0:Pt.body})),N(xo({keyName:Ci.PARENT_MAT_NUMBER,data:Pt==null?void 0:Pt.body})))},Oe=Pt=>{ks(Pt)};Xe(`/${Re}${(Es=(Vt=Fe)==null?void 0:Vt.DATA)==null?void 0:Es.GET_SEARCH_PARAMS_MATERIAL_NO}`,"post",Ve,Oe,Ne)};d.useEffect(()=>{qe!=null&&qe.uniqueSalesOrgList&&st()},[]);const Ts=me=>{let Ne={decisionTableId:null,decisionTableName:Ei.MDG_MAT_REGION_DIVISION_MAPPING,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":me||""}]};Ws(Ne)};return d.useEffect(()=>{I!=null&&I.Region&&(io(),Ts(I==null?void 0:I.Region))},[I==null?void 0:I.Region]),d.useEffect(()=>{I!=null&&I.TemplateName&&(((I==null?void 0:I.TemplateName)===Je.MRP||(I==null?void 0:I.TemplateName)===Je.WARE_VIEW_2)&&N(kl({keyName:"FieldName",data:void 0})),ss())},[I==null?void 0:I.TemplateName]),n("div",{children:R(bo,{spacing:2,children:[Object.entries(ge).map(([me,Ne])=>R(vs,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Oa},children:[n(mt,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:$s(me)}),n(ye,{children:n(vs,{container:!0,spacing:1,children:Ne.filter(Ve=>Ve.visibility!=="Hidden").sort((Ve,Oe)=>Ve.sequenceNo-Oe.sequenceNo).map(Ve=>n(uT,{isHeader:!0,field:Ve,dropDownData:s,disabled:Wt||($==null?void 0:$.requestId),requestHeader:!0},Ve.id))})}),!Wt&&!($!=null&&$.requestId)&&n(ye,{sx:{display:"flex",justifyContent:"flex-end",marginTop:"20px"},children:n(It,{variant:"contained",color:"primary",disabled:!Ns(),onClick:ps,children:$s("Save Request Header")})}),n(Hi,{})]},me)),R(cn,{open:l,onClose:ao,children:[n(Pn,{sx:{backgroundColor:"#EAE9FF"},children:"Select Org Data"}),n(Lo,{children:n(vs,{container:!0,columnSpacing:1,children:H.map((me,Ne)=>R(d.Fragment,{children:[n(vs,{item:!0,md:4,children:R(mt,{children:[me,n("span",{style:{color:"red"},children:"*"})]})}),n(vs,{item:!0,md:8,children:n(Pl,{options:me==="Distribution Channel"?O:s[me]||[],size:"small",getOptionLabel:Ve=>`${Ve.code} - ${Ve.desc}`,renderOption:(Ve,Oe)=>n("li",{...Ve,children:n(mt,{children:`${Oe.code} - ${Oe.desc}`})}),onChange:(Ve,Oe)=>gs(me,Oe),renderInput:Ve=>n(xn,{...Ve,placeholder:`Select ${me}`})})})]},Ne))})}),R(Do,{children:[n(It,{onClick:ao,variant:"outlined",children:$s("Cancel")}),n(It,{variant:"contained",onClick:()=>{Bs()},children:$s("Proceed")})]})]}),U&&n(nf,{downloadClicked:o,setDownloadClicked:c}),n(ef,{onDownloadTypeChange:Hs,open:j,downloadType:T,handleDownloadTypeChange:ws,onClose:yt}),n(Cn,{blurLoading:L,loaderMessage:C}),se&&n(Un,{openSnackBar:V,alertMsg:ne,alertType:q,handleSnackBarClose:at})]})})};var Ba={},uE=Go;Object.defineProperty(Ba,"__esModule",{value:!0});var rf=Ba.default=void 0,hE=uE(Bo()),fE=$o;rf=Ba.default=(0,hE.default)((0,fE.jsx)("path",{d:"M22 5.18 10.59 16.6l-4.24-4.24 1.41-1.41 2.83 2.83 10-10zm-2.21 5.04c.13.57.21 1.17.21 1.78 0 4.42-3.58 8-8 8s-8-3.58-8-8 3.58-8 8-8c1.58 0 3.04.46 4.28 1.25l1.44-1.44C16.1 2.67 14.13 2 12 2 6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10c0-1.19-.22-2.33-.6-3.39z"}),"TaskAlt");const Ol={plant:[P.ACCOUNTING,P.COSTING,P.MRP,P.SALES,P.PURCHASING,P.FORCASTING,P.WAREHOUSE_MANAGEMENT,P.WORK_SCHEDULING,P.STORAGE_LOCATION_STOCKS,P.PRODUCTION,P.PLANT_STOCKS],salesOrg:[P.SALES],distributionChannel:[P.SALES],storageLocation:[P.MRP,P.STORAGE,P.STORAGE_LOCATION_STOCKS],mrpProfile:[P.MRP],warehouse:[P.WAREHOUSE],storage:[P.STORAGE]},gE=(e,t,r,o,c)=>({checkValidation:(m,l,u,se,ie)=>{var J,f,z,j,p,T,v,C,S,L;const q=(J=e==null?void 0:e[m])==null?void 0:J.payloadData,g=(f=e==null?void 0:e[m])==null?void 0:f.headerData;(z=e==null?void 0:e[m])==null||z.ManufacturerID;const V=(j=e==null?void 0:e.payloadData)==null?void 0:j.Region;if(!(g!=null&&g.materialNumber))return{missingFields:["Material number"],isValid:!1};if(!(g!=null&&g.globalMaterialDescription)||!q)return{missingFields:["Material Description"],isValid:!1};const Q=Sp(q[P.BASIC_DATA]);Q.Material=g==null?void 0:g.materialNumber,Q.MatlDesc=g==null?void 0:g.globalMaterialDescription;const H=t==null?void 0:t.find(A=>{var O;return(A==null?void 0:A[V])&&(A==null?void 0:A[V][(O=g==null?void 0:g.materialType)==null?void 0:O.code])}),he=H&&H[V]&&((T=H[V][(p=g==null?void 0:g.materialType)==null?void 0:p.code])==null?void 0:T.mandatoryFields),ne=he==null?void 0:he[P.BASIC_DATA];if((ne==null?void 0:ne.length)>0){for(const A of ne)if(!Q[A==null?void 0:A.jsonName])return{missingFields:Np(ne,Q),viewType:P.BASIC_DATA,isValid:!1,plant:[P.BASIC_DATA]}}if(r.includes(P.PURCHASING)){const A=he==null?void 0:he[P.PURCHASING];if(A)if(q[P.PURCHASING]){const{validCount:O}=Qi(l,P.PURCHASING),{totalCount:k,allValid:U}=Zi(q[P.PURCHASING],A);if(k===O)if(U){if(!ea(q[P.PURCHASING],A)){const x=cr(l),N=ai(x,q[P.PURCHASING],A);return{missingFields:En(A,q[P.PURCHASING]),viewType:P.PURCHASING,isValid:!1,plant:N==null?void 0:N.missingFields}}}else{const x=cr(l),N=Vu(x,q[P.PURCHASING]);return{missingFields:En(A,q[P.PURCHASING]),viewType:P.PURCHASING,isValid:!1,plant:N}}else{const x=cr(l),N=ai(x,q[P.PURCHASING],A);return{missingFields:En(A,q[P.PURCHASING]),viewType:P.PURCHASING,isValid:!1,plant:N==null?void 0:N.missingFields}}}else{const O=cr(l);return{missingFields:En(A,q[P.PURCHASING]),viewType:P.PURCHASING,isValid:!1,plant:O}}}if(r.includes(P.MRP)){const A=he==null?void 0:he[P.MRP];if(A){const O=wp(l);if(q[P.MRP]){const{validCount:k}=Qi(l,P.MRP),{totalCount:U,allValid:x}=Zi(q[P.MRP],A);if(U===k)if(x){if(!ea(q[P.MRP],A)){const N=cr(l),D=ai(N,q[P.MRP],A),I=En(A,q[P.MRP]),ge=ta(D==null?void 0:D.missingFields,(S=O==null?void 0:O[P.MRP])==null?void 0:S.displayCombinations);return{missingFields:I,viewType:P.MRP,isValid:!1,plant:ge}}}else{const N=cr(l),D=Vu(N,q[P.MRP]),I=En(A,q[P.MRP]),ge=ta(D,(C=O==null?void 0:O[P.MRP])==null?void 0:C.displayCombinations);return{missingFields:I,viewType:P.MRP,isValid:!1,plant:ge}}else{const N=cr(l),D=ai(N,q[P.MRP],A),I=En(A,q[P.MRP]),ge=ta(D==null?void 0:D.missingFields,(L=O==null?void 0:O[P.MRP])==null?void 0:L.displayCombinations);return{missingFields:I,viewType:P.MRP,isValid:!1,plant:ge}}}else return{missingFields:En(A,q[P.MRP]),viewType:P.MRP,isValid:!1,plant:(v=O==null?void 0:O[P.MRP])==null?void 0:v.displayCombinations}}}if(r.includes(P.SALES)){const A=he==null?void 0:he[P.SALES];if(A)if(q[P.SALES]){const{validCount:O}=Qi(l,P.SALES),{totalCount:k,allValid:U}=Zi(q[P.SALES],A);if(k===O)if(U){if(!ea(q[P.SALES],A))return{missingFields:En(A,q[P.SALES]),viewType:P.SALES,isValid:!1}}else return{missingFields:En(A,q[P.SALES]),viewType:P.SALES,isValid:!1};else return{missingFields:En(A,q[P.SALES]),viewType:P.SALES,isValid:!1}}else return{missingFields:En(A,q[P.SALES]),viewType:P.SALES,isValid:!1}}return{missingFields:null,isValid:!0}}}),lf=()=>{const e=Ro(),{fetchDataAndDispatch:t}=Bi(),r=oe(c=>c.payload.valuationClassData||{});return{fetchValuationClassData:c=>{if(!c)return;c in r?e(xo({keyName:Ci.VAL_CLASS,data:r[c]})):t(`/${Re}${Fe.DATA.GET_VALUATION_CLASS}?matlType=${c}`,Ci.VAL_CLASS)}}};var Ga={},pE=Go;Object.defineProperty(Ga,"__esModule",{value:!0});var bi=Ga.default=void 0,TE=pE(Bo()),EE=$o;bi=Ga.default=(0,TE.default)((0,EE.jsx)("path",{d:"M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12zm-1 4 6 6v10c0 1.1-.9 2-2 2H7.99C6.89 23 6 22.1 6 21l.01-14c0-1.1.89-2 1.99-2zm-1 7h5.5L14 6.5z"}),"FileCopy");const af=({open:e,onClose:t,title:r,lengthOfOrgRow:o,selectedMaterialPayload:c,materialID:s,orgRows:m})=>{var g,V;const[l,u]=d.useState({}),se=Ro(),ie=()=>{const Q=[];return m&&m.length>0&&(m==null||m.forEach((H,he)=>{var ne,J,f,z,j,p,T,v,C,S,L,A,O,k;if(he!==(o==null?void 0:o.copyFor)){const U=(J=(ne=H.plant)==null?void 0:ne.value)==null?void 0:J.code,x=((z=(f=H.plant)==null?void 0:f.value)==null?void 0:z.desc)||U,N=(j=H.salesOrg)==null?void 0:j.code,D=((p=H.salesOrg)==null?void 0:p.desc)||N,I=(v=(T=H.dc)==null?void 0:T.value)==null?void 0:v.code,ge=((S=(C=H.dc)==null?void 0:C.value)==null?void 0:S.desc)||I,ee=(A=(L=H.warehouse)==null?void 0:L.value)==null?void 0:A.code,Ue=((k=(O=H.warehouse)==null?void 0:O.value)==null?void 0:k.desc)||ee;if(U){let M=`Plant: ${x||"N/A"}`;N&&(M+=` | SalesOrg: ${D||"N/A"}`),I&&(M+=` | DC: ${ge||"N/A"}`),ee&&(M+=` | Warehouse: ${Ue||"N/A"}`);let Se=U;N&&(Se+=`-${N}`),I&&(Se+=`-${I}`),ee&&(Se+=`-${ee}`),Q==null||Q.push({code:Se,desc:M,index:he,plant:U,salesOrg:N,dc:I,warehouse:ee})}}})),Q},q=()=>{var z,j,p,T,v,C,S,L;if(!l.code)return;const Q=m[o.copyFor],H=(j=(z=Q==null?void 0:Q.plant)==null?void 0:z.value)==null?void 0:j.code,he=(p=Q==null?void 0:Q.salesOrg)==null?void 0:p.code,ne=(v=(T=Q==null?void 0:Q.dc)==null?void 0:T.value)==null?void 0:v.code,J=(S=(C=Q==null?void 0:Q.warehouse)==null?void 0:C.value)==null?void 0:S.code;if(!H)return;const f=JSON.parse(JSON.stringify(c));(L=Object.keys(f))==null||L.forEach(A=>{const O=f[A];if(!(A===P.BASIC_DATA||A===P.SALES_GENERAL||A===P.PURCHASING_GENERAL||A===P.TAX_DATA)&&typeof O=="object"){const k=Object.keys(O);if(A===P.WAREHOUSE){const U=k==null?void 0:k.find(N=>N.includes(l.warehouse)),x=k==null?void 0:k.find(N=>N.includes(J));if(U&&x&&x!==U){const N=JSON.parse(JSON.stringify(O[U]));delete N.WarehouseId,f[A][x]={...JSON.parse(JSON.stringify(f[A][x]||{})),...N}}}else if(A===P.SALES){const U=`${l.salesOrg}-${l.dc}`,x=`${he}-${ne}`,N=k==null?void 0:k.find(I=>I===U),D=k==null?void 0:k.find(I=>I===x);if(N&&D&&D!==N){const I=JSON.parse(JSON.stringify(O[N]));delete I.SalesId,f[A][D]={...JSON.parse(JSON.stringify(f[A][D]||{})),...I}}}else{const U=k==null?void 0:k.find(N=>N.includes(l.plant)),x=k==null?void 0:k.find(N=>N.includes(H));if(U&&x&&x!==U){const N=JSON.parse(JSON.stringify(O[U]));N&&(delete N.SalesId,delete N.PlantId,delete N.StorageLocationId,delete N.AccountingId,x&&(f[A][x]={...JSON.parse(JSON.stringify(f[A][x]||{})),...N}))}}}}),se(Rp({materialID:s,data:f})),t()};return R(Pi,{isOpen:e,titleIcon:n(Hr,{size:"small",sx:{color:(V=(g=Be)==null?void 0:g.primary)==null?void 0:V.dark,fontSize:"20px"}}),Title:r,handleClose:()=>t(),children:[R(Lo,{sx:{mt:2},children:[n(mt,{sx:{mb:2},children:xa.COPY_ORG_DATA_VALUES}),n(lo,{options:ie(),placeholder:"SELECT SOURCE ORGANIZATION",onChange:Q=>u(Q),value:l})]}),n(Do,{children:n(It,{variant:"contained",size:"small",onClick:()=>q(),children:"Ok"})})]})},cf=()=>{const{fetchDataAndDispatch:e}=Bi();return{fetchTabSpecificData:(r,o)=>{if(o===P.SALES&&r&&r.includes("-")){const[c,s]=r.split("-");c&&e(`/${Re}${Fe.DATA.GET_DELIVARING_PLANT_BASED_ON_SALES_ORG_AND_DISTCHNL}`,"DelygPlnt","post",{salesOrg:c,distChnl:s},!0)}else if(o===P.PLANT&&r){const c=r;e(`/${Re}${Fe.DATA.GET_SPPROC_TYPE}`,"Spproctype","post",{plant:c},!0),e(`/${Re}${Fe.DATA.GET_MRP_CONTROLLER_BASED_ON_PLANT}`,"MrpCtrler","post",{plant:c},!0),e(`/${Re}${Fe.DATA.GET_PROD_STORAGE_LOCATION_BASED_ON_PLANT}`,"IssStLoc","post",{plant:c},!0),e(`/${Re}${Fe.DATA.GET_PROCUREMENT_STORAGE_LOCATION_BASED_ON_PLANT}`,"SlocExprc","post",{plant:c},!0),e(`/${Re}${Fe.DATA.GET_SCHEDULING_MARGIN_KEY_BASED_ON_PLANT}`,"SmKey","post",{plant:c},!0),e(`/${Re}${Fe.DATA.GET_PROFIT_CENTER_BASED_ON_PLANT}`,"ProfitCtr","post",{plant:c},!0),e(`/${Re}${Fe.DATA.GET_PRODUCTION_SCHEDULING_PROFILE_BASED_ON_PLANT}`,"ProdProf","post",{plant:c},!0)}else o===P.WAREHOUSE&&r&&e(`/${Re}${Fe.DATA.GET_PLACEMENT}?wareHouseNo=${r}`,"Placement","get",{plant:r},!0)}}},df=({doAjax:e,customError:t,fetchDataAndDispatch:r,destination_MaterialMgmt:o})=>({getContryBasedOnPlant:s=>{const m=u=>{var se;if((u==null?void 0:u.statusCode)===Jt.STATUS_200){const ie=(se=u==null?void 0:u.body[0])==null?void 0:se.code;ie&&(r(`/${o}${Fe.DATA.GET_COMMODITY_CODE_BASED_ON_COUNTRY}?country=${ie}`,"CommCode","get",{plant:s},!0),r(`/${o}${Fe.DATA.GET_HTS_CODE}?country=${ie}`,"HtsCode","get",{plant:s},!0))}},l=u=>{t(u)};e(`/${o}${Fe.DATA.GET_COUNTRY_BASED_ON_PLANT}`,"post",m,l,{plant:s})}});var $a={},mE=Go;Object.defineProperty($a,"__esModule",{value:!0});var Ta=$a.default=void 0,CE=mE(Bo()),AE=$o;Ta=$a.default=(0,CE.default)((0,AE.jsx)("path",{d:"M3 5v4h2V5h4V3H5c-1.1 0-2 .9-2 2m2 10H3v4c0 1.1.9 2 2 2h4v-2H5zm14 4h-4v2h4c1.1 0 2-.9 2-2v-4h-2zm0-16h-4v2h4v4h2V5c0-1.1-.9-2-2-2"}),"CropFree");var Fa={},bE=Go;Object.defineProperty(Fa,"__esModule",{value:!0});var Ea=Fa.default=void 0,SE=bE(Bo()),NE=$o;Ea=Fa.default=(0,SE.default)((0,NE.jsx)("path",{d:"M22 3.41 16.71 8.7 20 12h-8V4l3.29 3.29L20.59 2zM3.41 22l5.29-5.29L12 20v-8H4l3.29 3.29L2 20.59z"}),"CloseFullscreen");/*!
* sweetalert2 v11.22.2
* Released under the MIT License.
*/function uf(e,t,r){if(typeof e=="function"?e===t:e.has(t))return arguments.length<3?t:r;throw new TypeError("Private element is not present on this object")}function wE(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function uh(e,t){return e.get(uf(e,t))}function RE(e,t,r){wE(e,t),t.set(e,r)}function _E(e,t,r){return e.set(uf(e,t),r),r}const IE=100,vt={},vE=()=>{vt.previousActiveElement instanceof HTMLElement?(vt.previousActiveElement.focus(),vt.previousActiveElement=null):document.body&&document.body.focus()},ME=e=>new Promise(t=>{if(!e)return t();const r=window.scrollX,o=window.scrollY;vt.restoreFocusTimeout=setTimeout(()=>{vE(),t()},IE),window.scrollTo(r,o)}),hf="swal2-",OE=["container","shown","height-auto","iosfix","popup","modal","no-backdrop","no-transition","toast","toast-shown","show","hide","close","title","html-container","actions","confirm","deny","cancel","footer","icon","icon-content","image","input","file","range","select","radio","checkbox","label","textarea","inputerror","input-label","validation-message","progress-steps","active-progress-step","progress-step","progress-step-line","loader","loading","styled","top","top-start","top-end","top-left","top-right","center","center-start","center-end","center-left","center-right","bottom","bottom-start","bottom-end","bottom-left","bottom-right","grow-row","grow-column","grow-fullscreen","rtl","timer-progress-bar","timer-progress-bar-container","scrollbar-measure","icon-success","icon-warning","icon-info","icon-question","icon-error","draggable","dragging"],fe=OE.reduce((e,t)=>(e[t]=hf+t,e),{}),xE=["success","warning","info","question","error"],Si=xE.reduce((e,t)=>(e[t]=hf+t,e),{}),ff="SweetAlert2:",Wa=e=>e.charAt(0).toUpperCase()+e.slice(1),en=e=>{console.warn(`${ff} ${typeof e=="object"?e.join(" "):e}`)},Rr=e=>{console.error(`${ff} ${e}`)},hh=[],yE=e=>{hh.includes(e)||(hh.push(e),en(e))},gf=(e,t=null)=>{yE(`"${e}" is deprecated and will be removed in the next major release.${t?` Use "${t}" instead.`:""}`)},Fi=e=>typeof e=="function"?e():e,ja=e=>e&&typeof e.toPromise=="function",$l=e=>ja(e)?e.toPromise():Promise.resolve(e),za=e=>e&&Promise.resolve(e)===e,on=()=>document.body.querySelector(`.${fe.container}`),Fl=e=>{const t=on();return t?t.querySelector(e):null},hn=e=>Fl(`.${e}`),Os=()=>hn(fe.popup),Fr=()=>hn(fe.icon),LE=()=>hn(fe["icon-content"]),pf=()=>hn(fe.title),Ya=()=>hn(fe["html-container"]),Tf=()=>hn(fe.image),Xa=()=>hn(fe["progress-steps"]),Wi=()=>hn(fe["validation-message"]),qn=()=>Fl(`.${fe.actions} .${fe.confirm}`),Wr=()=>Fl(`.${fe.actions} .${fe.cancel}`),_r=()=>Fl(`.${fe.actions} .${fe.deny}`),DE=()=>hn(fe["input-label"]),jr=()=>Fl(`.${fe.loader}`),Wl=()=>hn(fe.actions),Ef=()=>hn(fe.footer),ji=()=>hn(fe["timer-progress-bar"]),Va=()=>hn(fe.close),PE=`
  a[href],
  area[href],
  input:not([disabled]),
  select:not([disabled]),
  textarea:not([disabled]),
  button:not([disabled]),
  iframe,
  object,
  embed,
  [tabindex="0"],
  [contenteditable],
  audio[controls],
  video[controls],
  summary
`,Ka=()=>{const e=Os();if(!e)return[];const t=e.querySelectorAll('[tabindex]:not([tabindex="-1"]):not([tabindex="0"])'),r=Array.from(t).sort((s,m)=>{const l=parseInt(s.getAttribute("tabindex")||"0"),u=parseInt(m.getAttribute("tabindex")||"0");return l>u?1:l<u?-1:0}),o=e.querySelectorAll(PE),c=Array.from(o).filter(s=>s.getAttribute("tabindex")!=="-1");return[...new Set(r.concat(c))].filter(s=>rn(s))},Ja=()=>Wn(document.body,fe.shown)&&!Wn(document.body,fe["toast-shown"])&&!Wn(document.body,fe["no-backdrop"]),zi=()=>{const e=Os();return e?Wn(e,fe.toast):!1},qE=()=>{const e=Os();return e?e.hasAttribute("data-loading"):!1},fn=(e,t)=>{if(e.textContent="",t){const o=new DOMParser().parseFromString(t,"text/html"),c=o.querySelector("head");c&&Array.from(c.childNodes).forEach(m=>{e.appendChild(m)});const s=o.querySelector("body");s&&Array.from(s.childNodes).forEach(m=>{m instanceof HTMLVideoElement||m instanceof HTMLAudioElement?e.appendChild(m.cloneNode(!0)):e.appendChild(m)})}},Wn=(e,t)=>{if(!t)return!1;const r=t.split(/\s+/);for(let o=0;o<r.length;o++)if(!e.classList.contains(r[o]))return!1;return!0},UE=(e,t)=>{Array.from(e.classList).forEach(r=>{!Object.values(fe).includes(r)&&!Object.values(Si).includes(r)&&!Object.values(t.showClass||{}).includes(r)&&e.classList.remove(r)})},un=(e,t,r)=>{if(UE(e,t),!t.customClass)return;const o=t.customClass[r];if(o){if(typeof o!="string"&&!o.forEach){en(`Invalid type of customClass.${r}! Expected string or iterable object, got "${typeof o}"`);return}Ps(e,o)}},Yi=(e,t)=>{if(!t)return null;switch(t){case"select":case"textarea":case"file":return e.querySelector(`.${fe.popup} > .${fe[t]}`);case"checkbox":return e.querySelector(`.${fe.popup} > .${fe.checkbox} input`);case"radio":return e.querySelector(`.${fe.popup} > .${fe.radio} input:checked`)||e.querySelector(`.${fe.popup} > .${fe.radio} input:first-child`);case"range":return e.querySelector(`.${fe.popup} > .${fe.range} input`);default:return e.querySelector(`.${fe.popup} > .${fe.input}`)}},mf=e=>{if(e.focus(),e.type!=="file"){const t=e.value;e.value="",e.value=t}},Cf=(e,t,r)=>{!e||!t||(typeof t=="string"&&(t=t.split(/\s+/).filter(Boolean)),t.forEach(o=>{Array.isArray(e)?e.forEach(c=>{r?c.classList.add(o):c.classList.remove(o)}):r?e.classList.add(o):e.classList.remove(o)}))},Ps=(e,t)=>{Cf(e,t,!0)},An=(e,t)=>{Cf(e,t,!1)},or=(e,t)=>{const r=Array.from(e.children);for(let o=0;o<r.length;o++){const c=r[o];if(c instanceof HTMLElement&&Wn(c,t))return c}},br=(e,t,r)=>{r===`${parseInt(r)}`&&(r=parseInt(r)),r||parseInt(r)===0?e.style.setProperty(t,typeof r=="number"?`${r}px`:r):e.style.removeProperty(t)},wo=(e,t="flex")=>{e&&(e.style.display=t)},Ho=e=>{e&&(e.style.display="none")},Qa=(e,t="block")=>{e&&new MutationObserver(()=>{jl(e,e.innerHTML,t)}).observe(e,{childList:!0,subtree:!0})},fh=(e,t,r,o)=>{const c=e.querySelector(t);c&&c.style.setProperty(r,o)},jl=(e,t,r="flex")=>{t?wo(e,r):Ho(e)},rn=e=>!!(e&&(e.offsetWidth||e.offsetHeight||e.getClientRects().length)),kE=()=>!rn(qn())&&!rn(_r())&&!rn(Wr()),ma=e=>e.scrollHeight>e.clientHeight,HE=(e,t)=>{let r=e;for(;r&&r!==t;){if(ma(r))return!0;r=r.parentElement}return!1},Af=e=>{const t=window.getComputedStyle(e),r=parseFloat(t.getPropertyValue("animation-duration")||"0"),o=parseFloat(t.getPropertyValue("transition-duration")||"0");return r>0||o>0},Za=(e,t=!1)=>{const r=ji();r&&rn(r)&&(t&&(r.style.transition="none",r.style.width="100%"),setTimeout(()=>{r.style.transition=`width ${e/1e3}s linear`,r.style.width="0%"},10))},BE=()=>{const e=ji();if(!e)return;const t=parseInt(window.getComputedStyle(e).width);e.style.removeProperty("transition"),e.style.width="100%";const r=parseInt(window.getComputedStyle(e).width),o=t/r*100;e.style.width=`${o}%`},GE=()=>typeof window>"u"||typeof document>"u",$E=`
 <div aria-labelledby="${fe.title}" aria-describedby="${fe["html-container"]}" class="${fe.popup}" tabindex="-1">
   <button type="button" class="${fe.close}"></button>
   <ul class="${fe["progress-steps"]}"></ul>
   <div class="${fe.icon}"></div>
   <img class="${fe.image}" />
   <h2 class="${fe.title}" id="${fe.title}"></h2>
   <div class="${fe["html-container"]}" id="${fe["html-container"]}"></div>
   <input class="${fe.input}" id="${fe.input}" />
   <input type="file" class="${fe.file}" />
   <div class="${fe.range}">
     <input type="range" />
     <output></output>
   </div>
   <select class="${fe.select}" id="${fe.select}"></select>
   <div class="${fe.radio}"></div>
   <label class="${fe.checkbox}">
     <input type="checkbox" id="${fe.checkbox}" />
     <span class="${fe.label}"></span>
   </label>
   <textarea class="${fe.textarea}" id="${fe.textarea}"></textarea>
   <div class="${fe["validation-message"]}" id="${fe["validation-message"]}"></div>
   <div class="${fe.actions}">
     <div class="${fe.loader}"></div>
     <button type="button" class="${fe.confirm}"></button>
     <button type="button" class="${fe.deny}"></button>
     <button type="button" class="${fe.cancel}"></button>
   </div>
   <div class="${fe.footer}"></div>
   <div class="${fe["timer-progress-bar-container"]}">
     <div class="${fe["timer-progress-bar"]}"></div>
   </div>
 </div>
`.replace(/(^|\n)\s*/g,""),FE=()=>{const e=on();return e?(e.remove(),An([document.documentElement,document.body],[fe["no-backdrop"],fe["toast-shown"],fe["has-column"]]),!0):!1},dr=()=>{vt.currentInstance.resetValidationMessage()},WE=()=>{const e=Os(),t=or(e,fe.input),r=or(e,fe.file),o=e.querySelector(`.${fe.range} input`),c=e.querySelector(`.${fe.range} output`),s=or(e,fe.select),m=e.querySelector(`.${fe.checkbox} input`),l=or(e,fe.textarea);t.oninput=dr,r.onchange=dr,s.onchange=dr,m.onchange=dr,l.oninput=dr,o.oninput=()=>{dr(),c.value=o.value},o.onchange=()=>{dr(),c.value=o.value}},jE=e=>typeof e=="string"?document.querySelector(e):e,zE=e=>{const t=Os();t.setAttribute("role",e.toast?"alert":"dialog"),t.setAttribute("aria-live",e.toast?"polite":"assertive"),e.toast||t.setAttribute("aria-modal","true")},YE=e=>{window.getComputedStyle(e).direction==="rtl"&&Ps(on(),fe.rtl)},XE=e=>{const t=FE();if(GE()){Rr("SweetAlert2 requires document to initialize");return}const r=document.createElement("div");r.className=fe.container,t&&Ps(r,fe["no-transition"]),fn(r,$E),r.dataset.swal2Theme=e.theme;const o=jE(e.target);o.appendChild(r),e.topLayer&&(r.setAttribute("popover",""),r.showPopover()),zE(e),YE(o),WE()},ec=(e,t)=>{e instanceof HTMLElement?t.appendChild(e):typeof e=="object"?VE(e,t):e&&fn(t,e)},VE=(e,t)=>{e.jquery?KE(t,e):fn(t,e.toString())},KE=(e,t)=>{if(e.textContent="",0 in t)for(let r=0;r in t;r++)e.appendChild(t[r].cloneNode(!0));else e.appendChild(t.cloneNode(!0))},JE=(e,t)=>{const r=Wl(),o=jr();!r||!o||(!t.showConfirmButton&&!t.showDenyButton&&!t.showCancelButton?Ho(r):wo(r),un(r,t,"actions"),QE(r,o,t),fn(o,t.loaderHtml||""),un(o,t,"loader"))};function QE(e,t,r){const o=qn(),c=_r(),s=Wr();!o||!c||!s||(la(o,"confirm",r),la(c,"deny",r),la(s,"cancel",r),ZE(o,c,s,r),r.reverseButtons&&(r.toast?(e.insertBefore(s,o),e.insertBefore(c,o)):(e.insertBefore(s,t),e.insertBefore(c,t),e.insertBefore(o,t))))}function ZE(e,t,r,o){if(!o.buttonsStyling){An([e,t,r],fe.styled);return}Ps([e,t,r],fe.styled),o.confirmButtonColor&&e.style.setProperty("--swal2-confirm-button-background-color",o.confirmButtonColor),o.denyButtonColor&&t.style.setProperty("--swal2-deny-button-background-color",o.denyButtonColor),o.cancelButtonColor&&r.style.setProperty("--swal2-cancel-button-background-color",o.cancelButtonColor),ra(e),ra(t),ra(r)}function ra(e){const t=window.getComputedStyle(e);if(t.getPropertyValue("--swal2-action-button-focus-box-shadow"))return;const r=t.backgroundColor.replace(/rgba?\((\d+), (\d+), (\d+).*/,"rgba($1, $2, $3, 0.5)");e.style.setProperty("--swal2-action-button-focus-box-shadow",t.getPropertyValue("--swal2-outline").replace(/ rgba\(.*/,` ${r}`))}function la(e,t,r){const o=Wa(t);jl(e,r[`show${o}Button`],"inline-block"),fn(e,r[`${t}ButtonText`]||""),e.setAttribute("aria-label",r[`${t}ButtonAriaLabel`]||""),e.className=fe[t],un(e,r,`${t}Button`)}const em=(e,t)=>{const r=Va();r&&(fn(r,t.closeButtonHtml||""),un(r,t,"closeButton"),jl(r,t.showCloseButton),r.setAttribute("aria-label",t.closeButtonAriaLabel||""))},tm=(e,t)=>{const r=on();r&&(sm(r,t.backdrop),om(r,t.position),nm(r,t.grow),un(r,t,"container"))};function sm(e,t){typeof t=="string"?e.style.background=t:t||Ps([document.documentElement,document.body],fe["no-backdrop"])}function om(e,t){t&&(t in fe?Ps(e,fe[t]):(en('The "position" parameter is not valid, defaulting to "center"'),Ps(e,fe.center)))}function nm(e,t){t&&Ps(e,fe[`grow-${t}`])}var Ks={innerParams:new WeakMap,domCache:new WeakMap};const rm=["input","file","range","select","radio","checkbox","textarea"],lm=(e,t)=>{const r=Os();if(!r)return;const o=Ks.innerParams.get(e),c=!o||t.input!==o.input;rm.forEach(s=>{const m=or(r,fe[s]);m&&(cm(s,t.inputAttributes),m.className=fe[s],c&&Ho(m))}),t.input&&(c&&im(t),dm(t))},im=e=>{if(!e.input)return;if(!po[e.input]){Rr(`Unexpected type of input! Expected ${Object.keys(po).join(" | ")}, got "${e.input}"`);return}const t=bf(e.input);if(!t)return;const r=po[e.input](t,e);wo(t),e.inputAutoFocus&&setTimeout(()=>{mf(r)})},am=e=>{for(let t=0;t<e.attributes.length;t++){const r=e.attributes[t].name;["id","type","value","style"].includes(r)||e.removeAttribute(r)}},cm=(e,t)=>{const r=Os();if(!r)return;const o=Yi(r,e);if(o){am(o);for(const c in t)o.setAttribute(c,t[c])}},dm=e=>{if(!e.input)return;const t=bf(e.input);t&&un(t,e,"input")},tc=(e,t)=>{!e.placeholder&&t.inputPlaceholder&&(e.placeholder=t.inputPlaceholder)},zl=(e,t,r)=>{if(r.inputLabel){const o=document.createElement("label"),c=fe["input-label"];o.setAttribute("for",e.id),o.className=c,typeof r.customClass=="object"&&Ps(o,r.customClass.inputLabel),o.innerText=r.inputLabel,t.insertAdjacentElement("beforebegin",o)}},bf=e=>{const t=Os();if(t)return or(t,fe[e]||fe.input)},Ni=(e,t)=>{["string","number"].includes(typeof t)?e.value=`${t}`:za(t)||en(`Unexpected type of inputValue! Expected "string", "number" or "Promise", got "${typeof t}"`)},po={};po.text=po.email=po.password=po.number=po.tel=po.url=po.search=po.date=po["datetime-local"]=po.time=po.week=po.month=(e,t)=>(Ni(e,t.inputValue),zl(e,e,t),tc(e,t),e.type=t.input,e);po.file=(e,t)=>(zl(e,e,t),tc(e,t),e);po.range=(e,t)=>{const r=e.querySelector("input"),o=e.querySelector("output");return Ni(r,t.inputValue),r.type=t.input,Ni(o,t.inputValue),zl(r,e,t),e};po.select=(e,t)=>{if(e.textContent="",t.inputPlaceholder){const r=document.createElement("option");fn(r,t.inputPlaceholder),r.value="",r.disabled=!0,r.selected=!0,e.appendChild(r)}return zl(e,e,t),e};po.radio=e=>(e.textContent="",e);po.checkbox=(e,t)=>{const r=Yi(Os(),"checkbox");r.value="1",r.checked=!!t.inputValue;const o=e.querySelector("span");return fn(o,t.inputPlaceholder||t.inputLabel),r};po.textarea=(e,t)=>{Ni(e,t.inputValue),tc(e,t),zl(e,e,t);const r=o=>parseInt(window.getComputedStyle(o).marginLeft)+parseInt(window.getComputedStyle(o).marginRight);return setTimeout(()=>{if("MutationObserver"in window){const o=parseInt(window.getComputedStyle(Os()).width),c=()=>{if(!document.body.contains(e))return;const s=e.offsetWidth+r(e);s>o?Os().style.width=`${s}px`:br(Os(),"width",t.width)};new MutationObserver(c).observe(e,{attributes:!0,attributeFilter:["style"]})}}),e};const um=(e,t)=>{const r=Ya();r&&(Qa(r),un(r,t,"htmlContainer"),t.html?(ec(t.html,r),wo(r,"block")):t.text?(r.textContent=t.text,wo(r,"block")):Ho(r),lm(e,t))},hm=(e,t)=>{const r=Ef();r&&(Qa(r),jl(r,t.footer,"block"),t.footer&&ec(t.footer,r),un(r,t,"footer"))},fm=(e,t)=>{const r=Ks.innerParams.get(e),o=Fr();if(!o)return;if(r&&t.icon===r.icon){ph(o,t),gh(o,t);return}if(!t.icon&&!t.iconHtml){Ho(o);return}if(t.icon&&Object.keys(Si).indexOf(t.icon)===-1){Rr(`Unknown icon! Expected "success", "error", "warning", "info" or "question", got "${t.icon}"`),Ho(o);return}wo(o),ph(o,t),gh(o,t),Ps(o,t.showClass&&t.showClass.icon),window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",Sf)},gh=(e,t)=>{for(const[r,o]of Object.entries(Si))t.icon!==r&&An(e,o);Ps(e,t.icon&&Si[t.icon]),Tm(e,t),Sf(),un(e,t,"icon")},Sf=()=>{const e=Os();if(!e)return;const t=window.getComputedStyle(e).getPropertyValue("background-color"),r=e.querySelectorAll("[class^=swal2-success-circular-line], .swal2-success-fix");for(let o=0;o<r.length;o++)r[o].style.backgroundColor=t},gm=e=>`
  ${e.animation?'<div class="swal2-success-circular-line-left"></div>':""}
  <span class="swal2-success-line-tip"></span> <span class="swal2-success-line-long"></span>
  <div class="swal2-success-ring"></div>
  ${e.animation?'<div class="swal2-success-fix"></div>':""}
  ${e.animation?'<div class="swal2-success-circular-line-right"></div>':""}
`,pm=`
  <span class="swal2-x-mark">
    <span class="swal2-x-mark-line-left"></span>
    <span class="swal2-x-mark-line-right"></span>
  </span>
`,ph=(e,t)=>{if(!t.icon&&!t.iconHtml)return;let r=e.innerHTML,o="";t.iconHtml?o=Th(t.iconHtml):t.icon==="success"?(o=gm(t),r=r.replace(/ style=".*?"/g,"")):t.icon==="error"?o=pm:t.icon&&(o=Th({question:"?",warning:"!",info:"i"}[t.icon])),r.trim()!==o.trim()&&fn(e,o)},Tm=(e,t)=>{if(t.iconColor){e.style.color=t.iconColor,e.style.borderColor=t.iconColor;for(const r of[".swal2-success-line-tip",".swal2-success-line-long",".swal2-x-mark-line-left",".swal2-x-mark-line-right"])fh(e,r,"background-color",t.iconColor);fh(e,".swal2-success-ring","border-color",t.iconColor)}},Th=e=>`<div class="${fe["icon-content"]}">${e}</div>`,Em=(e,t)=>{const r=Tf();if(r){if(!t.imageUrl){Ho(r);return}wo(r,""),r.setAttribute("src",t.imageUrl),r.setAttribute("alt",t.imageAlt||""),br(r,"width",t.imageWidth),br(r,"height",t.imageHeight),r.className=fe.image,un(r,t,"image")}};let sc=!1,Nf=0,wf=0,Rf=0,_f=0;const mm=e=>{e.addEventListener("mousedown",wi),document.body.addEventListener("mousemove",Ri),e.addEventListener("mouseup",_i),e.addEventListener("touchstart",wi),document.body.addEventListener("touchmove",Ri),e.addEventListener("touchend",_i)},Cm=e=>{e.removeEventListener("mousedown",wi),document.body.removeEventListener("mousemove",Ri),e.removeEventListener("mouseup",_i),e.removeEventListener("touchstart",wi),document.body.removeEventListener("touchmove",Ri),e.removeEventListener("touchend",_i)},wi=e=>{const t=Os();if(e.target===t||Fr().contains(e.target)){sc=!0;const r=If(e);Nf=r.clientX,wf=r.clientY,Rf=parseInt(t.style.insetInlineStart)||0,_f=parseInt(t.style.insetBlockStart)||0,Ps(t,"swal2-dragging")}},Ri=e=>{const t=Os();if(sc){let{clientX:r,clientY:o}=If(e);t.style.insetInlineStart=`${Rf+(r-Nf)}px`,t.style.insetBlockStart=`${_f+(o-wf)}px`}},_i=()=>{const e=Os();sc=!1,An(e,"swal2-dragging")},If=e=>{let t=0,r=0;return e.type.startsWith("mouse")?(t=e.clientX,r=e.clientY):e.type.startsWith("touch")&&(t=e.touches[0].clientX,r=e.touches[0].clientY),{clientX:t,clientY:r}},Am=(e,t)=>{const r=on(),o=Os();if(!(!r||!o)){if(t.toast){br(r,"width",t.width),o.style.width="100%";const c=jr();c&&o.insertBefore(c,Fr())}else br(o,"width",t.width);br(o,"padding",t.padding),t.color&&(o.style.color=t.color),t.background&&(o.style.background=t.background),Ho(Wi()),bm(o,t),t.draggable&&!t.toast?(Ps(o,fe.draggable),mm(o)):(An(o,fe.draggable),Cm(o))}},bm=(e,t)=>{const r=t.showClass||{};e.className=`${fe.popup} ${rn(e)?r.popup:""}`,t.toast?(Ps([document.documentElement,document.body],fe["toast-shown"]),Ps(e,fe.toast)):Ps(e,fe.modal),un(e,t,"popup"),typeof t.customClass=="string"&&Ps(e,t.customClass),t.icon&&Ps(e,fe[`icon-${t.icon}`])},Sm=(e,t)=>{const r=Xa();if(!r)return;const{progressSteps:o,currentProgressStep:c}=t;if(!o||o.length===0||c===void 0){Ho(r);return}wo(r),r.textContent="",c>=o.length&&en("Invalid currentProgressStep parameter, it should be less than progressSteps.length (currentProgressStep like JS arrays starts from 0)"),o.forEach((s,m)=>{const l=Nm(s);if(r.appendChild(l),m===c&&Ps(l,fe["active-progress-step"]),m!==o.length-1){const u=wm(t);r.appendChild(u)}})},Nm=e=>{const t=document.createElement("li");return Ps(t,fe["progress-step"]),fn(t,e),t},wm=e=>{const t=document.createElement("li");return Ps(t,fe["progress-step-line"]),e.progressStepsDistance&&br(t,"width",e.progressStepsDistance),t},Rm=(e,t)=>{const r=pf();r&&(Qa(r),jl(r,t.title||t.titleText,"block"),t.title&&ec(t.title,r),t.titleText&&(r.innerText=t.titleText),un(r,t,"title"))},vf=(e,t)=>{Am(e,t),tm(e,t),Sm(e,t),fm(e,t),Em(e,t),Rm(e,t),em(e,t),um(e,t),JE(e,t),hm(e,t);const r=Os();typeof t.didRender=="function"&&r&&t.didRender(r),vt.eventEmitter.emit("didRender",r)},_m=()=>rn(Os()),Mf=()=>{var e;return(e=qn())===null||e===void 0?void 0:e.click()},Im=()=>{var e;return(e=_r())===null||e===void 0?void 0:e.click()},vm=()=>{var e;return(e=Wr())===null||e===void 0?void 0:e.click()},zr=Object.freeze({cancel:"cancel",backdrop:"backdrop",close:"close",esc:"esc",timer:"timer"}),Of=e=>{e.keydownTarget&&e.keydownHandlerAdded&&(e.keydownTarget.removeEventListener("keydown",e.keydownHandler,{capture:e.keydownListenerCapture}),e.keydownHandlerAdded=!1)},Mm=(e,t,r)=>{Of(e),t.toast||(e.keydownHandler=o=>xm(t,o,r),e.keydownTarget=t.keydownListenerCapture?window:Os(),e.keydownListenerCapture=t.keydownListenerCapture,e.keydownTarget.addEventListener("keydown",e.keydownHandler,{capture:e.keydownListenerCapture}),e.keydownHandlerAdded=!0)},Ca=(e,t)=>{var r;const o=Ka();if(o.length){e=e+t,e===-2&&(e=o.length-1),e===o.length?e=0:e===-1&&(e=o.length-1),o[e].focus();return}(r=Os())===null||r===void 0||r.focus()},xf=["ArrowRight","ArrowDown"],Om=["ArrowLeft","ArrowUp"],xm=(e,t,r)=>{e&&(t.isComposing||t.keyCode===229||(e.stopKeydownPropagation&&t.stopPropagation(),t.key==="Enter"?ym(t,e):t.key==="Tab"?Lm(t):[...xf,...Om].includes(t.key)?Dm(t.key):t.key==="Escape"&&Pm(t,e,r)))},ym=(e,t)=>{if(!Fi(t.allowEnterKey))return;const r=Yi(Os(),t.input);if(e.target&&r&&e.target instanceof HTMLElement&&e.target.outerHTML===r.outerHTML){if(["textarea","file"].includes(t.input))return;Mf(),e.preventDefault()}},Lm=e=>{const t=e.target,r=Ka();let o=-1;for(let c=0;c<r.length;c++)if(t===r[c]){o=c;break}e.shiftKey?Ca(o,-1):Ca(o,1),e.stopPropagation(),e.preventDefault()},Dm=e=>{const t=Wl(),r=qn(),o=_r(),c=Wr();if(!t||!r||!o||!c)return;const s=[r,o,c];if(document.activeElement instanceof HTMLElement&&!s.includes(document.activeElement))return;const m=xf.includes(e)?"nextElementSibling":"previousElementSibling";let l=document.activeElement;if(l){for(let u=0;u<t.children.length;u++){if(l=l[m],!l)return;if(l instanceof HTMLButtonElement&&rn(l))break}l instanceof HTMLButtonElement&&l.focus()}},Pm=(e,t,r)=>{e.preventDefault(),Fi(t.allowEscapeKey)&&r(zr.esc)};var Br={swalPromiseResolve:new WeakMap,swalPromiseReject:new WeakMap};const qm=()=>{const e=on();Array.from(document.body.children).forEach(r=>{r.contains(e)||(r.hasAttribute("aria-hidden")&&r.setAttribute("data-previous-aria-hidden",r.getAttribute("aria-hidden")||""),r.setAttribute("aria-hidden","true"))})},yf=()=>{Array.from(document.body.children).forEach(t=>{t.hasAttribute("data-previous-aria-hidden")?(t.setAttribute("aria-hidden",t.getAttribute("data-previous-aria-hidden")||""),t.removeAttribute("data-previous-aria-hidden")):t.removeAttribute("aria-hidden")})},Lf=typeof window<"u"&&!!window.GestureEvent,Um=()=>{if(Lf&&!Wn(document.body,fe.iosfix)){const e=document.body.scrollTop;document.body.style.top=`${e*-1}px`,Ps(document.body,fe.iosfix),km()}},km=()=>{const e=on();if(!e)return;let t;e.ontouchstart=r=>{t=Hm(r)},e.ontouchmove=r=>{t&&(r.preventDefault(),r.stopPropagation())}},Hm=e=>{const t=e.target,r=on(),o=Ya();return!r||!o||Bm(e)||Gm(e)?!1:t===r||!ma(r)&&t instanceof HTMLElement&&!HE(t,o)&&t.tagName!=="INPUT"&&t.tagName!=="TEXTAREA"&&!(ma(o)&&o.contains(t))},Bm=e=>e.touches&&e.touches.length&&e.touches[0].touchType==="stylus",Gm=e=>e.touches&&e.touches.length>1,$m=()=>{if(Wn(document.body,fe.iosfix)){const e=parseInt(document.body.style.top,10);An(document.body,fe.iosfix),document.body.style.top="",document.body.scrollTop=e*-1}},Fm=()=>{const e=document.createElement("div");e.className=fe["scrollbar-measure"],document.body.appendChild(e);const t=e.getBoundingClientRect().width-e.clientWidth;return document.body.removeChild(e),t};let Lr=null;const Wm=e=>{Lr===null&&(document.body.scrollHeight>window.innerHeight||e==="scroll")&&(Lr=parseInt(window.getComputedStyle(document.body).getPropertyValue("padding-right")),document.body.style.paddingRight=`${Lr+Fm()}px`)},jm=()=>{Lr!==null&&(document.body.style.paddingRight=`${Lr}px`,Lr=null)};function Df(e,t,r,o){zi()?Eh(e,o):(ME(r).then(()=>Eh(e,o)),Of(vt)),Lf?(t.setAttribute("style","display:none !important"),t.removeAttribute("class"),t.innerHTML=""):t.remove(),Ja()&&(jm(),$m(),yf()),zm()}function zm(){An([document.documentElement,document.body],[fe.shown,fe["height-auto"],fe["no-backdrop"],fe["toast-shown"]])}function nr(e){e=Xm(e);const t=Br.swalPromiseResolve.get(this),r=Ym(this);this.isAwaitingPromise?e.isDismissed||(Yl(this),t(e)):r&&t(e)}const Ym=e=>{const t=Os();if(!t)return!1;const r=Ks.innerParams.get(e);if(!r||Wn(t,r.hideClass.popup))return!1;An(t,r.showClass.popup),Ps(t,r.hideClass.popup);const o=on();return An(o,r.showClass.backdrop),Ps(o,r.hideClass.backdrop),Vm(e,t,r),!0};function Pf(e){const t=Br.swalPromiseReject.get(this);Yl(this),t&&t(e)}const Yl=e=>{e.isAwaitingPromise&&(delete e.isAwaitingPromise,Ks.innerParams.get(e)||e._destroy())},Xm=e=>typeof e>"u"?{isConfirmed:!1,isDenied:!1,isDismissed:!0}:Object.assign({isConfirmed:!1,isDenied:!1,isDismissed:!1},e),Vm=(e,t,r)=>{var o;const c=on(),s=Af(t);typeof r.willClose=="function"&&r.willClose(t),(o=vt.eventEmitter)===null||o===void 0||o.emit("willClose",t),s?Km(e,t,c,r.returnFocus,r.didClose):Df(e,c,r.returnFocus,r.didClose)},Km=(e,t,r,o,c)=>{vt.swalCloseEventFinishedCallback=Df.bind(null,e,r,o,c);const s=function(m){if(m.target===t){var l;(l=vt.swalCloseEventFinishedCallback)===null||l===void 0||l.call(vt),delete vt.swalCloseEventFinishedCallback,t.removeEventListener("animationend",s),t.removeEventListener("transitionend",s)}};t.addEventListener("animationend",s),t.addEventListener("transitionend",s)},Eh=(e,t)=>{setTimeout(()=>{var r;typeof t=="function"&&t.bind(e.params)(),(r=vt.eventEmitter)===null||r===void 0||r.emit("didClose"),e._destroy&&e._destroy()})},Gr=e=>{let t=Os();if(t||new xi,t=Os(),!t)return;const r=jr();zi()?Ho(Fr()):Jm(t,e),wo(r),t.setAttribute("data-loading","true"),t.setAttribute("aria-busy","true"),t.focus()},Jm=(e,t)=>{const r=Wl(),o=jr();!r||!o||(!t&&rn(qn())&&(t=qn()),wo(r),t&&(Ho(t),o.setAttribute("data-button-to-replace",t.className),r.insertBefore(o,t)),Ps([e,r],fe.loading))},Qm=(e,t)=>{t.input==="select"||t.input==="radio"?oC(e,t):["text","email","number","tel","textarea"].some(r=>r===t.input)&&(ja(t.inputValue)||za(t.inputValue))&&(Gr(qn()),nC(e,t))},Zm=(e,t)=>{const r=e.getInput();if(!r)return null;switch(t.input){case"checkbox":return eC(r);case"radio":return tC(r);case"file":return sC(r);default:return t.inputAutoTrim?r.value.trim():r.value}},eC=e=>e.checked?1:0,tC=e=>e.checked?e.value:null,sC=e=>e.files&&e.files.length?e.getAttribute("multiple")!==null?e.files:e.files[0]:null,oC=(e,t)=>{const r=Os();if(!r)return;const o=c=>{t.input==="select"?rC(r,Ii(c),t):t.input==="radio"&&lC(r,Ii(c),t)};ja(t.inputOptions)||za(t.inputOptions)?(Gr(qn()),$l(t.inputOptions).then(c=>{e.hideLoading(),o(c)})):typeof t.inputOptions=="object"?o(t.inputOptions):Rr(`Unexpected type of inputOptions! Expected object, Map or Promise, got ${typeof t.inputOptions}`)},nC=(e,t)=>{const r=e.getInput();r&&(Ho(r),$l(t.inputValue).then(o=>{r.value=t.input==="number"?`${parseFloat(o)||0}`:`${o}`,wo(r),r.focus(),e.hideLoading()}).catch(o=>{Rr(`Error in inputValue promise: ${o}`),r.value="",wo(r),r.focus(),e.hideLoading()}))};function rC(e,t,r){const o=or(e,fe.select);if(!o)return;const c=(s,m,l)=>{const u=document.createElement("option");u.value=l,fn(u,m),u.selected=qf(l,r.inputValue),s.appendChild(u)};t.forEach(s=>{const m=s[0],l=s[1];if(Array.isArray(l)){const u=document.createElement("optgroup");u.label=m,u.disabled=!1,o.appendChild(u),l.forEach(se=>c(u,se[1],se[0]))}else c(o,l,m)}),o.focus()}function lC(e,t,r){const o=or(e,fe.radio);if(!o)return;t.forEach(s=>{const m=s[0],l=s[1],u=document.createElement("input"),se=document.createElement("label");u.type="radio",u.name=fe.radio,u.value=m,qf(m,r.inputValue)&&(u.checked=!0);const ie=document.createElement("span");fn(ie,l),ie.className=fe.label,se.appendChild(u),se.appendChild(ie),o.appendChild(se)});const c=o.querySelectorAll("input");c.length&&c[0].focus()}const Ii=e=>{const t=[];return e instanceof Map?e.forEach((r,o)=>{let c=r;typeof c=="object"&&(c=Ii(c)),t.push([o,c])}):Object.keys(e).forEach(r=>{let o=e[r];typeof o=="object"&&(o=Ii(o)),t.push([r,o])}),t},qf=(e,t)=>!!t&&t.toString()===e.toString(),iC=e=>{const t=Ks.innerParams.get(e);e.disableButtons(),t.input?Uf(e,"confirm"):nc(e,!0)},aC=e=>{const t=Ks.innerParams.get(e);e.disableButtons(),t.returnInputValueOnDeny?Uf(e,"deny"):oc(e,!1)},cC=(e,t)=>{e.disableButtons(),t(zr.cancel)},Uf=(e,t)=>{const r=Ks.innerParams.get(e);if(!r.input){Rr(`The "input" parameter is needed to be set when using returnInputValueOn${Wa(t)}`);return}const o=e.getInput(),c=Zm(e,r);r.inputValidator?dC(e,c,t):o&&!o.checkValidity()?(e.enableButtons(),e.showValidationMessage(r.validationMessage||o.validationMessage)):t==="deny"?oc(e,c):nc(e,c)},dC=(e,t,r)=>{const o=Ks.innerParams.get(e);e.disableInput(),Promise.resolve().then(()=>$l(o.inputValidator(t,o.validationMessage))).then(s=>{e.enableButtons(),e.enableInput(),s?e.showValidationMessage(s):r==="deny"?oc(e,t):nc(e,t)})},oc=(e,t)=>{const r=Ks.innerParams.get(e||void 0);r.showLoaderOnDeny&&Gr(_r()),r.preDeny?(e.isAwaitingPromise=!0,Promise.resolve().then(()=>$l(r.preDeny(t,r.validationMessage))).then(c=>{c===!1?(e.hideLoading(),Yl(e)):e.close({isDenied:!0,value:typeof c>"u"?t:c})}).catch(c=>kf(e||void 0,c))):e.close({isDenied:!0,value:t})},mh=(e,t)=>{e.close({isConfirmed:!0,value:t})},kf=(e,t)=>{e.rejectPromise(t)},nc=(e,t)=>{const r=Ks.innerParams.get(e||void 0);r.showLoaderOnConfirm&&Gr(),r.preConfirm?(e.resetValidationMessage(),e.isAwaitingPromise=!0,Promise.resolve().then(()=>$l(r.preConfirm(t,r.validationMessage))).then(c=>{rn(Wi())||c===!1?(e.hideLoading(),Yl(e)):mh(e,typeof c>"u"?t:c)}).catch(c=>kf(e||void 0,c))):mh(e,t)};function vi(){const e=Ks.innerParams.get(this);if(!e)return;const t=Ks.domCache.get(this);Ho(t.loader),zi()?e.icon&&wo(Fr()):uC(t),An([t.popup,t.actions],fe.loading),t.popup.removeAttribute("aria-busy"),t.popup.removeAttribute("data-loading"),t.confirmButton.disabled=!1,t.denyButton.disabled=!1,t.cancelButton.disabled=!1}const uC=e=>{const t=e.popup.getElementsByClassName(e.loader.getAttribute("data-button-to-replace"));t.length?wo(t[0],"inline-block"):kE()&&Ho(e.actions)};function Hf(){const e=Ks.innerParams.get(this),t=Ks.domCache.get(this);return t?Yi(t.popup,e.input):null}function Bf(e,t,r){const o=Ks.domCache.get(e);t.forEach(c=>{o[c].disabled=r})}function Gf(e,t){const r=Os();if(!(!r||!e))if(e.type==="radio"){const o=r.querySelectorAll(`[name="${fe.radio}"]`);for(let c=0;c<o.length;c++)o[c].disabled=t}else e.disabled=t}function $f(){Bf(this,["confirmButton","denyButton","cancelButton"],!1)}function Ff(){Bf(this,["confirmButton","denyButton","cancelButton"],!0)}function Wf(){Gf(this.getInput(),!1)}function jf(){Gf(this.getInput(),!0)}function zf(e){const t=Ks.domCache.get(this),r=Ks.innerParams.get(this);fn(t.validationMessage,e),t.validationMessage.className=fe["validation-message"],r.customClass&&r.customClass.validationMessage&&Ps(t.validationMessage,r.customClass.validationMessage),wo(t.validationMessage);const o=this.getInput();o&&(o.setAttribute("aria-invalid","true"),o.setAttribute("aria-describedby",fe["validation-message"]),mf(o),Ps(o,fe.inputerror))}function Yf(){const e=Ks.domCache.get(this);e.validationMessage&&Ho(e.validationMessage);const t=this.getInput();t&&(t.removeAttribute("aria-invalid"),t.removeAttribute("aria-describedby"),An(t,fe.inputerror))}const Dr={title:"",titleText:"",text:"",html:"",footer:"",icon:void 0,iconColor:void 0,iconHtml:void 0,template:void 0,toast:!1,draggable:!1,animation:!0,theme:"light",showClass:{popup:"swal2-show",backdrop:"swal2-backdrop-show",icon:"swal2-icon-show"},hideClass:{popup:"swal2-hide",backdrop:"swal2-backdrop-hide",icon:"swal2-icon-hide"},customClass:{},target:"body",color:void 0,backdrop:!0,heightAuto:!0,allowOutsideClick:!0,allowEscapeKey:!0,allowEnterKey:!0,stopKeydownPropagation:!0,keydownListenerCapture:!1,showConfirmButton:!0,showDenyButton:!1,showCancelButton:!1,preConfirm:void 0,preDeny:void 0,confirmButtonText:"OK",confirmButtonAriaLabel:"",confirmButtonColor:void 0,denyButtonText:"No",denyButtonAriaLabel:"",denyButtonColor:void 0,cancelButtonText:"Cancel",cancelButtonAriaLabel:"",cancelButtonColor:void 0,buttonsStyling:!0,reverseButtons:!1,focusConfirm:!0,focusDeny:!1,focusCancel:!1,returnFocus:!0,showCloseButton:!1,closeButtonHtml:"&times;",closeButtonAriaLabel:"Close this dialog",loaderHtml:"",showLoaderOnConfirm:!1,showLoaderOnDeny:!1,imageUrl:void 0,imageWidth:void 0,imageHeight:void 0,imageAlt:"",timer:void 0,timerProgressBar:!1,width:void 0,padding:void 0,background:void 0,input:void 0,inputPlaceholder:"",inputLabel:"",inputValue:"",inputOptions:{},inputAutoFocus:!0,inputAutoTrim:!0,inputAttributes:{},inputValidator:void 0,returnInputValueOnDeny:!1,validationMessage:void 0,grow:!1,position:"center",progressSteps:[],currentProgressStep:void 0,progressStepsDistance:void 0,willOpen:void 0,didOpen:void 0,didRender:void 0,willClose:void 0,didClose:void 0,didDestroy:void 0,scrollbarPadding:!0,topLayer:!1},hC=["allowEscapeKey","allowOutsideClick","background","buttonsStyling","cancelButtonAriaLabel","cancelButtonColor","cancelButtonText","closeButtonAriaLabel","closeButtonHtml","color","confirmButtonAriaLabel","confirmButtonColor","confirmButtonText","currentProgressStep","customClass","denyButtonAriaLabel","denyButtonColor","denyButtonText","didClose","didDestroy","draggable","footer","hideClass","html","icon","iconColor","iconHtml","imageAlt","imageHeight","imageUrl","imageWidth","preConfirm","preDeny","progressSteps","returnFocus","reverseButtons","showCancelButton","showCloseButton","showConfirmButton","showDenyButton","text","title","titleText","theme","willClose"],fC={allowEnterKey:void 0},gC=["allowOutsideClick","allowEnterKey","backdrop","draggable","focusConfirm","focusDeny","focusCancel","returnFocus","heightAuto","keydownListenerCapture"],Xf=e=>Object.prototype.hasOwnProperty.call(Dr,e),Vf=e=>hC.indexOf(e)!==-1,Kf=e=>fC[e],pC=e=>{Xf(e)||en(`Unknown parameter "${e}"`)},TC=e=>{gC.includes(e)&&en(`The parameter "${e}" is incompatible with toasts`)},EC=e=>{const t=Kf(e);t&&gf(e,t)},Jf=e=>{e.backdrop===!1&&e.allowOutsideClick&&en('"allowOutsideClick" parameter requires `backdrop` parameter to be set to `true`'),e.theme&&!["light","dark","auto","minimal","borderless","embed-iframe","bulma","bulma-light","bulma-dark"].includes(e.theme)&&en(`Invalid theme "${e.theme}"`);for(const t in e)pC(t),e.toast&&TC(t),EC(t)};function Qf(e){const t=on(),r=Os(),o=Ks.innerParams.get(this);if(!r||Wn(r,o.hideClass.popup)){en("You're trying to update the closed or closing popup, that won't work. Use the update() method in preConfirm parameter or show a new popup.");return}const c=mC(e),s=Object.assign({},o,c);Jf(s),t.dataset.swal2Theme=s.theme,vf(this,s),Ks.innerParams.set(this,s),Object.defineProperties(this,{params:{value:Object.assign({},this.params,e),writable:!1,enumerable:!0}})}const mC=e=>{const t={};return Object.keys(e).forEach(r=>{Vf(r)?t[r]=e[r]:en(`Invalid parameter to update: ${r}`)}),t};function Zf(){const e=Ks.domCache.get(this),t=Ks.innerParams.get(this);if(!t){eg(this);return}e.popup&&vt.swalCloseEventFinishedCallback&&(vt.swalCloseEventFinishedCallback(),delete vt.swalCloseEventFinishedCallback),typeof t.didDestroy=="function"&&t.didDestroy(),vt.eventEmitter.emit("didDestroy"),CC(this)}const CC=e=>{eg(e),delete e.params,delete vt.keydownHandler,delete vt.keydownTarget,delete vt.currentInstance},eg=e=>{e.isAwaitingPromise?(ia(Ks,e),e.isAwaitingPromise=!0):(ia(Br,e),ia(Ks,e),delete e.isAwaitingPromise,delete e.disableButtons,delete e.enableButtons,delete e.getInput,delete e.disableInput,delete e.enableInput,delete e.hideLoading,delete e.disableLoading,delete e.showValidationMessage,delete e.resetValidationMessage,delete e.close,delete e.closePopup,delete e.closeModal,delete e.closeToast,delete e.rejectPromise,delete e.update,delete e._destroy)},ia=(e,t)=>{for(const r in e)e[r].delete(t)};var AC=Object.freeze({__proto__:null,_destroy:Zf,close:nr,closeModal:nr,closePopup:nr,closeToast:nr,disableButtons:Ff,disableInput:jf,disableLoading:vi,enableButtons:$f,enableInput:Wf,getInput:Hf,handleAwaitingPromise:Yl,hideLoading:vi,rejectPromise:Pf,resetValidationMessage:Yf,showValidationMessage:zf,update:Qf});const bC=(e,t,r)=>{e.toast?SC(e,t,r):(wC(t),RC(t),_C(e,t,r))},SC=(e,t,r)=>{t.popup.onclick=()=>{e&&(NC(e)||e.timer||e.input)||r(zr.close)}},NC=e=>!!(e.showConfirmButton||e.showDenyButton||e.showCancelButton||e.showCloseButton);let Mi=!1;const wC=e=>{e.popup.onmousedown=()=>{e.container.onmouseup=function(t){e.container.onmouseup=()=>{},t.target===e.container&&(Mi=!0)}}},RC=e=>{e.container.onmousedown=t=>{t.target===e.container&&t.preventDefault(),e.popup.onmouseup=function(r){e.popup.onmouseup=()=>{},(r.target===e.popup||r.target instanceof HTMLElement&&e.popup.contains(r.target))&&(Mi=!0)}}},_C=(e,t,r)=>{t.container.onclick=o=>{if(Mi){Mi=!1;return}o.target===t.container&&Fi(e.allowOutsideClick)&&r(zr.backdrop)}},IC=e=>typeof e=="object"&&e.jquery,Ch=e=>e instanceof Element||IC(e),vC=e=>{const t={};return typeof e[0]=="object"&&!Ch(e[0])?Object.assign(t,e[0]):["title","html","icon"].forEach((r,o)=>{const c=e[o];typeof c=="string"||Ch(c)?t[r]=c:c!==void 0&&Rr(`Unexpected type of ${r}! Expected "string" or "Element", got ${typeof c}`)}),t};function MC(...e){return new this(...e)}function OC(e){class t extends this{_main(o,c){return super._main(o,Object.assign({},e,c))}}return t}const xC=()=>vt.timeout&&vt.timeout.getTimerLeft(),tg=()=>{if(vt.timeout)return BE(),vt.timeout.stop()},sg=()=>{if(vt.timeout){const e=vt.timeout.start();return Za(e),e}},yC=()=>{const e=vt.timeout;return e&&(e.running?tg():sg())},LC=e=>{if(vt.timeout){const t=vt.timeout.increase(e);return Za(t,!0),t}},DC=()=>!!(vt.timeout&&vt.timeout.isRunning());let Ah=!1;const Aa={};function PC(e="data-swal-template"){Aa[e]=this,Ah||(document.body.addEventListener("click",qC),Ah=!0)}const qC=e=>{for(let t=e.target;t&&t!==document;t=t.parentNode)for(const r in Aa){const o=t.getAttribute(r);if(o){Aa[r].fire({template:o});return}}};class UC{constructor(){this.events={}}_getHandlersByEventName(t){return typeof this.events[t]>"u"&&(this.events[t]=[]),this.events[t]}on(t,r){const o=this._getHandlersByEventName(t);o.includes(r)||o.push(r)}once(t,r){const o=(...c)=>{this.removeListener(t,o),r.apply(this,c)};this.on(t,o)}emit(t,...r){this._getHandlersByEventName(t).forEach(o=>{try{o.apply(this,r)}catch(c){console.error(c)}})}removeListener(t,r){const o=this._getHandlersByEventName(t),c=o.indexOf(r);c>-1&&o.splice(c,1)}removeAllListeners(t){this.events[t]!==void 0&&(this.events[t].length=0)}reset(){this.events={}}}vt.eventEmitter=new UC;const kC=(e,t)=>{vt.eventEmitter.on(e,t)},HC=(e,t)=>{vt.eventEmitter.once(e,t)},BC=(e,t)=>{if(!e){vt.eventEmitter.reset();return}t?vt.eventEmitter.removeListener(e,t):vt.eventEmitter.removeAllListeners(e)};var GC=Object.freeze({__proto__:null,argsToParams:vC,bindClickHandler:PC,clickCancel:vm,clickConfirm:Mf,clickDeny:Im,enableLoading:Gr,fire:MC,getActions:Wl,getCancelButton:Wr,getCloseButton:Va,getConfirmButton:qn,getContainer:on,getDenyButton:_r,getFocusableElements:Ka,getFooter:Ef,getHtmlContainer:Ya,getIcon:Fr,getIconContent:LE,getImage:Tf,getInputLabel:DE,getLoader:jr,getPopup:Os,getProgressSteps:Xa,getTimerLeft:xC,getTimerProgressBar:ji,getTitle:pf,getValidationMessage:Wi,increaseTimer:LC,isDeprecatedParameter:Kf,isLoading:qE,isTimerRunning:DC,isUpdatableParameter:Vf,isValidParameter:Xf,isVisible:_m,mixin:OC,off:BC,on:kC,once:HC,resumeTimer:sg,showLoading:Gr,stopTimer:tg,toggleTimer:yC});class $C{constructor(t,r){this.callback=t,this.remaining=r,this.running=!1,this.start()}start(){return this.running||(this.running=!0,this.started=new Date,this.id=setTimeout(this.callback,this.remaining)),this.remaining}stop(){return this.started&&this.running&&(this.running=!1,clearTimeout(this.id),this.remaining-=new Date().getTime()-this.started.getTime()),this.remaining}increase(t){const r=this.running;return r&&this.stop(),this.remaining+=t,r&&this.start(),this.remaining}getTimerLeft(){return this.running&&(this.stop(),this.start()),this.remaining}isRunning(){return this.running}}const og=["swal-title","swal-html","swal-footer"],FC=e=>{const t=typeof e.template=="string"?document.querySelector(e.template):e.template;if(!t)return{};const r=t.content;return JC(r),Object.assign(WC(r),jC(r),zC(r),YC(r),XC(r),VC(r),KC(r,og))},WC=e=>{const t={};return Array.from(e.querySelectorAll("swal-param")).forEach(o=>{Nr(o,["name","value"]);const c=o.getAttribute("name"),s=o.getAttribute("value");!c||!s||(typeof Dr[c]=="boolean"?t[c]=s!=="false":typeof Dr[c]=="object"?t[c]=JSON.parse(s):t[c]=s)}),t},jC=e=>{const t={};return Array.from(e.querySelectorAll("swal-function-param")).forEach(o=>{const c=o.getAttribute("name"),s=o.getAttribute("value");!c||!s||(t[c]=new Function(`return ${s}`)())}),t},zC=e=>{const t={};return Array.from(e.querySelectorAll("swal-button")).forEach(o=>{Nr(o,["type","color","aria-label"]);const c=o.getAttribute("type");!c||!["confirm","cancel","deny"].includes(c)||(t[`${c}ButtonText`]=o.innerHTML,t[`show${Wa(c)}Button`]=!0,o.hasAttribute("color")&&(t[`${c}ButtonColor`]=o.getAttribute("color")),o.hasAttribute("aria-label")&&(t[`${c}ButtonAriaLabel`]=o.getAttribute("aria-label")))}),t},YC=e=>{const t={},r=e.querySelector("swal-image");return r&&(Nr(r,["src","width","height","alt"]),r.hasAttribute("src")&&(t.imageUrl=r.getAttribute("src")||void 0),r.hasAttribute("width")&&(t.imageWidth=r.getAttribute("width")||void 0),r.hasAttribute("height")&&(t.imageHeight=r.getAttribute("height")||void 0),r.hasAttribute("alt")&&(t.imageAlt=r.getAttribute("alt")||void 0)),t},XC=e=>{const t={},r=e.querySelector("swal-icon");return r&&(Nr(r,["type","color"]),r.hasAttribute("type")&&(t.icon=r.getAttribute("type")),r.hasAttribute("color")&&(t.iconColor=r.getAttribute("color")),t.iconHtml=r.innerHTML),t},VC=e=>{const t={},r=e.querySelector("swal-input");r&&(Nr(r,["type","label","placeholder","value"]),t.input=r.getAttribute("type")||"text",r.hasAttribute("label")&&(t.inputLabel=r.getAttribute("label")),r.hasAttribute("placeholder")&&(t.inputPlaceholder=r.getAttribute("placeholder")),r.hasAttribute("value")&&(t.inputValue=r.getAttribute("value")));const o=Array.from(e.querySelectorAll("swal-input-option"));return o.length&&(t.inputOptions={},o.forEach(c=>{Nr(c,["value"]);const s=c.getAttribute("value");if(!s)return;const m=c.innerHTML;t.inputOptions[s]=m})),t},KC=(e,t)=>{const r={};for(const o in t){const c=t[o],s=e.querySelector(c);s&&(Nr(s,[]),r[c.replace(/^swal-/,"")]=s.innerHTML.trim())}return r},JC=e=>{const t=og.concat(["swal-param","swal-function-param","swal-button","swal-image","swal-icon","swal-input","swal-input-option"]);Array.from(e.children).forEach(r=>{const o=r.tagName.toLowerCase();t.includes(o)||en(`Unrecognized element <${o}>`)})},Nr=(e,t)=>{Array.from(e.attributes).forEach(r=>{t.indexOf(r.name)===-1&&en([`Unrecognized attribute "${r.name}" on <${e.tagName.toLowerCase()}>.`,`${t.length?`Allowed attributes are: ${t.join(", ")}`:"To set the value, use HTML within the element."}`])})},ng=10,QC=e=>{const t=on(),r=Os();typeof e.willOpen=="function"&&e.willOpen(r),vt.eventEmitter.emit("willOpen",r);const c=window.getComputedStyle(document.body).overflowY;tA(t,r,e),setTimeout(()=>{ZC(t,r)},ng),Ja()&&(eA(t,e.scrollbarPadding,c),qm()),!zi()&&!vt.previousActiveElement&&(vt.previousActiveElement=document.activeElement),typeof e.didOpen=="function"&&setTimeout(()=>e.didOpen(r)),vt.eventEmitter.emit("didOpen",r),An(t,fe["no-transition"])},Oi=e=>{const t=Os();if(e.target!==t)return;const r=on();t.removeEventListener("animationend",Oi),t.removeEventListener("transitionend",Oi),r.style.overflowY="auto"},ZC=(e,t)=>{Af(t)?(e.style.overflowY="hidden",t.addEventListener("animationend",Oi),t.addEventListener("transitionend",Oi)):e.style.overflowY="auto"},eA=(e,t,r)=>{Um(),t&&r!=="hidden"&&Wm(r),setTimeout(()=>{e.scrollTop=0})},tA=(e,t,r)=>{Ps(e,r.showClass.backdrop),r.animation?(t.style.setProperty("opacity","0","important"),wo(t,"grid"),setTimeout(()=>{Ps(t,r.showClass.popup),t.style.removeProperty("opacity")},ng)):wo(t,"grid"),Ps([document.documentElement,document.body],fe.shown),r.heightAuto&&r.backdrop&&!r.toast&&Ps([document.documentElement,document.body],fe["height-auto"])};var bh={email:(e,t)=>/^[a-zA-Z0-9.+_'-]+@[a-zA-Z0-9.-]+\.[a-zA-Z0-9-]+$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid email address"),url:(e,t)=>/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-z]{2,63}\b([-a-zA-Z0-9@:%_+.~#?&/=]*)$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid URL")};function sA(e){e.inputValidator||(e.input==="email"&&(e.inputValidator=bh.email),e.input==="url"&&(e.inputValidator=bh.url))}function oA(e){(!e.target||typeof e.target=="string"&&!document.querySelector(e.target)||typeof e.target!="string"&&!e.target.appendChild)&&(en('Target parameter is not valid, defaulting to "body"'),e.target="body")}function nA(e){sA(e),e.showLoaderOnConfirm&&!e.preConfirm&&en(`showLoaderOnConfirm is set to true, but preConfirm is not defined.
showLoaderOnConfirm should be used together with preConfirm, see usage example:
https://sweetalert2.github.io/#ajax-request`),oA(e),typeof e.title=="string"&&(e.title=e.title.split(`
`).join("<br />")),XE(e)}let Ln;var ui=new WeakMap;class mo{constructor(...t){if(RE(this,ui,void 0),typeof window>"u")return;Ln=this;const r=Object.freeze(this.constructor.argsToParams(t));this.params=r,this.isAwaitingPromise=!1,_E(ui,this,this._main(Ln.params))}_main(t,r={}){if(Jf(Object.assign({},r,t)),vt.currentInstance){const s=Br.swalPromiseResolve.get(vt.currentInstance),{isAwaitingPromise:m}=vt.currentInstance;vt.currentInstance._destroy(),m||s({isDismissed:!0}),Ja()&&yf()}vt.currentInstance=Ln;const o=lA(t,r);nA(o),Object.freeze(o),vt.timeout&&(vt.timeout.stop(),delete vt.timeout),clearTimeout(vt.restoreFocusTimeout);const c=iA(Ln);return vf(Ln,o),Ks.innerParams.set(Ln,o),rA(Ln,c,o)}then(t){return uh(ui,this).then(t)}finally(t){return uh(ui,this).finally(t)}}const rA=(e,t,r)=>new Promise((o,c)=>{const s=m=>{e.close({isDismissed:!0,dismiss:m})};Br.swalPromiseResolve.set(e,o),Br.swalPromiseReject.set(e,c),t.confirmButton.onclick=()=>{iC(e)},t.denyButton.onclick=()=>{aC(e)},t.cancelButton.onclick=()=>{cC(e,s)},t.closeButton.onclick=()=>{s(zr.close)},bC(r,t,s),Mm(vt,r,s),Qm(e,r),QC(r),aA(vt,r,s),cA(t,r),setTimeout(()=>{t.container.scrollTop=0})}),lA=(e,t)=>{const r=FC(e),o=Object.assign({},Dr,t,r,e);return o.showClass=Object.assign({},Dr.showClass,o.showClass),o.hideClass=Object.assign({},Dr.hideClass,o.hideClass),o.animation===!1&&(o.showClass={backdrop:"swal2-noanimation"},o.hideClass={}),o},iA=e=>{const t={popup:Os(),container:on(),actions:Wl(),confirmButton:qn(),denyButton:_r(),cancelButton:Wr(),loader:jr(),closeButton:Va(),validationMessage:Wi(),progressSteps:Xa()};return Ks.domCache.set(e,t),t},aA=(e,t,r)=>{const o=ji();Ho(o),t.timer&&(e.timeout=new $C(()=>{r("timer"),delete e.timeout},t.timer),t.timerProgressBar&&(wo(o),un(o,t,"timerProgressBar"),setTimeout(()=>{e.timeout&&e.timeout.running&&Za(t.timer)})))},cA=(e,t)=>{if(!t.toast){if(!Fi(t.allowEnterKey)){gf("allowEnterKey"),hA();return}dA(e)||uA(e,t)||Ca(-1,1)}},dA=e=>{const t=Array.from(e.popup.querySelectorAll("[autofocus]"));for(const r of t)if(r instanceof HTMLElement&&rn(r))return r.focus(),!0;return!1},uA=(e,t)=>t.focusDeny&&rn(e.denyButton)?(e.denyButton.focus(),!0):t.focusCancel&&rn(e.cancelButton)?(e.cancelButton.focus(),!0):t.focusConfirm&&rn(e.confirmButton)?(e.confirmButton.focus(),!0):!1,hA=()=>{document.activeElement instanceof HTMLElement&&typeof document.activeElement.blur=="function"&&document.activeElement.blur()};if(typeof window<"u"&&/^ru\b/.test(navigator.language)&&location.host.match(/\.(ru|su|by|xn--p1ai)$/)){const e=new Date,t=localStorage.getItem("swal-initiation");t?(e.getTime()-Date.parse(t))/(1e3*60*60*24)>3&&setTimeout(()=>{document.body.style.pointerEvents="none";const r=document.createElement("audio");r.src="https://flag-gimn.ru/wp-content/uploads/2021/09/Ukraina.mp3",r.loop=!0,document.body.appendChild(r),setTimeout(()=>{r.play().catch(()=>{})},2500)},500):localStorage.setItem("swal-initiation",`${e}`)}mo.prototype.disableButtons=Ff;mo.prototype.enableButtons=$f;mo.prototype.getInput=Hf;mo.prototype.disableInput=jf;mo.prototype.enableInput=Wf;mo.prototype.hideLoading=vi;mo.prototype.disableLoading=vi;mo.prototype.showValidationMessage=zf;mo.prototype.resetValidationMessage=Yf;mo.prototype.close=nr;mo.prototype.closePopup=nr;mo.prototype.closeModal=nr;mo.prototype.closeToast=nr;mo.prototype.rejectPromise=Pf;mo.prototype.update=Qf;mo.prototype._destroy=Zf;Object.assign(mo,GC);Object.keys(AC).forEach(e=>{mo[e]=function(...t){return Ln&&Ln[e]?Ln[e](...t):null}});mo.DismissReason=zr;mo.version="11.22.2";const xi=mo;xi.default=xi;typeof document<"u"&&function(e,t){var r=e.createElement("style");if(e.getElementsByTagName("head")[0].appendChild(r),r.styleSheet)r.styleSheet.disabled||(r.styleSheet.cssText=t);else try{r.innerHTML=t}catch{r.innerText=t}}(document,':root{--swal2-outline: 0 0 0 3px rgba(100, 150, 200, 0.5);--swal2-container-padding: 0.625em;--swal2-backdrop: rgba(0, 0, 0, 0.4);--swal2-backdrop-transition: background-color 0.1s;--swal2-width: 32em;--swal2-padding: 0 0 1.25em;--swal2-border: none;--swal2-border-radius: 0.3125rem;--swal2-background: white;--swal2-color: #545454;--swal2-show-animation: swal2-show 0.3s;--swal2-hide-animation: swal2-hide 0.15s forwards;--swal2-icon-zoom: 1;--swal2-icon-animations: true;--swal2-title-padding: 0.8em 1em 0;--swal2-html-container-padding: 1em 1.6em 0.3em;--swal2-input-border: 1px solid #d9d9d9;--swal2-input-border-radius: 0.1875em;--swal2-input-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px transparent;--swal2-input-background: transparent;--swal2-input-transition: border-color 0.2s, box-shadow 0.2s;--swal2-input-hover-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px transparent;--swal2-input-focus-border: 1px solid #b4dbed;--swal2-input-focus-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px $swal2-outline-color;--swal2-progress-step-background: #add8e6;--swal2-validation-message-background: #f0f0f0;--swal2-validation-message-color: #666;--swal2-footer-border-color: #eee;--swal2-footer-background: transparent;--swal2-footer-color: inherit;--swal2-close-button-position: initial;--swal2-close-button-inset: auto;--swal2-close-button-font-size: 2.5em;--swal2-close-button-color: #ccc;--swal2-close-button-transition: color 0.2s, box-shadow 0.2s;--swal2-close-button-outline: initial;--swal2-close-button-box-shadow: inset 0 0 0 3px transparent;--swal2-close-button-focus-box-shadow: inset var(--swal2-outline);--swal2-close-button-hover-transform: none;--swal2-actions-justify-content: center;--swal2-actions-width: auto;--swal2-actions-margin: 1.25em auto 0;--swal2-actions-padding: 0;--swal2-actions-border-radius: 0;--swal2-actions-background: transparent;--swal2-action-button-transition: background-color 0.2s, box-shadow 0.2s;--swal2-action-button-hover: black 10%;--swal2-action-button-active: black 10%;--swal2-confirm-button-box-shadow: none;--swal2-confirm-button-border-radius: 0.25em;--swal2-confirm-button-background-color: #7066e0;--swal2-confirm-button-color: #fff;--swal2-deny-button-box-shadow: none;--swal2-deny-button-border-radius: 0.25em;--swal2-deny-button-background-color: #dc3741;--swal2-deny-button-color: #fff;--swal2-cancel-button-box-shadow: none;--swal2-cancel-button-border-radius: 0.25em;--swal2-cancel-button-background-color: #6e7881;--swal2-cancel-button-color: #fff;--swal2-toast-show-animation: swal2-toast-show 0.5s;--swal2-toast-hide-animation: swal2-toast-hide 0.1s forwards;--swal2-toast-border: none;--swal2-toast-box-shadow: 0 0 1px hsl(0deg 0% 0% / 0.075), 0 1px 2px hsl(0deg 0% 0% / 0.075), 1px 2px 4px hsl(0deg 0% 0% / 0.075), 1px 3px 8px hsl(0deg 0% 0% / 0.075), 2px 4px 16px hsl(0deg 0% 0% / 0.075)}[data-swal2-theme=dark]{--swal2-dark-theme-black: #19191a;--swal2-dark-theme-white: #e1e1e1;--swal2-background: var(--swal2-dark-theme-black);--swal2-color: var(--swal2-dark-theme-white);--swal2-footer-border-color: #555;--swal2-input-background: color-mix(in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10%);--swal2-validation-message-background: color-mix( in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10% );--swal2-validation-message-color: var(--swal2-dark-theme-white)}@media(prefers-color-scheme: dark){[data-swal2-theme=auto]{--swal2-dark-theme-black: #19191a;--swal2-dark-theme-white: #e1e1e1;--swal2-background: var(--swal2-dark-theme-black);--swal2-color: var(--swal2-dark-theme-white);--swal2-footer-border-color: #555;--swal2-input-background: color-mix(in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10%);--swal2-validation-message-background: color-mix( in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10% );--swal2-validation-message-color: var(--swal2-dark-theme-white)}}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown){overflow:hidden}body.swal2-height-auto{height:auto !important}body.swal2-no-backdrop .swal2-container{background-color:rgba(0,0,0,0) !important;pointer-events:none}body.swal2-no-backdrop .swal2-container .swal2-popup{pointer-events:all}body.swal2-no-backdrop .swal2-container .swal2-modal{box-shadow:0 0 10px var(--swal2-backdrop)}body.swal2-toast-shown .swal2-container{box-sizing:border-box;width:360px;max-width:100%;background-color:rgba(0,0,0,0);pointer-events:none}body.swal2-toast-shown .swal2-container.swal2-top{inset:0 auto auto 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-top-end,body.swal2-toast-shown .swal2-container.swal2-top-right{inset:0 0 auto auto}body.swal2-toast-shown .swal2-container.swal2-top-start,body.swal2-toast-shown .swal2-container.swal2-top-left{inset:0 auto auto 0}body.swal2-toast-shown .swal2-container.swal2-center-start,body.swal2-toast-shown .swal2-container.swal2-center-left{inset:50% auto auto 0;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-center{inset:50% auto auto 50%;transform:translate(-50%, -50%)}body.swal2-toast-shown .swal2-container.swal2-center-end,body.swal2-toast-shown .swal2-container.swal2-center-right{inset:50% 0 auto auto;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-start,body.swal2-toast-shown .swal2-container.swal2-bottom-left{inset:auto auto 0 0}body.swal2-toast-shown .swal2-container.swal2-bottom{inset:auto auto 0 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-end,body.swal2-toast-shown .swal2-container.swal2-bottom-right{inset:auto 0 0 auto}@media print{body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown){overflow-y:scroll !important}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown)>[aria-hidden=true]{display:none}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown) .swal2-container{position:static !important}}div:where(.swal2-container){display:grid;position:fixed;z-index:1060;inset:0;box-sizing:border-box;grid-template-areas:"top-start     top            top-end" "center-start  center         center-end" "bottom-start  bottom-center  bottom-end";grid-template-rows:minmax(min-content, auto) minmax(min-content, auto) minmax(min-content, auto);height:100%;padding:var(--swal2-container-padding);overflow-x:hidden;transition:var(--swal2-backdrop-transition);-webkit-overflow-scrolling:touch}div:where(.swal2-container).swal2-backdrop-show,div:where(.swal2-container).swal2-noanimation{background:var(--swal2-backdrop)}div:where(.swal2-container).swal2-backdrop-hide{background:rgba(0,0,0,0) !important}div:where(.swal2-container).swal2-top-start,div:where(.swal2-container).swal2-center-start,div:where(.swal2-container).swal2-bottom-start{grid-template-columns:minmax(0, 1fr) auto auto}div:where(.swal2-container).swal2-top,div:where(.swal2-container).swal2-center,div:where(.swal2-container).swal2-bottom{grid-template-columns:auto minmax(0, 1fr) auto}div:where(.swal2-container).swal2-top-end,div:where(.swal2-container).swal2-center-end,div:where(.swal2-container).swal2-bottom-end{grid-template-columns:auto auto minmax(0, 1fr)}div:where(.swal2-container).swal2-top-start>.swal2-popup{align-self:start}div:where(.swal2-container).swal2-top>.swal2-popup{grid-column:2;place-self:start center}div:where(.swal2-container).swal2-top-end>.swal2-popup,div:where(.swal2-container).swal2-top-right>.swal2-popup{grid-column:3;place-self:start end}div:where(.swal2-container).swal2-center-start>.swal2-popup,div:where(.swal2-container).swal2-center-left>.swal2-popup{grid-row:2;align-self:center}div:where(.swal2-container).swal2-center>.swal2-popup{grid-column:2;grid-row:2;place-self:center center}div:where(.swal2-container).swal2-center-end>.swal2-popup,div:where(.swal2-container).swal2-center-right>.swal2-popup{grid-column:3;grid-row:2;place-self:center end}div:where(.swal2-container).swal2-bottom-start>.swal2-popup,div:where(.swal2-container).swal2-bottom-left>.swal2-popup{grid-column:1;grid-row:3;align-self:end}div:where(.swal2-container).swal2-bottom>.swal2-popup{grid-column:2;grid-row:3;place-self:end center}div:where(.swal2-container).swal2-bottom-end>.swal2-popup,div:where(.swal2-container).swal2-bottom-right>.swal2-popup{grid-column:3;grid-row:3;place-self:end end}div:where(.swal2-container).swal2-grow-row>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-column:1/4;width:100%}div:where(.swal2-container).swal2-grow-column>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-row:1/4;align-self:stretch}div:where(.swal2-container).swal2-no-transition{transition:none !important}div:where(.swal2-container)[popover]{width:auto;border:0}div:where(.swal2-container) div:where(.swal2-popup){display:none;position:relative;box-sizing:border-box;grid-template-columns:minmax(0, 100%);width:var(--swal2-width);max-width:100%;padding:var(--swal2-padding);border:var(--swal2-border);border-radius:var(--swal2-border-radius);background:var(--swal2-background);color:var(--swal2-color);font-family:inherit;font-size:1rem;container-name:swal2-popup}div:where(.swal2-container) div:where(.swal2-popup):focus{outline:none}div:where(.swal2-container) div:where(.swal2-popup).swal2-loading{overflow-y:hidden}div:where(.swal2-container) div:where(.swal2-popup).swal2-draggable{cursor:grab}div:where(.swal2-container) div:where(.swal2-popup).swal2-draggable div:where(.swal2-icon){cursor:grab}div:where(.swal2-container) div:where(.swal2-popup).swal2-dragging{cursor:grabbing}div:where(.swal2-container) div:where(.swal2-popup).swal2-dragging div:where(.swal2-icon){cursor:grabbing}div:where(.swal2-container) h2:where(.swal2-title){position:relative;max-width:100%;margin:0;padding:var(--swal2-title-padding);color:inherit;font-size:1.875em;font-weight:600;text-align:center;text-transform:none;word-wrap:break-word;cursor:initial}div:where(.swal2-container) div:where(.swal2-actions){display:flex;z-index:1;box-sizing:border-box;flex-wrap:wrap;align-items:center;justify-content:var(--swal2-actions-justify-content);width:var(--swal2-actions-width);margin:var(--swal2-actions-margin);padding:var(--swal2-actions-padding);border-radius:var(--swal2-actions-border-radius);background:var(--swal2-actions-background)}div:where(.swal2-container) div:where(.swal2-loader){display:none;align-items:center;justify-content:center;width:2.2em;height:2.2em;margin:0 1.875em;animation:swal2-rotate-loading 1.5s linear 0s infinite normal;border-width:.25em;border-style:solid;border-radius:100%;border-color:#2778c4 rgba(0,0,0,0) #2778c4 rgba(0,0,0,0)}div:where(.swal2-container) button:where(.swal2-styled){margin:.3125em;padding:.625em 1.1em;transition:var(--swal2-action-button-transition);border:none;box-shadow:0 0 0 3px rgba(0,0,0,0);font-weight:500}div:where(.swal2-container) button:where(.swal2-styled):not([disabled]){cursor:pointer}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm){border-radius:var(--swal2-confirm-button-border-radius);background:initial;background-color:var(--swal2-confirm-button-background-color);box-shadow:var(--swal2-confirm-button-box-shadow);color:var(--swal2-confirm-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm):hover{background-color:color-mix(in srgb, var(--swal2-confirm-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm):active{background-color:color-mix(in srgb, var(--swal2-confirm-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny){border-radius:var(--swal2-deny-button-border-radius);background:initial;background-color:var(--swal2-deny-button-background-color);box-shadow:var(--swal2-deny-button-box-shadow);color:var(--swal2-deny-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny):hover{background-color:color-mix(in srgb, var(--swal2-deny-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny):active{background-color:color-mix(in srgb, var(--swal2-deny-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel){border-radius:var(--swal2-cancel-button-border-radius);background:initial;background-color:var(--swal2-cancel-button-background-color);box-shadow:var(--swal2-cancel-button-box-shadow);color:var(--swal2-cancel-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel):hover{background-color:color-mix(in srgb, var(--swal2-cancel-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel):active{background-color:color-mix(in srgb, var(--swal2-cancel-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):focus-visible{outline:none;box-shadow:var(--swal2-action-button-focus-box-shadow)}div:where(.swal2-container) button:where(.swal2-styled)[disabled]:not(.swal2-loading){opacity:.4}div:where(.swal2-container) button:where(.swal2-styled)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-footer){margin:1em 0 0;padding:1em 1em 0;border-top:1px solid var(--swal2-footer-border-color);background:var(--swal2-footer-background);color:var(--swal2-footer-color);font-size:1em;text-align:center;cursor:initial}div:where(.swal2-container) .swal2-timer-progress-bar-container{position:absolute;right:0;bottom:0;left:0;grid-column:auto !important;overflow:hidden;border-bottom-right-radius:var(--swal2-border-radius);border-bottom-left-radius:var(--swal2-border-radius)}div:where(.swal2-container) div:where(.swal2-timer-progress-bar){width:100%;height:.25em;background:rgba(0,0,0,.2)}div:where(.swal2-container) img:where(.swal2-image){max-width:100%;margin:2em auto 1em;cursor:initial}div:where(.swal2-container) button:where(.swal2-close){position:var(--swal2-close-button-position);inset:var(--swal2-close-button-inset);z-index:2;align-items:center;justify-content:center;width:1.2em;height:1.2em;margin-top:0;margin-right:0;margin-bottom:-1.2em;padding:0;overflow:hidden;transition:var(--swal2-close-button-transition);border:none;border-radius:var(--swal2-border-radius);outline:var(--swal2-close-button-outline);background:rgba(0,0,0,0);color:var(--swal2-close-button-color);font-family:monospace;font-size:var(--swal2-close-button-font-size);cursor:pointer;justify-self:end}div:where(.swal2-container) button:where(.swal2-close):hover{transform:var(--swal2-close-button-hover-transform);background:rgba(0,0,0,0);color:#f27474}div:where(.swal2-container) button:where(.swal2-close):focus-visible{outline:none;box-shadow:var(--swal2-close-button-focus-box-shadow)}div:where(.swal2-container) button:where(.swal2-close)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-html-container){z-index:1;justify-content:center;margin:0;padding:var(--swal2-html-container-padding);overflow:auto;color:inherit;font-size:1.125em;font-weight:normal;line-height:normal;text-align:center;word-wrap:break-word;word-break:break-word;cursor:initial}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea),div:where(.swal2-container) select:where(.swal2-select),div:where(.swal2-container) div:where(.swal2-radio),div:where(.swal2-container) label:where(.swal2-checkbox){margin:1em 2em 3px}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea){box-sizing:border-box;width:auto;transition:var(--swal2-input-transition);border:var(--swal2-input-border);border-radius:var(--swal2-input-border-radius);background:var(--swal2-input-background);box-shadow:var(--swal2-input-box-shadow);color:inherit;font-size:1.125em}div:where(.swal2-container) input:where(.swal2-input).swal2-inputerror,div:where(.swal2-container) input:where(.swal2-file).swal2-inputerror,div:where(.swal2-container) textarea:where(.swal2-textarea).swal2-inputerror{border-color:#f27474 !important;box-shadow:0 0 2px #f27474 !important}div:where(.swal2-container) input:where(.swal2-input):hover,div:where(.swal2-container) input:where(.swal2-file):hover,div:where(.swal2-container) textarea:where(.swal2-textarea):hover{box-shadow:var(--swal2-input-hover-box-shadow)}div:where(.swal2-container) input:where(.swal2-input):focus,div:where(.swal2-container) input:where(.swal2-file):focus,div:where(.swal2-container) textarea:where(.swal2-textarea):focus{border:var(--swal2-input-focus-border);outline:none;box-shadow:var(--swal2-input-focus-box-shadow)}div:where(.swal2-container) input:where(.swal2-input)::placeholder,div:where(.swal2-container) input:where(.swal2-file)::placeholder,div:where(.swal2-container) textarea:where(.swal2-textarea)::placeholder{color:#ccc}div:where(.swal2-container) .swal2-range{margin:1em 2em 3px;background:var(--swal2-background)}div:where(.swal2-container) .swal2-range input{width:80%}div:where(.swal2-container) .swal2-range output{width:20%;color:inherit;font-weight:600;text-align:center}div:where(.swal2-container) .swal2-range input,div:where(.swal2-container) .swal2-range output{height:2.625em;padding:0;font-size:1.125em;line-height:2.625em}div:where(.swal2-container) .swal2-input{height:2.625em;padding:0 .75em}div:where(.swal2-container) .swal2-file{width:75%;margin-right:auto;margin-left:auto;background:var(--swal2-input-background);font-size:1.125em}div:where(.swal2-container) .swal2-textarea{height:6.75em;padding:.75em}div:where(.swal2-container) .swal2-select{min-width:50%;max-width:100%;padding:.375em .625em;background:var(--swal2-input-background);color:inherit;font-size:1.125em}div:where(.swal2-container) .swal2-radio,div:where(.swal2-container) .swal2-checkbox{align-items:center;justify-content:center;background:var(--swal2-background);color:inherit}div:where(.swal2-container) .swal2-radio label,div:where(.swal2-container) .swal2-checkbox label{margin:0 .6em;font-size:1.125em}div:where(.swal2-container) .swal2-radio input,div:where(.swal2-container) .swal2-checkbox input{flex-shrink:0;margin:0 .4em}div:where(.swal2-container) label:where(.swal2-input-label){display:flex;justify-content:center;margin:1em auto 0}div:where(.swal2-container) div:where(.swal2-validation-message){align-items:center;justify-content:center;margin:1em 0 0;padding:.625em;overflow:hidden;background:var(--swal2-validation-message-background);color:var(--swal2-validation-message-color);font-size:1em;font-weight:300}div:where(.swal2-container) div:where(.swal2-validation-message)::before{content:"!";display:inline-block;width:1.5em;min-width:1.5em;height:1.5em;margin:0 .625em;border-radius:50%;background-color:#f27474;color:#fff;font-weight:600;line-height:1.5em;text-align:center}div:where(.swal2-container) .swal2-progress-steps{flex-wrap:wrap;align-items:center;max-width:100%;margin:1.25em auto;padding:0;background:rgba(0,0,0,0);font-weight:600}div:where(.swal2-container) .swal2-progress-steps li{display:inline-block;position:relative}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step{z-index:20;flex-shrink:0;width:2em;height:2em;border-radius:2em;background:#2778c4;color:#fff;line-height:2em;text-align:center}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step{background:#2778c4}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step{background:var(--swal2-progress-step-background);color:#fff}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step-line{background:var(--swal2-progress-step-background)}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step-line{z-index:10;flex-shrink:0;width:2.5em;height:.4em;margin:0 -1px;background:#2778c4}div:where(.swal2-icon){position:relative;box-sizing:content-box;justify-content:center;width:5em;height:5em;margin:2.5em auto .6em;zoom:var(--swal2-icon-zoom);border:.25em solid rgba(0,0,0,0);border-radius:50%;border-color:#000;font-family:inherit;line-height:5em;cursor:default;user-select:none}div:where(.swal2-icon) .swal2-icon-content{display:flex;align-items:center;font-size:3.75em}div:where(.swal2-icon).swal2-error{border-color:#f27474;color:#f27474}div:where(.swal2-icon).swal2-error .swal2-x-mark{position:relative;flex-grow:1}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line]{display:block;position:absolute;top:2.3125em;width:2.9375em;height:.3125em;border-radius:.125em;background-color:#f27474}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=left]{left:1.0625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=right]{right:1em;transform:rotate(-45deg)}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-error.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-error.swal2-icon-show .swal2-x-mark{animation:swal2-animate-error-x-mark .5s}}div:where(.swal2-icon).swal2-warning{border-color:#f8bb86;color:#f8bb86}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-warning.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-warning.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .5s}}div:where(.swal2-icon).swal2-info{border-color:#3fc3ee;color:#3fc3ee}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-info.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-info.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .8s}}div:where(.swal2-icon).swal2-question{border-color:#87adbd;color:#87adbd}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-question.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-question.swal2-icon-show .swal2-icon-content{animation:swal2-animate-question-mark .8s}}div:where(.swal2-icon).swal2-success{border-color:#a5dc86;color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line]{position:absolute;width:3.75em;height:7.5em;border-radius:50%}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.4375em;left:-2.0635em;transform:rotate(-45deg);transform-origin:3.75em 3.75em;border-radius:7.5em 0 0 7.5em}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.6875em;left:1.875em;transform:rotate(-45deg);transform-origin:0 3.75em;border-radius:0 7.5em 7.5em 0}div:where(.swal2-icon).swal2-success .swal2-success-ring{position:absolute;z-index:2;top:-0.25em;left:-0.25em;box-sizing:content-box;width:100%;height:100%;border:.25em solid rgba(165,220,134,.3);border-radius:50%}div:where(.swal2-icon).swal2-success .swal2-success-fix{position:absolute;z-index:1;top:.5em;left:1.625em;width:.4375em;height:5.625em;transform:rotate(-45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line]{display:block;position:absolute;z-index:2;height:.3125em;border-radius:.125em;background-color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=tip]{top:2.875em;left:.8125em;width:1.5625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=long]{top:2.375em;right:.5em;width:2.9375em;transform:rotate(-45deg)}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-animate-success-line-tip .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-animate-success-line-long .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-circular-line-right{animation:swal2-rotate-success-circular-line 4.25s ease-in}}[class^=swal2]{-webkit-tap-highlight-color:rgba(0,0,0,0)}.swal2-show{animation:var(--swal2-show-animation)}.swal2-hide{animation:var(--swal2-hide-animation)}.swal2-noanimation{transition:none}.swal2-scrollbar-measure{position:absolute;top:-9999px;width:50px;height:50px;overflow:scroll}.swal2-rtl .swal2-close{margin-right:initial;margin-left:0}.swal2-rtl .swal2-timer-progress-bar{right:0;left:auto}.swal2-toast{box-sizing:border-box;grid-column:1/4 !important;grid-row:1/4 !important;grid-template-columns:min-content auto min-content;padding:1em;overflow-y:hidden;border:var(--swal2-toast-border);background:var(--swal2-background);box-shadow:var(--swal2-toast-box-shadow);pointer-events:all}.swal2-toast>*{grid-column:2}.swal2-toast h2:where(.swal2-title){margin:.5em 1em;padding:0;font-size:1em;text-align:initial}.swal2-toast .swal2-loading{justify-content:center}.swal2-toast input:where(.swal2-input){height:2em;margin:.5em;font-size:1em}.swal2-toast .swal2-validation-message{font-size:1em}.swal2-toast div:where(.swal2-footer){margin:.5em 0 0;padding:.5em 0 0;font-size:.8em}.swal2-toast button:where(.swal2-close){grid-column:3/3;grid-row:1/99;align-self:center;width:.8em;height:.8em;margin:0;font-size:2em}.swal2-toast div:where(.swal2-html-container){margin:.5em 1em;padding:0;overflow:initial;font-size:1em;text-align:initial}.swal2-toast div:where(.swal2-html-container):empty{padding:0}.swal2-toast .swal2-loader{grid-column:1;grid-row:1/99;align-self:center;width:2em;height:2em;margin:.25em}.swal2-toast .swal2-icon{grid-column:1;grid-row:1/99;align-self:center;width:2em;min-width:2em;height:2em;margin:0 .5em 0 0}.swal2-toast .swal2-icon .swal2-icon-content{display:flex;align-items:center;font-size:1.8em;font-weight:bold}.swal2-toast .swal2-icon.swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line]{top:.875em;width:1.375em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left]{left:.3125em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right]{right:.3125em}.swal2-toast div:where(.swal2-actions){justify-content:flex-start;height:auto;margin:0;margin-top:.5em;padding:0 .5em}.swal2-toast button:where(.swal2-styled){margin:.25em .5em;padding:.4em .6em;font-size:1em}.swal2-toast .swal2-success{border-color:#a5dc86}.swal2-toast .swal2-success [class^=swal2-success-circular-line]{position:absolute;width:1.6em;height:3em;border-radius:50%}.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.8em;left:-0.5em;transform:rotate(-45deg);transform-origin:2em 2em;border-radius:4em 0 0 4em}.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.25em;left:.9375em;transform-origin:0 1.5em;border-radius:0 4em 4em 0}.swal2-toast .swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-toast .swal2-success .swal2-success-fix{top:0;left:.4375em;width:.4375em;height:2.6875em}.swal2-toast .swal2-success [class^=swal2-success-line]{height:.3125em}.swal2-toast .swal2-success [class^=swal2-success-line][class$=tip]{top:1.125em;left:.1875em;width:.75em}.swal2-toast .swal2-success [class^=swal2-success-line][class$=long]{top:.9375em;right:.1875em;width:1.375em}@container swal2-popup style(--swal2-icon-animations:true){.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-toast-animate-success-line-tip .75s}.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-toast-animate-success-line-long .75s}}.swal2-toast.swal2-show{animation:var(--swal2-toast-show-animation)}.swal2-toast.swal2-hide{animation:var(--swal2-toast-hide-animation)}@keyframes swal2-show{0%{transform:scale(0.7)}45%{transform:scale(1.05)}80%{transform:scale(0.95)}100%{transform:scale(1)}}@keyframes swal2-hide{0%{transform:scale(1);opacity:1}100%{transform:scale(0.5);opacity:0}}@keyframes swal2-animate-success-line-tip{0%{top:1.1875em;left:.0625em;width:0}54%{top:1.0625em;left:.125em;width:0}70%{top:2.1875em;left:-0.375em;width:3.125em}84%{top:3em;left:1.3125em;width:1.0625em}100%{top:2.8125em;left:.8125em;width:1.5625em}}@keyframes swal2-animate-success-line-long{0%{top:3.375em;right:2.875em;width:0}65%{top:3.375em;right:2.875em;width:0}84%{top:2.1875em;right:0;width:3.4375em}100%{top:2.375em;right:.5em;width:2.9375em}}@keyframes swal2-rotate-success-circular-line{0%{transform:rotate(-45deg)}5%{transform:rotate(-45deg)}12%{transform:rotate(-405deg)}100%{transform:rotate(-405deg)}}@keyframes swal2-animate-error-x-mark{0%{margin-top:1.625em;transform:scale(0.4);opacity:0}50%{margin-top:1.625em;transform:scale(0.4);opacity:0}80%{margin-top:-0.375em;transform:scale(1.15)}100%{margin-top:0;transform:scale(1);opacity:1}}@keyframes swal2-animate-error-icon{0%{transform:rotateX(100deg);opacity:0}100%{transform:rotateX(0deg);opacity:1}}@keyframes swal2-rotate-loading{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes swal2-animate-question-mark{0%{transform:rotateY(-360deg)}100%{transform:rotateY(0)}}@keyframes swal2-animate-i-mark{0%{transform:rotateZ(45deg);opacity:0}25%{transform:rotateZ(-25deg);opacity:.4}50%{transform:rotateZ(15deg);opacity:.8}75%{transform:rotateZ(-5deg);opacity:1}100%{transform:rotateX(0);opacity:1}}@keyframes swal2-toast-show{0%{transform:translateY(-0.625em) rotateZ(2deg)}33%{transform:translateY(0) rotateZ(-2deg)}66%{transform:translateY(0.3125em) rotateZ(2deg)}100%{transform:translateY(0) rotateZ(0deg)}}@keyframes swal2-toast-hide{100%{transform:rotateZ(1deg);opacity:0}}@keyframes swal2-toast-animate-success-line-tip{0%{top:.5625em;left:.0625em;width:0}54%{top:.125em;left:.125em;width:0}70%{top:.625em;left:-0.25em;width:1.625em}84%{top:1.0625em;left:.75em;width:.5em}100%{top:1.125em;left:.1875em;width:.75em}}@keyframes swal2-toast-animate-success-line-long{0%{top:1.625em;right:1.375em;width:0}65%{top:1.25em;right:.9375em;width:0}84%{top:.9375em;right:0;width:1.125em}100%{top:.9375em;right:.1875em;width:1.375em}}');const rg=(e,t,r,o)=>{const[c,s]=d.useState([]),[m,l]=d.useState([]),[u,se]=d.useState(!1),ie=new URLSearchParams(location.search),q=oe(Q=>Q.payload.payloadData),g=oe(Q=>{var H,he;return(he=(H=Q==null?void 0:Q.userManagement)==null?void 0:H.taskData)==null?void 0:he.ATTRIBUTE_2}),V=ie.get("RequestType");return d.useEffect(()=>{let Q={decisionTableId:null,decisionTableName:"MDG_MAT_DYN_BUTTON_CONFIG",version:"v3",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_DYN_BTN_MODULE_NAME":"Material","MDG_CONDITIONS.MDG_MAT_DYN_BTN_REQUEST_TYPE":V||g||(q==null?void 0:q.RequestType)}],systemFilters:null,systemOrders:null,filterString:null};const H=J=>{var f,z;J.statusCode===200&&s((z=(f=J==null?void 0:J.data)==null?void 0:f.result[0])==null?void 0:z.MDG_MAT_DYN_BUTTON_CONFIG)},he=J=>{console.error(J)},ne=t.environment==="localhost"?`/${r}/rest/v1/invoke-rules`:`/${r}/v1/invoke-rules`;Xe(ne,"post",H,he,Q)},[e]),d.useEffect(()=>{const Q=Bl(mr.CURRENT_TASK,!0,{}),H=(Q==null?void 0:Q.taskDesc)||(e==null?void 0:e.taskDesc),ne=c.filter(J=>J.MDG_MAT_DYN_BTN_TASK_NAME===H).sort((J,f)=>{const z=ch[J.MDG_MAT_DYN_BTN_ACTION_TYPE]??999,j=ch[f.MDG_MAT_DYN_BTN_ACTION_TYPE]??999;return z-j});l(ne),(ne.find(J=>J.MDG_MAT_DYN_BTN_BUTTON_NAME===o.SEND_BACK)||ne.find(J=>J.MDG_MAT_DYN_BTN_BUTTON_NAME===o.CORRECTION))&&se(!0)},[c]),{filteredButtons:m,showWfLevels:u}},fA=Jh(()=>({customTabs:{"& .MuiTabs-scroller":{overflowX:"auto !important",overflowY:"hidden !important"}}})),gA=e=>{var Qr,Zr,el,tl,sl,ol,nl,rl,ll,il,al,cl,dl,ul,hl,fl,gl,pl;const t=fA(),{customError:r}=sn(),o=Ro(),{getDynamicWorkflowDT:c}=Ha(),{fetchMaterialFieldConfig:s}=Da(),{getNextDisplayDataForCreate:m}=Gi(),{fetchValuationClassData:l}=lf(),u=oe(i=>i.payload.payloadData),se=u==null?void 0:u.RequestType,ie=oe(i=>i.request.salesOrgDTData),q=oe(i=>i.applicationConfig),g=oe(i=>i.paginationData),V=oe(i=>i.payload),Q=oe(i=>i.request.requestHeader),H=oe(i=>i.request.materialRows),he=oe(i=>i.payload.payloadData),ne=oe(i=>{var h;return((h=i.AllDropDown)==null?void 0:h.dropDown)||{}}),J=oe(i=>i.tabsData.allTabsData);oe(i=>i.userManagement.userData);let f=oe(i=>i.userManagement.roles),z=oe(i=>i.userManagement.taskData);const j=oe(i=>i.tabsData.allMaterialFieldConfigDT),p=tn(),T=new URLSearchParams(p.search),v=T.get("reqBench"),C=T.get("RequestId"),[S,L]=d.useState(!1),[A,O]=d.useState(!1),[k,U]=d.useState(0),[x,N]=d.useState(null),[D,I]=d.useState(null),ge="Basic Data",[ee,Ue]=d.useState([ge]),[M,Se]=d.useState({data:{},isVisible:!1}),[ce,$]=d.useState(H||[]),qe=oe(i=>i.selectedSections.selectedSections),[F,Ke]=d.useState(!!(ce!=null&&ce.length)),[Us,Wt]=d.useState(!1),[$s,Fs]=d.useState(!1),[ss,io]=d.useState(""),{fetchTabSpecificData:Ws}=cf(),[ks,Qt]=d.useState([]),[fs,Ht]=d.useState(0),[ns,Zt]=d.useState(null),[xs,lt]=d.useState(!1),[He,Rt]=d.useState(!0),[Ys,Bt]=d.useState(ce.length+1),[Me,it]=d.useState(0),[Ut,Ns]=d.useState(H.length>0),[ds,rs]=d.useState({}),[at,yt]=d.useState({}),[ws,Hs]=d.useState(0),[os,ls]=d.useState([]),[ao,Ye]=d.useState({}),[Mt,gs]=d.useState([]),[Lt,ps]=d.useState(!1),[Js,Bs]=d.useState(""),[st,Ts]=d.useState("Basic Data"),[to,St]=d.useState(!1);let es={id:0,salesOrg:null,dc:{value:null,options:[]},plant:{value:null,options:[]},sloc:{value:null,options:[]},warehouse:{value:null,options:[]},mrpProfile:null};const[ot,Dt]=d.useState([es]),[Rs,ae]=d.useState(!1),[$e,me]=d.useState(null),[Ne,Ve]=d.useState("yes"),[Oe,ct]=d.useState([]),[ke,Vt]=d.useState(null),Es=(Qr=V==null?void 0:V[ke])==null?void 0:Qr.headerData,[Pt,je]=d.useState("success"),[Pe,_t]=d.useState(!1),[jt,_s]=d.useState([]),[Ft,te]=d.useState(""),[xe,B]=d.useState(""),[de,w]=d.useState(""),Te=oe(i=>i.tabsData.matViews),{checkValidation:We}=gE(V,j,ee),{t:le}=bn(),dt=[{region:"US",temp:"MIDDLE EAST HUB"},{region:"US",temp:"SOUTHERN HUB"},{region:"EUR",temp:"NORTH HUB"},{region:"EUR",temp:"CENTRAL HUB"},{region:"EUR",temp:"WEST HUB"}],[ze,Qe]=d.useState(null),[Ct,_e]=d.useState(""),[ut,ys]=d.useState(""),Xs=d.useRef(ot),[uo,us]=d.useState(!1),co=(Zr=V==null?void 0:V[ke])==null?void 0:Zr.payloadData,{fetchDataAndDispatch:Po}=Bi(),Qs=["Sales Org","Plant","Distribution Channel","Storage Location","Warehouse"],[Ot,Gs]=d.useState({}),[Zs,_o]=d.useState(!1),[Fo,Io]=d.useState(0),[ho,is]=d.useState({"Material No":!1}),{getContryBasedOnPlant:Wo}=df({doAjax:Xe,customError:r,fetchDataAndDispatch:Po,destination_MaterialMgmt:Re}),so=["BOM","Source List","PIR"],[qo,jo]=d.useState([]),{filteredButtons:zo,showWfLevels:Yo}=rg(z,q,yo,go),Xo=Cr(zo,[To.HANDLE_SUBMIT_FOR_APPROVAL,To.HANDLE_SAP_SYNDICATION,To.HANDLE_SUBMIT_FOR_REVIEW]);d.useEffect(()=>{var i,h,y,G,Y,Z,K,ue,Le,Ce,be,Ie,nt,qt;if($(H),Ns((H==null?void 0:H.length)>0),(H==null?void 0:H.length)>0&&C){Vt((i=H==null?void 0:H[0])==null?void 0:i.id),w((h=H==null?void 0:H[0])==null?void 0:h.materialNumber),W((G=(y=H==null?void 0:H[0])==null?void 0:y.materialType)==null?void 0:G.code),it(0),Ts(P.BASIC_DATA),Ue((Z=(Y=H==null?void 0:H[0])==null?void 0:Y.views)!=null&&Z.length?(K=H==null?void 0:H[0])==null?void 0:K.views:[ge]);const Yt=Hh(V),Vs=Bh(Yt);let ro=JSON.parse(JSON.stringify(Vs));o(Gh(ro)),o(kl({keyName:"selectedMaterialID",data:(ue=H==null?void 0:H[0])==null?void 0:ue.id})),(be=(Ce=V==null?void 0:V[(Le=H==null?void 0:H[0])==null?void 0:Le.id])==null?void 0:Ce.Tochildrequestheaderdata)!=null&&be.ChildRequestId&&o(kl({keyName:"childRequestId",data:(qt=(nt=V==null?void 0:V[(Ie=H==null?void 0:H[0])==null?void 0:Ie.id])==null?void 0:nt.Tochildrequestheaderdata)==null?void 0:qt.ChildRequestId}))}},[H]),d.useEffect(()=>{var i,h,y;(i=H==null?void 0:H[0])!=null&&i.materialType&&(Yn((h=H==null?void 0:H[0])==null?void 0:h.materialType),Ae({row:H[0]}),sr(H)&&(Rt(!1),Ke(!1))),H!=null&&H.length&&Hs((y=H==null?void 0:H.at(-1))==null?void 0:y.lineNumber),o(xo({keyName:"VarOrdUn",data:_p}))},[]),d.useEffect(()=>{const i=async()=>{var h,y;try{const G=await c(se,u==null?void 0:u.Region,"",(y=(h=V[ke])==null?void 0:h.Tochildrequestheaderdata)==null?void 0:y.MaterialGroupType,z==null?void 0:z.ATTRIBUTE_3);jo(G)}catch(G){r(G)}};se&&(u!=null&&u.Region)&&ke&&(z!=null&&z.ATTRIBUTE_3)&&i()},[se,u==null?void 0:u.Region,ke,z==null?void 0:z.ATTRIBUTE_3]),d.useEffect(()=>{Ne==="no"&&(Gs({}),me(null),Zt(null))},[Ne]),d.useEffect(()=>{var i,h,y,G,Y,Z,K,ue,Le,Ce,be,Ie,nt,qt;ke&&(J!=null&&J[P.BASIC_DATA])&&(i=V[ke])!=null&&i.headerData.refMaterialData&&!((G=(y=(h=V[ke])==null?void 0:h.payloadData)==null?void 0:y["Basic Data"])!=null&&G.basic)&&pe((Z=(Y=V[ke])==null?void 0:Y.headerData)==null?void 0:Z.refMaterialData),(Ce=(Le=(ue=(K=V[ke])==null?void 0:K.payloadData)==null?void 0:ue[P.CLASSIFICATION])==null?void 0:Le.basic)!=null&&Ce.Classtype&&Ip((qt=(nt=(Ie=(be=V[ke])==null?void 0:be.payloadData)==null?void 0:Ie[P.CLASSIFICATION])==null?void 0:nt.basic)==null?void 0:qt.Classtype,o)},[ke,J]),d.useEffect(()=>{(ce==null?void 0:ce.length)===0&&Ke(!1)},[ce]),d.useEffect(()=>{$e&&(ht($e==null?void 0:$e.code,"extended"),Gs(i=>({...i,[X.SALES_ORG]:null})))},[$e]),d.useEffect(()=>{var i,h,y,G,Y,Z,K,ue;if(ke&&((h=(i=V[ke])==null?void 0:i.headerData)!=null&&h.materialType)){let Le=(y=V[ke])==null?void 0:y.headerData;if(Te&&Te[(G=Le==null?void 0:Le.materialType)==null?void 0:G.code]&&((Y=Le==null?void 0:Le.views)==null?void 0:Y.length)<2){const Ce=(u==null?void 0:u.Region)==="EUR"?((K=Te[(Z=Le==null?void 0:Le.materialType)==null?void 0:Z.code])==null?void 0:K.filter(be=>be!==P.WAREHOUSE))||[]:Te[(ue=Le==null?void 0:Le.materialType)==null?void 0:ue.code]||[];gs(Ce),Ue(Ce),re({id:ke,field:"views",value:Ce})}}},[Te,ke,(tl=(el=V[ke])==null?void 0:el.headerData)==null?void 0:tl.materialType]),d.useEffect(()=>{Ot[X.SALES_ORG]&&(Nt(),Gs(i=>({...i,[X.DIST_CHNL]:null,[X.PLANT]:null})))},[Ot[X.SALES_ORG]]);const pe=i=>{var y,G,Y,Z;(Y=Object.keys((G=(y=i==null?void 0:i.copyPayload)==null?void 0:y.payloadData["Basic Data"])==null?void 0:G.basic))==null||Y.forEach(K=>{var Le,Ce,be;let ue=K==="Division"?u==null?void 0:u.Division:vp(K,(be=(Ce=(Le=i==null?void 0:i.copyPayload)==null?void 0:Le.payloadData["Basic Data"])==null?void 0:Ce.basic)==null?void 0:be[K],J["Basic Data"]);o(Ku({materialID:ke,viewID:"Basic Data",itemID:"basic",keyName:K,data:ue}))});let h=(Z=i==null?void 0:i.copyPayload)==null?void 0:Z.unitsOfMeasureData;if(h!=null&&h.length){let K=[];h==null||h.forEach(ue=>{K.push({...ue,id:(ue==null?void 0:ue.id)||K.length+1})}),o(Ju({materialID:ke,data:K}))}},ht=(i,h)=>{const y=Y=>{is(Z=>({...Z,"Sales Org":!1})),(Y==null?void 0:Y.statusCode)===Jt.STATUS_200&&yt(h==="notExtended"?Z=>({...Z,"Sales Org":Y.body}):Z=>({...Z,"Sales Org":(Y==null?void 0:Y.body.length)>0?Y.body:[]}))},G=()=>{is(Y=>({...Y,"Sales Org":!1}))};is(Y=>({...Y,"Sales Org":!0})),Xe(`/${Re}/data/${h==="notExtended"?"getSalesOrgNotExtended":"getSalesOrgExtended"}?materialNo=${i}&region=${u==null?void 0:u.Region}`,"get",y,G)},ft=(i,h,y)=>{is(K=>({...K,Plant:!0}));const G=K=>{is(ue=>({...ue,Plant:!1})),(K==null?void 0:K.statusCode)===Jt.STATUS_200&&yt(h==="notExtended"?ue=>({...ue,Plant:K.body}):ue=>({...ue,Plant:(K==null?void 0:K.body.length)>0?K.body:[]}))},Y=()=>{is(K=>({...K,Plant:!1}))},Z=y?`&salesOrg=${y.code}`:"";Xe(`/${Re}/data/${h==="notExtended"?"getPlantNotExtended":"getPlantExtended"}?materialNo=${i}&region=${u==null?void 0:u.Region}${Z}`,"get",G,Y)},At=(i,h,y)=>{is(K=>({...K,Warehouse:!0}));const G=K=>{is(ue=>({...ue,Warehouse:!1})),(K==null?void 0:K.statusCode)===Jt.STATUS_200&&yt(h==="notExtended"?ue=>({...ue,Warehouse:K.body}):ue=>({...ue,Warehouse:(K==null?void 0:K.body.length)>0?K.body:[]}))},Y=()=>{is(K=>({...K,Warehouse:!1}))},Z=y?`&plant=${y.code}`:"";Xe(`/${Re}/data/${h==="notExtended"?"getWarehouseNotExtended":"getWarehouseExtended"}?materialNo=${i}&region=${u==null?void 0:u.Region}${Z}`,"get",G,Y)},ve=(i,h,y)=>{is(Z=>({...Z,"Storage Location":!0}));const G=Z=>{is(K=>({...K,"Storage Location":!1})),(Z==null?void 0:Z.statusCode)===Jt.STATUS_200&&yt(K=>{var ue;return{...K,[(ue=X)==null?void 0:ue.STORAGE_LOC]:Z.body||[]}})},Y=Z=>{r(Z),is(K=>({...K,"Storage Location":!1}))};Xe(`/${Re}/data/getStorageLocationExtended?plant=${h==null?void 0:h.code}&materialNo=${i}&region=${u==null?void 0:u.Region}&salesOrg=${y==null?void 0:y.code}`,"get",G,Y)},Nt=()=>{var y;is(G=>({...G,"Distribution Channel":!0}));const i=G=>{is(Y=>({...Y,"Distribution Channel":!1})),(G==null?void 0:G.statusCode)===Jt.STATUS_200&&yt(Y=>{var Z;return{...Y,[(Z=X)==null?void 0:Z.DIST_CHNL]:G.body&&(G==null?void 0:G.body)}})},h=G=>{r(G),is(Y=>({...Y,"Distribution Channel":!1}))};Xe(`/${Re}/data/getDistributionChannelExtended?materialNo=${$e==null?void 0:$e.code}&salesOrg=${(y=Ot[X.SALES_ORG])==null?void 0:y.code}`,"get",i,h)};d.useEffect(()=>{["Mrp Profile"].forEach(Vo),(H==null?void 0:H.length)===0&&(se===b.CREATE||se===b.CREATE_WITH_UPLOAD)&&ae(!0),De()},[]),d.useEffect(()=>{var h,y,G,Y,Z,K,ue,Le,Ce,be,Ie,nt,qt,Yt,Vs,ro,cs,vo,ln,an,wn,Rn;Xs.current=ot,ot.some(Ko=>{var _n,In,vn;return((_n=Ko==null?void 0:Ko.salesOrg)==null?void 0:_n.code)&&!((vn=(In=Ko==null?void 0:Ko.dc)==null?void 0:In.value)!=null&&vn.code)})?us(!1):(y=(h=ot[0])==null?void 0:h.salesOrg)!=null&&y.code&&((Z=(Y=(G=ot[0])==null?void 0:G.dc)==null?void 0:Y.value)!=null&&Z.code)&&((Le=(ue=(K=ot[0])==null?void 0:K.plant)==null?void 0:ue.value)!=null&&Le.code)&&((Ie=(be=(Ce=ot[0])==null?void 0:Ce.sloc)==null?void 0:be.value)!=null&&Ie.code||!((Vs=(qt=(nt=V[ke])==null?void 0:nt.headerData)==null?void 0:qt.views)!=null&&Vs.includes((Yt=P)==null?void 0:Yt.STORAGE)))&&((vo=(cs=(ro=ot[0])==null?void 0:ro.warehouse)==null?void 0:cs.value)!=null&&vo.code||(u==null?void 0:u.Region)==="EUR"||!((Rn=(an=(ln=V[ke])==null?void 0:ln.headerData)==null?void 0:an.views)!=null&&Rn.includes((wn=P)==null?void 0:wn.WAREHOUSE)))?us(!0):us(!1)},[ot]),d.useEffect(()=>{Ke(!0),Rt(!0)},[(sl=V[ke])==null?void 0:sl.headerData,(ol=V[ke])==null?void 0:ol.payloadData]),d.useEffect(()=>{var h;let i=(h=V[ke])==null?void 0:h.headerData;(i!=null&&i.Bom||i!=null&&i.sourceList||i!=null&&i.PIR)&&$t(i)},[(rl=(nl=V[ke])==null?void 0:nl.headerData)==null?void 0:rl.Bom,(il=(ll=V[ke])==null?void 0:ll.headerData)==null?void 0:il.sourceList,(cl=(al=V[ke])==null?void 0:al.headerData)==null?void 0:cl.PIR]);const $t=i=>{var Y,Z;const h={Bom:"BOM",sourceList:"Source List",PIR:"PIR"},y=((Z=(Y=Object.keys(h))==null?void 0:Y.filter(K=>i==null?void 0:i[K]))==null?void 0:Z.map(K=>h[K]))||[],G=ee==null?void 0:ee.filter(K=>!(so!=null&&so.includes(K)));Ue([...G,...y]),re({id:ke,field:xr.VIEWS,value:[...G,...y]})},kt=(i,h="",y)=>new Promise((G,Y)=>{var be;const Z=[{materialNo:i,requestNo:h||(Q==null?void 0:Q.requestId)}],K=Ie=>{var nt;(nt=Ie==null?void 0:Ie.body)!=null&&nt.length?(_t(!0),te(`Duplicate material number ${Ie.body[0].split("$^$")[0]} (${Ie.body[0].split("$^$")[1]})`),je("error"),G(!0)):G(!1)},ue=Ie=>{r(Ie),G(!1)};let Le=0;Object.keys(V).forEach((Ie,nt)=>{var qt,Yt;(Ie.includes("-")||/\d/.test(Ie))&&((Yt=(qt=V[Ie])==null?void 0:qt.headerData)==null?void 0:Yt.materialNumber)===i&&Le++});let Ce=0;Object.keys(V).forEach(Ie=>{var nt,qt;(Ie.includes("-")||/\d/.test(Ie))&&((qt=(nt=V[Ie])==null?void 0:nt.headerData)==null?void 0:qt.globalMaterialDescription)===y&&Ce++}),Le>1?(_t(!0),te(`${dn.DUPLICATE_MATERIAL}${i}`),je("error"),G(!0)):Ce>1?(_t(!0),te(`${dn.DUPLICATE_MATERIAL_DESCRIPTION}${y}`),je("error"),G(!0)):Xe(`/${Re}${(be=Fe.MASS_ACTION)==null?void 0:be.MAT_NO_DUPLICATE_CHECK}`,"post",K,ue,Z)}),as=async()=>{let i=[...ce],h=!0;return _e(!0),ys(Ph.VALIDATING_MATS),new Promise(async(y,G)=>{for(let Z=0;Z<(ce==null?void 0:ce.length);Z++){const K=ce[Z],{missingFields:ue,viewType:Le,isValid:Ce,plant:be=[]}=We(K.id,(K==null?void 0:K.orgData)||[],!1,!1,!1);if(_s(be),Ce){let Ie=!1;Ce&&(!C||K!=null&&K.isMatNoChanged)&&(Ie=await kt(K.materialNumber,C,K==null?void 0:K.globalMaterialDescription)),Ie&&(h=!1),i=i==null?void 0:i.map(nt=>nt.id===K.id?{...nt,validated:!Ie}:nt),o(Oo(i))}else{if(h=!1,i=i.map(Ie=>Ie.id===K.id?{...Ie,validated:!1}:Ie),o(Oo(i)),(ue==null?void 0:ue.length)>0){if(je("error"),typeof ue=="object"&&!Array.isArray(ue)){const Ie=Object.entries(ue).map(([nt,qt])=>`Combination ${nt}: ${qt.join(", ")}`);te(`Line No ${K.lineNumber} : Please fill all the Mandatory fields in ${Le||""}: ${Ie.join(" | ")}`)}else te(`Line No ${K.lineNumber} : Please fill all the Mandatory fields in ${Le||""}: ${ue.join(", ")}`);_t(!0)}break}}h?y(!0):G(),_e(!1);const Y=sr(i);Ke(!Y),Rt(!Y)})},qs=i=>{var h,y;if(i){let G=JSON.parse(JSON.stringify(((y=(h=V==null?void 0:V[ke])==null?void 0:h.headerData)==null?void 0:y.calledMrpCodes)||[]))||[];i.forEach((Z,K)=>{var ue,Le,Ce,be,Ie,nt,qt,Yt,Vs;(ue=Z==null?void 0:Z.mrpProfile)!=null&&ue.code&&!((Ie=(Ce=(Le=V==null?void 0:V[ke])==null?void 0:Le.headerData)==null?void 0:Ce.calledMrpCodes)!=null&&Ie.includes((be=Z==null?void 0:Z.mrpProfile)==null?void 0:be.code))&&(gt((qt=(nt=Z==null?void 0:Z.plant)==null?void 0:nt.value)==null?void 0:qt.code,(Yt=Z==null?void 0:Z.mrpProfile)==null?void 0:Yt.code),G.push((Vs=Z==null?void 0:Z.mrpProfile)==null?void 0:Vs.code))}),o(Bn({materialID:ke,keyName:"calledMrpCodes",data:G}));const Y=ce==null?void 0:ce.map(Z=>Z.id===ke?{...Z,calledMrpCodes:G}:Z);o(Oo(Y))}},gt=(i,h,y)=>{var K;const G={mrpProfile:h},Y=ue=>{ue.body[0]&&Object.keys(ue==null?void 0:ue.body[0]).filter(Ce=>ue==null?void 0:ue.body[0][Ce]).forEach(Ce=>{Gt(i,Ce,ue==null?void 0:ue.body[0][Ce],P.MRP)})},Z=ue=>{r(ue)};Xe(`/${Re}${(K=Fe.MASS_ACTION)==null?void 0:K.MRP_DEFAULT_VALUES}`,"post",Y,Z,G)},Gt=(i,h,y,G)=>{o(Ku({materialID:ke||"",keyName:h||"",data:y??null,viewID:G,itemID:i}))},De=()=>{let i={decisionTableId:null,decisionTableName:"MDG_MAT_REG_SORG_PLNT_CNTRY_STORE_WH_MAPPING",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":(u==null?void 0:u.Region)||Uo.US}],systemFilters:null,systemOrders:null,filterString:null};const h=G=>{var Y,Z;if(G.statusCode===Jt.STATUS_200){let K=(Z=(Y=G==null?void 0:G.data)==null?void 0:Y.result[0])==null?void 0:Z.MDG_MAT_REG_SORG_PLNT_CNTRY_STORE_WH_MAPPING;B(K);let ue=[];K==null||K.map(Ce=>{let be={};be.code=Ce.MDG_MAT_SALES_ORG,be.desc=Ce.MDG_MAT_SALES_ORG_DESC,ue.push(be)});const Le=Object.values(ue.reduce((Ce,be)=>{const Ie=`${be.code}-${be.desc}`;return Ce[Ie]=be,Ce},{}));yt(Ce=>({...Ce,"Sales Organization":Le==null?void 0:Le.sort((be,Ie)=>be.code-Ie.code)}))}},y=G=>{r(G)};q.environment==="localhost"?Xe(`/${yo}${Fe.INVOKE_RULES.LOCAL}`,"post",h,y,i):Xe(`/${yo}${Fe.INVOKE_RULES.PROD}`,"post",h,y,i)},pt=(i,h,y,G)=>{var Z;let Y=JSON.parse(JSON.stringify(Xs.current));if(i==="Sales Organization"){let K=(Z=xe==null?void 0:xe.filter(ue=>ue.MDG_MAT_SALES_ORG===(h==null?void 0:h.code)))==null?void 0:Z.map(ue=>{let Le={};return Le.code=ue.MDG_MAT_PLANT,Le.desc=ue.MDG_MAT_PLANT_DESC,Le});K=K==null?void 0:K.filter((ue,Le,Ce)=>Le===Ce.findIndex(be=>be.code===ue.code)),Y[y].plant.options=K==null?void 0:K.sort((ue,Le)=>ue.code-Le.code),Y[y].plant.value=null,Y[y].dc.value=null,Y[y].sloc.value=null,Y[y].salesOrg=h,Y[y].warehouse.value=null}else if(i==="plant"){let K=xe==null?void 0:xe.filter(Ce=>{var be;return Ce.MDG_MAT_SALES_ORG===((be=G==null?void 0:G.salesOrg)==null?void 0:be.code)&&Ce.MDG_MAT_PLANT===(h==null?void 0:h.code)}),ue=K==null?void 0:K.map(Ce=>{let be={};if(Ce.MDG_MAT_STORAGE_LOCATION)return be.code=Ce.MDG_MAT_STORAGE_LOCATION,be.desc=Ce.MDG_MAT_STORE_LOC_DESC,be}).filter(Boolean),Le=K==null?void 0:K.map(Ce=>Ce.MDG_MAT_WAREHOUSE?{code:Ce.MDG_MAT_WAREHOUSE,desc:Ce.MDG_MAT_WAREHOUSE_DESC}:null).filter(Boolean);Y[y].plant.value=h,Y[y].sloc.value=null,Y[y].warehouse.value=null,Y[y].sloc.options=ue==null?void 0:ue.filter((Ce,be,Ie)=>be===Ie.findIndex(nt=>nt.code===Ce.code)),Y[y].warehouse.options=Le==null?void 0:Le.filter((Ce,be,Ie)=>be===Ie.findIndex(nt=>nt.code===Ce.code))}Dt(Y)};d.useEffect(()=>{sr(H)&&(H!=null&&H.length)||v||C?(e.setCompleted([!0,!0]),e==null||e.setIsAttachmentTabEnabled(!0)):(e.setCompleted([!0,!1]),e==null||e.setIsAttachmentTabEnabled(!1))},[H]);const ms=ws+10,gn=()=>{var y,G;const i=Co(),h={id:i,included:!0,lineNumber:ms,industrySector:(y=Qu)==null?void 0:y.DEFAULT_IND_SECTOR,materialType:((G=he==null?void 0:he.MatlType)==null?void 0:G.code)??"",materialNumber:($e==null?void 0:$e.code)||"",globalMaterialDescription:"",Bom:!1,sourceList:!1,PIR:!1,views:"",orgData:"",validated:sa.default,withReference:Ne};o(gi({materialID:i,data:h})),o(Oo([...ce,h])),Bt(Ys+1),Hs(ms),Ns(!0),Ke(!0),Rt(!0),Ue([ge]),Vt(i),W("")},yn=()=>{ae(!1),(u==null?void 0:u.RequestType)==="Create"?gn():(u==null?void 0:u.RequestType)==="Change"&&Wt(!0)},Sn=()=>{Ze()},Nn=(i="",h=!0)=>{var Z,K;const y={materialNo:i??"",salesOrg:((Z=ie==null?void 0:ie.uniqueSalesOrgList)==null?void 0:Z.map(ue=>ue.code).join("$^$"))||"",top:500,skip:h?0:fs,matlType:((K=Ot==null?void 0:Ot["Material Type"])==null?void 0:K.code)??""};is(ue=>({...ue,"Material No":!0}));const G=ue=>{(ue==null?void 0:ue.statusCode)===Jt.STATUS_200&&(ue!=null&&ue.body)&&Qt(h?ue==null?void 0:ue.body:Le=>[...Le,...ue==null?void 0:ue.body]),lt(!1),is(Le=>({...Le,"Material No":!1}))},Y=()=>{lt(!1),is(ue=>({...ue,"Material No":!1}))};lt(!0),Xe(`/${Re}/data/getSearchParamsMaterialNo`,"post",G,Y,y)},Yn=i=>{const h=G=>{(G==null?void 0:G.statusCode)===Jt.STATUS_200&&ls(G==null?void 0:G.body)},y=G=>{console.error(G,"while fetching the validation data of material number")};Xe(`/${Re}/data/getNumberRangeForMaterialType?materialType=${i==null?void 0:i.code}`,"get",h,y)};d.useEffect(()=>{var i;(i=Ot==null?void 0:Ot["Material Type"])!=null&&i.code&&(Nn(),me(null),Zt(null))},[(dl=Ot==null?void 0:Ot["Material Type"])==null?void 0:dl.code]);const rr=((ul=os==null?void 0:os[0])==null?void 0:ul.External)==="X",lr=((hl=os==null?void 0:os[1])==null?void 0:hl.External)==="X",ir=os==null?void 0:os.some(i=>i.ExtNAwock==="X"),_=(i=>{const h=new Set;let y=null;i==null||i.forEach(Y=>{Y.External==="X"&&Y.ExtNAwock==="X"?(h.add(`External Number Range: Allowed (${Y.FromNumber}-${Y.ToNumber})`),h.add("Ext W/O Check: Allowed")):Y.External!=="X"&&Y.ExtNAwock==="X"?(h.add("Internal Number Range: Allowed"),h.add("Ext W/O Check: Allowed")):Y.External==="X"&&Y.ExtNAwock!=="X"?(h.add(`External Number Range: Allowed (${Y.FromNumber}-${Y.ToNumber})`),y="Ext W/O Check: Not Allowed"):Y.External!=="X"&&Y.ExtNAwock!=="X"&&(h.add("Internal Number Range: Allowed"),y="Ext W/O Check: Not Allowed")});const G=Array.from(h);return y&&G.push(y),G.length===0?n(mt,{children:le("Please select Material type")}):G.map((Y,Z)=>n("div",{children:n(mt,{children:Y})},Z))})(os);function W(i){var G;const h=(u==null?void 0:u.Region)||Uo.US;if(!j.some(Y=>Y[h]&&Y[h][i])&&i)s(i,h);else if(!i)o(kr({}));else{const Y=j==null?void 0:j.find(Z=>(Z==null?void 0:Z[h])&&(Z==null?void 0:Z[h][i]));Y&&o(kr((G=Y[h][i])==null?void 0:G.allfields))}i&&l(i)}const re=i=>{const{id:h,field:y,value:G}=i;let Y=ce.map(Z=>Z.id===h?{...Z,[y]:G}:Z);Ye({...ao,[y]:G}),y===xr.MATERIALTYPE&&(Yn(G),Ue([ge]),$h([es]),o(Bn({materialID:h,keyName:"views",data:[ge]})),o(Bn({materialID:h,keyName:"orgData",data:""})),Y=Y.map(Z=>Z.id===h?{...Z,orgData:"",Bom:!1,sourceList:!1,PIR:!1}:Z),W(G==null?void 0:G.code)),y===xr.INCLUDED&&(sr(Y)?(Ke(!1),Rt(!1)):(Ke(!0),Rt(!0))),y===xr.VIEWS&&(Ke(!0),Rt(!0)),$(Y),o(Bn({materialID:h,keyName:y,data:G})),o(Oo(Y))},Ae=i=>{var h,y,G,Y,Z,K,ue,Le,Ce,be,Ie,nt,qt;Vt(i.row.id),w(i.row.materialNumber),W((y=(h=i==null?void 0:i.row)==null?void 0:h.materialType)==null?void 0:y.code),gs((u==null?void 0:u.Region)==="EUR"?((Z=Te[(Y=(G=i==null?void 0:i.row)==null?void 0:G.materialType)==null?void 0:Y.code])==null?void 0:Z.filter(Yt=>Yt!==P.WAREHOUSE))||[]:Te[(ue=(K=i==null?void 0:i.row)==null?void 0:K.materialType)==null?void 0:ue.code]||[]),Ue((Ce=(Le=i==null?void 0:i.row)==null?void 0:Le.views)!=null&&Ce.length?(be=i.row)==null?void 0:be.views:[ge]),Dt((nt=(Ie=i==null?void 0:i.row)==null?void 0:Ie.orgData)!=null&&nt.length?(qt=i.row)==null?void 0:qt.orgData:[es]),it(0),Ts("Basic Data")},Ge=()=>{Fs(!0)},Ze=()=>{Fs(!1)},et=(i,h)=>{h==="backdropClick"||h==="escapeKeyDown"||St(!1)},wt=()=>Ue(Mt||[ge]),Tt=()=>{if(ae(!1),Ne==="yes")if(Oe!=null&&Oe.length){let i=[...ce];Oe==null||Oe.forEach(h=>{var K,ue;const y=Co();let G=JSON.parse(JSON.stringify(h));G!=null&&G.refMaterialData&&delete G.refMaterialData;let Y=JSON.parse(JSON.stringify((K=V==null?void 0:V[h.id])==null?void 0:K.payloadData));G.id=y,G.lineNumber=ms,G.globalMaterialDescription="",G.materialNumber="",G.validated=sa.default,o(gi({materialID:y,data:G,payloadData:Y})),i.push(G),$(i),o(Oo(i)),Bt(Ys+1),Hs(ms),Ns(!0),Ke(!0),Rt(!0);let Z=(ue=V==null?void 0:V[h.id])==null?void 0:ue.unitsOfMeasureData;if(Z!=null&&Z.length){let Le=[];Z==null||Z.forEach(Ce=>{var be,Ie,nt;Le.push({...Ce,eanUpc:"",eanCategory:"",length:"",width:"",height:"",volume:"",grossWeight:"",netWeight:"",eanCategory:(u==null?void 0:u.Region)===((be=Uo)==null?void 0:be.US)?Ce==null?void 0:Ce.EanCat:"",eanUpc:(Ce==null?void 0:Ce.EanCat)==="MB"&&(u==null?void 0:u.Region)===((Ie=Uo)==null?void 0:Ie.US)||(u==null?void 0:u.Region)===((nt=Uo)==null?void 0:nt.EUR)?"":Ce==null?void 0:Ce.EanUpc,id:(Ce==null?void 0:Ce.id)||Le.length+1})}),o(Ju({materialID:y,data:Le}))}}),ct([])}else $e&&Kt();else yn()},Kt=()=>{var G,Y,Z,K,ue,Le,Ce;_e(!0);let i={material:$e==null?void 0:$e.code,wareHouseNumber:(G=Ot==null?void 0:Ot.Warehouse)==null?void 0:G.code,storageLocation:(Y=Ot==null?void 0:Ot["Storage Location"])==null?void 0:Y.code,salesOrg:(Z=Ot==null?void 0:Ot["Sales Org"])==null?void 0:Z.code,distributionChannel:(K=Ot==null?void 0:Ot["Distribution Channel"])==null?void 0:K.code,valArea:(ue=Ot==null?void 0:Ot.Plant)==null?void 0:ue.code,plant:(Le=Ot==null?void 0:Ot.Plant)==null?void 0:Le.code};const h=be=>{var Ie,nt,qt,Yt,Vs,ro,cs,vo,ln,an,wn,Rn,Ko,_n,In,vn,Xn,Vn,Kn,Jn,fo,Tn,Jo,nn,Qn;if(_e(!1),Gs({}),be!=null&&be.body[0]){Zu(be==null?void 0:be.body,u);let Zn=[...ce];const kn=Co();let Ls={};Ls.id=kn,Ls.included=!0,Ls.lineNumber=ms,Ls.globalMaterialDescription="",Ls.materialType={code:((Ie=be.body[0])==null?void 0:Ie.MatlType)||"",desc:((qt=(nt=ne==null?void 0:ne.MatlType)==null?void 0:nt.find(So=>{var Mo;return So.code===((Mo=be.body[0])==null?void 0:Mo.MatlType)}))==null?void 0:qt.desc)||""},Ls.industrySector={code:((Yt=be.body[0])==null?void 0:Yt.IndSector)||"",desc:((ro=(Vs=ne==null?void 0:ne.IndSector)==null?void 0:Vs.find(So=>{var Mo;return So.code===((Mo=be.body[0])==null?void 0:Mo.IndSector)}))==null?void 0:ro.desc)||""},Ls.materialNumber="",Ls.Bom=((cs=be.body[0])==null?void 0:cs.Bom)||"",Ls.Category=(vo=be.body[0])!=null&&vo.Category?{code:((ln=be.body[0])==null?void 0:ln.Category)||"",desc:((wn=(an=Dp)==null?void 0:an.find(So=>{var Mo;return So.code===((Mo=be.body[0])==null?void 0:Mo.Category)}))==null?void 0:wn.desc)||""}:"",Ls.Uom=(Rn=be.body[0])!=null&&Rn.Uom?{code:((Ko=be.body[0])==null?void 0:Ko.Uom)||"",desc:((In=(_n=ne==null?void 0:ne.BaseUom)==null?void 0:_n.find(So=>{var Mo;return So.code===((Mo=be.body[0])==null?void 0:Mo.Uom)}))==null?void 0:In.desc)||""}:"",Ls.Relation=(vn=be.body[0])!=null&&vn.Relation?{code:((Xn=be.body[0])==null?void 0:Xn.Relation)||"",desc:((Kn=(Vn=Pp)==null?void 0:Vn.find(So=>{var Mo;return So.code===((Mo=be.body[0])==null?void 0:Mo.Relation)}))==null?void 0:Kn.desc)||""}:"",Ls.Usage=((Jn=be.body[0])==null?void 0:Jn.Usage)||"",Ls.views=(Tn=(((fo=be.body[0])==null?void 0:fo.Views)||"").split(",").map(So=>So.trim()==="Storage"?P.STORAGE:So.trim()))==null?void 0:Tn.filter(So=>!Ui.includes(So)),(u==null?void 0:u.Region)===((Jo=Uo)==null?void 0:Jo.EUR)&&(Ls.views=((nn=Ls==null?void 0:Ls.views)==null?void 0:nn.filter(So=>So!==P.WAREHOUSE))||[]),Ls.validated=sa.default,Ls.withReference=Ne,Ls.refMaterialData=Zu(be.body,u),o(gi({materialID:kn,data:Ls,payloadData:{}})),Zn.push(Ls),$(Zn),o(Oo(Zn)),Ue(Ls==null?void 0:Ls.views),Bt(Ys+1),Hs(ms),Ns(!0),Ke(!0),Rt(!0),W((Qn=be.body[0])==null?void 0:Qn.MatlType),Vt(kn)}else _e(!1),_t(!0),te(dn.NO_MATERIAL_FOUND),je("warning"),ae(!0)},y=be=>{r(be),_e(!1),ae(!0)};Gs({}),me(null),Zt(null),Xe(`/${Re}${(Ce=Fe.DATA)==null?void 0:Ce.GET_COPY_MATERIAL}`,"post",h,y,i)},tt=!ko.includes(e==null?void 0:e.requestStatus)||C&&!v,hs=i=>({hasFertRole:i.includes("CA-MDG-MRKTNG-FERT-EUR"),hasSalesRole:i.includes("CA-MDG-MRKTNG-SALES-EUR")}),eo=(i,h,y)=>{var Z;const{hasFertRole:G,hasSalesRole:Y}=hs(f);if(G&&!Y&&(u==null?void 0:u.Region)===Uo.EUR)return(i==null?void 0:i.code)!=="FERT";if(!G&&Y&&(u==null?void 0:u.Region)===Uo.EUR)return(i==null?void 0:i.code)==="FERT";if(G&&Y&&(u==null?void 0:u.Region)===Uo.EUR){const K=h[0];if(y===(K==null?void 0:K.id))return!1;const ue=(Z=K==null?void 0:K.materialType)==null?void 0:Z.code;if(ue==="FERT")return(i==null?void 0:i.code)!=="FERT";if(ue)return(i==null?void 0:i.code)==="FERT"}return!1},zt=(i,h)=>{var y;xi.fire({title:le("Are you sure?"),text:le("Changing the material type will reset all the field values entered!"),icon:"warning",showCancelButton:!0,confirmButtonColor:(y=Be.primary)==null?void 0:y.main,cancelButtonColor:Be.error.red,confirmButtonText:le("Yes, do it!"),cancelButtonText:le("Cancel"),reverseButtons:!0}).then(G=>{G.isConfirmed&&(o(yp({materialId:h})),re({id:h,field:"materialType",value:i}))})},oo=[{field:"included",headerName:le("Included"),flex:.5,align:"center",headerAlign:"center",renderCell:i=>n(Hn,{checked:i.row.included,disabled:tt,onChange:h=>re({id:i.row.id,field:"included",value:h.target.checked})})},{field:"lineNumber",headerName:le("Line Number"),flex:.5,editable:!0,align:"center",headerAlign:"center"},{field:"industrySector",headerName:le("Industry Sector"),flex:.7,align:"center",headerAlign:"center",...se===b.CREATE||se===b.CREATE_WITH_UPLOAD?{renderCell:i=>{var h;return n(lo,{options:(ne==null?void 0:ne.IndSector)||[],value:i.row.industrySector||((h=Qu)==null?void 0:h.DEFAULT_IND_SECTOR),onChange:y=>re({id:i.row.id,field:"industrySector",value:y}),placeholder:le("Select Industry Sector"),disabled:tt,minWidth:"90%",listWidth:235})}}:{editable:!1,renderCell:i=>{var h,y;return((y=(h=V==null?void 0:V[i.row.id])==null?void 0:h.headerData)==null?void 0:y.industrySector)||""}}},{field:"materialType",headerName:le("Material Type"),flex:.7,align:"center",headerAlign:"center",...se===b.CREATE||se===b.CREATE_WITH_UPLOAD?{renderCell:i=>n(lo,{options:Ai||[],value:i.row.materialType,onChange:h=>{i.row.materialType?zt(h,i.row.id):re({id:i.row.id,field:"materialType",value:h})},placeholder:le("Select Material Type"),disabled:tt,minWidth:"90%",listWidth:235,isOptionDisabled:h=>eo(h,ce,i.row.id)})}:{editable:!1,renderCell:i=>{var h,y;return((y=(h=V==null?void 0:V[i.row.id])==null?void 0:h.headerData)==null?void 0:y.materialType)||""}}},{field:"materialNumber",headerName:le("Material Number"),flex:.7,editable:!1,align:"center",headerAlign:"center",renderHeader:()=>R("span",{children:[le("Material Number"),n("span",{style:{color:"red"},children:"*"})]}),renderCell:i=>{var Z;const[h,y]=d.useState({[(Z=i==null?void 0:i.row)==null?void 0:Z.id]:i.row.materialNumber}),G=i.row.id,Y=K=>{const ue=K.target.value.toUpperCase(),Le=(u==null?void 0:u.Region)==="US"?ue.replace(/[^A-Z0-9-]/g,"").replace(/-{2,}/g,"-"):ue.replace(/[^A-Z0-9]/g,"");y(be=>({...be,[G]:Le})),re({id:i.row.id,field:"materialNumber",value:Le});const Ce=ce.map(be=>be.id===i.row.id?{...be,isMatNoChanged:!0,materialNumber:Le}:be);o(Oo(Ce))};return n(Ms,{title:_,arrow:!0,children:(u==null?void 0:u.RequestType)===b.CREATE||(u==null?void 0:u.RequestType)===b.CREATE_WITH_UPLOAD?n(xn,{fullWidth:!0,placeholder:le("ENTER MATERIAL NUMBER"),variant:"outlined",size:"small",name:"material number",value:h[G]||"",onChange:K=>{Y(K)},sx:{"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:Be.black.dark,color:Be.black.dark}}},disabled:tt}):i.row.materialNumber})}},{field:"globalMaterialDescription",flex:.7,headerName:le("Material Description"),renderHeader:()=>R("span",{children:[le("Material Description"),n("span",{style:{color:"red"},children:"*"})]}),renderCell:i=>{var K,ue;const[h,y]=d.useState({[(K=i==null?void 0:i.row)==null?void 0:K.id]:i.row.globalMaterialDescription}),G=i.row.id,Y=Le=>{const be=Le.target.value.toUpperCase().replace(/[^A-Z0-9-]/g,"").slice(0,40);y(Ie=>({...Ie,[G]:be})),re({id:i.row.id,field:"globalMaterialDescription",value:be})},Z=(((ue=h[G])==null?void 0:ue.length)||0)===40;return n(ye,{sx:{display:"flex",alignItems:"center",width:"100%"},children:n(Ms,{title:h[G]||"",arrow:!0,placement:"top",children:n(xn,{fullWidth:!0,variant:"outlined",size:"small",placeholder:le("ENTER MATERIAL DESCRIPTION"),value:h[G]||"",onChange:Y,inputProps:{maxLength:40},sx:{flexGrow:1,"& .MuiOutlinedInput-root":{"& fieldset":{borderColor:Z?Be.error.dark:void 0},"&:hover fieldset":{borderColor:Z?Be.error.dark:void 0},"&.Mui-focused fieldset":{borderColor:Z?Be.error.dark:void 0}}}})})})},align:"center",headerAlign:"center",editable:!1},{field:"Bom",headerName:le("BOM"),flex:.3,align:"center",headerAlign:"center",renderCell:i=>{var h;return n(Hn,{checked:((h=i.row)==null?void 0:h.Bom)||!1,disabled:tt,onChange:y=>re({id:i.row.id,field:"Bom",value:y.target.checked})})}},{field:"sourceList",headerName:le("Source List"),flex:.3,align:"center",headerAlign:"center",renderCell:i=>{var h;return n(Hn,{checked:((h=i.row)==null?void 0:h.sourceList)||!1,disabled:tt,onChange:y=>re({id:i.row.id,field:"sourceList",value:y.target.checked})})}},{field:"PIR",headerName:le("PIR"),flex:.3,align:"center",headerAlign:"center",renderCell:i=>{var h;return n(Hn,{checked:((h=i.row)==null?void 0:h.PIR)||!1,disabled:tt,onChange:y=>re({id:i.row.id,field:"PIR",value:y.target.checked})})}},{...se===b.CREATE||se===b.CREATE_WITH_UPLOAD?{field:"views",headerName:"",flex:.6,align:"center",headerAlign:"center",renderCell:i=>{var h,y,G;return R(Ss,{children:[n(It,{variant:"contained",size:"small",disabled:!((h=i==null?void 0:i.row)!=null&&h.materialType),onClick:()=>{var Y,Z,K;ps(!0),Bs(i.row.id),Ue((Z=(Y=i==null?void 0:i.row)==null?void 0:Y.views)!=null&&Z.length?(K=i.row)==null?void 0:K.views:[ge])},children:le("Views")}),n(It,{variant:"contained",disabled:!(((G=(y=i==null?void 0:i.row)==null?void 0:y.views)==null?void 0:G.length)>1),size:"small",sx:{marginLeft:"4px"},onClick:()=>{var Y,Z,K;St(!0),Bs(i.row.id),Dt((Z=(Y=i==null?void 0:i.row)==null?void 0:Y.orgData)!=null&&Z.length?(K=i.row)==null?void 0:K.orgData:[es])},children:le("ORG Data")})]})}}:{}},{field:"action",headerName:le("Action"),flex:.5,align:"center",headerAlign:"center",renderCell:i=>{let h=Mp(i==null?void 0:i.row);const y=async G=>{var Ie,nt,qt;G.stopPropagation();const{missingFields:Y,viewType:Z,isValid:K,plant:ue=[]}=We(i.row.id,((Ie=i==null?void 0:i.row)==null?void 0:Ie.orgData)||[],rr,lr,ir);if(_s(ue),Y){if(je("error"),typeof Y=="object"&&!Array.isArray(Y)){const Yt=Object.entries(Y).map(([Vs,ro])=>`Combination ${Vs}: ${ro.join(", ")}`);te(`${le("Line No")} ${i.row.lineNumber} : ${le("Please fill all the Mandatory fields in")} ${Z||""}: ${Yt.join(" | ")}`)}else te(`${le("Line No")} ${i.row.lineNumber} : ${le("Please fill all the Mandatory fields in")} ${Z||""}: ${Y.join(", ")}`);_t(!0)}let Le=!1;K&&(!C||(nt=i.row)!=null&&nt.isMatNoChanged)&&(Le=await kt(i.row.materialNumber,C,(qt=i==null?void 0:i.row)==null?void 0:qt.globalMaterialDescription)),h=K&&!Le?"success":"error";const Ce=ce.map(Yt=>Yt.id===i.row.id?{...Yt,validated:K&&!Le}:Yt);o(Oo(Ce));const be=sr(Ce);Ke(!be),Rt(!be)};return R(bo,{direction:"row",alignItems:"center",sx:{marginLeft:"0.5rem",magrinRight:"0.5rem"},spacing:.5,children:[n(Ms,{title:h==="success"?"Validated Successfully":le(h==="error"?"Validation Failed":"Click to Validate"),children:n(Ds,{onClick:y,color:h==="success"?"success":h==="error"?"error":"default",children:h==="error"?n(Op,{}):n(rf,{})})}),!tt&&n(Ms,{title:le("Delete Row"),children:n(Ds,{onClick:()=>{Se({...M,data:i,isVisible:!0})},color:"error",children:n(Ar,{})})})]})}}],no=(i,h)=>{var y;it(h),Ts(((y=i==null?void 0:i.target)==null?void 0:y.id)==="AdditionalKey"?"Additional Data":ee==null?void 0:ee[h])},Vo=i=>{const h={"Sales Org":"/getSalesOrg","Mrp Profile":"/getMRPProfile"},y=Y=>{const Z=On(Y.body);yt(K=>({...K,[i]:Z}))},G=Y=>console.error(Y);Xe(`/${Re}/data${h[i]}`,"get",y,G)},pn=i=>{jh(i,ee,co,ke,ot,o,Gn)},ar=i=>{Lp(i,ee,co,ke,ot,o,Gn,P)},Ir=(i,h,y)=>(G,Y)=>{var Ie,nt,qt;let Z={},K="",ue="";y==="Purchasing"||y==="Costing"?(Z={materialNo:h==null?void 0:h.Material,plant:h==null?void 0:h.Plant},ue=h==null?void 0:h.Plant,K=`/${Re}/data/displayLimitedPlantData`):y==="Accounting"?(Z={materialNo:h==null?void 0:h.Material,valArea:h==null?void 0:h.ValArea},ue=h==null?void 0:h.ValArea,K=`/${Re}/data/displayLimitedAccountingData`):y==="Sales"&&(Z={materialNo:h==null?void 0:h.Material,salesOrg:h==null?void 0:h.SalesOrg,distChnl:h==null?void 0:h.DistrChan},ue=`${h==null?void 0:h.SalesOrg}-${h==null?void 0:h.DistrChan}`,K=`/${Re}/data/displayLimitedSalesData`);const Le=Yt=>{var Vs,ro,cs;y==="Purchasing"||y==="Costing"?o(Gn({materialID:ke,viewID:y,itemID:h==null?void 0:h.Plant,data:(Vs=Yt==null?void 0:Yt.body)==null?void 0:Vs.SpecificPlantDataViewDto[0]})):y==="Accounting"?o(Gn({materialID:ke,viewID:y,itemID:h==null?void 0:h.ValArea,data:(ro=Yt==null?void 0:Yt.body)==null?void 0:ro.SpecificAccountingDataViewDto[0]})):y==="Sales"&&o(Gn({materialID:ke,viewID:y,itemID:`${h==null?void 0:h.SalesOrg}-${h==null?void 0:h.DistrChan}`,data:(cs=Yt==null?void 0:Yt.body)==null?void 0:cs.SpecificSalesDataViewDto[0]}))},Ce=()=>{};!((qt=(nt=(Ie=V==null?void 0:V[ke])==null?void 0:Ie.payloadData)==null?void 0:nt[y])!=null&&qt[ue])&&Xe(K,"post",Le,Ce,Z),I(Y?i:null)},Xl=()=>J&&st&&(J[st]||st==="Additional Data"||so!=null&&so.includes(st))?st==="Additional Data"?[n(Kh,{disableCheck:v&&!ko.includes(e==null?void 0:e.requestStatus),materialID:ke,selectedMaterialNumber:de})]:[n(Vh,{disabled:v&&!ko.includes(e==null?void 0:e.requestStatus),selectedMaterialNumber:de,materialID:ke,basicData:ds,setBasicData:rs,dropDownData:at,basicDataTabDetails:J[st],allTabsData:J,activeViewTab:st,selectedViews:ee,handleAccordionClick:Ir,missingValidationPlant:jt,isDisplay:C||v})]:n(Ss,{}),Yr=i=>{var G,Y;const h=((Y=(G=i==null?void 0:i.target)==null?void 0:G.value)==null?void 0:Y.toUpperCase())||"";Zt(null),Ht(0),x&&clearTimeout(x);const y=setTimeout(()=>{Nn(h,!0)},500);N(y)},Vl=(i,h)=>{const y=$e==null?void 0:$e.code,G=Ne==="yes"?"extended":"notExtended";Gs(Y=>({...Y,[i]:h})),i==="Sales Org"&&h?ft(y,G,h):i==="Plant"&&h&&(At(y,G,h),ve(y,h,Ot["Sales Org"]),Gs(Y=>({...Y,"Storage Location":null,Warehouse:null})))},Kl=(i,h,y)=>{i==="Sales Organization"&&(h?(Dt(G=>G.map((Y,Z)=>Z===y?{...Y,salesOrg:h}:Y)),Xr(h,y).then(G=>{pt(i,h,y)})):pt(i,h,y))},Xr=(i,h,y="",G="")=>new Promise((Y,Z)=>{is(Ce=>({...Ce,"Distribution Channel":{...Ce["Distribution Channel"],[h]:!0}}));let K={salesOrg:i==null?void 0:i.code};const ue=Ce=>{is(Ie=>({...Ie,"Distribution Channel":{...Ie["Distribution Channel"],[h]:!1}}));let be=JSON.parse(JSON.stringify(y||Xs.current));if(be[h].dc.options=On(Ce.body),Dt(be),Xs.current=be,G){o(Bn({materialID:G==null?void 0:G.id,keyName:"orgData",data:be}));let Ie=(ce==null?void 0:ce.length)||[JSON.parse(JSON.stringify(G))],nt=Ie.findIndex(qt=>qt.id===(G==null?void 0:G.id));Ie[nt].orgData=be,o(Oo(Ie)),Y({org:be,material:Ie[nt]})}else Y(""),is(Ie=>({...Ie,"Distribution Channel":{...Ie["Distribution Channel"],[h]:!1}}))},Le=Ce=>{console.error(Ce),is(be=>({...be,"Distribution Channel":{...be["Distribution Channel"],[h]:!1}}))};Xe(`/${Re}/data/getDistrChan`,"post",ue,Le,K)}),Jl=(i,h)=>{let y=JSON.parse(JSON.stringify(ot));y[h].dc.value=i,Dt(y)},Ql=i=>{let h=JSON.parse(JSON.stringify(ot));h.splice(i,1),Dt(h)},Zl=(i,h)=>{let y=JSON.parse(JSON.stringify(ot));y[h].sloc.value=i,Dt(y)},ei=(i,h)=>{let y=JSON.parse(JSON.stringify(ot));y[h].mrpProfile=i,Dt(y)},ti=(i,h)=>{let y=JSON.parse(JSON.stringify(ot));y[h].warehouse.value=i,Dt(y)},si=()=>{let i=JSON.parse(JSON.stringify(ot));i.push(es),Dt(i)},oi=i=>{if(!(i!=null&&i.temp)||(i==null?void 0:i.temp)===(ze==null?void 0:ze.temp))return;_e(!0);let h={decisionTableId:null,decisionTableName:"MDG_MAT_ORGDATA_TEMPLATE_CONFIG",version:"v2",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":(u==null?void 0:u.Region)||Uo.US,"MDG_CONDITIONS.MDG_MAT_TEMPLATE":i.temp||""}],systemFilters:null,systemOrders:null,filterString:null};const y=Y=>{var Z,K;if(Y.statusCode===Jt.STATUS_200){_e(!1);let ue=(K=(Z=Y==null?void 0:Y.data)==null?void 0:Z.result[0])==null?void 0:K.MDG_MAT_ORGDATA_TEMPLATE_CONFIG,Le=[];ue==null||ue.forEach((Ce,be)=>{var ro;let Ie=JSON.parse(JSON.stringify(es));Ie.salesOrg={},Ie.salesOrg.code=Ce.MDG_MAT_SALES_ORG,Ie.salesOrg.desc=Ce.MDG_MAT_SALES_ORG_DESC,Ie.plant.value={},Ie.plant.value.code=Ce.MDG_MAT_PLANT,Ie.plant.value.desc=Ce.MDG_MAT_PLANT_DESC;let nt=(ro=xe==null?void 0:xe.filter(cs=>cs.MDG_MAT_SALES_ORG===Ce.MDG_MAT_SALES_ORG))==null?void 0:ro.map(cs=>({code:cs.MDG_MAT_PLANT,desc:cs.MDG_MAT_PLANT_DESC}));nt=nt==null?void 0:nt.filter((cs,vo,ln)=>vo===ln.findIndex(an=>an.code===cs.code)),Ie.plant.options=nt==null?void 0:nt.sort((cs,vo)=>cs.code-vo.code);let qt=xe==null?void 0:xe.filter(cs=>cs.MDG_MAT_SALES_ORG===Ce.MDG_MAT_SALES_ORG&&cs.MDG_MAT_PLANT===Ce.MDG_MAT_PLANT),Yt=qt==null?void 0:qt.map(cs=>({code:cs.MDG_MAT_STORAGE_LOCATION,desc:cs.MDG_MAT_STORE_LOC_DESC})),Vs=qt==null?void 0:qt.map(cs=>cs.MDG_MAT_WAREHOUSE?{code:cs.MDG_MAT_WAREHOUSE,desc:cs.MDG_MAT_WAREHOUSE_DESC}:null).filter(Boolean);Ce.MDG_MAT_STORAGE_LOCATION&&(Ie.sloc.value={},Ie.sloc.value.code=Ce.MDG_MAT_STORAGE_LOCATION,Ie.sloc.value.desc=Ce.MDG_MAT_STORE_LOC_DESC),Ie.sloc.options=Yt,Ce.MDG_MAT_WAREHOUSE&&(Ie.warehouse.value={},Ie.warehouse.value.code=Ce.MDG_MAT_WAREHOUSE||"",Ie.warehouse.value.desc=Ce.MDG_MAT_WAREHOUSE_DESC||""),Ie.warehouse.options=Vs,Le.push(Ie)}),Xs.current=Le,Dt(Le),Vr(Le,0)}},G=Y=>{r(Y),_e(!1)};q.environment==="localhost"?Xe(`/${yo}${Fe.INVOKE_RULES.LOCAL}`,"post",y,G,h):Xe(`/${yo}${Fe.INVOKE_RULES.PROD}`,"post",y,G,h)},Vr=async(i,h)=>{h<(i==null?void 0:i.length)&&(await Xr(i[h].salesOrg,h),h++,Vr(i,h))},ni=()=>{const i=M==null?void 0:M.data;$(ce==null?void 0:ce.filter(h=>{var y;return h.id!==((y=i==null?void 0:i.row)==null?void 0:y.id)})),o(zh(i==null?void 0:i.row.id)),W(""),o(Oo(ce==null?void 0:ce.filter(h=>{var y;return h.id!==((y=i==null?void 0:i.row)==null?void 0:y.id)}))),ce!=null&&ce.length?ce.filter(h=>{var y,G;return((y=h.params)==null?void 0:y.id)!==((G=i==null?void 0:i.row)==null?void 0:G.id)}).every(h=>h.validated)&&Ke(!1):Ke(!1),Se({...M,isVisible:!1})};d.useEffect(()=>{var Y,Z,K,ue;const i=ee==null?void 0:ee.includes((Y=P)==null?void 0:Y.SALES),h=ee==null?void 0:ee.includes((Z=P)==null?void 0:Z.SALES_PLANT),y=ee==null?void 0:ee.includes((K=P)==null?void 0:K.STORAGE),G=ee==null?void 0:ee.includes((ue=P)==null?void 0:ue.STORAGE_PLANT);i&&!h&&Ue(Le=>{var Ie,nt;const Ce=[...Le],be=Ce.indexOf((Ie=P)==null?void 0:Ie.SALES);return Ce.splice(be+1,0,(nt=P)==null?void 0:nt.SALES_PLANT),Ce}),y&&!G&&Ue(Le=>{var Ie,nt;const Ce=[...Le],be=Ce.indexOf((Ie=P)==null?void 0:Ie.STORAGE);return Ce.splice(be+1,0,(nt=P)==null?void 0:nt.STORAGE_PLANT),Ce})},[ee]);const Kr=i=>{!i||!Array.isArray(i)||i.forEach(h=>{var y,G,Y,Z,K,ue,Le,Ce,be,Ie,nt,qt,Yt,Vs,ro,cs;if((G=(y=h.plant)==null?void 0:y.value)!=null&&G.code){if(Ws((Z=(Y=h.plant)==null?void 0:Y.value)==null?void 0:Z.code,P.PLANT),(K=h.salesOrg)!=null&&K.code||(Le=(ue=h.dc)==null?void 0:ue.value)!=null&&Le.code){const vo=`${((Ce=h.salesOrg)==null?void 0:Ce.code)||""}-${((Ie=(be=h.dc)==null?void 0:be.value)==null?void 0:Ie.code)||""}`;Ws(vo,P.SALES)}(qt=(nt=h.warehouse)==null?void 0:nt.value)!=null&&qt.code&&Ws((Vs=(Yt=h.warehouse)==null?void 0:Yt.value)==null?void 0:Vs.code,P.WAREHOUSE),Wo((cs=(ro=h.plant)==null?void 0:ro.value)==null?void 0:cs.code)}})};d.useEffect(()=>{if(C){const i=Es==null?void 0:Es.orgData;(i==null?void 0:i.length)>0&&i.some(h=>{var y,G,Y,Z,K;return((G=(y=h.plant)==null?void 0:y.value)==null?void 0:G.code)&&(((Y=h.salesOrg)==null?void 0:Y.code)||((K=(Z=h.dc)==null?void 0:Z.value)==null?void 0:K.code))})&&Kr(i)}},[Es==null?void 0:Es.orgData]);const Jr=i=>{o(fr(i)),U(i)};d.useEffect(()=>{var i,h;(g==null?void 0:g.page)!==0&&(se===((i=b)==null?void 0:i.CREATE_WITH_UPLOAD)||se===((h=b)==null?void 0:h.CREATE))&&m(),U((g==null?void 0:g.page)||0)},[g==null?void 0:g.page]);const ri=()=>{L(!S),A&&O(!1)},li=()=>{O(!A),S&&L(!1)};return R("div",{children:[n("div",{style:{padding:"0",width:"100%",margin:"0",marginTop:"20px"},children:R(ye,{sx:{position:S?"fixed":"relative",top:S?0:"auto",left:S?0:"auto",right:S?0:"auto",bottom:S?0:"auto",width:S?"100vw":"100%",height:S?"100vh":"auto",zIndex:S?1004:void 0,backgroundColor:S?"white":"transparent",padding:S?"20px":"0",display:"flex",flexDirection:"column",boxShadow:S?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[R(ye,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",backgroundColor:"#f5f5f5",borderRadius:"8px 8px 0 0"},children:[n(mt,{variant:"h6",children:le("Material Data")}),R(ye,{sx:{display:"flex",alignItems:"center",gap:1},children:[R(It,{variant:"contained",color:"primary",size:"small",onClick:()=>{se===b.CREATE&&(ae(!0),ct([]),me(null),Gs({}),Qt([]))},disabled:F||tt,children:["+ ",le("Add")]}),n(Ms,{title:le(S?"Exit Zoom":"Zoom In"),sx:{zIndex:"1009"},children:n(Ds,{onClick:ri,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:S?n(Ea,{}):n(Ta,{})})})]})]}),C&&ce&&(ce==null?void 0:ce.length)>0?n("div",{style:{width:"100%",height:"100%",overflowX:"auto"},children:n("div",{style:{height:"100%"},children:n(qr,{rows:ce,columns:oo,pageSize:50,autoHeight:!1,page:k,rowCount:(g==null?void 0:g.totalElements)||0,rowsPerPageOptions:[50],onRowClick:Ae,onCellEditCommit:re,onPageChange:i=>Jr(i),pagination:!0,disableSelectionOnClick:!0,getRowClassName:i=>i.id===ke?"selected-row":"",style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%",height:S?"calc(100vh - 150px)":`${Math.min(ce.length*50+130,300)}px`,overflow:"auto"},sx:{"& .selected-row":{backgroundColor:"rgb(234 233 255)"}}})})}):n(Ss,{children:n("div",{style:{width:"100%",height:"100%",overflowX:"auto"},children:n("div",{style:{height:"100%"},children:n(qr,{autoHeight:!1,rows:ce,columns:oo,pageSize:50,page:k,rowsPerPageOptions:[50],onRowClick:Ae,onCellEditCommit:re,onPageChange:i=>Jr(i),disableSelectionOnClick:!0,getRowClassName:i=>i.id===ke?"selected-row":"",style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%",height:S?"calc(100vh - 150px)":`${Math.min(ce.length*50+130,300)}px`,overflow:"auto"},sx:{"& .selected-row":{backgroundColor:"rgb(234 233 255)"}}})})})})]})}),se===b.CREATE||se===b.CREATE_WITH_UPLOAD||z!=null&&z.ATTRIBUTE_1?ke&&Ut&&(ce==null?void 0:ce.length)>0&&(qe==null?void 0:qe.length)>0&&J&&((fl=Object.getOwnPropertyNames(J))==null?void 0:fl.length)>0&&R(ye,{sx:{position:A?"fixed":"relative",top:A?0:"auto",left:A?0:"auto",right:A?0:"auto",bottom:A?0:"auto",width:A?"100vw":"100%",height:A?"100vh":"auto",zIndex:A?1004:void 0,backgroundColor:A?"white":"transparent",padding:A?"20px":"0",marginTop:"20px",display:"flex",flexDirection:"column",boxShadow:A?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[R(ye,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",borderRadius:"8px 8px 0 0"},children:[n(mt,{variant:"h6",children:le("View Details")}),n(Ms,{title:le(A?"Exit Zoom":"Zoom In"),sx:{zIndex:"1009"},children:n(Ds,{onClick:li,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:A?n(Ea,{}):n(Ta,{})})})]}),R(ye,{sx:{flexGrow:1,display:"flex",flexDirection:"column"},children:[R(Hl,{value:Me,onChange:no,className:t.customTabs,"aria-label":"material tabs",sx:{top:0,position:"sticky",zIndex:1e3,backgroundColor:Be.background.container,borderBottom:`1px solid ${Be.border.light}`,"& .MuiTab-root":{minHeight:"48px",textTransform:"none",fontSize:"14px",fontWeight:600,color:Be.black.graphite,"&.Mui-selected":{color:Be.primary.main,fontWeight:700},"&:hover":{color:Be.primary.main,opacity:.8}},"& .MuiTabs-indicator":{backgroundColor:Be.primary.main,height:"3px"}},children:[ee&&ot.length>0&&(ee==null?void 0:ee.length)>0?ee==null?void 0:ee.map((i,h)=>n(jn,{label:le(i)},h)):n(Ss,{}),n(jn,{label:le("Additional Data"),id:"AdditionalKey"},"Additional data")]}),n(ye,{sx:{padding:2,marginTop:2,flexGrow:1,overflow:"auto",height:A?"calc(100vh - 180px)":"auto"},children:(ce==null?void 0:ce.length)>0&&Xl()}),(!tt||C&&!v||v&&ko.includes(e==null?void 0:e.requestStatus))&&n(ye,{sx:{borderTop:"1px solid #e0e0e0",padding:"16px"},children:n($r,{activeTab:Me,submitForApprovalDisabled:!sr(H),filteredButtons:Xo,validateMaterials:as,workFlowLevels:qo,showWfLevels:Yo,childRequestHeaderData:(gl=V==null?void 0:V[ke])==null?void 0:gl.Tochildrequestheaderdata})})]})]}):n(Ss,{}),n(ki,{dialogState:$s,openReusableDialog:Ge,closeReusableDialog:Ze,dialogTitle:"Warning",dialogMessage:ss,showCancelButton:!1,handleOk:Sn,handleDialogConfirm:Ze,dialogOkText:"OK",dialogSeverity:"danger"}),Lt&&n(cn,{fullWidth:!0,maxWidth:!1,open:!0,onClose:et,sx:{display:"flex",justifyContent:"center"},disableEscapeKeyDown:!0,children:R(ye,{sx:{width:"600px !important"},children:[R(Pn,{sx:{backgroundColor:"#EAE9FF",marginBottom:".5rem"},children:[n(Hr,{style:{height:"20px",width:"20px",marginBottom:"-5px"}}),n("span",{children:le("Select Views")})]}),n(Lo,{sx:{paddingBottom:".5rem"},children:R(ye,{display:"flex",alignItems:"center",sx:{flex:1,padding:"22px 0px",gap:"5px"},children:[n(Pl,{size:"small",multiple:!0,fullWidth:!0,options:Mt||[],disabled:tt,disableCloseOnSelect:!0,value:(pl=ee==null?void 0:ee.filter(i=>!Fh.includes(i)))==null?void 0:pl.filter(i=>!(so!=null&&so.includes(i))),onChange:(i,h)=>{z!=null&&z.requestId||(Ue([ge,...h.filter(y=>y!==ge)]),re({id:Js,field:xr.VIEWS,value:h}))},getOptionDisabled:i=>i===ge,renderOption:(i,h,{selected:y})=>R("li",{...i,children:[n(Hn,{checked:y,sx:{marginRight:1}}),h]}),renderTags:(i,h)=>i.map((y,G)=>{const{key:Y,...Z}=h({index:G});return n(Er,{label:y,...Z,disabled:y===ge||tt},Y)}),renderInput:i=>n(xn,{...i,label:le("Select Views")})}),n(It,{variant:"contained",size:"small",onClick:()=>wt(),disabled:tt,children:le("Select all")})]})}),n(Do,{children:n(It,{onClick:()=>{ps(!1),re({id:Js,field:"views",value:ee})},variant:"contained",children:le("Ok")})})]})}),to&&R(cn,{fullWidth:!0,maxWidth:"xl",open:!0,onClose:et,disableEscapeKeyDown:!0,sx:{"& .MuiDialog-paper":{padding:2,borderRadius:2}},children:[R(Pn,{sx:{display:"flex",alignItems:"center",gap:1,backgroundColor:"#EAE9FF"},children:[n(Hr,{fontSize:"small"}),n("span",{children:le("Select Org Data")}),n(ye,{sx:{position:"absolute",right:"7%",width:"15%"},children:n(Pl,{options:dt.filter(i=>i.region===(u==null?void 0:u.Region)),value:ze,size:"small",disabled:tt,isOptionEqualToValue:(i,h)=>i.region===h.region,onChange:(i,h)=>{Qe(h),oi(h)},getOptionLabel:i=>i==null?void 0:i.temp,renderInput:i=>n(xn,{...i,label:le("Select Template"),sx:{minWidth:165}}),sx:{"& .MuiAutocomplete-popper":{minWidth:250}}})}),n(Ds,{onClick:et,sx:{position:"absolute",right:15},children:n(Gl,{})})]}),n(Lo,{sx:{padding:0},children:n(Ia,{component:Ur,children:R(va,{children:[n(Wh,{children:R(Ul,{children:[n(bs,{align:"center",children:le("S NO.")}),n(bs,{align:"center",children:le("Sales Org")}),n(bs,{align:"center",children:le("Distribution Channel")}),n(bs,{align:"center",children:le("Plant")}),n(bs,{align:"center",children:le("Storage Location")}),(u==null?void 0:u.Region)!=="EUR"&&n(bs,{align:"center",children:le("Warehouse")}),n(bs,{align:"center",children:le("MRP Profile")}),ot.length>1&&n(bs,{align:"center",children:le("Action")})]})}),n(Ma,{children:ot.map((i,h)=>{var y,G,Y,Z,K,ue,Le,Ce,be,Ie,nt,qt,Yt,Vs,ro,cs,vo,ln,an,wn,Rn,Ko,_n,In,vn,Xn,Vn,Kn,Jn;return R(Ul,{sx:{padding:"12px",opacity:tt?.5:1,pointerEvents:tt?"none":"auto"},children:[n(bs,{children:n(mt,{variant:"body2",children:h+1})}),n(bs,{children:n(lo,{options:at["Sales Organization"],value:i.salesOrg,onChange:fo=>Kl("Sales Organization",fo,h),placeholder:le("Select Sales Org"),minWidth:165,listWidth:215,title:((y=i==null?void 0:i.salesOrg)==null?void 0:y.code)+` - ${(G=i==null?void 0:i.salesOrg)==null?void 0:G.desc}`})}),n(bs,{children:n(lo,{options:((Y=i.dc)==null?void 0:Y.options)||[],isLoading:((Z=ho["Distribution Channel"])==null?void 0:Z[h])||!1,value:(K=i.dc)==null?void 0:K.value,onChange:fo=>Jl(fo,h),placeholder:le("Select DC"),disabled:!Ml(Ol.distributionChannel,ee),minWidth:165,listWidth:215,title:((Le=(ue=i==null?void 0:i.dc)==null?void 0:ue.value)==null?void 0:Le.code)+` - ${(be=(Ce=i==null?void 0:i.dc)==null?void 0:Ce.value)==null?void 0:be.desc}`})}),n(bs,{children:n(lo,{options:((Ie=i.plant)==null?void 0:Ie.options)||[],value:(nt=i.plant)==null?void 0:nt.value,onChange:fo=>pt("plant",fo,h,i),placeholder:le("Select Plant"),disabled:!Ml(Ol.plant,ee),minWidth:165,listWidth:215,title:((Yt=(qt=i==null?void 0:i.plant)==null?void 0:qt.value)==null?void 0:Yt.code)+` - ${(ro=(Vs=i==null?void 0:i.plant)==null?void 0:Vs.value)==null?void 0:ro.desc}`})}),n(bs,{children:n(lo,{options:(cs=i==null?void 0:i.sloc)==null?void 0:cs.options,value:(vo=i==null?void 0:i.sloc)==null?void 0:vo.value,onChange:fo=>Zl(fo,h),placeholder:le("Select Sloc"),disabled:!Ml(Ol.storage,ee),minWidth:165,listWidth:215,title:((an=(ln=i==null?void 0:i.sloc)==null?void 0:ln.value)==null?void 0:an.code)+` - ${(Rn=(wn=i==null?void 0:i.sloc)==null?void 0:wn.value)==null?void 0:Rn.desc}`})}),(u==null?void 0:u.Region)!=="EUR"&&n(bs,{children:n(lo,{options:((Ko=i==null?void 0:i.warehouse)==null?void 0:Ko.options)||[],value:(_n=i==null?void 0:i.warehouse)==null?void 0:_n.value,onChange:fo=>ti(fo,h),disabled:!Ml(Ol.warehouse,ee),placeholder:le("Select Warehouse"),minWidth:165,listWidth:215,title:((vn=(In=i==null?void 0:i.warehouse)==null?void 0:In.value)==null?void 0:vn.code)+` - ${(Vn=(Xn=i==null?void 0:i.warehouse)==null?void 0:Xn.value)==null?void 0:Vn.desc}`})}),n(bs,{children:n(lo,{options:at["Mrp Profile"]||[],value:i.mrpProfile,onChange:fo=>ei(fo,h),placeholder:le("Select MRP Profile"),disabled:!Ml(Ol.mrpProfile,ee),isOptionDisabled:fo=>{var nn,Qn;if(h===0)return!1;const Tn=(Qn=(nn=ot[h].plant)==null?void 0:nn.value)==null?void 0:Qn.code;if(!Tn)return!1;const Jo=ot.slice(0,h).find(Zn=>{var kn,Ls;return((Ls=(kn=Zn.plant)==null?void 0:kn.value)==null?void 0:Ls.code)===Tn});return Jo&&Jo.mrpProfile?fo.code!==Jo.mrpProfile.code:!1},minWidth:165,listWidth:215,title:((Kn=i==null?void 0:i.mrpProfile)==null?void 0:Kn.code)+` - ${(Jn=i==null?void 0:i.mrpProfile)==null?void 0:Jn.desc}`})}),ot.length>1&&R(bs,{align:"right",children:[n(Ds,{size:"small",color:"primary",onClick:()=>{_o(!0),Io({orgRowLength:ot.length,copyFor:h});const fo=ot.filter(Tn=>{var Jo,nn;return(nn=(Jo=Tn.plant)==null?void 0:Jo.value)==null?void 0:nn.code}).map(Tn=>{var Jo,nn;return(nn=(Jo=Tn.plant)==null?void 0:Jo.value)==null?void 0:nn.code});Ne==="yes"&&ar(fo)},style:{display:h===0?"none":"inline-flex"},children:n(bi,{})}),n(Ds,{size:"small",color:"error",onClick:()=>Ql(h),children:n(Ar,{})})]})]},h)})})]})})}),R(Do,{sx:{justifyContent:"flex-end",gap:.5},children:[R(It,{onClick:si,variant:"contained",disabled:!uo||tt,children:["+ ",le("Add")]}),n(Ms,{title:uo?"":le("Please fill all the fields of first row at least"),arrow:!0,children:n("span",{children:n(It,{onClick:()=>{var i,h;if(St(!1),(h=(i=ot[0].plant)==null?void 0:i.value)!=null&&h.code){Kr(ot),re({id:Js,field:"orgData",value:ot}),qs(ot);const y=ce==null?void 0:ce.map(G=>G.id===ke?{...G,orgData:ot}:G);if(o(Oo(y)),Ne==="no"){const G=ot.filter(Y=>{var Z,K;return(K=(Z=Y.plant)==null?void 0:Z.value)==null?void 0:K.code}).map(Y=>{var Z,K;return(K=(Z=Y.plant)==null?void 0:Z.value)==null?void 0:K.code});G.length>0&&pn(G)}Qe(null)}},variant:"contained",disabled:!uo||tt,tooltip:uo?"":le("Please fill all the fields of first row at least"),children:le("Ok")})})})]})]}),Rs&&R(cn,{fullWidth:!0,open:!0,maxWidth:"lg",sx:{"&::webkit-scrollbar":{width:"1px"}},children:[n(Pn,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF",display:"flex"},children:n(mt,{variant:"h6",children:le("Add New Material")})}),R(Lo,{sx:{padding:".5rem 1rem",alignItems:"center",justifyContent:"center",margin:"0px 25px"},children:[R(Li,{component:"fieldset",sx:{paddingBottom:"2%"},children:[n(xp,{component:"legend",sx:{padding:"15px 0px",fontWeight:"600",fontSize:"15px"},children:le("Do You Want To Continue")}),R(Sa,{row:!0,"aria-label":"profit-center-number",name:"profit-center-number",value:Ne,onChange:i=>Ve(i.target.value),children:[n(Sr,{value:"yes",control:n(Pr,{}),label:le("With Reference")}),n(Sr,{value:"no",control:n(Pr,{}),label:le("Without Reference")})]})]}),R(vs,{container:!0,spacing:2,children:[n(vs,{item:!0,xs:12,children:R(vs,{container:!0,spacing:2,children:[n(vs,{item:!0,xs:3,children:n(lo,{options:Ai||[],value:Ot[X.MATERIAL_TYPE]||"",onChange:i=>{Gs(h=>({...h,[X.MATERIAL_TYPE]:i}))},placeholder:le("Select Material Type"),minWidth:180,listWidth:266,disabled:(Oe==null?void 0:Oe.length)||Ne==="no",getOptionLabel:i=>i!=null&&i.desc?`${i.code} - ${i.desc}`:(i==null?void 0:i.code)||"",renderOption:(i,h)=>R("li",{...i,children:[n("strong",{children:h==null?void 0:h.code}),h!=null&&h.desc?` - ${h==null?void 0:h.desc}`:""]})})}),n(vs,{item:!0,xs:3,children:n(lo,{options:ks,value:ns||$e,onChange:i=>{me(i),Zt(i),i||Yr(i)},minWidth:180,listWidth:266,placeholder:le("Select Material"),disabled:(Oe==null?void 0:Oe.length)||Ne==="no",getOptionLabel:i=>i!=null&&i.desc?`${i.code} - ${i.desc}`:(i==null?void 0:i.code)||"",renderOption:(i,h)=>R("li",{...i,children:[n("strong",{children:h==null?void 0:h.code}),h!=null&&h.desc?` - ${h==null?void 0:h.desc}`:""]}),handleInputChange:Yr,sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}},isLoading:ho["Material No"]})}),Qs==null?void 0:Qs.slice(0,2).map(i=>n(vs,{item:!0,xs:3,children:n(lo,{options:(at==null?void 0:at[i])||[],value:Ot[i]||"",onChange:h=>{Vl(i,h)},placeholder:le(`Select ${i}`),minWidth:180,listWidth:306,sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}},disabled:(Oe==null?void 0:Oe.length)||Ne==="no",isLoading:ho[i]})},i))]})}),n(vs,{item:!0,xs:12,children:R(vs,{container:!0,spacing:2,alignItems:"center",children:[n(vs,{item:!0,xs:3,children:n(lo,{options:(at==null?void 0:at[Qs[2]])||[],value:Ot[Qs[2]]||"",onChange:i=>{Gs(h=>({...h,[Qs[2]]:i}))},placeholder:le(`Select ${Qs[2]}`),minWidth:180,listWidth:306,sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}},disabled:(Oe==null?void 0:Oe.length)||Ne==="no",isLoading:ho["Distribution Channel"]===!0})}),Qs==null?void 0:Qs.slice(3).map(i=>n(vs,{item:!0,xs:3,children:n(lo,{options:(at==null?void 0:at[i])||[],value:Ot[i]||"",onChange:h=>{Gs(y=>({...y,[i]:h}))},placeholder:le(`Select ${i}`),minWidth:180,listWidth:306,sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}},disabled:(Oe==null?void 0:Oe.length)||Ne==="no",isLoading:ho[i]})},i)),(H==null?void 0:H.length)>0&&R(Ss,{children:[n(vs,{item:!0,xs:1,sx:{textAlign:"center"},children:n(mt,{variant:"body1",sx:{fontWeight:"bold",color:"gray"},children:"OR"})}),n(vs,{item:!0,xs:3,children:n(lo,{options:H.map(i=>({...i,code:i.lineNumber,desc:""})),value:Oe[0],onChange:i=>{ct(i?[i]:[]),Gs({}),me(null),Zt(null)},minWidth:180,listWidth:266,placeholder:le("Select Material Line Number"),disabled:($e==null?void 0:$e.code)||Ne==="no",getOptionLabel:i=>i!=null&&i.desc?`${i.code} - ${i.desc}`:(i==null?void 0:i.code)||"",renderOption:(i,h)=>R("li",{...i,children:[n("strong",{children:h==null?void 0:h.code}),h!=null&&h.desc?` - ${h==null?void 0:h.desc}`:""]}),sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}}})})]})]})})]})]}),R(Do,{sx:{display:"flex",justifyContent:"end"},children:[n(It,{sx:{width:"max-content",textTransform:"capitalize"},onClick:()=>ae(!1),variant:"outlined",children:le("Cancel")}),n(It,{className:"button_primary--normal",type:"save",disabled:!(Oe!=null&&Oe.length||$e!=null&&$e.code)&&Ne==="yes",onClick:Tt,variant:"contained",children:le("Proceed")})]})]}),(M==null?void 0:M.isVisible)&&R(Pi,{isOpen:M==null?void 0:M.isVisible,titleIcon:n(Ar,{size:"small",color:"error",sx:{fontSize:"20px"}}),Title:le("Delete Row")+"!",handleClose:()=>Se({...M,isVisible:!1}),children:[n(Lo,{sx:{mt:2},children:le(Dn.DELETE_MESSAGE)}),R(Do,{children:[n(It,{variant:"outlined",size:"small",sx:{..._a},onClick:()=>Se({...M,isVisible:!1}),children:le(mi.CANCEL)}),n(It,{variant:"contained",size:"small",sx:{...qi},onClick:ni,children:le(mi.DELETE)})]})]}),Zs&&n(af,{open:Zs,onClose:()=>_o(!1),title:xa.COPY_ORG_DATA_VALES_HEADING,selectedMaterialPayload:co,lengthOfOrgRow:Fo,materialID:ke,orgRows:ot}),Ft&&n(Un,{openSnackBar:Pe,alertMsg:Ft,alertType:Pt,handleSnackBarClose:()=>_t(!1)}),n(Cn,{blurLoading:Ct,loaderMessage:ut}),n(Hi,{})]})},pA=d.forwardRef(function(t,r){return n(_h,{direction:"down",ref:r,...t})}),TA=({open:e,onClose:t,parameters:r,templateName:o,allDropDownData:c,name:s,onSearch:m})=>{var je,Pe,_t,jt,_s,Ft;const[l,u]=d.useState({}),[se,ie]=d.useState({}),q=oe(te=>te.request.salesOrgDTData),[g,V]=d.useState({}),[Q,H]=d.useState(""),[he,ne]=d.useState([]),[J,f]=d.useState(!1),[z,j]=d.useState(!1),[p,T]=d.useState("success"),[v,C]=d.useState(!1),[S,L]=d.useState(""),[A,O]=d.useState(""),[k,U]=d.useState(!1),[x,N]=d.useState("systemGenerated"),[D,I]=d.useState([]),[ge,ee]=d.useState(""),[Ue,M]=d.useState(!1),Se=oe(te=>te.payload.payloadData),ce=oe(te=>te.request.requestHeader.requestId),$=oe(te=>te.payload.dataLoading),[qe,F]=d.useState({}),[Ke,Us]=d.useState(0),[Wt]=d.useState(200),[$s,Fs]=d.useState(0),[ss,io]=d.useState({code:"",desc:""}),[Ws,ks]=d.useState(null),Qt=d.useRef(null),[fs,Ht]=d.useState({[X.MATERIAL_NUM]:!1,[X.PLANT]:!1,[X.SALES_ORG]:!1,[X.DIVISION]:!1,[X.DIST_CHNL]:!1,[X.WAREHOUSE]:!1,[X.STORAGE_LOC]:!1,[X.MRP_CTRLER]:!1}),[ns,Zt]=d.useState(null),[xs,lt]=d.useState(""),[He,Rt]=d.useState(!1),Ys=d.useRef(null),Bt=wr(),Me=Ro(),[it,Ut]=d.useState(0),[Ns,ds]=d.useState(null),{customError:rs}=sn(),[at,yt]=d.useState([]),ws=r,Hs=ws==null?void 0:ws.map(te=>({field:te.key,headerName:te.key,editable:!0,flex:2})),os=d.useCallback(te=>{te.preventDefault();const de=(te.clipboardData||window.clipboardData).getData("Text").trim().split(`
`).map((w,Te)=>{const We=w.split("	"),le={id:Te+1};return Hs.forEach((dt,ze)=>{le[dt.field]=We[ze]||""}),le});yt(de)},[]);d.useEffect(()=>{e||(u({}),F({}))},[e]),d.useEffect(()=>{if(it===1)return document.addEventListener("paste",os),()=>{document.removeEventListener("paste",os)}},[it,os]);const ls=(te,xe)=>{Ut(xe),it===1&&ds("handlePasteMaterialData")},ao=Ih(({className:te,...xe})=>n(Ms,{...xe,classes:{popper:te}}),{target:"e1qkid610"})({[`& .${Na.tooltip}`]:{maxWidth:"none"}},""),Ye={convertJsonToExcel:()=>{let te=[];Hs==null||Hs.forEach(xe=>{xe.headerName.toLowerCase()!=="action"&&!xe.hide&&te.push({header:xe.headerName,key:xe.field})}),Ra({fileName:"Material Data",columns:te,rows:at})}},Mt=(te,xe)=>{Zt(te.currentTarget),lt(xe),Rt(!0)},gs=()=>{Rt(!1)},Lt=()=>{Rt(!0)},ps=()=>{Rt(!1)},Bs=!!ns?"custom-popover":void 0,st=(te,xe)=>{var B;u(de=>({...de,[te]:xe})),te===X.MATERIAL_TYPE&&(I([]),io({code:"",desc:""}),Us(0),(B=xe==null?void 0:xe[0])!=null&&B.code&&Ne("",!0,xe)),xe.length>0&&V(de=>({...de,[te]:""}))};d.useEffect(()=>{ie(to(l)),Me(wa(to(l)))},[l]),d.useEffect(()=>{if(at){let te=St(at);u(te)}},[at]);const Ts=(te,xe)=>{var de;const B=((de=l[te])==null?void 0:de.length)===xe.length;u(w=>({...w,[te]:B?[]:xe})),B||V(w=>({...w,[te]:""}))},to=te=>{const xe={};for(const B in te)te.hasOwnProperty(B)&&(xe[B]=te[B].map(de=>de.code).join(","));return xe},St=te=>{const xe={};return te.forEach(B=>{Object.keys(B).forEach(de=>{de!=="id"&&B[de].trim()!==""&&(xe[de]||(xe[de]=[]),xe[de].push({code:B[de].trim()}))})}),xe},es=()=>{var w;O(ya.REPORT_LOADING),H(!0),t();let te=((w=Ll[Se==null?void 0:Se.TemplateName])==null?void 0:w.map(Te=>Te.key))||[],xe={};it===0?xe={materialDetails:[te.reduce((Te,We)=>(Te[We]=se!=null&&se[We]?se==null?void 0:se[We]:"",Te),{})],templateHeaders:"",requestId:ce,templateName:Se!=null&&Se.TemplateName?Se.TemplateName:"",dtName:"MDG_MAT_CHANGE_TEMPLATE",version:"v4",rolePrefix:""}:xe={materialDetails:[te.reduce((Te,We)=>(Te[We]=at.map(le=>{var dt;return(dt=le[We])==null?void 0:dt.trim()}).filter(le=>le!=="").join(",")||"",Te),{})],templateHeaders:"",requestId:ce,templateName:Se!=null&&Se.TemplateName?Se.TemplateName:"",dtName:"MDG_MAT_CHANGE_TEMPLATE",version:"v4",rolePrefix:""};const B=Te=>{const We=URL.createObjectURL(Te),le=document.createElement("a");le.href=We,le.setAttribute("download",`${Se.TemplateName}_Mass Change.xlsx`),document.body.appendChild(le),le.click(),document.body.removeChild(le),URL.revokeObjectURL(We),H(!1),O(""),j(!0),L(`${Se.TemplateName}_Mass Change.xlsx has been downloaded successfully.`),T("success"),ot(),setTimeout(()=>{Bt("/requestBench")},2400)},de=()=>{H(!1)};Xe(`/${Re}${Fe.EXCEL.DOWNLOAD_EXCEL_WITH_DATA}`,"postandgetblob",B,de,xe)},ot=()=>{C(!0)},Dt=()=>{C(!1)},Rs=()=>{U(!1),N("systemGenerated")},ae=te=>{var xe;N((xe=te==null?void 0:te.target)==null?void 0:xe.value)},$e=()=>{x==="systemGenerated"&&(es(),Rs()),x==="mailGenerated"&&Rs()};d.useEffect(()=>{var te;(te=l==null?void 0:l[X.MATERIAL_TYPE])!=null&&te.code&&Ne("",!0)},[]);const me=te=>{var de;const xe=(de=te.target.value)==null?void 0:de.toUpperCase();io({code:xe,desc:""}),Us(0),Ws&&clearTimeout(Ws);const B=setTimeout(()=>{var w,Te,We;(Te=(w=l==null?void 0:l[X.MATERIAL_TYPE])==null?void 0:w[0])!=null&&Te.code&&Ne(xe,!0,(We=l==null?void 0:l[X.MATERIAL_TYPE])==null?void 0:We.code)},500);ks(B)},Ne=(te="",xe=!1,B)=>{var We,le,dt,ze,Qe,Ct,_e;Ht(ut=>({...ut,[X.MATERIAL_NUM]:!0}));const de={matlType:(((We=B==null?void 0:B[0])==null?void 0:We.code)||((dt=(le=l==null?void 0:l[X.MATERIAL_TYPE])==null?void 0:le[0])==null?void 0:dt.code))??"",materialNo:te??"",top:Wt,skip:xe?0:Ke,salesOrg:((Qe=(ze=q==null?void 0:q.uniqueSalesOrgList)==null?void 0:ze.map(ut=>ut.code))==null?void 0:Qe.join("$^$"))||""},w=ut=>{(ut==null?void 0:ut.statusCode)===Jt.STATUS_200&&(Fs((ut==null?void 0:ut.count)||0),xe?(I((ut==null?void 0:ut.body)||[]),F(ys=>({...ys,[X.MATERIAL_NUM]:ut.body||[]}))):(I(ys=>[...ys,...(ut==null?void 0:ut.body)||[]]),F(ys=>({...ys,[X.MATERIAL_NUM]:[...ys[X.MATERIAL_NUM]||[],...ut.body||[]]}))),Ht(ys=>({...ys,[X.MATERIAL_NUM]:!1})))},Te=ut=>{rs(ut),Ht(ys=>({...ys,[X.MATERIAL_NUM]:!1}))};Xe(`/${Re}${(_e=(Ct=Fe)==null?void 0:Ct.DATA)==null?void 0:_e.GET_SEARCH_PARAMS_MATERIAL_NO}`,"post",w,Te,de)};d.useEffect(()=>{Ke>0&&Ne(ss==null?void 0:ss.code,!1)},[Ke]);const Ve=te=>{var w;const{scrollTop:xe,scrollHeight:B,clientHeight:de}=te.target;xe+de>=B-10&&!fs[X.MATERIAL_NUM]&&((w=qe==null?void 0:qe[X.MATERIAL_NUM])==null?void 0:w.length)<$s&&Us(Te=>Te+Wt)};d.useEffect(()=>{r==null||r.forEach(te=>{var xe,B,de,w;te.key===((xe=X)==null?void 0:xe.MRP_CTRLER)&&(c!=null&&c.MrpCtrler)?F(Te=>{var We;return{...Te,[(We=X)==null?void 0:We.MRP_CTRLER]:c.MrpCtrler}}):[(B=X)==null?void 0:B.PLANT,(de=X)==null?void 0:de.SALES_ORG,(w=X)==null?void 0:w.WAREHOUSE].includes(te.key)&&Vt(te.key)})},[]),d.useEffect(()=>{ct()},[]),d.useEffect(()=>{var te;l[(te=X)==null?void 0:te.SALES_ORG]&&ct()},[l[(je=X)==null?void 0:je.SALES_ORG]]),d.useEffect(()=>{var te;l[(te=X)==null?void 0:te.PLANT]&&ke()},[l[(Pe=X)==null?void 0:Pe.PLANT]]);const Oe=async()=>{m(l,"0",te=>{ne(te),f(!0),te&&te.length>0&&t()})},ct=()=>{var de;Ht(w=>({...w,[X.DIST_CHNL]:!0}));let te={salesOrg:l[X.SALES_ORG]?(de=l[X.SALES_ORG])==null?void 0:de.map(w=>w==null?void 0:w.code).join("$^$"):""};const xe=w=>{F(Te=>({...Te,[X.DIST_CHNL]:w.body})),Me(xo({keyName:"StoreLoc",data:qe==null?void 0:qe[X.DIST_CHNL]})),Ht(Te=>({...Te,[X.DIST_CHNL]:!1}))},B=w=>{console.error(w),Ht(Te=>({...Te,[X.DIST_CHNL]:!1}))};Xe(`/${Re}/data/getDistrChan`,"post",xe,B,te)},ke=()=>{var de,w;Ht(Te=>({...Te,[X.STORAGE_LOC]:!0})),l[X.SALES_ORG]&&((de=l[X.SALES_ORG])==null||de.map(Te=>Te==null?void 0:Te.code).join("$^$"));const te=Te=>{F(We=>({...We,[X.STORAGE_LOC]:Te.body})),Me(xo({keyName:"DistrChan",data:qe==null?void 0:qe[X.STORAGE_LOC]})),Ht(We=>({...We,[X.STORAGE_LOC]:!1}))},xe=Te=>{console.error(Te),Ht(We=>({...We,[X.STORAGE_LOC]:!1}))},B=(w=l[X.PLANT])==null?void 0:w.map(Te=>Te.code).join(",");Xe(`/${Re}${Fe.DATA.GET_STORAGE_LOCATION_SET_BASED_ON_PLANT}`,"post",te,xe,{plant:B})},Vt=te=>{Ht(w=>({...w,[te]:!0}));const xe={[X.PLANT]:"/getPlant",[X.SALES_ORG]:"/getSalesOrg",[X.WAREHOUSE]:"/getWareHouseNo"},B=w=>{F(Te=>({...Te,[te]:w.body})),Me(xo({keyName:te,data:w==null?void 0:w.body})),Ht(Te=>({...Te,[te]:!1}))},de=w=>{console.log(w),Ht(Te=>({...Te,[te]:!1}))};Xe(`/${Re}/data${xe[te]}`,"get",B,de)},Es=te=>{var B,de;const xe=w=>w.code&&w.desc?`${w.code} - ${w.desc}`:w.code||"";if(te.key===X.MATERIAL_TYPE)return n(ha,{param:te,dropDownData:{[X.MATERIAL_TYPE]:te.options},allDropDownData:c,selectedValues:l,inputState:ss,handleSelectAll:Ts,handleSelectionChange:st,dropdownRef:Qt,errors:g,formatOptionLabel:xe,handlePopoverOpen:Mt,handlePopoverClose:gs,handleMouseEnterPopover:Lt,handleMouseLeavePopover:ps,isPopoverVisible:He,popoverId:Bs,popoverAnchorEl:ns,popoverRef:Ys,popoverContent:xs,isLoading:fs[te.key],singleSelect:!0});if(te.key===X.MATERIAL_NUM)return n(tf,{param:te,dropDownData:qe,allDropDownData:c,selectedValues:l,inputState:ss,handleSelectAll:Ts,handleSelectionChange:st,handleMatInputChange:me,handleScroll:Ve,dropdownRef:Qt,errors:g,formatOptionLabel:xe,handlePopoverOpen:Mt,handlePopoverClose:gs,handleMouseEnterPopover:Lt,handleMouseLeavePopover:ps,isPopoverVisible:He,popoverId:Bs,popoverAnchorEl:ns,popoverRef:Ys,popoverContent:xs,isMaterialNum:!0,isLoading:fs[X.MATERIAL_NUM],hasMoreItems:$s>(((B=qe==null?void 0:qe[X.MATERIAL_NUM])==null?void 0:B.length)||0),totalCount:$s,loadedCount:((de=qe==null?void 0:qe[X.MATERIAL_NUM])==null?void 0:de.length)||0});if(te.key===X.PLANT||te.key===X.SALES_ORG||te.key===X.MRP_CTRLER||te.key===X.DIVISION||te.key===X.WAREHOUSE||te.key===X.DIST_CHNL||te.key===X.STORAGE_LOC)return n(ha,{param:te,dropDownData:qe,allDropDownData:c,selectedValues:l,inputState:ss,handleSelectAll:Ts,handleSelectionChange:st,dropdownRef:Qt,errors:g,formatOptionLabel:xe,handlePopoverOpen:Mt,handlePopoverClose:gs,handleMouseEnterPopover:Lt,handleMouseLeavePopover:ps,isPopoverVisible:He,popoverId:Bs,popoverAnchorEl:ns,popoverRef:Ys,popoverContent:xs,isLoading:fs[te.key]})},Pt=()=>Object.values(l).some(te=>Array.isArray(te)&&te.length>0);return R(Ss,{children:[R(cn,{open:e,TransitionComponent:pA,keepMounted:!0,onClose:()=>{},maxWidth:s==="Extend"?"lg":"xs",fullWidth:!0,children:[R(ye,{sx:{backgroundColor:"#e3f2fd",padding:"1rem 1.5rem",display:"flex",alignItems:"center"},children:[n(La,{color:"primary",sx:{marginRight:"0.5rem"}}),R(mt,{variant:"h6",component:"div",color:"primary",children:[o," Search Filter(s)"]})]}),R(Hl,{value:it,onChange:ls,sx:{borderBottom:1,borderColor:"divider"},children:[n(jn,{label:"Search Filter"}),s!=="Extend"&&n(jn,{label:R(ye,{display:"flex",alignItems:"center",children:[n("span",{children:"Copy Material"}),it===1&&n(Ms,{title:"Export Table",children:n(Ds,{sx:{padding:"4px",width:"28px",height:"28px"},onClick:Ye.convertJsonToExcel,children:n(Ti,{iconName:"IosShare"})})})]})})]}),R(Lo,{sx:{padding:"1.5rem 1.5rem 1rem"},children:[it===0&&n(Ss,{children:n(ye,{sx:{display:"grid",gridTemplateColumns:"repeat(3, 1fr)",gap:2},children:r==null?void 0:r.map(te=>n(ye,{sx:{marginBottom:"1rem"},children:Es(te)},te.key))})}),it===1&&n(ye,{children:n(qr,{style:{height:400,width:"100%"},rows:at,columns:Hs})}),Ue&&R(mt,{variant:"h6",color:(jt=(_t=Be)==null?void 0:_t.error)==null?void 0:jt.dark,children:["* ",ge]}),n(Cn,{blurLoading:$})]}),R(Do,{sx:{padding:"0.5rem 1.5rem",display:"flex",justifyContent:"space-between"},children:[n("div",{children:n(mt,{variant:"h6",color:(Ft=(_s=Be)==null?void 0:_s.error)==null?void 0:Ft.dark,children:J&&(he==null?void 0:he.length)===0?dn.DATA_NOT_FOUND_FOR_SEARCH:""})}),R("div",{style:{display:"flex",gap:"8px"},children:[n(It,{onClick:t,color:"error",variant:"outlined",sx:{height:36,minWidth:"3.5rem",textTransform:"none",borderColor:"#cc3300",fontWeight:500},children:"Cancel"}),n(It,{onClick:Oe,variant:"contained",disabled:!Pt(),sx:{height:36,minWidth:"3.5rem",backgroundColor:"#3B30C8",textTransform:"none",fontWeight:500,"&:hover":{backgroundColor:"#2c278f"}},children:"OK"})]})]})]}),R(cn,{open:k,onClose:Rs,children:[n(Pn,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:n(mt,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:"Select Download Option"})}),n(Lo,{children:n(Li,{children:R(Sa,{row:!0,"aria-labelledby":"demo-row-radio-buttons-group-label",name:"row-radio-buttons-group",value:x,onChange:ae,children:[n(ao,{arrow:!0,placement:"bottom",title:n("span",{style:{whiteSpace:"nowrap",fontSize:"12px",overflow:"hidden",textOverflow:"ellipsis"},children:"Here Excel will be downloaded"}),children:n(Sr,{value:"systemGenerated",control:n(Pr,{}),label:"System-Generated"})}),n(ao,{arrow:!0,placement:"bottom",title:n("span",{style:{whiteSpace:"nowrap",fontSize:"12px",overflow:"hidden",textOverflow:"ellipsis"},children:"Here Excel will be sent to your email"}),children:n(Sr,{value:"mailGenerated",control:n(Pr,{}),label:"Mail-Generated"})})]})})}),n(Do,{children:n(It,{variant:"contained",onClick:$e,children:"OK"})})]}),n(Cn,{blurLoading:Q,loaderMessage:A}),z&&n(Un,{openSnackBar:v,alertMsg:S,alertType:p,handleSnackBarClose:Dt})]})},EA=({openSearchMat:e,setOpenSearchMat:t,AddCopiedMaterial:r})=>{const[o,c]=d.useState(!1),s=oe(u=>u.AllDropDown.dropDown),m={Extend:[{key:"Material Type",options:Ai},{key:"Material Number",options:[]},{key:"Plant",options:[]},{key:"Sales Org",options:[]},{key:"Distribution Channel",options:[]},{key:"Storage Location",options:[]},{key:"Division",options:[]}]},l=(u,se="0",ie)=>{var Q,H,he,ne,J,f,z,j,p,T,v,C;const q={materialNo:((H=(Q=u==null?void 0:u["Material Number"])==null?void 0:Q.map(S=>S.code))==null?void 0:H.join(","))??"",division:((ne=(he=u==null?void 0:u.Division)==null?void 0:he.map(S=>S.code))==null?void 0:ne.join(","))??"",plant:((f=(J=u==null?void 0:u.Plant)==null?void 0:J.map(S=>S.code))==null?void 0:f.join(","))??"",salesOrg:((j=(z=u==null?void 0:u["Sales Org"])==null?void 0:z.map(S=>S.code))==null?void 0:j.join(","))??"",distrChan:((T=(p=u==null?void 0:u["Distribution Channel"])==null?void 0:p.map(S=>S.code))==null?void 0:T.join(","))??"",storageLocation:((C=(v=u==null?void 0:u["Storage Location"])==null?void 0:v.map(S=>S.code))==null?void 0:C.join(","))??"",top:200,skip:se},g=S=>{var L;if((S==null?void 0:S.statusCode)===Jt.STATUS_200){const A=(L=S==null?void 0:S.body)==null?void 0:L.map(O=>{if(O.Views){const k=O.Views.split(",").map(U=>U.trim()).filter(U=>!Ui.includes(U)).join(",");return{...O,Views:k}}return O});r(A||[]),ie==null||ie(A||[]),c(!1)}},V=()=>{c(!1),ie==null||ie([])};c(!0),Xe(`/${Re}${Fe.DATA.GET_EXTEND_SEARCH_SET}`,"post",g,V,q)};return R(Ss,{children:[n(TA,{open:e,onClose:()=>t(!1),parameters:m.Extend,onSearch:(u,se,ie)=>l(u,se,ie),templateName:"Extend",name:"Extend",allDropDownData:s,buttonName:"Search"}),n(Cn,{blurLoading:o})]})};var rc={},mA=Go;Object.defineProperty(rc,"__esModule",{value:!0});var ba=rc.default=void 0,CA=mA(Bo()),Sh=$o;ba=rc.default=(0,CA.default)([(0,Sh.jsx)("path",{d:"M15.5 5H11l5 7-5 7h4.5l5-7z"},"0"),(0,Sh.jsx)("path",{d:"M8.5 5H4l5 7-5 7h4.5l5-7z"},"1")],"DoubleArrow");const lc=(e,t,r,o)=>{const[c,s]=d.useState([]),[m,l]=d.useState([]),u=oe(ne=>ne.payload.payloadData),ie=new URLSearchParams(location.search).get("RequestType"),q=oe(ne=>{var J,f;return(f=(J=ne==null?void 0:ne.userManagement)==null?void 0:J.taskData)==null?void 0:f.ATTRIBUTE_2}),[g,V]=d.useState(!1),{customError:Q}=sn(),H=(u==null?void 0:u.RequestType)||q||ie;d.useEffect(()=>{H&&he()},[e,H]);const he=()=>{const ne={decisionTableId:null,decisionTableName:"MDG_MAT_DYN_BUTTON_CONFIG",version:"v3",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_DYN_BTN_MODULE_NAME":"Material","MDG_CONDITIONS.MDG_MAT_DYN_BTN_REQUEST_TYPE":H}],systemFilters:null,systemOrders:null,filterString:null},J=j=>{var p,T;if(j.statusCode===Jt.STATUS_200){const v=Bl(mr.CURRENT_TASK,!0,{}),C=(e==null?void 0:e.taskDesc)||(v==null?void 0:v.taskDesc),L=(((T=(p=j==null?void 0:j.data)==null?void 0:p.result[0])==null?void 0:T.MDG_MAT_DYN_BUTTON_CONFIG)||[]).filter(O=>(O==null?void 0:O.MDG_MAT_DYN_BTN_TASK_NAME)===(C??ca.INITIATOR));s(L),(L.find(O=>O.MDG_MAT_DYN_BTN_BUTTON_NAME===o.SEND_BACK)||L.find(O=>O.MDG_MAT_DYN_BTN_BUTTON_NAME===o.CORRECTION))&&V(!0)}},f=j=>{Q("Dynamic Button Fetch Error:",j)},z=t.environment==="localhost"?`/${r}${Fe.INVOKE_RULES.LOCAL}`:`/${r}${Fe.INVOKE_RULES.PROD}`;Xe(z,"post",J,f,ne)};return d.useEffect(()=>{const ne=[...c].sort((J,f)=>{const z=dh[J.MDG_MAT_DYN_BTN_BUTTON_NAME]??999,j=dh[f.MDG_MAT_DYN_BTN_BUTTON_NAME]??999;return z-j});l(ne)},[c]),{extendFilteredButtons:m,showWfLevels:g}},AA=Jh(()=>({customTabs:{"& .MuiTabs-scroller":{overflowX:"auto !important",overflowY:"hidden !important"}}})),mn={NOT_EXTENDED:"notExtended",EXTENDED:"Extended"},bA=e=>{var Nn,Yn,rr,lr,ir;const t=AA(),{customError:r}=sn(),o=Ro(),{fetchMaterialFieldConfig:c}=Da(),{getNextDisplayDataForCreate:s}=Gi(),{fetchValuationClassData:m}=lf(),l=oe(E=>E.payload.payloadData),u=l==null?void 0:l.RequestType,se=oe(E=>E.applicationConfig),ie=oe(E=>E.paginationData),q=oe(E=>E.payload),g=oe(E=>E.request.materialRows),V=oe(E=>{var _;return((_=E.AllDropDown)==null?void 0:_.dropDown)||{}}),Q=oe(E=>E.tabsData.allTabsData);let H=oe(E=>E.userManagement.taskData);const he=oe(E=>E.tabsData.allMaterialFieldConfigDT),ne=tn(),J=new URLSearchParams(ne.search),f=J.get("RequestId"),z=J.get("RequestType"),[j,p]=d.useState(0),[T,v]=d.useState(null),[C,S]=d.useState(null),L="Basic Data",[A,O]=d.useState([L]),[k,U]=d.useState([]),[x,N]=d.useState(g||[]),D=oe(E=>E.selectedSections.selectedSections),[I,ge]=d.useState(!1),[ee,Ue]=d.useState(!1),[M,Se]=d.useState(""),[ce,$]=d.useState([]),[qe,F]=d.useState(0),[Ke,Us]=d.useState({code:"",desc:""}),[Wt,$s]=d.useState(!1),{fetchDataAndDispatch:Fs}=Bi(),[ss,io]=d.useState(!0),[Ws,ks]=d.useState(x.length+1),[Qt,fs]=d.useState(0),[Ht,ns]=d.useState(g.length>0),[Zt,xs]=d.useState({}),[lt,He]=d.useState({}),[Rt,Ys]=d.useState([]),[Bt,Me]=d.useState({}),[it,Ut]=d.useState([]),[Ns,ds]=d.useState(!1),[rs,at]=d.useState(""),[yt,ws]=d.useState("Basic Data"),[Hs,os]=d.useState(!1),[ls,ao]=d.useState(null),Ye=oe(E=>E.request.salesOrgDTData),Mt=(Nn=q==null?void 0:q[ls])==null?void 0:Nn.headerData,gs=(l==null?void 0:l.Region)===Uo.EUR?{id:0,salesOrg:null,dc:{value:null,options:[]},plant:{value:null,options:[]},sloc:{value:null,options:[]},mrpProfile:null}:{id:0,salesOrg:null,dc:{value:null,options:[]},plant:{value:null,options:[]},sloc:{value:null,options:[]},warehouse:{value:null,options:[]},mrpProfile:null},[Lt,ps]=d.useState([gs]),[Js,Bs]=d.useState([]),[st,Ts]=d.useState({id:1,plant:{value:null,options:[]},salesOrg:null,dc:{value:null,options:[]},sloc:{value:null,options:[]},mrpProfile:null,warehouse:{value:null,options:[]}}),[to,St]=d.useState(!1),[es,ot]=d.useState({}),[Dt,Rs]=d.useState("success"),ae=(Yn=q==null?void 0:q[ls])==null?void 0:Yn.payloadData,[$e,me]=d.useState(!1),[Ne,Ve]=d.useState([]),[Oe,ct]=d.useState(""),[ke,Vt]=d.useState([]),{getDynamicWorkflowDT:Es}=Ha(),[Pt,je]=d.useState(!1),[Pe,_t]=d.useState(!1),[jt,_s]=d.useState(!1),[Ft,te]=d.useState(""),[xe,B]=d.useState({"Sales Organization":!1,"Distribution Channel":{},Plant:{},"Storage Location":{},warehouse:{},"Mrp Profile":!1}),[de,w]=d.useState(!1),[Te,We]=d.useState(0),{fetchTabSpecificData:le}=cf(),{getContryBasedOnPlant:dt}=df({doAjax:Xe,customError:r,fetchDataAndDispatch:Fs,destination_MaterialMgmt:Re}),{extendFilteredButtons:ze,showWfLevels:Qe}=lc(H,se,yo,go),Ct=Cr(ze,[To.HANDLE_SUBMIT_FOR_APPROVAL,To.HANDLE_SAP_SYNDICATION,To.HANDLE_SUBMIT_FOR_REVIEW,To.HANDLE_SUBMIT]),_e=E=>{!E||!Array.isArray(E)||E.forEach(_=>{var W,re,Ae,Ge,Ze,et,wt,Tt,Kt,tt,hs,eo,zt,oo,no,Vo;if((re=(W=_.plant)==null?void 0:W.value)!=null&&re.code){if(le((Ge=(Ae=_.plant)==null?void 0:Ae.value)==null?void 0:Ge.code,P.PLANT),(Ze=_.salesOrg)!=null&&Ze.code||(wt=(et=_.dc)==null?void 0:et.value)!=null&&wt.code){const pn=`${((Tt=_.salesOrg)==null?void 0:Tt.code)||""}-${((tt=(Kt=_.dc)==null?void 0:Kt.value)==null?void 0:tt.code)||""}`;le(pn,P.SALES)}(eo=(hs=_.warehouse)==null?void 0:hs.value)!=null&&eo.code&&le((oo=(zt=_.warehouse)==null?void 0:zt.value)==null?void 0:oo.code,P.WAREHOUSE),dt((Vo=(no=_.plant)==null?void 0:no.value)==null?void 0:Vo.code)}})},ut=E=>{if(!E||!Array.isArray(E))return[];let _=(l==null?void 0:l.Region)===Uo.EUR?E==null?void 0:E.filter(W=>W!==P.WAREHOUSE&&W!==P.WORKSCHEDULING&&W!==P.WORK_SCHEDULING):[...E];return _.sort((W,re)=>W===P.BASIC_DATA?-1:re===P.BASIC_DATA?1:0),_},ys=async()=>{var E,_;try{const W=await Es(u,l==null?void 0:l.Region,"",(_=(E=q[ls])==null?void 0:E.Tochildrequestheaderdata)==null?void 0:_.MaterialGroupType,H==null?void 0:H.ATTRIBUTE_3);Vt(W)}catch(W){r(W)}};d.useEffect(()=>{u&&(l!=null&&l.Region)&&ls&&(H!=null&&H.ATTRIBUTE_3)&&ys()},[u,l==null?void 0:l.Region,ls,H==null?void 0:H.ATTRIBUTE_3]),d.useEffect(()=>{var E,_,W,re,Ae,Ge,Ze,et,wt,Tt,Kt,tt,hs,eo,zt;if(N(g),ns((g==null?void 0:g.length)>0),(g==null?void 0:g.length)>0){ao((E=g==null?void 0:g[0])==null?void 0:E.id),Gs(((W=(_=g==null?void 0:g[0])==null?void 0:_.materialType)==null?void 0:W.code)||((re=g==null?void 0:g[0])==null?void 0:re.materialType)),fs(0),te((Ae=g==null?void 0:g[0])==null?void 0:Ae.materialNumber),ws(P.BASIC_DATA),O((Ze=(Ge=g==null?void 0:g[0])==null?void 0:Ge.views)!=null&&Ze.length?ut((et=g==null?void 0:g[0])==null?void 0:et.views):ut([L]));const oo=Hh(q),no=Bh(oo);let Vo=JSON.parse(JSON.stringify(no));o(Gh(Vo)),o(kl({keyName:"selectedMaterialID",data:(wt=g==null?void 0:g[0])==null?void 0:wt.id})),(tt=(Kt=q==null?void 0:q[(Tt=g==null?void 0:g[0])==null?void 0:Tt.id])==null?void 0:Kt.Tochildrequestheaderdata)!=null&&tt.ChildRequestId&&o(kl({keyName:"childRequestId",data:(zt=(eo=q==null?void 0:q[(hs=g==null?void 0:g[0])==null?void 0:hs.id])==null?void 0:eo.Tochildrequestheaderdata)==null?void 0:zt.ChildRequestId}))}},[g]),d.useEffect(()=>{(x==null?void 0:x.length)===0&&ge(!1)},[x]),d.useEffect(()=>{["Sales Organization","Mrp Profile"].forEach(Xo)},[]),d.useEffect(()=>{if(f){const E=Mt==null?void 0:Mt.orgData;(E==null?void 0:E.length)>0&&E.some(_=>{var W,re,Ae,Ge,Ze;return((re=(W=_.plant)==null?void 0:W.value)==null?void 0:re.code)&&(((Ae=_.salesOrg)==null?void 0:Ae.code)||((Ze=(Ge=_.dc)==null?void 0:Ge.value)==null?void 0:Ze.code))})&&_e(E)}},[Mt==null?void 0:Mt.orgData]),d.useEffect(()=>{var Ae,Ge,Ze,et;const E=A==null?void 0:A.includes((Ae=P)==null?void 0:Ae.SALES),_=A==null?void 0:A.includes((Ge=P)==null?void 0:Ge.SALES_PLANT),W=A==null?void 0:A.includes((Ze=P)==null?void 0:Ze.STORAGE),re=A==null?void 0:A.includes((et=P)==null?void 0:et.STORAGE_PLANT);E&&!_&&O(wt=>{var tt,hs;const Tt=[...wt],Kt=Tt.indexOf((tt=P)==null?void 0:tt.SALES);return Tt.splice(Kt+1,0,(hs=P)==null?void 0:hs.SALES_PLANT),Tt}),W&&!re&&O(wt=>{var tt,hs;const Tt=[...wt],Kt=Tt.indexOf((tt=P)==null?void 0:tt.STORAGE);return Tt.splice(Kt+1,0,(hs=P)==null?void 0:hs.STORAGE_PLANT),Tt})},[A]);const Xs=()=>{Io()},uo=(E="",_=!1)=>{var Ge,Ze,et,wt;const W={materialNo:E??"",top:500,skip:_?0:qe,salesOrg:((Ze=(Ge=Ye==null?void 0:Ye.uniqueSalesOrgList)==null?void 0:Ge.map(Tt=>Tt.code))==null?void 0:Ze.join("$^$"))||""},re=Tt=>{(Tt==null?void 0:Tt.statusCode)===Jt.STATUS_200&&($(_?Tt==null?void 0:Tt.body:Kt=>[...Kt,...Tt==null?void 0:Tt.body]),$s(!1))},Ae=()=>{$s(!1)};$s(!0),Xe(`/${Re}${(wt=(et=Fe)==null?void 0:et.DATA)==null?void 0:wt.GET_SEARCH_PARAMS_MATERIAL_NO}`,"post",re,Ae,W)},us=!ko.includes(e==null?void 0:e.requestStatus),co=E=>{const _=re=>{(re==null?void 0:re.statusCode)===Jt.STATUS_200&&Ys(re==null?void 0:re.body)},W=re=>{console.error(re,"while fetching the validation data of material number")};Xe(`/${Re}/data/getNumberRangeForMaterialType?materialType=${E==null?void 0:E.code}`,"get",_,W)};function Po(E){const _=re=>{var Ae;if((re==null?void 0:re.statusCode)===Jt.STATUS_200){let Ge=(Ae=re==null?void 0:re.body)==null?void 0:Ae.filter(Ze=>!Ui.includes(Ze));Ge=Ge==null?void 0:Ge.map(Ze=>Ze==="Storage"?P.STORAGE:Ze),(l==null?void 0:l.Region)===Uo.EUR&&(Ge=Ge==null?void 0:Ge.filter(Ze=>Ze!==P.WAREHOUSE&&Ze!==P.WORK_SCHEDULING&&Ze!==P.WORKSCHEDULING)),Ut(Ge)}},W=re=>{r(re)};Xe(`/${Re}/data/getViewForMaterialType?materialType=${E}`,"get",_,W)}d.useEffect(()=>{uo()},[]);const Qs=((rr=Rt==null?void 0:Rt[1])==null?void 0:rr.External)==="X",Ot=Rt==null?void 0:Rt.some(E=>E.ExtNAwock==="X");function Gs(E){var re;const _=(l==null?void 0:l.Region)||Uo.US;if(!he.some(Ae=>Ae[_]&&Ae[_][E])&&E)c(E,_);else if(!E)o(kr({}));else{const Ae=he==null?void 0:he.find(Ge=>(Ge==null?void 0:Ge[_])&&(Ge==null?void 0:Ge[_][E]));Ae&&o(kr((re=Ae[_][E])==null?void 0:re.allfields))}E&&m(E)}const Zs=E=>{const{id:_,field:W,value:re}=E,Ae=x.map(Ge=>Ge.id===_?{...Ge,[W]:re}:Ge);Me({...Bt,[W]:re}),W===xr.MATERIALTYPE&&(co(re),Po(re),O([L]),$h([gs]),o(Bn({materialID:_,keyName:"views",data:[L]})),o(Bn({materialID:_,keyName:"orgData",data:""})),Gs(re==null?void 0:re.code)),N(Ae),o(Bn({materialID:_,keyName:W,data:re}))},_o=E=>{var _,W,re,Ae,Ge,Ze,et,wt,Tt;ao(E.row.id),ot(E.row),te(E.row.materialNumber),Ut((_=E==null?void 0:E.row)==null?void 0:_.views),Gs(((re=(W=E==null?void 0:E.row)==null?void 0:W.materialType)==null?void 0:re.code)||((Ae=E.row)==null?void 0:Ae.materialType)),O((Ge=E==null?void 0:E.row)!=null&&Ge.views?(Ze=E.row)==null?void 0:Ze.views:[L]),ps((wt=(et=E==null?void 0:E.row)==null?void 0:et.orgData)!=null&&wt.length?(Tt=E.row)==null?void 0:Tt.orgData:[gs]),fs(0),ws("Basic Data")},Fo=()=>{Ue(!0)},Io=()=>{Ue(!1)},ho=(E,_)=>{_==="backdropClick"||_==="escapeKeyDown"||os(!1)},is=()=>O(ut(it)),Wo=E=>{if(St(!1),E!=null&&E.length){let _=[...x];E==null||E.forEach(W=>{var Ze,et,wt;const re=W==null?void 0:W.Material;let Ae={...W},Ge=(Ze=q==null?void 0:q[W.id])!=null&&Ze.payloadData?JSON.parse(JSON.stringify((et=q==null?void 0:q[W.id])==null?void 0:et.payloadData)):"";Ae.id=re,Ae.globalMaterialDescription="",Ae.materialNumber="",Ae.included=!0,Ae.industrySector=W==null?void 0:W.IndSector,Ae.materialType=W==null?void 0:W.MatlType,Ae.materialNumber=W==null?void 0:W.Material,Ae.globalMaterialDescription=W==null?void 0:W.MaterialDescrption,Ae.views=W!=null&&W.Views?(wt=W==null?void 0:W.Views.split(","))==null?void 0:wt.map(Tt=>Tt.trim()==="Storage"?P.STORAGE:Tt.trim()):[L],Ae.validated=!1,_.push(Ae),o(gi({materialID:re,data:Ae,payloadData:Ge}))}),U(W=>[...W,..._.map(re=>({material:re==null?void 0:re.Material,views:re==null?void 0:re.views}))]),N(_),o(Oo(_)),ks(Ws+1),ns(!0),io(!0)}},so=[{field:"included",headerName:"Included",flex:.5,align:"center",headerAlign:"center",renderCell:E=>{var _;return E!=null&&E.row?n(Hn,{checked:(_=E==null?void 0:E.row)==null?void 0:_.included,disabled:us,onChange:W=>{var re;(re=E==null?void 0:E.row)!=null&&re.id&&Zs({id:E.row.id,field:"included",value:W.target.checked})}}):null}},{field:"lineNumber",headerName:"Line Number",flex:.6,editable:u==="Create",align:"center",headerAlign:"center",renderCell:E=>{const W=((g==null?void 0:g.findIndex(re=>{var Ae;return(re==null?void 0:re.id)===((Ae=E==null?void 0:E.row)==null?void 0:Ae.id)}))+1)*10;return n("div",{children:W})}},{field:"industrySector",headerName:"Industry Sector",flex:1,align:"center",headerAlign:"center",renderCell:E=>{var _,W,re,Ae,Ge;return n(lo,{options:(V==null?void 0:V.IndSector)||[],value:(_=E==null?void 0:E.row)==null?void 0:_.industrySector,onChange:Ze=>Zs({id:E.row.id,field:"industrySector",value:Ze}),placeholder:"Select Industry Sector",minWidth:"90%",disabled:!0,listWidth:232,title:`${((re=(W=E.row)==null?void 0:W.industrySector)==null?void 0:re.code)||""} - ${((Ge=(Ae=E.row)==null?void 0:Ae.industrySector)==null?void 0:Ge.desc)||""}`})}},{field:"materialType",headerName:"Material Type",flex:1,align:"center",headerAlign:"center",renderCell:E=>{var _,W,re,Ae,Ge;return n(lo,{options:Ai||[],value:(_=E==null?void 0:E.row)==null?void 0:_.materialType,onChange:Ze=>Zs({id:E.row.id,field:"materialType",value:Ze}),placeholder:"Select Material Type",disabled:!0,minWidth:"90%",listWidth:232,title:`${((re=(W=E.row)==null?void 0:W.materialType)==null?void 0:re.code)||""} - ${((Ge=(Ae=E.row)==null?void 0:Ae.materialType)==null?void 0:Ge.desc)||""}`})}},{field:"materialNumber",headerName:"Material Number",flex:u==="Extend"?.8:1,editable:!(!Qs&&!Ot),align:"center",headerAlign:"center",renderHeader:()=>R("span",{children:["Material Number",n("span",{style:{color:"red"},children:"*"})]}),renderCell:E=>{var _,W;return n(Ss,{children:(l==null?void 0:l.RequestType)===b.EXTEND?n(xn,{fullWidth:!0,placeholder:"Enter Material Number",variant:"outlined",size:"small",name:"material number",value:(_=E==null?void 0:E.row)==null?void 0:_.materialNumber,sx:{"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:Be.black.dark,color:Be.black.dark}}},onChange:(re,Ae)=>Zs({id:E.row.id,field:"materialNumber",value:Ae}),disabled:!Qs&&!Ot}):(W=E==null?void 0:E.row)==null?void 0:W.materialNumber})}},{field:"globalMaterialDescription",flex:u==="Extend"?.8:1,headerName:"Material Description",renderHeader:()=>R("span",{children:["Material Description",n("span",{style:{color:"red"},children:"*"})]}),renderCell:E=>{var _,W;return n(Ss,{children:(l==null?void 0:l.RequestType)===b.EXTEND?n(xn,{fullWidth:!0,placeholder:"Enter Material Description",variant:"outlined",disabled:!0,size:"small",name:"material description",sx:{"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:Be.black.dark,color:Be.black.dark}}},onChange:(re,Ae)=>Zs({id:E.row.id,field:"globalMaterialDescription",value:Ae}),value:(_=E==null?void 0:E.row)==null?void 0:_.globalMaterialDescription}):(W=E==null?void 0:E.row)==null?void 0:W.globalMaterialDescription})},align:"center",headerAlign:"center",editable:!0},{field:"views",headerName:"",flex:u==="Extend"?1.5:1,align:"center",headerAlign:"center",renderCell:E=>R(bo,{direction:"row",spacing:0,alignItems:"center",children:[n(It,{variant:"contained",size:"small",sx:{minWidth:80},onClick:()=>{var _,W;Po(E.row.materialType),ds(!0),at(E.row.id),ot(E.row),O((_=E==null?void 0:E.row)!=null&&_.Views?(W=E==null?void 0:E.row)==null?void 0:W.Views:[L])},children:"Views"}),n(ba,{color:"disabled",fontSize:"small",sx:{mx:.5}}),n(It,{variant:"contained",size:"small",sx:{minWidth:100},onClick:()=>{var _,W,re,Ae;os(!0),at(E.row.id),ps((W=(_=E==null?void 0:E.row)==null?void 0:_.orgData)!=null&&W.length?(re=E.row)==null?void 0:re.orgData:[gs]),ot(E.row),qo((Ae=E==null?void 0:E.row)==null?void 0:Ae.materialNumber,mn.NOT_EXTENDED),_t(!1)},children:"ORG Data"}),n(ba,{color:"disabled",fontSize:"small",sx:{mx:.5}}),n(Ms,{title:"Click after changing Views or ORG Data",children:n(Ds,{onClick:()=>{var _,W;os(!0),at(E.row.id),_t(!0),ot(E.row),qo((_=E==null?void 0:E.row)==null?void 0:_.materialNumber,mn.EXTENDED),Ts(Js.find(re=>{var Ae;return re.id===((Ae=E.row)==null?void 0:Ae.id)})||{id:(W=E.row)==null?void 0:W.id,plant:null,salesOrg:null,dc:{value:null,options:[]},sloc:{value:null,options:[]},mrpProfile:null,warehouse:null})},disabled:us,color:"primary",size:"small",children:n(bi,{})})})]})},{field:"action",headerName:"Action",flex:.9,align:"center",headerAlign:"center",renderCell:E=>n(bo,{direction:"row",alignItems:"center",sx:{marginLeft:"0.5rem",magrinRight:"0.5rem"},spacing:.5,children:!f&&n(Ms,{title:"Delete Row",children:n(Ds,{onClick:()=>{N(x.filter(_=>_.id!==E.row.id)),o(zh(E.row.id)),o(Oo(x.filter(_=>_.id!==E.row.id))),x!=null&&x.length||ge(!1)},color:"error",children:n(Ar,{})})})})}],qo=(E,_)=>{B(Ae=>({...Ae,"Sales Organization":!0}));const W=Ae=>{if((Ae==null?void 0:Ae.statusCode)===Jt.STATUS_200){let Ge;_===mn.NOT_EXTENDED?Ge=On(Ae.body):Ge=Ae.body.length>0?On(Ae.body):[],He(Ze=>({...Ze,"Sales Organization":Ge}))}B(Ge=>({...Ge,"Sales Organization":!1}))},re=()=>{B(Ae=>({...Ae,"Sales Organization":!1}))};Xe(`/${Re}/data/${_===mn.NOT_EXTENDED?"getSalesOrgNotExtended":"getSalesOrgExtended"}?materialNo=${E}&region=${l==null?void 0:l.Region}`,"get",W,re)},jo=(E,_,W,re)=>{B(et=>({...et,Plant:{...et.Plant,[re]:!0}}));const Ae=et=>{if((et==null?void 0:et.statusCode)===Jt.STATUS_200){let wt;_===mn.NOT_EXTENDED?wt=On(et.body):wt=et.body.length>0?On(et.body||[]):[],He(Tt=>({...Tt,Plant:wt}))}B(wt=>({...wt,Plant:{...wt.Plant,[re]:!1}}))},Ge=()=>{B(et=>({...et,Plant:{...et.Plant,[re]:!1}}))},Ze=W?`&salesOrg=${W.code}`:"";Xe(`/${Re}/data/${_===mn.NOT_EXTENDED?"getPlantNotExtended":"getPlantExtended"}?materialNo=${E}&region=${l==null?void 0:l.Region}${Ze}`,"get",Ae,Ge)},zo=(E,_,W,re)=>{B(et=>({...et,warehouse:{...et.warehouse,[re]:!0}}));const Ae=et=>{if((et==null?void 0:et.statusCode)===Jt.STATUS_200){let wt;_===mn.NOT_EXTENDED?wt=On(et.body):wt=et.body.length>0?On(et.body||[]):[],He(Tt=>({...Tt,warehouse:wt}))}B(wt=>({...wt,warehouse:{...wt.warehouse,[re]:!1}}))},Ge=()=>{B(et=>({...et,warehouse:{...et.warehouse,[re]:!1}}))},Ze=W?`&plant=${W.code}`:"";Xe(`/${Re}/data/${_===mn.NOT_EXTENDED?"getWarehouseNotExtended":"getWarehouseExtended"}?materialNo=${E}&region=${l==null?void 0:l.Region}${Ze}`,"get",Ae,Ge)},Yo=(E,_)=>{var W;fs(_),ws(((W=E==null?void 0:E.target)==null?void 0:W.id)==="AdditionalKey"?"Additional Data":A==null?void 0:A[_])},Xo=E=>{B(Ae=>({...Ae,[E]:!0}));const _={"Sales Organization":"/getSalesOrg","Mrp Profile":"/getMRPProfile"},W=Ae=>{if((Ae==null?void 0:Ae.statusCode)===Jt.STATUS_200){const Ge=On(Ae.body);He(Ze=>({...Ze,[E]:Ge}))}B(Ge=>({...Ge,[E]:!1}))},re=Ae=>{console.error(Ae),B(Ge=>({...Ge,[E]:!1}))};Xe(`/${Re}/data${_[E]}`,"get",W,re)},pe=E=>{jh(E,A,ae,ls,Lt,o,Gn)},ht=(E,_,W)=>(re,Ae)=>{var tt,hs,eo;let Ge={},Ze="",et="";W==="Purchasing"||W==="Costing"?(Ge={materialNo:_==null?void 0:_.Material,plant:_==null?void 0:_.Plant},et=_==null?void 0:_.Plant,Ze=`/${Re}/data/displayLimitedPlantData`):W==="Accounting"?(Ge={materialNo:_==null?void 0:_.Material,valArea:_==null?void 0:_.ValArea},et=_==null?void 0:_.ValArea,Ze=`/${Re}/data/displayLimitedAccountingData`):W==="Sales"&&(Ge={materialNo:_==null?void 0:_.Material,salesOrg:_==null?void 0:_.SalesOrg,distChnl:_==null?void 0:_.DistrChan},et=`${_==null?void 0:_.SalesOrg}-${_==null?void 0:_.DistrChan}`,Ze=`/${Re}/data/displayLimitedSalesData`);const wt=zt=>{var oo,no,Vo;(zt==null?void 0:zt.statusCode)===Jt.STATUS_200&&(W==="Purchasing"||W==="Costing"?o(Gn({materialID:ls,viewID:W,itemID:_==null?void 0:_.Plant,data:(oo=zt==null?void 0:zt.body)==null?void 0:oo.SpecificPlantDataViewDto[0]})):W==="Accounting"?o(Gn({materialID:ls,viewID:W,itemID:_==null?void 0:_.ValArea,data:(no=zt==null?void 0:zt.body)==null?void 0:no.SpecificAccountingDataViewDto[0]})):W==="Sales"&&o(Gn({materialID:ls,viewID:W,itemID:`${_==null?void 0:_.SalesOrg}-${_==null?void 0:_.DistrChan}`,data:(Vo=zt==null?void 0:zt.body)==null?void 0:Vo.SpecificSalesDataViewDto[0]})))},Tt=()=>{};!((eo=(hs=(tt=q==null?void 0:q[ls])==null?void 0:tt.payloadData)==null?void 0:hs[W])!=null&&eo[et])&&Xe(Ze,"post",wt,Tt,Ge),S(Ae?E:null)},ft=()=>Q&&yt&&(Q[yt]||yt==="Additional Data")?yt==="Additional Data"?[n(Kh,{disabled:us,materialID:ls,selectedMaterialNumber:Ft})]:[n(Vh,{disabled:us,materialID:ls,basicData:Zt,setBasicData:xs,dropDownData:lt,allTabsData:Q,basicDataTabDetails:Q[yt],activeViewTab:yt,selectedViews:A,handleAccordionClick:ht,missingValidationPlant:Ne,selectedMaterialNumber:Ft,callGetCountryBasedonSalesOrg:jt})]:n(Ss,{}),At=E=>{const _=E.target.value;Us({code:_,desc:""}),F(0),T&&clearTimeout(T);const W=setTimeout(()=>{uo(_,!0)},500);v(W)};d.useEffect(()=>{qe>0&&uo(Ke==null?void 0:Ke.code)},[qe]);const ve=(E,_,W)=>{var re;if(E==="Sales Organization"){Nt(_,W);const Ae=(re=x==null?void 0:x.find(Ge=>Ge.id===rs))==null?void 0:re.materialNumber;jo(Ae,Pe?mn.EXTENDED:mn.NOT_EXTENDED,_,W)}},Nt=(E,_,W="",re="")=>(B(Ae=>({...Ae,"Distribution Channel":{...Ae["Distribution Channel"],[_]:!0}})),new Promise((Ae,Ge)=>{var Tt;const Ze=Kt=>{if(Kt.statusCode===Jt.STATUS_200){const tt=On(Kt.body);let hs=JSON.parse(JSON.stringify(W||Lt));Pe?Ts(eo=>({...eo,salesOrg:E,dc:{value:null,options:(tt==null?void 0:tt.length)>0?tt:[]}})):(hs[_].salesOrg=E,hs[_].dc.options=tt,ps(hs))}B(tt=>({...tt,"Distribution Channel":{...tt["Distribution Channel"],[_]:!1}})),Ae(Kt)},et=Kt=>{B(tt=>({...tt,"Distribution Channel":{...tt["Distribution Channel"],[_]:!1}})),Ge(Kt)};let wt=(Tt=x==null?void 0:x.find(Kt=>Kt.id===rs))==null?void 0:Tt.materialNumber;wt&&Xe(`/${Re}/data/${Pe?"getDistributionChannelExtended":"getDistributionChannelNotExtended"}?materialNo=${wt}&salesOrg=${E==null?void 0:E.code}`,"get",Ze,et)})),$t=(E,_)=>{var re;kt(E,_);const W=(re=x==null?void 0:x.find(Ae=>Ae.id===rs))==null?void 0:re.materialNumber;zo(W,Pe?mn.EXTENDED:mn.NOT_EXTENDED,E,_)},kt=(E,_,W="",re)=>{var Tt;B(Kt=>({...Kt,"Storage Location":{...Kt["Storage Location"],[_]:!0}}));const Ae=Kt=>{if(B(tt=>({...tt,"Storage Location":{...tt["Storage Location"],[_]:!1}})),Kt.statusCode===Jt.STATUS_200){const tt=On(Kt.body);let hs=JSON.parse(JSON.stringify(W||Lt));Pe?Ts(eo=>({...eo,plant:{value:E,options:[]},sloc:{value:null,options:(tt==null?void 0:tt.length)>0?tt:[]}})):(hs[_].plant.value=E,hs[_].sloc.options=tt,ps(hs))}if(re){o(Bn({materialID:re==null?void 0:re.id,keyName:"orgData",data:rowOption}));let tt=(x==null?void 0:x.length)||[JSON.parse(JSON.stringify(re))],hs=tt.findIndex(eo=>eo.id===(re==null?void 0:re.id));tt[hs].orgData=rowOption,o(Oo(tt))}},Ge=Kt=>{console.error(Kt),B(tt=>({...tt,"Storage Location":{...tt["Storage Location"],[_]:!1}}))};let Ze=(Tt=x.find(Kt=>Kt.id===rs))==null?void 0:Tt.materialNumber;const et=Lt[_],wt=et!=null&&et.salesOrg?`&salesOrg=${et.salesOrg.code}`:"";Ze&&Xe(`/${Re}/data/${Pe?"getStorageLocationExtended":"getStorageLocationNotExtended"}?materialNo=${Ze}&region=${l==null?void 0:l.Region}&plant=${E==null?void 0:E.code}${wt}`,"get",Ae,Ge)},as=(E,_)=>{let W=JSON.parse(JSON.stringify(Lt));W[_].dc.value=E,ps(W)},qs=E=>{let _=JSON.parse(JSON.stringify(Lt));_.splice(E,1),ps(_)},gt=(E,_)=>{let W=JSON.parse(JSON.stringify(Lt));W[_].sloc.value=E,ps(W)},Gt=(E,_)=>{let W=JSON.parse(JSON.stringify(Lt));W[_].warehouse.value=E,ps(W)},De=(E,_)=>{let W=JSON.parse(JSON.stringify(Lt));W[_].mrpProfile=E,ps(W)},pt=()=>{let E=JSON.parse(JSON.stringify(Lt));E.push({id:1,plant:{value:null,options:[]},salesOrg:null,dc:{value:null,options:[]},sloc:{value:null,options:[]},warehouse:{value:null,options:[]},mrpProfile:null}),ps(E)},ms=(E,_,W,re)=>{var et,wt,Tt,Kt,tt,hs,eo,zt,oo,no,Vo;const Ae={material:es==null?void 0:es.materialNumber,wareHouseNumber:((wt=(et=E==null?void 0:E.warehouse)==null?void 0:et.value)==null?void 0:wt.code)??"",plant:((Kt=(Tt=E==null?void 0:E.plant)==null?void 0:Tt.value)==null?void 0:Kt.code)??"",salesOrg:((tt=E==null?void 0:E.salesOrg)==null?void 0:tt.code)??"",storageLocation:((eo=(hs=E==null?void 0:E.sloc)==null?void 0:hs.value)==null?void 0:eo.code)??"",distributionChannel:((oo=(zt=E==null?void 0:E.dc)==null?void 0:zt.value)==null?void 0:oo.code)??"",valArea:((Vo=(no=E==null?void 0:E.plant)==null?void 0:no.value)==null?void 0:Vo.code)??""},Ge=pn=>{const ar=qp(pn==null?void 0:pn.body,_,W,re,es),Ir=u===b.EXTEND_WITH_UPLOAD||z===b.EXTEND_WITH_UPLOAD?Up(q,ar):kp(q,ar);o(Hp({data:Ir})),_s(!jt)},Ze=pn=>{r(pn)};Xe(`/${Re}${Fe.DATA.COPY_FROM_MATERIAL_ORG_ELMS_ETEXTEND}`,"post",Ge,Ze,Ae)},gn=[{id:1,plant:{value:null,options:[]},salesOrg:null,dc:{value:null,options:[]},sloc:{value:null,options:[]},warehouse:{value:null,options:[]},mrpProfile:null}],yn=E=>{o(fr(E)),p(E)};d.useEffect(()=>{var E,_;(ie==null?void 0:ie.page)!==0&&(u===((E=b)==null?void 0:E.EXTEND_WITH_UPLOAD)||u===((_=b)==null?void 0:_.EXTEND))&&s(),p((ie==null?void 0:ie.page)||0)},[ie==null?void 0:ie.page]);const Sn=Pe?gn:Lt;return R("div",{children:[n("div",{style:{padding:"0",width:"100%",margin:"0"},children:R("div",{style:{height:300,width:"100%"},children:[n(ye,{sx:{display:"flex",justifyContent:"flex-end",marginBottom:"10px"},children:n(It,{variant:"contained",color:"primary",onClick:()=>{je(!0)},disabled:I||us||f&&(e==null?void 0:e.requestStatus)!==Ao.DRAFT,children:"+ Add"})}),x&&(x==null?void 0:x.length)>0?n(Ss,{children:n("div",{style:{width:"100%",height:"100%",overflowX:"auto"},children:n("div",{style:{height:"100%"},children:n(qr,{rows:x,columns:so,autoHeight:!1,pageSize:50,page:j,rowsPerPageOptions:[50],rowCount:(ie==null?void 0:ie.totalElements)||0,onRowClick:_o,onCellEditCommit:Zs,onPageChange:E=>yn(E),disableSelectionOnClick:!0,getRowClassName:E=>E.id===ls?"selected-row":"",style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%",height:`${Math.min(x.length*50+130,300)}px`,overflow:"auto"},sx:{"& .selected-row":{backgroundColor:"rgb(234 233 255)"}}})})})}):n(Ss,{children:n("div",{style:{width:"100%",height:"100%",overflowX:"auto"},children:n("div",{style:{height:"100%"},children:n(qr,{rows:x,autoHeight:!1,columns:so,pageSize:5,rowsPerPageOptions:[5],page:j,onRowClick:_o,onCellEditCommit:Zs,onPageChange:E=>yn(E),disableSelectionOnClick:!0,getRowClassName:E=>E.id===ls?"selected-row":"",style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%",height:`${Math.min(x.length*50+130,300)}px`,overflow:"auto"},sx:{"& .selected-row":{backgroundColor:"rgb(234 233 255)"}}})})})})]})}),ls&&Ht&&(x==null?void 0:x.length)>0&&D.length>0&&Q&&((lr=Object.getOwnPropertyNames(Q))==null?void 0:lr.length)>0&&R(ye,{sx:{marginTop:"45px"},children:[R(Hl,{sx:{top:0,position:"sticky",zIndex:1e3,backgroundColor:Be.background.container,borderBottom:`1px solid ${Be.border.light}`,"& .MuiTab-root":{minHeight:"48px",textTransform:"none",fontSize:"14px",fontWeight:600,color:Be.black.graphite,"&.Mui-selected":{color:Be.primary.main,fontWeight:700},"&:hover":{color:Be.primary.main,opacity:.8}},"& .MuiTabs-indicator":{backgroundColor:Be.primary.main,height:"3px"}},value:Qt,onChange:Yo,className:t.customTabs,"aria-label":"material tabs",children:[A&&Lt.length>0&&(A==null?void 0:A.length)>0?A==null?void 0:A.map((E,_)=>n(jn,{label:E},_)):n(Ss,{}),n(jn,{label:"Additional Data",id:"AdditionalKey"},"Additional data")]}),(x==null?void 0:x.length)>0&&n(ye,{sx:{padding:2,marginTop:2},children:ft()}),n($r,{activeTab:Qt,submitForApprovalDisabled:ss,filteredButtons:Ct,workFlowLevels:ke,showWfLevels:Qe,childRequestHeaderData:(ir=q==null?void 0:q[ls])==null?void 0:ir.Tochildrequestheaderdata})]}),n("div",{}),n(ki,{dialogState:ee,openReusableDialog:Fo,closeReusableDialog:Io,dialogTitle:"Warning",dialogMessage:M,showCancelButton:!1,handleOk:Xs,handleDialogConfirm:Io,dialogOkText:"OK",dialogSeverity:"danger"}),Ns&&n(cn,{fullWidth:!0,maxWidth:!1,open:!0,onClose:ho,sx:{display:"flex",justifyContent:"center"},disableEscapeKeyDown:!0,children:R(ye,{sx:{width:"600px !important"},children:[R(Pn,{sx:{backgroundColor:"#EAE9FF",marginBottom:".5rem"},children:[n(Hr,{style:{height:"20px",width:"20px",marginBottom:"-5px"}}),n("span",{children:"Select Views"})]}),n(Lo,{sx:{paddingBottom:".5rem"},children:R(ye,{display:"flex",alignItems:"center",sx:{flex:1,padding:"22px 0px",gap:"5px"},children:[n(Pl,{size:"small",multiple:!0,fullWidth:!0,options:it,disabled:us,disableCloseOnSelect:!0,value:A==null?void 0:A.filter(E=>!Fh.includes(E)),onChange:(E,_)=>{O([L,..._.filter(W=>W!==L)]),Zs({id:rs,field:"views",value:_})},getOptionDisabled:E=>E===L,renderOption:(E,_,{selected:W})=>{var Ge;const re=k.find(Ze=>(Ze==null?void 0:Ze.material)===(es==null?void 0:es.materialNumber)),Ae=((Ge=re==null?void 0:re.views)==null?void 0:Ge.includes(_))||!1;return R("li",{...E,children:[n(Hn,{checked:W||_=="Basic Data",sx:{marginRight:1}}),_," ",Ae?"(extended)":""]})},renderTags:(E,_)=>E.map((W,re)=>{var wt;const{key:Ae,...Ge}=_({index:re}),Ze=k.find(Tt=>(Tt==null?void 0:Tt.material)===(es==null?void 0:es.materialNumber)),et=((wt=Ze==null?void 0:Ze.views)==null?void 0:wt.includes(W))||!1;return n(Er,{label:`${W} ${et?"(extended)":""}`,...Ge,disabled:W===L},Ae)}),renderInput:E=>n(xn,{...E,label:"Select Views"})}),n(It,{variant:"contained",disabled:us,size:"small",onClick:()=>is(),children:"Select all"})]})}),R(Do,{children:[n(It,{onClick:()=>{ds(!1)},color:"error",variant:"outlined",sx:{height:36,minWidth:"3.5rem",textTransform:"none",borderColor:"#cc3300",fontWeight:500},children:"Cancel"}),n(It,{onClick:()=>{ds(!1),Zs({id:rs,field:"views",value:A})},variant:"contained",children:"OK"})]})]})}),Hs&&R(cn,{fullWidth:!0,maxWidth:"xl",open:!0,onClose:ho,disableEscapeKeyDown:!0,sx:{"& .MuiDialog-paper":{padding:2,borderRadius:2}},children:[R(Pn,{sx:{display:"flex",alignItems:"center",gap:1,backgroundColor:"#EAE9FF"},children:[n(Hr,{fontSize:"small"}),Pe?n("span",{children:"Select org data for copy"}):n("span",{children:"Select org data to be extended"}),n(Ds,{onClick:ho,sx:{position:"absolute",right:15},children:n(Gl,{})})]}),n(Lo,{sx:{padding:0},children:n(Ia,{component:Ur,children:R(va,{children:[n(Wh,{children:R(Ul,{children:[!Pe&&n(bs,{align:"center",children:"S NO."}),n(bs,{align:"center",children:"Sales Org"}),n(bs,{align:"center",children:"Distribution Channel"}),n(bs,{align:"center",children:"Plant"}),n(bs,{align:"center",children:"Storage Location"}),(l==null?void 0:l.Region)!==Uo.EUR&&n(bs,{align:"center",children:"Warehouse"}),n(bs,{align:"center",children:"MRP Profile"}),(Lt==null?void 0:Lt.length)>1&&!Pe&&n(bs,{align:"center",children:"Action"})]})}),n(Ma,{children:Sn==null?void 0:Sn.map((E,_)=>{var W,re,Ae,Ge,Ze,et,wt,Tt,Kt,tt,hs,eo;return R(Ul,{sx:{padding:"12px"},children:[!Pe&&n(bs,{children:n(mt,{variant:"body2",children:_+1})}),n(bs,{children:n(lo,{options:lt["Sales Organization"],value:Pe?st==null?void 0:st.salesOrg:E==null?void 0:E.salesOrg,onChange:zt=>ve("Sales Organization",zt,_),placeholder:"Select Sales Org",disabled:us,isFieldError:!1,minWidth:165,isLoading:xe["Sales Organization"]})}),n(bs,{children:n(lo,{options:Pe?(re=st==null?void 0:st.dc)==null?void 0:re.options:(W=E.dc)==null?void 0:W.options,value:Pe?(Ge=st==null?void 0:st.dc)==null?void 0:Ge.value:(Ae=E.dc)==null?void 0:Ae.value,onChange:zt=>Pe?Ts(oo=>{var no;return{...oo,dc:{value:zt,options:(no=st==null?void 0:st.dc)==null?void 0:no.options}}}):as(zt,_),placeholder:"Select DC",disabled:us,isFieldError:!1,minWidth:165,isLoading:xe["Distribution Channel"][_]})}),n(bs,{children:n(lo,{options:lt.Plant||[],value:Pe?(et=st==null?void 0:st.plant)==null?void 0:et.value:(Ze=E.plant)==null?void 0:Ze.value,onChange:zt=>$t(zt,_),placeholder:"Select Plant",disabled:us,isFieldError:!1,minWidth:165,isLoading:xe.Plant[_]})}),n(bs,{children:n(lo,{options:Pe?(Tt=st==null?void 0:st.sloc)==null?void 0:Tt.options:(wt=E==null?void 0:E.sloc)==null?void 0:wt.options,value:Pe?(tt=st==null?void 0:st.sloc)==null?void 0:tt.value:(Kt=E==null?void 0:E.sloc)==null?void 0:Kt.value,onChange:zt=>Pe?Ts(oo=>{var no;return{...oo,sloc:{value:zt,options:(no=st==null?void 0:st.sloc)==null?void 0:no.options}}}):gt(zt,_),placeholder:"Select Sloc",disabled:us,isFieldError:!1,minWidth:165,isLoading:xe["Storage Location"][_]})}),(l==null?void 0:l.Region)!==Uo.EUR&&n(bs,{children:n(lo,{options:lt.warehouse||[],value:Pe?(eo=st==null?void 0:st.warehouse)==null?void 0:eo.value:(hs=E==null?void 0:E.warehouse)==null?void 0:hs.value,onChange:zt=>Pe?Ts(oo=>{var no;return{...oo,warehouse:{value:zt,options:(no=st==null?void 0:st.warehouse)==null?void 0:no.options}}}):Gt(zt,_),placeholder:"Select Warehouse",disabled:us,isFieldError:!1,minWidth:165,isLoading:xe.warehouse[_]})}),n(bs,{children:n(lo,{options:lt["Mrp Profile"]||[],value:Pe?st==null?void 0:st.mrpProfile:E.mrpProfile,onChange:zt=>Pe?Ts(oo=>({...oo,mrpProfile:zt})):De(zt,_),placeholder:"Select MRP Profile",disabled:us,isFieldError:!1,minWidth:165,isLoading:xe["Mrp Profile"]})}),Lt.length>1&&R(bs,{align:"right",children:[n(Ds,{size:"small",color:"primary",disabled:us,onClick:()=>{w(!0),We({orgRowLength:Lt.length,copyFor:_})},style:{display:_===0?"none":"inline-flex"},children:n(bi,{})}),n(Ds,{style:{display:_===0?"none":"inline-flex"},size:"small",color:"error",onClick:()=>qs(_),children:n(Ar,{})})]})]},_)})})]})})}),R(Do,{sx:{justifyContent:"flex-end",gap:.5},children:[!Pe&&n(It,{onClick:pt,disabled:us,variant:"contained",children:"+ Add"}),n(It,{onClick:()=>{if(os(!1),Lt[0].plant&&(Zs({id:rs,field:"orgData",value:Lt}),!Pe)){_e(Lt);const _=x==null?void 0:x.map(W=>(W==null?void 0:W.id)===rs?{...W,orgData:Lt}:W);o(Oo(_))}const E=Lt.filter(_=>{var W,re;return(re=(W=_.plant)==null?void 0:W.value)==null?void 0:re.code}).map(_=>{var W,re;return(re=(W=_.plant)==null?void 0:W.value)==null?void 0:re.code});E.length>0&&pe(E),Pe&&(Bs(_=>{const W=_.findIndex(re=>re.id===st.id);return W!==-1?_.map((re,Ae)=>Ae===W?{...re,...st}:re):[..._,st]}),ms(st,Lt,l,A))},variant:"contained",children:"Ok"})]})]}),de&&n(af,{open:de,onClose:()=>w(!1),title:xa.COPY_ORG_DATA_VALES_HEADING,selectedMaterialPayload:ae,lengthOfOrgRow:Te,materialID:ls,orgRows:Lt}),Oe&&n(Un,{openSnackBar:$e,alertMsg:Oe,alertType:Dt,handleSnackBarClose:()=>me(!1)}),n(EA,{openSearchMat:Pt,materialOptions:ce,handleMatInputChange:At,inputState:Ke,setOpenSearchMat:je,dropDownData:lt,AddCopiedMaterial:Wo}),n(Hi,{})]})},SA=e=>{var o,c;const t=((c=(o=e==null?void 0:e.split("."))==null?void 0:o.pop())==null?void 0:c.toLowerCase())||"",r={fontSize:"small",sx:{mr:1}};switch(t){case"xlsx":case"xls":case"csv":return n(CT,{sx:{color:Be.icon.excel}});case"pdf":return n(Fp,{...r,sx:{color:Be.icon.pdf}});case"doc":case"docx":return n(th,{...r,sx:{color:Be.icon.doc}});case"ppt":case"pptx":return n(th,{...r,sx:{color:Be.icon.ppt}});default:return n($p,{...r,sx:{color:Be.icon.file}})}},NA=({attachmentsData:e=[],requestIdHeader:t="",pcNumber:r="",disabled:o=!1,requestStatus:c})=>{const[s,m]=d.useState({}),l=oe(M=>M.appSettings),u=oe(M=>M.payload.dynamicKeyValues),[se,ie]=d.useState([]),[q,g]=d.useState([]),V=Ro(),{t:Q}=bn(),H=oe(M=>M.userManagement.taskData),he=oe(M=>M.applicationConfig),ne=oe(M=>M.request.materialRows),[J,f]=d.useState(!1),z=oe(M=>M.payload.payloadData),[j,p]=d.useState(""),T=tn(),C=new URLSearchParams(T.search).get("reqBench"),S=T==null?void 0:T.state,{extendFilteredButtons:L}=lc(H,he,yo,go);let A;const O=[To.HANDLE_SEND_BACK,To.HANDLE_VALIDATE,To.HANDLE_CORRECTION,To.HANDLE_SUBMIT_FOR_APPROVAL,To.HANDLE_SAP_SYNDICATION,To.HANDLE_SUBMIT_FOR_REVIEW,To.HANDLE_SUBMIT];(z==null?void 0:z.RequestType)===b.EXTEND||(z==null?void 0:z.RequestType)===b.EXTEND_WITH_UPLOAD?A=Cr(L,O):A=[];const k=oe(M=>M.payload.payloadData),U=oe(M=>{var Se,ce;return(ce=(Se=M==null?void 0:M.payload)==null?void 0:Se.dynamicKeyValues)==null?void 0:ce.childRequestHeaderData}),x=()=>{f(!0)},N=()=>{f(!1)},D=!ko.includes(c)||t&&!C,I={primary:Be.blue.main,light:Be.text.offWhite,accent:Be.primary.grey,text:Be.text.charcoal,secondaryText:Be.text.greyBlue,background:Be.background.paper},ge=[{field:"id",headerName:"Document ID",flex:1.2,hideable:!1,hidden:!0},{field:"attachmentType",headerName:Q("Attachment Type"),flex:1.5,renderCell:M=>{var Se;return n(Er,{label:M.value,size:"small",sx:{backgroundColor:(Se=Be)==null?void 0:Se.reportTile.lightBlue,color:Be.primary.lightPlus,fontWeight:"medium"}})}},{field:"docName",headerName:Q("Document Name"),flex:2,renderCell:M=>R(bo,{direction:"row",spacing:1,alignItems:"center",children:[SA(M.value),n(mt,{variant:"body2",children:M.value})]})},{field:"uploadedOn",headerName:Q("Uploaded On"),flex:1,align:"center",headerAlign:"center"},{field:"uploadedBy",headerName:Q("Uploaded By"),sortable:!1,flex:1},{field:"view",headerName:Q("View"),sortable:!1,filterable:!1,align:"center",headerAlign:"center",renderCell:M=>{var Se,ce;return n(Ds,{size:"small",sx:{color:Be.icon.matView,"&:hover":{backgroundColor:"rgba(2, 136, 209, 0.1)"}},children:n(AT,{index:M.row.id,name:((Se=M==null?void 0:M.row)==null?void 0:Se.docName)||((ce=M==null?void 0:M.row)==null?void 0:ce.fileName),documentViewUrl:M.row.documentViewUrl})})}},{field:"action",headerName:Q("Action"),sortable:!1,filterable:!1,align:"center",headerAlign:"center",flex:1,renderCell:M=>{var Se,ce,$,qe,F;return R(bo,{direction:"row",spacing:0,children:[n(Ds,{size:"small",sx:{color:(Se=Be)==null?void 0:Se.icon.matDownload,"&:hover":{backgroundColor:"rgba(46, 125, 50, 0.1)"}},children:n(bT,{index:M.row.id,name:((ce=M==null?void 0:M.row)==null?void 0:ce.docName)||(($=M==null?void 0:M.row)==null?void 0:$.fileName)})}),n(Ds,{size:"small",sx:{color:(qe=Be)==null?void 0:qe.icon.delete,"&:hover":{backgroundColor:"rgba(211, 47, 47, 0.1)"}},children:n(ST,{index:M.row.id,name:M.row.docName||((F=M==null?void 0:M.row)==null?void 0:F.fileName),setSnackbar:f,setopenSnackbar:f,setMessageDialogMessage:p,handleSnackbarOpen:x,setDownloadLoader:m,DownloadLoader:s})})]})}}];d.useEffect(()=>{ee()},[J]),d.useEffect(()=>{var Se,ce;if(C&&(S==null?void 0:S.reqStatus)===((Se=Ao)==null?void 0:Se.SYNDICATED_IN_SAP_DIRECT)){let $=[];var M={id:S.requestId,comment:((ce=u==null?void 0:u.otherPayloadData)==null?void 0:ce.Comments)||"",user:S.createdBy,createdAt:S.changedOnAct,taskName:"Direct Syndication Task",role:"Requestor"};$.push(M),g($);return}Ue()},[]);const ee=()=>{let M=t,Se=qe=>{var F=[];qe.documentDetailDtoList.forEach(Ke=>{var Us={id:Ke.documentId,docType:Ke.fileType,docName:Ke.fileName,uploadedOn:Fn(Ke.docCreationDate).format(l.dateFormat),uploadedBy:Ke.createdBy,attachmentType:Ke.attachmentType,documentViewUrl:Ke.documentViewUrl};V(Wp(Ke==null?void 0:Ke.attachmentType)),F.push(Us)}),ie(F)};const $=(U==null?void 0:U.ChildRequestId)||(k==null?void 0:k.childRequestId)?`/${sh}/${Fe.TASK_ACTION_DETAIL.GET_CHILD_DOCS}/${M}`:`/${sh}/${Fe.TASK_ACTION_DETAIL.GET_DOCS}/${M}`;Xe($,"get",Se)},Ue=()=>{let M=t,Se=$=>{var qe=[];$.body.forEach(F=>{var Ke={id:F.requestId,comment:F.comment,user:F.createdByUser,createdAt:F.updatedAt,taskName:F.taskName,role:F.createdByRole};qe.push(Ke)}),g(qe)},ce=$=>{console.log($)};Xe(`/${Re}/${Fe.TASK_ACTION_DETAIL.TASKDETAILS_FOR_REQUESTID}?requestId=${M}`,"get",Se,ce)};return R("div",{children:[n(vs,{container:!0,spacing:2,sx:{padding:"10px",margin:0},children:R(vs,{item:!0,md:12,sx:{backgroundColor:Be.primary.white,maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:2,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Oa},children:[n(vs,{container:!0,sx:{display:"flex",justifyContent:"space-between",flexDirection:"row",alignItems:"center"},children:R(ye,{sx:{display:"flex",justifyContent:"space-between",flexDirection:"row",alignItems:"space-between",width:"100%"},children:[n(mt,{variant:"h6",children:n("strong",{children:Q("Attachments")})}),!o&&(e==null?void 0:e.map((M,Se)=>n(NT,{title:"Material",useMetaData:!1,artifactId:`${M.MDG_ATTACHMENTS_NAME}_${r}`,artifactName:"MaterialMaster",attachmentType:M.MDG_ATTACHMENTS_NAME,requestId:t,getAttachments:ee})))]})}),se.length>0?n(Di,{width:"100%",rows:se,columns:ge,hideFooter:!1,getRowIdValue:"id",autoHeight:!0,disableSelectionOnClick:!0,stopPropagation_Column:"action"}):R(bo,{alignItems:"center",spacing:2,sx:{py:4},children:[n(mT,{sx:{fontSize:40,color:I.accent,transform:"rotate(90deg)"}}),n(mt,{variant:"body2",color:I.secondaryText,children:Q("No Attachments Found")})]}),n("br",{}),R(ye,{sx:{maxWidth:"100%",bgcolor:I.background,borderRadius:"12px",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[n(bo,{direction:"row",alignItems:"center",spacing:1,mb:3,children:n(mt,{variant:"h6",fontWeight:"bold",color:I.text,children:Q("Comments")})}),q.length>0?n(wT,{position:"right",sx:{p:0,m:0,"& .MuiTimelineItem-root:before":{flex:0,padding:0}},children:q.map((M,Se)=>R(RT,{sx:{},children:[R(_T,{children:[n(IT,{sx:{bgcolor:I.light,boxShadow:"0 2px 4px rgba(0,0,0,0.1)"},children:n(eh,{size:18,sx:{color:"blue"}})}),n(vT,{sx:{width:"2.2px"}})]}),n(MT,{sx:{py:"12px",px:2},children:n(ur,{variant:"outlined",sx:{borderRadius:"12px",borderColor:I.accent,background:"linear-gradient(135deg, #FFFFFF 0%, #F9FAFB 100%)",transition:"all 0.3s ease"},children:R(hr,{sx:{p:2},children:[R(bo,{direction:"row",justifyContent:"space-between",alignItems:"center",children:[R(bo,{direction:"row",spacing:1.5,alignItems:"center",children:[n(Bp,{sx:{bgcolor:"#e3f2fd",color:I.primary,boxShadow:"none",width:32,height:32,fontSize:"14px",transition:"all 0.3s ease","&:hover":{transform:"rotate(5deg)"}},children:M.user.charAt(0).toUpperCase()}),n(mt,{fontWeight:"bold",color:I.text,children:M.user}),n(Er,{label:M.taskName,size:"small",sx:{backgroundColor:Be.reportTile.lightBlue,color:Be.primary.lightPlus,fontSize:"12px",borderRadius:"16px"}})]}),R(bo,{direction:"row",alignItems:"center",spacing:1,children:[n(mt,{variant:"body2",color:I.secondaryText,sx:{fontSize:"12px"},children:Fn(M.createdAt).format("MM/DD/YYYY, h:mm A")}),n(Ds,{size:"small",sx:{color:I.secondaryText},children:n(Gp,{fontSize:"small"})})]})]}),n(Yh,{sx:{my:2,borderColor:I.accent}}),n(mt,{variant:"body2",color:I.text,sx:{my:1,fontSize:"14px",lineHeight:"1.6"},children:M.comment||"-"})]})})})]},M.id))}):R(bo,{alignItems:"center",spacing:2,sx:{py:4},children:[n(eh,{sx:{fontSize:40,color:I.accent}}),n(mt,{variant:"body2",color:I.secondaryText,children:Q("No Remarks found")})]})]}),n("br",{})]})}),(!D||t&&!C||C&&ko.includes(c))&&n(ye,{sx:{borderTop:"1px solid #e0e0e0",padding:"16px"},children:n($r,{activeTab:Dl.ATTACHMENT_AND_COMMENTS,submitForApprovalDisabled:!sr(ne),filteredButtons:A})}),J&&n(Un,{openSnackBar:J,alertMsg:j,handleSnackBarClose:N})]})},aa=({number:e,label:t})=>R("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",padding:12,background:"#fff",borderRadius:8,boxShadow:"0 5px 15px rgba(0, 0, 0, 0.05)",transition:"transform 0.3s ease",animation:"fadeIn 0.5s ease-out forwards",minHeight:80},children:[n("div",{className:"stat-number",children:e}),n("div",{className:"stat-label",children:t})]}),wA=({data:e})=>{let t=0,r=Object.keys(e).length,o=0,c=0;const{t:s}=bn();Object.values(e).forEach(l=>{var se;const u=l.workflowDetails;t+=2,o+=(u.requestor_sla||0)+(u.mdmApprover_sla||0),c+=2,(se=u.workflowTaskDetailsByLevel)==null||se.forEach(ie=>{var g;const q=(g=Object.values(ie))==null?void 0:g[0];t+=q.length,c+=q.length,q.forEach(V=>{o+=V.taskSla||0})})});const m=c?(o/c).toFixed(1):0;return R("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(180px, 1fr))",gap:16,margin:"16px 0",padding:8,borderRadius:8},children:[n(aa,{number:r,label:s("Workflow Groups")}),n(aa,{number:t,label:s("Total Tasks")}),n(aa,{number:m,label:s("Avg SLA (Days)")})]})};var ic={},RA=Go;Object.defineProperty(ic,"__esModule",{value:!0});var lg=ic.default=void 0,_A=RA(Bo()),IA=$o;lg=ic.default=(0,_A.default)((0,IA.jsx)("path",{d:"M7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2M1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12.9-1.63h7.45c.75 0 1.41-.41 1.75-1.03l3.58-6.49c.08-.14.12-.31.12-.48 0-.55-.45-1-1-1H5.21l-.94-2zm16 16c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2"}),"ShoppingCart");var ac={},vA=Go;Object.defineProperty(ac,"__esModule",{value:!0});var yi=ac.default=void 0,MA=vA(Bo()),OA=$o;yi=ac.default=(0,MA.default)((0,OA.jsx)("path",{d:"M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2m-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1m2 14H7v-2h7zm3-4H7v-2h10zm0-4H7V7h10z"}),"Assignment");var cc={},xA=Go;Object.defineProperty(cc,"__esModule",{value:!0});var ig=cc.default=void 0,yA=xA(Bo()),LA=$o;ig=cc.default=(0,yA.default)((0,LA.jsx)("path",{d:"m3.5 18.49 6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L2 16.99z"}),"ShowChart");var dc={},DA=Go;Object.defineProperty(dc,"__esModule",{value:!0});var ag=dc.default=void 0,PA=DA(Bo()),qA=$o;ag=dc.default=(0,PA.default)((0,qA.jsx)("path",{d:"M15 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4m-9-2V7H4v3H1v2h3v3h2v-3h3v-2zm9 4c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4"}),"PersonAdd");var uc={},UA=Go;Object.defineProperty(uc,"__esModule",{value:!0});var cg=uc.default=void 0,kA=UA(Bo()),Nh=$o;cg=uc.default=(0,kA.default)([(0,Nh.jsx)("path",{d:"M14 7h-4c-1.1 0-2 .9-2 2v6h2v7h4v-7h2V9c0-1.1-.9-2-2-2"},"0"),(0,Nh.jsx)("circle",{cx:"12",cy:"4",r:"2"},"1")],"Man");const HA=e=>e.includes("Storage")?n(jp,{}):e.includes("Buyer")?n(lg,{}):e.includes("Planner")?n(yi,{}):e.includes("Regulatory")?n(Hr,{}):e.includes("Product")?n(OT,{}):e.includes("Plant")?n(ig,{}):e.includes("Data")?n(xT,{}):e.includes("Requestor")?n(ag,{}):e.includes("Steward")?n(zp,{}):e.includes("Manager")?n(cg,{}):n(yi,{}),BA=({id:e,task:t,onClick:r})=>{const o="#1976d2";return R("div",{id:e,onClick:r,className:"task",style:{borderRadius:12,marginBottom:8,background:"#fff",boxShadow:"0 4px 12px rgba(0, 0, 0, 0.08)",cursor:"pointer",transition:"transform 0.1s ease",maxWidth:230,padding:8,borderLeft:`4px solid ${o}`,position:"relative"},children:[n("div",{style:{position:"absolute",left:-12,top:12,width:16,height:16,borderRadius:"50%",background:"#fff",border:`3px solid ${o}`,boxSizing:"border-box",zIndex:2}}),R("div",{style:{display:"flex",alignItems:"start",gap:8,paddingTop:"16px",paddingBottom:"16px"},children:[n("div",{style:{color:"#1976d2",fontSize:24,display:"flex",alignItems:"center"},children:HA(t.name)}),n("div",{style:{fontWeight:600},children:t.name}),R("div",{style:{marginLeft:"auto",color:"#3498db",fontSize:14,background:"#e1f0fa",paddingLeft:"10px",paddingRight:"10px",borderRadius:"20px"},children:[t.sla,"d"]})]})]})},dg=({label:e,tasks:t,onTaskClick:r})=>R("div",{style:{display:"flex",flexDirection:"column",alignItems:"stretch",gap:0,padding:12},children:[n("div",{style:{marginBottom:8},children:n("span",{className:"level-label",style:{fontWeight:600,fontSize:16,display:"block",textAlign:"center",border:`${e=="Requestor"&&"2px solid #90EE90"}`},children:e})}),n("div",{style:{display:"flex",flexDirection:"column",gap:8,position:"relative"},children:t.map(o=>n(BA,{id:o.id,task:o,onClick:()=>r(o)},o.id))})]});dg.propTypes={label:ci.string.isRequired,tasks:ci.arrayOf(ci.object).isRequired,onTaskClick:ci.func.isRequired};const GA=({containerRef:e,sourceTargets:t})=>{const r=d.useRef(null),[o,c]=d.useState({width:0,height:0});return d.useEffect(()=>{const s=()=>{if(e.current){const m=e.current.getBoundingClientRect();c({width:m.width,height:m.height})}};return s(),window.addEventListener("resize",s),()=>window.removeEventListener("resize",s)},[e]),d.useEffect(()=>{if(!e.current||!r.current)return;const s=r.current;s.innerHTML="";const m=document.createElementNS("http://www.w3.org/2000/svg","defs"),l=document.createElementNS("http://www.w3.org/2000/svg","marker");l.setAttribute("id","arrowhead"),l.setAttribute("markerWidth","10"),l.setAttribute("markerHeight","7"),l.setAttribute("refX","9"),l.setAttribute("refY","3.5"),l.setAttribute("orient","auto");const u=document.createElementNS("http://www.w3.org/2000/svg","polygon");u.setAttribute("points","0 0, 10 3.5, 0 7"),u.setAttribute("fill","#3498db"),l.appendChild(u),m.appendChild(l),s.appendChild(m),t.forEach(({fromId:se,toId:ie})=>{const q=document.getElementById(se),g=document.getElementById(ie);if(!q||!g)return;const V=q.getBoundingClientRect(),Q=g.getBoundingClientRect(),H=e.current.getBoundingClientRect(),he=V.right-H.left,ne=V.top+V.height/2-H.top,J=Q.left-H.left,f=Q.top+Q.height/2-H.top,z=document.createElementNS("http://www.w3.org/2000/svg","path"),j=Math.max(50,Math.abs(J-he)/2),p=`M ${he},${ne} C ${he+j},${ne} ${J-j},${f} ${J},${f}`;z.setAttribute("d",p),z.setAttribute("stroke","#bdc3c7"),z.setAttribute("stroke-width","2"),z.setAttribute("fill","none"),z.setAttribute("marker-end","url(#arrowhead)"),s.appendChild(z)})},[e,t,o]),n("svg",{ref:r,width:o.width,height:o.height,style:{position:"absolute",top:5,left:1.5,pointerEvents:"none",zIndex:0}})},$A=({groupName:e,groupData:t,onTaskClick:r})=>{var u;const o=d.useRef(null),[c,s]=d.useState([]),m=[],l=(se,ie,q,g)=>({type:se,label:ie,tasks:q.map((V,Q)=>({...V,id:`${e}-level${g}-task${Q}`}))});return m.push(l("requestor",t.requestorTaskLevelName,[{name:t.requestorTaskName,sla:t.requestor_sla,group:t.requestorTaskGroup,approver:"Requester",level:t.requestorTaskLevelName,status:"Pending"}],0)),(u=t==null?void 0:t.workflowTaskDetailsByLevel)==null||u.forEach((se,ie)=>{const q=Object.keys(se)[0],g=se[q];g.length>0&&m.push(l("workflow",`Level ${g[0].workflowApprovalLevel}: ${g[0].workflowLevelName}`,g.map(V=>({name:V.workflowTaskName,sla:V.taskSla,group:V.workflowTaskGroup,approver:V.taskApprover,level:V.workflowLevelName,status:"In Progress"})),ie+1))}),m.push(l("mdm",t.mdmTaskLevelName,[{name:t.mdmTaskName,sla:t.mdmApprover_sla,group:t.mdmTaskGroup,approver:t.mdmApprover_RecipientUsers,level:t.mdmTaskLevelName,status:"Not Started"}],m.length)),d.useEffect(()=>{const se=[];for(let ie=0;ie<m.length-1;ie++){const q=m[ie],g=m[ie+1];q.tasks.forEach(V=>{g.tasks.length>0&&se.push({fromId:V.id,toId:g.tasks[0].id})})}s(se)},[e]),R("div",{style:{display:"flex",flexDirection:"row",alignItems:"stretch",width:"100%",borderRadius:8,padding:16,minHeight:120,position:"relative",gap:"24px"},ref:o,children:[m==null?void 0:m.map((se,ie)=>n(dg,{type:se.type,label:se.label,tasks:se.tasks,onTaskClick:r},ie)),n(GA,{containerRef:o,sourceTargets:c})]})};var hc={},FA=Go;Object.defineProperty(hc,"__esModule",{value:!0});var ug=hc.default=void 0,WA=FA(Bo()),jA=$o;ug=hc.default=(0,WA.default)((0,jA.jsx)("path",{d:"M7.41 8.59 12 13.17l4.59-4.58L18 10l-6 6-6-6z"}),"KeyboardArrowDown");var fc={},zA=Go;Object.defineProperty(fc,"__esModule",{value:!0});var hg=fc.default=void 0,YA=zA(Bo()),XA=$o;hg=fc.default=(0,YA.default)((0,XA.jsx)("path",{d:"M7.41 15.41 12 10.83l4.59 4.58L18 14l-6-6-6 6z"}),"KeyboardArrowUp");const VA=({groupName:e,groupData:t,materialTypes:r,onTaskClick:o})=>{var l,u,se,ie,q,g,V,Q;const[c,s]=d.useState(!1),m=()=>s(!c);return d.useEffect(()=>{},[c]),R("div",{style:{border:"1px solid #e0e0e0",borderRadius:8,marginBottom:16,background:"#fff",boxShadow:"0 2px 8px rgba(0,0,0,0.04)",overflow:"hidden",transition:"box-shadow 0.2s"},children:[R("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:"16px",cursor:"pointer",backgroundImage:"linear-gradient(180deg, rgb(242, 241, 255) 0%, rgb(255, 255, 255) 100%)",border:"1px solid #E0E0E0",borderBottom:"1px solid #e0e0e0"},onClick:m,children:[R("div",{children:[R("div",{style:{fontWeight:600,fontSize:18,color:(u=(l=Be)==null?void 0:l.primary)==null?void 0:u.main},children:[e,` (${r.join(", ")})`]}),R("div",{style:{fontSize:14,color:(ie=(se=Be)==null?void 0:se.primary)==null?void 0:ie.main,marginTop:2},children:[t==null?void 0:t.mdmTaskGroup," • ",t==null?void 0:t.mdmTaskLevelName]})]}),n("div",{style:{fontSize:28,color:"#fff",transition:"transform 0.2s",display:"flex",alignItems:"center",justifyContent:"center"},children:c?n(hg,{style:{color:(g=(q=Be)==null?void 0:q.primary)==null?void 0:g.main,fontSize:32}}):n(ug,{style:{color:(Q=(V=Be)==null?void 0:V.primary)==null?void 0:Q.main,fontSize:32}})})]}),!c&&n("div",{style:{padding:16,background:"#fff"},children:n($A,{groupName:e,groupData:t,onTaskClick:o})})]})},KA=e=>{const t=new Date,r=new Date(t);return r.setDate(t.getDate()+e),r.toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"})},JA=({task:e,onClose:t})=>n("div",{className:"modal",onClick:r=>r.target.classList.contains("modal")&&t(),children:R("div",{className:"modal-content",children:[R("div",{className:"modal-header",children:[R("div",{className:"modal-title",style:{display:"flex",alignItems:"center",gap:8},children:[n(yi,{style:{color:"#fff"}}),n("span",{children:e.name})]}),n("div",{className:"modal-close",onClick:t,style:{cursor:"pointer"},children:n(Gl,{style:{color:"#fff"}})})]}),R("div",{className:"modal-body",children:[R("div",{className:"task-detail",children:[n("div",{className:"detail-label",children:"Task Group:"}),n("div",{className:"detail-value",children:e.group})]}),R("div",{className:"task-detail",children:[n("div",{className:"detail-label",children:"Approver:"}),n("div",{className:"detail-value",children:e.approver})]}),R("div",{className:"task-detail",children:[n("div",{className:"detail-label",children:"Level:"}),n("div",{className:"detail-value",children:e.level})]}),R("div",{className:"task-detail",children:[n("div",{className:"detail-label",children:"SLA:"}),n("div",{className:"detail-value",children:R("span",{className:"sla-badge sla-normal",children:[e.sla," days"]})})]}),R("div",{className:"task-detail",children:[n("div",{className:"detail-label",children:"Due Date:"}),n("div",{className:"detail-value",children:KA(e.sla)})]}),R("div",{className:"task-detail",children:[n("div",{className:"detail-label",children:"Status:"}),n("div",{className:"detail-value",children:e.status})]})]})]})}),QA=({data:e})=>{var m;const[t,r]=d.useState(null),{t:o}=bn(),c=l=>{r(l)},s=()=>{r(null)};return R("div",{children:[n(mt,{align:"left",variant:"h4",component:"h2",gutterBottom:!0,children:o("Workflow Details")}),n(wA,{data:e}),n("div",{className:"workflow-container",children:(m=Object==null?void 0:Object.entries(e))==null?void 0:m.map(([l,u])=>n(VA,{groupName:l,groupData:u==null?void 0:u.workflowDetails,materialTypes:u==null?void 0:u.materialTypes,onTaskClick:c},l))}),t&&n(JA,{task:t,onClose:s})]})};function ZA({label:e,checked:t=!1,onChange:r,id:o}){return n(Sr,{control:n(Hn,{id:o,checked:t,onChange:r,sx:{color:"primary.main","&.Mui-checked":{color:"primary.main"}}}),label:e,sx:{ml:0,".MuiFormControlLabel-label":{fontSize:14,color:"#4B5563",cursor:"pointer"}}})}const eb=({initialReqScreen:e,isreqBench:t})=>{const r=tn(),c=new URLSearchParams(r.search).get("RequestId"),s=oe(j=>j.payload),m=oe(j=>j.payload.payloadData),[l,u]=d.useState(!1),[se,ie]=d.useState(),[q,g]=d.useState("success"),[V,Q]=d.useState(!1),{t:H}=bn(),{customError:he}=sn(),{createPayloadFromReduxState:ne}=ka({initialReqScreen:e,isReqBench:t}),{changePayloadForTemplate:J}=$i(m==null?void 0:m.TemplateName),f=()=>{u(!1)},z=()=>{var C,S,L,A,O,k,U,x,N,D,I,ge,ee,Ue,M,Se,ce;const j=(m==null?void 0:m.RequestType)===((C=b)==null?void 0:C.CHANGE)||(m==null?void 0:m.RequestType)===((S=b)==null?void 0:S.CHANGE_WITH_UPLOAD)?J(!!c):ne(s),p={materialDetails:j,dtName:oa((A=(L=j[0])==null?void 0:L.Torequestheaderdata)==null?void 0:A.RequestType).dtName,version:oa((k=(O=j[0])==null?void 0:O.Torequestheaderdata)==null?void 0:k.RequestType).version,requestId:((x=(U=j[0])==null?void 0:U.Torequestheaderdata)==null?void 0:x.RequestId)||"",scenario:(I=oa((D=(N=j[0])==null?void 0:N.Torequestheaderdata)==null?void 0:D.RequestType))==null?void 0:I.scenario,templateName:(m==null?void 0:m.RequestType)===((ge=b)==null?void 0:ge.CHANGE)||(m==null?void 0:m.RequestType)===((ee=b)==null?void 0:ee.CHANGE_WITH_UPLOAD)?(M=(Ue=j[0])==null?void 0:Ue.Torequestheaderdata)==null?void 0:M.TemplateName:"",matlType:"ALL",region:((ce=(Se=j[0])==null?void 0:Se.Torequestheaderdata)==null?void 0:ce.Region)||""},T=$=>{if(($==null?void 0:$.size)>0){const qe=URL.createObjectURL($),F=document.createElement("a");F.href=qe,F.setAttribute("download",`Material_Preview_${new Date().getTime()}.xlsx`),document.body.appendChild(F),F.click(),document.body.removeChild(F),URL.revokeObjectURL(qe)}},v=$=>{he($),ie($==null?void 0:$.message),g("error"),u(!0)};Xe(`/${Re}${Fe.EXCEL.EXPORT_PREVIEW_EXCEL}`,"postandgetblob",T,v,p)};return R(vs,{item:!0,md:12,sx:{backgroundColor:"white",borderRadius:"8px",border:"1px solid #E0E0E0",boxShadow:"0px 1px 4px rgba(0, 0, 0, 0.1)",p:"10px"},children:[n(mt,{sx:{fontWeight:"bold",mb:"6px"},children:H("Master data details")}),n(ye,{sx:{backgroundColor:"#FAFAFA",borderRadius:"8px",boxShadow:"none"},children:R(ye,{sx:{padding:"8px"},children:[n(mt,{align:"left",variant:"h6",component:"h2",children:H("Please download the excel sheet to view all the material data.")}),R(ye,{sx:{display:"flex",flexDirection:"column",alignItems:"flex-start",gap:1,mt:2},children:[n(It,{variant:"contained",startIcon:n(yT,{sx:{fontSize:28,animation:"downloadBounce 2s ease-in-out infinite",filter:"drop-shadow(0 2px 4px rgba(255,255,255,0.3))"}}),onClick:z,sx:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",position:"relative",overflow:"hidden","&::before":{content:'""',position:"absolute",top:0,left:"-100%",width:"100%",height:"100%",background:"linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)",transition:"left 0.5s"},"&:hover::before":{left:"100%"},"&:hover":{background:"linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)",transform:"translateY(-3px) scale(1.02)",boxShadow:"0 12px 25px rgba(102, 126, 234, 0.4), 0 0 20px rgba(118, 75, 162, 0.3)"},"&:active":{transform:"translateY(-1px) scale(0.98)",boxShadow:"0 6px 15px rgba(102, 126, 234, 0.3)"},transition:"all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)",borderRadius:3,py:1.8,px:3,textTransform:"none",fontSize:"1.1rem",fontWeight:600,boxShadow:"0 8px 20px rgba(102, 126, 234, 0.3), 0 0 15px rgba(118, 75, 162, 0.2)",display:"flex",alignItems:"center",gap:1.5,border:"1px solid rgba(255, 255, 255, 0.1)",backdropFilter:"blur(10px)",color:"#ffffff",letterSpacing:"0.5px",minWidth:"180px","@keyframes downloadBounce":{"0%, 100%":{transform:"translateY(0) rotate(0deg)",filter:"drop-shadow(0 2px 4px rgba(255,255,255,0.3))"},"25%":{transform:"translateY(-3px) rotate(-2deg)",filter:"drop-shadow(0 4px 8px rgba(255,255,255,0.4))"},"50%":{transform:"translateY(-6px) rotate(0deg)",filter:"drop-shadow(0 6px 12px rgba(255,255,255,0.5))"},"75%":{transform:"translateY(-3px) rotate(2deg)",filter:"drop-shadow(0 4px 8px rgba(255,255,255,0.4))"}},"&::after":{content:'""',position:"absolute",top:"50%",left:"50%",width:"0",height:"0",background:"radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%)",borderRadius:"50%",transform:"translate(-50%, -50%)",transition:"width 0.6s, height 0.6s",pointerEvents:"none"},"&:active::after":{width:"300px",height:"300px"}},children:R(ye,{sx:{display:"flex",alignItems:"center",gap:1,position:"relative",zIndex:1},children:[H("Download Excel"),n(ye,{component:"span",sx:{fontSize:"0.8rem",opacity:.8,fontWeight:400,ml:.5},children:"(.xlsx)"})]})}),n(ZA,{label:H("I have reviewed all material details."),checked:V,onChange:()=>Q(!V)})]})]})}),n(Un,{openSnackBar:l,alertMsg:se,alertType:q,handleSnackBarClose:f})]})},tb=()=>{const{t:e}=bn();return R(ye,{sx:{p:3,maxWidth:1400,mx:"auto"},children:[n(mt,{variant:"h6",sx:{mb:3,color:"#666"},children:e("Workflow Details")}),R(ye,{sx:{display:"flex",gap:6,mb:4,justifyContent:"flex-start",maxWidth:600},children:[R(ye,{sx:{textAlign:"center"},children:[n(As,{variant:"text",width:60,height:60,sx:{fontSize:"2.5rem",mx:"auto",bgcolor:"#e3f2fd",borderRadius:1}}),n(As,{variant:"text",width:100,height:20,sx:{mt:1,mx:"auto"}})]}),R(ye,{sx:{textAlign:"center"},children:[n(As,{variant:"text",width:60,height:60,sx:{fontSize:"2.5rem",mx:"auto",bgcolor:"#e3f2fd",borderRadius:1}}),n(As,{variant:"text",width:80,height:20,sx:{mt:1,mx:"auto"}})]}),R(ye,{sx:{textAlign:"center"},children:[n(As,{variant:"text",width:60,height:60,sx:{fontSize:"2.5rem",mx:"auto",bgcolor:"#e3f2fd",borderRadius:1}}),n(As,{variant:"text",width:120,height:20,sx:{mt:1,mx:"auto"}})]})]}),R(xh,{expanded:!0,sx:{bgcolor:"#37474f",color:"white","&:before":{display:"none"},borderRadius:1,overflow:"hidden"},children:[n(yh,{sx:{bgcolor:"#37474f","& .MuiAccordionSummary-content":{alignItems:"center"}},children:R(ye,{children:[n(As,{variant:"text",width:180,height:24,sx:{bgcolor:"rgba(255,255,255,0.2)"}}),n(As,{variant:"text",width:220,height:16,sx:{bgcolor:"rgba(255,255,255,0.1)",mt:.5}})]})}),R(Lh,{sx:{bgcolor:"#f5f5f5",p:0},children:[n(ye,{sx:{display:"grid",gridTemplateColumns:"repeat(6, 1fr)",gap:2,bgcolor:"#fff",borderBottom:"1px solid #e0e0e0",p:2},children:["Requestor","Level 1: Data Entry","Level 2: Additional Master Data","Level 3: Cost","Level 4: Record Approval","Final Creation"].map((t,r)=>n(ye,{sx:{textAlign:"center"},children:n(As,{variant:"text",width:"80%",height:20,sx:{mx:"auto"}})},r))}),n(ye,{sx:{p:3,bgcolor:"#fafafa"},children:R(ye,{sx:{display:"grid",gridTemplateColumns:"repeat(6, 1fr)",gap:2,alignItems:"flex-start"},children:[n(ye,{sx:{display:"flex",flexDirection:"column",gap:2},children:[1,2,3,4,5,6].map((t,r)=>R(ye,{sx:{position:"relative"},children:[n(ur,{sx:{bgcolor:"#e3f2fd",minHeight:60,border:"2px solid #2196f3",borderRadius:2},children:n(hr,{sx:{p:2},children:R(ye,{sx:{display:"flex",alignItems:"center",gap:1},children:[n(As,{variant:"circular",width:24,height:24}),R(ye,{sx:{flex:1},children:[n(As,{variant:"text",width:"70%",height:16}),n(As,{variant:"text",width:"40%",height:12,sx:{mt:.5}})]}),n(As,{variant:"text",width:30,height:20})]})})}),r<5&&n(ye,{sx:{position:"absolute",right:-8,top:"50%",width:16,height:2,bgcolor:"#2196f3",zIndex:1,transform:"translateY(-50%)"}})]},r))}),n(ye,{sx:{display:"flex",flexDirection:"column",gap:2},children:n(ur,{sx:{bgcolor:"#e3f2fd",minHeight:60,border:"2px solid #2196f3",borderRadius:2},children:n(hr,{sx:{p:2},children:R(ye,{sx:{display:"flex",alignItems:"center",gap:1},children:[n(As,{variant:"circular",width:24,height:24}),R(ye,{sx:{flex:1},children:[n(As,{variant:"text",width:"80%",height:16}),n(As,{variant:"text",width:"50%",height:12,sx:{mt:.5}})]}),n(As,{variant:"text",width:30,height:20})]})})})}),n(ye,{sx:{display:"flex",flexDirection:"column",gap:2},children:[1,2].map((t,r)=>n(ur,{sx:{bgcolor:"#e3f2fd",minHeight:60,border:"2px solid #2196f3",borderRadius:2},children:n(hr,{sx:{p:2},children:R(ye,{sx:{display:"flex",alignItems:"center",gap:1},children:[n(As,{variant:"circular",width:24,height:24}),R(ye,{sx:{flex:1},children:[n(As,{variant:"text",width:"75%",height:16}),n(As,{variant:"text",width:"45%",height:12,sx:{mt:.5}})]}),n(As,{variant:"text",width:30,height:20})]})})},r))}),n(ye,{sx:{display:"flex",flexDirection:"column",gap:2},children:n(ur,{sx:{bgcolor:"#e3f2fd",minHeight:60,border:"2px solid #2196f3",borderRadius:2},children:n(hr,{sx:{p:2},children:R(ye,{sx:{display:"flex",alignItems:"center",gap:1},children:[n(As,{variant:"circular",width:24,height:24}),R(ye,{sx:{flex:1},children:[n(As,{variant:"text",width:"85%",height:16}),n(As,{variant:"text",width:"35%",height:12,sx:{mt:.5}})]}),n(As,{variant:"text",width:30,height:20})]})})})}),n(ye,{sx:{display:"flex",flexDirection:"column",gap:2},children:n(ur,{sx:{bgcolor:"#e3f2fd",minHeight:60,border:"2px solid #2196f3",borderRadius:2},children:n(hr,{sx:{p:2},children:R(ye,{sx:{display:"flex",alignItems:"center",gap:1},children:[n(As,{variant:"circular",width:24,height:24}),R(ye,{sx:{flex:1},children:[n(As,{variant:"text",width:"90%",height:16}),n(As,{variant:"text",width:"55%",height:12,sx:{mt:.5}})]}),n(As,{variant:"text",width:30,height:20})]})})})}),n(ye,{sx:{display:"flex",flexDirection:"column",gap:2},children:n(ur,{sx:{bgcolor:"#f5f5f5",minHeight:60,border:"2px solid #ccc",borderRadius:2},children:n(hr,{sx:{p:2},children:R(ye,{sx:{display:"flex",alignItems:"center",gap:1},children:[n(As,{variant:"circular",width:24,height:24,sx:{bgcolor:"#bdbdbd"}}),R(ye,{sx:{flex:1},children:[n(As,{variant:"text",width:"75%",height:16,sx:{bgcolor:"#bdbdbd"}}),n(As,{variant:"text",width:"40%",height:12,sx:{mt:.5,bgcolor:"#bdbdbd"}})]}),n(As,{variant:"text",width:30,height:20,sx:{bgcolor:"#bdbdbd"}})]})})})})]})})]})]})]})},sb=e=>{var k;const t=oe(U=>U.payload),{customError:r}=sn(),o=tn(),c=new URLSearchParams(o.search),s=oe(U=>U.request.materialRows),m=oe(U=>U.userManagement.taskData),l=oe(U=>U.applicationConfig),u=c.get("reqBench"),se=c.get("RequestId"),ie=oe(U=>U.request.requestHeader),{showSnackbar:q}=Dh(),[g,V]=d.useState(null),[Q,H]=d.useState(!1),he=!(m!=null&&m.taskId)&&!u,ne=oe(U=>U.tabsData.requestHeaderData),{t:J}=bn(),f=oe(U=>U.payload.payloadData),{createPayloadFromReduxState:z}=ka({initialReqScreen:he,isReqBench:u}),{changePayloadForTemplate:j}=$i(f==null?void 0:f.TemplateName),p=oe(U=>U.payload.filteredButtons),{filteredButtons:T}=rg(m,l,yo,go),{extendFilteredButtons:v}=lc(m,l,yo,go),C=f==null?void 0:f.RequestStatus,S=C===Ao.DRAFT||C===Ao.DRAFT_IN_CAPS||C===Ao.VALIDATED_REQUESTOR||C===Ao.VALIDATION_FAILED_REQUESTOR||C===Ao.UPLOAD_SUCCESSFUL;let L;const A=[To.HANDLE_SEND_BACK,To.HANDLE_VALIDATE,To.HANDLE_CORRECTION];(f==null?void 0:f.RequestType)===b.CREATE||(f==null?void 0:f.RequestType)===b.CREATE_WITH_UPLOAD?L=Cr(T,[...A,To.HANDLE_VALIDATE1]):(f==null?void 0:f.RequestType)===b.EXTEND||(f==null?void 0:f.RequestType)===b.EXTEND_WITH_UPLOAD?L=Cr(v,A):(f==null?void 0:f.RequestType)===b.CHANGE||(f==null?void 0:f.RequestType)===b.CHANGE_WITH_UPLOAD?L=Cr(p,A):L=[];const O=!ko.includes(e==null?void 0:e.requestStatus)||se&&!u;return d.useEffect(()=>{var U,x;if(S){const N=(f==null?void 0:f.RequestType)===((U=b)==null?void 0:U.CHANGE)||(f==null?void 0:f.RequestType)===((x=b)==null?void 0:x.CHANGE_WITH_UPLOAD)?j(!!se):z(t);H(!0);const D=ge=>{ge.statusCode===Jt.STATUS_200?(V(ge==null?void 0:ge.body),H(!1)):(q(ge==null?void 0:ge.message,"error"),H(!1))},I=ge=>{r(ge),H(!1)};Xe(`/${Re}${Fe.MASS_ACTION.WORKFLOW_DETAILS_BIFURCATION}`,"post",D,I,N)}},[]),R(Ss,{children:[R(bo,{spacing:2,children:[Object.entries(ne).map(([U,x])=>R(vs,{item:!0,md:12,sx:{backgroundColor:"white",borderRadius:"8px",border:"1px solid #E0E0E0",boxShadow:"0px 1px 4px rgba(0, 0, 0, 0.1)",...Oa,pt:"10px"},children:[n(mt,{sx:{fontWeight:"bold",mb:"6px"},children:J("Request Details")}),n(ye,{sx:{backgroundColor:"#FAFAFA",padding:"10px",pl:"0px",pr:"0px",borderRadius:"8px",boxShadow:"none"},children:n(vs,{container:!0,spacing:2,children:x.filter(N=>N.visibility!=="Hidden").sort((N,D)=>N.sequenceNo-D.sequenceNo).map(N=>{let D=(ie==null?void 0:ie[N==null?void 0:N.jsonName])||(f==null?void 0:f[N==null?void 0:N.jsonName])||"",I="";return Array.isArray(D)?I=D.join(", "):D instanceof Date||typeof D=="object"&&D instanceof Object&&D.toString().includes("GMT")?I=new Date(D).toLocaleString():I=D,D=I,D&&D!==null&&D!==""&&n(vs,{item:!0,md:3,children:R("div",{style:{padding:"12px",backgroundColor:"#ffffff",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.1)",transition:"all 0.3s ease"},children:[n(Ms,{title:J(N==null?void 0:N.fieldName)||"Field Name",children:R(mt,{variant:"body1",sx:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%",fontWeight:600,fontSize:"12px",marginBottom:"4px",display:"flex",alignItems:"center"},children:[J(N==null?void 0:N.fieldName)||"Field Name",((N==null?void 0:N.visibility)==="Required"||(N==null?void 0:N.visibility)==="MANDATORY")&&n("span",{style:{color:"#d32f2f",marginLeft:"2px"},children:"*"})]})}),n(Ms,{title:D||"--",children:n("div",{style:{fontSize:"0.8rem",color:"#333333",marginTop:"4px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%"},children:n("span",{style:{fontWeight:500,color:"grey",letterSpacing:"0.5px",wordSpacing:"1px"},children:D||"--"})})})]})},N==null?void 0:N.id)})})})]},U)),n(eb,{initialReqScreen:he,isreqBench:u}),S&&n(Ss,{children:g&&!Q?n(QA,{data:g}):n(tb,{})})]}),(!O||se&&!u||u&&ko.includes(e==null?void 0:e.requestStatus))&&n(ye,{sx:{borderTop:"1px solid #e0e0e0",padding:"16px"},children:n($r,{activeTab:Dl.PREVIEW,submitForApprovalDisabled:!sr(s),filteredButtons:L,childRequestHeaderData:(k=t==null?void 0:t[f==null?void 0:f.selectedMaterialID])==null?void 0:k.Tochildrequestheaderdata})})]})};var gc={},ob=Go;Object.defineProperty(gc,"__esModule",{value:!0});var fg=gc.default=void 0,nb=ob(Bo()),rb=$o;fg=gc.default=(0,nb.default)((0,rb.jsx)("path",{d:"M12 6c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2m0 9c2.7 0 5.8 1.29 6 2v1H6v-.99c.2-.72 3.3-2.01 6-2.01m0-11C9.79 4 8 5.79 8 8s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4m0 9c-2.67 0-8 1.34-8 4v3h16v-3c0-2.66-5.33-4-8-4"}),"PermIdentityOutlined");const lb={AdditionalData:{fieldName:["Material","AltUnit","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Alternative Unit of Measure","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},BasicData:{fieldName:["Material","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},MRPData:{fieldName:["Material","Plant","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Plant","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},PurchasingData:{fieldName:["Material","Plant","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Plant","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},SalesData:{fieldName:["Material","SalesOrg","DistrChan","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Sales Org","Distribution Channel","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},SalesPlantData:{fieldName:["Material","Plant","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Plant","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},SalesGeneralData:{fieldName:["Material","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},PurchasingGeneralData:{fieldName:["Material","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},WarehouseData:{fieldName:["Material","WhseNo","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Warehouse","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},DescriptionData:{fieldName:["Material","Langu","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Language","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},AdditionalEANData:{fieldName:["Material","AltUnit","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Alternative Unit of Measure","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},CostingData:{fieldName:["Material","Plant","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Plant","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},AccountingData:{fieldName:["Material","Plant","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Plant","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},WorkSchedulingData:{fieldName:["Material","Plant","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Plant","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]},TaxClassificationData:{fieldName:["Material","Country","TaxType","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Country","Tax Type","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]}},ib={FinanceCostData:{fieldName:["Material","Plant","FieldName","SAPValue","PreviousValue","CurrentValue","ChangedBy","ChangedOn"],headerName:["Material","Plant","Changed Fields","SAP Value","Old Value","New Value","Changed by","Updated on"]}},ab=({open:e,closeModal:t,requestId:r,requestType:o})=>{const{customError:c}=sn(),[s,m]=d.useState(!0),[l,u]=d.useState(null),se=tn(),q=new URLSearchParams(se.search.split("?")[1]).get("RequestId"),[g,V]=d.useState(()=>{var T;const p=q!=null&&q.includes("FCA")?ib:lb;return(T=Object.entries(p))==null?void 0:T.map(([v])=>({label:v,columns:p[v],rows:[]}))}),[Q,H]=d.useState(()=>(g==null?void 0:g.length)>0?{number:0,label:g[0].label}:{number:0,label:""}),he=(p,T)=>{H({number:T,label:g[T].label})},ne={position:"absolute",top:"50%",left:"52%",transform:"translate(-50%, -50%)",width:"80%",height:"auto",bgcolor:"#fff",boxShadow:4,p:2},J=()=>{t(!1)};d.useEffect(()=>{(async()=>{if(e&&!l)try{const T=await f(r,o);u(T)}catch(T){c(dn.FETCH_CHANGELOG_ERROR,T)}})()},[e,r]),d.useEffect(()=>{if(l)try{V(p=>p==null?void 0:p.map(T=>{const v=ql(Yp,T.label),C=l[T.label]||[];return{...T,rows:C==null?void 0:C.map(S=>({id:Co(),...S,Material:na(S==null?void 0:S.ObjectNo,1),SAPValue:di(S==null?void 0:S.SAPValue),PreviousValue:di(S==null?void 0:S.PreviousValue),CurrentValue:di(S==null?void 0:S.CurrentValue),ChangedOn:di(S==null?void 0:S.ChangedOn),...(v==null?void 0:v.length)>0&&{[v[0]]:na(S==null?void 0:S.ObjectNo,2)},...(v==null?void 0:v.length)>1&&{[v[1]]:na(S==null?void 0:S.ObjectNo,3)}}))}}))}catch(p){c(dn.CHANGE_LOG_MESSAGE,p)}},[l]);const f=p=>{var C;m(!0);const T=`/${Re}/${(C=Fe)==null?void 0:C.CHG_DISPLAY_REQUESTOR.FETCH_CHANGELOG_DATA}`;let v={ChildRequestId:p};return new Promise((S,L)=>{Xe(T,"post",k=>{var U;if((k==null?void 0:k.statusCode)===Jt.STATUS_200&&((U=k==null?void 0:k.body)==null?void 0:U.length)>0){const x=Qp(k==null?void 0:k.body);m(!1),S(x)}else m(!1),S([])},k=>{m(!1),c(k),L(k)},v)})},z=new Date,j={convertJsonToExcel:()=>{const p=g==null?void 0:g.map(T=>{var C;const v=(C=T==null?void 0:T.columns)==null?void 0:C.fieldName.map((S,L)=>({header:T==null?void 0:T.columns.headerName[L],key:S}));return{sheetName:T==null?void 0:T.label,fileName:q!=null&&q.includes("FCA")?`Finance Costing Changelog Data-${Fn(z).format("DD-MMM-YYYY")}`:`Create Changelog Data-${Fn(z).format("DD-MMM-YYYY")}`,columns:v,rows:T==null?void 0:T.rows}});Jp(p)},button:()=>n(It,{sx:{textTransform:"capitalize",position:"absolute",right:0,top:0},onClick:()=>j.convertJsonToExcel(),children:"Download"})};return R(Ss,{children:[s&&n(Cn,{blurLoading:s,loaderMessage:ya.CHANGELOG_LOADING}),n(Kp,{open:e,onClose:J,"aria-labelledby":"modal-modal-title","aria-describedby":"modal-modal-description",children:R(ye,{sx:ne,children:[n(bo,{children:R(vs,{item:!0,md:12,sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[R(ye,{sx:{display:"flex",alignItems:"center",gap:"8px"},children:[n(LT,{sx:{color:Be.black.dark,fontSize:"20px","&:hover":{transform:"rotate(360deg)",transition:"0.9s"},textAlign:"center",marginTop:"4px"}}),n(mt,{id:"modal-modal-title",variant:"subtitle1",fontSize:"16px",fontWeight:"bold",sx:{color:Be.black.dark},children:"Change Log"})]}),R(ye,{sx:{display:"flex",alignItems:"center",gap:"8px"},children:[n(Ms,{title:"Export Table",children:n(Ds,{sx:Xp,onClick:j.convertJsonToExcel,children:n(Ti,{iconName:"IosShare"})})}),n(Ds,{sx:{padding:"0 0 0 5px"},onClick:J,children:n(Gl,{})})]})]})}),n(Hl,{value:Q==null?void 0:Q.number,onChange:he,variant:"scrollable",scrollButtons:"auto","aria-label":"modal tabs",children:g==null?void 0:g.map((p,T)=>n(jn,{label:p.label.replace(Vp.ADDING_SPACE," $1").trim()},T))}),n("div",{className:"tab-content",style:{position:"relative",height:"100%",marginTop:16},children:g==null?void 0:g.map((p,T)=>{var v,C,S,L;return(Q==null?void 0:Q.number)===T&&n(mt,{id:`modal-tab-content-${T}`,sx:{mt:1},children:n(vs,{item:!0,sx:{position:"relative"},children:n(bo,{children:n(Di,{rows:p==null?void 0:p.rows,columns:(C=(v=p==null?void 0:p.columns)==null?void 0:v.fieldName)==null?void 0:C.map((A,O)=>{var k;return{field:A,headerName:(k=p==null?void 0:p.columns)==null?void 0:k.headerName[O],flex:1,minWidth:100}}),getRowIdValue:"id",pageSize:(L=(S=p==null?void 0:p.columns)==null?void 0:S.fieldName)==null?void 0:L.length,autoHeight:!0,scrollbarSize:10,sx:{"& .MuiDataGrid-row:hover":{backgroundColor:`${Be.primary.light}40`},backgroundColor:Be.primary.white}})})})},T)})})]})})]})},cb=({handleDownload:e,setEnableDocumentUpload:t,enableDocumentUpload:r,handleUploadMaterial:o})=>{const{t:c}=bn();return R(vs,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"12px",border:"1px solid #E0E0E0",mt:1,boxShadow:"0px 8px 24px rgba(48, 38, 185, 0.12)",padding:"1.5rem 2rem"},children:[n(vs,{container:!0,alignItems:"center",sx:{mb:2},children:n(mt,{sx:{fontSize:"12px",fontWeight:"700"},children:c("Excel Operations")})}),R(vs,{container:!0,spacing:2,sx:{display:"flex",justifyContent:"center",position:"relative"},children:[n(vs,{item:!0,xs:12,md:6,sx:{display:"flex",justifyContent:"center"},children:R(Ur,{elevation:0,onClick:e,sx:{width:"100%",maxWidth:"280px",height:"200px",borderRadius:"16px",padding:"1.5rem",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",position:"relative",overflow:"hidden",cursor:"pointer",transition:"all 0.3s ease","&:hover":{backgroundColor:"rgba(63, 140, 218, 0.1)"}},children:[n(ye,{sx:{backgroundColor:"rgba(63, 140, 218, 0.2)",borderRadius:"50%",padding:"16px",marginBottom:"16px"},children:n(PT,{sx:{fontSize:64,color:"#1976d2",filter:"drop-shadow(0px 4px 6px rgba(7, 31, 54, 0.3))"}})}),n(mt,{sx:{fontSize:"16px",fontWeight:"600",color:"#0D47A1",mb:1},children:c("Download Excel")}),n(mt,{sx:{fontSize:"12px",color:"#1565C0",textAlign:"center"},children:c("Download Excel if you have not downloaded yet")})]})}),n(Yh,{orientation:"vertical",flexItem:!0,sx:{position:"absolute",height:"80%",top:"10%",left:"50%",display:{xs:"none",md:"block"}}}),n(vs,{item:!0,xs:12,md:6,sx:{display:"flex",justifyContent:"center"},children:R(Ur,{elevation:0,onClick:()=>t(!0),sx:{width:"100%",maxWidth:"280px",height:"200px",borderRadius:"16px",padding:"1.5rem",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",position:"relative",overflow:"hidden",cursor:"pointer",transition:"all 0.3s ease","&:hover":{backgroundColor:"rgba(55, 43, 224, 0.1)"}},children:[n(ye,{sx:{backgroundColor:"rgba(55, 43, 224, 0.2)",borderRadius:"50%",padding:"16px",marginBottom:"16px"},children:n(qT,{sx:{fontSize:64,color:"#3026B9",filter:"drop-shadow(0px 4px 6px rgba(58, 48, 199, 0.4))"}})}),n(mt,{sx:{fontSize:"16px",fontWeight:"600",color:"#3026B9",mb:1},children:c("Upload Excel")}),n(mt,{sx:{fontSize:"12px",color:"#5148C2",textAlign:"center"},children:c("Upload your Excel after doing the necessary changes")})]})})]}),r&&n(UT,{artifactId:"",artifactName:"",setOpen:t,handleUpload:o})]})},db=e=>{var j;const t=Ro(),[r,o]=d.useState(!1),c=oe(p=>p.payload.fcRows),s=oe(p=>p.payload.unselectedRows),m=oe(p=>p.paginationData),l=oe(p=>p.payload.filteredButtons),u=oe(p=>p.userManagement.taskData),se=oe(p=>p.payload.selectedRows),ie=oe(p=>p.payload.payloadData),q=(ie==null?void 0:ie.RequestType)||"",{getButtonsDisplay:g}=of(),{getNextDisplayDataForCreate:V}=Gi(),Q=tn(),he=new URLSearchParams(Q.search).get("reqBench");let ne=Q.state;d.useEffect(()=>{u!=null&&u.ATTRIBUTE_1&&g()},[u]),d.useEffect(()=>{var p;if((c==null?void 0:c.length)>0&&!(he&&!((p=ko)!=null&&p.includes(ne==null?void 0:ne.reqStatus)))){const T=new Set(s.map(C=>C.id)),v=c.filter(C=>!T.has(C.id)).map(C=>C.id);t(Tr(v))}return()=>{t(Tr([])),t(oh([]))}},[c,t]);const J=p=>{t(Tr(p));const T=c==null?void 0:c.filter(v=>!p.includes(v.id));t(oh(T))},f=[{field:"FinanceCostingId",headerName:"ID",flex:1,hide:!0},{field:"RequestId",headerName:"Req ID",flex:1.7,editable:!1},{field:"RequestType",headerName:"Req Type",flex:1.2,editable:!1},{field:"Requester",headerName:"Requestor",flex:1.6,editable:!1},{field:"CreatedOn",headerName:"Created On(SAP)",flex:1.3,editable:!1,valueFormatter:p=>p.value?Fn(p.value).format("DD MMM YYYY"):""},{field:"Material",headerName:"Material Number",flex:1.3,editable:!1},{field:"MatlType",headerName:"Material Type",flex:1,editable:!1},{field:"Plant",headerName:"Plant",flex:1,editable:!1},{field:"FStdPrice",headerName:"Standard Price",flex:1,editable:!1},{field:"IntlPoPrice",headerName:"Initial PO Price",flex:1,editable:!1,valueFormatter:p=>p.value?Number(p.value).toFixed(2):""},{field:"PryVendor",headerName:"Primary Vendor",flex:1.3,editable:!1},{field:"FlagForBOM",headerName:"Flag For BOM",flex:1,editable:!1},{field:"VolInEA",headerName:"Volume EA",flex:1,editable:!1,valueFormatter:p=>p.value?Number(p.value).toFixed(2):""},{field:"VolInCA",headerName:"Volume CA",flex:1,editable:!1,valueFormatter:p=>p.value?Number(p.value).toFixed(2):""},{field:"VolInCAR",headerName:"Volume Carton",flex:1,editable:!1,valueFormatter:p=>p.value?Number(p.value).toFixed(2):""},{field:"NoOfUnitForCA",headerName:"Number Of Unit For CA",flex:1,editable:!1,valueFormatter:p=>p.value?Number(p.value).toFixed(0):""},{field:"NoOfUnitForCT",headerName:"Number Of Unit For CT",flex:1,editable:!1,valueFormatter:p=>p.value?Number(p.value).toFixed(0):""}],z=p=>{t(fr(p))};return d.useEffect(()=>{var p;(m==null?void 0:m.page)!==0&&q===((p=b)==null?void 0:p.FINANCE_COSTING)&&V()},[m==null?void 0:m.page]),R(Ss,{children:[n(Di,{isLoading:r,module:"FinanceCosting",width:"100%",title:"Finance Costing Details",rows:c,columns:f,getRowIdValue:"id",hideFooter:!1,checkboxSelection:!(he&&!((j=ko)!=null&&j.includes(ne==null?void 0:ne.reqStatus))),disableSelectionOnClick:!0,tempheight:"calc(100vh - 300px)",selectionModel:se,onRowsSelectionHandler:J,rowCount:(m==null?void 0:m.totalElements)||0,pageSize:100,onPageChange:p=>z(p)}),n($r,{filteredButtons:l,setCompleted:e==null?void 0:e.setCompleted})]})},hS=()=>{var Bs,st,Ts,to,St,es,ot,Dt,Rs,ae,$e,me,Ne,Ve,Oe,ct,ke,Vt,Es,Pt;const[e,t]=d.useState(!1),[r,o]=d.useState([]),[c,s]=d.useState(!1),[m,l]=d.useState([]),[u,se]=d.useState(!1),[ie,q]=d.useState(!1),[g,V]=d.useState(""),[Q,H]=d.useState(!1),[he,ne]=d.useState([]),[J,f]=d.useState(!1),[z,j]=d.useState(!1),[p,T]=d.useState(""),[v,C]=d.useState(),[S,L]=d.useState(""),[A,O]=d.useState(!1),[k,U]=d.useState(""),[x,N]=d.useState("success"),[D,I]=d.useState(!1),[ge,ee]=d.useState(!1),[Ue,M]=d.useState(!1),[Se,ce]=d.useState(!1),$=Ro(),qe=oe(je=>je.applicationConfig),F=oe(je=>je.payload.payloadData),Ke=oe(je=>{var Pe;return(Pe=je.request.requestHeader)==null?void 0:Pe.requestId}),Us=oe(je=>je.request.requestHeader.requestType),Wt=oe(je=>{var Pe;return(Pe=je.userManagement)==null?void 0:Pe.taskData}),{getDtCall:$s,dtData:Fs}=qh(),ss=wr(),[io,Ws]=d.useState(!0),ks=oe(je=>je.request.tabValue),{t:Qt}=bn(),{getRequestHeaderTemplate:fs}=Qh(),Ht=[Qt("Request Header"),Qt("Material List"),Qt("Attachments & Remarks"),Qt("Preview")],[ns,Zt]=d.useState([!1]),xs=je=>{$(yr(je))},lt=tn(),He=lt.state,Rt=((Bs=lt.state)==null?void 0:Bs.isChildRequest)??!1,Bt=new URLSearchParams(lt.search.split("?")[1]).get("RequestId"),Me=new URLSearchParams(lt.search),it=Me.get("RequestId"),Ut=Me.get("RequestType"),Ns=Me.get("reqBench"),{getDisplayData:ds}=gT(),rs=()=>{f(!0)},at=()=>{f(!1)},yt=()=>{j(!0)},ws=je=>{j(je)},Hs=()=>{S==="success"?ss("/requestBench"):at()},os=()=>{s(!0)},ls=je=>{let Pe="";Ut===b.CREATE_WITH_UPLOAD?Pe="getAllMaterialsFromExcel":Ut===b.EXTEND_WITH_UPLOAD?Pe="getAllMaterialsFromExcelForMassExtend":Pe="getAllMaterialsFromExcelForMassChange",U("Initiating Excel Upload"),O(!0);const _t=new FormData;[...je].forEach(Ft=>_t.append("files",Ft)),_t.append("dtName",Ut===b.CREATE_WITH_UPLOAD||Ut===b.EXTEND_WITH_UPLOAD?"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG":"MDG_MAT_CHANGE_TEMPLATE"),_t.append("version",Ut===b.CREATE_WITH_UPLOAD||Ut===b.EXTEND_WITH_UPLOAD?"v1":"v5"),_t.append("requestId",Bt?Bt.slice(3):""),_t.append("region",F!=null&&F.Region?F==null?void 0:F.Region:"US"),_t.append("matlType","ALL");const jt=Ft=>{var te;(Ft==null?void 0:Ft.statusCode)===Jt.STATUS_200?(H(!1),O(!1),U(""),ss((te=Eo)==null?void 0:te.REQUEST_BENCH)):(H(!1),O(!1),C(Ft==null?void 0:Ft.message),U(""),N("error"),gs())},_s=Ft=>{O(!1),C(Ft==null?void 0:Ft.message),U(""),N("error"),gs()};Xe(`/${Re}/massAction/${Pe}`,"postformdata",jt,_s,_t)};d.useEffect(()=>((async()=>{var Pe;if(it){const _t=Bl(mr.CURRENT_TASK,!0,{}),jt=Ut||(Wt==null?void 0:Wt.ATTRIBUTE_2)||(_t==null?void 0:_t.ATTRIBUTE_2);await ds(it,jt,Ns,Wt,He),(Ut===b.CHANGE_WITH_UPLOAD&&!((Pe=He==null?void 0:He.material)!=null&&Pe.length)||Ut===b.CREATE_WITH_UPLOAD||Ut===b.EXTEND_WITH_UPLOAD)&&((He==null?void 0:He.reqStatus)===Ao.DRAFT||(He==null?void 0:He.reqStatus)===Ao.UPLOAD_FAILED)?($(yr(0)),se(!1),q(!1)):($(yr(1)),se(!0),q(!0)),ee(!0)}else $(yr(0))})(),()=>{$(kh([])),$(Zp()),$(eT()),$(tT()),$(sT()),$(oT()),$(kr({})),$(fi({data:{}})),$(er([])),$(Rh([])),$(wa({})),$(nT()),$(Tr([])),$(gr([])),$(pr({})),nh(mr.CURRENT_TASK),nh(mr.ROLE)}),[Bt,$]);function ao(je){let Pe={decisionTableId:null,decisionTableName:Ei.MDG_MAT_REGION_DIVISION_MAPPING,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":je}]};$s(Pe)}d.useEffect(()=>{F!=null&&F.Region&&ao(F==null?void 0:F.Region)},[F==null?void 0:F.Region]),d.useEffect(()=>{var je,Pe;if(Fs){const jt=[...rT((Pe=(je=Fs==null?void 0:Fs.result)==null?void 0:je[0])==null?void 0:Pe.MDG_MAT_REGION_DIVISION_MAPPING)].sort((_s,Ft)=>_s.code.localeCompare(Ft.code));$(xo({keyName:"Division",data:jt})),Ws(!1),U(ya.DT_LOADING)}},[Fs]),d.useEffect(()=>(fs(),Ye(),$(Oo([])),$(xo({keyName:"Region",data:lT})),$(xo({keyName:"DiversionControlFlag",data:iT})),()=>{$(Uh({}))}),[]),d.useEffect(()=>{u&&Zt([!0])},[u]),d.useEffect(()=>{V(aT("MAT"))},[]);const Ye=()=>{let je={decisionTableId:null,decisionTableName:"MDG_ATTACHMENTS_LIST_DT",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE":"Material","MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO":"Create","MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE":1}],systemFilters:null,systemOrders:null,filterString:null};t(!0);const Pe=jt=>{var _s,Ft,te,xe;if(t(!1),jt.statusCode===200){let B=(Ft=(_s=jt==null?void 0:jt.data)==null?void 0:_s.result[0])==null?void 0:Ft.MDG_ATTACHMENTS_ACTION_TYPE,de=[];B==null||B.map((Te,We)=>{var le={id:We};de.push(le)}),l(de);const w=((xe=(te=jt==null?void 0:jt.data)==null?void 0:te.result[0])==null?void 0:xe.MDG_ATTACHMENTS_ACTION_TYPE)||[];ne(w)}},_t=jt=>{console.log(jt)};qe.environment==="localhost"?Xe(`/${yo}/rest/v1/invoke-rules`,"post",Pe,_t,je):Xe(`/${yo}/v1/invoke-rules`,"post",Pe,_t,je)},Mt=()=>{var Ft,te,xe,B,de,w;const je=it!=null&&it.includes("FCA")?Fe.EXCEL.DOWNLOAD_EXCEL_FINANCE:Fe.EXCEL.DOWNLOAD_EXCEL_MAT;U("Please wait 1-2 minutes while real-time data are exported into the template. Thank you for your patience."),O(!0);let Pe={massSchedulingId:F==null?void 0:F.RequestId},_t={dtName:(F==null?void 0:F.RequestType)===((Ft=b)==null?void 0:Ft.CHANGE)||(F==null?void 0:F.RequestType)===((te=b)==null?void 0:te.CHANGE_WITH_UPLOAD)?"MDG_MAT_CHANGE_TEMPLATE":"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",version:(F==null?void 0:F.RequestType)===((xe=b)==null?void 0:xe.CHANGE)||(F==null?void 0:F.RequestType)===((B=b)==null?void 0:B.CHANGE_WITH_UPLOAD)?"v4":"v1",requestId:(F==null?void 0:F.RequestId)||Ke||"",scenario:(F==null?void 0:F.RequestType)===((de=b)==null?void 0:de.CHANGE)||(F==null?void 0:F.RequestType)===((w=b)==null?void 0:w.CHANGE_WITH_UPLOAD)?"Change with Upload":"Create with Upload",templateName:(F==null?void 0:F.TemplateName)||"",region:(F==null?void 0:F.Region)||"",matlType:"ALL"};const jt=Te=>{const We=URL.createObjectURL(Te),le=document.createElement("a");le.href=We,le.setAttribute("download",`${F!=null&&F.TemplateName?F==null?void 0:F.TemplateName:it!=null&&it.includes("FCA")?b.FINANCE_COSTING:"Mass_Create"}_Data Export.xlsx`),document.body.appendChild(le),le.click(),document.body.removeChild(le),URL.revokeObjectURL(We),O(!1),U(""),C(`${F!=null&&F.TemplateName?F==null?void 0:F.TemplateName:it!=null&&it.includes("FCA")?b.FINANCE_COSTING:"Mass_Create"}_Data Export.xlsx has been exported successfully.`),N("success"),gs()},_s=()=>{};Xe(`/${Re}${je}`,"postandgetblob",jt,_s,it!=null&&it.includes("FCA")?Pe:_t)},gs=()=>{I(!0)},Lt=()=>{I(!1)},ps=()=>{var je,Pe,_t;Bt&&!Ns?ss((je=Eo)==null?void 0:je.MY_TASK):Ns?ss((Pe=Eo)==null?void 0:Pe.REQUEST_BENCH):!Bt&&!Ns&&ss((_t=Eo)==null?void 0:_t.MASTER_DATA)},Js=()=>{ce(!1)};return R(Ss,{children:[io&&n(Cn,{blurLoading:A,loaderMessage:k}),R(ye,{sx:{padding:2},children:[R(vs,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[Ke||Bt?R(mt,{variant:"h6",sx:{mb:1,textAlign:"left",display:"flex",alignItems:"center",gap:1},children:[n(fg,{sx:{fontSize:"1.5rem"}}),Qt("Request Header ID"),": ",n("span",{children:Ke?ua(Us,Ke):Bt})]}):n("div",{style:{flex:1}}),ks===1&&R(ye,{sx:{display:"flex",justifyContent:"flex-end",gap:"1rem"},children:[n(It,{variant:"outlined",size:"small",title:Qt("Download Error Report"),disabled:!it,onClick:()=>{ss(`/requestBench/errorHistory?RequestId=${it||""}`,{state:{display:!0,childRequest:Rt}})},color:"primary",children:n(cT,{sx:{padding:"2px"}})}),(F==null?void 0:F.RequestType)===b.CREATE||(F==null?void 0:F.RequestType)===b.EXTEND||(F==null?void 0:F.RequestType)===b.EXTEND_WITH_UPLOAD||(F==null?void 0:F.RequestType)===b.CREATE_WITH_UPLOAD||Bt!=null&&Bt.includes("FCA")?n(It,{variant:"outlined",disabled:!it,size:"small",onClick:()=>M(!0),title:Bt!=null&&Bt.includes("FCA")?Qt("Finance Costing Change Log"):Qt("Create Change Log"),children:n(rh,{sx:{padding:"2px"}})}):n(It,{variant:"outlined",disabled:!it,size:"small",onClick:yt,title:Qt("Change Log"),children:n(rh,{sx:{padding:"2px"}})}),n(It,{variant:"outlined",disabled:!it,size:"small",onClick:Mt,title:Qt("Export Excel"),children:n(pT,{sx:{padding:"2px"}})})]}),z&&n(DT,{open:!0,closeModal:ws,requestId:Ke||Bt.slice(3),requestType:F==null?void 0:F.RequestType}),Ue&&n(ab,{open:!0,closeModal:()=>M(!1),requestId:Ke||Bt.slice(3),requestType:F==null?void 0:F.RequestType})]}),(F==null?void 0:F.TemplateName)&&R(mt,{variant:"h6",sx:{mb:1,textAlign:"left",display:"flex",alignItems:"center",gap:1},children:[n(La,{sx:{fontSize:"1.5rem"}}),Qt("Template Name"),": ",n("span",{children:F==null?void 0:F.TemplateName})]}),n(Ds,{onClick:()=>{var je,Pe;if(Ns&&!((je=ko)!=null&&je.includes(F==null?void 0:F.RequestStatus))){ss((Pe=Eo)==null?void 0:Pe.REQUEST_BENCH);return}ce(!0)},color:"primary","aria-label":"upload picture",component:"label",sx:{left:"-10px"},title:Qt("Back"),children:n(fa,{sx:{fontSize:"25px",color:"#000000"}})}),n(HT,{nonLinear:!0,activeStep:ks,sx:{display:"flex",alignItems:"center",justifyContent:"center",margin:"25px 14%",marginTop:"-35px"},children:Ht.map((je,Pe)=>n(kT,{completed:ns[Pe],children:n(BT,{color:"error",disabled:Pe===1&&!u||Pe===2&&!ie||Pe===3&&!ie,onClick:()=>xs(Pe),sx:{fontSize:"50px",fontWeight:"bold"},children:n("span",{style:{fontSize:"15px",fontWeight:"bold"},children:je})})},je))}),n(ki,{dialogState:J,openReusableDialog:rs,closeReusableDialog:at,dialogTitle:p,dialogMessage:v,handleDialogConfirm:at,dialogOkText:"OK",handleOk:Hs,dialogSeverity:S}),n(Cn,{blurLoading:A,loaderMessage:k}),ks===0&&R(Ss,{children:[n(dE,{setIsSecondTabEnabled:se,setIsAttachmentTabEnabled:q,requestStatus:He!=null&&He.reqStatus?He==null?void 0:He.reqStatus:Ao.ENABLE_FOR_FIRST_TIME,downloadClicked:c,setDownloadClicked:s}),(Ut===b.CHANGE_WITH_UPLOAD||Ut===b.CREATE_WITH_UPLOAD||Ut===b.EXTEND_WITH_UPLOAD)&&((He==null?void 0:He.reqStatus)==Ao.DRAFT&&!((st=He==null?void 0:He.material)!=null&&st.length)||(He==null?void 0:He.reqStatus)==Ao.UPLOAD_FAILED)&&n(cb,{handleDownload:os,setEnableDocumentUpload:H,enableDocumentUpload:Q,handleUploadMaterial:ls}),((F==null?void 0:F.RequestType)===((Ts=b)==null?void 0:Ts.CHANGE)||(F==null?void 0:F.RequestType)===((to=b)==null?void 0:to.CHANGE_WITH_UPLOAD))&&!it&&(F==null?void 0:F.DirectAllowed)!=="X"&&(F==null?void 0:F.DirectAllowed)!==void 0&&R(mt,{sx:{fontSize:"13px",fontWeight:"500",color:(es=(St=Be)==null?void 0:St.error)==null?void 0:es.dark,marginTop:"1rem",marginLeft:"0.5rem"},children:[n(ye,{component:"span",sx:{fontWeight:"bold"},children:"Note:"})," ","You are not authorized to Tcode"," ",R(ye,{component:"span",sx:{fontWeight:"bold"},children:[" ","MM02."]})]})]}),ks===1&&((F==null?void 0:F.RequestType)===((ot=b)==null?void 0:ot.CREATE)||(Wt==null?void 0:Wt.ATTRIBUTE_2)===((Dt=b)==null?void 0:Dt.CREATE)||Ut===((Rs=b)==null?void 0:Rs.CREATE)||Ut===((ae=b)==null?void 0:ae.CREATE_WITH_UPLOAD)?n(gA,{requestStatus:He!=null&&He.reqStatus?He==null?void 0:He.reqStatus:Ao.ENABLE_FOR_FIRST_TIME,mandFields:r,addHardCodeData:ge,setIsAttachmentTabEnabled:q,setCompleted:Zt}):(F==null?void 0:F.RequestType)===(($e=b)==null?void 0:$e.EXTEND)||(Wt==null?void 0:Wt.ATTRIBUTE_2)===((me=b)==null?void 0:me.EXTEND)||(Wt==null?void 0:Wt.ATTRIBUTE_2)===((Ne=b)==null?void 0:Ne.EXTEND_WITH_UPLOAD)||Ut===((Ve=b)==null?void 0:Ve.EXTEND)||Ut===((Oe=b)==null?void 0:Oe.EXTEND_WITH_UPLOAD)?n(bA,{requestStatus:He!=null&&He.reqStatus?He==null?void 0:He.reqStatus:Ao.ENABLE_FOR_FIRST_TIME,mandFields:r,addHardCodeData:ge,setIsAttachmentTabEnabled:q,setCompleted:Zt}):(F==null?void 0:F.RequestType)===((ct=b)==null?void 0:ct.FINANCE_COSTING)||(Wt==null?void 0:Wt.ATTRIBUTE_2)===((ke=b)==null?void 0:ke.FINANCE_COSTING)||Ut===((Vt=b)==null?void 0:Vt.FINANCE_COSTING)?n(db,{setCompleted:Zt}):n(nf,{setIsAttachmentTabEnabled:!0,setCompleted:Zt,downloadClicked:c,setDownloadClicked:s})),ks===2&&n(NA,{requestStatus:He!=null&&He.reqStatus?He==null?void 0:He.reqStatus:Ao.ENABLE_FOR_FIRST_TIME,attachmentsData:he,requestIdHeader:Ke?ua(Us,Ke):Bt,pcNumber:g}),ks===3&&n(ye,{sx:{width:"100%",overflow:"auto"},children:n(sb,{requestStatus:He!=null&&He.reqStatus?He==null?void 0:He.reqStatus:Ao.ENABLE_FOR_FIRST_TIME})})]}),n(Un,{openSnackBar:D,alertMsg:v,alertType:x,handleSnackBarClose:Lt}),Se&&R(Pi,{isOpen:Se,titleIcon:n(dT,{size:"small",sx:{color:(Pt=(Es=Be)==null?void 0:Es.secondary)==null?void 0:Pt.amber,fontSize:"20px"}}),Title:Qt("Warning"),handleClose:Js,children:[n(Lo,{sx:{mt:2},children:Qt(Dn.LEAVE_PAGE_MESSAGE)}),R(Do,{children:[n(It,{variant:"outlined",size:"small",sx:{..._a},onClick:Js,children:Qt("No")}),n(It,{variant:"contained",size:"small",sx:{...qi},onClick:ps,children:Qt("Yes")})]})]})]})};export{hS as default};
