import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON>ack, Grid, Box, TextField, Autocomplete, Backdrop, CircularProgress, BottomNavigation, Paper, Card } from "@mui/material";

import IconButton from "@mui/material/IconButton";
import Typography from "@mui/material/Typography";
import { Editor } from "react-draft-wysiwyg";
import { styled } from "@mui/system";
import Confirmation from "./ConfirmationDialog";
import { EditorState, ContentState } from "draft-js";
import draftToHtml from "draftjs-to-html";
import htmlToDraft from "html-to-draftjs";
import "react-draft-wysiwyg/dist/react-draft-wysiwyg.css";
import { MentionsInput, Mention } from "react-mentions";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import { destination_MaterialMgmt } from "../../../destinationVariables";
import { END_POINTS } from "@constant/apiEndPoints";
import { colors } from "@constant/colors";

import { doAjax, doCrudApi } from "../utility/serviceRequest";
import { useSelector } from "react-redux";
import CompletePreview from "./CompletePreview";

import { CheckOutlined, Code, Email, Phone, Sms, WhatsApp } from "@mui/icons-material";
import ChannelConfiguration from "./ChannelConfiguration";
import ReusablePromptBox from "../component/PromptBox_Email/ReusablePromptBox";
import { doAjax as doAjaxMDG } from "@components/Common/fetchService";

const defaultMentionStyle = {
  backgroundColor: colors.template.mentionBackground,
  width: "100%",
};
const defaultStyle = {
  control: {
    backgroundColor: "#fff",
    fontSize: 14,
    fontWeight: "normal",
  },
  "&multiLine": {
    control: {
      fontFamily: `"Roboto", sans-serif !important`,
      border: "1px solid silver",
      borderRadius: "5px",
      overflowWrap: "anywhere",
      height: "2.5rem",
    },
    highlighter: {
      padding: 9,
      boxSizing: "border-box",
      overflow: "hidden",
      height: "2.5rem",
      overflowWrap: "anywhere",
      border: "1px solid transparent",
      // color:"blue"
    },
    input: {
      padding: 9,
      border: "1px solid silver",
      overflow: "auto",
      height: "2.5rem",
      overflowWrap: "anywhere",
    },
  },

  "&singleLine": {
    display: "inline-block",
    width: "100%",
    overflowWrap: "anywhere",
    highlighter: {
      padding: 1,
      border: "2px inset transparent",
    },
    input: {
      padding: 1,
      border: "2px inset",
    },
  },

  suggestions: {
    bottom: "0",
    minHeight: 95,
    overflowY: "auto",
    list: {
      backgroundColor: colors.primary.white,
      border: `1px solid ${colors.template.suggestionBorder}`,
      fontSize: 14,
    },
    item: {
      padding: "5px 15px",
      borderBottom: `1px solid ${colors.template.suggestionItem}`,

      "&focused": {
        backgroundColor: colors.template.suggestionFocused,
      },
    },
  },
};

const channelDataTemplate = {
  Phone: {
    key: "Phone",
    label: "Phone",
    icon: <Phone />,
  },
  SMS: {
    key: "SMS",
    label: "SMS",
    icon: <Sms />,
  },
  WhatsApp: {
    key: "WhatsApp",
    label: "WhatsApp",
    icon: <WhatsApp />,
  },
  Email: {
    key: "Email",
    label: "Email",
    icon: <Email />,
  },
  InApp: {
    key: "InApp",
    label: "In-App",
    icon: <Code />,
  },
};

const CreateTemplate = ({ scenario, open, onClose, userList = [], groupList = [], setScenario, ...props }) => {
  const userReducer = useSelector((state) => state.userReducer);

  // const { token , destinations, environment, useWorkAccess, chartConfigs, user_id,applicationName } = userReducer;
  const [isLoader, setLoader] = React.useState(false);
  const [backdrop, setBackdrop] = React.useState(false);
  const [openPreview, setOpenPreview] = useState(false);
  const [entityList, setEntityList] = React.useState([]);
  const [processList, setProcessList] = React.useState([]);
  const [identifierList, setIdentifierList] = React.useState([]);
  const [templateData, setTemplateData] = React.useState({
    name: "",
    entity: null,
    process: null,
    identifier: null,
    subject: "",
    body: "",
    application: userReducer.applicationName,
    emailDefinitionId: "",
    createdBy: userReducer.userData.user_id,
    createdOn: new Date().toISOString(),
    updatedBy: userReducer.userData.user_id,
    updatedOn: new Date().toISOString(),
  });
  const [editorState, setEditorState] = React.useState(() => EditorState.createEmpty());
  const [processVariables, setProcessVariables] = React.useState([]);
  const [defaultEditorValue, setDefaultEditorValue] = React.useState(() => EditorState.createEmpty());
  const [showConfirmation, setShowConfirmation] = React.useState(false);
  const [confirmationMessage, setConfirmationMessage] = React.useState("");
  const [buttonAction, setButtonAction] = React.useState("Cancel");

  //entity
  const [entityData, setEntityData] = useState([]);
  const [processData, setProcessData] = useState([]);
  const [eventVariables, setEventVariables] = useState([]);
  const [selectedVariable, setSelectedVariable] = useState([]);
  const [availableVariable, setAvailableVariable] = useState([]);
  const [variableData, setVariableData] = useState([]);
  const [list1, setList1] = useState([]);
  const [list2, setList2] = useState([]);
  const [entity1, setEntity1] = useState("");
  const [openalert, setOpenalert] = useState(false);
  const [severity, setSeverity] = useState("success");
  const [emailError, setEmailError] = useState("");
  const [message, setMessage] = useState("");
  const [creationType, setCreationType] = useState("");
  const [confirmation, setConfirmation] = useState("");
  const [openEntity, setOpenEntity] = useState(false);
  const [selectedRow, setSelectedRow] = useState(null);
  const [isEventLoader, setEventLoader] = React.useState(false);
  const [isModuleLoader, setModuleLoader] = React.useState(false);
  const [identifier, setIdentifier] = useState(null);
  //entity end
  //group
  const [userDetails, setUserDetails] = useState(new Map());
  const [groupDetails, setGroupDetails] = useState(new Map());
  const [toparticipant, setToparticipant] = useState([]);
  const [toparticipantList, setToparticipantList] = useState({});
  const [ccparticipantType, setCcparticipantType] = useState({});
  const [ccToParticipant, setCcToParticipant] = useState([]);

  const [participantState, setParticipantState] = useState({
    receipentType: "",
    receipents: "",
    ccType: "",
    cc: "",
  });
  const variableDropdown = ["INITIATOR", "REVIEWER"];
  const [groupData, setGroupData] = useState([]);
  const [allGroups, setAllGroups] = useState([]);
  const [channelConfigKey, setChannelConfigKey] = useState("Email");
  const [value, setValue] = useState("");

  useEffect(() => {
    let content = props?.contentHTML ?? "";
    setEditorValue(content);
  }, []);

  const handleParticipantState = (key, value) => {
    setParticipantState((prev) => ({ ...prev, [`${key}`]: value }));
    if (participantState.receipentType == "VARIABLE" && value == "VARIABLE") {
      setToparticipant(variableDropdown);
    }
    if (key === "receipentType" && value == "GROUP") {
      var tempValue = [];
      var tempKeyValue = {};
      for (let [key, value] of groupDetails.entries()) {
        tempKeyValue[value] = key;
        tempValue.push(value);
      }
      setToparticipantList({ ...tempKeyValue, "": "" });
      setToparticipant(tempValue);

    }
    if (participantState.receipentType == "USER") {
      var tempValue = [];
      for (let [, value] of userDetails.entries()) {
        tempValue.push(value);
      }
      setToparticipant(tempValue);

    }
    if (key == "ccType" && value == "VARIABLE") {
      setCcToParticipant(variableDropdown);
    }
    if (participantState.receipentType == "VARIABLE" && value == "VARIABLE") {
      setToparticipant(variableDropdown);
    }
    if (key === "ccType" && value == "GROUP") {
      var tempValue = [];
      var tempKeyValue = {};
      for (let [key, value] of groupDetails.entries()) {
        tempKeyValue[value] = key;
        tempValue.push(value);
      }
      setToparticipantList({ ...tempKeyValue, "": "" });
      setCcToParticipant(tempValue);
    }
    if (key == "ccType" && value == "VARIABLE") {
      setCcToParticipant(variableDropdown);
    }
    if (key == "ccType" && value == "USER") {
      var tempValue = [];
      for (let [, value] of userDetails.entries()) {
        tempValue.push(value);
      }
      setCcToParticipant(tempValue);
    }
  };

  const resetDefault = () => {
    setTemplateData({
      name: "",
      entity: null,
      process: null,
      identifier: null,
      subject: "",
      body: "",
      application: "",
      emailDefinitionId: "",
      createdBy: userReducer.userData.user_id,
      createdOn: new Date().toISOString(),
      updatedBy: userReducer.userData.user_id,
      updatedOn: new Date().toISOString(),
    });
    let content = props?.contentHTML ?? "";
    setEditorValue(content);

    setParticipantState({
      receipentType: "",
      receipents: "",
      ccType: "",
      cc: "",
    });
    setEventVariables([]);
  };
  const closeConfirmationDialog = (evt) => {
    setPromptBoxState((prev) => ({ ...prev, open: false }));
    if (evt !== "Cancel" && evt !== "Discard" && evt !== "Timeout") {
      if (templateData.entity && templateData.process && templateData.name && templateData.identifier) {
        saveTemplateData(evt);
        onSaveDataElement();
        if (props?.creationType !== "edit") {
          props?.setCreationType("new");
        }
        props?.setSelectedRow(props?.data);
        props?.setIsEditing(null);
      } else {

        setLoader(false);
      }
    } else if (evt === "Discard") {
      resetDefault();
      props?.setSelectedRow(null);
      props?.setCreationType("new");
      props?.setIsEditing(null);
    } else if (evt === "Timeout") {
      window.location.reload();
    }
    setShowConfirmation(false);
    setConfirmationMessage("");
  };

  const saveTemplateData = (status) => {
    setLoader(true);
    setBackdrop(true);
    let payload = {
      application: userReducer.applicationName,
      applicationDesc: userReducer.applicationName,
      content: templateData.body,
      createdBy: templateData.createdBy,
      createdOn: templateData.createdOn,
      emailDefinitionId: templateData.emailDefinitionId,
      entity: templateData.entity.entity,
      entityDesc: templateData.entity.entityDesc,
      name: templateData.name,
      process: templateData.process.process,
      processDesc: templateData.process.processDesc,
      identifier: templateData?.identifier?.identifier,
      identifierDesc: templateData?.identifier?.identifierDesc,
      status: status,
      subject: templateData.subject.replaceAll("[", "").replaceAll("]", ""),
      updatedBy: userReducer.userData.user_id,
      updatedOn: new Date().toISOString(),
    };
    let method, url;
    if (templateData.emailDefinitionId === "") {
      method = "POST";
      url = `/WorkUtilsServices/v2/mail-definition/`;
    } else {
      method = "PATCH";
      url = `/WorkUtilsServices/v2/mail-definition/${templateData.emailDefinitionId}`;
    }
    doAjax(
      url,
      method,
      payload,
      function (oData) {
        if (oData.statusCode === 401 || oData.statusCode === "401") {
          promptAction_Functions.handleOpenPromptBox("TIMEOUT", {
            title: "Error",
            message: oData.message,
            severity: "danger",
            cancelButton: true,
            okButton: true,
            okButtonText: "Ok",
          });
          return;
        }

        props?.getMailDefinition();
        doAjax(
          `/WorkUtilsServices/v2/mail-definition`,
          "get",
          null,
          function (oData) {
            if (oData.statusCode === 401 || oData.statusCode === "401") {
              promptAction_Functions.handleOpenPromptBox("TIMEOUT", {
                title: "Error",
                message: "Session Timed Out.Kindly Refresh",
                severity: "danger",
                cancelButton: true,
                okButton: true,
                okButtonText: "Refresh",
              });
            }
            var applicationDesc = oData.data?.filter((item) => item.name === templateData.name);
            var emailDefId = applicationDesc[0].emailDefinitionId;
            if (props?.isRecepientData) {
              var url = "/WorkUtilsServices/v1/mail-mapping";
              if (props?.creationType !== "edit") {
              } else {
                method = "PATCH";
              }
              var payload = {
                ccParticipant: participantState.ccType === "GROUP" ? toparticipantList[participantState.cc] : participantState.cc,
                ccParticipantType: participantState.ccType,
                createdBy: templateData.createdBy,
                createdOn: templateData.createdOn,
                fromDestination: null,
                id: "",
                // application: userReducer.applicationName === "ITM" ? "ITM" : props?.application,
                application: userReducer.applicationName,
                name: templateData.name,
                process: templateData.process.process,
                regionId: "",
                status: status,
                templateId: emailDefId,
                toParticipant: participantState.receipentType === "GROUP" ? toparticipantList[participantState.receipents] : participantState.receipents,
                toParticipantType: participantState.receipentType,
                updatedBy: userReducer.userData.user_id,
                updatedOn: new Date().toISOString(),
              };
              doAjax(
                url,
                method,
                payload,
                (data) => {
                  if (data) {
                    setBackdrop(false);
                  }
                },
                function (error) {
                  props?.setAlert(true);
                  props?.setAlertSeverity("error");
                  props?.setAlertMessage(error);
                }
              );
            }
            setBackdrop(false);
            // props?.TemplateGroupData();
            // setLoader(false);
          },
          function (error) {
            props?.setAlert(true);
            props?.setAlertSeverity("error");
            props?.setAlertMessage(error);
          }
        );
        setLoader(false);
        props?.getMailDefinition();

        if (oData.statusCode === 200 && oData.status) {
          props?.setAlertMessage(oData.message);
          promptAction_Functions.handleOpenPromptBox("SUCCESS", {
            message: `Email template ${scenario === "CREATE" ? "created" : "updated"} succesfully`,
            redirectOnClose: false,
          });
          return;
        }
        if (oData.statusCode !== 200 && oData.status) {
          props?.setAlertMessage(oData.message);
          promptAction_Functions.handleOpenPromptBox("ERROR", {
            title: "Error",
            message: oData.message,
            severity: "danger",
            cancelButton: true,
            okButton: true,
            okButtonText: "Ok",
          });
          return;
        }
      },
      function (error) {
        props?.setAlert(true);
        props?.setAlertSeverity("error");
        props?.setAlertMessage(error);
        setLoader(false);
      }
    );
  };

  const getEntityList = (appName, identifier) => {
    setLoader(true);
    const onSuccess = (oData) => {
      if (oData?.statusCode === 401 || oData?.statusCode === "401") {
        promptAction_Functions.handleOpenPromptBox("TIMEOUT", {
          title: "Error",
          message: "Session Timed Out. Kindly Refresh",
          severity: "danger",
          cancelButton: true,
          okButton: true,
          okButtonText: "Refresh",
        });
      } else if (oData) {
        const formattedData = Array.isArray(oData) ? oData : [];
        setEntityList(formattedData);
      }
      setLoader(false);
    };

    const onError = (error) => {
      props?.setAlert?.(true);
      props?.setAlertSeverity?.("error");
      props?.setAlertMessage?.(error);
      setLoader(false);
    };

    const url = `/${destination_MaterialMgmt}${END_POINTS.EMAIL_CONFIG.FETCH_NOTIFICATION_MODULES}?identifierId=${encodeURIComponent(identifier)}`;

    doAjaxMDG(url, "get", onSuccess, onError);
  };

  const onSaveDataElement = () => {
    setEventLoader(true);
    let url = "/WorkUtilsServices/v1/application/variables";
    let payload = [];
    payload = eventVariables ? eventVariables : [{}];
    doAjax(
      url,
      "patch",
      payload,
      function (oData) {
        if (oData.statusCode === 401 || oData.statusCode === "401") {
          promptAction_Functions.handleOpenPromptBox("TIMEOUT", {
            title: "Error",
            message: "Session Timed Out.Kindly Refresh",
            severity: "danger",
            cancelButton: true,
            okButton: true,
            okButtonText: "Refresh",
          });
        }
        setEventLoader(false);
        setEmailError("Updated successfully");
        setSeverity("success");
        handleClickalert();
        setEventLoader(false);
      },
      function () {
        setEventLoader(false);
        setEmailError("error");
        setSeverity("error");
        handleClickalert();
      }
    );
  };

  const getProcessVariables = (appName, entityName, process, identifier) => {
    setLoader(true);

    doCrudApi(
      "fetchidentifierVariablesHana",
      [identifier, entityName, process],
      function (oData) {
        if (oData.statusCode === 401 || oData.statusCode === "401") {
          promptAction_Functions.handleOpenPromptBox("TIMEOUT", {
            title: "Error",
            message: "Session Timed Out.Kindly Refresh",
            severity: "danger",
            cancelButton: true,
            okButton: true,
            okButtonText: "Refresh",
          });

        }
        if (oData) setProcessVariables(oData);
        setLoader(false);
      },
      function (error) {
        props?.setAlert(true);
        props?.setAlertSeverity("error");
        props?.setAlertMessage(error);
        setLoader(false);
      }
    );
  };

  const getProcessList = (appName, entityName, identifier) => {
    setLoader(true);
    const onSuccess = (oData) => {
      if (oData?.statusCode === 401 || oData?.statusCode === "401") {
        promptAction_Functions.handleOpenPromptBox("TIMEOUT", {
          title: "Error",
          message: "Session Timed Out. Kindly Refresh",
          severity: "danger",
          cancelButton: true,
          okButton: true,
          okButtonText: "Refresh",
        });
      } else if (oData) {
        const formattedData = Array.isArray(oData) ? oData : [];
        setProcessList(formattedData);
      }
      setLoader(false);
    };

    const onError = (error) => {
      props?.setAlert?.(true);
      props?.setAlertSeverity?.("error");
      props?.setAlertMessage?.(error);
      setLoader(false);
    };

    const url = `/${destination_MaterialMgmt}${END_POINTS.EMAIL_CONFIG.FETCH_NOTIFICATION_EVENTS}?identifierId=${encodeURIComponent(identifier)}&notificationId=${encodeURIComponent(entityName)}`;

    doAjaxMDG(url, "get", onSuccess, onError);
  };

  const getIdentifierList = () => {
    setLoader(true);

    doCrudApi(
      "populateAppIdentifiersHana",
      [],

      function (oData) {
        if (oData.statusCode === 401 || oData.statusCode === "401") {
          promptAction_Functions.handleOpenPromptBox("TIMEOUT", {
            title: "Error",
            message: "Session Timed Out.Kindly Refresh",
            severity: "danger",
            cancelButton: true,
            okButton: true,
            okButtonText: "Refresh",
          });

        }
        if (oData) setIdentifierList(oData);
        setLoader(false);
      },
      function (error) {
        props?.setAlert(true);
        props?.setAlertSeverity("error");
        props?.setAlertMessage(error);
        setLoader(false);
      }
    );
  };

  const getProcess = () => {
    setEventLoader(true);
    if (userReducer.aDestinationUrl && userReducer.aDestinationUrl) {
      let payload = {
        // application: "ITM",
        application: userReducer.applicationName,
        entity: templateData.entity.entity,
        identifier: templateData.identifier.identifier,
      };
      doAjax(
        "/WorkUtilsServices/v2/application/process/Variable",
        "post",
        payload,
        function (oData) {
          if (oData.statusCode === 401 || oData.statusCode === "401") {
            promptAction_Functions.handleOpenPromptBox("TIMEOUT", {
              title: "Error",
              message: "Session Timed Out.Kindly Refresh",
              severity: "danger",
              cancelButton: true,
              okButton: true,
              okButtonText: "Refresh",
            });
          }
          if (oData.data) {
            setProcessData(oData.data);
            let _selectedVariable = oData.data?.filter((item) => item.processDesc === templateData.process.processDesc);
            setEventVariables(_selectedVariable?.[0]?.variables);
            setVariableData(oData.data.map(dataManipulation));
          }
          setEventLoader(false);
        },
        function () {
          setEventLoader(false);
          setEmailError("error");
          setSeverity("error");
          handleClickalert();
        }
      );
    }
  };

  useEffect(() => {
    if (userReducer.applicationName !== "") {
      getGroupdefinitiondata();
      // console.log(userReducer.applicationName);
    }
  }, [userReducer]);

  const getGroupdefinitiondata = () => {
    setLoader(true);

    doCrudApi(
      "fetchMailGroupingHana",
      [],
      function (oData) {
        if (oData.statusCode === 401 || oData.statusCode === "401") {
          promptAction_Functions.handleOpenPromptBox("TIMEOUT", {
            title: "Error",
            message: "Session Timed Out.Kindly Refresh",
            severity: "danger",
            cancelButton: true,
            okButton: true,
            okButtonText: "Refresh",
          });

        }
        if (oData) {
          let transFormData = oData.map((ele) => {
            return { id: ele.id, name: ele.groupName, userIdList: ele.email };
          });
          setGroupData(transFormData);
          let result = transFormData.concat(groupList);
          let transFormgroupData1 = result.map((ele) => {
            groupDetails.set(ele.id, ele.name);
            return ele.name;
          });
          setGroupDetails(new Map(groupDetails));
          // setter(transFormData);
          setToparticipant(transFormgroupData1);
          setCcToParticipant(transFormgroupData1);

        }
        setLoader(false);
      },
      function () {
        setEventLoader(false);
        setEmailError("error");
        setSeverity("error");
        handleClickalert();
      }
    );
  };
  const getUserList = () => {
    let transFormData = userList?.map((ele) => {
      userDetails?.set(ele.emailId, ele.userName);
      // return { code: ele.userName, description: ele.emailId };
      return ele.emailId;
    });
    setUserDetails(new Map(userDetails));
    setToparticipant(transFormData);
    setCcToParticipant(transFormData);
  };
  const handleClickalert = () => {
    setOpenalert(true);
  };

  const dataManipulation = (ele) => {
    let tempObj = {
      process: ele?.process,
      processDesc: ele?.processDesc,

      active: ele?.variables.filter((ele) => ele?.active === true),
      deactive: ele?.variables.filter((ele) => ele?.active === false),
    };
    return tempObj;
  };
  const getFileBase64 = (file, callback) => {
    var reader = new FileReader();
    reader.readAsDataURL(file);
    // Since FileReader is asynchronous,
    // we need to pass data back.
    reader.onload = () => callback(reader.result);
    // TODO: catch an error
    reader.onerror = () => {};
  };

  const imageUploadCallback = (file) => new Promise((resolve) => getFileBase64(file, (data) => resolve({ data: { link: data } })));

  const onContentStateChange = (editorState) => {
    let emailBodyData = draftToHtml(editorState);
    try {
      let box = document.createElement("div");

      box.style.display = "flex";
      box.style.flexDirection = "column";
      box.style.justifyContent = "start";

      box.innerHTML = emailBodyData;
      let elementlist = [...box.children];
      elementlist.forEach((element) => {
        if (element.localName === "p") {
          if (element.innerHTML) {
            // let div = document.createElement('div')
            element.style.display = "block !important";
            // div.innerHTML =
            element.style.margin = "0em";
            element.style.lineHeight = "1.2em";
            element.style.width = "100%";
          } else {
            let br = document.createElement("br");
            element.replaceWith(br);
          }
        }
      });
      emailBodyData = box.outerHTML;
    } catch (e) {
      console.log(e);
    }

    setTemplateData({ ...templateData, body: emailBodyData });
  };

  const setEditorValue = (content) => {

    const blocksFromHtml = htmlToDraft(content);
    const { contentBlocks, entityMap } = blocksFromHtml;
    const contentState = ContentState.createFromBlockArray(contentBlocks, entityMap);
    const editorState = EditorState.createWithContent(contentState);

    setDefaultEditorValue(editorState);
    setEditorState(editorState);
  };

  const nameValidator = (nameData) => {
    const regex = /^[A-Za-z][A-Za-z0-9_]*$/;
    return regex.test(nameData);
  };

  // console.log(variableData, "variableData");
  const participantData = ["VARIABLE", "GROUP", "USER"];

  React.useEffect(() => {
    if (userReducer.applicationName !== "") {
      getIdentifierList();

      if (props?.creationType !== "new" && props?.data) {
        let subject = props?.data.subject;
        if (subject !== "") {
          subject = subject.replaceAll("$", "$[");
          let arr = subject.split(" ");
          arr = arr.map((ele) => {
            if (ele[0] === "$") ele += "]";
            return ele;
          });
          subject = "";
          arr.forEach((ele) => (subject += ele + " "));
        }

        setTemplateData({
          name: props?.creationType === "copy" ? props?.data.name + "_Copy" : props?.data.name,
          entity: {
            entity: props?.data?.entity,
            entityDesc: props?.data?.entityDesc,
          },
          process: {
            process: props?.data.process,
            processDesc: props?.data.processDesc,
          },
          identifier: {
            identifier: props?.data.identifier,
            identifierDesc: props?.data.identifierDesc,
          },
          subject: subject,
          body: props?.data.content,
          application: props?.data.application,
          applicationDesc: props?.data.applicationDesc,
          emailDefinitionId: props?.creationType === "copy" ? "" : props?.data.emailDefinitionId,
          createdBy: props?.data.createdBy,
          createdOn: new Date(props?.data.createdOn).toISOString(),
          updatedBy: props?.data?.createdBy?.updatedBy,
          updatedOn: new Date(props?.data.updatedOn).toISOString(),
        });

        setParticipantState({
          receipentType: props?.data.toParticipantType,
          receipents: props?.data.toParticipantType === "GROUP" ? Array.from(groupDetails).find(([key]) => key == props?.data.toParticipant)?.[1] : props?.data.toParticipant,
          ccType: props?.data.ccParticipantType,
          cc: props?.data.ccParticipantType === "GROUP" ? Array.from(groupDetails).find(([key]) => key == props?.data.ccParticipant)?.[1] : props?.data.ccParticipant,
        });
        handleParticipantState("receipentType", props?.data.toParticipantType);
        handleParticipantState("ccType", props?.data.ccParticipantType);

        getEntityList(props?.data.application, props?.data.identifier);
        getProcessList(props?.data.application, props?.data.entity, props?.data.identifier);
        getProcessVariables(props?.data.application, props?.data.entity, props?.data.process, props?.data.identifier);

        setEditorValue(props?.data?.content);
      } else {
        resetDefault();
      }
    }

    // eslint-disable-next-line
  }, [props?.data, props?.creationType, userReducer]);

  const changeActiveState = (option, index) => {
    let tempState = structuredClone(eventVariables);
    tempState[index].active = !option.active;
    setEventVariables(tempState);
  };


  useEffect(() => {
    if (templateData?.identifier && templateData?.process && templateData?.entity) {
      getProcess(templateData?.entity);
    }
  }, [templateData?.identifier, templateData?.process, templateData?.entity, userReducer.applicationId]);

  useEffect(() => {
    if (userReducer.applicationId !== undefined) {
      getUserList();
    }
  }, [userReducer.applicationId]);
  //<-- Functions and variables for ReusablePromptBox *promptAction_Functions -->
  const [promptBoxState, setPromptBoxState] = useState({
    open: false,
    type: "",
    redirectOnClose: true,
    message: "",
    title: "",
    severity: "",
  });
  const [promptBoxScenario, setPromptBoxScenario] = useState("");

  const promptAction_Functions = {
    handleClosePromptBox: () => {
      setPromptBoxState((prev) => ({
        open: false,
        type: "",
        redirectOnClose: true,
        message: "",
        title: "",
        severity: "",
      }));
      setPromptBoxScenario("");
    },
    handleOpenPromptBox: (ref, data = {}) => {
      // SUCCESS,FAILURE,WARNING,QUANTITYERROR,
      let initialData = {
        open: true,
        title: "",
        message: "",
        okButton: true,
        cancelButton: true,
        okText: "Ok",
        cancelText: "Cancel",
        type: "dialog",
      };
      if (ref === "SUCCESS") {
        initialData.type = "snackbar";
      }
      setPromptBoxScenario(ref);
      setPromptBoxState({
        ...initialData,
        ...data,
      });
    },
    handleCloseAndRedirect: () => {
      promptAction_Functions.handleClosePromptBox();
      navigate("/purchaseManagement/purchaseOrder");
    },
    getCancelFunction: () => {
      switch (promptBoxScenario) {
        default:
          return () => {
            promptAction_Functions.handleClosePromptBox();
          };
      }
    },
    getCloseFunction: () => {
      switch (promptBoxScenario) {
        case "COMMENTERROR":
        default:
          return (value) => {
            promptAction_Functions.handleClosePromptBox();
          };
      }
    },
    getOkFunction: () => {
      switch (promptBoxScenario) {
        case "CONFIRM_DISCARD":
          return () => {
            handleClose();
          };
        case "CONFIRM_SUBMIT":
          return () => {
            closeConfirmationDialog("Active");
          };
        case "CONFIRM_SUBMIT_AS_DRAFT":
          return () => {
            closeConfirmationDialog("Draft");
          };

        case "TIMEOUT":
          return () => {
            closeConfirmationDialog("Timeout");
          };
        default:
          return () => promptAction_Functions.handleClosePromptBox();
      }
    },
    getCloseAndRedirectFunction: () => {
      if (!promptBoxState.redirectOnClose) {
        return promptAction_Functions.handleClosePromptBox;
      }
      return promptAction_Functions.handleCloseAndRedirect;
    },
  };

  let handleConfirm = (ref) => {
    switch (ref) {
      case "CONFIRM_DISCARD":
        promptAction_Functions.handleOpenPromptBox("CONFIRM_DISCARD", {
          title: "Confirm Discard",
          message: `Are you sure you want to proceed with discard? The entered data will be lost`,
          severity: "warning",
          cancelButton: true,
          okButton: true,
          okButtonText: "Discard",
        });
        break;
      case "CONFIRM_SUBMIT":
        if (!nameValidator(templateData.name)) {
          //check if template name is a valid one
          promptAction_Functions.handleOpenPromptBox("WARNING", {
            title: "Error",
            message: `Please Enter a valid template name`,
            severity: "danger",
            cancelButton: true,
            okButton: true,
            okButtonText: "Ok",
          });
          return;
        }

        if (!templateData.entity | !templateData.process | !templateData.name | !templateData.identifier) {
          //check if all the fields are filled
          promptAction_Functions.handleOpenPromptBox("WARNING", {
            title: "Error",
            message: `Please fill in all the mandatory fields`,
            severity: "danger",
            cancelButton: true,
            okButton: true,
            okButtonText: "Ok",
          });
          return;
        }
        promptAction_Functions.handleOpenPromptBox("CONFIRM_SUBMIT", {
          title: "Confirm Submit",
          message: `Are you sure you want to proceed with submission?`,
          severity: "success",
          cancelButton: true,
          okButton: true,
          okButtonText: "Submit",
        });
        break;
      case "CONFIRM_SUBMIT_AS_DRAFT":
        promptAction_Functions.handleOpenPromptBox("CONFIRM_SUBMIT_AS_DRAFT", {
          title: "Confirm Submit",
          message: `Are you sure you want to proceed with saving as draft?`,
          severity: "success",
          cancelButton: true,
          okButton: true,
          okButtonText: "Save as draft",
        });
        break;
    }
  };

  const handleClose = () => {
    onClose();
    if (props?.creationType !== "edit") {
      resetDefault();
    }
    setScenario("INITIAL");
    setProcessData([]);
    setCreationType("new");
  };
  const closePreview = () => {
    setOpenPreview(false);
  };
  useEffect(() => {
    if (templateData.subject?.length > 100) {
      promptAction_Functions.handleOpenPromptBox("ERROR", {
        title: "Error",
        message: "Subject exceeded max length of 100",
        severity: "danger",
        cancelButton: true,
        okButton: true,
        okButtonText: "Refresh",
      });
    }
  }, [templateData.subject]);
  return (
    <div>
      <Backdrop className="backdrop" sx={{ zIndex: "9999999" }} open={backdrop}>
        <CircularProgress color="primary" />
      </Backdrop>

      <ReusablePromptBox
        type={promptBoxState.type}
        promptState={promptBoxState.open}
        setPromptState={promptAction_Functions.handleClosePromptBox}
        onCloseAction={promptAction_Functions.getCloseFunction()}
        promptMessage={promptBoxState.message}
        dialogSeverity={promptBoxState.severity}
        dialogTitleText={promptBoxState.title}
        handleCancelButtonAction={promptAction_Functions.getCancelFunction()}
        cancelButtonText={promptBoxState.cancelText} //Cancel button display text
        showCancelButton={promptBoxState.cancelButton} //Enable Cancel button
        handleSnackBarPromptClose={promptAction_Functions.getCloseAndRedirectFunction()}
        handleOkButtonAction={promptAction_Functions.getOkFunction()}
        okButtonText={promptBoxState.okButtonText}
        showOkButton={promptBoxState.okButton}
      />
      <Grid container sx={{ marginBottom: "1.5rem" }}>
        <Grid
          item
          md={9}
          style={{
            display: "flex",
          }}
        >
          <Grid item sx={{ maxWidth: "max-content" }}>
            <IconButton
              onClick={handleClose}
              color="primary"
              component="label"
              className="iconButton-spacing-small"
              sx={{
                padding: "0.25rem",
                height: "max-content",
              }}
            >
              <ArrowCircleLeftOutlinedIcon
                sx={{
                  fontSize: "25px",
                  color: "#000000",
                }}
              />
            </IconButton>
          </Grid>
          <Grid item xs>
            <Typography variant="h3">
              <strong> {props?.creationType === "new" || props?.creationType === "copy" ? "Create" : "Edit"} Template</strong>
            </Typography>
            <Typography variant="body2" color="#777">
              This view allows users to {scenario === "CREATE" ? "create" : "edit"} Email Templates
            </Typography>
          </Grid>
        </Grid>
      </Grid>

      {/* </DialogTitle> */}
      {/* <DialogContent> */}
      <Grid container alignItems="center" justifyContent="center">
        <Box sx={{ marginBottom: "64px", width: "100%" }} className="cwWorkUtilsScroll">
          <Card
            sx={{
              padding: "1.5rem",
              marginBottom: "1.5rem",
              borderRadius: "8px",
              boxShadow: "0 2px 8px rgba(0,0,0,0.08)",
              background: "linear-gradient(to bottom, #ffffff, #fafbff)",
            }}
          >
            <Typography
              variant="h5"
              sx={{
                marginBottom: "1.5rem",
                fontWeight: 600,
                color: "#1a3e72",
                display: "flex",
                alignItems: "center",
                gap: "0.5rem",
              }}
            >
              <Email fontSize="small" /> Email Template
            </Typography>

            <Grid container spacing={3} columns={24}>
              <Grid item xs={6}>
                <Typography
                  variant="body1"
                  sx={{
                    marginBottom: "0.5rem",
                    fontWeight: 500,
                  }}
                >
                  {props?.headers[3] ? props?.headers[3] : "Template Name"}
                </Typography>
                
                <TextField
                  fullWidth
                  size="small"
                  placeholder="Enter Template Name"
                  className="CustomTextField"
                  id="outlined-basic"
                  variant="outlined"
                  value={templateData.name}
                  onChange={(e) =>
                    setTemplateData({
                      ...templateData,
                      name: e.target.value,
                    })
                  }
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      borderRadius: "6px",
                      "&:hover fieldset": {
                        borderColor: "#1976d2",
                      },
                    },
                  }}
                />
              </Grid>

              <Grid item xs={6}>
                <Typography
                  variant="body1"
                  sx={{
                    marginBottom: "0.5rem",
                    fontWeight: 500,
                  }}
                >
                  {props?.headers[0] ? props?.headers[0] : "Identifier"}
                </Typography>
                <Autocomplete
                  disabled={props?.creationType === "edit"}
                  variant="outlined"
                  disablePortal
                  className="CustomAutoComplete"
                  size="small"
                  id="combo-box-demo"
                  placeholder="Enter Identifier Name"
                  options={identifierList}
                  value={templateData.identifier}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      fullWidth
                      placeholder="Enter Identifier Name"
                      sx={{
                        "& .MuiOutlinedInput-root": {
                          borderRadius: "6px",
                          "&:hover fieldset": {
                            borderColor: "#1976d2",
                          },
                        },
                      }}
                    />
                  )}
                  getOptionLabel={(option) => (option.identifierDesc ? option.identifierDesc : "")}
                  onChange={(event, value) => {
                    setTemplateData({
                      ...templateData,
                      identifier: value,
                      entity: null,
                      process: null,
                    });
                    if (value) {
                      getEntityList(userReducer.applicationName, value.identifier);
                    }
                  }}
                  isOptionEqualToValue={(option, value) => option.identifier === value.identifier}
                />
              </Grid>

              <Grid item xs={6}>
                <Typography
                  variant="body1"
                  sx={{
                    marginBottom: "0.5rem",
                    fontWeight: 500,
                  }}
                >
                  {props?.headers[1] ? props?.headers[1] : "Module"}
                </Typography>
                <Autocomplete
                  disabled={props?.creationType === "edit"}
                  disablePortal
                  id="combo-box-demo"
                  size="small"
                  options={entityList}
                  value={templateData.entity}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      fullWidth
                      placeholder="Enter Module Name"
                      sx={{
                        "& .MuiOutlinedInput-root": {
                          borderRadius: "6px",
                          "&:hover fieldset": {
                            borderColor: "#1976d2",
                          },
                        },
                      }}
                    />
                  )}
                  getOptionLabel={(option) => (option.entityDesc ? option.entityDesc : "")}
                  onChange={(event, value) => {
                    setTemplateData({
                      ...templateData,
                      entity: value,
                      process: null,
                    });
                    if (value) {
                      getProcessList(userReducer.applicationName, value.entity, templateData?.identifier?.identifier);
                    }
                  }}
                  isOptionEqualToValue={(option, value) => option.entity === value.entity}
                />
              </Grid>

              <Grid item xs={6}>
                <Typography
                  variant="body1"
                  sx={{
                    marginBottom: "0.5rem",
                    fontWeight: 500,
                  }}
                >
                  {props?.headers[2] ? props?.headers[2] : "Event"}
                </Typography>
                <Autocomplete
                  disabled={props?.creationType === "edit"}
                  variant="outlined"
                  disablePortal
                  size="small"
                  id="combo-box-demo"
                  options={processList}
                  value={templateData.process}
                  renderInput={(params) => (
                    <TextField
                      placeholder="Enter Event Name"
                      fullWidth
                      {...params}
                      sx={{
                        "& .MuiOutlinedInput-root": {
                          borderRadius: "6px",
                          "&:hover fieldset": {
                            borderColor: "#1976d2",
                          },
                        },
                      }}
                    />
                  )}
                  getOptionLabel={(option) => (option.processDesc ? option.processDesc : "")}
                  onChange={(event, value) => {
                    setTemplateData({ ...templateData, process: value });
                    if (value) {
                      getProcessVariables(userReducer.applicationName, templateData.entity.entity, value.process, templateData?.identifier?.identifier);
                    }
                  }}
                  isOptionEqualToValue={(option, value) => option.processDesc === value.processDesc}
                />
              </Grid>
            </Grid>

            <Box
              sx={{
                marginTop: "2rem",
                padding: "1.5rem",
                backgroundColor: "#f8f9fa",
                borderRadius: "6px",
              }}
            >
              <Typography
                variant="h5"
                sx={{
                  marginBottom: "1rem",
                  fontWeight: 600,
                  color: "#1a3e72",
                }}
              >
                Event Variables
              </Typography>

              {Boolean(eventVariables?.length) ? (
                <Stack
                  direction="row"
                  rowGap={2}
                  columnGap={1.5}
                  alignItems="start"
                  justifyContent="start"
                  sx={{
                    minHeight: "20vh",
                    display: "flex",
                    width: "100%",
                    flexWrap: "wrap",
                    padding: "0.5rem",
                  }}
                >
                  {eventVariables?.map((option, index) => (
                    <Button
                      key={index}
                      variant={option.active ? "contained" : "outlined"}
                      color={option.active ? "primary" : "secondary"}
                      sx={{
                        marginBottom: "0.75rem",
                        borderRadius: "20px",
                        padding: "6px 16px",
                        textTransform: "none",
                        fontWeight: 500,
                        boxShadow: option.active ? "0 2px 5px rgba(0,0,0,0.1)" : "none",
                      }}
                      onClick={() => changeActiveState(option, index)}
                    >
                      {option.variable}
                    </Button>
                  ))}
                </Stack>
              ) : (
                <Box
                  sx={{
                    minHeight: "20vh",
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    backgroundColor: "#fff",
                    borderRadius: "6px",
                    border: "1px dashed #d32f2f",
                    padding: "1rem",
                  }}
                >
                  <Typography
                    sx={{
                      color: "#d32f2f",
                      fontWeight: 500,
                      fontSize: "0.95rem",
                    }}
                  >
                    No variables available. Please select an event first.
                  </Typography>
                </Box>
              )}
            </Box>
          </Card>
          {props?.useChannels && (
            <Box sx={{ padding: "1rem 1rem ", marginBottom: "1rem" }}>
              <Stack container direction="column">
                <Typography
                  sx={{
                    marginBottom: "1rem",
                  }}
                  variant="h5"
                >
                  Channels
                </Typography>
                <Grid spacing={2} direction="row">
                  <Stack justifyContent={"start"} alignItems={"center"} direction={"row"} spacing={2}>
                    {Object.values(channelDataTemplate).map((item) => (
                      <Button onClick={() => setChannelConfigKey(item.key)} variant="contained" color="action" startIcon={item.key === channelConfigKey ? <CheckOutlined /> : item.icon}>
                        {item.label}
                      </Button>
                    ))}
                  </Stack>
                  <ChannelConfiguration channel={channelConfigKey} />
                </Grid>
              </Stack>
            </Box>
          )}
          <Card
            sx={{
              padding: "1.5rem",
              marginBottom: "1.5rem",
              borderRadius: "8px",
              boxShadow: "0 2px 8px rgba(0,0,0,0.08)",
              background: "linear-gradient(to bottom, #ffffff, #fafbff)",
            }}
          >
            <Typography
              variant="h5"
              sx={{
                marginBottom: "1.5rem",
                fontWeight: 600,
                color: "#1a3e72",
                display: "flex",
                alignItems: "center",
                gap: "0.5rem",
              }}
            >
              <Email fontSize="small" /> Email Template
            </Typography>

            <Grid container direction="column" spacing={3}>
              <Grid item>
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "flex-start",
                    gap: "1rem",
                    width: "100%",
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: 500,
                      width: "80px",
                      paddingTop: "8px",
                      color: "#555",
                    }}
                  >
                    Subject
                  </Typography>

                  <TextField
                    fullWidth
                    size="small"
                    placeholder="Enter email subject line"
                    inputProps={{
                      maxLength: 100,
                    }}
                    value={templateData.subject}
                    onChange={(e) =>
                      setTemplateData({
                        ...templateData,
                        subject: e.target.value,
                      })
                    }
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        borderRadius: "6px",
                        backgroundColor: "#fff",
                        transition: "all 0.2s",
                        "&:hover fieldset": {
                          borderColor: "#1976d2",
                        },
                        "&.Mui-focused fieldset": {
                          borderColor: "#3b30c8",
                          borderWidth: "1px",
                        },
                      },
                    }}
                  />
                </Box>

                <Box
                  sx={{
                    mt: 0.5,
                    textAlign: "right",
                    color: templateData.subject?.length > 80 ? "#d32f2f" : "#666",
                    fontSize: "0.75rem",
                  }}
                >
                  {templateData.subject?.length || 0}/100
                </Box>
              </Grid>

              <Grid item>
                <Typography
                  sx={{
                    fontWeight: 500,
                    mb: 1.5,
                    color: "#555",
                  }}
                >
                  Email Body
                </Typography>

                <Box
                  sx={{
                    border: "1px solid #e0e0e0",
                    borderRadius: "8px",
                    overflow: "hidden",
                    "& .rdw-editor-toolbar": {
                      border: "none",
                      borderBottom: "1px solid #e0e0e0",
                      padding: "8px 16px",
                      background: "#f5f7fa",
                    },
                    "& .rdw-option-wrapper": {
                      border: "none",
                      background: "transparent",
                      height: "28px",
                      width: "28px",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      margin: "0 2px",
                      borderRadius: "4px",
                      "&:hover": {
                        background: "#e3e8f0",
                      },
                      "&.rdw-option-active": {
                        background: "#d8e0f0",
                      },
                    },
                    "& .rdw-dropdown-wrapper": {
                      border: "1px solid #e0e0e0",
                      borderRadius: "4px",
                      margin: "0 4px",
                    },
                    "& .Editor": {
                      padding: "16px",
                      minHeight: "250px",
                      fontSize: "14px",
                      lineHeight: "1.6",
                      color: "#333",
                    },
                    "& .public-DraftStyleDefault-block": {
                      margin: "0.5em 0",
                    },
                    "& .rdw-link-modal, & .rdw-image-modal": {
                      boxShadow: "0 3px 10px rgba(0,0,0,0.15)",
                      borderRadius: "8px",
                    },
                    "& .rdw-suggestion-dropdown": {
                      borderRadius: "8px",
                      boxShadow: "0 3px 10px rgba(0,0,0,0.15)",
                      padding: "8px 0",
                    },
                    "& .rdw-suggestion-option": {
                      padding: "8px 16px",
                      "&:hover": {
                        background: "#f0f4fa",
                      },
                    },
                  }}
                >
                  <Editor
                    editorState={editorState}
                    wrapperClassName="Editor"
                    editorClassName="Editor"
                    defaultEditorState={defaultEditorValue}
                    onEditorStateChange={setEditorState}
                    onContentStateChange={onContentStateChange}
                    toolbar={{
                      inline: { inDropdown: false },
                      list: { inDropdown: false },
                      textAlign: { inDropdown: false },
                      link: { inDropdown: false },
                      history: { inDropdown: false },
                      image: {
                        uploadCallback: imageUploadCallback,
                        previewImage: true,
                        alignmentEnabled: true,
                      },
                    }}
                    mention={{
                      separator: " ",
                      trigger: "$",
                      suggestions: eventVariables?.map((option) => ({
                        text: option.variable ?? "",
                        value: option.variable ?? "",
                        url: option.variable ?? "",
                      })),
                    }}
                  />
                </Box>

                <Box sx={{ mt: 2, display: "flex", alignItems: "center", gap: 1 }}>
                  <Box
                    sx={{
                      display: "inline-flex",
                      alignItems: "center",
                      gap: 0.5,
                      backgroundColor: "#f0f4fa",
                      padding: "4px 10px",
                      borderRadius: "4px",
                      fontSize: "0.75rem",
                      color: "#3b30c8",
                      fontWeight: 500,
                    }}
                  >
                    <Code fontSize="small" sx={{ fontSize: "14px" }} />
                    Use $variable to insert dynamic content
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Card>
          {props?.isRecepientData && (
            <Grid item xs={12} p={4} spacing={2}>
              <Grid container>
                <Typography sx={{ fontWeight: 500, color: "black ", fontSize: "18px" }}>Select The Participants</Typography>
                <Grid container spacing={2} p={2}>
                  <Grid item xs={12}>
                    <Typography>Recipent Type</Typography>
                    <Autocomplete
                      value={participantState?.receipentType}
                      onSelect={(params) => {
                        handleParticipantState("receipentType", params.target.value);
                      }}
                      disablePortal
                      id="combo-box-demo"
                      options={participantData}
                      size="small"
                      renderInput={(params) => <TextField fullWidth {...params} label="Select Recipient Type" />}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Typography>Receipents</Typography>
                    <Autocomplete value={participantState?.receipents} onSelect={(params) => handleParticipantState("receipents", params.target.value)} fullWidth disablePortal id="combo-box-demo" options={toparticipant} size="small" renderInput={(params) => <TextField fullWidth {...params} label="Select Recipients" />} />
                  </Grid>
                  <Grid item xs={12}>
                    <Typography>CC Type</Typography>
                    <Autocomplete value={participantState?.ccType} onSelect={(params) => handleParticipantState("ccType", params.target.value)} fullWidth disablePortal id="combo-box-demo" options={participantData} size="small" renderInput={(params) => <TextField fullWidth {...params} label="Add CC recipient type" />} />
                  </Grid>
                  <Grid item xs={12}>
                    <Typography>CC</Typography>
                    <Autocomplete value={participantState?.cc} onSelect={(params) => handleParticipantState("cc", params.target.value)} fullWidth disablePortal id="combo-box-demo" options={ccToParticipant} size="small" renderInput={(params) => <TextField fullWidth {...params} label="Add CC recipient " />} />
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          )}
        </Box>
        <Paper sx={{ position: "fixed", bottom: 0, left: 0, right: 0, zIndex: "4" }} elevation={2}>
          <BottomNavigation className="container_BottomNav">
            <Stack direction="row" sx={{ marginLeft: "auto" }} spacing={2} alignItems="center">
              {userReducer.feature.EMAIL_CONFIG_DISCARD === "True" && (
                <Button
                  variant="text"
                  sx={{
                    minWidth: "max-content",
                    padding: "6px 12px",
                    texttransform: "capitalize",
                    height: "2rem",
                  }}
                  onClick={() => {
                    handleConfirm("CONFIRM_DISCARD");
                  }}
                >
                  Discard
                </Button>
              )}
              {/* <Button variant="outlined" onClick={handlePreviewClick}>
                Preview
              </Button> */}
              {userReducer.feature.EMAIL_CONFIG_SAVE === "True" && (
                <Button
                  variant="outlined"
                  sx={{
                    minWidth: "max-content",
                    padding: "6px 12px",
                    texttransform: "capitalize",
                    height: "2rem",
                  }}
                  onClick={() => {
                    handleConfirm("CONFIRM_SUBMIT_AS_DRAFT");
                  }}
                >
                  Save As Draft
                </Button>
              )}
              {userReducer.feature.EMAIL_CONFIG_SUBMIT === "True" && (
                <Button
                  variant="contained"
                  sx={{
                    minWidth: "max-content",
                    padding: "6px 12px",
                    texttransform: "capitalize",
                    height: "2rem",
                  }}
                  onClick={() => {
                    handleConfirm("CONFIRM_SUBMIT");
                  }}
                >
                  Submit
                </Button>
              )}
            </Stack>
          </BottomNavigation>
        </Paper>
      </Grid>
      {/* </DialogContent> */}
      <Confirmation message={confirmationMessage} creationType={buttonAction} open={showConfirmation} onClose={(evt) => closeConfirmationDialog(evt)} />
      <CompletePreview openPreview={openPreview} closePreview={closePreview} />
      {/* </Dialog> */}
    </div>
  );
};

export default CreateTemplate;
