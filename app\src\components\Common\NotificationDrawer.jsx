import React, { useState, useEffect } from "react";
import { 
  <PERSON>, Badge, List, ListI<PERSON>, <PERSON><PERSON><PERSON>, 
  Divider, IconButton, Popover, Switch,
  FormControl, Select, MenuItem, Tooltip,
  Paper, Avatar, Stack, Chip, FormGroup, FormControlLabel
} from "@mui/material";
import NotificationsIcon from "@mui/icons-material/Notifications";
import DoneAllIcon from '@mui/icons-material/DoneAll';
import DeleteSweepIcon from '@mui/icons-material/DeleteSweep';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import { useDispatch, useSelector } from "react-redux";
import { clearNotificationData, updateNotificationData, setNotificationsDirect, setNotificationPreference } from "../../app/notificationSlice";
import { colors } from "@constant/colors";
import noDataFound from "../../utilityImages/nonotificationfound.png";
import { doAjax } from "./fetchService";
import { destination_Websocket } from "../../destinationVariables";
import moment from "moment";
import useLang from "@hooks/useLang";

const NotificationPopup = () => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [snoozeDuration, setSnoozeDuration] = useState("");
  const [snoozeEndTime, setSnoozeEndTime] = useState(null);
  const dispatch = useDispatch();
  const notifications = useSelector((state) => state.notifications.notifications);
  const notificationPreference = useSelector((state) => state.notifications.notificationPreference);
  const userData = useSelector((state) => state.userManagement.userData);
  const filteredNotifications = snoozeEndTime && new Date() < snoozeEndTime 
    ? [] 
    : notifications;
  const unreadCount = filteredNotifications?.filter((notif) => !notif.isRead)?.length;
  const { t } = useLang();

  useEffect(() => {
    if (snoozeEndTime && new Date() > snoozeEndTime) {
      setSnoozeEndTime(null);
      setSnoozeDuration("");
    }
  }, [snoozeEndTime, notifications]);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
    if (unreadCount > 0) {
      setTimeout(() => {
      }, 3000);
    }
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);
  const id = open ? 'notification-popover' : undefined;

  const markAsRead = (indexId) => {
    dispatch(updateNotificationData({ indexId, updatedData: { isRead: true } }));
    doAjax(`/${destination_Websocket}/notifications/read/${indexId}`,
      "post",
      () => {},
      () => {},
    );
  };

  const markAllAsRead = () => {
    doAjax(`/${destination_Websocket}/notifications/readAll/${userData?.emailId}`,
      "post",
      () => {},
      () => {},
    );
    dispatch(updateNotificationData("markAllAsRead"));
  };

  const clearAllNotifications = () => {
    doAjax(`/${destination_Websocket}/notifications/readAll/${userData?.emailId}`,
      "post",
      () => {},
      () => {},
    );
    dispatch(clearNotificationData([]));
  };

  const handleSnoozeChange = (event) => {
    const duration = event.target.value;
    setSnoozeDuration(duration);
    
    if (duration) {
      const durationMap = {
        "1min": 1,
        "5min": 5,
        "10min": 10,
        "30min": 30,
        "1hr": 60,
      };
      const minutes = durationMap[duration] || 0;
      const endTime = new Date();
      endTime.setMinutes(endTime.getMinutes() + minutes);
      setSnoozeEndTime(endTime);
      doAjax(`/${destination_Websocket}/notification-preferences/${userData?.emailId}/snooze?durationMinutes=${durationMap[duration]}`,
        "post",
        () => {},
        () => {},
      );
    } else {
      setSnoozeEndTime(null);
      doAjax(`/${destination_Websocket}/notification-preferences/${userData?.emailId}/snooze?durationMinutes=${0}`,
        "post",
        () => {},
        () => {},
      );
    }
  };

  const handleToggleNotifications = (event) => {
    // setNotificationsEnabled(event.target.checked);
    dispatch(setNotificationPreference(event.target.checked))
    const payload = {
      "emailNotification": null,
      "smsNotification": null,
      "inAppNotification": event.target.checked,
      "pushNotification": null,
      "snoozed": null,
      "snoozeUntil": null
    }
    doAjax(`/${destination_Websocket}/notification-preferences/${userData?.emailId}`,
      "post",
      () => {},
      () => {},
      payload
    );
    if(event.target.checked) {
      doAjax(`/${destination_Websocket}/notifications/fetch/unread/${userData?.emailId}`,
        "get",
        (data) => {
          if(data?.length) {
            dispatch(setNotificationsDirect([...data].reverse()));
          }
        },
        () => {}
      )
    }
  };

  const formatTimeAgo = (createdAt) => {
    if (!createdAt) return "";
    
    // Convert GMT to local time
    const localTime = moment(createdAt).local();
    const now = moment();
    return localTime.format("MMM D, YYYY [at] h:mm A");
  };

  // Get notification icon based on type
  const getNotificationIcon = (notif) => {
    const { requestId, isRead } = notif || {};
    let prefix = requestId?.substring(0, 3);
  
    const avatarColors = {
      NMA: "#1976d2",
      CMA: "#1565c0",
      EMA: "#0d47a1",
      NME: "#42a5f5",
      CME: "#64b5f6",
      EME: "#90caf9",
    };
  
    if (!avatarColors[prefix]) {
      prefix = 'N';
    }
  
    const alternateColors = ["#ff9800", "#f44336", "#4caf50", "#9c27b0", "#ffeb3b"];
    const avatarBgColor = isRead ? "#B0B0B0" : avatarColors[prefix] || alternateColors[Math.floor(Math.random() * alternateColors.length)];
  
    return (
      <Avatar sx={{ width: 35, height: 35, bgcolor: avatarBgColor, fontSize: "0.8rem" }}>
        {prefix}
      </Avatar>
    );
  };
  
  
  

  return (
    <Box>
      <Tooltip title={notificationPreference ? t("Notifications") : t("Notifications disabled")}>
        <IconButton 
          aria-describedby={id} 
          onClick={handleClick}
          sx={{ 
            position: 'relative',
            transition: 'transform 0.2s',
            '&:hover': { transform: 'scale(1.1)' },
          }}
        >
          <Badge 
            badgeContent={notificationPreference ? unreadCount : null} 
            color="error"
          >
            <NotificationsIcon 
              fontSize="medium" 
              sx={{ 
                color: notificationPreference ? colors?.secondary?.yellow : colors?.secondary?.grey,
                transition: 'color 0.3s',
              }} 
            />
          </Badge>
        </IconButton>
      </Tooltip>

      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        sx={{
          '& .MuiPopover-paper': {
            width: 380,
            maxHeight: 500,
            borderRadius: '12px',
            boxShadow: '0 8px 30px rgba(0, 0, 0, 0.12)',
            overflow: 'hidden',
            animation: 'fadeIn 0.3s ease-out',
            '@keyframes fadeIn': {
              '0%': { opacity: 0, transform: 'translateY(-10px)' },
              '100%': { opacity: 1, transform: 'translateY(0)' },
            },
          }
        }}
      >
        <Paper elevation={0} sx={{ overflow: 'hidden' }}>
          {/* Header Section */}
          <Box 
            sx={{ 
              p: 2, 
              background: 'linear-gradient(45deg,rgb(114, 177, 230) 30%,rgb(19, 203, 245) 90%)',
              color: 'white'
            }}
          >
            <Stack direction="row" justifyContent="space-between" alignItems="center">
              <Typography variant="h6" sx={{ fontWeight: "600" }}>
                {t("Notifications")}
              </Typography>
              <Stack direction="row" spacing={0.5}>
                <Tooltip title={t("Mark all as read")} arrow>
                  <IconButton 
                    onClick={markAllAsRead} 
                    disabled={unreadCount === 0}
                    sx={{ color: 'white', opacity: unreadCount === 0 ? 0.5 : 1 }}
                    size="small"
                  >
                    <DoneAllIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
                <Tooltip title={t("Clear all")} arrow>
                  <IconButton 
                    onClick={clearAllNotifications} 
                    disabled={filteredNotifications.length === 0}
                    sx={{ color: 'white', opacity: filteredNotifications.length === 0 ? 0.5 : 1 }}
                    size="small"
                  >
                    <DeleteSweepIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Stack>
            </Stack>
          </Box>

          {/* Settings Section */}
          <Box 
            sx={{ 
              px: 2, 
              py: 1.5, 
              bgcolor: 'rgba(0,0,0,0.02)',
              borderBottom: '1px solid rgba(0,0,0,0.08)',
            }}
          >
            <Stack direction="row" justifyContent="space-between" alignItems="center" spacing={2}>
              <FormGroup>
                <FormControlLabel 
                  control={
                    <Switch 
                      checked={notificationPreference} 
                      onChange={handleToggleNotifications}
                      size="small"
                      color="primary"
                    />
                  } 
                  label={
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {notificationPreference ? t("ON") : t("OFF")}
                    </Typography>
                  }
                  sx={{ m: 0 }}
                />
              </FormGroup>

              <Box>
                <FormControl 
                  size="small" 
                  variant="outlined" 
                  sx={{ 
                    minWidth: 140,
                    '& .MuiOutlinedInput-root': {
                      borderRadius: '8px',
                      transition: 'all 0.2s',
                      backgroundColor: 'rgba(255,255,255,0.9)',
                      boxShadow: '0 2px 5px rgba(0,0,0,0.05)',
                      '&:hover': {
                        boxShadow: '0 4px 10px rgba(0,0,0,0.1)',
                      },
                      '&.Mui-focused': {
                        boxShadow: '0 4px 12px rgba(25, 118, 210, 0.15)',
                      }
                    }
                  }}
                >
                  <Select
                    value={snoozeDuration}
                    onChange={handleSnoozeChange}
                    displayEmpty
                    inputProps={{ 'aria-label': 'Snooze time' }}
                    size="small"
                    sx={{ 
                      fontSize: '0.875rem',
                      fontWeight: 500,
                      '& .MuiSelect-select': { 
                        py: 1,
                        display: 'flex',
                        alignItems: 'center',
                        gap: 0.5
                      }
                    }}
                    startAdornment={
                      !snoozeDuration && (
                        <AccessTimeIcon 
                        fontSize="small" 
                        sx={{ 
                          color: colors.primary.blue, 
                          mr: 0.5, 
                          opacity: 0.8,
                          fontSize: '1rem'
                        }} 
                      />
                      )
                    }
                    MenuProps={{
                      PaperProps: {
                        sx: {
                          mt: 0.5,
                          boxShadow: '0 8px 20px rgba(0,0,0,0.15)',
                          borderRadius: '10px',
                          '& .MuiMenuItem-root': {
                            px: 2,
                            py: 1,
                            transition: 'all 0.15s',
                            borderRadius: '4px',
                            mx: 0.5,
                            my: 0.25,
                            '&:hover': {
                              backgroundColor: 'rgba(25, 118, 210, 0.08)',
                            },
                            '&.Mui-selected': {
                              backgroundColor: 'rgba(25, 118, 210, 0.12)',
                              '&:hover': {
                                backgroundColor: 'rgba(25, 118, 210, 0.18)',
                              },
                            }
                          },
                        }
                      },
                      anchorOrigin: {
                        vertical: 'bottom',
                        horizontal: 'right',
                      },
                      transformOrigin: {
                        vertical: 'top',
                        horizontal: 'right',
                      },
                    }}
                  >
                    <MenuItem value="">
                      <em>{t("Snooze")}..</em>
                    </MenuItem>
                    
                    <Divider sx={{ my: 0.5, opacity: 0.6 }} />
                    
                    <MenuItem value="1min" sx={{ color: 'text.primary' }}>
                      <AccessTimeIcon fontSize="small" sx={{ mr: 1, fontSize: '0.9rem', color: 'text.secondary' }} />
                      1 {t("minute")}
                    </MenuItem>
                    
                    <MenuItem value="5min" sx={{ color: 'text.primary' }}>
                      <AccessTimeIcon fontSize="small" sx={{ mr: 1, fontSize: '0.9rem', color: 'text.secondary' }} />
                      5 {t("minutes")}
                    </MenuItem>
                    
                    <MenuItem value="10min" sx={{ color: 'text.primary' }}>
                      <AccessTimeIcon fontSize="small" sx={{ mr: 1, fontSize: '0.9rem', color: 'text.secondary' }} />
                      10 {t("minutes")}
                    </MenuItem>
                    
                    <MenuItem value="30min" sx={{ color: 'text.primary' }}>
                      <AccessTimeIcon fontSize="small" sx={{ mr: 1, fontSize: '0.9rem', color: 'text.secondary' }} />
                      30 {t("minutes")}
                    </MenuItem>
                    
                    <MenuItem value="1hr" sx={{ color: 'text.primary' }}>
                      <AccessTimeIcon fontSize="small" sx={{ mr: 1, fontSize: '0.9rem', color: 'text.secondary' }} />
                      1 {t("hour")}
                    </MenuItem>
                  </Select>
                </FormControl>
              </Box>
            </Stack>
            
            {(snoozeEndTime && new Date() < snoozeEndTime) && (
              <Box mt={1}>
                <Chip 
                  size="small"
                  icon={<AccessTimeIcon fontSize="small" />}
                  label={`Snoozed until ${moment(snoozeEndTime).format('h:mm A')}`}
                  variant="outlined"
                  color="primary"
                  onDelete={() => setSnoozeEndTime(null)}
                  sx={{ fontSize: '0.75rem' }}
                />
              </Box>
            )}
          </Box>

          {/* Notifications List Section */}
          <Box sx={{ maxHeight: 350, overflowY: 'auto' }}>
            {(snoozeEndTime && new Date() < snoozeEndTime) ? (
              <Box display="flex" flexDirection="column" alignItems="center" py={4}>
                <AccessTimeIcon sx={{ fontSize: 60, color: colors?.secondary?.grey, opacity: 0.6, mb: 2 }} />
                <Typography variant="body2" align="center" sx={{ color: colors?.secondary?.grey }}>
                  {t("Notifications are snoozed until")} {moment(snoozeEndTime).format('h:mm A')}
                </Typography>
              </Box>
            ) : filteredNotifications?.length > 0 ? (
              <List disablePadding>
                {filteredNotifications.map((notif, index) => (
                  <ListItem
                    key={index}
                    onClick={() => markAsRead(notif?.id || index)}
                    sx={{
                      py: 1.5,
                      px: 2,
                      borderBottom: '1px solid rgba(0,0,0,0.06)',
                      cursor: "pointer",
                      transition: 'all 0.2s',
                      bgcolor: notif?.isRead ? 'transparent' : 'rgba(25, 118, 210, 0.08)',
                      '&:hover': {
                        bgcolor: 'rgba(0,0,0,0.04)',
                      },
                    }}
                    alignItems="flex-start"
                  >
                    <Stack direction="row" spacing={1.5} width="100%">
                      {/* Notification Icon */}
                      <Box mt={0.5}>
                        {getNotificationIcon(notif)}
                      </Box>
                      
                      <Box width="100%">
                        {/* Request ID */}
                        <Typography 
                          variant="subtitle2" 
                          sx={{ 
                            fontWeight: notif?.isRead ? 500 : 700,
                            mb: 0.5,
                            color: notif?.isRead ? 'text.primary' : colors.primary.blue
                          }}
                        >
                          {notif?.requestId || `REQ-${Math.floor(Math.random() * 10000)}`}
                        </Typography>
                        
                        {/* Message */}
                        <Typography
                          variant="body2"
                          sx={{ 
                            color: notif?.isRead ? 'text.secondary' : 'text.primary',
                            mb: 0.5,
                            lineHeight: 1.4
                          }}
                        >
                          {notif?.message}
                        </Typography>
                        
                        {/* Timestamp */}
                        <Typography 
                          variant="caption" 
                          sx={{ 
                            color: 'text.disabled',
                            display: 'block',
                            textAlign: 'right'
                          }}
                        >
                          {formatTimeAgo(notif?.createdAt || new Date(Date.now() - Math.floor(Math.random() * 1000000000)))}
                        </Typography>
                      </Box>
                    </Stack>
                  </ListItem>
                ))}
              </List>
            ) : (
              <Box display="flex" flexDirection="column" alignItems="center" py={4} px={3}>
                <img
                  src={noDataFound}
                  alt="No Notifications"
                  style={{ width: "180px", height: "auto", marginBottom: "16px", opacity: 0.7 }}
                />
                <Typography variant="body2" align="center" sx={{ color: colors?.secondary?.grey }}>
                  {t("No notifications available")}
                </Typography>
              </Box>
            )}
          </Box>
          
          {/* Footer */}
          {filteredNotifications?.length > 0 && (
            <Box 
              sx={{ 
                p: 1.5, 
                borderTop: '1px solid rgba(0,0,0,0.08)',
                textAlign: 'center'
              }}
            >
              <Typography 
                variant="body2" 
                sx={{ 
                  color: colors.primary.blue,
                  fontWeight: 500,
                  cursor: 'pointer',
                  '&:hover': { textDecoration: 'underline' }
                }}
              >
                {t("View all notifications")}
              </Typography>
            </Box>
          )}
        </Paper>
      </Popover>
    </Box>
  );
};

export default NotificationPopup;