import React, { useState } from 'react';
import StatsGrid from './StatsGrid';
import WorkflowGroupCard from './WorkflowGroupCard';
import TaskModal from './TaskModal';
import './workflow.css';
import { Typography } from '@mui/material';
import  useLang  from '../../../hooks/useLang';

const WorkflowDashboard = ({ data }) => {
  const [modalData, setModalData] = useState(null);
  const { t } = useLang();

  const handleShowModal = (taskInfo) => {
    setModalData(taskInfo);
  };

  const handleCloseModal = () => {
    setModalData(null);
  };

  return (
    <div>
      <Typography 
        align="left" 
        variant="h4" 
        component="h2" 
        gutterBottom
      >
        {t("Workflow Details")}
      </Typography>
      <StatsGrid data={data} />
      <div className="workflow-container">
        {Object?.entries(data)?.map(([groupName, groupData]) => {
          return (
            <WorkflowGroupCard
            key={groupName}
            groupName={groupName}
            groupData={groupData?.workflowDetails}
            materialTypes={groupData?.materialTypes}
            onTaskClick={handleShowModal}
          />
          )
        }
        )}
      </div>
      {modalData && <TaskModal task={modalData} onClose={handleCloseModal} />}
    </div>
  );
};

export default WorkflowDashboard;
