import { Box, Grid, Typo<PERSON>, Button } from '@mui/material';
import DownloadIcon from '@mui/icons-material/Download';
// import usePayloadCreation from "@hooks/usePayloadCreation";
import { useSelector } from 'react-redux';
import useLogger from '@hooks/useLogger';
import { doAjax } from "../../../components/Common/fetchService";
import { destination_MaterialMgmt } from "../../../../src/destinationVariables";
import { END_POINTS } from '@constant/apiEndPoints';
// import { getDTNameAndVersion } from '@helper/helper';
import ReusableSnackBar from "@components/Common/ReusableSnackBar";
import { useState } from 'react';
import useChangePayloadCreation from '@hooks/useChangePayloadCreation';
import { REQUEST_TYPE } from '@constant/enum';
import { useLocation } from 'react-router-dom';
// import CustomCheckbox from "@components/Common/ui/CustomCheckbox"

const PreviewDownloadCardPc = ({ initialReqScreen, isreqBench,module }) => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const RequestId = queryParams.get("RequestId");
  const payloadData = useSelector((state) => state.payload);
  const initialPayload = useSelector((state) => state.payload.payloadData);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState();
  const [alertType, setAlertType] = useState("success");
  const [reviewed,setReviewed] = useState(false)

  const { customError } = useLogger();
//   const { createPayloadFromReduxState } = usePayloadCreation({ initialReqScreen, isreqBench });
  const { changePayloadForTemplate } = useChangePayloadCreation(initialPayload?.TemplateName);

  const handleSnackBarClose = () => {
    setOpenSnackbar(false);
  };

//   const DownloadExcelPreview = () => {
//     const materialPayload = 
//     initialPayload?.RequestType === REQUEST_TYPE?.CHANGE || initialPayload?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD
//       ? RequestId ? changePayloadForTemplate(true) : changePayloadForTemplate(false)
//       : createPayloadFromReduxState(payloadData)
//     const payloadForDownloadExcel = {
//       materialDetails: materialPayload,
//       dtName: getDTNameAndVersion(materialPayload[0]?.Torequestheaderdata?.RequestType).dtName,
//       version: getDTNameAndVersion(materialPayload[0]?.Torequestheaderdata?.RequestType).version,
//       requestId: materialPayload[0]?.Torequestheaderdata?.RequestId || "",
//       scenario: getDTNameAndVersion(materialPayload[0]?.Torequestheaderdata?.RequestType)?.scenario,
//       templateName: 
//       initialPayload?.RequestType === REQUEST_TYPE?.CHANGE || initialPayload?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD
//       ? materialPayload[0]?.Torequestheaderdata?.TemplateName
//       : "",
//       matlType: "ALL",
//       region: materialPayload[0]?.Torequestheaderdata?.Region || "",
//     };
//     const hSuccess = (data) => {
//       if (data?.size > 0) {
//         const href = URL.createObjectURL(data);
//         const link = document.createElement("a");
//         link.href = href;
//         link.setAttribute("download", `Material_Preview_${new Date().getTime()}.xlsx`);
//         document.body.appendChild(link);
//         link.click();
//         document.body.removeChild(link);
//         URL.revokeObjectURL(href);
//       }
//     }
//     const hError = (error) => {
//       customError(error)
//       setMessageDialogMessage(error?.message);
//       setAlertType("error");
//       setOpenSnackbar(true);
//     }
//     doAjax(`/${destination_MaterialMgmt}${END_POINTS.EXCEL.EXPORT_PREVIEW_EXCEL}`, "postandgetblob", hSuccess, hError, payloadForDownloadExcel);
//   }
  return (
    <Grid
      item
      md={12}
      sx={{
        backgroundColor: "white",
        borderRadius: "8px",
        border: "1px solid #E0E0E0",
        boxShadow: "0px 1px 4px rgba(0, 0, 0, 0.1)",
        p: '10px'
      }}
    >
      <Typography
        sx={{
          fontWeight: "bold",
          mb: "6px",
        }}
      >
        Master data details
      </Typography>

      <Box
        sx={{
          backgroundColor: "#FAFAFA",
          borderRadius: "8px",
          boxShadow: "none",
        }}
      >
        <Box sx={{ padding: '8px' }}>
          <Typography align="left" variant="h6" component="h2">
           { `Please download the excel sheet to view all the ${module} data.`}
          </Typography>

          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'flex-start',
              gap: 1,
              mt: 2,
            }}
          >
            <Button
              variant="contained"
              startIcon={
                <DownloadIcon
                  sx={{
                    fontSize: 28,
                    animation: 'downloadBounce 2s ease-in-out infinite',
                    filter: 'drop-shadow(0 2px 4px rgba(255,255,255,0.3))',
                  }}
                />
              }
            //   onClick={DownloadExcelPreview}
              sx={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                position: 'relative',
                overflow: 'hidden',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: '-100%',
                  width: '100%',
                  height: '100%',
                  background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
                  transition: 'left 0.5s',
                },
                '&:hover::before': {
                  left: '100%',
                },
                '&:hover': {
                  background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                  transform: 'translateY(-3px) scale(1.02)',
                  boxShadow: '0 12px 25px rgba(102, 126, 234, 0.4), 0 0 20px rgba(118, 75, 162, 0.3)',
                },
                '&:active': {
                  transform: 'translateY(-1px) scale(0.98)',
                  boxShadow: '0 6px 15px rgba(102, 126, 234, 0.3)',
                },
                transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
                borderRadius: 3,
                py: 1.8,
                px: 3,
                textTransform: 'none',
                fontSize: '1.1rem',
                fontWeight: 600,
                boxShadow: '0 8px 20px rgba(102, 126, 234, 0.3), 0 0 15px rgba(118, 75, 162, 0.2)',
                display: 'flex',
                alignItems: 'center',
                gap: 1.5,
                border: '1px solid rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                color: '#ffffff',
                letterSpacing: '0.5px',
                minWidth: '180px',
                '@keyframes downloadBounce': {
                  '0%, 100%': {
                    transform: 'translateY(0) rotate(0deg)',
                    filter: 'drop-shadow(0 2px 4px rgba(255,255,255,0.3))',
                  },
                  '25%': {
                    transform: 'translateY(-3px) rotate(-2deg)',
                    filter: 'drop-shadow(0 4px 8px rgba(255,255,255,0.4))',
                  },
                  '50%': {
                    transform: 'translateY(-6px) rotate(0deg)',
                    filter: 'drop-shadow(0 6px 12px rgba(255,255,255,0.5))',
                  },
                  '75%': {
                    transform: 'translateY(-3px) rotate(2deg)',
                    filter: 'drop-shadow(0 4px 8px rgba(255,255,255,0.4))',
                  },
                },
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  width: '0',
                  height: '0',
                  background: 'radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%)',
                  borderRadius: '50%',
                  transform: 'translate(-50%, -50%)',
                  transition: 'width 0.6s, height 0.6s',
                  pointerEvents: 'none',
                },
                '&:active::after': {
                  width: '300px',
                  height: '300px',
                },
              }}
            >
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                position: 'relative',
                zIndex: 1,
              }}>
                Download Excel
                <Box
                  component="span"
                  sx={{
                    fontSize: '0.8rem',
                    opacity: 0.8,
                    fontWeight: 400,
                    ml: 0.5,
                  }}
                >
                  (.xlsx)
                </Box>
              </Box>
            </Button>
            {/* <CustomCheckbox label="I have reviewed all material details." checked={reviewed} onChange={() => setReviewed(!reviewed)}/> */}
          </Box>
        </Box>
      </Box>
      {<ReusableSnackBar openSnackBar={openSnackbar} alertMsg={messageDialogMessage} alertType={alertType} handleSnackBarClose={handleSnackBarClose} />}
    </Grid>
  );
};

export default PreviewDownloadCardPc;
