import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  openSnackbar: false,
  messageDialogMessage: '',
  alertType: 'success',
};

export const snackbarSlice = createSlice({
  name: 'snackbar',
  initialState,
  reducers: {
    openSnackbar: (state, action) => {
      state.openSnackbar = true;
      state.messageDialogMessage = action.payload.message || '';
      state.alertType = action.payload.type || 'success';
    },
    closeSnackbar: (state) => {
      state.openSnackbar = false;
    },
    setSnackbarMessage: (state, action) => {
      state.messageDialogMessage = action.payload;
    },
    setAlertType: (state, action) => {
      state.alertType = action.payload;
    },
    resetSnackbar: (state) => {
      return initialState;
    }
  },
});

export const {
  openSnackbar,
  closeSnackbar,
  setSnackbarMessage,
  setAlertType,
  resetSnackbar
} = snackbarSlice.actions;

export const snackBarReducer =  snackbarSlice.reducer;