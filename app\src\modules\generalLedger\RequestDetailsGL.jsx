import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  destination_GeneralLedger,
  destination_GeneralLedger_Mass,
  destination_ProfitCenter,
  destination_ProfitCenter_Mass,
} from "../../destinationVariables";
import { v4 as uuidv4 } from "uuid";
import { doAjax } from "../../components/Common/fetchService";

import useButtonDTConfig from "@hooks/useButtonDTConfig";
import ReusableBackDrop from "../../components/Common/ReusableBackDrop";

import ReusableDataTable from "../../components/Common/ReusableTable";
import SingleSelectDropdown from "@components/Common/ui/dropdown/SingleSelectDropdown";
import { useLocation, useNavigate } from "react-router-dom";
import { setDropDown } from "../../app/dropDownDataSlice";
import DescriptionIcon from "@mui/icons-material/Description";

import { useChangeLogUpdateGl } from "@hooks/useChangeLogUpdateGl";

import {
  TextField,
  IconButton,
  Box,
  Typography,
  Paper,
  Button,
  Tabs,
  Tab,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Snackbar,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Grid,
  Checkbox,
  Tooltip,
  Autocomplete,
  TableContainer,
  Chip,
} from "@mui/material";
import ReusableSnackBar from "@components/Common/ReusableSnackBar";

import { Add } from "@mui/icons-material";

import TaskAltIcon from "@mui/icons-material/TaskAlt";

import WarningAmberIcon from "@mui/icons-material/WarningAmber";
import GenericTabsGlobal from "../../components/MasterDataCockpit/GenericTabsGlobal";
import { colors } from "@constant/colors";

import { createPayloadForGL } from "../../functions";
import BottomNavGlobal from "../../components/RequestBench/RequestPages/BottomNavGlobal";
import { setDependentDropdown } from "@app/dropDownDataSlice";
import useGeneralLedgerFieldConfig from "../../hooks/useGeneralLedgerFieldConfig";
import {
  setGLRows,
  setSelectedRowIdGL,
  updateModuleFieldDataGL,
  setValidatedStatus,
  setdropdownDataForExtendedCode,
  setSelecteddropdownDataForExtendedCode,
} from "@app/generalLedgerTabSlice";

import CloseFullscreenIcon from "@mui/icons-material/CloseFullscreen";
import CropFreeIcon from "@mui/icons-material/CropFree";
import useLang from "@hooks/useLang";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { CHANGE_LOG_STATUSES } from "@constant/enum";

const RequestDetailsGL = ({ reqBench, apiResponses, moduleName }) => {
  console.log(moduleName, "moduleNameinDispatch");
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { t } = useLang();

  const initialPayload = useSelector((state) => state.payload.payloadData);

  let task = useSelector((state) => state?.userManagement.taskData);
  const { updateChangeLogGl } = useChangeLogUpdateGl();

  const validatedStatus = useSelector(
    (state) => state?.generalLedger?.validatedRowsStatus
  );
  const selectedExtendDropdownData = useSelector(
    (state) => state?.generalLedger?.selecteddropdownDataForExtendedCode
  );

  console.log(selectedExtendDropdownData, "selectedExtendDropdownData");
  const fixedOption = "Basic Data";

  const [selectedViews, setSelectedViews] = useState([fixedOption]);

  const selectedRowId = useSelector(
    (state) => state.generalLedger.selectedRowId
  );
  const companycodeExtendedTo = useSelector(
    (state) => state.generalLedger.dropdownDataForExtendedCode
  );

  const generalLedgerTabs = useSelector((state) => {
    const tabs = state.generalLedger.generalLedgerTabs || [];
    return tabs.filter((tab) => tab.tab !== "Initial Screen");
  });
  const createPayloadCopyForChangeLog = useSelector(
    (state) => state.changeLog.createChangeLogDataGL || []
  );
  const createChangeLogData = useSelector(
    (state) => state.changeLog.createChangeLogDataGL
  );

  console.log(createChangeLogData, "createPayloadCopyForChangeLoglk");
  console.log(generalLedgerTabs, "generalLedgerTabs");
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const isreqBench = queryParams.get("reqBench");
  const isrequestId = queryParams.get("RequestId");
  const requestId = queryParams.get("RequestId");

  const requestHeaderSlice = useSelector(
    (state) => state.request.requestHeader
  );
  const allDropDownData = useSelector(
    (state) => state.AllDropDown?.dropDown || {}
  );

  console.log(allDropDownData, "allDropDownData");

  const requestHeaderData = useSelector((state) => state.requestHeader);
  const { loading, error, fetchGeneralLedgerFieldConfig } =
    useGeneralLedgerFieldConfig(moduleName);
  const glRows = useSelector(
    (state) => state.generalLedger.payload.rowsHeaderData
  );

  console.log(glRows, "glRowsData");
  const [rows, setRows] = useState(glRows || []);

  const reduxPayload = useSelector((state) => state.generalLedger.payload);
  const rowsBodyData = useSelector(
    (state) => state.generalLedger.payload?.rowsBodyData || {}
  );

  let requestStatus =
    rowsBodyData?.[glRows[0]?.id]?.["Torequestheaderdata"]?.["RequestStatus"];
  console.log(requestStatus, "requestStatusdaaya");

  const selectedCompanyCodeData = useSelector(
    (state) => state.generalLedger.payload?.rowsHeaderData[0]?.companyCode || {}
  );

  console.log(selectedCompanyCodeData, "selectedCompanyCodeData");

  console.log(rowsBodyData, "rowsBodyData");
  const dynamicData = useSelector((state) => state.payload.dynamicKeyValues);
  const filteredButtons = useSelector((state) => state.payload.filteredButtons);

  const [selectedRow, setSelectedRow] = useState(null);
  console.log(selectedRow?.id, "idSelectedRow");
  const [selectedTab, setSelectedTab] = useState(0);
  const [rowTabData, setRowTabData] = useState({});
  const [dropdownDataCompany, setDropdownDataCompany] = useState([]);
  const [dropdownDataCompanyForExtend, setDropdownDataCompanyForExtend] =
    useState([]);
  const [dropdownDataAccountType, setDropdownDataAccountType] = useState([]);
  const [dropdownDataTaxJur, setDropdownDataTaxJur] = useState([]);
  const [dropdownDataAccountGroup, setDropdownDataAccountGroup] = useState([]);

  const [dropdownDataProfitCtrGrp, setDropdownDataProfitCtrGrp] = useState([]);
  const [dropdownDataFormPlanning, setDropdownDataFormPlanning] = useState([]);
  const [dropdownDataCOA, setDropdownDataCOA] = useState([]);
  const [dropdownDataCountry, setDropdownDataCountry] = useState([]);
  const [selectedCountry, setSelectedCountry] = useState("");
  const [dropdownDataRegion, setDropdownDataRegion] = useState([]);
  const [dropdownDataSegment, setDropdownDataSegment] = useState([]);
  const [dropdownDataLanguage, setDropdownDataLanguage] = useState([]);
  const [missingFieldsDialogOpen, setMissingFieldsDialogOpen] = useState(false);
  const [missingFields, setMissingFields] = useState([]);
  const [isAddRowEnabled, setIsAddRowEnabled] = useState(false);
  const [isSaveAsDraftEnabled, setIsSaveAsDraftEnabled] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [openSnackBar, setOpenSnackBar] = useState(false);
  const [alertMsg, setAlertMsg] = useState("");
  const [alertType, setAlertType] = useState("success"); // 'success' or 'error'
  const [isLoading, setIsLoading] = useState(false);
  const [validatedRows, setValidatedRows] = useState({});
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const { getButtonsDisplayGlobal } = useButtonDTConfig();
  const [originalRowData, setOriginalRowData] = useState({});
  const [originalTabData, setOriginalTabData] = useState({});
  const [isGridZoomed, setIsGridZoomed] = useState(false);
  const [isTabsZoomed, setIsTabsZoomed] = useState(false);
  const [openOrgData, setOpenOrgData] = useState(false);

  const [selectedOptions, setSelectedOptions] = useState([]);

  console.log(selectedRow, "selectedRowId");

  const selectAllOption = { code: "ALL", desc: "Select All" };
  const allOptions = [
    { code: "2010", desc: "RITZ TRANSFER SP" },
    { code: "2012", desc: "DUPONT MP LLC" },
    { code: "2014", desc: "ENABLE MIDSTREAM PARTNER" },
    { code: "2020", desc: "MF WNG EXPORT, LLC" },
    { code: "2500", desc: "Incture orissa cocd" },
  ];

  console.log(companycodeExtendedTo, "rowIdSelected");

  const handleChangeExtendCompanycode = (event, newValue) => {
    console.log(newValue, "newValueData");
    const isSelectAllClicked = newValue?.some(
      (val) => val.code === selectAllOption.code
    );

    if (isSelectAllClicked) {
      const allWithoutSelectAll = allOptions; // select all real options
      setSelectedOptions(allWithoutSelectAll);
    } else {
      dispatch(
        setSelecteddropdownDataForExtendedCode({
          uniqueId: selectedRow?.id,
          data: newValue,
        })
      );
      //setSelectedOptions(newValue);
      //  dispatch(
      //   updateModuleFieldDataGL({
      //     uniqueId: selectedRowId || selectedRow?.id,
      //     keyName: "COM",
      //     data: value,
      //     viewID: "Basic Data"
      //   })
      // );
    }
  };

  console.log(selectedOptions, "selectedOptionsdata");

  const getOptionLabel = (option) =>
    option.code === "ALL" ? option.desc : `${option.code} - ${option.desc}`;

  const isOptionSelected = (option, value) =>
    value.some((val) => val.code === option.code);

  const toggleGridZoom = () => {
    setIsGridZoomed(!isGridZoomed);
    if (isTabsZoomed) setIsTabsZoomed(false);
  };

  const toggleTabsZoom = () => {
    setIsTabsZoomed(!isTabsZoomed);
    if (isGridZoomed) setIsGridZoomed(false);
  };

  useEffect(() => {
    if (!generalLedgerTabs?.length) {
      fetchGeneralLedgerFieldConfig();
    }
  }, []);

  useEffect(() => {
    // let filtereddataforExtendedCompanyCode=dropdownDataCompany?.filter(x =>)
    console.log(selectedCompanyCodeData, "selectedCompanyCodeData");
    const filtereddataforExtendedCompanyCode = dropdownDataCompany?.filter(
      (item) => item.code !== selectedCompanyCodeData
    );

    setDropdownDataCompanyForExtend(filtereddataforExtendedCompanyCode);
  }, []);

  useEffect(() => {}, [rowsBodyData]);

  useEffect(() => {}, [
    setDropdownDataAccountType,
    setDropdownDataAccountGroup,
  ]);

  useEffect(() => {
    if (task?.ATTRIBUTE_1 || isrequestId) {
      getButtonsDisplayGlobal("General Ledger", "MDG_DYN_BTN_DT", "v2");
    }
  }, [task]);

  console.log(selectedTab, "selectedTab");

  const mandatoryFieldsConfig = generalLedgerTabs[selectedTab]?.data || [];
  console.log("mandatoryFieldsConfig", mandatoryFieldsConfig);

  const getAllmandetoryjson = () => {
    const extractedFields = [];

    generalLedgerTabs.forEach((tabObj) => {
      const data = tabObj.data;
      Object.values(data).forEach((fieldArray) => {
        extractedFields.push(...fieldArray);
      });
    });

    console.log(mandatoryFields, extractedFields, "mandetoryFieldsDatta");
    return extractedFields;
  };

  const columns = [
    {
      field: "included",
      headerName: "Included",
      // flex: 1,
      width: 100, // fixed or min width
      minWidth: 100,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => (
        <Checkbox
          checked={params.row.included}
          disabled={false}
          onChange={(e) =>
            handleRowInputChange(e.target.checked, params.row.id, "included")
          }
        />
      ),
    },
    {
      field: "lineNumber",
      headerName: "SL No,",
      // flex: 1,
      width: 100, // fixed or min width
      minWidth: 100,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        const rowIndex = glRows.findIndex((row) => row.id === params.row.id);
        return <div>{rowIndex + 1}</div>;
      },
    },
    {
      field: "chartOfAccount",
      headerName: "Chart Of Account",
      align: "center",
      headerAlign: "center",
      // flex: 2,
      width: 250, // fixed or min width
      minWidth: 200,
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={allDropDownData?.["COA"] || []}
            value={params.row.chartOfAccount}
            onChange={(newValue) =>
              handleRowInputChange(newValue, params.row.id, "chartOfAccount")
            }
            placeholder={t("Select Chart Of Account")}
            disabled={false}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "companyCode",
      headerName: "Company Code",
      align: "center",
      headerAlign: "center",
      // flex: 2,
      width: 250, // fixed or min width
      minWidth: 200,
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={allDropDownData?.["CompanyCode"] || []}
            value={params.row.companyCode}
            onChange={(newValue) =>
              handleRowInputChange(newValue, params.row.id, "companyCode")
            }
            placeholder={t("Select Company Code")}
            disabled={false}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "accountType",
      headerName: "Account Type",
      // flex: 2,
      width: 250, // fixed or min width
      minWidth: 200,
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={allDropDownData?.["accountType"] || []}
            value={params.row.accountType}
            onChange={(newValue) =>
              handleRowInputChange(newValue, params.row.id, "accountType")
            }
            placeholder={t("Select Account Type")}
            disabled={false}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "accountGroup",
      headerName: "Account Group",
      // flex: 2,
      width: 250, // fixed or min width
      minWidth: 200,
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={allDropDownData?.["accountGroup"] || []}
            value={params.row.accountGroup}
            onChange={(newValue) =>
              handleRowInputChange(newValue, params.row.id, "accountGroup")
            }
            placeholder={t("Select Account Group")}
            disabled={false}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "glAccountNumber",
      headerName: "General Ledger Number",
      // flex: 4,
      width: 250, // fixed or min width
      minWidth: 200,
      renderCell: (params) => {
        const accountGroupValue = params.row.accountGroup;
        console.log(accountGroupValue, "accountGroupValue");
        const value = params.row.glAccountNumber || "";
        const isInvalid = value.length > 0 && value.length < 10;

        const handleKeyDown = (e) => {
          // Allow navigation & control keys
          if (
            [
              "Backspace",
              "Delete",
              "Tab",
              "Escape",
              "Enter",
              "ArrowLeft",
              "ArrowRight",
            ].includes(e.key)
          ) {
            return;
          }

          // Block if not a digit
          if (!/^\d$/.test(e.key)) {
            e.preventDefault();
          }
        };

        const handlePaste = (e) => {
          const paste = e.clipboardData.getData("text");
          if (!/^\d+$/.test(paste)) {
            e.preventDefault(); // block paste if not all digits
          }
        };

        return (
          <TextField
            value={value}
            onChange={(e) => {
              const numericValue = e.target.value.slice(0, 10); // only digits allowed by keyboard/paste
              handleRowInputChange(
                numericValue,
                params.row.id,
                "glAccountNumber"
              );
            }}
            onKeyDown={handleKeyDown}
            onPaste={handlePaste}
            variant="outlined"
            size="small"
            placeholder={
              accountGroupValue?.FromAcct && accountGroupValue?.ToAcct
                ? `${accountGroupValue?.FromAcct} - ${accountGroupValue?.ToAcct}`
                : `-`
            }
            fullWidth
            error={isInvalid}
            helperText={isInvalid ? "Number should be 10 digits" : ""}
            inputProps={{
              inputMode: "numeric",
              maxLength: 10,
            }}
            sx={{
              "& .MuiInputBase-root.Mui-disabled": {
                "& > input": {
                  WebkitTextFillColor: colors.black.dark,
                  color: colors.black.dark,
                },
              },
            }}
          />
        );
      },
    },
    {
      field: "longDescription",
      headerName: "Long Description",
      // flex: 2,
      width: 250, // fixed or min width
      minWidth: 200,
      renderCell: (params) => {
        const value = params.row.longDescription || "";

        return (
          <TextField
            value={value}
            onChange={(e) => {
              handleRowInputChange(
                e.target.value.toUpperCase(),
                params.row.id,
                "longDescription"
              );
            }}
            variant="outlined"
            size="small"
            placeholder="Enter Long Description"
            fullWidth
            sx={{
              "& .MuiInputBase-root.Mui-disabled": {
                "& > input": {
                  WebkitTextFillColor: colors.black.dark,
                  color: colors.black.dark,
                },
              },
            }}
          />
        );
      },
    },
    {
      field: "shortDescription",
      headerName: "Short Description",
      // flex: 2,
      width: 250, // fixed or min width
      minWidth: 200,
      renderCell: (params) => {
        const value = params.row.shortDescription || "";

        return (
          <TextField
            value={value}
            onChange={(e) => {
              handleRowInputChange(
                e.target.value.toUpperCase(),
                params.row.id,
                "shortDescription"
              );
            }}
            variant="outlined"
            size="small"
            placeholder="Enter Short Description"
            fullWidth
            sx={{
              "& .MuiInputBase-root.Mui-disabled": {
                "& > input": {
                  WebkitTextFillColor: colors.black.dark,
                  color: colors.black.dark,
                },
              },
            }}
          />
        );
      },
    },
    {
      field: "businessSegment",
      headerName: "Business Segment",
      // flex: 1.5,
      width: 250, // fixed or min width
      minWidth: 200,
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={
              [
                { code: "CRUDE", desc: "" },
                { code: "INTERSTATE", desc: "" },
                { code: "NA", desc: "" },
              ] || []
            }
            value={params.row.businessSegment}
            onChange={(newValue) =>
              handleRowInputChange(newValue, params.row.id, "businessSegment")
            }
            placeholder={t("Select Business Segment")}
            disabled={false}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },

    {
      // ...(requestType === REQUEST_TYPE.CREATE || requestType === REQUEST_TYPE.CREATE_WITH_UPLOAD
      //   ? {
      field: "CoCodeToExtend",
      headerName: "Company Code Extend To", // No column heading
      width: 250, // fixed or min width
      minWidth: 200,
      align: "center", // Center-align cell content
      headerAlign: "center", // Center-align header
      renderCell: (params) => {
        // const companyCodeSelected = params?.row?.companyCode?.code;
        // const filteredcompanyCode = dropdownDataCompany?.filter(item => item?.code !== companyCodeSelected)

        // console.log(filteredcompanyCode,"filteredcompanyCode")
        //setDropdownDataCompanyForExtend(filteredcompanyCode)

        return (
          <Button
            variant="contained"
            //disabled={!(params?.row?.views?.length > 1)}
            size="small"
            sx={{ marginLeft: "4px" }}
            onClick={() => {
              setOpenOrgData(true);
              // setRowId(params.row.id), setOrgRow(params?.row?.orgData?.length ? params.row?.orgData : [orgRowFields]);
            }}
          >
            Company Code Extend To
          </Button>
        );
      },
      //}
      // : {}),
    },
    {
      field: "action",
      headerName: "Action",
      // flex: 0.3,
      width: 80, // fixed or min width
      minWidth: 80,
      headerAlign: "center",
      renderHeader: () => (
        <span style={{ fontWeight: "bold" }}>{t("Action")}</span>
      ),
      renderCell: (params) => {
        const rowId = params.row.id;
        const rowData = {
          id: rowId,
          ...rowsBodyData[rowId],
        };

        const validateStatus = getValidationStatus(rowId);
        console.log("validateStatus", validateStatus);

        let mandetoryFieldsData = getAllmandetoryjson();

        const minrangeOfGlAccount = parseInt(params.row.accountGroup?.FromAcct);
        const maxrangeOfGlAccount = parseInt(params.row.accountGroup?.ToAcct);
        const glValue = parseInt(params.row?.glAccountNumber);
        console.log(glValue, "glValueDta");

        console.log(
          minrangeOfGlAccount,
          maxrangeOfGlAccount,
          glValue,
          isNaN(minrangeOfGlAccount),
          "wewekwlkw"
        ); //
        const isInRange =
          !isNaN(minrangeOfGlAccount) &&
          !isNaN(maxrangeOfGlAccount) &&
          glValue >= minrangeOfGlAccount &&
          glValue <= maxrangeOfGlAccount;

        const handleValidateClick = (e) => {
          e.stopPropagation();
          if (glValue == "" || glValue == NaN || glValue == undefined) {
            if (isInRange) {
              //handleValidate(params.row, rowData, mandetoryFieldsData);
              handleValidate(params.row, rowData, generalLedgerTabs);
            } else {
              //alert("glAccount not In Range")
              setAlertType("error");
              setAlertMsg("GlAccount not In Range.");
              setOpenSnackBar(true);
            }
          } else {
            handleValidate(params.row, rowData, generalLedgerTabs);
          }
        };

        return (
          <Box>
            <Tooltip
              title={
                validateStatus === "success"
                  ? "Validated Successfully"
                  : validateStatus === "error"
                  ? "Validation Failed"
                  : "Click to Validate"
              }
            >
              <IconButton onClick={handleValidateClick} color={validateStatus}>
                {validateStatus === "error" ? (
                  <CancelOutlinedIcon />
                ) : (
                  <TaskAltIcon />
                )}
              </IconButton>
            </Tooltip>
            {/* <IconButton
              onClick={() => handleDelete(params.row.id)}
              color="error"
              size="small"
            >
              <DeleteOutlineOutlinedIcon />
            </IconButton> */}
          </Box>
        );
      },
    },
  ];

  const mandatoryFields = [
    "chartOfAccount",
    "companyCode",
    "accountType",
    "accountGroup",
    "glAccountNumber",
    "longDescription",
    "businessSegment",
  ];

  const isEqual = (a, b) => JSON.stringify(a) === JSON.stringify(b);

  const handleSnackBarClose = () => {
    setOpenSnackBar(false);
  };

  useEffect(() => {
    const allRowsValidated =
      glRows.length > 0 &&
      glRows.every((row) => validatedRows[row.id] === true);
    setIsAddRowEnabled(allRowsValidated);
    setIsSaveAsDraftEnabled(allRowsValidated);
  }, [glRows, validatedRows]);

  useEffect(() => {
    if (glRows.length && Object.keys(rowsBodyData).length) {
      const newOriginalRows = {};
      const newOriginalTabs = {};

      glRows.forEach((row) => {
        const rowId = row.id;
        if (!originalRowData[rowId]) {
          newOriginalRows[rowId] = JSON.parse(JSON.stringify(row));
        }
        if (!originalTabData[rowId] && rowsBodyData[rowId]) {
          const { id, ...restTab } = rowsBodyData[rowId];
          newOriginalTabs[rowId] = JSON.parse(JSON.stringify(restTab));
        }
      });

      setOriginalRowData((prev) => ({ ...prev, ...newOriginalRows }));
      setOriginalTabData((prev) => ({ ...prev, ...newOriginalTabs }));
    }
  }, [glRows, rowsBodyData]);

  const checkGLAccountDuplicateCheckGeneralLedger = (row) => {
    setIsLoading(true);
    var duplicateCheckDescription = {
      glAccount: row?.glAccountNumber,
      coa: row?.longDescription,
      //requestId: '',
    };
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data?.body?.length > 0) {
        setAlertType("error");
        setAlertMsg("Duplicate GL Account Number  found.");
        setValidatedRows((prev) => ({ ...prev, [row.id]: false }));
      } else {
        checkDuplicateNameCheckGeneralLedger(row);
      }
      setOpenSnackBar(true);
    };

    const hError = (error) => {
      setIsLoading(false);
      setAlertType("error");
      setAlertMsg("An error occurred during validation.");
      setOpenSnackBar(true);
      // Mark this row as not validated on error
      setValidatedRows((prev) => ({ ...prev, [row.id]: false }));
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/alter/fetchGlAccountNCoaDupliChk`,
      "post",
      hSuccess,
      hError,
      duplicateCheckDescription
    );
  };

  const checkDuplicateNameCheckGeneralLedger = (row) => {
    setIsLoading(true);
    var duplicateCheckDescription = {
      coa: row?.chartOfAccount,
      glName: row?.shortDescription,
      //requestId: '',
    };
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data?.body?.length > 0) {
        setAlertType("error");
        setAlertMsg("Duplicate GL Name found.");
        setValidatedRows((prev) => ({ ...prev, [row.id]: false }));
      } else {
        checkDuplicateDescCheckGeneralLedger(row);
      }
      setOpenSnackBar(true);
    };

    const hError = (error) => {
      setIsLoading(false);
      console.error(error);
      setAlertType("error");
      setAlertMsg("An error occurred during validation.");
      setOpenSnackBar(true);
      // Mark this row as not validated on error
      setValidatedRows((prev) => ({ ...prev, [row.id]: false }));
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/alter/fetchGlNameNCoaDupliChk`,
      "post",
      hSuccess,
      hError,
      duplicateCheckDescription
    );
  };

  const checkDuplicateDescCheckGeneralLedger = (row) => {
    setIsLoading(true);
    var duplicateCheckDescription = {
      coa: row?.chartOfAccount,
      glDesc: row?.chartOfAccount,
      //requestId: '',
    };
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data?.body?.length > 0) {
        setAlertType("error");
        setAlertMsg("Duplicate Gl Long Text found.");
        setValidatedRows((prev) => ({ ...prev, [row.id]: false }));
      } else {
        setAlertType("success");
        setAlertMsg("Validation Successful");
        // Mark this row as validated
        setValidatedRows((prev) => ({ ...prev, [row.id]: true }));
      }
      setOpenSnackBar(true);
    };

    const hError = (error) => {
      setIsLoading(false);
      console.error(error);
      setAlertType("error");
      setAlertMsg("An error occurred during validation.");
      setOpenSnackBar(true);
      // Mark this row as not validated on error
      setValidatedRows((prev) => ({ ...prev, [row.id]: false }));
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/alter/fetchGlDescNCoaDupliChk`,
      "post",
      hSuccess,
      hError,
      duplicateCheckDescription
    );
  };

  const handleValidate = (row, tab, config) => {
    const missing = [];

    const allFields = config.flatMap((section) => {
      return Object.values(section.data).flat(); // flatten inner field arrays
    });

    allFields?.forEach((field) => {
      if (field.visibility === "Mandatory") {
        const value = tab[field.jsonName];
        if (
          value === null ||
          value === undefined ||
          (typeof value === "string" && value.trim() === "")
        ) {
          missing.push(field.fieldName);
        }
      }
    });
    const headerMap = {
      companyCode: "Company Code",
      glAccountNumber: "General Ledger Number",
      businessSegment: "Business Segment",
      controllingArea: "Controlling Area",
      longDescription: "Long Text",
      shortDescription: "Short Text",
      longDescription: "Long Text",
      // Add more as needed
    };
    mandatoryFields.forEach((field) => {
      const value = row[field];
      const displayName = headerMap[field] || field;
      if (
        value === null ||
        value === undefined ||
        (typeof value === "string" && value.trim() === "")
      ) {
        missing.push(displayName);
      } else if (
        field === "costCenterNumber" &&
        (value.length !== 10 || !/^[a-zA-Z0-9]+$/.test(value))
      ) {
        // Check for exact 10 alphanumeric characters
        missing.push(displayName);
      }
    });

    const status = missing.length > 0 ? "error" : "success";

    dispatch(setValidatedStatus({ rowId: row.id, status }));

    if (status === "error") {
      setMissingFields(missing);
      setMissingFieldsDialogOpen(true);
    } else {
      setOriginalRowData((prev) => ({
        ...prev,
        [row.id]: JSON.parse(JSON.stringify(row)),
      }));
      setOriginalTabData((prev) => {
        const { id, ...restTab } = tab;
        return {
          ...prev,
          [row.id]: JSON.parse(JSON.stringify(restTab)),
        };
      });
      checkGLAccountDuplicateCheckGeneralLedger(row);
    }
  };

  const handleMassValidate = (row) => {
    const missing = [];
    mandatoryFields.forEach((field) => {
      if (!row[field]) {
        missing.push(field);
      }
    });

    if (missing.length > 0) {
      setMissingFields(missing);
      setMissingFieldsDialogOpen(true);
      setValidatedRows((prev) => ({ ...prev, [row.id]: false }));
      return false;
    } else {
      return new Promise((resolve) => {
        const hSuccess = (data) => {
          if (data?.body?.length > 0) {
            setValidatedRows((prev) => ({ ...prev, [row.id]: false }));
            resolve(false);
          } else {
            setValidatedRows((prev) => ({ ...prev, [row.id]: true }));
            resolve(true);
          }
        };

        const hError = () => {
          setValidatedRows((prev) => ({ ...prev, [row.id]: false }));
          resolve(false);
        };

        doAjax(
          `/${destination_ProfitCenter}/alter/fetchCoAreaPCDupliChk?ctrlAreaPCToCheck=${row?.controllingArea?.code}$$${row?.profitCenterNumber}`,
          "get",
          hSuccess,
          hError
        );
      });
    }
  };

  const validateAllRows = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForGL(
      reduxPayload,
      requestHeaderSlice,
      isrequestId,
      task,
      dynamicData,
      createChangeLogData,
      selectedExtendDropdownData
    );

    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);

      setAlertMsg("General Ledgers Validation initiated");
      setIsSaveAsDraftEnabled(true);

      setTimeout(() => {
        navigate("/requestbench");
      }, 3000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while validating the request");
      console.error("Error saving draft:", error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/massAction/validateMassGeneralLedger`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };
  const handleCloseDialog = () => {
    setMissingFieldsDialogOpen(false);
  };

  const handleDelete = (id) => {
    // setRows(glRows.filter((row) => row.id !== id));
    const updatedRows = glRows.filter((row) => row.id !== id);
    dispatch(setGLRows(updatedRows));
    if (selectedRow?.id === id) {
      setSelectedRow(null);
    }
    const updatedRowTabData = { ...rowTabData };
    delete updatedRowTabData[id];
    setRowTabData(updatedRowTabData);

    setValidatedRows((prev) => {
      const updated = { ...prev };
      delete updated[id];
      return updated;
    });
  };

  const minMaxRangeDetailsOfGlAccount = (value) => {
    console.log(value, "valueRange");
  };

  const handleRowInputChange = (value, id, field) => {
    //alert("coming34")
    if (field === "chartOfAccount") {
      getCompanyCode(value?.code);
      getAccountType(value?.code);
      getAccountGroup(value?.code);
      getSortKey();
      getLanguage();
    }
    if (field === "companyCode") {
      getAccountType(value?.code);
      getAccountCurrency(value?.code);
      getTaxCategory(value?.code);
      getHouseBank(value?.code);
      getAccontId(value?.code);
      getFiledStatusGroup(value?.code);
      getreconAccountType();
      getPlanningLevel();

      let filteredCompanyCodeForExtend = dropdownDataCompany?.filter(
        (item) => item.code !== value?.code
      );

      dispatch(
        setdropdownDataForExtendedCode({
          uniqueId: id,
          data: filteredCompanyCodeForExtend,
        })
      );

      dispatch(
        updateModuleFieldDataGL({
          uniqueId: selectedRowId || selectedRow?.id,
          keyName: "CompanyCode",
          data: value?.code,
          viewID: "Comp Codes",
        })
      );
    }
    if (field === "accountType") {
      //alert("coming5")
      //getAccountGroup()
      getCostElementCategory(value?.code);
      dispatch(
        updateModuleFieldDataGL({
          uniqueId: selectedRowId || selectedRow?.id,
          keyName: "Accounttype",
          data: value?.code,
          viewID: "Type/Description",
        })
      );
      {
        requestId &&
          !CHANGE_LOG_STATUSES.includes(requestStatus) &&
          updateChangeLogGl({
            uniqueId: selectedRowId || selectedRow?.id,
            viewName: "Type/Description",
            plantData: "",
            fieldName: "Account Type",
            jsonName: "Accounttype",
            currentValue: value?.code,
            requestId: initialPayload?.RequestId,
            childRequestId: requestId,
          });
      }
    }
    if (field === "accountGroup") {
      //alert("coming2")
      minMaxRangeDetailsOfGlAccount(value);
      dispatch(
        updateModuleFieldDataGL({
          uniqueId: selectedRowId || selectedRow?.id,
          keyName: "AccountGroup",
          data: value?.code,
          viewID: "Type/Description",
        })
      );
      {
        requestId &&
          !CHANGE_LOG_STATUSES.includes(requestStatus) &&
          updateChangeLogGl({
            uniqueId: selectedRowId || selectedRow?.id,
            viewName: "Type/Description",
            plantData: "",
            fieldName: "Account Group",
            jsonName: "AccountGroup",
            currentValue: value?.code,
            requestId: initialPayload?.RequestId,
            childRequestId: requestId,
          });
      }
    }
    if (field === "longDescription") {
      dispatch(
        updateModuleFieldDataGL({
          uniqueId: selectedRowId || selectedRow?.id,
          keyName: "Description",
          data: value,
          viewID: "Basic Data",
        })
      );
      {
        requestId &&
          !CHANGE_LOG_STATUSES.includes(requestStatus) &&
          updateChangeLogGl({
            uniqueId: selectedRowId || selectedRow?.id,
            viewName: "Basic Data",
            plantData: "",
            fieldName: "Long Text",
            jsonName: "Description",
            currentValue: value,
            requestId: initialPayload?.RequestId,
            childRequestId: requestId,
          });
      }
    }
    if (field === "shortDescription") {
      dispatch(
        updateModuleFieldDataGL({
          uniqueId: selectedRowId || selectedRow?.id,
          keyName: "GLname",
          data: value,
          viewID: "Basic Data",
        })
      );
      {
        requestId &&
          !CHANGE_LOG_STATUSES.includes(requestStatus) &&
          updateChangeLogGl({
            uniqueId: selectedRowId || selectedRow?.id,
            viewName: "Basic Data",
            plantData: "",
            fieldName: "Short Text",
            jsonName: "GLname",
            currentValue: value,
            requestId: initialPayload?.RequestId,
            childRequestId: requestId,
          });
      }
    }
    const updatedRows = glRows.map((row) =>
      row.id === id ? { ...row, [field]: value } : row
    );
    dispatch(setGLRows(updatedRows));
  };

  const handleRowClick = (params) => {
    const clickedRow = params.row;
    setSelectedRow(clickedRow);
    dispatch(setSelectedRowIdGL(clickedRow?.GeneralLedgerID));
  };

  const handleAddRow = () => {
    const id = uuidv4();
    const newRow = {
      id,
      chartOfAccount: "",
      companyCode: "",
      accountType: "",
      accountGroup: "",
      glAccountNumber: "",
      businessSegment: "",
      included: true,
      isNew: true,
    };
    dispatch(setGLRows([...glRows, newRow]));
    setValidatedRows((prev) => ({ ...prev, [newRow.id]: false }));
  };

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  const fieldsSyncedFromRow = {
    Description: "longDescription",
    GLname: "shortDescription",
    AccountType: "accountType",
    AccountGroup: "accountGroup",
  };

  const cleanTabForComparison = (tab, row) => {
    const cleanedTab = { ...tab };
    delete cleanedTab.id;
    for (const [tabKey, rowPath] of Object.entries(fieldsSyncedFromRow)) {
      const rowValue = rowPath
        .split(".")
        .reduce((acc, key) => (acc ? acc[key] : undefined), row);

      // If tab value matches what came from row, ignore it
      if (cleanedTab[tabKey] === rowValue) {
        delete cleanedTab[tabKey];
      }
    }
    return cleanedTab;
  };

  const isRowDirty = (rowId) => {
    const originalRow = originalRowData[rowId];
    const originalTab = originalTabData[rowId];
    const currentRow = glRows.find((r) => r.id === rowId);
    const currentTab = rowsBodyData[rowId];
    if (!originalRow || !originalTab || !currentRow || !currentTab) return true;

    const cleanedCurrentTab = cleanTabForComparison(currentTab, currentRow);
    const cleanedOriginalTab = cleanTabForComparison(originalTab, originalRow);

    return (
      !isEqual(originalRow, currentRow) ||
      !isEqual(cleanedOriginalTab, cleanedCurrentTab)
    );
  };

  const handleSelectAll = () =>
    setSelectedViews(dropdownDataCompany || [fixedOption]);

  const getValidationStatus = (rowId) => {
    const status = validatedStatus[rowId];
    const dirty = isRowDirty(rowId);
    if (!status) return "default";
    return isRowDirty(rowId) ? "error" : status;
  };

  useEffect(() => {
    setSelectedRow(glRows[0]);
    //getCompanyCode();
  }, []);

  const getCompanyCode = (coa) => {
    // alert("comapny Code Call")
    const hSuccess = (data) => {
      setDropdownDataCompany(data.body);
      dispatch(setDropDown({ keyName: "CompanyCode", data: data.body }));
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getCompanyCode?chartAccount=${coa}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getAccountType = () => {
    // alert("comapny Code Call")

    const hSuccess = (data) => {
      setDropdownDataAccountType(data.body);
      dispatch(setDropDown({ keyName: "accountType", data: data.body }));
      dispatch(
        setDependentDropdown({
          keyName: "Accounttype",
          data: data.body || [],
          keyName2: selectedRowId || selectedRow?.id,
        })
      );
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getGLAccountType`,
      "get",
      hSuccess,
      hError
    );
  };

  const getAccountCurrency = (compCode) => {
    // alert("comapny Code Call")

    const hSuccess = (data) => {
      // setDropdownDataAccountType(data.body)
      // dispatch(setDropDown({ keyName: "accountType", data: data.body }));
      dispatch(
        setDependentDropdown({
          keyName: "AccountCurrency",
          data: data.body || [],
          keyName2: selectedRowId || selectedRow?.id,
        })
      );
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getAccountCurrency?companyCode=${compCode}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getFiledStatusGroup = (compCode) => {
    console.log(compCode);
    // alert("comapny Code Call")

    const hSuccess = (data) => {
      // setDropdownDataAccountType(data.body)
      // dispatch(setDropDown({ keyName: "accountType", data: data.body }));
      dispatch(
        setDependentDropdown({
          keyName: "FieldStsGrp",
          data: data.body || [],
          keyName2: selectedRowId || selectedRow?.id,
        })
      );
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getFieldStatusGroup?fieldStatusVariant=${compCode}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getTaxCategory = (compCode) => {
    // alert("comapny Code Call")

    const hSuccess = (data) => {
      // setDropdownDataAccountType(data.body)
      // dispatch(setDropDown({ keyName: "accountType", data: data.body }));
      dispatch(
        setDependentDropdown({
          keyName: "Taxcategory",
          data: data.body || [],
          keyName2: selectedRowId || selectedRow?.id,
        })
      );
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getTaxCategory?companyCode=${compCode}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getHouseBank = (compCode) => {
    const hSuccess = (data) => {
      // setDropdownDataAccountType(data.body)
      // dispatch(setDropDown({ keyName: "accountType", data: data.body }));
      dispatch(
        setDependentDropdown({
          keyName: "HouseBank",
          data: data.body || [],
          keyName2: selectedRowId || selectedRow?.id,
        })
      );
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getHouseBank?companyCode=${compCode}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getAccontId = (compCode) => {
    const hSuccess = (data) => {
      // setDropdownDataAccountType(data.body)
      // dispatch(setDropDown({ keyName: "accountType", data: data.body }));
      dispatch(
        setDependentDropdown({
          keyName: "AccountId",
          data: data.body || [],
          keyName2: selectedRowId || selectedRow?.id,
        })
      );
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getAccountId?companyCode=${compCode}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getCostElementCategory = (accType) => {
    const hSuccess = (data) => {
      // setDropdownDataAccountType(data.body)
      // dispatch(setDropDown({ keyName: "accountType", data: data.body }));
      dispatch(
        setDependentDropdown({
          keyName: "CostEleCategory",
          data: data.body || [],
          keyName2: selectedRowId || selectedRow?.id,
        })
      );
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getCostElementCategory?accountType=${accType}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getreconAccountType = (compCode) => {
    const hSuccess = (data) => {
      // setDropdownDataAccountType(data.body)
      // dispatch(setDropDown({ keyName: "accountType", data: data.body }));
      dispatch(
        setDependentDropdown({
          keyName: "ReconAcc",
          data: data.body || [],
          keyName2: selectedRowId || selectedRow?.id,
        })
      );
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getReconAccountForAccountType`,
      "get",
      hSuccess,
      hError
    );
  };

  const getPlanningLevel = (compCode) => {
    const hSuccess = (data) => {
      // setDropdownDataAccountType(data.body)
      // dispatch(setDropDown({ keyName: "accountType", data: data.body }));
      dispatch(
        setDependentDropdown({
          keyName: "Planninglevel",
          data: data.body || [],
          keyName2: selectedRowId || selectedRow?.id,
        })
      );
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getPlanningLevel`,
      "get",
      hSuccess,
      hError
    );
  };

  const getAccountGroup = (coa) => {
    const hSuccess = (data) => {
      let accGrparr = [];
      data?.body?.map((item) => {
        let hash = {};
        hash["code"] = item?.AccountGroup;
        hash["desc"] = item?.Description;
        hash["FromAcct"] = item?.FromAcct;
        hash["ToAcct"] = item?.ToAcct;
        accGrparr?.push(hash);
      });
      setDropdownDataAccountType(accGrparr);
      dispatch(
        setDependentDropdown({
          keyName: "AccountGroup",
          data: accGrparr || [],
          keyName2: selectedRowId || selectedRow?.id,
        })
      );

      dispatch(setDropDown({ keyName: "accountGroup", data: accGrparr }));
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getAccountGroup?chartAccount=${coa}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getFormPlanningFrp = () => {
    const hSuccess = (data) => {
      setDropdownDataFormPlanning(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "Template", data: data.body },
      });
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter}/data/getFormPlanningTemp`,
      "get",
      hSuccess,
      hError
    );
  };

  const getChartOfAccount = () => {
    const hSuccess = (data) => {
      //setDropdownDataCOA(data.body);
      dispatch(setDropDown({ keyName: "COA", data: data.body }));
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getChartOfAccounts`,
      "get",
      hSuccess,
      hError
    );
  };

  const getBusSeg = () => {
    const hSuccess = (data) => {
      setDropdownDataCOA(data.body);
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter}/data/getBusinessSegment`,
      "get",
      hSuccess,
      hError
    );
  };

  useEffect(() => {
    getChartOfAccount();
    getBusSeg();
  }, []);

  const [rowRegionData, setRowRegionData] = useState({});

  const getSegment = () => {
    const hSuccess = (data) => {
      setDropdownDataSegment(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "Segment", data: data.body },
      });
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter}/data/getSegment`,
      "get",
      hSuccess,
      hError
    );
  };
  useEffect(() => {
    getSegment();
  }, []);

  const getLanguage = () => {
    const hSuccess = (data) => {
      setDropdownDataLanguage(data.body);
      // dispatch({
      //   type: "SET_DROPDOWN",
      //   payload: { keyName: "Language", data: data.body },
      // });
      dispatch(
        setDependentDropdown({
          keyName: "Language",
          data: data.body || [],
          keyName2: selectedRowId || selectedRow?.id,
        })
      );
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter}/data/getLanguageKey`,
      "get",
      hSuccess,
      hError
    );
  };

  const getSortKey = () => {
    const hSuccess = (data) => {
      setDropdownDataLanguage(data.body);
      // dispatch({
      //   type: "SET_DROPDOWN",
      //   payload: { keyName: "Language", data: data.body },
      // });
      dispatch(
        setDependentDropdown({
          keyName: "Sortkey",
          data: data.body || [],
          keyName2: selectedRowId || selectedRow?.id,
        })
      );
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getSortKey`,
      "get",
      hSuccess,
      hError
    );
  };
  // useEffect(() => {
  //   getLanguage();
  // }, []);

  const handleSaveAsDraft = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForGL(
      reduxPayload,
      requestHeaderSlice,
      isrequestId,
      task,
      dynamicData,
      createChangeLogData,
      selectedExtendDropdownData
    );

    const hSuccess = (data) => {
      setBlurLoading(false);
      setIsLoading(false);
      setAlertType("success");
      setAlertMsg("General Ledger Submission saved as draft.");
      setOpenSnackBar(true);
      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setIsLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving the draft.");
      setOpenSnackBar(true);
      console.error("Error saving draft:", error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/massAction/generalLedgersSaveAsDraft`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };

  const handleSubmitForReview = () => {
    setLoaderMessage("");
    setBlurLoading(true);
    const finalPayload = createPayloadForGL(
      reduxPayload,
      requestHeaderSlice,
      isrequestId,
      task,
      dynamicData,
      createChangeLogData,
      selectedExtendDropdownData
    );

    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("General Ledger submission for save as draft initiated");
      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while saving the draft");
      console.error("Error saving draft:", error);
    };

    // 🔄 API call
    doAjax(
      `/${destination_GeneralLedger_Mass}/massAction/generalLedgersSubmitForReview`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );

    console.log("finalPayload", finalPayload);
  };

  const handleValidateAndSyndicate = (type) => {
    setBlurLoading(true);
    const finalPayload = createPayloadForGL(
      reduxPayload,
      requestHeaderSlice,
      isrequestId,
      task,
      dynamicData,
      createChangeLogData,
      selectedExtendDropdownData
    );

    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      if (type === "validate")
        setAlertMsg("General Ledger Validation initiated");
      else if (type === "syndicate")
        setAlertMsg("General Ledger Syndication initiated");
      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while validating the request");
      console.error("Error saving draft:", error);
    };

    doAjax(
      type === "validate"
        ? `/${destination_GeneralLedger_Mass}/massAction/validateMassGeneralLedger`
        : `/${destination_GeneralLedger_Mass}/massAction/createGeneralLedgersApproved`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };

  const handleSubmitForApprove = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForGL(
      reduxPayload,
      requestHeaderSlice,
      isrequestId,
      task,
      dynamicData,
      createChangeLogData,
      selectedExtendDropdownData
    );

    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("General Ledger submission for Approve initiated");
      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while saving the draft");
      console.error("Error saving draft:", error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/massAction/generalLedgersApprovalSubmit`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };

  const viewsClose = (event, reason) => {
    // if (reason === "backdropClick" || reason === "escapeKeyDown") {
    //   return;
    // }
    setOpenOrgData(false);
  };

  const handleSendBack = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForGL(
      reduxPayload,
      requestHeaderSlice,
      isrequestId,
      task,
      dynamicData,
      createChangeLogData,
      selectedExtendDropdownData
    );

    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Profit Centers submission for Approve initiated");
      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while saving the draft");
      console.error("Error saving draft:", error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/massAction/profitCentersSendForCorrection`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };

  const handleCorrection = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForGL(
      reduxPayload,
      requestHeaderSlice,
      isrequestId,
      task,
      dynamicData,
      createChangeLogData,
      selectedExtendDropdownData
    );

    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg(data?.message ?? "General Ledgers Sent for Correction !");
      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while sending for correction");
      console.error("Error saving draft:", error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/massAction/profitCentersSendForReview`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };

  const handleRejectAndCancel = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForPC(
      reduxPayload,
      requestHeaderSlice,
      isrequestId,
      task,
      dynamicData
    );

    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg(data?.message ?? "General Ledgers Rejected !");
      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while rejecting the request");
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/massAction/profitCentersRejected`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };

  return (
    <Box sx={{ p: 3 }}>
      <ReusableSnackBar
        openSnackBar={openSnackBar}
        alertMsg={alertMsg}
        handleSnackBarClose={handleSnackBarClose}
        alertType={alertType}
        isLoading={isLoading}
      />

      {error && (
        <Typography color="error">{t("Error loading data")}</Typography>
      )}

      <Box
        sx={{
          position: isGridZoomed ? "fixed" : "relative",
          top: isGridZoomed ? 0 : "auto",
          left: isGridZoomed ? 0 : "auto",
          right: isGridZoomed ? 0 : "auto",
          bottom: isGridZoomed ? 0 : "auto",
          width: isGridZoomed ? "100vw" : "100%",
          height: isGridZoomed ? "100vh" : "auto",
          zIndex: isGridZoomed ? 1004 : 1,
          backgroundColor: isGridZoomed ? "white" : "transparent",
          padding: isGridZoomed ? "20px" : "0",
          display: "flex",
          flexDirection: "column",
          boxShadow: isGridZoomed ? "0px 0px 15px rgba(0, 0, 0, 0.2)" : "none",
          transition: "all 0.3s ease",
          borderRadius: "8px",
          border: "1px solid #e0e0e0",
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "8px 16px",
            backgroundColor: "#f5f5f5",
            borderRadius: "8px 8px 0 0",
          }}
        >
          <Typography
            gutterBottom
            sx={{ fontWeight: "bold", fontSize: "16px", mb: -2 }}
          >
            {t("List of General Ledgers")}
          </Typography>
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<Add />}
              onClick={handleAddRow}
              disabled={!isAddRowEnabled}
              sx={{ mb: 1 }}
            >
              {t("Add")}
            </Button>
            <Tooltip
              title={isGridZoomed ? "Exit Zoom" : "Zoom In"}
              sx={{ zIndex: "1009" }}
            >
              <IconButton
                onClick={toggleGridZoom}
                color="primary"
                sx={{
                  backgroundColor: "rgba(0, 0, 0, 0.05)",
                  "&:hover": {
                    backgroundColor: "rgba(0, 0, 0, 0.1)",
                  },
                }}
              >
                {isGridZoomed ? <CloseFullscreenIcon /> : <CropFreeIcon />}
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        <div style={{ height: "50vh", overflowY: "auto" }}>
          <ReusableDataTable
            isLoading={loading}
            rows={glRows}
            columns={columns}
            pageSize={10}
            tempheight={"50vh"}
            getRowIdValue={"id"}
            status_onRowSingleClick={true}
            callback_onRowSingleClick={handleRowClick}
            getRowClassName={(params) =>
              selectedRow?.id === params.row.id ? "Mui-selected" : ""
            }
          />
        </div>
      </Box>

      {openOrgData && (
        <Dialog
          fullWidth
          maxWidth={false}
          open={true}
          onClose={viewsClose}
          sx={{ display: "flex", justifyContent: "center" }}
          disableEscapeKeyDown
        >
          <Box sx={{ width: "600px !important" }}>
            <DialogTitle
              sx={{ backgroundColor: "#EAE9FF", marginBottom: ".5rem" }}
            >
              <DescriptionIcon
                style={{
                  height: "20px",
                  width: "20px",
                  marginBottom: "-5px",
                }}
              />
              <span>Select Company Code to Extend</span>
            </DialogTitle>
            <DialogContent sx={{ paddingBottom: ".5rem" }}>
              <Box
                display="flex"
                alignItems="center"
                sx={{ flex: 1, padding: "22px 0px", gap: "5px" }}
              >
                <Autocomplete
                  size="small"
                  multiple
                  fullWidth
                  options={[
                    selectAllOption,
                    ...companycodeExtendedTo?.[selectedRow?.id],
                  ]}
                  value={selectedExtendDropdownData?.[selectedRow?.id]}
                  getOptionLabel={getOptionLabel}
                  disableCloseOnSelect
                  isOptionEqualToValue={(option, value) =>
                    option.code === value.code
                  }
                  onChange={handleChangeExtendCompanycode}
                  renderOption={(props, option, { selected }) => (
                    <li {...props}>
                      <Checkbox checked={selected} sx={{ marginRight: 1 }} />
                      {getOptionLabel(option)}
                    </li>
                  )}
                  renderTags={(tagValue, getTagProps) =>
                    tagValue.map((option, index) => {
                      const { key, ...tagProps } = getTagProps({ index });
                      return (
                        <Chip
                          key={key}
                          label={`${option.code}`}
                          {...tagProps}
                        />
                      );
                    })
                  }
                  renderInput={(params) => <TextField {...params} />}
                />

                <Button
                  variant="contained"
                  size="small"
                  onClick={() => handleSelectAll()}
                >
                  Select all
                </Button>
              </Box>
            </DialogContent>
            <DialogActions>
              <Button
                onClick={() => {
                  setOpenOrgData(false),
                    handleCellEdit({
                      id: rowId,
                      field: "views",
                      value: selectedViews,
                    });
                }}
                variant="contained"
              >
                Ok
              </Button>
            </DialogActions>
          </Box>
        </Dialog>
      )}

      <Dialog
        open={missingFieldsDialogOpen}
        onClose={handleCloseDialog}
        aria-labelledby="missing-fields-dialog-title"
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle
          id="missing-fields-dialog-title"
          sx={{
            backgroundColor: "#fff3e0",
            color: "#e65100",
            display: "flex",
            alignItems: "center",
            gap: 1,
            fontWeight: "bold",
          }}
        >
          <WarningAmberIcon fontSize="medium" />
          {t("Missing Mandatory Fields")}
        </DialogTitle>

        <DialogContent sx={{ pt: 2 }}>
          <Typography variant="body1" gutterBottom>
            {t("Please complete the following mandatory fields:")}
          </Typography>
          <List dense>
            {missingFields.map((field, index) => (
              <ListItem key={index} disablePadding>
                <ListItemIcon sx={{ minWidth: 30 }}>
                  <WarningAmberIcon fontSize="small" color="warning" />
                </ListItemIcon>
                <ListItemText primary={field} />
              </ListItem>
            ))}
          </List>
        </DialogContent>

        <DialogActions sx={{ pr: 3, pb: 2 }}>
          <Button
            onClick={handleCloseDialog}
            variant="contained"
            color="warning"
            sx={{ textTransform: "none", fontWeight: 500 }}
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {selectedRow &&
        (reqBench === "true" && selectedRowId ? (
          <Box
            sx={{
              position: isTabsZoomed ? "fixed" : "relative",
              top: isTabsZoomed ? 0 : "auto",
              left: isTabsZoomed ? 0 : "auto",
              right: isTabsZoomed ? 0 : "auto",
              bottom: isTabsZoomed ? 0 : "auto",
              width: isTabsZoomed ? "100vw" : "100%",
              height: isTabsZoomed ? "100vh" : "auto",
              zIndex: isTabsZoomed ? 1004 : 1,
              backgroundColor: isTabsZoomed ? "white" : "transparent",
              padding: isTabsZoomed ? "20px" : "0",
              marginTop: "20px",
              display: "flex",
              flexDirection: "column",
              boxShadow: isTabsZoomed
                ? "0px 0px 15px rgba(0, 0, 0, 0.2)"
                : "none",
              transition: "all 0.3s ease",
              borderRadius: "8px",
              border: "1px solid #e0e0e0",
            }}
          >
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                padding: "8px 16px",
                borderRadius: "8px 8px 0 0",
              }}
            >
              <Typography variant="h6">{t("View Details")}</Typography>
              <Tooltip
                title={isTabsZoomed ? "Exit Zoom" : "Zoom In"}
                sx={{ zIndex: "1009" }}
              >
                <IconButton
                  onClick={toggleTabsZoom}
                  color="primary"
                  sx={{
                    backgroundColor: "rgba(0, 0, 0, 0.05)",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.1)",
                    },
                  }}
                >
                  {isTabsZoomed ? <CloseFullscreenIcon /> : <CropFreeIcon />}
                </IconButton>
              </Tooltip>
            </Box>
            <Box sx={{ mt: 3 }}>
              <Tabs
                value={selectedTab}
                onChange={handleTabChange}
                indicatorColor="primary"
                textColor="primary"
                aria-label="Request tabs"
                variant="scrollable"
                scrollButtons="auto"
                sx={{ borderBottom: 1, borderColor: "divider", mb: 2 }}
              >
                {generalLedgerTabs.map((tab, index) => (
                  <Tab key={index} label={tab.tab} />
                ))}
              </Tabs>
              <Paper elevation={2} sx={{ p: 3, borderRadius: 4 }}>
                {generalLedgerTabs[selectedTab] && (
                  <GenericTabsGlobal
                    disabled={false}
                    basicDataTabDetails={generalLedgerTabs[selectedTab].data}
                    dropDownData={{
                      CompanyCode: allDropDownData?.["accountType"],
                      Country: dropdownDataCountry,
                      Accounttype: dropdownDataAccountType,
                      AccountGroup: allDropDownData?.["accountType"],
                      Segment: dropdownDataSegment,
                      Language: dropdownDataLanguage,
                      Template: dropdownDataFormPlanning,
                      COArea: dropdownDataCOA,
                      TaxJurisdiction: dropdownDataTaxJur,
                    }}
                    activeViewTab={generalLedgerTabs[selectedTab].tab}
                    uniqueId={selectedRow?.id || selectedRowId || glRows[0]?.id}
                    selectedRow={selectedRow || {}}
                    module={"GeneralLedger"}
                  />
                )}
              </Paper>
            </Box>
          </Box>
        ) : (
          <Box
            sx={{
              position: isTabsZoomed ? "fixed" : "relative",
              top: isTabsZoomed ? 0 : "auto",
              left: isTabsZoomed ? 0 : "auto",
              right: isTabsZoomed ? 0 : "auto",
              bottom: isTabsZoomed ? 0 : "auto",
              width: isTabsZoomed ? "100vw" : "100%",
              height: isTabsZoomed ? "100vh" : "auto",
              zIndex: isTabsZoomed ? 1004 : 1,
              backgroundColor: isTabsZoomed ? "white" : "transparent",
              padding: isTabsZoomed ? "20px" : "0",
              marginTop: "20px",
              display: "flex",
              flexDirection: "column",
              boxShadow: isTabsZoomed
                ? "0px 0px 15px rgba(0, 0, 0, 0.2)"
                : "none",
              transition: "all 0.3s ease",
              borderRadius: "8px",
              border: "1px solid #e0e0e0",
            }}
          >
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                padding: "8px 16px",
                borderRadius: "8px 8px 0 0",
              }}
            >
              <Typography variant="h6">{t("View Details")}</Typography>
              <Tooltip
                title={isTabsZoomed ? "Exit Zoom" : "Zoom In"}
                sx={{ zIndex: "1009" }}
              >
                <IconButton
                  onClick={toggleTabsZoom}
                  color="primary"
                  sx={{
                    backgroundColor: "rgba(0, 0, 0, 0.05)",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.1)",
                    },
                  }}
                >
                  {isTabsZoomed ? <CloseFullscreenIcon /> : <CropFreeIcon />}
                </IconButton>
              </Tooltip>
            </Box>
            <Box sx={{ flexGrow: 1, display: "flex", flexDirection: "column" }}>
              <Tabs
                value={selectedTab}
                onChange={handleTabChange}
                indicatorColor="primary"
                textColor="primary"
                aria-label="Request tabs"
                variant="scrollable"
                scrollButtons="auto"
                sx={{ borderBottom: 1, borderColor: "divider", mb: 2 }}
              >
                {generalLedgerTabs.map((tab, index) => (
                  <Tab key={index} label={tab.tab} />
                ))}
              </Tabs>
              <Paper elevation={2} sx={{ p: 3, borderRadius: 4 }}>
                {generalLedgerTabs[selectedTab] && (
                  <GenericTabsGlobal
                    disabled={false}
                    basicDataTabDetails={generalLedgerTabs[selectedTab].data}
                    dropDownData={{
                      CompanyCode: dropdownDataCompany,
                      Country: dropdownDataCountry,
                      Segment: dropdownDataSegment,
                      Language: dropdownDataLanguage,
                      Template: dropdownDataFormPlanning,
                      COArea: dropdownDataCOA,
                      TaxJurisdiction: dropdownDataTaxJur,
                    }}
                    activeViewTab={generalLedgerTabs[selectedTab].tab}
                    uniqueId={selectedRow?.id || selectedRowId || glRows[0]?.id}
                    selectedRow={selectedRow || {}}
                    module={"GeneralLedger"}
                  />
                )}
              </Paper>
            </Box>
          </Box>
        ))}

      <BottomNavGlobal
        handleSaveAsDraft={handleSaveAsDraft}
        handleSubmitForReview={handleSubmitForReview}
        handleSubmitForApprove={handleSubmitForApprove}
        handleSendBack={handleSendBack}
        handleCorrection={handleCorrection}
        handleRejectAndCancel={handleRejectAndCancel}
        handleValidateAndSyndicate={handleValidateAndSyndicate}
        validateAllRows={validateAllRows}
        isSaveAsDraftEnabled={isSaveAsDraftEnabled}
        filteredButtons={filteredButtons}
        moduleName={"GeneralLedger"}
      />
      <ReusableBackDrop
        blurLoading={blurLoading}
        loaderMessage={loaderMessage}
      />
    </Box>
  );
};

export default RequestDetailsGL;
