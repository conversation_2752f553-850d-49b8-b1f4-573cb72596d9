import React, { useEffect, useState } from "react";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import {
  InfoOutlined,
  IosShare,
  Refresh,
  TuneOutlined,
} from "@mui/icons-material";
// import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import {
  Button,
  Checkbox,
  Grid,
  Paper,
  IconButton,
  Typography,
  TextField,
  Box,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Popper,
  BottomNavigation,
  ListItemText,
  InputLabel,
  tooltipClasses,
  Card,
  CardContent,
  OutlinedInput,
  Autocomplete,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  FormControlLabel,
  DialogActions,
  CardActions,
  FormControl,
  Divider,
  FormLabel,
  RadioGroup,
  Radio,
  Tabs,
  Tab,
} from "@mui/material";
// import moment from "moment/moment";
import { Stack } from "@mui/system";
// import Select from "@mui/material/Select";
// import { FormControl, MenuItem } from "@mui/material";
// import { useState, useEffect } from "react";
// import ForwardToInboxOutlinedIcon from "@mui/icons-material/ForwardToInboxOutlined";
// import CheckCircleOutlineOutlinedIcon from "@mui/icons-material/CheckCircleOutlineOutlined";
// import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
// import ReusableDialog from "../Common/ReusableDialog";
// import { ViewDetailsIcon } from "../Common/icons"
// import { useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
// import TrackChangesIcon from "@mui/icons-material/TrackChanges";
import styled from "@emotion/styled";
import html2canvas from "html2canvas";
// import {
//   commonFilterClear,
//   commonFilterUpdate,
// } from "../../app/commonFilterSlice";
// import FilterField from "../common/ReusableFilterBox/FilterField";
// import { setHistoryPath } from "../../app/utilitySlice";
import {
  container_filter,
  font_Small,
  iconButton_SpacingSmall,
  outerContainer_Information,
  outermostContainer,
  outermostContainer_Information,
} from "../commonStyles";
import ReusableFieldCatalog from "./ReusableFieldCatalog";
import { doAjax } from "../fetchService";
import { destination_MaterialMgmt } from "../../../destinationVariables";
import { ReusableDialog } from "../ReusablePromptBox/ReusablePromptBox";
import ReusableSnackBar from "../ReusableSnackBar";
import { useNavigate } from "react-router-dom";
import { clearTabsData } from "../../../app/tabsDetailsSlice";
import DisplayUnitsOfMeasureTab from "../../MasterDataCockpit/AdditionalDataTabs/DisplayUnitsOfMeasureTab";
import SearchBar from "../SearchBar";
import FieldSelectionForEdit from "./FieldSelectionForEdit";
// import {
//   DatePicker,
//   DesktopDatePicker,
//   LocalizationProvider,
// } from "@mui/x-date-pickers";
// import DateRange from "../Common/DateRangePicker";
// import { destination_MaterialMgmt } from "../../destinationVariables";
// import { doAjax } from "../Common/fetchService";
// import ClearIcon from '@mui/icons-material/Clear';
// import ReusableTable from "../common/ReusableTable";

const HtmlTooltip = styled(({ className, ...props }) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: "#f5f5f9",
    color: "rgba(0, 0, 0, 0.87)",
    maxWidth: 250,
    border: "1px solid #dadde9",
  },
}));
const exportAsPicture = () => {
  const html = document.getElementsByTagName("HTML")[0];
  const body = document.getElementsByTagName("BODY")[0];
  let htmlWidth = html.clientWidth;
  let bodyWidth = body.clientWidth;

  const data = document.getElementById("e-invoice-export"); //CHANGE THIS ID WITH ID OF OUTERMOST DIV CONTAINER
  const newWidth = data.scrollWidth - data.clientWidth;

  if (newWidth > data.clientWidth) {
    htmlWidth += newWidth;
    bodyWidth += newWidth;
  }

  html.style.width = htmlWidth + "px";
  body.style.width = bodyWidth + "px";

  html2canvas(data)
    .then((canvas) => {
      return canvas.toDataURL("image/png", 1.0);
    })
    .then((image) => {
      saveAs(image, "FieldCatalog.png"); //CHANGE THE NAME OF THE FILE
      html.style.width = null;
      body.style.width = null;
    });
};

const saveAs = (blob, fileName) => {
  const elem = window.document.createElement("a");
  elem.href = blob;
  elem.download = fileName;
  (document.body || document.documentElement).appendChild(elem);
  if (typeof elem.click === "function") {
    elem.click();
  } else {
    elem.target = "_blank";
    elem.dispatchEvent(
      new MouseEvent("click", {
        view: window,
        bubbles: true,
        cancelable: true,
      })
    );
  }
  URL.revokeObjectURL(elem.href);
  elem.remove();
};
const FieldSelection = ({}) => {
  const navigate = useNavigate();
  const [name, setName] = useState("");
  const [value, setValue] = useState(null);
  const [fieldSets, setFieldSets] = useState([]);
  const [groupedFieldSets, setGroupedFieldSets] = useState({});
  const [selectedFieldSet, setSelectedFieldSet] = useState(null);
  const [childCheckedStates, setChildCheckedStates] = useState({});
  const [DisabledChildCheck, setDisabledChildCheck] = useState({});
  const [childRadioValues, setChildRadioValues] = useState({});
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [messageDialogSeverity, setMessageDialogSeverity] = useState(false);
  const [messageDialogExtra, setMessageDialogExtra] = useState(false);
  const [messageDialogOK, setMessageDialogOK] = useState(true);
  const [successMsg, setsuccessMsg] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const additionalDataTabs = ["For Create", "For Change"];
  const dispatch = useDispatch();

  const handleMessageDialogClose = () => {
    navigate("/masterDataCockpit/materialMaster/createMaterialDetail");
  };
  const handleMessageDialogNavigate = () => {
    navigate("/masterDataCockpit/materialMaster/createMaterialDetail");
  };
  const handleClose = () => {
    setOpen(false);
  };
  const handleSnackBarClose = () => {
    setopenSnackbar(false);
    navigate("/masterDataCockpit/materialMaster/materialSingle");
  };
  const handleSnackBarOpen = () => {
    setopenSnackbar(true);
  };
  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };
  const extractRequiredFields = (data) => {
    // Implement the logic to extract the "Required" fields from your API response
    // For example, assuming your API response has an array of field objects
    return data.body
      .filter((field) => field.Required === "true")
      .map((field) => field.fieldName);
  };
  const getFieldCatalogDetails = () => {
    const hSuccess = (data) => {
      const fieldSets = [];
      const requiredFields = [];
      Object.keys(data.body).map((sectionName) => {
        const sectionData = data.body[sectionName];
        Object.keys(sectionData).map((subheading) => {
          const SUBHEAD = data.body[sectionName][subheading];
          if (Array.isArray(SUBHEAD)) {
            let dummy = {
              heading: subheading,
              fields: SUBHEAD.map((fieldData) => fieldData["fieldName"]),
              viewName: sectionName,
              fieldVisibility: SUBHEAD.map((fieldData) => {
                return {
                  fieldName: fieldData["fieldName"],
                  visibility: fieldData["visibility"],
                };
              }),
            };

            fieldSets.push(dummy);
            console.log(fieldSets, "hello");
            // Check if the field is required and add it to the requiredFields array
            SUBHEAD.forEach((fieldData) => {
              console.log("Field Name:", fieldData["fieldName"]);
              console.log("Is Required:", fieldData["Required"]);
              if (fieldData["Required"] === "true") {
                requiredFields.push(fieldData["fieldName"]);
              }
            });
          }
        });
      });
      // Set fieldSets first
      setFieldSets(fieldSets);
      console.log("Required Fields:", requiredFields);
      // Now, group fieldSets into groupedFieldSets based on viewName
      const groupedFieldSets = {};
      const visibilityResponse = {};
      const checkedRespone = {};
      fieldSets.forEach((fieldSet) => {
        const { heading, fields, viewName, fieldVisibility } = fieldSet;
        if (!groupedFieldSets[viewName]) {
          groupedFieldSets[viewName] = {
            heading: viewName,
            subheadings: [],
          };
        }
        groupedFieldSets[viewName].subheadings.push({
          heading,
          fields,
        });
        fieldVisibility.forEach((value) => {
          let visibility =
            value.visibility === "Required"
              ? "Mandatory"
              : value.visibility === "Hidden"
              ? "Hide"
              : value.visibility === "0"
              ? "0"
              : "Optional";

          visibilityResponse[value.fieldName] = visibility;

          if (value.visibility === "0") {
            checkedRespone[value.fieldName] = true;
          }
        });
      });
      // Set groupedFieldSets
      setGroupedFieldSets(groupedFieldSets);
      setChildRadioValues(visibilityResponse);
      setDisabledChildCheck(checkedRespone);
      setChildCheckedStates(checkedRespone);
      console.log(groupedFieldSets, "Fieldset");
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_MaterialMgmt}/data/getFieldCatalogueDetails?screenName=Create`,
      "get",
      hSuccess,
      hError
    );
  };
  useEffect(() => {
    getFieldCatalogDetails();
  }, []);
  const handleBottomNavigationSubmit = () => {
    // Initialize an empty payload
    console.log("Clicked");
    let payload = {};

    // Iterate through groupedFieldSets based on viewName
    Object.keys(groupedFieldSets).forEach((viewName) => {
      const subheadings = groupedFieldSets[viewName].subheadings;

      subheadings.forEach((subheading) => {
        const { heading, fields } = subheading;

        fields.forEach((field) => {
          if (childRadioValues[field] !== "0") {
            if (childCheckedStates[field]) {
              const visibility =
                childRadioValues[field] === "Mandatory"
                  ? "Required"
                  : childRadioValues[field] === "Hide"
                  ? "Hidden"
                  : "Optional";

              // Check if the field has already been added for the current viewName
              if (!payload[viewName]) {
                payload[viewName] = [];
              }

              // Check if the field is already in the payload for the current viewName
              const isFieldAdded = payload[viewName].some(
                (item) => item.fieldName === field
              );

              if (!isFieldAdded) {
                payload[viewName].push({
                  fieldName: field,
                  cardName: heading,
                  viewName: viewName,
                  visibility: visibility,
                  screenName: 'Create'
                });
              }
            }
          }
          // console.log(childRadioValues,"childCheckedStates")
        });
      });
    });

    const hSuccess = (data) => {
      console.log(data, "example");
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Submit");
        setMessageDialogMessage(
          `Field Catalog has been submitted successfully`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Submit");
        setsuccessMsg(false);
        setMessageDialogMessage("Submission Failed");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };

    const hError = (error) => {
      console.log(error);
    };

    // Iterate through the payload object and send each viewName's payload separately
    Object.keys(payload).forEach((viewName) => {
      const viewNamePayload = payload[viewName];

      if (viewNamePayload.length > 0) {
        doAjax(
          `/${destination_MaterialMgmt}/alter/changeVisibility`,
          "post",
          hSuccess,
          hError,
          viewNamePayload
        );
      } else {
        console.log(`No payload data to send for viewName: ${viewName}`);
      }
    });
    dispatch(clearTabsData());
  };

  const tabContents = [
    [
      <>
        <Grid container sx={container_filter}>
          <Grid item md={12}>
            {Object.keys(groupedFieldSets).map((viewName) => (
              <Accordion
                key={viewName}
                sx={{ mb: 2 }}
                className="filter-accordion"
              >
                <AccordionSummary sx={{ backgroundColor: "#f5f5f5" }}>
                  <Typography
                    sx={{ fontWeight: "700", margin: "0px !important" }}
                  >
                    {viewName}
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  {groupedFieldSets[viewName].subheadings.map(
                    (subheading, index) => (
                      <Accordion key={index} sx={{ mb: 2 }}>
                        <AccordionSummary
                          expandIcon={<ExpandMoreIcon />}
                          sx={{ backgroundColor: "#F1F0FF" }}
                        >
                          <Typography
                            sx={{
                              fontWeight: "700",
                              margin: "0px !important",
                              fontSize: "14px",
                            }}
                          >
                            {subheading.heading}
                          </Typography>
                        </AccordionSummary>

                        <AccordionDetails>
                          <div sx={{ fontSize: "25px" }}>
                            <ReusableFieldCatalog
                              fields={subheading.fields}
                              heading={subheading.heading}
                              childCheckedStates={childCheckedStates}
                              setChildCheckedStates={setChildCheckedStates}
                              childRadioValues={childRadioValues}
                              setChildRadioValues={setChildRadioValues}
                              onSubmitButtonClick={() =>
                                handleBottomNavigationSubmit()
                              }
                              mandatoryFields={fieldSets}
                              DisabledChildCheck={DisabledChildCheck}
                            />
                          </div>
                        </AccordionDetails>
                      </Accordion>
                    )
                  )}
                </AccordionDetails>
              </Accordion>
            ))}
          </Grid>
          <Paper
            sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
            elevation={2}
          >
            <BottomNavigation
              className="container_BottomNav"
              showLabels
              sx={{
                display: "flex",
                justifyContent: "flex-end",
              }}
              value={value}
              onChange={(event, newValue) => {
                // Update the selected field set based on the selected tab or field set
                setSelectedFieldSet(fieldSets[newValue]);
                setValue(newValue);
              }}
            >
              <Button
                size="small"
                variant="contained"
                onClick={handleBottomNavigationSubmit}
              >
                Submit
              </Button>
            </BottomNavigation>
          </Paper>
        </Grid>
      </>,
    ],
    [
      <>
        <FieldSelectionForEdit />
      </>,
    ],
  ];
  const handleChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredFields, setFilteredFields] = useState([]);

  function refreshPage() {
    // getFilter();
  }

  return (
    <div>
      <ReusableDialog
        dialogState={openMessageDialog}
        openReusableDialog={handleMessageDialogClickOpen}
        closeReusableDialog={handleMessageDialogClose}
        dialogTitle={messageDialogTitle}
        dialogMessage={messageDialogMessage}
        handleDialogConfirm={handleMessageDialogClose}
        dialogOkText={"OK"}
        handleExtraButton={handleMessageDialogNavigate}
        dialogSeverity={messageDialogSeverity}
      />
      {successMsg && (
        <ReusableSnackBar
          openSnackBar={openSnackbar}
          alertMsg={messageDialogMessage}
          handleSnackBarClose={handleSnackBarClose}
        />
      )}
      <div style={{ ...outermostContainer, backgroundColor: "#FAFCFF" }}>
        <Stack spacing={1}>
          {/* Information */}
          <Grid container sx={outermostContainer_Information}>
            <Grid item md={5} sx={outerContainer_Information}>
              <Typography variant="h3">
                <strong>Field Configurations</strong>
              </Typography>
              <Typography variant="body2" color="#777">
                This view displays the setiings for configuring the Fields
              </Typography>
            </Grid>
            <Grid item md={7} sx={{ display: "flex" }}>
              <Grid
                container
                direction="row"
                justifyContent="flex-end"
                alignItems="center"
                spacing={0}
              >
                <SearchBar
                  title="Search for fields in different views"
                  // handleSearchAction={getMaterialNoGlobalSearch}
                  module="FieldSelection"
                  keyName="string"
                  message={"Search for fields in different views"}
                  // clearSearchBar={clearSearchBar}
                />

                <Tooltip title="Reload">
                  <IconButton sx={iconButton_SpacingSmall}>
                    <Refresh
                      sx={{
                        "&:hover": {
                          transform: "rotate(360deg)",
                          transition: "0.9s",
                        },
                      }}
                      onClick={refreshPage}
                    />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Export">
                  <IconButton sx={iconButton_SpacingSmall}>
                    <IosShare onClick={exportAsPicture} />
                  </IconButton>
                </Tooltip>
              </Grid>
            </Grid>
          </Grid>
          <Paper>
            <Tabs
              value={activeTab}
              onChange={handleChange}
              variant="scrollable"
              sx={{
                background: "#FFF",
                borderBottom: "1px solid #BDBDBD",
                width: "100%",
              }}
              aria-label="mui tabs example"
            >
              {additionalDataTabs.map((factor, index) => (
                <Tab
                  sx={{ fontSize: "12px", fontWeight: "700" }}
                  key={index}
                  label={factor} // Set the label for each tab
                />
              ))}
            </Tabs>
          </Paper>

          {/* Display the content of the active tab */}
          {tabContents[activeTab].map((cardContent, index) => (
            <Box key={index}>{cardContent}</Box>
          ))}

          {/* <Paper
            sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
            elevation={2}
          >
            <BottomNavigation
              className="container_BottomNav"
              showLabels
              sx={{
                display: "flex",
                justifyContent: "flex-end",
              }}
              value={value}
              onChange={(event, newValue) => {
                // Update the selected field set based on the selected tab or field set
                setSelectedFieldSet(fieldSets[newValue]);
                setValue(newValue);
              }}
            >
              <Button
                size="small"
                variant="contained"
                onClick={handleBottomNavigationSubmit}
              >
                Submit
              </Button>
            </BottomNavigation>
          </Paper> */}
        </Stack>
      </div>
    </div>
  );
};

export default FieldSelection;
