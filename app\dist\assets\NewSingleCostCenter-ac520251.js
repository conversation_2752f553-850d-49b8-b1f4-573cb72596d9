import{s as Ht,q as p,r as i,cf as Lr,a as t,j as l,T as D,E as te,ch as J,x as $,G as m,b6 as qr,ay as Vr,at as Or,au as Mr,K as S,b$ as A,ai as V,b4 as X,B as M,F as H,u as Hr,b as jr,bW as zr,bh as Wr,ci as Jr,bX as Ur,C as _r,z as Ft,V as Et,aF as Rt,I as le,aG as It,W as Bt,ar as $t,X as Lt,t as B,cb as Kr,cc as Xr,al as qt,b1 as Gr,y as Vt,aD as Qr,aE as Yr,aB as K,bp as Zr,c3 as yr,c4 as Cr,c5 as Pr,c6 as eo,c7 as to,c8 as ro,bq as Ot}from"./index-75c1660a.js";import{d as oo}from"./ArrowCircleLeftOutlined-f7b52d40.js";import"./AutoCompleteType-e8a3df80.js";import"./dayjs.min-83c0b0e0.js";import{D as no}from"./DatePicker-31fef6b6.js";import{R as co}from"./ReusableAttachementAndComments-682b0475.js";import{l as Mt}from"./lookup-1dcf10ac.js";import{S as so,a as io,b as ao}from"./Stepper-2dbfb76b.js";import"./useChangeLogUpdate-7dd0a930.js";import"./dateViewRenderers-dbe02df3.js";import"./useSlotProps-da724f1f.js";import"./InputAdornment-a22e1655.js";import"./CSSTransition-8d766865.js";import"./useMediaQuery-33e0a836.js";import"./DesktopDatePicker-47a97548.js";import"./useMobilePicker-056b38fc.js";import"./CloudUpload-d5d09566.js";import"./utilityImages-067c3dc2.js";import"./Add-62a207fb.js";import"./Delete-1d158507.js";function G(r){var Q,F,U,k;const h=Ht();var x=r.field.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").replaceAll("%","").split(" ").join("");let n=p(u=>u.costCenter.errorFields);const a=p(u=>u.costCenter.singleCCPayload);console.log("valuesfrompayload",a.Description);const f=u=>{const b=g=>{console.log("value",g),h(V({keyName:"Currency",data:g.body}))},E=g=>{console.log(g,"error in dojax")};S(`/${A}/data/getCurrency?companyCode=${u==null?void 0:u.code}`,"get",b,E)},w=u=>{const b=g=>{console.log("value",g),h(V({keyName:"Region",data:g.body}))},E=g=>{console.log(g,"error in dojax")};S(`/${A}/data/getRegionBasedOnCountry?country=${u==null?void 0:u.code}`,"get",b,E)};i.useEffect(()=>{var u,b;(((u=r==null?void 0:r.field)==null?void 0:u.visibility)==="0"||((b=r==null?void 0:r.field)==null?void 0:b.visibility)==="Required")&&h(Lr(x))},[]),console.log("test",Object.keys(a).length);const N=p(u=>u.AllDropDown.dropDown);if(((Q=r.field)==null?void 0:Q.fieldType)==="Input")return t(m,{item:!0,md:2,children:r.field.visibility==="Hidden"?null:l($,{children:[l(D,{variant:"body2",color:"#777",children:[r.field.fieldName,r.field.visibility==="Required"||r.field.visibility==="0"?t("span",{style:{color:"red"},children:"*"}):""]}),t(te,{size:"small",type:r.field.dataType==="QUAN"?"number":"",placeholder:`Enter ${r.field.fieldName}`,inputProps:{maxLength:r.field.maxLength},value:a[x],onChange:(u,b)=>{const E=u.target.value;Object.keys(a).length>0?(console.log("0"),E.length>0&&E[0]===" "?(console.log("1"),h(J({keyName:x,data:E.trimStart()}))):(console.log("2"),h(J({keyName:x,data:E.toUpperCase()})))):(console.log("3"),h(J({keyName:x,data:E.trimStart()})))},required:r.field.visibility==="Required"||r.field.visibility==="0",error:n.includes(x)})]})});if(((F=r.field)==null?void 0:F.fieldType)==="Drop Down")return t(m,{item:!0,md:2,children:r.field.visibility==="Hidden"?null:l($,{children:[l(D,{variant:"body2",color:"#777",children:[r.field.fieldName,r.field.visibility==="Required"||r.field.visibility==="0"?t("span",{style:{color:"red"},children:"*"}):""]}),t(qr,{sx:{height:"31px"},fullWidth:!0,size:"small",value:a[x],onChange:(u,b)=>{r.field.fieldName==="Company Code"&&f(b),r.field.fieldName==="Country/Reg"&&w(b),h(J({keyName:x,data:b}))},options:N[x]??[],required:r.field.visibility==="0"||r.field.visibility==="Required",getOptionLabel:u=>`${u==null?void 0:u.code} - ${u==null?void 0:u.desc}`,renderOption:(u,b)=>t("li",{...u,children:l(D,{style:{fontSize:12},children:[b==null?void 0:b.code," - ",b==null?void 0:b.desc]})}),renderInput:u=>t(te,{...u,variant:"outlined",placeholder:`Select ${r.field.fieldName}`,error:n.includes(x)})})]})});if(((U=r.field)==null?void 0:U.fieldType)==="Radio Button")return l(m,{item:!0,md:2,children:[l(D,{variant:"body2",color:"#777",children:[r.field.fieldName,r.field.visibility==="Required"||r.field.visibility==="0"?t("span",{style:{color:"red"},children:"*"}):""]}),t(Vr,{sx:{padding:0},checked:a[x]==!0,onChange:u=>{h(J({keyName:x,data:u.target.checked}))}})]});if(((k=r.field)==null?void 0:k.fieldType)==="Calendar")return t(m,{item:!0,md:2,children:l($,{children:[l(D,{variant:"body2",color:"#777",children:[r.field.fieldName,r.field.visibility==="Required"||r.field.visibility==="0"?t("span",{style:{color:"red"},children:"*"}):""]}),t(Or,{dateAdapter:Mr,children:t(no,{slotProps:{textField:{size:"small"}},value:a[x],onChange:u=>h(J({keyName:x,data:"/Date("+Date.parse(u)+")/"})),required:r.field.visibility==="0"||r.field.visibility==="Required"})})]})})}const lo=r=>{let h=Object==null?void 0:Object.entries(r==null?void 0:r.basicDataTabDetails);const[x,n]=i.useState([]);return i.useEffect(()=>{n(h==null?void 0:h.map(a=>l(m,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...X},children:[t(m,{container:!0,children:t(D,{sx:{fontSize:"12px",fontWeight:"700"},children:a[0]})}),t(M,{children:t(m,{container:!0,spacing:1,children:[...a[1]].filter(f=>f.visibility!="Hidden").sort((f,w)=>f.sequenceNo-w.sequenceNo).map(f=>t(G,{field:f,dropDownData:r.dropDownData}))})})]})))},[r==null?void 0:r.basicDataTabDetails]),t(H,{children:x})},uo=r=>{let h=Object==null?void 0:Object.entries(r==null?void 0:r.controlTabDetails);console.log("basic",h);const[x,n]=i.useState([]);return i.useEffect(()=>{n(h==null?void 0:h.map(a=>l(m,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...X},children:[t(m,{container:!0,children:t(D,{sx:{fontSize:"12px",fontWeight:"700"},children:a[0]})}),t(M,{children:t(m,{container:!0,spacing:1,children:[...a[1]].filter(f=>f.visibility!="Hidden").sort((f,w)=>f.sequenceNo-w.sequenceNo).map(f=>t(G,{field:f,dropDownData:r.dropDownData}))})})]})))},[r==null?void 0:r.basicDataTabDetails]),t(H,{children:x})},mo=r=>{let h=Object==null?void 0:Object.entries(r==null?void 0:r.templatesTabDetails);console.log("basic",h);const[x,n]=i.useState([]);return i.useEffect(()=>{n(h==null?void 0:h.map(a=>l(m,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...X},children:[t(m,{container:!0,children:t(D,{sx:{fontSize:"12px",fontWeight:"700"},children:a[0]})}),t(M,{children:t(m,{container:!0,spacing:1,children:[...a[1]].filter(f=>f.visibility!="Hidden").sort((f,w)=>f.sequenceNo-w.sequenceNo).map(f=>t(G,{field:f,dropDownData:r.dropDownData}))})})]})))},[r==null?void 0:r.templatesTabDetails]),t(H,{children:x})},ho=r=>{let h=Object==null?void 0:Object.entries(r==null?void 0:r.addressTabDetails);console.log("basic",h);const[x,n]=i.useState([]);return i.useEffect(()=>{n(h==null?void 0:h.map(a=>l(m,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...X},children:[t(m,{container:!0,children:t(D,{sx:{fontSize:"12px",fontWeight:"700"},children:a[0]})}),t(M,{children:t(m,{container:!0,spacing:1,children:[...a[1]].filter(f=>f.visibility!="Hidden").sort((f,w)=>f.sequenceNo-w.sequenceNo).map(f=>t(G,{field:f,dropDownData:r.dropDownData}))})})]})))},[r==null?void 0:r.addressTabDetails]),t(H,{children:x})},fo=r=>{let h=Object==null?void 0:Object.entries(r==null?void 0:r.communicationTabDetails);console.log("basic",h);const[x,n]=i.useState([]);return i.useEffect(()=>{n(h==null?void 0:h.map(a=>l(m,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...X},children:[t(m,{container:!0,children:t(D,{sx:{fontSize:"12px",fontWeight:"700"},children:a[0]})}),t(M,{children:t(m,{container:!0,spacing:1,children:[...a[1]].filter(f=>f.visibility!="Hidden").sort((f,w)=>f.sequenceNo-w.sequenceNo).map(f=>t(G,{field:f,dropDownData:r.dropDownData}))})})]})))},[r==null?void 0:r.communicationTabDetails]),t(H,{children:x})},Oo=()=>{var pe,we,Ne,ke,Fe,Ee,Re,Ie,Be,$e,Le,qe,Ve,Oe,Me,He,je,ze,We,Je,Ue,_e,Ke,Xe,Ge,Qe,Ye,Ze,ye,Ce,Pe,et,tt,rt,ot,nt,ct,st,it,at,dt,lt,ut,mt,ht,ft,bt,xt,Dt,St,At,gt,Tt,vt,pt,wt;i.useState(0);const[r,h]=i.useState({}),n=Hr().state,a=Ht(),f=jr(),[w,N]=i.useState(!1),[Q,F]=i.useState(!1),[U,k]=i.useState(""),[u,b]=i.useState(!1),[E,g]=i.useState(!0),[bo,O]=i.useState(!1),[jt,j]=i.useState(!1),[zt,Y]=i.useState(!1),[Wt,re]=i.useState(!1),[Jt,ue]=i.useState(!1),[Ut,oe]=i.useState(!1),[_t,Kt]=i.useState(!1),[Xt,ne]=i.useState(!0),[Z,me]=i.useState(""),[ce,Gt]=i.useState("");i.useState("");const[he,L]=i.useState(!1),[Qt,Yt]=i.useState(""),[y,Zt]=i.useState([]),[yt,fe]=i.useState(!1),[Ct,se]=i.useState(!0),[Pt,be]=i.useState(!1),z=p(o=>o.appSettings),e=p(o=>o.costCenter.singleCCPayload),er=p(o=>o.costCenter.costCenterBasicData),tr=p(o=>o.costCenter.costCenterControl),rr=p(o=>o.costCenter.costCenterTemplate),or=p(o=>o.costCenter.costCenterAddress),nr=p(o=>o.costCenter.costCenterCommunication);p(o=>o.costCenter.costCenterHistory);const cr=p(o=>o.costCenter.requiredFields);console.log("controlTabDetails",e.RecordQuantity);let I=p(o=>o.userManagement.userData);const[C,xe]=i.useState(0),sr=p(o=>o.costCenter.singleCCPayload),De=["BASIC DATA","CONTROL","TEMPLATES","ADDRESS","COMMUNICATION","ATTACHMENTS & COMMENTS"],ir=()=>{Te()?xe(s=>s+1):ge()},ar=()=>{ne(!0),Te()?xe(s=>s-1):ge()},P=()=>{se(!0),Kt(!1)},dr=()=>{let o="Basic Data";const s=d=>{a(yr(d.body))},c=d=>{console.log(d)};S(`/${A}/data/getViewFieldDetails?viewName=${o}`,"get",s,c)},lr=()=>{let o="Control";const s=d=>{a(Cr(d.body))},c=d=>{console.log(d)};S(`/${A}/data/getViewFieldDetails?viewName=${o}`,"get",s,c)},ur=()=>{let o="Templates";const s=d=>{a(Pr(d.body))},c=d=>{console.log(d)};S(`/${A}/data/getViewFieldDetails?viewName=${o}`,"get",s,c)},mr=()=>{let o="Address";const s=d=>{a(eo(d.body))},c=d=>{console.log(d)};S(`/${A}/data/getViewFieldDetails?viewName=${o}`,"get",s,c)},hr=()=>{let o="Communication";const s=d=>{a(to(d.body))},c=d=>{console.log(d)};S(`/${A}/data/getViewFieldDetails?viewName=${o}`,"get",s,c)},fr=()=>{let o="History";const s=d=>{a(ro(d.body))},c=d=>{console.log(d)};S(`/${A}/data/getViewFieldDetails?viewName=${o}`,"get",s,c)},br=()=>{const o=c=>{a(V({keyName:"UserResponsible",data:c.body}))},s=c=>{console.log(c)};S(`/${A}/data/getUserResponsible`,"get",o,s)},xr=()=>{const o=c=>{a(V({keyName:"CostCenterCategory",data:c.body}))},s=c=>{console.log(c)};S(`/${A}/data/getCostCenterCategory`,"get",o,s)},Dr=()=>{const o=c=>{a(V({keyName:"CostCenter",data:c.body}))},s=c=>{console.log(c)};S(`/${A}/data/getCostCenter`,"get",o,s)},Se=(o,s)=>{const c=o.target.value;if(c.length>0&&c[0]===" ")me(c.trimStart());else{let d=c.toUpperCase();me(d)}};var ie={CostCenterHeaderID:"",ControllingArea:(we=(pe=n==null?void 0:n.controllingAreaData)==null?void 0:pe.newControllingArea)!=null&&we.code?(ke=(Ne=n==null?void 0:n.controllingAreaData)==null?void 0:Ne.newControllingArea)==null?void 0:ke.code:"",Testrun:Ct,Action:"I",Remarks:Z||"",ReqCreatedBy:I==null?void 0:I.user_id,ReqCreatedOn:"",RequestStatus:"",CreationId:"",EditId:"",DeleteId:"",MassCreationId:"",MassEditId:"",MassDeleteId:"",RequestType:"Create",MassRequestStatus:"",Toitem:[{CostCenterID:"",Costcenter:`${(Fe=n==null?void 0:n.companyCode)==null?void 0:Fe.newCompanyCode.code}${(Ee=n==null?void 0:n.costCenterName)==null?void 0:Ee.newCostCenterName}`?`${(Re=n==null?void 0:n.companyCode)==null?void 0:Re.newCompanyCode.code}${(Ie=n==null?void 0:n.costCenterName)==null?void 0:Ie.newCostCenterName}`:"",ValidFrom:(Be=n==null?void 0:n.validFromDate)!=null&&Be.newValidFromDate?"/Date("+Date.parse(($e=n==null?void 0:n.validFromDate)==null?void 0:$e.newValidFromDate)+")/":"",ValidTo:(Le=n==null?void 0:n.validToDate)!=null&&Le.newValidToDate?"/Date("+Date.parse((qe=n==null?void 0:n.validToDate)==null?void 0:qe.newValidToDate)+")/":"",PersonInCharge:e!=null&&e.PersonResponsible?e==null?void 0:e.PersonResponsible:"",CostcenterType:(Ve=e==null?void 0:e.CostCenterCategory)!=null&&Ve.code?(Oe=e==null?void 0:e.CostCenterCategory)==null?void 0:Oe.code:"",CostctrHierGrp:(Me=e==null?void 0:e.HierarchyArea)!=null&&Me.code?(He=e==null?void 0:e.HierarchyArea)==null?void 0:He.code:"",BusArea:(je=e==null?void 0:e.BusinessArea)!=null&&je.code?(ze=e==null?void 0:e.BusinessArea)==null?void 0:ze.code:"",CompCode:(We=e==null?void 0:e.CompanyCode)!=null&&We.code?(Je=e==null?void 0:e.CompanyCode)==null?void 0:Je.code:"",Currency:(Ue=e==null?void 0:e.Currency)!=null&&Ue.code?(_e=e==null?void 0:e.Currency)==null?void 0:_e.code:"",ProfitCtr:(Ke=e==null?void 0:e.ProfitCenter)!=null&&Ke.code?(Xe=e==null?void 0:e.ProfitCenter)==null?void 0:Xe.code:"",Name:e!=null&&e.Name?e==null?void 0:e.Name:"",Descript:e!=null&&e.Description?e==null?void 0:e.Description:"",PersonInChargeUser:(Ge=e==null?void 0:e.UserResponsible)!=null&&Ge.code?(Qe=e==null?void 0:e.UserResponsible)==null?void 0:Qe.code:"",RecordQuantity:(e==null?void 0:e.RecordQuantity)===!0?"X":"",LockIndActualPrimaryCosts:(e==null?void 0:e.ActualPrimaryCosts)===!0?"X":"",LockIndPlanPrimaryCosts:(e==null?void 0:e.PlanPrimaryCosts)===!0?"X":"",LockIndActSecondaryCosts:(e==null?void 0:e.ActsecondaryCosts)===!0?"X":"",LockIndPlanSecondaryCosts:(e==null?void 0:e.PlanSecondaryCosts)===!0?"X":"",LockIndActualRevenues:(e==null?void 0:e.ActualRevenue)===!0?"X":"",LockIndPlanRevenues:(e==null?void 0:e.PlanRevenue)===!0?"X":"",LockIndCommitmentUpdate:(e==null?void 0:e.CommitmentUpdate)===!0?"X":"",ConditionTableUsage:"",Application:"",CstgSheet:(Ye=e==null?void 0:e.CostingSheet)!=null&&Ye.code?(Ze=e==null?void 0:e.CostingSheet)==null?void 0:Ze.code:"",ActyIndepTemplate:(ye=e==null?void 0:e.ActyIndepFromPlngTemp)!=null&&ye.code?(Ce=e==null?void 0:e.ActyIndepFromPlngTemp)==null?void 0:Ce.code:"",ActyDepTemplate:(Pe=e==null?void 0:e.ActyDepFromPlngTemp)!=null&&Pe.code?(et=e==null?void 0:e.ActyDepFromPlngTemp)==null?void 0:et.code:"",AddrTitle:e!=null&&e.Title?e==null?void 0:e.Title:"",AddrName1:e!=null&&e.Name1?e==null?void 0:e.Name1:"",AddrName2:e!=null&&e.Name2?e==null?void 0:e.Name2:"",AddrName3:e!=null&&e.Name3?e==null?void 0:e.Name3:"",AddrName4:e!=null&&e.Name4?e==null?void 0:e.Name4:"",AddrStreet:e!=null&&e.Street?e==null?void 0:e.Street:"",AddrCity:e!=null&&e.Location?e==null?void 0:e.Location:"",AddrDistrict:e!=null&&e.District?e==null?void 0:e.District:"",AddrCountry:(tt=e==null?void 0:e.CountryReg)!=null&&tt.code?(rt=e==null?void 0:e.CountryReg)==null?void 0:rt.code:"",AddrCountryIso:"",AddrTaxjurcode:(ot=e==null?void 0:e.Jurisdiction)!=null&&ot.code?(nt=e==null?void 0:e.Jurisdiction)==null?void 0:nt.code:"",AddrPoBox:e!=null&&e.POBox?e==null?void 0:e.POBox:"",AddrPostlCode:e!=null&&e.PostalCode?e==null?void 0:e.PostalCode:"",AddrPobxPcd:e!=null&&e.POBoxPostCod?e==null?void 0:e.POBoxPostCod:"",AddrRegion:(ct=e==null?void 0:e.Region)!=null&&ct.code?(st=e==null?void 0:e.Region)==null?void 0:st.code:"",TelcoLangu:"",TelcoLanguIso:(it=e==null?void 0:e.LanguageKey)!=null&&it.code?(at=e==null?void 0:e.LanguageKey)==null?void 0:at.code:"",TelcoTelephone:e!=null&&e.Telephone1?e==null?void 0:e.Telephone1:"",TelcoTelephone2:e!=null&&e.Telephone2?e==null?void 0:e.Telephone2:"",TelcoTelebox:e!=null&&e.TeleboxNumber?e==null?void 0:e.TeleboxNumber:"",TelcoTelex:e!=null&&e.TelexNumber?e==null?void 0:e.TelexNumber:"",TelcoFaxNumber:e!=null&&e.FaxNumber?e==null?void 0:e.FaxNumber:"",TelcoTeletex:e!=null&&e.TeletexNumber?e==null?void 0:e.TeletexNumber:"",TelcoPrinter:e!=null&&e.PrinterDestination?e==null?void 0:e.PrinterDestination:"",TelcoDataLine:e!=null&&e.DataLine?e==null?void 0:e.DataLine:"",ActyDepTemplateAllocCc:(dt=e==null?void 0:e.ActyDepAllocTemp)!=null&&dt.code?(lt=e==null?void 0:e.ActyDepAllocTemp)==null?void 0:lt.code:"",ActyDepTemplateSk:(ut=e==null?void 0:e.TempActStatKeyFigure)!=null&&ut.code?(mt=e==null?void 0:e.TempActStatKeyFigure)==null?void 0:mt.code:"",ActyIndepTemplateAllocCc:(ht=e==null?void 0:e.ActyIndepAllocTemp)!=null&&ht.code?(ft=e==null?void 0:e.ActyIndepAllocTemp)==null?void 0:ft.code:"",ActyIndepTemplateSk:(bt=e==null?void 0:e.TempActStatKeyFigure)!=null&&bt.code?(xt=e==null?void 0:e.TempActStatKeyFigure)==null?void 0:xt.code:"",AvcActive:!1,AvcProfile:"",BudgetCarryingCostCtr:"",CurrencyIso:"",Department:e!=null&&e.Department?e==null?void 0:e.Department:"",FuncArea:(Dt=e==null?void 0:e.FunctionalArea)!=null&&Dt.code?(St=e==null?void 0:e.FunctionalArea)==null?void 0:St.code:"",FuncAreaFixAssigned:"",FuncAreaLong:"",Fund:"",FundFixAssigned:"",GrantFixAssigned:"",GrantId:"",JvEquityTyp:"",JvJibcl:"",JvJibsa:"",JvOtype:"",JvRecInd:"",JvVenture:"",Logsystem:""}]};const[Ae,Sr]=i.useState(0),Ar=(o,s)=>{const c=T=>{a(V({keyName:o,data:T.body})),Sr(q=>q+1)},d=T=>{console.log(T)};S(`/${A}/data/${s}`,"get",c,d)},gr=()=>{var o,s;(s=(o=Mt)==null?void 0:o.costCenter)==null||s.map(c=>{Ar(c==null?void 0:c.keyName,c==null?void 0:c.endPoint)})},Tr=()=>{var o,s;Ae==((s=(o=Mt)==null?void 0:o.costCenter)==null?void 0:s.length)?j(!1):j(!0)};i.useEffect(()=>{Tr()},[Ae]),i.useEffect(()=>{gr(),dr(),lr(),ur(),mr(),hr(),br(),xr(),Dr(),fr(),vr(),pr(),a(zr())},[]),i.useEffect(()=>{Gt(Wr("CC"))},[]);const vr=()=>{var c,d;const o=T=>{a(V({keyName:"HierarchyArea",data:T.body}))},s=T=>{console.log(T)};S(`/${A}/data/getHierarchyArea?controllingArea=${(d=(c=n==null?void 0:n.controllingAreaData)==null?void 0:c.newControllingArea)==null?void 0:d.code}`,"get",o,s)},pr=()=>{var c,d;const o=T=>{a(V({keyName:"ProfitCenter",data:T.body}))},s=T=>{console.log(T)};S(`/${A}/data/getProfitCenterAsPerControllingArea?controllingArea=${(d=(c=n==null?void 0:n.controllingAreaData)==null?void 0:c.newControllingArea)==null?void 0:d.code}`,"get",o,s)},ae=()=>{oe(!0)},W=()=>{re(!0)},de=()=>{re(!1)},wr=()=>{Jt?(oe(!1),ue(!1)):(oe(!1),f("/masterDataCockpit/costCenter"))},ge=()=>{fe(!0)},Te=()=>Zr(sr,cr,Zt);i.useEffect(()=>{a(Jr(y))},[y]);const Nr=()=>{j(!0);const o=c=>{if(j(!1),c.statusCode===200){N("Create"),k(`Cost Center has been Submitted for review NCS${c.body}`),L(!1),b("success"),g(!1),F(!0),ae(),O(!0),P();const d={artifactId:ce,createdBy:I==null?void 0:I.emailId,artifactType:"CostCenter",requestId:`NCS${c==null?void 0:c.body}`},T=R=>{console.log("Second API success",R)},q=R=>{console.error("Second API error",R)};S(`/${Ot}/documentManagement/updateDocRequestId`,"post",T,q,d)}else N("Create"),F(!1),k("Creation Failed"),L(!1),b("danger"),g(!1),O(!0),W(),_();handleClose()},s=c=>{console.log(c)};S(`/${A}/alter/costCenterSubmitForReview`,"post",o,s,ie)},kr=()=>{b(!1),W(),N("Confirm"),k("Do You Want to Save as Draft ?"),L(!0),Yt("proceed")};console.log(he,"setHandleExtrabutton");const Fr=()=>{j(!0);const o=c=>{if(de(),j(!1),c.statusCode===200){console.log("success"),N("Create"),k(`Cost Center has been Saved with ID NCS${c.body}`),L(!1),b("success"),g(!1),F(!0),ae(),O(!0);const d={artifactId:ce,createdBy:I==null?void 0:I.emailId,artifactType:"CostCenter",requestId:`NCS${c==null?void 0:c.body}`},T=R=>{console.log("Second API success",R)},q=R=>{console.error("Second API error",R)};S(`/${Ot}/documentManagement/updateDocRequestId`,"post",T,q,d)}else N("Save"),F(!1),k("Failed Saving the Data"),L(!1),b("danger"),g(!1),O(!0),W();handleClose()},s=c=>{console.log(c)};S(`/${A}/alter/costCenterAsDraft`,"post",o,s,ie)},Er=()=>{var q,R;Y(!0);const o={coArea:((R=(q=n==null?void 0:n.controllingAreaData)==null?void 0:q.newControllingArea)==null?void 0:R.code)||"",name:e!=null&&e.Name?e==null?void 0:e.Name.toUpperCase():""},s=v=>{var ee,Nt,kt;v.statusCode===201?(N("Create"),N("Create"),k("All Data has been Validated. Cost Center can be Sent for Review"),L(!1),b("success"),g(!1),F(!0),ae(),O(!0),ue(!0),(o.coArea!==""||o.name!=="")&&S(`/${A}/alter/fetchCCDescriptionDupliChk`,"post",c,d,o)):(N("Error"),F(!1),k(`${(ee=v==null?void 0:v.body)!=null&&ee.message[0]?(Nt=v==null?void 0:v.body)==null?void 0:Nt.message[0]:(kt=v==null?void 0:v.body)==null?void 0:kt.value}`),L(!1),b("danger"),g(!1),O(!0),W(),Y(!1))},c=v=>{v.body.length===0||!v.body.some(ee=>ee.toUpperCase()===o.name)?(Y(!1),ne(!1)):(Y(!1),N("Duplicate Check"),F(!1),k("There is a direct match for the Cost Center name. Please change the name."),L(!1),b("danger"),g(!1),O(!0),W(),ne(!0))},d=v=>{console.log(v)},T=v=>{console.log(v)};S(`/${A}/alter/validateCostCenter`,"post",s,T,ie)},ve=()=>{_(),Nr()},Rr=()=>{se(!1),be(!0)},Ir=()=>{fe(!1)},Br=o=>{switch(o){case 0:return t(lo,{basicDataTabDetails:er,dropDownData:r});case 1:return t(uo,{controlTabDetails:tr,dropDownData:r});case 2:return t(mo,{templatesTabDetails:rr,dropDownData:r});case 3:return t(ho,{addressTabDetails:or,dropDownData:r});case 4:return t(fo,{communicationTabDetails:nr,dropDownData:r});case 5:return t(co,{title:"CostCenter",useMetaData:!1,artifactId:ce,artifactName:"CostCenter"});default:return"Unknown step"}},$r=()=>{re(!1)},_=()=>{se(!0),be(!1)};return t(H,{children:jt===!0?t(Ur,{}):l("div",{children:[t(_r,{dialogState:Wt,openReusableDialog:W,closeReusableDialog:de,dialogTitle:w,dialogMessage:U,handleDialogConfirm:de,dialogOkText:"OK",showExtraButton:he,showCancelButton:!0,dialogSeverity:u,handleDialogReject:$r,handleExtraText:Qt,handleExtraButton:Fr}),Q&&t(Ft,{openSnackBar:Ut,alertMsg:U,handleSnackBarClose:wr}),y.length!=0&&t(Ft,{openSnackBar:yt,alertMsg:"Please fill the following Field: "+y.join(", "),handleSnackBarClose:Ir}),l(Et,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:_t,onClose:P,children:[l(Rt,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(D,{variant:"h6",children:"Remarks"}),t(le,{sx:{width:"max-content"},onClick:P,children:t(It,{})})]}),t(Bt,{sx:{padding:".5rem 1rem"},children:t($,{children:t(M,{sx:{minWidth:400},children:t($t,{sx:{height:"auto"},fullWidth:!0,children:t(te,{sx:{backgroundColor:"#F5F5F5"},value:Z,onChange:Se,multiline:!0,placeholder:"Enter Remarks",inputProps:{maxLength:254}})})})})}),l(Lt,{sx:{display:"flex",justifyContent:"end"},children:[t(B,{sx:{width:"max-content",textTransform:"capitalize"},onClick:P,children:"Cancel"}),t(B,{className:"button_primary--normal",type:"save",onClick:ve,variant:"contained",children:"Submit"})]})]}),l(Et,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:Pt,onClose:_,children:[l(Rt,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(D,{variant:"h6",children:"Remarks"}),t(le,{sx:{width:"max-content"},onClick:_,children:t(It,{})})]}),t(Bt,{sx:{padding:".5rem 1rem"},children:t($,{children:t(M,{sx:{minWidth:400},children:t($t,{sx:{height:"auto"},fullWidth:!0,children:t(te,{sx:{backgroundColor:"#F5F5F5"},value:Z,onChange:Se,multiline:!0,placeholder:"Enter Remarks",inputProps:{maxLength:254}})})})})}),l(Lt,{sx:{display:"flex",justifyContent:"end"},children:[t(B,{sx:{width:"max-content",textTransform:"capitalize"},onClick:_,children:"Cancel"}),t(B,{className:"button_primary--normal",type:"save",onClick:ve,variant:"contained",children:"Submit"})]})]}),t(Kr,{sx:{color:"#fff",zIndex:o=>o.zIndex.drawer+1},open:zt,children:t(Xr,{color:"inherit"})}),t(m,{container:!0,style:{...qt,backgroundColor:"#FAFCFF"},children:l(m,{sx:{width:"inherit"},children:[t(m,{item:!0,md:7,style:{padding:"16px",display:"flex"},children:l(m,{item:!0,md:5,sx:{display:"flex"},children:[t(m,{children:t(le,{color:"primary","aria-label":"upload picture",component:"label",sx:Gr,children:t(oo,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{f("/masterDataCockpit/costCenter"),a(clearPayload()),a(clearOrgData())}})})}),l(m,{children:[t(D,{variant:"h3",children:t("strong",{children:"Create Cost Center"})}),t(D,{variant:"body2",color:"#777",children:"This view creates a new Cost Center"})]})]})}),t(m,{container:!0,style:{padding:"0 1rem 0 1rem"},children:l(m,{container:!0,sx:qt,children:[l(m,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:[l(m,{item:!0,md:10,sx:{marginLeft:"40px",marginBottom:"20px"},children:[t(m,{item:!0,sx:{paddingTop:"2px !important"},children:l($,{flexDirection:"row",children:[t("div",{style:{width:"12%"},children:t(D,{variant:"body2",color:"#777",children:"Cost Center"})}),l(D,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[": ",(At=n==null?void 0:n.companyCode)==null?void 0:At.newCompanyCode.code,(gt=n==null?void 0:n.costCenterName)==null?void 0:gt.newCostCenterName]})]})}),t(m,{item:!0,sx:{paddingTop:"2px !important"},children:l($,{flexDirection:"row",children:[t("div",{style:{width:"12%"},children:t(D,{variant:"body2",color:"#777",children:"Controlling Area"})}),l(D,{variant:"body2",fontWeight:"bold",children:[":"," ",(vt=(Tt=n==null?void 0:n.controllingAreaData)==null?void 0:Tt.newControllingArea)==null?void 0:vt.code]})]})})]}),l(m,{item:!0,md:2,sx:{marginLeft:"40px",marginBottom:"20px"},children:[t(m,{item:!0,sx:{paddingTop:"2px !important"},children:l($,{flexDirection:"row",children:[t("div",{style:{width:"50%"},children:t(D,{variant:"body2",color:"#777",children:"Valid From"})}),l(D,{variant:"body2",fontWeight:"bold",children:[":"," ",Vt((pt=n==null?void 0:n.validFromDate)==null?void 0:pt.newValidFromDate).format(z==null?void 0:z.dateFormat)]})]})}),t(m,{item:!0,sx:{paddingTop:"2px !important"},children:l($,{flexDirection:"row",children:[t("div",{style:{width:"50%"},children:t(D,{variant:"body2",color:"#777",children:"Valid To"})}),l(D,{variant:"body2",fontWeight:"bold",children:[":"," ",Vt((wt=n==null?void 0:n.validToDate)==null?void 0:wt.newValidToDate).format(z==null?void 0:z.dateFormat)]})]})})]})]}),t(m,{container:!0,children:t(so,{activeStep:C,sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},children:De.map((o,s)=>t(io,{children:t(ao,{sx:{fontWeight:"700"},children:o})},o))})}),t(m,{container:!0,children:Br(C)})]})})]})}),t(Qr,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:l(Yr,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t(B,{variant:"contained",size:"small",sx:{...K,mr:1},onClick:kr,children:"Save As Draft"}),t(B,{variant:"contained",size:"small",sx:{...K,mr:1},onClick:ar,disabled:C===0,children:"Back"}),C===De.length-1?l(H,{children:[t(B,{variant:"contained",size:"small",sx:{...K,mr:1},onClick:Er,children:"Validate"}),t(B,{variant:"contained",size:"small",sx:{...K,mr:1},onClick:Rr,disabled:Xt,children:"Submit For Review"})]}):t(B,{variant:"contained",size:"small",sx:{...K,mr:1},onClick:ir,children:"Next"})]})})]})})};export{Oo as default};
