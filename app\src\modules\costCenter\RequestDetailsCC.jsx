import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  destination_CostCenter_Mass,
  destination_ProfitCenter_Mass,
} from "../../destinationVariables";
import { v4 as uuidv4 } from "uuid";
import { doAjax } from "../../components/Common/fetchService";
import useCostCenterFieldConfig from "@hooks/useCostCenterFieldConfig";
import useButtonDTConfig from "@hooks/useButtonDTConfig";
import ReusableBackDrop from "../../components/Common/ReusableBackDrop";
import ReusableDataTable from "../../components/Common/ReusableTable";
import SingleSelectDropdown from "@components/Common/ui/dropdown/SingleSelectDropdown";
import { useLocation, useNavigate } from "react-router-dom";
import {
  TextField,
  IconButton,
  Box,
  Typography,
  Paper,
  Button,
  Tabs,
  Tab,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Checkbox,
  Tooltip,
  FormControl,
  RadioGroup,
  Radio,
  FormControlLabel,
  FormLabel,
  Grid,
} from "@mui/material";
import ReusableSnackBar from "@components/Common/ReusableSnackBar";
import TaskAltIcon from "@mui/icons-material/TaskAlt";
import WarningAmberIcon from "@mui/icons-material/WarningAmber";
import GenericTabsGlobal from "../../components/MasterDataCockpit/GenericTabsGlobal";
import { colors } from "@constant/colors";
import {
  setCCRows,
  setOpenDialog,
  updateModuleFieldDataCC,
} from "@app/costCenterTabsSlice";
import {
  createPayloadForCC,
  fetchCurrencyBasedOnCompCode,
} from "../../functions";
import BottomNavGlobal from "../../components/RequestBench/RequestPages/BottomNavGlobal";
import { setDependentDropdown } from "@app/dropDownDataSlice";
import {
  setSelectedRowIdCC,
  setValidatedStatus,
} from "@app/costCenterTabsSlice";
import { setDropDown } from "@app/dropDownDataSlice";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import CropFreeIcon from "@mui/icons-material/CropFree";
import CloseFullscreenIcon from "@mui/icons-material/CloseFullscreen";
import useLang from "@hooks/useLang";
const RequestDetailsCC = ({ reqBench, apiResponses }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { t } = useLang();
  let task = useSelector((state) => state?.userManagement.taskData);
  const selectedRowId = useSelector((state) => state.costCenter.selectedRowId);
  const costCenterTabs = useSelector((state) => {
    const tabs = state.costCenter.costCenterTabs || [];
    return tabs.filter((tab) => tab.tab !== "Initial Screen");
  });
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const isrequestId = queryParams.get("RequestId");
  const requestHeaderSlice = useSelector(
    (state) => state.request.requestHeader
  );
  const allDropDownData = useSelector(
    (state) => state.AllDropDown?.dropDown || {}
  );
  const { loading, error, fetchCostCenterFieldConfig } =
    useCostCenterFieldConfig();
  const ccRows = useSelector(
    (state) => state.costCenter.payload.rowsHeaderData
  );
  const [rows, setRows] = useState(ccRows || []);
  const reduxPayload = useSelector((state) => state.costCenter.payload);
  const dynamicData = useSelector((state) => state.payload.dynamicKeyValues);
  const filteredButtons = useSelector((state) => state.payload.filteredButtons);
  const [selectedRow, setSelectedRow] = useState(null);
  const [selectedTab, setSelectedTab] = useState(0);
  const [rowTabData, setRowTabData] = useState({});
  const [dropdownDataCompany, setDropdownDataCompany] = useState([]);
  const [dropdownDataTaxJur, setDropdownDataTaxJur] = useState([]);
  const [dropdownDataFormPlanning, setDropdownDataFormPlanning] = useState([]);
  const [dropdownDataCOA, setDropdownDataCOA] = useState([]);
  const [dropdownDataCountry, setDropdownDataCountry] = useState([]);
  const [selectedCountry, setSelectedCountry] = useState("");
  const [dropdownDataRegion, setDropdownDataRegion] = useState([]);
  const [dropdownDataSegment, setDropdownDataSegment] = useState([]);
  const [dropdownDataLanguage, setDropdownDataLanguage] = useState([]);
  const [missingFieldsDialogOpen, setMissingFieldsDialogOpen] = useState(false);
  const [missingFields, setMissingFields] = useState([]);
  const [isAddRowEnabled, setIsAddRowEnabled] = useState(false);
  const [isSaveAsDraftEnabled, setIsSaveAsDraftEnabled] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [openSnackBar, setOpenSnackBar] = useState(false);
  const [alertMsg, setAlertMsg] = useState("");
  const [alertType, setAlertType] = useState("success"); // 'success' or 'error'
  const [isLoading, setIsLoading] = useState(false);
  const [validatedRows, setValidatedRows] = useState({});
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const { getButtonsDisplayGlobal } = useButtonDTConfig();
  const [validateEnabled, setValidateEnabled] = useState(false);
  const [originalRowData, setOriginalRowData] = useState({});
  const [originalTabData, setOriginalTabData] = useState({});
  const [withReference, setWithReference] = useState("yes");
  const [newRowId, setNewRowId] = useState();
  const [isAddRowMode, setIsAddRowMode] = useState(false);
  const [selectedControllingAreaCode, setSelectedControllingAreaCode] =
    useState("");
  const [selectedCompanyCode, setSelectedCompanyCode] = useState("");
  const [selectedCostCenterCode, setSelectedCostCenterCode] = useState("");
  const [costCenterOptions, setCostCenterOptions] = useState([]);
  const [costcenterResponse, setCostcenterResponse] = useState([]);
  const rowsBodyData = useSelector(
    (state) => state.costCenter.payload?.rowsBodyData || {}
  );
  const [isGridZoomed, setIsGridZoomed] = useState(false);
  const [isTabsZoomed, setIsTabsZoomed] = useState(false);
  const validatedStatus = useSelector(
    (state) => state?.costCenter?.validatedRowsStatus
  );
  const openDialog = useSelector((state) => state.costCenter.isOpenDialog);
  useEffect(() => {
    if (!costCenterTabs?.length) {
      fetchCostCenterFieldConfig();
    }
  }, []);
  useEffect(() => {
    if (selectedControllingAreaCode && selectedCompanyCode) {
      fetchCostCenters();
    }
  }, [selectedControllingAreaCode, selectedCompanyCode]);
  const fetchCostCenters = () => {
    if (!selectedControllingAreaCode || !selectedCompanyCode) return;
    const payload = {
      controllingArea: selectedControllingAreaCode,
      companyCode: selectedCompanyCode,
      top: "100",
      skip: "0",
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCostCentersNo`,
      "post",
      (data) => {
        if (Array.isArray(data.body?.list)) {
          const costCenters = data.body.list
            .map((item) => item.code)
            .filter((code, i, self) => code && self.indexOf(code) === i);
          setCostCenterOptions(
            costCenters.map((code) => ({ code, desc: code }))
          );
        }
      },
      (err) => {
        console.error("Cost Center fetch failed", err);
      },
      payload
    );
  };
  const getCompanyCodeBasedOnControllingArea = (CA) => {
    const hSuccess = (data) => {
      setDropdownDataCompany(data.body);
      dispatch(setDropDown({ keyName: "CompCode", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCompanyCodeBasedOnControllingArea?controllingArea=${CA}&rolePrefix=ETP`,
      "get",
      hSuccess,
      hError
    );
  };
  useEffect(() => {
    if (
      ccRows.length >= 1 &&
      (ccRows[0]?.costCenterNumber ||
        ccRows[0]?.controllingArea ||
        ccRows[0]?.companyCode)
    ) {
      dispatch(setOpenDialog(false));
    }
  }, []);
  useEffect(() => {
    if (task?.ATTRIBUTE_1 || isrequestId) {
      getButtonsDisplayGlobal("ET CC", "MDG_DYN_BTN_DT", "v2");
    }
  }, [task]);
  const columns = [
    {
      field: "included",
      headerName: "Included",
      flex: 0.3,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => (
        <Checkbox
          checked={params.row.included}
          disabled={false}
          onChange={(e) =>
            handleRowInputChange(e.target.checked, params.row.id, "included")
          }
        />
      ),
    },
    {
      field: "lineNumber",
      headerName: "Sl No.",
      flex: 0.25,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        const rowIndex = ccRows.findIndex((row) => row.id === params.row.id);
        return <div>{rowIndex + 1}</div>;
      },
    },
    {
      field: "controllingArea",
      headerName: "Controlling Area",
      align: "left",
      headerAlign: "left",
      flex: 1,
      renderHeader: () => (
        <span>
          {t("Controlling Area")}
          {isFieldMandatory("controllingArea") && (
            <span style={{ color: "red" }}> *</span>
          )}
        </span>
      ),
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={allDropDownData?.["ControllingArea"] || []}
            value={params.row.controllingArea}
            onChange={(newValue) =>
              handleRowInputChange(newValue, params.row.id, "controllingArea")
            }
            placeholder={t("Select Controlling Area")}
            disabled={false}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "CompCode",
      headerName: "Company Code",
      align: "left",
      headerAlign: "left",
      flex: 1,
      renderHeader: () => (
        <span>
          {t("Company Code")}
          {isFieldMandatory("CompCode") && (
            <span style={{ color: "red" }}> *</span>
          )}
        </span>
      ),
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={allDropDownData?.CompCode || []}
            value={
              params.row.CompCode?.code
                ? params.row.CompCode?.code
                : params.row.CompCode
            }
            onChange={(newValue) =>
              handleRowInputChange(newValue, params.row.id, "CompCode")
            }
            placeholder={t("Select Company Code")}
            disabled={false}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "costCenterNumber",
      headerName: "Cost Center Number",
      align: "left",
      headerAlign: "left",
      flex: 1,
      renderHeader: () => (
        <span>
          {t("Cost Center Number")}
          {isFieldMandatory("costCenterNumber") && (
            <span style={{ color: "red" }}> *</span>
          )}
        </span>
      ),
      renderCell: (params) => {
        console.log("fdyugudgf", params.row);
        const costCenterValue = params.row.costCenterNumber || "";
        const CompCodePrefix =
          params.row.CompCode?.code || params.row.CompCode || "";
        const value = CompCodePrefix + costCenterValue;
        // const value = CompCode+params.row.costCenterNumber || "";
        const isInvalid = value.length > 0 && value.length < 10;

        const handleKeyDown = (e) => {
          // Allow navigation & control keys
          if (
            [
              "Backspace",
              "Delete",
              "Tab",
              "Escape",
              "Enter",
              "ArrowLeft",
              "ArrowRight",
            ].includes(e.key)
          ) {
            return;
          }

          // Block if not a digit
          if (!/^[a-zA-Z0-9]$/.test(e.key)) {
            e.preventDefault();
          }
          const input = e.target;
          const selectionStart = input.selectionStart;
          if (selectionStart < CompCodePrefix.length) {
            e.preventDefault();
            input.setSelectionRange(
              CompCodePrefix.length,
              CompCodePrefix.length
            ); // Move cursor after prefix
          }
        };

        const handlePaste = (e) => {
          const paste = e.clipboardData.getData("text");
          if (!/^\d+$/.test(paste)) {
            e.preventDefault(); // block paste if not all digits
          }
        };

        const handleChange = (e) => {
          let inputValue = e.target.value;

          // Ensure it starts with CompCode
          if (!inputValue.startsWith(CompCodePrefix)) return;

          // Extract user-entered value after CompCode
          let enteredValue = inputValue.slice(CompCodePrefix.length);

          // Capitalize letters and remove non-alphanumerics (just in case)
          enteredValue = enteredValue.toUpperCase().replace(/[^A-Z0-9]/g, "");

          // Limit to 10 characters
          const sanitizedValue = enteredValue.slice(0, 10);

          // Update Redux or parent state
          handleRowInputChange(
            sanitizedValue,
            params.row.id,
            "costCenterNumber"
          );
        };
        return (
          <TextField
            value={value}
            onChange={handleChange}
            onKeyDown={handleKeyDown}
            onPaste={handlePaste}
            variant="outlined"
            size="small"
            placeholder={t("Enter Cost Center Number")}
            fullWidth
            error={isInvalid}
            helperText={isInvalid ? "Number should be 10 digits" : ""}
            FormHelperTextProps={{
              sx: {
                marginTop: "20px",
                paddingLeft: "15px",
                position: "absolute",
                style: {
                  paddingBottom: "0px", // adjust for spacing if needed
                },
              },
            }}
            inputProps={{
              inputMode: "numeric",
              maxLength: 10,
            }}
            // sx={{
            //   "& .MuiInputBase-root.Mui-disabled": {
            //     "& > input": {
            //       WebkitTextFillColor: colors.black.dark,
            //       color: colors.black.dark,
            //     },
            //   },
            // }}

            sx={{
              position: "relative",
              "& .MuiInputBase-root": {
                height: "35px", // Set fixed height
                alignItems: "center",
              },
              "& .MuiFormHelperText-root": {
                marginLeft: "0px",
              },
              "& .MuiInputBase-root.Mui-disabled": {
                "& > input": {
                  WebkitTextFillColor: colors.black.dark,
                  color: colors.black.dark,
                },
              },
            }}
          />
        );
      },
    },
    {
      field: "longDescription",
      headerName: "Long Description",
      align: "left",
      headerAlign: "left",
      flex: 1.5,
      renderHeader: () => (
        <span>
          {t("Long Description")}
          {isFieldMandatory("longDescription") && (
            <span style={{ color: "red" }}> *</span>
          )}
        </span>
      ),
      renderCell: (params) => {
        const value = params.row.longDescription || "";
        return (
          <TextField
            value={value}
            onChange={(e) => {
              handleRowInputChange(
                e.target.value.toUpperCase(),
                params.row.id,
                "longDescription"
              );
            }}
            variant="outlined"
            size="small"
            placeholder={t("Enter Long Description")}
            fullWidth
            sx={{
              "& .MuiInputBase-root.Mui-disabled": {
                "& > input": {
                  WebkitTextFillColor: colors.black.dark,
                  color: colors.black.dark,
                },
              },
            }}
          />
        );
      },
    },
    {
      field: "action",
      headerName: "Action",
      align: "left",
      headerAlign: "left",
      width: 150,
      renderHeader: () => (
        <span style={{ fontWeight: "bold" }}>{t("Action")}</span>
      ),
      renderCell: (params) => {
        const rowId = params.row.id;
        const rowData = {
          id: rowId,
          ...rowsBodyData[rowId],
        };
        const validateStatus = getValidationStatus(rowId);
        const handleValidateClick = (e) => {
          e.stopPropagation();
          handleValidate(params.row, rowData, costCenterTabs);
        };
        return (
          <Box>
            <Tooltip
              title={
                validateStatus === "success"
                  ? "Validated Successfully"
                  : validateStatus === "error"
                  ? "Validation Failed"
                  : "Click to Validate"
              }
            >
              <IconButton onClick={handleValidateClick} color={validateStatus}>
                {validateStatus === "error" ? (
                  <CancelOutlinedIcon />
                ) : (
                  <TaskAltIcon />
                )}
              </IconButton>
            </Tooltip>
          </Box>
        );
      },
    },
  ];
  const mandatoryFields = [
    "controllingArea",
    "costCenterNumber",
    "CompCode",
    "longDescription",
  ];
  const handleSnackBarClose = () => {
    setOpenSnackBar(false);
  };
  const handleDialogClose = () => {
    dispatch(setOpenDialog(false));
  };
  const toggleGridZoom = () => {
    setIsGridZoomed(!isGridZoomed);
    if (isTabsZoomed) setIsTabsZoomed(false);
  };
  const toggleTabsZoom = () => {
    setIsTabsZoomed(!isTabsZoomed);
    if (isGridZoomed) setIsGridZoomed(false);
  };
  const getCostCenterCategory = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CostcenterType", data: data.body }));
    };
    const hError = (error) => {
      console.error(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCostCenterCategory`,
      "get",
      hSuccess,
      hError
    );
  };
  const getFunctionalArea = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "FuncAreaLong", data: data.body }));
    };
    const hError = (error) => {
      console.error(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getFunctionalArea`,
      "get",
      hSuccess,
      hError
    );
  };
  useEffect(() => {
    getCostCenterCategory();
    getFunctionalArea();
  }, []);
  const isEqual = (a, b) => JSON.stringify(a) === JSON.stringify(b);
  const fieldsSyncedFromRow = {
    Description: "Descript",
    CompCode: "CompCode.code",
  };
  const cleanTabForComparison = (tab, row) => {
    const cleanedTab = { ...tab };
    delete cleanedTab.id;
    for (const [tabKey, rowPath] of Object.entries(fieldsSyncedFromRow)) {
      const rowValue = rowPath
        .split(".")
        .reduce((acc, key) => (acc ? acc[key] : undefined), row);
      // If tab value matches what came from row, ignore it
      if (cleanedTab[tabKey] === rowValue) {
        delete cleanedTab[tabKey];
      }
    }
    return cleanedTab;
  };
  const isRowDirty = (rowId) => {
    const originalRow = originalRowData[rowId];
    const originalTab = originalTabData[rowId];
    const currentRow = ccRows.find((r) => r.id === rowId);
    const currentTab = rowsBodyData[rowId];
    if (!originalRow || !originalTab || !currentRow || !currentTab) return true;
    const cleanedCurrentTab = cleanTabForComparison(currentTab, currentRow);
    const cleanedOriginalTab = cleanTabForComparison(originalTab, originalRow);
    return (
      !isEqual(originalRow, currentRow) ||
      !isEqual(cleanedOriginalTab, cleanedCurrentTab)
    );
  };
  const getValidationStatus = (rowId) => {
    const status = validatedStatus[rowId];
    const dirty = isRowDirty(rowId);
    if (!status) return "default";
    return isRowDirty(rowId) ? "error" : status;
  };
  const mandatoryFieldsConfig = costCenterTabs[selectedTab]?.data || [];
  useEffect(() => {
    const allRowsValidatedAndClean =
      ccRows.length > 0 &&
      ccRows.every((row) => {
        const isValidated = validatedRows[row.id] === true;
        const isClean = !isRowDirty(row.id);
        return isValidated && isClean;
      });
    setIsAddRowEnabled(allRowsValidatedAndClean);
    setValidateEnabled(allRowsValidatedAndClean);
  }, [ccRows, rowsBodyData, validatedRows, originalRowData, originalTabData]);
  const checkDuplicateCheckCostCenter = (row) => {
    setIsLoading(true);
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data?.body?.length > 0) {
        setAlertType("error");
        setAlertMsg("Duplicate Cost Center found.");
        // Mark this row as not validated
        setValidatedRows((prev) => ({ ...prev, [row.id]: false }));
      } else {
        setAlertType("success");
        setAlertMsg("Validation Successful");
        // Mark this row as validated
        setValidatedRows((prev) => ({ ...prev, [row.id]: true }));
      }
      setOpenSnackBar(true);
    };
    const hError = (error) => {
      setIsLoading(false);
      console.error(error);
      setAlertType("error");
      setAlertMsg("An error occurred during validation.");
      setOpenSnackBar(true);
      // Mark this row as not validated on error
      setValidatedRows((prev) => ({ ...prev, [row.id]: false }));
    };
    doAjax(
      `/${destination_CostCenter_Mass}/alter/fetchCoAreaCCDupliChk?ctrlAreaCCToCheck=${row?.controllingArea?.code??row?.controllingArea}$$${row?.CompCode?.code??row?.CompCode}${row?.costCenterNumber}`,
      "get",
      hSuccess,
      hError
    );
  };
  const handleValidate = (row, tab, config) => {
    const missing = [];
    const allFields = config.flatMap((section) => {
      return Object.values(section.data).flat(); // flatten inner field arrays
    });
    allFields?.forEach((field) => {
      if (field.visibility === "Mandatory") {
        const value = tab[field.jsonName];
        if (
          value === null ||
          value === undefined ||
          (typeof value === "string" && value.trim() === "")
        ) {
          missing.push(field.fieldName);
        }
      }
    });
    const headerMap = {
      CompCode: "Company Code",
      costCenterNumber: "Cost Center Number",
      controllingArea: "Controlling Area",
      longDescription: "Long Description",
    };
    mandatoryFields.forEach((field) => {
      const value = row[field];
      const displayName = headerMap[field] || field;
      if (
        value === null ||
        value === undefined ||
        (typeof value === "string" && value.trim() === "")
      ) {
        missing.push(displayName);
      } else if (
        field === "costCenterNumber" &&
        ((value.length !== 6 && value.length !== 10) ||
          !/^[a-zA-Z0-9]+$/.test(value))
      ) {
        // Check for exact 10 alphanumeric characters
        missing.push(displayName);
      }
    });
    const status = missing.length > 0 ? "error" : "success";
    dispatch(setValidatedStatus({ rowId: row.id, status }));
    if (status === "error") {
      setMissingFields(missing);
      setMissingFieldsDialogOpen(true);
    } else {
      setOriginalRowData((prev) => ({
        ...prev,
        [row.id]: JSON.parse(JSON.stringify(row)),
      }));
      setOriginalTabData((prev) => {
        const { id, ...restTab } = tab;
        return {
          ...prev,
          [row.id]: JSON.parse(JSON.stringify(restTab)),
        };
      });
        setAlertType("success");
        setAlertMsg("Validation Successful");
        // Mark this row as validated
        setValidatedRows((prev) => ({ ...prev, [row.id]: true }));
        setOpenSnackBar(true);
      // Note: Might use later for Object Number Duplicity
      // checkDuplicateCheckCostCenter(row);
    }
  };
  useEffect(() => {
    if (ccRows.length && Object.keys(rowsBodyData).length) {
      const newOriginalRows = {};
      const newOriginalTabs = {};
      ccRows.forEach((row) => {
        const rowId = row.id;
        if (!originalRowData[rowId]) {
          newOriginalRows[rowId] = JSON.parse(JSON.stringify(row));
        }
        if (!originalTabData[rowId] && rowsBodyData[rowId]) {
          const { id, ...restTab } = rowsBodyData[rowId];
          newOriginalTabs[rowId] = JSON.parse(JSON.stringify(restTab));
        }
      });
      setOriginalRowData((prev) => ({ ...prev, ...newOriginalRows }));
      setOriginalTabData((prev) => ({ ...prev, ...newOriginalTabs }));
    }
  }, [ccRows, rowsBodyData]);
  const isFieldMandatory = (fieldName) => mandatoryFields.includes(fieldName);
  const handleMassValidate = (row) => {
    const missing = [];
    mandatoryFields.forEach((field) => {
      if (!row[field]) {
        missing.push(field);
      }
    });
    if (missing.length > 0) {
      setMissingFields(missing);
      setMissingFieldsDialogOpen(true);
      setValidatedRows((prev) => ({ ...prev, [row.id]: false }));
      return false;
    } else {
      return new Promise((resolve) => {
        const hSuccess = (data) => {
          if (data?.body?.length > 0) {
            setValidatedRows((prev) => ({ ...prev, [row.id]: false }));
            resolve(false);
          } else {
            setValidatedRows((prev) => ({ ...prev, [row.id]: true }));
            resolve(true);
          }
        };
        const hError = () => {
          setValidatedRows((prev) => ({ ...prev, [row.id]: false }));
          resolve(false);
        };
        doAjax(
          `/${destination_CostCenter_Mass}/alter/fetchCoAreaCCDupliChk?ctrlAreaCCToCheck=${row?.controllingArea?.code}$$${row?.costCenterNumber}`,
          "get",
          hSuccess,
          hError
        );
      });
    }
  };
  const validateAllRows = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForCC(
      reduxPayload,
      requestHeaderSlice,
      isrequestId,
      task,
      dynamicData
    );
    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Cost Centers Validation initiated");
      setIsSaveAsDraftEnabled(true);
      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };
    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while validating the request");
      console.error("Error saving draft:", error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/massAction/validateMassCostCenter`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };
  const handleCloseDialog = () => {
    setMissingFieldsDialogOpen(false);
  };
  const handleRowInputChange = (value, id, field) => {
    if (field === "controllingArea") {
      const controllingAreaValue = value?.code || value;
      getCompanyCodeBasedOnControllingArea(controllingAreaValue);
      dispatch(
        updateModuleFieldDataCC({
          uniqueId: selectedRowId || selectedRow?.id,
          keyName: "controllingArea",
          data: controllingAreaValue,
          viewID: null,
        })
      );
      const updatedRows = ccRows.map((row) => {
        console.log("cccccarea");
        if (row.id === id) {
          return {
            ...row,
            controllingArea: controllingAreaValue,
          };
        }
        return row;
      });
      dispatch(setCCRows(updatedRows));
      return;
    }
    if (field === "CompCode") {
      getProfitCenter(value?.code);
      dispatch(
        updateModuleFieldDataCC({
          uniqueId: selectedRowId || selectedRow?.id,
          keyName: "CompCode",
          data: value?.code,
          viewID: "CompCode",
        })
      );
      fetchCurrencyBasedOnCompCode(
        value?.code,
        dispatch,
        selectedRowId || selectedRow?.id
      );
    }
    if (field === "longDescription") {
      dispatch(
        updateModuleFieldDataCC({
          uniqueId: selectedRowId || selectedRow?.id,
          keyName: "Descript",
          data: value,
          viewID: "Basic Data",
        })
      );
    }
    const updatedRows = ccRows.map((row) =>
      row.id === id ? { ...row, [field]: value } : row
    );
    dispatch(setCCRows(updatedRows));
  };
  const handleRowClick = (params) => {
    const clickedRow = params.row;
    setSelectedRow(clickedRow);
    dispatch(setSelectedRowIdCC(clickedRow?.CostCenterID));
  };
  const handleAddRow = () => {
    const id = uuidv4();
    setNewRowId(id);
    setIsAddRowMode(true);
    setWithReference("yes"); // Reset radio to default
    // ⛔ Reset dropdown selections
    setSelectedControllingAreaCode("");
    setSelectedCompanyCode("");
    setSelectedCostCenterCode("");
    const newRow = {
      id,
      controllingArea: "",
      costCenterNumber: "",
      longDescription: "",
      businessSegment: "",
      compCode: "",
      included: true,
      isNew: true,
    };
    dispatch(setCCRows([...ccRows, newRow]));
    setValidatedRows((prev) => ({ ...prev, [newRow.id]: false }));
  };
  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };
  useEffect(() => {
    setSelectedRow(ccRows[0]);
  }, []);
  const getCompanyCode = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CompCode", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCompCode?rolePrefix=ETP`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCostCenterGrp = (coa) => {
    const hSuccess = (data) => {
      dispatch(
        setDependentDropdown({
          keyName: "PrctrHierGrp",
          data: data?.body || [],
          keyName2: selectedRowId || selectedRow?.id,
        })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCostCtrGroup?controllingArea=${coa}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getProfitCenter = (compcode) => {
    const payload = {
      controllingArea: "ETCA",
      companyCode: compcode,
      top: "100",
      skip: "0",
    };
    const hSuccess = (data) => {
      dispatch(
        setDependentDropdown({
          keyName: "ProfitCtr",
          data: data?.body?.list || [],
          keyName2: selectedRowId || selectedRow?.id,
        })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getProfitCentersNo`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const getFormPlanningFrp = () => {
    const hSuccess = (data) => {
      setDropdownDataFormPlanning(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "Template", data: data.body },
      });
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getFormPlanningTemp`,
      "get",
      hSuccess,
      hError
    );
  };
  const getControllingArea = () => {
    const hSuccess = (data) => {
      setDropdownDataCOA(data.body);
      dispatch(setDropDown({ keyName: "ControllingArea", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getControllingArea`,
      "get",
      hSuccess,
      hError
    );
  };
  const getTaxJurisdiction = () => {
    const hSuccess = (data) => {
      setDropdownDataTaxJur(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "TaxJurisdiction", data: data.body },
      });
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getJurisdiction`,
      "get",
      hSuccess,
      hError
    );
  };
  const getBusSeg = () => {
    const hSuccess = (data) => {
      setDropdownDataCOA(data.body);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getBusinessSegment`,
      "get",
      hSuccess,
      hError
    );
  };
  useEffect(() => {
    getCountryData();
    getCostCenterGrp();
    getFormPlanningFrp();
    getControllingArea();
    getTaxJurisdiction();
    getBusSeg();
  }, []);
  const getCountryData = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "AddrCountry", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCountry`,
      "get",
      hSuccess,
      hError
    );
  };
  const handleCountryChange = (e, fieldData) => {
    const selectedCountryCode = e.target.value;
    setSelectedCountry(selectedCountryCode);
    const rowId = selectedRow?.id;
    if (!rowId) return;
    getRegionBasedOnCountry(selectedCountryCode, fieldData, rowId);
  };
  const [rowRegionData, setRowRegionData] = useState({});
  const getRegionBasedOnCountry = (countryCode, fieldData, rowId) => {
    const hSuccess = (data) => {
      // Store region data for this specific row
      setRowRegionData((prev) => ({
        ...prev,
        [rowId]: data.body,
      }));
      // Also update the general dropdown data for UI rendering
      setDropdownDataRegion(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "Region", data: data.body },
      });
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getRegionBasedOnCountry?country=${countryCode}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getLanguage = () => {
    const hSuccess = (data) => {
      setDropdownDataLanguage(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "Language", data: data.body },
      });
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getLanguageKey`,
      "get",
      hSuccess,
      hError
    );
  };
  useEffect(() => {
    getLanguage();
  }, []);
  const handleSaveAsDraft = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForCC(
      reduxPayload,
      requestHeaderSlice,
      isrequestId,
      task,
      dynamicData
    );
    const hSuccess = (data) => {
      setBlurLoading(false);
      setIsLoading(false);
      setAlertType("success");
      setAlertMsg("Cost Centers Submission saved as draft.");
      setOpenSnackBar(true);
      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };
    const hError = (error) => {
      setBlurLoading(false);
      setIsLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving the draft.");
      setOpenSnackBar(true);
      console.error("Error saving draft:", error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/massAction/costCentersSaveAsDraft`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };
  const handleSubmitForReview = () => {
    setLoaderMessage("");
    setBlurLoading(true);
    const finalPayload = createPayloadForCC(
      reduxPayload,
      requestHeaderSlice,
      isrequestId,
      task,
      dynamicData
    );
    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Cost Centers submission for save as draft initiated");
      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };
    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while saving the draft");
      console.error("Error saving draft:", error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/massAction/costCentersSubmitForReview`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };
  const handleValidateAndSyndicate = (type) => {
    setBlurLoading(true);
    const finalPayload = createPayloadForCC(
      reduxPayload,
      requestHeaderSlice,
      isrequestId,
      task,
      dynamicData
    );
    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      if (type === "VALIDATE") setAlertMsg("Cost Centers Validation initiated");
      else if (type === "SYNDICATE")
        setAlertMsg("Cost Centers Syndication initiated");
      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };
    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while validating the request");
      console.error("Error saving draft:", error);
    };
    doAjax(
      type === "VALIDATE"
        ? `/${destination_CostCenter_Mass}/massAction/validateMassCostCenter`
        : `/${destination_CostCenter_Mass}/massAction/createCostCentersApproved`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };
  const handleSubmitForApprove = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForCC(
      reduxPayload,
      requestHeaderSlice,
      isrequestId,
      task,
      dynamicData
    );
    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Cost Centers submission for Approve initiated");
      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };
    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while saving the draft");
      console.error("Error saving draft:", error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/massAction/costCentersApprovalSubmit`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };
  const handleSendBack = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForCC(
      reduxPayload,
      requestHeaderSlice,
      isrequestId,
      task,
      dynamicData
    );
    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Cost Centers submission for Approve initiated");
      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };
    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while saving the draft");
      console.error("Error saving draft:", error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/massAction/costCentersSendForCorrection`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };
  const handleCorrection = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForCC(
      reduxPayload,
      requestHeaderSlice,
      isrequestId,
      task,
      dynamicData
    );
    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg(data?.message ?? "Cost Centers Sent for Correction !");
      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };
    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while sending for correction");
      console.error("Error saving draft:", error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/massAction/costCentersSendForReview`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };
  const handleRejectAndCancel = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForCC(
      reduxPayload,
      requestHeaderSlice,
      isrequestId,
      task,
      dynamicData
    );
    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg(data?.message ?? "Cost Centers Rejected !");
      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };
    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while rejecting the request");
    };
    doAjax(
      `/${destination_CostCenter_Mass}/massAction/costCentersRejected`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };
  const addCostCenterCopy = (rowId) => {
    setBlurLoading(true);
    console.log("rowId", rowId);
    const payload = {
      coAreaCCs: [
        {
          controllingArea: selectedControllingAreaCode,
          costCenter: selectedCostCenterCode,
        },
      ],
    };
    const hSuccess = (data) => {
      const rawData = data?.body || [];
      const response = rawData[0];
      if (!response) {
        console.warn("No response data received for Cost Center copy.");
        setBlurLoading(false);
        handleDialogClose();
        return;
      }
      const country = response?.addressTabDto?.AddrCountry || "";
      const region = response?.addressTabDto?.AddrRegion || "";
      const updatedRows = ccRows.map((row) =>
        row.id === rowId
          ? {
              ...row,
              controllingArea: response.controllingArea || "",
              CompCode: response.basicDataTabDto?.CompCode || "",
            }
          : row
      );
      const updatedTabs = {
        [rowId]: {
          PersonInCharge: response.basicDataTabDto?.PersonInCharge || "",
          PersonInChargeUser:
            response.basicDataTabDto?.PersonInChargeUser || "",
          CostcenterType: response.basicDataTabDto?.CostcenterType || "",
          FuncAreaLong: response.basicDataTabDto?.FuncAreaLong || "",
          Currency: response.basicDataTabDto?.Currency || "",
          ProfitCtr: response.basicDataTabDto?.ProfitCtr || "",
          CompCode: response.basicDataTabDto?.CompCode || [],
          AddrCountry: country,
          AddrStreet: response.addressTabDto?.AddrStreet || "",
          AddrCity: response.addressTabDto?.AddrCity || "",
          AddrRegion: region,
        },
      };
      dispatch({
        type: "costCenter/setCostCenterRows",
        payload: updatedRows,
      });
      dispatch({
        type: "costCenter/setCostCenterTab",
        payload: updatedTabs,
      });
      dispatch(
        updateModuleFieldDataCC({
          uniqueId: rowId,
          keyName: "CompCode",
          data: response.basicDataTabDto?.CompCode || "",
          viewID: "Comp Codes",
        })
      );
      dispatch(
        updateModuleFieldDataCC({
          uniqueId: rowId,
          keyName: "costCenterNumber",
          data: response?.costCenter || "",
          viewID: "Comp Codes",
        })
      );
      dispatch(
        updateModuleFieldDataCC({
          uniqueId: rowId,
          keyName: "AddrCountry",
          data: country,
          viewID: "Address",
        })
      );
      dispatch(
        updateModuleFieldDataCC({
          uniqueId: rowId,
          keyName: "AddrRegion",
          data: region,
          viewID: "Address",
        })
      );
      if (country) {
        getRegionBasedOnCountry(country, null, rowId);
      }
      getProfitCenter(response.basicDataTabDto?.CompCode);
      fetchCurrencyBasedOnCompCode(response.basicDataTabDto?.CompCode);
      setCostcenterResponse(rawData);
      setBlurLoading(false);
      handleDialogClose();
    };
    const hError = (error) => {
      console.error("Error fetching profit center data", error);
      setBlurLoading(false);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCostCentersData`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleProceed = () => {
    if (withReference === "no") {
      dispatch(setOpenDialog(false));
      setIsAddRowMode(false);
    } else {
      if (isAddRowMode) {
        const newRow = {
          id: newRowId,
          controllingArea: "",
          costCenterNumber: "",
          longDescription: "",
          companyCode: "",
          included: true,
          isNew: true,
        };
        dispatch(setCCRows([...ccRows, newRow]));
        setValidatedRows((prev) => ({ ...prev, [newRowId]: false }));
        addCostCenterCopy(newRowId);
      } else {
        const id = ccRows[0]?.id;
        if (id) addCostCenterCopy(id);
      }
      dispatch(setOpenDialog(true));
      setIsAddRowMode(false);
    }
  };
  return (
    <Box sx={{ p: 3 }}>
      <ReusableSnackBar
        openSnackBar={openSnackBar}
        alertMsg={alertMsg}
        handleSnackBarClose={handleSnackBarClose}
        alertType={alertType}
        isLoading={isLoading}
      />
      {error && (
        <Typography color="error">{t("Error loading data")}</Typography>
      )}
      <div
        style={{ padding: "0", width: "100%", margin: "0", marginTop: "20px" }}
      >
        <Box
          sx={{
            position: isGridZoomed ? "fixed" : "relative",
            top: isGridZoomed ? 0 : "auto",
            left: isGridZoomed ? 0 : "auto",
            right: isGridZoomed ? 0 : "auto",
            bottom: isGridZoomed ? 0 : "auto",
            width: isGridZoomed ? "100vw" : "100%",
            height: isGridZoomed ? "100vh" : "auto",
            zIndex: isGridZoomed ? 1004 : 1,
            backgroundColor: isGridZoomed ? "white" : "transparent",
            padding: isGridZoomed ? "20px" : "0",
            display: "flex",
            flexDirection: "column",
            boxShadow: isGridZoomed
              ? "0px 0px 15px rgba(0, 0, 0, 0.2)"
              : "none",
            transition: "all 0.3s ease",
            borderRadius: "8px",
            border: "1px solid #e0e0e0",
          }}
        >
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              padding: "8px 16px",
              backgroundColor: "#f5f5f5",
              borderRadius: "8px 8px 0 0",
            }}
          >
            <Typography variant="h6">{t("List of Cost Centers")}</Typography>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <Button
                variant="contained"
                color="primary"
                size="small"
                onClick={handleAddRow}
                disabled={!isAddRowEnabled}
              >
                + {t("Add")}
              </Button>
              <Tooltip
                title={isGridZoomed ? "Exit Zoom" : "Zoom In"}
                sx={{ zIndex: "1009" }}
              >
                <IconButton
                  onClick={toggleGridZoom}
                  color="primary"
                  sx={{
                    backgroundColor: "rgba(0, 0, 0, 0.05)",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.1)",
                    },
                  }}
                >
                  {isGridZoomed ? <CloseFullscreenIcon /> : <CropFreeIcon />}
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
          <div style={{ width: "100%", height: "100%", overflowX: "auto" }}>
            <div style={{ height: "100%" }}>
              <div>
                <ReusableDataTable
                  isLoading={loading}
                  rows={ccRows}
                  columns={columns}
                  pageSize={10}
                  tempheight={"50vh"}
                  getRowIdValue={"id"}
                  status_onRowSingleClick={true}
                  callback_onRowSingleClick={handleRowClick}
                  getRowClassName={(params) =>
                    selectedRow?.id === params.row.id ? "Mui-selected" : ""
                  }
                />
              </div>
            </div>
          </div>
        </Box>
      </div>
      {openDialog && (
        <Dialog
          fullWidth
          open={openDialog}
          maxWidth="lg"
          onClose={handleDialogClose}
          sx={{
            "&::webkit-scrollbar": {
              width: "1px",
            },
          }}
        >
          <DialogTitle
            sx={{
              justifyContent: "space-between",
              alignItems: "center",
              height: "max-content",
              padding: ".5rem",
              paddingLeft: "1rem",
              backgroundColor: "#EAE9FF",
              display: "flex",
            }}
          >
            <Typography variant="h6">Add New CostCenter</Typography>
          </DialogTitle>
          <DialogContent
            sx={{
              padding: ".5rem 1rem",
              alignItems: "center",
              justifyContent: "center",
              margin: "0px 25px",
            }}
          >
            <FormControl component="fieldset" sx={{ paddingBottom: "2%" }}>
              <FormLabel
                component="legend"
                sx={{
                  padding: "15px 0px",
                  fontWeight: "600",
                  fontSize: "15px",
                }}
              >
                Do You Want To Continue
              </FormLabel>
              <RadioGroup
                row
                aria-label="cost-center-number"
                name="cost-center-number"
                value={withReference}
                onChange={(event) => setWithReference(event.target.value)}
              >
                <FormControlLabel
                  value="yes"
                  control={<Radio />}
                  label="With Reference"
                />
                <FormControlLabel
                  value="no"
                  control={<Radio />}
                  label="Without Reference"
                />
              </RadioGroup>
            </FormControl>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Grid container spacing={2}>
                  <Grid item xs={3}>
                    <SingleSelectDropdown
                      options={dropdownDataCOA || []}
                      value={
                        dropdownDataCOA?.find(
                          (item) => item.code === selectedControllingAreaCode
                        ) || null
                      }
                      onChange={(newValue) => {
                        getCompanyCodeBasedOnControllingArea(newValue?.code);
                        setSelectedControllingAreaCode(newValue?.code || "");
                      }}
                      placeholder="Select Controlling Area"
                      minWidth="90%"
                      listWidth={235}
                      disabled={withReference === "no"}
                    />
                  </Grid>
                  <Grid item xs={3}>
                    <SingleSelectDropdown
                      options={dropdownDataCompany || []}
                      value={
                        dropdownDataCompany?.find(
                          (item) => item.code === selectedCompanyCode
                        ) || null
                      }
                      onChange={(newValue) =>
                        setSelectedCompanyCode(newValue?.code || "")
                      }
                      placeholder="Select Company Code"
                      disabled={withReference === "no"}
                      minWidth="90%"
                      listWidth={235}
                    />
                  </Grid>
                  <Grid item xs={3}>
                    <SingleSelectDropdown
                      options={costCenterOptions}
                      value={
                        costCenterOptions.find(
                          (item) => item.code === selectedCostCenterCode
                        ) || null
                      }
                      onChange={(newValue) =>
                        setSelectedCostCenterCode(newValue?.code || "")
                      }
                      placeholder="Select Cost Center"
                      minWidth="90%"
                      listWidth={235}
                      disabled={withReference === "no"}
                    />
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
            <Button
              sx={{
                width: "max-content",
                textTransform: "capitalize",
              }}
              onClick={handleDialogClose}
              variant="outlined"
            >
              Cancel
            </Button>
            <Button
              className="button_primary--normal"
              type="save"
              onClick={handleProceed}
              variant="contained"
            >
              Proceed
            </Button>
          </DialogActions>
        </Dialog>
      )}
      <Dialog
        open={missingFieldsDialogOpen}
        onClose={handleCloseDialog}
        aria-labelledby="missing-fields-dialog-title"
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle
          id="missing-fields-dialog-title"
          sx={{
            backgroundColor: "#fff3e0",
            color: "#e65100",
            display: "flex",
            alignItems: "center",
            gap: 1,
            fontWeight: "bold",
          }}
        >
          <WarningAmberIcon fontSize="medium" />
          {t("Missing Mandatory Fields")}
        </DialogTitle>

        <DialogContent sx={{ pt: 2 }}>
          <Typography variant="body1" gutterBottom>
            {t("Please complete the following mandatory fields:")}
          </Typography>
          <List dense>
            {missingFields.map((field, index) => (
              <ListItem key={index} disablePadding>
                <ListItemIcon sx={{ minWidth: 30 }}>
                  <WarningAmberIcon fontSize="small" color="warning" />
                </ListItemIcon>
                <ListItemText primary={field} />
              </ListItem>
            ))}
          </List>
        </DialogContent>
        <DialogActions sx={{ pr: 3, pb: 2 }}>
          <Button
            onClick={handleCloseDialog}
            variant="contained"
            color="warning"
            sx={{ textTransform: "none", fontWeight: 500 }}
          >
            {t("Close")}
          </Button>
        </DialogActions>
      </Dialog>
      {selectedRow &&
        (reqBench === "true" && selectedRowId ? (
          <Box
            sx={{
              position: isTabsZoomed ? "fixed" : "relative",
              top: isTabsZoomed ? 0 : "auto",
              left: isTabsZoomed ? 0 : "auto",
              right: isTabsZoomed ? 0 : "auto",
              bottom: isTabsZoomed ? 0 : "auto",
              width: isTabsZoomed ? "100vw" : "100%",
              height: isTabsZoomed ? "100vh" : "auto",
              zIndex: isTabsZoomed ? 1004 : 1,
              backgroundColor: isTabsZoomed ? "white" : "transparent",
              padding: isTabsZoomed ? "20px" : "0",
              marginTop: "20px",
              display: "flex",
              flexDirection: "column",
              boxShadow: isTabsZoomed
                ? "0px 0px 15px rgba(0, 0, 0, 0.2)"
                : "none",
              transition: "all 0.3s ease",
              borderRadius: "8px",
              border: "1px solid #e0e0e0",
            }}
          >
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                padding: "8px 16px",
                borderRadius: "8px 8px 0 0",
              }}
            >
              <Typography variant="h6">{t("View Details")}</Typography>
              <Tooltip
                title={isTabsZoomed ? "Exit Zoom" : "Zoom In"}
                sx={{ zIndex: "1009" }}
              >
                <IconButton
                  onClick={toggleTabsZoom}
                  color="primary"
                  sx={{
                    backgroundColor: "rgba(0, 0, 0, 0.05)",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.1)",
                    },
                  }}
                >
                  {isTabsZoomed ? <CloseFullscreenIcon /> : <CropFreeIcon />}
                </IconButton>
              </Tooltip>
            </Box>
            <Box sx={{ mt: 3 }}>
              <Tabs
                value={selectedTab}
                onChange={handleTabChange}
                indicatorColor="primary"
                textColor="primary"
                aria-label="Request tabs"
                variant="scrollable"
                scrollButtons="auto"
                sx={{
                  top: 0,
                  position: "sticky",
                  zIndex: 1000,
                  backgroundColor: colors.background.container,
                  borderBottom: `1px solid ${colors.border.light}`,
                  "& .MuiTab-root": {
                    minHeight: "48px",
                    textTransform: "none",
                    fontSize: "14px",
                    fontWeight: 600,
                    color: colors.black.graphite,
                    "&.Mui-selected": {
                      color: colors.primary.main,
                      fontWeight: 700,
                    },
                    "&:hover": {
                      color: colors.primary.main,
                      opacity: 0.8,
                    },
                  },
                  "& .MuiTabs-indicator": {
                    backgroundColor: colors.primary.main,
                    height: "3px",
                  },
                }}
              >
                {costCenterTabs.map((tab, index) => (
                  <Tab key={index} label={tab.tab} />
                ))}
              </Tabs>
              <Paper elevation={2} sx={{ p: 3, borderRadius: 4 }}>
                {costCenterTabs[selectedTab] && (
                  <GenericTabsGlobal
                    disabled={true}
                    basicDataTabDetails={costCenterTabs[selectedTab].data}
                    dropDownData={{
                      Country: dropdownDataCountry,
                      Segment: dropdownDataSegment,
                      Language: dropdownDataLanguage,
                      Template: dropdownDataFormPlanning,
                      COArea: dropdownDataCOA,
                      TaxJurisdiction: dropdownDataTaxJur,
                    }}
                    activeViewTab={costCenterTabs[selectedTab].tab}
                    uniqueId={selectedRow?.id || selectedRowId || ccRows[0]?.id}
                    selectedRow={selectedRow || {}}
                    module={"CostCenter"}
                  />
                )}
              </Paper>
            </Box>
          </Box>
        ) : (
          <Box
            sx={{
              position: isTabsZoomed ? "fixed" : "relative",
              top: isTabsZoomed ? 0 : "auto",
              left: isTabsZoomed ? 0 : "auto",
              right: isTabsZoomed ? 0 : "auto",
              bottom: isTabsZoomed ? 0 : "auto",
              width: isTabsZoomed ? "100vw" : "100%",
              height: isTabsZoomed ? "100vh" : "auto",
              zIndex: isTabsZoomed ? 1004 : 1,
              backgroundColor: isTabsZoomed ? "white" : "transparent",
              padding: isTabsZoomed ? "20px" : "0",
              marginTop: "20px",
              display: "flex",
              flexDirection: "column",
              boxShadow: isTabsZoomed
                ? "0px 0px 15px rgba(0, 0, 0, 0.2)"
                : "none",
              transition: "all 0.3s ease",
              borderRadius: "8px",
              border: "1px solid #e0e0e0",
            }}
          >
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                padding: "8px 16px",
                borderRadius: "8px 8px 0 0",
              }}
            >
              <Typography variant="h6">{t("View Details")}</Typography>
              <Tooltip
                title={isTabsZoomed ? "Exit Zoom" : "Zoom In"}
                sx={{ zIndex: "1009" }}
              >
                <IconButton
                  onClick={toggleTabsZoom}
                  color="primary"
                  sx={{
                    backgroundColor: "rgba(0, 0, 0, 0.05)",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.1)",
                    },
                  }}
                >
                  {isTabsZoomed ? <CloseFullscreenIcon /> : <CropFreeIcon />}
                </IconButton>
              </Tooltip>
            </Box>
            <Box sx={{ flexGrow: 1, display: "flex", flexDirection: "column" }}>
              <Tabs
                value={selectedTab}
                onChange={handleTabChange}
                indicatorColor="primary"
                textColor="primary"
                aria-label="Request tabs"
                variant="scrollable"
                scrollButtons="auto"
                sx={{
                  top: 0,
                  position: "sticky",
                  zIndex: 1000,
                  backgroundColor: colors.background.container,
                  borderBottom: `1px solid ${colors.border.light}`,
                  "& .MuiTab-root": {
                    minHeight: "48px",
                    textTransform: "none",
                    fontSize: "14px",
                    fontWeight: 600,
                    color: colors.black.graphite,
                    "&.Mui-selected": {
                      color: colors.primary.main,
                      fontWeight: 700,
                    },
                    "&:hover": {
                      color: colors.primary.main,
                      opacity: 0.8,
                    },
                  },
                  "& .MuiTabs-indicator": {
                    backgroundColor: colors.primary.main,
                    height: "3px",
                  },
                }}
              >
                {costCenterTabs.map((tab, index) => (
                  <Tab key={index} label={tab.tab} />
                ))}
              </Tabs>
              <Paper elevation={2} sx={{ p: 3, borderRadius: 4 }}>
                {costCenterTabs[selectedTab] && (
                  <GenericTabsGlobal
                    disabled={false}
                    basicDataTabDetails={costCenterTabs[selectedTab].data}
                    dropDownData={{
                      Country: dropdownDataCountry,
                      Segment: dropdownDataSegment,
                      Language: dropdownDataLanguage,
                      Template: dropdownDataFormPlanning,
                      COArea: dropdownDataCOA,
                      TaxJurisdiction: dropdownDataTaxJur,
                    }}
                    activeViewTab={costCenterTabs[selectedTab].tab}
                    uniqueId={selectedRow?.id || selectedRowId || ccRows[0]?.id}
                    selectedRow={selectedRow || {}}
                    module={"CostCenter"}
                  />
                )}
              </Paper>
              <Box
                sx={{
                  borderTop: "1px solid #e0e0e0",
                  padding: "16px",
                }}
              >
                <BottomNavGlobal
                  handleSaveAsDraft={handleSaveAsDraft}
                  handleSubmitForReview={handleSubmitForReview}
                  handleSubmitForApprove={handleSubmitForApprove}
                  handleSendBack={handleSendBack}
                  handleCorrection={handleCorrection}
                  handleRejectAndCancel={handleRejectAndCancel}
                  handleValidateAndSyndicate={handleValidateAndSyndicate}
                  validateAllRows={validateAllRows}
                  isSaveAsDraftEnabled={isSaveAsDraftEnabled}
                  validateEnabled={validateEnabled}
                  filteredButtons={filteredButtons}
                  moduleName={"CostCenter"}
                />
              </Box>
            </Box>
          </Box>
        ))}
      <BottomNavGlobal
        handleSaveAsDraft={handleSaveAsDraft}
        handleSubmitForReview={handleSubmitForReview}
        handleSubmitForApprove={handleSubmitForApprove}
        handleSendBack={handleSendBack}
        handleCorrection={handleCorrection}
        handleRejectAndCancel={handleRejectAndCancel}
        handleValidateAndSyndicate={handleValidateAndSyndicate}
        validateAllRows={validateAllRows}
        isSaveAsDraftEnabled={isSaveAsDraftEnabled}
        validateEnabled={validateEnabled}
        filteredButtons={filteredButtons}
        moduleName={"CostCenter"}
      />
      <ReusableBackDrop
        blurLoading={blurLoading}
        loaderMessage={loaderMessage}
      />
    </Box>
  );
};
export default RequestDetailsCC;
