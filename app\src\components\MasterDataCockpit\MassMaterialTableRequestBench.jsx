import React, { useEffect, useState } from "react";
import { Box, TextField, MenuItem, Checkbox, FormControlLabel, Button, Tab, Tabs, Grid, Typography } from "@mui/material";
import RequestHeader from "../RequestBench/RequestPages/RequestHeader";
import { doAjax } from "../Common/fetchService";
import { destination_DocumentManagement, destination_IDM, destination_MaterialMgmt } from "../../destinationVariables";
import { useDispatch, useSelector } from "react-redux";
import { accountingDataTabs, basicDataTabs, mrpDataTabs, purchasingDataTabs, requestHeaderTabs, salesDataTabs } from "../../app/tabsDetailsSlice";
import RequestDetails from "../RequestBench/RequestPages/RequestDetails";
import { setDisplayPayload, setGeneralInformation,  setMatRequiredFieldsGI } from "../../app/payloadslice";
import { idGenerator, transformApiResponseToReduxPayload } from "../../functions";
import { useParams } from "react-router-dom";
import { container_Padding } from "../common/commonStyles";
import AttachmentsCommentsTab from "../RequestBench/RequestPages/AttachmentsCommentsTab";
import { setAttachmentType } from "../../app/initialDataSlice";
import { setMaterialRows, setTabValue } from "../../app/requestDataSlice";
import { useLocation } from "react-router-dom";
import CommentOutlinedIcon from '@mui/icons-material/CommentOutlined';
import ListAltOutlinedIcon from '@mui/icons-material/ListAltOutlined';
import ListOutlinedIcon from '@mui/icons-material/ListOutlined';
import PanoramaVerticalOutlinedIcon from '@mui/icons-material/PanoramaVerticalOutlined';
import PermIdentityOutlinedIcon from '@mui/icons-material/PermIdentityOutlined';

const MassMaterialTableRequestBench = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [mandFields, setMandFields] = useState([]);
  const [isSecondTabEnabled, setIsSecondTabEnabled] = useState(false);
  const [isAttachmentTabEnabled, setIsAttachmentTabEnabled] = useState(false);
  const [pcNumber, setPcNumber] = useState("");
  const [attachmentsData, setAttachmentsData] = useState([]);
  const [ruleData, setRuleData] = useState([]);
  const [questions, setQuestions] = useState([]);
  const[addHardCodeData, setAddHardCodeData]=useState(false);
  const { RequestId } = useParams();
  const dispatch = useDispatch();
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const requestIdHeader = useSelector((state) => state.request.requestHeader.requestId);

  const tabValue = useSelector((state) => state.request.tabValue);

  const handleTabChange = (event, newValue) => {
    dispatch(setTabValue(newValue));
  };

  const location = useLocation();

  // Extract the hash fragment
  

  const getGITemplate = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_GI_MATERIAL_QUESTIONS",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          // "MDG_CONDITIONS.MDG_GI_MODULE": "CC SUNOCO",
          "MDG_CONDITIONS.MDG_GI_SCENARIO": "Create",
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    setIsLoading(true);
    // const formData = new FormData();

    const hSuccess = (data) => {
      
      if (data.statusCode === 200) {
        const questionsData = data?.data?.result[0]?.MDG_GI_QUESTIONS_ACTION_TYPE || [];
       
        setQuestions(questionsData);
        questionsData.map((question) => {
          const input = question?.MDG_GI_INPUT_OPTION;
        });

        questionsData.map((question) => {
          if (question?.MDG_GI_INPUT_OPTION === "Radio Button") {
            if (question?.MDG_GI_VISIBILITY === " Mandatory") {
              console.log("insidevisibility");
              setMatRequiredFieldsGI(question.MDG_GI_QUESTION_TYPE.replaceAll("(", "").replaceAll(")", "").replaceAll("/", "").replaceAll("-", "").replaceAll(".", "").split(" ").join(""));
            }
            if (question?.MDG_GI_QUESTION_TYPE !== "Choose Priority Level") {
              dispatch(
                setSingleMaterialPayload({
                  keyName: question.MDG_GI_QUESTION_TYPE.replaceAll("(", "").replaceAll(")", "").replaceAll("/", "").replaceAll("-", "").replaceAll(".", "").split(" ").join(""),
                  data: "No",
                })
              );
            } else {
              dispatch(
                setSingleMaterialPayload({
                  keyName: question.MDG_GI_QUESTION_TYPE.replaceAll("(", "").replaceAll(")", "").replaceAll("/", "").replaceAll("-", "").replaceAll(".", "").split(" ").join(""),
                  data: "Medium",
                })
              );
            }
          } else {
            if (question?.MDG_GI_VISIBILITY === " Mandatory") {
              dispatch(setMatRequiredFieldsGI(question.MDG_GI_QUESTION_TYPE.replaceAll("(", "").replaceAll(")", "").replaceAll("/", "").replaceAll("-", "").replaceAll(".", "").split(" ").join("")));
            }
            dispatch(
              setSingleMaterialPayload({
                keyName: question.MDG_GI_QUESTION_TYPE.replaceAll("(", "").replaceAll(")", "").replaceAll("/", "").replaceAll("-", "").replaceAll(".", "").split(" ").join(""),
                data: "",
              })
            );
          }
        });
        dispatch(setGeneralInformation(questionsData));
      }
    };

    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(`/${destination_IDM}/rest/v1/invoke-rules`, "post", hSuccess, hError, payload);
    } else {
      doAjax(`/${destination_IDM}/v1/invoke-rules`, "post", hSuccess, hError, payload);
    }
  };

  const getDisplaydata = () => {
    const payload = {
      massCreationId: RequestId.slice(3), // Adjust `requestId` as needed
      massChangeId: "",
      screenName: "Mass Create",
      dtName: "MDG_MAT_FIELD_CONFIG",
      version: "v2",
    };

    const hSuccess = (data) => {
      const apiResponse = data.body; // Extract body from the API response
      const transformedPayload = transformApiResponseToReduxPayload(apiResponse);
      
      // Use the transformed payload wherever needed
      dispatch(setDisplayPayload({data:transformedPayload}));


      // function extractNumericKeys(obj) {
        const numericKeys = Object.keys(transformedPayload).filter(key => !isNaN(Number(key)));
        const extractedData = {};
        numericKeys.forEach(key => {
            extractedData[key] = transformedPayload[key];
        })
       dispatch(setMaterialRows(Object.values(extractedData)?.map(item=>item.headerData))) 

    };

    const hError = (error) => {
      console.error("Error fetching data:", error);
    };

    // Perform the API call
    doAjax(`/${destination_MaterialMgmt}/data/displayMassMaterial`, "post", hSuccess, hError, payload);
  };

  useEffect(() => {
    
    if (RequestId) {
      dispatch(setTabValue(1));
      getDisplaydata();
      setIsSecondTabEnabled(true)
      setIsAttachmentTabEnabled(true)
      setAddHardCodeData(true)
    }
  }, [RequestId, dispatch]);

  useEffect(() => {
    getCreateTemplate();
    getGITemplate();
    getAttachmentsIDM();
    dispatch(setTabValue(0))
    dispatch(setMaterialRows([])) 
  }, []);

  useEffect(() => {
    setPcNumber(idGenerator("MAT"));
  }, []);

  const getAttachmentsIDM = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_ATTACHMENTS_LIST_DT",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE": "Material",
          "MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO": "Mass Create",
          "MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE": 1,
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    setIsLoading(true);
    // const formData = new FormData();
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        let responseData = data?.data?.result[0]?.MDG_ATTACHMENTS_ACTION_TYPE;
        let templateData = [];
        responseData?.map((element, index) => {
          var tempRow = {
            id: index,
            // templateName: element?.MDG_CHANGE_TEMPLATE_NAME,
            // templateData: element?.MDG_CHANGE_TEMPLATE_FIELD_LIST,
          };
          templateData.push(tempRow);
        });
        setRuleData(templateData);
        const attachmentNames = data?.data?.result[0]?.MDG_ATTACHMENTS_ACTION_TYPE || [];

        // Update state with attachment names
        setAttachmentsData(attachmentNames);
      } else {
        // setMessageDialogTitle("Create");
        // setsuccessMsg(false);
        // setMessageDialogMessage("Creation Failed");
        // setHandleExtrabutton(false);
        // setMessageDialogSeverity("danger");
        // setMessageDialogOK(false);
        // setMessageDialogExtra(true);
        // handleMessageDialogClickOpen();
        // handleCreateDialogClose();
        // setIsLoading(false);
      }
      // handleClose();
    };

    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(`/${destination_IDM}/rest/v1/invoke-rules`, "post", hSuccess, hError, payload);
    } else {
      doAjax(`/${destination_IDM}/v1/invoke-rules`, "post", hSuccess, hError, payload);
    }
  };

  function groupBy(array, key) {
    return array.reduce((result, currentValue) => {
      (result[currentValue[key]] = result[currentValue[key]] || []).push(currentValue);
      return result;
    }, {});
  }
  function removeHiddenAndEmptyObjects(dataArray) {
    for (let i = 0; i < dataArray.length; i++) {
      let view = dataArray[i];
      view.cards = view.cards.filter((card) => card.cardDetails.some((item) => item.visibility !== "Hidden"));
      if (view.cards.length === 0) {
        dataArray.splice(i, 1);
        i--; // Adjust index after removing element
      }
    }
    return dataArray;
  }
  let mandatoryFields = [];
  const getCreateTemplate = () => {
    // console.log("i am called");
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_MAT_FIELD_CONFIG",
      version: "v2",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_SCENARIO": "Mass Create",
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    // setIsLoading(true);
    const hSuccess = (data) => {
      // console.log("dattt", data);
      // setIsLoading(false);
      if (data.statusCode === 200) {
        let responseData = data?.data?.result[0]?.MDG_MAT_FIELD_DETAILS_ACTION_TYPE;

        // Sort the response data by MDG_MAT_VIEW_SEQUENCE
        let sortedData = responseData.sort((a, b) => a.MDG_MAT_VIEW_SEQUENCE - b.MDG_MAT_VIEW_SEQUENCE);
        // Group the sorted data by MDG_MAT_VIEW_NAME
        const groupedFields = groupBy(sortedData, "MDG_MAT_VIEW_NAME");

        let view_data_array = [];

        Object.entries(groupedFields).forEach(([viewName, fields]) => {
          // Group the fields by MDG_MAT_CARD_NAME
          let groupedFieldsDataCardNameWise = groupBy(fields, "MDG_MAT_CARD_NAME");
          let cards = [];

          Object.entries(groupedFieldsDataCardNameWise).forEach(([cardName, cardFields]) => {
            // Sort cardFields by MDG_MAT_SEQUENCE_NO
            cardFields.sort((a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO);

            // Map the sorted cardFields to the required structure
            let cardDetails = cardFields.map((item) => ({
              fieldName: item.MDG_MAT_UI_FIELD_NAME,
              sequenceNo: item.MDG_MAT_SEQUENCE_NO,
              fieldType: item.MDG_MAT_FIELD_TYPE,
              maxLength: item.MDG_MAT_MAX_LENGTH,
              dataType: item.MDG_MAT_DATA_TYPE,
              viewName: item.MDG_MAT_VIEW_NAME,
              cardName: item.MDG_MAT_CARD_NAME,
              cardSeq: item.MDG_MAT_CARD_SEQUENCE,
              value: item.MDG_MAT_DEFAULT_VALUE,
              visibility: item.MDG_MAT_VISIBILITY,
              jsonName: item.MDG_MAT_JSON_FIELD_NAME,
            }));

            cards.push({ cardName, cardSeq: cardFields[0].MDG_MAT_CARD_SEQUENCE, cardDetails });
          });

          // Sort cards by cardSeq
          cards.sort((a, b) => a.cardSeq - b.cardSeq);
          console.log("cardssss", cards);
          view_data_array.push({ viewName, cards });
        });

        let getjsonWhichHaveValue = removeHiddenAndEmptyObjects(view_data_array);

        let main_viewHash = {};
        main_viewHash["viewData"] = getjsonWhichHaveValue;
        console.log("dattt10", main_viewHash);

        let transformedData = {};

        main_viewHash["viewData"].forEach((view) => {
          let cardData = {};
          view.cards.forEach((card) => {
            cardData[card.cardName] = card.cardDetails;

            if(view.viewName !== "Request Header") {
              card.cardDetails.forEach((detail) => {
                if (detail.visibility === "0") {
                  mandatoryFields.push(detail.jsonName); // Add fieldName to the array
                }
              });
            }

          });
          transformedData[view.viewName] = cardData;
        });
        setMandFields(mandatoryFields);
        let mtabsData = Object.keys(transformedData);

        mtabsData.map((items) => {
          if (items === "Basic Data") {
            dispatch(basicDataTabs(transformedData["Basic Data"]));
            console.log(transformedData["Basic Data"], "basicDataTabs")
          } else if (items === "Sales") {
            dispatch(salesDataTabs(transformedData["Sales"]));
          } else if (items === "Purchasing") {
            dispatch(purchasingDataTabs(transformedData["Purchasing"]));
          } else if (items === "MRP") {
            dispatch(mrpDataTabs(transformedData["MRP"]));
          } else if (items === "Accounting") {
            dispatch(accountingDataTabs(transformedData["Accounting"]));
          } else if (items === "Request Header") {
            dispatch(requestHeaderTabs(transformedData["Request Header"]));
          }
        });
      } else {
      }
    };
    const hError = (error) => {
      console.log(error);
    };
    if (applicationConfig.environment === "localhost") {
      doAjax(`/${destination_IDM}/rest/v1/invoke-rules`, "post", hSuccess, hError, payload);
    } else {
      doAjax(`/${destination_IDM}/v1/invoke-rules`, "post", hSuccess, hError, payload);
    }
  };

  return (
    <Box sx={{ padding: 2 }}>
      {
        requestIdHeader && (
          <Typography 
            variant="h6" 
            sx={{ mb: 1, textAlign: 'left', display: 'flex', alignItems: 'center', gap: 1 }}
          >
            <PermIdentityOutlinedIcon sx={{ fontSize: '1.5rem' }} />
           Request ID: <span style={{ fontWeight: 'bold' }}>NMM{requestIdHeader}</span>      
          </Typography>
        )
      }

      <Tabs value={tabValue} onChange={handleTabChange} sx={{ mb: 0.8}} aria-label="request form tabs">
      <Tab
        icon={<ListOutlinedIcon />}
        iconPosition="start"
        label="Request Header"
        sx={{ fontSize: 12, fontWeight: "bold" }}
      />
      <Tab
        icon={<ListAltOutlinedIcon />}
        iconPosition="start"
        label="Material List"
        sx={{ fontSize: 12, fontWeight: "bold" }}
        disabled={!isSecondTabEnabled}
      />
      <Tab
        icon={<CommentOutlinedIcon />}
        iconPosition="start"
        label="Attachments & Comments"
        sx={{ fontSize: 12, fontWeight: "bold" }}
        disabled={!isAttachmentTabEnabled}
      />
        {/* <Tab label="Request Change History" /> */}
      </Tabs>

      {tabValue === 0 && <RequestHeader setIsSecondTabEnabled={setIsSecondTabEnabled} setIsAttachmentTabEnabled={setIsAttachmentTabEnabled} />}

      {tabValue === 1 && <RequestDetails mandFields={mandFields} addHardCodeData={addHardCodeData} />}
      {/* {tabValue === 2 && <Box>Request Comments Content</Box>} */}
      {tabValue === 2 && <AttachmentsCommentsTab disabled={true} attachmentsData={attachmentsData} requestIdHeader={requestIdHeader ? requestIdHeader : RequestId} pcNumber={pcNumber} />}
      {tabValue === 3 && <Box>Request Change History Content</Box>}
    </Box>
  );
};

export default MassMaterialTableRequestBench;
