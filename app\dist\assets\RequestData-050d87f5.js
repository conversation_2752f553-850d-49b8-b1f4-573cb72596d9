import{r,a as e,j as n,aF as qe,T as z,B as He,ar as C,aw as I,M as l,I as De,aG as je,b7 as Pe,W as Ve,G as s,wT as u,at as ze,au as Be,av as ke,X as Qe,t as V,V as Ke,F as G,K as B,Z as k,$ as w,as as Ze,b as Xe,a8 as Je,s as et,pS as tt,cB as P,g as nt,l as f,A as rt,aj as lt,x as at,an as it,ao as st,f as ot,E as ct,ax as dt,aA as ut,aC as ht,y as ce,uM as de,h as Fe,wU as pt,al as Ct,aE as mt,aD as St,Y as ft,bF as gt,wV as Et}from"./index-17b8d91e.js";import{d as At}from"./CloudDownload-26349cbd.js";import{L as F}from"./LargeDropdown-a9f64e3a.js";import{S as we}from"./SingleSelectDropdown-29664b58.js";const Tt=({open:W,onClose:ue,handleSubmit:he=()=>{},title:$=u.CREATE_REQUEST,submitButtonText:pe=u.INITIATE_REQUEST,isLoading:b=!1})=>{var Ne;const[U,D]=r.useState("material"),[Ce,g]=r.useState([]),[Q,K]=r.useState([]),[Y,Z]=r.useState([]),[me,X]=r.useState(""),[Oe,S]=r.useState(null),[E,y]=r.useState({createdOn:null,top:{code:"10",desc:""}}),[q,Se]=r.useState([]),[J,L]=r.useState([]),[ee,O]=r.useState([]),[fe,T]=r.useState(!1),[A,ge]=r.useState(null),[H,Ee]=r.useState([]),[Ae,te]=r.useState([]),[Te,xe]=r.useState([]),[j,N]=r.useState([]),[_e,ne]=r.useState([]),[re,le]=r.useState([]),[Ie,ae]=r.useState([]),[Re,ie]=r.useState([]),ve=[{code:"material",desc:"Material"},{code:"cost_center",desc:"Cost Center"},{code:"profit_center",desc:"Profit Center"},{code:"general_ledger",desc:"General Ledger"},{code:"hierarchy",desc:"Hierarchy"}],ye=a=>({material:[{code:"1",desc:"MAT_MATERIAL_FIELD_CONFIG"},{code:"2",desc:"MAT_MATERIAL_FIELD_CONFIG"},{code:"3",desc:"MAT_MATERIAL_FIELD_CONFIG"},{code:"4",desc:"MAT_MATERIAL_FIELD_CONFIG"},{code:"5",desc:"MAT_MATERIAL_FIELD_CONFIG"}],cost_center:[{code:"1",desc:"CC_COST_CENTER_CONFIG"},{code:"2",desc:"CC_BUDGET_CONFIG"},{code:"3",desc:"CC_ALLOCATION_CONFIG"}],profit_center:[{code:"1",desc:"PC_PROFIT_CENTER_CONFIG"},{code:"2",desc:"PC_REVENUE_CONFIG"},{code:"3",desc:"PC_ANALYSIS_CONFIG"}],general_ledger:[{code:"1",desc:"GL_ACCOUNT_CONFIG"},{code:"2",desc:"GL_POSTING_CONFIG"},{code:"3",desc:"GL_BALANCE_CONFIG"}],hierarchy:[{code:"1",desc:"HIE_STRUCTURE_CONFIG"},{code:"2",desc:"HIE_LEVEL_CONFIG"},{code:"3",desc:"HIE_RELATIONSHIP_CONFIG"}]})[a]||[],se={fontSize:"12px",fontWeight:500},t=({children:a,sx:m})=>e(z,{sx:{...se,marginBottom:"4px",...m},children:a}),i=()=>{var x,R;T(!0);const a=v=>{O(v.body),T(!1)},m=v=>{T(!1),console.log(v)};B(`/${k}${(R=(x=w)==null?void 0:x.DATA_CLEANSE_APIS)==null?void 0:R.GET_MATERIAL_GRP}`,"get",a,m)},c=(a="")=>{var v,Ge;T(!0);let m={materialNo:a,salesOrg:"",top:200,skip:0};const x=Me=>{T(!1),Se(Me.body)},R=Me=>{console.log(Me)};B(`/${k}${(Ge=(v=w)==null?void 0:v.DATA_CLEANSE_APIS)==null?void 0:Ge.SEARCH_MAT_NO}`,"post",x,R,m)},p=()=>{var x,R;T(!0);const a=v=>{T(!1),L(v.body)},m=v=>{T(!1)};B(`/${k}${(R=(x=w)==null?void 0:x.DATA_CLEANSE_APIS)==null?void 0:R.GET_MAT_TYPE}`,"get",a,m)},o=()=>{Ee([{code:"1",desc:"Cost Center 1"},{code:"2",desc:"Cost Center 2"},{code:"3",desc:"Cost Center 3"}])},h=()=>{te([{code:"1",desc:"Profit Center 1"},{code:"2",desc:"Profit Center 2"},{code:"3",desc:"Profit Center 3"}])},d=()=>{xe([{code:"1",desc:"GL Account 1"},{code:"2",desc:"GL Account 2"},{code:"3",desc:"GL Account 3"}])},_=()=>{N([{code:"1",desc:"Hierarchy Level 1"},{code:"2",desc:"Hierarchy Level 2"},{code:"3",desc:"Hierarchy Level 3"}])},M=a=>{const m=a.target.value;if(A&&clearTimeout(A),m.length>=4){const x=setTimeout(()=>{c(m)},500);ge(x)}},oe=a=>{y({...E,createdOn:a})},We=(a,m)=>{if(m!==null){const x=m;let R={...E,[a]:x};y(R)}},$e=a=>{switch(D(a),be(),a){case"material":i(),p();break;case"cost_center":o();break;case"profit_center":h();break;case"general_ledger":d();break;case"hierarchy":_();break}},be=()=>{g([]),K([]),Z([]),X(""),S(null),ne([]),le([]),ae([]),ie([]),y({createdOn:null,top:{code:"10",desc:""}})},Le=()=>{be(),ue(),D("material")},Ue=[{code:"10",desc:""},{code:"20",desc:""},{code:"50",desc:""},{code:"100",desc:""},{code:"200",desc:""},{code:"500",desc:""},{code:"1000",desc:""}],Ye=()=>{switch(U){case"material":return n(G,{children:[n(s,{item:!0,xs:12,md:6,children:[e(t,{children:u.MATERIAL}),e(C,{size:"small",fullWidth:!0,children:e(Ze,{matGroup:q,selectedMaterialGroup:Ce,setSelectedMaterialGroup:g,isDropDownLoading:fe,placeholder:u.SELECT_MATERIAL,onInputChange:M,minCharacters:4})})]}),n(s,{item:!0,xs:12,md:6,children:[e(t,{children:u.MATERIAL_TYPE}),e(F,{matGroup:J,selectedMaterialGroup:Q,setSelectedMaterialGroup:K,placeholder:u.SELECT_MATERIAL_TYPE})]}),n(s,{item:!0,xs:12,md:6,children:[e(t,{children:u.MATERIAL_GROUP}),e(F,{matGroup:ee,selectedMaterialGroup:Y,setSelectedMaterialGroup:Z,placeholder:u.SELECT_MATERIAL_GROUP})]})]});case"cost_center":return n(G,{children:[n(s,{item:!0,xs:12,md:6,children:[e(t,{children:"Cost Center"}),e(F,{matGroup:H,selectedMaterialGroup:_e,setSelectedMaterialGroup:ne,placeholder:u.SELECT_COST_CENTER})]}),n(s,{item:!0,xs:12,md:6,children:[e(t,{children:u.CATEGORY}),e(C,{size:"small",fullWidth:!0,children:n(I,{value:"",onChange:()=>{},displayEmpty:!0,sx:{fontSize:"12px"},children:[e(l,{value:"",disabled:!0,children:"Select Category"}),e(l,{value:"production",children:"Production"}),e(l,{value:"administration",children:"Administration"}),e(l,{value:"sales",children:"Sales & Marketing"})]})})]}),n(s,{item:!0,xs:12,md:6,children:[e(t,{children:"Budget Range"}),e(C,{size:"small",fullWidth:!0,children:n(I,{value:"",onChange:()=>{},displayEmpty:!0,sx:{fontSize:"12px"},children:[e(l,{value:"",disabled:!0,children:"Select Budget Range"}),e(l,{value:"0-10000",children:"0 - 10,000"}),e(l,{value:"10000-50000",children:"10,000 - 50,000"}),e(l,{value:"50000+",children:"50,000+"})]})})]})]});case"profit_center":return n(G,{children:[n(s,{item:!0,xs:12,md:6,children:[e(t,{children:"Profit Center"}),e(F,{matGroup:Ae,selectedMaterialGroup:re,setSelectedMaterialGroup:le,placeholder:"Select Profit Center"})]}),n(s,{item:!0,xs:12,md:6,children:[e(t,{children:"Revenue Type"}),e(C,{size:"small",fullWidth:!0,children:n(I,{value:"",onChange:()=>{},displayEmpty:!0,sx:{fontSize:"12px"},children:[e(l,{value:"",disabled:!0,children:"Select Revenue Type"}),e(l,{value:"primary",children:"Primary Revenue"}),e(l,{value:"secondary",children:"Secondary Revenue"}),e(l,{value:"other",children:"Other Revenue"})]})})]}),n(s,{item:!0,xs:12,md:6,children:[e(t,{children:"Profit Margin Range"}),e(C,{size:"small",fullWidth:!0,children:n(I,{value:"",onChange:()=>{},displayEmpty:!0,sx:{fontSize:"12px"},children:[e(l,{value:"",disabled:!0,children:"Select Margin Range"}),e(l,{value:"0-10",children:"0% - 10%"}),e(l,{value:"10-25",children:"10% - 25%"}),e(l,{value:"25+",children:"25%+"})]})})]})]});case"general_ledger":return n(G,{children:[n(s,{item:!0,xs:12,md:6,children:[e(t,{children:"GL Account"}),e(F,{matGroup:Te,selectedMaterialGroup:Ie,setSelectedMaterialGroup:ae,placeholder:"Select GL Account"})]}),n(s,{item:!0,xs:12,md:6,children:[e(t,{children:"Account Type"}),e(C,{size:"small",fullWidth:!0,children:n(I,{value:"",onChange:()=>{},displayEmpty:!0,sx:{fontSize:"12px"},children:[e(l,{value:"",disabled:!0,children:"Select Account Type"}),e(l,{value:"asset",children:"Asset"}),e(l,{value:"liability",children:"Liability"}),e(l,{value:"equity",children:"Equity"}),e(l,{value:"revenue",children:"Revenue"}),e(l,{value:"expense",children:"Expense"})]})})]}),n(s,{item:!0,xs:12,md:6,children:[e(t,{children:"Balance Type"}),e(C,{size:"small",fullWidth:!0,children:n(I,{value:"",onChange:()=>{},displayEmpty:!0,sx:{fontSize:"12px"},children:[e(l,{value:"",disabled:!0,children:"Select Balance Type"}),e(l,{value:"debit",children:"Debit Balance"}),e(l,{value:"credit",children:"Credit Balance"}),e(l,{value:"zero",children:"Zero Balance"})]})})]})]});case"hierarchy":return n(G,{children:[n(s,{item:!0,xs:12,md:6,children:[e(t,{children:"Hierarchy Level"}),e(F,{matGroup:j,selectedMaterialGroup:Re,setSelectedMaterialGroup:ie,placeholder:"Select Hierarchy Level"})]}),n(s,{item:!0,xs:12,md:6,children:[e(t,{children:"Parent Node"}),e(C,{size:"small",fullWidth:!0,children:n(I,{value:"",onChange:()=>{},displayEmpty:!0,sx:{fontSize:"12px"},children:[e(l,{value:"",disabled:!0,children:"Select Parent Node"}),e(l,{value:"root",children:"Root"}),e(l,{value:"branch1",children:"Branch 1"}),e(l,{value:"branch2",children:"Branch 2"})]})})]}),n(s,{item:!0,xs:12,md:6,children:[e(t,{children:"Node Type"}),e(C,{size:"small",fullWidth:!0,children:n(I,{value:"",onChange:()=>{},displayEmpty:!0,sx:{fontSize:"12px"},children:[e(l,{value:"",disabled:!0,children:"Select Node Type"}),e(l,{value:"parent",children:"Parent Node"}),e(l,{value:"child",children:"Child Node"}),e(l,{value:"leaf",children:"Leaf Node"})]})})]})]});default:return null}};return r.useEffect(()=>{W&&(i(),p())},[W]),r.useEffect(()=>()=>{A&&clearTimeout(A)},[A]),e(G,{children:n(Ke,{open:W,onClose:Le,maxWidth:"md",fullWidth:!0,PaperProps:{sx:{borderRadius:2,minHeight:"400px"}},children:[n(qe,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",pb:1},children:[e(z,{variant:"h6",component:"div",children:$}),n(He,{sx:{display:"flex",alignItems:"center",gap:2},children:[e(C,{size:"small",sx:{minWidth:150},children:e(I,{value:U,onChange:a=>$e(a.target.value),sx:{fontSize:"12px"},children:ve.map(a=>e(l,{value:a.code,children:a.desc},a.code))})}),e(De,{edge:"end",color:"inherit",onClick:Le,"aria-label":"close",children:e(je,{})})]})]}),e(Pe,{}),e(Ve,{sx:{pt:3},children:n(s,{container:!0,spacing:3,children:[Ye(),n(s,{item:!0,xs:12,md:6,children:[e(t,{children:u.BUSINESS_RULE}),e(C,{size:"small",fullWidth:!0,children:n(I,{value:me,onChange:a=>X(a.target.value),displayEmpty:!0,sx:{fontSize:"12px"},children:[e(l,{value:"",disabled:!0,children:u.SELECT}),ye(U).map(a=>e(l,{value:a.code,children:a.desc},a.code))]})})]}),n(s,{item:!0,xs:12,md:6,children:[e(t,{children:u.NO_OBJECTS}),e(C,{size:"small",fullWidth:!0,children:e(we,{options:Ue,value:(Ne=E==null?void 0:E.top)==null?void 0:Ne.code,onChange:a=>We("top",a),placeholder:"Select Number of Objects",disabled:!1,minWidth:"90%",listWidth:210})})]}),n(s,{item:!0,xs:12,md:6,children:[e(t,{children:"Created On"}),e(C,{fullWidth:!0,sx:{padding:0},children:e(ze,{dateAdapter:Be,children:e(ke,{handleDate:oe,date:E==null?void 0:E.createdOn})})})]})]})}),e(Pe,{}),n(Qe,{sx:{p:2,gap:1},children:[e(V,{onClick:be,variant:"outlined",color:"primary",disabled:b,children:"Clear"}),e(V,{onClick:Le,variant:"outlined",color:"secondary",disabled:b,children:"Cancel"}),e(V,{onClick:()=>he(E),variant:"contained",color:"primary",disabled:b,sx:{minWidth:"120px"},children:b?"Creating...":pe})]})]})})},vt=()=>{var se;const[W,ue]=r.useState(null),[he,$]=r.useState(!1),[pe,b]=r.useState(""),[U,D]=r.useState(""),Ce=Xe(),[g,Q]=r.useState({createdOn:null}),[K,Y]=r.useState(!1),[Z,me]=r.useState(!1),[X,Oe]=r.useState(""),[S,E]=r.useState([]),[y,q]=r.useState([]),[Se,J]=r.useState(0),[L,ee]=r.useState(0),[O,fe]=r.useState((se=Je)==null?void 0:se.TOP_SKIP),T=et(),{showSnackbar:A}=tt(),ge=()=>{setSelectedCreatedBy([]),setSelectedMaterial([]),setSelectedDivision([]),setSelectedReqType([]),setSelectedOptions([]);let t={...rbSearchForm,reqPriority:""};T(commonFilterUpdate({module:"RequestBench",filterData:t})),T(commonFilterClear({module:"RequestBench",days:7})),setClearClicked(!0)},H={fontSize:"12px",fontWeight:500},Ee=P(nt)(({theme:t})=>({marginTop:"0px !important",border:`1px solid ${f.primary.border}`,borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.05)","&:not(:last-child)":{borderBottom:0},"&:before":{display:"none"}})),Ae=P(rt)(({theme:t})=>({minHeight:"2rem !important",margin:"0px !important",backgroundColor:f.primary.ultraLight,borderRadius:"8px 8px 0 0",transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:`${f.primary.light}20`}})),te=P(V)({borderRadius:"4px",padding:"4px 12px",textTransform:"none",fontSize:"0.875rem"}),Te=P(s)({padding:"0.75rem",gap:"0.5rem"}),xe=P(s)({display:"flex",justifyContent:"flex-end",paddingRight:"0.75rem",paddingBottom:"0.75rem",paddingTop:"0rem",gap:"0.5rem"}),j=P(z)({fontSize:"0.75rem",color:f.primary.dark,marginBottom:"0.25rem",fontWeight:500});r.useEffect(()=>{N()},[]),r.useEffect(()=>q([...S]),[S]);const N=(t="")=>{var o,h;const i={page:t==="pagination"?L:0,size:t==="pagination"?O:100,orderBy:"creationDate"};D(!0);const c=d=>{E((d==null?void 0:d.body)||[]),D(!1)},p=d=>{E([]),D(!1)};B(`/${k}${(h=(o=w)==null?void 0:o.DATA_CLEANSE_APIS)==null?void 0:h.CLEANSING_REQ}`,"post",c,p,i)},_e=t=>{var o,h;$(!0);let i={requestId:t};const c=d=>{var oe;const _=URL.createObjectURL(d),M=document.createElement("a");M.href=_,M.setAttribute("download",`${t}_Data Cleanse.pdf`),document.body.appendChild(M),M.click(),document.body.removeChild(M),URL.revokeObjectURL(_),$(!1),b(""),A(`${t}${(oe=u)==null?void 0:oe.EXPORT_SUCCESS}`,"success")},p=()=>{$(!1),b(""),A(`Failed exporting ${t}_Data Cleanse.pdf`,"error")};B(`/${k}${(h=(o=w)==null?void 0:o.DATA_CLEANSE_APIS)==null?void 0:h.DOWNLOAD_PDF}`,"postandgetblob",c,p,i)},ne=[{code:"Material",desc:""},{code:"Cost Center",desc:""},{code:"Profit Center",desc:""},{code:"General Ledger",desc:""},{code:"Hierarchy",desc:""}],re=t=>{var o,h,d;let i={RequestId:"",ObjectCount:0,UserId:"<EMAIL>",InitiatedOn:ce(new Date).format("YYYY-MM-DDThh:mm:ss")??"",Status:"In-Progress",Top:((o=t==null?void 0:t.top)==null?void 0:o.code)||"10",Skip:"0",FromDate:ce(t==null?void 0:t.createdOn[0]).format("YYYY-MM-DDThh:mm:ss")??"",ToDate:ce(t==null?void 0:t.createdOn[1]).format("YYYY-MM-DDThh:mm:ss")??"",DtName:"MDG_MAT_MATERIAL_FIELD_CONFIG",DtVersion:"v4",DtRegion:"US",DtScenario:"Create",DtMaterialType:"FERT"};const c=_=>{A(_==null?void 0:_.message,"success"),N(),Y(!1)},p=_=>{A(_.message||u.ERROR,"error")};B(`/${k}${(d=(h=w)==null?void 0:h.DATA_CLEANSE_APIS)==null?void 0:d.INITIATE_DATA_QUALITY_CHECK}`,"post",c,p,[i])},le=(t,i)=>({field:t,headerName:i,editable:!1,flex:1.4,renderCell:c=>e(gt,{sx:{justifyContent:"flex-start",borderRadius:"4px",color:"#000",width:"100%",minWidth:"4.6rem",fontSize:"12px",background:f.statusColorMap[c.row.status.toLowerCase().replace(/[^a-z0-9]/gi,"")]||f.statusColorMap.default},label:c.row.status})}),Ie=t=>{Q({...g,createdOn:t})},ae=t=>{if(t.target.value!==null){const i=t.target.name,c=t.target.value;let p={...g,[i]:c};Q(p)}},Re=t=>{if(!t){q([...S]),J(S==null?void 0:S.length);return}const i=S==null?void 0:S.filter(c=>{var h;let p=!1,o=Object.keys(c);for(let d=0;d<o.length&&(p=c[o[d]]?(c==null?void 0:c[o==null?void 0:o[d]])&&((h=c==null?void 0:c[o==null?void 0:o[d]].toString().toLowerCase())==null?void 0:h.indexOf(t==null?void 0:t.toLowerCase()))!=-1:!1,!p);d++);return p});q([...i]),J(i==null?void 0:i.length)},ie=t=>{const i=t.target.value;fe(i),ee(0)},ve=(t,i)=>{ee(isNaN(i)?0:i)};r.useEffect(()=>{L!==0&&O*(L+1)>S.length&&S.length%O===0&&N("pagination")},[L]);const ye=[{field:"requestId",headerName:"Request ID",flex:1.5},{field:"module",headerName:"Module",flex:1,renderCell:t=>t.row.module||"Material"},{field:"userId",headerName:"Initiated By",flex:1},{field:"creationDate",headerName:"Initiated On",flex:1,renderCell:t=>{var i;return ce((i=t==null?void 0:t.row)==null?void 0:i.initiatedOn).format("YYYY-MM-DD hh:mm")??""}},{field:"objectCount",headerName:"Object Count",flex:1},le(Et,"Status"),{field:"actions",headerName:"Actions",flex:1,headerAlign:"center",align:"center",renderCell:t=>{var i,c,p;return n("div",{children:[e(Fe,{title:"Report",children:e(De,{disabled:((i=t==null?void 0:t.row)==null?void 0:i.status)===`${de.DRAFT}`,onClick:()=>{var o,h,d;if((o=t==null?void 0:t.row)!=null&&o.objectCount){_e((h=t==null?void 0:t.row)==null?void 0:h.requestId);return}A((d=u)==null?void 0:d.FAILED_FETCHING_DATA,"error")},children:e(At,{sx:{color:((c=t==null?void 0:t.row)==null?void 0:c.status)===`${de.DRAFT}`?"#808080":"#ffd93f"}})})}),e(Fe,{title:"View Requests",children:e(De,{disabled:((p=t==null?void 0:t.row)==null?void 0:p.status)===`${de.DRAFT}`,onClick:()=>{var o,h,d;if((o=t==null?void 0:t.row)!=null&&o.objectCount){Ce(`/dataCheck/data?requestId=${(h=t==null?void 0:t.row)==null?void 0:h.requestId}`,{state:t==null?void 0:t.row});return}A((d=u)==null?void 0:d.FAILED_FETCHING_DATA,"error")},children:e(pt,{sx:{fontSize:"20px",color:t.row.status===de.DRAFT?"#808080":`${f.blue.indigo}`}})})})]})}}];return n("div",{style:{...lt,backgroundColor:"#FAFCFF"},children:[n(at,{spacing:1,children:[e(s,{container:!0,mt:0,sx:Ct,children:n(s,{item:!0,md:4,children:[e(z,{variant:"h3",children:e("strong",{children:u.DATA_CLEANSE})}),e(z,{variant:"body2",color:"#777",children:"This view displays the list of Data Cleanse Requests"})]})}),e(s,{container:!0,sx:{paddingBottom:"10px"},children:e(s,{item:!0,md:12,children:n(Ee,{expanded:Z,onChange:(t,i)=>me(i),sx:{mb:2},children:[n(Ae,{expandIcon:e(it,{sx:{fontSize:"1.25rem",color:f.primary.main}}),"aria-controls":"panel1a-content",id:"panel1a-header",children:[e(st,{sx:{fontSize:"1.25rem",marginRight:1,color:f.primary.main}}),e(z,{sx:{fontSize:"0.875rem",fontWeight:600,color:f.primary.dark},children:u.FILTER})]}),n(ot,{sx:{padding:0},children:[e(Te,{children:n(s,{container:!0,rowSpacing:1,spacing:2,alignItems:"center",sx:{padding:"0rem 1rem 0.5rem"},children:[n(s,{item:!0,md:2,children:[e(j,{sx:H,children:"Module"}),e(C,{size:"small",fullWidth:!0,children:e(we,{options:ne,value:X,onChange:t=>{Oe(t)},placeholder:"Select Module",disabled:!1,minWidth:"90%",listWidth:210})})]}),n(s,{item:!0,md:2,children:[e(j,{sx:H,children:"Request ID"}),e(ct,{name:"requestId",fullWidth:!0,variant:"outlined",placeholder:"Request ID",size:"small",InputLabelProps:{shrink:!0},onChange:ae,value:g==null?void 0:g.requestId})]}),n(s,{item:!0,md:2,children:[e(j,{sx:H,children:"Created On"}),e(C,{fullWidth:!0,sx:{padding:0},children:e(ze,{dateAdapter:Be,children:e(ke,{handleDate:Ie,date:g==null?void 0:g.createdOn})})})]})]})}),n(xe,{children:[e(te,{variant:"outlined",size:"small",startIcon:e(dt,{sx:{fontSize:"1rem"}}),onClick:ge,sx:{borderColor:f.primary.main,color:f.primary.main},children:"Clear"}),e(te,{variant:"contained",size:"small",startIcon:e(ut,{sx:{fontSize:"1rem"}}),onClick:()=>re(g),sx:{backgroundColor:f.primary.main},children:"Search"})]})]})]})})}),e(ht,{isLoading:U,module:"DataCleanse",width:"100%",title:"Data Cleanse Requests",rows:y||[],columns:ye,onSearch:t=>Re(t),onRefresh:N,page:L,showSearch:!0,showRefresh:!0,pageSize:O,rowCount:Se??(y==null?void 0:y.length)??0,onPageChange:ve,onPageSizeChange:ie,getRowIdValue:"id",hideFooter:!0,showCustomNavigation:!0})]}),e(St,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:e(mt,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:W,onChange:t=>{ue(t)},children:e(V,{size:"small",variant:"contained",onClick:()=>{Y(!0)},children:u.CLEANSE_REQUEST})})}),e(Tt,{open:K,onClose:()=>Y(!1),handleSubmit:re,title:u.NEW_REQUEST,submitButtonText:u.INITIATE_REQUEST,isLoading:!1}),e(ft,{blurLoading:he,loaderMessage:pe})]})};export{vt as default};
