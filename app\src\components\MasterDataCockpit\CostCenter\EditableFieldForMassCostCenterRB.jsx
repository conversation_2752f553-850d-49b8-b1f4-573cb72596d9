import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  TextField,
  Grid,
  Autocomplete,
  IconButton,
  FormControlLabel,
  Checkbox,
} from "@mui/material";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import SaveIcon from "@mui/icons-material/Save";
import { DateRangePicker } from "rsuite";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";

import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { setPayload, setPayloadWhole } from "../../../app/editPayloadSlice";
import { setCCRequiredFields, setMultipleCostCenterData, setSingleCostCenterPayload } from "../../../app/costCenterTabsSlice";
import { doAjax } from "../../Common/fetchService";
import { destination_CostCenter } from "../../../destinationVariables";
import { setDropDown } from "../../../app/dropDownDataSlice";
import moment from "moment/moment";
import { setMultipleProfitCenterData } from "../../../app/profitCenterTabsSlice";
function transformApiData(apiValue, dropDownData) {
  if (Array.isArray(dropDownData)) {
    const matchingOption = dropDownData.find(
      (option) => option.code === apiValue
    );
    return matchingOption || "";
  } else {
    return "";
  }
}
const EditableFieldForMassCostCenterRB = ({
  label,
  value,
  length,
  units,
  onSave,
  fieldGroup,
  isEditMode,
  activeTabIndex,
  visibility,
  ccTabs,
  selectedRowData,
  type,
}) => {
  const [editedValue, setEditedValue] = useState(value);
  const [changeStatus, setChangeStatus] = useState(false);
  const dropDownData = useSelector((state) => state.AllDropDown.dropDown);
  const [error,setError]=useState(false);

  const costCenterData = useSelector(
    (state) => state.costCenter.MultipleCostCenterData
  );
  const dispatch = useDispatch();
  const transformedValue = transformApiData(editedValue, dropDownData);
  const editField = useSelector((state) => state.edit.payload);
  let activeRow = {};
  let activeIndex = -1;

  for (let index = 0; index < costCenterData?.length; index++) {
    if (costCenterData[index].costCenter === selectedRowData) {
      activeRow = costCenterData[index];
      activeIndex = index;
      break;
    }
  }
  let activeTabName = ccTabs[activeTabIndex];
  const getValueForFieldName = (data, fieldName) => {
    const field = data?.find((field) => field?.fieldName === fieldName);
    return field ? field.value : "";
  };
  const costCenterInnerData = costCenterData[activeIndex];

  let key = label
    .replaceAll("(", "")
    .replaceAll(")", "")
    .replaceAll("/", "")
    .replaceAll("-", "")
    .replaceAll(".", "")
    .split(" ")
    .join("");

  useEffect(() => {
    setEditedValue(value);
  }, [value]);
  useEffect(() => {
    if (
      visibility === "0" ||
      visibility === "Required"
    ) {
      if(activeTabName === 'Basic Data'){
        //console.log("active_tab_coming")
        dispatch(setCCRequiredFields(key));
        dispatch(setCCRequiredFields(["Name"]));
      }else{
        console.log(key,"+++++++++++++++key")
        dispatch(setCCRequiredFields(key));
      }
      
    }
  }, [activeTabName]);

  const fieldData = {
    label,
    value: editedValue,
    units,
    type,
    visibility
  };


  const onEdit = (label, newValue) => {
    console.log("fieldGroup", fieldGroup, label, newValue);
    dispatch(
      setPayload({
        keyname: key
          .replaceAll("(", "")
          .replaceAll(")", "")
          .replaceAll("/", "")
          .replaceAll("-", "")
          .replaceAll(".", "")
          .split(" ")
          .join(""),
        data: newValue,
      })
    )

    let changedData = costCenterData?.map((item, index) => {
      let activeTabName = ccTabs[activeTabIndex];
      if (index === activeIndex) {
        let temp0 = item.viewData;
        let temp = item.viewData[activeTabName]; //Basic Data
        console.log("temp", temp);
        let temp2 = item.viewData[activeTabName][fieldGroup]; //names
        console.log("temp2", temp2);
        return {
          ...item,
          viewData: {
            ...temp0,
            [activeTabName]: {
              ...temp,
              [fieldGroup]: temp2?.map((innerItem) => {
                if (innerItem.fieldName === label) {
                  return { ...innerItem, value: newValue };
                  // console.log('...inneritem', ...innerItem)
                } else {
                  return innerItem;
                }
              }),
            },
          },
        };
      } else {
        return item;
      }
    });
    console.log("changedData", changedData);
    dispatch(setMultipleCostCenterData(changedData));
  };



  
console.log('costinnerdata', costCenterInnerData)
  const getCurrency = (newValue) => {
    console.log("compcode", newValue);
    const hSuccess = (data) => {
      console.log("value", data);
      /*dispatch(
        setPayload({
          keyname: "Currency",
          data: "",
        })
      );*/
      dispatch(setDropDown({ keyName: "Currency", data: data.body }));
    };
    const hError = (error) => {
      console.log(error, "error in dojax");
    };
    doAjax(
      `/${destination_CostCenter}/data/getCurrency?companyCode=${newValue?.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCurrencyBasedOnCompCode = (newValue) => {
    console.log("compcode", newValue);
    const hSuccess = (data) => {
      console.log("value", data);
      /*dispatch(
        setPayload({
          keyname: "Currency",
          data: "",
        })
      );*/
      dispatch(setDropDown({ keyName: "Currency", data: data.body }));
    };
    const hError = (error) => {
      console.log(error, "error in dojax");
    };
    doAjax(
      `/${destination_CostCenter}/data/getCurrency?companyCode=${newValue}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getRegion = (newValue) => {
    console.log("countryyyyy", newValue);
    const hSuccess = (data) => {
      console.log("value", data);
      dispatch(
        setPayload({
          keyname: "Region",
          data: "",
        })
      );
      dispatch(setDropDown({ keyName: "Region", data: data.body }));
    };
    const hError = (error) => {
      console.log(error, "error in dojax");
    };
    doAjax(
      `/${destination_CostCenter}/data/getRegionBasedOnCountry?country=${newValue?.code}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getRegionBasedOnCountry = (newValue) => {
    console.log("countryyyyy", newValue);
    const hSuccess = (data) => {
      console.log("value", data);
      dispatch(
        setPayload({
          keyname: "Region",
          data: "",
        })
      );
      dispatch(setDropDown({ keyName: "Region", data: data.body }));
    };
    const hError = (error) => {
      console.log(error, "error in dojax");
    };
    doAjax(
      `/${destination_CostCenter}/data/getRegionBasedOnCountry?country=${newValue}`,
      "get",
      hSuccess,
      hError
    );
  };

  useEffect(() => {
    
    if (label === "Country/Reg") {
      getRegionBasedOnCountry(editedValue);
    }
    if (label === "Company Code") {
      getCurrencyBasedOnCompCode(editedValue);
    }
  }, []);

  return (
    <Grid item>
      <Stack>
        {isEditMode ? (
          <>
            <Typography variant="body2" color="#777">
              {label}{" "}
              {visibility === "Required" || visibility === "0" ? (
                <span style={{ color: "red" }}>*</span>
              ) : (
                ""
              )}
            </Typography>
            {type === "Drop Down" ? (
              <Autocomplete
                options={dropDownData[key] ?? []}
                value={
                  (getValueForFieldName(
                    costCenterInnerData.viewData[activeTabName][fieldGroup],
                    label
                  ) &&
                    dropDownData[key]?.filter(
                      (x) =>
                        x.code ===
                        getValueForFieldName(
                          costCenterInnerData.viewData[activeTabName][
                            fieldGroup
                          ],
                          label
                        )
                    ) &&
                    dropDownData[key]?.filter(
                      (x) =>
                        x.code ===
                        getValueForFieldName(
                          costCenterInnerData.viewData[activeTabName][
                            fieldGroup
                          ],
                          label
                        )
                    )[0]) ||
                  ""
                }
                onChange={(event, newValue) => {
                  /*if(visibility === "Required" || visibility === "0"){
                    //alert("coming")
                    if(newValue === null)
                    setError(true)
                  }*/
                  if (label === "Comp Code") {
                    getCurrency(newValue);
                  }
                  if (label === "Country/Reg") {
                    getRegion(newValue);
                  }
                  onEdit(label, newValue?.code);
                  console.log("newValue", newValue);
                  setEditedValue(newValue?.code);
                  setChangeStatus(true);
                  console.log("keys", key);
                }}
                getOptionLabel={(option) => {
                  console.log("optionn", option);
                  return option === "" || option?.code === ""
                    ? ""
                    : `${option?.code} - ${option?.desc}` ?? "";
                }}
                renderOption={(props, option) => {
                  console.log("option vakue", option);
                  return (
                    <li {...props}>
                      <Typography style={{ fontSize: 12 }}>
                        {`${option?.code} - ${option?.desc}`}
                      </Typography>
                    </li>
                  );
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    variant="outlined"
                    placeholder={`Select ${fieldData.label}`}
                    size="small"
                    label={null}
                    error={error}
                  />
                )}
              />
            ) : type === "Input" ? (
              <TextField
                variant="outlined"
                size="small"
                value={getValueForFieldName(
                  costCenterInnerData.viewData[activeTabName][fieldGroup],
                  label
                ).toUpperCase()}
                placeholder={`Enter ${fieldData.label}`}
                inputProps={{maxLength: length}}
                onChange={(event) => {
                  const newValue = event.target.value;
                  if (newValue.length > 0 && newValue[0] === " ") {
                    onEdit(label,newValue.trimStart())
                  } else {
                    let costCenterUpperCase = newValue.toUpperCase();
                    onEdit(label,costCenterUpperCase.trimStart())
                  }
                  /*if(visibility === "Required" || visibility === "0"){
                    if(newValue.length <= 0)
                    setError(true)
                  }*/
                }}
                error={error}
              />
            ) : type === "Calendar" ? (
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  slotProps={{ textField: { size: "small" } }}
                  value={editedValue}
                  placeholder="Select Date Range"
                  onChange={(newValue) => {
                    onEdit(newValue);
                    setEditedValue(newValue);
                  }}
                  // required={
                  //   props.details.visibility === "0" ||
                  //   props.details.visibility === "Required"
                  // }
                />
              </LocalizationProvider>
            ) : type === "Radio Button" ? (
              <Grid item md={2}>
                <Checkbox
                  sx={{ padding: 0 }}
                  checked={getValueForFieldName(
                    costCenterInnerData.viewData[activeTabName][fieldGroup],
                    label
                  ) == true}
                  onChange={(e) => {
                    console.log('oncheckbox', label, e.target.checked)
                    onEdit(label, e.target.checked);
                    // setEditedValue(newValue);
                  }}
                />
              </Grid>
            ) : (
              ""
            )}
          </>
        ) : (
          <>
            <>
              <Typography variant="body2" color="#777">
                {label}{" "}
                {visibility === "Required" || visibility === "0" ? (
                  <span style={{ color: "red" }}>*</span>
                ) : (
                  ""
                )}
              </Typography>
              <Typography variant="body2" fontWeight="bold">
                {editedValue}
                {type === "Radio Button" ? (
                  <Checkbox
                    sx={{ padding: 0 }}
                    checked={editedValue}
                    disabled
                  />
                ) : (
                  ""
                )}
              </Typography>
            </>
          </>
        )}
      </Stack>
    </Grid>
  );
};

export default EditableFieldForMassCostCenterRB;
