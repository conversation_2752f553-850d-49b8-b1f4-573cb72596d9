import {
  Box,
  Grid,
  Typography,
} from "@mui/material";
import { useEffect, useState } from "react";
import { container_Padding } from "../common/commonStyles";
import FilterFieldGlobal from "./FilterFieldGlobal";

const GenericTabsGlobal = (props) => {
  let filterFields = props?.basicDataTabDetails && Object?.entries(props?.basicDataTabDetails);
  console.log("basic", filterFields);
  console.log("propdewrwrew",props)
  const [basicJsx, setbasicJsx] = useState([]);
 
  useEffect(() => {
    setbasicJsx(
      filterFields?.map((item) => {
        return (
          <Grid
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              borderRadius: "8px",
              border: "1px solid #E0E0E0",
              mt: 0.5,
              mb: 1,
              boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
              ...container_Padding,
            }}
            key={item[0]}
          >
            <Grid container>
              <Typography
                sx={{
                  fontSize: "12px",
                  fontWeight: "700",
                  paddingBottom:"10px",
                }}
              >
                {item[0]}
              </Typography>
            </Grid>
            <Box>
              <Grid container spacing={1} paddingBottom={1} >
                {[...item[1]]
                  .filter((x) => x.visibility != "Hidden")
                  .sort((a, b) => a.sequenceNo - b.sequenceNo)
                  ?.map((innerItem) => {
                    const isIndividuallyDisabled = ["Description", "CompanyCode","GLname","Accounttype","AccountGroup"].includes(innerItem.jsonName);
                    return (
                      <FilterFieldGlobal
                          key={innerItem.fieldName}
                          disabled={props?.disabled|| isIndividuallyDisabled}
                          field={innerItem}
                          dropDownData={props.dropDownData}
                          uniqueId={props?.uniqueId}
                          viewName={props?.activeViewTab}
                          selectedRow={props?.selectedRow}
                          module={props?.module}
                      />
                    );
                  })}
              </Grid>
            </Box>
          </Grid>
        );
      })
    );
  }, [props?.basicDataTabDetails, props.activeViewTab, props?.uniqueId]);

  return <>{basicJsx}</>;
};

export default GenericTabsGlobal;