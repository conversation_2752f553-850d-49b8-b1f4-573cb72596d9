import React, { useEffect, useState } from "react";
import ReusableSnackBar from "../../Common/ReusableSnackBar";
import {
  BottomNavigation,
  Box,
  Button,
  CardContent,
  Grid,
  IconButton,
  FormLabel,
  Paper,
  Stack,
  Tab,
  Radio,
  Tabs,
  RadioGroup,
  Typography,
  FormControlLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  FormControl,
  TextField,
  Autocomplete,
  Stepper,
  Step,
  StepLabel,
  Card,
  Backdrop,
  CircularProgress,
  Alert,
  Tooltip,
} from "@mui/material";
import lookup from "../../../data/lookup.json";
import CloseIcon from "@mui/icons-material/Close";
import MarkunreadOutlinedIcon from "@mui/icons-material/MarkunreadOutlined";
import {
  DateField,
  DatePicker,
  DesktopDatePicker,
  DesktopDateTimePicker,
  LocalizationProvider,
} from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import {
  button_Outlined,
  iconButton_SpacingSmall,
  container_Padding,
  outermostContainer_Information,
  button_Primary,
} from "../../common/commonStyles";
import ReusableTable from "../../Common/ReusableTable";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import { checkIwaAccess, exportFileAsPdf, formValidator, idGenerator, saveExcel } from "../../../functions";
import { useDispatch, useSelector } from "react-redux";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import { useLocation, useNavigate } from "react-router-dom";
import {
  destination_DocumentManagement,
  destination_GeneralLedger,
  destination_IDM,
} from "../../../destinationVariables";
import { doAjax, promiseAjax } from "../../Common/fetchService";
// import EditableFieldForCostCenter from "./EditableFieldForCostCenter";
import { setDropDown } from "../../../app/dropDownDataSlice";
import moment from "moment/moment";
import ReusableDialog from "../../Common/ReusableDialog";
import ReusableAttachementAndComments from "../../Common/ReusableAttachmentAndComments/ReusableAttachementAndComments";
import {
  Timeline,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineItem,
  TimelineSeparator,
  timelineItemClasses,
} from "@mui/lab";
import TrackChangesTwoToneIcon from "@mui/icons-material/TrackChangesTwoTone";
import LoupeOutlinedIcon from '@mui/icons-material/LoupeOutlined';
import { ArrowCircleLeftOutlined, CheckCircleOutlineOutlined, InfoOutlined } from "@mui/icons-material";
import { MatDownload, MatView } from "../../DocumentManagement/UtilDoc";
// import EditableFieldForGL from "./EditableFieldForGL";
import FormatListBulletedOutlinedIcon from "@mui/icons-material/FormatListBulletedOutlined";

import {
  clearGeneralLedger,
  clearSingleGLPayloadGI,
  setGLRequiredFieldsGI,
  setGeneralLedgerViewData,
  setGlGIPayload,
  setSinglegeneralLedgerPayload,
  setSinglegeneralLedgerPayloadGI,
} from "../../../app/generalLedgerTabSlice";
import { setGeneralInformation, setPayload, setPayloadForNewChange, setPayloadWhole } from "../../../app/editPayloadSlice";
import ChangeLog from "../../Changelog/ChangeLog";
import LoadingComponent from "../../Common/LoadingComponent";
import { Zoom } from '@mui/material';
import ReusableIcon from "../../Common/ReusableIcon";
import applicationConfigReducer from "../../../app/applicationConfigReducer";
import { clearTaskData } from "../../../app/userManagementSlice";
import ReusableBackDrop from "../../Common/ReusableBackDrop";
import { clearCostCenterPayload } from "../../../app/costCenterTabSliceET";
import { clearProfitCenterPayload } from "../../../app/profitCenterTabsSlice";
import { clearCostCenter } from "../../../app/costCenterTabsSlice";
import {
  CheckCircleOutline,
  WarningAmberOutlined,
} from "@mui/icons-material";


const DisplayGeneralLedger = () => {
  const [remarksError, setRemarksError] = useState(false)
  const [isEditMode, setIsEditMode] = useState(true);
  const [activeTab, setActiveTab] = useState(0);
  const [isDisplayMode, setIsDisplayMode] = useState(false);
  const [responseFromAPI, setResponseFromAPI] = useState({});
  const [factorsArray, setFactorsArray] = useState([]);
  const [activeStep, setActiveStep] = useState(0);
  // const [compCode, setCompanyCode] = useState();
  const [costCenterDetails, setCostCenterDetails] = useState([]);
  const [iDs, setIds] = useState();
  const [dupliDialog, setDupliDialog] = useState(false);
  const [value, setValue] = useState([]);
  const [messageDialogTitle, setMessageDialogTitle] = useState('');
  const [successMsg, setsuccessMsg] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [messageDialogSeverity, setMessageDialogSeverity] = useState(false);
  const [messageDialogOK, setMessageDialogOK] = useState(true);
  const [messageDialogExtra, setMessageDialogExtra] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [validateFlag, setValidateFlag] = useState(false);
  const [remarks, setRemarks] = useState("");
  const [attachments, setAttachments] = useState([]);
  const [openCorrectionDialog, setOpenCorrectionDialog] = useState(false);
  const [comments, setComments] = useState([]);
  const [openRemarkDialog, setOpenRemarkDialog] = useState(false);
  const [testrunStatus, setTestrunStatus] = useState(true);
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [submitForReviewDisabled, setSubmitForReviewDisabled] = useState(true);
  const [handleExtrabutton, setHandleExtrabutton] = useState(false);
  const [isChangeLogopen, setisChangeLogopen] = useState(false);
  const [handleExtraText, setHandleExtraText] = useState("");
  const [apiCount, setApiCount] = useState(0);
  const [dialogType, setDialogType] = useState("");
  const [pageName, setPageName] = useState("");
  const [formValidationErrorItemsGI, setFormValidationErrorItemsGI] = useState([]);
  const [formValidationErrorItems, setFormValidationErrorItems] = useState([]);
  const [glNumber, setGlNumber] = useState("");
  const [openSnackbarValidation, setOpenSnackbarValidation] = useState(false);
  const [attachmentFromIdm, setAttachmentFromIdm] = useState([])
  const [questions, setQuestions] = useState([]);
  const [fercRows, setFercRows] = useState([])
  const [gLValidationErrors, setGLValidationErrors] = useState([])
  const [dialogOpen, setDialogOpen] = useState(false);
  const [openDraftDialog, setOpenDraftDialog] = useState(false);
  const [isFocused, setIsFocused] = useState(false);


  const [openCreateDetails, setOpenCreateDetails] = useState(false)

  const [createdDetailsField, setCreatedDetailsfield] = useState([])
  const [createdRowDetailsField, setCreatedRowDetailsField] = useState([])

  const [buttonsIDM, setButtonsIDM] = useState([]);
  const [dialogTitle, setDialogTitle] = useState("");
  const [dialogInput, setDialogInput] = useState("");
  const [dialogSnackbarMsg, setDialogSnackbarMsg] = useState("");
  const [isMandatory, setIsMandatory] = useState(false);
  const [userInput, setUserInput] = useState("");
  const [currentActionType, setCurrentActionType] = useState("");
  const [showAlert, setShowAlert] = useState(false);
  const [sendBackDialogOpen, setSendBackDialogOpen] = useState(false);
  const [sendBackReason, setSendBackReason] = useState('');
  const [validateDialogOpen, setValidateDialogOpen] = useState(false);
  const [validationDialogMessage, setValidationDialogMessage] = useState(false);
  const [validationMessageDialogSeverity, setValidationMessageDialogSeverity] = useState(false);
  const [validationMessageDialogOK, setValidationMessageDialogOK] = useState(false);
  const [validationSuccessMsg, setValidationSuccessMsg] = useState(false);
  const [toastType, setToastType] = useState('');
  const [isToastPromise, setIsToastPromise] = useState(false);
  const [numberChange, setNumberChange] = useState(false);
  const [filteredButtons, setFilteredButtons] = useState([]);
  const [dialogOkText, setDialogOkText] = useState("")
  const [inputText, setTextInput] = useState(false);
  const [companyCodeRow, setCompanyCodeRow] = useState([])

  const [openCompanyCode, setOpenCompanyCode] = useState(false)

  const [currentButtonState, setCurrentButtonState] = useState({});

  const [glCreateErrorData, setGlCreateErrorData] = useState([])
  const [openGlErrorTable, setOpenGlErrorTable] = useState(false)

  const [dialogContentForFMDDetails, setDialogContentForFMDDetails] = useState('')

  const [openDialogforFerc, setOpenDialogForFerc] = useState(false); // Initially open the dialog
  const [openFercTable, setOpenFercTable] = useState(false)
  const [openMessageDialogshort, setOpenMessageDialogshort] = useState(false)
  const [openMessageDialogForValidate, setOpenMessageDialogForValidate] = useState(false)
  const [openSnackbarValidate, setopenSnackbarValidate] = useState(false);
  const [openMessageDialogFor5161ShortLongChange, setOpenMessageDialogFor5161ShortLongChange] = useState(false)
  const [gLAccountFor5161Series, setGLAccountFor5161Series] = useState([])
  const [openMessageDialogForTrading, setOpenMessageDialogForTrading] = useState(false)
  const [openMessageDialogForTradingpresent, setOpenMessageDialogForTradingPresent] = useState(false)
  const [saveAsDraftState, setSaveAsDraftState] = useState(false)
  const handleCloseDialogFerc = () => {
    setOpenDialogForFerc(false); // Close dialog after user confirms
  };
  const handleOkFercInfo = () => {
    //alert("come")
    setOpenDialogForFerc(false)
    //penDialogforFerc
    setOpenFercTable(true);
  }

  console.log(questions, "questionsData")
  const presentDate = new Date();
  const backDate = new Date();
  backDate.setDate(backDate.getDate() - 15);

  const task = useSelector((state) => state?.userManagement?.taskData);
  const dropDownData = useSelector((state) => state.AllDropDown.dropDown);

  let shortDescFor5161Series = useSelector((state) => state.generalLedger.shortTextFor5161Series);
  let longDescriptionFor5161Series = useSelector((state) => state.generalLedger.longTextFor5161Series);
  const previousPage = sessionStorage.getItem('previousPage') || '/';
  // const filteredButtons = buttonsIDM?.filter(button => button?.MDG_DYN_BTN_TASK_NAME === task?.taskDesc);
  console.log(filteredButtons, "filteredButtonsData")

  console.log(remarks, "remarksData")
  const newChangePayload = useSelector((state) => state.edit.payloadForChangeNewValue)
  let demoData = {
    "General Information": {
      "Choose Priority Level": "Medium",
      "Business Justification": "SDF",
      "Business Segment": "INTRASTATE",
      "Did You Receive A SAP Or JE Error?": "",
      "Are You Taxable?": "false",
      "Are You Making Ledger Specific Posting?": "",
      "Is This Account Used For Manual Entries Only?": "false",
      "Is Trading Partner Needed?": "false",
      "GL Sub Account Type": "REVENUE THIRD PARTY-GATHERING, TRANSPORTATION AND OTHER FEES",
      "When Do You Need This In Production By?": "/Date(*************)/",
      "Message": "A Notification to the User advising, \"This account requires SAP Configuration to post to a profit center; please apply additional time for it to be fully active in SAP.  Notification will be sent once it is available in production by FMD Team"
    },
    "requestStatus": "Draft",
    "viewData": {
      "General Information": "",
      "Type/Description": {
        "Control in COA ETCN ET NATURAL/OPERATIONAL COA": [
          {
            "fieldName": "Account Type",
            "sequenceNo": 4,
            "fieldType": "Drop Down",
            "maxLength": 1,
            "dataType": "String",
            "viewName": "Type/Description",
            "cardName": "Control in COA ETCN ET NATURAL/OPERATIONAL COA",
            "cardSeq": 1,
            "visibility": "Display",
            "oldValue": "",
            "value": "P"
          },
          {
            "fieldName": "Account Group",
            "sequenceNo": 5,
            "fieldType": "Drop Down",
            "maxLength": 4,
            "dataType": "String",
            "viewName": "Type/Description",
            "cardName": "Control in COA ETCN ET NATURAL/OPERATIONAL COA",
            "cardSeq": 1,
            "visibility": "Display",
            "oldValue": "",
            "value": "REVE"
          }
        ],
        "Detailed Control for P&L Statement Accounts": [
          {
            "fieldName": "Functional Area",
            "sequenceNo": 1,
            "fieldType": "Drop Down",
            "maxLength": 4,
            "dataType": "String",
            "viewName": "Type/Description",
            "cardName": "Detailed Control for P&L Statement Accounts",
            "cardSeq": 2,
            "visibility": "Hidden",
            "oldValue": "",
            "value": ""
          }
        ],
        "Description": [
          {
            "fieldName": "Short Text",
            "sequenceNo": 1,
            "fieldType": "Input",
            "maxLength": 20,
            "dataType": "String",
            "viewName": "Type/Description",
            "cardName": "Description",
            "cardSeq": 3,
            "visibility": "Mandatory",
            "oldValue": "",
            "value": "FSFD"
          },
          {
            "fieldName": "Long Text",
            "sequenceNo": 2,
            "fieldType": "Input",
            "maxLength": 50,
            "dataType": "String",
            "viewName": "Type/Description",
            "cardName": "Description",
            "cardSeq": 3,
            "visibility": "Mandatory",
            "oldValue": "",
            "value": "VVC"
          }
        ],
        "Consolidation Data in COA ETCN ET NATURAL/OPERATIONAL COA": [
          {
            "fieldName": "Trading Partner",
            "sequenceNo": 1,
            "fieldType": "Drop Down",
            "maxLength": 6,
            "dataType": "String",
            "viewName": "Type/Description",
            "cardName": "Consolidation Data in COA ETCN ET NATURAL/OPERATIONAL COA",
            "cardSeq": 4,
            "visibility": "Hidden",
            "oldValue": "",
            "value": ""
          },
          {
            "fieldName": "Group Account Number",
            "sequenceNo": 2,
            "fieldType": "Drop Down",
            "maxLength": 10,
            "dataType": "String",
            "viewName": "Type/Description",
            "cardName": "Consolidation Data in COA ETCN ET NATURAL/OPERATIONAL COA",
            "cardSeq": 4,
            "visibility": "Hidden",
            "oldValue": "",
            "value": ""
          }
        ]
      },
      "Control Data": {
        "Account Control in Company Code": [
          {
            "fieldName": "Account Currency",
            "sequenceNo": 1,
            "fieldType": "Drop Down",
            "maxLength": 5,
            "dataType": "String",
            "viewName": "Control Data",
            "cardName": "Account Control in Company Code",
            "cardSeq": 1,
            "visibility": "Mandatory",
            "oldValue": "",
            "value": "USD"
          },
          {
            "fieldName": "Only Balance In Local Currency",
            "sequenceNo": 2,
            "fieldType": "Radio Button",
            "maxLength": 1,
            "dataType": "Boolean",
            "viewName": "Control Data",
            "cardName": "Account Control in Company Code",
            "cardSeq": 1,
            "visibility": "Optional",
            "oldValue": "",
            "value": false
          },
          {
            "fieldName": "Exchange Rate Difference Key",
            "sequenceNo": 3,
            "fieldType": "Drop Down",
            "maxLength": 4,
            "dataType": "String",
            "viewName": "Control Data",
            "cardName": "Account Control in Company Code",
            "cardSeq": 1,
            "visibility": "Hidden",
            "oldValue": "",
            "value": ""
          },
          {
            "fieldName": "Valuation Group",
            "sequenceNo": 4,
            "fieldType": "Input",
            "maxLength": 10,
            "dataType": "String",
            "viewName": "Control Data",
            "cardName": "Account Control in Company Code",
            "cardSeq": 1,
            "visibility": "Hidden",
            "oldValue": "",
            "value": ""
          },
          {
            "fieldName": "Tax Category",
            "sequenceNo": 5,
            "fieldType": "Drop Down",
            "maxLength": 2,
            "dataType": "String",
            "viewName": "Control Data",
            "cardName": "Account Control in Company Code",
            "cardSeq": 1,
            "visibility": "Optional",
            "oldValue": "",
            "value": "+"
          },
          {
            "fieldName": "Posting Without Tax Allowed",
            "sequenceNo": 6,
            "fieldType": "Radio Button",
            "maxLength": 1,
            "dataType": "Boolean",
            "viewName": "Control Data",
            "cardName": "Account Control in Company Code",
            "cardSeq": 1,
            "visibility": "Optional",
            "oldValue": "",
            "value": false
          },
          {
            "fieldName": "Recon. Account For Account Type",
            "sequenceNo": 7,
            "fieldType": "Drop Down",
            "maxLength": 1,
            "dataType": "String",
            "viewName": "Control Data",
            "cardName": "Account Control in Company Code",
            "cardSeq": 1,
            "visibility": "Optional",
            "oldValue": "",
            "value": ""
          },
          {
            "fieldName": "Alternative Account Number",
            "sequenceNo": 8,
            "fieldType": "Drop Down",
            "maxLength": 10,
            "dataType": "String",
            "viewName": "Control Data",
            "cardName": "Account Control in Company Code",
            "cardSeq": 1,
            "visibility": "Hidden",
            "oldValue": "",
            "value": ""
          },
          {
            "fieldName": "Account Managed In Ext System",
            "sequenceNo": 9,
            "fieldType": "Radio Button",
            "maxLength": 0,
            "dataType": "Boolean",
            "viewName": "Control Data",
            "cardName": "Account Control in Company Code",
            "cardSeq": 1,
            "visibility": "Hidden",
            "oldValue": "",
            "value": false
          },
          {
            "fieldName": "Tolerance Group",
            "sequenceNo": 11,
            "fieldType": "Drop Down",
            "maxLength": 0,
            "dataType": "String",
            "viewName": "Control Data",
            "cardName": "Account Control in Company Code",
            "cardSeq": 1,
            "visibility": "Hidden",
            "oldValue": "",
            "value": ""
          }
        ],
        "Account Management in Company Code": [
          {
            "fieldName": "Open Item Management",
            "sequenceNo": 1,
            "fieldType": "Radio Button",
            "maxLength": 1,
            "dataType": "Boolean",
            "viewName": "Control Data",
            "cardName": "Account Management in Company Code",
            "cardSeq": 2,
            "visibility": "Hidden",
            "oldValue": "",
            "value": false
          },
          {
            "fieldName": "Open Item Management By Ledger Group",
            "sequenceNo": 2,
            "fieldType": "Radio Button",
            "maxLength": 0,
            "dataType": "Boolean",
            "viewName": "Control Data",
            "cardName": "Account Management in Company Code",
            "cardSeq": 2,
            "visibility": "Hidden",
            "oldValue": "",
            "value": false
          },
          {
            "fieldName": "Sort Key",
            "sequenceNo": 3,
            "fieldType": "Drop Down",
            "maxLength": 3,
            "dataType": "String",
            "viewName": "Control Data",
            "cardName": "Account Management in Company Code",
            "cardSeq": 2,
            "visibility": "Hidden",
            "oldValue": "",
            "value": "001"
          },
          {
            "fieldName": "Authorization Group",
            "sequenceNo": 4,
            "fieldType": "Input",
            "maxLength": 4,
            "dataType": "String",
            "viewName": "Control Data",
            "cardName": "Account Management in Company Code",
            "cardSeq": 2,
            "visibility": "Hidden",
            "oldValue": "",
            "value": ""
          },
          {
            "fieldName": "Clerk Abbreviation",
            "sequenceNo": 5,
            "fieldType": "Drop Down",
            "maxLength": 0,
            "dataType": "String",
            "viewName": "Control Data",
            "cardName": "Account Management in Company Code",
            "cardSeq": 2,
            "visibility": "Hidden",
            "oldValue": "",
            "value": ""
          }
        ],
        "Account Settings in Controlling Area": [
          {
            "fieldName": "CElem Category",
            "sequenceNo": 1,
            "fieldType": "Drop Down",
            "maxLength": 2,
            "dataType": "String",
            "viewName": "Control Data",
            "cardName": "Account Settings in Controlling Area",
            "cardSeq": 3,
            "visibility": "Mandatory",
            "oldValue": "",
            "value": "11"
          },
          {
            "fieldName": "Record Quantity",
            "sequenceNo": 2,
            "fieldType": "Radio Button",
            "maxLength": 1,
            "dataType": "Boolean",
            "viewName": "Control Data",
            "cardName": "Account Settings in Controlling Area",
            "cardSeq": 3,
            "visibility": "Hidden",
            "oldValue": "",
            "value": false
          },
          {
            "fieldName": "Internal UOM",
            "sequenceNo": 3,
            "fieldType": "Drop Down",
            "maxLength": 3,
            "dataType": "String",
            "viewName": "Control Data",
            "cardName": "Account Settings in Controlling Area",
            "cardSeq": 3,
            "visibility": "Hidden",
            "oldValue": "",
            "value": ""
          }
        ]
      },
      "Create/Bank/Interest": {
        "Control of Document creation in Company Code": [
          {
            "fieldName": "Field Status Group",
            "sequenceNo": 1,
            "fieldType": "Drop Down",
            "maxLength": 4,
            "dataType": "String",
            "viewName": "Create/Bank/Interest",
            "cardName": "Control of Document creation in Company Code",
            "cardSeq": 1,
            "visibility": "Hidden",
            "oldValue": "",
            "value": "Z029"
          },
          {
            "fieldName": "Post Automatically Only",
            "sequenceNo": 2,
            "fieldType": "Radio Button",
            "maxLength": 1,
            "dataType": "Boolean",
            "viewName": "Create/Bank/Interest",
            "cardName": "Control of Document creation in Company Code",
            "cardSeq": 1,
            "visibility": "Optional",
            "oldValue": "",
            "value": false
          },
          {
            "fieldName": "Supplement Auto Postings",
            "sequenceNo": 3,
            "fieldType": "Radio Button",
            "maxLength": 1,
            "dataType": "Boolean",
            "viewName": "Create/Bank/Interest",
            "cardName": "Control of Document creation in Company Code",
            "cardSeq": 1,
            "visibility": "Hidden",
            "oldValue": "",
            "value": false
          },
          {
            "fieldName": "Recon. Acct Ready For Input",
            "sequenceNo": 4,
            "fieldType": "Radio Button",
            "maxLength": 0,
            "dataType": "Boolean",
            "viewName": "Create/Bank/Interest",
            "cardName": "Control of Document creation in Company Code",
            "cardSeq": 1,
            "visibility": "Hidden",
            "oldValue": "",
            "value": false
          }
        ],
        "Bank/Financial Details In Company Code": [
          {
            "fieldName": "Planning Level",
            "sequenceNo": 1,
            "fieldType": "Drop Down",
            "maxLength": 2,
            "dataType": "String",
            "viewName": "Create/Bank/Interest",
            "cardName": "Bank/Financial Details In Company Code",
            "cardSeq": 2,
            "visibility": "Hidden",
            "oldValue": "",
            "value": ""
          },
          {
            "fieldName": "Relevant To Cash Flow",
            "sequenceNo": 2,
            "fieldType": "Radio Button",
            "maxLength": 1,
            "dataType": "Boolean",
            "viewName": "Create/Bank/Interest",
            "cardName": "Bank/Financial Details In Company Code",
            "cardSeq": 2,
            "visibility": "Hidden",
            "oldValue": "",
            "value": false
          },
          {
            "fieldName": "House Bank",
            "sequenceNo": 3,
            "fieldType": "Drop Down",
            "maxLength": 5,
            "dataType": "String",
            "viewName": "Create/Bank/Interest",
            "cardName": "Bank/Financial Details In Company Code",
            "cardSeq": 2,
            "visibility": "Hidden",
            "oldValue": "",
            "value": ""
          },
          {
            "fieldName": "Account Id",
            "sequenceNo": 4,
            "fieldType": "Drop Down",
            "maxLength": 5,
            "dataType": "String",
            "viewName": "Create/Bank/Interest",
            "cardName": "Bank/Financial Details In Company Code",
            "cardSeq": 2,
            "visibility": "Hidden",
            "oldValue": "",
            "value": ""
          }
        ],
        "Blocking Status": [
          {
            "fieldName": "Blocked For Posting at COA",
            "sequenceNo": 1,
            "fieldType": "Radio Button",
            "maxLength": 0,
            "dataType": "Boolean",
            "viewName": "Create/Bank/Interest",
            "cardName": "Blocking Status",
            "cardSeq": 3,
            "visibility": "Optional",
            "oldValue": "",
            "value": false
          },
          {
            "fieldName": "Blocked For Posting Company Code",
            "sequenceNo": 2,
            "fieldType": "Radio Button",
            "maxLength": 0,
            "dataType": "Boolean",
            "viewName": "Create/Bank/Interest",
            "cardName": "Blocking Status",
            "cardSeq": 3,
            "visibility": "Optional",
            "oldValue": "",
            "value": false
          }
        ]
      },
      "FERC Information": "",
      "Attachments & Comments": ""
    },
    "FERC Information": [
      {
        "id": 12792,
        "compCode": "2015",
        "glAccount": "314778",
        "glLongDescription": "FEES-MARKETING",
        "ccUsed": "",
        "wbsUsed": "",
        "fercIndicator": "",
        "ruleSet": "",
        "fercAccount": ""
      }
    ],
    "GLAccount": "314778",
    "CompCode": "2010,2015",
    "CompanyCodesExtendedTo": null,
    "GeneralLedgerId": "4593",
    "GeneralLedgerErrorID": null,
    "ChartOfAccount": "ETCN",
    "SubAccountGroup": null,
    "Info": "ET Create",
    "IsRequestLvlErrorPresent": false,
    "IsObjectLvlErrorPresent": false,
    "generalInfoId": "5298",
    "OIMCSLIndicator": null,
    "taxFieldCheck": null,
    "OIMExisting": false,
    "CSLExisting": false,
    "TotalIntermediateTasks": 0
  }

  console.log(newChangePayload, "newChangePayload")
  console.log(fercRows, "fercRows====")
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const appSettings = useSelector((state) => state.appSettings);
  let iwaAccessData = useSelector(
    (state) => state.userManagement.entitiesAndActivities?.["General Ledger"]
  );
  const artifactId = useSelector((state) => state.initialData.ArtifactId)
  const giGLPayload = useSelector((state) => state.generalLedger.glGIPayload)
  console.log(giGLPayload, "giGLPayload==========")
  const distinctArtifactId = [...new Set(artifactId)]
  console.log(distinctArtifactId, "distinctArtifactId")
  let taskData = useSelector((state) => state?.initialData?.IWMMyTask);
  let userData = useSelector((state) => state.userManagement.userData);
  const applicationConfig = useSelector((state) => state.applicationConfig);
  let generalLedgerRowData = location.state;
  let requiredFields = useSelector(
    (state) => state.generalLedger.requiredFields
  );
  let requiredFieldsGI = useSelector(
    (state) => state.generalLedger.requiredFieldsGI
  );
  console.log(requiredFieldsGI, "requiredFieldsGI")
  let singleGLPayloadMain = useSelector((state) => state.generalLedger.singleGLPayloadGI)
  const [newValueLength, setNewValueLength] = useState(
    singleGLPayloadMain?.BusinessJustification
      ? singleGLPayloadMain?.BusinessJustification?.length
      : 0);
  console.log(singleGLPayloadMain, "singleGLPayloadMainDtaa")
  let taskRowDetails = useSelector((state) => state.userManagement.taskData);
  console.log(taskRowDetails, "taskRowDetails0")
  const singleGLPayload = useSelector((state) => state.edit.payload);
  console.log("ccroewdata", singleGLPayload);
  const generalLedgerViewData = useSelector(
    (state) => state.generalLedger.generalLedgerViewData
  );
  let extendedCompanyCode = useSelector((state) => state?.edit?.selectedCheckBox);
  console.log("extended", extendedCompanyCode);
  console.log(generalLedgerViewData, "generalLedgerViewData");
  console.log("generalLedgerRowData", generalLedgerRowData);
  console.log("costCenterDetails", costCenterDetails);
  console.log(remarks, "Remarks");
  const optionsData = useSelector((state) => state.AllDropDown.dropDown)

  let buttonPriority = {
    "handleSubmitForApproval": 6,
    "handleSendBack": 1,
    "handleReject": 3,
    "handleValidate": 5,
    "handleSAPSyndication": 8,
    "handleIdGenerator": 4,
    "handleSubmitForReview": 7,
    "handleCorrection": 2
  }


  function checkElements() {
    // Split the input string by commas to get an array of elements
    let compCodeFrSunooCheck = iDs?.CompCode ? iDs?.CompCode : generalLedgerRowData?.compCode

    console.log(compCodeFrSunooCheck, "compCodeFrSunooCheck==")
    const elements = compCodeFrSunooCheck?.split(',');

    // Check if every element starts with 7
    const allStartWith7 = elements?.every(element => element?.startsWith('7'));

    // Return the corresponding result based on the check
    return allStartWith7 ? "SUNOCO Create" : "ET Create";
  }




  const handleChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  const handleSnackBarCloseValidation = () => {
    setOpenSnackbarValidation(false);
    setMessageDialogMessage("");
  };

  console.log("taskData", taskData);
  console.log("taskRowDetails", taskRowDetails);
  console.log("glids", iDs);

  const rows = [];
  //   id	24
  // compCode	"1100"
  // glAccount	"220110"
  // glLongDescription	"Dummy GL"
  // ccUsed	"TZUS124342"
  // wbsUsed	"TEST"
  // fercIndicator	"1234"
  // ruleSet	"ABCD"
  // const columnsforFERC = [
  //   {
  //     field: "compCode",
  //     headerName: "Company Code",
  //     editable: false,
  //     flex: 1,
  //     // width: 100,
  //   },
  //   {
  //     field: "glAccount",
  //     headerName: "GL Account",
  //     editable: false,
  //     flex: 1,
  //     // width: 400,
  //   },
  //   {
  //     field: "glLongDescription",
  //     headerName: "GL Long Description",
  //     editable: false,
  //     flex: 1,
  //     // width: 400,
  //   },
  //   {
  //     field: "ccUsed",
  //     headerName: "CC Used",
  //     editable: false,
  //     flex: 1,
  //     renderCell: (params) => {
  //       // const [options, setOptions] = useState([]);
  //       const compCode = params?.row?.compCode;
  //       console.log(params, "paramsss")
  //       console.log(optionsData[compCode], "Data of options");
  //       const myOptions = [];
  //       optionsData[compCode]?.map((item) => {
  //         myOptions.push(item.code);
  //       })
  //       console.log(myOptions, "myOptions");


  //       return (
  //         <Autocomplete
  //           // freeSolo
  //           fullWidth
  //           options={myOptions}
  //           placeholder="Select CC Name"
  //           value={
  //             newChangePayload[`${params.field}_${params.id}`]?.toUpperCase()
  //           }
  //           // getOptionLabel={(option) => option.label || ''} // Adjust as per the structure of your data
  //           onChange={(e, value) => handleOptionChange(params?.row, value)}
  //           renderInput={(params) => (
  //             <TextField
  //               {...params}
  //               variant="outlined"
  //               size="small"
  //             />
  //           )}
  //         />
  //       );
  //     },
  //     // width: 400,
  //   },
  //   {
  //     field: "wbsUsed",
  //     headerName: "WBS Used",
  //     editable: false,
  //     flex: 1,
  //     renderCell: (params) => {
  //       const myOptions = [];
  //       optionsData[[`${'wbsUsed'}_${params?.row?.id}`]]?.map((item) => {
  //         myOptions.push(item.code);
  //       })

  //       console.log(optionsData[`${'wbsUsed'}_${params?.row?.id}`], "myOptions");
  //       return (
  //         <Autocomplete
  //           // freeSolo
  //           fullWidth
  //           options={myOptions}
  //           placeholder="Select  Name"
  //           value={
  //             newChangePayload[`${params.field}_${params.id}`]?.toUpperCase()
  //           }
  //           // getOptionLabel={(option) => option.label || ''} // Adjust as per the structure of your data
  //           onChange={(e, value) => handleOptionChangeWbs(params?.row, value)}
  //           renderInput={(params) => (
  //             <TextField
  //               {...params}
  //               variant="outlined"
  //               size="small"
  //             />
  //           )}
  //         />
  //       );
  //     },
  //     width: 400,
  //   },
  //   {
  //     field: "fercIndicator",
  //     headerName: "FERC Indicator",
  //     editable: false,
  //     flex: 1,
  //     renderCell: (params) => {
  //       // const [options, setOptions] = useState([]);
  //       console.log(newChangePayload[`${params.field}_${params.id}`], "params========")


  //       return (
  //         <TextField
  //           variant="outlined"
  //           size="small"
  //           fullWidth
  //           value={
  //             newChangePayload[`${params.field}_${params.id}`]?.toUpperCase()
  //           }
  //           disabled

  //         />
  //       );
  //     }
  //     // width: 400,
  //   },
  //   {

  //     field: "ruleSet",
  //     headerName: "Rule Set",
  //     editable: false,
  //     flex: 1,
  //     renderCell: (params) => {
  //       console.log(newChangePayload[`${params.field}_${params.id}`], "params========")

  //       // if (userData?.role === "Finance") {
  //       return (
  //         <TextField
  //           variant="outlined"
  //           size="small"
  //           fullWidth
  //           value={
  //             newChangePayload[`${params.field}_${params.id}`]?.toUpperCase()
  //           }
  //           placeholder={`Enter ruleSet`}
  //           // inputProps={{ maxLength: length }}
  //           onChange={(event) => {
  //             console.log(event, "event===")
  //             const newValue = event.target.value;
  //             console.log("testing", newValue);

  //             let costCenterUpperCase = newValue.toUpperCase();
  //             onEditFORFERC(`${params.field}_${params.id}`, costCenterUpperCase);

  //           }}
  //         />
  //       );
  //       // } else {
  //       //   return (
  //       //     <TextField
  //       //       variant="outlined"
  //       //       size="small"
  //       //       fullWidth
  //       //       value={
  //       //         newChangePayload[`${params.field}_${params.id}`]?.toUpperCase()
  //       //       }
  //       //       placeholder={`Enter ruleSet`}
  //       //       disabled
  //       //     // inputProps={{ maxLength: length }}
  //       //     // onChange={(event) => {
  //       //     //   console.log(event,"event===")
  //       //     //   const newValue = event.target.value;
  //       //     //   console.log("testing", newValue);

  //       //     //     let costCenterUpperCase = newValue.toUpperCase();
  //       //     //     onEditFORFERC(`${params.field}_${params.id}` , costCenterUpperCase);

  //       //     // }}
  //       //     />
  //       //   );
  //       // }


  //     }
  //   }
  //   // width: 400,

  // ];
  const columnsforFERC = [
    {
      field: "compCode",
      headerName: "Company Code",
      editable: false,
      flex: 1,
      // width: 100,
    },
    {
      field: "glAccount",
      headerName: "GL Account",
      editable: false,
      flex: 1,
      // width: 400,
    },
    {
      field: "glLongDescription",
      headerName: "GL Long Description",
      editable: false,
      flex: 1,
      // width: 400,
    },
    {
      field: "wbsUsed",
      headerName: "WBS Used",
      editable: false,
      flex: 1,

      renderHeader: () => (
        <Tooltip
          title="If Expense account, please provide Cost Center or WBS"
          placement="top"
          PopperProps={{
            modifiers: [
              {
                name: 'offset',
                options: {
                  offset: [0, -8], // Adjust the Y-axis here (positive to go up, negative to go down)
                },
              },
            ],
          }}
        >
          <span style={{ color: "blue" }}>WBS Used</span>
        </Tooltip>
      ),
      renderCell: (params) => {
        const myOptions = [];
        optionsData[[`${'wbsUsed'}_${params?.row?.id}`]]?.map((item) => {
          myOptions.push(item.code);
        })

        console.log(optionsData[`${'wbsUsed'}_${params?.row?.id}`], "myOptions");
        return (
          <Autocomplete
            // freeSolo
            fullWidth
            options={myOptions}
            placeholder="Select  Name"
            value={
              newChangePayload[`${params.field}_${params.id}`]?.toUpperCase()
            }
            // getOptionLabel={(option) => option.label || ''} // Adjust as per the structure of your data
            onChange={(e, value) => handleOptionChangeWbs(params?.row, value)}
            renderInput={(params) => (
              <TextField
                {...params}
                variant="outlined"
                size="small"
              />
            )}
          />
        );
      },
      // width: 400,
    },
    {
      field: "ccUsed",
      headerName: "Cost Center Used",
      editable: false,
      flex: 1,
      renderHeader: () => (
        <Tooltip
          title="If Expense account, please provide Cost Center or WBS"
          placement="top"
          PopperProps={{
            modifiers: [
              {
                name: 'offset',
                options: {
                  offset: [0, -8], // Adjust the Y-axis here (positive to go up, negative to go down)
                },
              },
            ],
          }}
        >
          <span style={{ color: "blue" }}>Cost Center Used</span>
        </Tooltip>
      ),
      renderCell: (params) => {
        // const [options, setOptions] = useState([]);
        const compCode = params?.row?.compCode;
        console.log(params, "paramsss")
        console.log(optionsData[compCode], "Data of options");
        const myOptions = [];
        optionsData[compCode]?.map((item) => {
          myOptions.push(item.code);
        })
        console.log(myOptions, "myOptions");


        return (
          <Autocomplete
            // freeSolo
            fullWidth
            options={myOptions}
            placeholder="Select CC Name"
            value={
              newChangePayload[`${params.field}_${params.id}`]?.toUpperCase()
            }
            // getOptionLabel={(option) => option.label || ''} // Adjust as per the structure of your data
            onChange={(e, value) => handleOptionChange(params?.row, value)}
            renderInput={(params) => (
              <TextField
                {...params}
                variant="outlined"
                size="small"
              />
            )}
          />
        );
      },
      // width: 400,
    },

    {
      field: "fercIndicator",
      headerName: "FERC Indicator",
      editable: false,
      flex: 1,
      renderHeader: () => (
        <Tooltip
          title="Will auto-generate from Cost Center or WBS if applicable"
          placement="top"
          PopperProps={{
            modifiers: [
              {
                name: 'offset',
                options: {
                  offset: [0, -8], // Adjust the Y-axis here (positive to go up, negative to go down)
                },
              },
            ],
          }}
        >
          <span style={{ color: "blue" }}>FERC Indicator</span>
        </Tooltip>
      ),
      renderCell: (params) => {
        // const [options, setOptions] = useState([]);


        return (
          <TextField
            variant="outlined"
            size="small"
            fullWidth
            value={
              newChangePayload[`${params.field}_${params.id}`]?.toUpperCase()
            }
            disabled

          />
        );
      }
      // width: 400,
    },
    {
      field: "fercAccount",
      headerName: "FERC Account",
      editable: false,
      flex: 1,
      renderHeader: () => (
        <Tooltip
          title="Regulatory Team will populate if applicable"
          placement="top"
          PopperProps={{
            modifiers: [
              {
                name: 'offset',
                options: {
                  offset: [0, -8], // Adjust the Y-axis here (positive to go up, negative to go down)
                },
              },
            ],
          }}
        >
          <span style={{ color: "blue" }}>FERC Account</span>
        </Tooltip>
      ),
      renderCell: (params) => {
        const field = params?.row;
        const rowIndex = params.row.id;
        const [localValue, setLocalValue] = useState(newChangePayload[`${params.field}_${params.id}`]?.toUpperCase() || "");
        // const localValue = tempComments[rowIndex] || "";
        const handleChangeValueInTable = (event) => {
          let costCenterUpperCase = event.target.value[0] === " "
            ? event.target.value.trimStart()
            : event.target.value

          setLocalValue(costCenterUpperCase);
          onEditFORFERC(`${params.field}_${params.id}`, costCenterUpperCase)
        };

        // const handleBlur = (event) => {
        //   const finalValue = event.target.value.toUpperCase();
        //   handleCommentChange(rowIndex, finalValue);
        //   onEditFORFERC(`${params.field}_${params.id}`, finalValue); // Sync with the parent state
        // };

        return (
          <Tooltip title={newChangePayload[`${params.field}_${params.id}`]?.toUpperCase()} placement="top" arrow>
            <TextField
              variant="outlined"
              size="small"
              fullWidth
              // value={localValue}
              value={localValue}
              inputProps={{
                style: { textTransform: "uppercase" },
                maxLength: 254,
              }}

              //placeholder={`Enter FERC Account`}
              // inputProps={{ maxLength: length }}
              onChange={(e) => {
                handleChangeValueInTable(e);
                // const newValue = e.target.value;
              }}
              // onBlur={(event) => {
              //     handleCommentChange(rowIndex, event.target.value.toUpperCase());
              // }}
              // onFocus={() => setIsFocused(true)}
              onKeyDown={(event) => {
                console.log("Key down: ", event.key);
                event.stopPropagation();
              }}
            />
          </Tooltip>
        );
      },
      // width: 400,
    },
    {

      field: "ruleSet",
      headerName: "Rule Set",
      editable: false,
      flex: 1,
      renderHeader: () => (
        <Tooltip
          title="Regulatory Team will populate if applicable"
          placement="top"
          PopperProps={{
            modifiers: [
              {
                name: 'offset',
                options: {
                  offset: [0, -8], // Adjust the Y-axis here (positive to go up, negative to go down)
                },
              },
            ],
          }}
        >
          <span style={{ color: "blue" }}>Rule Set</span>
        </Tooltip>
      ),
      renderCell: (params) => {
        const field = params?.row;
        const rowIndex = params.row.id;
        const [localValue, setLocalValue] = useState(newChangePayload[`${params.field}_${params.id}`]?.toUpperCase() || "");
        // const localValue = tempComments[rowIndex] || "";
        const handleChangeValueInTable = (event) => {
          let costCenterUpperCase = event.target.value[0] === " "
            ? event.target.value.trimStart()
            : event.target.value

          setLocalValue(costCenterUpperCase);
          onEditFORFERC(`${params.field}_${params.id}`, costCenterUpperCase)
        };

        // const handleBlur = (event) => {
        //   const finalValue = event.target.value.toUpperCase();
        //   handleCommentChange(rowIndex, finalValue);
        //   onEditFORFERC(`${params.field}_${params.id}`, finalValue); // Sync with the parent state
        // };

        return (
          <Tooltip title={newChangePayload[`${params.field}_${params.id}`]?.toUpperCase()} placement="top" arrow>
            <TextField
              variant="outlined"
              size="small"
              fullWidth
              // value={localValue}
              value={localValue}
              inputProps={{
                style: { textTransform: "uppercase" },
                maxLength: 254,
              }}

              //placeholder={`Enter FERC Account`}
              // inputProps={{ maxLength: length }}
              onChange={(e) => {
                handleChangeValueInTable(e);
                // const newValue = e.target.value;
              }}
              // onBlur={(event) => {
              //     handleCommentChange(rowIndex, event.target.value.toUpperCase());
              // }}
              // onFocus={() => setIsFocused(true)}
              onKeyDown={(event) => {
                console.log("Key down: ", event.key);
                event.stopPropagation();
              }}
            />
          </Tooltip>
        );
      },
    }
    // width: 400,

  ];

  const handleRemarksDialogClose = () => {
    setRemarks("");
    setTestrunStatus(true);
    setOpenRemarkDialog(false);
    setMessageDialogMessage("")
  };

  let glColumnError = [
    {
      "field": "generalLedger",
      "headerName": "General Ledger",
      "width": 150
    },
    {
      "field": "compCode",
      "headerName": "Comp Code",
      "width": 150
    },
    {
      "field": "message",
      "headerName": "Message",
      "flex": 1,
      "sortable": false
    }
  ]

  //companycode dependent
  const getTaxCategory = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "TaxCategory", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getTaxCategory?companyCode=${taskData?.body?.compCode
        ? taskData?.body?.compCode
        : generalLedgerRowData?.compCode
      }`,
      "get",
      hSuccess,
      hError
    );
  };

  const getFieldStatusGroup = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "FieldStatusGroup", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getFieldStatusGroup?companyCode=${taskData?.body?.compCode
        ? taskData?.body?.compCode
        : generalLedgerRowData?.compCode
      }`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCostElementCategory = () => {
    // console.log("value",value.code);
    //alert("coming")

    const hSuccess = (data) => {
      // console.log("value",data);
      dispatch(
        setDropDown({ keyName: "CElemCategory", data: data.body })
      );
    };
    const hError = (error) => {
      console.log(error, "error in dojax");
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getCostElementCategory?accountType=${taskData?.body?.AccountType ? taskData?.body?.AccountType : generalLedgerRowData?.glAccountType}`,
      "get",
      hSuccess,
      hError
    );
  };
  //chartofaccount dependent lookups
  const getGroupAccountNumber = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "GroupAccountNumber", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getGroupAccountNumber?chartAccount=${taskData?.body?.coa
        ? taskData?.body?.coa
        : generalLedgerRowData?.chartOfAccount
      }`,
      "get",
      hSuccess,
      hError
    );
  };
  const getAlternativeAccountNumber = () => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "AlternativeAccountNumber", data: data.body })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getAlternativeAccountNumber?chartAccount=${taskData?.body?.coa
        ? taskData?.body?.coa
        : generalLedgerRowData?.chartOfAccount
      }`,
      "get",
      hSuccess,
      hError
    );
  };
  const getAccountCurrency = (value) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "AccountCurrency", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getAccountCurrency?companyCode=${taskData?.body?.compCode
        ? taskData?.body?.compCode
        : generalLedgerRowData?.compCode
      }`,
      "get",
      hSuccess,
      hError
    );
  };
  // const getAccountGroup = () => {
  //   const hSuccess = (data) => {
  //     dispatch(setDropDown({ keyName: "AccountGroup", data: data.body }));
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(
  //     `/${destination_GeneralLedger}/data/getAccountGroupCodeDesc?chartAccount=${taskData?.body?.coa
  //       ? taskData?.body?.coa
  //       : generalLedgerRowData?.chartOfAccount
  //     }`,
  //     "get",
  //     hSuccess,
  //     hError
  //   );
  // };

  useEffect(() => {
    if (activeStep == 2) {
      let arrferc = []
      fercRows.map((itemdata) => {
        let ferc_hash = {}
        ferc_hash["id"] = itemdata?.id
        ferc_hash["compCode"] = itemdata?.compCode
        ferc_hash["glAccount"] = itemdata?.glAccount
        ferc_hash["glLongDescription"] = singleGLPayload?.LongText
        ferc_hash["ruleSet"] = itemdata?.ruleSet
        ferc_hash["ccUsed"] = itemdata?.ccUsed
        ferc_hash["wbsUsed"] = itemdata?.wbsUsed
        ferc_hash["fercIndicator"] = itemdata?.fercIndicator
        ferc_hash["fercAccount"] = itemdata?.fercAccount
        arrferc.push(ferc_hash)

      })
      setFercRows(arrferc)
    }
  }, [activeStep])

  const validateForGLNameDuplicateCheckFromDraftTimeOut = () => {
    var duplicateCheckName = {
      glName: singleGLPayload?.ShortText ? singleGLPayload?.ShortText?.toUpperCase() : "",
      coa: generalLedgerRowData?.chartOfAccount
        ? generalLedgerRowData?.chartOfAccount
        : iDs?.ChartOfAccount
          ? iDs?.ChartOfAccount
          : "",
    };

    return promiseAjax(
      `/${destination_GeneralLedger}/alter/fetchGlNameNCoaDupliChk`,
      "post",
      duplicateCheckName
    );
  };

  const validateForGLDescriptionDuplicateCheckFromDraftTimeout = () => {
    var duplicateCheckDescription = {
      glDesc: singleGLPayload?.LongText ? singleGLPayload?.LongText?.toUpperCase() : "",
      coa: generalLedgerRowData?.chartOfAccount
        ? generalLedgerRowData?.chartOfAccount
        : iDs?.ChartOfAccount
          ? iDs?.ChartOfAccount
          : "",
    };

    return promiseAjax(
      `/${destination_GeneralLedger}/alter/fetchGlDescNCoaDupliChk`,
      "post",
      duplicateCheckDescription
    );
  };

  const getDuplicateCheckStatus = async () => {
    let glNameFromGlViewData = "";
    generalLedgerViewData?.["Type/Description"]?.["Description"]?.map(
      (description_element) => {
        if (description_element?.fieldName === "Short Text") {
          glNameFromGlViewData = description_element?.value;
        }
      }
    );

    let glDescFromGlViewData = "";
    generalLedgerViewData?.["Type/Description"]?.["Description"]?.map(
      (description_element) => {
        if (description_element?.fieldName === "Long Text") {
          glDescFromGlViewData = description_element?.value;
        }
      }
    );


    if (glNameFromGlViewData !== singleGLPayload?.ShortText) {
      // let data = await validateShortDescCostCenter();
      let dupSDCheck = await validateForGLNameDuplicateCheckFromDraftTimeOut();
      let data = await dupSDCheck.json();
      console.log("dupSDChecksd", dupSDCheck);
      console.log("check sd", data);
      if (data.body.length > 0) {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          `Short Text is a duplicate within this company code, Discarding the Request.`
        );
        setHandleExtrabutton(false);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setBlurLoading(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setTimeout(() => {
          navigate("/masterDataCockpitNew/generalLedger");
        }, 3000);
        // setSubmitForReviewDisabled(true);
        return;
      }
    }

    if (glDescFromGlViewData !== singleGLPayload?.LongText) {
      // let data = await validateLongDescCostCenter();
      let dupLDCheck = await validateForGLDescriptionDuplicateCheckFromDraftTimeout();
      let data = await dupLDCheck.json();
      console.log("check ld", data);
      console.log("dupLDCheck ld", dupLDCheck);
      if (data.body.length > 0) {
        // Handle direct match
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          `Long Text is a duplicate within this company code, Discarding the Request..`
        );
        setHandleExtrabutton(false);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setBlurLoading(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setTimeout(() => {
          navigate("/masterDataCockpitNew/generalLedger");
        }, 3000);
        // setSubmitForReviewDisabled(true);
        return;
      }
    }
    validateForGeneralLedgerAfterAllCheckFromSaveAsDraft();
  }


  useEffect(() => {
    setGlNumber(idGenerator("GL"));
  }, []);

  useEffect(() => {
    if (applicationConfig?.logoutUserWarning && generalLedgerRowData?.reqStatus === "Draft") {
      // onSaveAsDraft()
      getDuplicateCheckStatus()
    }
  }, [applicationConfig?.logoutUserWarning])


  useEffect(() => {
    const filtered = buttonsIDM.filter(
      (button) => button.MDG_DYN_BTN_TASK_NAME === taskRowDetails?.taskDesc
    );
    console.log(filtered, "filteredButtonArr")
    const sortedButtonArr = filtered.sort((a, b) => {
      const priorityA = buttonPriority[a.MDG_DYN_BTN_ACTION_TYPE]  // Default to a large number if not found
      const priorityB = buttonPriority[b.MDG_DYN_BTN_ACTION_TYPE]
      return priorityA - priorityB;
    });

    console.log(sortedButtonArr, "sortedButtonArr");
    setFilteredButtons(sortedButtonArr);
  }, [buttonsIDM]);

  // const getfercInfoData = () => {
  //   let ferc_Arr = []
  //   if (generalLedgerRowData?.requestType === "Display For Extend") {

  //     extendedCompanyCode?.map((compCode, index) => {
  //       const dynamicProperty = `ruleSet_${index}`
  //       console.log(newChangePayload[dynamicProperty], "dynamicProperty")
  //       let ferchash = {}

  //       ferchash["FERCInformationID"] = "",
  //         ferchash["CompCode"] = compCode,
  //         ferchash["GLAccount"] = generalLedgerRowData?.glAccount
  //           ? generalLedgerRowData?.glAccount
  //           : iDs?.GLAccount
  //             ? iDs?.GLAccount
  //             : "",
  //         ferchash["Description"] = singleGLPayload?.ShortText ? singleGLPayload?.ShortText : "",
  //         ferchash["CCUsed"] = "",
  //         ferchash["WBSUsed"] = "",
  //         ferchash["FERCIndicator"] = "",
  //         ferchash["RuleSet"] = newChangePayload[dynamicProperty] ? newChangePayload[dynamicProperty] : ''
  //       ferc_Arr.push(ferchash)
  //     })
  //   } else if (generalLedgerRowData?.requestType === "Extend" || taskRowDetails?.requestType === "Extend") {
  //     iDs?.CompanyCodesExtendedTo?.split(",")?.map((compCode, index) => {
  //       const dynamicProperty = `ruleSet_${index}`
  //       console.log(newChangePayload[dynamicProperty], "dynamicProperty")
  //       let ferchash = {}

  //       ferchash["FERCInformationID"] = "",
  //         ferchash["CompCode"] = compCode,
  //         ferchash["GLAccount"] = generalLedgerRowData?.glAccount
  //           ? generalLedgerRowData?.glAccount
  //           : iDs?.GLAccount
  //             ? iDs?.GLAccount
  //             : "",
  //         ferchash["Description"] = singleGLPayload?.ShortText ? singleGLPayload?.ShortText : "",
  //         ferchash["CCUsed"] = "",
  //         ferchash["WBSUsed"] = "",
  //         ferchash["FERCIndicator"] = "",
  //         ferchash["RuleSet"] = newChangePayload[dynamicProperty] ? newChangePayload[dynamicProperty] : ''
  //       ferc_Arr.push(ferchash)
  //     })
  //   } else if (generalLedgerRowData?.requestType === "Create" || taskRowDetails?.requestType === "Create") {
  //     iDs?.CompCode?.split(",")?.map((compCode, index) => {
  //       const dynamicProperty = `ruleSet_${index}`
  //       console.log(newChangePayload[dynamicProperty], "dynamicProperty")
  //       let ferchash = {}

  //       ferchash["FERCInformationID"] = "",
  //         ferchash["CompCode"] = compCode,
  //         ferchash["GLAccount"] = generalLedgerRowData?.glAccount
  //           ? generalLedgerRowData?.glAccount
  //           : iDs?.GLAccount
  //             ? iDs?.GLAccount
  //             : "",
  //         ferchash["Description"] = singleGLPayload?.ShortText ? singleGLPayload?.ShortText : "",
  //         ferchash["CCUsed"] = "",
  //         ferchash["WBSUsed"] = "",
  //         ferchash["FERCIndicator"] = "",
  //         ferchash["RuleSet"] = newChangePayload[dynamicProperty] ? newChangePayload[dynamicProperty] : ''
  //       ferc_Arr.push(ferchash)
  //     })
  //   }
  //   console.log(ferc_Arr, "ferc_Arr----------")
  //   return ferc_Arr

  // }

  const getfercInfoData = () => {
    let companyCodesData = taskData?.body ? taskData?.body?.compCode?.split(',') : generalLedgerRowData?.companyCode?.newCompanyCode?.map(item => item.code)

    console.log(companyCodesData, "companyCodesData")

    let ferc_Arr = []
    // if(generalLedgerRowData?.requestType === "Display For Create"){
    //console.log("coming")
    fercRows?.map((itemData, index) => {
      console.log(itemData, "itemDataofFerc")
      const dynamicPropertyRuleSet = `ruleSet_${index}`
      const dynamicPropertyWbsused = `wbsUsed_${index}`
      const dynamicPropertyCCUsed = `ccUsed_${index}`
      const dynamicPropertyFercIndicator = `fercIndicator_${index}`
      const dynamicPropertyAccount = `fercAccount_${index}`
      //console.log(newChangePayload[dynamicProperty], "dynamicProperty")
      let ferchash = {}

      ferchash["FERCInformationID"] = "",
        ferchash["CompCode"] = itemData?.compCode,
        ferchash["GLAccount"] = itemData?.glAccount,
        ferchash["Description"] = itemData?.glLongDescription,
        ferchash["CCUsed"] = newChangePayload[dynamicPropertyCCUsed] ? newChangePayload[dynamicPropertyCCUsed] : '',
        ferchash["WBSUsed"] = newChangePayload[dynamicPropertyWbsused] ? newChangePayload[dynamicPropertyWbsused] : '',
        ferchash["FERCIndicator"] = newChangePayload[dynamicPropertyFercIndicator] ? newChangePayload[dynamicPropertyFercIndicator] : ''
      ferchash["RuleSet"] = newChangePayload[dynamicPropertyRuleSet] ? newChangePayload[dynamicPropertyRuleSet] : ''
      ferchash["FercAccount"] = newChangePayload[dynamicPropertyAccount] ? newChangePayload[dynamicPropertyAccount] : ''

      ferc_Arr.push(ferchash)
    })

    console.log(ferc_Arr, "ferc_Arr----------")
    return ferc_Arr


  }

  const getValueForFieldNameForPayload = (data, fieldName) => {
    console.log("getvalueforfieldname-----", data, fieldName);
    const field = data?.find((field) => field?.fieldName === fieldName);
    return field ? field.value : "";
  };


  function isTaxableCheck() {
    const accountGroup = singleGLPayload?.AccountGroup;
    const businessSegment = singleGLPayloadMain?.["BusinessSegment"];
    const areYouTaxable = singleGLPayload?.TaxCategory;
    if (

      areYouTaxable?.length > 0 || singleGLPayload?.PostingWithoutTaxAllowed === true
    ) {
      return "TRUE";
    } else {
      return "NA";
    }
  }


  function checkOIMCSLIndicator() {
    const accountGroups = ["ASST", "LIEQ", "ZCNV"];
    const isAccountGroupValid = accountGroups?.includes(singleGLPayload?.AccountGroup);
    if (isAccountGroupValid) {
      if (singleGLPayload?.OpenItemManagementByLedgerGroup === true || singleGLPayload?.OpenItemManagement === true) {
        return "TRUE"
      }
    }

    return "NA"; // Either account group is invalid or required fields are missing
  }


  var payload = {
    //Info:iDs?.Info?iDs?.Info:"",
    OIMExisting: "",
    CSLExisting: "",
    GeneralLedgerID: iDs?.GeneralLedgerId ? iDs?.GeneralLedgerId : "",
    Action:
      generalLedgerRowData?.requestType === "Create"
        ? "I"
        : generalLedgerRowData?.requestType === "Change"
          ? "U"
          : taskRowDetails?.requestType === "Create"
            ? "I"
            : taskRowDetails?.requestType === "Change"
              ? "U"
              : generalLedgerRowData?.requestType === "Extend"
                ? "I"
                : taskRowDetails?.requestType === "Extend"
                  ? "I"
                  : generalLedgerRowData?.requestType === "Extend"
                    ? "I"
                    : "U",
    RequestID: "",
    TaskStatus: "",
    TaskId: taskRowDetails?.taskId ? taskRowDetails?.taskId : "",
    Remarks: userInput ? userInput : remarks ? remarks : "",
    TotalIntermediateTasks: iDs?.TotalIntermediateTasks ? iDs?.TotalIntermediateTasks : 0,

    //Info: "",
    CreationId:
      taskRowDetails?.requestType === "Create"
        ? taskRowDetails?.requestId.slice(8)
        : generalLedgerRowData?.requestType === "Create"
          ? generalLedgerRowData?.requestId.slice(8)
          : "",
    EditId:
      taskRowDetails?.requestType === "Change"
        ? taskRowDetails?.requestId.slice(8)
        : generalLedgerRowData?.requestType === "Change"
          ? generalLedgerRowData?.requestId.slice(8)
          : "",
    ExtendId:
      taskRowDetails?.requestType === "Extend"
        ? taskRowDetails?.requestId.slice(8)
        : generalLedgerRowData?.requestType === "Extend" &&
          generalLedgerRowData.requestId
          ? generalLedgerRowData?.requestId.slice(8)
          : "",
    MassExtendId: "",
    DeleteId: "",
    MassCreationId: "",
    MassEditId: "",
    MassDeleteId: "",
    RequestType:
      generalLedgerRowData?.requestType === "Create"
        ? "Create"
        : generalLedgerRowData?.requestType === "Change"
          ? "Change"
          : generalLedgerRowData?.requestType === "Extend"
            ? "Extend"
            : taskRowDetails?.requestType === "Create"
              ? "Create"
              : taskRowDetails?.requestType === "Change"
                ? "Change"
                : taskRowDetails?.requestType === "Extend"
                  ? "Extend"
                  : "Extend",
    ReqCreatedBy: taskData?.body?.createdBy ? taskData?.body?.createdBy : generalLedgerRowData?.createdBy
      ? generalLedgerRowData?.createdBy : userData?.emailId ? userData?.emailId : "",
    ReqCreatedOn: taskData?.body?.creationDate
      ? taskData?.body?.creationDate
      : generalLedgerRowData?.reqCreatedOn
        ? generalLedgerRowData?.reqCreatedOn
        : "",
    ReqUpdatedOn: "",
    RequestStatus: taskData?.body?.reqStatus ? taskData?.body?.reqStatus : "",
    Testrun: testrunStatus,
    COA: generalLedgerRowData?.chartOfAccount
      ? generalLedgerRowData?.chartOfAccount
      : iDs?.ChartOfAccount
        ? iDs?.ChartOfAccount
        : "",
    CompanyCode: generalLedgerRowData?.compCode
      ? generalLedgerRowData?.compCode
      : iDs?.CompCode
        ? iDs?.CompCode
        : "",
    //CoCodeToExtend:"",
    CoCodeToExtend: generalLedgerRowData?.requestType === "Display For Extend" ?
      extendedCompanyCode?.join(",") : iDs?.CompanyCodesExtendedTo ?
        iDs?.CompanyCodesExtendedTo : ''
    ,
    GLAccount: generalLedgerRowData?.glAccount
      ? generalLedgerRowData?.glAccount
      : iDs?.GLAccount
        ? iDs?.GLAccount
        : "",
    Accounttype: singleGLPayload?.AccountType
      ? singleGLPayload?.AccountType
      : "",
    Taxcategory: singleGLPayload?.TaxCategory
      ? singleGLPayload?.TaxCategory
      : "",
    AccountGroup: singleGLPayload?.AccountGroup
      ? singleGLPayload?.AccountGroup
      : "",
    GLname: singleGLPayload?.ShortText ? singleGLPayload?.ShortText : "",
    Description: singleGLPayload?.LongText ? singleGLPayload?.LongText : "",
    TradingPartner: singleGLPayload?.TradingPartner
      ? singleGLPayload?.TradingPartner
      : "",
    GroupAccNo: singleGLPayload?.GroupAccountNumber
      ? singleGLPayload?.GroupAccountNumber
      : "",
    AccountCurrency: singleGLPayload?.AccountCurrency
      ? singleGLPayload?.AccountCurrency
      : "",
    Exchangerate: singleGLPayload?.ExchangeRateDifferenceKey
      ? singleGLPayload?.ExchangeRateDifferenceKey
      : "",
    Balanceinlocrcy:
      singleGLPayload?.OnlyBalanceInLocalCurrency === true ? "X" : "",
    Pstnwotax: singleGLPayload?.PostingWithoutTaxAllowed === true ? "X" : "",
    ReconAcc: singleGLPayload?.ReconAccountForAccountType
      ? singleGLPayload?.ReconAccountForAccountType
      : "",
    Valuationgrp: singleGLPayload?.ValuationGroup
      ? singleGLPayload?.ValuationGroup
      : "",
    AlterAccno: singleGLPayload?.AlternativeAccountNumber
      ? singleGLPayload?.AlternativeAccountNumber
      : "",
    Openitmmanage: singleGLPayload?.OpenItemManagement === true ? "X" : "",
    Sortkey: singleGLPayload?.SortKey ? singleGLPayload?.SortKey : "",
    CostEleCategory: singleGLPayload?.CElemCategory
      ? singleGLPayload?.CElemCategory : '',
    FieldStsGrp:
      singleGLPayload?.FieldStatusGroup
        ? singleGLPayload?.FieldStatusGroup
        : getValueForFieldNameForPayload(
          iDs?.viewData["Create/Bank/Interest"]?.["Control of Document creation in Company Code"],
          "Field Status Group"
        ),
    PostAuto: singleGLPayload?.PostAutomaticallyOnly === true ? "X" : "",
    Supplementautopost:
      singleGLPayload?.SupplementAutoPostings === true ? "X" : "",
    Planninglevel: singleGLPayload?.PlanningLevel
      ? singleGLPayload?.PlanningLevel
      : "",

    Relvnttocashflow: singleGLPayload?.RelevantToCashFlow === true ? "X" : "",
    HouseBank: singleGLPayload?.HouseBank ? singleGLPayload?.HouseBank : "",
    AccountId: singleGLPayload?.AccountId ? singleGLPayload?.AccountId : "",
    Interestindicator: singleGLPayload?.InterestIndicator
      ? singleGLPayload?.InterestIndicator
      : "",
    ICfrequency: singleGLPayload?.InterestCalculationFrequency
      ? singleGLPayload?.InterestCalculationFrequency
      : "",
    KeydateofLIC: singleGLPayload?.KeyDateOfLastIntCalc
      ? singleGLPayload?.KeyDateOfLastIntCalc
      : "",
    LastIntrstundate: singleGLPayload?.DateOfLastInterestRun
      ? singleGLPayload?.DateOfLastInterestRun
      : "",
    AccmngExistsys: "",
    Infationkey: "",
    Tolerancegrp: "",
    AuthGroup: "",
    AccountClerk: "",
    ReconAccReady: "",
    PostingBlocked: "",
    PlanningBlocked: "",
    GeneralInfoID: "",
    RequestPriority: singleGLPayloadMain?.["ChoosePriorityLevel"] ? singleGLPayloadMain?.["ChoosePriorityLevel"] : '',
    BusinessJustification: singleGLPayloadMain?.["BusinessJustification"] ? singleGLPayloadMain?.["BusinessJustification"] : '',
    SAPorJEErrorCheck: singleGLPayloadMain?.["DidYouReceiveASAPOrJEError?"] ? singleGLPayloadMain?.["DidYouReceiveASAPOrJEError?"] === 'Yes' ? true : false : '',
    TaxableCheck: singleGLPayloadMain?.["AreYouTaxable?"] ? singleGLPayloadMain?.["AreYouTaxable?"] === 'Yes' ? true : false : '',
    SpecificPostingCheck: singleGLPayloadMain?.["AreYouMakingLedgerSpecificPosting?"] ? singleGLPayloadMain?.["AreYouMakingLedgerSpecificPosting?"] === 'Yes' ? true : false : '',
    ManualEntriesCheck: singleGLPayloadMain?.["IsThisAccountUsedForManualEntriesOnly?"] ? singleGLPayloadMain?.["IsThisAccountUsedForManualEntriesOnly?"] === 'Yes' ? true : false : '',
    TradingPartnerCheck: singleGLPayloadMain?.["IsTradingPartnerNeeded?"] ? singleGLPayloadMain?.["IsTradingPartnerNeeded?"] === 'Yes' ? true : false : '',
    GLSubAccountType: singleGLPayloadMain?.["GLSubAccountType"] ? singleGLPayloadMain?.["GLSubAccountType"] : '',
    // ProductionDateEstm: "/Date(*************)/",
    // Message: "Test Message",
    ProductionDateEstm: singleGLPayloadMain?.["WhenDoYouNeedThisInProductionBy?"] ?
      "/Date(" + Date.parse(singleGLPayloadMain?.["WhenDoYouNeedThisInProductionBy?"]) + ")/" : "",
    Message: singleGLPayloadMain?.["Message"] ? singleGLPayloadMain?.["Message"] : '',
    FERCInformation: getfercInfoData(),
    BusinessSegment: singleGLPayloadMain?.["BusinessSegment"] ? singleGLPayloadMain?.["BusinessSegment"]?.toUpperCase() : 'NA',
    OIMCSLIndicator: checkOIMCSLIndicator(),
    //taxFieldCheck: singleGLPayload?.TaxCategory?.length > 0 ? "TRUE" : 'NA',
    taxFieldCheck: isTaxableCheck(),
    Info: checkElements(),
    FunctionalArea: singleGLPayload?.FunctionalArea
      ? singleGLPayload?.FunctionalArea :
      getValueForFieldNameForPayload(
        iDs?.viewData["Type/Description"]?.["Detailed Control for P&L Statement Accounts"],
        "Functional Area"
      ),

    OpenItemManagebyLedgerGrp: singleGLPayload?.OpenItemManagementByLedgerGroup === true ? "X" : "",


    InternalUOM: singleGLPayload?.InternalUOM
      ? singleGLPayload?.InternalUOM :
      getValueForFieldNameForPayload(
        iDs?.viewData["Control Data"]?.["Account Settings in Controlling Area"],
        "Internal UOM"
      ),

    RecordQuantity: singleGLPayload?.RecordQuantity === true ? "X" : getValueForFieldNameForPayload(
      iDs?.viewData["Control Data"]?.["Account Settings in Controlling Area"],
      "Record Quantity"
    ) === true ? "X" : "",

    PostingBlockedCOA: singleGLPayload?.BlockedForPostingatCOA === true ? "X" : "",
    PostingBlockedCoCd: singleGLPayload?.BlockedForPostingCompanyCode === true ? "X" : ""


  };

  console.log(payload, "payload=================")

  const handleSetEditedPayload = () => {
    let activeTabName = factorsArray[activeStep];
    console.log("activeTabName", activeTabName, factorsArray);
    let viewDataArray = Object.entries(generalLedgerViewData);

    // viewDataArray.push(costCenterViewData);

    console.log("viewDataArray", viewDataArray);
    const toSetArray = {};
    viewDataArray?.map((item) => {
      console.log("bottle", item[1]);
      let temp = Object.entries(item[1]); //Basic Data\
      console.log("notebook", temp);
      temp.forEach((fieldGroup) => {
        fieldGroup[1].forEach((field) => {
          if (field?.fieldType === "Calendar") {
            toSetArray[
              field.fieldName
                .replaceAll("(", "")
                .replaceAll(")", "")
                .replaceAll("/", "")
                .replaceAll("-", "")
                .replaceAll(".", "")
                .split(" ")
                .join("")
            ] = parseInt(field.value.replace("/Date(", "").replace(")/", ""));
          } else {
            toSetArray[
              field.fieldName
                .replaceAll("(", "")
                .replaceAll(")", "")
                .replaceAll("/", "")
                .replaceAll("-", "")
                .replaceAll(".", "")
                .split(" ")
                .join("")
            ] = field.value;
          }
        });
      });
      return item;
    });
    console.log("toSetArray", toSetArray);
    dispatch(setPayloadWhole(toSetArray));
  };

  const handleOptionChange = (rows, ccName) => {
    getDropDownForWbsused(rows?.compCode, rows?.id)
    console.log(rows, ccName, "compCode")
    let newValue = ''
    optionsData[rows?.compCode].filter((item) => {
      if (item?.code === ccName)
        newValue = item.desc
      //console.log(item.desc, "descripto")
    })

    dispatch(
      setPayloadForNewChange({
        keyname: `${'ccUsed'}_${rows.id}`,
        data: ccName,
      }))


    dispatch(
      setPayloadForNewChange({
        keyname: `${'fercIndicator'}_${rows.id}`,
        data: newValue,
      }))
    dispatch(
      setPayloadForNewChange({
        keyname: `${'wbsUsed'}_${rows.id}`,
        data: '',
      }))
    // dispatch(
    //   setPayloadForNewChange({
    //     keyname: `${'fercAccount'}_${rows.id}`,
    //     data: newValue,
    // }))

  }

  const handleOptionChangeWbs = (rows, wbsused) => {
    console.log(wbsused, "ccname55525")

    getCCUsedandFercFromWbsUsed(rows, wbsused)

    dispatch(
      setPayloadForNewChange({
        keyname: `${'wbsUsed'}_${rows.id}`,
        data: wbsused,
      }))

    // dispatch(
    //   setPayloadForNewChange({
    //     keyname: `${'wbsUsed'}_${rows.id}`,
    //     data: wbsused,
    //   }))

  }

  const getCCUsedandFercFromWbsUsed = (rows, wbsused) => {
    const payload = {
      "records": [
        wbsused
      ]
    };

    const hSuccess = (data) => {
      const responseBody = data?.body;
      console.log(responseBody, "dataaaaaaaaaaaaa", wbsused);
      //console.log(responseBody[0][wbsused]?.code,responseBody[0][wbsused]?.desc,"data===ofres")
      dispatch(
        setPayloadForNewChange({
          keyname: `${'ccUsed'}_${rows.id}`,
          data: responseBody[0][wbsused]?.code,
        }))
      //console.log(`${'fercIndicator'}_${rows.id}`,"fercrowsIdData")
      dispatch(setPayloadForNewChange({ keyname: `${'fercIndicator'}_${rows.id}`, data: responseBody[0][wbsused]?.desc }));

      // dispatch(
      //   setPayloadForNewChange({
      //     keyname: `${'fercIndicator'}_${rows.id}`,
      //     data: newValue,
      // }))

    };

    const hError = (error) => {
      console.error("Error fetching data", error);
    };

    doAjax(
      `/${destination_GeneralLedger}/data/getCCAndFercIndicatorBasedOnWBS`,
      "post",
      hSuccess,
      hError,
      payload
    );
  }



  function removeHiddenAndEmptyObjects(obj) {
    console.log(obj, "obj---")
    for (let prop in obj) {
      if (obj.hasOwnProperty(prop)) {
        if (Array.isArray(obj[prop])) {
          // If property is an array, iterate over its elements
          obj[prop] = obj[prop].filter((item) => item.visibility !== "Hidden");
          console.log(obj[prop], "obj[prop]===")
          if (obj[prop].length === 0) {
            // Remove the property if the array is empty
            delete obj[prop];
          }
        } else if (typeof obj[prop] === "object") {
          // If property is an object, recursively call the function
          obj[prop] = removeHiddenAndEmptyObjects(obj[prop]);
          if (Object.keys(obj[prop]).length === 0) {
            // Remove the property if the object is empty
            delete obj[prop];
          }
        }
      }
    }
    return obj;
  }

  const getDropdownDataForCompCode = (compcode, id) => {
    const payload = {
      compCode: compcode,
    };

    const hSuccess = (data) => {
      const responseBody = data.body;
      console.log(data, "dataaaaaaaaaaaaa");
      const options = responseBody.map(item => ({
        code: item.code, // Adjust according to your data structure
        desc: item.desc, // Adjust according to your data structure
      }));
      dispatch(setDropDown({ keyName: compcode, data: options }));

    };

    const hError = (error) => {
      console.error("Error fetching data", error);
    };

    doAjax(
      `/${destination_GeneralLedger}/data/getCCAndFercIndicator?compCode=${compcode}`,
      "get",
      hSuccess,
      hError,
      payload
    );
  };

  //const getDropDownForWbsused =(compcode)=>{
  const getDropDownForWbsused = (compcode, id) => {
    const payload = {
      compCode: compcode,
    };

    const hSuccess = (data) => {
      const responseBody = data.body;
      console.log(data, "dataaaaaaaaaaaaa");

      dispatch(setDropDown({ keyName: `${'wbsUsed'}_${id}`, data: responseBody }));
    };

    const hError = (error) => {
      console.error("Error fetching data", error);
    };

    doAjax(
      `/${destination_GeneralLedger}/data/getWBSBasedOnCompCode?companyCode=${compcode}`,
      "get",
      hSuccess,
      hError,
      payload
    );
  };

  const getGeneralLedgerDisplayData = () => {
    var payload = taskData?.body?.id
      ? {
        id: taskData?.body?.id ? taskData?.body?.id : "",
        requestId: taskData?.body?.requestId ? taskData?.body?.requestId?.slice(8) : "",
        glAccount: taskData?.body?.glAccount ? taskData?.body?.glAccount : "",
        compCode: taskData?.body?.compCode,
        reqStatus: taskData?.body?.reqStatus,
        screenName:
          taskRowDetails?.requestType === "Create" ? "Create" :
            taskRowDetails?.requestType === "Extend" ? "Extend" : "Display",
        accountType: taskData?.body?.AccountType ? taskData?.body?.AccountType : "",
        chartOfAccount: taskData?.body?.coa ? taskData?.body?.coa : "",
      }
      : {
        id: generalLedgerRowData?.reqStatus ? generalLedgerRowData?.id : "",
        chartOfAccount: generalLedgerRowData?.chartOfAccount ? generalLedgerRowData?.chartOfAccount : "",
        requestId: generalLedgerRowData?.requestId ? generalLedgerRowData?.requestId?.slice(8) : "",
        glAccount: generalLedgerRowData?.glAccount
          ? generalLedgerRowData?.glAccount
          : "",
        compCode: generalLedgerRowData?.compCode
          ? generalLedgerRowData?.compCode
          : "",
        reqStatus: generalLedgerRowData?.reqStatus
          ? generalLedgerRowData?.reqStatus
          : "Approved",
        screenName: generalLedgerRowData?.requestType === 'Display For Extend'
          ? "Display For Extend" : generalLedgerRowData?.requestType === 'Create'
            ? "Create" :
            generalLedgerRowData?.requestType === 'Extend' ?
              "Extend"
              : "Display",
        //screenName: "Display",
        accountType: generalLedgerRowData?.glAccountType ? generalLedgerRowData?.glAccountType : taskData?.body?.AccountType ? taskData?.body?.AccountType : ''
      };
    const hSuccess = (data) => {
      const responseBody = data?.body?.viewData;
      const responseIDs = data.body;
      setIds(responseIDs);
      //getGlAccountRange(responseIDs?.)
      getGlAccountRange(singleGLPayload?.AccountType)
      //console.log(generalLedgerRowData?.requestType,"requestTypestatus")
      let viewDataArray = Object.entries(responseBody);
      console.log(viewDataArray, "viewDataArray")
      const toSetArray = {};
      viewDataArray?.map((item) => {
        //alert("coming")
        console.log("bottle", item[1]);
        let temp = Object.entries(item[1]); //Basic Data\
        console.log("notebook", temp);
        temp.forEach((fieldGroup) => {
          fieldGroup[1].forEach((field) => {
            toSetArray[
              field.fieldName
                .replaceAll("(", "")
                .replaceAll(")", "")
                .replaceAll("/", "")
                .replaceAll("-", "")
                .replaceAll(".", "")
                .split(" ")
                .join("")
            ] = field.value;
          });
        });
        return item;
      });
      dispatch(setPayloadWhole(toSetArray));
      console.log(data.body["FERC Information"], "FERC Information-------")
      if (generalLedgerRowData?.requestType === 'Extend' || taskRowDetails?.requestType === "Extend" || generalLedgerRowData?.requestType === 'Create' || taskRowDetails?.requestType === "Create") {

        //console.log(data.body["General Information"], "GeneralInfoFordata")
        for (const key in data.body["General Information"]) {
          console.log(`${key}: ${data.body["General Information"][key]}`, "keydatapair");

          if (key == 'When Do You Need This In Production By?') {

            dispatch(
              setSinglegeneralLedgerPayloadGI({
                keyName: key
                  .replaceAll("(", "")
                  .replaceAll(")", "")
                  .replaceAll("/", "")
                  .replaceAll("-", "")
                  .replaceAll(".", "")
                  .split(" ")
                  .join(""),
                data: data?.body["General Information"][key]?.length > 0
                  ? moment(data?.body["General Information"][key]).format(appSettings?.dateFormat)
                  : ''
              })
            )

          } else {
            dispatch(
              setSinglegeneralLedgerPayloadGI({
                keyName: key
                  .replaceAll("(", "")
                  .replaceAll(")", "")
                  .replaceAll("/", "")
                  .replaceAll("-", "")
                  .replaceAll(".", "")
                  .split(" ")
                  .join(""),
                data: data?.body["General Information"][key] === "false" ? 'No' :
                  data?.body["General Information"][key] === "true" ? 'Yes' : data.body["General Information"][key],
              })
            )
          }

        }
      }
      console.log(responseBody, "responseBodyinhsuccess")
      //const responseIDs = data.body;
      // dispatch(setGeneralLedgerViewData(responseBody));
      // const categoryKeys = Object.keys(responseBody);
      //const index = categoryKeys.indexOf("Attachments & Comments");
      let removeHiddenVisibilityEachlevel = removeHiddenAndEmptyObjects(responseBody)
      console.log(removeHiddenVisibilityEachlevel, "removeHiddenVisibilityEachlevel");
      //setOnlyDispayKey(removeHiddenVisibilityEachlevel)

      dispatch(setGeneralLedgerViewData(removeHiddenVisibilityEachlevel));
      const categoryKeys = Object.keys(removeHiddenVisibilityEachlevel);

      setFactorsArray(categoryKeys);
      if (generalLedgerRowData?.requestType === 'Display For Extend' || generalLedgerRowData?.requestType === 'Display For Extend') {
        let arrofFerc = []
        extendedCompanyCode?.map((itemData, index) => {
          console.log(itemData, "itemData=====")
          let hash_data = {}
          hash_data["id"] = index
          hash_data["compCode"] = itemData
          hash_data["glAccount"] = generalLedgerRowData?.glAccount
          hash_data["glLongDescription"] = responseBody["Type/Description"]?.Description?.[0]?.value
          arrofFerc.push(hash_data)
        })
        let updatedData = arrofFerc;
        //console.log(updatedData, "updatedData====")
        setFercRows(updatedData);
      } else {
        let arrofFerc = []
        data.body["FERC Information"]?.map((itemData, index) => {
          //console.log(itemData,"itemData=====")
          const dynamicPropertyRuleSet = `ruleSet_${index}`
          const dynamicPropertyWbsUsed = `wbsUsed_${index}`
          const dynamicPropertyCCUsed = `ccUsed_${index}`
          const dynamicPropertyFercIndicator = `fercIndicator_${index}`
          const dynamicPropertyFercAccount = `fercAccount_${index}`
          dispatch(
            setPayloadForNewChange({
              keyname: dynamicPropertyRuleSet,
              data: itemData?.ruleSet,
            })
          )
          dispatch(
            setPayloadForNewChange({
              keyname: dynamicPropertyCCUsed,
              data: itemData?.ccUsed,
            })
          )
          dispatch(
            setPayloadForNewChange({
              keyname: dynamicPropertyWbsUsed,
              data: itemData?.wbsUsed,
            })
          )
          dispatch(
            setPayloadForNewChange({
              keyname: dynamicPropertyFercIndicator,
              data: itemData?.fercIndicator,
            })
          )
          dispatch(
            setPayloadForNewChange({
              keyname: dynamicPropertyFercAccount,
              data: itemData?.fercAccount,
            })
          )
          getDropdownDataForCompCode(itemData?.compCode, index);
          getDropDownForWbsused(itemData?.compCode, index)
          let hash_data = {}
          hash_data["id"] = index
          hash_data["compCode"] = itemData?.compCode
          hash_data["glAccount"] = itemData?.glAccount
          hash_data["glLongDescription"] = itemData?.glLongDescription
          hash_data["ruleSet"] = itemData?.ruleSet
          hash_data["ccUsed"] = itemData?.ccUsed
          hash_data["wbsUsed"] = itemData?.wbsUsed
          hash_data["fercIndicator"] = itemData?.fercIndicator
          hash_data["fercAccount"] = itemData?.fercAccount

          arrofFerc.push(hash_data)
        })

        console.log(arrofFerc, "arrofFerc")
        setFercRows(arrofFerc);
      }


      console.log(responseIDs?.GLAccount)
      getCompanyCodeRows(responseIDs)
      //getGITemplate(responseIDs?.GLAccount)
      //factorsArray.push('Attachment & Comments')
      const mappedData = categoryKeys?.map((category) => ({
        category,
        data: responseBody[category],
        setIsEditMode,
      }));

      setCostCenterDetails(mappedData);

      console.log("mappedData", mappedData, iDs);
      // dispatch(setSingleCostCenterPayload(data));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/displayGeneralLedger`,
      "post",
      hSuccess,
      hError,
      payload
    );
    setPageName(payload.screenName);
  };
  const getButtons = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_DYN_BTN_DT",
      version: "v2",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_DYN_BTN_MODULE_NAME": "General Ledger",
          "MDG_CONDITIONS.MDG_DYN_BTN_REQUEST_TYPE": "Create",
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    // setIsLoading(true);
    // const formData = new FormData();

    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        let responseData =
          data?.data?.result[0]?.MDG_DYN_BTN_ACTION_TYPE;
        // let lookupKeyName= data?.data?.result[0]?.conditions[0]?.["MDG_CONDITIONS.MDG_FIELD_NAME"] 
        setButtonsIDM(responseData);
      }
    };

    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(
        `/${destination_IDM}/rest/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      doAjax(
        `/${destination_IDM}/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };
  const handleAlertClose = () => {
    setShowAlert(false);
  };

  // const handleDialogConfirm = () => {

  //   if (isMandatory && !userInput) {
  //     // alert('Input is mandatory');
  //     setShowAlert(true); // Show alert
  //     // <Alert severity="warning">Input is mandatory</Alert>
  //     return;
  //   } else {
  //     handleMessageDialogClose();
  //     executeAction(currentActionType, currentButtonState);
  //   }
  //   handleMessageDialogClose();
  //   // setMessageDialogMessage(dialogSnackbarMsg);
  //   //setMessageDialogMessage("");
  //   // setsuccessMsg(true);
  //   // handleSnackBarOpen();
  // };

  const handleButtonAction = (button) => {
    setMessageDialogMessage("")
    setCurrentButtonState(button);
    setDialogOkText("Proceed")
    //  setMessageDialogSeverity("success")
    setDialogTitle(button.MDG_DYN_BTN_COMMENT_BOX_NAME);

    setIsMandatory(button.MDG_DYN_BTN_COMMENT_BOX_INPUT === "Mandatory");

    setTextInput(
      button.MDG_DYN_BTN_COMMENT_BOX_INPUT === "Mandatory" ||
        button.MDG_DYN_BTN_COMMENT_BOX_INPUT === "Optional"
        ? true
        : false
    );
    // setMessageDialogMessage(button.MDG_DYN_BTN_SNACKBAR_MSG); 
    setCurrentActionType(button.MDG_DYN_BTN_ACTION_TYPE);

    if (
      button.MDG_DYN_BTN_COMMENT_BOX_INPUT === "Mandatory" ||
      button.MDG_DYN_BTN_COMMENT_BOX_INPUT === "Optional"
    ) {
      setTestrunStatus(false)
      handleMessageDialogClickOpen();
    } else {
      // handleSnackBarOpen();
      executeAction(button.MDG_DYN_BTN_ACTION_TYPE, button);
    }
  };
  const executeAction = (actionType, button) => {
    switch (actionType) {
      case 'handleSubmitForApproval':
        handleGeneralLedgerSubmitCreate(button);
        break;
      case 'handleSubmitForReview':
        handleGeneralLedgerReviewCreate(button);
        break;
      case 'handleSendBack':
        handleCorrectionApproverCreate(button)
        //sendforcorrection api for mid parallel 
        break;
      case 'handleCorrection':
        handleCorrectionMDMCreate(button);
        ;//sendforreview api for final
        break;
      case 'handleReject':
        // Handle reject action
        handleReject(button);
        break;
      case 'handleValidate':
        onValidateGeneralLedgerApprover(button);
        break;
      case 'handleSAPSyndication':
        handleGeneralLedgerApproveCreate(button);
        break;
      default:
        console.log('Unknown action type');
    }
  };
  console.log("ID", iDs);

  useEffect(() => {
    getGlAccountRange(generalLedgerRowData?.glAccountType ? generalLedgerRowData?.glAccountType : taskData?.body?.AccountType ? taskData?.body?.AccountType : '')
  }, [generalLedgerRowData, taskData])

  const getHouseBank = (copyfromCopCodeData) => {
    console.log(copyfromCopCodeData, "copyfromCopCodeData")
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "HouseBank", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getHouseBank?companyCode=${copyfromCopCodeData}`,
      "get",
      hSuccess,
      hError
    );
  };


  const getGlAccountRange = (compCodeType) => {

    let payload = {
      "decisionTableId": null,
      "decisionTableName": "MDG_GL_ACCOUNT_NO_RANGE_DT",
      "version": "v5",
      "rulePolicy": null,
      "validityDate": null,
      "conditions": [
        {
          "MDG_CONDITIONS.MDG_SERIAL_NO": 1
        }
      ],
      "systemFilters": null,
      "systemOrders": null,
      "filterString": null
    };
    //setIsLoading(true);
    // const formData = new FormData();
    const hSuccess = (data) => {
      //setIsLoading(false);
      if (data.statusCode === 200) {
        //console.log(compCodeType, "compCodeType")
        let responseData = data?.data?.result[0]?.MDG_GL_ACCOUNT_NO_RANGE_ACTION_TYPE;

        console.log(singleGLPayloadMain?.["GLSubAccountType"], compCodeType, "responseDataidmRange")


        const copyfromCopCodeData = responseData.find(item => item.MDG_GL_ACCOUNT_TYPE_CODE === compCodeType && item.MDG_GL_SUB_ACCOUNT_GRP === singleGLPayloadMain?.["GLSubAccountType"])?.MDG_GL_COMPANY_CODE || '';
        getHouseBank(copyfromCopCodeData)
        //setCopyFromComapnyCode(copyfromCopCodeData)
      } else {

      }
      handleClose();
    };

    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfigReducer.environment === "localhost") {
      doAjax(
        `/${destination_IDM}/rest/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      doAjax(
        `/${destination_IDM}/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }

  }


  useEffect(() => {
    getGlAccountType()
    getAccountGroup();
    getButtons();
    getTaxCategory();
    getFieldStatusGroup();
    getCostElementCategory();
    getHouseBank();
    getAccountCurrency()


    getAlternativeAccountNumber();
    getGroupAccountNumber();
    getAttachementFromIdM();
    //if(generalLedgerRowData?.requestType === 'Display For Extend'){
    getGITemplate();
    //getButtons();
    //}
    //getGeneralLedgerDisplayData();
    //getAccountId()
    //
    setTimeout(() => {
      //getGeneralLedgerDisplayData()
      getDemodata()
    }, 1000);
    setTimeout(() => {
      if (generalLedgerRowData?.newGLAccount) {
        const newGLAccount = generalLedgerRowData?.newGLAccount;

        // Check if newGLAccount starts with '51' or '61'
        if (newGLAccount?.startsWith('51') || newGLAccount?.startsWith('61')) {
          getGlNameandDescriptionFromGlAccount();
        }
      }
      //getDemodata()
    }, 2000);

  }, []);

  const getDemodata = () => {
    const responseBody = demoData?.viewData;
    //console.log(responseBody["Type/Description"]?.Description?.[0], "responseBody++++")
    //const toSetArray = {};
    let viewDataArray = Object.entries(responseBody);
    console.log(viewDataArray, "viewDataArray===")
    const toSetArray = {};
    viewDataArray?.map((item) => {
      let temp = Object.entries(item[1]); //Basic Data\
      console.log(temp, "temp===")
      temp.forEach((fieldGroup) => {
        fieldGroup[1].forEach((field) => {
          console.log(field, "field}}}")
          if (field.fieldName === 'Long Text' || field.fieldName === 'Short Text') {
            toSetArray[
              field?.fieldName
                .replaceAll("(", "")
                .replaceAll(")", "")
                .replaceAll("/", "")
                .replaceAll("-", "")
                .replaceAll(".", "")
                .split(" ")
                .join("")
            ] = "";
          } else {
            toSetArray[
              field?.fieldName
                .replaceAll("(", "")
                .replaceAll(")", "")
                .replaceAll("/", "")
                .replaceAll("-", "")
                .replaceAll(".", "")
                .split(" ")
                .join("")
            ] = field?.value;
          }
        });
      });
      return item;
    });
    dispatch(setPayloadWhole(toSetArray));
    let removeHiddenVisibilityEachlevel = removeHiddenAndEmptyObjects(responseBody)
    console.log(removeHiddenVisibilityEachlevel, "removeHiddenVisibilityEachlevel");
    //setOnlyDispayKey(removeHiddenVisibilityEachlevel)
    const responseIDs = demoData;
    dispatch(setGeneralLedgerViewData(removeHiddenVisibilityEachlevel));
    const categoryKeys = Object.keys(removeHiddenVisibilityEachlevel);
    setFactorsArray(categoryKeys);

    if (true) {

      //console.log(data.body["General Information"], "GeneralInfoFordata")
      for (const key in demoData["General Information"]) {
        console.log(`${key}: ${demoData["General Information"][key]}`, "keydatapair");

        if (key == 'When Do You Need This In Production By?') {

          dispatch(
            setSinglegeneralLedgerPayloadGI({
              keyName: key
                .replaceAll("(", "")
                .replaceAll(")", "")
                .replaceAll("/", "")
                .replaceAll("-", "")
                .replaceAll(".", "")
                .split(" ")
                .join(""),
              data: demoData["General Information"][key]?.length > 0
                ? moment(demoData["General Information"][key]).format(appSettings?.dateFormat)
                : ''
            })
          )

        } else {
          dispatch(
            setSinglegeneralLedgerPayloadGI({
              keyName: key
                .replaceAll("(", "")
                .replaceAll(")", "")
                .replaceAll("/", "")
                .replaceAll("-", "")
                .replaceAll(".", "")
                .split(" ")
                .join(""),
              data: demoData["General Information"][key] === "false" ? 'No' :
                demoData["General Information"][key] === "true" ? 'Yes' : demoData["General Information"][key],
            })
          )
        }

      }
    }
    //const attachment = ["FERC Information", "Attachment & Documents"];
    //factorsArray.concat(attachment);
    // factorsArray.push('Attachment & Comments')
    console.log(generalLedgerRowData, "generalLedgerRowData?.newGLAccount")
    //console.log(regulatedCompanyCode,"jewkjkwregulatedCompanyCode")
    let arrofFerc = []
    // generalLedgerRowData?.companyCode?.newCompanyCode?.map((itemData, index) => {
    //   //regulatedCompanyCode?.map((itemData, index) => {
    //     console.log(regulatedCompanyCode,"jewkjkw",dropDownData["RegulatedCompanyCode"])


    //   if (dropDownData["RegulatedCompanyCode"]?.includes(itemData?.code)) {
    //     let hash_data = {}
    //     hash_data["id"] = index
    //     hash_data["compCode"] = itemData?.code
    //     hash_data["glAccount"] = generalLedgerRowData?.newGLAccount
    //     hash_data["glLongDescription"] = responseBody["Type/Description"]?.Description?.[0]?.value
    //     arrofFerc.push(hash_data)
    //   }
    // })


    // let updatedData = arrofFerc;
    // console.log(updatedData, "updatedData========")
    // updatedData?.map((itemData)=>{
    //   console.log(itemData,"itemData============")
    //   getDropdownData(itemData?.compCode);
    // })
    // setFercRows(updatedData);
    const mappedData = categoryKeys?.map((category) => ({
      category,
      data: responseBody[category],
      setIsEditMode,
    }));

    setCostCenterDetails(mappedData);
    setIds(responseIDs);
    console.log("mappedData", mappedData, iDs);
  }

  const getAccountGroup = () => {
    //const hSuccess = (data) => {
    //   dispatch(setDropDown({ keyName: "AccountGroup", data: data.body }));
    // };
    // const hError = (error) => {
    //   console.log(error);
    // };
    // doAjax(
    //   `/${destination_GeneralLedger}/data/getAccountGroupCodeDesc?chartAccount=${generalLedgerRowData?.chartOfAccounts?.newChartOfAccount?.code}`,
    //   "get",
    //   hSuccess,
    //   hError
    // );
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_CUSTOM_DROPDOWN_LIST",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MODULE": "General Ledger",
          "MDG_CONDITIONS.MDG_FIELD_NAME": "Account Group"
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    // setIsLoading(true);
    // const formData = new FormData();

    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        const lookupData =
          data?.data?.result[0]?.MDG_CUSTOM_LOOKUP_ACTION_TYPE || [];
        let lookupKeyName = data?.data?.result[0]?.conditions[0]?.["MDG_CONDITIONS.MDG_FIELD_NAME"]
        console.log("questionData", lookupData);
        console.log("lookupKeyName", lookupKeyName);
        let lookupDataArr = []
        lookupData?.map((itemData) => {
          let lookupDataHash = {}
          lookupDataHash["code"] = itemData?.MDG_LOOKUP_CODE
          lookupDataHash["desc"] = itemData?.MDG_LOOKUP_DESC
          lookupDataArr.push(lookupDataHash)
        })
        console.log(lookupDataArr, "lookupfoAccountGroup")
        //dispatch(setDropDown({ keyName: AccountGroup, data: lookupData }));
        dispatch(setDropDown({ keyName: "AccountGroup", data: lookupDataArr }));
        // setQuestions(questionsData);
      }
    };

    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(
        `/${destination_IDM}/rest/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      doAjax(
        `/${destination_IDM}/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };

  const getGlAccountType = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_CUSTOM_DROPDOWN_LIST",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MODULE": "General Ledger",
          "MDG_CONDITIONS.MDG_FIELD_NAME": "Account Type"
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };

    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        const lookupData =
          data?.data?.result[0]?.MDG_CUSTOM_LOOKUP_ACTION_TYPE || [];
        let lookupKeyName = data?.data?.result[0]?.conditions[0]?.["MDG_CONDITIONS.MDG_FIELD_NAME"]
        console.log("questionData", lookupData);
        console.log("lookupKeyName", lookupKeyName);
        let lookupDataArr = []
        lookupData?.map((itemData) => {
          let lookupDataHash = {}
          lookupDataHash["code"] = itemData?.MDG_LOOKUP_CODE
          lookupDataHash["desc"] = itemData?.MDG_LOOKUP_DESC
          lookupDataArr.push(lookupDataHash)
        })
        console.log(lookupDataArr, "lookupfoAccountType")
        //dispatch(setDropDown({ keyName: lookupKeyName, data: lookupData }));
        dispatch(setDropDown({ keyName: "AccountType", data: lookupDataArr }));
        // setQuestions(questionsData);
      }
    };

    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(
        `/${destination_IDM}/rest/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      doAjax(
        `/${destination_IDM}/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };


  const getGITemplate = () => {
    //console.log(generalLedgerRowData?.accountGroup?.accontGroupWithCopy?.Description, generalLedgerRowData?.accountType?.newAccountType?.code, "forgitemplate data")
    let payload = {
      "decisionTableId": null,
      "decisionTableName": "MDG_GI_GL_QUESTIONS",
      "version": "v4",
      "rulePolicy": null,
      "validityDate": null,
      "conditions": [
        {
          "MDG_CONDITIONS.MDG_GI_MODULE": "GL",
          "MDG_CONDITIONS.MDG_GI_SCENARIO": "Create",
          "MDG_CONDITIONS.MDG_GI_GL_ACCOUNT_GROUP": generalLedgerRowData?.AccountGroup ? generalLedgerRowData?.AccountGroup : taskData?.body?.AccountGroup ? taskData?.body?.AccountGroup : 'REVE',
          "MDG_CONDITIONS.MDG_GI_GL_ACCOUNT_TYPE": generalLedgerRowData?.glAccountType ? generalLedgerRowData?.glAccountType : taskData?.body?.AccountType ? taskData?.body?.AccountType : 'P'
        }
      ],
      "systemFilters": null,
      "systemOrders": null,
      "filterString": null
    };
    setIsLoading(true);
    // const formData = new FormData();

    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        // const questionsData = data?.data?.result[0]?.MDG_GI_QUESTIONS_ACTION_TYPE || [];
        const GIData =
          data?.data?.result[0]?.MDG_GI_QUESTIONS_ACTION_TYPE || [];
        const questionsData = GIData.sort(
          (a, b) => a.MDG_SEQUENCE_NO - b.MDG_SEQUENCE_NO
        );
        console.log(questionsData, "questionsData===")
        //console.log(iDs?.GLAccount,"iDs?.GLAccount")
        let glAccount = taskData?.body?.glAccount ? taskData?.body?.glAccount : generalLedgerRowData?.glAccount
        const filteredquestionsData = glAccount?.startsWith("45")
          ? questionsData?.filter(item => item?.MDG_GI_QUESTION_TYPE !== "Business Segment")
          : questionsData;
        console.log(questionsData, "questionsData===")
        console.log(filteredquestionsData, "filteredquestionsData===")

        // console.log()
        setQuestions(filteredquestionsData);

        filteredquestionsData?.map((question) => {
          if (question?.MDG_GI_INPUT_OPTION === 'Radio Button') {
            if (question?.MDG_GI_VISIBILITY === "Mandatory") {
              console.log("insidevisibility");
              (setGLRequiredFieldsGI(question.MDG_GI_QUESTION_TYPE
                .replaceAll("(", "")
                .replaceAll(")", "")
                .replaceAll("/", "")
                .replaceAll("-", "")
                .replaceAll(".", "")
                .split(" ")
                .join(""),))
            }
            if (question?.MDG_GI_QUESTION_TYPE !== "Choose Priority Level") {
              dispatch(
                setSinglegeneralLedgerPayloadGI({
                  keyName: question.MDG_GI_QUESTION_TYPE
                    .replaceAll("(", "")
                    .replaceAll(")", "")
                    .replaceAll("/", "")
                    .replaceAll("-", "")
                    .replaceAll(".", "")
                    .split(" ")
                    .join(""),
                  data: "No",
                })
              )
            } else {
              dispatch(
                setSinglegeneralLedgerPayloadGI({
                  keyName: question.MDG_GI_QUESTION_TYPE
                    .replaceAll("(", "")
                    .replaceAll(")", "")
                    .replaceAll("/", "")
                    .replaceAll("-", "")
                    .replaceAll(".", "")
                    .split(" ")
                    .join(""),
                  data: "Medium",
                })
              )
            }
          } else if (question?.MDG_GI_INPUT_OPTION === 'Dropdown') {
            if (question?.MDG_GI_QUESTION_TYPE == "Business Segment") {
              let dropDownData = question?.MDG_GI_INPUT_VALUE?.split(',')
              let new_arr_businessSeg = []
              dropDownData.map((item) => {
                let new_hash_businessSeg = {}
                new_hash_businessSeg["code"] = item
                new_arr_businessSeg.push(new_hash_businessSeg)
              })
              console.log(new_arr_businessSeg, "new_arr_businessSeg")

              dispatch(setGLRequiredFieldsGI(question.MDG_GI_QUESTION_TYPE
                .replaceAll("(", "")
                .replaceAll(")", "")
                .replaceAll("/", "")
                .replaceAll("-", "")
                .replaceAll(".", "")
                .split(" ")
                .join(""),))

              dispatch(setDropDown({ keyName: "BusinessSegment", data: new_arr_businessSeg }));
            }
          } else {
            if (question?.MDG_GI_VISIBILITY === "Mandatory") {
              console.log("insidevisibility");
              dispatch(setGLRequiredFieldsGI(question.MDG_GI_QUESTION_TYPE
                .replaceAll("(", "")
                .replaceAll(")", "")
                .replaceAll("/", "")
                .replaceAll("-", "")
                .replaceAll(".", "")
                .split(" ")
                .join(""),));
            }
            dispatch(
              setSinglegeneralLedgerPayloadGI({
                keyName: question.MDG_GI_QUESTION_TYPE
                  .replaceAll("(", "")
                  .replaceAll(")", "")
                  .replaceAll("/", "")
                  .replaceAll("-", "")
                  .replaceAll(".", "")
                  .split(" ")
                  .join(""),
                data: "",
              }))
          }

        })


        dispatch(setGeneralInformation(filteredquestionsData));
      }
    };

    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(
        `/${destination_IDM}/rest/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      doAjax(
        `/${destination_IDM}/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };

  const getAttachementFromIdM = () => {
    console.log(generalLedgerRowData?.requestType, "requesttype")
    let payload = {}
    if (generalLedgerRowData?.requestType === 'Extend') {
      payload = {
        "decisionTableId": null,
        "decisionTableName": "MDG_ATTACHMENTS_LIST_DT",
        "version": "v1",
        "rulePolicy": null,
        "validityDate": null,
        "conditions": [
          {
            "MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE": "General Ledger",
            "MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO": "Extend",
            "MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE": 2
          }
        ],
        "systemFilters": null,
        "systemOrders": null,
        "filterString": null
      };
    } else {
      payload = {
        "decisionTableId": null,
        "decisionTableName": "MDG_ATTACHMENTS_LIST_DT",
        "version": "v1",
        "rulePolicy": null,
        "validityDate": null,
        "conditions": [
          {
            "MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE": "General Ledger",
            "MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO": generalLedgerRowData?.requestType === 'Extend' ||
              generalLedgerRowData?.requestType === 'Create'
              ? generalLedgerRowData?.requestType
              : generalLedgerRowData?.requestType === 'Display For Extend'
                ? "Extend" : taskRowDetails?.requestType
                  ? taskRowDetails?.requestType
                  : "Change",
            "MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE": 2
          }
        ],
        "systemFilters": null,
        "systemOrders": null,
        "filterString": null
      };

    }

    setIsLoading(true);
    // const formData = new FormData();
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        let responseData =
          data?.data?.result[0]?.MDG_ATTACHMENTS_ACTION_TYPE;
        setAttachmentFromIdm(responseData)
        console.log(responseData, "responseData====")
        // let templateData = [];
        // responseData.map((element, index) => {
        //   console.log("element", element);

        //   var tempRow = {
        //     id: index,
        //     templateName: element?.MDG_CHANGE_TEMPLATE_NAME,
        //     templateData: element?.MDG_CHANGE_TEMPLATE_FIELD_LIST,
        //   };
        //   templateData.push(tempRow);
        // });
        //setRuleData(templateData);
      } else {
        // setMessageDialogTitle("Create");
        // setsuccessMsg(false);
        // setMessageDialogMessage("Creation Failed");
        // setHandleExtrabutton(false);
        // setMessageDialogSeverity("danger");
        // setMessageDialogOK(false);
        // setMessageDialogExtra(true);
        // handleMessageDialogClickOpen();
        // handleCreateDialogClose();
        // setIsLoading(false);
      }
      //handleClose();
    };

    const hError = (error) => {
      console.log(error);
    };



    if (applicationConfig.environment === "localhost") {
      doAjax(
        `/${destination_IDM}/rest/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      doAjax(
        `/${destination_IDM}/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  }

  useEffect(() => {
    // if (generalLedgerViewData.length === 0) {
    //   return;
    // }
    handleSetEditedPayload();
  }, [generalLedgerViewData]);

  const handleDialogConfirm = () => {
    if (isMandatory && !userInput) {
      setRemarksError(true)
      return;
    } else {
      handleMessageDialogClose();
      executeAction(currentActionType, currentButtonState);
    }
  };



  const handleMessageDialogClose = () => {
    setRemarksError(false)
    setIsMandatory(false)
    setOpenMessageDialog(false);
    setMessageDialogMessage("");
  };
  const handleMessageDialogNavigate = () => {
    navigate("/masterDataCockpitNew/generalLedger");
  };
  const handleSnackBarClose = () => {
    if (validateFlag) {
      setopenSnackbar(false);
      setValidateFlag(false);
    } else {
      if (Object.keys(taskRowDetails)?.length !== 0) {
        setMessageDialogSeverity(false)
        setopenSnackbar(false);
        //setMessageDialogTitle("Success");
        setMessageDialogMessage("")
        //dispatch(clearTaskData())
        dispatch(clearTaskData())
        dispatch(clearProfitCenterPayloadGI())
        dispatch(clearCostCenterPayload())
        dispatch(clearSingleGLPayloadGI())
        dispatch(clearProfitCenterPayload())
        dispatch(clearCostCenter())
        navigate("/workspace/MyTasks");
      } else {
        setMessageDialogSeverity(false)
        setopenSnackbar(false);
        //setMessageDialogTitle("Success");
        setMessageDialogMessage("")
        navigate("/requestBench");
      }
    }
  };

  // const handleSnackBarClose = () => {
  //   if (validateFlag) {
  //     setopenSnackbar(false);
  //     setValidateFlag(false);
  //   } else {
  //     setopenSnackbar(false);
  //     navigate("/masterDataCockpitNew/generalLedger");
  //   }
  // };
  const handleSnackBarOpenValidation = () => {
    setOpenSnackbarValidation(true);
  };
  const handleCheckValidationError = () => {
    return formValidator(
      singleGLPayload,
      requiredFields,
      setFormValidationErrorItems
    );
  };
  const handleCheckValidationErrorGI = () => {
    return formValidator(
      singleGLPayloadMain,
      requiredFieldsGI,
      setFormValidationErrorItemsGI
    );
  };

  const disableActionButton = () => {
    setSubmitForReviewDisabled(true);
    const updatedButtons = filteredButtons?.map((button) => {
      if (
        button?.MDG_DYN_BTN_BUTTON_NAME === "Approve" ||
        button?.MDG_DYN_BTN_BUTTON_NAME === "SAP Syndication" ||
        button?.MDG_DYN_BTN_BUTTON_NAME === "Submit"
      ) {
        return { ...button, MDG_DYN_BTN_BUTTON_STATUS: "DISABLED" };
      }
      return button;
    });
    setFilteredButtons(updatedButtons);
  }

  const handleBack = () => {
    //setSubmitForReviewDisabled(true)
    disableActionButton()
    const isValidation = handleCheckValidationError();
    const isValidationGi = handleCheckValidationErrorGI();
    if (isEditMode) {
      if (isValidation && isValidationGi) {
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
        //dispatch(clearGeneralLedger());
      } else {
        handleSnackBarOpenValidation();
      }
    } else {
      setActiveStep((prevActiveStep) => prevActiveStep - 1);
      //dispatch(clearGeneralLedger());
    }
  };
  // const handleNext = () => {
  //   const isValidation = handleCheckValidationError();
  //   const isValidationGi = handleCheckValidationErrorGI();
  //   //console.log(isValidationGi, "isValidationGi")
  //   if (isEditMode) {
  //     if (isValidation && isValidationGi) {
  //       setActiveStep((prevActiveStep) => prevActiveStep + 1);
  //       //dispatch(clearGeneralLedger());
  //     } else {
  //       handleSnackBarOpenValidation();
  //     }
  //   } else {
  //     setActiveStep((prevActiveStep) => prevActiveStep + 1);
  //     //dispatch(clearGeneralLedger());
  //   }
  // };

  // const handleNext = () => {
  //   setActiveStep((prevActiveStep) => prevActiveStep + 1);
  // }
  //   //if ((shortDescFor5161Series != singleGLPayload?.ShortText) || (longDescriptionFor5161Series!=!= singleGLPayload?.ShortText)){



  //   }

  const handleMessageDialogClickOpenForTrading = () => {
    setOpenMessageDialogForTrading(true);
  }
  const handleMessageDialogClickOpenForTradingPresent = () => {
    setOpenMessageDialogForTradingPresent(true);
  }
  const handleMessageDialogCloseOpenForTrading = () => {
    setOpenMessageDialogForTrading(false);
  }
  const handleMessageDialogCloseOpenForTradingPresent = () => {
    setOpenMessageDialogForTradingPresent(false);
  }

  const handleMessageDialogClickOpenValidate = () => {
    setOpenMessageDialogForValidate(true);
  };
  const handleMessageDialogClickOpenShortlongChange = () => {
    setOpenMessageDialogFor5161ShortLongChange(true)
  }

  const handleNext = () => {
    // Check if shortDescFor5161Series or longDescriptionFor5161Series don't match singleGLPayload?.ShortText
    console.log(activeStep, shortDescFor5161Series, shortDescFor5161Series?.length, "shortLengthinNext")

    if (singleGLPayloadMain["IsTradingPartnerNeeded?"] === "Yes" && activeStep == 1) {
      const allowedStrings = ["AFFILIATE", "INTERCOMPANY"];

      if (allowedStrings.some(str => singleGLPayload?.LongText?.includes(str))) {
        if (activeStep === 1 && (shortDescFor5161Series?.length > 0 || longDescriptionFor5161Series?.length > 0)) {
          if ((shortDescFor5161Series !== singleGLPayload?.ShortText) || (longDescriptionFor5161Series !== singleGLPayload?.LongText)) {
            // Show confirmation dialog
            // const confirmProceed = window.confirm(`This GL has a counter GL number-${gLAccountFor5161Series} with Short Text- ${shortDescFor5161Series}  & Long Text- ${longDescriptionFor5161Series} .You have changed it. So do you want to proceed with your changes?`);
            console.log(gLAccountFor5161Series, "gLAccountFor5161Series====")
            setOpenMessageDialogFor5161ShortLongChange(true)
            setMessageDialogTitle("Short Text And Long Text Duplicate Check");
            setsuccessMsg(false);
            // setMessageDialogMessage(
            //   `This GL has a counter GL number-${gLAccountFor5161Series} with Short Text- ${shortDescFor5161Series}  & Long Text- ${longDescriptionFor5161Series} .You have changed it. So do you want to proceed with your changes?`
            // );
            setMessageDialogMessage(`
              This GL has a counter-GL Account Number ${gLAccountFor5161Series} with short text ({${shortDescFor5161Series}}) and long text (${longDescriptionFor5161Series}). The ${generalLedgerRowData?.newGLAccount}/${gLAccountFor5161Series} GL names should match. Do you wish to proceed?`)

            setHandleExtraText("Proceed")
            setHandleExtrabutton(true);
            // setMessageDialogSeverity("danger");
            setMessageDialogSeverity("warning");
            //setMessageDialogOK(false);
            setMessageDialogExtra(true);
            handleMessageDialogClickOpenShortlongChange();
            return;
          } else if ((shortDescFor5161Series == singleGLPayload?.ShortText) || (longDescriptionFor5161Series == singleGLPayload?.LongText)) {

            console.log(gLAccountFor5161Series, "gLAccountFor5161Series====")
            setOpenMessageDialogFor5161ShortLongChange(true)
            setMessageDialogTitle("Short Text And Long Text Duplicate Check");
            setsuccessMsg(false);
            // setMessageDialogMessage(
            //   `This GL has a counter GL number-${gLAccountFor5161Series} with Short Text- ${shortDescFor5161Series}  & Long Text- ${longDescriptionFor5161Series} Are Same.Do You have changed It?`
            // );
            setMessageDialogMessage(`
              This GL has a counter-GL Account Number ${gLAccountFor5161Series} with short text ({${shortDescFor5161Series}}) and long text (${longDescriptionFor5161Series}). The ${generalLedgerRowData?.newGLAccount}/${gLAccountFor5161Series} GL names should match. Do you wish to proceed?`)
            setHandleExtraText("Proceed")
            setHandleExtrabutton(true);
            // setMessageDialogSeverity("danger");
            setMessageDialogSeverity("warning");
            //setMessageDialogOK(false);
            setMessageDialogExtra(true);
            handleMessageDialogClickOpenShortlongChange();
          } else {
            setOpenMessageDialogFor5161ShortLongChange(true)
            proceedWithNextStep()
          }

        } else {
          const isValidation = handleCheckValidationError();
          const isValidationGI = handleCheckValidationErrorGI()
          if (isValidation && isValidationGI) {
            setActiveStep((prevActiveStep) => prevActiveStep + 1);
            //dispatch(clearGeneralLedger());
          } else {
            //alert("coming")
            //setActiveStep((prevActiveStep) => prevActiveStep + 1);
            handleSnackBarOpenValidation();
          }
        }
        getCheckIsAffilateOrInterCompany(singleGLPayload?.LongText)
      } else {
        handleMessageDialogClickOpenForTrading();
        //setSubmitForReviewDisabled(true);
      }
    } else {
      if (activeStep === 1 && (shortDescFor5161Series?.length > 0 || longDescriptionFor5161Series?.length > 0)) {
        if ((shortDescFor5161Series !== singleGLPayload?.ShortText) || (longDescriptionFor5161Series !== singleGLPayload?.LongText)) {
          // Show confirmation dialog
          // const confirmProceed = window.confirm(`This GL has a counter GL number-${gLAccountFor5161Series} with Short Text- ${shortDescFor5161Series}  & Long Text- ${longDescriptionFor5161Series} .You have changed it. So do you want to proceed with your changes?`);
          console.log(gLAccountFor5161Series, "gLAccountFor5161Series====")
          setOpenMessageDialogFor5161ShortLongChange(true)
          setMessageDialogTitle("Short Text And Long Text Duplicate Check");
          setsuccessMsg(false);
          // setMessageDialogMessage(
          //   `This GL has a counter GL number-${gLAccountFor5161Series} with Short Text- ${shortDescFor5161Series}  & Long Text- ${longDescriptionFor5161Series} .You have changed it. So do you want to proceed with your changes?`
          // );
          setMessageDialogMessage(`
            This GL has a counter-GL Account Number ${gLAccountFor5161Series} with short text ({${shortDescFor5161Series}}) and long text (${longDescriptionFor5161Series}). The ${generalLedgerRowData?.newGLAccount}/${gLAccountFor5161Series} GL names should match. Do you wish to proceed?`)

          setHandleExtraText("Proceed")
          setHandleExtrabutton(true);
          // setMessageDialogSeverity("danger");
          setMessageDialogSeverity("warning");
          //setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpenShortlongChange();
          return;
        } else if ((shortDescFor5161Series == singleGLPayload?.ShortText) || (longDescriptionFor5161Series == singleGLPayload?.LongText)) {

          console.log(gLAccountFor5161Series, "gLAccountFor5161Series====")
          setOpenMessageDialogFor5161ShortLongChange(true)
          setMessageDialogTitle("Short Text And Long Text Duplicate Check");
          setsuccessMsg(false);
          // setMessageDialogMessage(
          //   `This GL has a counter GL number-${gLAccountFor5161Series} with Short Text- ${shortDescFor5161Series}  & Long Text- ${longDescriptionFor5161Series} Are Same.Do You have changed It?`
          // );
          setMessageDialogMessage(`
            This GL has a counter-GL Account Number ${gLAccountFor5161Series} with short text ({${shortDescFor5161Series}}) and long text (${longDescriptionFor5161Series}). The ${generalLedgerRowData?.newGLAccount}/${gLAccountFor5161Series} GL names should match. Do you wish to proceed?`)
          setHandleExtraText("Proceed")
          setHandleExtrabutton(true);
          // setMessageDialogSeverity("danger");
          setMessageDialogSeverity("warning");
          //setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpenShortlongChange();


        } else {
          setOpenMessageDialogFor5161ShortLongChange(true)
          proceedWithNextStep()
        }

      } else {
        const isValidation = handleCheckValidationError();
        const isValidationGI = handleCheckValidationErrorGI()
        if (isValidation && isValidationGI) {
          setActiveStep((prevActiveStep) => prevActiveStep + 1);
          //dispatch(clearGeneralLedger());
        } else {
          //alert("coming")
          //setActiveStep((prevActiveStep) => prevActiveStep + 1);
          handleSnackBarOpenValidation();
        }
      }
    }

  }

  const onEdit = () => {
    setIsEditMode(true);
    setIsDisplayMode(false);
  };
  const onEditFORFERC = (label, newValue) => {
    console.log("newlabel", newValue, label);
    dispatch(
      setPayloadForNewChange({
        keyname: label
          .replaceAll("(", "")
          .replaceAll(")", "")
          .replaceAll("/", "")
          .replaceAll("-", "")
          .replaceAll(".", "")
          .split(" ")
          .join(""),
        data: newValue,
      })
    );
  };
  const handleClosemodalData = (data) => {
    setisChangeLogopen(data);
  };

  const onGeneralLedgerSubmitChange = () => {
    handleRemarksDialogClose();
    handleGeneralLedgerSubmitChange();
  };
  const onGeneralLedgerSubmitExtend = () => {
    handleRemarksDialogClose();
    handleGeneralLedgerSubmitExtend();
  };
  const onGeneralLedgerSubmitCreate = () => {
    handleRemarksDialogClose();
    handleGeneralLedgerSubmitCreate();
  };

  const onGeneralLedgerReviewChange = () => {
    handleRemarksDialogClose();
    handleGeneralLedgerReviewChange();
  };
  const onGeneralLedgerReviewExtend = () => {
    handleRemarksDialogClose();
    handleGeneralLedgerReviewExtend();
  };
  const onGeneralLedgerReviewCreate = () => {
    handleRemarksDialogClose();
    handleGeneralLedgerReviewCreate();
  };

  const onCostCenterCorrectionChange = () => {
    handleCostCenterCorrectionChange();
  };
  const onCostCenterCorrectionCreate = () => {
    handleCostCenterCorrectionCreate();
  };

  const onGeneralLedgerApproveChange = () => {
    handleRemarksDialogClose();
    handleGeneralLedgerApproveChange();
  };
  const onGeneralLedgerApproveExtend = () => {
    handleRemarksDialogClose();
    handleGeneralLedgerApproveExtend();
  };
  // const onGeneralLedgerApproveExtend = () => {
  //   handleRemarksDialogClose();
  //   handleGeneralLedgerApproveChange();
  // };
  const onGeneralLedgerApproveCreate = () => {
    handleRemarksDialogClose();
    handleGeneralLedgerApproveCreate();
  };
  const onCostCenterRereview = () => {
    handleCostCenterRereview();
  };
  const handleMessageDialogClickOpen = () => {
    //setMessageDialogMessage('')
    setOpenMessageDialog(true);
  };
  const handleSnackBarOpen = () => {
    setopenSnackbar(true);
  };
  const openChangeLog = () => {
    setisChangeLogopen(true);
  };
  const validationRows = gLValidationErrors
    ?.filter((row) => row?.code === 400)
    ?.map((item, index) => {
      if (item.code === 400) {
        return {
          id: index,
          generalledger: item?.generalLedger,
          error: item?.status?.message,
        };
      }
    });
  const validationColumns = [
    {
      field: "generalledger",
      headerName: "General Ledger",
      editable: false,
      flex: 1,
      // width: 100,
    },
    {
      field: "error",
      headerName: "Error",
      editable: false,
      flex: 1,
      // width: 400,
    },
  ];

  const validateForGLNameDuplicateCheck = (button) => {
    setBlurLoading(true)
    let glNameFromGlViewData = "";
    generalLedgerViewData?.["Type/Description"]?.["Description"]?.map(
      (description_element) => {
        if (description_element?.fieldName === "Short Text") {
          glNameFromGlViewData = description_element?.value;
        }
      }
    );
    if (glNameFromGlViewData !== singleGLPayload?.ShortText) {
      var duplicateCheckName = {
        glName: singleGLPayload?.ShortText ? singleGLPayload?.ShortText?.toUpperCase() : "",
        coa: generalLedgerRowData?.chartOfAccount
          ? generalLedgerRowData?.chartOfAccount
          : iDs?.ChartOfAccount
            ? iDs?.ChartOfAccount
            : "",
      };
      const hDuplicateCheckNameSuccess = (data) => {
        //console.log(duplicateCheck.glName, "duplicateCheck.glName")
        // Handle success of duplicate check
        if (data.body.length === 0) {
          // No direct match, enable the "Submit for Review" button
          setBlurLoading(false);
          //setSubmitForReviewDisabled(false);

          validateForGLDescriptionDuplicateCheck(button)

          // doAjax(
          //   `/${destination_GeneralLedger}/alter/fetchGlDescNCoaDupliChk`,
          //   "post",
          //   hSuccessDesc,
          //   hErrorDesc,
          //   duplicateCheckDescription
          // );
        } else {
          // Handle direct match
          //alert("coming")
          setBlurLoading(false);
          setMessageDialogTitle("Duplicate Check");
          setsuccessMsg(false);
          setMessageDialogMessage(
            `Short Text is a duplicate of an existing GL Account Number – if you are OK with this please proceed, if not click cancel and update the description`
          );
          setHandleExtraText("Proceed")
          setHandleExtrabutton(true);
          setMessageDialogSeverity("warning");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpenshort();
          setSubmitForReviewDisabled(true);
        }
      };

      const hDuplicateCheckNameError = (error) => {
        // Handle error of duplicate check
        console.log(error);
      };

      doAjax(
        `/${destination_GeneralLedger}/alter/fetchGlNameNCoaDupliChk`,
        "post",
        hDuplicateCheckNameSuccess,
        hDuplicateCheckNameError,
        duplicateCheckName
      );

    } else {
      validateForGLDescriptionDuplicateCheck(button)
    }

  }

  const validateForGLDescriptionDuplicateCheck = (button) => {
    setBlurLoading(true)
    let glDescFromGlViewData = "";
    generalLedgerViewData?.["Type/Description"]?.["Description"]?.map(
      (description_element) => {
        if (description_element?.fieldName === "Long Text") {
          glDescFromGlViewData = description_element?.value;
        }
      }
    );
    if (glDescFromGlViewData !== singleGLPayload?.LongText) {
      var duplicateCheckDescription = {
        glDesc: singleGLPayload?.LongText ? singleGLPayload?.LongText?.toUpperCase() : "",
        coa: generalLedgerRowData?.chartOfAccount
          ? generalLedgerRowData?.chartOfAccount
          : iDs?.ChartOfAccount
            ? iDs?.ChartOfAccount
            : "",
      };



      const hSuccessDesc = (data) => {
        //console.log(duplicateCheck.glName, "duplicateCheck.glName")
        // Handle success of duplicate check
        if (data.body.length === 0) {
          // No direct match, enable the "Submit for Review" button
          setBlurLoading(false);
          setSubmitForReviewDisabled(false);
          validateForGeneralLedgerAfterAllCheck()

          // doAjax(
          //   `/${destination_GeneralLedger}/alter/validateSingleGeneralLedger`,
          //   "post",
          //   hSuccess,
          //   hError,
          //   payload
          // );
        } else {
          // Handle direct match
          setBlurLoading(false);
          setMessageDialogTitle("Duplicate Check");
          setsuccessMsg(false);
          setMessageDialogMessage(
            `Long Text is a duplicate of an existing GL Account Number – if you are OK with this please proceed, if not click cancel and update the description`
          );
          setHandleExtraText("Proceed")
          setHandleExtrabutton(true);
          // setMessageDialogSeverity("danger");
          setMessageDialogSeverity("warning");
          //setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpenValidate();
          setSubmitForReviewDisabled(true);
        }
      };
      const hErrorDesc = (errordesc) => {
        console.log(errordesc);
      };


      doAjax(
        `/${destination_GeneralLedger}/alter/fetchGlDescNCoaDupliChk`,
        "post",
        hSuccessDesc,
        hErrorDesc,
        duplicateCheckDescription
      );
    } else {
      validateForGeneralLedgerAfterAllCheck()
    }
  }


  const validateForGeneralLedgerAfterAllCheck = () => {

    payload.Testrun = true
    setBlurLoading(true)
    const hSuccess = (data) => {
      // setIsLoading();
      setBlurLoading(false);
      if (data.statusCode === 200) {
        setMessageDialogMessage(
          "Request has been Validated Successfully"
        );

        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setBlurLoading(false);
        setsuccessMsg(true);
        handleSnackBarOpenValidate()
        //handleSnackBarOpen();
        //setValidateFlag(true);
        const updatedButtons = filteredButtons.map((button) => {
          if (
            button?.MDG_DYN_BTN_BUTTON_NAME === "Approve" ||
            button?.MDG_DYN_BTN_BUTTON_NAME === "SAP Syndication" ||
            button?.MDG_DYN_BTN_BUTTON_NAME === "Submit"
          ) {
            return { ...button, MDG_DYN_BTN_BUTTON_STATUS: "ENABLED" };
          }
          return button;
        });
        setFilteredButtons(updatedButtons);
        setSubmitForReviewDisabled(false);
      } else {
        if (data?.statusCode === 400) {
          // Filter the body array
          const filteredData = data?.body?.filter(item => item.code == 400);

          // Map the data to the format required for the DataGrid
          const tableData = filteredData.map((item, index) => ({
            id: index + 1,  // Or use just `index` if you prefer starting from 0
            generalLedger: item.generalLedger,
            compCode: item.compCode,
            message: item.status.message.join(", ")
          }));
          console.log(tableData, "tableData===")
          setSubmitForReviewDisabled(true);
          setGlCreateErrorData(tableData)
          setOpenGlErrorTable(true)

          // Now, you can use `tableData` to populate your DataGrid
          //console.log(tableData);
        } else if (data?.statusCode === 500) {
          setSubmitForReviewDisabled(true);
          setMessageDialogMessage("");
          setBlurLoading(false);
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          // setMessageDialogMessage(
          //   `${data?.body?.message[0] ? data?.body?.message[0] : data?.body?.value
          //   }`
          // );
          console.log(data?.body, data?.body?.message, " =======================databody")
          setMessageDialogMessage(`${data?.body}`);
          setHandleExtrabutton(false);
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          setSubmitForReviewDisabled(true);
        } else {
          setSubmitForReviewDisabled(true);
          setMessageDialogMessage("");
          setBlurLoading(false);
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          // setMessageDialogMessage(
          //   `${data?.body?.message[0] ? data?.body?.message[0] : data?.body?.value
          //   }`
          // );
          console.log(data?.body[0], data?.body[0]?.status?.message[0], " =======================databody")
          setMessageDialogMessage(
            `${data?.body[0]?.status?.message[0] ? data?.body[0]?.status?.message[0] : data?.body[0]?.status?.value
            }`
          );
          setHandleExtrabutton(false);
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          setSubmitForReviewDisabled(true);
        }
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
      // setMessageDialogSeverity("error");
      // // setMessageDialogMessage(
      // //   "Failed Submitting General Ledger"
      // // );
      // setMessageDialogMessage(`Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>.`);
      // //setsuccessMsg(true);
      // setBlurLoading(false);
      // handleSnackBarOpen();
    };
    doAjax(
      `/${destination_GeneralLedger}/alter/validateSingleGeneralLedger`,
      "post",
      hSuccess,
      hError,
      payload
    );
  }




  const onValidateGeneralLedgerApprover = (button) => {
    setSaveAsDraftState(false)

    let glNameFromGlViewData = "";
    generalLedgerViewData?.["Type/Description"]?.["Description"]?.map(
      (description_element) => {
        if (description_element?.fieldName === "Short Text") {
          glNameFromGlViewData = description_element?.value;
        }
      }
    );


    let glDescFromGlViewData = "";
    generalLedgerViewData?.["Type/Description"]?.["Description"]?.map(
      (description_element) => {
        if (description_element?.fieldName === "Long Text") {
          glDescFromGlViewData = description_element?.value;
        }
      }
    );


    if (glNameFromGlViewData !== singleGLPayload?.ShortText) {

      validateForGLNameDuplicateCheck(button)

    } else if (glDescFromGlViewData !== singleGLPayload?.LongText) {
      validateForGLDescriptionDuplicateCheck(button)

    } else {

      validateForGeneralLedgerAfterAllCheck()
    };
  }




  const functions_ExportAsExcelErrorValidate = {
    convertJsonToExcel: () => {
      let excelColumns = [];
      glColumnError.forEach((item) => {
        if (item.headerName.toLowerCase() !== "action" && !item.hide) {
          excelColumns.push({ header: item.headerName, key: item.field });
        }
      });
      saveExcel({
        fileName: `Error log Data-${moment(presentDate).format("DD-MMM-YYYY")}`,
        columns: excelColumns,
        rows: glCreateErrorData,
      })
    },
    button: () => {
      return (
        <Button
          sx={{
            textTransform: "capitalize",
            position: "absolute",
            right: 0,
            top: 0,
          }}
          onClick={() => functions_ExportAsExcelErrorValidate.convertJsonToExcel()}
        >
          Download
        </Button>
      );
    },
  };


  const handleReject = (button) => {
    setBlurLoading(true);
    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        console.log("success");
        setValidateFlag(true)
        setMessageDialogMessage(
          `${button.MDG_DYN_BTN_SNACKBAR_MSG}`
        );
        setMessageDialogSeverity("success");
        setsuccessMsg(true);
        setBlurLoading(false);
        handleSnackBarOpen();
        setTimeout(() => {
          navigate("/requestBench");
        }, 3000);
        // setIsLoading(false);
      } else {
        setDialogTitle("Error")
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Rejecting Request");
        setHandleExtrabutton(false);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        setBlurLoading(false);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/alter/generalLedgerRejected`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };



  const validateForGLNameDuplicateCheckFromSaveAsDraft = () => {
    setBlurLoading(true)

    var duplicateCheckName = {
      glName: singleGLPayload?.ShortText ? singleGLPayload?.ShortText?.toUpperCase() : "",
      coa: generalLedgerRowData?.chartOfAccount
        ? generalLedgerRowData?.chartOfAccount
        : iDs?.ChartOfAccount
          ? iDs?.ChartOfAccount
          : "",
    };
    const hDuplicateCheckNameSuccess = (data) => {
      console.log("1")
      //console.log(duplicateCheck.glName, "duplicateCheck.glName")
      // Handle success of duplicate check
      if (data.body.length === 0) {
        console.log("2")
        // No direct match, enable the "Submit for Review" button
        setBlurLoading(false);
        //setSubmitForReviewDisabled(false);

        validateForGLDescriptionDuplicateCheckFromSaveAsDraft()


      } else {
        // Handle direct match
        //alert("coming")
        setBlurLoading(false);
        setMessageDialogTitle("Duplicate Check");
        setsuccessMsg(false);
        setMessageDialogMessage(
          `Short Text is a duplicate of an existing GL Account Number – if you are OK with this please proceed, if not click cancel and update the description`
        );
        setHandleExtraText("Proceed")
        setHandleExtrabutton(true);
        setMessageDialogSeverity("warning");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpenshort();
        setSubmitForReviewDisabled(true);
      }
    };

    const hDuplicateCheckNameError = (error) => {
      // Handle error of duplicate check
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger}/alter/fetchGlNameNCoaDupliChk`,
      "post",
      hDuplicateCheckNameSuccess,
      hDuplicateCheckNameError,
      duplicateCheckName
    );

  }



  const validateForGLDescriptionDuplicateCheckFromSaveAsDraft = () => {
    setBlurLoading(true)

    var duplicateCheckDescription = {
      glDesc: singleGLPayload?.LongText ? singleGLPayload?.LongText?.toUpperCase() : "",
      coa: generalLedgerRowData?.chartOfAccount
        ? generalLedgerRowData?.chartOfAccount
        : iDs?.ChartOfAccount
          ? iDs?.ChartOfAccount
          : "",
    };

    // console.log("3")

    const hSuccessDesc = (data) => {
      //console.log(duplicateCheck.glName, "duplicateCheck.glName")
      // Handle success of duplicate check
      if (
        data.body.length === 0
      ) {
        // No direct match, enable the "Submit for Review" button
        setBlurLoading(false);
        setSubmitForReviewDisabled(false);
        validateForGeneralLedgerAfterAllCheckFromSaveAsDraft()
        console.log("4come")
      } else {
        // Handle direct match
        setBlurLoading(false);
        setMessageDialogTitle("Duplicate Check");
        setsuccessMsg(false);
        setMessageDialogMessage(
          `Long Text is a duplicate of an existing GL Account Number – if you are OK with this please proceed, if not click cancel and update the description`
        );
        setHandleExtraText("Proceed")
        setHandleExtrabutton(true);
        // setMessageDialogSeverity("danger");
        setMessageDialogSeverity("warning");
        //setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpenValidate();
        setSubmitForReviewDisabled(true);
      }
    };
    const hErrorDesc = (errordesc) => {
      console.log(errordesc);
    };


    doAjax(
      `/${destination_GeneralLedger}/alter/fetchGlDescNCoaDupliChk`,
      "post",
      hSuccessDesc,
      hErrorDesc,
      duplicateCheckDescription
    );

  }



  const validateForGeneralLedgerAfterAllCheckFromSaveAsDraft = () => {
    setBlurLoading(true)
    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        setBlurLoading(false)
        handleDraftDialogClose();
        setValidateFlag(true)
        setMessageDialogMessage(
          `Request has been Saved as Draft with ID GLFS0NEW${data.body}`
        );
        setMessageDialogSeverity("success");
        setsuccessMsg(true);
        handleSnackBarOpen();
        distinctArtifactId.map((artifactId) => {
          console.log(artifactId, "artifactId=====");
          const secondApiPayload = {
            artifactId: artifactId,
            createdBy: userData?.emailId,
            artifactType: "GeneralLedger",
            requestId: `GLFS0NEW${data?.body}`,
          };
          const secondApiSuccess = (secondApiData) => {
            console.log("Second API success", secondApiData);
            // Handle success for the second API if needed
          };

          const secondApiError = (secondApiError) => {
            console.error("Second API error", secondApiError);
            // Handle error for the second API if needed
          };
          // {requestId&&
          doAjax(
            `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
            "post",
            secondApiSuccess,
            secondApiError,
            secondApiPayload
          );
        });
        setTimeout(() => {
          navigate("/masterDataCockpitNew/generalLedger");
        }, 2000);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting Request"
        );
        setHandleExtrabutton(false);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setBlurLoading(false);
        handleMessageDialogClickOpen();
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/alter/generalLedgerAsDraft`,
      "post",
      hSuccess,
      hError,
      payload
    );
  }


  // const onValidateGeneralLedger = () => {
  //   setBlurLoading(true);
  //   // const duplicateCheck = `${singleGLPayload?.ShortText}$$${
  //   //   generalLedgerRowData?.compCode
  //   //     ? generalLedgerRowData?.compCode
  //   //     : iDs?.CompCode
  //   //     ? iDs?.CompCode
  //   //     : ""
  //   // }`;

  //   var duplicateCheck = {
  //     glName: singleGLPayload?.ShortText ? singleGLPayload?.ShortText?.toUpperCase() : "",
  //     compCode: generalLedgerRowData?.compCode
  //       ? generalLedgerRowData?.compCode
  //       : iDs?.CompCode
  //         ? iDs?.CompCode
  //         : "",
  //   };

  //   const hSuccess = (data) => {
  //     // setIsLoading();
  //     if (data.statusCode === 200) {
  //       setMessageDialogTitle("Create");
  //       console.log("success");
  //       setMessageDialogTitle("Create");
  //       setMessageDialogMessage(
  //         `All Data has been Validated. General Ledger can be Send for Review`
  //       );
  //       setSubmitForReviewDisabled(false);
  //       setHandleExtrabutton(false);
  //       setMessageDialogSeverity("success");
  //       setMessageDialogOK(false);
  //       setsuccessMsg(true);
  //       handleSnackBarOpen();
  //       setMessageDialogExtra(true);
  //       setValidateFlag(true);
  //       setBlurLoading(false);
  //     } else {
  //       setBlurLoading(false);
  //       setMessageDialogTitle("Error");
  //       setsuccessMsg(false);
  //       setMessageDialogMessage(
  //         `${data?.body?.message[0] ? data?.body?.message[0] : data?.body?.value
  //         }`
  //       );
  //       setHandleExtrabutton(false);
  //       setMessageDialogSeverity("danger");
  //       setMessageDialogOK(false);
  //       setMessageDialogExtra(true);
  //       handleMessageDialogClickOpen();
  //       setSubmitForReviewDisabled(true);
  //     }
  //     handleClose();
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   const hDuplicateCheckSuccess = (data) => {
  //     console.log(duplicateCheck.glName, "duplicateCheck.glName")
  //     // Handle success of duplicate check
  //     if (
  //       data.body.length === 0 ||
  //       !data.body.some((item) => item.toUpperCase() === duplicateCheck.glName)
  //     ) {
  //       // No direct match, enable the "Submit for Review" button
  //       setBlurLoading(false);
  //       setSubmitForReviewDisabled(false);

  //       doAjax(
  //         `/${destination_GeneralLedger}/alter/validateSingleGeneralLedger`,
  //         "post",
  //         hSuccess,
  //         hError,
  //         payload
  //       );
  //     } else {
  //       // Handle direct match
  //       setBlurLoading(false);
  //       setMessageDialogTitle("Duplicate Check");
  //       setsuccessMsg(false);
  //       setMessageDialogMessage(
  //         `There is a direct match for the Short Text. Please change the Short Text.`
  //       );
  //       setHandleExtrabutton(false);
  //       setMessageDialogSeverity("danger");
  //       setMessageDialogOK(false);
  //       setMessageDialogExtra(true);
  //       handleMessageDialogClickOpen();
  //       setSubmitForReviewDisabled(true);
  //     }
  //   };

  //   const hDuplicateCheckError = (error) => {
  //     // Handle error of duplicate check
  //     console.log(error);
  //   };

  //   doAjax(
  //     `/${destination_GeneralLedger}/alter/fetchGlNameNCompCodeDupliChk`,
  //     "post",
  //     hDuplicateCheckSuccess,
  //     hDuplicateCheckError,
  //     duplicateCheck
  //   );
  // };

  const handleDialogClose = () => {
    setDialogOpen(false);
  };
  // const onValidateGeneralLedger = () => {
  //   setBlurLoading(true);
  //   var duplicateCheck = {
  //     glName: singleGLPayload?.ShortText ? singleGLPayload?.ShortText?.toUpperCase() : "",
  //     coa: generalLedgerRowData?.chartOfAccounts
  //       ? generalLedgerRowData?.chartOfAccounts?.newChartOfAccount?.code
  //       : iDs?.chartOfAccounts
  //         ? iDs?.CompCode
  //         : "",
  //   };
  //   var duplicateCheckDescription = {
  //     glDesc: singleGLPayload?.LongText ? singleGLPayload?.LongText?.toUpperCase() : "",
  //     coa: generalLedgerRowData?.chartOfAccounts
  //       ? generalLedgerRowData?.chartOfAccounts?.newChartOfAccount?.code
  //       : iDs?.chartOfAccounts
  //         ? iDs?.CompCode
  //         : "",
  //   };

  //   const hSuccess = (data) => {
  //     // setIsLoading();
  //     if (data.statusCode === 200) {
  //       setMessageDialogTitle("Create");
  //       console.log("success");
  //       setMessageDialogTitle("Create");
  //       setMessageDialogMessage(
  //         `All Data has been Validated. General Ledger can be Send for Review`
  //       );
  //       setSubmitForReviewDisabled(false);
  //       setHandleExtrabutton(false);
  //       setMessageDialogSeverity("success");
  //       setMessageDialogOK(false);
  //       setsuccessMsg(true);
  //       handleSnackBarOpen();
  //       setMessageDialogExtra(true);
  //       setValidateFlag(true);
  //       setBlurLoading(false);
  //     } else {
  //       setBlurLoading(false);
  //       setMessageDialogTitle("Error");
  //       setsuccessMsg(false);
  //       setMessageDialogMessage(
  //         `${data?.body?.message[0] ? data?.body?.message[0] : data?.body?.value
  //         }`
  //       );
  //       setHandleExtrabutton(false);
  //       setMessageDialogSeverity("danger");
  //       setMessageDialogOK(false);
  //       setMessageDialogExtra(true);
  //       handleMessageDialogClickOpen();
  //       setSubmitForReviewDisabled(true);
  //     }
  //     handleClose();
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };

  //   const hSuccessDesc = (data) => {
  //     //console.log(duplicateCheck.glName, "duplicateCheck.glName")
  //     // Handle success of duplicate check
  //     if (
  //       data.body.length === 0 ||
  //       !data.body.some((item) => item.toUpperCase() === duplicateCheck.glDesc)
  //     ) {
  //       // No direct match, enable the "Submit for Review" button
  //       setBlurLoading(false);
  //       setSubmitForReviewDisabled(false);

  //       doAjax(
  //         `/${destination_GeneralLedger}/alter/validateSingleGeneralLedger`,
  //         "post",
  //         hSuccess,
  //         hError,
  //         payload
  //       );
  //     } else {
  //       // Handle direct match
  //       setBlurLoading(false);
  //       setMessageDialogTitle("Duplicate Check");
  //       setsuccessMsg(false);
  //       setMessageDialogMessage(
  //         `There is a direct match for the Short Text. Please change the Short Text.`
  //       );
  //       setHandleExtrabutton(false);
  //       setMessageDialogSeverity("danger");
  //       setMessageDialogOK(false);
  //       setMessageDialogExtra(true);
  //       handleMessageDialogClickOpen();
  //       setSubmitForReviewDisabled(true);
  //     }
  //   };
  //   const hErrorDesc = (errordesc) => {
  //     console.log(errordesc);
  //   };
  //   const hDuplicateCheckSuccess = (data) => {
  //     //console.log(duplicateCheck.glName, "duplicateCheck.glName")
  //     // Handle success of duplicate check
  //     if (
  //       data.body.length === 0 ||
  //       !data.body.some((item) => item.toUpperCase() === duplicateCheck.glName)
  //     ) {
  //       // No direct match, enable the "Submit for Review" button
  //       //setBlurLoading(false);
  //       //setSubmitForReviewDisabled(false);

  //       doAjax(
  //         `/${destination_GeneralLedger}/alter/fetchGlDescNCoaDupliChk`,
  //         "post",
  //         hSuccessDesc,
  //         hErrorDesc,
  //         duplicateCheckDescription
  //       );
  //     } else {
  //       // Handle direct match
  //       setBlurLoading(false);
  //       setMessageDialogTitle("Duplicate Check");
  //       setsuccessMsg(false);
  //       setMessageDialogMessage(
  //         `There is a direct match for the Short Text. Please change the Short Text.`
  //       );
  //       setHandleExtrabutton(false);
  //       setMessageDialogSeverity("danger");
  //       setMessageDialogOK(false);
  //       setMessageDialogExtra(true);
  //       handleMessageDialogClickOpen();
  //       setSubmitForReviewDisabled(true);
  //     }
  //   };

  //   const hDuplicateCheckError = (error) => {
  //     // Handle error of duplicate check
  //     console.log(error);
  //   };

  //   doAjax(
  //     `/${destination_GeneralLedger}/alter/fetchGlNameNCoaDupliChk`,
  //     "post",
  //     hDuplicateCheckSuccess,
  //     hDuplicateCheckError,
  //     duplicateCheck
  //   );
  // };


  const onValidateGeneralLedgerExtend = () => {
    setBlurLoading(true);
    // const duplicateCheck = `${singleGLPayload?.ShortText}$$${
    //   generalLedgerRowData?.compCode
    //     ? generalLedgerRowData?.compCode
    //     : iDs?.CompCode
    //     ? iDs?.CompCode
    //     : ""
    // }`;

    // var duplicateCheck = {
    //   glName: singleGLPayload?.ShortText ? singleGLPayload?.ShortText?.toUpperCase() : "",
    //   compCode: generalLedgerRowData?.compCode
    //     ? generalLedgerRowData?.compCode
    //     : iDs?.CompCode
    //       ? iDs?.CompCode
    //       : "",
    // };

    const hSuccess = (data) => {
      // setIsLoading();
      if (data.statusCode === 201) {
        setMessageDialogTitle("Create");
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Request has been Validated Successfully.`
        );
        setSubmitForReviewDisabled(false);
        setHandleExtrabutton(false);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setValidateFlag(true);
        setBlurLoading(false);
      } else {
        setBlurLoading(false);
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        if (data?.body?.message?.length != 0) {
          const content = (
            <Typography component="div">
              <ul>
                {data?.body?.message.map((item, index) => (
                  <li key={index}>
                    {item}
                  </li>
                ))}
              </ul>
            </Typography>
          );
          setMessageDialogMessage(content);
        } else {
          const content = data.body.value
          setMessageDialogMessage(content);
        }
        setHandleExtrabutton(false);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setSubmitForReviewDisabled(true);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    // const hDuplicateCheckSuccess = (data) => {
    //   console.log(duplicateCheck.glName, "duplicateCheck.glName")
    //   // Handle success of duplicate check
    //   if (
    //     data.body.length === 0 ||
    //     !data.body.some((item) => item.toUpperCase() === duplicateCheck.glName)
    //   ) {
    //     // No direct match, enable the "Submit for Review" button
    //     setBlurLoading(false);
    //     setSubmitForReviewDisabled(false);

    doAjax(
      `/${destination_GeneralLedger}/alter/validateGeneralLedgerExtend`,
      "post",
      hSuccess,
      hError,
      payload
    );
    // } else {
    //   // Handle direct match
    //   setBlurLoading(false);
    //   setMessageDialogTitle("Duplicate Check");
    //   setsuccessMsg(false);
    //   setMessageDialogMessage(
    //     `There is a direct match for the Short Text. Please change the Short Text.`
    //   );
    //   setHandleExtrabutton(false);
    //   setMessageDialogSeverity("danger");
    //   setMessageDialogOK(false);
    //   setMessageDialogExtra(true);
    //   handleMessageDialogClickOpen();
    //   setSubmitForReviewDisabled(true);
    // }
    //};

    // const hDuplicateCheckError = (error) => {
    //   // Handle error of duplicate check
    //   console.log(error);
    // };

    // doAjax(
    //   `/${destination_GeneralLedger}/alter/fetchGlNameNCompCodeDupliChk`,
    //   "post",
    //   hDuplicateCheckSuccess,
    //   hDuplicateCheckError,
    //   duplicateCheck
    // );
  };
  // const onValidateGeneralLedger = () => {
  //   setBlurLoading(true);
  //   var duplicateCheck = {
  //     glName: singleGLPayload?.ShortText
  //       ? singleGLPayload?.ShortText?.toUpperCase()
  //       : "",
  //     compCode: generalLedgerRowData?.compCode
  //       ? generalLedgerRowData?.compCode
  //       : iDs?.CompCode
  //       ? iDs?.CompCode
  //       : "",
  //   };
  //   let glNameFromGlViewData = "";
  //   generalLedgerViewData?.["Type/Description"]?.["Description"]?.map(
  //     (description_element) => {
  //       if (description_element?.fieldName === "Short Text") {
  //         glNameFromGlViewData = description_element?.value;
  //       }
  //     }
  //   );
  //   console.log(
  //     duplicateCheck?.glName?.toUpperCase(),
  //     glNameFromGlViewData?.toUpperCase(),
  //     "checkingElement"
  //   );
  //   const hSuccess = (data) => {
  //     // setIsLoading();
  //     if (data.statusCode === 201) {
  //       setMessageDialogTitle("Create");
  //       setMessageDialogTitle("Create");
  //       setMessageDialogMessage(
  //         `All Data has been Validated. General Ledger can be Send for Review`
  //       );
  //       setSubmitForReviewDisabled(false);
  //       setHandleExtrabutton(false);
  //       setMessageDialogSeverity("success");
  //       setMessageDialogOK(false);
  //       setsuccessMsg(true);
  //       handleSnackBarOpen();
  //       setMessageDialogExtra(true);
  //       setValidateFlag(true);
  //       if (duplicateCheck.compCode !== "" || duplicateCheck.glName !== "") {
  //         setSubmitForReviewDisabled(false);
  //         if (
  //           duplicateCheck?.glName?.toUpperCase() ===
  //             glNameFromGlViewData?.toUpperCase() &&
  //           pageName === "Change"
  //         ) {
  //           // new added for skip duplicate check if not any changes
  //           setBlurLoading(false);
  //         } else {
  //           doAjax(
  //             `/${destination_GeneralLedger}/alter/fetchGlNameNCompCodeDupliChk`,
  //             "post",
  //             hDuplicateCheckSuccess,
  //             hDuplicateCheckError,
  //             duplicateCheck
  //           );
  //         }
  //       }
  //       setBlurLoading(false);
  //     } else {
  //       setBlurLoading(false);
  //       setMessageDialogTitle("Error");
  //       setsuccessMsg(false);
  //       setMessageDialogMessage(
  //         `${
  //           data?.body?.message[0] ? data?.body?.message[0] : data?.body?.value
  //         }`
  //       );
  //       setHandleExtrabutton(false);
  //       setMessageDialogSeverity("danger");
  //       setMessageDialogOK(false);
  //       setMessageDialogExtra(true);
  //       handleMessageDialogClickOpen();
  //       setSubmitForReviewDisabled(true);
  //     }
  //     handleClose();
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   const hDuplicateCheckSuccess = (data) => {
  //     // Handle success of duplicate check
  //     if (
  //       data.body.length === 0 ||
  //       !data.body.some((item) => item.toUpperCase() === duplicateCheck.glName)
  //     ) {
  //       // No direct match, enable the "Submit for Review" button
  //       setBlurLoading(false);
  //       setSubmitForReviewDisabled(false);
  //       setTestrunStatus(false);
  //     } else {
  //       // Handle direct match
  //       setBlurLoading(false);
  //       setMessageDialogTitle("Duplicate Check");
  //       setsuccessMsg(false);
  //       setMessageDialogMessage(
  //         `There is a direct match for the Short Text. Please change the Short Text.`
  //       );
  //       setHandleExtrabutton(false);
  //       setMessageDialogSeverity("danger");
  //       setMessageDialogOK(false);
  //       setMessageDialogExtra(true);
  //       handleMessageDialogClickOpen();
  //       setSubmitForReviewDisabled(true);
  //     }
  //   };

  //   const hDuplicateCheckError = (error) => {
  //     // Handle error of duplicate check
  //     console.log(error);
  //   };

  //   doAjax(
  //     `/${destination_GeneralLedger}/alter/validateSingleGeneralLedger`,
  //     "post",
  //     hSuccess,
  //     hError,
  //     payload
  //   );
  // };
  const openTableForCompanyCode = () => {
    setOpenCompanyCode(true)
  }

  const handleCloseCompanyCode = () => {

    setOpenCompanyCode(false)
  }

  const onGeneralLedgerCorrection = () => {
    //alert("coming")
    if (
      // userData?.role === "MDM Steward" &&
      (generalLedgerRowData?.requestType === "Create" ||
        taskRowDetails?.requestType === "Create")
    ) {
      handleCorrectionMDMCreate();
    } else if (
      // userData?.role === "MDM Steward" &&
      (generalLedgerRowData?.requestType === "Change" ||
        taskRowDetails?.requestType === "Change")
    ) {
      handleCorrectionMDMChange();
    }
    else if (
      // userData?.role === "MDM Steward" &&
      (generalLedgerRowData?.requestType === "Extend" ||
        taskRowDetails?.requestType === "Extend")
    ) {
      handleCorrectionMDMExtend();
    } else if (
      // userData?.role === "Approver" &&
      (generalLedgerRowData?.requestType === "Create" ||
        taskRowDetails?.requestType === "Create")
    ) {
      handleCorrectionApproverCreate();
    } else if (
      // userData?.role === "Approver" &&
      (generalLedgerRowData?.requestType === "Change" ||
        taskRowDetails?.requestType === "Change")
    ) {
      handleCorrectionApproverChange();
    }
    else if (
      // userData?.role === "Approver" &&
      (generalLedgerRowData?.requestType === "Extend" ||
        taskRowDetails?.requestType === "Extend")
    ) {
      handleCorrectionApproverExtend();
    }
  };

  const onGeneralLedgerSubmitRemarks = () => { //chiranjit
    // if (
    //   // userData?.role === "Finance" &&
    //   (generalLedgerRowData?.requestType === "Create" ||
    //     taskRowDetails?.requestType === "Create") &&
    //   isEditMode
    // ) {
    //   onGeneralLedgerReviewCreate();
    // } else if (
    //   // userData?.role === "MDM Steward" &&
    //   (generalLedgerRowData?.requestType === "Create" ||
    //     taskRowDetails?.requestType === "Create") &&
    //   !isEditMode
    // ) {
    //   onGeneralLedgerSubmitCreate();
    // } else if (
    //   // userData?.role === "Approver" &&
    //   (generalLedgerRowData?.requestType === "Create" ||
    //     taskRowDetails?.requestType === "Create") &&
    //   !isEditMode
    // ) {
    //   onGeneralLedgerApproveCreate();
    // } else if (
    //   // userData?.role === "Finance" &&
    //   !generalLedgerRowData?.requestType && //for change from master table
    //   isEditMode
    // ) {
    //   onGeneralLedgerReviewChange();
    // } else if (
    //   // userData?.role === "Finance" &&
    //   (generalLedgerRowData?.requestType === "Change" ||
    //     taskRowDetails?.requestType === "Change") &&
    //   isEditMode
    // ) {
    //   onGeneralLedgerReviewChange();
    // } else if (
    //   // userData?.role === "MDM Steward" &&
    //   (generalLedgerRowData?.requestType === "Change" ||
    //     taskRowDetails?.requestType === "Change") &&
    //   !isEditMode
    // ) {
    //   onGeneralLedgerSubmitChange();
    // } else if (
    //   // userData?.role === "Approver" &&
    //   (generalLedgerRowData?.requestType === "Change" ||
    //     taskRowDetails?.requestType === "Change") &&
    //   !isEditMode
    // ) {
    //   onGeneralLedgerApproveChange();
    // } else if (
    //   // userData?.role === "Finance" &&
    //   !generalLedgerRowData?.requestType && //for change from master table
    //   isEditMode
    // ) {
    //   onGeneralLedgerReviewExtend();
    // } else if (
    //   // userData?.role === "Finance" &&
    //   (generalLedgerRowData?.requestType === "Extend" ||
    //     generalLedgerRowData?.requestType === "Display For Extend" ||
    //     taskRowDetails?.requestType === "Extend") &&
    //   isEditMode
    // ) {
    //   onGeneralLedgerReviewExtend();
    // } else if (
    //   // userData?.role === "MDM Steward" &&
    //   (generalLedgerRowData?.requestType === "Extend" ||
    //     taskRowDetails?.requestType === "Extend") &&
    //   !isEditMode
    // ) {
    //   onGeneralLedgerSubmitExtend();
    // } else if (
    //   // userData?.role === "Approver" &&
    //   (generalLedgerRowData?.requestType === "Extend" ||
    //     taskRowDetails?.requestType === "Extend") &&
    //   !isEditMode
    // ) {
    //   onGeneralLedgerApproveExtend();
    // }
    onGeneralLedgerReviewCreate()
  };
  const handleCloseGlErrorTable = () => {
    setOpenGlErrorTable(false)
    setGlCreateErrorData([])
  }

  const handleCorrectionApproverCreate = (button) => {
    setBlurLoading(true)
    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogMessage(
          `${button.MDG_DYN_BTN_SNACKBAR_MSG} with ID GLFS0NEW${data.body} `
        );
        setMessageDialogSeverity("success");
        setsuccessMsg(true);
        setBlurLoading(false);
        distinctArtifactId.map((artifactId) => {
          console.log(artifactId, "artifactId=====");
          const secondApiPayload = {
            artifactId: artifactId,
            createdBy: userData?.emailId,
            artifactType: "GeneralLedger",
            requestId: `GLFS0NEW${data?.body}`,
          };
          const secondApiSuccess = (secondApiData) => {
            console.log("Second API success", secondApiData);
            // Handle success for the second API if needed
          };

          const secondApiError = (secondApiError) => {
            console.error("Second API error", secondApiError);
            // Handle error for the second API if needed
          };
          // {requestId&&
          doAjax(
            `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
            "post",
            secondApiSuccess,
            secondApiError,
            secondApiPayload
          );
        });
        handleSnackBarOpen();
      } else {
        setDialogTitle("Error")
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Request");
        setHandleExtrabutton(false);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        setBlurLoading(false);
        handleMessageDialogClickOpen();
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    console.log("remarkssssssssss", remarks);
    doAjax(
      `/${destination_GeneralLedger}/alter/generalLedgerSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCorrectionMDMChange = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `General Ledger Submitted for Correction with ID GLSC${data.body}`
        );

        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting General Ledger for Correction"
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    console.log("hsdfjgdh", payload);
    doAjax(
      `/${destination_GeneralLedger}/alter/changeGeneralLedgerSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCorrectionMDMExtend = () => {
    //alert("coming")
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Extend");
        setMessageDialogMessage(
          `General Ledger Submitted for Correction with ID GLSE${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting General Ledger for Correction"
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    console.log("hsdfjgdh", payload);
    doAjax(
      `/${destination_GeneralLedger}/alter/extendGeneralLedgerSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCorrectionMDMCreate = (button) => {
    setBlurLoading(true)
    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogMessage(
          `${button.MDG_DYN_BTN_SNACKBAR_MSG} with ID GLFS0NEW${data.body} `
        );
        setMessageDialogSeverity("success");
        setsuccessMsg(true);
        setValidateFlag(false)
        //setBlurLoading(false);
        distinctArtifactId.map((artifactId) => {
          console.log(artifactId, "artifactId=====");
          const secondApiPayload = {
            artifactId: artifactId,
            createdBy: userData?.emailId,
            artifactType: "GeneralLedger",
            requestId: `GLFS0NEW${data?.body}`,
          };
          const secondApiSuccess = (secondApiData) => {
            console.log("Second API success", secondApiData);
            // Handle success for the second API if needed
          };

          const secondApiError = (secondApiError) => {
            console.error("Second API error", secondApiError);
            // Handle error for the second API if needed
          };
          // {requestId&&
          doAjax(
            `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
            "post",
            secondApiSuccess,
            secondApiError,
            secondApiPayload
          );
        });
        setBlurLoading(false)

        handleSnackBarOpen();
      } else {
        setDialogTitle("Error")
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Request");
        setHandleExtrabutton(false);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        setBlurLoading(false);
        handleMessageDialogClickOpen();
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/alter/generalLedgerSendForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCorrectionApproverChange = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `General Ledger Submitted for Correction with ID NCR${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting General Ledger for Correction"
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    console.log("remarksssaaaa", remarks);
    doAjax(
      `/${destination_GeneralLedger}/alter/changeGeneralLedgerSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCorrectionApproverExtend = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Extend");
        setMessageDialogMessage(
          `General Ledger Submitted for Correction with ID GLSE${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting General Ledger for Correction"
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    console.log("remarksssaaaa", remarks);
    doAjax(
      `/${destination_GeneralLedger}/alter/extendGeneralLedgerSendForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const attachmentColumns = [
    {
      field: "id",
      headerName: "Document ID",
      flex: 1,
      hide: true,
    },
    // {
    //   field: "docType",
    //   headerName: "Document Type",
    //   flex: 1,
    // },
    {
      field: "attachmentType",
      headerName: "Attachment Type",
      sortable: false,
      flex: 1,
    },
    {
      field: "docName",
      headerName: "Document Name",
      flex: 1,
    },
    {
      field: "uploadedOn",
      headerName: "Uploaded On",
      flex: 1,
      align: "center",
      headerAlign: "center",
    },
    ,
    {
      field: "uploadedBy",
      headerName: "Uploaded By",
      sortable: false,
      flex: 1,
    },

    {
      field: "action",
      headerName: "Action",
      sortable: false,
      filterable: false,
      align: "center",
      headerAlign: "center",
      flex: 1,
      renderCell: (cellValues) => {
        return (
          <>
            {/* <MatView index={cellValues.row.id} name={cellValues.row.docName} /> */}
            <MatDownload
              index={cellValues.row.id}
              name={cellValues.row.docName}
            />
          </>
        );
      },
    },
  ];
  const getAttachments = () => {
    let requestId = taskRowDetails?.requestId
      ? taskRowDetails?.requestId
      : generalLedgerRowData?.requestId;
    let hSuccess = (data) => {
      var attachmentRows = [];
      data.documentDetailDtoList.forEach((doc) => {
        var tempRow = {
          id: doc.documentId,
          docType: doc.fileType,
          docName: doc.fileName,
          //uploadedOn: moment(doc.docCreationDate).format(appSettings.date),
          uploadedOn: moment(doc.docCreationDate).format(appSettings.dateFormat),
          uploadedBy: doc.createdBy,
          attachmentType: doc.attachmentType,
        };
        if (true) attachmentRows.push(tempRow);
      });
      setAttachments(attachmentRows);
    };
    // invoiceHeaderData?.extInvNum &&
    doAjax(
      `/${destination_DocumentManagement}/documentManagement/getDocByRequestId/${requestId}`,
      "get",
      hSuccess
    );
  };
  const getComments = () => {
    let requestId = taskRowDetails?.requestId
      ? taskRowDetails?.requestId
      : generalLedgerRowData?.requestId;
    let hSuccess = (data) => {
      console.log("commentsdata", data);
      var commentRows = [];
      data.body.forEach((cmt) => {
        var tempRow = {
          id: cmt.requestId,
          comment: cmt.comment,
          user: cmt.createdByUser,
          createdAt: cmt.updatedAt,
        };
        commentRows.push(tempRow);
      });
      setComments(commentRows);
    };
    let hError = (error) => {
      console.log(error);
    };
    // invoiceHeaderData?.extInvNum &&
    doAjax(
      `/${destination_GeneralLedger}/activitylog/fetchTaskDetailsForRequestId?requestId=${requestId}`,
      "get",
      hSuccess,
      hError
    );
  };
  const onGeneralLedgerSaveAsDraftCreate = () => {
    handleGeneralLedgerSaveAsDraftCreate();
  };

  const handleGeneralLedgerSaveAsDraftCreate = () => {
    //console.log(messageDialogSeverity,"setMessageDialogSeverity")
    setMessageDialogSeverity(false);
    handleMessageDialogClickOpen();
    setMessageDialogTitle("Confirm");
    setMessageDialogMessage(`Do You Want to Save as Draft ?`);
    setHandleExtrabutton(true);
    setHandleExtraText("proceed");
    setDialogType("Create");
  };

  const onSaveAsDraft = () => {
    setSaveAsDraftState(true)
    let glNameFromGlViewData = "";
    generalLedgerViewData?.["Type/Description"]?.["Description"]?.map(
      (description_element) => {
        if (description_element?.fieldName === "Short Text") {
          glNameFromGlViewData = description_element?.value;
        }
      }
    );


    let glDescFromGlViewData = "";
    generalLedgerViewData?.["Type/Description"]?.["Description"]?.map(
      (description_element) => {
        if (description_element?.fieldName === "Long Text") {
          glDescFromGlViewData = description_element?.value;
        }
      }
    );


    if (glNameFromGlViewData !== singleGLPayload?.ShortText) {

      validateForGLNameDuplicateCheckFromSaveAsDraft()

    } else if (glDescFromGlViewData !== singleGLPayload?.LongText) {
      validateForGLDescriptionDuplicateCheckFromSaveAsDraft()

    } else {

      validateForGeneralLedgerAfterAllCheckFromSaveAsDraft()
    };

  };
  const onGeneralLedgerSaveAsDraftChange = () => {
    handleGeneralLedgerSaveAsDraftChange();
  };
  const onGeneralLedgerSaveAsDraftExtend = () => {
    handleGeneralLedgerSaveAsDraftExtend();
  };
  const handleGeneralLedgerSaveAsDraftChange = () => {
    setMessageDialogSeverity(false);
    handleMessageDialogClickOpen();
    setMessageDialogTitle("Confirm");
    setMessageDialogMessage(`Do You Want to Save as Draft?`);
    setHandleExtrabutton(true);
    setHandleExtraText("proceed");
    setDialogType("Change");
  };
  const handleGeneralLedgerSaveAsDraftExtend = () => {
    setMessageDialogSeverity(false);
    handleMessageDialogClickOpen();
    setMessageDialogTitle("Confirm");
    setMessageDialogMessage(`Do You Want to Save as Draft?`);
    setHandleExtrabutton(true);
    setHandleExtraText("proceed");
    setDialogType("Extend");
  };

  const handleProceedbutton = () => {
    console.log(dialogType, "dialogType");
    handleWarningDialogClose();
    setIsLoading(true);
    if (dialogType === "Change") {
      const hSuccess = (data) => {
        setIsLoading(false);
        if (data.statusCode === 200) {
          console.log("success");
          setMessageDialogTitle("Create");
          setMessageDialogMessage(
            `General Ledger Saved As Draft with ID GLSC${data.body} `
          );
          setHandleExtrabutton(false);
          setMessageDialogSeverity("success");
          setMessageDialogOK(false);
          setsuccessMsg(true);
          handleSnackBarOpen();
          setMessageDialogExtra(true);
          distinctArtifactId.map((artifactId) => {
            console.log(artifactId, "artifactId=====")
            const secondApiPayload = {
              artifactId: artifactId,
              createdBy: userData?.emailId,
              artifactType: "GeneralLedger",
              requestId: `GLSC${data?.body}`,
            };
            const secondApiSuccess = (secondApiData) => {
              console.log("Second API success", secondApiData);
              // Handle success for the second API if needed
            };

            const secondApiError = (secondApiError) => {
              console.error("Second API error", secondApiError);
              // Handle error for the second API if needed
            };
            // {requestId&&
            doAjax(
              `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
              "post",
              secondApiSuccess,
              secondApiError,
              secondApiPayload
            );

          })
          // const secondApiPayload = {
          //   artifactId: glNumber,
          //   createdBy: userData?.emailId,
          //   artifactType: "GeneralLedger",
          //   requestId: `GLSC${data?.body}`,
          // };
          // const secondApiSuccess = (secondApiData) => {
          //   console.log("Second API success", secondApiData);
          //   // Handle success for the second API if needed
          // };

          // const secondApiError = (secondApiError) => {
          //   console.error("Second API error", secondApiError);
          //   // Handle error for the second API if needed
          // };
          // // {requestId&&
          // doAjax(
          //   `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          //   "post",
          //   secondApiSuccess,
          //   secondApiError,
          //   secondApiPayload
          // );
        } else {
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          setMessageDialogMessage("Failed Saving General Ledger");
          setHandleExtrabutton(false);
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          // setIsLoading(false);
        }
        handleClose();
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
        `/${destination_GeneralLedger}/alter/changeGeneralLedgerAsDraft`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else if (dialogType === "Extend") {

      const hSuccess = (data) => {
        setIsLoading(false);
        if (data.statusCode === 200) {
          console.log("success");
          setMessageDialogTitle("Create");
          setMessageDialogMessage(
            `General Ledger Saved As Draft with ID GLSE${data.body} `
          );
          setHandleExtrabutton(false);
          setMessageDialogSeverity("success");
          setMessageDialogOK(false);
          setsuccessMsg(true);
          handleSnackBarOpen();
          setMessageDialogExtra(true);

          distinctArtifactId.map((artifactId) => {
            console.log(artifactId, "artifactId=====")
            const secondApiPayload = {
              artifactId: artifactId,
              createdBy: userData?.emailId,
              artifactType: "GeneralLedger",
              requestId: `GLSE${data?.body}`,
            };
            const secondApiSuccess = (secondApiData) => {
              console.log("Second API success", secondApiData);
              // Handle success for the second API if needed
            };

            const secondApiError = (secondApiError) => {
              console.error("Second API error", secondApiError);
              // Handle error for the second API if needed
            };
            // {requestId&&
            doAjax(
              `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
              "post",
              secondApiSuccess,
              secondApiError,
              secondApiPayload
            );

          })


        } else {
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          setMessageDialogMessage("Failed Saving General Ledger");
          setHandleExtrabutton(false);
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          // setIsLoading(false);
        }
        handleClose();
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
        `/${destination_GeneralLedger}/alter/extendGeneralLedgerAsDraft`,
        "post",
        hSuccess,
        hError,
        payload
      );

    } else {
      const hSuccess = (data) => {
        handleWarningDialogClose();
        setIsLoading(false);
        if (data.statusCode === 200) {
          console.log("success");
          setMessageDialogTitle("Create");
          setMessageDialogMessage(
            `Cost Center Saved As Draft with ID GLFS0NEW${data.body} `
          );
          setHandleExtrabutton(false);
          setMessageDialogSeverity("success");
          setMessageDialogOK(false);
          setsuccessMsg(true);
          handleSnackBarOpen();
          setMessageDialogExtra(true);
          distinctArtifactId.map((artifactId) => {
            console.log(artifactId, "artifactId=====")
            const secondApiPayload = {
              artifactId: artifactId,
              createdBy: userData?.emailId,
              artifactType: "GeneralLedger",
              requestId: `GLFS0NEW${data?.body}`,
            };
            const secondApiSuccess = (secondApiData) => {
              console.log("Second API success", secondApiData);
              // Handle success for the second API if needed
            };

            const secondApiError = (secondApiError) => {
              console.error("Second API error", secondApiError);
              // Handle error for the second API if needed
            };
            // {requestId&&
            doAjax(
              `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
              "post",
              secondApiSuccess,
              secondApiError,
              secondApiPayload
            );

          })
        } else {
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          setMessageDialogMessage("Failed Saving General Ledger");
          setHandleExtrabutton(false);
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          // setIsLoading(false);
        }
        handleClose();
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
        `/${destination_GeneralLedger}/alter/generalLedgerAsDraft`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };

  const handleOpenRemarkDialog = () => {
    setTestrunStatus(false);
    setOpenRemarkDialog(true);
  };

  const loaderCount = () => {
    if (apiCount == lookup?.generalLedger?.length) {
      setIsLoading(false);
    } else {
      setIsLoading(true);
    }
  };

  useEffect(() => {
    getAttachments();
    getComments();
  }, []);

  useEffect(() => {
    getCompanyCodeRows()
  }, [])


  function getDescriptionByCode(code, data) {
    console.log(code, data, "codeDatainglDescCode")
    const item = data?.find(entry => entry?.code === code);
    return item ? item?.desc : 'Description not found';
  }

  const getCompanyCodeRows = (responseIDs) => {
    console.log(location?.state, "generalLedgerRowData")


    const hSuccess = (data) => {
      console.log(data?.body, "hhhhhhhhhhh")
      if (location?.state?.compCode) {
        let compCodeArr = []
        location?.state?.compCode?.split(',')?.map((item, index) => {
          let compCodehash = {}
          compCodehash["id"] = index
          compCodehash["copyCompanyCode"] = item
          compCodehash["copyCompanyCodeDesc"] = getDescriptionByCode(item, data?.body);
          compCodeArr.push(compCodehash)

        })
        setCompanyCodeRow(compCodeArr)
      } else {
        let compCodeArr = []
        taskData?.body?.compCode?.split(',')?.map((item, index) => {
          let compCodehash = {}
          compCodehash["id"] = index
          compCodehash["copyCompanyCode"] = item
          compCodehash["copyCompanyCodeDesc"] = getDescriptionByCode(item, data?.body);
          compCodeArr.push(compCodehash)

        })
        console.log(compCodeArr, "compCodeArr")
        setCompanyCodeRow(compCodeArr)

      }

      //dispatch(setDropDown({ keyName: "CompanyCodeForWithCopy", data: companyCodeArr }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getCompanyCode?chartAccount=${generalLedgerRowData?.chartOfAccount
        ? generalLedgerRowData?.chartOfAccount
        : responseIDs?.ChartOfAccount}`,
      "get",
      hSuccess,
      hError
    );

  }

  const handleDDChangeGIQuestion = (label, newValue) => {
    console.log('newVALUE', newValue, label)
    dispatch(
      setSinglegeneralLedgerPayloadGI({
        keyName: label.replaceAll("(", "")
          .replaceAll(")", "")
          .replaceAll("/", "")
          .replaceAll("-", "")
          .replaceAll(".", "")
          .split(" ")
          .join(""),
        data: newValue?.code,
      }))
  }



  const handleRadioChangeGIQuestion = (event, label, newValue) => {
    console.log(event, newValue, "eventData----------")
    if (event?.target?.name === 'Is This Account Used For Manual Entries Only?') {
      console.log(event.target.value, "valuein Data")
      if (event?.target?.value === 'Yes') {
        //TaxCategory
        //alert("coming")
        dispatch(
          setPayload({
            keyname: "PostAutomaticallyOnly"
            ,
            data: false,
          })
        );

      } else {
        dispatch(
          setPayload({
            keyname: "PostAutomaticallyOnly",

            data: true,
          })
        );
      }
    }
    if (event?.target?.name === 'Are You Taxable?') {
      console.log(event?.target?.value, "valuein Data")
      if (event?.target?.value === 'Yes') {
        //TaxCategory
        dispatch(
          setPayload({
            keyname: "TaxCategory"
            ,
            data: "*",
          })
        );
        dispatch(
          setPayload({
            keyname: "PostingWithoutTaxAllowed"
            ,
            data: true,
          })
        );

      } else {
        dispatch(
          setPayload({
            keyname: "TaxCategory"
            ,
            data: '',
          })
        );
        dispatch(
          setPayload({
            keyname: "PostingWithoutTaxAllowed"
            ,
            data: false,
          })
        );
      }
    }
    if (event?.target?.name === 'Are You Making Ledger Specific Posting?') {
      console.log(event?.target?.value, "valuein Data")
      // debugger
      if (event?.target?.value === 'Yes') {
        //alert("openitem")
        //TaxCategory
        //         OpenItemManagement: true
        // ​
        // OpenItemManagementByLedgerGroup: false
        dispatch(
          setPayload({
            keyname: "OpenItemManagementByLedgerGroup",
            data: true,
          })
        );
        dispatch(
          setPayload({
            keyname: "OpenItemManagement",
            data: false,
          })
        );
        console.log('singleglpay', singleGLPayload)
      } else {
        dispatch(
          setPayload({
            keyname: "OpenItemManagementByLedgerGroup"
            ,
            data: false,
          })
        );
        dispatch(
          setPayload({
            keyname: "OpenItemManagement",
            data: true,
          })
        );
      }
    }
    let toSetArray = {}

    if (event?.target?.type === 'radio') {
      dispatch(
        setSinglegeneralLedgerPayloadGI({
          keyName: event.target.name
            .replaceAll("(", "")
            .replaceAll(")", "")
            .replaceAll("/", "")
            .replaceAll("-", "")
            .replaceAll(".", "")
            .split(" ")
            .join(""),
          data: event.target.value,
        })
      )
    }
    else {
      dispatch(
        setSinglegeneralLedgerPayloadGI({
          keyName: event?.target?.name
            .replaceAll("(", "")
            .replaceAll(")", "")
            .replaceAll("/", "")
            .replaceAll("-", "")
            .replaceAll(".", "")
            .split(" ")
            .join(""),
          data: event?.target?.value.toUpperCase(),
        })
      )
    }
  };

  const handleRadioChangeGIQuestionTextField = (event) => {
    let newValue = event.target.value
    console.log(event, newValue, event?.target?.name, "eventData----------")
    if (event?.target?.name === 'Is This Account Used For Manual Entries Only?') {
      console.log(event.target.value, event?.target?.name, "valuein Data")
      if (event?.target?.value === 'Yes') {
        //TaxCategory

        dispatch(
          setPayload({
            keyname: "PostAutomaticallyOnly"
            ,
            data: false,
          })
        );

      } else {
        dispatch(
          setPayload({
            keyname: "PostAutomaticallyOnly",

            data: true,
          })
        );
      }
    }
    if (event?.target?.name === 'Are You Taxable?') {
      console.log(event?.target?.value, "valuein Data")
      if (event?.target?.value === 'Yes') {
        //TaxCategory
        dispatch(
          setPayload({
            keyname: "TaxCategory"
            ,
            data: "*",
          })
        );
        dispatch(
          setPayload({
            keyname: "PostingWithoutTaxAllowed"
            ,
            data: true,
          })
        );

      } else {
        dispatch(
          setPayload({
            keyname: "TaxCategory"
            ,
            data: '',
          })
        );
        dispatch(
          setPayload({
            keyname: "PostingWithoutTaxAllowed"
            ,
            data: false,
          })
        );
      }
    }
    if (event?.target?.name === 'Are You Making Ledger Specific Posting?') {
      console.log(event?.target?.value, "valuein Data")
      if (event?.target?.value === 'Yes') {
        //TaxCategory
        //         OpenItemManagement: true
        // ​
        // OpenItemManagementByLedgerGroup: false
        dispatch(
          setPayload({
            keyname: "OpenItemManagementByLedgerGroup",
            data: true,
          })
        );
        dispatch(
          setPayload({
            keyname: "OpenItemManagement",
            data: false,
          })
        );

      } else {
        dispatch(
          setPayload({
            keyname: "OpenItemManagementByLedgerGroup"
            ,
            data: false,
          })
        );
        dispatch(
          setPayload({
            keyname: "OpenItemManagement",
            data: true,
          })
        );
      }
    }
    let toSetArray = {}

    if (event?.target?.type === 'radio') {
      dispatch(
        setSinglegeneralLedgerPayloadGI({
          keyName: event.target.name
            .replaceAll("(", "")
            .replaceAll(")", "")
            .replaceAll("/", "")
            .replaceAll("-", "")
            .replaceAll(".", "")
            .split(" ")
            .join(""),
          data: event.target.value,
        })
      )
    }
    else {
      let selectedLabel = event?.target?.name.replaceAll("(", "")
        .replaceAll(")", "")
        .replaceAll("/", "")
        .replaceAll("-", "")
        .replaceAll(".", "")
        .split(" ")
        .join("")
      dispatch(
        setSinglegeneralLedgerPayloadGI({
          keyName: selectedLabel,
          data: event?.target?.value,
        })
      );
      setNewValueLength(event?.target?.value?.length);
    }
  };

  const onEditCapitalizeBlur = (label, newValue) => {
    let selectedLabel = label.replaceAll("(", "")
      .replaceAll(")", "")
      .replaceAll("/", "")
      .replaceAll("-", "")
      .replaceAll(".", "")
      .split(" ")
      .join("")
    dispatch(
      setSinglegeneralLedgerPayloadGI({
        keyName: selectedLabel,
        data: newValue[0] === " " ? newValue.toUpperCase().trimStart() : newValue.toUpperCase(),
      })
    );
  };


  console.log(singleGLPayloadMain, "singleGLPayloadafterDispatch")
  useEffect(() => {
    loaderCount();
  }, [apiCount]);
  console.log(factorsArray, "factorsArray=======")

  const onEditDate = (label, newValue) => {
    console.log("label", label, newValue);

    dispatch(
      setSinglegeneralLedgerPayloadGI({
        keyName: label.replaceAll("(", "")
          .replaceAll(")", "")
          .replaceAll("/", "")
          .replaceAll("-", "")
          .replaceAll(".", "")
          .split(" ")
          .join(""),
        data: newValue ? newValue : null,
      }))
  };

  const OpenToolTipIconButton = () => {
    setOpenDialogForFerc(true)
  }

  const tabContents = factorsArray
    ?.map((item) => {
      const mdata = costCenterDetails.filter(
        (ii) => ii.category?.split(" ")[0] == item?.split(" ")[0]
      );
      if (mdata.length != 0) {
        return { category: item?.split(" ")[0], data: mdata[0].data };
      }
    })
    ?.map((categoryData, index) => {
      console.log("categoryy", categoryData?.category);

      if (categoryData?.category == "Type/Description" && activeStep == 1) {
        return [
          <Grid
            key={categoryData.category}
            container
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              // borderRadius: "8px",
              // border: "1px solid #E0E0E0",
              mt: 1,
              // boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
              // padding: "10px",
              mb: 1,
            }}
          >
            {console.log(Object.keys(categoryData.data), "fieldGroupData====")}
            {Object.keys(categoryData.data)?.map((fieldGroup) => (

              <Grid
                key={fieldGroup}
                item
                md={12}
                sx={{
                  backgroundColor: "white",
                  maxHeight: "max-content",
                  height: "max-content",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                  mt: 0.25,
                  boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                  ...container_Padding,
                  // ...container_columnGap,
                }}
              >
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                    margin: "0px !important",
                  }}
                >
                  {fieldGroup}
                </Typography>
                <Box sx={{ width: "100%" }}>
                  <CardContent
                    sx={{
                      padding: "0",
                      paddingBottom: "0 !important",
                      paddingTop: "10px !important",
                    }}
                  >
                    <Grid
                      container
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(6,1fr)",
                        gap: "15px",
                      }}
                      justifyContent="space-between"
                      alignItems="flex-start"
                      md={12}
                    >
                      {categoryData.data[fieldGroup]?.map((field) => {
                        console.log("fieldDatatttt", field);
                        return (
                          <EditableFieldForGL
                            // key={field.fieldName}
                            length={field.maxLength}
                            label={field.fieldName}
                            value={field.value}
                            visibility={field.visibility}
                            onSave={(newValue) =>
                              handleFieldSave(field.fieldName, newValue)
                            }
                            data={singleGLPayload}
                            isEditMode={isEditMode}
                            // isExtendMode={isExtendMode}
                            type={field.fieldType}
                            field={field} // Update the type as needed
                            taskRequestId={generalLedgerRowData?.requestId}
                            generalLedgerRowData={generalLedgerRowData}
                          />
                        );
                      })}
                    </Grid>
                  </CardContent>
                </Box>
              </Grid>
            ))}
          </Grid>,
        ];
      } else if (categoryData?.category == "Control" && activeStep == 2) {
        return [
          <Grid
            key={categoryData.category}
            container
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              mt: 1,
              mb: 1,
            }}
          >
            {Object.keys(categoryData.data)?.map((fieldGroup) => (
              <Grid
                key={fieldGroup}
                item
                md={12}
                sx={{
                  backgroundColor: "white",
                  maxHeight: "max-content",
                  height: "max-content",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                  mt: 0.25,
                  boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                  ...container_Padding,
                  // ...container_columnGap,
                }}
              >
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                    margin: "0px !important",
                  }}
                >
                  {fieldGroup}
                </Typography>
                <Box sx={{ width: "100%" }}>
                  <CardContent>
                    <Grid
                      container
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(6,1fr)",
                        gap: "15px",
                      }}
                      justifyContent="space-between"
                      alignItems="flex-start"
                      md={12}
                    >
                      {categoryData.data[fieldGroup]?.map((field) => (
                        <EditableFieldForGL
                          key={field.fieldName}
                          label={field.fieldName}
                          value={field?.value === "X" ? true : false}
                          onSave={(newValue) =>
                            handleFieldSave(field.fieldName, newValue)
                          }
                          isEditMode={isEditMode}
                          type={field.fieldType} // Update the type as needed
                          taskRequestId={generalLedgerRowData?.requestId}
                          visibility={field.visibility}
                          data={singleGLPayload}
                          generalLedgerRowData={generalLedgerRowData}
                        />
                      ))}
                    </Grid>
                  </CardContent>
                </Box>
              </Grid>
            ))}
          </Grid>,
        ];
      } else if (
        categoryData?.category == "Create/Bank/Interest" &&
        activeStep == 3
      ) {
        return [
          <Grid
            key={categoryData.category}
            container
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              // borderRadius: "8px",
              // border: "1px solid #E0E0E0",
              mt: 1,
              // boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
              // padding: "10px",
              mb: 1,
            }}
          >
            {Object.keys(categoryData.data)?.map((fieldGroup) => (
              <Grid
                key={fieldGroup}
                item
                md={12}
                sx={{
                  backgroundColor: "white",
                  maxHeight: "max-content",
                  height: "max-content",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                  mt: 0.25,
                  boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                  ...container_Padding,
                  // ...container_columnGap,
                }}
              >
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                    margin: "0px !important",
                  }}
                >
                  {fieldGroup}
                </Typography>
                <Box sx={{ width: "100%" }}>
                  <CardContent>
                    <Grid
                      container
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(6,1fr)",
                        gap: "15px",
                      }}
                      justifyContent="space-between"
                      alignItems="flex-start"
                      md={12}
                    >
                      {categoryData.data[fieldGroup]?.map((field) => (
                        <EditableFieldForGL
                          key={field.fieldName}
                          label={field.fieldName}
                          value={field.value}
                          onSave={(newValue) =>
                            handleFieldSave(field.fieldName, newValue)
                          }
                          visibility={field.visibility}
                          isEditMode={isEditMode}
                          // isExtendMode={isExtendMode}
                          type={field.fieldType} // Update the type as needed
                          taskRequestId={generalLedgerRowData?.requestId}
                          data={singleGLPayload}
                          generalLedgerRowData={generalLedgerRowData}
                        />
                      ))}
                    </Grid>
                  </CardContent>
                </Box>
              </Grid>
            ))}
          </Grid>,
        ];
      } else if (
        categoryData?.category == "Keyword/Translation" &&
        activeStep == 4
      ) {
        return [
          <Grid
            key={categoryData.category}
            container
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              // borderRadius: "8px",
              // border: "1px solid #E0E0E0",
              mt: 1,
              // boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
              // padding: "10px",
              mb: 1,
            }}
          >
            {Object.keys(categoryData.data)?.map((fieldGroup) => (
              <Grid
                key={fieldGroup}
                item
                md={12}
                sx={{
                  backgroundColor: "white",
                  maxHeight: "max-content",
                  height: "max-content",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                  mt: 0.25,
                  boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                  ...container_Padding,
                  // ...container_columnGap,
                }}
              >
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                    margin: "0px !important",
                  }}
                >
                  {fieldGroup}
                </Typography>
                <Box sx={{ width: "100%" }}>
                  <CardContent>
                    <Grid
                      container
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(6,1fr)",
                        gap: "15px",
                      }}
                      justifyContent="space-between"
                      alignItems="flex-start"
                      md={12}
                    >
                      {categoryData.data[fieldGroup]?.map((field) => (
                        <EditableFieldForGL
                          key={field.fieldName}
                          label={field.fieldName}
                          value={field.value}
                          onSave={(newValue) =>
                            handleFieldSave(field.fieldName, newValue)
                          }
                          isEditMode={isEditMode}
                          // isExtendMode={isExtendMode}
                          type={field.fieldType} // Update the type as needed
                          taskRequestId={generalLedgerRowData?.requestId}
                          visibility={field.visibility}
                          data={singleGLPayload}
                          generalLedgerRowData={generalLedgerRowData}
                        />
                      ))}
                    </Grid>
                  </CardContent>
                </Box>
              </Grid>
            ))}
          </Grid>,
        ];
      } else if (categoryData?.category == "Information" && activeStep == 5) {
        return [
          <Grid
            key={categoryData.category}
            container
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              // borderRadius: "8px",
              // border: "1px solid #E0E0E0",
              mt: 1,
              // boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
              // padding: "10px",
              mb: 1,
            }}
          >
            {Object.keys(categoryData.data)?.map((fieldGroup) => (
              <Grid
                key={fieldGroup}
                item
                md={12}
                sx={{
                  backgroundColor: "white",
                  maxHeight: "max-content",
                  height: "max-content",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                  mt: 0.25,
                  boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                  ...container_Padding,
                  // ...container_columnGap,
                }}
              >
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                    margin: "0px !important",
                  }}
                >
                  {fieldGroup}
                </Typography>
                <Box sx={{ width: "100%" }}>
                  <CardContent>
                    <Grid
                      container
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(6,1fr)",
                        gap: "15px",
                      }}
                      justifyContent="space-between"
                      alignItems="flex-start"
                      md={12}
                    >
                      {categoryData.data[fieldGroup]?.map((field) => (
                        <EditableFieldForGL
                          key={field.fieldName}
                          label={field.fieldName}
                          value={field.value}
                          onSave={(newValue) =>
                            handleFieldSave(field.fieldName, newValue)
                          }
                          isEditMode={isEditMode}
                          // isExtendMode={isExtendMode}
                          type={field.fieldType} // Update the type as needed
                          taskRequestId={generalLedgerRowData?.requestId}
                          visibility={field.visibility}
                          data={singleGLPayload}
                          generalLedgerRowData={generalLedgerRowData}
                        />
                      ))}
                    </Grid>
                  </CardContent>
                </Box>
              </Grid>
            ))}
          </Grid>,
        ];
      } else if (categoryData?.category == "Attachments") {
        return [
          <>
            {generalLedgerRowData?.reqStatus === "Draft" || (Object.keys(taskRowDetails).length && taskRowDetails?.taskNature === "Single-User" && taskRowDetails?.itmStatus === "Open") || (Object.keys(taskRowDetails).length && taskRowDetails?.taskNature !== "Single-User" && taskRowDetails?.itmStatus !== "Open")
              ?
              attachmentFromIdm?.map((item, index) => {
                return (
                  <Grid
                    item
                    md={12}
                    sx={{
                      backgroundColor: "white",
                      maxHeight: "max-content",
                      height: "max-content",
                      borderRadius: "8px",
                      border: "1px solid #E0E0E0",
                      mt: 0.25,
                      boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                      ...container_Padding,
                    }}
                  >
                    <><Grid container>
                      <Typography
                        sx={{
                          fontSize: "12px",
                          fontWeight: "700",
                        }}
                      >
                        {/* {item?.MDG_GI_VISIBILITY === "Mandatory"  ? (
                          <span style={{ color: "red" }}>*</span>
                        ) : (
                          ""
                        )} */}

                        {item.MDG_ATTACHMENTS_NAME}
                        {item?.MDG_ATTACHMENTS_NAME === "Mandatory" ? (
                          <span style={{ color: "red" }}>*</span>
                        ) : (
                          ""
                        )}

                      </Typography>
                    </Grid>

                      <Grid container>
                        <Grid item>
                          <ReusableAttachementAndComments
                            title="GeneralLedger"
                            useMetaData={false}
                            artifactId={`${item.MDG_ATTACHMENTS_NAME}_${glNumber}`}
                            artifactName="GeneralLedger"
                            attachmentType={item.MDG_ATTACHMENTS_NAME}
                          />

                        </Grid>
                      </Grid>
                    </>


                  </Grid>)

              })
              : ""}
            {!isEditMode ? (
              <Card sx={{ padding: "1rem 1rem 0rem 1rem" }}>
                <Grid
                  container
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                  }}
                >
                  <Typography variant="h6">
                    <strong>Attachments</strong>
                  </Typography>
                </Grid>
                {Boolean(attachments.length) && (
                  <ReusableTable
                    width="100%"
                    rows={attachments}
                    columns={attachmentColumns}
                    hideFooter={false}
                    getRowIdValue={"id"}
                    disableSelectionOnClick={true}
                    stopPropagation_Column={"action"}
                  />
                )}
                {!Boolean(attachments.length) && (
                  <Typography variant="body2">No Attachments Found</Typography>
                )}
                <br />
                <Typography variant="h6">Comments</Typography>
                {Boolean(comments.length) && (
                  <Timeline
                    sx={{
                      [`& .${timelineItemClasses.root}:before`]: {
                        flex: 0,
                        padding: 0,
                      },
                    }}
                  >
                    {comments?.map((comment) => (
                      // {console.log(comment)}
                      <TimelineItem>
                        <TimelineSeparator>
                          <TimelineDot>
                            {/* chiranjit */}

                            <CheckCircleOutlineOutlined
                              sx={{ color: "#757575" }}
                            />
                          </TimelineDot>
                          <TimelineConnector />
                        </TimelineSeparator>
                        <TimelineContent sx={{ py: "12px", px: 2 }}>
                          <Card
                            elevation={0}
                            sx={{
                              border: 1,
                              borderColor: "#C4C4C4",
                              borderRadius: "8px",
                              width: "650px",
                            }}
                          >
                            <Box sx={{ padding: "1rem" }}>
                              <Stack spacing={1}>
                                <Grid
                                  sx={{
                                    display: "flex",
                                    justifyContent: "space-between",
                                  }}
                                >
                                  <Typography
                                    sx={{
                                      textAlign: "right",
                                      color: " #757575",
                                      fontWeight: "500",
                                      fontSize: "14px",
                                    }}
                                  >
                                    {moment(comment.createdAt).format(
                                      appSettings.date
                                    )}
                                  </Typography>
                                </Grid>

                                <Typography
                                  sx={{
                                    fontSize: "14px",

                                    color: " #757575",
                                    fontWeight: "500",
                                  }}
                                >
                                  {comment.user}
                                </Typography>
                                <Typography
                                  sx={{
                                    fontSize: "14px",
                                    color: "#1D1D1D",
                                    fontWeight: "600",
                                    wordWrap: "break-word",
                                  }}
                                >
                                  {comment.comment}
                                </Typography>
                              </Stack>
                            </Box>
                          </Card>
                        </TimelineContent>
                      </TimelineItem>
                    ))}
                  </Timeline>
                )}
                {!Boolean(comments.length) && (
                  <Typography variant="body2">No Comments Found</Typography>
                )}
                <br />
              </Card>
            ) : (
              <>
                <Grid container>
                  <Grid
                    item
                    md={12}
                    sx={{
                      backgroundColor: "white",
                      maxHeight: "max-content",
                      height: "max-content",
                      borderRadius: "8px",
                      border: "1px solid #E0E0E0",
                      mt: 0.25,
                      boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                      ...container_Padding,
                    }}
                  >
                    {/* <Grid container>
                      <Typography
                        sx={{
                          fontSize: "12px",
                          fontWeight: "700",
                        }}
                      >
                        Additional Attachments
                      </Typography>
                    </Grid> */}

                    {/* <Grid container>
                      <Grid item>
                        <ReusableAttachementAndComments
                          title="GeneralLedger"
                          useMetaData={false}
                          artifactId={glNumber}
                          artifactName="GeneralLedger"
                        />
                      </Grid>
                    </Grid> */}
                  </Grid>
                </Grid>
                <Grid container>
                  <Grid
                    item
                    md={12}
                    sx={{
                      backgroundColor: "white",
                      maxHeight: "max-content",
                      height: "max-content",
                      borderRadius: "8px",
                      border: "1px solid #E0E0E0",
                      mt: 0.25,
                      boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                      ...container_Padding,
                    }}
                  >
                    {/* <Card sx={{ padding: "1rem 1rem 0rem 1rem" }}> */}
                    <Grid
                      container
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                      }}
                    >
                      <Typography variant="h6">
                        <strong>Attachments</strong>
                      </Typography>
                    </Grid>
                    {Boolean(attachments.length) && (
                      <ReusableTable
                        width="100%"
                        rows={attachments}
                        columns={attachmentColumns}
                        hideFooter={false}
                        getRowIdValue={"id"}
                        disableSelectionOnClick={true}
                        stopPropagation_Column={"action"}
                      />
                    )}
                    {!Boolean(attachments.length) && (
                      <Typography variant="body2">
                        No Attachments Found
                      </Typography>
                    )}
                    <br />
                    <Typography variant="h6">Comments</Typography>
                    {Boolean(comments.length) && (
                      <Timeline
                        sx={{
                          [`& .${timelineItemClasses.root}:before`]: {
                            flex: 0,
                            padding: 0,
                          },
                        }}
                      >
                        {comments?.map((comment) => (
                          <TimelineItem>
                            <TimelineSeparator>
                              <TimelineDot>
                                <CheckCircleOutlineOutlined
                                  sx={{ color: "#757575" }}
                                />
                              </TimelineDot>
                              <TimelineConnector />
                            </TimelineSeparator>
                            <TimelineContent sx={{ py: "12px", px: 2 }}>
                              <Card
                                elevation={0}
                                sx={{
                                  border: 1,
                                  borderColor: "#C4C4C4",
                                  borderRadius: "8px",
                                  width: "650px",
                                }}
                              >
                                <Box sx={{ padding: "1rem" }}>
                                  <Stack spacing={1}>
                                    <Grid
                                      sx={{
                                        display: "flex",
                                        justifyContent: "space-between",
                                      }}
                                    >
                                      <Typography
                                        sx={{
                                          textAlign: "right",
                                          color: " #757575",
                                          fontWeight: "500",
                                          fontSize: "12px",
                                        }}
                                      >
                                        {moment(comment.createdAt).format(
                                          "DD MMM YYYY"
                                        )}
                                      </Typography>
                                    </Grid>

                                    <Typography
                                      sx={{
                                        fontSize: "12px",

                                        color: " #757575",
                                        fontWeight: "500",
                                      }}
                                    >
                                      {comment.user}
                                    </Typography>
                                    <Typography
                                      sx={{
                                        fontSize: "12px",
                                        color: "#1D1D1D",
                                        fontWeight: "600",
                                        wordWrap: "break-word",
                                      }}
                                    >
                                      {comment.comment}
                                    </Typography>
                                  </Stack>
                                </Box>
                              </Card>
                            </TimelineContent>
                          </TimelineItem>
                        ))}
                      </Timeline>
                    )}
                    {!Boolean(comments.length) && (
                      <Typography variant="body2">No Comments Found</Typography>
                    )}
                    <br />
                    {/* </Card> */}
                  </Grid>
                </Grid>
              </>
            )}
          </>,
        ];
      } else if (categoryData?.category == "General") {
        //alert("coming")
        return [
          <>
            {generalLedgerRowData?.reqStatus === "Draft" ?
              // {/* {demoData?.requestStatus=== "Draft" ? */}
              <Grid container spacing={2}>
                {questions?.map((question, index) => (
                  <Grid item md={12} key={index}>
                    <Grid
                      container
                      sx={{
                        backgroundColor: "white",
                        maxHeight: "max-content",
                        height: "max-content",
                        borderRadius: "8px",
                        border: "1px solid #E0E0E0",
                        mt: 0.25,
                        boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                        padding: "16px",
                      }}
                    >
                      <Stack sx={{ width: "100%" }}>
                        <Typography
                          sx={{
                            fontSize: "12px",
                            fontWeight: "700",
                          }}
                        >
                          {question.MDG_GI_QUESTION_TYPE}
                          {question.MDG_GI_VISIBILITY === "Mandatory" ? (
                            <span style={{ color: "red" }}>*</span>
                          ) : (
                            ""
                          )}
                        </Typography>

                        {question.MDG_GI_INPUT_OPTION === "Radio Button" ? (
                          <RadioGroup
                            aria-labelledby={`radio-group-label-${index}`}
                            defaultValue=""
                            //name={`radio-group-${index}`}
                            value={singleGLPayloadMain[question.MDG_GI_QUESTION_TYPE
                              .replaceAll("(", "")
                              .replaceAll(")", "")
                              .replaceAll("/", "")
                              .replaceAll("-", "")
                              .replaceAll(".", "")
                              .split(" ")
                              .join("")]}
                            name={question.MDG_GI_QUESTION_TYPE}
                            row
                            onChange={(event) => {
                              let label = question.MDG_GI_QUESTION_TYPE;
                              const newValue = event.target.value;
                              //handleRadioChangeGIQuestion(label, newValue);
                              handleRadioChangeGIQuestion(event, label, newValue)
                              handleCheckValidationError();
                            }}
                          >
                            {question.MDG_GI_INPUT_VALUE.split(",").map(
                              (option, optIndex) => (
                                <FormControlLabel
                                  key={optIndex}
                                  value={option}
                                  control={<Radio />}
                                  label={option}
                                />
                              )
                            )}
                          </RadioGroup>
                        ) : question.MDG_GI_INPUT_OPTION === 'Dropdown' ? (
                          <Grid item md={2}>
                            {console.log(dropDownData[question.MDG_GI_QUESTION_TYPE], question.MDG_GI_QUESTION_TYPE, "hjkl")}
                            <Autocomplete
                              sx={{ height: "31px" }}
                              // disabled
                              fullWidth
                              size="small"
                              // value={valueFromPayload[keyName]}
                              value={
                                (singleGLPayloadMain[question.MDG_GI_QUESTION_TYPE.replaceAll("(", "")
                                  .replaceAll(")", "")
                                  .replaceAll("/", "")
                                  .replaceAll("-", "")
                                  .replaceAll(".", "")
                                  .split(" ")
                                  .join("")] &&
                                  dropDownData[question.MDG_GI_QUESTION_TYPE.replaceAll("(", "")
                                    .replaceAll(")", "")
                                    .replaceAll("/", "")
                                    .replaceAll("-", "")
                                    .replaceAll(".", "")
                                    .split(" ")
                                    .join("")]?.filter(
                                      (x) =>
                                        x.code ===
                                        singleGLPayloadMain[question.MDG_GI_QUESTION_TYPE.replaceAll("(", "")
                                          .replaceAll(")", "")
                                          .replaceAll("/", "")
                                          .replaceAll("-", "")
                                          .replaceAll(".", "")
                                          .split(" ")
                                          .join("")]
                                    ) &&
                                  dropDownData[question.MDG_GI_QUESTION_TYPE.replaceAll("(", "")
                                    .replaceAll(")", "")
                                    .replaceAll("/", "")
                                    .replaceAll("-", "")
                                    .replaceAll(".", "")
                                    .split(" ")
                                    .join("")]?.filter(
                                      (x) =>
                                        x.code ===
                                        singleGLPayloadMain[question.MDG_GI_QUESTION_TYPE.replaceAll("(", "")
                                          .replaceAll(")", "")
                                          .replaceAll("/", "")
                                          .replaceAll("-", "")
                                          .replaceAll(".", "")
                                          .split(" ")
                                          .join("")]
                                    )[0]) ||
                                singleGLPayloadMain[question.MDG_GI_QUESTION_TYPE.replaceAll("(", "")
                                  .replaceAll(")", "")
                                  .replaceAll("/", "")
                                  .replaceAll("-", "")
                                  .replaceAll(".", "")
                                  .split(" ")
                                  .join("")]
                              }


                              // onChange={(e, newValue)=>{handleDDChangeGIQuestion(question.MDG_GI_QUESTION_TYPE,newValue )}}
                              onChange={(event, newValue) => handleDDChangeGIQuestion(question?.MDG_GI_QUESTION_TYPE, newValue)}
                              // onChange={(event, newValue) => {
                              //   let label = question.MDG_GI_QUESTION_TYPE;
                              //   onEditGI(label, newValue?.MDG_LOOKUP_CODE);
                              // handleCheckValidationErrorGI();}}
                              options={dropDownData[question.MDG_GI_QUESTION_TYPE?.replaceAll("(", "")
                                .replaceAll(")", "")
                                .replaceAll("/", "")
                                .replaceAll("-", "")
                                .replaceAll(".", "")
                                .split(" ")
                                .join("")
                              ] ?? []}
                              getOptionLabel={(option) => `${option?.code} `}
                              renderOption={(props, option) => (
                                <li {...props}>
                                  <Typography style={{ fontSize: 12 }}>
                                    {option?.desc ? (
                                      <>
                                        <strong>{option.code}</strong> -{" "}
                                        {option.desc}
                                      </>
                                    ) : (
                                      <strong>{option.code}</strong>
                                    )}
                                  </Typography>
                                </li>
                              )}
                              renderInput={(params) => (
                                <TextField
                                  {...params}
                                  variant="outlined"
                                  placeholder="Please Enter..."
                                // error={errorFields.includes(keyName)}
                                />
                              )}
                            />
                          </Grid>
                        ) : question.MDG_GI_INPUT_OPTION === "Calendar" ? (
                          <LocalizationProvider dateAdapter={AdapterDateFns}>
                            <DatePicker
                              slotProps={{ textField: { size: "small" } }}

                              value={
                                new Date(singleGLPayloadMain[question?.MDG_GI_QUESTION_TYPE
                                  ?.replaceAll("(", "")
                                  ?.replaceAll(")", "")
                                  ?.replaceAll("/", "")
                                  ?.replaceAll("-", "")
                                  ?.replaceAll(".", "")
                                  ?.split(" ")
                                  ?.join("")])
                              }
                              placeholder="Select Date Range"
                              maxDate={new Date(9999, 12, 31)}
                              onChange={(newValue) => {
                                onEditDate(
                                  question?.MDG_GI_QUESTION_TYPE,
                                  newValue ? newValue : null
                                );
                              }}
                            />
                          </LocalizationProvider>
                        ) :

                          question.MDG_GI_QUESTION_TYPE === 'GL Sub Account Type' ? (<TextField
                            //fullWidth
                            //placeholder="Please Enter..."
                            multiline
                            name={question.MDG_GI_QUESTION_TYPE}
                            inputProps={{ maxLength: 200 }}
                            value={singleGLPayloadMain['GL Sub Account Type'
                              .replaceAll("(", "")
                              .replaceAll(")", "")
                              .replaceAll("/", "")
                              .replaceAll("-", "")
                              .replaceAll(".", "")
                              .split(" ")
                              .join("")]}
                            disabled
                          //onChange={handleRadioChangeGIQuestion} 
                          />) : question.MDG_GI_QUESTION_TYPE === 'Message' ? (<TextField
                            //fullWidth
                            //placeholder="Please Enter..."
                            multiline
                            name={question.MDG_GI_QUESTION_TYPE}
                            inputProps={{ maxLength: 200 }}
                            value={singleGLPayloadMain['Message'
                              .replaceAll("(", "")
                              .replaceAll(")", "")
                              .replaceAll("/", "")
                              .replaceAll("-", "")
                              .replaceAll(".", "")
                              .split(" ")
                              .join("")]}
                            disabled
                          //onChange={handleRadioChangeGIQuestion} 
                          />) : (
                            // <TextField
                            //   fullWidth
                            //   placeholder="Please Enter..."
                            //   multiline
                            //   name={question.MDG_GI_QUESTION_TYPE}
                            //   inputProps={{
                            //     style: { textTransform: 'uppercase' },
                            //     maxLength:254 // This handles the transformation directly
                            //   }}
                            //   value={singleGLPayloadMain[question.MDG_GI_QUESTION_TYPE
                            //     .replaceAll("(", "")
                            //     .replaceAll(")", "")
                            //     .replaceAll("/", "")
                            //     .replaceAll("-", "")
                            //     .replaceAll(".", "")
                            //     .split(" ")
                            //     .join("")]}
                            //   //onChange={handleRadioChangeGIQuestion}
                            //   onBlur={(e) => onEditCapitalizeBlur(question.MDG_GI_QUESTION_TYPE, e.target.value)}
                            //   onChange={(event) => {
                            //     let label = question.MDG_GI_QUESTION_TYPE;
                            //     const newValue = event.target.value;
                            //     //handleRadioChangeGIQuestion(label, newValue);
                            //     handleRadioChangeGIQuestion(event,label, newValue)
                            //     handleCheckValidationError();
                            //   }
                            // }
                            // />
                            <TextField
                              fullWidth
                              placeholder="Please Enter..."
                              multiline
                              disabled={!isEditMode}
                              name={question.MDG_GI_QUESTION_TYPE}

                              inputProps={{
                                style: { textTransform: 'uppercase' },
                                maxLength: 254  // This handles the transformation directly
                              }}
                              value={
                                singleGLPayloadMain[
                                question.MDG_GI_QUESTION_TYPE.replaceAll("(", "")
                                  .replaceAll(")", "")
                                  .replaceAll("/", "")
                                  .replaceAll("-", "")
                                  .replaceAll(".", "")
                                  .split(" ")
                                  .join("")
                                ]
                              }
                              sx={{
                                "& .MuiOutlinedInput-root": {
                                  "&.Mui-focused fieldset": {
                                    borderColor:
                                      isFocused && newValueLength >= 254 ? "red" : "", // Change border color when focused
                                  },
                                  "& fieldset": {
                                    borderColor:
                                      isFocused && newValueLength >= 254 ? "red" : "", // Change border color when limit exceeded
                                  },
                                },
                                paddingBottom: "4px"
                              }}
                              onFocus={() => setIsFocused(true)}

                              helperText={
                                isFocused &&
                                (newValueLength === 254
                                  ? `Max Length Reached`
                                  : `${newValueLength}/254`
                                )
                              }
                              FormHelperTextProps={{
                                sx: {
                                  color:
                                    isFocused &&
                                      newValueLength === 254
                                      ? "red"
                                      : "blue",
                                  position: "absolute",
                                  bottom: "-20px",
                                  paddingBottom: "4px"
                                },
                              }}
                              //onChange={handleRadioChangeGIQuestionTextField}
                              onBlur={(e) => {
                                onEditCapitalizeBlur(question.MDG_GI_QUESTION_TYPE, e.target.value)
                                setIsFocused(false)
                              }}
                              onChange={(event) => {
                                handleRadioChangeGIQuestionTextField(event)

                              }
                              }
                            />
                          )}
                      </Stack>
                    </Grid>
                  </Grid>
                ))}
              </Grid> : <Grid container spacing={2}>
                {questions.map((question, index) => (
                  <Grid item md={12} key={index}>
                    <Grid
                      container
                      sx={{
                        backgroundColor: "white",
                        maxHeight: "max-content",
                        height: "max-content",
                        borderRadius: "8px",
                        border: "1px solid #E0E0E0",
                        mt: 0.25,
                        boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                        padding: "16px",
                      }}
                    >
                      <Stack sx={{ width: "100%" }}>
                        <Typography
                          sx={{
                            fontSize: "12px",
                            fontWeight: "700",
                          }}
                        >
                          {question.MDG_GI_QUESTION_TYPE}
                          {question.MDG_GI_VISIBILITY === "Mandatory" ? (
                            <span style={{ color: "red" }}>*</span>
                          ) : (
                            ""
                          )}
                        </Typography>

                        {question.MDG_GI_INPUT_OPTION === "Radio Button" ? (
                          <RadioGroup
                            aria-labelledby={`radio-group-label-${index}`}
                            defaultValue=""
                            //name={`radio-group-${index}`}
                            value={singleGLPayloadMain[question.MDG_GI_QUESTION_TYPE
                              .replaceAll("(", "")
                              .replaceAll(")", "")
                              .replaceAll("/", "")
                              .replaceAll("-", "")
                              .replaceAll(".", "")
                              .split(" ")
                              .join("")]}

                            name={question.MDG_GI_QUESTION_TYPE}
                            row
                            onChange={(event) => {
                              let label = question.MDG_GI_QUESTION_TYPE;
                              const newValue = event.target.value;
                              //handleRadioChangeGIQuestion(label, newValue);
                              handleRadioChangeGIQuestion(event, label, newValue)
                              handleCheckValidationError();
                            }}
                          >
                            {question.MDG_GI_INPUT_VALUE.split(",").map(
                              (option, optIndex) => (
                                <FormControlLabel
                                  key={optIndex}
                                  value={option}
                                  control={<Radio disabled />}
                                  label={option}
                                />
                              )
                            )}
                          </RadioGroup>
                        ) : (
                          <TextField
                            fullWidth
                            placeholder="Please Enter..."
                            multiline
                            name={question.MDG_GI_QUESTION_TYPE}
                            inputProps={{ maxLength: 200 }}
                            value={singleGLPayloadMain[question.MDG_GI_QUESTION_TYPE
                              .replaceAll("(", "")
                              .replaceAll(")", "")
                              .replaceAll("/", "")
                              .replaceAll("-", "")
                              .replaceAll(".", "")
                              .split(" ")
                              .join("")]}
                            onChange={handleRadioChangeGIQuestion}
                            disabled
                          />
                        )}
                      </Stack>
                    </Grid>
                  </Grid>
                ))}
              </Grid>

            }
          </>
        ]
      } else if (categoryData?.category == "FERC") {
        //alert("coming")
        //if(openFercTable) {
        return [
          <>
            <Grid item>
              <Typography
                sx={{
                  fontSize: "12px",
                  fontWeight: "700",
                  display: "inline-flex",  // Align text and icon horizontally
                  alignItems: "center",    // Vertically align the icon with the text
                }}
              >
                FERC Information
                {/* <InfoOutlined
              sx={{

                fontSize: "15px",
                //color: "#000000",
                //alignItems: "flex-end",
              }}
              onClick={OpenToolTipIconButton}
            /> */}
              </Typography>
            </Grid>
            <Grid item>
              <ReusableTable
                isLoading={isLoading}
                width="100%"
                // title={"General Ledger Master List (" + rows.length + ")"}
                //fercInformationColumns
                rows={fercRows}
                columns={columnsforFERC}
                //pageSize={10}
                getRowIdValue={"id"}
                //hideFooter={true}
              // checkboxSelection={false}
              // disableSelectionOnClick={true}
              // status_onRowSingleClick={true}
              // stopPropagation_Column={"action"}
              // status_onRowDoubleClick={true}
              />
            </Grid>

          </>
        ]
        //}
      }
    });

  console.log("tabcontents", tabContents);

  const handleGeneralLedgerSubmitChange = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `General Ledger Submitted for Approval with ID GLFS0NEW${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Approve");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting General Ledger");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/alter/changeGeneralLedgerApprovalSubmit`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleGeneralLedgerSubmitExtend = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setsuccessMsg(true);
        setMessageDialogMessage(`Request has been Submitted for Review`);
        setMessageDialogSeverity("success");
      } else {
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting General Ledger");
        setMessageDialogSeverity("danger");
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/alter/extendGeneralLedgerApprovalSubmit`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const handleGeneralLedgerSubmitCreate = (button) => {
    setBlurLoading(true)
    const hSuccess = (data) => {
      setBlurLoading(false)
      setIsLoading(false);
      if (data.statusCode === 200) {
        setMessageDialogMessage(
          `${button.MDG_DYN_BTN_SNACKBAR_MSG} with ID GLFS0NEW${data.body} `
        );
        setMessageDialogSeverity("success");
        setsuccessMsg(true);
        setBlurLoading(false);
        distinctArtifactId.map((artifactId) => {
          console.log(artifactId, "artifactId=====")
          const secondApiPayload = {
            artifactId: artifactId,
            createdBy: userData?.emailId,
            artifactType: "GeneralLedger",
            requestId: `GLFS0NEW${data?.body}`,
          };
          const secondApiSuccess = (secondApiData) => {
            console.log("Second API success", secondApiData);
            // Handle success for the second API if needed
            //setMessageDialogMessage('')
          };

          const secondApiError = (secondApiError) => {
            console.error("Second API error", secondApiError);
            // Handle error for the second API if needed
          };
          // {requestId&&
          doAjax(
            `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
            "post",
            secondApiSuccess,
            secondApiError,
            secondApiPayload
          );

        })

        handleSnackBarOpen();

      } else {
        setMessageDialogSeverity("error");
        setMessageDialogMessage(
          "Failed Submitting General Ledger"
        );
        setsuccessMsg(true);
        setBlurLoading(false);
        handleSnackBarOpen();
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error, "error")
      // setMessageDialogSeverity("error");
      // // setMessageDialogMessage(
      // //   "Failed Submitting General Ledger"
      // // );
      // setMessageDialogMessage(`Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>.`);
      // setsuccessMsg(true);
      // setBlurLoading(false);
      // handleSnackBarOpen();
    };
    doAjax(
      `/${destination_GeneralLedger}/alter/generalLedgerApprovalSubmit`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleGeneralLedgerReviewExtend = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Extend");
        setMessageDialogMessage(
          `General Ledger Submitted For Review with ID GLSE${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
        console.log(distinctArtifactId, "distinctArtifactId========")
        distinctArtifactId.map((artifactId) => {
          console.log(artifactId, "artifactId====+++=")
          const secondApiPayload = {
            artifactId: artifactId,
            createdBy: userData?.emailId,
            artifactType: "GeneralLedger",
            requestId: `GLSE${data?.body}`,
          };
          const secondApiSuccess = (secondApiData) => {
            console.log("Second API success", secondApiData);
            // Handle success for the second API if needed
          };

          const secondApiError = (secondApiError) => {
            console.error("Second API error", secondApiError);
            // Handle error for the second API if needed
          };
          // {requestId&&
          doAjax(
            `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
            "post",
            secondApiSuccess,
            secondApiError,
            secondApiPayload
          );
        })

      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting General Ledger");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/alter/extendGeneralLedgerSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleGeneralLedgerReviewChange = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `General Ledger Submitted For Review with ID GLSC${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
        const secondApiPayload = {
          artifactId: glNumber,
          createdBy: userData?.emailId,
          artifactType: "GeneralLedger",
          requestId: `GLSC${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting General Ledger");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/alter/changeGeneralLedgerSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleGeneralLedgerReviewCreate = (button) => {
    payload.Testrun = false
    setBlurLoading(true)
    const hSuccess = (data) => {
      setBlurLoading(false)
      setValidateFlag(true)
      //setIsLoading(false);
      if (data.statusCode === 200) {
        setMessageDialogMessage(button?.MDG_DYN_BTN_SNACKBAR_MSG ?
          `${button?.MDG_DYN_BTN_SNACKBAR_MSG} with ID GLFS0NEW${data.body} `
          : `Request has been submitted for review  with ID GLFS0NEW${data.body} `

        );
        setMessageDialogSeverity("success");
        setsuccessMsg(true);
        setBlurLoading(false);
        setValidateFlag(false)
        handleSnackBarOpen();
        distinctArtifactId.map((artifactId) => {
          console.log(artifactId, "artifactId=====")
          const secondApiPayload = {
            artifactId: artifactId,
            createdBy: userData?.emailId,
            artifactType: "GeneralLedger",
            requestId: `GLFS0NEW${data?.body}`,
          };
          const secondApiSuccess = (secondApiData) => {
            console.log("Second API success", secondApiData);
            // Handle success for the second API if needed
          };

          const secondApiError = (secondApiError) => {
            console.error("Second API error", secondApiError);
            // Handle error for the second API if needed
          };
          // {requestId&&
          doAjax(
            `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
            "post",
            secondApiSuccess,
            secondApiError,
            secondApiPayload
          );

        })

        setTimeout(() => {
          navigate("/masterDataCockpitNew/generalLedger");
        }, 2000);


      } else {
        setMessageDialogSeverity("error");
        setMessageDialogMessage(
          "Failed Submitting  General ledger"
        );
        setsuccessMsg(true);
        setBlurLoading(false);
        handleSnackBarOpen();
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
      // setMessageDialogSeverity("error");
      // // setMessageDialogMessage(
      // //   "Failed Submitting General Ledger"
      // // );
      // setMessageDialogMessage(`Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>.`);
      // setsuccessMsg(true);
      // setBlurLoading(false);
      // handleSnackBarOpen();
    };
    doAjax(
      `/${destination_GeneralLedger}/alter/generalLedgerSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const handleCostCenterCorrectionChange = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `General Ledger submitted for Correction with ID GLSC${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting for Correction");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/alter/changeGeneralLedgerSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCostCenterCorrectionCreate = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `General Ledger submitted for Correction with ID NCR${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting for Correction");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/alter/costCenterSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleGeneralLedgerApproveChange = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 201) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(`${data.message}`);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Approving General Ledger");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/alter/changeGeneralLedgerApproved`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleGeneralLedgerApproveExtend = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 201) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(`${data.message}`);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Approving General Ledger");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/alter/extendGeneralLedgerApproved`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const handleAttachment = async () => {
    const rowData = {
      id: taskData?.body?.id ? taskData?.body?.id : "",
      requestId: taskRowDetails?.requestId,
      glAccount: taskData?.body?.glAccount ? taskData?.body?.glAccount : "",
      generalLedgerId: taskData?.body?.id ? taskData?.body?.id : "",
      compCode: taskData?.body?.compCode,
      chartOfAccount: taskData?.body?.coa,
      reqStatus: "Syndicated In SAP",
      requestType: taskRowDetails?.requestType,
      AccountType: taskData?.body?.AccountType ? taskData?.body?.AccountType : "",
      AccountGroup: taskData?.body?.AccountGroup ? taskData?.body?.AccountGroup : "",
      createdBy: taskRowDetails?.createdBy ?? "",
      createdOn: taskRowDetails?.createdOn ?? "",

    };

    const pdfBlob = await exportFileAsPdf(rowData, appSettings);
    console.log(pdfBlob, "pdfBlob");

    const formData = new FormData();
    formData.append('files', pdfBlob, `${rowData?.requestId}_${rowData?.glAccount}.pdf`); // Append the blob as a file with a name
    formData.append('glAccount', rowData?.glAccount);
    formData.append('compCode', rowData?.compCode);

    const hSuccess = (data) => {
      if (data?.statusCode === 201) {
        console.log(data?.message);
      }
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger}/alter/uploadDocumentToSAP`,
      "postblobfile",
      hSuccess,
      hError,
      formData // Pass the formData directly
    );

    const docPayload = new FormData();
    docPayload.append('files', pdfBlob, `${rowData?.requestId}_${rowData?.glAccount}.pdf`);
    console.log(glNumber, "glNumber");
    var doc = {
      artifactId: `Consolidated PDF_${glNumber}` ?? "",
      createdBy: userData?.emailId ?? "",
      artifactType: "GeneralLedger",
      attachmentType: "Consolidated PDF",
      requestId: taskRowDetails?.requestId
        ? taskRowDetails?.requestId
        : "",
      object: taskData?.body?.glAccount ? taskData?.body?.glAccount : "",
    };
    docPayload.append("doc", JSON.stringify(doc));

    const firstApiSuccess = (data) => {
      console.log(data, "First Api Success");
      const updateDocPayload = {
        artifactId: `Consolidated PDF_${glNumber}` ?? "",
        createdBy: userData?.emailId ?? "",
        artifactType: "GeneralLedger",
        requestId: taskRowDetails?.requestId,
        attachmentType: "Consolidated PDF",
      };

      const apiSuccess = (data) => {
        console.log(data, "Success Updating Request Id");
      };

      const apiError = (data) => {
        console.error("Error Updating Request Id", data);
      };

      doAjax(
        `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
        "post",
        apiSuccess,
        apiError,
        updateDocPayload
      );
    };

    doAjax(`/${destination_DocumentManagement}/documentManagement/uploadDocument`,
      'postformdata',
      firstApiSuccess,
      () => { },
      docPayload)
    // });
  };

  const handleGeneralLedgerApproveCreate = (button) => {
    //console.log("fffffffffff");
    setBlurLoading(true)
    setIsLoading(false);
    const hSuccess = (data) => {
      console.log("fffffffffff");
      setIsLoading(false);

      if (data.statusCode === 201) {
        setValidateFlag(true)
        setMessageDialogMessage(`${button.MDG_DYN_BTN_SNACKBAR_MSG}`);
        setMessageDialogSeverity("success");
        setsuccessMsg(true);
        setBlurLoading(false);
        handleSnackBarOpen();
        handleAttachment();
        distinctArtifactId.map((artifactId) => {
          console.log(artifactId, "artifactId=====");
          const secondApiPayload = {
            artifactId: artifactId,
            createdBy: userData?.emailId,
            artifactType: "GeneralLedger",
            //requestId: `GLFS0NEW${data?.body}`,
            requestId: taskRowDetails?.requestId,

          };
          const secondApiSuccess = (secondApiData) => {
            console.log("Second API success", secondApiData);
            // Handle success for the second API if needed
          };

          const secondApiError = (secondApiError) => {
            console.error("Second API error", secondApiError);
            // Handle error for the second API if needed
          };
          // {requestId&&
          doAjax(
            `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
            "post",
            secondApiSuccess,
            secondApiError,
            secondApiPayload
          );
        });
        setTimeout(() => {
          navigate("/masterDataCockpitNew/generalLedger");
        }, 2000);
      } else {
        setMessageDialogSeverity("error");

        if (data.body?.message?.length > 0) {
          const messageList = (
            <ul>
              {data.body.message.map((msg, index) => (
                <li key={index}>{msg}</li>
              ))}
            </ul>
          );
          setMessageDialogMessage(messageList);
        }
        else {
          setMessageDialogMessage(data.body?.value || data.message);
        }
        setTextInput(false);


        setsuccessMsg(true);
        setBlurLoading(false);
        handleSnackBarOpen();
      }
      // handleClose();
    };
    // if (data.statusCode === 201) {
    //   setMessageDialogMessage(`${button.MDG_DYN_BTN_SNACKBAR_MSG}`);
    //   setMessageDialogSeverity("success");
    //   setsuccessMsg(true);
    //   setBlurLoading(false);
    //   handleSnackBarOpen();
    // } else {
    //   setMessageDialogSeverity("error");
    //   setMessageDialogMessage(
    //     "Failed Submitting Gl Create"
    //   );
    //   setsuccessMsg(true);
    //   setBlurLoading(false);
    //   handleSnackBarOpen();
    // }
    const hError = (error) => {
      //console.log(error);
      // setMessageDialogSeverity("error");
      // // setMessageDialogMessage(
      // //   "Failed Submitting General Ledger"
      // // );
      // setMessageDialogMessage(`Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>.`);
      // setsuccessMsg(true);
      // setBlurLoading(false);
      // handleSnackBarOpen();
    };
    doAjax(
      `/${destination_GeneralLedger}/alter/createGeneralLedgerApproved`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCostCenterRereview = () => {
    console.log("apicallllllllll");
    const hSuccess = (data) => {
      setMessageDialogMessage(
        `Create id generated for Data Owners CMS${data.body}`
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/alter/costCenterSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleOpenCorrectionDialog = () => {
    setOpenCorrectionDialog(true);
  };
  const handleCorrectionDialogClose = () => {
    setRemarks("");
    setOpenCorrectionDialog(false);
  };
  const handleRemarks = (e) => {
    const newValue = e.target.value;
    if (newValue.length > 0 && newValue[0] === " ") {
      setRemarks(newValue.trimStart());
    } else {
      //let costCenterValue = e.target.value;
      let remarksUpperCase = newValue;
      setRemarks(remarksUpperCase);
    }
  };

  const handleWarningDialogClose = () => {
    setOpenMessageDialog(false);
  };


  const onGLSaveAsDraft = () => {
    handleDraftDialogClose();
    onSaveAsDraft();
  };
  const handleDraftDialog = () => {
    setTestrunStatus(false);
    setOpenDraftDialog(true);
  };
  const handleDraftDialogClose = () => {
    setTestrunStatus(true);
    setOpenDraftDialog(false);
  };

  const OpenReusabletableForCreateDetails = () => {
    // Example usage
    //const accountType = 'X'; // You can dynamically assign this value
    const contentForDetailsField = getDialogContentByAccountType(taskData?.body?.AccountType ? taskData?.body?.AccountType : generalLedgerRowData?.glAccountType ? generalLedgerRowData?.glAccountType : "N");
    setDialogContentForFMDDetails(contentForDetailsField);
    console.log(contentForDetailsField, "contentForDetailsField")

    // let columnjsonArr=[]
    // let columnjsonhash={}
    // columnjsonhash["id"] ='1'
    // columnjsonhash["sortKey"]=singleGLPayload?.sortKey ? singleGLPayload?.sortKey :''
    // columnjsonhash["fieldStatusGroup"] =singleGLPayload?.FieldStatusGroup ?? 'x'
    // columnjsonhash["groupCOA"] =singleGLPayload?.groupCOA ?? ''
    // columnjsonhash["coa"] =generalLedgerRowData?.chartOfAccount
    // ? generalLedgerRowData?.chartOfAccount
    // : iDs?.ChartOfAccount
    //   ? iDs?.ChartOfAccount
    //   : ""
    // columnjsonhash["country"] =singleGLPayload?.country ?? ''
    // columnjsonhash["controllingArea"] =singleGLPayload?.controllingArea
    // columnjsonhash["createdOn"] =singleGLPayload?.createdOn ?? ''
    // columnjsonhash["createdBy"] =singleGLPayload?.createdBy ?? ''
    // columnjsonArr.push(columnjsonhash)

    // console.log(columnjsonArr,"columnjsonArr")


    // setCreatedRowDetailsField(columnjsonArr)
    // setCreatedDetailsfield(columnsForDetailsField)
    setOpenCreateDetails(true)
  }
  useEffect(() => {

  }, [createdDetailsField])

  // useEffect (()=>{
  //   if(activeStep == 4){
  //     setOpenDialogForFerc(true)
  //   }
  // },[activeStep])


  function getDialogContentByAccountType(accountType) {
    switch (accountType) {
      case 'X':
        return (
          <Stack spacing={2} sx={{ padding: '10px' }}>
            <Stack direction="row">
              <Typography variant="body2" color="#777" sx={{ flex: 0.5, textAlign: 'left' }}>
                Sort Key
              </Typography>
              <Typography variant="body2" fontWeight="bold" sx={{ flex: 0.5, textAlign: 'left' }}>
                : {singleGLPayload?.SortKey ? singleGLPayload?.SortKey : "NA"}
              </Typography>
            </Stack>
            <Stack direction="row">
              <Typography variant="body2" color="#777" sx={{ flex: 0.5, textAlign: 'left' }}>
                Field Status Group
              </Typography>
              <Typography variant="body2" fontWeight="bold" sx={{ flex: 0.5, textAlign: 'left' }}>
                : {singleGLPayload?.FieldStatusGroup
                  ? singleGLPayload?.FieldStatusGroup
                  : getValueForFieldNameForPayload(
                    iDs?.viewData["Create/Bank/Interest"]?.["Control of Document creation in Company Code"],
                    "Field Status Group"
                  ) ? getValueForFieldNameForPayload(
                    iDs?.viewData["Create/Bank/Interest"]?.["Control of Document creation in Company Code"],
                    "Field Status Group"
                  ) : "NA"}
              </Typography>
            </Stack>
            <Stack direction="row">
              <Typography variant="body2" color="#777" sx={{ flex: 0.5, textAlign: 'left' }}>
                Group COA
              </Typography>
              <Typography variant="body2" fontWeight="bold" sx={{ flex: 0.5, textAlign: 'left' }}>
                : {singleGLPayload?.groupCoa ?? 'NA'}
              </Typography>
            </Stack>

            <Stack direction="row">
              <Typography variant="body2" color="#777" sx={{ flex: 0.5, textAlign: 'left' }}>
                COA
              </Typography>
              <Typography variant="body2" fontWeight="bold" sx={{ flex: 0.5, textAlign: 'left' }}>
                : {generalLedgerRowData?.chartOfAccount
                  ? generalLedgerRowData?.chartOfAccount
                  : iDs?.ChartOfAccount
                    ? iDs?.ChartOfAccount
                    : "NA"}
              </Typography>
            </Stack>
            <Stack direction="row">
              <Typography variant="body2" color="#777" sx={{ flex: 0.5, textAlign: 'left' }}>
                Country
              </Typography>
              <Typography variant="body2" fontWeight="bold" sx={{ flex: 0.5, textAlign: 'left' }}>
                : {singleGLPayload?.Country ? singleGLPayload?.Country : "NA"}
              </Typography>
            </Stack>
            <Stack direction="row">
              <Typography variant="body2" color="#777" sx={{ flex: 0.5, textAlign: 'left' }}>
                Controlling Area
              </Typography>
              <Typography variant="body2" fontWeight="bold" sx={{ flex: 0.5, textAlign: 'left' }}>
                : {singleGLPayload?.controllingarea ? singleGLPayload?.controllingarea : "NA"}
              </Typography>
            </Stack>
            <Stack direction="row">
              <Typography variant="body2" color="#777" sx={{ flex: 0.5, textAlign: 'left' }}>
                Created On
              </Typography>
              <Typography variant="body2" fontWeight="bold" sx={{ flex: 0.5, textAlign: 'left' }}>
                : {singleGLPayload?.createdOn ? singleGLPayload?.createdOn : "NA"}
              </Typography>
            </Stack>
            <Stack direction="row">
              <Typography variant="body2" color="#777" sx={{ flex: 0.5, textAlign: 'left' }}>
                Created By
              </Typography>
              <Typography variant="body2" fontWeight="bold" sx={{ flex: 0.5, textAlign: 'left' }}>
                : {singleGLPayload?.createdBy ? singleGLPayload?.createdBy : 'NA'}
              </Typography>
            </Stack>
          </Stack>
        );
      case 'P':
        return (
          <Stack spacing={2} sx={{ padding: '10px' }}>
            <Stack direction="row">
              <Typography variant="body2" color="#777" sx={{ flex: 0.5, textAlign: 'left' }}>
                COA
              </Typography>
              <Typography variant="body2" fontWeight="bold" sx={{ flex: 0.5, textAlign: 'left' }}>
                : {generalLedgerRowData?.chartOfAccount
                  ? generalLedgerRowData?.chartOfAccount
                  : iDs?.ChartOfAccount
                    ? iDs?.ChartOfAccount
                    : "NA"}
              </Typography>
            </Stack>
            <Stack direction="row">
              <Typography variant="body2" color="#777" sx={{ flex: 0.5, textAlign: 'left' }}>
                Controlling Area
              </Typography>
              <Typography variant="body2" fontWeight="bold" sx={{ flex: 0.5, textAlign: 'left' }}>
                : {singleGLPayload?.controllingarea ? singleGLPayload?.controllingarea : "NA"}
              </Typography>
            </Stack>
            <Stack direction="row">
              <Typography variant="body2" color="#777" sx={{ flex: 0.5, textAlign: 'left' }}>
                Created On
              </Typography>
              <Typography variant="body2" fontWeight="bold" sx={{ flex: 0.5, textAlign: 'left' }}>
                : {singleGLPayload?.createdOn ? singleGLPayload?.createdOn : "NA"}
              </Typography>
            </Stack>
            <Stack direction="row">
              <Typography variant="body2" color="#777" sx={{ flex: 0.5, textAlign: 'left' }}>
                Created By
              </Typography>
              <Typography variant="body2" fontWeight="bold" sx={{ flex: 0.5, textAlign: 'left' }}>
                : {singleGLPayload?.createdBy ? singleGLPayload?.createdBy : "NA"}
              </Typography>
            </Stack>
          </Stack>
        );
      case 'S':
        return (
          <Stack spacing={2} sx={{ padding: '10px' }}>
            <Stack direction="row">
              <Typography variant="body2" color="#777" sx={{ flex: 0.5, textAlign: 'left' }}>
                Field Status Group
              </Typography>
              <Typography variant="body2" fontWeight="bold" sx={{ flex: 0.5, textAlign: 'left' }}>
                : {singleGLPayload?.FieldStatusGroup
                  ? singleGLPayload?.FieldStatusGroup
                  : getValueForFieldNameForPayload(
                    iDs?.viewData["Create/Bank/Interest"]?.["Control of Document creation in Company Code"],
                    "Field Status Group"
                  ) ? getValueForFieldNameForPayload(
                    iDs?.viewData["Create/Bank/Interest"]?.["Control of Document creation in Company Code"],
                    "Field Status Group"
                  ) : "NA"}
              </Typography>
            </Stack>
            <Stack direction="row">
              <Typography variant="body2" color="#777" sx={{ flex: 0.5, textAlign: 'left' }}>
                Group COA
              </Typography>
              <Typography variant="body2" fontWeight="bold" sx={{ flex: 0.5, textAlign: 'left' }}>
                : {singleGLPayload?.groupCoa ?? 'NA'}
              </Typography>
            </Stack>
            <Stack direction="row">
              <Typography variant="body2" color="#777" sx={{ flex: 0.5, textAlign: 'left' }}>
                COA
              </Typography>
              <Typography variant="body2" fontWeight="bold" sx={{ flex: 0.5, textAlign: 'left' }}>
                : {generalLedgerRowData?.chartOfAccount
                  ? generalLedgerRowData?.chartOfAccount
                  : iDs?.ChartOfAccount
                    ? iDs?.ChartOfAccount
                    : "NA"}
              </Typography>
            </Stack>
            <Stack direction="row">
              <Typography variant="body2" color="#777" sx={{ flex: 0.5, textAlign: 'left' }}>
                Country
              </Typography>
              <Typography variant="body2" fontWeight="bold" sx={{ flex: 0.5, textAlign: 'left' }}>
                : {singleGLPayload?.Country ? singleGLPayload?.Country : "NA"}
              </Typography>
            </Stack>
            <Stack direction="row">
              <Typography variant="body2" color="#777" sx={{ flex: 0.5, textAlign: 'left' }}>
                Controlling Area
              </Typography>
              <Typography variant="body2" fontWeight="bold" sx={{ flex: 0.5, textAlign: 'left' }}>
                : {singleGLPayload?.controllingarea ? singleGLPayload?.controllingarea : "NA"}
              </Typography>
            </Stack>
            <Stack direction="row">
              <Typography variant="body2" color="#777" sx={{ flex: 0.5, textAlign: 'left' }}>
                Created On
              </Typography>
              <Typography variant="body2" fontWeight="bold" sx={{ flex: 0.5, textAlign: 'left' }}>
                : {singleGLPayload?.createdOn ? singleGLPayload?.createdOn : "NA"}
              </Typography>
            </Stack>
            <Stack direction="row">
              <Typography variant="body2" color="#777" sx={{ flex: 0.5, textAlign: 'left' }}>
                Created By
              </Typography>
              <Typography variant="body2" fontWeight="bold" sx={{ flex: 0.5, textAlign: 'left' }}>
                : {singleGLPayload?.createdBy ? singleGLPayload?.createdBy : "NA"}
              </Typography>
            </Stack>
          </Stack>
        );
      case 'N':
        return (
          <Stack spacing={3} sx={{ padding: '10px' }} >
            <Stack direction="row">
              <Typography variant="body2" color="#777" sx={{ flex: 0.5, textAlign: 'left' }}>
                COA
              </Typography>
              <Typography variant="body2" fontWeight="bold" sx={{ flex: 0.5, textAlign: 'left' }}>
                : {generalLedgerRowData?.chartOfAccount
                  ? generalLedgerRowData?.chartOfAccount
                  : iDs?.ChartOfAccount
                    ? iDs?.ChartOfAccount
                    : "NA"}
              </Typography>
            </Stack>
            <Stack direction="row">
              <Typography variant="body2" color="#777" sx={{ flex: 0.5, textAlign: 'left' }}>
                Country
              </Typography>
              <Typography variant="body2" fontWeight="bold" sx={{ flex: 0.5, textAlign: 'left' }}>
                : {singleGLPayload?.Country ? singleGLPayload?.Country : "NA"}
              </Typography>
            </Stack>
            <Stack direction="row">
              <Typography variant="body2" color="#777" sx={{ flex: 0.5, textAlign: 'left' }}>
                Controlling Area
              </Typography>
              <Typography variant="body2" fontWeight="bold" sx={{ flex: 0.5, textAlign: 'left' }}>
                : {singleGLPayload?.controllingarea ? singleGLPayload?.controllingarea : "NA"}
              </Typography>
            </Stack>
            <Stack direction="row">
              <Typography variant="body2" color="#777" sx={{ flex: 0.5, textAlign: 'left' }}>
                Created On
              </Typography>
              <Typography variant="body2" fontWeight="bold" sx={{ flex: 0.5, textAlign: 'left' }}>
                : {singleGLPayload?.createdOn ? singleGLPayload?.createdOn : "NA"}
              </Typography>
            </Stack>
            <Stack direction="row">
              <Typography variant="body2" color="#777" sx={{ flex: 0.5, textAlign: 'left' }}>
                Created By
              </Typography>
              <Typography variant="body2" fontWeight="bold" sx={{ flex: 0.5, textAlign: 'left' }}>
                : {singleGLPayload?.createdBy ? singleGLPayload?.createdBy : "NA"}
              </Typography>
            </Stack>
          </Stack>
        );
      default:
        return <Typography>No data available</Typography>;
    }
  }

  const handleCloseCreateDetails = () => {
    setOpenCreateDetails(false)
  }

  const functions_ExportAsExcelDetail = {
    convertJsonToExcel: () => {
      let excelColumns = [];
      createdDetailsField.forEach((item) => {
        if (item.headerName.toLowerCase() !== "action" && !item.hide) {
          excelColumns.push({ header: item.headerName, key: item.field });
        }
      });
      saveExcel({
        fileName: `FMD Specific Fields View List-${moment(presentDate).format("DD-MMM-YYYY")}`,
        columns: excelColumns,
        rows: createdRowDetailsField,
      })
    },
    button: () => {
      return (
        <Button
          sx={{
            textTransform: "capitalize",
            position: "absolute",
            right: 0,
            top: 0,
          }}
          onClick={() => functions_ExportAsExcelDetail.convertJsonToExcel()}
        >
          Download
        </Button>
      );
    },
  };

  const handleMessageDialogClickOpenshort = () => {
    setOpenMessageDialogshort(true);
  };

  const handleMessageDialogCloseshort = () => {
    setOpenMessageDialogshort(false);
  };
  const handleMessageDialogCloseValidate = () => {
    setOpenMessageDialogForValidate(false);
  };

  const handleProceedbuttonForGLLong = () => {
    handleWarningDialogCloseshort()
    if (saveAsDraftState) {
      validateForGLDescriptionDuplicateCheckFromSaveAsDraft()
    } else {
      validateForGLDescriptionDuplicateCheck()
    }

  }

  const handleSnackBarOpenValidate = () => {
    setopenSnackbarValidate(true);
  };
  const handleSnackBarCloseValidate = () => {
    setMessageDialogSeverity(false)
    setopenSnackbarValidate(false);
    setMessageDialogMessage("");
  };

  // const handleSnackBarCloseValidate = () => {


  //   if (validateFlag) {
  //   setopenSnackbarValidate(false);
  //   setValidateFlag(false);
  //   } else {
  //   if(Object.keys(taskRowDetails)?.length !== 0){
  //     setMessageDialogSeverity(false)
  //     setopenSnackbarValidate(false);
  //   //setMessageDialogTitle("Success");
  //    setMessageDialogMessage("")
  //    //navigate("/masterDataCockpitNew/generalLedger");
  //    navigate("/workspace/MyTasks");
  //   }else{
  //     setMessageDialogSeverity(false)
  //     setopenSnackbarValidate(false);
  //   //setMessageDialogTitle("Success");
  //    setMessageDialogMessage("")
  //    navigate("/requestBench");
  //   }
  //   }
  // };



  const handleWarningDialogCloseValidate = () => {
    setOpenMessageDialogForValidate(false);
  };
  const handleWarningDialogCloseshort = () => {
    setOpenMessageDialogshort(false);
  };

  const handleProceedbuttonForValidation = () => {
    handleWarningDialogCloseValidate()
    if (saveAsDraftState) {
      validateForGeneralLedgerAfterAllCheckFromSaveAsDraft()
    } else {
      validateForGeneralLedgerAfterAllCheck()
    }

  }

  const handleMessageDialogCloseShortLongChange = () => {
    setOpenMessageDialogFor5161ShortLongChange(false);
  };

  const handleProceedbuttonForShortLongChange = () => {
    proceedWithNextStep()
  }
  const proceedWithNextStep = () => {
    // Proceed with validation checks
    setOpenMessageDialogFor5161ShortLongChange(true)
    const isValidation = handleCheckValidationError();
    const isValidationGI = handleCheckValidationErrorGI();

    if (isValidation && isValidationGI) {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
      // dispatch(clearGeneralLedger());
    } else {
      // Show validation snackbar
      handleSnackBarOpenValidation();
    }
  };


  console.log("factorsarray", factorsArray);
  return (
    <>
      {isLoading === true ? (
        <LoadingComponent />
      ) : (
        <div style={{ backgroundColor: "#FAFCFF" }}>
          {/* <ReusableDialog
            dialogState={openMessageDialog}
            openReusableDialog={handleMessageDialogClickOpen}
            closeReusableDialog={handleMessageDialogClose}
            dialogTitle={messageDialogTitle}
            dialogMessage={messageDialogMessage}
            handleDialogConfirm={handleMessageDialogClose}
            dialogOkText={"OK"}
            showExtraButton={handleExtrabutton}
            showCancelButton={true}
            // handleExtraButton={handleMessageDialogNavigate}
            dialogSeverity={messageDialogSeverity}
            handleDialogReject={handleWarningDialogClose}
            handleExtraText={handleExtraText}
            handleExtraButton={handleProceedbutton}
          /> */}

          <Dialog
            open={openMessageDialogForTradingpresent}
          //onClose={handleCloseDialogFerc}
          >
            <DialogTitle>{"Trading Alert Message"}</DialogTitle>
            <DialogContent>

              {messageDialogMessage}

            </DialogContent>
            <DialogActions>
              <Button onClick={handleMessageDialogCloseOpenForTradingPresent} color="primary">
                OK
              </Button>
            </DialogActions>
          </Dialog>

          <Dialog
            open={openMessageDialogForTrading}
          //onClose={handleCloseDialogFerc}
          >
            <DialogTitle>{"Trading Message"}</DialogTitle>
            <DialogContent>
              <DialogContentText>
                Please update description to include intercompany or affiliate or update GL Trading Partner selection.
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleMessageDialogCloseOpenForTrading} color="primary">
                OK
              </Button>
            </DialogActions>
          </Dialog>

          <ReusableDialog
            dialogState={openMessageDialog}
            openReusableDialog={handleMessageDialogClickOpen}
            closeReusableDialog={handleMessageDialogClose}
            dialogTitle={messageDialogTitle}
            dialogMessage={messageDialogMessage}
            handleDialogConfirm={handleProceedbuttonForGLLong}
            dialogOkText={"Ok"}
            //showOkButton={false}
            // showExtraButton={handleExtrabutton}
            showCancelButton={true}
            // handleExtraButton={handleMessageDialogNavigate}
            dialogSeverity={messageDialogSeverity}
            handleDialogReject={handleWarningDialogClose}
          //handleExtraText={handleExtraText}
          //handleExtraButton={handleProceedbuttonForGLLong}
          />
          <ReusableDialog
            dialogState={openMessageDialogForValidate}
            openReusableDialog={handleMessageDialogClickOpenValidate}
            closeReusableDialog={handleMessageDialogCloseValidate}
            dialogTitle={messageDialogTitle}
            dialogMessage={messageDialogMessage}
            handleDialogConfirm={handleProceedbuttonForValidation}
            dialogOkText={"OK"}
            //showOkButton={false}
            //showExtraButton={handleExtrabutton}
            showCancelButton={true}
            // handleExtraButton={handleMessageDialogNavigate}
            dialogSeverity={messageDialogSeverity}
            handleDialogReject={handleMessageDialogCloseValidate}
          //handleExtraText={handleExtraText}
          //handleExtraButton={handleProceedbuttonForValidation}
          />

          <ReusableDialog
            dialogState={openMessageDialogFor5161ShortLongChange}
            openReusableDialog={handleMessageDialogClickOpenShortlongChange}
            closeReusableDialog={handleMessageDialogCloseShortLongChange}
            handleDialogConfirm={handleProceedbuttonForShortLongChange}
            dialogOkText={"Ok"}
            dialogTitle={messageDialogTitle}
            dialogMessage={messageDialogMessage}


            //showOkButton={true}
            //showExtraButton={handleExtrabutton}
            showCancelButton={true}

            dialogSeverity={messageDialogSeverity}
            handleDialogReject={handleMessageDialogCloseShortLongChange}
          //handleExtraText={handleExtraText}
          //handleExtraButton={handleProceedbuttonForShortLongChange}
          />

          <ReusableDialog
            dialogState={openMessageDialogshort}
            openReusableDialog={handleMessageDialogClickOpenshort}
            closeReusableDialog={handleMessageDialogCloseshort}
            dialogTitle={messageDialogTitle}
            dialogMessage={messageDialogMessage}
            handleDialogConfirm={handleProceedbuttonForGLLong}
            dialogOkText={"Ok"}
            //showOkButton={false}
            // showExtraButton={handleExtrabutton}
            showCancelButton={true}
            // handleExtraButton={handleMessageDialogNavigate}
            dialogSeverity={messageDialogSeverity}
            handleDialogReject={handleWarningDialogCloseshort}
          //handleExtraText={handleExtraText}
          //handleExtraButton={handleProceedbuttonForGLLong}
          />
          <ReusableDialog
            dialogState={openMessageDialogForValidate}
            openReusableDialog={handleMessageDialogClickOpenValidate}
            closeReusableDialog={handleMessageDialogCloseValidate}
            dialogTitle={messageDialogTitle}
            dialogMessage={messageDialogMessage}
            handleDialogConfirm={handleProceedbuttonForValidation}
            dialogOkText={"OK"}
            //showOkButton={false}
            //showExtraButton={handleExtrabutton}
            showCancelButton={true}
            // handleExtraButton={handleMessageDialogNavigate}
            dialogSeverity={messageDialogSeverity}
            handleDialogReject={handleMessageDialogCloseValidate}
          //handleExtraText={handleExtraText}
          //handleExtraButton={handleProceedbuttonForValidation}
          />

          <ReusableDialog
            dialogState={openMessageDialog}
            openReusableDialog={handleMessageDialogClickOpen}
            closeReusableDialog={handleMessageDialogClose}
            dialogTitle={dialogTitle}
            dialogMessage={messageDialogMessage}
            handleDialogConfirm={handleDialogConfirm}
            // showOkButton={true}
            dialogOkText={"Ok"}
            dialogSeverity={messageDialogSeverity}
            showCancelButton={true}
            showInputText={inputText}
            inputText={userInput}
            setInputText={setUserInput}
            mandatoryTextInput={isMandatory}
            remarksError={remarksError}
          />

          {/* <ReusableDialog
            dialogState={openMessageDialog}
            openReusableDialog={handleMessageDialogClickOpen}
            closeReusableDialog={handleMessageDialogClose}
            dialogTitle={dialogTitle}
            // dialogMessage={messageDialogMessage}
            handleDialogConfirm={handleDialogConfirm}
            showOkButton={isMandatory}
            dialogOkText={"Proceed"}
            showCancelButton={true}
            showInputText={isMandatory}
            dialogSeverity={messageDialogSeverity}
            inputText={userInput}
            setInputText={setUserInput}
          /> */}

          <ReusableSnackBar
            openSnackBar={openSnackbarValidate}
            alertMsg={messageDialogMessage}
            handleSnackBarClose={handleSnackBarCloseValidate}
            alertType="success"
            transition={Zoom}
            snackbarBgColor="#333"
            textColor="white" // Custom text color
          />

          {successMsg && (
            <ReusableSnackBar
              openSnackBar={openSnackbar}
              alertMsg={messageDialogMessage}
              alertType="success" // or "error" based on your condition
              handleSnackBarClose={handleSnackBarClose}
              transition={Zoom}
              snackbarBgColor="#333"
              textColor="white" // Custom text color
            />
          )}
          {showAlert && <Alert severity="warning"
            sx={{ width: '20%', margin: '0 auto' }}
            onClose={handleAlertClose}>
            Input is mandatory</Alert>}
          {formValidationErrorItems.length != 0 && (
            <ReusableSnackBar
              openSnackBar={openSnackbarValidation}
              alertMsg={
                "Please enter the following Field: " +
                formValidationErrorItems.join(", ")
              }
              handleSnackBarClose={handleSnackBarCloseValidation}
              alertType="error"
              transition={Zoom}
              snackbarBgColor="#333"
              textColor="white" // Custom text color
              isLoading={false}
            />
          )}
          {formValidationErrorItemsGI.length != 0 && (
            <ReusableSnackBar
              openSnackBar={openSnackbarValidation}
              alertMsg={
                "Please enter the following Field: " +
                formValidationErrorItemsGI.join(", ")
              }
              handleSnackBarClose={handleSnackBarCloseValidation}
            />
          )}

          <Dialog
            open={openDialogforFerc}
          //onClose={handleCloseDialogFerc}
          >
            <DialogTitle>{"FERC Information"}</DialogTitle>
            <DialogContent>
              <DialogContentText>
                If Expense account, please provide Cost Center or WBS
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleOkFercInfo} color="primary">
                OK
              </Button>
            </DialogActions>
          </Dialog>


          <Dialog
            open={openGlErrorTable}
            fullWidth
            onClose={handleCloseGlErrorTable}
            sx={{
              "&::webkit-scrollbar": {
                width: "1px",
              },
              // paddingBottom:1
            }}
          >
            <DialogTitle
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF40",
                // borderBottom: "1px solid grey",
                display: "flex",
              }}
            >
              <Typography variant="h6" color="red" >
                Error General Ledger
              </Typography>

              <Grid md={1}>
                <Tooltip title="Export Table">
                  <IconButton
                    sx={iconButton_SpacingSmall}
                    onClick={functions_ExportAsExcelErrorValidate.convertJsonToExcel}
                  >
                    <ReusableIcon iconName={"IosShare"} />
                  </IconButton>
                </Tooltip>
                {/* <Typography > */}

                <IconButton
                  sx={{ width: "max-content" }}
                  onClick={handleCloseGlErrorTable}
                  children={<CloseIcon />}
                />
              </Grid>

            </DialogTitle>
            <DialogContent sx={{ padding: ".5rem 1rem" }}>

              <ReusableTable
                width="100%"
                rows={glCreateErrorData}
                columns={glColumnError}
                getRowIdValue={"id"}
                pageSize={10}

              />

            </DialogContent>

            <DialogActions sx={{ display: "flex", justifyContent: "end" }}>

            </DialogActions>
          </Dialog>

          <Dialog
            open={openCreateDetails}
            // fullWidth
            onClose={handleCloseCreateDetails}
            hideBackdrop={false}
            maxWidth="sm"
            elevation={2}
            PaperProps={{
              sx: {
                boxShadow: "none",
                marginBottom: "8px",
                borderRadius: "8px",
                width: "40%",
                maxWidth: "400px",
              },
            }}
          >
            <DialogTitle
              id="mdm-view-dialog"
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF",
                display: "flex",
              }}
            >
              MDM Specific Field View List
            </DialogTitle>
            <DialogContent sx={{ padding: ".5rem 1rem" }}>

              {dialogContentForFMDDetails}

            </DialogContent>

            <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
              <Button onClick={handleCloseCreateDetails} color="primary">
                Close
              </Button>
            </DialogActions>
          </Dialog>

          <Dialog
            open={openCompanyCode}
            fullWidth
            onClose={handleCloseCompanyCode}
            sx={{
              "&::webkit-scrollbar": {
                width: "1px",
              },
              // paddingBottom:1
            }}
          >
            <DialogTitle
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF40",
                // borderBottom: "1px solid grey",
                display: "flex",
              }}
            >
              <Typography variant="h6" >
                List Of Company Code
              </Typography>

              <IconButton
                sx={{ width: "max-content" }}
                onClick={handleCloseCompanyCode}
                children={<CloseIcon />}
              />
            </DialogTitle>
            <DialogContent sx={{ padding: ".5rem 1rem" }}>

              <ReusableTable
                width="100%"
                rows={companyCodeRow}
                columns={[
                  {
                    field: "copyCompanyCode",
                    headerName: "Company Code",
                    editable: false,
                    flex: 1,
                    // width: 100,
                  }, {
                    field: "copyCompanyCodeDesc",
                    headerName: "Company Code Description",
                    editable: false,
                    flex: 1,
                  }]}
                pageSize={10}
                getRowIdValue={"id"}

              />

            </DialogContent>

            <DialogActions sx={{ display: "flex", justifyContent: "end" }}>

            </DialogActions>
          </Dialog>
          <Dialog
            hideBackdrop={false}
            elevation={2}
            PaperProps={{
              sx: { boxShadow: "none" },
            }}
            open={openCorrectionDialog}
            onClose={handleCorrectionDialogClose}
          >
            <DialogTitle
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF40",
                display: "flex",
              }}
            >
              <Typography variant="h6">Remarks</Typography>

              <IconButton
                sx={{ width: "max-content" }}
                onClick={handleCorrectionDialogClose}
                children={<CloseIcon />}
              />
            </DialogTitle>
            <DialogContent sx={{ padding: ".5rem 1rem" }}>
              <Stack>
                <Box sx={{ minWidth: 400 }}>
                  <FormControl sx={{ height: "auto" }} fullWidth>
                    <TextField
                      sx={{ backgroundColor: "#F5F5F5" }}
                      onChange={handleRemarks}
                      inputProps={{ maxLength: 200 }}
                      value={remarks}
                      multiline
                      placeholder={"Enter Remarks for Correction"}
                    ></TextField>
                  </FormControl>
                </Box>
              </Stack>
            </DialogContent>
            <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
              <Button
                sx={{ width: "max-content", textTransform: "capitalize" }}
                onClick={handleCorrectionDialogClose}
              >
                Cancel
              </Button>
              <Button
                className="button_primary--normal"
                type="save"
                onClick={onGeneralLedgerCorrection}
                variant="contained"
              >
                Submit
              </Button>
            </DialogActions>
          </Dialog>

          <Dialog
            hideBackdrop={false}
            elevation={2}
            PaperProps={{
              sx: { boxShadow: "none" },
            }}
            open={openRemarkDialog}
            onClose={handleRemarksDialogClose}
          >
            {/* <Grid
          container
          sx={{ display: "flex", justifyContent: "space-between" }}
        > */}
            {/* <Grid item> */}
            <DialogTitle
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF40",
                // borderBottom: "1px solid grey",
                display: "flex",
              }}
            >
              <Typography variant="h6">Remarks</Typography>

              <IconButton
                sx={{ width: "max-content" }}
                onClick={handleRemarksDialogClose}
                children={<CloseIcon />}
              />
            </DialogTitle>
            {/* </Grid> */}
            {/* </Grid> */}
            <DialogContent sx={{ padding: ".5rem 1rem" }}>
              <Stack>
                <Box sx={{ minWidth: 400 }}>
                  <FormControl sx={{ height: "auto" }} fullWidth>
                    <TextField
                      sx={{ backgroundColor: "#F5F5F5" }}
                      value={remarks}
                      onChange={handleRemarks}
                      inputProps={{ maxLength: 200 }}
                      multiline
                      placeholder={"Enter Remarks"}
                    ></TextField>
                  </FormControl>
                </Box>
              </Stack>
            </DialogContent>
            <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
              <Button
                sx={{ width: "max-content", textTransform: "capitalize" }}
                onClick={handleRemarksDialogClose}
              >
                Cancel
              </Button>
              <Button
                className="button_primary--normal"
                type="save"
                onClick={onGeneralLedgerSubmitRemarks}
                variant="contained"
              >
                Submit
              </Button>
            </DialogActions>
          </Dialog>

          <Dialog
            open={dialogOpen}
            fullWidth
            onClose={handleDialogClose}
            sx={{
              "&::webkit-scrollbar": {
                width: "1px",
              },
              // paddingBottom:1
            }}
          >
            <DialogTitle
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF40",
                // borderBottom: "1px solid grey",
                display: "flex",
              }}
            >
              <Typography variant="h6" color="red">
                Errors
              </Typography>

              <IconButton
                sx={{ width: "max-content" }}
                onClick={handleDialogClose}
                children={<CloseIcon />}
              />
            </DialogTitle>
            <DialogContent sx={{ padding: ".5rem 1rem" }}>
              {/* <Grid container> */}

              {validationRows && (
                <ReusableTable
                  // isLoading={isLoading}
                  width="100%"
                  // title={"Profit Center Master List (" + initialRows.length + ")"}
                  rows={validationRows}
                  columns={validationColumns}
                  pageSize={10}
                  getRowIdValue={"id"}
                  hideFooter={true}
                  checkboxSelection={false}
                  disableSelectionOnClick={true}
                  status_onRowSingleClick={true}
                  stopPropagation_Column={"action"}
                  status_onRowDoubleClick={true}
                />
              )}

              {/* </Grid> */}
            </DialogContent>

            <DialogActions sx={{ display: "flex", justifyContent: "end" }}>

            </DialogActions>
          </Dialog>


          <Dialog
            hideBackdrop={false}
            elevation={2}
            PaperProps={{
              sx: { boxShadow: "none", marginBottom: "8px", borderRadius: "8px" },
            }}
            open={openDraftDialog}
            onClose={handleDraftDialogClose}
          >
            <Grid
              container
              sx={{ display: "flex", justifyContent: "space-between", backgroundColor: "#EAE9FF", }}
            >
              <Grid item>
                <DialogTitle
                  id="alert-dialog-title"
                  sx={{
                    justifyContent: "space-between",

                    height: "max-content",

                    paddingLeft: "1rem",


                    display: "flex",
                    alignItems: "flex-start",
                  }}
                >
                  <span style={{ display: "flex", alignItems: "center" }}>

                    <WarningAmberOutlined sx={{ color: "orange" }} />

                    &nbsp;&nbsp;
                  </span>
                  <Grid sx={{
                    display: "flex",
                    flexDirection: "column",
                  }}>
                    <Typography >Confirm</Typography>
                  </Grid>

                </DialogTitle>
              </Grid>
            </Grid>
            <DialogContent>
              <Stack>
                <Grid container>
                  <Grid
                    item
                    md={12}
                    sx={{

                      textAlign: "left",
                    }}
                  >
                    <Box sx={{ minWidth: 400 }}>
                      <FormControl sx={{ height: "auto" }} fullWidth>

                        <Typography variant="subtitle2" fontWeight={"bold"}>Do You Really Want to Proceed with SAVE AS DRAFT?</Typography>
                      </FormControl>
                    </Box>
                  </Grid>
                </Grid>
              </Stack>
              {/* <TextField
            autoFocus
            margin="dense"
            id="name"
            label="Enter Remarks for Correction"
            type="text"
            fullWidth
            variant="standard"
            onChange={handleRemarks}
          /> */}
            </DialogContent>
            <DialogActions sx={{
              padding: "1rem 1.5rem",
            }}>
              <Button
                color="error"
                variant="outlined"
                sx={{
                  height: 40,
                  minWidth: "4rem",
                  textTransform: "none",
                  borderColor: "#cc3300",
                  // backgroundColor:"#cc3300",
                  // color: "white",
                }}
                onClick={handleDraftDialogClose}
              >
                Cancel
              </Button>
              <Button
                className="button_primary--normal"
                type="save"
                onClick={onGLSaveAsDraft}
                variant="contained"
              >
                Submit
              </Button>
            </DialogActions>
          </Dialog>

          {/* <Backdrop
            sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
            open={blurLoading}
          // onClick={handleClose}
          >
            <CircularProgress color="inherit" />
          </Backdrop> */}
          <ReusableBackDrop
            blurLoading={blurLoading}
            loaderMessage={loaderMessage}
          />

          <Grid container sx={outermostContainer_Information}>
            <Grid item md={12} style={{ padding: "16px", display: "flex" }}>
              <Grid md={9} sx={{ display: "flex" }}>
                <Grid>
                  <IconButton
                    // onClick={handleBacktoRO}
                    color="primary"
                    aria-label="upload picture"
                    component="label"
                    sx={iconButton_SpacingSmall}
                  >
                    {/* <ArrowCircleLeftOutlinedIcon
                      sx={{
                        fontSize: "25px",
                        color: "#000000",
                      }}
                      onClick={() => {
                        dispatch(clearTaskData())
                        dispatch(clearProfitCenterPayloadGI())
                        dispatch(clearCostCenterPayload())
                        dispatch(clearSingleGLPayloadGI())
                        dispatch(clearProfitCenterPayload())
                        dispatch(clearCostCenter())
                        navigate("/masterDataCockpitNew/generalLedger");
                      }}
                    /> */}
                    <IconButton
                      // onClick={() => navigate(-1)}
                      onClick={() => navigate(-1)}
                      color="primary"
                      aria-label="upload picture"
                      component="label"
                    >
                      <ArrowCircleLeftOutlined
                        sx={{
                          fontSize: "25px",
                          color: "#000000",
                        }}
                      />
                    </IconButton>
                  </IconButton>
                </Grid>
                <Grid>
                  {isEditMode ? (
                    taskRowDetails?.requestType === "Create" ||
                      generalLedgerRowData?.requestType === "Create" ? (
                      <>
                        <Grid item md={12}>
                          <Typography variant="h3">
                            <strong>Create General Ledger: </strong>
                          </Typography>

                          <Typography variant="body2" color="#777">
                            This view edits the details of the General Ledger
                          </Typography>
                        </Grid>
                      </>
                    )
                      :
                      taskRowDetails?.requestType === "Extend" ||
                        generalLedgerRowData?.requestType === "Extend" ? (
                        <>
                          <Grid item md={12}>
                            <Typography variant="h3">
                              <strong>Extend General Ledger: </strong>
                            </Typography>

                            <Typography variant="body2" color="#777">
                              This view changes multiple General Ledger
                            </Typography>
                          </Grid>
                        </>
                      ) : (
                        <>
                          <Grid item md={12}>
                            <Typography variant="h3">
                              <strong>Extend General Ledger: </strong>
                            </Typography>

                            <Typography variant="body2" color="#777">
                              This view edits the details of the General Ledger
                            </Typography>
                          </Grid>
                        </>
                      )
                  ) : (
                    ""
                  )}

                  {isDisplayMode ? (
                    <Grid item md={12}>
                      <Typography variant="h3">
                        <strong>Display General Ledger </strong>
                      </Typography>

                      <Typography variant="body2" color="#777">
                        This view displays the details of the General Ledger
                      </Typography>
                    </Grid>
                  ) : (
                    ""
                  )}
                </Grid>
              </Grid>
              {/* {generalLedgerRowData?.reqStatus === "Correction Pending" ? (
            <Grid>
              <IconButton
                color="primary"
                aria-label="upload picture"
                component="label"
                sx={iconButton_SpacingSmall}
              >
                <MarkunreadOutlinedIcon
                  sx={{
                    fontSize: "25px",
                    color: "#000000",
                    alignItems: "flex-end",
                  }}
                  onClick={() => {}}
                />
              </IconButton>
            </Grid>
          ) : (
            ""
          )} */}
              <Grid
                md={3}
                sx={{ display: "flex", justifyContent: "flex-end" }}
                gap={2}
              >
                {/* <Tooltip title="MDM Specific Field View List">
                 <Button
                   variant="outlined"
                   size="small"
                   sx={{ button_Outlined, mr: 1 }}
                   onClick={OpenReusabletableForCreateDetails}
                   title="MDM Specific Field View List"
                 >
                   <FormatListBulletedOutlinedIcon />
                 </Button>
               </Tooltip> */}

                {taskRowDetails?.taskDesc === 'MDM Approval' ?
                  <Tooltip title="MDM Specific Field View List">
                    <Button
                      variant="outlined"
                      size="small"
                      sx={{ button_Outlined, mr: 1 }}
                      onClick={OpenReusabletableForCreateDetails}
                      title="MDM Specific Field View List"
                    >
                      <FormatListBulletedOutlinedIcon />
                    </Button>
                  </Tooltip>


                  // <Grid>     
                  //   <Button
                  //     variant="outlined"
                  //     size="small"
                  //     sx={button_Outlined}
                  //     onClick={OpenReusabletableForCreateDetails}
                  //     title="FMD Specific Fields List"
                  //   >
                  //     <LoupeOutlinedIcon
                  //       sx={{ padding: "2px" }}
                  //       fontSize="small"
                  //     />
                  //   </Button>
                  // </Grid>:''
                  : ''
                }
                {generalLedgerRowData?.requestId ||
                  taskRowDetails?.requestType ? (
                  <Grid>
                    <Button
                      variant="outlined"
                      size="small"
                      sx={button_Outlined}
                      onClick={openChangeLog}
                      title="Change Log"
                    >
                      <TrackChangesTwoToneIcon
                        sx={{ padding: "2px" }}
                        fontSize="small"
                      />
                    </Button>
                  </Grid>
                ) : (
                  ""
                )}

                {isChangeLogopen && (
                  <ChangeLog
                    open={true}
                    closeModal={handleClosemodalData}
                    requestId={
                      generalLedgerRowData?.requestId
                        ? generalLedgerRowData?.requestId
                        : taskRowDetails?.requestId
                    }
                    requestType={
                      generalLedgerRowData?.requestType
                        ? generalLedgerRowData?.requestType
                        : taskRowDetails?.requestType
                    }
                    generalLedgerId={iDs?.GeneralLedgerId}
                    pageName={"generalLedger"}
                    controllingArea={
                      generalLedgerRowData?.compCode
                        ? generalLedgerRowData?.compCode
                        : taskData?.body?.compCode
                    }
                    centerName={
                      generalLedgerRowData?.glAccount
                        ? generalLedgerRowData?.glAccount
                        : taskData?.body?.glAccount
                    }
                  />
                )}
                {checkIwaAccess(iwaAccessData, "General Ledger", "ChangeGL") &&
                  (userData?.role === "Super User" &&
                    generalLedgerRowData?.requestType &&
                    taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
                    isDisplayMode ? (
                    <Grid gap={1} sx={{ display: "flex" }}>
                      <Grid
                        gap={1}
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                        }}
                      >
                        <>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={onEdit}
                            >
                              Fill Details
                              <EditOutlinedIcon
                                sx={{ padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                        </>
                      </Grid>
                    </Grid>
                  ) : userData?.role === "Finance" &&
                    (generalLedgerRowData?.requestType ||
                      taskRowDetails?.requestType) &&
                    taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
                    isDisplayMode ? (
                    <Grid gap={1} sx={{ display: "flex" }}>
                      <Grid
                        gap={1}
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                        }}
                      >
                        <>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={onEdit}
                            >
                              Fill Details
                              <EditOutlinedIcon
                                sx={{ padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                        </>
                      </Grid>
                    </Grid>
                  ) : userData?.role === "Super User" &&
                    !generalLedgerRowData?.requestType &&
                    taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
                    isDisplayMode ? (
                    <Grid gap={1} sx={{ display: "flex" }}>
                      <Grid
                        gap={1}
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                        }}
                      >
                        <>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={onEdit}
                            >
                              Change
                              <EditOutlinedIcon
                                sx={{ padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                        </>
                      </Grid>
                    </Grid>
                  ) : userData?.role === "Finance" &&
                    !generalLedgerRowData?.requestType &&
                    taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
                    isDisplayMode ? (
                    <Grid gap={1} sx={{ display: "flex" }}>
                      <Grid
                        gap={1}
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                        }}
                      >
                        <>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={onEdit}
                            >
                              Change
                              <EditOutlinedIcon
                                sx={{ padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                        </>
                      </Grid>
                    </Grid>
                  ) : (
                    ""
                  ))}
              </Grid>
            </Grid>
            <Grid
              container
              display="flex"
              flexDirection="row"
              flexWrap="nowrap"
            >

              <Box width="100%" sx={{ marginLeft: "40px" }}>
                <Grid item sx={{ paddingTop: "2px !important" }}>
                  {/* <Stack flexDirection="row">
                    
                    <div style={{ width: "15%" }}>
                      <Typography variant="body2" color="#777">
                        Chart of Accounts
                      </Typography>
                    </div>
                    <Typography
                      variant="body2"
                      fontWeight="bold"
                      justifyContent="flex-start"
                    >
                      :{" "}
                      {generalLedgerRowData?.chartOfAccount
                        ? generalLedgerRowData?.chartOfAccount
                        : iDs?.ChartOfAccount
                          ? iDs?.ChartOfAccount
                          : ""}
                    </Typography>
                  </Stack> */}
                  <Stack flexDirection="row">
                    <Grid item col md={10}>
                      <Stack flexDirection="row">
                        <Grid item col md={2}>
                          <Typography variant="body2" color="#777">
                            Chart of Accounts
                          </Typography>
                        </Grid>
                        <Typography
                          variant="body2"
                          fontWeight="bold"
                          justifyContent="flex-start"
                        >
                          :{" "}
                          {generalLedgerRowData?.chartOfAccount
                            ? generalLedgerRowData?.chartOfAccount
                            : iDs?.ChartOfAccount
                              ? iDs?.ChartOfAccount
                              : ""}
                        </Typography>
                      </Stack>
                    </Grid>
                    {/* <Grid item col md={4}>
                      <Stack flexDirection="row">
                        <Grid item col md={3}>
                          <Typography variant="body2" color="#777">
                            Request Id
                          </Typography>
                        </Grid>
                        <Typography
                          variant="body2"
                          fontWeight="bold"
                          justifyContent="flex-start"
                        >
                          :{" "}
                          {generalLedgerRowData?.createdAt
                            ? generalLedgerRowData?.requestId
                            : taskRowDetails?.requestId
                              ? taskRowDetails?.requestId
                              : ""}
                        </Typography>
                      </Stack>
                    </Grid> */}
                    <Grid item col md={4}>
                      <Stack flexDirection="row">
                        <Grid item col md={3}>
                          <Typography variant="body2" color="#777">
                            Request Id
                          </Typography>
                        </Grid>
                        <Typography
                          variant="body2"
                          fontWeight="bold"
                          justifyContent="flex-start"
                        >
                          :{" "}
                          {generalLedgerRowData?.requestId
                            ? generalLedgerRowData?.requestId
                            : taskRowDetails?.requestId
                              ? taskRowDetails?.requestId
                              : ""}
                        </Typography>
                      </Stack>
                    </Grid>
                  </Stack>
                </Grid>

                <Grid item sx={{ paddingTop: "2px !important" }}>

                  <Stack flexDirection="row">
                    <Grid item col md={10}>
                      <Stack flexDirection="row">
                        <Grid item col md={2}>
                          <Typography variant="body2" color="#777">
                            Company Code
                          </Typography>
                        </Grid>
                        <Typography
                          variant="body2"
                          fontWeight="bold"
                          justifyContent="flex-start"
                        >
                          :{" "}
                          {generalLedgerRowData?.compCode
                            ? generalLedgerRowData?.compCode?.split(',')?.slice(0, 2)?.join(',')
                            : iDs?.CompCode
                              ? iDs?.CompCode?.split(',')?.slice(0, 2)?.join(',')
                              : ""}
                          {"..."}
                          <InfoOutlined
                            sx={{

                              fontSize: "15px",
                              //color: "#000000",
                              //alignItems: "flex-end",
                            }}
                            //onClick={openTableForCompanyCode}

                            onClick={openTableForCompanyCode}
                          />
                        </Typography>
                      </Stack>
                    </Grid>
                    <Grid item col md={4}>
                      <Stack flexDirection="row">
                        <Grid item col md={3}>
                          <Typography variant="body2" color="#777">
                            Requested By
                          </Typography>
                        </Grid>
                        <Typography
                          variant="body2"
                          fontWeight="bold"
                          justifyContent="flex-start"
                        >
                          :{" "}
                          {generalLedgerRowData?.createdBy
                            ? generalLedgerRowData?.createdBy
                            : taskRowDetails?.createdBy
                              ? taskRowDetails?.createdBy
                              : ""}

                        </Typography>
                      </Stack>
                    </Grid>

                  </Stack>

                </Grid>


                <Grid item sx={{ paddingTop: "2px !important" }}>

                  <Stack flexDirection="row">
                    <Grid item col md={10}>
                      <Stack flexDirection="row">
                        <Grid item col md={2}>
                          <Typography variant="body2" color="#777">
                            G/L Account
                          </Typography>
                        </Grid>
                        <Typography
                          variant="body2"
                          fontWeight="bold"
                          justifyContent="flex-start"
                        >
                          :{" "}
                          {generalLedgerRowData?.glAccount
                            ? generalLedgerRowData?.glAccount
                            : iDs?.GLAccount
                              ? iDs?.GLAccount
                              : ""}

                        </Typography>
                      </Stack>
                    </Grid>
                    <Grid item col md={4}>
                      <Stack flexDirection="row">
                        <Grid item col md={3}>
                          <Typography variant="body2" color="#777">
                            Requested On
                          </Typography>
                        </Grid>
                        <Typography
                          variant="body2"
                          fontWeight="bold"
                          justifyContent="flex-start"
                        >
                          :{" "}
                          {/* {moment(taskRowDetails?.createdOn).format(
                              appSettings?.dateFormat
                            ) ??
                              moment(generalLedgerRowData?.creationDate).format(
                                appSettings?.dateFormat
                              )} */}
                          {Object.keys(taskData).length ? moment(taskData?.body?.creationDate).format(
                            appSettings?.dateFormat
                          ) : moment(generalLedgerRowData?.createdOn).format(
                            appSettings?.dateFormat
                          )
                          }
                        </Typography>
                      </Stack>
                    </Grid>

                  </Stack>

                </Grid>




                {
                  (["Extend", "Display For Extend"].includes(generalLedgerRowData?.requestType) ||
                    taskRowDetails?.requestType === "Extend") && (
                    <Grid item sx={{ paddingTop: "2px !important" }}>
                      <Stack flexDirection="row">
                        <div style={{ width: "15%" }}>
                          <Typography variant="body2" color="#777">
                            Extended to company codes
                          </Typography>
                        </div>
                        <Typography variant="body2" fontWeight="bold">
                          :
                          {generalLedgerRowData?.requestType === 'Extend'
                            ? iDs?.CompanyCodesExtendedTo
                            : generalLedgerRowData?.compCode
                              ? extendedCompanyCode?.join(", ")
                              : ""}
                        </Typography>
                      </Stack>
                    </Grid>
                  )
                }

              </Box>
            </Grid>
            {/* </Box>
            </Grid> */}

            <Grid container style={{ marginLeft: 25 }}>
              <Stepper
                activeStep={activeStep}
                // onChange={handleC/hange}
                // variant="scrollable"
                sx={{
                  background: "#FFFFFF",
                  borderBottom: "1px solid #BDBDBD",
                  width: "100%",
                  height: "48px",
                }}
                aria-label="mui tabs example"
              >
                {factorsArray?.map((factor, index) => (
                  <Step key={factor}>
                    <StepLabel sx={{ fontWeight: "700" }}>{factor}</StepLabel>
                  </Step>
                ))}
              </Stepper>

              {/* Display the cards of the currently active tab */}
              {/* {tabContents &&
                tabContents[activeStep]?.map((cardContent, index) => (
                  <Box key={index} sx={{ mb: 2, width: "100%" }}>
                    <Typography variant="body2">{cardContent}</Typography>
                  </Box>
                ))} */}
            </Grid>
          </Grid>

          <Grid
            gap={1}
            sx={{ display: "flex", justifyContent: "space-between" }}
          >
            <Paper
              sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
              elevation={2}
            >
              <BottomNavigation
                className="container_BottomNav"
                showLabels
                sx={{ display: "flex", justifyContent: "flex-end" }}
              >
                {
                  taskRowDetails?.taskNature !== "Single-User" && taskRowDetails?.itmStatus === "Open" ? (
                    <>
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Primary, mr: 1 }}
                        onClick={handleBack}
                        disabled={activeStep === 0}
                      >
                        Back
                      </Button>
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Primary, mr: 1 }}
                        onClick={handleNext}
                        disabled={
                          activeStep === factorsArray.length - 1 ? true : false
                        }
                      >
                        Next
                      </Button>
                    </>
                  )
                    : (<>
                      {activeStep === factorsArray?.length - 1
                        ? filteredButtons?.map((button, index) => (
                          <Button
                            key={index}
                            variant="contained"
                            size="small"
                            sx={{ ...button_Primary, mr: 1 }}
                            disabled={
                              button?.MDG_DYN_BTN_BUTTON_STATUS === "DISABLED"
                            }
                            onClick={() => handleButtonAction(button)}
                          >
                            {button.MDG_DYN_BTN_BUTTON_NAME}
                          </Button>
                        ))
                        : ""}
                      {generalLedgerRowData?.reqStatus === "Draft" ? (
                        <>
                          {activeStep != 0 ? (
                            <Button
                              variant="contained"
                              size="small"
                              sx={{ ...button_Primary, mr: 1 }}
                              onClick={handleDraftDialog}
                            >
                              Save As Draft
                            </Button>
                          ) : (
                            ""
                          )}

                          {activeStep === factorsArray?.length - 1 && (
                            <>
                              <Button
                                variant="contained"
                                size="small"
                                sx={{ ...button_Primary, mr: 1 }}
                                onClick={onValidateGeneralLedgerApprover}
                              >
                                Validate
                              </Button>
                              <Button
                                variant="contained"
                                size="small"
                                sx={{ ...button_Primary, mr: 1 }}
                                // onClick={handleGeneralLedgerReviewCreate}
                                onClick={handleOpenRemarkDialog} //handleOpenRemarkDialog
                                disabled={submitForReviewDisabled}
                              >
                                Submit
                              </Button>
                            </>
                          )}

                        </>
                      ) : (
                        ""
                      )}

                      {/* <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary, mr: 1 }}
                  onClick={handleOpenRemarkDialog}
                  //disabled={submitForReviewDisabled}
                >
                  Submit
                </Button>
              */}
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Primary, mr: 1 }}
                        onClick={handleBack}
                        disabled={activeStep === 0}
                      >
                        Back
                      </Button>
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Primary, mr: 1 }}
                        onClick={handleNext}
                        disabled={
                          activeStep === factorsArray.length - 1 ? true : false
                        }
                      >
                        Next
                      </Button>
                    </>
                    )}

              </BottomNavigation>
            </Paper>

          </Grid>
        </div>
      )}
    </>
  );
};

export default DisplayGeneralLedger;
