import { useLocation } from "react-router-dom";
import { useSelector } from "react-redux";
import { MODULE_MAP, REQUEST_TYPE } from "../constant/enum";
import useChangePayloadCreation from "@hooks/useChangePayloadCreation";
import usePayloadCreation from "@hooks/usePayloadCreation";

const previewBifurcationPayload = (
  module,
  requestId,
  requestType,
  templateName,
  payloadData
) => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const taskData = useSelector((state) => state.userManagement.taskData);
  const isReqBench = queryParams.get("reqBench");
  const initialReqScreen = !Boolean(taskData?.taskId) && !isReqBench;

  const { createPayloadFromReduxState } = usePayloadCreation({
    initialReqScreen,
    isReqBench: isReqBench,
  });

  const { changePayloadForTemplate } = useChangePayloadCreation(templateName);

  const payloadMap = {
    [MODULE_MAP.MAT]: () => {
      if (
        requestType === REQUEST_TYPE.CHANGE ||
        requestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD
      ) {
        return requestId
          ? changePayloadForTemplate(true)
          : changePayloadForTemplate(false);
      }
      return createPayloadFromReduxState(payloadData);
    },
    [MODULE_MAP.PC]: () => null,
    [MODULE_MAP.CC]: () => null,
    [MODULE_MAP.GL]: () => null,
    [MODULE_MAP.PCG]: () => null,
    [MODULE_MAP.CCG]: () => null,
    [MODULE_MAP.CEG]: () => null,
  };

  const generatePayload = payloadMap[module] || (() => null);

  return generatePayload();
};

export default previewBifurcationPayload;
