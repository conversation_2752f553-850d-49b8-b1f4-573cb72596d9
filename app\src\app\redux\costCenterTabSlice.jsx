import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  rows: [], // For table display
  tabs: {}, // For tab data keyed by rowId
  selectedRowId: null,
  apiData: [], // To track selected row
};

const costCenterTabSlice = createSlice({
  name: "costCenterData",
  initialState,
  reducers: {
    setCostCenterApiData: (state, action) => {
      state.apiData = action.payload;
    },

    setCCRows: (state, action) => {
      state.rows = action.payload;
      console.log("setCostCenterRows1", action.payload);
    },
    setCostCenterTabs: (state, action) => {
      // console.log("state",state,action)
      console.log("action", action.payload);
      state.tabs = action.payload;
      // console.log("setProfitCenterTabspayload", action);
    },
    setSelectedRowId: (state, action) => {
      state.selectedRowId = action.payload; // e.g. "1358"
    },
    clearCostCenterData: (state) => {
      state.rows = [];
      state.tabs = {};
      state.selectedRowId = null;
    },
  },
});

export const {
  setCostCenterRows,
  setCostCenterTabs,
  setSelectedRowId,
  clearCostCenterData,
  setCostCenterApiData,
} = costCenterTabSlice.actions;

export default costCenterTabSlice.reducer;
