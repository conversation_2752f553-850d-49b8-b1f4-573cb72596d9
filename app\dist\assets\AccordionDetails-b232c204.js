import{o as e}from"./index-75c1660a.js";import{g as n,X as a,w as t,G as m,x as s,z as d}from"./Button-c2ace85e.js";import{i as c}from"./Dropdown-fc3a3f6e.js";const l=n({components:{MuiAccordion:{styleOverrides:{root:{"&.Mui-disabled":{backgroundColor:"var(--background-disabled)"}}}}}});function b(r){return e.jsx(a,{theme:l,children:e.jsx(t,{...r})})}const u={small:{minHeight:"2rem "},medium:{minHeight:"2.5rem "},large:{minHeight:"3.25rem"},xlarge:{minHeight:"3.5rem"}},g=m(s)(({size:r="medium",variant:o,disabled:i})=>({display:"flex",...o=="leftIcon"&&{flexDirection:"row-reverse"},gap:"0.5rem","& .MuiAccordionSummary-content":{margin:"0.5rem 0rem",...r=="small"&&{margin:"0.25rem 0rem"}},padding:"0rem 1rem",fontFamily:"inherit",fontSize:"1rem",fontWeight:"500",backgroundColor:"var(--background-default)","& .MuiPaper-root":{"& .MuiAccordion-root":{...i&&{backgroundColor:"var(--background-disabled)"}}},...u[r]})),h=({variant:r="leftIcon",...o})=>e.jsx(g,{variant:r,...o,expandIcon:r==="noIcon"?null:e.jsx(c,{})});function v(r){return e.jsx(d,{...r})}export{v as c,h as g,b as t};
