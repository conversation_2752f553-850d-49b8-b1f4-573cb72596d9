import{r as p,s as M,q as v,a as d,G as s,b4 as V,j as g,x as P,T as h,E as C,B,aC as m,bG as F,ay as L,b6 as W,b as O,bH as I,al as z,v as G,w as H,F as A}from"./index-17b8d91e.js";import{d as j}from"./ArrowCircleLeftOutlined-2a09f8e2.js";import"./SingleSelectDropdown-29664b58.js";const _=D=>{var o;p.useState(!1);const U=M(),c=v(i=>{var n;return(n=i==null?void 0:i.payload)==null?void 0:n.additionalData});let u=v(i=>{var n;return(n=i==null?void 0:i.tabsData)==null?void 0:n.additionalData});console.log("deac",u);const y=(o=u==null?void 0:u.Description)==null?void 0:o.Data.map((i,n)=>({id:n+1,language:i.Language,materialDescription:"NPI Test Material"})),a=[{field:"id",headerName:"ID",width:50},{field:"language",headerName:"Language",width:150,valueOptions:["EN","AR","ES"],type:"singleSelect",align:"left",headerAlign:"left",editable:!0,preProcessEditCellProps:i=>{let n=c==null?void 0:c.map(f=>{var b;return f.id===i.id?{...f,language:(b=i==null?void 0:i.props)==null?void 0:b.value}:f});U(F(n))}},{field:"materialDescription",headerName:"Material Description",width:250,type:"text",align:"left",headerAlign:"left",editable:!0,preProcessEditCellProps:i=>{let n=c==null?void 0:c.map(f=>{var b;return f.id===i.id?{...f,materialDescription:(b=i==null?void 0:i.props)==null?void 0:b.value}:f});U(F(n))}}];return d("div",{children:d(s,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:.25,...V},children:g(s,{container:!0,display:"block",children:[d(s,{item:!0,sx:{paddingTop:"2px !important",mb:1},children:g(P,{flexDirection:"row",alignItems:"center",children:[d("div",{style:{width:"5%"},children:d(h,{variant:"body2",color:"#777",children:"Material"})}),d(h,{variant:"body2",fontWeight:"bold",children:":"}),d(C,{size:"small",sx:{ml:2},value:u.Material})]})}),d(B,{sx:{width:"50%"},children:d(m,{rows:y??[],columns:a,getRowIdValue:"id",hideFooter:!0,checkboxSelection:!1,experimentalFeatures:{newEditingApi:!0}})})]})})})},$=D=>{var b,N,x,w,E,T,S,k;p.useState(!1),p.useState([]);const[U,c]=p.useState(""),[u,y]=p.useState({}),a=v(e=>e.AllDropDown.dropDown);console.log("drop",a);const o=v(e=>e.payload.payloadData);console.log("payl",o),v(e=>e.payload.payloadData);const[i,n]=p.useState([{id:1,xValue:"1",aUnit:(b=o==null?void 0:o.BaseUnit)==null?void 0:b.code,measureUnitText:(N=o==null?void 0:o.BaseUnit)==null?void 0:N.desc,resemble:"<=>",yValue:"1",bUnit:(x=o==null?void 0:o.BaseUnit)==null?void 0:x.code,measurementUnitText:(w=o==null?void 0:o.BaseUnit)==null?void 0:w.desc,eanUpc:"",eanCategory:"",autoCheckDigit:"",addEans:"",length:"",width:"",height:"",unitsOfDimension:"",volume:"",volumeUnit:"",grossWeight:"",netWeight:"",weightUnit:"",noLowerLvlUnits:"",lowerLvlUnits:"",remVolAfterNesting:"",maxStackFactor:"",maxTopLoadFullPkg:"",UomToploadFullPkg:"",capacityUsage:"",UomCategory:""},{id:2,xValue:"",aUnit:"",measureUnitText:"",resemble:"<=>",yValue:"1",bUnit:(E=o==null?void 0:o.BaseUnit)==null?void 0:E.code,measurementUnitText:(T=o==null?void 0:o.BaseUnit)==null?void 0:T.desc,eanUpc:"",eanCategory:"",autoCheckDigit:"",addEans:"",length:"",width:"",height:"",unitsOfDimension:"",volume:"",volumeUnit:"",grossWeight:"",netWeight:"",weightUnit:"",noLowerLvlUnits:"",lowerLvlUnits:"",remVolAfterNesting:"",maxStackFactor:"",maxTopLoadFullPkg:"",UomToploadFullPkg:"",capacityUsage:"",UomCategory:""}]),f=[{field:"xValue",headerName:"X",type:"text",editable:!0,preProcessEditCellProps:e=>{let r=i==null?void 0:i.map(t=>{var l;return t.id===e.id&&(t.xValue=(l=e==null?void 0:e.props)==null?void 0:l.value),t});n(r)}},{field:"aUnit",headerName:"AUn",editable:!0,type:"singleSelect",valueOptions:(S=a==null?void 0:a.BaseUnit)==null?void 0:S.map(e=>e.code),editable:!0,preProcessEditCellProps:e=>{let r=i==null?void 0:i.map(t=>{var l;return t.id===e.id?{...t,language:(l=e==null?void 0:e.props)==null?void 0:l.value}:t});n(r)}},{field:"measureUnitText",headerName:"Measurement Unit Text",type:"text",editable:!0,preProcessEditCellProps:e=>{let r=i==null?void 0:i.map(t=>{var l;return t.id===e.id&&(t.measureUnitText=(l=e==null?void 0:e.props)==null?void 0:l.value),t});n(r)}},{field:"resemble",headerName:"<=>",editable:!0},{field:"yValue",headerName:"Y",type:"text",editable:!0,preProcessEditCellProps:e=>{let r=i==null?void 0:i.map(t=>{var l;return t.id===e.id&&(t.yValue=(l=e==null?void 0:e.props)==null?void 0:l.value),t});n(r)}},{field:"bUnit",headerName:"BUn",editable:!0,type:"singleSelect",valueOptions:(k=a==null?void 0:a.BaseUnit)==null?void 0:k.map(e=>e.code),editable:!0,preProcessEditCellProps:e=>{let r=i==null?void 0:i.map(t=>{var l;return t.id===e.id?{...t,language:(l=e==null?void 0:e.props)==null?void 0:l.value}:t});n(r)}},{field:"measurementUnitText",headerName:"Measurement Unit Text",type:"text",editable:!0,preProcessEditCellProps:e=>{let r=i==null?void 0:i.map(t=>{var l;return t.id===e.id&&(t.measurementUnitText=(l=e==null?void 0:e.props)==null?void 0:l.value),t});n(r)}},{field:"eanUpc",headerName:"EAN/UPC",type:"text",editable:!0,preProcessEditCellProps:e=>{let r=i==null?void 0:i.map(t=>{var l;return t.id===e.id&&(t.eanUpc=(l=e==null?void 0:e.props)==null?void 0:l.value),t});n(r)}},{field:"eanCategory",headerName:"EAN Category ",type:"singleSelect",editable:!0,preProcessEditCellProps:e=>{let r=i==null?void 0:i.map(t=>{var l;return t.id===e.id&&(t.eanCategory=(l=e==null?void 0:e.props)==null?void 0:l.value),t});n(r)}},{field:"autoCheckDigit",headerName:"Auto Check Digit",renderCell:e=>d(L,{sx:{padding:0}})},{field:"addEans",headerName:"Additional EANs",renderCell:e=>d(L,{sx:{padding:0}})},{field:"length",headerName:"Length",type:"text",editable:!0,preProcessEditCellProps:e=>{let r=i==null?void 0:i.map(t=>{var l;return t.id===e.id&&(t.length=(l=e==null?void 0:e.props)==null?void 0:l.value),t});n(r)}},{field:"width",headerName:"Width",type:"text",editable:!0,preProcessEditCellProps:e=>{let r=i==null?void 0:i.map(t=>{var l;return t.id===e.id&&(t.width=(l=e==null?void 0:e.props)==null?void 0:l.value),t});n(r)}},{field:"height",headerName:"Height",type:"text",editable:!0,preProcessEditCellProps:e=>{let r=i==null?void 0:i.map(t=>{var l;return t.id===e.id&&(t.height=(l=e==null?void 0:e.props)==null?void 0:l.value),t});n(r)}},{field:"unitsOfDimension",headerName:"Unit of Dimension",editable:!0,type:"singleSelect",editable:!0,preProcessEditCellProps:e=>{let r=i==null?void 0:i.map(t=>{var l;return t.id===e.id&&(t.unitsOfDimension=(l=e==null?void 0:e.props)==null?void 0:l.value),t});n(r)}},{field:"volume",headerName:"Volume",type:"text",editable:!0,preProcessEditCellProps:e=>{let r=i==null?void 0:i.map(t=>{var l;return t.id===e.id&&(t.volume=(l=e==null?void 0:e.props)==null?void 0:l.value),t});n(r)}},{field:"volumeUnit",headerName:"Volume Unit",editable:!0,type:"singleSelect",editable:!0,preProcessEditCellProps:e=>{let r=i==null?void 0:i.map(t=>{var l;return t.id===e.id&&(t.volumeUnit=(l=e==null?void 0:e.props)==null?void 0:l.value),t});n(r)}},{field:"grossWeight",headerName:"Gross Weight",type:"text",editable:!0,preProcessEditCellProps:e=>{let r=i==null?void 0:i.map(t=>{var l;return t.id===e.id&&(t.grossWeight=(l=e==null?void 0:e.props)==null?void 0:l.value),t});n(r)}},{field:"netWeight",headerName:"Net Weight",type:"text",editable:!0,preProcessEditCellProps:e=>{let r=i==null?void 0:i.map(t=>{var l;return t.id===e.id&&(t.netWeight=(l=e==null?void 0:e.props)==null?void 0:l.value),t});n(r)}},{field:"weightUnit",headerName:"Weight Unit",editable:!0,type:"singleSelect",editable:!0,preProcessEditCellProps:e=>{let r=i==null?void 0:i.map(t=>{var l;return t.id===e.id&&(t.weightUnit=(l=e==null?void 0:e.props)==null?void 0:l.value),t});n(r)}},{field:"noLowerLvlUnits",headerName:"No. Lower- Level Units",type:"text",editable:!0,preProcessEditCellProps:e=>{let r=i==null?void 0:i.map(t=>{var l;return t.id===e.id&&(t.noLowerLvlUnits=(l=e==null?void 0:e.props)==null?void 0:l.value),t});n(r)}},{field:"lowerLvlUnits",headerName:"Lower Level Units",editable:!0,type:"singleSelect",editable:!0,preProcessEditCellProps:e=>{let r=initialRows==null?void 0:initialRows.map(t=>{var l;return t.id===e.id&&(t.lowerLvlUnits=(l=e==null?void 0:e.props)==null?void 0:l.value),t});n(r)}},{field:"remVolAfterNesting",headerName:"Remainder Volume after Nesting",type:"text",editable:!0,preProcessEditCellProps:e=>{let r=initialRows==null?void 0:initialRows.map(t=>{var l;return t.id===e.id&&(t.remVolAfterNesting=(l=e==null?void 0:e.props)==null?void 0:l.value),t});n(r)}},{field:"maxStackFactor",headerName:"Max. Stacking Factor",type:"text",editable:!0,preProcessEditCellProps:e=>{let r=initialRows==null?void 0:initialRows.map(t=>{var l;return t.id===e.id&&(t.maxStackFactor=(l=e==null?void 0:e.props)==null?void 0:l.value),t});n(r)}},{field:"maxTopLoadFullPkg",headerName:"Max. Top Load on full Package",type:"text",editable:!0,preProcessEditCellProps:e=>{let r=initialRows==null?void 0:initialRows.map(t=>{var l;return t.id===e.id&&(t.maxTopLoadFullPkg=(l=e==null?void 0:e.props)==null?void 0:l.value),t});n(r)}},{field:"UomToploadFullPkg",headerName:"UOM of Max. Topload on Full Package",editable:!0,type:"singleSelect",editable:!0,preProcessEditCellProps:e=>{let r=initialRows==null?void 0:initialRows.map(t=>{var l;return t.id===e.id&&(t.UomToploadFullPkg=(l=e==null?void 0:e.props)==null?void 0:l.value),t});n(r)}},{field:"capacityUsage",headerName:"Capacity Usage",type:"text",editable:!0,preProcessEditCellProps:e=>{let r=initialRows==null?void 0:initialRows.map(t=>{var l;return t.id===e.id&&(t.capacityUsage=(l=e==null?void 0:e.props)==null?void 0:l.value),t});n(r)}},{field:"UomCategory",headerName:"UOM Category",editable:!0,type:"singleSelect",editable:!0,preProcessEditCellProps:e=>{let r=initialRows==null?void 0:initialRows.map(t=>{var l;return t.id===e.id&&(t.UomCategory=(l=e==null?void 0:e.props)==null?void 0:l.value),t});n(r)}}];return p.useEffect(()=>{console.log(i)},[i]),d("div",{children:d(s,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:.25,...V},children:g(s,{container:!0,display:"block",children:[d(s,{item:!0,sx:{paddingTop:"2px !important",mb:1},children:g(P,{flexDirection:"row",alignItems:"center",children:[d("div",{style:{width:"5%"},children:d(h,{variant:"body2",color:"#777",children:"Material"})}),d(h,{variant:"body2",fontWeight:"bold",children:":"}),d(C,{size:"small",sx:{ml:2},value:"textValue",onChange:e=>{c(e.target.value)}})]})}),d(s,{item:!0,sx:{paddingTop:"2px !important",mb:1},children:g(P,{flexDirection:"row",alignItems:"center",children:[d("div",{style:{width:"5%"},children:d(h,{variant:"body2",color:"#777",children:"Description"})}),d(h,{variant:"body2",fontWeight:"bold",children:":"}),d(C,{size:"small",sx:{ml:2},value:"textValue",onChange:e=>{c(e.target.value)}})]})}),d(s,{container:!0,display:"block",sx:{padding:2},children:d(s,{item:!0,sx:{paddingTop:"2px !important",mb:1},children:g(P,{flexDirection:"row",alignItems:"center",children:[d("div",{style:{width:"7%"},children:d(h,{variant:"body2",color:"#777",children:"Units Of Measure Group"})}),d(h,{variant:"body2",fontWeight:"bold",children:":"}),d(s,{item:!0,md:1.5,children:d(W,{sx:{height:"31px",ml:2},size:"small",value:u==null?void 0:u.BaseUnit,onChange:(e,r)=>{y({...u,BaseUnit:r})},options:(a==null?void 0:a.BaseUnit)??[],getOptionLabel:e=>`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`,renderOption:(e,r)=>d("li",{...e,children:g(h,{style:{fontSize:12},children:[r==null?void 0:r.code," - ",r==null?void 0:r.desc]})}),renderInput:e=>d(C,{...e,variant:"outlined",placeholder:"Select Unit Of Measure Group"})})})]})})}),d("div",{className:"confirmOrder-lineItem",children:d(m,{scrollbarSize:!0,title:"Units of Measure/ EANs/ Dimensions  ",width:"100%",rows:i??[],columns:f,getRowIdValue:"id",hideFooter:!0,checkboxSelection:!1,experimentalFeatures:{newEditingApi:!0}})})]})})})},J=()=>{const D=O();let U=v(i=>{var n;return(n=i==null?void 0:i.tabsData)==null?void 0:n.additionalData});I(),p.useState("1");const[c,u]=p.useState(0);console.log("displayData",U);const y=["Description","Units of Measure"],a=[[d(A,{children:d(_,{})})],[d(A,{children:d($,{})})]],o=(i,n)=>{u(n)};return d("div",{children:d(s,{container:!0,style:{...z,backgroundColor:"#FAFCFF"},children:g(s,{sx:{width:"inherit"},children:[d(s,{item:!0,md:7,style:{padding:"16px",display:"flex"},children:g("div",{style:{display:"flex"},children:[d("div",{children:d(j,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{D(-1)}})}),d("div",{children:d(h,{variant:"h3",children:d("strong",{children:"Display Additional Details"})})})]})}),g(s,{container:!0,children:[d(G,{value:c,onChange:o,variant:"scrollable",sx:{background:"#FFF",borderBottom:"1px solid #BDBDBD",width:"100%"},"aria-label":"mui tabs example",children:y.map((i,n)=>d(H,{sx:{fontSize:"12px",fontWeight:"700"},label:i},n))}),a[c].map((i,n)=>d(B,{sx:{mb:2,width:"100%"},children:d(h,{variant:"body2",children:i})},n))]})]})})})};export{J as default};
