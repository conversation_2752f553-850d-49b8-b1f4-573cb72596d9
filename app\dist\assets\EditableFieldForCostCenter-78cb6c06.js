import{r as i,q as D,s as W,cf as K,a as o,G as x,j as p,T as A,ay as z,x as O,b6 as _,E as P,at as J,au as M,F as Q,bI as R,K as I,b$ as U,ai as B}from"./index-17b8d91e.js";import{D as X}from"./DatePicker-68227989.js";function Y(a,d){return Array.isArray(d)&&d.find(s=>s.code===a)||""}const ee=({label:a,value:d,units:S,data:s,onSave:Z,isEditMode:k,visibility:t,length:H,type:g,taskRequestId:L})=>{var F,q;const[m,C]=i.useState(d),[b,T]=i.useState(!1),[E,$]=i.useState(!1),h=D(e=>e.AllDropDown.dropDown),u=W();Y(m,h),D(e=>e.edit.payload);let y=D(e=>e.userManagement.taskData);const v=y==null?void 0:y.subject;console.log("editField",a,m);const j={label:a,value:m,units:S,type:g,visibility:t};console.log("fieldData",s);let c=a.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("");i.useEffect(()=>{C(d),console.log("mobile",a,m)},[d]),i.useEffect(()=>{(t==="0"||t==="Required")&&u(K(c))},[]);const f=e=>{u(R({keyname:c.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""),data:e}))},G=e=>{console.log("compcode",e);const r=l=>{console.log("value",l),u(R({keyname:"Currency",data:""})),u(B({keyName:"Currency",data:l.body}))},n=l=>{console.log(l,"error in dojax")};I(`/${U}/data/getCurrency?companyCode=${e==null?void 0:e.code}`,"get",r,n)},N=e=>{console.log("countryyyyy",e);const r=l=>{console.log("value",l),u(R({keyname:"Region",data:""})),u(B({keyName:"Region",data:l.body}))},n=l=>{console.log(l,"error in dojax")};I(`/${U}/data/getRegionBasedOnCountry?country=${e==null?void 0:e.code}`,"get",r,n)};return console.log("dropDownData[key]",h[c]),o(Q,{children:k?t==="Hidden"?null:o(x,{item:!0,children:o(O,{children:k?p("div",{children:[p(A,{variant:"body2",color:"#777",children:[a,t==="Required"||t==="0"?o("span",{style:{color:"red"},children:"*"}):""]}),g==="Drop Down"?o(_,{options:h[c]??[],value:s[c]&&((F=h[c])==null?void 0:F.filter(e=>e.code===s[c]))&&((q=h[c])==null?void 0:q.filter(e=>e.code===s[c])[0])||"",onChange:(e,r,n)=>{console.log("reason",n),console.log(t,r,"visibility2"),(t==="Required"||t==="0")&&r===null&&$(!0),a==="Comp Code"&&G(r),a==="Country/Reg"&&N(r),f(n==="clear"?"":r==null?void 0:r.code),C(r.code),T(!0)},getOptionLabel:e=>(console.log("optionn",e),e===""||(e==null?void 0:e.code)===""?"":`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??""),renderOption:(e,r)=>(console.log("option vakue",r),o("li",{...e,children:o(A,{style:{fontSize:12},children:`${r==null?void 0:r.code} - ${r==null?void 0:r.desc}`})})),renderInput:e=>o(P,{...e,variant:"outlined",placeholder:`Select ${j.label}`,size:"small",error:E})}):g==="Input"?o(P,{variant:"outlined",size:"small",fullWidth:!0,value:s[c].toUpperCase(),placeholder:`Enter ${j.label}`,inputProps:{maxLength:H},onChange:e=>{const r=e.target.value;if(r.length>0&&r[0]===" ")f(r.trimStart());else{let n=r.toUpperCase();f(n)}(t==="Required"||t==="0")&&r.length<=0&&$(!0),C(r.toUpperCase())},error:E}):g==="Calendar"?o(J,{dateAdapter:M,children:o(X,{slotProps:{textField:{size:"small"}},placeholder:"Select Date Range"})}):g==="Radio Button"?o(x,{item:!0,md:2,children:o(z,{sx:{padding:0},checked:s[c],onChange:(e,r)=>{f(r),C(r)}})}):""]}):""})}):L&&t==="Hidden"||v&&t==="Hidden"?null:o(x,{item:!0,children:p(O,{children:[p(A,{variant:"body2",color:"#777",children:[a,t==="Required"||t==="0"?o("span",{style:{color:"red"},children:"*"}):""]}),p(A,{variant:"body2",fontWeight:"bold",children:[s[c],g==="Radio Button"?o(z,{sx:{padding:0},checked:s[c],disabled:!0}):""]})]})})})};export{ee as E};
