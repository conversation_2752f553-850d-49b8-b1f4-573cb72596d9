import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import WorkspaceComponent from "@cw/cherrywork-iwm-workspace/Workspace";
import configData from "../../data/configData";
import { getLanguageTranslationData, userPermissions } from "../../data/propData";
import { useNavigate } from "react-router-dom";
import { setTaskData, setUserDetails } from "../../app/userManagementSlice";
import { setHistoryPath } from "../../app/utilitySlice";
import { baseUrl_ITMJava } from "../../data/baseUrl";
import { doAjax } from "../Common/fetchService";
import { destination_CostCenter, destination_GeneralLedger, destination_MaterialMgmt, destination_ProfitCenter, destination_BankKey, destination_IDM } from "../../destinationVariables";
import { setIwmMyTask } from "../../app/initialDataSlice";
import { clearGeneralLedger } from "../../app/generalLedgerTabSlice";
import { clearPaginationData } from "@app/paginationSlice"
import { setChangeFieldRows } from "@app/payloadSlice";
import { API_CODE, DECISION_TABLE_NAME, ERROR_MESSAGES, INFO_MESSAGES, LOCAL_STORAGE_KEYS } from "@constant/enum";
import { END_POINTS } from "@constant/apiEndPoints";
import useLogger from "@hooks/useLogger";
import ReusableDialog from "@components/Common/ReusableDialog";
import { setChangeFieldRowsDisplay, setFCRows } from "../../app/payloadslice";
import { getRequestTypeFromId, setLocalStorage } from "@helper/helper";
import useDisplayData from "@hooks/useDisplayDataDto";
import { useSnackbar } from "@hooks/useSnackbar";

export default function MyTasks() {
  let userData = useSelector((state) => state.userManagement.userData);
  const task = useSelector((state) => state.userManagement.taskData);
  const { customLog } = useLogger();
  const langSelected = useSelector((state) => state.appSettings.language);
  const applicationConfig = useSelector((state) => state.applicationConfig);
  let dispatch = useDispatch();
  const navigate = useNavigate();
  const [rowData, setRowData] = useState({});
  const [userListBySystem, setUserListBySystem] = useState(null);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState("");
  const [messageDialogMessage, setMessageDialogMessage] = useState();
  const [messageDialogSeverity, setMessageDialogSeverity] = useState("");
  const {getDisplayData} = useDisplayData();
  const { showSnackbar } = useSnackbar();

  const DestinationConfig = {
    APPLICATION_NAME: "1784",
    CRUD_API_ENV: "itm",
    DB_TYPE: "hana",
    SERVICE_BASE_URL: [
      {
        Description: "",
        Name: "ITMJavaServices",
        URL: "https://cherryworkproducts-itm-java-dev.cfapps.eu10-004.hana.ondemand.com",
      },
      // {
      //   Description: "",
      //   Name: "IWAServices",
      //   URL: "https://cw-mdg-authentication-dev.cfapps.eu10-004.hana.ondemand.com",
      // },
      {
        Description: "",
        Name: "ConfigServer",
        URL: "https://cherryworkproducts-config-server.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "",
        Name: "WorkNetServices",
        URL: "https://cherryworkproducts-worknet-dev.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "",
        Name: "CrudApiServices",
        URL: "https://cw-caf-crudapi-dev.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "",
        Name: "WorkFormsServices",
        URL: "https://cherrywork-wf-java-qas.cfapps.eu10-004.hana.ondemand.com/workforms",
      },
      {
        Description: "",
        Name: "NotificationServices",
        URL: "https://cherryworkproducts-messaging-dev.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "",
        Name: "ITMGraphServices",
        URL: "https://cherrywork-btp-qas-dashboard.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "Native Workflow Services",
        Name: "NativeWorkflowServices",
        URL: "https://cherryworkproducts-custom-wf-qas.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "Native Workflow UI URL",
        Name: "NativeWorkflowUiUrl",
        URL: "https://cherrywork-native-workflow-qas.cfapps.eu10-004.hana.ondemand.com/native-ui",
      },
      {
        Description: "",
        Name: "OnboardingServices",
        URL: "https://cherryworkproducts-itm-java-qas.cfapps.eu10-004.hana.ondemand.com",
      },
    ],
  };

  const userPrefData = {
    DateTimeFormat: {
      dateTimeFormat: "DD MMM YYYY||HH:mm",
      timeZone: "Asia/Calcutta",
    },
  };

  var MMUrl = {
    "Approver User Task - Mass": `/requestBench/createRequest`,
    "MDM User Task - Mass": `/requestBench/createRequest`,
  };
  var urlsMass = {
    "Approver User Task - Mass": `/masterDataCockpit/materialMaster/massMaterialTableRequestBench`,
    "MDM User Task - Mass": `/masterDataCockpit/materialMaster/massMaterialTableRequestBench`,
  };
  var urls = {
    "Approver User Task": `/masterDataCockpit/materialMaster/displayMaterialDetail/${task?.requestId}`,
    "MDM User Task": `/masterDataCockpit/materialMaster/displayMaterialDetail/${task?.requestId}`,
    "Sales User Task": `/masterDataCockpit/materialMaster/displayMaterialDetail/${task?.requestId}`,
    "Procurement User Task": `/masterDataCockpit/materialMaster/displayMaterialDetail/${task?.requestId}`,
  };

  var costUrlsSun = {
    "Approver User Task": `/masterDataCockpit/sunoco/costCenterSunoco/displayChangeCC/${task?.requestId}`,
    "MDM User Task": `/masterDataCockpit/sunoco/costCenterSunoco/displayChangeCC/${task?.requestId}`,
    "Finance User Task": `/masterDataCockpit/sunoco/costCenterSunoco/displayChangeCC/${task?.requestId}`,
  };
  var costCreateUrlsSun = {
    "Approver User Task": `/masterDataCockpit/sunoco/costCenterSunoco/displaySingleCCSunoco/${task?.requestId}`,
    "MDM User Task": `/masterDataCockpit/sunoco/costCenterSunoco/displaySingleCCSunoco/${task?.requestId}`,
    "Finance User Task": `/masterDataCockpit/sunoco/costCenterSunoco/displaySingleCCSunoco/${task?.requestId}`,
  };
  var costUrls = {
    "Approver User Task": `/masterDataCockpit/sunoco/costCenterSunoco/displayChangeCC/${task?.requestId}`,
    "MDM User Task": `/masterDataCockpit/sunoco/costCenterSunoco/displayChangeCC/${task?.requestId}`,
    "Finance User Task": `/masterDataCockpit/sunoco/costCenterSunoco/displayChangeCC/${task?.requestId}`,
  };
  var costCreateUrls = {
    "Approver User Task": `/masterDataCockpit/sunoco/costCenterSunoco/displaySingleCCSunoco/${task?.requestId}`,
    "MDM User Task": `/masterDataCockpit/sunoco/costCenterSunoco/displaySingleCCSunoco/${task?.requestId}`,
    "Finance User Task": `/masterDataCockpit/sunoco/costCenterSunoco/displaySingleCCSunoco/${task?.requestId}`,
  };
  var costMassCreateUrls = {
    "Approver User Task - Mass": `/masterDataCockpit/costCenter/massCostCenterTableRequestBench/${task?.requestId}`,
    "MDM User Task - Mass": `/masterDataCockpit/costCenter/massCostCenterTableRequestBench/${task?.requestId}`,
    "Finance User Task - Mass": `/masterDataCockpit/costCenter/massCostCenterTableRequestBench/${task?.requestId}`,
  };
  var costCenterMassChangeUrls = {
    "Approver User Task - Mass": `/masterDataCockpit/costCenter/massChangeCostCenterTableRequestBench/${task?.requestId}`,
    "MDM User Task - Mass": `/masterDataCockpit/costCenter/massChangeCostCenterTableRequestBench/${task?.requestId}`,
    "Finance User Task - Mass": `/masterDataCockpit/costCenter/massChangeCostCenterTableRequestBench/${task?.requestId}`,
  };

  var bankKeyUrls = {
    "Approver User Task": `/masterDataCockpit/bankKey/displayBankKey/${task?.requestId}`,
    "MDM User Task": `/masterDataCockpit/bankKey/displayBankKey/${task?.requestId}`,
    "Finance User Task": `/masterDataCockpit/bankKey/displayBankKey/${task?.requestId}`,
  };

  var profitUrls = {
    "Approver User Task": `/masterDataCockpit/sunoco/profitCenterSunoco/displayChangePC/${task?.requestId}`,
    "MDM User Task": `/masterDataCockpit/sunoco/profitCenterSunoco/displayChangePC/${task?.requestId}`,
    "Finance User Task": `/masterDataCockpit/sunoco/profitCenterSunoco/displayChangePC/${task?.requestId}`,
  };
  var massChangeCCPC = {
    "Approver User Task - Mass": `/masterDataCockpit/sunoco/sunocoCCPC/changeMassCCPCRequestBench/${task?.requestId}`,
    "MDM User Task - Mass": `/masterDataCockpit/sunoco/sunocoCCPC/changeMassCCPCRequestBench/${task?.requestId}`,
    "Finance User Task - Mass": `/masterDataCockpit/sunoco/sunocoCCPC/changeMassCCPCRequestBench/${task?.requestId}`,
  };
  var profitUrlsSun = {
    "Approver User Task": `/masterDataCockpit/sunoco/profitCenterSunoco/displayChangePCSunoco/${task?.requestId}`,
    "MDM User Task": `/masterDataCockpit/sunoco/profitCenterSunoco/displayChangePCSunoco/${task?.requestId}`,
    "Finance User Task": `/masterDataCockpit/sunoco/profitCenterSunoco/displayChangePCSunoco/${task?.requestId}`,
  };
  var profitCreateUrlsSun = {
    "Approver User Task": `/masterDataCockpit/sunoco/profitCenterSunoco/displaySinglePCSunRB/${task?.requestId}`,
    "MDM User Task": `/masterDataCockpit/sunoco/profitCenterSunoco/displaySinglePCSunRB/${task?.requestId}`,
    "Finance User Task": `/masterDataCockpit/sunoco/profitCenterSunoco/displaySinglePCSunRB/${task?.requestId}`,
  };
  var profitCreateUrls = {
    "Approver User Task": `/masterDataCockpit/sunoco/profitCenterSunoco/displaySinglePCSunoco/${task?.requestId}`,
    "MDM User Task": `/masterDataCockpit/sunoco/profitCenterSunoco/displaySinglePCSunoco/${task?.requestId}`,
    "Finance User Task": `/masterDataCockpit/sunoco/profitCenterSunoco/displaySinglePCSunoco/${task?.requestId}`,
  };

  var sunocoCCPCCreateUrls = {
    "Approver User Task": `/masterDataCockpit/sunoco/CostandProfitCenter/viewSunocoCostCenter/${task?.requestId}`,
    "MDM User Task": `/masterDataCockpit/sunoco/CostandProfitCenter/viewSunocoCostCenter/${task?.requestId}`,
    "Finance User Task": `/masterDataCockpit/sunoco/CostandProfitCenter/viewSunocoCostCenter/${task?.requestId}`,
  };

  var PCGCreateUrls = {
    "Approver User Task": `/masterDataCockpit/et/hierarchyNodeProfitCenter/displayHierarchyNodeProfitCenter/${task?.requestId}`,
    "MDM User Task": `/masterDataCockpit/et/hierarchyNodeProfitCenter/displayHierarchyNodeProfitCenter/${task?.requestId}`,
    "Finance User Task": `/masterDataCockpit/et/hierarchyNodeProfitCenter/displayHierarchyNodeProfitCenter/${task?.requestId}`,
  };

  var generalLedgerUrls = {
    "Approver User Task": `/masterDataCockpit/generalLedger/displayGeneralLedger/${task?.requestId}`,
    "MDM User Task": `/masterDataCockpit/generalLedger/displayGeneralLedger/${task?.requestId}`,
    "Finance User Task": `/masterDataCockpit/generalLedger/displayGeneralLedger/${task?.requestId}`,
  };
  var generalLedgerUrlsChange = {
    "Approver User Task": `/masterDataCockpit/generalLedger/displayChangeGL/${task?.requestId}`,
    "MDM User Task": `/masterDataCockpit/generalLedger/displayChangeGL/${task?.requestId}`,
    "Finance User Task": `/masterDataCockpit/generalLedger/displayChangeGL/${task?.requestId}`,
  };
  var generalLedgerMassUrls = {
    "Approver User Task - Mass": `/masterDataCockpit/generalLedger/massGLTableRequestBench`,
    "MDM User Task - Mass": `/masterDataCockpit/generalLedger/massGLTableRequestBench`,
    "Finance User Task - Mass": `/masterDataCockpit/generalLedger/massGLTableRequestBench`,
  };
  var generalLedgerMassChangeUrls = {
    "Approver User Task - Mass": `/masterDataCockpit/generalLedger/massChangeGLTableRequestBench`,
    "MDM User Task - Mass": `/masterDataCockpit/generalLedger/massChangeGLTableRequestBench`,
    "Finance User Task - Mass": `/masterDataCockpit/generalLedger/massChangeGLTableRequestBench`,
  };
  var costMassUrls = {
    "Approver User Task - Mass": `/masterDataCockpit/costCenter/massCostCenterTableRequestBench`,
    "MDM User Task - Mass": `/masterDataCockpit/costCenter/massCostCenterTableRequestBench`,
    "Finance User Task - Mass": `/masterDataCockpit/costCenter/massCostCenterTableRequestBench`,
  };

  var profitMassUrls = {
    "Approver User Task - Mass": `/masterDataCockpit/profitCenter/massProfitCenterTableRequestBench`,
    "MDM User Task - Mass": `/masterDataCockpit/profitCenter/massProfitCenterTableRequestBench`,
    "Finance User Task - Mass": `/masterDataCockpit/profitCenter/massProfitCenterTableRequestBench`,
  };
  var profitMassUrlsSun = {
    "Approver User Task - Mass": `/masterDataCockpit/sunoco/profitCenterSunoco/massProfitCenterTableRequestBenchSunoco`,
    "MDM User Task - Mass": `/masterDataCockpit/sunoco/profitCenterSunoco/massProfitCenterTableRequestBenchSunoco`,
    "Finance User Task - Mass": `/masterDataCockpit/sunoco/profitCenterSunoco/massProfitCenterTableRequestBenchSunoco`,
  };

  var bankKeyMassUrls = {
    "Approver User Task - Mass": `/masterDataCockpit/et/bankKey/massBKTableRequestBench`,
    "MDM User Task - Mass": `/masterDataCockpit/et/bankKey/massBKTableRequestBench`,
    "Finance User Task - Mass": `/masterDataCockpit/et/bankKey/massBKTableRequestBench`,
  };
  
  const forwardedToUsers = (forwardedTo) => {
   
    const payload = {
        eventId:"TASK_FORWARDING",
        taskName:forwardedTo?.forwardedTasks.map(user => user.taskDesc).join(','),
        requestId: forwardedTo?.forwardedTasks.map(user => `${user.ATTRIBUTE_1}`).join(','),
        recipientGroup: forwardedTo?.recipientUsers.map(user => user.ownerId).join(','),
        flowType:forwardedTo?.forwardedTasks.map(user => user.ATTRIBUTE_2).join(',')
     
    }
   
     const hSuccess = (res) => {
      customLog(res)
        };
  doAjax(
          `/${destination_MaterialMgmt}/mail/sendMail`,
          "post",
          hSuccess,
          payload
        );
  }

  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };

  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };

  const onTaskClick = async (task) => {
    setLocalStorage(LOCAL_STORAGE_KEYS.CURRENT_TASK, task);
    try {
      if ((task?.taskNature === "Single-User") || (task?.taskNature !== "Single-User" && task?.itmStatus !== "Open")) {
        dispatch(setTaskData(task));
        if (task?.processDisplayName === "Material") {
          if(!task?.ATTRIBUTE_1){
            showSnackbar(INFO_MESSAGES.FETCHING_REQUEST_ID,'info')
          }else if(!task?.ATTRIBUTE_2){
            showSnackbar(INFO_MESSAGES.FETCHING_REQUEST_TYPE,'info')
          }else{
            const displayResponse = await getDisplayData(task?.ATTRIBUTE_1,task?.ATTRIBUTE_2,null,task,null);
            if(displayResponse?.statusCode === API_CODE.STATUS_200){
              navigate(`/requestBench/createRequest?RequestId=${task?.ATTRIBUTE_1 || task?.requestId}&RequestType=${task?.ATTRIBUTE_2 || getRequestTypeFromId(task?.requestId)}`);
            }
          }
          
        }
        dispatch(setHistoryPath({ url: window.location.pathname, module: "ITMWorkbench" }));
      } else {
        setMessageDialogMessage("Kindly claim the task before proceeding");
        setMessageDialogTitle("Claim Task");
        setMessageDialogSeverity("info");
        handleMessageDialogClickOpen();
      }
    } catch (error) {
      customLog(ERROR_MESSAGES?.ERROR_SET_ROLE);
    }
  };
  

  const getRowDataMass = (task) => {
    if (task?.requestId?.includes("ECMN") || task?.requestId?.includes("SCMN")) {
      doAjax(`/${destination_CostCenter}/data/getTaskDetailForWorkSpace?requestId=${task?.requestId?.slice(4)}`, "get", (resData) => {
        var tempData = resData.data;
        var tempUserData = tempData?.map((udata) => {
          return { ...udata, userId: udata?.emailId };
        });
        dispatch(setIwmMyTask(resData));
        var finalData = { ...resData, data: tempUserData };
        setRowData(finalData);
        if (resData?.body?.id)
          navigate(
            `${costMassCreateUrls[task?.taskDesc]}`,
            {
              state: finalData,
            }
          );
      });

    } else if (task?.requestId?.includes("ECMC") || task?.requestId?.includes("SCMC")) {
      doAjax(`/${destination_CostCenter}/data/getTaskDetailForWorkSpace?requestId=${task?.requestId?.slice(4)}`, "get", (resData) => {
        var tempData = resData.data;
        var tempUserData = tempData?.map((udata) => {
          return { ...udata, userId: udata?.emailId };
        });
        dispatch(setIwmMyTask(resData));
        var finalData = { ...resData, data: tempUserData };
        setRowData(finalData);
        if (resData?.body?.id)
          navigate(
            // `${urls[task?.taskDesc]}`
            `${costCenterMassChangeUrls[task?.taskDesc]}`,
            {
              state: finalData,
            }
          );
      });
    } else if (task?.requestId?.includes("EPMN") || task?.requestId?.includes("EPMC")) {
      navigate(
        // `${urls[task?.taskDesc]}`
        `${profitMassUrls[task?.taskDesc]}`,
        {
          state: rowData,
        }
      );
    } else if (task?.requestId?.includes("SPMN") || task?.requestId?.includes("SPMC")) {
      navigate(
        // `${urls[task?.taskDesc]}`
        `${profitMassUrlsSun[task?.taskDesc]}`,
        {
          state: rowData,
        }
      );
    } else if (task?.requestId?.includes("CPMN")) {
      doAjax(`/${destination_CostCenter}/data/getTaskDetailForWorkSpace?requestId=${task?.requestId?.slice(4)}`, "get", (resData) => {
        var tempData = resData.data;
        var tempUserData = tempData?.map((udata) => {
          return { ...udata, userId: udata?.emailId };
        });
        dispatch(setIwmMyTask(resData));
        var finalData = { ...resData, data: tempUserData };
        setRowData(finalData);
        if (resData?.body?.id)
          navigate(
            // `${urls[task?.taskDesc]}`
            `${costMassCreateUrls[task?.taskDesc]}`,
            {
              state: finalData,
            }
          );
      });
    } else if (task?.requestId?.includes("CPMC")) {
      doAjax(`/${destination_CostCenter}/data/getTaskDetailForWorkSpace?requestId=${task?.requestId?.slice(4)}`, "get", (resData) => {
        var tempData = resData.data;
        var tempUserData = tempData?.map((udata) => {
          return { ...udata, userId: udata?.emailId };
        });
        dispatch(setIwmMyTask(resData));
        var finalData = { ...resData, data: tempUserData };
        setRowData(finalData);
        if (resData?.body?.id)
          navigate(
            // `${urls[task?.taskDesc]}`
            `${massChangeCCPC[task?.taskDesc]}`,
            {
              state: finalData,
            }
          );
      });
    } else if (task?.requestId?.includes("GLMN")) {
      dispatch(clearGeneralLedger());
      navigate(
        // `${urls[task?.taskDesc]}`
        `${generalLedgerMassUrls[task?.taskDesc]}`,
        {
          state: rowData,
        }
      );
    } else if (task?.requestId?.includes("GLMC")) {
      dispatch(clearGeneralLedger());
      navigate(
        // `${urls[task?.taskDesc]}`
        `${generalLedgerMassChangeUrls[task?.taskDesc]}`,
        {
          state: rowData,
        }
      );
    } else if (task?.requestId?.includes("NBM") || task?.requestId?.includes("CBM")) {
      navigate(
        // `${urls[task?.taskDesc]}`//
        `${bankKeyMassUrls[task?.taskDesc]}`,
        {
          state: rowData,
        }
      );
    } else {
      navigate(
        // `${urls[task?.taskDesc]}`
        `/requestBench/createRequest`,
        {
          state: rowData,
        }
      );
      // }
    }
  };

  const getRowData = (task) => {
    if (task?.requestId?.includes("ECSC")) {
      doAjax(`/${destination_CostCenter}/data/getTaskDetailForWorkSpace?requestId=${task?.requestId?.slice(4)}`, "get", (resData) => {
        var tempData = resData.data;
        var tempUserData = tempData?.map((udata) => {
          return { ...udata, userId: udata?.emailId };
        });
        dispatch(setIwmMyTask(resData));
        var finalData = { ...resData, data: tempUserData };
        setRowData(finalData);
        console.log("resData", resData);
        if (resData?.body?.id)
          navigate(
            // `${urls[task?.taskDesc]}`
            `${costUrls[task?.taskDesc]}`,
            {
              state: rowData,
            }
          );
      });
    } else if (task?.requestId?.includes("SCSC")) {
      doAjax(`/${destination_CostCenter}/data/getTaskDetailForWorkSpace?requestId=${task?.requestId?.slice(4)}`, "get", (resData) => {
        var tempData = resData.data;
        var tempUserData = tempData?.map((udata) => {
          return { ...udata, userId: udata?.emailId };
        });
        dispatch(setIwmMyTask(resData));
        var finalData = { ...resData, data: tempUserData };
        setRowData(finalData);
        console.log("resData", resData);
        if (resData?.body?.id)
          navigate(
            // `${urls[task?.taskDesc]}`
            `${costUrlsSun[task?.taskDesc]}`,
            {
              state: rowData,
            }
          );
      });
    } else if (task?.requestId?.includes("ECSN")) {
      doAjax(`/${destination_CostCenter}/data/getTaskDetailForWorkSpace?requestId=${task?.requestId?.slice(4)}`, "get", (resData) => {
        var tempData = resData.data;
        var tempUserData = tempData?.map((udata) => {
          return { ...udata, userId: udata?.emailId };
        });
        dispatch(setIwmMyTask(resData));
        var finalData = { ...resData, data: tempUserData };
        setRowData(finalData);
        console.log("resData", resData);
        if (resData?.body?.id)
          navigate(
            // `${urls[task?.taskDesc]}`
            `${costCreateUrls[task?.taskDesc]}`,
            {
              state: rowData,
            }
          );
      });
    } else if (task?.requestId?.includes("SCSN")) {
      doAjax(`/${destination_CostCenter}/data/getTaskDetailForWorkSpace?requestId=${task?.requestId?.slice(4)}`, "get", (resData) => {
        var tempData = resData.data;
        var tempUserData = tempData?.map((udata) => {
          return { ...udata, userId: udata?.emailId };
        });
        dispatch(setIwmMyTask(resData));
        var finalData = { ...resData, data: tempUserData };
        setRowData(finalData);
        console.log("resData", resData);
        if (resData?.body?.id)
          navigate(
            // `${urls[task?.taskDesc]}`
            `${costCreateUrlsSun[task?.taskDesc]}`,
            {
              state: rowData,
            }
          );
      });
    } else if (task?.requestId?.includes("EPSN")) {
      doAjax(`/${destination_ProfitCenter}/data/getTaskDetailForWorkSpace?requestId=${task?.requestId?.slice(4)}`, "get", (resData) => {
        var tempData = resData.data;
        var tempUserData = tempData?.map((udata) => {
          return { ...udata, userId: udata?.emailId };
        });
        dispatch(setIwmMyTask(resData));
        var finalData = { ...resData, data: tempUserData };
        setRowData(finalData);
        console.log("resData", resData);
        if (resData?.body?.id)
          navigate(
            // `${urls[task?.taskDesc]}`
            `${profitCreateUrls[task?.taskDesc]}`,
            {
              state: rowData,
            }
          );
      });
    } else if (task?.requestId?.includes("EPSC")) {
      doAjax(`/${destination_ProfitCenter}/data/getTaskDetailForWorkSpace?requestId=${task?.requestId?.slice(4)}`, "get", (resData) => {
        var tempData = resData.data;
        var tempUserData = tempData?.map((udata) => {
          return { ...udata, userId: udata?.emailId };
        });
        dispatch(setIwmMyTask(resData));
        var finalData = { ...resData, data: tempUserData };
        setRowData(finalData);
        console.log("resData", resData);
        if (resData?.body?.id)
          navigate(
            // `${urls[task?.taskDesc]}`
            `${profitUrls[task?.taskDesc]}`,
            {
              state: rowData,
            }
          );
      });
    } else if (task?.requestId?.includes("SPSN")) {
      doAjax(`/${destination_ProfitCenter}/data/getTaskDetailForWorkSpace?requestId=${task?.requestId?.slice(4)}`, "get", (resData) => {
        var tempData = resData.data;
        var tempUserData = tempData?.map((udata) => {
          return { ...udata, userId: udata?.emailId };
        });
        dispatch(setIwmMyTask(resData));
        var finalData = { ...resData, data: tempUserData };
        setRowData(finalData);
        console.log("resData", resData);
        if (resData?.body?.id)
          navigate(
            // `${urls[task?.taskDesc]}`
            `${profitCreateUrlsSun[task?.taskDesc]}`,
            {
              state: rowData,
            }
          );
      });
    } else if (task?.requestId?.includes("SPSC")) {
      doAjax(`/${destination_ProfitCenter}/data/getTaskDetailForWorkSpace?requestId=${task?.requestId?.slice(4)}`, "get", (resData) => {
        var tempData = resData.data;
        var tempUserData = tempData?.map((udata) => {
          return { ...udata, userId: udata?.emailId };
        });
        dispatch(setIwmMyTask(resData));
        var finalData = { ...resData, data: tempUserData };
        setRowData(finalData);
        console.log("resData", resData);
        if (resData?.body?.id)
          navigate(
            // `${urls[task?.taskDesc]}`
            `${profitUrlsSun[task?.taskDesc]}`,
            {
              state: rowData,
            }
          );
      });
    } else if (task?.requestId?.includes("CPSN")) {
      doAjax(`/${destination_CostCenter}/data/getTaskDetailForWorkSpaceForCombo?requestId=${task?.requestId?.slice(4)}`, "get", (resData) => {
        var tempData = resData.data;
        var tempUserData = tempData?.map((udata) => {
          return { ...udata, userId: udata?.emailId };
        });
        dispatch(setIwmMyTask(resData));
        var finalData = { ...resData, data: tempUserData };
        setRowData(finalData);
        console.log("resData", resData, tempUserData);
        if (resData?.body?.id)
          navigate(
            // `${urls[task?.taskDesc]}`
            `${sunocoCCPCCreateUrls[task?.taskDesc]}`,
            {
              state: rowData,
            }
          );
      });
    } else if (task?.requestId?.includes("CPSC")) {
      doAjax(`/${destination_CostCenter}/data/getTaskDetailForWorkSpaceForCombo?requestId=${task?.requestId?.slice(4)}`, "get", (resData) => {
        var tempData = resData.data;
        var tempUserData = tempData?.map((udata) => {
          return { ...udata, userId: udata?.emailId };
        });
        dispatch(setIwmMyTask(resData));
        var finalData = { ...resData, data: tempUserData };
        setRowData(finalData);
        console.log("resData", resData);
        if (resData?.body?.id)
          navigate(
            // `${urls[task?.taskDesc]}`
            `${profitUrls[task?.taskDesc]}`,
            {
              state: rowData,
            }
          );
      });
    } else if (task?.requestId?.includes("PGSN")) {
      doAjax(`/${destination_ProfitCenter}/data/getTaskDetailForWorkSpace?requestId=${task?.requestId?.slice(4)}`, "get", (resData) => {
        var tempData = resData.data;
        var tempUserData = tempData?.map((udata) => {
          return { ...udata, userId: udata?.emailId };
        });
        dispatch(setIwmMyTask(resData));
        var finalData = { ...resData, data: tempUserData };
        setRowData(finalData);
        console.log("resData", resData, tempUserData);
        if (resData?.body?.id)
          navigate(
            // `${urls[task?.taskDesc]}`
            `${PCGCreateUrls[task?.taskDesc]}`,
            {
              state: rowData,
            }
          );
      });
    } else if (task?.requestId?.includes("PGSC")) {
      doAjax(`/${destination_ProfitCenter}/data/getTaskDetailForWorkSpace?requestId=${task?.requestId?.slice(4)}`, "get", (resData) => {
        var tempData = resData.data;
        var tempUserData = tempData?.map((udata) => {
          return { ...udata, userId: udata?.emailId };
        });
        dispatch(setIwmMyTask(resData));
        var finalData = { ...resData, data: tempUserData };
        setRowData(finalData);
        if (resData?.body?.id)
          navigate(`${PCGCreateUrls[task?.taskDesc]}`, {
            state: rowData,
          });
      });
    } else if (task?.requestId?.includes("GLSN")) {
      doAjax(`/${destination_GeneralLedger}/data/getTaskDetailForWorkSpace?requestId=${task?.requestId?.slice(4)}`, "get", (resData) => {
        var tempData = resData.data;
        var tempUserData = tempData?.map((udata) => {
          return { ...udata, userId: udata?.emailId };
        });
        dispatch(setIwmMyTask(resData));
        var finalData = { ...resData, data: tempUserData };
        setRowData(finalData);
        console.log("resData", resData);
        if (resData?.body?.id)
          navigate(
            // `${urls[task?.taskDesc]}`
            `${generalLedgerUrls[task?.taskDesc]}`,
            {
              state: rowData,
            }
          );
      });
    } else if (task?.requestId?.includes("GLSC")) {
      doAjax(`/${destination_GeneralLedger}/data/getTaskDetailForWorkSpace?requestId=${task?.requestId?.slice(4)}`, "get", (resData) => {
        var tempData = resData.data;
        var tempUserData = tempData?.map((udata) => {
          return { ...udata, userId: udata?.emailId };
        });
        dispatch(setIwmMyTask(resData));
        var finalData = { ...resData, data: tempUserData };
        setRowData(finalData);
        console.log("resData", resData);
        if (resData?.body?.id)
          navigate(
            // `${urls[task?.taskDesc]}`
            `${generalLedgerUrlsChange[task?.taskDesc]}`,
            {
              state: rowData,
            }
          );
      });
    } else if (task?.requestId?.includes("NBS") || task?.requestId?.includes("CBS")) {
      doAjax(`/${destination_BankKey}/data/getTaskDetailForWorkSpace?requestId=${task?.requestId?.slice(3)}`, "get", (resData) => {
        var tempData = resData.data;
        var tempUserData = tempData?.map((udata) => {
          return { ...udata, userId: udata?.emailId };
        });
        dispatch(setIwmMyTask(resData));
        var finalData = { ...resData, data: tempUserData };
        setRowData(finalData);
        console.log("resData", resData);
        if (resData?.body?.id)
          navigate(
            // `${urls[task?.taskDesc]}`
            `${bankKeyUrls[task?.taskDesc]}`,
            {
              state: rowData,
            }
          );
      });
    } else {
      console.log("eadfjsasdfnksdh");
      doAjax(`/${destination_MaterialMgmt}/data/getTaskDetailForWorkSpace?requestId=${task?.requestId?.slice(3)}`, "get", (resData) => {
        var tempData = resData.data;
        var tempUserData = tempData?.map((udata) => {
          return { ...udata, userId: udata?.emailId };
        });
        dispatch(setIwmMyTask(resData));
        var finalData = { ...resData, data: tempUserData };
        setRowData(finalData);
        console.log("resData", resData);
        if (resData?.body?.id)
          navigate(
            // `${urls[task?.taskDesc]}`
            `${urls[task?.taskDesc]}`,
            {
              state: rowData,
            }
          );
      });
    }
  };
  const fetchFilterViewList = () => {
    console.log("fetchFilterView");
  };
  const clearFilterView = () => {
    console.log("clearFilterView");
  };
  // Might use later or will remove after a week
  // const fetchUserRawData = () => {
  //   doAjax(`/${destination_MaterialMgmt}/workflow/fetchAllIASUsersForward`, "get", (resData) => {
  //     var tempData = resData.data;
  //     var tempUserData = tempData?.map((udata) => {
  //       return { ...udata, userId: udata?.emailId };
  //     });
  //     var finalData = { ...resData, data: tempUserData };
  //     setUserRawData(finalData);
  //     setUserListBySystem({ MDG: [...finalData.data] });
  //   });
  // };

  // const fetchUserGroupRawData = () => {
  //   doAjax(`/${destination_MaterialMgmt}/workflow/fetchAllIASGroups`, "get", (resData) => {
  //     var tempData = resData.data;
  //     var tempGroupData = tempData?.map((gData) => {
  //       return { ...gData, groupName: gData?.name };
  //     });
  //     var finalData = { ...resData, data: tempGroupData };
  //     setUserGroupRawData(finalData);
  //   });
  // };

  const onActionComplete = (successFlag, taskPayload) => {
    console.log("Success flag.", successFlag);
    console.log("Task Payload.", taskPayload);
  };

  useEffect(() => {
    setIwmMyTask({});
    dispatch(clearPaginationData())
    dispatch(setChangeFieldRows([]));
    dispatch(setChangeFieldRowsDisplay({}));
    dispatch(setTaskData({}));
    dispatch(setFCRows([]));
    //NOTE: Might use later
    // fetchUserRawData();
    // fetchUserGroupRawData();
  }, []);
  return (
    <div style={{ width: "calc(100vw - 105px)", height: "calc(100vh-130px)" }} className={"workspaceOverride"}>
       <WorkspaceComponent
       token={"********************************************************************************************************************************************************************************************************************************************************************************.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.b0BWWn_rlTWe3h1Trx-MN3nDcZIU9i9HCr1y2h4bed4quLD6yHN2O9xzmQunvA6DQz0K898LxjHI9nFVCXs8m_7Aas00UiJ-vCi3vhwqLAOB77IlNbBV_OufjMShrkzmr2VHpyHpjVHg5RbJj6zFHleXxXXmQJkiefcaNf-5l-n6Tqxad16V9wHkKaMIFLAbx1tKjNyCPq-yX9otIE7gByKFPG_ijmJ15u_kMuGZgE6fQ5tbNRjNB-HlsDDvmXDJ9866G6j8q_YIYcuUO6yX083v4hwwaY4Y-5PqYMo7v1mIdK1i0UjcAl9853So4JcHcV5iLl0waC8ht5nO85oVFg"}
       configData={configData}
       destinationData={DestinationConfig}
       userData={{ ...userData, user_id: userData?.emailId }}
       userPreferences={userPrefData} //Not needed check
       userPermissions={userPermissions}
       userList={{}}
       groupList={{}}
       languageTranslationData={getLanguageTranslationData(langSelected)}
       userListBySystem={userListBySystem}
       useWorkAccess={
         applicationConfig.environment === "localhost" ? true : false
       } 
       useConfigServerDestination={
         applicationConfig.environment === "localhost" ? true : false
       }
       // inboxTypeKey={task?.inboxTypeKey}
       // workspaceLabel={task?.workspaceLabel}
       inboxTypeKey={"MY_TASKS"}
       workspaceLabel={"Open Tasks"}
       workspaceFiltersByAPIDriven={false}
       subInboxTypeKey={null}
       cachingBaseUrl={baseUrl_ITMJava}
       onTaskClick={onTaskClick}
       // onTaskLinkClick={onTaskLinkClick} 
       onActionComplete={onActionComplete}
       selectedFilterView={null}
       isFilterView={false}
       fetchFilterViewList={fetchFilterViewList}
       savedFilterViewData={[]}
       clearFilterView={clearFilterView}
       filterViewList={[]}
       selectedTabId={null}
       forwardTaskData = {forwardedToUsers}
       userProcess={[]}
       // handleCustomActionApis={handleCustomActionApis}   prev comment
     />

        <ReusableDialog
          dialogState={openMessageDialog}
          openReusableDialog={handleMessageDialogClickOpen}
          closeReusableDialog={handleMessageDialogClose}
          dialogTitle={messageDialogTitle}
          dialogMessage={messageDialogMessage}
          handleDialogConfirm={handleMessageDialogClose}
          dialogOkText={"OK"}
          // handleOk={handleOk}
          // handleExtraButton={handleMessageDialogNavigate}
          dialogSeverity={messageDialogSeverity}
        />
    </div>
  );
}
