import React, { useEffect, useState } from "react";
import {
  Autocomplete,
  Checkbox,
  Grid,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import {
  setCCRequiredFields,
  setSingleCostCenterPayload,
} from "../../../app/costCenterTabsSlice";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { useDispatch, useSelector } from "react-redux";
import { doAjax } from "../../Common/fetchService";
import { destination_CostCenter } from "../../../destinationVariables";
import { setDropDown } from "../../../app/dropDownDataSlice";

export default function FilterFieldTypeCC(props) {
  // console.log("error at ff tab",props.errors );
  const dispatch = useDispatch();
  var keyName = props.field.fieldName
    .replaceAll("(", "")
    .replaceAll(")", "")
    .replaceAll("/", "")
    .replaceAll("-", "")
    .replaceAll(".", "")
    .replaceAll("%", "")
    .split(" ")
    .join("");
  let errorFields = useSelector((state) => state.costCenter.errorFields);
  const valueFromPayload = useSelector(
    (state) => state.costCenter.singleCCPayload
  );
  console.log("valuesfrompayload", valueFromPayload.Description);
  const getCurrency = (value) => {
    // console.log("value",value.code);
    const hSuccess = (data) => {
      console.log("value", data);
      dispatch(setDropDown({ keyName: "Currency", data: data.body }));
    };
    const hError = (error) => {
      console.log(error, "error in dojax");
    };
    doAjax(
      `/${destination_CostCenter}/data/getCurrency?companyCode=${value?.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getRegion = (value) => {
    // console.log("value",value.code);
    const hSuccess = (data) => {
      console.log("value", data);
      dispatch(setDropDown({ keyName: "Region", data: data.body }));
    };
    const hError = (error) => {
      console.log(error, "error in dojax");
    };
    doAjax(
      `/${destination_CostCenter}/data/getRegionBasedOnCountry?country=${value?.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  useEffect(() => {
    if (
      props?.field?.visibility === "0" ||
      props?.field?.visibility === "Required"
    ) {
      dispatch(setCCRequiredFields(keyName));
    }
  }, []);
  console.log('test',Object.keys(valueFromPayload).length)
  const dropDownData = useSelector((state) => state.AllDropDown.dropDown);
  if (props.field?.fieldType === "Input") {
    return (
      <Grid item md={2}>
        {props.field.visibility === "Hidden" ? null : (
          <Stack>
            <Typography variant="body2" color="#777">
              {props.field.fieldName}
              {props.field.visibility === "Required" ||
              props.field.visibility === "0" ? (
                <span style={{ color: "red" }}>*</span>
              ) : (
                ""
              )}
            </Typography>
            <TextField
              size="small"
              // disabled
              type={props.field.dataType === "QUAN" ? "number" : ""}
              placeholder={`Enter ${props.field.fieldName}`}
              // error={errorFields.includes(
              //   props?.keyName
              // )}
              inputProps={{ maxLength: props.field.maxLength }}
              value={valueFromPayload[keyName]}
              onChange={(e, value) => {
                const newValue = e.target.value;
                if (Object.keys(valueFromPayload).length > 0) {
                  console.log('0')
                  if (newValue.length > 0 && newValue[0] === " ") {
                    console.log('1')
                    dispatch(setSingleCostCenterPayload({ keyName: keyName,data:newValue.trimStart()}));
                  } else{
                    console.log('2')
                    dispatch(
                      setSingleCostCenterPayload({
                        keyName: keyName,
                        data: newValue.toUpperCase(),
                      })
                    );
                  }
                }else{
                  console.log('3')
                  dispatch(
                    setSingleCostCenterPayload({
                      keyName: keyName,
                      data: newValue.trimStart(),
                    }))
                }
              }}
              required={
                props.field.visibility === "Required" ||
                props.field.visibility === "0"
              }
              error={errorFields.includes(keyName)}
            />
          </Stack>
        )}
      </Grid>
    );
  } else if (props.field?.fieldType === "Drop Down") {
    return (
      <Grid item md={2}>
        {props.field.visibility === "Hidden" ? null : (
          <Stack>
            <Typography variant="body2" color="#777">
              {props.field.fieldName}
              {props.field.visibility === "Required" ||
              props.field.visibility === "0" ? (
                <span style={{ color: "red" }}>*</span>
              ) : (
                ""
              )}
            </Typography>
            <Autocomplete
              sx={{ height: "31px" }}
              // disabled
              fullWidth
              size="small"
              value={valueFromPayload[keyName]}
              onChange={(e, value) => {
                if (props.field.fieldName === "Company Code") {
                  getCurrency(value);
                }
                if (props.field.fieldName === "Country/Reg") {
                  getRegion(value);
                }
                // if(disableArray.filter(item=>item == option.code).length == 0){
                dispatch(
                  setSingleCostCenterPayload({
                    keyName: keyName,
                    data: value,
                  })
                );
                // }
              }}
              options={dropDownData[keyName] ?? []}
              required={
                props.field.visibility === "0" ||
                props.field.visibility === "Required"
              }
              getOptionLabel={(option) => `${option?.code} - ${option?.desc}`}
              renderOption={(props, option) => (
                <li {...props}>
                  <Typography style={{ fontSize: 12 }}>
                    {option?.code} - {option?.desc}
                  </Typography>
                </li>
              )}
              renderInput={(params) => (
                <TextField
                  {...params}
                  variant="outlined"
                  placeholder={`Select ${props.field.fieldName}`}
                  error={errorFields.includes(keyName)}
                />
              )}
            />
          </Stack>
        )}
      </Grid>
    );
  } else if (props.field?.fieldType === "Radio Button") {
    return (
      <Grid item md={2}>
        <Typography variant="body2" color="#777">
          {props.field.fieldName}
          {props.field.visibility === "Required" ||
          props.field.visibility === "0" ? (
            <span style={{ color: "red" }}>*</span>
          ) : (
            ""
          )}
        </Typography>
        <Checkbox
          sx={{ padding: 0 }}
          checked={valueFromPayload[keyName] == true}
          // required={errorFields.includes(props?.keyName)}
          onChange={(e) => {
            dispatch(
              setSingleCostCenterPayload({
                keyName: keyName,
                data: e.target.checked,
              })
            );
          }}
        />
      </Grid>
    );
  } else if (props.field?.fieldType === "Calendar") {
    return (
      <Grid item md={2}>
        <Stack>
          <Typography variant="body2" color="#777">
            {props.field.fieldName}
            {props.field.visibility === "Required" ||
            props.field.visibility === "0" ? (
              <span style={{ color: "red" }}>*</span>
            ) : (
              ""
            )}
          </Typography>

          <LocalizationProvider dateAdapter={AdapterDateFns}>
            {/* <DemoContainer components={["DatePicker"]}> */}
            <DatePicker
              slotProps={{ textField: { size: "small" } }}
              value={valueFromPayload[keyName]}
              onChange={(newValue) =>
                dispatch(
                  setSingleCostCenterPayload({
                    keyName: keyName,
                    data: "/Date(" + Date.parse(newValue) + ")/",
                  })
                )
              }
              required={
                props.field.visibility === "0" ||
                props.field.visibility === "Required"
              }
            />
            {/* </DemoContainer> */}
          </LocalizationProvider>
        </Stack>
      </Grid>
    );
  }
}
