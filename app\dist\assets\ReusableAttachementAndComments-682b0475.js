import{q as T,r as l,s as Le,u as $e,e as Ne,l as C,j as s,dc as p,a,F as _,t as de,dd as ze,de as We,V as Ue,aF as je,T as h,aG as He,I as ce,W as Ge,x as X,a5 as ue,cc as Ke,cb as Je,z as Ve,a_ as Xe,K as pe,bq as K,$ as J,df as Ye,dg as V,a4 as he,dh as Qe,di as ge,b as Ze,dj as et}from"./index-75c1660a.js";import{d as tt}from"./CloudUpload-d5d09566.js";import{i as me}from"./utilityImages-067c3dc2.js";import{d as at}from"./Add-62a207fb.js";import{d as rt}from"./Delete-1d158507.js";const nt=({sidebyside:M=!1,getAttachmentshook:Y=()=>{},processName:Q="",artifactId:m="",artifactName:S="",poNumber:A,isAnAttachment:O,promptAction_Functions:b,attachmentType:k})=>{var se;const L=T(e=>{var t,r;return(r=(t=e.request)==null?void 0:t.requestHeader)==null?void 0:r.requestType}),$=T(e=>{var t,r;return(r=(t=e==null?void 0:e.userManagement)==null?void 0:t.taskData)==null?void 0:r.ATTRIBUTE_2}),N=T(e=>{var t,r;return(r=(t=e.payload)==null?void 0:t.payloadData)==null?void 0:r.RequestType}),[w,D]=l.useState(!1),[c,f]=l.useState(!1),[n,B]=l.useState([]),[o,x]=l.useState([]),[z,R]=l.useState(!1),[W,ot]=l.useState(["Others"]),[Z,ee]=l.useState(!1),[fe,E]=l.useState(!1),[xe,I]=l.useState(""),[ye,U]=l.useState("success"),te=Le(),Ce=$e(),Se=new URLSearchParams(Ce.search).get("reqBench");let j=T(e=>e.userManagement.userData);const y=T(e=>e.payload.payloadData),P=T(e=>{var t,r;return(r=(t=e==null?void 0:e.payload)==null?void 0:t.dynamicKeyValues)==null?void 0:r.childRequestHeaderData}),{t:u}=Ne();let ae={other:C.attachmentDialog.other,"order change":C.attachmentDialog.orderChange},re=e=>ae[e]?ae[e]:C.attachmentDialog.orderChange,H=()=>{R(!1),B([])};const be=()=>{E(!1),I("")};let ne=()=>{R(!0)},Be=e=>a(_,{children:e==null?void 0:e.map((t,r)=>{var g,i,v,F;return s("div",{style:{minWidth:"5rem",minHeight:"6rem",height:"6.5rem",width:"5.4rem",borderRadius:".5rem",margin:"0 .5rem",border:`1px solid ${re((g=t.metaData)==null?void 0:g.toLowerCase())}`,overflow:"hidden",boxShadow:"0px 6px 6px -3px rgba(0,0,0,0.2)",position:"relative"},children:[a(p,{sx:{height:"1rem",backgroundColor:re((i=t.metaData)==null?void 0:i.toLowerCase()),display:"flex",justifyContent:"center",overflow:"hidden"},children:a(h,{sx:{fontSize:"10px",width:"100%",textAlign:"center"},children:t==null?void 0:t.metaData})}),a(p,{sx:{display:"flex",justifyContent:"center",alignItems:"center",padding:"0.6rem"},children:a("img",{style:{width:"1.6rem",height:"1.6rem"},src:me[((v=t==null?void 0:t.name)==null?void 0:v.split(".")[1])||((F=t==null?void 0:t.fileName)==null?void 0:F.split(".")[1])]})}),a(p,{sx:{overflow:"hidden"},children:a(h,{variant:"body2",sx:{padding:".3rem",color:"grey",textAlign:"center",textOverflow:"ellipsis",overflow:"hidden",whiteSpace:"noWrap",height:"2rem"},children:(t==null?void 0:t.name)||(t==null?void 0:t.fileName)})})]},r)})});const Te=e=>{e.preventDefault(),D(!0)},De=()=>{D(!1)},Re=e=>{e.preventDefault(),D(!1);const t=Array.from(e.dataTransfer.files);if(n.length+t.length>5){b.handleOpenPromptBox("Warning",{title:"Warning",message:ue.NUMBER_OF_FILES_LIMIT,severity:"warning",cancelButton:!1});return}oe(t)},ve=()=>{document.getElementById("fileButton").click()},oe=e=>{let t=[];e.forEach(r=>{r.id=Xe(),t.push(r)}),B(r=>[...r,...t])},Ae=()=>{let e=t=>{var g;let r=[];(g=t==null?void 0:t.documentDetailDtoList)==null||g.forEach(i=>{r.push({id:i.documentId,docType:i.fileType,name:i==null?void 0:i.fileName,uploadedBy:i.createdBy,metaData:i.metadataTag??""})}),x(r)};if(S==="RequestBench"){const r=(P==null?void 0:P.ChildRequestId)||(y==null?void 0:y.childRequestId)?`/${K}/${J.TASK_ACTION_DETAIL.GET_CHILD_DOCS}/${m}`:`/${K}/${J.TASK_ACTION_DETAIL.GET_DOCS}/${m}`;pe(r,"get",e)}},q=L||$||N;let Oe=()=>{if(!Z&&n[0]){let e=[...n];(()=>{f(!0);const r=new FormData;let g={};W==null||W.forEach(d=>{g[d.metadataTag]=d.id});let i=[];e==null||e.forEach(d=>{({...d},d.name),r.append("files",d,d.name),i.push(d.name)}),te(Ye(m));const{RequestId:v,RequestType:F,childRequestId:le}=y||{},{ChildRequestId:ie}=P||{},Ie=[he.CHANGE,he.CHANGE_WITH_UPLOAD].includes(F),Pe=v?V(q,v):null,qe=Ie?ie?V(q,ie):null:le?V(q,le):null,G={artifactId:m,createdBy:j==null?void 0:j.emailId,artifactType:S,attachmentType:k,requestId:Pe,childRequestId:qe,fileName:i.join("$^$"),requestType:q};O&&(G.isAnAttachment=O),A&&S!=="Purchase Order"&&(G.poNumber=A),r.append("doc",JSON.stringify(G));let Fe=d=>{te(Qe(k)),d.responseMessage.status==="Failure"?(f(!1),U("error"),I(`${u(ge.UPLOAD_FAILED)}`),E(!0)):(d.responseMessage.status==="Success"||d.responseMessage.status==="Partial Success")&&(Y(),U("success"),I(`Documents for ${S} ${m} uploaded successfully `),E(!0),H(),f(!1),x(Me=>[...Me,...n]))},_e=d=>{f(!1),U("error"),I(`${u(ge.ALT_UPLOAD_ERROR)}`),E(!0)};pe(`/${K}/${J.DMS_API.UPLOAD_DOCUMENT}`,"postformdata",Fe,_e,r)})();return}n[0]||b.handleOpenPromptBox("Warning",{title:"Warning",message:u("Please add some files to upload"),severity:"warning",cancelButton:!1})},ke=()=>{H()},we=e=>{let t=n.filter(r=>r.id!==e);B(t)},Ee=()=>{let e=0;n.forEach(t=>{e+=t.length}),e>5e9?(b.handleOpenPromptBox("ERROR",{title:"Warning",message:u("Files size excceded"),severity:"warning",cancelButton:!1}),ee(!0)):ee(!1)};return l.useEffect(()=>{Ee()},[n]),l.useEffect(()=>{Ae()},[]),s(_,{children:[s(p,{children:[a(p,{sx:{display:"flex",flexDirection:"row"},children:M===!1?s(_,{children:[a(p,{sx:{display:"flex",flexDirection:"row",margin:"1rem 0",maxWidth:"50vw",overflowX:"auto",position:"relative",minHeight:"7rem"},children:o[0]&&Be(o)}),a(p,{onClick:ne,style:{width:"5rem",maxWidth:"5rem",height:"6.5rem",borderRadius:".5rem",backgroundColor:"#eae9ff",padding:"1rem",display:"flex",justifyContent:"center",alignItems:"center",margin:"1rem .5rem",boxShadow:"0px 2px 4px -1px rgba(0,0,0,0.2),0px 4px 5px 0px rgba(0,0,0,0.14),0px 1px 10px 0px rgba(0,0,0,0.12)",cursor:"pointer"},children:a(at,{color:"primary",sx:{fontSize:24}})})]}):a(p,{display:"flex",justifyContent:"center",alignItems:"center",sx:{mr:1},children:a(de,{variant:"contained",component:"label",onClick:ne,startIcon:a(ze,{}),disabled:Se&&!((se=We)!=null&&se.includes(y==null?void 0:y.RequestStatus)),sx:{backgroundColor:"#1976d2",color:"#fff",textTransform:"capitalize",borderRadius:"5px",padding:"10px 20px","&:hover":{backgroundColor:"#1565c0"},boxShadow:"0px 4px 10px rgba(0, 0, 0, 0.2)"},children:u("Upload File")})})}),z&&s(Ue,{fullWidth:!0,maxWidth:"sm",open:!0,onClose:H,sx:{"& .MuiDialog-paper":{borderRadius:"12px",padding:"1rem"},overflow:"hidden"},children:[s(je,{sx:{padding:"1rem 1.5rem"},children:[a(h,{variant:"h6",sx:{fontWeight:500},children:u("Add New Attachment")}),a(ce,{"aria-label":"close",onClick:ke,sx:e=>({position:"absolute",right:12,top:10,color:e.palette.grey[500]}),children:a(He,{})})]}),s(Ge,{sx:{padding:"1.5rem",overflow:"hidden"},children:[s(p,{className:`dropzone ${w?"dragover":""}`,sx:{width:"100%",border:`2px dashed ${w?"#3b30c8":"#d0d5dd"}`,borderRadius:"8px",padding:"2rem",backgroundColor:w?"#f8f9ff":"#fafbff",transition:"all 0.3s ease",cursor:"pointer",minHeight:"200px",display:"flex",alignItems:"center",justifyContent:"center"},onDragOver:Te,onDragLeave:De,onDrop:Re,children:[s(X,{alignItems:"center",spacing:1,children:[a(tt,{sx:{fontSize:48,color:"#3b30c8"}}),a(h,{variant:"body1",sx:{color:"#344054"},children:u("Drag and drop files")}),a(h,{variant:"body2",color:"primary",sx:{cursor:"pointer",textDecoration:"underline","&:hover":{color:"#3b30c8"}},onClick:ve,children:u("or click to upload")})]}),a("input",{id:"fileButton",multiple:!0,accept:".jpeg, .jpg, .xls, .xlsx, .docx, .pdf",type:"file",name:"files",onChange:e=>{const t=Array.from(e.target.files);if(n.length+t.length>5){b.handleOpenPromptBox("Warning",{title:u("Warning"),message:ue.NUMBER_OF_FILES_LIMIT,severity:"warning",cancelButton:!1});return}oe(t)},style:{display:"none"}})]}),n[0]&&a(p,{sx:{maxHeight:"14rem",overflowY:"auto",marginTop:"1.5rem",padding:"1rem",backgroundColor:C.primary.white,borderRadius:"8px",border:"1px solid #eee","&::-webkit-scrollbar-thumb":{backgroundColor:"#888",borderRadius:"3px"}},children:n==null?void 0:n.map((e,t)=>{var r;return s(p,{sx:{display:"flex",alignItems:"center",padding:"0.75rem",borderRadius:"6px",backgroundColor:C.primary.white,marginBottom:"0.5rem",border:"1px solid #eee","&:hover":{backgroundColor:"#f9fafb"}},children:[a("img",{style:{width:"24px",height:"24px",marginRight:"0.75rem"},src:me[(r=e.name)==null?void 0:r.split(".")[1]]}),a(h,{variant:"body1",sx:{flexGrow:1},children:e.name}),Z?s(h,{variant:"body2",color:"error",sx:{marginLeft:"1rem"},children:[parseFloat(e.size/1e6).toFixed(2)," MB"]}):s(h,{sx:{marginLeft:"auto",marginRight:"10%"},color:"gray",children:[parseFloat(e.size/1e6).toFixed(2)," MB"]}),a(ce,{id:`closeBtn-${e.id}`,size:"small",onClick:g=>{g.stopPropagation(),we(e.id)},sx:{marginLeft:"0.5rem",opacity:.8},children:a(rt,{fontSize:"small",color:"error"})})]},t)})}),s(X,{direction:"row",spacing:2,sx:{marginTop:"1.5rem",justifyContent:"flex-end",alignItems:"center"},children:[s(h,{variant:"body2",color:"error",children:["*",u("Max file size 500 MB")]}),a(h,{variant:"body2",sx:{color:C.placeholder.dark},children:u("Maximum 5 files allowed")}),a(de,{variant:"contained",onClick:Oe,sx:{borderRadius:"6px",padding:"0.5rem 1.5rem"},children:u("Upload")})]})]})]}),a(Je,{sx:{color:C.primary.white,zIndex:1400},open:c,children:a(Ke,{color:"inherit"})})]}),a(Ve,{openSnackBar:fe,alertMsg:xe,alertType:ye,handleSnackBarClose:be})]})},pt=({title:M="",module:Y="",artifactName:Q="",getAttachments:m=()=>{},artifactId:S="",poNumber:A="",processName:O="",isAnAttachment:b="",commentOnly:k=!0,view:L=!1,attachmentType:$="",requestId:N="",disableCheck:w})=>{const D=Ze(),[c,f]=l.useState({open:!1,type:"",redirectOnClose:!0,message:"",title:"",severity:"",okButtonText:""}),[n,B]=l.useState(""),o={handleClosePromptBox:()=>{f(x=>({open:!1,type:"",redirectOnClose:!0,message:"",title:"",severity:""})),B("")},handleOpenPromptBox:(x,z={})=>{let R={open:!0,title:"",message:"",okButton:!0,cancelButton:!0,okText:"Ok",cancelText:"Cancel",type:"dialog"};x==="SUCCESS"&&(R.type="snackbar"),B(x),f({...R,...z})},handleCloseAndRedirect:()=>{o.handleClosePromptBox(),D("/purchaseOrder/management")},getCancelFunction:()=>{switch(n){default:return()=>{o.handleClosePromptBox()}}},getCloseFunction:()=>{switch(n){case"COMMENTERROR":default:return x=>{o.handleClosePromptBox()}}},getOkFunction:()=>{switch(n){default:return()=>{o.handleClosePromptBox()}}},getCloseAndRedirectFunction:()=>c.redirectOnClose?o.handleCloseAndRedirect:o.handleClosePromptBox};return s(_,{children:[a(et,{type:c.type,promptState:c.open,setPromptState:o.handleClosePromptBox,onCloseAction:o.getCloseFunction(),promptMessage:c.message,dialogSeverity:c.severity,dialogTitleText:c.title,handleCancelButtonAction:o.getCancelFunction(),cancelButtonText:c.cancelText,showCancelButton:c.cancelButton,handleSnackBarPromptClose:o.getCloseAndRedirectFunction(),handleOkButtonAction:o.getOkFunction(),okButtonText:c.okButtonText,showOkButton:c.okButton}),a(X,{children:k&&a(nt,{sidebyside:!0,view:L,useMetaData:!1,title:M,artifactName:Q,artifactId:S,processName:O,poNumber:A,promptAction_Functions:o,isAnAttachment:b,attachmentType:$,requestId:N,getAttachmentshook:m})})]})};export{pt as R};
