import {
  Backdrop,
  BottomNavigation,
  Box,
  Button,
  Card,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  Paper,
  Stack,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import {
  button_Outlined,
  iconButton_SpacingSmall,
  button_Primary,
  outermostContainer,
  outermostContainer_Information,
} from "../../Common/commonStyles";
import CloseIcon from "@mui/icons-material/Close";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import ReusableTable from "../../Common/ReusableTable";
import { useLocation, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { checkIwaAccess, idGenerator } from "../../../functions";
import {
  destination_DocumentManagement,
  destination_GeneralLedger,
} from "../../../destinationVariables";
import { setMultipleGLData } from "../../../app/generalLedgerTabSlice";
import { doAjax } from "../../Common/fetchService";
import ReusableSnackBar from "../../Common/ReusableSnackBar";
import lookup from "../../../data/lookup.json";
import { setDropDown } from "../../../app/dropDownDataSlice";
import ReusableAttachementAndComments from "../../Common/ReusableAttachmentAndComments/ReusableAttachementAndComments";
import LoadingComponent from "../../Common/LoadingComponent";
import ReusableDialog from "../../Common/ReusableDialog";
import AttachFileOutlinedIcon from "@mui/icons-material/AttachFileOutlined";
import {
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineItem,
  TimelineSeparator,
  timelineItemClasses,
} from "@mui/lab";
import moment from "moment/moment";
import { Timeline } from "rsuite";
import { CheckCircleOutlineOutlined } from "@mui/icons-material";
const MassGLTableRequestBench = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [value, setValue] = useState("1");
  const [selectedRows, setSelectedRows] = useState([]);
  let [factorsArray, setFactorsArray] = useState([]);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const location = useLocation();
  const massProfitRowData = location.state;
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [messageDialogExtra, setMessageDialogExtra] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState(false);
  const [messageDialogSeverity, setMessageDialogSeverity] = useState(false);
  const [messageDialogOK, setMessageDialogOK] = useState(true);
  const [successMsg, setsuccessMsg] = useState(false);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [openCorrectionDialog, setOpenCorrectionDialog] = useState(false);
  const [openRemarkDialog, setOpenRemarkDialog] = useState(false);
  const [remarks, setRemarks] = useState("");
  const [testRun, setTestRun] = useState(true);
  const [submitForReviewDisabled, setSubmitForReviewDisabled] = useState(true);
  const [validationStatus, setValidationStatus] = useState(true);
  const [approveDisabled, setApproveDisabled] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [attachments, setAttachments] = useState([]);
  const [comments, setComments] = useState([]);
  const [validateFlag, setValidateFlag] = useState(false);
  const [blurLoading, setBlurLoading] = useState(false);
  const [glNumber, setGlNumber] = useState("");
  const [dialogOpen, setDialogOpen] = useState(false);
  const [profitValidationError, setProfitValidationErrors] = useState([]);
  const [directMatchedProfitCenters, setDirectMatchedProfitCenters] = useState(
    []
  );
  console.log("mouse", selectedRows);
  let task = useSelector((state) => state.userManagement.taskData);
  let userData = useSelector((state) => state.userManagement.userData);
  let iwaAccessData = useSelector(
    (state) => state.userManagement.entitiesAndActivities?.["General Ledger"]
  );
  let taskRowDetails = useSelector((state) => state.userManagement.taskData);
  const appSettings = useSelector((state) => state.appSettings);
  const MultipleProfitCenter = useSelector(
    (state) => state.generalLedger.MultipleGLData
  );
  const [apiCount, setApiCount] = useState(0);
  const fetchDynamicApiData = (keyName, endPoint) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: keyName, data: data.body }));
      // setIsLoading(false);
      setApiCount((prev) => prev + 1);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/${endPoint}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getAllLookups = () => {
    lookup?.generalLedger?.map((item) => {
      fetchDynamicApiData(item?.keyName, item?.endPoint);
    });
  };
  const loaderCount = () => {
    if (apiCount == lookup?.generalLedger?.length) {
      setIsLoading(false);
    } else {
      setIsLoading(true);
    }
  };
  useEffect(() => {
    loaderCount();
  }, [apiCount]);
  useEffect(() => {
    getAllLookups();
  }, []);
  const handleSnackBarOpen = () => {
    setopenSnackbar(true);
  };
  const handleSnackBarClose = () => {
    if (validateFlag) {
      setopenSnackbar(false);
      setValidateFlag(false);
    } else {
      setopenSnackbar(false);
      navigate("/masterDataCockpit/generalLedger");
    }
  };
  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };

  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };

  const handleMessageDialogNavigate = () => {
    // navigate("/masterDataCockpit/materialMaster/materialSingle");
  };
  const handleOpenDialog = () => {
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const attachmentColumns = [
    {
      field: "id",
      headerName: "Document ID",
      flex: 1,
      hide: true,
    },
    {
      field: "docType",
      headerName: "Type",
      flex: 1,
    },
    {
      field: "docName",
      headerName: "Document Name",
      flex: 1,
    },
    {
      field: "uploadedOn",
      headerName: "Uploaded On",
      flex: 1,
      align: "center",
      headerAlign: "center",
    },
    {
      field: "uploadedBy",
      headerName: "Uploaded By",
      sortable: false,
      flex: 1,
    },
    {
      field: "action",
      headerName: "Action",
      sortable: false,
      filterable: false,
      align: "center",
      headerAlign: "center",
      flex: 1,
      renderCell: (cellValues) => {
        return (
          <>
            <MatView index={cellValues.row.id} name={cellValues.row.docName} />
            <MatDownload
              index={cellValues.row.id}
              name={cellValues.row.docName}
            />
          </>
        );
      },
    },
  ];
  const getAttachments = () => {
    let requestId = task?.subject
      ? task?.subject
      : massProfitRowData?.requestId;
    let hSuccess = (data) => {
      var attachmentRows = [];
      data.documentDetailDtoList.forEach((doc) => {
        var tempRow = {
          id: doc.documentId,
          docType: doc.fileType,
          docName: doc.fileName,
          uploadedOn: moment(doc.docCreationDate).format(appSettings.date),
          uploadedBy: doc.createdBy,
        };
        if (true) attachmentRows.push(tempRow);
      });
      setAttachments(attachmentRows);
    };
    // invoiceHeaderData?.extInvNum &&
    doAjax(
      `/${destination_DocumentManagement}/documentManagement/getDocByRequestId/${requestId}`,
      "get",
      hSuccess
    );
  };

  const getComments = () => {
    let requestId = task?.subject
      ? task?.subject
      : massProfitRowData?.requestId;
    let hSuccess = (data) => {
      console.log("commentsdata", data);

      var commentRows = [];
      data.body.forEach((cmt) => {
        var tempRow = {
          id: cmt.requestId,
          comment: cmt.comment,
          user: cmt.createdByUser,
          createdAt: cmt.updatedAt,
        };
        commentRows.push(tempRow);
      });
      setComments(commentRows);
      console.log("commentrows", commentRows);
    };

    let hError = (error) => {
      console.log(error);
    };
    // invoiceHeaderData?.extInvNum &&
    doAjax(
      `/${destination_GeneralLedger}/activitylog/fetchTaskDetailsForRequestId?requestId=${requestId}`,
      "get",
      hSuccess,
      hError
    );
  };

  useEffect(() => {
    getAttachments();
    getComments();
    setGlNumber(idGenerator("GL"));
  }, []);

  const getMassProfitCenterTable = () => {
    console.log("sdfkhdgkf");
    setIsLoading(true);
    let payload = {};
    if (task?.processDesc === "Mass Change") {
      payload = {
        massCreationId: "",
        massExtendId: "",
        massChangeId: task?.subject
          ? task?.subject?.slice(3)
          : massProfitRowData?.requestId.slice(3),
        screenName: "Change",
      };
    } else if (task?.processDesc === "Mass Create") {
      payload = {
        massCreationId: task?.subject
          ? task?.subject?.slice(3)
          : massProfitRowData?.requestId.slice(3),
        massChangeId: "",
        massExtendId: "",
        screenName: "Create",
      };
    } else if (massProfitRowData?.requestType === "Mass Create") {
      payload = {
        massCreationId: massProfitRowData?.requestId?.slice(3),
        massChangeId: "",
        massExtendId: "",
        screenName: "Create",
      };
    } else if (massProfitRowData?.requestType === "Mass Change") {
      payload = {
        massCreationId: "",
        massExtendId: "",
        massChangeId: massProfitRowData?.requestId?.slice(3),
        screenName: "Change",
      };
    }
    const hSuccess = (data) => {
      setIsLoading(false);
      // setFactorsArray(categoryKeys);
      if (data.body) {
        dispatch(setMultipleGLData(data?.body));
      }
    };

    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/displayMassGeneralLedger`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  useEffect(() => {
    if (MultipleProfitCenter.length === 0) {
      getMassProfitCenterTable();
    } else {
      return;
    }
  }, []);
  const initialRows = MultipleProfitCenter?.map((pCenter, index) => {
    const headerData = pCenter;
    const basicData = pCenter?.viewData || {};
    return {
      id: index,
      chartOfAccount: headerData?.ChartOfAccount || "",
      companyCode: headerData?.CompCode || "",
      glAccount: headerData?.GLAccount || "",
      accountType:
        basicData["Type/Description"]["Control in COA"]?.find(
          (field) => field?.fieldName === "Account Type"
        )?.value || "",
      accountGroup:
        basicData["Type/Description"]["Control in COA"]?.find(
          (field) => field?.fieldName === "Account Group"
        )?.value || "",
      // functionalArea:
      //     basicData["Type/Description"]["Detailed Control for P&L Statement Accounts"].find(
      //       (field) => field?.fieldName === "Functional Area"
      //     )?.value||"",
      shortText:
        basicData["Type/Description"]["Description"]?.find(
          (field) => field?.fieldName === "Short Text"
        )?.value || "",
      longText:
        basicData["Type/Description"]["Description"]?.find(
          (field) => field?.fieldName === "Long Text"
        )?.value || "",
      accountCurrency:
        basicData["Control Data"]["Account Control in Company Code"]?.find(
          (field) => field?.fieldName === "Account Currency"
        )?.value || "",
    };
  });
  const columns = [
    {
      field: "glAccount",
      headerName: "GL Account",
      editable: false,
      flex: 1,
      renderCell: (params) => {
        const isDirectMatch = profitValidationError.find(
          (element) => element.generalLedger === params.value
        );
        console.log(isDirectMatch, "isDirectMatch");
        console.log(params, "params");

        if (isDirectMatch && isDirectMatch.code === 400) {
          return (
            <Typography sx={{ fontSize: "12px", color: "red" }}>
              {params.value}
            </Typography>
          );
        } else {
          return (
            <Typography sx={{ fontSize: "12px" }}>{params.value}</Typography>
          );
        }
      },
    },
    {
      field: "chartOfAccount",
      headerName: "Chart Of Account",
      editable: false,
      flex: 1,
    },
    {
      field: "companyCode",
      headerName: "Company Code",
      editable: false,
      flex: 1,
    },
    {
      field: "accountType",
      headerName: "Account Type",
      editable: false,
      flex: 1,
    },
    {
      field: "accountGroup",
      headerName: "Account Group",
      editable: false,
      flex: 1,
    },
    {
      field: "shortText",
      headerName: "Short Text",
      editable: false,
      flex: 1,
      renderCell: (params) => {
        const isDirectMatch = directMatchedProfitCenters.includes(
          params.row.profitCenterName
        );
        return (
          <Typography
            sx={{ fontSize: "12px", color: isDirectMatch ? "red" : "inherit" }}
          >
            {params.value}
          </Typography>
        );
      },
    },

    {
      field: "longText",
      headerName: "Long Text",
      editable: false,
      flex: 1,
    },
    {
      field: "accountCurrency",
      headerName: "Account Currency",
      editable: false,
      flex: 1,
    },
  ];
  const handleSelectionModelChange = (selectedIds) => {
    // console.log('selected', selectedIds)
    if (selectedIds.length > 0) {
      setTestRun(true);
      console.log("selectedIds1", selectedIds);
    } else {
      // console.log("select", selectedIds);
      setTestRun(false);
    }
    console.log("selectedIds", selectedIds);
    setSelectedRows(selectedIds);
    // setTestRun(true);
  };
  const getValueForFieldName = (data, fieldName) => {
    const field = data?.find((field) => field?.fieldName === fieldName);
    return field ? field.value : "";
  };

  var payloadmapping = MultipleProfitCenter.map((x) => {
    console.log(x, "xx");
    return {
      GeneralLedgerID: x?.GeneralLedgeId ?? "",
      Action:
        task?.processDesc === "Mass Create"
          ? "I"
          : task?.processDesc === "Mass Change"
          ? "U"
          : massProfitRowData?.requestType === "Mass Change"
          ? "U"
          : massProfitRowData?.requestType === "Mass Create"
          ? "I"
          : "",
      RequestID: "",
      TaskStatus: "",
      TaskId: task?.taskId ? task?.taskId : "",
      Remarks: remarks ? remarks : "",
      Info: "",
      CreationId: "",
      EditId: "",
      DeleteId: "",
      MassCreationId:
        task?.processDesc === "Mass Create"
          ? task?.subject?.slice(3)
          : massProfitRowData?.requestType === "Mass Create"
          ? massProfitRowData?.requestId.slice(3)
          : "",
      MassEditId:
        task?.processDesc === "Mass Change"
          ? task?.subject?.slice(3)
          : massProfitRowData?.requestType === "Mass Change"
          ? massProfitRowData?.requestId.slice(3)
          : "",
      MassDeleteId: "",
      RequestType:
        task?.processDesc === "Mass Create"
          ? "Mass Create"
          : task?.processDesc === "Mass Change"
          ? "Mass Change"
          : massProfitRowData?.requestType === "Mass Change"
          ? "Mass Change"
          : massProfitRowData?.requestType === "Mass Create"
          ? "Mass Create"
          : "",
      ReqCreatedBy: userData?.user_id ?? "",
      ReqCreatedOn: task?.createdOn ? "/Date(" + task?.createdOn + ")/" : "",
      ReqUpdatedOn: "",
      RequestStatus: "",
      Testrun: testRun,
      COA: x?.ChartOfAccount ?? "",
      CompanyCode: x?.CompCode ?? "",
      CoCodeToExtend: "",
      GLAccount: x?.GLAccount ?? "",
      Accounttype: getValueForFieldName(
        x?.viewData["Type/Description"]?.["Control in COA"],
        "Account Type"
      ),
      AccountGroup: getValueForFieldName(
        x?.viewData["Type/Description"]?.["Control in COA"],
        "Account Group"
      ),
      GLname: getValueForFieldName(
        x?.viewData["Type/Description"]?.["Description"],
        "Short Text"
      ),
      Description: getValueForFieldName(
        x?.viewData["Type/Description"]?.["Description"],
        "Long Text"
      ),
      TradingPartner: getValueForFieldName(
        x?.viewData["Type/Description"]?.["Consolidation Data in COA"],
        "Trading Partner"
      ),
      GroupAccNo: getValueForFieldName(
        x?.viewData["Type/Description"]?.["Consolidation Data in COA"],
        "Group Account Number"
      ),
      AccountCurrency: getValueForFieldName(
        x?.viewData["Control Data"]?.["Account Control in Company Code"],
        "Account Currency"
      ),
      Exchangerate: getValueForFieldName(
        x?.viewData["Control Data"]?.["Account Control in Company Code"],
        "Exchange Rate Difference Key"
      ),
      Balanceinlocrcy:
        getValueForFieldName(
          x?.viewData["Control Data"]?.["Account Control in Company Code"],
          "Only Balance In Local Currency"
        ) === true
          ? "X"
          : "",
      Taxcategory: getValueForFieldName(
        x?.viewData["Control Data"]?.["Account Control in Company Code"],
        "Tax Category"
      ),
      Pstnwotax:
        getValueForFieldName(
          x?.viewData["Control Data"]?.["Account Control in Company Code"],
          "Posting Without Tax Allowed"
        ) === true
          ? "X"
          : "",
      ReconAcc: getValueForFieldName(
        x?.viewData["Control Data"]?.["Account Control in Company Code"],
        "Recon. Account For Account Type"
      ),
      Valuationgrp: getValueForFieldName(
        x?.viewData["Control Data"]?.["Account Control in Company Code"],
        "Valuation Group"
      ),
      AlterAccno: getValueForFieldName(
        x?.viewData["Control Data"]?.["Account Control in Company Code"],
        "Alternative Account Number"
      ),
      Openitmmanage:
        getValueForFieldName(
          x?.viewData["Control Data"]?.["Account Management in Company Code"],
          "Open Item Management"
        ) === true
          ? "X"
          : "",
      Sortkey: getValueForFieldName(
        x?.viewData["Control Data"]?.["Account Management in Company Code"],
        "Sort Key"
      ),
      CostEleCategory: getValueForFieldName(
        x?.viewData["Control Data"]?.["Account Management in Company Code"],
        "Sort Key"
      ),
      FieldStsGrp: getValueForFieldName(
        x?.viewData["Create/Bank/Interest"]?.[
          "Control of Document creation in Company Code"
        ],
        "Field Status Group"
      ),
      PostAuto:
        getValueForFieldName(
          x?.viewData["Create/Bank/Interest"]?.[
            "Control of Document creation in Company Code"
          ],
          "Post Automatically Only"
        ) === true
          ? "X"
          : "",
      Supplementautopost:
        getValueForFieldName(
          x?.viewData["Create/Bank/Interest"]?.[
            "Control of Document creation in Company Code"
          ],
          "Supplement Auto Postings"
        ) === true
          ? "X"
          : "",
      Planninglevel: getValueForFieldName(
        x?.viewData["Create/Bank/Interest"]?.[
          "Bank/Financial Details in Company Code"
        ],
        "Planning Level"
      ),
      Relvnttocashflow:
        getValueForFieldName(
          x?.viewData["Create/Bank/Interest"]?.[
            "Bank/Financial Details in Company Code"
          ],
          "Relevant To Cash Flows"
        ) === true
          ? "X"
          : "",
      HouseBank: getValueForFieldName(
        x?.viewData["Create/Bank/Interest"]?.[
          "Bank/Financial Details in Company Code"
        ],
        "House Bank"
      ),
      AccountId: getValueForFieldName(
        x?.viewData["Create/Bank/Interest"]?.[
          "Bank/Financial Details in Company Code"
        ],
        "Account ID"
      ),
      Interestindicator: getValueForFieldName(
        x?.viewData["Create/Bank/Interest"]?.[
          "Interest Calculation Information in Company Code"
        ],
        "Interest Indicator"
      ),
      ICfrequency: getValueForFieldName(
        x?.viewData["Create/Bank/Interest"]?.[
          "Interest Calculation Information in Company Code"
        ],
        "Interest Calculation Frequency"
      ),
      KeydateofLIC: getValueForFieldName(
        x?.viewData["Create/Bank/Interest"]?.[
          "Interest Calculation Information in Company Code"
        ],
        "Key Date Of Last Interest Calculation"
      ),
      LastIntrstundate: getValueForFieldName(
        x?.viewData["Create/Bank/Interest"]?.[
          "Interest Calculation Information in Company Code"
        ],
        "Date Of Last Interest Run"
      ),
      AccmngExistsys: "",
      Infationkey: "",
      Tolerancegrp: "",
      AuthGroup: "",
      AccountClerk: "",
      ReconAccReady: "",
      PostingBlocked: "",
      PlanningBlocked: "",
    };
  });
  console.log(payloadmapping, "payloadmapping");
  const handleSubmitForApproval = () => {
    const payload = payloadmapping;
    console.log("isLoading1", isLoading);
    console.log("paylaod", payload);
    // Assuming payloadmapping is an array with a single element
    const hSuccess = (data) => {
      setBlurLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Mass General Ledger Submitted for Approval with ID NLM${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setTestRun(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting the Mass General Ledger for Approval  "
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setTestRun(true);
        // setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/massAction/generalLedgersApprovalSubmit`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  //mdm submits for approval in change
  const handleSubmitForApprovalChange = () => {
    const selectedData = initialRows.filter((_, index) =>
      selectedRows.includes(index)
    );
    const payload = payloadmapping;
    console.log("paylaod", payload);
    console.log("isLoading2", isLoading);
    // Assuming payloadmapping is an array with a single element
    const hSuccess = (data) => {
      setBlurLoading(false);
      // setTestRun(true);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Mass General Ledger Change Submitted for Approval with ID CGM${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting Mass General Ledger Data for Approval"
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
    };
    const hError = (error) => {
      console.log("error");
    };

    doAjax(
      `/${destination_GeneralLedger}/massAction/changeGeneralLedgersApprovalSubmit`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  //approver approves in create
  const handleApproveMassProfitCenter = () => {
    const selectedData = initialRows.filter((_, index) =>
      selectedRows.includes(index)
    );
    const payload = payloadmapping;
    console.log("paylaod", payload);
    console.log("isLoading3", isLoading);
    // Assuming payloadmapping is an array with a single element
    const hSuccess = (data) => {
      setBlurLoading(false);
      if (data.statusCode === 201) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          "Mass General Ledger Approved & SAP Syndication Completed"
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setTestRun(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting the General Ledger for Approval "
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setTestRun(true);
        // setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/massAction/createGeneralLedgersApproved`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  //approver approves in change
  const handleApproveMassProfitCenterChange = () => {
    const payload = payloadmapping;
    console.log("paylaod", payload);
    console.log("isLoading4", isLoading);
    // Assuming payloadmapping is an array with a single element
    const hSuccess = (data) => {
      setBlurLoading(false);
      // setTestRun(true);
      if (data.statusCode === 201) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          "Mass General Ledger Change Approved & SAP Syndication Completed"
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Approving Mass General Ledger Change");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
    };
    const hError = (error) => {
      console.log("error");
    };
    doAjax(
      `/${destination_GeneralLedger}/massAction/changeGeneralLedgersApproved`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const handleSubmitForReviewMassProfitCenter = () => {
    const payload = payloadmapping;
    console.log("paylaod", payload);
    console.log("isLoading5", isLoading);
    // Assuming payloadmapping is an array with a single element
    const hSuccess = (data) => {
      setBlurLoading(false);
      // setTestRun(true);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Mass General Ledger Submitted for Review with ID NLM${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        const secondApiPayload = {
          artifactId: glNumber,
          createdBy: userData?.emailId,
          artifactType: "GeneralLedger",
          requestId: `NLM${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting the Mass General Ledger for Review"
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/massAction/generalLedgersSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const handleSubmitForReviewChange = () => {
    const payload = payloadmapping;
    console.log("paylaod", payload);
    console.log("isLoading5", isLoading);
    // Assuming payloadmapping is an array with a single element
    const hSuccess = (data) => {
      setBlurLoading(false);
      // setTestRun(true);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Mass General Ledger Submitted for Review with ID CGM${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        const secondApiPayload = {
          artifactId: glNumber,
          createdBy: userData?.emailId,
          artifactType: "GeneralLedger",
          requestId: `CGM${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting the Mass General Ledger for Review  "
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/massAction/changeGeneralLedgersSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const handleRemarks = (e, value) => {
    //setRemarks(e.target.value);
    const newValue = e.target.value;
    if (newValue.length > 0 && newValue[0] === " ") {
      setRemarks(newValue.trimStart());
    } else {
      //let costCenterValue = e.target.value;
      let remarksUpperCase = newValue.toUpperCase();
      setRemarks(remarksUpperCase);
    }
  };
  const handleRemarksDialogClose = () => {
    setTestRun(true);
    setOpenRemarkDialog(false);
  };

  const onCostCenterSubmitRemarks = () => {
    if (
      (userData?.role === "MDM Steward" &&
        task.processDesc === "Mass Create") ||
      (userData?.role === "MDM Steward" &&
        massProfitRowData?.requestType === "Mass Create")
    ) {
      setBlurLoading(true);
      handleRemarksDialogClose();
      handleSubmitForApproval();
    } else if (
      (userData?.role === "Approver" && task.processDesc === "Mass Create") ||
      (userData?.role === "Approver" &&
        massProfitRowData?.requestType === "Mass Create")
    ) {
      setBlurLoading(true);
      handleRemarksDialogClose();
      handleApproveMassProfitCenter();
    } else if (
      (userData?.role === "Finance" && task.processDesc === "Mass Create") ||
      (userData?.role === "Finance" &&
        massProfitRowData?.requestType === "Mass Create")
    ) {
      setBlurLoading(true);
      handleRemarksDialogClose();
      handleSubmitForReviewMassProfitCenter();
    } else if (
      (userData?.role === "MDM Steward" &&
        task.processDesc === "Mass Change") ||
      (userData?.role === "MDM Steward" &&
        massProfitRowData?.requestType === "Mass Change")
    ) {
      setBlurLoading(true);
      handleRemarksDialogClose();
      handleSubmitForApprovalChange();
    } else if (
      (userData?.role === "Approver" && task.processDesc === "Mass Change") ||
      (userData?.role === "Approver" &&
        massProfitRowData?.requestType === "Mass Change")
    ) {
      setBlurLoading(true);
      handleRemarksDialogClose();
      handleApproveMassProfitCenterChange();
    } else if (
      (userData?.role === "Finance" && task.processDesc === "Mass Change") ||
      (userData?.role === "Finance" &&
        massProfitRowData?.requestType === "Mass Change")
    ) {
      setBlurLoading(true);
      handleRemarksDialogClose();
      handleSubmitForReviewChange();
    }
  };

  const handleOpenRemarkDialog = () => {
    setTestRun(false);
    setOpenRemarkDialog(true);
  };

  const handleCorrectionDialogClose = () => {
    setOpenCorrectionDialog(false);
  };

  const handleOpenCorrectionDialog = () => {
    setTestRun(false);
    setOpenCorrectionDialog(true);
  };

  const handleCorrectionMDMCreate = () => {
    console.log("isLoading6", isLoading);
    const selectedData = initialRows.filter((_, index) =>
      selectedRows.includes(index)
    );
    const payload = payloadmapping;
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `General Ledger Submitted for Correction with ID NLS${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        const secondApiPayload = {
          artifactId: glNumber,
          createdBy: userData?.emailId,
          artifactType: "GeneralLedger",
          requestId: `NLS${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting General Ledger for Correction"
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    console.log("remarkssssssssss", remarks);
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_GeneralLedger}/massAction/generalLedgersSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const handleCorrectionMDMChange = () => {
    const selectedData = initialRows.filter((_, index) =>
      selectedRows.includes(index)
    );
    const payload = payloadmapping;
    const hSuccess = (data) => {
      setIsLoading(false);
      // setTestRun(true);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `General Ledger Submitted for Correction with ID NLM${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        const secondApiPayload = {
          artifactId: glNumber,
          createdBy: userData?.emailId,
          artifactType: "GeneralLedger",
          requestId: `NLM${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting General Ledger for Correction"
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    console.log("remarkssssssssss", remarks);
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_GeneralLedger}/massAction/changeGeneralLedgersSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const handleCorrectionApproverCreate = () => {
    console.log("isLoading7", isLoading);
    const selectedData = initialRows.filter((_, index) =>
      selectedRows.includes(index)
    );
    const payload = payloadmapping;
    const hSuccess = (data) => {
      setIsLoading(false);
      // setTestRun(true);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `General Ledger Submitted for Correction with ID NLM${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        const secondApiPayload = {
          artifactId: glNumber,
          createdBy: userData?.emailId,
          artifactType: "GeneralLedger",
          requestId: `NLM${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting General Ledger for Correction"
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_GeneralLedger}/massAction/generalLedgersSendForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCorrectionApproverChange = () => {
    const selectedData = initialRows.filter((_, index) =>
      selectedRows.includes(index)
    );
    const payload = payloadmapping;
    const hSuccess = (data) => {
      setIsLoading(false);
      // setTestRun(true);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `General Ldger Submitted for Correction with ID NGS${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting General Ledger for Correction"
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_GeneralLedger}/massAction/changeGeneralLedgersSendForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const onProfitCenterCorrection = () => {
    if (
      (userData?.role === "MDM Steward" &&
        task.processDesc === "Mass Create") ||
      (userData?.role === "MDM Steward" &&
        massProfitRowData?.requestType === "Mass Create")
    ) {
      setIsLoading(true);
      handleCorrectionDialogClose();
      handleCorrectionMDMCreate();
    } else if (
      (userData?.role === "Approver" && task.processDesc === "Mass Create") ||
      (userData?.role === "Approver" &&
        massProfitRowData?.requestType === "Mass Create")
    ) {
      setIsLoading(true);
      handleCorrectionDialogClose();
      handleCorrectionApproverCreate();
    } else if (
      (userData?.role === "MDM Steward" &&
        task.processDesc === "Mass Change") ||
      (userData?.role === "MDM Steward" &&
        massProfitRowData?.requestType === "Mass Change")
    ) {
      handleCorrectionDialogClose();
      handleCorrectionMDMChange();
    } else if (
      (userData?.role === "Approver" && task.processDesc === "Mass Change") ||
      (userData?.role === "Approver" &&
        massProfitRowData?.requestType === "Mass Change")
    ) {
      handleCorrectionDialogClose();
      handleCorrectionApproverChange();
    }
  };

  const onValidateGeneralLedgerApprove = () => {
    setBlurLoading(true);
    let payload = payloadmapping;
    // payload = selectedProfitCenterRows;
    const hSuccess = (data) => {
      // setIsLoading(false);
      if (data.statusCode === 400) {
        setProfitValidationErrors(data.body);
        setDialogOpen(true);
        setApproveDisabled(true);
        setBlurLoading(false);
      } else {
        setApproveDisabled(false);
        setMessageDialogTitle("Create");
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `All Data has been Validated.General Ledger can be Send for Review`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
        setValidateFlag(true);
        setBlurLoading(false);

        // Now, make the duplicate check API call
        // Ensure that the conditions for making the duplicate check API call are met
      }
      // setBlurLoading(false);
    };

    const hDuplicateCheckSuccess = (data) => {
      console.log("dataaaa", data);
      // Handle success of duplicate check
      if (
        data.body.length === 0 ||
        !data.body.some((item) =>
          duplicateCheckPayload.some(
            (payloadItem) =>
              payloadItem?.glName?.toUpperCase() === item.description
          )
        )
      ) {
        // No direct match, enable the "Submit for Review" button
        setBlurLoading(false);
        setSubmitForReviewDisabled(false);
        setTestRun(true);
      } else {
        // Handle direct match
        const directMatches = data.body.map((item) => item.description);
        setBlurLoading(false);
        setMessageDialogTitle("Duplicate Check");
        setsuccessMsg(false);
        setMessageDialogMessage(
          `There is a direct match for the General Ledger name.`
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setSubmitForReviewDisabled(true);
        setDirectMatchedProfitCenters(directMatches);
      }
    };

    const hDuplicateCheckError = (error) => {
      // Handle error of duplicate check
      console.log(error);
    };

    const hError = (error) => {
      console.log(error);
    };

    // Call the main API for validation
    doAjax(
      `/${destination_GeneralLedger}/massAction/validateMassGeneralLedger`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const onValidateGeneralLedger = () => {
    setBlurLoading(true);
    const selectedData = initialRows.filter((_, index) =>
      selectedRows.includes(index)
    );
    console.log("selectedData", selectedData);
    const selectedProfitCenterRows = selectedData.map((x) => ({
      ...payloadmapping[x?.id],
    }));
    console.log("selectedProfitCenterRows", selectedProfitCenterRows);
    const duplicateCheckPayload = [];
    selectedProfitCenterRows.map((x) => {
      var idk = {
        glName: x?.GLname.toUpperCase(),
        compCode: x?.CompanyCode,
      };
      duplicateCheckPayload.push(idk);
    });
    console.log("duplicateCheckPayload", duplicateCheckPayload);
    let payload = payloadmapping;
    payload = selectedProfitCenterRows;
    const hSuccess = (data) => {
      // setIsLoading(false);
      if (data.statusCode === 400) {
        setProfitValidationErrors(data.body);
        setDialogOpen(true);
        setBlurLoading(false);
      } else {
        setMessageDialogTitle("Create");
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `All Data has been Validated.General Ledger can be Send for Review`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
        setValidateFlag(true);
        // setBlurLoading(false);

        // Now, make the duplicate check API call
        // Ensure that the conditions for making the duplicate check API call are met
        if (
          duplicateCheckPayload.glName !== "" ||
          duplicateCheckPayload.compCode !== ""
        ) {
          // payloadmapping.Toitem = duplicateCheckPayload.name;
          doAjax(
            `/${destination_GeneralLedger}/alter/fetchGlNameNCompCodeDupliChkMass`,
            "post",
            hDuplicateCheckSuccess,
            hDuplicateCheckError,
            duplicateCheckPayload
          );
        }
      }
      // setBlurLoading(false);
    };

    const hDuplicateCheckSuccess = (data) => {
      console.log("dataaaa", data);
      // Handle success of duplicate check
      if (
        data.body.length === 0 ||
        !data.body.some((item) =>
          duplicateCheckPayload.some(
            (payloadItem) =>
              payloadItem?.glName?.toUpperCase() === item.description
          )
        )
      ) {
        // No direct match, enable the "Submit for Review" button
        setBlurLoading(false);
        setSubmitForReviewDisabled(false);
      } else {
        // Handle direct match
        const directMatches = data.body.map((item) => item.description);
        setBlurLoading(false);
        setMessageDialogTitle("Duplicate Check");
        setsuccessMsg(false);
        setMessageDialogMessage(
          `There is a direct match for the General Ledger name.`
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setSubmitForReviewDisabled(true);
        setDirectMatchedProfitCenters(directMatches);
      }
    };

    const hDuplicateCheckError = (error) => {
      // Handle error of duplicate check
      console.log(error);
    };

    const hError = (error) => {
      console.log(error);
    };

    // Call the main API for validation
    doAjax(
      `/${destination_GeneralLedger}/massAction/validateMassGeneralLedger`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleDialogClose = () => {
    setDialogOpen(false);
  };
  const validationColumns = [
    {
      field: "generalLedger",
      headerName: "General Ledger",
      editable: false,
      flex: 1,
      // width: 100,
    },
    {
      field: "error",
      headerName: "Error",
      editable: false,
      flex: 1,
      // width: 400,
    },
  ];
  const validationRows = profitValidationError
    ?.filter((row) => row?.code === 400)
    ?.map((item, index) => {
      if (item.code === 400) {
        return {
          id: index,
          generalLedger: item?.generalLedger,
          error: item?.status?.message,
        };
      }
    });

  return (
    <>
      {isLoading === true ? (
        <LoadingComponent />
      ) : (
        <div style={{ backgroundColor: "#FAFCFF" }}>
          <Dialog
            hideBackdrop={false}
            elevation={2}
            PaperProps={{
              sx: { boxShadow: "none" },
            }}
            open={openCorrectionDialog}
            onClose={handleCorrectionDialogClose}
          >
            <DialogTitle
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF40",
                display: "flex",
              }}
            >
              <Typography variant="h6">Remarks</Typography>

              <IconButton
                sx={{ width: "max-content" }}
                onClick={handleCorrectionDialogClose}
                children={<CloseIcon />}
              />
            </DialogTitle>

            <DialogContent sx={{ padding: ".5rem 1rem" }}>
              <Stack>
                <Box sx={{ minWidth: 400 }}>
                  <FormControl sx={{ height: "auto" }} fullWidth>
                    <TextField
                      sx={{ backgroundColor: "#F5F5F5" }}
                      onChange={handleRemarks}
                      value={remarks}
                      multiline
                      placeholder={"Enter Remarks for Correction"}
                      inputProps={{ maxLength: 254 }}
                    ></TextField>
                  </FormControl>
                </Box>
              </Stack>
            </DialogContent>
            <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
              <Button
                sx={{ width: "max-content", textTransform: "capitalize" }}
                onClick={handleCorrectionDialogClose}
              >
                Cancel
              </Button>
              <Button
                className="button_primary--normal"
                type="save"
                onClick={onProfitCenterCorrection}
                variant="contained"
              >
                Submit
              </Button>
            </DialogActions>
          </Dialog>

          <Dialog
            hideBackdrop={false}
            elevation={2}
            PaperProps={{
              sx: { boxShadow: "none" },
            }}
            open={openRemarkDialog}
            onClose={handleRemarksDialogClose}
          >
            <DialogTitle
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF40",
                // borderBottom: "1px solid grey",
                display: "flex",
              }}
            >
              <Typography variant="h6">Remarks</Typography>

              <IconButton
                sx={{ width: "max-content" }}
                onClick={handleRemarksDialogClose}
                children={<CloseIcon />}
              />
            </DialogTitle>

            <DialogContent sx={{ padding: ".5rem 1rem" }}>
              <Stack>
                <Box sx={{ minWidth: 400 }}>
                  <FormControl sx={{ height: "auto" }} fullWidth>
                    <TextField
                      sx={{ backgroundColor: "#F5F5F5" }}
                      onChange={handleRemarks}
                      value={remarks}
                      multiline
                      placeholder={"Enter Remarks"}
                      inputProps={{ maxLength: 254 }}
                    ></TextField>
                  </FormControl>
                </Box>
              </Stack>
            </DialogContent>
            <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
              <Button
                sx={{ width: "max-content", textTransform: "capitalize" }}
                onClick={handleRemarksDialogClose}
              >
                Cancel
              </Button>
              <Button
                className="button_primary--normal"
                type="save"
                onClick={onCostCenterSubmitRemarks}
                variant="contained"
              >
                Submit
              </Button>
            </DialogActions>
          </Dialog>

          {/* error validation dialog */}
          <Dialog
            open={dialogOpen}
            fullWidth
            onClose={handleDialogClose}
            sx={{
              "&::webkit-scrollbar": {
                width: "1px",
              },
            }}
          >
            <DialogTitle
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF40",

                display: "flex",
              }}
            >
              <Typography variant="h6" color="red">
                Errors
              </Typography>

              <IconButton
                sx={{ width: "max-content" }}
                onClick={handleDialogClose}
                children={<CloseIcon />}
              />
            </DialogTitle>
            <DialogContent sx={{ padding: ".5rem 1rem" }}>
              <ReusableTable
                isLoading={isLoading}
                width="100%"
                rows={validationRows}
                columns={validationColumns}
                pageSize={10}
                getRowIdValue={"id"}
                hideFooter={true}
                checkboxSelection={false}
                disableSelectionOnClick={true}
                status_onRowSingleClick={true}
                stopPropagation_Column={"action"}
                status_onRowDoubleClick={true}
              />
            </DialogContent>

            <DialogActions
              sx={{ display: "flex", justifyContent: "end" }}
            ></DialogActions>
          </Dialog>

          <Backdrop
            sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
            open={blurLoading}
            // onClick={handleClose}
          >
            <CircularProgress color="inherit" />
          </Backdrop>

          {successMsg && (
            <ReusableSnackBar
              openSnackBar={openSnackbar}
              alertMsg={messageDialogMessage}
              handleSnackBarClose={handleSnackBarClose}
            />
          )}

          <ReusableDialog
            dialogState={openMessageDialog}
            openReusableDialog={handleMessageDialogClickOpen}
            closeReusableDialog={handleMessageDialogClose}
            dialogTitle={messageDialogTitle}
            dialogMessage={messageDialogMessage}
            handleDialogConfirm={handleMessageDialogClose}
            dialogOkText={"OK"}
            handleExtraButton={handleMessageDialogNavigate}
            dialogSeverity={messageDialogSeverity}
          />

          <div style={{ ...outermostContainer, backgroundColor: "#FAFCFF" }}>
            <Grid container sx={outermostContainer_Information}>
              <Grid item md={12} sx={{ display: "flex", marginBottom: "0" }}>
                <Grid item md={11} sx={{ display: "flex" }}>
                  <Grid>
                    <IconButton
                      color="primary"
                      aria-label="upload picture"
                      component="label"
                      sx={iconButton_SpacingSmall}
                    >
                      <ArrowCircleLeftOutlinedIcon
                        style={{
                          height: "1em",
                          width: "1em",
                          color: "#000000",
                        }}
                        onClick={() => {
                          //navigate("/masterDataCockpit/profitCenter");
                          navigate(-1);
                        }}
                      />
                    </IconButton>
                  </Grid>

                  {task.processDesc === "Mass Create" ? (
                    <Grid>
                      <Typography variant="h3">
                        <strong>Create Multiple General Ledger</strong>
                      </Typography>
                      <Typography variant="body2" color="#777">
                        This view displays list of General Ledgers
                      </Typography>
                    </Grid>
                  ) : massProfitRowData?.requestType === "Mass Create" ? (
                    <Grid>
                      <Typography variant="h3">
                        <strong>Create Multiple General Ledger</strong>
                      </Typography>
                      <Typography variant="body2" color="#777">
                        This view displays list of General Ledgers
                      </Typography>
                    </Grid>
                  ) : massProfitRowData?.requestType === "Mass Change" ? (
                    <Grid>
                      <Typography variant="h3">
                        <strong>Change Multiple General Ledger</strong>
                      </Typography>
                      <Typography variant="body2" color="#777">
                        This view displays list of General Ledgers
                      </Typography>
                    </Grid>
                  ) : task?.processDesc === "Mass Change" ? (
                    <Grid>
                      <Typography variant="h3">
                        <strong>Change Multiple General Ledger</strong>
                      </Typography>
                      <Typography variant="body2" color="#777">
                        This view displays list of General Ledgers
                      </Typography>
                    </Grid>
                  ) : (
                    ""
                  )}
                </Grid>
                <Grid item md={1} sx={{ display: "flex" }}>
                  <Tooltip title="Uploaded documents" arrow>
                    <IconButton onClick={handleOpenDialog}>
                      <AttachFileOutlinedIcon />
                    </IconButton>
                  </Tooltip>
                </Grid>
                <Dialog
                  hideBackdrop={false}
                  elevation={2}
                  PaperProps={{
                    sx: { boxShadow: "none" },
                  }}
                  open={openDialog}
                  onClose={handleCloseDialog}
                >
                  <DialogTitle
                    sx={{
                      justifyContent: "space-between",
                      alignItems: "center",
                      height: "max-content",
                      padding: ".5rem",
                      paddingLeft: "1rem",
                      backgroundColor: "#EAE9FF40",
                      // borderBottom: "1px solid grey",
                      display: "flex",
                    }}
                  >
                    {userData?.role === "Finance" ? (
                      <>
                        <Typography variant="h6">Add Attachment</Typography>
                      </>
                    ) : (
                      ""
                    )}
                  </DialogTitle>
                  <DialogContent sx={{ padding: ".5rem 1rem" }}>
                    <Card sx={{ padding: "1rem 1rem 0rem 1rem" }}>
                      {userData?.role === "Finance" ? (
                        <>
                          <Stack>
                            <Box sx={{ minWidth: 400 }}>
                              <ReusableAttachementAndComments
                                title="GeneralLedger"
                                useMetaData={false}
                                artifactId={glNumber}
                                artifactName="GeneralLedger"
                              />
                            </Box>
                          </Stack>
                        </>
                      ) : (
                        ""
                      )}
                      <Grid
                        container
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                        }}
                      >
                        <Typography variant="h6">
                          <strong>Attachments</strong>
                        </Typography>
                      </Grid>
                      {Boolean(attachments.length) && (
                        <ReusableTable
                          width="100%"
                          rows={attachments}
                          columns={attachmentColumns}
                          hideFooter={false}
                          getRowIdValue={"id"}
                          disableSelectionOnClick={true}
                          stopPropagation_Column={"action"}
                        />
                      )}
                      {!Boolean(attachments.length) && (
                        <Typography variant="body2">
                          No Attachments Found
                        </Typography>
                      )}
                      <br />
                      <Typography variant="h6">Comments</Typography>
                      {Boolean(comments.length) && (
                        <Timeline
                          sx={{
                            [`& .${timelineItemClasses.root}:before`]: {
                              flex: 0,
                              padding: 0,
                            },
                          }}
                        >
                          {comments.map((comment) => (
                            <TimelineItem>
                              <TimelineSeparator>
                                <TimelineDot>
                                  <CheckCircleOutlineOutlined
                                    sx={{ color: "#757575" }}
                                  />
                                </TimelineDot>
                                <TimelineConnector />
                              </TimelineSeparator>
                              <TimelineContent sx={{ py: "12px", px: 2 }}>
                                <Card
                                  elevation={0}
                                  sx={{
                                    border: 1,
                                    borderColor: "#C4C4C4",
                                    borderRadius: "8px",
                                    width: "650px",
                                  }}
                                >
                                  <Box sx={{ padding: "1rem" }}>
                                    <Stack spacing={1}>
                                      <Grid
                                        sx={{
                                          display: "flex",
                                          justifyContent: "space-between",
                                        }}
                                      >
                                        <Typography
                                          sx={{
                                            textAlign: "right",
                                            color: " #757575",
                                            fontWeight: "500",
                                            fontSize: "12px",
                                          }}
                                        >
                                          {moment(comment.createdAt).format(
                                            "DD MMM YYYY"
                                          )}
                                        </Typography>
                                      </Grid>

                                      <Typography
                                        sx={{
                                          fontSize: "12px",

                                          color: " #757575",
                                          fontWeight: "500",
                                        }}
                                      >
                                        {comment.user}
                                      </Typography>
                                      <Typography
                                        sx={{
                                          fontSize: "12px",
                                          color: "#1D1D1D",
                                          fontWeight: "600",
                                        }}
                                      >
                                        {comment.comment}
                                      </Typography>
                                    </Stack>
                                  </Box>
                                </Card>
                              </TimelineContent>
                            </TimelineItem>
                          ))}
                        </Timeline>
                      )}
                      {!Boolean(comments.length) && (
                        <Typography variant="body2">
                          No Comments Found
                        </Typography>
                      )}
                      <br />
                    </Card>
                  </DialogContent>
                  <DialogActions>
                    <Button onClick={handleCloseDialog}>Close</Button>
                  </DialogActions>
                </Dialog>
              </Grid>
            </Grid>

            <Grid item sx={{ position: "relative" }}>
              {/* <Stack> */}
              <ReusableTable
                isLoading={isLoading}
                width="100%"
                title={"Mass General Ledger List (" + initialRows?.length + ")"}
                rows={initialRows}
                columns={columns}
                pageSize={10}
                getRowIdValue={"id"}
                hideFooter={false}
                checkboxSelection={
                  (userData?.role === "Finance" &&
                    task.processDesc === "Mass Create") ||
                  (userData?.role === "Finance" &&
                    massProfitRowData?.requestType === "Mass Create") ||
                  (userData?.role === "Finance" &&
                    task.processDesc === "Mass Change") ||
                  (userData?.role === "Finance" &&
                    massProfitRowData?.requestType === "Mass Change")
                }
                disableSelectionOnClick={true}
                status_onRowSingleClick={true}
                onRowsSelectionHandler={handleSelectionModelChange}
                callback_onRowSingleClick={(params) => {
                  const glAccount = params.row.glAccount;
                  const dataToSend = MultipleProfitCenter.find(
                    (item) => item.GLAccount === glAccount
                  );
                  console.log(dataToSend, "yo");
                  navigate(
                    `/masterDataCockpit/generalLedger/massGLTableRequestBench/displayMultipleGLRequestBench/${glAccount}`,
                    {
                      state: {
                        rowData: params.row,
                        requestNumber: massProfitRowData?.requestId.slice(3),
                        tabsData: dataToSend,
                        requestbenchRowData: massProfitRowData,
                      },
                    }
                  );
                }}
                stopPropagation_Column={"action"}
                status_onRowDoubleClick={true}
              />
              {/* </Stack> */}
            </Grid>
          </div>

          {checkIwaAccess(iwaAccessData, "General Ledger", "ChangeGL")  &&  (taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN")? (
            <Paper
              sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
              elevation={2}
            >
              <BottomNavigation
                className="container_BottomNav"
                showLabels
                sx={{
                  display: "flex",

                  justifyContent: "flex-end",
                }}
                value={value}
              >
                {((userData?.role === "MDM Steward" &&
                  task.processDesc === "Mass Create") ||
                  (userData?.role === "MDM Steward" &&
                    massProfitRowData?.requestType === "Mass Create")) &&
                task?.itmStatus?.toUpperCase() !== "OPEN" ? (
                  <>
                    <Button
                      variant="outlined"
                      size="small"
                      sx={{ button_Outlined, mr: 1 }}
                      onClick={handleOpenCorrectionDialog}
                    >
                      Correction
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary }}
                      onClick={handleOpenRemarkDialog}
                    >
                      Submit For Approval
                    </Button>
                  </>
                ) : ((userData?.role === "Approver" &&
                    task.processDesc === "Mass Create") ||
                    (userData?.role === "Approver" &&
                      massProfitRowData?.requestType === "Mass Create")) &&
                  task?.itmStatus?.toUpperCase() !== "OPEN" ? (
                  <>
                    <Button
                      variant="outlined"
                      size="small"
                      sx={{ button_Outlined, mr: 1 }}
                      onClick={handleOpenCorrectionDialog}
                    >
                      Correction
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={onValidateGeneralLedgerApprove}
                    >
                      Validate
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary }}
                      onClick={handleOpenRemarkDialog}
                      disabled={approveDisabled}
                    >
                      Approve
                    </Button>
                  </>
                ) : ((userData?.role === "Finance" &&
                    task.processDesc === "Mass Create") ||
                    (userData?.role === "Finance" &&
                      massProfitRowData?.requestType === "Mass Create")) &&
                  task?.itmStatus?.toUpperCase() !== "OPEN" ? (
                  <>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={onValidateGeneralLedger}
                      disabled={!testRun}
                    >
                      Validate
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary }}
                      onClick={handleOpenRemarkDialog}
                      disabled={submitForReviewDisabled}
                    >
                      Submit For Review
                    </Button>
                  </>
                ) : (
                  ""
                )}

                {((userData?.role === "MDM Steward" &&
                  task.processDesc === "Mass Change") ||
                  (userData?.role === "MDM Steward" &&
                    massProfitRowData?.requestType === "Mass Change")) &&
                task?.itmStatus?.toUpperCase() !== "OPEN" ? (
                  <>
                    <Button
                      variant="outlined"
                      size="small"
                      sx={{ button_Outlined, mr: 1 }}
                      onClick={handleOpenCorrectionDialog}
                    >
                      Correction
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary }}
                      onClick={handleOpenRemarkDialog}
                    >
                      Submit For Approval
                    </Button>
                  </>
                ) : ((userData?.role === "Approver" &&
                    task.processDesc === "Mass Change") ||
                    (userData?.role === "Approver" &&
                      massProfitRowData?.requestType === "Mass Change")) &&
                  task?.itmStatus?.toUpperCase() !== "OPEN" ? (
                  <>
                    <Button
                      variant="outlined"
                      size="small"
                      sx={{ button_Outlined, mr: 1 }}
                      onClick={handleOpenCorrectionDialog}
                    >
                      Correction
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={onValidateGeneralLedgerApprove}
                      // disabled={!testRun}
                    >
                      Validate
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary }}
                      onClick={handleOpenRemarkDialog}
                    >
                      Approve
                    </Button>
                  </>
                ) : ((userData?.role === "Finance" &&
                    task.processDesc === "Mass Change") ||
                    (userData?.role === "Finance" &&
                      massProfitRowData?.requestType === "Mass Change")) &&
                  task?.itmStatus?.toUpperCase() !== "OPEN" ? (
                  <>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={onValidateGeneralLedger}
                      disabled={!testRun}
                    >
                      Validate
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary }}
                      onClick={handleOpenRemarkDialog}
                      disabled={submitForReviewDisabled}
                    >
                      Submit For Review
                    </Button>
                  </>
                ) : (
                  ""
                )}
              </BottomNavigation>
            </Paper>
          ) : (
            ""
          )}
        </div>
      )}
    </>
  );
};

export default MassGLTableRequestBench;
