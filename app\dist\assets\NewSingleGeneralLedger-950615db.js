import{s as Ye,q as v,r as a,f4 as yt,a as n,j as l,T as x,E as ae,f2 as z,x as N,G as o,b6 as Nt,ay as Bt,at as Ft,au as Ot,K as y,eV as _,eK as qt,ai as Ke,b4 as J,B,F as M,b as Mt,u as Vt,f5 as jt,bh as Wt,bX as $t,V as pt,aF as Ht,I as _e,aG as zt,W as Kt,ar as _t,X as Jt,t as q,C as Ut,z as Je,al as Ue,b1 as Xt,aD as Yt,aE as Qt,aB as K,bp as Zt,bq as Xe}from"./index-17b8d91e.js";import{d as Lt}from"./ArrowCircleLeftOutlined-2a09f8e2.js";import{D as Gt}from"./DatePicker-68227989.js";import{R as Pt}from"./ReusableAttachementAndComments-bab6bbfc.js";import{S as en,a as tn,b as nn}from"./Stepper-88e4fb0c.js";import"./dateViewRenderers-34586552.js";import"./useSlotProps-e34e1e13.js";import"./InputAdornment-5b0053c5.js";import"./CSSTransition-30917e2c.js";import"./useMediaQuery-6a073ac5.js";import"./DesktopDatePicker-07c19cde.js";import"./useMobilePicker-9978caff.js";import"./CloudUpload-27b6d63e.js";import"./utilityImages-067c3dc2.js";import"./Add-********.js";import"./Delete-9f4d7a45.js";function U(t){var m,T,Z,w;const i=Ye();var d=t.field.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").replaceAll("%","").split(" ").join("");let S=v(c=>c.generalLedger.errorFields);const b=v(c=>c.generalLedger.singleGLPayload),h=c=>{const u=A=>{i(Ke({keyName:"CostElementCategory",data:A.body}))},I=A=>{console.log(A,"error in dojax")};y(`/${_}/data/getCostElementCategory?accountType=${c==null?void 0:c.code}`,"get",u,I)},C=()=>{const c=I=>{i(Ke({keyName:"Region",data:I.body}))},u=I=>{console.log(I,"error in dojax")};y(`/${qt}/data/getRegionBasedOnCountry?country=${t.country}`,"get",c,u)};a.useEffect(()=>{t.field.fieldName==="Region"&&C()},[]),a.useEffect(()=>{var c,u;(((c=t==null?void 0:t.field)==null?void 0:c.visibility)==="0"||((u=t==null?void 0:t.field)==null?void 0:u.visibility)==="Required")&&i(yt(d))},[]);const r=v(c=>c.AllDropDown.dropDown);if(((m=t.field)==null?void 0:m.fieldType)==="Input")return n(o,{item:!0,md:2,children:t.field.visibility==="Hidden"?null:l(N,{children:[l(x,{variant:"body2",color:"#777",children:[t.field.fieldName,t.field.visibility==="Required"||t.field.visibility==="0"?n("span",{style:{color:"red"},children:"*"}):""]}),n(ae,{size:"small",type:t.field.dataType==="QUAN"?"number":"",placeholder:`Enter ${t.field.fieldName}`,inputProps:{maxLength:t.field.maxLength},value:b[d],onChange:c=>{const u=c.target.value;if(u.length>0&&u[0]===" ")i(z({keyName:d,data:u.trimStart()}));else{let I=u.toUpperCase();i(z({keyName:d,data:I}))}},error:S.includes(d),required:t.field.visibility==="Required"||t.field.visibility==="0"})]})});if(((T=t.field)==null?void 0:T.fieldType)==="Drop Down")return n(o,{item:!0,md:2,children:t.field.visibility==="Hidden"?null:l(N,{children:[l(x,{variant:"body2",color:"#777",children:[t.field.fieldName,t.field.visibility==="Required"||t.field.visibility==="0"?n("span",{style:{color:"red"},children:"*"}):""]}),n(Nt,{sx:{height:"31px"},fullWidth:!0,size:"small",value:b[d],onChange:(c,u)=>{t.field.fieldName==="Account Type"&&h(u),i(z({keyName:d,data:u}))},disabled:t.field.fieldName==="Account Type"||t.field.fieldName==="Account Group",options:r[d]??[],required:t.field.visibility==="0"||t.field.visibility==="Required",getOptionLabel:c=>`${c==null?void 0:c.code} - ${c==null?void 0:c.desc}`,renderOption:(c,u)=>n("li",{...c,children:l(x,{style:{fontSize:12},children:[u==null?void 0:u.code," - ",u==null?void 0:u.desc]})}),renderInput:c=>n(ae,{...c,variant:"outlined",placeholder:`Select ${t.field.fieldName}`,error:S.includes(d)})})]})});if(((Z=t.field)==null?void 0:Z.fieldType)==="Radio Button")return l(o,{item:!0,md:2,children:[l(x,{variant:"body2",color:"#777",children:[t.field.fieldName,t.field.visibility==="Required"||t.field.visibility==="0"?n("span",{style:{color:"red"},children:"*"}):""]}),n(Bt,{sx:{padding:0},checked:b[d]==!0,onChange:c=>{i(z({keyName:d,data:c.target.checked}))}})]});if(((w=t.field)==null?void 0:w.fieldType)==="Calendar")return n(o,{item:!0,md:2,children:l(N,{children:[l(x,{variant:"body2",color:"#777",children:[t.field.fieldName,t.field.visibility==="Required"||t.field.visibility==="0"?n("span",{style:{color:"red"},children:"*"}):""]}),n(Ft,{dateAdapter:Ot,children:n(Gt,{slotProps:{textField:{size:"small"}},value:b[d],maxDate:new Date(9999,12,31),onChange:c=>i(z({keyName:d,data:Date.parse(c)})),required:t.field.visibility==="0"||t.field.visibility==="Required"})})]})})}const rn=t=>{let i=Object==null?void 0:Object.entries(t==null?void 0:t.controlDataTabDetails);const[d,S]=a.useState([]);return a.useEffect(()=>{S(i==null?void 0:i.map(b=>{var h,C;return l(o,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...J},children:[n(o,{container:!0,children:n(x,{sx:{fontSize:"12px",fontWeight:"700"},children:b[0]})}),n(B,{children:n(o,{container:!0,spacing:1,children:(C=(h=[...b[1]].filter(r=>(r==null?void 0:r.visibility)!="Hidden"))==null?void 0:h.sort((r,m)=>(r==null?void 0:r.sequenceNo)-(m==null?void 0:m.sequenceNo)))==null?void 0:C.map(r=>n(U,{field:r,dropDownData:t==null?void 0:t.dropDownData,country:t.country}))})})]})}))},[t==null?void 0:t.basicDataTabDetails]),n(M,{children:d})},an=t=>{let i=Object==null?void 0:Object.entries(t==null?void 0:t.typeDescriptionTabDetails);console.log("basic",t,i);const[d,S]=a.useState([]);return a.useEffect(()=>{S(i==null?void 0:i.map(b=>{var h,C;return l(o,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...J},children:[n(o,{container:!0,children:n(x,{sx:{fontSize:"12px",fontWeight:"700"},children:b[0]})}),n(B,{children:n(o,{container:!0,spacing:1,children:(C=(h=[...b[1]].filter(r=>(r==null?void 0:r.visibility)!="Hidden"))==null?void 0:h.sort((r,m)=>(r==null?void 0:r.sequenceNo)-(m==null?void 0:m.sequenceNo)))==null?void 0:C.map(r=>n(U,{field:r,dropDownData:t==null?void 0:t.dropDownData,country:t.country}))})})]})}))},[t==null?void 0:t.basicDataTabDetails]),n(M,{children:d})},cn=t=>{let i=Object==null?void 0:Object.entries(t==null?void 0:t.createBankInterestTabDetails);console.log("basic",t,i);const[d,S]=a.useState([]);return a.useEffect(()=>{S(i==null?void 0:i.map(b=>{var h,C;return l(o,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...J},children:[n(o,{container:!0,children:n(x,{sx:{fontSize:"12px",fontWeight:"700"},children:b[0]})}),n(B,{children:n(o,{container:!0,spacing:1,children:(C=(h=[...b[1]].filter(r=>(r==null?void 0:r.visibility)!="Hidden"))==null?void 0:h.sort((r,m)=>(r==null?void 0:r.sequenceNo)-(m==null?void 0:m.sequenceNo)))==null?void 0:C.map(r=>n(U,{field:r,dropDownData:t==null?void 0:t.dropDownData,country:t.country}))})})]})}))},[t==null?void 0:t.basicDataTabDetails]),n(M,{children:d})},on=t=>{let i=Object==null?void 0:Object.entries(t==null?void 0:t.keywordTranslationTabDetails);console.log("basic",t,i);const[d,S]=a.useState([]);return a.useEffect(()=>{S(i==null?void 0:i.map(b=>{var h,C;return l(o,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...J},children:[n(o,{container:!0,children:n(x,{sx:{fontSize:"12px",fontWeight:"700"},children:b[0]})}),n(B,{children:n(o,{container:!0,spacing:1,children:(C=(h=[...b[1]].filter(r=>(r==null?void 0:r.visibility)!="Hidden"))==null?void 0:h.sort((r,m)=>(r==null?void 0:r.sequenceNo)-(m==null?void 0:m.sequenceNo)))==null?void 0:C.map(r=>n(U,{field:r,dropDownData:t==null?void 0:t.dropDownData,country:t.country}))})})]})}))},[t==null?void 0:t.basicDataTabDetails]),n(M,{children:d})},sn=t=>{let i=Object==null?void 0:Object.entries(t==null?void 0:t.informationTabDetails);console.log("basic",t,i);const[d,S]=a.useState([]);return a.useEffect(()=>{S(i==null?void 0:i.map(b=>{var h,C;return l(o,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...J},children:[n(o,{container:!0,children:n(x,{sx:{fontSize:"12px",fontWeight:"700"},children:b[0]})}),n(B,{children:n(o,{container:!0,spacing:1,children:(C=(h=[...b[1]].filter(r=>(r==null?void 0:r.visibility)!="Hidden"))==null?void 0:h.sort((r,m)=>(r==null?void 0:r.sequenceNo)-(m==null?void 0:m.sequenceNo)))==null?void 0:C.map(r=>n(U,{field:r,dropDownData:t==null?void 0:t.dropDownData,country:t.country}))})})]})}))},[t==null?void 0:t.basicDataTabDetails]),n(M,{children:d})},En=()=>{var ge,xe,be,Ce,De,Se,ve,Te,Ae,ke,we,Ee,Re,Ie,ye,Ne,Be,Fe,Oe,qe,Me,Ve,je,We,$e;const[t,i]=a.useState(0),[d,S]=a.useState({}),[b,h]=a.useState(!1),[C,r]=a.useState(""),[m,T]=a.useState(!1),[Z,w]=a.useState(!0),[c,u]=a.useState(!1),[I,A]=a.useState(!1),[Qe,L]=a.useState(!1),[Ze,F]=a.useState(!1),[Le,G]=a.useState(!1),[X,Ge]=a.useState([]),[Pe,ie]=a.useState(!1),[et,ce]=a.useState(!1),[tt,$]=a.useState(!0),[nt,V]=a.useState(!1),[rt,at]=a.useState(""),[P,it]=a.useState(""),[ee,oe]=a.useState(""),[ct,se]=a.useState(!1),[le,de]=a.useState(!0),[ln,p]=a.useState(!1),ue=Mt(),ot=Vt(),st=Ye(),f=ot.state;console.log("displayData",f);const lt=v(s=>s.generalLedger.generalLedgerControlData),dt=v(s=>s.generalLedger.generalLedgerTypeDescription),ut=v(s=>s.generalLedger.generalLedgerCreateBankInterest),ht=v(s=>s.generalLedger.generalLedgerKeywordTranslation),ft=v(s=>s.generalLedger.generalLedgerInformation),e=v(s=>s.generalLedger.singleGLPayload);let R=v(s=>s.userManagement.userData);console.log("glpaylaod",e);const mt=v(s=>s.generalLedger.singleGLPayload),gt=v(s=>s.generalLedger.requiredFields);let he=["TYPE/ DESCRIPTION","CONTROL DATA","CREATE/ BANK/ INTEREST","KEYWORD/ TRANSLATION","INFORMATION","ATTACHMENTS & COMMENTS"];const xt=s=>{switch(s){case 0:return n(an,{typeDescriptionTabDetails:dt,dropDownData:d});case 1:return n(rn,{controlDataTabDetails:lt,dropDownData:d});case 2:return n(cn,{createBankInterestTabDetails:ut,dropDownData:d});case 3:return n(on,{keywordTranslationTabDetails:ht,dropDownData:d});case 4:return n(sn,{informationTabDetails:ft,dropDownData:d});case 5:return n(Pt,{title:"GeneralLedger",useMetaData:!1,artifactId:P,artifactName:"GeneralLedger"});default:return"Unknown step"}};a.useEffect(()=>{st(jt(X))},[X]);const fe=()=>Zt(mt,gt,Ge);a.useEffect(()=>{it(Wt("GL"))},[]);const me=()=>{ie(!0)},bt=()=>{fe()?i(k=>k+1):me()},Ct=()=>{$(!0),fe()?i(k=>k-1):me()},j=()=>{G(!0)},te=()=>{G(!1)},Dt=()=>{G(!1)},Y=()=>{de(!0),se(!1)},St=()=>{ie(!1)},vt=()=>{et?(L(!1),ce(!1)):(L(!1),ue("/masterDataCockpit/generalLedger"))};var ne={GeneralLedgerID:"",Action:"I",RequestID:"",TaskStatus:"",TaskId:"",Remarks:ee||"",Info:"",CreationId:"",EditId:"",DeleteId:"",MassCreationId:"",MassEditId:"",MassDeleteId:"",RequestType:"Create",ReqCreatedBy:R==null?void 0:R.user_id,ReqCreatedOn:"",ReqUpdatedOn:"",RequestStatus:"",Testrun:le,COA:(xe=(ge=f==null?void 0:f.chartOfAccounts)==null?void 0:ge.newChartOfAccount)==null?void 0:xe.code,CompanyCode:(Ce=(be=f==null?void 0:f.companyCode)==null?void 0:be.newCompanyCode)==null?void 0:Ce.code,CoCodeToExtend:"",GLAccount:f!=null&&f.newGLAccount?f==null?void 0:f.newGLAccount:" ",Accounttype:e!=null&&e.AccountType?(De=e==null?void 0:e.AccountType)==null?void 0:De.code:"",AccountGroup:e!=null&&e.AccountGroup?(Se=e==null?void 0:e.AccountGroup)==null?void 0:Se.code:"",GLname:e!=null&&e.ShortText?e==null?void 0:e.ShortText:"",Description:e!=null&&e.LongText?e==null?void 0:e.LongText:"",TradingPartner:e!=null&&e.TradingPartner?(ve=e==null?void 0:e.TradingPartner)==null?void 0:ve.code:"",GroupAccNo:e!=null&&e.GroupAccountNumber?(Te=e==null?void 0:e.GroupAccountNumber)==null?void 0:Te.code:"121100",AccountCurrency:e!=null&&e.AccountCurrency?(Ae=e==null?void 0:e.AccountCurrency)==null?void 0:Ae.code:"",Exchangerate:e!=null&&e.ExchangeRateDifferenceKey?(ke=e==null?void 0:e.ExchangeRateDifferenceKey)==null?void 0:ke.code:"",Balanceinlocrcy:(e==null?void 0:e.OnlyBalanceInLocalCurrency)===!0?"X":"",Taxcategory:e!=null&&e.TaxCategory?(we=e==null?void 0:e.TaxCategory)==null?void 0:we.code:"",Pstnwotax:(e==null?void 0:e.PostingWithoutTaxAllowed)===!0?"X":"",ReconAcc:e!=null&&e.ReconAccountForAccountType?(Ee=e==null?void 0:e.ReconAccountForAccountType)==null?void 0:Ee.code:"",Valuationgrp:e!=null&&e.ValuationGroup?e==null?void 0:e.ValuationGroup:"",AlterAccno:e!=null&&e.AlternativeAccountNumber?(Re=e==null?void 0:e.AlternativeAccountNumber)==null?void 0:Re.code:"",Openitmmanage:(e==null?void 0:e.OpenItemManagement)===!0?"X":"",Sortkey:e!=null&&e.SortKey?(Ie=e==null?void 0:e.SortKey)==null?void 0:Ie.code:"",CostEleCategory:e!=null&&e.CostElementCategory?(ye=e==null?void 0:e.CostElementCategory)==null?void 0:ye.code:"",FieldStsGrp:e!=null&&e.FieldStatusGroup?(Ne=e==null?void 0:e.FieldStatusGroup)==null?void 0:Ne.code:"",PostAuto:(e==null?void 0:e.PostAutomaticallyOnly)===!0?"X":"",Supplementautopost:(e==null?void 0:e.SupplementAutoPostings)===!0?"X":"",Planninglevel:e!=null&&e.PlanningLevel?(Be=e==null?void 0:e.PlanningLevel)==null?void 0:Be.code:"",Relvnttocashflow:(e==null?void 0:e.RelevantToCashFlows)===!0?"X":"",HouseBank:e!=null&&e.HouseBank?(Fe=e==null?void 0:e.HouseBank)==null?void 0:Fe.code:"",AccountId:e!=null&&e.AccountID?(Oe=e==null?void 0:e.AccountID)==null?void 0:Oe.code:"",Interestindicator:e!=null&&e.InterestIndicator?(qe=e==null?void 0:e.InterestIndicator)==null?void 0:qe.code:"",ICfrequency:e!=null&&e.InterestCalculationFrequency?(Me=e==null?void 0:e.InterestCalculationFrequency)==null?void 0:Me.code:"",KeydateofLIC:e!=null&&e.KeyDateOfLastInterestCalculation?"/Date("+(e==null?void 0:e.KeyDateOfLastInterestCalculation)+")/":"",LastIntrstundate:e!=null&&e.DateOfLastInterestRun?"/Date("+(e==null?void 0:e.DateOfLastInterestRun)+")/":"",AccmngExistsys:"",Infationkey:"",Tolerancegrp:"",AuthGroup:"",AccountClerk:"",ReconAccReady:"",PostingBlocked:"",PlanningBlocked:""};const re=()=>{L(!0)},Tt=()=>{var W,E,pe;p(!0);var s={glName:(W=e==null?void 0:e.ShortText)==null?void 0:W.toUpperCase(),compCode:(pe=(E=f==null?void 0:f.companyCode)==null?void 0:E.newCompanyCode)==null?void 0:pe.code};const k=D=>{var Q,He,ze;F(),D.statusCode===201?(h("Create"),console.log("success"),h("Create"),r("All Data has been Validated. General Ledger can be Send for Review"),$(!1),V(!1),T("success"),w(!1),u(!0),re(),A(!0),ce(!0),p(!1)):(p(!1),h("Error"),u(!1),r(`${(Q=D==null?void 0:D.body)!=null&&Q.message[0]?(He=D==null?void 0:D.body)==null?void 0:He.message[0]:(ze=D==null?void 0:D.body)==null?void 0:ze.value}`),V(!1),T("danger"),w(!1),A(!0),j(),$(!0)),handleClose()},g=D=>{console.log(D)},O=D=>{D.body.length===0||!D.body.some(Q=>Q.toUpperCase()===s)?(p(!1),$(!1),y(`/${_}/alter/validateSingleGeneralLedger`,"post",k,g,ne)):(p(!1),h("Duplicate Check"),u(!1),r("There is a direct match for the Cost Center name. Please change the name."),V(!1),T("danger"),w(!1),A(!0),j(),$(!0))},H=D=>{console.log(D)};y(`/${_}/alter/fetchGlNameNCompCodeDupliChk`,"post",O,H,s)},At=()=>{T(!1),j(),h("Confirm"),r("Do You Want to Save as Draft ?"),V(!0),at("proceed")},kt=()=>{F(!0);const s=g=>{if(te(),F(!1),g.statusCode===200){console.log("success"),h("Create"),r(`General Ledger has been Saved with ID NLS${g.body}`),V(!1),T("success"),w(!1),u(!0),re(),A(!0);const O={artifactId:P,createdBy:R==null?void 0:R.emailId,artifactType:"GeneralLedger",requestId:`NLS${g==null?void 0:g.body}`},H=E=>{console.log("Second API success",E)},W=E=>{console.error("Second API error",E)};y(`/${Xe}/documentManagement/updateDocRequestId`,"post",H,W,O)}else h("Save"),u(!1),r("Failed Saving the Data"),V(!1),T("danger"),w(!1),A(!0),j();handleClose()},k=g=>{console.log(g)};y(`/${_}/alter/generalLedgerAsDraft`,"post",s,k,ne)},wt=()=>{F(!0);const s=g=>{if(F(!1),g.statusCode===200){console.log("success"),h("Create"),r(`General Ledger has been Submitted for review NLS${g.body}`),T("success"),w(!1),u(!0),re(),A(!0),F(!1);const O={artifactId:P,createdBy:R==null?void 0:R.emailId,artifactType:"GeneralLedger",requestId:`NLS${g==null?void 0:g.body}`},H=E=>{console.log("Second API success",E)},W=E=>{console.error("Second API error",E)};y(`/${Xe}/documentManagement/updateDocRequestId`,"post",H,W,O)}else h("Create"),u(!1),r("Creation Failed"),T("danger"),w(!1),A(!0),j(),F(!1);handleClose()},k=g=>{console.log(g)};y(`/${_}/alter/generalLedgerSubmitForReview`,"post",s,k,ne)},Et=(s,k)=>{const g=s.target.value;if(g.length>0&&g[0]===" ")oe(g.trimStart());else{let O=g.toUpperCase();oe(O)}},Rt=()=>{de(!1),se(!0)},It=()=>{wt(),Y()};return console.log("testrun",le),n(M,{children:Ze===!0?n($t,{}):l("div",{children:[l(pt,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:ct,onClose:Y,children:[l(Ht,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[n(x,{variant:"h6",children:"Remarks"}),n(_e,{sx:{width:"max-content"},onClick:Y,children:n(zt,{})})]}),n(Kt,{sx:{padding:".5rem 1rem"},children:n(N,{children:n(B,{sx:{minWidth:400},children:n(_t,{sx:{height:"auto"},fullWidth:!0,children:n(ae,{sx:{backgroundColor:"#F5F5F5"},value:ee.toUpperCase(),onChange:Et,multiline:!0,placeholder:"Enter Remarks",inputProps:{maxLength:254}})})})})}),l(Jt,{sx:{display:"flex",justifyContent:"end"},children:[n(q,{sx:{width:"max-content",textTransform:"capitalize"},onClick:Y,children:"Cancel"}),n(q,{className:"button_primary--normal",type:"save",onClick:It,variant:"contained",children:"Submit"})]})]}),n(Ut,{dialogState:Le,openReusableDialog:j,closeReusableDialog:te,dialogTitle:b,dialogMessage:C,handleDialogConfirm:te,dialogOkText:"OK",showExtraButton:nt,showCancelButton:!0,dialogSeverity:m,handleDialogReject:Dt,handleExtraText:rt,handleExtraButton:kt}),X.length!=0&&n(Je,{openSnackBar:Pe,alertMsg:"Please fill the following Field: "+X.join(", "),handleSnackBarClose:St}),c&&n(Je,{openSnackBar:Qe,alertMsg:C,handleSnackBarClose:vt}),n(o,{container:!0,style:{...Ue,backgroundColor:"#FAFCFF"},children:l(o,{sx:{width:"inherit"},children:[n(o,{item:!0,md:7,style:{padding:"16px",display:"flex"},children:l(o,{item:!0,md:5,sx:{display:"flex"},children:[n(o,{children:n(_e,{color:"primary","aria-label":"upload picture",component:"label",sx:Xt,children:n(Lt,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{ue("/masterDataCockpit/generalLedger")}})})}),l(o,{children:[n(x,{variant:"h3",children:n("strong",{children:"Create General Ledger"})}),n(x,{variant:"body2",color:"#777",children:"This view creates a new General Ledger"})]})]})}),n(o,{container:!0,style:{padding:"0 1rem 0 1rem"},children:l(o,{container:!0,sx:Ue,children:[l(o,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:[l(B,{width:"70%",sx:{marginLeft:"40px"},children:[n(o,{item:!0,sx:{paddingTop:"2px !important"},children:l(N,{flexDirection:"row",children:[n("div",{style:{width:"15%"},children:n(x,{variant:"body2",color:"#777",children:"Chart Of Account"})}),l(x,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[":"," ",(je=(Ve=f==null?void 0:f.chartOfAccounts)==null?void 0:Ve.newChartOfAccount)==null?void 0:je.code]})]})}),n(o,{item:!0,sx:{paddingTop:"2px !important"},children:l(N,{flexDirection:"row",children:[n("div",{style:{width:"15%"},children:n(x,{variant:"body2",color:"#777",children:"Company Code"})}),l(x,{variant:"body2",fontWeight:"bold",children:[": ",($e=(We=f==null?void 0:f.companyCode)==null?void 0:We.newCompanyCode)==null?void 0:$e.code]})]})}),n(o,{item:!0,sx:{paddingTop:"2px !important"},children:l(N,{flexDirection:"row",children:[n("div",{style:{width:"15%"},children:n(x,{variant:"body2",color:"#777",children:"G/L Account"})}),l(x,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[": ",f==null?void 0:f.newGLAccount]})]})})]}),n(B,{width:"30%",sx:{marginLeft:"40px"},children:n(o,{item:!0,children:l(N,{flexDirection:"row",children:[n(x,{variant:"body2",color:"#777",style:{width:"30%"}}),n(x,{variant:"body2",fontWeight:"bold",sx:{width:"8%",textAlign:"center"}}),n(x,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start"})]})})})]}),n(o,{container:!0,children:n(en,{activeStep:t,sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},children:he.map((s,k)=>n(tn,{children:n(nn,{sx:{fontWeight:"700"},children:s})},s))})}),n(o,{container:!0,children:xt(t)})]})})]})}),n(Yt,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:l(Qt,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(q,{variant:"contained",size:"small",sx:{...K,mr:1},onClick:At,children:"Save As Draft"}),n(q,{variant:"contained",size:"small",sx:{...K,mr:1},onClick:Ct,disabled:t===0,children:"Back"}),t===he.length-1?l(M,{children:[n(q,{variant:"contained",size:"small",sx:{...K,mr:1},onClick:Tt,children:"Validate"}),n(q,{variant:"contained",size:"small",sx:{...K,mr:1},onClick:Rt,disabled:tt,children:"Submit For Review"})]}):n(q,{variant:"contained",size:"small",sx:{...K,mr:1},onClick:bt,children:"Next"})]})})]})})};export{En as default};
