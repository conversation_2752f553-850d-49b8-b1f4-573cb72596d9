import React, { useEffect, useState } from "react";
import {
  St<PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  TextField,
  Grid,
  Autocomplete,
  IconButton,
  FormControlLabel,
  Checkbox,
} from "@mui/material";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import SaveIcon from "@mui/icons-material/Save";
import { DateRangePicker } from "rsuite";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";

import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { setPayload } from "../../../app/editPayloadSlice";
import { setSingleCostCenterPayload } from "../../../app/costCenterTabsSlice";
import { doAjax } from "../../Common/fetchService";
import {
  destination_CostCenter,
  destination_GeneralLedger,
} from "../../../destinationVariables";
import { setDropDown } from "../../../app/dropDownDataSlice";
import moment from "moment";
import { setGLRequiredFields } from "../../../app/generalLedgerTabSlice";
function transformApiData(apiValue, dropDownData) {
  if (Array.isArray(dropDownData)) {
    const matchingOption = dropDownData.find(
      (option) => option.code === apiValue
    );
    return matchingOption || "";
  } else {
    return "";
  }
}
const EditableFieldForGL = ({
  label,
  value,
  units,
  onSave,
  isEditMode,
  visibility,
  length,
  type,
  taskRequestId,
  data,
  generalLedgerRowData,
}) => {
  const [editedValue, setEditedValue] = useState(value);
  const [changeStatus, setChangeStatus] = useState(false);
  const [error, setError] = useState(false);
  const dropDownData = useSelector((state) => state.AllDropDown.dropDown);
  const dispatch = useDispatch();
  const transformedValue = transformApiData(editedValue, dropDownData);
  let taskDetails = useSelector((state) => state.userManagement.taskData);
  const appSettings = useSelector((state) => state.appSettings);
  const requestId = taskDetails?.subject;
  const singleGLPayload = useSelector((state) => state.edit.payload);
  const [useEffectState, setUseEffectState] = useState(true);
  let taskData = useSelector((state) => state?.initialData?.IWMMyTask);
  const fieldData = {
    label,
    value: editedValue,
    units,
    type,
  };
  const requiredField = useSelector((state) => state.generalLedger.errorFields);
  // console.log("fieldData", fieldData);

  let key = label
    .replaceAll("(", "")
    .replaceAll(")", "")
    .replaceAll("/", "")
    .replaceAll("-", "")
    .replaceAll(".", "")
    .split(" ")
    .join("");
  useEffect(() => {
    setEditedValue(value);
  }, [value]);

  console.log("glLabel", label, data[key]);

  useEffect(() => {
    console.log("insideuseeffect");
    if (visibility === "0" || visibility === "Required") {
      console.log("insidevisibility");
      dispatch(setGLRequiredFields(key));
    }
  }, []);
  const onEdit = (newValue) => {
    dispatch(
      setPayload({
        keyname: key
          .replaceAll("(", "")
          .replaceAll(")", "")
          .replaceAll("/", "")
          .replaceAll("-", "")
          .replaceAll(".", "")
          .split(" ")
          .join(""),
        data: newValue,
      })
    );
  };
  const getAccountId = (compCode, houseBank) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "AccountID", data: data.body }));
    };
    const hError = (error) => {
      console.log(error, "error in dojax");
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getAccountId?companyCode=${compCode}&houseBank=${houseBank}`,
      "get",
      hSuccess,
      hError
    );
  };

  useEffect(() => {
    if (label === "House Bank") {
      getAccountId(
        generalLedgerRowData?.compCode || taskData?.compCode,
        singleGLPayload?.HouseBank
      );
    }
  }, []);

  // let date = "/Date(*************)/";
  // console.log('slice', date.slice(6).slice(0, -2))
  return (
    <>
      {!isEditMode ? (
        (taskRequestId && visibility === "Hidden") ||
        (requestId && visibility === "Hidden") ? null : (
          <Grid item>
            <Stack>
              <Typography variant="body2" color="#777">
                {label}
                {visibility === "Required" || visibility === "0" ? (
                  <span style={{ color: "red" }}>*</span>
                ) : (
                  ""
                )}
              </Typography>
              <Typography variant="body2" fontWeight="bold">
                {}
                {type === "Radio Button" ? (
                  <Checkbox sx={{ padding: 0 }} checked={data[key]} disabled />
                ) : (
                  ""
                )}
                {type === "Calendar"
                  ? moment(data[key]).format(appSettings?.dateFormat)
                  : data[key]}
              </Typography>
            </Stack>
          </Grid>
        )
      ) : visibility === "Hidden" ? null : (
        <Grid item>
          <Stack>
            {isEditMode ? (
              <div>
                <Typography variant="body2" color="#777">
                  {label}
                  {visibility === "Required" || visibility === "0" ? (
                    <span style={{ color: "red" }}>*</span>
                  ) : (
                    ""
                  )}
                </Typography>

                {type === "Drop Down" ? (
                  <Autocomplete
                    options={dropDownData[key] ?? []}
                    value={
                      (data[key] &&
                        dropDownData[key]?.filter(
                          (x) => x.code === data[key]
                        ) &&
                        dropDownData[key]?.filter(
                          (x) => x.code === data[key]
                        )[0]) ||
                      ""
                    }
                    onChange={(event, newValue, reason) => {
                      if (label === "House Bank") {
                        getAccountId(
                          generalLedgerRowData?.compCode || taskData?.compCode,
                          newValue.code
                        );
                      }
                      if (visibility === "Required" || visibility === "0") {
                        if (newValue === null) setError(true);
                      }

                      if (reason === "clear") {
                        onEdit("");
                      } else {
                        onEdit(newValue?.code);
                      }
                      // setEditedValue(newValue.code);
                      setChangeStatus(true);
                    }}
                    getOptionLabel={(option) => {
                      return option === "" || option?.code === ""
                        ? ""
                        : `${option?.code} - ${option?.desc}` ?? "";
                    }}
                    renderOption={(props, option) => {
                      return (
                        <li {...props}>
                          <Typography style={{ fontSize: 12 }}>
                            {`${option?.code} - ${option?.desc}`}
                          </Typography>
                        </li>
                      );
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        variant="outlined"
                        placeholder={`Select ${fieldData.label}`}
                        size="small"
                        error={error}
                      />
                    )}
                  />
                ) : type === "Input" ? (
                  <TextField
                    variant="outlined"
                    size="small"
                    fullWidth
                    value={data[key].toUpperCase()}
                    placeholder={`Enter ${fieldData.label}`}
                    inputProps={{ maxLength: length }}
                    onChange={(event) => {
                      const newValue = event.target.value;
                      if (newValue.length > 0 && newValue[0] === " ") {
                        onEdit(newValue.trimStart());
                      } else {
                        let costCenterUpperCase = newValue.toUpperCase();
                        onEdit(costCenterUpperCase);
                      }
                      if (visibility === "Required" || visibility === "0") {
                        if (newValue.length <= 0) setError(true);
                      }
                      setEditedValue(newValue.toUpperCase());
                    }}
                    error={error}
                  />
                ) : type === "Calendar" ? (
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DatePicker
                      slotProps={{ textField: { size: "small" } }}
                      value={data[key] === "" ? null : data[key]}
                      placeholder="Select Date Range"
                      maxDate={new Date(9999, 12, 31)}
                      onChange={(newValue) => {
                        const newDate = Date.parse(newValue);
                        onEdit(newDate);
                        setEditedValue(newDate);
                      }}
                    />
                  </LocalizationProvider>
                ) : type === "Radio Button" ? (
                  <Grid item md={2}>
                    <Checkbox
                      sx={{ padding: 0 }}
                      checked={data[key]}
                      onChange={(event, newValue) => {
                        onEdit(newValue);
                        setEditedValue(newValue);
                      }}
                    />
                  </Grid>
                ) : (
                  ""
                )}
              </div>
            ) : (
              ""
            )}
          </Stack>
        </Grid>
      )}
    </>
  );
};

export default EditableFieldForGL;
