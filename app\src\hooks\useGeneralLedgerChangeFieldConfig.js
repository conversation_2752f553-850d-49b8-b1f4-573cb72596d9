import { changeTemplateDT } from "@app/tabsDetailsSlice";
import { doAjax } from "@components/Common/fetchService";
import React from "react";
import { useSelector, useDispatch } from "react-redux";
import { destination_IDM } from "../destinationVariables";
import { END_POINTS } from "@constant/apiEndPoints";
import { API_CODE } from "@constant/enum";
import { setDropDown } from "../app/dropDownDataSlice";
import useLogger from "./useLogger";
import {setChangeFieldSelectionData} from "../app/costCenterTabsSlice";

const useGeneralLedgerChangeFieldConfig = () => {

  const currentHash = window.location.hash; 
  const parts = currentHash.split("/");
  const activeLocation = parts[parts.length - 1]; 

  console.log("activeLocation", activeLocation);

  const { customError } = useLogger();
  const initialPayload = useSelector((state) => state?.costCenter?.payload?.requestHeaderData);
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const dispatch = useDispatch();

  const module ="General Ledger";

  // const getChangeTemplate = (templateName) => {
  // const payload = {
  // decisionTableId: null,
  // decisionTableName: "MDG_CHANGE_TEMPLATE_DT",
  // version: "v4",
  // rulePolicy: null,
  // validityDate: null,
  // conditions: [
  //   {
  //     "MDG_CONDITIONS.MDG_MODULE": module,
  //     // "MDG_CONDITIONS.MDG_MAT_TEMPLATE":
  //     //   initialPayload?.TemplateName || templateName,
  //     "MDG_CONDITIONS.MDG_MAT_ROLE": "REQ_DISPLAY",
  //   },
  // ],
  // systemFilters: null,
  // systemOrders: null,
  // filterString: null,
  // };
  // const hSuccess = (data) => {
  //   console.log("datadatadata",data)
  //   if (data.statusCode === API_CODE.STATUS_200) {
  //   const responseData =
  //     data?.data?.result?.[0]?.MDG_CHANGE_TEMPLATE_ACTION_TYPE || [];
  //   dispatch(setChangeFieldSelectionData(responseData));
  //   // Get unique template names
  //   const templateNames = [
  //   ...new Set(
  //   responseData.map((item) => item?.MDG_CHANGE_TEMPLATE_NAME).filter(Boolean)
  //   ),
  //   ].map((name) => ({ code: name }));

  //   console.log("Unique Template Names:", templateNames);
  //   dispatch(setDropDown({keyName:"TemplateName", data: templateNames}));
  //   // return templateNames;
  //   } else {
  //   customError("Failed to fetch data");
  //   return [];
  // }
  // };

  // const hError = (error) => {
  // customError(error);
  // };

  // const endpoint =
  // applicationConfig.environment === "localhost"
  //   ? END_POINTS.INVOKE_RULES.LOCAL
  //   : END_POINTS.INVOKE_RULES.PROD;

  // doAjax(`/${destination_IDM}${endpoint}`, "post", hSuccess, hError, payload);
  // };

  
  const getChangeTemplate = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_ET_CHNG_FIELD_SELECTION_DT",
      version: "v4",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE": "GENERAL LEDGER",
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    //setIsLoading(true);
    // const formData = new FormData();
    const hSuccess = (data) => {
      //setIsLoading(false);
      if (data.statusCode === 200) {
        let responseData =
          data?.data?.result[0]?.MDG_ET_CHNG_FIELD_SELECTION_ACTION_TYPE;
           dispatch(setChangeFieldSelectionData(responseData));
        //const uniqueLevels = [...new Set(responseData?.map(item => item.MDG_FIELD_SELECTION_LVL))];
        const templateNames = [
        ...new Set(
        responseData.map((item) => item?.MDG_FIELD_SELECTION_LVL).filter(Boolean)
        ),
        ].map((name) => ({ code: name }));
        console.log(templateNames,"uniqueLevelsdata")
        dispatch(setDropDown({keyName:"TemplateName", data: templateNames}));
        dispatch(setChangeFieldSelectionData(uniqueLevels));
        console.log(uniqueLevels);
        console.log(responseData,"responseData")
        let fieldSelectionCompanyCode = [];
        let fieldSelectionCOA = [];
        let fieldSelectionTempBlock = [];
        console.log(responseData, "responseData===");
        responseData?.map((element, index) => {
          console.log("element====", element);

          if (element.MDG_FIELD_SELECTION_LVL == "COMPANY CODE") {
            let COHash = {};
            COHash["id"] = index;
            COHash["name"] = element.MDG_SELECT_OPTION;
            fieldSelectionCompanyCode.push(COHash);
          } else if (element.MDG_FIELD_SELECTION_LVL == "CHART OF ACCOUNT") {
            let COAHash = {};
            COAHash["id"] = index;
            COAHash["name"] = element.MDG_SELECT_OPTION;
            fieldSelectionCOA.push(COAHash);
          } else {
            let COAHash = {};
            COAHash["id"] = index;
            COAHash["name"] = element.MDG_SELECT_OPTION;
            fieldSelectionTempBlock.push(COAHash);
            //console.log(element.MDG_FIELD_SELECTION_LVL ,"jk")
          }
        });
        console.log(
          fieldSelectionCompanyCode,
          fieldSelectionCOA,
          fieldSelectionTempBlock,
          "fieldSelectionCompanyCode"
        );
        //const distinctNames = [];
        const uniqueNames = new Set();
        const distinctCompanyCodeData = fieldSelectionCompanyCode.filter(
          (obj) => {
            if (!uniqueNames.has(obj.name)) {
              uniqueNames.add(obj.name);
              return true;
            }
            return false;
          }
        );
        const uniqueNamesCOA = new Set();
        const distinctCartOfAccoutData = fieldSelectionCOA.filter((obj) => {
          if (!uniqueNamesCOA.has(obj.name)) {
            uniqueNamesCOA.add(obj.name);
            return true;
          }
          return false;
        });
        console.log(
          distinctCompanyCodeData,
          distinctCartOfAccoutData,
          "distinctCompanyCodeData"
        );
        //setRuleData(templateData);
        setDataList(distinctCompanyCodeData);
        setDataListCOA(distinctCartOfAccoutData);
        setDataListBlocked(fieldSelectionTempBlock);
      } else {
      }
      handleClose();
    };

    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(
        `/${destination_IDM}/rest/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      doAjax(
        `/${destination_IDM}/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };

  
  return { getChangeTemplate };
};

export default useGeneralLedgerChangeFieldConfig;
