import React from 'react';
import { Autocomplete, TextField, Checkbox, Chip, Popover, Box, CircularProgress } from '@mui/material';
import { FixedSizeList } from 'react-window';
import { colors } from '@constant/colors';

const LISTBOX_PADDING = 8; // px

const OuterElementContext = React.createContext({});

const OuterElementType = React.forwardRef((props, ref) => {
  const outerProps = React.useContext(OuterElementContext);
  return <div ref={ref} {...props} {...outerProps} />;
});

function ListboxComponent(props) {
  const { children, ...other } = props;
  const itemData = [];
  children.forEach((item) => {
    itemData.push(item);
  });

  const itemCount = itemData.length;
  const itemSize = 48;

  const getHeight = () => {
    if (itemCount > 8) {
      return 8 * itemSize;
    }
    return itemData.length * itemSize;
  };

  return (
    <div ref={props.ref}>
      <OuterElementContext.Provider value={other}>
        <FixedSizeList
          itemData={itemData}
          height={getHeight() + 2 * LISTBOX_PADDING}
          width="100%"
          outerElementType={OuterElementType}
          innerElementType="ul"
          itemSize={itemSize}
          overscanCount={5}
          itemCount={itemCount}
        >
          {({ data, index, style }) => {
            const dataSet = data[index];
            const inlineStyle = {
              ...style,
              top: style.top + LISTBOX_PADDING,
            };

            return React.cloneElement(dataSet, {
              style: inlineStyle,
            });
          }}
        </FixedSizeList>
      </OuterElementContext.Provider>
    </div>
  );
}

const FilterChangeDropdown = ({
  param,
  mandatory = false,
  dropDownData,
  allDropDownData,
  selectedValues,
  inputState,
  handleSelectAll,
  handleSelectionChange,
  handleMatInputChange,
  handleScroll,
  dropdownRef,
  errors,
  formatOptionLabel,
  handlePopoverOpen,
  handlePopoverClose,
  handleMouseEnterPopover,
  handleMouseLeavePopover,
  isPopoverVisible,
  popoverId,
  popoverAnchorEl,
  popoverRef,
  popoverContent,
  isMaterialNum = false,
  isLoading = false,
  isSelectAll = false,
  singleSelect = false,
}) => {

  const getOptions = () => {
    const options = isMaterialNum 
      ? (dropDownData?.[param?.key] || [])
      : (dropDownData?.[param?.key] || allDropDownData?.[param?.key] || []);
    return isSelectAll && options.length > 0 && !singleSelect ? ["Select All", ...options] : options;
  };

  // For single select mode
  const getSingleValue = () => {
    if (!singleSelect) return selectedValues[param.key] || [];
    const currentValue = selectedValues[param.key];
    if (Array.isArray(currentValue) && currentValue.length > 0) {
      return currentValue[0];
    }
    return null;
  };

  return (
    <Autocomplete
      key={param.key}
      multiple={!singleSelect}
      disableListWrap
      ListboxComponent={ListboxComponent}
      options={getOptions()}
      getOptionLabel={(option) =>
        typeof option === "string"
          ? option
          : option === "Select All"
          ? "Select All"
          : formatOptionLabel(option)
      }
      value={singleSelect ? getSingleValue() : selectedValues[param.key] || []}
      inputValue={isMaterialNum && !singleSelect ? inputState?.code : undefined}
      onChange={(event, newValue) => {
        if (!singleSelect && newValue.includes("Select All")) {
          handleSelectAll(
            param.key,
            getOptions().filter(option => option !== "Select All")
          );
        } else if (singleSelect) {
          handleSelectionChange(param.key, newValue ? [newValue] : []);
        } else {
          handleSelectionChange(param.key, newValue);
        }
      }}
      disableCloseOnSelect={!singleSelect}
      renderOption={(props, option, { selected }) => (
        <li {...props} style={{ 
          display: 'flex',
          alignItems: 'center',
          width: '100%',
          cursor: 'pointer',
        }}>
          {!singleSelect && (
            <Checkbox
              checked={
                option === "Select All"
                  ? selectedValues[param.key]?.length === (getOptions().length - 1) 
                  : selectedValues[param.key]?.some(
                      (selectedOption) => selectedOption?.code === option?.code
                    )
              }
              sx={{ marginRight: 1 }}
            />
          )}
          {typeof option === "string" || option === "Select All" ? (
            <span style={{ 
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis'
            }}
            title={option}
            >
              {option}
            </span>
          ) : (
            <span style={{ 
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis'
            }}
            title={`${option?.code}${option?.desc ? ` - ${option?.desc}` : ""}`}
            >
              <strong>{option?.code}</strong>
              {option?.desc ? ` - ${option?.desc}` : ""}
            </span>
          )}
        </li>
      )}
      renderTags={(selected, getTagProps) => {
        if (singleSelect) return null;

        const selectedOptionsText = selected
          .map((option) =>
            typeof option === "string" ? option : formatOptionLabel(option)
          )
          .join("<br />");

        return selected.length > 1 ? (
          <>
            <Chip
              sx={{
                height: 25,
                fontSize: "0.85rem",
                ".MuiChip-label": { padding: "0 6px" },
              }}
              label={`${formatOptionLabel(selected[0])}`}
              {...getTagProps({ index: 0 })}
            />
            <Chip
              sx={{
                height: 25,
                fontSize: "0.85rem",
                ".MuiChip-label": { padding: "0 6px" },
              }}
              label={`+${selected.length - 1}`}
              onMouseEnter={(event) =>
                handlePopoverOpen(event, selectedOptionsText)
              }
              onMouseLeave={handlePopoverClose}
            />
            <Popover
              id={popoverId}
              open={isPopoverVisible}
              anchorEl={popoverAnchorEl}
              onClose={handlePopoverClose}
              anchorOrigin={{
                vertical: "bottom",
                horizontal: "center",
              }}
              transformOrigin={{
                vertical: "top",
                horizontal: "center",
              }}
              onMouseEnter={handleMouseEnterPopover}
              onMouseLeave={handleMouseLeavePopover}
              ref={popoverRef}
              sx={{
                "& .MuiPopover-paper": {
                  backgroundColor: colors.primary.whiteSmoke,
                  boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.1)",
                  borderRadius: "8px",
                  padding: "10px",
                  fontSize: "0.875rem",
                  color: colors.blue.main,
                  border: "1px solid #ddd",
                },
              }}
            >
              <Box
                sx={{
                  maxHeight: "270px",
                  overflowY: "auto",
                  padding: "5px",
                }}
                dangerouslySetInnerHTML={{ __html: popoverContent }}
              />
            </Popover>
          </>
        ) : (
          selected.map((option, index) => (
            <Chip
              sx={{
                height: 25,
                fontSize: "0.85rem",
                ".MuiChip-label": { padding: "0 6px" },
              }}
              label={`${formatOptionLabel(option)}`}
              {...getTagProps({ index })}
            />
          ))
        );
      }}
      renderInput={(params) => (
        <TextField
          {...params}
          label={
            mandatory ? (
              <>
                <strong>Select {param.key}</strong> <span style={{ color: colors?.error?.dark }}>*</span>
              </>
            ) : (
              `Select ${param.key}`
            )
          }
          variant="outlined"
          error={!!errors[param.key]}
          helperText={errors[param.key]}
          onChange={handleMatInputChange ? handleMatInputChange : undefined}
          ListboxProps={{
            onScroll: isMaterialNum ? handleScroll : undefined,
            ref: isMaterialNum ? dropdownRef : undefined,
          }}
          InputProps={{
            ...params.InputProps,
            endAdornment: (
              <>
                {isLoading ? (
                  <CircularProgress size={20} sx={{ mr: 1 }} />
                ) : null}
                {params.InputProps.endAdornment}
              </>
            ),
          }}
          sx={{
            "& .MuiOutlinedInput-root": {
              borderRadius: "8px",
              height: 50,
              boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
            },
            "& .MuiInputLabel-root": {
              fontWeight: 500,
            },
          }}
        />
      )}
    />
  );
};

export default FilterChangeDropdown;
