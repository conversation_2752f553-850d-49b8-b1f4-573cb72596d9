import React, { useEffect, useState } from "react";
import ReusableSnackBar from "../../Common/ReusableSnackBar";
import {
  BottomNavigation,
  Box,
  Button,
  CardContent,
  Grid,
  IconButton,
  Paper,
  Stack,
  Tab,
  Tabs,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  FormControl,
  TextField,
  Autocomplete,
  Stepper,
  Step,
  StepLabel,
  Card,
  Backdrop,
  CircularProgress,
} from "@mui/material";
import lookup from "../../../data/lookup.json";
import CloseIcon from "@mui/icons-material/Close";
import MarkunreadOutlinedIcon from "@mui/icons-material/MarkunreadOutlined";
import {
  DateField,
  DatePicker,
  DesktopDatePicker,
  DesktopDateTimePicker,
  LocalizationProvider,
} from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import {
  button_Outlined,
  iconButton_SpacingSmall,
  container_Padding,
  outermostContainer_Information,
  button_Primary,
} from "../../common/commonStyles";
import ReusableTable from "../../Common/ReusableTable";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import { checkIwaAccess, formValidator, idGenerator } from "../../../functions";
import { useDispatch, useSelector } from "react-redux";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import { useLocation, useNavigate } from "react-router-dom";
import {
  destination_DocumentManagement,
  destination_GeneralLedger,
} from "../../../destinationVariables";
import { doAjax } from "../../Common/fetchService";
// import EditableFieldForCostCenter from "./EditableFieldForCostCenter";
import { setDropDown } from "../../../app/dropDownDataSlice";
import moment from "moment/moment";
import ReusableDialog from "../../Common/ReusableDialog";
import ReusableAttachementAndComments from "../../Common/ReusableAttachmentAndComments/ReusableAttachementAndComments";
import {
  Timeline,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineItem,
  TimelineSeparator,
  timelineItemClasses,
} from "@mui/lab";
import TrackChangesTwoToneIcon from "@mui/icons-material/TrackChangesTwoTone";
import { CheckCircleOutlineOutlined } from "@mui/icons-material";
import { MatDownload, MatView } from "../../DocumentManagement/UtilDoc";
import EditableFieldForGL from "./EditableFieldForGL";
import {
  clearGeneralLedger,
  setGeneralLedgerViewData,
} from "../../../app/generalLedgerTabSlice";
import { setPayloadWhole } from "../../../app/editPayloadSlice";
import ChangeLog from "../../Changelog/ChangeLog";
import LoadingComponent from "../../Common/LoadingComponent";
const DisplayGeneralLedger = () => {
  const [isEditMode, setIsEditMode] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [isDisplayMode, setIsDisplayMode] = useState(true);
  const [responseFromAPI, setResponseFromAPI] = useState({});
  const [factorsArray, setFactorsArray] = useState([]);
  const [activeStep, setActiveStep] = useState(0);
  // const [compCode, setCompanyCode] = useState();
  const [costCenterDetails, setCostCenterDetails] = useState([]);
  const [iDs, setIds] = useState();
  const [dupliDialog, setDupliDialog] = useState(false);
  const [value, setValue] = useState([]);
  const [messageDialogTitle, setMessageDialogTitle] = useState(false);
  const [successMsg, setsuccessMsg] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [messageDialogSeverity, setMessageDialogSeverity] = useState(false);
  const [messageDialogOK, setMessageDialogOK] = useState(true);
  const [messageDialogExtra, setMessageDialogExtra] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [validateFlag, setValidateFlag] = useState(false);
  const [remarks, setRemarks] = useState("");
  const [attachments, setAttachments] = useState([]);
  const [openCorrectionDialog, setOpenCorrectionDialog] = useState(false);
  const [comments, setComments] = useState([]);
  const [openRemarkDialog, setOpenRemarkDialog] = useState(false);
  const [testrunStatus, setTestrunStatus] = useState(true);
  const [blurLoading, setBlurLoading] = useState(false);
  const [submitForReviewDisabled, setSubmitForReviewDisabled] = useState(true);
  const [handleExtrabutton, setHandleExtrabutton] = useState(false);
  const [isChangeLogopen, setisChangeLogopen] = useState(false);
  const [handleExtraText, setHandleExtraText] = useState("");
  const [apiCount, setApiCount] = useState(0);
  const [dialogType, setDialogType] = useState("");
  const [pageName, setPageName] = useState("");
  const [formValidationErrorItems, setFormValidationErrorItems] = useState([]);
  const [glNumber, setGlNumber] = useState("");
  const [openSnackbarValidation, setOpenSnackbarValidation] = useState(false);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const appSettings = useSelector((state) => state.appSettings);
  let iwaAccessData = useSelector(
    (state) => state.userManagement.entitiesAndActivities?.["General Ledger"]
  );
  let taskData = useSelector((state) => state?.initialData?.IWMMyTask);
  let userData = useSelector((state) => state.userManagement.userData);
  let generalLedgerRowData = location.state;
  let requiredFields = useSelector(
    (state) => state.generalLedger.requiredFields
  );
  let extendedCompanyCode = useSelector((state) => state.edit.selectedCheckBox);
  console.log("extended", extendedCompanyCode);

  let taskRowDetails = useSelector((state) => state.userManagement.taskData);
  const singleGLPayload = useSelector((state) => state.edit.payload);
  console.log("ccroewdata", singleGLPayload);
  const generalLedgerViewData = useSelector(
    (state) => state.generalLedger.generalLedgerViewData
  );
  console.log(generalLedgerViewData, "generalLedgerViewData");
  console.log("generalLedgerRowData", generalLedgerRowData);
  console.log("costCenterDetails", costCenterDetails);
  console.log(remarks, "Remarks");

  const handleChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  const handleSnackBarCloseValidation = () => {
    setOpenSnackbarValidation(false);
  };

  console.log("taskData", taskData);
  console.log("taskRowDetails", taskRowDetails);
  console.log("glids", iDs);

  const handleRemarksDialogClose = () => {
    setRemarks("");
    setTestrunStatus(true);
    setOpenRemarkDialog(false);
  };

  const fetchDynamicApiData = (keyName, endPoint) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: keyName, data: data.body }));
      // setIsLoading(false);
      setApiCount((prev) => prev + 1);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/${endPoint}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getAllLookups = () => {
    lookup?.generalLedger?.map((item) => {
      fetchDynamicApiData(item?.keyName, item?.endPoint);
    });
  };

  //companycode dependent
  const getTaxCategory = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "TaxCategory", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getTaxCategory?companyCode=${
        taskData?.body?.compCode
          ? taskData?.body?.compCode
          : generalLedgerRowData?.compCode
      }`,
      "get",
      hSuccess,
      hError
    );
  };

  const getHouseBank = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "HouseBank", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getHouseBank?companyCode=${
        taskData?.body?.compCode
          ? taskData?.body?.compCode
          : generalLedgerRowData?.compCode
      }`,
      "get",
      hSuccess,
      hError
    );
  };
  const getFieldStatusGroup = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "FieldStatusGroup", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getFieldStatusGroup?companyCode=${
        taskData?.body?.compCode
          ? taskData?.body?.compCode
          : generalLedgerRowData?.compCode
      }`,
      "get",
      hSuccess,
      hError
    );
  };
  //chartofaccount dependent lookups
  const getGroupAccountNumber = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "GroupAccountNumber", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getGroupAccountNumber?chartAccount=${
        taskData?.body?.coa
          ? taskData?.body?.coa
          : generalLedgerRowData?.chartOfAccount
      }`,
      "get",
      hSuccess,
      hError
    );
  };
  const getAlternativeAccountNumber = () => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "AlternativeAccountNumber", data: data.body })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getAlternativeAccountNumber?chartAccount=${
        taskData?.body?.coa
          ? taskData?.body?.coa
          : generalLedgerRowData?.chartOfAccount
      }`,
      "get",
      hSuccess,
      hError
    );
  };
  const getAccountGroup = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "AccountGroup", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getAccountGroupCodeDesc?chartAccount=${
        taskData?.body?.coa
          ? taskData?.body?.coa
          : generalLedgerRowData?.chartOfAccount
      }`,
      "get",
      hSuccess,
      hError
    );
  };

  useEffect(() => {
    setGlNumber(idGenerator("GL"));
  }, []);

  var payload = {
    GeneralLedgerID: iDs?.GeneralLedgeId ? iDs?.GeneralLedgeId : "",
    Action:
      generalLedgerRowData?.requestType === "Create"
        ? "I"
        : generalLedgerRowData?.requestType === "Change"
        ? "U"
        : taskRowDetails?.processDesc === "Create"
        ? "I"
        : taskRowDetails?.processDesc === "Change"
        ? "U"
        : generalLedgerRowData?.requestType === "Extend"
        ? "I"
        : taskRowDetails?.processDesc === "Extend"
        ? "I"
        : generalLedgerRowData?.requestType === "Extend"
        ? "I"
        : "U",
    RequestID: "",
    TaskStatus: "",
    TaskId: taskRowDetails?.taskId ? taskRowDetails?.taskId : "",
    Remarks: remarks ? remarks : "",
    Info: "",
    CreationId:
      taskRowDetails?.processDesc === "Create"
        ? taskRowDetails?.subject.slice(3)
        : generalLedgerRowData?.requestType === "Create"
        ? generalLedgerRowData?.requestId.slice(3)
        : "",
    EditId:
      taskRowDetails?.processDesc === "Change"
        ? taskRowDetails?.subject.slice(3)
        : generalLedgerRowData?.requestType === "Change"
        ? generalLedgerRowData?.requestId.slice(3)
        : "",
    ExtendId:
      taskRowDetails?.processDesc === "Extend"
        ? taskRowDetails?.subject.slice(3)
        : generalLedgerRowData?.requestType === "Extend" &&
          generalLedgerRowData.requestId
        ? generalLedgerRowData?.requestId.slice(3)
        : "",
    MassExtendId: "",
    DeleteId: "",
    MassCreationId: "",
    MassEditId: "",
    MassDeleteId: "",
    RequestType:
      generalLedgerRowData?.requestType === "Create"
        ? "Create"
        : generalLedgerRowData?.requestType === "Change"
        ? "Change"
        : generalLedgerRowData?.requestType === "Extend"
        ? "Extend"
        : taskRowDetails?.processDesc === "Create"
        ? "Create"
        : taskRowDetails?.processDesc === "Change"
        ? "Change"
        : taskRowDetails?.processDesc === "Extend"
        ? "Extend"
        : "Extend",
    ReqCreatedBy: userData?.user_id,
    ReqCreatedOn: taskRowDetails?.createdOn
      ? "/Date(" + taskRowDetails?.createdOn + ")/"
      : generalLedgerRowData?.createdOn
      ? "/Date(" + Date.parse(generalLedgerRowData?.createdOn) + ")/"
      : "",
    ReqUpdatedOn: "",
    RequestStatus: "",
    Testrun: testrunStatus === true,
    COA: generalLedgerRowData?.chartOfAccount
      ? generalLedgerRowData?.chartOfAccount
      : iDs?.ChartOfAccount
      ? iDs?.ChartOfAccount
      : "",
    CompanyCode: generalLedgerRowData?.compCode
      ? generalLedgerRowData?.compCode
      : iDs?.CompCode
      ? iDs?.CompCode
      : "",
    CoCodeToExtend: extendedCompanyCode[0]
      ? extendedCompanyCode.join(",")
      : iDs?.CompanyCodesExtendedTo
      ? iDs?.CompanyCodesExtendedTo
      : "",
    GLAccount: generalLedgerRowData?.glAccount
      ? generalLedgerRowData?.glAccount
      : iDs?.GLAccount
      ? iDs?.GLAccount
      : "",
    Accounttype: singleGLPayload?.AccountType
      ? singleGLPayload?.AccountType
      : "",
    AccountGroup: singleGLPayload?.AccountGroup
      ? singleGLPayload?.AccountGroup
      : "",
    GLname: singleGLPayload?.ShortText ? singleGLPayload?.ShortText : "",
    Description: singleGLPayload?.LongText ? singleGLPayload?.LongText : "",
    TradingPartner: singleGLPayload?.TradingPartner
      ? singleGLPayload?.TradingPartner
      : "",
    GroupAccNo: singleGLPayload?.GroupAccountNumber
      ? singleGLPayload?.GroupAccountNumber
      : "121100",
    AccountCurrency: singleGLPayload?.AccountCurrency
      ? singleGLPayload?.AccountCurrency
      : "",
    Exchangerate: singleGLPayload?.ExchangeRateDifferenceKey
      ? singleGLPayload?.ExchangeRateDifferenceKey
      : "",
    Balanceinlocrcy:
      singleGLPayload?.OnlyBalanceInLocalCurrency === true ? "X" : "",
    Taxcategory: singleGLPayload?.TaxCategory
      ? singleGLPayload?.TaxCategory
      : "",
    Pstnwotax: singleGLPayload?.PostingWithoutTaxAllowed === true ? "X" : "",
    ReconAcc: singleGLPayload?.ReconAccountForAccountType
      ? singleGLPayload?.ReconAccountForAccountType
      : "",
    Valuationgrp: singleGLPayload?.ValuationGroup
      ? singleGLPayload?.ValuationGroup
      : "",
    AlterAccno: singleGLPayload?.AlternativeAccountNumber
      ? singleGLPayload?.AlternativeAccountNumber
      : "",
    Openitmmanage: singleGLPayload?.OpenItemManagement === true ? "X" : "",
    Sortkey: singleGLPayload?.SortKey ? singleGLPayload?.SortKey : "",
    CostEleCategory: singleGLPayload?.CostElementCategory
      ? singleGLPayload?.CostElementCategory
      : "",
    FieldStsGrp: singleGLPayload?.FieldStatusGroup
      ? singleGLPayload?.FieldStatusGroup
      : "",
    PostAuto: singleGLPayload?.PostAutomaticallyOnly === true ? "X" : "",
    Supplementautopost:
      singleGLPayload?.SupplementAutoPostings === true ? "X" : "",
    Planninglevel: singleGLPayload?.PlanningLevel
      ? singleGLPayload?.PlanningLevel
      : "",
    Relvnttocashflow: singleGLPayload?.RelevantToCashFlows === true ? "X" : "",
    HouseBank: singleGLPayload?.HouseBank ? singleGLPayload?.HouseBank : "",
    AccountId: singleGLPayload?.AccountID ? singleGLPayload?.AccountID : "",
    Interestindicator: singleGLPayload?.InterestIndicator
      ? singleGLPayload?.InterestIndicator
      : "",
    ICfrequency: singleGLPayload?.InterestCalculationFrequency
      ? singleGLPayload?.InterestCalculationFrequency
      : "",
    KeydateofLIC: singleGLPayload?.KeyDateOfLastIntCalc
      ? singleGLPayload?.KeyDateOfLastIntCalc
      : "",
    LastIntrstundate: singleGLPayload?.DateOfLastInterestRun
      ? singleGLPayload?.DateOfLastInterestRun
      : "",
    AccmngExistsys: "",
    Infationkey: "",
    Tolerancegrp: "",
    AuthGroup: "",
    AccountClerk: "",
    ReconAccReady: "",
    PostingBlocked: "",
    PlanningBlocked: "",
  };

  const handleSetEditedPayload = () => {
    let activeTabName = factorsArray[activeStep];
    console.log("activeTabName", activeTabName, factorsArray);
    let viewDataArray = Object.entries(generalLedgerViewData);

    // viewDataArray.push(costCenterViewData);

    console.log("viewDataArray", viewDataArray);
    const toSetArray = {};
    viewDataArray.map((item) => {
      console.log("bottle", item[1]);
      let temp = Object.entries(item[1]); //Basic Data\
      console.log("notebook", temp);
      temp.forEach((fieldGroup) => {
        fieldGroup[1].forEach((field) => {
          if (field?.fieldType === "Calendar") {
            toSetArray[
              field.fieldName
                .replaceAll("(", "")
                .replaceAll(")", "")
                .replaceAll("/", "")
                .replaceAll("-", "")
                .replaceAll(".", "")
                .split(" ")
                .join("")
            ] = parseInt(field.value.replace("/Date(", "").replace(")/", ""));
          } else {
            toSetArray[
              field.fieldName
                .replaceAll("(", "")
                .replaceAll(")", "")
                .replaceAll("/", "")
                .replaceAll("-", "")
                .replaceAll(".", "")
                .split(" ")
                .join("")
            ] = field.value;
          }
        });
      });
      return item;
    });
    console.log("toSetArray", toSetArray);
    dispatch(setPayloadWhole(toSetArray));
  };

  const getCostElementCategory = (value) => {
    console.log("compcode", value);
    const hSuccess = (data) => {
      console.log("value", data);
      dispatch(
        setDropDown({ keyName: "CostElementCategory", data: data.body })
      );
    };
    const hError = (error) => {
      console.log(error, "error in dojax");
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getCostElementCategory?accountType=${value}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getGeneralLedgerDisplayData = () => {
    var payload = taskData?.body?.id
      ? {
          id: taskData?.body?.id ? taskData?.body?.id : "",
          glAccount: taskData?.body?.glAccount ? taskData?.body?.glAccount : "",
          compCode: taskData?.body?.compCode,
          reqStatus: taskData?.body?.reqStatus,
          screenName:
            taskRowDetails?.processDesc === "Create" ? "Create" : "Change",
        }
      : {
          id: generalLedgerRowData?.reqStatus ? generalLedgerRowData?.id : "",
          glAccount: generalLedgerRowData?.glAccount
            ? generalLedgerRowData?.glAccount
            : "",
          compCode: generalLedgerRowData?.compCode
            ? generalLedgerRowData?.compCode
            : "",
          reqStatus: generalLedgerRowData?.reqStatus
            ? generalLedgerRowData?.reqStatus
            : "Approved",
          screenName:
            generalLedgerRowData?.requestType === "Extend"
              ? "Create"
              : generalLedgerRowData?.requestType === "Create"
              ? "Create"
              : generalLedgerRowData?.requestType === "Change"
              ? "Change"
              : "Change",
        };
    const hSuccess = (data) => {
      const responseBody = data.body.viewData;
      const responseIDs = data.body;
      dispatch(setGeneralLedgerViewData(responseBody));
      const categoryKeys = Object.keys(responseBody);
      setFactorsArray(categoryKeys);
      const attachment = ["Attachment & Documents"];
      factorsArray.concat(attachment);
      // factorsArray.push('Attachment & Comments')
      const mappedData = categoryKeys.map((category) => ({
        category,
        data: responseBody[category],
        setIsEditMode,
      }));
      getCostElementCategory(
        responseBody["Type/Description"]["Control in COA"].find(
          (field) => field?.fieldName === "Account Type"
        ).value
      );

      setCostCenterDetails(mappedData);
      setIds(responseIDs);
      console.log("mappedData", mappedData, iDs);
      // dispatch(setSingleCostCenterPayload(data));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/displayGeneralLedger`,
      "post",
      hSuccess,
      hError,
      payload
    );
    setPageName(payload.screenName);
  };

  console.log("ID", iDs);
  useEffect(() => {
    getGeneralLedgerDisplayData();
    getTaxCategory();
    getFieldStatusGroup();
    getHouseBank();
    getAccountGroup();
    getAlternativeAccountNumber();
    getGroupAccountNumber();
    getAllLookups();
  }, []);

  useEffect(() => {
    if (generalLedgerViewData.length === 0) {
      return;
    }
    handleSetEditedPayload();
  }, [generalLedgerViewData]);

  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };
  const handleMessageDialogNavigate = () => {
    navigate("/masterDataCockpit/generalLedger");
  };
  const handleSnackBarClose = () => {
    if (validateFlag) {
      setopenSnackbar(false);
      setValidateFlag(false);
    } else {
      setopenSnackbar(false);
      navigate("/masterDataCockpit/generalLedger");
    }
  };
  const handleSnackBarOpenValidation = () => {
    setOpenSnackbarValidation(true);
  };
  const handleCheckValidationError = () => {
    return formValidator(
      singleGLPayload,
      requiredFields,
      setFormValidationErrorItems
    );
  };

  const handleBack = () => {
    setSubmitForReviewDisabled(true);
    const isValidation = handleCheckValidationError();
    if (isEditMode) {
      if (isValidation) {
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
        dispatch(clearGeneralLedger());
      } else {
        handleSnackBarOpenValidation();
      }
    } else {
      setActiveStep((prevActiveStep) => prevActiveStep - 1);
      dispatch(clearGeneralLedger());
    }
  };
  const handleNext = () => {
    const isValidation = handleCheckValidationError();
    if (isEditMode) {
      if (isValidation) {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
        dispatch(clearGeneralLedger());
      } else {
        handleSnackBarOpenValidation();
      }
    } else {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
      dispatch(clearGeneralLedger());
    }
  };

  const onEdit = () => {
    setIsEditMode(true);
    setIsDisplayMode(false);
  };
  const handleClosemodalData = (data) => {
    setisChangeLogopen(data);
  };

  const onGeneralLedgerSubmitChange = () => {
    setIsLoading(true);
    handleRemarksDialogClose();
    handleGeneralLedgerSubmitChange();
  };
  const onGeneralLedgerSubmitExtend = () => {
    setIsLoading(true);
    handleRemarksDialogClose();
    handleGeneralLedgerSubmitExtend();
  };
  const onGeneralLedgerSubmitCreate = () => {
    setIsLoading(true);
    handleRemarksDialogClose();
    handleGeneralLedgerSubmitCreate();
  };

  const onGeneralLedgerReviewChange = () => {
    setIsLoading(true);
    handleRemarksDialogClose();
    handleGeneralLedgerReviewChange();
  };
  const onGeneralLedgerReviewExtend = () => {
    setIsLoading(true);
    handleRemarksDialogClose();
    handleGeneralLedgerReviewExtend();
  };
  const onGeneralLedgerReviewCreate = () => {
    setIsLoading(true);
    handleRemarksDialogClose();
    handleGeneralLedgerReviewCreate();
  };

  const onCostCenterCorrectionChange = () => {
    handleCostCenterCorrectionChange();
  };
  const onCostCenterCorrectionCreate = () => {
    handleCostCenterCorrectionCreate();
  };

  const onGeneralLedgerApproveChange = () => {
    setIsLoading(true);
    handleRemarksDialogClose();
    handleGeneralLedgerApproveChange();
  };
  const onGeneralLedgerApproveExtend = () => {
    setIsLoading(true);
    handleRemarksDialogClose();
    handleGeneralLedgerApproveExtend();
  };
  const onGeneralLedgerApproveCreate = () => {
    setIsLoading(true);
    handleRemarksDialogClose();
    handleGeneralLedgerApproveCreate();
  };
  const onCostCenterRereview = () => {
    handleCostCenterRereview();
  };
  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };
  const handleSnackBarOpen = () => {
    setopenSnackbar(true);
  };
  const openChangeLog = () => {
    setisChangeLogopen(true);
  };
  const onValidateGeneralLedgerApprover = () => {
    setBlurLoading(true);

    const hSuccess = (data) => {
      // setIsLoading();
      if (data.statusCode === 201) {
        setMessageDialogTitle("Create");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `All Data has been Validated. General Ledger can be Send for Review`
        );
        setSubmitForReviewDisabled(false);
        setHandleExtrabutton(false);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setValidateFlag(true);
        setSubmitForReviewDisabled(false);
        setBlurLoading(false);
      } else {
        setBlurLoading(false);
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          `${
            data?.body?.message[0] ? data?.body?.message[0] : data?.body?.value
          }`
        );
        setHandleExtrabutton(false);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setSubmitForReviewDisabled(true);
      }
    };
    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger}/alter/validateSingleGeneralLedger`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const onValidateGeneralLedger = () => {
    setBlurLoading(true);
    var duplicateCheck = {
      glName: singleGLPayload?.ShortText
        ? singleGLPayload?.ShortText?.toUpperCase()
        : "",
      compCode: generalLedgerRowData?.compCode
        ? generalLedgerRowData?.compCode
        : iDs?.CompCode
        ? iDs?.CompCode
        : "",
    };
    let glNameFromGlViewData = "";
    generalLedgerViewData?.["Type/Description"]?.["Description"].map(
      (description_element) => {
        if (description_element?.fieldName === "Short Text") {
          glNameFromGlViewData = description_element?.value;
        }
      }
    );
    console.log(
      duplicateCheck?.glName?.toUpperCase(),
      glNameFromGlViewData?.toUpperCase(),
      "checkingElement"
    );
    const hSuccess = (data) => {
      if (data.statusCode === 201) {
        setMessageDialogTitle("Create");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `All Data has been Validated. General Ledger can be Send for Review`
        );
        setSubmitForReviewDisabled(false);
        setHandleExtrabutton(false);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setValidateFlag(true);
        if (duplicateCheck.compCode !== "" || duplicateCheck.glName !== "") {
          setSubmitForReviewDisabled(false);
          if (
            duplicateCheck?.glName?.toUpperCase() ===
              glNameFromGlViewData?.toUpperCase() &&
            pageName === "Change"
          ) {
            // new added for skip duplicate check if not any changes
            setBlurLoading(false);
          } else {
            doAjax(
              `/${destination_GeneralLedger}/alter/fetchGlNameNCompCodeDupliChk`,
              "post",
              hDuplicateCheckSuccess,
              hDuplicateCheckError,
              duplicateCheck
            );
          }
        }
        setBlurLoading(false);
      } else {
        setBlurLoading(false);
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          `${
            data?.body?.message[0] ? data?.body?.message[0] : data?.body?.value
          }`
        );
        setHandleExtrabutton(false);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setSubmitForReviewDisabled(true);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    const hDuplicateCheckSuccess = (data) => {
      if (
        data.body.length === 0 ||
        !data.body.some((item) => item.toUpperCase() === duplicateCheck.glName)
      ) {
        setBlurLoading(false);
        setSubmitForReviewDisabled(false);
        setTestrunStatus(false);
      } else {
        setBlurLoading(false);
        setMessageDialogTitle("Duplicate Check");
        setsuccessMsg(false);
        setMessageDialogMessage(
          `There is a direct match for the Short Text. Please change the Short Text.`
        );
        setHandleExtrabutton(false);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setSubmitForReviewDisabled(true);
      }
    };
    const hDuplicateCheckError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger}/alter/validateSingleGeneralLedger`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const onGeneralLedgerCorrection = () => {
    if (
      userData?.role === "MDM Steward" &&
      (generalLedgerRowData?.requestType === "Create" ||
        taskRowDetails?.processDesc === "Create")
    ) {
      setIsLoading(true);
      handleCorrectionMDMCreate();
    } else if (
      userData?.role === "MDM Steward" &&
      (generalLedgerRowData?.requestType === "Change" ||
        taskRowDetails?.processDesc === "Change")
    ) {
      setIsLoading(true);
      handleCorrectionMDMChange();
    } else if (
      userData?.role === "MDM Steward" &&
      (generalLedgerRowData?.requestType === "Extend" ||
        taskRowDetails?.processDesc === "Extend")
    ) {
      setIsLoading(true);
      handleCorrectionMDMExtend();
    } else if (
      userData?.role === "Approver" &&
      (generalLedgerRowData?.requestType === "Create" ||
        taskRowDetails?.processDesc === "Create")
    ) {
      setIsLoading(true);
      handleCorrectionApproverCreate();
    } else if (
      userData?.role === "Approver" &&
      (generalLedgerRowData?.requestType === "Change" ||
        taskRowDetails?.processDesc === "Change")
    ) {
      setIsLoading(true);
      handleCorrectionApproverChange();
    } else if (
      userData?.role === "Approver" &&
      (generalLedgerRowData?.requestType === "Extend" ||
        taskRowDetails?.processDesc === "Extend")
    ) {
      setIsLoading(true);
      handleCorrectionApproverExtend();
    }
  };

  const onGeneralLedgerSubmitRemarks = () => {
    if (
      userData?.role === "Finance" &&
      (generalLedgerRowData?.requestType === "Create" ||
        taskRowDetails?.processDesc === "Create") &&
      isEditMode
    ) {
      onGeneralLedgerReviewCreate();
    } else if (
      userData?.role === "MDM Steward" &&
      (generalLedgerRowData?.requestType === "Create" ||
        taskRowDetails?.processDesc === "Create") &&
      !isEditMode
    ) {
      onGeneralLedgerSubmitCreate();
    } else if (
      userData?.role === "Approver" &&
      (generalLedgerRowData?.requestType === "Create" ||
        taskRowDetails?.processDesc === "Create") &&
      !isEditMode
    ) {
      onGeneralLedgerApproveCreate();
    } else if (
      userData?.role === "Finance" &&
      !generalLedgerRowData?.requestType && //for change from master table
      isEditMode
    ) {
      onGeneralLedgerReviewChange();
    } else if (
      userData?.role === "Finance" &&
      (generalLedgerRowData?.requestType === "Change" ||
        taskRowDetails?.processDesc === "Change") &&
      isEditMode
    ) {
      onGeneralLedgerReviewChange();
    } else if (
      userData?.role === "MDM Steward" &&
      (generalLedgerRowData?.requestType === "Change" ||
        taskRowDetails?.processDesc === "Change") &&
      !isEditMode
    ) {
      onGeneralLedgerSubmitChange();
    } else if (
      userData?.role === "Approver" &&
      (generalLedgerRowData?.requestType === "Change" ||
        taskRowDetails?.processDesc === "Change") &&
      !isEditMode
    ) {
      onGeneralLedgerApproveChange();
    } else if (
      userData?.role === "Finance" &&
      (generalLedgerRowData?.requestType === "Extend" ||
        taskRowDetails?.processDesc === "Extend") &&
      isEditMode
    ) {
      onGeneralLedgerReviewExtend();
    } else if (
      userData?.role === "MDM Steward" &&
      (generalLedgerRowData?.requestType === "Extend" ||
        taskRowDetails?.processDesc === "Extend") &&
      !isEditMode
    ) {
      onGeneralLedgerSubmitExtend();
    } else if (
      userData?.role === "Approver" &&
      (generalLedgerRowData?.requestType === "Extend" ||
        taskRowDetails?.processDesc === "Extend") &&
      !isEditMode
    ) {
      onGeneralLedgerApproveExtend();
    }
  };
  const handleCorrectionMDMCreate = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `General Ledger Submitted for Correction with ID NLS${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting General Ledger for Correction"
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    console.log("remarkssssssssss", remarks);
    doAjax(
      `/${destination_GeneralLedger}/alter/generalLedgerSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCorrectionMDMChange = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `General Ledger Submitted for Correction with ID CLS${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting General Ledger for Correction"
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    console.log("hsdfjgdh", payload);
    doAjax(
      `/${destination_GeneralLedger}/alter/changeGeneralLedgerSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCorrectionMDMExtend = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `General Ledger Submitted for Correction with ID ELS${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting General Ledger for Correction"
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    console.log("hsdfjgdh", payload);
    doAjax(
      `/${destination_GeneralLedger}/alter/extendGeneralLedgerSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCorrectionApproverCreate = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `General Ledger Submitted for Correction with ID NLS${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting General Ledger for Correction"
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/alter/generalLedgerSendForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCorrectionApproverChange = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `General Ledger Submitted for Correction with ID CLS${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting General Ledger for Correction"
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    console.log("remarksssaaaa", remarks);
    doAjax(
      `/${destination_GeneralLedger}/alter/changeGeneralLedgerSendForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCorrectionApproverExtend = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `General Ledger Submitted for Correction with ID ELS${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting General Ledger for Correction"
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger}/alter/extendGeneralLedgerSendForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const attachmentColumns = [
    {
      field: "id",
      headerName: "Document ID",
      flex: 1,
      hide: true,
    },
    {
      field: "docType",
      headerName: "Type",
      flex: 1,
    },
    {
      field: "docName",
      headerName: "Document Name",
      flex: 1,
    },
    {
      field: "uploadedOn",
      headerName: "Uploaded On",
      flex: 1,
      align: "center",
      headerAlign: "center",
    },
    {
      field: "uploadedBy",
      headerName: "Uploaded By",
      sortable: false,
      flex: 1,
    },
    {
      field: "action",
      headerName: "Action",
      sortable: false,
      filterable: false,
      align: "center",
      headerAlign: "center",
      flex: 1,
      renderCell: (cellValues) => {
        return (
          <>
            <MatView index={cellValues.row.id} name={cellValues.row.docName} />
            <MatDownload
              index={cellValues.row.id}
              name={cellValues.row.docName}
            />
          </>
        );
      },
    },
  ];
  const getAttachments = () => {
    let requestId = taskRowDetails?.subject
      ? taskRowDetails?.subject
      : generalLedgerRowData?.requestId;
    let hSuccess = (data) => {
      var attachmentRows = [];
      data.documentDetailDtoList.forEach((doc) => {
        var tempRow = {
          id: doc.documentId,
          docType: doc.fileType,
          docName: doc.fileName,
          uploadedOn: moment(doc.docCreationDate).format(appSettings.date),
          uploadedBy: doc.createdBy,
        };
        if (true) attachmentRows.push(tempRow);
      });
      setAttachments(attachmentRows);
    };
    // invoiceHeaderData?.extInvNum &&
    doAjax(
      `/${destination_DocumentManagement}/documentManagement/getDocByRequestId/${requestId}`,
      "get",
      hSuccess
    );
  };
  const getComments = () => {
    let requestId = taskRowDetails?.subject
      ? taskRowDetails?.subject
      : generalLedgerRowData?.requestId;
    let hSuccess = (data) => {
      console.log("commentsdata", data);
      var commentRows = [];
      data.body.forEach((cmt) => {
        var tempRow = {
          id: cmt.requestId,
          comment: cmt.comment,
          user: cmt.createdByUser,
          createdAt: cmt.updatedAt,
        };
        commentRows.push(tempRow);
      });
      setComments(commentRows);
    };
    let hError = (error) => {
      console.log(error);
    };
    // invoiceHeaderData?.extInvNum &&
    doAjax(
      `/${destination_GeneralLedger}/activitylog/fetchTaskDetailsForRequestId?requestId=${requestId}`,
      "get",
      hSuccess,
      hError
    );
  };
  const onGeneralLedgerSaveAsDraftCreate = () => {
    handleGeneralLedgerSaveAsDraftCreate();
  };

  const handleGeneralLedgerSaveAsDraftCreate = () => {
    //console.log(messageDialogSeverity,"setMessageDialogSeverity")
    setMessageDialogSeverity(false);
    handleMessageDialogClickOpen();
    setMessageDialogTitle("Confirm");
    setMessageDialogMessage(`Do You Want to Save as Draft ?`);
    setHandleExtrabutton(true);
    setHandleExtraText("proceed");
    setDialogType("Create");
  };
  const onGeneralLedgerSaveAsDraftChange = () => {
    handleGeneralLedgerSaveAsDraftChange();
  };
  const handleGeneralLedgerSaveAsDraftChange = () => {
    setMessageDialogSeverity(false);
    handleMessageDialogClickOpen();
    setMessageDialogTitle("Confirm");
    setMessageDialogMessage(`Do You Want to Save as Draft?`);
    setHandleExtrabutton(true);
    setHandleExtraText("proceed");
    setDialogType("Change");
  };
  const onGeneralLedgerSaveAsDraftExtend = () => {
    handleGeneralLedgerSaveAsDraftExtend();
  };
  const handleGeneralLedgerSaveAsDraftExtend = () => {
    setMessageDialogSeverity(false);
    handleMessageDialogClickOpen();
    setMessageDialogTitle("Confirm");
    setMessageDialogMessage(`Do You Want to Save as Draft?`);
    setHandleExtrabutton(true);
    setHandleExtraText("proceed");
    setDialogType("Extend");
  };

  const handleProceedbutton = () => {
    console.log(dialogType, "dialogType");
    handleWarningDialogClose();
    setIsLoading(true);
    if (dialogType === "Change") {
      const hSuccess = (data) => {
        setIsLoading(false);
        if (data.statusCode === 200) {
          console.log("success");
          setMessageDialogTitle("Create");
          setMessageDialogMessage(
            `General Ledger Saved As Draft with ID CLS${data.body} `
          );
          setHandleExtrabutton(false);
          setMessageDialogSeverity("success");
          setMessageDialogOK(false);
          setsuccessMsg(true);
          handleSnackBarOpen();
          setMessageDialogExtra(true);
          const secondApiPayload = {
            artifactId: glNumber,
            createdBy: userData?.emailId,
            artifactType: "GeneralLedger",
            requestId: `CLS${data?.body}`,
          };
          const secondApiSuccess = (secondApiData) => {
            console.log("Second API success", secondApiData);
            // Handle success for the second API if needed
          };

          const secondApiError = (secondApiError) => {
            console.error("Second API error", secondApiError);
            // Handle error for the second API if needed
          };
          // {requestId&&
          doAjax(
            `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
            "post",
            secondApiSuccess,
            secondApiError,
            secondApiPayload
          );
        } else {
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          setMessageDialogMessage("Failed Saving General Ledger");
          setHandleExtrabutton(false);
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          // setIsLoading(false);
        }
        handleClose();
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
        `/${destination_GeneralLedger}/alter/changeGeneralLedgerAsDraft`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      const hSuccess = (data) => {
        handleWarningDialogClose();
        setIsLoading(false);
        if (data.statusCode === 200) {
          console.log("success");
          setMessageDialogTitle("Create");
          setMessageDialogMessage(
            `Cost Center Saved As Draft with ID NLS${data.body} `
          );
          setHandleExtrabutton(false);
          setMessageDialogSeverity("success");
          setMessageDialogOK(false);
          setsuccessMsg(true);
          handleSnackBarOpen();
          setMessageDialogExtra(true);
          const secondApiPayload = {
            artifactId: glNumber,
            createdBy: userData?.emailId,
            artifactType: "GeneralLedger",
            requestId: `NCS${data?.body}`,
          };
          const secondApiSuccess = (secondApiData) => {
            console.log("Second API success", secondApiData);
            // Handle success for the second API if needed
          };

          const secondApiError = (secondApiError) => {
            console.error("Second API error", secondApiError);
            // Handle error for the second API if needed
          };
          // {requestId&&
          doAjax(
            `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
            "post",
            secondApiSuccess,
            secondApiError,
            secondApiPayload
          );
        } else {
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          setMessageDialogMessage("Failed Saving General Ledger");
          setHandleExtrabutton(false);
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          // setIsLoading(false);
        }
        handleClose();
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
        `/${destination_GeneralLedger}/alter/generalLedgerAsDraft`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };

  const handleOpenRemarkDialog = () => {
    setTestrunStatus(false);
    setOpenRemarkDialog(true);
  };

  const loaderCount = () => {
    if (apiCount == lookup?.generalLedger?.length) {
      setIsLoading(false);
    } else {
      setIsLoading(true);
    }
  };

  useEffect(() => {
    getAttachments();
    getComments();
  }, []);

  useEffect(() => {
    loaderCount();
  }, [apiCount]);

  const tabContents = factorsArray
    .map((item) => {
      const mdata = costCenterDetails.filter(
        (ii) => ii.category?.split(" ")[0] == item?.split(" ")[0]
      );
      if (mdata.length != 0) {
        return { category: item?.split(" ")[0], data: mdata[0].data };
      }
    })
    .map((categoryData, index) => {
      console.log("categoryy", categoryData.category);
      if (categoryData?.category == "Type/Description" && activeStep == 0) {
        return [
          <Grid
            key={categoryData.category}
            container
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              // borderRadius: "8px",
              // border: "1px solid #E0E0E0",
              mt: 1,
              // boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
              // padding: "10px",
              mb: 1,
            }}
          >
            {Object.keys(categoryData.data).map((fieldGroup) => (
              <Grid
                key={fieldGroup}
                item
                md={12}
                sx={{
                  backgroundColor: "white",
                  maxHeight: "max-content",
                  height: "max-content",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                  mt: 0.25,
                  boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                  ...container_Padding,
                  // ...container_columnGap,
                }}
              >
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                    margin: "0px !important",
                  }}
                >
                  {fieldGroup}
                </Typography>
                <Box sx={{ width: "100%" }}>
                  <CardContent
                    sx={{
                      padding: "0",
                      paddingBottom: "0 !important",
                      paddingTop: "10px !important",
                    }}
                  >
                    <Grid
                      container
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(6,1fr)",
                        gap: "15px",
                      }}
                      justifyContent="space-between"
                      alignItems="flex-start"
                      md={12}
                    >
                      {categoryData.data[fieldGroup].map((field) => {
                        console.log("fieldDatatttt", field);
                        return (
                          <EditableFieldForGL
                            // key={field.fieldName}
                            length={field.maxLength}
                            label={field.fieldName}
                            value={field.value}
                            visibility={field.visibility}
                            onSave={(newValue) =>
                              handleFieldSave(field.fieldName, newValue)
                            }
                            data={singleGLPayload}
                            isEditMode={isEditMode}
                            // isExtendMode={isExtendMode}
                            type={field.fieldType}
                            field={field} // Update the type as needed
                            taskRequestId={generalLedgerRowData?.requestId}
                            generalLedgerRowData={generalLedgerRowData}
                          />
                        );
                      })}
                    </Grid>
                  </CardContent>
                </Box>
              </Grid>
            ))}
          </Grid>,
        ];
      } else if (categoryData?.category == "Control" && activeStep == 1) {
        return [
          <Grid
            key={categoryData.category}
            container
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              mt: 1,
              mb: 1,
            }}
          >
            {Object.keys(categoryData.data).map((fieldGroup) => (
              <Grid
                key={fieldGroup}
                item
                md={12}
                sx={{
                  backgroundColor: "white",
                  maxHeight: "max-content",
                  height: "max-content",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                  mt: 0.25,
                  boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                  ...container_Padding,
                  // ...container_columnGap,
                }}
              >
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                    margin: "0px !important",
                  }}
                >
                  {fieldGroup}
                </Typography>
                <Box sx={{ width: "100%" }}>
                  <CardContent>
                    <Grid
                      container
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(6,1fr)",
                        gap: "15px",
                      }}
                      justifyContent="space-between"
                      alignItems="flex-start"
                      md={12}
                    >
                      {categoryData.data[fieldGroup].map((field) => (
                        <EditableFieldForGL
                          key={field.fieldName}
                          label={field.fieldName}
                          value={field?.value === "X" ? true : false}
                          onSave={(newValue) =>
                            handleFieldSave(field.fieldName, newValue)
                          }
                          isEditMode={isEditMode}
                          type={field.fieldType} // Update the type as needed
                          taskRequestId={generalLedgerRowData?.requestId}
                          visibility={field.visibility}
                          data={singleGLPayload}
                          generalLedgerRowData={generalLedgerRowData}
                        />
                      ))}
                    </Grid>
                  </CardContent>
                </Box>
              </Grid>
            ))}
          </Grid>,
        ];
      } else if (
        categoryData?.category == "Create/Bank/Interest" &&
        activeStep == 2
      ) {
        return [
          <Grid
            key={categoryData.category}
            container
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              // borderRadius: "8px",
              // border: "1px solid #E0E0E0",
              mt: 1,
              // boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
              // padding: "10px",
              mb: 1,
            }}
          >
            {Object.keys(categoryData.data).map((fieldGroup) => (
              <Grid
                key={fieldGroup}
                item
                md={12}
                sx={{
                  backgroundColor: "white",
                  maxHeight: "max-content",
                  height: "max-content",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                  mt: 0.25,
                  boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                  ...container_Padding,
                  // ...container_columnGap,
                }}
              >
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                    margin: "0px !important",
                  }}
                >
                  {fieldGroup}
                </Typography>
                <Box sx={{ width: "100%" }}>
                  <CardContent>
                    <Grid
                      container
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(6,1fr)",
                        gap: "15px",
                      }}
                      justifyContent="space-between"
                      alignItems="flex-start"
                      md={12}
                    >
                      {categoryData.data[fieldGroup].map((field) => (
                        <EditableFieldForGL
                          key={field.fieldName}
                          label={field.fieldName}
                          value={field.value}
                          onSave={(newValue) =>
                            handleFieldSave(field.fieldName, newValue)
                          }
                          isEditMode={isEditMode}
                          // isExtendMode={isExtendMode}
                          type={field.fieldType} // Update the type as needed
                          taskRequestId={generalLedgerRowData?.requestId}
                          data={singleGLPayload}
                          generalLedgerRowData={generalLedgerRowData}
                        />
                      ))}
                    </Grid>
                  </CardContent>
                </Box>
              </Grid>
            ))}
          </Grid>,
        ];
      } else if (
        categoryData?.category == "Keyword/Translation" &&
        activeStep == 3
      ) {
        return [
          <Grid
            key={categoryData.category}
            container
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              // borderRadius: "8px",
              // border: "1px solid #E0E0E0",
              mt: 1,
              // boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
              // padding: "10px",
              mb: 1,
            }}
          >
            {Object.keys(categoryData.data).map((fieldGroup) => (
              <Grid
                key={fieldGroup}
                item
                md={12}
                sx={{
                  backgroundColor: "white",
                  maxHeight: "max-content",
                  height: "max-content",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                  mt: 0.25,
                  boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                  ...container_Padding,
                  // ...container_columnGap,
                }}
              >
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                    margin: "0px !important",
                  }}
                >
                  {fieldGroup}
                </Typography>
                <Box sx={{ width: "100%" }}>
                  <CardContent>
                    <Grid
                      container
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(6,1fr)",
                        gap: "15px",
                      }}
                      justifyContent="space-between"
                      alignItems="flex-start"
                      md={12}
                    >
                      {categoryData.data[fieldGroup].map((field) => (
                        <EditableFieldForGL
                          key={field.fieldName}
                          label={field.fieldName}
                          value={field.value}
                          onSave={(newValue) =>
                            handleFieldSave(field.fieldName, newValue)
                          }
                          isEditMode={isEditMode}
                          // isExtendMode={isExtendMode}
                          type={field.fieldType} // Update the type as needed
                          taskRequestId={generalLedgerRowData?.requestId}
                          visibility={field.visibility}
                          data={singleGLPayload}
                          generalLedgerRowData={generalLedgerRowData}
                        />
                      ))}
                    </Grid>
                  </CardContent>
                </Box>
              </Grid>
            ))}
          </Grid>,
        ];
      } else if (categoryData?.category == "Information" && activeStep == 4) {
        return [
          <Grid
            key={categoryData.category}
            container
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              // borderRadius: "8px",
              // border: "1px solid #E0E0E0",
              mt: 1,
              // boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
              // padding: "10px",
              mb: 1,
            }}
          >
            {Object.keys(categoryData.data).map((fieldGroup) => (
              <Grid
                key={fieldGroup}
                item
                md={12}
                sx={{
                  backgroundColor: "white",
                  maxHeight: "max-content",
                  height: "max-content",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                  mt: 0.25,
                  boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                  ...container_Padding,
                  // ...container_columnGap,
                }}
              >
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                    margin: "0px !important",
                  }}
                >
                  {fieldGroup}
                </Typography>
                <Box sx={{ width: "100%" }}>
                  <CardContent>
                    <Grid
                      container
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(6,1fr)",
                        gap: "15px",
                      }}
                      justifyContent="space-between"
                      alignItems="flex-start"
                      md={12}
                    >
                      {categoryData.data[fieldGroup].map((field) => (
                        <EditableFieldForGL
                          key={field.fieldName}
                          label={field.fieldName}
                          value={field.value}
                          onSave={(newValue) =>
                            handleFieldSave(field.fieldName, newValue)
                          }
                          isEditMode={isEditMode}
                          // isExtendMode={isExtendMode}
                          type={field.fieldType} // Update the type as needed
                          taskRequestId={generalLedgerRowData?.requestId}
                          visibility={field.visibility}
                          data={singleGLPayload}
                          generalLedgerRowData={generalLedgerRowData}
                        />
                      ))}
                    </Grid>
                  </CardContent>
                </Box>
              </Grid>
            ))}
          </Grid>,
        ];
      } else if (categoryData?.category == "Attachments") {
        return [
          <>
            {!isEditMode ? (
              <Card sx={{ padding: "1rem 1rem 0rem 1rem" }}>
                <Grid
                  container
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                  }}
                >
                  <Typography variant="h6">
                    <strong>Attachments</strong>
                  </Typography>
                </Grid>
                {Boolean(attachments.length) && (
                  <ReusableTable
                    width="100%"
                    rows={attachments}
                    columns={attachmentColumns}
                    hideFooter={false}
                    getRowIdValue={"id"}
                    disableSelectionOnClick={true}
                    stopPropagation_Column={"action"}
                  />
                )}
                {!Boolean(attachments.length) && (
                  <Typography variant="body2">No Attachments Found</Typography>
                )}
                <br />
                <Typography variant="h6">Comments</Typography>
                {Boolean(comments.length) && (
                  <Timeline
                    sx={{
                      [`& .${timelineItemClasses.root}:before`]: {
                        flex: 0,
                        padding: 0,
                      },
                    }}
                  >
                    {comments.map((comment) => (
                      <TimelineItem>
                        <TimelineSeparator>
                          <TimelineDot>
                            <CheckCircleOutlineOutlined
                              sx={{ color: "#757575" }}
                            />
                          </TimelineDot>
                          <TimelineConnector />
                        </TimelineSeparator>
                        <TimelineContent sx={{ py: "12px", px: 2 }}>
                          <Card
                            elevation={0}
                            sx={{
                              border: 1,
                              borderColor: "#C4C4C4",
                              borderRadius: "8px",
                              width: "650px",
                            }}
                          >
                            <Box sx={{ padding: "1rem" }}>
                              <Stack spacing={1}>
                                <Grid
                                  sx={{
                                    display: "flex",
                                    justifyContent: "space-between",
                                  }}
                                >
                                  <Typography
                                    sx={{
                                      textAlign: "right",
                                      color: " #757575",
                                      fontWeight: "500",
                                      fontSize: "14px",
                                    }}
                                  >
                                    {moment(comment.createdAt).format(
                                      appSettings.date
                                    )}
                                  </Typography>
                                </Grid>

                                <Typography
                                  sx={{
                                    fontSize: "14px",

                                    color: " #757575",
                                    fontWeight: "500",
                                  }}
                                >
                                  {comment.user}
                                </Typography>
                                <Typography
                                  sx={{
                                    fontSize: "14px",
                                    color: "#1D1D1D",
                                    fontWeight: "600",
                                  }}
                                >
                                  {comment.comment}
                                </Typography>
                              </Stack>
                            </Box>
                          </Card>
                        </TimelineContent>
                      </TimelineItem>
                    ))}
                  </Timeline>
                )}
                {!Boolean(comments.length) && (
                  <Typography variant="body2">No Comments Found</Typography>
                )}
                <br />
              </Card>
            ) : (
              <>
                <ReusableAttachementAndComments
                  title="GeneralLedger"
                  useMetaData={false}
                  artifactId={glNumber}
                  artifactName="GeneralLedger"
                />
                <Card sx={{ padding: "1rem 1rem 0rem 1rem" }}>
                  <Grid
                    container
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                    }}
                  >
                    <Typography variant="h6">
                      <strong>Attachments</strong>
                    </Typography>
                  </Grid>
                  {Boolean(attachments.length) && (
                    <ReusableTable
                      width="100%"
                      rows={attachments}
                      columns={attachmentColumns}
                      hideFooter={false}
                      getRowIdValue={"id"}
                      disableSelectionOnClick={true}
                      stopPropagation_Column={"action"}
                    />
                  )}
                  {!Boolean(attachments.length) && (
                    <Typography variant="body2">
                      No Attachments Found
                    </Typography>
                  )}
                  <br />
                  <Typography variant="h6">Comments</Typography>
                  {Boolean(comments.length) && (
                    <Timeline
                      sx={{
                        [`& .${timelineItemClasses.root}:before`]: {
                          flex: 0,
                          padding: 0,
                        },
                      }}
                    >
                      {comments.map((comment) => (
                        <TimelineItem>
                          <TimelineSeparator>
                            <TimelineDot>
                              <CheckCircleOutlineOutlined
                                sx={{ color: "#757575" }}
                              />
                            </TimelineDot>
                            <TimelineConnector />
                          </TimelineSeparator>
                          <TimelineContent sx={{ py: "12px", px: 2 }}>
                            <Card
                              elevation={0}
                              sx={{
                                border: 1,
                                borderColor: "#C4C4C4",
                                borderRadius: "8px",
                                width: "650px",
                              }}
                            >
                              <Box sx={{ padding: "1rem" }}>
                                <Stack spacing={1}>
                                  <Grid
                                    sx={{
                                      display: "flex",
                                      justifyContent: "space-between",
                                    }}
                                  >
                                    <Typography
                                      sx={{
                                        textAlign: "right",
                                        color: " #757575",
                                        fontWeight: "500",
                                        fontSize: "12px",
                                      }}
                                    >
                                      {moment(comment.createdAt).format(
                                        "DD MMM YYYY"
                                      )}
                                    </Typography>
                                  </Grid>

                                  <Typography
                                    sx={{
                                      fontSize: "12px",

                                      color: " #757575",
                                      fontWeight: "500",
                                    }}
                                  >
                                    {comment.user}
                                  </Typography>
                                  <Typography
                                    sx={{
                                      fontSize: "12px",
                                      color: "#1D1D1D",
                                      fontWeight: "600",
                                    }}
                                  >
                                    {comment.comment}
                                  </Typography>
                                </Stack>
                              </Box>
                            </Card>
                          </TimelineContent>
                        </TimelineItem>
                      ))}
                    </Timeline>
                  )}
                  {!Boolean(comments.length) && (
                    <Typography variant="body2">No Comments Found</Typography>
                  )}
                  <br />
                </Card>
              </>
            )}
          </>,
        ];
      }
    });

  const handleGeneralLedgerSubmitChange = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `General Ledger Submitted for Approval with ID CLS${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Approve");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting General Ledger");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/alter/changeGeneralLedgerApprovalSubmit`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleGeneralLedgerSubmitExtend = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `General Ledger Submitted for Approval with ID ELS${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Approve");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting General Ledger");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/alter/extendGeneralLedgerApprovalSubmit`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleGeneralLedgerSubmitCreate = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `General Ledger Submitted for Approval with ID NLS${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting General Ledger");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/alter/generalLedgerApprovalSubmit`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleGeneralLedgerReviewChange = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `General Ledger Submitted For Review with ID CLS${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
        const secondApiPayload = {
          artifactId: glNumber,
          createdBy: userData?.emailId,
          artifactType: "GeneralLedger",
          requestId: `CLS${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting General Ledger");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/alter/changeGeneralLedgerSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleGeneralLedgerReviewExtend = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `General Ledger Submitted For Review with ID ELS${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
        const secondApiPayload = {
          artifactId: glNumber,
          createdBy: userData?.emailId,
          artifactType: "GeneralLedger",
          requestId: `ELS${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting General Ledger");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/alter/extendGeneralLedgerSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleGeneralLedgerReviewCreate = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `General Ledger Submitted for Review with ID NLS${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
        const secondApiPayload = {
          artifactId: glNumber,
          createdBy: userData?.emailId,
          artifactType: "GeneralLedger",
          requestId: `NLS${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Saving the Data");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/alter/generalLedgerSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const handleCostCenterCorrectionChange = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `General Ledger submitted for Correction with ID CLS${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting for Correction");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/alter/changeGeneralLedgerSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCostCenterCorrectionCreate = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `General Ledger submitted for Correction with ID NCR${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting for Correction");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/alter/costCenterSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleGeneralLedgerApproveChange = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 201) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(`${data.message}`);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Approving General Ledger");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/alter/changeGeneralLedgerApproved`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleGeneralLedgerApproveExtend = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 201) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(`${data.message}`);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Approving General Ledger");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/alter/extendGeneralLedgerApproved`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleGeneralLedgerApproveCreate = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 201) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(`${data.message}`);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Approving the General Ledger");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/alter/createGeneralLedgerApproved`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCostCenterRereview = () => {
    const hSuccess = (data) => {
      setMessageDialogMessage(
        `Create id generated for Data Owners CMS${data.body}`
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/alter/costCenterSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleOpenCorrectionDialog = () => {
    setOpenCorrectionDialog(true);
  };
  const handleCorrectionDialogClose = () => {
    setRemarks("");
    setOpenCorrectionDialog(false);
  };
  const handleRemarks = (e) => {
    const newValue = e.target.value;
    if (newValue.length > 0 && newValue[0] === " ") {
      setRemarks(newValue.trimStart());
    } else {
      //let costCenterValue = e.target.value;
      let remarksUpperCase = newValue.toUpperCase();
      setRemarks(remarksUpperCase);
    }
  };

  const handleWarningDialogClose = () => {
    setOpenMessageDialog(false);
  };
  console.log("factorsarray", factorsArray);
  return (
    <>
      {isLoading === true ? (
        <LoadingComponent />
      ) : (
      <div style={{ backgroundColor: "#FAFCFF" }}>
        <ReusableDialog
          dialogState={openMessageDialog}
          openReusableDialog={handleMessageDialogClickOpen}
          closeReusableDialog={handleMessageDialogClose}
          dialogTitle={messageDialogTitle}
          dialogMessage={messageDialogMessage}
          handleDialogConfirm={handleMessageDialogClose}
          dialogOkText={"OK"}
          showExtraButton={handleExtrabutton}
          showCancelButton={true}
          // handleExtraButton={handleMessageDialogNavigate}
          dialogSeverity={messageDialogSeverity}
          handleDialogReject={handleWarningDialogClose}
          handleExtraText={handleExtraText}
          handleExtraButton={handleProceedbutton}
        />
        {successMsg && (
          <ReusableSnackBar
            openSnackBar={openSnackbar}
            alertMsg={messageDialogMessage}
            handleSnackBarClose={handleSnackBarClose}
          />
        )}
        {formValidationErrorItems.length != 0 && (
          <ReusableSnackBar
            openSnackBar={openSnackbarValidation}
            alertMsg={
              "Please fill the following Field: " +
              formValidationErrorItems.join(", ")
            }
            handleSnackBarClose={handleSnackBarCloseValidation}
          />
        )}
        <Dialog
          hideBackdrop={false}
          elevation={2}
          PaperProps={{
            sx: { boxShadow: "none" },
          }}
          open={openCorrectionDialog}
          onClose={handleCorrectionDialogClose}
        >
          <DialogTitle
            sx={{
              justifyContent: "space-between",
              alignItems: "center",
              height: "max-content",
              padding: ".5rem",
              paddingLeft: "1rem",
              backgroundColor: "#EAE9FF40",
              display: "flex",
            }}
          >
            <Typography variant="h6">Remarks</Typography>

            <IconButton
              sx={{ width: "max-content" }}
              onClick={handleCorrectionDialogClose}
              children={<CloseIcon />}
            />
          </DialogTitle>
          <DialogContent sx={{ padding: ".5rem 1rem" }}>
            <Stack>
              <Box sx={{ minWidth: 400 }}>
                <FormControl sx={{ height: "auto" }} fullWidth>
                  <TextField
                    sx={{ backgroundColor: "#F5F5F5" }}
                    onChange={handleRemarks}
                    value={remarks}
                    multiline
                    placeholder={"Enter Remarks for Correction"}
                    inputProps={{ maxLength: 254 }}
                  ></TextField>
                </FormControl>
              </Box>
            </Stack>
          </DialogContent>
          <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
            <Button
              sx={{ width: "max-content", textTransform: "capitalize" }}
              onClick={handleCorrectionDialogClose}
            >
              Cancel
            </Button>
            <Button
              className="button_primary--normal"
              type="save"
              onClick={onGeneralLedgerCorrection}
              variant="contained"
            >
              Submit
            </Button>
          </DialogActions>
        </Dialog>

        <Dialog
          hideBackdrop={false}
          elevation={2}
          PaperProps={{
            sx: { boxShadow: "none" },
          }}
          open={openRemarkDialog}
          onClose={handleRemarksDialogClose}
        >
          {/* <Grid
          container
          sx={{ display: "flex", justifyContent: "space-between" }}
        > */}
          {/* <Grid item> */}
          <DialogTitle
            sx={{
              justifyContent: "space-between",
              alignItems: "center",
              height: "max-content",
              padding: ".5rem",
              paddingLeft: "1rem",
              backgroundColor: "#EAE9FF40",
              // borderBottom: "1px solid grey",
              display: "flex",
            }}
          >
            <Typography variant="h6">Remarks</Typography>

            <IconButton
              sx={{ width: "max-content" }}
              onClick={handleRemarksDialogClose}
              children={<CloseIcon />}
            />
          </DialogTitle>
          {/* </Grid> */}
          {/* </Grid> */}
          <DialogContent sx={{ padding: ".5rem 1rem" }}>
            <Stack>
              <Box sx={{ minWidth: 400 }}>
                <FormControl sx={{ height: "auto" }} fullWidth>
                  <TextField
                    sx={{ backgroundColor: "#F5F5F5" }}
                    value={remarks.toUpperCase()}
                    onChange={handleRemarks}
                    multiline
                    placeholder={"Enter Remarks"}
                    inputProps={{ maxLength: 254 }}
                  ></TextField>
                </FormControl>
              </Box>
            </Stack>
          </DialogContent>
          <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
            <Button
              sx={{ width: "max-content", textTransform: "capitalize" }}
              onClick={handleRemarksDialogClose}
            >
              Cancel
            </Button>
            <Button
              className="button_primary--normal"
              type="save"
              onClick={onGeneralLedgerSubmitRemarks}
              variant="contained"
            >
              Submit
            </Button>
          </DialogActions>
        </Dialog>

        <Backdrop
          sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
          open={blurLoading}
          // onClick={handleClose}
        >
          <CircularProgress color="inherit" />
        </Backdrop>

        <Grid container sx={outermostContainer_Information}>
          <Grid item md={12} style={{ padding: "16px", display: "flex" }}>
            <Grid md={9} sx={{ display: "flex" }}>
              <Grid>
                <IconButton
                  // onClick={handleBacktoRO}
                  color="primary"
                  aria-label="upload picture"
                  component="label"
                  sx={iconButton_SpacingSmall}
                >
                  <ArrowCircleLeftOutlinedIcon
                    sx={{
                      fontSize: "25px",
                      color: "#000000",
                    }}
                    onClick={() => {
                      navigate("/masterDataCockpit/generalLedger");
                    }}
                  />
                </IconButton>
              </Grid>
              <Grid>
                {isEditMode ? (
                  taskRowDetails?.processDesc === "Create" ||
                  generalLedgerRowData?.requestType === "Create" ? (
                    <>
                      <Grid item md={12}>
                        <Typography variant="h3">
                          <strong>Create General Ledger </strong>
                        </Typography>

                        <Typography variant="body2" color="#777">
                          This view edits the details of the General Ledger
                        </Typography>
                      </Grid>
                    </>
                  ) : taskRowDetails?.processDesc === "Change" ||
                    generalLedgerRowData?.requestType === "Change" ? (
                    <>
                      <Grid item md={12}>
                        <Typography variant="h3">
                          <strong>Change General Ledger </strong>
                        </Typography>

                        <Typography variant="body2" color="#777">
                          This view edits the details of the General Ledger
                        </Typography>
                      </Grid>
                    </>
                  ) : taskRowDetails?.processDesc === "Extend" ||
                    generalLedgerRowData?.requestType === "Extend" ? (
                    <>
                      <Grid item md={12}>
                        <Typography variant="h3">
                          <strong>Extend General Ledger </strong>
                        </Typography>

                        <Typography variant="body2" color="#777">
                          This view edits the details of the General Ledger
                        </Typography>
                      </Grid>
                    </>
                  ) : (
                    <>
                      <Grid item md={12}>
                        <Typography variant="h3">
                          <strong>Change General Ledger </strong>
                        </Typography>

                        <Typography variant="body2" color="#777">
                          This view edits the details of the General Ledger
                        </Typography>
                      </Grid>
                    </>
                  )
                ) : (
                  ""
                )}

                {isDisplayMode ? (
                  <Grid item md={12}>
                    <Typography variant="h3">
                      <strong>Display General Ledger </strong>
                    </Typography>

                    <Typography variant="body2" color="#777">
                      This view displays the details of the General Ledger
                    </Typography>
                  </Grid>
                ) : (
                  ""
                )}
              </Grid>
            </Grid>

            <Grid
              md={3}
              sx={{ display: "flex", justifyContent: "flex-end" }}
              gap={2}
            >
              {generalLedgerRowData?.requestId ||
              taskRowDetails?.processDesc ? (
                <Grid>
                  <Button
                    variant="outlined"
                    size="small"
                    sx={button_Outlined}
                    onClick={openChangeLog}
                    title="Change Log"
                  >
                    <TrackChangesTwoToneIcon
                      sx={{ padding: "2px" }}
                      fontSize="small"
                    />
                  </Button>
                </Grid>
              ) : (
                ""
              )}

              {isChangeLogopen && (
                <ChangeLog
                  open={true}
                  closeModal={handleClosemodalData}
                  requestId={
                    generalLedgerRowData?.requestId
                      ? generalLedgerRowData?.requestId
                      : taskRowDetails?.subject
                  }
                  requestType={
                    generalLedgerRowData?.requestType
                      ? generalLedgerRowData?.requestType
                      : taskData?.body?.processDesc
                  }
                  pageName={"generalLedger"}
                  controllingArea={
                    generalLedgerRowData?.controllingArea
                      ? generalLedgerRowData?.controllingArea
                      : taskData?.body?.compCode
                  }
                  centerName={
                    generalLedgerRowData?.costCenter
                      ? generalLedgerRowData?.costCenter
                      : taskData?.body?.glAccount
                  }
                />
              )}
              {checkIwaAccess(iwaAccessData, "General Ledger", "ChangeGL") &&
                (userData?.role === "Super User" &&
                generalLedgerRowData?.requestType &&
                taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
                isDisplayMode ? (
                  <Grid gap={1} sx={{ display: "flex" }}>
                    <Grid
                      gap={1}
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                      }}
                    >
                      <>
                        <Grid item>
                          <Button
                            variant="outlined"
                            size="small"
                            sx={button_Outlined}
                            onClick={onEdit}
                          >
                            Fill Details
                            <EditOutlinedIcon
                              sx={{ padding: "2px" }}
                              fontSize="small"
                            />
                          </Button>
                        </Grid>
                      </>
                    </Grid>
                  </Grid>
                ) : userData?.role === "Finance" &&
                  (generalLedgerRowData?.requestType ||
                    taskRowDetails?.processDesc) &&
                  taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
                  isDisplayMode ? (
                  <Grid gap={1} sx={{ display: "flex" }}>
                    <Grid
                      gap={1}
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                      }}
                    >
                      <>
                        <Grid item>
                          <Button
                            variant="outlined"
                            size="small"
                            sx={button_Outlined}
                            onClick={onEdit}
                          >
                            Fill Details
                            <EditOutlinedIcon
                              sx={{ padding: "2px" }}
                              fontSize="small"
                            />
                          </Button>
                        </Grid>
                      </>
                    </Grid>
                  </Grid>
                ) : userData?.role === "Super User" &&
                  !generalLedgerRowData?.requestType &&
                  taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
                  isDisplayMode ? (
                  <Grid gap={1} sx={{ display: "flex" }}>
                    <Grid
                      gap={1}
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                      }}
                    >
                      <>
                        <Grid item>
                          <Button
                            variant="outlined"
                            size="small"
                            sx={button_Outlined}
                            onClick={onEdit}
                          >
                            Change
                            <EditOutlinedIcon
                              sx={{ padding: "2px" }}
                              fontSize="small"
                            />
                          </Button>
                        </Grid>
                      </>
                    </Grid>
                  </Grid>
                ) : userData?.role === "Finance" &&
                  !generalLedgerRowData?.requestType &&
                  taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
                  isDisplayMode ? (
                  <Grid gap={1} sx={{ display: "flex" }}>
                    <Grid
                      gap={1}
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                      }}
                    >
                      <>
                        <Grid item>
                          <Button
                            variant="outlined"
                            size="small"
                            sx={button_Outlined}
                            onClick={onEdit}
                          >
                            Change
                            <EditOutlinedIcon
                              sx={{ padding: "2px" }}
                              fontSize="small"
                            />
                          </Button>
                        </Grid>
                      </>
                    </Grid>
                  </Grid>
                ) : (
                  ""
                ))}
            </Grid>
          </Grid>
          <Grid container display="flex" flexDirection="row" flexWrap="nowrap">
            <Box width="100%" sx={{ marginLeft: "40px" }}>
              <Grid item sx={{ paddingTop: "2px !important" }}>
                <Stack flexDirection="row">
                  <div style={{ width: "15%" }}>
                    <Typography variant="body2" color="#777">
                      Chart of Accounts
                    </Typography>
                  </div>
                  <Typography
                    variant="body2"
                    fontWeight="bold"
                    justifyContent="flex-start"
                  >
                    :{" "}
                    {generalLedgerRowData?.chartOfAccount
                      ? generalLedgerRowData?.chartOfAccount
                      : iDs?.ChartOfAccount
                      ? iDs?.ChartOfAccount
                      : ""}
                  </Typography>
                </Stack>
              </Grid>
              {generalLedgerRowData?.requestType != "Extend" ? (
                <Grid item sx={{ paddingTop: "2px !important" }}>
                  <Stack flexDirection="row">
                    <div style={{ width: "15%" }}>
                      <Typography variant="body2" color="#777">
                        Company Code
                      </Typography>
                    </div>
                    <Typography variant="body2" fontWeight="bold">
                      :{" "}
                      {generalLedgerRowData?.compCode
                        ? generalLedgerRowData?.compCode
                        : iDs?.CompCode
                        ? iDs?.CompCode
                        : ""}
                    </Typography>
                  </Stack>
                </Grid>
              ) : (
                <>
                  <Grid item sx={{ paddingTop: "2px !important" }}>
                    <Stack flexDirection="row">
                      <div style={{ width: "15%" }}>
                        <Typography variant="body2" color="#777">
                          Reference Company Code
                        </Typography>
                      </div>
                      <Typography variant="body2" fontWeight="bold">
                        :{" "}
                        {generalLedgerRowData?.compCode
                          ? generalLedgerRowData?.compCode
                          : iDs?.CompCode
                          ? iDs?.CompCode
                          : ""}
                      </Typography>
                    </Stack>
                  </Grid>
                </>
              )}

              <Grid item sx={{ paddingTop: "2px !important" }}>
                <Stack flexDirection="row">
                  <div style={{ width: "15%" }}>
                    <Typography variant="body2" color="#777">
                      G/L Account
                    </Typography>
                  </div>
                  <Typography variant="body2" fontWeight="bold">
                    :{" "}
                    {generalLedgerRowData?.glAccount
                      ? generalLedgerRowData?.glAccount
                      : iDs?.GLAccount
                      ? iDs?.GLAccount
                      : ""}
                  </Typography>
                </Stack>
              </Grid>
              <Grid item sx={{ paddingTop: "2px !important" }}>
                <Stack flexDirection="row">
                  <div style={{ width: "15%" }}>
                    <Typography variant="body2" color="#777">
                      Extended Company Code
                    </Typography>
                  </div>
                  <Typography variant="body2" fontWeight="bold">
                    : {extendedCompanyCode.join(", ")}
                  </Typography>
                </Stack>
              </Grid>
            </Box>
          </Grid>

          <Grid container style={{ marginLeft: 25 }}>
            <Stepper
              activeStep={activeStep}
              // onChange={handleC/hange}
              // variant="scrollable"
              sx={{
                background: "#FFFFFF",
                borderBottom: "1px solid #BDBDBD",
                width: "100%",
                height: "48px",
              }}
              aria-label="mui tabs example"
            >
              {factorsArray.map((factor, index) => (
                <Step key={factor}>
                  <StepLabel sx={{ fontWeight: "700" }}>{factor}</StepLabel>
                </Step>
              ))}
            </Stepper>

            {/* Display the cards of the currently active tab */}
            {tabContents &&
              tabContents[activeStep]?.map((cardContent, index) => (
                <Box key={index} sx={{ mb: 2, width: "100%" }}>
                  <Typography variant="body2">{cardContent}</Typography>
                </Box>
              ))}
          </Grid>
        </Grid>

        <Grid gap={1} sx={{ display: "flex", justifyContent: "space-between" }}>
          {checkIwaAccess(iwaAccessData, "General Ledger", "ChangeGL") &&
            (!generalLedgerRowData?.requestType && !isEditMode ? (
              <Paper
                sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                elevation={2}
              >
                <BottomNavigation
                  className="container_BottomNav"
                  showLabels
                  sx={{ display: "flex", justifyContent: "flex-end" }}
                >
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleBack}
                    disabled={activeStep === 0}
                  >
                    Back
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleNext}
                    disabled={
                      activeStep === factorsArray.length - 1 ? true : false
                    }
                  >
                    Next
                  </Button>
                </BottomNavigation>
              </Paper>
            ) : (
              <Paper
                sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                elevation={2}
              >
                <BottomNavigation
                  className="container_BottomNav"
                  showLabels
                  sx={{ display: "flex", justifyContent: "flex-end" }}
                >
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleBack}
                    disabled={activeStep === 0}
                  >
                    Back
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleNext}
                    disabled={
                      activeStep === factorsArray.length - 1 ? true : false
                    }
                  >
                    Next
                  </Button>
                </BottomNavigation>
              </Paper>
            ))}

          {checkIwaAccess(iwaAccessData, "General Ledger", "ChangeGL") &&
            (userData?.role === "Super User" &&
            !generalLedgerRowData?.requestType &&
            taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
            isEditMode ? (
              <Paper
                sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                elevation={2}
              >
                <BottomNavigation
                  className="container_BottomNav"
                  showLabels
                  sx={{ display: "flex", justifyContent: "flex-end" }}
                >
                  {taskRowDetails?.taskId ? (
                    ""
                  ) : (
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ button_Outlined, mr: 1 }}
                      onClick={onGeneralLedgerSaveAsDraftChange}
                    >
                      Save As Draft
                    </Button>
                  )}
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleBack}
                    disabled={activeStep === 0}
                  >
                    Back
                  </Button>

                  {activeStep === factorsArray.length - 1 ? (
                    <>
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Outlined, mr: 1 }}
                        onClick={onValidateGeneralLedger}
                      >
                        Validate
                      </Button>
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Primary, mr: 1 }}
                        onClick={onGeneralLedgerReviewChange}
                        disabled={submitForReviewDisabled}
                      >
                        Submit For Review
                      </Button>
                    </>
                  ) : (
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleNext}
                      disabled={
                        activeStep === factorsArray.length - 1 ? true : false
                      }
                    >
                      Next
                    </Button>
                  )}
                </BottomNavigation>
              </Paper>
            ) : userData?.role === "Finance" &&
              !generalLedgerRowData?.requestType &&
              taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
              isEditMode ? (
              <Paper
                sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                elevation={2}
              >
                <BottomNavigation
                  className="container_BottomNav"
                  showLabels
                  sx={{ display: "flex", justifyContent: "flex-end" }}
                >
                  {taskRowDetails?.taskId ? (
                    ""
                  ) : (
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ button_Outlined, mr: 1 }}
                      onClick={onGeneralLedgerSaveAsDraftChange}
                    >
                      Save As Draft
                    </Button>
                  )}
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleBack}
                    disabled={activeStep === 0}
                  >
                    Back
                  </Button>
                  {activeStep === factorsArray.length - 1 ? (
                    <>
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Outlined, mr: 1 }}
                        onClick={onValidateGeneralLedger}
                      >
                        Validate
                      </Button>
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Primary, mr: 1 }}
                        onClick={handleOpenRemarkDialog}
                        disabled={submitForReviewDisabled}
                      >
                        Submit For Review
                      </Button>
                    </>
                  ) : (
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleNext}
                      disabled={
                        activeStep === factorsArray.length - 1 ? true : false
                      }
                    >
                      Next
                    </Button>
                  )}
                </BottomNavigation>
              </Paper>
            ) : (
              ""
            ))}

          {checkIwaAccess(iwaAccessData, "General Ledger", "ChangeGL") &&
            (userData?.role === "Super User" &&
            generalLedgerRowData?.requestType === "Create" &&
            taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
            !isEditMode ? (
              <Paper
                sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                elevation={2}
              >
                <BottomNavigation
                  className="container_BottomNav"
                  showLabels
                  sx={{ display: "flex", justifyContent: "flex-end" }}
                >
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={onValidateGeneralLedgerApprover}
                  >
                    Validate
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ button_Outlined, mr: 1 }}
                    onClick={onGeneralLedgerApproveCreate}
                    disabled={submitForReviewDisabled}
                  >
                    Approve
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={onGeneralLedgerSubmitCreate}
                  >
                    Submit For Approval
                  </Button>

                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleBack}
                    disabled={activeStep === 0}
                  >
                    Back
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleNext}
                    disabled={
                      activeStep === factorsArray.length - 1 ? true : false
                    }
                  >
                    Next
                  </Button>
                </BottomNavigation>
              </Paper>
            ) : userData?.role === "Super User" &&
              generalLedgerRowData?.requestType === "Change" &&
              taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
              !isEditMode ? (
              <Paper
                sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                elevation={2}
              >
                <BottomNavigation
                  className="container_BottomNav"
                  showLabels
                  sx={{ display: "flex", justifyContent: "flex-end" }}
                >
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={onValidateGeneralLedgerApprover}
                  >
                    Validate
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ button_Outlined, mr: 1 }}
                    onClick={onGeneralLedgerApproveChange}
                    disabled={submitForReviewDisabled}
                  >
                    Approve
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={onGeneralLedgerSubmitChange}
                  >
                    Submit For Approval
                  </Button>
                  {/* <Button
                  variant="outlined"
                  size="small"
                  sx={button_Outlined}
                  onClick={onCostCenterCorrectionChange}
                >
                  Correction
                  <EditOutlinedIcon
                    sx={{ marginLeft: "5px", padding: "2px" }}
                    fontSize="small"
                  />
                </Button> */}
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleBack}
                    disabled={activeStep === 0}
                  >
                    Back
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleNext}
                    disabled={
                      activeStep === factorsArray.length - 1 ? true : false
                    }
                  >
                    Next
                  </Button>
                </BottomNavigation>
              </Paper>
            ) : userData?.role === "MDM Steward" &&
              (generalLedgerRowData?.requestType === "Create" ||
                taskRowDetails?.processDesc === "Create") &&
              taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
              !isEditMode ? (
              <Paper
                sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                elevation={2}
              >
                <BottomNavigation
                  className="container_BottomNav"
                  showLabels
                  sx={{ display: "flex", justifyContent: "flex-end" }}
                >
                  <Button
                    variant="outlined"
                    size="small"
                    sx={{ button_Outlined, mr: 1 }}
                    onClick={handleOpenCorrectionDialog}
                  >
                    Correction
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleOpenRemarkDialog}
                  >
                    Submit For Approval
                  </Button>
                  {/* <Button
                  variant="outlined"
                  size="small"
                  sx={button_Outlined}
                  onClick={onCostCenterCorrectionCreate}
                >
                  Correction
                  <EditOutlinedIcon
                    sx={{ marginLeft: "5px", padding: "2px" }}
                    fontSize="small"
                  />
                </Button> */}
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleBack}
                    disabled={activeStep === 0}
                  >
                    Back
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleNext}
                    disabled={
                      activeStep === factorsArray.length - 1 ? true : false
                    }
                  >
                    Next
                  </Button>
                </BottomNavigation>
              </Paper>
            ) : userData?.role === "MDM Steward" &&
              (generalLedgerRowData?.requestType === "Change" ||
                taskRowDetails?.processDesc === "Change") &&
              taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
              !isEditMode ? (
              <Paper
                sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                elevation={2}
              >
                <BottomNavigation
                  className="container_BottomNav"
                  showLabels
                  sx={{ display: "flex", justifyContent: "flex-end" }}
                >
                  <Button
                    variant="outlined"
                    size="small"
                    sx={{ button_Outlined, mr: 1 }}
                    onClick={handleOpenCorrectionDialog}
                  >
                    Correction
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleOpenRemarkDialog}
                  >
                    Submit For Approval
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleBack}
                    disabled={activeStep === 0}
                  >
                    Back
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleNext}
                    disabled={
                      activeStep === factorsArray.length - 1 ? true : false
                    }
                  >
                    Next
                  </Button>
                </BottomNavigation>
              </Paper>
            ) : userData?.role === "Approver" &&
              (generalLedgerRowData?.requestType === "Create" ||
                taskRowDetails?.processDesc === "Create") &&
              taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
              !isEditMode ? (
              <Paper
                sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                elevation={2}
              >
                <BottomNavigation
                  className="container_BottomNav"
                  showLabels
                  sx={{ display: "flex", justifyContent: "flex-end" }}
                >
                  <Button
                    variant="outlined"
                    size="small"
                    sx={{ button_Outlined, mr: 1 }}
                    onClick={handleOpenCorrectionDialog}
                  >
                    Correction
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={onValidateGeneralLedgerApprover}
                  >
                    Validate
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ button_Outlined, mr: 1 }}
                    onClick={handleOpenRemarkDialog}
                    disabled={submitForReviewDisabled}
                  >
                    Approve
                  </Button>

                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleBack}
                    disabled={activeStep === 0}
                  >
                    Back
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleNext}
                    disabled={
                      activeStep === factorsArray.length - 1 ? true : false
                    }
                  >
                    Next
                  </Button>
                </BottomNavigation>
              </Paper>
            ) : userData?.role === "Approver" &&
              (generalLedgerRowData?.requestType === "Change" ||
                taskRowDetails?.processDesc === "Change") &&
              taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
              !isEditMode ? (
              <Paper
                sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                elevation={2}
              >
                <BottomNavigation
                  className="container_BottomNav"
                  showLabels
                  sx={{ display: "flex", justifyContent: "flex-end" }}
                >
                  <Button
                    variant="outlined"
                    size="small"
                    sx={{ button_Outlined, mr: 1 }}
                    onClick={handleOpenCorrectionDialog}
                  >
                    Correction
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={onValidateGeneralLedgerApprover}
                  >
                    Validate
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ button_Outlined, mr: 1 }}
                    onClick={handleOpenRemarkDialog}
                    disabled={submitForReviewDisabled}
                  >
                    Approve
                  </Button>
                  {/* <Button
                  variant="outlined"
                  size="small"
                  sx={button_Outlined}
                  onClick={onCostCenterCorrectionChange}
                >
                  Correction
                  <EditOutlinedIcon
                    sx={{ marginLeft: "5px", padding: "2px" }}
                    fontSize="small"
                  />
                </Button> */}
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleBack}
                    disabled={activeStep === 0}
                  >
                    Back
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleNext}
                    disabled={
                      activeStep === factorsArray.length - 1 ? true : false
                    }
                  >
                    Next
                  </Button>
                </BottomNavigation>
              </Paper>
            )
            : userData?.role === "Approver" &&
              (generalLedgerRowData?.requestType === "Extend" ||
                taskRowDetails?.processDesc === "Extend") &&
              taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
              !isEditMode ? (
              <Paper
                sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                elevation={2}
              >
                <BottomNavigation
                  className="container_BottomNav"
                  showLabels
                  sx={{ display: "flex", justifyContent: "flex-end" }}
                >
                  <Button
                    variant="outlined"
                    size="small"
                    sx={{ button_Outlined, mr: 1 }}
                    onClick={handleOpenCorrectionDialog}
                  >
                    Correction
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={onValidateGeneralLedgerApprover}
                  >
                    Validate
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ button_Outlined, mr: 1 }}
                    onClick={handleOpenRemarkDialog}
                    // disabled={submitForReviewDisabled}
                  >
                    Approve
                  </Button>
                  {/* <Button
                  variant="outlined"
                  size="small"
                  sx={button_Outlined}
                  onClick={onCostCenterCorrectionChange}
                >
                  Correction
                  <EditOutlinedIcon
                    sx={{ marginLeft: "5px", padding: "2px" }}
                    fontSize="small"
                  />
                </Button> */}
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleBack}
                    disabled={activeStep === 0}
                  >
                    Back
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleNext}
                    disabled={
                      activeStep === factorsArray.length - 1 ? true : false
                    }
                  >
                    Next
                  </Button>
                </BottomNavigation>
              </Paper>
            ) : userData?.role === "Super User" &&
              generalLedgerRowData?.requestType === "Create" &&
              taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
              isEditMode ? (
              <Paper
                sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                elevation={2}
              >
                <BottomNavigation
                  className="container_BottomNav"
                  showLabels
                  sx={{ display: "flex", justifyContent: "flex-end" }}
                >
                  {/* <Button
                  variant="outlined"
                  size="small"
                  sx={button_Outlined}
                  onClick={onCostCenterSaveAsDraft}
                >
                  Save As Draft
                </Button> */}
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleBack}
                    disabled={activeStep === 0}
                  >
                    Back
                  </Button>
                  {activeStep === factorsArray.length - 1 ? (
                    <>
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Outlined, mr: 1 }}
                        onClick={onValidateGeneralLedger}
                      >
                        Validate
                      </Button>
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Primary, mr: 1 }}
                        onClick={onGeneralLedgerReviewCreate}
                        disabled={submitForReviewDisabled}
                      >
                        Submit For Review
                      </Button>
                    </>
                  ) : (
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleNext}
                      disabled={
                        activeStep === factorsArray.length - 1 ? true : false
                      }
                    >
                      Next
                    </Button>
                  )}
                </BottomNavigation>
              </Paper>
            ) : userData?.role === "Super User" &&
              generalLedgerRowData?.requestType === "Change" &&
              taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
              isEditMode ? (
              <Paper
                sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                elevation={2}
              >
                <BottomNavigation
                  className="container_BottomNav"
                  showLabels
                  sx={{ display: "flex", justifyContent: "flex-end" }}
                >
                  {/* <Button
                  variant="outlined"
                  size="small"
                  sx={button_Outlined}
                  onClick={onCostCenterSaveAsDraft}
                >
                  Save As Draft
                </Button> */}
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleBack}
                    disabled={activeStep === 0}
                  >
                    Back
                  </Button>
                  {activeStep === factorsArray.length - 1 ? (
                    <>
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Outlined, mr: 1 }}
                        onClick={onValidateGeneralLedger}
                      >
                        Validate
                      </Button>
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Primary, mr: 1 }}
                        onClick={onGeneralLedgerReviewChange}
                        disabled={submitForReviewDisabled}
                      >
                        Submit For Review
                      </Button>
                    </>
                  ) : (
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleNext}
                      disabled={
                        activeStep === factorsArray.length - 1 ? true : false
                      }
                    >
                      Next
                    </Button>
                  )}
                </BottomNavigation>
              </Paper>
            ) : userData?.role === "Finance" &&
              (generalLedgerRowData?.requestType === "Create" ||
                taskRowDetails?.processDesc === "Create") &&
              taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
              isEditMode ? (
              <Paper
                sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                elevation={2}
              >
                <BottomNavigation
                  className="container_BottomNav"
                  showLabels
                  sx={{ display: "flex", justifyContent: "flex-end" }}
                >
                  {taskRowDetails?.taskId ? (
                    ""
                  ) : (
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ button_Outlined, mr: 1 }}
                      onClick={onGeneralLedgerSaveAsDraftCreate}
                    >
                      Save As Draft
                    </Button>
                  )}
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleBack}
                    disabled={activeStep === 0}
                  >
                    Back
                  </Button>
                  {activeStep === factorsArray.length - 1 ? (
                    <>
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Outlined, mr: 1 }}
                        onClick={onValidateGeneralLedger}
                      >
                        Validate
                      </Button>
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Primary, mr: 1 }}
                        onClick={handleOpenRemarkDialog}
                        disabled={submitForReviewDisabled}
                      >
                        Submit For Review
                      </Button>
                    </>
                  ) : (
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleNext}
                      disabled={
                        activeStep === factorsArray.length - 1 ? true : false
                      }
                    >
                      Next
                    </Button>
                  )}
                </BottomNavigation>
              </Paper>
            ) : userData?.role === "Finance" &&
              (generalLedgerRowData?.requestType === "Change" ||
                taskRowDetails?.processDesc === "Change") &&
              taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
              isEditMode ? (
              <Paper
                sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                elevation={2}
              >
                <BottomNavigation
                  className="container_BottomNav"
                  showLabels
                  sx={{ display: "flex", justifyContent: "flex-end" }}
                >
                  {taskRowDetails?.taskId ? (
                    ""
                  ) : (
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ button_Outlined, mr: 1 }}
                      onClick={onGeneralLedgerSaveAsDraftChange}
                    >
                      Save As Draft
                    </Button>
                  )}
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleBack}
                    disabled={activeStep === 0}
                  >
                    Back
                  </Button>
                  {activeStep === factorsArray.length - 1 ? (
                    <>
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Outlined, mr: 1 }}
                        onClick={onValidateGeneralLedger}
                      >
                        Validate
                      </Button>
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Primary, mr: 1 }}
                        onClick={handleOpenRemarkDialog}
                        disabled={submitForReviewDisabled}
                      >
                        Submit For Review
                      </Button>
                    </>
                  ) : (
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleNext}
                      disabled={
                        activeStep === factorsArray.length - 1 ? true : false
                      }
                    >
                      Next
                    </Button>
                  )}
                </BottomNavigation>
              </Paper>
            ) : userData?.role === "MDM Steward" &&
              (generalLedgerRowData?.requestType === "Extend" ||
                taskRowDetails?.processDesc === "Extend") &&
              taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
              !isEditMode ? (
              <Paper
                sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                elevation={2}
              >
                <BottomNavigation
                  className="container_BottomNav"
                  showLabels
                  sx={{ display: "flex", justifyContent: "flex-end" }}
                >
                  <Button
                    variant="outlined"
                    size="small"
                    sx={{ button_Outlined, mr: 1 }}
                    onClick={handleOpenCorrectionDialog}
                  >
                    Correction
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleOpenRemarkDialog}
                  >
                    Submit For Approval
                  </Button>
                  {/* <Button
                  variant="outlined"
                  size="small"
                  sx={button_Outlined}
                  onClick={onCostCenterCorrectionCreate}
                >
                  Correction
                  <EditOutlinedIcon
                    sx={{ marginLeft: "5px", padding: "2px" }}
                    fontSize="small"
                  />
                </Button> */}
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleBack}
                    disabled={activeStep === 0}
                  >
                    Back
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleNext}
                    disabled={
                      activeStep === factorsArray.length - 1 ? true : false
                    }
                  >
                    Next
                  </Button>
                </BottomNavigation>
              </Paper>
            ) : userData?.role === "Finance" &&
              (generalLedgerRowData?.requestType === "Extend" ||
                taskRowDetails?.processDesc === "Extend") &&
              taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
              isEditMode ? (
              <Paper
                sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                elevation={2}
              >
                <BottomNavigation
                  className="container_BottomNav"
                  showLabels
                  sx={{ display: "flex", justifyContent: "flex-end" }}
                >
                  {taskRowDetails?.taskId ? (
                    ""
                  ) : (
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ button_Outlined, mr: 1 }}
                      onClick={onGeneralLedgerSaveAsDraftExtend}
                    >
                      Save As Draft
                    </Button>
                  )}
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={handleBack}
                    disabled={activeStep === 0}
                  >
                    Back
                  </Button>
                  {activeStep === factorsArray.length - 1 ? (
                    <>
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Outlined, mr: 1 }}
                        onClick={onValidateGeneralLedger}
                      >
                        Validate
                      </Button>
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Primary, mr: 1 }}
                        onClick={handleOpenRemarkDialog}
                        // disabled={submitForReviewDisabled}
                      >
                        Submit For Review
                      </Button>
                    </>
                  ) : (
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleNext}
                      disabled={
                        activeStep === factorsArray.length - 1 ? true : false
                      }
                    >
                      Next
                    </Button>
                  )}
                </BottomNavigation>
              </Paper>
            ) : (
              ""
            ))}
        </Grid>
      </div>
      )} 
    </>
  );
};

export default DisplayGeneralLedger;
