import{a as n,j as p,l as Y,m as os,n as rs,o as is,p as Mt,r as s,e as Ia,q as Oe,s as ya,t as ne,B as X,G as x,v as cs,w as pa,F as je,M as lt,S as _t,T as _e,I as Dt,R as Ct,x as Nt,y as Ze,z as Rt,C as Wt,E as It,h as Ht,H as ds,J as us,K as k,N as pt,O as yt,P as fe,Q as fs,U as i,V as Ra,W as xa,X as La,Y as Vt,Z as v,$ as me,a0 as He,a1 as ht,a2 as gs,a3 as ps,a4 as hs,a5 as zt,a6 as Ms,a7 as wt,b as Ds,a8 as Es,a9 as As,aa as Ts,ab as j,ac as Ss,ad as _s,ae as Cs,af as Ns,ag as kt,ah as Is,ai as ha,aj as ys,ak as Ma,al as Rs,am as xs,an as Ls,ao as vs,f as Gs,ap as We,aq as de,ar as rt,as as Ps,at as Os,au as $s,av as bs,aw as ws,ax as Da,ay as ks,az as Ea,aA as Us,aB as Bs,aC as Ys,aD as Ut,aE as Ws,aF as Hs,aG as zs,aH as Vs,aI as Xs,aJ as qs,aK as Aa,aL as Ta,aM as Sa,aN as it,aO as Js,aP as Ks,aQ as Qs,aR as Zs,aS as Fs,aT as ms,aU as js,aV as el,aW as tl,aX as al,aY as nl,g as sl,A as ll,aZ as ol,a_ as Bt,a$ as _a,b0 as Ca}from"./index-75c1660a.js";import{A as rl}from"./AttachmentUploadDialog-e237fc29.js";import{u as va}from"./useMaterialFieldConfig-3a0b0d62.js";import{L as Fe}from"./LargeDropdown-701bd2f7.js";import{S as il}from"./SingleSelectDropdown-0d30aa01.js";import{d as cl,F as Yt}from"./FilterChangeDropdown-6376893e.js";import{I as dl}from"./InputAdornment-a22e1655.js";import"./CloudUpload-d5d09566.js";import"./Delete-1d158507.js";import"./utilityImages-067c3dc2.js";const ul=({percentage:G,id:V})=>{const se=L=>{var W,Q,I,Z,le;return L===100?(W=Y)==null?void 0:W.progressColors.complete:L>=75?(Q=Y)==null?void 0:Q.progressColors.high:L>=50?(I=Y)==null?void 0:I.progressColors.medium:L>=25?(Z=Y)==null?void 0:Z.progressColors.low:(le=Y)==null?void 0:le.progressColors.minimal},q=Math.round(G/100*16),he=30,ee=30,g=20,O=15,J=2*Math.PI/16,C=L=>{const W=L*J-Math.PI/2,Q=(L+1)*J-Math.PI/2,I=he+O*Math.cos(W),Z=ee+O*Math.sin(W),le=he+g*Math.cos(W),$e=ee+g*Math.sin(W),N=he+g*Math.cos(Q),y=ee+g*Math.sin(Q),B=he+O*Math.cos(Q),be=ee+O*Math.sin(Q);return`M ${I} ${Z} L ${le} ${$e} A ${g} ${g} 0 0 1 ${N} ${y} L ${B} ${be} A ${O} ${O} 0 0 0 ${I} ${Z} Z`},te=se(G);return n("div",{style:{display:"flex",alignItems:"center",justifyContent:"center",width:"100%",height:"100%",padding:"8px"},children:p("div",{style:{position:"relative",width:"60px",height:"60px",display:"flex",alignItems:"center",justifyContent:"center"},children:[p("svg",{width:"60",height:"60",viewBox:"0 0 60 60",style:{position:"absolute",top:0,left:0},children:[n("defs",{children:p("linearGradient",{id:`segmentGradient-${V}`,x1:"0%",y1:"0%",x2:"100%",y2:"100%",children:[n("stop",{offset:"0%",stopColor:te.start}),n("stop",{offset:"100%",stopColor:te.end})]})}),Array.from({length:16},(L,W)=>{var Q,I,Z;return n("path",{d:C(W),fill:W<q?`url(#segmentGradient-${V})`:(Q=Y)==null?void 0:Q.progressColors.inactive.fill,stroke:(I=Y)==null?void 0:I.progressColors.inactive.stroke,strokeWidth:"0.5",style:{transition:`fill 0.6s cubic-bezier(0.4, 0, 0.2, 1) ${W*.05}s`,filter:W<q?`drop-shadow(0 1px 2px ${(Z=Y)==null?void 0:Z.progressColors.shadow.dropShadow})`:"none"}},W)})]}),n("div",{style:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",zIndex:1},children:p("div",{style:{fontSize:"12px",fontWeight:"700",color:te.end,letterSpacing:"-0.025em"},children:[G,"%"]})}),n("div",{style:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",width:"32px",height:"32px",borderRadius:"50%",zIndex:0}})]})})};var Xt={},fl=rs;Object.defineProperty(Xt,"__esModule",{value:!0});var Ga=Xt.default=void 0,gl=fl(os()),pl=is;Ga=Xt.default=(0,gl.default)((0,pl.jsx)("path",{d:"M14 4v5c0 1.12.37 2.16 1 3H9c.65-.86 1-1.9 1-3V4zm3-2H7c-.55 0-1 .45-1 1s.45 1 1 1h1v5c0 1.66-1.34 3-3 3v2h5.97v7l1 1 1-1v-7H19v-2c-1.66 0-3-1.34-3-3V4h1c.55 0 1-.45 1-1s-.45-1-1-1"}),"PushPinOutlined");const hl=G=>us[G]||G;function Ml({moduleName:G,handleSearch:V=()=>{},disabled:se=!1,onPresetActiveChange:ze=()=>{},onClearPreset:q=()=>{}}){var et,tt,m,u,E,D;const[he,ee]=Mt.useState(null),[g,O]=s.useState(null),[J,C]=s.useState(null),[te,L]=s.useState(!1),[W,Q]=s.useState(!0),{t:I}=Ia(),Z=Oe(c=>c.commonFilter[G]),le=ya(),$e=!!he,N=()=>{O(""),ee(null),ae()};Oe(c=>c.commonFilterUpdate);const y=Oe(c=>{var d;return(d=c==null?void 0:c.userManagement)==null?void 0:d.userData});s.useEffect(()=>{if(W){Q(!1);return}if(te){const c=setTimeout(()=>{V(),L(!1)},50);return()=>clearTimeout(c)}},[Z]);const B=()=>{L(!0);let c=M=>{C(M.reverse());var A=M.filter(h=>h.defaultPin===1);if(A.length>0){Te(A[0].id),ie(A[0].id);let h=JSON.parse(A[0].presetDescription);if(h.createdOn){const P=new Date,b=new Date;b.setDate(b.getDate()-7);try{let T=b,w=P;if(typeof h.createdOn=="string"){if(h.createdOn.includes("[")||h.createdOn.includes('"')){const ce=h.createdOn.replace(/\\"/g,'"').replace(/\\\\/g,"\\");try{const Se=JSON.parse(ce);Array.isArray(Se)&&Se.length===2&&(T=new Date(Se[0]),w=new Date(Se[1]))}catch(Se){console.log("Failed to parse date string:",Se)}}}else Array.isArray(h.createdOn)&&h.createdOn.length===2&&(T=new Date(h.createdOn[0]),w=new Date(h.createdOn[1]));!isNaN(T.getTime())&&!isNaN(w.getTime())?h.createdOn=[T,w]:h.createdOn=[b,P]}catch(T){console.error("Error processing date:",T),h.createdOn=[b,P]}}le(fe({module:G,filterData:h}))}L(!1)},d=()=>{};k(`/${pt}/presetFilter/listOfFilters/${G}/${y==null?void 0:y.emailId}`,"get",c,d)},be=()=>{B()},[we,Me]=s.useState(!1),xe=()=>{Me(!0)},ae=()=>{Me(!1)},Ce=()=>{ge(!0);const c=new FormData;c.append("userId",y==null?void 0:y.emailId),c.append("filterName",g),c.append("module",G),c.append("presetDescription",JSON.stringify(Z));let d=A=>{B(),re(!0),pe(`Preset Filter ${g} Saved successfully`),xe(),ge(!1)},M=A=>{re(!0),Ve("Error"),pe(`Preset Filter ${g} Saving Failed `),Ge("danger"),Pe()};k(`/${pt}/presetFilter/savePresetFilter`,"postformdata",d,M,c)},Ne=c=>{try{Te(c.id);let d=JSON.parse(c.presetDescription);if(d.createdOn){const M=new Date,A=new Date;A.setDate(A.getDate()-7);try{let h=A,P=M;if(typeof d.createdOn=="string"){if(d.createdOn.includes("[")||d.createdOn.includes('"')){const b=d.createdOn.replace(/\\"/g,'"').replace(/\\\\/g,"\\");try{const T=JSON.parse(b);Array.isArray(T)&&T.length===2&&(h=new Date(T[0]),P=new Date(T[1]))}catch(T){console.log("Failed to parse date string:",T)}}}else Array.isArray(d.createdOn)&&d.createdOn.length===2&&(h=new Date(d.createdOn[0]),P=new Date(d.createdOn[1]));!isNaN(h.getTime())&&!isNaN(P.getTime())?d.createdOn=[h,P]:d.createdOn=[A,M]}catch(h){console.error("Error processing date:",h),d.createdOn=[A,M]}}le(fe({module:G,filterData:d}))}catch(d){console.error("Error setting filter preset:",d)}},De=(c,d)=>{const M=new FormData;M.append("filterId",d),M.append("defaultPin",c?0:1),M.append("module",G),M.append("userId",y==null?void 0:y.emailId);let A=P=>B(),h=()=>{};k(`/${pt}/presetFilter/pinFilter`,"putformdata",A,h,M)},Ee=()=>{ge(!0);let c=M=>{C(M.reverse()),ge(!1)},d=()=>{ge(!1)};k(`/${pt}/presetFilter/listOfFilters/${G}/${y==null?void 0:y.emailId}`,"get",c,d)},[Ie,ge]=s.useState(!1),ke=c=>{ge(!0);let d=A=>{Ee()},M=A=>{Ee()};k(`/${pt}/presetFilter/delete/id/${c}`,"delete",d,M)},Le=()=>{Te(null),ie(null),L(!0),le(yt({module:G})),q(),re(!0),pe("Preset Filter Cleared successfully"),xe(),N()};s.useEffect(()=>{be()},[]);const[ye,Ue]=s.useState("SelectPreFilter"),qe=(c,d)=>{Ue(d)},[oe,re]=s.useState(!1),[U,Be]=s.useState(!1),[ve,Ve]=s.useState(""),[H,pe]=s.useState(""),[Re,Ge]=s.useState(""),Pe=()=>{Be(!0)},Ae=()=>{Be(!1),N()},$=c=>{ee(c.currentTarget)},[Ye,ie]=s.useState(null),[z,Te]=s.useState(null);s.useEffect(()=>{ze(!!z)},[z,ze]);const F=c=>{ie(c.id),Te(c.id),Ne(c),L(!0),re(!0),pe("Preset Filter Applied successfully"),xe(),N()};return p("div",{className:"reusable-preset",children:[p(ne,{id:"demo-customized-button","aria-controls":$e?"demo-customized-menu":void 0,"aria-haspopup":"true","aria-expanded":$e?"true":void 0,variant:"outlined",disableElevation:!0,onClick:$,endIcon:n(Ct,{iconName:"KeyboardArrowDown"}),startIcon:z&&n(Ct,{iconName:"Check"}),sx:{position:"relative",...z&&{borderColor:(tt=(et=Y)==null?void 0:et.primary)==null?void 0:tt.main,color:(u=(m=Y)==null?void 0:m.primary)==null?void 0:u.main,fontWeight:"bold"}},children:[I(z?"Preset Active":"Preset Filter"),z&&n(X,{sx:{position:"absolute",top:-5,right:-5,width:10,height:10,borderRadius:"50%",backgroundColor:(D=(E=Y)==null?void 0:E.primary)==null?void 0:D.main}})]}),n(ds,{MenuListProps:{style:{paddingTop:"0px",paddingBottom:"0px",position:"auto"}},id:"demo-customized-menu",anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},anchorEl:he,open:$e,onClose:N,children:p(X,{sx:{width:"500px",overflowY:"hidden"},children:[n(X,{sx:{borderBottom:1,width:"inherit",borderColor:"divider"},children:n(x,{container:!0,children:n(x,{item:!0,md:12,children:p(cs,{value:ye,onChange:qe,children:[n(pa,{label:I("Select Filter"),value:"SelectPreFilter",sx:{fontWeight:"700",fontSize:"14px",textTransform:"none"}}),n(pa,{label:I("Save Filter"),value:"SaveFilter",sx:{fontWeight:"700",fontSize:"14px",textTransform:"none"}})]})})})}),ye==="SelectPreFilter"&&(Ie?p(je,{children:[n(X,{sx:{minHeight:"10.1rem",maxHeight:"24.8vh",overflowY:"scroll"},children:n(lt,{PaperProps:{style:{minHeight:"auto"}},sx:{fontWeight:500,padding:"11px","&:hover":{backgroundColor:"#EAE9FF",color:"#3B30C8"}},children:p(x,{container:!0,children:[n(x,{item:!0,xs:12,children:n(_t,{variant:"text"})}),n(x,{item:!0,xs:12,children:n(_t,{variant:"text"})})]})})}),n(X,{sx:{borderTop:1,width:"inherit",borderColor:"divider"},children:n(lt,{sx:{display:"flex",justifyContent:"right"},children:n(ne,{onClick:N,variant:"outlined",sx:{textTransform:"none",fontWeight:"bold"},style:{height:40,minWidth:"6rem"},children:I("Cancel")})})})]}):p(je,{children:[n(X,{sx:{minHeight:"10.1rem",maxHeight:"24.8vh",overflowY:"scroll"},children:J&&(J==null?void 0:J.map(c=>n(lt,{PaperProps:{style:{minHeight:"auto"}},sx:{fontWeight:500,padding:"11px",color:Ye===c.id?"#3B30C8":"inherit","&:hover":{color:"#3B30C8"}},onClick:()=>F(c),children:p(x,{container:!0,spacing:1,children:[p(x,{item:!0,xs:12,display:"flex",justifyContent:"space-between",alignItems:"center",children:[n(_e,{variant:"subtitle1",sx:{fontWeight:500},children:c.filterName}),p(X,{children:[n(Dt,{onClick:d=>{d.stopPropagation(),De(c.defaultPin,c.id)},size:"small",sx:{padding:0,mr:1},children:n(Ct,{iconName:"PushPin",isSelected:c.defaultPin})}),n(Dt,{onClick:d=>{d.stopPropagation(),ke(c.id)},size:"small",sx:{padding:0},children:n(Ct,{iconName:"Delete",iconColor:"red"})})]})]}),n(x,{item:!0,xs:12,children:(()=>{const d=JSON.parse(c.presetDescription),M=Object.entries(d).filter(([A,h])=>h&&h!==""&&!(typeof h=="object"&&Object.values(h).every(P=>!P)));return M.length>0&&n(Nt,{direction:"row",spacing:.5,flexWrap:"wrap",gap:.5,children:M.map(([A,h],P)=>n(X,{sx:{backgroundColor:"#EAE9FF",borderRadius:"4px",padding:"2px 8px",fontSize:"0.75rem",color:"#3B30C8",display:"inline-flex",alignItems:"center"},children:p(_e,{component:"span",sx:{fontSize:"0.75rem",fontWeight:500},children:[`${hl(A)}: `,n("span",{style:{fontWeight:400},children:(()=>{if(typeof h=="object"){if(A==="createdOn"&&Array.isArray(h)&&h.length===2){const b=Ze(h[0]).format("DD MMM YYYY"),T=Ze(h[1]).format("DD MMM YYYY");return`${b} - ${T}`}return h.code||h.desc||JSON.stringify(h)}if(typeof h=="string"&&h.includes("$^$"))return h.split("$^$").join(", ");if(A==="createdOn"&&typeof h=="string"){const b=/"([\d-]+T[\d:.]+Z)","([\d-]+T[\d:.]+Z)"/,T=h.match(b);if(T&&T.length===3){const w=Ze(T[1]).format("DD MMM YYYY"),ce=Ze(T[2]).format("DD MMM YYYY");return`${w} - ${ce}`}try{const w=h.replace(/\\"/g,'"').replace(/\\\\/g,"\\"),ce=JSON.parse(w);if(Array.isArray(ce)&&ce.length===2){const Se=Ze(ce[0]).format("DD MMM YYYY"),at=Ze(ce[1]).format("DD MMM YYYY");return`${Se} - ${at}`}}catch{}}if(typeof h=="string"&&(h.includes("[")||h.includes('"')))try{const b=h.replace(/\\"/g,'"').replace(/\\\\/g,"\\"),T=JSON.parse(b);if(Array.isArray(T)&&T.length===2&&typeof T[0]=="string"&&T[0].includes("T")){const w=Ze(T[0]).format("DD MMM YYYY"),ce=Ze(T[1]).format("DD MMM YYYY");return`${w} - ${ce}`}return h}catch{return h}return h})()})]})},P))})})()})]})},c.id)))}),p(X,{sx:{borderTop:1,width:"inherit",borderColor:"divider"},children:[p(lt,{sx:{display:"flex",justifyContent:"flex-end",gap:2},children:[n(ne,{onClick:Le,variant:"outlined",color:"error",sx:{textTransform:"none",fontWeight:"bold"},style:{height:40,minWidth:"6rem"},disabled:!z,children:I("Clear Preset")}),n(ne,{onClick:N,variant:"outlined",sx:{textTransform:"none",fontWeight:"bold"},style:{height:40,minWidth:"6rem"},children:I("Cancel")})]}),oe&&n(Rt,{openSnackBar:we,handleSnackbarClose:ae,alertMsg:H}),oe&&n(Wt,{dialogState:U,openReusableDialog:Pe,closeReusableDialog:Ae,dialogTitle:ve,dialogMessage:H,handleDialogConfirm:Ae,dialogOkText:"OK",dialogSeverity:Re})]})]})),ye==="SaveFilter"&&(Ie?p(X,{sx:{minHeight:"13.5rem",maxHeight:"30vh",overflow:"hidden"},children:[p(Nt,{mt:6,ml:6,children:[n(_t,{variant:"text",width:"400px"}),n(_t,{variant:"text",width:"400px"})]}),p(X,{mt:8,mr:2,display:"flex",justifyContent:"flex-end",alignItems:"flex-end",children:[n(ne,{variant:"outlined",sx:{textTransform:"none",fontWeight:"bold"},style:{height:40,marginLeft:"10px",minWidth:"6rem"},children:"Cancel"}),n(ne,{variant:"contained",disabled:se,style:{height:40,marginLeft:"1rem",minWidth:"6rem",textTransform:"none"},children:"Save"})]})]}):p(X,{sx:{minHeight:"13.5rem",maxHeight:"30vh",overflow:"hidden"},children:[oe&&n(Wt,{dialogState:U,openReusableDialog:Pe,closeReusableDialog:Ae,dialogTitle:ve,dialogMessage:H,handleDialogConfirm:Ae,dialogOkText:"OK",dialogSeverity:Re}),oe&&n(Rt,{openSnackBar:we,handleSnackbarClose:ae,alertMsg:H}),p(Nt,{mt:6,ml:6,children:[n(_e,{variant:"subtitle2",fontWeight:"bold",children:I("Add Filter Name")}),p(Nt,{direction:"row",children:[n(It,{id:"outlined-basic",variant:"outlined",sx:{width:"380px"},value:g,onChange:c=>{O(c.target.value)},onKeyDown:c=>{c.stopPropagation()},fullwidth:!0,padding:"none",focused:!1,InputProps:{disableUnderline:!0,sx:{height:"35px"}}}),n(Dt,{sx:{padding:0,marginLeft:"1rem"},children:n(Ga,{sx:{marginTop:"10px"}})})]})]}),p(X,{mt:8,mr:2,display:"flex",justifyContent:"flex-end",alignItems:"flex-end",children:[n(ne,{onClick:c=>{c.stopPropagation(),N()},variant:"outlined",sx:{textTransform:"none",fontWeight:"bold"},style:{height:40,marginLeft:"10px",minWidth:"6rem"},children:"Cancel"}),n(Ht,{title:se?"Please fill mandatory fields":"",arrow:!0,children:n("span",{children:n(ne,{variant:"contained",disabled:se,onClick:Ce,style:{height:40,marginLeft:"1rem",minWidth:"6rem",textTransform:"none"},children:"Save"})})})]})]}))]})})]})}const Dl=s.forwardRef(function(V,se){return n(fs,{direction:"down",ref:se,...V})}),El=({open:G,onClose:V,parameters:se,templateName:ze,allDropDownData:q,onSearch:he,buttonName:ee="Search"})=>{var Te,F,et,tt,m;const[g,O]=s.useState({}),[J,C]=s.useState({}),[te,L]=s.useState({[i.MATERIAL_NUMBER]:[],[i.SALES_ORG]:[],[i.DISTRIBUTION_CHANNEL]:[],[i.PLANT]:[],[i.WAREHOUSE]:[],[i.STORAGE_LOCATION]:[]});s.useState([]);const[W,Q]=s.useState(!1),[I,Z]=s.useState({code:"",desc:""}),[le,$e]=s.useState(null),[N,y]=s.useState({[i.MATERIAL_NUMBER]:!1,[i.SALES_ORG]:!1,[i.DISTRIBUTION_CHANNEL]:!1,[i.PLANT]:!1,[i.WAREHOUSE]:!1,[i.STORAGE_LOCATION]:!1}),B=s.useRef(null),be=Oe(u=>u.request.salesOrgDTData),{fetchOrgData:we}=va(),[Me,xe]=s.useState(null),[ae,Ce]=s.useState(""),[Ne,De]=s.useState(!1),Ee=s.useRef(null),Ie=(u,E)=>{O(D=>({...D,[u]:E})),E.length>0&&C(D=>({...D,[u]:""}))},ge=(u,E)=>{xe(u.currentTarget),Ce(E),De(!0)},ke=()=>{De(!1)},Le=()=>{De(!0)},ye=()=>{De(!1)},Ue=(u,E)=>{O(D=>({...D,[u]:E}))},qe=u=>{var c;const E=(c=u.target.value)==null?void 0:c.toUpperCase();Z({code:E,desc:""}),le&&clearTimeout(le);const D=setTimeout(()=>{var d;(d=g==null?void 0:g[i.MATERIAL_TYPE])!=null&&d.length&&re(E,!0,g==null?void 0:g[i.MATERIAL_TYPE][0])},500);$e(D)},oe=u=>u.code&&u.desc?`${u.code} - ${u.desc}`:u.code||"",re=(u="",E=!1,D)=>{var A,h,P,b;y(T=>({...T,[i.MATERIAL_NUMBER]:!0}));const c={matlType:(D==null?void 0:D.code)??"",materialNo:u??"",top:500,skip:E?0:skip,salesOrg:((h=(A=be==null?void 0:be.uniqueSalesOrgList)==null?void 0:A.map(T=>T.code))==null?void 0:h.join("$^$"))||""},d=T=>{(T==null?void 0:T.statusCode)===He.STATUS_200&&(L(E?w=>({...w,[i.MATERIAL_NUMBER]:T.body}):w=>({...w,[i.MATERIAL_NUMBER]:[...w[i.MATERIAL_NUMBER]||[],...T.body]})),y(w=>({...w,[i.MATERIAL_NUMBER]:!1})))},M=T=>{customError(T),y(w=>({...w,[i.MATERIAL_NUMBER]:!1}))};k(`/${v}${(b=(P=me)==null?void 0:P.DATA)==null?void 0:b.GET_SEARCH_PARAMS_MATERIAL_NO}`,"post",d,M,c)},U=(u,E)=>{y(d=>({...d,[i.SALES_ORG]:!0}));const D=d=>{if((d==null?void 0:d.statusCode)===He.STATUS_200){let M=d.body.length>0?ht(d.body):[];L(A=>({...A,[i.SALES_ORG]:M}))}y(M=>({...M,[i.SALES_ORG]:!1}))},c=()=>{y(d=>({...d,[i.SALES_ORG]:!1}))};k(`/${v}${me.DATA.GET_SALES_ORG_EXTENDED}?materialNo=${u}&region=${E}`,"get",D,c)},Be=(u,E,D)=>{y(M=>({...M,[i.PLANT]:!0}));const c=M=>{if((M==null?void 0:M.statusCode)===He.STATUS_200){let A=M.body.length>0?ht(M.body||[]):[];L(h=>({...h,[i.PLANT]:A}))}y(A=>({...A,[i.PLANT]:!1}))},d=()=>{y(M=>({...M,[i.PLANT]:!1}))};k(`/${v}${me.DATA.GET_PLANT_EXTENDED}?materialNo=${u}&region=${D}&salesOrg=${E}`,"get",c,d)},ve=(u,E)=>{y(d=>({...d,[i.DISTRIBUTION_CHANNEL]:!0}));const D=d=>{if((d==null?void 0:d.statusCode)===He.STATUS_200){let M=d.body.length>0?ht(d.body||[]):[];L(A=>({...A,[i.DISTRIBUTION_CHANNEL]:M}))}y(M=>({...M,[i.DISTRIBUTION_CHANNEL]:!1}))},c=()=>{y(d=>({...d,[i.DISTRIBUTION_CHANNEL]:!1}))};k(`/${v}${me.DATA.GET_DISTR_CHAN_EXTENDED}?materialNo=${u}&salesOrg=${E}`,"get",D,c)},Ve=(u,E,D)=>{y(M=>({...M,[i.WAREHOUSE]:!0}));const c=M=>{if((M==null?void 0:M.statusCode)===He.STATUS_200){let A=M.body.length>0?ht(M.body||[]):[];L(h=>({...h,[i.WAREHOUSE]:A}))}y(A=>({...A,[i.WAREHOUSE]:!1}))},d=()=>{y(M=>({...M,[i.WAREHOUSE]:!1}))};k(`/${v}${me.DATA.GET_WAREHOUSE_EXTENDED}?materialNo=${u}&region=${D}&plant=${E}`,"get",c,d)},H=(u,E,D,c)=>{y(A=>({...A,[i.STORAGE_LOCATION]:!0}));const d=A=>{if((A==null?void 0:A.statusCode)===He.STATUS_200){let h=A.body.length>0?ht(A.body||[]):[];L(P=>({...P,[i.STORAGE_LOCATION]:h}))}y(h=>({...h,[i.STORAGE_LOCATION]:!1}))},M=()=>{y(A=>({...A,[i.STORAGE_LOCATION]:!1}))};k(`/${v}${me.DATA.GET_STOR_LOC_EXTENDED}?materialNo=${u}&region=${D}&plant=${E}&salesOrg=${c}`,"get",d,M)};s.useEffect(()=>{var u,E,D;(E=g[(u=i)==null?void 0:u.REGION])!=null&&E.length&&we(g[(D=i)==null?void 0:D.REGION][0])},[g[(Te=i)==null?void 0:Te.REGION]]),s.useEffect(()=>{var u,E,D;(E=g[(u=i)==null?void 0:u.MATERIAL_TYPE])!=null&&E.length&&re("",!0,g[(D=i)==null?void 0:D.MATERIAL_TYPE][0])},[g[(F=i)==null?void 0:F.MATERIAL_TYPE]]),s.useEffect(()=>{var u,E,D,c,d,M,A,h,P,b,T;if((E=g[(u=i)==null?void 0:u.MATERIAL_NUMBER])!=null&&E.length){U((c=g[(D=i)==null?void 0:D.MATERIAL_NUMBER][0])==null?void 0:c.code,(M=g[(d=i)==null?void 0:d.REGION][0])==null?void 0:M.code);return}(h=g[(A=i)==null?void 0:A.MATERIAL_TYPE])!=null&&h.length&&!((b=g[(P=i)==null?void 0:P.MATERIAL_NUMBER])!=null&&b.length)&&re("",!0,g[(T=i)==null?void 0:T.MATERIAL_TYPE][0])},[g[(et=i)==null?void 0:et.MATERIAL_NUMBER]]),s.useEffect(()=>{var u,E,D,c,d,M,A,h,P,b,T,w;(E=g[(u=i)==null?void 0:u.SALES_ORG])!=null&&E.length&&(Be((c=g[(D=i)==null?void 0:D.MATERIAL_NUMBER][0])==null?void 0:c.code,(M=g[(d=i)==null?void 0:d.SALES_ORG][0])==null?void 0:M.code,(h=g[(A=i)==null?void 0:A.REGION][0])==null?void 0:h.code),ve((b=g[(P=i)==null?void 0:P.MATERIAL_NUMBER][0])==null?void 0:b.code,(w=g[(T=i)==null?void 0:T.SALES_ORG][0])==null?void 0:w.code))},[g[(tt=i)==null?void 0:tt.SALES_ORG]]),s.useEffect(()=>{var u,E,D,c,d,M,A,h,P,b,T,w,ce,Se,at,xt;(E=g[(u=i)==null?void 0:u.PLANT])!=null&&E.length&&(Ve((c=g[(D=i)==null?void 0:D.MATERIAL_NUMBER][0])==null?void 0:c.code,(M=g[(d=i)==null?void 0:d.PLANT][0])==null?void 0:M.code,(h=g[(A=i)==null?void 0:A.REGION][0])==null?void 0:h.code),H((b=g[(P=i)==null?void 0:P.MATERIAL_NUMBER][0])==null?void 0:b.code,(w=g[(T=i)==null?void 0:T.PLANT][0])==null?void 0:w.code,(Se=g[(ce=i)==null?void 0:ce.REGION][0])==null?void 0:Se.code,(xt=g[(at=i)==null?void 0:at.SALES_ORG][0])==null?void 0:xt.code))},[g[(m=i)==null?void 0:m.PLANT]]),s.useEffect(()=>{Object.keys(g).length===0&&L(u=>({...u,[i.MATERIAL_NUMBER]:[],[i.SALES_ORG]:[],[i.DISTRIBUTION_CHANNEL]:[],[i.PLANT]:[],[i.WAREHOUSE]:[],[i.STORAGE_LOCATION]:[]}))},[g]);const pe=s.useRef(),Re=s.useRef(),Ge=s.useRef(),Pe=s.useRef(),Ae=s.useRef();s.useEffect(()=>{var E,D,c;const u=(D=(E=g[i.REGION])==null?void 0:E[0])==null?void 0:D.code;(!((c=g[i.REGION])!=null&&c.length)||u!==pe.current)&&(L(d=>({...d,[i.MATERIAL_TYPE]:[]})),O(d=>({...d,[i.MATERIAL_TYPE]:[]}))),pe.current=u},[g[i.REGION]]),s.useEffect(()=>{var E,D,c;const u=(D=(E=g[i.MATERIAL_TYPE])==null?void 0:E[0])==null?void 0:D.code;(!((c=g[i.MATERIAL_TYPE])!=null&&c.length)||u!==Re.current)&&(L(d=>({...d,[i.MATERIAL_NUMBER]:[]})),O(d=>({...d,[i.MATERIAL_NUMBER]:[]}))),Re.current=u},[g[i.MATERIAL_TYPE]]),s.useEffect(()=>{var E,D,c;const u=(D=(E=g[i.MATERIAL_NUMBER])==null?void 0:E[0])==null?void 0:D.code;(!((c=g[i.MATERIAL_NUMBER])!=null&&c.length)||u!==Ge.current)&&(L(d=>({...d,[i.SALES_ORG]:[]})),O(d=>({...d,[i.SALES_ORG]:[]}))),Ge.current=u},[g[i.MATERIAL_NUMBER]]),s.useEffect(()=>{var E,D,c;const u=(D=(E=g[i.SALES_ORG])==null?void 0:E[0])==null?void 0:D.code;(!((c=g[i.SALES_ORG])!=null&&c.length)||u!==Pe.current)&&(L(d=>({...d,[i.DISTRIBUTION_CHANNEL]:[],[i.PLANT]:[]})),O(d=>({...d,[i.DISTRIBUTION_CHANNEL]:[],[i.PLANT]:[]}))),Pe.current=u},[g[i.SALES_ORG]]),s.useEffect(()=>{var E,D,c;const u=(D=(E=g[i.PLANT])==null?void 0:E[0])==null?void 0:D.code;(!((c=g[i.PLANT])!=null&&c.length)||u!==Ae.current)&&(L(d=>({...d,[i.WAREHOUSE]:[],[i.STORAGE_LOCATION]:[]})),O(d=>({...d,[i.WAREHOUSE]:[],[i.STORAGE_LOCATION]:[]}))),Ae.current=u},[g[i.PLANT]]);const $=u=>{var E,D,c;return(u==null?void 0:u.key)===((E=i)==null?void 0:E.MATERIAL_NUMBER)?n(Yt,{param:u,dropDownData:te,allDropDownData:q,selectedValues:g,inputState:I,handleSelectAll:Ue,handleSelectionChange:Ie,handleMatInputChange:qe,dropdownRef:B,errors:J,formatOptionLabel:oe,handlePopoverOpen:ge,handlePopoverClose:ke,handleMouseEnterPopover:Le,handleMouseLeavePopover:ye,isPopoverVisible:Ne,popoverId:z?"custom-popover":void 0,popoverAnchorEl:Me,popoverRef:Ee,popoverContent:ae,isLoading:N[u.key],singleSelect:u.singleSelect}):(u==null?void 0:u.key)===((D=i)==null?void 0:D.REGION)||(u==null?void 0:u.key)===((c=i)==null?void 0:c.MATERIAL_TYPE)?n(Yt,{param:u,dropDownData:{[u.key]:u.options},allDropDownData:q,selectedValues:g,inputState:I,handleSelectAll:Ue,handleSelectionChange:Ie,dropdownRef:B,errors:J,formatOptionLabel:oe,handlePopoverOpen:ge,handlePopoverClose:ke,handleMouseEnterPopover:Le,handleMouseLeavePopover:ye,isPopoverVisible:Ne,popoverId:z?"custom-popover":void 0,popoverAnchorEl:Me,popoverRef:Ee,popoverContent:ae,isLoading:N[u.key],singleSelect:u.singleSelect}):n(Yt,{param:u,dropDownData:te,allDropDownData:q,selectedValues:g,inputState:I,handleSelectAll:Ue,handleSelectionChange:Ie,dropdownRef:B,errors:J,formatOptionLabel:oe,handlePopoverOpen:ge,handlePopoverClose:ke,handleMouseEnterPopover:Le,handleMouseLeavePopover:ye,isPopoverVisible:Ne,popoverId:z?"custom-popover":void 0,popoverAnchorEl:Me,popoverRef:Ee,popoverContent:ae,isLoading:N[u.key],singleSelect:u.singleSelect})},Ye=()=>{Q(!0),he(g),O({})},ie=()=>Object.values(g).some(u=>Array.isArray(u)&&u.length>0),z=!!Me;return p(je,{children:[p(Ra,{open:G,TransitionComponent:Dl,keepMounted:!0,onClose:V,maxWidth:"lg",fullWidth:!0,children:[p(X,{sx:{backgroundColor:"#e3f2fd",padding:"1rem 1.5rem",display:"flex",alignItems:"center"},children:[n(cl,{color:"primary",sx:{marginRight:"0.5rem"}}),p(_e,{variant:"h6",component:"div",color:"primary",children:[ze," Search Filter(s)"]})]}),n(xa,{sx:{padding:"1.5rem 1.5rem 1rem"},children:n(X,{sx:{display:"grid",gridTemplateColumns:"repeat(3, 1fr)",gap:2},children:se==null?void 0:se.map(u=>n(X,{sx:{marginBottom:"1rem"},children:$(u)},u.key))})}),p(La,{sx:{padding:"0.5rem 1.5rem",display:"flex",justifyContent:"space-between"},children:[n("div",{}),p("div",{style:{display:"flex",gap:"8px"},children:[n(ne,{onClick:()=>{O({})},color:"warning",variant:"outlined",sx:{height:36,minWidth:"3.5rem",textTransform:"none",borderColor:"#cc3300",fontWeight:500},children:"Clear"}),n(ne,{onClick:()=>{O({}),V()},color:"error",variant:"outlined",sx:{height:36,minWidth:"3.5rem",textTransform:"none",borderColor:"#cc3300",fontWeight:500},children:"Cancel"}),n(ne,{onClick:Ye,variant:"contained",disabled:!ie(),sx:{height:36,minWidth:"3.5rem",backgroundColor:"#3B30C8",textTransform:"none",fontWeight:500,"&:hover":{backgroundColor:"#2c278f"}},children:ee})]})]})]}),n(Vt,{blurLoading:!1})]})},Al=({openSearch:G,setOpenSearch:V,onSearchComplete:se})=>{const[ze,q]=s.useState(!1),he=Oe(N=>N.AllDropDown.dropDown),[ee,g]=s.useState(""),[O,J]=s.useState(!1),[C,te]=s.useState(""),[L,W]=s.useState(""),[Q,I]=s.useState(!1),Z=()=>{I(!0)},le=()=>{I(!1)},$e=N=>{var Me,xe,ae,Ce,Ne,De,Ee,Ie,ge,ke,Le,ye,Ue,qe,oe,re,U,Be,ve,Ve,H,pe,Re,Ge,Pe,Ae;g((Me=ps)==null?void 0:Me.REPORT_LOADING),q(!0);let y={orgDetails:[{material:((Ce=(ae=N==null?void 0:N[(xe=i)==null?void 0:xe.MATERIAL_NUMBER])==null?void 0:ae[0])==null?void 0:Ce.code)||"",whseNo:((Ee=(De=N==null?void 0:N[(Ne=i)==null?void 0:Ne.WAREHOUSE])==null?void 0:De[0])==null?void 0:Ee.code)||"",storLoc:((ke=(ge=N==null?void 0:N[(Ie=i)==null?void 0:Ie.STORAGE_LOCATION])==null?void 0:ge[0])==null?void 0:ke.code)||"",salesOrg:((Ue=(ye=N==null?void 0:N[(Le=i)==null?void 0:Le.SALES_ORG])==null?void 0:ye[0])==null?void 0:Ue.code)||"",distrChan:((re=(oe=N==null?void 0:N[(qe=i)==null?void 0:qe.DISTRIBUTION_CHANNEL])==null?void 0:oe[0])==null?void 0:re.code)||"",valArea:((ve=(Be=N==null?void 0:N[(U=i)==null?void 0:U.PLANT])==null?void 0:Be[0])==null?void 0:ve.code)||"",plant:((pe=(H=N==null?void 0:N[(Ve=i)==null?void 0:Ve.PLANT])==null?void 0:H[0])==null?void 0:pe.code)||""}],region:((Pe=(Ge=N==null?void 0:N[(Re=i)==null?void 0:Re.REGION])==null?void 0:Ge[0])==null?void 0:Pe.code)||"",scenario:(Ae=hs)==null?void 0:Ae.EXTEND_WITH_UPLOAD,matlType:"ALL",dtName:"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",version:"v1"};const B=$=>{var z,Te;if(($==null?void 0:$.size)==0){q(!1),g(""),J(!0),te((z=zt)==null?void 0:z.DATA_NOT_FOUND_FOR_SEARCH),W("danger"),Z();return}V(!1);const Ye=URL.createObjectURL($),ie=document.createElement("a");ie.href=Ye,ie.setAttribute("download","SAP Excel Report.xlsx"),document.body.appendChild(ie),ie.click(),document.body.removeChild(ie),URL.revokeObjectURL(Ye),q(!1),g(""),J(!0),te((Te=Ms)==null?void 0:Te.SAP_DOWNLOAD_SUCCESS),W("success"),Z()},be=()=>{var $;q(!1),J(!0),te(($=zt)==null?void 0:$.ERR_DOWNLOADING_EXCEL),W("danger"),Z()},we=`/${v}${me.EXCEL.DOWNLOAD_EXCEL_SAP_REPORT}`;k(we,"postandgetblob",B,be,y)};return p(je,{children:[n(El,{open:G,onClose:()=>V(!1),parameters:gs,onSearch:(N,y,B)=>$e(N),templateName:"Export",allDropDownData:he,buttonName:"Export"}),n(Vt,{blurLoading:ze,loaderMessage:ee}),O&&n(Rt,{openSnackBar:Q,alertMsg:C,alertType:L,handleSnackBarClose:le})]})},Tl=it(sl,{target:"e1abudjx5"})(({theme:G})=>({marginTop:"0px !important",border:`1px solid ${Y.primary.border}`,borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.05)","&:not(:last-child)":{borderBottom:0},"&:before":{display:"none"}}),""),Sl=it(ll,{target:"e1abudjx4"})(({theme:G})=>({minHeight:"2rem !important",margin:"0px !important",backgroundColor:Y.primary.ultraLight,borderRadius:"8px 8px 0 0",transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:`${Y.primary.light}20`}}),""),_l=it(x,{target:"e1abudjx3"})({name:"seull4",styles:"padding:0.75rem;gap:0.5rem"}),Cl=it(x,{target:"e1abudjx2"})({name:"1ikq1ll",styles:"display:flex;justify-content:flex-end;padding-right:0.75rem;padding-bottom:0.75rem;padding-top:0rem;gap:0.5rem"}),Na=it(ne,{target:"e1abudjx1"})({name:"1x9mjbh",styles:"border-radius:4px;padding:4px 12px;text-transform:none;font-size:0.875rem"}),ue=it(_e,{target:"e1abudjx0"})({fontSize:"0.75rem",color:Y.primary.dark,marginBottom:"0.25rem",fontWeight:500},""),Vl=()=>{var ua,fa,ga;const G=Oe(e=>e.AllDropDown.dropDown),V=Oe(e=>e.request.salesOrgDTData),{fetchOrgData:se}=va(),{getDtCall:ze,dtData:q}=wt(),{getDtCall:he,dtData:ee}=wt(),{getDtCall:g,dtData:O}=wt();let J=Oe(e=>{var l;return(l=e.userManagement.entitiesAndActivities)==null?void 0:l.Material});const C=ya(),te=Ds(),L=48,W=8,Q={PaperProps:{style:{maxHeight:L*4.5+W,width:250}}},I=Oe(e=>e.appSettings),[Z,le]=s.useState(!0),[$e,N]=s.useState(!1),[y,B]=s.useState(!1),[be,we]=s.useState(!1),[Me,xe]=s.useState(!1),[ae,Ce]=s.useState({}),[Ne,De]=s.useState([]),[Ee,Ie]=s.useState([]),[ge,ke]=s.useState(""),[Le,ye]=s.useState(null),[Ue,qe]=s.useState([]),[oe,re]=s.useState([]),[U,Be]=s.useState(""),[ve,Ve]=s.useState([]),[H,pe]=s.useState([]),[Re,Ge]=s.useState([]),[Pe,Ae]=s.useState(!1),[$,Ye]=s.useState([]),[ie,z]=s.useState([]),[Te,F]=s.useState(!1),[et,tt]=s.useState(null),[m,u]=s.useState([]),[E,D]=s.useState([...m]),[c,d]=s.useState((ua=Es)==null?void 0:ua.TOP_SKIP);Mt.useState(""),s.useState(!1),s.useState(""),s.useState(""),s.useState(!0);const[M,A]=s.useState(!1),[h,P]=s.useState(!0);s.useState([]);const[b,T]=s.useState([]),[w,ce]=s.useState([]),[Se,at]=s.useState([]);s.useState([]),s.useState([]);const[xt,ct]=s.useState(!0),[Nl,Pa]=s.useState([]),[Il,Oa]=s.useState([]);s.useState(!1);const[Et,Lt]=s.useState([]),[yl,$a]=s.useState([]),[Rl,ba]=s.useState({}),[wa,ka]=s.useState([]),[Ua,Ba]=s.useState([]);s.useState(!1),s.useState([]);const[dt,Ya]=s.useState([]),[Wa,Ha]=s.useState([]),[vt,za]=s.useState([]);s.useState([]),s.useState(!1);const[Va,Xa]=s.useState("success"),[qa,qt]=s.useState(!1),[Ja,Gt]=s.useState(!1),[Jt,Kt]=s.useState(""),[xl,Ka]=s.useState(!1),[Qa,Qt]=s.useState(!1),ut=Mt.useRef(null),[Za,Fa]=s.useState(0),[ma,Zt]=s.useState(!1),At=Mt.useRef(null),[ja,en]=s.useState(0),tn=["Create Multiple","Upload Template","Download Template"],an=["Change Multiple","Upload Template","Download Template"],[nn,Ll]=s.useState(""),[sn,Pt]=s.useState(!1),[ln,Ot]=s.useState(!1),{t:_}=Ia(),[$t,on]=s.useState(),rn=e=>{Pt(!1)};Oe(e=>e.applicationConfig);const cn=()=>{qt(!0)},dn=()=>{qt(!1)},t=Oe(e=>e.commonFilter.MaterialMaster),un=()=>{Fn(!1)},fn=(e,l)=>{l!==0&&(Fa(l),Qt(!1),l===1?vn():l===2&&_n())},gn=e=>{ut.current&&ut.current.contains(e.target)||Qt(!1)},pn=e=>{const l=new FormData;[...e].forEach(a=>l.append("files",a)),l.append("dtName","MDG_MAT_FIELD_CONFIG"),l.append("version","v1");var r=`/${v}/massAction/getAllMaterialsFromExcel`;k(r,"postformdata",a=>{var R;C(ol((R=a==null?void 0:a.body)==null?void 0:R.tableData)),te("/masterDataCockpit/materialMaster/massMaterialTable",{state:Jt}),a.statusCode===200&&(Je("Create"),Xe(`${e.name} has been Uploaded Succesfully`),Ke("success"),P(!1),Ka(!0),A(!0),te("/masterDataCockpit/materialMaster/massMaterialTable",{state:Jt})),un()},a=>{console.log(a)},l)},hn=(e,l)=>{l!==0&&(en(l),Zt(!1),l===1?Gn():l===2&&Sn())},Mn=e=>{At.current&&At.current.contains(e.target)||Zt(!1)},Ft=(e="")=>{var f;F(!0);let l={materialNo:e,salesOrg:((f=V==null?void 0:V.uniqueSalesOrgList)==null?void 0:f.map(a=>a.code).join("$^$"))||"",top:200,skip:0};const r=a=>{F(!1),qe(a.body)},o=a=>{console.log(a)};k(`/${v}/data/getSearchParamsMaterialNo`,"post",r,o,l)},Dn=e=>{const l=e.target.value;if(ke(l),Le&&clearTimeout(Le),l.length>=4){const r=setTimeout(()=>{Ft(l)},500);ye(r)}},En=e=>{if(e.target.value!==null){var l=e.target.value;let r={...t,description:l};C(fe({module:"MaterialMaster",filterData:r}))}},An=(e,l)=>{if(e.target.value!==null){var r=e.target.value;let o={...t,createdBy:r};C(fe({module:"MaterialMaster",filterData:o}))}},Tn=(e,l)=>{if(e.target.value!==null){var r=e.target.value;let o={...t,oldMaterialNumber:r};C(fe({module:"MaterialMaster",filterData:o}))}},Sn=()=>{var l={materialNos:nn.map(f=>f.materialNumber??""),dtName:"MDG_MAT_FIELD_CONFIG",version:"v2"};let r=f=>{le(!1);const a=URL.createObjectURL(f),R=document.createElement("a");R.href=a,R.setAttribute("download","Material_Mass Change.xls"),document.body.appendChild(R),R.click(),document.body.removeChild(R),URL.revokeObjectURL(a),Qe(),Je("Success"),Xe("Material_Mass Change.xls has been downloaded successfully"),Ke("success")},o=f=>{f.message&&(Qe(),Je("Error"),Xe(`${f.message}`),Ke("danger"))};k(`/${v}/excel/downloadExcelWithData`,"postandgetblob",r,o,l)},_n=async()=>{const e=new URLSearchParams({dtName:"MDG_MAT_FIELD_CONFIG",version:"v2"});let l=o=>{const f=URL.createObjectURL(o),a=document.createElement("a");a.href=f,a.setAttribute("download",`${name}`),document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(f),Qe(),Je("Success"),Xe("Material_Mass Create.xls has been downloaded successfully"),Ke("success")},r=o=>{o.message&&(Qe(),Je("Error"),Xe(`${o.message}`),Ke("danger"))};k(`/${v}/excel/downloadExcel?${e.toString()}`,"getblobfile",l,r)};let mt={"Basic Material":`/${v}/data/getBasicMatl`,"Product Hierarchy":`/${v}/data/getProdHier`,"Purchasing Group":`/${v}/data/getPurGroup`,"Lab/Office":`/${v}/data/getDsnOffice`,"Transportation Group":`/${v}/data/getTransGrp`,"Material Group 5":`/${v}/data/getMatlGrp5`,"Profit Center":`/${v}/data/getProfitCenterBasedOnPlant`,"MRP Controller":`/${v}/data/getMRPController`,"Warehouse No":`/${v}/data/getWareHouseNo`,"MRP Profile":`/${v}/data/getMRPProfile`};const jt=(e,l)=>{let r={plant:(H==null?void 0:H.map(a=>a.code).join("$^$"))||""};we(!0);const o=a=>{we(!1);const R=a.body;Ya(K=>({...K,[l]:R}))},f=a=>{we(!1)};l==="Profit Center"?k(e,"post",o,f,r):k(e,"get",o,f)},Cn=e=>{var l,r;((l=ae[e])==null?void 0:l.length)===((r=dt[e])==null?void 0:r.length)?Ce(o=>({...o,[e]:[]})):Ce(o=>({...o,[e]:dt[e]??[]}))},Nn=e=>{const l=e.target.value;Lt(l),$a([]),l.forEach(async r=>{const o=mt[r];jt(o,r)})},In=()=>{F(!0);const e=r=>{F(!1),ka(r.body)},l=r=>{F(!1)};k(`/${v}/data/getMatlType`,"get",e,l)},yn=()=>{F(!0);const e=r=>{Ba(r.body),F(!1)},l=r=>{F(!1),console.log(r)};k(`/${v}/data/getMatlGroup`,"get",e,l)},Rn=()=>{F(!0);const e=r=>{F(!1),ce(r.body)},l=r=>{F(!1),console.log(r)};k(`/${v}/data/getSalesOrg`,"get",e,l)},xn=e=>{F(!0);let l={salesOrg:e?e.map(f=>f==null?void 0:f.code).join("$^$"):""};const r=f=>{F(!1),at(f.body)},o=f=>{console.log(f)};k(`/${v}/data/getDistrChan`,"post",r,o,l)},Ln=()=>{const e=r=>{C(ha({keyName:"VolumeUnit",data:r.body}))},l=r=>{console.log(r)};k(`/${v}/data/getVolumeUnit`,"get",e,l)};s.useState([]);const vn=()=>{Gt(!0),Kt("Create")},Gn=()=>{Gt(!0),Kt("Change")};s.useEffect(()=>{Me&&(ft(),xe(!1))},[Me]),s.useEffect(()=>{Ln(),C(As())},[]);const Pn=()=>{C(Js()),C(Ks()),C(Qs()),C(Zs()),C(Fs()),C(ms([])),C(js({})),C(el({}))};s.useEffect(()=>(yn(),In(),Rn(),Pn(),jt([mt]),()=>{C(yt({module:"MaterialMaster",days:7}))}),[]),s.useEffect(()=>D([...m]),[m]),s.useEffect(()=>{var e=Ee.map(r=>r==null?void 0:r.code).join("$^$");let l={...t,type:e};C(fe({module:"MaterialMaster",filterData:l}))},[Ee]),s.useEffect(()=>{var e=ie.map(r=>r==null?void 0:r.code).join("$^$");let l={...t,distributionChannel:e};C(fe({module:"MaterialMaster",filterData:l}))},[ie]),s.useEffect(()=>{var e=Ne.map(r=>r==null?void 0:r.code).join("$^$");let l={...t,PurStatus:e};C(fe({module:"MaterialMaster",filterData:l}))},[Ne]),s.useEffect(()=>{var e=oe.map(r=>r==null?void 0:r.code).join("$^$");let l={...t,number:e};C(fe({module:"MaterialMaster",filterData:l}))},[oe]),s.useEffect(()=>{var e=Re.map(r=>r==null?void 0:r.code).join("$^$");let l={...t,division:e};C(fe({module:"MaterialMaster",filterData:l}))},[Re]),s.useEffect(()=>{Object.keys(ae).forEach(e=>{var o;const l=(o=ae[e])==null?void 0:o.map(f=>f==null?void 0:f.code).join("$^$");let r={...t,[e]:l};C(fe({module:"MaterialMaster",filterData:r}))})},[ae]),s.useEffect(()=>{var e=$.map(r=>r==null?void 0:r.code).join("$^$");let l={...t,salesOrg:e};C(fe({module:"MaterialMaster",filterData:l})),On(),Ft()},[$]);const On=()=>{const e=Ts($,V);T(e)};s.useEffect(()=>{var e=ve.map(r=>r==null?void 0:r.code).join("$^$");let l={...t,group:e};C(fe({module:"MaterialMaster",filterData:l}))},[ve]),s.useEffect(()=>{var e=H.map(r=>r==null?void 0:r.code).join("$^$");let l={...t,plant:e};C(fe({module:"MaterialMaster",filterData:l}))},[H]);const $n=e=>{if(!e){St(aa),nt(0),D([...m]);return}const l=m.filter(r=>{var a;let o=!1,f=Object.keys(r);for(let R=0;R<f.length&&(o=r[f[R]]?(r==null?void 0:r[f==null?void 0:f[R]])&&((a=r==null?void 0:r[f==null?void 0:f[R]].toString().toLowerCase())==null?void 0:a.indexOf(e==null?void 0:e.toLowerCase()))!=-1:!1,!o);R++);return o});D([...l]),St(l==null?void 0:l.length)},ea=new Date,Tt=new Date;Tt.setDate(Tt.getDate()-7),s.useState([Tt,ea]),s.useState([Tt,ea]);const bn=e=>{var l=e;C(fe({module:"MaterialMaster",filterData:{...t,createdOn:l}}))},ft=()=>{nt(0),B(!0),Ot(!1);let e={fromDate:j(t==null?void 0:t.createdOn[0]).format("YYYYMMDD")??"",toDate:j(t==null?void 0:t.createdOn[1]).format("YYYYMMDD")??"",createdBy:(t==null?void 0:t.createdBy)??"",materialDesc:(t==null?void 0:t.description)??"",plant:(t==null?void 0:t.plant)??"",materialGroup:(t==null?void 0:t.group)??"",materialType:(t==null?void 0:t.type)??"",changedBy:(t==null?void 0:t.changedBy)??"",taskId:(t==null?void 0:t.taskId)??"",status:(t==null?void 0:t.status)??"",salesOrg:(t==null?void 0:t.salesOrg)??"",division:(t==null?void 0:t.division)??"",distributionChannel:(t==null?void 0:t.distributionChannel)??"",storageLocation:(t==null?void 0:t.storageLocation)??"",ProdHier:(t==null?void 0:t["Product Hierarchy"])??"",BasicMatl:(t==null?void 0:t["Basic Material"])??"",ProfitCtr:(t==null?void 0:t["Profit Center"])??"",PurGroup:(t==null?void 0:t["Purchasing Group"])??"",MatlGrp5:(t==null?void 0:t["Material Group 5"])??"",MrpCtrler:(t==null?void 0:t["MRP Controller"])??"",warehouseNo:(t==null?void 0:t["Warehouse No"])??"",Mrpprofile:(t==null?void 0:t["MRP Profile"])??"",oldMaterialNo:(t==null?void 0:t.oldMaterialNumber)??"",number:(t==null?void 0:t.number)??"",PurStatus:(t==null?void 0:t.PurStatus)??"",top:c,skip:0,labOffice:(t==null?void 0:t["Lab/Office"])??"",transportationGroup:(t==null?void 0:t["Transportation Group"])??"",batchManagement:(t==null?void 0:t.batchManagement)??""};const l=o=>{var K;if((o==null?void 0:o.statusCode)===He.STATUS_200){var f=[];for(let S=0;S<((K=o==null?void 0:o.body)==null?void 0:K.length);S++){var a=o==null?void 0:o.body[S],R={id:Bt(),Number:a.Number,materialType:a.Materialtype!==""?`${a.Materialtype} - ${a.MaterialTypeDesc}`:"Not Available",materialDesc:a.MaterialDescrption!==""?`${a.MaterialDescrption}`:"Not Available",materialGroup:a.MaterialGroup!==""?`${a.MaterialGroup} - ${a.materialGroupDesc}`:"-",XplantMatStatus:a.XplantMatStatus!==""?`${a.XplantMatStatus} ${a.XplantMatStatusDesc?"-"+a.XplantMatStatusDesc:""}`:"-",Plant:a.Plant.length>0?`${a.Plant}`:"-",WarehouseNo:a.WarehouseNo.length>0?`${a.WarehouseNo}`:"-",createdOn:j(a.CreatedOn).format(I==null?void 0:I.dateFormat),changedOn:j(a.LastChange).format(I==null?void 0:I.dateFormat),changedBy:a.ChangedBy,createdBy:a.CreatedBy,Division:a.Division!==""?`${a.Division} ${a.DivisionDesc?"-"+a.DivisionDesc:""}`:"Not Available",StorageLocation:a.StorageLocation.length>0?`${a.StorageLocation} `:"-",oldMaterialNumber:a.OldMaterialNumber!==""?`${a.OldMaterialNumber} - ${a.OldMaterialNumberName}`:"Not Available",labOffice:a.LabOffice!==""?`${a.LabOffice} - ${a.LabOfficeName}`:"Not Available",transportationGroup:a.TrnsportGroup!==""?`${a.TrnsportGroup} - ${a.TrnsportGroupName}`:"Not Available",SalesOrg:a.SalesOrg.length>0?`${a.SalesOrg}`:"-",DistChnl:a.DistChnl.length>0?`${a.DistChnl}`:"-",indSector:a.Industrysector!==""?a.Industrysector:"-",PrimaryVendor:a.PryVendor!==""?a.PryVendor:"-"};f.push(R)}f.sort((S,st)=>j(S.createdOn,"DD MMM YYYY HH:mm")-j(st.createdOn,"DD MMM YYYY HH:mm")),u(f.reverse()),B(!1),St(o.count),na(o.count),C(_a({module:"MaterialMgmt"}))}else(o==null?void 0:o.statusCode)===He.STATUS_414&&(Ca(o==null?void 0:o.message,"error"),B(!1))},r=o=>{console.log(o)};k(`/${v}/data/getMaterialBasedOnAdditionalParams`,"post",l,r,e)},wn=()=>{B(!0);let e={fromDate:j(t==null?void 0:t.createdOn[0]).format("YYYYMMDD")??"",toDate:j(t==null?void 0:t.createdOn[1]).format("YYYYMMDD")??"",createdBy:(t==null?void 0:t.createdBy)??"",materialDesc:(t==null?void 0:t.description)??"",plant:(t==null?void 0:t.plant)??"",materialGroup:(t==null?void 0:t.group)??"",materialType:(t==null?void 0:t.type)??"",changedBy:(t==null?void 0:t.changedBy)??"",taskId:(t==null?void 0:t.taskId)??"",status:(t==null?void 0:t.status)??"",salesOrg:(t==null?void 0:t.salesOrg)??"",division:(t==null?void 0:t.division)??"",distributionChannel:(t==null?void 0:t.distributionChannel)??"",storageLocation:(t==null?void 0:t.storageLocation)??"",ProdHier:(t==null?void 0:t["Product Hierarchy"])??"",BasicMatl:(t==null?void 0:t["Basic Material"])??"",ProfitCtr:(t==null?void 0:t["Profit Center"])??"",PurGroup:(t==null?void 0:t["Purchasing Group"])??"",MatlGrp5:(t==null?void 0:t["Material Group 5"])??"",MrpCtrler:(t==null?void 0:t["MRP Controller"])??"",warehouseNo:(t==null?void 0:t["Warehouse No"])??"",Mrpprofile:(t==null?void 0:t["MRP Profile"])??"",oldMaterialNo:(t==null?void 0:t.oldMaterialNumber)??"",number:(t==null?void 0:t.number)??"",PurStatus:(t==null?void 0:t.PurStatus)??"",top:c,skip:c*gt,fetchCount:!1,labOffice:(t==null?void 0:t["Lab/Office"])??"",transportationGroup:(t==null?void 0:t["Transportation Group"])??"",batchManagement:(t==null?void 0:t.batchManagement)??""};const l=o=>{var K;B(!1);var f=[];for(let S=0;S<((K=o==null?void 0:o.body)==null?void 0:K.length);S++){var a=o==null?void 0:o.body[S],R={id:Bt(),Number:a.Number,materialType:a.Materialtype!==""?`${a.Materialtype} - ${a.MaterialTypeDesc}`:"Not Available",materialDesc:a.MaterialDescrption!==""?`${a.MaterialDescrption}`:"Not Available",materialGroup:a.MaterialGroup!==""?`${a.MaterialGroup} - ${a.materialGroupDesc}`:"-",XplantMatStatus:a.XplantMatStatus!==""?`${a.XplantMatStatus} ${a.XplantMatStatusDesc?"-"+a.XplantMatStatusDesc:""}`:"-",Plant:a.Plant.length>0?`${a.Plant}`:"Not Available",WarehouseNo:a.WarehouseNo.length>0?`${a.WarehouseNo}`:"-",createdOn:j(a.CreatedOn).format(I==null?void 0:I.dateFormat),changedOn:j(a.LastChange).format(I==null?void 0:I.dateFormat),changedBy:a.ChangedBy,createdBy:a.CreatedBy,division:a.Division!==""?`${a.Division}- ${a.DivisionDesc} `:"Not Available",storageLocation:a.StorageLocation.length>0?`${a.StorageLocation} `:"-",oldMaterialNumber:a.OldMaterialNumber!==""?`${a.OldMaterialNumber} - ${a.OldMaterialNumberName}`:"Not Available",labOffice:a.LabOffice!==""?`${a.LabOffice} - ${a.LabOfficeName}`:"Not Available",transportationGroup:a.TrnsportGroup!==""?`${a.TrnsportGroup} - ${a.TrnsportGroupName}`:"Not Available",SalesOrg:a.SalesOrg.length>0?`${a.SalesOrg}`:"-",DistChnl:a.DistChnl.length>0?`${a.DistChnl}`:"-",indSector:a.Industrysector!==""?a.Industrysector:"-",PrimaryVendor:a.PryVendor!==""?a.PryVendor:"-"};f.push(R)}f.sort((S,st)=>j(S.createdOn,"DD MMM YYYY HH:mm")-j(st.createdOn,"DD MMM YYYY HH:mm")),u(S=>[...S,...f]),B(!1)},r=o=>{B(!1),console.log(o)};k(`/${v}/data/getMaterialBasedOnAdditionalParams`,"post",l,r,e)};s.useState([]),s.useState([]),s.useState(null),s.useState(null);const[ta,St]=s.useState(0),[aa,na]=s.useState(0);s.useState(!1);const[vl,kn]=s.useState(!1),[Un,Bn]=s.useState(!1);s.useState(!1),s.useState(!1);const[Gl,Yn]=s.useState(!1),[Pl,Wn]=s.useState(!1);s.useState(!1),s.useState(""),s.useState("");const[Hn,sa]=s.useState(!1),[zn,Je]=s.useState(""),[la,Xe]=s.useState(""),[Vn,Ke]=s.useState(""),bt=()=>{Bn(!1)},Xn=()=>{Ae(!0),k(`/${v}/${me.MASS_ACTION.AGGREGATE_DAILY_REQUESTS}`,"post",e=>{Ae(!1)},e=>{},{})},Qe=()=>{sa(!0)},oa=()=>{sa(!1),Wn(!1),kn(!1),Yn(!1)};s.useState(null),s.useState(null),s.useState(null);const[ra,qn]=s.useState(!1),ia=()=>{pe([]),Lt([]),re([]),Be(""),De([]),z([]),Ve([]),Ye([]),Ce({}),Ie([]),Ge([]),C(yt({module:"MaterialMaster"})),ba(e=>{const l={...e};return Object.keys(l).forEach(r=>{l[r]={code:"",desc:""}}),l}),xe(!0)},Jn=e=>{const l=e.map(S=>m.find(st=>st.id===S));var r=l.map(S=>S.company),o=new Set(r),f=l.map(S=>S.vendor),a=new Set(f),R=l.map(S=>S.paymentTerm),K=new Set(R);l.length>0?o.size===1?a.size===1?K.size!==1?(ct(!0),Je("Error"),Xe("Invoice cannot be generated for vendors with different payment terms"),Ke("danger"),Qe()):ct(!1):(ct(!0),Je("Error"),Xe("Invoice cannot be generated for multiple suppliers"),Ke("danger"),Qe()):(ct(!0),Je("Error"),Xe("Invoice cannot be generated for multiple companies"),Ke("danger"),Qe()):ct(!0),Pa(e),Oa(l)},[gt,nt]=s.useState(0),Kn=e=>{const l=e.target.value;d(l),nt(0)},Qn=(e,l)=>{nt(isNaN(l)?0:l)};s.useEffect(()=>{ln||gt!=0&&gt*c>=(m==null?void 0:m.length)&&wn()},[gt,c]);function Zn(){ft()}s.useState([]),s.useState([]);const[Ol,Fn]=s.useState(!1);s.useState(null),s.useState(null),s.useState([]),s.useState(null),s.useState("");const ot=(e,l)=>({field:e,headerName:_(l),editable:!1,flex:1,renderCell:r=>{const o=r.value?r.value.split(",").map(R=>R.trim()):[],f=o.length-1;if(o.length===0)return"-";const a=R=>{const[K,...S]=R.split("-");return p(je,{children:[n("strong",{children:K}),S.length?` - ${S.join("-")}`:""]})};return p(X,{sx:{display:"flex",alignItems:"center",width:"100%",minWidth:0},children:[n(Ht,{title:o[0],placement:"top",arrow:!0,children:n(_e,{variant:"body2",sx:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",flex:1,minWidth:0},children:a(o[0])})}),f>0&&n(X,{sx:{display:"flex",alignItems:"center",ml:1,flexShrink:0},children:n(Ht,{arrow:!0,placement:"right",title:p(X,{sx:{p:1,maxHeight:200,overflowY:"auto"},children:[p(_e,{variant:"subtitle2",sx:{fontWeight:600,mb:1},children:["Additional ",l,"s (",f,")"]}),o.slice(1).map((R,K)=>n(_e,{variant:"body2",sx:{mb:.5},children:a(R)},K))]}),children:p(X,{sx:{display:"flex",alignItems:"center",cursor:"pointer"},children:[n(tl,{sx:{fontSize:"1rem",color:"primary.main","&:hover":{color:"primary.dark"}}}),p(_e,{variant:"caption",sx:{ml:.5,color:"primary.main",fontSize:"11px"},children:["+",f]})]})})})]})}}),mn=()=>({field:"dataValidation",headerName:_("Data Validation"),editable:!1,flex:1,renderCell:e=>{const l=e.value;let r;return l!=null?r=l:r=[100,75,50,25][e.api.getRowIndexRelativeToVisibleRows(e.id)%4],n(ul,{percentage:r,id:e.id})}}),jn=(e,l)=>({field:e,headerName:_(l),editable:!1,flex:1,renderCell:r=>{var a;const[o,...f]=((a=r.value)==null?void 0:a.split(" - "))||[];return p("span",{style:{flex:1,wordBreak:"break-word",whiteSpace:"normal"},children:[n("strong",{children:o})," ",f.length?`- ${f.join(" - ")}`:""]})}}),ca=e=>{let l={decisionTableId:null,decisionTableName:kt.MDG_MAT_SEARCHSCREEN_COLUMN,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":(e==null?void 0:e.toUpperCase())||"US","MDG_CONDITIONS.MDG_MODULE":"Material","MDG_CONDITIONS.MDG_MAT_SEARCHTYPE":"Master Data"}]};he(l)},es=()=>{let e={decisionTableId:null,decisionTableName:kt.MDG_MAT_SEARCHSCREEN_PARAMETER,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":"US","MDG_CONDITIONS.MDG_MODULE":"Material","MDG_CONDITIONS.MDG_MAT_SEARCHTYPE":"Master Data"}]};g(e)};_("Material Number"),_("Material Type"),_("Material Group"),_("X-Plant Material Status"),_("Division"),ot("plant","Plant"),ot("storageLocation","Storage Location"),ot("salesOrg","Sales Org"),ot("distributionChannel","Distribution Channel"),ot("warehouseNo","Warehouse No"),_("Primary Vendor");const ts=e=>{const l=[];let r=(e==null?void 0:e.sort((o,f)=>o.MDG_MAT_SEQUENCE_NO-f.MDG_MAT_SEQUENCE_NO))||[];return r&&(r==null||r.forEach(o=>{if((o==null?void 0:o.MDG_MAT_VISIBILITY)===al.DISPLAY&&o!=null&&o.MDG_MAT_UI_FIELD_NAME){const f=o.MDG_MAT_JSON_FIELD_NAME,a=o.MDG_MAT_UI_FIELD_NAME;f==="DataValidation"?l.push(mn()):o.MDG_MAT_FIELD_TYPE==="Multiple"?l.push(ot(f,a)):o.MDG_MAT_FIELD_TYPE==="Single"&&l.push(jn(f,a))}})),l};s.useEffect(()=>{var e,l,r,o;if(ee){const f=ts((l=(e=ee==null?void 0:ee.result)==null?void 0:e[0])==null?void 0:l.MDG_MAT_SEARCHSCREEN_COLUMN_ACTION_TYPE);Ha(f)}if(O){const f=(o=(r=O==null?void 0:O.result)==null?void 0:r[0])==null?void 0:o.MDG_MAT_SEARCHSCREEN_PARAMETER_ACTION_TYPE,a=f==null?void 0:f.filter(R=>R.MDG_MAT_FILTER_TYPE==="Additional").map(R=>({title:_(R.MDG_MAT_UI_FIELD_NAME)}));za(f),on(a)}},[ee,O]),s.useEffect(()=>{ft()},[c]),s.useEffect(()=>{var e;if(U){se(U),da(U);const l=(e=U==null?void 0:U.code)==null?void 0:e.toUpperCase();(l==="US"||l==="EUR")&&ca(l)}},[U]),s.useEffect(()=>(ca("US"),es(),C(yt({module:"DuplicateDesc"})),C(Ss()),C(_s()),C(Cs({})),()=>{C(Ns())}),[]);let as=s.useRef(null);const da=e=>{let l={decisionTableId:null,decisionTableName:kt.MDG_MAT_REGION_DIVISION_MAPPING,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":(e==null?void 0:e.code)||""}]};ze(l)};s.useEffect(()=>{var e,l;if(q){const r=Is((l=(e=q==null?void 0:q.result)==null?void 0:e[0])==null?void 0:l.MDG_MAT_REGION_DIVISION_MAPPING);C(ha({keyName:"Division",data:r}))}},[q]),s.useEffect(()=>{U&&(se(U),da(U))},[U]);const ns=()=>{Ot(!0),ls()},ss=()=>{Ot(!0),nt(0)},ls=()=>{nt(0),B(!0);let e={fromDate:j(t==null?void 0:t.createdOn[0]).format("YYYYMMDD")??"",toDate:j(t==null?void 0:t.createdOn[1]).format("YYYYMMDD")??"",createdBy:(t==null?void 0:t.createdBy)??"",materialDesc:(t==null?void 0:t.description)??"",plant:(t==null?void 0:t.plant)??"",materialGroup:(t==null?void 0:t.group)??"",materialType:(t==null?void 0:t.type)??"",changedBy:(t==null?void 0:t.changedBy)??"",taskId:(t==null?void 0:t.taskId)??"",status:(t==null?void 0:t.status)??"",salesOrg:(t==null?void 0:t.salesOrg)??"",division:(t==null?void 0:t.division)??"",distributionChannel:(t==null?void 0:t.distributionChannel)??"",storageLocation:(t==null?void 0:t.storageLocation)??"",ProdHier:(t==null?void 0:t["Product Hierarchy"])??"",BasicMatl:(t==null?void 0:t["Basic Material"])??"",ProfitCtr:(t==null?void 0:t["Profit Center"])??"",PurGroup:(t==null?void 0:t["Purchasing Group"])??"",MatlGrp5:(t==null?void 0:t["Material Group 5"])??"",MrpCtrler:(t==null?void 0:t["MRP Controller"])??"",warehouseNo:(t==null?void 0:t["Warehouse No"])??"",Mrpprofile:(t==null?void 0:t["MRP Profile"])??"",oldMaterialNo:(t==null?void 0:t.oldMaterialNumber)??"",number:(t==null?void 0:t.number)??"",PurStatus:(t==null?void 0:t.PurStatus)??"",top:aa,skip:0,labOffice:(t==null?void 0:t["Lab/Office"])??"",transportationGroup:(t==null?void 0:t["Transportation Group"])??"",batchManagement:(t==null?void 0:t.batchManagement)??""};const l=o=>{var K;if((o==null?void 0:o.statusCode)===He.STATUS_200){var f=[];for(let S=0;S<((K=o==null?void 0:o.body)==null?void 0:K.length);S++){var a=o==null?void 0:o.body[S],R={id:Bt(),Number:a.Number,materialType:a.Materialtype!==""?`${a.Materialtype} - ${a.MaterialTypeDesc}`:"Not Available",materialDesc:a.MaterialDescrption!==""?`${a.MaterialDescrption}`:"Not Available",materialGroup:a.MaterialGroup!==""?`${a.MaterialGroup} - ${a.materialGroupDesc}`:"-",XplantMatStatus:a.XplantMatStatus!==""?`${a.XplantMatStatus} ${a.XplantMatStatusDesc?"-"+a.XplantMatStatusDesc:""}`:"-",Plant:a.Plant.length>0?`${a.Plant}`:"-",WarehouseNo:a.WarehouseNo.length>0?`${a.WarehouseNo}`:"-",createdOn:j(a.CreatedOn).format(I==null?void 0:I.dateFormat),changedOn:j(a.LastChange).format(I==null?void 0:I.dateFormat),changedBy:a.ChangedBy,createdBy:a.CreatedBy,division:a.Division!==""?`${a.Division}- ${a.DivisionDesc} `:"Not Available",StorageLocation:a.StorageLocation.length>0?`${a.StorageLocation} `:"-",oldMaterialNumber:a.OldMaterialNumber!==""?`${a.OldMaterialNumber} - ${a.OldMaterialNumberName}`:"Not Available",labOffice:a.LabOffice!==""?`${a.LabOffice} - ${a.LabOfficeName}`:"Not Available",transportationGroup:a.TrnsportGroup!==""?`${a.TrnsportGroup} - ${a.TrnsportGroupName}`:"Not Available",SalesOrg:a.SalesOrg.length>0?`${a.SalesOrg}`:"-",DistChnl:a.DistChnl.length>0?`${a.DistChnl}`:"-",indSector:a.Industrysector!==""?a.Industrysector:"-",PrimaryVendor:a.PryVendor!==""?a.PryVendor:"-"};f.push(R)}f.sort((S,st)=>j(S.createdOn,"DD MMM YYYY HH:mm")-j(st.createdOn,"DD MMM YYYY HH:mm")),u(f.reverse()),B(!1),nt(Math.floor((f==null?void 0:f.length)/c)),St(o.count),na(o.count),C(_a({module:"MaterialMgmt"}))}else(o==null?void 0:o.statusCode)===He.STATUS_414&&(Ca(o==null?void 0:o.message,"error"),B(!1))},r=o=>{console.log(o)};k(`/${v}/data/getMaterialBasedOnAdditionalParams`,"post",l,r,e)};return p("div",{ref:as,children:[n(Wt,{dialogState:Hn,openReusableDialog:Qe,closeReusableDialog:oa,dialogTitle:zn,dialogMessage:la,handleDialogConfirm:oa,dialogOkText:"OK",dialogSeverity:Vn}),n(Rt,{openSnackBar:qa,alertMsg:la,alertType:Va,handleSnackBarClose:dn}),n("div",{style:{...ys,backgroundColor:"#FAFCFF"},children:p(Ma,{spacing:1,children:[n(x,{container:!0,mt:0,sx:Rs,children:p(x,{item:!0,md:5,children:[n(_e,{variant:"h3",children:n("strong",{children:_("Material")})}),n(_e,{variant:"body2",color:"#777",children:_("This view displays the list of Materials")})]})}),n(x,{container:!0,sx:xs,children:n(x,{item:!0,md:12,children:p(Tl,{defaultExpanded:!1,children:[p(Sl,{expandIcon:n(Ls,{sx:{fontSize:"1.25rem",color:Y.primary.main}}),"aria-controls":"panel1a-content",id:"panel1a-header",children:[n(vs,{sx:{fontSize:"1.25rem",marginRight:1,color:Y.primary.main}}),n(_e,{sx:{fontSize:"0.875rem",fontWeight:600,color:Y.primary.dark},children:_("Search Material")})]}),p(Gs,{sx:{padding:"1rem 1rem 0.5rem"},children:[p(_l,{container:!0,children:[p(x,{container:!0,rowSpacing:1,spacing:2,alignItems:"center",sx:{padding:"0rem 1rem 0.5rem"},children:[vt==null?void 0:vt.filter(e=>e.MDG_MAT_VISIBILITY!=="Hidden").sort((e,l)=>e.MDG_MAT_SEQUENCE_NO-l.MDG_MAT_SEQUENCE_NO).map((e,l)=>{var r,o,f,a,R,K;return p(Mt.Fragment,{children:[(e==null?void 0:e.MDG_MAT_FIELD_TYPE)===We.REGION&&p(x,{item:!0,md:2,children:[p(ue,{sx:de,children:[_(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)," ",n("span",{style:{color:(o=(r=Y)==null?void 0:r.error)==null?void 0:o.dark},children:"*"})]}),n(rt,{size:"small",fullWidth:!0,children:n(il,{options:[{code:"US",desc:"USA"},{code:"EUR",desc:"Europe"}],value:U,onChange:S=>{Be(S),Ye([]),z([]),pe([]),re([])},placeholder:_(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME),disabled:!1,minWidth:"90%",listWidth:210})})]}),(e==null?void 0:e.MDG_MAT_FIELD_TYPE)===We.SALESORG&&p(x,{item:!0,md:2,children:[p(ue,{sx:de,children:[_(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME),n("span",{style:{color:(a=(f=Y)==null?void 0:f.error)==null?void 0:a.dark},children:"*"})]}),n(Fe,{matGroup:U?V==null?void 0:V.uniqueSalesOrgList:[],selectedMaterialGroup:$,setSelectedMaterialGroup:S=>{Ye(S),z([]),S.length===0?(at([]),z([])):xn(S)},placeholder:_(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)})]}),(e==null?void 0:e.MDG_MAT_FIELD_TYPE)===We.PLANT&&p(x,{item:!0,md:2,children:[p(ue,{sx:de,children:[_(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME),n("span",{style:{color:(K=(R=Y)==null?void 0:R.error)==null?void 0:K.dark},children:"*"})]}),n(Fe,{matGroup:$!=null&&$.length?b:[],selectedMaterialGroup:H,setSelectedMaterialGroup:pe,placeholder:_(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)})]}),(e==null?void 0:e.MDG_MAT_FIELD_TYPE)===We.NUMBER&&p(x,{item:!0,md:2,children:[n(ue,{sx:de,children:_(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)}),n(rt,{size:"small",fullWidth:!0,children:n(Ps,{matGroup:Ue,selectedMaterialGroup:oe,setSelectedMaterialGroup:re,isDropDownLoading:Te,placeholder:_(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME),onInputChange:Dn,minCharacters:4})})]}),(e==null?void 0:e.MDG_MAT_FIELD_TYPE)===We.MATERIALTYPE&&p(x,{item:!0,md:2,children:[n(ue,{sx:de,children:_(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)}),n(Fe,{matGroup:wa,selectedMaterialGroup:Ee,setSelectedMaterialGroup:Ie,placeholder:_(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)})]}),(e==null?void 0:e.MDG_MAT_FIELD_TYPE)===We.MATERIALGROUP&&p(x,{item:!0,md:2,children:[n(ue,{sx:de,children:_(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)}),n(Fe,{matGroup:Ua,selectedMaterialGroup:ve,setSelectedMaterialGroup:Ve,placeholder:_(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)})]}),(e==null?void 0:e.MDG_MAT_FIELD_TYPE)===We.DISTRIBUTIONCHANNEL&&p(x,{item:!0,md:2,children:[n(ue,{sx:de,children:_(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)}),n(Fe,{matGroup:$!=null&&$.length?Se:[],selectedMaterialGroup:ie,setSelectedMaterialGroup:S=>{if(!S||S.length===0){z([]);return}z(S)},isDropDownLoading:Te,placeholder:_(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)})]}),(e==null?void 0:e.MDG_MAT_FIELD_TYPE)===We.DIVISION&&p(x,{item:!0,md:2,children:[n(ue,{sx:de,children:_(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)}),n(Fe,{matGroup:U?G==null?void 0:G.Division:[],selectedMaterialGroup:Re,setSelectedMaterialGroup:S=>{if(!S||S.length===0){Ge([]);return}Ge(S)},placeholder:_(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)})]}),(e==null?void 0:e.MDG_MAT_FIELD_TYPE)===We.PURSTATUS&&p(x,{item:!0,md:2,children:[n(ue,{sx:de,children:_(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)}),n(Fe,{matGroup:(G==null?void 0:G.CrossPlantMaterialStatus)??[],selectedMaterialGroup:Ne,setSelectedMaterialGroup:S=>{if(!S||S.length===0){De([]);return}De(S)},placeholder:_(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)})]}),(e==null?void 0:e.MDG_MAT_FIELD_TYPE)===We.CREATEDON&&p(x,{item:!0,md:2,children:[n(ue,{sx:de,children:_(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)}),n(rt,{fullWidth:!0,sx:{padding:0},children:n(Os,{dateAdapter:$s,children:n(bs,{handleDate:bn,date:t==null?void 0:t.createdOn})})})]})]},l)}),p(x,{item:!0,md:2,children:[n(ue,{sx:de,children:_("Add New Filters")}),n(rt,{sx:{width:"100%"},children:n(ws,{sx:{font_Small:de,fontSize:"12px",width:"100%"},size:"small",multiple:!0,limitTags:2,value:Et,onChange:Nn,renderValue:e=>e.join(", "),MenuProps:{MenuProps:Q},endAdornment:Et.length>0&&n(dl,{position:"end",sx:{marginRight:"10px"},children:n(Dt,{size:"small",onClick:()=>Lt([]),"aria-label":"Clear selections",children:n(Da,{})})}),children:$t==null?void 0:$t.map(e=>p(lt,{value:e.title,children:[n(ks,{checked:Et.indexOf(e.title)>-1}),e.title]},e.title))})})]})]}),n(x,{container:!0,sx:{flexDirection:"row",padding:"0rem 1rem 0.5rem"},gap:1,children:Et.map((e,l)=>{var r;return e==="Old Material Number"?n(je,{children:p(x,{item:!0,md:2,children:[n(ue,{sx:de,children:_(e)}),n(rt,{size:"small",fullWidth:!0,children:n(It,{sx:{fontSize:"12px !important"},fullWidth:!0,size:"small",value:t==null?void 0:t.oldMaterialNumber,onChange:Tn,placeholder:_("ENTER OLD MATERIAL NUMBER")})})]},l)}):e==="Material Description"?n(je,{children:p(x,{item:!0,md:2,children:[n(ue,{sx:de,children:_(e)}),n(rt,{size:"small",fullWidth:!0,children:n(It,{sx:{fontSize:"12px !important"},fullWidth:!0,size:"small",value:t==null?void 0:t.materialDescription,onChange:En,placeholder:_("ENTER MATERIAL DESCRIPTION")})})]},l)}):e==="Created By"?n(je,{children:p(x,{item:!0,md:2,children:[n(ue,{sx:de,children:_("Created By")}),n(It,{sx:{fontSize:"12px !important"},fullWidth:!0,size:"small",value:t==null?void 0:t.createdBy,onChange:An,placeholder:_("ENTER CREATED BY")})]},l)}):p(x,{item:!0,md:2,children:[n(ue,{sx:{fontSize:"12px"},children:_(e)}),n(Fe,{matGroup:(dt==null?void 0:dt[e])??[],selectedMaterialGroup:((r=ae[e])==null?void 0:r.length)>0?ae[e]:[],setSelectedMaterialGroup:o=>{var f;if(!o||o.length===0){Ce(a=>({...a,[e]:[]}));return}o.length>0&&((f=o[o.length-1])==null?void 0:f.code)==="Select All"?Cn(e):Ce(a=>({...a,[e]:o}))},placeholder:`SELECT ${e==null?void 0:e.toUpperCase()}`})]},l)})})]}),p(Cl,{children:[n(Na,{variant:"outlined",size:"small",startIcon:n(Da,{sx:{fontSize:"1rem"}}),onClick:()=>{ia()},disabled:ra,sx:{borderColor:Y.primary.main,color:Y.primary.main},children:_("Clear")}),n(x,{sx:{...Ea},children:n(Ml,{moduleName:"MaterialMaster",handleSearch:ft,disabled:U===""||!($!=null&&$.length)||!(H!=null&&H.length),onPresetActiveChange:e=>qn(e),onClearPreset:ia})}),n(Na,{variant:"contained",size:"small",startIcon:n(Us,{sx:{fontSize:"1rem"}}),sx:{...Bs,...Ea},disabled:ra,onClick:()=>{const e=[];if(U==""&&e.push("Region"),$!=null&&$.length||e.push("SalesOrg"),H!=null&&H.length||e.push("Plant"),e.length>0){Xe(zt.MANDATORY_FILTER_MD(e.join(", "))),Xa("error"),cn();return}ft()},children:_("Search")})]})]})]})})}),n(x,{item:!0,sx:{position:"relative"},children:n(Ma,{children:n(Ys,{isLoading:y,paginationLoading:y,module:"MaterialMgmt",width:"100%",title:_("List of Materials")+" ("+ta+")",rows:E??[],columns:Wa??[],showSearch:!0,showRefresh:!0,showSelectedCount:!0,showExport:!0,onSearch:e=>$n(e),onRefresh:Zn,pageSize:c,page:gt,onPageSizeChange:Kn,rowCount:ta??(m==null?void 0:m.length)??0,onPageChange:Qn,getRowIdValue:"id",hideFooter:!0,disableSelectionOnClick:!0,status_onRowSingleClick:!0,tempheight:"calc(100vh - 320px)",onRowsSelectionHandler:Jn,callback_onRowSingleClick:e=>{var r,o;const l=e.row.Number;(o=(r=e==null?void 0:e.row)==null?void 0:r.materialType)==null||o.split(" - ")[0],N(!0),te(`/masterDataCockpit/materialMaster/DisplayMaterialSAPView/${l}`,{state:e.row})},showCustomNavigation:!0,stopPropagation_Column:"action",status_onRowDoubleClick:!0,showFirstPageoptions:!0,showSelectAllOptions:!0,onSelectAllOptions:ns,onSelectFirstPageOptions:ss})})}),(J==null?void 0:J.length)>0&&n(Ut,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:p(Ws,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:et,onChange:e=>{tt(e)},children:[p(Ra,{open:Un,onClose:bt,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[p(Hs,{children:[n(_e,{variant:"h6",children:"Inputs"}),n(Dt,{onClick:bt,children:n(zs,{})})]}),n(xa,{dividers:!0,children:n(x,{container:!0,spacing:1})}),p(La,{children:[n(ne,{onClick:bt,children:"Cancel"}),n(ne,{variant:"contained",children:"Proceed"})]})]}),n(ne,{size:"small",variant:"contained",onClick:()=>{te("/requestBench/createRequest"),C(Vs({})),C(Xs({}))},children:_("Create Request")}),n(ne,{size:"small",variant:"contained",onClick:()=>{Xn()},children:_("Finance Request")}),n(ne,{size:"small",variant:"contained",onClick:()=>{Pt(!0)},children:_("SAP Data Export")}),n(qs,{variant:"contained",ref:ut,"aria-label":"split button"}),n(Aa,{sx:{zIndex:1},open:Qa,anchorEl:ut.current,placement:"top-end",children:n(Ut,{style:{width:(fa=ut.current)==null?void 0:fa.clientWidth},children:n(Ta,{onClickAway:gn,children:n(Sa,{id:"split-button-menu",autoFocusItem:!0,children:tn.slice(1).map((e,l)=>n(lt,{selected:l===Za-1,onClick:()=>fn(e,l+1),children:e},e))})})})}),n(Aa,{sx:{zIndex:1},open:ma,anchorEl:At.current,placement:"top-end",children:n(Ut,{style:{width:(ga=At.current)==null?void 0:ga.clientWidth},children:n(Ta,{onClickAway:Mn,children:n(Sa,{id:"split-button-menu",autoFocusItem:!0,children:an.slice(1).map((e,l)=>n(lt,{selected:l===ja-1,onClick:()=>hn(e,l+1),children:e},e))})})})}),Ja&&n(rl,{artifactId:"",artifactName:"",setOpen:Gt,handleUpload:pn})]})})]})}),n(Al,{openSearch:sn,setOpenSearch:Pt,onSearchComplete:rn}),n(Vt,{blurLoading:Pe}),n(nl,{})]})};export{Vl as default};
