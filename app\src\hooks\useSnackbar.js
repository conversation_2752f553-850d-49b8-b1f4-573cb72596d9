import { useSelector, useDispatch } from 'react-redux';
import { openSnackbar, closeSnackbar } from '../app/snackbarSlice';

export const useSnackbar = () => {
  const dispatch = useDispatch();
  const snackbarState = useSelector((state) => state.snackbar);

  const showSnackbar = (message, type = 'info') => {
    dispatch(openSnackbar({ message, type }));
  };

  const snackBarClose = () => {
    dispatch(closeSnackbar());
  };

  return {
    openSnackbar: snackbarState.openSnackbar,
    message: snackbarState.messageDialogMessage,
    type: snackbarState.alertType,
    showSnackbar,
    snackBarClose,
  };
};