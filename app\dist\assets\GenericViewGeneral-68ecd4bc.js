import{j as c,l as d,a,an as m,T as o,A as h,G as t,b4 as b,B as g,f as u,g as w}from"./index-17b8d91e.js";import{F as f}from"./FilterField-727c53f0.js";const D=e=>{var r,l;return c(w,{sx:{marginBottom:"20px",boxShadow:3,borderRadius:"10px",borderColor:(r=d)==null?void 0:r.primary.white},children:[a(h,{expandIcon:a(m,{}),sx:{backgroundColor:d.primary.whiteSmoke,borderRadius:"10px",padding:"8px 16px","&:hover":{backgroundColor:d.hover.hoverbg}},children:a(o,{variant:"h6",sx:{fontWeight:"bold"},children:e==null?void 0:e.viewName})}),a(u,{children:(l=e==null?void 0:e.<PERSON><PERSON>ield<PERSON>)==null?void 0:l.map(n=>c(t,{item:!0,md:12,sx:{backgroundColor:d.white,maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:`1px solid ${d.grey}`,mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...b},children:[a(t,{container:!0,children:a(o,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:n[0]})}),a(g,{children:a(t,{container:!0,spacing:1,children:[...n[1]].filter(i=>i.visibility!=="Hidden").sort((i,x)=>i.sequenceNo-x.sequenceNo).map(i=>a(f,{disabled:e==null?void 0:e.disabled,field:i,dropDownData:e.dropDownData,materialID:e==null?void 0:e.materialID,selectedMaterialNumber:e==null?void 0:e.selectedMaterialNumber,viewName:e==null?void 0:e.viewName,plantData:e.viewName},i.fieldName))})})]},n[0]))})]},1)};export{D as G};
