import{pQ as l,o as i}from"./index-75c1660a.js";import{a as u,M as p,Z as s,G as a,v as g,b as x,d as h,_ as b,c as f}from"./Button-c2ace85e.js";import{i as v}from"./Dropdown-fc3a3f6e.js";const y=e=>i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"none",...e,children:i.jsx("g",{id:"Icons /General",children:i.jsx("path",{d:"M13.333 17.5L5.83301 10L13.333 2.5",stroke:"currentColor",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"})})}),M=l(y),j=e=>i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"none",...e,children:i.jsx("g",{id:"Icons /General",children:i.jsx("path",{d:"M6.66699 2.5L14.167 10L6.66699 17.5",stroke:"currentColor",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"})})}),C=l(j);function $(e){return i.jsx(u,{...e})}const w=p(s)(()=>({"& .MuiTableRow-root:nth-of-type(odd)":{backgroundColor:"var(--divider-secondary)"}}));function I({variant:e="default",children:n,...r}){return e==="zebra-striped"?i.jsx(w,{...r,children:n}):i.jsx(s,{...r,children:n})}const T=a(g)(({theme:e})=>({padding:e.spacing(1),fontSize:"inherit",fontFamily:"inherit",color:"var(--text-primary)",borderBottom:"0.5px solid var(--divider-primary)",borderRight:"none","&.MuiTableCell-head":{fontWeight:500},"&.MuiTableCell-body":{fontWeight:400},"&.MuiTableCell-sizeMedium":{fontSize:"0.875rem","&.MuiTableCell-head":{padding:"0.5rem",lineHeight:"2.5rem"},"&.MuiTableCell-body":{padding:"0.5rem",lineHeight:"2.2rem"}},"&.MuiTableCell-sizeSmall":{fontSize:"0.75rem","&.MuiTableCell-head":{padding:"0.5rem",lineHeight:"1.5rem"},"&.MuiTableCell-body":{padding:"0.5rem",lineHeight:"1rem"}}}));function B(e){return i.jsx(T,{...e})}function F(e){return i.jsx(x,{...e})}const k=a(h)(()=>({"& .MuiTablePagination-root":{color:"var(--text-primary)"},"& .MuiTablePagination-selectLabel":{fontFamily:"inherit"},"& .MuiTablePagination-displayedRows":{color:"var(--text-primary)",border:"1px solid var(--divider-primary)",borderRadius:"4px",padding:"0.5rem 4rem 0.5rem 0.5rem",marginRight:"-4rem",fontFamily:"inherit"},"& .MuiTablePagination-toolbar":{color:"var(--text-primary)",padding:"0.5rem"},"& .MuiTablePagination-select":{border:"1px solid var(--divider-primary)",borderRadius:"4px",padding:"0.5rem",marginLeft:"0.75rem",alignSelf:"center"},"& .MuiInputBase-root":{marginRight:"1.25rem",fontFamily:"inherit"},"& .MuiTablePagination-actions":{marginLeft:"0.5rem"},"& .MuiSelect-icon":{position:"absolute",right:"0.25rem",top:"unset"}})),L=e=>i.jsx(v,{size:"small",...e,sx:{}}),P=e=>{const{count:n,page:r,rowsPerPage:d,onPageChange:t}=e,o=Math.ceil(n/d)-1,c=()=>{r>0&&t(null,r-1)},m=()=>{r<o&&t(null,r+1)};return i.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"1rem",padding:" 0 0.5rem"},children:[i.jsx(M,{size:"xsmall",onClick:c,style:{cursor:r>0?"pointer":"not-allowed",opacity:r>0?1:.5}}),i.jsx(C,{size:"xsmall",onClick:m,style:{cursor:r<o?"pointer":"not-allowed",opacity:r<o?1:.5}})]})};function G(e){return i.jsx(k,{SelectProps:{IconComponent:L},ActionsComponent:P,...e})}const z=a(b)(()=>({"&.Mui-selected":{backgroundColor:"var(--primary-light)"}}));function H(e){return i.jsx(z,{...e})}function Z(e){return i.jsx(f,{sx:{backgroundColor:"var(--background-default)",boxShadow:"none"},...e})}export{G as T,F as a,M as b,C as c,$ as f,H as i,B as n,I as s,Z as t};
