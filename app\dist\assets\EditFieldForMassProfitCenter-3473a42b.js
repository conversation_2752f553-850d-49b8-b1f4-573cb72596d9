import{r as f,q as E,s as V,cx as ee,a as o,x as te,j as v,T as F,b6 as ae,E as K,at as re,au as oe,G as W,ay as _,F as N,ab as ce,bI as O,cv as se,K as H,b$ as J,ai as Q}from"./index-75c1660a.js";import{D as ne}from"./DatePicker-31fef6b6.js";function le(a,n){return Array.isArray(n)&&n.find(P=>P.code===a)||""}const pe=({label:a,value:n,length:q,units:P,onSave:de,fieldGroup:l,isEditMode:X,activeTabIndex:z,visibility:d,isExtendMode:ie,pcTabs:T,selectedRowData:I,options:ge=[],type:m})=>{var B,L;const[h,A]=f.useState(n),[he,Y]=f.useState(!1),D=E(e=>e.AllDropDown.dropDown),s=E(e=>e.profitCenter.MultipleProfitCenterData),u=V();le(h,D);const S=E(e=>e.appSettings);E(e=>e.edit.payload);let M={},k=-1;for(let e=0;e<(s==null?void 0:s.length);e++)if(s[e].profitCenter===I){M=s[e],k=e;break}console.log("selectedrowdata",I,s[0].profitCenter);let i=T[z];console.log("activerow",k,M,i);const p=(e,t)=>{const c=e==null?void 0:e.find(r=>(r==null?void 0:r.fieldName)===t);return c?c.value:""},y=s[k];let g=a.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("");f.useEffect(()=>{A(n)},[n]),console.log(d,g,"visibility45"),f.useEffect(()=>{(d==="0"||d==="Required")&&u(ee(g))},[i]);const R={label:a,value:h,units:P,type:m,visibility:d};console.log("fieldData==========",R);const C=(e,t)=>{u(O({keyname:g.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""),data:t}));let c=s==null?void 0:s.map((r,G)=>{let $=T[z];if(G===k){let b=r.viewData,U=r.viewData[$];console.log("temp",U);let x=r.viewData[$][l];return console.log("temp2",x),{...r,viewData:{...b,[$]:{...U,[l]:x==null?void 0:x.map(j=>j.fieldName===e?{...j,value:t}:j)}}}}else return r});console.log("changedData",c),u(se(c))},Z=e=>{console.log("compcode",e);const t=r=>{console.log("value",r),u(O({keyname:"Currency",data:""})),u(Q({keyName:"Currency",data:r.body}))},c=r=>{console.log(r,"error in dojax")};H(`/${J}/data/getCurrency?companyCode=${e==null?void 0:e.code}`,"get",t,c)},w=e=>{console.log("countryyyyy",e);const t=r=>{console.log("value",r),u(O({keyname:"Region",data:""})),u(Q({keyName:"Region",data:r.body}))},c=r=>{console.log(r,"error in dojax")};H(`/${J}/data/getRegionBasedOnCountry?country=${e==null?void 0:e.code}`,"get",t,c)};return console.log("chiranjit",h),f.useEffect(()=>{(a==="Analysis Period From"||a==="Analysis Period To"||a==="Created On")&&A(parseInt(n.replace("/Date(","").replace(")/","")))},[n]),console.log("editedValue[key] ",D[g]),console.log("editedValue[key] ",h),o(W,{item:!0,children:o(te,{children:X?v(N,{children:[v(F,{variant:"body2",color:"#777",children:[a," ",d==="Required"||d==="0"?o("span",{style:{color:"red"},children:"*"}):""]}),m==="Drop Down"?o(ae,{options:D[g]??[],value:p(y.viewData[i][l],a)&&((B=D[g])==null?void 0:B.filter(e=>e.code===p(y.viewData[i][l],a)))&&((L=D[g])==null?void 0:L.filter(e=>e.code===p(y.viewData[i][l],a))[0])||"",onChange:(e,t)=>{a==="Comp Code"&&Z(t),a==="Country/Reg"&&w(t),C(a,t==null?void 0:t.code),console.log("newValue",t),A(t.code),Y(!0),console.log("keys",g)},getOptionLabel:e=>(console.log("optionn",e),e===""||(e==null?void 0:e.code)===""?"":`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??""),renderOption:(e,t)=>(console.log("option vakue",t),o("li",{...e,children:o(F,{style:{fontSize:12},children:`${t==null?void 0:t.code} - ${t==null?void 0:t.desc}`})})),renderInput:e=>o(K,{...e,variant:"outlined",placeholder:`Select ${R.label}`,size:"small",label:null})}):m==="Input"?o(K,{variant:"outlined",size:"small",value:p(y.viewData[i][l],a).toUpperCase(),placeholder:`Enter ${R.label}`,inputProps:{maxLength:q},onChange:e=>{const t=e.target.value;if(t.length>0&&t[0]===" ")C(a,t.trimStart());else{let c=t.toUpperCase();C(a,c)}}}):m==="Calendar"?o(re,{dateAdapter:oe,children:o(ne,{slotProps:{textField:{size:"small"}},value:parseInt(p(y.viewData[i][l],a).replace("/Date(","").replace(")/","")),placeholder:"Select Date Range",maxDate:new Date(9999,12,31),onChange:e=>{C(e),A(e)}})}):m==="Radio Button"?o(W,{item:!0,md:2,children:o(_,{sx:{padding:0},checked:p(y.viewData[i][l],a)==!0,onChange:e=>{console.log("oncheckbox",a,e.target.checked),C(a,e.target.checked)}})}):""]}):o(N,{children:v(N,{children:[v(F,{variant:"body2",color:"#777",children:[a," ",d==="Required"||d==="0"?o("span",{style:{color:"red"},children:"*"}):""]}),v(F,{variant:"body2",fontWeight:"bold",children:[a==="Analysis Period From"||a==="Analysis Period To"?ce(h).format(S==null?void 0:S.dateFormat):h,m==="Radio Button"?o(_,{sx:{padding:0},checked:h,disabled:!0}):""]})]})})})})};export{pe as E};
