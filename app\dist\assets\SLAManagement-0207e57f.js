import{r,q as ee,j as d,a as e,C as _e,z as je,V as We,aF as ze,T as u,R as ke,I as le,W as qe,G as S,X as He,b8 as Ve,t as be,aB as Ye,az as Ke,bp as Xe,K as Y,fY as oe,B as Re,aq as Ne,F as O,x as ve,E as ne,b6 as Je,av as Qe,au as Ze,at as et,M as Ie,aw as tt,ar as Be,g0 as ye,ab as me,S as xe,m as lt,n as ot,o as it,aj as ct,ak as Fe,f9 as ut,aC as dt,b1 as Pe,h as $e,aE as pt,aD as gt,al as ht,bY as mt,g1 as ft}from"./index-17b8d91e.js";import{d as Ct}from"./DeleteOutlined-888bfc33.js";import{I as at}from"./InputAdornment-5b0053c5.js";import{a as st}from"./DesktopDatePicker-07c19cde.js";import{V as St}from"./icons-35b62b58.js";import"./dateViewRenderers-34586552.js";import"./useSlotProps-e34e1e13.js";import"./CSSTransition-30917e2c.js";import"./useMediaQuery-6a073ac5.js";const yt=({handleClose:_,scenarioRef:ie,data:ce,open:h,controller:b})=>{var H;const[E,j]=r.useState("");r.useState([]),r.useState([]);const te=ee(n=>n.userManagement.userData);let K={processName:"",serviceName:"",serviceType:"",sla:"",slaType:"",startDate:null,endDate:null,slaFormat:"Days",date:[],slaReference:"",masterDataCategory:"",masterData:"",recipientGroup:"",emailTemplate:"",validity:"",inAppNotification:!1};const[p,c]=r.useState(K),[R,ue]=r.useState([]);r.useState("");const[ae,se]=r.useState([]),[T,M]=r.useState([]),[fe,D]=r.useState([]),[N,W]=r.useState([]),[re,de]=r.useState({}),[A,Ae]=r.useState({}),[z,Te]=r.useState({});let q=r.useRef(null);const[Ce,P]=r.useState([]);(H=ee(n=>n.userManagement.groups))==null||H.map(n=>n.name);let y=ee(n=>n.masterData),[Se,pe]=r.useState(!1),$=["processName","serviceName","sla","slaType","slaFormat","date","slaReference"];const U=n=>{let i={};for(let t of T)if((t==null?void 0:t["SLA Service Name"])===n){ue(t==null?void 0:t["SLA Type"]),c(m=>({...m,slaReference:t==null?void 0:t["SLA Reference"]}));break}console.log(i)},L=()=>{let n=T,i=[],t={};n.forEach(m=>{m["SLA Process Name"]===p.processName&&i.push(m)}),i.forEach(m=>{t[m["SLA Service Name"]]||(t[m["SLA Service Name"]]=!0)}),se(Object.keys(t))};let X=n=>{n==="purchasing group"&&P(y==null?void 0:y.purchasingGroups),n==="supplier"&&P(y==null?void 0:y.vendorCode),n==="company"&&P(y==null?void 0:y.companyCode)};const x=()=>{Y(`/${oe}/sla/getSLAServiceDropdownValues`,"get",n=>{if(n.slaDropdownList){M(n.slaDropdownList.map(t=>{var m;return{...t,"SLA Type":(m=t["SLA Type"])==null?void 0:m.split(",")}}));let i={};n.slaDropdownList.forEach(t=>{i[t["SLA Process Name"]]||(i[t["SLA Process Name"]]=!0)}),D(Object.keys(i))}})},B=n=>{c(i=>({...i,[n.target.name]:n.target.value}))},De=n=>{c(i=>({...i,[n]:i[n]==="Days"?"Hours":"Days"}))},o=()=>{var m,J,Q;let n={...p,createdBy:te.emailId,sla:parseInt(p.sla),startDate:(m=p.date)==null?void 0:m[0],endDate:(J=p.date)==null?void 0:J[1]};n.emailTemplate="",n.masterData=(Q=n.masterData)==null?void 0:Q.split(" - ").slice(-1).toString(),delete n.date;let i=I=>{b("SUCCESS",p.processName),g()},t=I=>{console.log(I)};Y(`/${oe}/sla/create`,"post",i,t,n)},g=()=>{c(K),_("CREATE")},C=(n,i)=>{let t=n.toLowerCase();var m="";if(t==="purchasing group"&&(m=`/${ye}/Odata/getSearchDetails?name={##}&type=PurchasingGroup`),t==="supplier"&&(m=`/${ye}/Odata/getSearchDetails?name={##}&type=vendor`),t==="company"&&(m=`/${ye}/Odata/getSearchDetails?name={##}&type=company`),i){const J=I=>{var s=Object.keys(I.data).map(l=>`${I.data[l]} - ${l}`);n==="purchasing group"&&(P(s),pe(!1)),n==="supplier"&&(P(s),pe(!1)),n==="company"&&(P(s),pe(!1))},Q=I=>{console.log(I)};Y(`${m}`.replace("{##}",i==null?void 0:i.split(" - ")[0]),"get",J,Q)}},G=(n,i)=>{let t=p.masterDataCategory;q&&clearTimeout(q.current),i?(pe(!0),q.current=setTimeout(()=>{C(t,i)},1e3)):X(t)},ge=[{name:"processName",label:"Process Name",isRequired:!0,value:p,onchange:B,disabled:!1,errorItems:N,variant:"select",option:fe,placeholder:"Select Process Name",col:5},{name:"serviceName",label:"Service Name",isRequired:!0,value:p,onchange:B,disabled:!1,errorItems:N,variant:"select",option:ae,placeholder:"Select Service Name",col:5},{name:"slaType",label:"SLA Type",isRequired:!0,value:p,onchange:B,disabled:!1,errorItems:N,variant:"select",option:R,placeholder:"Select SLA Type",col:5},{name:"date",label:"SLA Validity Range",isRequired:!0,value:p,onChange:n=>{c(i=>({...i,date:n}))},disabled:!1,errorItems:N,variant:"daterangepicker",format:"dd MMM yyyy",placeholder:"Enter SLA Validity",col:5},{name:"sla",label:"SLA",isRequired:!0,value:p,onchange:B,disabled:!1,errorItems:N,variant:"textWithAdornment",placeholder:"Enter SLA",col:5,adornment:{name:"slaFormat",onChange:De,value:p}},{name:"slaReference",label:" ",isRequired:!1,value:p,variant:"custom",col:5,component:n=>e(O,{children:e(ve,{sx:{backgroundColor:"#EAE9FF",borderRadius:"10px",padding:".5rem"},children:e(u,{children:`SLA Reference: ${n.value[n.name]??""}`})})})},{name:"masterDataCategory",label:"Master Data Category",isRequired:!1,value:p,onchange:B,disabled:!1,errorItems:N,variant:"select",option:["Supplier","Company","Purchasing Group"],multiline:!0,placeholder:"Select Master Data Category",col:5},{name:"masterData",label:"Master Data",isRequired:!1,value:p,onchange:(n,i)=>{c(t=>({...t,masterData:i})),G(n,i)},disabled:!1,elasticLoading:Se,errorItems:N,option:Ce,variant:"autocomplete",multiline:!0,placeholder:"Select Master",col:5}],we=n=>{const i=t=>{var m,J,Q,I,s;switch(t.variant){case"text":return e(ne,{fullWidth:!0,size:"small",placeholder:t.placeholder,name:t.name,value:(m=t.value)==null?void 0:m[t.name],onChange:t.onchange,required:t.isRequired,error:(J=t.errorItems)==null?void 0:J.includes(t.name),InputProps:{},multiline:t.multiline});case"select":return e(Be,{fullWidth:!0,size:"small",children:d(tt,{error:t.errorItems.includes(t.name),select:!0,fullWidth:!0,size:"small",placeholder:t.placeholder,name:t.name,value:t.value[t.name],onChange:t.onchange,displayEmpty:!0,InputProps:{},children:[e(Ie,{value:"",children:e(u,{className:"placeholderstyle",style:{color:"#C1C1C1"},children:t.placeholder})}),(Q=t==null?void 0:t.option)==null?void 0:Q.map(l=>e(Ie,{value:l,children:l}))]})});case"datepicker":return e(et,{dateAdapter:Ze,children:e(st,{inputFormat:t.format??"",value:t.value[t.name],name:t.name,onChange:t.onChange,InputProps:{},renderInput:l=>e(ne,{fullWidth:!0,size:"small",...l,placeholder:t.placeholder,sx:{backgroundColor:"white",borderRadius:"6px"}})})});case"textWithAdornment":return e(ve,{direction:"row",children:e(ne,{fullWidth:!0,size:"small",placeholder:t.placeholder,name:t.name,value:(I=t.value)==null?void 0:I[t.name],onChange:t.onchange,required:t.isRequired,error:(s=t.errorItems)==null?void 0:s.includes(t.name),InputProps:{endAdornment:e(at,{sx:{height:"100%",marginRight:0},position:"end",children:e(le,{size:"small",onClick:()=>t.adornment.onChange(t.adornment.name),sx:{fontSize:"14px"},children:t.adornment.value[t.adornment.name]})})},multiline:t.multiline})});case"daterangepicker":return e(Qe,{className:"dates",size:"md",format:"dd MMM yyyy",placement:"auto",date:t.value[t.name],handleDate:t.onChange});case"autocomplete":return e(Je,{disablePortal:!0,placeholder:t.placeholder,id:"combo-box-demo",options:t==null?void 0:t.option,fullWidth:!0,size:"small",freeSolo:!0,loading:(t==null?void 0:t.elasticLoading)??!1,name:t.name,renderInput:l=>e(ne,{placeholder:t.placeholder,...l}),onInputChange:(l,a)=>t.onchange(t.name,a),value:t.value[t.name]});case"custom":return t.component(t);default:return e(O,{})}};return e(O,{children:n.map(t=>e(S,{item:!0,xs:1,md:t.col,sx:{height:"max-content",marginTop:"auto"},children:d(Re,{sx:{minWidth:120,display:"flex",justifyContent:"end",flexDirection:"column"},children:[d(Re,{sx:{display:"flex",flexDirection:"row",justifyContent:"start",alignItems:"self-start",marginBottom:"auto"},children:[e(u,{sx:{...Ne,display:"inline-block"},children:t.label}),t.isRequired&&e(u,{sx:{...Ne,display:"inline-block",color:"red"},children:"*"})]}),e(O,{children:i(t)})]})}))})},[he,w]=r.useState({currentNotification:"",success:!0,open:!1,title:"",severity:""}),[Le,Oe]=r.useState({currentNotification:"",success:!0,open:!1,title:"",severity:""}),v={MessageDialogCancel:()=>{w(n=>({open:!1,currentNotification:"",success:"",title:"",severity:""}))},MessageDialogClickOpen:()=>{w(n=>({...n,open:!0}))},MessageDialogClose:()=>{w(n=>({open:!1,currentNotification:"",success:"",title:"",severity:""})),Oe(n=>({open:!1,currentNotification:"",success:"",title:"",severity:""})),_()},messageDialogCloseAndRedirect:()=>{w(n=>({open:!1,currentNotification:"",success:"",title:"",severity:""})),Oe(n=>({open:!1,currentNotification:"",success:"",title:"",severity:""}))},getHandleOkFunction:()=>{switch(E){case"CONFIRMUPDATE":w(n=>({open:!1,currentNotification:"",success:"",title:"",severity:""})),o();break;case"CONFIRMCREATE":w(n=>({open:!1,currentNotification:"",success:"",title:"",severity:""})),o();break;case"ERROR":v.MessageDialogClose();break;case"CONFIRMCANCEL":v.MessageDialogClose();default:v.MessageDialogClose()}},viewOkButton:()=>{switch(E){case"CONFIRMUPDATE":return!0;case"CONFIRMCREATE":return!0;case"ERROR":return!1;case"CONFIRMCANCEL":return!1;default:return!1}},viewCancelButton:()=>{switch(E){case"CONFIRMUPDATE":return!0;case"CONFIRMCREATE":return!0;case"ERROR":return!1;case"CONFIRMCANCEL":return!0;default:return!1}},getOkButtonText:()=>{switch(E){case"CONFIRMUPDATE":return"Submit";case"CONFIRMCREATE":return"Submit";case"ERROR":return"OK";case"CONFIRMCANCEL":return"OK";default:return"OK"}},getHandleCancleFunction:()=>{switch(E){case"CONFIRMUPDATE":return v.MessageDialogClose();case"CONFIRMCREATE":return v.MessageDialogClose();case"ERROR":return()=>{};case"CONFIRMCANCEL":return v.MessageDialogClose();default:return()=>{}}}};return r.useEffect(()=>{U(p.serviceName)},[p.serviceName]),r.useEffect(()=>{L()},[p.processName]),r.useEffect(()=>{let n;p.masterDataCategory.toLowerCase()==="company"?n=Object.keys(re).map(i=>`${re[i]} - ${i}`):p.masterDataCategory.toLowerCase()==="supplier"?n=Object.keys(A).map(i=>`${A[i]} - ${i}`):n=Object.keys(z).map(i=>`${z[i]} -${i}`),P(n),c(i=>({...i,masterData:""}))},[p.masterDataCategory]),r.useEffect(()=>{x()},[]),d("div",{children:[e(_e,{dialogState:he.open,openReusableDialog:v.MessageDialogClickOpen,closeReusableDialog:v.MessageDialogCancel,dialogTitle:he.title,dialogMessage:he.currentNotification,handleOk:v.getHandleOkFunction,dialogOkText:v.getOkButtonText(),showOkButton:v.viewOkButton(),showCancelButton:v.viewCancelButton(),dialogSeverity:he.severity,handleDialogReject:v.getHandleCancleFunction}),e(je,{openSnackBar:Le.open,alertMsg:Le.currentNotification,handleSnackBarClose:v.MessageDialogClose}),d(We,{maxWidth:"md",open:h,onClose:g,children:[d(ze,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[e(u,{variant:"h6",children:"Create SLA"}),e(le,{sx:{width:"max-content"},onClick:g,children:e(ke,{iconName:"Close"})})]}),d(qe,{sx:{padding:".5rem 1rem"},children:[e(S,{container:!0,sx:{width:"600px",padding:".5rem 0rem",display:"flex",flexDirection:"row",justifyContent:"center",alignItems:"center"},spacing:1,columns:10,children:we(ge)})," "]}),d(He,{sx:{display:"flex",justifyContent:"end"},children:[e(be,{variant:"outlined",sx:{...Ve,marginLeft:"auto"},onClick:g,children:"Cancel"}),e(be,{variant:"contained",sx:{...Ye,...Ke},onClick:()=>{Xe(p,$,W)&&(j("CONFIRMCREATE"),w(n=>({...n,currentNotification:"Would you like to proceed with submission of the details?",success:!1,open:!0,title:"Confirm Submit",severity:"success"})))},children:"Submit"})]})]})]})},vt=({id:_,open:ie,handleClose:ce})=>{const[h,b]=r.useState({}),[E,j]=r.useState([]),[te,K]=r.useState(!1),[p,c]=r.useState({}),[R,ue]=r.useState({}),[ae,se]=r.useState({}),T=ee(D=>D.appSettings);let M=async()=>{try{let D=W=>{let re=W.data,de={...re};Object.keys(E).forEach(A=>{E[A]===re.emailTemplate&&(de.emailTemplate=A)}),b(de),K(!0)},N=()=>{};Y(`/${oe}/sla/getSingleSla/${_}`,"get",D,N)}catch(D){console.log(D)}};r.useEffect(()=>{M()},[E]),console.log();let fe=D=>{var W;let N="";switch((W=D.masterDataCategory)==null?void 0:W.toLowerCase()){case"company":N=R[D.masterData];break;case"supplier":N=p[D.masterData];break;case"purchasing group":N=ae[D.masterData];break}return console.log(R),N};return d(We,{open:ie,onClose:()=>ce("VIEW"),children:[d(ze,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[e(u,{variant:"h6",children:h.processName&&`SLA - ${h.processName}`}),e(le,{sx:{width:"max-content"},onClick:()=>ce("VIEW"),children:e(ke,{iconName:"Close"})})]}),e(qe,{sx:{padding:".5rem 1rem"},children:te&&d(S,{container:!0,col:10,children:[d(S,{item:!0,xs:"5",sx:{padding:"0px .4em",margin:".5rem 0"},children:[e(u,{variant:"body1",sx:{textAlign:"start"},children:"Process Name"}),e(u,{variant:"body1",fontWeight:"bold",sx:{textAlign:"start"},children:h.processName})]}),d(S,{item:!0,xs:"5",sx:{padding:"0px .4em",margin:".5rem 0"},children:[e(u,{variant:"body1",sx:{textAlign:"start"},children:"Service Name"}),e(u,{variant:"body1",fontWeight:"bold",sx:{textAlign:"start"},children:h.serviceName})]}),d(S,{item:!0,xs:"5",sx:{padding:"0px .4em",margin:".5rem 0"},children:[e(u,{variant:"body1",sx:{textAlign:"start"},children:"SLA Type"}),e(u,{variant:"body1",fontWeight:"bold",sx:{textAlign:"start"},children:h.slaType})]}),e(S,{item:!0,xs:"5",sx:{padding:"0px .4em",margin:".5rem 0"},children:e(ve,{sx:{backgroundColor:D=>D.background.secondary,borderRadius:"10px",padding:".5rem"},children:e(u,{children:`SLA Reference: ${h.slaReference??""}`})})}),d(S,{item:!0,xs:"5",sx:{padding:"0px .4em",margin:".5rem 0"},children:[e(u,{variant:"body1",sx:{textAlign:"start"},children:"SLA Validity Range"}),e(u,{variant:"body1",fontWeight:"bold",sx:{textAlign:"start"},children:`${me(h.startDate).format(T.dateFormat)} ~ ${me(h.endDate).format(T.dateFormat)}`})]}),d(S,{item:!0,xs:"5",sx:{padding:"0px .4em",margin:".5rem 0"},children:[e(u,{variant:"body1",sx:{textAlign:"start"},children:"Master Data Category"}),e(u,{variant:"body1",fontWeight:"bold",sx:{textAlign:"start"},children:h.masterDataCategory})]}),d(S,{item:!0,xs:"5",sx:{padding:"0px .4em",margin:".5rem 0"},children:[e(u,{variant:"body1",sx:{textAlign:"start"},children:"Master Data"}),d(ve,{children:[e(u,{variant:"body1",fontWeight:"bold",sx:{textAlign:"start"},children:fe({masterData:h.masterData,masterDataCategory:h.masterDataCategory})}),e(u,{fontWeight:"bold",sx:{textAlign:"start"},children:h.masterData})]})]}),d(S,{item:!0,xs:"5",sx:{padding:"0px .4em",margin:".5rem 0"},children:[e(u,{variant:"body1",sx:{textAlign:"start"},children:"Created Date"}),e(u,{variant:"body1",fontWeight:"bold",sx:{textAlign:"start"},children:me(h.createdAt).format(T.dateFormat)})]}),d(S,{item:!0,xs:"5",sx:{padding:"0px .4em",margin:".5rem 0"},children:[e(u,{variant:"body1",sx:{textAlign:"start"},children:"Created By"}),e(u,{variant:"body1",fontWeight:"bold",sx:{textAlign:"start"},children:h.createdBy})]}),d(S,{item:!0,xs:"5",sx:{padding:"0px .4em",margin:".5rem 0"},children:[e(u,{variant:"body1",sx:{textAlign:"start"},children:"Updated Date"}),e(u,{variant:"body1",fontWeight:"bold",sx:{textAlign:"start"},children:me(h.updatedAt).format(T.dateFormat)})]}),d(S,{item:!0,xs:"5",sx:{padding:"0px .4em",margin:".5rem 0"},children:[e(u,{variant:"body1",sx:{textAlign:"start"},children:"Updated By"}),e(u,{variant:"body1",fontWeight:"bold",sx:{textAlign:"start"},children:h.updatedBy})]})]})})]})},Dt=({handleClose:_,id:ie,open:ce,controller:h})=>{var I;const[b,E]=r.useState("");r.useState({}),r.useState([]);const[j,te]=r.useState([]);(I=ee(s=>s.userManagement.groups))==null||I.map(s=>s.name);const K=ee(s=>s.userManagement.userData);let p={processName:"",serviceName:"",serviceType:"",sla:"",slaType:"",startDate:null,endDate:null,slaFormat:"Days",date:[],slaReference:"PO Open Date",masterDataCategory:"",masterData:"",recipientGroup:"",emailTemplate:"",inAppNotification:!1};const[c,R]=r.useState(p),[ue,ae]=r.useState([]),[se,T]=r.useState([]),[M,fe]=r.useState([]);let D=["processName","serviceName","serviceType","sla","slaType","slaFormat","date","slaReference"];const[N,W]=r.useState([]),[re,de]=r.useState([]),[A,Ae]=r.useState(!1),[z,Te]=r.useState({}),[q,Ce]=r.useState({}),[P,y]=r.useState([]),[Se,pe]=r.useState({}),[$,U]=r.useState({});let L=ee(s=>s.masterData),[X,x]=r.useState(!1),B=r.useRef(null),De=s=>{s==="purchasing group"&&y(L==null?void 0:L.purchasingGroups),s==="supplier"&&y(L==null?void 0:L.vendorCode),s==="company"&&y(L==null?void 0:L.companyCode)};const o=(s,l)=>{let a=s.toLowerCase();var f="";if(a==="purchasing group"&&(f=`/${ye}/Odata/getSearchDetails?name={##}&type=PurchasingGroup`),a==="supplier"&&(f=`/${ye}/Odata/getSearchDetails?name={##}&type=vendor`),a==="company"&&(f=`/${ye}/Odata/getSearchDetails?name={##}&type=company`),l){const Z=k=>{var Me=Object.keys(k.data).map(F=>`${k.data[F]} - ${F}`);y(Me),x(!1)},V=k=>{console.log(k)};Y(`${f}`.replace("{##}",l==null?void 0:l.split(" - ")[0]),"get",Z,V)}},g=s=>{let l=c.masterDataCategory;B&&clearTimeout(B.current),s?(x(!0),B.current=setTimeout(()=>{o(l,s)},1e3)):De(l)};let C=(s,l)=>{R(a=>({...a,[s]:l})),g(l)};const G=s=>{for(let l of se)if((l==null?void 0:l["SLA Service Name"].toLowerCase())===s.toLowerCase()){ae(l==null?void 0:l["SLA Type"]),R(a=>({...a,slaReference:l==null?void 0:l["SLA Reference"]}));break}},Ee=()=>{let s=se,l=[],a={};s.forEach(f=>{f["SLA Process Name"]===c.processName&&l.push(f)}),l.forEach(f=>{a[f["SLA Service Name"]]||(a[f["SLA Service Name"]]=!0)}),de(Object.keys(a))},ge=s=>{R(l=>({...l,[s.target.name]:s.target.value}))},we=s=>{R(l=>({...l,[s]:l[s]==="Days"?"Hours":"Days"}))},he=()=>{var f,Z,V;let s={...c,sla:parseInt(c.sla),startDate:(f=c.date)==null?void 0:f[0],endDate:(Z=c.date)==null?void 0:Z[1],masterData:(V=c.masterData)==null?void 0:V.split(" - ").slice(-1).toString()};s.emailTemplate=j[c.emailTemplate]??"",s.updatedBy=K.emailId,delete s.date,delete s.createdAt,delete s.createdBy,delete s.ruleId,delete s.updatedAt;let l=k=>{h("SUCCESS",c.processName),w()},a=k=>{console.log(k)};!Xe(c,D,fe)&&Y(`/${oe}/sla/update/${ie}`,"patch",l,a,s)},w=()=>{R(p),_("UPDATE")},Le=[{name:"processName",label:"Process Name",isRequired:!0,value:c,onchange:ge,disabled:!1,errorItems:M,variant:"select",option:N,placeholder:"Select Process Name",col:5},{name:"serviceName",label:"Service Name",isRequired:!0,value:c,onchange:ge,disabled:!1,errorItems:M,variant:"select",option:re,placeholder:"Select Service Name",col:5},{name:"slaType",label:"SLA Type",isRequired:!0,value:c,onchange:ge,disabled:!1,errorItems:M,variant:"select",option:ue,placeholder:"Select SLA Type",col:5},{name:"date",label:"SLA Validity Range",isRequired:!0,value:c,onChange:s=>{R(l=>({...l,date:s}))},disabled:!1,errorItems:M,variant:"daterangepicker",format:"dd MMM yyyy",placeholder:"Enter SLA Validity",col:5},{name:"sla",label:"SLA",isRequired:!0,value:c,onchange:ge,disabled:!1,errorItems:M,variant:"textWithAdornment",placeholder:"Enter SLA",col:5,adornment:{name:"slaFormat",onChange:we,value:c}},{name:"slaReference",label:" ",isRequired:!1,value:c,variant:"custom",col:5,component:s=>e(O,{children:e(ve,{sx:{backgroundColor:"#EAE9FF",borderRadius:"10px",padding:".5rem"},children:e(u,{children:`SLA Reference: ${s.value[s.name]??""}`})})})},{name:"masterDataCategory",label:"Master Data Category",isRequired:!1,value:c,onchange:ge,disabled:!1,errorItems:M,variant:"select",option:["Supplier","Company","Purchasing Group"],multiline:!0,placeholder:"Select Master Data Category",col:5},{name:"masterData",label:"Master Data",isRequired:!1,value:c,onchange:C,disabled:!1,elasticLoading:X,errorItems:M,option:P,variant:"autocomplete",multiline:!0,placeholder:"Select Master Data",col:5}],Oe=s=>{const l=a=>{var f,Z,V,k,Me;switch(a.variant){case"text":return e(O,{children:A?e(ne,{fullWidth:!0,size:"small",placeholder:a.placeholder,name:a.name,value:(f=a.value)==null?void 0:f[a.name],onChange:a.onchange,required:a.isRequired,error:(Z=a.errorItems)==null?void 0:Z.includes(a.name),InputProps:{},multiline:a.multiline}):e(xe,{variant:"rectangular",height:"2rem"})});case"select":return e(O,{children:A?e(Be,{fullWidth:!0,size:"small",children:d(tt,{sx:Ne,select:!0,fullWidth:!0,size:"small",placeholder:a.placeholder,name:a.name,value:a.value[a.name],onChange:a.onchange,displayEmpty:!0,InputProps:{},children:[e(Ie,{value:"",children:e(u,{className:"placeholderstyle",style:{color:"#C1C1C1"},children:a.placeholder})}),(V=a.option)==null?void 0:V.map(F=>e(Ie,{value:F==null?void 0:F.toString(),children:`${F}`}))]})}):e(xe,{variant:"rectangular",height:"2rem"})});case"datepicker":return e(et,{dateAdapter:Ze,children:e(st,{inputFormat:a.format??"",value:a.value[a.name],name:a.name,onChange:a.onChange,InputProps:{},renderInput:F=>e(ne,{fullWidth:!0,size:"small",...F,placeholder:a.placeholder,sx:{backgroundColor:"white",borderRadius:"6px"}})})});case"textWithAdornment":return e(O,{children:A?e(ve,{direction:"row",children:e(ne,{fullWidth:!0,size:"small",placeholder:a.placeholder,name:a.name,value:(k=a.value)==null?void 0:k[a.name],onChange:a.onchange,required:a.isRequired,error:(Me=a.errorItems)==null?void 0:Me.includes(a.name),InputProps:{endAdornment:e(at,{sx:{height:"100%",marginRight:0},position:"end",children:e(le,{size:"small",onClick:()=>a.adornment.onChange(a.adornment.name),sx:{fontSize:"14px"},children:a.adornment.value[a.adornment.name]})})},multiline:a.multiline})}):e(xe,{variant:"rectangular",height:"2rem"})});case"daterangepicker":return e(O,{children:A?e(Qe,{className:"dates",size:"md",format:"dd MMM yyyy",placement:"auto",date:a.value[a.name],handleDate:a.onChange}):e(xe,{variant:"rectangular",height:"2rem"})});case"autocomplete":return e(Be,{fullWidth:!0,size:"small",sx:{maxHeight:"max-content"},children:e(Je,{disablePortal:!0,placeholder:a.placeholder,id:"combo-box-demo",options:a==null?void 0:a.option,fullWidth:!0,size:"small",freeSolo:!0,loading:(a==null?void 0:a.elasticLoading)??!1,name:a.name,renderInput:F=>e(ne,{placeholder:a.placeholder,...F}),onInputChange:(F,nt)=>a.onchange(a.name,nt),value:a.value[a.name]})});case"custom":return e(O,{children:A?a.component(a):e(xe,{variant:"rectangular",height:"2rem"})});default:return e(O,{})}};return e(O,{children:s.map(a=>e(S,{item:!0,xs:1,md:a.col,sx:{height:"max-content",marginTop:"auto"},children:d(Re,{sx:{minWidth:120,display:"flex",justifyContent:"end",flexDirection:"column"},children:[d(Re,{sx:{display:"flex",flexDirection:"row",justifyContent:"start",alignItems:"self-start",marginBottom:"auto"},children:[e(u,{sx:{...Ne,display:"inline-block"},children:a.label}),a.isRequired&&e(u,{sx:{...Ne,display:"inline-block",color:"red"},children:"*"})]}),e(O,{children:l(a)})]})}))})},[v,H]=r.useState({currentNotification:"",success:!0,open:!1,title:"",severity:""}),[n,i]=r.useState({currentNotification:"",success:!0,open:!1,title:"",severity:""}),t={MessageDialogCancel:()=>{H(s=>({open:!1,currentNotification:"",success:"",title:"",severity:""}))},MessageDialogClickOpen:()=>{H(s=>({...s,open:!0}))},MessageDialogClose:()=>{H(s=>({open:!1,currentNotification:"",success:"",title:"",severity:""})),i(s=>({open:!1,currentNotification:"",success:"",title:"",severity:""})),_()},messageDialogCloseAndRedirect:()=>{H(s=>({open:!1,currentNotification:"",success:"",title:"",severity:""})),i(s=>({open:!1,currentNotification:"",success:"",title:"",severity:""}))},getHandleOkFunction:()=>{switch(b){case"CONFIRMUPDATE":H(s=>({open:!1,currentNotification:"",success:"",title:"",severity:""})),he();break;case"CONFIRMCREATE":H(s=>({open:!1,currentNotification:"",success:"",title:"",severity:""})),he();break;case"ERROR":t.MessageDialogClose();break;case"CONFIRMCANCEL":t.MessageDialogClose();default:t.MessageDialogClose()}},viewOkButton:()=>{switch(b){case"CONFIRMUPDATE":return!0;case"CONFIRMCREATE":return!0;case"ERROR":return!1;case"CONFIRMCANCEL":return!1;default:return!1}},viewCancelButton:()=>{switch(b){case"CONFIRMUPDATE":return!0;case"CONFIRMCREATE":return!0;case"ERROR":return!1;case"CONFIRMCANCEL":return!0;default:return!1}},getOkButtonText:()=>{switch(b){case"CONFIRMUPDATE":return"Submit";case"CONFIRMCREATE":return"Submit";case"ERROR":return"OK";case"CONFIRMCANCEL":return"OK";default:return"OK"}},getHandleCancleFunction:()=>{switch(b){case"CONFIRMUPDATE":return t.MessageDialogClose();case"CONFIRMCREATE":return t.MessageDialogClose();case"ERROR":return()=>{};case"CONFIRMCANCEL":return t.MessageDialogClose();default:return()=>{}}}};let m=async()=>{try{let s=a=>{var V,k;let f=a.data,Z={...f,date:[me((V=f.startDate)==null?void 0:V.split("T")[0],"YYYY-MM-DD").toDate(),me((k=f.endDate)==null?void 0:k.split("T")[0],"YYYY-MM-DD").toDate()]};R(Z)},l=a=>{};Y(`/${oe}/sla/getSingleSla/${ie}`,"get",s,l)}catch(s){console.log(s)}};const J=()=>{Y(`/${oe}/sla/getSLAServiceDropdownValues`,"get",s=>{T(s.slaDropdownList.map(a=>{var f;return{...a,"SLA Type":(f=a["SLA Type"])==null?void 0:f.split(",")}}));let l={};s.slaDropdownList.forEach(a=>{l[a["SLA Process Name"]]||(l[a["SLA Process Name"]]=!0)}),W(Object.keys(l))})};let Q=async()=>{await J(),m(),Ae(!0)};return r.useEffect(()=>{G(c.serviceName)},[c.serviceName]),r.useEffect(()=>{Ee()},[c.processName]),r.useEffect(()=>{G(c.serviceName)},[c.serviceName]),r.useEffect(()=>{let s={},l="";switch(c.masterDataCategory.toLowerCase()){case"supplier":s=q;break;case"company":s=z;break;case"purchasing group":s=$;break}s[c.masterData]&&(console.log(s),l=`${c.masterData} - ${s[c.masterData]}`,R(a=>({...a,masterData:l})))},[z,q,$]),r.useEffect(()=>{Q()},[]),r.useEffect(()=>{let s;c.masterDataCategory.toLowerCase()==="company"?s=Object.keys(z).map(l=>`${z[l]} - ${l}`):c.masterDataCategory.toLowerCase()==="supplier"?s=Object.keys(q).map(l=>`${q[l]} - ${l}`):s=Object.keys($).map(l=>`${$[l]} -${l}`),y(s)},[c.masterDataCategory]),r.useEffect(()=>{},[Se,Se]),d("div",{children:[e(_e,{dialogState:v.open,openReusableDialog:t.MessageDialogClickOpen,closeReusableDialog:t.MessageDialogCancel,dialogTitle:v.title,dialogMessage:v.currentNotification,handleOk:t.getHandleOkFunction,dialogOkText:t.getOkButtonText(),showOkButton:t.viewOkButton(),showCancelButton:t.viewCancelButton(),dialogSeverity:v.severity,handleDialogReject:t.getHandleCancleFunction}),e(je,{openSnackBar:n.open,alertMsg:n.currentNotification,handleSnackBarClose:t.MessageDialogClose}),d(We,{maxWidth:"md",open:ce,onClose:w,children:[d(ze,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[e(u,{variant:"h6",children:"Update SLA"}),e(le,{sx:{width:"max-content"},onClick:w,children:e(ke,{iconName:"Close"})})]}),e(qe,{sx:{padding:".5rem 1rem"},children:e(S,{container:!0,sx:{width:"600px",display:"flex",flexDirection:"row",justifyContent:"center",alignItems:"center",padding:"0.5rem 0rem"},spacing:2,columns:10,children:Oe(Le)})}),d(He,{sx:{display:"flex",justifyContent:"end"},children:[e(be,{variant:"outlined",sx:{...Ve,marginLeft:"auto"},onClick:w,children:"Cancel"}),e(be,{variant:"contained",sx:{...Ye,...Ke},onClick:()=>{E("CONFIRMCREATE"),H(s=>({...s,currentNotification:"Would you like to proceed with submission of the details?",success:!1,open:!0,title:"Confirm Submit",severity:"success"}))},children:"Submit"})]})]})]})};var Ue={},xt=ot;Object.defineProperty(Ue,"__esModule",{value:!0});var rt=Ue.default=void 0,Nt=xt(lt()),Ge=it;rt=Ue.default=(0,Nt.default)([(0,Ge.jsx)("path",{d:"M12 6c-3.9 0-7 3.1-7 7s3.1 7 7 7 7-3.1 7-7-3.1-7-7-7m3.7 10.9L11 14V8h1.5v5.3l4 2.4z",opacity:".3"},"0"),(0,Ge.jsx)("path",{d:"m22 5.7-4.6-3.9-1.3 1.5 4.6 3.9zM12.5 8H11v6l4.7 2.9.8-1.2-4-2.4zM12 4c-5 0-9 4-9 9s4 9 9 9 9-4 9-9-4-9-9-9m0 16c-3.9 0-7-3.1-7-7s3.1-7 7-7 7 3.1 7 7-3.1 7-7 7M7.9 3.4 6.6 1.9 2 5.7l1.3 1.5z"},"1")],"AccessAlarmsTwoTone");const Tt=()=>{const[_,ie]=r.useState([]),[ce,h]=r.useState(!1),[b,E]=r.useState(""),[j,te]=r.useState(""),[K,p]=r.useState(!1),[c,R]=r.useState(10),[ue,ae]=r.useState(!1),[se,T]=r.useState(!1),[M,fe]=r.useState({}),[D,N]=r.useState({}),[W,re]=r.useState({});ee(o=>o.masterData);const de=ee(o=>o.appSettings),A=o=>{switch(y(),o){case"CREATE":ae(!1);break;case"UPDATE":T(!1);break;case"VIEW":p(!1);break}},[Ae,z]=r.useState(0),Te=o=>{const g=o.target.value;R(g),z(0)},q=(o,g)=>{z(isNaN(g)?0:g)},Ce=o=>{switch(o){case"CREATE":ae(!0);break;case"UPDATE":T(!0);break;case"VIEW":p(!0);break}};let P=(o,g)=>{switch(o){case"DELETE":E("CONFIRMDELETE"),U(C=>({...C,currentNotification:`Confirm delete SLA ${g==null?void 0:g.processName}?`,success:!1,open:!0,title:"Confirm Delete",severity:"warning"}))}},y=()=>{h(!0);let o=C=>{ie(C.data),h(!1)},g=()=>{};Y(`/${oe}/sla/getAll`,"get",o,g)},Se=o=>{let g=G=>{X(Ee=>({...Ee,currentNotification:`SLA ${j} Deleted successfully.`,success:!0,open:!0,title:"Success",severity:"success"})),y()},C=()=>{E("ERROR"),U(G=>({...G,currentNotification:`Failed to Delete SLA ${j}`,success:!1,open:!0,title:"Error",severity:"danger"}))};Y(`/${oe}/sla/delete/${j}`,"delete",g,C)},pe=[{field:"ruleId",headerName:"Process Name",editable:!1,hide:!0},{field:"processName",headerName:"Process Name",editable:!1,width:200},{field:"serviceName",headerName:"Service Name",editable:!1,width:200,hide:!1},{field:"slaType",headerName:"SLA Type",editable:!1,width:200,hide:!1,renderCell:o=>{var C;return d(Re,{sx:{display:"flex",flexDirection:"row",justifyContent:"center",alignItems:"center"},children:[{notification:e(ft,{color:"primary"}),escalation:e(rt,{color:"danger"})}[(C=o.row.slaType)==null?void 0:C.toLowerCase()],e(u,{variant:"body2",sx:{marginLeft:"6px",display:"flex",justifyContent:"center",alignItems:"center"},children:o.row.slaType})]})}},{field:"masterData",headerName:"Master Data",editable:!1,flex:1,renderCell:o=>d(Fe,{children:[e(u,{variant:"body2",children:(()=>{var G;let C="";switch((G=o.row.masterDataCategory)==null?void 0:G.toLowerCase()){case"company":C=D[o.row.masterData];break;case"supplier":C=M[o.row.masterData];break;case"purchasing group":C=W[o.row.masterData];break}return console.log(C),C})()}),e(u,{variant:"body2",children:o.row.masterData})]})},{field:"sla",headerName:"SLA",editable:!1,flex:1,renderCell:o=>e(u,{variant:"body2",children:`${o.row.sla} ${o.row.slaFormat}`})},{field:"endDate",headerName:"Validity End Date",editable:!1,flex:1,renderCell:o=>me(o.row.endDate).format(de.dateFormat)},{field:"actionItem",headerName:"Action",headerAlign:"center",align:"center",editable:!1,width:150,clickable:!1,renderCell:o=>d(O,{children:[e($e,{title:"View Details",children:e(le,{sx:Pe,onClick:()=>{Ce("VIEW"),te(o.row.ruleId)},children:e(St,{})})}),e($e,{title:"Delete",children:e(le,{sx:Pe,onClick:()=>{te(o.row.ruleId),P("DELETE",o.row)},children:e(Ct,{color:"danger"})})}),e($e,{title:"Edit",children:e(le,{sx:Pe,onClick:()=>{te(o.row.ruleId),Ce("UPDATE")},children:e(ke,{iconName:"Edit"})})})]})}];const[$,U]=r.useState({currentNotification:"",success:!0,open:!1,title:"",severity:""}),[L,X]=r.useState({currentNotification:"",success:!0,open:!1,title:"",severity:""}),x={MessageDialogCancel:()=>{U(o=>({open:!1,currentNotification:"",success:"",title:"",severity:""}))},MessageDialogClickOpen:()=>{U(o=>({...o,open:!0}))},MessageDialogClose:()=>{U(o=>({open:!1,currentNotification:"",success:"",title:"",severity:""})),X(o=>({open:!1,currentNotification:"",success:"",title:"",severity:""}))},messageDialogCloseAndRedirect:()=>{U(o=>({open:!1,currentNotification:"",success:"",title:"",severity:""})),X(o=>({open:!1,currentNotification:"",success:"",title:"",severity:""}))},getHandleOkFunction:()=>{switch(b){case"CONFIRMDELETE":U(o=>({open:!1,currentNotification:"",success:"",title:"",severity:""})),Se();break;case"ERROR":x.MessageDialogClose();break;case"CONFIRMCANCEL":x.MessageDialogClose();default:x.MessageDialogClose()}},viewOkButton:()=>{switch(b){case"CONFIRMDELETE":return!1;case"ERROR":return!1;case"CONFIRMCANCEL":return!1;default:return!1}},viewCancelButton:()=>{switch(b){case"CONFIRMDELETE":return!0;case"ERROR":return!1;case"CONFIRMCANCEL":return!0;default:return!1}},getOkButtonText:()=>{switch(b){case"CONFIRMDELETE":return"Delete";case"ERROR":return"OK";case"CONFIRMCANCEL":return"OK";default:return""}},getHandleCancleFunction:()=>{switch(b){case"CONFIRMDELETE":return x.MessageDialogClose();case"ERROR":return()=>{};case"CONFIRMCANCEL":return x.MessageDialogClose();default:return()=>{}}}};let B=(o,g)=>{switch(o){case"SUCCESS":X(C=>({...C,currentNotification:`SLA ${g} updated successfully.`,success:!0,open:!0,title:"Success",severity:"success"}));break}},De=(o,g)=>{switch(o){case"SUCCESS":X(C=>({...C,currentNotification:`SLA ${g} created successfully.`,success:!0,open:!0,title:"Success",severity:"success"}));break}};return r.useEffect(()=>{y()},[]),d("div",{id:"printScreen",style:ct,children:[e(_e,{dialogState:$.open,openReusableDialog:x.MessageDialogClickOpen,closeReusableDialog:x.MessageDialogCancel,dialogTitle:$.title,dialogMessage:$.currentNotification,handleOk:x.getHandleOkFunction,dialogOkText:x.getOkButtonText(),showOkButton:x.viewOkButton(),showCancelButton:x.viewCancelButton(),dialogSeverity:$.severity,handleDialogReject:x.getHandleCancleFunction}),e(je,{openSnackBar:L.open,alertMsg:L.currentNotification,handleSnackBarClose:x.messageDialogCloseAndRedirect}),e(gt,{sx:{position:"fixed",bottom:0,left:0,right:0,zIndex:1},elevation:2,children:e(pt,{showLabels:!0,className:"container_BottomNav",sx:{display:"flex",justifyContent:"flex-end"},children:e(be,{variant:"contained",sx:{marginLeft:"auto"},onClick:()=>Ce("CREATE"),children:"Create SLA"})})}),ue&&e(yt,{open:ue,controller:De,handleClose:A}),se&&e(Dt,{open:se,handleClose:A,id:j,controller:B}),K&&e(vt,{open:K,handleClose:A,id:j}),d(Fe,{spacing:1,children:[e(S,{container:!0,sx:ht,children:d(S,{item:!0,md:5,sx:mt,children:[e(u,{variant:"h3",children:e("strong",{children:"SLA Configurations"})}),e(u,{variant:"body2",children:"This view displays the existing SLAs and the configurable SLAs."})]})}),e(Fe,{children:e(S,{children:e(S,{container:!0,columns:12,sx:ut,children:e(S,{item:!0,md:12,sx:{position:"relative"},children:e(dt,{tempheight:"calc(100vh - 260px)",width:"100%",stopPropagation_Column:["actionItem"],pageSize:c,page:Ae,onPageSizeChange:Te,rowCount:_.length??0,rows:_,columns:pe,getRowIdValue:"ruleId",hideFooter:!0,checkboxSelection:!1,onPageChange:q,disableSelectionOnClick:!0,isLoading:ce,showCustomNavigation:!0})})})})})]})]})};export{Tt as default};
