import{r as l,q as D,s as Qr,b as el,p as de,aV as tl,cn as rl,K as m,a as r,bX as ll,j as o,C as al,aj as nl,ak as ze,G as d,al as sl,bY as ol,T as u,bZ as il,h as yt,I as ue,b1 as Pt,b_ as cl,R as he,am as dl,g as ul,A as hl,an as fl,f as gl,aq as J,ar as E,E as P,b6 as k,t as b,b8 as Cl,aB as ml,az as pl,aC as Sl,bm as xl,aD as fe,aE as yl,aJ as Me,aK as Oe,aL as Te,aM as Ie,M as Le,V as bt,aF as vt,aG as At,W as wt,X as Et,b7 as Pl,F as bl,c0 as C,P as ge,O as vl,c1 as Al,ab as Fe,co as kt,ai as I,cp as wl,cq as El,cr as kl,cs as Nl,ct as $l,cu as Dl,cv as zl,a_ as Nt}from"./index-17b8d91e.js";import"./utilityImages-067c3dc2.js";import"./react-dropzone-uploader-f23f38ba.js";/* empty css            */import{A as Ml}from"./AttachmentUploadDialog-d151e36a.js";import"./CloudUpload-27b6d63e.js";import"./Delete-9f4d7a45.js";const Kl=()=>{var mt,pt,St;l.useState(!1);const[Ol,$t]=l.useState(!1),[Dt,L]=l.useState("");D(e=>e.appSettings.Format);const g=Qr(),X=el();l.useState(!1);const[zt,A]=l.useState(!1),[Mt,Z]=l.useState(!1),[Ce,Ot]=l.useState(null),[F,Re]=l.useState([]);de.useState(""),l.useState(!1),l.useState("");const[Tl,Tt]=l.useState("");l.useState(!0);const[Il,Be]=l.useState(!1),[Ll,We]=l.useState(!0);l.useState([]),l.useState([]),l.useState([]),l.useState([]),l.useState(!0),l.useState([]),l.useState([]),l.useState(!1);const[Ge,Fl]=l.useState([]);l.useState([]);const[Ve,It]=l.useState({});l.useState([]),l.useState([]),l.useState(!1),l.useState([]);const[Lt,Rl]=l.useState([]);l.useState([]);const[Ft,_e]=l.useState(!1),[Rt,Ue]=l.useState(!1);l.useState(!0),l.useState("sm");const[Bt,Y]=l.useState(!1),[Bl,Ye]=l.useState(!1),[S,Q]=l.useState(""),[z,qe]=l.useState(""),[me,ee]=l.useState(0),[He,pe]=l.useState(10),[je,te]=l.useState(0);l.useState([]),l.useState(null),l.useState(null);const[Wt,Ke]=l.useState(0),[Gt,Je]=l.useState(0);l.useState(!1),l.useState(!1),l.useState(!1),l.useState(!1),l.useState(!1),l.useState(!1),l.useState(""),l.useState("");const[Vt,Xe]=l.useState(!1),[_t,R]=l.useState(""),[Ut,B]=l.useState(),[Ze,Yt]=l.useState(!1),[re,qt]=l.useState(!1);l.useState(null);const q=de.useRef(null),[Se,xe]=l.useState(!1),[Ht,jt]=l.useState(0),[ye,Pe]=l.useState(!1),H=de.useRef(null),j=de.useRef(null),[N,be]=l.useState(""),[M,Kt]=l.useState(""),[v,W]=l.useState(""),[Jt,Xt]=l.useState(0),[ve,Ae]=l.useState(!1),[O,Zt]=l.useState(""),[Qt,er]=l.useState(0),[Qe,tr]=l.useState([]),[rr,le]=l.useState(!1),[lr,ae]=l.useState(!1),[et,ne]=l.useState(!1),[tt,se]=l.useState(!1),[rt,ar]=l.useState([]),[Wl,nr]=l.useState(!0),we=["Create Multiple","Upload Template ","Download Template "],Ee=["Change Multiple","Upload Template ","Download Template "],ke=["Create Single","With Copy","Without Copy"];console.log("newProfitCenterName",v);let sr=D(e=>{var t;return(t=e.userManagement.entitiesAndActivities)==null?void 0:t["Display Material"]}),G=D(e=>e.userManagement.userData);const[lt,at]=l.useState(!1),c=D(e=>e.commonFilter.ProfitCenter);console.log("newCompanyCode",N);const oe=D(e=>e.commonSearchBar.ProfitCenter),nt=D(e=>e.profitCenter.handleMassMode);console.log("formcontroller_SearchBar",oe);const h=D(e=>{var t;return(t=e==null?void 0:e.AllDropDown)==null?void 0:t.dropDown}),V=D(e=>e.AllDropDown.dropDown);console.log("dropDownData",h==null?void 0:h.ProfitCenter);const x={profitCenter:{newProfitCenter:z},companyCode:{newCompanyCode:N},companyCodeCopy:{newCompanyCodeCopy:M},profitCenterName:{newProfitCenterName:v},controllingArea:{newControllingArea:S},controllingAreaDataCopy:{newControllingAreaCopyFrom:O}};console.log("typepc",typeof x);const or=()=>{_e(!0)},ir=()=>{Ue(!0)},Ne=()=>{Ue(!1),ae(!1),se(!1),Q(""),be(""),W(""),qe("")},st=e=>{console.log("first",Ce);const t=a=>{g(I({keyName:"CompCodeBasedOnControllingArea",data:a.body.map((s,i)=>({id:i,companyCodes:s.code,companyName:s.desc,assigned:"X"}))}))},n=a=>{console.log(a)};m(`/${C}/data/getCompCodeBasedOnControllingArea?controllingArea=${e.code}`,"get",t,n)},ot=e=>{console.log("first",e);const t=a=>{g(I({keyName:"CompCode",data:a.body}))},n=a=>{console.log(a)};m(`/${C}/data/getCompCodeBasedOnControllingArea?controllingArea=${e.code}`,"get",t,n)},cr=e=>{console.log("first",e);const t=a=>{g(I({keyName:"ProfitCenter",data:a.body}))},n=a=>{console.log(a)};m(`/${C}/data/getProfitCenterAsPerControllingArea?controllingArea=${e.code}`,"get",t,n)},dr=()=>{ur()},ur=()=>{var f,p,w;if((S==null?void 0:S.code)===void 0||(S==null?void 0:S.code)===""||(M==null?void 0:M.code)===void 0||(M==null?void 0:M.code)===""||v===void 0||v===""||(O==null?void 0:O.code)===void 0||(O==null?void 0:O.code)===""||(z==null?void 0:z.code)===void 0||(z==null?void 0:z.code)===""){se(!1),ae(!0);return}else{if(v.length!==5){se(!0),ae(!1);return}else se(!1);ae(!1)}let e=(f=x==null?void 0:x.controllingArea)==null?void 0:f.newControllingArea.code,t=(p=x==null?void 0:x.companyCodeCopy)==null?void 0:p.newCompanyCodeCopy.code,n=(w=x==null?void 0:x.profitCenterName)==null?void 0:w.newProfitCenterName,a=e.concat("$$","P",t,n);console.log("sendNewProfitCenterData",x),A(!0);const s=y=>{var T,$;A(!1),console.log("dupli",y),y.body.length>0?at(!0):X(`/masterDataCockpit/profitCenter/displayCopyProfitCenter/${($=(T=x==null?void 0:x.profitCenterName)==null?void 0:T.newProfitCenterName)==null?void 0:$.code}`,{state:x})},i=y=>{console.log(y)};m(`/${C}/alter/fetchCoAreaPCDupliChk?ctrlAreaPCToCheck=${a}`,"get",s,i)},hr=()=>{if((S==null?void 0:S.code)===void 0||(S==null?void 0:S.code)===""||(N==null?void 0:N.code)===void 0||(N==null?void 0:N.code)===""||v===void 0||v===""){ne(!1),le(!0);return}else{if(v.length!==5){ne(!0),le(!1);return}else ne(!1);le(!1)}st(S);let e=S.code,t=N.code,n=v,a=e.concat("$$","P",t,n);A(!0);const s=f=>{A(!1),console.log("data",f),f.body.length>0?(console.log("kfdkfgk"),at(!0)):(console.log("gyggu"),X("/masterDataCockpit/profitCenter/newSingleProfitCenter",{state:x}))},i=f=>{console.log(f)};m(`/${C}/alter/fetchCoAreaPCDupliChk?ctrlAreaPCToCheck=${a}`,"get",s,i)},fr=e=>{j.current&&j.current.contains(e.target)||Ae(t=>!t)},$e=()=>{Yt(!1),qt(!1),_e(!1),le(!1),ne(!1),Q(""),be(""),W("")},gr=e=>{if(e.target.value!==null){var t=e.target.value;let n={...c,profitCenterName:t};g(ge({module:"ProfitCenter",filterData:n}))}},Cr=(e,t)=>{{var n=t;let a={...c,controllingArea:n};g(ge({module:"ProfitCenter",filterData:a})),br(a)}},mr=()=>{ir()},pr=()=>{or()},Sr=(e,t)=>{{var n=t;let a={...c,profitCenterGroup:n};g(ge({module:"ProfitCenter",filterData:a}))}},xr=(e,t)=>{{var n=t;let a={...c,segment:n};g(ge({module:"ProfitCenter",filterData:a}))}},yr={"Task ID":"taskId",Status:"status",SalesOrganization:"salesOrg",Division:"division",OldMaterialNumber:"oldMaterialNumber","Lab/Office":"labOffice","Transportation Group":"transportationGroup","Batch management":"batchManagement"},Pr=()=>{const e=n=>{g(I({keyName:"ControllingArea",data:n.body}))},t=n=>{console.log(n)};m(`/${C}/data/getControllingArea`,"get",e,t)},br=e=>{var a;const t=s=>{g(I({keyName:"ProfitCtrGroupSearch",data:s.body}))},n=s=>{console.log(s)};m(`/${C}/data/getProfitCtrGroup?controllingArea=${(a=e==null?void 0:e.controllingArea)==null?void 0:a.code}`,"get",t,n)},it=e=>{console.log("first",Ce);const t=a=>{g(I({keyName:"ProfitCtrGroup",data:a.body}))},n=a=>{console.log(a)};m(`/${C}/data/getProfitCtrGroup?controllingArea=${e.code}`,"get",t,n)},vr=e=>{const t=a=>{g(I({keyName:"Segment",data:a.body}))},n=a=>{console.log(a)};m(`/${C}/data/getSegment`,"get",t,n)},Ar=()=>{let e="Basic Data";const t=a=>{g(wl(a.body))},n=a=>{console.log(a)};m(`/${C}/data/getViewFieldDetails?viewName=${e}`,"get",t,n)},wr=()=>{let e="Indicators";const t=a=>{console.log("profit",a),g(El(a.body))},n=a=>{console.log(a)};m(`/${C}/data/getViewFieldDetails?viewName=${e}`,"get",t,n)},Er=()=>{let e="Comp Codes";const t=a=>{g(kl(a.body))},n=a=>{console.log(a)};m(`/${C}/data/getViewFieldDetails?viewName=${e}`,"get",t,n)},kr=()=>{let e="Address";const t=a=>{g(Nl(a.body))},n=a=>{console.log(a)};m(`/${C}/data/getViewFieldDetails?viewName=${e}`,"get",t,n)},Nr=()=>{let e="Communication";const t=a=>{g($l(a.body))},n=a=>{console.log(a)};m(`/${C}/data/getViewFieldDetails?viewName=${e}`,"get",t,n)},$r=()=>{let e="History";const t=a=>{g(Dl(a.body))},n=a=>{console.log(a)};m(`/${C}/data/getViewFieldDetails?viewName=${e}`,"get",t,n)},Dr=()=>{$t(!0)},zr=e=>{A(!0),console.log(e);const t=new FormData;if([...e].forEach(i=>t.append("files",i)),console.log(nt,"handleMassModePC"),nt==="Change")var n=`/${C}/massAction/getAllProfitCenterFromExcelForMassChange`;else var n=`/${C}/massAction/getAllProfitCenterFromExcel`;m(n,"postformdata",i=>{console.log(i,"example"),A(!1),i.statusCode===200?(Y(!1),g(zl(i==null?void 0:i.body)),R("Create"),B(`${e.name} has been Uploaded Succesfully`),L("success"),We(!1),Ye(!0),Dr(),Be(!0),A(!1),X("/masterDataCockpit/profitCenter/createMultipleProfitCenter")):(Y(!1),R("Create"),Ye(!1),B("Creation Failed"),L("danger"),We(!1),Be(!0),_(),A(!1)),Wr()},i=>{console.log(i)},t)},Mr=e=>{console.log("newselection",e),ar(e);let t=dt.map(s=>s.field);const n=F.filter(s=>e.includes(s.id));let a=[];n.map(s=>{console.log("sssssss",s);let i={};t.forEach(f=>{console.log("yyyyy",s[f]),s[f]!==null&&(i[f]=s[f]||"")}),a.push(i),tr(a),console.log("requiredArrayDetails",a)})};l.useEffect(()=>{Pr(),vr(),Ar(),wr(),Er(),kr(),$r(),Nr(),g(tl({})),g(rl())},[]);const Or=()=>{Tt("")},Tr=e=>{console.log("pcSearchForm",c),Z(!0),e||(ee(0),pe(10),te(0));let t={controllingArea:"",profitCenter:(oe==null?void 0:oe.number)??"",profitCenterName:"",createdBy:"",segment:"",profitCenterGroup:"",top:"1000",skip:e??0};const n=s=>{var w,y,T,$;var i=[];for(let U=0;U<((y=(w=s==null?void 0:s.body)==null?void 0:w.list)==null?void 0:y.length);U++){var f=(T=s==null?void 0:s.body)==null?void 0:T.list[U];{var p={id:Nt(),description:f.Description,controllingArea:f.ControllingArea,companyCode:f.CompanyCode,profitCenter:f.ProfitCenter,profitCenterGroup:f.ProfitCenterGroup,profitCenterName:f.ProfitCenterName,createdBy:f.CreatedBy,segment:f.Segment};i.push(p)}}console.log("rowsss",i),Re(i.reverse()),Z(!1),Ke(i.length),Je(($=s==null?void 0:s.body)==null?void 0:$.count)};let a=s=>{console.log(s)};m(`/${C}/data/getProfitCentersBasedOnAdditionalParams`,"post",n,a,t)},De=new Date,ie=new Date;ie.setDate(ie.getDate()-15),l.useState([ie,De]),l.useState([ie,De]),l.useEffect(()=>{(parseInt(me)+1)*parseInt(He)>=parseInt(je)+1e3&&(ce(je+1e3),te(e=>e+1e3))},[me,He]),console.log("pcsearchform",c);const ce=e=>{var s,i,f;Z(!0),e||(ee(0),pe(10),te(0));let t={controllingArea:((s=c==null?void 0:c.controllingArea)==null?void 0:s.code)??"",profitCenter:(c==null?void 0:c.profitCenter)??"",profitCenterName:(c==null?void 0:c.profitCenterName)??"",createdBy:(c==null?void 0:c.createdBy)??"",segment:((i=c==null?void 0:c.segment)==null?void 0:i.code)??"",profitCenterGroup:((f=c==null?void 0:c.profitCenterGroup)==null?void 0:f.code)??"",top:1e3,skip:e??0};const n=p=>{var $,U,xt;console.log("searchdata",p);var w=[];for(let K=0;K<((U=($=p==null?void 0:p.body)==null?void 0:$.list)==null?void 0:U.length);K++){var y=p==null?void 0:p.body.list[K],T={id:Nt(),description:y.Description,controllingArea:y.ControllingArea,companyCode:y.CompanyCode,profitCenter:y.ProfitCenter,profitCenterGroup:y.ProfitCenterGroup,profitCenterName:y.ProfitCenterName,createdBy:y.CreatedBy,segment:y.Segment};w.push(T)}w.sort((K,Zr)=>Fe(K.createdOn,"DD MMM YYYY HH:mm")-Fe(Zr.createdOn,"DD MMM YYYY HH:mm")),Re(w.reverse()),Z(!1),Ke(w.length),Je((xt=p==null?void 0:p.body)==null?void 0:xt.count)},a=p=>{console.log(p)};m(`/${C}/data/getProfitCentersBasedOnAdditionalParams`,"post",n,a,t)};l.useEffect(()=>{ce()},[]),l.useState([]);const _=()=>{Xe(!0)},ct=()=>{Xe(!1)},Ir=(e,t)=>{ee(t)},Lr=e=>{const t=e.target.value;pe(t),ee(0),te(0)};l.useState(null),l.useState(null);const Fr=()=>{g(vl({module:"ProfitCenter"}))};function Rr(){ce()}l.useState([]),l.useState([]);const[Gl,Br]=l.useState(!1);l.useState(null),l.useState(null),l.useState([]);const Wr=()=>{Br(!1)};l.useState(null),l.useState("");const dt=[{field:"profitCenter",headerName:"Profit Center",editable:!1,flex:1},{field:"profitCenterName",headerName:"Profit Center Name",editable:!1,flex:1},{field:"controllingArea",headerName:"Controlling Area",editable:!1,flex:1},{field:"segment",headerName:"Segment",editable:!1,flex:1},{field:"profitCenterGroup",headerName:"Profit Center Group",editable:!1,flex:1},{field:"createdBy",headerName:"Created By",editable:!1,flex:1}],Gr=Ge.map(e=>{const t=yr[e];return t?{field:t,headerName:e,editable:!1,flex:1}:null}).filter(e=>e!==null),ut=[...dt,...Gr],ht={convertJsonToExcel:()=>{let e=[];ut.forEach(t=>{t.headerName.toLowerCase()!=="action"&&!t.hide&&e.push({header:t.headerName,key:t.field})}),Al({fileName:`Profit Center Data-${Fe(De).format("DD-MMM-YYYY")}`,columns:e,rows:F})},button:()=>r(b,{sx:{textTransform:"capitalize",position:"absolute",right:0,top:0},onClick:()=>ht.convertJsonToExcel(),children:"Download"})};let Vr=l.useRef(null);const _r=async()=>{var e=Qe.map(a=>({profitCenter:a.profitCenter,controllingArea:a.controllingArea}));console.log("downloadPayload",e);let t=a=>{A(!1);const s=URL.createObjectURL(a),i=document.createElement("a");i.href=s,i.setAttribute("download","Profit Center_Mass Change.xls"),document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(s),_(),R("Success"),B("Profit Center_Mass Change.xls has been downloaded successfully"),L("success")},n=a=>{a.message&&(_(),R("Error"),B(`${a.message}`),L("danger"))};m(`/${C}/excel/downloadExcelWithData`,"postandgetblob",t,n,e)},Ur=async()=>{var e=Qe.map(a=>({profitCenter:a.profitCenter,controllingArea:a.controllingArea}));console.log("downloadPayload",e);let t=a=>{const s=URL.createObjectURL(a),i=document.createElement("a");i.href=s,i.setAttribute("download","Profit Center_Mass Create.xls"),document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(s),_(),R("Success"),B("Profit Center_Mass Create.xls has been downloaded successfully"),L("success")},n=a=>{a.message&&(_(),R("Error"),B(`${a.message}`),L("danger"))};m(`/${C}/excel/downloadExcel`,"getblobfile",t,n)},ft=(e,t)=>{t!==0&&(Xt(t),Ae(!1),t===1?mr():t===2&&pr())},Yr=()=>{Y(!0),g(kt("Create"))},qr=()=>{xe(e=>!e)},Hr=e=>{q.current&&q.current.contains(e.target)||xe(t=>!t)},jr=()=>{Pe(e=>!e)},Kr=()=>{Ae(e=>!e)},Jr=e=>{H.current&&H.current.contains(e.target)||Pe(t=>!t)},gt=(e,t)=>{t!==0&&(jt(t),xe(!1),t===1?Yr():t===2&&Ur())},Ct=(e,t)=>{t!==0&&(er(t),Pe(!1),t===1?Xr():t===2&&(rt.length>0?(console.log("selectedRows",rt),A(!0),nr(!1),_r()):console.log("Please select at least one row to download Excel.")))},Xr=()=>{Y(!0),g(kt("Change"))};return r(bl,{children:zt===!0?r(ll,{}):o("div",{ref:Vr,children:[r(al,{dialogState:Vt,openReusableDialog:_,closeReusableDialog:ct,dialogTitle:_t,dialogMessage:Ut,handleDialogConfirm:ct,dialogOkText:"OK",dialogSeverity:Dt}),r("div",{style:{...nl,backgroundColor:"#FAFCFF"},children:o(ze,{spacing:1,children:[o(d,{container:!0,sx:sl,children:[o(d,{item:!0,md:5,sx:ol,children:[r(u,{variant:"h3",children:r("strong",{children:"Profit Center"})}),r(u,{variant:"body2",color:"#777",children:"This view displays the list of Profit Centers"})]}),r(d,{item:!0,md:7,sx:{display:"flex"},children:o(d,{container:!0,direction:"row",justifyContent:"flex-end",alignItems:"center",spacing:0,children:[r(il,{title:"Search for multiple Profit Center numbers separated by comma",handleSearchAction:()=>Tr(),module:"ProfitCenter",keyName:"number",message:"Search Profit Center ",clearSearchBar:Or}),r(yt,{title:"Reload",children:r(ue,{sx:Pt,children:r(cl,{sx:{"&:hover":{transform:"rotate(360deg)",transition:"0.9s"}},onClick:Rr})})}),r(yt,{title:"Export Table",children:r(ue,{sx:Pt,onClick:ht.convertJsonToExcel,children:r(he,{iconName:"IosShare"})})})]})})]}),r(d,{container:!0,sx:dl,children:r(d,{item:!0,md:12,children:o(ul,{className:"filter-accordian",children:[r(hl,{expandIcon:r(fl,{}),"aria-controls":"panel1a-content",id:"panel1a-header",sx:{minHeight:"2rem !important",margin:"0px !important"},children:r(u,{sx:{fontWeight:"700"},children:"Search Profit Center"})}),o(gl,{sx:{padding:"0.5rem 1rem 0.5rem"},children:[o(d,{container:!0,rowSpacing:1,spacing:2,justifyContent:"space-between",alignItems:"center",children:[o(d,{container:!0,spacing:1,sx:{padding:"0rem 1rem 0.5rem"},children:[o(d,{item:!0,md:2,children:[r(u,{sx:J,children:"Profit Center Name"}),r(E,{size:"small",fullWidth:!0,children:r(P,{sx:{fontSize:"12px !important"},fullWidth:!0,size:"small",value:c==null?void 0:c.profitCenterName,onChange:gr,placeholder:"Enter Profit Center"})})]}),o(d,{item:!0,md:2,children:[r(u,{sx:J,children:"Controlling Area"}),r(E,{size:"small",fullWidth:!0,children:r(k,{sx:{height:"31px"},fullWidth:!0,size:"small",value:c==null?void 0:c.controllingArea,onChange:Cr,options:(h==null?void 0:h.ControllingArea)??[],getOptionLabel:e=>e!=null&&e.code?`${e==null?void 0:e.code}-${e==null?void 0:e.desc}`??"":"",renderOption:(e,t)=>r("li",{...e,children:r(u,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>r(P,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"Select Controlling Area"})})})]}),o(d,{item:!0,md:2,children:[r(u,{sx:J,children:"Segment"}),r(E,{fullWidth:!0,size:"small",children:r(k,{sx:{height:"31px"},fullWidth:!0,size:"small",onChange:xr,options:(h==null?void 0:h.Segment)??[],getOptionLabel:e=>e!=null&&e.code?`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??"":"",value:c==null?void 0:c.segment,renderOption:(e,t)=>r("li",{...e,children:r(u,{style:{fontSize:12},children:`${t==null?void 0:t.code} - ${t==null?void 0:t.desc}`})}),renderInput:e=>r(P,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"Select Segment"})})})]}),o(d,{item:!0,md:2,children:[r(u,{sx:J,children:"Profit Center Group"}),r(E,{fullWidth:!0,size:"small",children:r(k,{sx:{height:"31px"},fullWidth:!0,size:"small",onChange:Sr,value:c==null?void 0:c.profitCenterGroup,options:(h==null?void 0:h.ProfitCtrGroupSearch)??[],getOptionLabel:e=>e!=null&&e.code?`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??"":"",renderOption:(e,t)=>r("li",{...e,children:r(u,{style:{fontSize:12},children:`${t==null?void 0:t.code} - ${t==null?void 0:t.desc}`})}),renderInput:e=>r(P,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"Select Profit Center Group"})})})]})]}),r(d,{container:!0,sx:{flexDirection:"row",padding:"0rem 1rem 0.5rem"},gap:1,children:Ge.map((e,t)=>r(d,{item:!0,children:o(ze,{children:[r(u,{sx:{fontSize:"12px"},children:e}),r(k,{sx:J,size:"small",options:Lt??[],getOptionLabel:(n,a)=>{var s,i;return`${(s=n[a])==null?void 0:s.code} - ${(i=n[a])==null?void 0:i.desc}`},placeholder:`Enter ${e}`,value:Ve[e],onChange:(n,a)=>It({...Ve,[e]:a}),renderInput:n=>r(P,{sx:{fontSize:"12px !important"},...n,size:"small",placeholder:`Enter ${e}`,variant:"outlined"})},e[t])]})}))})]}),r(d,{container:!0,style:{display:"flex",justifyContent:"flex-end"},children:o(d,{item:!0,style:{display:"flex",justifyContent:"space-around"},children:[r(b,{variant:"outlined",sx:Cl,onClick:Fr,children:"Clear"}),r(b,{variant:"contained",sx:{...ml,...pl},onClick:()=>ce(),children:"Search"})]})})]})]})})}),r(d,{item:!0,sx:{position:"relative"},children:r(ze,{children:r(Sl,{isLoading:Mt,module:"ProfitCenter",width:"100%",title:"List of Profit Centers ("+Wt+")",rows:F,columns:ut,page:me,pageSize:10,rowCount:Gt??(F==null?void 0:F.length)??0,onPageChange:Ir,onPageSizeChange:Lr,getRowIdValue:"id",hideFooter:!0,checkboxSelection:!0,disableSelectionOnClick:!0,status_onRowSingleClick:!0,onRowsSelectionHandler:Mr,callback_onRowSingleClick:e=>{console.log(e,"params");const t=e.row.profitCenter;X(`/masterDataCockpit/profitCenter/displayProfitCenter/${t}`,{state:e.row})},stopPropagation_Column:"action",status_onRowDoubleClick:!0,showCustomNavigation:!0})})}),xl(sr,"Profit Center","CreatePC")&&(G==null?void 0:G.role)==="Super User"||(G==null?void 0:G.role)==="Finance"?r(fe,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:o(yl,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:Ce,onChange:e=>{Ot(e)},children:[o(Me,{variant:"contained",ref:j,"aria-label":"split button",children:[r(b,{size:"small",variant:"contained",onClick:()=>ft(ke[0],0),children:ke[0]}),r(b,{size:"small","aria-controls":ve?"split-button-menu":void 0,"aria-expanded":ve?"true":void 0,"aria-label":"select action","aria-haspopup":"menu",onClick:Kr,children:r(he,{iconName:"ArrowDropUp",iconColor:"#FFFFFF"})})]}),r(Oe,{sx:{zIndex:1},open:ve,anchorEl:j.current,placement:"top-end",children:r(fe,{style:{width:(mt=j.current)==null?void 0:mt.clientWidth},children:r(Te,{onClickAway:fr,children:r(Ie,{id:"split-button-menu",autoFocusItem:!0,children:ke.slice(1).map((e,t)=>r(Le,{selected:t===Jt-1,onClick:()=>ft(e,t+1),children:e},e))})})})}),o(bt,{open:Ft,onClose:$e,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[o(vt,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[r(u,{variant:"h6",children:"New Profit Center"}),r(ue,{sx:{width:"max-content"},onClick:$e,children:r(At,{})})]}),r(wt,{sx:{padding:".5rem 1rem"},children:o(d,{container:!0,spacing:1,children:[o(d,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[o(u,{children:["Controlling Area",r("span",{style:{color:"red"},children:"*"})]}),r(E,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px"},children:r(k,{sx:{height:"31px"},fullWidth:!0,size:"small",onChange:(e,t)=>{Q(t),st(t),it(t),ot(t)},options:(h==null?void 0:h.ControllingArea)??[],getOptionLabel:e=>e!=null&&e.code?`${e==null?void 0:e.code}-${e==null?void 0:e.desc}`??"":"",value:c==null?void 0:c.costCenterCategory,renderOption:(e,t)=>r("li",{...e,children:r(u,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>r(P,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT CONTROLLING AREA",error:re})})}),re&&r(u,{variant:"caption",color:"error",children:"Please Select a Controlling Area."})]}),o(d,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[o(u,{children:["Profit Center",r("span",{style:{color:"red"},children:"*"})]}),o(E,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px",flexDirection:"row"},children:[r(d,{md:2,children:r(P,{sx:{fontSize:"12px !important",height:"31px"},value:"P",fullWidth:!0,size:"small",editable:!1})}),r(d,{md:5,children:r(k,{sx:{height:"42px"},required:"true",size:"small",onChange:(e,t)=>{be(t)},options:(h==null?void 0:h.CompCode)??[],getOptionLabel:e=>`${e==null?void 0:e.code}-${e==null?void 0:e.desc}`,renderOption:(e,t)=>r("li",{...e,children:r(u,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>r(P,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT COMPANY CODE"})})}),o(d,{md:5,children:[r(P,{sx:{fontSize:"12px !important",height:"31px"},fullWidth:!0,size:"small",value:v,onChange:e=>{let t=e.target.value;if(t.length>0&&t[0]===" ")W(t.trimStart());else{let n=t.toUpperCase();W(n)}},inputProps:{maxLength:5,style:{textTransform:"uppercase"}},placeholder:"Enter Profit Center",required:!0,error:et}),et&&r(u,{variant:"caption",color:"error",children:"Cost Center must be 10 digits"})]})]}),Ze&&r(u,{variant:"caption",color:"error",children:"Please enter a Profit Center."})]}),rr&&r(d,{children:r(u,{style:{color:"red"},children:"Please Enter Mandatory Fields"})}),lt&&r(d,{children:r(u,{style:{color:"red"},children:"*The Profit Center with Controlling Area already exists. Please enter different Profit Center or Controlling Area"})})]})}),o(Et,{sx:{display:"flex",justifyContent:"end"},children:[r(b,{sx:{width:"max-content",textTransform:"capitalize"},onClick:$e,children:"Cancel"}),r(b,{className:"button_primary--normal",type:"save",onClick:hr,variant:"contained",children:"Proceed"})]})]}),o(bt,{open:Rt,onClose:Ne,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[o(vt,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[r(u,{variant:"h6",children:"New Profit Center"}),r(ue,{sx:{width:"max-content"},onClick:Ne,children:r(At,{})})]}),o(wt,{sx:{padding:".5rem 1rem"},children:[o(d,{container:!0,spacing:1,children:[o(d,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[o(u,{children:["Controlling Area",r("span",{style:{color:"red"},children:"*"})]}),r(E,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px"},children:r(k,{sx:{height:"31px"},fullWidth:!0,size:"small",onChange:(e,t)=>{Q(t),ot(t),it(t)},options:(h==null?void 0:h.ControllingArea)??[],getOptionLabel:e=>`${e==null?void 0:e.code}-${e==null?void 0:e.desc}`,renderOption:(e,t)=>r("li",{...e,children:r(u,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>r(P,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT CONTROLLING AREA",error:re})})}),re&&r(u,{variant:"caption",color:"error",children:"Please Select a Controlling Area."})]}),o(d,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[o(u,{children:["Profit Center",r("span",{style:{color:"red"},children:"*"})]}),o(E,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px",flexDirection:"row"},children:[r(d,{md:2,children:r(P,{sx:{fontSize:"12px !important",height:"31px"},value:"P",fullWidth:!0,size:"small",editable:!1})}),r(d,{md:5,children:r(k,{sx:{height:"42px"},required:"true",size:"small",onChange:(e,t)=>{Kt(t)},options:(h==null?void 0:h.CompCode)??[],getOptionLabel:e=>`${e==null?void 0:e.code}-${e.desc}`,renderOption:(e,t)=>r("li",{...e,children:r(u,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t.desc}`})}),renderInput:e=>r(P,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT COMPANY CODE"})})}),o(d,{md:5,children:[r(P,{sx:{fontSize:"12px !important",height:"31px"},fullWidth:!0,size:"small",value:v,onChange:e=>{let t=e.target.value;if(t.length>0&&t[0]===" ")W(t.trimStart());else{let n=t.toUpperCase();W(n)}},inputProps:{maxLength:5,style:{textTransform:"uppercase"}},placeholder:"Enter Profit Center",required:!0,error:tt}),tt&&r(u,{variant:"caption",color:"error",children:"Profit Center must be 10 digits"})]})]}),Ze&&r(u,{variant:"caption",color:"error",children:"Please enter a Profit Center."})]}),r(Pl,{sx:{width:"100%",marginLeft:"2%"},children:r("b",{children:"Copy From"})}),o(d,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[o(u,{children:["Controlling Area",r("span",{style:{color:"red"},children:"*"})]}),r(E,{fullWidth:!0,sx:{margin:".5em 0px"},children:r(k,{sx:{height:"42px"},required:"true",size:"small",onChange:(e,t)=>{Zt(t),cr(t)},options:(V==null?void 0:V.ControllingArea)??[],getOptionLabel:e=>`${e==null?void 0:e.code}-${e==null?void 0:e.desc}`,renderOption:(e,t)=>r("li",{...e,children:r(u,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),error:S==="",renderInput:e=>r(P,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT CONTROLLING AREA"})})})]}),o(d,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[o(u,{children:["Profit Center",r("span",{style:{color:"red"},children:"*"})]}),r(E,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px",flexDirection:"row"},children:r(d,{md:12,children:r(k,{sx:{height:"42px"},required:"true",size:"small",onChange:(e,t)=>{qe(t)},options:(V==null?void 0:V.ProfitCenter)??[],getOptionLabel:e=>`${e==null?void 0:e.code}-${e==null?void 0:e.desc}`,renderOption:(e,t)=>r("li",{...e,children:r(u,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>r(P,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT COMPANY CODE"})})})})]}),lt&&r(d,{children:r(u,{style:{color:"red"},children:"*The Profit Center with Controlling Area already exists. Please enter different Profit Center or Controlling Area"})})]}),lr&&r(d,{children:r(u,{style:{color:"red"},children:"Please Enter Mandatory Fields"})})]}),o(Et,{sx:{display:"flex",justifyContent:"end"},children:[r(b,{sx:{width:"max-content",textTransform:"capitalize"},onClick:Ne,children:"Cancel"}),r(b,{className:"button_primary--normal",type:"save",onClick:dr,variant:"contained",children:"Proceed"})]})]}),o(Me,{variant:"contained",ref:q,"aria-label":"split button",children:[r(b,{size:"small",variant:"contained",onClick:()=>gt(we[0],0),sx:{cursor:"default"},children:we[0]}),r(b,{size:"small","aria-controls":Se?"split-button-menu":void 0,"aria-expanded":Se?"true":void 0,"aria-label":"select action","aria-haspopup":"menu",onClick:qr,children:r(he,{iconName:"ArrowDropUp",iconColor:"#FFFFFF"})})]}),r(Oe,{sx:{zIndex:1},open:Se,anchorEl:q.current,placement:"top-end",children:r(fe,{style:{width:(pt=q.current)==null?void 0:pt.clientWidth},children:r(Te,{onClickAway:Hr,children:r(Ie,{id:"split-button-menu",autoFocusItem:!0,children:we.slice(1).map((e,t)=>r(Le,{selected:t===Ht-1,onClick:()=>gt(e,t+1),children:e},e))})})})}),o(Me,{variant:"contained",ref:H,"aria-label":"split button",children:[r(b,{size:"small",onClick:()=>Ct(Ee[0],0),sx:{cursor:"default"},children:Ee[0]}),r(b,{size:"small","aria-controls":ye?"split-button-menu":void 0,"aria-expanded":ye?"true":void 0,"aria-label":"select action","aria-haspopup":"menu",onClick:jr,children:r(he,{iconName:"ArrowDropUp",iconColor:"#FFFFFF"})})]}),r(Oe,{sx:{zIndex:1},open:ye,anchorEl:H.current,placement:"top-end",children:r(fe,{style:{width:(St=H.current)==null?void 0:St.clientWidth},children:r(Te,{onClickAway:Jr,children:r(Ie,{id:"split-button-menu",autoFocusItem:!0,children:Ee.slice(1).map((e,t)=>r(Le,{selected:t===Qt-1,onClick:()=>Ct(e,t+1),children:e},e))})})})}),Bt&&r(Ml,{artifactId:"",artifactName:"",setOpen:Y,handleUpload:zr})]})}):""]})})]})})};export{Kl as default};
