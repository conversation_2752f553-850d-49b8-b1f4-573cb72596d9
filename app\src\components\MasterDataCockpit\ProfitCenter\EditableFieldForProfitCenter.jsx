import React, { useEffect, useState } from "react";
import {
  <PERSON>ack,
  Typography,
  TextField,
  Grid,
  Autocomplete,
  Checkbox,
} from "@mui/material";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { setPayload } from "../../../app/editPayloadSlice";
import moment from "moment/moment";
import { doAjax } from "../../Common/fetchService";
import { destination_ProfitCenter } from "../../../destinationVariables";
import { setDropDown } from "../../../app/dropDownDataSlice";
import { setPCRequiredFields } from "../../../app/profitCenterTabsSlice";

function transformApiData(apiValue, dropDownData) {
  if (Array.isArray(dropDownData)) {
    const matchingOption = dropDownData.find(
      (option) => option.code === apiValue
    );
    return matchingOption || "";
  } else {
    return "";
  }
}
const EditableFieldForProfitCenter = ({
  label,
  value,
  length,
  data,
  visibility,
  onSave,
  isEditMode,
  type,
  taskRequestId
}) => {
  const [editedValue, setEditedValue] = useState(value);
  const [changeStatus, setChangeStatus] = useState(false);
  const dropDownData = useSelector((state) => state.AllDropDown.dropDown);
  const dispatch = useDispatch();
  const transformedValue = transformApiData(editedValue, dropDownData);
  const editField = useSelector((state) => state.edit.payload);
  const appSettings = useSelector((state) => state.appSettings);
  let taskDetails = useSelector((state) => state.userManagement.taskData);
  const [error,setError]=useState(false);
  const requestId = taskDetails?.subject;
  console.log("editField",label,value, data );

  const fieldData = {
    label,
    value: editedValue,
    type,
    visibility
  };

  const handleSave = () => {
    onSave(fieldData);
  };

  let key = label
    .replaceAll("(", "")
    .replaceAll(")", "")
    .replaceAll("/", "")
    .replaceAll("-", "")
    .replaceAll(".", "")
    .split(" ")
    .join("");
  
  useEffect(() => {
      if (
        visibility === "0" ||
        visibility === "Required"
      ) {
        dispatch(setPCRequiredFields(key));
      }
  }, []);

  useEffect(() => {
    setEditedValue(value);
    if (label === "Analysis Period From" || label === "Analysis Period To" ) {
      setEditedValue(parseInt(value.replace("/Date(", "").replace(")/", "")));
    }
  }, [value]);

  const onEdit = (newValue) => {
    dispatch(
      setPayload({
        keyname: key
          .replaceAll("(", "")
          .replaceAll(")", "")
          .replaceAll("/", "")
          .replaceAll("-", "")
          .replaceAll(".", "")
          .split(" ")
          .join(""),
        data: newValue,
      })
    );
  };

  useEffect(() => {
    if (label === "Analysis Period From" || label === "Analysis Period To" || label === "Created On") {
      const timestamp = parseInt(value.replace("/Date(", "").replace(")/", ""));
      dispatch(
        setPayload({
          keyname: key
            .replaceAll("(", "")
            .replaceAll(")", "")
            .replaceAll("/", "")
            .replaceAll("-", "")
            .replaceAll(".", "")
            .split(" ")
            .join(""),
          data: timestamp,
        })
      );
    }
  }, []);
  const getRegionBasedOnCountry = (value) => {
    const hSuccess = (data) => {
      console.log("value", data);
      dispatch(setDropDown({ keyName: "Region", data: data.body }));
    };
    const hError = (error) => {
      console.log(error, "error in dojax");
    };
    doAjax(
      `/${destination_ProfitCenter}/data/getRegionBasedOnCountry?country=${value?.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  console.log("editedvalue", editedValue);

  return (
    <>
      {!isEditMode ? (
        (taskRequestId && visibility === "Hidden") ||
        (requestId && visibility === "Hidden") ? null : (
          <Grid item>
            <Stack>
              <Typography variant="body2" color="#777">
                {label}
                {visibility === "Required" || visibility === "0" ? (
                  <span style={{ color: "red" }}>*</span>
                ) : (
                  ""
                )}
              </Typography>
              <Typography variant="body2" fontWeight="bold">
                {label === "Analysis Period From" ||
                label === "Analysis Period To"
                  ? moment(data[key]).format(appSettings?.dateFormat)
                  : data[key]}
                {type === "Radio Button" ? (
                  <Checkbox sx={{ padding: 0 }} checked={data[key]} disabled />
                ) : (
                  ""
                )}
              </Typography>
            </Stack>
          </Grid>
        )
      ) : visibility === "Hidden" ? null : (
        <Grid item>
          <Stack>
            {isEditMode ? (
              <div>
                <Typography variant="body2" color="#777">
                  {label}{" "}
                  {visibility === "Required" || visibility === "0" ? (
                    <span style={{ color: "red" }}>*</span>
                  ) : (
                    ""
                  )}
                </Typography>
                {type === "Drop Down" ? (
                  <Autocomplete
                    options={dropDownData[key] ?? []}
                    value={
                      (data[key] &&
                        dropDownData[key]?.filter(
                          (x) => x.code === data[key]
                        ) &&
                        dropDownData[key]?.filter(
                          (x) => x.code === data[key]
                        )[0]) ||
                      ""
                    }
                    onChange={(event, newValue,reason) => {
                      if (label === "Country/Reg.") {
                        getRegionBasedOnCountry(newValue);
                      }
                      onEdit(newValue?.code);
                      setEditedValue(newValue?.code);
                      setChangeStatus(true);

                      if (reason === "clear") {
                        onEdit("");
                      } else {
                        onEdit(newValue?.code);
                      }
                      if(visibility === "Required" || visibility === "0"){
                        //alert("coming")
                        if(newValue.length === null)
                        setError(true)
                      }
                    }}
                    getOptionLabel={(option) => {
                      console.log("optionn", option);
                      return option === "" || option?.code === ""
                        ? ""
                        : `${option?.code} - ${option?.desc}` ?? "";
                    }}
                    renderOption={(props, option) => {
                      return (
                        <li {...props}>
                          <Typography style={{ fontSize: 12 }}>
                            {`${option?.code} - ${option?.desc}`}
                          </Typography>
                        </li>
                      );
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        variant="outlined"
                        placeholder={`Select ${fieldData.label}`}
                        size="small"
                        label={null}
                        error={error}
                      />
                    )}
                  />
                ) : type === "Input" ? (
                  <TextField
                    variant="outlined"
                    size="small"
                    fullWidth
                    value={data[key].toUpperCase()}
                    // inputProps={{ maxLength: props.field.maxLength }}
                    placeholder={`Enter ${fieldData.label}`}
                    inputProps={{ maxLength: length }}
                    onChange={(event) => {
                      const newValue = event.target.value;
                      if (newValue.length > 0 && newValue[0] === " ") {
                        onEdit(newValue.trimStart());
                      } else {
                        //let costCenterValue = e.target.value;
                        let costCenterUpperCase = newValue.toUpperCase();
                        onEdit(costCenterUpperCase);
                      }
                      if(visibility === "Required" || visibility === "0"){
                        if(newValue.length <= 0)
                        setError(true)
                      }
                      // onEdit(newValue);
                      setEditedValue(newValue.toUpperCase());
                    }}
                    error={error}
                  />
                ) : type === "Calendar" ? (
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DatePicker
                      slotProps={{ textField: { size: "small" } }}
                      value={data[key]}
                      placeholder="Select Date Range"
                      maxDate={new Date(9999, 12, 31)}
                      onChange={(newValue) => {
                        onEdit(newValue);
                        setEditedValue(newValue);
                      }}
                      // required={
                      //   props.details.visibility === "0" ||
                      //   props.details.visibility === "Required"
                      // }
                    />
                  </LocalizationProvider>
                ) : type === "Radio Button" ? (
                  <Grid item md={2}>
                    <Checkbox
                      sx={{ padding: 0 }}
                      checked={data[key]}
                      onChange={(event, newValue) => {
                        onEdit(newValue);
                        setEditedValue(newValue);
                      }}
                    />
                  </Grid>
                ) : (
                  ""
                )}
              </div>
            ) : (
              ""
            )}
          </Stack>
        </Grid>
      )}
    </>
  );
};

export default EditableFieldForProfitCenter;
