import { PRICE_CTRL_DATA } from "@constant/enum";
import { createSlice } from "@reduxjs/toolkit";


const initialState = {
  dropDown: {
    VarPurOrderUnitActive: [
      { code: "0", desc: "Not Active" },
      { code: "1", desc: "Active" },
      { code: "2", desc: "Active with Own Price" },
    ],
    PriceCtrl: PRICE_CTRL_DATA,
    QualFFreeGoodDisc: [
      { code: "0", desc: "Not eligible for Discount in Kind" },
      {
        code: "1",
        desc: "Eligible for Discount in Kind for Purchasing And Sales",
      },
      { code: "2", desc: "Eligible for Discount in Kind only for Purchasing" },
      { code: "3", desc: "Eligible for Discount in Kind only for Sales" },
    ],
    VarTransportationGroup: [
      { code: "NSTG", desc: "No Spec Trans Grp" },
      { code: "55555", desc: "test" },
      { code: "1600", desc: "Etoposide" },
      { code: "0001", desc: "PFZR - On pallets" },
      { code: "0002", desc: "SPC Air" },
      { code: "0009", desc: "Narcotics" },
      { code: "1000", desc: "Refrigerated 150lbs" },
      { code: "1010", desc: "Refrigerated 12 lbs" },
      { code: "1050", desc: "Refrigerated 60lbs" },
      { code: "1060", desc: "FREEZE (-25C-15C)" },
      { code: "1080", desc: "Refrigerated" },
      { code: "1081", desc: "Biologic Cold Storage" },
      { code: "1100", desc: "Vault Item 150lbs" },
      { code: "1150", desc: "Vault Item 60lbs" },
      { code: "1200", desc: "Aerosol 150lbs" },
      { code: "1250", desc: "Aerosol 60lbs" },
    ],
    VarMolecule: [
      { code: "TIO", desc: "Tiotropium - Bronchodilator" },
      { code: "TOF", desc: "Tofacitinib - JAK Inhibitor" },
      { code: "TOC", desc: "Tocopheryl - Vitamin E" },
      { code: "TIZ", desc: "Tizanidine - Muscle Relaxant" },
      { code: "TOB", desc: "Tobramycin - Antibiotic" },
      { code: "TOL", desc: "Tolazamide - Anti-diabetic Agent" },
    ],
    VarSalt: [
      { code: "ACETATE", desc: "Acetate" },
      { code: "LACTATE", desc: "Lactate" },
      { code: "LYSINE", desc: "Lysine" },
      { code: "MAGNESIUM", desc: "Magnesium" },
      { code: "MALEATE", desc: "Maleate" },
      { code: "MEGLUMINE", desc: "Meglumine" },
      { code: "MESYLATE", desc: "Mesylate" },
      { code: "MONOFUMARATE", desc: "Monofumarate" },
      { code: "MONONITRATE", desc: "Mononitrate" },
      { code: "NAPISILATE", desc: "Napsilate" },
      { code: "NITRATE", desc: "Nitrate" },
      { code: "NONE_SPECIFIED", desc: "None specified" },
      { code: "OCTASULFATE", desc: "Octasulfate" },
      { code: "OROTATE", desc: "Orotate" },
      { code: "OXALATE", desc: "Oxalate" },
    ],
     pharmacopoeiaData : [
      { code: "BP", desc: "British Pharmacopoeia" },
      { code: "CHP", desc: "Chinese Pharmacopoeia" },
      { code: "EP", desc: "European Pharmacopoeia" },
      { code: "IH", desc: "In House Specification" },
      { code: "IP", desc: "Indian Specification" },
      { code: "JP", desc: "Japanese Pharmacopoeia" },
      { code: "PH_FR", desc: "French Pharmacopoeia" },
      { code: "PH.EUR", desc: "European Pharmacopoeia" },
    ],  
    
    polymorphicFormData : [
      { code: "FORM_XIV", desc: "FORM XIV" },
      { code: "FORM_Y", desc: "FORM Y" },
      { code: "FORM_B", desc: "FORM-B" },
      { code: "FORM_B1", desc: "FORM-B1" },
      { code: "FORM_D", desc: "FORM-D" },
      { code: "FORM_E", desc: "FORM-E" },
      { code: "FORM_VII", desc: "FORM VII" },
      { code: "FORM_VI", desc: "FORM VI" },
      { code: "FORM_V2", desc: "FORM V2" },
      { code: "FORM_V", desc: "FORM V" },
      { code: "FORM_RACEMIC", desc: "FORM RACEMIC" },
      { code: "FORM_POLYCRYSTALLINE", desc: "FORM POLYCRYSTALLINE" },
      { code: "FORM_P", desc: "FORM P" },
      { code: "FORM_F", desc: "FORM-F" },
      { code: "FORM_IV", desc: "FORM IV" },
    ],
    stageFormData : [
      { code: "BL", desc: "BL - BLENDED" },
      { code: "CO", desc: "CO - COMPACTED" },
      { code: "GR", desc: "GR - GRANULATION" },
      { code: "MI", desc: "MI - MICRONIZED" },
      { code: "MM", desc: "MM - MULTI-MILLED" },
      { code: "PU", desc: "PU - PULVERIZED" },
      { code: "OTHER", desc: "Other - OTHER" },
    ],
  },
};

export const dropDownDataSlice = createSlice({
  name: "AllDropDown",
  initialState,
  reducers: {
    setDropDown: (state, action) => {
      state.dropDown[action.payload.keyName] = action.payload.data;
      return state;
    },
    setDependentDropdown : (state,action) =>{
      if(!state.dropDown[action.payload.keyName]) state.dropDown[action.payload.keyName] = {};
      state.dropDown[action.payload.keyName][action.payload.keyName2] = action.payload.data;
      return state
    }
  },
});

export const { setDropDown, setDependentDropdown } = dropDownDataSlice.actions;

export default dropDownDataSlice.reducer;
