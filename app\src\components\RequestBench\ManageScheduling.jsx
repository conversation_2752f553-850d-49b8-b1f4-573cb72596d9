import { doAjax } from "@components/Common/fetchService";
import { END_POINTS } from "@constant/apiEndPoints";
import moment from "moment";
import { forwardRef, useImperativeHandle, useState } from "react";
import { useSelector } from "react-redux";
import CustomDataGrid from ".././MasterDataCockpit/ScheduleSyndication/CustomGrid";
import { destination_Admin, destination_MaterialMgmt } from "../.././destinationVariables";
import { Box, Button, Dialog, DialogActions, DialogContent, DialogTitle, IconButton, styled, Tooltip, tooltipClasses, Typography } from "@mui/material";
import InfoIcon from "@mui/icons-material/Info";
import useLogger from "@hooks/useLogger";
import { useNavigate } from "react-router-dom";
import { REQUEST_TYPE } from "@constant/enum";
import useChangePayloadCreation from "@hooks/useChangePayloadCreation";


const ManageScheduling = forwardRef((props, ref) => {
    const [priorityRow, setPriorityRow] = useState([]);
    const payloadData = useSelector((state) => state.payload);
    const templateName = payloadData?.payloadData?.TemplateName || "";
    const [openScheduledMessageDialog, setOpenScheduledMessageDialog] = useState(false);
    const [openPriorityDialog, setOpenPriorityDialog] = useState(false);
    const { changePayloadForTemplate } = useChangePayloadCreation(templateName);
    const rbSearchForm = useSelector(
        (state) => state.commonFilter["RequestBench"]
      );
    const { customError } = useLogger();
    const navigate = useNavigate();
    useImperativeHandle(ref, () => ({
        handlePriorityDialogClickOpen
    }));
    const paginationData = useSelector((state) => state.paginationData);
    const priorityColumns = [
    {
      field: "id",
      headerName:"",
      editable: false,
      flex: 1,
      hide:true,
      // width: "10%",
    },
    {
      field: "reqId",
      headerName: "Request ID",
      editable: false,
      flex: 2.5,
      // width: "20%",
    },
    {
      field: "module",
      headerName: "Module",
      editable: false,
      flex: 2,
      // width: "20%",
    },
    {
      field: "reqType",
      headerName: "Request Type",
      editable: false,
      flex: 1.5,
      // width: "10%",
    },
    {
      field: "scheduledBy",
      headerName: "Scheduled By",
      editable: false,
      flex: 4,
      // width: "15%",
    },
    {
      field: "scheduledOn",
      headerName: "Scheduled On",
      editable: false,
      flex: 1,
      // width: "10%",
    },
    {
      field: "totalObjects",
      headerName: "Total Objects",
      editable: false,
      flex: 1,
      renderHeader: () => (
        <Tooltip title="Objects count scheduled for SAP syndication" arrow>
          <span>Request ID</span>
        </Tooltip>
      ),
      // width: "10%",
    },
    {
      field: "pendingObjects",
      headerName: "Pending Objects",
      editable: false,
      flex: 1,
      renderHeader: () => (
        <Tooltip title="Objects count pending for SAP syndicated." arrow>
          <span>Request ID</span>
        </Tooltip>
      ),
      // width: "10%",
    },
    {
      field: "objectSuccessCount",
      headerName: "Success Objects",
      editable: false,
      flex: 1,
      renderHeader: () => (
        <Tooltip title="Objects count syndicated in SAP" arrow>
          <span>Request ID</span>
        </Tooltip>
      ),
      // width: "10%",
    },
    {
      field: "objectFailureCount",
      headerName: "Failure Objects",
      editable: false,
      flex: 1,
      renderHeader: () => (
        <Tooltip title="Objects count failed during syndication in SAP" arrow>
          <span>Request ID</span>
        </Tooltip>
      ),
      // width: "10%",
    },
    {
      field: "retryCount",
      headerName: "Retry Count",
      editable: false,
      flex: 1,
      renderHeader: () => (
        <Tooltip title="Number of times request retriggered.(Max- 3 count, after that it wouldnt be picked & set as status- Retry Count Exceeded)" arrow>
          <span>Request ID</span>
        </Tooltip>
      ),
      // width: "10%",
    },
    {
      field: "schedulerStatus",
      headerName: "Scheduler Status",
      editable: false,
      flex: 1,
      // width: "10%",
    },
    {
      field: "action",
      headerName: "Action",
      editable: false,
      flex: 1,
      // width: "10%",
    }
  ];
  const handlePriorityDialogClickOpen = () => {
    getPriority()
    // setOpenPriorityDialog(true);
  };
  const getPriority = () => {
    const todayDate = moment().format("YYYY-MM-DDTHH:mm:ss.SSSZ");

    const requestBody = {
      modules: "Material,Cost Center,Profit Center,CC-PC Combo,General Ledger",
      requestTypes: "Create with Upload,Change with Upload,Extend with Upload",
      statuses: "Scheduler - Failed,Scheduler - Pending,Scheduler - Partially Completed", // Map user-selected status to appropriate statuses
      toDate: moment( rbSearchForm?.createdOn[1]
        ) .utc() // Convert to UTC (Zulu Time)
        .format("YYYY-MM-DDTHH:mm:ss") ?? "",
      fromDate: moment( rbSearchForm?.createdOn[0]
        )
        .utc() // Convert to UTC (Zulu Time)
        .format("YYYY-MM-DDTHH:mm:ss") ?? "",
      // requestType: "Create with Upload",
    };
    const hSuccess = (data) => {
      // setIsLoading(false);
      if (data.statusCode === 200) {
      
        let priorityRequests = data?.data || [];
        setOpenPriorityDialog(true);
        let priorityRow = priorityRequests?.map((item, index) => {
          return {
            id: index,
            schedulerId: item?.SchedulerId,
            reqId: item?.RequestId,
            reqType: item?.RequestType,
            priority: item?.Priority,
            scheduledBy: item?.ReqScheduledBy,
            scheduledOn: item?.ReqScheduledOn,
            totalObjects: item?.ObjectCount,
            pendingObjects: item?.PendingObjectsCount,
            retryCount: item?.RetryCount,
            module: item?.Module,
            objectSuccessCount: item?.ObjectsSuccessCount,
            objectFailureCount: item?.ObjectsFailureCount,
            updatedOn: item?.ReqUpdatedOn,
            schedulerStatus: item?.SchedulerStatus,
          };
        });

        const existingRowsInGroup = priorityRow?.filter(
          (item) =>
            item.module === "Material" && item.reqType === "Create with Upload"
        );

        const maxPriority =
          existingRowsInGroup?.length > 0
            ? Math.max(...existingRowsInGroup.map((item) => item.priority))
            : 0;
        var newRequestRow = {
          id: priorityRow.length + 1,
          schedulerId: "",
          reqId: props.taskData?.requestId || props.taskData?.ATTRIBUTE_1 ||  "",
          reqType: props?.taskData?.ATTRIBUTE_2 ?? "Create with Upload",
          priority: maxPriority + 1,
          scheduledBy: props.userData?.emailId ?? "",
          scheduledOn: todayDate ?? "",
          totalObjects: paginationData?.totalElements || 0 ,
          pendingObjects: paginationData?.totalElements || 0,
          retryCount: 0,
          module: "Material",
          objectSuccessCount: 0,
          objectFailureCount: 0,
          updatedOn: todayDate ?? "",
          schedulerStatus: "Scheduler - Pending",
        };
      
        let newRows = [...priorityRow, newRequestRow];
        const sortedRows = newRows.sort((a, b) => {
          if (a.module !== b.module) {
            return a.module.localeCompare(b.module); // Sort by module
          }
          if (a.reqType !== b.reqType) {
            return a.reqType.localeCompare(b.reqType); // Sort by reqType
          }
          return a.priority - b.priority; // Sort by priority
        });
        setPriorityRow(sortedRows);
      } else {
        props.setDialogTitle("Error");
        props.setSuccessMsg(false);
        props.setMessageDialogMessage(
          "Failed Fetching Scheduled Requests. Try Once more."
        );
        props.setMessageDialogSeverity("danger");
        // setMessageDialogOK(false);
        // setMessageDialogExtrasetMessageDialogExtra(true);
        // handleErrorDialogClickOpen();
        // setIsLoading(false);
        props.handleMessageDialogClickOpen();
      }
    };
    const hError = (error) => {
      // hSuccess(data)
      customError(error);
    };
    doAjax(
      `/${destination_Admin}${END_POINTS.DATA?.FETCH_SCHEDULERS_IN_REQ_BENCH}`,
      "post",
      hSuccess,
      hError,
      requestBody
    );
  };
  const handlePriorityDialogClose = () => {
    setOpenScheduledMessageDialog(false);
    setOpenPriorityDialog(false);
  };
  const handleRowUpdate = (updatedRows) => {    
    setPriorityRow(updatedRows);
  };
  const handleApproveMassCostCenterForScheduled = (button) => {
    const requestUrl = props.requestType === REQUEST_TYPE.CREATE_WITH_UPLOAD ? `/${destination_MaterialMgmt}${END_POINTS.MASS_ACTION.CREATE_MAT_APPROVED}` : props.requestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD ? `/${destination_MaterialMgmt}${END_POINTS.MASS_ACTION.EXTEND_MAT_APPROVED}` : `/${destination_MaterialMgmt}${END_POINTS.MASS_ACTION.CHANGE_MAT_APPROVED}`;
    handlePriorityDialogClose()
    props.setBlurLoading(true);
    let payload = props?.requestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD ? changePayloadForTemplate(true) : props.createPayloadFromReduxState(payloadData)
    const updatedPayload = payload?.map(item => ({
      ...item,
      Tochildrequestheaderdata: {
        ...item?.Tochildrequestheaderdata,
        IsScheduled: true,
      },
      // CostCenter: {
      //   ...item?.CostCenter,
      //   IsScheduled: true,
      // }
    }));
    const hSuccess = (data) => {
      // let data = {statusCode:201}
      props.setBlurLoading(false);
      if (data.statusCode === 200 || data.statusCode === 201) {
        // setMessageDialogMessage(`${button.MDG_DYN_BTN_SNACKBAR_MSG}`);
        props.setMessageDialogSeverity("success");
        // setBlurLoading(false);
        props.setSuccessMsg(true);
        // handleSnackBarOpen();
        // handleAttachment();
        // setTimeout(() => {
        //   navigate("/masterDataCockpit/sunoco/CostandProfitCenter");
        // }, 3000);
        onChangePriority();
      } else {
        props.setDialogTitle("Error");
        props.setSuccessMsg(false);
        props.setTextInput(false);
        props.setMessageDialogMessage(data?.message ?? "Failed Submitting Request");
        props.setMessageDialogSeverity("danger");
        // setMessageDialogOK(false);
        // setMessageDialogExtra(true);
        props.setBlurLoading(false);
        // handleErrorDialogClickOpen();
        props.handleMessageDialogClickOpen();
      }
    };
    const hError = (error) => {
      console.log("error");
    };
    doAjax(
      requestUrl,
      "post",
      hSuccess,
      hError,
      updatedPayload
    );
  };
  var payloadForScheduler = priorityRow?.map((y,index)=>{
   
    return{
        SchedulerId: y?.schedulerId ?? "",
        RequestId: y?.reqId,
        RequestType: y?.reqType,
        Module: y?.module,
        Priority: y?.priority,
        ObjectCount: y?.totalObjects,
        ObjectsSuccessCount: y?.objectSuccessCount,
        ObjectsFailureCount: y?.objectFailureCount,
        PendingObjectsCount: y?.pendingObjects,
        ReqScheduledBy: y?.scheduledBy,
        ReqScheduledOn: y?.scheduledOn,
        ReqUpdatedOn: y?.updatedOn,
        SchedulerStatus: y?.schedulerStatus,
        RetryCount:y?.retryCount
        }
  })
  const onChangePriority = () => {
   
    const hSuccess = (data) => {
      // let data = {statusCode:202}
      if (data.statusCode === 200) {
        props.setMessageDialogSeverity("success");
        props.setMessageDialogMessage("Request has been submitted and scheduled for Syndication.");
        props.setBlurLoading(false);
        props.setSuccessMsg(true);
        props.handleSnackBarOpen();
        setTimeout(() => {
          // navigate("/masterDataCockpit/sunoco/CostandProfitCenter");
          navigate("/masterDataCockpit/materialMaster/material");
        }, 3000);
      }
      else if(data?.statusCode === 400){
        props.setDialogTitle("Info");
        props.setSuccessMsg(false);
        props.setMessageDialogMessage(data?.message);
        props.setTextInput(false);
        props.setMessageDialogSeverity("info");
        props.setMessageDialogOK(false);
        props.setBlurLoading(false);
        props.handleErrorDialogClickOpen();
        setTimeout(() => {
          navigate("/requestBench");
        }, 3000);
      } else {
        props.setDialogTitle("Error");
        props.setSuccessMsg(false);
        props.setMessageDialogMessage("Failed Scheduling Request, You will be redirected to Request Bench for Scheduling it.");
        props.setTextInput(false);
        props.setMessageDialogSeverity("danger");
        props.setMessageDialogOK(false);
        props.setBlurLoading(false);
        props.handleErrorDialogClickOpen();
        setTimeout(() => {
          navigate("/requestBench");
        }, 3000);
      }
      // handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}${END_POINTS?.DATA?.UPDATE_PRIORITY}`,
      "post",
      hSuccess,
      hError,
      payloadForScheduler
    );
  };
  const NoMaxWidthTooltip = styled(({ className, ...props }) => (
    <Tooltip {...props} classes={{ popper: className }} />
  ))({
    [`& .${tooltipClasses.tooltip}`]: {
      maxWidth: "none",
    },
  });
  return (
    <Dialog
      open={openPriorityDialog}
      onClose={handlePriorityDialogClose}
      fullWidth
      maxWidth="xl"
      PaperProps={{
        sx: {
          borderRadius: 3,
          boxShadow: 8,
          backgroundColor: "#ffffff",
        },
      }}
    >
      {/* Dialog Header */}
      <Box
        sx={{
          px: 3,
          py: 2,
          borderBottom: "1px solid #e0e0e0",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          backgroundColor: "#F4F6FA",
          borderTopLeftRadius: 12,
          borderTopRightRadius: 12,
        }}
      >
        <Typography variant="h6" fontWeight={600} color="text.primary">
          List of Requests Scheduled
        </Typography>
        <NoMaxWidthTooltip
          arrow
          title={
            <Typography fontSize={12}>
              Here you can prioritize your requests
            </Typography>
          }
        >
          <IconButton size="small">
            <InfoIcon fontSize="small" />
          </IconButton>
        </NoMaxWidthTooltip>
      </Box>

      {/* Dialog Content */}
      <DialogContent sx={{ px: 3, py: 2 }}>
        <Box
          sx={{
            border: "1px solid #e0e0e0",
            borderRadius: 2,
            overflow: "hidden",
          }}
        >
          <CustomDataGrid
            columns={priorityColumns}
            row={priorityRow}
            onRowUpdate={handleRowUpdate}
            selectionType={"SAPScheduler"}
            showDragIcon={true}
          />
        </Box>
      </DialogContent>

      {/* Action Buttons */}
      <DialogActions
        sx={{
          px: 3,
          py: 2,
          backgroundColor: "#FAFAFA",
          borderTop: "1px solid #e0e0e0",
          borderBottomLeftRadius: 12,
          borderBottomRightRadius: 12,
        }}
      >
        <Button
          onClick={handlePriorityDialogClose}
          variant="outlined"
          color="primary"
          sx={{ textTransform: "capitalize", minWidth: 100 }}
        >
          Cancel
        </Button>
        <Button
          onClick={() =>
            handleApproveMassCostCenterForScheduled(props.currentButtonState)
          }
          variant="contained"
          color="primary"
          sx={{
            textTransform: "capitalize",
            minWidth: 100,
          }}
        >
          Submit
        </Button>
      </DialogActions>
    </Dialog>


  );
});

export default ManageScheduling;