import {
  Box,
  Grid,
  IconButton,
  Tabs,
  <PERSON>po<PERSON>,
  Stack,
  Paper,
  BottomNavigation,
  Button,
  CardContent,
  Stepper,
  Step,
  StepLabel,
} from "@mui/material";
import Tab from "@mui/material/Tab";
import React, { useState, useEffect } from "react";
import {
  iconButton_SpacingSmall,
  outermostContainer,
  container_Padding,
  outerContainer_Information,
  button_Primary,
  outermostContainer_Information,
  button_Outlined,
} from "../../common/commonStyles";
import { useSelector, useDispatch } from "react-redux";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import { useNavigate, useLocation } from "react-router-dom";
// import EditableField from "./EditFieldForDisplay";
import { doAjax } from "../../Common/fetchService";
import {
  destination_CostCenter,
  destination_MaterialMgmt,
  destination_ProfitCenter,
} from "../../../destinationVariables";
//   import EditableFieldForCostCenter from "./EditableFieldForCostCenter";
//   import EditableFieldForMassCostCenter from "./EditFieldForMassCostCenter";
import ChangeLog from "../../Changelog/ChangeLog";
import moment from "moment";
import EditableField from "../EditFieldForDisplay";
import EditableFieldForProfitCenter from "./EditableFieldForProfitCenter";
import EditFieldForMassProfitCenter from "./EditFieldForMassProfitCenter";
import TrackChangesTwoToneIcon from "@mui/icons-material/TrackChangesTwoTone";
import { setDropDown } from "../../../app/dropDownDataSlice";
import CompCodesProfitCenter from "../ProfitCenterTabs/CompCodeProfitCenter";
import { setPayloadWhole } from "../../../app/editPayloadSlice";
import { formValidator } from "../../../functions";
import ReusableSnackBar from "../../Common/ReusableSnackBar";
import { clearProfitCenter } from "../../../app/profitCenterTabsSlice";
//   import EditableField from "./EditFieldForDisplay";

const DisplayMultipleProfitCenterRequestBench = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [dropDownData, setDropDownData] = useState({});
  const [value, setValue] = useState(0);
  const [tablevalue, setTableValue] = useState([]);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isDisplayMode, setIsDisplayMode] = useState(true);
  let [factorsArray, setFactorsArray] = useState([]);
  const [materialDetails, setMaterialDetails] = useState([]);
  const [dispCompCode, setDispCompCode] = useState([]);
  const [activeStep, setActiveStep] = useState(0);
  const [IDs, setIDs] = useState();
  const allTabs = useSelector((state) => state.tabsData);
  const [isChangeLogopen, setisChangeLogopen] = useState(false);
  const [formValidationErrorItems, setFormValidationErrorItems] = useState([]); //chiranjit
  const [openSnackbarValidation, setOpenSnackbarValidation] = useState(false); //chiranjit
  const reference = {
    basicData: "Basic Data",
  };
  const location = useLocation();
  const MultipleProfitCenter = useSelector(
    (state) => state.profitCenter.MultipleProfitCenterRequestBench
  );
  const tabsData = location.state.tabsData;
  const selectedRowData = location.state.rowData;
  const massProfitRowData = location.state.requestbenchRowData;
  let taskData = useSelector((state) => state?.initialData?.IWMMyTask);
  console.log(taskData, "taskData_in_mass");
  console.log(massProfitRowData, "========massProfitRowData===========");
  console.log("selectedrowdata", selectedRowData, tabsData);
  const compcodeData =
    tabsData.viewData["Comp Codes"][
      "Company Code Assignment for Profit Center"
    ];
  console.log("commpcodedata", compcodeData);
  const PayloadData = useSelector((state) => state.payload);
  let singlePCPayloadAfterChange = useSelector((state) => state.edit.payload);
  console.log(singlePCPayloadAfterChange, "singlePCPayloadAfterChange");
  //console.log(multipleProfitCenterData, "singlePCPayloadAfterChange");
  let requiredFieldTabWise= useSelector((state) => state.profitCenter.requiredFields);
  console.log(requiredFieldTabWise,"required_field_for_data") //chiranjit
  const compCodesTabDetails = useSelector(
    (state) => state.profitCenter.profitCenterCompCodes
  );
  //const requestNumber = location.state.requestNumber;
  //const requestNumber = useSelector((state) => state.userManagement?.taskData?.subject.slice(3));
  //console.log("requestNumber", requestNumber, tabsData);
  /*let requestNumber=''
  if (location.state.requestNumber !== undefined || location.state.requestNumber !== null){
    requestNumber = location.state.requestNumber;
  }else{
    requestNumber = useSelector((state) => state.userManagement.taskData.subject.slice(3));
  }*/
  let task = useSelector((state) => state.userManagement.taskData);
  let profitCenterMultiple = useSelector(
    (state) => state.profitCenter.MultipleProfitCenterData
  );
  let userData = useSelector((state) => state.userManagement.userData);
  console.log(profitCenterMultiple, "profitCenterMultiple");
  console.log(task, "task_in_mass=========================");
  console.log(task?.processDesc, "task?.processDesc");
  console.log(task?.subject, "task?.subject");
  console.log(massProfitRowData?.requestId, "massProfitRowData?.requestId");
  console.log(massProfitRowData?.requestType, "massProfitRowData?.requestType");
  let requestNumber = "";
  let controllingArea = "";
  let profitCentername = "";
  if (task?.processDesc === "Mass Change") {
    requestNumber = task?.subject
      ? task?.subject?.slice(3)
      : massProfitRowData?.requestId.slice(3);
    controllingArea = taskData?.body?.controllingArea
      ? ""
      : selectedRowData.controllingArea;
    profitCentername = task?.body?.profitCenter
      ? ""
      : selectedRowData.profitCenter;
  } else if (task?.processDesc === "Mass Create") {
    requestNumber = task?.subject
      ? task?.subject?.slice(3)
      : massProfitRowData?.requestId.slice(3);
    controllingArea = taskData?.body?.controllingArea
      ? ""
      : selectedRowData.controllingArea;
    profitCentername = task?.body?.profitCenter
      ? ""
      : selectedRowData.profitCenter;
  } else if (massProfitRowData?.requestType === "Mass Create") {
    requestNumber = task?.subject
      ? task?.subject?.slice(3)
      : massProfitRowData?.requestId.slice(3);
    controllingArea = taskData?.body?.controllingArea
      ? ""
      : selectedRowData.controllingArea;
    profitCentername = task?.body?.profitCenter
      ? ""
      : selectedRowData.profitCenter;
  } else if (massProfitRowData?.requestType === "Mass Change") {
    requestNumber = task?.subject
      ? task?.subject?.slice(3)
      : massProfitRowData?.requestId.slice(3);
    controllingArea = taskData?.body?.controllingArea
      ? ""
      : selectedRowData.controllingArea;
    profitCentername = task?.body?.profitCenter
      ? ""
      : selectedRowData.profitCenter;
  }

  //console.log(requestNumber,controllingArea,profitCentername,"================")

  let activeRow = {};
  let activeIndex = -1;
  for (let index = 0; index < MultipleProfitCenter?.length; index++) {
    if (
      MultipleProfitCenter[index].profitCenter === selectedRowData.profitCenter
    ) {
      activeRow = MultipleProfitCenter[index];
      activeIndex = index;
      break;
    }
  }
  const handleChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleBack = () => {
    //setActiveStep((prevActiveStep) => prevActiveStep - 1);
    const isValidation = handleCheckValidationError();
    if(isEditMode){
      if (isValidation) {
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
        dispatch(clearProfitCenter());
      } else {
        handleSnackBarOpenValidation();
      }
    }else{
      setActiveStep((prevActiveStep) => prevActiveStep - 1);
      dispatch(clearProfitCenter());
    }
    
  };
  const handleNext = () => {
    //setActiveStep((prevActiveStep) => prevActiveStep + 1);
    const isValidation = handleCheckValidationError();
    if(isEditMode){   
      if (isValidation) {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
        dispatch(clearProfitCenter());
      } else {
        handleSnackBarOpenValidation();
      }
    }else{
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
      dispatch(clearProfitCenter());
    }
  };
  const handleSnackBarOpenValidation = () => {
    setOpenSnackbarValidation(true);
  };

  const tabsArray = Object.entries(tabsData?.viewData)
    .filter((item) => typeof item[1] == "object" && item[1] != null)
    .map((item) => item[0]);

  const tabContents = Object.entries(tabsData.viewData)
    .filter((item) => typeof item[1] == "object" && item[1] != null)
    .map((item) => Object.entries(item[1]));

    const tempHash = {}; //chiranjit
    const gridArr = tabContents.map(item => {
      item.forEach((eachTab, i) => {
        eachTab.forEach((eachItem, i) => {
          if (i !== 0) {
            eachItem.forEach(fieldItem => {
              tempHash[fieldItem.fieldName.
               replaceAll("(", "")
              .replaceAll(")", "")
              .replaceAll("/", "")
              .replaceAll("-", "")
              .replaceAll(".", "")
              .split(" ")
              .join("")] =  fieldItem.value;
            });
          }
        });
      });
    });

    console.log('tabcontentsnow', tabContents, tempHash)
  const onEdit = () => {
    setIsEditMode(true);
    setIsDisplayMode(false);
  };
  const handleClosemodalData = (data) => {
    setisChangeLogopen(data);
  };
  const openChangeLog = () => {
    setisChangeLogopen(true);
  };

  const getProfitCenterGroupBasedOnControllingArea = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ProfitCtrGroup", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/data/getProfitCtrGroup?controllingArea=${selectedRowData.controllingArea}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getRegion = (value) => {
    console.log("compcode", value);
    const hSuccess = (data) => {
      console.log("value", data);
      dispatch(setDropDown({ keyName: "Region", data: data.body }));
    };
    const hError = (error) => {
      console.log(error, "error in dojax");
    };
    doAjax(
      `/${destination_ProfitCenter}/data/getRegionBasedOnCountry?country=${value}`,
      "get",
      hSuccess,
      hError
    );
  };

  useEffect(()=>{ //chiranjit
    //console.log(tabContents,"tabContentsinuseEffect")
    
    //console.log(gridArr,"gridArr");
    dispatch(setPayloadWhole(tempHash));
  },[])


  useEffect(() => {
    setDispCompCode(
      _.zip(
        compcodeData[0].value,
        compcodeData[1].value,
        compcodeData[2].value
      ).map((item, index) => {
        return {
          id: index,
          companyCodes: item[0]?.split("$$$")[0]
          ? item[0]?.split("$$$")[0]
          : "",
          companyName: item[1]?.split("$$$")[0]
          ? item[1]?.split("$$$")[0]
          : "",
          assigned: item[2] ? item[2] : "",
        };
      })
    );
    getProfitCenterGroupBasedOnControllingArea();
    getRegion(
      tabsData?.viewData["Address"]["Address Data"].find(
        (field) => field?.fieldName === "Country/Reg."
      )?.value
    );
  }, []);
  console.log(singlePCPayloadAfterChange,requiredFieldTabWise,"requiredFieldTabWise")
  const handleCheckValidationError = () => {
    //chiranjit
    return formValidator(
      singlePCPayloadAfterChange,
      requiredFieldTabWise,
      setFormValidationErrorItems
    );
  };
  const handleSnackBarCloseValidation = () => {
    setOpenSnackbarValidation(false);
  };

  console.log(
    requestNumber,
    controllingArea,
    profitCentername,
    "================"
  );
  console.log("tabcontents", tabsData);
  return (
    <div>
      <Grid
        container
        style={{
          ...outermostContainer_Information,
          backgroundColor: "#FAFCFF",
        }}
      >
        {formValidationErrorItems.length != 0 && (
            <ReusableSnackBar
              openSnackBar={openSnackbarValidation}
              alertMsg={
                "Please fill the following Field: " +
                formValidationErrorItems.join(", ")
              }
              handleSnackBarClose={handleSnackBarCloseValidation}
            />
          )}
        <Grid sx={{ width: "inherit" }}>
          <Grid item md={12} style={{ padding: "16px", display: "flex" }}>
            {/* <Grid  sx={{ display: "flex" }}> */}

            <Grid style={{ display: "flex", justifyContent: "flex-end" }}>
              <IconButton
                // onClick={handleBacktoRO}
                color="primary"
                aria-label="upload picture"
                component="label"
                sx={iconButton_SpacingSmall}
              >
                <ArrowCircleLeftOutlinedIcon
                  style={{ height: "1em", width: "1em", color: "#000000" }}
                  onClick={() => {
                    //navigate("/masterDataCockpit/profitCenter/massProfitCenterTableRequestBench");
                    navigate(-1);
                  }}
                />
              </IconButton>
            </Grid>

            <Grid md={10}>
              <Typography variant="h3">
                <strong>
                  Multiple Profit Center : {selectedRowData.profitCenter}{" "}
                </strong>
              </Typography>
              <Typography variant="body2" color="#777">
                This view displays details of uploaded Profit Center
              </Typography>
            </Grid>
            <Grid
              md={1}
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                marginRight: "4px",
              }}
            >
              <Button
                variant="outlined"
                size="small"
                sx={button_Outlined}
                onClick={openChangeLog}
                title="Chnage Log"
              >
                <TrackChangesTwoToneIcon
                  sx={{ padding: "2px" }}
                  fontSize="small"
                />
              </Button>
            </Grid>
            {isChangeLogopen && (
              <ChangeLog
                open={true}
                closeModal={handleClosemodalData}
                requestId={requestNumber}
                requestType={"Mass"}
                pageName={"profitCenter"}
                controllingArea={selectedRowData.controllingArea}
                centerName={selectedRowData.profitCenter}
              />
            )}

            {!isEditMode ? (
              userData?.role === "Finance" ? (
                <Grid
                  md={1}
                  sx={{ display: "flex", justifyContent: "flex-end" }}
                >
                  <Grid item>
                    <Button
                      variant="outlined"
                      size="small"
                      sx={button_Outlined}
                      onClick={onEdit}
                    >
                      Change
                      <EditOutlinedIcon
                        sx={{ padding: "2px" }}
                        fontSize="small"
                      />
                    </Button>
                  </Grid>
                </Grid>
              ) : (
                ""
              )
            ) : (
              ""
            )}
          </Grid>
          <Grid container display="flex" flexDirection="row" flexWrap="nowrap">
            <Box width="70%" sx={{ marginLeft: "40px" }}>
              <Grid item sx={{ paddingTop: "2px !important" }}>
                <Stack flexDirection="row">
                  <div style={{ width: "15%" }}>
                    <Typography variant="body2" color="#777">
                      Profit Center
                    </Typography>
                  </div>
                  <Typography
                    variant="body2"
                    fontWeight="bold"
                    justifyContent="flex-start"
                  >
                    : {selectedRowData.profitCenter}
                  </Typography>
                </Stack>
              </Grid>

              <Grid item sx={{ paddingTop: "2px !important" }}>
                <Stack flexDirection="row">
                  <div style={{ width: "15%" }}>
                    <Typography variant="body2" color="#777">
                      Controlling Area
                    </Typography>
                  </div>
                  <Typography variant="body2" fontWeight="bold">
                    : {selectedRowData.controllingArea}
                  </Typography>
                </Stack>
              </Grid>
            </Box>
          </Grid>

          <Grid container style={{ padding: "16px" }}>
            <Stepper
              activeStep={activeStep}
              onChange={handleChange}
              variant="scrollable"
              sx={{
                background: "#FFFFFF",
                borderBottom: "1px solid #BDBDBD",
                width: "100%",
                height: "48px",
              }}
              aria-label="mui tabs example"
            >
              {tabsArray.map((factor, index) => (
                <Step key={factor}>
                  <StepLabel sx={{ fontWeight: "700" }}>{factor}</StepLabel>
                </Step>
              ))}
            </Stepper>

            <Grid
              key={tabContents}
              container
              item
              md={12}
              sx={{
                backgroundColor: "white",
                maxHeight: "max-content",
                height: "max-content",
                // borderRadius: "8px",
                // border: "1px solid #E0E0E0",
                mt: 1,
                // boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                // padding: "10px",
                mb: 1,
              }}
            >
              {tabContents &&
                tabContents[activeStep]?.map((item, index) => {
                  return activeStep === 2 ? (
                    <CompCodesProfitCenter
                      compCodesTabDetails={compCodesTabDetails}
                      displayCompCode={dispCompCode}
                    />
                  ) : (
                    <Box key={index} sx={{ width: "100%" }}>
                      <Grid
                        item
                        md={12}
                        sx={{
                          backgroundColor: "white",
                          maxHeight: "max-content",
                          height: "max-content",
                          borderRadius: "8px",
                          border: "1px solid #E0E0E0",
                          mt: 0.25,
                          boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                          ...container_Padding,
                          // ...container_columnGap,
                        }}
                      >
                        <Grid container>
                          <Typography
                            sx={{
                              fontSize: "12px",
                              fontWeight: "700",
                              margin: "0px !important",
                            }}
                          >
                            {item[0]}
                          </Typography>
                        </Grid>
                        <Box>
                          <Box sx={{ width: "100%" }}>
                            <CardContent
                              sx={{
                                padding: "0",
                                paddingBottom: "0 !important",
                                paddingTop: "10px !important",
                              }}
                            >
                              <Grid
                                container
                                style={{
                                  display: "grid",
                                  gridTemplateColumns: "repeat(6,1fr)",
                                  gap: "15px",
                                }}
                                justifyContent="space-between"
                                alignItems="flex-start"
                                md={12}
                              >
                                {[...item[1]].map((innerItem) => {
                                  console.log("inneritem", item[1]);
                                  return (
                                    <EditFieldForMassProfitCenter
                                      activeTabIndex={activeStep}
                                      fieldGroup={item[0]}
                                      selectedRowData={
                                        selectedRowData.profitCenter
                                      }
                                      pcTabs={tabsArray}
                                      label={innerItem.fieldName}
                                      value={innerItem.value}
                                      length={innerItem.maxLength}
                                      visibility={innerItem.visibility}
                                      onSave={(newValue) =>
                                        handleFieldSave(
                                          innerItem.fieldName,
                                          newValue
                                        )
                                      }
                                      isEditMode={isEditMode}
                                      // isExtendMode={isExtendMode}
                                      type={innerItem.fieldType}
                                      field={innerItem} // Update the type as needed
                                    />
                                  );
                                })}
                              </Grid>
                            </CardContent>
                          </Box>
                        </Box>
                      </Grid>
                    </Box>
                  );
                })}
            </Grid>
          </Grid>
        </Grid>
      </Grid>

      {isEditMode ? (
        <Paper
          sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
          elevation={2}
        >
          <BottomNavigation
            className="container_BottomNav"
            showLabels
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              gap: 1,
            }}
            value={value}
            onChange={(newValue) => {
              setValue(newValue);
            }}
          >
            <Button
              size="small"
              variant="contained"
              onClick={() => {
                navigate(-1);
              }}
            >
              Save
            </Button>
            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary, mr: 1 }}
              onClick={handleBack}
              disabled={activeStep === 0}
            >
              Back
            </Button>
            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary, mr: 1 }}
              onClick={handleNext}
              disabled={activeStep === tabsArray.length - 1 ? true : false}
            >
              Next
            </Button>
          </BottomNavigation>
        </Paper>
      ) : (
        <Paper
          sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
          elevation={2}
        >
          <BottomNavigation
            className="container_BottomNav"
            showLabels
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              gap: 1,
            }}
            value={value}
            onChange={(newValue) => {
              setValue(newValue);
            }}
          >
            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary, mr: 1 }}
              onClick={handleBack}
              disabled={activeStep === 0}
            >
              Back
            </Button>
            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary, mr: 1 }}
              onClick={handleNext}
              disabled={activeStep === tabsArray.length - 1 ? true : false}
            >
              Next
            </Button>
          </BottomNavigation>
        </Paper>
      )}
    </div>
  );
};

export default DisplayMultipleProfitCenterRequestBench;
