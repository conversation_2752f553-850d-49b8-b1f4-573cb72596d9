import { Box, Grid, Tab, Tabs, Typography } from "@mui/material";
import React, { useState } from "react";
import { outermostContainer_Information } from "../Common/commonStyles";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import TabContext from "@mui/lab/TabContext/TabContext";
import TabList from "@mui/lab/TabList/TabList";
import TabPanel from "@mui/lab/TabPanel/TabPanel";
import { useNavigate, useParams } from "react-router-dom";
import DescriptionTab from "./AdditionalDataTabs/DescriptionTab";
import UnitsOfMeasureTab from "./AdditionalDataTabs/UnitsOfMeasureTab";
import AdditionalEANSTab from "./AdditionalDataTabs/AdditionalEANSTab";
import BasicDataTextsTab from "./AdditionalDataTabs/BasicDataTextsTab";
import ConsumptionTab from "./AdditionalDataTabs/ConsumptionTab";
import DisplayDescriptionTab from "./AdditionalDataTabs/DisplayDescriptionTab";
import DisplayUnitsOfMeasureTab from "./AdditionalDataTabs/DisplayUnitsOfMeasureTab";
import { useSelector } from "react-redux";
const DisplayAdditionalData = () => {
  const navigate = useNavigate();
  // const dispatch = useDispatch();
  let displayData = useSelector((state) => state?.tabsData?.additionalData);
  const { reqId } = useParams();
  const [value, setValue] = useState("1");
  const [activeTab, setActiveTab] = useState(0);
  console.log("displayData",displayData);
  const additionalDataTabs = [
    "Description",
    "Units of Measure",
    // "Additional EANs",
    // "Document Data",
    // "Basic Data Texts",
    // "Inspection Text",
    // "Internal Comment",
    // "Consumption",
  ];
  const tabContents = [
    [
      <>
        <DisplayDescriptionTab />
      </>,
    ],
    [
      <>
        <DisplayUnitsOfMeasureTab />
      </>,
    ],
   
  ];

  const handleChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  return (
    <div>
      <Grid
        container
        style={{
          ...outermostContainer_Information,
          backgroundColor: "#FAFCFF",
        }}
      >
        <Grid sx={{ width: "inherit" }}>
          <Grid item md={7} style={{ padding: "16px", display: "flex" }}>
            <div style={{ display: "flex" }}>
              <div>
                <ArrowCircleLeftOutlinedIcon
                  style={{ height: "1em", width: "1em", color: "#000000" }}
                  onClick={() => {
                    navigate(-1);
                  }}
                />
              </div>
              <div>
                <Typography variant="h3">
                  <strong>Display Additional Details</strong>
                </Typography>
              </div>
            </div>
          </Grid>

          <Grid container>
            <Tabs
              value={activeTab}
              onChange={handleChange}
              variant="scrollable"
              sx={{
                background: "#FFF",
                borderBottom: "1px solid #BDBDBD",
                width: "100%",
              }}
              aria-label="mui tabs example"
            >
              {additionalDataTabs.map((factor, index) => (
                <Tab
                  sx={{ fontSize: "12px", fontWeight: "700" }}
                  key={index}
                  label={factor}
                />
              ))}
            </Tabs>

            {/* Display the cards of the currently active tab */}
            {tabContents[activeTab].map((cardContent, index) => (
              <Box key={index} sx={{ mb: 2, width: "100%" }}>
                <Typography variant="body2">{cardContent}</Typography>
              </Box>
            ))}
          </Grid>
        </Grid>
      </Grid>
    </div>
  );
};

export default DisplayAdditionalData;
