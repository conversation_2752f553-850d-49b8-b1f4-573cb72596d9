import{r as h,q as A,s as L,cx as G,bI as $,a as t,G as F,j as u,T as C,ab as W,ay as O,x as I,b6 as K,E as T,at as N,au as _,F as J,K as M,c0 as Q,ai as X}from"./index-75c1660a.js";import{D as Y}from"./DatePicker-31fef6b6.js";function Z(a,n){return Array.isArray(n)&&n.find(l=>l.code===a)||""}const ee=({label:a,value:n,length:R,data:l,visibility:c,onSave:v,isEditMode:S,type:o,taskRequestId:z})=>{var k,q;const[D,i]=h.useState(n),[b,B]=h.useState(!1),f=A(e=>e.AllDropDown.dropDown),g=L();Z(D,f),A(e=>e.edit.payload);const x=A(e=>e.appSettings);let P=A(e=>e.userManagement.taskData);const[E,y]=h.useState(!1),U=P==null?void 0:P.subject;console.log("editField",a,n,l);const j={label:a,value:D,type:o,visibility:c};let s=a.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("");h.useEffect(()=>{(c==="0"||c==="Required")&&g(G(s))},[]),h.useEffect(()=>{i(n),(a==="Analysis Period From"||a==="Analysis Period To")&&i(parseInt(n.replace("/Date(","").replace(")/","")))},[n]);const d=e=>{g($({keyname:s.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""),data:e}))};h.useEffect(()=>{if(a==="Analysis Period From"||a==="Analysis Period To"||a==="Created On"){const e=parseInt(n.replace("/Date(","").replace(")/",""));g($({keyname:s.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""),data:e}))}},[]);const H=e=>{const r=m=>{console.log("value",m),g(X({keyName:"Region",data:m.body}))},p=m=>{console.log(m,"error in dojax")};M(`/${Q}/data/getRegionBasedOnCountry?country=${e==null?void 0:e.code}`,"get",r,p)};return console.log("editedvalue",D),t(J,{children:S?c==="Hidden"?null:t(F,{item:!0,children:t(I,{children:S?u("div",{children:[u(C,{variant:"body2",color:"#777",children:[a," ",c==="Required"||c==="0"?t("span",{style:{color:"red"},children:"*"}):""]}),o==="Drop Down"?t(K,{options:f[s]??[],value:l[s]&&((k=f[s])==null?void 0:k.filter(e=>e.code===l[s]))&&((q=f[s])==null?void 0:q.filter(e=>e.code===l[s])[0])||"",onChange:(e,r,p)=>{a==="Country/Reg."&&H(r),d(r==null?void 0:r.code),i(r==null?void 0:r.code),B(!0),d(p==="clear"?"":r==null?void 0:r.code),(c==="Required"||c==="0")&&r.length===null&&y(!0)},getOptionLabel:e=>(console.log("optionn",e),e===""||(e==null?void 0:e.code)===""?"":`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??""),renderOption:(e,r)=>t("li",{...e,children:t(C,{style:{fontSize:12},children:`${r==null?void 0:r.code} - ${r==null?void 0:r.desc}`})}),renderInput:e=>t(T,{...e,variant:"outlined",placeholder:`Select ${j.label}`,size:"small",label:null,error:E})}):o==="Input"?t(T,{variant:"outlined",size:"small",fullWidth:!0,value:l[s].toUpperCase(),placeholder:`Enter ${j.label}`,inputProps:{maxLength:R},onChange:e=>{const r=e.target.value;if(r.length>0&&r[0]===" ")d(r.trimStart());else{let p=r.toUpperCase();d(p)}(c==="Required"||c==="0")&&r.length<=0&&y(!0),i(r.toUpperCase())},error:E}):o==="Calendar"?t(N,{dateAdapter:_,children:t(Y,{slotProps:{textField:{size:"small"}},value:l[s],placeholder:"Select Date Range",maxDate:new Date(9999,12,31),onChange:e=>{d(e),i(e)}})}):o==="Radio Button"?t(F,{item:!0,md:2,children:t(O,{sx:{padding:0},checked:l[s],onChange:(e,r)=>{d(r),i(r)}})}):""]}):""})}):z&&c==="Hidden"||U&&c==="Hidden"?null:t(F,{item:!0,children:u(I,{children:[u(C,{variant:"body2",color:"#777",children:[a,c==="Required"||c==="0"?t("span",{style:{color:"red"},children:"*"}):""]}),u(C,{variant:"body2",fontWeight:"bold",children:[a==="Analysis Period From"||a==="Analysis Period To"?W(l[s]).format(x==null?void 0:x.dateFormat):l[s],o==="Radio Button"?t(O,{sx:{padding:0},checked:l[s],disabled:!0}):""]})]})})})};export{ee as E};
