import {
    Backdrop,
    BottomNavigation,
    Box,
    <PERSON>ton,
    Card,
    CircularProgress,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    FormControl,
    Grid,
    IconButton,
    Paper,
    Stack,
    TextField,
    Tooltip,
    Typography,
  } from "@mui/material";
  import React, { useEffect, useState } from "react";
  import {
    iconButton_SpacingSmall,
    button_Primary,
    button_Outlined,
    outermostContainer,
    outermostContainer_Information,
  } from "../../Common/commonStyles";
  import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
  import ReusableTable from "../../Common/ReusableTable";
  import { useLocation, useNavigate } from "react-router-dom";
  import { useDispatch, useSelector } from "react-redux";
  import { checkIwaAccess, idGenerator } from "../../../functions";
  import {
      destination_BankKey,
    destination_DocumentManagement,
  } from "../../../destinationVariables";
  import { Timeline } from "rsuite";
  
  import {
    TimelineConnector,
    TimelineContent,
    TimelineDot,
    TimelineItem,
    TimelineSeparator,
    timelineItemClasses,
  } from "@mui/lab";
  import { doAjax } from "../../Common/fetchService";
  import ReusableSnackBar from "../../Common/ReusableSnackBar";
  import moment from "moment/moment";
  import ReusableDialog from "../../Common/ReusableDialog";
  import CloseIcon from "@mui/icons-material/Close";
  import lookup from "../../../data/lookup.json";
  import LoadingComponent from "../../Common/LoadingComponent";
  import { setDropDown } from "../../../app/dropDownDataSlice";
  import { MatDownload, MatView } from "../../DocumentManagement/UtilDoc";
  import AttachFileOutlinedIcon from "@mui/icons-material/AttachFileOutlined";
  import { CheckCircleOutlineOutlined } from "@mui/icons-material";
  import ReusableAttachementAndComments from "../../Common/ReusableAttachmentAndComments/ReusableAttachementAndComments";
import { clearBankKey, setMultipleBankKeyData } from "../../../app/bankKeyTabSlice";
import { payloadSlice } from "../../../app/payloadslice";
  
  const MassBKTableRequestBench = () => {
    const [isLoading, setIsLoading] = useState(true);
    const [value, setValue] = useState("1");
    const [selectedRows, setSelectedRows] = useState([]);
    let [factorsArray, setFactorsArray] = useState([]);
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const location = useLocation();
    const massBKRowData = location.state;
    const [messageDialogMessage, setMessageDialogMessage] = useState("");
    const [openSnackbar, setopenSnackbar] = useState(false);
    const [messageDialogExtra, setMessageDialogExtra] = useState(false);
    const [messageDialogTitle, setMessageDialogTitle] = useState(false);
    const [messageDialogSeverity, setMessageDialogSeverity] = useState(false);
    const [messageDialogOK, setMessageDialogOK] = useState(true);
    const [successMsg, setsuccessMsg] = useState(false);
    const [openMessageDialog, setOpenMessageDialog] = useState(false);
    const [openCorrectionDialog, setOpenCorrectionDialog] = useState(false);
    const [openRemarkDialog, setOpenRemarkDialog] = useState(false);
    const [remarks, setRemarks] = useState("");
    const [testRun, setTestRun] = useState(false);
    const [approveDisabled, setApproveDisabled] = useState(true);
    const [submitForReviewDisabled, setSubmitForReviewDisabled] = useState(true);
    const [validationStatus, setValidationStatus] = useState(true);
    const [openDialog, setOpenDialog] = useState(false);
    const [attachments, setAttachments] = useState([]);
    const [comments, setComments] = useState([]);
    const [validateFlag, setValidateFlag] = useState(false);
    const [blurLoading, setBlurLoading] = useState(false);
    const [bkNumber, setBkNumber] = useState("");
    const [dialogOpen, setDialogOpen] = useState(false);
    const [bkValidationErrors, setBKValidationErrors] = useState([]);
    const [testrunStatus, setTestrunStatus] = useState(true);
    const [remarksValidationError,setRemarksValidationError]=useState(false);
    console.log(massBKRowData,"massBKRowData")
    console.log("mouse", selectedRows);
    let task = useSelector((state) => state.userManagement.taskData);
    let userData = useSelector((state) => state.userManagement.userData);
    let iwaAccessData = useSelector(
      (state) => state.userManagement.entitiesAndActivities?.["Bank Key"]
    );
    // let taskRowDetails = useSelector((state) => state.userManagement.taskData);
    const appSettings = useSelector((state) => state.appSettings);
    const multipleBankKeyData = useSelector(
      (state) => state.bankKey.MultipleBankKeyData
    );
    const MultipleBankKey = useSelector(
      (state) => state.profitCenter.MultipleBankKey
    );
    const handleSnackBarOpen = () => {
      setopenSnackbar(true);
    };
    const handleSnackBarClose = () => {
      if (validateFlag) {
        setopenSnackbar(false);
        setValidateFlag(false);
      } else {
        setopenSnackbar(false);
        navigate("/masterDataCockpit/BankKey");
      }
    };
    const handleMessageDialogClickOpen = () => {
      setOpenMessageDialog(true);
    };
  
    const handleMessageDialogClose = () => {
      setOpenMessageDialog(false);
    };
  
    const handleMessageDialogNavigate = () => {
      // navigate("/masterDataCockpit/materialMaster/materialSingle");
    };
    const handleOpenDialog = () => {
      setOpenDialog(true);
    };
  
    const handleCloseDialog = () => {
      setOpenDialog(false);
    };
  
    const attachmentColumns = [
      {
        field: "id",
        headerName: "Document ID",
        flex: 1,
        hide: true,
      },
      {
        field: "docType",
        headerName: "Type",
        flex: 1,
      },
      {
        field: "docName",
        headerName: "Document Name",
        flex: 1,
      },
      {
        field: "uploadedOn",
        headerName: "Uploaded On",
        flex: 1,
        align: "center",
        headerAlign: "center",
      },
      {
        field: "uploadedBy",
        headerName: "Uploaded By",
        sortable: false,
        flex: 1,
      },
      {
        field: "action",
        headerName: "Action",
        sortable: false,
        filterable: false,
        align: "center",
        headerAlign: "center",
        flex: 1,
        renderCell: (cellValues) => {
          return (
            <>
              <MatView index={cellValues.row.id} name={cellValues.row.docName} />
              <MatDownload
                index={cellValues.row.id}
                name={cellValues.row.docName}
              />
            </>
          );
        },
      },
    ];
    const getAttachments = () => {
      let requestId = task?.subject
        ? task?.subject
        : massBKRowData?.requestId;
      let hSuccess = (data) => {
        var attachmentRows = [];
        data.documentDetailDtoList.forEach((doc) => {
          var tempRow = {
            id: doc.documentId,
            docType: doc.fileType,
            docName: doc.fileName,
            uploadedOn: moment(doc.docCreationDate).format(appSettings.date),
            uploadedBy: doc.createdBy,
          };
          if (true) attachmentRows.push(tempRow);
        });
        setAttachments(attachmentRows);
      };
      // invoiceHeaderData?.extInvNum &&
      doAjax(
        `/${destination_DocumentManagement}/documentManagement/getDocByRequestId/${requestId}`,
        "get",
        hSuccess
      );
    };
  
    const getComments = () => {
      let requestId = task?.
      subject
        ? task?.subject
        : massBKRowData?.requestId;
      let hSuccess = (data) => {
        console.log("commentsdata", data);
  
        var commentRows = [];
        data.body.forEach((cmt) => {
          var tempRow = {
            id: cmt.requestId,
            comment: cmt.comment,
            user: cmt.createdByUser,
            createdAt: cmt.updatedAt,
          };
          commentRows.push(tempRow);
        });
        setComments(commentRows);
        console.log("commentrows", commentRows);
      };
  
      let hError = (error) => {
        console.log(error);
      };
      // invoiceHeaderData?.extInvNum &&
      doAjax(
        `/${destination_BankKey}/activitylog/fetchTaskDetailsForRequestId?requestId=${requestId}`,
        "get",
        hSuccess,
        hError
      );
    };
  
    useEffect(() => {
      getAttachments();
      getComments();
      setBkNumber(idGenerator("BK"));
    }, []);
    const getValueForFieldName = (data, fieldName) => {
      const field = data?.find((field) => field?.fieldName === fieldName);
      return field ? field.value : "";
    };
    const getValueForFieldNameasTrueBlank = (data, fieldName) => {
      //console.log("getvalueforfieldname", data, fieldName);
      const field = data?.find((field) => field?.fieldName === fieldName);
      console.log(field.value,"field.value")
      return field.value === true ? "X" : "";
    };
  
    const getMassBankKeyTable = () => {
      //console.log("sdfkhdgkf");
      console.log(task?.processDesc,massBKRowData?.requestType ,"sdfkhdgkf")
      setIsLoading(true);
      let payload = {};
      if (task?.processDesc === "Mass Change") {
        payload = {
          massCreationId: "",
          massChangeId: task?.subject
            ? task?.subject?.slice(3)
            : massBKRowData?.requestId.slice(3),
          screenName: "Change",
        };
      } else if (task?.processDesc === "Mass Create") {
        payload = {
          massCreationId: task?.subject
            ? task?.subject?.slice(3)
            : massBKRowData?.requestId.slice(3),
          massChangeId: "",
          screenName: "Create",
        };
      } else if (massBKRowData?.requestType === "Mass Create") {
        payload = {
          massCreationId: massBKRowData?.requestId?.slice(3),
          massChangeId: "",
          screenName: "Create",
        };
      } else if (massBKRowData?.requestType === "Mass Change") {
        payload = {
          massCreationId: "",
          massChangeId: massBKRowData?.requestId?.slice(3),
          screenName: "Change",
        };
      }
      const hSuccess = (data) => {
        setIsLoading(false);
        // setFactorsArray(categoryKeys);
        if (data.body) {
          dispatch(setMultipleBankKeyData(data?.body));
        }
      };
  
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        `/${destination_BankKey}/data/displayMassBankKey`,
        "post",
        hSuccess,
        hError,
        payload
      );
      console.log(payload,"payload for display bank key");
    };
    
  
    // Loader and lookup for independent apis start
    const [apiCount, setApiCount] = useState(0);
    const fetchDynamicApiData = (keyName, endPoint) => {
      const hSuccess = (data) => {
        dispatch(setDropDown({ keyName: keyName, data: data.body }));
        // setIsLoading(false);
        setApiCount((prev) => prev + 1);
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        `/${destination_BankKey}/data/${endPoint}`,
        "get",
        hSuccess,
        hError
      );
    };
    const getAllLookups = () => {
      console.log("Calleddddd");
      lookup?.bankKey?.map((item) => {
        fetchDynamicApiData(item?.keyName, item?.endPoint);
      });
    };
    const loaderCount = () => {
      console.log("apiCount", apiCount);
      if (apiCount == lookup?.bankKey?.length) {
        setIsLoading(false);
      } else {
        setIsLoading(true);
      }
    };
    useEffect(() => {
      loaderCount();
    }, [apiCount]);
  
    // Loader and lookup for independent apis end
  
    useEffect(() => {
      if (multipleBankKeyData.length === 0) {
        getMassBankKeyTable();
      } else {
        return;
      }
    }, []);
  
    useEffect(() => {
      getAllLookups();
      dispatch(clearBankKey());
    }, []);
  
    const handleSelectionModelChange = (selectedIds) => {
      // console.log('selected', selectedIds)
      if (selectedIds.length > 0) {
        setTestRun(true);
        console.log("selectedIds1", selectedIds);
      } else {
        // console.log("select", selectedIds);
        setTestRun(false);
      }
      console.log("selectedIds", selectedIds);
      setSelectedRows(selectedIds);
      // setTestRun(true);
    };
    const initialRows = multipleBankKeyData?.map((bKey, index) => {
        const headerData = bKey;
        const basicData = bKey?.viewData?.["Bank Details"] || {};
        console.log(basicData,headerData,"basicData")
        return {
          id: index,
          bankKey: headerData?.BankKey,
          bankCountry: headerData?.BankCtry,
          bankName:
            basicData["Address"].find((field) => field?.fieldName === "Bank Name")
              ?.value || "",
          Region:
            basicData["Address"].find(
              (field) => field?.fieldName === "Region"
            )?.value || "",
          City:
            basicData["Address"].find(
              (field) => field?.fieldName === "City"
            )?.value || "",
          bankNumber:
            basicData["Control Data"].find(
              (field) => field?.fieldName === "Bank Number"
            )?.value || "",
          bankBranch:
            basicData["Address"].find(
              (field) => field?.fieldName === "Bank Branch"
            )?.value || "",
          swiftBic:
            basicData["Control Data"].find(
              (field) => field?.fieldName === "SWIFT/BIC"
            )?.value || "",
        };
      });
  
      console.log(initialRows,"initialRows====")
  
    const columns = [
      {
        field: "bankKey",
        headerName: "Bank key",
        editable: false,
        flex: 1,
        renderCell: (params) => {
          const isDirectMatch = bkValidationErrors.find(element=> element.bankKey===params.value);
          console.log(isDirectMatch, "isDirectMatch")
          console.log(params, "params")
    
          if (isDirectMatch && isDirectMatch.code === 400) {
            return (
              <Typography sx={{ fontSize: "12px", color: "red" }}>
                {params.value}
              </Typography>
            );
          } else {
            return (
              <Typography sx={{ fontSize: "12px" }}>
                {params.value}
              </Typography>
            );
          }
        },
      },
        {
          field: "bankName",
          headerName: "Bank Name",
          editable: false,
          flex: 1,
        },
        {
          field: "bankCountry",
          headerName: "Bank Country",
          editable: false,
          flex: 1,
        },
        {
          field: "Region",
          headerName: "Region",
          editable: false,
          flex: 1,
        },
        {
          field: "City",
          headerName: "City",
          editable: false,
          flex: 1,
        },
    
        {
          field: "bankNumber",
          headerName: "Bank Number",
          editable: false,
          flex: 1,
          
        },
        {
          field: "bankBranch",
          headerName: "Bank Branch",
          editable: false,
          flex: 1,
        },
      {
          field: "swiftBic",
          headerName: "Swift/Bic",
          editable: false,
          flex: 1,
        },
      ];
      console.log(task?.createdOn,"created_on_task",task)
      var payloadmapping = multipleBankKeyData.map((x) => {
        console.log("samsung", x);
        return {
            AddressDto: {
              AddressID: x?.AddressId ?? '',
              Title: x?.Title ? x?.Title : "",
              Name: getValueForFieldName(
                x?.viewData["Address Details"]?.["Name"],
                "Name"
              ),
              Name2: getValueForFieldName(
                x?.viewData["Address Details"]?.["Name"],
                "Name 1"
              ),
              Name3: getValueForFieldName(
                x?.viewData["Address Details"]?.["Name"],
                "Name 2"
              ),
              Name4: getValueForFieldName(
                x?.viewData["Address Details"]?.["Name"],
                "Name 3"
              ),
              Sort1: getValueForFieldName(
                x?.viewData["Address Details"]?.["Search Terms"],
                "Search Term 1"
              ),
              Sort2: getValueForFieldName(
                x?.viewData["Address Details"]?.["Search Terms"],
                "Search Term 2"
              ),
              BuildLong: getValueForFieldName(
                x?.viewData["Address Details"]?.["Street Address"],
                "Building Code"
              ),
              RoomNo: getValueForFieldName(
                x?.viewData["Address Details"]?.["Street Address"],
                "Room Number"
              ),
              Floor: getValueForFieldName(
                x?.viewData["Address Details"]?.["Street Address"],
                "Floor"
              ),
              
              COName: getValueForFieldName(
                x?.viewData["Address Details"]?.["Street Address"],
                "c/o"
              ),
              StrSuppl1: getValueForFieldName(
                x?.viewData["Address Details"]?.["Street Address"],
                "Street 1"
              ),
              StrSuppl2: getValueForFieldName(
                x?.viewData["Address Details"]?.["Street Address"],
                "Street 2"
              ),
              Street: getValueForFieldName(
                x?.viewData["Address Details"]?.["Street Address"],
                "Street 3"
              ),
              HouseNo: getValueForFieldName(
                x?.viewData["Address Details"]?.["Street Address"],
                "House Number"
              ),
              HouseNo2:getValueForFieldName(
                x?.viewData["Address Details"]?.["Street Address"],
                "Supplement"
              ),
              StrSuppl3: getValueForFieldName(
                x?.viewData["Address Details"]?.["Street Address"],
                "Street 4"
              ),
              Location: getValueForFieldName(
                x?.viewData["Address Details"]?.["Street Address"],
                "Street 5"
              ),
              District: getValueForFieldName(
                x?.viewData["Address Details"]?.["Street Address"],
                "District"
              ),
              HomeCity: getValueForFieldName(
                x?.viewData["Address Details"]?.["Street Address"],
                "Other City"
              ),
              PostlCod1: getValueForFieldName(
                x?.viewData["Address Details"]?.["Street Address"],
                "Postal Code"
              ),
              PostlCod2: x?.PostalCode1 ? x?.PostalCode1 : "",
              PostlCod3: x?.CompanyPostCd
                ? x?.CompanyPostCd
                : "",
              PoBox: x?.POBox ? x?.POBox : "",
              PoBoxCit:getValueForFieldName(
                x?.viewData["Address Details"]?.["PO Box Address"],
                "PO Box City"
              ),
              PoBoxReg: getValueForFieldName(
                x?.viewData["Address Details"]?.["PO Box Address"],
                "Region 2"
              ),
              PoboxCtry: getValueForFieldName(
                x?.viewData["Address Details"]?.["PO Box Address"],
                "Country 2"
              ),
              Country: getValueForFieldName(
                x?.viewData["Address Details"]?.["Street Address"],
                "Country 1"
              ),
              TimeZone: getValueForFieldName(
                x?.viewData["Address Details"]?.["Street Address"],
                "Time Zone"
              ),
              Taxjurcode: getValueForFieldName(
                x?.viewData["Address Details"]?.["Street Address"],
                "Tax Jurisdiction"
              ),
              Transpzone: getValueForFieldName(
                x?.viewData["Address Details"]?.["Street Address"],
                "Transport Zone"
              ),
              Regiogroup: getValueForFieldName(
                x?.viewData["Address Details"]?.["Street Address"],
                "Structure Group"
              ),
              DontUseS: getValueForFieldName(
                x?.viewData["Address Details"]?.["Street Address"],
                "Undeliverable"
              ),
              DontUseP: getValueForFieldName(
                x?.viewData["Address Details"]?.["PO Box Address"],
                "Undeliverable 1"
              ),
              PoWONo:getValueForFieldNameasTrueBlank(
                x?.viewData["Address Details"]?.["PO Box Address"],
                "PO Box w/o No."
              ),
              DeliServType: getValueForFieldName(
                x?.viewData["Address Details"]?.["PO Box Address"],
                "Delivery Service Type"
              ),
              DeliServNumber: getValueForFieldName(
                x?.viewData["Address Details"]?.["PO Box Address"],
                "Delivery Service No."
              ),
              Township: getValueForFieldName(
                x?.viewData["Address Details"]?.["PO Box Address"],
                "Township"
              ),
              Langu: getValueForFieldName(
                x?.viewData["Address Details"]?.["Communication"],
                "Language"
              ),
              Tel1Numbr: getValueForFieldName(
                x?.viewData["Address Details"]?.["Communication"],
                "Telephone"
              ),
              Tel1Ext: getValueForFieldName(
                x?.viewData["Address Details"]?.["Communication"],
                "Extension"
              ),
              FaxNumber: getValueForFieldName(
                x?.viewData["Address Details"]?.["Communication"],
                "Fax"
              ),
              FaxExtens: getValueForFieldName(
                x?.viewData["Address Details"]?.["Communication"],
                "Extension 1"
              ),
              MobilePhone: getValueForFieldName(
                x?.viewData["Address Details"]?.["Communication"],
                "Mobile Phone"
              ),
              EMail: getValueForFieldName(
                x?.viewData["Address Details"]?.["Communication"],
                "E-Mail Address"
              ),
              AdrNotes: getValueForFieldName(
                x?.viewData["Address Details"]?.["Communication"],
                "Notes"
              ),
              Region: getValueForFieldName(
                x?.viewData["Address Details"]?.["Street Address"],
                "Region 1"
              ),
              
              PoBoxLobby: x?.PoBoxLobby
                ? x?.PoBoxLobby
                : "",
              
            },
            BankKeyID:x?.BankKeyId ?? '',
            ReqCreatedBy: userData?.user_id,
            ReqCreatedOn: task?.createdOn ? "/Date(" + task?.createdOn + ")/" : "/Date(" + Date.parse(massBKRowData?.createdOn) + ")/",
            RequestStatus: "",
            CreationId: "",
            EditId: "",
            DeleteId: "",
            MassCreationId:
            task?.processDesc === "Mass Create"
              ? task?.subject?.slice(3)
              : massBKRowData?.requestType === "Mass Create"
              ? massBKRowData?.requestId.slice(3)
              : "",
            MassEditId:
            task?.processDesc === "Mass Change"
              ? task?.subject?.slice(3)
              : massBKRowData?.requestType === "Mass Change"
              ? massBKRowData?.requestId.slice(3)
              : "",
            MassDeleteId: "",
            RequestType:
            task?.processDesc === "Mass Create"
              ? "Mass Create"
              : task?.processDesc === "Mass Change"
              ? "Mass Change"
              : massBKRowData?.requestType === "Mass Change"
              ? "Mass Change"
              : massBKRowData?.requestType === "Mass Create"
              ? "Mass Create"
              : "",
            TaskId: task?.taskId ? task?.taskId : "",
            Remarks: remarks ? remarks : "",
            Action:
            task?.processDesc === "Mass Create"
              ? "I"
              : task?.processDesc === "Mass Change"
              ? "U"
              : massBKRowData?.requestType === "Mass Change"
              ? "U"
              : massBKRowData?.requestType === "Mass Create"
              ? "I"
              : "",
            Validation: testrunStatus === true ? "X" : "",
            BankCtry: x?.BankCtry,
            BankKey: x?.BankKey,
            BankName: getValueForFieldName(
              x?.viewData["Bank Details"]?.["Address"],
              "Bank Name"
            ),
            BankRegion:getValueForFieldName(
              x?.viewData["Bank Details"]?.["Address"],
              "Region"
            ),
            BankStreet: getValueForFieldName(
              x?.viewData["Bank Details"]?.["Address"],
              "Street"
            ),
            City: getValueForFieldName(
              x?.viewData["Bank Details"]?.["Address"],
              "City"
            ),
            BankBranch: getValueForFieldName(
              x?.viewData["Bank Details"]?.["Address"],
              "Bank Branch"
            ),
            SwiftCode: getValueForFieldName(
              x?.viewData["Bank Details"]?.["Control Data"],
              "SWIFT/BIC"
            ),
            BankGroup: getValueForFieldName(
              x?.viewData["Bank Details"]?.["Control Data"],
              "Bank Group"
            ),
            PobkCurac:getValueForFieldNameasTrueBlank(
              x?.viewData["Bank Details"]?.["Control Data"],
              "Postbank Acct"
            ),
            BankNo: getValueForFieldName(
              x?.viewData["Bank Details"]?.["Control Data"],
              "Bank Number"
            ),
          }
      });
      console.log("massBKRowData", massBKRowData);//
  
    //mdm submits for approval in create
    const handleSubmitForApproval = () => {
      const payload = payloadmapping;
      console.log("isLoading1", isLoading);
      console.log("paylaod", payload);
      // Assuming payloadmapping is an array with a single element
      const hSuccess = (data) => {
        setBlurLoading(false);
        if (data.statusCode === 200) {
          console.log("success");
          setMessageDialogTitle("Create");
          setMessageDialogMessage(
            `Mass Bank Key Submitted for Approval with ID NBM${data.body}`
          );
          setMessageDialogSeverity("success");
          setMessageDialogOK(false);
          setsuccessMsg(true);
          handleSnackBarOpen();
          setMessageDialogExtra(true);
          setTestRun(true);
          // setIsLoading(false);
        } else {
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          setMessageDialogMessage(
            "Failed Submitting the Mass Bank Key for Approval  "
          );
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          setTestRun(true);
          // setIsLoading(false);
        }
        handleClose();
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        `/${destination_BankKey}/massAction/bankKeysApprovalSubmit`,
        "post",
        hSuccess,
        hError,
        payload
      );
    };
  
    //mdm submits for approval in change
    const handleSubmitForApprovalChange = () => {
      const selectedData = initialRows.filter((_, index) =>
        selectedRows.includes(index)
      );
      const payload = payloadmapping;
      console.log("paylaod", payload);
      console.log("isLoading2", isLoading);
      // Assuming payloadmapping is an array with a single element
      const hSuccess = (data) => {
        setBlurLoading(false);
        setTestRun(true);
        if (data.statusCode === 200) {
          console.log("success");
          setMessageDialogTitle("Create");
          setMessageDialogMessage(
            `Mass Bank Keys Change Submitted for Approval with ID CBM${data.body}`
          );
          setMessageDialogSeverity("success");
          setMessageDialogOK(false);
          setsuccessMsg(true);
          handleSnackBarOpen();
          setMessageDialogExtra(true);
          // setIsLoading(false);
        } else {
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          setMessageDialogMessage(
            "Failed Submitting Mass Bank Key Data for Approval"
          );
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          // setIsLoading(false);
        }
      };
      const hError = (error) => {
        console.log("error");
      };
  
      doAjax(
        `/${destination_BankKey}/massAction/changeBankKeysApprovalSubmit`,
        "post",
        hSuccess,
        hError,
        payload
      );
    };
  
    //approver approves in create
    const handleApproveMassBankKey = () => {
      const selectedData = initialRows.filter((_, index) =>
        selectedRows.includes(index)
      );
      const payload = payloadmapping;
      console.log("paylaod", payload);
      console.log("isLoading3", isLoading);
      // Assuming payloadmapping is an array with a single element
      const hSuccess = (data) => {
        setBlurLoading(false);
        if (data.statusCode === 201) {
          console.log("success");
          setMessageDialogTitle("Create");
          setMessageDialogMessage(
            "Mass Bank Key Approved & SAP Syndication Completed"
          );
          setMessageDialogSeverity("success");
          setMessageDialogOK(false);
          setsuccessMsg(true);
          handleSnackBarOpen();
          setMessageDialogExtra(true);
          setTestRun(true);
          // setIsLoading(false);
        } else {
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          setMessageDialogMessage(
            "Failed Submitting the Bank key for Approval "
          );
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          setTestRun(true);
          // setIsLoading(false);
        }
        handleClose();
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        `/${destination_BankKey}/massAction/createBankKeysApproved`,
        "post",
        hSuccess,
        hError,
        payload
      );
    };
  
    //approver approves in change
    const handleApproveMassBankKeyChange = () => {
      const payload = payloadmapping;
      console.log("paylaod", payload);
      console.log("isLoading4", isLoading);
      // Assuming payloadmapping is an array with a single element
      const hSuccess = (data) => {
        setBlurLoading(false);
        setTestRun(true);
        if (data.statusCode === 201) {
          console.log("success");
          setMessageDialogTitle("Create");
          setMessageDialogMessage(
            "Mass Bank Key Change Approved & SAP Syndication Completed"
          );
          setMessageDialogSeverity("success");
          setMessageDialogOK(false);
          setsuccessMsg(true);
          handleSnackBarOpen();
          setMessageDialogExtra(true);
          // setIsLoading(false);
        } else {
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          setMessageDialogMessage("Failed Approving Mass Bank Key Change");
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          // setIsLoading(false);
        }
      };
      const hError = (error) => {
        console.log("error");
      };
      doAjax(
        `/${destination_BankKey}/massAction/changeBankKeysApproved`,
        "post",
        hSuccess,
        hError,
        payload
      );
    };
  
    const handleSubmitForReviewMassBankKey = () => {
      const payload = payloadmapping;
      console.log("paylaod", payload);
      console.log("isLoading5", isLoading);
      // Assuming payloadmapping is an array with a single element
      const hSuccess = (data) => {
        setBlurLoading(false);
        setTestRun(true);
        if (data.statusCode === 200) {
          console.log("success");
          setMessageDialogTitle("Create");
          setMessageDialogMessage(
            `Mass Bank Key Submitted for Review with ID NBM${data.body}`
          );
          setMessageDialogSeverity("success");
          setMessageDialogOK(false);
          setsuccessMsg(true);
          handleSnackBarOpen();
          setMessageDialogExtra(true);
          const secondApiPayload = {
            artifactId: bkNumber,
            createdBy: userData?.emailId,
            artifactType: "BankKey",
            requestId: `NBM${data?.body}`,
          };
          const secondApiSuccess = (secondApiData) => {
            console.log("Second API success", secondApiData);
            // Handle success for the second API if needed
          };
  
          const secondApiError = (secondApiError) => {
            console.error("Second API error", secondApiError);
            // Handle error for the second API if needed
          };
          // {requestId&&
          doAjax(
            `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
            "post",
            secondApiSuccess,
            secondApiError,
            secondApiPayload
          );
          // setIsLoading(false);
        } else {
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          setMessageDialogMessage(
            "Failed Submitting the Mass Bank Key for Review  "
          );
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          // setIsLoading(false);
        }
        handleClose();
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        `/${destination_BankKey}/massAction/bankKeysSubmitForReview`,
        "post",
        hSuccess,
        hError,
        payload
      );
    };
  
    const handleSubmitForReviewChange = () => {
      const payload = payloadmapping;
      console.log("paylaod", payload);
      console.log("isLoading5", isLoading);
      // Assuming payloadmapping is an array with a single element
      const hSuccess = (data) => {
        setBlurLoading(false);
        setTestRun(true);
        if (data.statusCode === 200) {
          console.log("success");
          setMessageDialogTitle("Create");
          setMessageDialogMessage(
            `Mass Bank Key Submitted for Review with ID CBM${data.body}`
          );
          setMessageDialogSeverity("success");
          setMessageDialogOK(false);
          setsuccessMsg(true);
          handleSnackBarOpen();
          setMessageDialogExtra(true);
          const secondApiPayload = {
            artifactId: bkNumber,
            createdBy: userData?.emailId,
            artifactType: "BankKey",
            requestId: `CBM${data?.body}`,
          };
          const secondApiSuccess = (secondApiData) => {
            console.log("Second API success", secondApiData);
            // Handle success for the second API if needed
          };
  
          const secondApiError = (secondApiError) => {
            console.error("Second API error", secondApiError);
            // Handle error for the second API if needed
          };
          // {requestId&&
          doAjax(
            `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
            "post",
            secondApiSuccess,
            secondApiError,
            secondApiPayload
          );
          // setIsLoading(false);
        } else {
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          setMessageDialogMessage(
            "Failed Submitting the Mass Bank Key for Review  "
          );
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          // setIsLoading(false);
        }
        handleClose();
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        `/${destination_BankKey}/massAction/changeBankKeysSubmitForReview`,
        "post",
        hSuccess,
        hError,
        payload
      );
    };
  
    const handleRemarks = (e, value) => {
      //setRemarks(e.target.value);
      const newValue = e.target.value;
      if (newValue.length > 0 && newValue[0] === " ") {
        setRemarks(newValue.trimStart());
      } else {
        //let costCenterValue = e.target.value;
        let remarksUpperCase = newValue.toUpperCase();
        setRemarks(remarksUpperCase);
      }
    };
    const handleRemarksDialogClose = () => {
      setRemarksValidationError(false);
      setTestRun(true);
      setOpenRemarkDialog(false);
    };
  
    const onCostCenterSubmitRemarks = () => {
      if (
        (userData?.role === "MDM Steward" &&
          task?.processDesc === "Mass Create") ||
        (userData?.role === "MDM Steward" &&
        massBKRowData?.requestType === "Mass Create")
      ) {
        setBlurLoading(true);
        handleRemarksDialogClose();
        handleSubmitForApproval();
      } else if (
        (userData?.role === "Approver" && task.processDesc === "Mass Create") ||
        (userData?.role === "Approver" &&
        massBKRowData?.requestType === "Mass Create")
      ) {
        setBlurLoading(true);
        handleRemarksDialogClose();
        handleApproveMassBankKey();
      } else if (
        (userData?.role === "Finance" && task.processDesc === "Mass Create") ||
        (userData?.role === "Finance" &&
        massBKRowData?.requestType === "Mass Create")
      ) {
        if (remarks.length <= 0){
          setRemarksValidationError(true)
        }else{
          setRemarksValidationError(false)
          setBlurLoading(true);
          handleRemarksDialogClose();
          handleSubmitForReviewMassBankKey();
        }
      } else if (
        (userData?.role === "MDM Steward" &&
          task?.processDesc === "Mass Change") ||
        (userData?.role === "MDM Steward" &&
        massBKRowData?.requestType === "Mass Change")
      ) {
        setBlurLoading(true);
        handleRemarksDialogClose();
        handleSubmitForApprovalChange();
      } else if (
        (userData?.role === "Approver" && task.processDesc === "Mass Change") ||
        (userData?.role === "Approver" &&
        massBKRowData?.requestType === "Mass Change")
      ) {
        setBlurLoading(true);
        handleRemarksDialogClose();
        handleApproveMassBankKeyChange();
      } else if (
        (userData?.role === "Finance" && task?.processDesc === "Mass Change") ||
        (userData?.role === "Finance" &&
        massBKRowData?.requestType === "Mass Change")
      ) {
        if (remarks.length <= 0){
          setRemarksValidationError(true)
        }else{
          setRemarksValidationError(false)
          setBlurLoading(true);
          handleRemarksDialogClose();
          handleSubmitForReviewChange();
        }
      }
    };
  
    const handleOpenRemarkDialog = () => {
      setTestRun(false);
      setOpenRemarkDialog(true);
    };
  
    const handleCorrectionDialogClose = () => {
      setOpenCorrectionDialog(false);
    };
  
    const handleOpenCorrectionDialog = () => {
      setTestRun(false);
      setOpenCorrectionDialog(true);
    };
  
    const handleCorrectionMDMCreate = () => {
      console.log("isLoading6", isLoading);
      const selectedData = initialRows.filter((_, index) =>
        selectedRows.includes(index)
      );
      const payload = payloadmapping;
      const hSuccess = (data) => {
        setIsLoading(false);
        setTestRun(true);
        if (data.statusCode === 200) {
          console.log("success");
          setMessageDialogTitle("Create");
          setMessageDialogMessage(
            `Bank Key Submitted for Correction with ID NBS${data.body}`
          );
          setMessageDialogSeverity("success");
          setMessageDialogOK(false);
          setsuccessMsg(true);
          handleSnackBarOpen();
          setMessageDialogExtra(true);
          // setIsLoading(false);
        } else {
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          setMessageDialogMessage(
            "Failed Submitting Bank Key for Correction"
          );
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          // setIsLoading(false);
        }
        handleCorrectionDialogClose();
      };
      const hError = (error) => {
        console.log(error);
      };
      console.log("remarkssssssssss", remarks);
      doAjax(
        // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
        `/${destination_BankKey}/massAction/bankKeysSendForCorrection`,
        "post",
        hSuccess,
        hError,
        payload
      );
    };
  
    const handleCorrectionMDMChange = () => {
      const selectedData = initialRows.filter((_, index) =>
        selectedRows.includes(index)
      );
      const payload = payloadmapping;
      const hSuccess = (data) => {
        setIsLoading(false);
        setTestRun(true);
        if (data.statusCode === 200) {
          console.log("success");
          setMessageDialogTitle("Create");
          setMessageDialogMessage(
            `Bank key Submitted for Correction with ID CBM${data.body}`
          );
          setMessageDialogSeverity("success");
          setMessageDialogOK(false);
          setsuccessMsg(true);
          handleSnackBarOpen();
          setMessageDialogExtra(true);
          // setIsLoading(false);
        } else {
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          setMessageDialogMessage(
            "Failed Submitting Bank Key for Correction"
          );
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          // setIsLoading(false);
        }
        handleCorrectionDialogClose();
      };
      const hError = (error) => {
        console.log(error);
      };
      console.log("remarkssssssssss", remarks);
      doAjax(
        // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
        `/${destination_BankKey}/massAction/changeBankKeysSendForCorrection`,
        "post",
        hSuccess,
        hError,
        payload
      );
    };
  
    const handleCorrectionApproverCreate = () => {
      console.log("isLoading7", isLoading);
      const selectedData = initialRows.filter((_, index) =>
        selectedRows.includes(index)
      );
      const payload = payloadmapping;
      const hSuccess = (data) => {
        setIsLoading(false);
        setTestRun(true);
        if (data.statusCode === 200) {
          console.log("success");
          setMessageDialogTitle("Create");
          setMessageDialogMessage(
            `Bank key Submitted for Correction with ID NBM${data.body}`
          );
          setMessageDialogSeverity("success");
          setMessageDialogOK(false);
          setsuccessMsg(true);
          handleSnackBarOpen();
          setMessageDialogExtra(true);
          // setIsLoading(false);
        } else {
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          setMessageDialogMessage(
            "Failed Submitting Bank Key for Correction"
          );
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          // setIsLoading(false);
        }
        handleCorrectionDialogClose();
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
        `/${destination_BankKey}/massAction/bankKeysSendForReview`,
        "post",
        hSuccess,
        hError,
        payload
      );
    };
    const handleCorrectionApproverChange = () => {
      const selectedData = initialRows.filter((_, index) =>
        selectedRows.includes(index)
      );
      const payload = payloadmapping;
      const hSuccess = (data) => {
        setIsLoading(false);
        setTestRun(true);
        if (data.statusCode === 200) {
          console.log("success");
          setMessageDialogTitle("Create");
          setMessageDialogMessage(
            `Bank key Submitted for Correction with ID CBM${data.body}`
          );
          setMessageDialogSeverity("success");
          setMessageDialogOK(false);
          setsuccessMsg(true);
          handleSnackBarOpen();
          setMessageDialogExtra(true);
          // setIsLoading(false);
        } else {
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          setMessageDialogMessage(
            "Failed Submitting Bank key for Correction"
          );
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          // setIsLoading(false);
        }
        handleCorrectionDialogClose();
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
        `/${destination_BankKey}/massAction/changeBankKeysSendForCorrection`,
        "post",
        hSuccess,
        hError,
        payload
      );
    };
    const onBankKeyCorrection = () => {
      if (
        (userData?.role === "MDM Steward" &&
          task.processDesc === "Mass Create") ||
        (userData?.role === "MDM Steward" &&
        massBKRowData?.requestType === "Mass Create")
      ) {
        setIsLoading(true);
        handleCorrectionDialogClose();
        handleCorrectionMDMCreate();
      } else if (
        (userData?.role === "Approver" && task?.processDesc === "Mass Create") ||
        (userData?.role === "Approver" &&
        massBKRowData?.requestType === "Mass Create")
      ) {
        setIsLoading(true);
        handleCorrectionDialogClose();
        handleCorrectionApproverCreate();
      } else if (
        (userData?.role === "MDM Steward" &&
          task.processDesc === "Mass Change") ||
        (userData?.role === "MDM Steward" &&
        massBKRowData?.requestType === "Mass Change")
      ) {
        handleCorrectionDialogClose();
        handleCorrectionMDMChange();
      } else if (
        (userData?.role === "Approver" && task?.processDesc === "Mass Change") ||
        (userData?.role === "Approver" &&
        massBKRowData?.requestType === "Mass Change")
      ) {
        handleCorrectionDialogClose();
        handleCorrectionApproverChange();
      }
    };
  
    const onValidateBankKeyApprove = () => {
      // setIsLoading(true);
      setBlurLoading(true);
     
      let payload = payloadmapping;
      // payload.testRun = true
      const hSuccess = (data) => {
        if (data.statusCode === 400) {
          setBKValidationErrors(data.body);
          setApproveDisabled(true);
          setDialogOpen(true);
          setBlurLoading(false);
        } else {
          setApproveDisabled(false);
          setValidationStatus(false);
          setMessageDialogTitle("Create");
          console.log("success");
          setMessageDialogTitle("Create");
          setMessageDialogMessage(
            `All Data has been Validated. Bank Key can be Sent for Review`
          );
          setMessageDialogSeverity("success");
          setMessageDialogOK(false);
          setsuccessMsg(true);
          handleSnackBarOpen();
          setMessageDialogExtra(true);
          setIsLoading(false);
          setValidateFlag(true);
          setBlurLoading(false);
          setTestrunStatus(false);
        }
      };
  
      const hError = (error) => {
        console.log(error);
      };
  
      // Call the main API for validation
      doAjax(
        `/${destination_BankKey}/massAction/validateMassBankKey`,
        "post",
        hSuccess,
        hError,
        payload
      );
    };
    const onValidateBankKey = () => {
      // setIsLoading(true);
      setBlurLoading(true);
      const selectedData = initialRows.filter((_, index) =>
        selectedRows.includes(index)
      );
      console.log("selectedData", selectedData);
      const selectedBankKeyRows= selectedData.map((x) => ({
        ...payloadmapping[x?.id],
      }));
      //console.log("selectedProfitCenterRows", selectedProfitCenterRows);
      /*const duplicateCheckPayload = [];
      selectedProfitCenterRows.map((x) => {
        var idk = {
          coArea: x?.ControllingArea,
          name: x?.PrctrName.toUpperCase(),
        };
        duplicateCheckPayload.push(idk);
      });*/
      //console.log("duplicateCheckPayload", duplicateCheckPayload);
      // payloadmapping.Testrun = true
      let payload = payloadmapping;
      payload = selectedBankKeyRows;
      // payload.testRun = true
      const hSuccess = (data) => {
        if (data.statusCode === 400) {
          setBKValidationErrors(data.body);
          setDialogOpen(true);
          setBlurLoading(false);
        } else {
          setValidationStatus(false);
          setMessageDialogTitle("Create");
          console.log("success");
          setMessageDialogTitle("Create");
          setMessageDialogMessage(
            `All Data has been Validated. Bank Key can be Sent for Review`
          );
          setMessageDialogSeverity("success");
          setMessageDialogOK(false);
          setsuccessMsg(true);
          handleSnackBarOpen();
          setMessageDialogExtra(true);
          setIsLoading(false);
          setValidateFlag(true);
          setBlurLoading(false);
          setSubmitForReviewDisabled(false); // for now chiranjit
          setTestrunStatus(false);
  
          // Now, make the duplicate check API call
          // Ensure that the conditions for making the duplicate check API call are met
          /*if (
            duplicateCheckPayload.coArea !== "" ||
            duplicateCheckPayload.name !== ""
          ) {
            // payloadmapping.Toitem = duplicateCheckPayload.name;
            doAjax(
              `/${destination_ProfitCenter}/alter/fetchPCDescriptionsDupliChk`,
              "post",
              hDuplicateCheckSuccess,
              hDuplicateCheckError,
              duplicateCheckPayload
            );
          }*/
        }
      };
  
      const hDuplicateCheckSuccess = (data) => {
        console.log("dataaaa", data);
        // Handle success of duplicate check
        if (
          data.body.length === 0 ||
          !data.body.some((item) =>
            duplicateCheckPayload.some(
              (payloadItem) => payloadItem.name.toUpperCase() === item.matches[0]
            )
          )
        ) {
          // No direct match, enable the "Submit for Review" button
          setBlurLoading(false);
          setSubmitForReviewDisabled(false);
        } else {
          // Handle direct match
          const directMatches = data.body.map(item => item.matches[0]);
          setBlurLoading(false);
          setMessageDialogTitle("Duplicate Check");
          setsuccessMsg(false);
          setMessageDialogMessage(
            `There is a direct match for the Bank Key name.`
          );
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          setSubmitForReviewDisabled(true);
          setDirectMatchedProfitCenters(directMatches);
        }
      };
  
      const hDuplicateCheckError = (error) => {
        // Handle error of duplicate check
        console.log(error);
      };
  
      const hError = (error) => {
        console.log(error);
      };
  
      // Call the main API for validation
      doAjax(
        `/${destination_BankKey}/massAction/validateMassBankKey`,
        "post",
        hSuccess,
        hError,
        payload
      );
    };
    const handleDialogClose = () => {
      setDialogOpen(false);
    };
    const validationColumns = [
      {
        field: "id",
        headerName: "ID",
        hide: true,
        editable: false,
        flex: 1,
        // width: 100,
      },
      {
        field: "bankKey",
        headerName: "Bank Key",
        editable: false,
        flex: 1,
        // width: 100,
      },
      {
        field: "error",
        headerName: "Error",
        editable: false,
        flex: 1,
        // width: 400,
      },
    ];
    const validationRows = bkValidationErrors?.map((item, index) => {
      console.log("erroritem", item);
      if (item.code === 400) {
        return {
          id: index,
          bankKey: item?.bankKey,
          error: item?.status?.message,
        };
      }
    });
  
    return (
      <>
        {isLoading === true ? (
          <LoadingComponent />
        ) : (
          <div style={{ backgroundColor: "#FAFCFF" }}>
            <Dialog
              hideBackdrop={false}
              elevation={2}
              PaperProps={{
                sx: { boxShadow: "none" },
              }}
              open={openCorrectionDialog}
              onClose={handleCorrectionDialogClose}
            >
              <DialogTitle
                sx={{
                  justifyContent: "space-between",
                  alignItems: "center",
                  height: "max-content",
                  padding: ".5rem",
                  paddingLeft: "1rem",
                  backgroundColor: "#EAE9FF40",
                  display: "flex",
                }}
              >
                <Typography variant="h6">REMARKS</Typography>
  
                <IconButton
                  sx={{ width: "max-content" }}
                  onClick={handleCorrectionDialogClose}
                  children={<CloseIcon />}
                />
              </DialogTitle>
  
              <DialogContent sx={{ padding: ".5rem 1rem" }}>
                <Stack>
                  <Box sx={{ minWidth: 400 }}>
                    <FormControl sx={{ height: "auto" }} fullWidth>
                      <TextField
                        sx={{ backgroundColor: "#F5F5F5" }}
                        onChange={handleRemarks}
                        value={remarks}
                        multiline
                        placeholder={"ENTER REMARKS FOR CORRECTION"}
                        inputProps={{maxLength: 254}}
                      ></TextField>
                    </FormControl>
                  </Box>
                </Stack>
              </DialogContent>
              <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
                <Button
                  sx={{ width: "max-content", textTransform: "capitalize" }}
                  onClick={handleCorrectionDialogClose}
                >
                  Cancel
                </Button>
                <Button
                  className="button_primary--normal"
                  type="save"
                  onClick={onBankKeyCorrection}
                  variant="contained"
                >
                  Submit
                </Button>
              </DialogActions>
            </Dialog>
  
            <Dialog
              hideBackdrop={false}
              elevation={2}
              PaperProps={{
                sx: { boxShadow: "none" },
              }}
              open={openRemarkDialog}
              onClose={handleRemarksDialogClose}
            >
              <DialogTitle
                sx={{
                  justifyContent: "space-between",
                  alignItems: "center",
                  height: "max-content",
                  padding: ".5rem",
                  paddingLeft: "1rem",
                  backgroundColor: "#EAE9FF40",
                  // borderBottom: "1px solid grey",
                  display: "flex",
                }}
              >
                <Typography variant="h6">REMARKS</Typography>
  
                <IconButton
                  sx={{ width: "max-content" }}
                  onClick={handleRemarksDialogClose}
                  children={<CloseIcon />}
                />
              </DialogTitle>
  
              <DialogContent sx={{ padding: ".5rem 1rem" }}>
                <Stack>
                  <Box sx={{ minWidth: 400 }}>
                    <FormControl sx={{ height: "auto" }} fullWidth>
                      <TextField
                        sx={{ backgroundColor: "#F5F5F5" }}
                        onChange={handleRemarks}
                        value={remarks}
                        multiline
                        placeholder={"ENTER REMARKS"}
                        inputProps={{maxLength: 254}}
                      ></TextField>
                    </FormControl>
                    {remarksValidationError && (
                      <Grid>
                        <Typography style={{ color: "red" }}>
                            Please Enter Remarks
                        </Typography>
                      </Grid>
                    )}
                  </Box>
                </Stack>
              </DialogContent>
              <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
                <Button
                  sx={{ width: "max-content", textTransform: "capitalize" }}
                  onClick={handleRemarksDialogClose}
                >
                  Cancel
                </Button>
                <Button
                  className="button_primary--normal"
                  type="save"
                  onClick={onCostCenterSubmitRemarks}
                  variant="contained"
                >
                  Submit
                </Button>
              </DialogActions>
            </Dialog>
  
            {/* error validation dialog */}
            <Dialog
              open={dialogOpen}
              fullWidth
              onClose={handleDialogClose}
              sx={{
                "&::webkit-scrollbar": {
                  width: "1px",
                },
              }}
            >
              <DialogTitle
                sx={{
                  justifyContent: "space-between",
                  alignItems: "center",
                  height: "max-content",
                  padding: ".5rem",
                  paddingLeft: "1rem",
                  backgroundColor: "#EAE9FF40",
  
                  display: "flex",
                }}
              >
                <Typography variant="h6" color="red">
                  Errors
                </Typography>
  
                <IconButton
                  sx={{ width: "max-content" }}
                  onClick={handleDialogClose}
                  children={<CloseIcon />}
                />
              </DialogTitle>
              <DialogContent sx={{ padding: ".5rem 1rem" }}>
                <ReusableTable
                  isLoading={isLoading}
                  width="100%"
                  rows={validationRows}
                  columns={validationColumns}
                  pageSize={10}
                  getRowId={"id"}
                  hideFooter={true}
                  checkboxSelection={false}
                  disableSelectionOnClick={true}
                  status_onRowSingleClick={true}
                  stopPropagation_Column={"action"}
                  status_onRowDoubleClick={true}
                />
              </DialogContent>
  
              <DialogActions
                sx={{ display: "flex", justifyContent: "end" }}
              ></DialogActions>
            </Dialog>
  
            <Backdrop
              sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
              open={blurLoading}
              // onClick={handleClose}
            >
              <CircularProgress color="inherit" />
            </Backdrop>
  
            {successMsg && (
              <ReusableSnackBar
                openSnackBar={openSnackbar}
                alertMsg={messageDialogMessage}
                handleSnackBarClose={handleSnackBarClose}
              />
            )}
  
            <ReusableDialog
              dialogState={openMessageDialog}
              openReusableDialog={handleMessageDialogClickOpen}
              closeReusableDialog={handleMessageDialogClose}
              dialogTitle={messageDialogTitle}
              dialogMessage={messageDialogMessage}
              handleDialogConfirm={handleMessageDialogClose}
              dialogOkText={"OK"}
              handleExtraButton={handleMessageDialogNavigate}
              dialogSeverity={messageDialogSeverity}
            />
  
            <div style={{ ...outermostContainer, backgroundColor: "#FAFCFF" }}>
              <Grid container sx={outermostContainer_Information}>
                <Grid item md={12} sx={{ display: "flex", marginBottom: "0" }}>
                  <Grid item md={11} sx={{ display: "flex" }}>
                    <Grid>
                      <IconButton
                        color="primary"
                        aria-label="upload picture"
                        component="label"
                        sx={iconButton_SpacingSmall}
                      >
                        <ArrowCircleLeftOutlinedIcon
                          style={{
                            height: "1em",
                            width: "1em",
                            color: "#000000",
                          }}
                          onClick={() => {
                            //navigate("/masterDataCockpit/profitCenter");
                            navigate(-1);
                          }}
                        />
                      </IconButton>
                    </Grid>
  
                    {task?.processDesc === "Mass Create" ? (
                      <Grid>
                        <Typography variant="h3">
                          <strong>Create Multiple Bank Key</strong>
                        </Typography>
                        <Typography variant="body2" color="#777">
                          This view displays list of Bank keys
                        </Typography>
                      </Grid>
                    ) : massBKRowData?.requestType === "Mass Create" ? (
                      <Grid>
                        <Typography variant="h3">
                          <strong>Create Multiple Bank Key</strong>
                        </Typography>
                        <Typography variant="body2" color="#777">
                          This view displays list of Bank Keys
                        </Typography>
                      </Grid>
                    ) : massBKRowData?.requestType === "Mass Change" ? (
                      <Grid>
                        <Typography variant="h3">
                          <strong>Change Multiple Bank Key</strong>
                        </Typography>
                        <Typography variant="body2" color="#777">
                          This view displays list of Bank Keys
                        </Typography>
                      </Grid>
                    ) : task?.processDesc === "Mass Change" ? (
                      <Grid>
                        <Typography variant="h3">
                          <strong>Change Multiple Bank Key</strong>
                        </Typography>
                        <Typography variant="body2" color="#777">
                          This view displays list of Bank Keys
                        </Typography>
                      </Grid>
                    ) : (
                      ""
                    )}
                  </Grid>
                  <Grid item md={1} sx={{ display: "flex" }}>
                    <Tooltip title="Uploaded documents" arrow>
                      <IconButton onClick={handleOpenDialog}>
                        <AttachFileOutlinedIcon />
                      </IconButton>
                    </Tooltip>
                  </Grid>
                  <Dialog
                    hideBackdrop={false}
                    elevation={2}
                    PaperProps={{
                      sx: { boxShadow: "none" ,width:"100%"},
                    }}
                    open={openDialog}
                    onClose={handleCloseDialog}
                  >
                    <DialogTitle
                      sx={{
                        justifyContent: "space-between",
                        alignItems: "center",
                        height: "max-content",
                        padding: ".5rem",
                        paddingLeft: "1rem",
                        backgroundColor: "#EAE9FF40",
                        // borderBottom: "1px solid grey",
                        display: "flex",
                      }}
                    >
                      {userData?.role === "Finance" ? (
                        <>
                          <Typography variant="h6">Add Attachment</Typography>
                        </>
                      ) : (
                        ""
                      )}
                    </DialogTitle>
                    <DialogContent sx={{ padding: ".5rem 1rem" }}>
                      <Card sx={{ padding: "1rem 1rem 0rem 1rem" }}>
                        {userData?.role === "Finance" ? (
                          <>
                            <Stack>
                              <Box sx={{ minWidth: 400 }}>
                                <ReusableAttachementAndComments
                                  title="BankKey"
                                  useMetaData={false}
                                  artifactId={bkNumber}
                                  artifactName="BankKey"
                                />
                              </Box>
                            </Stack>
                          </>
                        ) : (
                          ""
                        )}
                        <Grid
                          container
                          sx={{
                            display: "flex",
                            justifyContent: "space-between",
                          }}
                        >
                          <Typography variant="h6">
                            <strong>Attachments</strong>
                          </Typography>
                        </Grid>
                        {Boolean(attachments.length) && (
                          <ReusableTable
                            width="50%"
                            rows={attachments}
                            columns={attachmentColumns}
                            hideFooter={false}
                            getRowIdValue={"id"}
                            disableSelectionOnClick={true}
                            stopPropagation_Column={"action"}
                          />
                        )}
                        {!Boolean(attachments.length) && (
                          <Typography variant="body2">
                            No Attachments Found
                          </Typography>
                        )}
                        <br />
                        <Typography variant="h6">Comments</Typography>
                        {Boolean(comments.length) && (
                          <Timeline
                            sx={{
                              [`& .${timelineItemClasses.root}:before`]: {
                                flex: 0,
                                padding: 0,
                              },
                            }}
                          >
                            {comments.map((comment) => (
                              <TimelineItem>
                                <TimelineSeparator>
                                  <TimelineDot>
                                    <CheckCircleOutlineOutlined
                                      sx={{ color: "#757575" }}
                                    />
                                  </TimelineDot>
                                  <TimelineConnector />
                                </TimelineSeparator>
                                <TimelineContent sx={{ py: "12px", px: 2 }}>
                                  <Card
                                    elevation={0}
                                    sx={{
                                      border: 1,
                                      borderColor: "#C4C4C4",
                                      borderRadius: "8px",
                                      width: "650px",
                                    }}
                                  >
                                    <Box sx={{ padding: "1rem" }}>
                                      <Stack spacing={1}>
                                        <Grid
                                          sx={{
                                            display: "flex",
                                            justifyContent: "space-between",
                                          }}
                                        >
                                          <Typography
                                            sx={{
                                              textAlign: "right",
                                              color: " #757575",
                                              fontWeight: "500",
                                              fontSize: "12px",
                                            }}
                                          >
                                            {moment(comment.createdAt).format(
                                              "DD MMM YYYY"
                                            )}
                                          </Typography>
                                        </Grid>
  
                                        <Typography
                                          sx={{
                                            fontSize: "12px",
  
                                            color: " #757575",
                                            fontWeight: "500",
                                          }}
                                        >
                                          {comment.user}
                                        </Typography>
                                        <Typography
                                          sx={{
                                            fontSize: "12px",
                                            color: "#1D1D1D",
                                            fontWeight: "600",
                                          }}
                                        >
                                          {comment.comment}
                                        </Typography>
                                      </Stack>
                                    </Box>
                                  </Card>
                                </TimelineContent>
                              </TimelineItem>
                            ))}
                          </Timeline>
                        )}
                        {!Boolean(comments.length) && (
                          <Typography variant="body2">
                            No Comments Found
                          </Typography>
                        )}
                        <br />
                      </Card>
                    </DialogContent>
                    <DialogActions>
                      <Button onClick={handleCloseDialog}>Close</Button>
                    </DialogActions>
                  </Dialog>
                </Grid>
              </Grid>
  
              <Grid item sx={{ position: "relative" }}>
                {/* <Stack> */}
                <ReusableTable
                  isLoading={isLoading}
                  width="100%"
                  title={"Mass Bank Key List (" + initialRows?.length + ")"}
                  rows={initialRows}
                  columns={columns}
                  pageSize={10}
                  getRowIdValue={"id"}
                  hideFooter={false}
                  checkboxSelection={
                    (userData?.role === "Finance" &&
                      task?.processDesc === "Mass Create") ||
                    (userData?.role === "Finance" &&
                    massBKRowData?.requestType === "Mass Create") ||
                    (userData?.role === "Finance" &&
                      task?.processDesc === "Mass Change") ||
                    (userData?.role === "Finance" &&
                    massBKRowData?.requestType === "Mass Change")
                  }
                  disableSelectionOnClick={true}
                  status_onRowSingleClick={true}
                  onRowsSelectionHandler={handleSelectionModelChange}
                  callback_onRowSingleClick={(params) => {
                    console.log("paramss", params);
                    const bankKey = params.row.bankKey;
                    console.log(multipleBankKeyData,"multipleBankKeyData=====")
                    const dataToSend = multipleBankKeyData.find(
                      (item) => item.BankKey === bankKey
                    );
                    console.log(dataToSend, "yo");
                    navigate(
                      `/masterDataCockpit/bankkey/massBKTableRequestBench/displayMultipleBankKeyRequestBench/${bankKey}`,
                      {
                        state: {
                          rowData: params.row,
                          requestNumber: massBKRowData?.requestId.slice(3),
                          tabsData: dataToSend,
                          requestbenchRowData: massBKRowData,
                        },
                      }
                    );
                  }}
                  stopPropagation_Column={"action"}
                  status_onRowDoubleClick={true}
                />
                {/* </Stack> */}
              </Grid>
            </div>
  
            {checkIwaAccess(iwaAccessData, "Bank Key", "ChangeBK") ? (
              <Paper
                sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                elevation={2}
              >
                <BottomNavigation
                  className="container_BottomNav"
                  showLabels
                  sx={{
                    display: "flex",
  
                    justifyContent: "flex-end",
                  }}
                  value={value}
                >
                  {(userData?.role === "MDM Steward" &&
                    task?.processDesc === "Mass Create") ||
                  (userData?.role === "MDM Steward" &&
                  massBKRowData?.requestType === "Mass Create") ? (
                    <>
                      <Button
                       variant="outlined"
                      size="small"
                      sx={{ button_Outlined, mr: 1 }}
                        onClick={handleOpenCorrectionDialog}
                      >
                        Correction
                      </Button>
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Primary }}
                        onClick={handleOpenRemarkDialog}
                      >
                        Submit For Approval
                      </Button>
                    </>
                  ) : (userData?.role === "Approver" &&
                      task?.processDesc === "Mass Create") ||
                    (userData?.role === "Approver" &&
                      massBKRowData?.requestType === "Mass Create") ? (
                    <>
                      <Button
                        variant="outlined"
                      size="small"
                      sx={{ button_Outlined, mr: 1 }}
                        onClick={handleOpenCorrectionDialog}
                      >
                        Correction
                      </Button>
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Primary, mr: 1 }}
                        onClick={onValidateBankKeyApprove}
                      >
                        Validate
                      </Button>
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Primary }}
                        onClick={handleOpenRemarkDialog}
                        disabled={approveDisabled}
                      >
                        Approve
                      </Button>
                    </>
                  ) : (userData?.role === "Finance" &&
                      task?.processDesc === "Mass Create") ||
                    (userData?.role === "Finance" &&
                    massBKRowData?.requestType === "Mass Create") ? (
                    <>
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Primary, mr: 1 }}
                        onClick={onValidateBankKey}
                        disabled={!testRun}
                      >
                        Validate
                      </Button>
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Primary }}
                        onClick={handleOpenRemarkDialog}
                        disabled={submitForReviewDisabled}
                      >
                        Submit For Review
                      </Button>
                    </>
                  ) : (
                    ""
                  )}
  
                  {(userData?.role === "MDM Steward" &&
                    task?.processDesc === "Mass Change") ||
                  (userData?.role === "MDM Steward" &&
                  massBKRowData?.requestType === "Mass Change") ? (
                    <>
                      <Button
                       variant="outlined"
                      size="small"
                      sx={{ button_Outlined, mr: 1 }}
                        onClick={handleOpenCorrectionDialog}
                      >
                        Correction
                      </Button>
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Primary }}
                        onClick={handleOpenRemarkDialog}
                      >
                        Submit For Approval
                      </Button>
                    </>
                  ) : (userData?.role === "Approver" &&
                      task?.processDesc === "Mass Change") ||
                    (userData?.role === "Approver" &&
                    massBKRowData?.requestType === "Mass Change") ? (
                    <>
                      <Button
                        variant="outlined"
                      size="small"
                      sx={{ button_Outlined, mr: 1 }}
                        onClick={handleOpenCorrectionDialog}
                      >
                        Correction
                      </Button>
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Primary, mr: 1 }}
                        onClick={onValidateBankKeyApprove}
                      >
                        Validate
                      </Button>
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Primary }}
                        onClick={handleOpenRemarkDialog}
                        disabled={approveDisabled}
                      >
                        Approve
                      </Button>
                    </>
                  ) : (userData?.role === "Finance" &&
                      task?.processDesc === "Mass Change") ||
                    (userData?.role === "Finance" &&
                    massBKRowData?.requestType === "Mass Change") ? (
                    <>
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Primary, mr: 1 }}
                        onClick={onValidateBankKey}
                        disabled={!testRun}
                      >
                        Validate
                      </Button>
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Primary }}
                        onClick={handleOpenRemarkDialog}
                        disabled={submitForReviewDisabled}
                      >
                        Submit For Review
                      </Button>
                    </>
                  ) : (
                    ""
                  )}
                </BottomNavigation>
              </Paper>
            ) : (
              ""
            )}
          </div>
        )}
      </>
    );
  };
  
  export default MassBKTableRequestBench;
  