import{r as p,s as m,q as u,a as t,j as x,b4 as b,G as i,T as f,B as w,aC as C,F as y,ai as D,ay as N}from"./index-75c1660a.js";const S=({compCodesTabDetails:s=[],displayCompCode:n=[]})=>{let r=Object==null?void 0:Object.entries(s);p.useState([]);const l=m(),a=u(e=>e.AllDropDown.dropDown.CompCodeBasedOnControllingArea??[]);console.log(s,n,a,"props1234");const c=[{field:"id",headerName:"ID",type:"text",hide:"true",editable:"false"},{field:"companyCodes",headerName:"Company Codes",width:350,editable:"false"},{field:"companyName",headerName:"Company Name",type:"text",editable:"false",width:350},{field:"assigned",headerName:"Assigned",width:350,editable:"true",renderCell:e=>(console.log("params",e),t(N,{sx:{padding:0},checked:e.row.assigned=="X",onChange:h=>{console.log("dddd",a,h.target.checked);let d=0,g=a==null?void 0:a.map(o=>o.id===e.row.id?(d++,{...o,assigned:o.assigned==""?"X":""}):o);console.log("finalcount",d),l(D({keyName:"CompCodeBasedOnControllingArea",data:g}))}}))}];return t(y,{children:r==null?void 0:r.map(e=>x(i,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...b},children:[t(i,{container:!0,children:t(f,{sx:{fontSize:"12px",fontWeight:"700"},children:e[0]})}),t(w,{children:t(C,{width:"100%",rows:n[0]?n:a??[],columns:c,getRowIdValue:"id",hideFooter:!0,checkboxSelection:!1})})]}))})};export{S as C};
