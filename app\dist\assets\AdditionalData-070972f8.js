import{q as J,r as F,e as ce,j as V,a as n,E as de,h as ne,V as Le,aF as ke,W as qe,T as oe,eo as K,ep as v,l as H,F as re,X as ve,t as se,a5 as te,s as he,u as ge,b0 as ue,K as Re,Z as _e,$ as Se,bG as le,aw as Ge,M as Fe,ar as We,I as fe,dm as Be,B as xe,b8 as $e,G,dw as p,bR as ee,bl as ae,eq as Ve,aC as Oe,dp as Pe,a_ as be,er as Q,b4 as He,ay as ze,es as je,al as Me,v as Ye,w as Je}from"./index-17b8d91e.js";import{d as me}from"./DeleteOutline-30de3a49.js";import{u as Ae}from"./useChangeLogUpdate-f322f7d1.js";import{S as ye}from"./SingleSelectDropdown-29664b58.js";import{u as Ze}from"./useCustomDtCall-15db5725.js";const Ke=({params:e,disabled:E=!1,handleCellEdit:i,isAdd:u=!0})=>{var j,Y,P,c,C,b,a,d,t,s,g,N,y,L,R,A,f,M,k;const o=J(r=>r.payload.payloadData),l=o==null?void 0:o.Region,[U,_]=F.useState(!1),[T,w]=F.useState(""),[x,D]=F.useState(""),[h,O]=F.useState(""),{t:S}=ce(),m=u?(j=e==null?void 0:e.row)==null?void 0:j.materialDescription:((Y=e==null?void 0:e.row)==null?void 0:Y.globalMaterialDescription)||"",z=()=>{var r;w(m.slice(0,(r=v)==null?void 0:r.US).trim()),D(m.slice(27).trim()),O(""),_(!0)},W=()=>{_(!1)},q=()=>{var Z,X,ie,Ie,Ce,De,Ne,Ue,Ee;if(!T.trim()){O((Z=te)==null?void 0:Z.MAIN_DESCR_MANDATORY);return}let r=T.toUpperCase().padEnd((X=v)==null?void 0:X.US," "),I=x.toUpperCase(),B=I?`${r} ${I}`:r.trim(),$;if(l===((ie=K)==null?void 0:ie.US)?$=/^[A-Z0-9\/"\-\s]*$/:l===((Ie=K)==null?void 0:Ie.EUR)&&($=/^[A-Z0-9"\-\s]*$/),!$.test(B)){O(l===((Ce=K)==null?void 0:Ce.US)?(De=te)==null?void 0:De.DESCRIPTION_VALIDITY_US:(Ne=te)==null?void 0:Ne.DESCRIPTION_VALIDITY_EUR);return}(Ue=e==null?void 0:e.row)!=null&&Ue.id&&(u?i((Ee=e==null?void 0:e.row)==null?void 0:Ee.id,B):i({id:e.row.id,field:"globalMaterialDescription",value:B})),_(!1)};return V(re,{children:[n(ne,{title:n("span",{style:{whiteSpace:"pre"},children:m}),arrow:!0,children:n("span",{children:n(de,{fullWidth:!0,variant:"outlined",disabled:E,size:"small",value:m,placeholder:S("ENTER MATERIAL DESCRIPTION"),onClick:z,InputProps:{readOnly:!0}})})}),V(Le,{open:U,onClose:W,maxWidth:"sm",fullWidth:!0,children:[n(ke,{children:S("Enter Material Description")}),V(qe,{children:[V(oe,{children:[S("Material Description")," (",l===((P=K)==null?void 0:P.EUR)?`Max ${(c=v)==null?void 0:c.EUR} Chars, Only Alphabets`:`Max ${(C=v)==null?void 0:C.US} Chars`,") ",n("span",{style:{color:(a=(b=H)==null?void 0:b.error)==null?void 0:a.red},children:"*"})]}),n(de,{variant:"outlined",fullWidth:!0,size:"small",placeholder:l===((d=K)==null?void 0:d.EUR)?`Enter description (Max ${(t=v)==null?void 0:t.EUR} chars)`:`Enter First Part (Max ${(s=v)==null?void 0:s.US} chars)`,value:T,onChange:r=>{var B,$,Z,X,ie;const I=r.target.value.toUpperCase();w(l===((B=K)==null?void 0:B.EUR)?(Z=I==null?void 0:I.replace(/[^A-Z0-9"\-\s]/g,""))==null?void 0:Z.slice(0,($=v)==null?void 0:$.EUR):(ie=I==null?void 0:I.replace(/[^A-Z0-9\/"\-\s]/g,""))==null?void 0:ie.slice(0,(X=v)==null?void 0:X.US))},helperText:l===((g=K)==null?void 0:g.EUR)?`${T.length}/${(N=v)==null?void 0:N.EUR} characters used (Only letters, numbers, quotes, hyphen and spaces allowed)`:`${T.length}/${(y=v)==null?void 0:y.US} characters used (Only letters, numbers, /, ", - and spaces allowed)`}),l===((L=K)==null?void 0:L.US)&&V(re,{children:[n(oe,{sx:{marginTop:2},children:`Special Material Description (Max ${(R=v)==null?void 0:R.US_SPECIAL} Chars)`}),n(de,{variant:"outlined",fullWidth:!0,size:"small",placeholder:`Enter special Description (Max ${(A=v)==null?void 0:A.US_SPECIAL} chars)`,value:x,onChange:r=>{var B,$;const I=r.target.value.toUpperCase();D(($=I==null?void 0:I.replace(/[^A-Z0-9\/"\-\s]/g,""))==null?void 0:$.slice(0,(B=v)==null?void 0:B.US_SPECIAL))},helperText:`${x.length}/${(f=v)==null?void 0:f.US_SPECIAL} characters used (Optional)`})]}),h&&n(oe,{color:(k=(M=H)==null?void 0:M.error)==null?void 0:k.dark,sx:{marginTop:1},children:h})]}),V(ve,{children:[n(se,{onClick:W,color:"secondary",children:"Cancel"}),n(se,{onClick:q,color:"primary",variant:"contained",disabled:!T.trim(),children:"Save"})]})]})]})},Xe=({materialID:e,selectedMaterialNumber:E,disabled:i})=>{var P;const{t:u}=ce(),o=he(),l=ge(),{updateChangeLog:U}=Ae(),T=new URLSearchParams(l.search).get("RequestId"),w=J(c=>c.payload.payloadData),x=J(c=>c.payload),D=((P=x[e])==null?void 0:P.additionalData)||[],[h,O]=F.useState([]);F.useEffect(()=>{S(),m()},[e]);const S=async()=>{var c,C,b;try{const a=t=>O((t==null?void 0:t.body)||[]),d=()=>{var t;return ue((t=te)==null?void 0:t.ERROR_FETCHING_LANGU,"error")};Re(`/${_e}${(C=(c=Se)==null?void 0:c.DATA)==null?void 0:C.GET_LANGUAGE}`,"get",a,d)}catch{ue((b=te)==null?void 0:b.ERROR_FETCHING_LANGU,"error")}},m=()=>{var b,a,d,t,s;const c=((a=(b=x[e])==null?void 0:b.headerData)==null?void 0:a.globalMaterialDescription)||((s=(t=(d=x[e])==null?void 0:d.additionalData)==null?void 0:t[0])==null?void 0:s.materialDescription)||"";let C=[...D];D.length?C[0]={...C[0],materialDescription:c}:C=[{id:1,language:"EN",materialDescription:c}],o(le({materialID:e,data:C}))},z=(c,C)=>{const b=D.map(a=>a.id===C.id?{...a,language:c.target.value}:a);o(le({materialID:e,data:b}))},W=(c,C)=>{const b=D.map(a=>a.id===c?{...a,materialDescription:C}:a);if(o(le({materialID:e,data:b})),T&&!p.includes(w==null?void 0:w.RequestStatus)){const a=D.find(d=>d.id===c);a&&U({materialID:E,viewName:ee.DESCRIPTION,plantData:a.language,fieldName:"Material Description",jsonName:"materialDescription",currentValue:C,requestId:w==null?void 0:w.RequestId,childRequestId:T,isDescriptionData:!0,language:a.language})}},q=()=>{const c=Array.isArray(D)?D:[],b={id:c.length>0?Math.max(...c.map(a=>a.id))+1:1,language:"",materialDescription:"",isNew:!0};o(le({materialID:e,data:[...c,b]}))},j=c=>{if(c===1)return;const C=D.filter(b=>b.id!==c);o(le({materialID:e,data:C}))},Y=F.useMemo(()=>[{field:"id",headerName:u("ID"),flex:.2,align:"center",headerAlign:"center"},{field:"language",headerName:u("Language"),flex:.4,type:"singleSelect",align:"center",headerAlign:"center",editable:!0,renderCell:c=>{const C=c.id===1,b=D.filter(a=>a.id!==c.id).map(a=>a.language);return n(We,{fullWidth:!0,children:n(Ge,{sx:{height:"31px",width:"100%"},value:c.value||(C?"EN":""),onChange:a=>!C&&z(a,c),displayEmpty:!0,renderValue:a=>{var d;return a?`${a} - ${((d=h.find(t=>t.code===a))==null?void 0:d.desc)||""}`:n("span",{style:{color:H.primary.grey,fontSize:"12px"},children:"Select Language"})},disabled:C||i,children:h.map(a=>n(Fe,{value:a.code,disabled:b.includes(a.code),children:`${a.code} - ${a.desc}`},a.code))})})}},{field:"materialDescription",headerName:u("Material Description"),flex:1,align:"center",headerAlign:"center",editable:!1,renderCell:c=>n(Ke,{disabled:i,params:c,handleCellEdit:W})},{field:"actions",headerName:u("Actions"),flex:.3,align:"center",headerAlign:"center",sortable:!1,renderCell:c=>(c.row.id,n(ne,{title:c.row.isNew?u("Delete row"):u("Cannot delete existing row"),children:n("span",{children:n(fe,{onClick:()=>j(c.row.id),disabled:!c.row.isNew||i,size:"small",color:"error",children:n(me,{fontSize:"small"})})})}))}],[D,h,i]);return V("div",{children:[n(xe,{sx:{width:"50%",height:"50vh"},className:"confirmOrder-lineItem",children:n(Be,{rows:D??[],columns:Y,getRowId:c=>c.id,hideFooter:!0,disableSelectionOnClick:!0,style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%"}})}),n(G,{children:!i&&n(se,{variant:"outlined",sx:{...$e,mt:2},onClick:q,children:u("Add Row")})})]})},Qe=e=>{var c,C,b,a,d;const E=he(),i=J(t=>t.AllDropDown.dropDown),u=J(t=>t.payload),o=((c=u[e.materialID])==null?void 0:c.unitsOfMeasureData)||[];let l=(b=(C=u[e==null?void 0:e.materialID])==null?void 0:C.payloadData)==null?void 0:b["Basic Data"],U=(a=u[e.materialID])==null?void 0:a.headerData;const _=((d=u[e.materialID])==null?void 0:d.UniqueAltUnit)||[],{updateChangeLog:T}=Ae(),{getDtCall:w,dtData:x}=Ze(),D=ge(),O=new URLSearchParams(D.search).get("RequestId"),S=J(t=>t.payload.payloadData),{t:m}=ce(),z=F.useCallback((t,s,g)=>{var L;const N={...o==null?void 0:o.find(R=>R.id===t),[s]:g};if(s==="aUnit"){const R=(L=i==null?void 0:i.BaseUom)==null?void 0:L.find(A=>A.code===g);N.measureUnitText=(R==null?void 0:R.desc)||""}const y=o.map(R=>R.id===t?N:R);E(ae({materialID:e.materialID,data:y})),O&&!p.includes(S==null?void 0:S.RequestStatus)&&T({materialID:e.selectedMaterialNumber,viewName:ee.ADDITIONAL_DATA,plantData:N.aUnit||"",fieldName:s,jsonName:s,currentValue:N[s],requestId:S==null?void 0:S.RequestId,childRequestId:e.requestId,isUnitOfMeasure:!0,uomId:N.UomId||null})},[o,e.materialID,e.selectedMaterialNumber,E,T]),W=(t,s)=>{const g=t!=null&&t.value?s==null?void 0:s.find(N=>N.code===t.value):null;return n(ne,{title:(g==null?void 0:g.desc)||m("No value selected"),arrow:!0,placement:"top",children:n("div",{style:{width:"100%"},children:n(ye,{options:t.field==="aUnit"?i==null?void 0:i.BaseUom:t.field==="eanCategory"?i==null?void 0:i.CategoryOfInternationalArticleNumberEAN:t.field==="unitsOfDimension"?i==null?void 0:i.BaseUom:t.field==="volumeUnit"?i==null?void 0:i.Volumeunit:t.field==="weightUnit"?i==null?void 0:i.UnitOfWt:[],value:g,onChange:N=>{z(t.row.id,t.field,N==null?void 0:N.code)},disabled:(e==null?void 0:e.disabled)||(t==null?void 0:t.field)==="eanCategory",placeholder:m("Select Option"),isOptionDisabled:N=>_.includes(N.code)})})})},q=t=>{var y;const s=t.row.aUnit===((y=l==null?void 0:l.basic)==null?void 0:y.BaseUom),g=t.field==="xValue"||t.field==="yValue",N=(e==null?void 0:e.disabled)||s&&g||(t==null?void 0:t.field)==="eanUpc";return n(de,{fullWidth:!0,size:"small",value:s&&g?"1":t.value||"",onChange:L=>z(t.row.id,t.field,L.target.value),disabled:N,InputProps:{sx:{"&.Mui-disabled":{"& input":{WebkitTextFillColor:H.text.primary,color:H.text.primary}}}}})},j=[{field:"id",headerName:m("ID"),width:80,hide:!0},{field:"xValue",headerName:"X",width:150,editable:!1,renderCell:q},{field:"aUnit",headerName:m("AUn"),width:150,editable:!1,renderCell:t=>W(t,i==null?void 0:i.BaseUom)},{field:"yValue",headerName:m("Y"),width:150,editable:!1,renderCell:q},{field:"eanUpc",headerName:m("EAN/UPC"),width:150,editable:!1,renderCell:q},{field:"eanCategory",headerName:m("EAN Category"),width:160,editable:!1,renderCell:t=>W(t,i==null?void 0:i.CategoryOfInternationalArticleNumberEAN)},{field:"length",headerName:m("Length"),width:120,editable:!1,renderCell:q},{field:"width",headerName:m("Width"),width:120,editable:!1,renderCell:q},{field:"height",headerName:m("Height"),width:120,editable:!1,renderCell:q},{field:"unitsOfDimension",headerName:m("Unit of Dimension"),width:160,editable:!1,renderCell:t=>W(t,i==null?void 0:i.BaseUom)},{field:"volume",headerName:m("Volume"),width:120,editable:!1,renderCell:q},{field:"volumeUnit",headerName:m("Volume Unit"),width:160,editable:!1,renderCell:t=>W(t,i==null?void 0:i.Volumeunit)},{field:"grossWeight",headerName:m("Gross Weight"),width:140,editable:!1,renderCell:q},{field:"netWeight",headerName:m("Net Weight"),width:140,editable:!1,renderCell:q},{field:"weightUnit",headerName:m("Weight Unit"),width:160,editable:!1,renderCell:t=>W(t,i==null?void 0:i.UnitOfWt)},{field:"actions",headerName:m("Actions"),width:100,sortable:!1,renderCell:t=>n(ne,{title:t.row.isNew?"Delete row":"Cannot delete existing row",children:n("span",{children:n(fe,{onClick:()=>P(t.row.id),disabled:!t.row.isNew||e.disabled,size:"small",color:"error",children:n(me,{fontSize:"small"})})})})}],Y=()=>{var s,g,N,y,L,R,A,f;let t={decisionTableId:null,decisionTableName:Pe.MDG_MAT_REGION_MATTYPE_PLANT_UOM_DT,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":(s=u==null?void 0:u.payloadData)==null?void 0:s.Region,"MDG_CONDITIONS.MDG_MAT_MATERIAL_TYPE":((g=u==null?void 0:u.payloadData)==null?void 0:g.Region)==="EUR"?"ALL":(N=U==null?void 0:U.materialType)==null?void 0:N.code,"MDG_CONDITIONS.MDG_MAT_PLANT":((y=u==null?void 0:u.payloadData)==null?void 0:y.Region)==="US"?"ALL":((L=U==null?void 0:U.orgData)==null?void 0:L.length)>1?"1610":(f=(A=(R=U==null?void 0:U.orgData[0])==null?void 0:R.plant)==null?void 0:A.value)==null?void 0:f.code}]};w(t)};F.useEffect(()=>{o!=null&&o.length||Y()},[]),F.useEffect(()=>{var t,s,g,N,y,L;if((s=(t=x==null?void 0:x.data)==null?void 0:t.result)!=null&&s[0]){let R=(N=(g=x==null?void 0:x.data)==null?void 0:g.result)==null?void 0:N[0].MDG_MAT_REGION_MATTYPE_PLANT_UOM_DT;if(!R||!Array.isArray(R)){ue((y=te)==null?void 0:y.NO_DATA_AVAILABLE,"error");return}let A=R.map((f,M)=>{var I,B,$,Z;const k=(I=i==null?void 0:i.BaseUom)==null?void 0:I.find(X=>X.code===f.MDG_MAT_UOM),r=f.MDG_MAT_UOM===((B=l==null?void 0:l.basic)==null?void 0:B.BaseUom);return{id:M+1,uomId:null,xValue:"1",aUnit:f.MDG_MAT_UOM||"",measureUnitText:(k==null?void 0:k.desc)||"",yValue:"1",bUnit:f.MDG_MAT_UOM||"",measurementUnitText:((Z=($=l==null?void 0:l.basic)==null?void 0:$.BaseUom)==null?void 0:Z.desc)||"",eanUpc:"",eanCategory:"",autoCheckDigit:"",addEans:"",length:f.MDG_MAT_LENGTH||"",width:f.MDG_MAT_WIDTH||"",height:f.MDG_MAT_HEIGHT||"",unitsOfDimension:f.MDG_MAT_UNIT_DIMENSIONS||"",volume:f.MDG_MAT_VOLUME||"",volumeUnit:f.MDG_MAT_VOLUME_UNIT||"",grossWeight:f.MDG_MAT_GROSS_NET_WEIGHT||"",netWeight:"",weightUnit:f.MDG_MAT_WEIGHT_UNIT||"",noLowerLvlUnits:"",lowerLvlUnits:"",remVolAfterNesting:"",maxStackFactor:"",maxTopLoadFullPkg:"",UomToploadFullPkg:"",capacityUsage:"",UomCategory:""}});A=A.sort((f,M)=>{var k,r;return f.aUnit===((k=l==null?void 0:l.basic)==null?void 0:k.BaseUom)?-1:M.aUnit===((r=l==null?void 0:l.basic)==null?void 0:r.BaseUom)?1:0}),JSON.stringify(A)!==JSON.stringify((L=u[e.materialID])==null?void 0:L.unitsOfMeasureData)&&E(ae({materialID:e==null?void 0:e.materialID,data:A}))}},[x]),F.useEffect(()=>{if(o!=null&&o.length){const t=[...new Set(o.map(s=>s.aUnit).filter(Boolean))];E(Ve({materialID:e==null?void 0:e.materialID,data:t}))}},[o]);const P=t=>{const s=o.filter(g=>g.id!==t);E(ae({materialID:e==null?void 0:e.materialID,data:s}))};return n("div",{children:n(G,{container:!0,direction:"row",sx:{backgroundColor:"white",padding:2},children:V(G,{item:!0,xs:12,mt:4,children:[n(Oe,{rows:o,columns:j,getRowIdValue:"id",hideFooter:!1,checkboxSelection:!1,disableSelectionOnClick:!0,onCellEditCommit:t=>{z(t.id,t.field,t.value)},width:"100%",title:m("Units of Measure/ EANs/ Dimensions"),showSearch:!1,showRefresh:!1,showExport:!1,showFilter:!0,showColumns:!0}),!(e!=null&&e.disabled)&&n(se,{variant:"outlined",sx:{mt:2},onClick:()=>{const s={id:o.length>0?Math.max(...o.map(g=>g.id))+1:1,isNew:!0,uomId:null,xValue:"1",aUnit:"",measureUnitText:"",yValue:"1",bUnit:"",measurementUnitText:"",eanUpc:"",eanCategory:"",autoCheckDigit:"",addEans:"",length:"",width:"",height:"",unitsOfDimension:"",volume:"",volumeUnit:"",grossWeight:"",netWeight:"",weightUnit:"",noLowerLvlUnits:"",lowerLvlUnits:"",remVolAfterNesting:"",maxStackFactor:"",maxTopLoadFullPkg:"",UomToploadFullPkg:"",capacityUsage:"",UomCategory:""};E(ae({materialID:e==null?void 0:e.materialID,data:[...o,s]}))},children:m("Add Row")})]})})})},we=({value:e,onChange:E,disabled:i})=>n(ze,{checked:e||!1,onChange:u=>E(u.target.checked),disabled:i}),pe=({value:e,onChange:E,disabled:i,maxLength:u})=>n(de,{variant:"outlined",fullWidth:!0,size:"small",value:e||"",onChange:o=>{const l=o.target.value;/^\d*$/.test(l)&&E(l)},disabled:i,inputProps:{pattern:"[0-9]*",inputMode:"numeric"},sx:{"& .MuiInputBase-input":{color:H.black.dark,fontSize:"12px"},"& .MuiInputBase-input.Mui-disabled":{WebkitTextFillColor:H.black.dark,color:H.black.dark},"& .MuiOutlinedInput-root":{"&.Mui-disabled":{"& > input":{WebkitTextFillColor:H.black.dark,color:H.black.dark}}}}}),Te=({options:e=[],value:E,onChange:i,disabled:u,placeholder:o,isOptionDisabled:l})=>{const U=e.find(T=>T.code===E),_=U?`${U.code} - ${U.desc||""}`:"";return n(ye,{options:e,value:_,onChange:i,disabled:u,placeholder:o,isOptionDisabled:l})},et=e=>{var j,Y,P,c,C,b;const E=he(),i=!1,u=J(a=>a.AllDropDown.dropDown),o=J(a=>a.payload),l=((j=o[e.materialID])==null?void 0:j.eanData)||[],U=((Y=o[e.materialID])==null?void 0:Y.UniqueAltUnit)||[],_=((P=o[e.materialID])==null?void 0:P.unitsOfMeasureData)||[];(c=o==null?void 0:o.payloadData)!=null&&c.Region,(b=(C=o[e==null?void 0:e.materialID])==null?void 0:C.payloadData)==null||b["Basic Data"];const T=ge(),{updateChangeLog:w}=Ae(),D=new URLSearchParams(T.search).get("RequestId"),h=J(a=>a.payload.payloadData),O=a=>{E(je({materialID:e.materialID,data:a}))},{t:S}=ce(),m=(a,d,t)=>{var s,g,N,y,L,R;if(d===((s=Q)==null?void 0:s.EAN_UPC)){const A=l.find(r=>r.id===a),f=A.altunit,M=l.some(r=>r.id!==a&&r.altunit===f&&r.MainEan===!0),k=l.map(r=>r.id===a?{...r,[d]:t,au:!1,MainEan:M?!1:!!t}:r);if(O(k),_.length>0&&t&&!M){const r=_.map(I=>I.aUnit===f?{...I,eanUpc:t,eanCategory:A.eanCategory}:I);JSON.stringify(r)!==JSON.stringify(_)&&E(ae({materialID:e.materialID,data:r}))}D&&!p.includes(h==null?void 0:h.RequestStatus)&&w({materialID:e.materialID,viewName:ee.ADDITIONAL_EAN_DATA,plantData:"",fieldName:"EanUpc",jsonName:(g=Q)==null?void 0:g.EAN_UPC,currentValue:t,requestId:h==null?void 0:h.RequestId,isAdditionalEAN:!0,eanId:A.EanId||null,childRequestId:D})}else if(d==="MainEan"&&t===!0){const A=l.find(r=>r.id===a),f=A.altunit,k=l.map(r=>r.altunit===f?{...r,MainEan:!1}:r).map(r=>r.id===a?{...r,MainEan:!0}:r);if(O(k),_.length>0){const r=_.map(I=>I.aUnit===f?{...I,eanUpc:A.eanUpc,eanCategory:A.eanCategory}:I);JSON.stringify(r)!==JSON.stringify(_)&&E(ae({materialID:e.materialID,data:r}))}D&&!p.includes(h==null?void 0:h.RequestStatus)&&w({materialID:e.materialID,viewName:ee.ADDITIONAL_EAN_DATA,plantData:"",fieldName:"MainEan",jsonName:(N=Q)==null?void 0:N.MAIN_EAN,currentValue:t,requestId:h==null?void 0:h.RequestId,childRequestId:D,isAdditionalEAN:!0,eanId:A.EanId||null})}else if(d==="eanCategory"){const A=l.find(M=>M.id===a),f=l.map(M=>M.id===a?{...M,[d]:t,MainEan:!1}:M);O(f),D&&!p.includes(h==null?void 0:h.RequestStatus)&&w({materialID:e.materialID,viewName:ee.ADDITIONAL_EAN_DATA,plantData:"",fieldName:"EanCat",jsonName:(y=Q)==null?void 0:y.EAN_CATEGORY,currentValue:t,requestId:h==null?void 0:h.RequestId,childRequestId:D,isAdditionalEAN:!0,eanId:A.EanId||null})}else if(d==="au"&&t===!0){const A=l.find(f=>f.id===a);if(A.eanUpc){const f=k=>{const r=l.map(I=>I.id===a?{...I,[d]:t,eanUpc:I.eanUpc+k.body,MainEan:!1}:I);O(r),D&&!p.includes(h==null?void 0:h.RequestStatus)&&w({materialID:e.materialID,viewName:ee.ADDITIONAL_EAN_DATA,plantData:"",fieldName:"other",jsonName:"au",currentValue:t,requestId:h==null?void 0:h.RequestId,childRequestId:D,isAdditionalEAN:!0,eanId:A.EanId||null})},M=k=>{var r;ue((r=te)==null?void 0:r.ERROR_FETCHING_DATA,"error")};Re(`/${_e}${(R=(L=Se)==null?void 0:L.DATA)==null?void 0:R.GET_CHECK_DIGIT}?number=${A.eanUpc}`,"get",f,M,{eanUpc:A.eanUpc})}}else{const A=l.map(M=>M.id===a?{...M,[d]:t}:M),f=l.find(M=>M.id===a);O(A),D&&!p.includes(h==null?void 0:h.RequestStatus)&&w({materialID:e.materialID,viewName:ee.ADDITIONAL_EAN_DATA,plantData:"",fieldName:d,jsonName:d,currentValue:t,requestId:h==null?void 0:h.RequestId,childRequestId:D,isAdditionalEAN:!0,eanId:f.EanId||null})}},z=a=>{const d=l.filter(t=>t.id!==a);O(d)},W=()=>{const a={id:be(),EanId:null,altunit:"",MainEan:!1,eanUpc:"",eanCategory:"",au:!1,isNew:!0};O([...l,a])};F.useEffect(()=>{if(U.length){const a=l.filter(g=>U.includes(g.altunit)||g.altunit===""),d=a.map(g=>g.altunit),s=U.filter(g=>!d.includes(g)).map(g=>({id:be(),EanId:null,altunit:g,MainEan:!1,eanUpc:"",eanCategory:"",au:!1}));O([...a,...s])}},[U]);const q=[{field:"altunit",headerName:S("Alt. Unit"),width:200,renderCell:a=>n(Te,{options:(u==null?void 0:u.BaseUom)||[],value:a.row.altunit,onChange:d=>m(a.row.id,"altunit",d==null?void 0:d.code),disabled:e==null?void 0:e.disabled,placeholder:"SELECT Alt. Unit",isOptionDisabled:d=>!U.includes(d.code)})},{field:"eanUpc",headerName:S("EAN/UPC"),width:180,renderCell:a=>{var d,t;return n("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:n(ne,{title:a.row[(d=Q)==null?void 0:d.EAN_UPC]||"",arrow:!0,children:n("div",{style:{width:"100%"},children:n(pe,{value:a.row[(t=Q)==null?void 0:t.EAN_UPC],onChange:s=>{var g;return m(a.row.id,(g=Q)==null?void 0:g.EAN_UPC,s)},disabled:e==null?void 0:e.disabled,maxLength:13,onClick:s=>s.stopPropagation(),onDoubleClick:s=>s.stopPropagation()})})})})}},{field:"eanCategory",headerName:S("EAN Cat."),width:200,renderCell:a=>n(Te,{options:(u==null?void 0:u.EanCat)||[],value:a.row.eanCategory,onChange:d=>m(a.row.id,"eanCategory",d==null?void 0:d.code),disabled:e==null?void 0:e.disabled,placeholder:"SELECT EAN Cat.",isOptionDisabled:d=>l.filter(s=>s.altunit===a.row.altunit&&s.id!==a.row.id).some(s=>s.eanCategory===d.code)})},{field:"au",headerName:S("Au"),width:150,renderCell:a=>n(we,{value:a.row.au,onChange:d=>m(a.row.id,"au",d),disabled:(e==null?void 0:e.disabled)||a.row.au})},{field:"MainEan",headerName:S("Main EAN"),width:160,renderCell:a=>n(we,{value:a.row.MainEan,onChange:d=>m(a.row.id,"MainEan",d),disabled:e==null?void 0:e.disabled})},{field:"actions",headerName:S("Actions"),width:180,renderCell:a=>n(ne,{title:a.row.isNew?"Delete row":"Cannot delete existing row",children:n("span",{children:n(fe,{onClick:()=>z(a.row.id),disabled:(e==null?void 0:e.disabled)||!a.row.isNew,color:"error",size:"small",children:n(me,{fontSize:"small"})})})})}];return n("div",{children:n(G,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:.25,...He},children:V(G,{container:!0,display:"block",sx:{marginLeft:"-10px"},children:[n(G,{item:!0,xs:4}),V(G,{item:!0,xs:10,mt:4,children:[n(Oe,{title:"Additional EANs/Units of Measure",isLoading:i,rows:l,columns:q,getRowIdValue:"id",hideFooter:!1,checkboxSelection:!1,disableSelectionOnClick:!0,status_onRowSingleClick:!0,stopPropagation_Column:["eanUpc","action"],status_onRowDoubleClick:!0,width:"100%"}),!(e!=null&&e.disabled)&&n(se,{variant:"outlined",sx:{mt:2},onClick:W,disabled:!(_!=null&&_.length),children:S("Add Row")})]})]})})})},dt=e=>{const{t:E}=ce(),[i,u]=F.useState(0),o=window.location.href.includes("DisplayMaterialSAPView"),l=[E("Description"),E("Units of Measure"),...o?[]:[E("Additional EANs")]],U=[[n(re,{children:n(Xe,{materialID:e==null?void 0:e.materialID,selectedMaterialNumber:e==null?void 0:e.selectedMaterialNumber,disabled:e.disableCheck})})],[n(re,{children:n(Qe,{materialID:e==null?void 0:e.materialID,selectedMaterialNumber:e==null?void 0:e.selectedMaterialNumber,disabled:e.disableCheck})})],...o?[]:[[n(re,{children:n(et,{materialID:e==null?void 0:e.materialID,selectedMaterialNumber:e==null?void 0:e.selectedMaterialNumber,disabled:e.disableCheck})})]]],_=(T,w)=>{u(w)};return n("div",{children:n(G,{container:!0,style:{...Me,backgroundColor:"#FAFCFF"},children:n(G,{sx:{width:"inherit"},children:n(G,{container:!0,style:{padding:"0 1rem 0 1rem"},children:n(G,{container:!0,sx:Me,children:n(G,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:V(G,{container:!0,children:[n(Ye,{value:i,onChange:_,variant:"scrollable",sx:{background:"#FFF",borderBottom:"1px solid #BDBDBD",width:"100%"},"aria-label":"mui tabs example",children:l.map((T,w)=>n(Je,{sx:{fontSize:"12px",fontWeight:"700"},label:T},w))}),U[i].map((T,w)=>n(xe,{sx:{mb:2,width:"100%"},children:n(oe,{variant:"body2",children:T})},w))]})})})})})})})};export{dt as default};
