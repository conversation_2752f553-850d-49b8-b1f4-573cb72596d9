import {
  Backdrop,
  BottomNavigation,
  Box,
  Button,
  Card,
  Checkbox,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  Paper,
  Stack,
  Step,
  StepButton,
  Stepper,
  Tab,
  Tabs,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import CloseIcon from "@mui/icons-material/Close";
import {
  button_Outlined,
  iconButton_SpacingSmall,
  button_Primary,
  outermostContainer,
  outermostContainer_Information,
} from "../../Common/commonStyles";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import ReusableTable from "../../Common/ReusableTable";
import { useLocation, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { checkIwaAccess, idGenerator } from "../../../functions";
import {
  destination_CostCenter,
  destination_DocumentManagement,
} from "../../../destinationVariables";
import {
  setMultipleCostCenterData,
  setMultipleCostCenterRequestBench,
} from "../../../app/costCenterTabsSlice";
import { doAjax } from "../../Common/fetchService";
import ReusableSnackBar from "../../Common/ReusableSnackBar";
import moment from "moment";
import ReusableDialog from "../../Common/ReusableDialog";
import LoadingComponent from "../../Common/LoadingComponent";
import {
  FamilyRestroomOutlined,
  HelpOutlineOutlined,
} from "@mui/icons-material";
import { Timeline } from "rsuite";
import { CheckCircleOutlineOutlined } from "@mui/icons-material";
import {
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineItem,
  TimelineSeparator,
  timelineItemClasses,
} from "@mui/lab";
import ReusableAttachementAndComments from "../../Common/ReusableAttachmentAndComments/ReusableAttachementAndComments";
import AttachFileOutlinedIcon from "@mui/icons-material/AttachFileOutlined";
import { MatDownload, MatView } from "../../DocumentManagement/UtilDoc";
import { setDropDown } from "../../../app/dropDownDataSlice";
import lookup from "../../../data/lookup.json";

const MassCostCenterTableRequestBench = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [blurLoading, setBlurLoading] = useState(false);
  const [value, setValue] = useState("1");
  const [selectedRows, setSelectedRows] = useState([]);
  let [factorsArray, setFactorsArray] = useState([]);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const location = useLocation();
  const massCostCenterRowData = location.state;
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [messageDialogExtra, setMessageDialogExtra] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState(false);
  const [messageDialogSeverity, setMessageDialogSeverity] = useState(false);
  const [messageDialogOK, setMessageDialogOK] = useState(true);
  const [successMsg, setsuccessMsg] = useState(false);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [openCorrectionDialog, setOpenCorrectionDialog] = useState(false);
  const [openRemarkDialog, setOpenRemarkDialog] = useState(false);
  const [submitForReviewDisabled, setSubmitForReviewDisabled] = useState(true);
  const [activeStep, setActiveStep] = React.useState(0);
  const [remarks, setRemarks] = useState("");
  const [completed, setCompleted] = React.useState({});
  const [attachments, setAttachments] = useState([]);
  const [comments, setComments] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [testRun, setTestRun] = useState(true);
  const [approveDisabled, setApproveDisabled] = useState(true);
  const [ccNumber, setCcNumber] = useState("");
  const [selectedPayloadData, setSelectedPayloadData] = useState([]);
  const [newPayload, setNewPayload] = useState({});
  const [directMatchedProfitCenters, setDirectMatchedProfitCenters] = useState(
    []
  );
  const [dialogOpen, setDialogOpen] = useState(false);
  const [costValidationError, setCostValidationErrors] = useState([]);

  const [validateFlag, setValidateFlag] = useState(false);
  const handleSnackBarOpen = () => {
    setopenSnackbar(true);
  };
  const handleSnackBarClose = () => {
    if (validateFlag) {
      setopenSnackbar(false);
      setValidateFlag(false);
    } else {
      setopenSnackbar(false);
      navigate("/masterDataCockpit/costCenter");
    }
  };

  const handleStep = (step) => () => {
    setActiveStep(step);
  };
  const handleOpenDialog = () => {
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };
  const MultipleCostCenter = useSelector(
    (state) => state.costCenter.MultipleCostCenterData
  );
  console.log(MultipleCostCenter, "MultipleCostCenter");
  const appSettings = useSelector((state) => state.appSettings);
  console.log("massCostCenterRowData", massCostCenterRowData);
  let task = useSelector((state) => state.userManagement.taskData);
  let userData = useSelector((state) => state.userManagement.userData);
  let iwaAccessData = useSelector(
    (state) => state.userManagement.entitiesAndActivities?.["Cost Center"]
  );
  const steps = [`Mass Cost Center List `, "Attachment And Comments"];
  const attachmentColumns = [
    {
      field: "id",
      headerName: "Document ID",
      flex: 1,
      hide: true,
    },
    {
      field: "docType",
      headerName: "Type",
      flex: 1,
    },
    {
      field: "docName",
      headerName: "Document Name",
      flex: 1,
    },
    {
      field: "uploadedOn",
      headerName: "Uploaded On",
      flex: 1,
      align: "center",
      headerAlign: "center",
    },
    {
      field: "uploadedBy",
      headerName: "Uploaded By",
      sortable: false,
      flex: 1,
    },
    {
      field: "action",
      headerName: "Action",
      sortable: false,
      filterable: false,
      align: "center",
      headerAlign: "center",
      flex: 1,
      renderCell: (cellValues) => {
        return (
          <>
            <MatView index={cellValues.row.id} name={cellValues.row.docName} />
            <MatDownload
              index={cellValues.row.id}
              name={cellValues.row.docName}
            />
          </>
        );
      },
    },
  ];

  const handleSelectionModelChange = (selectedIds) => {
    console.log("selected", selectedIds);
    setSelectedPayloadData(selectedIds);
    if (selectedIds.length > 0) {
      setTestRun(true);
      console.log("selectedIds1", selectedIds);
    } else {
      // console.log("select", selectedIds);
      setTestRun(false);
    }
    console.log("selectedIds", selectedIds);
    setSelectedRows(selectedIds);
    // setTestRun(true);
  };
  const getMassCostCenterTable = () => {
    setIsLoading(true);
    let payload = {};
    if (task?.processDesc === "Mass Change") {
      payload = {
        massCreationId: "",
        massChangeId: task?.subject
          ? task?.subject?.slice(3)
          : massCostCenterRowData?.requestId.slice(3),
        screenName: "Change",
      };
    } else if (task?.processDesc === "Mass Create") {
      payload = {
        massCreationId: task?.subject
          ? task?.subject?.slice(3)
          : massCostCenterRowData?.requestId.slice(3),
        massChangeId: "",
        screenName: "Create",
      };
    } else if (massCostCenterRowData?.requestType === "Mass Create") {
      payload = {
        massCreationId: massCostCenterRowData?.requestId?.slice(3),
        massChangeId: "",
        screenName: "Create",
      };
    } else if (massCostCenterRowData?.requestType === "Mass Change") {
      payload = {
        massCreationId: "",
        massChangeId: massCostCenterRowData?.requestId?.slice(3),
        screenName: "Change",
      };
    }
    const hSuccess = (data) => {
      setIsLoading(false);
      // setFactorsArray(categoryKeys);
      dispatch(setMultipleCostCenterData(data?.body));
    };

    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/displayMassCostCenter`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCorrectionDialogClose = () => {
    setOpenCorrectionDialog(false);
  };

  // Loader and lookup for independent apis start
  const [apiCount, setApiCount] = useState(0);
  const fetchDynamicApiData = (keyName, endPoint) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: keyName, data: data.body }));
      // setIsLoading(false);
      setApiCount((prev) => prev + 1);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/${endPoint}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getAllLookups = () => {
    lookup?.costCenter?.map((item) => {
      fetchDynamicApiData(item?.keyName, item?.endPoint);
    });
  };
  const loaderCount = () => {
    if (apiCount == lookup?.costCenter?.length) {
      setIsLoading(false);
      console.log("apiCount", apiCount);
    } else {
      setIsLoading(true);
    }
  };

  useEffect(() => {
    setCcNumber(idGenerator("CC"));
  }, []);
  useEffect(() => {
    loaderCount();
  }, [apiCount]);

  // Loader and lookup for independent apis end

  useEffect(() => {
    getAllLookups();
  }, []);

  useEffect(() => {
    if (MultipleCostCenter?.length === 0) {
      getMassCostCenterTable();
    } else {
      return;
    }
  }, []);

  console.log("newPayload", newPayload, selectedPayloadData);

  useEffect(() => {
    const tempObj = {};
    let tempArray = [];
    console.log('userdata', userData?.role, MultipleCostCenter)
    if (userData?.role == "MDM Steward" || userData?.role == "Approver") {
      console.log('userrole', userData?.role, MultipleCostCenter)
      tempArray = MultipleCostCenter;  
    } else {
      tempArray = MultipleCostCenter?.filter((item, index) =>
        selectedPayloadData?.includes(index)
      );
    }
    tempArray.forEach((element) => {
      if (element?.controllingArea in tempObj) {
        tempObj[element.controllingArea].push(element);
      } else {
        tempObj[element.controllingArea] = [element];
      }
    });
    setNewPayload(tempObj);
    console.log("temparray", tempArray, tempObj);
  }, [MultipleCostCenter, selectedPayloadData]);

  const initialRows = MultipleCostCenter?.map((ccenter, index) => {
    const headerData = ccenter;
    const basicData = ccenter?.viewData?.["Basic Data"] || {};
    return {
      id: index,
      costCenter: headerData.costCenter,
      controllingArea: headerData.controllingArea,
      name:
        basicData["Names"]?.find((field) => field?.fieldName === "Name")
          ?.value || "",
      description:
        basicData["Names"]?.find((field) => field?.fieldName === "Description")
          ?.value || "",
      personResponsible:
        basicData["Basic Data"]?.find(
          (field) => field?.fieldName === "Person Responsible"
        )?.value || "",
      companyCode:
        basicData["Basic Data"]?.find(
          (field) => field?.fieldName === "Company Code"
        )?.value || "",
      profitCenter:
        basicData["Basic Data"]?.find(
          (field) => field?.fieldName === "Profit Center"
        )?.value || "",
      costCenterCategory:
        basicData["Basic Data"]?.find(
          (field) => field?.fieldName === "Cost Center Category"
        )?.value || "",
      validFrom: headerData.validFrom,
      validTo: headerData.validTo,
    };
  });
  const columns = [
    {
      field: "costCenter",
      headerName: "Cost Center",
      editable: false,
      flex: 1,
      renderCell: (params) => {
        const isDirectMatch = costValidationError.find(element=> element.costCenter===params.value);
        console.log(isDirectMatch, "isDirectMatch")
        console.log(params, "params")

        if (isDirectMatch && isDirectMatch.code === 400) {
          return (
            <Typography sx={{ fontSize: "12px", color: "red" }}>
              {params.value}
            </Typography>
          );
        } else {
          return (
            <Typography sx={{ fontSize: "12px" }}>
              {params.value}
            </Typography>
          );
        }
      },
    },
    {
      field: "controllingArea",
      headerName: "Controlling Area",
      editable: false,
      flex: 1,
    },
    {
      field: "name",
      headerName: "Name",
      editable: false,
      flex: 1,
      renderCell: (params) => {
        const isDirectMatch = directMatchedProfitCenters.includes(
          params.row.profitCenterName
        );
        return (
          <Typography
            sx={{ fontSize: "12px", color: isDirectMatch ? "red" : "inherit" }}
          >
            {params.value}
          </Typography>
        );
      },
    },
    {
      field: "description",
      headerName: "Description",
      editable: false,
      flex: 1,
    },
    {
      field: "personResponsible",
      headerName: "Person Responsible",
      editable: false,
      flex: 1,
    },
    {
      field: "companyCode",
      headerName: "Company Code",
      editable: false,
      flex: 1,
    },
    {
      field: "profitCenter",
      headerName: "Profit Center",
      editable: false,
      flex: 1,
    },
    {
      field: "costCenterCategory",
      headerName: "Cost Center Category",
      editable: false,
      flex: 1,
    },

    {
      field: "validFrom",
      headerName: "Valid From",
      editable: false,
      flex: 1,
      renderCell: (params) => {
        return (
          <Typography sx={{ fontSize: "12px" }}>
            {moment(params.row.validFrom).format(appSettings?.dateFormat)}
          </Typography>
        );
      },
    },
    {
      field: "validTo",
      headerName: "Valid To",
      editable: false,
      flex: 1,
      renderCell: (params) => {
        return (
          <Typography sx={{ fontSize: "12px" }}>
            {moment(params.row.validTo).format(appSettings?.dateFormat)}
          </Typography>
        );
      },
    },
  ];
  const getValueForFieldName = (data, fieldName) => {
    const field = data?.find((field) => field?.fieldName === fieldName);
    return field ? field.value : "";
  };
  const getAttachments = () => {
    let requestId = task?.subject
      ? task?.subject
      : massCostCenterRowData?.requestId;
    let hSuccess = (data) => {
      var attachmentRows = [];
      data.documentDetailDtoList.forEach((doc) => {
        var tempRow = {
          id: doc.documentId,
          docType: doc.fileType,
          docName: doc.fileName,
          uploadedOn: moment(doc.docCreationDate).format(appSettings.date),
          uploadedBy: doc.createdBy,
        };
        if (true) attachmentRows.push(tempRow);
      });
      setAttachments(attachmentRows);
    };
    // invoiceHeaderData?.extInvNum &&
    doAjax(
      `/${destination_DocumentManagement}/documentManagement/getDocByRequestId/${requestId}`,
      "get",
      hSuccess
    );
  };

  const getComments = () => {
    let requestId = task?.subject
      ? task?.subject
      : massCostCenterRowData?.requestId;
    let hSuccess = (data) => {
      console.log("commentsdata", data);

      var commentRows = [];
      data.body.forEach((cmt) => {
        var tempRow = {
          id: cmt.requestId,
          comment: cmt.comment,
          user: cmt.createdByUser,
          createdAt: cmt.updatedAt,
        };
        commentRows.push(tempRow);
      });
      setComments(commentRows);
      console.log("commentrows", commentRows);
    };

    let hError = (error) => {
      console.log(error);
    };
    // invoiceHeaderData?.extInvNum &&
    doAjax(
      `/${destination_CostCenter}/activitylog/fetchTaskDetailsForRequestId?requestId=${requestId}`,
      "get",
      hSuccess,
      hError
    );
  };

  useEffect(() => {
    getAttachments();
    getComments();
  }, []);
  var payloadmapping = Object.keys(newPayload)?.map((y) => {
    console.log("payloadmapping", y);
    return {
      TaskId: task?.taskId ? task?.taskId : "",
      CostCenterHeaderID: MultipleCostCenter[0]?.costCenterHeaderId,
      ControllingArea: y ?? "",
      Testrun: testRun,
      Action:
        task?.processDesc === "Mass Create"
          ? "I"
          : task?.processDesc === "Mass Change"
          ? "U"
          : massCostCenterRowData?.requestType === "Mass Change"
          ? "U"
          : massCostCenterRowData?.requestType === "Mass Create"
          ? "I"
          : "",
      ReqCreatedBy: userData?.user_id ? userData?.user_id : "",
      ReqCreatedOn: userData?.createdOn
        ? "/Date(" + userData?.createdOn + ")/"
        : "",
      RequestStatus: "",
      CreationId: "",
      EditId: "",
      DeleteId: "",
      Remarks: remarks,
      MassCreationId:
        task?.processDesc === "Mass Create" ? task?.subject?.slice(3) : "",
      MassEditId:
        task?.processDesc === "Mass Change"
          ? task?.subject?.slice(3)
          : massCostCenterRowData?.requestId.slice(3),
      MassDeleteId: "",
      RequestType: "",
      MassRequestStatus: "",
      Toitem: newPayload[y].map((x) => {
        console.log("x", x);

        return {
          CostCenterID: x?.costCenterId ? x?.costCenterId : "",
          Costcenter: x?.costCenter ? x?.costCenter : "",
          ValidFrom: x?.validFrom ? x?.validFrom : "",
          ValidTo: x?.validTo ? x?.validTo : "",
          PersonInCharge: getValueForFieldName(
            x?.["viewData"]?.["Basic Data"]?.["Basic Data"],
            "Person Responsible"
          ),
          CostcenterType: getValueForFieldName(
            x?.["viewData"]?.["Basic Data"]?.["Basic Data"],
            "Cost Center Category"
          ),
          CostctrHierGrp: getValueForFieldName(
            x?.["viewData"]?.["Basic Data"]?.["Basic Data"],
            "Hierarchy Area"
          ),
          BusArea: getValueForFieldName(
            x?.["viewData"]?.["Basic Data"]?.["Basic Data"],
            "Business Area"
          ),
          CompCode: getValueForFieldName(
            x?.["viewData"]?.["Basic Data"]?.["Basic Data"],
            "Company Code"
          ),
          Currency: getValueForFieldName(
            x?.["viewData"]?.["Basic Data"]?.["Basic Data"],
            "Currency"
          ),
          ProfitCtr: getValueForFieldName(
            x?.["viewData"]?.["Basic Data"]?.["Basic Data"],
            "Profit Center"
          ),
          Name: getValueForFieldName(
            x?.["viewData"]?.["Basic Data"]?.["Names"],
            "Name"
          ),
          Descript: getValueForFieldName(
            x?.["viewData"]?.["Basic Data"]?.["Names"],
            "Description"
          ),
          PersonInChargeUser: getValueForFieldName(
            x?.["viewData"]?.["Basic Data"]?.["Names"],
            "User Responsible"
          ),

          RecordQuantity:
            getValueForFieldName(
              x?.["viewData"]?.["Control"]?.["Control"],
              "Record Quantity"
            ) === true
              ? "X"
              : "",
          LockIndActualPrimaryCosts:
            getValueForFieldName(
              x?.["viewData"]?.["Control"]?.["Control"],
              "Actual Primary Costs"
            ) === true
              ? "X"
              : "",
          LockIndPlanPrimaryCosts:
            getValueForFieldName(
              x?.["viewData"]?.["Control"]?.["Control"],
              "Plan Primary Costs"
            ) === true
              ? "X"
              : "",
          LockIndActSecondaryCosts:
            getValueForFieldName(
              x?.["viewData"]?.["Control"]?.["Control"],
              "Act. secondary Costs"
            ) === true
              ? "X"
              : "",
          LockIndPlanSecondaryCosts:
            getValueForFieldName(
              x?.["viewData"]?.["Control"]?.["Control"],
              "Plan Secondary Costs"
            ) === true
              ? "X"
              : "",
          LockIndActualRevenues:
            getValueForFieldName(
              x?.["viewData"]?.["Control"]?.["Control"],
              "Actual Revenue"
            ) === true
              ? "X"
              : "",
          LockIndPlanRevenues:
            getValueForFieldName(
              x?.["viewData"]?.["Control"]?.["Control"],
              "Plan Revenue"
            ) === true
              ? "X"
              : "",
          LockIndCommitmentUpdate:
            getValueForFieldName(
              x?.["viewData"]?.["Control"]?.["Control"],
              "Commitment Update"
            ) === true
              ? "X"
              : "",

          ConditionTableUsage: "",
          Application: "",
          CstgSheet: getValueForFieldName(
            x?.["viewData"]?.["Templates"]?.["Overhead rates"],
            "Costing Sheet"
          ),
          ActyIndepTemplate: getValueForFieldName(
            x?.["viewData"]?.["Templates"]?.["Formula planning"],
            "Acty-Indep. Form Plng Temp"
          ),
          ActyDepTemplate: getValueForFieldName(
            x?.["viewData"]?.["Templates"]?.["Formula planning"],
            "Acty-Dep. Form Plng Temp"
          ),
          AddrTitle: getValueForFieldName(
            x?.["viewData"]?.["Address"]?.["Address Data"],
            "Title"
          ),
          AddrName1: getValueForFieldName(
            x?.["viewData"]?.["Address"]?.["Address Data"],
            "Name 1"
          ),
          AddrName2: getValueForFieldName(
            x?.["viewData"]?.["Address"]?.["Address Data"],
            "Name 2"
          ),
          AddrName3: getValueForFieldName(
            x?.["viewData"]?.["Address"]?.["Address Data"],
            "Name 3"
          ),
          AddrName4: getValueForFieldName(
            x?.["viewData"]?.["Address"]?.["Address Data"],
            "Name 4"
          ),
          AddrStreet: getValueForFieldName(
            x?.["viewData"]?.["Address"]?.["Address Data"],
            "Street"
          ),
          AddrCity: getValueForFieldName(
            x?.["viewData"]?.["Address"]?.["Address Data"],
            "Location"
          ),
          AddrDistrict: getValueForFieldName(
            x?.["viewData"]?.["Address"]?.["Address Data"],
            "District"
          ),
          AddrCountry: getValueForFieldName(
            x?.["viewData"]?.["Address"]?.["Address Data"],
            "Country/Reg"
          ),
          AddrCountryIso: "",
          AddrTaxjurcode: getValueForFieldName(
            x?.["viewData"]?.["Address"]?.["Address Data"],
            "Jurisdiction"
          ),
          AddrPoBox: getValueForFieldName(
            x?.["viewData"]?.["Address"]?.["Address Data"],
            "PO Box"
          ),
          AddrPostlCode: getValueForFieldName(
            x?.["viewData"]?.["Address"]?.["Address Data"],
            "Postal Code"
          ),
          AddrPobxPcd: getValueForFieldName(
            x?.["viewData"]?.["Address"]?.["Address Data"],
            "PO Box Post Cod"
          ),
          AddrRegion: getValueForFieldName(
            x?.["viewData"]?.["Address"]?.["Address Data"],
            "Region"
          ),
          TelcoLangu: "",
          TelcoLanguIso: getValueForFieldName(
            x?.["viewData"]?.["Communication"]?.["Communication Data"],
            "Language Key"
          ),
          TelcoTelephone: getValueForFieldName(
            x?.["viewData"]?.["Communication"]?.["Communication Data"],
            "Telephone 1"
          ),
          TelcoTelephone2: getValueForFieldName(
            x?.["viewData"]?.["Communication"]?.["Communication Data"],
            "Telephone 2"
          ),
          TelcoTelebox: getValueForFieldName(
            x?.["viewData"]?.["Communication"]?.["Communication Data"],
            "Telebox Number"
          ),
          TelcoTelex: getValueForFieldName(
            x?.["viewData"]?.["Communication"]?.["Communication Data"],
            "Telex Number"
          ),
          TelcoFaxNumber: getValueForFieldName(
            x?.["viewData"]?.["Communication"]?.["Communication Data"],
            "Fax Number"
          ),
          TelcoTeletex: getValueForFieldName(
            x?.["viewData"]?.["Communication"]?.["Communication Data"],
            "Teletex Number"
          ),
          TelcoPrinter: getValueForFieldName(
            x?.["viewData"]?.["Communication"]?.["Communication Data"],
            "Printer Destination"
          ),
          TelcoDataLine: getValueForFieldName(
            x?.["viewData"]?.["Communication"]?.["Communication Data"],
            "Data Line"
          ),

          ActyDepTemplateAllocCc: getValueForFieldName(
            x?.["viewData"]?.["Templates"]?.[
              "Activity and Business Process Allocation"
            ],
            "Acty-Dep. Alloc Template"
          ),
          ActyDepTemplateSk: getValueForFieldName(
            x?.["viewData"]?.["Templates"]?.["Actual Statistical Key Figures"],
            "Templ.: Act. Stat. Key Figure"
          ),
          ActyIndepTemplateAllocCc: getValueForFieldName(
            x?.["viewData"]?.["Templates"]?.[
              "Activity and Business Process Allocation"
            ],
            "Acty-Indep. Alloc Temp"
          ),
          ActyIndepTemplateSk: getValueForFieldName(
            x?.["viewData"]?.["Templates"]?.["Actual Statistical Key Figures"],
            "Templ.: Act. Stat. Key Figure"
          ),
          AvcActive: false,
          AvcProfile: "",
          BudgetCarryingCostCtr: "",
          CurrencyIso: "",
          Department: getValueForFieldName(
            x?.["viewData"]?.["Basic Data"]?.["Basic Data"],
            "Department"
          ),
          FuncArea: getValueForFieldName(
            x?.["viewData"]?.["Basic Data"]?.["Basic Data"],
            "Functional Area"
          ),
          FuncAreaFixAssigned: "",
          FuncAreaLong: "",
          Fund: "",
          FundFixAssigned: "",
          GrantFixAssigned: "",
          GrantId: "",
          JvEquityTyp: "",
          JvJibcl: "",
          JvJibsa: "",
          JvOtype: "",
          JvRecInd: "",
          JvVenture: "",
          Logsystem: "",
        };
      }),
    };
  });
  console.log("rishav", payloadmapping);
  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };
  const handleOpenCorrectionDialog = () => {
    setOpenCorrectionDialog(true);
  };
  const handleOpenRemarkDialog = () => {
    setTestRun(false);
    setOpenRemarkDialog(true);
  };
  const handleRemarks = (e, value) => {
    //setRemarks(e.target.value);
    const newValue = e.target.value;
    if (newValue.length > 0 && newValue[0] === " ") {
      setRemarks(newValue.trimStart());
    } else {
      //let costCenterValue = e.target.value;
      let remarksUpperCase = newValue.toUpperCase();
      setRemarks(remarksUpperCase);
    }
  };
  const handleRemarksDialogClose = () => {
    setTestRun(true);
    setOpenRemarkDialog(false);
  };
  const onCostCenterCorrection = () => {
    if (
      (userData?.role === "MDM Steward" &&
        task.processDesc === "Mass Create") ||
      (userData?.role === "MDM Steward" &&
        massCostCenterRowData?.requestType === "Mass Create")
    ) {
      handleCorrectionDialogClose();
      handleCorrectionMDMCreate();
    } else if (
      (userData?.role === "Approver" && task.processDesc === "Mass Create") ||
      (userData?.role === "Approver" &&
        massCostCenterRowData?.requestType === "Mass Create")
    ) {
      handleCorrectionDialogClose();
      handleCorrectionApproverCreate();
    } else if (
      (userData?.role === "MDM Steward" &&
        task.processDesc === "Mass Change") ||
      (userData?.role === "MDM Steward" &&
        massCostCenterRowData?.requestType === "Mass Change")
    ) {
      handleCorrectionDialogClose();
      handleCorrectionMDMChange();
    } else if (
      (userData?.role === "Approver" && task.processDesc === "Mass Change") ||
      (userData?.role === "Approver" &&
        massCostCenterRowData?.requestType === "Mass Change")
    ) {
      handleCorrectionDialogClose();
      handleCorrectionApproverChange();
    }
  };
  const onCostCenterSubmitRemarks = () => {
    if (
      (userData?.role === "MDM Steward" &&
        task.processDesc === "Mass Create") ||
      (userData?.role === "MDM Steward" &&
        massCostCenterRowData?.requestType === "Mass Create")
    ) {
      setBlurLoading(true);
      handleRemarksDialogClose();
      handleSubmitForApproval();
    } else if (
      (userData?.role === "Approver" && task.processDesc === "Mass Create") ||
      (userData?.role === "Approver" &&
        massCostCenterRowData?.requestType === "Mass Create")
    ) {
      setBlurLoading(true);
      handleRemarksDialogClose();
      handleApproveMassCostCenter();
    } else if (
      (userData?.role === "Finance" && task.processDesc === "Mass Create") ||
      (userData?.role === "Finance" &&
        massCostCenterRowData?.requestType === "Mass Create")
    ) {
      setBlurLoading(true);
      handleRemarksDialogClose();
      handleSubmitForReviewCreate();
    } else if (
      (userData?.role === "MDM Steward" &&
        task.processDesc === "Mass Change") ||
      (userData?.role === "MDM Steward" &&
        massCostCenterRowData?.requestType === "Mass Change")
    ) {
      setBlurLoading(true);
      handleRemarksDialogClose();
      handleSubmitForApprovalChange();
    } else if (
      (userData?.role === "Approver" && task.processDesc === "Mass Change") ||
      (userData?.role === "Approver" &&
        massCostCenterRowData?.requestType === "Mass Change")
    ) {
      setBlurLoading(true);
      handleRemarksDialogClose();
      handleApproveMassCostCenterChange();
    } else if (
      (userData?.role === "Finance" && task.processDesc === "Mass Change") ||
      (userData?.role === "Finance" &&
        massCostCenterRowData?.requestType === "Mass Change")
    ) {
      setBlurLoading(true);
      handleRemarksDialogClose();
      handleSubmitForReviewChange();
    }
  };
  const handleSubmitForApproval = () => {
    // setIsLoading(true);
    const payload = payloadmapping;
    console.log("paylaod", payload);
    // Assuming payloadmapping is an array with a single element
    const hSuccess = (data) => {
      setBlurLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Mass Cost Center Submitted for Approval with ID NCM${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting Mass Cost Center for Approval"
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
    };
    const hError = (error) => {
      console.log("error");
    };
    doAjax(
      `/${destination_CostCenter}/massAction/costCentersApprovalSubmit`,
      "post",
      hSuccess,
      hError,
      payloadmapping
    );
  };
  const handleSubmitForApprovalChange = () => {
    const payload = payloadmapping;
    console.log("paylaod", payload);
    // Assuming payloadmapping is an array with a single element
    const hSuccess = (data) => {
      setBlurLoading(false);
      console.log("kdfljsald");
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Mass Cost Center Submitted for Approval with ID CCM${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting Mass Cost Center for Approval"
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
    };
    const hError = (error) => {
      console.log("error");
    };
    doAjax(
      `/${destination_CostCenter}/massAction/changeCostCentersApprovalSubmit`,
      "post",
      hSuccess,
      hError,
      payloadmapping
    );
  };
  const handleApproveMassCostCenter = () => {
    const payload = payloadmapping;
    console.log("paylaod", payload);
    // Assuming payloadmapping is an array with a single element
    const hSuccess = (data) => {
      setBlurLoading(false);
      if (data.statusCode === 201) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          "Mass Cost Center Approved & SAP Syndication Completed"
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Approving Mass Cost Center ");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
    };
    const hError = (error) => {
      console.log("error");
    };
    doAjax(
      `/${destination_CostCenter}/massAction/createCostCentersApproved`,
      "post",
      hSuccess,
      hError,
      payloadmapping
    );
  };
  const handleApproveMassCostCenterChange = () => {
    // setBlurLoading(true);
    const payload = payloadmapping;
    console.log("paylaod", payload);
    // Assuming payloadmapping is an array with a single element
    const hSuccess = (data) => {
      setBlurLoading(false);
      if (data.statusCode === 201) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          "Mass Cost Center Change Approved & SAP Syndication Completed"
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Approving Mass Cost Center Change");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
    };
    const hError = (error) => {
      console.log("error");
    };
    doAjax(
      `/${destination_CostCenter}/massAction/changeCostCentersApproved`,
      "post",
      hSuccess,
      hError,
      payloadmapping
    );
  };
  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };
  const handleMessageDialogNavigate = () => {
    // navigate("/masterDataCockpit/materialMaster/materialSingle");
  };
  const handleSubmitForReviewCreate = () => {
    // setIsLoading(true);
    const selectedData = initialRows.filter((_, index) =>
      selectedRows.includes(index)
    );
    // console.log("selectedData", selectedData);
    // const selectedCostCenterRows = selectedData.map((x) => ({
    //   ...payloadmapping.Toitem[x?.id],
    // }));
    // let payload = payloadmapping;
    // payload.Toitem = selectedCostCenterRows;

    const hSuccess = (data) => {
      setBlurLoading(false);
      // setIsLoading(true);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Mass Cost Center Sent for Review with ID NCM${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        const secondApiPayload = {
          artifactId: ccNumber,
          createdBy: userData?.emailId,
          artifactType: "CostCenter",
          requestId: `NCM${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting the Cost Center for Review "
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
    };
    const hError = (error) => {
      console.log("error");
    };
    doAjax(
      `/${destination_CostCenter}/massAction/costCentersSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payloadmapping
    );
  };
  const handleSubmitForReviewChange = () => {
    // setBlurLoading(true);
    const selectedData = initialRows.filter((_, index) =>
      selectedRows.includes(index)
    );
    // console.log("selectedData", selectedData);
    // const selectedCostCenterRows = selectedData.map((x) => ({
    //   ...payloadmapping.Toitem[x?.id],
    // }));
    // let payload = payloadmapping;
    // payload.Toitem = selectedCostCenterRows;

    const hSuccess = (data) => {
      setBlurLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Mass Cost Center Change Sent for Review with ID CCM${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        const secondApiPayload = {
          artifactId: ccNumber,
          createdBy: userData?.emailId,
          artifactType: "CostCenter",
          requestId: `CCM${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting the Mass Cost Center for Review "
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
    };
    const hError = (error) => {
      console.log("error");
    };
    doAjax(
      `/${destination_CostCenter}/massAction/changeCostCentersSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payloadmapping
    );
  };
  const onValidateCostCenterApprove = () => {
    setBlurLoading(true);

    let payload = payloadmapping;
    const hSuccess = (data) => {
      // setIsLoading(false);
      if (data.statusCode === 400) {
        setCostValidationErrors(data.body);
        setDialogOpen(true);
        setBlurLoading(false);
      } else {
        // Handle success
        setApproveDisabled(false);
        setMessageDialogTitle("Create");
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `All Data has been Validated. Cost Center can be Sent for Review`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
        setValidateFlag(true);
        setBlurLoading(false);
      }
    };
    const hError = (error) => {
      console.log(error);
    };

    // Call the main API for validation
    doAjax(
      `/${destination_CostCenter}/massAction/validateMassCostCenter`,
      "post",
      hSuccess,
      hError,
      payloadmapping
    );
  };
  const checkCCNameEditorNot =(duplicateCCNameViewData,duplicateCCName)=>{
    return duplicateCCNameViewData.every(element => !duplicateCCName.includes(element))
  }
  const onValidateCostCenter = () => {
    setBlurLoading(true);
    const selectedData = initialRows.filter((_, index) =>
      selectedRows.includes(index)
    );
    console.log("selectedData", selectedData);
    // const selectedProfitCenterRows = selectedData.map((x) => ({
    //   ...payloadmapping.Toitem[x?.id],
    // }));
    // console.log("selectedProfitCenterRows", selectedProfitCenterRows);
    const duplicateCheckPayload = [];
    const duplicateCCName=[]
    
    selectedData.map((x) => {
      var idk = {
        coArea: x?.controllingArea,
        name: x?.name,
      };
      duplicateCheckPayload.push(idk);
      duplicateCCName.push(x?.name.toUpperCase())
    });
    const duplicateCCNameViewData=[]

    MultipleCostCenter.map((ccObject) =>{
      //duplicateCCNameViewData.push(ccName.costCenterName)
      ccObject?.viewData?.["Basic Data"]?.["Names"].map((field)=>{
          if (field.fieldName === 'Name'){
            duplicateCCNameViewData.push(field.value.toUpperCase())
          }
      })
      //duplicateCCNameViewData
    })
    console.log(duplicateCCNameViewData,duplicateCCName,"arrayofviewand")
    let isuserEditCCName=checkCCNameEditorNot(duplicateCCNameViewData,duplicateCCName)

    console.log("duplicateCheckPayload", duplicateCheckPayload);
    // let payload = payloadmapping;
    // payload.Toitem = selectedProfitCenterRows;
    const hSuccess = (data) => {
      // setIsLoading(false);
      if (data.statusCode === 400) {
        // Handle error
        setCostValidationErrors(data.body);
        setDialogOpen(true);
        setBlurLoading(false);
      } else {
        // Handle success
        setMessageDialogTitle("Create");
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `All Data has been Validated. Cost Center can be Sent for Review`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
        setValidateFlag(true);
        setBlurLoading(false);

        // Now, make the duplicate check API call
        // Ensure that the conditions for making the duplicate check API call are met
        if (
          duplicateCheckPayload.coArea !== "" ||
          duplicateCheckPayload.name !== ""
        ) {
          // payloadmapping.Toitem = duplicateCheckPayload.name;
          //massProfitRowData?.requestType === "Mass Change"
          if (massCostCenterRowData?.requestType === "Mass Change" && !isuserEditCCName){
            setBlurLoading(false)
          }else{
            doAjax(
              `/${destination_CostCenter}/alter/fetchCCDescriptionsDupliChk`,
              "post",
              hDuplicateCheckSuccess,
              hDuplicateCheckError,
              duplicateCheckPayload
            );
          }
        }
      }
    };

    const hDuplicateCheckSuccess = (data) => {
      console.log("dataaaa", data);
      // Handle success of duplicate check
      // if (data.body?.length === 0 || !data.body.some(item => item.toUpperCase() === duplicateCheckPayload.name)) {
      if (
        data.body.length === 0 ||
        !data.body.some((item) =>
          duplicateCheckPayload.some(
            (payloadItem) => payloadItem.name.toUpperCase() === item.matches[0]
          )
        )
      ) {
        // No direct match, enable the "Submit for Review" button
        setBlurLoading(false);
        setSubmitForReviewDisabled(false);
        setTestRun(true);
      } else {
        // Handle direct match
        setBlurLoading(false);
        setMessageDialogTitle("Duplicate Check");
        setsuccessMsg(false);
        setMessageDialogMessage(
          `There is a direct match for the Cost Center name.`
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setSubmitForReviewDisabled(true);
        setDirectMatchedProfitCenters(directMatches);
      }
    };

    const hDuplicateCheckError = (error) => {
      // Handle error of duplicate check
      console.log(error);
    };

    const hError = (error) => {
      console.log(error);
    };

    // Call the main API for validation
    doAjax(
      `/${destination_CostCenter}/massAction/validateMassCostCenter`,
      "post",
      hSuccess,
      hError,
      payloadmapping
    );
  };
  const handleCorrectionMDMCreate = () => {
    const selectedData = initialRows.filter((_, index) =>
      selectedRows.includes(index)
    );
    const payload = payloadmapping;
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Cost Center Submitted for Correction with ID NCM${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Cost Center for Correction");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_CostCenter}/massAction/costCentersSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payloadmapping
    );
  };
  const handleCorrectionMDMChange = () => {
    const selectedData = initialRows.filter((_, index) =>
      selectedRows.includes(index)
    );
    const payload = payloadmapping;
    const hSuccess = (data) => {
      setIsLoading(FamilyRestroomOutlined);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Cost Center Submitted for Correction with ID CCM${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Cost Center for Correction");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_CostCenter}/massAction/changeCostCentersSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payloadmapping
    );
  };
  const handleCorrectionApproverCreate = () => {
    console.log("isLoading7", isLoading);
    const selectedData = initialRows.filter((_, index) =>
      selectedRows.includes(index)
    );
    const payload = payloadmapping;
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Cost Center Submitted for Correction with ID NCM${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Cost Center for Correction");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_CostCenter}/massAction/costCentersSendForReview`,
      "post",
      hSuccess,
      hError,
      payloadmapping
    );
  };
  const handleCorrectionApproverChange = () => {
    const selectedData = initialRows.filter((_, index) =>
      selectedRows.includes(index)
    );
    const payload = payloadmapping;
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Cost Center Submitted for Correction with ID CCM${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Cost Center for Correction");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_CostCenter}/massAction/changeCostCentersSendForReview`,
      "post",
      hSuccess,
      hError,
      payloadmapping
    );
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
  };

  const validationRows = costValidationError
  ?.filter((row) => row?.code === 400)
  ?.map((item, index) => {
    if (item.code === 400) {
      return {
        id: index,
        costCenter: item?.costCenter,
        error: item?.status?.message,
      };
    }
  });

  const validationColumns = [
    {
      field: "costCenter",
      headerName: "Cost Center",
      editable: false,
      flex: 1,
      // width: 100,
    },
    {
      field: "error",
      headerName: "Error",
      editable: false,
      flex: 1,
      // width: 400,
    },
  ];

  // const getStepContent = (step) => {
  //   switch (step) {
  //     case 0:
  //       return (

  //       );
  //     case 1:
  //       return (
  //         <>
  //           <Card sx={{ padding: "1rem 1rem 0rem 1rem" }}>
  //             <Grid
  //               container
  //               sx={{
  //                 display: "flex",
  //                 justifyContent: "space-between",
  //               }}
  //             >
  //               <Typography variant="h6">
  //                 <strong>Attachments</strong>
  //               </Typography>
  //             </Grid>
  //             {Boolean(attachments.length) && (
  //               <ReusableTable
  //                 width="100%"
  //                 rows={attachments}
  //                 columns={attachmentColumns}
  //                 hideFooter={false}
  //                 getRowIdValue={"id"}
  //                 disableSelectionOnClick={true}
  //                 stopPropagation_Column={"action"}
  //               />
  //             )}
  //             {!Boolean(attachments.length) && (
  //               <Typography variant="body2">No Attachments Found</Typography>
  //             )}
  //             <br />
  //             <Typography variant="h6">Comments</Typography>
  //             {Boolean(comments.length) && (
  //               <Timeline
  //                 sx={{
  //                   [`& .${timelineItemClasses.root}:before`]: {
  //                     flex: 0,
  //                     padding: 0,
  //                   },
  //                 }}
  //               >
  //                 {comments.map((comment) => (
  //                   <TimelineItem>
  //                     <TimelineSeparator>
  //                       <TimelineDot>
  //                         <CheckCircleOutlineOutlined
  //                           sx={{ color: "#757575" }}
  //                         />
  //                       </TimelineDot>
  //                       <TimelineConnector />
  //                     </TimelineSeparator>
  //                     <TimelineContent sx={{ py: "12px", px: 2 }}>
  //                       <Card
  //                         elevation={0}
  //                         sx={{
  //                           border: 1,
  //                           borderColor: "#C4C4C4",
  //                           borderRadius: "8px",
  //                           width: "650px",
  //                         }}
  //                       >
  //                         <Box sx={{ padding: "1rem" }}>
  //                           <Stack spacing={1}>
  //                             <Grid
  //                               sx={{
  //                                 display: "flex",
  //                                 justifyContent: "space-between",
  //                               }}
  //                             >
  //                               <Typography
  //                                 sx={{
  //                                   textAlign: "right",
  //                                   color: " #757575",
  //                                   fontWeight: "500",
  //                                   fontSize: "12px",
  //                                 }}
  //                               >
  //                                 {moment(comment.createdAt).format(
  //                                   "DD MMM YYYY"
  //                                 )}
  //                               </Typography>
  //                             </Grid>

  //                             <Typography
  //                               sx={{
  //                                 fontSize: "12px",

  //                                 color: " #757575",
  //                                 fontWeight: "500",
  //                               }}
  //                             >
  //                               {comment.user}
  //                             </Typography>
  //                             <Typography
  //                               sx={{
  //                                 fontSize: "12px",
  //                                 color: "#1D1D1D",
  //                                 fontWeight: "600",
  //                               }}
  //                             >
  //                               {comment.comment}
  //                             </Typography>
  //                           </Stack>
  //                         </Box>
  //                       </Card>
  //                     </TimelineContent>
  //                   </TimelineItem>
  //                 ))}
  //               </Timeline>
  //             )}
  //             {!Boolean(comments.length) && (
  //               <Typography variant="body2">No Comments Found</Typography>
  //             )}
  //             <br />
  //           </Card>
  //         </>
  //       );

  //     default:
  //       return "Unknown step";
  //   }
  // };

  return (
    <>
      {/* {isLoading === true ? (
        <LoadingComponent />
      ) : ( */}
      <div style={{ backgroundColor: "#FAFCFF" }}>
        <Dialog
          hideBackdrop={false}
          elevation={2}
          PaperProps={{
            sx: { boxShadow: "none" },
          }}
          open={openCorrectionDialog}
          onClose={handleCorrectionDialogClose}
        >
          <DialogTitle
            sx={{
              justifyContent: "space-between",
              alignItems: "center",
              height: "max-content",
              padding: ".5rem",
              paddingLeft: "1rem",
              backgroundColor: "#EAE9FF40",
              display: "flex",
            }}
          >
            <Typography variant="h6">Remarks</Typography>

            <IconButton
              sx={{ width: "max-content" }}
              onClick={handleCorrectionDialogClose}
              children={<CloseIcon />}
            />
          </DialogTitle>

          <DialogContent sx={{ padding: ".5rem 1rem" }}>
            <Stack>
              <Box sx={{ minWidth: 400 }}>
                <FormControl sx={{ height: "auto" }} fullWidth>
                  <TextField
                    sx={{ backgroundColor: "#F5F5F5" }}
                    onChange={handleRemarks}
                    value={remarks}
                    multiline
                    placeholder={"Enter Remarks for Correction"}
                    inputProps={{maxLength: 254}}
                  ></TextField>
                </FormControl>
              </Box>
            </Stack>
          </DialogContent>
          <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
            <Button
              sx={{ width: "max-content", textTransform: "capitalize" }}
              onClick={handleCorrectionDialogClose}
            >
              Cancel
            </Button>
            <Button
              className="button_primary--normal"
              type="save"
              onClick={onCostCenterCorrection}
              variant="contained"
            >
              Submit
            </Button>
          </DialogActions>
        </Dialog>

        <Dialog
          hideBackdrop={false}
          elevation={2}
          PaperProps={{
            sx: { boxShadow: "none" },
          }}
          open={openRemarkDialog}
          onClose={handleRemarksDialogClose}
        >
          <DialogTitle
            sx={{
              justifyContent: "space-between",
              alignItems: "center",
              height: "max-content",
              padding: ".5rem",
              paddingLeft: "1rem",
              backgroundColor: "#EAE9FF40",
              // borderBottom: "1px solid grey",
              display: "flex",
            }}
          >
            <Typography variant="h6">Remarks</Typography>

            <IconButton
              sx={{ width: "max-content" }}
              onClick={handleRemarksDialogClose}
              children={<CloseIcon />}
            />
          </DialogTitle>

          <DialogContent sx={{ padding: ".5rem 1rem" }}>
            <Stack>
              <Box sx={{ minWidth: 400 }}>
                <FormControl sx={{ height: "auto" }} fullWidth>
                  <TextField
                    sx={{ backgroundColor: "#F5F5F5" }}
                    onChange={handleRemarks}
                    value={remarks}
                    multiline
                    placeholder={"Enter Remarks"}
                    inputProps={{maxLength: 254}}
                  ></TextField>
                </FormControl>
              </Box>
            </Stack>
          </DialogContent>
          <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
            <Button
              sx={{ width: "max-content", textTransform: "capitalize" }}
              onClick={handleRemarksDialogClose}
            >
              Cancel
            </Button>
            <Button
              className="button_primary--normal"
              type="save"
              onClick={onCostCenterSubmitRemarks}
              variant="contained"
            >
              Submit
            </Button>
          </DialogActions>
        </Dialog>

        <Dialog
            open={dialogOpen}
            fullWidth
            onClose={handleDialogClose}
            sx={{
              "&::webkit-scrollbar": {
                width: "1px",
              },
              // paddingBottom:1
            }}
          >
            <DialogTitle
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF40",
                // borderBottom: "1px solid grey",
                display: "flex",
              }}
            >
              <Typography variant="h6" color="red">
                Errors
              </Typography>

              <IconButton
                sx={{ width: "max-content" }}
                onClick={handleDialogClose}
                children={<CloseIcon />}
              />
            </DialogTitle>
            <DialogContent sx={{ padding: ".5rem 1rem" }}>
              {/* <Grid container> */}

              {validationRows && (
                <ReusableTable
                  isLoading={isLoading}
                  width="100%"
                  // title={"Profit Center Master List (" + initialRows.length + ")"}
                  rows={validationRows}
                  columns={validationColumns}
                  pageSize={10}
                  getRowIdValue={"id"}
                  hideFooter={true}
                  checkboxSelection={false}
                  disableSelectionOnClick={true}
                  status_onRowSingleClick={true}
                  stopPropagation_Column={"action"}
                  status_onRowDoubleClick={true}
                />
              )}

              {/* </Grid> */}
            </DialogContent>

            <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
             
            </DialogActions>
          </Dialog>

        <ReusableDialog
          dialogState={openMessageDialog}
          openReusableDialog={handleMessageDialogClickOpen}
          closeReusableDialog={handleMessageDialogClose}
          dialogTitle={messageDialogTitle}
          dialogMessage={messageDialogMessage}
          handleDialogConfirm={handleMessageDialogClose}
          dialogOkText={"OK"}
          handleExtraButton={handleMessageDialogNavigate}
          dialogSeverity={messageDialogSeverity}
        />

        {successMsg && (
          <ReusableSnackBar
            openSnackBar={openSnackbar}
            alertMsg={messageDialogMessage}
            handleSnackBarClose={handleSnackBarClose}
          />
        )}
        <div style={{ ...outermostContainer, backgroundColor: "#FAFCFF" }}>
          <Grid container sx={outermostContainer_Information}>
            <Grid item md={12} sx={{ display: "flex", marginBottom: "0" }}>
              <Grid item md={11} sx={{ display: "flex" }}>
                <Grid>
                  <IconButton
                    // onClick={handleBacktoRO}
                    color="primary"
                    aria-label="upload picture"
                    component="label"
                    sx={iconButton_SpacingSmall}
                  >
                    <ArrowCircleLeftOutlinedIcon
                      style={{
                        height: "1em",
                        width: "1em",
                        color: "#000000",
                      }}
                      // sx={{
                      //   fontSize: "1.5em",
                      //   color: "#000000",
                      // }}
                      onClick={() => {
                        //navigate("/masterDataCockpit/costCenter");
                        navigate(-1);
                      }}
                    />
                  </IconButton>
                </Grid>
                {task.processDesc === "Mass Create" ? (
                  <Grid>
                    <Typography variant="h3">
                      <strong>Create Multiple Cost Center</strong>
                    </Typography>
                    <Typography variant="body2" color="#777">
                      This view displays list of uploaded Cost Centers
                    </Typography>
                  </Grid>
                ) : massCostCenterRowData?.requestType === "Mass Create" ? (
                  <Grid>
                    <Typography variant="h3">
                      <strong>Create Multiple Cost Center</strong>
                    </Typography>
                    <Typography variant="body2" color="#777">
                      This view displays list of Cost Centers
                    </Typography>
                  </Grid>
                ) : massCostCenterRowData?.requestType === "Mass Change" ? (
                  <Grid>
                    <Typography variant="h3">
                      <strong>Change Multiple Cost Center</strong>
                    </Typography>
                    <Typography variant="body2" color="#777">
                      This view displays list of Cost Centers
                    </Typography>
                  </Grid>
                ) : task?.processDesc === "Mass Change" ? (
                  <Grid>
                    <Typography variant="h3">
                      <strong>Change Multiple Cost Center</strong>
                    </Typography>
                    <Typography variant="body2" color="#777">
                      This view displays list of Cost Centers
                    </Typography>
                  </Grid>
                ) : (
                  ""
                )}
              </Grid>
              <Grid item md={1} sx={{ display: "flex" }}>
                <Tooltip title="Uploaded documents" arrow>
                  <IconButton onClick={handleOpenDialog}>
                    <AttachFileOutlinedIcon />
                  </IconButton>
                </Tooltip>
              </Grid>
              <Dialog
                hideBackdrop={false}
                elevation={2}
                PaperProps={{
                  sx: { boxShadow: "none" },
                }}
                open={openDialog}
                onClose={handleCloseDialog}
              >
                <DialogTitle
                  sx={{
                    justifyContent: "space-between",
                    alignItems: "center",
                    height: "max-content",
                    padding: ".5rem",
                    paddingLeft: "1rem",
                    backgroundColor: "#EAE9FF40",
                    // borderBottom: "1px solid grey",
                    display: "flex",
                  }}
                >
                  {userData?.role === "MDM Steward" ? (
                    <>
                      <Typography variant="h6">Add Attachment</Typography>
                    </>
                  ) : (
                    ""
                  )}
                </DialogTitle>

                <DialogContent sx={{ padding: ".5rem 1rem" }}>
                  <Card sx={{ padding: "1rem 1rem 0rem 1rem" }}>
                    {userData?.role === "Finance" ? (
                      <Stack>
                        <Box sx={{ minWidth: 400 }}>
                          <ReusableAttachementAndComments
                            title="CostCenter"
                            useMetaData={false}
                            artifactId={ccNumber}
                            artifactName="CostCenter"
                          />
                        </Box>
                      </Stack>
                    ) : (
                      ""
                    )}
                    <Grid
                      container
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                      }}
                    >
                      <Typography variant="h6">
                        <strong>Attachments</strong>
                      </Typography>
                    </Grid>
                    {Boolean(attachments.length) && (
                      <ReusableTable
                        width="100%"
                        rows={attachments}
                        columns={attachmentColumns}
                        hideFooter={false}
                        getRowIdValue={"id"}
                        disableSelectionOnClick={true}
                        stopPropagation_Column={"action"}
                        artifactId={ccNumber}
                      />
                    )}
                    {!Boolean(attachments.length) && (
                      <Typography variant="body2">
                        No Attachments Found
                      </Typography>
                    )}
                    <br />
                    <Typography variant="h6">Comments</Typography>
                    {Boolean(comments.length) && (
                      <Timeline
                        sx={{
                          [`& .${timelineItemClasses.root}:before`]: {
                            flex: 0,
                            padding: 0,
                          },
                        }}
                      >
                        {comments.map((comment) => (
                          <TimelineItem>
                            <TimelineSeparator>
                              <TimelineDot>
                                <CheckCircleOutlineOutlined
                                  sx={{ color: "#757575" }}
                                />
                              </TimelineDot>
                              <TimelineConnector />
                            </TimelineSeparator>
                            <TimelineContent sx={{ py: "12px", px: 2 }}>
                              <Card
                                elevation={0}
                                sx={{
                                  border: 1,
                                  borderColor: "#C4C4C4",
                                  borderRadius: "8px",
                                  width: "650px",
                                }}
                              >
                                <Box sx={{ padding: "1rem" }}>
                                  <Stack spacing={1}>
                                    <Grid
                                      sx={{
                                        display: "flex",
                                        justifyContent: "space-between",
                                      }}
                                    >
                                      <Typography
                                        sx={{
                                          textAlign: "right",
                                          color: " #757575",
                                          fontWeight: "500",
                                          fontSize: "12px",
                                        }}
                                      >
                                        {moment(comment.createdAt).format(
                                          "DD MMM YYYY"
                                        )}
                                      </Typography>
                                    </Grid>

                                    <Typography
                                      sx={{
                                        fontSize: "12px",

                                        color: " #757575",
                                        fontWeight: "500",
                                      }}
                                    >
                                      {comment.user}
                                    </Typography>
                                    <Typography
                                      sx={{
                                        fontSize: "12px",
                                        color: "#1D1D1D",
                                        fontWeight: "600",
                                      }}
                                    >
                                      {comment.comment}
                                    </Typography>
                                  </Stack>
                                </Box>
                              </Card>
                            </TimelineContent>
                          </TimelineItem>
                        ))}
                      </Timeline>
                    )}
                    {!Boolean(comments.length) && (
                      <Typography variant="body2">No Comments Found</Typography>
                    )}
                    <br />
                  </Card>
                </DialogContent>
                <DialogActions>
                  <Button onClick={handleCloseDialog}>Close</Button>
                </DialogActions>
              </Dialog>
            </Grid>
          </Grid>

          <Grid item sx={{ position: "relative" }}>
            {/* <Stack> */}
            {/* <Box sx={{ width: "100%" }}>
                <Stepper
                  nonLinear
                  activeStep={activeStep}
                  sx={{
                    background: "#FFFFFF",
                    borderBottom: "1px solid #BDBDBD",
                    width: "100%",
                    height: "48px",
                  }}
                >
                  {steps.map((label, index) => (
                    <Step key={label} completed={completed[index]}>
                      <StepButton color="inherit" onClick={handleStep(index)} sx={{ fontWeight: "700" }}>
                        {label}
                      </StepButton>
                    </Step>
                  ))}
                </Stepper>

                <Grid container>{getStepContent(activeStep)}</Grid>
              </Box> */}
            {/* </Stack> */}
            <ReusableTable
              // isLoading={isLoading}
              width="100%"
              // title={
              //   "Mass Cost Center List (" + initialRows?.length + ")"
              // }
              rows={initialRows}
              columns={columns}
              pageSize={10}
              getRowIdValue={"id"}
              hideFooter={false}
              disableSelectionOnClick={true}
              status_onRowSingleClick={true}
              checkboxSelection={
                (userData?.role === "Finance" &&
                  task.processDesc === "Mass Create") ||
                (userData?.role === "Finance" &&
                  massCostCenterRowData?.requestType === "Mass Create") ||
                (userData?.role === "Finance" &&
                  task.processDesc === "Mass Change") ||
                (userData?.role === "Finance" &&
                  massCostCenterRowData?.requestType === "Mass Change")
              }
              onRowsSelectionHandler={handleSelectionModelChange}
              callback_onRowSingleClick={(params) => {
                const costCenter = params.row.costCenter;
                const dataToSend = MultipleCostCenter.find(
                  (item) => item.costCenter === costCenter
                );
                console.log(dataToSend, "yo");
                navigate(
                  `/masterDataCockpit/costCenter/massCostCenterTableRequestBench/displayMultipleCostCenterRequestBench/${costCenter}`,
                  {
                    state: {
                      rowData: params.row,
                      requestNumber: massCostCenterRowData?.requestId.slice(3),
                      tabsData: dataToSend,
                      requestbenchRowData: massCostCenterRowData,
                    },
                  }
                );
              }}
              stopPropagation_Column={"action"}
              status_onRowDoubleClick={true}
            />
          </Grid>
        </div>

        {checkIwaAccess(iwaAccessData, "Cost Center", "ChangeCC") ? (
          <Paper
            sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
            elevation={2}
          >
            <BottomNavigation
              className="container_BottomNav"
              showLabels
              sx={{
                display: "flex",
                justifyContent: "flex-end",
              }}
              value={value}
            >
              {(userData?.role === "MDM Steward" &&
                task.processDesc === "Mass Create") ||
              (userData?.role === "MDM Steward" &&
                massCostCenterRowData?.requestType === "Mass Create") ? (
                <>
                  <Button
                    variant="outlined"
                    size="small"
                    sx={{ button_Outlined, mr: 1 }}
                    onClick={handleOpenCorrectionDialog}
                  >
                    Correction
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary }}
                    onClick={handleOpenRemarkDialog}
                  >
                    Submit For Approval
                  </Button>
                </>
              ) : (userData?.role === "Approver" &&
                  task.processDesc === "Mass Create") ||
                (userData?.role === "Approver" &&
                  massCostCenterRowData?.requestType === "Mass Create") ? (
                <>
                  <Button
                    variant="outlined"
                    size="small"
                    sx={{ button_Outlined, mr: 1 }}
                    onClick={handleOpenCorrectionDialog}
                  >
                    Correction
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={onValidateCostCenterApprove}
                  >
                    Validate
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary }}
                    onClick={handleOpenRemarkDialog}
                    disabled={approveDisabled}
                  >
                    Approve
                  </Button>
                </>
              ) : (userData?.role === "Finance" &&
                  task.processDesc === "Mass Create") ||
                (userData?.role === "Finance" &&
                  massCostCenterRowData?.requestType === "Mass Create") ? (
                <>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={onValidateCostCenter}
                    disabled={!testRun}
                  >
                    Validate
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary }}
                    onClick={handleOpenRemarkDialog}
                    disabled={submitForReviewDisabled}
                  >
                    Submit For Review
                  </Button>
                </>
              ) : (
                ""
              )}

              {(userData?.role === "MDM Steward" &&
                task.processDesc === "Mass Change") ||
              (userData?.role === "MDM Steward" &&
                massCostCenterRowData?.requestType === "Mass Change") ? (
                <>
                  <Button
                    variant="outlined"
                    size="small"
                    sx={{ button_Outlined, mr: 1 }}
                    onClick={handleOpenCorrectionDialog}
                  >
                    Correction
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary }}
                    onClick={handleOpenRemarkDialog}
                  >
                    Submit For Approval
                  </Button>
                </>
              ) : (userData?.role === "Approver" &&
                  task.processDesc === "Mass Change") ||
                (userData?.role === "Approver" &&
                  massCostCenterRowData?.requestType === "Mass Change") ? (
                <>
                  <Button
                    variant="outlined"
                    size="small"
                    sx={{ button_Outlined, mr: 1 }}
                    onClick={handleOpenCorrectionDialog}
                  >
                    Correction
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={onValidateCostCenterApprove}
                  >
                    Validate
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary }}
                    onClick={handleOpenRemarkDialog}
                    disabled={approveDisabled}
                  >
                    Approve
                  </Button>
                </>
              ) : (userData?.role === "Finance" &&
                  task.processDesc === "Mass Change") ||
                (userData?.role === "Finance" &&
                  massCostCenterRowData?.requestType === "Mass Change") ? (
                <>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary, mr: 1 }}
                    onClick={onValidateCostCenter}
                    disabled={!testRun}
                  >
                    Validate
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{ ...button_Primary }}
                    onClick={handleOpenRemarkDialog}
                    disabled={submitForReviewDisabled}
                  >
                    Submit For Review
                  </Button>
                </>
              ) : (
                ""
              )}
            </BottomNavigation>
          </Paper>
        ) : (
          ""
        )}
      </div>
      {/* )} */}
      <Backdrop
        sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
        open={blurLoading}
        // onClick={handleClose}
      >
        <CircularProgress color="inherit" />
      </Backdrop>
    </>
  );
};

export default MassCostCenterTableRequestBench;
