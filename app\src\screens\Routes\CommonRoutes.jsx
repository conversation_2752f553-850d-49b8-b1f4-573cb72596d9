import React from "react";
import { Route } from "react-router-dom";
const Dashboard = React.lazy(() => import("../../components/Dashboard/Dashboard"));
const Home = React.lazy(() => import("../../components/BroadcastManagement/Home"));
// const Playground = React.lazy(() => import("../../components/Playground"));
const Document = React.lazy(() => import("../../components/DocumentManagement/Document"));

export const CommonRoutes = [
  <Route path="/dashboard" element={<Dashboard />} />, 
  <Route path="/" element={<Home />} />, 
  // <Route path="/playground" element={<Playground />} />,
  <Route path="/documentManagement" element={<Document />} />
];
