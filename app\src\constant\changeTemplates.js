import { MATERIAL_VIEWS } from "./enum";

export const TEMPLATE_KEYS = {
    LOGISTIC: "Logistic Data",
    MRP: "MRP Data",
    WARE_VIEW_2: "Warehouse View 2",
    ITEM_CAT: "Item Cat Group",
    SET_DNU: "Set To DNU",
    UPD_DESC: "Update Descriptions",
    CHG_STAT: "Change Status",
    CREATE_PCG: "CREATE_PCG",
    CHANGE_PCG: "CHANGE_PCG",
};

export const DIRECT_SYN_TEMP = [
  TEMPLATE_KEYS?.MRP,
  TEMPLATE_KEYS?.WARE_VIEW_2,
  TEMPLATE_KEYS?.ITEM_CAT
]

export const TEMPLATE_NAMES_BOM = [
    { code: TEMPLATE_KEYS?.LOGISTIC, desc: "" },
    { code: TEMPLATE_KEYS?.MRP, desc: "" },
    { code: TEMPLATE_KEYS?.WARE_VIEW_2, desc: "" },
    { code: TEMPLATE_KEYS?.ITEM_CAT, desc: "" },
    { code: TEMPLATE_KEYS?.SET_DNU, desc: "" },
    { code: TEMPLATE_KEYS?.UPD_DESC, desc: "" },
    { code: TEMPLATE_KEYS?.CHG_STAT, desc: "" },]

export const MANDATORY_FILTERS = {
  [TEMPLATE_KEYS?.LOGISTIC]: ["Division", 'Material Number'],
  [TEMPLATE_KEYS?.MRP]: ["Plant", "Division", 'Material Number'],
  [TEMPLATE_KEYS?.WARE_VIEW_2]: ["Warehouse", "Division", 'Material Number', "Plant"],
  [TEMPLATE_KEYS?.ITEM_CAT]: ["Sales Org", "Division", 'Material Number'],
  [TEMPLATE_KEYS?.SET_DNU]: ["Plant", "Sales Org", "Division", 'Material Number'],
  [TEMPLATE_KEYS?.UPD_DESC]: ["Division", 'Material Number'],
  [TEMPLATE_KEYS?.CHG_STAT]: ["Sales Org", "Division", 'Material Number'],
  [TEMPLATE_KEYS?.CREATE_PCG] : ["Profit Center Group", "Controlling Area", "Profit Center Group Description"],
   [TEMPLATE_KEYS?.CHANGE_PCG] : ["Profit Center Group", "Controlling Area"]
}

export const Templates = {
  [TEMPLATE_KEYS?.LOGISTIC]: [
    {   
      key: 'Division',
      options: [],
    },
    {
      key: 'Plant',
      options: [],
    },
    {
      key: 'Material Number',
      options: [],
    },
  ],

  [TEMPLATE_KEYS?.MRP]: [
    {   
      key: 'Division',
      options: [],
    },
    {
      key: 'Plant',
      options: [],
    },
    {
      key: 'MRP Controller',
      options: [],
    },
    {
      key: 'Material Number',
      options: [],
    },
  ],

  [TEMPLATE_KEYS?.WARE_VIEW_2]: [
    {   
      key: 'Division',
      options: [],
    },
    {
      key: 'Plant',
      options: [],
    },
    {
      key: 'Warehouse',
      options: [],
    },
    {
      key: 'Material Number',
      options: [],
    },
  ],

  [TEMPLATE_KEYS?.ITEM_CAT]: [
    {   
      key: 'Division',
      options: [],
    },
    {
      key: 'Sales Org',
      options: [],
    },
    {
      key: 'Distribution Channel',
      options: [],
    },
    {
      key: 'Material Number',
      options: [],
    },
  ],

  [TEMPLATE_KEYS?.SET_DNU]: [
    {   
      key: 'Division',
      options: [],
    },
    {
      key: 'Sales Org',
      options: [],
    },
    {
      key: 'Distribution Channel',
      options: [],
    },
    {
      key: 'Plant',
      options: [],
    },
    {
      key: 'Material Number',
      options: [],
    },
  ],

  [TEMPLATE_KEYS?.UPD_DESC]: [
    {   
      key: 'Division',
      options: [],
    },
    {
      key: 'Plant',
      options: [],
    },
    {
      key: 'Material Number',
      options: [],
    },
  ],

  [TEMPLATE_KEYS?.CHG_STAT]: [
    {   
      key: 'Division',
      options: [],
    },
    {
      key: 'Sales Org',
      options: [],
    },
    {
      key: 'Distribution Channel',
      options: [],
    },
    {
      key: 'Material Number',
      options: [],
    },
  ],
};

export const Hierarchy_Templates = {
  [TEMPLATE_KEYS?.CREATE_PCG]: [
    {   
      key: 'Controlling Area',
      options: [],
    },
    {
      key: 'Profit Center Group',
      type: 'Input',
      options: [],
      length:10,
      characterAllowed:/[^a-zA-Z0-9_\/-]/g
    },
    {
      key: 'Profit Center Group Description',
      options: [],
      length:40,
      characterAllowed:/[^a-zA-Z0-9_\/\- ]/g
    },
    
  ],

  [TEMPLATE_KEYS?.CHANGE_PCG]: [
    {   
      key: 'Controlling Area',
      options: [],
    },
    {
      key: 'Profit Center Group',
      type: 'Dropdown',
      options: [],
      length:10,
      characterAllowed:/[^a-zA-Z0-9_\/-]/g
    },
  ],
}

export const CHANGE_TEMPLATES_FIELD_IDENTIFICATION = {
  "SalesData":["SalesOrg","DistrChan"],
  "AdditionalData":["AltUnit"],
  "DescriptionData":["Langu"],
  "BasicData":[],
  "PlantData":["Plant"],
  "WarehouseData":["WhseNo"]
}

export const TEMPLATE_NAME_MANIPULATION = {
  [TEMPLATE_KEYS.LOGISTIC] : "AdditionalData",
  [TEMPLATE_KEYS.ITEM_CAT]: "SalesData",
  [TEMPLATE_KEYS.UPD_DESC]: "DescriptionData",
  [TEMPLATE_KEYS.WARE_VIEW_2]: "WarehouseData",
  [TEMPLATE_KEYS.MRP]: {
    "Basic Data": "BasicData",
    "Plant Data": "PlantData"
  },
  [TEMPLATE_KEYS.CHG_STAT]: {
    "Basic Data": "BasicData",
    "Plant Data": "PlantData",
    "Sales Data": "SalesData"
  },
  [TEMPLATE_KEYS.SET_DNU]: {
    "Description": "DescriptionData",
    "Basic Data": "BasicData",
    "Plant Data": "PlantData",
    "Sales Data": "SalesData"
  }
};

export const CREATE_CHANGE_LOG_MANDATORY_FIELDS = {
  [MATERIAL_VIEWS.BASIC_DATA]:0,
  [MATERIAL_VIEWS.SALES]:2,
  [MATERIAL_VIEWS.PURCHASING]:1,
  [MATERIAL_VIEWS.MRP]:1,
  [MATERIAL_VIEWS.ACCOUNTING]:1,
  [MATERIAL_VIEWS.COSTING]:1,
  [MATERIAL_VIEWS.DESCRIPTION]:1,
  [MATERIAL_VIEWS.ADDITIONAL_DATA]:1,
  [MATERIAL_VIEWS.WAREHOUSE]:1,
  [MATERIAL_VIEWS.WORKSCHEDULING]:1,
  [MATERIAL_VIEWS.TAX_DATA]:2,
  [MATERIAL_VIEWS.SALES_GENERAL]:0,
  [MATERIAL_VIEWS.PURCHASING_GENERAL]:0,
  [MATERIAL_VIEWS.SALES_PLANT]:1,
  [MATERIAL_VIEWS.ADDITIONAL_EAN_DATA]:1,
  [MATERIAL_VIEWS.FINANCE_COST_DATA]:1,
}

export const CREATE_VIEWS_MANIPULATION = {
    [MATERIAL_VIEWS.BASIC_DATA]:"BasicData",
    [MATERIAL_VIEWS.SALES]:"SalesData",
    [MATERIAL_VIEWS.PURCHASING]:"PurchasingData",
    [MATERIAL_VIEWS.MRP]:"MRPData",
    [MATERIAL_VIEWS.ACCOUNTING]:"AccountingData",
    [MATERIAL_VIEWS.COSTING]:"CostingData",
    [MATERIAL_VIEWS.WAREHOUSE]:"WarehouseData",
    [MATERIAL_VIEWS.ADDITIONAL_DATA]:"AdditionalData",
    [MATERIAL_VIEWS.ADDITIONAL_EAN_DATA]:"AdditionalEANData",
    [MATERIAL_VIEWS.DESCRIPTION]:"DescriptionData",
    [MATERIAL_VIEWS.WORKSCHEDULING]:"WorkSchedulingData",
    [MATERIAL_VIEWS.TAX_DATA]:"TaxClassificationData",
    [MATERIAL_VIEWS.SALES_GENERAL]:"SalesGeneralData",
    [MATERIAL_VIEWS.PURCHASING_GENERAL]:"PurchasingGeneralData",
    [MATERIAL_VIEWS.SALES_PLANT]:"SalesPlantData",
    [MATERIAL_VIEWS.FINANCE_COST_DATA]:"FinanceCostData",
}

export const CREATE_CHANGE_TEMPLATES_FIELD_IDENTIFICATION = {
  "SalesData":["SalesOrg","DistrChan"],
  "AdditionalData":["AltUnit"],
  "AdditionalEANData":["AltUnit"],
  "DescriptionData":["Langu"],
  "BasicData":[],
  "WarehouseData":["WhseNo"],
  "MRPData":["Plant"],
  "AccountingData":["Plant"],
  "CostingData":["Plant"],
  "PurchasingData":["Plant"],
  "WorkSchedulingData":["Plant"],
  "TaxClassificationData":["Country","TaxType"],
  "SalesGeneralData":[],
  "PurchasingGeneralData":[],
  "SalesPlantData":["Plant"],
  "FinanceCostData":["Plant"]
}

