import{m as K,n as X,o as J,r as f,j as t,a as e,G as l,wU as c,vP as ne,wb as V,wX as oe,bs as C,br as A,B as o,ge as ie,T as r,E as Q,wG as F,I as Z,wY as le,x as T,aD as E,bF as B,D as ce,gh as ae,b7 as se,wc as de,wZ as he,S as i,w_ as ue,e as xe,pS as ge,b as be,u as fe,K as H,b1 as G,t as pe,Z as q,$ as Y,Y as me}from"./index-75c1660a.js";import{I as $}from"./InputAdornment-a22e1655.js";import{d as ve}from"./ArrowCircleLeftOutlined-f7b52d40.js";import{d as ye}from"./Download-ecbb28d9.js";import{C as Se}from"./Container-754d6379.js";var z={},we=X;Object.defineProperty(z,"__esModule",{value:!0});var ee=z.default=void 0,Ce=we(K()),Ae=J;ee=z.default=(0,Ce.default)((0,Ae.jsx)("path",{d:"M16.54 11 13 7.46l1.41-1.41 2.12 2.12 4.24-4.24 1.41 1.41zM11 7H2v2h9zm10 6.41L19.59 12 17 14.59 14.41 12 13 13.41 15.59 16 13 18.59 14.41 20 17 17.41 19.59 20 21 18.59 18.41 16zM11 15H2v2h9z"}),"Rule");var P={},Ee=X;Object.defineProperty(P,"__esModule",{value:!0});var te=P.default=void 0,Ie=Ee(K()),_e=J;te=P.default=(0,Ie.default)((0,_e.jsx)("path",{d:"M3 5v14h18V5zm16 6h-3.33V7H19zm-5.33 0h-3.33V7h3.33zM8.33 7v4H5V7zM5 17v-4h3.33v4zm5.33 0v-4h3.33v4zm5.34 0v-4H19v4z"}),"ViewModuleOutlined");const Le=({apiData:h})=>{var m,_,L;const[M,U]=f.useState(!0),[g,D]=f.useState(""),[N,j]=f.useState(!1),p=Object.keys(h).map((n,s)=>({id:s+1,materialNumber:n,score:Math.round(h[n][0].MAT_MATERIAL_FIELD_CONFIG.totalScore),configCount:h[n].length})),[x,O]=f.useState(((m=p[0])==null?void 0:m.id)||null),v=f.useMemo(()=>{if(!g.trim())return p;const n=g.toLowerCase();return p.filter(s=>{const u=s.materialNumber.toLowerCase().includes(n),a=s.score.toString().includes(n);return u||a})},[p,g]);f.useEffect(()=>{g&&v.length>0&&!x&&O(v[0].id)},[g,v,x]);const y=n=>n>=80?"success":n>=50?"warning":"error",d=()=>{D("")},I=p.find(n=>n.id===x),S=I?h[I.materialNumber]:[],k=n=>{if(!n||n.length===0)return 0;const s=n.reduce((u,a)=>{const R=Object.keys(a)[0];return u+a[R].totalScore},0);return Math.round(s/n.length)},W=n=>{const s=n.filter(a=>a.value.pass===!0).length,u=n.filter(a=>a.value.pass===!1).length;return{passCount:s,failCount:u}};return t(o,{sx:{display:"flex",flexDirection:"column",height:"100vh",bgcolor:"#f5f5f5"},children:[e(o,{sx:{p:3,bgcolor:"white",borderBottom:1,borderColor:"divider"},children:e(l,{container:!0,spacing:3,justifyContent:"center",children:[{label:c.MODULE,value:"Material",color:"#FF6B6B",icon:e(te,{})},{label:c.NO_OBJECTS,value:Object.keys(h).length,color:"#FF9800",icon:e(ne,{})},{label:c.BUSINESS_RULES,value:(L=h==null?void 0:h[(_=Object.keys(h))==null?void 0:_[0]])==null?void 0:L.length,color:"#9C27B0",icon:e(ee,{})},{label:c.AGGREGATE_SCORE,value:`${Math.round(p.reduce((n,s)=>n+s.score,0)/p.length)}%`,color:"#2196F3",icon:e(V,{})},{label:c.CREATEDBY,value:"<EMAIL>",color:"#4CAF50",icon:e(oe,{})}].map((n,s)=>e(l,{item:!0,xs:12,sm:6,md:2.4,children:e(C,{sx:{background:`linear-gradient(135deg, ${n.color}15 0%, ${n.color}08 100%)`,border:`1px solid ${n.color}30`,transition:"all 0.3s ease","&:hover":{boxShadow:`0 8px 25px ${n.color}20`},borderRadius:"10px"},children:t(A,{sx:{textAlign:"center",py:2},children:[e(o,{sx:{display:"flex",justifyContent:"center",mb:1},children:e(ie,{sx:{bgcolor:n.color,width:40,height:40},children:n.icon})}),e(r,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:n.label}),e(r,{variant:"h5",fontWeight:"bold",color:n.color,children:n.value})]})})},s))})}),t(o,{sx:{display:"flex",flex:1,overflow:"hidden",zIndex:"1"},children:[e(ce,{variant:"persistent",open:M,sx:{width:320,flexShrink:0,"& .MuiDrawer-paper":{width:320,boxSizing:"border-box",position:"relative",height:"auto",bgcolor:"white",borderRight:1,borderColor:"divider"}},children:t(o,{sx:{height:"100%",display:"flex",flexDirection:"column"},children:[e(o,{sx:{p:2,borderBottom:1,borderColor:"divider"},children:e(Q,{fullWidth:!0,variant:"outlined",placeholder:c.SEARCH,value:g,onChange:n=>D(n.target.value),onFocus:()=>j(!0),onBlur:()=>j(!1),size:"small",InputProps:{startAdornment:e($,{position:"start",children:e(F,{color:"action"})}),endAdornment:g&&e($,{position:"end",children:e(Z,{onClick:d,size:"small",children:e(le,{})})})},sx:{"& .MuiOutlinedInput-root":{bgcolor:"grey.50","&:hover":{bgcolor:"grey.100"},"&.Mui-focused":{bgcolor:"white"}}}})}),g&&e(o,{sx:{px:2,py:1,bgcolor:"grey.50",borderBottom:1,borderColor:"divider"},children:t(r,{variant:"caption",color:"text.secondary",children:[v.length," of ",p.length," materials"]})}),e(o,{sx:{flex:1,overflow:"auto",p:1,maxHeight:"500px"},children:t(T,{spacing:1,children:[v.map(n=>t(E,{elevation:x===n.id?8:1,sx:{p:2,cursor:"pointer",border:x===n.id?2:1,borderColor:x===n.id?"primary.main":"divider",bgcolor:x===n.id?"primary.50":"white",transition:"all 0.2s ease","&:hover":{bgcolor:x===n.id?"primary.100":"grey.50"},borderRadius:"10px"},onClick:()=>O(n.id),children:[t(o,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[e(r,{variant:"caption",color:"text.secondary",children:c.MATERIAL}),e(r,{variant:"caption",color:"text.secondary",paddingRight:"5px",children:c.SCORE})]}),t(o,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[e(r,{variant:"subtitle2",fontWeight:x===n.id?600:500,color:x===n.id?"primary.main":"text.primary",children:n.materialNumber}),e(B,{label:`${n.score}%`,size:"small",color:y(n.score),variant:"outlined"})]})]},n.id)),v.length===0&&t(o,{sx:{textAlign:"center",py:6},children:[e(F,{sx:{fontSize:48,color:"grey.300",mb:2}}),e(r,{variant:"body2",color:"text.secondary",children:c.NO_MATERIAL_FOUND}),e(r,{variant:"caption",color:"text.secondary",children:c.ADJUST})]})]})})]})}),e(o,{sx:{flex:1,overflow:"auto",p:3},children:I&&S.length>0?t(T,{spacing:3,children:[e(E,{elevation:2,sx:{p:3,background:"linear-gradient(135deg,rgb(137, 155, 233) 0%,rgb(171, 114, 228) 100%)",color:"white",borderRadius:"10px"},children:t(l,{container:!0,alignItems:"center",justifyContent:"space-between",children:[t(l,{item:!0,children:[e(r,{variant:"h3",fontWeight:"bold",gutterBottom:!0,color:"white",children:I.materialNumber}),t(r,{variant:"body2",sx:{opacity:.9},children:[c.ANALYSIS_DETAILS," - ",S.length," Business Rule",S.length!==1?"s":""]})]}),e(l,{item:!0,children:t(o,{sx:{textAlign:"right"},children:[t(r,{variant:"h4",fontWeight:"bold",color:"white",children:[k(S),"%"]}),e(r,{variant:"body2",sx:{opacity:.9},children:c.OVERALL_SCORE})]})})]})}),e(l,{container:!0,spacing:3,sx:{marginLeft:"200px"},children:S.map((n,s)=>{const u=Object.keys(n)[0],a=n[u],{passCount:R,failCount:w}=W(a.fieldDetails);return e(l,{item:!0,xs:12,lg:6,children:e(C,{elevation:3,sx:{ml:-3,mr:3,height:"100%",borderRadius:"10px",transition:"all 0.3s ease","&:hover":{boxShadow:6}},children:t(A,{sx:{p:3},children:[t(o,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[t(r,{variant:"h6",fontWeight:"bold",color:"primary",children:[t(o,{component:"span",sx:{fontWeight:"bold"},children:[c.BUSINESS_RULE,": "]}),e(o,{component:"span",sx:{fontWeight:"medium"},children:u})]}),e(B,{label:`${Math.round(a.totalScore)}%`,color:y(a.totalScore),sx:{fontWeight:"bold"}})]}),e(o,{sx:{mb:2},children:e(ae,{variant:"determinate",value:a.totalScore,color:y(a.totalScore),sx:{height:8,borderRadius:4}})}),t(l,{container:!0,spacing:2,sx:{mb:2},children:[e(l,{item:!0,xs:4,children:t(o,{sx:{textAlign:"center"},children:[e(r,{variant:"h6",fontWeight:"bold",children:a.fieldDetails.length}),e(r,{variant:"caption",color:"text.secondary",children:c.FIELDS})]})}),e(l,{item:!0,xs:4,children:t(o,{sx:{textAlign:"center"},children:[e(r,{variant:"h6",fontWeight:"bold",color:"success.main",children:R}),e(r,{variant:"caption",color:"text.secondary",children:c.PASS})]})}),e(l,{item:!0,xs:4,children:t(o,{sx:{textAlign:"center"},children:[e(r,{variant:"h6",fontWeight:"bold",color:"error.main",children:w}),e(r,{variant:"caption",color:"text.secondary",children:c.FAIL})]})})]}),e(se,{sx:{my:2}}),e(T,{spacing:1.5,children:a.fieldDetails.map((b,re)=>e(E,{variant:"outlined",sx:{p:2,bgcolor:b.value.pass?"success.50":"error.50",borderColor:b.value.pass?"success.200":"error.200"},children:t(l,{container:!0,alignItems:"center",spacing:2,paddingLeft:"-500px",children:[e(l,{item:!0,children:b.value.pass?e(V,{color:"success"}):e(de,{color:"error"})}),t(l,{item:!0,xs:!0,children:[t(r,{variant:"h5",children:[e(o,{component:"span",sx:{fontWeight:"bold"},children:"Field Name: "}),e(o,{component:"span",sx:{fontWeight:"normal"},children:b.fieldName})]}),t(r,{variant:"h6",color:"text.secondary",children:[c.SCORE,": ",b.value.givenScore,"/",b.value.allotedScore]})]}),e(l,{item:!0,children:t(o,{sx:{textAlign:"right"},children:[t(r,{variant:"h6",color:"text.secondary",children:[c.PRESENT_VALUE,": ",b.value.dataPresentValue||"N/A"]}),e("br",{}),t(r,{variant:"h6",color:"text.secondary",children:[c.EXPECTED_VALUE,": ",b.value.dataDefaultValue]})]})})]})},re))})]})})},s)})})]}):t(E,{elevation:2,sx:{p:6,textAlign:"center",bgcolor:"grey.50",minHeight:400,display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center"},children:[e(he,{sx:{fontSize:64,color:"grey.300",mb:2}}),e(r,{variant:"h5",color:"text.secondary",gutterBottom:!0,children:c.NO_MATERIAL}),e(r,{variant:"body2",color:"text.secondary",children:c.VALIDATIONS_DETAILS})]})})]})]})},Re=()=>t(Se,{maxWidth:"xl",sx:{py:3,bgcolor:"#f5f5f5",minHeight:"100vh"},children:[t(l,{container:!0,spacing:3,sx:{mb:4},children:[e(l,{item:!0,xs:12,sm:6,md:2.4,children:e(C,{sx:{textAlign:"center",p:2,bgcolor:"#fff5f5"},children:t(A,{children:[e(o,{sx:{display:"flex",justifyContent:"center",mb:2},children:e(i,{variant:"circular",width:48,height:48})}),e(r,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:e(i,{width:80})}),e(r,{variant:"h6",color:"#dc3545",fontWeight:"bold",children:e(i,{width:60})})]})})}),e(l,{item:!0,xs:12,sm:6,md:2.4,children:e(C,{sx:{textAlign:"center",p:2,bgcolor:"#fff8f0"},children:t(A,{children:[e(o,{sx:{display:"flex",justifyContent:"center",mb:2},children:e(i,{variant:"circular",width:48,height:48})}),e(r,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:e(i,{width:100})}),e(r,{variant:"h6",color:"#fd7e14",fontWeight:"bold",children:e(i,{width:40})})]})})}),e(l,{item:!0,xs:12,sm:6,md:2.4,children:e(C,{sx:{textAlign:"center",p:2,bgcolor:"#f8f0ff"},children:t(A,{children:[e(o,{sx:{display:"flex",justifyContent:"center",mb:2},children:e(i,{variant:"circular",width:48,height:48})}),e(r,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:e(i,{width:120})}),e(r,{variant:"h6",color:"#6f42c1",fontWeight:"bold",children:e(i,{width:20})})]})})}),e(l,{item:!0,xs:12,sm:6,md:2.4,children:e(C,{sx:{textAlign:"center",p:2,bgcolor:"#f0f8ff"},children:t(A,{children:[e(o,{sx:{display:"flex",justifyContent:"center",mb:2},children:e(i,{variant:"circular",width:48,height:48})}),e(r,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:e(i,{width:90})}),e(r,{variant:"h6",color:"#007bff",fontWeight:"bold",children:e(i,{width:30})})]})})}),e(l,{item:!0,xs:12,sm:6,md:2.4,children:e(C,{sx:{textAlign:"center",p:2,bgcolor:"#f0fff4"},children:t(A,{children:[e(o,{sx:{display:"flex",justifyContent:"center",mb:2},children:e(i,{variant:"circular",width:48,height:48})}),e(r,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:e(i,{width:70})}),e(r,{variant:"h6",color:"#28a745",fontWeight:"bold",children:e(i,{width:180})})]})})})]}),t(l,{container:!0,spacing:3,children:[t(l,{item:!0,xs:12,md:4,children:[e(o,{sx:{mb:2},children:e(Q,{fullWidth:!0,placeholder:"Search materials...",InputProps:{startAdornment:e($,{position:"start",children:e(F,{color:"action"})})},disabled:!0})}),e(E,{sx:{p:2,maxHeight:400,overflow:"auto"},children:[1,2,3,4].map(h=>t(o,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",p:2,mb:1,border:"1px solid #e0e0e0",borderRadius:1,bgcolor:h===1?"#e3f2fd":"white"},children:[t(o,{children:[e(r,{variant:"body2",color:"text.secondary",children:e(i,{width:50})}),e(r,{variant:"h6",children:e(i,{width:60})})]}),t(o,{children:[e(r,{variant:"body2",color:"text.secondary",children:e(i,{width:40})}),e(B,{label:e(i,{width:20}),size:"small",sx:{bgcolor:"#ffebee",color:"#d32f2f"}})]})]},h))})]}),t(l,{item:!0,xs:12,md:8,children:[e(E,{sx:{p:3,mb:3,bgcolor:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)"},children:t(o,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[t(o,{children:[e(r,{variant:"h4",sx:{color:"white",fontWeight:"bold"},children:e(i,{width:60,sx:{bgcolor:"rgba(255,255,255,0.3)"}})}),e(r,{variant:"body1",sx:{color:"white",opacity:.9},children:e(i,{width:200,sx:{bgcolor:"rgba(255,255,255,0.3)"}})})]}),t(o,{sx:{textAlign:"right"},children:[e(r,{variant:"h4",sx:{color:"white",fontWeight:"bold"},children:e(i,{width:40,sx:{bgcolor:"rgba(255,255,255,0.3)"}})}),e(r,{variant:"body1",sx:{color:"white",opacity:.9},children:e(i,{width:80,sx:{bgcolor:"rgba(255,255,255,0.3)"}})})]})]})}),t(E,{sx:{p:3},children:[t(o,{sx:{display:"flex",alignItems:"center",gap:2,mb:3},children:[e(r,{variant:"h6",sx:{color:"#1976d2"},children:e(i,{width:100})}),e(i,{width:200}),e(B,{label:e(i,{width:20}),size:"small",sx:{bgcolor:"#ffebee",color:"#d32f2f"}})]}),e(o,{sx:{mb:3},children:e(i,{variant:"rectangular",width:"100%",height:8,sx:{borderRadius:1}})}),t(l,{container:!0,spacing:3,sx:{mb:3},children:[e(l,{item:!0,xs:4,children:t(o,{sx:{textAlign:"center"},children:[e(r,{variant:"h5",fontWeight:"bold",children:e(i,{width:20})}),e(r,{variant:"body2",color:"text.secondary",children:e(i,{width:40})})]})}),e(l,{item:!0,xs:4,children:t(o,{sx:{textAlign:"center"},children:[e(r,{variant:"h5",fontWeight:"bold",sx:{color:"#4caf50"},children:e(i,{width:20})}),e(r,{variant:"body2",color:"text.secondary",children:e(i,{width:30})})]})}),e(l,{item:!0,xs:4,children:t(o,{sx:{textAlign:"center"},children:[e(r,{variant:"h5",fontWeight:"bold",sx:{color:"#f44336"},children:e(i,{width:20})}),e(r,{variant:"body2",color:"text.secondary",children:e(i,{width:25})})]})})]}),e(o,{sx:{border:"1px solid #e0e0e0",borderRadius:1,p:2},children:t(o,{sx:{display:"flex",alignItems:"center",gap:2,mb:2},children:[e(ue,{sx:{color:"#f44336"}}),t(o,{sx:{flex:1},children:[e(r,{variant:"body1",fontWeight:"bold",children:e(i,{width:150})}),e(r,{variant:"body2",color:"text.secondary",children:e(i,{width:80})})]}),t(o,{sx:{textAlign:"right"},children:[e(r,{variant:"body2",color:"text.secondary",children:e(i,{width:100})}),e(r,{variant:"body2",color:"text.secondary",children:e(i,{width:120})})]})]})})]})]})]})]}),Te=()=>{var W;const[h,M]=f.useState(!1),[U,g]=f.useState(!1),[D,N]=f.useState(""),[j,p]=f.useState({}),{t:x}=xe(),{showSnackbar:O}=ge(),v=be(),y=fe(),d=y==null?void 0:y.state;f.useEffect(()=>{d!=null&&d.requestId&&S(d==null?void 0:d.requestId)},[d]);const I={padding:"1rem",minHeight:"100vh"},S=m=>{var s,u;g(!0);let _={requestId:m};const L=a=>{p(a==null?void 0:a.body),g(!1)},n=a=>{g(!1)};H(`/${q}${(u=(s=Y)==null?void 0:s.DATA_CLEANSE_APIS)==null?void 0:u.CLEANSING_REQ_DETAILS}`,"post",L,n,_)},k=m=>{var s,u;M(!0);let _={requestId:m};const L=a=>{var b;const R=URL.createObjectURL(a),w=document.createElement("a");w.href=R,w.setAttribute("download",`${m}_Data Cleanse.pdf`),document.body.appendChild(w),w.click(),document.body.removeChild(w),URL.revokeObjectURL(R),M(!1),N(""),O(`${m}${(b=c)==null?void 0:b.EXPORT_SUCCESS}`,"success")},n=()=>{M(!1),N(""),O(`Failed exporting ${m}_Data Cleanse.pdf`,"error")};H(`/${q}${(u=(s=Y)==null?void 0:s.DATA_CLEANSE_APIS)==null?void 0:u.DOWNLOAD_PDF}`,"postandgetblob",L,n,_)};return t("div",{style:{maxHeight:"50vh"},children:[e(l,{container:!0,sx:{padding:"16px"},children:t(l,{item:!0,md:12,sx:{padding:"16px",display:"flex",mb:-6},children:[t(l,{md:9,sx:{display:"flex"},children:[e(Z,{color:"primary",sx:G,onClick:()=>v(-1),children:e(ve,{sx:{fontSize:"25px",color:"#000000"}})}),t(l,{item:!0,md:12,children:[e(r,{variant:"h3",children:e("strong",{children:`${x(c.CLEANSE_REQUEST)} - ${d==null?void 0:d.requestId}`})}),e(r,{variant:"body2",color:"#777",children:x(c.VIEW)})]})]}),e(l,{md:3,sx:{display:"flex",justifyContent:"flex-end",alignItems:"flex-start"},children:e(pe,{variant:"outlined",color:"primary",startIcon:e(ye,{}),sx:G,onClick:()=>{k((d==null?void 0:d.requestId)||"")},children:c.PDF})})]})}),e(me,{blurLoading:h,loaderMessage:D}),e("div",{style:{...I,backgroundColor:"#FAFCFF"},children:e(T,{spacing:1,children:U||!((W=Object==null?void 0:Object.keys(j))!=null&&W.length)?e(Re,{}):e(Le,{apiData:j})})})]})};export{Te as default};
