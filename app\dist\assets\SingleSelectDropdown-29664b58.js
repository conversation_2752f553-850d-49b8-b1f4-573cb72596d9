import{p as o,a as s,T as b,j as S,E,cc as T,F as y,l as i,b6 as I,d4 as k,d6 as w}from"./index-17b8d91e.js";const C=8,O=o.createContext({}),P=o.forwardRef((l,t)=>{const a=o.useContext(O);return s("div",{ref:t,...l,...a})});function p(l){const{children:t,...a}=l,c=[];t.forEach(n=>{c.push(n)});const m=c.length,d=w.HEIGHT,f=()=>m>8?8*d:c.length*d;return s("div",{ref:l.ref,children:s(O.Provider,{value:a,children:s(k,{itemData:c,height:f()+2*C,width:"100%",outerElementType:P,innerElementType:"ul",itemSize:d,overscanCount:5,itemCount:m,children:({data:n,index:h,style:u})=>{const x=n[h],r={...u,top:u.top+C};return o.cloneElement(x,{style:r})}})})})}const z=({options:l=[],value:t=null,onChange:a,placeholder:c="SELECT OPTION",disabled:m=!1,minWidth:d,isFieldError:f,handleInputChange:n,isLoading:h=!1,isOptionDisabled:u=()=>!1})=>{const x=o.useMemo(()=>t?typeof t=="string"||typeof t=="number"?{code:t.toString(),desc:""}:t:null,[t]);return s(I,{fullWidth:!0,size:"small",options:l,value:x,onChange:(r,e)=>a(e),getOptionDisabled:u||(()=>!1),getOptionLabel:r=>r!=null&&r.desc?`${r.code} - ${r.desc}`:(r==null?void 0:r.code)||"",isOptionEqualToValue:(r,e)=>r.code===e.code,ListboxComponent:p,disableCloseOnSelect:!1,renderOption:(r,e)=>s(b,{...r,component:"li",style:{fontSize:12,padding:"8px 16px",width:"100%",cursor:"pointer",display:"flex",alignItems:"start"},children:S("span",{style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},title:`${e==null?void 0:e.code}${e!=null&&e.desc?` - ${e==null?void 0:e.desc}`:""}`,children:[s("strong",{children:e==null?void 0:e.code}),e!=null&&e.desc?` - ${e==null?void 0:e.desc}`:""]})}),renderInput:r=>{var e,g;return s(E,{...r,placeholder:c==null?void 0:c.toUpperCase(),title:t!=null&&t.code?t==null?void 0:t.code:t,fullWidth:!0,onChange:n||void 0,InputProps:{...r.InputProps,endAdornment:S(y,{children:[h?s(T,{size:20,sx:{mr:1}}):null,r.InputProps.endAdornment]}),sx:{"& .MuiOutlinedInput-notchedOutline":{borderColor:f&&((g=(e=i)==null?void 0:e.error)==null?void 0:g.dark)}}},sx:{minWidth:d}})},disabled:m,sx:{minWidth:d,"& .MuiAutocomplete-popper":{width:`${d}px !important`},"& .MuiAutocomplete-option":{fontSize:"12px",color:i.black.dark,"&:hover":{backgroundColor:i.primary.whiteSmoke}},"& .MuiAutocomplete-listbox":{padding:"0px !important"},"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:i.black.dark,color:i.black.dark}}}})};export{z as S};
