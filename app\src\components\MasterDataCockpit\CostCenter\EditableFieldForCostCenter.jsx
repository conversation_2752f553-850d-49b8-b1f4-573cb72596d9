import React, { useEffect, useState } from "react";
import {
  St<PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  TextField,
  Grid,
  Autocomplete,
  IconButton,
  FormControlLabel,
  Checkbox,
} from "@mui/material";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import SaveIcon from "@mui/icons-material/Save";
import { DateRangePicker } from "rsuite";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";

import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { setPayload } from "../../../app/editPayloadSlice";
import { setCCRequiredFields, setSingleCostCenterPayload } from "../../../app/costCenterTabsSlice";
import { doAjax } from "../../Common/fetchService";
import { destination_CostCenter } from "../../../destinationVariables";
import { setDropDown } from "../../../app/dropDownDataSlice";
import LoadingComponent from "../../Common/LoadingComponent";
function transformApiData(apiValue, dropDownData) {
  if (Array.isArray(dropDownData)) {
    const matchingOption = dropDownData.find(
      (option) => option.code === apiValue
    );
    return matchingOption || "";
  } else {
    return "";
  }
}
const EditableFieldForCostCenter = ({
  label,
  value,
  units,
  data,
  onSave,
  isEditMode,
  visibility,
  length,
  type,
  taskRequestId,
}) => {
  const [editedValue, setEditedValue] = useState(value);
  const [changeStatus, setChangeStatus] = useState(false);
  const [error,setError]=useState(false);
  const dropDownData = useSelector((state) => state.AllDropDown.dropDown);
  const dispatch = useDispatch();
  const transformedValue = transformApiData(editedValue, dropDownData);
  const editField = useSelector((state) => state.edit.payload);
  let taskDetails = useSelector((state) => state.userManagement.taskData);
  const requestId = taskDetails?.subject;

  // const requestId = 2155515515;
  console.log("editField", label, editedValue);
  const fieldData = {
    label,
    value: editedValue,
    units,
    type,
    visibility
  };

  console.log("fieldData", data);
  const handleSave = () => {
    onSave(fieldData);
  };

  let key = label
    .replaceAll("(", "")
    .replaceAll(")", "")
    .replaceAll("/", "")
    .replaceAll("-", "")
    .replaceAll(".", "")
    .split(" ")
    .join("");

  useEffect(() => {
    setEditedValue(value);
    console.log('mobile', label,editedValue)
  }, [value]);

  //console.log("keyname", label);
  useEffect(() => {
    if (
      visibility === "0" ||
      visibility === "Required"
    ) {
      dispatch(setCCRequiredFields(key));
    }
  }, []);

  const onEdit = (newValue) => {
    // console.log('oneditreason', reason)

    //console.log("rakesh", label,newValue);
    dispatch(
      setPayload({
        keyname: key
          .replaceAll("(", "")
          .replaceAll(")", "")
          .replaceAll("/", "")
          .replaceAll("-", "")
          .replaceAll(".", "")
          .split(" ")
          .join(""),
        data: newValue,
      })
    );
  };

  const getCurrency = (newValue) => {
    console.log("compcode", newValue);
    const hSuccess = (data) => {
      console.log("value", data);
      dispatch(
        setPayload({
          keyname: "Currency",
          data: "",
        })
      );
      dispatch(setDropDown({ keyName: "Currency", data: data.body }));
    };
    const hError = (error) => {
      console.log(error, "error in dojax");
    };
    doAjax(
      `/${destination_CostCenter}/data/getCurrency?companyCode=${newValue?.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getRegion = (newValue) => {
    console.log("countryyyyy", newValue);
    const hSuccess = (data) => {
      console.log("value", data);
      dispatch(
        setPayload({
          keyname: "Region",
          data: "",
        })
      );
      dispatch(setDropDown({ keyName: "Region", data: data.body }));
    };
    const hError = (error) => {
      console.log(error, "error in dojax");
    };
    doAjax(
      `/${destination_CostCenter}/data/getRegionBasedOnCountry?country=${newValue?.code}`,
      "get",
      hSuccess,
      hError
    );
  };

  console.log("dropDownData[key]", dropDownData[key]);
  return (
    <>
      {!isEditMode ? (
        (taskRequestId && visibility === "Hidden") ||
        (requestId && visibility === "Hidden") ? null : (
          <Grid item>
            <Stack>
              <Typography variant="body2" color="#777">
                {label}
                {visibility === "Required" || visibility === "0" ? (
                  <span style={{ color: "red" }}>*</span>
                ) : (
                  ""
                )}
              </Typography>
              <Typography variant="body2" fontWeight="bold">
                {data[key]}
                {type === "Radio Button" ? (
                  <Checkbox
                    sx={{ padding: 0 }}
                    checked={data[key]}
                    disabled
                  />
                ) : (
                  ""
                )}
              </Typography>
            </Stack>
          </Grid>
        )
      ) : visibility === "Hidden" ? null : (
        <Grid item>
          <Stack>
            {isEditMode ? (
              <div>
                <Typography variant="body2" color="#777">
                  {label}
                  {visibility === "Required" || visibility === "0" ? (
                    <span style={{ color: "red" }}>*</span>
                  ) : (
                    ""
                  )}
                </Typography>

                {type === "Drop Down" ? (
                  <Autocomplete
                    options={dropDownData[key] ?? []}
                    value={
                      (data[key] &&
                        dropDownData[key]?.filter(
                          (x) => x.code === data[key]
                        ) &&
                        dropDownData[key]?.filter(
                          (x) => x.code === data[key]
                        )[0]) ||
                      ""
                    }
                    onChange={(event, newValue, reason) => {
                      console.log("reason", reason);
                      console.log(visibility,newValue,"visibility2")
                      if(visibility === "Required" || visibility === "0"){
                        //alert("coming")
                        if(newValue === null)
                        setError(true)
                      }
                      if (label === "Comp Code") {
                        getCurrency(newValue);
                      }
                      if (label === "Country/Reg") {
                        getRegion(newValue);
                      }
                      if (reason === "clear") {
                        onEdit("");
                      } else {
                        onEdit(newValue?.code);
                      }
                      setEditedValue(newValue.code);
                      setChangeStatus(true);
                    }}
                    
                    getOptionLabel={(option) => {
                      console.log("optionn", option);
                      return option === "" || option?.code === ""
                        ? ""
                        : `${option?.code} - ${option?.desc}` ?? "";
                    }}
                    renderOption={(props, option) => {
                      console.log("option vakue", option);
                      return (
                        <li {...props}>
                          <Typography style={{ fontSize: 12 }}>
                            {`${option?.code} - ${option?.desc}`}
                          </Typography>
                        </li>
                      );
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        variant="outlined"
                        placeholder={`Select ${fieldData.label}`}
                        size="small"
                        // label={null}
                        error={error}
                      />
                    )}
                  />
                ) : type === "Input" ? (
                  <TextField
                    variant="outlined"
                    size="small"
                    fullWidth
                    value={data[key].toUpperCase()}
                    placeholder={`Enter ${fieldData.label}`}
                    inputProps={{ maxLength: length }}
                    onChange={(event) => {
                      const newValue = event.target.value;
                      //console.log(visibility,"visibility========");
                      
                      if (newValue.length > 0 && newValue[0] === " ") {
                        onEdit(newValue.trimStart());
                      } else {
                        //let costCenterValue = e.target.value;
                        let costCenterUpperCase = newValue.toUpperCase();
                        onEdit(costCenterUpperCase);
                      }
                      if(visibility === "Required" || visibility === "0"){
                        if(newValue.length <= 0)
                        setError(true)
                      }
                      // onEdit(newValue);
                      setEditedValue(newValue.toUpperCase());
                    }}
                    error={error}
                  />
                ) : type === "Calendar" ? (
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DatePicker
                      slotProps={{ textField: { size: "small" } }}
                      // value={data[key]}
                      placeholder="Select Date Range"
                      // onChange={(newValue) =>
                      //   dispatch(
                      //     setPayload({
                      //       keyName: props.keyName,
                      //       data: "/Date(" + Date.parse(newValue) + ")/",
                      //     })
                      //   )
                      // }
                      // required={
                      //   props.details.visibility === "0" ||
                      //   props.details.visibility === "Required"
                      // }
                    />
                  </LocalizationProvider>
                ) : type === "Radio Button" ? (
                  <Grid item md={2}>
                    <Checkbox
                      sx={{ padding: 0 }}
                      checked={data[key]}
                      onChange={(event, newValue) => {
                        onEdit(newValue);
                        setEditedValue(newValue);
                      }}
                    />
                  </Grid>
                ) : (
                  ""
                )}
              </div>
            ) : (
              ""
            )}
          </Stack>
        </Grid>
      )}
    </>
  );
};

export default EditableFieldForCostCenter;
