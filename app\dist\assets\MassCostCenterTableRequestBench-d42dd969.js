import{r as d,b as Ts,s as Ds,u as Fs,p as Dt,q as Z,bh as Is,j as u,V as se,aF as re,a as r,X as ne,aj as ws,al as Ns,G as E,I as x,b1 as Bs,T as m,h as ks,F as z,W as ae,bs as Ft,x as le,B as ie,aC as be,bt as Ps,y as ce,t as T,bm as Es,aE as qs,b8 as de,aB as L,aD as $s,K as D,b$ as w,bq as Se,aG as Me,ar as It,E as wt,C as Ls,z as Os,cc as zs,cb as _s,ca as js,ai as Rs,cj as Vs}from"./index-75c1660a.js";import{d as Us}from"./ArrowCircleLeftOutlined-f7b52d40.js";import{R as Ws}from"./ReusableAttachementAndComments-682b0475.js";import{d as Js}from"./AttachFileOutlined-872f8e38.js";import{M as Ks,a as Xs}from"./UtilDoc-7fb813ce.js";import{l as Nt}from"./lookup-1dcf10ac.js";import{T as Gs}from"./Timeline-5c068db1.js";import{t as Hs,T as Ys,a as Qs,b as Zs,c as xs,d as er}from"./TimelineSeparator-6e03ad1b.js";import"./CloudUpload-d5d09566.js";import"./utilityImages-067c3dc2.js";import"./Add-62a207fb.js";import"./Delete-1d158507.js";/* empty css            */import"./FileDownloadOutlined-329b8f56.js";import"./VisibilityOutlined-a5a8c4d9.js";import"./DeleteOutlined-fe5b7345.js";import"./Slider-c4e5ff46.js";import"./clsx-a965ebfb.js";const Tr=()=>{var Ue,We;const[Te,_]=d.useState(!0),[Bt,S]=d.useState(!1),[kt,or]=d.useState("1"),[V,Pt]=d.useState([]);d.useState([]);const pe=Ts(),De=Ds(),n=Fs().state,[Fe,g]=d.useState(""),[Et,ue]=d.useState(!1),[sr,f]=d.useState(!1),[qt,h]=d.useState(!1),[$t,v]=d.useState(!1),[rr,y]=d.useState(!0),[Lt,A]=d.useState(!1),[Ot,Ie]=d.useState(!1),[zt,we]=d.useState(!1),[_t,Ne]=d.useState(!1),[Be,ke]=d.useState(!0);Dt.useState(0);const[Ce,Pe]=d.useState("");Dt.useState({});const[he,jt]=d.useState([]),[me,Rt]=d.useState([]),[Vt,Ee]=d.useState(!1),[ge,K]=d.useState(!0),[qe,Ut]=d.useState(!0),[ee,Wt]=d.useState(""),[X,Jt]=d.useState([]),[fe,Kt]=d.useState({}),[Xt,Gt]=d.useState([]),[Ht,ve]=d.useState(!1),[oe,$e]=d.useState([]),[Yt,ye]=d.useState(!1),k=()=>{ue(!0)},Qt=()=>{Yt?(ue(!1),ye(!1)):(ue(!1),pe("/masterDataCockpit/costCenter"))},Zt=()=>{Ee(!0)},Le=()=>{Ee(!1)},F=Z(t=>t.costCenter.MultipleCostCenterData);console.log(F,"MultipleCostCenter");const U=Z(t=>t.appSettings);console.log("massCostCenterRowData",n);let a=Z(t=>t.userManagement.taskData),o=Z(t=>t.userManagement.userData),xt=Z(t=>{var l;return(l=t.userManagement.entitiesAndActivities)==null?void 0:l["Cost Center"]});const es=[{field:"id",headerName:"Document ID",flex:1,hide:!0},{field:"docType",headerName:"Type",flex:1},{field:"docName",headerName:"Document Name",flex:1},{field:"uploadedOn",headerName:"Uploaded On",flex:1,align:"center",headerAlign:"center"},{field:"uploadedBy",headerName:"Uploaded By",sortable:!1,flex:1},{field:"action",headerName:"Action",sortable:!1,filterable:!1,align:"center",headerAlign:"center",flex:1,renderCell:t=>u(z,{children:[r(Ks,{index:t.row.id,name:t.row.docName}),r(Xs,{index:t.row.id,name:t.row.docName})]})}],os=t=>{console.log("selected",t),Jt(t),t.length>0?(K(!0),console.log("selectedIds1",t)):K(!1),console.log("selectedIds",t),Pt(t)},ts=()=>{var i,e,b,M;_(!0);let t={};(a==null?void 0:a.processDesc)==="Mass Change"?t={massCreationId:"",massChangeId:a!=null&&a.subject?(i=a==null?void 0:a.subject)==null?void 0:i.slice(3):n==null?void 0:n.requestId.slice(3),screenName:"Change"}:(a==null?void 0:a.processDesc)==="Mass Create"?t={massCreationId:a!=null&&a.subject?(e=a==null?void 0:a.subject)==null?void 0:e.slice(3):n==null?void 0:n.requestId.slice(3),massChangeId:"",screenName:"Create"}:(n==null?void 0:n.requestType)==="Mass Create"?t={massCreationId:(b=n==null?void 0:n.requestId)==null?void 0:b.slice(3),massChangeId:"",screenName:"Create"}:(n==null?void 0:n.requestType)==="Mass Change"&&(t={massCreationId:"",massChangeId:(M=n==null?void 0:n.requestId)==null?void 0:M.slice(3),screenName:"Change"});const l=B=>{_(!1),De(js(B==null?void 0:B.body))},s=B=>{console.log(B)};D(`/${w}/data/displayMassCostCenter`,"post",l,s,t)},q=()=>{we(!1)},[Ae,ss]=d.useState(0),rs=(t,l)=>{const s=e=>{De(Rs({keyName:t,data:e.body})),ss(b=>b+1)},i=e=>{console.log(e)};D(`/${w}/data/${l}`,"get",s,i)},ns=()=>{var t,l;(l=(t=Nt)==null?void 0:t.costCenter)==null||l.map(s=>{rs(s==null?void 0:s.keyName,s==null?void 0:s.endPoint)})},as=()=>{var t,l;Ae==((l=(t=Nt)==null?void 0:t.costCenter)==null?void 0:l.length)?(_(!1),console.log("apiCount",Ae)):_(!0)};d.useEffect(()=>{Wt(Is("CC"))},[]),d.useEffect(()=>{as()},[Ae]),d.useEffect(()=>{ns()},[]),d.useEffect(()=>{if((F==null?void 0:F.length)===0)ts();else return},[]),console.log("newPayload",fe,X),d.useEffect(()=>{const t={};let l=[];console.log("userdata",o==null?void 0:o.role,F),(o==null?void 0:o.role)=="MDM Steward"||(o==null?void 0:o.role)=="Approver"?(console.log("userrole",o==null?void 0:o.role,F),l=F):l=F==null?void 0:F.filter((s,i)=>X==null?void 0:X.includes(i)),l.forEach(s=>{(s==null?void 0:s.controllingArea)in t?t[s.controllingArea].push(s):t[s.controllingArea]=[s]}),Kt(t),console.log("temparray",l,t)},[F,X]);const j=F==null?void 0:F.map((t,l)=>{var e,b,M,B,W,p,N,$,R,G,H,Y,Q;const s=t,i=((e=t==null?void 0:t.viewData)==null?void 0:e["Basic Data"])||{};return{id:l,costCenter:s.costCenter,controllingArea:s.controllingArea,name:((M=(b=i.Names)==null?void 0:b.find(C=>(C==null?void 0:C.fieldName)==="Name"))==null?void 0:M.value)||"",description:((W=(B=i.Names)==null?void 0:B.find(C=>(C==null?void 0:C.fieldName)==="Description"))==null?void 0:W.value)||"",personResponsible:((N=(p=i["Basic Data"])==null?void 0:p.find(C=>(C==null?void 0:C.fieldName)==="Person Responsible"))==null?void 0:N.value)||"",companyCode:((R=($=i["Basic Data"])==null?void 0:$.find(C=>(C==null?void 0:C.fieldName)==="Company Code"))==null?void 0:R.value)||"",profitCenter:((H=(G=i["Basic Data"])==null?void 0:G.find(C=>(C==null?void 0:C.fieldName)==="Profit Center"))==null?void 0:H.value)||"",costCenterCategory:((Q=(Y=i["Basic Data"])==null?void 0:Y.find(C=>(C==null?void 0:C.fieldName)==="Cost Center Category"))==null?void 0:Q.value)||"",validFrom:s.validFrom,validTo:s.validTo}}),ls=[{field:"costCenter",headerName:"Cost Center",editable:!1,flex:1,renderCell:t=>{const l=oe.find(s=>s.costCenter===t.value);return console.log(l,"isDirectMatch"),console.log(t,"params"),l&&l.code===400?r(m,{sx:{fontSize:"12px",color:"red"},children:t.value}):r(m,{sx:{fontSize:"12px"},children:t.value})}},{field:"controllingArea",headerName:"Controlling Area",editable:!1,flex:1},{field:"name",headerName:"Name",editable:!1,flex:1,renderCell:t=>{const l=Xt.includes(t.row.profitCenterName);return r(m,{sx:{fontSize:"12px",color:l?"red":"inherit"},children:t.value})}},{field:"description",headerName:"Description",editable:!1,flex:1},{field:"personResponsible",headerName:"Person Responsible",editable:!1,flex:1},{field:"companyCode",headerName:"Company Code",editable:!1,flex:1},{field:"profitCenter",headerName:"Profit Center",editable:!1,flex:1},{field:"costCenterCategory",headerName:"Cost Center Category",editable:!1,flex:1},{field:"validFrom",headerName:"Valid From",editable:!1,flex:1,renderCell:t=>r(m,{sx:{fontSize:"12px"},children:ce(t.row.validFrom).format(U==null?void 0:U.dateFormat)})},{field:"validTo",headerName:"Valid To",editable:!1,flex:1,renderCell:t=>r(m,{sx:{fontSize:"12px"},children:ce(t.row.validTo).format(U==null?void 0:U.dateFormat)})}],c=(t,l)=>{const s=t==null?void 0:t.find(i=>(i==null?void 0:i.fieldName)===l);return s?s.value:""},is=()=>{let t=a!=null&&a.subject?a==null?void 0:a.subject:n==null?void 0:n.requestId,l=s=>{var i=[];s.documentDetailDtoList.forEach(e=>{var b={id:e.documentId,docType:e.fileType,docName:e.fileName,uploadedOn:ce(e.docCreationDate).format(U.date),uploadedBy:e.createdBy};i.push(b)}),jt(i)};D(`/${Se}/documentManagement/getDocByRequestId/${t}`,"get",l)},cs=()=>{let t=a!=null&&a.subject?a==null?void 0:a.subject:n==null?void 0:n.requestId,l=i=>{console.log("commentsdata",i);var e=[];i.body.forEach(b=>{var M={id:b.requestId,comment:b.comment,user:b.createdByUser,createdAt:b.updatedAt};e.push(M)}),Rt(e),console.log("commentrows",e)},s=i=>{console.log(i)};D(`/${w}/activitylog/fetchTaskDetailsForRequestId?requestId=${t}`,"get",l,s)};d.useEffect(()=>{is(),cs()},[]);var I=(Ue=Object.keys(fe))==null?void 0:Ue.map(t=>{var l,s,i;return console.log("payloadmapping",t),{TaskId:a!=null&&a.taskId?a==null?void 0:a.taskId:"",CostCenterHeaderID:(l=F[0])==null?void 0:l.costCenterHeaderId,ControllingArea:t??"",Testrun:ge,Action:(a==null?void 0:a.processDesc)==="Mass Create"?"I":(a==null?void 0:a.processDesc)==="Mass Change"||(n==null?void 0:n.requestType)==="Mass Change"?"U":(n==null?void 0:n.requestType)==="Mass Create"?"I":"",ReqCreatedBy:o!=null&&o.user_id?o==null?void 0:o.user_id:"",ReqCreatedOn:o!=null&&o.createdOn?"/Date("+(o==null?void 0:o.createdOn)+")/":"",RequestStatus:"",CreationId:"",EditId:"",DeleteId:"",Remarks:Ce,MassCreationId:(a==null?void 0:a.processDesc)==="Mass Create"?(s=a==null?void 0:a.subject)==null?void 0:s.slice(3):"",MassEditId:(a==null?void 0:a.processDesc)==="Mass Change"?(i=a==null?void 0:a.subject)==null?void 0:i.slice(3):n==null?void 0:n.requestId.slice(3),MassDeleteId:"",RequestType:"",MassRequestStatus:"",Toitem:fe[t].map(e=>{var b,M,B,W,p,N,$,R,G,H,Y,Q,C,Je,Ke,Xe,Ge,He,Ye,Qe,Ze,xe,eo,oo,to,so,ro,no,ao,lo,io,co,po,uo,Co,ho,mo,go,fo,vo,yo,Ao,bo,So,Mo,To,Do,Fo,Io,wo,No,Bo,ko,Po,Eo,qo,$o,Lo,Oo,zo,_o,jo,Ro,Vo,Uo,Wo,Jo,Ko,Xo,Go,Ho,Yo,Qo,Zo,xo,et,ot,tt,st,rt,nt,at,lt,it,ct,dt,pt,ut,Ct,ht,mt,gt,ft,vt,yt,At,bt,St,Mt,Tt;return console.log("x",e),{CostCenterID:e!=null&&e.costCenterId?e==null?void 0:e.costCenterId:"",Costcenter:e!=null&&e.costCenter?e==null?void 0:e.costCenter:"",ValidFrom:e!=null&&e.validFrom?e==null?void 0:e.validFrom:"",ValidTo:e!=null&&e.validTo?e==null?void 0:e.validTo:"",PersonInCharge:c((M=(b=e==null?void 0:e.viewData)==null?void 0:b["Basic Data"])==null?void 0:M["Basic Data"],"Person Responsible"),CostcenterType:c((W=(B=e==null?void 0:e.viewData)==null?void 0:B["Basic Data"])==null?void 0:W["Basic Data"],"Cost Center Category"),CostctrHierGrp:c((N=(p=e==null?void 0:e.viewData)==null?void 0:p["Basic Data"])==null?void 0:N["Basic Data"],"Hierarchy Area"),BusArea:c((R=($=e==null?void 0:e.viewData)==null?void 0:$["Basic Data"])==null?void 0:R["Basic Data"],"Business Area"),CompCode:c((H=(G=e==null?void 0:e.viewData)==null?void 0:G["Basic Data"])==null?void 0:H["Basic Data"],"Company Code"),Currency:c((Q=(Y=e==null?void 0:e.viewData)==null?void 0:Y["Basic Data"])==null?void 0:Q["Basic Data"],"Currency"),ProfitCtr:c((Je=(C=e==null?void 0:e.viewData)==null?void 0:C["Basic Data"])==null?void 0:Je["Basic Data"],"Profit Center"),Name:c((Xe=(Ke=e==null?void 0:e.viewData)==null?void 0:Ke["Basic Data"])==null?void 0:Xe.Names,"Name"),Descript:c((He=(Ge=e==null?void 0:e.viewData)==null?void 0:Ge["Basic Data"])==null?void 0:He.Names,"Description"),PersonInChargeUser:c((Qe=(Ye=e==null?void 0:e.viewData)==null?void 0:Ye["Basic Data"])==null?void 0:Qe.Names,"User Responsible"),RecordQuantity:c((xe=(Ze=e==null?void 0:e.viewData)==null?void 0:Ze.Control)==null?void 0:xe.Control,"Record Quantity")===!0?"X":"",LockIndActualPrimaryCosts:c((oo=(eo=e==null?void 0:e.viewData)==null?void 0:eo.Control)==null?void 0:oo.Control,"Actual Primary Costs")===!0?"X":"",LockIndPlanPrimaryCosts:c((so=(to=e==null?void 0:e.viewData)==null?void 0:to.Control)==null?void 0:so.Control,"Plan Primary Costs")===!0?"X":"",LockIndActSecondaryCosts:c((no=(ro=e==null?void 0:e.viewData)==null?void 0:ro.Control)==null?void 0:no.Control,"Act. secondary Costs")===!0?"X":"",LockIndPlanSecondaryCosts:c((lo=(ao=e==null?void 0:e.viewData)==null?void 0:ao.Control)==null?void 0:lo.Control,"Plan Secondary Costs")===!0?"X":"",LockIndActualRevenues:c((co=(io=e==null?void 0:e.viewData)==null?void 0:io.Control)==null?void 0:co.Control,"Actual Revenue")===!0?"X":"",LockIndPlanRevenues:c((uo=(po=e==null?void 0:e.viewData)==null?void 0:po.Control)==null?void 0:uo.Control,"Plan Revenue")===!0?"X":"",LockIndCommitmentUpdate:c((ho=(Co=e==null?void 0:e.viewData)==null?void 0:Co.Control)==null?void 0:ho.Control,"Commitment Update")===!0?"X":"",ConditionTableUsage:"",Application:"",CstgSheet:c((go=(mo=e==null?void 0:e.viewData)==null?void 0:mo.Templates)==null?void 0:go["Overhead rates"],"Costing Sheet"),ActyIndepTemplate:c((vo=(fo=e==null?void 0:e.viewData)==null?void 0:fo.Templates)==null?void 0:vo["Formula planning"],"Acty-Indep. Form Plng Temp"),ActyDepTemplate:c((Ao=(yo=e==null?void 0:e.viewData)==null?void 0:yo.Templates)==null?void 0:Ao["Formula planning"],"Acty-Dep. Form Plng Temp"),AddrTitle:c((So=(bo=e==null?void 0:e.viewData)==null?void 0:bo.Address)==null?void 0:So["Address Data"],"Title"),AddrName1:c((To=(Mo=e==null?void 0:e.viewData)==null?void 0:Mo.Address)==null?void 0:To["Address Data"],"Name 1"),AddrName2:c((Fo=(Do=e==null?void 0:e.viewData)==null?void 0:Do.Address)==null?void 0:Fo["Address Data"],"Name 2"),AddrName3:c((wo=(Io=e==null?void 0:e.viewData)==null?void 0:Io.Address)==null?void 0:wo["Address Data"],"Name 3"),AddrName4:c((Bo=(No=e==null?void 0:e.viewData)==null?void 0:No.Address)==null?void 0:Bo["Address Data"],"Name 4"),AddrStreet:c((Po=(ko=e==null?void 0:e.viewData)==null?void 0:ko.Address)==null?void 0:Po["Address Data"],"Street"),AddrCity:c((qo=(Eo=e==null?void 0:e.viewData)==null?void 0:Eo.Address)==null?void 0:qo["Address Data"],"Location"),AddrDistrict:c((Lo=($o=e==null?void 0:e.viewData)==null?void 0:$o.Address)==null?void 0:Lo["Address Data"],"District"),AddrCountry:c((zo=(Oo=e==null?void 0:e.viewData)==null?void 0:Oo.Address)==null?void 0:zo["Address Data"],"Country/Reg"),AddrCountryIso:"",AddrTaxjurcode:c((jo=(_o=e==null?void 0:e.viewData)==null?void 0:_o.Address)==null?void 0:jo["Address Data"],"Jurisdiction"),AddrPoBox:c((Vo=(Ro=e==null?void 0:e.viewData)==null?void 0:Ro.Address)==null?void 0:Vo["Address Data"],"PO Box"),AddrPostlCode:c((Wo=(Uo=e==null?void 0:e.viewData)==null?void 0:Uo.Address)==null?void 0:Wo["Address Data"],"Postal Code"),AddrPobxPcd:c((Ko=(Jo=e==null?void 0:e.viewData)==null?void 0:Jo.Address)==null?void 0:Ko["Address Data"],"PO Box Post Cod"),AddrRegion:c((Go=(Xo=e==null?void 0:e.viewData)==null?void 0:Xo.Address)==null?void 0:Go["Address Data"],"Region"),TelcoLangu:"",TelcoLanguIso:c((Yo=(Ho=e==null?void 0:e.viewData)==null?void 0:Ho.Communication)==null?void 0:Yo["Communication Data"],"Language Key"),TelcoTelephone:c((Zo=(Qo=e==null?void 0:e.viewData)==null?void 0:Qo.Communication)==null?void 0:Zo["Communication Data"],"Telephone 1"),TelcoTelephone2:c((et=(xo=e==null?void 0:e.viewData)==null?void 0:xo.Communication)==null?void 0:et["Communication Data"],"Telephone 2"),TelcoTelebox:c((tt=(ot=e==null?void 0:e.viewData)==null?void 0:ot.Communication)==null?void 0:tt["Communication Data"],"Telebox Number"),TelcoTelex:c((rt=(st=e==null?void 0:e.viewData)==null?void 0:st.Communication)==null?void 0:rt["Communication Data"],"Telex Number"),TelcoFaxNumber:c((at=(nt=e==null?void 0:e.viewData)==null?void 0:nt.Communication)==null?void 0:at["Communication Data"],"Fax Number"),TelcoTeletex:c((it=(lt=e==null?void 0:e.viewData)==null?void 0:lt.Communication)==null?void 0:it["Communication Data"],"Teletex Number"),TelcoPrinter:c((dt=(ct=e==null?void 0:e.viewData)==null?void 0:ct.Communication)==null?void 0:dt["Communication Data"],"Printer Destination"),TelcoDataLine:c((ut=(pt=e==null?void 0:e.viewData)==null?void 0:pt.Communication)==null?void 0:ut["Communication Data"],"Data Line"),ActyDepTemplateAllocCc:c((ht=(Ct=e==null?void 0:e.viewData)==null?void 0:Ct.Templates)==null?void 0:ht["Activity and Business Process Allocation"],"Acty-Dep. Alloc Template"),ActyDepTemplateSk:c((gt=(mt=e==null?void 0:e.viewData)==null?void 0:mt.Templates)==null?void 0:gt["Actual Statistical Key Figures"],"Templ.: Act. Stat. Key Figure"),ActyIndepTemplateAllocCc:c((vt=(ft=e==null?void 0:e.viewData)==null?void 0:ft.Templates)==null?void 0:vt["Activity and Business Process Allocation"],"Acty-Indep. Alloc Temp"),ActyIndepTemplateSk:c((At=(yt=e==null?void 0:e.viewData)==null?void 0:yt.Templates)==null?void 0:At["Actual Statistical Key Figures"],"Templ.: Act. Stat. Key Figure"),AvcActive:!1,AvcProfile:"",BudgetCarryingCostCtr:"",CurrencyIso:"",Department:c((St=(bt=e==null?void 0:e.viewData)==null?void 0:bt["Basic Data"])==null?void 0:St["Basic Data"],"Department"),FuncArea:c((Tt=(Mt=e==null?void 0:e.viewData)==null?void 0:Mt["Basic Data"])==null?void 0:Tt["Basic Data"],"Functional Area"),FuncAreaFixAssigned:"",FuncAreaLong:"",Fund:"",FundFixAssigned:"",GrantFixAssigned:"",GrantId:"",JvEquityTyp:"",JvJibcl:"",JvJibsa:"",JvOtype:"",JvRecInd:"",JvVenture:"",Logsystem:""}})}});console.log("rishav",I);const P=()=>{Ie(!0)},te=()=>{we(!0)},J=()=>{K(!1),Ne(!0)},Oe=(t,l)=>{const s=t.target.value;if(s.length>0&&s[0]===" ")Pe(s.trimStart());else{let i=s.toUpperCase();Pe(i)}},O=()=>{K(!0),Ne(!1)},ds=()=>{(o==null?void 0:o.role)==="MDM Steward"&&a.processDesc==="Mass Create"||(o==null?void 0:o.role)==="MDM Steward"&&(n==null?void 0:n.requestType)==="Mass Create"?(q(),As()):(o==null?void 0:o.role)==="Approver"&&a.processDesc==="Mass Create"||(o==null?void 0:o.role)==="Approver"&&(n==null?void 0:n.requestType)==="Mass Create"?(q(),Ss()):(o==null?void 0:o.role)==="MDM Steward"&&a.processDesc==="Mass Change"||(o==null?void 0:o.role)==="MDM Steward"&&(n==null?void 0:n.requestType)==="Mass Change"?(q(),bs()):((o==null?void 0:o.role)==="Approver"&&a.processDesc==="Mass Change"||(o==null?void 0:o.role)==="Approver"&&(n==null?void 0:n.requestType)==="Mass Change")&&(q(),Ms())},ps=()=>{(o==null?void 0:o.role)==="MDM Steward"&&a.processDesc==="Mass Create"||(o==null?void 0:o.role)==="MDM Steward"&&(n==null?void 0:n.requestType)==="Mass Create"?(S(!0),O(),us()):(o==null?void 0:o.role)==="Approver"&&a.processDesc==="Mass Create"||(o==null?void 0:o.role)==="Approver"&&(n==null?void 0:n.requestType)==="Mass Create"?(S(!0),O(),hs()):(o==null?void 0:o.role)==="Finance"&&a.processDesc==="Mass Create"||(o==null?void 0:o.role)==="Finance"&&(n==null?void 0:n.requestType)==="Mass Create"?(S(!0),O(),fs()):(o==null?void 0:o.role)==="MDM Steward"&&a.processDesc==="Mass Change"||(o==null?void 0:o.role)==="MDM Steward"&&(n==null?void 0:n.requestType)==="Mass Change"?(S(!0),O(),Cs()):(o==null?void 0:o.role)==="Approver"&&a.processDesc==="Mass Change"||(o==null?void 0:o.role)==="Approver"&&(n==null?void 0:n.requestType)==="Mass Change"?(S(!0),O(),ms()):((o==null?void 0:o.role)==="Finance"&&a.processDesc==="Mass Change"||(o==null?void 0:o.role)==="Finance"&&(n==null?void 0:n.requestType)==="Mass Change")&&(S(!0),O(),vs())},us=()=>{console.log("paylaod",I);const l=i=>{S(!1),i.statusCode===200?(console.log("success"),h("Create"),g(`Mass Cost Center Submitted for Approval with ID NCM${i.body}`),v("success"),y(!1),A(!0),k(),f(!0)):(h("Error"),A(!1),g("Failed Submitting Mass Cost Center for Approval"),v("danger"),y(!1),f(!0),P())},s=i=>{console.log("error")};D(`/${w}/massAction/costCentersApprovalSubmit`,"post",l,s,I)},Cs=()=>{console.log("paylaod",I);const l=i=>{S(!1),console.log("kdfljsald"),i.statusCode===200?(console.log("success"),h("Create"),g(`Mass Cost Center Submitted for Approval with ID CCM${i.body}`),v("success"),y(!1),A(!0),k(),f(!0)):(h("Error"),A(!1),g("Failed Submitting Mass Cost Center for Approval"),v("danger"),y(!1),f(!0),P())},s=i=>{console.log("error")};D(`/${w}/massAction/changeCostCentersApprovalSubmit`,"post",l,s,I)},hs=()=>{console.log("paylaod",I);const l=i=>{S(!1),i.statusCode===201?(console.log("success"),h("Create"),g("Mass Cost Center Approved & SAP Syndication Completed"),v("success"),y(!1),A(!0),k(),f(!0)):(h("Error"),A(!1),g("Failed Approving Mass Cost Center "),v("danger"),y(!1),f(!0),P())},s=i=>{console.log("error")};D(`/${w}/massAction/createCostCentersApproved`,"post",l,s,I)},ms=()=>{console.log("paylaod",I);const l=i=>{S(!1),i.statusCode===201?(console.log("success"),h("Create"),g("Mass Cost Center Change Approved & SAP Syndication Completed"),v("success"),y(!1),A(!0),k(),f(!0)):(h("Error"),A(!1),g("Failed Approving Mass Cost Center Change"),v("danger"),y(!1),f(!0),P())},s=i=>{console.log("error")};D(`/${w}/massAction/changeCostCentersApproved`,"post",l,s,I)},ze=()=>{Ie(!1)},gs=()=>{},fs=()=>{j.filter((s,i)=>V.includes(i));const t=s=>{if(S(!1),s.statusCode===200){console.log("success"),h("Create"),g(`Mass Cost Center Sent for Review with ID NCM${s.body}`),v("success"),y(!1),A(!0),k(),f(!0);const i={artifactId:ee,createdBy:o==null?void 0:o.emailId,artifactType:"CostCenter",requestId:`NCM${s==null?void 0:s.body}`},e=M=>{console.log("Second API success",M)},b=M=>{console.error("Second API error",M)};D(`/${Se}/documentManagement/updateDocRequestId`,"post",e,b,i)}else h("Error"),A(!1),g("Failed Submitting the Cost Center for Review "),v("danger"),y(!1),f(!0),P()},l=s=>{console.log("error")};D(`/${w}/massAction/costCentersSubmitForReview`,"post",t,l,I)},vs=()=>{j.filter((s,i)=>V.includes(i));const t=s=>{if(S(!1),s.statusCode===200){console.log("success"),h("Create"),g(`Mass Cost Center Change Sent for Review with ID CCM${s.body}`),v("success"),y(!1),A(!0),k(),f(!0);const i={artifactId:ee,createdBy:o==null?void 0:o.emailId,artifactType:"CostCenter",requestId:`CCM${s==null?void 0:s.body}`},e=M=>{console.log("Second API success",M)},b=M=>{console.error("Second API error",M)};D(`/${Se}/documentManagement/updateDocRequestId`,"post",e,b,i)}else h("Error"),A(!1),g("Failed Submitting the Mass Cost Center for Review "),v("danger"),y(!1),f(!0),P()},l=s=>{console.log("error")};D(`/${w}/massAction/changeCostCentersSubmitForReview`,"post",t,l,I)},_e=()=>{S(!0);const t=s=>{s.statusCode===400?($e(s.body),ve(!0),S(!1)):(Ut(!1),h("Create"),console.log("success"),h("Create"),g("All Data has been Validated. Cost Center can be Sent for Review"),v("success"),y(!1),A(!0),k(),f(!0),ye(!0),S(!1))},l=s=>{console.log(s)};D(`/${w}/massAction/validateMassCostCenter`,"post",t,l,I)},ys=(t,l)=>t.every(s=>!l.includes(s)),je=()=>{S(!0);const t=j.filter((p,N)=>V.includes(N));console.log("selectedData",t);const l=[],s=[];t.map(p=>{var N={coArea:p==null?void 0:p.controllingArea,name:p==null?void 0:p.name};l.push(N),s.push(p==null?void 0:p.name.toUpperCase())});const i=[];F.map(p=>{var N,$;($=(N=p==null?void 0:p.viewData)==null?void 0:N["Basic Data"])==null||$.Names.map(R=>{R.fieldName==="Name"&&i.push(R.value.toUpperCase())})}),console.log(i,s,"arrayofviewand");let e=ys(i,s);console.log("duplicateCheckPayload",l);const b=p=>{p.statusCode===400?($e(p.body),ve(!0),S(!1)):(h("Create"),console.log("success"),h("Create"),g("All Data has been Validated. Cost Center can be Sent for Review"),v("success"),y(!1),A(!0),k(),f(!0),ye(!0),S(!1),(l.coArea!==""||l.name!=="")&&((n==null?void 0:n.requestType)==="Mass Change"&&!e?S(!1):D(`/${w}/alter/fetchCCDescriptionsDupliChk`,"post",M,B,l)))},M=p=>{console.log("dataaaa",p),p.body.length===0||!p.body.some(N=>l.some($=>$.name.toUpperCase()===N.matches[0]))?(S(!1),ke(!1),K(!0)):(S(!1),h("Duplicate Check"),A(!1),g("There is a direct match for the Cost Center name."),v("danger"),y(!1),f(!0),P(),ke(!0),Gt(directMatches))},B=p=>{console.log(p)},W=p=>{console.log(p)};D(`/${w}/massAction/validateMassCostCenter`,"post",b,W,I)},As=()=>{j.filter((s,i)=>V.includes(i));const t=s=>{_(!1),s.statusCode===200?(console.log("success"),h("Create"),g(`Cost Center Submitted for Correction with ID NCM${s.body}`),v("success"),y(!1),A(!0),k(),f(!0)):(h("Error"),A(!1),g("Failed Submitting Cost Center for Correction"),v("danger"),y(!1),f(!0),P()),q()},l=s=>{console.log(s)};D(`/${w}/massAction/costCentersSendForCorrection`,"post",t,l,I)},bs=()=>{j.filter((s,i)=>V.includes(i));const t=s=>{_(Vs),s.statusCode===200?(console.log("success"),h("Create"),g(`Cost Center Submitted for Correction with ID CCM${s.body}`),v("success"),y(!1),A(!0),k(),f(!0)):(h("Error"),A(!1),g("Failed Submitting Cost Center for Correction"),v("danger"),y(!1),f(!0),P()),q()},l=s=>{console.log(s)};D(`/${w}/massAction/changeCostCentersSendForCorrection`,"post",t,l,I)},Ss=()=>{console.log("isLoading7",Te),j.filter((s,i)=>V.includes(i));const t=s=>{_(!1),s.statusCode===200?(console.log("success"),h("Create"),g(`Cost Center Submitted for Correction with ID NCM${s.body}`),v("success"),y(!1),A(!0),k(),f(!0)):(h("Error"),A(!1),g("Failed Submitting Cost Center for Correction"),v("danger"),y(!1),f(!0),P()),q()},l=s=>{console.log(s)};D(`/${w}/massAction/costCentersSendForReview`,"post",t,l,I)},Ms=()=>{j.filter((s,i)=>V.includes(i));const t=s=>{_(!1),s.statusCode===200?(console.log("success"),h("Create"),g(`Cost Center Submitted for Correction with ID CCM${s.body}`),v("success"),y(!1),A(!0),k(),f(!0)):(h("Error"),A(!1),g("Failed Submitting Cost Center for Correction"),v("danger"),y(!1),f(!0),P()),q()},l=s=>{console.log(s)};D(`/${w}/massAction/changeCostCentersSendForReview`,"post",t,l,I)},Re=()=>{ve(!1)},Ve=(We=oe==null?void 0:oe.filter(t=>(t==null?void 0:t.code)===400))==null?void 0:We.map((t,l)=>{var s;if(t.code===400)return{id:l,costCenter:t==null?void 0:t.costCenter,error:(s=t==null?void 0:t.status)==null?void 0:s.message}});return u(z,{children:[u("div",{style:{backgroundColor:"#FAFCFF"},children:[u(se,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:zt,onClose:q,children:[u(re,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[r(m,{variant:"h6",children:"Remarks"}),r(x,{sx:{width:"max-content"},onClick:q,children:r(Me,{})})]}),r(ae,{sx:{padding:".5rem 1rem"},children:r(le,{children:r(ie,{sx:{minWidth:400},children:r(It,{sx:{height:"auto"},fullWidth:!0,children:r(wt,{sx:{backgroundColor:"#F5F5F5"},onChange:Oe,value:Ce,multiline:!0,placeholder:"Enter Remarks for Correction",inputProps:{maxLength:254}})})})})}),u(ne,{sx:{display:"flex",justifyContent:"end"},children:[r(T,{sx:{width:"max-content",textTransform:"capitalize"},onClick:q,children:"Cancel"}),r(T,{className:"button_primary--normal",type:"save",onClick:ds,variant:"contained",children:"Submit"})]})]}),u(se,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:_t,onClose:O,children:[u(re,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[r(m,{variant:"h6",children:"Remarks"}),r(x,{sx:{width:"max-content"},onClick:O,children:r(Me,{})})]}),r(ae,{sx:{padding:".5rem 1rem"},children:r(le,{children:r(ie,{sx:{minWidth:400},children:r(It,{sx:{height:"auto"},fullWidth:!0,children:r(wt,{sx:{backgroundColor:"#F5F5F5"},onChange:Oe,value:Ce,multiline:!0,placeholder:"Enter Remarks",inputProps:{maxLength:254}})})})})}),u(ne,{sx:{display:"flex",justifyContent:"end"},children:[r(T,{sx:{width:"max-content",textTransform:"capitalize"},onClick:O,children:"Cancel"}),r(T,{className:"button_primary--normal",type:"save",onClick:ps,variant:"contained",children:"Submit"})]})]}),u(se,{open:Ht,fullWidth:!0,onClose:Re,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[u(re,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[r(m,{variant:"h6",color:"red",children:"Errors"}),r(x,{sx:{width:"max-content"},onClick:Re,children:r(Me,{})})]}),r(ae,{sx:{padding:".5rem 1rem"},children:Ve&&r(be,{isLoading:Te,width:"100%",rows:Ve,columns:[{field:"costCenter",headerName:"Cost Center",editable:!1,flex:1},{field:"error",headerName:"Error",editable:!1,flex:1}],pageSize:10,getRowIdValue:"id",hideFooter:!0,checkboxSelection:!1,disableSelectionOnClick:!0,status_onRowSingleClick:!0,stopPropagation_Column:"action",status_onRowDoubleClick:!0})}),r(ne,{sx:{display:"flex",justifyContent:"end"}})]}),r(Ls,{dialogState:Ot,openReusableDialog:P,closeReusableDialog:ze,dialogTitle:qt,dialogMessage:Fe,handleDialogConfirm:ze,dialogOkText:"OK",handleExtraButton:gs,dialogSeverity:$t}),Lt&&r(Os,{openSnackBar:Et,alertMsg:Fe,handleSnackBarClose:Qt}),u("div",{style:{...ws,backgroundColor:"#FAFCFF"},children:[r(E,{container:!0,sx:Ns,children:u(E,{item:!0,md:12,sx:{display:"flex",marginBottom:"0"},children:[u(E,{item:!0,md:11,sx:{display:"flex"},children:[r(E,{children:r(x,{color:"primary","aria-label":"upload picture",component:"label",sx:Bs,children:r(Us,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{pe(-1)}})})}),a.processDesc==="Mass Create"?u(E,{children:[r(m,{variant:"h3",children:r("strong",{children:"Create Multiple Cost Center"})}),r(m,{variant:"body2",color:"#777",children:"This view displays list of uploaded Cost Centers"})]}):(n==null?void 0:n.requestType)==="Mass Create"?u(E,{children:[r(m,{variant:"h3",children:r("strong",{children:"Create Multiple Cost Center"})}),r(m,{variant:"body2",color:"#777",children:"This view displays list of Cost Centers"})]}):(n==null?void 0:n.requestType)==="Mass Change"?u(E,{children:[r(m,{variant:"h3",children:r("strong",{children:"Change Multiple Cost Center"})}),r(m,{variant:"body2",color:"#777",children:"This view displays list of Cost Centers"})]}):(a==null?void 0:a.processDesc)==="Mass Change"?u(E,{children:[r(m,{variant:"h3",children:r("strong",{children:"Change Multiple Cost Center"})}),r(m,{variant:"body2",color:"#777",children:"This view displays list of Cost Centers"})]}):""]}),r(E,{item:!0,md:1,sx:{display:"flex"},children:r(ks,{title:"Uploaded documents",arrow:!0,children:r(x,{onClick:Zt,children:r(Js,{})})})}),u(se,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:Vt,onClose:Le,children:[r(re,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:(o==null?void 0:o.role)==="MDM Steward"?r(z,{children:r(m,{variant:"h6",children:"Add Attachment"})}):""}),r(ae,{sx:{padding:".5rem 1rem"},children:u(Ft,{sx:{padding:"1rem 1rem 0rem 1rem"},children:[(o==null?void 0:o.role)==="Finance"?r(le,{children:r(ie,{sx:{minWidth:400},children:r(Ws,{title:"CostCenter",useMetaData:!1,artifactId:ee,artifactName:"CostCenter"})})}):"",r(E,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:r(m,{variant:"h6",children:r("strong",{children:"Attachments"})})}),!!he.length&&r(be,{width:"100%",rows:he,columns:es,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action",artifactId:ee}),!he.length&&r(m,{variant:"body2",children:"No Attachments Found"}),r("br",{}),r(m,{variant:"h6",children:"Comments"}),!!me.length&&r(Gs,{sx:{[`& .${Hs.root}:before`]:{flex:0,padding:0}},children:me.map(t=>u(Ys,{children:[u(Qs,{children:[r(Zs,{children:r(Ps,{sx:{color:"#757575"}})}),r(xs,{})]}),r(er,{sx:{py:"12px",px:2},children:r(Ft,{elevation:0,sx:{border:1,borderColor:"#C4C4C4",borderRadius:"8px",width:"650px"},children:r(ie,{sx:{padding:"1rem"},children:u(le,{spacing:1,children:[r(E,{sx:{display:"flex",justifyContent:"space-between"},children:r(m,{sx:{textAlign:"right",color:" #757575",fontWeight:"500",fontSize:"12px"},children:ce(t.createdAt).format("DD MMM YYYY")})}),r(m,{sx:{fontSize:"12px",color:" #757575",fontWeight:"500"},children:t.user}),r(m,{sx:{fontSize:"12px",color:"#1D1D1D",fontWeight:"600"},children:t.comment})]})})})})]}))}),!me.length&&r(m,{variant:"body2",children:"No Comments Found"}),r("br",{})]})}),r(ne,{children:r(T,{onClick:Le,children:"Close"})})]})]})}),r(E,{item:!0,sx:{position:"relative"},children:r(be,{width:"100%",rows:j,columns:ls,pageSize:10,getRowIdValue:"id",hideFooter:!1,disableSelectionOnClick:!0,status_onRowSingleClick:!0,checkboxSelection:(o==null?void 0:o.role)==="Finance"&&a.processDesc==="Mass Create"||(o==null?void 0:o.role)==="Finance"&&(n==null?void 0:n.requestType)==="Mass Create"||(o==null?void 0:o.role)==="Finance"&&a.processDesc==="Mass Change"||(o==null?void 0:o.role)==="Finance"&&(n==null?void 0:n.requestType)==="Mass Change",onRowsSelectionHandler:os,callback_onRowSingleClick:t=>{const l=t.row.costCenter,s=F.find(i=>i.costCenter===l);console.log(s,"yo"),pe(`/masterDataCockpit/costCenter/massCostCenterTableRequestBench/displayMultipleCostCenterRequestBench/${l}`,{state:{rowData:t.row,requestNumber:n==null?void 0:n.requestId.slice(3),tabsData:s,requestbenchRowData:n}})},stopPropagation_Column:"action",status_onRowDoubleClick:!0})})]}),Es(xt,"Cost Center","ChangeCC")?r($s,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:u(qs,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},value:kt,children:[(o==null?void 0:o.role)==="MDM Steward"&&a.processDesc==="Mass Create"||(o==null?void 0:o.role)==="MDM Steward"&&(n==null?void 0:n.requestType)==="Mass Create"?u(z,{children:[r(T,{variant:"outlined",size:"small",sx:{button_Outlined:de,mr:1},onClick:te,children:"Correction"}),r(T,{variant:"contained",size:"small",sx:{...L},onClick:J,children:"Submit For Approval"})]}):(o==null?void 0:o.role)==="Approver"&&a.processDesc==="Mass Create"||(o==null?void 0:o.role)==="Approver"&&(n==null?void 0:n.requestType)==="Mass Create"?u(z,{children:[r(T,{variant:"outlined",size:"small",sx:{button_Outlined:de,mr:1},onClick:te,children:"Correction"}),r(T,{variant:"contained",size:"small",sx:{...L,mr:1},onClick:_e,children:"Validate"}),r(T,{variant:"contained",size:"small",sx:{...L},onClick:J,disabled:qe,children:"Approve"})]}):(o==null?void 0:o.role)==="Finance"&&a.processDesc==="Mass Create"||(o==null?void 0:o.role)==="Finance"&&(n==null?void 0:n.requestType)==="Mass Create"?u(z,{children:[r(T,{variant:"contained",size:"small",sx:{...L,mr:1},onClick:je,disabled:!ge,children:"Validate"}),r(T,{variant:"contained",size:"small",sx:{...L},onClick:J,disabled:Be,children:"Submit For Review"})]}):"",(o==null?void 0:o.role)==="MDM Steward"&&a.processDesc==="Mass Change"||(o==null?void 0:o.role)==="MDM Steward"&&(n==null?void 0:n.requestType)==="Mass Change"?u(z,{children:[r(T,{variant:"outlined",size:"small",sx:{button_Outlined:de,mr:1},onClick:te,children:"Correction"}),r(T,{variant:"contained",size:"small",sx:{...L},onClick:J,children:"Submit For Approval"})]}):(o==null?void 0:o.role)==="Approver"&&a.processDesc==="Mass Change"||(o==null?void 0:o.role)==="Approver"&&(n==null?void 0:n.requestType)==="Mass Change"?u(z,{children:[r(T,{variant:"outlined",size:"small",sx:{button_Outlined:de,mr:1},onClick:te,children:"Correction"}),r(T,{variant:"contained",size:"small",sx:{...L,mr:1},onClick:_e,children:"Validate"}),r(T,{variant:"contained",size:"small",sx:{...L},onClick:J,disabled:qe,children:"Approve"})]}):(o==null?void 0:o.role)==="Finance"&&a.processDesc==="Mass Change"||(o==null?void 0:o.role)==="Finance"&&(n==null?void 0:n.requestType)==="Mass Change"?u(z,{children:[r(T,{variant:"contained",size:"small",sx:{...L,mr:1},onClick:je,disabled:!ge,children:"Validate"}),r(T,{variant:"contained",size:"small",sx:{...L},onClick:J,disabled:Be,children:"Submit For Review"})]}):""]})}):""]}),r(_s,{sx:{color:"#fff",zIndex:t=>t.zIndex.drawer+1},open:Bt,children:r(zs,{color:"inherit"})})]})};export{Tr as default};
