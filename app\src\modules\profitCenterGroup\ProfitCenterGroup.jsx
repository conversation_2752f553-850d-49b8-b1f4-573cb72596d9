import {
  Autocomplete,
  Backdrop,
  BottomNavigation,
  Box,
  Button,
  CircularProgress,
  <PERSON><PERSON>se,
  Dialog,
  Tooltip,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  MenuList,
  MenuItem,
  Popper,
  FormControlLabel,
  FormLabel,
  Grid,
  IconButton,
  ButtonGroup,
  Paper,
  Radio,
  RadioGroup,
  Stack,
  Step,
  StepLabel,
  StepButton,
  Stepper,
  Accordion,
  AccordionSummary,
  ClickAwayListener,
  AccordionDetails,
  SvgIcon,
  Skeleton,
  Tab,
  Tabs,
  TextField,
  Typography,
  tooltipClasses,
} from "@mui/material";
import ClearIcon from "@mui/icons-material/Clear";
import SearchIcon from "@mui/icons-material/Search";
import { colors } from "@constant/colors";
import SingleSelectDropdown from "@components/Common/ui/dropdown/SingleSelectDropdown";
import InfoIcon from "@mui/icons-material/Info";
import PlusOneIcon from "@mui/icons-material/PlusOne";
import ControlPointIcon from "@mui/icons-material/ControlPoint";
import FilterListIcon from "@mui/icons-material/FilterList";
import RemoveCircleOutlineIcon from "@mui/icons-material/RemoveCircleOutline";

import { AiOutlinePlus, AiOutlineMinus } from "react-icons/ai";

import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

import { alpha, styled } from "@mui/material/styles";

import { TreeItem, treeItemClasses } from "@mui/x-tree-view/TreeItem";

import { useSpring, animated } from "@react-spring/web";

import React, { useEffect, useRef, useState } from "react";

import { useSelector, useDispatch } from "react-redux";

import {
  iconButton_SpacingSmall,
  font_Small,
  button_Primary,
  button_Outlined,
  container_Padding,
  button_Marginleft,
  outermostContainer,
  container_filter,
  outermostContainer_Information,
  outerContainer_Information,
} from "../../components/Common/commonStyles";
import { useNavigate } from "react-router-dom";
import CloseIcon from "@mui/icons-material/Close";

import { setDropDown } from "../../app/dropDownDataSlice";
import { doAjax } from "../../components/Common/fetchService";
import {
  destination_IDM,
  destination_ProfitCenter,
  destination_ProfitCenter_Mass,
} from "../../destinationVariables";
import {
  profitCenterReducer,
  setHandleMassMode,
} from "../../app/profitCenterTabsSlice";
import AttachmentUploadDialog from "../../components/Common/AttachmentUploadDialog";
import { id } from "date-fns/locale";
import {
  clearHierarchyGroup,
  commonFilterClear,
  commonFilterUpdate,
} from "../../app/commonFilterSlice";
import ReusableHierarchyTree from "../../components/MasterDataCockpit/Hierarchy/ReusableHIerarchyTree";
import ReusablePreset from "../../components/Common/ReusablePresetFilter";
import ReusableDialog from "../../components/Common/ReusableDialog";
import { clearArtifactId, setAttachmentType } from "../../app/initialDataSlice";
import {
  clearGeneralLedger,
  clearSingleGLPayloadGI,
} from "../../app/generalLedgerTabSlice";
import { clearTaskData } from "../../app/userManagementSlice";
import ReusableBackDrop from "../../components/Common/ReusableBackDrop";
import useLang from "@hooks/useLang";

const StyledAccordionSummary = styled(AccordionSummary)(({ theme }) => ({
  minHeight: "2rem !important",
  margin: "0px !important",
  backgroundColor: colors.primary.ultraLight,
  borderRadius: "8px 8px 0 0",
  transition: "all 0.2s ease-in-out",
  "&:hover": {
    backgroundColor: `${colors.primary.light}20`,
  },
}));

const StyledAccordion = styled(Accordion)(({ theme }) => ({
  marginTop: "0px !important",
  border: `1px solid ${colors.primary.border}`,
  borderRadius: "8px",
  boxShadow: "0 2px 4px rgba(0,0,0,0.05)",
  "&:not(:last-child)": {
    borderBottom: 0,
  },
  "&:before": {
    display: "none",
  },
}));

const ButtonContainer = styled(Grid)({
  display: "flex",
  justifyContent: "flex-end",
  paddingRight: "0.75rem",
  paddingBottom: "0.75rem",
  paddingTop: "0rem",
  gap: "0.5rem",
});

const ActionButton = styled(Button)({
  borderRadius: "4px",
  padding: "4px 12px",
  textTransform: "none",
  fontSize: "0.875rem",
});

const LabelTypography = styled(Typography)({
  fontSize: "0.75rem",
  color: colors.primary.dark,
  marginBottom: "0.25rem",
  fontWeight: 500,
});

const ProfitCenterGroup = () => {
  let ref_elementForExport = useRef(null);
  const { t } = useLang();
  const [openInitialDialog, setOpenInitialDialog] = useState(false);
  const anchorRefCreate = useRef(null);
  const anchorRefChange = useRef(null);
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [activeStep, setActiveStep] = useState(0);
  const [completed, setCompleted] = React.useState({});
  const [openButtonCreate, setOpenButtonCreate] = useState(false);
  const [openButtonChange, setOpenButtonChange] = useState(false);
  const [selectedIndexCreate, setSelectedIndexCreate] = useState(0);
  const [anchorEl, setAnchorEl] = useState(null);
  const [controllingArea, setControllingArea] = useState({
    code: "ETCA",
    desc: "ET FAMILY CO AREA",
  });
  const [profitCenterGroup, setProfitCenterGroup] = useState(null);
  const [profitCenterGroupDescription, setProfitCenterGroupDescription] =
    useState(null);
  const anchorRef = React.useRef(null);
  const [bigTreeData, setBigTreeData] = useState([]);
  const [openDownloadExcelDialog, setOpenDownloadExcelDialog] = useState(false);
  const [enableDocumentUpload, setEnableDocumentUpload] = useState(false);
  const [scenarioState, setScenarioState] = useState("create");
  const [isLoading, setIsLoading] = useState(false);
  const [newNodeList, setNewNodeList] = useState([]);
  const [newTagList, setNewTagList] = useState([]);
  const [newDescList, setNewDescList] = useState([]);
  const [newReplaceNodeList, setNewReplaceNodeList] = useState([]);
  const [newReplaceTagList, setNewReplaceTagList] = useState([]);
  const [newDescriptionAdded, setNewDescriptionAdded] = useState([]);
  const [newAddedNode, setNewAddedNode] = useState([]);
  const [moveNodeList, setMoveNodeList] = useState([]);
  const [restoreMoveNode, setRestoreMoveNode] = useState([]);
  const [removePCData, setRemovePCData] = useState([]);
  const [removeNodeData, setRemoveNodeData] = useState([]);
  const [storeMoveTagList, setStoreMoveTagList] = useState([]);
  const [restoreMoveTagList, setRestoreMoveTagList] = useState([]);
  const [changeDescription, setChangeDescription] = useState([]);
  const [isPresetActive, setIsPresetActive] = useState(false);

  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState("");
  const [messageDialogMessage, setMessageDialogMessage] = useState();
  const [messageDialogSeverity, setMessageDialogSeverity] = useState("");
  const [parentNodeForObject, setParentNodeForObject] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [searchValue, setSearchValue] = useState("");
  const [isValidationErrorNode, setIsValidationErrorNode] = useState(false);
  const [isValidationErrorDesc, setIsValidationErrorDesc] = useState(false);

  const [isDuplicateNodeRequest, setIsDuplicateNodeRequest] = useState(false);
  const [isDuplicateDBDesc, setIsDuplicateDBDesc] = useState(false);
  const [isProceedDisabled, setIsProceedDisabled] = useState(true);
  const [isProceedCreateDisabled, setIsProceedCreateDisabled] = useState(true);

  const [openDownloadDialog, setOpenDownloadDialog] = useState(false);
  const [downloadType, setDownloadType] = useState("systemGenerated");

  const [openDownloadChangeDialog, setOpenDownloadChangeDialog] =
    useState(false);
  const options = ["Create Multiple", "Upload Template", "Download Template"];
  const optionsChange = [
    "Change Multiple",
    "Upload Template",
    "Download Template",
  ];
  const [multipleProfitCenterData, setMultipleProfitCenterData] = useState({});
  // const [data, setData] = useState(data1);
  const [initialNodeData, setInitialNodeData] = useState([
    {
      id: "1",
      title: "",
      child: [],
      tags: [],
      description: "",
    },
  ]);
  let iwaAccessData = useSelector(
    (state) => state.userManagement.entitiesAndActivities?.["Material"]
  );
  // const [initialNodeData, setInitialNodeData] = useState([{}]);
  console.log("initialNodeData", initialNodeData);

  const [isAccordionExpanded, setIsAccordionExpanded] = useState(
    initialNodeData.length === 0 || !initialNodeData[0]?.title // condition to check if no data
  );

  const NoMaxWidthTooltip = styled(({ className, ...props }) => (
    <Tooltip {...props} classes={{ popper: className }} />
  ))({
    [`& .${tooltipClasses.tooltip}`]: {
      maxWidth: "none",
    },
  });

  const handleDownloadDialogClose = () => {
    setOpenDownloadDialog(false);
    setDownloadType("systemGenerated");
  };
  const handleDownloadChangeDialogClose = () => {
    setOpenDownloadChangeDialog(false);
    setDownloadType("systemGenerated");
  };
  const handleDownloadTypeChange = (event) => {
    setDownloadType(event?.target?.value);
  };
  const handleMultipleDownloadTypeChange = (event) => {
    setDownloadType(event?.target?.value);
  };
  const onDownloadTypeChange = () => {
    if (downloadType === "systemGenerated") {
      handleDownloadCreate();
      handleDownloadDialogClose();
    }
    if (downloadType === "mailGenerated") {
      handleEmailDownload();
      handleDownloadDialogClose();
    }
  };
  const onMultipleDownloadTypeChange = () => {
    console.log("changedownloadtype", downloadType);
    if (downloadType === "systemGenerated") {
      handleChangeDownload();
      handleDownloadChangeDialogClose();
    }
    if (downloadType === "mailGenerated") {
      handleChangeDownloadEmail();
      handleDownloadChangeDialogClose();
    }
  };

  const handleToggleCreate = () => {
    setOpenButtonCreate((prevOpen) => !prevOpen);
  };
  const handleToggleChange = () => {
    setOpenButtonChange((prevOpen) => !prevOpen);
  };

  const applicationConfig = useSelector((state) => state.applicationConfig);
  const handleMassModePC = useSelector(
    (state) => state.profitCenter.handleMassMode
  );
  const getFilter = () => {
    setIsAccordionExpanded(false);
    // debugger
    setIsLoading(true);
    var payload = {
      node:
        pcSearchForm?.profitCenterGroup?.code === ""
          ? ""
          : pcSearchForm?.profitCenterGroup?.code,
      controllingArea:
        pcSearchForm?.controllingArea?.code === ""
          ? ""
          : pcSearchForm?.controllingArea?.code,
      classValue: "0106",
      id: "",
      screenName: "Display",
    };
    const hSuccess = (data) => {
      console.log(data.body, "data");
      let innerData = [];
      if (data.statusCode === 200) {
        innerData.push(data.body.HierarchyTree);
        setInitialNodeData(innerData);
      }
      setInitialNodeData(innerData);
      setIsLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/displayHierarchyTreeNodeStructure`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const dropDownData = useSelector((state) => state?.AllDropDown?.dropDown);
  const pcSearchForm = useSelector(
    (state) => state.commonFilter["HierarchyNodeProfitCenter"]
  );
  console.log("pcSearchForm", pcSearchForm);

  // For Search filter
  const handleControllingAreaSearch = (value) => {
    if (true) {
      var tempControllingArea = value;

      let tempFilterData = {
        ...pcSearchForm,
        controllingArea: tempControllingArea,
      };
      dispatch(
        commonFilterUpdate({
          module: "HierarchyNodeProfitCenter",
          filterData: tempFilterData,
        })
      );
    }
    // getProfitCenterGroup(value.code);
    getProfitCenterGroupFilter(value.code);
  };
  const handleProfitCenterGroupSearch = (value) => {
    console.log("value", value);
    if (true) {
      var tempProfitCenterGroup = value;
      console.log("tempProfitCenterGroup", tempProfitCenterGroup);
      let tempFilterData = {
        ...pcSearchForm,
        profitCenterGroup: tempProfitCenterGroup,
      };
      dispatch(
        commonFilterUpdate({
          module: "HierarchyNodeProfitCenter",
          filterData: tempFilterData,
        })
      );
    }
  };

  const getControllingArea = (value) => {
    console.log("first", value);
    const hSuccess = (data) => {
      dispatch(
        setDropDown({
          keyName: "COAREA",
          data: data.body,
        })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getControllingArea`,
      "get",
      hSuccess,
      hError
    );
  };

  const getNewControllingArea = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_CUSTOM_DROPDOWN_LIST",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MODULE": "PCG",
          "MDG_CONDITIONS.MDG_FIELD_NAME": "Controlling Area",
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };

    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        const lookupData =
          data?.data?.result[0]?.MDG_CUSTOM_LOOKUP_ACTION_TYPE || [];
        console.log("questionData", lookupData);

        let lookupDataArr = [];
        lookupData?.map((itemData) => {
          let lookupDataHash = {};
          lookupDataHash["code"] = itemData?.MDG_LOOKUP_CODE;
          lookupDataHash["desc"] = itemData?.MDG_LOOKUP_DESC;
          lookupDataArr.push(lookupDataHash);
        });
        console.log(lookupDataArr, "lookupDataArr");

        dispatch(
          setDropDown({ keyName: "NewControllingArea", data: lookupDataArr })
        );
        // setQuestions(questionsData);
      }
    };

    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(
        `/${destination_IDM}/rest/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      doAjax(
        `/${destination_IDM}/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };

  const handleParentNodeSearch = () => {
    if (searchValue.length === 10) {
      getParentNode(searchValue);
      setErrorMessage("");
    } else {
      setErrorMessage("Profit Center needs to be of length 10");
    }
  };

  const getParentNode = (value) => {
    const secondPayload = {
      controllingArea: pcSearchForm?.controllingArea?.code,
      hierarchy: pcSearchForm?.profitCenterGroup?.code,
      pcList: [value],
    };
    const secondApiSuccess = (secondData) => {
      // let secondData = {
      //   statusCode: "200",
      //   body: [{ "Node": "877888", "ProfitCenter": "YYTUYU" }]
      // }
      if (
        secondData?.statusCode == "200" &&
        secondData?.body &&
        secondData?.body.length > 0
      ) {
        console.log("herrr");
        setParentNodeForObject(secondData?.body[0]?.Node);
        setErrorMessage("");
      } else {
        setParentNodeForObject("");
        setErrorMessage("No parent node found.");
      }
    };
    const secondApiError = (error) => {
      // setNodeData('');
      setErrorMessage("Error fetching parent node.");
    };
    doAjax(
      `${destination_ProfitCenter_Mass}/node/fetchParentNodeForObject`,
      "post",
      secondApiSuccess,
      secondApiError,
      secondPayload
    );
  };

  // for create/ change dialog
  const getProfitCenterGroup = (value) => {
    console.log("first", value);
    const hSuccess = (data) => {
      dispatch(
        setDropDown({
          keyName: "PRCTRGroup",
          data: data.body,
        })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/node/getZeroLevelNodes?controllingArea=${value}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getProfitCenterGroupFilter = (value) => {
    console.log("first", value);
    const hSuccess = (data) => {
      dispatch(
        setDropDown({
          keyName: "PRCTRGroupFilter",
          data: data.body,
        })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getProfitCtrGroup?controllingArea=${value}`,
      "get",
      hSuccess,
      hError
    );
  };

  const handleAccordionToggle = () => {
    setIsAccordionExpanded(!isAccordionExpanded);
  };

  const handleClear = () => {
    dispatch(commonFilterClear({ module: "HierarchyNodeProfitCenter" }));
  };

  const getHeirarchyNodeTreeStructure = () => {
    setIsLoading(true);
    var payload = {
      node:
        pcSearchForm?.profitCenterGroup?.code === ""
          ? "DSM-1"
          : pcSearchForm?.profitCenterGroup?.code,
      controllingArea:
        pcSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : pcSearchForm?.controllingArea?.code,
      classValue: "0106",
      id: "",
      screenName: "Display",
    };
    const hSuccess = (data) => {
      console.log(data.body, "data");
      let innerData = [];
      if (data.statusCode === 200) {
        innerData.push(data.body.HierarchyTree);
        setInitialNodeData(innerData);
      }
      setInitialNodeData(innerData);
      setIsLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/displayHierarchyTreeNodeStructure`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  console.log("setdata", initialNodeData);

  useEffect(() => {
    // getHeirarchyNodeTreeStructure();
    getControllingArea();
    getProfitCenterGroup("ETCA");
    getProfitCenterGroupFilter("ETCA");
    // getFilter();
    getNewControllingArea();
    dispatch(clearArtifactId());
    dispatch(clearGeneralLedger());
    dispatch(clearSingleGLPayloadGI());
    dispatch(clearTaskData());
    // dispatch(clearProfitCenterGroup());
    dispatch(
      clearHierarchyGroup({
        module: "HierarchyNodeProfitCenter",
        groupName: "profitCenterGroup",
      })
    );
    // dispatch(commonFilterClear({ module: "HierarchyNodeProfitCenter" }));
  }, []);

  const getBigTreeData = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_BIG_HIERARCHY_NODES_DT",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [{ "MDG_CONDITIONS.MDG_MODULE": "PC Hierarchy Group" }],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };

    const hSuccess = (data) => {
      // debugger
      if (data.statusCode === 200) {
        var tempArr = [];
        data?.data?.result?.[0]?.MDG_BIG_HIERARCHY_NODES_ACTION_TYPE?.map(
          (item) => {
            tempArr?.push(item?.MDG_HIERARCHY_BIG_NODES);
          }
        );
        setBigTreeData(tempArr);
      }
    };

    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(
        `/${destination_IDM}/rest/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      doAjax(
        `/${destination_IDM}/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };

  useEffect(() => {
    getBigTreeData();
  }, []);

  const refreshPage = () => {
    dispatch(commonFilterClear({ module: "HierarchyNodeProfitCenter" }));
    // reloadDisplay();
    setInitialNodeData([{}]);
    setIsAccordionExpanded(true);
  };
  const reloadDisplay = () => {
    setIsLoading(true);
    var payload = {
      node: "AAM-1",
      controllingArea: "ETCA",
      classValue: "0106",
      id: "",
      screenName: "Display",
    };
    const hSuccess = (data) => {
      console.log(data.body, "data");
      let innerData = [];
      if (data.statusCode === 200) {
        innerData.push(data.body.HierarchyTree);
        setInitialNodeData(innerData);
      }
      setInitialNodeData(innerData);
      setIsLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/displayHierarchyTreeNodeStructure`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const handleDownloadCreate = async () => {
    setLoaderMessage(
      "Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience."
    );
    // setIsLoading(true);
    setBlurLoading(true);
    let hSuccess = (response) => {
      if (response?.Blob?.size !== 0 || response?.Blob?.type !== "") {
        // setIsLoading(false);
        setBlurLoading(false);
        setLoaderMessage("");
        const href = URL.createObjectURL(response);
        const link = document.createElement("a");
        link.href = href;
        link.setAttribute("download", `PCG Mass Create.xlsx`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(href);
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Success");
        setMessageDialogMessage(
          `Profit Center Hierarchy_Mass Create.xlsx has been downloaded successfully`
        );
        setMessageDialogSeverity("success");
      } else {
        setBlurLoading(false);
        setLoaderMessage(false);
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage("Please try again.");
        //setMessageDialogMessage(`${error.message}`);
        setMessageDialogSeverity("danger");
      }
    };
    let hError = (error) => {
      // setIsLoading(false);
      setBlurLoading(false);
      setLoaderMessage("");
      if (error) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(`Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>
 `);
        setMessageDialogSeverity("danger");
      }
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/excel/downloadHierarchyNodeTemplate`,
      "getblobfile",
      hSuccess,
      hError
      // downloadPayload
    );
  };
  const handleEmailDownload = async () => {
    // setLoaderMessage(
    //   "Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience."
    // );
    // setIsLoading(true);
    setBlurLoading(true);
    let hSuccess = (response) => {
      // setIsLoading(false);
      setBlurLoading(false);
      setLoaderMessage("");
      // const href = URL.createObjectURL(response);
      // const link = document.createElement("a");
      // link.href = href;
      // link.setAttribute("download", `PCG Mass Create.xlsx`);
      // document.body.appendChild(link);
      // link.click();
      // document.body.removeChild(link);
      // URL.revokeObjectURL(href);
      handleMessageDialogClickOpen();
      setMessageDialogTitle("Success");
      setMessageDialogMessage(
        `Download has been started. you will get the Excel file via email`
      );
      setMessageDialogSeverity("success");
    };
    let hError = (error) => {
      // setIsLoading(false);
      setBlurLoading(false);
      setLoaderMessage("");
      if (error) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(`Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>
 `);
        setMessageDialogSeverity("danger");
      }
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/excel/downloadHierarchyNodeTemplateInMail`,
      "get",
      hSuccess,
      hError
      // downloadPayload
    );
  };
  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };
  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };
  const uploadExcel = (file) => {
    console.log(file);
    const formData = new FormData();
    [...file].forEach((item) => formData.append("files", item));
    console.log("fffff", formData);
    if (handleMassModePC === "Change") {
      var uploadUrl = `/${destination_ProfitCenter_Mass}/massAction/getAllHierarchyNodeFromExcelForChange`;
      setEnableDocumentUpload(false);
      setBlurLoading(true);
      const hSuccess = (data) => {
        console.log(data?.body, "data?.body");
        if (data.statusCode === 200) {
          setBlurLoading(false);
          setLoaderMessage("");
          const content = (
            <Typography component="div">
              <ul>
                <li>
                  Mass Upload Process has Started in the background. As soon as
                  the request ID is generated, you will receive a notification
                  and mail for it containing the new request ID number.
                </li>
                <li>
                  Then you can visit the Request Bench Tab and search for that
                  request ID and do further actions on it.
                </li>
                <li>
                  Note - All request IDs generated in the background would
                  initially have the status Draft.
                </li>
              </ul>
            </Typography>
          );
          setMessageDialogTitle("Header - Information");
          setMessageDialogMessage(content);
          setMessageDialogSeverity("success");
          handleMessageDialogClickOpen();
        } else if (data.statusCode === 429) {
          setBlurLoading(false);
          setMessageDialogTitle("Error");
          setLoaderMessage("");
          setEnableDocumentUpload(false);
          setMessageDialogMessage(
            data?.message || "Too many requests. Please try again later."
          );
          setMessageDialogSeverity("danger");
          handleMessageDialogClickOpen();
        } else {
          setEnableDocumentUpload(false);
          setBlurLoading(false);
          setLoaderMessage("");
          setMessageDialogTitle("Error");
          setMessageDialogMessage(
            "Upload failed. Incorrect template tab name, please recheck upload file"
          );
          setMessageDialogSeverity("danger");
          handleMessageDialogClickOpen();
        }
      };
      const hError = (error) => {
        setBlurLoading(false);
        setLoaderMessage("");
        console.log(error);
        setMessageDialogTitle("Error");
        setMessageDialogMessage(
          `Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>`
        );
        setMessageDialogSeverity("danger");
        handleMessageDialogClickOpen();
      };
      doAjax(uploadUrl, "postformdata", hSuccess, hError, formData);
    } else {
      var uploadUrl = `/${destination_ProfitCenter_Mass}/massAction/getAllHierarchyNodeFromExcel`;
      setEnableDocumentUpload(false);
      setBlurLoading(true);
      const hSuccess = (data) => {
        if (data.statusCode === 200) {
          setBlurLoading(false);
          setLoaderMessage("");
          const content = (
            <Typography component="div">
              <ul>
                <li>
                  Mass Upload Process has Started in the background. As soon as
                  the request ID is generated, you will receive a notification
                  and mail for it containing the new request ID number.
                </li>
                <li>
                  Then you can visit the Request Bench Tab and search for that
                  request ID and do further actions on it.
                </li>
                <li>
                  Note - All request IDs generated in the background would
                  initially have the status Draft.
                </li>
              </ul>
            </Typography>
          );
          setMessageDialogTitle("Header - Information");
          setMessageDialogMessage(content);
          setMessageDialogSeverity("success");
          handleMessageDialogClickOpen();
          setIsLoading(false);
        } else if (data.statusCode === 429) {
          setBlurLoading(false);
          setMessageDialogTitle("Error");
          setLoaderMessage("");
          setEnableDocumentUpload(false);
          setMessageDialogMessage(
            data?.message || "Too many requests. Please try again later."
          );
          setMessageDialogSeverity("danger");
          handleMessageDialogClickOpen();
        } else {
          setEnableDocumentUpload(false);
          setBlurLoading(false);
          setLoaderMessage("");
          console.log("success200failcreate");
          setMessageDialogTitle("Error");
          // setsuccessMsg(false);
          setMessageDialogMessage(
            "Upload failed. Incorrect template tab name, please recheck upload file"
          );
          setMessageDialogSeverity("danger");
          // setMessageDialogOK(false);
          // setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          // setIsLoading(false);
        }
        // handleClose();
      };
      const hError = (error) => {
        setBlurLoading(false);
        setLoaderMessage("");
        console.log(error);
        setMessageDialogTitle("Error");
        setMessageDialogMessage(
          `Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>`
        );
        setMessageDialogSeverity("danger");
        // setMessageDialogOK(false);
        // setMessageDialogExtra(true);
        // setDialogOkText("OK");
        handleMessageDialogClickOpen();
      };
      doAjax(uploadUrl, "postformdata", hSuccess, hError, formData);
    }

    // navigate(
    //   "/masterDataCockpit/et/hierarchyNodeProfitCenter/createMassHierarchyNode"
    // );
  };

  console.log("msssd", multipleProfitCenterData);

  const handleCreateMultiple = () => {
    setEnableDocumentUpload(true);
    dispatch(setHandleMassMode("Create"));
  };

  const handleClickCreate = (option, index) => {
    // dispatch(setHandleMassMode("Change"));
    if (index !== 0) {
      setSelectedIndexCreate(index);
      setOpenButtonCreate(false);
      if (index === 1) {
        handleCreateMultiple();
        // setEnableDocumentUpload(true);
      } else if (index === 2) {
        // handleDownloadCreate();
        setOpenDownloadDialog(true);
      }
    }
  };
  const handleChangeMultiple = () => {
    setEnableDocumentUpload(true);
    dispatch(setHandleMassMode("Change"));
  };

  const handleChangeDownload = () => {
    setLoaderMessage(
      "Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience."
    );
    // setIsLoading(true);
    setBlurLoading(true);
    let hSuccess = (response) => {
      if (response?.Blob?.size !== 0 || response?.Blob?.type !== "") {
        // setIsLoading(false);
        setBlurLoading(false);
        setLoaderMessage("");
        const href = URL.createObjectURL(response);
        const link = document.createElement("a");

        link.href = href;
        link.setAttribute("download", `PCG Mass Change.xlsx`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(href);
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Success");

        setMessageDialogMessage(
          `Profit Center_Hierarchy_Mass Change.xlsx has been downloaded successfully`
        );

        setMessageDialogSeverity("success");
      } else {
        setBlurLoading(false);
        setLoaderMessage(false);
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage("Please try again.");
        //setMessageDialogMessage(`${error.message}`);
        setMessageDialogSeverity("danger");
      }
    };

    let hError = (error) => {
      // setIsLoading(false);
      setBlurLoading(false);
      setLoaderMessage("");
      if (error) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(`Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>
 `);
        setMessageDialogSeverity("danger");
      }
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/excel/downloadHierarchyNodeTemplateForChange`,
      "getblobfile",
      hSuccess,
      hError
    );
  };

  const handleChangeDownloadEmail = () => {
    // setLoaderMessage(
    //   "Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience."
    // );
    // setIsLoading(true);
    setBlurLoading(true);
    let hSuccess = (response) => {
      // setIsLoading(false);
      setBlurLoading(false);
      setLoaderMessage("");
      // const href = URL.createObjectURL(response);
      // const link = document.createElement("a");

      // link.href = href;
      // link.setAttribute("download", `PCG Mass Change.xlsx`);
      // document.body.appendChild(link);
      // link.click();
      // document.body.removeChild(link);
      // URL.revokeObjectURL(href);
      handleMessageDialogClickOpen();
      setMessageDialogTitle("Success");

      setMessageDialogMessage(
        `Download has been started. You will get the Excel file via email`
      );

      setMessageDialogSeverity("success");
    };

    let hError = (error) => {
      // setIsLoading(false);
      setBlurLoading(false);
      setLoaderMessage("");
      if (error) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(`Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>
 `);
        setMessageDialogSeverity("danger");
      }
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/excel/downloadHierarchyNodeTemplateForChangeInMail`,
      "get",
      hSuccess,
      hError
    );
  };
  const handleClickChange = (option, index) => {
    // dispatch(setHandleMassMode("Change"));
    if (index !== 0) {
      setSelectedIndexCreate(index);
      setOpenButtonCreate(false);
      if (index === 1) {
        // handleCreateSingleWithCopy();
        handleChangeMultiple();
      } else if (index === 2) {
        // handleCreateSingleWithoutCopy();
        // handleChangeDownload();
        setOpenDownloadChangeDialog(true);
      }
    }
  };

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };
  const handleCloseButtonChange = (event) => {
    if (
      anchorRefChange.current &&
      anchorRefChange.current.contains(event.target)
    ) {
      return;
    }
    // setOpenButton(false);
    setOpenButtonChange((prevOpen) => !prevOpen);
  };
  const optionsCreateMultiple = [
    "Create Multiple",
    "Upload Template ",
    "Download Template ",
  ];
  const optionsChangeMultiple = [
    "Change Multiple",
    "Upload Template ",
    "Download Template ",
  ];
  const steps = [
    "GENERAL INFORMATION",
    "HIERARCHY NODE",
    "ATTACHMENTS & COMMENTS",
  ];

  // const getStepContent = (step) => {
  //   switch (step) {
  //     case 0:
  //       return <GeneralInformationTabForProfitCenterET />;
  //     case 1:
  //       return [
  //         <>
  //           <Grid container>
  //             <Grid
  //               item
  //               md={12}
  //               sx={{
  //                 backgroundColor: "white",
  //                 maxHeight: "max-content",
  //                 height: "max-content",
  //                 borderRadius: "8px",
  //                 border: "1px solid #E0E0E0",
  //                 mt: 0.25,
  //                 boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
  //                 ...container_Padding,
  //                 // ...container_columnGap,
  //               }}
  //             >
  //               <Grid item md={10} sx={{ marginLeft: "40px", mb: 4 }}>
  //                 <Button
  //                   variant="contained"
  //                   size="small"
  //                   sx={{ ...button_Primary, mr: 1 }}
  //                 >
  //                   Same Level
  //                 </Button>

  //                 <Button
  //                   variant="contained"
  //                   size="small"
  //                   sx={{ ...button_Primary, mr: 1 }}

  //                   //   onClick={handleBack}
  //                 >
  //                   Lower Level
  //                 </Button>

  //                 <Button
  //                   variant="contained"
  //                   size="small"
  //                   sx={{ ...button_Primary, mr: 1 }}
  //                 >
  //                   Profit Center
  //                 </Button>
  //               </Grid>

  //               <Grid
  //                 container
  //                 display="flex"
  //                 flexDirection="row"
  //                 flexWrap="nowrap"
  //               >
  //                 <Grid item md={10} sx={{ marginLeft: "40px" }}>
  //                   {getTreeNodes(data)}

  //                   {showInputT && (
  //                     <div>
  //                       <input
  //                         type="text"
  //                         placeholder="Enter title"
  //                         onBlur={() => setShowInput(false)}
  //                         onKeyDown={(e) => {
  //                           if (e.key === "Enter") {
  //                             handleAddNodeForParent(e.target.value);
  //                           }
  //                         }}
  //                       />
  //                     </div>
  //                   )}

  //                   {!showInputT && (
  //                     <IconButton
  //                       color="primary"
  //                       aria-label="upload picture"
  //                       component="label"
  //                       sx={iconButton_SpacingSmall}
  //                     >
  //                       <ControlPointIcon
  //                         style={{
  //                           height: "0.6em",

  //                           width: "0.8em",

  //                           color: "#000000",
  //                         }}
  //                         onClick={() => setShowInput(true)}
  //                       />
  //                     </IconButton>
  //                   )}
  //                 </Grid>
  //               </Grid>
  //             </Grid>
  //           </Grid>
  //         </>,
  //       ];

  //     case 2:
  //       return (
  //         <>
  //           <Grid container>
  //             <Grid
  //               item
  //               md={12}
  //               sx={{
  //                 backgroundColor: "white",
  //                 maxHeight: "max-content",
  //                 height: "max-content",
  //                 borderRadius: "8px",
  //                 border: "1px solid #E0E0E0",
  //                 mt: 0.25,
  //                 boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
  //                 ...container_Padding,
  //                 // ...container_columnGap,
  //               }}
  //             >
  //               <Grid container>
  //                 <Typography
  //                   sx={{
  //                     fontSize: "12px",
  //                     fontWeight: "700",
  //                   }}
  //                 >
  //                   Additional Attachments
  //                 </Typography>
  //               </Grid>

  //               <Grid container>
  //                 <Grid item>
  //                   <ReusableAttachementAndComments
  //                     title="ProfitCenter"
  //                     useMetaData={false}
  //                     // artifactId={pcNumber}
  //                     artifactName="ProfitCenter"
  //                   />
  //                 </Grid>
  //               </Grid>
  //             </Grid>
  //           </Grid>
  //         </>
  //       );
  //     default:
  //       return "Unknown step";
  //   }
  // };

  const handleUpdate = (ref, data) => {
    switch (ref) {
      case "UPDATEMOVENODE":
        console.log("insidemodenode");
        updateMoveNodeList(data);
        break;

      case "RESTOREMOVENODE":
        updateRestoreMoveNode(data);
        break;

      case "REMOVEPCDATA":
        updateRemovePCData(data);
        break;

      case "REMOVENODEDATA":
        updateRemoveNodeData(data);
        break;

      case "UPDATEMOVENODE":
        updateMoveNodeList(data);
        break;

      case "STOREMOVETAGLIST":
        updateStoreMoveTagList(data);
        break;

      case "RESTOREMOVETAGLIST":
        updateRestoreMoveTag(data);
        break;

      case "CHANGEDESCRIPTION":
        updateChangedDescription(data);
        break;

      default:
        return null;
    }
  };

  function MinusSquare(props) {
    return (
      <SvgIcon fontSize="inherit" style={{ width: 14, height: 14 }} {...props}>
        {/* tslint:disable-next-line: max-line-length */}

        <path d="M22.047 22.074v0 0-20.147 0h-20.12v0 20.147 0h20.12zM22.047 24h-20.12q-.803 0-1.365-.562t-.562-1.365v-20.147q0-.776.562-1.351t1.365-.575h20.147q.776 0 1.351.575t.575 1.351v20.147q0 .803-.575 1.365t-1.378.562v0zM17.873 11.023h-11.826q-.375 0-.669.281t-.294.682v0q0 .401.294 .682t.669.281h11.826q.375 0 .669-.281t.294-.682v0q0-.401-.294-.682t-.669-.281z" />
      </SvgIcon>
    );
  }

  function PlusSquare(props) {
    return (
      <SvgIcon fontSize="inherit" style={{ width: 14, height: 14 }} {...props}>
        {/* tslint:disable-next-line: max-line-length */}

        <path d="M22.047 22.074v0 0-20.147 0h-20.12v0 20.147 0h20.12zM22.047 24h-20.12q-.803 0-1.365-.562t-.562-1.365v-20.147q0-.776.562-1.351t1.365-.575h20.147q.776 0 1.351.575t.575 1.351v20.147q0 .803-.575 1.365t-1.378.562v0zM17.873 12.977h-4.923v4.896q0 .401-.281.682t-.682.281v0q-.375 0-.669-.281t-.294-.682v-4.896h-4.923q-.401 0-.682-.294t-.281-.669v0q0-.401.281-.682t.682-.281h4.923v-4.896q0-.401.294-.682t.669-.281v0q.401 0 .682.281t.281.682v4.896h4.923q.401 0 .682.281t.281.682v0q0 .375-.281.669t-.682.294z" />
      </SvgIcon>
    );
  }

  function CloseSquare(props) {
    return (
      <SvgIcon
        className="close"
        fontSize="inherit"
        style={{ width: 14, height: 14 }}
        {...props}
      >
        {/* tslint:disable-next-line: max-line-length */}

        <path d="M17.485 17.512q-.281.281-.682.281t-.696-.268l-4.12-4.147-4.12 4.147q-.294.268-.696.268t-.682-.281-.281-.682.294-.669l4.12-4.147-4.12-4.147q-.294-.268-.294-.669t.281-.682.682-.281.696 .268l4.12 4.147 4.12-4.147q.294-.268.696-.268t.682.281 .281.669-.294.682l-4.12 4.147 4.12 4.147q.294.268 .294.669t-.281.682zM22.047 22.074v0 0-20.147 0h-20.12v0 20.147 0h20.12zM22.047 24h-20.12q-.803 0-1.365-.562t-.562-1.365v-20.147q0-.776.562-1.351t1.365-.575h20.147q.776 0 1.351.575t.575 1.351v20.147q0 .803-.575 1.365t-1.378.562v0z" />
      </SvgIcon>
    );
  }

  function TransitionComponent(props) {
    const style = useSpring({
      to: {
        opacity: props.in ? 1 : 0,

        transform: `translate3d(${props.in ? 0 : 20}px,0,0)`,
      },
    });

    return (
      <animated.div style={style}>
        <Collapse {...props} />
      </animated.div>
    );
  }

  const CustomTreeItem = React.forwardRef((props, ref) => (
    <TreeItem {...props} TransitionComponent={TransitionComponent} ref={ref} />
  ));

  const StyledTreeItem = styled(CustomTreeItem)(({ theme }) => ({
    [`& .${treeItemClasses.iconContainer}`]: {
      "& .close": {
        opacity: 0.3,
      },
    },

    [`& .${treeItemClasses.group}`]: {
      marginLeft: 15,

      paddingLeft: 18,

      borderLeft: `1px dashed ${alpha(theme.palette.text.primary, 0.4)}`,
    },
  }));

  const handleCloseInitialDialog = () => {
    setOpenInitialDialog(false);
    setScenarioState("create");
    // setControllingArea(null);
    setProfitCenterGroup(null);
    setProfitCenterGroupDescription(null);
    setIsValidationErrorNode(false);
    setIsValidationErrorDesc(false);
    setIsDuplicateDBDesc(false);
    setIsDuplicateNodeRequest(false);
  };
  const handleDownloadExcelDialogClose = () => {
    setOpenDownloadExcelDialog(false);
  };

  const navigateData = {
    controllingArea,
    profitCenterGroup,
    profitCenterGroupDescription,
    scenarioState,
  };
  console.log("nvd", navigateData);

  useEffect(() => {
    setControllingArea({ code: "ETCA", desc: "ET FAMILY CO AREA" });
    setProfitCenterGroup(null);
    setProfitCenterGroupDescription(null);
    setIsValidationErrorNode(false);
    setIsValidationErrorDesc(false);
    setIsDuplicateDBDesc(false);
    setIsDuplicateNodeRequest(false);
  }, [scenarioState]);

  useEffect(() => {
    if (navigateData?.scenarioState === "create") {
      const isDisabled =
        // !navigateData?.controllingArea?.code ||
        !navigateData?.profitCenterGroup?.code ||
        !navigateData?.profitCenterGroupDescription?.desc;
      setIsProceedCreateDisabled(isDisabled);
    } else {
      const isDisabled =
        // !navigateData?.controllingArea?.code ||
        !navigateData?.profitCenterGroup?.code;
      setIsProceedDisabled(isDisabled);
    }
  }, [
    navigateData?.profitCenterGroup?.code,
    navigateData?.profitCenterGroupDescription?.desc,
    navigateData?.scenarioState,
  ]);

  const handleInitialDialogSubmit = () => {
    if (navigateData?.scenarioState === "create") {
      var payload = {
        requestId: "",
        controllingArea: navigateData?.controllingArea?.code,
        hierarchyGrp: "",
        node: navigateData?.profitCenterGroup?.code,
      };
      const hSuccess = (data) => {
        setBlurLoading(false);
        console.log("dupli", data);

        if (
          data?.body?.PresentInHier === "X" ||
          data?.body?.PresentInCA === "X" ||
          data?.body?.isDbDuplicate
        ) {
          setIsValidationErrorNode(true);
          setIsValidationErrorDesc(false);
          setIsDuplicateDBDesc(false);
          console.log("CHECKERROR");
        } else {
          var payloadForDesc = {
            requestId: "",
            classValue: "0106",
            controllingArea: navigateData?.controllingArea?.code,
            desc: navigateData?.profitCenterGroupDescription?.desc,
          };
          const hSuccessDesc = (data) => {
            setBlurLoading(false);
            console.log("dupli", data);

            if (
              Object.keys(data.body).length != 0 &&
              data?.body?.isDbDuplicate === true
            ) {
              setIsValidationErrorNode(false);
              setIsValidationErrorDesc(false);
              setIsDuplicateDBDesc(true);
              console.log("CHECKERROR");
            } else if (Object.keys(data.body).length != 0) {
              setIsValidationErrorNode(false);
              setIsValidationErrorDesc(true);
              setIsDuplicateDBDesc(false);
              console.log("CHECKERROR");
            } else {
              navigate(
                "/masterDataCockpit/groupNode/hierarchyNodeProfitCenter/newHierarchyGroup",
                {
                  state: navigateData,
                }
              );
            }
          };
          const hErrorDesc = () => {};

          doAjax(
            `/${destination_ProfitCenter_Mass}/node/descDuplicacyCheckForPCG`,
            "post",
            hSuccessDesc,
            hErrorDesc,
            payloadForDesc
          );
        }
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        `/${destination_ProfitCenter_Mass}/node/nodeDuplicacyCheckForPCG`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      var payload = {
        requestId: "",
        controllingArea: navigateData?.controllingArea?.code,
        hierarchyGrp: "",
        node: navigateData?.profitCenterGroup?.code,
      };

      const hSuccess = (data) => {
        setBlurLoading(false);

        if (data?.body?.isDbDuplicate) {
          setIsDuplicateNodeRequest(true);
          console.log("CHECKERROR");
        } else {
          navigate(
            "/masterDataCockpit/groupNode/hierarchyNodeProfitCenter/newHierarchyGroup",
            {
              state: navigateData,
            }
          );
        }
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        `/${destination_ProfitCenter_Mass}/node/nodeDuplicacyCheckForPCG`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };

  const handleDownloadExcelDialogSubmit = () => {};

  const handleControllingArea = () => {};

  const handleTreeDataChange = (newData) => {
    console.log("Tree data changed:", newData);
    // You might want to update your state or API here
  };

  const [showInputT, setShowInput] = useState(false);

  const [showInputFields, setShowInputFields] = useState({});
  const [showInputFieldsForPC, setShowInputFieldsForPC] = useState({});
  const [editingNodeId, setEditingNodeId] = useState(null);
  const [newPcValues, setNewPcValues] = useState({});

  // const handleAddNodeForParent = (value) => {
  //   setData((prev) => [
  //     ...prev,

  //     { id: (prev.length + 1).toString(), pc: [], title: value, child: [] },
  //   ]);
  // };

  const addNode = (parentId, newNode, treeData) => {
    return treeData.map((item) => {
      if (item.title === parentId) {
        item.child.push(newNode);
      } else if (item.child.length > 0) {
        item.child = addNode(parentId, newNode, item.child);
      }

      return item;
    });
  };

  const handleAddNode = (parentId, newNodeTitle) => {
    console.log("parentid", parentId, newNodeTitle);

    if (newNodeTitle.trim() !== "") {
      const newNode = { title: newNodeTitle, child: [] };
      let parentIdStart = parentId.split("_")[0];
      console.log("setdata", data);
      let temp = data.map((element) => {
        console.log("outside", element);
        if (element.id.split("_")[0] == parentIdStart) {
          let newdata = addData(element, parentId, newNode);
          console.log("insideif", newdata);
          return newdata;
        }
        return element;
      });
      console.log("temp", temp);
      setData(temp);
      setShowInputFields({ ...showInputFields, [parentId]: false });
    }
  };

  const stringCheck = (str1, str2) => {
    console.log("str1", str1, str2);
    let i = 0,
      j = 0;
    while (i < str1.length && j < str2.length) {
      if (str1[i] != str2[j]) {
        return false;
      }
      i++;
      j++;
    }
    return true;
  };

  const addData = (dataArray, id, newNode) => {
    console.log("debugger", dataArray.id, dataArray, id);
    if (dataArray.id === id) {
      console.log("dataadding", newNode);
      dataArray.child.push({
        ...newNode,
        id: `${id}_${dataArray.child.length + 1}`,
      });
    } else if (!stringCheck(dataArray.id, id)) {
    } else {
      dataArray.child = dataArray.child.map((element) => {
        let temp = addData(element, id, newNode);
        return temp;
      });
    }

    return dataArray;
  };

  const handleAddPc = (nodeId) => {
    const newValue = newPcValues[nodeId];
    if (newValue) {
      // Find the node in the data array
      const updatedData = data.map((node) => {
        if (node.id === nodeId) {
          // Update the node's pc array with the new value
          return {
            ...node,
            pc: [...node.pc, newValue],
          };
        } else if (Array.isArray(node.child)) {
          // Recursively update child nodes
          return {
            ...node,
            child: updateChildNodes(node.child, nodeId, newValue),
          };
        }
        return node;
      });

      // Update the data state
      setData(updatedData);

      // Clear the input field
      setNewPcValues({ ...newPcValues, [nodeId]: "" });
    }
  };

  const updateChildNodes = (childNodes, nodeId, newValue) => {
    return childNodes.map((childNode) => {
      if (childNode.id === nodeId) {
        // Update the child node's pc array with the new value
        return {
          ...childNode,
          pc: [...childNode.pc, newValue],
        };
      } else if (Array.isArray(childNode.child)) {
        // Recursively update child nodes
        return {
          ...childNode,
          child: updateChildNodes(childNode.child, nodeId, newValue),
        };
      }
      return childNode;
    });
  };

  // const getAllNodeIds = (nodes) => {
  //   // debugger
  //   let ids = [];
  //   nodes?.map((node) => {
  //     ids.push(node.id);
  //     if (node.child) {
  //       ids = ids.concat(getAllNodeIds(node.child));
  //     }
  //   });
  //   console.log("nodeids", ids);
  //   return ids;
  // };

  // const allNodeIds = getAllNodeIds(initialNodeData);

  // let getTreeNodes = (data) => {
  //   let treeCount = 1;

  //   const createChildNodes = (list, parentId) => {
  //     return list?.map((item) => {
  //       const nodeId = item.id;
  //       const showInput = showInputFields[nodeId];
  //       const showInputForProfitCenter = showInputFields[nodeId];
  //       return (
  //         <StyledTreeItem
  //           key={nodeId}
  //           nodeId={nodeId}
  //           sx={{ color: "#180859" }}
  //           label={<p>{`${item.title} - ${item.description}`}</p>}
  //         >
  //           {item?.child?.[0] ? createChildNodes(item?.child, nodeId) : <></>}
  //           {item.tags.map((pc, index) => (
  //             <StyledTreeItem
  //               key={`${nodeId}-pc-${index}`}
  //               nodeId={`${nodeId}-pc-${index}`}
  //               label={<p>{pc}</p>}
  //               sx={{ color: "#348ceb" }}
  //             />
  //           ))}
  //           {showInput && (
  //             <div>
  //               <input
  //                 type="text"
  //                 placeholder="Enter title"
  //                 onBlur={() =>
  //                   setShowInputFields({ ...showInputFields, [nodeId]: false })
  //                 }
  //                 onKeyDown={(e) => {
  //                   if (e.key === "Enter") {
  //                     handleAddNode(nodeId, e.target.value);
  //                   }
  //                 }}
  //               />
  //             </div>
  //           )}

  //           {showInputForProfitCenter && (
  //             <div>
  //               <input
  //                 placeholder="Enter title"
  //                 // value={newPcValues[nodeId] || ""}
  //                 onChange={(e) =>
  //                   setNewPcValues({ ...newPcValues, [nodeId]: e.target.value })
  //                 }
  //                 onBlur={() =>
  //                   setShowInputFieldsForPC({
  //                     ...showInputFieldsForPC,
  //                     [nodeId]: false,
  //                   })
  //                 }
  //                 onKeyDown={(e) => {
  //                   if (e.key === "Enter") {
  //                     handleAddPc(nodeId, e.target.value);
  //                   }
  //                 }}
  //               />
  //             </div>
  //           )}

  //           <div style={{ display: "flex" }}>
  //             {/* {!showInput && (
  //               <IconButton
  //                 color="primary"
  //                 aria-label="upload picture"
  //                 component="label"
  //                 sx={iconButton_SpacingSmall}
  //               >
  //                 <ControlPointIcon
  //                   style={{
  //                     height: "0.6em",
  //                     width: "0.8em",
  //                     color: "#000000",
  //                   }}
  //                   onClick={() =>
  //                     // alert('test')

  //                     setShowInputFields({ ...showInputFields, [nodeId]: true })
  //                   }
  //                 />
  //               </IconButton>
  //             )} */}

  //             {/* {!showInput && (
  //               <IconButton
  //                 color="primary"
  //                 aria-label="upload picture"
  //                 component="label"
  //                 sx={iconButton_SpacingSmall}
  //               >
  //                 <PlusOneIcon
  //                   style={{
  //                     height: "0.6em",
  //                     width: "0.8em",

  //                     color: "#000000",
  //                   }}
  //                   onClick={() =>
  //                     setShowInputFieldsForPC({
  //                       ...showInputFieldsForPC,
  //                       [nodeId]: true,
  //                     })
  //                   }
  //                 />
  //               </IconButton>
  //             )} */}
  //           </div>
  //         </StyledTreeItem>
  //       );
  //     });
  //   };

  //   return (
  //     <TreeView
  //       aria-label="customized"
  //       defaultExpanded={allNodeIds}
  //       defaultCollapseIcon={<MinusSquare />}
  //       defaultExpandIcon={<PlusSquare />}
  //       defaultEndIcon={<CloseSquare />}
  //       sx={{ overflowX: "hidden" }}
  //     >
  //       {createChildNodes(initialNodeData, 1)}
  //     </TreeView>
  //   );
  // };

  const MuiTreeNode = ({ node }) => {
    const [isExpanded, setIsExpanded] = useState(false);

    const toggleExpand = () => {
      setIsExpanded(!isExpanded);
    };

    return (
      <div>
        <div onClick={toggleExpand}>
          {isExpanded ? <AiOutlineMinus /> : <AiOutlinePlus />}{" "}
          {/* Plus or minus icon based on expand state */}
          <span>{node.value}</span>
        </div>

        {isExpanded && node.children && (
          <div style={{ marginLeft: "20px" }}>
            {node.children?.map((child) => (
              <MuiTreeNode key={child.value} node={child} />
            ))}
          </div>
        )}
      </div>
    );
  };

  const updateNodeList = (updatedState) => {
    setNewNodeList(updatedState);
  };
  const updatePCList = (updatedState) => {
    setNewTagList(updatedState);
  };
  const updateDescList = (updatedState) => {
    setNewDescList(updatedState);
  };
  const updateReplaceNodeList = (updatedState) => {
    setNewReplaceNodeList(updatedState);
  };

  const updateReplaceTagList = (updatedState) => {
    setNewReplaceTagList(updatedState);
  };
  const updateTreeData = (updatedState) => {
    setData(updatedState);
  };

  const updateChangeDescList = (updatedState) => {
    setNewDescriptionAdded(updatedState);
  };

  const updatedNewNodeList = (updatedState) => {
    setNewAddedNode(updatedState);
  };

  const updateMoveNodeList = (updatedState) => {
    setMoveNodeList(updatedState);
  };
  const updateRestoreMoveNode = (updatedState) => {
    setRestoreMoveNode(updatedState);
  };

  const updateRemovePCData = (updatedState) => {
    setRemovePCData(updatedState);
  };
  const updateRemoveNodeData = (updatedState) => {
    setRemoveNodeData(updatedState);
  };

  const updateStoreMoveTagList = (updatedState) => {
    setStoreMoveTagList(updatedState);
  };
  const updateRestoreMoveTag = (updatedState) => {
    setRestoreMoveTagList(updatedState);
  };
  const updateChangedDescription = (updatedState) => {
    setChangeDescription(updatedState);
  };

  console.log("scenariostate", scenarioState);
  return (
    <div ref={ref_elementForExport}>
      <ReusableDialog
        dialogState={openMessageDialog}
        openReusableDialog={handleMessageDialogClickOpen}
        closeReusableDialog={handleMessageDialogClose}
        dialogTitle={messageDialogTitle}
        dialogMessage={messageDialogMessage}
        handleDialogConfirm={handleMessageDialogClose}
        dialogOkText={"OK"}
        // handleExtraButton={handleMessageDialogNavigate}
        dialogSeverity={messageDialogSeverity}
      />

      <ReusableBackDrop
        blurLoading={blurLoading}
        loaderMessage={loaderMessage}
      />

      {enableDocumentUpload && (
        <AttachmentUploadDialog
          artifactId=""
          artifactName=""
          setOpen={setEnableDocumentUpload}
          handleUpload={uploadExcel}
        />
      )}

      <Dialog
        hideBackdrop={false}
        elevation={2}
        PaperProps={{
          sx: { boxShadow: "none", width: 700 },
        }}
        open={openInitialDialog}
        onClose={handleCloseInitialDialog}
      >
        <DialogTitle
          sx={{
            justifyContent: "space-between",
            alignItems: "center",
            height: "max-content",
            padding: ".5rem",
            paddingLeft: "1rem",
            backgroundColor: "#EAE9FF40",
            // borderBottom: "1px solid grey",

            display: "flex",
          }}
        >
          <Typography variant="h6">{t("Please Select")}</Typography>

          <IconButton
            sx={{ width: "max-content" }}
            onClick={handleCloseInitialDialog}
            children={<CloseIcon />}
          />
        </DialogTitle>

        <DialogContent sx={{ padding: ".5rem 1rem" }}>
          <Stack>
            <Box sx={{ minWidth: 400 }}>
              <FormControl>
                <FormLabel
                  id="demo-radio-buttons-group-label"
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                  }}
                >
                  {" "}
                </FormLabel>

                <RadioGroup
                  aria-labelledby="demo-radio-buttons-group-label"
                  defaultValue={scenarioState}
                  name="radio-buttons-group"
                  row
                  onChange={(e) => setScenarioState(e.target.value)}
                >
                  <FormControlLabel
                    value="create"
                    control={<Radio />}
                    label="Create"
                  />
                  <FormControlLabel
                    value="change"
                    control={<Radio />}
                    label="Change"
                  />
                </RadioGroup>
              </FormControl>

              <Grid container spacing={1}>
                {scenarioState === "create" ? (
                  <>
                    <Grid
                      item
                      md={6}
                      sx={{ width: "100%", marginTop: ".5rem" }}
                    >
                      <Typography>
                        {t("Controlling Area")}
                        <span style={{ color: "red" }}>*</span>
                      </Typography>

                      <FormControl
                        fullWidth
                        sx={{ margin: ".5em 0px", minWidth: "250px" }}
                      >
                        <Autocomplete
                          sx={{ height: "31px" }}
                          fullWidth
                          size="small"
                          value={
                            controllingArea ?? {
                              code: "ETCA",
                              desc: "ET FAMILY CO AREA",
                            }
                          }
                          options={dropDownData?.COAREA ?? []}
                          disableClearable
                          onChange={(e, value) => {
                            setIsValidationErrorNode(false);
                            setIsValidationErrorDesc(false);
                            setControllingArea(value);
                          }}
                          getOptionLabel={(option) => {
                            if (option?.code)
                              return `${option?.code}-${option?.desc}` ?? "";
                            else return "";
                          }}
                          renderOption={(props, option) => (
                            <li {...props}>
                              <Typography style={{ fontSize: 12 }}>
                                {option?.desc ? (
                                  <>
                                    <strong>{option.code}</strong> -{" "}
                                    {option.desc}
                                  </>
                                ) : (
                                  <strong>{option?.code}</strong>
                                )}
                              </Typography>
                            </li>
                          )}
                          renderInput={(params) => (
                            <TextField
                              sx={{ fontSize: "12px !important" }}
                              {...params}
                              variant="outlined"
                              placeholder={t("SELECT CONTROLLING AREA")}

                              // error={controllingAreaValid}
                            />
                          )}
                        />
                      </FormControl>
                    </Grid>
                    <Grid
                      item
                      md={6}
                      sx={{ width: "100%", marginTop: ".5rem" }}
                    >
                      <Typography>
                        {t("Profit Center Group")}
                        <span style={{ color: "red" }}>*</span>
                      </Typography>

                      <FormControl
                        fullWidth
                        sx={{ margin: ".5em 0px", minWidth: "250px" }}
                      >
                        <TextField
                          sx={{ fontSize: "12px !important" }}
                          fullWidth
                          size="small"
                          value={profitCenterGroup?.code ?? ""}
                          inputProps={{ maxLength: 10 }}
                          onChange={(e, value) => {
                            setIsValidationErrorNode(false);
                            setIsValidationErrorDesc(false);
                            console.log("pctrgroup", e.target.value);
                            setProfitCenterGroup({
                              code: e.target.value
                                ?.replace(/[^a-zA-Z0-9_\/-]/g, "")
                                ?.toUpperCase(),
                              desc: "",
                            });
                          }}
                          placeholder={t("ENTER PROFIT CENTER GROUP")}
                        />
                      </FormControl>
                    </Grid>
                    <Grid
                      item
                      md={6}
                      sx={{ width: "100%", marginTop: ".5rem" }}
                    >
                      <Typography>
                        {t("Profit Center Group Description")}
                        <span style={{ color: "red" }}>*</span>
                      </Typography>

                      <FormControl
                        fullWidth
                        sx={{ margin: ".5em 0px", minWidth: "250px" }}
                      >
                        <TextField
                          sx={{ fontSize: "12px !important" }}
                          fullWidth
                          size="small"
                          value={profitCenterGroupDescription?.desc ?? ""}
                          inputProps={{ maxLength: 40 }}
                          onChange={(e, value) => {
                            setIsValidationErrorNode(false);
                            setIsValidationErrorDesc(false);
                            let inputValue = e.target.value;
                            if (inputValue.startsWith(" ")) {
                              inputValue = inputValue.trimStart();
                            }
                            const capitalizedValue = inputValue
                              .replace(/[^a-zA-Z0-9-&()#.'/$%, ]/g, "") // Allow only specific characters
                              .replace(/\s{2,}/g, " ") // Replace multiple spaces with a single space
                              .replace(/\s*([-&()#.'/$%,])\s*/g, "$1") // Remove spaces before and after special characters
                              .replace(/([-&()#.'/$%,])\s+/g, "$1") // Remove spaces **after** special characters
                              .trimStart()
                              ?.toUpperCase();

                            console.log("pctrgroup", e.target.value);
                            setProfitCenterGroupDescription({
                              code: "",
                              // desc: e.target.value.toUpperCase(),
                              desc: capitalizedValue,
                            });
                          }}
                          placeholder={t(
                            "ENTER PROFIT CENTER GROUP DESCRIPTION"
                          )}
                        />
                      </FormControl>
                    </Grid>
                  </>
                ) : (
                  <>
                    <Grid
                      item
                      md={6}
                      sx={{ width: "100%", marginTop: ".5rem" }}
                    >
                      <Typography>
                        {t("Controlling Area")}
                        <span style={{ color: "red" }}>*</span>
                      </Typography>

                      <FormControl
                        fullWidth
                        sx={{ margin: ".5em 0px", minWidth: "250px" }}
                      >
                        <Autocomplete
                          sx={{ height: "31px" }}
                          fullWidth
                          size="small"
                          value={
                            controllingArea ?? {
                              code: "ETCA",
                              desc: "ET FAMILY CO AREA",
                            }
                          }
                          options={dropDownData?.NewControllingArea ?? []}
                          disableClearable
                          // options={[{code:"ETCA", desc:"ET CO AREA"}]}
                          onChange={(e, value) => {
                            setControllingArea(value);
                            getProfitCenterGroup(value.code);
                          }}
                          getOptionLabel={(option) => {
                            if (option?.code)
                              return `${option?.code}-${option?.desc}` ?? "";
                            else return "";
                          }}
                          renderOption={(props, option) => (
                            <li {...props}>
                              <Typography style={{ fontSize: 12 }}>
                                {option?.desc ? (
                                  <>
                                    <strong>{option.code}</strong> -{" "}
                                    {option.desc}
                                  </>
                                ) : (
                                  <strong>{option?.code}</strong>
                                )}
                              </Typography>
                            </li>
                          )}
                          renderInput={(params) => (
                            <TextField
                              sx={{ fontSize: "12px !important" }}
                              {...params}
                              variant="outlined"
                              placeholder={t("SELECT CONTROLLING AREA")}

                              // error={controllingAreaValid}
                            />
                          )}
                        />
                      </FormControl>
                    </Grid>

                    <Grid
                      item
                      md={6}
                      sx={{ width: "100%", marginTop: ".5rem" }}
                    >
                      <Typography>
                        {t("Profit Center Group")}
                        <span style={{ color: "red" }}>*</span>
                      </Typography>

                      <FormControl
                        fullWidth
                        sx={{ margin: ".5em 0px", minWidth: "250px" }}
                      >
                        <Autocomplete
                          sx={{ height: "31px" }}
                          fullWidth
                          size="small"
                          onChange={(e, value, reason) => {
                            console.log("profitcentergroup", value);
                            if (reason === "clear") {
                              setProfitCenterGroup(null);
                              setProfitCenterGroupDescription(null);
                              return;
                            }
                            setIsDuplicateNodeRequest(false);
                            setProfitCenterGroup({
                              code: value.code,
                              desc: "",
                            });

                            setProfitCenterGroupDescription({
                              code: "",
                              desc: value.desc,
                            });
                          }}
                          options={dropDownData?.PRCTRGroup ?? []}
                          getOptionLabel={(option) => {
                            if (option?.code)
                              return `${option?.code}-${option?.desc}` ?? "";
                            else return "";
                          }}
                          //   value={pcSearchForm?.costCenterCategory}

                          renderOption={(props, option) => (
                            <li {...props}>
                              <Typography style={{ fontSize: 12 }}>
                                {option?.desc ? (
                                  <>
                                    <strong>{option.code}</strong> -{" "}
                                    {option.desc}
                                  </>
                                ) : (
                                  <strong>{option?.code}</strong>
                                )}
                              </Typography>
                            </li>
                          )}
                          renderInput={(params) => (
                            <TextField
                              sx={{ fontSize: "12px !important" }}
                              {...params}
                              variant="outlined"
                              placeholder={t("SELECT PROFIT CENTER GROUP")}

                              // error={controllingAreaValid}
                            />
                          )}
                        />
                      </FormControl>
                    </Grid>
                  </>
                )}
              </Grid>
            </Box>
          </Stack>
          {isValidationErrorNode ? (
            <Grid>
              <Typography style={{ color: "red" }}>
                {t(
                  "*This Node already exists in the selected Controlling Area"
                )}
              </Typography>
            </Grid>
          ) : (
            ""
          )}
          {isValidationErrorDesc ? (
            <Grid>
              <Typography style={{ color: "red" }}>
                {t(
                  "*This Description already exists for some node in selected Controlling Area"
                )}
              </Typography>
            </Grid>
          ) : (
            ""
          )}
          {isDuplicateNodeRequest ? (
            <Grid>
              <Typography style={{ color: "red" }}>
                {t("*There is an ongoing request for this Profit Center Group")}
              </Typography>
            </Grid>
          ) : (
            ""
          )}
          {isDuplicateDBDesc && scenarioState === "create" ? (
            <Grid>
              <Typography style={{ color: "red" }}>
                {t(
                  "*This Description already exists in some ongoing request in selected Controlling Area."
                )}
              </Typography>
            </Grid>
          ) : (
            ""
          )}
        </DialogContent>

        <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
          {/* <Button
                  sx={{ width: "max-content", textTransform: "capitalize" }}
                  onClick={handleCloseInitialDialog}
                >
                  Cancel
                </Button>

                <Button
                  className="button_primary--normal"
                  type="save"
                  onClick={handleInitialDialogSubmit}
                  variant="contained"
                >
                  Proceed
                </Button> */}
          {scenarioState === "create" ? (
            <>
              <Button
                sx={{
                  width: "max-content",
                  textTransform: "capitalize",
                }}
                onClick={handleCloseInitialDialog}
              >
                {t("Cancel")}
              </Button>

              <Button
                className="button_primary--normal"
                type="save"
                onClick={handleInitialDialogSubmit}
                variant="contained"
                disabled={isProceedCreateDisabled}
              >
                {t("Proceed")}
              </Button>
            </>
          ) : (
            <>
              <Button
                sx={{
                  width: "max-content",
                  textTransform: "capitalize",
                }}
                onClick={handleCloseInitialDialog}
              >
                {t("Cancel")}
              </Button>

              <Button
                className="button_primary--normal"
                type="save"
                onClick={handleInitialDialogSubmit}
                variant="contained"
                disabled={isProceedDisabled}
              >
                {t("Proceed")}
              </Button>
            </>
          )}
        </DialogActions>
      </Dialog>
      <Dialog
        hideBackdrop={false}
        elevation={2}
        PaperProps={{
          sx: { boxShadow: "none" },
        }}
        open={openDownloadExcelDialog}
        onClose={handleDownloadExcelDialogClose}
      >
        <DialogTitle
          sx={{
            justifyContent: "space-between",

            alignItems: "center",

            height: "max-content",

            padding: ".5rem",

            paddingLeft: "1rem",

            backgroundColor: "#EAE9FF40",

            // borderBottom: "1px solid grey",

            display: "flex",
          }}
        >
          <Typography variant="h6">Please Select</Typography>

          <IconButton
            sx={{ width: "max-content" }}
            onClick={handleDownloadExcelDialogClose}
            children={<CloseIcon />}
          />
        </DialogTitle>

        <DialogContent sx={{ padding: ".5rem 1rem" }}>
          <Stack>
            <Box sx={{ minWidth: 400 }}>
              <Grid container spacing={1}>
                <Grid item md={6} sx={{ width: "100%", marginTop: ".5rem" }}>
                  <Typography>
                    {t("Controlling Area")}
                    <span style={{ color: "red" }}>*</span>
                  </Typography>

                  <FormControl
                    fullWidth
                    sx={{ margin: ".5em 0px", minWidth: "250px" }}
                  >
                    <Autocomplete
                      sx={{ height: "31px" }}
                      fullWidth
                      size="small"
                      options={dropDownData?.COAREA ?? []}
                      onChange={(e, value) => {
                        setControllingArea(value);
                        getProfitCenterGroup(value.code);
                      }}
                      getOptionLabel={(option) => {
                        if (option?.code)
                          return `${option?.code}-${option?.desc}` ?? "";
                        else return "";
                      }}
                      renderOption={(props, option) => (
                        <li {...props}>
                          <Typography style={{ fontSize: 12 }}>
                            {option?.desc ? (
                              <>
                                <strong>{option.code}</strong> - {option.desc}
                              </>
                            ) : (
                              <strong>{option?.code}</strong>
                            )}
                          </Typography>
                        </li>
                      )}
                      renderInput={(params) => (
                        <TextField
                          sx={{ fontSize: "12px !important" }}
                          {...params}
                          variant="outlined"
                          placeholder={t("SELECT CONTROLLING AREA")}

                          // error={controllingAreaValid}
                        />
                      )}
                    />
                  </FormControl>
                </Grid>

                <Grid item md={6} sx={{ width: "100%", marginTop: ".5rem" }}>
                  <Typography>
                    {t("Profit Center Group")}
                    <span style={{ color: "red" }}>*</span>
                  </Typography>

                  <FormControl
                    fullWidth
                    sx={{ margin: ".5em 0px", minWidth: "250px" }}
                  >
                    <Autocomplete
                      sx={{ height: "31px" }}
                      fullWidth
                      size="small"
                      onChange={(e, value) => {
                        setProfitCenterGroup(value);
                      }}
                      options={dropDownData?.PRCTRGroup ?? []}
                      getOptionLabel={(option) => {
                        if (option?.code)
                          return `${option?.code}-${option?.desc}` ?? "";
                        else return "";
                      }}
                      //   value={pcSearchForm?.costCenterCategory}

                      renderOption={(props, option) => (
                        <li {...props}>
                          <Typography style={{ fontSize: 12 }}>
                            {option?.desc ? (
                              <>
                                <strong>{option.code}</strong> - {option.desc}
                              </>
                            ) : (
                              <strong>{option?.code}</strong>
                            )}
                          </Typography>
                        </li>
                      )}
                      renderInput={(params) => (
                        <TextField
                          sx={{ fontSize: "12px !important" }}
                          {...params}
                          variant="outlined"
                          placeholder={t("SELECT CONTROLLING AREA")}

                          // error={controllingAreaValid}
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
              </Grid>
            </Box>
          </Stack>
        </DialogContent>

        <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
          <Button
            sx={{ width: "max-content", textTransform: "capitalize" }}
            onClick={handleDownloadExcelDialogClose}
          >
            {t("Cancel")}
          </Button>

          <Button
            className="button_primary--normal"
            type="save"
            onClick={handleDownloadExcelDialogSubmit}
            variant="contained"
          >
            {t("Proceed")}
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog open={openDownloadDialog} onClose={handleDownloadDialogClose}>
        <DialogTitle
          sx={{
            justifyContent: "space-between",
            alignItems: "center",
            height: "max-content",
            padding: ".5rem",
            paddingLeft: "1rem",
            backgroundColor: "#EAE9FF40",
            // borderBottom: "1px solid grey",
            display: "flex",
          }}
        >
          <Typography
            variant="h6"
            gutterBottom
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            {t("Select Download Option")}
          </Typography>
        </DialogTitle>
        <DialogContent>
          <FormControl>
            <RadioGroup
              row
              aria-labelledby="demo-row-radio-buttons-group-label"
              name="row-radio-buttons-group"
              value={downloadType}
              onChange={handleDownloadTypeChange}
            >
              <NoMaxWidthTooltip
                arrow
                placement="bottom"
                title={
                  <span
                    style={{
                      whiteSpace: "nowrap", // Prevents line break
                      fontSize: "12px",
                      // maxWidth: "400px", // Optional width constraint
                      overflow: "hidden",
                      textOverflow: "ellipsis", // Adds ellipsis if overflow
                    }}
                  >
                    {t("Here Excel will be downloaded")}
                  </span>
                }
                // placement="right"
              >
                <FormControlLabel
                  value="systemGenerated"
                  control={<Radio />}
                  label="System-Generated"
                />
              </NoMaxWidthTooltip>

              <NoMaxWidthTooltip
                arrow
                placement="bottom"
                title={
                  <span
                    style={{
                      whiteSpace: "nowrap", // Prevents line break
                      fontSize: "12px",
                      // maxWidth: "400px", // Optional width constraint
                      overflow: "hidden",
                      textOverflow: "ellipsis", // Adds ellipsis if overflow
                    }}
                  >
                    {t("Here Excel will be sent to your email")}
                  </span>
                }
                // placement="right"
              >
                <FormControlLabel
                  value="mailGenerated"
                  control={<Radio />}
                  label="Mail-Generated"
                />
              </NoMaxWidthTooltip>
            </RadioGroup>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button variant="contained" onClick={onDownloadTypeChange}>
            {t("OK")}
          </Button>
        </DialogActions>
      </Dialog>

      {/* change multiple */}
      <Dialog
        open={openDownloadChangeDialog}
        onClose={handleDownloadChangeDialogClose}
      >
        <DialogTitle
          sx={{
            justifyContent: "space-between",
            alignItems: "center",
            height: "max-content",
            padding: ".5rem",
            paddingLeft: "1rem",
            backgroundColor: "#EAE9FF40",
            // borderBottom: "1px solid grey",
            display: "flex",
          }}
        >
          <Typography
            variant="h6"
            gutterBottom
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            {t("Select Download Option")}
          </Typography>
        </DialogTitle>
        <DialogContent>
          <FormControl>
            <RadioGroup
              row
              aria-labelledby="demo-row-radio-buttons-group-label"
              name="row-radio-buttons-group"
              value={downloadType}
              onChange={handleMultipleDownloadTypeChange}
            >
              <NoMaxWidthTooltip
                arrow
                placement="bottom"
                title={
                  <span
                    style={{
                      whiteSpace: "nowrap", // Prevents line break
                      fontSize: "12px",
                      // maxWidth: "400px", // Optional width constraint
                      overflow: "hidden",
                      textOverflow: "ellipsis", // Adds ellipsis if overflow
                    }}
                  >
                    {t("Here Excel will be downloaded")}
                  </span>
                }
                // placement="right"
              >
                <FormControlLabel
                  value="systemGenerated"
                  control={<Radio />}
                  label="System-Generated"
                />
              </NoMaxWidthTooltip>

              <NoMaxWidthTooltip
                arrow
                placement="bottom"
                title={
                  <span
                    style={{
                      whiteSpace: "nowrap", // Prevents line break
                      fontSize: "12px",
                      // maxWidth: "400px", // Optional width constraint
                      overflow: "hidden",
                      textOverflow: "ellipsis", // Adds ellipsis if overflow
                    }}
                  >
                    {t("Here Excel will be sent to your email")}
                  </span>
                }
                // placement="right"
              >
                <FormControlLabel
                  value="mailGenerated"
                  control={<Radio />}
                  label="Mail-Generated"
                />
              </NoMaxWidthTooltip>
            </RadioGroup>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button variant="contained" onClick={onMultipleDownloadTypeChange}>
            {t("OK")}
          </Button>
        </DialogActions>
      </Dialog>

      <div style={{ ...outermostContainer, backgroundColor: "#FAFCFF" }}>
        <Stack spacing={1}>
          <Grid container mt={0} sx={outermostContainer_Information}>
            <Grid item md={5}>
              <Typography variant="h3">
                <strong>{t("Profit Center Group")}</strong>
              </Typography>
              <Typography variant="body2" color="#777">
                {t("This view displays the selected Profit Center Hierarchy")}
              </Typography>
            </Grid>
          </Grid>

          <Grid container sx={container_filter}>
            <Grid item md={12}>
              <StyledAccordion defaultExpanded={true}>
                <StyledAccordionSummary
                  expandIcon={
                    <ExpandMoreIcon
                      sx={{ fontSize: "1.25rem", color: colors.primary.main }}
                    />
                  }
                  aria-controls="panel1a-content"
                  id="panel1a-header"
                >
                  <FilterListIcon
                    sx={{
                      fontSize: "1.25rem",
                      marginRight: 1,
                      color: colors.primary.main,
                    }}
                  />
                  <Typography
                    sx={{
                      fontSize: "0.875rem",
                      fontWeight: 600,
                      color: colors.primary.dark,
                    }}
                  >
                    {t("Search Profit Center Group")}
                  </Typography>
                </StyledAccordionSummary>
                <AccordionDetails sx={{ padding: "1rem 1rem 0.5rem" }}>
                  <Grid
                    container
                    rowSpacing={1}
                    spacing={2}
                    justifyContent="space-between"
                    alignItems="center"
                    // sx={{ marginBottom: "0.5rem" }}
                  >
                    <Grid
                      container
                      spacing={1}
                      sx={{ padding: "0rem 1rem 0.5rem" }}
                    >
                      <Grid item md={2}>
                        <LabelTypography sx={font_Small}>
                          {t("Controlling Area")}{" "}
                          <span style={{ color: colors?.error?.dark }}>*</span>
                        </LabelTypography>
                        <FormControl size="small" fullWidth>
                          <SingleSelectDropdown
                            options={dropDownData?.COAREA ?? []}
                            value={pcSearchForm?.controllingArea}
                            onChange={(value) => {
                              handleControllingAreaSearch(value);
                            }}
                            placeholder={t("SELECT CONTROLLING AREA")}
                            disabled={false}
                            minWidth="90%"
                            listWidth={210}
                          />
                        </FormControl>
                      </Grid>

                      <Grid item md={2}>
                        <LabelTypography sx={font_Small}>
                          {t("Profit Center Group")}{" "}
                          <span style={{ color: colors?.error?.dark }}>*</span>
                        </LabelTypography>
                        <FormControl size="small" fullWidth>
                          <SingleSelectDropdown
                            options={dropDownData?.PRCTRGroupFilter ?? []}
                            value={pcSearchForm?.profitCenterGroup}
                            onChange={(newValue) => {
                              handleProfitCenterGroupSearch(newValue);
                            }}
                            placeholder={t("SELECT PROFIT CENTER GROUP")}
                            disabled={false}
                            minWidth="90%"
                            listWidth={210}
                          />
                        </FormControl>
                      </Grid>
                    </Grid>
                  </Grid>
                  <ButtonContainer>
                    <ActionButton
                      variant="outlined"
                      size="small"
                      startIcon={<ClearIcon sx={{ fontSize: "1rem" }} />}
                      onClick={() => {
                        handleClear();
                      }}
                      // disabled={isPresetActive}
                      sx={{
                        borderColor: colors.primary.main,
                        color: colors.primary.main,
                      }}
                    >
                      {t("Clear")}
                    </ActionButton>

                    {/* MIGHT UNCOMMENT LATER */}
                    <Grid sx={{ ...button_Marginleft }}>
                      <ReusablePreset
                        moduleName={"MaterialMaster"}
                        handleSearch={getFilter}
                        // disabled={selectedRegion === "" || !selectedSalesOrg?.length || !selectedPlant?.length}
                        onPresetActiveChange={(isActive) =>
                          setIsPresetActive(isActive)
                        }
                        onClearPreset={handleClear}
                      />
                    </Grid>

                    <ActionButton
                      variant="contained"
                      size="small"
                      startIcon={<SearchIcon sx={{ fontSize: "1rem" }} />}
                      sx={{ ...button_Primary, ...button_Marginleft }}
                      // disabled={isPresetActive}
                      onClick={() => {
                        const missingFields = [];

                        // if (selectedRegion=="") missingFields.push("Region");
                        // if (!selectedSalesOrg?.length) missingFields.push("SalesOrg");
                        // if (!selectedPlant?.length) missingFields.push("Plant");

                        // if (missingFields.length > 0) {
                        //   setMessageDialogMessage(ERROR_MESSAGES.MANDATORY_FILTER_MD(missingFields.join(", ")));
                        //   setAlertType("error");
                        //   handleSnackBarOpen();
                        //   return;
                        // }

                        getFilter();
                      }}
                    >
                      {t("Search")}
                    </ActionButton>
                  </ButtonContainer>
                </AccordionDetails>
              </StyledAccordion>
            </Grid>
          </Grid>

          <>
            {initialNodeData.length > 0 && initialNodeData[0]?.label && (
              <Grid container>
                <Grid
                  item
                  md={12}
                  sx={{
                    backgroundColor: "white",
                    maxHeight: "max-content",
                    height: "max-content",
                    borderRadius: "8px",
                    border: "1px solid #E0E0E0",
                    mt: 0.25,
                    boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                    ...container_Padding,
                    // ...container_columnGap,
                  }}
                >
                  <Grid
                    container
                    display="flex"
                    flexDirection="row"
                    flexWrap="nowrap"
                  >
                    <Grid item md={12}>
                      {initialNodeData[0] ? (
                        <Grid
                          item
                          sx={{
                            display: "flex",
                            justifyContent: "space-between",
                          }}
                        ></Grid>
                      ) : (
                        ""
                      )}

                      <Grid>
                        <Typography
                          variant="body1"
                          fontWeight="bold"
                          justifyContent="flex-start"
                          marginBottom={2}
                        >
                          {t("Existing Structure in SAP")}
                        </Typography>
                      </Grid>

                      <ReusableHierarchyTree
                        initialRawTreeData={initialNodeData}
                        editmode={false}
                        object="Profit Center"
                      />
                      {showInputT && (
                        <div>
                          <input
                            type="text"
                            placeholder="Enter title"
                            onBlur={() => setShowInput(false)}
                            onKeyDown={(e) => {
                              if (e.key === "Enter") {
                                handleAddNodeForParent(e.target.value);
                              }
                            }}
                          />
                        </div>
                      )}
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            )}
          </>

          <Paper
            sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
            elevation={2}
          >
            {iwaAccessData?.length > 0 && (
              <Paper
                sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                elevation={2}
              >
                <BottomNavigation
                  className="container_BottomNav"
                  showLabels
                  sx={{
                    display: "flex",
                    justifyContent: "flex-end",
                    gap: 1,
                  }}
                >
                  <Button
                    size="small"
                    variant="contained"
                    onClick={() => {
                      navigate("/requestBench/createProfitCenterGroupRequest");
                      dispatch(setDisplayPayload({}));
                      dispatch(setRequestHeader({}));
                    }}
                  >
                    {t("Create Request")}
                  </Button>

                  <ButtonGroup
                    variant="contained"
                    ref={anchorRef}
                    aria-label="split button"
                  ></ButtonGroup>

                  {enableDocumentUpload && (
                    <AttachmentUploadDialog
                      artifactId=""
                      artifactName=""
                      setOpen={setEnableDocumentUpload}
                      handleUpload={uploadExcel}
                    />
                  )}
                </BottomNavigation>
              </Paper>
            )}
          </Paper>
        </Stack>
      </div>
    </div>
  );
};

export default ProfitCenterGroup;
