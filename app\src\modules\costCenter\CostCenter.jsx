import React, { useMemo } from "react";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DemoContainer } from "@mui/x-date-pickers/internals/demo";
import DeleteIcon from "@mui/icons-material/Delete";
import DownloadIcon from "@mui/icons-material/Download";
import CloseIcon from "@mui/icons-material/Close";
import {
  FolderSpecial,
  InfoOutlined,
  IosShare,
  Refresh,
  TuneOutlined,
} from "@mui/icons-material";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import {
  Button,
  Checkbox,
  Grid,
  Paper,
  IconButton,
  Typography,
  TextField,
  Box,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Popper,
  BottomNavigation,
  ListItemText,
  InputLabel,
  tooltipClasses,
  Card,
  CardContent,
  OutlinedInput,
  Autocomplete,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  ButtonGroup,
  ClickAwayListener,
  MenuList,
  Divider,
  FormControlLabel,
  FormGroup,
  Backdrop,
  CircularProgress,
  ToggleButtonGroup,
  ToggleButton,
  Radio,
  RadioGroup,
  FormLabel,
} from "@mui/material";

import moment from "moment/moment";
import { Stack } from "@mui/system";
import Select from "@mui/material/Select";
import { FormControl, MenuItem } from "@mui/material";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";

import ReusableDialog from "../../components/Common/ReusableDialog";

import { useRef } from "react";
import { useDispatch, useSelector } from "react-redux";

import styled from "@emotion/styled";

import {
  commonFilterClear,
  commonFilterUpdate,
} from "../../app/commonFilterSlice";
import { v4 as uuidv4 } from "uuid";
import WarningIcon from "@mui/icons-material/Warning";
import {
  button_Marginleft,
  button_Outlined,
  button_Primary,
  container_filter,
  container_table,
  font_Small,
  iconButton_SpacingSmall,
  outerContainer_Information,
  outermostContainer,
  outermostContainer_Information,
} from "../../components/Common/commonStyles";
import { LocalizationProvider } from "@mui/x-date-pickers";
import {
  destination_CostCenter_Mass,
  destination_IDM,

} from "../../destinationVariables";
import { doAjax } from "../../components/Common/fetchService";
import ClearIcon from "@mui/icons-material/Clear";
import ReusableTable from "../../components/Common//ReusableTable";

import { setDropDown } from "../../app/dropDownDataSlice";
import { Fragment } from "react";

// import "./masterDataCockpit.css";
import {
  clearCostCenter,
  setCostCenterAddressTab,
  setCostCenterBasicDataTab,
  setCostCenterCommunicationTab,
  setCostCenterControlTab,
  setCostCenterHistoryTab,
  setCostCenterTemplatesTab,
  setHandleMassMode,
  setSelectedHeader,
  setSelectedHeaderTab,
  setSingleCostCenterPayload,
  clearSingleCostCenter,
} from "../../app/costCenterTabsSlice";
import { clearCostCenterPayload } from "../../app/costCenterTabSliceET";
import AttachmentUploadDialog from "../../components/Common/AttachmentUploadDialog";
import ReusableIcon from "../../components/Common/ReusableIcon";
import {
  clearArtifactId,
} from "../../app/initialDataSlice";
import {  saveExcel } from "../../functions";
import { clearTaskData, setTaskData } from "../../app/userManagementSlice";

import ReusablePreset from "../../components/Common/ReusablePresetFilter";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";

import DateRange from "../../components/Common/DateRangePicker";
import { clearPayload } from "../../app/editPayloadSlice";
import ReusableBackDrop from "../../components/Common/ReusableBackDrop";
import FilterListIcon from "@mui/icons-material/FilterList";
import { colors } from "@constant/colors";
import { REQUEST_TYPE } from "@constant/enum";

const CostCenter = () => {
  const [snackbar, setSnackbar] = useState(false);
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [isDropDownLoading, setIsDropDownLoading] = useState(false); // State for loader

  const [openSearchDialog, setOpenSearchDialog] = useState(false);
  const [searchDialogTitle, setSearchDialogTitle] = useState("");
  const [searchDialogMessage, setSearchDialogMessage] = useState();

  const [pcInputValue, setPcInputValue] = useState("");
  const [descInputValue, setDescInputValue] = useState("");
  const [streetInputValue, setStreetInputValue] = useState("");
  const [ccNameInputValue, setCcNameInputValue] = useState("");
  const [userInputValue, setUserInputValue] = useState("");
  const [personInputValue, setPersonInputValue] = useState("");
  const [ccInputValue, setCcInputValue] = useState("");
  const [locationInputValue, setLocationInputValue] = useState("");
  const [createdByInputValue, setCreatedByInputValue] = useState("");
  const [selectedValues, setSelectedValues] = useState({});
  const [selectedCreatedBy, setselectedCreatedBy] = useState([]);
  const [selectedProfitCenter, setselectedProfitCenter] = useState([]);
  const [selectedCostCenter, setselectedCostCenter] = useState([]);
  const [selectedPersonResponsible, setselectedPersonResponsible] = useState(
    []
  );
  const [selectedStreet, setselectedStreet] = useState([]);
  const [selectedLocation, setselectedLocation] = useState([]);
  const [selectedCostCenterName, setselectedCostCenterName] = useState([]);
  const [selectedUserResponsible, setselectedUserResponsible] = useState([]);

  const [selectedPresetValues, setSelectedPresetValues] = useState({});
  const [selectedPresetCreatedBy, setselectedPresetCreatedBy] = useState([]);
  const [selectedPresetProfitCenter, setselectedPresetProfitCenter] = useState(
    []
  );
  const [selectedPresetCostCenter, setselectedPresetCostCenter] = useState([]);
  const [selectedPresetPersonResponsible, setselectedPresetPersonResponsible] =
    useState([]);
  const [selectedPresetStreet, setselectedPresetStreet] = useState([]);
  const [selectedPresetLocation, setselectedPresetLocation] = useState([]);
  const [selectedPresetCostCenterName, setselectedPresetCostCenterName] =
    useState([]);
  const [selectedPresetUserResponsible, setselectedPresetUserResponsible] =
    useState([]);
  const [selectedPresetComanyCode, setselectedPresetComanyCode] = useState([]);
  const [selectedPresetDescription, setselectedPresetDescription] = useState(
    []
  );
  const [selectedPresetCCcategory, setselectedPresetCCcategory] = useState([]);
  const [selectedDateRange, setselectedDateRange] = useState([]);

  const [messageDialogSeverity, setMessageDialogSeverity] = useState("");
  const appSettings = useSelector((state) => state.appSettings["Format"]);
  const rmSearchForm = useSelector((state) => state.commonFilter["CostCenter"]);
  const [fileSizeError, setFileSizeError] = useState(false);
  const [selectedComanyCode, setselectedComanyCode] = useState([]);
  const [selectedDescription, setselectedDescription] = useState([]);
  const [selectedCCcategory, setselectedCCcategory] = useState([]);
  const [docList, setDocList] = useState([]);
  const dropdownData = useSelector((state) => state.AllDropDown.dropDown);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [count, setCount] = useState(0);
  const [page, setPage] = useState(0);
  console.log(page, "pagee");
  const [pageSize, setPageSize] = useState(10);
  const [ruleData, setRuleData] = useState([]);
  const [downloadMultiple, setDownloadMultiple] = useState(false);
  const [duplicateFieldsData, setDuplicateFieldsData] = useState([]);
  //const [compCodeData, setCompCodeData] = useState([]);
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const [skip, setSkip] = useState(0);
  const [costCenterLength, setCostCenterLength] = useState("");
  const [dataListCOA, setDataListCOA] = useState([]);
  const [selectedListItemsCOA, setSelectedListItemsCOA] = useState([]);
  const [alignment, setAlignment] = useState("ALL OTHER CHANGES");
  const [openSelectColumnDialog, setOpenSelectColumnDialog] = useState(false);
  const [filteredRuleData, setFilteredRuleData] = useState(false);
  const [filteredRuleDataMass, setFilteredRuleDataMass] = useState(false);
  const [openDownloadDialog, setOpenDownloadDialog] = useState(false);
  const [openDownloadChangeDialog, setOpenDownloadChangeDialog] =
    useState(false);
  const [downloadType, setDownloadType] = useState("systemGenerated");
  const [requiredArrayDetailsMass, setRequiredArrayDetailsMass] = useState([]);

  const memoizedCCValue = useMemo(() => {
    if (selectedCostCenter.length > 0) return selectedCostCenter;
    else if (selectedPresetCostCenter.length > 0)
      return selectedPresetCostCenter;
    else return [];
  }, [selectedCostCenter, selectedPresetCostCenter]);

  const memoizedPCValue = useMemo(() => {
    if (selectedProfitCenter.length > 0) return selectedProfitCenter;
    else if (selectedPresetProfitCenter.length > 0)
      return selectedPresetProfitCenter;
    else return [];
  }, [selectedProfitCenter, selectedPresetProfitCenter]);

  const memoizedLDValue = useMemo(() => {
    if (selectedDescription.length > 0) return selectedDescription;
    else if (selectedPresetDescription.length > 0)
      return selectedPresetDescription;
    else return [];
  }, [selectedDescription, selectedPresetDescription]);

  const memoizedSDValue = useMemo(() => {
    if (selectedCostCenterName.length > 0) return selectedCostCenterName;
    else if (selectedPresetCostCenterName.length > 0)
      return selectedPresetCostCenterName;
    else return [];
  }, [selectedCostCenterName, selectedPresetCostCenterName]);

  const memoizedPRValue = useMemo(() => {
    if (selectedPersonResponsible.length > 0) return selectedPersonResponsible;
    else if (selectedPresetPersonResponsible.length > 0)
      return selectedPresetPersonResponsible;
    else return [];
  }, [selectedPersonResponsible, selectedPresetPersonResponsible]);

  const memoizedURValue = useMemo(() => {
    if (selectedUserResponsible.length > 0) return selectedUserResponsible;
    else if (selectedPresetUserResponsible.length > 0)
      return selectedPresetUserResponsible;
    else return [];
  }, [selectedUserResponsible, selectedPresetUserResponsible]);

  const memoizedStreetValue = useMemo(() => {
    if (selectedStreet.length > 0) return selectedStreet;
    else if (selectedPresetStreet.length > 0) return selectedPresetStreet;
    else return [];
  }, [selectedStreet, selectedPresetStreet]);

  const memoizedLocationValue = useMemo(() => {
    if (selectedLocation.length > 0) return selectedLocation;
    else if (selectedPresetLocation.length > 0) return selectedPresetLocation;
    else return [];
  }, [selectedLocation, selectedPresetLocation]);

  const memoizedCreatedByValue = useMemo(() => {
    if (selectedCreatedBy.length > 0) return selectedCreatedBy;
    else if (selectedPresetCreatedBy.length > 0) return selectedPresetCreatedBy;
    else return [];
  }, [selectedCreatedBy, selectedPresetCreatedBy]);

  const isInitialRender = React.useRef(true);
  const [
    buttonDisabledForSingleWithoutCopy,
    setButtonDisabledForSingleWithoutCopy,
  ] = useState(true);
  const [newCostCenterValid, setNewCostCenterValid] = useState(false);
  const [newCostCenterValidWithCopy, setNewCostCenterValidWithCopy] =
    useState(false);
  const [newCompanyCodeValid, setNewCompanyCodeValid] = useState(false);
  const [newControllingAreavalied, setNewControllingAreaValied] =
    useState(false);
  const [selectedMassChangeRowData, setSelectedMassChangeRowData] = useState(
    []
  );
  const [cascadingDropDownData, setCascadingDropDownData] = useState([]);
  const initialScreenData = useSelector(
    (state) => state.costCenter.singleCCPayload
  );
  const buttonsIDM = useSelector((state) => state?.profitCenter?.buttonsIDM);
  // let userData = useSelector((state) => state?.userManagement?.userData);
  // let iwaAccessData = useSelector(
  //   (state) => state?.userManagement?.entitiesAndActivities?.["Return Order"]
  // );

  const StyledAccordion = styled(Accordion)(({ theme }) => ({
    marginTop: "0px !important",
    border: `1px solid ${colors.primary.border}`,
    borderRadius: "8px",
    boxShadow: "0 2px 4px rgba(0,0,0,0.05)",
    "&:not(:last-child)": {
      borderBottom: 0,
    },
    "&:before": {
      display: "none",
    },
  }));

  const StyledAccordionSummary = styled(AccordionSummary)(({ theme }) => ({
    minHeight: "2rem !important",
    margin: "0px !important",
    backgroundColor: colors.primary.ultraLight,
    borderRadius: "8px 8px 0 0",
    transition: "all 0.2s ease-in-out",
    "&:hover": {
      backgroundColor: `${colors.primary.light}20`,
    },
  }));

  const LabelTypography = styled(Typography)({
    fontSize: "0.75rem",
    color: colors.primary.dark,
    marginBottom: "0.25rem",
    fontWeight: 500,
  });
  const ActionButton = styled(Button)({
    borderRadius: "4px",
    padding: "4px 12px",
    textTransform: "none",
    fontSize: "0.875rem",
  });
  const ButtonContainer = styled(Grid)({
    display: "flex",
    justifyContent: "flex-end",
    paddingRight: "0.75rem",
    paddingBottom: "0.75rem",
    paddingTop: "0rem",
    gap: "0.5rem",
  });

  console.log("buttonsIDM", buttonsIDM);
  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };
  let iwaAccessData = useSelector(
    (state) =>
      state?.userManagement?.entitiesAndActivities?.["Display Material"]
  );
  let userData = useSelector((state) => state?.userManagement?.userData);
  const handlePageSizeChange = (event) => {
    const newPageSize = event.target.value;
    setPageSize(newPageSize);
    setPage(0);
    setSkip(0);
  };
  const ITEM_HEIGHT = 48;
  const ITEM_PADDING_TOP = 8;
  const MenuProps = {
    PaperProps: {
      style: {
        maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
        width: 250,
      },
    },
  };

  const handleOnClick = (costCenterNumber) => {
    // setViewDetailpage(true);
    //     dispatch(setHistoryPath({url:window.location.pathname, module:"po workbench"}));

    navigate(
      "/masterDataCockpit/materialMaster/displayMaterialDetail/" +
        materialNumber
    );
  };

  useEffect(() => {
    Object.keys(selectedValues).forEach((option) => {
      const tempSelected = selectedValues[option]
        ?.map((item) => item?.code)
        .join("$^$");
      let tempFilterData = {
        ...rmSearchForm,
        [option]: tempSelected,
      };

      dispatch(
        commonFilterUpdate({
          module: "CostCenter",
          filterData: tempFilterData,
        })
      );
    });
  }, [selectedValues]);

  useEffect(() => {
    var tempCompanyCode = selectedComanyCode
      .map((item) => item?.code)
      .join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      "Company Code": tempCompanyCode,
    };
    dispatch(
      commonFilterUpdate({
        module: "CostCenter",
        filterData: tempFilterData,
      })
    );
  }, [selectedComanyCode]);

  useEffect(() => {
    var tempCreatedBy = selectedCreatedBy.map((item) => item?.code).join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      createdBy: tempCreatedBy,
    };
    dispatch(
      commonFilterUpdate({
        module: "CostCenter",
        filterData: tempFilterData,
      })
    );
  }, [selectedCreatedBy]);

  useEffect(() => {
    var tempLocation = selectedLocation.map((item) => item?.code).join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      location: tempLocation,
    };
    dispatch(
      commonFilterUpdate({
        module: "CostCenter",
        filterData: tempFilterData,
      })
    );
  }, [selectedLocation]);

  useEffect(() => {
    var tempCostCenterName = selectedCostCenterName
      .map((item) => item?.code)
      .join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      costCenterName: tempCostCenterName,
    };
    dispatch(
      commonFilterUpdate({
        module: "CostCenter",
        filterData: tempFilterData,
      })
    );
  }, [selectedCostCenterName]);

  useEffect(() => {
    var tempStreet = selectedStreet.map((item) => item?.code).join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      street: tempStreet,
    };
    dispatch(
      commonFilterUpdate({
        module: "CostCenter",
        filterData: tempFilterData,
      })
    );
  }, [selectedStreet]);

  useEffect(() => {
    var tempPC = selectedProfitCenter.map((item) => item?.code).join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      "Profit Center": tempPC,
    };
    dispatch(
      commonFilterUpdate({
        module: "CostCenter",
        filterData: tempFilterData,
      })
    );
  }, [selectedProfitCenter]);

  useEffect(() => {
    var tempCC = selectedCostCenter.map((item) => item?.code).join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      "Cost Center": tempCC,
    };
    dispatch(
      commonFilterUpdate({
        module: "CostCenter",
        filterData: tempFilterData,
      })
    );
  }, [selectedCostCenter]);

  useEffect(() => {
    var tempUserResponsible = selectedUserResponsible
      .map((item) => item?.code)
      .join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      "User Responsible": tempUserResponsible,
    };
    dispatch(
      commonFilterUpdate({
        module: "CostCenter",
        filterData: tempFilterData,
      })
    );
  }, [selectedUserResponsible]);

  useEffect(() => {
    var tempPersonResponsible = selectedPersonResponsible
      .map((item) => item?.code)
      .join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      personResponsible: tempPersonResponsible,
    };
    dispatch(
      commonFilterUpdate({
        module: "CostCenter",
        filterData: tempFilterData,
      })
    );
  }, [selectedPersonResponsible]);

  useEffect(() => {
    var tempCostCenterCategory = selectedCCcategory
      .map((item) => item?.code)
      .join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      "Cost Center Category": tempCostCenterCategory,
    };
    dispatch(
      commonFilterUpdate({
        module: "CostCenter",
        filterData: tempFilterData,
      })
    );
  }, [selectedCCcategory]);

  useEffect(() => {
    var tempDescription = selectedDescription
      .map((item) => item?.code)
      .join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      description: tempDescription,
    };
    dispatch(
      commonFilterUpdate({
        module: "CostCenter",
        filterData: tempFilterData,
      })
    );
  }, [selectedDescription]);

  // const updateuserIdInDispatch = () => {
  //   let tempFilterData = {
  //     ...rmSearchForm,
  //     compCodeUserId: compCodeData,
  //   };
  //   dispatch(
  //     commonFilterUpdate({
  //       module: "CostCenter",
  //       filterData: tempFilterData,
  //     })
  //   );
  // };

  // const fetchCompCode = (data) => {

  //   const hSuccess = (response) => {
  //     if (response.body && response.body.length > 0) {
  //       const updatedCompCodeData = response.body;
  //       setCompCodeData(updatedCompCodeData);

  //       getCCData(skip+1000, response.body[0]);
  //   };
  // }

  //   const hError = (error) => {
  //     console.error(error);
  //   };

  //   doAjax(
  //     `/${destination_CostCenter_Mass}/data/getUserIdFromCompCodeCC?controllingArea=${data?.ControllingArea}`,
  //     "get",
  //     hSuccess,
  //     hError
  //   );
  // };
  // useEffect(() => {
  //   fetchCompCode(initialScreenData);
  // }, [initialScreenData?.ControllingArea]);

  // useEffect(() => {
  //   // if ( (parseInt(page) + 1) * parseInt(pageSize) >= parseInt(skip) + 1000) {
  //     console.log("Pagination useEffect triggered");

  //     getCCData(skip + 1000);
  //     // setSkip((prev) => prev + 1000);
  //   // }
  // }, []);

  // useEffect(() => {
  //   if(page!==0){
  //     if ((parseInt(page) + 1) * parseInt(pageSize) >= parseInt(skip) + 100) {
  //       getFilterBasedOnPagination();
  //       // setSkip((prev) => prev + 500);
  //     }
  //   }
  // }, [page]);
  useEffect(() => {
    if (page * pageSize >= rmDataRows?.length) {
      getFilterBasedOnPagination();
      // setSkip((prev) => prev + 500);
    }
  }, [page, pageSize]);

  useEffect(() => {
    getFilter(skip + 1000);
  }, []);

  var newDate = new Date();
  var validToDate = new Date("December 31, 9999 01:15:00");
  // validToDate.setFullYear(("July 21, 1983 01:15:00"));
  const [Status_ServiceReqForm, setStatus_ServiceReqForm] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [timerId, setTimerId] = useState(null);
  const [tableLoading, setTableLoading] = useState(false);
  const [value, setValue] = useState("1");
  const ariaLabel = { "aria-label": "description" };
  const [rmDataRows, setRmDataRows] = useState([]);
  const [tableData, setTableData] = useState([...rmDataRows]);
  console.log("rmDataRows", rmDataRows);
  console.log("tableData", tableData);
  const [UserName, setUserName] = React.useState("");
  const [openSnackBaraccept, setOpenSnackBaraccept] = useState(false);
  const [confirmingid, setConfirmingid] = useState("");
  const [materialNumber, setMaterialNumber] = useState("");
  const [confirmStatus, setConfirmStatus] = useState(true);
  const [messageDialogExtra, setMessageDialogExtra] = useState(false);
  const [messageDialogOK, setMessageDialogOK] = useState(true);
  const [companyCodeSet, setCompanyCodeSet] = useState([]);
  const [plantCodeSet, setPlantCodeSet] = useState([]);
  const [vendorDetailsSet, setVendorDetailsSet] = useState([]);
  const [taskstatusSet, setTasksttusSet] = useState([]);
  const [disableButton, setDisableButton] = useState(true);
  const [selectedRow, setSelectedRow] = useState([]);
  const [selectedDetails, setSelectedDetails] = useState([]);
  const [downloadError, setdownloadError] = useState(false);
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [displayedFields, setDisplayedFields] = useState([]);
  const [filterFieldData, setFilterFieldData] = useState({});
  const [matType, setMatType] = useState([]);
  const [matGroup, setMatGroup] = useState([]);
  const [viewDetailpage, setViewDetailpage] = useState(false);
  const [matNumber, setMatNumber] = useState([]);
  const [dynamicOptions, setDynamicOptions] = useState({});
  const [plantForWarehouse, setPlantForWarehouse] = useState([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogOpenCreate, setDialogOpenCreate] = useState(false);
  const [fullWidth, setFullWidth] = useState(true);
  const [maxWidth, setMaxWidth] = useState("sm");
  const [enableDocumentUpload, setEnableDocumentUpload] = useState(false);
  const [successMsg, setsuccessMsg] = useState(false);
  const [newCostCenterName, setNewCostCenterName] = useState("");
  const [newCompanyCode, setNewComapnyCode] = useState("");
  // const [newCombinedCostcenter, setNewCombinedCostcenter] = useState("");
  const [newValidFromDate, setNewValidFromDate] = useState(newDate);
  const [newValidToDate, setNewValidToDate] = useState(validToDate);
  const [newControllingArea, setNewControllingArea] = useState(
    initialScreenData?.ControllingArea
  );
  const [newControllingAreaCopyFrom, setNewControllingAreaCopyFrom] =
    useState("");
  const [newCostCenterCopyFrom, setNewCostCenterCopyFrom] = useState("");
  const [newCostCenter, setNewCostCenter] = useState("");
  const [isValidationError, setIsValidationError] = useState(false);
  const [isValidationErrorwithCopy, setIsValidationErrorwithCopy] =
    useState(false);
  console.log("newCompanyCode", newCompanyCode);
  const [checkValidationCostCenter, setCheckValidationCostCenter] =
    useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  // const [handleMassMode, setHandleMassMode] = useState("");
  const [openButton, setOpenButton] = useState(false);
  const [openButtonCreateSingle, setOpenButtonCreateSingle] = useState(false);
  const anchorRef = React.useRef(null);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [openButtonChange, setOpenButtonChange] = useState(false);
  const [openButtonCreate, setOpenButtonCreate] = useState(false);
  const anchorRefChange = React.useRef(null);
  const anchorRefCreate = React.useRef(null);
  const [selectedIndexChange, setSelectedIndexChange] = useState(0);
  const [selectedIndexCreate, setSelectedIndexCreate] = useState(0);
  const [isCheckboxSelected, setIsCheckboxSelected] = useState(true);
  const [selectedListItems, setSelectedListItems] = useState([]);
  const [isAnyFieldEmpty, setIsAnyFieldEmpty] = useState(false);

  const [inputValue, setInputValue] = useState("");
  console.log("inputValue", inputValue);

  // useEffect(() => setTableData([...rmDataRows]), [rmDataRows]);

  const [dataListAllOtherChanges, setDataListAllOtherChanges] = useState([]);
  const [dataListBlockNames, setDataListBlockNames] = useState([]);
  const [dataListTemporaryBlockNames, setDataListTemporaryBlockNames] =
    useState([]);
  const [dataListProfitCenterChange, setDataListProfitCenterChange] = useState(
    []
  );
  const [fieldSelectionFromIdm, setFieldselectionFromIdm] = useState([]);
  const [dataListAllOtherChangesSelected, setDataListAllOtherChangesSelected] =
    useState([]);
  const [selectedTab, setSelectedTab] = useState("ALL OTHER CHANGES");
  const [dataListBlockNamesSelected, setDataListBlockNamesSelected] = useState(
    []
  );
  const [
    dataListTemporaryBlockNamesSelected,
    setDataListTemporaryBlockNamesSelected,
  ] = useState([]);
  const [
    dataListProfitCenterChangeSelected,
    setDataListProfitCenterChangeSelected,
  ] = useState([]);
  const [ccAvailability, setCCAvailabilty] = useState("yes");

  console.log(
    dataListAllOtherChanges,
    dataListBlockNames,
    dataListTemporaryBlockNames,
    dataListProfitCenterChange,
    "allselectionData"
  );

  const [isAnyFieldEmptyForSingleCopy, setIsAnyFieldEmptyForSingleCopy] =
    useState(false);
  const handleMassModeCC = useSelector(
    (state) => state.costCenter.handleMassMode
  );

  const options = ["Create Multiple", "Upload Template ", "Download Template "];
  const optionsCreateSingle = ["Create Single", "With Copy", "Without Copy"];
  const optionsChange = [
    "Change Multiple",
    "Upload Template",
    "Download Template",
  ];

  console.log(rmSearchForm, "rmSearchForm=======");
  const formcontroller_SearchBar = useSelector(
    (state) => state.commonSearchBar["CostCenter"]
  );
  const dropDownData = useSelector((state) => state?.AllDropDown?.dropDown);
  //   const dropDownData = {
  //     ETCCSearchData: [

  //         {
  //             "code": "1010",
  //             "desc": "1010"

  //         },
  //         {
  //             "code": "2010",
  //             "desc": "2010",

  //         },
  //         {
  //             "code": "2015",
  //             "desc": "2010",

  //         },
  //         {
  //             "code": "2020",
  //             "desc": "2010",

  //         },
  //         {
  //             "code": "2021",
  //             "desc": "2010",

  //         },
  //         {
  //             "code": "2030",

  //         },
  //         {
  //             "code": "2040",

  //         },
  //         {
  //             "code": "1010",
  //             "desc": "1010"

  //         },
  //         {
  //             "code": "2010",
  //             "desc": "2010",

  //         },
  //         {
  //             "code": "2015",
  //             "desc": "2010",

  //         },
  //         {
  //             "code": "2020",
  //             "desc": "2010",

  //         },
  //         {
  //             "code": "2021",
  //             "desc": "2010",

  //         },
  //         {
  //             "code": "2030",

  //         },
  //         {
  //             "code": "2040",

  //         },
  //         {
  //         "code": "1010",
  //         "desc": "1010"

  //     },
  //     {
  //         "code": "2010",
  //         "desc": "2010",

  //     },
  //     {
  //         "code": "2015",
  //         "desc": "2010",

  //     },
  //     {
  //         "code": "2020",
  //         "desc": "2010",

  //     },
  //     {
  //         "code": "2021",
  //         "desc": "2010",

  //     },
  //     {
  //         "code": "2030",

  //     },
  //     {
  //         "code": "2040",

  //     },
  //     {
  //       "code": "1010",
  //       "desc": "1010"

  //   },
  //   {
  //       "code": "2010",
  //       "desc": "2010",

  //   },
  //   {
  //       "code": "2015",
  //       "desc": "2010",

  //   },
  //   {
  //       "code": "2020",
  //       "desc": "2010",

  //   },
  //   {
  //       "code": "2021",
  //       "desc": "2010",

  //   },
  //   {
  //       "code": "2030",

  //   },
  //   {
  //       "code": "2040",

  //   },
  //   {
  //     "code": "1010",
  //     "desc": "1010"

  // },
  // {
  //     "code": "2010",
  //     "desc": "2010",

  // },
  // {
  //     "code": "2015",
  //     "desc": "2010",

  // },
  // {
  //     "code": "2020",
  //     "desc": "2010",

  // },
  // {
  //     "code": "2021",
  //     "desc": "2010",

  // },
  // {
  //     "code": "2030",

  // },
  // {
  //     "code": "2040",

  // },
  // {
  //   "code": "1010",
  //   "desc": "1010"

  // },
  // {
  //   "code": "2010",
  //   "desc": "2010",

  // },
  // {
  //   "code": "2015",
  //   "desc": "2010",

  // },
  // {
  //   "code": "2020",
  //   "desc": "2010",

  // },
  // {
  //   "code": "2021",
  //   "desc": "2010",

  // },
  // {
  //   "code": "2030",

  // },
  // {
  //   "code": "2040",

  // },
  // {
  //   "code": "1010",
  //   "desc": "1010"

  // },
  // {
  //   "code": "2010",
  //   "desc": "2010",

  // },
  // {
  //   "code": "2015",
  //   "desc": "2010",

  // },
  // {
  //   "code": "2020",
  //   "desc": "2010",

  // },
  // {
  //   "code": "2021",
  //   "desc": "2010",

  // },
  // {
  //   "code": "2030",

  // },
  // {
  //   "code": "2040",

  // },
  // {
  //   "code": "1010",
  //   "desc": "1010"

  // },
  // {
  //   "code": "2010",
  //   "desc": "2010",

  // },
  // {
  //   "code": "2015",
  //   "desc": "2010",

  // },
  // {
  //   "code": "2020",
  //   "desc": "2010",

  // },
  // {
  //   "code": "2021",
  //   "desc": "2010",

  // },
  // {
  //   "code": "2030",

  // },
  // {
  //   "code": "2040",

  // },
  // {
  //   "code": "1010",
  //   "desc": "1010"

  // },
  // {
  //   "code": "2010",
  //   "desc": "2010",

  // },
  // {
  //   "code": "2015",
  //   "desc": "2010",

  // },
  // {
  //   "code": "2020",
  //   "desc": "2010",

  // },
  // {
  //   "code": "2021",
  //   "desc": "2010",

  // },
  // {
  //   "code": "2030",

  // },
  // {
  //   "code": "2040",

  // },
  // {
  //   "code": "1010",
  //   "desc": "1010"

  // },
  // {
  //   "code": "2010",
  //   "desc": "2010",

  // },
  // {
  //   "code": "2015",
  //   "desc": "2010",

  // },
  // {
  //   "code": "2020",
  //   "desc": "2010",

  // },
  // {
  //   "code": "2021",
  //   "desc": "2010",

  // },
  // {
  //   "code": "2030",

  // },
  // {
  //   "code": "2040",

  // }
  //     ],
  //     ETUserResponsible: [

  //         {
  //             "code": "1010",

  //         },
  //         {
  //             "code": "2010",

  //         },
  //         {
  //             "code": "2015",

  //         },
  //         {
  //             "code": "2020",

  //         },
  //         {
  //             "code": "2021",

  //         },
  //         {
  //             "code": "2030",

  //         },
  //         {
  //             "code": "2040",

  //         }
  //     ],
  //     CompanyCode: [

  //         {
  //             "code": "1010",

  //         },
  //         {
  //             "code": "2010",

  //         },
  //         {
  //             "code": "2015",

  //         },
  //         {
  //             "code": "2020",

  //         },
  //         {
  //             "code": "2021",

  //         },
  //         {
  //             "code": "2030",

  //         },
  //         {
  //             "code": "2040",

  //         }
  //     ]
  //   };

  const handleDialogClickOpen = () => {
    setDialogOpen(true);
  };
  const handleDialogClickOpenWithCopy = () => {
    setDialogOpenCreate(true);
  };
  const handleChange = (event, newAlignment) => {
    setAlignment(newAlignment);
    if (newAlignment === "ALL OTHER CHANGES") {
      setSelectedTab("ALL OTHER CHANGES");
    } else if (newAlignment === "BLOCK") {
      setSelectedTab("BLOCK");
    } else if (newAlignment === "PROFIT CENTER CHANGE") {
      setSelectedTab("PROFIT CENTER CHANGE");
    } else if (newAlignment === "TEMPORARY BLOCK/UNBLOCK") {
      setSelectedTab("TEMPORARY BLOCK/UNBLOCK");
    }
  };

  const NoMaxWidthTooltip = styled(({ className, ...props }) => (
    <Tooltip {...props} classes={{ popper: className }} />
  ))({
    [`& .${tooltipClasses.tooltip}`]: {
      maxWidth: "none",
    },
  });

  const handleSelectionListItem = (selectedItem) => {
    console.log("selectedListItems", selectedListItems);
    console.log("ruleData", ruleData);
    const { templateName } = selectedItem;

    // Find all items with the same templateName
    const itemsToSelect = ruleData.filter(
      (item) => item.templateName === templateName
    );

    // Update selectedListItems to include all items with this templateName
    setSelectedListItems((prevSelected) => {
      console.log("prevSelected", prevSelected);
      const newSelected = [...prevSelected];

      // Toggle selection for all items with the same templateName
      itemsToSelect.forEach((item) => {
        const itemIndex = newSelected.findIndex(
          (selectedItem) => selectedItem.id === item.id
        );
        if (itemIndex === -1) {
          newSelected.push(item);
        } else {
          newSelected.splice(itemIndex, 1); // Deselect if already selected
        }
      });

      return newSelected;
    });
  };

  const handleSelectionAllOtherChanges = (item) => {
    //chiranjit
    //console.log(item,"item===========")
    const selectedIndex = dataListAllOtherChangesSelected.findIndex(
      (selectedItem) => selectedItem.id === item.id
    );
    console.log(selectedIndex, "selectedIndex");
    let newSelected = [];
    if (selectedIndex === -1) {
      newSelected = [...dataListAllOtherChangesSelected, item];
    } else if (selectedIndex === 0) {
      newSelected = dataListAllOtherChangesSelected.slice(1);
    } else if (selectedIndex === dataListAllOtherChangesSelected.length - 1) {
      newSelected = dataListAllOtherChangesSelected.slice(0, -1);
    } else if (selectedIndex > 0) {
      newSelected = [
        ...dataListAllOtherChangesSelected.slice(0, selectedIndex),
        ...dataListAllOtherChangesSelected.slice(selectedIndex + 1),
      ];
    }
    console.log(newSelected, "newSelected=====");

    setDataListAllOtherChangesSelected(newSelected);
  };
  const handleSelectionBlock = (item) => {
    //chiranjit
    //console.log(item,"item===========")
    const selectedIndex = dataListBlockNamesSelected.findIndex(
      (selectedItem) => selectedItem.id === item.id
    );
    console.log(selectedIndex, "selectedIndex");
    let newSelected = [];
    if (selectedIndex === -1) {
      newSelected = [...dataListBlockNamesSelected, item];
    } else if (selectedIndex === 0) {
      newSelected = dataListBlockNamesSelected.slice(1);
    } else if (selectedIndex === dataListBlockNamesSelected.length - 1) {
      newSelected = dataListBlockNamesSelected.slice(0, -1);
    } else if (selectedIndex > 0) {
      newSelected = [
        ...dataListBlockNamesSelected.slice(0, selectedIndex),
        ...dataListBlockNamesSelected.slice(selectedIndex + 1),
      ];
    }
    console.log(newSelected, "newSelected=====");

    setDataListBlockNamesSelected(newSelected);
  };
  const handleSelectionProfitCenterChange = (item) => {
    //chiranjit
    //console.log(item,"item===========")
    const selectedIndex = dataListProfitCenterChangeSelected.findIndex(
      (selectedItem) => selectedItem.id === item.id
    );
    console.log(selectedIndex, "selectedIndex");
    let newSelected = [];
    if (selectedIndex === -1) {
      newSelected = [...dataListProfitCenterChangeSelected, item];
    } else if (selectedIndex === 0) {
      newSelected = dataListProfitCenterChangeSelected.slice(1);
    } else if (
      selectedIndex ===
      dataListProfitCenterChangeSelected.length - 1
    ) {
      newSelected = dataListProfitCenterChangeSelected.slice(0, -1);
    } else if (selectedIndex > 0) {
      newSelected = [
        ...dataListProfitCenterChangeSelected.slice(0, selectedIndex),
        ...dataListProfitCenterChangeSelected.slice(selectedIndex + 1),
      ];
    }
    console.log(newSelected, "newSelected=====");

    setDataListProfitCenterChangeSelected(newSelected);
  };
  const handleSelectionTemporaryBlockChange = (item) => {
    //chiranjit
    //console.log(item,"item===========")
    const selectedIndex = dataListTemporaryBlockNamesSelected.findIndex(
      (selectedItem) => selectedItem.id === item.id
    );
    console.log(selectedIndex, "selectedIndex");
    let newSelected = [];
    if (selectedIndex === -1) {
      newSelected = [...dataListTemporaryBlockNamesSelected, item];
    } else if (selectedIndex === 0) {
      newSelected = dataListTemporaryBlockNamesSelected.slice(1);
    } else if (
      selectedIndex ===
      dataListTemporaryBlockNamesSelected.length - 1
    ) {
      newSelected = dataListTemporaryBlockNamesSelected.slice(0, -1);
    } else if (selectedIndex > 0) {
      newSelected = [
        ...dataListTemporaryBlockNamesSelected.slice(0, selectedIndex),
        ...dataListTemporaryBlockNamesSelected.slice(selectedIndex + 1),
      ];
    }
    console.log(newSelected, "newSelected=====");

    setDataListTemporaryBlockNamesSelected(newSelected);
  };
  const handleSelectAll = () => {
    if (!selectAll && alignment === "ALL OTHER CHANGES") {
      setDataListAllOtherChangesSelected(dataListAllOtherChanges);
    } else if (selectAll && alignment === "ALL OTHER CHANGES") {
      setDataListAllOtherChangesSelected([]);
    } else if (!selectAll && alignment === "PROFIT CENTER CHANGE") {
      setDataListProfitCenterChangeSelected(dataListProfitCenterChange);
    } else if (selectAll && alignment === "PROFIT CENTER CHANGE") {
      setDataListProfitCenterChangeSelected([]);
    } else if (!selectAll && alignment === "TEMPORARY BLOCK/UNBLOCK") {
      setDataListTemporaryBlockNamesSelected(dataListTemporaryBlockNames);
    } else if (selectAll && alignment === "TEMPORARY BLOCK/UNBLOCK") {
      setDataListTemporaryBlockNamesSelected([]);
    } else if (!selectAll && alignment === "BLOCK") {
      setDataListBlockNamesSelected(dataListBlockNames);
    } else {
      setDataListBlockNamesSelected([]);
    }
    setSelectAll(!selectAll);
  };
  console.log(
    "selectedListItems",
    dataListAllOtherChanges,
    dataListProfitCenterChange,
    dataListBlockNames,
    dataListTemporaryBlockNames
  );
  // Group items by templateName to display unique templateName values
  const groupedItems = {};
  ruleData.forEach((item) => {
    if (!groupedItems[item.templateName]) {
      groupedItems[item.templateName] = item;
    }
  });

  const handleSelectedColumn = () => {
    
    setIsLoading(true);
    setIsCheckboxSelected(false);
    handleChangeDownload();
  };
  const sendNewCostCenterData = {
    companyCode: { newCompanyCode },
    costCenterName: { newCostCenterName },
    controllingAreaData: initialScreenData?.ControllingArea,
    controllingAreaDataCopy: { newControllingAreaCopyFrom },
    // combinedCostCenter:{newCombinedCostcenter},
    //costCenterDataCopy:{newCostCenterCopyFrom},
    costCenter: { newCostCenter },
    // costCenter: "TUK1-PR43",
    validFromDate: { newValidFromDate },
    validToDate: { newValidToDate },
  };

  // Preset Function Start
  const PresetMethod = () => {
    let tempFilterData = {
      costCenterName: rmSearchForm?.costCenterName ?? "",
      costCenter: "",
      controllingArea: rmSearchForm?.controllingArea?.code ?? "",
      companyCode: rmSearchForm?.companyCode?.code ?? "",
      profitCenter: rmSearchForm?.profitCenter?.code ?? "",
      hierarchyArea: rmSearchForm?.hierarchyArea?.code ?? "",
      costCenterCategory: rmSearchForm?.costCenterCategory?.code ?? "",
      createdBy: "",
      fromDate: "",
      toDate: "",
      personResponsible: filterFieldData?.["Person Responsible"] ?? "",
      businessArea: filterFieldData?.["Business Area"]?.code ?? "",
      functionalArea: filterFieldData?.["Functional Area"]?.code ?? "",
    };
    dispatch(
      commonFilterUpdate({
        module: "CostCenter",
        filterData: tempFilterData,
      })
    );
  };
  const PresetObj = [
    { name: "CostCenterName", value: "TUK1" },
    // { name: "company", value: planningSearchForm?.companyCode },
    // { name: "supplier", value: planningSearchForm?.vendorNo },
    // { name: "purchasingGroup", value: planningSearchForm?.purchGrp },
    // { name: "status", value: planningSearchForm?.planningSheetStatus },
    // {
    //   name: "toDate",
    //   value:
    //     moment(planningSearchForm?.taskCreationDate[1]).format("YYYY-MM-DD") +
    //     "T00:00:00",
    // },
    // {
    //   name: "fromDate",
    //   value:
    //     moment(planningSearchForm?.taskCreationDate[0]).format("YYYY-MM-DD") +
    //     "T00:00:00",
    // },
  ];
  // Preset Function End

  const duplicateCheck = () => {
    let selectedCompanyCode =
      sendNewCostCenterData?.companyCode?.newCompanyCode?.code;
    let selectedCostCenterName =
      sendNewCostCenterData?.costCenterName?.newCostCenterName;

    if (ccAvailability === "no") {
      if (newCompanyCode?.code === undefined || newCompanyCode?.code === "") {
        setNewCostCenterValid(false);
        setIsValidationError(true);
        setIsAnyFieldEmpty(true);

        return;
      } else {
        setIsValidationError(false);
      }

      setIsLoading(true);

      navigate("/masterDataCockpit/costCenter/newSingleCostCenter", {
        state: sendNewCostCenterData,
      });
    } else {
      if (
        newCompanyCode?.code === undefined ||
        newCompanyCode?.code === "" ||
        newCostCenterName === undefined ||
        newCostCenterName === ""
      ) {
        setNewCostCenterValid(false);
        setIsValidationError(true);
        setIsAnyFieldEmpty(true);

        return;
      } else {
        if (newCostCenterName.length !== 6) {
          setNewCostCenterValid(true);
          setIsValidationError(false);
          return;
        } else {
          setNewCostCenterValid(false);
        }
        setIsValidationError(false);
      }

      let result = initialScreenData?.ControllingArea.concat(
        "$$",
        selectedCompanyCode,
        selectedCostCenterName
      );

      setIsLoading(true);
      const hSuccess = (data) => {
        setIsLoading(false);

        if (data.body.length > 0) {
          setCheckValidationCostCenter(true);
        } else {
          navigate("/masterDataCockpit/costCenter/newSingleCostCenter", {
            state: sendNewCostCenterData,
          });
        }
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        `/${destination_CostCenter_Mass}/alter/fetchCoAreaCCDupliChk?ctrlAreaCCToCheck=${result}`,
        "get",
        hSuccess,
        hError
      );
    }
  };
  const duplicateCheckWithCopy = () => {
    if (
      newControllingArea?.code === undefined ||
      newControllingArea?.code === "" ||
      newCompanyCode?.code === undefined ||
      newCompanyCode?.code === "" ||
      newCostCenterName === undefined ||
      newCostCenterName === "" ||
      newControllingAreaCopyFrom?.code === undefined ||
      newControllingAreaCopyFrom?.code === "" ||
      newCostCenter?.code === undefined ||
      newCostCenter?.code === ""
    ) {
      setNewCostCenterValidWithCopy(false);
      setIsValidationErrorwithCopy(true);
      return;
    } else {
      if (newCostCenterName.length !== 6) {
        setNewCostCenterValidWithCopy(true);
        setIsValidationErrorwithCopy(false);
        return;
        //duplicateCheck()
      } else {
        setNewCostCenterValidWithCopy(false);
      }
      setIsValidationErrorwithCopy(false);
      // setIsAnyFieldEmpty(false)
    }
    //console.log(newControllingAreaCopyFrom,"newControllingAreaCopyFrom")
    let selectedControllingArea =
      sendNewCostCenterData?.controllingAreaData?.newControllingArea.code;
    let selectedCompanyCode =
      sendNewCostCenterData?.companyCode?.newCompanyCode.code;
    let selectedCostCenterName =
      sendNewCostCenterData?.costCenterName?.newCostCenterName;

    let result = selectedControllingArea.concat(
      "$$",
      selectedCompanyCode,
      selectedCostCenterName
    );

    console.log(
      sendNewCostCenterData?.costCenter?.newCostCenter,
      "sendNewCostCenterData=========="
    );
    console.log(
      sendNewCostCenterData?.costCenterName?.newCostCenterName,
      "sendNewCostCenterData=========="
    );
    setIsLoading(true);
    const hSuccess = (data) => {
      setIsLoading(false);
      console.log("dupli", data);
      if (data.body.length > 0) {
        setCheckValidationCostCenter(true);
      } else {
        navigate(
          `/masterDataCockpit/costCenter/displayCopyCostCenter/${sendNewCostCenterData?.costCenter?.newCostCenter?.code}`,
          {
            state: sendNewCostCenterData,
          }
        );
      }
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/alter/fetchCoAreaCCDupliChk?ctrlAreaCCToCheck=${result}`,
      "get",
      hSuccess,
      hError
    );
  };
  const handleDialogProceed = () => {
    //   let selectedCompanyCode =
    //   sendNewCostCenterData?.companyCode?.newCompanyCode?.code;
    // let selectedCostCenterName =
    //   sendNewCostCenterData?.costCenterName?.newCostCenterName;
    //   if(!ccAvailability){
    //     navigate("/masterDataCockpit/costCenter/newSingleCostCenter", {
    //       state: sendNewCostCenterData,
    //     });
    //   }else{
    duplicateCheck();
    // }
  };
  const handleDialogProceedWithCopy = () => {
    duplicateCheckWithCopy();
  };

  //console.log(newCostCenterValid,"NewCostCenterValid===========")

  //const check

  const handleCostCenterAvailability = (event) => {
    setCCAvailabilty(event?.target?.value);
  };
  const handleDialogClose = () => {
    setDialogOpen(false);
    setIsValidationError(false);
    setButtonDisabledForSingleWithoutCopy(true);
    setNewCostCenterValid(false);
    setNewCompanyCodeValid(false);
    setNewControllingArea("");
    setNewComapnyCode("");
    setNewCostCenterName("");
    setCheckValidationCostCenter(false);
  };
  const handleDialogCloseCreate = () => {
    setIsValidationError(false);
    setIsValidationErrorwithCopy(false);
    setNewCostCenterValidWithCopy(false);
    setNewControllingArea("");
    setNewComapnyCode("");
    setNewCostCenterName("");
    setNewControllingAreaCopyFrom("");
    setNewCostCenterCopyFrom("");
    setDialogOpenCreate(false);
    setCheckValidationCostCenter(false);
  };
  const handleDialogCloseCreateSingle = (event) => {
    if (
      anchorRefCreate.current &&
      anchorRefCreate.current.contains(event.target)
    ) {
      return;
    }
    //setDialogOpenCreate(false);
    setOpenButtonCreate((prevOpen) => !prevOpen);
  };
  const handleCostCenterName = (e, value) => {
    if (true) {
      var tempCostCenterName = value;

      let tempFilterData = {
        ...rmSearchForm,
        costCenterName: tempCostCenterName,
      };
      dispatch(
        commonFilterUpdate({
          module: "CostCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleStreet = (e, value) => {
    if (true) {
      var tempStreet = value;

      let tempFilterData = {
        ...rmSearchForm,
        street: tempStreet,
      };
      dispatch(
        commonFilterUpdate({
          module: "CostCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleLocation = (e, value) => {
    if (true) {
      var tempLocation = value;

      let tempFilterData = {
        ...rmSearchForm,
        location: tempLocation,
      };
      dispatch(
        commonFilterUpdate({
          module: "CostCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleCreatedBy = (e, value) => {
    if (true) {
      var tempCreatedBy = value;

      let tempFilterData = {
        ...rmSearchForm,
        createdBy: tempCreatedBy,
      };
      dispatch(
        commonFilterUpdate({
          module: "CostCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleLookupChange = (newValue, option) => {
    if (true) {
      var tempOption = option;

      let tempFilterData = {
        ...rmSearchForm,
        [tempOption]: newValue,
      };

      console.log("tempfilterdata", tempFilterData);
      dispatch(
        commonFilterUpdate({
          module: "CostCenter",
          filterData: tempFilterData,
        })
      );
    }
    // if (option==="FERC Indicator") {
    //   var tempData = newValue;
    //   let tempFilterData = {
    //     ...rmSearchForm,
    //     fercIndicator: tempData,
    //   };
    //   dispatch(
    //     commonFilterUpdate({
    //       module: "CostCenter",
    //       filterData: tempFilterData,
    //     })
    //   );
    // } else if (option==="Profit Center") {
    //   var tempData = newValue;
    //   let tempFilterData = {
    //     ...rmSearchForm,
    //     profitCenter: tempData,
    //   };
    //   dispatch(
    //     commonFilterUpdate({
    //       module: "CostCenter",
    //       filterData: tempFilterData,
    //     })
    //   );
    // }else if (option==="Country/Reg") {
    //   var tempData = newValue;
    //   let tempFilterData = {
    //     ...rmSearchForm,
    //     country: tempData,
    //   };
    //   dispatch(
    //     commonFilterUpdate({
    //       module: "CostCenter",
    //       filterData: tempFilterData,
    //     })
    //   );
    // }else if (option==="Functional Area") {
    //   var tempData = newValue;
    //   let tempFilterData = {
    //     ...rmSearchForm,
    //     country: tempData,
    //   };
    //   dispatch(
    //     commonFilterUpdate({
    //       module: "CostCenter",
    //       filterData: tempFilterData,
    //     })
    //   );
    // }

    console.log("newValue", newValue);
    console.log("option", option);
  };
  const handleCreatedOn = (e) => {
    if (e.target.value !== null) {
      var tempCreatedOn = e.target.value;

      let tempFilterData = {
        ...rmSearchForm,
        createdOn: tempCreatedOn,
      };
      dispatch(
        commonFilterUpdate({
          module: "CostCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleDescription = (e, value) => {
    if (true) {
      var tempDescription = value;

      let tempFilterData = {
        ...rmSearchForm,
        description: tempDescription,
      };
      dispatch(
        commonFilterUpdate({
          module: "CostCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handlePersonResponsible = (e, value) => {
    if (true) {
      // var tempPersonResponsible = e.target.value;
      var tempPersonResponsible = value;

      let tempFilterData = {
        ...rmSearchForm,
        personResponsible: tempPersonResponsible,
      };
      dispatch(
        commonFilterUpdate({
          module: "CostCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleCostCenter = (e) => {
    if (e.target.value !== null) {
      var tempCostCenter = e.target.value;

      let tempFilterData = {
        ...rmSearchForm,
        costCenter: tempCostCenter,
      };
      dispatch(
        commonFilterUpdate({
          module: "CostCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleInputChange = (e) => {
    const value = e.target.value;
    setInputValue(value);

    let tempFilterData = {
      ...rmSearchForm,
      costCenterString: value,
    };

    dispatch(
      commonFilterUpdate({
        module: "CostCenter",
        filterData: tempFilterData,
      })
    );
  };

  const handleSelectAllOptions = (option) => {
    if (selectedValues[option]?.length === dynamicOptions[option]?.length) {
      setSelectedValues((prev) => ({
        ...prev,
        [option]: [],
      }));
      setSelectedPresetValues((prev) => ({
        ...prev,
        [option]: [],
      }));
    } else {
      setSelectedValues((prev) => ({
        ...prev,
        [option]: dynamicOptions[option] ?? [],
      }));
    }
  };

  const handleSelectAllCompanyCodes = () => {
    if (selectedComanyCode.length === dropDownData.CompanyCode.length) {
      setselectedComanyCode([]);
      setselectedPresetComanyCode([]);
    } else {
      setselectedComanyCode(dropDownData?.CompanyCode);
    }
  };

  const handleSelectAllCreatedBy = () => {
    if (
      selectedCreatedBy.length === dropDownData?.ETCreatedBySearchData?.length
    ) {
      setselectedCreatedBy([]);
      setselectedPresetCreatedBy([]);
    } else {
      setselectedCreatedBy(dropDownData?.ETCreatedBySearchData);
    }
  };

  const handleSelectAllProfitCenter = () => {
    if (selectedProfitCenter.length === dropDownData?.ETPCSearchData?.length) {
      setselectedProfitCenter([]);
      setselectedPresetProfitCenter([]);
    } else {
      setselectedProfitCenter(dropDownData?.ETPCSearchData);
    }
  };

  const handleSelectAllCostCenter = () => {
    if (selectedCostCenter.length === dropDownData?.ETCCSearchData?.length) {
      setselectedCostCenter([]);
      setselectedPresetCostCenter([]);
    } else {
      setselectedCostCenter(dropDownData?.ETCCSearchData);
    }
  };

  const handleSelectAllStreet = () => {
    if (selectedStreet.length === dropDownData?.ETStreetSearchData?.length) {
      setselectedStreet([]);
      setselectedPresetStreet([]);
    } else {
      setselectedStreet(dropDownData?.ETStreetSearchData);
    }
  };

  const handleSelectAllLocation = () => {
    if (
      selectedLocation.length === dropDownData?.ETLocationSearchData?.length
    ) {
      setselectedLocation([]);
      setselectedPresetLocation([]);
    } else {
      setselectedLocation(dropDownData?.ETLocationSearchData);
    }
  };

  const handleSelectAllCostCenterName = () => {
    if (
      selectedCostCenterName.length === dropDownData?.ETCCNameSearchData?.length
    ) {
      setselectedCostCenterName([]);
      setselectedPresetCostCenterName([]);
    } else {
      setselectedCostCenterName(dropDownData?.ETCCNameSearchData);
    }
  };

  const handleSelectAllPersonResponsible = () => {
    if (
      selectedPersonResponsible.length ===
      dropDownData?.ETPersonResponsibleSearch?.length
    ) {
      setselectedPersonResponsible([]);
      setselectedPresetPersonResponsible([]);
    } else {
      setselectedPersonResponsible(dropDownData?.ETPersonResponsibleSearch);
    }
  };

  const handleSelectAllUserResponsible = () => {
    if (
      selectedUserResponsible.length === dropDownData?.ETUserResponsible?.length
    ) {
      setselectedUserResponsible([]);
      setselectedPresetUserResponsible([]);
    } else {
      setselectedUserResponsible(dropDownData?.ETUserResponsible);
    }
  };

  const handleSelectAllDescription = () => {
    if (
      selectedDescription.length ===
      dropDownData?.ETDescriptionSearchData.length
    ) {
      setselectedDescription([]);
      setselectedPresetDescription([]);
    } else {
      setselectedDescription(dropDownData?.ETDescriptionSearchData);
    }
  };

  const handleSelectAllCCcategory = () => {
    if (
      selectedCCcategory.length ===
      dropDownData?.CostCenterCategorySearch.length
    ) {
      setselectedCCcategory([]);
      setselectedPresetCCcategory([]);
    } else {
      setselectedCCcategory(dropDownData?.CostCenterCategorySearch);
    }
  };

  const handleSearchAction = (value) => {
    console.log("newvalue", value);
    if (!value) {
      setTableData([...rmDataRows]);
      return;
    }
    const selected = rmDataRows.filter((row) => {
      let rowMatched = false;
      let keys = Object.keys(row);
      console.log("keys", keys);

      const displayedKeys = allColumns.map((col) => col.field);
      for (let k = 0; k < keys.length; k++) {
        if (displayedKeys.includes(keys[k])) {
          rowMatched = !row[keys[k]]
            ? false
            : row?.[keys?.[k]] &&
              row?.[keys?.[k]]
                .toString()
                .toLowerCase()
                ?.indexOf(value?.toLowerCase()) != -1;

          if (rowMatched) break;
        }
      }
      return rowMatched;
    });
    console.log("selected", selected);
    setTableData([...selected]);
    setRmDataRows([...selected]);
  };
  // useEffect(() => {
  //   setCount(tableData.length)
  // }, [tableData]);

  const handleControllingArea = (e, value) => {
    if (true) {
      var tempControllingArea = value;

      let tempFilterData = {
        ...rmSearchForm,
        controllingArea: tempControllingArea,
      };
      dispatch(
        commonFilterUpdate({
          module: "CostCenter",
          filterData: tempFilterData,
        })
      );
      getHierarchyArea(tempFilterData);
      // getProfitCenter(tempFilterData);
      // getCompanyCode(tempFilterData);
      // getCostCenterCategory(tempFilterData);
    }
  };
  const handleUserResponsible = (e, value) => {
    if (true) {
      var tempUserResponsible = value;

      let tempFilterData = {
        ...rmSearchForm,
        userResponsible: tempUserResponsible,
      };
      dispatch(
        commonFilterUpdate({
          module: "CostCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleCompanyCode = (e, value) => {
    if (true) {
      var tempCompanyCode = value;

      let tempFilterData = {
        ...rmSearchForm,
        companyCode: tempCompanyCode,
      };
      dispatch(
        commonFilterUpdate({
          module: "CostCenter",
          filterData: tempFilterData,
        })
      );
    }
  };

  const handleClearPayload = (selectedOptions) => {
    selectedOptions?.map((option) => {
      let tempFilterData = {
        ...rmSearchForm,
        [option]: "",
      };
      dispatch(
        commonFilterUpdate({
          module: "CostCenter",
          filterData: tempFilterData,
        })
      );
    });
  };

  const handleProfitCenter = (e, value) => {
    if (true) {
      var tempProfitCenter = value;

      let tempFilterData = {
        ...rmSearchForm,
        profitCenter: tempProfitCenter,
      };
      dispatch(
        commonFilterUpdate({
          module: "CostCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleHierarchyArea = (e, value) => {
    if (true) {
      var tempHierarchyArea = value;

      let tempFilterData = {
        ...rmSearchForm,
        hierarchyArea: tempHierarchyArea,
      };
      dispatch(
        commonFilterUpdate({
          module: "CostCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleCostCenterCategory = (e, value) => {
    if (true) {
      var tempCostCenterCategory = value;

      let tempFilterData = {
        ...rmSearchForm,
        costCenterCategory: tempCostCenterCategory,
      };
      dispatch(
        commonFilterUpdate({
          module: "CostCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleBlockingStatus = (e) => {
    if (e.target.value !== null) {
      var tempBlockingStatus = e.target.value;

      let tempFilterData = {
        ...rmSearchForm,
        blockingStatus: tempBlockingStatus,
      };
      dispatch(
        commonFilterUpdate({
          module: "CostCenter",
          filterData: tempFilterData,
        })
      );
    }
  };

  // const handleCreatedBy = (e, value) => {
  //   if (true) {
  //     var tempCreatedBy = value;

  //     let tempFilterData = {
  //       ...rmSearchForm,
  //       createdBy: tempCreatedBy,
  //     };
  //     dispatch(
  //       commonFilterUpdate({
  //         module: "MaterialMaster",
  //         filterData: tempFilterData,
  //       })
  //     );
  //   }
  // };
  // const handleChangedBy = (e, value) => {
  //   if (true) {
  //     var tempChangedBy = value;

  //     let tempFilterData = {
  //       ...rmSearchForm,
  //       changedBy: tempChangedBy,
  //     };
  //     dispatch(
  //       commonFilterUpdate({
  //         module: "MaterialMaster",
  //         filterData: tempFilterData,
  //       })
  //     );
  //   }
  // };

  // const handleSelection = (event) => {
  //   const selectedItems = event.target.value;
  //   fetchOptionsForDynamicFilter(dynamicDataApis[selectedItems])
  //   setSelectedOptions(selectedItems);
  //   setDisplayedFields([]);
  // };

  let dynamicDataApis = {
    // "Cost Center Name": `/${destination_CostCenter_Mass}/data/getSalesOrg`,
    // "Business Area": `/${destination_CostCenter_Mass}/data/getBusinessArea`,
    "FERC Indicator": `/${destination_CostCenter_Mass}/data/getSearchParamsFercInd`,
    "Functional Area": `/${destination_CostCenter_Mass}/data/getSearchParamsFuncArea`,
    // "Profit Center": `/${destination_CostCenter_Mass}/data/getSearchParamsProfitCenter`,
    "Country/Reg": `/${destination_CostCenter_Mass}/data/getSearchParamsCountryReg`,
    // "Batch Management" : `/${destination_MaterialMgmt}/data/getBatchManagement`,
    // "Old Material Number" : `/${destination_MaterialMgmt}/data/getOldMaterialNo`,
  };
  // console.log('rmdatarows', rmDataRows)
  const handleSelection = (event) => {
    // debugger
    const selectedItems = event.target.value;
    setSelectedOptions(selectedItems);
    setDisplayedFields([]);
    console.log("selected field", event.target.value);

    selectedItems.forEach(async (selectedItem) => {
      console.log("selected", selectedItem);
      const apiEndpoint = dynamicDataApis[selectedItem];
      console.log("apiendpoint", apiEndpoint);
      fetchOptionsForDynamicFilter(apiEndpoint, selectedItem);
    });
  };

  const isCompanyCodeSelected = (option) => {
    console.log(option, "isoptionselected");
    return selectedComanyCode.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const isCreatedBySelected = (option) => {
    console.log(option, "isoptionselected");
    return selectedCreatedBy.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const isStreetSelected = (option) => {
    console.log(option, "isoptionselected");
    return selectedStreet.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const isProfitCenterSelected = (option) => {
    console.log(option, "isoptionselected");
    return selectedProfitCenter.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const isCostCenterSelected = (option) => {
    console.log(option, "isoptionselected");
    return selectedCostCenter.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const isLocationSelected = (option) => {
    return selectedLocation.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const isCostCenterNameSelected = (option) => {
    return selectedCostCenterName.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const isPersonResponsibleSelected = (option) => {
    return selectedPersonResponsible.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const isUserResponsibleSelected = (option) => {
    return selectedUserResponsible.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const isOptionSelected = (option, dropdownOption) => {
    return selectedValues[option]?.some(
      (selectedOption) => selectedOption?.code === dropdownOption?.code
    );
  };

  const isCCcategorySelected = (option) => {
    console.log(option, "isoptionselected");
    return selectedCCcategory.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };
  const isDescriptionSelected = (option) => {
    return selectedDescription.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const handleAddFields = () => {
    const numSelected = selectedOptions.length;
    const newFields = Array.from({ length: numSelected }, (_, index) => ({
      id: index,
      value: "",
    }));
    setDisplayedFields(newFields);
  };

  const handleFieldChange = (fieldId, value) => {
    setDisplayedFields(
      (selectedOptions) => selectedOptions?.map((option) => option)
      // prevFields?.map((field) => (field.id === fieldId ? { ...field, value } : field))
    );
  };
  const items = [
    // { title: "Cost Center Name" },
    { title: "Short Description" },
    { title: "FERC Indicator" },
    { title: "Functional Area" },
    { title: "Profit Center" },
    { title: "Country/Reg" },
    { title: "Region" },
    { title: "Location" },
    { title: "Street" },
    { title: "Created On" },
    { title: "Created By" },
    // Add more options as needed
  ];
  const titleToFieldMapping = {
    "Short Description": "CostCenterName",
    "FERC Indicator": "fercIndicator",
    "Functional Area": "functionalArea",
    "Profit Center": "ProfitCenter",
    Street: "street",
    Location: "location",
    "Country/Reg": "countryreg",
    Region: "region",
    "Created On": "createdOn",
    "Created By": "createdBy",
    // "Person Responsible": "personResponsible",
    // "Business Area": "businessArea",
    // Add more mappings as needed
  };
  const names = ["Blocked", "Unblocked", ""];
  const getCompanyCode = () => {
    setIsDropDownLoading(true);
    const Payload = {
      controllingArea: rmSearchForm?.controllingArea?.code
        ? rmSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : rmSearchForm?.controllingArea?.code
        : "ETCA",
      rolePrefix: "ETP",
      top: 200,
      skip: 0,
    };
    // var HA = "TZUS";
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CompCodeSearch", data: data.body }));
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_ProfitCenter}/data/getCompCode?contrllingArea=${CA?.controllingArea?.code}`,
      `/${destination_CostCenter_Mass}/data/getSearchParamsCompCode`,
      "post",
      hSuccess,
      hError,
      Payload
    );
  };
  const getCCName = (CCName) => {
    // let payload = {
    //   "costCenterName": CCName,
    // }
    setIsDropDownLoading(true);
    const payload = {
      controllingArea: rmSearchForm?.controllingArea?.code
        ? rmSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : rmSearchForm?.controllingArea?.code
        : "ETCA",
      rolePrefix: "ETP",
      costCenterName: CCName,
      top: 200,
      skip: 0,
    };
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ETCCNameSearchData", data: data.body }));
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_ProfitCenter}/data/getCompCode?contrllingArea=${CA?.controllingArea?.code}`,
      `/${destination_CostCenter_Mass}/data/getSearchParamsCCName`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const getControllingArea = () => {
    setIsDropDownLoading(true);
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ControllingArea", data: data.body }));
      setIsDropDownLoading(false);
      console.log("MYDATa", data.body);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getSearchParamsConArea`,
      "get",
      hSuccess,
      hError
    );
  };
  const getUserResponsible = (user) => {
    setIsDropDownLoading(true);
    const payload = {
      controllingArea: rmSearchForm?.controllingArea?.code
        ? rmSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : rmSearchForm?.controllingArea?.code
        : "ETCA",
      rolePrefix: "ETP",
      userRespons: user,
      top: 200,
      skip: 0,
    };
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ETUserResponsible", data: data.body }));
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_CostCenter_Mass}/data/getUserResponsible`,
      `/${destination_CostCenter_Mass}/data/getSearchParamsUserRespons`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const getPersonReponsible = (person) => {
    // var HA = "TZUS";
    setIsDropDownLoading(true);
    const payload = {
      controllingArea: rmSearchForm?.controllingArea?.code
        ? rmSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : rmSearchForm?.controllingArea?.code
        : "ETCA",
      rolePrefix: "ETP",
      personRespons: person,
      top: 200,
      skip: 0,
    };
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "ETPersonResponsibleSearch", data: data.body })
      );
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_ProfitCenter}/data/getCompCode?contrllingArea=${CA?.controllingArea?.code}`,
      `/${destination_CostCenter_Mass}/data/getSearchParamsPersonRespons`,
      "post",

      hSuccess,
      hError,
      payload
    );
  };
  const getCostCenterCategory = () => {
    setIsDropDownLoading(true);
    const payload = {
      controllingArea: rmSearchForm?.controllingArea?.code
        ? rmSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : rmSearchForm?.controllingArea?.code
        : "ETCA",
      rolePrefix: "ETP",
      top: 200,
      skip: 0,
    };
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "CostCenterCategorySearch", data: data.body })
      );
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_CostCenter_Mass}/data/getCostCenterCategory`,
      `/${destination_CostCenter_Mass}/data/getSearchParamsCCCategory`,
      "post",

      hSuccess,
      hError,
      payload
    );
  };
  const getDescription = (desc) => {
    setIsDropDownLoading(true);
    let payload = {
      controllingArea: rmSearchForm?.controllingArea?.code
        ? rmSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : rmSearchForm?.controllingArea?.code
        : "ETCA",
      rolePrefix: "ETP",
      description: desc,
      top: 200,
      skip: 0,
    };
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "ETDescriptionSearchData", data: data.body })
      );
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_ProfitCenter}/data/getCompCode?contrllingArea=${CA?.controllingArea?.code}`,
      `/${destination_CostCenter_Mass}/data/getSearchParamsDescription`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const getStreet = (street) => {
    console.log("street", street);
    // let payload = {
    //   "street": street,
    // }
    setIsDropDownLoading(true);
    const payload = {
      controllingArea: rmSearchForm?.controllingArea?.code
        ? rmSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : rmSearchForm?.controllingArea?.code
        : "ETCA",
      rolePrefix: "ETP",
      street: street,
      top: 200,
      skip: 0,
    };
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ETStreetSearchData", data: data.body }));
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_ProfitCenter}/data/getCompCode?contrllingArea=${CA?.controllingArea?.code}`,
      `/${destination_CostCenter_Mass}/data/getSearchParamsStreet`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const getLocation = (location) => {
    // var HA = "TZUS";
    setIsDropDownLoading(true);
    const payload = {
      controllingArea: rmSearchForm?.controllingArea?.code
        ? rmSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : rmSearchForm?.controllingArea?.code
        : "ETCA",
      rolePrefix: "ETP",
      location: location,
      top: 200,
      skip: 0,
    };
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "ETLocationSearchData", data: data.body })
      );
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_ProfitCenter}/data/getCompCode?contrllingArea=${CA?.controllingArea?.code}`,
      `/${destination_CostCenter_Mass}/data/getSearchParamsLocation`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const getCreatedBy = (createdBy) => {
    setIsDropDownLoading(true);
    const payload = {
      controllingArea: rmSearchForm?.controllingArea?.code
        ? rmSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : rmSearchForm?.controllingArea?.code
        : "ETCA",
      rolePrefix: "ETP",
      createdBy: createdBy,
      top: 200,
      skip: 0,
    };
    // var HA = "TZUS";
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "ETCreatedBySearchData", data: data.body })
      );
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_ProfitCenter}/data/getCompCode?contrllingArea=${CA?.controllingArea?.code}`,
      `/${destination_CostCenter_Mass}/data/getSearchParamsCreatedBy`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const getProfitCenterSearch = (pc) => {
    // var HA = "TZUS";
    setIsDropDownLoading(true);
    let payload = {
      profitCenter: pc,
      controllingArea: rmSearchForm?.controllingArea?.code
        ? rmSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : rmSearchForm?.controllingArea?.code
        : "ETCA",
      rolePrefix: "ETP",
      top: 200,
      skip: 0,
    };
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ETPCSearchData", data: data.body }));
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_ProfitCenter}/data/getCompCode?contrllingArea=${CA?.controllingArea?.code}`,
      `/${destination_CostCenter_Mass}/data/getSearchParamsProfitCenter`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const getCostCenterSearch = (cc) => {
    // var HA = "TZUS";
    setIsDropDownLoading(true);
    let payload = {
      costCenter: cc,
      controllingArea: rmSearchForm?.controllingArea?.code
        ? rmSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : rmSearchForm?.controllingArea?.code
        : "ETCA",
      rolePrefix: "ETP",
      top: 200,
      skip: 0,
    };
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ETCCSearchData", data: data.body }));
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_ProfitCenter}/data/getCompCode?contrllingArea=${CA?.controllingArea?.code}`,
      `/${destination_CostCenter_Mass}/data/getSearchParamsCostCenter`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const getCostCenter = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CostCenter", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCostCenter`,
      "get",
      hSuccess,
      hError
    );
  };
  const getProfitCenter = (CA) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ProfitCenterSearch", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getProfitCenterAsPerControllingArea?controllingArea=${CA.controllingArea?.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getProfitCenterBasedOnCompanyCode = (value) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ProfitCenter", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getPcBasedOnCompCode?compCode=${value?.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getHierarchyArea = (CA) => {
    const payload = {
      controllingArea: rmSearchForm?.controllingArea?.code
        ? rmSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : rmSearchForm?.controllingArea?.code
        : "ETCA",
      rolePrefix: "ETP",
      userRespons: "",
    };
    console.log("CA", CA);
    // var HA = "TZUS";
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "HierarchyAreaSearch", data: data.body })
      );
      console.log("data", data);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_CostCenter_Mass}/data/getHierarchyArea?controllingArea=${CA.controllingArea?.code}`,
      `/${destination_CostCenter_Mass}/data/getSearchParamsHierarchyArea`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const getCompanyCodeBasedOnControllingArea = (data) => {
    console.log("companycodedata", data);
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CompanyCode", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCompanyCodeBasedOnControllingArea?controllingArea=${data?.ControllingArea}&rolePrefix=ETP`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCompanyCodeForCreate = (value) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CompanyCode", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCompanyCodeBasedOnControllingArea?controllingArea=${value.code}&rolePrefix=ETP`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCompanyCodeBasedOnControllingAreaCopy = (value) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CompCodeCopy", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCostCenterBasedOnCOA?controllingArea=${value.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  // const getActyIndepFormPlngTemp = () => {
  //   const hSuccess = (data) => {
  //     dispatch(
  //       setDropDown({ keyName: "ActyIndepFormPlngTemp", data: data.body })
  //     );
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(
  //     `/${destination_CostCenter_Mass}/data/getCostCenterCategory`,
  //     "get",
  //     hSuccess,
  //     hError
  //   );
  // };
  // const getActyDepFormPlngTemp = () => {
  //   const hSuccess = (data) => {
  //     dispatch(
  //       setDropDown({ keyName: "ActyDepFormPlngTemp", data: data.body })
  //     );
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(
  //     `/${destination_CostCenter_Mass}/data/getCostCenterCategory`,
  //     "get",
  //     hSuccess,
  //     hError
  //   );
  // };
  // const getActyIndepAllocTemp = () => {
  //   const hSuccess = (data) => {
  //     dispatch(setDropDown({ keyName: "ActyIndepAllocTemp", data: data.body }));
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(
  //     `/${destination_CostCenter_Mass}/data/getCostCenterCategory`,
  //     "get",
  //     hSuccess,
  //     hError
  //   );
  // };
  // const getActyDepAllocTemp = () => {
  //   const hSuccess = (data) => {
  //     dispatch(setDropDown({ keyName: "ActyDepAllocTemp", data: data.body }));
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(
  //     `/${destination_CostCenter_Mass}/data/getCostCenterCategory`,
  //     "get",
  //     hSuccess,
  //     hError
  //   );
  // };
  // const getTemplActStatKeyFigure = () => {
  //   const hSuccess = (data) => {
  //     dispatch(
  //       setDropDown({ keyName: "TemplActStatKeyFigure", data: data.body })
  //     );
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(
  //     `/${destination_CostCenter_Mass}/data/getCostCenterCategory`,
  //     "get",
  //     hSuccess,
  //     hError
  //   );
  // };
  const getBusinessArea = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "BusinessArea", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getBusinessArea`,
      "get",
      hSuccess,
      hError
    );
  };
  const getFunctionalArea = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "FunctionalArea", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getFunctionalArea`,
      "get",
      hSuccess,
      hError
    );
  };

  const getCostingSheet = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CostingSheet", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCostingSheet`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCountryOrRegion = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CountryReg", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCountry`,
      "get",
      hSuccess,
      hError
    );
  };
  const getJurisdiction = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "Jurisdiction", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getJurisdiction`,
      "get",
      hSuccess,
      hError
    );
  };
  const getRegion = () => {
    setIsDropDownLoading(true);
    const payload = {
      controllingArea: rmSearchForm?.controllingArea?.code
        ? rmSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : rmSearchForm?.controllingArea?.code
        : "ETCA",
      rolePrefix: "ETP",
      top: 200,
      skip: 0,
    };
    const hSuccess = (data) => {
      setDynamicOptions((prev) => ({ ...prev, Region: data.body }));
      setIsDropDownLoading(false);
      // dispatch(setDropDown({ keyName: "Region", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_CostCenter_Mass}/data/getRegionBasedOnCountry?country=${country?.code}`,
      `/${destination_CostCenter_Mass}/data/getSearchParamsRegion`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const getCostCenterBasicDetails = () => {
    let viewName = "Basic Data";
    const hSuccess = (data) => {
      dispatch(setCostCenterBasicDataTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getControlCostCenter = () => {
    let viewName = "Control";
    const hSuccess = (data) => {
      dispatch(setCostCenterControlTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getTemplatesCostCenter = () => {
    let viewName = "Templates";
    const hSuccess = (data) => {
      dispatch(setCostCenterTemplatesTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getAddressCostCenter = () => {
    let viewName = "Address";
    const hSuccess = (data) => {
      dispatch(setCostCenterAddressTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCommunicationCostCenter = () => {
    let viewName = "Communication";
    const hSuccess = (data) => {
      dispatch(setCostCenterCommunicationTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getHistoryCostCenter = () => {
    let viewName = "History";
    const hSuccess = (data) => {
      dispatch(setCostCenterHistoryTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getLanguageKey = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "LanguageKey", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getLanguageKey`,
      "get",
      hSuccess,
      hError
    );
  };
  const getChangeTemplate = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_ET_CHNG_FIELD_SELECTION_DT",
      version: "v4",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE": "COST CENTER",
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    setIsLoading(true);
    // const formData = new FormData();
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        let responseData =
          data?.data?.result[0]?.MDG_ET_CHNG_FIELD_SELECTION_ACTION_TYPE;
        console.log("responseData", responseData);
        // setButtonsIDM(responseData);

        setFieldselectionFromIdm(responseData);
        let fieldSelectionAllOtherChanges = [];
        let fieldSelectionBlock = [];
        let fieldSelectionTemporaryBlock = [];
        let fieldSelectionProfitCenterChange = [];
        console.log(responseData, "responseData===");
        responseData?.map((element, index) => {
          console.log("element====", element);

          if (element.MDG_FIELD_SELECTION_LVL === "ALL OTHER CHANGES") {
            let COHash = {};
            COHash["id"] = index;
            COHash["name"] = element.MDG_SELECT_OPTION;
            fieldSelectionAllOtherChanges.push(COHash);
          } else if (element.MDG_FIELD_SELECTION_LVL === "BLOCK") {
            let COHash = {};
            COHash["id"] = index;
            COHash["name"] = element.MDG_SELECT_OPTION;
            fieldSelectionBlock.push(COHash);
          } else if (
            element.MDG_FIELD_SELECTION_LVL === "TEMPORARY BLOCK/UNBLOCK"
          ) {
            let COHash = {};
            COHash["id"] = index;
            COHash["name"] = element.MDG_SELECT_OPTION;
            fieldSelectionTemporaryBlock.push(COHash);
          } else {
            let COAHash = {};
            COAHash["id"] = index;
            COAHash["name"] = element.MDG_SELECT_OPTION;
            fieldSelectionProfitCenterChange.push(COAHash);
          }
        });
        console.log("fieldSelectionBlock", fieldSelectionBlock);
        console.log(
          "fieldSelectionTemporaryBlock",
          fieldSelectionTemporaryBlock
        );
        console.log(
          "fieldSelectionProfitCenterChange",
          fieldSelectionProfitCenterChange
        );
        console.log(
          fieldSelectionAllOtherChanges,
          "fieldSelectionAllOtherChanges"
        );
        //const distinctNames = [];
        const uniqueAllOtherChanges = new Set();
        const distinctAllOtherChangesData =
          fieldSelectionAllOtherChanges.filter((obj) => {
            if (!uniqueAllOtherChanges.has(obj.name)) {
              uniqueAllOtherChanges.add(obj.name);
              return true;
            }
            return false;
          });
        const uniqueBlockNames = new Set();
        const distinctBlockNames = fieldSelectionBlock.filter((obj) => {
          if (!uniqueBlockNames.has(obj.name)) {
            uniqueBlockNames.add(obj.name);
            return true;
          }
          return false;
        });
        const uniqueTemporaryBlockNames = new Set();
        const distinctTemporaryBlockNames = fieldSelectionTemporaryBlock.filter(
          (obj) => {
            if (!uniqueTemporaryBlockNames.has(obj.name)) {
              uniqueTemporaryBlockNames.add(obj.name);
              return true;
            }
            return false;
          }
        );
        const uniqueProfitCenterChange = new Set();
        const distinctProfitCenterChange =
          fieldSelectionProfitCenterChange.filter((obj) => {
            if (!uniqueProfitCenterChange.has(obj.name)) {
              uniqueProfitCenterChange.add(obj.name);
              return true;
            }
            return false;
          });
        console.log(
          distinctAllOtherChangesData,
          distinctBlockNames,
          distinctTemporaryBlockNames,
          distinctProfitCenterChange,
          "distinctCompanyCodeData"
        );
        //setRuleData(templateData);
        setDataListBlockNamesSelected(distinctBlockNames);
        setDataListAllOtherChanges(distinctAllOtherChangesData);
        setDataListBlockNames(distinctBlockNames);
        setDataListTemporaryBlockNamesSelected(distinctTemporaryBlockNames);
        setDataListTemporaryBlockNames(distinctTemporaryBlockNames);
        setDataListProfitCenterChange(distinctProfitCenterChange);
        setDataListProfitCenterChangeSelected(distinctProfitCenterChange);
        // let templateData = [];
        // responseData.map((element, index) => {
        //   console.log("element", element);

        //   var tempRow = {
        //     id: index,
        //     templateName: element?.MDG_SELECT_OPTION || "",
        //     templateData: element?.MDG_FEILD_NAME || "",
        //   };
        //   templateData.push(tempRow);
        // });
        // console.log("templateData", templateData);
        // setRuleData(templateData);
      } else {
        // setMessageDialogTitle("Create");
        // setsuccessMsg(false);
        // setMessageDialogMessage("Creation Failed");
        // setHandleExtrabutton(false);
        // setMessageDialogSeverity("danger");
        // setMessageDialogOK(false);
        // setMessageDialogExtra(true);
        // handleMessageDialogClickOpen();
        // handleCreateDialogClose();
        // setIsLoading(false);
      }
      handleClose();
    };

    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(
        `/${destination_IDM}/rest/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      doAjax(
        `/${destination_IDM}/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };
  function groupBy(array, key) {
    return array.reduce((result, currentValue) => {
      (result[currentValue[key]] = result[currentValue[key]] || []).push(
        currentValue
      );
      return result;
    }, {});
  }

  const getNewControllingArea = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_CUSTOM_DROPDOWN_LIST",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MODULE": "Cost Center",
          "MDG_CONDITIONS.MDG_FIELD_NAME": "Controlling Area",
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };

    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        const lookupData =
          data?.data?.result[0]?.MDG_CUSTOM_LOOKUP_ACTION_TYPE || [];
        console.log("questionData", lookupData);

        let lookupDataArr = [];
        lookupData?.map((itemData) => {
          let lookupDataHash = {};
          lookupDataHash["code"] = itemData?.MDG_LOOKUP_CODE;
          lookupDataHash["desc"] = itemData?.MDG_LOOKUP_DESC;
          lookupDataArr.push(lookupDataHash);
        });
        console.log(lookupDataArr, "lookupDataArr");

        dispatch(
          setDropDown({ keyName: "NewControllingArea", data: lookupDataArr })
        );
        // setQuestions(questionsData);
      }
    };

    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(
        `/${destination_IDM}/rest/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      doAjax(
        `/${destination_IDM}/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };

  const getCreateTemplate = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_CC_FIELD_CONFIG",
      version: "v3",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_CC_SCENARIO": REQUEST_TYPE.CREATE,
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    setIsLoading(true); //
    // const formData = new FormData();
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        let responseData =
          data?.data?.result[0]?.MDG_CC_FIELD_DETAILS_ACTION_TYPE;
        let sortedData = responseData.sort(
          (a, b) => a.MDG_CC_VIEW_SEQUENCE - b.MDG_CC_VIEW_SEQUENCE
        );

        sortedData?.map((itemData) => {
          //console.log()
          if (itemData?.MDG_CC_JSON_FIELD_NAME == "ValidFrom") {
            const parsedDate = moment(
              itemData?.MDG_CC_DEFAULT_VALUE,
              "DD/MM/YYYY"
            );
            const formattedDate = new Date(parsedDate);
            //console.log(formattedDate,"formattedDate")

            setNewValidFromDate(formattedDate);
          }
          if (itemData?.MDG_CC_JSON_FIELD_NAME == "ValidTo") {
            const parsedDate = moment(
              itemData?.MDG_CC_DEFAULT_VALUE,
              "DD/MM/YYYY"
            );
            const formattedDate = new Date(parsedDate);
          }
        });
        const groupedFields = groupBy(sortedData, "MDG_CC_VIEW_NAME");
        groupedFields?.["Initial Screen"]?.map((item) => {
          console.log(item, "item===");
          if (item?.MDG_CC_UI_FIELD_NAME === "Cost Center") {
            console.log(item?.MDG_CC_MAX_LENGTH, "item?.MDG_CC_MAX_LENGTH");
            setCostCenterLength(item?.MDG_CC_MAX_LENGTH);
          }
        });
        // setCostCenterLength(
        //   groupedFields?.["Initial Screen"][2]?.MDG_CC_MAX_LENGTH
        // );
        setInitialScreenDataInRedux(groupedFields?.["Initial Screen"]);
        console.log(groupedFields?.["Initial Screen"], "groupedFields");
      }
    };
    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(
        `/${destination_IDM}/rest/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      doAjax(
        `/${destination_IDM}/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };
  const setInitialScreenDataInRedux = (data) => {
    console.log("inital data", data);
    data?.map((item) => {
      dispatch(
        setSingleCostCenterPayload({
          keyName: item?.MDG_CC_UI_FIELD_NAME.replaceAll("(", "")
            .replaceAll(")", "")
            .replaceAll("/", "")
            .replaceAll("-", "")
            .replaceAll(".", "")
            .replaceAll("%", "")
            .split(" ")
            .join(""),
          data: item?.MDG_CC_DEFAULT_VALUE,
        })
      );
    });
  };
  useEffect(() => {
    getCompanyCodeBasedOnControllingArea(initialScreenData);
  }, [initialScreenData?.ControllingArea]);

  // const getCostCenterCategoryIDMRule = () => {
  //   let costCenterCategoryArr = [];
  //   responseData.map((item) => {
  //     console.log(item.MDG_CC_COST_CENTER_CATEGORY[0], "============");
  //     let accountType_hash = {};
  //     accountType_hash["code"] = item.MDG_GL_ACCOUNT_TYPE_CODE;
  //     accountType_hash["desc"] = item.MDG_GL_ACCOUNT_TYPE;
  //     accountTypeArr.push(accountType_hash);
  //   });
  //   const result = Object.values(
  //     accountTypeArr.reduce((acc, obj) => ({ ...acc, [obj.desc]: obj }), {})
  //   );
  //   console.log(result, "result==");
  //   return result;
  // };

  // const getCascadingDropDownForCCG = () => {
  //   let payload = {
  //     decisionTableId: null,
  //     decisionTableName: "MDG_CC_CASCADING_DROPLIST_DT",
  //     version: "v1",
  //     rulePolicy: null,
  //     validityDate: null,
  //     conditions: [
  //       {
  //         "MDG_CONDITIONS.MDG_SERIAL_NO": 1,
  //       },
  //     ],
  //     systemFilters: null,
  //     systemOrders: null,
  //     filterString: null,
  //   };

  //   setIsLoading(true);
  //   const hSuccess = (data) => {
  //     setIsLoading(false);
  //     if (data.statusCode === 200) {
  //       let responseData =
  //         data?.data?.result[0]?.MDG_CC_CASCADING_DROPLIST_ACTION_TYPE;
  //       setCascadingDropDownData(responseData);

  //       let getCostCenterCategoryIDM =
  //         getCostCenterCategoryIDMRule(responseData);
  //         setDropDown({ keyName: "CostCenterCategory", data: getCostCenterCategoryIDM })
  //         console.log("getCostCenterCategoryIDM",getCostCenterCategoryIDM)
  //     } else {
  //     }
  //     handleClose();
  //   };

  //   const hError = (error) => {
  //     console.log(error);
  //   };

  //   if (applicationConfig.environment === "localhost") {
  //     doAjax(
  //       `/${destination_IDM}/rest/v1/invoke-rules`,
  //       "post",
  //       hSuccess,
  //       hError,
  //       payload
  //     );
  //   } else {
  //     doAjax(
  //       `/${destination_IDM}/v1/invoke-rules`,
  //       "post",
  //       hSuccess,
  //       hError,
  //       payload
  //     );
  //   }
  // };
  useEffect(() => {
    // getUserResponsible();
    getCostCenterCategory();
    getNewControllingArea();
    getControllingArea();
    getCreateTemplate();
    // getPersonReponsible();
    getRegion();
    // getCascadingDropDownForCCG();
    // getBusinessArea();
    // getCreatedBy();
    // getLocation();
    getFunctionalArea();
    getCostCenterBasicDetails();
    getControlCostCenter();
    getTemplatesCostCenter();
    getAddressCostCenter();
    getCommunicationCostCenter();
    getHistoryCostCenter();
    getCostCenter();
    // getControllingArea();
    // getUserResponsible();
    // getHierarchyArea();
    getCompanyCode();
    //check_all_fields_are_filled_are_empty_or_not();
    dispatch(setTaskData({}));
    getChangeTemplate();
    // getCascadingDropDownForCCG();
    // setInitialScreenDataInRedux();//to dispatch intial screen data in payload
    dispatch(clearCostCenter());
    dispatch(clearPayload({}));
    dispatch(clearTaskData());
    dispatch(clearSingleCostCenter());
    dispatch(clearCostCenterPayload());
    dispatch(clearArtifactId());
  }, []);
  // const fetchOptionsForDynamicFilter = (apiEndpoint) => {
  //   const hSuccess = (data) => {
  //     setDynamicOptions({
  //       ...dynamicOptions,
  //       [apiEndpoint]: data.body,

  //     });
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(apiEndpoint, "get", hSuccess, hError);
  // };
  const fetchOptionsForDynamicFilter = (apiEndpoint, selectedItem) => {
    setIsDropDownLoading(true);
    let payload;
    if (selectedItem === "FERC Indicator") {
      payload = {
        controllingArea: rmSearchForm?.controllingArea?.code
          ? rmSearchForm?.controllingArea?.code === ""
            ? "ETCA"
            : rmSearchForm?.controllingArea?.code
          : "ETCA",
        rolePrefix: "ETP",
        fercInd: "",
        top: 200,
        skip: 0,
      };
    } else if (selectedItem === "Functional Area") {
      payload = {
        controllingArea: rmSearchForm?.controllingArea?.code
          ? rmSearchForm?.controllingArea?.code === ""
            ? "ETCA"
            : rmSearchForm?.controllingArea?.code
          : "ETCA",
        rolePrefix: "ETP",
        funcArea: "",
        top: 200,
        skip: 0,
      };
    } else {
      payload = {
        controllingArea: rmSearchForm?.controllingArea?.code
          ? rmSearchForm?.controllingArea?.code === ""
            ? "ETCA"
            : rmSearchForm?.controllingArea?.code
          : "ETCA",
        rolePrefix: "ETP",
        top: 200,
        skip: 0,
      };
    }

    const hSuccess = (data) => {
      const newOptions = data.body;
      // debugger
      // Merge the new options with the existing dynamicOptions
      //setDynamicOptions((prevOptions) => [...prevOptions, ...newOptions]);
      setDynamicOptions((prev) => ({ ...prev, [selectedItem]: newOptions }));
      console.log("newrakeshconsole", {
        ...dynamicOptions,
        [selectedItem]: newOptions,
      });
      setIsDropDownLoading(false);
    };

    console.log(dynamicOptions, "dinamicoptions==================");
    const hError = (error) => {
      console.log(error);
    };
    doAjax(apiEndpoint, "post", hSuccess, hError, payload);
  };
  const clearSearchBar = () => {
    setMaterialNumber("");
  };
  const getStoreLookupDataInDB = () => {
    const hSuccess = (data) => {
      console.log("dataforexcel", data);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/excel/saveExcelLookupDataInDB`,
      "get",
      hSuccess,
      hError
    );
  };
  const getStoreLookupDataInDB2 = () => {
    const hSuccess = (data) => {
      console.log("dataforexcel", data);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/excel/saveExcelDependentLookupDataInDB`,
      "get",
      hSuccess,
      hError
    );
  };
  const getMaterialNoGlobalSearch = (fetchSkip) => {
    // getStoreLookupDataInDB();
    // getStoreLookupDataInDB2();
    // var tempCostCenter = value;

    // let tempFilterData = {
    //   ...rmSearchForm,
    //   costCenter: tempCostCenter,
    // };
    // dispatch(
    //   commonFilterUpdate({
    //     module: "CostCenter",
    //     filterData: tempFilterData,
    //   })
    // );
    console.log("rmSearchForm", rmDataRows);
    // setTableLoading(true);
    if (!fetchSkip) {
      setPage(0);
      setPageSize(10);
      setSkip(0);
    }
    let payload = {
      costCenterName: "",
      costCenter: formcontroller_SearchBar?.number ?? "",
      controllingArea: "",
      companyCode: "",
      profitCenter: "",
      hierarchyArea: "",
      costCenterCategory: "",
      createdBy: "",
      fromDate: "",
      toDate: "",
      businessArea: "",
      personResponsible: "",
      userResponsible: "",
      functionalArea: "",
      fercIndicator: "",
      street: "",
      location: "",
      description: "",
      country: "",
      region: "",
      blockingStatus: "",
      top: 500,
      skip: fetchSkip ?? 0,
    };
    const hSuccess = (data) => {
      //alert("coming")
      var rows = [];
      for (let index = 0; index < data?.body?.list?.length; index++) {
        var tempObj = data?.body?.list[index];
        if (true) {
          var tempRow = {
            id: uuidv4(),
            description: tempObj?.Description,
            controllingArea: tempObj?.controllingArea,
            companyCode: tempObj?.CompanyCode,
            hierarchyArea: tempObj?.HeirarchyArea,
            costCenterCategory: tempObj?.CCtrCategory,
            costCenter: tempObj?.costCenter,
            CostCenterName: tempObj?.CostCenterName,
            businessArea:
              tempObj["BusinessArea"] !== ""
                ? `${tempObj["BusinessArea"]}`
                : "Not Available",

            functionalArea:
              tempObj["FunctionalArea"] !== ""
                ? `${tempObj["FunctionalArea"]}`
                : "Not Available",
            personResponsible:
              tempObj["PersonResponsible"] !== ""
                ? `${tempObj["PersonResponsible"]}`
                : "Not Available",
            userResponsible:
              tempObj["UserResponsible"] !== ""
                ? `${tempObj["UserResponsible"]}`
                : "Not Available",
            fercIndicator:
              tempObj["FERCindicator"] !== ""
                ? `${tempObj["FERCindicator"]}`
                : "Not Available",
            street:
              tempObj["Street"] !== ""
                ? `${tempObj["Street"]}`
                : "Not Available",
            location:
              tempObj["Location"] !== ""
                ? `${tempObj["Location"]}`
                : "Not Available",
            countryreg:
              tempObj["Country"] !== ""
                ? `${tempObj["Country"]}`
                : "Not Available",
            region:
              tempObj["Region"] !== ""
                ? `${tempObj["Region"]}`
                : "Not Available",
            createdOn:
              tempObj["CreatedOn"] !== ""
                ? `${moment(tempObj["CreatedOn"]).format("DD MMM YYYY")}`
                : "Not Available",
            createdBy:
              tempObj["CreatedBy"] !== ""
                ? `${tempObj["CreatedBy"]}`
                : "Not Available",
            ProfitCenter:
              tempObj["ProfitCenter"] !== ""
                ? `${tempObj["ProfitCenter"]}`
                : "Not Available",
            blockingStatus:
              tempObj["BlockingStatus"] === "X" ? "Blocked" : "Unblocked",
          };
          rows.push(tempRow);
        }
      }
      // rows.sort(
      //   (a, b) =>
      //     moment(a.createdOn, "DD MMM YYYY HH:mm") -
      //     moment(b.createdOn, "DD MMM YYYY HH:mm")
      // );
      setRmDataRows(rows); //chiranjit
      setTableLoading(false);
      setroCount(rows.length);
      setCount(data?.body?.count);
    };
    let hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCostCentersBasedOnAdditionalParams`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  /* Setting Default Dates */
  const presentDate = new Date();
  const backDate = new Date();
  backDate.setDate(backDate.getDate() - 15);

  const [date, setDate] = useState([backDate, presentDate]);
  const [date1, setDate1] = useState([backDate, presentDate]);

  const handleDate = (e) => {
    // if (e !== null) setDate(e.reverse());
    if (e !== null) {
      // var createdOn = e.reverse();
      dispatch(
        commonFilterUpdate({
          module: "CostCenter",
          filterData: {
            ...rmSearchForm,
            createdOn: e,
          },
        })
      );
    }
  };

  const handleDate1 = (e) => {
    if (e !== null) setDate1(e.reverse());
  };

  const handleSnackBarClickaccept = () => {
    setOpenSnackBaraccept(true);
  };

  const handleSnackBarCloseaccept = (event, reason) => {
    if (reason === "clickaway") {
      return;
    }

    setOpenSnackBaraccept(false);
  };

  const handleUserName = (e) => {
    setUserName(e.target.value);
  };

  // Get Filter Data
  const getFilter = (fetchSkip) => {
    // console.log("rmSearchForm", rmSearchForm);
    setTableLoading(true);
    // if (!fetchSkip) {
    setPage(0);
    //   setPageSize(10);
    //   setSkip(0);
    // }

    console.log(filterFieldData, "filterFieldData============");
    let payload = {
      costCenterName: rmSearchForm?.costCenterName ?? "",
      costCenter: rmSearchForm?.["Cost Center"] ?? "",
      controllingArea: rmSearchForm?.controllingArea?.code
        ? rmSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : rmSearchForm?.controllingArea?.code
        : "ETCA",
      companyCode: rmSearchForm?.["Company Code"] ?? "",
      profitCenter: rmSearchForm?.["Profit Center"] ?? "",
      hierarchyArea: rmSearchForm?.["Hierarchy Area"] ?? "",
      // userId:
      //   rmSearchForm?.compCodeUserId?.length > 0
      //     ? rmSearchForm?.compCodeUserId[0].UserId
      //     : "",
      //userId: userId?.UserId || compCodeData[0]?.UserId || "",
      rolePrefix: "ETP",
      costCenterCategory: rmSearchForm?.["Cost Center Category"] ?? "",
      createdBy: rmSearchForm?.createdBy ?? "",
      fromDate:
        moment(rmSearchForm?.createdOn[0]).format("YYYY-MM-DDT00:00:00") ?? "",
      toDate:
        moment(rmSearchForm?.createdOn[1]).format("YYYY-MM-DDT00:00:00") ?? "",
      businessArea: "",
      personResponsible: rmSearchForm?.personResponsible ?? "",
      userResponsible: rmSearchForm?.["User Responsible"] ?? "",
      // businessArea: filterFieldData?.["Business Area"]?.code ?? "",
      functionalArea: rmSearchForm?.["Functional Area"] ?? "",
      fercIndicator: rmSearchForm?.["FERC Indicator"] ?? "",
      street: rmSearchForm?.street ?? "",
      location: rmSearchForm?.location ?? "",
      description: rmSearchForm?.description ?? "",
      country: rmSearchForm?.["Country/Reg"] ?? "",
      region: rmSearchForm?.["Region"] ?? "",
      blockingStatus:
        rmSearchForm?.blockingStatus === "Blocked"
          ? "X"
          : rmSearchForm?.blockingStatus === "Unblocked"
          ? "Y"
          : "",
      top: 100,
      skip: 0,
      // skip: fetchSkip ?? 0,
    };
    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        console.log("data", data.body.list);
        var rows = [];
        for (let index = 0; index < data?.body?.list?.length; index++) {
          var tempObj = data?.body?.list[index];
          console.log("hshshsh", tempObj);

          var tempRow = {
            id: uuidv4(),
            description: tempObj?.Description,
            controllingArea: tempObj?.controllingArea,
            companyCode: tempObj?.CompanyCode,
            hierarchyArea: tempObj?.HeirarchyArea,
            costCenterCategory: tempObj?.CCtrCategory,
            costCenter: tempObj?.costCenter,
            CostCenterName: tempObj?.CostCenterName,
            businessArea:
              tempObj["BusinessArea"] !== ""
                ? `${tempObj["BusinessArea"]}`
                : "Not Available",

            functionalArea:
              tempObj["FunctionalArea"] !== ""
                ? `${tempObj["FunctionalArea"]}`
                : "Not Available",
            personResponsible:
              tempObj["PersonResponsible"] !== ""
                ? `${tempObj["PersonResponsible"]}`
                : "Not Available",
            userResponsible:
              tempObj["UserResponsible"] !== ""
                ? `${tempObj["UserResponsible"]}`
                : "Not Available",
            fercIndicator:
              tempObj["FERCindicator"] !== ""
                ? `${tempObj["FERCindicator"]}`
                : "Not Available",
            street:
              tempObj["Street"] !== ""
                ? `${tempObj["Street"]}`
                : "Not Available",
            location:
              tempObj["Location"] !== ""
                ? `${tempObj["Location"]}`
                : "Not Available",
            countryreg:
              tempObj["Country"] !== ""
                ? `${tempObj["Country"]}`
                : "Not Available",
            region:
              tempObj["Region"] !== ""
                ? `${tempObj["Region"]}`
                : "Not Available",
            createdOn:
              tempObj["CreatedOn"] !== ""
                ? `${moment(tempObj["CreatedOn"]).format("DD MMM YYYY")}`
                : "Not Available",
            createdBy:
              tempObj["CreatedBy"] !== ""
                ? `${tempObj["CreatedBy"]}`
                : "Not Available",
            ProfitCenter:
              tempObj["ProfitCenter"] !== ""
                ? `${tempObj["ProfitCenter"]}`
                : "Not Available",
            blockingStatus:
              tempObj["BlockingStatus"] === "X" ? "Blocked" : "Unblocked",
          };
          rows.push(tempRow);
        }
        console.log("tempobj", tempRow);
        console.log("tempObH", tempObj);
        // rows.sort(
        //   (a, b) =>
        //     moment(a.createdOn, "DD MMM YYYY HH:mm") -
        //     moment(b.createdOn, "DD MMM YYYY HH:mm")
        // );
        setRmDataRows(rows);
        setTableLoading(false);
        setroCount(rows.length);
        setCount(data?.body?.count);
      } else if (data.statusCode === 400) {
        setSearchDialogTitle("Warning");
        setSearchDialogMessage(
          "Please Select Lesser Fields as the URL is getting too long !!"
        );
        handleSearchDialogClickOpen();
      }
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCostCentersBasedOnAdditionalParams`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  // const getCCData = (fetchSkip) => {
  //   console.log("rmSearchForminccdata", rmSearchForm);
  //   setTableLoading(true);
  //   if (!fetchSkip) {
  //     setPage(0);
  //     setPageSize(10);
  //     setSkip(0);
  //   }

  //   console.log(filterFieldData, "filterFieldData============");
  //   let payload = {
  //     costCenterName: rmSearchForm?.costCenterName?.code ?? "",
  //     costCenter: rmSearchForm?.costCenterString ?? "",
  //     //userId: userId?.UserId || compCodeData[0]?.UserId || "",
  //     rolePrefix: "ETP",
  //     controllingArea: rmSearchForm?.controllingArea?.code
  //       ? rmSearchForm?.controllingArea?.code === ""
  //         ? "ETCA"
  //         : rmSearchForm?.controllingArea?.code
  //       : "ETCA",
  //     companyCode: rmSearchForm?.companyCode?.code ?? "",
  //     profitCenter: rmSearchForm?.["Profit Center"]?.code ?? "",
  //     hierarchyArea: rmSearchForm?.hierarchyArea?.code ?? "",
  //     costCenterCategory: rmSearchForm?.costCenterCategory?.code ?? "",
  //     createdBy: rmSearchForm?.createdBy?.code ?? "",
  //     fromDate:
  //       moment(rmSearchForm?.createdOn[0]).format("YYYY-MM-DDT00:00:00") ?? "",
  //     toDate:
  //       moment(rmSearchForm?.createdOn[1]).format("YYYY-MM-DDT00:00:00") ?? "",
  //     businessArea: "",
  //     personResponsible: rmSearchForm?.personResponsible?.code ?? "",
  //     userResponsible: rmSearchForm?.userResponsible?.code ?? "",
  //     // businessArea: filterFieldData?.["Business Area"]?.code ?? "",
  //     functionalArea: rmSearchForm?.["Functional Area"]?.code ?? "",
  //     fercIndicator: rmSearchForm?.["FERC Indicator"]?.code ?? "",
  //     street: rmSearchForm?.street?.code ?? "",
  //     location: rmSearchForm?.location?.code ?? "",
  //     description: rmSearchForm?.description?.code ?? "",
  //     country: rmSearchForm?.["Country/Reg"]?.code ?? "",
  //     region: rmSearchForm?.["Region"]?.code ?? "",
  //     blockingStatus:
  //       rmSearchForm?.blockingStatus === "Blocked"
  //         ? "X"
  //         : rmSearchForm?.blockingStatus === "Unblocked"
  //         ? "Y"
  //         : "",
  //     top: 100,
  //     skip: fetchSkip ?? 0,
  //   };
  //   const hSuccess = (data) => {
  //     console.log("data", data.body.list);
  //     var rows = [];
  //     for (let index = 0; index < data?.body?.list?.length; index++) {
  //       var tempObj = data?.body?.list[index];
  //       console.log("hshshsh", tempObj);

  //       var tempRow = {
  //         id: uuidv4(),
  //         description: tempObj?.Description,
  //         controllingArea: tempObj?.controllingArea,
  //         companyCode: tempObj?.CompanyCode,
  //         hierarchyArea: tempObj?.HeirarchyArea,
  //         costCenterCategory: tempObj?.CCtrCategory,
  //         costCenter: tempObj?.costCenter,
  //         CostCenterName: tempObj?.CostCenterName,
  //         businessArea:
  //           tempObj["BusinessArea"] !== ""
  //             ? `${tempObj["BusinessArea"]}`
  //             : "Not Available",

  //         functionalArea:
  //           tempObj["FunctionalArea"] !== ""
  //             ? `${tempObj["FunctionalArea"]}`
  //             : "Not Available",
  //         personResponsible:
  //           tempObj["PersonResponsible"] !== ""
  //             ? `${tempObj["PersonResponsible"]}`
  //             : "Not Available",
  //         userResponsible:
  //           tempObj["UserResponsible"] !== ""
  //             ? `${tempObj["UserResponsible"]}`
  //             : "Not Available",
  //         fercIndicator:
  //           tempObj["FERCindicator"] !== ""
  //             ? `${tempObj["FERCindicator"]}`
  //             : "Not Available",
  //         street:
  //           tempObj["Street"] !== "" ? `${tempObj["Street"]}` : "Not Available",
  //         location:
  //           tempObj["Location"] !== ""
  //             ? `${tempObj["Location"]}`
  //             : "Not Available",
  //         countryreg:
  //           tempObj["Country"] !== ""
  //             ? `${tempObj["Country"]}`
  //             : "Not Available",
  //         region:
  //           tempObj["Region"] !== "" ? `${tempObj["Region"]}` : "Not Available",
  //         createdOn:
  //           tempObj["CreatedOn"] !== ""
  //             ? `${moment(tempObj["CreatedOn"]).format("DD MMM YYYY")}`
  //             : "Not Available",
  //         createdBy:
  //           tempObj["CreatedBy"] !== ""
  //             ? `${tempObj["CreatedBy"]}`
  //             : "Not Available",
  //         ProfitCenter:
  //           tempObj["ProfitCenter"] !== ""
  //             ? `${tempObj["ProfitCenter"]}`
  //             : "Not Available",
  //         blockingStatus:
  //           tempObj["BlockingStatus"] === "X" ? "Blocked" : "Unblocked",
  //       };
  //       rows.push(tempRow);
  //     }
  //     console.log("tempobj", tempRow);
  //     console.log("tempObH", tempObj);
  //     rows.sort(
  //       (a, b) =>
  //         moment(a.createdOn, "DD MMM YYYY HH:mm") -
  //         moment(b.createdOn, "DD MMM YYYY HH:mm")
  //     );
  //     // setRmDataRows(rows.reverse());
  //     if (fetchSkip === 0) {
  //       setRmDataRows(rows.reverse());
  //     } else {
  //       setRmDataRows((prevRows) => [...prevRows, ...rows.reverse()]);
  //     }
  //     setTableLoading(false);
  //     setroCount(rows.length);
  //     setCount(data?.body?.count);
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(
  //     `/${destination_CostCenter_Mass}/data/getCostCentersBasedOnAdditionalParams`,
  //     "post",
  //     hSuccess,
  //     hError,
  //     payload
  //   );
  // };

  const getFilterBasedOnPagination = (fetchSkip) => {
    console.log("rmSearchForminccdata", rmSearchForm);
    setTableLoading(true);
    // if (!fetchSkip) {
    //   setPage(0);
    //   setPageSize(10);
    //   setSkip(0);
    // }

    console.log(filterFieldData, "filterFieldData============");
    let payload = {
      costCenterName: rmSearchForm?.costCenterName ?? "",
      costCenter: rmSearchForm?.["Cost Center"] ?? "",
      //userId: userId?.UserId || compCodeData[0]?.UserId || "",
      rolePrefix: "ETP",
      controllingArea: rmSearchForm?.controllingArea?.code
        ? rmSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : rmSearchForm?.controllingArea?.code
        : "ETCA",
      companyCode: rmSearchForm?.["Company Code"] ?? "",
      profitCenter: rmSearchForm?.["Profit Center"] ?? "",
      hierarchyArea: rmSearchForm?.["Hierarchy Area"]?.code ?? "",
      costCenterCategory: rmSearchForm?.["Cost Center Category"] ?? "",
      createdBy: rmSearchForm?.createdBy ?? "",
      fromDate:
        moment(rmSearchForm?.createdOn[0]).format("YYYY-MM-DDT00:00:00") ?? "",
      toDate:
        moment(rmSearchForm?.createdOn[1]).format("YYYY-MM-DDT00:00:00") ?? "",
      businessArea: "",
      personResponsible: rmSearchForm?.personResponsible ?? "",
      userResponsible: rmSearchForm?.["User Responsible"] ?? "",
      // businessArea: filterFieldData?.["Business Area"]?.code ?? "",
      functionalArea: rmSearchForm?.["Functional Area"] ?? "",
      fercIndicator: rmSearchForm?.["FERC Indicator"] ?? "",
      street: rmSearchForm?.street ?? "",
      location: rmSearchForm?.location ?? "",
      description: rmSearchForm?.description ?? "",
      country: rmSearchForm?.["Country/Reg"] ?? "",
      region: rmSearchForm?.["Region"] ?? "",
      blockingStatus:
        rmSearchForm?.blockingStatus === "Blocked"
          ? "X"
          : rmSearchForm?.blockingStatus === "Unblocked"
          ? "Y"
          : "",
      top: 100,
      skip: rmDataRows?.length ?? 0,
    };
    const hSuccess = (data) => {
      // let data = demoData;
      console.log("data", data.body.list);
      var rows = [];
      for (let index = 0; index < data?.body?.list?.length; index++) {
        var tempObj = data?.body?.list[index];
        console.log("hshshsh", tempObj);

        var tempRow = {
          id: uuidv4(),
          description: tempObj?.Description,
          controllingArea: tempObj?.controllingArea,
          companyCode: tempObj?.CompanyCode,
          hierarchyArea: tempObj?.HeirarchyArea,
          costCenterCategory: tempObj?.CCtrCategory,
          costCenter: tempObj?.costCenter,
          CostCenterName: tempObj?.CostCenterName,
          businessArea:
            tempObj["BusinessArea"] !== ""
              ? `${tempObj["BusinessArea"]}`
              : "Not Available",

          functionalArea:
            tempObj["FunctionalArea"] !== ""
              ? `${tempObj["FunctionalArea"]}`
              : "Not Available",
          personResponsible:
            tempObj["PersonResponsible"] !== ""
              ? `${tempObj["PersonResponsible"]}`
              : "Not Available",
          userResponsible:
            tempObj["UserResponsible"] !== ""
              ? `${tempObj["UserResponsible"]}`
              : "Not Available",
          fercIndicator:
            tempObj["FERCindicator"] !== ""
              ? `${tempObj["FERCindicator"]}`
              : "Not Available",
          street:
            tempObj["Street"] !== "" ? `${tempObj["Street"]}` : "Not Available",
          location:
            tempObj["Location"] !== ""
              ? `${tempObj["Location"]}`
              : "Not Available",
          countryreg:
            tempObj["Country"] !== ""
              ? `${tempObj["Country"]}`
              : "Not Available",
          region:
            tempObj["Region"] !== "" ? `${tempObj["Region"]}` : "Not Available",
          createdOn:
            tempObj["CreatedOn"] !== ""
              ? `${moment(tempObj["CreatedOn"]).format("DD MMM YYYY")}`
              : "Not Available",
          createdBy:
            tempObj["CreatedBy"] !== ""
              ? `${tempObj["CreatedBy"]}`
              : "Not Available",
          ProfitCenter:
            tempObj["ProfitCenter"] !== ""
              ? `${tempObj["ProfitCenter"]}`
              : "Not Available",
          blockingStatus:
            tempObj["BlockingStatus"] === "X" ? "Blocked" : "Unblocked",
        };
        rows.push(tempRow);
      }
      console.log("tempobj", tempRow);
      console.log("tempObH", tempObj);
      // rows.sort(
      //   (a, b) =>
      //     moment(a.createdOn, "DD MMM YYYY HH:mm") -
      //     moment(b.createdOn, "DD MMM YYYY HH:mm")
      // );
      // setRmDataRows(rows.reverse());
      // if (fetchSkip === 0) {
      // setRmDataRows(rows.reverse());
      // } else {
      setRmDataRows((prevRows) => [...prevRows, ...rows]);
      // }
      setTableLoading(false);
      setroCount(rows.length);
      setCount(data?.body?.count);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCostCentersBasedOnAdditionalParams`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const [userList, setUserList] = useState([]);

  const [confirmation, setconfirmation] = useState([]);
  const [confirmationText, setConfirmationText] = useState(null);
  const [poHeader, setPoHeader] = useState(null);
  const [roCount, setroCount] = useState(0);
  const [showBtmNav, setShowBtmNav] = useState(false);
  const [openDialogIDM, setOpenDialogIDM] = useState(false);
  const [openSnackbarDialog, setOpenSnackbarDialog] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [showTableInDialog, setShowTableInDialog] = useState(false);
  const [opendialog3, setOpendialog3] = useState(false);
  const [openforwarddialog, setOpenforwarddialog] = useState(false);
  const [rejectInputText, setRejectInputText] = useState("");
  const [acceptInputText, setAcceptInputText] = useState("");
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState("");
  const [messageDialogMessage, setMessageDialogMessage] = useState();
  const [dialogTitle, setDialogTitle] = useState("");
  const [dialogOkText, setDialogOkText] = useState("");
  const [blurLoading, setBlurLoading] = useState("");
  const [loaderMessage, setLoaderMessage] = useState("");
  const [selectAll, setSelectAll] = useState(false);
  const [tableRows, setTableRows] = useState([]);
  const [dataList, setDataList] = useState([]);

  const [radiovalue, setRadiovalue] = useState("");
  const [selectedTemplateData, setSelectedTemplateData] = useState("");

  const duplicateFieldsColumns = [
    {
      field: "costCenter",
      headerName: "Cost Center",
      editable: false,
      flex: 1,
      width: 150,
    },
    {
      field: "reqId",
      headerName: "Req Id",
      editable: false,
      flex: 1,
      width: 200,
    },
    {
      field: "requestedBy",
      headerName: "Requested By",
      editable: false,
      flex: 1,
      width: 250,
    },
  ];

  console.log("selectedListItems", selectedListItems);
  // const handleSelectionListItem = (item) => {
  //   let updatedSelectedListItems;
  //   if (selectedListItems.some((selectedItem) => selectedItem.id === item.id)) {
  //     updatedSelectedListItems = selectedListItems.filter(
  //       (selectedItem) => selectedItem.id !== item.id
  //     );
  //   } else {
  //     updatedSelectedListItems = [...selectedListItems, item];
  //   }
  //   setSelectedListItems(updatedSelectedListItems);
  //   setSelectAll(updatedSelectedListItems.length === ruleData.length);
  // };
  // const handleSelectionListItem = (item) => {
  //   const isSelected = selectedListItems.some(
  //     (selectedItem) => selectedItem.id === item.id
  //   );
  //   let updatedSelectedListItems;
  //   if (isSelected) {
  //     updatedSelectedListItems = selectedListItems.filter(
  //       (selectedItem) => selectedItem.id !== item.id
  //     );
  //   } else {
  //     updatedSelectedListItems = [...selectedListItems, item];
  //   }
  //   setSelectedListItems(updatedSelectedListItems);
  // };

  // const handleApply = () => {
  //   dispatch(setFields(selectedListItems));
  //   navigate("/masterDataCockpit/costCenter/changeETCC", {
  //     state: tableRows,
  //   });
  //   console.log(tableRows, "tableRows");
  // };

  const handleApply = () => {
    let filterDataForLock = [];
    if (alignment === "ALL OTHER CHANGES") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "ALL OTHER CHANGES"
      );
      dataListAllOtherChangesSelected.forEach((input) => {
        console.log(input?.name, "name----");
        filteredData?.map((item, index) => {
          console.log(item.MDG_SELECT_OPTION, input?.name, "itemData67");
          if (item.MDG_SELECT_OPTION == input.name) {
            console.log();
            let COHash = {};
            COHash["id"] = index;
            COHash["name"] = item?.MDG_FIELD_NAME;
            filterDataForLock.push(COHash);
          }
        });
      });
    } else if (alignment === "BLOCK") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "BLOCK"
      );
      dataListBlockNamesSelected.forEach((input) => {
        //console.log(input?.name,"name----")
        filteredData?.map((item, index) => {
          if (item.MDG_SELECT_OPTION === input.name) {
            let blockHash = {};
            blockHash["id"] = index;
            blockHash["name"] = item?.MDG_FIELD_NAME;
            ///fieldSelectionCompanyCode.push(COHash)
            filterDataForLock.push(blockHash);
          }
        });
      });
    } else if (alignment === "TEMPORARY BLOCK/UNBLOCK") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "TEMPORARY BLOCK/UNBLOCK"
      );
      dataListTemporaryBlockNamesSelected.forEach((input) => {
        //console.log(input?.name,"name----")
        filteredData?.map((item, index) => {
          if (item.MDG_SELECT_OPTION === input.name) {
            let blockHash = {};
            blockHash["id"] = index;
            blockHash["name"] = item?.MDG_FIELD_NAME;
            ///fieldSelectionCompanyCode.push(COHash)
            filterDataForLock.push(blockHash);
          }
        });
      });
    } else {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "PROFIT CENTER CHANGE"
      );
      dataListProfitCenterChangeSelected.forEach((input) => {
        console.log(input?.name, "name----");
        filteredData?.map((item, index) => {
          if (item.MDG_SELECT_OPTION === input.name) {
            let pcHash = {};
            pcHash["id"] = index;
            pcHash["name"] = item?.MDG_FIELD_NAME;
            ///fieldSelectionCompanyCode.push(COHash)
            filterDataForLock.push(pcHash);
          }
        });
      });
    }
    console.log("myar", filterDataForLock);
    let changedFieldsToCheck = filterDataForLock
      ?.map((item) => item?.name)
      ?.join(",");
    if (downloadMultiple === true && selectedRows.length === 0) {
      if (changedFieldsToCheck !== "") {
        handleChangeDownloadEmpty();
        setDownloadMultiple(false);
        setOpenDialogIDM(false);
        setSelectedListItems([]);
        setDataListAllOtherChangesSelected([]);
        return;
      } else {
        setMessageDialogTitle("Error");
        setMessageDialogMessage("Please Select Any Field To Proceed?");
        setMessageDialogSeverity("danger");
        handleMessageDialogClickOpen();
        return;
      }
    }
    let payload = [];
    if (downloadMultiple === true && selectedRows.length > 0) {
      if (changedFieldsToCheck === "") {
        setMessageDialogTitle("Error");
        setMessageDialogMessage("Please Select Any Field To Proceed?");
        setMessageDialogSeverity("danger");
        handleMessageDialogClickOpen();
        return;
      }
      payload = selectedMassChangeRowData.map((row) => ({
        coArea: row.controllingArea,
        costCenter: row.costCenter,
        changedFieldsToCheck: changedFieldsToCheck,
      }));
    } else {
      if (changedFieldsToCheck === "") {
        setMessageDialogTitle("Error");
        setMessageDialogMessage("Please Select Any Field To Proceed?");
        setMessageDialogSeverity("danger");
        handleMessageDialogClickOpen();
        return;
      }
      payload = [
        {
          coArea: tableRows?.controllingArea,
          costCenter: tableRows?.costCenter,
          changedFieldsToCheck: changedFieldsToCheck,
        },
      ];
    }

    const hSuccess = (response) => {
      const hasError = response.some((item) => item.statusCode !== 200);
      if (!hasError) {
        if (downloadMultiple === true) {
          if (selectedRows.length > 0) {
            handleChangeDownload();
            setDownloadMultiple(false);
            setOpenDialog(false);
            setOpenDialogIDM(false);
            setSelectedListItems([]);
            setDataListAllOtherChangesSelected([]);
          } else if (selectedRows.length === 0) {
            handleChangeDownloadEmpty();
            setDownloadMultiple(false);
            setOpenDialog(false);
            setOpenDialogIDM(false);
            setSelectedListItems([]);
            setDataListAllOtherChangesSelected([]);
          }

          // handleChangeDownload();
          // setOpenDialogIDM(false);
          // setSelectedListItems([]);
        } else {
          dispatch(setSelectedHeader(dataListAllOtherChangesSelected));
          dispatch(setSelectedHeaderTab(selectedTab));
          if (alignment === "ALL OTHER CHANGES") {
            let filterDataWithSelectedData = [];
            const filteredData = fieldSelectionFromIdm.filter(
              (item) => item.MDG_FIELD_SELECTION_LVL === "ALL OTHER CHANGES"
            );
            dataListAllOtherChangesSelected.forEach((input) => {
              console.log(input?.name, "name----");
              filteredData?.map((item, index) => {
                console.log(item.MDG_SELECT_OPTION, input?.name, "itemData67");
                if (item.MDG_SELECT_OPTION == input.name) {
                  console.log();
                  let COHash = {};
                  COHash["id"] = index;
                  COHash["name"] = item?.MDG_FIELD_NAME;
                  ///fieldSelectionCompanyCode.push(COHash)
                  filterDataWithSelectedData.push(COHash);
                }
              });
              // console.log(filterDataWithSelectedData, "filterDataWithSelectedData1")
            });
            let newFilterData = Object?.values(
              filterDataWithSelectedData?.reduce((acc, item) => {
                if (!acc[item?.name]) acc[item?.name] = item;
                return acc;
              }, {})
            );
            dispatch(setFields(newFilterData));
            console.log(newFilterData, "newFilterData");
            // dispatch(setFields(filterDataWithSelectedData))
            //dispatch(setFields(selectedListItems))
          } else if (alignment === "BLOCK") {
            const filteredData = fieldSelectionFromIdm.filter(
              (item) => item.MDG_FIELD_SELECTION_LVL === "BLOCK"
            );
            let filterDataWithSelectedData = [];
            dataListBlockNamesSelected.forEach((input) => {
              //console.log(input?.name,"name----")
              filteredData?.map((item, index) => {
                if (item.MDG_SELECT_OPTION === input.name) {
                  let blockHash = {};
                  blockHash["id"] = index;
                  blockHash["name"] = item?.MDG_FIELD_NAME;
                  ///fieldSelectionCompanyCode.push(COHash)
                  filterDataWithSelectedData.push(blockHash);
                }
              });
              // console.log(filterDataWithSelectedData, "filterDataWithSelectedData1")
            });
            let newFilterData = Object?.values(
              filterDataWithSelectedData?.reduce((acc, item) => {
                if (!acc[item?.name]) acc[item?.name] = item;
                return acc;
              }, {})
            );
            dispatch(setFields(newFilterData));
            console.log(newFilterData, "newFilterData");
            // dispatch(setFields(filterDataWithSelectedData))
          } else if (alignment === "TEMPORARY BLOCK/UNBLOCK") {
            const filteredData = fieldSelectionFromIdm.filter(
              (item) =>
                item.MDG_FIELD_SELECTION_LVL === "TEMPORARY BLOCK/UNBLOCK"
            );
            let filterDataWithSelectedData = [];
            dataListTemporaryBlockNamesSelected.forEach((input) => {
              //console.log(input?.name,"name----")
              filteredData?.map((item, index) => {
                if (item.MDG_SELECT_OPTION === input.name) {
                  let blockHash = {};
                  blockHash["id"] = index;
                  blockHash["name"] = item?.MDG_FIELD_NAME;
                  ///fieldSelectionCompanyCode.push(COHash)
                  filterDataWithSelectedData.push(blockHash);
                }
              });
              // console.log(filterDataWithSelectedData, "filterDataWithSelectedData1")
            });
            let newFilterData = Object?.values(
              filterDataWithSelectedData?.reduce((acc, item) => {
                if (!acc[item?.name]) acc[item?.name] = item;
                return acc;
              }, {})
            );
            dispatch(setFields(newFilterData));
            console.log(newFilterData, "newFilterData");
            // dispatch(setFields(filterDataWithSelectedData))
          } else {
            const filteredData = fieldSelectionFromIdm.filter(
              (item) => item.MDG_FIELD_SELECTION_LVL === "PROFIT CENTER CHANGE"
            );
            let filterDataWithSelectedData = [];
            dataListProfitCenterChangeSelected.forEach((input) => {
              console.log(input?.name, "name----");
              filteredData?.map((item, index) => {
                if (item.MDG_SELECT_OPTION === input.name) {
                  let pcHash = {};
                  pcHash["id"] = index;
                  pcHash["name"] = item?.MDG_FIELD_NAME;
                  ///fieldSelectionCompanyCode.push(COHash)
                  filterDataWithSelectedData.push(pcHash);
                }
              });
            });
            let newFilterData = Object?.values(
              filterDataWithSelectedData?.reduce((acc, item) => {
                if (!acc[item?.name]) acc[item?.name] = item;
                return acc;
              }, {})
            );
            dispatch(setFields(newFilterData));
            console.log(newFilterData, "newFilterData");
          }
          if (selectedTab === "TEMPORARY BLOCK/UNBLOCK") {
            navigate("/masterDataCockpit/costCenter/changeETCCTemporaryBlock", {
              state: tableRows,
            });
          } else {
            navigate("/masterDataCockpit/costCenter/changeETCC", {
              state: tableRows,
            });
          }
        }
      } else {
        const filteredData = response.filter((item) => item.statusCode === 400);
        console.log(filteredData, "fld");
        let duplicateFieldsArr = [];

        filteredData?.map((item, index) => {
          let dataHash = {};
          const costCenter = item?.message
            ?.split("Cost Center: ")[1]
            ?.split(",")[0];
          dataHash["id"] = index;
          dataHash["costCenter"] = costCenter;
          dataHash["reqId"] =
            item?.body?.EditIds?.[0] || item?.body?.MassEditIds?.[0];
          dataHash["requestedBy"] = item?.body?.RequestCreatedBy?.[0];

          duplicateFieldsArr.push(dataHash);
        });

        setDuplicateFieldsData(duplicateFieldsArr);

        setShowTableInDialog(true);

        // setMessageDialogTitle("Error");
        // setMessageDialogMessage("There is an ongoing Request in the flow for the selected fields. Please wait till the ongoing request is completed.");
        // setMessageDialogSeverity("danger");
        // setMessageDialogOK(false);
        // setMessageDialogExtra(true);
        // setDialogOkText("OK");
        // setBlurLoading(false);
        // handleMessageDialogClickOpen();
      }
    };
    const hError = (error) => {
      console.log("Failure");
    };

    doAjax(
      `/${destination_CostCenter_Mass}/alter/checkDuplicateCCRequest`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleApplyEmail = () => {
    console.log("aaaaaaaaaa");
    let filterDataForLock = [];
    if (alignment === "ALL OTHER CHANGES") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "ALL OTHER CHANGES"
      );
      dataListAllOtherChangesSelected.forEach((input) => {
        console.log(input?.name, "name----");
        filteredData?.map((item, index) => {
          console.log(item.MDG_SELECT_OPTION, input?.name, "itemData67");
          if (item.MDG_SELECT_OPTION == input.name) {
            console.log();
            let COHash = {};
            COHash["id"] = index;
            COHash["name"] = item?.MDG_FIELD_NAME;
            filterDataForLock.push(COHash);
          }
        });
      });
    } else if (alignment === "BLOCK") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "BLOCK"
      );
      dataListBlockNamesSelected.forEach((input) => {
        //console.log(input?.name,"name----")
        filteredData?.map((item, index) => {
          if (item.MDG_SELECT_OPTION === input.name) {
            let blockHash = {};
            blockHash["id"] = index;
            blockHash["name"] = item?.MDG_FIELD_NAME;
            ///fieldSelectionCompanyCode.push(COHash)
            filterDataForLock.push(blockHash);
          }
        });
      });
    } else if (alignment === "TEMPORARY BLOCK/UNBLOCK") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "TEMPORARY BLOCK/UNBLOCK"
      );
      dataListTemporaryBlockNamesSelected.forEach((input) => {
        //console.log(input?.name,"name----")
        filteredData?.map((item, index) => {
          if (item.MDG_SELECT_OPTION === input.name) {
            let blockHash = {};
            blockHash["id"] = index;
            blockHash["name"] = item?.MDG_FIELD_NAME;
            ///fieldSelectionCompanyCode.push(COHash)
            filterDataForLock.push(blockHash);
          }
        });
      });
    } else {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "PROFIT CENTER CHANGE"
      );
      dataListProfitCenterChangeSelected.forEach((input) => {
        console.log(input?.name, "name----");
        filteredData?.map((item, index) => {
          if (item.MDG_SELECT_OPTION === input.name) {
            let pcHash = {};
            pcHash["id"] = index;
            pcHash["name"] = item?.MDG_FIELD_NAME;
            ///fieldSelectionCompanyCode.push(COHash)
            filterDataForLock.push(pcHash);
          }
        });
      });
    }
    console.log("myar", filterDataForLock);
    let changedFieldsToCheck = filterDataForLock
      ?.map((item) => item?.name)
      ?.join(",");
    if (downloadMultiple === true && selectedRows.length === 0) {
      if (changedFieldsToCheck !== "") {
        handleChangeDownloadEmptyEmail();
        setDownloadMultiple(false);
        setOpenDialogIDM(false);
        setSelectedListItems([]);
        setDataListAllOtherChangesSelected([]);
        return;
      } else {
        setMessageDialogTitle("Error");
        setMessageDialogMessage("Please Select Any Field To Proceed?");
        setMessageDialogSeverity("danger");
        handleMessageDialogClickOpen();
        return;
      }
    }
    let payload = [];
    if (downloadMultiple === true && selectedRows.length > 0) {
      if (changedFieldsToCheck === "") {
        setMessageDialogTitle("Error");
        setMessageDialogMessage("Please Select Any Field To Proceed?");
        setMessageDialogSeverity("danger");
        handleMessageDialogClickOpen();
        return;
      }
      payload = selectedMassChangeRowData.map((row) => ({
        coArea: row.controllingArea,
        costCenter: row.costCenter,
        changedFieldsToCheck: changedFieldsToCheck,
      }));
    } else {
      if (changedFieldsToCheck === "") {
        setMessageDialogTitle("Error");
        setMessageDialogMessage("Please Select Any Field To Proceed?");
        setMessageDialogSeverity("danger");
        handleMessageDialogClickOpen();
        return;
      }
      payload = [
        {
          coArea: tableRows?.controllingArea,
          costCenter: tableRows?.costCenter,
          changedFieldsToCheck: changedFieldsToCheck,
        },
      ];
    }

    const hSuccess = (response) => {
      const hasError = response.some((item) => item.statusCode !== 200);
      if (!hasError) {
        if (downloadMultiple === true) {
          if (selectedRows.length > 0) {
            handleChangeDownloadEmail();
            setDownloadMultiple(false);
            setOpenDialog(false);
            setOpenDialogIDM(false);
            setSelectedListItems([]);
            setDataListAllOtherChangesSelected([]);
          } else if (selectedRows.length === 0) {
            handleChangeDownloadEmptyEmail();
            setDownloadMultiple(false);
            setOpenDialog(false);
            setOpenDialogIDM(false);
            setSelectedListItems([]);
            setDataListAllOtherChangesSelected([]);
          }

          // handleChangeDownload();
          // setOpenDialogIDM(false);
          // setSelectedListItems([]);
        } else {
          dispatch(setSelectedHeader(dataListAllOtherChangesSelected));
          dispatch(setSelectedHeaderTab(selectedTab));
          if (alignment === "ALL OTHER CHANGES") {
            let filterDataWithSelectedData = [];
            const filteredData = fieldSelectionFromIdm.filter(
              (item) => item.MDG_FIELD_SELECTION_LVL === "ALL OTHER CHANGES"
            );
            dataListAllOtherChangesSelected.forEach((input) => {
              console.log(input?.name, "name----");
              filteredData?.map((item, index) => {
                console.log(item.MDG_SELECT_OPTION, input?.name, "itemData67");
                if (item.MDG_SELECT_OPTION == input.name) {
                  console.log();
                  let COHash = {};
                  COHash["id"] = index;
                  COHash["name"] = item?.MDG_FIELD_NAME;
                  ///fieldSelectionCompanyCode.push(COHash)
                  filterDataWithSelectedData.push(COHash);
                }
              });
              // console.log(filterDataWithSelectedData, "filterDataWithSelectedData1")
            });
            let newFilterData = Object?.values(
              filterDataWithSelectedData?.reduce((acc, item) => {
                if (!acc[item?.name]) acc[item?.name] = item;
                return acc;
              }, {})
            );
            dispatch(setFields(newFilterData));
            console.log(newFilterData, "newFilterData");
            // dispatch(setFields(filterDataWithSelectedData))
            //dispatch(setFields(selectedListItems))
          } else if (alignment === "BLOCK") {
            const filteredData = fieldSelectionFromIdm.filter(
              (item) => item.MDG_FIELD_SELECTION_LVL === "BLOCK"
            );
            let filterDataWithSelectedData = [];
            dataListBlockNamesSelected.forEach((input) => {
              //console.log(input?.name,"name----")
              filteredData?.map((item, index) => {
                if (item.MDG_SELECT_OPTION === input.name) {
                  let blockHash = {};
                  blockHash["id"] = index;
                  blockHash["name"] = item?.MDG_FIELD_NAME;
                  ///fieldSelectionCompanyCode.push(COHash)
                  filterDataWithSelectedData.push(blockHash);
                }
              });
              // console.log(filterDataWithSelectedData, "filterDataWithSelectedData1")
            });
            let newFilterData = Object?.values(
              filterDataWithSelectedData?.reduce((acc, item) => {
                if (!acc[item?.name]) acc[item?.name] = item;
                return acc;
              }, {})
            );
            dispatch(setFields(newFilterData));
            console.log(newFilterData, "newFilterData");
            // dispatch(setFields(filterDataWithSelectedData))
          } else if (alignment === "TEMPORARY BLOCK/UNBLOCK") {
            const filteredData = fieldSelectionFromIdm.filter(
              (item) =>
                item.MDG_FIELD_SELECTION_LVL === "TEMPORARY BLOCK/UNBLOCK"
            );
            let filterDataWithSelectedData = [];
            dataListTemporaryBlockNamesSelected.forEach((input) => {
              //console.log(input?.name,"name----")
              filteredData?.map((item, index) => {
                if (item.MDG_SELECT_OPTION === input.name) {
                  let blockHash = {};
                  blockHash["id"] = index;
                  blockHash["name"] = item?.MDG_FIELD_NAME;
                  ///fieldSelectionCompanyCode.push(COHash)
                  filterDataWithSelectedData.push(blockHash);
                }
              });
              // console.log(filterDataWithSelectedData, "filterDataWithSelectedData1")
            });
            let newFilterData = Object?.values(
              filterDataWithSelectedData?.reduce((acc, item) => {
                if (!acc[item?.name]) acc[item?.name] = item;
                return acc;
              }, {})
            );
            dispatch(setFields(newFilterData));
            console.log(newFilterData, "newFilterData");
            // dispatch(setFields(filterDataWithSelectedData))
          } else {
            const filteredData = fieldSelectionFromIdm.filter(
              (item) => item.MDG_FIELD_SELECTION_LVL === "PROFIT CENTER CHANGE"
            );
            let filterDataWithSelectedData = [];
            dataListProfitCenterChangeSelected.forEach((input) => {
              console.log(input?.name, "name----");
              filteredData?.map((item, index) => {
                if (item.MDG_SELECT_OPTION === input.name) {
                  let pcHash = {};
                  pcHash["id"] = index;
                  pcHash["name"] = item?.MDG_FIELD_NAME;
                  ///fieldSelectionCompanyCode.push(COHash)
                  filterDataWithSelectedData.push(pcHash);
                }
              });
            });
            let newFilterData = Object?.values(
              filterDataWithSelectedData?.reduce((acc, item) => {
                if (!acc[item?.name]) acc[item?.name] = item;
                return acc;
              }, {})
            );
            dispatch(setFields(newFilterData));
            console.log(newFilterData, "newFilterData");
          }
          if (selectedTab === "TEMPORARY BLOCK/UNBLOCK") {
            navigate("/masterDataCockpit/costCenter/changeETCCTemporaryBlock", {
              state: tableRows,
            });
          } else {
            navigate("/masterDataCockpit/costCenter/changeETCC", {
              state: tableRows,
            });
          }
        }
      } else {
        const filteredData = response.filter((item) => item.statusCode === 400);
        console.log(filteredData, "fld");
        let duplicateFieldsArr = [];

        filteredData?.map((item, index) => {
          let dataHash = {};
          const costCenter = item?.message
            ?.split("Cost Center: ")[1]
            ?.split(",")[0];
          dataHash["id"] = index;
          dataHash["costCenter"] = costCenter;
          dataHash["reqId"] =
            item?.body?.EditIds?.[0] || item?.body?.MassEditIds?.[0];
          dataHash["requestedBy"] = item?.body?.RequestCreatedBy?.[0];

          duplicateFieldsArr.push(dataHash);
        });

        setDuplicateFieldsData(duplicateFieldsArr);

        setShowTableInDialog(true);

        // setMessageDialogTitle("Error");
        // setMessageDialogMessage("There is an ongoing Request in the flow for the selected fields. Please wait till the ongoing request is completed.");
        // setMessageDialogSeverity("danger");
        // setMessageDialogOK(false);
        // setMessageDialogExtra(true);
        // setDialogOkText("OK");
        // setBlurLoading(false);
        // handleMessageDialogClickOpen();
      }
    };
    const hError = (error) => {
      console.log("Failure");
    };

    doAjax(
      `/${destination_CostCenter_Mass}/alter/checkDuplicateCCRequest`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  // const handleApply = () => {
  //   if (downloadMultiple === true) {
  //     if (selectedRows.length > 0) {
  //       handleChangeDownload();
  //       setOpenDialog(false);
  //       setOpenDialogIDM(false);
  //       setSelectedListItems([]);
  //     } else if (selectedRows.length === 0) {
  //       handleChangeDownloadEmpty();
  //       setOpenDialog(false);
  //       setOpenDialogIDM(false);
  //       setSelectedListItems([]);
  //     }

  //     // handleChangeDownload();
  //     // setOpenDialogIDM(false);
  //     // setSelectedListItems([]);
  //   } else {
  //     dispatch(setSelectedHeader(dataListAllOtherChangesSelected));
  //     dispatch(setSelectedHeaderTab(selectedTab));
  //     if (alignment === "ALL OTHER CHANGES") {
  //       let filterDataWithSelectedData = [];
  //       const filteredData = fieldSelectionFromIdm.filter(
  //         (item) => item.MDG_FIELD_SELECTION_LVL === "ALL OTHER CHANGES"
  //       );
  //       dataListAllOtherChangesSelected.forEach((input) => {
  //         console.log(input?.name, "name----");
  //         filteredData?.map((item, index) => {
  //           console.log(item.MDG_SELECT_OPTION, input?.name, "itemData67");
  //           if (item.MDG_SELECT_OPTION == input.name) {
  //             console.log();
  //             let COHash = {};
  //             COHash["id"] = index;
  //             COHash["name"] = item?.MDG_FIELD_NAME;
  //             ///fieldSelectionCompanyCode.push(COHash)
  //             filterDataWithSelectedData.push(COHash);
  //           }
  //         });
  //         // console.log(filterDataWithSelectedData, "filterDataWithSelectedData1")
  //       });
  //       let newFilterData = Object?.values(
  //         filterDataWithSelectedData?.reduce((acc, item) => {
  //           if (!acc[item?.name]) acc[item?.name] = item;
  //           return acc;
  //         }, {})
  //       );
  //       dispatch(setFields(newFilterData));
  //       console.log(newFilterData, "newFilterData");
  //       // dispatch(setFields(filterDataWithSelectedData))
  //       //dispatch(setFields(selectedListItems))
  //     } else if (alignment === "BLOCK") {
  //       const filteredData = fieldSelectionFromIdm.filter(
  //         (item) => item.MDG_FIELD_SELECTION_LVL === "BLOCK"
  //       );
  //       let filterDataWithSelectedData = [];
  //       dataListBlockNamesSelected.forEach((input) => {
  //         //console.log(input?.name,"name----")
  //         filteredData?.map((item, index) => {
  //           if (item.MDG_SELECT_OPTION === input.name) {
  //             let blockHash = {};
  //             blockHash["id"] = index;
  //             blockHash["name"] = item?.MDG_FIELD_NAME;
  //             ///fieldSelectionCompanyCode.push(COHash)
  //             filterDataWithSelectedData.push(blockHash);
  //           }
  //         });
  //         // console.log(filterDataWithSelectedData, "filterDataWithSelectedData1")
  //       });
  //       let newFilterData = Object?.values(
  //         filterDataWithSelectedData?.reduce((acc, item) => {
  //           if (!acc[item?.name]) acc[item?.name] = item;
  //           return acc;
  //         }, {})
  //       );
  //       dispatch(setFields(newFilterData));
  //       console.log(newFilterData, "newFilterData");
  //       // dispatch(setFields(filterDataWithSelectedData))
  //     } else {
  //       const filteredData = fieldSelectionFromIdm.filter(
  //         (item) => item.MDG_FIELD_SELECTION_LVL === "PROFIT CENTER CHANGE"
  //       );
  //       let filterDataWithSelectedData = [];
  //       dataListProfitCenterChangeSelected.forEach((input) => {
  //         console.log(input?.name, "name----");
  //         filteredData?.map((item, index) => {
  //           if (item.MDG_SELECT_OPTION === input.name) {
  //             let pcHash = {};
  //             pcHash["id"] = index;
  //             pcHash["name"] = item?.MDG_FIELD_NAME;
  //             ///fieldSelectionCompanyCode.push(COHash)
  //             filterDataWithSelectedData.push(pcHash);
  //           }
  //         });
  //         // console.log(filterDataWithSelectedData, "filterDataWithSelectedData1")
  //       });
  //       let newFilterData = Object?.values(
  //         filterDataWithSelectedData?.reduce((acc, item) => {
  //           if (!acc[item?.name]) acc[item?.name] = item;
  //           return acc;
  //         }, {})
  //       );
  //       dispatch(setFields(newFilterData));
  //       console.log(newFilterData, "newFilterData");
  //       // dispatch(setFields(filterDataWithSelectedData))
  //       // console.log(filterDataWithSelectedData, "filterDataWithSelectedData")
  //     }
  //     navigate("/masterDataCockpit/costCenter/changeETCC", {
  //       state: tableRows,
  //     });
  //   }
  //   console.log(tableRows, "tableRows");
  // };

  // const handleApply = () => {
  //   // Map selected items to their corresponding fields based on MDG_FEILD_NAME
  //   const selectedFields = selectedListItems.map((item) => {
  //     const selectedItem = ruleData.find(
  //       (data) => data.MDG_SELECT_OPTION === item.MDG_SELECT_OPTION
  //     );
  //     return {
  //       MDG_FEILD_NAME: selectedItem ? selectedItem.MDG_FEILD_NAME : "",
  //       // Add other relevant data from selected item if needed
  //     };
  //   });

  //   // Pass selected fields to the next page
  //   navigate("/masterDataCockpit/sunoco/costCenterSunoco/changeCCSunocoField", {
  //     state: selectedFields,
  //   });
  // };
  const handleSelectColumnDialogClose = () => {
    setOpenSelectColumnDialog(false);
  };
  const handleOpenDialogIDM = (rows) => {
    console.log(rows, "target");
    setOpenDialog(true);
    // setOpenDialogIDM(true);
    setTableRows(rows);
    // getListofData(); // Fetch data when the dialog is opened
  };
  useEffect(() => {
    if (tableRows?.blockingStatus === "Blocked") {
      // const filteredData = dataListBlockNamesSelected.filter(item => item.templateName !== "Block");
      setFilteredRuleData(true);
    } else {
      setFilteredRuleData(false);
    }
  }, [tableRows]);
  useEffect(() => {
    const isAnyBlocked = requiredArrayDetailsMass?.some(
      (row) => row.blockingStatus === "Blocked"
    );

    if (isAnyBlocked) {
      setFilteredRuleDataMass(true);
    } else {
      setFilteredRuleDataMass(false);
    }
  }, [requiredArrayDetailsMass]);

  const handleCloseDialogIDM = () => {
    setDataListAllOtherChangesSelected([]);
    setOpenDialogIDM(false);
    setSelectAll(false);
    setSelectedListItems([]);
    //setSelectAll(!selectAll);
  };
  const handleCloseDialog = () => {
    setDataListAllOtherChangesSelected([]);
    setSelectAll(false);
    setOpenDialog(false);
  };
  const handleDownloadDialogClose = () => {
    setOpenDownloadDialog(false);
    setDownloadType("systemGenerated");
  };
  const handleDownloadChangeDialogClose = () => {
    setOpenDownloadChangeDialog(false);
    setDownloadType("systemGenerated");
  };
  const handleDownloadTypeChange = (event) => {
    setDownloadType(event?.target?.value);
  };
  const handleMultipleDownloadTypeChange = (event) => {
    setDownloadType(event?.target?.value);
  };
  const onDownloadTypeChange = () => {
    console.log("downloadType", downloadType);
    if (downloadType === "systemGenerated") {
      handleCreateDownload();
      handleDownloadDialogClose();
    }
    if (downloadType === "mailGenerated") {
      handleEmailDownload();
      handleDownloadDialogClose();
    }
  };

  const onMultipleDownloadTypeChange = () => {
    // debugger
    console.log("changedownloadtype", downloadType);
    if (downloadType === "systemGenerated") {
      handleApply();
      handleDownloadChangeDialogClose();
    }
    if (downloadType === "mailGenerated") {
      handleApplyEmail();
      handleDownloadChangeDialogClose();
    }
  };

  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };

  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };

  const handleSearchDialogClickOpen = () => {
    setOpenSearchDialog(true);
  };

  const handleSearchDialogClose = () => {
    setOpenSearchDialog(false);
  };

  const [anchorEl_Preset, setAnchorEl] = useState(null);
  const openAnchor = Boolean(anchorEl_Preset);

  const handleSnackBarOpen = () => {
    setopenSnackbar(true);
  };

  const [presets, setPresets] = useState(null);
  const [presetName, setPresetName] = useState(null);

  const handleClear = () => {
    dispatch(commonFilterClear({ module: "CostCenter" }));
    setselectedCCcategory([]);
    setselectedComanyCode([]);
    setselectedPersonResponsible([]);
    setselectedUserResponsible([]);
    setselectedProfitCenter([]);
    setselectedCostCenter([]);
    setselectedCostCenterName([]);
    setselectedLocation([]);
    setselectedDescription([]);
    setselectedCreatedBy([]);
    setselectedStreet([]);
    setSelectedValues({});

    setselectedPresetCCcategory([]);
    setselectedPresetComanyCode([]);
    setselectedPresetPersonResponsible([]);
    setselectedPresetUserResponsible([]);
    setselectedPresetProfitCenter([]);
    setselectedPresetCostCenter([]);
    setselectedPresetCostCenterName([]);
    setselectedPresetLocation([]);
    setselectedPresetDescription([]);
    setselectedPresetCreatedBy([]);
    setselectedPresetStreet([]);
    setSelectedPresetValues({});

    setFilterFieldData({});
  };

  const uploadExcel = (file) => {
    setLoaderMessage(
      "Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience."
    );
    setBlurLoading(true);
    // console.log(file);
    const formData = new FormData();
    [...file].forEach((item) => formData.append("files", item));
    formData.append("dtName", "MDG_CC_FIELD_CONFIG");
    formData.append("version", "v3");
    formData.append("IsSunoco", "false");
    if (handleMassModeCC === REQUEST_TYPE.CHANGE) {
      var uploadUrl = `/${destination_CostCenter_Mass}/massAction/getAllCostCenterFromExcelForMassChange`;
      const hSuccess = (data) => {
        // console.log(data, "example");
        // setIsLoading();
        if (data.statusCode === 200) {
          setEnableDocumentUpload(false);
          // dispatch(setControllingArea(data?.body?.controllingArea));
          // dispatch(setMultipleProfitCenterData(data?.body));
          console.log("uploadeddd", data?.body);
          setMessageDialogTitle("Create");
          // const dialogMessage = `

          //   2. Mass Upload Process has Started in the background. As soon as the request ID is generated, you will receive a notification and mail for it containing the new request ID number.
          //   3. Then you can visit the Request Bench Tab and search for that request ID and do further actions on it.
          //   4. Note - All request IDs generated in the background would initially have the status Draft.
          //   5. Ok button
          // `.split('\n')?.map(line => line.trim()).join('\n');
          const content = (
            <Typography component="div">
              <ul>
                <li>
                  Mass Upload Process has Started in the background. As soon as
                  the request ID is generated, you will receive a notification
                  and mail for it containing the new request ID number.
                </li>
                <li>
                  Then you can visit the Request Bench Tab and search for that
                  request ID and do further actions on it.
                </li>
                <li>
                  Note - All request IDs generated in the background would
                  initially have the status Draft.
                </li>
              </ul>
            </Typography>
          );
          handleMessageDialogClickOpen();
          setMessageDialogTitle("Header - Information");
          setMessageDialogMessage(content);
          setMessageDialogSeverity("success");
          setMessageDialogOK(false);
          setsuccessMsg(true);
          setMessageDialogExtra(true);
          handleSnackBarOpen();
          setBlurLoading(false);
          setLoaderMessage("");

          // navigate(`/masterDataCockpit/profitCenter/createMultipleProfitCenter`);
        } else {
          setEnableDocumentUpload(false);
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          setMessageDialogMessage(
            "Upload failed. Incorrect template tab name, please recheck upload file"
          );
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          setBlurLoading(false);
          setLoaderMessage("");
        }
        // handleClose();
      };
      const hError = (error) => {
        setMessageDialogTitle("Error");
        setMessageDialogMessage(error?.message);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        setDialogOkText("OK");
        setBlurLoading(false);
        setLoaderMessage("");
        handleMessageDialogClickOpen();
      };
      doAjax(uploadUrl, "postformdata", hSuccess, hError, formData);
    } else {
      var uploadUrl = `/${destination_CostCenter_Mass}/massAction/getAllCostCenterFromExcel`;
      const hSuccess = (data) => {
        // console.log(data, "example");
        // setIsLoading();
        if (data.statusCode === 200) {
          setEnableDocumentUpload(false);
          // dispatch(setControllingArea(data?.body?.controllingArea));
          // dispatch(setMultipleProfitCenterData(data?.body));
          console.log("uploadeddd", data?.body);
          setMessageDialogTitle("Create");
          // const dialogMessage = `

          //   2. Mass Upload Process has Started in the background. As soon as the request ID is generated, you will receive a notification and mail for it containing the new request ID number.
          //   3. Then you can visit the Request Bench Tab and search for that request ID and do further actions on it.
          //   4. Note - All request IDs generated in the background would initially have the status Draft.
          //   5. Ok button
          // `.split('\n')?.map(line => line.trim()).join('\n');
          const content = (
            <Typography component="div">
              <ul>
                <li>
                  Mass Upload Process has Started in the background. As soon as
                  the request ID is generated, you will receive a notification
                  and mail for it containing the new request ID number.
                </li>
                <li>
                  Then you can visit the Request Bench Tab and search for that
                  request ID and do further actions on it.
                </li>
                <li>
                  Note - All request IDs generated in the background would
                  initially have the status Draft.
                </li>
              </ul>
            </Typography>
          );
          handleMessageDialogClickOpen();
          setMessageDialogTitle("Header - Information");
          setMessageDialogMessage(content);
          setMessageDialogSeverity("success");
          setMessageDialogOK(false);
          setsuccessMsg(true);
          setMessageDialogExtra(true);
          handleSnackBarOpen();
          setBlurLoading(false);
          setLoaderMessage("");

          // navigate(`/masterDataCockpit/profitCenter/createMultipleProfitCenter`);
        } else if (data.statusCode === 429) {
          // Handling status code 429 (Too Many Requests)
          setBlurLoading(false);
          setLoaderMessage("");
          setEnableDocumentUpload(false);
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          // Map the body.message from the response to display it in the UI
          setMessageDialogMessage(data?.message || "Please try again.");
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          setIsLoading(false);
        } else {
          setEnableDocumentUpload(false);
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          setMessageDialogMessage(
            "Upload failed. Incorrect template tab name, please recheck upload file"
          );
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          setBlurLoading(false);
          setLoaderMessage("");
        }
        handleClose();
      };
      const hError = (error) => {
        setMessageDialogTitle("Error");
        setMessageDialogMessage(error?.message);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        setDialogOkText("OK");
        setBlurLoading(false);
        setLoaderMessage("");
        handleMessageDialogClickOpen();
      };
      doAjax(uploadUrl, "postformdata", hSuccess, hError, formData);
    }
  };
  const handleSelectionModelChange = (newSelection) => {
    console.log("newselection", newSelection);
    // handlesna;
    setSelectedRows(newSelection);
    let filterValueColumns = columns?.map((t) => t.field);
    const selectedRowsDetails = rmDataRows.filter((row) =>
      newSelection.includes(row.id)
    );
    let requiredArrayDetails = [];
    selectedRowsDetails?.map((s) => {
      console.log("sssssss", s);
      let requiredObject = {};
      filterValueColumns.forEach((y) => {
        console.log("yyyyy", s[y]);
        if (s[y] !== null) {
          requiredObject[y] = s[y] || "";
        }
      });
      requiredObject["controllingArea"] = s["controllingArea"];
      requiredArrayDetails.push(requiredObject);
      setSelectedMassChangeRowData(requiredArrayDetails);
      console.log("requiredArrayDetails", requiredArrayDetails);
      setRequiredArrayDetailsMass(requiredArrayDetails);
    });
  };

  // const onRowsSelectionHandler = (ids) => {
  //   //Selected Columns stored here
  //   const selectedRowsData = ids?.map((id) =>
  //     rmDataRows.find((row) => row.id === id)
  //   );
  //   var compCodes = selectedRowsData?.map((row) => row.company);
  //   var companySet = new Set(compCodes);
  //   var vendors = selectedRowsData?.map((row) => row.vendor);
  //   var vendorSet = new Set(vendors);
  //   var paymentTerms = selectedRowsData?.map((row) => row.paymentTerm);
  //   var paymentTermsSet = new Set(paymentTerms);
  //   if (selectedRowsData.length > 0) {
  //     if (companySet.size === 1) {
  //       if (vendorSet.size === 1) {
  //         if (paymentTermsSet.size !== 1) {
  //           setDisableButton(true);
  //           setMessageDialogTitle("Error");
  //           setMessageDialogMessage(
  //             "Invoice cannot be generated for vendors with different payment terms"
  //           );
  //           setMessageDialogSeverity("danger");
  //           handleMessageDialogClickOpen();
  //         } else setDisableButton(false);
  //       } else {
  //         setDisableButton(true);
  //         setMessageDialogTitle("Error");
  //         setMessageDialogMessage(
  //           "Invoice cannot be generated for multiple suppliers"
  //         );
  //         setMessageDialogSeverity("danger");
  //         handleMessageDialogClickOpen();
  //       }
  //     } else {
  //       setDisableButton(true);
  //       setMessageDialogTitle("Error");
  //       setMessageDialogMessage(
  //         "Invoice cannot be generated for multiple companies"
  //       );
  //       setMessageDialogSeverity("danger");
  //       handleMessageDialogClickOpen();
  //     }
  //   } else {
  //     setDisableButton(true); //Enable the Create E-Invoice button when at least one row is selected and no two companys or vendors are same
  //   }
  //   setSelectedRow(ids); //Setting the ids(PO Numbers) of selected rows
  //   setSelectedDetails(selectedRowsData); //Setting the entire data of a selected row
  // };
  function refreshPage() {
    dispatch(commonFilterClear({ module: "CostCenter" }));
    getFilter();
  }

  const [company, setCompany] = useState([]);
  const [Companyid, setCompanyid] = useState([]);

  // let { poId } = useParams();
  const [open, setOpen] = useState(false);
  const [matAnchorEl, setMatAnchorEl] = useState(null);
  const [materialDetails, setMaterialDetails] = useState(null);
  const [itemDataRows, setItemDataRows] = useState([]);

  const handlePODetailsClick = (event) => {
    setOpendialog3(true);
  };

  const matOpen = Boolean(matAnchorEl);
  const popperId = matOpen ? "simple-popper" : undefined;

  const handleClose = () => {
    setOpen(false);
  };

  const [poNum, setPONum] = useState(null);
  const [id, setID] = useState("");
  const columns = [
    {
      field: "companyCode",
      headerName: "Company Code",
      editable: false, //dd
      flex: 1,
    },
    {
      field: "costCenter",
      headerName: "Cost Center",
      editable: false, //dd
      flex: 1,
    },
    // {
    //   field: "CostCenterName",
    //   headerName: "Cost Center Name",
    //   editable: false, //dd
    //   flex: 1,
    // },
    {
      field: "description",
      headerName: "Long Description",
      editable: false, //text
      flex: 1,
    },
    {
      field: "costCenterCategory",
      headerName: "Cost Center Category", //dd
      editable: false,
      flex: 1,
    },
    {
      field: "personResponsible",
      headerName: "Person Responsible", //dd
      editable: false,
      flex: 1,
    },
    {
      field: "userResponsible",
      headerName: "User Responsible", //dd
      editable: false,
      flex: 1,
    },

    // {
    //   field: "controllingArea",
    //   headerName: "Controlling Area",
    //   editable: false, //dd
    //   flex: 1,
    // },

    // {
    //   field: "profitCenter",
    //   headerName: "Profit Center", //dd
    //   editable: false,
    //   flex: 1,
    // },
    // {
    //   field: "hierarchyArea",
    //   headerName: "Hierarchy Area", //dd
    //   editable: false,
    //   flex: 1,
    // },

    {
      field: "blockingStatus",
      headerName: "Blocking Status", //dd
      editable: false,
      flex: 1,
    },
  ];
  const actionColumn = [
    {
      field: "actions",
      align: "center",
      flex: 1, // Use flex for responsive width
      headerAlign: "center",
      headerName: "Actions",
      sortable: false,
      renderCell: (params) => (
        <div>
          <Tooltip title="Change">
            <IconButton
              aria-label="View Metadata"
              onClick={() => handleOpenDialogIDM(params?.row)} // Open dialog when edit button is clicked
            >
              <EditOutlinedIcon />
            </IconButton>
          </Tooltip>
        </div>
      ),
    },
  ];
  const dynamicFilterColumns = selectedOptions
    ?.map((option) => {
      const field = titleToFieldMapping[option]; // Get the corresponding field from the mapping
      if (!field) {
        return null; // Handle the case when the field doesn't exist in the mapping
      }
      return {
        field: field, // Use the field name from the mapping
        headerName: option,
        editable: false,
        flex: 1,
      };
    })
    .filter((column) => column !== null); // Remove any null columns

  const allColumns = [
    ...columns,
    ...dynamicFilterColumns,
    // ...actionColumn,
    // Other fixed and dynamic columns as needed
  ];
  const capitalize = (str) => {
    //  str?.map((str)=>{
    const arr = str.split(" ");
    for (var i = 0; i < arr.length; i++) {
      arr[i] = arr[i].charAt(0) + arr[i].slice(1).toLowerCase();
    }

    const str2 = arr.join(" ");
    return str2;
    //  })
  };

  // useEffect(() => {
  //   getFilter();
  // }, []);

  // useEffect(() => {
  //   if ((rmSearchForm?.company).length) {
  //     getVendorDetails();
  //     getPlantCodeSet()
  //   }
  // }, [rmSearchForm?.company]);

  // let serviceRequestForm_Component = new createServiceRequestForm(Status_ServiceReqForm, setStatus_ServiceReqForm)
  // <-- Function for taking screenshot (Export button) -->
  let ref_elementForExport = useRef(null);
  // let exportAsPicture = () => {
  //   setTimeout(() => {
  //     captureScreenShot("Material-Single");
  //   }, 100);
  // };
  const functions_ExportAsExcel = {
    convertJsonToExcel: () => {
      let excelColumns = [];
      allColumns.forEach((item) => {
        if (item.headerName.toLowerCase() !== "actions" && !item.hide) {
          excelColumns.push({ header: item.headerName, key: item.field });
        }
      });
      saveExcel({
        fileName: `Cost Center Data-${moment(presentDate).format(
          "DD-MMM-YYYY"
        )}`,
        columns: excelColumns,
        rows: rmDataRows,
      });
    },
    button: () => {
      return (
        <Button
          sx={{
            textTransform: "capitalize",
            position: "absolute",
            right: 0,
            top: 0,
          }}
          onClick={() => functions_ExportAsExcel.convertJsonToExcel()}
        >
          Download
        </Button>
      );
    },
  };

  const handleSnackBarClose = () => {
    if (validateFlag) {
      setopenSnackbar(false);
      //setValidateFlag(false);
    } else {
      setopenSnackbar(false);
      navigate("/masterDataCockpit/costCenter");
    }
  };

  const handleCreateDownload = () => {
    setLoaderMessage(
      "Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience."
    );
    setBlurLoading(true);
    var downloadPayload = selectedMassChangeRowData?.map((x) => {
      return {
        costCenter: x.costCenter,
        controllingArea: x.controllingArea,
      };
    });
    const params = new URLSearchParams({
      dtName: "MDG_CC_FIELD_CONFIG",
      version: "v3",
    });
    let hSuccess = (response) => {
      if (response?.size !== 0 || response?.type !== "") {
        const href = URL.createObjectURL(response);
        const link = document.createElement("a");
        link.href = href;
        link.setAttribute("download", `Cost Center Mass Create.xlsx`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(href);
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Success");

        setMessageDialogMessage(
          `Cost Center Mass Create.xlsx has been downloaded successfully`
        );
        setMessageDialogSeverity("success");
        setBlurLoading(false);
        setLoaderMessage("");
      } else {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        // setMessageDialogMessage(`${error.message}`);
        setMessageDialogMessage("Please try again.");
        setMessageDialogSeverity("danger");
        setBlurLoading(false);
        setLoaderMessage("");
      }
    };
    let hError = (error) => {
      if (error.message) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        // setMessageDialogMessage(`${error.message}`);
        setMessageDialogMessage(
          "Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>"
        );
        setMessageDialogSeverity("danger");
        setBlurLoading(false);
        setLoaderMessage("");
      }
    };
    doAjax(
      `/${destination_CostCenter_Mass}/excel/downloadExcel?${params.toString()}&rolePrefix=ETP`,
      "getblobfile",
      hSuccess,
      hError
    );
  };
  const handleEmailDownload = () => {
    // setLoaderMessage("Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience.")
    setBlurLoading(true);
    var downloadPayload = selectedMassChangeRowData?.map((x) => {
      return {
        costCenter: x.costCenter,
        controllingArea: x.controllingArea,
      };
    });
    const params = new URLSearchParams({
      dtName: "MDG_CC_FIELD_CONFIG",
      version: "v3",
    });
    let hSuccess = (response) => {
      // const href = URL.createObjectURL(response);
      // const link = document.createElement("a");
      // link.href = href;
      // link.setAttribute("download", `Cost Center Mass Create.xlsx`);
      // document.body.appendChild(link);
      // link.click();
      // document.body.removeChild(link);
      // URL.revokeObjectURL(href);
      setMessageDialogMessage(
        `Download has been started. You will get the Excel file via email`
      );
      handleMessageDialogClickOpen();
      setMessageDialogTitle("Success");

      setMessageDialogSeverity("success");
      setBlurLoading(false);
      setLoaderMessage("");
    };
    let hError = (error) => {
      if (error.message) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        // setMessageDialogMessage(`${error.message}`);
        setMessageDialogMessage(
          "Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>"
        );
        setMessageDialogSeverity("danger");
        setBlurLoading(false);
        setLoaderMessage("");
      }
    };
    doAjax(
      `/${destination_CostCenter_Mass}/excel/downloadExcelInMail?${params.toString()}&rolePrefix=ETP`,
      "getblobfile",
      hSuccess,
      hError
    );
  };
  const handleChangeDownload = () => {
    setLoaderMessage(
      "Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience."
    );
    setBlurLoading(true);
    console.log("selectedMassChangeRowData", selectedMassChangeRowData);
    var downloadPayload = selectedMassChangeRowData?.map((x) => {
      // console.log("xxxxx",x),
      return {
        costCenter: x.costCenter,
        controllingArea: x.controllingArea,
      };
    });
    // const selectedItemArr = ["Cost Center", "Controlling Area"];
    let selectedItemArr = [];
    if (alignment === "PROFIT CENTER CHANGE") {
      selectedItemArr = ["Cost Center", "Controlling Area", "Comp Code"];
    } else {
      selectedItemArr = ["Cost Center", "Controlling Area"];
    }
    let filterDataWithSelectedData = [];
    let selectedOptionsForDownload = new Set();

    const addUniqueFields = (filteredData, selectedList) => {
      const uniqueFields = new Set();
      selectedList.forEach((input) => {
        filteredData?.forEach((item, index) => {
          if (
            item.MDG_SELECT_OPTION === input.name &&
            !uniqueFields.has(item.MDG_FIELD_NAME)
          ) {
            uniqueFields.add(item.MDG_FIELD_NAME);
            filterDataWithSelectedData.push({
              id: index,
              name: item.MDG_FIELD_NAME,
            });

            selectedOptionsForDownload.add(item.MDG_SELECT_OPTION); // Add the selected option to the set
          }
        });
      });
    };

    if (alignment === "ALL OTHER CHANGES") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "ALL OTHER CHANGES"
      );
      addUniqueFields(filteredData, dataListAllOtherChangesSelected);
    } else if (alignment === "BLOCK") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "BLOCK"
      );
      addUniqueFields(filteredData, dataListBlockNamesSelected);
    } else if (alignment === "TEMPORARY BLOCK/UNBLOCK") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "TEMPORARY BLOCK/UNBLOCK"
      );
      addUniqueFields(filteredData, dataListTemporaryBlockNamesSelected);
    } else {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "PROFIT CENTER CHANGE"
      );
      addUniqueFields(filteredData, dataListProfitCenterChangeSelected);
    }

    console.log(
      filterDataWithSelectedData,
      "filterDataWithSelectedDatadownload"
    );

    filterDataWithSelectedData?.map((selectedElement) => {
      selectedItemArr?.push(selectedElement.name);
    });
    const downloadPayloadHash = {
      coAreaCCs: downloadPayload,
      headers: selectedItemArr,
      rolePrefix: "ETP",
      dtName: "MDG_CC_FIELD_CONFIG",
      version: "v3",
      templateName: Array.from(selectedOptionsForDownload)
        .sort((a, b) => a.toLowerCase().localeCompare(b.toLowerCase()))
        .join(","),
      templateHeaders: "Long Description,Cost Center Category",
    };
    const downloadPayloadHashTemporaryBlock = {
      coAreaCCs: downloadPayload,
      isSunoco: false,
    };
    console.log("downloadPayload", downloadPayload);
    let hSuccess = (response) => {
      handleCloseDialog();
      if (response?.size !== 0 || response?.type !== "") {
        const href = URL.createObjectURL(response);
        const link = document.createElement("a");
        link.href = href;
        link.setAttribute("download", `Cost Center Mass Change.xlsx`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(href);
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Success");

        setMessageDialogMessage(
          `Cost Center Mass Change.xlsx has been downloaded successfully`
        );

        setMessageDialogSeverity("success");
        setBlurLoading(false);
        setLoaderMessage("");
      } else {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        // setMessageDialogMessage(`${error.message}`);
        setMessageDialogMessage("Please try again.");
        setMessageDialogSeverity("danger");
        setBlurLoading(false);
        setLoaderMessage("");
      }
    };
    let hError = (error) => {
      if (error.message) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        // setMessageDialogMessage(`${error.message}`);
        setMessageDialogMessage(
          "Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>"
        );
        setMessageDialogSeverity("danger");
        setBlurLoading(false);
        setLoaderMessage("");
      }
    };
    if (alignment === "TEMPORARY BLOCK/UNBLOCK") {
      doAjax(
        `/${destination_CostCenter_Mass}/excel/downloadExcelWithDataForTempBlock`,
        "postandgetblob",
        hSuccess,
        hError,
        downloadPayloadHashTemporaryBlock
      );
    } else {
      doAjax(
        `/${destination_CostCenter_Mass}/excel/downloadExcelWithData`,
        "postandgetblob",
        hSuccess,
        hError,
        downloadPayloadHash
      );
    }
  };

  const handleChangeDownloadEmail = () => {
    // setLoaderMessage(
    //   "Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience."
    // );
    setBlurLoading(true);
    console.log("selectedMassChangeRowData", selectedMassChangeRowData);
    var downloadPayload = selectedMassChangeRowData?.map((x) => {
      // console.log("xxxxx",x),
      return {
        costCenter: x.costCenter,
        controllingArea: x.controllingArea,
      };
    });
    // const selectedItemArr = ["Cost Center", "Controlling Area"];
    let selectedItemArr = [];
    if (alignment === "PROFIT CENTER CHANGE") {
      selectedItemArr = ["Cost Center", "Controlling Area", "Comp Code"];
    } else {
      selectedItemArr = ["Cost Center", "Controlling Area"];
    }
    let filterDataWithSelectedData = [];
    let selectedOptionsForDownload = new Set();

    const addUniqueFields = (filteredData, selectedList) => {
      const uniqueFields = new Set();
      selectedList.forEach((input) => {
        filteredData?.forEach((item, index) => {
          if (
            item.MDG_SELECT_OPTION === input.name &&
            !uniqueFields.has(item.MDG_FIELD_NAME)
          ) {
            uniqueFields.add(item.MDG_FIELD_NAME);
            filterDataWithSelectedData.push({
              id: index,
              name: item.MDG_FIELD_NAME,
            });

            selectedOptionsForDownload.add(item.MDG_SELECT_OPTION); // Add the selected option to the set
          }
        });
      });
    };

    if (alignment === "ALL OTHER CHANGES") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "ALL OTHER CHANGES"
      );
      addUniqueFields(filteredData, dataListAllOtherChangesSelected);
    } else if (alignment === "BLOCK") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "BLOCK"
      );
      addUniqueFields(filteredData, dataListBlockNamesSelected);
    } else if (alignment === "TEMPORARY BLOCK/UNBLOCK") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "TEMPORARY BLOCK/UNBLOCK"
      );
      addUniqueFields(filteredData, dataListTemporaryBlockNamesSelected);
    } else {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "PROFIT CENTER CHANGE"
      );
      addUniqueFields(filteredData, dataListProfitCenterChangeSelected);
    }

    console.log(
      filterDataWithSelectedData,
      "filterDataWithSelectedDatadownload"
    );

    filterDataWithSelectedData?.map((selectedElement) => {
      selectedItemArr?.push(selectedElement.name);
    });
    const downloadPayloadHash = {
      coAreaCCs: downloadPayload,
      headers: selectedItemArr,
      rolePrefix: "ETP",
      dtName: "MDG_CC_FIELD_CONFIG",
      version: "v3",
      templateName: Array.from(selectedOptionsForDownload)
        .sort((a, b) => a.toLowerCase().localeCompare(b.toLowerCase()))
        .join(","),
      templateHeaders: "Long Description,Cost Center Category",
    };
    const downloadPayloadHashTemporaryBlock = {
      coAreaCCs: downloadPayload,
      isSunoco: false,
    };
    console.log("downloadPayload", downloadPayload);
    let hSuccess = (response) => {
      handleCloseDialog();
      // const href = URL.createObjectURL(response);
      // const link = document.createElement("a");
      // link.href = href;
      // link.setAttribute("download", `Cost Center Mass Change.xlsx`);
      // document.body.appendChild(link);
      // link.click();
      // document.body.removeChild(link);
      // URL.revokeObjectURL(href);
      handleMessageDialogClickOpen();
      setMessageDialogTitle("Success");

      setMessageDialogMessage(
        `Download has been started. You will get the Excel file via email`
      );

      setMessageDialogSeverity("success");
      setBlurLoading(false);
      setLoaderMessage("");
    };
    let hError = (error) => {
      if (error.message) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        // setMessageDialogMessage(`${error.message}`);
        setMessageDialogMessage(
          "Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>"
        );
        setMessageDialogSeverity("danger");
        setBlurLoading(false);
        setLoaderMessage("");
      }
    };
    if (alignment === "TEMPORARY BLOCK/UNBLOCK") {
      doAjax(
        `/${destination_CostCenter_Mass}/excel/downloadExcelWithDataForTempBlockInMail`,
        "post",
        hSuccess,
        hError,
        downloadPayloadHashTemporaryBlock
      );
    } else {
      doAjax(
        `/${destination_CostCenter_Mass}/excel/downloadExcelWithDataInMail`,
        "post",
        hSuccess,
        hError,
        downloadPayloadHash
      );
    }
  };
  const handleChangeDownloadEmpty = () => {
    setLoaderMessage(
      "Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience."
    );
    setBlurLoading(true);
    console.log("selectedMassChangeRowData", selectedMassChangeRowData);
    var downloadPayload = selectedMassChangeRowData?.map((x) => {
      // console.log("xxxxx",x),
      return {
        costCenter: x.costCenter,
        controllingArea: x.controllingArea,
      };
    });
    // const selectedItemArr = ["Cost Center", "Controlling Area"];
    let selectedItemArr = [];
    if (alignment === "PROFIT CENTER CHANGE") {
      selectedItemArr = ["Cost Center", "Controlling Area", "Comp Code"];
    } else {
      selectedItemArr = ["Cost Center", "Controlling Area"];
    }
    let filterDataWithSelectedData = [];
    let selectedOptionsForDownload = new Set();

    const addUniqueFields = (filteredData, selectedList) => {
      const uniqueFields = new Set();
      selectedList.forEach((input) => {
        filteredData?.forEach((item, index) => {
          if (
            item.MDG_SELECT_OPTION === input.name &&
            !uniqueFields.has(item.MDG_FIELD_NAME)
          ) {
            uniqueFields.add(item.MDG_FIELD_NAME);
            filterDataWithSelectedData.push({
              id: index,
              name: item.MDG_FIELD_NAME,
            });

            selectedOptionsForDownload.add(item.MDG_SELECT_OPTION); // Add the selected option to the set
          }
        });
      });
    };
    if (alignment === "ALL OTHER CHANGES") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "ALL OTHER CHANGES"
      );
      addUniqueFields(filteredData, dataListAllOtherChangesSelected);
    } else if (alignment === "BLOCK") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "BLOCK"
      );
      addUniqueFields(filteredData, dataListBlockNamesSelected);
    } else if (alignment === "TEMPORARY BLOCK/UNBLOCK") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "TEMPORARY BLOCK/UNBLOCK"
      );
      addUniqueFields(filteredData, dataListTemporaryBlockNamesSelected);
    } else {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "PROFIT CENTER CHANGE"
      );
      addUniqueFields(filteredData, dataListProfitCenterChangeSelected);
    }

    console.log(
      filterDataWithSelectedData,
      "filterDataWithSelectedDatadownload"
    );

    filterDataWithSelectedData?.map((selectedElement) => {
      selectedItemArr?.push(selectedElement.name);
    });
    const downloadPayloadHash = {
      coAreaCCs: [],
      headers: selectedItemArr,
      dtName: "MDG_CC_FIELD_CONFIG",
      version: "v3",
      rolePrefix: "ETP",
      templateName: Array.from(selectedOptionsForDownload)
        .sort((a, b) => a.toLowerCase().localeCompare(b.toLowerCase()))
        .join(","),
      templateHeaders: "Long Description,Cost Center Category",
    };
    const downloadPayloadHashTemporaryBlock = {
      coAreaCCs: [],
      isSunoco: false,
    };
    console.log("downloadPayload", downloadPayload);
    let hSuccess = (response) => {
      handleCloseDialog();
      if (response?.size !== 0 || response?.type !== "") {
        const href = URL.createObjectURL(response);
        const link = document.createElement("a");
        link.href = href;
        link.setAttribute("download", `Cost Center Mass Change.xlsx`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(href);
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Success");

        setMessageDialogMessage(
          `Cost Center Mass Change.xlsx has been downloaded successfully`
        );

        setMessageDialogSeverity("success");
        setBlurLoading(false);
        setLoaderMessage("");
      } else {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        // setMessageDialogMessage(`${error.message}`);
        setMessageDialogMessage("Please try again.");
        setMessageDialogSeverity("danger");
        setBlurLoading(false);
        setLoaderMessage("");
      }
    };
    let hError = (error) => {
      if (error.message) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        // setMessageDialogMessage(`${error.message}`);
        setMessageDialogMessage(
          "Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>"
        );
        setMessageDialogSeverity("danger");
        setBlurLoading(false);
        setLoaderMessage("");
      }
    };
    if (alignment === "TEMPORARY BLOCK/UNBLOCK") {
      doAjax(
        `/${destination_CostCenter_Mass}/excel/downloadExcelWithDataForTempBlock`,
        "postandgetblob",
        hSuccess,
        hError,
        downloadPayloadHashTemporaryBlock
      );
    } else {
      doAjax(
        `/${destination_CostCenter_Mass}/excel/downloadExcelWithData`,
        "postandgetblob",
        hSuccess,
        hError,
        downloadPayloadHash
      );
    }
  };
  const handleChangeDownloadEmptyEmail = () => {
    // setLoaderMessage(
    //   "Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience."
    // );
    setBlurLoading(true);
    console.log("selectedMassChangeRowData", selectedMassChangeRowData);
    var downloadPayload = selectedMassChangeRowData?.map((x) => {
      // console.log("xxxxx",x),
      return {
        costCenter: x.costCenter,
        controllingArea: x.controllingArea,
      };
    });
    // const selectedItemArr = ["Cost Center", "Controlling Area"];
    let selectedItemArr = [];
    if (alignment === "PROFIT CENTER CHANGE") {
      selectedItemArr = ["Cost Center", "Controlling Area", "Comp Code"];
    } else {
      selectedItemArr = ["Cost Center", "Controlling Area"];
    }
    let filterDataWithSelectedData = [];
    let selectedOptionsForDownload = new Set();

    const addUniqueFields = (filteredData, selectedList) => {
      const uniqueFields = new Set();
      selectedList.forEach((input) => {
        filteredData?.forEach((item, index) => {
          if (
            item.MDG_SELECT_OPTION === input.name &&
            !uniqueFields.has(item.MDG_FIELD_NAME)
          ) {
            uniqueFields.add(item.MDG_FIELD_NAME);
            filterDataWithSelectedData.push({
              id: index,
              name: item.MDG_FIELD_NAME,
            });

            selectedOptionsForDownload.add(item.MDG_SELECT_OPTION); // Add the selected option to the set
          }
        });
      });
    };
    if (alignment === "ALL OTHER CHANGES") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "ALL OTHER CHANGES"
      );
      addUniqueFields(filteredData, dataListAllOtherChangesSelected);
    } else if (alignment === "BLOCK") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "BLOCK"
      );
      addUniqueFields(filteredData, dataListBlockNamesSelected);
    } else if (alignment === "TEMPORARY BLOCK/UNBLOCK") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "TEMPORARY BLOCK/UNBLOCK"
      );
      addUniqueFields(filteredData, dataListTemporaryBlockNamesSelected);
    } else {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "PROFIT CENTER CHANGE"
      );
      addUniqueFields(filteredData, dataListProfitCenterChangeSelected);
    }

    console.log(
      filterDataWithSelectedData,
      "filterDataWithSelectedDatadownload"
    );

    filterDataWithSelectedData?.map((selectedElement) => {
      selectedItemArr?.push(selectedElement.name);
    });
    const downloadPayloadHash = {
      coAreaCCs: [],
      headers: selectedItemArr,
      dtName: "MDG_CC_FIELD_CONFIG",
      version: "v3",
      rolePrefix: "ETP",
      templateName: Array.from(selectedOptionsForDownload)
        .sort((a, b) => a.toLowerCase().localeCompare(b.toLowerCase()))
        .join(","),
      templateHeaders: "Long Description,Cost Center Category",
    };
    const downloadPayloadHashTemporaryBlock = {
      coAreaCCs: [],
      isSunoco: false,
    };
    console.log("downloadPayload", downloadPayload);
    let hSuccess = (response) => {
      handleCloseDialog();
      // const href = URL.createObjectURL(response);
      // const link = document.createElement("a");
      // link.href = href;
      // link.setAttribute("download", `Cost Center Mass Change.xlsx`);
      // document.body.appendChild(link);
      // link.click();
      // document.body.removeChild(link);
      // URL.revokeObjectURL(href);
      handleMessageDialogClickOpen();
      setMessageDialogTitle("Success");

      setMessageDialogMessage(
        `Download has been started. You will get the Excel file via email`
      );

      setMessageDialogSeverity("success");
      setBlurLoading(false);
      setLoaderMessage("");
    };
    let hError = (error) => {
      if (error.message) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        // setMessageDialogMessage(`${error.message}`);
        setMessageDialogMessage(
          "Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>"
        );
        setMessageDialogSeverity("danger");
        setBlurLoading(false);
        setLoaderMessage("");
      }
    };
    if (alignment === "TEMPORARY BLOCK/UNBLOCK") {
      doAjax(
        `/${destination_CostCenter_Mass}/excel/downloadExcelWithDataForTempBlockInMail`,
        "post",
        hSuccess,
        hError,
        downloadPayloadHashTemporaryBlock
      );
    } else {
      doAjax(
        `/${destination_CostCenter_Mass}/excel/downloadExcelWithDataInMail`,
        "post",
        hSuccess,
        hError,
        downloadPayloadHash
      );
    }
  };

  const handleCreateSingleWithoutCopy = () => {
    handleDialogClickOpen();
  };
  const handleCreateSingleWithCopy = () => {
    handleDialogClickOpenWithCopy();
  };
  const handleCreateMultiple = () => {
    setEnableDocumentUpload(true);
    setHandleMassMode("Create");
    dispatch(setHandleMassMode("Create"));
  };

  const handleToggle = () => {
    setOpenButton((prevOpen) => !prevOpen);
  };
  const handleClick = (option, index) => {
    // dispatch(setHandleMassMode("Create"));
    if (index !== 0) {
      setSelectedIndex(index);
      setOpenButton(false);
      if (index === 1) {
        handleCreateMultiple();
      } else if (index === 2) {
        dispatch(setHandleMassMode("Create"));
        setOpenDownloadDialog(true);
        // handleCreateDownload();
      }
    }
  };
  const handleCloseButton = (event) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }
    setOpenButton(false);
  };
  const handleToggleChange = () => {
    setOpenButtonChange((prevOpen) => !prevOpen);
  };
  const handleToggleCreate = () => {
    setOpenButtonCreate((prevOpen) => !prevOpen);
  };
  const handleCloseButtonChange = (event) => {
    if (
      anchorRefChange.current &&
      anchorRefChange.current.contains(event.target)
    ) {
      return;
    }
    setOpenButtonChange(false);
  };
  const handleCloseButtonCreate = (event) => {
    if (
      anchorRefCreate.current &&
      anchorRefCreate.current.contains(event.target)
    ) {
      return;
    }
    setOpenButtonCreate(false);
  };
  const handleClickChange = (option, index) => {
    console.log("indexx", index);
    if (index !== 0) {
      setSelectedIndexChange(index);
      setOpenButtonChange(false);

      if (index === 1) {
        handleChangeMultiple();
      } else if (index === 2) {
        // Check if at least one row is selected

        setDownloadMultiple(true);
        setOpenDialogIDM(true);
        // if (selectedRows.length > 0) {
        //   console.log("selectedRows", selectedRows);
        //   setDownloadMultiple(true);
        //   //setOpenDialogIDM(true);
        //   setOpenDialog(true)
        //   // setIsLoading(true);
        //   // setIsCheckboxSelected(false);
        //   // handleChangeDownload();
        // } else {
        //   // Handle the case when no rows are selected (e.g., show a message)
        //   console.log("Please select at least one row to download Excel.");
        // }
      }
    }
  };
  const handleDescInputChange = (e) => {
    // Clear any existing timer
    const inputValue = e.target.value;
    setDescInputValue(inputValue);
    if (timerId) {
      clearTimeout(timerId);
    }

    if (inputValue.length >= 4) {
      // Set a new timer to execute handleDQM after 1 second
      const newTimerId = setTimeout(() => {
        getDescription(inputValue);
      }, 500);

      // Store the new timer ID
      setTimerId(newTimerId);
    }
  };

  const handleStreetInputChange = (e) => {
    // Clear any existing timer
    const inputValue = e.target.value;
    setStreetInputValue(inputValue);
    if (timerId) {
      clearTimeout(timerId);
    }

    if (inputValue.length >= 4) {
      // Set a new timer to execute handleDQM after 1 second
      const newTimerId = setTimeout(() => {
        getStreet(inputValue);
      }, 500);

      // Store the new timer ID
      setTimerId(newTimerId);
    }
  };

  const handlePCInputChange = (e) => {
    const inputValue = e.target.value;
    setPcInputValue(inputValue);
    // Clear any existing timer
    if (timerId) {
      clearTimeout(timerId);
    }

    if (inputValue.length >= 4) {
      // Set a new timer to execute handleDQM after 1 second
      const newTimerId = setTimeout(() => {
        getProfitCenterSearch(inputValue);
      }, 500);

      // Store the new timer ID
      setTimerId(newTimerId);
    }
  };

  const handleUserResInputChange = (e) => {
    const inputValue = e.target.value;
    setUserInputValue(inputValue);
    // Clear any existing timer
    if (timerId) {
      clearTimeout(timerId);
    }

    if (inputValue.length >= 4) {
      // Set a new timer to execute handleDQM after 1 second
      const newTimerId = setTimeout(() => {
        getUserResponsible(inputValue);
      }, 500);

      // Store the new timer ID
      setTimerId(newTimerId);
    }
  };

  const handlePersonResInputChange = (e) => {
    const inputValue = e.target.value;
    setPersonInputValue(inputValue);
    // Clear any existing timer
    if (timerId) {
      clearTimeout(timerId);
    }

    if (inputValue.length >= 4) {
      // Set a new timer to execute handleDQM after 1 second
      const newTimerId = setTimeout(() => {
        getPersonReponsible(inputValue);
      }, 500);

      // Store the new timer ID
      setTimerId(newTimerId);
    }
  };

  const handleLocationInputChange = (e) => {
    const inputValue = e.target.value;
    setLocationInputValue(inputValue);
    // Clear any existing timer
    if (timerId) {
      clearTimeout(timerId);
    }

    if (inputValue.length >= 4) {
      // Set a new timer to execute handleDQM after 1 second
      const newTimerId = setTimeout(() => {
        getLocation(inputValue);
      }, 500);

      // Store the new timer ID
      setTimerId(newTimerId);
    }
  };

  const handleCreatedByInputChange = (e) => {
    const inputValue = e.target.value;
    setCreatedByInputValue(inputValue);
    // Clear any existing timer
    if (timerId) {
      clearTimeout(timerId);
    }

    if (inputValue.length >= 4) {
      // Set a new timer to execute handleDQM after 1 second
      const newTimerId = setTimeout(() => {
        getCreatedBy(inputValue);
      }, 500);

      // Store the new timer ID
      setTimerId(newTimerId);
    }
  };

  const handleCCInputChange = (e) => {
    const inputValue = e.target.value;
    setCcInputValue(inputValue);
    // Clear any existing timer
    if (timerId) {
      clearTimeout(timerId);
    }

    if (inputValue.length >= 4) {
      // Set a new timer to execute handleDQM after 1 second
      const newTimerId = setTimeout(() => {
        getCostCenterSearch(inputValue);
      }, 500);

      // Store the new timer ID
      setTimerId(newTimerId);
    }
  };

  const handleCCNameInputChange = (e) => {
    // Clear any existing timer
    const inputValue = e.target.value;
    setCcNameInputValue(inputValue);
    if (timerId) {
      clearTimeout(timerId);
    }

    if (inputValue.length >= 4) {
      // Set a new timer to execute handleDQM after 1 second
      const newTimerId = setTimeout(() => {
        getCCName(inputValue);
      }, 500);

      // Store the new timer ID
      setTimerId(newTimerId);
    }
  };

  const handleClickCreate = (option, index) => {
    // dispatch(setHandleMassMode("Change"));
    if (index !== 0) {
      setSelectedIndexCreate(index);
      setOpenButtonCreate(false);
      if (index === 1) {
        handleCreateSingleWithCopy();
      } else if (index === 2) {
        handleCreateSingleWithoutCopy();
      }
    }
  };
  const handleChangeMultiple = () => {
    setEnableDocumentUpload(true);
    dispatch(setHandleMassMode("Change"));
  };
  console.log("costCenterName", newValidFromDate);

  const functions_ExportAsExcelErrorProcessNotCompleate = {
    convertJsonToExcel: () => {
      let excelColumns = [];
      duplicateFieldsColumns.forEach((item) => {
        if (item.headerName.toLowerCase() !== "action" && !item.hide) {
          excelColumns.push({ header: item.headerName, key: item.field });
        }
      });
      saveExcel({
        fileName: `Duplicate Requests -${moment(presentDate).format(
          "DD-MMM-YYYY"
        )}`,
        columns: excelColumns,
        rows: duplicateFieldsData,
      });
    },
    button: () => {
      return (
        <Button
          sx={{
            textTransform: "capitalize",
            position: "absolute",
            right: 0,
            top: 0,
          }}
          onClick={() =>
            functions_ExportAsExcelErrorProcessNotCompleate.convertJsonToExcel()
          }
        >
          Download
        </Button>
      );
    },
  };
  return (
    <>
      {/* {isLoading === true ? (
        <LoadingComponent />
      ) : ( */}
      <div ref={ref_elementForExport}>
        <ReusableDialog
          dialogState={openMessageDialog}
          openReusableDialog={handleMessageDialogClickOpen}
          closeReusableDialog={handleMessageDialogClose}
          dialogTitle={messageDialogTitle}
          dialogMessage={messageDialogMessage}
          handleDialogConfirm={handleMessageDialogClose}
          dialogOkText={"OK"}
          // handleExtraButton={handleMessageDialogNavigate}
          dialogSeverity={messageDialogSeverity}
        />

        <ReusableDialog
          dialogState={openSearchDialog}
          openReusableDialog={handleSearchDialogClickOpen}
          closeReusableDialog={handleSearchDialogClose}
          dialogTitle={searchDialogTitle}
          dialogMessage={searchDialogMessage}
          handleDialogConfirm={handleSearchDialogClose}
          dialogSeverity={"danger"}
          showCancelButton={false}
          dialogOkText={"OK"}
        />

        <Dialog
          open={showTableInDialog}
          onClose={() => setShowTableInDialog(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle
            sx={{ bgcolor: "#FFDAB9", color: "warning.contrastText" }}
          >
            <Typography
              variant="h6"
              gutterBottom
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <span>
                <WarningIcon sx={{ mr: 1 }} /> Duplicate Requests Alert
              </span>

              <Tooltip title="Export Table">
                <IconButton
                  sx={iconButton_SpacingSmall}
                  onClick={
                    functions_ExportAsExcelErrorProcessNotCompleate.convertJsonToExcel
                  }
                >
                  <ReusableIcon iconName={"IosShare"} />
                </IconButton>
              </Tooltip>
            </Typography>
          </DialogTitle>
          <DialogContent>
            <div style={{ marginTop: "20px" }}>
              <ReusableTable
                height={400}
                rows={duplicateFieldsData}
                columns={duplicateFieldsColumns}
                pageSize={duplicateFieldsData.length}
                getRowIdValue={"id"}
                hideFooter={true}
                checkboxSelection={false}
                disableSelectionOnClick={true}
                status_onRowSingleClick={true}
                stopPropagation_Column={"action"}
                status_onRowDoubleClick={true}
              />
            </div>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={() => {
                setShowTableInDialog(false);
                setOpenDialogIDM(false);
              }}
            >
              OK
            </Button>
          </DialogActions>
        </Dialog>

        <Dialog open={openDownloadDialog} onClose={handleDownloadDialogClose}>
          <DialogTitle
            sx={{
              justifyContent: "space-between",
              alignItems: "center",
              height: "max-content",
              padding: ".5rem",
              paddingLeft: "1rem",
              backgroundColor: "#EAE9FF40",
              // borderBottom: "1px solid grey",
              display: "flex",
            }}
          >
            <Typography
              variant="h6"
              gutterBottom
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              Select Download Option
            </Typography>
          </DialogTitle>
          <DialogContent>
            <FormControl>
              <RadioGroup
                row
                aria-labelledby="demo-row-radio-buttons-group-label"
                name="row-radio-buttons-group"
                value={downloadType}
                onChange={handleDownloadTypeChange}
              >
                <NoMaxWidthTooltip
                  arrow
                  placement="bottom"
                  title={
                    <span
                      style={{
                        whiteSpace: "nowrap", // Prevents line break
                        fontSize: "12px",
                        // maxWidth: "400px", // Optional width constraint
                        overflow: "hidden",
                        textOverflow: "ellipsis", // Adds ellipsis if overflow
                      }}
                    >
                      Here Excel will be downloaded
                    </span>
                  }
                  // placement="right"
                >
                  <FormControlLabel
                    value="systemGenerated"
                    control={<Radio />}
                    label="System-Generated"
                  />
                </NoMaxWidthTooltip>

                <NoMaxWidthTooltip
                  arrow
                  placement="bottom"
                  title={
                    <span
                      style={{
                        whiteSpace: "nowrap", // Prevents line break
                        fontSize: "12px",
                        // maxWidth: "400px", // Optional width constraint
                        overflow: "hidden",
                        textOverflow: "ellipsis", // Adds ellipsis if overflow
                      }}
                    >
                      Here Excel will be sent to your email
                    </span>
                  }
                  // placement="right"
                >
                  <FormControlLabel
                    value="mailGenerated"
                    control={<Radio />}
                    label="Mail-Generated"
                  />
                </NoMaxWidthTooltip>
              </RadioGroup>
            </FormControl>
          </DialogContent>
          <DialogActions>
            <Button variant="contained" onClick={onDownloadTypeChange}>
              OK
            </Button>
          </DialogActions>
        </Dialog>
        <Dialog
          open={openDownloadChangeDialog}
          onClose={handleDownloadChangeDialogClose}
        >
          <DialogTitle
            sx={{
              justifyContent: "space-between",
              alignItems: "center",
              height: "max-content",
              padding: ".5rem",
              paddingLeft: "1rem",
              backgroundColor: "#EAE9FF40",
              // borderBottom: "1px solid grey",
              display: "flex",
            }}
          >
            <Typography
              variant="h6"
              gutterBottom
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              Select Download Option
            </Typography>
          </DialogTitle>
          <DialogContent>
            <FormControl>
              <RadioGroup
                row
                aria-labelledby="demo-row-radio-buttons-group-label"
                name="row-radio-buttons-group"
                value={downloadType}
                onChange={handleMultipleDownloadTypeChange}
              >
                <NoMaxWidthTooltip
                  arrow
                  placement="bottom"
                  title={
                    <span
                      style={{
                        whiteSpace: "nowrap", // Prevents line break
                        fontSize: "12px",
                        // maxWidth: "400px", // Optional width constraint
                        overflow: "hidden",
                        textOverflow: "ellipsis", // Adds ellipsis if overflow
                      }}
                    >
                      Here Excel will be downloaded
                    </span>
                  }
                  // placement="right"
                >
                  <FormControlLabel
                    value="systemGenerated"
                    control={<Radio />}
                    label="System-Generated"
                  />
                </NoMaxWidthTooltip>

                <NoMaxWidthTooltip
                  arrow
                  placement="bottom"
                  title={
                    <span
                      style={{
                        whiteSpace: "nowrap", // Prevents line break
                        fontSize: "12px",
                        // maxWidth: "400px", // Optional width constraint
                        overflow: "hidden",
                        textOverflow: "ellipsis", // Adds ellipsis if overflow
                      }}
                    >
                      Here Excel will be sent to your email
                    </span>
                  }
                  // placement="right"
                >
                  <FormControlLabel
                    value="mailGenerated"
                    control={<Radio />}
                    label="Mail-Generated"
                  />
                </NoMaxWidthTooltip>
              </RadioGroup>
            </FormControl>
          </DialogContent>
          <DialogActions>
            <Button variant="contained" onClick={onMultipleDownloadTypeChange}>
              OK
            </Button>
          </DialogActions>
        </Dialog>
        <div>
          <Dialog
            open={openDialog}
            onClose={handleCloseDialog}
            sx={{
              "&::webkit-scrollbar": {
                width: "1px",
              },
            }}
          >
            <DialogTitle
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF40",
                // borderBottom: "1px solid grey",
                display: "flex",
              }}
            >
              <Grid>
                <Grid
                  container
                  sx={{ display: "flex", justifyContent: "space-between" }}
                >
                  <Grid item md={12}>
                    <ToggleButtonGroup
                      color="primary"
                      value={alignment}
                      exclusive
                      onChange={handleChange}
                      // aria-label="Platform"
                    >
                      <ToggleButton
                        value="ALL OTHER CHANGES"
                        //disabled={dataListAllOtherChanges?.length !== 0}
                      >
                        ALL OTHER CHANGES
                      </ToggleButton>
                      {!filteredRuleData && (
                        <ToggleButton value="BLOCK">BLOCK</ToggleButton>
                      )}
                      <ToggleButton
                        value="PROFIT CENTER CHANGE"
                        //disabled={dataListProfitCenterChange?.length !== 0}
                      >
                        PROFIT CENTER CHANGE
                      </ToggleButton>
                      <ToggleButton
                        value="TEMPORARY BLOCK/UNBLOCK"
                        //disabled={dataListProfitCenterChange?.length !== 0}
                      >
                        TEMPORARY BLOCK/UNBLOCK
                      </ToggleButton>
                    </ToggleButtonGroup>
                  </Grid>
                  {/* <Grid item md={1} sx={{ justifyContent: "flex-end" }}>
                    <IconButton
                      sx={{ width: "max-content" }}
                      onClick={handleCloseDialog}
                      children={<CloseIcon />}
                    />
                  </Grid> */}
                </Grid>
                <Grid>
                  <Typography variant="h6">
                    Select the field(s) to be changed
                  </Typography>
                </Grid>
              </Grid>
            </DialogTitle>
            <DialogContent
              sx={{
                padding: ".5rem 1rem",
                maxHeight: 400,
                // maxWidth: 400,
                overflowY: "auto",
              }}
            >
              <Grid container>
                {alignment === "ALL OTHER CHANGES" ? (
                  <Grid item xs={12}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          sx={{ height: "2vh" }}
                          onChange={handleSelectAll}
                          checked={selectAll}
                        />
                      }
                      label="SELECT ALL"
                    />
                  </Grid>
                ) : (
                  ""
                )}
                {alignment === "ALL OTHER CHANGES"
                  ? dataListAllOtherChanges?.map((item) => (
                      <Grid item xs={12} key={item?.id}>
                        <FormControlLabel
                          control={
                            <Checkbox
                              sx={{ height: "2vh" }}
                              onChange={() =>
                                handleSelectionAllOtherChanges(item)
                              }
                              checked={dataListAllOtherChangesSelected?.some(
                                (selectedItem) => selectedItem.id === item.id
                              )}
                            />
                          }
                          label={item.name}
                        />
                      </Grid>
                    ))
                  : alignment === "BLOCK"
                  ? dataListBlockNames?.map((item) => (
                      <Grid item xs={12} key={item?.id}>
                        <FormControlLabel
                          control={
                            <Checkbox
                              sx={{ height: "2vh" }}
                              onChange={() => handleSelectionBlock(item)}
                              checked={dataListBlockNamesSelected?.some(
                                (selectedItem) => selectedItem.id === item.id
                              )}
                              disabled
                            />
                          }
                          label={item.name}
                        />
                      </Grid>
                    ))
                  : alignment === "TEMPORARY BLOCK/UNBLOCK"
                  ? dataListTemporaryBlockNames?.map((item) => (
                      <Grid item xs={12} key={item?.id}>
                        <FormControlLabel
                          control={
                            <Checkbox
                              sx={{ height: "2vh" }}
                              onChange={() =>
                                handleSelectionTemporaryBlockChange(item)
                              }
                              checked={dataListTemporaryBlockNamesSelected?.some(
                                (selectedItem) => selectedItem.id === item.id
                              )}
                              disabled
                            />
                          }
                          label={item.name}
                        />
                      </Grid>
                    ))
                  : dataListProfitCenterChange?.map((item) => (
                      <Grid item xs={12} key={item.id}>
                        <FormControlLabel
                          control={
                            <Checkbox
                              sx={{ height: "2vh" }}
                              onChange={() =>
                                handleSelectionProfitCenterChange(item)
                              }
                              checked={dataListProfitCenterChangeSelected?.some(
                                (selectedItem) => selectedItem.id === item.id
                              )}
                              disabled
                            />
                          }
                          label={item.name}
                        />
                      </Grid>
                    ))}
              </Grid>
            </DialogContent>
            <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
              <Button
                sx={{ width: "max-content", textTransform: "capitalize" }}
                onClick={handleCloseDialog}
              >
                Cancel
              </Button>
              <Button
                className="button_primary--normal"
                type="save"
                onClick={handleApply}
                variant="contained"
              >
                Apply
              </Button>
            </DialogActions>
          </Dialog>
          <ReusableBackDrop
            blurLoading={blurLoading}
            loaderMessage={loaderMessage}
          />
        </div>
        <div style={{ ...outermostContainer, backgroundColor: "#FAFCFF" }}>
          <Stack spacing={1}>
            {/* Information */}
            <Grid container sx={outermostContainer_Information}>
              <Grid item md={5} sx={outerContainer_Information}>
                <Typography variant="h3">
                  <strong>Cost Center</strong>
                </Typography>
                <Typography variant="body2" color="#777">
                  This view displays the list of Cost Centers
                </Typography>
              </Grid>
              {/* <Grid item md={7} sx={{ display: "flex" }}>
                <Grid
                  container
                  direction="row"
                  justifyContent="flex-end"
                  alignItems="center"
                  spacing={0}
                >
                  <SearchBar
                    title="Search for Table data"
                    handleSearchAction={handleSearchAction}
                    module="CostCenter"
                    keyName="requestId"
                    message={"Search"}
                    // clearSearchBar={clearSearchBar}
                  />

                  <Tooltip title="Reload">
                    <IconButton sx={iconButton_SpacingSmall}>
                      <Refresh
                        sx={{
                          "&:hover": {
                            transform: "rotate(360deg)",
                            transition: "0.9s",
                          },
                        }}
                        onClick={refreshPage}
                      />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Export Table">
                    <IconButton
                      sx={iconButton_SpacingSmall}
                      onClick={functions_ExportAsExcel.convertJsonToExcel}
                    >
                      <ReusableIcon iconName={"IosShare"} />
                    </IconButton>
                  </Tooltip>
                </Grid>
              </Grid> */}
            </Grid>
            {/* <Grid container sx={container_filter}>
              <Grid item md={12}>
                <Accordion className="filter-accordian">
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls="panel1a-content"
                    id="panel1a-header"
                    sx={{
                      minHeight: "2rem !important",
                      margin: "0px !important",
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: "700",
                      }}
                    >
                      Search Cost Center
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails sx={{ padding: "0.5rem 1rem 0.5rem" }}>
                    <Grid
                      container
                      rowSpacing={1}
                      spacing={2}
                      justifyContent="space-between"
                      alignItems="center"
                      // sx={{ marginBottom: "0.5rem" }}
                    >
                      <Grid
                        container
                        spacing={1}
                        sx={{ padding: "0rem 1rem 0.5rem" }}
                      >
                        <Grid item md={2}>
                          <Typography sx={font_Small}>
                            Controlling Area
                          </Typography>
                          <FormControl size="small" fullWidth>
                            <Autocomplete
                              sx={{ height: "31px" }}
                              fullWidth
                              size="small"
                              value={rmSearchForm?.controllingArea}
                              onChange={handleControllingArea}
                              noOptionsText={
                                isDropDownLoading ? (
                                  <Box
                                    sx={{
                                      display: "flex",
                                      justifyContent: "center",
                                      mt: 1,
                                      zIndex: 9999,
                                      top: "10px",
                                    }}
                                  >
                                    <CircularProgress size={20} />
                                  </Box>
                                ) : (
                                  "No Data Available"
                                )
                              }
                              options={dropDownData?.ControllingArea ?? []}
                              getOptionLabel={(option) => {
                                if (option?.code)
                                  return `${option?.code}` ?? "";
                                else return "";
                              }}
                              renderOption={(props, option) => (
                                <li {...props}>
                                  <Typography style={{ fontSize: 12 }}>
                                    {option?.desc ? (
                                      <>
                                        <strong>{option.code}</strong> -{" "}
                                        {option.desc}
                                      </>
                                    ) : (
                                      <strong>{option.code}</strong>
                                    )}
                                  </Typography>
                                </li>
                              )}
                              renderInput={(params) => (
                                <TextField
                                  sx={{ fontSize: "12px !important" }}
                                  {...params}
                                  variant="outlined"
                                  placeholder="Select Controlling Area"
                                />
                              )}
                            />
                          </FormControl>
                        </Grid>
                        <Grid item md={2}>
                          <Typography sx={font_Small}>Cost Center</Typography>
                          <FormControl
                            fullWidth
                            size="small"
                            sx={{ paddingBottom: "0.7rem" }}
                          >
                            <Autocomplete
                              sx={{ height: "31px" }}
                              fullWidth
                              multiple
                              disableCloseOnSelect
                              size="small"
                              value={memoizedCCValue}
                              // loading={isDropDownLoading}
                              noOptionsText={
                                isDropDownLoading ? (
                                  <Box
                                    sx={{
                                      display: "flex",
                                      justifyContent: "center",
                                      mt: 1,
                                      zIndex: 9999,
                                      top: "10px",
                                    }}
                                  >
                                    <CircularProgress size={20} />
                                  </Box>
                                ) : (
                                  "No Data Available"
                                )
                              }
                              onChange={(e, value, reason) => {
                                if (reason === "clear" || value?.length === 0) {
                                  setselectedCostCenter([]);
                                  setselectedPresetCostCenter([]);
                                  return;
                                }

                                if (
                                  value.length > 0 &&
                                  value[value.length - 1]?.code === "Select All"
                                ) {
                                  handleSelectAllCostCenter();
                                } else {
                                  setselectedCostCenter(value);
                                }
                              }}
                              renderTags={(value, getTagProps) =>
                                value.length > 0 ? (
                                  <>
                                    <Chip
                                      label={value[0].code}
                                      {...getTagProps({ index: 0 })}
                                      sx={{
                                        height: 20,
                                        fontSize: "0.75rem",
                                        ".MuiChip-label": { padding: "0 6px" },
                                      }}
                                    />
                                    {value.length > 1 && (
                                      <Chip
                                        label={`+${value.length - 1}`}
                                        sx={{
                                          height: 20,
                                          fontSize: "0.75rem",
                                          ".MuiChip-label": {
                                            padding: "0 6px",
                                          },
                                        }}
                                      />
                                    )}
                                  </>
                                ) : null
                              }
                              limitTags={1}
                              options={
                                dropDownData?.ETCCSearchData?.length
                                  ? [
                                      {
                                        code: "Select All",
                                        desc: "Select All",
                                      },
                                      ...dropDownData?.ETCCSearchData,
                                    ]
                                  : dropDownData?.ETCCSearchData ?? []
                              }
                              getOptionLabel={(option) => {
                                if (option?.code)
                                  return `${option?.code}` ?? "";
                                else return "";
                              }}
                              renderOption={(props, option, { selected }) => (
                                <li {...props}>
                                  <FormGroup>
                                    <FormControlLabel
                                      control={
                                        <Checkbox
                                          checked={
                                            isCostCenterSelected(option) ||
                                            (option?.code === "Select All" &&
                                              selectedCostCenter?.length ===
                                                dropDownData?.ETCCSearchData
                                                  ?.length)
                                          }
                                        />
                                      }
                                      // label={`${option?.code} - ${option?.desc}`}
                                      label={
                                        <>
                                          <strong>{option.code}</strong> -{" "}
                                          {option.desc}
                                        </>
                                      }
                                    />
                                  </FormGroup>
                                </li>
                              )}
                              renderInput={(params) => (
                                <Tooltip
                                  title={
                                    ccInputValue.length < 4
                                      ? "Enter at least 4 characters"
                                      : ""
                                  }
                                  arrow
                                  disableHoverListener={
                                    ccInputValue.length >= 4
                                  }
                                  placement="top" // Ensures the tooltip appears above the text field
                                >
                                  <TextField
                                    sx={{
                                      fontSize: "12px !important",
                                      "& .MuiOutlinedInput-root": {
                                        height: 35,
                                      },
                                      "& .MuiInputBase-input": {
                                        padding: "10px 14px",
                                      },
                                    }}
                                    {...params}
                                    variant="outlined"
                                    // helperText={ccInputValue.length < 4 ? 'Enter at least 4 characters' : ''}
                                    placeholder={
                                      memoizedCCValue.length === 0
                                        ? "Select Cost Center"
                                        : ""
                                    }
                                    onChange={(e) => {
                                      handleCCInputChange(e);
                                    }}
                                  />
                                </Tooltip>
                              )}
                            />
                          </FormControl>
                        </Grid>
                        <Grid item md={2}>
                          <Typography sx={font_Small}>Company Code</Typography>
                          <FormControl
                            fullWidth
                            size="small"
                            sx={{ paddingBottom: "0.7rem" }}
                          >
                            <Autocomplete
                              sx={{ height: "31px" }}
                              fullWidth
                              size="small"
                              multiple
                              limitTags={1}
                              noOptionsText={
                                isDropDownLoading ? (
                                  <Box
                                    sx={{
                                      display: "flex",
                                      justifyContent: "center",
                                      mt: 1,
                                      zIndex: 9999,
                                      top: "10px",
                                    }}
                                  >
                                    <CircularProgress size={20} />
                                  </Box>
                                ) : (
                                  "No Data Available"
                                )
                              }
                              options={[
                                { code: "Select All", desc: "Select All" },
                                ...(dropDownData?.CompanyCode ?? []),
                              ]}
                              disableCloseOnSelect
                              onChange={(e, value, reason) => {
                                if (reason === "clear" || value?.length === 0) {
                                  setselectedComanyCode([]);
                                  setselectedPresetComanyCode([]);
                                  return;
                                }

                                console.log(value, "valueinauto");
                                if (
                                  value.length > 0 &&
                                  value[value.length - 1]?.code === "Select All"
                                ) {
                                  handleSelectAllCompanyCodes();
                                } else {
                                  setselectedComanyCode(value);
                                }
                              }}
                              getOptionLabel={(option) => {
                                if (option?.code)
                                  return `${option?.code}` ?? "";
                                else return "";
                              }}
                              value={
                                selectedComanyCode.length > 0
                                  ? selectedComanyCode
                                  : selectedPresetComanyCode.length > 0
                                  ? selectedPresetComanyCode
                                  : []
                              }
                              renderTags={(value, getTagProps) =>
                                value.length > 0 ? (
                                  <>
                                    <Chip
                                      label={value[0].code}
                                      {...getTagProps({ index: 0 })}
                                      sx={{
                                        height: 20,
                                        fontSize: "0.75rem",
                                        ".MuiChip-label": { padding: "0 6px" },
                                      }}
                                    />
                                    {value.length > 1 && (
                                      <Chip
                                        label={`+${value.length - 1}`}
                                        sx={{
                                          height: 20,
                                          fontSize: "0.75rem",
                                          ".MuiChip-label": {
                                            padding: "0 6px",
                                          },
                                        }}
                                      />
                                    )}
                                  </>
                                ) : null
                              }
                              renderOption={(props, option, { selected }) => (
                                <li {...props}>
                                  <FormGroup>
                                    <FormControlLabel
                                      control={
                                        <Checkbox
                                          checked={
                                            isCompanyCodeSelected(option) ||
                                            (option?.code === "Select All" &&
                                              selectedComanyCode?.length ===
                                                dropDownData?.CompanyCode
                                                  ?.length)
                                          }
                                        />
                                      }
                                      // label={`${option?.code} - ${option?.desc}`}
                                      label={
                                        <>
                                          <strong>{option.code}</strong> -{" "}
                                          {option.desc}
                                        </>
                                      }
                                    />
                                  </FormGroup>
                                </li>
                              )}
                              renderInput={(params) => (
                                <TextField
                                  sx={{
                                    fontSize: "12px !important",
                                    "& .MuiOutlinedInput-root": {
                                      height: 35,
                                    },
                                    "& .MuiInputBase-input": {
                                      padding: "10px 14px",
                                    },
                                  }}
                                  {...params}
                                  variant="outlined"
                                  placeholder="Select Company Code"
                                />
                              )}
                            />
                          </FormControl>
                        </Grid>
                        <Grid item md={2}>
                          <Typography sx={font_Small}>
                            Long Description
                          </Typography>
                          <FormControl fullWidth size="small">
                            <Autocomplete
                              sx={{ height: "31px" }}
                              fullWidth
                              size="small"
                              multiple
                              disableCloseOnSelect
                              value={memoizedLDValue}
                              noOptionsText={
                                isDropDownLoading ? (
                                  <Box
                                    sx={{
                                      display: "flex",
                                      justifyContent: "center",
                                      mt: 1,
                                      zIndex: 9999,
                                      top: "10px",
                                    }}
                                  >
                                    <CircularProgress size={20} />
                                  </Box>
                                ) : (
                                  "No Data Available"
                                )
                              }
                              renderTags={(value, getTagProps) =>
                                value.length > 0 ? (
                                  <>
                                    <Chip
                                      label={value[0].code}
                                      {...getTagProps({ index: 0 })}
                                      sx={{
                                        height: 20,
                                        fontSize: "0.75rem",
                                        ".MuiChip-label": { padding: "0 6px" },
                                      }}
                                    />
                                    {value.length > 1 && (
                                      <Chip
                                        label={`+${value.length - 1}`}
                                        sx={{
                                          height: 20,
                                          fontSize: "0.75rem",
                                          ".MuiChip-label": {
                                            padding: "0 6px",
                                          },
                                        }}
                                      />
                                    )}
                                  </>
                                ) : null
                              }
                              onChange={(e, value, reason) => {
                                if (reason === "clear" || value?.length === 0) {
                                  setselectedDescription([]);
                                  setselectedPresetDescription([]);
                                  return;
                                }

                                if (
                                  value.length > 0 &&
                                  value[value.length - 1]?.code === "Select All"
                                ) {
                                  handleSelectAllDescription();
                                } else {
                                  setselectedDescription(value);
                                }
                              }}
                              limitTags={1}
                              options={
                                dropDownData?.ETDescriptionSearchData?.length
                                  ? [
                                      { code: "Select All" },
                                      ...dropDownData?.ETDescriptionSearchData,
                                    ]
                                  : dropDownData?.ETDescriptionSearchData ?? []
                              }
                              getOptionLabel={(option) => {
                                if (option?.code) return option?.code ?? "";
                                else return "";
                              }}
                              renderOption={(props, option, { selected }) => (
                                <li {...props}>
                                  <FormGroup>
                                    <FormControlLabel
                                      control={
                                        <Checkbox
                                          checked={
                                            isDescriptionSelected(option) ||
                                            (option?.code === "Select All" &&
                                              selectedDescription?.length ===
                                                dropDownData
                                                  ?.ETDescriptionSearchData
                                                  ?.length)
                                          }
                                        />
                                      }
                                      // label={`${option?.code}`}
                                      label={
                                        <>
                                          <strong>{option.code}</strong>
                                        </>
                                      }
                                    />
                                  </FormGroup>
                                </li>
                              )}
                              renderInput={(params) => (
                                <Tooltip
                                  title={
                                    descInputValue.length < 4
                                      ? "Enter at least 4 characters"
                                      : ""
                                  }
                                  arrow
                                  disableHoverListener={
                                    descInputValue.length >= 4
                                  }
                                  placement="top" // Ensures the tooltip appears above the text field
                                >
                                  <TextField
                                    sx={{
                                      fontSize: "12px !important",
                                      "& .MuiOutlinedInput-root": {
                                        height: 35,
                                      },
                                      "& .MuiInputBase-input": {
                                        padding: "10px 14px",
                                      },
                                    }}
                                    {...params}
                                    variant="outlined"
                                    placeholder={
                                      memoizedLDValue.length === 0
                                        ? "Select Long Description"
                                        : ""
                                    }
                                    onChange={(e) => {
                                      handleDescInputChange(e);
                                    }}
                                  />
                                </Tooltip>
                              )}
                            />
                          </FormControl>
                        </Grid>
                        <Grid item md={2}>
                          <Typography sx={font_Small}>
                            Cost Center Category
                          </Typography>
                          <FormControl fullWidth size="small">
                            <Autocomplete
                              sx={{ height: "31px" }}
                              fullWidth
                              size="small"
                              multiple
                              disableCloseOnSelect
                              value={
                                selectedCCcategory.length > 0
                                  ? selectedCCcategory
                                  : selectedPresetCCcategory.length > 0
                                  ? selectedPresetCCcategory
                                  : []
                              }
                              noOptionsText={
                                isDropDownLoading ? (
                                  <Box
                                    sx={{
                                      display: "flex",
                                      justifyContent: "center",
                                      mt: 1,
                                      zIndex: 9999,
                                      top: "10px",
                                    }}
                                  >
                                    <CircularProgress size={20} />
                                  </Box>
                                ) : (
                                  "No Data Available"
                                )
                              }
                              renderTags={(value, getTagProps) =>
                                value.length > 0 ? (
                                  <>
                                    <Chip
                                      label={value[0].code}
                                      {...getTagProps({ index: 0 })}
                                      sx={{
                                        height: 20,
                                        fontSize: "0.75rem",
                                        ".MuiChip-label": { padding: "0 6px" },
                                      }}
                                    />
                                    {value.length > 1 && (
                                      <Chip
                                        label={`+${value.length - 1}`}
                                        sx={{
                                          height: 20,
                                          fontSize: "0.75rem",
                                          ".MuiChip-label": {
                                            padding: "0 6px",
                                          },
                                        }}
                                      />
                                    )}
                                  </>
                                ) : null
                              }
                              onChange={(e, value, reason) => {
                                if (reason === "clear" || value?.length === 0) {
                                  setselectedCCcategory([]);
                                  setselectedPresetCCcategory([]);
                                  return;
                                }

                                if (
                                  value.length > 0 &&
                                  value[value.length - 1]?.code === "Select All"
                                ) {
                                  handleSelectAllCCcategory();
                                } else {
                                  setselectedCCcategory(value);
                                }
                              }}
                              limitTags={1}
                              options={
                                dropDownData?.CostCenterCategorySearch?.length
                                  ? [
                                      {
                                        code: "Select All",
                                        desc: "Select All",
                                      },
                                      ...dropDownData?.CostCenterCategorySearch,
                                    ]
                                  : dropDownData?.CostCenterCategorySearch ?? []
                              }
                              getOptionLabel={(option) => {
                                if (option?.code)
                                  return `${option?.code}` ?? "";
                                else return "";
                              }}
                              renderOption={(props, option, { selected }) => (
                                <li {...props}>
                                  <FormGroup>
                                    <FormControlLabel
                                      control={
                                        <Checkbox
                                          checked={
                                            isCCcategorySelected(option) ||
                                            (option?.code === "Select All" &&
                                              selectedCCcategory?.length ===
                                                dropDownData
                                                  ?.CostCenterCategorySearch
                                                  ?.length)
                                          }
                                        />
                                      }
                                      // label={`${option?.code} - ${option?.desc}`}
                                      label={
                                        <>
                                          <strong>{option.code}</strong> -{" "}
                                          {option.desc}
                                        </>
                                      }
                                    />
                                  </FormGroup>
                                </li>
                              )}
                              renderInput={(params) => (
                                <TextField
                                  sx={{
                                    fontSize: "12px !important",
                                    "& .MuiOutlinedInput-root": {
                                      height: 35,
                                    },
                                    "& .MuiInputBase-input": {
                                      padding: "10px 14px",
                                    },
                                  }}
                                  {...params}
                                  variant="outlined"
                                  placeholder="Select Cost Center Category"
                                />
                              )}
                            />
                          </FormControl>
                        </Grid>
                        <Grid item md={2}>
                          <Typography sx={font_Small}>
                            Person Responsible
                          </Typography>
                          <FormControl fullWidth size="small">
                            <Autocomplete
                              sx={{ height: "31px" }}
                              fullWidth
                              size="small"
                              multiple
                              disableCloseOnSelect
                              value={memoizedPRValue}
                              noOptionsText={
                                isDropDownLoading ? (
                                  <Box
                                    sx={{
                                      display: "flex",
                                      justifyContent: "center",
                                      mt: 1,
                                      zIndex: 9999,
                                      top: "10px",
                                    }}
                                  >
                                    <CircularProgress size={20} />
                                  </Box>
                                ) : (
                                  "No Data Available"
                                )
                              }
                              onChange={(e, value, reason) => {
                                if (reason === "clear" || value?.length === 0) {
                                  setselectedPersonResponsible([]);
                                  setselectedPresetPersonResponsible([]);
                                  return;
                                }

                                if (
                                  value.length > 0 &&
                                  value[value.length - 1]?.code === "Select All"
                                ) {
                                  handleSelectAllPersonResponsible();
                                } else {
                                  setselectedPersonResponsible(value);
                                }
                              }}
                              renderTags={(value, getTagProps) =>
                                value.length > 0 ? (
                                  <>
                                    <Chip
                                      label={value[0].code}
                                      {...getTagProps({ index: 0 })}
                                      sx={{
                                        height: 20,
                                        fontSize: "0.75rem",
                                        ".MuiChip-label": { padding: "0 6px" },
                                      }}
                                    />
                                    {value.length > 1 && (
                                      <Chip
                                        label={`+${value.length - 1}`}
                                        sx={{
                                          height: 20,
                                          fontSize: "0.75rem",
                                          ".MuiChip-label": {
                                            padding: "0 6px",
                                          },
                                        }}
                                      />
                                    )}
                                  </>
                                ) : null
                              }
                              limitTags={1}
                              options={
                                dropDownData?.ETPersonResponsibleSearch?.length
                                  ? [
                                      { code: "Select All" },
                                      ...dropDownData?.ETPersonResponsibleSearch,
                                    ]
                                  : dropDownData?.ETPersonResponsibleSearch ??
                                    []
                              }
                              getOptionLabel={(option) => {
                                if (option?.code) return option?.code ?? "";
                                else return "";
                              }}
                              renderOption={(props, option, { selected }) => (
                                <li {...props}>
                                  <FormGroup>
                                    <FormControlLabel
                                      control={
                                        <Checkbox
                                          checked={
                                            isPersonResponsibleSelected(
                                              option
                                            ) ||
                                            (option?.code === "Select All" &&
                                              selectedPersonResponsible?.length ===
                                                dropDownData
                                                  ?.ETPersonResponsibleSearch
                                                  ?.length)
                                          }
                                        />
                                      }
                                      // label={`${option?.code}`}
                                      label={
                                        <>
                                          <strong>{option.code}</strong>
                                        </>
                                      }
                                    />
                                  </FormGroup>
                                </li>
                              )}
                              renderInput={(params) => (
                                <Tooltip
                                  title={
                                    personInputValue.length < 4
                                      ? "Enter at least 4 characters"
                                      : ""
                                  }
                                  arrow
                                  disableHoverListener={
                                    personInputValue.length >= 4
                                  }
                                  placement="top" // Ensures the tooltip appears above the text field
                                >
                                  <TextField
                                    sx={{
                                      fontSize: "12px !important",
                                      "& .MuiOutlinedInput-root": {
                                        height: 35,
                                      },
                                      "& .MuiInputBase-input": {
                                        padding: "10px 14px",
                                      },
                                    }}
                                    {...params}
                                    variant="outlined"
                                    // helperText={personInputValue.length < 4 ? 'Enter at least 4 characters' : ''}
                                    placeholder={
                                      memoizedPRValue.length === 0
                                        ? "Select Person Responsible"
                                        : ""
                                    }
                                    onChange={(e) => {
                                      handlePersonResInputChange(e);
                                    }}
                                  />
                                </Tooltip>
                              )}
                            />
                          </FormControl>
                        </Grid>
                        <Grid item md={2}>
                          <Typography sx={font_Small}>
                            User Responsible
                          </Typography>
                          <FormControl
                            size="small"
                            fullWidth
                            sx={{ paddingBottom: "0.7rem" }}
                          >
                            <Autocomplete
                              sx={{ height: "31px" }}
                              fullWidth
                              size="small"
                              multiple
                              disableCloseOnSelect
                              value={memoizedURValue}
                              noOptionsText={
                                isDropDownLoading ? (
                                  <Box
                                    sx={{
                                      display: "flex",
                                      justifyContent: "center",
                                      mt: 1,
                                      zIndex: 9999,
                                      top: "10px",
                                    }}
                                  >
                                    <CircularProgress size={20} />
                                  </Box>
                                ) : (
                                  "No Data Available"
                                )
                              }
                              onChange={(e, value, reason) => {
                                if (reason === "clear" || value?.length === 0) {
                                  setselectedUserResponsible([]);
                                  setselectedPresetUserResponsible([]);
                                  return;
                                }

                                if (
                                  value.length > 0 &&
                                  value[value.length - 1]?.code === "Select All"
                                ) {
                                  handleSelectAllUserResponsible();
                                } else {
                                  setselectedUserResponsible(value);
                                }
                              }}
                              limitTags={1}
                              options={
                                dropDownData?.ETUserResponsible?.length
                                  ? [
                                      { code: "Select All" },
                                      ...dropDownData?.ETUserResponsible,
                                    ]
                                  : dropDownData?.ETUserResponsible ?? []
                              }
                              getOptionLabel={(option) => {
                                if (option?.code) return option?.code ?? "";
                                else return "";
                              }}
                              renderOption={(props, option, { selected }) => (
                                <li {...props}>
                                  <FormGroup>
                                    <FormControlLabel
                                      control={
                                        <Checkbox
                                          checked={
                                            isUserResponsibleSelected(option) ||
                                            (option?.code === "Select All" &&
                                              selectedUserResponsible?.length ===
                                                dropDownData?.ETUserResponsible
                                                  ?.length)
                                          }
                                        />
                                      }
                                      // label={`${option?.code}`}
                                      label={
                                        <>
                                          <strong>{option.code}</strong>
                                        </>
                                      }
                                    />
                                  </FormGroup>
                                </li>
                              )}
                              renderTags={(value, getTagProps) =>
                                value.length > 0 ? (
                                  <>
                                    <Chip
                                      label={value[0].code}
                                      {...getTagProps({ index: 0 })}
                                      sx={{
                                        height: 20,
                                        fontSize: "0.75rem",
                                        ".MuiChip-label": { padding: "0 6px" },
                                      }}
                                    />
                                    {value.length > 1 && (
                                      <Chip
                                        label={`+${value.length - 1}`}
                                        sx={{
                                          height: 20,
                                          fontSize: "0.75rem",
                                          ".MuiChip-label": {
                                            padding: "0 6px",
                                          },
                                        }}
                                      />
                                    )}
                                  </>
                                ) : null
                              }
                              renderInput={(params) => (
                                <Tooltip
                                  title={
                                    userInputValue.length < 4
                                      ? "Enter at least 4 characters"
                                      : ""
                                  }
                                  arrow
                                  disableHoverListener={
                                    userInputValue.length >= 4
                                  }
                                  placement="top" // Ensures the tooltip appears above the text field
                                >
                                  <TextField
                                    sx={{
                                      fontSize: "12px !important",
                                      "& .MuiOutlinedInput-root": {
                                        height: 35,
                                      },
                                      "& .MuiInputBase-input": {
                                        padding: "10px 14px",
                                      },
                                    }}
                                    {...params}
                                    variant="outlined"
                                    // helperText={userInputValue.length < 4 ? 'Enter at least 4 characters' : ''}
                                    placeholder={
                                      memoizedURValue.length === 0
                                        ? "Select User Responsible"
                                        : ""
                                    }
                                    onChange={(e) => {
                                      handleUserResInputChange(e);
                                    }}
                                  />
                                </Tooltip>
                              )}
                            />
                          </FormControl>
                        </Grid>

                        
                        <Grid item md={2}>
                          <Typography sx={font_Small}>
                            Blocking Status
                          </Typography>
                          <FormControl fullWidth size="small">
                            <Select
                              placeholder={"Select Blocking Status"}
                              sx={{ height: "31px" }}
                              size="small"
                              value={rmSearchForm?.blockingStatus}
                              name="blockingStatus"
                              onChange={(e) => handleBlockingStatus(e)}
                              displayEmpty={true}
                              // input={<OutlinedInput label="Tag" />}
                              // renderValue={(selected) => selected}
                              MenuProps={MenuProps}
                            >
                              {names?.map((name) => (
                                <MenuItem
                                  sx={font_Small}
                                  key={name}
                                  value={name}
                                  style={{
                                    fontSize: "12px !important",
                                    height: "35px",
                                  }}
                                >
                                  <ListItemText
                                    sx={font_Small}
                                    primary={name}
                                    style={{ fontSize: "12px !important" }}
                                  />
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                        </Grid>
                        
                        <Grid item md={2}>
                          <Typography sx={font_Small}>
                            Add New Filters
                          </Typography>
                          <FormControl fullWidth>
                            <Select
                              sx={{
                                font_Small,
                                height: "31px",
                                fontSize: "12px",
                              }}
                              // fullWidth
                              size="small"
                              multiple
                              limitTags={2}
                              value={selectedOptions}
                              onChange={handleSelection}
                              renderValue={(selected) => selected.join(", ")}
                              MenuProps={{
                                MenuProps,
                              }}
                              endAdornment={
                                selectedOptions.length > 0 && (
                                  <InputAdornment
                                    position="end"
                                    sx={{ marginRight: "15px" }}
                                  >
                                    <IconButton
                                      size="small"
                                      sx={{ height: "10px", width: "10px" }}
                                      onClick={() => {
                                        // handleClearPayload(selectedOptions)
                                        setSelectedOptions([]);
                                      }}
                                      aria-label="Clear selections"
                                    >
                                      <ClearIcon />
                                    </IconButton>
                                  </InputAdornment>
                                )
                              }
                            >
                              {items?.map((option) => (
                                <MenuItem
                                  key={option.title}
                                  value={option.title}
                                >
                                  <Checkbox
                                    checked={
                                      selectedOptions.indexOf(option.title) > -1
                                    }
                                  />
                                  {option.title}
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                          <Grid
                            style={{
                              display: "flex",
                              justifyContent: "space-around",
                            }}
                          ></Grid>
                        </Grid>
                      </Grid>
                      <Grid
                        container
                        rowSpacing={1}
                        spacing={2}
                        justifyContent="space-between"
                        alignItems="center"
                        sx={{ padding: "0.5rem 1rem 0.5rem" }}
                        // sx={{ marginBottom: "0.5rem" }}
                      >
                        <Grid
                          container
                          spacing={1}
                          sx={{ padding: "0rem 1rem 0.5rem" }}
                        >
                          {selectedOptions?.map((option, i) => {
                            console.log(
                              "fercvalue",
                              option,
                              rmSearchForm[option],
                              rmSearchForm
                            );
                            if (option === "Short Description") {
                              return (
                                <>
                                  <Grid item md={2}>
                                    <Typography sx={font_Small}>
                                      Short Description
                                    </Typography>
                                    <FormControl
                                      fullWidth
                                      size="small"
                                      sx={{ paddingBottom: "0.7rem" }}
                                    >
                                      <Autocomplete
                                        sx={{ height: "31px" }}
                                        fullWidth
                                        size="small"
                                        multiple
                                        disableCloseOnSelect
                                        value={memoizedSDValue}
                                        noOptionsText={
                                          isDropDownLoading ? (
                                            <Box
                                              sx={{
                                                display: "flex",
                                                justifyContent: "center",
                                                mt: 1,
                                                zIndex: 9999,
                                                top: "10px",
                                              }}
                                            >
                                              <CircularProgress size={20} />
                                            </Box>
                                          ) : (
                                            "No Data Available"
                                          )
                                        }
                                        renderTags={(value, getTagProps) =>
                                          value.length > 0 ? (
                                            <>
                                              <Chip
                                                label={value[0].code}
                                                {...getTagProps({ index: 0 })}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": {
                                                    padding: "0 6px",
                                                  },
                                                }}
                                              />
                                              {value.length > 1 && (
                                                <Chip
                                                  label={`+${value.length - 1}`}
                                                  sx={{
                                                    height: 20,
                                                    fontSize: "0.75rem",
                                                    ".MuiChip-label": {
                                                      padding: "0 6px",
                                                    },
                                                  }}
                                                />
                                              )}
                                            </>
                                          ) : null
                                        }
                                        onChange={(e, value, reason) => {
                                          if (
                                            reason === "clear" ||
                                            value?.length === 0
                                          ) {
                                            setselectedCostCenterName([]);
                                            setselectedPresetCostCenterName([]);
                                            return;
                                          }

                                          if (
                                            value.length > 0 &&
                                            value[value.length - 1]?.code ===
                                              "Select All"
                                          ) {
                                            handleSelectAllCostCenterName();
                                          } else {
                                            setselectedCostCenterName(value);
                                          }
                                        }}
                                        limitTags={1}
                                        options={
                                          dropDownData?.ETCCNameSearchData
                                            ?.length
                                            ? [
                                                { code: "Select All" },
                                                ...dropDownData?.ETCCNameSearchData,
                                              ]
                                            : dropDownData?.ETCCNameSearchData ??
                                              []
                                        }
                                        getOptionLabel={(option) => {
                                          if (option?.code)
                                            return option?.code ?? "";
                                          else return "";
                                        }}
                                        renderOption={(
                                          props,
                                          option,
                                          { selected }
                                        ) => (
                                          <li {...props}>
                                            <FormGroup>
                                              <FormControlLabel
                                                control={
                                                  <Checkbox
                                                    checked={
                                                      isCostCenterNameSelected(
                                                        option
                                                      ) ||
                                                      (option?.code ===
                                                        "Select All" &&
                                                        selectedCostCenterName?.length ===
                                                          dropDownData
                                                            ?.ETCCNameSearchData
                                                            ?.length)
                                                    }
                                                  />
                                                }
                                                // label={`${option?.code}`}
                                                label={
                                                  <>
                                                    <strong>
                                                      {option.code}
                                                    </strong>
                                                  </>
                                                }
                                              />
                                            </FormGroup>
                                          </li>
                                        )}
                                        renderInput={(params) => (
                                          <Tooltip
                                            title={
                                              ccNameInputValue.length < 4
                                                ? "Enter at least 4 characters"
                                                : ""
                                            }
                                            arrow
                                            disableHoverListener={
                                              ccNameInputValue.length >= 4
                                            }
                                            placement="top" // Ensures the tooltip appears above the text field
                                          >
                                            <TextField
                                              sx={{
                                                fontSize: "12px !important",
                                                "& .MuiOutlinedInput-root": {
                                                  height: 35,
                                                },
                                                "& .MuiInputBase-input": {
                                                  padding: "10px 14px",
                                                },
                                              }}
                                              {...params}
                                              variant="outlined"
                                              placeholder={
                                                memoizedSDValue.length === 0
                                                  ? "Select Short Description"
                                                  : ""
                                              }
                                              onChange={(e) => {
                                                handleCCNameInputChange(e);
                                              }}
                                            />
                                          </Tooltip>
                                        )}
                                      />
                                    </FormControl>
                                  </Grid>
                                </>
                              );
                            } else if (option === "Created On") {
                              return (
                                <Grid item md={2}>
                                  <Typography sx={font_Small}>
                                    {option}
                                  </Typography>
                                  <FormControl size="small" fullWidth>
                                    <LocalizationProvider
                                      dateAdapter={AdapterDateFns}
                                    >
                                      <DateRange
                                        handleDate={handleDate}
                                        date={selectedDateRange}
                                      />
                                    </LocalizationProvider>
                                  </FormControl>
                                </Grid>
                              );
                            } else if (option === "Street") {
                              return (
                                <Grid item md={2}>
                                  <Typography sx={font_Small}>
                                    Street
                                  </Typography>
                                  <FormControl fullWidth size="small">
                                    <Autocomplete
                                      sx={{ height: "31px" }}
                                      fullWidth
                                      size="small"
                                      multiple
                                      disableCloseOnSelect
                                      value={memoizedStreetValue}
                                      noOptionsText={
                                        isDropDownLoading ? (
                                          <Box
                                            sx={{
                                              display: "flex",
                                              justifyContent: "center",
                                              mt: 1,
                                              zIndex: 9999,
                                              top: "10px",
                                            }}
                                          >
                                            <CircularProgress size={20} />
                                          </Box>
                                        ) : (
                                          "No Data Available"
                                        )
                                      }
                                      limitTags={1}
                                      renderTags={(value, getTagProps) =>
                                        value.length > 0 ? (
                                          <>
                                            <Chip
                                              label={value[0].code}
                                              {...getTagProps({ index: 0 })}
                                              sx={{
                                                height: 20,
                                                fontSize: "0.75rem",
                                                ".MuiChip-label": {
                                                  padding: "0 6px",
                                                },
                                              }}
                                            />
                                            {value.length > 1 && (
                                              <Chip
                                                label={`+${value.length - 1}`}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": {
                                                    padding: "0 6px",
                                                  },
                                                }}
                                              />
                                            )}
                                          </>
                                        ) : null
                                      }
                                      onChange={(e, value, reason) => {
                                        if (
                                          reason === "clear" ||
                                          value?.length === 0
                                        ) {
                                          setselectedStreet([]);
                                          setselectedPresetStreet([]);
                                          return;
                                        }

                                        if (
                                          value.length > 0 &&
                                          value[value.length - 1]?.code ===
                                            "Select All"
                                        ) {
                                          handleSelectAllStreet();
                                        } else {
                                          setselectedStreet(value);
                                        }
                                      }}
                                      options={
                                        dropDownData?.ETStreetSearchData?.length
                                          ? [
                                              { code: "Select All" },
                                              ...dropDownData?.ETStreetSearchData,
                                            ]
                                          : dropDownData?.ETStreetSearchData ??
                                            []
                                      }
                                      getOptionLabel={(option) => {
                                        if (option?.code)
                                          return option?.code ?? "";
                                        else return "";
                                      }}
                                      renderOption={(
                                        props,
                                        option,
                                        { selected }
                                      ) => (
                                        <li {...props}>
                                          <FormGroup>
                                            <FormControlLabel
                                              control={
                                                <Checkbox
                                                  checked={
                                                    isStreetSelected(option) ||
                                                    (option?.code ===
                                                      "Select All" &&
                                                      selectedStreet?.length ===
                                                        dropDownData
                                                          ?.ETStreetSearchData
                                                          ?.length)
                                                  }
                                                />
                                              }
                                              // label={`${option?.code}`}
                                              label={
                                                <>
                                                  <strong>{option.code}</strong>
                                                </>
                                              }
                                            />
                                          </FormGroup>
                                        </li>
                                      )}
                                      renderInput={(params) => (
                                        <Tooltip
                                          title={
                                            streetInputValue.length < 4
                                              ? "Enter at least 4 characters"
                                              : ""
                                          }
                                          arrow
                                          disableHoverListener={
                                            streetInputValue.length >= 4
                                          }
                                          placement="top" // Ensures the tooltip appears above the text field
                                        >
                                          <TextField
                                            sx={{
                                              fontSize: "12px !important",
                                              "& .MuiOutlinedInput-root": {
                                                height: 35,
                                              },
                                              "& .MuiInputBase-input": {
                                                padding: "10px 14px",
                                              },
                                            }}
                                            {...params}
                                            variant="outlined"
                                            placeholder={
                                              memoizedStreetValue.length === 0
                                                ? "Select Street"
                                                : ""
                                            }
                                            onChange={(e) => {
                                              handleStreetInputChange(e);
                                            }}
                                          />
                                        </Tooltip>
                                      )}
                                    />
                                  </FormControl>
                                </Grid>
                              );
                            } else if (option === "Location") {
                              return (
                                <>
                                  <Grid item md={2}>
                                    <Typography sx={font_Small}>
                                      Location
                                    </Typography>
                                    <FormControl fullWidth size="small">
                                      <Autocomplete
                                        sx={{ height: "31px" }}
                                        fullWidth
                                        size="small"
                                        multiple
                                        disableCloseOnSelect
                                        value={memoizedLocationValue}
                                        noOptionsText={
                                          isDropDownLoading ? (
                                            <Box
                                              sx={{
                                                display: "flex",
                                                justifyContent: "center",
                                                mt: 1,
                                                zIndex: 9999,
                                                top: "10px",
                                              }}
                                            >
                                              <CircularProgress size={20} />
                                            </Box>
                                          ) : (
                                            "No Data Available"
                                          )
                                        }
                                        renderTags={(value, getTagProps) =>
                                          value.length > 0 ? (
                                            <>
                                              <Chip
                                                label={value[0].code}
                                                {...getTagProps({ index: 0 })}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": {
                                                    padding: "0 6px",
                                                  },
                                                }}
                                              />
                                              {value.length > 1 && (
                                                <Chip
                                                  label={`+${value.length - 1}`}
                                                  sx={{
                                                    height: 20,
                                                    fontSize: "0.75rem",
                                                    ".MuiChip-label": {
                                                      padding: "0 6px",
                                                    },
                                                  }}
                                                />
                                              )}
                                            </>
                                          ) : null
                                        }
                                        limitTags={1}
                                        onChange={(e, value, reason) => {
                                          if (
                                            reason === "clear" ||
                                            value?.length === 0
                                          ) {
                                            setselectedLocation([]);
                                            setselectedPresetLocation([]);
                                            return;
                                          }

                                          if (
                                            value.length > 0 &&
                                            value[value.length - 1]?.code ===
                                              "Select All"
                                          ) {
                                            handleSelectAllLocation();
                                          } else {
                                            setselectedLocation(value);
                                          }
                                        }}
                                        options={
                                          dropDownData?.ETLocationSearchData
                                            ?.length
                                            ? [
                                                { code: "Select All" },
                                                ...dropDownData?.ETLocationSearchData,
                                              ]
                                            : dropDownData?.ETLocationSearchData ??
                                              []
                                        }
                                        getOptionLabel={(option) => {
                                          if (option?.code)
                                            return option?.code ?? "";
                                          else return "";
                                        }}
                                        renderOption={(
                                          props,
                                          option,
                                          { selected }
                                        ) => (
                                          <li {...props}>
                                            <FormGroup>
                                              <FormControlLabel
                                                control={
                                                  <Checkbox
                                                    checked={
                                                      isLocationSelected(
                                                        option
                                                      ) ||
                                                      (option?.code ===
                                                        "Select All" &&
                                                        selectedLocation?.length ===
                                                          dropDownData
                                                            ?.ETLocationSearchData
                                                            ?.length)
                                                    }
                                                  />
                                                }
                                                // label={`${option?.code}`}
                                                label={
                                                  <>
                                                    <strong>
                                                      {option.code}
                                                    </strong>
                                                  </>
                                                }
                                              />
                                            </FormGroup>
                                          </li>
                                        )}
                                        renderInput={(params) => (
                                          <Tooltip
                                            title={
                                              locationInputValue.length < 4
                                                ? "Enter at least 4 characters"
                                                : ""
                                            }
                                            arrow
                                            disableHoverListener={
                                              locationInputValue.length >= 4
                                            }
                                            placement="top" // Ensures the tooltip appears above the text field
                                          >
                                            <TextField
                                              sx={{
                                                fontSize: "12px !important",
                                                "& .MuiOutlinedInput-root": {
                                                  height: 35,
                                                },
                                                "& .MuiInputBase-input": {
                                                  padding: "10px 14px",
                                                },
                                              }}
                                              {...params}
                                              variant="outlined"
                                              // helperText={locationInputValue.length < 4 ? 'Enter at least 4 characters' : ''}
                                              placeholder={
                                                memoizedLocationValue.length ===
                                                0
                                                  ? "Select Location"
                                                  : ""
                                              }
                                              onChange={(e) => {
                                                handleLocationInputChange(e);
                                              }}
                                            />
                                          </Tooltip>
                                        )}
                                      />
                                    </FormControl>
                                  </Grid>
                                </>
                              );
                            } else if (option === "Created By") {
                              return (
                                <>
                                  <Grid item md={2}>
                                    <Typography sx={font_Small}>
                                      Created By
                                    </Typography>
                                    <FormControl
                                      fullWidth
                                      size="small"
                                      sx={{ paddingBottom: "0.7rem" }}
                                    >
                                      <Autocomplete
                                        sx={{ height: "31px" }}
                                        fullWidth
                                        multiple
                                        disableCloseOnSelect
                                        size="small"
                                        value={memoizedCreatedByValue}
                                        noOptionsText={
                                          isDropDownLoading ? (
                                            <Box
                                              sx={{
                                                display: "flex",
                                                justifyContent: "center",
                                                mt: 1,
                                                zIndex: 9999,
                                                top: "10px",
                                              }}
                                            >
                                              <CircularProgress size={20} />
                                            </Box>
                                          ) : (
                                            "No Data Available"
                                          )
                                        }
                                        renderTags={(value, getTagProps) =>
                                          value.length > 0 ? (
                                            <>
                                              <Chip
                                                label={value[0].code}
                                                {...getTagProps({ index: 0 })}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": {
                                                    padding: "0 6px",
                                                  },
                                                }}
                                              />
                                              {value.length > 1 && (
                                                <Chip
                                                  label={`+${value.length - 1}`}
                                                  sx={{
                                                    height: 20,
                                                    fontSize: "0.75rem",
                                                    ".MuiChip-label": {
                                                      padding: "0 6px",
                                                    },
                                                  }}
                                                />
                                              )}
                                            </>
                                          ) : null
                                        }
                                        onChange={(e, value, reason) => {
                                          if (
                                            reason === "clear" ||
                                            value?.length === 0
                                          ) {
                                            setselectedCreatedBy([]);
                                            setselectedPresetCreatedBy([]);
                                            return;
                                          }

                                          if (
                                            value.length > 0 &&
                                            value[value.length - 1]?.code ===
                                              "Select All"
                                          ) {
                                            handleSelectAllCreatedBy();
                                          } else {
                                            setselectedCreatedBy(value);
                                          }
                                        }}
                                        limitTags={1}
                                        options={
                                          dropDownData?.ETCreatedBySearchData
                                            ?.length
                                            ? [
                                                { code: "Select All" },
                                                ...dropDownData?.ETCreatedBySearchData,
                                              ]
                                            : dropDownData?.ETCreatedBySearchData ??
                                              []
                                        }
                                        getOptionLabel={(option) => {
                                          if (option?.code)
                                            return option?.code ?? "";
                                          else return "";
                                        }}
                                        renderOption={(
                                          props,
                                          option,
                                          { selected }
                                        ) => (
                                          <li {...props}>
                                            <FormGroup>
                                              <FormControlLabel
                                                control={
                                                  <Checkbox
                                                    checked={
                                                      isCreatedBySelected(
                                                        option
                                                      ) ||
                                                      (option?.code ===
                                                        "Select All" &&
                                                        selectedCreatedBy?.length ===
                                                          dropDownData
                                                            ?.ETCreatedBySearchData
                                                            ?.length)
                                                    }
                                                  />
                                                }
                                                // label={`${option?.code}`}
                                                label={
                                                  <>
                                                    <strong>
                                                      {option.code}
                                                    </strong>
                                                  </>
                                                }
                                              />
                                            </FormGroup>
                                          </li>
                                        )}
                                        renderInput={(params) => (
                                          <Tooltip
                                            title={
                                              createdByInputValue.length < 4
                                                ? "Enter at least 4 characters"
                                                : ""
                                            }
                                            arrow
                                            disableHoverListener={
                                              createdByInputValue.length >= 4
                                            }
                                            placement="top" // Ensures the tooltip appears above the text field
                                          >
                                            <TextField
                                              sx={{
                                                fontSize: "12px !important",
                                                "& .MuiOutlinedInput-root": {
                                                  height: 35,
                                                },
                                                "& .MuiInputBase-input": {
                                                  padding: "10px 14px",
                                                },
                                              }}
                                              {...params}
                                              variant="outlined"
                                              // helperText={createdByInputValue.length < 4 ? 'Enter at least 4 characters' : ''}
                                              placeholder={
                                                memoizedCreatedByValue.length ===
                                                0
                                                  ? "Select Created By"
                                                  : ""
                                              }
                                              onChange={(e) => {
                                                handleCreatedByInputChange(e);
                                              }}
                                            />
                                          </Tooltip>
                                        )}
                                      />
                                    </FormControl>
                                  </Grid>
                                </>
                              );
                            } else if (option === "Profit Center") {
                              return (
                                <>
                                  <Grid item md={2}>
                                    <Typography sx={font_Small}>
                                      Profit Center
                                    </Typography>
                                    <FormControl fullWidth size="small">
                                      <Autocomplete
                                        sx={{ height: "31px" }}
                                        fullWidth
                                        multiple
                                        disableCloseOnSelect
                                        size="small"
                                        value={memoizedPCValue}
                                        noOptionsText={
                                          isDropDownLoading ? (
                                            <Box
                                              sx={{
                                                display: "flex",
                                                justifyContent: "center",
                                                mt: 1,
                                                zIndex: 9999,
                                                top: "10px",
                                              }}
                                            >
                                              <CircularProgress size={20} />
                                            </Box>
                                          ) : (
                                            "No Data Available"
                                          )
                                        }
                                        renderTags={(value, getTagProps) =>
                                          value.length > 0 ? (
                                            <>
                                              <Chip
                                                label={value[0].code}
                                                {...getTagProps({ index: 0 })}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": {
                                                    padding: "0 6px",
                                                  },
                                                }}
                                              />
                                              {value.length > 1 && (
                                                <Chip
                                                  label={`+${value.length - 1}`}
                                                  sx={{
                                                    height: 20,
                                                    fontSize: "0.75rem",
                                                    ".MuiChip-label": {
                                                      padding: "0 6px",
                                                    },
                                                  }}
                                                />
                                              )}
                                            </>
                                          ) : null
                                        }
                                        onChange={(e, value, reason) => {
                                          if (
                                            reason === "clear" ||
                                            value?.length === 0
                                          ) {
                                            setselectedProfitCenter([]);
                                            setselectedPresetProfitCenter([]);
                                            return;
                                          }

                                          if (
                                            value.length > 0 &&
                                            value[value.length - 1]?.code ===
                                              "Select All"
                                          ) {
                                            handleSelectAllProfitCenter();
                                          } else {
                                            setselectedProfitCenter(value);
                                          }
                                        }}
                                        limitTags={1}
                                        options={
                                          dropDownData?.ETPCSearchData?.length
                                            ? [
                                                {
                                                  code: "Select All",
                                                  desc: "Select All",
                                                },
                                                ...dropDownData?.ETPCSearchData,
                                              ]
                                            : dropDownData?.ETPCSearchData ?? []
                                        }
                                        getOptionLabel={(option) => {
                                          if (option?.code)
                                            return `${option?.code}` ?? "";
                                          else return "";
                                        }}
                                        renderOption={(
                                          props,
                                          option,
                                          { selected }
                                        ) => (
                                          <li {...props}>
                                            <FormGroup>
                                              <FormControlLabel
                                                control={
                                                  <Checkbox
                                                    checked={
                                                      isProfitCenterSelected(
                                                        option
                                                      ) ||
                                                      (option?.code ===
                                                        "Select All" &&
                                                        selectedProfitCenter?.length ===
                                                          dropDownData
                                                            ?.ETPCSearchData
                                                            ?.length)
                                                    }
                                                  />
                                                }
                                                // label={`${option?.code} - ${option?.desc}`}
                                                label={
                                                  <>
                                                    <strong>
                                                      {option.code}
                                                    </strong>{" "}
                                                    - {option.desc}
                                                  </>
                                                }
                                              />
                                            </FormGroup>
                                          </li>
                                        )}
                                        renderInput={(params) => (
                                          <Tooltip
                                            title={
                                              pcInputValue.length < 4
                                                ? "Enter at least 4 characters"
                                                : ""
                                            }
                                            arrow
                                            disableHoverListener={
                                              pcInputValue.length >= 4
                                            }
                                            placement="top" // Ensures the tooltip appears above the text field
                                          >
                                            <TextField
                                              sx={{
                                                fontSize: "12px !important",
                                                "& .MuiOutlinedInput-root": {
                                                  height: 35,
                                                },
                                                "& .MuiInputBase-input": {
                                                  padding: "10px 14px",
                                                },
                                              }}
                                              {...params}
                                              variant="outlined"
                                              // helperText={pcInputValue.length < 4 ? 'Enter at least 4 characters' : ''}
                                              placeholder={
                                                memoizedPCValue.length === 0
                                                  ? "Select Profit Center"
                                                  : ""
                                              }
                                              onChange={(e) => {
                                                handlePCInputChange(e);
                                              }}
                                            />
                                          </Tooltip>
                                        )}
                                      />
                                    </FormControl>
                                  </Grid>
                                </>
                              );
                            } else {
                              return (
                                <Grid item md={2}>
                                  <Typography sx={font_Small}>
                                    {option}
                                  </Typography>
                                  <FormControl fullWidth size="small">
                                    <Autocomplete
                                      sx={{ height: "31px" }}
                                      fullWidth
                                      multiple
                                      disableCloseOnSelect
                                      size="small"
                                      noOptionsText={
                                        isDropDownLoading ? (
                                          <Box
                                            sx={{
                                              display: "flex",
                                              justifyContent: "center",
                                              mt: 1,
                                              zIndex: 9999,
                                              top: "10px",
                                            }}
                                          >
                                            <CircularProgress size={20} />
                                          </Box>
                                        ) : (
                                          "No Data Available"
                                        )
                                      }
                                      value={
                                        selectedValues[option]?.length > 0
                                          ? selectedValues[option]
                                          : selectedPresetValues[option]
                                              ?.length > 0
                                          ? selectedPresetValues[option]
                                          : []
                                      }
                                      renderTags={(value, getTagProps) =>
                                        value.length > 0 ? (
                                          <>
                                            <Chip
                                              label={value[0].code}
                                              {...getTagProps({ index: 0 })}
                                              sx={{
                                                height: 20,
                                                fontSize: "0.75rem",
                                                ".MuiChip-label": {
                                                  padding: "0 6px",
                                                },
                                              }}
                                            />
                                            {value.length > 1 && (
                                              <Chip
                                                label={`+${value.length - 1}`}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": {
                                                    padding: "0 6px",
                                                  },
                                                }}
                                              />
                                            )}
                                          </>
                                        ) : null
                                      }
                                      onChange={(e, value, reason) => {
                                        if (
                                          reason === "clear" ||
                                          value?.length === 0
                                        ) {
                                          setSelectedValues((prev) => ({
                                            ...prev,
                                            [option]: [],
                                          }));
                                          setSelectedPresetValues((prev) => ({
                                            ...prev,
                                            [option]: [],
                                          }));
                                          return;
                                        }

                                        if (
                                          value.length > 0 &&
                                          value[value.length - 1]?.code ===
                                            "Select All"
                                        ) {
                                          handleSelectAllOptions(option);
                                        } else {
                                          setSelectedValues((prev) => ({
                                            ...prev,
                                            [option]: value,
                                          }));
                                        }
                                      }}
                                      limitTags={1}
                                      options={
                                        dynamicOptions?.[option]?.length
                                          ? [
                                              { code: "Select All" },
                                              ...dynamicOptions?.[option],
                                            ]
                                          : dynamicOptions?.[option] ?? []
                                      }
                                      getOptionLabel={(option) =>
                                        option?.code ? `${option.code}` : ""
                                      }
                                      renderOption={(
                                        props,
                                        dropdownOption,
                                        { selected }
                                      ) => (
                                        <li {...props}>
                                          <FormGroup>
                                            <FormControlLabel
                                              control={
                                                <Checkbox
                                                  checked={
                                                    isOptionSelected(
                                                      option,
                                                      dropdownOption
                                                    ) ||
                                                    (dropdownOption?.code ===
                                                      "Select All" &&
                                                      selectedValues[option]
                                                        ?.length ===
                                                        dynamicOptions?.[option]
                                                          ?.length)
                                                  }
                                                />
                                              }
                                              // label={`${dropdownOption?.code}`}
                                              label={
                                                <>
                                                  <strong>
                                                    {dropdownOption?.code}
                                                  </strong>
                                                </>
                                              }
                                            />
                                          </FormGroup>
                                        </li>
                                      )}
                                      renderInput={(params) => (
                                        <TextField
                                          sx={{
                                            fontSize: "12px !important",
                                            "& .MuiOutlinedInput-root": {
                                              height: 35,
                                            },
                                            "& .MuiInputBase-input": {
                                              padding: "10px 14px",
                                            },
                                          }}
                                          {...params}
                                          variant="outlined"
                                          placeholder={`Select ${option}`}
                                        />
                                      )}
                                    />
                                  </FormControl>
                                </Grid>
                              );
                            }
                          })}
                        </Grid>
                      </Grid>
                    </Grid>
                    <Grid
                      container
                      style={{
                        display: "flex",
                        justifyContent: "flex-end",
                      }}
                    >
                      <Grid
                        item
                        style={{
                          display: "flex",
                          justifyContent: "space-around",
                        }}
                      >
                        <Grid>
                          <Button
                            variant="outlined"
                            sx={button_Outlined}
                            onClick={handleClear}
                          >
                            Clear
                          </Button>
                        </Grid>
                        <Grid sx={{ ...button_Marginleft }}>
                          <ReusablePreset
                            moduleName={"CostCenter"}
                            PresetObj={PresetObj}
                            handleSearch={() => getFilter()}
                            PresetMethod={PresetMethod}
                          />
                        </Grid>
                        <Grid>
                          <Button
                            variant="contained"
                            sx={{ ...button_Primary, ...button_Marginleft }}
                            onClick={() => getFilter()}
                          >
                            Search
                          </Button>
                        </Grid>
                      </Grid>
                    </Grid>
                  </AccordionDetails>
                </Accordion>
              </Grid>
            </Grid> */}

            <Grid container sx={container_filter}>
              <Grid item md={12}>
                <StyledAccordion defaultExpanded={true}>
                  <StyledAccordionSummary
                    expandIcon={
                      <ExpandMoreIcon
                        sx={{ fontSize: "1.25rem", color: colors.primary.main }}
                      />
                    }
                    aria-controls="panel1a-content"
                    id="panel1a-header"
                    // sx={{
                    //   minHeight: "2rem !important",
                    //   margin: "0px !important",
                    // }}
                  >
                    <FilterListIcon
                      sx={{
                        fontSize: "1.25rem",
                        marginRight: 1,
                        color: colors.primary.main,
                      }}
                    />
                    <Typography
                      sx={{
                        fontSize: "0.875rem",
                        fontWeight: 600,
                        color: colors.primary.dark,
                      }}
                    >
                      Search Cost Center
                    </Typography>
                  </StyledAccordionSummary>
                  <AccordionDetails sx={{ padding: "0.5rem 1rem 0.5rem" }}>
                    <Grid
                      container
                      rowSpacing={1}
                      spacing={2}
                      justifyContent="space-between"
                      alignItems="center"
                      // sx={{ marginBottom: "0.5rem" }}
                    >
                      <Grid
                        container
                        spacing={1}
                        sx={{ padding: "0rem 1rem 0.5rem" }}
                      >
                        <Grid item md={2}>
                          <LabelTypography sx={font_Small}>
                            Controlling Area
                          </LabelTypography>
                          <FormControl size="small" fullWidth>
                            <Autocomplete
                              sx={{ height: "31px" }}
                              fullWidth
                              size="small"
                              value={rmSearchForm?.controllingArea}
                              onChange={handleControllingArea}
                              noOptionsText={
                                isDropDownLoading ? (
                                  <Box
                                    sx={{
                                      display: "flex",
                                      justifyContent: "center",
                                      mt: 1,
                                      zIndex: 9999,
                                      top: "10px",
                                    }}
                                  >
                                    <CircularProgress size={20} />
                                  </Box>
                                ) : (
                                  "No Data Available"
                                )
                              }
                              options={dropDownData?.ControllingArea ?? []}
                              getOptionLabel={(option) => {
                                if (option?.code)
                                  return `${option?.code}` ?? "";
                                else return "";
                              }}
                              renderOption={(props, option) => (
                                <li {...props}>
                                  <Typography style={{ fontSize: 12 }}>
                                    {/* {`${option?.code}-${option?.desc}`} */}
                                    {option?.desc ? (
                                      <>
                                        <strong>{option.code}</strong> -{" "}
                                        {option.desc}
                                      </>
                                    ) : (
                                      <strong>{option.code}</strong>
                                    )}
                                  </Typography>
                                </li>
                              )}
                              renderInput={(params) => (
                                <TextField
                                  sx={{ fontSize: "12px !important" }}
                                  {...params}
                                  variant="outlined"
                                  placeholder="Select Controlling Area"
                                />
                              )}
                            />
                          </FormControl>
                        </Grid>
                        <Grid item md={2}>
                          <LabelTypography sx={font_Small}>
                            Cost Center
                          </LabelTypography>
                          <FormControl
                            fullWidth
                            size="small"
                            sx={{ paddingBottom: "0.7rem" }}
                          >
                            <Autocomplete
                              sx={{ height: "31px" }}
                              fullWidth
                              multiple
                              disableCloseOnSelect
                              size="small"
                              value={memoizedCCValue}
                              // loading={isDropDownLoading}
                              noOptionsText={
                                isDropDownLoading ? (
                                  <Box
                                    sx={{
                                      display: "flex",
                                      justifyContent: "center",
                                      mt: 1,
                                      zIndex: 9999,
                                      top: "10px",
                                    }}
                                  >
                                    <CircularProgress size={20} />
                                  </Box>
                                ) : (
                                  "No Data Available"
                                )
                              }
                              onChange={(e, value, reason) => {
                                if (reason === "clear" || value?.length === 0) {
                                  setselectedCostCenter([]);
                                  setselectedPresetCostCenter([]);
                                  return;
                                }

                                if (
                                  value.length > 0 &&
                                  value[value.length - 1]?.code === "Select All"
                                ) {
                                  handleSelectAllCostCenter();
                                } else {
                                  setselectedCostCenter(value);
                                }
                              }}
                              renderTags={(value, getTagProps) =>
                                value.length > 0 ? (
                                  <>
                                    <Chip
                                      label={value[0].code}
                                      {...getTagProps({ index: 0 })}
                                      sx={{
                                        height: 20,
                                        fontSize: "0.75rem",
                                        ".MuiChip-label": { padding: "0 6px" },
                                      }}
                                    />
                                    {value.length > 1 && (
                                      <Chip
                                        label={`+${value.length - 1}`}
                                        sx={{
                                          height: 20,
                                          fontSize: "0.75rem",
                                          ".MuiChip-label": {
                                            padding: "0 6px",
                                          },
                                        }}
                                      />
                                    )}
                                  </>
                                ) : null
                              }
                              limitTags={1}
                              options={
                                dropDownData?.ETCCSearchData?.length
                                  ? [
                                      {
                                        code: "Select All",
                                        desc: "Select All",
                                      },
                                      ...dropDownData?.ETCCSearchData,
                                    ]
                                  : dropDownData?.ETCCSearchData ?? []
                              }
                              getOptionLabel={(option) => {
                                if (option?.code)
                                  return `${option?.code}` ?? "";
                                else return "";
                              }}
                              renderOption={(props, option, { selected }) => (
                                <li {...props}>
                                  <FormGroup>
                                    <FormControlLabel
                                      control={
                                        <Checkbox
                                          checked={
                                            isCostCenterSelected(option) ||
                                            (option?.code === "Select All" &&
                                              selectedCostCenter?.length ===
                                                dropDownData?.ETCCSearchData
                                                  ?.length)
                                          }
                                        />
                                      }
                                      // label={`${option?.code} - ${option?.desc}`}
                                      label={
                                        <>
                                          <strong>{option.code}</strong> -{" "}
                                          {option.desc}
                                        </>
                                      }
                                    />
                                  </FormGroup>
                                </li>
                              )}
                              renderInput={(params) => (
                                <Tooltip
                                  title={
                                    ccInputValue.length < 4
                                      ? "Enter at least 4 characters"
                                      : ""
                                  }
                                  arrow
                                  disableHoverListener={
                                    ccInputValue.length >= 4
                                  }
                                  placement="top" // Ensures the tooltip appears above the text field
                                >
                                  <TextField
                                    sx={{
                                      fontSize: "12px !important",
                                      "& .MuiOutlinedInput-root": {
                                        height: 35,
                                      },
                                      "& .MuiInputBase-input": {
                                        padding: "10px 14px",
                                      },
                                    }}
                                    {...params}
                                    variant="outlined"
                                    // helperText={ccInputValue.length < 4 ? 'Enter at least 4 characters' : ''}
                                    placeholder={
                                      memoizedCCValue.length === 0
                                        ? "Select Cost Center"
                                        : ""
                                    }
                                    onChange={(e) => {
                                      handleCCInputChange(e);
                                    }}
                                  />
                                </Tooltip>
                              )}
                            />
                          </FormControl>
                        </Grid>
                        <Grid item md={2}>
                          <LabelTypography sx={font_Small}>
                            Company Code
                          </LabelTypography>
                          <FormControl
                            fullWidth
                            size="small"
                            sx={{ paddingBottom: "0.7rem" }}
                          >
                            <Autocomplete
                              sx={{ height: "31px" }}
                              fullWidth
                              size="small"
                              multiple
                              limitTags={1}
                              noOptionsText={
                                isDropDownLoading ? (
                                  <Box
                                    sx={{
                                      display: "flex",
                                      justifyContent: "center",
                                      mt: 1,
                                      zIndex: 9999,
                                      top: "10px",
                                    }}
                                  >
                                    <CircularProgress size={20} />
                                  </Box>
                                ) : (
                                  "No Data Available"
                                )
                              }
                              options={[
                                { code: "Select All", desc: "Select All" },
                                ...(dropDownData?.CompanyCode ?? []),
                              ]}
                              disableCloseOnSelect
                              onChange={(e, value, reason) => {
                                if (reason === "clear" || value?.length === 0) {
                                  setselectedComanyCode([]);
                                  setselectedPresetComanyCode([]);
                                  return;
                                }

                                console.log(value, "valueinauto");
                                if (
                                  value.length > 0 &&
                                  value[value.length - 1]?.code === "Select All"
                                ) {
                                  handleSelectAllCompanyCodes();
                                } else {
                                  setselectedComanyCode(value);
                                }
                              }}
                              getOptionLabel={(option) => {
                                if (option?.code)
                                  return `${option?.code}` ?? "";
                                else return "";
                              }}
                              value={
                                selectedComanyCode.length > 0
                                  ? selectedComanyCode
                                  : selectedPresetComanyCode.length > 0
                                  ? selectedPresetComanyCode
                                  : []
                              }
                              renderTags={(value, getTagProps) =>
                                value.length > 0 ? (
                                  <>
                                    <Chip
                                      label={value[0].code}
                                      {...getTagProps({ index: 0 })}
                                      sx={{
                                        height: 20,
                                        fontSize: "0.75rem",
                                        ".MuiChip-label": { padding: "0 6px" },
                                      }}
                                    />
                                    {value.length > 1 && (
                                      <Chip
                                        label={`+${value.length - 1}`}
                                        sx={{
                                          height: 20,
                                          fontSize: "0.75rem",
                                          ".MuiChip-label": {
                                            padding: "0 6px",
                                          },
                                        }}
                                      />
                                    )}
                                  </>
                                ) : null
                              }
                              renderOption={(props, option, { selected }) => (
                                <li {...props}>
                                  <FormGroup>
                                    <FormControlLabel
                                      control={
                                        <Checkbox
                                          checked={
                                            isCompanyCodeSelected(option) ||
                                            (option?.code === "Select All" &&
                                              selectedComanyCode?.length ===
                                                dropDownData?.CompanyCode
                                                  ?.length)
                                          }
                                        />
                                      }
                                      // label={`${option?.code} - ${option?.desc}`}
                                      label={
                                        <>
                                          <strong>{option.code}</strong> -{" "}
                                          {option.desc}
                                        </>
                                      }
                                    />
                                  </FormGroup>
                                </li>
                              )}
                              renderInput={(params) => (
                                <TextField
                                  sx={{
                                    fontSize: "12px !important",
                                    "& .MuiOutlinedInput-root": {
                                      height: 35,
                                    },
                                    "& .MuiInputBase-input": {
                                      padding: "10px 14px",
                                    },
                                  }}
                                  {...params}
                                  variant="outlined"
                                  placeholder="Select Company Code"
                                />
                              )}
                            />
                          </FormControl>
                        </Grid>
                        <Grid item md={2}>
                          <LabelTypography sx={font_Small}>
                            Long Description
                          </LabelTypography>
                          <FormControl fullWidth size="small">
                            <Autocomplete
                              sx={{ height: "31px" }}
                              fullWidth
                              size="small"
                              multiple
                              disableCloseOnSelect
                              value={memoizedLDValue}
                              noOptionsText={
                                isDropDownLoading ? (
                                  <Box
                                    sx={{
                                      display: "flex",
                                      justifyContent: "center",
                                      mt: 1,
                                      zIndex: 9999,
                                      top: "10px",
                                    }}
                                  >
                                    <CircularProgress size={20} />
                                  </Box>
                                ) : (
                                  "No Data Available"
                                )
                              }
                              renderTags={(value, getTagProps) =>
                                value.length > 0 ? (
                                  <>
                                    <Chip
                                      label={value[0].code}
                                      {...getTagProps({ index: 0 })}
                                      sx={{
                                        height: 20,
                                        fontSize: "0.75rem",
                                        ".MuiChip-label": { padding: "0 6px" },
                                      }}
                                    />
                                    {value.length > 1 && (
                                      <Chip
                                        label={`+${value.length - 1}`}
                                        sx={{
                                          height: 20,
                                          fontSize: "0.75rem",
                                          ".MuiChip-label": {
                                            padding: "0 6px",
                                          },
                                        }}
                                      />
                                    )}
                                  </>
                                ) : null
                              }
                              onChange={(e, value, reason) => {
                                if (reason === "clear" || value?.length === 0) {
                                  setselectedDescription([]);
                                  setselectedPresetDescription([]);
                                  return;
                                }

                                if (
                                  value.length > 0 &&
                                  value[value.length - 1]?.code === "Select All"
                                ) {
                                  handleSelectAllDescription();
                                } else {
                                  setselectedDescription(value);
                                }
                              }}
                              limitTags={1}
                              options={
                                dropDownData?.ETDescriptionSearchData?.length
                                  ? [
                                      { code: "Select All" },
                                      ...dropDownData?.ETDescriptionSearchData,
                                    ]
                                  : dropDownData?.ETDescriptionSearchData ?? []
                              }
                              getOptionLabel={(option) => {
                                if (option?.code) return option?.code ?? "";
                                else return "";
                              }}
                              renderOption={(props, option, { selected }) => (
                                <li {...props}>
                                  <FormGroup>
                                    <FormControlLabel
                                      control={
                                        <Checkbox
                                          checked={
                                            isDescriptionSelected(option) ||
                                            (option?.code === "Select All" &&
                                              selectedDescription?.length ===
                                                dropDownData
                                                  ?.ETDescriptionSearchData
                                                  ?.length)
                                          }
                                        />
                                      }
                                      // label={`${option?.code}`}
                                      label={
                                        <>
                                          <strong>{option.code}</strong>
                                        </>
                                      }
                                    />
                                  </FormGroup>
                                </li>
                              )}
                              renderInput={(params) => (
                                <Tooltip
                                  title={
                                    descInputValue.length < 4
                                      ? "Enter at least 4 characters"
                                      : ""
                                  }
                                  arrow
                                  disableHoverListener={
                                    descInputValue.length >= 4
                                  }
                                  placement="top" // Ensures the tooltip appears above the text field
                                >
                                  <TextField
                                    sx={{
                                      fontSize: "12px !important",
                                      "& .MuiOutlinedInput-root": {
                                        height: 35,
                                      },
                                      "& .MuiInputBase-input": {
                                        padding: "10px 14px",
                                      },
                                    }}
                                    {...params}
                                    variant="outlined"
                                    placeholder={
                                      memoizedLDValue.length === 0
                                        ? "Select Long Description"
                                        : ""
                                    }
                                    onChange={(e) => {
                                      handleDescInputChange(e);
                                    }}
                                  />
                                </Tooltip>
                              )}
                            />
                          </FormControl>
                        </Grid>
                        <Grid item md={2}>
                          <LabelTypography sx={font_Small}>
                            Cost Center Category
                          </LabelTypography>
                          <FormControl fullWidth size="small">
                            <Autocomplete
                              sx={{ height: "31px" }}
                              fullWidth
                              size="small"
                              multiple
                              disableCloseOnSelect
                              value={
                                selectedCCcategory.length > 0
                                  ? selectedCCcategory
                                  : selectedPresetCCcategory.length > 0
                                  ? selectedPresetCCcategory
                                  : []
                              }
                              noOptionsText={
                                isDropDownLoading ? (
                                  <Box
                                    sx={{
                                      display: "flex",
                                      justifyContent: "center",
                                      mt: 1,
                                      zIndex: 9999,
                                      top: "10px",
                                    }}
                                  >
                                    <CircularProgress size={20} />
                                  </Box>
                                ) : (
                                  "No Data Available"
                                )
                              }
                              renderTags={(value, getTagProps) =>
                                value.length > 0 ? (
                                  <>
                                    <Chip
                                      label={value[0].code}
                                      {...getTagProps({ index: 0 })}
                                      sx={{
                                        height: 20,
                                        fontSize: "0.75rem",
                                        ".MuiChip-label": { padding: "0 6px" },
                                      }}
                                    />
                                    {value.length > 1 && (
                                      <Chip
                                        label={`+${value.length - 1}`}
                                        sx={{
                                          height: 20,
                                          fontSize: "0.75rem",
                                          ".MuiChip-label": {
                                            padding: "0 6px",
                                          },
                                        }}
                                      />
                                    )}
                                  </>
                                ) : null
                              }
                              onChange={(e, value, reason) => {
                                if (reason === "clear" || value?.length === 0) {
                                  setselectedCCcategory([]);
                                  setselectedPresetCCcategory([]);
                                  return;
                                }

                                if (
                                  value.length > 0 &&
                                  value[value.length - 1]?.code === "Select All"
                                ) {
                                  handleSelectAllCCcategory();
                                } else {
                                  setselectedCCcategory(value);
                                }
                              }}
                              limitTags={1}
                              options={
                                dropDownData?.CostCenterCategorySearch?.length
                                  ? [
                                      {
                                        code: "Select All",
                                        desc: "Select All",
                                      },
                                      ...dropDownData?.CostCenterCategorySearch,
                                    ]
                                  : dropDownData?.CostCenterCategorySearch ?? []
                              }
                              getOptionLabel={(option) => {
                                if (option?.code)
                                  return `${option?.code}` ?? "";
                                else return "";
                              }}
                              renderOption={(props, option, { selected }) => (
                                <li {...props}>
                                  <FormGroup>
                                    <FormControlLabel
                                      control={
                                        <Checkbox
                                          checked={
                                            isCCcategorySelected(option) ||
                                            (option?.code === "Select All" &&
                                              selectedCCcategory?.length ===
                                                dropDownData
                                                  ?.CostCenterCategorySearch
                                                  ?.length)
                                          }
                                        />
                                      }
                                      // label={`${option?.code} - ${option?.desc}`}
                                      label={
                                        <>
                                          <strong>{option.code}</strong> -{" "}
                                          {option.desc}
                                        </>
                                      }
                                    />
                                  </FormGroup>
                                </li>
                              )}
                              renderInput={(params) => (
                                <TextField
                                  sx={{
                                    fontSize: "12px !important",
                                    "& .MuiOutlinedInput-root": {
                                      height: 35,
                                    },
                                    "& .MuiInputBase-input": {
                                      padding: "10px 14px",
                                    },
                                  }}
                                  {...params}
                                  variant="outlined"
                                  placeholder="Select Cost Center Category"
                                />
                              )}
                            />
                          </FormControl>
                        </Grid>
                        <Grid item md={2}>
                          <LabelTypography sx={font_Small}>
                            Person Responsible
                          </LabelTypography>
                          <FormControl fullWidth size="small">
                            <Autocomplete
                              sx={{ height: "31px" }}
                              fullWidth
                              size="small"
                              multiple
                              disableCloseOnSelect
                              value={memoizedPRValue}
                              noOptionsText={
                                isDropDownLoading ? (
                                  <Box
                                    sx={{
                                      display: "flex",
                                      justifyContent: "center",
                                      mt: 1,
                                      zIndex: 9999,
                                      top: "10px",
                                    }}
                                  >
                                    <CircularProgress size={20} />
                                  </Box>
                                ) : (
                                  "No Data Available"
                                )
                              }
                              onChange={(e, value, reason) => {
                                if (reason === "clear" || value?.length === 0) {
                                  setselectedPersonResponsible([]);
                                  setselectedPresetPersonResponsible([]);
                                  return;
                                }

                                if (
                                  value.length > 0 &&
                                  value[value.length - 1]?.code === "Select All"
                                ) {
                                  handleSelectAllPersonResponsible();
                                } else {
                                  setselectedPersonResponsible(value);
                                }
                              }}
                              renderTags={(value, getTagProps) =>
                                value.length > 0 ? (
                                  <>
                                    <Chip
                                      label={value[0].code}
                                      {...getTagProps({ index: 0 })}
                                      sx={{
                                        height: 20,
                                        fontSize: "0.75rem",
                                        ".MuiChip-label": { padding: "0 6px" },
                                      }}
                                    />
                                    {value.length > 1 && (
                                      <Chip
                                        label={`+${value.length - 1}`}
                                        sx={{
                                          height: 20,
                                          fontSize: "0.75rem",
                                          ".MuiChip-label": {
                                            padding: "0 6px",
                                          },
                                        }}
                                      />
                                    )}
                                  </>
                                ) : null
                              }
                              limitTags={1}
                              options={
                                dropDownData?.ETPersonResponsibleSearch?.length
                                  ? [
                                      { code: "Select All" },
                                      ...dropDownData?.ETPersonResponsibleSearch,
                                    ]
                                  : dropDownData?.ETPersonResponsibleSearch ??
                                    []
                              }
                              getOptionLabel={(option) => {
                                if (option?.code) return option?.code ?? "";
                                else return "";
                              }}
                              renderOption={(props, option, { selected }) => (
                                <li {...props}>
                                  <FormGroup>
                                    <FormControlLabel
                                      control={
                                        <Checkbox
                                          checked={
                                            isPersonResponsibleSelected(
                                              option
                                            ) ||
                                            (option?.code === "Select All" &&
                                              selectedPersonResponsible?.length ===
                                                dropDownData
                                                  ?.ETPersonResponsibleSearch
                                                  ?.length)
                                          }
                                        />
                                      }
                                      // label={`${option?.code}`}
                                      label={
                                        <>
                                          <strong>{option.code}</strong>
                                        </>
                                      }
                                    />
                                  </FormGroup>
                                </li>
                              )}
                              renderInput={(params) => (
                                <Tooltip
                                  title={
                                    personInputValue.length < 4
                                      ? "Enter at least 4 characters"
                                      : ""
                                  }
                                  arrow
                                  disableHoverListener={
                                    personInputValue.length >= 4
                                  }
                                  placement="top" // Ensures the tooltip appears above the text field
                                >
                                  <TextField
                                    sx={{
                                      fontSize: "12px !important",
                                      "& .MuiOutlinedInput-root": {
                                        height: 35,
                                      },
                                      "& .MuiInputBase-input": {
                                        padding: "10px 14px",
                                      },
                                    }}
                                    {...params}
                                    variant="outlined"
                                    // helperText={personInputValue.length < 4 ? 'Enter at least 4 characters' : ''}
                                    placeholder={
                                      memoizedPRValue.length === 0
                                        ? "Select Person Responsible"
                                        : ""
                                    }
                                    onChange={(e) => {
                                      handlePersonResInputChange(e);
                                    }}
                                  />
                                </Tooltip>
                              )}
                            />
                          </FormControl>
                        </Grid>
                        <Grid item md={2}>
                          <LabelTypography sx={font_Small}>
                            User Responsible
                          </LabelTypography>
                          <FormControl
                            size="small"
                            fullWidth
                            sx={{ paddingBottom: "0.7rem" }}
                          >
                            <Autocomplete
                              sx={{ height: "31px" }}
                              fullWidth
                              size="small"
                              multiple
                              disableCloseOnSelect
                              value={memoizedURValue}
                              noOptionsText={
                                isDropDownLoading ? (
                                  <Box
                                    sx={{
                                      display: "flex",
                                      justifyContent: "center",
                                      mt: 1,
                                      zIndex: 9999,
                                      top: "10px",
                                    }}
                                  >
                                    <CircularProgress size={20} />
                                  </Box>
                                ) : (
                                  "No Data Available"
                                )
                              }
                              onChange={(e, value, reason) => {
                                if (reason === "clear" || value?.length === 0) {
                                  setselectedUserResponsible([]);
                                  setselectedPresetUserResponsible([]);
                                  return;
                                }

                                if (
                                  value.length > 0 &&
                                  value[value.length - 1]?.code === "Select All"
                                ) {
                                  handleSelectAllUserResponsible();
                                } else {
                                  setselectedUserResponsible(value);
                                }
                              }}
                              limitTags={1}
                              options={
                                dropDownData?.ETUserResponsible?.length
                                  ? [
                                      { code: "Select All" },
                                      ...dropDownData?.ETUserResponsible,
                                    ]
                                  : dropDownData?.ETUserResponsible ?? []
                              }
                              getOptionLabel={(option) => {
                                if (option?.code) return option?.code ?? "";
                                else return "";
                              }}
                              renderOption={(props, option, { selected }) => (
                                <li {...props}>
                                  <FormGroup>
                                    <FormControlLabel
                                      control={
                                        <Checkbox
                                          checked={
                                            isUserResponsibleSelected(option) ||
                                            (option?.code === "Select All" &&
                                              selectedUserResponsible?.length ===
                                                dropDownData?.ETUserResponsible
                                                  ?.length)
                                          }
                                        />
                                      }
                                      // label={`${option?.code}`}
                                      label={
                                        <>
                                          <strong>{option.code}</strong>
                                        </>
                                      }
                                    />
                                  </FormGroup>
                                </li>
                              )}
                              renderTags={(value, getTagProps) =>
                                value.length > 0 ? (
                                  <>
                                    <Chip
                                      label={value[0].code}
                                      {...getTagProps({ index: 0 })}
                                      sx={{
                                        height: 20,
                                        fontSize: "0.75rem",
                                        ".MuiChip-label": { padding: "0 6px" },
                                      }}
                                    />
                                    {value.length > 1 && (
                                      <Chip
                                        label={`+${value.length - 1}`}
                                        sx={{
                                          height: 20,
                                          fontSize: "0.75rem",
                                          ".MuiChip-label": {
                                            padding: "0 6px",
                                          },
                                        }}
                                      />
                                    )}
                                  </>
                                ) : null
                              }
                              renderInput={(params) => (
                                <Tooltip
                                  title={
                                    userInputValue.length < 4
                                      ? "Enter at least 4 characters"
                                      : ""
                                  }
                                  arrow
                                  disableHoverListener={
                                    userInputValue.length >= 4
                                  }
                                  placement="top" // Ensures the tooltip appears above the text field
                                >
                                  <TextField
                                    sx={{
                                      fontSize: "12px !important",
                                      "& .MuiOutlinedInput-root": {
                                        height: 35,
                                      },
                                      "& .MuiInputBase-input": {
                                        padding: "10px 14px",
                                      },
                                    }}
                                    {...params}
                                    variant="outlined"
                                    // helperText={userInputValue.length < 4 ? 'Enter at least 4 characters' : ''}
                                    placeholder={
                                      memoizedURValue.length === 0
                                        ? "Select User Responsible"
                                        : ""
                                    }
                                    onChange={(e) => {
                                      handleUserResInputChange(e);
                                    }}
                                  />
                                </Tooltip>
                              )}
                            />
                          </FormControl>
                        </Grid>
                        <Grid item md={2}>
                          <LabelTypography sx={font_Small}>
                            Blocking Status
                          </LabelTypography>
                          <FormControl fullWidth size="small">
                            <Select
                              placeholder={"Select Blocking Status"}
                              sx={{ height: "35px" }}
                              size="small"
                              value={rmSearchForm?.blockingStatus}
                              name="blockingStatus"
                              onChange={(e) => handleBlockingStatus(e)}
                              displayEmpty={true}
                              // input={<OutlinedInput label="Tag" />}
                              // renderValue={(selected) => selected}
                              MenuProps={MenuProps}
                            >
                              {/* <MenuItem sx={font_Small} disabled value={""}>
                                <div
                                  style={{
                                    color: "#C1C1C1",
                                    fontSize: "12px",
                                  }}
                                >
                                  Select Blocking Status
                                </div>
                              </MenuItem> */}
                              {names?.map((name) => (
                                <MenuItem
                                  sx={font_Small}
                                  key={name}
                                  value={name}
                                  style={{
                                    fontSize: "12px !important",
                                    height: "35px",
                                  }}
                                >
                                  {/* <Checkbox
                                  checked={
                                    rbSearchForm?.reqStatus.indexOf(name) > -1
                                  }
                                /> */}
                                  <ListItemText
                                    sx={font_Small}
                                    primary={name}
                                    style={{ fontSize: "12px !important" }}
                                  />
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                        </Grid>
                        {/* dynamic filter// */}
                        <Grid item md={2}>
                          <LabelTypography sx={font_Small}>
                            Add New Filters
                          </LabelTypography>
                          <FormControl fullWidth>
                            <Select
                              sx={{
                                font_Small,
                                height: "35px",
                                fontSize: "12px",
                              }}
                              // fullWidth
                              size="small"
                              multiple
                              limitTags={2}
                              value={selectedOptions}
                              onChange={handleSelection}
                              renderValue={(selected) => selected.join(", ")}
                              MenuProps={{
                                MenuProps,
                              }}
                              endAdornment={
                                selectedOptions.length > 0 && (
                                  <InputAdornment
                                    position="end"
                                    sx={{ marginRight: "15px" }}
                                  >
                                    <IconButton
                                      size="small"
                                      sx={{ height: "10px", width: "10px" }}
                                      onClick={() => {
                                        // handleClearPayload(selectedOptions)
                                        setSelectedOptions([]);
                                      }}
                                      aria-label="Clear selections"
                                    >
                                      <ClearIcon />
                                    </IconButton>
                                  </InputAdornment>
                                )
                              }
                            >
                              {items?.map((option) => (
                                <MenuItem
                                  key={option.title}
                                  value={option.title}
                                >
                                  <Checkbox
                                    checked={
                                      selectedOptions.indexOf(option.title) > -1
                                    }
                                  />
                                  {option.title}
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                          <Grid
                            style={{
                              display: "flex",
                              justifyContent: "space-around",
                            }}
                          ></Grid>
                        </Grid>
                      </Grid>
                      <Grid
                        container
                        rowSpacing={1}
                        spacing={2}
                        justifyContent="space-between"
                        alignItems="center"
                        sx={{ padding: "0.5rem 1rem 0.5rem" }}
                        // sx={{ marginBottom: "0.5rem" }}
                      >
                        <Grid
                          container
                          spacing={1}
                          sx={{ padding: "0rem 1rem 0.5rem" }}
                        >
                          {selectedOptions?.map((option, i) => {
                            console.log(
                              "fercvalue",
                              option,
                              rmSearchForm[option],
                              rmSearchForm
                            );
                            if (option === "Short Description") {
                              return (
                                <>
                                  <Grid item md={2}>
                                    <LabelTypography sx={font_Small}>
                                      Short Description
                                    </LabelTypography>
                                    <FormControl
                                      fullWidth
                                      size="small"
                                      sx={{ paddingBottom: "0.7rem" }}
                                    >
                                      <Autocomplete
                                        sx={{ height: "31px" }}
                                        fullWidth
                                        size="small"
                                        multiple
                                        disableCloseOnSelect
                                        value={memoizedSDValue}
                                        noOptionsText={
                                          isDropDownLoading ? (
                                            <Box
                                              sx={{
                                                display: "flex",
                                                justifyContent: "center",
                                                mt: 1,
                                                zIndex: 9999,
                                                top: "10px",
                                              }}
                                            >
                                              <CircularProgress size={20} />
                                            </Box>
                                          ) : (
                                            "No Data Available"
                                          )
                                        }
                                        renderTags={(value, getTagProps) =>
                                          value.length > 0 ? (
                                            <>
                                              <Chip
                                                label={value[0].code}
                                                {...getTagProps({ index: 0 })}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": {
                                                    padding: "0 6px",
                                                  },
                                                }}
                                              />
                                              {value.length > 1 && (
                                                <Chip
                                                  label={`+${value.length - 1}`}
                                                  sx={{
                                                    height: 20,
                                                    fontSize: "0.75rem",
                                                    ".MuiChip-label": {
                                                      padding: "0 6px",
                                                    },
                                                  }}
                                                />
                                              )}
                                            </>
                                          ) : null
                                        }
                                        onChange={(e, value, reason) => {
                                          if (
                                            reason === "clear" ||
                                            value?.length === 0
                                          ) {
                                            setselectedCostCenterName([]);
                                            setselectedPresetCostCenterName([]);
                                            return;
                                          }

                                          if (
                                            value.length > 0 &&
                                            value[value.length - 1]?.code ===
                                              "Select All"
                                          ) {
                                            handleSelectAllCostCenterName();
                                          } else {
                                            setselectedCostCenterName(value);
                                          }
                                        }}
                                        limitTags={1}
                                        options={
                                          dropDownData?.ETCCNameSearchData
                                            ?.length
                                            ? [
                                                { code: "Select All" },
                                                ...dropDownData?.ETCCNameSearchData,
                                              ]
                                            : dropDownData?.ETCCNameSearchData ??
                                              []
                                        }
                                        getOptionLabel={(option) => {
                                          if (option?.code)
                                            return option?.code ?? "";
                                          else return "";
                                        }}
                                        renderOption={(
                                          props,
                                          option,
                                          { selected }
                                        ) => (
                                          <li {...props}>
                                            <FormGroup>
                                              <FormControlLabel
                                                control={
                                                  <Checkbox
                                                    checked={
                                                      isCostCenterNameSelected(
                                                        option
                                                      ) ||
                                                      (option?.code ===
                                                        "Select All" &&
                                                        selectedCostCenterName?.length ===
                                                          dropDownData
                                                            ?.ETCCNameSearchData
                                                            ?.length)
                                                    }
                                                  />
                                                }
                                                // label={`${option?.code}`}
                                                label={
                                                  <>
                                                    <strong>
                                                      {option.code}
                                                    </strong>
                                                  </>
                                                }
                                              />
                                            </FormGroup>
                                          </li>
                                        )}
                                        renderInput={(params) => (
                                          <Tooltip
                                            title={
                                              ccNameInputValue.length < 4
                                                ? "Enter at least 4 characters"
                                                : ""
                                            }
                                            arrow
                                            disableHoverListener={
                                              ccNameInputValue.length >= 4
                                            }
                                            placement="top" // Ensures the tooltip appears above the text field
                                          >
                                            <TextField
                                              sx={{
                                                fontSize: "12px !important",
                                                "& .MuiOutlinedInput-root": {
                                                  height: 35,
                                                },
                                                "& .MuiInputBase-input": {
                                                  padding: "10px 14px",
                                                },
                                              }}
                                              {...params}
                                              variant="outlined"
                                              placeholder={
                                                memoizedSDValue.length === 0
                                                  ? "Select Short Description"
                                                  : ""
                                              }
                                              onChange={(e) => {
                                                handleCCNameInputChange(e);
                                              }}
                                            />
                                          </Tooltip>
                                        )}
                                      />
                                    </FormControl>
                                  </Grid>
                                </>
                              );
                            } else if (option === "Created On") {
                              return (
                                <Grid item md={2}>
                                  <LabelTypography sx={font_Small}>
                                    {option}
                                  </LabelTypography>
                                  <FormControl size="small" fullWidth>
                                    <LocalizationProvider
                                      dateAdapter={AdapterDateFns}
                                    >
                                      <DateRange
                                        handleDate={handleDate}
                                        date={selectedDateRange}
                                      />
                                    </LocalizationProvider>
                                  </FormControl>
                                </Grid>
                              );
                            } else if (option === "Street") {
                              return (
                                <Grid item md={2}>
                                  <LabelTypography sx={font_Small}>
                                    Street
                                  </LabelTypography>
                                  <FormControl fullWidth size="small">
                                    <Autocomplete
                                      sx={{ height: "31px" }}
                                      fullWidth
                                      size="small"
                                      multiple
                                      disableCloseOnSelect
                                      value={memoizedStreetValue}
                                      noOptionsText={
                                        isDropDownLoading ? (
                                          <Box
                                            sx={{
                                              display: "flex",
                                              justifyContent: "center",
                                              mt: 1,
                                              zIndex: 9999,
                                              top: "10px",
                                            }}
                                          >
                                            <CircularProgress size={20} />
                                          </Box>
                                        ) : (
                                          "No Data Available"
                                        )
                                      }
                                      limitTags={1}
                                      renderTags={(value, getTagProps) =>
                                        value.length > 0 ? (
                                          <>
                                            <Chip
                                              label={value[0].code}
                                              {...getTagProps({ index: 0 })}
                                              sx={{
                                                height: 20,
                                                fontSize: "0.75rem",
                                                ".MuiChip-label": {
                                                  padding: "0 6px",
                                                },
                                              }}
                                            />
                                            {value.length > 1 && (
                                              <Chip
                                                label={`+${value.length - 1}`}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": {
                                                    padding: "0 6px",
                                                  },
                                                }}
                                              />
                                            )}
                                          </>
                                        ) : null
                                      }
                                      onChange={(e, value, reason) => {
                                        if (
                                          reason === "clear" ||
                                          value?.length === 0
                                        ) {
                                          setselectedStreet([]);
                                          setselectedPresetStreet([]);
                                          return;
                                        }

                                        if (
                                          value.length > 0 &&
                                          value[value.length - 1]?.code ===
                                            "Select All"
                                        ) {
                                          handleSelectAllStreet();
                                        } else {
                                          setselectedStreet(value);
                                        }
                                      }}
                                      options={
                                        dropDownData?.ETStreetSearchData?.length
                                          ? [
                                              { code: "Select All" },
                                              ...dropDownData?.ETStreetSearchData,
                                            ]
                                          : dropDownData?.ETStreetSearchData ??
                                            []
                                      }
                                      getOptionLabel={(option) => {
                                        if (option?.code)
                                          return option?.code ?? "";
                                        else return "";
                                      }}
                                      renderOption={(
                                        props,
                                        option,
                                        { selected }
                                      ) => (
                                        <li {...props}>
                                          <FormGroup>
                                            <FormControlLabel
                                              control={
                                                <Checkbox
                                                  checked={
                                                    isStreetSelected(option) ||
                                                    (option?.code ===
                                                      "Select All" &&
                                                      selectedStreet?.length ===
                                                        dropDownData
                                                          ?.ETStreetSearchData
                                                          ?.length)
                                                  }
                                                />
                                              }
                                              // label={`${option?.code}`}
                                              label={
                                                <>
                                                  <strong>{option.code}</strong>
                                                </>
                                              }
                                            />
                                          </FormGroup>
                                        </li>
                                      )}
                                      renderInput={(params) => (
                                        <Tooltip
                                          title={
                                            streetInputValue.length < 4
                                              ? "Enter at least 4 characters"
                                              : ""
                                          }
                                          arrow
                                          disableHoverListener={
                                            streetInputValue.length >= 4
                                          }
                                          placement="top" // Ensures the tooltip appears above the text field
                                        >
                                          <TextField
                                            sx={{
                                              fontSize: "12px !important",
                                              "& .MuiOutlinedInput-root": {
                                                height: 35,
                                              },
                                              "& .MuiInputBase-input": {
                                                padding: "10px 14px",
                                              },
                                            }}
                                            {...params}
                                            variant="outlined"
                                            placeholder={
                                              memoizedStreetValue.length === 0
                                                ? "Select Street"
                                                : ""
                                            }
                                            onChange={(e) => {
                                              handleStreetInputChange(e);
                                            }}
                                          />
                                        </Tooltip>
                                      )}
                                    />
                                  </FormControl>
                                </Grid>
                              );
                            } else if (option === "Location") {
                              return (
                                <>
                                  <Grid item md={2}>
                                    <LabelTypography sx={font_Small}>
                                      Location
                                    </LabelTypography>
                                    <FormControl fullWidth size="small">
                                      <Autocomplete
                                        sx={{ height: "31px" }}
                                        fullWidth
                                        size="small"
                                        multiple
                                        disableCloseOnSelect
                                        value={memoizedLocationValue}
                                        noOptionsText={
                                          isDropDownLoading ? (
                                            <Box
                                              sx={{
                                                display: "flex",
                                                justifyContent: "center",
                                                mt: 1,
                                                zIndex: 9999,
                                                top: "10px",
                                              }}
                                            >
                                              <CircularProgress size={20} />
                                            </Box>
                                          ) : (
                                            "No Data Available"
                                          )
                                        }
                                        renderTags={(value, getTagProps) =>
                                          value.length > 0 ? (
                                            <>
                                              <Chip
                                                label={value[0].code}
                                                {...getTagProps({ index: 0 })}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": {
                                                    padding: "0 6px",
                                                  },
                                                }}
                                              />
                                              {value.length > 1 && (
                                                <Chip
                                                  label={`+${value.length - 1}`}
                                                  sx={{
                                                    height: 20,
                                                    fontSize: "0.75rem",
                                                    ".MuiChip-label": {
                                                      padding: "0 6px",
                                                    },
                                                  }}
                                                />
                                              )}
                                            </>
                                          ) : null
                                        }
                                        limitTags={1}
                                        onChange={(e, value, reason) => {
                                          if (
                                            reason === "clear" ||
                                            value?.length === 0
                                          ) {
                                            setselectedLocation([]);
                                            setselectedPresetLocation([]);
                                            return;
                                          }

                                          if (
                                            value.length > 0 &&
                                            value[value.length - 1]?.code ===
                                              "Select All"
                                          ) {
                                            handleSelectAllLocation();
                                          } else {
                                            setselectedLocation(value);
                                          }
                                        }}
                                        options={
                                          dropDownData?.ETLocationSearchData
                                            ?.length
                                            ? [
                                                { code: "Select All" },
                                                ...dropDownData?.ETLocationSearchData,
                                              ]
                                            : dropDownData?.ETLocationSearchData ??
                                              []
                                        }
                                        getOptionLabel={(option) => {
                                          if (option?.code)
                                            return option?.code ?? "";
                                          else return "";
                                        }}
                                        renderOption={(
                                          props,
                                          option,
                                          { selected }
                                        ) => (
                                          <li {...props}>
                                            <FormGroup>
                                              <FormControlLabel
                                                control={
                                                  <Checkbox
                                                    checked={
                                                      isLocationSelected(
                                                        option
                                                      ) ||
                                                      (option?.code ===
                                                        "Select All" &&
                                                        selectedLocation?.length ===
                                                          dropDownData
                                                            ?.ETLocationSearchData
                                                            ?.length)
                                                    }
                                                  />
                                                }
                                                // label={`${option?.code}`}
                                                label={
                                                  <>
                                                    <strong>
                                                      {option.code}
                                                    </strong>
                                                  </>
                                                }
                                              />
                                            </FormGroup>
                                          </li>
                                        )}
                                        renderInput={(params) => (
                                          <Tooltip
                                            title={
                                              locationInputValue.length < 4
                                                ? "Enter at least 4 characters"
                                                : ""
                                            }
                                            arrow
                                            disableHoverListener={
                                              locationInputValue.length >= 4
                                            }
                                            placement="top" // Ensures the tooltip appears above the text field
                                          >
                                            <TextField
                                              sx={{
                                                fontSize: "12px !important",
                                                "& .MuiOutlinedInput-root": {
                                                  height: 35,
                                                },
                                                "& .MuiInputBase-input": {
                                                  padding: "10px 14px",
                                                },
                                              }}
                                              {...params}
                                              variant="outlined"
                                              // helperText={locationInputValue.length < 4 ? 'Enter at least 4 characters' : ''}
                                              placeholder={
                                                memoizedLocationValue.length ===
                                                0
                                                  ? "Select Location"
                                                  : ""
                                              }
                                              onChange={(e) => {
                                                handleLocationInputChange(e);
                                              }}
                                            />
                                          </Tooltip>
                                        )}
                                      />
                                    </FormControl>
                                  </Grid>
                                </>
                              );
                            } else if (option === "Created By") {
                              return (
                                <>
                                  <Grid item md={2}>
                                    <LabelTypography sx={font_Small}>
                                      Created By
                                    </LabelTypography>
                                    <FormControl
                                      fullWidth
                                      size="small"
                                      sx={{ paddingBottom: "0.7rem" }}
                                    >
                                      <Autocomplete
                                        sx={{ height: "31px" }}
                                        fullWidth
                                        multiple
                                        disableCloseOnSelect
                                        size="small"
                                        value={memoizedCreatedByValue}
                                        noOptionsText={
                                          isDropDownLoading ? (
                                            <Box
                                              sx={{
                                                display: "flex",
                                                justifyContent: "center",
                                                mt: 1,
                                                zIndex: 9999,
                                                top: "10px",
                                              }}
                                            >
                                              <CircularProgress size={20} />
                                            </Box>
                                          ) : (
                                            "No Data Available"
                                          )
                                        }
                                        renderTags={(value, getTagProps) =>
                                          value.length > 0 ? (
                                            <>
                                              <Chip
                                                label={value[0].code}
                                                {...getTagProps({ index: 0 })}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": {
                                                    padding: "0 6px",
                                                  },
                                                }}
                                              />
                                              {value.length > 1 && (
                                                <Chip
                                                  label={`+${value.length - 1}`}
                                                  sx={{
                                                    height: 20,
                                                    fontSize: "0.75rem",
                                                    ".MuiChip-label": {
                                                      padding: "0 6px",
                                                    },
                                                  }}
                                                />
                                              )}
                                            </>
                                          ) : null
                                        }
                                        onChange={(e, value, reason) => {
                                          if (
                                            reason === "clear" ||
                                            value?.length === 0
                                          ) {
                                            setselectedCreatedBy([]);
                                            setselectedPresetCreatedBy([]);
                                            return;
                                          }

                                          if (
                                            value.length > 0 &&
                                            value[value.length - 1]?.code ===
                                              "Select All"
                                          ) {
                                            handleSelectAllCreatedBy();
                                          } else {
                                            setselectedCreatedBy(value);
                                          }
                                        }}
                                        limitTags={1}
                                        options={
                                          dropDownData?.ETCreatedBySearchData
                                            ?.length
                                            ? [
                                                { code: "Select All" },
                                                ...dropDownData?.ETCreatedBySearchData,
                                              ]
                                            : dropDownData?.ETCreatedBySearchData ??
                                              []
                                        }
                                        getOptionLabel={(option) => {
                                          if (option?.code)
                                            return option?.code ?? "";
                                          else return "";
                                        }}
                                        renderOption={(
                                          props,
                                          option,
                                          { selected }
                                        ) => (
                                          <li {...props}>
                                            <FormGroup>
                                              <FormControlLabel
                                                control={
                                                  <Checkbox
                                                    checked={
                                                      isCreatedBySelected(
                                                        option
                                                      ) ||
                                                      (option?.code ===
                                                        "Select All" &&
                                                        selectedCreatedBy?.length ===
                                                          dropDownData
                                                            ?.ETCreatedBySearchData
                                                            ?.length)
                                                    }
                                                  />
                                                }
                                                // label={`${option?.code}`}
                                                label={
                                                  <>
                                                    <strong>
                                                      {option.code}
                                                    </strong>
                                                  </>
                                                }
                                              />
                                            </FormGroup>
                                          </li>
                                        )}
                                        renderInput={(params) => (
                                          <Tooltip
                                            title={
                                              createdByInputValue.length < 4
                                                ? "Enter at least 4 characters"
                                                : ""
                                            }
                                            arrow
                                            disableHoverListener={
                                              createdByInputValue.length >= 4
                                            }
                                            placement="top" // Ensures the tooltip appears above the text field
                                          >
                                            <TextField
                                              sx={{
                                                fontSize: "12px !important",
                                                "& .MuiOutlinedInput-root": {
                                                  height: 35,
                                                },
                                                "& .MuiInputBase-input": {
                                                  padding: "10px 14px",
                                                },
                                              }}
                                              {...params}
                                              variant="outlined"
                                              // helperText={createdByInputValue.length < 4 ? 'Enter at least 4 characters' : ''}
                                              placeholder={
                                                memoizedCreatedByValue.length ===
                                                0
                                                  ? "Select Created By"
                                                  : ""
                                              }
                                              onChange={(e) => {
                                                handleCreatedByInputChange(e);
                                              }}
                                            />
                                          </Tooltip>
                                        )}
                                      />
                                    </FormControl>
                                  </Grid>
                                </>
                              );
                            } else if (option === "Profit Center") {
                              return (
                                <>
                                  <Grid item md={2}>
                                    <LabelTypography sx={font_Small}>
                                      Profit Center
                                    </LabelTypography>
                                    <FormControl fullWidth size="small">
                                      <Autocomplete
                                        sx={{ height: "31px" }}
                                        fullWidth
                                        multiple
                                        disableCloseOnSelect
                                        size="small"
                                        value={memoizedPCValue}
                                        noOptionsText={
                                          isDropDownLoading ? (
                                            <Box
                                              sx={{
                                                display: "flex",
                                                justifyContent: "center",
                                                mt: 1,
                                                zIndex: 9999,
                                                top: "10px",
                                              }}
                                            >
                                              <CircularProgress size={20} />
                                            </Box>
                                          ) : (
                                            "No Data Available"
                                          )
                                        }
                                        renderTags={(value, getTagProps) =>
                                          value.length > 0 ? (
                                            <>
                                              <Chip
                                                label={value[0].code}
                                                {...getTagProps({ index: 0 })}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": {
                                                    padding: "0 6px",
                                                  },
                                                }}
                                              />
                                              {value.length > 1 && (
                                                <Chip
                                                  label={`+${value.length - 1}`}
                                                  sx={{
                                                    height: 20,
                                                    fontSize: "0.75rem",
                                                    ".MuiChip-label": {
                                                      padding: "0 6px",
                                                    },
                                                  }}
                                                />
                                              )}
                                            </>
                                          ) : null
                                        }
                                        onChange={(e, value, reason) => {
                                          if (
                                            reason === "clear" ||
                                            value?.length === 0
                                          ) {
                                            setselectedProfitCenter([]);
                                            setselectedPresetProfitCenter([]);
                                            return;
                                          }

                                          if (
                                            value.length > 0 &&
                                            value[value.length - 1]?.code ===
                                              "Select All"
                                          ) {
                                            handleSelectAllProfitCenter();
                                          } else {
                                            setselectedProfitCenter(value);
                                          }
                                        }}
                                        limitTags={1}
                                        options={
                                          dropDownData?.ETPCSearchData?.length
                                            ? [
                                                {
                                                  code: "Select All",
                                                  desc: "Select All",
                                                },
                                                ...dropDownData?.ETPCSearchData,
                                              ]
                                            : dropDownData?.ETPCSearchData ?? []
                                        }
                                        getOptionLabel={(option) => {
                                          if (option?.code)
                                            return `${option?.code}` ?? "";
                                          else return "";
                                        }}
                                        renderOption={(
                                          props,
                                          option,
                                          { selected }
                                        ) => (
                                          <li {...props}>
                                            <FormGroup>
                                              <FormControlLabel
                                                control={
                                                  <Checkbox
                                                    checked={
                                                      isProfitCenterSelected(
                                                        option
                                                      ) ||
                                                      (option?.code ===
                                                        "Select All" &&
                                                        selectedProfitCenter?.length ===
                                                          dropDownData
                                                            ?.ETPCSearchData
                                                            ?.length)
                                                    }
                                                  />
                                                }
                                                // label={`${option?.code} - ${option?.desc}`}
                                                label={
                                                  <>
                                                    <strong>
                                                      {option.code}
                                                    </strong>{" "}
                                                    - {option.desc}
                                                  </>
                                                }
                                              />
                                            </FormGroup>
                                          </li>
                                        )}
                                        renderInput={(params) => (
                                          <Tooltip
                                            title={
                                              pcInputValue.length < 4
                                                ? "Enter at least 4 characters"
                                                : ""
                                            }
                                            arrow
                                            disableHoverListener={
                                              pcInputValue.length >= 4
                                            }
                                            placement="top" // Ensures the tooltip appears above the text field
                                          >
                                            <TextField
                                              sx={{
                                                fontSize: "12px !important",
                                                "& .MuiOutlinedInput-root": {
                                                  height: 35,
                                                },
                                                "& .MuiInputBase-input": {
                                                  padding: "10px 14px",
                                                },
                                              }}
                                              {...params}
                                              variant="outlined"
                                              // helperText={pcInputValue.length < 4 ? 'Enter at least 4 characters' : ''}
                                              placeholder={
                                                memoizedPCValue.length === 0
                                                  ? "Select Profit Center"
                                                  : ""
                                              }
                                              onChange={(e) => {
                                                handlePCInputChange(e);
                                              }}
                                            />
                                          </Tooltip>
                                        )}
                                      />
                                    </FormControl>
                                  </Grid>
                                </>
                              );
                            } else {
                              return (
                                <Grid item md={2}>
                                  <LabelTypography sx={font_Small}>
                                    {option}
                                  </LabelTypography>
                                  <FormControl fullWidth size="small">
                                    <Autocomplete
                                      sx={{ height: "31px" }}
                                      fullWidth
                                      multiple
                                      disableCloseOnSelect
                                      size="small"
                                      noOptionsText={
                                        isDropDownLoading ? (
                                          <Box
                                            sx={{
                                              display: "flex",
                                              justifyContent: "center",
                                              mt: 1,
                                              zIndex: 9999,
                                              top: "10px",
                                            }}
                                          >
                                            <CircularProgress size={20} />
                                          </Box>
                                        ) : (
                                          "No Data Available"
                                        )
                                      }
                                      value={
                                        selectedValues[option]?.length > 0
                                          ? selectedValues[option]
                                          : selectedPresetValues[option]
                                              ?.length > 0
                                          ? selectedPresetValues[option]
                                          : []
                                      }
                                      renderTags={(value, getTagProps) =>
                                        value.length > 0 ? (
                                          <>
                                            <Chip
                                              label={value[0].code}
                                              {...getTagProps({ index: 0 })}
                                              sx={{
                                                height: 20,
                                                fontSize: "0.75rem",
                                                ".MuiChip-label": {
                                                  padding: "0 6px",
                                                },
                                              }}
                                            />
                                            {value.length > 1 && (
                                              <Chip
                                                label={`+${value.length - 1}`}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": {
                                                    padding: "0 6px",
                                                  },
                                                }}
                                              />
                                            )}
                                          </>
                                        ) : null
                                      }
                                      onChange={(e, value, reason) => {
                                        if (
                                          reason === "clear" ||
                                          value?.length === 0
                                        ) {
                                          setSelectedValues((prev) => ({
                                            ...prev,
                                            [option]: [],
                                          }));
                                          setSelectedPresetValues((prev) => ({
                                            ...prev,
                                            [option]: [],
                                          }));
                                          return;
                                        }

                                        if (
                                          value.length > 0 &&
                                          value[value.length - 1]?.code ===
                                            "Select All"
                                        ) {
                                          handleSelectAllOptions(option);
                                        } else {
                                          setSelectedValues((prev) => ({
                                            ...prev,
                                            [option]: value,
                                          }));
                                        }
                                      }}
                                      limitTags={1}
                                      options={
                                        dynamicOptions?.[option]?.length
                                          ? [
                                              { code: "Select All" },
                                              ...dynamicOptions?.[option],
                                            ]
                                          : dynamicOptions?.[option] ?? []
                                      }
                                      getOptionLabel={(option) =>
                                        option?.code ? `${option.code}` : ""
                                      }
                                      renderOption={(
                                        props,
                                        dropdownOption,
                                        { selected }
                                      ) => (
                                        <li {...props}>
                                          <FormGroup>
                                            <FormControlLabel
                                              control={
                                                <Checkbox
                                                  checked={
                                                    isOptionSelected(
                                                      option,
                                                      dropdownOption
                                                    ) ||
                                                    (dropdownOption?.code ===
                                                      "Select All" &&
                                                      selectedValues[option]
                                                        ?.length ===
                                                        dynamicOptions?.[option]
                                                          ?.length)
                                                  }
                                                />
                                              }
                                              // label={`${dropdownOption?.code}`}
                                              label={
                                                <>
                                                  <strong>
                                                    {dropdownOption?.code}
                                                  </strong>
                                                </>
                                              }
                                            />
                                          </FormGroup>
                                        </li>
                                      )}
                                      renderInput={(params) => (
                                        <TextField
                                          sx={{
                                            fontSize: "12px !important",
                                            "& .MuiOutlinedInput-root": {
                                              height: 35,
                                            },
                                            "& .MuiInputBase-input": {
                                              padding: "10px 14px",
                                            },
                                          }}
                                          {...params}
                                          variant="outlined"
                                          placeholder={`Select ${option}`}
                                        />
                                      )}
                                    />
                                  </FormControl>
                                </Grid>
                              );
                            }
                          })}
                        </Grid>
                      </Grid>
                    </Grid>
                    <Grid
                      container
                      style={{
                        display: "flex",
                        justifyContent: "flex-end",
                      }}
                    >
                      <Grid
                        item
                        style={{
                          display: "flex",
                          justifyContent: "space-around",
                        }}
                      >
                        <Grid>
                          <Button
                            variant="outlined"
                            sx={button_Outlined}
                            onClick={handleClear}
                          >
                            Clear
                          </Button>
                        </Grid>
                        <Grid sx={{ ...button_Marginleft }}>
                          <ReusablePreset
                            moduleName={"CostCenter"}
                            PresetObj={PresetObj}
                            handleSearch={() => getFilter()}
                            PresetMethod={PresetMethod}
                          />
                        </Grid>
                        <Grid>
                          <Button
                            variant="contained"
                            sx={{ ...button_Primary, ...button_Marginleft }}
                            onClick={() => getFilter()}
                          >
                            Search
                          </Button>
                        </Grid>
                      </Grid>
                    </Grid>
                  </AccordionDetails>
                </StyledAccordion>
              </Grid>
            </Grid>

            <Grid item sx={{ position: "relative" }}>
              <Stack>
                <ReusableTable
                  isLoading={tableLoading}
                  module={"CostCenter"}
                  width="100%"
                  title={"List of Cost Centers"}
                  rows={rmDataRows}
                  columns={allColumns}
                  showSearch={true}
                  page={page}
                  pageSize={pageSize}
                  showExport={true}
                  showRefresh={true}
                  rowCount={count ?? rmDataRows?.length ?? 0}
                  onPageChange={handlePageChange}
                  onPageSizeChange={handlePageSizeChange}
                  getRowIdValue={"id"}
                  hideFooter={true}
                  checkboxSelection={true}
                  disableSelectionOnClick={true}
                  status_onRowDoubleClick={true}
                  onRowsSelectionHandler={handleSelectionModelChange}
                  callback_onRowDoubleClick={(params) => {
                    const costCenterNumber = params.row.costCenter; // Adjust this based on your data structure
                    console.log("materialNumber", costCenterNumber);
                    navigate(
                      `/masterDataCockpit/costCenter/displayCostCenter/${costCenterNumber}`,
                      {
                        state: params.row,
                      }
                    );
                  }}
                  // setShowWork={setShowWork}
                  stopPropagation_Column={"action"}
                  // status_onRowDoubleClick={true}
                  showCustomNavigation={true}
                />
                {/* {viewDetailpage && <SingleMaterialDetail />} */}
              </Stack>
            </Grid>
            {/* {
            showBtmNav && */}
            {/* {(checkIwaAccess(iwaAccessData, "Cost Center", "CreateCC") &&
                userData?.role === "Super User") ||
              userData?.role === "Finance" ? ( */}
            <Paper
              sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
              elevation={2}
            >
              <BottomNavigation
                className="container_BottomNav"
                showLabels
                sx={{
                  display: "flex",
                  justifyContent: "flex-end",
                  gap: 1,
                }}
                value={value}
                onChange={(newValue) => {
                  setValue(newValue);
                }}
              >
                <Button
                  onClick={() => {
                    navigate("/requestBench/CostCenterRequestTab");
                    // dispatch(setDisplayPayload({}));
                    // dispatch(setRequestHeader({}));
                  }}
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary }}
                >
                  Create Request
                </Button>

                {enableDocumentUpload && (
                  <AttachmentUploadDialog
                    artifactId=""
                    artifactName=""
                    setOpen={setEnableDocumentUpload}
                    handleUpload={uploadExcel}
                  />
                )}
              </BottomNavigation>
            </Paper>
            {/* ) : (
                ""
              )} */}
          </Stack>
        </div>
      </div>
      {/* )} */}
    </>
  );
};

export default CostCenter;
