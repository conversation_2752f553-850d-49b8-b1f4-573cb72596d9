import React, { useEffect, useState } from "react";
import ReusableSnackBar from "../../Common/ReusableSnackBar";
import {
  Backdrop,
  BottomNavigation,
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  Paper,
  Stack,
  Step,
  StepLabel,
  Stepper,
  Tab,
  Tabs,
  TextField,
  Typography,
} from "@mui/material";
import {
  button_Outlined,
  iconButton_SpacingSmall,
  container_Padding,
  button_Primary,
  outermostContainer_Information,
} from "../../common/commonStyles";
import { CheckCircleOutlineOutlined } from "@mui/icons-material";
import MarkunreadOutlinedIcon from "@mui/icons-material/MarkunreadOutlined";
import CloseIcon from "@mui/icons-material/Close";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import { checkIwaAccess, idGenerator, formValidator } from "../../../functions";
import { useDispatch, useSelector } from "react-redux";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import { useLocation, useNavigate } from "react-router-dom";
import {
  destination_DocumentManagement,
  destination_ProfitCenter,
} from "../../../destinationVariables";
import { doAjax } from "../../Common/fetchService";
import EditableFieldForProfitCenter from "./EditableFieldForProfitCenter";
import CompCodesProfitCenter from "../ProfitCenterTabs/CompCodeProfitCenter";
import moment from "moment/moment";
import { setDropDown } from "../../../app/dropDownDataSlice";
import ReusableDialog from "../../Common/ReusableDialog";
import { MatDownload, MatView } from "../../DocumentManagement/UtilDoc";
import {
  Timeline,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineItem,
  TimelineSeparator,
  timelineItemClasses,
} from "@mui/lab";
import ReusableTable from "../../Common/ReusableTable";
import lookup from "../../../data/lookup.json";
import LoadingComponent from "../../Common/LoadingComponent";
import ReusableAttachementAndComments from "../../Common/ReusableAttachmentAndComments/ReusableAttachementAndComments";
import { setPayloadWhole } from "../../../app/editPayloadSlice";
import {
  clearProfitCenter,
  setProfitCenterViewData,
} from "../../../app/profitCenterTabsSlice";
const DisplayCopyProfitCenter = () => {
  const [isEditMode, setIsEditMode] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [isDisplayMode, setIsDisplayMode] = useState(true);
  const [responseFromAPI, setResponseFromAPI] = useState({});
  const [factorsArray, setFactorsArray] = useState([]);
  const [costCenterDetails, setCostCenterDetails] = useState([]);
  const [iDs, setIds] = useState();
  const [value, setValue] = useState([]);
  const [activeStep, setActiveStep] = useState(0);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [messageDialogSeverity, setMessageDialogSeverity] = useState(false);
  const [messageDialogOK, setMessageDialogOK] = useState(true);
  const [successMsg, setsuccessMsg] = useState(false);
  const [messageDialogExtra, setMessageDialogExtra] = useState(false);
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [blurLoading, setBlurLoading] = useState(false);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState(false);
  const [validateFlag, setValidateFlag] = useState(false);
  const [openCorrectionDialog, setOpenCorrectionDialog] = useState(false);
  const [openCreateDialog, setOpenCreateDialog] = useState(false);
  const [pcNumber, setPcNumber] = useState("");
  const [handleExtrabutton, setHandleExtrabutton] = useState(false);
  const [handleExtraText, setHandleExtraText] = useState("");
  const [remarks, setRemarks] = useState("");
  const [dispCompCode, setDispCompCode] = useState([]);
  const [comments, setComments] = useState([]);
  const [attachments, setAttachments] = useState([]);
  const [submitForReviewDisabled, setSubmitForReviewDisabled] = useState(true);
  const [testrunStatus, setTestrunStatus] = useState(true);
  const [errorsFields, setErrorsFields] = useState([]);
  const [formValidationErrorItems, setFormValidationErrorItems] = useState([]); //chiranjit
  const [openSnackbarValidation, setOpenSnackbarValidation] = useState(false);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const appSettings = useSelector((state) => state.appSettings);

  let iwaAccessData = useSelector(
    (state) => state.userManagement.entitiesAndActivities?.["Profit Center"]
  );
  let userData = useSelector((state) => state.userManagement.userData);
  let taskData = useSelector((state) => state?.initialData?.IWMMyTask);
  let profitCenterRowData = location.state;
  console.log("profitCenterRowData", profitCenterRowData);
  console.log("testrunStatus", testrunStatus);
  const singleCCPayload = useSelector(
    (state) => state.costCenter.singleCCPayload
  );
  const profitCenterViewData = useSelector(
    (state) => state.profitCenter.profitCenterViewData
  );
  const compCodesTabDetails = useSelector(
    (state) => state.profitCenter.profitCenterCompCodes
  );
  const editedData = useSelector((state) => state.edit.payload);
  let taskRowDetails = useSelector((state) => state.userManagement.taskData);

  let singlePCPayloadAfterChange = useSelector((state) => state.edit.payload);
  console.log(singlePCPayloadAfterChange, "singlePCPayloadAfterChange");
  let requiredFieldTabWise = useSelector(
    (state) => state.profitCenter.requiredFields
  );
  console.log(requiredFieldTabWise, "required_field_for_data");
  var payload = {
    TaskId: taskRowDetails?.taskId ? taskRowDetails?.taskId : "",
    ProfitCenterID: iDs?.profitCenterId ? iDs?.profitCenterId : "",
    RequestID: "",
    Action: "I",
    TaskStatus: "",
    ReqCreatedBy: userData?.user_id,
    ReqCreatedOn: "",
    RequestStatus: "",
    CreationId: profitCenterRowData?.requestId
      ? profitCenterRowData?.requestId
      : "",
    EditId: "",
    DeleteId: "",
    MassCreationId: "",
    MassEditId: "",
    MassDeleteId: "",
    RequestType: "Create",
    MassRequestStatus: "",
    Remarks: remarks ? remarks : "",
    PrctrName: editedData?.Name ? editedData?.Name : "",
    LongText: editedData?.LongText ? editedData?.LongText : "",
    InChargeUser: editedData?.UserResponsible
      ? editedData?.UserResponsible
      : "",
    InCharge: editedData?.PersonResponsible
      ? editedData?.PersonResponsible
      : "",
    Department: editedData?.Department ? editedData?.Department : "",
    PrctrHierGrp: editedData?.ProfitCtrGroup ? editedData?.ProfitCtrGroup : "",
    Segment: editedData?.Segment ? editedData?.Segment : "",
    LockInd: editedData?.Lockindicator === "true" ? "X" : "",
    Template: editedData?.FormPlanningTemp ? editedData?.FormPlanningTemp : "",
    Title: editedData?.Title ? editedData?.Title : "",
    Name1: editedData?.Name1 ? editedData?.Name1 : "",
    Name2: editedData?.Name2 ? editedData?.Name2 : "",
    Name3: editedData?.Name3 ? editedData?.Name3 : "",
    Name4: editedData?.Name4 ? editedData?.Name4 : "",
    Street: editedData?.Street ? editedData?.Street : "",
    City: editedData?.City ? editedData?.City : "",
    District: editedData?.District ? editedData?.District : "",
    Country: editedData?.CountryReg ? editedData?.CountryReg : "",
    Taxjurcode: editedData?.TaxJur ? editedData?.TaxJur : "",
    PoBox: editedData?.POBox ? editedData?.POBox : "",
    PostlCode: editedData?.PostalCode ? editedData?.PostalCode : "",
    PobxPcd: editedData?.POBoxPCode ? editedData?.POBoxPCode : "",
    Region: editedData?.Region ? editedData?.Region : "",
    Langu: editedData?.Language ? editedData?.Language : "EN",
    Telephone: editedData?.Telephone1 ? editedData?.Telephone1 : "",
    Telephone2: editedData?.Telephone2 ? editedData?.Telephone2 : "",
    Telebox: editedData?.Telebox ? editedData?.Telebox : "",
    Telex: editedData?.Telex ? editedData?.Telex : "",
    FaxNumber: editedData?.FaxNumber ? editedData?.FaxNumber : "",
    Teletex: editedData?.Teletex ? editedData?.Teletex : "",
    Printer: editedData?.Printername ? editedData?.Printername : "",
    DataLine: editedData?.Dataline ? editedData?.Dataline : "",
    ProfitCenter:
      `P${profitCenterRowData?.companyCodeCopy?.newCompanyCodeCopy?.code}${profitCenterRowData?.profitCenterName?.newProfitCenterName}`
        ? `P${profitCenterRowData?.companyCodeCopy?.newCompanyCodeCopy?.code}${profitCenterRowData?.profitCenterName?.newProfitCenterName}`
        : "",
    ControllingArea: profitCenterRowData?.controllingArea?.newControllingArea
      ?.code
      ? profitCenterRowData?.controllingArea?.newControllingArea?.code
      : "",
    ValidfromDate: editedData?.AnalysisPeriodFrom
      ? "/Date(" + editedData?.AnalysisPeriodFrom + ")/"
      : "",
    ValidtoDate: editedData?.AnalysisPeriodTo
      ? "/Date(" + editedData?.AnalysisPeriodTo + ")/"
      : "",
    Testrun: testrunStatus,
    Countryiso: "",
    LanguIso: "",
    Logsystem: "",
    ToCompanycode: dispCompCode?.map((compCode) => {
      return {
        CompCodeID: "",
        CompanyName: compCode?.companyName ? compCode?.companyName : "",
        AssignToPrctr: "X",
        CompCode: compCode?.companyCodes ? compCode?.companyCodes : "",
      };
    }),
  };

  // Loader and lookup for independent apis start
  const [apiCount, setApiCount] = useState(0);
  const fetchDynamicApiData = (keyName, endPoint) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: keyName, data: data.body }));
      // setIsLoading(false);
      setApiCount((prev) => prev + 1);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/data/${endPoint}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getAllLookups = () => {
    lookup?.profitCenter?.map((item) => {
      fetchDynamicApiData(item?.keyName, item?.endPoint);
    });
  };
  const loaderCount = () => {
    if (apiCount == lookup?.profitCenter?.length) {
      setIsLoading(false);
    } else {
      setIsLoading(true);
    }
  };
  useEffect(() => {
    loaderCount();
  }, [apiCount]);

  // Loader and lookup for independent apis end
  useEffect(() => {
    setPcNumber(idGenerator("PC"));
  }, []);
  useEffect(() => {
    getAllLookups();
    getProfitCenterDisplayData();

    // getHierarchyArea();
    // getCompanyCode();
    // getProfitCenter();
    // getCurrency();
  }, []);

  useEffect(() => {
    getProfitCenterGroupBasedOnControllingArea();
    if (profitCenterViewData.length === 0) {
      return;
    }
    handleSetEditedPayload();
  }, [profitCenterViewData]);
  const attachmentColumns = [
    {
      field: "id",
      headerName: "Document ID",
      flex: 1,
      hide: true,
    },
    {
      field: "docType",
      headerName: "Type",
      flex: 1,
    },
    {
      field: "docName",
      headerName: "Document Name",
      flex: 1,
    },
    {
      field: "uploadedOn",
      headerName: "Uploaded On",
      flex: 1,
      align: "center",
      headerAlign: "center",
    },
    {
      field: "uploadedBy",
      headerName: "Uploaded By",
      sortable: false,
      flex: 1,
    },
    {
      field: "action",
      headerName: "Action",
      sortable: false,
      filterable: false,
      align: "center",
      headerAlign: "center",
      flex: 1,
      renderCell: (cellValues) => {
        return (
          <>
            <MatView index={cellValues.row.id} name={cellValues.row.docName} />
            <MatDownload
              index={cellValues.row.id}
              name={cellValues.row.docName}
            />
          </>
        );
      },
    },
  ];

  const handleSetEditedPayload = () => {
    let activeTabName = factorsArray[activeStep];
    console.log("activeTabName", activeTabName, factorsArray);
    let viewDataArray = Object.entries(profitCenterViewData);
    console.log("viewDataArray", viewDataArray);
    const toSetArray = {};
    viewDataArray.map((item) => {
      console.log("bottle", item[1]);
      let temp = Object.entries(item[1]);
      console.log("notebook", temp);
      temp.forEach((fieldGroup) => {
        fieldGroup[1].forEach((field) => {
          toSetArray[
            field.fieldName
              .replaceAll("(", "")
              .replaceAll(")", "")
              .replaceAll("/", "")
              .replaceAll("-", "")
              .replaceAll(".", "")
              .split(" ")
              .join("")
          ] = field.value;
        });
      });
      return item;
    });
    console.log("toSetArray", toSetArray);
    dispatch(setPayloadWhole(toSetArray));
  };

  const getRegion = (value) => {
    console.log("compcode", value);
    const hSuccess = (data) => {
      console.log("value", data);
      dispatch(setDropDown({ keyName: "Region", data: data.body }));
    };
    const hError = (error) => {
      console.log(error, "error in dojax");
    };
    doAjax(
      `/${destination_ProfitCenter}/data/getRegionBasedOnCountry?country=${value}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getProfitCenterDisplayData = () => {
    setIsLoading(true);
    var displayPayload = {
      id: "",
      profitCenter: profitCenterRowData?.profitCenter?.newProfitCenter?.code
        ? profitCenterRowData?.profitCenter?.newProfitCenter?.code
        : "",
      // profitCenter: "TUK1_7899",
      controllingArea: profitCenterRowData?.controllingArea?.newControllingArea
        ?.code
        ? profitCenterRowData?.controllingArea?.newControllingArea?.code
        : "",
      reqStatus: "Approved",
      screenName: "Create",
      // costCenter: profitCenterRowData.costCenters?profitCenterRowData?.costCenters:"",
    };
    console.log("displayPayload", displayPayload);
    const hSuccess = (data) => {
      setIsLoading(false);
      const responseBody = data.body.viewData;
      const responseIDs = data.body;
      dispatch(setProfitCenterViewData(responseBody));
      // console.log(responseBody,"dcdcdc");
      const categoryKeys = Object.keys(responseBody);
      setFactorsArray(categoryKeys);
      const mappedData = categoryKeys.map((category) => ({
        category,
        data: responseBody[category],
        setIsEditMode,
      }));
      setCostCenterDetails(mappedData);
      checkErrorFields(responseBody);
      setIds(responseIDs);
      let compcodeData =
        data?.body?.viewData["Comp Codes"][
          "Company Code Assignment for Profit Center"
        ];

      setDispCompCode(
        _.zip(
          compcodeData[0].value,
          compcodeData[1].value,
          compcodeData[2].value
        ).map((item, index) => {
          return {
            id: index,
            companyCodes: item[0].split("$")[0],
            companyName: item[1],
            assigned: item[2],
          };
        })
      );
      getRegion(
        responseBody["Address"]["Address Data"].find(
          (field) => field?.fieldName === "Country/Reg."
        ).value
      );
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter}/data/displayProfitCenter`,
      "post",
      hSuccess,
      hError,
      displayPayload
    );
  };

  const requiredFields = useSelector(
    //chiranjit
    (state) => state.profitCenter.requiredFields
  );
  const handleSnackBarCloseValidation = () => {
    setOpenSnackbarValidation(false);
  };

  const handleCheckValidationError = () => {
    //chiranjit
    return formValidator(
      singlePCPayloadAfterChange,
      requiredFieldTabWise,
      setFormValidationErrorItems
    );
  };
  console.log(requiredFields, "requiredFields");

  console.log(errorsFields, "error_field_arr");
  const checkErrorFields = (viewData) => {
    //chiranjit
    //console.log(viewData,"viewData===============")
    let error_field_arr = [];
    for (const section in viewData) {
      if (viewData.hasOwnProperty(section)) {
        for (const fieldGroup in viewData[section]) {
          if (viewData[section].hasOwnProperty(fieldGroup)) {
            const fields = viewData[section][fieldGroup];
            //console.log(fields,"fieldsview_data")
            for (const field of fields) {
              if (field.visibility === "0" || field.visibility === "Required") {
                console.log(field.fieldName, "field.fieldName");
                let required_field_name = field.fieldName.replace(/\s/g, "");
                error_field_arr.push(required_field_name);
              }
            }
          }
        }
      }
      setErrorsFields((prevState) => ({
        ...prevState,
        error_field_arr,
      }));
    }
    //return result;
  };

  console.log("dispcomp", dispCompCode);
  const handleSnackBarOpen = () => {
    setopenSnackbar(true);
  };
  const getProfitCenterGroupBasedOnControllingArea = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ProfitCtrGroup", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/data/getProfitCtrGroup?controllingArea=${
        taskData?.body?.controllingArea ||
        profitCenterRowData?.controllingArea?.newControllingArea?.code
      }`,
      "get",
      hSuccess,
      hError
    );
  };
  //   useEffect(() => {
  //     getProfitCenterDisplayData();
  //     getProfitCenterGroupBasedOnControllingArea();
  //   }, []);

  const onEdit = () => {
    setIsEditMode(true);
    setIsDisplayMode(false);
  };
  const handleSnackBarOpenValidation = () => {
    setOpenSnackbarValidation(true);
  };
  const handleBack = () => {
    // setActiveStep((prevActiveStep) => prevActiveStep - 1);
    setSubmitForReviewDisabled(true);
    const isValidation = handleCheckValidationError();
    
    if(isEditMode){
      if (isValidation) {
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
        dispatch(clearProfitCenter());
      } else {
        handleSnackBarOpenValidation();
      }
    }else{
      setActiveStep((prevActiveStep) => prevActiveStep - 1);
        dispatch(clearProfitCenter());
    }
  };
  const handleNext = () => {
    //setActiveStep((prevActiveStep) => prevActiveStep + 1);
    const isValidation = handleCheckValidationError();  
    if(isEditMode){
      if (isValidation) {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
      dispatch(clearProfitCenter());
      } else {
        handleSnackBarOpenValidation();
      }
    }else{
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
      dispatch(clearProfitCenter());
    }
  };
  const onCostCenterReview = () => {
    handleCostCenterReview();
  };
  const onCostCenterRereview = () => {
    handleCostCenterRereview();
  };
  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };
  const handleProfitCenterSubmitForApprovalCreate = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Profit Center Submitted for Approval with ID CPR${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Profit Center");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/alter/profitCenterApprovalSubmit`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCostCenterReview = () => {
    const hSuccess = (data) => {
      setMessageDialogMessage(
        `Create id generated for Data Owners CMS${data.body} `
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_ProfitCenter}/alter/profitCenterSendForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleProfitCenterSaveAsDraft = () => {
    const hSuccess = (data) => {
      setMessageDialogMessage(
        `Create id generated for Data Owners CMS${data.body} `
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/alter/profitCenterAsDraft`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleProfitCenterCorrection = () => {
    const hSuccess = (data) => {
      setMessageDialogMessage(
        `Create id generated for Data Owners CMS${data.body}`
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_ProfitCenter}/alter/profitCenterSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleProfitCenterApproveCreate = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 201) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(`${data.message} `);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Approving Profit Center");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_ProfitCenter}/alter/createProfitCenterApproved`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleProfitCenterRereview = () => {
    const hSuccess = (data) => {
      setMessageDialogMessage(
        `Create id generated for Data Owners CMS${data.body}`
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_ProfitCenter}/alter/profitCenterSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleProfitCenterCorrectionCreate = () => {
    console.log("apicallllllllll");
    const hSuccess = (data) => {
      setMessageDialogMessage(
        `Profit Center has been Sent for Correction CMS${data.body}`
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_ProfitCenter}/alter/profitCenterSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleProfitCenterReviewCreate = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Profit Center has been Submitted for review NPS${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // handleRemarksDialogClose();
        // setIsLoading(false);

        // Make the second API call

        const secondApiPayload = {
          artifactId: pcNumber,
          createdBy: userData?.emailId,
          artifactType: "ProfitCenter",
          requestId: `NPS${data?.body}`,
        };
        console.log("secondApiPayload", secondApiPayload);
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
        // }
      } else {
        setMessageDialogTitle("Create");
        setsuccessMsg(false);
        setMessageDialogMessage("Creation Failed");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_ProfitCenter}/alter/profitCenterSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleWarningDialogClose = () => {
    setOpenMessageDialog(false);
  };
  const handleProfitCenterSaveAsDraftCreate = () => {
    setMessageDialogSeverity(false);
    handleMessageDialogClickOpen();
    setMessageDialogTitle("Confirm");
    setMessageDialogMessage(`Do You Want to Save as Draft ?`);
    setHandleExtrabutton(true);
    setHandleExtraText("proceed");
  };
  const handleProceedbutton = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Profit Center Saved As Draft with ID NPS${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        const secondApiPayload = {
          artifactId: pcNumber,
          createdBy: userData?.emailId,
          artifactType: "ProfitCenter",
          requestId: `NPS${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Saving Profit Center");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_ProfitCenter}/alter/profitCenterAsDraft`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleProfitCenterReviewChange = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Profit Center Submitted for Review with ID CPR${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // handleRemarksDialogClose();
        // setIsLoading(false);

        // Make the second API call

        const secondApiPayload = {
          artifactId: pcNumber,
          createdBy: userData?.emailId,
          artifactType: "ProfitCenter",
          requestId: `CPR${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
        // }
      } else {
        setTestrunStatus(true);
        setSubmitForReviewDisabled(true);
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Profit Center");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_ProfitCenter}/alter/changeProfitCenterSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const handleProfitCenterApproveChange = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 201) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(`${data.message} `);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Saving Profit Center");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_ProfitCenter}/alter/changeProfitCenterApproved`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleProfitCenterSubmitChange = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Profit Center Submitted for Approval with ID CPR${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Profit Center");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_ProfitCenter}/alter/changeProfitCenterApprovalSubmit`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleProfitCenterCorrectionChange = () => {
    console.log("apicallllllllll");
    const hSuccess = (data) => {
      setMessageDialogMessage(
        `Create id generated for Data Owners CMS${data.body}`
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_ProfitCenter}/alter/changeProfitCenterSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const onValidateProfitCenter = () => {
    setBlurLoading(true);
    // Define duplicateCheckPayload outside of the onValidateCostCenter function
    const duplicateCheckPayload = {
      coArea: iDs?.controllingArea
        ? iDs?.controllingArea
        : taskData?.body?.controllingArea,
      name: singlePCPayloadAfterChange?.Name
        ? singlePCPayloadAfterChange?.Name?.toUpperCase()
        : "",
    };

    // const isValidation = handleCheckValidationError();
    // if (isValidation) {
    const hSuccess = (data) => {
      if (data.statusCode === 201) {
        // Handle success
        setMessageDialogTitle("Create");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `All Data has been Validated. Cost Center can be Sent for Review`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
        setValidateFlag(true);

        // Now, make the duplicate check API call
        // Ensure that the conditions for making the duplicate check API call are met
        if (
          duplicateCheckPayload.coArea !== "" ||
          duplicateCheckPayload.name !== ""
        ) {
          doAjax(
            `/${destination_ProfitCenter}/alter/fetchPCDescriptionDupliChk`,
            "post",
            hDuplicateCheckSuccess,
            hDuplicateCheckError,
            duplicateCheckPayload
          );
        }
      } else {
        // Handle error
        setBlurLoading(false);
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          `${
            data?.body?.message[0] ? data?.body?.message[0] : data?.body?.value
          }`
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
    };

    const hDuplicateCheckSuccess = (data) => {
      // Handle success of duplicate check
      if (
        data.body.length === 0 ||
        !data.body.some(
          (item) => item.toUpperCase() === duplicateCheckPayload.name
        )
      ) {
        // No direct match, enable the "Submit for Review" button
        setBlurLoading(false);
        setSubmitForReviewDisabled(false);
        setTestrunStatus(false);
      } else {
        // Handle direct match
        setBlurLoading(false);
        setMessageDialogTitle("Duplicate Check");
        setsuccessMsg(false);
        setMessageDialogMessage(
          `There is a direct match for the Profit Center name. Please change the name.`
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setSubmitForReviewDisabled(true);
      }
    };

    const hDuplicateCheckError = (error) => {
      // Handle error of duplicate check
      console.log(error);
    };

    const hError = (error) => {
      console.log(error);
    };

    // Call the main API for validation
    doAjax(
      `/${destination_ProfitCenter}/alter/validateSingleProfitCenter`,
      "post",
      hSuccess,
      hError,
      payload
    );
    // } else {
    //   handleSnackBarOpenValidation();
    // }
  };

  const handleSnackBarClose = () => {
    if (validateFlag) {
      setopenSnackbar(false);
      setValidateFlag(false);
    } else {
      setopenSnackbar(false);
      navigate("/masterDataCockpit/profitCenter");
    }
  };
  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };
  const handleMessageDialogNavigate = () => {
    navigate("/masterDataCockpit/profitCenter");
  };
  const onProfitCenterApproveCreate = () => {
    setIsLoading(true);
    handleProfitCenterApproveCreate();
  };
  const onProfitCenterSubmitCreate = () => {
    setIsLoading(true);
    handleProfitCenterSubmitForApprovalCreate();
  };
  const onProfitCenterCorrectionCreate = () => {
    handleProfitCenterCorrectionCreate();
  };
  const onProfitCenterReviewCreate = () => {
    setIsLoading(true);
    handleProfitCenterReviewCreate();
  };
  const onProfitCenterSaveAsDraftCreate = () => {
    //setIsLoading(true);
    handleProfitCenterSaveAsDraftCreate();
  };
  const onProfitCenterApproveChange = () => {
    setIsLoading(true);
    handleProfitCenterApproveChange();
  };
  const onProfitCenterReviewChange = () => {
    setIsLoading(true);
    handleProfitCenterReviewChange();
  };
  const onProfitCenterSubmitChange = () => {
    setIsLoading(true);
    handleProfitCenterSubmitChange();
  };
  const onProfitCenterCorrectionChange = () => {
    handleProfitCenterCorrectionChange();
  };
  const onProfitCenterCorrection = () => {
    //     if (
    //       userData?.role === "MDM Steward" &&
    //       (profitCenterRowData?.requestType === "Create" ||
    //         taskRowDetails?.processDesc === "Create")
    //     ) {
    //       handleCorrectionMDMCreate();
    //     } else if (
    //       userData?.role === "MDM Steward" &&
    //       (profitCenterRowData?.requestType === "Change" ||
    //         taskRowDetails?.processDesc === "Change")
    //     ) {
    //       handleCorrectionMDMChange();
    //     } else if (
    //       userData?.role === "Approver" &&
    //       (profitCenterRowData?.requestType === "Create" ||
    //         taskRowDetails?.processDesc === "Create")
    //     ) {
    //       handleCorrectionApproverCreate();
    //     } else if (
    //       userData?.role === "Approver" &&
    //       (profitCenterRowData?.requestType === "Change" ||
    //         taskRowDetails?.processDesc === "Change")
    //     ) {
    //       handleCorrectionApproverChange();
    //     }
  };
  const handleCorrectionMDMCreate = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Profit Center Submitted for Correction with ID NPR${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting Profit Center for Correction"
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_ProfitCenter}/alter/profitCenterSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const onSubmitForReviewButtonClick = () => {
    setIsLoading(true);
    // const formData = new FormData();
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Profit Center has been Submitted for Review NPS${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // Make the second API call
        console.log("secondapi");
        const secondApiPayload = {
          artifactId: pcNumber,
          createdBy: userData?.emailId,
          artifactType: "ProfitCenter",
          requestId: `NPS${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
        // }
      } else {
        setMessageDialogTitle("Create");
        setsuccessMsg(false);
        setMessageDialogMessage("Creation Failed");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleClose();
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter}/alter/profitCenterSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const onCostCenterSubmitRemarks = () => {
    handleCreateDialogClose();
    onSubmitForReviewButtonClick();
  };
  const handleCorrectionMDMChange = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Profit Center Submitted for Correction with ID CPR${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting Profit Center for Correction"
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_ProfitCenter}/alter/changeProfitCenterSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCorrectionApproverCreate = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Profit Center Submitted for Correction with ID NPR${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting Profit Center for Correction"
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_ProfitCenter}/alter/profitCenterSendForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCorrectionApproverChange = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Profit Center Submitted for Correction with ID CPR${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting Profit Center for Correction"
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_ProfitCenter}/alter/changeProfitCenterSendForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCreateDialog = () => {
    setOpenCreateDialog(true);
  };
  const handleCreateDialogClose = () => {
    setOpenCreateDialog(false);
  };
  const handleOpenCorrectionDialog = () => {
    setOpenCorrectionDialog(true);
  };
  const handleCorrectionDialogClose = () => {
    setOpenCorrectionDialog(false);
  };
  const handleRemarks = (e, value) => {
    //setRemarks(e.target.value);
    const newValue = e.target.value;
    if (newValue.length > 0 && newValue[0] === " ") {
      setRemarks(newValue.trimStart());
    } else {
      //let costCenterValue = e.target.value;
      let remarksUpperCase = newValue.toUpperCase();
      setRemarks(remarksUpperCase);
    }
  };

  console.log("factorsarray", factorsArray);

  const tabContents = factorsArray
    .map((item) => {
      const mdata = costCenterDetails.filter(
        (ii) => ii.category?.split(" ")[0] == item?.split(" ")[0]
      );
      if (mdata.length != 0) {
        return { category: item?.split(" ")[0], data: mdata[0].data };
      }
      // return { category: item?.split(" ")[0], data: ddata };
    })
    .map((categoryData, index) => {
      if (categoryData?.category == "Basic" && activeStep == 0) {
        return [
          <Grid
            key={categoryData.category}
            container
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              mt: 1,
              mb: 1,
            }}
          >
            {Object.keys(categoryData.data).map((fieldGroup) => (
              <Grid
                key={fieldGroup}
                item
                md={12}
                sx={{
                  backgroundColor: "white",
                  maxHeight: "max-content",
                  height: "max-content",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                  mt: 0.25,
                  boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                  ...container_Padding,
                }}
              >
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                    margin: "0px !important",
                  }}
                >
                  {fieldGroup}
                </Typography>
                <Box sx={{ width: "100%" }}>
                  <CardContent
                    sx={{
                      padding: "0",
                      paddingBottom: "0 !important",
                      paddingTop: "10px !important",
                    }}
                  >
                    <Grid
                      container
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(6,1fr)",
                        gap: "15px",
                      }}
                      justifyContent="space-between"
                      alignItems="flex-start"
                      md={12}
                    >
                      {categoryData.data[fieldGroup].map((field) => {
                        console.log("fieldDatatttt", field);
                        return (
                          <EditableFieldForProfitCenter
                            label={field.fieldName}
                            value={field.value}
                            length={field.maxLength}
                            data={editedData}
                            visibility={field.visibility}
                            onSave={(newValue) =>
                              handleFieldSave(field.fieldName, newValue)
                            }
                            isEditMode={isEditMode}
                            type={field.fieldType}
                            field={field}
                          />
                        );
                      })}
                    </Grid>
                  </CardContent>
                </Box>
              </Grid>
            ))}
          </Grid>,
        ];
      } else if (categoryData?.category == "Indicators" && activeStep == 1) {
        return [
          <Grid
            key={categoryData.category}
            container
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              mt: 1,
              mb: 1,
            }}
          >
            {Object.keys(categoryData.data).map((fieldGroup) => (
              <Grid
                key={fieldGroup}
                item
                md={12}
                sx={{
                  backgroundColor: "white",
                  maxHeight: "max-content",
                  height: "max-content",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                  mt: 0.25,
                  boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                  ...container_Padding,
                }}
              >
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                    margin: "0px !important",
                  }}
                >
                  {fieldGroup}
                </Typography>
                <Box sx={{ width: "100%" }}>
                  <CardContent
                    sx={{
                      padding: "0",
                      paddingBottom: "0 !important",
                      paddingTop: "10px !important",
                    }}
                  >
                    <Grid
                      container
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(6,1fr)",
                        gap: "15px",
                      }}
                      justifyContent="space-between"
                      alignItems="flex-start"
                      md={12}
                    >
                      {categoryData.data[fieldGroup].map((field) => {
                        return (
                          <EditableFieldForProfitCenter
                            label={field.fieldName}
                            value={field.value}
                            data={editedData}
                            onSave={(newValue) =>
                              handleFieldSave(field.fieldName, newValue)
                            }
                            visibility={field.visibility}
                            isEditMode={isEditMode}
                            type={field.fieldType}
                            field={field}
                          />
                        );
                      })}
                    </Grid>
                  </CardContent>
                </Box>
              </Grid>
            ))}
          </Grid>,
        ];
      } else if (categoryData?.category == "Comp" && activeStep == 2) {
        return [
          <Grid
            key={categoryData.category}
            container
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              mt: 1,
              mb: 1,
            }}
          >
            <CompCodesProfitCenter
              compCodesTabDetails={compCodesTabDetails}
              displayCompCode={dispCompCode}
              // companyCodeBasedOnControllingArea = {}
              // dropDownData={dropDownData}
              // rows={rows}
              // setrows={setrows}
            />
          </Grid>,
        ];
      } else if (categoryData?.category == "Address" && activeStep == 3) {
        return [
          <Grid
            key={categoryData.category}
            container
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              mt: 1,
              mb: 1,
            }}
          >
            {Object.keys(categoryData.data).map((fieldGroup) => (
              <Grid
                key={fieldGroup}
                item
                md={12}
                sx={{
                  backgroundColor: "white",
                  maxHeight: "max-content",
                  height: "max-content",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                  mt: 0.25,
                  boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                  ...container_Padding,
                }}
              >
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                    margin: "0px !important",
                  }}
                >
                  {fieldGroup}
                </Typography>
                <Box sx={{ width: "100%" }}>
                  <CardContent
                    sx={{
                      padding: "0",
                      paddingBottom: "0 !important",
                      paddingTop: "10px !important",
                    }}
                  >
                    <Grid
                      container
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(6,1fr)",
                        gap: "15px",
                      }}
                      justifyContent="space-between"
                      alignItems="flex-start"
                      md={12}
                    >
                      {categoryData.data[fieldGroup].map((field) => {
                        return (
                          <EditableFieldForProfitCenter
                            label={field.fieldName}
                            value={field.value}
                            data={editedData}
                            onSave={(newValue) =>
                              handleFieldSave(field.fieldName, newValue)
                            }
                            visibility={field.visibility}
                            isEditMode={isEditMode}
                            type={field.fieldType}
                            field={field}
                          />
                        );
                      })}
                    </Grid>
                  </CardContent>
                </Box>
              </Grid>
            ))}
          </Grid>,
        ];
      } else if (categoryData?.category == "Communication" && activeStep == 4) {
        return [
          <Grid
            key={categoryData.category}
            container
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              mt: 1,
              mb: 1,
            }}
          >
            {Object.keys(categoryData.data).map((fieldGroup) => (
              <Grid
                key={fieldGroup}
                item
                md={12}
                sx={{
                  backgroundColor: "white",
                  maxHeight: "max-content",
                  height: "max-content",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                  mt: 0.25,
                  boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                  ...container_Padding,
                }}
              >
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                    margin: "0px !important",
                  }}
                >
                  {fieldGroup}
                </Typography>
                <Box sx={{ width: "100%" }}>
                  <CardContent
                    sx={{
                      padding: "0",
                      paddingBottom: "0 !important",
                      paddingTop: "10px !important",
                    }}
                  >
                    <Grid
                      container
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(6,1fr)",
                        gap: "15px",
                      }}
                      justifyContent="space-between"
                      alignItems="flex-start"
                      md={12}
                    >
                      {categoryData.data[fieldGroup].map((field) => {
                        return (
                          <EditableFieldForProfitCenter
                            label={field.fieldName}
                            value={field.value}
                            data={editedData}
                            onSave={(newValue) =>
                              handleFieldSave(field.fieldName, newValue)
                            }
                            visibility={field.visibility}
                            isEditMode={isEditMode}
                            type={field.fieldType}
                            field={field}
                          />
                        );
                      })}
                    </Grid>
                  </CardContent>
                </Box>
              </Grid>
            ))}
          </Grid>,
        ];
      } else if ((categoryData?.category == "History") & (activeStep == 5)) {
        return [
          <Grid
            key={categoryData.category}
            container
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              mt: 1,
              mb: 1,
            }}
          >
            {Object.keys(categoryData.data).map((fieldGroup) => (
              <Grid
                key={fieldGroup}
                item
                md={12}
                sx={{
                  backgroundColor: "white",
                  maxHeight: "max-content",
                  height: "max-content",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                  mt: 0.25,
                  boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                  ...container_Padding,
                }}
              >
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                    margin: "0px !important",
                  }}
                >
                  {fieldGroup}
                </Typography>
                <Box sx={{ width: "100%" }}>
                  <CardContent
                    sx={{
                      padding: "0",
                      paddingBottom: "0 !important",
                      paddingTop: "10px !important",
                    }}
                  >
                    <Grid
                      container
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(6,1fr)",
                        gap: "15px",
                      }}
                      justifyContent="space-between"
                      alignItems="flex-start"
                      md={12}
                    >
                      {categoryData.data[fieldGroup].map((field) => {
                        return (
                          <EditableFieldForProfitCenter
                            label={field.fieldName}
                            value={field.value}
                            data={editedData}
                            onSave={(newValue) =>
                              handleFieldSave(field.fieldName, newValue)
                            }
                            visibility={field.visibility}
                            isEditMode={isEditMode}
                            type={field.fieldType}
                            field={field}
                          />
                        );
                      })}
                    </Grid>
                  </CardContent>
                </Box>
              </Grid>
            ))}
          </Grid>,
        ];
      } else if (categoryData?.category == "Attachments" && activeStep == 6) {
        return [
          <>
            {!isEditMode ? (
              <Card sx={{ padding: "1rem 1rem 0rem 1rem" }}>
                <Grid
                  container
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                  }}
                >
                  <Typography variant="h6">
                    <strong>Attachments</strong>
                  </Typography>
                </Grid>
                {Boolean(attachments.length) && (
                  <ReusableTable
                    width="100%"
                    rows={attachments}
                    columns={attachmentColumns}
                    hideFooter={false}
                    getRowIdValue={"id"}
                    disableSelectionOnClick={true}
                    stopPropagation_Column={"action"}
                  />
                )}
                {!Boolean(attachments.length) && (
                  <Typography variant="body2">No Attachments Found</Typography>
                )}
                <br />
                <Typography variant="h6">Comments</Typography>
                {Boolean(comments.length) && (
                  <Timeline
                    sx={{
                      [`& .${timelineItemClasses.root}:before`]: {
                        flex: 0,
                        padding: 0,
                      },
                    }}
                  >
                    {comments.map((comment) => (
                      <TimelineItem>
                        <TimelineSeparator>
                          <TimelineDot>
                            <CheckCircleOutlineOutlined
                              sx={{ color: "#757575" }}
                            />
                          </TimelineDot>
                          <TimelineConnector />
                        </TimelineSeparator>
                        <TimelineContent sx={{ py: "12px", px: 2 }}>
                          <Card
                            elevation={0}
                            sx={{
                              border: 1,
                              borderColor: "#C4C4C4",
                              borderRadius: "8px",
                              width: "650px",
                            }}
                          >
                            <Box sx={{ padding: "1rem" }}>
                              <Stack spacing={1}>
                                <Grid
                                  sx={{
                                    display: "flex",
                                    justifyContent: "space-between",
                                  }}
                                >
                                  <Typography
                                    sx={{
                                      textAlign: "right",
                                      color: " #757575",
                                      fontWeight: "500",
                                      fontSize: "12px",
                                    }}
                                  >
                                    {moment(comment.createdAt).format(
                                      "DD MMM YYYY"
                                    )}
                                  </Typography>
                                </Grid>

                                <Typography
                                  sx={{
                                    fontSize: "12px",

                                    color: " #757575",
                                    fontWeight: "500",
                                  }}
                                >
                                  {comment.user}
                                </Typography>
                                <Typography
                                  sx={{
                                    fontSize: "12px",
                                    color: "#1D1D1D",
                                    fontWeight: "600",
                                  }}
                                >
                                  {comment.comment}
                                </Typography>
                              </Stack>
                            </Box>
                          </Card>
                        </TimelineContent>
                      </TimelineItem>
                    ))}
                  </Timeline>
                )}
                {!Boolean(comments.length) && (
                  <Typography variant="body2">No Comments Found</Typography>
                )}
                <br />
              </Card>
            ) : (
              <>
                <ReusableAttachementAndComments
                  title="ProfitCenter"
                  useMetaData={false}
                  artifactId={pcNumber}
                  artifactName="ProfitCenter"
                />
                <Card sx={{ padding: "1rem 1rem 0rem 1rem" }}>
                  <Grid
                    container
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                    }}
                  >
                    <Typography variant="h6">
                      <strong>Attachments</strong>
                    </Typography>
                  </Grid>
                  {Boolean(attachments.length) && (
                    <ReusableTable
                      width="100%"
                      rows={attachments}
                      columns={attachmentColumns}
                      hideFooter={false}
                      getRowIdValue={"id"}
                      disableSelectionOnClick={true}
                      stopPropagation_Column={"action"}
                    />
                  )}
                  {!Boolean(attachments.length) && (
                    <Typography variant="body2">
                      No Attachments Found
                    </Typography>
                  )}
                  <br />
                  <Typography variant="h6">Comments</Typography>
                  {Boolean(comments.length) && (
                    <Timeline
                      sx={{
                        [`& .${timelineItemClasses.root}:before`]: {
                          flex: 0,
                          padding: 0,
                        },
                      }}
                    >
                      {comments.map((comment) => (
                        <TimelineItem>
                          <TimelineSeparator>
                            <TimelineDot>
                              <CheckCircleOutlineOutlined
                                sx={{ color: "#757575" }}
                              />
                            </TimelineDot>
                            <TimelineConnector />
                          </TimelineSeparator>
                          <TimelineContent sx={{ py: "12px", px: 2 }}>
                            <Card
                              elevation={0}
                              sx={{
                                border: 1,
                                borderColor: "#C4C4C4",
                                borderRadius: "8px",
                                width: "650px",
                              }}
                            >
                              <Box sx={{ padding: "1rem" }}>
                                <Stack spacing={1}>
                                  <Grid
                                    sx={{
                                      display: "flex",
                                      justifyContent: "space-between",
                                    }}
                                  >
                                    <Typography
                                      sx={{
                                        textAlign: "right",
                                        color: " #757575",
                                        fontWeight: "500",
                                        fontSize: "12px",
                                      }}
                                    >
                                      {moment(comment.createdAt).format(
                                        "DD MMM YYYY"
                                      )}
                                    </Typography>
                                  </Grid>

                                  <Typography
                                    sx={{
                                      fontSize: "12px",

                                      color: " #757575",
                                      fontWeight: "500",
                                    }}
                                  >
                                    {comment.user}
                                  </Typography>
                                  <Typography
                                    sx={{
                                      fontSize: "12px",
                                      color: "#1D1D1D",
                                      fontWeight: "600",
                                    }}
                                  >
                                    {comment.comment}
                                  </Typography>
                                </Stack>
                              </Box>
                            </Card>
                          </TimelineContent>
                        </TimelineItem>
                      ))}
                    </Timeline>
                  )}
                  {!Boolean(comments.length) && (
                    <Typography variant="body2">No Comments Found</Typography>
                  )}
                  <br />
                </Card>
              </>
            )}
          </>,
        ];
      }
    });

  return (
    <>
      {isLoading === true ? (
        <LoadingComponent />
      ) : (
        <div style={{ backgroundColor: "#FAFCFF" }}>
          <ReusableDialog
            dialogState={openMessageDialog}
            openReusableDialog={handleMessageDialogClickOpen}
            closeReusableDialog={handleMessageDialogClose}
            dialogTitle={messageDialogTitle}
            dialogMessage={messageDialogMessage}
            handleDialogConfirm={handleMessageDialogClose}
            dialogOkText={"OK"}
            //handleExtraButton={handleMessageDialogNavigate}
            dialogSeverity={messageDialogSeverity}
            showCancelButton={true}
            handleDialogReject={handleWarningDialogClose}
            handleExtraText={handleExtraText}
            showExtraButton={handleExtrabutton}
            handleExtraButton={handleProceedbutton}
          />

          {successMsg && (
            <ReusableSnackBar
              openSnackBar={openSnackbar}
              alertMsg={messageDialogMessage}
              handleSnackBarClose={handleSnackBarClose}
            />
          )}
          {formValidationErrorItems.length != 0 && (
            <ReusableSnackBar
              openSnackBar={openSnackbarValidation}
              alertMsg={
                "Please fill the following Field: " +
                formValidationErrorItems.join(", ")
              }
              handleSnackBarClose={handleSnackBarCloseValidation}
            />
          )}

          <Dialog
            open={openCorrectionDialog}
            onClose={handleCorrectionDialogClose}
            sx={{
              "&::webkit-scrollbar": {
                width: "1px",
              },
            }}
            fullWidth
          >
            <DialogTitle
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF40",
                // borderBottom: "1px solid grey",
                display: "flex",
              }}
            >
              <Typography variant="h6">Remarks</Typography>

              <IconButton
                sx={{ width: "max-content" }}
                onClick={handleCorrectionDialogClose}
                children={<CloseIcon />}
              />
            </DialogTitle>
            <DialogContent sx={{ padding: ".5rem 1rem" }}>
              <TextField
                autoFocus
                margin="dense"
                id="name"
                label="Enter Remarks for Correction"
                type="text"
                fullWidth
                variant="standard"
                value={remarks}
                onChange={handleRemarks}
              />
            </DialogContent>
            <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
              <Button
                sx={{ width: "max-content", textTransform: "capitalize" }}
                onClick={handleCorrectionDialogClose}
              >
                Cancel
              </Button>
              <Button
                className="button_primary--normal"
                type="save"
                onClick={onProfitCenterCorrection}
                variant="contained"
              >
                OK
              </Button>
            </DialogActions>
          </Dialog>

          <Dialog
            hideBackdrop={false}
            elevation={2}
            PaperProps={{
              sx: { boxShadow: "none" },
            }}
            open={openCreateDialog}
            onClose={handleCreateDialogClose}
          >
            {/* <Grid
          container
          sx={{ display: "flex", justifyContent: "space-between" }}
        > */}
            {/* <Grid item> */}
            <DialogTitle
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF40",
                // borderBottom: "1px solid grey",
                display: "flex",
              }}
            >
              <Typography variant="h6">Remarks</Typography>

              <IconButton
                sx={{ width: "max-content" }}
                onClick={handleCreateDialogClose}
                children={<CloseIcon />}
              />
            </DialogTitle>
            {/* </Grid> */}
            {/* </Grid> */}
            <DialogContent sx={{ padding: ".5rem 1rem" }}>
              <Stack>
                <Box sx={{ minWidth: 400 }}>
                  <FormControl sx={{ height: "auto" }} fullWidth>
                    <TextField
                      sx={{ backgroundColor: "#F5F5F5" }}
                      // value={inputText}
                      value={remarks}
                      onChange={handleRemarks}
                      multiline
                      placeholder={"Enter Remarks"}
                      inputProps={{ maxLength: 254 }}
                    ></TextField>
                  </FormControl>
                </Box>
              </Stack>
              {/* <TextField
            autoFocus
            margin="dense"
            id="name"
            label="Enter Remarks for Correction"
            type="text"
            fullWidth
            variant="standard"
            onChange={handleRemarks}
          /> */}
            </DialogContent>
            <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
              <Button
                sx={{ width: "max-content", textTransform: "capitalize" }}
                onClick={handleCreateDialogClose}
              >
                Cancel
              </Button>
              <Button
                className="button_primary--normal"
                type="save"
                onClick={onCostCenterSubmitRemarks}
                variant="contained"
              >
                Submit
              </Button>
            </DialogActions>
          </Dialog>

          <Backdrop
            sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
            open={blurLoading}
            // onClick={handleClose}
          >
            <CircularProgress color="inherit" />
          </Backdrop>

          <Grid container sx={outermostContainer_Information}>
            <Grid item md={12} style={{ padding: "16px", display: "flex" }}>
              <Grid md={12} sx={{ display: "flex" }}>
                <Grid>
                  <IconButton
                    color="primary"
                    aria-label="upload picture"
                    component="label"
                    sx={iconButton_SpacingSmall}
                  >
                    <ArrowCircleLeftOutlinedIcon
                      sx={{
                        fontSize: "25px",
                        color: "#000000",
                      }}
                      onClick={() => {
                        navigate("/masterDataCockpit/profitCenter");
                      }}
                    />
                  </IconButton>
                </Grid>

                <Grid>
                  {!profitCenterRowData?.requestType && isEditMode ? (
                    <Grid item md={12}>
                      <Typography variant="h3">
                        <strong>Create Profit Center </strong>
                      </Typography>
                      <Typography variant="body2" color="#777">
                        This view creates new Profit Center from existing Profit
                        Center
                      </Typography>
                    </Grid>
                  ) : (
                    ""
                  )}
                  {isEditMode &&
                  profitCenterRowData?.requestType === "Change" ? (
                    <Grid item md={12}>
                      <Typography variant="h3">
                        <strong>Change Profit Center </strong>
                      </Typography>
                      <Typography variant="body2" color="#777">
                        This view changes the details of the Profit Center
                      </Typography>
                    </Grid>
                  ) : (
                    ""
                  )}

                  {isEditMode &&
                  profitCenterRowData?.requestType === "Create" ? (
                    <Grid item md={12}>
                      <Typography variant="h3">
                        <strong>Create Profit Center </strong>
                      </Typography>
                      <Typography variant="body2" color="#777">
                        This view creates a new Profit Center
                      </Typography>
                    </Grid>
                  ) : (
                    ""
                  )}
                  {/* {isExtendMode ? (
                <Grid item md={9}>
                  <Typography variant="h3">
                    <strong>Extend Material: {materialData.reqId} </strong>
                  </Typography>

                  <Typography variant="body2" color="#777">
                    This view extends the details of the materials
                  </Typography>
                </Grid>
              ) : (
                ""
              )} */}

                  {isDisplayMode ? (
                    <Grid item md={12}>
                      <Typography variant="h3">
                        <strong>Display Profit Center </strong>
                      </Typography>

                      <Typography variant="body2" color="#777">
                        This view displays the details of the Profit Center
                      </Typography>
                    </Grid>
                  ) : (
                    ""
                  )}
                </Grid>
              </Grid>
              {profitCenterRowData?.reqStatus === "Correction Pending" ? (
                <Grid>
                  <IconButton
                    color="primary"
                    aria-label="upload picture"
                    component="label"
                    sx={iconButton_SpacingSmall}
                  >
                    <MarkunreadOutlinedIcon
                      sx={{
                        fontSize: "25px",
                        color: "#000000",
                        alignItems: "flex-end",
                      }}
                      onClick={() => {}}
                    />
                  </IconButton>
                </Grid>
              ) : (
                ""
              )}
              <Grid
                md={3}
                sx={{ display: "flex", justifyContent: "flex-end" }}
                gap={2}
              >
                {checkIwaAccess(iwaAccessData, "Profit Center", "ChangePC") &&
                  (userData?.role === "Super User" &&
                  profitCenterRowData?.requestType &&
                  isDisplayMode ? (
                    <Grid gap={1} sx={{ display: "flex" }}>
                      <Grid
                        gap={1}
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                        }}
                      >
                        <>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={onEdit}
                            >
                              Fill Details
                              <EditOutlinedIcon
                                sx={{ padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                        </>
                      </Grid>
                    </Grid>
                  ) : userData?.role === "Finance" &&
                    profitCenterRowData?.requestType &&
                    isDisplayMode ? (
                    <Grid gap={1} sx={{ display: "flex" }}>
                      <Grid
                        gap={1}
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                        }}
                      >
                        <>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={onEdit}
                            >
                              Fill Details
                              <EditOutlinedIcon
                                sx={{ padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                        </>
                      </Grid>
                    </Grid>
                  ) : userData?.role === "Super User" &&
                    !profitCenterRowData?.requestType &&
                    isDisplayMode ? (
                    <Grid gap={1} sx={{ display: "flex" }}>
                      <Grid
                        gap={1}
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                        }}
                      >
                        <>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={onEdit}
                            >
                              Change
                              <EditOutlinedIcon
                                sx={{ padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                        </>
                      </Grid>
                    </Grid>
                  ) : userData?.role === "Finance" &&
                    !profitCenterRowData?.requestType &&
                    isDisplayMode ? (
                    <Grid gap={1} sx={{ display: "flex" }}>
                      <Grid
                        gap={1}
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                        }}
                      >
                        <>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={onEdit}
                            >
                              Change
                              <EditOutlinedIcon
                                sx={{ padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                        </>
                      </Grid>
                    </Grid>
                  ) : (
                    ""
                  ))}
              </Grid>
            </Grid>
            <Grid
              container
              display="flex"
              flexDirection="row"
              flexWrap="nowrap"
            >
              <Box width="70%" sx={{ marginLeft: "40px" }}>
                <Grid item sx={{ paddingTop: "2px !important" }}>
                  <Stack flexDirection="row">
                    <div style={{ width: "10%" }}>
                      <Typography variant="body2" color="#777">
                        Profit Center
                      </Typography>
                    </div>
                    <Typography
                      variant="body2"
                      fontWeight="bold"
                      justifyContent="flex-start"
                    >
                      :{" "}
                      {`P${profitCenterRowData?.companyCodeCopy?.newCompanyCodeCopy?.code}${profitCenterRowData?.profitCenterName?.newProfitCenterName}`}
                    </Typography>
                  </Stack>
                </Grid>

                <Grid item sx={{ paddingTop: "2px !important" }}>
                  <Stack flexDirection="row">
                    <div style={{ width: "10%" }}>
                      <Typography variant="body2" color="#777">
                        Controlling Area
                      </Typography>
                    </div>
                    <Typography variant="body2" fontWeight="bold">
                      :{" "}
                      {
                        profitCenterRowData?.controllingArea?.newControllingArea
                          ?.code
                      }
                    </Typography>
                  </Stack>
                </Grid>
              </Box>
            </Grid>

            <Grid container style={{ marginLeft: 25 }}>
              <Stepper
                activeStep={activeStep}
                sx={{
                  background: "#FFFFFF",
                  borderBottom: "1px solid #BDBDBD",
                  width: "100%",
                  height: "48px",
                }}
                aria-label="mui tabs example"
              >
                {factorsArray.map((factor, index) => (
                  <Step key={factor}>
                    <StepLabel sx={{ fontWeight: "700" }}>{factor}</StepLabel>
                  </Step>
                ))}
              </Stepper>

              {/* Display the cards of the currently active Step */}

              {tabContents &&
                tabContents[activeStep]?.map((cardContent, index) => (
                  <Box key={index} sx={{ mb: 2, width: "100%" }}>
                    <Typography variant="body2">{cardContent}</Typography>
                  </Box>
                ))}
            </Grid>
          </Grid>

          <Grid
            gap={1}
            sx={{ display: "flex", justifyContent: "space-between" }}
          >
            {checkIwaAccess(iwaAccessData, "Profit Center", "ChangePC") &&
              (!profitCenterRowData?.requestType && !isEditMode ? (
                <Paper
                  sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                  elevation={2}
                >
                  <BottomNavigation
                    className="container_BottomNav"
                    showLabels
                    sx={{ display: "flex", justifyContent: "flex-end" }}
                  >
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleBack}
                      disabled={activeStep === 0}
                    >
                      Back
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleNext}
                      disabled={
                        activeStep === factorsArray.length - 1 ? true : false
                      }
                    >
                      Next
                    </Button>
                  </BottomNavigation>
                </Paper>
              ) : (
                ""
              ))}

            {checkIwaAccess(iwaAccessData, "Profit Center", "ChangePC") &&
              (userData?.role === "Super User" &&
              !profitCenterRowData?.requestType &&
              isEditMode ? (
                <Paper
                  sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                  elevation={2}
                >
                  <BottomNavigation
                    className="container_BottomNav"
                    showLabels
                    sx={{ display: "flex", justifyContent: "flex-end" }}
                  >
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ button_Outlined, mr: 1 }}
                      onClick={onProfitCenterSaveAsDraftCreate}
                    >
                      Save As Draft
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleBack}
                      disabled={activeStep === 0}
                    >
                      Back
                    </Button>
                    {activeStep === factorsArray.length - 1 ? (
                      <>
                        <Button
                          variant="contained"
                          size="small"
                          sx={{ ...button_Primary, mr: 1 }}
                          onClick={onValidateProfitCenter}
                        >
                          Validate
                        </Button>
                        <Button
                          variant="contained"
                          size="small"
                          sx={{ ...button_Primary, mr: 1 }}
                          onClick={onProfitCenterReviewChange}
                          disabled={submitForReviewDisabled}
                        >
                          Submit For Review
                        </Button>
                      </>
                    ) : (
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Primary, mr: 1 }}
                        onClick={handleNext}
                        disabled={
                          activeStep === factorsArray.length - 1 ? true : false
                        }
                      >
                        Next
                      </Button>
                    )}
                  </BottomNavigation>
                </Paper>
              ) : userData?.role === "Finance" &&
                !profitCenterRowData?.requestType && //for change from master table
                isEditMode ? (
                <Paper
                  sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                  elevation={2}
                >
                  <BottomNavigation
                    className="container_BottomNav"
                    showLabels
                    sx={{ display: "flex", justifyContent: "flex-end" }}
                  >
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ button_Outlined, mr: 1 }}
                      onClick={onProfitCenterSaveAsDraftCreate}
                    >
                      Save As Draft
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleBack}
                      disabled={activeStep === 0}
                    >
                      Back
                    </Button>
                    {activeStep === factorsArray.length - 1 ? (
                      <>
                        <Button
                          variant="contained"
                          size="small"
                          sx={{ ...button_Primary, mr: 1 }}
                          onClick={onValidateProfitCenter}
                        >
                          Validate
                        </Button>

                        <Button
                          variant="contained"
                          size="small"
                          sx={{ ...button_Primary, mr: 1 }}
                          onClick={handleCreateDialog}
                          disabled={submitForReviewDisabled}
                        >
                          Submit For Review
                        </Button>
                      </>
                    ) : (
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Primary, mr: 1 }}
                        onClick={handleNext}
                        disabled={
                          activeStep === factorsArray.length - 1 ? true : false
                        }
                      >
                        Next
                      </Button>
                    )}
                  </BottomNavigation>
                </Paper>
              ) : (
                ""
              ))}
          </Grid>
        </div>
      )}
    </>
  );
};

export default DisplayCopyProfitCenter;
