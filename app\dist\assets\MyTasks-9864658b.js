import{q as c,bQ as E,s as w,b as O,r as i,pS as A,pU as X,aS as $,aT as Z,aU as j,aV as I,pV as P,j as B,a as T,pW as G,K as _,pX as k,pY as D,a0 as Q,pZ as H,p_ as z,a5 as K,Z as x,cQ as ee,C as ae}from"./index-75c1660a.js";import{c as se,u as oe,g as ie,W as re}from"./propData-c6bd838d.js";import{u as te}from"./useDisplayDataDto-9973887b.js";import"./react-beautiful-dnd.esm-db50900e.js";import"./useMediaQuery-33e0a836.js";import"./DialogContentText-ef8524b5.js";import"./CardMedia-f3120f7c.js";import"./Container-754d6379.js";import"./InputAdornment-a22e1655.js";import"./ListItemButton-f13df81b.js";import"./Slider-c4e5ff46.js";import"./Stepper-2dbfb76b.js";import"./StepButton-e06eb73a.js";import"./ToggleButtonGroup-63ceda7a.js";import"./index.esm-93e9b0e6.js";import"./makeStyles-c2a7efc7.js";import"./toConsumableArray-42cf6573.js";import"./asyncToGenerator-88583e02.js";import"./DatePicker-6e8720de.js";import"./Timeline-5c068db1.js";import"./dayjs.min-83c0b0e0.js";import"./isBetween-51871e12.js";import"./CSSTransition-8d766865.js";function ve(){let n=c(a=>a.userManagement.userData);const e=c(a=>a.userManagement.taskData),{customLog:p}=E(),S=c(a=>a.appSettings.language),l=c(a=>a.applicationConfig);let o=w();const h=O();i.useState({});const[U,ne]=i.useState(null),[M,u]=i.useState(!1),[g,y]=i.useState(""),[R,N]=i.useState(),[f,q]=i.useState(""),{getDisplayData:W}=te(),{showSnackbar:C}=A(),F={APPLICATION_NAME:"1784",CRUD_API_ENV:"itm",DB_TYPE:"hana",SERVICE_BASE_URL:[{Description:"",Name:"ITMJavaServices",URL:"https://cherryworkproducts-itm-java-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"ConfigServer",URL:"https://cherryworkproducts-config-server.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"WorkNetServices",URL:"https://cherryworkproducts-worknet-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"CrudApiServices",URL:"https://cw-caf-crudapi-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"WorkFormsServices",URL:"https://cherrywork-wf-java-qas.cfapps.eu10-004.hana.ondemand.com/workforms"},{Description:"",Name:"NotificationServices",URL:"https://cherryworkproducts-messaging-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"ITMGraphServices",URL:"https://cherrywork-btp-qas-dashboard.cfapps.eu10-004.hana.ondemand.com"},{Description:"Native Workflow Services",Name:"NativeWorkflowServices",URL:"https://cherryworkproducts-custom-wf-qas.cfapps.eu10-004.hana.ondemand.com"},{Description:"Native Workflow UI URL",Name:"NativeWorkflowUiUrl",URL:"https://cherrywork-native-workflow-qas.cfapps.eu10-004.hana.ondemand.com/native-ui"},{Description:"",Name:"OnboardingServices",URL:"https://cherryworkproducts-itm-java-qas.cfapps.eu10-004.hana.ondemand.com"}]},V={DateTimeFormat:{dateTimeFormat:"DD MMM YYYY||HH:mm",timeZone:"Asia/Calcutta"}};`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`,`${e==null?void 0:e.requestId}`;const v=a=>{const r={eventId:"TASK_FORWARDING",taskName:a==null?void 0:a.forwardedTasks.map(s=>s.taskDesc).join(","),requestId:a==null?void 0:a.forwardedTasks.map(s=>`${s.ATTRIBUTE_1}`).join(","),recipientGroup:a==null?void 0:a.recipientUsers.map(s=>s.ownerId).join(","),flowType:a==null?void 0:a.forwardedTasks.map(s=>s.ATTRIBUTE_2).join(",")},t=s=>{p(s)};_(`/${x}/mail/sendMail`,"post",t,r)},d=()=>{u(!0)},m=()=>{u(!1)},L=async a=>{var r;k(ee.CURRENT_TASK,a);try{if((a==null?void 0:a.taskNature)==="Single-User"||(a==null?void 0:a.taskNature)!=="Single-User"&&(a==null?void 0:a.itmStatus)!=="Open"){if(o(I(a)),(a==null?void 0:a.processDisplayName)==="Material")if(!(a!=null&&a.ATTRIBUTE_1))C(D.FETCHING_REQUEST_ID,"info");else if(!(a!=null&&a.ATTRIBUTE_2))C(D.FETCHING_REQUEST_TYPE,"info");else{const t=await W(a==null?void 0:a.ATTRIBUTE_1,a==null?void 0:a.ATTRIBUTE_2,null,a,null);(t==null?void 0:t.statusCode)===Q.STATUS_200&&h(`/requestBench/createRequest?RequestId=${(a==null?void 0:a.ATTRIBUTE_1)||(a==null?void 0:a.requestId)}&RequestType=${(a==null?void 0:a.ATTRIBUTE_2)||H(a==null?void 0:a.requestId)}`)}o(z({url:window.location.pathname,module:"ITMWorkbench"}))}else N("Kindly claim the task before proceeding"),y("Claim Task"),q("info"),d()}catch{p((r=K)==null?void 0:r.ERROR_SET_ROLE)}},b=()=>{console.log("fetchFilterView")},Y=()=>{console.log("clearFilterView")},J=(a,r)=>{console.log("Success flag.",a),console.log("Task Payload.",r)};return i.useEffect(()=>{X({}),o($()),o(Z([])),o(j({})),o(I({})),o(P([]))},[]),B("div",{style:{width:"calc(100vw - 105px)",height:"calc(100vh-130px)"},className:"workspaceOverride",children:[T(re,{token:"********************************************************************************************************************************************************************************************************************************************************************************.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ifsqFBZ_padTXFjXyBwKUrJpAi-FGt5TRdcNpYWrGo8FqJKGTJ2PepbIesuafKHuK6J-G9JM3-RmpBc5F5-s5D_d5OTM59BRqDzR0wgGCVxnhCKbRps_LRJ-EDl1E8wtjKkmZgd0q-Z5vuHX5WTdrsgi5Sgf9v5iW6bgCKVR6SHTAE8N5psPS0ME2B3p4nyZ6dRI62mWIbdk7JO4wTekv80abM_PS5liJlmbqpP4sHX9FYumUFKYwHghn3rQWi4HO_aAWoh0V1HInsUyKEApTouDQeeMvPtuATA7aqCGytz0qu__e_qFhAg2e3OMIjFkAu6Es4eH8o6kGUXBaCKeog",configData:se,destinationData:F,userData:{...n,user_id:n==null?void 0:n.emailId},userPreferences:V,userPermissions:oe,userList:{},groupList:{},languageTranslationData:ie(S),userListBySystem:U,useWorkAccess:l.environment==="localhost",useConfigServerDestination:l.environment==="localhost",inboxTypeKey:"MY_TASKS",workspaceLabel:"Open Tasks",workspaceFiltersByAPIDriven:!1,subInboxTypeKey:null,cachingBaseUrl:G,onTaskClick:L,onActionComplete:J,selectedFilterView:null,isFilterView:!1,fetchFilterViewList:b,savedFilterViewData:[],clearFilterView:Y,filterViewList:[],selectedTabId:null,forwardTaskData:v,userProcess:[]}),T(ae,{dialogState:M,openReusableDialog:d,closeReusableDialog:m,dialogTitle:g,dialogMessage:R,handleDialogConfirm:m,dialogOkText:"OK",dialogSeverity:f})]})}export{ve as default};
