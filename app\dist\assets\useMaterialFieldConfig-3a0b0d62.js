import{s as ae,bQ as re,q,r as W,u as ne,cP as le,cQ as ce,bn as g,$ as C,K as b,a0 as Q,cR as $,cS as Y,cT as j,cU as B,cV as K,cW as k,cX as m,cY as x,cZ as H,c_ as X,c$ as Z,d0 as J,d1 as Ee,aX as oe}from"./index-75c1660a.js";const z=t=>{let S={},_=t==null?void 0:t.sort((r,N)=>r.MDG_MAT_VIEW_SEQUENCE-N.MDG_MAT_VIEW_SEQUENCE);const L=J(_,"MDG_MAT_VIEW_NAME");let v=[];Object.entries(L).forEach(([r,N])=>{let M=J(N,"MDG_MAT_CARD_NAME"),n=[];Object.entries(M).forEach(([O,D])=>{D.sort((E,V)=>E.MDG_MAT_SEQUENCE_NO-V.MDG_MAT_SEQUENCE_NO);let h=D.map(E=>({fieldName:E.MDG_MAT_UI_FIELD_NAME,sequenceNo:E.MDG_MAT_SEQUENCE_NO,fieldType:E.MDG_MAT_FIELD_TYPE,maxLength:E.MDG_MAT_MAX_LENGTH,dataType:E.MDG_MAT_DATA_TYPE,viewName:E.MDG_MAT_VIEW_NAME,cardName:E.MDG_MAT_CARD_NAME,cardSeq:E.MDG_MAT_CARD_SEQUENCE,value:E.MDG_MAT_DEFAULT_VALUE,visibility:E.MDG_MAT_VISIBILITY,jsonName:E.MDG_MAT_JSON_FIELD_NAME,fieldPriority:E.MDG_MAT_MATERIAL_FLD_PRT}));n.push({cardName:O,cardSeq:D[0].MDG_MAT_CARD_SEQUENCE,cardDetails:h})}),n.sort((O,D)=>O.cardSeq-D.cardSeq),v.push({viewName:r,cards:n})});let d=Ee(v),y={};return d.forEach(r=>{let N={};r.cards.forEach(M=>{N[M.cardName]=M.cardDetails,r.viewName!=="Request Header"&&M.cardDetails.forEach(n=>{n.visibility===oe.MANDATORY&&(S[n.viewName]||(S[n.viewName]=[]),S[n.viewName].push({jsonName:n==null?void 0:n.jsonName,fieldName:n==null?void 0:n.fieldName}))})}),y[r.viewName]=N}),{transformedData:y,mandatoryFields:S}},Te=()=>{const t=ae(),{customError:S}=re(),_=q(a=>{var l;return(l=a.payload)==null?void 0:l.payloadData}),L=q(a=>a.applicationConfig),{userData:v,taskData:d}=q(a=>a.userManagement),[y,r]=W.useState(!0),[N,M]=W.useState(null);ne();const n=window.location.href.includes("DisplayMaterialSAPView"),O=le(ce.CURRENT_TASK);let D=null;D=typeof O=="string"?JSON.parse(O):O;let h=D==null?void 0:D.ATTRIBUTE_5;const E=async(a,l)=>{if(_!=null&&_.RequestType||n){const T={decisionTableId:null,decisionTableName:"MDG_MAT_MATERIAL_FIELD_CONFIG",version:"v4",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_SCENARIO":(_==null?void 0:_.RequestType)||"Display","MDG_CONDITIONS.MDG_MAT_MATERIAL_TYPE":n?a.split("-")[0]:a||"VERP","MDG_CONDITIONS.MDG_MAT_REGION":(_==null?void 0:_.Region)||"US","MDG_CONDITIONS.MDG_MAT_GROUP_ROLE":n?"Z_MAT_REQ_DISPLAY":d.ATTRIBUTE_5?d.ATTRIBUTE_5:h||"Z_MAT_REQ_INITIATE"}],systemFilters:null,systemOrders:null,filterString:null},i=s=>{var I,G,u,U,p;if(s.statusCode===Q.STATUS_200){if(Array.isArray((I=s==null?void 0:s.data)==null?void 0:I.result)&&((G=s==null?void 0:s.data)!=null&&G.result.every(o=>Object.keys(o).length!==0))){let o=(U=(u=s==null?void 0:s.data)==null?void 0:u.result[0])==null?void 0:U.MDG_MAT_MATERIAL_FIELD_CONFIG;const{transformedData:c,mandatoryFields:F}=z(o),P=Object.keys(c).map(e=>(e==="Basic Data"?t($(c["Basic Data"])):e==="Sales"?t(Y(c.Sales)):e==="Purchasing"?t(j(c.Purchasing)):e==="MRP"?t(B(c.MRP)):e==="Accounting"&&t(K(c.Accounting)),e!=="Request Header"&&t(k({tab:e,data:c[e]})),{[e]:c[e]}));t(m({[(_==null?void 0:_.Region)||l||"US"]:{[a]:{allfields:x(P),mandatoryFields:F}}}));let f=[...new Set((p=o==null?void 0:o.sort((e,w)=>e.MDG_MAT_VIEW_SEQUENCE-w.MDG_MAT_VIEW_SEQUENCE))==null?void 0:p.map(e=>e.MDG_MAT_VIEW_NAME))];f=[...f.filter(e=>!H.includes(e))],t(X({matType:a,views:f}))}else t(m({[(_==null?void 0:_.Region)||l||"US"]:{[a]:{}}}));r(!1)}},A=s=>{S(s),M(s),r(!1)},R=L.environment==="localhost"?`/${g}${C.INVOKE_RULES.LOCAL}`:`/${g}${C.INVOKE_RULES.PROD}`;b(R,"post",i,A,T)}},V=(a,l,T)=>{r(!0);try{E(a,l,T)}catch(i){M(i),r(!1)}},ee=async(a,l)=>{if(_!=null&&_.RequestType){const T={decisionTableId:null,decisionTableName:"MDG_MAT_EXTEND_TEMPLATE_DT",version:"v2",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_SCENARIO":"Extend","MDG_CONDITIONS.MDG_MAT_REGION":"US","MDG_CONDITIONS.MDG_MAT_GROUP_ROLE":"CA-MDG-MD-TEAM-US"}],systemFilters:null,systemOrders:null,filterString:null},i=s=>{var I,G,u,U,p;if(s.statusCode===Q.STATUS_200){if(Array.isArray((I=s==null?void 0:s.data)==null?void 0:I.result)&&((G=s==null?void 0:s.data)!=null&&G.result.every(o=>Object.keys(o).length!==0))){let o=(U=(u=s==null?void 0:s.data)==null?void 0:u.result[0])==null?void 0:U.MDG_MAT_EXTEND_TEMPLATE_DT;const{transformedData:c,mandatoryFields:F}=z(o),P=Object.keys(c).map(e=>(e==="Basic Data"?t($(c["Basic Data"])):e==="Sales"?t(Y(c.Sales)):e==="Purchasing"?t(j(c.Purchasing)):e==="MRP"?t(B(c.MRP)):e==="Accounting"&&t(K(c.Accounting)),e!=="Request Header"&&t(k({tab:e,data:c[e]})),{[e]:c[e]}));t(m({[(_==null?void 0:_.Region)||l||"US"]:{[a]:{allfields:x(P),mandatoryFields:F}}}));let f=[...new Set((p=o==null?void 0:o.sort((e,w)=>e.MDG_MAT_VIEW_SEQUENCE-w.MDG_MAT_VIEW_SEQUENCE))==null?void 0:p.map(e=>e.MDG_MAT_VIEW_NAME))];f=f.filter(e=>!H.includes(e)),t(X({matType:a,views:f}))}else t(m({[(_==null?void 0:_.Region)||l||"US"]:{[a]:{}}}));r(!1)}},A=s=>{S(s),M(s),r(!1)},R=L.environment==="localhost"?`/${g}${C.INVOKE_RULES.LOCAL}`:`/${g}${C.INVOKE_RULES.PROD}`;b(R,"post",i,A,T)}},se=(a,l)=>{r(!0);try{ee(a,l)}catch(T){M(T),r(!1)}},_e=a=>{let l={decisionTableId:null,decisionTableName:"MDG_MAT_REG_SORG_PLNT_CNTRY_STORE_WH_MAPPING",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":"US"}],systemFilters:null,systemOrders:null,filterString:null};const T=A=>{var R,s;if(A.statusCode===Q.STATUS_200){let I=(s=(R=A==null?void 0:A.data)==null?void 0:R.result[0])==null?void 0:s.MDG_MAT_REG_SORG_PLNT_CNTRY_STORE_WH_MAPPING;const G=Array.from(new Map(I.map(u=>[u.MDG_MAT_SALES_ORG,{code:u.MDG_MAT_SALES_ORG,desc:u.MDG_MAT_SALES_ORG_DESC}])).values());t(Z({keyName:"uniqueSalesOrgList",data:G})),t(Z({keyName:"salesOrgData",data:I}))}},i=A=>{S(A)};L.environment==="localhost"?b(`/${g}${C.INVOKE_RULES.LOCAL}`,"post",T,i,l):b(`/${g}${C.INVOKE_RULES.PROD}`,"post",T,i,l)};return{loading:y,error:N,fetchMaterialFieldConfig:V,fetchMaterialFieldConfigExtend:se,fetchOrgData:a=>{r(!0);try{_e(a)}catch(l){M(l),r(!1)}}}};export{Te as u};
