import{r as f,dY as p,dZ as h,dO as w}from"./index-75c1660a.js";const E=typeof window<"u"?f.useLayoutEffect:f.useEffect,S=E;function y(t,u,e,s,o){const[r,i]=f.useState(()=>o&&e?e(t).matches:s?s(t).matches:u);return S(()=>{let c=!0;if(!e)return;const n=e(t),d=()=>{c&&i(n.matches)};return d(),n.addListener(d),()=>{c=!1,n.removeListener(d)}},[t,e]),r}const l=w["useSyncExternalStore"];function L(t,u,e,s,o){const r=f.useCallback(()=>u,[u]),i=f.useMemo(()=>{if(o&&e)return()=>e(t).matches;if(s!==null){const{matches:a}=s(t);return()=>a}return r},[r,t,s,o,e]),[c,n]=f.useMemo(()=>{if(e===null)return[r,()=>()=>{}];const a=e(t);return[()=>a.matches,m=>(a.addListener(m),()=>{a.removeListener(m)})]},[r,e,t]);return l(n,c,i)}function b(t,u={}){const e=p(),s=typeof window<"u"&&typeof window.matchMedia<"u",{defaultMatches:o=!1,matchMedia:r=s?window.matchMedia:null,ssrMatchMedia:i=null,noSsr:c=!1}=h({name:"MuiUseMediaQuery",props:u,theme:e});let n=typeof t=="function"?t(e):t;return n=n.replace(/^@media( ?)/m,""),(l!==void 0?L:y)(n,o,r,i,c)}export{b as u};
