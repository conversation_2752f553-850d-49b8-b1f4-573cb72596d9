import{r as t,q as p,s as kn,b as Cn,p as Z,K as k,a as n,bX as Bn,j as u,cb as Sn,cc as xn,C as vn,aj as mn,ak as de,G as h,al as Dn,bY as wn,T as g,bZ as Nn,h as _e,I as Q,b1 as Ue,b_ as Kn,R as ue,am as pn,g as Mn,A as En,an as In,f as An,aq as D,ar as O,b6 as he,E as w,aw as Tn,ax as zn,t as C,b8 as bn,aB as Rn,az as Fn,aC as $n,bm as Ln,aD as ge,aE as Pn,V as _n,aF as Un,aG as Wn,W as Yn,X as Vn,aJ as We,aK as Ye,aL as Ve,aM as He,M as Ge,F as Hn,P as b,c1 as Gn,ab as W,eK as B,O as Jn,eL as Je,ai as qn,eM as Xn,eN as Zn,a_ as qe,eO as Qn,Z as fe}from"./index-17b8d91e.js";import"./react-dropzone-uploader-f23f38ba.js";import{A as On}from"./AttachmentUploadDialog-d151e36a.js";import{I as jn}from"./InputAdornment-5b0053c5.js";import"./CloudUpload-27b6d63e.js";import"./Delete-9f4d7a45.js";import"./utilityImages-067c3dc2.js";const Ct=()=>{var Le,Pe;t.useState(!1);const[et,Xe]=t.useState(!1),[Ze,S]=t.useState("");p(e=>e.appSettings.Format);const[Y,Qe]=t.useState(""),[M,Oe]=t.useState("");t.useState(""),t.useState("");const[ye,je]=t.useState(!1);t.useState(!1);const[at,ke]=t.useState(!1),[ea,V]=t.useState(!1),[aa,Ce]=t.useState(!1),y=kn(),j=Cn(),[na,Be]=t.useState(0),[ee,H]=t.useState(0),[Se,ae]=t.useState(10),[xe,G]=t.useState(0),ta=(e,a)=>{H(a)},sa=e=>{const a=e.target.value;ae(a),H(0),G(0)},la=48,oa=8,ra={PaperProps:{style:{maxHeight:la*4.5+oa,width:250}}},[ia,R]=t.useState(!1),[ca,da]=t.useState(null),[A,ve]=t.useState([]);Z.useState(""),t.useState(!1),t.useState("");const[nt,ua]=t.useState("");t.useState(!0);const[tt,me]=t.useState(!1),[st,De]=t.useState(!0);t.useState([]),t.useState([]),t.useState([]),t.useState([]);const[lt,F]=t.useState(!0),[ot,ha]=t.useState([]),[ga,fa]=t.useState([]);t.useState(!1);const[J,we]=t.useState([]),[rt,ya]=t.useState([]),[Ne,ka]=t.useState({});t.useState([]),t.useState([]),t.useState(!1),t.useState([]);const[Ke,Ca]=t.useState([]);t.useState([]);const[Ba,pe]=t.useState(!1);t.useState(!1),t.useState(!0),t.useState("sm");const[Sa,$]=t.useState(!1);t.useState("");const[xa,Me]=t.useState(!1),[va,ma]=t.useState(!1),[ne,te]=t.useState(!1);Z.useRef(null);const L=Z.useRef(null),[Da,wa]=t.useState(0);t.useState(!1);const[se,le]=t.useState(!1),P=Z.useRef(null);t.useState(0);const[Na,Ka]=t.useState(0),pa=p(e=>e.AllDropDown.dropDown),[it,Ee]=t.useState(!1);console.log("dropdownData",pa);const oe=["Create Multiple","Upload Template ","Download Template "],re=["Change Multiple","Upload Template ","Download Template "],i=p(e=>e.commonFilter.BankKey),E=p(e=>e.commonSearchBar.BankKey);console.log("formcontroller_SearchBar",E),console.log("rmSearchForm",i);const T=p(e=>{var a;return(a=e==null?void 0:e.AllDropDown)==null?void 0:a.dropDown});let Ma=p(e=>{var a;return(a=e.userManagement.entitiesAndActivities)==null?void 0:a["Bank Key"]}),z=p(e=>e.userManagement.userData);const Ie=p(e=>e.bankKey.handleMassMode),Ea=()=>{pe(!0)},Ia=()=>{if((M==null?void 0:M.code)===void 0||(M==null?void 0:M.code)===""||Y===void 0||Y===""){Ee(!1),Me(!0);return}else Ee(!1),$a(),Me(!1)},ie=()=>{pe(!1),je(!1)},Aa=e=>{if(e.target.value!==null){var a=e.target.value;let o={...i,bankName:a};y(b({module:"BankKey",filterData:o}))}},Ta=e=>{if(e.target.value!==null){var a=e.target.value;let o={...i,bankBranch:a};y(b({module:"BankKey",filterData:o}))}},za=e=>{if(e.target.value!==null){var a=e.target.value;let o={...i,swiftBic:a};y(b({module:"BankKey",filterData:o}))}},ba=e=>{if(e.target.value!==null){var a=e.target.value;let o={...i,bankNumber:a};y(b({module:"BankKey",filterData:o}))}},Ra=(e,a)=>{{var o=a;let r={...i,bankCtrRegion:o};y(b({module:"BankKey",filterData:r}))}},Fa=(e,a)=>{{var o=e.target.value;let r={...i,createdBy:o};y(b({module:"BankKey",filterData:r}))}},N={bankKey:{newBankKey:Y},bankCtryReg:{newBankCtryReg:M}},Ae={convertJsonToExcel:()=>{let e=[];Re.forEach(a=>{a.headerName.toLowerCase()!=="action"&&!a.hide&&e.push({header:a.headerName,key:a.field})}),Gn({fileName:`Bank Key Data-${W(ce).format("DD-MMM-YYYY")}`,columns:e,rows:A})},button:()=>n(C,{sx:{textTransform:"capitalize",position:"absolute",right:0,top:0},onClick:()=>Ae.convertJsonToExcel(),children:"Download"})},$a=()=>{var c,s,l;Ce(!0);let e=(c=N==null?void 0:N.bankKey)==null?void 0:c.newBankKey,a=(l=(s=N==null?void 0:N.bankCtryReg)==null?void 0:s.newBankCtryReg)==null?void 0:l.code,o=e.concat("$$",a);console.log("sendNewBankKeyData",N);const r=f=>{console.log("dupli",f),f.body.length>0?(Ce(!1),ma(!0)):j("/masterDataCockpit/bankKey/newSingleBankKey",{state:N})},d=f=>{console.log(f)};k(`/${B}/alter/fetchBankKeyCountryDupliChk?bankKeyCountryToCheck=${o}`,"get",r,d)},La=()=>{const e=o=>{y(qn({keyName:"CountryReg",data:o.body}))},a=o=>{console.log(o)};k(`/${B}/data/getCountry`,"get",e,a)};t.useEffect(()=>{(parseInt(ee)+1)*parseInt(Se)>=parseInt(xe)+1e3&&(X(xe+1e3),G(e=>e+1e3))},[ee,Se]),t.useEffect(()=>{La()},[]);let Pa={"Person Responsible":`/${fe}/data/getSalesOrg`,"Business Area ":`/${fe}/data/getDivision`,"Functional Area":`/${fe}/data/getLaboratoryDesignOffice`};const _a=e=>{const a=e.target.value;we(a),ya([]),console.log("selected field",e.target.value),a.forEach(async o=>{const r=Pa[o];Va(r)})},Ua={"Task ID":"taskId",Status:"status",SalesOrganization:"salesOrg",Division:"division",OldMaterialNumber:"oldMaterialNumber","Lab/Office":"labOffice","Transportation Group":"transportationGroup","Batch management":"batchManagement"},Wa=()=>{let e="Bank Details";const a=r=>{y(Xn(r.body))},o=r=>{console.log(r)};k(`/${B}/data/getViewFieldDetails?viewName=${e}`,"get",a,o)},Ya=()=>{let e="Address Details";const a=r=>{y(Zn(r.body))},o=r=>{console.log(r)};k(`/${B}/data/getViewFieldDetails?viewName=${e}`,"get",a,o)};t.useEffect(()=>{Wa(),Ya()},[]);const Va=e=>{k(e,"get",r=>{console.log("dataaaaaaaa",r.body),Ca([...Ke,r.body])},r=>{console.log(r)})},Ha=()=>{ua("")},Ga=e=>{console.log("hhhfhfhfhf"),V(!0),e||(H(0),ae(10),G(0));let a={bankCountry:"",bankKey:(E==null?void 0:E.number)??"",bankName:"",swiftCode:"",bankNumber:"",region:"",branch:"",createdBy:"",top:1e3,skip:e??0};console.log("payload",a);const o=d=>{var f,I,_;console.log("data",d.body.list);var c=[];for(let K=0;K<((I=(f=d==null?void 0:d.body)==null?void 0:f.list)==null?void 0:I.length);K++){var s=d==null?void 0:d.body.list[K];console.log("hshshsh",s);var l={id:qe(),bankCtryReg:(s==null?void 0:s.BankCountry)!==""?s==null?void 0:s.BankCountry:"Not Available",bankKey:(s==null?void 0:s.BankInternalID)!==""?s==null?void 0:s.BankInternalID:"Not Available",bankName:(s==null?void 0:s.BankName)!==""?s==null?void 0:s.BankName:"Not Available",bankBranch:(s==null?void 0:s.Branch)!==""?s==null?void 0:s.Branch:"Not Available",swiftBic:(s==null?void 0:s.SWIFTCode)!==""?s==null?void 0:s.SWIFTCode:"Not Available",bankNumber:(s==null?void 0:s.BankNumber)!==""?s==null?void 0:s.BankNumber:"Not Available",createdBy:(s==null?void 0:s.CreatedByUser)!==""?s==null?void 0:s.CreatedByUser:"Not Available",changedBy:(s==null?void 0:s.changedBy)!==""?s==null?void 0:s.changedBy:"Not Available",createdOn:W(s.CreationDate).format("DD MMM YYYY")};c.push(l)}console.log("tempobj",l),console.log("tempObH",s),c.sort((K,U)=>W(K.createdOn,"DD MMM YYYY HH:mm")-W(U.createdOn,"DD MMM YYYY HH:mm")),ve(c),V(!1),Te(c.length),Be((_=d==null?void 0:d.body)==null?void 0:_.count)};let r=d=>{console.log(d)};k(`/${B}/data/getBankKeysBasedOnAdditionalParams`,"post",o,r,a)},ce=new Date,q=new Date;q.setDate(q.getDate()-15),t.useState([q,ce]),t.useState([q,ce]);const X=e=>{var d;console.log("called"),V(!0),e||(H(0),ae(10),G(0));let a={bankCountry:((d=i==null?void 0:i.bankCtrRegion)==null?void 0:d.code)??"",bankKey:(E==null?void 0:E.number)??"",bankName:(i==null?void 0:i.bankName)??"",swiftCode:(i==null?void 0:i.swiftBic)??"",bankNumber:(i==null?void 0:i.bankNumber)??"",region:"",branch:(i==null?void 0:i.bankBranch)??"",createdBy:(i==null?void 0:i.createdBy)??"",top:1e3,skip:e??0};const o=c=>{var I,_,K;console.log("data",c.body.list);var s=[];for(let U=0;U<((_=(I=c==null?void 0:c.body)==null?void 0:I.list)==null?void 0:_.length);U++){var l=c==null?void 0:c.body.list[U];console.log("hshshsh",l);var f={id:qe(),bankCtryReg:(l==null?void 0:l.BankCountry)!==""?l==null?void 0:l.BankCountry:"Not Available",bankKey:(l==null?void 0:l.BankInternalID)!==""?l==null?void 0:l.BankInternalID:"Not Available",bankName:(l==null?void 0:l.BankName)!==""?l==null?void 0:l.BankName:"Not Available",bankBranch:(l==null?void 0:l.Branch)!==""?l==null?void 0:l.Branch:"Not Available",swiftBic:(l==null?void 0:l.SWIFTCode)!==""?l==null?void 0:l.SWIFTCode:"Not Available",bankNumber:(l==null?void 0:l.BankNumber)!==""?l==null?void 0:l.BankNumber:"Not Available",createdBy:(l==null?void 0:l.CreatedByUser)!==""?l==null?void 0:l.CreatedByUser:"Not Available",changedBy:(l==null?void 0:l.changedBy)!==""?l==null?void 0:l.changedBy:"Not Available",createdOn:W(l.CreationDate).format("DD MMM YYYY")};s.push(f)}console.log("tempobj",f),console.log("tempObH",l),ve(s),V(!1),Te(s.length),Be((K=c==null?void 0:c.body)==null?void 0:K.count)},r=c=>{console.log(c)};k(`/${B}/data/getBankKeysBasedOnAdditionalParams`,"post",o,r,a)};t.useState([]),t.useState([]),t.useState(null),t.useState(null);const[Ja,Te]=t.useState(0);t.useState(!1),t.useState(!1),t.useState(!1),t.useState(!1),t.useState(!1),t.useState(!1),t.useState(""),t.useState("");const[qa,ze]=t.useState(!1),[Xa,x]=t.useState(""),[Za,v]=t.useState(),m=()=>{ze(!0)},be=()=>{ze(!1)};t.useState(null),t.useState(null),t.useState(null);const Qa=()=>{y(Jn({module:"BankKey"}))},Oa=e=>{const a=e.map(f=>A.find(I=>I.id===f));var o=a.map(f=>f.company),r=new Set(o),d=a.map(f=>f.vendor),c=new Set(d),s=a.map(f=>f.paymentTerm),l=new Set(s);a.length>0?r.size===1?c.size===1?l.size!==1?(F(!0),x("Error"),v("Invoice cannot be generated for vendors with different payment terms"),S("danger"),m()):F(!1):(F(!0),x("Error"),v("Invoice cannot be generated for multiple suppliers"),S("danger"),m()):(F(!0),x("Error"),v("Invoice cannot be generated for multiple companies"),S("danger"),m()):F(!0),ha(e),fa(a)};function ja(){X()}t.useState([]),t.useState([]);const[ct,en]=t.useState(!1);t.useState(null),t.useState(null),t.useState([]);const an=()=>{en(!1)};t.useState(null),t.useState("");const Re=[{field:"bankCtryReg",headerName:"Bank Country",editable:!1,flex:1},{field:"bankKey",headerName:"Bank key",editable:!1,flex:1},{field:"bankName",headerName:"Bank Name",editable:!1,flex:1},{field:"bankBranch",headerName:"Bank Branch",editable:!1,flex:1},{field:"swiftBic",headerName:"SWIFT/BIC",editable:!1,flex:1},{field:"bankNumber",headerName:"Bank Number",editable:!1,flex:1},{field:"createdBy",headerName:"Created By",editable:!1,flex:1},{field:"createdOn",headerName:"Created On",editable:!1,flex:1}],nn=J.map(e=>{const a=Ua[e];return a?{field:a,headerName:e,editable:!1,flex:1}:null}).filter(e=>e!==null),tn=[...Re,...nn],sn=()=>{Xe(!0)},ln=e=>{R(!0),console.log(e);const a=new FormData;if([...e].forEach(c=>a.append("files",c)),console.log(Ie,"handleMassModeBK"),Ie==="Change")var o=`/${B}/massAction/getAllBankKeyFromExcelForMassChange`;else var o=`/${B}/massAction/getAllBankKeyFromExcel`;k(o,"postformdata",c=>{console.log(c,"example"),R(!1),c.statusCode===200?($(!1),y(Qn(c==null?void 0:c.body)),x("Create"),v(`${e.name} has been Uploaded Succesfully`),S("success"),De(!1),ke(!0),sn(),me(!0),R(!1),j("/masterDataCockpit/bankKey/createMultipleBankKey")):($(!1),x("Create"),ke(!1),v("Creation Failed"),S("danger"),De(!1),me(!0),m(),R(!1)),an()},c=>{console.log(c)},a)};t.useEffect(()=>{X()},[]);let on=t.useRef(null);const rn=async()=>{let e=o=>{const r=URL.createObjectURL(o),d=document.createElement("a");d.href=r,d.setAttribute("download","Bank Key_Mass Create.xls"),document.body.appendChild(d),d.click(),document.body.removeChild(d),URL.revokeObjectURL(r),m(),x("Success"),v("Bank Key_Mass Create.xls has been downloaded successfully"),S("success")},a=o=>{o.message&&(m(),x("Error"),v(`${o.message}`),S("danger"))};k(`/${B}/excel/downloadExcel`,"getblobfile",e,a)},cn=async()=>{var e=ga.map(r=>({bankCtry:(r==null?void 0:r.bankCtryReg)??"",bankKey:(r==null?void 0:r.bankKey)??""}));console.log("downloadPayload",e);let a=r=>{R(!1);const d=URL.createObjectURL(r),c=document.createElement("a");c.href=d,c.setAttribute("download","Bank Key_Mass Change.xls"),document.body.appendChild(c),c.click(),document.body.removeChild(c),URL.revokeObjectURL(d),m(),x("Success"),v("Bank Key_Mass Change.xls has been downloaded successfully"),S("success")},o=r=>{r.message&&(m(),x("Error"),v(`${r.message}`),S("danger"))};k(`/${B}/excel/downloadExcelWithData`,"postandgetblob",a,o,e)},dn=()=>{$(!0),y(Je("Create"))},un=()=>{te(e=>!e)},Fe=(e,a)=>{a!==0&&(wa(a),te(!1),a===1?dn():a===2&&rn())},hn=e=>{L.current&&L.current.contains(e.target)||te(!1)},gn=()=>{le(e=>!e)},fn=e=>{P.current&&P.current.contains(e.target)||le(!1)},$e=(e,a)=>{a!==0&&(Ka(a),le(!1),a===1?yn():a===2&&cn())},yn=()=>{$(!0),y(Je("Change"))};return n(Hn,{children:ia===!0?n(Bn,{}):u("div",{ref:on,children:[n(Sn,{sx:{color:"#fff",zIndex:e=>e.zIndex.drawer+999999},open:aa,children:n(xn,{color:"inherit"})}),n(vn,{dialogState:qa,openReusableDialog:m,closeReusableDialog:be,dialogTitle:Xa,dialogMessage:Za,handleDialogConfirm:be,dialogOkText:"OK",dialogSeverity:Ze}),n("div",{style:{...mn,backgroundColor:"#FAFCFF"},children:u(de,{spacing:1,children:[u(h,{container:!0,sx:Dn,children:[u(h,{item:!0,md:5,sx:wn,children:[n(g,{variant:"h3",children:n("strong",{children:"Bank Key"})}),n(g,{variant:"body2",color:"#777",children:"This view displays the list of Bank Keys"})]}),n(h,{item:!0,md:7,sx:{display:"flex"},children:u(h,{container:!0,direction:"row",justifyContent:"flex-end",alignItems:"center",spacing:0,children:[n(Nn,{title:"Search for multiple Bank Key numbers separated by comma",handleSearchAction:()=>Ga(),module:"BankKey",keyName:"number",message:"Search Bank Key ",clearSearchBar:Ha}),n(_e,{title:"Reload",children:n(Q,{sx:Ue,children:n(Kn,{sx:{"&:hover":{transform:"rotate(360deg)",transition:"0.9s"}},onClick:ja})})}),n(_e,{title:"Export Table",children:n(Q,{sx:Ue,onClick:Ae.convertJsonToExcel,children:n(ue,{iconName:"IosShare"})})})]})})]}),n(h,{container:!0,sx:pn,children:n(h,{item:!0,md:12,children:u(Mn,{className:"filter-accordian",children:[n(En,{expandIcon:n(In,{}),"aria-controls":"panel1a-content",id:"panel1a-header",sx:{minHeight:"2rem !important",margin:"0px !important"},children:n(g,{sx:{fontWeight:"700"},children:"Search Bank Key"})}),u(An,{sx:{padding:"0.5rem 1rem 0.5rem"},children:[u(h,{container:!0,rowSpacing:1,spacing:2,justifyContent:"space-between",alignItems:"center",children:[u(h,{container:!0,spacing:1,sx:{padding:"0rem 1rem 0.5rem"},children:[u(h,{item:!0,md:2,children:[n(g,{sx:D,children:"Bank Country"}),n(O,{size:"small",fullWidth:!0,children:n(he,{sx:{height:"31px"},fullWidth:!0,size:"small",value:i==null?void 0:i.bankCtrRegion,onChange:Ra,options:(T==null?void 0:T.CountryReg)??[],getOptionLabel:e=>e!=null&&e.code?`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??"":"",renderOption:(e,a)=>n("li",{...e,children:n(g,{style:{fontSize:12},children:`${a==null?void 0:a.code} - ${a==null?void 0:a.desc}`})}),renderInput:e=>n(w,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"Select Country"})})})]}),u(h,{item:!0,md:2,children:[n(g,{sx:D,children:"Bank Name"}),n(w,{sx:{fontSize:"12px !important"},size:"small",fullWidth:!0,onChange:Aa,placeholder:"Enter Bank Name",value:i==null?void 0:i.bankName})]}),u(h,{item:!0,md:2,children:[n(g,{sx:D,children:"Bank Branch"}),n(w,{sx:{fontSize:"12px !important"},size:"small",fullWidth:!0,onChange:Ta,placeholder:"Enter Bank Branch",value:i==null?void 0:i.bankBranch})]}),u(h,{item:!0,md:2,children:[n(g,{sx:D,children:"SWIFT/BIC"}),n(w,{sx:{fontSize:"12px !important"},size:"small",fullWidth:!0,onChange:za,placeholder:"Enter Swift/BIC",value:i==null?void 0:i.swiftBic})]}),u(h,{item:!0,md:2,children:[n(g,{sx:D,children:"Bank Number"}),n(w,{sx:{fontSize:"12px !important"},size:"small",fullWidth:!0,onChange:ba,placeholder:"Enter Bank Number",value:i==null?void 0:i.bankNumber})]}),u(h,{item:!0,md:2,children:[n(g,{sx:D,children:"Created By"}),n(w,{sx:{fontSize:"12px !important"},fullWidth:!0,size:"small",value:i==null?void 0:i.createdBy,onChange:Fa,placeholder:"Enter Created By"})]}),u(h,{item:!0,md:2,children:[n(g,{sx:D,children:"Add New Filters"}),n(O,{children:n(Tn,{sx:{font_Small:D,height:"31px",fontSize:"12px",width:"200px"},size:"small",multiple:!0,limitTags:2,value:J,onChange:_a,renderValue:e=>e.join(", "),MenuProps:{MenuProps:ra},endAdornment:J.length>0&&n(jn,{position:"end",children:n(Q,{size:"small",onClick:()=>we([]),"aria-label":"Clear selections",children:n(zn,{})})})})}),n(h,{style:{display:"flex",justifyContent:"space-around"}})]})]}),n(h,{container:!0,sx:{flexDirection:"row",padding:"0rem 1rem 0.5rem"},gap:1,children:J.map((e,a)=>n(h,{item:!0,children:u(de,{children:[n(g,{sx:{fontSize:"12px"},children:e}),n(he,{sx:D,size:"small",options:Ke??[],getOptionLabel:(o,r)=>{var d,c;return`${(d=o[r])==null?void 0:d.code} - ${(c=o[r])==null?void 0:c.desc}`},placeholder:`Enter ${e}`,value:Ne[e],onChange:(o,r)=>ka({...Ne,[e]:r}),renderInput:o=>n(w,{sx:{fontSize:"12px !important"},...o,size:"small",placeholder:`Enter ${e}`,variant:"outlined"})},e[a])]})}))})]}),n(h,{container:!0,style:{display:"flex",justifyContent:"flex-end"},children:u(h,{item:!0,style:{display:"flex",justifyContent:"space-around"},children:[n(C,{variant:"outlined",sx:bn,onClick:Qa,children:"Clear"}),n(C,{variant:"contained",sx:{...Rn,...Fn},onClick:()=>X(),children:"Search"})]})})]})]})})}),n(h,{item:!0,sx:{position:"relative"},children:n(de,{children:n($n,{isLoading:ea,module:"BankKey",width:"100%",title:"List of Bank Keys ("+Ja+")",rows:A,columns:tn,page:ee,pageSize:10,rowCount:na??(A==null?void 0:A.length)??0,onPageChange:ta,onPageSizeChange:sa,getRowIdValue:"id",hideFooter:!0,checkboxSelection:!0,disableSelectionOnClick:!0,status_onRowSingleClick:!0,onRowsSelectionHandler:Oa,callback_onRowSingleClick:e=>{const a=e.row.bankKey;j(`/masterDataCockpit/bankKey/displayBankKey/${a}`,{state:e.row})},stopPropagation_Column:"action",status_onRowDoubleClick:!0,showCustomNavigation:!0})})}),Ln(Ma,"Bank Key","CreateBK")&&(z==null?void 0:z.role)==="Super User"||(z==null?void 0:z.role)==="Finance"?n(ge,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:u(Pn,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:ca,onChange:e=>{da(e)},children:[n(C,{size:"small",variant:"contained",onClick:Ea,children:"Create Single"}),u(_n,{open:Ba,onClose:ie,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[u(Un,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[n(g,{variant:"h6",children:"New Bank Key"}),n(Q,{sx:{width:"max-content"},onClick:ie,children:n(Wn,{})})]}),u(Yn,{sx:{padding:".5rem 1rem"},children:[u(h,{container:!0,children:[u(h,{item:!0,md:5,sx:{width:"100%",marginTop:".5rem",marginRight:"5rem"},children:[u(g,{children:["Bank Country",n("span",{style:{color:"red"},children:"*"})]}),n(O,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px"},children:n(he,{sx:{height:"31px"},fullWidth:!0,size:"small",onChange:(e,a)=>{Oe(a)},options:(T==null?void 0:T.CountryReg)??[],getOptionLabel:e=>e!=null&&e.code?`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??"":"",renderOption:(e,a)=>n("li",{...e,children:n(g,{style:{fontSize:12},children:`${a==null?void 0:a.code} - ${a==null?void 0:a.desc}`})}),renderInput:e=>n(w,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT BANK COUNTRY",error:ye})})}),ye&&n(g,{variant:"caption",color:"error",children:"Please Select a Country."})]}),u(h,{item:!0,md:5,sx:{width:"100%",marginTop:".5rem"},children:[u(g,{children:["Bank Key",n("span",{style:{color:"red"},children:"*"})]}),n(O,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px"},children:n(w,{sx:{fontSize:"12px !important",height:"31px"},fullWidth:!0,size:"small",value:Y,onChange:e=>{let o=e.target.value.toUpperCase();Qe(o)},inputProps:{maxLength:15,style:{textTransform:"uppercase"}},placeholder:"Enter Bank Key",required:!0})})]})]}),xa&&n(h,{children:n(g,{style:{color:"red"},children:"Please Enter Mandatory Fields"})}),va&&n(h,{children:n(g,{style:{color:"red"},children:"*The Bank Key with this Country already exist. Please enter different Bank Key or Country"})})]}),u(Vn,{sx:{display:"flex",justifyContent:"end"},children:[n(C,{sx:{width:"max-content",textTransform:"capitalize"},onClick:ie,children:"Cancel"}),n(C,{className:"button_primary--normal",type:"save",onClick:Ia,variant:"contained",children:"Proceed"})]})]}),u(We,{variant:"contained",ref:L,"aria-label":"split button",children:[n(C,{size:"small",onClick:()=>Fe(oe[0],0),sx:{cursor:"default"},children:oe[0]}),n(C,{size:"small","aria-controls":ne?"split-button-menu":void 0,"aria-expanded":ne?"true":void 0,"aria-label":"select action","aria-haspopup":"menu",onClick:un,children:n(ue,{iconName:"ArrowDropUp",iconColor:"#FFFFFF"})})]}),n(Ye,{sx:{zIndex:1},open:ne,anchorEl:L.current,placement:"top-end",children:n(ge,{style:{width:(Le=L.current)==null?void 0:Le.clientWidth},children:n(Ve,{onClickAway:hn,children:n(He,{id:"split-button-menu",autoFocusItem:!0,children:oe.slice(1).map((e,a)=>n(Ge,{selected:a===Da-1,onClick:()=>Fe(e,a+1),children:e},e))})})})}),u(We,{variant:"contained",ref:P,"aria-label":"split button",children:[n(C,{size:"small",onClick:()=>$e(re[0],0),sx:{cursor:"default"},children:re[0]}),n(C,{size:"small","aria-controls":se?"split-button-menu":void 0,"aria-expanded":se?"true":void 0,"aria-label":"select action","aria-haspopup":"menu",onClick:gn,children:n(ue,{iconName:"ArrowDropUp",iconColor:"#FFFFFF"})})]}),n(Ye,{sx:{zIndex:1},open:se,anchorEl:P.current,placement:"top-end",children:n(ge,{style:{width:(Pe=P.current)==null?void 0:Pe.clientWidth},children:n(Ve,{onClickAway:fn,children:n(He,{id:"split-button-menu",autoFocusItem:!0,children:re.slice(1).map((e,a)=>n(Ge,{selected:a===Na-1,onClick:()=>$e(e,a+1),children:e},e))})})})}),Sa&&n(On,{artifactId:"",artifactName:"",setOpen:$,handleUpload:ln})]})}):""]})})]})})};export{Ct as default};
