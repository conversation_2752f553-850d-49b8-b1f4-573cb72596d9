import{q as R,r as i,s as Ee,u as Ie,e as Fe,l as C,j as l,dc as h,a as r,F as M,t as oe,dd as Pe,de as _e,V as Me,aF as qe,T as m,aG as Le,I as le,W as $e,x as V,a5 as ie,cc as Ne,cb as ze,z as We,a_ as Ue,K as de,bq as G,$ as K,df as je,dg as J,a4 as ce,dh as He,di as ue,b as Ge,dj as Ke}from"./index-17b8d91e.js";import{d as Je}from"./CloudUpload-27b6d63e.js";import{i as pe}from"./utilityImages-067c3dc2.js";import{d as Ve}from"./Add-98854918.js";import{d as Xe}from"./Delete-9f4d7a45.js";const Ye=({sidebyside:q=!1,getAttachmentshook:X=()=>{},processName:Y="",artifactId:f="",artifactName:S="",poNumber:D,isAnAttachment:k,promptAction_Functions:b,attachmentType:w})=>{var se;const L=R(e=>{var t,a;return(a=(t=e.request)==null?void 0:t.requestHeader)==null?void 0:a.requestType}),$=R(e=>{var t,a;return(a=(t=e==null?void 0:e.userManagement)==null?void 0:t.taskData)==null?void 0:a.ATTRIBUTE_2}),N=R(e=>{var t,a;return(a=(t=e.payload)==null?void 0:t.payloadData)==null?void 0:a.RequestType}),[E,A]=i.useState(!1),[u,x]=i.useState(!1),[n,B]=i.useState([]),[s,y]=i.useState([]),[z,O]=i.useState(!1),[W,Qe]=i.useState(["Others"]),[Q,Z]=i.useState(!1),[he,I]=i.useState(!1),[me,F]=i.useState(""),[ge,U]=i.useState("success"),ee=Ee(),fe=Ie(),xe=new URLSearchParams(fe.search).get("reqBench");let j=R(e=>e.userManagement.userData);const o=R(e=>e.payload.payloadData),T=R(e=>{var t,a;return(a=(t=e==null?void 0:e.payload)==null?void 0:t.dynamicKeyValues)==null?void 0:a.childRequestHeaderData}),{t:p}=Fe();let te={other:C.attachmentDialog.other,"order change":C.attachmentDialog.orderChange},re=e=>te[e]?te[e]:C.attachmentDialog.orderChange,H=()=>{O(!1),B([])};const ye=()=>{I(!1),F("")};let ae=()=>{O(!0)},Ce=e=>r(M,{children:e==null?void 0:e.map((t,a)=>{var g,d,v,_;return l("div",{style:{minWidth:"5rem",minHeight:"6rem",height:"6.5rem",width:"5.4rem",borderRadius:".5rem",margin:"0 .5rem",border:`1px solid ${re((g=t.metaData)==null?void 0:g.toLowerCase())}`,overflow:"hidden",boxShadow:"0px 6px 6px -3px rgba(0,0,0,0.2)",position:"relative"},children:[r(h,{sx:{height:"1rem",backgroundColor:re((d=t.metaData)==null?void 0:d.toLowerCase()),display:"flex",justifyContent:"center",overflow:"hidden"},children:r(m,{sx:{fontSize:"10px",width:"100%",textAlign:"center"},children:t==null?void 0:t.metaData})}),r(h,{sx:{display:"flex",justifyContent:"center",alignItems:"center",padding:"0.6rem"},children:r("img",{style:{width:"1.6rem",height:"1.6rem"},src:pe[((v=t==null?void 0:t.name)==null?void 0:v.split(".")[1])||((_=t==null?void 0:t.fileName)==null?void 0:_.split(".")[1])]})}),r(h,{sx:{overflow:"hidden"},children:r(m,{variant:"body2",sx:{padding:".3rem",color:"grey",textAlign:"center",textOverflow:"ellipsis",overflow:"hidden",whiteSpace:"noWrap",height:"2rem"},children:(t==null?void 0:t.name)||(t==null?void 0:t.fileName)})})]},a)})});const Se=e=>{e.preventDefault(),A(!0)},be=()=>{A(!1)},Be=e=>{e.preventDefault(),A(!1);const t=Array.from(e.dataTransfer.files);if(n.length+t.length>5){b.handleOpenPromptBox("Warning",{title:"Warning",message:ie.NUMBER_OF_FILES_LIMIT,severity:"warning",cancelButton:!1});return}ne(t)},Te=()=>{document.getElementById("fileButton").click()},ne=e=>{let t=[];e.forEach(a=>{a.id=Ue(),t.push(a)}),B(a=>[...a,...t])},ve=()=>{let e=t=>{var g;let a=[];(g=t==null?void 0:t.documentDetailDtoList)==null||g.forEach(d=>{a.push({id:d.documentId,docType:d.fileType,name:d==null?void 0:d.fileName,uploadedBy:d.createdBy,metaData:d.metadataTag??""})}),y(a)};if(S==="RequestBench"){const a=(T==null?void 0:T.ChildRequestId)||(o==null?void 0:o.childRequestId)?`/${G}/${K.TASK_ACTION_DETAIL.GET_CHILD_DOCS}/${f}`:`/${G}/${K.TASK_ACTION_DETAIL.GET_DOCS}/${f}`;de(a,"get",e)}},P=L||$||N;let Re=()=>{if(!Q&&n[0]){let e=[...n];(()=>{x(!0);const a=new FormData;let g={};W==null||W.forEach(c=>{g[c.metadataTag]=c.id});let d=[];e==null||e.forEach(c=>{({...c},c.name),a.append("files",c,c.name),d.push(c.name)}),ee(je(f));var v={artifactId:f,createdBy:j==null?void 0:j.emailId,artifactType:S,attachmentType:w,requestId:J(P,o==null?void 0:o.RequestId),childRequestId:[ce.CHANGE,ce.CHANGE_WITH_UPLOAD].includes(o==null?void 0:o.RequestType)?J(P,T==null?void 0:T.ChildRequestId)??null:J(P,o==null?void 0:o.childRequestId)??null,fileName:d.join("$^$"),requestType:P};k&&(v.isAnAttachment=k),D&&S!=="Purchase Order"&&(v.poNumber=D),a.append("doc",JSON.stringify(v));let _=c=>{ee(He(w)),c.responseMessage.status==="Failure"?(x(!1),U("error"),F(`${p(ue.UPLOAD_FAILED)}`),I(!0)):(c.responseMessage.status==="Success"||c.responseMessage.status==="Partial Success")&&(X(),U("success"),F(`Documents for ${S} ${f} uploaded successfully `),I(!0),H(),x(!1),y(we=>[...we,...n]))},ke=c=>{x(!1),U("error"),F(`${p(ue.ALT_UPLOAD_ERROR)}`),I(!0)};de(`/${G}/${K.DMS_API.UPLOAD_DOCUMENT}`,"postformdata",_,ke,a)})();return}n[0]||b.handleOpenPromptBox("Warning",{title:"Warning",message:p("Please add some files to upload"),severity:"warning",cancelButton:!1})},Ae=()=>{H()},Oe=e=>{let t=n.filter(a=>a.id!==e);B(t)},De=()=>{let e=0;n.forEach(t=>{e+=t.length}),e>5e9?(b.handleOpenPromptBox("ERROR",{title:"Warning",message:p("Files size excceded"),severity:"warning",cancelButton:!1}),Z(!0)):Z(!1)};return i.useEffect(()=>{De()},[n]),i.useEffect(()=>{ve()},[]),l(M,{children:[l(h,{children:[r(h,{sx:{display:"flex",flexDirection:"row"},children:q===!1?l(M,{children:[r(h,{sx:{display:"flex",flexDirection:"row",margin:"1rem 0",maxWidth:"50vw",overflowX:"auto",position:"relative",minHeight:"7rem"},children:s[0]&&Ce(s)}),r(h,{onClick:ae,style:{width:"5rem",maxWidth:"5rem",height:"6.5rem",borderRadius:".5rem",backgroundColor:"#eae9ff",padding:"1rem",display:"flex",justifyContent:"center",alignItems:"center",margin:"1rem .5rem",boxShadow:"0px 2px 4px -1px rgba(0,0,0,0.2),0px 4px 5px 0px rgba(0,0,0,0.14),0px 1px 10px 0px rgba(0,0,0,0.12)",cursor:"pointer"},children:r(Ve,{color:"primary",sx:{fontSize:24}})})]}):r(h,{display:"flex",justifyContent:"center",alignItems:"center",sx:{mr:1},children:r(oe,{variant:"contained",component:"label",onClick:ae,startIcon:r(Pe,{}),disabled:xe&&!((se=_e)!=null&&se.includes(o==null?void 0:o.RequestStatus)),sx:{backgroundColor:"#1976d2",color:"#fff",textTransform:"capitalize",borderRadius:"5px",padding:"10px 20px","&:hover":{backgroundColor:"#1565c0"},boxShadow:"0px 4px 10px rgba(0, 0, 0, 0.2)"},children:p("Upload File")})})}),z&&l(Me,{fullWidth:!0,maxWidth:"sm",open:!0,onClose:H,sx:{"& .MuiDialog-paper":{borderRadius:"12px",padding:"1rem"},overflow:"hidden"},children:[l(qe,{sx:{padding:"1rem 1.5rem"},children:[r(m,{variant:"h6",sx:{fontWeight:500},children:p("Add New Attachment")}),r(le,{"aria-label":"close",onClick:Ae,sx:e=>({position:"absolute",right:12,top:10,color:e.palette.grey[500]}),children:r(Le,{})})]}),l($e,{sx:{padding:"1.5rem",overflow:"hidden"},children:[l(h,{className:`dropzone ${E?"dragover":""}`,sx:{width:"100%",border:`2px dashed ${E?"#3b30c8":"#d0d5dd"}`,borderRadius:"8px",padding:"2rem",backgroundColor:E?"#f8f9ff":"#fafbff",transition:"all 0.3s ease",cursor:"pointer",minHeight:"200px",display:"flex",alignItems:"center",justifyContent:"center"},onDragOver:Se,onDragLeave:be,onDrop:Be,children:[l(V,{alignItems:"center",spacing:1,children:[r(Je,{sx:{fontSize:48,color:"#3b30c8"}}),r(m,{variant:"body1",sx:{color:"#344054"},children:p("Drag and drop files")}),r(m,{variant:"body2",color:"primary",sx:{cursor:"pointer",textDecoration:"underline","&:hover":{color:"#3b30c8"}},onClick:Te,children:p("or click to upload")})]}),r("input",{id:"fileButton",multiple:!0,accept:".jpeg, .jpg, .xls, .xlsx, .docx, .pdf",type:"file",name:"files",onChange:e=>{const t=Array.from(e.target.files);if(n.length+t.length>5){b.handleOpenPromptBox("Warning",{title:p("Warning"),message:ie.NUMBER_OF_FILES_LIMIT,severity:"warning",cancelButton:!1});return}ne(t)},style:{display:"none"}})]}),n[0]&&r(h,{sx:{maxHeight:"14rem",overflowY:"auto",marginTop:"1.5rem",padding:"1rem",backgroundColor:C.primary.white,borderRadius:"8px",border:"1px solid #eee","&::-webkit-scrollbar-thumb":{backgroundColor:"#888",borderRadius:"3px"}},children:n==null?void 0:n.map((e,t)=>{var a;return l(h,{sx:{display:"flex",alignItems:"center",padding:"0.75rem",borderRadius:"6px",backgroundColor:C.primary.white,marginBottom:"0.5rem",border:"1px solid #eee","&:hover":{backgroundColor:"#f9fafb"}},children:[r("img",{style:{width:"24px",height:"24px",marginRight:"0.75rem"},src:pe[(a=e.name)==null?void 0:a.split(".")[1]]}),r(m,{variant:"body1",sx:{flexGrow:1},children:e.name}),Q?l(m,{variant:"body2",color:"error",sx:{marginLeft:"1rem"},children:[parseFloat(e.size/1e6).toFixed(2)," MB"]}):l(m,{sx:{marginLeft:"auto",marginRight:"10%"},color:"gray",children:[parseFloat(e.size/1e6).toFixed(2)," MB"]}),r(le,{id:`closeBtn-${e.id}`,size:"small",onClick:g=>{g.stopPropagation(),Oe(e.id)},sx:{marginLeft:"0.5rem",opacity:.8},children:r(Xe,{fontSize:"small",color:"error"})})]},t)})}),l(V,{direction:"row",spacing:2,sx:{marginTop:"1.5rem",justifyContent:"flex-end",alignItems:"center"},children:[l(m,{variant:"body2",color:"error",children:["*",p("Max file size 500 MB")]}),r(m,{variant:"body2",sx:{color:C.placeholder.dark},children:p("Maximum 5 files allowed")}),r(oe,{variant:"contained",onClick:Re,sx:{borderRadius:"6px",padding:"0.5rem 1.5rem"},children:p("Upload")})]})]})]}),r(ze,{sx:{color:C.primary.white,zIndex:1400},open:u,children:r(Ne,{color:"inherit"})})]}),r(We,{openSnackBar:he,alertMsg:me,alertType:ge,handleSnackBarClose:ye})]})},st=({title:q="",module:X="",artifactName:Y="",getAttachments:f=()=>{},artifactId:S="",poNumber:D="",processName:k="",isAnAttachment:b="",commentOnly:w=!0,view:L=!1,attachmentType:$="",requestId:N="",disableCheck:E})=>{const A=Ge(),[u,x]=i.useState({open:!1,type:"",redirectOnClose:!0,message:"",title:"",severity:"",okButtonText:""}),[n,B]=i.useState(""),s={handleClosePromptBox:()=>{x(y=>({open:!1,type:"",redirectOnClose:!0,message:"",title:"",severity:""})),B("")},handleOpenPromptBox:(y,z={})=>{let O={open:!0,title:"",message:"",okButton:!0,cancelButton:!0,okText:"Ok",cancelText:"Cancel",type:"dialog"};y==="SUCCESS"&&(O.type="snackbar"),B(y),x({...O,...z})},handleCloseAndRedirect:()=>{s.handleClosePromptBox(),A("/purchaseOrder/management")},getCancelFunction:()=>{switch(n){default:return()=>{s.handleClosePromptBox()}}},getCloseFunction:()=>{switch(n){case"COMMENTERROR":default:return y=>{s.handleClosePromptBox()}}},getOkFunction:()=>{switch(n){default:return()=>{s.handleClosePromptBox()}}},getCloseAndRedirectFunction:()=>u.redirectOnClose?s.handleCloseAndRedirect:s.handleClosePromptBox};return l(M,{children:[r(Ke,{type:u.type,promptState:u.open,setPromptState:s.handleClosePromptBox,onCloseAction:s.getCloseFunction(),promptMessage:u.message,dialogSeverity:u.severity,dialogTitleText:u.title,handleCancelButtonAction:s.getCancelFunction(),cancelButtonText:u.cancelText,showCancelButton:u.cancelButton,handleSnackBarPromptClose:s.getCloseAndRedirectFunction(),handleOkButtonAction:s.getOkFunction(),okButtonText:u.okButtonText,showOkButton:u.okButton}),r(V,{children:w&&r(Ye,{sidebyside:!0,view:L,useMetaData:!1,title:q,artifactName:Y,artifactId:S,processName:k,poNumber:D,promptAction_Functions:s,isAnAttachment:b,attachmentType:$,requestId:N,getAttachmentshook:f})})]})};export{st as R};
