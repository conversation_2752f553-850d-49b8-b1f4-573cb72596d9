import{r as g,a as s,bv as F,bc as I,ay as O,T as b,B as f,j as d,I as a,ax as K,cc as Q,d2 as X,d3 as Y,E as Z,d4 as u,aD as p,d5 as M,bF as S,F as G}from"./index-75c1660a.js";const on=({matGroup:i,selectedMaterialGroup:n,setSelectedMaterialGroup:c,isDropDownLoading:T,placeholder:C="Select Option",onInputChange:k,minCharacters:y=0})=>{const[m,z]=g.useState(!1),[$,R]=g.useState(null),[V,_]=g.useState(""),[j,v]=g.useState(!1),[l,r]=g.useState(""),B=g.useRef(null),P=g.useRef(null),t=g.useMemo(()=>{const o=Array.isArray(i)?i.map(h=>({value:h.code,label:h.desc||h.code})):[];return l?o.filter(h=>h.value.toLowerCase().includes(l.toLowerCase())||h.label.toLowerCase().includes(l.toLowerCase())):o},[i,l]),D=o=>{const h=o.target.value;r(h),k&&k(o)},w=g.useCallback(o=>{const h=n.some(x=>x.code===o.value);c(h?n.filter(x=>x.code!==o.value):[...n,{code:o.value,desc:o.label}])},[n,c]),E=g.useCallback(()=>{(n==null?void 0:n.length)===i.length?c([]):c(i.map(o=>({code:o.code,desc:o.desc})))},[i,n==null?void 0:n.length,c]),H=g.useCallback(()=>{c([])},[c]),W=(o,h)=>{R(o.currentTarget),_(h),v(!0)},A=()=>{v(!1)},N=()=>{v(!0)},U=()=>{v(!1)},q=g.useCallback(({index:o,style:h})=>{if(o===0)return s(f,{component:"div",sx:{...h,padding:"4px 8px",cursor:"pointer","&:hover":{backgroundColor:"action.hover"}},onClick:E,children:s(F,{sx:{width:"100%",py:.5},children:s(I,{sx:{margin:0,"& .MuiFormControlLabel-label":{flex:1}},control:s(O,{size:"small",checked:(n==null?void 0:n.length)===i.length,indeterminate:(n==null?void 0:n.length)>0&&(n==null?void 0:n.length)<(i==null?void 0:i.length),sx:{py:.5}}),label:s(b,{sx:{fontSize:13},children:"Select All"})})})});const x=t[o-1],L=n.some(e=>e.code===x.value);return s(f,{component:"div",sx:{...h,padding:"4px 8px",cursor:"pointer","&:hover":{backgroundColor:"action.hover"}},onClick:()=>w(x),children:s(F,{sx:{width:"100%",py:.5},children:s(I,{sx:{margin:0,"& .MuiFormControlLabel-label":{flex:1}},control:s(O,{size:"small",checked:L,sx:{py:.5}}),label:d(b,{sx:{fontSize:13},children:[s("strong",{children:x.value})," - ",x.label]})})})})},[t,n,w,E,i==null?void 0:i.length]);g.useEffect(()=>{const o=h=>{P.current&&!P.current.contains(h.target)&&z(!1)};return document.addEventListener("mousedown",o),()=>{document.removeEventListener("mousedown",o)}},[]);const J=()=>{var o,h;return(n==null?void 0:n.length)===0?null:(n==null?void 0:n.length)>1?d(G,{children:[s(S,{sx:{height:25,fontSize:"0.85rem",".MuiChip-label":{padding:"0 6px"}},label:(o=n==null?void 0:n[0])==null?void 0:o.code}),s(S,{sx:{height:25,fontSize:"0.85rem",".MuiChip-label":{padding:"0 6px"},ml:.5},label:`+${(n==null?void 0:n.length)-1}`,onMouseEnter:x=>{const L=n.slice(1).map(e=>`<strong>${e.code}</strong> - ${e.desc}`).join("<br />");W(x,L)},onMouseLeave:A})]}):s(S,{sx:{height:25,fontSize:"0.85rem",".MuiChip-label":{padding:"0 6px"}},label:(h=n==null?void 0:n[0])==null?void 0:h.code})};return d(f,{ref:P,sx:{position:"relative",width:"100%"},children:[s(Z,{fullWidth:!0,size:"small",placeholder:C==null?void 0:C.toUpperCase(),value:l,onChange:D,onFocus:()=>z(!0),InputProps:{startAdornment:J(),endAdornment:d(f,{sx:{display:"flex",alignItems:"center"},children:[(n==null?void 0:n.length)>0&&s(a,{size:"small",onClick:o=>{o.stopPropagation(),H(),r("")},sx:{padding:"4px",mr:.5,"&:hover":{backgroundColor:"rgba(0, 0, 0, 0.04)"}},children:s(K,{sx:{fontSize:"16px"}})}),T?s(Q,{size:20,sx:{mr:1}}):s(a,{size:"small",onClick:o=>{o.stopPropagation(),z(!m)},sx:{padding:"4px"},children:m?s(X,{sx:{fontSize:"20px"}}):s(Y,{sx:{fontSize:"20px"}})})]})}}),m&&s(p,{sx:{position:"absolute",top:"100%",left:0,right:0,mt:1,maxHeight:300,zIndex:1e3},children:t.length===0?s(f,{sx:{p:2,textAlign:"center"},children:s(b,{variant:"body2",color:"text.secondary",children:l.length<y?`Please enter at least ${y} characters`:"No options found"})}):s(u,{height:Math.min(t.length*45+45,300),itemCount:t.length+1,itemSize:45,width:"100%",ref:B,children:q})}),s(M,{open:j,anchorEl:$,onClose:A,anchorOrigin:{vertical:"bottom",horizontal:"left"},transformOrigin:{vertical:"top",horizontal:"left"},onMouseEnter:N,onMouseLeave:U,PaperProps:{sx:{p:1,maxWidth:300}},children:s(b,{variant:"body2",dangerouslySetInnerHTML:{__html:V}})})]})};export{on as L};
