import{r as f,s as It,b as Ft,u as Et,q as X,bh as qt,K as q,b$ as j,a as n,j as d,b4 as ge,T as v,B as J,br as be,G as h,aC as Zr,bt as Pr,bs as qe,x as Z,ab as ve,F as H,bX as zt,C as Bt,z as wr,V as Ve,aF as We,I as He,aG as Rr,W as Ke,ar as le,E as ze,X as Xe,t as a,cb as jt,cc as Mt,al as Ot,b1 as $t,b8 as O,bm as Be,aD as _,aE as V,aB as x,b6 as Lt,at as Dr,au as en,cd as Ut,bW as je,bq as ce,bp as kt,ce as _t,ai as de}from"./index-17b8d91e.js";import{d as Vt,C as Wt}from"./ChangeLog-0f47d713.js";import{d as Ht}from"./ArrowCircleLeftOutlined-2a09f8e2.js";import{d as Me}from"./EditOutlined-36c8ca4d.js";import{E as Se}from"./EditableFieldForCostCenter-78cb6c06.js";import{R as Kt}from"./ReusableAttachementAndComments-bab6bbfc.js";import{M as Xt,a as Jt}from"./UtilDoc-d76e2af6.js";import{l as rn}from"./lookup-1dcf10ac.js";import{T as nn}from"./Timeline-36ba02ac.js";import{t as tn,T as on,a as sn,b as ln,c as cn,d as dn}from"./TimelineSeparator-0839d5e3.js";import{S as Yt,a as Qt,b as Gt}from"./Stepper-88e4fb0c.js";import{D as an}from"./DatePicker-68227989.js";import"./CloudUpload-27b6d63e.js";import"./utilityImages-067c3dc2.js";import"./Add-98854918.js";import"./Delete-9f4d7a45.js";/* empty css            */import"./FileDownloadOutlined-c800f30b.js";import"./VisibilityOutlined-315d5644.js";import"./DeleteOutlined-888bfc33.js";import"./Slider-3eb7e770.js";import"./clsx-a965ebfb.js";import"./dateViewRenderers-34586552.js";import"./useSlotProps-e34e1e13.js";import"./InputAdornment-5b0053c5.js";import"./CSSTransition-30917e2c.js";import"./useMediaQuery-6a073ac5.js";import"./DesktopDatePicker-07c19cde.js";import"./useMobilePicker-9978caff.js";const Bo=()=>{var xr,gr,br,vr,Sr,Ar,Cr,Tr,yr,Nr,Ir,Fr,Er,qr,zr,Br,jr,Mr,Or,$r,Lr,Ur,kr,_r,Vr,Wr;const[F,Je]=f.useState(!1);f.useState(0);const[ae,mn]=f.useState(!0);f.useState({});const[z,pn]=f.useState([]),[b,Ae]=f.useState(0),[Ye,hn]=f.useState([]),[u,un]=f.useState(),[fn,Zt]=f.useState(!1);f.useState([]);const[xn,A]=f.useState(!1),[gn,N]=f.useState(!1),[Pt,bn]=f.useState(!1),[Qe,C]=f.useState(""),[vn,T]=f.useState(!1),[wt,I]=f.useState(!0),[Rt,E]=f.useState(!1),[Sn,B]=f.useState(!0),[An,Ce]=f.useState(!1),[Cn,Oe]=f.useState(!1),[Tn,$e]=f.useState(!1),[P,Te]=f.useState(""),[te,yn]=f.useState([]),[Nn,Ge]=f.useState(!1),[In,Ze]=f.useState(!1),[Fn,En]=f.useState([]);f.useState({});const[w,qn]=f.useState([]),[G,me]=f.useState(!0);f.useState(!0);const[oe,zn]=f.useState(""),[Bn,y]=f.useState(!1),[jn,Pe]=f.useState(""),[we,Le]=f.useState(""),[Re,Mn]=f.useState([]),[On,De]=f.useState(!1),[$n,pe]=f.useState(!0),[Dt,Ln]=f.useState(""),[se,Un]=f.useState([]),[kn,R]=f.useState(!1),W=It(),er=Ft(),_n=Et(),D=X(s=>s.appSettings);let he=X(s=>{var c;return(c=s.userManagement.entitiesAndActivities)==null?void 0:c["Cost Center"]});console.log("iwaAccessData",he);let p=X(s=>{var c;return(c=s==null?void 0:s.initialData)==null?void 0:c.IWMMyTask}),l=X(s=>s.userManagement.userData),r=_n.state,t=X(s=>s.userManagement.taskData);console.log(t,"taskRowDetailscost");const e=X(s=>s.edit.payload);let rr=X(s=>s.edit.payload),nr=X(s=>s.costCenter.requiredFields);console.log(nr,"required_field_for_data"),t!=null&&t.subject?t==null||t.subject:r==null||r.requestId,console.log("ccroewdata",new Date(`${u==null?void 0:u.ValidFrom} GMT-0000`).toUTCString()),console.log(p,"taskData in costCenter"),console.log("costCenterRowData",r),console.log("costCenterDetails",Ye),console.log(P,"Remarks");const Ue=X(s=>s.costCenter.costCenterViewData),Vn=X(s=>s.costCenter.requiredFields);console.log(Vn,"requiredFields");const Wn=()=>{De(!1)};let Hn="/Date("+1705506186e3+")/";console.log("createdOn",Hn);const[Kn,tr]=f.useState(!1);var $={TaskId:t!=null&&t.taskId?t==null?void 0:t.taskId:"",CostCenterHeaderID:u!=null&&u.costCenterHeaderId?u==null?void 0:u.costCenterHeaderId:"",ControllingArea:(gr=(xr=r==null?void 0:r.controllingAreaDataCopy)==null?void 0:xr.newControllingAreaCopyFrom)!=null&&gr.code?(vr=(br=r==null?void 0:r.controllingAreaDataCopy)==null?void 0:br.newControllingAreaCopyFrom)==null?void 0:vr.code:r!=null&&r.controllingArea?r==null?void 0:r.controllingArea:(Sr=p==null?void 0:p.body)==null?void 0:Sr.controllingArea,Testrun:$n,Action:(r==null?void 0:r.requestType)==="Create"?"I":(r==null?void 0:r.requestType)==="Change"?"U":(t==null?void 0:t.processDesc)==="Create"?"I":((t==null?void 0:t.processDesc)==="Change","U"),ReqCreatedBy:l==null?void 0:l.user_id,ReqCreatedOn:t!=null&&t.createdOn?"/Date("+(t==null?void 0:t.createdOn)+")/":r!=null&&r.createdOn?"/Date("+Date.parse(r==null?void 0:r.createdOn)+")/":"",RequestStatus:"",CreationId:(t==null?void 0:t.processDesc)==="Create"?t==null?void 0:t.subject.slice(3):(r==null?void 0:r.requestType)==="Create"?r==null?void 0:r.requestId.slice(3):"",EditId:(t==null?void 0:t.processDesc)==="Change"?t==null?void 0:t.subject.slice(3):(r==null?void 0:r.requestType)==="Change"?r==null?void 0:r.requestId.slice(3):"",DeleteId:"",MassCreationId:"",MassEditId:"",MassDeleteId:"",RequestType:(r==null?void 0:r.requestType)==="Create"?"Create":(r==null?void 0:r.requestType)==="Change"?"Change":(t==null?void 0:t.processDesc)==="Create"?"Create":((t==null?void 0:t.processDesc)==="Change","Change"),MassRequestStatus:"",Remarks:P||"",Toitem:[{CostCenterID:u!=null&&u.costCenterId?u==null?void 0:u.costCenterId:"",ValidFrom:u!=null&&u.validFrom?u==null?void 0:u.validFrom:"",ValidTo:u!=null&&u.validTo?u==null?void 0:u.validTo:"",Costcenter:r!=null&&r.costCenter?r==null?void 0:r.costCenter:(Ar=p==null?void 0:p.body)==null?void 0:Ar.costCenter,PersonInCharge:e!=null&&e.PersonResponsible?e==null?void 0:e.PersonResponsible:"",CostcenterType:e!=null&&e.CostCenterCategory?e==null?void 0:e.CostCenterCategory:"",CostctrHierGrp:"TUK1-PRODU",BusArea:e!=null&&e.BusinessArea?e==null?void 0:e.BusinessArea:"",CompCode:e!=null&&e.CompanyCode?e==null?void 0:e.CompanyCode:"TUK1",Currency:e!=null&&e.Currency?e==null?void 0:e.Currency:"",ProfitCtr:e!=null&&e.ProfitCenter?e==null?void 0:e.ProfitCenter:"",Name:e!=null&&e.Name?e==null?void 0:e.Name:"",Descript:e!=null&&e.Description?e==null?void 0:e.Description:"",PersonInChargeUser:e!=null&&e.UserResponsible?e==null?void 0:e.UserResponsible:"",RecordQuantity:(e==null?void 0:e.RecordQuantity)===!0?"X":"",LockIndActualPrimaryCosts:(e==null?void 0:e.ActualPrimaryCosts)===!0?"X":"",LockIndPlanPrimaryCosts:(e==null?void 0:e.PlanPrimaryCosts)===!0?"X":"",LockIndActSecondaryCosts:(e==null?void 0:e.ActsecondaryCosts)===!0?"X":"",LockIndPlanSecondaryCosts:(e==null?void 0:e.PlanSecondaryCosts)===!0?"X":"",LockIndActualRevenues:(e==null?void 0:e.ActualRevenue)===!0?"X":"",LockIndPlanRevenues:(e==null?void 0:e.PlanRevenue)===!0?"X":"",LockIndCommitmentUpdate:(e==null?void 0:e.CommitmentUpdate)===!0?"X":"",ConditionTableUsage:"",Application:"",CstgSheet:e!=null&&e.CostingSheet?e==null?void 0:e.CostingSheet:"",ActyIndepTemplate:e!=null&&e.ActyIndepFromPlngTemp?e==null?void 0:e.ActyIndepFromPlngTemp:"",ActyDepTemplate:e!=null&&e.ActyDepFromPlngTemp?e==null?void 0:e.ActyDepFromPlngTemp:"",AddrTitle:e!=null&&e.Title?e==null?void 0:e.Title:"",AddrName1:e!=null&&e.Name1?e==null?void 0:e.Name1:"",AddrName2:e!=null&&e.Name2?e==null?void 0:e.Name2:"",AddrName3:e!=null&&e.Name3?e==null?void 0:e.Name3:"",AddrName4:e!=null&&e.Name4?e==null?void 0:e.Name4:"",AddrStreet:e!=null&&e.Street?e==null?void 0:e.Street:"",AddrCity:e!=null&&e.Location?e==null?void 0:e.Location:"",AddrDistrict:e!=null&&e.District?e==null?void 0:e.District:"",AddrCountry:e!=null&&e.CountryReg?e==null?void 0:e.CountryReg:"",AddrCountryIso:"",AddrTaxjurcode:e!=null&&e.Jurisdiction?e==null?void 0:e.Jurisdiction:"",AddrPoBox:e!=null&&e.POBox?e==null?void 0:e.POBox:"",AddrPostlCode:e!=null&&e.PostalCode?e==null?void 0:e.PostalCode:"",AddrPobxPcd:e!=null&&e.POBoxPostCod?e==null?void 0:e.POBoxPostCod:"",AddrRegion:e!=null&&e.Region?e==null?void 0:e.Region:"",TelcoLangu:"",TelcoLanguIso:e!=null&&e.LanguageKey?e==null?void 0:e.LanguageKey:"",TelcoTelephone:e!=null&&e.Telephone1?e==null?void 0:e.Telephone1:"",TelcoTelephone2:e!=null&&e.Telephone2?e==null?void 0:e.Telephone2:"",TelcoTelebox:e!=null&&e.TeleboxNumber?e==null?void 0:e.TeleboxNumber:"",TelcoTelex:e!=null&&e.TelexNumber?e==null?void 0:e.TelexNumber:"",TelcoFaxNumber:e!=null&&e.FaxNumber?e==null?void 0:e.FaxNumber:"",TelcoTeletex:e!=null&&e.TeletexNumber?e==null?void 0:e.TeletexNumber:"",TelcoPrinter:e!=null&&e.PrinterDestination?e==null?void 0:e.PrinterDestination:"",TelcoDataLine:e!=null&&e.DataLine?e==null?void 0:e.DataLine:"",ActyDepTemplateAllocCc:e!=null&&e.ActyDepAllocTemp?e==null?void 0:e.ActyDepAllocTemp:"",ActyDepTemplateSk:e!=null&&e.TempActStatKeyFigure?e==null?void 0:e.TempActStatKeyFigure:"",ActyIndepTemplateAllocCc:e!=null&&e.ActyIndepAllocTemp?e==null?void 0:e.ActyIndepAllocTemp:"",ActyIndepTemplateSk:e!=null&&e.TempActStatKeyFigure?e==null?void 0:e.TempActStatKeyFigure:"",AvcActive:!1,AvcProfile:"",BudgetCarryingCostCtr:"",CurrencyIso:"",Department:e!=null&&e.Department?e==null?void 0:e.Department:"",FuncArea:e!=null&&e.FunctionalArea?e==null?void 0:e.FunctionalArea:"",FuncAreaFixAssigned:"",FuncAreaLong:"",Fund:"",FundFixAssigned:"",GrantFixAssigned:"",GrantId:"",JvEquityTyp:"",JvJibcl:"",JvJibsa:"",JvOtype:"",JvRecInd:"",JvVenture:"",Logsystem:""}]};console.log("errorsFields",rr,Fn.error_field_arr);const or=()=>kt(rr,nr,Mn),Xn=s=>{let c=[];for(const o in s){if(s.hasOwnProperty(o)){for(const i in s[o])if(s[o].hasOwnProperty(i)){console.log(s[o][i],"viewData[section][fieldGroup]");const m=s[o][i];for(const S of m)if(S.visibility==="0"||S.visibility==="Required"){console.log(S.fieldName,"field.fieldName");let g=S.fieldName.replace(/\s/g,"");c.push(g)}}}En(i=>({...i,error_field_arr:c}))}};console.log("taskData",p),console.log("taskRowDetails",t);const Jn=()=>{z[b];let s=Object.entries(Ue);console.log("viewDataArray",s);const c={};s.map(o=>{console.log("bottle",o[1]);let i=Object.entries(o[1]);return console.log("notebook",i),i.forEach(m=>{m[1].forEach(S=>{c[S.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("")]=S.value})}),o}),console.log("toSetArray",c),W(Ut(c))},Yn=()=>{var i,m,S,g,ne,ue,fe,xe,Hr,Kr,Xr,Jr;var s=(i=p==null?void 0:p.body)!=null&&i.id?{id:(m=p==null?void 0:p.body)!=null&&m.id?(S=p==null?void 0:p.body)==null?void 0:S.id:"",costCenter:(g=p==null?void 0:p.body)!=null&&g.costCenter?(ne=p==null?void 0:p.body)==null?void 0:ne.costCenter:"",controllingArea:(ue=p==null?void 0:p.body)==null?void 0:ue.controllingArea,reqStatus:(fe=p==null?void 0:p.body)==null?void 0:fe.reqStatus,screenName:(t==null?void 0:t.processDesc)==="Create"?"Create":"Change"}:{id:r!=null&&r.reqStatus?r==null?void 0:r.id:"",costCenter:r!=null&&r.costCenter?(xe=r==null?void 0:r.costCenter)==null?void 0:xe.split(" ")[0]:"",controllingArea:(Kr=(Hr=r==null?void 0:r.controllingAreaDataCopy)==null?void 0:Hr.newControllingAreaCopyFrom)!=null&&Kr.code?(Jr=(Xr=r==null?void 0:r.controllingAreaDataCopy)==null?void 0:Xr.newControllingAreaCopyFrom)==null?void 0:Jr.code:r!=null&&r.controllingArea?r==null?void 0:r.controllingArea:"",reqStatus:r!=null&&r.reqStatus?r==null?void 0:r.reqStatus:"Approved",screenName:r!=null&&r.requestType?r==null?void 0:r.requestType:"Change"};const c=ie=>{var Yr;if(ie.statusCode===200){const Q=ie.body.viewData;W(_t(Q));const yt=ie.body;console.log("ccdata",ie.body),Rn(Q["Basic Data"]["Basic Data"].find(K=>(K==null?void 0:K.fieldName)==="Company Code").value),Un(st((Yr=Q==null?void 0:Q["Basic Data"])==null?void 0:Yr.Names,"Name")),Dn(Q.Address["Address Data"].find(K=>(K==null?void 0:K.fieldName)==="Country/Reg").value);const Qr=Object.keys(Q);pn(Qr);const Nt=["Attachment & Documents"];z.concat(Nt);const Gr=Qr.map(K=>({category:K,data:Q[K],setIsEditMode:Je}));hn(Gr),un(yt),console.log("mappedData",Gr,u),Xn(Q)}else bn(!0),N(!1),A("Error"),C("Unable to fetch data of Cost Center"),Le("danger"),T("danger"),I(!0),Ce(!0)},o=ie=>{console.log(ie)};q(`/${j}/data/displayCostCenter`,"post",c,o,s),Ln(s.screenName)},[sr,Qn]=f.useState(0),Gn=(s,c)=>{const o=m=>{W(de({keyName:s,data:m.body})),Qn(S=>S+1)},i=m=>{console.log(m)};q(`/${j}/data/${c}`,"get",o,i)},Zn=()=>{var s,c;(c=(s=rn)==null?void 0:s.costCenter)==null||c.map(o=>{Gn(o==null?void 0:o.keyName,o==null?void 0:o.endPoint)})},Pn=()=>{var s,c;sr==((c=(s=rn)==null?void 0:s.costCenter)==null?void 0:c.length)?B(!1):B(!0)};f.useEffect(()=>{Pn()},[sr]),f.useEffect(()=>{zn(qt("CC"))},[]),f.useEffect(()=>{Zn(),Yn(),et(),nt(),tt()},[]),f.useEffect(()=>{Ue.length!==0&&Jn()},[Ue]);const ir=()=>{Ce(!1)},wn=()=>{Tn?(Oe(!1),$e(!1)):(Oe(!1),er("/masterDataCockpit/costCenter"))},L=()=>{me(!0);const s=or();F?s?(Ae(c=>c-1),W(je())):pr():(Ae(c=>c-1),W(je()))},U=()=>{const s=or();F?s?(Ae(c=>c+1),W(je())):pr():(Ae(c=>c+1),W(je()))},Rn=s=>{console.log("compcode",s);const c=i=>{console.log("value",i),W(de({keyName:"Currency",data:i.body}))},o=i=>{console.log(i,"error in dojax")};q(`/${j}/data/getCurrency?companyCode=${s}`,"get",c,o)},Dn=s=>{console.log("compcode",s);const c=i=>{console.log("value",i),W(de({keyName:"Region",data:i.body}))},o=i=>{console.log(i,"error in dojax")};q(`/${j}/data/getRegionBasedOnCountry?country=${s}`,"get",c,o)},et=()=>{var o,i;const s=m=>{W(de({keyName:"HierarchyArea",data:m.body}))},c=m=>{console.log(m)};q(`/${j}/data/getHierarchyArea?controllingArea=${(o=p==null?void 0:p.body)!=null&&o.controllingArea?(i=p==null?void 0:p.body)==null?void 0:i.controllingArea:r==null?void 0:r.controllingArea}`,"get",s,c)},rt=s=>{tr(s)},nt=()=>{var o,i;const s=m=>{W(de({keyName:"CompanyCode",data:m.body}))},c=m=>{console.log(m)};q(`/${j}/data/getCompanyCodeBasedOnControllingArea?controllingArea=${(o=p==null?void 0:p.body)!=null&&o.controllingArea?(i=p==null?void 0:p.body)==null?void 0:i.controllingArea:r==null?void 0:r.controllingArea}`,"get",s,c)},tt=()=>{var o,i;const s=m=>{W(de({keyName:"ProfitCenter",data:m.body}))},c=m=>{console.log(m)};q(`/${j}/data/getProfitCenterAsPerControllingArea?controllingArea=${(o=p==null?void 0:p.body)!=null&&o.controllingArea?(i=p==null?void 0:p.body)==null?void 0:i.controllingArea:r==null?void 0:r.controllingArea}`,"get",s,c)},ye=()=>{Je(!0),mn(!1)},lr=()=>{B(!0),Y(),ht()},cr=()=>{B(!0),Y(),ut()},Ne=()=>{B(!0),pe(!1),Y(),ft()},dr=()=>{B(!0),pe(!1),Y(),xt()},ke=()=>{bt()},ot=()=>{gt()},ar=()=>{B(!0),Y(),St()},mr=()=>{B(!0),Y(),At()},M=()=>{Ce(!0)},k=()=>{Oe(!0)},Ie=()=>{R(!0);const s=o=>{var i,m,S;R(!1),o.statusCode===201?(R(!1),A("Create"),A("Create"),C("All Data has been Validated. Cost Center can be Sent for Review"),y(!1),T("success"),I(!1),N(!0),k(),E(!0),$e(!0),me(!1)):(R(!1),A("Error"),N(!1),C(`${(i=o==null?void 0:o.body)!=null&&i.message[0]?(m=o==null?void 0:o.body)==null?void 0:m.message[0]:(S=o==null?void 0:o.body)==null?void 0:S.value}`),y(!1),T("danger"),I(!1),E(!0),M())},c=o=>{console.log(o)};q(`/${j}/alter/validateCostCenter`,"post",s,c,$)},st=(s,c)=>{console.log("getvalueforfieldname",s,c);const o=s==null?void 0:s.find(i=>(i==null?void 0:i.fieldName)===c);return o?o.value:""},Fe=()=>{var S;R(!0);const s={coArea:r!=null&&r.controllingArea?r==null?void 0:r.controllingArea:u!=null&&u.controllingArea?u==null?void 0:u.controllingArea:"",name:e!=null&&e.Name?(S=e==null?void 0:e.Name)==null?void 0:S.toUpperCase():""},c=g=>{var ne,ue,fe,xe;R(!1),g.statusCode===201?(A("Create"),A("Create"),C("All Data has been Validated. Cost Center can be Sent for Review"),y(!1),T("success"),I(!1),N(!0),k(),E(!0),$e(!0),console.log(se==null?void 0:se.toUpperCase(),"costcenterNameinRow"),(s.coArea!==""||s.name!=="")&&(me(!1),((ne=s==null?void 0:s.name)==null?void 0:ne.toUpperCase())===(se==null?void 0:se.toUpperCase())?R(!1):q(`/${j}/alter/fetchCCDescriptionDupliChk`,"post",o,i,s))):(A("Error"),N(!1),C(`${(ue=g==null?void 0:g.body)!=null&&ue.message[0]?(fe=g==null?void 0:g.body)==null?void 0:fe.message[0]:(xe=g==null?void 0:g.body)==null?void 0:xe.value}`),y(!1),T("danger"),I(!1),E(!0),M())},o=g=>{g.body.length===0||!g.body.some(ne=>ne.toUpperCase()===s.name)||(A("Duplicate Check"),N(!1),C("There is a direct match for the Cost Center name. Please change the name."),y(!1),T("danger"),I(!1),E(!0),M()),me(!1)},i=g=>{console.log(g)},m=g=>{console.log(g)};q(`/${j}/alter/validateCostCenter`,"post",c,m,$)},pr=()=>{De(!0)},it=()=>{(l==null?void 0:l.role)==="MDM Steward"&&((r==null?void 0:r.requestType)==="Create"||(t==null?void 0:t.processDesc)==="Create")?(B(!0),lt()):(l==null?void 0:l.role)==="MDM Steward"&&((r==null?void 0:r.requestType)==="Change"||(t==null?void 0:t.processDesc)==="Change")?(B(!0),ct()):(l==null?void 0:l.role)==="Approver"&&((r==null?void 0:r.requestType)==="Create"||(t==null?void 0:t.processDesc)==="Create")?(B(!0),dt()):(l==null?void 0:l.role)==="Approver"&&((r==null?void 0:r.requestType)==="Change"||(t==null?void 0:t.processDesc)==="Change")&&(B(!0),at())},lt=()=>{const s=o=>{B(!1),o.statusCode===200?(console.log("success"),A("Create"),C(`Cost Center Submitted for Correction with ID NCS${o.body}`),y(!1),T("success"),I(!1),N(!0),k(),E(!0)):(A("Error"),N(!1),C("Failed Submitting Cost Center for Correction"),y(!1),T("danger"),I(!1),E(!0),M()),ee()},c=o=>{console.log(o)};console.log("remarkssssssssss",P),q(`/${j}/alter/costCenterSendForCorrection`,"post",s,c,$)},ct=()=>{const s=o=>{B(!1),o.statusCode===200?(console.log("success"),A("Create"),C(`Cost Center Submitted for Correction with ID CCS${o.body}`),y(!1),T("success"),I(!1),N(!0),k(),E(!0)):(A("Error"),N(!1),C("Failed Submitting Cost Center for Correction"),y(!1),T("danger"),I(!1),E(!0),M()),ee()},c=o=>{console.log(o)};console.log("hsdfjgdh",$),q(`/${j}/alter/changeCostCenterSendForCorrection`,"post",s,c,$)},dt=()=>{const s=o=>{B(!1),o.statusCode===200?(console.log("success"),A("Create"),C(`Cost Center Submitted for Correction with ID NCS${o.body}`),y(!1),T("success"),I(!1),N(!0),k(),E(!0)):(A("Error"),N(!1),C("Failed Submitting Cost Center for Correction"),y(!1),T("danger"),I(!1),E(!0),M()),ee()},c=o=>{console.log(o)};q(`/${j}/alter/costCenterSendForReview`,"post",s,c,$)},at=()=>{const s=o=>{B(!1),o.statusCode===200?(console.log("success"),A("Create"),C(`Cost Center Submitted for Correction with ID CCS${o.body}`),y(!1),T("success"),I(!1),N(!0),k(),E(!0)):(A("Error"),N(!1),C("Failed Submitting Cost Center for Correction"),y(!1),T("danger"),I(!1),E(!0),M()),ee()},c=o=>{console.log(o)};console.log("remarksssaaaa",P),q(`/${j}/alter/changeCostCenterSendForReview`,"post",s,c,$)},hr=[{field:"id",headerName:"Document ID",flex:1,hide:!0},{field:"docType",headerName:"Type",flex:1},{field:"docName",headerName:"Document Name",flex:1},{field:"uploadedOn",headerName:"Uploaded On",flex:1,align:"center",headerAlign:"center"},{field:"uploadedBy",headerName:"Uploaded By",sortable:!1,flex:1},{field:"action",headerName:"Action",sortable:!1,filterable:!1,align:"center",headerAlign:"center",flex:1,renderCell:s=>d(H,{children:[n(Xt,{index:s.row.id,name:s.row.docName}),n(Jt,{index:s.row.id,name:s.row.docName})]})}];console.log("statecomment",w);const mt=()=>{let s=t!=null&&t.subject?t==null?void 0:t.subject:r==null?void 0:r.requestId,c=o=>{var i=[];o.documentDetailDtoList.forEach(m=>{var S={id:m.documentId,docType:m.fileType,docName:m.fileName,uploadedOn:ve(m.docCreationDate).format(D.date),uploadedBy:m.createdBy};i.push(S)}),yn(i)};q(`/${ce}/documentManagement/getDocByRequestId/${s}`,"get",c)},pt=()=>{let s=t!=null&&t.subject?t==null?void 0:t.subject:r==null?void 0:r.requestId,c=i=>{console.log("commentsdata",i);var m=[];i.body.forEach(S=>{var g={id:S.requestId,comment:S.comment,user:S.createdByUser,createdAt:S.updatedAt};m.push(g)}),qn(m),console.log("commentrows",m)},o=i=>{console.log(i)};q(`/${j}/activitylog/fetchTaskDetailsForRequestId?requestId=${s}`,"get",c,o)};f.useEffect(()=>{mt(),pt()},[]);const ur=z.map(s=>{const c=Ye.filter(o=>{var i;return((i=o.category)==null?void 0:i.split(" ")[0])==(s==null?void 0:s.split(" ")[0])});if(c.length!=0)return{category:s==null?void 0:s.split(" ")[0],data:c[0].data}}).map((s,c)=>{if((s==null?void 0:s.category)=="Basic"&&b==0)return[n(h,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(s.data).map(o=>d(h,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ge},children:[n(v,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),n(J,{sx:{width:"100%"},children:n(be,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:n(h,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:s.data[o].map(i=>(console.log("fieldDatatttt",i),n(Se,{length:i.maxLength,label:i.fieldName,data:e,value:i.value,visibility:i.visibility,onSave:m=>handleFieldSave(i.fieldName,m),isEditMode:F,type:i.fieldType,field:i,taskRequestId:r==null?void 0:r.requestId})))})})})]},o))},s.category)];if((s==null?void 0:s.category)=="Control"&&b==1)return[n(h,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(s.data).map(o=>d(h,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ge},children:[n(v,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),n(J,{sx:{width:"100%"},children:n(be,{children:n(h,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:s.data[o].map(i=>n(Se,{data:e,label:i.fieldName,value:(i==null?void 0:i.value)==="X",onSave:m=>handleFieldSave(i.fieldName,m),visibility:i.visibility,isEditMode:F,type:i.fieldType,taskRequestId:r==null?void 0:r.requestId},i.fieldName))})})})]},o))},s.category)];if((s==null?void 0:s.category)=="Templates"&&b==2)return[n(h,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(s.data).map(o=>d(h,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ge},children:[n(v,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),n(J,{sx:{width:"100%"},children:n(be,{children:n(h,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:s.data[o].map(i=>n(Se,{label:i.fieldName,data:e,value:i.value,onSave:m=>handleFieldSave(i.fieldName,m),isEditMode:F,visibility:i.visibility,type:i.fieldType,taskRequestId:r==null?void 0:r.requestId},i.fieldName))})})})]},o))},s.category)];if((s==null?void 0:s.category)=="Address"&&b==3)return[n(h,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(s.data).map(o=>d(h,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ge},children:[n(v,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),n(J,{sx:{width:"100%"},children:n(be,{children:n(h,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:s.data[o].map(i=>n(Se,{label:i.fieldName,data:e,value:i.value,onSave:m=>handleFieldSave(i.fieldName,m),isEditMode:F,type:i.fieldType,visibility:i.visibility,taskRequestId:r==null?void 0:r.requestId},i.fieldName))})})})]},o))},s.category)];if((s==null?void 0:s.category)=="Communication"&&b==4)return[n(h,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(s.data).map(o=>d(h,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ge},children:[n(v,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:o}),n(J,{sx:{width:"100%"},children:n(be,{children:n(h,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:s.data[o].map(i=>n(Se,{label:i.fieldName,data:e,value:i.value,onSave:m=>handleFieldSave(i.fieldName,m),isEditMode:F,visibility:i.visibility,type:i.fieldType,taskRequestId:r==null?void 0:r.requestId},i.fieldName))})})})]},o))},s.category)];if((s==null?void 0:s.category)=="Attachments"&&b==5)return[n(H,{children:F?d(H,{children:[n(Kt,{title:"CostCenter",useMetaData:!1,artifactId:oe,artifactName:"CostCenter"}),d(qe,{sx:{padding:"1rem 1rem 0rem 1rem"},children:[n(h,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:n(v,{variant:"h6",children:n("strong",{children:"Attachments"})})}),!!te.length&&n(Zr,{width:"100%",rows:te,columns:hr,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action"}),!te.length&&n(v,{variant:"body2",children:"No Attachments Found"}),n("br",{}),n(v,{variant:"h6",children:"Comments"}),!!w.length&&n(nn,{sx:{[`& .${tn.root}:before`]:{flex:0,padding:0}},children:w.map(o=>d(on,{children:[d(sn,{children:[n(ln,{children:n(Pr,{sx:{color:"#757575"}})}),n(cn,{})]}),n(dn,{sx:{py:"12px",px:2},children:n(qe,{elevation:0,sx:{border:1,borderColor:"#C4C4C4",borderRadius:"8px",width:"650px"},children:n(J,{sx:{padding:"1rem"},children:d(Z,{spacing:1,children:[n(h,{sx:{display:"flex",justifyContent:"space-between"},children:n(v,{sx:{textAlign:"right",color:" #757575",fontWeight:"500",fontSize:"12px"},children:ve(o.createdAt).format("DD MMM YYYY")})}),n(v,{sx:{fontSize:"12px",color:" #757575",fontWeight:"500"},children:o.user}),n(v,{sx:{fontSize:"12px",color:"#1D1D1D",fontWeight:"600"},children:o.comment})]})})})})]}))}),!w.length&&n(v,{variant:"body2",children:"No Comments Found"}),n("br",{})]})]}):d(qe,{sx:{padding:"1rem 1rem 0rem 1rem"},children:[n(h,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:n(v,{variant:"h6",children:n("strong",{children:"Attachments"})})}),!!te.length&&n(Zr,{width:"100%",rows:te,columns:hr,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action"}),!te.length&&n(v,{variant:"body2",children:"No Attachments Found"}),n("br",{}),n(v,{variant:"h6",children:"Comments"}),!!w.length&&n(nn,{sx:{[`& .${tn.root}:before`]:{flex:0,padding:0}},children:w.map(o=>d(on,{children:[d(sn,{children:[n(ln,{children:n(Pr,{sx:{color:"#757575"}})}),n(cn,{})]}),n(dn,{sx:{py:"12px",px:2},children:n(qe,{elevation:0,sx:{border:1,borderColor:"#C4C4C4",borderRadius:"8px",width:"650px"},children:n(J,{sx:{padding:"1rem"},children:d(Z,{spacing:1,children:[n(h,{sx:{display:"flex",justifyContent:"space-between"},children:n(v,{sx:{textAlign:"right",color:" #757575",fontWeight:"500",fontSize:"12px"},children:ve(o.createdAt).format("DD MMM YYYY")})}),n(v,{sx:{fontSize:"12px",color:" #757575",fontWeight:"500"},children:o.user}),n(v,{sx:{fontSize:"12px",color:"#1D1D1D",fontWeight:"600"},children:o.comment})]})})})})]}))}),!w.length&&n(v,{variant:"body2",children:"No Comments Found"}),n("br",{})]})})]}),ht=()=>{const s=o=>{if(B(!1),o.statusCode===200){console.log("success"),A("Create"),C(`Cost Center Submitted for Review with ID CCS${o.body} `),y(!1),T("success"),I(!1),N(!0),k(),E(!0);const i={artifactId:oe,createdBy:l==null?void 0:l.emailId,artifactType:"CostCenter",requestId:`CCS${o==null?void 0:o.body}`},m=g=>{console.log("Second API success",g)},S=g=>{console.error("Second API error",g)};q(`/${ce}/documentManagement/updateDocRequestId`,"post",m,S,i)}else A("Error"),N(!1),C("Failed Submitting Profit Center"),y(!1),T("danger"),I(!1),E(!0),M();handleClose()},c=o=>{console.log(o)};q(`/${j}/alter/changeCostCenterApprovalSubmit`,"post",s,c,$)},ut=()=>{const s=o=>{B(!1),o.statusCode===200?(console.log("success"),A("Create"),C(`Cost Center Submitted for Approval with ID NCS${o.body}`),y(!1),T("success"),I(!1),N(!0),k(),E(!0)):(A("Error"),N(!1),C("Failed Submitting Cost Center"),y(!1),T("danger"),I(!1),E(!0),M()),handleClose()},c=o=>{console.log(o)};q(`/${j}/alter/costCenterApprovalSubmit`,"post",s,c,$)},ft=()=>{const s=o=>{if(B(!1),o.statusCode===200){console.log("success"),A("Create"),C(`Cost Center Submitted For Review with ID CCS${o.body} `),y(!1),T("success"),I(!1),N(!0),k(),E(!0);const i={artifactId:oe,createdBy:l==null?void 0:l.emailId,artifactType:"CostCenter",requestId:`CCS${o==null?void 0:o.body}`},m=g=>{console.log("Second API success",g)},S=g=>{console.error("Second API error",g)};q(`/${ce}/documentManagement/updateDocRequestId`,"post",m,S,i)}else A("Error"),N(!1),C("Failed Submitting Cost Center"),y(!1),T("danger"),I(!1),E(!0),M();handleClose()},c=o=>{console.log(o)};q(`/${j}/alter/changeCostCenterSubmitForReview`,"post",s,c,$)},xt=()=>{const s=o=>{if(B(!1),o.statusCode===200){console.log("success"),A("Create"),C(`Cost Center Submitted for Review with ID NCS${o.body} `),y(!1),T("success"),I(!1),N(!0),k(),E(!0);const i={artifactId:oe,createdBy:l==null?void 0:l.emailId,artifactType:"CostCenter",requestId:`NCS${o==null?void 0:o.body}`},m=g=>{console.log("Second API success",g)},S=g=>{console.error("Second API error",g)};q(`/${ce}/documentManagement/updateDocRequestId`,"post",m,S,i)}else A("Error"),N(!1),C("Failed Saving the Data"),y(!1),T("danger"),I(!1),E(!0),M(),pe(!0);handleClose()},c=o=>{console.log(o)};q(`/${j}/alter/costCenterSubmitForReview`,"post",s,c,$)},gt=()=>{T(!1),M(),A("Confirm"),C("Do You Want to Save as Draft ?"),y(!0),Pe("proceed"),Le("Create")},bt=()=>{T(!1),M(),A("Confirm"),C("Do You Want to Save as Draft?"),y(!0),Pe("proceed"),Le("Change")},vt=()=>{if(console.log(we,"dialogType"),_e(),B(!0),we==="Change"){const s=o=>{if(B(!1),o.statusCode===200){console.log("success"),A("Create"),C(`Cost Center Saved As Draft with ID CCS${o.body} `),y(!1),T("success"),I(!1),N(!0),k(),E(!0);const i={artifactId:oe,createdBy:l==null?void 0:l.emailId,artifactType:"CostCenter",requestId:`CCS${o==null?void 0:o.body}`},m=g=>{console.log("Second API success",g)},S=g=>{console.error("Second API error",g)};q(`/${ce}/documentManagement/updateDocRequestId`,"post",m,S,i)}else A("Error"),N(!1),C("Failed Saving Cost Center"),y(!1),T("danger"),I(!1),E(!0),M();handleClose()},c=o=>{console.log(o)};q(`/${j}/alter/changeCostCenterAsDraft`,"post",s,c,$)}else{const s=o=>{if(_e(),B(!1),o.statusCode===200){console.log("success"),A("Create"),C(`Cost Center Saved As Draft with ID NCS${o.body} `),y(!1),T("success"),I(!1),N(!0),k(),E(!0);const i={artifactId:oe,createdBy:l==null?void 0:l.emailId,artifactType:"CostCenter",requestId:`NCS${o==null?void 0:o.body}`},m=g=>{console.log("Second API success",g)},S=g=>{console.error("Second API error",g)};q(`/${ce}/documentManagement/updateDocRequestId`,"post",m,S,i)}else A("Error"),N(!1),C("Failed Saving Cost Center"),y(!1),T("danger"),I(!1),E(!0),M();handleClose()},c=o=>{console.log(o)};q(`/${j}/alter/costCenterAsDraft`,"post",s,c,$)}},St=()=>{const s=o=>{B(!1),o.statusCode===201?(console.log("success"),A("Create"),C(`${o.message}`),y(!1),T("success"),I(!1),N(!0),k(),E(!0)):(A("Error"),N(!1),C("Failed Approving Cost Center"),y(!1),T("danger"),I(!1),E(!0),M()),handleClose()},c=o=>{console.log(o)};q(`/${j}/alter/changeCostCenterApproved`,"post",s,c,$)},At=()=>{const s=o=>{B(!1),o.statusCode===201?(console.log("success"),A("Create"),C(`${o.message}`),y(!1),T("success"),I(!1),N(!0),k(),E(!0)):(A("Error"),N(!1),C("Failed Approving the Cost Center"),y(!1),T("danger"),I(!1),E(!0),M()),handleClose()},c=o=>{console.log(o)};q(`/${j}/alter/createCostCenterApproved`,"post",s,c,$)},Ee=()=>{Ge(!0)},ee=()=>{Te(""),Ge(!1)},fr=(s,c)=>{const o=s.target.value;if(o.length>0&&o[0]===" ")Te(o.trimStart());else{let i=o.toUpperCase();Te(i)}},re=()=>{pe(!1),Ze(!0)},Y=()=>{Te(""),pe(!0),Ze(!1)},Ct=()=>{(l==null?void 0:l.role)==="Finance"&&((r==null?void 0:r.requestType)==="Create"||(t==null?void 0:t.processDesc)==="Create")&&F?dr():(l==null?void 0:l.role)==="MDM Steward"&&((r==null?void 0:r.requestType)==="Create"||(t==null?void 0:t.processDesc)==="Create")&&!F?cr():(l==null?void 0:l.role)==="Approver"&&((r==null?void 0:r.requestType)==="Create"||(t==null?void 0:t.processDesc)==="Create")&&!F?mr():(l==null?void 0:l.role)==="Finance"&&!(r!=null&&r.requestType)&&F||(l==null?void 0:l.role)==="Finance"&&((r==null?void 0:r.requestType)==="Change"||(t==null?void 0:t.processDesc)==="Change")&&F?Ne():(l==null?void 0:l.role)==="MDM Steward"&&((r==null?void 0:r.requestType)==="Change"||(t==null?void 0:t.processDesc)==="Change")&&!F?lr():(l==null?void 0:l.role)==="Approver"&&((r==null?void 0:r.requestType)==="Change"||(t==null?void 0:t.processDesc)==="Change")&&!F&&ar()},Tt=()=>{tr(!0)},_e=()=>{Ce(!1)};return console.log("factorsarray",z),n(H,{children:Sn===!0?n(zt,{}):d("div",{style:{backgroundColor:"#FAFCFF"},children:[n(Bt,{dialogState:An,openReusableDialog:M,closeReusableDialog:ir,dialogTitle:xn,dialogMessage:Qe,handleDialogConfirm:ir,dialogOkText:"OK",showExtraButton:Bn,showCancelButton:!0,dialogSeverity:vn,handleDialogReject:_e,handleExtraText:jn,handleExtraButton:vt}),gn&&n(wr,{openSnackBar:Cn,alertMsg:Qe,handleSnackBarClose:wn}),Re.length!=0&&n(wr,{openSnackBar:On,alertMsg:"Please fill the following Field: "+Re.join(", "),handleSnackBarClose:Wn}),d(Ve,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:Nn,onClose:ee,children:[d(We,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[n(v,{variant:"h6",children:"Remarks"}),n(He,{sx:{width:"max-content"},onClick:ee,children:n(Rr,{})})]}),n(Ke,{sx:{padding:".5rem 1rem"},children:n(Z,{children:n(J,{sx:{minWidth:400},children:n(le,{sx:{height:"auto"},fullWidth:!0,children:n(ze,{sx:{backgroundColor:"#F5F5F5"},onChange:fr,value:P,multiline:!0,placeholder:"Enter Remarks for Correction",inputProps:{maxLength:254}})})})})}),d(Xe,{sx:{display:"flex",justifyContent:"end"},children:[n(a,{sx:{width:"max-content",textTransform:"capitalize"},onClick:ee,children:"Cancel"}),n(a,{className:"button_primary--normal",type:"save",onClick:it,variant:"contained",children:"Submit"})]})]}),n(jt,{sx:{color:"#fff",zIndex:s=>s.zIndex.drawer+1},open:kn,children:n(Mt,{color:"inherit"})}),d(Ve,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:In,onClose:Y,children:[d(We,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[n(v,{variant:"h6",children:"Remarks"}),n(He,{sx:{width:"max-content"},onClick:Y,children:n(Rr,{})})]}),n(Ke,{sx:{padding:".5rem 1rem"},children:n(Z,{children:n(J,{sx:{minWidth:400},children:n(le,{sx:{height:"auto"},fullWidth:!0,children:n(ze,{sx:{backgroundColor:"#F5F5F5"},value:P,onChange:fr,multiline:!0,placeholder:"Enter Remarks",inputProps:{maxLength:254}})})})})}),d(Xe,{sx:{display:"flex",justifyContent:"end"},children:[n(a,{sx:{width:"max-content",textTransform:"capitalize"},onClick:Y,children:"Cancel"}),n(a,{className:"button_primary--normal",type:"save",onClick:Ct,variant:"contained",children:"Submit"})]})]}),d(h,{container:!0,sx:Ot,children:[d(h,{item:!0,md:12,style:{padding:"16px",display:"flex"},children:[d(h,{md:9,sx:{display:"flex"},children:[n(h,{children:n(He,{color:"primary","aria-label":"upload picture",component:"label",sx:$t,children:n(Ht,{sx:{fontSize:"25px",color:"#000000"},onClick:()=>{er(-1)}})})}),d(h,{children:[F?d(h,{item:!0,md:12,children:[n(v,{variant:"h3",children:n("strong",{children:"Change Cost Center: "})}),n(v,{variant:"body2",color:"#777",children:"This view edits the details of the Cost Center"})]}):"",ae?d(h,{item:!0,md:12,children:[n(v,{variant:"h3",children:n("strong",{children:"Display Cost Center "})}),n(v,{variant:"body2",color:"#777",children:"This view displays the details of the Cost Center"})]}):""]})]}),d(h,{md:3,sx:{display:"flex",justifyContent:"flex-end"},gap:2,children:[r!=null&&r.requestId||t!=null&&t.processDesc?n(h,{children:n(a,{variant:"outlined",size:"small",sx:O,onClick:Tt,title:"Change Log",children:n(Vt,{sx:{padding:"2px"},fontSize:"small"})})}):"",Kn&&n(Wt,{open:!0,closeModal:rt,requestId:r!=null&&r.requestId?r==null?void 0:r.requestId:t==null?void 0:t.subject,requestType:r!=null&&r.requestType?r==null?void 0:r.requestType:(Cr=p==null?void 0:p.body)==null?void 0:Cr.processDesc,pageName:"costCenter",controllingArea:r!=null&&r.controllingArea?r==null?void 0:r.controllingArea:(Tr=p==null?void 0:p.body)==null?void 0:Tr.controllingArea,centerName:r!=null&&r.costCenter?r==null?void 0:r.costCenter:(yr=p==null?void 0:p.body)==null?void 0:yr.costCenter}),Be(he,"Cost Center","ChangeCC")&&((l==null?void 0:l.role)==="Super User"&&(r!=null&&r.requestType)&&((Nr=t==null?void 0:t.itmStatus)==null?void 0:Nr.toUpperCase())!=="OPEN"&&ae?n(h,{gap:1,sx:{display:"flex"},children:n(h,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:n(H,{children:n(h,{item:!0,children:d(a,{variant:"outlined",size:"small",sx:O,onClick:ye,children:["Fill Details",n(Me,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(l==null?void 0:l.role)==="Finance"&&(r!=null&&r.requestType||t!=null&&t.processDesc)&&((Ir=t==null?void 0:t.itmStatus)==null?void 0:Ir.toUpperCase())!=="OPEN"&&ae?n(h,{gap:1,sx:{display:"flex"},children:n(h,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:n(H,{children:n(h,{item:!0,children:d(a,{variant:"outlined",size:"small",sx:O,onClick:ye,children:["Fill Details",n(Me,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(l==null?void 0:l.role)==="Super User"&&!(r!=null&&r.requestType)&&((Fr=t==null?void 0:t.itmStatus)==null?void 0:Fr.toUpperCase())!=="OPEN"&&ae?n(h,{gap:1,sx:{display:"flex"},children:n(h,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:n(H,{children:n(h,{item:!0,children:d(a,{variant:"outlined",size:"small",sx:O,onClick:ye,children:["Change",n(Me,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):(l==null?void 0:l.role)==="Finance"&&!(r!=null&&r.requestType)&&((Er=t==null?void 0:t.itmStatus)==null?void 0:Er.toUpperCase())!=="OPEN"&&ae?n(h,{gap:1,sx:{display:"flex"},children:n(h,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:n(H,{children:n(h,{item:!0,children:d(a,{variant:"outlined",size:"small",sx:O,onClick:ye,children:["Change",n(Me,{sx:{padding:"2px"},fontSize:"small"})]})})})})}):"")]})]}),d(h,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:[d(h,{item:!0,md:10,sx:{marginLeft:"40px"},children:[n(h,{item:!0,sx:{paddingTop:"2px !important"},children:d(Z,{flexDirection:"row",children:[n("div",{style:{width:"12%"},children:n(v,{variant:"body2",color:"#777",children:"Cost Center"})}),d(v,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[":"," ",r!=null&&r.costCenter?r==null?void 0:r.costCenter:u!=null&&u.costCenter?u==null?void 0:u.costCenter:""]})]})}),n(h,{item:!0,sx:{paddingTop:"2px !important"},children:d(Z,{flexDirection:"row",children:[n("div",{style:{width:"12%"},children:n(v,{variant:"body2",color:"#777",children:"Controlling Area"})}),d(v,{variant:"body2",fontWeight:"bold",children:[":"," ",r!=null&&r.controllingArea?r==null?void 0:r.controllingArea:u!=null&&u.controllingArea?u==null?void 0:u.controllingArea:""]})]})}),n(h,{item:!0,sx:{paddingTop:"2px !important"},children:d(Z,{flexDirection:"row",children:[n("div",{style:{width:"12%"},children:n(v,{variant:"body2",color:"#777",children:"Valid From"})}),d(v,{variant:"body2",fontWeight:"bold",children:[": ",ve(u==null?void 0:u.validFrom).format(D==null?void 0:D.dateFormat)]})]})}),n(h,{item:!0,sx:{paddingTop:"2px !important"},children:d(Z,{flexDirection:"row",children:[n("div",{style:{width:"12%"},children:n(v,{variant:"body2",color:"#777",children:"Valid To"})}),d(v,{variant:"body2",fontWeight:"bold",children:[": ",ve(u==null?void 0:u.validTo).format(D==null?void 0:D.dateFormat)]}),n(v,{variant:"body2",fontWeight:"bold"})]})})]}),n(h,{item:!0,md:2,sx:{marginLeft:"40px"}})]}),d(h,{container:!0,style:{marginLeft:25},children:[n(Yt,{activeStep:b,sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},"aria-label":"mui tabs example",children:z.map((s,c)=>n(Qt,{children:n(Gt,{sx:{fontWeight:"700"},children:s})},s))}),ur&&((qr=ur[b])==null?void 0:qr.map((s,c)=>n(J,{sx:{mb:2,width:"100%"},children:n(v,{variant:"body2",children:s})},c)))]})]}),d(h,{gap:1,sx:{display:"flex",justifyContent:"space-between"},children:[Be(he,"Cost Center","ChangeCC")&&(!(r!=null&&r.requestType)&&!F?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:b===0,children:"Back"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:U,disabled:b===z.length-1,children:"Next"})]})}):n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:b===0,children:"Back"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:U,disabled:b===z.length-1,children:"Next"})]})})),Be(he,"Cost Center","ChangeCC")&&((l==null?void 0:l.role)==="Super User"&&!(r!=null&&r.requestType)&&((zr=t==null?void 0:t.itmStatus)==null?void 0:zr.toUpperCase())!=="OPEN"&&F?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t!=null&&t.taskId?"":n(a,{variant:"contained",size:"small",sx:{button_Outlined:O,mr:1},onClick:ke,children:"Save As Draft"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:b===0,children:"Back"}),b===z.length-1?d(H,{children:[n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:Fe,children:"Validate"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:Ne,disabled:G,children:"Submit For Review"})]}):n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:U,disabled:b===z.length-1,children:"Next"})]})}):(l==null?void 0:l.role)==="Finance"&&!(r!=null&&r.requestType)&&((Br=t==null?void 0:t.itmStatus)==null?void 0:Br.toUpperCase())!=="OPEN"&&F?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t!=null&&t.taskId?"":n(a,{variant:"contained",size:"small",sx:{button_Outlined:O,mr:1},onClick:ke,children:"Save As Draft"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:b===0,children:"Back"}),b===z.length-1?d(H,{children:[n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:Fe,children:"Validate"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:re,disabled:G,children:"Submit For Review"})]}):n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:U,disabled:b===z.length-1,children:"Next"})]})}):""),Be(he,"Cost Center","ChangeCC")&&((l==null?void 0:l.role)==="Super User"&&(r==null?void 0:r.requestType)==="Create"&&((jr=t==null?void 0:t.itmStatus)==null?void 0:jr.toUpperCase())!=="OPEN"&&!F?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:Ie,children:"Validate"}),n(a,{variant:"contained",size:"small",sx:{button_Outlined:O,mr:1},onClick:mr,disabled:G,children:"Approve"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:cr,children:"Submit For Approval"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:b===0,children:"Back"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:U,disabled:b===z.length-1,children:"Next"})]})}):(l==null?void 0:l.role)==="Super User"&&(r==null?void 0:r.requestType)==="Change"&&((Mr=t==null?void 0:t.itmStatus)==null?void 0:Mr.toUpperCase())!=="OPEN"&&!F?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:Ie,children:"Validate"}),n(a,{variant:"contained",size:"small",sx:{button_Outlined:O,mr:1},onClick:ar,disabled:G,children:"Approve"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:lr,children:"Submit For Approval"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:b===0,children:"Back"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:U,disabled:b===z.length-1,children:"Next"})]})}):(l==null?void 0:l.role)==="MDM Steward"&&((r==null?void 0:r.requestType)==="Create"||(t==null?void 0:t.processDesc)==="Create")&&((Or=t==null?void 0:t.itmStatus)==null?void 0:Or.toUpperCase())!=="OPEN"&&!F?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(a,{variant:"outlined",size:"small",sx:{button_Outlined:O,mr:1},onClick:Ee,children:"Correction"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:re,children:"Submit For Approval"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:b===0,children:"Back"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:U,disabled:b===z.length-1,children:"Next"})]})}):(l==null?void 0:l.role)==="MDM Steward"&&((r==null?void 0:r.requestType)==="Change"||(t==null?void 0:t.processDesc)==="Change")&&(($r=t==null?void 0:t.itmStatus)==null?void 0:$r.toUpperCase())!=="OPEN"&&!F?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(a,{variant:"outlined",size:"small",sx:{button_Outlined:O,mr:1},onClick:Ee,children:"Correction"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:re,children:"Submit For Approval"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:b===0,children:"Back"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:U,disabled:b===z.length-1,children:"Next"})]})}):(l==null?void 0:l.role)==="Approver"&&((r==null?void 0:r.requestType)==="Create"||(t==null?void 0:t.processDesc)==="Create")&&((Lr=t==null?void 0:t.itmStatus)==null?void 0:Lr.toUpperCase())!=="OPEN"&&!F?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(a,{variant:"outlined",size:"small",sx:{button_Outlined:O,mr:1},onClick:Ee,children:"Correction"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:Ie,children:"Validate"}),n(a,{variant:"contained",size:"small",sx:{button_Outlined:O,mr:1},onClick:re,disabled:G,children:"Approve"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:b===0,children:"Back"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:U,disabled:b===z.length-1,children:"Next"})]})}):(l==null?void 0:l.role)==="Approver"&&((r==null?void 0:r.requestType)==="Change"||(t==null?void 0:t.processDesc)==="Change")&&((Ur=t==null?void 0:t.itmStatus)==null?void 0:Ur.toUpperCase())!=="OPEN"&&!F?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(a,{variant:"outlined",size:"small",sx:{button_Outlined:O,mr:1},onClick:Ee,children:"Correction"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:Ie,children:"Validate"}),n(a,{variant:"contained",size:"small",sx:{button_Outlined:O,mr:1},onClick:re,disabled:G,children:"Approve"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:b===0,children:"Back"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:U,disabled:b===z.length-1,children:"Next"})]})}):(l==null?void 0:l.role)==="Super User"&&(r==null?void 0:r.requestType)==="Create"&&((kr=t==null?void 0:t.itmStatus)==null?void 0:kr.toUpperCase())!=="OPEN"&&F?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:b===0,children:"Back"}),b===z.length-1?n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:dr,children:"Submit For Review"}):n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:U,disabled:b===z.length-1,children:"Next"})]})}):(l==null?void 0:l.role)==="Super User"&&(r==null?void 0:r.requestType)==="Change"&&((_r=t==null?void 0:t.itmStatus)==null?void 0:_r.toUpperCase())!=="OPEN"&&F?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:b===0,children:"Back"}),b===z.length-1?n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:Ne,children:"Submit For Review"}):n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:U,disabled:b===z.length-1,children:"Next"})]})}):(l==null?void 0:l.role)==="Finance"&&((r==null?void 0:r.requestType)==="Create"||(t==null?void 0:t.processDesc)==="Create")&&((Vr=t==null?void 0:t.itmStatus)==null?void 0:Vr.toUpperCase())!=="OPEN"&&F?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t.taskId?"":n(a,{variant:"contained",size:"small",sx:{button_Outlined:O,mr:1},onClick:ot,children:"Save As Draft"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:b===0,children:"Back"}),b===z.length-1?d(H,{children:[n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:Fe,children:"Validate"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:re,disabled:G,children:"Submit For Review"})]}):n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:U,disabled:b===z.length-1,children:"Next"})]})}):(l==null?void 0:l.role)==="Finance"&&((r==null?void 0:r.requestType)==="Change"||(t==null?void 0:t.processDesc)==="Change")&&((Wr=t==null?void 0:t.itmStatus)==null?void 0:Wr.toUpperCase())!=="OPEN"&&F?n(_,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(V,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[t.taskId?"":n(a,{variant:"contained",size:"small",sx:{button_Outlined:O,mr:1},onClick:ke,children:"Save As Draft"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:L,disabled:b===0,children:"Back"}),b===z.length-1?d(H,{children:[n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:Fe,children:"Validate"}),n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:re,disabled:G,children:"Submit For Review"})]}):n(a,{variant:"contained",size:"small",sx:{...x,mr:1},onClick:U,disabled:b===z.length-1,children:"Next"})]})}):(l==null?void 0:l.role)==="Finance"&&"")]}),d(Ve,{open:fn,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[n(We,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:n(v,{variant:"h6",children:"New Cost Center"})}),n(Ke,{sx:{padding:".5rem 1rem"},children:d(h,{container:!0,spacing:1,children:[d(h,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[d(v,{children:["Cost Center",n("span",{style:{color:"red"},children:"*"})]}),n(le,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px"},children:n(ze,{sx:{fontSize:"12px !important",height:"31px"},fullWidth:!0,size:"small",placeholder:"Enter Cost Center Name",required:!0})})]}),d(h,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[d(v,{children:["Controlling Area",n("span",{style:{color:"red"},children:"*"})]}),n(le,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px"},children:n(Lt,{sx:{height:"42px"},required:"true",size:"small",renderInput:s=>n(ze,{sx:{fontSize:"12px !important"},...s,variant:"outlined",placeholder:"Select Cost Center"})})})]}),d(h,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[d(v,{children:["Valid From",n("span",{style:{color:"red"},children:"*"})]}),n(le,{fullWidth:!0,sx:{margin:".5em 0px"},children:n(Dr,{dateAdapter:en,children:n(an,{slotProps:{textField:{size:"small"}}})})})]}),d(h,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[d(v,{children:["Valid To",n("span",{style:{color:"red"},children:"*"})]}),n(le,{fullWidth:!0,sx:{margin:".5em 0px"},children:n(Dr,{dateAdapter:en,children:n(an,{slotProps:{textField:{size:"small"}}})})})]})]})}),d(Xe,{sx:{display:"flex",justifyContent:"end"},children:[n(a,{sx:{width:"max-content",textTransform:"capitalize"},children:"Cancel"}),n(a,{className:"button_primary--normal",type:"save",variant:"contained",children:"Proceed"})]})]})]})})};export{Bo as default};
