import{r as a,q as f,s as _,f4 as J,a as s,G as $,j as m,T as x,ay as T,y as Q,x as U,b6 as R,E as W,at as X,au as Y,F as Z,bI as b,K as L,eV as V,ai as w}from"./index-75c1660a.js";import{D as ee}from"./DatePicker-31fef6b6.js";function re(n,o){return Array.isArray(o)&&o.find(q=>q.code===n)||""}const te=({label:n,value:o,units:j,onSave:q,isEditMode:D,visibility:l,length:v,type:d,taskRequestId:y,data:t,generalLedgerRowData:u})=>{var O,P;const[I,A]=a.useState(o),[ce,M]=a.useState(!1),[B,H]=a.useState(!1),C=f(e=>e.AllDropDown.dropDown),S=_();re(I,C);let g=f(e=>e.userManagement.taskData);const k=f(e=>e.appSettings),K=g==null?void 0:g.subject,E=f(e=>e.edit.payload);a.useState(!0);let h=f(e=>{var r;return(r=e==null?void 0:e.initialData)==null?void 0:r.IWMMyTask});const z={label:n,value:I,units:j,type:d};f(e=>e.generalLedger.errorFields);let c=n.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("");a.useEffect(()=>{A(o)},[o]),console.log("glLabel",n,t[c]),a.useEffect(()=>{console.log("insideuseeffect"),(l==="0"||l==="Required")&&(console.log("insidevisibility"),S(J(c)))},[]);const i=e=>{S(b({keyname:c.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""),data:e}))},G=(e,r)=>{const p=F=>{S(w({keyName:"AccountID",data:F.body}))},N=F=>{console.log(F,"error in dojax")};L(`/${V}/data/getAccountId?companyCode=${e}&houseBank=${r}`,"get",p,N)};return a.useEffect(()=>{n==="House Bank"&&G((u==null?void 0:u.compCode)||(h==null?void 0:h.compCode),E==null?void 0:E.HouseBank)},[]),s(Z,{children:D?l==="Hidden"?null:s($,{item:!0,children:s(U,{children:D?m("div",{children:[m(x,{variant:"body2",color:"#777",children:[n,l==="Required"||l==="0"?s("span",{style:{color:"red"},children:"*"}):""]}),d==="Drop Down"?s(R,{options:C[c]??[],value:t[c]&&((O=C[c])==null?void 0:O.filter(e=>e.code===t[c]))&&((P=C[c])==null?void 0:P.filter(e=>e.code===t[c])[0])||"",onChange:(e,r,p)=>{n==="House Bank"&&G((u==null?void 0:u.compCode)||(h==null?void 0:h.compCode),r.code),(l==="Required"||l==="0")&&r===null&&H(!0),i(p==="clear"?"":r==null?void 0:r.code),M(!0)},getOptionLabel:e=>e===""||(e==null?void 0:e.code)===""?"":`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`??"",renderOption:(e,r)=>s("li",{...e,children:s(x,{style:{fontSize:12},children:`${r==null?void 0:r.code} - ${r==null?void 0:r.desc}`})}),renderInput:e=>s(W,{...e,variant:"outlined",placeholder:`Select ${z.label}`,size:"small",error:B})}):d==="Input"?s(W,{variant:"outlined",size:"small",fullWidth:!0,value:t[c].toUpperCase(),placeholder:`Enter ${z.label}`,inputProps:{maxLength:v},onChange:e=>{const r=e.target.value;if(r.length>0&&r[0]===" ")i(r.trimStart());else{let p=r.toUpperCase();i(p)}(l==="Required"||l==="0")&&r.length<=0&&H(!0),A(r.toUpperCase())},error:B}):d==="Calendar"?s(X,{dateAdapter:Y,children:s(ee,{slotProps:{textField:{size:"small"}},value:t[c]===""?null:t[c],placeholder:"Select Date Range",maxDate:new Date(9999,12,31),onChange:e=>{const r=Date.parse(e);i(r),A(r)}})}):d==="Radio Button"?s($,{item:!0,md:2,children:s(T,{sx:{padding:0},checked:t[c],onChange:(e,r)=>{i(r),A(r)}})}):""]}):""})}):y&&l==="Hidden"||K&&l==="Hidden"?null:s($,{item:!0,children:m(U,{children:[m(x,{variant:"body2",color:"#777",children:[n,l==="Required"||l==="0"?s("span",{style:{color:"red"},children:"*"}):""]}),m(x,{variant:"body2",fontWeight:"bold",children:[d==="Radio Button"?s(T,{sx:{padding:0},checked:t[c],disabled:!0}):"",d==="Calendar"?Q(t[c]).format(k==null?void 0:k.dateFormat):t[c]]})]})})})};export{te as E};
