import{o as q,p as Xn,r as j,dO as wb,pS as Sb,b as kb,a as ch,x as Cb,gs as $b}from"./index-75c1660a.js";import{f as yt}from"./Typography-655c2d0d.js";import{B as Eb,y as qi}from"./Button-c2ace85e.js";import{g as Fr}from"./TextField-38e32fd7.js";import{m as Br}from"./Box-5169177e.js";import{f as Gn}from"./Grid-e6535e9f.js";import{m as Qo}from"./Stack-005d135b.js";import{e as Tb}from"./IconButton-c40e0dd9.js";import{p as Nb}from"./Tooltip-ba20bf71.js";import{c as Ab}from"./CircularProgress-0ffbc952.js";import{t as Pb,g as _b,c as Rb}from"./AccordionDetails-b232c204.js";import"./Dropdown-fc3a3f6e.js";function Mb(e){return q.jsx(Eb,{...e})}var Ob=Object.defineProperty,jb=(e,r,o)=>r in e?Ob(e,r,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[r]=o,Zg=(e,r,o)=>jb(e,typeof r!="symbol"?r+"":r,o);function At(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}var Ib=typeof Symbol=="function"&&Symbol.observable||"@@observable",dh=Ib,Hc=()=>Math.random().toString(36).substring(7).split("").join("."),zb={INIT:`@@redux/INIT${Hc()}`,REPLACE:`@@redux/REPLACE${Hc()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${Hc()}`},Ds=zb;function go(e){if(typeof e!="object"||e===null)return!1;let r=e;for(;Object.getPrototypeOf(r)!==null;)r=Object.getPrototypeOf(r);return Object.getPrototypeOf(e)===r||Object.getPrototypeOf(e)===null}function ey(e,r,o){if(typeof e!="function")throw new Error(At(2));if(typeof r=="function"&&typeof o=="function"||typeof o=="function"&&typeof arguments[3]=="function")throw new Error(At(0));if(typeof r=="function"&&typeof o>"u"&&(o=r,r=void 0),typeof o<"u"){if(typeof o!="function")throw new Error(At(1));return o(ey)(e,r)}let i=e,l=r,u=new Map,d=u,f=0,m=!1;function g(){d===u&&(d=new Map,u.forEach((b,T)=>{d.set(T,b)}))}function S(){if(m)throw new Error(At(3));return l}function y(b){if(typeof b!="function")throw new Error(At(4));if(m)throw new Error(At(5));let T=!0;g();const N=f++;return d.set(N,b),function(){if(T){if(m)throw new Error(At(6));T=!1,g(),d.delete(N),u=null}}}function v(b){if(!go(b))throw new Error(At(7));if(typeof b.type>"u")throw new Error(At(8));if(typeof b.type!="string")throw new Error(At(17));if(m)throw new Error(At(9));try{m=!0,l=i(l,b)}finally{m=!1}return(u=d).forEach(T=>{T()}),b}function $(b){if(typeof b!="function")throw new Error(At(10));i=b,v({type:Ds.REPLACE})}function E(){const b=y;return{subscribe(T){if(typeof T!="object"||T===null)throw new Error(At(11));function N(){const A=T;A.next&&A.next(S())}return N(),{unsubscribe:b(N)}},[dh](){return this}}}return v({type:Ds.INIT}),{dispatch:v,subscribe:y,getState:S,replaceReducer:$,[dh]:E}}function Db(e){Object.keys(e).forEach(r=>{const o=e[r];if(typeof o(void 0,{type:Ds.INIT})>"u")throw new Error(At(12));if(typeof o(void 0,{type:Ds.PROBE_UNKNOWN_ACTION()})>"u")throw new Error(At(13))})}function ty(e){const r=Object.keys(e),o={};for(let u=0;u<r.length;u++){const d=r[u];typeof e[d]=="function"&&(o[d]=e[d])}const i=Object.keys(o);let l;try{Db(o)}catch(u){l=u}return function(u={},d){if(l)throw l;let f=!1;const m={};for(let g=0;g<i.length;g++){const S=i[g],y=o[S],v=u[S],$=y(v,d);if(typeof $>"u")throw d&&d.type,new Error(At(14));m[S]=$,f=f||$!==v}return f=f||i.length!==Object.keys(u).length,f?m:u}}function Fs(...e){return e.length===0?r=>r:e.length===1?e[0]:e.reduce((r,o)=>(...i)=>r(o(...i)))}function Fb(...e){return r=>(o,i)=>{const l=r(o,i);let u=()=>{throw new Error(At(15))};const d={getState:l.getState,dispatch:(m,...g)=>u(m,...g)},f=e.map(m=>m(d));return u=Fs(...f)(l.dispatch),{...l,dispatch:u}}}function ny(e){return go(e)&&"type"in e&&typeof e.type=="string"}var Xd=Symbol.for("immer-nothing"),ji=Symbol.for("immer-draftable"),rn=Symbol.for("immer-state");function _t(e,...r){throw new Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var Ko=Object.getPrototypeOf;function yr(e){return!!e&&!!e[rn]}function Zn(e){var r;return e?ry(e)||Array.isArray(e)||!!e[ji]||!!((r=e.constructor)!=null&&r[ji])||nl(e)||rl(e):!1}var Bb=Object.prototype.constructor.toString();function ry(e){if(!e||typeof e!="object")return!1;const r=Ko(e);if(r===null)return!0;const o=Object.hasOwnProperty.call(r,"constructor")&&r.constructor;return o===Object?!0:typeof o=="function"&&Function.toString.call(o)===Bb}function Lb(e){return yr(e)||_t(15,e),e[rn].base_}function Wi(e,r){Go(e)===0?Reflect.ownKeys(e).forEach(o=>{r(o,e[o],e)}):e.forEach((o,i)=>r(i,o,e))}function Go(e){const r=e[rn];return r?r.type_:Array.isArray(e)?1:nl(e)?2:rl(e)?3:0}function Ui(e,r){return Go(e)===2?e.has(r):Object.prototype.hasOwnProperty.call(e,r)}function Jc(e,r){return Go(e)===2?e.get(r):e[r]}function oy(e,r,o){const i=Go(e);i===2?e.set(r,o):i===3?e.add(o):e[r]=o}function qb(e,r){return e===r?e!==0||1/e===1/r:e!==e&&r!==r}function nl(e){return e instanceof Map}function rl(e){return e instanceof Set}function Bo(e){return e.copy_||e.base_}function yd(e,r){if(nl(e))return new Map(e);if(rl(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);const o=ry(e);if(r===!0||r==="class_only"&&!o){const i=Object.getOwnPropertyDescriptors(e);delete i[rn];let l=Reflect.ownKeys(i);for(let u=0;u<l.length;u++){const d=l[u],f=i[d];f.writable===!1&&(f.writable=!0,f.configurable=!0),(f.get||f.set)&&(i[d]={configurable:!0,writable:!0,enumerable:f.enumerable,value:e[d]})}return Object.create(Ko(e),i)}else{const i=Ko(e);if(i!==null&&o)return{...e};const l=Object.create(i);return Object.assign(l,e)}}function Hd(e,r=!1){return Zs(e)||yr(e)||!Zn(e)||(Go(e)>1&&(e.set=e.add=e.clear=e.delete=Wb),Object.freeze(e),r&&Object.entries(e).forEach(([o,i])=>Hd(i,!0))),e}function Wb(){_t(2)}function Zs(e){return Object.isFrozen(e)}var vd={};function Yo(e){const r=vd[e];return r||_t(0,e),r}function Ub(e,r){vd[e]||(vd[e]=r)}var Qi;function ay(){return Qi}function Qb(e,r){return{drafts_:[],parent_:e,immer_:r,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function ph(e,r){r&&(Yo("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=r)}function bd(e){xd(e),e.drafts_.forEach(Vb),e.drafts_=null}function xd(e){e===Qi&&(Qi=e.parent_)}function fh(e){return Qi=Qb(Qi,e)}function Vb(e){const r=e[rn];r.type_===0||r.type_===1?r.revoke_():r.revoked_=!0}function mh(e,r){r.unfinalizedDrafts_=r.drafts_.length;const o=r.drafts_[0];return e!==void 0&&e!==o?(o[rn].modified_&&(bd(r),_t(4)),Zn(e)&&(e=Bs(r,e),r.parent_||Ls(r,e)),r.patches_&&Yo("Patches").generateReplacementPatches_(o[rn].base_,e,r.patches_,r.inversePatches_)):e=Bs(r,o,[]),bd(r),r.patches_&&r.patchListener_(r.patches_,r.inversePatches_),e!==Xd?e:void 0}function Bs(e,r,o){if(Zs(r))return r;const i=r[rn];if(!i)return Wi(r,(l,u)=>hh(e,i,r,l,u,o)),r;if(i.scope_!==e)return r;if(!i.modified_)return Ls(e,i.base_,!0),i.base_;if(!i.finalized_){i.finalized_=!0,i.scope_.unfinalizedDrafts_--;const l=i.copy_;let u=l,d=!1;i.type_===3&&(u=new Set(l),l.clear(),d=!0),Wi(u,(f,m)=>hh(e,i,l,f,m,o,d)),Ls(e,l,!1),o&&e.patches_&&Yo("Patches").generatePatches_(i,o,e.patches_,e.inversePatches_)}return i.copy_}function hh(e,r,o,i,l,u,d){if(yr(l)){const f=u&&r&&r.type_!==3&&!Ui(r.assigned_,i)?u.concat(i):void 0,m=Bs(e,l,f);if(oy(o,i,m),yr(m))e.canAutoFreeze_=!1;else return}else d&&o.add(l);if(Zn(l)&&!Zs(l)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;Bs(e,l),(!r||!r.scope_.parent_)&&typeof i!="symbol"&&Object.prototype.propertyIsEnumerable.call(o,i)&&Ls(e,l)}}function Ls(e,r,o=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&Hd(r,o)}function Kb(e,r){const o=Array.isArray(e),i={type_:o?1:0,scope_:r?r.scope_:ay(),modified_:!1,finalized_:!1,assigned_:{},parent_:r,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1};let l=i,u=Jd;o&&(l=[i],u=Vi);const{revoke:d,proxy:f}=Proxy.revocable(l,u);return i.draft_=f,i.revoke_=d,f}var Jd={get(e,r){if(r===rn)return e;const o=Bo(e);if(!Ui(o,r))return Gb(e,o,r);const i=o[r];return e.finalized_||!Zn(i)?i:i===Zc(e.base_,r)?(ed(e),e.copy_[r]=Sd(i,e)):i},has(e,r){return r in Bo(e)},ownKeys(e){return Reflect.ownKeys(Bo(e))},set(e,r,o){const i=iy(Bo(e),r);if(i!=null&&i.set)return i.set.call(e.draft_,o),!0;if(!e.modified_){const l=Zc(Bo(e),r),u=l==null?void 0:l[rn];if(u&&u.base_===o)return e.copy_[r]=o,e.assigned_[r]=!1,!0;if(qb(o,l)&&(o!==void 0||Ui(e.base_,r)))return!0;ed(e),wd(e)}return e.copy_[r]===o&&(o!==void 0||r in e.copy_)||Number.isNaN(o)&&Number.isNaN(e.copy_[r])||(e.copy_[r]=o,e.assigned_[r]=!0),!0},deleteProperty(e,r){return Zc(e.base_,r)!==void 0||r in e.base_?(e.assigned_[r]=!1,ed(e),wd(e)):delete e.assigned_[r],e.copy_&&delete e.copy_[r],!0},getOwnPropertyDescriptor(e,r){const o=Bo(e),i=Reflect.getOwnPropertyDescriptor(o,r);return i&&{writable:!0,configurable:e.type_!==1||r!=="length",enumerable:i.enumerable,value:o[r]}},defineProperty(){_t(11)},getPrototypeOf(e){return Ko(e.base_)},setPrototypeOf(){_t(12)}},Vi={};Wi(Jd,(e,r)=>{Vi[e]=function(){return arguments[0]=arguments[0][0],r.apply(this,arguments)}});Vi.deleteProperty=function(e,r){return Vi.set.call(this,e,r,void 0)};Vi.set=function(e,r,o){return Jd.set.call(this,e[0],r,o,e[0])};function Zc(e,r){const o=e[rn];return(o?Bo(o):e)[r]}function Gb(e,r,o){var i;const l=iy(r,o);return l?"value"in l?l.value:(i=l.get)==null?void 0:i.call(e.draft_):void 0}function iy(e,r){if(!(r in e))return;let o=Ko(e);for(;o;){const i=Object.getOwnPropertyDescriptor(o,r);if(i)return i;o=Ko(o)}}function wd(e){e.modified_||(e.modified_=!0,e.parent_&&wd(e.parent_))}function ed(e){e.copy_||(e.copy_=yd(e.base_,e.scope_.immer_.useStrictShallowCopy_))}var Yb=class{constructor(r){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(o,i,l)=>{if(typeof o=="function"&&typeof i!="function"){const d=i;i=o;const f=this;return function(m=d,...g){return f.produce(m,S=>i.call(this,S,...g))}}typeof i!="function"&&_t(6),l!==void 0&&typeof l!="function"&&_t(7);let u;if(Zn(o)){const d=fh(this),f=Sd(o,void 0);let m=!0;try{u=i(f),m=!1}finally{m?bd(d):xd(d)}return ph(d,l),mh(u,d)}else if(!o||typeof o!="object"){if(u=i(o),u===void 0&&(u=o),u===Xd&&(u=void 0),this.autoFreeze_&&Hd(u,!0),l){const d=[],f=[];Yo("Patches").generateReplacementPatches_(o,u,d,f),l(d,f)}return u}else _t(1,o)},this.produceWithPatches=(o,i)=>{if(typeof o=="function")return(d,...f)=>this.produceWithPatches(d,m=>o(m,...f));let l,u;return[this.produce(o,i,(d,f)=>{l=d,u=f}),l,u]},typeof(r==null?void 0:r.autoFreeze)=="boolean"&&this.setAutoFreeze(r.autoFreeze),typeof(r==null?void 0:r.useStrictShallowCopy)=="boolean"&&this.setUseStrictShallowCopy(r.useStrictShallowCopy)}createDraft(r){Zn(r)||_t(8),yr(r)&&(r=Xb(r));const o=fh(this),i=Sd(r,void 0);return i[rn].isManual_=!0,xd(o),i}finishDraft(r,o){const i=r&&r[rn];(!i||!i.isManual_)&&_t(9);const{scope_:l}=i;return ph(l,o),mh(void 0,l)}setAutoFreeze(r){this.autoFreeze_=r}setUseStrictShallowCopy(r){this.useStrictShallowCopy_=r}applyPatches(r,o){let i;for(i=o.length-1;i>=0;i--){const u=o[i];if(u.path.length===0&&u.op==="replace"){r=u.value;break}}i>-1&&(o=o.slice(i+1));const l=Yo("Patches").applyPatches_;return yr(r)?l(r,o):this.produce(r,u=>l(u,o))}};function Sd(e,r){const o=nl(e)?Yo("MapSet").proxyMap_(e,r):rl(e)?Yo("MapSet").proxySet_(e,r):Kb(e,r);return(r?r.scope_:ay()).drafts_.push(o),o}function Xb(e){return yr(e)||_t(10,e),ly(e)}function ly(e){if(!Zn(e)||Zs(e))return e;const r=e[rn];let o;if(r){if(!r.modified_)return r.base_;r.finalized_=!0,o=yd(e,r.scope_.immer_.useStrictShallowCopy_)}else o=yd(e,!0);return Wi(o,(i,l)=>{oy(o,i,ly(l))}),r&&(r.finalized_=!1),o}function Hb(){const e="replace",r="add",o="remove";function i(y,v,$,E){switch(y.type_){case 0:case 2:return u(y,v,$,E);case 1:return l(y,v,$,E);case 3:return d(y,v,$,E)}}function l(y,v,$,E){let{base_:b,assigned_:T}=y,N=y.copy_;N.length<b.length&&([b,N]=[N,b],[$,E]=[E,$]);for(let A=0;A<b.length;A++)if(T[A]&&N[A]!==b[A]){const x=v.concat([A]);$.push({op:e,path:x,value:S(N[A])}),E.push({op:e,path:x,value:S(b[A])})}for(let A=b.length;A<N.length;A++){const x=v.concat([A]);$.push({op:r,path:x,value:S(N[A])})}for(let A=N.length-1;b.length<=A;--A){const x=v.concat([A]);E.push({op:o,path:x})}}function u(y,v,$,E){const{base_:b,copy_:T}=y;Wi(y.assigned_,(N,A)=>{const x=Jc(b,N),C=Jc(T,N),k=A?Ui(b,N)?e:r:o;if(x===C&&k===e)return;const M=v.concat(N);$.push(k===o?{op:k,path:M}:{op:k,path:M,value:C}),E.push(k===r?{op:o,path:M}:k===o?{op:r,path:M,value:S(x)}:{op:e,path:M,value:S(x)})})}function d(y,v,$,E){let{base_:b,copy_:T}=y,N=0;b.forEach(A=>{if(!T.has(A)){const x=v.concat([N]);$.push({op:o,path:x,value:A}),E.unshift({op:r,path:x,value:A})}N++}),N=0,T.forEach(A=>{if(!b.has(A)){const x=v.concat([N]);$.push({op:r,path:x,value:A}),E.unshift({op:o,path:x,value:A})}N++})}function f(y,v,$,E){$.push({op:e,path:[],value:v===Xd?void 0:v}),E.push({op:e,path:[],value:y})}function m(y,v){return v.forEach($=>{const{path:E,op:b}=$;let T=y;for(let C=0;C<E.length-1;C++){const k=Go(T);let M=E[C];typeof M!="string"&&typeof M!="number"&&(M=""+M),(k===0||k===1)&&(M==="__proto__"||M==="constructor")&&_t(19),typeof T=="function"&&M==="prototype"&&_t(19),T=Jc(T,M),typeof T!="object"&&_t(18,E.join("/"))}const N=Go(T),A=g($.value),x=E[E.length-1];switch(b){case e:switch(N){case 2:return T.set(x,A);case 3:_t(16);default:return T[x]=A}case r:switch(N){case 1:return x==="-"?T.push(A):T.splice(x,0,A);case 2:return T.set(x,A);case 3:return T.add(A);default:return T[x]=A}case o:switch(N){case 1:return T.splice(x,1);case 2:return T.delete(x);case 3:return T.delete($.value);default:return delete T[x]}default:_t(17,b)}}),y}function g(y){if(!Zn(y))return y;if(Array.isArray(y))return y.map(g);if(nl(y))return new Map(Array.from(y.entries()).map(([$,E])=>[$,g(E)]));if(rl(y))return new Set(Array.from(y).map(g));const v=Object.create(Ko(y));for(const $ in y)v[$]=g(y[$]);return Ui(y,ji)&&(v[ji]=y[ji]),v}function S(y){return yr(y)?g(y):y}Ub("Patches",{applyPatches_:m,generatePatches_:i,generateReplacementPatches_:f})}var mn=new Yb,ol=mn.produce,sy=mn.produceWithPatches.bind(mn);mn.setAutoFreeze.bind(mn);mn.setUseStrictShallowCopy.bind(mn);var gh=mn.applyPatches.bind(mn);mn.createDraft.bind(mn);mn.finishDraft.bind(mn);function uy(e){return({dispatch:r,getState:o})=>i=>l=>typeof l=="function"?l(r,o,e):i(l)}var Jb=uy(),Zb=uy,e1=typeof window<"u"&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(arguments.length!==0)return typeof arguments[0]=="object"?Fs:Fs.apply(null,arguments)},t1=e=>e&&typeof e.match=="function";function Hn(e,r){function o(...i){if(r){let l=r(...i);if(!l)throw new Error(Jn(0));return{type:e,payload:l.payload,..."meta"in l&&{meta:l.meta},..."error"in l&&{error:l.error}}}return{type:e,payload:i[0]}}return o.toString=()=>`${e}`,o.type=e,o.match=i=>ny(i)&&i.type===e,o}var cy=class _i extends Array{constructor(...r){super(...r),Object.setPrototypeOf(this,_i.prototype)}static get[Symbol.species](){return _i}concat(...r){return super.concat.apply(this,r)}prepend(...r){return r.length===1&&Array.isArray(r[0])?new _i(...r[0].concat(this)):new _i(...r.concat(this))}};function yh(e){return Zn(e)?ol(e,()=>{}):e}function ys(e,r,o){return e.has(r)?e.get(r):e.set(r,o(r)).get(r)}function n1(e){return typeof e=="boolean"}var r1=()=>function(e){const{thunk:r=!0,immutableCheck:o=!0,serializableCheck:i=!0,actionCreatorCheck:l=!0}=e??{};let u=new cy;return r&&(n1(r)?u.push(Jb):u.push(Zb(r.extraArgument))),u},eu="RTK_autoBatch",$i=()=>e=>({payload:e,meta:{[eu]:!0}}),vh=e=>r=>{setTimeout(r,e)},o1=(e={type:"raf"})=>r=>(...o)=>{const i=r(...o);let l=!0,u=!1,d=!1;const f=new Set,m=e.type==="tick"?queueMicrotask:e.type==="raf"?typeof window<"u"&&window.requestAnimationFrame?window.requestAnimationFrame:vh(10):e.type==="callback"?e.queueNotification:vh(e.timeout),g=()=>{d=!1,u&&(u=!1,f.forEach(S=>S()))};return Object.assign({},i,{subscribe(S){const y=()=>l&&S(),v=i.subscribe(y);return f.add(S),()=>{v(),f.delete(S)}},dispatch(S){var y;try{return l=!((y=S==null?void 0:S.meta)!=null&&y[eu]),u=!l,u&&(d||(d=!0,m(g))),i.dispatch(S)}finally{l=!0}}})},a1=e=>function(r){const{autoBatch:o=!0}=r??{};let i=new cy(e);return o&&i.push(o1(typeof o=="object"?o:void 0)),i};function i1(e){const r=r1(),{reducer:o=void 0,middleware:i,devTools:l=!0,duplicateMiddlewareCheck:u=!0,preloadedState:d=void 0,enhancers:f=void 0}=e||{};let m;if(typeof o=="function")m=o;else if(go(o))m=ty(o);else throw new Error(Jn(1));let g;typeof i=="function"?g=i(r):g=r();let S=Fs;l&&(S=e1({trace:!1,...typeof l=="object"&&l}));const y=Fb(...g),v=a1(y);let $=typeof f=="function"?f(v):v();const E=S(...$);return ey(m,d,E)}function dy(e){const r={},o=[];let i;const l={addCase(u,d){const f=typeof u=="string"?u:u.type;if(!f)throw new Error(Jn(28));if(f in r)throw new Error(Jn(29));return r[f]=d,l},addMatcher(u,d){return o.push({matcher:u,reducer:d}),l},addDefaultCase(u){return i=u,l}};return e(l),[r,o,i]}function l1(e){return typeof e=="function"}function s1(e,r){let[o,i,l]=dy(r),u;if(l1(e))u=()=>yh(e());else{const f=yh(e);u=()=>f}function d(f=u(),m){let g=[o[m.type],...i.filter(({matcher:S})=>S(m)).map(({reducer:S})=>S)];return g.filter(S=>!!S).length===0&&(g=[l]),g.reduce((S,y)=>{if(y)if(yr(S)){const v=y(S,m);return v===void 0?S:v}else{if(Zn(S))return ol(S,v=>y(v,m));{const v=y(S,m);if(v===void 0){if(S===null)return S;throw Error("A case reducer on a non-draftable value must not return undefined")}return v}}return S},f)}return d.getInitialState=u,d}var py=(e,r)=>t1(e)?e.match(r):e(r);function Lr(...e){return r=>e.some(o=>py(o,r))}function Ii(...e){return r=>e.every(o=>py(o,r))}function tu(e,r){if(!e||!e.meta)return!1;const o=typeof e.meta.requestId=="string",i=r.indexOf(e.meta.requestStatus)>-1;return o&&i}function al(e){return typeof e[0]=="function"&&"pending"in e[0]&&"fulfilled"in e[0]&&"rejected"in e[0]}function Zd(...e){return e.length===0?r=>tu(r,["pending"]):al(e)?Lr(...e.map(r=>r.pending)):Zd()(e[0])}function ja(...e){return e.length===0?r=>tu(r,["rejected"]):al(e)?Lr(...e.map(r=>r.rejected)):ja()(e[0])}function nu(...e){const r=o=>o&&o.meta&&o.meta.rejectedWithValue;return e.length===0?Ii(ja(...e),r):al(e)?Ii(ja(...e),r):nu()(e[0])}function yo(...e){return e.length===0?r=>tu(r,["fulfilled"]):al(e)?Lr(...e.map(r=>r.fulfilled)):yo()(e[0])}function kd(...e){return e.length===0?r=>tu(r,["pending","fulfilled","rejected"]):al(e)?Lr(...e.flatMap(r=>[r.pending,r.rejected,r.fulfilled])):kd()(e[0])}var u1="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW",ep=(e=21)=>{let r="",o=e;for(;o--;)r+=u1[Math.random()*64|0];return r},c1=["name","message","stack","code"],td=class{constructor(e,r){Zg(this,"_type"),this.payload=e,this.meta=r}},bh=class{constructor(r,o){Zg(this,"_type"),this.payload=r,this.meta=o}},d1=e=>{if(typeof e=="object"&&e!==null){const r={};for(const o of c1)typeof e[o]=="string"&&(r[o]=e[o]);return r}return{message:String(e)}},xh="External signal was aborted",wh=(()=>{function e(r,o,i){const l=Hn(r+"/fulfilled",(m,g,S,y)=>({payload:m,meta:{...y||{},arg:S,requestId:g,requestStatus:"fulfilled"}})),u=Hn(r+"/pending",(m,g,S)=>({payload:void 0,meta:{...S||{},arg:g,requestId:m,requestStatus:"pending"}})),d=Hn(r+"/rejected",(m,g,S,y,v)=>({payload:y,error:(i&&i.serializeError||d1)(m||"Rejected"),meta:{...v||{},arg:S,requestId:g,rejectedWithValue:!!y,requestStatus:"rejected",aborted:(m==null?void 0:m.name)==="AbortError",condition:(m==null?void 0:m.name)==="ConditionError"}}));function f(m,{signal:g}={}){return(S,y,v)=>{const $=i!=null&&i.idGenerator?i.idGenerator(m):ep(),E=new AbortController;let b,T;function N(x){T=x,E.abort()}g&&(g.aborted?N(xh):g.addEventListener("abort",()=>N(xh),{once:!0}));const A=async function(){var x,C;let k;try{let M=(x=i==null?void 0:i.condition)==null?void 0:x.call(i,m,{getState:y,extra:v});if(f1(M)&&(M=await M),M===!1||E.signal.aborted)throw{name:"ConditionError",message:"Aborted due to condition callback returning false."};const I=new Promise((L,z)=>{b=()=>{z({name:"AbortError",message:T||"Aborted"})},E.signal.addEventListener("abort",b)});S(u($,m,(C=i==null?void 0:i.getPendingMeta)==null?void 0:C.call(i,{requestId:$,arg:m},{getState:y,extra:v}))),k=await Promise.race([I,Promise.resolve(o(m,{dispatch:S,getState:y,extra:v,requestId:$,signal:E.signal,abort:N,rejectWithValue:(L,z)=>new td(L,z),fulfillWithValue:(L,z)=>new bh(L,z)})).then(L=>{if(L instanceof td)throw L;return L instanceof bh?l(L.payload,$,m,L.meta):l(L,$,m)})])}catch(M){k=M instanceof td?d(null,$,m,M.payload,M.meta):d(M,$,m)}finally{b&&E.signal.removeEventListener("abort",b)}return i&&!i.dispatchConditionRejection&&d.match(k)&&k.meta.condition||S(k),k}();return Object.assign(A,{abort:N,requestId:$,arg:m,unwrap(){return A.then(p1)}})}}return Object.assign(f,{pending:u,rejected:d,fulfilled:l,settled:Lr(d,l),typePrefix:r})}return e.withTypes=()=>e,e})();function p1(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}function f1(e){return e!==null&&typeof e=="object"&&typeof e.then=="function"}var m1=Symbol.for("rtk-slice-createasyncthunk");function h1(e,r){return`${e}/${r}`}function g1({creators:e}={}){var r;const o=(r=e==null?void 0:e.asyncThunk)==null?void 0:r[m1];return function(i){const{name:l,reducerPath:u=l}=i;if(!l)throw new Error(Jn(11));typeof process<"u";const d=(typeof i.reducers=="function"?i.reducers(v1()):i.reducers)||{},f=Object.keys(d),m={sliceCaseReducersByName:{},sliceCaseReducersByType:{},actionCreators:{},sliceMatchers:[]},g={addCase(x,C){const k=typeof x=="string"?x:x.type;if(!k)throw new Error(Jn(12));if(k in m.sliceCaseReducersByType)throw new Error(Jn(13));return m.sliceCaseReducersByType[k]=C,g},addMatcher(x,C){return m.sliceMatchers.push({matcher:x,reducer:C}),g},exposeAction(x,C){return m.actionCreators[x]=C,g},exposeCaseReducer(x,C){return m.sliceCaseReducersByName[x]=C,g}};f.forEach(x=>{const C=d[x],k={reducerName:x,type:h1(l,x),createNotation:typeof i.reducers=="function"};x1(C)?S1(k,C,g,o):b1(k,C,g)});function S(){const[x={},C=[],k=void 0]=typeof i.extraReducers=="function"?dy(i.extraReducers):[i.extraReducers],M={...x,...m.sliceCaseReducersByType};return s1(i.initialState,I=>{for(let L in M)I.addCase(L,M[L]);for(let L of m.sliceMatchers)I.addMatcher(L.matcher,L.reducer);for(let L of C)I.addMatcher(L.matcher,L.reducer);k&&I.addDefaultCase(k)})}const y=x=>x,v=new Map,$=new WeakMap;let E;function b(x,C){return E||(E=S()),E(x,C)}function T(){return E||(E=S()),E.getInitialState()}function N(x,C=!1){function k(I){let L=I[x];return typeof L>"u"&&C&&(L=ys($,k,T)),L}function M(I=y){const L=ys(v,C,()=>new WeakMap);return ys(L,I,()=>{const z={};for(const[h,P]of Object.entries(i.selectors??{}))z[h]=y1(P,I,()=>ys($,I,T),C);return z})}return{reducerPath:x,getSelectors:M,get selectors(){return M(k)},selectSlice:k}}const A={name:l,reducer:b,actions:m.actionCreators,caseReducers:m.sliceCaseReducersByName,getInitialState:T,...N(u),injectInto(x,{reducerPath:C,...k}={}){const M=C??u;return x.inject({reducerPath:M,reducer:b},k),{...A,...N(M,!0)}}};return A}}function y1(e,r,o,i){function l(u,...d){let f=r(u);return typeof f>"u"&&i&&(f=o()),e(f,...d)}return l.unwrapped=e,l}var Lo=g1();function v1(){function e(r,o){return{_reducerDefinitionType:"asyncThunk",payloadCreator:r,...o}}return e.withTypes=()=>e,{reducer(r){return Object.assign({[r.name](...o){return r(...o)}}[r.name],{_reducerDefinitionType:"reducer"})},preparedReducer(r,o){return{_reducerDefinitionType:"reducerWithPrepare",prepare:r,reducer:o}},asyncThunk:e}}function b1({type:e,reducerName:r,createNotation:o},i,l){let u,d;if("reducer"in i){if(o&&!w1(i))throw new Error(Jn(17));u=i.reducer,d=i.prepare}else u=i;l.addCase(e,u).exposeCaseReducer(r,u).exposeAction(r,d?Hn(e,d):Hn(e))}function x1(e){return e._reducerDefinitionType==="asyncThunk"}function w1(e){return e._reducerDefinitionType==="reducerWithPrepare"}function S1({type:e,reducerName:r},o,i,l){if(!l)throw new Error(Jn(18));const{payloadCreator:u,fulfilled:d,pending:f,rejected:m,settled:g,options:S}=o,y=l(e,u,S);i.exposeAction(r,y),d&&i.addCase(y.fulfilled,d),f&&i.addCase(y.pending,f),m&&i.addCase(y.rejected,m),g&&i.addMatcher(y.settled,g),i.exposeCaseReducer(r,{fulfilled:d||vs,pending:f||vs,rejected:m||vs,settled:g||vs})}function vs(){}function Jn(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}var k1=Object.defineProperty,C1=(e,r,o)=>r in e?k1(e,r,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[r]=o,$1=(e,r,o)=>C1(e,typeof r!="symbol"?r+"":r,o);function E1(e,r=`expected a function, instead received ${typeof e}`){if(typeof e!="function")throw new TypeError(r)}function T1(e,r=`expected an object, instead received ${typeof e}`){if(typeof e!="object")throw new TypeError(r)}function N1(e,r="expected all items to be functions, instead received the following types: "){if(!e.every(o=>typeof o=="function")){const o=e.map(i=>typeof i=="function"?`function ${i.name||"unnamed"}()`:typeof i).join(", ");throw new TypeError(`${r}[${o}]`)}}var Sh=e=>Array.isArray(e)?e:[e];function A1(e){const r=Array.isArray(e[0])?e[0]:e;return N1(r,"createSelector expects all input-selectors to be functions, but received the following types: "),r}function P1(e,r){const o=[],{length:i}=e;for(let l=0;l<i;l++)o.push(e[l].apply(null,r));return o}var _1=class{constructor(r){this.value=r}deref(){return this.value}},R1=typeof WeakRef<"u"?WeakRef:_1,M1=0,kh=1;function bs(){return{s:M1,v:void 0,o:null,p:null}}function qs(e,r={}){let o=bs();const{resultEqualityCheck:i}=r;let l,u=0;function d(){var f;let m=o;const{length:g}=arguments;for(let v=0,$=g;v<$;v++){const E=arguments[v];if(typeof E=="function"||typeof E=="object"&&E!==null){let b=m.o;b===null&&(m.o=b=new WeakMap);const T=b.get(E);T===void 0?(m=bs(),b.set(E,m)):m=T}else{let b=m.p;b===null&&(m.p=b=new Map);const T=b.get(E);T===void 0?(m=bs(),b.set(E,m)):m=T}}const S=m;let y;if(m.s===kh)y=m.v;else if(y=e.apply(null,arguments),u++,i){const v=((f=l==null?void 0:l.deref)==null?void 0:f.call(l))??l;v!=null&&i(v,y)&&(y=v,u!==0&&u--),l=typeof y=="object"&&y!==null||typeof y=="function"?new R1(y):y}return S.s=kh,S.v=y,y}return d.clearCache=()=>{o=bs(),d.resetResultsCount()},d.resultsCount=()=>u,d.resetResultsCount=()=>{u=0},d}function O1(e,...r){const o=typeof e=="function"?{memoize:e,memoizeOptions:r}:e,i=(...l)=>{let u=0,d=0,f,m={},g=l.pop();typeof g=="object"&&(m=g,g=l.pop()),E1(g,`createSelector expects an output function after the inputs, but received: [${typeof g}]`);const S={...o,...m},{memoize:y,memoizeOptions:v=[],argsMemoize:$=qs,argsMemoizeOptions:E=[],devModeChecks:b={}}=S,T=Sh(v),N=Sh(E),A=A1(l),x=y(function(){return u++,g.apply(null,arguments)},...T),C=$(function(){d++;const k=P1(A,arguments);return f=x.apply(null,k),f},...N);return Object.assign(C,{resultFunc:g,memoizedResultFunc:x,dependencies:A,dependencyRecomputations:()=>d,resetDependencyRecomputations:()=>{d=0},lastResult:()=>f,recomputations:()=>u,resetRecomputations:()=>{u=0},memoize:y,argsMemoize:$})};return Object.assign(i,{withTypes:()=>i}),i}var tp=O1(qs),j1=Object.assign((e,r=tp)=>{T1(e,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof e}`);const o=Object.keys(e),i=o.map(l=>e[l]);return r(i,(...l)=>l.reduce((u,d,f)=>(u[o[f]]=d,u),{}))},{withTypes:()=>j1}),I1=class extends Error{constructor(r){super(r[0].message),$1(this,"issues"),this.name="SchemaError",this.issues=r}},fy=(e=>(e.uninitialized="uninitialized",e.pending="pending",e.fulfilled="fulfilled",e.rejected="rejected",e))(fy||{});function Ch(e){return{status:e,isUninitialized:e==="uninitialized",isLoading:e==="pending",isSuccess:e==="fulfilled",isError:e==="rejected"}}var $h=go;function my(e,r){if(e===r||!($h(e)&&$h(r)||Array.isArray(e)&&Array.isArray(r)))return r;const o=Object.keys(r),i=Object.keys(e);let l=o.length===i.length;const u=Array.isArray(r)?[]:{};for(const d of o)u[d]=my(e[d],r[d]),l&&(l=e[d]===u[d]);return l?e:u}function _a(e){let r=0;for(const o in e)r++;return r}var Eh=e=>[].concat(...e);function z1(e){return new RegExp("(^|:)//").test(e)}function D1(){return typeof document>"u"?!0:document.visibilityState!=="hidden"}function Ws(e){return e!=null}function F1(){return typeof navigator>"u"||navigator.onLine===void 0?!0:navigator.onLine}var B1=e=>e.replace(/\/$/,""),L1=e=>e.replace(/^\//,"");function q1(e,r){if(!e)return r;if(!r)return e;if(z1(r))return r;const o=e.endsWith("/")||!r.startsWith("?")?"/":"";return e=B1(e),r=L1(r),`${e}${o}${r}`}function W1(e,r,o){return e.has(r)?e.get(r):e.set(r,o).get(r)}var Th=(...e)=>fetch(...e),U1=e=>e.status>=200&&e.status<=299,Q1=e=>/ion\/(vnd\.api\+)?json/.test(e.get("content-type")||"");function Nh(e){if(!go(e))return e;const r={...e};for(const[o,i]of Object.entries(r))i===void 0&&delete r[o];return r}function V1({baseUrl:e,prepareHeaders:r=y=>y,fetchFn:o=Th,paramsSerializer:i,isJsonContentType:l=Q1,jsonContentType:u="application/json",jsonReplacer:d,timeout:f,responseHandler:m,validateStatus:g,...S}={}){return typeof fetch>"u"&&o===Th&&console.warn("Warning: `fetch` is not available. Please supply a custom `fetchFn` property to use `fetchBaseQuery` on SSR environments."),async(v,$,E)=>{const{getState:b,extra:T,endpoint:N,forced:A,type:x}=$;let C,{url:k,headers:M=new Headers(S.headers),params:I=void 0,responseHandler:L=m??"json",validateStatus:z=g??U1,timeout:h=f,...P}=typeof v=="string"?{url:v}:v,_,R=$.signal;h&&(_=new AbortController,$.signal.addEventListener("abort",_.abort),R=_.signal);let F={...S,signal:R,...P};M=new Headers(Nh(M)),F.headers=await r(M,{getState:b,arg:v,extra:T,endpoint:N,forced:A,type:x,extraOptions:E})||M;const G=fe=>typeof fe=="object"&&(go(fe)||Array.isArray(fe)||typeof fe.toJSON=="function");if(!F.headers.has("content-type")&&G(F.body)&&F.headers.set("content-type",u),G(F.body)&&l(F.headers)&&(F.body=JSON.stringify(F.body,d)),I){const fe=~k.indexOf("?")?"&":"?",ce=i?i(I):new URLSearchParams(Nh(I));k+=fe+ce}k=q1(e,k);const H=new Request(k,F);C={request:new Request(k,F)};let Z,W=!1,ee=_&&setTimeout(()=>{W=!0,_.abort()},h);try{Z=await o(H)}catch(fe){return{error:{status:W?"TIMEOUT_ERROR":"FETCH_ERROR",error:String(fe)},meta:C}}finally{ee&&clearTimeout(ee),_==null||_.signal.removeEventListener("abort",_.abort)}const K=Z.clone();C.response=K;let re,le="";try{let fe;if(await Promise.all([y(Z,L).then(ce=>re=ce,ce=>fe=ce),K.text().then(ce=>le=ce,()=>{})]),fe)throw fe}catch(fe){return{error:{status:"PARSING_ERROR",originalStatus:Z.status,data:le,error:String(fe)},meta:C}}return z(Z,re)?{data:re,meta:C}:{error:{status:Z.status,data:re},meta:C}};async function y(v,$){if(typeof $=="function")return $(v);if($==="content-type"&&($=l(v.headers)?"json":"text"),$==="json"){const E=await v.text();return E.length?JSON.parse(E):null}return v.text()}}var Ah=class{constructor(e,r=void 0){this.value=e,this.meta=r}},np=Hn("__rtkq/focused"),hy=Hn("__rtkq/unfocused"),rp=Hn("__rtkq/online"),gy=Hn("__rtkq/offline");function ru(e){return e.type==="query"}function K1(e){return e.type==="mutation"}function ou(e){return e.type==="infinitequery"}function Us(e){return ru(e)||ou(e)}function op(e,r,o,i,l,u){return G1(e)?e(r,o,i,l).filter(Ws).map(Cd).map(u):Array.isArray(e)?e.map(Cd).map(u):[]}function G1(e){return typeof e=="function"}function Cd(e){return typeof e=="string"?{type:e}:e}function Y1(e,r){return e.catch(r)}var Ki=Symbol("forceQueryFn"),$d=e=>typeof e[Ki]=="function";function X1({serializeQueryArgs:e,queryThunk:r,infiniteQueryThunk:o,mutationThunk:i,api:l,context:u}){const d=new Map,f=new Map,{unsubscribeQueryResult:m,removeMutationResult:g,updateSubscriptionOptions:S}=l.internalActions;return{buildInitiateQuery:T,buildInitiateInfiniteQuery:N,buildInitiateMutation:A,getRunningQueryThunk:y,getRunningMutationThunk:v,getRunningQueriesThunk:$,getRunningMutationsThunk:E};function y(x,C){return k=>{var M;const I=u.endpointDefinitions[x],L=e({queryArgs:C,endpointDefinition:I,endpointName:x});return(M=d.get(k))==null?void 0:M[L]}}function v(x,C){return k=>{var M;return(M=f.get(k))==null?void 0:M[C]}}function $(){return x=>Object.values(d.get(x)||{}).filter(Ws)}function E(){return x=>Object.values(f.get(x)||{}).filter(Ws)}function b(x,C){const k=(M,{subscribe:I=!0,forceRefetch:L,subscriptionOptions:z,[Ki]:h,...P}={})=>(_,R)=>{var F;const G=e({queryArgs:M,endpointDefinition:C,endpointName:x});let H;const Z={...P,type:"query",subscribe:I,forceRefetch:L,subscriptionOptions:z,endpointName:x,originalArgs:M,queryCacheKey:G,[Ki]:h};if(ru(C))H=r(Z);else{const{direction:be,initialPageParam:Se}=P;H=o({...Z,direction:be,initialPageParam:Se})}const W=l.endpoints[x].select(M),ee=_(H),K=W(R()),{requestId:re,abort:le}=ee,fe=K.requestId!==re,ce=(F=d.get(_))==null?void 0:F[G],ie=()=>W(R()),he=Object.assign(h?ee.then(ie):fe&&!ce?Promise.resolve(K):Promise.all([ce,ee]).then(ie),{arg:M,requestId:re,subscriptionOptions:z,queryCacheKey:G,abort:le,async unwrap(){const be=await he;if(be.isError)throw be.error;return be.data},refetch:()=>_(k(M,{subscribe:!1,forceRefetch:!0})),unsubscribe(){I&&_(m({queryCacheKey:G,requestId:re}))},updateSubscriptionOptions(be){he.subscriptionOptions=be,_(S({endpointName:x,requestId:re,queryCacheKey:G,options:be}))}});if(!ce&&!fe&&!h){const be=W1(d,_,{});be[G]=he,he.then(()=>{delete be[G],_a(be)||d.delete(_)})}return he};return k}function T(x,C){return b(x,C)}function N(x,C){return b(x,C)}function A(x){return(C,{track:k=!0,fixedCacheKey:M}={})=>(I,L)=>{const z=i({type:"mutation",endpointName:x,originalArgs:C,track:k,fixedCacheKey:M}),h=I(z),{requestId:P,abort:_,unwrap:R}=h,F=Y1(h.unwrap().then(W=>({data:W})),W=>({error:W})),G=()=>{I(g({requestId:P,fixedCacheKey:M}))},H=Object.assign(F,{arg:h.arg,requestId:P,abort:_,unwrap:R,reset:G}),Z=f.get(I)||{};return f.set(I,Z),Z[P]=H,H.then(()=>{delete Z[P],_a(Z)||f.delete(I)}),M&&(Z[M]=H,H.then(()=>{Z[M]===H&&(delete Z[M],_a(Z)||f.delete(I))})),H}}}var yy=class extends I1{constructor(r,o,i,l){super(r),this.value=o,this.schemaName=i,this._bqMeta=l}};async function Do(e,r,o,i){const l=await e["~standard"].validate(r);if(l.issues)throw new yy(l.issues,r,o,i);return l.value}function H1(e){return e}var Ei=(e={})=>({...e,[eu]:!0});function J1({reducerPath:e,baseQuery:r,context:{endpointDefinitions:o},serializeQueryArgs:i,api:l,assertTagType:u,selectors:d,onSchemaFailure:f,catchSchemaFailure:m,skipSchemaValidation:g}){const S=(P,_,R,F)=>(G,H)=>{const Z=o[P],W=i({queryArgs:_,endpointDefinition:Z,endpointName:P});if(G(l.internalActions.queryResultPatched({queryCacheKey:W,patches:R})),!F)return;const ee=l.endpoints[P].select(_)(H()),K=op(Z.providesTags,ee.data,void 0,_,{},u);G(l.internalActions.updateProvidedBy([{queryCacheKey:W,providedTags:K}]))};function y(P,_,R=0){const F=[_,...P];return R&&F.length>R?F.slice(0,-1):F}function v(P,_,R=0){const F=[...P,_];return R&&F.length>R?F.slice(1):F}const $=(P,_,R,F=!0)=>(G,H)=>{const Z=l.endpoints[P].select(_)(H()),W={patches:[],inversePatches:[],undo:()=>G(l.util.patchQueryData(P,_,W.inversePatches,F))};if(Z.status==="uninitialized")return W;let ee;if("data"in Z)if(Zn(Z.data)){const[K,re,le]=sy(Z.data,R);W.patches.push(...re),W.inversePatches.push(...le),ee=K}else ee=R(Z.data),W.patches.push({op:"replace",path:[],value:ee}),W.inversePatches.push({op:"replace",path:[],value:Z.data});return W.patches.length===0||G(l.util.patchQueryData(P,_,W.patches,F)),W},E=(P,_,R)=>F=>F(l.endpoints[P].initiate(_,{subscribe:!1,forceRefetch:!0,[Ki]:()=>({data:R})})),b=(P,_)=>P.query&&P[_]?P[_]:H1,T=async(P,{signal:_,abort:R,rejectWithValue:F,fulfillWithValue:G,dispatch:H,getState:Z,extra:W})=>{var ee,K;const re=o[P.endpointName],{metaSchema:le,skipSchemaValidation:fe=g}=re;try{let ce=b(re,"transformResponse");const ie={signal:_,abort:R,dispatch:H,getState:Z,extra:W,endpoint:P.endpointName,type:P.type,forced:P.type==="query"?N(P,Z()):void 0,queryCacheKey:P.type==="query"?P.queryCacheKey:void 0},he=P.type==="query"?P[Ki]:void 0;let be;const Se=async(Ne,Ee,Me,We)=>{if(Ee==null&&Ne.pages.length)return Promise.resolve({data:Ne});const dt={queryArg:P.originalArgs,pageParam:Ee},Ie=await qe(dt),tt=We?y:v;return{data:{pages:tt(Ne.pages,Ie.data,Me),pageParams:tt(Ne.pageParams,Ee,Me)},meta:Ie.meta}};async function qe(Ne){let Ee;const{extraOptions:Me,argSchema:We,rawResponseSchema:dt,responseSchema:Ie}=re;if(We&&!fe&&(Ne=await Do(We,Ne,"argSchema",{})),he?Ee=he():re.query?Ee=await r(re.query(Ne),ie,Me):Ee=await re.queryFn(Ne,ie,Me,Wt=>r(Wt,ie,Me)),typeof process<"u",Ee.error)throw new Ah(Ee.error,Ee.meta);let{data:tt}=Ee;dt&&!fe&&(tt=await Do(dt,Ee.data,"rawResponseSchema",Ee.meta));let de=await ce(tt,Ee.meta,Ne);return Ie&&!fe&&(de=await Do(Ie,de,"responseSchema",Ee.meta)),{...Ee,data:de}}if(P.type==="query"&&"infiniteQueryOptions"in re){const{infiniteQueryOptions:Ne}=re,{maxPages:Ee=1/0}=Ne;let Me;const We={pages:[],pageParams:[]},dt=(ee=d.selectQueryEntry(Z(),P.queryCacheKey))==null?void 0:ee.data,Ie=N(P,Z())&&!P.direction||!dt?We:dt;if("direction"in P&&P.direction&&Ie.pages.length){const tt=P.direction==="backward",de=(tt?vy:Ed)(Ne,Ie,P.originalArgs);Me=await Se(Ie,de,Ee,tt)}else{const{initialPageParam:tt=Ne.initialPageParam}=P,de=(dt==null?void 0:dt.pageParams)??[],Wt=de[0]??tt,_e=de.length;Me=await Se(Ie,Wt,Ee),he&&(Me={data:Me.data.pages[0]});for(let Ye=1;Ye<_e;Ye++){const ye=Ed(Ne,Me.data,P.originalArgs);Me=await Se(Me.data,ye,Ee)}}be=Me}else be=await qe(P.originalArgs);return le&&!fe&&be.meta&&(be.meta=await Do(le,be.meta,"metaSchema",be.meta)),G(be.data,Ei({fulfilledTimeStamp:Date.now(),baseQueryMeta:be.meta}))}catch(ce){let ie=ce;if(ie instanceof Ah){let he=b(re,"transformErrorResponse");const{rawErrorResponseSchema:be,errorResponseSchema:Se}=re;let{value:qe,meta:Ne}=ie;try{be&&!fe&&(qe=await Do(be,qe,"rawErrorResponseSchema",Ne)),le&&!fe&&(Ne=await Do(le,Ne,"metaSchema",Ne));let Ee=await he(qe,Ne,P.originalArgs);return Se&&!fe&&(Ee=await Do(Se,Ee,"errorResponseSchema",Ne)),F(Ee,Ei({baseQueryMeta:Ne}))}catch(Ee){ie=Ee}}try{if(ie instanceof yy){const he={endpoint:P.endpointName,arg:P.originalArgs,type:P.type,queryCacheKey:P.type==="query"?P.queryCacheKey:void 0};(K=re.onSchemaFailure)==null||K.call(re,ie,he),f==null||f(ie,he);const{catchSchemaFailure:be=m}=re;if(be)return F(be(ie,he),Ei({baseQueryMeta:ie._bqMeta}))}}catch(he){ie=he}throw typeof process<"u",console.error(ie),ie}};function N(P,_){const R=d.selectQueryEntry(_,P.queryCacheKey),F=d.selectConfig(_).refetchOnMountOrArgChange,G=R==null?void 0:R.fulfilledTimeStamp,H=P.forceRefetch??(P.subscribe&&F);return H?H===!0||(Number(new Date)-Number(G))/1e3>=H:!1}const A=()=>wh(`${e}/executeQuery`,T,{getPendingMeta({arg:P}){const _=o[P.endpointName];return Ei({startedTimeStamp:Date.now(),...ou(_)?{direction:P.direction}:{}})},condition(P,{getState:_}){var R;const F=_(),G=d.selectQueryEntry(F,P.queryCacheKey),H=G==null?void 0:G.fulfilledTimeStamp,Z=P.originalArgs,W=G==null?void 0:G.originalArgs,ee=o[P.endpointName],K=P.direction;return $d(P)?!0:(G==null?void 0:G.status)==="pending"?!1:N(P,F)||ru(ee)&&(R=ee==null?void 0:ee.forceRefetch)!=null&&R.call(ee,{currentArg:Z,previousArg:W,endpointState:G,state:F})?!0:!(H&&!K)},dispatchConditionRejection:!0}),x=A(),C=A(),k=wh(`${e}/executeMutation`,T,{getPendingMeta(){return Ei({startedTimeStamp:Date.now()})}}),M=P=>"force"in P,I=P=>"ifOlderThan"in P,L=(P,_,R)=>(F,G)=>{const H=M(R)&&R.force,Z=I(R)&&R.ifOlderThan,W=(K=!0)=>{const re={forceRefetch:K,isPrefetch:!0};return l.endpoints[P].initiate(_,re)},ee=l.endpoints[P].select(_)(G());if(H)F(W());else if(Z){const K=ee==null?void 0:ee.fulfilledTimeStamp;if(!K){F(W());return}(Number(new Date)-Number(new Date(K)))/1e3>=Z&&F(W())}else F(W(!1))};function z(P){return _=>{var R,F;return((F=(R=_==null?void 0:_.meta)==null?void 0:R.arg)==null?void 0:F.endpointName)===P}}function h(P,_){return{matchPending:Ii(Zd(P),z(_)),matchFulfilled:Ii(yo(P),z(_)),matchRejected:Ii(ja(P),z(_))}}return{queryThunk:x,mutationThunk:k,infiniteQueryThunk:C,prefetch:L,updateQueryData:$,upsertQueryData:E,patchQueryData:S,buildMatchThunkActions:h}}function Ed(e,{pages:r,pageParams:o},i){const l=r.length-1;return e.getNextPageParam(r[l],r,o[l],o,i)}function vy(e,{pages:r,pageParams:o},i){var l;return(l=e.getPreviousPageParam)==null?void 0:l.call(e,r[0],r,o[0],o,i)}function by(e,r,o,i){return op(o[e.meta.arg.endpointName][r],yo(e)?e.payload:void 0,nu(e)?e.payload:void 0,e.meta.arg.originalArgs,"baseQueryMeta"in e.meta?e.meta.baseQueryMeta:void 0,i)}function xs(e,r,o){const i=e[r];i&&o(i)}function Gi(e){return("arg"in e?e.arg.fixedCacheKey:e.fixedCacheKey)??e.requestId}function Ph(e,r,o){const i=e[Gi(r)];i&&o(i)}var ws={};function Z1({reducerPath:e,queryThunk:r,mutationThunk:o,serializeQueryArgs:i,context:{endpointDefinitions:l,apiUid:u,extractRehydrationInfo:d,hasRehydrationInfo:f},assertTagType:m,config:g}){const S=Hn(`${e}/resetApiState`);function y(z,h,P,_){var R;z[R=h.queryCacheKey]??(z[R]={status:"uninitialized",endpointName:h.endpointName}),xs(z,h.queryCacheKey,F=>{F.status="pending",F.requestId=P&&F.requestId?F.requestId:_.requestId,h.originalArgs!==void 0&&(F.originalArgs=h.originalArgs),F.startedTimeStamp=_.startedTimeStamp;const G=l[_.arg.endpointName];ou(G)&&"direction"in h&&(F.direction=h.direction)})}function v(z,h,P,_){xs(z,h.arg.queryCacheKey,R=>{if(R.requestId!==h.requestId&&!_)return;const{merge:F}=l[h.arg.endpointName];if(R.status="fulfilled",F)if(R.data!==void 0){const{fulfilledTimeStamp:G,arg:H,baseQueryMeta:Z,requestId:W}=h;let ee=ol(R.data,K=>F(K,P,{arg:H.originalArgs,baseQueryMeta:Z,fulfilledTimeStamp:G,requestId:W}));R.data=ee}else R.data=P;else R.data=l[h.arg.endpointName].structuralSharing??!0?my(yr(R.data)?Lb(R.data):R.data,P):P;delete R.error,R.fulfilledTimeStamp=h.fulfilledTimeStamp})}const $=Lo({name:`${e}/queries`,initialState:ws,reducers:{removeQueryResult:{reducer(z,{payload:{queryCacheKey:h}}){delete z[h]},prepare:$i()},cacheEntriesUpserted:{reducer(z,h){for(const P of h.payload){const{queryDescription:_,value:R}=P;y(z,_,!0,{arg:_,requestId:h.meta.requestId,startedTimeStamp:h.meta.timestamp}),v(z,{arg:_,requestId:h.meta.requestId,fulfilledTimeStamp:h.meta.timestamp,baseQueryMeta:{}},R,!0)}},prepare:z=>({payload:z.map(h=>{const{endpointName:P,arg:_,value:R}=h,F=l[P];return{queryDescription:{type:"query",endpointName:P,originalArgs:h.arg,queryCacheKey:i({queryArgs:_,endpointDefinition:F,endpointName:P})},value:R}}),meta:{[eu]:!0,requestId:ep(),timestamp:Date.now()}})},queryResultPatched:{reducer(z,{payload:{queryCacheKey:h,patches:P}}){xs(z,h,_=>{_.data=gh(_.data,P.concat())})},prepare:$i()}},extraReducers(z){z.addCase(r.pending,(h,{meta:P,meta:{arg:_}})=>{const R=$d(_);y(h,_,R,P)}).addCase(r.fulfilled,(h,{meta:P,payload:_})=>{const R=$d(P.arg);v(h,P,_,R)}).addCase(r.rejected,(h,{meta:{condition:P,arg:_,requestId:R},error:F,payload:G})=>{xs(h,_.queryCacheKey,H=>{if(!P){if(H.requestId!==R)return;H.status="rejected",H.error=G??F}})}).addMatcher(f,(h,P)=>{const{queries:_}=d(P);for(const[R,F]of Object.entries(_))((F==null?void 0:F.status)==="fulfilled"||(F==null?void 0:F.status)==="rejected")&&(h[R]=F)})}}),E=Lo({name:`${e}/mutations`,initialState:ws,reducers:{removeMutationResult:{reducer(z,{payload:h}){const P=Gi(h);P in z&&delete z[P]},prepare:$i()}},extraReducers(z){z.addCase(o.pending,(h,{meta:P,meta:{requestId:_,arg:R,startedTimeStamp:F}})=>{R.track&&(h[Gi(P)]={requestId:_,status:"pending",endpointName:R.endpointName,startedTimeStamp:F})}).addCase(o.fulfilled,(h,{payload:P,meta:_})=>{_.arg.track&&Ph(h,_,R=>{R.requestId===_.requestId&&(R.status="fulfilled",R.data=P,R.fulfilledTimeStamp=_.fulfilledTimeStamp)})}).addCase(o.rejected,(h,{payload:P,error:_,meta:R})=>{R.arg.track&&Ph(h,R,F=>{F.requestId===R.requestId&&(F.status="rejected",F.error=P??_)})}).addMatcher(f,(h,P)=>{const{mutations:_}=d(P);for(const[R,F]of Object.entries(_))((F==null?void 0:F.status)==="fulfilled"||(F==null?void 0:F.status)==="rejected")&&R!==(F==null?void 0:F.requestId)&&(h[R]=F)})}}),b={tags:{},keys:{}},T=Lo({name:`${e}/invalidation`,initialState:b,reducers:{updateProvidedBy:{reducer(z,h){var P,_,R;for(const{queryCacheKey:F,providedTags:G}of h.payload){N(z,F);for(const{type:H,id:Z}of G){const W=(_=(P=z.tags)[H]??(P[H]={}))[R=Z||"__internal_without_id"]??(_[R]=[]);W.includes(F)||W.push(F)}z.keys[F]=G}},prepare:$i()}},extraReducers(z){z.addCase($.actions.removeQueryResult,(h,{payload:{queryCacheKey:P}})=>{N(h,P)}).addMatcher(f,(h,P)=>{var _,R,F;const{provided:G}=d(P);for(const[H,Z]of Object.entries(G))for(const[W,ee]of Object.entries(Z)){const K=(R=(_=h.tags)[H]??(_[H]={}))[F=W||"__internal_without_id"]??(R[F]=[]);for(const re of ee)K.includes(re)||K.push(re)}}).addMatcher(Lr(yo(r),nu(r)),(h,P)=>{A(h,[P])}).addMatcher($.actions.cacheEntriesUpserted.match,(h,P)=>{const _=P.payload.map(({queryDescription:R,value:F})=>({type:"UNKNOWN",payload:F,meta:{requestStatus:"fulfilled",requestId:"UNKNOWN",arg:R}}));A(h,_)})}});function N(z,h){var P;const _=z.keys[h]??[];for(const R of _){const F=R.type,G=R.id??"__internal_without_id",H=(P=z.tags[F])==null?void 0:P[G];H&&(z.tags[F][G]=H.filter(Z=>Z!==h))}delete z.keys[h]}function A(z,h){const P=h.map(_=>{const R=by(_,"providesTags",l,m),{queryCacheKey:F}=_.meta.arg;return{queryCacheKey:F,providedTags:R}});T.caseReducers.updateProvidedBy(z,T.actions.updateProvidedBy(P))}const x=Lo({name:`${e}/subscriptions`,initialState:ws,reducers:{updateSubscriptionOptions(z,h){},unsubscribeQueryResult(z,h){},internal_getRTKQSubscriptions(){}}}),C=Lo({name:`${e}/internalSubscriptions`,initialState:ws,reducers:{subscriptionsUpdated:{reducer(z,h){return gh(z,h.payload)},prepare:$i()}}}),k=Lo({name:`${e}/config`,initialState:{online:F1(),focused:D1(),middlewareRegistered:!1,...g},reducers:{middlewareRegistered(z,{payload:h}){z.middlewareRegistered=z.middlewareRegistered==="conflict"||u!==h?"conflict":!0}},extraReducers:z=>{z.addCase(rp,h=>{h.online=!0}).addCase(gy,h=>{h.online=!1}).addCase(np,h=>{h.focused=!0}).addCase(hy,h=>{h.focused=!1}).addMatcher(f,h=>({...h}))}}),M=ty({queries:$.reducer,mutations:E.reducer,provided:T.reducer,subscriptions:C.reducer,config:k.reducer}),I=(z,h)=>M(S.match(h)?void 0:z,h),L={...k.actions,...$.actions,...x.actions,...C.actions,...E.actions,...T.actions,resetApiState:S};return{reducer:I,actions:L}}var Yn=Symbol.for("RTKQ/skipToken"),xy={status:"uninitialized"},_h=ol(xy,()=>{}),Rh=ol(xy,()=>{});function ex({serializeQueryArgs:e,reducerPath:r,createSelector:o}){const i=x=>_h,l=x=>Rh;return{buildQuerySelector:v,buildInfiniteQuerySelector:$,buildMutationSelector:E,selectInvalidatedBy:b,selectCachedArgsForQuery:T,selectApiState:d,selectQueries:f,selectMutations:g,selectQueryEntry:m,selectConfig:S};function u(x){return{...x,...Ch(x.status)}}function d(x){return x[r]}function f(x){var C;return(C=d(x))==null?void 0:C.queries}function m(x,C){var k;return(k=f(x))==null?void 0:k[C]}function g(x){var C;return(C=d(x))==null?void 0:C.mutations}function S(x){var C;return(C=d(x))==null?void 0:C.config}function y(x,C,k){return M=>{if(M===Yn)return o(i,k);const I=e({queryArgs:M,endpointDefinition:C,endpointName:x});return o(L=>m(L,I)??_h,k)}}function v(x,C){return y(x,C,u)}function $(x,C){const{infiniteQueryOptions:k}=C;function M(I){const L={...I,...Ch(I.status)},{isLoading:z,isError:h,direction:P}=L,_=P==="forward",R=P==="backward";return{...L,hasNextPage:N(k,L.data,L.originalArgs),hasPreviousPage:A(k,L.data,L.originalArgs),isFetchingNextPage:z&&_,isFetchingPreviousPage:z&&R,isFetchNextPageError:h&&_,isFetchPreviousPageError:h&&R}}return y(x,C,M)}function E(){return x=>{let C;return typeof x=="object"?C=Gi(x)??Yn:C=x,o(C===Yn?l:k=>{var M,I;return((I=(M=d(k))==null?void 0:M.mutations)==null?void 0:I[C])??Rh},u)}}function b(x,C){const k=x[r],M=new Set;for(const I of C.filter(Ws).map(Cd)){const L=k.provided.tags[I.type];if(!L)continue;let z=(I.id!==void 0?L[I.id]:Eh(Object.values(L)))??[];for(const h of z)M.add(h)}return Eh(Array.from(M.values()).map(I=>{const L=k.queries[I];return L?[{queryCacheKey:I,endpointName:L.endpointName,originalArgs:L.originalArgs}]:[]}))}function T(x,C){return Object.values(f(x)).filter(k=>(k==null?void 0:k.endpointName)===C&&k.status!=="uninitialized").map(k=>k.originalArgs)}function N(x,C,k){return C?Ed(x,C,k)!=null:!1}function A(x,C,k){return!C||!x.getPreviousPageParam?!1:vy(x,C,k)!=null}}var Ss=WeakMap?new WeakMap:void 0,Qs=({endpointName:e,queryArgs:r})=>{let o="";const i=Ss==null?void 0:Ss.get(r);if(typeof i=="string")o=i;else{const l=JSON.stringify(r,(u,d)=>(d=typeof d=="bigint"?{$bigint:d.toString()}:d,d=go(d)?Object.keys(d).sort().reduce((f,m)=>(f[m]=d[m],f),{}):d,d));go(r)&&(Ss==null||Ss.set(r,l)),o=l}return`${e}(${o})`};function tx(...e){return function(r){const o=qs(m=>{var g;return(g=r.extractRehydrationInfo)==null?void 0:g.call(r,m,{reducerPath:r.reducerPath??"api"})}),i={reducerPath:"api",keepUnusedDataFor:60,refetchOnMountOrArgChange:!1,refetchOnFocus:!1,refetchOnReconnect:!1,invalidationBehavior:"delayed",...r,extractRehydrationInfo:o,serializeQueryArgs(m){let g=Qs;if("serializeQueryArgs"in m.endpointDefinition){const S=m.endpointDefinition.serializeQueryArgs;g=y=>{const v=S(y);return typeof v=="string"?v:Qs({...y,queryArgs:v})}}else r.serializeQueryArgs&&(g=r.serializeQueryArgs);return g(m)},tagTypes:[...r.tagTypes||[]]},l={endpointDefinitions:{},batch(m){m()},apiUid:ep(),extractRehydrationInfo:o,hasRehydrationInfo:qs(m=>o(m)!=null)},u={injectEndpoints:f,enhanceEndpoints({addTagTypes:m,endpoints:g}){if(m)for(const S of m)i.tagTypes.includes(S)||i.tagTypes.push(S);if(g)for(const[S,y]of Object.entries(g))typeof y=="function"?y(l.endpointDefinitions[S]):Object.assign(l.endpointDefinitions[S]||{},y);return u}},d=e.map(m=>m.init(u,i,l));function f(m){const g=m.endpoints({query:S=>({...S,type:"query"}),mutation:S=>({...S,type:"mutation"}),infiniteQuery:S=>({...S,type:"infinitequery"})});for(const[S,y]of Object.entries(g)){if(m.overrideExisting!==!0&&S in l.endpointDefinitions){if(m.overrideExisting==="throw")throw new Error(Jn(39));typeof process<"u";continue}typeof process<"u",l.endpointDefinitions[S]=y;for(const v of d)v.injectEndpoint(S,y)}return u}return u.injectEndpoints({endpoints:r.endpoints})}}function Ir(e,...r){return Object.assign(e,...r)}var nx=({api:e,queryThunk:r,internalState:o})=>{const i=`${e.reducerPath}/subscriptions`;let l=null,u=null;const{updateSubscriptionOptions:d,unsubscribeQueryResult:f}=e.internalActions,m=(y,v)=>{var $,E,b;if(d.match(v)){const{queryCacheKey:N,requestId:A,options:x}=v.payload;return($=y==null?void 0:y[N])!=null&&$[A]&&(y[N][A]=x),!0}if(f.match(v)){const{queryCacheKey:N,requestId:A}=v.payload;return y[N]&&delete y[N][A],!0}if(e.internalActions.removeQueryResult.match(v))return delete y[v.payload.queryCacheKey],!0;if(r.pending.match(v)){const{meta:{arg:N,requestId:A}}=v,x=y[E=N.queryCacheKey]??(y[E]={});return x[`${A}_running`]={},N.subscribe&&(x[A]=N.subscriptionOptions??x[A]??{}),!0}let T=!1;if(r.fulfilled.match(v)||r.rejected.match(v)){const N=y[v.meta.arg.queryCacheKey]||{},A=`${v.meta.requestId}_running`;T||(T=!!N[A]),delete N[A]}if(r.rejected.match(v)){const{meta:{condition:N,arg:A,requestId:x}}=v;if(N&&A.subscribe){const C=y[b=A.queryCacheKey]??(y[b]={});C[x]=A.subscriptionOptions??C[x]??{},T=!0}}return T},g=()=>o.currentSubscriptions,S={getSubscriptions:g,getSubscriptionCount:y=>{const v=g()[y]??{};return _a(v)},isRequestSubscribed:(y,v)=>{var $;const E=g();return!!(($=E==null?void 0:E[y])!=null&&$[v])}};return(y,v)=>{if(l||(l=JSON.parse(JSON.stringify(o.currentSubscriptions))),e.util.resetApiState.match(y))return l=o.currentSubscriptions={},u=null,[!0,!1];if(e.internalActions.internal_getRTKQSubscriptions.match(y))return[!1,S];const $=m(o.currentSubscriptions,y);let E=!0;if($){u||(u=setTimeout(()=>{const N=JSON.parse(JSON.stringify(o.currentSubscriptions)),[,A]=sy(l,()=>N);v.next(e.internalActions.subscriptionsUpdated(A)),l=N,u=null},500));const b=typeof y.type=="string"&&!!y.type.startsWith(i),T=r.rejected.match(y)&&y.meta.condition&&!!y.meta.arg.subscribe;E=!b&&!T}return[E,!1]}};function rx(e){for(const r in e)return!1;return!0}var ox=2147483647/1e3-1,ax=({reducerPath:e,api:r,queryThunk:o,context:i,internalState:l,selectors:{selectQueryEntry:u,selectConfig:d}})=>{const{removeQueryResult:f,unsubscribeQueryResult:m,cacheEntriesUpserted:g}=r.internalActions,S=Lr(m.match,o.fulfilled,o.rejected,g.match);function y(T){const N=l.currentSubscriptions[T];return!!N&&!rx(N)}const v={},$=(T,N,A)=>{const x=N.getState(),C=d(x);if(S(T)){let k;if(g.match(T))k=T.payload.map(M=>M.queryDescription.queryCacheKey);else{const{queryCacheKey:M}=m.match(T)?T.payload:T.meta.arg;k=[M]}E(k,N,C)}if(r.util.resetApiState.match(T))for(const[k,M]of Object.entries(v))M&&clearTimeout(M),delete v[k];if(i.hasRehydrationInfo(T)){const{queries:k}=i.extractRehydrationInfo(T);E(Object.keys(k),N,C)}};function E(T,N,A){const x=N.getState();for(const C of T){const k=u(x,C);b(C,k==null?void 0:k.endpointName,N,A)}}function b(T,N,A,x){const C=i.endpointDefinitions[N],k=(C==null?void 0:C.keepUnusedDataFor)??x.keepUnusedDataFor;if(k===1/0)return;const M=Math.max(0,Math.min(k,ox));if(!y(T)){const I=v[T];I&&clearTimeout(I),v[T]=setTimeout(()=>{y(T)||A.dispatch(f({queryCacheKey:T})),delete v[T]},M*1e3)}}return $},Mh=new Error("Promise never resolved before cacheEntryRemoved."),ix=({api:e,reducerPath:r,context:o,queryThunk:i,mutationThunk:l,internalState:u,selectors:{selectQueryEntry:d,selectApiState:f}})=>{const m=kd(i),g=kd(l),S=yo(i,l),y={};function v(N,A,x){const C=y[N];C!=null&&C.valueResolved&&(C.valueResolved({data:A,meta:x}),delete C.valueResolved)}function $(N){const A=y[N];A&&(delete y[N],A.cacheEntryRemoved())}const E=(N,A,x)=>{const C=b(N);function k(M,I,L,z){const h=d(x,I),P=d(A.getState(),I);!h&&P&&T(M,z,I,A,L)}if(i.pending.match(N))k(N.meta.arg.endpointName,C,N.meta.requestId,N.meta.arg.originalArgs);else if(e.internalActions.cacheEntriesUpserted.match(N))for(const{queryDescription:M,value:I}of N.payload){const{endpointName:L,originalArgs:z,queryCacheKey:h}=M;k(L,h,N.meta.requestId,z),v(h,I,{})}else if(l.pending.match(N))A.getState()[r].mutations[C]&&T(N.meta.arg.endpointName,N.meta.arg.originalArgs,C,A,N.meta.requestId);else if(S(N))v(C,N.payload,N.meta.baseQueryMeta);else if(e.internalActions.removeQueryResult.match(N)||e.internalActions.removeMutationResult.match(N))$(C);else if(e.util.resetApiState.match(N))for(const M of Object.keys(y))$(M)};function b(N){return m(N)?N.meta.arg.queryCacheKey:g(N)?N.meta.arg.fixedCacheKey??N.meta.requestId:e.internalActions.removeQueryResult.match(N)?N.payload.queryCacheKey:e.internalActions.removeMutationResult.match(N)?Gi(N.payload):""}function T(N,A,x,C,k){const M=o.endpointDefinitions[N],I=M==null?void 0:M.onCacheEntryAdded;if(!I)return;const L={},z=new Promise(G=>{L.cacheEntryRemoved=G}),h=Promise.race([new Promise(G=>{L.valueResolved=G}),z.then(()=>{throw Mh})]);h.catch(()=>{}),y[x]=L;const P=e.endpoints[N].select(Us(M)?A:x),_=C.dispatch((G,H,Z)=>Z),R={...C,getCacheEntry:()=>P(C.getState()),requestId:k,extra:_,updateCachedData:Us(M)?G=>C.dispatch(e.util.updateQueryData(N,A,G)):void 0,cacheDataLoaded:h,cacheEntryRemoved:z},F=I(A,R);Promise.resolve(F).catch(G=>{if(G!==Mh)throw G})}return E},lx=({api:e,context:{apiUid:r},reducerPath:o})=>(i,l)=>{var u,d;e.util.resetApiState.match(i)&&l.dispatch(e.internalActions.middlewareRegistered(r)),typeof process<"u"},sx=({reducerPath:e,context:r,context:{endpointDefinitions:o},mutationThunk:i,queryThunk:l,api:u,assertTagType:d,refetchQuery:f,internalState:m})=>{const{removeQueryResult:g}=u.internalActions,S=Lr(yo(i),nu(i)),y=Lr(yo(i,l),ja(i,l));let v=[];const $=(T,N)=>{S(T)?b(by(T,"invalidatesTags",o,d),N):y(T)?b([],N):u.util.invalidateTags.match(T)&&b(op(T.payload,void 0,void 0,void 0,void 0,d),N)};function E(T){var N;const{queries:A,mutations:x}=T;for(const C of[A,x])for(const k in C)if(((N=C[k])==null?void 0:N.status)==="pending")return!0;return!1}function b(T,N){const A=N.getState(),x=A[e];if(v.push(...T),x.config.invalidationBehavior==="delayed"&&E(x))return;const C=v;if(v=[],C.length===0)return;const k=u.util.selectInvalidatedBy(A,C);r.batch(()=>{const M=Array.from(k.values());for(const{queryCacheKey:I}of M){const L=x.queries[I],z=m.currentSubscriptions[I]??{};L&&(_a(z)===0?N.dispatch(g({queryCacheKey:I})):L.status!=="uninitialized"&&N.dispatch(f(L)))}})}return $},ux=({reducerPath:e,queryThunk:r,api:o,refetchQuery:i,internalState:l})=>{const u={},d=(v,$)=>{(o.internalActions.updateSubscriptionOptions.match(v)||o.internalActions.unsubscribeQueryResult.match(v))&&m(v.payload,$),(r.pending.match(v)||r.rejected.match(v)&&v.meta.condition)&&m(v.meta.arg,$),(r.fulfilled.match(v)||r.rejected.match(v)&&!v.meta.condition)&&f(v.meta.arg,$),o.util.resetApiState.match(v)&&S()};function f({queryCacheKey:v},$){const E=$.getState()[e],b=E.queries[v],T=l.currentSubscriptions[v];if(!b||b.status==="uninitialized")return;const{lowestPollingInterval:N,skipPollingIfUnfocused:A}=y(T);if(!Number.isFinite(N))return;const x=u[v];x!=null&&x.timeout&&(clearTimeout(x.timeout),x.timeout=void 0);const C=Date.now()+N;u[v]={nextPollTimestamp:C,pollingInterval:N,timeout:setTimeout(()=>{(E.config.focused||!A)&&$.dispatch(i(b)),f({queryCacheKey:v},$)},N)}}function m({queryCacheKey:v},$){const E=$.getState()[e].queries[v],b=l.currentSubscriptions[v];if(!E||E.status==="uninitialized")return;const{lowestPollingInterval:T}=y(b);if(!Number.isFinite(T)){g(v);return}const N=u[v],A=Date.now()+T;(!N||A<N.nextPollTimestamp)&&f({queryCacheKey:v},$)}function g(v){const $=u[v];$!=null&&$.timeout&&clearTimeout($.timeout),delete u[v]}function S(){for(const v of Object.keys(u))g(v)}function y(v={}){let $=!1,E=Number.POSITIVE_INFINITY;for(let b in v)v[b].pollingInterval&&(E=Math.min(v[b].pollingInterval,E),$=v[b].skipPollingIfUnfocused||$);return{lowestPollingInterval:E,skipPollingIfUnfocused:$}}return d},cx=({api:e,context:r,queryThunk:o,mutationThunk:i})=>{const l=Zd(o,i),u=ja(o,i),d=yo(o,i),f={};return(m,g)=>{var S,y;if(l(m)){const{requestId:v,arg:{endpointName:$,originalArgs:E}}=m.meta,b=r.endpointDefinitions[$],T=b==null?void 0:b.onQueryStarted;if(T){const N={},A=new Promise((M,I)=>{N.resolve=M,N.reject=I});A.catch(()=>{}),f[v]=N;const x=e.endpoints[$].select(Us(b)?E:v),C=g.dispatch((M,I,L)=>L),k={...g,getCacheEntry:()=>x(g.getState()),requestId:v,extra:C,updateCachedData:Us(b)?M=>g.dispatch(e.util.updateQueryData($,E,M)):void 0,queryFulfilled:A};T(E,k)}}else if(d(m)){const{requestId:v,baseQueryMeta:$}=m.meta;(S=f[v])==null||S.resolve({data:m.payload,meta:$}),delete f[v]}else if(u(m)){const{requestId:v,rejectedWithValue:$,baseQueryMeta:E}=m.meta;(y=f[v])==null||y.reject({error:m.payload??m.error,isUnhandledError:!$,meta:E}),delete f[v]}}},dx=({reducerPath:e,context:r,api:o,refetchQuery:i,internalState:l})=>{const{removeQueryResult:u}=o.internalActions,d=(m,g)=>{np.match(m)&&f(g,"refetchOnFocus"),rp.match(m)&&f(g,"refetchOnReconnect")};function f(m,g){const S=m.getState()[e],y=S.queries,v=l.currentSubscriptions;r.batch(()=>{for(const $ of Object.keys(v)){const E=y[$],b=v[$];!b||!E||(Object.values(b).some(T=>T[g]===!0)||Object.values(b).every(T=>T[g]===void 0)&&S.config[g])&&(_a(b)===0?m.dispatch(u({queryCacheKey:$})):E.status!=="uninitialized"&&m.dispatch(i(E)))}})}return d};function px(e){const{reducerPath:r,queryThunk:o,api:i,context:l}=e,{apiUid:u}=l,d={invalidateTags:Hn(`${r}/invalidateTags`)},f=S=>S.type.startsWith(`${r}/`),m=[lx,ax,sx,ux,ix,cx];return{middleware:S=>{let y=!1;const v={...e,internalState:{currentSubscriptions:{}},refetchQuery:g,isThisApiSliceAction:f},$=m.map(T=>T(v)),E=nx(v),b=dx(v);return T=>N=>{if(!ny(N))return T(N);y||(y=!0,S.dispatch(i.internalActions.middlewareRegistered(u)));const A={...S,next:T},x=S.getState(),[C,k]=E(N,A,x);let M;if(C?M=T(N):M=k,S.getState()[r]&&(b(N,A,x),f(N)||l.hasRehydrationInfo(N)))for(const I of $)I(N,A,x);return M}},actions:d};function g(S){return e.api.endpoints[S.endpointName].initiate(S.originalArgs,{subscribe:!1,forceRefetch:!0})}}var Oh=Symbol(),fx=({createSelector:e=tp}={})=>({name:Oh,init(r,{baseQuery:o,tagTypes:i,reducerPath:l,serializeQueryArgs:u,keepUnusedDataFor:d,refetchOnMountOrArgChange:f,refetchOnFocus:m,refetchOnReconnect:g,invalidationBehavior:S,onSchemaFailure:y,catchSchemaFailure:v,skipSchemaValidation:$},E){Hb();const b=ie=>(typeof process<"u",ie);Object.assign(r,{reducerPath:l,endpoints:{},internalActions:{onOnline:rp,onOffline:gy,onFocus:np,onFocusLost:hy},util:{}});const T=ex({serializeQueryArgs:u,reducerPath:l,createSelector:e}),{selectInvalidatedBy:N,selectCachedArgsForQuery:A,buildQuerySelector:x,buildInfiniteQuerySelector:C,buildMutationSelector:k}=T;Ir(r.util,{selectInvalidatedBy:N,selectCachedArgsForQuery:A});const{queryThunk:M,infiniteQueryThunk:I,mutationThunk:L,patchQueryData:z,updateQueryData:h,upsertQueryData:P,prefetch:_,buildMatchThunkActions:R}=J1({baseQuery:o,reducerPath:l,context:E,api:r,serializeQueryArgs:u,assertTagType:b,selectors:T,onSchemaFailure:y,catchSchemaFailure:v,skipSchemaValidation:$}),{reducer:F,actions:G}=Z1({context:E,queryThunk:M,mutationThunk:L,serializeQueryArgs:u,reducerPath:l,assertTagType:b,config:{refetchOnFocus:m,refetchOnReconnect:g,refetchOnMountOrArgChange:f,keepUnusedDataFor:d,reducerPath:l,invalidationBehavior:S}});Ir(r.util,{patchQueryData:z,updateQueryData:h,upsertQueryData:P,prefetch:_,resetApiState:G.resetApiState,upsertQueryEntries:G.cacheEntriesUpserted}),Ir(r.internalActions,G);const{middleware:H,actions:Z}=px({reducerPath:l,context:E,queryThunk:M,mutationThunk:L,infiniteQueryThunk:I,api:r,assertTagType:b,selectors:T});Ir(r.util,Z),Ir(r,{reducer:F,middleware:H});const{buildInitiateQuery:W,buildInitiateInfiniteQuery:ee,buildInitiateMutation:K,getRunningMutationThunk:re,getRunningMutationsThunk:le,getRunningQueriesThunk:fe,getRunningQueryThunk:ce}=X1({queryThunk:M,mutationThunk:L,infiniteQueryThunk:I,api:r,serializeQueryArgs:u,context:E});return Ir(r.util,{getRunningMutationThunk:re,getRunningMutationsThunk:le,getRunningQueryThunk:ce,getRunningQueriesThunk:fe}),{name:Oh,injectEndpoint(ie,he){var be;const Se=(be=r.endpoints)[ie]??(be[ie]={});ru(he)&&Ir(Se,{name:ie,select:x(ie,he),initiate:W(ie,he)},R(M,ie)),K1(he)&&Ir(Se,{name:ie,select:k(),initiate:K(ie)},R(L,ie)),ou(he)&&Ir(Se,{name:ie,select:C(ie,he),initiate:ee(ie,he)},R(M,ie))}}}}),wy={exports:{}},nd={};/**
 * @license React
 * use-sync-external-store-with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var jh;function mx(){if(jh)return nd;jh=1;var e=Xn;function r(m,g){return m===g&&(m!==0||1/m===1/g)||m!==m&&g!==g}var o=typeof Object.is=="function"?Object.is:r,i=e.useSyncExternalStore,l=e.useRef,u=e.useEffect,d=e.useMemo,f=e.useDebugValue;return nd.useSyncExternalStoreWithSelector=function(m,g,S,y,v){var $=l(null);if($.current===null){var E={hasValue:!1,value:null};$.current=E}else E=$.current;$=d(function(){function T(k){if(!N){if(N=!0,A=k,k=y(k),v!==void 0&&E.hasValue){var M=E.value;if(v(M,k))return x=M}return x=k}if(M=x,o(A,k))return M;var I=y(k);return v!==void 0&&v(M,I)?(A=k,M):(A=k,x=I)}var N=!1,A,x,C=S===void 0?null:S;return[function(){return T(g())},C===null?void 0:function(){return T(C())}]},[g,S,y,v]);var b=i(m,$[0],$[1]);return u(function(){E.hasValue=!0,E.value=b},[b]),f(b),b},nd}wy.exports=mx();var hx=wy.exports;function Sy(e){e()}function gx(){let e=null,r=null;return{clear(){e=null,r=null},notify(){Sy(()=>{let o=e;for(;o;)o.callback(),o=o.next})},get(){const o=[];let i=e;for(;i;)o.push(i),i=i.next;return o},subscribe(o){let i=!0;const l=r={callback:o,next:null,prev:r};return l.prev?l.prev.next=l:e=l,function(){!i||e===null||(i=!1,l.next?l.next.prev=l.prev:r=l.prev,l.prev?l.prev.next=l.next:e=l.next)}}}}var Ih={notify(){},get:()=>[]};function yx(e,r){let o,i=Ih,l=0,u=!1;function d(b){S();const T=i.subscribe(b);let N=!1;return()=>{N||(N=!0,T(),y())}}function f(){i.notify()}function m(){E.onStateChange&&E.onStateChange()}function g(){return u}function S(){l++,o||(o=e.subscribe(m),i=gx())}function y(){l--,o&&l===0&&(o(),o=void 0,i.clear(),i=Ih)}function v(){u||(u=!0,S())}function $(){u&&(u=!1,y())}const E={addNestedSub:d,notifyNestedSubs:f,handleChangeWrapper:m,isSubscribed:g,trySubscribe:v,tryUnsubscribe:$,getListeners:()=>i};return E}var vx=()=>typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",bx=vx(),xx=()=>typeof navigator<"u"&&navigator.product==="ReactNative",wx=xx(),Sx=()=>bx||wx?j.useLayoutEffect:j.useEffect,kx=Sx();function zh(e,r){return e===r?e!==0||r!==0||1/e===1/r:e!==e&&r!==r}function zi(e,r){if(zh(e,r))return!0;if(typeof e!="object"||e===null||typeof r!="object"||r===null)return!1;const o=Object.keys(e),i=Object.keys(r);if(o.length!==i.length)return!1;for(let l=0;l<o.length;l++)if(!Object.prototype.hasOwnProperty.call(r,o[l])||!zh(e[o[l]],r[o[l]]))return!1;return!0}var Dh=Symbol.for("react-redux-context"),Fh=typeof globalThis<"u"?globalThis:{};function Cx(){if(!j.createContext)return{};const e=Fh[Dh]??(Fh[Dh]=new Map);let r=e.get(j.createContext);return r||(r=j.createContext(null),e.set(j.createContext,r)),r}var vo=Cx();function $x(e){const{children:r,context:o,serverState:i,store:l}=e,u=j.useMemo(()=>{const m=yx(l);return{store:l,subscription:m,getServerState:i?()=>i:void 0}},[l,i]),d=j.useMemo(()=>l.getState(),[l]);kx(()=>{const{subscription:m}=u;return m.onStateChange=m.notifyNestedSubs,m.trySubscribe(),d!==l.getState()&&m.notifyNestedSubs(),()=>{m.tryUnsubscribe(),m.onStateChange=void 0}},[u,d]);const f=o||vo;return j.createElement(f.Provider,{value:u},r)}var Ex=$x;function ap(e=vo){return function(){return j.useContext(e)}}var ky=ap();function Cy(e=vo){const r=e===vo?ky:ap(e),o=()=>{const{store:i}=r();return i};return Object.assign(o,{withTypes:()=>o}),o}var $y=Cy();function Tx(e=vo){const r=e===vo?$y:Cy(e),o=()=>r().dispatch;return Object.assign(o,{withTypes:()=>o}),o}var Nx=Tx(),Ax=(e,r)=>e===r;function Px(e=vo){const r=e===vo?ky:ap(e),o=(i,l={})=>{const{equalityFn:u=Ax}=typeof l=="function"?{equalityFn:l}:l,d=r(),{store:f,subscription:m,getServerState:g}=d;j.useRef(!0);const S=j.useCallback({[i.name](v){return i(v)}}[i.name],[i]),y=hx.useSyncExternalStoreWithSelector(m.addNestedSub,f.getState,g||f.getState,S,u);return j.useDebugValue(y),y};return Object.assign(o,{withTypes:()=>o}),o}var _x=Px(),Rx=Sy;function ks(e){return e.replace(e[0],e[0].toUpperCase())}function Mx(e){return e.type==="query"}function Ox(e){return e.type==="mutation"}function Ey(e){return e.type==="infinitequery"}function Ti(e,...r){return Object.assign(e,...r)}var rd=Symbol();function od(e,r,o,i){const l=j.useMemo(()=>({queryArgs:e,serialized:typeof e=="object"?r({queryArgs:e,endpointDefinition:o,endpointName:i}):e}),[e,r,o,i]),u=j.useRef(l);return j.useEffect(()=>{u.current.serialized!==l.serialized&&(u.current=l)},[l]),u.current.serialized===l.serialized?u.current.queryArgs:e}function Cs(e){const r=j.useRef(e);return j.useEffect(()=>{zi(r.current,e)||(r.current=e)},[e]),zi(r.current,e)?r.current:e}var jx=()=>typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Ix=jx(),zx=()=>typeof navigator<"u"&&navigator.product==="ReactNative",Dx=zx(),Fx=()=>Ix||Dx?j.useLayoutEffect:j.useEffect,Bx=Fx(),Bh=e=>e.isUninitialized?{...e,isUninitialized:!1,isFetching:!0,isLoading:e.data===void 0,status:fy.pending}:e;function ad(e,...r){const o={};return r.forEach(i=>{o[i]=e[i]}),o}var id=["data","status","isLoading","isSuccess","isError","error"];function Lx({api:e,moduleOptions:{batch:r,hooks:{useDispatch:o,useSelector:i,useStore:l},unstable__sideEffectsInRender:u,createSelector:d},serializeQueryArgs:f,context:m}){const g=u?C=>C():j.useEffect;return{buildQueryHooks:N,buildInfiniteQueryHooks:A,buildMutationHook:x,usePrefetch:v};function S(C,k,M){if(k!=null&&k.endpointName&&C.isUninitialized){const{endpointName:_}=k,R=m.endpointDefinitions[_];M!==Yn&&f({queryArgs:k.originalArgs,endpointDefinition:R,endpointName:_})===f({queryArgs:M,endpointDefinition:R,endpointName:_})&&(k=void 0)}let I=C.isSuccess?C.data:k==null?void 0:k.data;I===void 0&&(I=C.data);const L=I!==void 0,z=C.isLoading,h=(!k||k.isLoading||k.isUninitialized)&&!L&&z,P=C.isSuccess||L&&(z&&!(k!=null&&k.isError)||C.isUninitialized);return{...C,data:I,currentData:C.data,isFetching:z,isLoading:h,isSuccess:P}}function y(C,k,M){if(k!=null&&k.endpointName&&C.isUninitialized){const{endpointName:_}=k,R=m.endpointDefinitions[_];M!==Yn&&f({queryArgs:k.originalArgs,endpointDefinition:R,endpointName:_})===f({queryArgs:M,endpointDefinition:R,endpointName:_})&&(k=void 0)}let I=C.isSuccess?C.data:k==null?void 0:k.data;I===void 0&&(I=C.data);const L=I!==void 0,z=C.isLoading,h=(!k||k.isLoading||k.isUninitialized)&&!L&&z,P=C.isSuccess||z&&L;return{...C,data:I,currentData:C.data,isFetching:z,isLoading:h,isSuccess:P}}function v(C,k){const M=o(),I=Cs(k);return j.useCallback((L,z)=>M(e.util.prefetch(C,L,{...I,...z})),[C,M,I])}function $(C,k,{refetchOnReconnect:M,refetchOnFocus:I,refetchOnMountOrArgChange:L,skip:z=!1,pollingInterval:h=0,skipPollingIfUnfocused:P=!1,..._}={}){const{initiate:R}=e.endpoints[C],F=o(),G=j.useRef(void 0);if(!G.current){const ie=F(e.internalActions.internal_getRTKQSubscriptions());G.current=ie}const H=od(z?Yn:k,Qs,m.endpointDefinitions[C],C),Z=Cs({refetchOnReconnect:M,refetchOnFocus:I,pollingInterval:h,skipPollingIfUnfocused:P}),W=_.initialPageParam,ee=Cs(W),K=j.useRef(void 0);let{queryCacheKey:re,requestId:le}=K.current||{},fe=!1;re&&le&&(fe=G.current.isRequestSubscribed(re,le));const ce=!fe&&K.current!==void 0;return g(()=>{ce&&(K.current=void 0)},[ce]),g(()=>{var ie;const he=K.current;if(typeof process<"u",H===Yn){he==null||he.unsubscribe(),K.current=void 0;return}const be=(ie=K.current)==null?void 0:ie.subscriptionOptions;if(!he||he.arg!==H){he==null||he.unsubscribe();const Se=F(R(H,{subscriptionOptions:Z,forceRefetch:L,...Ey(m.endpointDefinitions[C])?{initialPageParam:ee}:{}}));K.current=Se}else Z!==be&&he.updateSubscriptionOptions(Z)},[F,R,L,H,Z,ce,ee,C]),[K,F,R,Z]}function E(C,k){return(M,{skip:I=!1,selectFromResult:L}={})=>{const{select:z}=e.endpoints[C],h=od(I?Yn:M,f,m.endpointDefinitions[C],C),P=j.useRef(void 0),_=j.useMemo(()=>d([z(h),(Z,W)=>W,Z=>h],k,{memoizeOptions:{resultEqualityCheck:zi}}),[z,h]),R=j.useMemo(()=>L?d([_],L,{devModeChecks:{identityFunctionCheck:"never"}}):_,[_,L]),F=i(Z=>R(Z,P.current),zi),G=l(),H=_(G.getState(),P.current);return Bx(()=>{P.current=H},[H]),F}}function b(C){j.useEffect(()=>()=>{var k,M;(M=(k=C.current)==null?void 0:k.unsubscribe)==null||M.call(k),C.current=void 0},[C])}function T(C){if(!C.current)throw new Error(Jn(38));return C.current.refetch()}function N(C){const k=(L,z={})=>{const[h]=$(C,L,z);return b(h),j.useMemo(()=>({refetch:()=>T(h)}),[h])},M=({refetchOnReconnect:L,refetchOnFocus:z,pollingInterval:h=0,skipPollingIfUnfocused:P=!1}={})=>{const{initiate:_}=e.endpoints[C],R=o(),[F,G]=j.useState(rd),H=j.useRef(void 0),Z=Cs({refetchOnReconnect:L,refetchOnFocus:z,pollingInterval:h,skipPollingIfUnfocused:P});g(()=>{var re,le;const fe=(re=H.current)==null?void 0:re.subscriptionOptions;Z!==fe&&((le=H.current)==null||le.updateSubscriptionOptions(Z))},[Z]);const W=j.useRef(Z);g(()=>{W.current=Z},[Z]);const ee=j.useCallback(function(re,le=!1){let fe;return r(()=>{var ce;(ce=H.current)==null||ce.unsubscribe(),H.current=fe=R(_(re,{subscriptionOptions:W.current,forceRefetch:!le})),G(re)}),fe},[R,_]),K=j.useCallback(()=>{var re,le;(re=H.current)!=null&&re.queryCacheKey&&R(e.internalActions.removeQueryResult({queryCacheKey:(le=H.current)==null?void 0:le.queryCacheKey}))},[R]);return j.useEffect(()=>()=>{var re;(re=H==null?void 0:H.current)==null||re.unsubscribe()},[]),j.useEffect(()=>{F!==rd&&!H.current&&ee(F,!0)},[F,ee]),j.useMemo(()=>[ee,F,{reset:K}],[ee,F,K])},I=E(C,S);return{useQueryState:I,useQuerySubscription:k,useLazyQuerySubscription:M,useLazyQuery(L){const[z,h,{reset:P}]=M(L),_=I(h,{...L,skip:h===rd}),R=j.useMemo(()=>({lastArg:h}),[h]);return j.useMemo(()=>[z,{..._,reset:P},R],[z,_,P,R])},useQuery(L,z){const h=k(L,z),P=I(L,{selectFromResult:L===Yn||z!=null&&z.skip?void 0:Bh,...z}),_=ad(P,...id);return j.useDebugValue(_),j.useMemo(()=>({...P,...h}),[P,h])}}}function A(C){const k=(I,L={})=>{const[z,h,P,_]=$(C,I,L),R=j.useRef(_);g(()=>{R.current=_},[_]);const F=j.useCallback(function(Z,W){let ee;return r(()=>{var K;(K=z.current)==null||K.unsubscribe(),z.current=ee=h(P(Z,{subscriptionOptions:R.current,direction:W}))}),ee},[z,h,P]);b(z);const G=od(L.skip?Yn:I,Qs,m.endpointDefinitions[C],C),H=j.useCallback(()=>T(z),[z]);return j.useMemo(()=>({trigger:F,refetch:H,fetchNextPage:()=>F(G,"forward"),fetchPreviousPage:()=>F(G,"backward")}),[H,F,G])},M=E(C,y);return{useInfiniteQueryState:M,useInfiniteQuerySubscription:k,useInfiniteQuery(I,L){const{refetch:z,fetchNextPage:h,fetchPreviousPage:P}=k(I,L),_=M(I,{selectFromResult:I===Yn||L!=null&&L.skip?void 0:Bh,...L}),R=ad(_,...id,"hasNextPage","hasPreviousPage");return j.useDebugValue(R),j.useMemo(()=>({..._,fetchNextPage:h,fetchPreviousPage:P,refetch:z}),[_,h,P,z])}}}function x(C){return({selectFromResult:k,fixedCacheKey:M}={})=>{const{select:I,initiate:L}=e.endpoints[C],z=o(),[h,P]=j.useState();j.useEffect(()=>()=>{h!=null&&h.arg.fixedCacheKey||h==null||h.reset()},[h]);const _=j.useCallback(function(re){const le=z(L(re,{fixedCacheKey:M}));return P(le),le},[z,L,M]),{requestId:R}=h||{},F=j.useMemo(()=>I({fixedCacheKey:M,requestId:h==null?void 0:h.requestId}),[M,h,I]),G=j.useMemo(()=>k?d([F],k):F,[k,F]),H=i(G,zi),Z=M==null?h==null?void 0:h.arg.originalArgs:void 0,W=j.useCallback(()=>{r(()=>{h&&P(void 0),M&&z(e.internalActions.removeMutationResult({requestId:R,fixedCacheKey:M}))})},[z,M,h,R]),ee=ad(H,...id,"endpointName");j.useDebugValue(ee);const K=j.useMemo(()=>({...H,originalArgs:Z,reset:W}),[H,Z,W]);return j.useMemo(()=>[_,K],[_,K])}}}var qx=Symbol(),Wx=({batch:e=Rx,hooks:r={useDispatch:Nx,useSelector:_x,useStore:$y},createSelector:o=tp,unstable__sideEffectsInRender:i=!1,...l}={})=>({name:qx,init(u,{serializeQueryArgs:d},f){const m=u,{buildQueryHooks:g,buildInfiniteQueryHooks:S,buildMutationHook:y,usePrefetch:v}=Lx({api:u,moduleOptions:{batch:e,hooks:r,unstable__sideEffectsInRender:i,createSelector:o},serializeQueryArgs:d,context:f});return Ti(m,{usePrefetch:v}),Ti(f,{batch:e}),{injectEndpoint($,E){if(Mx(E)){const{useQuery:b,useLazyQuery:T,useLazyQuerySubscription:N,useQueryState:A,useQuerySubscription:x}=g($);Ti(m.endpoints[$],{useQuery:b,useLazyQuery:T,useLazyQuerySubscription:N,useQueryState:A,useQuerySubscription:x}),u[`use${ks($)}Query`]=b,u[`useLazy${ks($)}Query`]=T}if(Ox(E)){const b=y($);Ti(m.endpoints[$],{useMutation:b}),u[`use${ks($)}Mutation`]=b}else if(Ey(E)){const{useInfiniteQuery:b,useInfiniteQuerySubscription:T,useInfiniteQueryState:N}=S($);Ti(m.endpoints[$],{useInfiniteQuery:b,useInfiniteQuerySubscription:T,useInfiniteQueryState:N}),u[`use${ks($)}InfiniteQuery`]=b}}}}}),Ux=tx(fx(),Wx());const Qx="/IWAApi",Lh="/api/v1/users",_s=Ux({reducerPath:"IWAServices/Users",baseQuery:V1({baseUrl:Qx}),tagTypes:["Users","Roles"],endpoints:e=>({createQuickUser:e.mutation({query:r=>({url:`${Lh}/quick`,method:"POST",body:r}),invalidatesTags:["Users"]}),searchusersFromSource:e.query({query:({userId:r})=>`${Lh}/source-users?userId=${r}`,providesTags:["Users"]})})}),{useCreateQuickUserMutation:Vx,useSearchusersFromSourceQuery:Kx}=_s;function Ty(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Ny={exports:{}},en={},qh={exports:{}},Wh={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Uh;function Gx(){return Uh||(Uh=1,function(e){function r(W,ee){var K=W.length;W.push(ee);e:for(;0<K;){var re=K-1>>>1,le=W[re];if(0<l(le,ee))W[re]=ee,W[K]=le,K=re;else break e}}function o(W){return W.length===0?null:W[0]}function i(W){if(W.length===0)return null;var ee=W[0],K=W.pop();if(K!==ee){W[0]=K;e:for(var re=0,le=W.length,fe=le>>>1;re<fe;){var ce=2*(re+1)-1,ie=W[ce],he=ce+1,be=W[he];if(0>l(ie,K))he<le&&0>l(be,ie)?(W[re]=be,W[he]=K,re=he):(W[re]=ie,W[ce]=K,re=ce);else if(he<le&&0>l(be,K))W[re]=be,W[he]=K,re=he;else break e}}return ee}function l(W,ee){var K=W.sortIndex-ee.sortIndex;return K!==0?K:W.id-ee.id}if(typeof performance=="object"&&typeof performance.now=="function"){var u=performance;e.unstable_now=function(){return u.now()}}else{var d=Date,f=d.now();e.unstable_now=function(){return d.now()-f}}var m=[],g=[],S=1,y=null,v=3,$=!1,E=!1,b=!1,T=typeof setTimeout=="function"?setTimeout:null,N=typeof clearTimeout=="function"?clearTimeout:null,A=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function x(W){for(var ee=o(g);ee!==null;){if(ee.callback===null)i(g);else if(ee.startTime<=W)i(g),ee.sortIndex=ee.expirationTime,r(m,ee);else break;ee=o(g)}}function C(W){if(b=!1,x(W),!E)if(o(m)!==null)E=!0,H(k);else{var ee=o(g);ee!==null&&Z(C,ee.startTime-W)}}function k(W,ee){E=!1,b&&(b=!1,N(L),L=-1),$=!0;var K=v;try{for(x(ee),y=o(m);y!==null&&(!(y.expirationTime>ee)||W&&!P());){var re=y.callback;if(typeof re=="function"){y.callback=null,v=y.priorityLevel;var le=re(y.expirationTime<=ee);ee=e.unstable_now(),typeof le=="function"?y.callback=le:y===o(m)&&i(m),x(ee)}else i(m);y=o(m)}if(y!==null)var fe=!0;else{var ce=o(g);ce!==null&&Z(C,ce.startTime-ee),fe=!1}return fe}finally{y=null,v=K,$=!1}}var M=!1,I=null,L=-1,z=5,h=-1;function P(){return!(e.unstable_now()-h<z)}function _(){if(I!==null){var W=e.unstable_now();h=W;var ee=!0;try{ee=I(!0,W)}finally{ee?R():(M=!1,I=null)}}else M=!1}var R;if(typeof A=="function")R=function(){A(_)};else if(typeof MessageChannel<"u"){var F=new MessageChannel,G=F.port2;F.port1.onmessage=_,R=function(){G.postMessage(null)}}else R=function(){T(_,0)};function H(W){I=W,M||(M=!0,R())}function Z(W,ee){L=T(function(){W(e.unstable_now())},ee)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(W){W.callback=null},e.unstable_continueExecution=function(){E||$||(E=!0,H(k))},e.unstable_forceFrameRate=function(W){0>W||125<W?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):z=0<W?Math.floor(1e3/W):5},e.unstable_getCurrentPriorityLevel=function(){return v},e.unstable_getFirstCallbackNode=function(){return o(m)},e.unstable_next=function(W){switch(v){case 1:case 2:case 3:var ee=3;break;default:ee=v}var K=v;v=ee;try{return W()}finally{v=K}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(W,ee){switch(W){case 1:case 2:case 3:case 4:case 5:break;default:W=3}var K=v;v=W;try{return ee()}finally{v=K}},e.unstable_scheduleCallback=function(W,ee,K){var re=e.unstable_now();switch(typeof K=="object"&&K!==null?(K=K.delay,K=typeof K=="number"&&0<K?re+K:re):K=re,W){case 1:var le=-1;break;case 2:le=250;break;case 5:le=**********;break;case 4:le=1e4;break;default:le=5e3}return le=K+le,W={id:S++,callback:ee,priorityLevel:W,startTime:K,expirationTime:le,sortIndex:-1},K>re?(W.sortIndex=K,r(g,W),o(m)===null&&W===o(g)&&(b?(N(L),L=-1):b=!0,Z(C,K-re))):(W.sortIndex=le,r(m,W),E||$||(E=!0,H(k))),W},e.unstable_shouldYield=P,e.unstable_wrapCallback=function(W){var ee=v;return function(){var K=v;v=ee;try{return W.apply(this,arguments)}finally{v=K}}}}(Wh)),Wh}var Qh;function Yx(){return Qh||(Qh=1,qh.exports=Gx()),qh.exports}/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Vh;function Xx(){if(Vh)return en;Vh=1;var e=Xn,r=Yx();function o(t){for(var n="https://reactjs.org/docs/error-decoder.html?invariant="+t,a=1;a<arguments.length;a++)n+="&args[]="+encodeURIComponent(arguments[a]);return"Minified React error #"+t+"; visit "+n+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var i=new Set,l={};function u(t,n){d(t,n),d(t+"Capture",n)}function d(t,n){for(l[t]=n,t=0;t<n.length;t++)i.add(n[t])}var f=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),m=Object.prototype.hasOwnProperty,g=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,S={},y={};function v(t){return m.call(y,t)?!0:m.call(S,t)?!1:g.test(t)?y[t]=!0:(S[t]=!0,!1)}function $(t,n,a,s){if(a!==null&&a.type===0)return!1;switch(typeof n){case"function":case"symbol":return!0;case"boolean":return s?!1:a!==null?!a.acceptsBooleans:(t=t.toLowerCase().slice(0,5),t!=="data-"&&t!=="aria-");default:return!1}}function E(t,n,a,s){if(n===null||typeof n>"u"||$(t,n,a,s))return!0;if(s)return!1;if(a!==null)switch(a.type){case 3:return!n;case 4:return n===!1;case 5:return isNaN(n);case 6:return isNaN(n)||1>n}return!1}function b(t,n,a,s,c,p,w){this.acceptsBooleans=n===2||n===3||n===4,this.attributeName=s,this.attributeNamespace=c,this.mustUseProperty=a,this.propertyName=t,this.type=n,this.sanitizeURL=p,this.removeEmptyString=w}var T={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(t){T[t]=new b(t,0,!1,t,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(t){var n=t[0];T[n]=new b(n,1,!1,t[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(t){T[t]=new b(t,2,!1,t.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(t){T[t]=new b(t,2,!1,t,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(t){T[t]=new b(t,3,!1,t.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(t){T[t]=new b(t,3,!0,t,null,!1,!1)}),["capture","download"].forEach(function(t){T[t]=new b(t,4,!1,t,null,!1,!1)}),["cols","rows","size","span"].forEach(function(t){T[t]=new b(t,6,!1,t,null,!1,!1)}),["rowSpan","start"].forEach(function(t){T[t]=new b(t,5,!1,t.toLowerCase(),null,!1,!1)});var N=/[\-:]([a-z])/g;function A(t){return t[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(t){var n=t.replace(N,A);T[n]=new b(n,1,!1,t,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(t){var n=t.replace(N,A);T[n]=new b(n,1,!1,t,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(t){var n=t.replace(N,A);T[n]=new b(n,1,!1,t,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(t){T[t]=new b(t,1,!1,t.toLowerCase(),null,!1,!1)}),T.xlinkHref=new b("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(t){T[t]=new b(t,1,!1,t.toLowerCase(),null,!0,!0)});function x(t,n,a,s){var c=T.hasOwnProperty(n)?T[n]:null;(c!==null?c.type!==0:s||!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(E(n,a,c,s)&&(a=null),s||c===null?v(n)&&(a===null?t.removeAttribute(n):t.setAttribute(n,""+a)):c.mustUseProperty?t[c.propertyName]=a===null?c.type===3?!1:"":a:(n=c.attributeName,s=c.attributeNamespace,a===null?t.removeAttribute(n):(c=c.type,a=c===3||c===4&&a===!0?"":""+a,s?t.setAttributeNS(s,n,a):t.setAttribute(n,a))))}var C=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,k=Symbol.for("react.element"),M=Symbol.for("react.portal"),I=Symbol.for("react.fragment"),L=Symbol.for("react.strict_mode"),z=Symbol.for("react.profiler"),h=Symbol.for("react.provider"),P=Symbol.for("react.context"),_=Symbol.for("react.forward_ref"),R=Symbol.for("react.suspense"),F=Symbol.for("react.suspense_list"),G=Symbol.for("react.memo"),H=Symbol.for("react.lazy"),Z=Symbol.for("react.offscreen"),W=Symbol.iterator;function ee(t){return t===null||typeof t!="object"?null:(t=W&&t[W]||t["@@iterator"],typeof t=="function"?t:null)}var K=Object.assign,re;function le(t){if(re===void 0)try{throw Error()}catch(a){var n=a.stack.trim().match(/\n( *(at )?)/);re=n&&n[1]||""}return`
`+re+t}var fe=!1;function ce(t,n){if(!t||fe)return"";fe=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(n)if(n=function(){throw Error()},Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(n,[])}catch(V){var s=V}Reflect.construct(t,[],n)}else{try{n.call()}catch(V){s=V}t.call(n.prototype)}else{try{throw Error()}catch(V){s=V}t()}}catch(V){if(V&&s&&typeof V.stack=="string"){for(var c=V.stack.split(`
`),p=s.stack.split(`
`),w=c.length-1,O=p.length-1;1<=w&&0<=O&&c[w]!==p[O];)O--;for(;1<=w&&0<=O;w--,O--)if(c[w]!==p[O]){if(w!==1||O!==1)do if(w--,O--,0>O||c[w]!==p[O]){var D=`
`+c[w].replace(" at new "," at ");return t.displayName&&D.includes("<anonymous>")&&(D=D.replace("<anonymous>",t.displayName)),D}while(1<=w&&0<=O);break}}}finally{fe=!1,Error.prepareStackTrace=a}return(t=t?t.displayName||t.name:"")?le(t):""}function ie(t){switch(t.tag){case 5:return le(t.type);case 16:return le("Lazy");case 13:return le("Suspense");case 19:return le("SuspenseList");case 0:case 2:case 15:return t=ce(t.type,!1),t;case 11:return t=ce(t.type.render,!1),t;case 1:return t=ce(t.type,!0),t;default:return""}}function he(t){if(t==null)return null;if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case I:return"Fragment";case M:return"Portal";case z:return"Profiler";case L:return"StrictMode";case R:return"Suspense";case F:return"SuspenseList"}if(typeof t=="object")switch(t.$$typeof){case P:return(t.displayName||"Context")+".Consumer";case h:return(t._context.displayName||"Context")+".Provider";case _:var n=t.render;return t=t.displayName,t||(t=n.displayName||n.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case G:return n=t.displayName||null,n!==null?n:he(t.type)||"Memo";case H:n=t._payload,t=t._init;try{return he(t(n))}catch{}}return null}function be(t){var n=t.type;switch(t.tag){case 24:return"Cache";case 9:return(n.displayName||"Context")+".Consumer";case 10:return(n._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return t=n.render,t=t.displayName||t.name||"",n.displayName||(t!==""?"ForwardRef("+t+")":"ForwardRef");case 7:return"Fragment";case 5:return n;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return he(n);case 8:return n===L?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof n=="function")return n.displayName||n.name||null;if(typeof n=="string")return n}return null}function Se(t){switch(typeof t){case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function qe(t){var n=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(n==="checkbox"||n==="radio")}function Ne(t){var n=qe(t)?"checked":"value",a=Object.getOwnPropertyDescriptor(t.constructor.prototype,n),s=""+t[n];if(!t.hasOwnProperty(n)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var c=a.get,p=a.set;return Object.defineProperty(t,n,{configurable:!0,get:function(){return c.call(this)},set:function(w){s=""+w,p.call(this,w)}}),Object.defineProperty(t,n,{enumerable:a.enumerable}),{getValue:function(){return s},setValue:function(w){s=""+w},stopTracking:function(){t._valueTracker=null,delete t[n]}}}}function Ee(t){t._valueTracker||(t._valueTracker=Ne(t))}function Me(t){if(!t)return!1;var n=t._valueTracker;if(!n)return!0;var a=n.getValue(),s="";return t&&(s=qe(t)?t.checked?"true":"false":t.value),t=s,t!==a?(n.setValue(t),!0):!1}function We(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}function dt(t,n){var a=n.checked;return K({},n,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:a??t._wrapperState.initialChecked})}function Ie(t,n){var a=n.defaultValue==null?"":n.defaultValue,s=n.checked!=null?n.checked:n.defaultChecked;a=Se(n.value!=null?n.value:a),t._wrapperState={initialChecked:s,initialValue:a,controlled:n.type==="checkbox"||n.type==="radio"?n.checked!=null:n.value!=null}}function tt(t,n){n=n.checked,n!=null&&x(t,"checked",n,!1)}function de(t,n){tt(t,n);var a=Se(n.value),s=n.type;if(a!=null)s==="number"?(a===0&&t.value===""||t.value!=a)&&(t.value=""+a):t.value!==""+a&&(t.value=""+a);else if(s==="submit"||s==="reset"){t.removeAttribute("value");return}n.hasOwnProperty("value")?_e(t,n.type,a):n.hasOwnProperty("defaultValue")&&_e(t,n.type,Se(n.defaultValue)),n.checked==null&&n.defaultChecked!=null&&(t.defaultChecked=!!n.defaultChecked)}function Wt(t,n,a){if(n.hasOwnProperty("value")||n.hasOwnProperty("defaultValue")){var s=n.type;if(!(s!=="submit"&&s!=="reset"||n.value!==void 0&&n.value!==null))return;n=""+t._wrapperState.initialValue,a||n===t.value||(t.value=n),t.defaultValue=n}a=t.name,a!==""&&(t.name=""),t.defaultChecked=!!t._wrapperState.initialChecked,a!==""&&(t.name=a)}function _e(t,n,a){(n!=="number"||We(t.ownerDocument)!==t)&&(a==null?t.defaultValue=""+t._wrapperState.initialValue:t.defaultValue!==""+a&&(t.defaultValue=""+a))}var Ye=Array.isArray;function ye(t,n,a,s){if(t=t.options,n){n={};for(var c=0;c<a.length;c++)n["$"+a[c]]=!0;for(a=0;a<t.length;a++)c=n.hasOwnProperty("$"+t[a].value),t[a].selected!==c&&(t[a].selected=c),c&&s&&(t[a].defaultSelected=!0)}else{for(a=""+Se(a),n=null,c=0;c<t.length;c++){if(t[c].value===a){t[c].selected=!0,s&&(t[c].defaultSelected=!0);return}n!==null||t[c].disabled||(n=t[c])}n!==null&&(n.selected=!0)}}function In(t,n){if(n.dangerouslySetInnerHTML!=null)throw Error(o(91));return K({},n,{value:void 0,defaultValue:void 0,children:""+t._wrapperState.initialValue})}function gn(t,n){var a=n.value;if(a==null){if(a=n.children,n=n.defaultValue,a!=null){if(n!=null)throw Error(o(92));if(Ye(a)){if(1<a.length)throw Error(o(93));a=a[0]}n=a}n==null&&(n=""),a=n}t._wrapperState={initialValue:Se(a)}}function Ot(t,n){var a=Se(n.value),s=Se(n.defaultValue);a!=null&&(a=""+a,a!==t.value&&(t.value=a),n.defaultValue==null&&t.defaultValue!==a&&(t.defaultValue=a)),s!=null&&(t.defaultValue=""+s)}function br(t){var n=t.textContent;n===t._wrapperState.initialValue&&n!==""&&n!==null&&(t.value=n)}function zn(t){switch(t){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Dn(t,n){return t==null||t==="http://www.w3.org/1999/xhtml"?zn(n):t==="http://www.w3.org/2000/svg"&&n==="foreignObject"?"http://www.w3.org/1999/xhtml":t}var yn,jt=function(t){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(n,a,s,c){MSApp.execUnsafeLocalFunction(function(){return t(n,a,s,c)})}:t}(function(t,n){if(t.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in t)t.innerHTML=n;else{for(yn=yn||document.createElement("div"),yn.innerHTML="<svg>"+n.valueOf().toString()+"</svg>",n=yn.firstChild;t.firstChild;)t.removeChild(t.firstChild);for(;n.firstChild;)t.appendChild(n.firstChild)}});function Ue(t,n){if(n){var a=t.firstChild;if(a&&a===t.lastChild&&a.nodeType===3){a.nodeValue=n;return}}t.textContent=n}var Et={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},wo=["Webkit","ms","Moz","O"];Object.keys(Et).forEach(function(t){wo.forEach(function(n){n=n+t.charAt(0).toUpperCase()+t.substring(1),Et[n]=Et[t]})});function xr(t,n,a){return n==null||typeof n=="boolean"||n===""?"":a||typeof n!="number"||n===0||Et.hasOwnProperty(t)&&Et[t]?(""+n).trim():n+"px"}function Jo(t,n){t=t.style;for(var a in n)if(n.hasOwnProperty(a)){var s=a.indexOf("--")===0,c=xr(a,n[a],s);a==="float"&&(a="cssFloat"),s?t.setProperty(a,c):t[a]=c}}var Fn=K({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Ut(t,n){if(n){if(Fn[t]&&(n.children!=null||n.dangerouslySetInnerHTML!=null))throw Error(o(137,t));if(n.dangerouslySetInnerHTML!=null){if(n.children!=null)throw Error(o(60));if(typeof n.dangerouslySetInnerHTML!="object"||!("__html"in n.dangerouslySetInnerHTML))throw Error(o(61))}if(n.style!=null&&typeof n.style!="object")throw Error(o(62))}}function So(t,n){if(t.indexOf("-")===-1)return typeof n.is=="string";switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var wr=null;function Sr(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var an=null,Gt=null,Qt=null;function vn(t){if(t=ci(t)){if(typeof an!="function")throw Error(o(280));var n=t.stateNode;n&&(n=Rl(n),an(t.stateNode,t.type,n))}}function Bn(t){Gt?Qt?Qt.push(t):Qt=[t]:Gt=t}function Zo(){if(Gt){var t=Gt,n=Qt;if(Qt=Gt=null,vn(t),n)for(t=0;t<n.length;t++)vn(n[t])}}function tr(t,n){return t(n)}function ko(){}var Co=!1;function kr(t,n,a){if(Co)return t(n,a);Co=!0;try{return tr(t,n,a)}finally{Co=!1,(Gt!==null||Qt!==null)&&(ko(),Zo())}}function bn(t,n){var a=t.stateNode;if(a===null)return null;var s=Rl(a);if(s===null)return null;a=s[n];e:switch(n){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(s=!s.disabled)||(t=t.type,s=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!s;break e;default:t=!1}if(t)return null;if(a&&typeof a!="function")throw Error(o(231,n,typeof a));return a}var Cr=!1;if(f)try{var $r={};Object.defineProperty($r,"passive",{get:function(){Cr=!0}}),window.addEventListener("test",$r,$r),window.removeEventListener("test",$r,$r)}catch{Cr=!1}function Ua(t,n,a,s,c,p,w,O,D){var V=Array.prototype.slice.call(arguments,3);try{n.apply(a,V)}catch(ne){this.onError(ne)}}var Er=!1,nr=null,it=!1,lt=null,rr={onError:function(t){Er=!0,nr=t}};function Qa(t,n,a,s,c,p,w,O,D){Er=!1,nr=null,Ua.apply(rr,arguments)}function $o(t,n,a,s,c,p,w,O,D){if(Qa.apply(this,arguments),Er){if(Er){var V=nr;Er=!1,nr=null}else throw Error(o(198));it||(it=!0,lt=V)}}function xn(t){var n=t,a=t;if(t.alternate)for(;n.return;)n=n.return;else{t=n;do n=t,n.flags&4098&&(a=n.return),t=n.return;while(t)}return n.tag===3?a:null}function J(t){if(t.tag===13){var n=t.memoizedState;if(n===null&&(t=t.alternate,t!==null&&(n=t.memoizedState)),n!==null)return n.dehydrated}return null}function X(t){if(xn(t)!==t)throw Error(o(188))}function ue(t){var n=t.alternate;if(!n){if(n=xn(t),n===null)throw Error(o(188));return n!==t?null:t}for(var a=t,s=n;;){var c=a.return;if(c===null)break;var p=c.alternate;if(p===null){if(s=c.return,s!==null){a=s;continue}break}if(c.child===p.child){for(p=c.child;p;){if(p===a)return X(c),t;if(p===s)return X(c),n;p=p.sibling}throw Error(o(188))}if(a.return!==s.return)a=c,s=p;else{for(var w=!1,O=c.child;O;){if(O===a){w=!0,a=c,s=p;break}if(O===s){w=!0,s=c,a=p;break}O=O.sibling}if(!w){for(O=p.child;O;){if(O===a){w=!0,a=p,s=c;break}if(O===s){w=!0,s=p,a=c;break}O=O.sibling}if(!w)throw Error(o(189))}}if(a.alternate!==s)throw Error(o(190))}if(a.tag!==3)throw Error(o(188));return a.stateNode.current===a?t:n}function we(t){return t=ue(t),t!==null?Re(t):null}function Re(t){if(t.tag===5||t.tag===6)return t;for(t=t.child;t!==null;){var n=Re(t);if(n!==null)return n;t=t.sibling}return null}var Ke=r.unstable_scheduleCallback,pt=r.unstable_cancelCallback,Tr=r.unstable_shouldYield,Vr=r.unstable_requestPaint,ke=r.unstable_now,wn=r.unstable_getCurrentPriorityLevel,Sn=r.unstable_ImmediatePriority,Kr=r.unstable_UserBlockingPriority,ea=r.unstable_NormalPriority,j0=r.unstable_LowPriority,Dp=r.unstable_IdlePriority,fl=null,or=null;function I0(t){if(or&&typeof or.onCommitFiberRoot=="function")try{or.onCommitFiberRoot(fl,t,void 0,(t.current.flags&128)===128)}catch{}}var Ln=Math.clz32?Math.clz32:F0,z0=Math.log,D0=Math.LN2;function F0(t){return t>>>=0,t===0?32:31-(z0(t)/D0|0)|0}var ml=64,hl=4194304;function Va(t){switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return t&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return t}}function gl(t,n){var a=t.pendingLanes;if(a===0)return 0;var s=0,c=t.suspendedLanes,p=t.pingedLanes,w=a&268435455;if(w!==0){var O=w&~c;O!==0?s=Va(O):(p&=w,p!==0&&(s=Va(p)))}else w=a&~c,w!==0?s=Va(w):p!==0&&(s=Va(p));if(s===0)return 0;if(n!==0&&n!==s&&!(n&c)&&(c=s&-s,p=n&-n,c>=p||c===16&&(p&4194240)!==0))return n;if(s&4&&(s|=a&16),n=t.entangledLanes,n!==0)for(t=t.entanglements,n&=s;0<n;)a=31-Ln(n),c=1<<a,s|=t[a],n&=~c;return s}function B0(t,n){switch(t){case 1:case 2:case 4:return n+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return n+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function L0(t,n){for(var a=t.suspendedLanes,s=t.pingedLanes,c=t.expirationTimes,p=t.pendingLanes;0<p;){var w=31-Ln(p),O=1<<w,D=c[w];D===-1?(!(O&a)||O&s)&&(c[w]=B0(O,n)):D<=n&&(t.expiredLanes|=O),p&=~O}}function bu(t){return t=t.pendingLanes&-1073741825,t!==0?t:t&1073741824?1073741824:0}function Fp(){var t=ml;return ml<<=1,!(ml&4194240)&&(ml=64),t}function xu(t){for(var n=[],a=0;31>a;a++)n.push(t);return n}function Ka(t,n,a){t.pendingLanes|=n,n!==536870912&&(t.suspendedLanes=0,t.pingedLanes=0),t=t.eventTimes,n=31-Ln(n),t[n]=a}function q0(t,n){var a=t.pendingLanes&~n;t.pendingLanes=n,t.suspendedLanes=0,t.pingedLanes=0,t.expiredLanes&=n,t.mutableReadLanes&=n,t.entangledLanes&=n,n=t.entanglements;var s=t.eventTimes;for(t=t.expirationTimes;0<a;){var c=31-Ln(a),p=1<<c;n[c]=0,s[c]=-1,t[c]=-1,a&=~p}}function wu(t,n){var a=t.entangledLanes|=n;for(t=t.entanglements;a;){var s=31-Ln(a),c=1<<s;c&n|t[s]&n&&(t[s]|=n),a&=~c}}var Ve=0;function Bp(t){return t&=-t,1<t?4<t?t&268435455?16:536870912:4:1}var Lp,Su,qp,Wp,Up,ku=!1,yl=[],Gr=null,Yr=null,Xr=null,Ga=new Map,Ya=new Map,Hr=[],W0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Qp(t,n){switch(t){case"focusin":case"focusout":Gr=null;break;case"dragenter":case"dragleave":Yr=null;break;case"mouseover":case"mouseout":Xr=null;break;case"pointerover":case"pointerout":Ga.delete(n.pointerId);break;case"gotpointercapture":case"lostpointercapture":Ya.delete(n.pointerId)}}function Xa(t,n,a,s,c,p){return t===null||t.nativeEvent!==p?(t={blockedOn:n,domEventName:a,eventSystemFlags:s,nativeEvent:p,targetContainers:[c]},n!==null&&(n=ci(n),n!==null&&Su(n)),t):(t.eventSystemFlags|=s,n=t.targetContainers,c!==null&&n.indexOf(c)===-1&&n.push(c),t)}function U0(t,n,a,s,c){switch(n){case"focusin":return Gr=Xa(Gr,t,n,a,s,c),!0;case"dragenter":return Yr=Xa(Yr,t,n,a,s,c),!0;case"mouseover":return Xr=Xa(Xr,t,n,a,s,c),!0;case"pointerover":var p=c.pointerId;return Ga.set(p,Xa(Ga.get(p)||null,t,n,a,s,c)),!0;case"gotpointercapture":return p=c.pointerId,Ya.set(p,Xa(Ya.get(p)||null,t,n,a,s,c)),!0}return!1}function Vp(t){var n=Eo(t.target);if(n!==null){var a=xn(n);if(a!==null){if(n=a.tag,n===13){if(n=J(a),n!==null){t.blockedOn=n,Up(t.priority,function(){qp(a)});return}}else if(n===3&&a.stateNode.current.memoizedState.isDehydrated){t.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}t.blockedOn=null}function vl(t){if(t.blockedOn!==null)return!1;for(var n=t.targetContainers;0<n.length;){var a=$u(t.domEventName,t.eventSystemFlags,n[0],t.nativeEvent);if(a===null){a=t.nativeEvent;var s=new a.constructor(a.type,a);wr=s,a.target.dispatchEvent(s),wr=null}else return n=ci(a),n!==null&&Su(n),t.blockedOn=a,!1;n.shift()}return!0}function Kp(t,n,a){vl(t)&&a.delete(n)}function Q0(){ku=!1,Gr!==null&&vl(Gr)&&(Gr=null),Yr!==null&&vl(Yr)&&(Yr=null),Xr!==null&&vl(Xr)&&(Xr=null),Ga.forEach(Kp),Ya.forEach(Kp)}function Ha(t,n){t.blockedOn===n&&(t.blockedOn=null,ku||(ku=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,Q0)))}function Ja(t){function n(c){return Ha(c,t)}if(0<yl.length){Ha(yl[0],t);for(var a=1;a<yl.length;a++){var s=yl[a];s.blockedOn===t&&(s.blockedOn=null)}}for(Gr!==null&&Ha(Gr,t),Yr!==null&&Ha(Yr,t),Xr!==null&&Ha(Xr,t),Ga.forEach(n),Ya.forEach(n),a=0;a<Hr.length;a++)s=Hr[a],s.blockedOn===t&&(s.blockedOn=null);for(;0<Hr.length&&(a=Hr[0],a.blockedOn===null);)Vp(a),a.blockedOn===null&&Hr.shift()}var ta=C.ReactCurrentBatchConfig,bl=!0;function V0(t,n,a,s){var c=Ve,p=ta.transition;ta.transition=null;try{Ve=1,Cu(t,n,a,s)}finally{Ve=c,ta.transition=p}}function K0(t,n,a,s){var c=Ve,p=ta.transition;ta.transition=null;try{Ve=4,Cu(t,n,a,s)}finally{Ve=c,ta.transition=p}}function Cu(t,n,a,s){if(bl){var c=$u(t,n,a,s);if(c===null)qu(t,n,s,xl,a),Qp(t,s);else if(U0(c,t,n,a,s))s.stopPropagation();else if(Qp(t,s),n&4&&-1<W0.indexOf(t)){for(;c!==null;){var p=ci(c);if(p!==null&&Lp(p),p=$u(t,n,a,s),p===null&&qu(t,n,s,xl,a),p===c)break;c=p}c!==null&&s.stopPropagation()}else qu(t,n,s,null,a)}}var xl=null;function $u(t,n,a,s){if(xl=null,t=Sr(s),t=Eo(t),t!==null)if(n=xn(t),n===null)t=null;else if(a=n.tag,a===13){if(t=J(n),t!==null)return t;t=null}else if(a===3){if(n.stateNode.current.memoizedState.isDehydrated)return n.tag===3?n.stateNode.containerInfo:null;t=null}else n!==t&&(t=null);return xl=t,null}function Gp(t){switch(t){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(wn()){case Sn:return 1;case Kr:return 4;case ea:case j0:return 16;case Dp:return 536870912;default:return 16}default:return 16}}var Jr=null,Eu=null,wl=null;function Yp(){if(wl)return wl;var t,n=Eu,a=n.length,s,c="value"in Jr?Jr.value:Jr.textContent,p=c.length;for(t=0;t<a&&n[t]===c[t];t++);var w=a-t;for(s=1;s<=w&&n[a-s]===c[p-s];s++);return wl=c.slice(t,1<s?1-s:void 0)}function Sl(t){var n=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&n===13&&(t=13)):t=n,t===10&&(t=13),32<=t||t===13?t:0}function kl(){return!0}function Xp(){return!1}function ln(t){function n(a,s,c,p,w){this._reactName=a,this._targetInst=c,this.type=s,this.nativeEvent=p,this.target=w,this.currentTarget=null;for(var O in t)t.hasOwnProperty(O)&&(a=t[O],this[O]=a?a(p):p[O]);return this.isDefaultPrevented=(p.defaultPrevented!=null?p.defaultPrevented:p.returnValue===!1)?kl:Xp,this.isPropagationStopped=Xp,this}return K(n.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=kl)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=kl)},persist:function(){},isPersistent:kl}),n}var na={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Tu=ln(na),Za=K({},na,{view:0,detail:0}),G0=ln(Za),Nu,Au,ei,Cl=K({},Za,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:_u,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==ei&&(ei&&t.type==="mousemove"?(Nu=t.screenX-ei.screenX,Au=t.screenY-ei.screenY):Au=Nu=0,ei=t),Nu)},movementY:function(t){return"movementY"in t?t.movementY:Au}}),Hp=ln(Cl),Y0=K({},Cl,{dataTransfer:0}),X0=ln(Y0),H0=K({},Za,{relatedTarget:0}),Pu=ln(H0),J0=K({},na,{animationName:0,elapsedTime:0,pseudoElement:0}),Z0=ln(J0),ev=K({},na,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),tv=ln(ev),nv=K({},na,{data:0}),Jp=ln(nv),rv={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},ov={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},av={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function iv(t){var n=this.nativeEvent;return n.getModifierState?n.getModifierState(t):(t=av[t])?!!n[t]:!1}function _u(){return iv}var lv=K({},Za,{key:function(t){if(t.key){var n=rv[t.key]||t.key;if(n!=="Unidentified")return n}return t.type==="keypress"?(t=Sl(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?ov[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:_u,charCode:function(t){return t.type==="keypress"?Sl(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Sl(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),sv=ln(lv),uv=K({},Cl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Zp=ln(uv),cv=K({},Za,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:_u}),dv=ln(cv),pv=K({},na,{propertyName:0,elapsedTime:0,pseudoElement:0}),fv=ln(pv),mv=K({},Cl,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),hv=ln(mv),gv=[9,13,27,32],Ru=f&&"CompositionEvent"in window,ti=null;f&&"documentMode"in document&&(ti=document.documentMode);var yv=f&&"TextEvent"in window&&!ti,ef=f&&(!Ru||ti&&8<ti&&11>=ti),tf=" ",nf=!1;function rf(t,n){switch(t){case"keyup":return gv.indexOf(n.keyCode)!==-1;case"keydown":return n.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function of(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var ra=!1;function vv(t,n){switch(t){case"compositionend":return of(n);case"keypress":return n.which!==32?null:(nf=!0,tf);case"textInput":return t=n.data,t===tf&&nf?null:t;default:return null}}function bv(t,n){if(ra)return t==="compositionend"||!Ru&&rf(t,n)?(t=Yp(),wl=Eu=Jr=null,ra=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(n.ctrlKey||n.altKey||n.metaKey)||n.ctrlKey&&n.altKey){if(n.char&&1<n.char.length)return n.char;if(n.which)return String.fromCharCode(n.which)}return null;case"compositionend":return ef&&n.locale!=="ko"?null:n.data;default:return null}}var xv={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function af(t){var n=t&&t.nodeName&&t.nodeName.toLowerCase();return n==="input"?!!xv[t.type]:n==="textarea"}function lf(t,n,a,s){Bn(s),n=Al(n,"onChange"),0<n.length&&(a=new Tu("onChange","change",null,a,s),t.push({event:a,listeners:n}))}var ni=null,ri=null;function wv(t){$f(t,0)}function $l(t){var n=sa(t);if(Me(n))return t}function Sv(t,n){if(t==="change")return n}var sf=!1;if(f){var Mu;if(f){var Ou="oninput"in document;if(!Ou){var uf=document.createElement("div");uf.setAttribute("oninput","return;"),Ou=typeof uf.oninput=="function"}Mu=Ou}else Mu=!1;sf=Mu&&(!document.documentMode||9<document.documentMode)}function cf(){ni&&(ni.detachEvent("onpropertychange",df),ri=ni=null)}function df(t){if(t.propertyName==="value"&&$l(ri)){var n=[];lf(n,ri,t,Sr(t)),kr(wv,n)}}function kv(t,n,a){t==="focusin"?(cf(),ni=n,ri=a,ni.attachEvent("onpropertychange",df)):t==="focusout"&&cf()}function Cv(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return $l(ri)}function $v(t,n){if(t==="click")return $l(n)}function Ev(t,n){if(t==="input"||t==="change")return $l(n)}function Tv(t,n){return t===n&&(t!==0||1/t===1/n)||t!==t&&n!==n}var qn=typeof Object.is=="function"?Object.is:Tv;function oi(t,n){if(qn(t,n))return!0;if(typeof t!="object"||t===null||typeof n!="object"||n===null)return!1;var a=Object.keys(t),s=Object.keys(n);if(a.length!==s.length)return!1;for(s=0;s<a.length;s++){var c=a[s];if(!m.call(n,c)||!qn(t[c],n[c]))return!1}return!0}function pf(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function ff(t,n){var a=pf(t);t=0;for(var s;a;){if(a.nodeType===3){if(s=t+a.textContent.length,t<=n&&s>=n)return{node:a,offset:n-t};t=s}e:{for(;a;){if(a.nextSibling){a=a.nextSibling;break e}a=a.parentNode}a=void 0}a=pf(a)}}function mf(t,n){return t&&n?t===n?!0:t&&t.nodeType===3?!1:n&&n.nodeType===3?mf(t,n.parentNode):"contains"in t?t.contains(n):t.compareDocumentPosition?!!(t.compareDocumentPosition(n)&16):!1:!1}function hf(){for(var t=window,n=We();n instanceof t.HTMLIFrameElement;){try{var a=typeof n.contentWindow.location.href=="string"}catch{a=!1}if(a)t=n.contentWindow;else break;n=We(t.document)}return n}function ju(t){var n=t&&t.nodeName&&t.nodeName.toLowerCase();return n&&(n==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||n==="textarea"||t.contentEditable==="true")}function Nv(t){var n=hf(),a=t.focusedElem,s=t.selectionRange;if(n!==a&&a&&a.ownerDocument&&mf(a.ownerDocument.documentElement,a)){if(s!==null&&ju(a)){if(n=s.start,t=s.end,t===void 0&&(t=n),"selectionStart"in a)a.selectionStart=n,a.selectionEnd=Math.min(t,a.value.length);else if(t=(n=a.ownerDocument||document)&&n.defaultView||window,t.getSelection){t=t.getSelection();var c=a.textContent.length,p=Math.min(s.start,c);s=s.end===void 0?p:Math.min(s.end,c),!t.extend&&p>s&&(c=s,s=p,p=c),c=ff(a,p);var w=ff(a,s);c&&w&&(t.rangeCount!==1||t.anchorNode!==c.node||t.anchorOffset!==c.offset||t.focusNode!==w.node||t.focusOffset!==w.offset)&&(n=n.createRange(),n.setStart(c.node,c.offset),t.removeAllRanges(),p>s?(t.addRange(n),t.extend(w.node,w.offset)):(n.setEnd(w.node,w.offset),t.addRange(n)))}}for(n=[],t=a;t=t.parentNode;)t.nodeType===1&&n.push({element:t,left:t.scrollLeft,top:t.scrollTop});for(typeof a.focus=="function"&&a.focus(),a=0;a<n.length;a++)t=n[a],t.element.scrollLeft=t.left,t.element.scrollTop=t.top}}var Av=f&&"documentMode"in document&&11>=document.documentMode,oa=null,Iu=null,ai=null,zu=!1;function gf(t,n,a){var s=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;zu||oa==null||oa!==We(s)||(s=oa,"selectionStart"in s&&ju(s)?s={start:s.selectionStart,end:s.selectionEnd}:(s=(s.ownerDocument&&s.ownerDocument.defaultView||window).getSelection(),s={anchorNode:s.anchorNode,anchorOffset:s.anchorOffset,focusNode:s.focusNode,focusOffset:s.focusOffset}),ai&&oi(ai,s)||(ai=s,s=Al(Iu,"onSelect"),0<s.length&&(n=new Tu("onSelect","select",null,n,a),t.push({event:n,listeners:s}),n.target=oa)))}function El(t,n){var a={};return a[t.toLowerCase()]=n.toLowerCase(),a["Webkit"+t]="webkit"+n,a["Moz"+t]="moz"+n,a}var aa={animationend:El("Animation","AnimationEnd"),animationiteration:El("Animation","AnimationIteration"),animationstart:El("Animation","AnimationStart"),transitionend:El("Transition","TransitionEnd")},Du={},yf={};f&&(yf=document.createElement("div").style,"AnimationEvent"in window||(delete aa.animationend.animation,delete aa.animationiteration.animation,delete aa.animationstart.animation),"TransitionEvent"in window||delete aa.transitionend.transition);function Tl(t){if(Du[t])return Du[t];if(!aa[t])return t;var n=aa[t],a;for(a in n)if(n.hasOwnProperty(a)&&a in yf)return Du[t]=n[a];return t}var vf=Tl("animationend"),bf=Tl("animationiteration"),xf=Tl("animationstart"),wf=Tl("transitionend"),Sf=new Map,kf="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Zr(t,n){Sf.set(t,n),u(n,[t])}for(var Fu=0;Fu<kf.length;Fu++){var Bu=kf[Fu],Pv=Bu.toLowerCase(),_v=Bu[0].toUpperCase()+Bu.slice(1);Zr(Pv,"on"+_v)}Zr(vf,"onAnimationEnd"),Zr(bf,"onAnimationIteration"),Zr(xf,"onAnimationStart"),Zr("dblclick","onDoubleClick"),Zr("focusin","onFocus"),Zr("focusout","onBlur"),Zr(wf,"onTransitionEnd"),d("onMouseEnter",["mouseout","mouseover"]),d("onMouseLeave",["mouseout","mouseover"]),d("onPointerEnter",["pointerout","pointerover"]),d("onPointerLeave",["pointerout","pointerover"]),u("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),u("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),u("onBeforeInput",["compositionend","keypress","textInput","paste"]),u("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),u("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),u("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var ii="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Rv=new Set("cancel close invalid load scroll toggle".split(" ").concat(ii));function Cf(t,n,a){var s=t.type||"unknown-event";t.currentTarget=a,$o(s,n,void 0,t),t.currentTarget=null}function $f(t,n){n=(n&4)!==0;for(var a=0;a<t.length;a++){var s=t[a],c=s.event;s=s.listeners;e:{var p=void 0;if(n)for(var w=s.length-1;0<=w;w--){var O=s[w],D=O.instance,V=O.currentTarget;if(O=O.listener,D!==p&&c.isPropagationStopped())break e;Cf(c,O,V),p=D}else for(w=0;w<s.length;w++){if(O=s[w],D=O.instance,V=O.currentTarget,O=O.listener,D!==p&&c.isPropagationStopped())break e;Cf(c,O,V),p=D}}}if(it)throw t=lt,it=!1,lt=null,t}function Ze(t,n){var a=n[Gu];a===void 0&&(a=n[Gu]=new Set);var s=t+"__bubble";a.has(s)||(Ef(n,t,2,!1),a.add(s))}function Lu(t,n,a){var s=0;n&&(s|=4),Ef(a,t,s,n)}var Nl="_reactListening"+Math.random().toString(36).slice(2);function li(t){if(!t[Nl]){t[Nl]=!0,i.forEach(function(a){a!=="selectionchange"&&(Rv.has(a)||Lu(a,!1,t),Lu(a,!0,t))});var n=t.nodeType===9?t:t.ownerDocument;n===null||n[Nl]||(n[Nl]=!0,Lu("selectionchange",!1,n))}}function Ef(t,n,a,s){switch(Gp(n)){case 1:var c=V0;break;case 4:c=K0;break;default:c=Cu}a=c.bind(null,n,a,t),c=void 0,!Cr||n!=="touchstart"&&n!=="touchmove"&&n!=="wheel"||(c=!0),s?c!==void 0?t.addEventListener(n,a,{capture:!0,passive:c}):t.addEventListener(n,a,!0):c!==void 0?t.addEventListener(n,a,{passive:c}):t.addEventListener(n,a,!1)}function qu(t,n,a,s,c){var p=s;if(!(n&1)&&!(n&2)&&s!==null)e:for(;;){if(s===null)return;var w=s.tag;if(w===3||w===4){var O=s.stateNode.containerInfo;if(O===c||O.nodeType===8&&O.parentNode===c)break;if(w===4)for(w=s.return;w!==null;){var D=w.tag;if((D===3||D===4)&&(D=w.stateNode.containerInfo,D===c||D.nodeType===8&&D.parentNode===c))return;w=w.return}for(;O!==null;){if(w=Eo(O),w===null)return;if(D=w.tag,D===5||D===6){s=p=w;continue e}O=O.parentNode}}s=s.return}kr(function(){var V=p,ne=Sr(a),oe=[];e:{var te=Sf.get(t);if(te!==void 0){var se=Tu,me=t;switch(t){case"keypress":if(Sl(a)===0)break e;case"keydown":case"keyup":se=sv;break;case"focusin":me="focus",se=Pu;break;case"focusout":me="blur",se=Pu;break;case"beforeblur":case"afterblur":se=Pu;break;case"click":if(a.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":se=Hp;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":se=X0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":se=dv;break;case vf:case bf:case xf:se=Z0;break;case wf:se=fv;break;case"scroll":se=G0;break;case"wheel":se=hv;break;case"copy":case"cut":case"paste":se=tv;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":se=Zp}var ge=(n&4)!==0,ft=!ge&&t==="scroll",U=ge?te!==null?te+"Capture":null:te;ge=[];for(var B=V,Q;B!==null;){Q=B;var ae=Q.stateNode;if(Q.tag===5&&ae!==null&&(Q=ae,U!==null&&(ae=bn(B,U),ae!=null&&ge.push(si(B,ae,Q)))),ft)break;B=B.return}0<ge.length&&(te=new se(te,me,null,a,ne),oe.push({event:te,listeners:ge}))}}if(!(n&7)){e:{if(te=t==="mouseover"||t==="pointerover",se=t==="mouseout"||t==="pointerout",te&&a!==wr&&(me=a.relatedTarget||a.fromElement)&&(Eo(me)||me[Nr]))break e;if((se||te)&&(te=ne.window===ne?ne:(te=ne.ownerDocument)?te.defaultView||te.parentWindow:window,se?(me=a.relatedTarget||a.toElement,se=V,me=me?Eo(me):null,me!==null&&(ft=xn(me),me!==ft||me.tag!==5&&me.tag!==6)&&(me=null)):(se=null,me=V),se!==me)){if(ge=Hp,ae="onMouseLeave",U="onMouseEnter",B="mouse",(t==="pointerout"||t==="pointerover")&&(ge=Zp,ae="onPointerLeave",U="onPointerEnter",B="pointer"),ft=se==null?te:sa(se),Q=me==null?te:sa(me),te=new ge(ae,B+"leave",se,a,ne),te.target=ft,te.relatedTarget=Q,ae=null,Eo(ne)===V&&(ge=new ge(U,B+"enter",me,a,ne),ge.target=Q,ge.relatedTarget=ft,ae=ge),ft=ae,se&&me)t:{for(ge=se,U=me,B=0,Q=ge;Q;Q=ia(Q))B++;for(Q=0,ae=U;ae;ae=ia(ae))Q++;for(;0<B-Q;)ge=ia(ge),B--;for(;0<Q-B;)U=ia(U),Q--;for(;B--;){if(ge===U||U!==null&&ge===U.alternate)break t;ge=ia(ge),U=ia(U)}ge=null}else ge=null;se!==null&&Tf(oe,te,se,ge,!1),me!==null&&ft!==null&&Tf(oe,ft,me,ge,!0)}}e:{if(te=V?sa(V):window,se=te.nodeName&&te.nodeName.toLowerCase(),se==="select"||se==="input"&&te.type==="file")var ve=Sv;else if(af(te))if(sf)ve=Ev;else{ve=Cv;var Ce=kv}else(se=te.nodeName)&&se.toLowerCase()==="input"&&(te.type==="checkbox"||te.type==="radio")&&(ve=$v);if(ve&&(ve=ve(t,V))){lf(oe,ve,a,ne);break e}Ce&&Ce(t,te,V),t==="focusout"&&(Ce=te._wrapperState)&&Ce.controlled&&te.type==="number"&&_e(te,"number",te.value)}switch(Ce=V?sa(V):window,t){case"focusin":(af(Ce)||Ce.contentEditable==="true")&&(oa=Ce,Iu=V,ai=null);break;case"focusout":ai=Iu=oa=null;break;case"mousedown":zu=!0;break;case"contextmenu":case"mouseup":case"dragend":zu=!1,gf(oe,a,ne);break;case"selectionchange":if(Av)break;case"keydown":case"keyup":gf(oe,a,ne)}var $e;if(Ru)e:{switch(t){case"compositionstart":var Te="onCompositionStart";break e;case"compositionend":Te="onCompositionEnd";break e;case"compositionupdate":Te="onCompositionUpdate";break e}Te=void 0}else ra?rf(t,a)&&(Te="onCompositionEnd"):t==="keydown"&&a.keyCode===229&&(Te="onCompositionStart");Te&&(ef&&a.locale!=="ko"&&(ra||Te!=="onCompositionStart"?Te==="onCompositionEnd"&&ra&&($e=Yp()):(Jr=ne,Eu="value"in Jr?Jr.value:Jr.textContent,ra=!0)),Ce=Al(V,Te),0<Ce.length&&(Te=new Jp(Te,t,null,a,ne),oe.push({event:Te,listeners:Ce}),$e?Te.data=$e:($e=of(a),$e!==null&&(Te.data=$e)))),($e=yv?vv(t,a):bv(t,a))&&(V=Al(V,"onBeforeInput"),0<V.length&&(ne=new Jp("onBeforeInput","beforeinput",null,a,ne),oe.push({event:ne,listeners:V}),ne.data=$e))}$f(oe,n)})}function si(t,n,a){return{instance:t,listener:n,currentTarget:a}}function Al(t,n){for(var a=n+"Capture",s=[];t!==null;){var c=t,p=c.stateNode;c.tag===5&&p!==null&&(c=p,p=bn(t,a),p!=null&&s.unshift(si(t,p,c)),p=bn(t,n),p!=null&&s.push(si(t,p,c))),t=t.return}return s}function ia(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5);return t||null}function Tf(t,n,a,s,c){for(var p=n._reactName,w=[];a!==null&&a!==s;){var O=a,D=O.alternate,V=O.stateNode;if(D!==null&&D===s)break;O.tag===5&&V!==null&&(O=V,c?(D=bn(a,p),D!=null&&w.unshift(si(a,D,O))):c||(D=bn(a,p),D!=null&&w.push(si(a,D,O)))),a=a.return}w.length!==0&&t.push({event:n,listeners:w})}var Mv=/\r\n?/g,Ov=/\u0000|\uFFFD/g;function Nf(t){return(typeof t=="string"?t:""+t).replace(Mv,`
`).replace(Ov,"")}function Pl(t,n,a){if(n=Nf(n),Nf(t)!==n&&a)throw Error(o(425))}function _l(){}var Wu=null,Uu=null;function Qu(t,n){return t==="textarea"||t==="noscript"||typeof n.children=="string"||typeof n.children=="number"||typeof n.dangerouslySetInnerHTML=="object"&&n.dangerouslySetInnerHTML!==null&&n.dangerouslySetInnerHTML.__html!=null}var Vu=typeof setTimeout=="function"?setTimeout:void 0,jv=typeof clearTimeout=="function"?clearTimeout:void 0,Af=typeof Promise=="function"?Promise:void 0,Iv=typeof queueMicrotask=="function"?queueMicrotask:typeof Af<"u"?function(t){return Af.resolve(null).then(t).catch(zv)}:Vu;function zv(t){setTimeout(function(){throw t})}function Ku(t,n){var a=n,s=0;do{var c=a.nextSibling;if(t.removeChild(a),c&&c.nodeType===8)if(a=c.data,a==="/$"){if(s===0){t.removeChild(c),Ja(n);return}s--}else a!=="$"&&a!=="$?"&&a!=="$!"||s++;a=c}while(a);Ja(n)}function eo(t){for(;t!=null;t=t.nextSibling){var n=t.nodeType;if(n===1||n===3)break;if(n===8){if(n=t.data,n==="$"||n==="$!"||n==="$?")break;if(n==="/$")return null}}return t}function Pf(t){t=t.previousSibling;for(var n=0;t;){if(t.nodeType===8){var a=t.data;if(a==="$"||a==="$!"||a==="$?"){if(n===0)return t;n--}else a==="/$"&&n++}t=t.previousSibling}return null}var la=Math.random().toString(36).slice(2),ar="__reactFiber$"+la,ui="__reactProps$"+la,Nr="__reactContainer$"+la,Gu="__reactEvents$"+la,Dv="__reactListeners$"+la,Fv="__reactHandles$"+la;function Eo(t){var n=t[ar];if(n)return n;for(var a=t.parentNode;a;){if(n=a[Nr]||a[ar]){if(a=n.alternate,n.child!==null||a!==null&&a.child!==null)for(t=Pf(t);t!==null;){if(a=t[ar])return a;t=Pf(t)}return n}t=a,a=t.parentNode}return null}function ci(t){return t=t[ar]||t[Nr],!t||t.tag!==5&&t.tag!==6&&t.tag!==13&&t.tag!==3?null:t}function sa(t){if(t.tag===5||t.tag===6)return t.stateNode;throw Error(o(33))}function Rl(t){return t[ui]||null}var Yu=[],ua=-1;function to(t){return{current:t}}function et(t){0>ua||(t.current=Yu[ua],Yu[ua]=null,ua--)}function Je(t,n){ua++,Yu[ua]=t.current,t.current=n}var no={},It=to(no),Yt=to(!1),To=no;function ca(t,n){var a=t.type.contextTypes;if(!a)return no;var s=t.stateNode;if(s&&s.__reactInternalMemoizedUnmaskedChildContext===n)return s.__reactInternalMemoizedMaskedChildContext;var c={},p;for(p in a)c[p]=n[p];return s&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=n,t.__reactInternalMemoizedMaskedChildContext=c),c}function Xt(t){return t=t.childContextTypes,t!=null}function Ml(){et(Yt),et(It)}function _f(t,n,a){if(It.current!==no)throw Error(o(168));Je(It,n),Je(Yt,a)}function Rf(t,n,a){var s=t.stateNode;if(n=n.childContextTypes,typeof s.getChildContext!="function")return a;s=s.getChildContext();for(var c in s)if(!(c in n))throw Error(o(108,be(t)||"Unknown",c));return K({},a,s)}function Ol(t){return t=(t=t.stateNode)&&t.__reactInternalMemoizedMergedChildContext||no,To=It.current,Je(It,t),Je(Yt,Yt.current),!0}function Mf(t,n,a){var s=t.stateNode;if(!s)throw Error(o(169));a?(t=Rf(t,n,To),s.__reactInternalMemoizedMergedChildContext=t,et(Yt),et(It),Je(It,t)):et(Yt),Je(Yt,a)}var Ar=null,jl=!1,Xu=!1;function Of(t){Ar===null?Ar=[t]:Ar.push(t)}function Bv(t){jl=!0,Of(t)}function ro(){if(!Xu&&Ar!==null){Xu=!0;var t=0,n=Ve;try{var a=Ar;for(Ve=1;t<a.length;t++){var s=a[t];do s=s(!0);while(s!==null)}Ar=null,jl=!1}catch(c){throw Ar!==null&&(Ar=Ar.slice(t+1)),Ke(Sn,ro),c}finally{Ve=n,Xu=!1}}return null}var da=[],pa=0,Il=null,zl=0,kn=[],Cn=0,No=null,Pr=1,_r="";function Ao(t,n){da[pa++]=zl,da[pa++]=Il,Il=t,zl=n}function jf(t,n,a){kn[Cn++]=Pr,kn[Cn++]=_r,kn[Cn++]=No,No=t;var s=Pr;t=_r;var c=32-Ln(s)-1;s&=~(1<<c),a+=1;var p=32-Ln(n)+c;if(30<p){var w=c-c%5;p=(s&(1<<w)-1).toString(32),s>>=w,c-=w,Pr=1<<32-Ln(n)+c|a<<c|s,_r=p+t}else Pr=1<<p|a<<c|s,_r=t}function Hu(t){t.return!==null&&(Ao(t,1),jf(t,1,0))}function Ju(t){for(;t===Il;)Il=da[--pa],da[pa]=null,zl=da[--pa],da[pa]=null;for(;t===No;)No=kn[--Cn],kn[Cn]=null,_r=kn[--Cn],kn[Cn]=null,Pr=kn[--Cn],kn[Cn]=null}var sn=null,un=null,nt=!1,Wn=null;function If(t,n){var a=Nn(5,null,null,0);a.elementType="DELETED",a.stateNode=n,a.return=t,n=t.deletions,n===null?(t.deletions=[a],t.flags|=16):n.push(a)}function zf(t,n){switch(t.tag){case 5:var a=t.type;return n=n.nodeType!==1||a.toLowerCase()!==n.nodeName.toLowerCase()?null:n,n!==null?(t.stateNode=n,sn=t,un=eo(n.firstChild),!0):!1;case 6:return n=t.pendingProps===""||n.nodeType!==3?null:n,n!==null?(t.stateNode=n,sn=t,un=null,!0):!1;case 13:return n=n.nodeType!==8?null:n,n!==null?(a=No!==null?{id:Pr,overflow:_r}:null,t.memoizedState={dehydrated:n,treeContext:a,retryLane:1073741824},a=Nn(18,null,null,0),a.stateNode=n,a.return=t,t.child=a,sn=t,un=null,!0):!1;default:return!1}}function Zu(t){return(t.mode&1)!==0&&(t.flags&128)===0}function ec(t){if(nt){var n=un;if(n){var a=n;if(!zf(t,n)){if(Zu(t))throw Error(o(418));n=eo(a.nextSibling);var s=sn;n&&zf(t,n)?If(s,a):(t.flags=t.flags&-4097|2,nt=!1,sn=t)}}else{if(Zu(t))throw Error(o(418));t.flags=t.flags&-4097|2,nt=!1,sn=t}}}function Df(t){for(t=t.return;t!==null&&t.tag!==5&&t.tag!==3&&t.tag!==13;)t=t.return;sn=t}function Dl(t){if(t!==sn)return!1;if(!nt)return Df(t),nt=!0,!1;var n;if((n=t.tag!==3)&&!(n=t.tag!==5)&&(n=t.type,n=n!=="head"&&n!=="body"&&!Qu(t.type,t.memoizedProps)),n&&(n=un)){if(Zu(t))throw Ff(),Error(o(418));for(;n;)If(t,n),n=eo(n.nextSibling)}if(Df(t),t.tag===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(o(317));e:{for(t=t.nextSibling,n=0;t;){if(t.nodeType===8){var a=t.data;if(a==="/$"){if(n===0){un=eo(t.nextSibling);break e}n--}else a!=="$"&&a!=="$!"&&a!=="$?"||n++}t=t.nextSibling}un=null}}else un=sn?eo(t.stateNode.nextSibling):null;return!0}function Ff(){for(var t=un;t;)t=eo(t.nextSibling)}function fa(){un=sn=null,nt=!1}function tc(t){Wn===null?Wn=[t]:Wn.push(t)}var Lv=C.ReactCurrentBatchConfig;function di(t,n,a){if(t=a.ref,t!==null&&typeof t!="function"&&typeof t!="object"){if(a._owner){if(a=a._owner,a){if(a.tag!==1)throw Error(o(309));var s=a.stateNode}if(!s)throw Error(o(147,t));var c=s,p=""+t;return n!==null&&n.ref!==null&&typeof n.ref=="function"&&n.ref._stringRef===p?n.ref:(n=function(w){var O=c.refs;w===null?delete O[p]:O[p]=w},n._stringRef=p,n)}if(typeof t!="string")throw Error(o(284));if(!a._owner)throw Error(o(290,t))}return t}function Fl(t,n){throw t=Object.prototype.toString.call(n),Error(o(31,t==="[object Object]"?"object with keys {"+Object.keys(n).join(", ")+"}":t))}function Bf(t){var n=t._init;return n(t._payload)}function Lf(t){function n(U,B){if(t){var Q=U.deletions;Q===null?(U.deletions=[B],U.flags|=16):Q.push(B)}}function a(U,B){if(!t)return null;for(;B!==null;)n(U,B),B=B.sibling;return null}function s(U,B){for(U=new Map;B!==null;)B.key!==null?U.set(B.key,B):U.set(B.index,B),B=B.sibling;return U}function c(U,B){return U=po(U,B),U.index=0,U.sibling=null,U}function p(U,B,Q){return U.index=Q,t?(Q=U.alternate,Q!==null?(Q=Q.index,Q<B?(U.flags|=2,B):Q):(U.flags|=2,B)):(U.flags|=1048576,B)}function w(U){return t&&U.alternate===null&&(U.flags|=2),U}function O(U,B,Q,ae){return B===null||B.tag!==6?(B=Qc(Q,U.mode,ae),B.return=U,B):(B=c(B,Q),B.return=U,B)}function D(U,B,Q,ae){var ve=Q.type;return ve===I?ne(U,B,Q.props.children,ae,Q.key):B!==null&&(B.elementType===ve||typeof ve=="object"&&ve!==null&&ve.$$typeof===H&&Bf(ve)===B.type)?(ae=c(B,Q.props),ae.ref=di(U,B,Q),ae.return=U,ae):(ae=us(Q.type,Q.key,Q.props,null,U.mode,ae),ae.ref=di(U,B,Q),ae.return=U,ae)}function V(U,B,Q,ae){return B===null||B.tag!==4||B.stateNode.containerInfo!==Q.containerInfo||B.stateNode.implementation!==Q.implementation?(B=Vc(Q,U.mode,ae),B.return=U,B):(B=c(B,Q.children||[]),B.return=U,B)}function ne(U,B,Q,ae,ve){return B===null||B.tag!==7?(B=zo(Q,U.mode,ae,ve),B.return=U,B):(B=c(B,Q),B.return=U,B)}function oe(U,B,Q){if(typeof B=="string"&&B!==""||typeof B=="number")return B=Qc(""+B,U.mode,Q),B.return=U,B;if(typeof B=="object"&&B!==null){switch(B.$$typeof){case k:return Q=us(B.type,B.key,B.props,null,U.mode,Q),Q.ref=di(U,null,B),Q.return=U,Q;case M:return B=Vc(B,U.mode,Q),B.return=U,B;case H:var ae=B._init;return oe(U,ae(B._payload),Q)}if(Ye(B)||ee(B))return B=zo(B,U.mode,Q,null),B.return=U,B;Fl(U,B)}return null}function te(U,B,Q,ae){var ve=B!==null?B.key:null;if(typeof Q=="string"&&Q!==""||typeof Q=="number")return ve!==null?null:O(U,B,""+Q,ae);if(typeof Q=="object"&&Q!==null){switch(Q.$$typeof){case k:return Q.key===ve?D(U,B,Q,ae):null;case M:return Q.key===ve?V(U,B,Q,ae):null;case H:return ve=Q._init,te(U,B,ve(Q._payload),ae)}if(Ye(Q)||ee(Q))return ve!==null?null:ne(U,B,Q,ae,null);Fl(U,Q)}return null}function se(U,B,Q,ae,ve){if(typeof ae=="string"&&ae!==""||typeof ae=="number")return U=U.get(Q)||null,O(B,U,""+ae,ve);if(typeof ae=="object"&&ae!==null){switch(ae.$$typeof){case k:return U=U.get(ae.key===null?Q:ae.key)||null,D(B,U,ae,ve);case M:return U=U.get(ae.key===null?Q:ae.key)||null,V(B,U,ae,ve);case H:var Ce=ae._init;return se(U,B,Q,Ce(ae._payload),ve)}if(Ye(ae)||ee(ae))return U=U.get(Q)||null,ne(B,U,ae,ve,null);Fl(B,ae)}return null}function me(U,B,Q,ae){for(var ve=null,Ce=null,$e=B,Te=B=0,kt=null;$e!==null&&Te<Q.length;Te++){$e.index>Te?(kt=$e,$e=null):kt=$e.sibling;var Fe=te(U,$e,Q[Te],ae);if(Fe===null){$e===null&&($e=kt);break}t&&$e&&Fe.alternate===null&&n(U,$e),B=p(Fe,B,Te),Ce===null?ve=Fe:Ce.sibling=Fe,Ce=Fe,$e=kt}if(Te===Q.length)return a(U,$e),nt&&Ao(U,Te),ve;if($e===null){for(;Te<Q.length;Te++)$e=oe(U,Q[Te],ae),$e!==null&&(B=p($e,B,Te),Ce===null?ve=$e:Ce.sibling=$e,Ce=$e);return nt&&Ao(U,Te),ve}for($e=s(U,$e);Te<Q.length;Te++)kt=se($e,U,Te,Q[Te],ae),kt!==null&&(t&&kt.alternate!==null&&$e.delete(kt.key===null?Te:kt.key),B=p(kt,B,Te),Ce===null?ve=kt:Ce.sibling=kt,Ce=kt);return t&&$e.forEach(function(fo){return n(U,fo)}),nt&&Ao(U,Te),ve}function ge(U,B,Q,ae){var ve=ee(Q);if(typeof ve!="function")throw Error(o(150));if(Q=ve.call(Q),Q==null)throw Error(o(151));for(var Ce=ve=null,$e=B,Te=B=0,kt=null,Fe=Q.next();$e!==null&&!Fe.done;Te++,Fe=Q.next()){$e.index>Te?(kt=$e,$e=null):kt=$e.sibling;var fo=te(U,$e,Fe.value,ae);if(fo===null){$e===null&&($e=kt);break}t&&$e&&fo.alternate===null&&n(U,$e),B=p(fo,B,Te),Ce===null?ve=fo:Ce.sibling=fo,Ce=fo,$e=kt}if(Fe.done)return a(U,$e),nt&&Ao(U,Te),ve;if($e===null){for(;!Fe.done;Te++,Fe=Q.next())Fe=oe(U,Fe.value,ae),Fe!==null&&(B=p(Fe,B,Te),Ce===null?ve=Fe:Ce.sibling=Fe,Ce=Fe);return nt&&Ao(U,Te),ve}for($e=s(U,$e);!Fe.done;Te++,Fe=Q.next())Fe=se($e,U,Te,Fe.value,ae),Fe!==null&&(t&&Fe.alternate!==null&&$e.delete(Fe.key===null?Te:Fe.key),B=p(Fe,B,Te),Ce===null?ve=Fe:Ce.sibling=Fe,Ce=Fe);return t&&$e.forEach(function(xb){return n(U,xb)}),nt&&Ao(U,Te),ve}function ft(U,B,Q,ae){if(typeof Q=="object"&&Q!==null&&Q.type===I&&Q.key===null&&(Q=Q.props.children),typeof Q=="object"&&Q!==null){switch(Q.$$typeof){case k:e:{for(var ve=Q.key,Ce=B;Ce!==null;){if(Ce.key===ve){if(ve=Q.type,ve===I){if(Ce.tag===7){a(U,Ce.sibling),B=c(Ce,Q.props.children),B.return=U,U=B;break e}}else if(Ce.elementType===ve||typeof ve=="object"&&ve!==null&&ve.$$typeof===H&&Bf(ve)===Ce.type){a(U,Ce.sibling),B=c(Ce,Q.props),B.ref=di(U,Ce,Q),B.return=U,U=B;break e}a(U,Ce);break}else n(U,Ce);Ce=Ce.sibling}Q.type===I?(B=zo(Q.props.children,U.mode,ae,Q.key),B.return=U,U=B):(ae=us(Q.type,Q.key,Q.props,null,U.mode,ae),ae.ref=di(U,B,Q),ae.return=U,U=ae)}return w(U);case M:e:{for(Ce=Q.key;B!==null;){if(B.key===Ce)if(B.tag===4&&B.stateNode.containerInfo===Q.containerInfo&&B.stateNode.implementation===Q.implementation){a(U,B.sibling),B=c(B,Q.children||[]),B.return=U,U=B;break e}else{a(U,B);break}else n(U,B);B=B.sibling}B=Vc(Q,U.mode,ae),B.return=U,U=B}return w(U);case H:return Ce=Q._init,ft(U,B,Ce(Q._payload),ae)}if(Ye(Q))return me(U,B,Q,ae);if(ee(Q))return ge(U,B,Q,ae);Fl(U,Q)}return typeof Q=="string"&&Q!==""||typeof Q=="number"?(Q=""+Q,B!==null&&B.tag===6?(a(U,B.sibling),B=c(B,Q),B.return=U,U=B):(a(U,B),B=Qc(Q,U.mode,ae),B.return=U,U=B),w(U)):a(U,B)}return ft}var ma=Lf(!0),qf=Lf(!1),Bl=to(null),Ll=null,ha=null,nc=null;function rc(){nc=ha=Ll=null}function oc(t){var n=Bl.current;et(Bl),t._currentValue=n}function ac(t,n,a){for(;t!==null;){var s=t.alternate;if((t.childLanes&n)!==n?(t.childLanes|=n,s!==null&&(s.childLanes|=n)):s!==null&&(s.childLanes&n)!==n&&(s.childLanes|=n),t===a)break;t=t.return}}function ga(t,n){Ll=t,nc=ha=null,t=t.dependencies,t!==null&&t.firstContext!==null&&(t.lanes&n&&(Ht=!0),t.firstContext=null)}function $n(t){var n=t._currentValue;if(nc!==t)if(t={context:t,memoizedValue:n,next:null},ha===null){if(Ll===null)throw Error(o(308));ha=t,Ll.dependencies={lanes:0,firstContext:t}}else ha=ha.next=t;return n}var Po=null;function ic(t){Po===null?Po=[t]:Po.push(t)}function Wf(t,n,a,s){var c=n.interleaved;return c===null?(a.next=a,ic(n)):(a.next=c.next,c.next=a),n.interleaved=a,Rr(t,s)}function Rr(t,n){t.lanes|=n;var a=t.alternate;for(a!==null&&(a.lanes|=n),a=t,t=t.return;t!==null;)t.childLanes|=n,a=t.alternate,a!==null&&(a.childLanes|=n),a=t,t=t.return;return a.tag===3?a.stateNode:null}var oo=!1;function lc(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Uf(t,n){t=t.updateQueue,n.updateQueue===t&&(n.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,effects:t.effects})}function Mr(t,n){return{eventTime:t,lane:n,tag:0,payload:null,callback:null,next:null}}function ao(t,n,a){var s=t.updateQueue;if(s===null)return null;if(s=s.shared,De&2){var c=s.pending;return c===null?n.next=n:(n.next=c.next,c.next=n),s.pending=n,Rr(t,a)}return c=s.interleaved,c===null?(n.next=n,ic(s)):(n.next=c.next,c.next=n),s.interleaved=n,Rr(t,a)}function ql(t,n,a){if(n=n.updateQueue,n!==null&&(n=n.shared,(a&4194240)!==0)){var s=n.lanes;s&=t.pendingLanes,a|=s,n.lanes=a,wu(t,a)}}function Qf(t,n){var a=t.updateQueue,s=t.alternate;if(s!==null&&(s=s.updateQueue,a===s)){var c=null,p=null;if(a=a.firstBaseUpdate,a!==null){do{var w={eventTime:a.eventTime,lane:a.lane,tag:a.tag,payload:a.payload,callback:a.callback,next:null};p===null?c=p=w:p=p.next=w,a=a.next}while(a!==null);p===null?c=p=n:p=p.next=n}else c=p=n;a={baseState:s.baseState,firstBaseUpdate:c,lastBaseUpdate:p,shared:s.shared,effects:s.effects},t.updateQueue=a;return}t=a.lastBaseUpdate,t===null?a.firstBaseUpdate=n:t.next=n,a.lastBaseUpdate=n}function Wl(t,n,a,s){var c=t.updateQueue;oo=!1;var p=c.firstBaseUpdate,w=c.lastBaseUpdate,O=c.shared.pending;if(O!==null){c.shared.pending=null;var D=O,V=D.next;D.next=null,w===null?p=V:w.next=V,w=D;var ne=t.alternate;ne!==null&&(ne=ne.updateQueue,O=ne.lastBaseUpdate,O!==w&&(O===null?ne.firstBaseUpdate=V:O.next=V,ne.lastBaseUpdate=D))}if(p!==null){var oe=c.baseState;w=0,ne=V=D=null,O=p;do{var te=O.lane,se=O.eventTime;if((s&te)===te){ne!==null&&(ne=ne.next={eventTime:se,lane:0,tag:O.tag,payload:O.payload,callback:O.callback,next:null});e:{var me=t,ge=O;switch(te=n,se=a,ge.tag){case 1:if(me=ge.payload,typeof me=="function"){oe=me.call(se,oe,te);break e}oe=me;break e;case 3:me.flags=me.flags&-65537|128;case 0:if(me=ge.payload,te=typeof me=="function"?me.call(se,oe,te):me,te==null)break e;oe=K({},oe,te);break e;case 2:oo=!0}}O.callback!==null&&O.lane!==0&&(t.flags|=64,te=c.effects,te===null?c.effects=[O]:te.push(O))}else se={eventTime:se,lane:te,tag:O.tag,payload:O.payload,callback:O.callback,next:null},ne===null?(V=ne=se,D=oe):ne=ne.next=se,w|=te;if(O=O.next,O===null){if(O=c.shared.pending,O===null)break;te=O,O=te.next,te.next=null,c.lastBaseUpdate=te,c.shared.pending=null}}while(!0);if(ne===null&&(D=oe),c.baseState=D,c.firstBaseUpdate=V,c.lastBaseUpdate=ne,n=c.shared.interleaved,n!==null){c=n;do w|=c.lane,c=c.next;while(c!==n)}else p===null&&(c.shared.lanes=0);Mo|=w,t.lanes=w,t.memoizedState=oe}}function Vf(t,n,a){if(t=n.effects,n.effects=null,t!==null)for(n=0;n<t.length;n++){var s=t[n],c=s.callback;if(c!==null){if(s.callback=null,s=a,typeof c!="function")throw Error(o(191,c));c.call(s)}}}var pi={},ir=to(pi),fi=to(pi),mi=to(pi);function _o(t){if(t===pi)throw Error(o(174));return t}function sc(t,n){switch(Je(mi,n),Je(fi,t),Je(ir,pi),t=n.nodeType,t){case 9:case 11:n=(n=n.documentElement)?n.namespaceURI:Dn(null,"");break;default:t=t===8?n.parentNode:n,n=t.namespaceURI||null,t=t.tagName,n=Dn(n,t)}et(ir),Je(ir,n)}function ya(){et(ir),et(fi),et(mi)}function Kf(t){_o(mi.current);var n=_o(ir.current),a=Dn(n,t.type);n!==a&&(Je(fi,t),Je(ir,a))}function uc(t){fi.current===t&&(et(ir),et(fi))}var ot=to(0);function Ul(t){for(var n=t;n!==null;){if(n.tag===13){var a=n.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||a.data==="$!"))return n}else if(n.tag===19&&n.memoizedProps.revealOrder!==void 0){if(n.flags&128)return n}else if(n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return null;n=n.return}n.sibling.return=n.return,n=n.sibling}return null}var cc=[];function dc(){for(var t=0;t<cc.length;t++)cc[t]._workInProgressVersionPrimary=null;cc.length=0}var Ql=C.ReactCurrentDispatcher,pc=C.ReactCurrentBatchConfig,Ro=0,at=null,bt=null,wt=null,Vl=!1,hi=!1,gi=0,qv=0;function zt(){throw Error(o(321))}function fc(t,n){if(n===null)return!1;for(var a=0;a<n.length&&a<t.length;a++)if(!qn(t[a],n[a]))return!1;return!0}function mc(t,n,a,s,c,p){if(Ro=p,at=n,n.memoizedState=null,n.updateQueue=null,n.lanes=0,Ql.current=t===null||t.memoizedState===null?Vv:Kv,t=a(s,c),hi){p=0;do{if(hi=!1,gi=0,25<=p)throw Error(o(301));p+=1,wt=bt=null,n.updateQueue=null,Ql.current=Gv,t=a(s,c)}while(hi)}if(Ql.current=Yl,n=bt!==null&&bt.next!==null,Ro=0,wt=bt=at=null,Vl=!1,n)throw Error(o(300));return t}function hc(){var t=gi!==0;return gi=0,t}function lr(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return wt===null?at.memoizedState=wt=t:wt=wt.next=t,wt}function En(){if(bt===null){var t=at.alternate;t=t!==null?t.memoizedState:null}else t=bt.next;var n=wt===null?at.memoizedState:wt.next;if(n!==null)wt=n,bt=t;else{if(t===null)throw Error(o(310));bt=t,t={memoizedState:bt.memoizedState,baseState:bt.baseState,baseQueue:bt.baseQueue,queue:bt.queue,next:null},wt===null?at.memoizedState=wt=t:wt=wt.next=t}return wt}function yi(t,n){return typeof n=="function"?n(t):n}function gc(t){var n=En(),a=n.queue;if(a===null)throw Error(o(311));a.lastRenderedReducer=t;var s=bt,c=s.baseQueue,p=a.pending;if(p!==null){if(c!==null){var w=c.next;c.next=p.next,p.next=w}s.baseQueue=c=p,a.pending=null}if(c!==null){p=c.next,s=s.baseState;var O=w=null,D=null,V=p;do{var ne=V.lane;if((Ro&ne)===ne)D!==null&&(D=D.next={lane:0,action:V.action,hasEagerState:V.hasEagerState,eagerState:V.eagerState,next:null}),s=V.hasEagerState?V.eagerState:t(s,V.action);else{var oe={lane:ne,action:V.action,hasEagerState:V.hasEagerState,eagerState:V.eagerState,next:null};D===null?(O=D=oe,w=s):D=D.next=oe,at.lanes|=ne,Mo|=ne}V=V.next}while(V!==null&&V!==p);D===null?w=s:D.next=O,qn(s,n.memoizedState)||(Ht=!0),n.memoizedState=s,n.baseState=w,n.baseQueue=D,a.lastRenderedState=s}if(t=a.interleaved,t!==null){c=t;do p=c.lane,at.lanes|=p,Mo|=p,c=c.next;while(c!==t)}else c===null&&(a.lanes=0);return[n.memoizedState,a.dispatch]}function yc(t){var n=En(),a=n.queue;if(a===null)throw Error(o(311));a.lastRenderedReducer=t;var s=a.dispatch,c=a.pending,p=n.memoizedState;if(c!==null){a.pending=null;var w=c=c.next;do p=t(p,w.action),w=w.next;while(w!==c);qn(p,n.memoizedState)||(Ht=!0),n.memoizedState=p,n.baseQueue===null&&(n.baseState=p),a.lastRenderedState=p}return[p,s]}function Gf(){}function Yf(t,n){var a=at,s=En(),c=n(),p=!qn(s.memoizedState,c);if(p&&(s.memoizedState=c,Ht=!0),s=s.queue,vc(Jf.bind(null,a,s,t),[t]),s.getSnapshot!==n||p||wt!==null&&wt.memoizedState.tag&1){if(a.flags|=2048,vi(9,Hf.bind(null,a,s,c,n),void 0,null),St===null)throw Error(o(349));Ro&30||Xf(a,n,c)}return c}function Xf(t,n,a){t.flags|=16384,t={getSnapshot:n,value:a},n=at.updateQueue,n===null?(n={lastEffect:null,stores:null},at.updateQueue=n,n.stores=[t]):(a=n.stores,a===null?n.stores=[t]:a.push(t))}function Hf(t,n,a,s){n.value=a,n.getSnapshot=s,Zf(n)&&em(t)}function Jf(t,n,a){return a(function(){Zf(n)&&em(t)})}function Zf(t){var n=t.getSnapshot;t=t.value;try{var a=n();return!qn(t,a)}catch{return!0}}function em(t){var n=Rr(t,1);n!==null&&Kn(n,t,1,-1)}function tm(t){var n=lr();return typeof t=="function"&&(t=t()),n.memoizedState=n.baseState=t,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:yi,lastRenderedState:t},n.queue=t,t=t.dispatch=Qv.bind(null,at,t),[n.memoizedState,t]}function vi(t,n,a,s){return t={tag:t,create:n,destroy:a,deps:s,next:null},n=at.updateQueue,n===null?(n={lastEffect:null,stores:null},at.updateQueue=n,n.lastEffect=t.next=t):(a=n.lastEffect,a===null?n.lastEffect=t.next=t:(s=a.next,a.next=t,t.next=s,n.lastEffect=t)),t}function nm(){return En().memoizedState}function Kl(t,n,a,s){var c=lr();at.flags|=t,c.memoizedState=vi(1|n,a,void 0,s===void 0?null:s)}function Gl(t,n,a,s){var c=En();s=s===void 0?null:s;var p=void 0;if(bt!==null){var w=bt.memoizedState;if(p=w.destroy,s!==null&&fc(s,w.deps)){c.memoizedState=vi(n,a,p,s);return}}at.flags|=t,c.memoizedState=vi(1|n,a,p,s)}function rm(t,n){return Kl(8390656,8,t,n)}function vc(t,n){return Gl(2048,8,t,n)}function om(t,n){return Gl(4,2,t,n)}function am(t,n){return Gl(4,4,t,n)}function im(t,n){if(typeof n=="function")return t=t(),n(t),function(){n(null)};if(n!=null)return t=t(),n.current=t,function(){n.current=null}}function lm(t,n,a){return a=a!=null?a.concat([t]):null,Gl(4,4,im.bind(null,n,t),a)}function bc(){}function sm(t,n){var a=En();n=n===void 0?null:n;var s=a.memoizedState;return s!==null&&n!==null&&fc(n,s[1])?s[0]:(a.memoizedState=[t,n],t)}function um(t,n){var a=En();n=n===void 0?null:n;var s=a.memoizedState;return s!==null&&n!==null&&fc(n,s[1])?s[0]:(t=t(),a.memoizedState=[t,n],t)}function cm(t,n,a){return Ro&21?(qn(a,n)||(a=Fp(),at.lanes|=a,Mo|=a,t.baseState=!0),n):(t.baseState&&(t.baseState=!1,Ht=!0),t.memoizedState=a)}function Wv(t,n){var a=Ve;Ve=a!==0&&4>a?a:4,t(!0);var s=pc.transition;pc.transition={};try{t(!1),n()}finally{Ve=a,pc.transition=s}}function dm(){return En().memoizedState}function Uv(t,n,a){var s=uo(t);if(a={lane:s,action:a,hasEagerState:!1,eagerState:null,next:null},pm(t))fm(n,a);else if(a=Wf(t,n,a,s),a!==null){var c=Kt();Kn(a,t,s,c),mm(a,n,s)}}function Qv(t,n,a){var s=uo(t),c={lane:s,action:a,hasEagerState:!1,eagerState:null,next:null};if(pm(t))fm(n,c);else{var p=t.alternate;if(t.lanes===0&&(p===null||p.lanes===0)&&(p=n.lastRenderedReducer,p!==null))try{var w=n.lastRenderedState,O=p(w,a);if(c.hasEagerState=!0,c.eagerState=O,qn(O,w)){var D=n.interleaved;D===null?(c.next=c,ic(n)):(c.next=D.next,D.next=c),n.interleaved=c;return}}catch{}finally{}a=Wf(t,n,c,s),a!==null&&(c=Kt(),Kn(a,t,s,c),mm(a,n,s))}}function pm(t){var n=t.alternate;return t===at||n!==null&&n===at}function fm(t,n){hi=Vl=!0;var a=t.pending;a===null?n.next=n:(n.next=a.next,a.next=n),t.pending=n}function mm(t,n,a){if(a&4194240){var s=n.lanes;s&=t.pendingLanes,a|=s,n.lanes=a,wu(t,a)}}var Yl={readContext:$n,useCallback:zt,useContext:zt,useEffect:zt,useImperativeHandle:zt,useInsertionEffect:zt,useLayoutEffect:zt,useMemo:zt,useReducer:zt,useRef:zt,useState:zt,useDebugValue:zt,useDeferredValue:zt,useTransition:zt,useMutableSource:zt,useSyncExternalStore:zt,useId:zt,unstable_isNewReconciler:!1},Vv={readContext:$n,useCallback:function(t,n){return lr().memoizedState=[t,n===void 0?null:n],t},useContext:$n,useEffect:rm,useImperativeHandle:function(t,n,a){return a=a!=null?a.concat([t]):null,Kl(4194308,4,im.bind(null,n,t),a)},useLayoutEffect:function(t,n){return Kl(4194308,4,t,n)},useInsertionEffect:function(t,n){return Kl(4,2,t,n)},useMemo:function(t,n){var a=lr();return n=n===void 0?null:n,t=t(),a.memoizedState=[t,n],t},useReducer:function(t,n,a){var s=lr();return n=a!==void 0?a(n):n,s.memoizedState=s.baseState=n,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:n},s.queue=t,t=t.dispatch=Uv.bind(null,at,t),[s.memoizedState,t]},useRef:function(t){var n=lr();return t={current:t},n.memoizedState=t},useState:tm,useDebugValue:bc,useDeferredValue:function(t){return lr().memoizedState=t},useTransition:function(){var t=tm(!1),n=t[0];return t=Wv.bind(null,t[1]),lr().memoizedState=t,[n,t]},useMutableSource:function(){},useSyncExternalStore:function(t,n,a){var s=at,c=lr();if(nt){if(a===void 0)throw Error(o(407));a=a()}else{if(a=n(),St===null)throw Error(o(349));Ro&30||Xf(s,n,a)}c.memoizedState=a;var p={value:a,getSnapshot:n};return c.queue=p,rm(Jf.bind(null,s,p,t),[t]),s.flags|=2048,vi(9,Hf.bind(null,s,p,a,n),void 0,null),a},useId:function(){var t=lr(),n=St.identifierPrefix;if(nt){var a=_r,s=Pr;a=(s&~(1<<32-Ln(s)-1)).toString(32)+a,n=":"+n+"R"+a,a=gi++,0<a&&(n+="H"+a.toString(32)),n+=":"}else a=qv++,n=":"+n+"r"+a.toString(32)+":";return t.memoizedState=n},unstable_isNewReconciler:!1},Kv={readContext:$n,useCallback:sm,useContext:$n,useEffect:vc,useImperativeHandle:lm,useInsertionEffect:om,useLayoutEffect:am,useMemo:um,useReducer:gc,useRef:nm,useState:function(){return gc(yi)},useDebugValue:bc,useDeferredValue:function(t){var n=En();return cm(n,bt.memoizedState,t)},useTransition:function(){var t=gc(yi)[0],n=En().memoizedState;return[t,n]},useMutableSource:Gf,useSyncExternalStore:Yf,useId:dm,unstable_isNewReconciler:!1},Gv={readContext:$n,useCallback:sm,useContext:$n,useEffect:vc,useImperativeHandle:lm,useInsertionEffect:om,useLayoutEffect:am,useMemo:um,useReducer:yc,useRef:nm,useState:function(){return yc(yi)},useDebugValue:bc,useDeferredValue:function(t){var n=En();return bt===null?n.memoizedState=t:cm(n,bt.memoizedState,t)},useTransition:function(){var t=yc(yi)[0],n=En().memoizedState;return[t,n]},useMutableSource:Gf,useSyncExternalStore:Yf,useId:dm,unstable_isNewReconciler:!1};function Un(t,n){if(t&&t.defaultProps){n=K({},n),t=t.defaultProps;for(var a in t)n[a]===void 0&&(n[a]=t[a]);return n}return n}function xc(t,n,a,s){n=t.memoizedState,a=a(s,n),a=a==null?n:K({},n,a),t.memoizedState=a,t.lanes===0&&(t.updateQueue.baseState=a)}var Xl={isMounted:function(t){return(t=t._reactInternals)?xn(t)===t:!1},enqueueSetState:function(t,n,a){t=t._reactInternals;var s=Kt(),c=uo(t),p=Mr(s,c);p.payload=n,a!=null&&(p.callback=a),n=ao(t,p,c),n!==null&&(Kn(n,t,c,s),ql(n,t,c))},enqueueReplaceState:function(t,n,a){t=t._reactInternals;var s=Kt(),c=uo(t),p=Mr(s,c);p.tag=1,p.payload=n,a!=null&&(p.callback=a),n=ao(t,p,c),n!==null&&(Kn(n,t,c,s),ql(n,t,c))},enqueueForceUpdate:function(t,n){t=t._reactInternals;var a=Kt(),s=uo(t),c=Mr(a,s);c.tag=2,n!=null&&(c.callback=n),n=ao(t,c,s),n!==null&&(Kn(n,t,s,a),ql(n,t,s))}};function hm(t,n,a,s,c,p,w){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(s,p,w):n.prototype&&n.prototype.isPureReactComponent?!oi(a,s)||!oi(c,p):!0}function gm(t,n,a){var s=!1,c=no,p=n.contextType;return typeof p=="object"&&p!==null?p=$n(p):(c=Xt(n)?To:It.current,s=n.contextTypes,p=(s=s!=null)?ca(t,c):no),n=new n(a,p),t.memoizedState=n.state!==null&&n.state!==void 0?n.state:null,n.updater=Xl,t.stateNode=n,n._reactInternals=t,s&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=c,t.__reactInternalMemoizedMaskedChildContext=p),n}function ym(t,n,a,s){t=n.state,typeof n.componentWillReceiveProps=="function"&&n.componentWillReceiveProps(a,s),typeof n.UNSAFE_componentWillReceiveProps=="function"&&n.UNSAFE_componentWillReceiveProps(a,s),n.state!==t&&Xl.enqueueReplaceState(n,n.state,null)}function wc(t,n,a,s){var c=t.stateNode;c.props=a,c.state=t.memoizedState,c.refs={},lc(t);var p=n.contextType;typeof p=="object"&&p!==null?c.context=$n(p):(p=Xt(n)?To:It.current,c.context=ca(t,p)),c.state=t.memoizedState,p=n.getDerivedStateFromProps,typeof p=="function"&&(xc(t,n,p,a),c.state=t.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(n=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),n!==c.state&&Xl.enqueueReplaceState(c,c.state,null),Wl(t,a,c,s),c.state=t.memoizedState),typeof c.componentDidMount=="function"&&(t.flags|=4194308)}function va(t,n){try{var a="",s=n;do a+=ie(s),s=s.return;while(s);var c=a}catch(p){c=`
Error generating stack: `+p.message+`
`+p.stack}return{value:t,source:n,stack:c,digest:null}}function Sc(t,n,a){return{value:t,source:null,stack:a??null,digest:n??null}}function kc(t,n){try{console.error(n.value)}catch(a){setTimeout(function(){throw a})}}var Yv=typeof WeakMap=="function"?WeakMap:Map;function vm(t,n,a){a=Mr(-1,a),a.tag=3,a.payload={element:null};var s=n.value;return a.callback=function(){rs||(rs=!0,zc=s),kc(t,n)},a}function bm(t,n,a){a=Mr(-1,a),a.tag=3;var s=t.type.getDerivedStateFromError;if(typeof s=="function"){var c=n.value;a.payload=function(){return s(c)},a.callback=function(){kc(t,n)}}var p=t.stateNode;return p!==null&&typeof p.componentDidCatch=="function"&&(a.callback=function(){kc(t,n),typeof s!="function"&&(lo===null?lo=new Set([this]):lo.add(this));var w=n.stack;this.componentDidCatch(n.value,{componentStack:w!==null?w:""})}),a}function xm(t,n,a){var s=t.pingCache;if(s===null){s=t.pingCache=new Yv;var c=new Set;s.set(n,c)}else c=s.get(n),c===void 0&&(c=new Set,s.set(n,c));c.has(a)||(c.add(a),t=ub.bind(null,t,n,a),n.then(t,t))}function wm(t){do{var n;if((n=t.tag===13)&&(n=t.memoizedState,n=n!==null?n.dehydrated!==null:!0),n)return t;t=t.return}while(t!==null);return null}function Sm(t,n,a,s,c){return t.mode&1?(t.flags|=65536,t.lanes=c,t):(t===n?t.flags|=65536:(t.flags|=128,a.flags|=131072,a.flags&=-52805,a.tag===1&&(a.alternate===null?a.tag=17:(n=Mr(-1,1),n.tag=2,ao(a,n,1))),a.lanes|=1),t)}var Xv=C.ReactCurrentOwner,Ht=!1;function Vt(t,n,a,s){n.child=t===null?qf(n,null,a,s):ma(n,t.child,a,s)}function km(t,n,a,s,c){a=a.render;var p=n.ref;return ga(n,c),s=mc(t,n,a,s,p,c),a=hc(),t!==null&&!Ht?(n.updateQueue=t.updateQueue,n.flags&=-2053,t.lanes&=~c,Or(t,n,c)):(nt&&a&&Hu(n),n.flags|=1,Vt(t,n,s,c),n.child)}function Cm(t,n,a,s,c){if(t===null){var p=a.type;return typeof p=="function"&&!Uc(p)&&p.defaultProps===void 0&&a.compare===null&&a.defaultProps===void 0?(n.tag=15,n.type=p,$m(t,n,p,s,c)):(t=us(a.type,null,s,n,n.mode,c),t.ref=n.ref,t.return=n,n.child=t)}if(p=t.child,!(t.lanes&c)){var w=p.memoizedProps;if(a=a.compare,a=a!==null?a:oi,a(w,s)&&t.ref===n.ref)return Or(t,n,c)}return n.flags|=1,t=po(p,s),t.ref=n.ref,t.return=n,n.child=t}function $m(t,n,a,s,c){if(t!==null){var p=t.memoizedProps;if(oi(p,s)&&t.ref===n.ref)if(Ht=!1,n.pendingProps=s=p,(t.lanes&c)!==0)t.flags&131072&&(Ht=!0);else return n.lanes=t.lanes,Or(t,n,c)}return Cc(t,n,a,s,c)}function Em(t,n,a){var s=n.pendingProps,c=s.children,p=t!==null?t.memoizedState:null;if(s.mode==="hidden")if(!(n.mode&1))n.memoizedState={baseLanes:0,cachePool:null,transitions:null},Je(xa,cn),cn|=a;else{if(!(a&1073741824))return t=p!==null?p.baseLanes|a:a,n.lanes=n.childLanes=1073741824,n.memoizedState={baseLanes:t,cachePool:null,transitions:null},n.updateQueue=null,Je(xa,cn),cn|=t,null;n.memoizedState={baseLanes:0,cachePool:null,transitions:null},s=p!==null?p.baseLanes:a,Je(xa,cn),cn|=s}else p!==null?(s=p.baseLanes|a,n.memoizedState=null):s=a,Je(xa,cn),cn|=s;return Vt(t,n,c,a),n.child}function Tm(t,n){var a=n.ref;(t===null&&a!==null||t!==null&&t.ref!==a)&&(n.flags|=512,n.flags|=2097152)}function Cc(t,n,a,s,c){var p=Xt(a)?To:It.current;return p=ca(n,p),ga(n,c),a=mc(t,n,a,s,p,c),s=hc(),t!==null&&!Ht?(n.updateQueue=t.updateQueue,n.flags&=-2053,t.lanes&=~c,Or(t,n,c)):(nt&&s&&Hu(n),n.flags|=1,Vt(t,n,a,c),n.child)}function Nm(t,n,a,s,c){if(Xt(a)){var p=!0;Ol(n)}else p=!1;if(ga(n,c),n.stateNode===null)Jl(t,n),gm(n,a,s),wc(n,a,s,c),s=!0;else if(t===null){var w=n.stateNode,O=n.memoizedProps;w.props=O;var D=w.context,V=a.contextType;typeof V=="object"&&V!==null?V=$n(V):(V=Xt(a)?To:It.current,V=ca(n,V));var ne=a.getDerivedStateFromProps,oe=typeof ne=="function"||typeof w.getSnapshotBeforeUpdate=="function";oe||typeof w.UNSAFE_componentWillReceiveProps!="function"&&typeof w.componentWillReceiveProps!="function"||(O!==s||D!==V)&&ym(n,w,s,V),oo=!1;var te=n.memoizedState;w.state=te,Wl(n,s,w,c),D=n.memoizedState,O!==s||te!==D||Yt.current||oo?(typeof ne=="function"&&(xc(n,a,ne,s),D=n.memoizedState),(O=oo||hm(n,a,O,s,te,D,V))?(oe||typeof w.UNSAFE_componentWillMount!="function"&&typeof w.componentWillMount!="function"||(typeof w.componentWillMount=="function"&&w.componentWillMount(),typeof w.UNSAFE_componentWillMount=="function"&&w.UNSAFE_componentWillMount()),typeof w.componentDidMount=="function"&&(n.flags|=4194308)):(typeof w.componentDidMount=="function"&&(n.flags|=4194308),n.memoizedProps=s,n.memoizedState=D),w.props=s,w.state=D,w.context=V,s=O):(typeof w.componentDidMount=="function"&&(n.flags|=4194308),s=!1)}else{w=n.stateNode,Uf(t,n),O=n.memoizedProps,V=n.type===n.elementType?O:Un(n.type,O),w.props=V,oe=n.pendingProps,te=w.context,D=a.contextType,typeof D=="object"&&D!==null?D=$n(D):(D=Xt(a)?To:It.current,D=ca(n,D));var se=a.getDerivedStateFromProps;(ne=typeof se=="function"||typeof w.getSnapshotBeforeUpdate=="function")||typeof w.UNSAFE_componentWillReceiveProps!="function"&&typeof w.componentWillReceiveProps!="function"||(O!==oe||te!==D)&&ym(n,w,s,D),oo=!1,te=n.memoizedState,w.state=te,Wl(n,s,w,c);var me=n.memoizedState;O!==oe||te!==me||Yt.current||oo?(typeof se=="function"&&(xc(n,a,se,s),me=n.memoizedState),(V=oo||hm(n,a,V,s,te,me,D)||!1)?(ne||typeof w.UNSAFE_componentWillUpdate!="function"&&typeof w.componentWillUpdate!="function"||(typeof w.componentWillUpdate=="function"&&w.componentWillUpdate(s,me,D),typeof w.UNSAFE_componentWillUpdate=="function"&&w.UNSAFE_componentWillUpdate(s,me,D)),typeof w.componentDidUpdate=="function"&&(n.flags|=4),typeof w.getSnapshotBeforeUpdate=="function"&&(n.flags|=1024)):(typeof w.componentDidUpdate!="function"||O===t.memoizedProps&&te===t.memoizedState||(n.flags|=4),typeof w.getSnapshotBeforeUpdate!="function"||O===t.memoizedProps&&te===t.memoizedState||(n.flags|=1024),n.memoizedProps=s,n.memoizedState=me),w.props=s,w.state=me,w.context=D,s=V):(typeof w.componentDidUpdate!="function"||O===t.memoizedProps&&te===t.memoizedState||(n.flags|=4),typeof w.getSnapshotBeforeUpdate!="function"||O===t.memoizedProps&&te===t.memoizedState||(n.flags|=1024),s=!1)}return $c(t,n,a,s,p,c)}function $c(t,n,a,s,c,p){Tm(t,n);var w=(n.flags&128)!==0;if(!s&&!w)return c&&Mf(n,a,!1),Or(t,n,p);s=n.stateNode,Xv.current=n;var O=w&&typeof a.getDerivedStateFromError!="function"?null:s.render();return n.flags|=1,t!==null&&w?(n.child=ma(n,t.child,null,p),n.child=ma(n,null,O,p)):Vt(t,n,O,p),n.memoizedState=s.state,c&&Mf(n,a,!0),n.child}function Am(t){var n=t.stateNode;n.pendingContext?_f(t,n.pendingContext,n.pendingContext!==n.context):n.context&&_f(t,n.context,!1),sc(t,n.containerInfo)}function Pm(t,n,a,s,c){return fa(),tc(c),n.flags|=256,Vt(t,n,a,s),n.child}var Ec={dehydrated:null,treeContext:null,retryLane:0};function Tc(t){return{baseLanes:t,cachePool:null,transitions:null}}function _m(t,n,a){var s=n.pendingProps,c=ot.current,p=!1,w=(n.flags&128)!==0,O;if((O=w)||(O=t!==null&&t.memoizedState===null?!1:(c&2)!==0),O?(p=!0,n.flags&=-129):(t===null||t.memoizedState!==null)&&(c|=1),Je(ot,c&1),t===null)return ec(n),t=n.memoizedState,t!==null&&(t=t.dehydrated,t!==null)?(n.mode&1?t.data==="$!"?n.lanes=8:n.lanes=1073741824:n.lanes=1,null):(w=s.children,t=s.fallback,p?(s=n.mode,p=n.child,w={mode:"hidden",children:w},!(s&1)&&p!==null?(p.childLanes=0,p.pendingProps=w):p=cs(w,s,0,null),t=zo(t,s,a,null),p.return=n,t.return=n,p.sibling=t,n.child=p,n.child.memoizedState=Tc(a),n.memoizedState=Ec,t):Nc(n,w));if(c=t.memoizedState,c!==null&&(O=c.dehydrated,O!==null))return Hv(t,n,w,s,O,c,a);if(p){p=s.fallback,w=n.mode,c=t.child,O=c.sibling;var D={mode:"hidden",children:s.children};return!(w&1)&&n.child!==c?(s=n.child,s.childLanes=0,s.pendingProps=D,n.deletions=null):(s=po(c,D),s.subtreeFlags=c.subtreeFlags&14680064),O!==null?p=po(O,p):(p=zo(p,w,a,null),p.flags|=2),p.return=n,s.return=n,s.sibling=p,n.child=s,s=p,p=n.child,w=t.child.memoizedState,w=w===null?Tc(a):{baseLanes:w.baseLanes|a,cachePool:null,transitions:w.transitions},p.memoizedState=w,p.childLanes=t.childLanes&~a,n.memoizedState=Ec,s}return p=t.child,t=p.sibling,s=po(p,{mode:"visible",children:s.children}),!(n.mode&1)&&(s.lanes=a),s.return=n,s.sibling=null,t!==null&&(a=n.deletions,a===null?(n.deletions=[t],n.flags|=16):a.push(t)),n.child=s,n.memoizedState=null,s}function Nc(t,n){return n=cs({mode:"visible",children:n},t.mode,0,null),n.return=t,t.child=n}function Hl(t,n,a,s){return s!==null&&tc(s),ma(n,t.child,null,a),t=Nc(n,n.pendingProps.children),t.flags|=2,n.memoizedState=null,t}function Hv(t,n,a,s,c,p,w){if(a)return n.flags&256?(n.flags&=-257,s=Sc(Error(o(422))),Hl(t,n,w,s)):n.memoizedState!==null?(n.child=t.child,n.flags|=128,null):(p=s.fallback,c=n.mode,s=cs({mode:"visible",children:s.children},c,0,null),p=zo(p,c,w,null),p.flags|=2,s.return=n,p.return=n,s.sibling=p,n.child=s,n.mode&1&&ma(n,t.child,null,w),n.child.memoizedState=Tc(w),n.memoizedState=Ec,p);if(!(n.mode&1))return Hl(t,n,w,null);if(c.data==="$!"){if(s=c.nextSibling&&c.nextSibling.dataset,s)var O=s.dgst;return s=O,p=Error(o(419)),s=Sc(p,s,void 0),Hl(t,n,w,s)}if(O=(w&t.childLanes)!==0,Ht||O){if(s=St,s!==null){switch(w&-w){case 4:c=2;break;case 16:c=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:c=32;break;case 536870912:c=268435456;break;default:c=0}c=c&(s.suspendedLanes|w)?0:c,c!==0&&c!==p.retryLane&&(p.retryLane=c,Rr(t,c),Kn(s,t,c,-1))}return Wc(),s=Sc(Error(o(421))),Hl(t,n,w,s)}return c.data==="$?"?(n.flags|=128,n.child=t.child,n=cb.bind(null,t),c._reactRetry=n,null):(t=p.treeContext,un=eo(c.nextSibling),sn=n,nt=!0,Wn=null,t!==null&&(kn[Cn++]=Pr,kn[Cn++]=_r,kn[Cn++]=No,Pr=t.id,_r=t.overflow,No=n),n=Nc(n,s.children),n.flags|=4096,n)}function Rm(t,n,a){t.lanes|=n;var s=t.alternate;s!==null&&(s.lanes|=n),ac(t.return,n,a)}function Ac(t,n,a,s,c){var p=t.memoizedState;p===null?t.memoizedState={isBackwards:n,rendering:null,renderingStartTime:0,last:s,tail:a,tailMode:c}:(p.isBackwards=n,p.rendering=null,p.renderingStartTime=0,p.last=s,p.tail=a,p.tailMode=c)}function Mm(t,n,a){var s=n.pendingProps,c=s.revealOrder,p=s.tail;if(Vt(t,n,s.children,a),s=ot.current,s&2)s=s&1|2,n.flags|=128;else{if(t!==null&&t.flags&128)e:for(t=n.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Rm(t,a,n);else if(t.tag===19)Rm(t,a,n);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===n)break e;for(;t.sibling===null;){if(t.return===null||t.return===n)break e;t=t.return}t.sibling.return=t.return,t=t.sibling}s&=1}if(Je(ot,s),!(n.mode&1))n.memoizedState=null;else switch(c){case"forwards":for(a=n.child,c=null;a!==null;)t=a.alternate,t!==null&&Ul(t)===null&&(c=a),a=a.sibling;a=c,a===null?(c=n.child,n.child=null):(c=a.sibling,a.sibling=null),Ac(n,!1,c,a,p);break;case"backwards":for(a=null,c=n.child,n.child=null;c!==null;){if(t=c.alternate,t!==null&&Ul(t)===null){n.child=c;break}t=c.sibling,c.sibling=a,a=c,c=t}Ac(n,!0,a,null,p);break;case"together":Ac(n,!1,null,null,void 0);break;default:n.memoizedState=null}return n.child}function Jl(t,n){!(n.mode&1)&&t!==null&&(t.alternate=null,n.alternate=null,n.flags|=2)}function Or(t,n,a){if(t!==null&&(n.dependencies=t.dependencies),Mo|=n.lanes,!(a&n.childLanes))return null;if(t!==null&&n.child!==t.child)throw Error(o(153));if(n.child!==null){for(t=n.child,a=po(t,t.pendingProps),n.child=a,a.return=n;t.sibling!==null;)t=t.sibling,a=a.sibling=po(t,t.pendingProps),a.return=n;a.sibling=null}return n.child}function Jv(t,n,a){switch(n.tag){case 3:Am(n),fa();break;case 5:Kf(n);break;case 1:Xt(n.type)&&Ol(n);break;case 4:sc(n,n.stateNode.containerInfo);break;case 10:var s=n.type._context,c=n.memoizedProps.value;Je(Bl,s._currentValue),s._currentValue=c;break;case 13:if(s=n.memoizedState,s!==null)return s.dehydrated!==null?(Je(ot,ot.current&1),n.flags|=128,null):a&n.child.childLanes?_m(t,n,a):(Je(ot,ot.current&1),t=Or(t,n,a),t!==null?t.sibling:null);Je(ot,ot.current&1);break;case 19:if(s=(a&n.childLanes)!==0,t.flags&128){if(s)return Mm(t,n,a);n.flags|=128}if(c=n.memoizedState,c!==null&&(c.rendering=null,c.tail=null,c.lastEffect=null),Je(ot,ot.current),s)break;return null;case 22:case 23:return n.lanes=0,Em(t,n,a)}return Or(t,n,a)}var Om,Pc,jm,Im;Om=function(t,n){for(var a=n.child;a!==null;){if(a.tag===5||a.tag===6)t.appendChild(a.stateNode);else if(a.tag!==4&&a.child!==null){a.child.return=a,a=a.child;continue}if(a===n)break;for(;a.sibling===null;){if(a.return===null||a.return===n)return;a=a.return}a.sibling.return=a.return,a=a.sibling}},Pc=function(){},jm=function(t,n,a,s){var c=t.memoizedProps;if(c!==s){t=n.stateNode,_o(ir.current);var p=null;switch(a){case"input":c=dt(t,c),s=dt(t,s),p=[];break;case"select":c=K({},c,{value:void 0}),s=K({},s,{value:void 0}),p=[];break;case"textarea":c=In(t,c),s=In(t,s),p=[];break;default:typeof c.onClick!="function"&&typeof s.onClick=="function"&&(t.onclick=_l)}Ut(a,s);var w;a=null;for(V in c)if(!s.hasOwnProperty(V)&&c.hasOwnProperty(V)&&c[V]!=null)if(V==="style"){var O=c[V];for(w in O)O.hasOwnProperty(w)&&(a||(a={}),a[w]="")}else V!=="dangerouslySetInnerHTML"&&V!=="children"&&V!=="suppressContentEditableWarning"&&V!=="suppressHydrationWarning"&&V!=="autoFocus"&&(l.hasOwnProperty(V)?p||(p=[]):(p=p||[]).push(V,null));for(V in s){var D=s[V];if(O=c!=null?c[V]:void 0,s.hasOwnProperty(V)&&D!==O&&(D!=null||O!=null))if(V==="style")if(O){for(w in O)!O.hasOwnProperty(w)||D&&D.hasOwnProperty(w)||(a||(a={}),a[w]="");for(w in D)D.hasOwnProperty(w)&&O[w]!==D[w]&&(a||(a={}),a[w]=D[w])}else a||(p||(p=[]),p.push(V,a)),a=D;else V==="dangerouslySetInnerHTML"?(D=D?D.__html:void 0,O=O?O.__html:void 0,D!=null&&O!==D&&(p=p||[]).push(V,D)):V==="children"?typeof D!="string"&&typeof D!="number"||(p=p||[]).push(V,""+D):V!=="suppressContentEditableWarning"&&V!=="suppressHydrationWarning"&&(l.hasOwnProperty(V)?(D!=null&&V==="onScroll"&&Ze("scroll",t),p||O===D||(p=[])):(p=p||[]).push(V,D))}a&&(p=p||[]).push("style",a);var V=p;(n.updateQueue=V)&&(n.flags|=4)}},Im=function(t,n,a,s){a!==s&&(n.flags|=4)};function bi(t,n){if(!nt)switch(t.tailMode){case"hidden":n=t.tail;for(var a=null;n!==null;)n.alternate!==null&&(a=n),n=n.sibling;a===null?t.tail=null:a.sibling=null;break;case"collapsed":a=t.tail;for(var s=null;a!==null;)a.alternate!==null&&(s=a),a=a.sibling;s===null?n||t.tail===null?t.tail=null:t.tail.sibling=null:s.sibling=null}}function Dt(t){var n=t.alternate!==null&&t.alternate.child===t.child,a=0,s=0;if(n)for(var c=t.child;c!==null;)a|=c.lanes|c.childLanes,s|=c.subtreeFlags&14680064,s|=c.flags&14680064,c.return=t,c=c.sibling;else for(c=t.child;c!==null;)a|=c.lanes|c.childLanes,s|=c.subtreeFlags,s|=c.flags,c.return=t,c=c.sibling;return t.subtreeFlags|=s,t.childLanes=a,n}function Zv(t,n,a){var s=n.pendingProps;switch(Ju(n),n.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Dt(n),null;case 1:return Xt(n.type)&&Ml(),Dt(n),null;case 3:return s=n.stateNode,ya(),et(Yt),et(It),dc(),s.pendingContext&&(s.context=s.pendingContext,s.pendingContext=null),(t===null||t.child===null)&&(Dl(n)?n.flags|=4:t===null||t.memoizedState.isDehydrated&&!(n.flags&256)||(n.flags|=1024,Wn!==null&&(Bc(Wn),Wn=null))),Pc(t,n),Dt(n),null;case 5:uc(n);var c=_o(mi.current);if(a=n.type,t!==null&&n.stateNode!=null)jm(t,n,a,s,c),t.ref!==n.ref&&(n.flags|=512,n.flags|=2097152);else{if(!s){if(n.stateNode===null)throw Error(o(166));return Dt(n),null}if(t=_o(ir.current),Dl(n)){s=n.stateNode,a=n.type;var p=n.memoizedProps;switch(s[ar]=n,s[ui]=p,t=(n.mode&1)!==0,a){case"dialog":Ze("cancel",s),Ze("close",s);break;case"iframe":case"object":case"embed":Ze("load",s);break;case"video":case"audio":for(c=0;c<ii.length;c++)Ze(ii[c],s);break;case"source":Ze("error",s);break;case"img":case"image":case"link":Ze("error",s),Ze("load",s);break;case"details":Ze("toggle",s);break;case"input":Ie(s,p),Ze("invalid",s);break;case"select":s._wrapperState={wasMultiple:!!p.multiple},Ze("invalid",s);break;case"textarea":gn(s,p),Ze("invalid",s)}Ut(a,p),c=null;for(var w in p)if(p.hasOwnProperty(w)){var O=p[w];w==="children"?typeof O=="string"?s.textContent!==O&&(p.suppressHydrationWarning!==!0&&Pl(s.textContent,O,t),c=["children",O]):typeof O=="number"&&s.textContent!==""+O&&(p.suppressHydrationWarning!==!0&&Pl(s.textContent,O,t),c=["children",""+O]):l.hasOwnProperty(w)&&O!=null&&w==="onScroll"&&Ze("scroll",s)}switch(a){case"input":Ee(s),Wt(s,p,!0);break;case"textarea":Ee(s),br(s);break;case"select":case"option":break;default:typeof p.onClick=="function"&&(s.onclick=_l)}s=c,n.updateQueue=s,s!==null&&(n.flags|=4)}else{w=c.nodeType===9?c:c.ownerDocument,t==="http://www.w3.org/1999/xhtml"&&(t=zn(a)),t==="http://www.w3.org/1999/xhtml"?a==="script"?(t=w.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild)):typeof s.is=="string"?t=w.createElement(a,{is:s.is}):(t=w.createElement(a),a==="select"&&(w=t,s.multiple?w.multiple=!0:s.size&&(w.size=s.size))):t=w.createElementNS(t,a),t[ar]=n,t[ui]=s,Om(t,n,!1,!1),n.stateNode=t;e:{switch(w=So(a,s),a){case"dialog":Ze("cancel",t),Ze("close",t),c=s;break;case"iframe":case"object":case"embed":Ze("load",t),c=s;break;case"video":case"audio":for(c=0;c<ii.length;c++)Ze(ii[c],t);c=s;break;case"source":Ze("error",t),c=s;break;case"img":case"image":case"link":Ze("error",t),Ze("load",t),c=s;break;case"details":Ze("toggle",t),c=s;break;case"input":Ie(t,s),c=dt(t,s),Ze("invalid",t);break;case"option":c=s;break;case"select":t._wrapperState={wasMultiple:!!s.multiple},c=K({},s,{value:void 0}),Ze("invalid",t);break;case"textarea":gn(t,s),c=In(t,s),Ze("invalid",t);break;default:c=s}Ut(a,c),O=c;for(p in O)if(O.hasOwnProperty(p)){var D=O[p];p==="style"?Jo(t,D):p==="dangerouslySetInnerHTML"?(D=D?D.__html:void 0,D!=null&&jt(t,D)):p==="children"?typeof D=="string"?(a!=="textarea"||D!=="")&&Ue(t,D):typeof D=="number"&&Ue(t,""+D):p!=="suppressContentEditableWarning"&&p!=="suppressHydrationWarning"&&p!=="autoFocus"&&(l.hasOwnProperty(p)?D!=null&&p==="onScroll"&&Ze("scroll",t):D!=null&&x(t,p,D,w))}switch(a){case"input":Ee(t),Wt(t,s,!1);break;case"textarea":Ee(t),br(t);break;case"option":s.value!=null&&t.setAttribute("value",""+Se(s.value));break;case"select":t.multiple=!!s.multiple,p=s.value,p!=null?ye(t,!!s.multiple,p,!1):s.defaultValue!=null&&ye(t,!!s.multiple,s.defaultValue,!0);break;default:typeof c.onClick=="function"&&(t.onclick=_l)}switch(a){case"button":case"input":case"select":case"textarea":s=!!s.autoFocus;break e;case"img":s=!0;break e;default:s=!1}}s&&(n.flags|=4)}n.ref!==null&&(n.flags|=512,n.flags|=2097152)}return Dt(n),null;case 6:if(t&&n.stateNode!=null)Im(t,n,t.memoizedProps,s);else{if(typeof s!="string"&&n.stateNode===null)throw Error(o(166));if(a=_o(mi.current),_o(ir.current),Dl(n)){if(s=n.stateNode,a=n.memoizedProps,s[ar]=n,(p=s.nodeValue!==a)&&(t=sn,t!==null))switch(t.tag){case 3:Pl(s.nodeValue,a,(t.mode&1)!==0);break;case 5:t.memoizedProps.suppressHydrationWarning!==!0&&Pl(s.nodeValue,a,(t.mode&1)!==0)}p&&(n.flags|=4)}else s=(a.nodeType===9?a:a.ownerDocument).createTextNode(s),s[ar]=n,n.stateNode=s}return Dt(n),null;case 13:if(et(ot),s=n.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(nt&&un!==null&&n.mode&1&&!(n.flags&128))Ff(),fa(),n.flags|=98560,p=!1;else if(p=Dl(n),s!==null&&s.dehydrated!==null){if(t===null){if(!p)throw Error(o(318));if(p=n.memoizedState,p=p!==null?p.dehydrated:null,!p)throw Error(o(317));p[ar]=n}else fa(),!(n.flags&128)&&(n.memoizedState=null),n.flags|=4;Dt(n),p=!1}else Wn!==null&&(Bc(Wn),Wn=null),p=!0;if(!p)return n.flags&65536?n:null}return n.flags&128?(n.lanes=a,n):(s=s!==null,s!==(t!==null&&t.memoizedState!==null)&&s&&(n.child.flags|=8192,n.mode&1&&(t===null||ot.current&1?xt===0&&(xt=3):Wc())),n.updateQueue!==null&&(n.flags|=4),Dt(n),null);case 4:return ya(),Pc(t,n),t===null&&li(n.stateNode.containerInfo),Dt(n),null;case 10:return oc(n.type._context),Dt(n),null;case 17:return Xt(n.type)&&Ml(),Dt(n),null;case 19:if(et(ot),p=n.memoizedState,p===null)return Dt(n),null;if(s=(n.flags&128)!==0,w=p.rendering,w===null)if(s)bi(p,!1);else{if(xt!==0||t!==null&&t.flags&128)for(t=n.child;t!==null;){if(w=Ul(t),w!==null){for(n.flags|=128,bi(p,!1),s=w.updateQueue,s!==null&&(n.updateQueue=s,n.flags|=4),n.subtreeFlags=0,s=a,a=n.child;a!==null;)p=a,t=s,p.flags&=14680066,w=p.alternate,w===null?(p.childLanes=0,p.lanes=t,p.child=null,p.subtreeFlags=0,p.memoizedProps=null,p.memoizedState=null,p.updateQueue=null,p.dependencies=null,p.stateNode=null):(p.childLanes=w.childLanes,p.lanes=w.lanes,p.child=w.child,p.subtreeFlags=0,p.deletions=null,p.memoizedProps=w.memoizedProps,p.memoizedState=w.memoizedState,p.updateQueue=w.updateQueue,p.type=w.type,t=w.dependencies,p.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),a=a.sibling;return Je(ot,ot.current&1|2),n.child}t=t.sibling}p.tail!==null&&ke()>wa&&(n.flags|=128,s=!0,bi(p,!1),n.lanes=4194304)}else{if(!s)if(t=Ul(w),t!==null){if(n.flags|=128,s=!0,a=t.updateQueue,a!==null&&(n.updateQueue=a,n.flags|=4),bi(p,!0),p.tail===null&&p.tailMode==="hidden"&&!w.alternate&&!nt)return Dt(n),null}else 2*ke()-p.renderingStartTime>wa&&a!==1073741824&&(n.flags|=128,s=!0,bi(p,!1),n.lanes=4194304);p.isBackwards?(w.sibling=n.child,n.child=w):(a=p.last,a!==null?a.sibling=w:n.child=w,p.last=w)}return p.tail!==null?(n=p.tail,p.rendering=n,p.tail=n.sibling,p.renderingStartTime=ke(),n.sibling=null,a=ot.current,Je(ot,s?a&1|2:a&1),n):(Dt(n),null);case 22:case 23:return qc(),s=n.memoizedState!==null,t!==null&&t.memoizedState!==null!==s&&(n.flags|=8192),s&&n.mode&1?cn&1073741824&&(Dt(n),n.subtreeFlags&6&&(n.flags|=8192)):Dt(n),null;case 24:return null;case 25:return null}throw Error(o(156,n.tag))}function eb(t,n){switch(Ju(n),n.tag){case 1:return Xt(n.type)&&Ml(),t=n.flags,t&65536?(n.flags=t&-65537|128,n):null;case 3:return ya(),et(Yt),et(It),dc(),t=n.flags,t&65536&&!(t&128)?(n.flags=t&-65537|128,n):null;case 5:return uc(n),null;case 13:if(et(ot),t=n.memoizedState,t!==null&&t.dehydrated!==null){if(n.alternate===null)throw Error(o(340));fa()}return t=n.flags,t&65536?(n.flags=t&-65537|128,n):null;case 19:return et(ot),null;case 4:return ya(),null;case 10:return oc(n.type._context),null;case 22:case 23:return qc(),null;case 24:return null;default:return null}}var Zl=!1,Ft=!1,tb=typeof WeakSet=="function"?WeakSet:Set,pe=null;function ba(t,n){var a=t.ref;if(a!==null)if(typeof a=="function")try{a(null)}catch(s){st(t,n,s)}else a.current=null}function zm(t,n,a){try{a()}catch(s){st(t,n,s)}}var Dm=!1;function nb(t,n){if(Wu=bl,t=hf(),ju(t)){if("selectionStart"in t)var a={start:t.selectionStart,end:t.selectionEnd};else e:{a=(a=t.ownerDocument)&&a.defaultView||window;var s=a.getSelection&&a.getSelection();if(s&&s.rangeCount!==0){a=s.anchorNode;var c=s.anchorOffset,p=s.focusNode;s=s.focusOffset;try{a.nodeType,p.nodeType}catch{a=null;break e}var w=0,O=-1,D=-1,V=0,ne=0,oe=t,te=null;t:for(;;){for(var se;oe!==a||c!==0&&oe.nodeType!==3||(O=w+c),oe!==p||s!==0&&oe.nodeType!==3||(D=w+s),oe.nodeType===3&&(w+=oe.nodeValue.length),(se=oe.firstChild)!==null;)te=oe,oe=se;for(;;){if(oe===t)break t;if(te===a&&++V===c&&(O=w),te===p&&++ne===s&&(D=w),(se=oe.nextSibling)!==null)break;oe=te,te=oe.parentNode}oe=se}a=O===-1||D===-1?null:{start:O,end:D}}else a=null}a=a||{start:0,end:0}}else a=null;for(Uu={focusedElem:t,selectionRange:a},bl=!1,pe=n;pe!==null;)if(n=pe,t=n.child,(n.subtreeFlags&1028)!==0&&t!==null)t.return=n,pe=t;else for(;pe!==null;){n=pe;try{var me=n.alternate;if(n.flags&1024)switch(n.tag){case 0:case 11:case 15:break;case 1:if(me!==null){var ge=me.memoizedProps,ft=me.memoizedState,U=n.stateNode,B=U.getSnapshotBeforeUpdate(n.elementType===n.type?ge:Un(n.type,ge),ft);U.__reactInternalSnapshotBeforeUpdate=B}break;case 3:var Q=n.stateNode.containerInfo;Q.nodeType===1?Q.textContent="":Q.nodeType===9&&Q.documentElement&&Q.removeChild(Q.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(o(163))}}catch(ae){st(n,n.return,ae)}if(t=n.sibling,t!==null){t.return=n.return,pe=t;break}pe=n.return}return me=Dm,Dm=!1,me}function xi(t,n,a){var s=n.updateQueue;if(s=s!==null?s.lastEffect:null,s!==null){var c=s=s.next;do{if((c.tag&t)===t){var p=c.destroy;c.destroy=void 0,p!==void 0&&zm(n,a,p)}c=c.next}while(c!==s)}}function es(t,n){if(n=n.updateQueue,n=n!==null?n.lastEffect:null,n!==null){var a=n=n.next;do{if((a.tag&t)===t){var s=a.create;a.destroy=s()}a=a.next}while(a!==n)}}function _c(t){var n=t.ref;if(n!==null){var a=t.stateNode;switch(t.tag){case 5:t=a;break;default:t=a}typeof n=="function"?n(t):n.current=t}}function Fm(t){var n=t.alternate;n!==null&&(t.alternate=null,Fm(n)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(n=t.stateNode,n!==null&&(delete n[ar],delete n[ui],delete n[Gu],delete n[Dv],delete n[Fv])),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}function Bm(t){return t.tag===5||t.tag===3||t.tag===4}function Lm(t){e:for(;;){for(;t.sibling===null;){if(t.return===null||Bm(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.flags&2||t.child===null||t.tag===4)continue e;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Rc(t,n,a){var s=t.tag;if(s===5||s===6)t=t.stateNode,n?a.nodeType===8?a.parentNode.insertBefore(t,n):a.insertBefore(t,n):(a.nodeType===8?(n=a.parentNode,n.insertBefore(t,a)):(n=a,n.appendChild(t)),a=a._reactRootContainer,a!=null||n.onclick!==null||(n.onclick=_l));else if(s!==4&&(t=t.child,t!==null))for(Rc(t,n,a),t=t.sibling;t!==null;)Rc(t,n,a),t=t.sibling}function Mc(t,n,a){var s=t.tag;if(s===5||s===6)t=t.stateNode,n?a.insertBefore(t,n):a.appendChild(t);else if(s!==4&&(t=t.child,t!==null))for(Mc(t,n,a),t=t.sibling;t!==null;)Mc(t,n,a),t=t.sibling}var Tt=null,Qn=!1;function io(t,n,a){for(a=a.child;a!==null;)qm(t,n,a),a=a.sibling}function qm(t,n,a){if(or&&typeof or.onCommitFiberUnmount=="function")try{or.onCommitFiberUnmount(fl,a)}catch{}switch(a.tag){case 5:Ft||ba(a,n);case 6:var s=Tt,c=Qn;Tt=null,io(t,n,a),Tt=s,Qn=c,Tt!==null&&(Qn?(t=Tt,a=a.stateNode,t.nodeType===8?t.parentNode.removeChild(a):t.removeChild(a)):Tt.removeChild(a.stateNode));break;case 18:Tt!==null&&(Qn?(t=Tt,a=a.stateNode,t.nodeType===8?Ku(t.parentNode,a):t.nodeType===1&&Ku(t,a),Ja(t)):Ku(Tt,a.stateNode));break;case 4:s=Tt,c=Qn,Tt=a.stateNode.containerInfo,Qn=!0,io(t,n,a),Tt=s,Qn=c;break;case 0:case 11:case 14:case 15:if(!Ft&&(s=a.updateQueue,s!==null&&(s=s.lastEffect,s!==null))){c=s=s.next;do{var p=c,w=p.destroy;p=p.tag,w!==void 0&&(p&2||p&4)&&zm(a,n,w),c=c.next}while(c!==s)}io(t,n,a);break;case 1:if(!Ft&&(ba(a,n),s=a.stateNode,typeof s.componentWillUnmount=="function"))try{s.props=a.memoizedProps,s.state=a.memoizedState,s.componentWillUnmount()}catch(O){st(a,n,O)}io(t,n,a);break;case 21:io(t,n,a);break;case 22:a.mode&1?(Ft=(s=Ft)||a.memoizedState!==null,io(t,n,a),Ft=s):io(t,n,a);break;default:io(t,n,a)}}function Wm(t){var n=t.updateQueue;if(n!==null){t.updateQueue=null;var a=t.stateNode;a===null&&(a=t.stateNode=new tb),n.forEach(function(s){var c=db.bind(null,t,s);a.has(s)||(a.add(s),s.then(c,c))})}}function Vn(t,n){var a=n.deletions;if(a!==null)for(var s=0;s<a.length;s++){var c=a[s];try{var p=t,w=n,O=w;e:for(;O!==null;){switch(O.tag){case 5:Tt=O.stateNode,Qn=!1;break e;case 3:Tt=O.stateNode.containerInfo,Qn=!0;break e;case 4:Tt=O.stateNode.containerInfo,Qn=!0;break e}O=O.return}if(Tt===null)throw Error(o(160));qm(p,w,c),Tt=null,Qn=!1;var D=c.alternate;D!==null&&(D.return=null),c.return=null}catch(V){st(c,n,V)}}if(n.subtreeFlags&12854)for(n=n.child;n!==null;)Um(n,t),n=n.sibling}function Um(t,n){var a=t.alternate,s=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:if(Vn(n,t),sr(t),s&4){try{xi(3,t,t.return),es(3,t)}catch(ge){st(t,t.return,ge)}try{xi(5,t,t.return)}catch(ge){st(t,t.return,ge)}}break;case 1:Vn(n,t),sr(t),s&512&&a!==null&&ba(a,a.return);break;case 5:if(Vn(n,t),sr(t),s&512&&a!==null&&ba(a,a.return),t.flags&32){var c=t.stateNode;try{Ue(c,"")}catch(ge){st(t,t.return,ge)}}if(s&4&&(c=t.stateNode,c!=null)){var p=t.memoizedProps,w=a!==null?a.memoizedProps:p,O=t.type,D=t.updateQueue;if(t.updateQueue=null,D!==null)try{O==="input"&&p.type==="radio"&&p.name!=null&&tt(c,p),So(O,w);var V=So(O,p);for(w=0;w<D.length;w+=2){var ne=D[w],oe=D[w+1];ne==="style"?Jo(c,oe):ne==="dangerouslySetInnerHTML"?jt(c,oe):ne==="children"?Ue(c,oe):x(c,ne,oe,V)}switch(O){case"input":de(c,p);break;case"textarea":Ot(c,p);break;case"select":var te=c._wrapperState.wasMultiple;c._wrapperState.wasMultiple=!!p.multiple;var se=p.value;se!=null?ye(c,!!p.multiple,se,!1):te!==!!p.multiple&&(p.defaultValue!=null?ye(c,!!p.multiple,p.defaultValue,!0):ye(c,!!p.multiple,p.multiple?[]:"",!1))}c[ui]=p}catch(ge){st(t,t.return,ge)}}break;case 6:if(Vn(n,t),sr(t),s&4){if(t.stateNode===null)throw Error(o(162));c=t.stateNode,p=t.memoizedProps;try{c.nodeValue=p}catch(ge){st(t,t.return,ge)}}break;case 3:if(Vn(n,t),sr(t),s&4&&a!==null&&a.memoizedState.isDehydrated)try{Ja(n.containerInfo)}catch(ge){st(t,t.return,ge)}break;case 4:Vn(n,t),sr(t);break;case 13:Vn(n,t),sr(t),c=t.child,c.flags&8192&&(p=c.memoizedState!==null,c.stateNode.isHidden=p,!p||c.alternate!==null&&c.alternate.memoizedState!==null||(Ic=ke())),s&4&&Wm(t);break;case 22:if(ne=a!==null&&a.memoizedState!==null,t.mode&1?(Ft=(V=Ft)||ne,Vn(n,t),Ft=V):Vn(n,t),sr(t),s&8192){if(V=t.memoizedState!==null,(t.stateNode.isHidden=V)&&!ne&&t.mode&1)for(pe=t,ne=t.child;ne!==null;){for(oe=pe=ne;pe!==null;){switch(te=pe,se=te.child,te.tag){case 0:case 11:case 14:case 15:xi(4,te,te.return);break;case 1:ba(te,te.return);var me=te.stateNode;if(typeof me.componentWillUnmount=="function"){s=te,a=te.return;try{n=s,me.props=n.memoizedProps,me.state=n.memoizedState,me.componentWillUnmount()}catch(ge){st(s,a,ge)}}break;case 5:ba(te,te.return);break;case 22:if(te.memoizedState!==null){Km(oe);continue}}se!==null?(se.return=te,pe=se):Km(oe)}ne=ne.sibling}e:for(ne=null,oe=t;;){if(oe.tag===5){if(ne===null){ne=oe;try{c=oe.stateNode,V?(p=c.style,typeof p.setProperty=="function"?p.setProperty("display","none","important"):p.display="none"):(O=oe.stateNode,D=oe.memoizedProps.style,w=D!=null&&D.hasOwnProperty("display")?D.display:null,O.style.display=xr("display",w))}catch(ge){st(t,t.return,ge)}}}else if(oe.tag===6){if(ne===null)try{oe.stateNode.nodeValue=V?"":oe.memoizedProps}catch(ge){st(t,t.return,ge)}}else if((oe.tag!==22&&oe.tag!==23||oe.memoizedState===null||oe===t)&&oe.child!==null){oe.child.return=oe,oe=oe.child;continue}if(oe===t)break e;for(;oe.sibling===null;){if(oe.return===null||oe.return===t)break e;ne===oe&&(ne=null),oe=oe.return}ne===oe&&(ne=null),oe.sibling.return=oe.return,oe=oe.sibling}}break;case 19:Vn(n,t),sr(t),s&4&&Wm(t);break;case 21:break;default:Vn(n,t),sr(t)}}function sr(t){var n=t.flags;if(n&2){try{e:{for(var a=t.return;a!==null;){if(Bm(a)){var s=a;break e}a=a.return}throw Error(o(160))}switch(s.tag){case 5:var c=s.stateNode;s.flags&32&&(Ue(c,""),s.flags&=-33);var p=Lm(t);Mc(t,p,c);break;case 3:case 4:var w=s.stateNode.containerInfo,O=Lm(t);Rc(t,O,w);break;default:throw Error(o(161))}}catch(D){st(t,t.return,D)}t.flags&=-3}n&4096&&(t.flags&=-4097)}function rb(t,n,a){pe=t,Qm(t)}function Qm(t,n,a){for(var s=(t.mode&1)!==0;pe!==null;){var c=pe,p=c.child;if(c.tag===22&&s){var w=c.memoizedState!==null||Zl;if(!w){var O=c.alternate,D=O!==null&&O.memoizedState!==null||Ft;O=Zl;var V=Ft;if(Zl=w,(Ft=D)&&!V)for(pe=c;pe!==null;)w=pe,D=w.child,w.tag===22&&w.memoizedState!==null?Gm(c):D!==null?(D.return=w,pe=D):Gm(c);for(;p!==null;)pe=p,Qm(p),p=p.sibling;pe=c,Zl=O,Ft=V}Vm(t)}else c.subtreeFlags&8772&&p!==null?(p.return=c,pe=p):Vm(t)}}function Vm(t){for(;pe!==null;){var n=pe;if(n.flags&8772){var a=n.alternate;try{if(n.flags&8772)switch(n.tag){case 0:case 11:case 15:Ft||es(5,n);break;case 1:var s=n.stateNode;if(n.flags&4&&!Ft)if(a===null)s.componentDidMount();else{var c=n.elementType===n.type?a.memoizedProps:Un(n.type,a.memoizedProps);s.componentDidUpdate(c,a.memoizedState,s.__reactInternalSnapshotBeforeUpdate)}var p=n.updateQueue;p!==null&&Vf(n,p,s);break;case 3:var w=n.updateQueue;if(w!==null){if(a=null,n.child!==null)switch(n.child.tag){case 5:a=n.child.stateNode;break;case 1:a=n.child.stateNode}Vf(n,w,a)}break;case 5:var O=n.stateNode;if(a===null&&n.flags&4){a=O;var D=n.memoizedProps;switch(n.type){case"button":case"input":case"select":case"textarea":D.autoFocus&&a.focus();break;case"img":D.src&&(a.src=D.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(n.memoizedState===null){var V=n.alternate;if(V!==null){var ne=V.memoizedState;if(ne!==null){var oe=ne.dehydrated;oe!==null&&Ja(oe)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(o(163))}Ft||n.flags&512&&_c(n)}catch(te){st(n,n.return,te)}}if(n===t){pe=null;break}if(a=n.sibling,a!==null){a.return=n.return,pe=a;break}pe=n.return}}function Km(t){for(;pe!==null;){var n=pe;if(n===t){pe=null;break}var a=n.sibling;if(a!==null){a.return=n.return,pe=a;break}pe=n.return}}function Gm(t){for(;pe!==null;){var n=pe;try{switch(n.tag){case 0:case 11:case 15:var a=n.return;try{es(4,n)}catch(D){st(n,a,D)}break;case 1:var s=n.stateNode;if(typeof s.componentDidMount=="function"){var c=n.return;try{s.componentDidMount()}catch(D){st(n,c,D)}}var p=n.return;try{_c(n)}catch(D){st(n,p,D)}break;case 5:var w=n.return;try{_c(n)}catch(D){st(n,w,D)}}}catch(D){st(n,n.return,D)}if(n===t){pe=null;break}var O=n.sibling;if(O!==null){O.return=n.return,pe=O;break}pe=n.return}}var ob=Math.ceil,ts=C.ReactCurrentDispatcher,Oc=C.ReactCurrentOwner,Tn=C.ReactCurrentBatchConfig,De=0,St=null,gt=null,Nt=0,cn=0,xa=to(0),xt=0,wi=null,Mo=0,ns=0,jc=0,Si=null,Jt=null,Ic=0,wa=1/0,jr=null,rs=!1,zc=null,lo=null,os=!1,so=null,as=0,ki=0,Dc=null,is=-1,ls=0;function Kt(){return De&6?ke():is!==-1?is:is=ke()}function uo(t){return t.mode&1?De&2&&Nt!==0?Nt&-Nt:Lv.transition!==null?(ls===0&&(ls=Fp()),ls):(t=Ve,t!==0||(t=window.event,t=t===void 0?16:Gp(t.type)),t):1}function Kn(t,n,a,s){if(50<ki)throw ki=0,Dc=null,Error(o(185));Ka(t,a,s),(!(De&2)||t!==St)&&(t===St&&(!(De&2)&&(ns|=a),xt===4&&co(t,Nt)),Zt(t,s),a===1&&De===0&&!(n.mode&1)&&(wa=ke()+500,jl&&ro()))}function Zt(t,n){var a=t.callbackNode;L0(t,n);var s=gl(t,t===St?Nt:0);if(s===0)a!==null&&pt(a),t.callbackNode=null,t.callbackPriority=0;else if(n=s&-s,t.callbackPriority!==n){if(a!=null&&pt(a),n===1)t.tag===0?Bv(Xm.bind(null,t)):Of(Xm.bind(null,t)),Iv(function(){!(De&6)&&ro()}),a=null;else{switch(Bp(s)){case 1:a=Sn;break;case 4:a=Kr;break;case 16:a=ea;break;case 536870912:a=Dp;break;default:a=ea}a=oh(a,Ym.bind(null,t))}t.callbackPriority=n,t.callbackNode=a}}function Ym(t,n){if(is=-1,ls=0,De&6)throw Error(o(327));var a=t.callbackNode;if(Sa()&&t.callbackNode!==a)return null;var s=gl(t,t===St?Nt:0);if(s===0)return null;if(s&30||s&t.expiredLanes||n)n=ss(t,s);else{n=s;var c=De;De|=2;var p=Jm();(St!==t||Nt!==n)&&(jr=null,wa=ke()+500,jo(t,n));do try{lb();break}catch(O){Hm(t,O)}while(!0);rc(),ts.current=p,De=c,gt!==null?n=0:(St=null,Nt=0,n=xt)}if(n!==0){if(n===2&&(c=bu(t),c!==0&&(s=c,n=Fc(t,c))),n===1)throw a=wi,jo(t,0),co(t,s),Zt(t,ke()),a;if(n===6)co(t,s);else{if(c=t.current.alternate,!(s&30)&&!ab(c)&&(n=ss(t,s),n===2&&(p=bu(t),p!==0&&(s=p,n=Fc(t,p))),n===1))throw a=wi,jo(t,0),co(t,s),Zt(t,ke()),a;switch(t.finishedWork=c,t.finishedLanes=s,n){case 0:case 1:throw Error(o(345));case 2:Io(t,Jt,jr);break;case 3:if(co(t,s),(s&130023424)===s&&(n=Ic+500-ke(),10<n)){if(gl(t,0)!==0)break;if(c=t.suspendedLanes,(c&s)!==s){Kt(),t.pingedLanes|=t.suspendedLanes&c;break}t.timeoutHandle=Vu(Io.bind(null,t,Jt,jr),n);break}Io(t,Jt,jr);break;case 4:if(co(t,s),(s&4194240)===s)break;for(n=t.eventTimes,c=-1;0<s;){var w=31-Ln(s);p=1<<w,w=n[w],w>c&&(c=w),s&=~p}if(s=c,s=ke()-s,s=(120>s?120:480>s?480:1080>s?1080:1920>s?1920:3e3>s?3e3:4320>s?4320:1960*ob(s/1960))-s,10<s){t.timeoutHandle=Vu(Io.bind(null,t,Jt,jr),s);break}Io(t,Jt,jr);break;case 5:Io(t,Jt,jr);break;default:throw Error(o(329))}}}return Zt(t,ke()),t.callbackNode===a?Ym.bind(null,t):null}function Fc(t,n){var a=Si;return t.current.memoizedState.isDehydrated&&(jo(t,n).flags|=256),t=ss(t,n),t!==2&&(n=Jt,Jt=a,n!==null&&Bc(n)),t}function Bc(t){Jt===null?Jt=t:Jt.push.apply(Jt,t)}function ab(t){for(var n=t;;){if(n.flags&16384){var a=n.updateQueue;if(a!==null&&(a=a.stores,a!==null))for(var s=0;s<a.length;s++){var c=a[s],p=c.getSnapshot;c=c.value;try{if(!qn(p(),c))return!1}catch{return!1}}}if(a=n.child,n.subtreeFlags&16384&&a!==null)a.return=n,n=a;else{if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return!0;n=n.return}n.sibling.return=n.return,n=n.sibling}}return!0}function co(t,n){for(n&=~jc,n&=~ns,t.suspendedLanes|=n,t.pingedLanes&=~n,t=t.expirationTimes;0<n;){var a=31-Ln(n),s=1<<a;t[a]=-1,n&=~s}}function Xm(t){if(De&6)throw Error(o(327));Sa();var n=gl(t,0);if(!(n&1))return Zt(t,ke()),null;var a=ss(t,n);if(t.tag!==0&&a===2){var s=bu(t);s!==0&&(n=s,a=Fc(t,s))}if(a===1)throw a=wi,jo(t,0),co(t,n),Zt(t,ke()),a;if(a===6)throw Error(o(345));return t.finishedWork=t.current.alternate,t.finishedLanes=n,Io(t,Jt,jr),Zt(t,ke()),null}function Lc(t,n){var a=De;De|=1;try{return t(n)}finally{De=a,De===0&&(wa=ke()+500,jl&&ro())}}function Oo(t){so!==null&&so.tag===0&&!(De&6)&&Sa();var n=De;De|=1;var a=Tn.transition,s=Ve;try{if(Tn.transition=null,Ve=1,t)return t()}finally{Ve=s,Tn.transition=a,De=n,!(De&6)&&ro()}}function qc(){cn=xa.current,et(xa)}function jo(t,n){t.finishedWork=null,t.finishedLanes=0;var a=t.timeoutHandle;if(a!==-1&&(t.timeoutHandle=-1,jv(a)),gt!==null)for(a=gt.return;a!==null;){var s=a;switch(Ju(s),s.tag){case 1:s=s.type.childContextTypes,s!=null&&Ml();break;case 3:ya(),et(Yt),et(It),dc();break;case 5:uc(s);break;case 4:ya();break;case 13:et(ot);break;case 19:et(ot);break;case 10:oc(s.type._context);break;case 22:case 23:qc()}a=a.return}if(St=t,gt=t=po(t.current,null),Nt=cn=n,xt=0,wi=null,jc=ns=Mo=0,Jt=Si=null,Po!==null){for(n=0;n<Po.length;n++)if(a=Po[n],s=a.interleaved,s!==null){a.interleaved=null;var c=s.next,p=a.pending;if(p!==null){var w=p.next;p.next=c,s.next=w}a.pending=s}Po=null}return t}function Hm(t,n){do{var a=gt;try{if(rc(),Ql.current=Yl,Vl){for(var s=at.memoizedState;s!==null;){var c=s.queue;c!==null&&(c.pending=null),s=s.next}Vl=!1}if(Ro=0,wt=bt=at=null,hi=!1,gi=0,Oc.current=null,a===null||a.return===null){xt=1,wi=n,gt=null;break}e:{var p=t,w=a.return,O=a,D=n;if(n=Nt,O.flags|=32768,D!==null&&typeof D=="object"&&typeof D.then=="function"){var V=D,ne=O,oe=ne.tag;if(!(ne.mode&1)&&(oe===0||oe===11||oe===15)){var te=ne.alternate;te?(ne.updateQueue=te.updateQueue,ne.memoizedState=te.memoizedState,ne.lanes=te.lanes):(ne.updateQueue=null,ne.memoizedState=null)}var se=wm(w);if(se!==null){se.flags&=-257,Sm(se,w,O,p,n),se.mode&1&&xm(p,V,n),n=se,D=V;var me=n.updateQueue;if(me===null){var ge=new Set;ge.add(D),n.updateQueue=ge}else me.add(D);break e}else{if(!(n&1)){xm(p,V,n),Wc();break e}D=Error(o(426))}}else if(nt&&O.mode&1){var ft=wm(w);if(ft!==null){!(ft.flags&65536)&&(ft.flags|=256),Sm(ft,w,O,p,n),tc(va(D,O));break e}}p=D=va(D,O),xt!==4&&(xt=2),Si===null?Si=[p]:Si.push(p),p=w;do{switch(p.tag){case 3:p.flags|=65536,n&=-n,p.lanes|=n;var U=vm(p,D,n);Qf(p,U);break e;case 1:O=D;var B=p.type,Q=p.stateNode;if(!(p.flags&128)&&(typeof B.getDerivedStateFromError=="function"||Q!==null&&typeof Q.componentDidCatch=="function"&&(lo===null||!lo.has(Q)))){p.flags|=65536,n&=-n,p.lanes|=n;var ae=bm(p,O,n);Qf(p,ae);break e}}p=p.return}while(p!==null)}eh(a)}catch(ve){n=ve,gt===a&&a!==null&&(gt=a=a.return);continue}break}while(!0)}function Jm(){var t=ts.current;return ts.current=Yl,t===null?Yl:t}function Wc(){(xt===0||xt===3||xt===2)&&(xt=4),St===null||!(Mo&268435455)&&!(ns&268435455)||co(St,Nt)}function ss(t,n){var a=De;De|=2;var s=Jm();(St!==t||Nt!==n)&&(jr=null,jo(t,n));do try{ib();break}catch(c){Hm(t,c)}while(!0);if(rc(),De=a,ts.current=s,gt!==null)throw Error(o(261));return St=null,Nt=0,xt}function ib(){for(;gt!==null;)Zm(gt)}function lb(){for(;gt!==null&&!Tr();)Zm(gt)}function Zm(t){var n=rh(t.alternate,t,cn);t.memoizedProps=t.pendingProps,n===null?eh(t):gt=n,Oc.current=null}function eh(t){var n=t;do{var a=n.alternate;if(t=n.return,n.flags&32768){if(a=eb(a,n),a!==null){a.flags&=32767,gt=a;return}if(t!==null)t.flags|=32768,t.subtreeFlags=0,t.deletions=null;else{xt=6,gt=null;return}}else if(a=Zv(a,n,cn),a!==null){gt=a;return}if(n=n.sibling,n!==null){gt=n;return}gt=n=t}while(n!==null);xt===0&&(xt=5)}function Io(t,n,a){var s=Ve,c=Tn.transition;try{Tn.transition=null,Ve=1,sb(t,n,a,s)}finally{Tn.transition=c,Ve=s}return null}function sb(t,n,a,s){do Sa();while(so!==null);if(De&6)throw Error(o(327));a=t.finishedWork;var c=t.finishedLanes;if(a===null)return null;if(t.finishedWork=null,t.finishedLanes=0,a===t.current)throw Error(o(177));t.callbackNode=null,t.callbackPriority=0;var p=a.lanes|a.childLanes;if(q0(t,p),t===St&&(gt=St=null,Nt=0),!(a.subtreeFlags&2064)&&!(a.flags&2064)||os||(os=!0,oh(ea,function(){return Sa(),null})),p=(a.flags&15990)!==0,a.subtreeFlags&15990||p){p=Tn.transition,Tn.transition=null;var w=Ve;Ve=1;var O=De;De|=4,Oc.current=null,nb(t,a),Um(a,t),Nv(Uu),bl=!!Wu,Uu=Wu=null,t.current=a,rb(a),Vr(),De=O,Ve=w,Tn.transition=p}else t.current=a;if(os&&(os=!1,so=t,as=c),p=t.pendingLanes,p===0&&(lo=null),I0(a.stateNode),Zt(t,ke()),n!==null)for(s=t.onRecoverableError,a=0;a<n.length;a++)c=n[a],s(c.value,{componentStack:c.stack,digest:c.digest});if(rs)throw rs=!1,t=zc,zc=null,t;return as&1&&t.tag!==0&&Sa(),p=t.pendingLanes,p&1?t===Dc?ki++:(ki=0,Dc=t):ki=0,ro(),null}function Sa(){if(so!==null){var t=Bp(as),n=Tn.transition,a=Ve;try{if(Tn.transition=null,Ve=16>t?16:t,so===null)var s=!1;else{if(t=so,so=null,as=0,De&6)throw Error(o(331));var c=De;for(De|=4,pe=t.current;pe!==null;){var p=pe,w=p.child;if(pe.flags&16){var O=p.deletions;if(O!==null){for(var D=0;D<O.length;D++){var V=O[D];for(pe=V;pe!==null;){var ne=pe;switch(ne.tag){case 0:case 11:case 15:xi(8,ne,p)}var oe=ne.child;if(oe!==null)oe.return=ne,pe=oe;else for(;pe!==null;){ne=pe;var te=ne.sibling,se=ne.return;if(Fm(ne),ne===V){pe=null;break}if(te!==null){te.return=se,pe=te;break}pe=se}}}var me=p.alternate;if(me!==null){var ge=me.child;if(ge!==null){me.child=null;do{var ft=ge.sibling;ge.sibling=null,ge=ft}while(ge!==null)}}pe=p}}if(p.subtreeFlags&2064&&w!==null)w.return=p,pe=w;else e:for(;pe!==null;){if(p=pe,p.flags&2048)switch(p.tag){case 0:case 11:case 15:xi(9,p,p.return)}var U=p.sibling;if(U!==null){U.return=p.return,pe=U;break e}pe=p.return}}var B=t.current;for(pe=B;pe!==null;){w=pe;var Q=w.child;if(w.subtreeFlags&2064&&Q!==null)Q.return=w,pe=Q;else e:for(w=B;pe!==null;){if(O=pe,O.flags&2048)try{switch(O.tag){case 0:case 11:case 15:es(9,O)}}catch(ve){st(O,O.return,ve)}if(O===w){pe=null;break e}var ae=O.sibling;if(ae!==null){ae.return=O.return,pe=ae;break e}pe=O.return}}if(De=c,ro(),or&&typeof or.onPostCommitFiberRoot=="function")try{or.onPostCommitFiberRoot(fl,t)}catch{}s=!0}return s}finally{Ve=a,Tn.transition=n}}return!1}function th(t,n,a){n=va(a,n),n=vm(t,n,1),t=ao(t,n,1),n=Kt(),t!==null&&(Ka(t,1,n),Zt(t,n))}function st(t,n,a){if(t.tag===3)th(t,t,a);else for(;n!==null;){if(n.tag===3){th(n,t,a);break}else if(n.tag===1){var s=n.stateNode;if(typeof n.type.getDerivedStateFromError=="function"||typeof s.componentDidCatch=="function"&&(lo===null||!lo.has(s))){t=va(a,t),t=bm(n,t,1),n=ao(n,t,1),t=Kt(),n!==null&&(Ka(n,1,t),Zt(n,t));break}}n=n.return}}function ub(t,n,a){var s=t.pingCache;s!==null&&s.delete(n),n=Kt(),t.pingedLanes|=t.suspendedLanes&a,St===t&&(Nt&a)===a&&(xt===4||xt===3&&(Nt&130023424)===Nt&&500>ke()-Ic?jo(t,0):jc|=a),Zt(t,n)}function nh(t,n){n===0&&(t.mode&1?(n=hl,hl<<=1,!(hl&130023424)&&(hl=4194304)):n=1);var a=Kt();t=Rr(t,n),t!==null&&(Ka(t,n,a),Zt(t,a))}function cb(t){var n=t.memoizedState,a=0;n!==null&&(a=n.retryLane),nh(t,a)}function db(t,n){var a=0;switch(t.tag){case 13:var s=t.stateNode,c=t.memoizedState;c!==null&&(a=c.retryLane);break;case 19:s=t.stateNode;break;default:throw Error(o(314))}s!==null&&s.delete(n),nh(t,a)}var rh;rh=function(t,n,a){if(t!==null)if(t.memoizedProps!==n.pendingProps||Yt.current)Ht=!0;else{if(!(t.lanes&a)&&!(n.flags&128))return Ht=!1,Jv(t,n,a);Ht=!!(t.flags&131072)}else Ht=!1,nt&&n.flags&1048576&&jf(n,zl,n.index);switch(n.lanes=0,n.tag){case 2:var s=n.type;Jl(t,n),t=n.pendingProps;var c=ca(n,It.current);ga(n,a),c=mc(null,n,s,t,c,a);var p=hc();return n.flags|=1,typeof c=="object"&&c!==null&&typeof c.render=="function"&&c.$$typeof===void 0?(n.tag=1,n.memoizedState=null,n.updateQueue=null,Xt(s)?(p=!0,Ol(n)):p=!1,n.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,lc(n),c.updater=Xl,n.stateNode=c,c._reactInternals=n,wc(n,s,t,a),n=$c(null,n,s,!0,p,a)):(n.tag=0,nt&&p&&Hu(n),Vt(null,n,c,a),n=n.child),n;case 16:s=n.elementType;e:{switch(Jl(t,n),t=n.pendingProps,c=s._init,s=c(s._payload),n.type=s,c=n.tag=fb(s),t=Un(s,t),c){case 0:n=Cc(null,n,s,t,a);break e;case 1:n=Nm(null,n,s,t,a);break e;case 11:n=km(null,n,s,t,a);break e;case 14:n=Cm(null,n,s,Un(s.type,t),a);break e}throw Error(o(306,s,""))}return n;case 0:return s=n.type,c=n.pendingProps,c=n.elementType===s?c:Un(s,c),Cc(t,n,s,c,a);case 1:return s=n.type,c=n.pendingProps,c=n.elementType===s?c:Un(s,c),Nm(t,n,s,c,a);case 3:e:{if(Am(n),t===null)throw Error(o(387));s=n.pendingProps,p=n.memoizedState,c=p.element,Uf(t,n),Wl(n,s,null,a);var w=n.memoizedState;if(s=w.element,p.isDehydrated)if(p={element:s,isDehydrated:!1,cache:w.cache,pendingSuspenseBoundaries:w.pendingSuspenseBoundaries,transitions:w.transitions},n.updateQueue.baseState=p,n.memoizedState=p,n.flags&256){c=va(Error(o(423)),n),n=Pm(t,n,s,a,c);break e}else if(s!==c){c=va(Error(o(424)),n),n=Pm(t,n,s,a,c);break e}else for(un=eo(n.stateNode.containerInfo.firstChild),sn=n,nt=!0,Wn=null,a=qf(n,null,s,a),n.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling;else{if(fa(),s===c){n=Or(t,n,a);break e}Vt(t,n,s,a)}n=n.child}return n;case 5:return Kf(n),t===null&&ec(n),s=n.type,c=n.pendingProps,p=t!==null?t.memoizedProps:null,w=c.children,Qu(s,c)?w=null:p!==null&&Qu(s,p)&&(n.flags|=32),Tm(t,n),Vt(t,n,w,a),n.child;case 6:return t===null&&ec(n),null;case 13:return _m(t,n,a);case 4:return sc(n,n.stateNode.containerInfo),s=n.pendingProps,t===null?n.child=ma(n,null,s,a):Vt(t,n,s,a),n.child;case 11:return s=n.type,c=n.pendingProps,c=n.elementType===s?c:Un(s,c),km(t,n,s,c,a);case 7:return Vt(t,n,n.pendingProps,a),n.child;case 8:return Vt(t,n,n.pendingProps.children,a),n.child;case 12:return Vt(t,n,n.pendingProps.children,a),n.child;case 10:e:{if(s=n.type._context,c=n.pendingProps,p=n.memoizedProps,w=c.value,Je(Bl,s._currentValue),s._currentValue=w,p!==null)if(qn(p.value,w)){if(p.children===c.children&&!Yt.current){n=Or(t,n,a);break e}}else for(p=n.child,p!==null&&(p.return=n);p!==null;){var O=p.dependencies;if(O!==null){w=p.child;for(var D=O.firstContext;D!==null;){if(D.context===s){if(p.tag===1){D=Mr(-1,a&-a),D.tag=2;var V=p.updateQueue;if(V!==null){V=V.shared;var ne=V.pending;ne===null?D.next=D:(D.next=ne.next,ne.next=D),V.pending=D}}p.lanes|=a,D=p.alternate,D!==null&&(D.lanes|=a),ac(p.return,a,n),O.lanes|=a;break}D=D.next}}else if(p.tag===10)w=p.type===n.type?null:p.child;else if(p.tag===18){if(w=p.return,w===null)throw Error(o(341));w.lanes|=a,O=w.alternate,O!==null&&(O.lanes|=a),ac(w,a,n),w=p.sibling}else w=p.child;if(w!==null)w.return=p;else for(w=p;w!==null;){if(w===n){w=null;break}if(p=w.sibling,p!==null){p.return=w.return,w=p;break}w=w.return}p=w}Vt(t,n,c.children,a),n=n.child}return n;case 9:return c=n.type,s=n.pendingProps.children,ga(n,a),c=$n(c),s=s(c),n.flags|=1,Vt(t,n,s,a),n.child;case 14:return s=n.type,c=Un(s,n.pendingProps),c=Un(s.type,c),Cm(t,n,s,c,a);case 15:return $m(t,n,n.type,n.pendingProps,a);case 17:return s=n.type,c=n.pendingProps,c=n.elementType===s?c:Un(s,c),Jl(t,n),n.tag=1,Xt(s)?(t=!0,Ol(n)):t=!1,ga(n,a),gm(n,s,c),wc(n,s,c,a),$c(null,n,s,!0,t,a);case 19:return Mm(t,n,a);case 22:return Em(t,n,a)}throw Error(o(156,n.tag))};function oh(t,n){return Ke(t,n)}function pb(t,n,a,s){this.tag=t,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=n,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=s,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Nn(t,n,a,s){return new pb(t,n,a,s)}function Uc(t){return t=t.prototype,!(!t||!t.isReactComponent)}function fb(t){if(typeof t=="function")return Uc(t)?1:0;if(t!=null){if(t=t.$$typeof,t===_)return 11;if(t===G)return 14}return 2}function po(t,n){var a=t.alternate;return a===null?(a=Nn(t.tag,n,t.key,t.mode),a.elementType=t.elementType,a.type=t.type,a.stateNode=t.stateNode,a.alternate=t,t.alternate=a):(a.pendingProps=n,a.type=t.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=t.flags&14680064,a.childLanes=t.childLanes,a.lanes=t.lanes,a.child=t.child,a.memoizedProps=t.memoizedProps,a.memoizedState=t.memoizedState,a.updateQueue=t.updateQueue,n=t.dependencies,a.dependencies=n===null?null:{lanes:n.lanes,firstContext:n.firstContext},a.sibling=t.sibling,a.index=t.index,a.ref=t.ref,a}function us(t,n,a,s,c,p){var w=2;if(s=t,typeof t=="function")Uc(t)&&(w=1);else if(typeof t=="string")w=5;else e:switch(t){case I:return zo(a.children,c,p,n);case L:w=8,c|=8;break;case z:return t=Nn(12,a,n,c|2),t.elementType=z,t.lanes=p,t;case R:return t=Nn(13,a,n,c),t.elementType=R,t.lanes=p,t;case F:return t=Nn(19,a,n,c),t.elementType=F,t.lanes=p,t;case Z:return cs(a,c,p,n);default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case h:w=10;break e;case P:w=9;break e;case _:w=11;break e;case G:w=14;break e;case H:w=16,s=null;break e}throw Error(o(130,t==null?t:typeof t,""))}return n=Nn(w,a,n,c),n.elementType=t,n.type=s,n.lanes=p,n}function zo(t,n,a,s){return t=Nn(7,t,s,n),t.lanes=a,t}function cs(t,n,a,s){return t=Nn(22,t,s,n),t.elementType=Z,t.lanes=a,t.stateNode={isHidden:!1},t}function Qc(t,n,a){return t=Nn(6,t,null,n),t.lanes=a,t}function Vc(t,n,a){return n=Nn(4,t.children!==null?t.children:[],t.key,n),n.lanes=a,n.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},n}function mb(t,n,a,s,c){this.tag=n,this.containerInfo=t,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=xu(0),this.expirationTimes=xu(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=xu(0),this.identifierPrefix=s,this.onRecoverableError=c,this.mutableSourceEagerHydrationData=null}function Kc(t,n,a,s,c,p,w,O,D){return t=new mb(t,n,a,O,D),n===1?(n=1,p===!0&&(n|=8)):n=0,p=Nn(3,null,null,n),t.current=p,p.stateNode=t,p.memoizedState={element:s,isDehydrated:a,cache:null,transitions:null,pendingSuspenseBoundaries:null},lc(p),t}function hb(t,n,a){var s=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:M,key:s==null?null:""+s,children:t,containerInfo:n,implementation:a}}function ah(t){if(!t)return no;t=t._reactInternals;e:{if(xn(t)!==t||t.tag!==1)throw Error(o(170));var n=t;do{switch(n.tag){case 3:n=n.stateNode.context;break e;case 1:if(Xt(n.type)){n=n.stateNode.__reactInternalMemoizedMergedChildContext;break e}}n=n.return}while(n!==null);throw Error(o(171))}if(t.tag===1){var a=t.type;if(Xt(a))return Rf(t,a,n)}return n}function ih(t,n,a,s,c,p,w,O,D){return t=Kc(a,s,!0,t,c,p,w,O,D),t.context=ah(null),a=t.current,s=Kt(),c=uo(a),p=Mr(s,c),p.callback=n??null,ao(a,p,c),t.current.lanes=c,Ka(t,c,s),Zt(t,s),t}function ds(t,n,a,s){var c=n.current,p=Kt(),w=uo(c);return a=ah(a),n.context===null?n.context=a:n.pendingContext=a,n=Mr(p,w),n.payload={element:t},s=s===void 0?null:s,s!==null&&(n.callback=s),t=ao(c,n,w),t!==null&&(Kn(t,c,w,p),ql(t,c,w)),w}function ps(t){if(t=t.current,!t.child)return null;switch(t.child.tag){case 5:return t.child.stateNode;default:return t.child.stateNode}}function lh(t,n){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var a=t.retryLane;t.retryLane=a!==0&&a<n?a:n}}function Gc(t,n){lh(t,n),(t=t.alternate)&&lh(t,n)}function gb(){return null}var sh=typeof reportError=="function"?reportError:function(t){console.error(t)};function Yc(t){this._internalRoot=t}fs.prototype.render=Yc.prototype.render=function(t){var n=this._internalRoot;if(n===null)throw Error(o(409));ds(t,n,null,null)},fs.prototype.unmount=Yc.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var n=t.containerInfo;Oo(function(){ds(null,t,null,null)}),n[Nr]=null}};function fs(t){this._internalRoot=t}fs.prototype.unstable_scheduleHydration=function(t){if(t){var n=Wp();t={blockedOn:null,target:t,priority:n};for(var a=0;a<Hr.length&&n!==0&&n<Hr[a].priority;a++);Hr.splice(a,0,t),a===0&&Vp(t)}};function Xc(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function ms(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11&&(t.nodeType!==8||t.nodeValue!==" react-mount-point-unstable "))}function uh(){}function yb(t,n,a,s,c){if(c){if(typeof s=="function"){var p=s;s=function(){var V=ps(w);p.call(V)}}var w=ih(n,s,t,0,null,!1,!1,"",uh);return t._reactRootContainer=w,t[Nr]=w.current,li(t.nodeType===8?t.parentNode:t),Oo(),w}for(;c=t.lastChild;)t.removeChild(c);if(typeof s=="function"){var O=s;s=function(){var V=ps(D);O.call(V)}}var D=Kc(t,0,!1,null,null,!1,!1,"",uh);return t._reactRootContainer=D,t[Nr]=D.current,li(t.nodeType===8?t.parentNode:t),Oo(function(){ds(n,D,a,s)}),D}function hs(t,n,a,s,c){var p=a._reactRootContainer;if(p){var w=p;if(typeof c=="function"){var O=c;c=function(){var D=ps(w);O.call(D)}}ds(n,w,t,c)}else w=yb(a,n,t,c,s);return ps(w)}Lp=function(t){switch(t.tag){case 3:var n=t.stateNode;if(n.current.memoizedState.isDehydrated){var a=Va(n.pendingLanes);a!==0&&(wu(n,a|1),Zt(n,ke()),!(De&6)&&(wa=ke()+500,ro()))}break;case 13:Oo(function(){var s=Rr(t,1);if(s!==null){var c=Kt();Kn(s,t,1,c)}}),Gc(t,1)}},Su=function(t){if(t.tag===13){var n=Rr(t,134217728);if(n!==null){var a=Kt();Kn(n,t,134217728,a)}Gc(t,134217728)}},qp=function(t){if(t.tag===13){var n=uo(t),a=Rr(t,n);if(a!==null){var s=Kt();Kn(a,t,n,s)}Gc(t,n)}},Wp=function(){return Ve},Up=function(t,n){var a=Ve;try{return Ve=t,n()}finally{Ve=a}},an=function(t,n,a){switch(n){case"input":if(de(t,a),n=a.name,a.type==="radio"&&n!=null){for(a=t;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll("input[name="+JSON.stringify(""+n)+'][type="radio"]'),n=0;n<a.length;n++){var s=a[n];if(s!==t&&s.form===t.form){var c=Rl(s);if(!c)throw Error(o(90));Me(s),de(s,c)}}}break;case"textarea":Ot(t,a);break;case"select":n=a.value,n!=null&&ye(t,!!a.multiple,n,!1)}},tr=Lc,ko=Oo;var vb={usingClientEntryPoint:!1,Events:[ci,sa,Rl,Bn,Zo,Lc]},Ci={findFiberByHostInstance:Eo,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},bb={bundleType:Ci.bundleType,version:Ci.version,rendererPackageName:Ci.rendererPackageName,rendererConfig:Ci.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:C.ReactCurrentDispatcher,findHostInstanceByFiber:function(t){return t=we(t),t===null?null:t.stateNode},findFiberByHostInstance:Ci.findFiberByHostInstance||gb,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var gs=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!gs.isDisabled&&gs.supportsFiber)try{fl=gs.inject(bb),or=gs}catch{}}return en.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=vb,en.createPortal=function(t,n){var a=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Xc(n))throw Error(o(200));return hb(t,n,null,a)},en.createRoot=function(t,n){if(!Xc(t))throw Error(o(299));var a=!1,s="",c=sh;return n!=null&&(n.unstable_strictMode===!0&&(a=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(c=n.onRecoverableError)),n=Kc(t,1,!1,null,null,a,!1,s,c),t[Nr]=n.current,li(t.nodeType===8?t.parentNode:t),new Yc(n)},en.findDOMNode=function(t){if(t==null)return null;if(t.nodeType===1)return t;var n=t._reactInternals;if(n===void 0)throw typeof t.render=="function"?Error(o(188)):(t=Object.keys(t).join(","),Error(o(268,t)));return t=we(n),t=t===null?null:t.stateNode,t},en.flushSync=function(t){return Oo(t)},en.hydrate=function(t,n,a){if(!ms(n))throw Error(o(200));return hs(null,t,n,!0,a)},en.hydrateRoot=function(t,n,a){if(!Xc(t))throw Error(o(405));var s=a!=null&&a.hydratedSources||null,c=!1,p="",w=sh;if(a!=null&&(a.unstable_strictMode===!0&&(c=!0),a.identifierPrefix!==void 0&&(p=a.identifierPrefix),a.onRecoverableError!==void 0&&(w=a.onRecoverableError)),n=ih(n,null,t,1,a??null,c,!1,p,w),t[Nr]=n.current,li(t),s)for(t=0;t<s.length;t++)a=s[t],c=a._getVersion,c=c(a._source),n.mutableSourceEagerHydrationData==null?n.mutableSourceEagerHydrationData=[a,c]:n.mutableSourceEagerHydrationData.push(a,c);return new fs(n)},en.render=function(t,n,a){if(!ms(n))throw Error(o(200));return hs(null,t,n,!1,a)},en.unmountComponentAtNode=function(t){if(!ms(t))throw Error(o(40));return t._reactRootContainer?(Oo(function(){hs(null,null,t,!1,function(){t._reactRootContainer=null,t[Nr]=null})}),!0):!1},en.unstable_batchedUpdates=Lc,en.unstable_renderSubtreeIntoContainer=function(t,n,a,s){if(!ms(a))throw Error(o(200));if(t==null||t._reactInternals===void 0)throw Error(o(38));return hs(t,n,a,!1,s)},en.version="18.3.1-next-f1338f8080-20240426",en}function Ay(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Ay)}catch(e){console.error(e)}}Ay(),Ny.exports=Xx();var Py=Ny.exports;const $s=Ty(Py),Yi={black:"#000",white:"#fff"},ka={300:"#e57373",400:"#ef5350",500:"#f44336",700:"#d32f2f",800:"#c62828"},Ca={50:"#f3e5f5",200:"#ce93d8",300:"#ba68c8",400:"#ab47bc",500:"#9c27b0",700:"#7b1fa2"},$a={50:"#e3f2fd",200:"#90caf9",400:"#42a5f5",700:"#1976d2",800:"#1565c0"},Ea={300:"#4fc3f7",400:"#29b6f6",500:"#03a9f4",700:"#0288d1",900:"#01579b"},Ta={300:"#81c784",400:"#66bb6a",500:"#4caf50",700:"#388e3c",800:"#2e7d32",900:"#1b5e20"},Ni={300:"#ffb74d",400:"#ffa726",500:"#ff9800",700:"#f57c00",900:"#e65100"},Hx={50:"#fafafa",100:"#f5f5f5",200:"#eeeeee",300:"#e0e0e0",400:"#bdbdbd",500:"#9e9e9e",600:"#757575",700:"#616161",800:"#424242",900:"#212121",A100:"#f5f5f5",A200:"#eeeeee",A400:"#bdbdbd",A700:"#616161"};function Xo(e,...r){const o=new URL(`https://mui.com/production-error/?code=${e}`);return r.forEach(i=>o.searchParams.append("args[]",i)),`Minified MUI error #${e}; visit ${o} for the full message.`}const _y="$$material";function Vs(){return Vs=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var o=arguments[r];for(var i in o)({}).hasOwnProperty.call(o,i)&&(e[i]=o[i])}return e},Vs.apply(null,arguments)}function Jx(e){if(e.sheet)return e.sheet;for(var r=0;r<document.styleSheets.length;r++)if(document.styleSheets[r].ownerNode===e)return document.styleSheets[r]}function Zx(e){var r=document.createElement("style");return r.setAttribute("data-emotion",e.key),e.nonce!==void 0&&r.setAttribute("nonce",e.nonce),r.appendChild(document.createTextNode("")),r.setAttribute("data-s",""),r}var ew=function(){function e(o){var i=this;this._insertTag=function(l){var u;i.tags.length===0?i.insertionPoint?u=i.insertionPoint.nextSibling:i.prepend?u=i.container.firstChild:u=i.before:u=i.tags[i.tags.length-1].nextSibling,i.container.insertBefore(l,u),i.tags.push(l)},this.isSpeedy=o.speedy===void 0?!0:o.speedy,this.tags=[],this.ctr=0,this.nonce=o.nonce,this.key=o.key,this.container=o.container,this.prepend=o.prepend,this.insertionPoint=o.insertionPoint,this.before=null}var r=e.prototype;return r.hydrate=function(o){o.forEach(this._insertTag)},r.insert=function(o){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(Zx(this));var i=this.tags[this.tags.length-1];if(this.isSpeedy){var l=Jx(i);try{l.insertRule(o,l.cssRules.length)}catch{}}else i.appendChild(document.createTextNode(o));this.ctr++},r.flush=function(){this.tags.forEach(function(o){var i;return(i=o.parentNode)==null?void 0:i.removeChild(o)}),this.tags=[],this.ctr=0},e}(),Lt="-ms-",Ks="-moz-",Be="-webkit-",Ry="comm",ip="rule",lp="decl",tw="@import",My="@keyframes",nw="@layer",rw=Math.abs,au=String.fromCharCode,ow=Object.assign;function aw(e,r){return Pt(e,0)^45?(((r<<2^Pt(e,0))<<2^Pt(e,1))<<2^Pt(e,2))<<2^Pt(e,3):0}function Oy(e){return e.trim()}function iw(e,r){return(e=r.exec(e))?e[0]:e}function Le(e,r,o){return e.replace(r,o)}function Td(e,r){return e.indexOf(r)}function Pt(e,r){return e.charCodeAt(r)|0}function Xi(e,r,o){return e.slice(r,o)}function dr(e){return e.length}function sp(e){return e.length}function Es(e,r){return r.push(e),e}function lw(e,r){return e.map(r).join("")}var iu=1,Ia=1,jy=0,on=0,vt=0,qa="";function lu(e,r,o,i,l,u,d){return{value:e,root:r,parent:o,type:i,props:l,children:u,line:iu,column:Ia,length:d,return:""}}function Ai(e,r){return ow(lu("",null,null,"",null,null,0),e,{length:-e.length},r)}function sw(){return vt}function uw(){return vt=on>0?Pt(qa,--on):0,Ia--,vt===10&&(Ia=1,iu--),vt}function pn(){return vt=on<jy?Pt(qa,on++):0,Ia++,vt===10&&(Ia=1,iu++),vt}function hr(){return Pt(qa,on)}function Rs(){return on}function il(e,r){return Xi(qa,e,r)}function Hi(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function Iy(e){return iu=Ia=1,jy=dr(qa=e),on=0,[]}function zy(e){return qa="",e}function Ms(e){return Oy(il(on-1,Nd(e===91?e+2:e===40?e+1:e)))}function cw(e){for(;(vt=hr())&&vt<33;)pn();return Hi(e)>2||Hi(vt)>3?"":" "}function dw(e,r){for(;--r&&pn()&&!(vt<48||vt>102||vt>57&&vt<65||vt>70&&vt<97););return il(e,Rs()+(r<6&&hr()==32&&pn()==32))}function Nd(e){for(;pn();)switch(vt){case e:return on;case 34:case 39:e!==34&&e!==39&&Nd(vt);break;case 40:e===41&&Nd(e);break;case 92:pn();break}return on}function pw(e,r){for(;pn()&&e+vt!==57&&!(e+vt===84&&hr()===47););return"/*"+il(r,on-1)+"*"+au(e===47?e:pn())}function fw(e){for(;!Hi(hr());)pn();return il(e,on)}function mw(e){return zy(Os("",null,null,null,[""],e=Iy(e),0,[0],e))}function Os(e,r,o,i,l,u,d,f,m){for(var g=0,S=0,y=d,v=0,$=0,E=0,b=1,T=1,N=1,A=0,x="",C=l,k=u,M=i,I=x;T;)switch(E=A,A=pn()){case 40:if(E!=108&&Pt(I,y-1)==58){Td(I+=Le(Ms(A),"&","&\f"),"&\f")!=-1&&(N=-1);break}case 34:case 39:case 91:I+=Ms(A);break;case 9:case 10:case 13:case 32:I+=cw(E);break;case 92:I+=dw(Rs()-1,7);continue;case 47:switch(hr()){case 42:case 47:Es(hw(pw(pn(),Rs()),r,o),m);break;default:I+="/"}break;case 123*b:f[g++]=dr(I)*N;case 125*b:case 59:case 0:switch(A){case 0:case 125:T=0;case 59+S:N==-1&&(I=Le(I,/\f/g,"")),$>0&&dr(I)-y&&Es($>32?Gh(I+";",i,o,y-1):Gh(Le(I," ","")+";",i,o,y-2),m);break;case 59:I+=";";default:if(Es(M=Kh(I,r,o,g,S,l,f,x,C=[],k=[],y),u),A===123)if(S===0)Os(I,r,M,M,C,u,y,f,k);else switch(v===99&&Pt(I,3)===110?100:v){case 100:case 108:case 109:case 115:Os(e,M,M,i&&Es(Kh(e,M,M,0,0,l,f,x,l,C=[],y),k),l,k,y,f,i?C:k);break;default:Os(I,M,M,M,[""],k,0,f,k)}}g=S=$=0,b=N=1,x=I="",y=d;break;case 58:y=1+dr(I),$=E;default:if(b<1){if(A==123)--b;else if(A==125&&b++==0&&uw()==125)continue}switch(I+=au(A),A*b){case 38:N=S>0?1:(I+="\f",-1);break;case 44:f[g++]=(dr(I)-1)*N,N=1;break;case 64:hr()===45&&(I+=Ms(pn())),v=hr(),S=y=dr(x=I+=fw(Rs())),A++;break;case 45:E===45&&dr(I)==2&&(b=0)}}return u}function Kh(e,r,o,i,l,u,d,f,m,g,S){for(var y=l-1,v=l===0?u:[""],$=sp(v),E=0,b=0,T=0;E<i;++E)for(var N=0,A=Xi(e,y+1,y=rw(b=d[E])),x=e;N<$;++N)(x=Oy(b>0?v[N]+" "+A:Le(A,/&\f/g,v[N])))&&(m[T++]=x);return lu(e,r,o,l===0?ip:f,m,g,S)}function hw(e,r,o){return lu(e,r,o,Ry,au(sw()),Xi(e,2,-2),0)}function Gh(e,r,o,i){return lu(e,r,o,lp,Xi(e,0,i),Xi(e,i+1,-1),i)}function Ra(e,r){for(var o="",i=sp(e),l=0;l<i;l++)o+=r(e[l],l,e,r)||"";return o}function gw(e,r,o,i){switch(e.type){case nw:if(e.children.length)break;case tw:case lp:return e.return=e.return||e.value;case Ry:return"";case My:return e.return=e.value+"{"+Ra(e.children,i)+"}";case ip:e.value=e.props.join(",")}return dr(o=Ra(e.children,i))?e.return=e.value+"{"+o+"}":""}function yw(e){var r=sp(e);return function(o,i,l,u){for(var d="",f=0;f<r;f++)d+=e[f](o,i,l,u)||"";return d}}function vw(e){return function(r){r.root||(r=r.return)&&e(r)}}function Dy(e){var r=Object.create(null);return function(o){return r[o]===void 0&&(r[o]=e(o)),r[o]}}var bw=function(e,r,o){for(var i=0,l=0;i=l,l=hr(),i===38&&l===12&&(r[o]=1),!Hi(l);)pn();return il(e,on)},xw=function(e,r){var o=-1,i=44;do switch(Hi(i)){case 0:i===38&&hr()===12&&(r[o]=1),e[o]+=bw(on-1,r,o);break;case 2:e[o]+=Ms(i);break;case 4:if(i===44){e[++o]=hr()===58?"&\f":"",r[o]=e[o].length;break}default:e[o]+=au(i)}while(i=pn());return e},ww=function(e,r){return zy(xw(Iy(e),r))},Yh=new WeakMap,Sw=function(e){if(!(e.type!=="rule"||!e.parent||e.length<1)){for(var r=e.value,o=e.parent,i=e.column===o.column&&e.line===o.line;o.type!=="rule";)if(o=o.parent,!o)return;if(!(e.props.length===1&&r.charCodeAt(0)!==58&&!Yh.get(o))&&!i){Yh.set(e,!0);for(var l=[],u=ww(r,l),d=o.props,f=0,m=0;f<u.length;f++)for(var g=0;g<d.length;g++,m++)e.props[m]=l[f]?u[f].replace(/&\f/g,d[g]):d[g]+" "+u[f]}}},kw=function(e){if(e.type==="decl"){var r=e.value;r.charCodeAt(0)===108&&r.charCodeAt(2)===98&&(e.return="",e.value="")}};function Fy(e,r){switch(aw(e,r)){case 5103:return Be+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return Be+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return Be+e+Ks+e+Lt+e+e;case 6828:case 4268:return Be+e+Lt+e+e;case 6165:return Be+e+Lt+"flex-"+e+e;case 5187:return Be+e+Le(e,/(\w+).+(:[^]+)/,Be+"box-$1$2"+Lt+"flex-$1$2")+e;case 5443:return Be+e+Lt+"flex-item-"+Le(e,/flex-|-self/,"")+e;case 4675:return Be+e+Lt+"flex-line-pack"+Le(e,/align-content|flex-|-self/,"")+e;case 5548:return Be+e+Lt+Le(e,"shrink","negative")+e;case 5292:return Be+e+Lt+Le(e,"basis","preferred-size")+e;case 6060:return Be+"box-"+Le(e,"-grow","")+Be+e+Lt+Le(e,"grow","positive")+e;case 4554:return Be+Le(e,/([^-])(transform)/g,"$1"+Be+"$2")+e;case 6187:return Le(Le(Le(e,/(zoom-|grab)/,Be+"$1"),/(image-set)/,Be+"$1"),e,"")+e;case 5495:case 3959:return Le(e,/(image-set\([^]*)/,Be+"$1$`$1");case 4968:return Le(Le(e,/(.+:)(flex-)?(.*)/,Be+"box-pack:$3"+Lt+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+Be+e+e;case 4095:case 3583:case 4068:case 2532:return Le(e,/(.+)-inline(.+)/,Be+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(dr(e)-1-r>6)switch(Pt(e,r+1)){case 109:if(Pt(e,r+4)!==45)break;case 102:return Le(e,/(.+:)(.+)-([^]+)/,"$1"+Be+"$2-$3$1"+Ks+(Pt(e,r+3)==108?"$3":"$2-$3"))+e;case 115:return~Td(e,"stretch")?Fy(Le(e,"stretch","fill-available"),r)+e:e}break;case 4949:if(Pt(e,r+1)!==115)break;case 6444:switch(Pt(e,dr(e)-3-(~Td(e,"!important")&&10))){case 107:return Le(e,":",":"+Be)+e;case 101:return Le(e,/(.+:)([^;!]+)(;|!.+)?/,"$1"+Be+(Pt(e,14)===45?"inline-":"")+"box$3$1"+Be+"$2$3$1"+Lt+"$2box$3")+e}break;case 5936:switch(Pt(e,r+11)){case 114:return Be+e+Lt+Le(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return Be+e+Lt+Le(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return Be+e+Lt+Le(e,/[svh]\w+-[tblr]{2}/,"lr")+e}return Be+e+Lt+e+e}return e}var Cw=function(e,r,o,i){if(e.length>-1&&!e.return)switch(e.type){case lp:e.return=Fy(e.value,e.length);break;case My:return Ra([Ai(e,{value:Le(e.value,"@","@"+Be)})],i);case ip:if(e.length)return lw(e.props,function(l){switch(iw(l,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return Ra([Ai(e,{props:[Le(l,/:(read-\w+)/,":"+Ks+"$1")]})],i);case"::placeholder":return Ra([Ai(e,{props:[Le(l,/:(plac\w+)/,":"+Be+"input-$1")]}),Ai(e,{props:[Le(l,/:(plac\w+)/,":"+Ks+"$1")]}),Ai(e,{props:[Le(l,/:(plac\w+)/,Lt+"input-$1")]})],i)}return""})}},$w=[Cw],Ew=function(e){var r=e.key;if(r==="css"){var o=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(o,function(E){var b=E.getAttribute("data-emotion");b.indexOf(" ")!==-1&&(document.head.appendChild(E),E.setAttribute("data-s",""))})}var i=e.stylisPlugins||$w,l={},u,d=[];u=e.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+r+' "]'),function(E){for(var b=E.getAttribute("data-emotion").split(" "),T=1;T<b.length;T++)l[b[T]]=!0;d.push(E)});var f,m=[Sw,kw];{var g,S=[gw,vw(function(E){g.insert(E)})],y=yw(m.concat(i,S)),v=function(E){return Ra(mw(E),y)};f=function(E,b,T,N){g=T,v(E?E+"{"+b.styles+"}":b.styles),N&&($.inserted[b.name]=!0)}}var $={key:r,sheet:new ew({key:r,container:u,nonce:e.nonce,speedy:e.speedy,prepend:e.prepend,insertionPoint:e.insertionPoint}),nonce:e.nonce,inserted:l,registered:{},insert:f};return $.sheet.hydrate(d),$},Tw=!0;function By(e,r,o){var i="";return o.split(" ").forEach(function(l){e[l]!==void 0?r.push(e[l]+";"):l&&(i+=l+" ")}),i}var up=function(e,r,o){var i=e.key+"-"+r.name;(o===!1||Tw===!1)&&e.registered[i]===void 0&&(e.registered[i]=r.styles)},Ly=function(e,r,o){up(e,r,o);var i=e.key+"-"+r.name;if(e.inserted[r.name]===void 0){var l=r;do e.insert(r===l?"."+i:"",l,e.sheet,!0),l=l.next;while(l!==void 0)}};function Nw(e){for(var r=0,o,i=0,l=e.length;l>=4;++i,l-=4)o=e.charCodeAt(i)&255|(e.charCodeAt(++i)&255)<<8|(e.charCodeAt(++i)&255)<<16|(e.charCodeAt(++i)&255)<<24,o=(o&65535)*1540483477+((o>>>16)*59797<<16),o^=o>>>24,r=(o&65535)*1540483477+((o>>>16)*59797<<16)^(r&65535)*1540483477+((r>>>16)*59797<<16);switch(l){case 3:r^=(e.charCodeAt(i+2)&255)<<16;case 2:r^=(e.charCodeAt(i+1)&255)<<8;case 1:r^=e.charCodeAt(i)&255,r=(r&65535)*1540483477+((r>>>16)*59797<<16)}return r^=r>>>13,r=(r&65535)*1540483477+((r>>>16)*59797<<16),((r^r>>>15)>>>0).toString(36)}var Aw={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},Pw=/[A-Z]|^ms/g,_w=/_EMO_([^_]+?)_([^]*?)_EMO_/g,qy=function(e){return e.charCodeAt(1)===45},Xh=function(e){return e!=null&&typeof e!="boolean"},ld=Dy(function(e){return qy(e)?e:e.replace(Pw,"-$&").toLowerCase()}),Hh=function(e,r){switch(e){case"animation":case"animationName":if(typeof r=="string")return r.replace(_w,function(o,i,l){return pr={name:i,styles:l,next:pr},i})}return Aw[e]!==1&&!qy(e)&&typeof r=="number"&&r!==0?r+"px":r};function Ji(e,r,o){if(o==null)return"";var i=o;if(i.__emotion_styles!==void 0)return i;switch(typeof o){case"boolean":return"";case"object":{var l=o;if(l.anim===1)return pr={name:l.name,styles:l.styles,next:pr},l.name;var u=o;if(u.styles!==void 0){var d=u.next;if(d!==void 0)for(;d!==void 0;)pr={name:d.name,styles:d.styles,next:pr},d=d.next;var f=u.styles+";";return f}return Rw(e,r,o)}case"function":{if(e!==void 0){var m=pr,g=o(e);return pr=m,Ji(e,r,g)}break}}var S=o;if(r==null)return S;var y=r[S];return y!==void 0?y:S}function Rw(e,r,o){var i="";if(Array.isArray(o))for(var l=0;l<o.length;l++)i+=Ji(e,r,o[l])+";";else for(var u in o){var d=o[u];if(typeof d!="object"){var f=d;r!=null&&r[f]!==void 0?i+=u+"{"+r[f]+"}":Xh(f)&&(i+=ld(u)+":"+Hh(u,f)+";")}else if(Array.isArray(d)&&typeof d[0]=="string"&&(r==null||r[d[0]]===void 0))for(var m=0;m<d.length;m++)Xh(d[m])&&(i+=ld(u)+":"+Hh(u,d[m])+";");else{var g=Ji(e,r,d);switch(u){case"animation":case"animationName":{i+=ld(u)+":"+g+";";break}default:i+=u+"{"+g+"}"}}}return i}var Jh=/label:\s*([^\s;{]+)\s*(;|$)/g,pr;function su(e,r,o){if(e.length===1&&typeof e[0]=="object"&&e[0]!==null&&e[0].styles!==void 0)return e[0];var i=!0,l="";pr=void 0;var u=e[0];if(u==null||u.raw===void 0)i=!1,l+=Ji(o,r,u);else{var d=u;l+=d[0]}for(var f=1;f<e.length;f++)if(l+=Ji(o,r,e[f]),i){var m=u;l+=m[f]}Jh.lastIndex=0;for(var g="",S;(S=Jh.exec(l))!==null;)g+="-"+S[1];var y=Nw(l)+g;return{name:y,styles:l,next:pr}}var Mw=function(e){return e()},Ow=j.useInsertionEffect?j.useInsertionEffect:!1,Wy=Ow||Mw,Uy=j.createContext(typeof HTMLElement<"u"?Ew({key:"css"}):null);Uy.Provider;var Qy=function(e){return j.forwardRef(function(r,o){var i=j.useContext(Uy);return e(r,i,o)})},cp=j.createContext({}),dp={}.hasOwnProperty,Ad="__EMOTION_TYPE_PLEASE_DO_NOT_USE__",jw=function(e,r){var o={};for(var i in r)dp.call(r,i)&&(o[i]=r[i]);return o[Ad]=e,o},Iw=function(e){var r=e.cache,o=e.serialized,i=e.isStringTag;return up(r,o,i),Wy(function(){return Ly(r,o,i)}),null},zw=Qy(function(e,r,o){var i=e.css;typeof i=="string"&&r.registered[i]!==void 0&&(i=r.registered[i]);var l=e[Ad],u=[i],d="";typeof e.className=="string"?d=By(r.registered,u,e.className):e.className!=null&&(d=e.className+" ");var f=su(u,void 0,j.useContext(cp));d+=r.key+"-"+f.name;var m={};for(var g in e)dp.call(e,g)&&g!=="css"&&g!==Ad&&(m[g]=e[g]);return m.className=d,o&&(m.ref=o),j.createElement(j.Fragment,null,j.createElement(Iw,{cache:r,serialized:f,isStringTag:typeof l=="string"}),j.createElement(l,m))}),Dw=zw,Fw=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|fetchpriority|fetchPriority|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,Bw=Dy(function(e){return Fw.test(e)||e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)<91}),Lw=Bw,qw=function(e){return e!=="theme"},Zh=function(e){return typeof e=="string"&&e.charCodeAt(0)>96?Lw:qw},eg=function(e,r,o){var i;if(r){var l=r.shouldForwardProp;i=e.__emotion_forwardProp&&l?function(u){return e.__emotion_forwardProp(u)&&l(u)}:l}return typeof i!="function"&&o&&(i=e.__emotion_forwardProp),i},Ww=function(e){var r=e.cache,o=e.serialized,i=e.isStringTag;return up(r,o,i),Wy(function(){return Ly(r,o,i)}),null},Uw=function e(r,o){var i=r.__emotion_real===r,l=i&&r.__emotion_base||r,u,d;o!==void 0&&(u=o.label,d=o.target);var f=eg(r,o,i),m=f||Zh(l),g=!m("as");return function(){var S=arguments,y=i&&r.__emotion_styles!==void 0?r.__emotion_styles.slice(0):[];if(u!==void 0&&y.push("label:"+u+";"),S[0]==null||S[0].raw===void 0)y.push.apply(y,S);else{var v=S[0];y.push(v[0]);for(var $=S.length,E=1;E<$;E++)y.push(S[E],v[E])}var b=Qy(function(T,N,A){var x=g&&T.as||l,C="",k=[],M=T;if(T.theme==null){M={};for(var I in T)M[I]=T[I];M.theme=j.useContext(cp)}typeof T.className=="string"?C=By(N.registered,k,T.className):T.className!=null&&(C=T.className+" ");var L=su(y.concat(k),N.registered,M);C+=N.key+"-"+L.name,d!==void 0&&(C+=" "+d);var z=g&&f===void 0?Zh(x):m,h={};for(var P in T)g&&P==="as"||z(P)&&(h[P]=T[P]);return h.className=C,A&&(h.ref=A),j.createElement(j.Fragment,null,j.createElement(Ww,{cache:N,serialized:L,isStringTag:typeof x=="string"}),j.createElement(x,h))});return b.displayName=u!==void 0?u:"Styled("+(typeof l=="string"?l:l.displayName||l.name||"Component")+")",b.defaultProps=r.defaultProps,b.__emotion_real=b,b.__emotion_base=l,b.__emotion_styles=y,b.__emotion_forwardProp=f,Object.defineProperty(b,"toString",{value:function(){return"."+d}}),b.withComponent=function(T,N){var A=e(T,Vs({},o,N,{shouldForwardProp:eg(b,N,!0)}));return A.apply(void 0,y)},b}},Qw=["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"],Pd=Uw.bind(null);Qw.forEach(function(e){Pd[e]=Pd(e)});var Vy={exports:{}},sd,tg;function Vw(){if(tg)return sd;tg=1;var e="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";return sd=e,sd}var ud,ng;function Kw(){if(ng)return ud;ng=1;var e=Vw();function r(){}function o(){}return o.resetWarningCache=r,ud=function(){function i(d,f,m,g,S,y){if(y!==e){var v=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw v.name="Invariant Violation",v}}i.isRequired=i;function l(){return i}var u={array:i,bigint:i,bool:i,func:i,number:i,object:i,string:i,symbol:i,any:i,arrayOf:l,element:i,elementType:i,instanceOf:l,node:i,objectOf:l,oneOf:l,oneOfType:l,shape:l,exact:l,checkPropTypes:o,resetWarningCache:r};return u.PropTypes=u,u},ud}Vy.exports=Kw()();var Gw=Vy.exports;const Ma=Ty(Gw);/**
 * @mui/styled-engine v6.4.11
 *
 * @license MIT
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */function Yw(e,r){return Pd(e,r)}function Xw(e,r){Array.isArray(e.__emotion_styles)&&(e.__emotion_styles=r(e.__emotion_styles))}const rg=[];function og(e){return rg[0]=e,su(rg)}var Ky={exports:{}},Ge={};/**
 * @license React
 * react-is.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ag;function Hw(){if(ag)return Ge;ag=1;var e=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),l=Symbol.for("react.profiler"),u=Symbol.for("react.consumer"),d=Symbol.for("react.context"),f=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),g=Symbol.for("react.suspense_list"),S=Symbol.for("react.memo"),y=Symbol.for("react.lazy"),v=Symbol.for("react.view_transition"),$=Symbol.for("react.client.reference");function E(b){if(typeof b=="object"&&b!==null){var T=b.$$typeof;switch(T){case e:switch(b=b.type,b){case o:case l:case i:case m:case g:case v:return b;default:switch(b=b&&b.$$typeof,b){case d:case f:case y:case S:return b;case u:return b;default:return T}}case r:return T}}}return Ge.ContextConsumer=u,Ge.ContextProvider=d,Ge.Element=e,Ge.ForwardRef=f,Ge.Fragment=o,Ge.Lazy=y,Ge.Memo=S,Ge.Portal=r,Ge.Profiler=l,Ge.StrictMode=i,Ge.Suspense=m,Ge.SuspenseList=g,Ge.isContextConsumer=function(b){return E(b)===u},Ge.isContextProvider=function(b){return E(b)===d},Ge.isElement=function(b){return typeof b=="object"&&b!==null&&b.$$typeof===e},Ge.isForwardRef=function(b){return E(b)===f},Ge.isFragment=function(b){return E(b)===o},Ge.isLazy=function(b){return E(b)===y},Ge.isMemo=function(b){return E(b)===S},Ge.isPortal=function(b){return E(b)===r},Ge.isProfiler=function(b){return E(b)===l},Ge.isStrictMode=function(b){return E(b)===i},Ge.isSuspense=function(b){return E(b)===m},Ge.isSuspenseList=function(b){return E(b)===g},Ge.isValidElementType=function(b){return typeof b=="string"||typeof b=="function"||b===o||b===l||b===i||b===m||b===g||typeof b=="object"&&b!==null&&(b.$$typeof===y||b.$$typeof===S||b.$$typeof===d||b.$$typeof===u||b.$$typeof===f||b.$$typeof===$||b.getModuleId!==void 0)},Ge.typeOf=E,Ge}Ky.exports=Hw();var Gy=Ky.exports;function fr(e){if(typeof e!="object"||e===null)return!1;const r=Object.getPrototypeOf(e);return(r===null||r===Object.prototype||Object.getPrototypeOf(r)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}function Yy(e){if(j.isValidElement(e)||Gy.isValidElementType(e)||!fr(e))return e;const r={};return Object.keys(e).forEach(o=>{r[o]=Yy(e[o])}),r}function fn(e,r,o={clone:!0}){const i=o.clone?{...e}:e;return fr(e)&&fr(r)&&Object.keys(r).forEach(l=>{j.isValidElement(r[l])||Gy.isValidElementType(r[l])?i[l]=r[l]:fr(r[l])&&Object.prototype.hasOwnProperty.call(e,l)&&fr(e[l])?i[l]=fn(e[l],r[l],o):o.clone?i[l]=fr(r[l])?Yy(r[l]):r[l]:i[l]=r[l]}),i}const Jw=e=>{const r=Object.keys(e).map(o=>({key:o,val:e[o]}))||[];return r.sort((o,i)=>o.val-i.val),r.reduce((o,i)=>({...o,[i.key]:i.val}),{})};function Zw(e){const{values:r={xs:0,sm:600,md:900,lg:1200,xl:1536},unit:o="px",step:i=5,...l}=e,u=Jw(r),d=Object.keys(u);function f(v){return`@media (min-width:${typeof r[v]=="number"?r[v]:v}${o})`}function m(v){return`@media (max-width:${(typeof r[v]=="number"?r[v]:v)-i/100}${o})`}function g(v,$){const E=d.indexOf($);return`@media (min-width:${typeof r[v]=="number"?r[v]:v}${o}) and (max-width:${(E!==-1&&typeof r[d[E]]=="number"?r[d[E]]:$)-i/100}${o})`}function S(v){return d.indexOf(v)+1<d.length?g(v,d[d.indexOf(v)+1]):f(v)}function y(v){const $=d.indexOf(v);return $===0?f(d[1]):$===d.length-1?m(d[$]):g(v,d[d.indexOf(v)+1]).replace("@media","@media not all and")}return{keys:d,values:u,up:f,down:m,between:g,only:S,not:y,unit:o,...l}}function eS(e,r){if(!e.containerQueries)return r;const o=Object.keys(r).filter(i=>i.startsWith("@container")).sort((i,l)=>{var u,d;const f=/min-width:\s*([0-9.]+)/;return+(((u=i.match(f))==null?void 0:u[1])||0)-+(((d=l.match(f))==null?void 0:d[1])||0)});return o.length?o.reduce((i,l)=>{const u=r[l];return delete i[l],i[l]=u,i},{...r}):r}function tS(e,r){return r==="@"||r.startsWith("@")&&(e.some(o=>r.startsWith(`@${o}`))||!!r.match(/^@\d/))}function nS(e,r){const o=r.match(/^@([^/]+)?\/?(.+)?$/);if(!o)return null;const[,i,l]=o,u=Number.isNaN(+i)?i||0:+i;return e.containerQueries(l).up(u)}function rS(e){const r=(u,d)=>u.replace("@media",d?`@container ${d}`:"@container");function o(u,d){u.up=(...f)=>r(e.breakpoints.up(...f),d),u.down=(...f)=>r(e.breakpoints.down(...f),d),u.between=(...f)=>r(e.breakpoints.between(...f),d),u.only=(...f)=>r(e.breakpoints.only(...f),d),u.not=(...f)=>{const m=r(e.breakpoints.not(...f),d);return m.includes("not all and")?m.replace("not all and ","").replace("min-width:","width<").replace("max-width:","width>").replace("and","or"):m}}const i={},l=u=>(o(i,u),i);return o(l),{...e,containerQueries:l}}const oS={borderRadius:4};function Di(e,r){return r?fn(e,r,{clone:!1}):e}const uu={xs:0,sm:600,md:900,lg:1200,xl:1536},ig={keys:["xs","sm","md","lg","xl"],up:e=>`@media (min-width:${uu[e]}px)`},aS={containerQueries:e=>({up:r=>{let o=typeof r=="number"?r:uu[r]||r;return typeof o=="number"&&(o=`${o}px`),e?`@container ${e} (min-width:${o})`:`@container (min-width:${o})`}})};function qr(e,r,o){const i=e.theme||{};if(Array.isArray(r)){const l=i.breakpoints||ig;return r.reduce((u,d,f)=>(u[l.up(l.keys[f])]=o(r[f]),u),{})}if(typeof r=="object"){const l=i.breakpoints||ig;return Object.keys(r).reduce((u,d)=>{if(tS(l.keys,d)){const f=nS(i.containerQueries?i:aS,d);f&&(u[f]=o(r[d],d))}else if(Object.keys(l.values||uu).includes(d)){const f=l.up(d);u[f]=o(r[d],d)}else{const f=d;u[f]=r[f]}return u},{})}return o(r)}function iS(e={}){var r;return((r=e.keys)==null?void 0:r.reduce((o,i)=>{const l=e.up(i);return o[l]={},o},{}))||{}}function lS(e,r){return e.reduce((o,i)=>{const l=o[i];return(!l||Object.keys(l).length===0)&&delete o[i],o},r)}function xe(e){if(typeof e!="string")throw new Error(Xo(7));return e.charAt(0).toUpperCase()+e.slice(1)}function cu(e,r,o=!0){if(!r||typeof r!="string")return null;if(e&&e.vars&&o){const i=`vars.${r}`.split(".").reduce((l,u)=>l&&l[u]?l[u]:null,e);if(i!=null)return i}return r.split(".").reduce((i,l)=>i&&i[l]!=null?i[l]:null,e)}function Gs(e,r,o,i=o){let l;return typeof e=="function"?l=e(o):Array.isArray(e)?l=e[o]||i:l=cu(e,o)||i,r&&(l=r(l,i,e)),l}function mt(e){const{prop:r,cssProperty:o=e.prop,themeKey:i,transform:l}=e,u=d=>{if(d[r]==null)return null;const f=d[r],m=d.theme,g=cu(m,i)||{};return qr(d,f,S=>{let y=Gs(g,l,S);return S===y&&typeof S=="string"&&(y=Gs(g,l,`${r}${S==="default"?"":xe(S)}`,S)),o===!1?y:{[o]:y}})};return u.propTypes={},u.filterProps=[r],u}function sS(e){const r={};return o=>(r[o]===void 0&&(r[o]=e(o)),r[o])}const uS={m:"margin",p:"padding"},cS={t:"Top",r:"Right",b:"Bottom",l:"Left",x:["Left","Right"],y:["Top","Bottom"]},lg={marginX:"mx",marginY:"my",paddingX:"px",paddingY:"py"},dS=sS(e=>{if(e.length>2)if(lg[e])e=lg[e];else return[e];const[r,o]=e.split(""),i=uS[r],l=cS[o]||"";return Array.isArray(l)?l.map(u=>i+u):[i+l]}),pp=["m","mt","mr","mb","ml","mx","my","margin","marginTop","marginRight","marginBottom","marginLeft","marginX","marginY","marginInline","marginInlineStart","marginInlineEnd","marginBlock","marginBlockStart","marginBlockEnd"],fp=["p","pt","pr","pb","pl","px","py","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","paddingX","paddingY","paddingInline","paddingInlineStart","paddingInlineEnd","paddingBlock","paddingBlockStart","paddingBlockEnd"];[...pp,...fp];function ll(e,r,o,i){const l=cu(e,r,!0)??o;return typeof l=="number"||typeof l=="string"?u=>typeof u=="string"?u:typeof l=="string"?`calc(${u} * ${l})`:l*u:Array.isArray(l)?u=>{if(typeof u=="string")return u;const d=Math.abs(u),f=l[d];return u>=0?f:typeof f=="number"?-f:`-${f}`}:typeof l=="function"?l:()=>{}}function mp(e){return ll(e,"spacing",8)}function sl(e,r){return typeof r=="string"||r==null?r:e(r)}function pS(e,r){return o=>e.reduce((i,l)=>(i[l]=sl(r,o),i),{})}function fS(e,r,o,i){if(!r.includes(o))return null;const l=dS(o),u=pS(l,i),d=e[o];return qr(e,d,u)}function Xy(e,r){const o=mp(e.theme);return Object.keys(e).map(i=>fS(e,r,i,o)).reduce(Di,{})}function ut(e){return Xy(e,pp)}ut.propTypes={};ut.filterProps=pp;function ct(e){return Xy(e,fp)}ct.propTypes={};ct.filterProps=fp;function Hy(e=8,r=mp({spacing:e})){if(e.mui)return e;const o=(...i)=>(i.length===0?[1]:i).map(l=>{const u=r(l);return typeof u=="number"?`${u}px`:u}).join(" ");return o.mui=!0,o}function du(...e){const r=e.reduce((i,l)=>(l.filterProps.forEach(u=>{i[u]=l}),i),{}),o=i=>Object.keys(i).reduce((l,u)=>r[u]?Di(l,r[u](i)):l,{});return o.propTypes={},o.filterProps=e.reduce((i,l)=>i.concat(l.filterProps),[]),o}function Pn(e){return typeof e!="number"?e:`${e}px solid`}function jn(e,r){return mt({prop:e,themeKey:"borders",transform:r})}const mS=jn("border",Pn),hS=jn("borderTop",Pn),gS=jn("borderRight",Pn),yS=jn("borderBottom",Pn),vS=jn("borderLeft",Pn),bS=jn("borderColor"),xS=jn("borderTopColor"),wS=jn("borderRightColor"),SS=jn("borderBottomColor"),kS=jn("borderLeftColor"),CS=jn("outline",Pn),$S=jn("outlineColor"),pu=e=>{if(e.borderRadius!==void 0&&e.borderRadius!==null){const r=ll(e.theme,"shape.borderRadius",4),o=i=>({borderRadius:sl(r,i)});return qr(e,e.borderRadius,o)}return null};pu.propTypes={};pu.filterProps=["borderRadius"];du(mS,hS,gS,yS,vS,bS,xS,wS,SS,kS,pu,CS,$S);const fu=e=>{if(e.gap!==void 0&&e.gap!==null){const r=ll(e.theme,"spacing",8),o=i=>({gap:sl(r,i)});return qr(e,e.gap,o)}return null};fu.propTypes={};fu.filterProps=["gap"];const mu=e=>{if(e.columnGap!==void 0&&e.columnGap!==null){const r=ll(e.theme,"spacing",8),o=i=>({columnGap:sl(r,i)});return qr(e,e.columnGap,o)}return null};mu.propTypes={};mu.filterProps=["columnGap"];const hu=e=>{if(e.rowGap!==void 0&&e.rowGap!==null){const r=ll(e.theme,"spacing",8),o=i=>({rowGap:sl(r,i)});return qr(e,e.rowGap,o)}return null};hu.propTypes={};hu.filterProps=["rowGap"];const ES=mt({prop:"gridColumn"}),TS=mt({prop:"gridRow"}),NS=mt({prop:"gridAutoFlow"}),AS=mt({prop:"gridAutoColumns"}),PS=mt({prop:"gridAutoRows"}),_S=mt({prop:"gridTemplateColumns"}),RS=mt({prop:"gridTemplateRows"}),MS=mt({prop:"gridTemplateAreas"}),OS=mt({prop:"gridArea"});du(fu,mu,hu,ES,TS,NS,AS,PS,_S,RS,MS,OS);function Oa(e,r){return r==="grey"?r:e}const jS=mt({prop:"color",themeKey:"palette",transform:Oa}),IS=mt({prop:"bgcolor",cssProperty:"backgroundColor",themeKey:"palette",transform:Oa}),zS=mt({prop:"backgroundColor",themeKey:"palette",transform:Oa});du(jS,IS,zS);function dn(e){return e<=1&&e!==0?`${e*100}%`:e}const DS=mt({prop:"width",transform:dn}),hp=e=>{if(e.maxWidth!==void 0&&e.maxWidth!==null){const r=o=>{var i,l,u,d,f;const m=((u=(l=(i=e.theme)==null?void 0:i.breakpoints)==null?void 0:l.values)==null?void 0:u[o])||uu[o];return m?((f=(d=e.theme)==null?void 0:d.breakpoints)==null?void 0:f.unit)!=="px"?{maxWidth:`${m}${e.theme.breakpoints.unit}`}:{maxWidth:m}:{maxWidth:dn(o)}};return qr(e,e.maxWidth,r)}return null};hp.filterProps=["maxWidth"];const FS=mt({prop:"minWidth",transform:dn}),BS=mt({prop:"height",transform:dn}),LS=mt({prop:"maxHeight",transform:dn}),qS=mt({prop:"minHeight",transform:dn});mt({prop:"size",cssProperty:"width",transform:dn});mt({prop:"size",cssProperty:"height",transform:dn});const WS=mt({prop:"boxSizing"});du(DS,hp,FS,BS,LS,qS,WS);const ul={border:{themeKey:"borders",transform:Pn},borderTop:{themeKey:"borders",transform:Pn},borderRight:{themeKey:"borders",transform:Pn},borderBottom:{themeKey:"borders",transform:Pn},borderLeft:{themeKey:"borders",transform:Pn},borderColor:{themeKey:"palette"},borderTopColor:{themeKey:"palette"},borderRightColor:{themeKey:"palette"},borderBottomColor:{themeKey:"palette"},borderLeftColor:{themeKey:"palette"},outline:{themeKey:"borders",transform:Pn},outlineColor:{themeKey:"palette"},borderRadius:{themeKey:"shape.borderRadius",style:pu},color:{themeKey:"palette",transform:Oa},bgcolor:{themeKey:"palette",cssProperty:"backgroundColor",transform:Oa},backgroundColor:{themeKey:"palette",transform:Oa},p:{style:ct},pt:{style:ct},pr:{style:ct},pb:{style:ct},pl:{style:ct},px:{style:ct},py:{style:ct},padding:{style:ct},paddingTop:{style:ct},paddingRight:{style:ct},paddingBottom:{style:ct},paddingLeft:{style:ct},paddingX:{style:ct},paddingY:{style:ct},paddingInline:{style:ct},paddingInlineStart:{style:ct},paddingInlineEnd:{style:ct},paddingBlock:{style:ct},paddingBlockStart:{style:ct},paddingBlockEnd:{style:ct},m:{style:ut},mt:{style:ut},mr:{style:ut},mb:{style:ut},ml:{style:ut},mx:{style:ut},my:{style:ut},margin:{style:ut},marginTop:{style:ut},marginRight:{style:ut},marginBottom:{style:ut},marginLeft:{style:ut},marginX:{style:ut},marginY:{style:ut},marginInline:{style:ut},marginInlineStart:{style:ut},marginInlineEnd:{style:ut},marginBlock:{style:ut},marginBlockStart:{style:ut},marginBlockEnd:{style:ut},displayPrint:{cssProperty:!1,transform:e=>({"@media print":{display:e}})},display:{},overflow:{},textOverflow:{},visibility:{},whiteSpace:{},flexBasis:{},flexDirection:{},flexWrap:{},justifyContent:{},alignItems:{},alignContent:{},order:{},flex:{},flexGrow:{},flexShrink:{},alignSelf:{},justifyItems:{},justifySelf:{},gap:{style:fu},rowGap:{style:hu},columnGap:{style:mu},gridColumn:{},gridRow:{},gridAutoFlow:{},gridAutoColumns:{},gridAutoRows:{},gridTemplateColumns:{},gridTemplateRows:{},gridTemplateAreas:{},gridArea:{},position:{},zIndex:{themeKey:"zIndex"},top:{},right:{},bottom:{},left:{},boxShadow:{themeKey:"shadows"},width:{transform:dn},maxWidth:{style:hp},minWidth:{transform:dn},height:{transform:dn},maxHeight:{transform:dn},minHeight:{transform:dn},boxSizing:{},font:{themeKey:"font"},fontFamily:{themeKey:"typography"},fontSize:{themeKey:"typography"},fontStyle:{themeKey:"typography"},fontWeight:{themeKey:"typography"},letterSpacing:{},textTransform:{},lineHeight:{},textAlign:{},typography:{cssProperty:!1,themeKey:"typography"}};function US(...e){const r=e.reduce((i,l)=>i.concat(Object.keys(l)),[]),o=new Set(r);return e.every(i=>o.size===Object.keys(i).length)}function QS(e,r){return typeof e=="function"?e(r):e}function VS(){function e(o,i,l,u){const d={[o]:i,theme:l},f=u[o];if(!f)return{[o]:i};const{cssProperty:m=o,themeKey:g,transform:S,style:y}=f;if(i==null)return null;if(g==="typography"&&i==="inherit")return{[o]:i};const v=cu(l,g)||{};return y?y(d):qr(d,i,$=>{let E=Gs(v,S,$);return $===E&&typeof $=="string"&&(E=Gs(v,S,`${o}${$==="default"?"":xe($)}`,$)),m===!1?E:{[m]:E}})}function r(o){const{sx:i,theme:l={}}=o||{};if(!i)return null;const u=l.unstable_sxConfig??ul;function d(f){let m=f;if(typeof f=="function")m=f(l);else if(typeof f!="object")return f;if(!m)return null;const g=iS(l.breakpoints),S=Object.keys(g);let y=g;return Object.keys(m).forEach(v=>{const $=QS(m[v],l);if($!=null)if(typeof $=="object")if(u[v])y=Di(y,e(v,$,l,u));else{const E=qr({theme:l},$,b=>({[v]:b}));US(E,$)?y[v]=r({sx:$,theme:l}):y=Di(y,E)}else y=Di(y,e(v,$,l,u))}),eS(l,lS(S,y))}return Array.isArray(i)?i.map(d):d(i)}return r}const za=VS();za.filterProps=["sx"];function KS(e,r){var o;const i=this;if(i.vars){if(!((o=i.colorSchemes)!=null&&o[e])||typeof i.getColorSchemeSelector!="function")return{};let l=i.getColorSchemeSelector(e);return l==="&"?r:((l.includes("data-")||l.includes("."))&&(l=`*:where(${l.replace(/\s*&$/,"")}) &`),{[l]:r})}return i.palette.mode===e?r:{}}function gp(e={},...r){const{breakpoints:o={},palette:i={},spacing:l,shape:u={},...d}=e,f=Zw(o),m=Hy(l);let g=fn({breakpoints:f,direction:"ltr",components:{},palette:{mode:"light",...i},spacing:m,shape:{...oS,...u}},d);return g=rS(g),g.applyStyles=KS,g=r.reduce((S,y)=>fn(S,y),g),g.unstable_sxConfig={...ul,...d==null?void 0:d.unstable_sxConfig},g.unstable_sx=function(S){return za({sx:S,theme:this})},g}function GS(e){return Object.keys(e).length===0}function YS(e=null){const r=j.useContext(cp);return!r||GS(r)?e:r}const XS=gp();function HS(e=XS){return YS(e)}const sg=e=>e,JS=()=>{let e=sg;return{configure(r){e=r},generate(r){return e(r)},reset(){e=sg}}},ZS=JS();function Jy(e){var r,o,i="";if(typeof e=="string"||typeof e=="number")i+=e;else if(typeof e=="object")if(Array.isArray(e)){var l=e.length;for(r=0;r<l;r++)e[r]&&(o=Jy(e[r]))&&(i&&(i+=" "),i+=o)}else for(o in e)e[o]&&(i&&(i+=" "),i+=o);return i}function ze(){for(var e,r,o=0,i="",l=arguments.length;o<l;o++)(e=arguments[o])&&(r=Jy(e))&&(i&&(i+=" "),i+=r);return i}const ek={active:"active",checked:"checked",completed:"completed",disabled:"disabled",error:"error",expanded:"expanded",focused:"focused",focusVisible:"focusVisible",open:"open",readOnly:"readOnly",required:"required",selected:"selected"};function Rt(e,r,o="Mui"){const i=ek[r];return i?`${o}-${i}`:`${ZS.generate(e)}-${r}`}function ht(e,r,o="Mui"){const i={};return r.forEach(l=>{i[l]=Rt(e,l,o)}),i}function Zy(e){const{variants:r,...o}=e,i={variants:r,style:og(o),isProcessed:!0};return i.style===o||r&&r.forEach(l=>{typeof l.style!="function"&&(l.style=og(l.style))}),i}const tk=gp();function cd(e){return e!=="ownerState"&&e!=="theme"&&e!=="sx"&&e!=="as"}function nk(e){return e?(r,o)=>o[e]:null}function rk(e,r,o){e.theme=ik(e.theme)?o:e.theme[r]||e.theme}function js(e,r){const o=typeof r=="function"?r(e):r;if(Array.isArray(o))return o.flatMap(i=>js(e,i));if(Array.isArray(o==null?void 0:o.variants)){let i;if(o.isProcessed)i=o.style;else{const{variants:l,...u}=o;i=u}return e0(e,o.variants,[i])}return o!=null&&o.isProcessed?o.style:o}function e0(e,r,o=[]){var i;let l;e:for(let u=0;u<r.length;u+=1){const d=r[u];if(typeof d.props=="function"){if(l??(l={...e,...e.ownerState,ownerState:e.ownerState}),!d.props(l))continue}else for(const f in d.props)if(e[f]!==d.props[f]&&((i=e.ownerState)==null?void 0:i[f])!==d.props[f])continue e;typeof d.style=="function"?(l??(l={...e,...e.ownerState,ownerState:e.ownerState}),o.push(d.style(l))):o.push(d.style)}return o}function ok(e={}){const{themeId:r,defaultTheme:o=tk,rootShouldForwardProp:i=cd,slotShouldForwardProp:l=cd}=e;function u(d){rk(d,r,o)}return(d,f={})=>{Xw(d,C=>C.filter(k=>k!==za));const{name:m,slot:g,skipVariantsResolver:S,skipSx:y,overridesResolver:v=nk(sk(g)),...$}=f,E=S!==void 0?S:g&&g!=="Root"&&g!=="root"||!1,b=y||!1;let T=cd;g==="Root"||g==="root"?T=i:g?T=l:lk(d)&&(T=void 0);const N=Yw(d,{shouldForwardProp:T,label:ak(),...$}),A=C=>{if(C.__emotion_real===C)return C;if(typeof C=="function")return function(k){return js(k,C)};if(fr(C)){const k=Zy(C);return k.variants?function(M){return js(M,k)}:k.style}return C},x=(...C)=>{const k=[],M=C.map(A),I=[];if(k.push(u),m&&v&&I.push(function(h){var P,_;const R=(_=(P=h.theme.components)==null?void 0:P[m])==null?void 0:_.styleOverrides;if(!R)return null;const F={};for(const G in R)F[G]=js(h,R[G]);return v(h,F)}),m&&!E&&I.push(function(h){var P,_;const R=h.theme,F=(_=(P=R==null?void 0:R.components)==null?void 0:P[m])==null?void 0:_.variants;return F?e0(h,F):null}),b||I.push(za),Array.isArray(M[0])){const h=M.shift(),P=new Array(k.length).fill(""),_=new Array(I.length).fill("");let R;R=[...P,...h,..._],R.raw=[...P,...h.raw,..._],k.unshift(R)}const L=[...k,...M,...I],z=N(...L);return d.muiName&&(z.muiName=d.muiName),z};return N.withConfig&&(x.withConfig=N.withConfig),x}}function ak(e,r){let o;return o}function ik(e){for(const r in e)return!1;return!0}function lk(e){return typeof e=="string"&&e.charCodeAt(0)>96}function sk(e){return e&&e.charAt(0).toLowerCase()+e.slice(1)}function _d(e,r){const o={...r};for(const i in e)if(Object.prototype.hasOwnProperty.call(e,i)){const l=i;if(l==="components"||l==="slots")o[l]={...e[l],...o[l]};else if(l==="componentsProps"||l==="slotProps"){const u=e[l],d=r[l];if(!d)o[l]=u||{};else if(!u)o[l]=d;else{o[l]={...d};for(const f in u)if(Object.prototype.hasOwnProperty.call(u,f)){const m=f;o[l][m]=_d(u[m],d[m])}}}else o[l]===void 0&&(o[l]=e[l])}return o}const Zi=typeof window<"u"?j.useLayoutEffect:j.useEffect;function uk(e,r=Number.MIN_SAFE_INTEGER,o=Number.MAX_SAFE_INTEGER){return Math.max(r,Math.min(e,o))}function yp(e,r=0,o=1){return uk(e,r,o)}function ck(e){e=e.slice(1);const r=new RegExp(`.{1,${e.length>=6?2:1}}`,"g");let o=e.match(r);return o&&o[0].length===1&&(o=o.map(i=>i+i)),o?`rgb${o.length===4?"a":""}(${o.map((i,l)=>l<3?parseInt(i,16):Math.round(parseInt(i,16)/255*1e3)/1e3).join(", ")})`:""}function bo(e){if(e.type)return e;if(e.charAt(0)==="#")return bo(ck(e));const r=e.indexOf("("),o=e.substring(0,r);if(!["rgb","rgba","hsl","hsla","color"].includes(o))throw new Error(Xo(9,e));let i=e.substring(r+1,e.length-1),l;if(o==="color"){if(i=i.split(" "),l=i.shift(),i.length===4&&i[3].charAt(0)==="/"&&(i[3]=i[3].slice(1)),!["srgb","display-p3","a98-rgb","prophoto-rgb","rec-2020"].includes(l))throw new Error(Xo(10,l))}else i=i.split(",");return i=i.map(u=>parseFloat(u)),{type:o,values:i,colorSpace:l}}const dk=e=>{const r=bo(e);return r.values.slice(0,3).map((o,i)=>r.type.includes("hsl")&&i!==0?`${o}%`:o).join(" ")},Ri=(e,r)=>{try{return dk(e)}catch{return e}};function gu(e){const{type:r,colorSpace:o}=e;let{values:i}=e;return r.includes("rgb")?i=i.map((l,u)=>u<3?parseInt(l,10):l):r.includes("hsl")&&(i[1]=`${i[1]}%`,i[2]=`${i[2]}%`),r.includes("color")?i=`${o} ${i.join(" ")}`:i=`${i.join(", ")}`,`${r}(${i})`}function t0(e){e=bo(e);const{values:r}=e,o=r[0],i=r[1]/100,l=r[2]/100,u=i*Math.min(l,1-l),d=(g,S=(g+o/30)%12)=>l-u*Math.max(Math.min(S-3,9-S,1),-1);let f="rgb";const m=[Math.round(d(0)*255),Math.round(d(8)*255),Math.round(d(4)*255)];return e.type==="hsla"&&(f+="a",m.push(r[3])),gu({type:f,values:m})}function Rd(e){e=bo(e);let r=e.type==="hsl"||e.type==="hsla"?bo(t0(e)).values:e.values;return r=r.map(o=>(e.type!=="color"&&(o/=255),o<=.03928?o/12.92:((o+.055)/1.055)**2.4)),Number((.2126*r[0]+.7152*r[1]+.0722*r[2]).toFixed(3))}function pk(e,r){const o=Rd(e),i=Rd(r);return(Math.max(o,i)+.05)/(Math.min(o,i)+.05)}function Ct(e,r){return e=bo(e),r=yp(r),(e.type==="rgb"||e.type==="hsl")&&(e.type+="a"),e.type==="color"?e.values[3]=`/${r}`:e.values[3]=r,gu(e)}function Ts(e,r,o){try{return Ct(e,r)}catch{return e}}function vp(e,r){if(e=bo(e),r=yp(r),e.type.includes("hsl"))e.values[2]*=1-r;else if(e.type.includes("rgb")||e.type.includes("color"))for(let o=0;o<3;o+=1)e.values[o]*=1-r;return gu(e)}function Xe(e,r,o){try{return vp(e,r)}catch{return e}}function bp(e,r){if(e=bo(e),r=yp(r),e.type.includes("hsl"))e.values[2]+=(100-e.values[2])*r;else if(e.type.includes("rgb"))for(let o=0;o<3;o+=1)e.values[o]+=(255-e.values[o])*r;else if(e.type.includes("color"))for(let o=0;o<3;o+=1)e.values[o]+=(1-e.values[o])*r;return gu(e)}function He(e,r,o){try{return bp(e,r)}catch{return e}}function fk(e,r=.15){return Rd(e)>.5?vp(e,r):bp(e,r)}function Ns(e,r,o){try{return fk(e,r)}catch{return e}}function xp(e,r){return()=>null}function Wr(e){return e&&e.ownerDocument||document}function Md(e,r){typeof e=="function"?e(r):e&&(e.current=r)}let ug=0;function mk(e){const[r,o]=j.useState(e),i=e||r;return j.useEffect(()=>{r==null&&(ug+=1,o(`mui-${ug}`))},[r]),i}const hk={...wb},cg=hk.useId;function wp(e){if(cg!==void 0){const r=cg();return e??r}return mk(e)}function mr(e){const r=j.useRef(e);return Zi(()=>{r.current=e}),j.useRef((...o)=>(0,r.current)(...o)).current}function er(...e){const r=j.useRef(void 0),o=j.useCallback(i=>{const l=e.map(u=>{if(u==null)return null;if(typeof u=="function"){const d=u,f=d(i);return typeof f=="function"?f:()=>{d(null)}}return u.current=i,()=>{u.current=null}});return()=>{l.forEach(u=>u==null?void 0:u())}},e);return j.useMemo(()=>e.every(i=>i==null)?null:i=>{r.current&&(r.current(),r.current=void 0),i!=null&&(r.current=o(i))},e)}function dg(){return null}dg.isRequired=dg;function qt(e,r,o=void 0){const i={};for(const l in e){const u=e[l];let d="",f=!0;for(let m=0;m<u.length;m+=1){const g=u[m];g&&(d+=(f===!0?"":" ")+r(g),f=!1,o&&o[g]&&(d+=" "+o[g]))}i[l]=d}return i}function gk(e){return typeof e=="string"}function n0(e,r,o){return e===void 0||gk(e)?r:{...r,ownerState:{...r.ownerState,...o}}}function r0(e){var r,o,i="";if(typeof e=="string"||typeof e=="number")i+=e;else if(typeof e=="object")if(Array.isArray(e)){var l=e.length;for(r=0;r<l;r++)e[r]&&(o=r0(e[r]))&&(i&&(i+=" "),i+=o)}else for(o in e)e[o]&&(i&&(i+=" "),i+=o);return i}function pg(){for(var e,r,o=0,i="",l=arguments.length;o<l;o++)(e=arguments[o])&&(r=r0(e))&&(i&&(i+=" "),i+=r);return i}function o0(e,r=[]){if(e===void 0)return{};const o={};return Object.keys(e).filter(i=>i.match(/^on[A-Z]/)&&typeof e[i]=="function"&&!r.includes(i)).forEach(i=>{o[i]=e[i]}),o}function fg(e){if(e===void 0)return{};const r={};return Object.keys(e).filter(o=>!(o.match(/^on[A-Z]/)&&typeof e[o]=="function")).forEach(o=>{r[o]=e[o]}),r}function a0(e){const{getSlotProps:r,additionalProps:o,externalSlotProps:i,externalForwardedProps:l,className:u}=e;if(!r){const $=pg(o==null?void 0:o.className,u,l==null?void 0:l.className,i==null?void 0:i.className),E={...o==null?void 0:o.style,...l==null?void 0:l.style,...i==null?void 0:i.style},b={...o,...l,...i};return $.length>0&&(b.className=$),Object.keys(E).length>0&&(b.style=E),{props:b,internalRef:void 0}}const d=o0({...l,...i}),f=fg(i),m=fg(l),g=r(d),S=pg(g==null?void 0:g.className,o==null?void 0:o.className,u,l==null?void 0:l.className,i==null?void 0:i.className),y={...g==null?void 0:g.style,...o==null?void 0:o.style,...l==null?void 0:l.style,...i==null?void 0:i.style},v={...g,...o,...m,...f};return S.length>0&&(v.className=S),Object.keys(y).length>0&&(v.style=y),{props:v,internalRef:g.ref}}function i0(e,r,o){return typeof e=="function"?e(r,o):e}function Sp(e){var r;return parseInt(j.version,10)>=19?((r=e==null?void 0:e.props)==null?void 0:r.ref)||null:(e==null?void 0:e.ref)||null}const yk=j.createContext(void 0);function vk(e){const{theme:r,name:o,props:i}=e;if(!r||!r.components||!r.components[o])return i;const l=r.components[o];return l.defaultProps?_d(l.defaultProps,i):!l.styleOverrides&&!l.variants?_d(l,i):i}function bk({props:e,name:r}){const o=j.useContext(yk);return vk({props:e,name:r,theme:{components:o}})}const mg={theme:void 0};function xk(e){let r,o;return function(i){let l=r;return(l===void 0||i.theme!==o)&&(mg.theme=i.theme,l=Zy(e(mg)),r=l,o=i.theme),l}}function wk(e=""){function r(...o){if(!o.length)return"";const i=o[0];return typeof i=="string"&&!i.match(/(#|\(|\)|(-?(\d*\.)?\d+)(px|em|%|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc))|^(-?(\d*\.)?\d+)$|(\d+ \d+ \d+)/)?`, var(--${e?`${e}-`:""}${i}${r(...o.slice(1))})`:`, ${i}`}return(o,...i)=>`var(--${e?`${e}-`:""}${o}${r(...i)})`}const hg=(e,r,o,i=[])=>{let l=e;r.forEach((u,d)=>{d===r.length-1?Array.isArray(l)?l[Number(u)]=o:l&&typeof l=="object"&&(l[u]=o):l&&typeof l=="object"&&(l[u]||(l[u]=i.includes(u)?[]:{}),l=l[u])})},Sk=(e,r,o)=>{function i(l,u=[],d=[]){Object.entries(l).forEach(([f,m])=>{(!o||o&&!o([...u,f]))&&m!=null&&(typeof m=="object"&&Object.keys(m).length>0?i(m,[...u,f],Array.isArray(m)?[...d,f]:d):r([...u,f],m,d))})}i(e)},kk=(e,r)=>typeof r=="number"?["lineHeight","fontWeight","opacity","zIndex"].some(o=>e.includes(o))||e[e.length-1].toLowerCase().includes("opacity")?r:`${r}px`:r;function dd(e,r){const{prefix:o,shouldSkipGeneratingVar:i}=r||{},l={},u={},d={};return Sk(e,(f,m,g)=>{if((typeof m=="string"||typeof m=="number")&&(!i||!i(f,m))){const S=`--${o?`${o}-`:""}${f.join("-")}`,y=kk(f,m);Object.assign(l,{[S]:y}),hg(u,f,`var(${S})`,g),hg(d,f,`var(${S}, ${y})`,g)}},f=>f[0]==="vars"),{css:l,vars:u,varsWithDefaults:d}}function Ck(e,r={}){const{getSelector:o=T,disableCssColorScheme:i,colorSchemeSelector:l}=r,{colorSchemes:u={},components:d,defaultColorScheme:f="light",...m}=e,{vars:g,css:S,varsWithDefaults:y}=dd(m,r);let v=y;const $={},{[f]:E,...b}=u;if(Object.entries(b||{}).forEach(([N,A])=>{const{vars:x,css:C,varsWithDefaults:k}=dd(A,r);v=fn(v,k),$[N]={css:C,vars:x}}),E){const{css:N,vars:A,varsWithDefaults:x}=dd(E,r);v=fn(v,x),$[f]={css:N,vars:A}}function T(N,A){var x,C;let k=l;if(l==="class"&&(k=".%s"),l==="data"&&(k="[data-%s]"),l!=null&&l.startsWith("data-")&&!l.includes("%s")&&(k=`[${l}="%s"]`),N){if(k==="media")return e.defaultColorScheme===N?":root":{[`@media (prefers-color-scheme: ${((C=(x=u[N])==null?void 0:x.palette)==null?void 0:C.mode)||N})`]:{":root":A}};if(k)return e.defaultColorScheme===N?`:root, ${k.replace("%s",String(N))}`:k.replace("%s",String(N))}return":root"}return{vars:v,generateThemeVars:()=>{let N={...g};return Object.entries($).forEach(([,{vars:A}])=>{N=fn(N,A)}),N},generateStyleSheets:()=>{var N,A;const x=[],C=e.defaultColorScheme||"light";function k(L,z){Object.keys(z).length&&x.push(typeof L=="string"?{[L]:{...z}}:L)}k(o(void 0,{...S}),S);const{[C]:M,...I}=$;if(M){const{css:L}=M,z=(A=(N=u[C])==null?void 0:N.palette)==null?void 0:A.mode,h=!i&&z?{colorScheme:z,...L}:{...L};k(o(C,{...h}),h)}return Object.entries(I).forEach(([L,{css:z}])=>{var h,P;const _=(P=(h=u[L])==null?void 0:h.palette)==null?void 0:P.mode,R=!i&&_?{colorScheme:_,...z}:{...z};k(o(L,{...R}),R)}),x}}}function $k(e){return function(r){return e==="media"?`@media (prefers-color-scheme: ${r})`:e?e.startsWith("data-")&&!e.includes("%s")?`[${e}="${r}"] &`:e==="class"?`.${r} &`:e==="data"?`[data-${r}] &`:`${e.replace("%s",r)} &`:"&"}}function l0(){return{text:{primary:"rgba(0, 0, 0, 0.87)",secondary:"rgba(0, 0, 0, 0.6)",disabled:"rgba(0, 0, 0, 0.38)"},divider:"rgba(0, 0, 0, 0.12)",background:{paper:Yi.white,default:Yi.white},action:{active:"rgba(0, 0, 0, 0.54)",hover:"rgba(0, 0, 0, 0.04)",hoverOpacity:.04,selected:"rgba(0, 0, 0, 0.08)",selectedOpacity:.08,disabled:"rgba(0, 0, 0, 0.26)",disabledBackground:"rgba(0, 0, 0, 0.12)",disabledOpacity:.38,focus:"rgba(0, 0, 0, 0.12)",focusOpacity:.12,activatedOpacity:.12}}}const Ek=l0();function s0(){return{text:{primary:Yi.white,secondary:"rgba(255, 255, 255, 0.7)",disabled:"rgba(255, 255, 255, 0.5)",icon:"rgba(255, 255, 255, 0.5)"},divider:"rgba(255, 255, 255, 0.12)",background:{paper:"#121212",default:"#121212"},action:{active:Yi.white,hover:"rgba(255, 255, 255, 0.08)",hoverOpacity:.08,selected:"rgba(255, 255, 255, 0.16)",selectedOpacity:.16,disabled:"rgba(255, 255, 255, 0.3)",disabledBackground:"rgba(255, 255, 255, 0.12)",disabledOpacity:.38,focus:"rgba(255, 255, 255, 0.12)",focusOpacity:.12,activatedOpacity:.24}}}const gg=s0();function yg(e,r,o,i){const l=i.light||i,u=i.dark||i*1.5;e[r]||(e.hasOwnProperty(o)?e[r]=e[o]:r==="light"?e.light=bp(e.main,l):r==="dark"&&(e.dark=vp(e.main,u)))}function Tk(e="light"){return e==="dark"?{main:$a[200],light:$a[50],dark:$a[400]}:{main:$a[700],light:$a[400],dark:$a[800]}}function Nk(e="light"){return e==="dark"?{main:Ca[200],light:Ca[50],dark:Ca[400]}:{main:Ca[500],light:Ca[300],dark:Ca[700]}}function Ak(e="light"){return e==="dark"?{main:ka[500],light:ka[300],dark:ka[700]}:{main:ka[700],light:ka[400],dark:ka[800]}}function Pk(e="light"){return e==="dark"?{main:Ea[400],light:Ea[300],dark:Ea[700]}:{main:Ea[700],light:Ea[500],dark:Ea[900]}}function _k(e="light"){return e==="dark"?{main:Ta[400],light:Ta[300],dark:Ta[700]}:{main:Ta[800],light:Ta[500],dark:Ta[900]}}function Rk(e="light"){return e==="dark"?{main:Ni[400],light:Ni[300],dark:Ni[700]}:{main:"#ed6c02",light:Ni[500],dark:Ni[900]}}function kp(e){const{mode:r="light",contrastThreshold:o=3,tonalOffset:i=.2,...l}=e,u=e.primary||Tk(r),d=e.secondary||Nk(r),f=e.error||Ak(r),m=e.info||Pk(r),g=e.success||_k(r),S=e.warning||Rk(r);function y(E){return pk(E,gg.text.primary)>=o?gg.text.primary:Ek.text.primary}const v=({color:E,name:b,mainShade:T=500,lightShade:N=300,darkShade:A=700})=>{if(E={...E},!E.main&&E[T]&&(E.main=E[T]),!E.hasOwnProperty("main"))throw new Error(Xo(11,b?` (${b})`:"",T));if(typeof E.main!="string")throw new Error(Xo(12,b?` (${b})`:"",JSON.stringify(E.main)));return yg(E,"light",N,i),yg(E,"dark",A,i),E.contrastText||(E.contrastText=y(E.main)),E};let $;return r==="light"?$=l0():r==="dark"&&($=s0()),fn({common:{...Yi},mode:r,primary:v({color:u,name:"primary"}),secondary:v({color:d,name:"secondary",mainShade:"A400",lightShade:"A200",darkShade:"A700"}),error:v({color:f,name:"error"}),warning:v({color:S,name:"warning"}),info:v({color:m,name:"info"}),success:v({color:g,name:"success"}),grey:Hx,contrastThreshold:o,getContrastText:y,augmentColor:v,tonalOffset:i,...$},l)}function Mk(e){const r={};return Object.entries(e).forEach(o=>{const[i,l]=o;typeof l=="object"&&(r[i]=`${l.fontStyle?`${l.fontStyle} `:""}${l.fontVariant?`${l.fontVariant} `:""}${l.fontWeight?`${l.fontWeight} `:""}${l.fontStretch?`${l.fontStretch} `:""}${l.fontSize||""}${l.lineHeight?`/${l.lineHeight} `:""}${l.fontFamily||""}`)}),r}function Ok(e,r){return{toolbar:{minHeight:56,[e.up("xs")]:{"@media (orientation: landscape)":{minHeight:48}},[e.up("sm")]:{minHeight:64}},...r}}function jk(e){return Math.round(e*1e5)/1e5}const vg={textTransform:"uppercase"},bg='"Roboto", "Helvetica", "Arial", sans-serif';function Ik(e,r){const{fontFamily:o=bg,fontSize:i=14,fontWeightLight:l=300,fontWeightRegular:u=400,fontWeightMedium:d=500,fontWeightBold:f=700,htmlFontSize:m=16,allVariants:g,pxToRem:S,...y}=typeof r=="function"?r(e):r,v=i/14,$=S||(T=>`${T/m*v}rem`),E=(T,N,A,x,C)=>({fontFamily:o,fontWeight:T,fontSize:$(N),lineHeight:A,...o===bg?{letterSpacing:`${jk(x/N)}em`}:{},...C,...g}),b={h1:E(l,96,1.167,-1.5),h2:E(l,60,1.2,-.5),h3:E(u,48,1.167,0),h4:E(u,34,1.235,.25),h5:E(u,24,1.334,0),h6:E(d,20,1.6,.15),subtitle1:E(u,16,1.75,.15),subtitle2:E(d,14,1.57,.1),body1:E(u,16,1.5,.15),body2:E(u,14,1.43,.15),button:E(d,14,1.75,.4,vg),caption:E(u,12,1.66,.4),overline:E(u,12,2.66,1,vg),inherit:{fontFamily:"inherit",fontWeight:"inherit",fontSize:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}};return fn({htmlFontSize:m,pxToRem:$,fontFamily:o,fontSize:i,fontWeightLight:l,fontWeightRegular:u,fontWeightMedium:d,fontWeightBold:f,...b},y,{clone:!1})}const zk=.2,Dk=.14,Fk=.12;function rt(...e){return[`${e[0]}px ${e[1]}px ${e[2]}px ${e[3]}px rgba(0,0,0,${zk})`,`${e[4]}px ${e[5]}px ${e[6]}px ${e[7]}px rgba(0,0,0,${Dk})`,`${e[8]}px ${e[9]}px ${e[10]}px ${e[11]}px rgba(0,0,0,${Fk})`].join(",")}const Bk=["none",rt(0,2,1,-1,0,1,1,0,0,1,3,0),rt(0,3,1,-2,0,2,2,0,0,1,5,0),rt(0,3,3,-2,0,3,4,0,0,1,8,0),rt(0,2,4,-1,0,4,5,0,0,1,10,0),rt(0,3,5,-1,0,5,8,0,0,1,14,0),rt(0,3,5,-1,0,6,10,0,0,1,18,0),rt(0,4,5,-2,0,7,10,1,0,2,16,1),rt(0,5,5,-3,0,8,10,1,0,3,14,2),rt(0,5,6,-3,0,9,12,1,0,3,16,2),rt(0,6,6,-3,0,10,14,1,0,4,18,3),rt(0,6,7,-4,0,11,15,1,0,4,20,3),rt(0,7,8,-4,0,12,17,2,0,5,22,4),rt(0,7,8,-4,0,13,19,2,0,5,24,4),rt(0,7,9,-4,0,14,21,2,0,5,26,4),rt(0,8,9,-5,0,15,22,2,0,6,28,5),rt(0,8,10,-5,0,16,24,2,0,6,30,5),rt(0,8,11,-5,0,17,26,2,0,6,32,5),rt(0,9,11,-5,0,18,28,2,0,7,34,6),rt(0,9,12,-6,0,19,29,2,0,7,36,6),rt(0,10,13,-6,0,20,31,3,0,8,38,7),rt(0,10,13,-6,0,21,33,3,0,8,40,7),rt(0,10,14,-6,0,22,35,3,0,8,42,7),rt(0,11,14,-7,0,23,36,3,0,9,44,8),rt(0,11,15,-7,0,24,38,3,0,9,46,8)],Lk={easeInOut:"cubic-bezier(0.4, 0, 0.2, 1)",easeOut:"cubic-bezier(0.0, 0, 0.2, 1)",easeIn:"cubic-bezier(0.4, 0, 1, 1)",sharp:"cubic-bezier(0.4, 0, 0.6, 1)"},qk={shortest:150,shorter:200,short:250,standard:300,complex:375,enteringScreen:225,leavingScreen:195};function xg(e){return`${Math.round(e)}ms`}function Wk(e){if(!e)return 0;const r=e/36;return Math.min(Math.round((4+15*r**.25+r/5)*10),3e3)}function Uk(e){const r={...Lk,...e.easing},o={...qk,...e.duration};return{getAutoHeightDuration:Wk,create:(i=["all"],l={})=>{const{duration:u=o.standard,easing:d=r.easeInOut,delay:f=0,...m}=l;return(Array.isArray(i)?i:[i]).map(g=>`${g} ${typeof u=="string"?u:xg(u)} ${d} ${typeof f=="string"?f:xg(f)}`).join(",")},...e,easing:r,duration:o}}const Qk={mobileStepper:1e3,fab:1050,speedDial:1050,appBar:1100,drawer:1200,modal:1300,snackbar:1400,tooltip:1500};function Vk(e){return fr(e)||typeof e>"u"||typeof e=="string"||typeof e=="boolean"||typeof e=="number"||Array.isArray(e)}function u0(e={}){const r={...e};function o(i){const l=Object.entries(i);for(let u=0;u<l.length;u++){const[d,f]=l[u];!Vk(f)||d.startsWith("unstable_")?delete i[d]:fr(f)&&(i[d]={...f},o(i[d]))}}return o(r),`import { unstable_createBreakpoints as createBreakpoints, createTransitions } from '@mui/material/styles';

const theme = ${JSON.stringify(r,null,2)};

theme.breakpoints = createBreakpoints(theme.breakpoints || {});
theme.transitions = createTransitions(theme.transitions || {});

export default theme;`}function Od(e={},...r){const{breakpoints:o,mixins:i={},spacing:l,palette:u={},transitions:d={},typography:f={},shape:m,...g}=e;if(e.vars&&e.generateThemeVars===void 0)throw new Error(Xo(20));const S=kp(u),y=gp(e);let v=fn(y,{mixins:Ok(y.breakpoints,i),palette:S,shadows:Bk.slice(),typography:Ik(S,f),transitions:Uk(d),zIndex:{...Qk}});return v=fn(v,g),v=r.reduce(($,E)=>fn($,E),v),v.unstable_sxConfig={...ul,...g==null?void 0:g.unstable_sxConfig},v.unstable_sx=function($){return za({sx:$,theme:this})},v.toRuntimeSource=u0,v}function jd(e){let r;return e<1?r=5.11916*e**2:r=4.5*Math.log(e+1)+2,Math.round(r*10)/1e3}const Kk=[...Array(25)].map((e,r)=>{if(r===0)return"none";const o=jd(r);return`linear-gradient(rgba(255 255 255 / ${o}), rgba(255 255 255 / ${o}))`});function c0(e){return{inputPlaceholder:e==="dark"?.5:.42,inputUnderline:e==="dark"?.7:.42,switchTrackDisabled:e==="dark"?.2:.12,switchTrack:e==="dark"?.3:.38}}function d0(e){return e==="dark"?Kk:[]}function Gk(e){const{palette:r={mode:"light"},opacity:o,overlays:i,...l}=e,u=kp(r);return{palette:u,opacity:{...c0(u.mode),...o},overlays:i||d0(u.mode),...l}}function Yk(e){var r;return!!e[0].match(/(cssVarPrefix|colorSchemeSelector|rootSelector|typography|mixins|breakpoints|direction|transitions)/)||!!e[0].match(/sxConfig$/)||e[0]==="palette"&&!!((r=e[1])!=null&&r.match(/(mode|contrastThreshold|tonalOffset)/))}const Xk=e=>[...[...Array(25)].map((r,o)=>`--${e?`${e}-`:""}overlays-${o}`),`--${e?`${e}-`:""}palette-AppBar-darkBg`,`--${e?`${e}-`:""}palette-AppBar-darkColor`],Hk=e=>(r,o)=>{const i=e.rootSelector||":root",l=e.colorSchemeSelector;let u=l;if(l==="class"&&(u=".%s"),l==="data"&&(u="[data-%s]"),l!=null&&l.startsWith("data-")&&!l.includes("%s")&&(u=`[${l}="%s"]`),e.defaultColorScheme===r){if(r==="dark"){const d={};return Xk(e.cssVarPrefix).forEach(f=>{d[f]=o[f],delete o[f]}),u==="media"?{[i]:o,"@media (prefers-color-scheme: dark)":{[i]:d}}:u?{[u.replace("%s",r)]:d,[`${i}, ${u.replace("%s",r)}`]:o}:{[i]:{...o,...d}}}if(u&&u!=="media")return`${i}, ${u.replace("%s",String(r))}`}else if(r){if(u==="media")return{[`@media (prefers-color-scheme: ${String(r)})`]:{[i]:o}};if(u)return u.replace("%s",String(r))}return i};function Jk(e,r){r.forEach(o=>{e[o]||(e[o]={})})}function Y(e,r,o){!e[r]&&o&&(e[r]=o)}function Mi(e){return typeof e!="string"||!e.startsWith("hsl")?e:t0(e)}function zr(e,r){`${r}Channel`in e||(e[`${r}Channel`]=Ri(Mi(e[r]),`MUI: Can't create \`palette.${r}Channel\` because \`palette.${r}\` is not one of these formats: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color().
To suppress this warning, you need to explicitly provide the \`palette.${r}Channel\` as a string (in rgb format, for example "12 12 12") or undefined if you want to remove the channel token.`))}function Zk(e){return typeof e=="number"?`${e}px`:typeof e=="string"||typeof e=="function"||Array.isArray(e)?e:"8px"}const ur=e=>{try{return e()}catch{}},e2=(e="mui")=>wk(e);function pd(e,r,o,i){if(!r)return;r=r===!0?{}:r;const l=i==="dark"?"dark":"light";if(!o){e[i]=Gk({...r,palette:{mode:l,...r==null?void 0:r.palette}});return}const{palette:u,...d}=Od({...o,palette:{mode:l,...r==null?void 0:r.palette}});return e[i]={...r,palette:u,opacity:{...c0(l),...r==null?void 0:r.opacity},overlays:(r==null?void 0:r.overlays)||d0(l)},d}function t2(e={},...r){const{colorSchemes:o={light:!0},defaultColorScheme:i,disableCssColorScheme:l=!1,cssVarPrefix:u="mui",shouldSkipGeneratingVar:d=Yk,colorSchemeSelector:f=o.light&&o.dark?"media":void 0,rootSelector:m=":root",...g}=e,S=Object.keys(o)[0],y=i||(o.light&&S!=="light"?"light":S),v=e2(u),{[y]:$,light:E,dark:b,...T}=o,N={...T};let A=$;if((y==="dark"&&!("dark"in o)||y==="light"&&!("light"in o))&&(A=!0),!A)throw new Error(Xo(21,y));const x=pd(N,A,g,y);E&&!N.light&&pd(N,E,void 0,"light"),b&&!N.dark&&pd(N,b,void 0,"dark");let C={defaultColorScheme:y,...x,cssVarPrefix:u,colorSchemeSelector:f,rootSelector:m,getCssVar:v,colorSchemes:N,font:{...Mk(x.typography),...x.font},spacing:Zk(g.spacing)};Object.keys(C.colorSchemes).forEach(z=>{const h=C.colorSchemes[z].palette,P=_=>{const R=_.split("-"),F=R[1],G=R[2];return v(_,h[F][G])};if(h.mode==="light"&&(Y(h.common,"background","#fff"),Y(h.common,"onBackground","#000")),h.mode==="dark"&&(Y(h.common,"background","#000"),Y(h.common,"onBackground","#fff")),Jk(h,["Alert","AppBar","Avatar","Button","Chip","FilledInput","LinearProgress","Skeleton","Slider","SnackbarContent","SpeedDialAction","StepConnector","StepContent","Switch","TableCell","Tooltip"]),h.mode==="light"){Y(h.Alert,"errorColor",Xe(h.error.light,.6)),Y(h.Alert,"infoColor",Xe(h.info.light,.6)),Y(h.Alert,"successColor",Xe(h.success.light,.6)),Y(h.Alert,"warningColor",Xe(h.warning.light,.6)),Y(h.Alert,"errorFilledBg",P("palette-error-main")),Y(h.Alert,"infoFilledBg",P("palette-info-main")),Y(h.Alert,"successFilledBg",P("palette-success-main")),Y(h.Alert,"warningFilledBg",P("palette-warning-main")),Y(h.Alert,"errorFilledColor",ur(()=>h.getContrastText(h.error.main))),Y(h.Alert,"infoFilledColor",ur(()=>h.getContrastText(h.info.main))),Y(h.Alert,"successFilledColor",ur(()=>h.getContrastText(h.success.main))),Y(h.Alert,"warningFilledColor",ur(()=>h.getContrastText(h.warning.main))),Y(h.Alert,"errorStandardBg",He(h.error.light,.9)),Y(h.Alert,"infoStandardBg",He(h.info.light,.9)),Y(h.Alert,"successStandardBg",He(h.success.light,.9)),Y(h.Alert,"warningStandardBg",He(h.warning.light,.9)),Y(h.Alert,"errorIconColor",P("palette-error-main")),Y(h.Alert,"infoIconColor",P("palette-info-main")),Y(h.Alert,"successIconColor",P("palette-success-main")),Y(h.Alert,"warningIconColor",P("palette-warning-main")),Y(h.AppBar,"defaultBg",P("palette-grey-100")),Y(h.Avatar,"defaultBg",P("palette-grey-400")),Y(h.Button,"inheritContainedBg",P("palette-grey-300")),Y(h.Button,"inheritContainedHoverBg",P("palette-grey-A100")),Y(h.Chip,"defaultBorder",P("palette-grey-400")),Y(h.Chip,"defaultAvatarColor",P("palette-grey-700")),Y(h.Chip,"defaultIconColor",P("palette-grey-700")),Y(h.FilledInput,"bg","rgba(0, 0, 0, 0.06)"),Y(h.FilledInput,"hoverBg","rgba(0, 0, 0, 0.09)"),Y(h.FilledInput,"disabledBg","rgba(0, 0, 0, 0.12)"),Y(h.LinearProgress,"primaryBg",He(h.primary.main,.62)),Y(h.LinearProgress,"secondaryBg",He(h.secondary.main,.62)),Y(h.LinearProgress,"errorBg",He(h.error.main,.62)),Y(h.LinearProgress,"infoBg",He(h.info.main,.62)),Y(h.LinearProgress,"successBg",He(h.success.main,.62)),Y(h.LinearProgress,"warningBg",He(h.warning.main,.62)),Y(h.Skeleton,"bg",`rgba(${P("palette-text-primaryChannel")} / 0.11)`),Y(h.Slider,"primaryTrack",He(h.primary.main,.62)),Y(h.Slider,"secondaryTrack",He(h.secondary.main,.62)),Y(h.Slider,"errorTrack",He(h.error.main,.62)),Y(h.Slider,"infoTrack",He(h.info.main,.62)),Y(h.Slider,"successTrack",He(h.success.main,.62)),Y(h.Slider,"warningTrack",He(h.warning.main,.62));const _=Ns(h.background.default,.8);Y(h.SnackbarContent,"bg",_),Y(h.SnackbarContent,"color",ur(()=>h.getContrastText(_))),Y(h.SpeedDialAction,"fabHoverBg",Ns(h.background.paper,.15)),Y(h.StepConnector,"border",P("palette-grey-400")),Y(h.StepContent,"border",P("palette-grey-400")),Y(h.Switch,"defaultColor",P("palette-common-white")),Y(h.Switch,"defaultDisabledColor",P("palette-grey-100")),Y(h.Switch,"primaryDisabledColor",He(h.primary.main,.62)),Y(h.Switch,"secondaryDisabledColor",He(h.secondary.main,.62)),Y(h.Switch,"errorDisabledColor",He(h.error.main,.62)),Y(h.Switch,"infoDisabledColor",He(h.info.main,.62)),Y(h.Switch,"successDisabledColor",He(h.success.main,.62)),Y(h.Switch,"warningDisabledColor",He(h.warning.main,.62)),Y(h.TableCell,"border",He(Ts(h.divider,1),.88)),Y(h.Tooltip,"bg",Ts(h.grey[700],.92))}if(h.mode==="dark"){Y(h.Alert,"errorColor",He(h.error.light,.6)),Y(h.Alert,"infoColor",He(h.info.light,.6)),Y(h.Alert,"successColor",He(h.success.light,.6)),Y(h.Alert,"warningColor",He(h.warning.light,.6)),Y(h.Alert,"errorFilledBg",P("palette-error-dark")),Y(h.Alert,"infoFilledBg",P("palette-info-dark")),Y(h.Alert,"successFilledBg",P("palette-success-dark")),Y(h.Alert,"warningFilledBg",P("palette-warning-dark")),Y(h.Alert,"errorFilledColor",ur(()=>h.getContrastText(h.error.dark))),Y(h.Alert,"infoFilledColor",ur(()=>h.getContrastText(h.info.dark))),Y(h.Alert,"successFilledColor",ur(()=>h.getContrastText(h.success.dark))),Y(h.Alert,"warningFilledColor",ur(()=>h.getContrastText(h.warning.dark))),Y(h.Alert,"errorStandardBg",Xe(h.error.light,.9)),Y(h.Alert,"infoStandardBg",Xe(h.info.light,.9)),Y(h.Alert,"successStandardBg",Xe(h.success.light,.9)),Y(h.Alert,"warningStandardBg",Xe(h.warning.light,.9)),Y(h.Alert,"errorIconColor",P("palette-error-main")),Y(h.Alert,"infoIconColor",P("palette-info-main")),Y(h.Alert,"successIconColor",P("palette-success-main")),Y(h.Alert,"warningIconColor",P("palette-warning-main")),Y(h.AppBar,"defaultBg",P("palette-grey-900")),Y(h.AppBar,"darkBg",P("palette-background-paper")),Y(h.AppBar,"darkColor",P("palette-text-primary")),Y(h.Avatar,"defaultBg",P("palette-grey-600")),Y(h.Button,"inheritContainedBg",P("palette-grey-800")),Y(h.Button,"inheritContainedHoverBg",P("palette-grey-700")),Y(h.Chip,"defaultBorder",P("palette-grey-700")),Y(h.Chip,"defaultAvatarColor",P("palette-grey-300")),Y(h.Chip,"defaultIconColor",P("palette-grey-300")),Y(h.FilledInput,"bg","rgba(255, 255, 255, 0.09)"),Y(h.FilledInput,"hoverBg","rgba(255, 255, 255, 0.13)"),Y(h.FilledInput,"disabledBg","rgba(255, 255, 255, 0.12)"),Y(h.LinearProgress,"primaryBg",Xe(h.primary.main,.5)),Y(h.LinearProgress,"secondaryBg",Xe(h.secondary.main,.5)),Y(h.LinearProgress,"errorBg",Xe(h.error.main,.5)),Y(h.LinearProgress,"infoBg",Xe(h.info.main,.5)),Y(h.LinearProgress,"successBg",Xe(h.success.main,.5)),Y(h.LinearProgress,"warningBg",Xe(h.warning.main,.5)),Y(h.Skeleton,"bg",`rgba(${P("palette-text-primaryChannel")} / 0.13)`),Y(h.Slider,"primaryTrack",Xe(h.primary.main,.5)),Y(h.Slider,"secondaryTrack",Xe(h.secondary.main,.5)),Y(h.Slider,"errorTrack",Xe(h.error.main,.5)),Y(h.Slider,"infoTrack",Xe(h.info.main,.5)),Y(h.Slider,"successTrack",Xe(h.success.main,.5)),Y(h.Slider,"warningTrack",Xe(h.warning.main,.5));const _=Ns(h.background.default,.98);Y(h.SnackbarContent,"bg",_),Y(h.SnackbarContent,"color",ur(()=>h.getContrastText(_))),Y(h.SpeedDialAction,"fabHoverBg",Ns(h.background.paper,.15)),Y(h.StepConnector,"border",P("palette-grey-600")),Y(h.StepContent,"border",P("palette-grey-600")),Y(h.Switch,"defaultColor",P("palette-grey-300")),Y(h.Switch,"defaultDisabledColor",P("palette-grey-600")),Y(h.Switch,"primaryDisabledColor",Xe(h.primary.main,.55)),Y(h.Switch,"secondaryDisabledColor",Xe(h.secondary.main,.55)),Y(h.Switch,"errorDisabledColor",Xe(h.error.main,.55)),Y(h.Switch,"infoDisabledColor",Xe(h.info.main,.55)),Y(h.Switch,"successDisabledColor",Xe(h.success.main,.55)),Y(h.Switch,"warningDisabledColor",Xe(h.warning.main,.55)),Y(h.TableCell,"border",Xe(Ts(h.divider,1),.68)),Y(h.Tooltip,"bg",Ts(h.grey[700],.92))}zr(h.background,"default"),zr(h.background,"paper"),zr(h.common,"background"),zr(h.common,"onBackground"),zr(h,"divider"),Object.keys(h).forEach(_=>{const R=h[_];_!=="tonalOffset"&&R&&typeof R=="object"&&(R.main&&Y(h[_],"mainChannel",Ri(Mi(R.main))),R.light&&Y(h[_],"lightChannel",Ri(Mi(R.light))),R.dark&&Y(h[_],"darkChannel",Ri(Mi(R.dark))),R.contrastText&&Y(h[_],"contrastTextChannel",Ri(Mi(R.contrastText))),_==="text"&&(zr(h[_],"primary"),zr(h[_],"secondary")),_==="action"&&(R.active&&zr(h[_],"active"),R.selected&&zr(h[_],"selected")))})}),C=r.reduce((z,h)=>fn(z,h),C);const k={prefix:u,disableCssColorScheme:l,shouldSkipGeneratingVar:d,getSelector:Hk(C)},{vars:M,generateThemeVars:I,generateStyleSheets:L}=Ck(C,k);return C.vars=M,Object.entries(C.colorSchemes[C.defaultColorScheme]).forEach(([z,h])=>{C[z]=h}),C.generateThemeVars=I,C.generateStyleSheets=L,C.generateSpacing=function(){return Hy(g.spacing,mp(this))},C.getColorSchemeSelector=$k(f),C.spacing=C.generateSpacing(),C.shouldSkipGeneratingVar=d,C.unstable_sxConfig={...ul,...g==null?void 0:g.unstable_sxConfig},C.unstable_sx=function(z){return za({sx:z,theme:this})},C.toRuntimeSource=u0,C}function wg(e,r,o){e.colorSchemes&&o&&(e.colorSchemes[r]={...o!==!0&&o,palette:kp({...o===!0?{}:o.palette,mode:r})})}function n2(e={},...r){const{palette:o,cssVariables:i=!1,colorSchemes:l=o?void 0:{light:!0},defaultColorScheme:u=o==null?void 0:o.mode,...d}=e,f=u||"light",m=l==null?void 0:l[f],g={...l,...o?{[f]:{...typeof m!="boolean"&&m,palette:o}}:void 0};if(i===!1){if(!("colorSchemes"in e))return Od(e,...r);let S=o;"palette"in e||g[f]&&(g[f]!==!0?S=g[f].palette:f==="dark"&&(S={mode:"dark"}));const y=Od({...e,palette:S},...r);return y.defaultColorScheme=f,y.colorSchemes=g,y.palette.mode==="light"&&(y.colorSchemes.light={...g.light!==!0&&g.light,palette:y.palette},wg(y,"dark",g.dark)),y.palette.mode==="dark"&&(y.colorSchemes.dark={...g.dark!==!0&&g.dark,palette:y.palette},wg(y,"light",g.light)),y}return!o&&!("light"in g)&&f==="light"&&(g.light=!0),t2({...d,colorSchemes:g,defaultColorScheme:f,...typeof i!="boolean"&&i},...r)}const p0=n2();function Cp(){const e=HS(p0);return e[_y]||e}function r2(e){return e!=="ownerState"&&e!=="theme"&&e!=="sx"&&e!=="as"}const o2=e=>r2(e)&&e!=="classes",je=ok({themeId:_y,defaultTheme:p0,rootShouldForwardProp:o2}),$t=xk;function Mt(e){return bk(e)}function f0(e,r){if(e==null)return{};var o={};for(var i in e)if({}.hasOwnProperty.call(e,i)){if(r.indexOf(i)!==-1)continue;o[i]=e[i]}return o}function Id(e,r){return Id=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(o,i){return o.__proto__=i,o},Id(e,r)}function m0(e,r){e.prototype=Object.create(r.prototype),e.prototype.constructor=e,Id(e,r)}const Ys=Xn.createContext(null);function a2(e){return Rt("MuiPaper",e)}ht("MuiPaper",["root","rounded","outlined","elevation","elevation0","elevation1","elevation2","elevation3","elevation4","elevation5","elevation6","elevation7","elevation8","elevation9","elevation10","elevation11","elevation12","elevation13","elevation14","elevation15","elevation16","elevation17","elevation18","elevation19","elevation20","elevation21","elevation22","elevation23","elevation24"]);const i2=e=>{const{square:r,elevation:o,variant:i,classes:l}=e,u={root:["root",i,!r&&"rounded",i==="elevation"&&`elevation${o}`]};return qt(u,a2,l)},l2=je("div",{name:"MuiPaper",slot:"Root",overridesResolver:(e,r)=>{const{ownerState:o}=e;return[r.root,r[o.variant],!o.square&&r.rounded,o.variant==="elevation"&&r[`elevation${o.elevation}`]]}})($t(({theme:e})=>({backgroundColor:(e.vars||e).palette.background.paper,color:(e.vars||e).palette.text.primary,transition:e.transitions.create("box-shadow"),variants:[{props:({ownerState:r})=>!r.square,style:{borderRadius:e.shape.borderRadius}},{props:{variant:"outlined"},style:{border:`1px solid ${(e.vars||e).palette.divider}`}},{props:{variant:"elevation"},style:{boxShadow:"var(--Paper-shadow)",backgroundImage:"var(--Paper-overlay)"}}]}))),yu=j.forwardRef(function(e,r){var o;const i=Mt({props:e,name:"MuiPaper"}),l=Cp(),{className:u,component:d="div",elevation:f=1,square:m=!1,variant:g="elevation",...S}=i,y={...i,component:d,elevation:f,square:m,variant:g},v=i2(y);return q.jsx(l2,{as:d,ownerState:y,className:ze(v.root,u),ref:r,...S,style:{...g==="elevation"&&{"--Paper-shadow":(l.vars||l).shadows[f],...l.vars&&{"--Paper-overlay":(o=l.vars.overlays)==null?void 0:o[f]},...!l.vars&&l.palette.mode==="dark"&&{"--Paper-overlay":`linear-gradient(${Ct("#fff",jd(f))}, ${Ct("#fff",jd(f))})`}},...S.style}})});function _n(e,r){const{className:o,elementType:i,ownerState:l,externalForwardedProps:u,internalForwardedProps:d,shouldForwardComponentProp:f=!1,...m}=r,{component:g,slots:S={[e]:void 0},slotProps:y={[e]:void 0},...v}=u,$=S[e]||i,E=i0(y[e],l),{props:{component:b,...T},internalRef:N}=a0({className:o,...m,externalForwardedProps:e==="root"?v:void 0,externalSlotProps:E}),A=er(N,E==null?void 0:E.ref,r.ref),x=e==="root"?b||g:b,C=n0($,{...e==="root"&&!g&&!S[e]&&d,...e!=="root"&&!S[e]&&d,...T,...x&&!f&&{as:x},...x&&f&&{component:x},ref:A},l);return[$,C]}function s2(e){return typeof e.main=="string"}function u2(e,r=[]){if(!s2(e))return!1;for(const o of r)if(!e.hasOwnProperty(o)||typeof e[o]!="string")return!1;return!0}function ho(e=[]){return([,r])=>r&&u2(r,e)}function c2(e){return typeof e=="function"?e():e}const h0=j.forwardRef(function(e,r){const{children:o,container:i,disablePortal:l=!1}=e,[u,d]=j.useState(null),f=er(j.isValidElement(o)?Sp(o):null,r);if(Zi(()=>{l||d(c2(i)||document.body)},[i,l]),Zi(()=>{if(u&&!l)return Md(r,u),()=>{Md(r,null)}},[r,u,l]),l){if(j.isValidElement(o)){const m={ref:f};return j.cloneElement(o,m)}return o}return u&&Py.createPortal(o,u)}),d2={xxsmall:"0.75rem",xsmall:"1rem",small:"1.125rem",medium:"1.25rem",large:"1.5rem",xlarge:"1.75rem",xxlarge:"2rem"},p2={primary:"var(--primary-main)",secondary:"var(--secondary-main)",success:"var(--success-main)",error:"var(--error-main)",info:"var(--info-main)",warning:"var(--warning-main)"};function Wa(e){function r({style:o,color:i="var(--text-secondary)",size:l="medium",variant:u="outlined",...d}){const f=typeof l=="string"&&d2[l]||l,m=p2[i]||i;return q.jsx(e,{style:{...o,width:f,height:f},color:m,variant:u,...d})}return r}/**
 * @license lucide-react v0.460.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const f2=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),g0=(...e)=>e.filter((r,o,i)=>!!r&&r.trim()!==""&&i.indexOf(r)===o).join(" ").trim();/**
 * @license lucide-react v0.460.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var m2={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.460.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const h2=j.forwardRef(({color:e="currentColor",size:r=24,strokeWidth:o=2,absoluteStrokeWidth:i,className:l="",children:u,iconNode:d,...f},m)=>j.createElement("svg",{ref:m,...m2,width:r,height:r,stroke:e,strokeWidth:i?Number(o)*24/Number(r):o,className:g0("lucide",l),...f},[...d.map(([g,S])=>j.createElement(g,S)),...Array.isArray(u)?u:[u]]));/**
 * @license lucide-react v0.460.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const y0=(e,r)=>{const o=j.forwardRef(({className:i,...l},u)=>j.createElement(h2,{ref:u,iconNode:r,className:g0(`lucide-${f2(e)}`,i),...l}));return o.displayName=`${e}`,o};var g2=Object.defineProperty,y2=(e,r,o)=>r in e?g2(e,r,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[r]=o,Is=(e,r,o)=>y2(e,typeof r!="symbol"?r+"":r,o);const v2="_screen_1xn16_1",b2="_userFormContainer_1xn16_15",x2="_scrollableContent_1xn16_29",w2="_formHeader_1xn16_47",S2="_userForm_1xn16_15",k2="_userFormTitle_1xn16_85",C2="_userDeleteButton_1xn16_103",$2="_formFieldsContainer_1xn16_117",E2="_field_1xn16_137",T2="_fieldlabel_1xn16_159",N2="_autoCompletePaper_1xn16_185",A2="_autoCompleteListbox_1xn16_201",P2="_inputField_1xn16_213",_2="_footer_1xn16_283",R2="_cancelButton_1xn16_317",M2="_cancelButtonText_1xn16_337",O2="_submitButton_1xn16_355",Ae={screen:v2,userFormContainer:b2,scrollableContent:x2,formHeader:w2,userForm:S2,userFormTitle:k2,userDeleteButton:C2,formFieldsContainer:$2,field:E2,fieldlabel:T2,autoCompletePaper:N2,autoCompleteListbox:A2,inputField:P2,footer:_2,cancelButton:R2,cancelButtonText:M2,submitButton:O2};var v0={exports:{}},Qe={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sg;function j2(){if(Sg)return Qe;Sg=1;var e=typeof Symbol=="function"&&Symbol.for,r=e?Symbol.for("react.element"):60103,o=e?Symbol.for("react.portal"):60106,i=e?Symbol.for("react.fragment"):60107,l=e?Symbol.for("react.strict_mode"):60108,u=e?Symbol.for("react.profiler"):60114,d=e?Symbol.for("react.provider"):60109,f=e?Symbol.for("react.context"):60110,m=e?Symbol.for("react.async_mode"):60111,g=e?Symbol.for("react.concurrent_mode"):60111,S=e?Symbol.for("react.forward_ref"):60112,y=e?Symbol.for("react.suspense"):60113,v=e?Symbol.for("react.suspense_list"):60120,$=e?Symbol.for("react.memo"):60115,E=e?Symbol.for("react.lazy"):60116,b=e?Symbol.for("react.block"):60121,T=e?Symbol.for("react.fundamental"):60117,N=e?Symbol.for("react.responder"):60118,A=e?Symbol.for("react.scope"):60119;function x(k){if(typeof k=="object"&&k!==null){var M=k.$$typeof;switch(M){case r:switch(k=k.type,k){case m:case g:case i:case u:case l:case y:return k;default:switch(k=k&&k.$$typeof,k){case f:case S:case E:case $:case d:return k;default:return M}}case o:return M}}}function C(k){return x(k)===g}return Qe.AsyncMode=m,Qe.ConcurrentMode=g,Qe.ContextConsumer=f,Qe.ContextProvider=d,Qe.Element=r,Qe.ForwardRef=S,Qe.Fragment=i,Qe.Lazy=E,Qe.Memo=$,Qe.Portal=o,Qe.Profiler=u,Qe.StrictMode=l,Qe.Suspense=y,Qe.isAsyncMode=function(k){return C(k)||x(k)===m},Qe.isConcurrentMode=C,Qe.isContextConsumer=function(k){return x(k)===f},Qe.isContextProvider=function(k){return x(k)===d},Qe.isElement=function(k){return typeof k=="object"&&k!==null&&k.$$typeof===r},Qe.isForwardRef=function(k){return x(k)===S},Qe.isFragment=function(k){return x(k)===i},Qe.isLazy=function(k){return x(k)===E},Qe.isMemo=function(k){return x(k)===$},Qe.isPortal=function(k){return x(k)===o},Qe.isProfiler=function(k){return x(k)===u},Qe.isStrictMode=function(k){return x(k)===l},Qe.isSuspense=function(k){return x(k)===y},Qe.isValidElementType=function(k){return typeof k=="string"||typeof k=="function"||k===i||k===g||k===u||k===l||k===y||k===v||typeof k=="object"&&k!==null&&(k.$$typeof===E||k.$$typeof===$||k.$$typeof===d||k.$$typeof===f||k.$$typeof===S||k.$$typeof===T||k.$$typeof===N||k.$$typeof===A||k.$$typeof===b)},Qe.typeOf=x,Qe}v0.exports=j2();var I2=v0.exports,b0=I2,z2={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},D2={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},x0={};x0[b0.ForwardRef]=z2;x0[b0.Memo]=D2;var kg=function(e,r){var o=arguments;if(r==null||!dp.call(r,"css"))return j.createElement.apply(void 0,o);var i=o.length,l=new Array(i);l[0]=Dw,l[1]=jw(e,r);for(var u=2;u<i;u++)l[u]=o[u];return j.createElement.apply(null,l)};(function(e){var r;r||(r=e.JSX||(e.JSX={}))})(kg||(kg={}));function $p(){for(var e=arguments.length,r=new Array(e),o=0;o<e;o++)r[o]=arguments[o];return su(r)}function cl(){var e=$p.apply(void 0,arguments),r="animation-"+e.name;return{name:r,styles:"@keyframes "+r+"{"+e.styles+"}",anim:1,toString:function(){return"_EMO_"+this.name+"_"+this.styles+"_EMO_"}}}xp(Ma.elementType);Ma.oneOfType([Ma.func,Ma.object]);function F2(e,r=166){let o;function i(...l){const u=()=>{e.apply(this,l)};clearTimeout(o),o=setTimeout(u,r)}return i.clear=()=>{clearTimeout(o)},i}function fd({controlled:e,default:r,name:o,state:i="value"}){const{current:l}=j.useRef(e!==void 0),[u,d]=j.useState(r),f=l?e:u,m=j.useCallback(g=>{l||d(g)},[]);return[f,m]}const Cg={};function w0(e,r){const o=j.useRef(Cg);return o.current===Cg&&(o.current=e(r)),o}const B2=[];function L2(e){j.useEffect(e,B2)}class Ep{constructor(){Is(this,"currentId",null),Is(this,"clear",()=>{this.currentId!==null&&(clearTimeout(this.currentId),this.currentId=null)}),Is(this,"disposeEffect",()=>this.clear)}static create(){return new Ep}start(r,o){this.clear(),this.currentId=setTimeout(()=>{this.currentId=null,o()},r)}}function q2(){const e=w0(Ep.create).current;return L2(e.disposeEffect),e}function $g(e){try{return e.matches(":focus-visible")}catch{}return!1}const W2=e=>{const r=j.useRef({});return j.useEffect(()=>{r.current=e}),r.current};function U2(e){var r;const{elementType:o,externalSlotProps:i,ownerState:l,skipResolvingSlotProps:u=!1,...d}=e,f=u?{}:i0(i,l),{props:m,internalRef:g}=a0({...d,externalSlotProps:f}),S=er(g,f==null?void 0:f.ref,(r=e.additionalProps)==null?void 0:r.ref);return n0(o,{...m,ref:S},l)}const Q2=j.createContext(),V2=()=>j.useContext(Q2)??!1;function K2(e){return Rt("MuiSvgIcon",e)}ht("MuiSvgIcon",["root","colorPrimary","colorSecondary","colorAction","colorError","colorDisabled","fontSizeInherit","fontSizeSmall","fontSizeMedium","fontSizeLarge"]);const G2=e=>{const{color:r,fontSize:o,classes:i}=e,l={root:["root",r!=="inherit"&&`color${xe(r)}`,`fontSize${xe(o)}`]};return qt(l,K2,i)},Y2=je("svg",{name:"MuiSvgIcon",slot:"Root",overridesResolver:(e,r)=>{const{ownerState:o}=e;return[r.root,o.color!=="inherit"&&r[`color${xe(o.color)}`],r[`fontSize${xe(o.fontSize)}`]]}})($t(({theme:e})=>{var r,o,i,l,u,d,f,m,g,S,y,v,$,E;return{userSelect:"none",width:"1em",height:"1em",display:"inline-block",flexShrink:0,transition:(l=(r=e.transitions)==null?void 0:r.create)==null?void 0:l.call(r,"fill",{duration:(i=(o=(e.vars??e).transitions)==null?void 0:o.duration)==null?void 0:i.shorter}),variants:[{props:b=>!b.hasSvgAsChild,style:{fill:"currentColor"}},{props:{fontSize:"inherit"},style:{fontSize:"inherit"}},{props:{fontSize:"small"},style:{fontSize:((d=(u=e.typography)==null?void 0:u.pxToRem)==null?void 0:d.call(u,20))||"1.25rem"}},{props:{fontSize:"medium"},style:{fontSize:((m=(f=e.typography)==null?void 0:f.pxToRem)==null?void 0:m.call(f,24))||"1.5rem"}},{props:{fontSize:"large"},style:{fontSize:((S=(g=e.typography)==null?void 0:g.pxToRem)==null?void 0:S.call(g,35))||"2.1875rem"}},...Object.entries((e.vars??e).palette).filter(([,b])=>b&&b.main).map(([b])=>{var T,N;return{props:{color:b},style:{color:(N=(T=(e.vars??e).palette)==null?void 0:T[b])==null?void 0:N.main}}}),{props:{color:"action"},style:{color:(v=(y=(e.vars??e).palette)==null?void 0:y.action)==null?void 0:v.active}},{props:{color:"disabled"},style:{color:(E=($=(e.vars??e).palette)==null?void 0:$.action)==null?void 0:E.disabled}},{props:{color:"inherit"},style:{color:void 0}}]}})),zd=j.forwardRef(function(e,r){const o=Mt({props:e,name:"MuiSvgIcon"}),{children:i,className:l,color:u="inherit",component:d="svg",fontSize:f="medium",htmlColor:m,inheritViewBox:g=!1,titleAccess:S,viewBox:y="0 0 24 24",...v}=o,$=j.isValidElement(i)&&i.type==="svg",E={...o,color:u,component:d,fontSize:f,instanceFontSize:e.fontSize,inheritViewBox:g,viewBox:y,hasSvgAsChild:$},b={};g||(b.viewBox=y);const T=G2(E);return q.jsxs(Y2,{as:d,className:ze(T.root,l),focusable:"false",color:m,"aria-hidden":S?void 0:!0,role:S?"img":void 0,ref:r,...b,...v,...$&&i.props,ownerState:E,children:[$?i.props.children:i,S?q.jsx("title",{children:S}):null]})});zd.muiName="SvgIcon";function Tp(e,r){function o(i,l){return q.jsx(zd,{"data-testid":`${r}Icon`,ref:l,...i,children:e})}return o.muiName=zd.muiName,j.memo(j.forwardRef(o))}function X2(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Np(e,r){var o=function(l){return r&&j.isValidElement(l)?r(l):l},i=Object.create(null);return e&&j.Children.map(e,function(l){return l}).forEach(function(l){i[l.key]=o(l)}),i}function H2(e,r){e=e||{},r=r||{};function o(S){return S in r?r[S]:e[S]}var i=Object.create(null),l=[];for(var u in e)u in r?l.length&&(i[u]=l,l=[]):l.push(u);var d,f={};for(var m in r){if(i[m])for(d=0;d<i[m].length;d++){var g=i[m][d];f[i[m][d]]=o(g)}f[m]=o(m)}for(d=0;d<l.length;d++)f[l[d]]=o(l[d]);return f}function Uo(e,r,o){return o[r]!=null?o[r]:e.props[r]}function J2(e,r){return Np(e.children,function(o){return j.cloneElement(o,{onExited:r.bind(null,o),in:!0,appear:Uo(o,"appear",e),enter:Uo(o,"enter",e),exit:Uo(o,"exit",e)})})}function Z2(e,r,o){var i=Np(e.children),l=H2(r,i);return Object.keys(l).forEach(function(u){var d=l[u];if(j.isValidElement(d)){var f=u in r,m=u in i,g=r[u],S=j.isValidElement(g)&&!g.props.in;m&&(!f||S)?l[u]=j.cloneElement(d,{onExited:o.bind(null,d),in:!0,exit:Uo(d,"exit",e),enter:Uo(d,"enter",e)}):!m&&f&&!S?l[u]=j.cloneElement(d,{in:!1}):m&&f&&j.isValidElement(g)&&(l[u]=j.cloneElement(d,{onExited:o.bind(null,d),in:g.props.in,exit:Uo(d,"exit",e),enter:Uo(d,"enter",e)}))}}),l}var eC=Object.values||function(e){return Object.keys(e).map(function(r){return e[r]})},tC={component:"div",childFactory:function(e){return e}},Ap=function(e){m0(r,e);function r(i,l){var u;u=e.call(this,i,l)||this;var d=u.handleExited.bind(X2(u));return u.state={contextValue:{isMounting:!0},handleExited:d,firstRender:!0},u}var o=r.prototype;return o.componentDidMount=function(){this.mounted=!0,this.setState({contextValue:{isMounting:!1}})},o.componentWillUnmount=function(){this.mounted=!1},r.getDerivedStateFromProps=function(i,l){var u=l.children,d=l.handleExited,f=l.firstRender;return{children:f?J2(i,d):Z2(i,u,d),firstRender:!1}},o.handleExited=function(i,l){var u=Np(this.props.children);i.key in u||(i.props.onExited&&i.props.onExited(l),this.mounted&&this.setState(function(d){var f=Vs({},d.children);return delete f[i.key],{children:f}}))},o.render=function(){var i=this.props,l=i.component,u=i.childFactory,d=f0(i,["component","childFactory"]),f=this.state.contextValue,m=eC(this.state.children).map(u);return delete d.appear,delete d.enter,delete d.exit,l===null?Xn.createElement(Ys.Provider,{value:f},m):Xn.createElement(Ys.Provider,{value:f},Xn.createElement(l,d,m))},r}(Xn.Component);Ap.propTypes={};Ap.defaultProps=tC;class Xs{constructor(){Is(this,"mountEffect",()=>{this.shouldMount&&!this.didMount&&this.ref.current!==null&&(this.didMount=!0,this.mounted.resolve())}),this.ref={current:null},this.mounted=null,this.didMount=!1,this.shouldMount=!1,this.setShouldMount=null}static create(){return new Xs}static use(){const r=w0(Xs.create).current,[o,i]=j.useState(!1);return r.shouldMount=o,r.setShouldMount=i,j.useEffect(r.mountEffect,[o]),r}mount(){return this.mounted||(this.mounted=rC(),this.shouldMount=!0,this.setShouldMount(this.shouldMount)),this.mounted}start(...r){this.mount().then(()=>{var o;return(o=this.ref.current)==null?void 0:o.start(...r)})}stop(...r){this.mount().then(()=>{var o;return(o=this.ref.current)==null?void 0:o.stop(...r)})}pulsate(...r){this.mount().then(()=>{var o;return(o=this.ref.current)==null?void 0:o.pulsate(...r)})}}function nC(){return Xs.use()}function rC(){let e,r;const o=new Promise((i,l)=>{e=i,r=l});return o.resolve=e,o.reject=r,o}function oC(e){const{className:r,classes:o,pulsate:i=!1,rippleX:l,rippleY:u,rippleSize:d,in:f,onExited:m,timeout:g}=e,[S,y]=j.useState(!1),v=ze(r,o.ripple,o.rippleVisible,i&&o.ripplePulsate),$={width:d,height:d,top:-(d/2)+u,left:-(d/2)+l},E=ze(o.child,S&&o.childLeaving,i&&o.childPulsate);return!f&&!S&&y(!0),j.useEffect(()=>{if(!f&&m!=null){const b=setTimeout(m,g);return()=>{clearTimeout(b)}}},[m,f,g]),q.jsx("span",{className:v,style:$,children:q.jsx("span",{className:E})})}const An=ht("MuiTouchRipple",["root","ripple","rippleVisible","ripplePulsate","child","childLeaving","childPulsate"]),Dd=550,aC=80,iC=cl`
  0% {
    transform: scale(0);
    opacity: 0.1;
  }

  100% {
    transform: scale(1);
    opacity: 0.3;
  }
`,lC=cl`
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
`,sC=cl`
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(0.92);
  }

  100% {
    transform: scale(1);
  }
`,uC=je("span",{name:"MuiTouchRipple",slot:"Root"})({overflow:"hidden",pointerEvents:"none",position:"absolute",zIndex:0,top:0,right:0,bottom:0,left:0,borderRadius:"inherit"}),cC=je(oC,{name:"MuiTouchRipple",slot:"Ripple"})`
  opacity: 0;
  position: absolute;

  &.${An.rippleVisible} {
    opacity: 0.3;
    transform: scale(1);
    animation-name: ${iC};
    animation-duration: ${Dd}ms;
    animation-timing-function: ${({theme:e})=>e.transitions.easing.easeInOut};
  }

  &.${An.ripplePulsate} {
    animation-duration: ${({theme:e})=>e.transitions.duration.shorter}ms;
  }

  & .${An.child} {
    opacity: 1;
    display: block;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: currentColor;
  }

  & .${An.childLeaving} {
    opacity: 0;
    animation-name: ${lC};
    animation-duration: ${Dd}ms;
    animation-timing-function: ${({theme:e})=>e.transitions.easing.easeInOut};
  }

  & .${An.childPulsate} {
    position: absolute;
    /* @noflip */
    left: 0px;
    top: 0;
    animation-name: ${sC};
    animation-duration: 2500ms;
    animation-timing-function: ${({theme:e})=>e.transitions.easing.easeInOut};
    animation-iteration-count: infinite;
    animation-delay: 200ms;
  }
`,dC=j.forwardRef(function(e,r){const o=Mt({props:e,name:"MuiTouchRipple"}),{center:i=!1,classes:l={},className:u,...d}=o,[f,m]=j.useState([]),g=j.useRef(0),S=j.useRef(null);j.useEffect(()=>{S.current&&(S.current(),S.current=null)},[f]);const y=j.useRef(!1),v=q2(),$=j.useRef(null),E=j.useRef(null),b=j.useCallback(x=>{const{pulsate:C,rippleX:k,rippleY:M,rippleSize:I,cb:L}=x;m(z=>[...z,q.jsx(cC,{classes:{ripple:ze(l.ripple,An.ripple),rippleVisible:ze(l.rippleVisible,An.rippleVisible),ripplePulsate:ze(l.ripplePulsate,An.ripplePulsate),child:ze(l.child,An.child),childLeaving:ze(l.childLeaving,An.childLeaving),childPulsate:ze(l.childPulsate,An.childPulsate)},timeout:Dd,pulsate:C,rippleX:k,rippleY:M,rippleSize:I},g.current)]),g.current+=1,S.current=L},[l]),T=j.useCallback((x={},C={},k=()=>{})=>{const{pulsate:M=!1,center:I=i||C.pulsate,fakeElement:L=!1}=C;if((x==null?void 0:x.type)==="mousedown"&&y.current){y.current=!1;return}(x==null?void 0:x.type)==="touchstart"&&(y.current=!0);const z=L?null:E.current,h=z?z.getBoundingClientRect():{width:0,height:0,left:0,top:0};let P,_,R;if(I||x===void 0||x.clientX===0&&x.clientY===0||!x.clientX&&!x.touches)P=Math.round(h.width/2),_=Math.round(h.height/2);else{const{clientX:F,clientY:G}=x.touches&&x.touches.length>0?x.touches[0]:x;P=Math.round(F-h.left),_=Math.round(G-h.top)}if(I)R=Math.sqrt((2*h.width**2+h.height**2)/3),R%2===0&&(R+=1);else{const F=Math.max(Math.abs((z?z.clientWidth:0)-P),P)*2+2,G=Math.max(Math.abs((z?z.clientHeight:0)-_),_)*2+2;R=Math.sqrt(F**2+G**2)}x!=null&&x.touches?$.current===null&&($.current=()=>{b({pulsate:M,rippleX:P,rippleY:_,rippleSize:R,cb:k})},v.start(aC,()=>{$.current&&($.current(),$.current=null)})):b({pulsate:M,rippleX:P,rippleY:_,rippleSize:R,cb:k})},[i,b,v]),N=j.useCallback(()=>{T({},{pulsate:!0})},[T]),A=j.useCallback((x,C)=>{if(v.clear(),(x==null?void 0:x.type)==="touchend"&&$.current){$.current(),$.current=null,v.start(0,()=>{A(x,C)});return}$.current=null,m(k=>k.length>0?k.slice(1):k),S.current=C},[v]);return j.useImperativeHandle(r,()=>({pulsate:N,start:T,stop:A}),[N,T,A]),q.jsx(uC,{className:ze(An.root,l.root,u),ref:E,...d,children:q.jsx(Ap,{component:null,exit:!0,children:f})})});function pC(e){return Rt("MuiButtonBase",e)}const fC=ht("MuiButtonBase",["root","disabled","focusVisible"]),mC=e=>{const{disabled:r,focusVisible:o,focusVisibleClassName:i,classes:l}=e,u=qt({root:["root",r&&"disabled",o&&"focusVisible"]},pC,l);return o&&i&&(u.root+=` ${i}`),u},hC=je("button",{name:"MuiButtonBase",slot:"Root",overridesResolver:(e,r)=>r.root})({display:"inline-flex",alignItems:"center",justifyContent:"center",position:"relative",boxSizing:"border-box",WebkitTapHighlightColor:"transparent",backgroundColor:"transparent",outline:0,border:0,margin:0,borderRadius:0,padding:0,cursor:"pointer",userSelect:"none",verticalAlign:"middle",MozAppearance:"none",WebkitAppearance:"none",textDecoration:"none",color:"inherit","&::-moz-focus-inner":{borderStyle:"none"},[`&.${fC.disabled}`]:{pointerEvents:"none",cursor:"default"},"@media print":{colorAdjust:"exact"}}),Fd=j.forwardRef(function(e,r){const o=Mt({props:e,name:"MuiButtonBase"}),{action:i,centerRipple:l=!1,children:u,className:d,component:f="button",disabled:m=!1,disableRipple:g=!1,disableTouchRipple:S=!1,focusRipple:y=!1,focusVisibleClassName:v,LinkComponent:$="a",onBlur:E,onClick:b,onContextMenu:T,onDragLeave:N,onFocus:A,onFocusVisible:x,onKeyDown:C,onKeyUp:k,onMouseDown:M,onMouseLeave:I,onMouseUp:L,onTouchEnd:z,onTouchMove:h,onTouchStart:P,tabIndex:_=0,TouchRippleProps:R,touchRippleRef:F,type:G,...H}=o,Z=j.useRef(null),W=nC(),ee=er(W.ref,F),[K,re]=j.useState(!1);m&&K&&re(!1),j.useImperativeHandle(i,()=>({focusVisible:()=>{re(!0),Z.current.focus()}}),[]);const le=W.shouldMount&&!g&&!m;j.useEffect(()=>{K&&y&&!g&&W.pulsate()},[g,y,K,W]);const fe=Dr(W,"start",M,S),ce=Dr(W,"stop",T,S),ie=Dr(W,"stop",N,S),he=Dr(W,"stop",L,S),be=Dr(W,"stop",ye=>{K&&ye.preventDefault(),I&&I(ye)},S),Se=Dr(W,"start",P,S),qe=Dr(W,"stop",z,S),Ne=Dr(W,"stop",h,S),Ee=Dr(W,"stop",ye=>{$g(ye.target)||re(!1),E&&E(ye)},!1),Me=mr(ye=>{Z.current||(Z.current=ye.currentTarget),$g(ye.target)&&(re(!0),x&&x(ye)),A&&A(ye)}),We=()=>{const ye=Z.current;return f&&f!=="button"&&!(ye.tagName==="A"&&ye.href)},dt=mr(ye=>{y&&!ye.repeat&&K&&ye.key===" "&&W.stop(ye,()=>{W.start(ye)}),ye.target===ye.currentTarget&&We()&&ye.key===" "&&ye.preventDefault(),C&&C(ye),ye.target===ye.currentTarget&&We()&&ye.key==="Enter"&&!m&&(ye.preventDefault(),b&&b(ye))}),Ie=mr(ye=>{y&&ye.key===" "&&K&&!ye.defaultPrevented&&W.stop(ye,()=>{W.pulsate(ye)}),k&&k(ye),b&&ye.target===ye.currentTarget&&We()&&ye.key===" "&&!ye.defaultPrevented&&b(ye)});let tt=f;tt==="button"&&(H.href||H.to)&&(tt=$);const de={};tt==="button"?(de.type=G===void 0?"button":G,de.disabled=m):(!H.href&&!H.to&&(de.role="button"),m&&(de["aria-disabled"]=m));const Wt=er(r,Z),_e={...o,centerRipple:l,component:f,disabled:m,disableRipple:g,disableTouchRipple:S,focusRipple:y,tabIndex:_,focusVisible:K},Ye=mC(_e);return q.jsxs(hC,{as:tt,className:ze(Ye.root,d),ownerState:_e,onBlur:Ee,onClick:b,onContextMenu:ce,onFocus:Me,onKeyDown:dt,onKeyUp:Ie,onMouseDown:fe,onMouseLeave:be,onMouseUp:he,onDragLeave:ie,onTouchEnd:qe,onTouchMove:Ne,onTouchStart:Se,ref:Wt,tabIndex:m?-1:_,type:G,...de,...H,children:[u,le?q.jsx(dC,{ref:ee,center:l,...R}):null]})});function Dr(e,r,o,i=!1){return mr(l=>(o&&o(l),i||e[r](l),!0))}function gC(e){return Rt("MuiCircularProgress",e)}ht("MuiCircularProgress",["root","determinate","indeterminate","colorPrimary","colorSecondary","svg","circle","circleDeterminate","circleIndeterminate","circleDisableShrink"]);const mo=44,Bd=cl`
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
`,Ld=cl`
  0% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 100px, 200px;
    stroke-dashoffset: -15px;
  }

  100% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: -126px;
  }
`,yC=typeof Bd!="string"?$p`
        animation: ${Bd} 1.4s linear infinite;
      `:null,vC=typeof Ld!="string"?$p`
        animation: ${Ld} 1.4s ease-in-out infinite;
      `:null,bC=e=>{const{classes:r,variant:o,color:i,disableShrink:l}=e,u={root:["root",o,`color${xe(i)}`],svg:["svg"],circle:["circle",`circle${xe(o)}`,l&&"circleDisableShrink"]};return qt(u,gC,r)},xC=je("span",{name:"MuiCircularProgress",slot:"Root",overridesResolver:(e,r)=>{const{ownerState:o}=e;return[r.root,r[o.variant],r[`color${xe(o.color)}`]]}})($t(({theme:e})=>({display:"inline-block",variants:[{props:{variant:"determinate"},style:{transition:e.transitions.create("transform")}},{props:{variant:"indeterminate"},style:yC||{animation:`${Bd} 1.4s linear infinite`}},...Object.entries(e.palette).filter(ho()).map(([r])=>({props:{color:r},style:{color:(e.vars||e).palette[r].main}}))]}))),wC=je("svg",{name:"MuiCircularProgress",slot:"Svg",overridesResolver:(e,r)=>r.svg})({display:"block"}),SC=je("circle",{name:"MuiCircularProgress",slot:"Circle",overridesResolver:(e,r)=>{const{ownerState:o}=e;return[r.circle,r[`circle${xe(o.variant)}`],o.disableShrink&&r.circleDisableShrink]}})($t(({theme:e})=>({stroke:"currentColor",variants:[{props:{variant:"determinate"},style:{transition:e.transitions.create("stroke-dashoffset")}},{props:{variant:"indeterminate"},style:{strokeDasharray:"80px, 200px",strokeDashoffset:0}},{props:({ownerState:r})=>r.variant==="indeterminate"&&!r.disableShrink,style:vC||{animation:`${Ld} 1.4s ease-in-out infinite`}}]}))),kC=j.forwardRef(function(e,r){const o=Mt({props:e,name:"MuiCircularProgress"}),{className:i,color:l="primary",disableShrink:u=!1,size:d=40,style:f,thickness:m=3.6,value:g=0,variant:S="indeterminate",...y}=o,v={...o,color:l,disableShrink:u,size:d,thickness:m,value:g,variant:S},$=bC(v),E={},b={},T={};if(S==="determinate"){const N=2*Math.PI*((mo-m)/2);E.strokeDasharray=N.toFixed(3),T["aria-valuenow"]=Math.round(g),E.strokeDashoffset=`${((100-g)/100*N).toFixed(3)}px`,b.transform="rotate(-90deg)"}return q.jsx(xC,{className:ze($.root,i),style:{width:d,height:d,...b,...f},ownerState:v,ref:r,role:"progressbar",...T,...y,children:q.jsx(wC,{className:$.svg,ownerState:v,viewBox:`${mo/2} ${mo/2} ${mo} ${mo}`,children:q.jsx(SC,{className:$.circle,style:E,ownerState:v,cx:mo,cy:mo,r:(mo-m)/2,fill:"none",strokeWidth:m})})})});function CC(e){return Rt("MuiIconButton",e)}const Eg=ht("MuiIconButton",["root","disabled","colorInherit","colorPrimary","colorSecondary","colorError","colorInfo","colorSuccess","colorWarning","edgeStart","edgeEnd","sizeSmall","sizeMedium","sizeLarge","loading","loadingIndicator","loadingWrapper"]),$C=e=>{const{classes:r,disabled:o,color:i,edge:l,size:u,loading:d}=e,f={root:["root",d&&"loading",o&&"disabled",i!=="default"&&`color${xe(i)}`,l&&`edge${xe(l)}`,`size${xe(u)}`],loadingIndicator:["loadingIndicator"],loadingWrapper:["loadingWrapper"]};return qt(f,CC,r)},EC=je(Fd,{name:"MuiIconButton",slot:"Root",overridesResolver:(e,r)=>{const{ownerState:o}=e;return[r.root,o.loading&&r.loading,o.color!=="default"&&r[`color${xe(o.color)}`],o.edge&&r[`edge${xe(o.edge)}`],r[`size${xe(o.size)}`]]}})($t(({theme:e})=>({textAlign:"center",flex:"0 0 auto",fontSize:e.typography.pxToRem(24),padding:8,borderRadius:"50%",color:(e.vars||e).palette.action.active,transition:e.transitions.create("background-color",{duration:e.transitions.duration.shortest}),variants:[{props:r=>!r.disableRipple,style:{"--IconButton-hoverBg":e.vars?`rgba(${e.vars.palette.action.activeChannel} / ${e.vars.palette.action.hoverOpacity})`:Ct(e.palette.action.active,e.palette.action.hoverOpacity),"&:hover":{backgroundColor:"var(--IconButton-hoverBg)","@media (hover: none)":{backgroundColor:"transparent"}}}},{props:{edge:"start"},style:{marginLeft:-12}},{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:{edge:"end"},style:{marginRight:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}}]})),$t(({theme:e})=>({variants:[{props:{color:"inherit"},style:{color:"inherit"}},...Object.entries(e.palette).filter(ho()).map(([r])=>({props:{color:r},style:{color:(e.vars||e).palette[r].main}})),...Object.entries(e.palette).filter(ho()).map(([r])=>({props:{color:r},style:{"--IconButton-hoverBg":e.vars?`rgba(${(e.vars||e).palette[r].mainChannel} / ${e.vars.palette.action.hoverOpacity})`:Ct((e.vars||e).palette[r].main,e.palette.action.hoverOpacity)}})),{props:{size:"small"},style:{padding:5,fontSize:e.typography.pxToRem(18)}},{props:{size:"large"},style:{padding:12,fontSize:e.typography.pxToRem(28)}}],[`&.${Eg.disabled}`]:{backgroundColor:"transparent",color:(e.vars||e).palette.action.disabled},[`&.${Eg.loading}`]:{color:"transparent"}}))),TC=je("span",{name:"MuiIconButton",slot:"LoadingIndicator",overridesResolver:(e,r)=>r.loadingIndicator})(({theme:e})=>({display:"none",position:"absolute",visibility:"visible",top:"50%",left:"50%",transform:"translate(-50%, -50%)",color:(e.vars||e).palette.action.disabled,variants:[{props:{loading:!0},style:{display:"flex"}}]})),S0=j.forwardRef(function(e,r){const o=Mt({props:e,name:"MuiIconButton"}),{edge:i=!1,children:l,className:u,color:d="default",disabled:f=!1,disableFocusRipple:m=!1,size:g="medium",id:S,loading:y=null,loadingIndicator:v,...$}=o,E=wp(S),b=v??q.jsx(kC,{"aria-labelledby":E,color:"inherit",size:16}),T={...o,edge:i,color:d,disabled:f,disableFocusRipple:m,loading:y,loadingIndicator:b,size:g},N=$C(T);return q.jsxs(EC,{id:y?E:S,className:ze(N.root,u),centerRipple:!0,focusRipple:!m,disabled:f||y,ref:r,...$,ownerState:T,children:[typeof y=="boolean"&&q.jsx("span",{className:N.loadingWrapper,style:{display:"contents"},children:q.jsx(TC,{className:N.loadingIndicator,ownerState:T,children:y&&b})}),l]})}),NC=Tp(q.jsx("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close");function Tg(e){return e.normalize("NFD").replace(/[\u0300-\u036f]/g,"")}function AC(e={}){const{ignoreAccents:r=!0,ignoreCase:o=!0,limit:i,matchFrom:l="any",stringify:u,trim:d=!1}=e;return(f,{inputValue:m,getOptionLabel:g})=>{let S=d?m.trim():m;o&&(S=S.toLowerCase()),r&&(S=Tg(S));const y=S?f.filter(v=>{let $=(u||g)(v);return o&&($=$.toLowerCase()),r&&($=Tg($)),l==="start"?$.startsWith(S):$.includes(S)}):f;return typeof i=="number"?y.slice(0,i):y}}const PC=AC(),Ng=5,_C=e=>{var r;return e.current!==null&&((r=e.current.parentElement)==null?void 0:r.contains(document.activeElement))},RC=[];function Ag(e,r,o){if(r||e==null)return"";const i=o(e);return typeof i=="string"?i:""}function MC(e){const{unstable_isActiveElementInListbox:r=_C,unstable_classNamePrefix:o="Mui",autoComplete:i=!1,autoHighlight:l=!1,autoSelect:u=!1,blurOnSelect:d=!1,clearOnBlur:f=!e.freeSolo,clearOnEscape:m=!1,componentName:g="useAutocomplete",defaultValue:S=e.multiple?RC:null,disableClearable:y=!1,disableCloseOnSelect:v=!1,disabled:$,disabledItemsFocusable:E=!1,disableListWrap:b=!1,filterOptions:T=PC,filterSelectedOptions:N=!1,freeSolo:A=!1,getOptionDisabled:x,getOptionKey:C,getOptionLabel:k=J=>J.label??J,groupBy:M,handleHomeEndKeys:I=!e.freeSolo,id:L,includeInputInList:z=!1,inputValue:h,isOptionEqualToValue:P=(J,X)=>J===X,multiple:_=!1,onChange:R,onClose:F,onHighlightChange:G,onInputChange:H,onOpen:Z,open:W,openOnFocus:ee=!1,options:K,readOnly:re=!1,selectOnFocus:le=!e.freeSolo,value:fe}=e,ce=wp(L);let ie=k;ie=J=>{const X=k(J);return typeof X!="string"?String(X):X};const he=j.useRef(!1),be=j.useRef(!0),Se=j.useRef(null),qe=j.useRef(null),[Ne,Ee]=j.useState(null),[Me,We]=j.useState(-1),dt=l?0:-1,Ie=j.useRef(dt),tt=j.useRef(Ag(S??fe,_,ie)).current,[de,Wt]=fd({controlled:fe,default:S,name:g}),[_e,Ye]=fd({controlled:h,default:tt,name:g,state:"inputValue"}),[ye,In]=j.useState(!1),gn=j.useCallback((J,X,ue)=>{if(!(_?de.length<X.length:X!==null)&&!f)return;const we=Ag(X,_,ie);_e!==we&&(Ye(we),H&&H(J,we,ue))},[ie,_e,_,H,Ye,f,de]),[Ot,br]=fd({controlled:W,default:!1,name:g,state:"open"}),[zn,Dn]=j.useState(!0),yn=!_&&de!=null&&_e===ie(de),jt=Ot&&!re,Ue=jt?T(K.filter(J=>!(N&&(_?de:[de]).some(X=>X!==null&&P(J,X)))),{inputValue:yn&&zn?"":_e,getOptionLabel:ie}):[],Et=W2({filteredOptions:Ue,value:de,inputValue:_e});j.useEffect(()=>{const J=de!==Et.value;ye&&!J||A&&!J||gn(null,de,"reset")},[de,gn,ye,Et.value,A]);const wo=Ot&&Ue.length>0&&!re,xr=mr(J=>{J===-1?Se.current.focus():Ne.querySelector(`[data-tag-index="${J}"]`).focus()});j.useEffect(()=>{_&&Me>de.length-1&&(We(-1),xr(-1))},[de,_,Me,xr]);function Jo(J,X){if(!qe.current||J<0||J>=Ue.length)return-1;let ue=J;for(;;){const we=qe.current.querySelector(`[data-option-index="${ue}"]`),Re=E?!1:!we||we.disabled||we.getAttribute("aria-disabled")==="true";if(we&&we.hasAttribute("tabindex")&&!Re)return ue;if(X==="next"?ue=(ue+1)%Ue.length:ue=(ue-1+Ue.length)%Ue.length,ue===J)return-1}}const Fn=mr(({event:J,index:X,reason:ue})=>{if(Ie.current=X,X===-1?Se.current.removeAttribute("aria-activedescendant"):Se.current.setAttribute("aria-activedescendant",`${ce}-option-${X}`),G&&["mouse","keyboard","touch"].includes(ue)&&G(J,X===-1?null:Ue[X],ue),!qe.current)return;const we=qe.current.querySelector(`[role="option"].${o}-focused`);we&&(we.classList.remove(`${o}-focused`),we.classList.remove(`${o}-focusVisible`));let Re=qe.current;if(qe.current.getAttribute("role")!=="listbox"&&(Re=qe.current.parentElement.querySelector('[role="listbox"]')),!Re)return;if(X===-1){Re.scrollTop=0;return}const Ke=qe.current.querySelector(`[data-option-index="${X}"]`);if(Ke&&(Ke.classList.add(`${o}-focused`),ue==="keyboard"&&Ke.classList.add(`${o}-focusVisible`),Re.scrollHeight>Re.clientHeight&&ue!=="mouse"&&ue!=="touch")){const pt=Ke,Tr=Re.clientHeight+Re.scrollTop,Vr=pt.offsetTop+pt.offsetHeight;Vr>Tr?Re.scrollTop=Vr-Re.clientHeight:pt.offsetTop-pt.offsetHeight*(M?1.3:0)<Re.scrollTop&&(Re.scrollTop=pt.offsetTop-pt.offsetHeight*(M?1.3:0))}}),Ut=mr(({event:J,diff:X,direction:ue="next",reason:we})=>{if(!jt)return;const Re=Jo((()=>{const Ke=Ue.length-1;if(X==="reset")return dt;if(X==="start")return 0;if(X==="end")return Ke;const pt=Ie.current+X;return pt<0?pt===-1&&z?-1:b&&Ie.current!==-1||Math.abs(X)>1?0:Ke:pt>Ke?pt===Ke+1&&z?-1:b||Math.abs(X)>1?Ke:0:pt})(),ue);if(Fn({index:Re,reason:we,event:J}),i&&X!=="reset")if(Re===-1)Se.current.value=_e;else{const Ke=ie(Ue[Re]);Se.current.value=Ke,Ke.toLowerCase().indexOf(_e.toLowerCase())===0&&_e.length>0&&Se.current.setSelectionRange(_e.length,Ke.length)}}),So=()=>{const J=(X,ue)=>{const we=X?ie(X):"",Re=ue?ie(ue):"";return we===Re};if(Ie.current!==-1&&Et.filteredOptions&&Et.filteredOptions.length!==Ue.length&&Et.inputValue===_e&&(_?de.length===Et.value.length&&Et.value.every((X,ue)=>ie(de[ue])===ie(X)):J(Et.value,de))){const X=Et.filteredOptions[Ie.current];if(X)return Ue.findIndex(ue=>ie(ue)===ie(X))}return-1},wr=j.useCallback(()=>{if(!jt)return;const J=So();if(J!==-1){Ie.current=J;return}const X=_?de[0]:de;if(Ue.length===0||X==null){Ut({diff:"reset"});return}if(qe.current){if(X!=null){const ue=Ue[Ie.current];if(_&&ue&&de.findIndex(Re=>P(ue,Re))!==-1)return;const we=Ue.findIndex(Re=>P(Re,X));we===-1?Ut({diff:"reset"}):Fn({index:we});return}if(Ie.current>=Ue.length-1){Fn({index:Ue.length-1});return}Fn({index:Ie.current})}},[Ue.length,_?!1:de,N,Ut,Fn,jt,_e,_]),Sr=mr(J=>{Md(qe,J),J&&wr()});j.useEffect(()=>{wr()},[wr]);const an=J=>{Ot||(br(!0),Dn(!0),Z&&Z(J))},Gt=(J,X)=>{Ot&&(br(!1),F&&F(J,X))},Qt=(J,X,ue,we)=>{if(_){if(de.length===X.length&&de.every((Re,Ke)=>Re===X[Ke]))return}else if(de===X)return;R&&R(J,X,ue,we),Wt(X)},vn=j.useRef(!1),Bn=(J,X,ue="selectOption",we="options")=>{let Re=ue,Ke=X;if(_){Ke=Array.isArray(de)?de.slice():[];const pt=Ke.findIndex(Tr=>P(X,Tr));pt===-1?Ke.push(X):we!=="freeSolo"&&(Ke.splice(pt,1),Re="removeOption")}gn(J,Ke,Re),Qt(J,Ke,Re,{option:X}),!v&&(!J||!J.ctrlKey&&!J.metaKey)&&Gt(J,Re),(d===!0||d==="touch"&&vn.current||d==="mouse"&&!vn.current)&&Se.current.blur()};function Zo(J,X){if(J===-1)return-1;let ue=J;for(;;){if(X==="next"&&ue===de.length||X==="previous"&&ue===-1)return-1;const we=Ne.querySelector(`[data-tag-index="${ue}"]`);if(!we||!we.hasAttribute("tabindex")||we.disabled||we.getAttribute("aria-disabled")==="true")ue+=X==="next"?1:-1;else return ue}}const tr=(J,X)=>{if(!_)return;_e===""&&Gt(J,"toggleInput");let ue=Me;Me===-1?_e===""&&X==="previous"&&(ue=de.length-1):(ue+=X==="next"?1:-1,ue<0&&(ue=0),ue===de.length&&(ue=-1)),ue=Zo(ue,X),We(ue),xr(ue)},ko=J=>{he.current=!0,Ye(""),H&&H(J,"","clear"),Qt(J,_?[]:null,"clear")},Co=J=>X=>{if(J.onKeyDown&&J.onKeyDown(X),!X.defaultMuiPrevented&&(Me!==-1&&!["ArrowLeft","ArrowRight"].includes(X.key)&&(We(-1),xr(-1)),X.which!==229))switch(X.key){case"Home":jt&&I&&(X.preventDefault(),Ut({diff:"start",direction:"next",reason:"keyboard",event:X}));break;case"End":jt&&I&&(X.preventDefault(),Ut({diff:"end",direction:"previous",reason:"keyboard",event:X}));break;case"PageUp":X.preventDefault(),Ut({diff:-Ng,direction:"previous",reason:"keyboard",event:X}),an(X);break;case"PageDown":X.preventDefault(),Ut({diff:Ng,direction:"next",reason:"keyboard",event:X}),an(X);break;case"ArrowDown":X.preventDefault(),Ut({diff:1,direction:"next",reason:"keyboard",event:X}),an(X);break;case"ArrowUp":X.preventDefault(),Ut({diff:-1,direction:"previous",reason:"keyboard",event:X}),an(X);break;case"ArrowLeft":tr(X,"previous");break;case"ArrowRight":tr(X,"next");break;case"Enter":if(Ie.current!==-1&&jt){const ue=Ue[Ie.current],we=x?x(ue):!1;if(X.preventDefault(),we)return;Bn(X,ue,"selectOption"),i&&Se.current.setSelectionRange(Se.current.value.length,Se.current.value.length)}else A&&_e!==""&&yn===!1&&(_&&X.preventDefault(),Bn(X,_e,"createOption","freeSolo"));break;case"Escape":jt?(X.preventDefault(),X.stopPropagation(),Gt(X,"escape")):m&&(_e!==""||_&&de.length>0)&&(X.preventDefault(),X.stopPropagation(),ko(X));break;case"Backspace":if(_&&!re&&_e===""&&de.length>0){const ue=Me===-1?de.length-1:Me,we=de.slice();we.splice(ue,1),Qt(X,we,"removeOption",{option:de[ue]})}break;case"Delete":if(_&&!re&&_e===""&&de.length>0&&Me!==-1){const ue=Me,we=de.slice();we.splice(ue,1),Qt(X,we,"removeOption",{option:de[ue]})}break}},kr=J=>{In(!0),ee&&!he.current&&an(J)},bn=J=>{if(r(qe)){Se.current.focus();return}In(!1),be.current=!0,he.current=!1,u&&Ie.current!==-1&&jt?Bn(J,Ue[Ie.current],"blur"):u&&A&&_e!==""?Bn(J,_e,"blur","freeSolo"):f&&gn(J,de,"blur"),Gt(J,"blur")},Cr=J=>{const X=J.target.value;_e!==X&&(Ye(X),Dn(!1),H&&H(J,X,"input")),X===""?!y&&!_&&Qt(J,null,"clear"):an(J)},$r=J=>{const X=Number(J.currentTarget.getAttribute("data-option-index"));Ie.current!==X&&Fn({event:J,index:X,reason:"mouse"})},Ua=J=>{Fn({event:J,index:Number(J.currentTarget.getAttribute("data-option-index")),reason:"touch"}),vn.current=!0},Er=J=>{const X=Number(J.currentTarget.getAttribute("data-option-index"));Bn(J,Ue[X],"selectOption"),vn.current=!1},nr=J=>X=>{const ue=de.slice();ue.splice(J,1),Qt(X,ue,"removeOption",{option:de[J]})},it=J=>{Ot?Gt(J,"toggleInput"):an(J)},lt=J=>{J.currentTarget.contains(J.target)&&J.target.getAttribute("id")!==ce&&J.preventDefault()},rr=J=>{J.currentTarget.contains(J.target)&&(Se.current.focus(),le&&be.current&&Se.current.selectionEnd-Se.current.selectionStart===0&&Se.current.select(),be.current=!1)},Qa=J=>{!$&&(_e===""||!Ot)&&it(J)};let $o=A&&_e.length>0;$o=$o||(_?de.length>0:de!==null);let xn=Ue;return M&&(xn=Ue.reduce((J,X,ue)=>{const we=M(X);return J.length>0&&J[J.length-1].group===we?J[J.length-1].options.push(X):J.push({key:ue,index:ue,group:we,options:[X]}),J},[])),$&&ye&&bn(),{getRootProps:(J={})=>({...J,onKeyDown:Co(J),onMouseDown:lt,onClick:rr}),getInputLabelProps:()=>({id:`${ce}-label`,htmlFor:ce}),getInputProps:()=>({id:ce,value:_e,onBlur:bn,onFocus:kr,onChange:Cr,onMouseDown:Qa,"aria-activedescendant":jt?"":null,"aria-autocomplete":i?"both":"list","aria-controls":wo?`${ce}-listbox`:void 0,"aria-expanded":wo,autoComplete:"off",ref:Se,autoCapitalize:"none",spellCheck:"false",role:"combobox",disabled:$}),getClearProps:()=>({tabIndex:-1,type:"button",onClick:ko}),getPopupIndicatorProps:()=>({tabIndex:-1,type:"button",onClick:it}),getTagProps:({index:J})=>({key:J,"data-tag-index":J,tabIndex:-1,...!re&&{onDelete:nr(J)}}),getListboxProps:()=>({role:"listbox",id:`${ce}-listbox`,"aria-labelledby":`${ce}-label`,ref:Sr,onMouseDown:J=>{J.preventDefault()}}),getOptionProps:({index:J,option:X})=>{const ue=(_?de:[de]).some(Re=>Re!=null&&P(X,Re)),we=x?x(X):!1;return{key:(C==null?void 0:C(X))??ie(X),tabIndex:-1,role:"option",id:`${ce}-option-${J}`,onMouseMove:$r,onClick:Er,onTouchStart:Ua,"data-option-index":J,"aria-disabled":we,"aria-selected":ue}},id:ce,inputValue:_e,value:de,dirty:$o,expanded:jt&&Ne,popupOpen:jt,focused:ye||Me!==-1,anchorEl:Ne,setAnchorEl:Ee,focusedTag:Me,groupedOptions:xn}}var tn="top",Mn="bottom",On="right",nn="left",Pp="auto",dl=[tn,Mn,On,nn],Da="start",el="end",OC="clippingParents",k0="viewport",Pi="popper",jC="reference",Pg=dl.reduce(function(e,r){return e.concat([r+"-"+Da,r+"-"+el])},[]),C0=[].concat(dl,[Pp]).reduce(function(e,r){return e.concat([r,r+"-"+Da,r+"-"+el])},[]),IC="beforeRead",zC="read",DC="afterRead",FC="beforeMain",BC="main",LC="afterMain",qC="beforeWrite",WC="write",UC="afterWrite",QC=[IC,zC,DC,FC,BC,LC,qC,WC,UC];function vr(e){return e?(e.nodeName||"").toLowerCase():null}function hn(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var r=e.ownerDocument;return r&&r.defaultView||window}return e}function Ho(e){var r=hn(e).Element;return e instanceof r||e instanceof Element}function Rn(e){var r=hn(e).HTMLElement;return e instanceof r||e instanceof HTMLElement}function _p(e){if(typeof ShadowRoot>"u")return!1;var r=hn(e).ShadowRoot;return e instanceof r||e instanceof ShadowRoot}function VC(e){var r=e.state;Object.keys(r.elements).forEach(function(o){var i=r.styles[o]||{},l=r.attributes[o]||{},u=r.elements[o];!Rn(u)||!vr(u)||(Object.assign(u.style,i),Object.keys(l).forEach(function(d){var f=l[d];f===!1?u.removeAttribute(d):u.setAttribute(d,f===!0?"":f)}))})}function KC(e){var r=e.state,o={popper:{position:r.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(r.elements.popper.style,o.popper),r.styles=o,r.elements.arrow&&Object.assign(r.elements.arrow.style,o.arrow),function(){Object.keys(r.elements).forEach(function(i){var l=r.elements[i],u=r.attributes[i]||{},d=Object.keys(r.styles.hasOwnProperty(i)?r.styles[i]:o[i]),f=d.reduce(function(m,g){return m[g]="",m},{});!Rn(l)||!vr(l)||(Object.assign(l.style,f),Object.keys(u).forEach(function(m){l.removeAttribute(m)}))})}}const GC={name:"applyStyles",enabled:!0,phase:"write",fn:VC,effect:KC,requires:["computeStyles"]};function gr(e){return e.split("-")[0]}var Vo=Math.max,Hs=Math.min,Fa=Math.round;function qd(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(r){return r.brand+"/"+r.version}).join(" "):navigator.userAgent}function $0(){return!/^((?!chrome|android).)*safari/i.test(qd())}function Ba(e,r,o){r===void 0&&(r=!1),o===void 0&&(o=!1);var i=e.getBoundingClientRect(),l=1,u=1;r&&Rn(e)&&(l=e.offsetWidth>0&&Fa(i.width)/e.offsetWidth||1,u=e.offsetHeight>0&&Fa(i.height)/e.offsetHeight||1);var d=Ho(e)?hn(e):window,f=d.visualViewport,m=!$0()&&o,g=(i.left+(m&&f?f.offsetLeft:0))/l,S=(i.top+(m&&f?f.offsetTop:0))/u,y=i.width/l,v=i.height/u;return{width:y,height:v,top:S,right:g+y,bottom:S+v,left:g,x:g,y:S}}function Rp(e){var r=Ba(e),o=e.offsetWidth,i=e.offsetHeight;return Math.abs(r.width-o)<=1&&(o=r.width),Math.abs(r.height-i)<=1&&(i=r.height),{x:e.offsetLeft,y:e.offsetTop,width:o,height:i}}function E0(e,r){var o=r.getRootNode&&r.getRootNode();if(e.contains(r))return!0;if(o&&_p(o)){var i=r;do{if(i&&e.isSameNode(i))return!0;i=i.parentNode||i.host}while(i)}return!1}function Ur(e){return hn(e).getComputedStyle(e)}function YC(e){return["table","td","th"].indexOf(vr(e))>=0}function xo(e){return((Ho(e)?e.ownerDocument:e.document)||window.document).documentElement}function vu(e){return vr(e)==="html"?e:e.assignedSlot||e.parentNode||(_p(e)?e.host:null)||xo(e)}function _g(e){return!Rn(e)||Ur(e).position==="fixed"?null:e.offsetParent}function XC(e){var r=/firefox/i.test(qd()),o=/Trident/i.test(qd());if(o&&Rn(e)){var i=Ur(e);if(i.position==="fixed")return null}var l=vu(e);for(_p(l)&&(l=l.host);Rn(l)&&["html","body"].indexOf(vr(l))<0;){var u=Ur(l);if(u.transform!=="none"||u.perspective!=="none"||u.contain==="paint"||["transform","perspective"].indexOf(u.willChange)!==-1||r&&u.willChange==="filter"||r&&u.filter&&u.filter!=="none")return l;l=l.parentNode}return null}function pl(e){for(var r=hn(e),o=_g(e);o&&YC(o)&&Ur(o).position==="static";)o=_g(o);return o&&(vr(o)==="html"||vr(o)==="body"&&Ur(o).position==="static")?r:o||XC(e)||r}function Mp(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Fi(e,r,o){return Vo(e,Hs(r,o))}function HC(e,r,o){var i=Fi(e,r,o);return i>o?o:i}function T0(){return{top:0,right:0,bottom:0,left:0}}function N0(e){return Object.assign({},T0(),e)}function A0(e,r){return r.reduce(function(o,i){return o[i]=e,o},{})}var JC=function(e,r){return e=typeof e=="function"?e(Object.assign({},r.rects,{placement:r.placement})):e,N0(typeof e!="number"?e:A0(e,dl))};function ZC(e){var r,o=e.state,i=e.name,l=e.options,u=o.elements.arrow,d=o.modifiersData.popperOffsets,f=gr(o.placement),m=Mp(f),g=[nn,On].indexOf(f)>=0,S=g?"height":"width";if(!(!u||!d)){var y=JC(l.padding,o),v=Rp(u),$=m==="y"?tn:nn,E=m==="y"?Mn:On,b=o.rects.reference[S]+o.rects.reference[m]-d[m]-o.rects.popper[S],T=d[m]-o.rects.reference[m],N=pl(u),A=N?m==="y"?N.clientHeight||0:N.clientWidth||0:0,x=b/2-T/2,C=y[$],k=A-v[S]-y[E],M=A/2-v[S]/2+x,I=Fi(C,M,k),L=m;o.modifiersData[i]=(r={},r[L]=I,r.centerOffset=I-M,r)}}function e$(e){var r=e.state,o=e.options,i=o.element,l=i===void 0?"[data-popper-arrow]":i;l!=null&&(typeof l=="string"&&(l=r.elements.popper.querySelector(l),!l)||E0(r.elements.popper,l)&&(r.elements.arrow=l))}const t$={name:"arrow",enabled:!0,phase:"main",fn:ZC,effect:e$,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function La(e){return e.split("-")[1]}var n$={top:"auto",right:"auto",bottom:"auto",left:"auto"};function r$(e,r){var o=e.x,i=e.y,l=r.devicePixelRatio||1;return{x:Fa(o*l)/l||0,y:Fa(i*l)/l||0}}function Rg(e){var r,o=e.popper,i=e.popperRect,l=e.placement,u=e.variation,d=e.offsets,f=e.position,m=e.gpuAcceleration,g=e.adaptive,S=e.roundOffsets,y=e.isFixed,v=d.x,$=v===void 0?0:v,E=d.y,b=E===void 0?0:E,T=typeof S=="function"?S({x:$,y:b}):{x:$,y:b};$=T.x,b=T.y;var N=d.hasOwnProperty("x"),A=d.hasOwnProperty("y"),x=nn,C=tn,k=window;if(g){var M=pl(o),I="clientHeight",L="clientWidth";if(M===hn(o)&&(M=xo(o),Ur(M).position!=="static"&&f==="absolute"&&(I="scrollHeight",L="scrollWidth")),M=M,l===tn||(l===nn||l===On)&&u===el){C=Mn;var z=y&&M===k&&k.visualViewport?k.visualViewport.height:M[I];b-=z-i.height,b*=m?1:-1}if(l===nn||(l===tn||l===Mn)&&u===el){x=On;var h=y&&M===k&&k.visualViewport?k.visualViewport.width:M[L];$-=h-i.width,$*=m?1:-1}}var P=Object.assign({position:f},g&&n$),_=S===!0?r$({x:$,y:b},hn(o)):{x:$,y:b};if($=_.x,b=_.y,m){var R;return Object.assign({},P,(R={},R[C]=A?"0":"",R[x]=N?"0":"",R.transform=(k.devicePixelRatio||1)<=1?"translate("+$+"px, "+b+"px)":"translate3d("+$+"px, "+b+"px, 0)",R))}return Object.assign({},P,(r={},r[C]=A?b+"px":"",r[x]=N?$+"px":"",r.transform="",r))}function o$(e){var r=e.state,o=e.options,i=o.gpuAcceleration,l=i===void 0?!0:i,u=o.adaptive,d=u===void 0?!0:u,f=o.roundOffsets,m=f===void 0?!0:f,g={placement:gr(r.placement),variation:La(r.placement),popper:r.elements.popper,popperRect:r.rects.popper,gpuAcceleration:l,isFixed:r.options.strategy==="fixed"};r.modifiersData.popperOffsets!=null&&(r.styles.popper=Object.assign({},r.styles.popper,Rg(Object.assign({},g,{offsets:r.modifiersData.popperOffsets,position:r.options.strategy,adaptive:d,roundOffsets:m})))),r.modifiersData.arrow!=null&&(r.styles.arrow=Object.assign({},r.styles.arrow,Rg(Object.assign({},g,{offsets:r.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:m})))),r.attributes.popper=Object.assign({},r.attributes.popper,{"data-popper-placement":r.placement})}const a$={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:o$,data:{}};var As={passive:!0};function i$(e){var r=e.state,o=e.instance,i=e.options,l=i.scroll,u=l===void 0?!0:l,d=i.resize,f=d===void 0?!0:d,m=hn(r.elements.popper),g=[].concat(r.scrollParents.reference,r.scrollParents.popper);return u&&g.forEach(function(S){S.addEventListener("scroll",o.update,As)}),f&&m.addEventListener("resize",o.update,As),function(){u&&g.forEach(function(S){S.removeEventListener("scroll",o.update,As)}),f&&m.removeEventListener("resize",o.update,As)}}const l$={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:i$,data:{}};var s$={left:"right",right:"left",bottom:"top",top:"bottom"};function zs(e){return e.replace(/left|right|bottom|top/g,function(r){return s$[r]})}var u$={start:"end",end:"start"};function Mg(e){return e.replace(/start|end/g,function(r){return u$[r]})}function Op(e){var r=hn(e),o=r.pageXOffset,i=r.pageYOffset;return{scrollLeft:o,scrollTop:i}}function jp(e){return Ba(xo(e)).left+Op(e).scrollLeft}function c$(e,r){var o=hn(e),i=xo(e),l=o.visualViewport,u=i.clientWidth,d=i.clientHeight,f=0,m=0;if(l){u=l.width,d=l.height;var g=$0();(g||!g&&r==="fixed")&&(f=l.offsetLeft,m=l.offsetTop)}return{width:u,height:d,x:f+jp(e),y:m}}function d$(e){var r,o=xo(e),i=Op(e),l=(r=e.ownerDocument)==null?void 0:r.body,u=Vo(o.scrollWidth,o.clientWidth,l?l.scrollWidth:0,l?l.clientWidth:0),d=Vo(o.scrollHeight,o.clientHeight,l?l.scrollHeight:0,l?l.clientHeight:0),f=-i.scrollLeft+jp(e),m=-i.scrollTop;return Ur(l||o).direction==="rtl"&&(f+=Vo(o.clientWidth,l?l.clientWidth:0)-u),{width:u,height:d,x:f,y:m}}function Ip(e){var r=Ur(e),o=r.overflow,i=r.overflowX,l=r.overflowY;return/auto|scroll|overlay|hidden/.test(o+l+i)}function P0(e){return["html","body","#document"].indexOf(vr(e))>=0?e.ownerDocument.body:Rn(e)&&Ip(e)?e:P0(vu(e))}function Bi(e,r){var o;r===void 0&&(r=[]);var i=P0(e),l=i===((o=e.ownerDocument)==null?void 0:o.body),u=hn(i),d=l?[u].concat(u.visualViewport||[],Ip(i)?i:[]):i,f=r.concat(d);return l?f:f.concat(Bi(vu(d)))}function Wd(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function p$(e,r){var o=Ba(e,!1,r==="fixed");return o.top=o.top+e.clientTop,o.left=o.left+e.clientLeft,o.bottom=o.top+e.clientHeight,o.right=o.left+e.clientWidth,o.width=e.clientWidth,o.height=e.clientHeight,o.x=o.left,o.y=o.top,o}function Og(e,r,o){return r===k0?Wd(c$(e,o)):Ho(r)?p$(r,o):Wd(d$(xo(e)))}function f$(e){var r=Bi(vu(e)),o=["absolute","fixed"].indexOf(Ur(e).position)>=0,i=o&&Rn(e)?pl(e):e;return Ho(i)?r.filter(function(l){return Ho(l)&&E0(l,i)&&vr(l)!=="body"}):[]}function m$(e,r,o,i){var l=r==="clippingParents"?f$(e):[].concat(r),u=[].concat(l,[o]),d=u[0],f=u.reduce(function(m,g){var S=Og(e,g,i);return m.top=Vo(S.top,m.top),m.right=Hs(S.right,m.right),m.bottom=Hs(S.bottom,m.bottom),m.left=Vo(S.left,m.left),m},Og(e,d,i));return f.width=f.right-f.left,f.height=f.bottom-f.top,f.x=f.left,f.y=f.top,f}function _0(e){var r=e.reference,o=e.element,i=e.placement,l=i?gr(i):null,u=i?La(i):null,d=r.x+r.width/2-o.width/2,f=r.y+r.height/2-o.height/2,m;switch(l){case tn:m={x:d,y:r.y-o.height};break;case Mn:m={x:d,y:r.y+r.height};break;case On:m={x:r.x+r.width,y:f};break;case nn:m={x:r.x-o.width,y:f};break;default:m={x:r.x,y:r.y}}var g=l?Mp(l):null;if(g!=null){var S=g==="y"?"height":"width";switch(u){case Da:m[g]=m[g]-(r[S]/2-o[S]/2);break;case el:m[g]=m[g]+(r[S]/2-o[S]/2);break}}return m}function tl(e,r){r===void 0&&(r={});var o=r,i=o.placement,l=i===void 0?e.placement:i,u=o.strategy,d=u===void 0?e.strategy:u,f=o.boundary,m=f===void 0?OC:f,g=o.rootBoundary,S=g===void 0?k0:g,y=o.elementContext,v=y===void 0?Pi:y,$=o.altBoundary,E=$===void 0?!1:$,b=o.padding,T=b===void 0?0:b,N=N0(typeof T!="number"?T:A0(T,dl)),A=v===Pi?jC:Pi,x=e.rects.popper,C=e.elements[E?A:v],k=m$(Ho(C)?C:C.contextElement||xo(e.elements.popper),m,S,d),M=Ba(e.elements.reference),I=_0({reference:M,element:x,placement:l}),L=Wd(Object.assign({},x,I)),z=v===Pi?L:M,h={top:k.top-z.top+N.top,bottom:z.bottom-k.bottom+N.bottom,left:k.left-z.left+N.left,right:z.right-k.right+N.right},P=e.modifiersData.offset;if(v===Pi&&P){var _=P[l];Object.keys(h).forEach(function(R){var F=[On,Mn].indexOf(R)>=0?1:-1,G=[tn,Mn].indexOf(R)>=0?"y":"x";h[R]+=_[G]*F})}return h}function h$(e,r){r===void 0&&(r={});var o=r,i=o.placement,l=o.boundary,u=o.rootBoundary,d=o.padding,f=o.flipVariations,m=o.allowedAutoPlacements,g=m===void 0?C0:m,S=La(i),y=S?f?Pg:Pg.filter(function(E){return La(E)===S}):dl,v=y.filter(function(E){return g.indexOf(E)>=0});v.length===0&&(v=y);var $=v.reduce(function(E,b){return E[b]=tl(e,{placement:b,boundary:l,rootBoundary:u,padding:d})[gr(b)],E},{});return Object.keys($).sort(function(E,b){return $[E]-$[b]})}function g$(e){if(gr(e)===Pp)return[];var r=zs(e);return[Mg(e),r,Mg(r)]}function y$(e){var r=e.state,o=e.options,i=e.name;if(!r.modifiersData[i]._skip){for(var l=o.mainAxis,u=l===void 0?!0:l,d=o.altAxis,f=d===void 0?!0:d,m=o.fallbackPlacements,g=o.padding,S=o.boundary,y=o.rootBoundary,v=o.altBoundary,$=o.flipVariations,E=$===void 0?!0:$,b=o.allowedAutoPlacements,T=r.options.placement,N=gr(T),A=N===T,x=m||(A||!E?[zs(T)]:g$(T)),C=[T].concat(x).reduce(function(ce,ie){return ce.concat(gr(ie)===Pp?h$(r,{placement:ie,boundary:S,rootBoundary:y,padding:g,flipVariations:E,allowedAutoPlacements:b}):ie)},[]),k=r.rects.reference,M=r.rects.popper,I=new Map,L=!0,z=C[0],h=0;h<C.length;h++){var P=C[h],_=gr(P),R=La(P)===Da,F=[tn,Mn].indexOf(_)>=0,G=F?"width":"height",H=tl(r,{placement:P,boundary:S,rootBoundary:y,altBoundary:v,padding:g}),Z=F?R?On:nn:R?Mn:tn;k[G]>M[G]&&(Z=zs(Z));var W=zs(Z),ee=[];if(u&&ee.push(H[_]<=0),f&&ee.push(H[Z]<=0,H[W]<=0),ee.every(function(ce){return ce})){z=P,L=!1;break}I.set(P,ee)}if(L)for(var K=E?3:1,re=function(ce){var ie=C.find(function(he){var be=I.get(he);if(be)return be.slice(0,ce).every(function(Se){return Se})});if(ie)return z=ie,"break"},le=K;le>0;le--){var fe=re(le);if(fe==="break")break}r.placement!==z&&(r.modifiersData[i]._skip=!0,r.placement=z,r.reset=!0)}}const v$={name:"flip",enabled:!0,phase:"main",fn:y$,requiresIfExists:["offset"],data:{_skip:!1}};function jg(e,r,o){return o===void 0&&(o={x:0,y:0}),{top:e.top-r.height-o.y,right:e.right-r.width+o.x,bottom:e.bottom-r.height+o.y,left:e.left-r.width-o.x}}function Ig(e){return[tn,On,Mn,nn].some(function(r){return e[r]>=0})}function b$(e){var r=e.state,o=e.name,i=r.rects.reference,l=r.rects.popper,u=r.modifiersData.preventOverflow,d=tl(r,{elementContext:"reference"}),f=tl(r,{altBoundary:!0}),m=jg(d,i),g=jg(f,l,u),S=Ig(m),y=Ig(g);r.modifiersData[o]={referenceClippingOffsets:m,popperEscapeOffsets:g,isReferenceHidden:S,hasPopperEscaped:y},r.attributes.popper=Object.assign({},r.attributes.popper,{"data-popper-reference-hidden":S,"data-popper-escaped":y})}const x$={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:b$};function w$(e,r,o){var i=gr(e),l=[nn,tn].indexOf(i)>=0?-1:1,u=typeof o=="function"?o(Object.assign({},r,{placement:e})):o,d=u[0],f=u[1];return d=d||0,f=(f||0)*l,[nn,On].indexOf(i)>=0?{x:f,y:d}:{x:d,y:f}}function S$(e){var r=e.state,o=e.options,i=e.name,l=o.offset,u=l===void 0?[0,0]:l,d=C0.reduce(function(S,y){return S[y]=w$(y,r.rects,u),S},{}),f=d[r.placement],m=f.x,g=f.y;r.modifiersData.popperOffsets!=null&&(r.modifiersData.popperOffsets.x+=m,r.modifiersData.popperOffsets.y+=g),r.modifiersData[i]=d}const k$={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:S$};function C$(e){var r=e.state,o=e.name;r.modifiersData[o]=_0({reference:r.rects.reference,element:r.rects.popper,placement:r.placement})}const $$={name:"popperOffsets",enabled:!0,phase:"read",fn:C$,data:{}};function E$(e){return e==="x"?"y":"x"}function T$(e){var r=e.state,o=e.options,i=e.name,l=o.mainAxis,u=l===void 0?!0:l,d=o.altAxis,f=d===void 0?!1:d,m=o.boundary,g=o.rootBoundary,S=o.altBoundary,y=o.padding,v=o.tether,$=v===void 0?!0:v,E=o.tetherOffset,b=E===void 0?0:E,T=tl(r,{boundary:m,rootBoundary:g,padding:y,altBoundary:S}),N=gr(r.placement),A=La(r.placement),x=!A,C=Mp(N),k=E$(C),M=r.modifiersData.popperOffsets,I=r.rects.reference,L=r.rects.popper,z=typeof b=="function"?b(Object.assign({},r.rects,{placement:r.placement})):b,h=typeof z=="number"?{mainAxis:z,altAxis:z}:Object.assign({mainAxis:0,altAxis:0},z),P=r.modifiersData.offset?r.modifiersData.offset[r.placement]:null,_={x:0,y:0};if(M){if(u){var R,F=C==="y"?tn:nn,G=C==="y"?Mn:On,H=C==="y"?"height":"width",Z=M[C],W=Z+T[F],ee=Z-T[G],K=$?-L[H]/2:0,re=A===Da?I[H]:L[H],le=A===Da?-L[H]:-I[H],fe=r.elements.arrow,ce=$&&fe?Rp(fe):{width:0,height:0},ie=r.modifiersData["arrow#persistent"]?r.modifiersData["arrow#persistent"].padding:T0(),he=ie[F],be=ie[G],Se=Fi(0,I[H],ce[H]),qe=x?I[H]/2-K-Se-he-h.mainAxis:re-Se-he-h.mainAxis,Ne=x?-I[H]/2+K+Se+be+h.mainAxis:le+Se+be+h.mainAxis,Ee=r.elements.arrow&&pl(r.elements.arrow),Me=Ee?C==="y"?Ee.clientTop||0:Ee.clientLeft||0:0,We=(R=P==null?void 0:P[C])!=null?R:0,dt=Z+qe-We-Me,Ie=Z+Ne-We,tt=Fi($?Hs(W,dt):W,Z,$?Vo(ee,Ie):ee);M[C]=tt,_[C]=tt-Z}if(f){var de,Wt=C==="x"?tn:nn,_e=C==="x"?Mn:On,Ye=M[k],ye=k==="y"?"height":"width",In=Ye+T[Wt],gn=Ye-T[_e],Ot=[tn,nn].indexOf(N)!==-1,br=(de=P==null?void 0:P[k])!=null?de:0,zn=Ot?In:Ye-I[ye]-L[ye]-br+h.altAxis,Dn=Ot?Ye+I[ye]+L[ye]-br-h.altAxis:gn,yn=$&&Ot?HC(zn,Ye,Dn):Fi($?zn:In,Ye,$?Dn:gn);M[k]=yn,_[k]=yn-Ye}r.modifiersData[i]=_}}const N$={name:"preventOverflow",enabled:!0,phase:"main",fn:T$,requiresIfExists:["offset"]};function A$(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function P$(e){return e===hn(e)||!Rn(e)?Op(e):A$(e)}function _$(e){var r=e.getBoundingClientRect(),o=Fa(r.width)/e.offsetWidth||1,i=Fa(r.height)/e.offsetHeight||1;return o!==1||i!==1}function R$(e,r,o){o===void 0&&(o=!1);var i=Rn(r),l=Rn(r)&&_$(r),u=xo(r),d=Ba(e,l,o),f={scrollLeft:0,scrollTop:0},m={x:0,y:0};return(i||!i&&!o)&&((vr(r)!=="body"||Ip(u))&&(f=P$(r)),Rn(r)?(m=Ba(r,!0),m.x+=r.clientLeft,m.y+=r.clientTop):u&&(m.x=jp(u))),{x:d.left+f.scrollLeft-m.x,y:d.top+f.scrollTop-m.y,width:d.width,height:d.height}}function M$(e){var r=new Map,o=new Set,i=[];e.forEach(function(u){r.set(u.name,u)});function l(u){o.add(u.name);var d=[].concat(u.requires||[],u.requiresIfExists||[]);d.forEach(function(f){if(!o.has(f)){var m=r.get(f);m&&l(m)}}),i.push(u)}return e.forEach(function(u){o.has(u.name)||l(u)}),i}function O$(e){var r=M$(e);return QC.reduce(function(o,i){return o.concat(r.filter(function(l){return l.phase===i}))},[])}function j$(e){var r;return function(){return r||(r=new Promise(function(o){Promise.resolve().then(function(){r=void 0,o(e())})})),r}}function I$(e){var r=e.reduce(function(o,i){var l=o[i.name];return o[i.name]=l?Object.assign({},l,i,{options:Object.assign({},l.options,i.options),data:Object.assign({},l.data,i.data)}):i,o},{});return Object.keys(r).map(function(o){return r[o]})}var zg={placement:"bottom",modifiers:[],strategy:"absolute"};function Dg(){for(var e=arguments.length,r=new Array(e),o=0;o<e;o++)r[o]=arguments[o];return!r.some(function(i){return!(i&&typeof i.getBoundingClientRect=="function")})}function z$(e){e===void 0&&(e={});var r=e,o=r.defaultModifiers,i=o===void 0?[]:o,l=r.defaultOptions,u=l===void 0?zg:l;return function(d,f,m){m===void 0&&(m=u);var g={placement:"bottom",orderedModifiers:[],options:Object.assign({},zg,u),modifiersData:{},elements:{reference:d,popper:f},attributes:{},styles:{}},S=[],y=!1,v={state:g,setOptions:function(b){var T=typeof b=="function"?b(g.options):b;E(),g.options=Object.assign({},u,g.options,T),g.scrollParents={reference:Ho(d)?Bi(d):d.contextElement?Bi(d.contextElement):[],popper:Bi(f)};var N=O$(I$([].concat(i,g.options.modifiers)));return g.orderedModifiers=N.filter(function(A){return A.enabled}),$(),v.update()},forceUpdate:function(){if(!y){var b=g.elements,T=b.reference,N=b.popper;if(Dg(T,N)){g.rects={reference:R$(T,pl(N),g.options.strategy==="fixed"),popper:Rp(N)},g.reset=!1,g.placement=g.options.placement,g.orderedModifiers.forEach(function(L){return g.modifiersData[L.name]=Object.assign({},L.data)});for(var A=0;A<g.orderedModifiers.length;A++){if(g.reset===!0){g.reset=!1,A=-1;continue}var x=g.orderedModifiers[A],C=x.fn,k=x.options,M=k===void 0?{}:k,I=x.name;typeof C=="function"&&(g=C({state:g,options:M,name:I,instance:v})||g)}}}},update:j$(function(){return new Promise(function(b){v.forceUpdate(),b(g)})}),destroy:function(){E(),y=!0}};if(!Dg(d,f))return v;v.setOptions(m).then(function(b){!y&&m.onFirstUpdate&&m.onFirstUpdate(b)});function $(){g.orderedModifiers.forEach(function(b){var T=b.name,N=b.options,A=N===void 0?{}:N,x=b.effect;if(typeof x=="function"){var C=x({state:g,name:T,instance:v,options:A}),k=function(){};S.push(C||k)}})}function E(){S.forEach(function(b){return b()}),S=[]}return v}}var D$=[l$,$$,a$,GC,k$,v$,N$,t$,x$],F$=z$({defaultModifiers:D$});function B$(e){return Rt("MuiPopper",e)}ht("MuiPopper",["root"]);function L$(e,r){if(r==="ltr")return e;switch(e){case"bottom-end":return"bottom-start";case"bottom-start":return"bottom-end";case"top-end":return"top-start";case"top-start":return"top-end";default:return e}}function Ud(e){return typeof e=="function"?e():e}function q$(e){return e.nodeType!==void 0}const W$=e=>{const{classes:r}=e;return qt({root:["root"]},B$,r)},U$={},Q$=j.forwardRef(function(e,r){const{anchorEl:o,children:i,direction:l,disablePortal:u,modifiers:d,open:f,placement:m,popperOptions:g,popperRef:S,slotProps:y={},slots:v={},TransitionProps:$,ownerState:E,...b}=e,T=j.useRef(null),N=er(T,r),A=j.useRef(null),x=er(A,S),C=j.useRef(x);Zi(()=>{C.current=x},[x]),j.useImperativeHandle(S,()=>A.current,[]);const k=L$(m,l),[M,I]=j.useState(k),[L,z]=j.useState(Ud(o));j.useEffect(()=>{A.current&&A.current.forceUpdate()}),j.useEffect(()=>{o&&z(Ud(o))},[o]),Zi(()=>{if(!L||!f)return;const F=Z=>{I(Z.placement)};let G=[{name:"preventOverflow",options:{altBoundary:u}},{name:"flip",options:{altBoundary:u}},{name:"onUpdate",enabled:!0,phase:"afterWrite",fn:({state:Z})=>{F(Z)}}];d!=null&&(G=G.concat(d)),g&&g.modifiers!=null&&(G=G.concat(g.modifiers));const H=F$(L,T.current,{placement:k,...g,modifiers:G});return C.current(H),()=>{H.destroy(),C.current(null)}},[L,u,d,f,g,k]);const h={placement:M};$!==null&&(h.TransitionProps=$);const P=W$(e),_=v.root??"div",R=U2({elementType:_,externalSlotProps:y.root,externalForwardedProps:b,additionalProps:{role:"tooltip",ref:N},ownerState:e,className:P.root});return q.jsx(_,{...R,children:typeof i=="function"?i(h):i})}),V$=j.forwardRef(function(e,r){const{anchorEl:o,children:i,container:l,direction:u="ltr",disablePortal:d=!1,keepMounted:f=!1,modifiers:m,open:g,placement:S="bottom",popperOptions:y=U$,popperRef:v,style:$,transition:E=!1,slotProps:b={},slots:T={},...N}=e,[A,x]=j.useState(!0),C=()=>{x(!1)},k=()=>{x(!0)};if(!f&&!g&&(!E||A))return null;let M;if(l)M=l;else if(o){const z=Ud(o);M=z&&q$(z)?Wr(z).body:Wr(null).body}const I=!g&&f&&(!E||A)?"none":void 0,L=E?{in:g,onEnter:C,onExited:k}:void 0;return q.jsx(h0,{disablePortal:d,container:M,children:q.jsx(Q$,{anchorEl:o,direction:u,disablePortal:d,modifiers:m,ref:r,open:E?!A:g,placement:S,popperOptions:y,popperRef:v,slotProps:b,slots:T,...N,style:{position:"fixed",top:0,left:0,display:I,...$},TransitionProps:L,children:i})})}),K$=je(V$,{name:"MuiPopper",slot:"Root",overridesResolver:(e,r)=>r.root})({}),R0=j.forwardRef(function(e,r){const o=V2(),i=Mt({props:e,name:"MuiPopper"}),{anchorEl:l,component:u,components:d,componentsProps:f,container:m,disablePortal:g,keepMounted:S,modifiers:y,open:v,placement:$,popperOptions:E,popperRef:b,transition:T,slots:N,slotProps:A,...x}=i,C=(N==null?void 0:N.root)??(d==null?void 0:d.Root),k={anchorEl:l,container:m,disablePortal:g,keepMounted:S,modifiers:y,open:v,placement:$,popperOptions:E,popperRef:b,transition:T,...x};return q.jsx(K$,{as:u,direction:o?"rtl":"ltr",slots:{root:C},slotProps:A??f,...k,ref:r})});function G$(e){return Rt("MuiListSubheader",e)}ht("MuiListSubheader",["root","colorPrimary","colorInherit","gutters","inset","sticky"]);const Y$=e=>{const{classes:r,color:o,disableGutters:i,inset:l,disableSticky:u}=e,d={root:["root",o!=="default"&&`color${xe(o)}`,!i&&"gutters",l&&"inset",!u&&"sticky"]};return qt(d,G$,r)},X$=je("li",{name:"MuiListSubheader",slot:"Root",overridesResolver:(e,r)=>{const{ownerState:o}=e;return[r.root,o.color!=="default"&&r[`color${xe(o.color)}`],!o.disableGutters&&r.gutters,o.inset&&r.inset,!o.disableSticky&&r.sticky]}})($t(({theme:e})=>({boxSizing:"border-box",lineHeight:"48px",listStyle:"none",color:(e.vars||e).palette.text.secondary,fontFamily:e.typography.fontFamily,fontWeight:e.typography.fontWeightMedium,fontSize:e.typography.pxToRem(14),variants:[{props:{color:"primary"},style:{color:(e.vars||e).palette.primary.main}},{props:{color:"inherit"},style:{color:"inherit"}},{props:({ownerState:r})=>!r.disableGutters,style:{paddingLeft:16,paddingRight:16}},{props:({ownerState:r})=>r.inset,style:{paddingLeft:72}},{props:({ownerState:r})=>!r.disableSticky,style:{position:"sticky",top:0,zIndex:1,backgroundColor:(e.vars||e).palette.background.paper}}]}))),Qd=j.forwardRef(function(e,r){const o=Mt({props:e,name:"MuiListSubheader"}),{className:i,color:l="default",component:u="li",disableGutters:d=!1,disableSticky:f=!1,inset:m=!1,...g}=o,S={...o,color:l,component:u,disableGutters:d,disableSticky:f,inset:m},y=Y$(S);return q.jsx(X$,{as:u,className:ze(y.root,i),ref:r,ownerState:S,...g})});Qd&&(Qd.muiSkipListHighlight=!0);const H$=Tp(q.jsx("path",{d:"M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"}),"Cancel");function J$(e){return Rt("MuiChip",e)}const Oe=ht("MuiChip",["root","sizeSmall","sizeMedium","colorDefault","colorError","colorInfo","colorPrimary","colorSecondary","colorSuccess","colorWarning","disabled","clickable","clickableColorPrimary","clickableColorSecondary","deletable","deletableColorPrimary","deletableColorSecondary","outlined","filled","outlinedPrimary","outlinedSecondary","filledPrimary","filledSecondary","avatar","avatarSmall","avatarMedium","avatarColorPrimary","avatarColorSecondary","icon","iconSmall","iconMedium","iconColorPrimary","iconColorSecondary","label","labelSmall","labelMedium","deleteIcon","deleteIconSmall","deleteIconMedium","deleteIconColorPrimary","deleteIconColorSecondary","deleteIconOutlinedColorPrimary","deleteIconOutlinedColorSecondary","deleteIconFilledColorPrimary","deleteIconFilledColorSecondary","focusVisible"]),Z$=e=>{const{classes:r,disabled:o,size:i,color:l,iconColor:u,onDelete:d,clickable:f,variant:m}=e,g={root:["root",m,o&&"disabled",`size${xe(i)}`,`color${xe(l)}`,f&&"clickable",f&&`clickableColor${xe(l)}`,d&&"deletable",d&&`deletableColor${xe(l)}`,`${m}${xe(l)}`],label:["label",`label${xe(i)}`],avatar:["avatar",`avatar${xe(i)}`,`avatarColor${xe(l)}`],icon:["icon",`icon${xe(i)}`,`iconColor${xe(u)}`],deleteIcon:["deleteIcon",`deleteIcon${xe(i)}`,`deleteIconColor${xe(l)}`,`deleteIcon${xe(m)}Color${xe(l)}`]};return qt(g,J$,r)},eE=je("div",{name:"MuiChip",slot:"Root",overridesResolver:(e,r)=>{const{ownerState:o}=e,{color:i,iconColor:l,clickable:u,onDelete:d,size:f,variant:m}=o;return[{[`& .${Oe.avatar}`]:r.avatar},{[`& .${Oe.avatar}`]:r[`avatar${xe(f)}`]},{[`& .${Oe.avatar}`]:r[`avatarColor${xe(i)}`]},{[`& .${Oe.icon}`]:r.icon},{[`& .${Oe.icon}`]:r[`icon${xe(f)}`]},{[`& .${Oe.icon}`]:r[`iconColor${xe(l)}`]},{[`& .${Oe.deleteIcon}`]:r.deleteIcon},{[`& .${Oe.deleteIcon}`]:r[`deleteIcon${xe(f)}`]},{[`& .${Oe.deleteIcon}`]:r[`deleteIconColor${xe(i)}`]},{[`& .${Oe.deleteIcon}`]:r[`deleteIcon${xe(m)}Color${xe(i)}`]},r.root,r[`size${xe(f)}`],r[`color${xe(i)}`],u&&r.clickable,u&&i!=="default"&&r[`clickableColor${xe(i)})`],d&&r.deletable,d&&i!=="default"&&r[`deletableColor${xe(i)}`],r[m],r[`${m}${xe(i)}`]]}})($t(({theme:e})=>{const r=e.palette.mode==="light"?e.palette.grey[700]:e.palette.grey[300];return{maxWidth:"100%",fontFamily:e.typography.fontFamily,fontSize:e.typography.pxToRem(13),display:"inline-flex",alignItems:"center",justifyContent:"center",height:32,color:(e.vars||e).palette.text.primary,backgroundColor:(e.vars||e).palette.action.selected,borderRadius:32/2,whiteSpace:"nowrap",transition:e.transitions.create(["background-color","box-shadow"]),cursor:"unset",outline:0,textDecoration:"none",border:0,padding:0,verticalAlign:"middle",boxSizing:"border-box",[`&.${Oe.disabled}`]:{opacity:(e.vars||e).palette.action.disabledOpacity,pointerEvents:"none"},[`& .${Oe.avatar}`]:{marginLeft:5,marginRight:-6,width:24,height:24,color:e.vars?e.vars.palette.Chip.defaultAvatarColor:r,fontSize:e.typography.pxToRem(12)},[`& .${Oe.avatarColorPrimary}`]:{color:(e.vars||e).palette.primary.contrastText,backgroundColor:(e.vars||e).palette.primary.dark},[`& .${Oe.avatarColorSecondary}`]:{color:(e.vars||e).palette.secondary.contrastText,backgroundColor:(e.vars||e).palette.secondary.dark},[`& .${Oe.avatarSmall}`]:{marginLeft:4,marginRight:-4,width:18,height:18,fontSize:e.typography.pxToRem(10)},[`& .${Oe.icon}`]:{marginLeft:5,marginRight:-6},[`& .${Oe.deleteIcon}`]:{WebkitTapHighlightColor:"transparent",color:e.vars?`rgba(${e.vars.palette.text.primaryChannel} / 0.26)`:Ct(e.palette.text.primary,.26),fontSize:22,cursor:"pointer",margin:"0 5px 0 -6px","&:hover":{color:e.vars?`rgba(${e.vars.palette.text.primaryChannel} / 0.4)`:Ct(e.palette.text.primary,.4)}},variants:[{props:{size:"small"},style:{height:24,[`& .${Oe.icon}`]:{fontSize:18,marginLeft:4,marginRight:-4},[`& .${Oe.deleteIcon}`]:{fontSize:16,marginRight:4,marginLeft:-4}}},...Object.entries(e.palette).filter(ho(["contrastText"])).map(([o])=>({props:{color:o},style:{backgroundColor:(e.vars||e).palette[o].main,color:(e.vars||e).palette[o].contrastText,[`& .${Oe.deleteIcon}`]:{color:e.vars?`rgba(${e.vars.palette[o].contrastTextChannel} / 0.7)`:Ct(e.palette[o].contrastText,.7),"&:hover, &:active":{color:(e.vars||e).palette[o].contrastText}}}})),{props:o=>o.iconColor===o.color,style:{[`& .${Oe.icon}`]:{color:e.vars?e.vars.palette.Chip.defaultIconColor:r}}},{props:o=>o.iconColor===o.color&&o.color!=="default",style:{[`& .${Oe.icon}`]:{color:"inherit"}}},{props:{onDelete:!0},style:{[`&.${Oe.focusVisible}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.action.selectedChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.focusOpacity}))`:Ct(e.palette.action.selected,e.palette.action.selectedOpacity+e.palette.action.focusOpacity)}}},...Object.entries(e.palette).filter(ho(["dark"])).map(([o])=>({props:{color:o,onDelete:!0},style:{[`&.${Oe.focusVisible}`]:{background:(e.vars||e).palette[o].dark}}})),{props:{clickable:!0},style:{userSelect:"none",WebkitTapHighlightColor:"transparent",cursor:"pointer","&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette.action.selectedChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.hoverOpacity}))`:Ct(e.palette.action.selected,e.palette.action.selectedOpacity+e.palette.action.hoverOpacity)},[`&.${Oe.focusVisible}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.action.selectedChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.focusOpacity}))`:Ct(e.palette.action.selected,e.palette.action.selectedOpacity+e.palette.action.focusOpacity)},"&:active":{boxShadow:(e.vars||e).shadows[1]}}},...Object.entries(e.palette).filter(ho(["dark"])).map(([o])=>({props:{color:o,clickable:!0},style:{[`&:hover, &.${Oe.focusVisible}`]:{backgroundColor:(e.vars||e).palette[o].dark}}})),{props:{variant:"outlined"},style:{backgroundColor:"transparent",border:e.vars?`1px solid ${e.vars.palette.Chip.defaultBorder}`:`1px solid ${e.palette.mode==="light"?e.palette.grey[400]:e.palette.grey[700]}`,[`&.${Oe.clickable}:hover`]:{backgroundColor:(e.vars||e).palette.action.hover},[`&.${Oe.focusVisible}`]:{backgroundColor:(e.vars||e).palette.action.focus},[`& .${Oe.avatar}`]:{marginLeft:4},[`& .${Oe.avatarSmall}`]:{marginLeft:2},[`& .${Oe.icon}`]:{marginLeft:4},[`& .${Oe.iconSmall}`]:{marginLeft:2},[`& .${Oe.deleteIcon}`]:{marginRight:5},[`& .${Oe.deleteIconSmall}`]:{marginRight:3}}},...Object.entries(e.palette).filter(ho()).map(([o])=>({props:{variant:"outlined",color:o},style:{color:(e.vars||e).palette[o].main,border:`1px solid ${e.vars?`rgba(${e.vars.palette[o].mainChannel} / 0.7)`:Ct(e.palette[o].main,.7)}`,[`&.${Oe.clickable}:hover`]:{backgroundColor:e.vars?`rgba(${e.vars.palette[o].mainChannel} / ${e.vars.palette.action.hoverOpacity})`:Ct(e.palette[o].main,e.palette.action.hoverOpacity)},[`&.${Oe.focusVisible}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette[o].mainChannel} / ${e.vars.palette.action.focusOpacity})`:Ct(e.palette[o].main,e.palette.action.focusOpacity)},[`& .${Oe.deleteIcon}`]:{color:e.vars?`rgba(${e.vars.palette[o].mainChannel} / 0.7)`:Ct(e.palette[o].main,.7),"&:hover, &:active":{color:(e.vars||e).palette[o].main}}}}))]}})),tE=je("span",{name:"MuiChip",slot:"Label",overridesResolver:(e,r)=>{const{ownerState:o}=e,{size:i}=o;return[r.label,r[`label${xe(i)}`]]}})({overflow:"hidden",textOverflow:"ellipsis",paddingLeft:12,paddingRight:12,whiteSpace:"nowrap",variants:[{props:{variant:"outlined"},style:{paddingLeft:11,paddingRight:11}},{props:{size:"small"},style:{paddingLeft:8,paddingRight:8}},{props:{size:"small",variant:"outlined"},style:{paddingLeft:7,paddingRight:7}}]});function Fg(e){return e.key==="Backspace"||e.key==="Delete"}const nE=j.forwardRef(function(e,r){const o=Mt({props:e,name:"MuiChip"}),{avatar:i,className:l,clickable:u,color:d="default",component:f,deleteIcon:m,disabled:g=!1,icon:S,label:y,onClick:v,onDelete:$,onKeyDown:E,onKeyUp:b,size:T="medium",variant:N="filled",tabIndex:A,skipFocusWhenDisabled:x=!1,...C}=o,k=j.useRef(null),M=er(k,r),I=W=>{W.stopPropagation(),$&&$(W)},L=W=>{W.currentTarget===W.target&&Fg(W)&&W.preventDefault(),E&&E(W)},z=W=>{W.currentTarget===W.target&&$&&Fg(W)&&$(W),b&&b(W)},h=u!==!1&&v?!0:u,P=h||$?Fd:f||"div",_={...o,component:P,disabled:g,size:T,color:d,iconColor:j.isValidElement(S)&&S.props.color||d,onDelete:!!$,clickable:h,variant:N},R=Z$(_),F=P===Fd?{component:f||"div",focusVisibleClassName:R.focusVisible,...$&&{disableRipple:!0}}:{};let G=null;$&&(G=m&&j.isValidElement(m)?j.cloneElement(m,{className:ze(m.props.className,R.deleteIcon),onClick:I}):q.jsx(H$,{className:ze(R.deleteIcon),onClick:I}));let H=null;i&&j.isValidElement(i)&&(H=j.cloneElement(i,{className:ze(R.avatar,i.props.className)}));let Z=null;return S&&j.isValidElement(S)&&(Z=j.cloneElement(S,{className:ze(R.icon,S.props.className)})),q.jsxs(eE,{as:P,className:ze(R.root,l),disabled:h&&g?!0:void 0,onClick:v,onKeyDown:L,onKeyUp:z,ref:M,tabIndex:x&&g?-1:A,ownerState:_,...F,...C,children:[H||Z,q.jsx(tE,{className:ze(R.label),ownerState:_,children:y}),G]})}),cr=ht("MuiInputBase",["root","formControl","focused","disabled","adornedStart","adornedEnd","error","sizeSmall","multiline","colorSecondary","fullWidth","hiddenLabel","readOnly","input","inputSizeSmall","inputMultiline","inputTypeSearch","inputAdornedStart","inputAdornedEnd","inputHiddenLabel"]),md={...cr,...ht("MuiInput",["root","underline","input"])},Bg={...cr,...ht("MuiOutlinedInput",["root","notchedOutline","input"])},Na={...cr,...ht("MuiFilledInput",["root","underline","input","adornedStart","adornedEnd","sizeSmall","multiline","hiddenLabel"])},rE=Tp(q.jsx("path",{d:"M7 10l5 5 5-5z"}),"ArrowDropDown");function oE(e){return Rt("MuiAutocomplete",e)}const Pe=ht("MuiAutocomplete",["root","expanded","fullWidth","focused","focusVisible","tag","tagSizeSmall","tagSizeMedium","hasPopupIcon","hasClearIcon","inputRoot","input","inputFocused","endAdornment","clearIndicator","popupIndicator","popupIndicatorOpen","popper","popperDisablePortal","paper","listbox","loading","noOptions","option","groupLabel","groupUl"]);var Lg,qg;const aE=e=>{const{classes:r,disablePortal:o,expanded:i,focused:l,fullWidth:u,hasClearIcon:d,hasPopupIcon:f,inputFocused:m,popupOpen:g,size:S}=e,y={root:["root",i&&"expanded",l&&"focused",u&&"fullWidth",d&&"hasClearIcon",f&&"hasPopupIcon"],inputRoot:["inputRoot"],input:["input",m&&"inputFocused"],tag:["tag",`tagSize${xe(S)}`],endAdornment:["endAdornment"],clearIndicator:["clearIndicator"],popupIndicator:["popupIndicator",g&&"popupIndicatorOpen"],popper:["popper",o&&"popperDisablePortal"],paper:["paper"],listbox:["listbox"],loading:["loading"],noOptions:["noOptions"],option:["option"],groupLabel:["groupLabel"],groupUl:["groupUl"]};return qt(y,oE,r)},iE=je("div",{name:"MuiAutocomplete",slot:"Root",overridesResolver:(e,r)=>{const{ownerState:o}=e,{fullWidth:i,hasClearIcon:l,hasPopupIcon:u,inputFocused:d,size:f}=o;return[{[`& .${Pe.tag}`]:r.tag},{[`& .${Pe.tag}`]:r[`tagSize${xe(f)}`]},{[`& .${Pe.inputRoot}`]:r.inputRoot},{[`& .${Pe.input}`]:r.input},{[`& .${Pe.input}`]:d&&r.inputFocused},r.root,i&&r.fullWidth,u&&r.hasPopupIcon,l&&r.hasClearIcon]}})({[`&.${Pe.focused} .${Pe.clearIndicator}`]:{visibility:"visible"},"@media (pointer: fine)":{[`&:hover .${Pe.clearIndicator}`]:{visibility:"visible"}},[`& .${Pe.tag}`]:{margin:3,maxWidth:"calc(100% - 6px)"},[`& .${Pe.inputRoot}`]:{[`.${Pe.hasPopupIcon}&, .${Pe.hasClearIcon}&`]:{paddingRight:30},[`.${Pe.hasPopupIcon}.${Pe.hasClearIcon}&`]:{paddingRight:56},[`& .${Pe.input}`]:{width:0,minWidth:30}},[`& .${md.root}`]:{paddingBottom:1,"& .MuiInput-input":{padding:"4px 4px 4px 0px"}},[`& .${md.root}.${cr.sizeSmall}`]:{[`& .${md.input}`]:{padding:"2px 4px 3px 0"}},[`& .${Bg.root}`]:{padding:9,[`.${Pe.hasPopupIcon}&, .${Pe.hasClearIcon}&`]:{paddingRight:39},[`.${Pe.hasPopupIcon}.${Pe.hasClearIcon}&`]:{paddingRight:65},[`& .${Pe.input}`]:{padding:"7.5px 4px 7.5px 5px"},[`& .${Pe.endAdornment}`]:{right:9}},[`& .${Bg.root}.${cr.sizeSmall}`]:{paddingTop:6,paddingBottom:6,paddingLeft:6,[`& .${Pe.input}`]:{padding:"2.5px 4px 2.5px 8px"}},[`& .${Na.root}`]:{paddingTop:19,paddingLeft:8,[`.${Pe.hasPopupIcon}&, .${Pe.hasClearIcon}&`]:{paddingRight:39},[`.${Pe.hasPopupIcon}.${Pe.hasClearIcon}&`]:{paddingRight:65},[`& .${Na.input}`]:{padding:"7px 4px"},[`& .${Pe.endAdornment}`]:{right:9}},[`& .${Na.root}.${cr.sizeSmall}`]:{paddingBottom:1,[`& .${Na.input}`]:{padding:"2.5px 4px"}},[`& .${cr.hiddenLabel}`]:{paddingTop:8},[`& .${Na.root}.${cr.hiddenLabel}`]:{paddingTop:0,paddingBottom:0,[`& .${Pe.input}`]:{paddingTop:16,paddingBottom:17}},[`& .${Na.root}.${cr.hiddenLabel}.${cr.sizeSmall}`]:{[`& .${Pe.input}`]:{paddingTop:8,paddingBottom:9}},[`& .${Pe.input}`]:{flexGrow:1,textOverflow:"ellipsis",opacity:0},variants:[{props:{fullWidth:!0},style:{width:"100%"}},{props:{size:"small"},style:{[`& .${Pe.tag}`]:{margin:2,maxWidth:"calc(100% - 4px)"}}},{props:{inputFocused:!0},style:{[`& .${Pe.input}`]:{opacity:1}}},{props:{multiple:!0},style:{[`& .${Pe.inputRoot}`]:{flexWrap:"wrap"}}}]}),lE=je("div",{name:"MuiAutocomplete",slot:"EndAdornment",overridesResolver:(e,r)=>r.endAdornment})({position:"absolute",right:0,top:"50%",transform:"translate(0, -50%)"}),sE=je(S0,{name:"MuiAutocomplete",slot:"ClearIndicator",overridesResolver:(e,r)=>r.clearIndicator})({marginRight:-2,padding:4,visibility:"hidden"}),uE=je(S0,{name:"MuiAutocomplete",slot:"PopupIndicator",overridesResolver:(e,r)=>{const{ownerState:o}=e;return[r.popupIndicator,o.popupOpen&&r.popupIndicatorOpen]}})({padding:2,marginRight:-2,variants:[{props:{popupOpen:!0},style:{transform:"rotate(180deg)"}}]}),cE=je(R0,{name:"MuiAutocomplete",slot:"Popper",overridesResolver:(e,r)=>{const{ownerState:o}=e;return[{[`& .${Pe.option}`]:r.option},r.popper,o.disablePortal&&r.popperDisablePortal]}})($t(({theme:e})=>({zIndex:(e.vars||e).zIndex.modal,variants:[{props:{disablePortal:!0},style:{position:"absolute"}}]}))),dE=je(yu,{name:"MuiAutocomplete",slot:"Paper",overridesResolver:(e,r)=>r.paper})($t(({theme:e})=>({...e.typography.body1,overflow:"auto"}))),pE=je("div",{name:"MuiAutocomplete",slot:"Loading",overridesResolver:(e,r)=>r.loading})($t(({theme:e})=>({color:(e.vars||e).palette.text.secondary,padding:"14px 16px"}))),fE=je("div",{name:"MuiAutocomplete",slot:"NoOptions",overridesResolver:(e,r)=>r.noOptions})($t(({theme:e})=>({color:(e.vars||e).palette.text.secondary,padding:"14px 16px"}))),mE=je("ul",{name:"MuiAutocomplete",slot:"Listbox",overridesResolver:(e,r)=>r.listbox})($t(({theme:e})=>({listStyle:"none",margin:0,padding:"8px 0",maxHeight:"40vh",overflow:"auto",position:"relative",[`& .${Pe.option}`]:{minHeight:48,display:"flex",overflow:"hidden",justifyContent:"flex-start",alignItems:"center",cursor:"pointer",paddingTop:6,boxSizing:"border-box",outline:"0",WebkitTapHighlightColor:"transparent",paddingBottom:6,paddingLeft:16,paddingRight:16,[e.breakpoints.up("sm")]:{minHeight:"auto"},[`&.${Pe.focused}`]:{backgroundColor:(e.vars||e).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},'&[aria-disabled="true"]':{opacity:(e.vars||e).palette.action.disabledOpacity,pointerEvents:"none"},[`&.${Pe.focusVisible}`]:{backgroundColor:(e.vars||e).palette.action.focus},'&[aria-selected="true"]':{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / ${e.vars.palette.action.selectedOpacity})`:Ct(e.palette.primary.main,e.palette.action.selectedOpacity),[`&.${Pe.focused}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.hoverOpacity}))`:Ct(e.palette.primary.main,e.palette.action.selectedOpacity+e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:(e.vars||e).palette.action.selected}},[`&.${Pe.focusVisible}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.focusOpacity}))`:Ct(e.palette.primary.main,e.palette.action.selectedOpacity+e.palette.action.focusOpacity)}}}}))),hE=je(Qd,{name:"MuiAutocomplete",slot:"GroupLabel",overridesResolver:(e,r)=>r.groupLabel})($t(({theme:e})=>({backgroundColor:(e.vars||e).palette.background.paper,top:-8}))),gE=je("ul",{name:"MuiAutocomplete",slot:"GroupUl",overridesResolver:(e,r)=>r.groupUl})({padding:0,[`& .${Pe.option}`]:{paddingLeft:24}}),Vd=j.forwardRef(function(e,r){const o=Mt({props:e,name:"MuiAutocomplete"}),{autoComplete:i=!1,autoHighlight:l=!1,autoSelect:u=!1,blurOnSelect:d=!1,ChipProps:f,className:m,clearIcon:g=Lg||(Lg=q.jsx(NC,{fontSize:"small"})),clearOnBlur:S=!o.freeSolo,clearOnEscape:y=!1,clearText:v="Clear",closeText:$="Close",componentsProps:E,defaultValue:b=o.multiple?[]:null,disableClearable:T=!1,disableCloseOnSelect:N=!1,disabled:A=!1,disabledItemsFocusable:x=!1,disableListWrap:C=!1,disablePortal:k=!1,filterOptions:M,filterSelectedOptions:I=!1,forcePopupIcon:L="auto",freeSolo:z=!1,fullWidth:h=!1,getLimitTagsText:P=ke=>`+${ke}`,getOptionDisabled:_,getOptionKey:R,getOptionLabel:F,isOptionEqualToValue:G,groupBy:H,handleHomeEndKeys:Z=!o.freeSolo,id:W,includeInputInList:ee=!1,inputValue:K,limitTags:re=-1,ListboxComponent:le,ListboxProps:fe,loading:ce=!1,loadingText:ie="Loading…",multiple:he=!1,noOptionsText:be="No options",onChange:Se,onClose:qe,onHighlightChange:Ne,onInputChange:Ee,onOpen:Me,open:We,openOnFocus:dt=!1,openText:Ie="Open",options:tt,PaperComponent:de,PopperComponent:Wt,popupIcon:_e=qg||(qg=q.jsx(rE,{})),readOnly:Ye=!1,renderGroup:ye,renderInput:In,renderOption:gn,renderTags:Ot,selectOnFocus:br=!o.freeSolo,size:zn="medium",slots:Dn={},slotProps:yn={},value:jt,...Ue}=o,{getRootProps:Et,getInputProps:wo,getInputLabelProps:xr,getPopupIndicatorProps:Jo,getClearProps:Fn,getTagProps:Ut,getListboxProps:So,getOptionProps:wr,value:Sr,dirty:an,expanded:Gt,id:Qt,popupOpen:vn,focused:Bn,focusedTag:Zo,anchorEl:tr,setAnchorEl:ko,inputValue:Co,groupedOptions:kr}=MC({...o,componentName:"Autocomplete"}),bn=!T&&!A&&an&&!Ye,Cr=(!z||L===!0)&&L!==!1,{onMouseDown:$r}=wo(),{ref:Ua,...Er}=So(),nr=F||(ke=>ke.label??ke),it={...o,disablePortal:k,expanded:Gt,focused:Bn,fullWidth:h,getOptionLabel:nr,hasClearIcon:bn,hasPopupIcon:Cr,inputFocused:Zo===-1,popupOpen:vn,size:zn},lt=aE(it),rr={slots:{paper:de,popper:Wt,...Dn},slotProps:{chip:f,listbox:fe,...E,...yn}},[Qa,$o]=_n("listbox",{elementType:mE,externalForwardedProps:rr,ownerState:it,className:lt.listbox,additionalProps:Er,ref:Ua}),[xn,J]=_n("paper",{elementType:yu,externalForwardedProps:rr,ownerState:it,className:lt.paper}),[X,ue]=_n("popper",{elementType:R0,externalForwardedProps:rr,ownerState:it,className:lt.popper,additionalProps:{disablePortal:k,style:{width:tr?tr.clientWidth:null},role:"presentation",anchorEl:tr,open:vn}});let we;if(he&&Sr.length>0){const ke=wn=>({className:lt.tag,disabled:A,...Ut(wn)});Ot?we=Ot(Sr,ke,it):we=Sr.map((wn,Sn)=>{const{key:Kr,...ea}=ke({index:Sn});return q.jsx(nE,{label:nr(wn),size:zn,...ea,...rr.slotProps.chip},Kr)})}if(re>-1&&Array.isArray(we)){const ke=we.length-re;!Bn&&ke>0&&(we=we.splice(0,re),we.push(q.jsx("span",{className:lt.tag,children:P(ke)},we.length)))}const Re=ye||(ke=>q.jsxs("li",{children:[q.jsx(hE,{className:lt.groupLabel,ownerState:it,component:"div",children:ke.group}),q.jsx(gE,{className:lt.groupUl,ownerState:it,children:ke.children})]},ke.key)),Ke=gn||((ke,wn)=>{const{key:Sn,...Kr}=ke;return q.jsx("li",{...Kr,children:nr(wn)},Sn)}),pt=(ke,wn)=>{const Sn=wr({option:ke,index:wn});return Ke({...Sn,className:lt.option},ke,{selected:Sn["aria-selected"],index:wn,inputValue:Co},it)},Tr=rr.slotProps.clearIndicator,Vr=rr.slotProps.popupIndicator;return q.jsxs(j.Fragment,{children:[q.jsx(iE,{ref:r,className:ze(lt.root,m),ownerState:it,...Et(Ue),children:In({id:Qt,disabled:A,fullWidth:!0,size:zn==="small"?"small":void 0,InputLabelProps:xr(),InputProps:{ref:ko,className:lt.inputRoot,startAdornment:we,onMouseDown:ke=>{ke.target===ke.currentTarget&&$r(ke)},...(bn||Cr)&&{endAdornment:q.jsxs(lE,{className:lt.endAdornment,ownerState:it,children:[bn?q.jsx(sE,{...Fn(),"aria-label":v,title:v,ownerState:it,...Tr,className:ze(lt.clearIndicator,Tr==null?void 0:Tr.className),children:g}):null,Cr?q.jsx(uE,{...Jo(),disabled:A,"aria-label":vn?$:Ie,title:vn?$:Ie,ownerState:it,...Vr,className:ze(lt.popupIndicator,Vr==null?void 0:Vr.className),children:_e}):null]})}},inputProps:{className:lt.input,disabled:A,readOnly:Ye,...wo()}})}),tr?q.jsx(cE,{as:X,...ue,children:q.jsxs(dE,{as:xn,...J,children:[ce&&kr.length===0?q.jsx(pE,{className:lt.loading,ownerState:it,children:ie}):null,kr.length===0&&!z&&!ce?q.jsx(fE,{className:lt.noOptions,ownerState:it,role:"presentation",onMouseDown:ke=>{ke.preventDefault()},children:be}):null,kr.length>0?q.jsx(Qa,{as:le,...$o,children:kr.map((ke,wn)=>H?Re({key:ke.key,group:ke.group,children:ke.options.map((Sn,Kr)=>pt(Sn,ke.index+Kr))}):pt(ke,wn))}):null]})}):null]})}),yE=e=>q.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"none",...e,children:q.jsx("g",{id:"Icons /General",children:q.jsx("path",{d:"M2.5 4.99996H17.5M15.8333 4.99996V16.6666C15.8333 17.5 15 18.3333 14.1667 18.3333H5.83333C5 18.3333 4.16667 17.5 4.16667 16.6666V4.99996M6.66667 4.99996V3.33329C6.66667 2.49996 7.5 1.66663 8.33333 1.66663H11.6667C12.5 1.66663 13.3333 2.49996 13.3333 3.33329V4.99996",stroke:"currentColor",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"})})}),vE=Wa(yE);/**
 * @license lucide-react v0.460.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bE=y0("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);function Kd(){return q.jsx("svg",{width:"16",height:"17",viewBox:"0 0 16 17",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:q.jsx("path",{d:"M13.6688 5.5L7.66876 11.5L1.66876 5.5",stroke:"#4B5768","stroke-linecap":"round","stroke-linejoin":"round"})})}function xE(){return q.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:q.jsx("path",{d:"M16.0031 21L7.00313 12L16.0031 3",stroke:"#4B5768","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})})}const Wg=[{label:"Internal User",value:"internal User"},{label:"External User",value:"external User"}],Ug=[{label:"Mr.",value:"Mr."},{label:"Mrs.",value:"Mrs."},{label:"Master.",value:"Master."},{label:"Miss.",value:"Miss."}],Qg=["salutation","firstName","lastName","email","userId","userType"],wE=({index:e,form:r,onSelect:o,onClear:i,validationError:l})=>{const[u,d]=j.useState(""),f=j.useMemo(()=>F2(y=>{d(y)},500),[]),{data:m,isFetching:g}=Kx({userId:u},{skip:!u}),S=j.useMemo(()=>(m==null?void 0:m.data)||[],[m]);return q.jsx(Vd,{freeSolo:!0,id:`user-id-search-${e}`,options:S,popupIcon:q.jsx(Kd,{}),getOptionLabel:y=>typeof y=="string"?y:y.userId??"",value:S.find(y=>y.userId===r.userId)||null,onChange:(y,v)=>{v?o(v):i()},onInputChange:(y,v,$)=>{f(v),v&&!g&&(r.userId=v),$==="clear"&&(r.userId="")},loading:g,renderInput:y=>q.jsx(Fr,{...y,className:Ae.inputField,variant:"outlined",size:"xlarge",placeholder:"Search User ID",error:!!l,helperText:l}),noOptionsText:"Type to search for a user ID",fullWidth:!0,size:"small",classes:{paper:Ae.autoCompletePaper,listbox:Ae.autoCompleteListbox}})},SE=({onSubmit:e,onUserActionClick:r},o)=>{const[i,l]=j.useState([{salutation:"",firstName:"",middleName:"",lastName:"",email:"",userId:"",username:"",userType:""}]),[u,d]=j.useState({}),[f,m]=j.useState(!1),g=j.useCallback(A=>A.replace(/([A-Z])/g," $1").replace(/^./,x=>x.toUpperCase()),[]),S=j.useCallback((A,x)=>Qg.includes(x)&&!A?`${g(x)} is a mandatory.`:(x==="firstName"||x==="middleName"||x==="lastName")&&A&&!/^[A-Za-z\s\-']+$/.test(A)?"Special characters and numbers are not allowed.":x==="email"&&A&&!/^(?=[A-Za-z])[A-Za-z0-9._-]+@[^\s@]+\.[^\s@]+$/.test(A)?"Please enter a valid email address.":"",[g]),y=j.useCallback(()=>{const A={};let x=!0;return i.forEach((C,k)=>{const M={};Qg.forEach(I=>{const L=S(C[I],I);L&&(M[I]=L,x=!1)}),Object.keys(M).length&&(A[k]=M)}),d(A),x},[i,S]),v=j.useCallback((A,x,C)=>{l(k=>k.map((M,I)=>I===A?{...M,[x]:C}:M)),f&&d(k=>{const M={...k};return M[A]&&(delete M[A][x],Object.keys(M[A]).length||delete M[A]),M})},[f]),$=j.useCallback((A,x)=>{l(C=>C.map((k,M)=>M===A?{...k,userId:x.userId,username:x.userName,email:x.officialEmailId,salutation:x.salutation,firstName:x.firstName,middleName:x.middleName,lastName:x.lastName}:k))},[]),E=j.useCallback(A=>{l(x=>x.map((C,k)=>k===A?{...C,salutation:"",firstName:"",middleName:"",lastName:"",email:"",userId:"",username:""}:C))},[]),b=j.useCallback(()=>{l(A=>[...A,{salutation:"",firstName:"",middleName:"",lastName:"",email:"",userId:"",username:"",userType:""}])},[]),T=j.useCallback(A=>{l(x=>x.filter((C,k)=>k!==A)),d(x=>{const C={...x};return delete C[A],C})},[]),N=j.useCallback(()=>{m(!0),y()&&e(i)},[e,i,y]);return j.useImperativeHandle(o,()=>({addUserForm:b}),[b]),q.jsxs(Br,{className:Ae.screen,children:[q.jsx(Br,{className:Ae.userFormContainer,children:q.jsx("div",{className:Ae.scrollableContent,children:i.map((A,x)=>{var C,k,M,I,L,z,h,P,_;return q.jsxs(Qo,{className:Ae.userForm,children:[q.jsxs(Qo,{className:Ae.formHeader,children:[q.jsx(yt,{className:Ae.userFormTitle,children:`User ${x+1}`}),x!==0&&q.jsx(Qo,{className:Ae.userDeleteButton,onClick:()=>T(x),children:q.jsx(vE,{size:"xsmall",color:"inherit"})})]}),q.jsxs(Gn,{container:!0,spacing:2,className:Ae.formFieldsContainer,wrap:"wrap",children:[q.jsxs(Gn,{item:!0,xs:12,sm:2.4,className:Ae.field,children:[q.jsx(yt,{className:Ae.fieldlabel,children:"User Type*"}),q.jsx(Vd,{id:"userType",options:Wg,popupIcon:q.jsx(Kd,{}),getOptionLabel:R=>R.label,value:Wg.find(R=>R.value===A.userType)||void 0,onChange:(R,F)=>v(x,"userType",F?F.value:""),renderInput:R=>{var F,G;return q.jsx(Fr,{...R,className:Ae.inputField,variant:"outlined",size:"xlarge",placeholder:"Select User Type",error:!!((F=u[x])!=null&&F.userType),helperText:(G=u[x])==null?void 0:G.userType})},fullWidth:!0,disableClearable:!0,size:"small",classes:{paper:Ae.autoCompletePaper,listbox:Ae.autoCompleteListbox}})]}),q.jsxs(Gn,{item:!0,xs:12,sm:2.4,className:Ae.field,children:[q.jsx(yt,{className:Ae.fieldlabel,children:"User ID*"}),A.userType==="internal User"?q.jsx(wE,{index:x,form:A,onSelect:R=>$(x,R),onClear:()=>E(x),validationError:(C=u[x])==null?void 0:C.userId}):q.jsx(Fr,{id:"user-id",className:Ae.inputField,variant:"outlined",size:"xlarge",fullWidth:!0,placeholder:"Enter User ID",value:A.userId,onChange:R=>v(x,"userId",R.target.value),error:!!((k=u[x])!=null&&k.userId),helperText:(M=u[x])==null?void 0:M.userId})]}),q.jsxs(Gn,{item:!0,xs:12,sm:2.4,className:Ae.field,children:[q.jsx(yt,{className:Ae.fieldlabel,children:"User Name"}),q.jsx(Fr,{id:"username",className:Ae.inputField,variant:"outlined",size:"xlarge",fullWidth:!0,placeholder:"Enter Username",value:A.username,onChange:R=>v(x,"username",R.target.value)})]}),q.jsxs(Gn,{item:!0,xs:12,sm:2.4,className:Ae.field,children:[q.jsx(yt,{className:Ae.fieldlabel,children:"Official Email ID*"}),q.jsx(Fr,{id:"official-email",className:Ae.inputField,variant:"outlined",size:"xlarge",fullWidth:!0,placeholder:"Enter Email",value:A.email,onChange:R=>v(x,"email",R.target.value),error:!!((I=u[x])!=null&&I.email),helperText:(L=u[x])==null?void 0:L.email})]}),q.jsxs(Gn,{item:!0,xs:12,sm:2.4,className:Ae.field,children:[q.jsx(yt,{className:Ae.fieldlabel,children:"Salutation*"}),q.jsx(Vd,{id:"salutation",options:Ug,popupIcon:q.jsx(Kd,{}),getOptionLabel:R=>R.label,value:A.salutation&&Ug.find(R=>R.value===A.salutation)||null,onChange:(R,F)=>v(x,"salutation",F?F.value:""),renderInput:R=>{var F,G;return q.jsx(Fr,{...R,className:Ae.inputField,variant:"outlined",size:"xlarge",placeholder:"Select Salutation",error:!!((F=u[x])!=null&&F.salutation),helperText:(G=u[x])==null?void 0:G.salutation})},fullWidth:!0,disableClearable:!0,size:"small",classes:{paper:Ae.autoCompletePaper,listbox:Ae.autoCompleteListbox}})]}),q.jsxs(Gn,{item:!0,xs:12,sm:2.4,className:Ae.field,children:[q.jsx(yt,{className:Ae.fieldlabel,children:"First Name*"}),q.jsx(Fr,{id:"first-name",className:Ae.inputField,variant:"outlined",size:"xlarge",fullWidth:!0,placeholder:"Enter First Name",value:A.firstName,onChange:R=>v(x,"firstName",R.target.value),error:!!((z=u[x])!=null&&z.firstName),helperText:(h=u[x])==null?void 0:h.firstName})]}),q.jsxs(Gn,{item:!0,xs:12,sm:2.4,className:Ae.field,children:[q.jsx(yt,{className:Ae.fieldlabel,children:"Middle Name"}),q.jsx(Fr,{id:"middle-name",className:Ae.inputField,variant:"outlined",size:"xlarge",fullWidth:!0,placeholder:"Enter Middle Name",value:A.middleName,onChange:R=>v(x,"middleName",R.target.value)})]}),q.jsxs(Gn,{item:!0,xs:12,sm:2.4,className:Ae.field,children:[q.jsx(yt,{className:Ae.fieldlabel,children:"Last Name*"}),q.jsx(Fr,{id:"last-name",className:Ae.inputField,variant:"outlined",size:"xlarge",fullWidth:!0,placeholder:"Enter Last Name",value:A.lastName,onChange:R=>v(x,"lastName",R.target.value),error:!!((P=u[x])!=null&&P.lastName),helperText:(_=u[x])==null?void 0:_.lastName})]})]})]},`userForm-${x}`)})})}),q.jsxs(Br,{className:Ae.footer,children:[q.jsx(qi,{onClick:()=>r==null?void 0:r("usersummary"),id:"cancelButton",variant:"tertiary",className:Ae.cancelButton,children:q.jsx(yt,{className:Ae.cancelButtonText,children:"Cancel"})}),q.jsx(qi,{className:Ae.submitButton,sx:{color:"var(--Light-Theme-Primary-Background-Default, #FFF)",padding:"0.5rem 0.75rem",justifyContent:"center",alignItems:"center",gap:"0.25rem",borderRadius:"0.125rem"},id:"submitButton",variant:"primary",startIcon:q.jsx(bE,{size:16}),onClick:N,children:q.jsx(yt,{sx:{color:"var(--Light-Theme-Primary-Background-Default, #FFF)",fontFamily:"Roboto",fontSize:"0.875rem",fontStyle:"normal",fontWeight:"400",lineHeight:"normal",textTransform:"capitalize"},children:"Preview"})})]})]})},kE=j.forwardRef(SE),CE=e=>{var r;const o={systemProps:{},otherProps:{}},i=((r=e==null?void 0:e.theme)==null?void 0:r.unstable_sxConfig)??ul;return Object.keys(e).forEach(l=>{i[l]?o.systemProps[l]=e[l]:o.otherProps[l]=e[l]}),o};function $E(e){const{sx:r,...o}=e,{systemProps:i,otherProps:l}=CE(o);let u;return Array.isArray(r)?u=[i,...r]:typeof r=="function"?u=(...d)=>{const f=r(...d);return fr(f)?{...i,...f}:i}:u={...i,...r},{...l,sx:u}}const EE=xp(Ma.element);EE.isRequired=xp(Ma.element.isRequired);function Vg(...e){return e.reduce((r,o)=>o==null?r:function(...i){r.apply(this,i),o.apply(this,i)},()=>{})}function Js(e){return Wr(e).defaultView||window}function TE(e=window){const r=e.document.documentElement.clientWidth;return e.innerWidth-r}function NE(){return $E}const Kg={disabled:!1};var AE=function(e){return e.scrollTop},Oi="unmounted",qo="exited",Wo="entering",Pa="entered",Gd="exiting",Qr=function(e){m0(r,e);function r(i,l){var u;u=e.call(this,i,l)||this;var d=l,f=d&&!d.isMounting?i.enter:i.appear,m;return u.appearStatus=null,i.in?f?(m=qo,u.appearStatus=Wo):m=Pa:i.unmountOnExit||i.mountOnEnter?m=Oi:m=qo,u.state={status:m},u.nextCallback=null,u}r.getDerivedStateFromProps=function(i,l){var u=i.in;return u&&l.status===Oi?{status:qo}:null};var o=r.prototype;return o.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},o.componentDidUpdate=function(i){var l=null;if(i!==this.props){var u=this.state.status;this.props.in?u!==Wo&&u!==Pa&&(l=Wo):(u===Wo||u===Pa)&&(l=Gd)}this.updateStatus(!1,l)},o.componentWillUnmount=function(){this.cancelNextCallback()},o.getTimeouts=function(){var i=this.props.timeout,l,u,d;return l=u=d=i,i!=null&&typeof i!="number"&&(l=i.exit,u=i.enter,d=i.appear!==void 0?i.appear:u),{exit:l,enter:u,appear:d}},o.updateStatus=function(i,l){if(i===void 0&&(i=!1),l!==null)if(this.cancelNextCallback(),l===Wo){if(this.props.unmountOnExit||this.props.mountOnEnter){var u=this.props.nodeRef?this.props.nodeRef.current:$s.findDOMNode(this);u&&AE(u)}this.performEnter(i)}else this.performExit();else this.props.unmountOnExit&&this.state.status===qo&&this.setState({status:Oi})},o.performEnter=function(i){var l=this,u=this.props.enter,d=this.context?this.context.isMounting:i,f=this.props.nodeRef?[d]:[$s.findDOMNode(this),d],m=f[0],g=f[1],S=this.getTimeouts(),y=d?S.appear:S.enter;if(!i&&!u||Kg.disabled){this.safeSetState({status:Pa},function(){l.props.onEntered(m)});return}this.props.onEnter(m,g),this.safeSetState({status:Wo},function(){l.props.onEntering(m,g),l.onTransitionEnd(y,function(){l.safeSetState({status:Pa},function(){l.props.onEntered(m,g)})})})},o.performExit=function(){var i=this,l=this.props.exit,u=this.getTimeouts(),d=this.props.nodeRef?void 0:$s.findDOMNode(this);if(!l||Kg.disabled){this.safeSetState({status:qo},function(){i.props.onExited(d)});return}this.props.onExit(d),this.safeSetState({status:Gd},function(){i.props.onExiting(d),i.onTransitionEnd(u.exit,function(){i.safeSetState({status:qo},function(){i.props.onExited(d)})})})},o.cancelNextCallback=function(){this.nextCallback!==null&&(this.nextCallback.cancel(),this.nextCallback=null)},o.safeSetState=function(i,l){l=this.setNextCallback(l),this.setState(i,l)},o.setNextCallback=function(i){var l=this,u=!0;return this.nextCallback=function(d){u&&(u=!1,l.nextCallback=null,i(d))},this.nextCallback.cancel=function(){u=!1},this.nextCallback},o.onTransitionEnd=function(i,l){this.setNextCallback(l);var u=this.props.nodeRef?this.props.nodeRef.current:$s.findDOMNode(this),d=i==null&&!this.props.addEndListener;if(!u||d){setTimeout(this.nextCallback,0);return}if(this.props.addEndListener){var f=this.props.nodeRef?[this.nextCallback]:[u,this.nextCallback],m=f[0],g=f[1];this.props.addEndListener(m,g)}i!=null&&setTimeout(this.nextCallback,i)},o.render=function(){var i=this.state.status;if(i===Oi)return null;var l=this.props,u=l.children;l.in,l.mountOnEnter,l.unmountOnExit,l.appear,l.enter,l.exit,l.timeout,l.addEndListener,l.onEnter,l.onEntering,l.onEntered,l.onExit,l.onExiting,l.onExited,l.nodeRef;var d=f0(l,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]);return Xn.createElement(Ys.Provider,{value:null},typeof u=="function"?u(i,d):Xn.cloneElement(Xn.Children.only(u),d))},r}(Xn.Component);Qr.contextType=Ys;Qr.propTypes={};function Aa(){}Qr.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:Aa,onEntering:Aa,onEntered:Aa,onExit:Aa,onExiting:Aa,onExited:Aa};Qr.UNMOUNTED=Oi;Qr.EXITED=qo;Qr.ENTERING=Wo;Qr.ENTERED=Pa;Qr.EXITING=Gd;const PE=e=>e.scrollTop;function Gg(e,r){const{timeout:o,easing:i,style:l={}}=e;return{duration:l.transitionDuration??(typeof o=="number"?o:o[r.mode]||0),easing:l.transitionTimingFunction??(typeof i=="object"?i[r.mode]:i),delay:l.transitionDelay}}function _E(e){return Rt("MuiTypography",e)}ht("MuiTypography",["root","h1","h2","h3","h4","h5","h6","subtitle1","subtitle2","body1","body2","inherit","button","caption","overline","alignLeft","alignRight","alignCenter","alignJustify","noWrap","gutterBottom","paragraph"]);const RE={primary:!0,secondary:!0,error:!0,info:!0,success:!0,warning:!0,textPrimary:!0,textSecondary:!0,textDisabled:!0},ME=NE(),OE=e=>{const{align:r,gutterBottom:o,noWrap:i,paragraph:l,variant:u,classes:d}=e,f={root:["root",u,e.align!=="inherit"&&`align${xe(r)}`,o&&"gutterBottom",i&&"noWrap",l&&"paragraph"]};return qt(f,_E,d)},jE=je("span",{name:"MuiTypography",slot:"Root",overridesResolver:(e,r)=>{const{ownerState:o}=e;return[r.root,o.variant&&r[o.variant],o.align!=="inherit"&&r[`align${xe(o.align)}`],o.noWrap&&r.noWrap,o.gutterBottom&&r.gutterBottom,o.paragraph&&r.paragraph]}})($t(({theme:e})=>{var r;return{margin:0,variants:[{props:{variant:"inherit"},style:{font:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}},...Object.entries(e.typography).filter(([o,i])=>o!=="inherit"&&i&&typeof i=="object").map(([o,i])=>({props:{variant:o},style:i})),...Object.entries(e.palette).filter(ho()).map(([o])=>({props:{color:o},style:{color:(e.vars||e).palette[o].main}})),...Object.entries(((r=e.palette)==null?void 0:r.text)||{}).filter(([,o])=>typeof o=="string").map(([o])=>({props:{color:`text${xe(o)}`},style:{color:(e.vars||e).palette.text[o]}})),{props:({ownerState:o})=>o.align!=="inherit",style:{textAlign:"var(--Typography-textAlign)"}},{props:({ownerState:o})=>o.noWrap,style:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}},{props:({ownerState:o})=>o.gutterBottom,style:{marginBottom:"0.35em"}},{props:({ownerState:o})=>o.paragraph,style:{marginBottom:16}}]}})),Yg={h1:"h1",h2:"h2",h3:"h3",h4:"h4",h5:"h5",h6:"h6",subtitle1:"h6",subtitle2:"h6",body1:"p",body2:"p",inherit:"p"},IE=j.forwardRef(function(e,r){const{color:o,...i}=Mt({props:e,name:"MuiTypography"}),l=!RE[o],u=ME({...i,...l&&{color:o}}),{align:d="inherit",className:f,component:m,gutterBottom:g=!1,noWrap:S=!1,paragraph:y=!1,variant:v="body1",variantMapping:$=Yg,...E}=u,b={...u,align:d,color:o,className:f,component:m,gutterBottom:g,noWrap:S,paragraph:y,variant:v,variantMapping:$},T=m||(y?"p":$[v]||Yg[v])||"span",N=OE(b);return q.jsx(jE,{as:T,ref:r,className:ze(N.root,f),...E,ownerState:b,style:{...d!=="inherit"&&{"--Typography-textAlign":d},...E.style}})}),zE={entering:{opacity:1},entered:{opacity:1}},Yd=j.forwardRef(function(e,r){const o=Cp(),i={enter:o.transitions.duration.enteringScreen,exit:o.transitions.duration.leavingScreen},{addEndListener:l,appear:u=!0,children:d,easing:f,in:m,onEnter:g,onEntered:S,onEntering:y,onExit:v,onExited:$,onExiting:E,style:b,timeout:T=i,TransitionComponent:N=Qr,...A}=e,x=j.useRef(null),C=er(x,Sp(d),r),k=_=>R=>{if(_){const F=x.current;R===void 0?_(F):_(F,R)}},M=k(y),I=k((_,R)=>{PE(_);const F=Gg({style:b,timeout:T,easing:f},{mode:"enter"});_.style.webkitTransition=o.transitions.create("opacity",F),_.style.transition=o.transitions.create("opacity",F),g&&g(_,R)}),L=k(S),z=k(E),h=k(_=>{const R=Gg({style:b,timeout:T,easing:f},{mode:"exit"});_.style.webkitTransition=o.transitions.create("opacity",R),_.style.transition=o.transitions.create("opacity",R),v&&v(_)}),P=k($);return q.jsx(N,{appear:u,in:m,nodeRef:x,onEnter:I,onEntered:L,onEntering:M,onExit:h,onExited:P,onExiting:z,addEndListener:_=>{l&&l(x.current,_)},timeout:T,...A,children:(_,{ownerState:R,...F})=>j.cloneElement(d,{style:{opacity:0,visibility:_==="exited"&&!m?"hidden":void 0,...zE[_],...b,...d.props.style},ref:C,...F})})});function DE(e){return Rt("MuiBackdrop",e)}ht("MuiBackdrop",["root","invisible"]);const FE=e=>{const{classes:r,invisible:o}=e;return qt({root:["root",o&&"invisible"]},DE,r)},BE=je("div",{name:"MuiBackdrop",slot:"Root",overridesResolver:(e,r)=>{const{ownerState:o}=e;return[r.root,o.invisible&&r.invisible]}})({position:"fixed",display:"flex",alignItems:"center",justifyContent:"center",right:0,bottom:0,top:0,left:0,backgroundColor:"rgba(0, 0, 0, 0.5)",WebkitTapHighlightColor:"transparent",variants:[{props:{invisible:!0},style:{backgroundColor:"transparent"}}]}),M0=j.forwardRef(function(e,r){const o=Mt({props:e,name:"MuiBackdrop"}),{children:i,className:l,component:u="div",invisible:d=!1,open:f,components:m={},componentsProps:g={},slotProps:S={},slots:y={},TransitionComponent:v,transitionDuration:$,...E}=o,b={...o,component:u,invisible:d},T=FE(b),N={transition:v,root:m.Root,...y},A={...g,...S},x={slots:N,slotProps:A},[C,k]=_n("root",{elementType:BE,externalForwardedProps:x,className:ze(T.root,l),ownerState:b}),[M,I]=_n("transition",{elementType:Yd,externalForwardedProps:x,ownerState:b});return q.jsx(M,{in:f,timeout:$,...E,...I,children:q.jsx(C,{"aria-hidden":!0,...k,classes:T,ref:r,children:i})})});function LE(e){const r=Wr(e);return r.body===e?Js(e).innerWidth>r.documentElement.clientWidth:e.scrollHeight>e.clientHeight}function Li(e,r){r?e.setAttribute("aria-hidden","true"):e.removeAttribute("aria-hidden")}function Xg(e){return parseInt(Js(e).getComputedStyle(e).paddingRight,10)||0}function qE(e){const r=["TEMPLATE","SCRIPT","STYLE","LINK","MAP","META","NOSCRIPT","PICTURE","COL","COLGROUP","PARAM","SLOT","SOURCE","TRACK"].includes(e.tagName),o=e.tagName==="INPUT"&&e.getAttribute("type")==="hidden";return r||o}function Hg(e,r,o,i,l){const u=[r,o,...i];[].forEach.call(e.children,d=>{const f=!u.includes(d),m=!qE(d);f&&m&&Li(d,l)})}function hd(e,r){let o=-1;return e.some((i,l)=>r(i)?(o=l,!0):!1),o}function WE(e,r){const o=[],i=e.container;if(!r.disableScrollLock){if(LE(i)){const u=TE(Js(i));o.push({value:i.style.paddingRight,property:"padding-right",el:i}),i.style.paddingRight=`${Xg(i)+u}px`;const d=Wr(i).querySelectorAll(".mui-fixed");[].forEach.call(d,f=>{o.push({value:f.style.paddingRight,property:"padding-right",el:f}),f.style.paddingRight=`${Xg(f)+u}px`})}let l;if(i.parentNode instanceof DocumentFragment)l=Wr(i).body;else{const u=i.parentElement,d=Js(i);l=(u==null?void 0:u.nodeName)==="HTML"&&d.getComputedStyle(u).overflowY==="scroll"?u:i}o.push({value:l.style.overflow,property:"overflow",el:l},{value:l.style.overflowX,property:"overflow-x",el:l},{value:l.style.overflowY,property:"overflow-y",el:l}),l.style.overflow="hidden"}return()=>{o.forEach(({value:l,el:u,property:d})=>{l?u.style.setProperty(d,l):u.style.removeProperty(d)})}}function UE(e){const r=[];return[].forEach.call(e.children,o=>{o.getAttribute("aria-hidden")==="true"&&r.push(o)}),r}class QE{constructor(){this.modals=[],this.containers=[]}add(r,o){let i=this.modals.indexOf(r);if(i!==-1)return i;i=this.modals.length,this.modals.push(r),r.modalRef&&Li(r.modalRef,!1);const l=UE(o);Hg(o,r.mount,r.modalRef,l,!0);const u=hd(this.containers,d=>d.container===o);return u!==-1?(this.containers[u].modals.push(r),i):(this.containers.push({modals:[r],container:o,restore:null,hiddenSiblings:l}),i)}mount(r,o){const i=hd(this.containers,u=>u.modals.includes(r)),l=this.containers[i];l.restore||(l.restore=WE(l,o))}remove(r,o=!0){const i=this.modals.indexOf(r);if(i===-1)return i;const l=hd(this.containers,d=>d.modals.includes(r)),u=this.containers[l];if(u.modals.splice(u.modals.indexOf(r),1),this.modals.splice(i,1),u.modals.length===0)u.restore&&u.restore(),r.modalRef&&Li(r.modalRef,o),Hg(u.container,r.mount,r.modalRef,u.hiddenSiblings,!1),this.containers.splice(l,1);else{const d=u.modals[u.modals.length-1];d.modalRef&&Li(d.modalRef,!1)}return i}isTopModal(r){return this.modals.length>0&&this.modals[this.modals.length-1]===r}}const VE=["input","select","textarea","a[href]","button","[tabindex]","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable="false"])'].join(",");function KE(e){const r=parseInt(e.getAttribute("tabindex")||"",10);return Number.isNaN(r)?e.contentEditable==="true"||(e.nodeName==="AUDIO"||e.nodeName==="VIDEO"||e.nodeName==="DETAILS")&&e.getAttribute("tabindex")===null?0:e.tabIndex:r}function GE(e){if(e.tagName!=="INPUT"||e.type!=="radio"||!e.name)return!1;const r=i=>e.ownerDocument.querySelector(`input[type="radio"]${i}`);let o=r(`[name="${e.name}"]:checked`);return o||(o=r(`[name="${e.name}"]`)),o!==e}function YE(e){return!(e.disabled||e.tagName==="INPUT"&&e.type==="hidden"||GE(e))}function XE(e){const r=[],o=[];return Array.from(e.querySelectorAll(VE)).forEach((i,l)=>{const u=KE(i);u===-1||!YE(i)||(u===0?r.push(i):o.push({documentOrder:l,tabIndex:u,node:i}))}),o.sort((i,l)=>i.tabIndex===l.tabIndex?i.documentOrder-l.documentOrder:i.tabIndex-l.tabIndex).map(i=>i.node).concat(r)}function HE(){return!0}function JE(e){const{children:r,disableAutoFocus:o=!1,disableEnforceFocus:i=!1,disableRestoreFocus:l=!1,getTabbable:u=XE,isEnabled:d=HE,open:f}=e,m=j.useRef(!1),g=j.useRef(null),S=j.useRef(null),y=j.useRef(null),v=j.useRef(null),$=j.useRef(!1),E=j.useRef(null),b=er(Sp(r),E),T=j.useRef(null);j.useEffect(()=>{!f||!E.current||($.current=!o)},[o,f]),j.useEffect(()=>{if(!f||!E.current)return;const x=Wr(E.current);return E.current.contains(x.activeElement)||(E.current.hasAttribute("tabIndex")||E.current.setAttribute("tabIndex","-1"),$.current&&E.current.focus()),()=>{l||(y.current&&y.current.focus&&(m.current=!0,y.current.focus()),y.current=null)}},[f]),j.useEffect(()=>{if(!f||!E.current)return;const x=Wr(E.current),C=I=>{T.current=I,!(i||!d()||I.key!=="Tab")&&x.activeElement===E.current&&I.shiftKey&&(m.current=!0,S.current&&S.current.focus())},k=()=>{var I,L;const z=E.current;if(z===null)return;if(!x.hasFocus()||!d()||m.current){m.current=!1;return}if(z.contains(x.activeElement)||i&&x.activeElement!==g.current&&x.activeElement!==S.current)return;if(x.activeElement!==v.current)v.current=null;else if(v.current!==null)return;if(!$.current)return;let h=[];if((x.activeElement===g.current||x.activeElement===S.current)&&(h=u(E.current)),h.length>0){const P=!!((I=T.current)!=null&&I.shiftKey&&((L=T.current)==null?void 0:L.key)==="Tab"),_=h[0],R=h[h.length-1];typeof _!="string"&&typeof R!="string"&&(P?R.focus():_.focus())}else z.focus()};x.addEventListener("focusin",k),x.addEventListener("keydown",C,!0);const M=setInterval(()=>{x.activeElement&&x.activeElement.tagName==="BODY"&&k()},50);return()=>{clearInterval(M),x.removeEventListener("focusin",k),x.removeEventListener("keydown",C,!0)}},[o,i,l,d,f,u]);const N=x=>{y.current===null&&(y.current=x.relatedTarget),$.current=!0,v.current=x.target;const C=r.props.onFocus;C&&C(x)},A=x=>{y.current===null&&(y.current=x.relatedTarget),$.current=!0};return q.jsxs(j.Fragment,{children:[q.jsx("div",{tabIndex:f?0:-1,onFocus:A,ref:g,"data-testid":"sentinelStart"}),j.cloneElement(r,{ref:b,onFocus:N}),q.jsx("div",{tabIndex:f?0:-1,onFocus:A,ref:S,"data-testid":"sentinelEnd"})]})}function ZE(e){return typeof e=="function"?e():e}function e5(e){return e?e.props.hasOwnProperty("in"):!1}const Jg=()=>{},Ps=new QE;function t5(e){const{container:r,disableEscapeKeyDown:o=!1,disableScrollLock:i=!1,closeAfterTransition:l=!1,onTransitionEnter:u,onTransitionExited:d,children:f,onClose:m,open:g,rootRef:S}=e,y=j.useRef({}),v=j.useRef(null),$=j.useRef(null),E=er($,S),[b,T]=j.useState(!g),N=e5(f);let A=!0;(e["aria-hidden"]==="false"||e["aria-hidden"]===!1)&&(A=!1);const x=()=>Wr(v.current),C=()=>(y.current.modalRef=$.current,y.current.mount=v.current,y.current),k=()=>{Ps.mount(C(),{disableScrollLock:i}),$.current&&($.current.scrollTop=0)},M=mr(()=>{const _=ZE(r)||x().body;Ps.add(C(),_),$.current&&k()}),I=()=>Ps.isTopModal(C()),L=mr(_=>{v.current=_,_&&(g&&I()?k():$.current&&Li($.current,A))}),z=j.useCallback(()=>{Ps.remove(C(),A)},[A]);j.useEffect(()=>()=>{z()},[z]),j.useEffect(()=>{g?M():(!N||!l)&&z()},[g,z,N,l,M]);const h=_=>R=>{var F;(F=_.onKeyDown)==null||F.call(_,R),!(R.key!=="Escape"||R.which===229||!I())&&(o||(R.stopPropagation(),m&&m(R,"escapeKeyDown")))},P=_=>R=>{var F;(F=_.onClick)==null||F.call(_,R),R.target===R.currentTarget&&m&&m(R,"backdropClick")};return{getRootProps:(_={})=>{const R=o0(e);delete R.onTransitionEnter,delete R.onTransitionExited;const F={...R,..._};return{role:"presentation",...F,onKeyDown:h(F),ref:E}},getBackdropProps:(_={})=>{const R=_;return{"aria-hidden":!0,...R,onClick:P(R),open:g}},getTransitionProps:()=>{const _=()=>{T(!1),u&&u()},R=()=>{T(!0),d&&d(),l&&z()};return{onEnter:Vg(_,(f==null?void 0:f.props.onEnter)??Jg),onExited:Vg(R,(f==null?void 0:f.props.onExited)??Jg)}},rootRef:E,portalRef:L,isTopModal:I,exited:b,hasTransition:N}}function n5(e){return Rt("MuiModal",e)}ht("MuiModal",["root","hidden","backdrop"]);const r5=e=>{const{open:r,exited:o,classes:i}=e;return qt({root:["root",!r&&o&&"hidden"],backdrop:["backdrop"]},n5,i)},o5=je("div",{name:"MuiModal",slot:"Root",overridesResolver:(e,r)=>{const{ownerState:o}=e;return[r.root,!o.open&&o.exited&&r.hidden]}})($t(({theme:e})=>({position:"fixed",zIndex:(e.vars||e).zIndex.modal,right:0,bottom:0,top:0,left:0,variants:[{props:({ownerState:r})=>!r.open&&r.exited,style:{visibility:"hidden"}}]}))),a5=je(M0,{name:"MuiModal",slot:"Backdrop",overridesResolver:(e,r)=>r.backdrop})({zIndex:-1}),i5=j.forwardRef(function(e,r){const o=Mt({name:"MuiModal",props:e}),{BackdropComponent:i=a5,BackdropProps:l,classes:u,className:d,closeAfterTransition:f=!1,children:m,container:g,component:S,components:y={},componentsProps:v={},disableAutoFocus:$=!1,disableEnforceFocus:E=!1,disableEscapeKeyDown:b=!1,disablePortal:T=!1,disableRestoreFocus:N=!1,disableScrollLock:A=!1,hideBackdrop:x=!1,keepMounted:C=!1,onBackdropClick:k,onClose:M,onTransitionEnter:I,onTransitionExited:L,open:z,slotProps:h={},slots:P={},theme:_,...R}=o,F={...o,closeAfterTransition:f,disableAutoFocus:$,disableEnforceFocus:E,disableEscapeKeyDown:b,disablePortal:T,disableRestoreFocus:N,disableScrollLock:A,hideBackdrop:x,keepMounted:C},{getRootProps:G,getBackdropProps:H,getTransitionProps:Z,portalRef:W,isTopModal:ee,exited:K,hasTransition:re}=t5({...F,rootRef:r}),le={...F,exited:K},fe=r5(le),ce={};if(m.props.tabIndex===void 0&&(ce.tabIndex="-1"),re){const{onEnter:Ne,onExited:Ee}=Z();ce.onEnter=Ne,ce.onExited=Ee}const ie={slots:{root:y.Root,backdrop:y.Backdrop,...P},slotProps:{...v,...h}},[he,be]=_n("root",{ref:r,elementType:o5,externalForwardedProps:{...ie,...R,component:S},getSlotProps:G,ownerState:le,className:ze(d,fe==null?void 0:fe.root,!le.open&&le.exited&&(fe==null?void 0:fe.hidden))}),[Se,qe]=_n("backdrop",{ref:l==null?void 0:l.ref,elementType:i,externalForwardedProps:ie,shouldForwardComponentProp:!0,additionalProps:l,getSlotProps:Ne=>H({...Ne,onClick:Ee=>{k&&k(Ee),Ne!=null&&Ne.onClick&&Ne.onClick(Ee)}}),className:ze(l==null?void 0:l.className,fe==null?void 0:fe.backdrop),ownerState:le});return!C&&!z&&(!re||K)?null:q.jsx(h0,{ref:W,container:g,disablePortal:T,children:q.jsxs(he,{...be,children:[!x&&i?q.jsx(Se,{...qe}):null,q.jsx(JE,{disableEnforceFocus:E,disableAutoFocus:$,disableRestoreFocus:N,isEnabled:ee,open:z,children:j.cloneElement(m,ce)})]})})});function l5(e){return Rt("MuiDialog",e)}const gd=ht("MuiDialog",["root","scrollPaper","scrollBody","container","paper","paperScrollPaper","paperScrollBody","paperWidthFalse","paperWidthXs","paperWidthSm","paperWidthMd","paperWidthLg","paperWidthXl","paperFullWidth","paperFullScreen"]),O0=j.createContext({}),s5=je(M0,{name:"MuiDialog",slot:"Backdrop",overrides:(e,r)=>r.backdrop})({zIndex:-1}),u5=e=>{const{classes:r,scroll:o,maxWidth:i,fullWidth:l,fullScreen:u}=e,d={root:["root"],container:["container",`scroll${xe(o)}`],paper:["paper",`paperScroll${xe(o)}`,`paperWidth${xe(String(i))}`,l&&"paperFullWidth",u&&"paperFullScreen"]};return qt(d,l5,r)},c5=je(i5,{name:"MuiDialog",slot:"Root",overridesResolver:(e,r)=>r.root})({"@media print":{position:"absolute !important"}}),d5=je("div",{name:"MuiDialog",slot:"Container",overridesResolver:(e,r)=>{const{ownerState:o}=e;return[r.container,r[`scroll${xe(o.scroll)}`]]}})({height:"100%","@media print":{height:"auto"},outline:0,variants:[{props:{scroll:"paper"},style:{display:"flex",justifyContent:"center",alignItems:"center"}},{props:{scroll:"body"},style:{overflowY:"auto",overflowX:"hidden",textAlign:"center","&::after":{content:'""',display:"inline-block",verticalAlign:"middle",height:"100%",width:"0"}}}]}),p5=je(yu,{name:"MuiDialog",slot:"Paper",overridesResolver:(e,r)=>{const{ownerState:o}=e;return[r.paper,r[`scrollPaper${xe(o.scroll)}`],r[`paperWidth${xe(String(o.maxWidth))}`],o.fullWidth&&r.paperFullWidth,o.fullScreen&&r.paperFullScreen]}})($t(({theme:e})=>({margin:32,position:"relative",overflowY:"auto","@media print":{overflowY:"visible",boxShadow:"none"},variants:[{props:{scroll:"paper"},style:{display:"flex",flexDirection:"column",maxHeight:"calc(100% - 64px)"}},{props:{scroll:"body"},style:{display:"inline-block",verticalAlign:"middle",textAlign:"initial"}},{props:({ownerState:r})=>!r.maxWidth,style:{maxWidth:"calc(100% - 64px)"}},{props:{maxWidth:"xs"},style:{maxWidth:e.breakpoints.unit==="px"?Math.max(e.breakpoints.values.xs,444):`max(${e.breakpoints.values.xs}${e.breakpoints.unit}, 444px)`,[`&.${gd.paperScrollBody}`]:{[e.breakpoints.down(Math.max(e.breakpoints.values.xs,444)+32*2)]:{maxWidth:"calc(100% - 64px)"}}}},...Object.keys(e.breakpoints.values).filter(r=>r!=="xs").map(r=>({props:{maxWidth:r},style:{maxWidth:`${e.breakpoints.values[r]}${e.breakpoints.unit}`,[`&.${gd.paperScrollBody}`]:{[e.breakpoints.down(e.breakpoints.values[r]+32*2)]:{maxWidth:"calc(100% - 64px)"}}}})),{props:({ownerState:r})=>r.fullWidth,style:{width:"calc(100% - 64px)"}},{props:({ownerState:r})=>r.fullScreen,style:{margin:0,width:"100%",maxWidth:"100%",height:"100%",maxHeight:"none",borderRadius:0,[`&.${gd.paperScrollBody}`]:{margin:0,maxWidth:"100%"}}}]}))),f5=j.forwardRef(function(e,r){const o=Mt({props:e,name:"MuiDialog"}),i=Cp(),l={enter:i.transitions.duration.enteringScreen,exit:i.transitions.duration.leavingScreen},{"aria-describedby":u,"aria-labelledby":d,"aria-modal":f=!0,BackdropComponent:m,BackdropProps:g,children:S,className:y,disableEscapeKeyDown:v=!1,fullScreen:$=!1,fullWidth:E=!1,maxWidth:b="sm",onBackdropClick:T,onClick:N,onClose:A,open:x,PaperComponent:C=yu,PaperProps:k={},scroll:M="paper",slots:I={},slotProps:L={},TransitionComponent:z=Yd,transitionDuration:h=l,TransitionProps:P,..._}=o,R={...o,disableEscapeKeyDown:v,fullScreen:$,fullWidth:E,maxWidth:b,scroll:M},F=u5(R),G=j.useRef(),H=We=>{G.current=We.target===We.currentTarget},Z=We=>{N&&N(We),G.current&&(G.current=null,T&&T(We),A&&A(We,"backdropClick"))},W=wp(d),ee=j.useMemo(()=>({titleId:W}),[W]),K={transition:z,...I},re={transition:P,paper:k,backdrop:g,...L},le={slots:K,slotProps:re},[fe,ce]=_n("root",{elementType:c5,shouldForwardComponentProp:!0,externalForwardedProps:le,ownerState:R,className:ze(F.root,y),ref:r}),[ie,he]=_n("backdrop",{elementType:s5,shouldForwardComponentProp:!0,externalForwardedProps:le,ownerState:R}),[be,Se]=_n("paper",{elementType:p5,shouldForwardComponentProp:!0,externalForwardedProps:le,ownerState:R,className:ze(F.paper,k.className)}),[qe,Ne]=_n("container",{elementType:d5,externalForwardedProps:le,ownerState:R,className:ze(F.container)}),[Ee,Me]=_n("transition",{elementType:Yd,externalForwardedProps:le,ownerState:R,additionalProps:{appear:!0,in:x,timeout:h,role:"presentation"}});return q.jsx(fe,{closeAfterTransition:!0,slots:{backdrop:ie},slotProps:{backdrop:{transitionDuration:h,as:m,...he}},disableEscapeKeyDown:v,onClose:A,open:x,onClick:Z,...ce,..._,children:q.jsx(Ee,{...Me,children:q.jsx(qe,{onMouseDown:H,...Ne,children:q.jsx(be,{as:C,elevation:24,role:"dialog","aria-describedby":u,"aria-labelledby":W,"aria-modal":f,...Se,children:q.jsx(O0.Provider,{value:ee,children:S})})})})})});function m5(e){return Rt("MuiDialogActions",e)}ht("MuiDialogActions",["root","spacing"]);const h5=e=>{const{classes:r,disableSpacing:o}=e;return qt({root:["root",!o&&"spacing"]},m5,r)},g5=je("div",{name:"MuiDialogActions",slot:"Root",overridesResolver:(e,r)=>{const{ownerState:o}=e;return[r.root,!o.disableSpacing&&r.spacing]}})({display:"flex",alignItems:"center",padding:8,justifyContent:"flex-end",flex:"0 0 auto",variants:[{props:({ownerState:e})=>!e.disableSpacing,style:{"& > :not(style) ~ :not(style)":{marginLeft:8}}}]}),y5=j.forwardRef(function(e,r){const o=Mt({props:e,name:"MuiDialogActions"}),{className:i,disableSpacing:l=!1,...u}=o,d={...o,disableSpacing:l},f=h5(d);return q.jsx(g5,{className:ze(f.root,i),ownerState:d,ref:r,...u})});function v5(e){return Rt("MuiDialogContent",e)}ht("MuiDialogContent",["root","dividers"]);function b5(e){return Rt("MuiDialogTitle",e)}const x5=ht("MuiDialogTitle",["root"]),w5=e=>{const{classes:r,dividers:o}=e;return qt({root:["root",o&&"dividers"]},v5,r)},S5=je("div",{name:"MuiDialogContent",slot:"Root",overridesResolver:(e,r)=>{const{ownerState:o}=e;return[r.root,o.dividers&&r.dividers]}})($t(({theme:e})=>({flex:"1 1 auto",WebkitOverflowScrolling:"touch",overflowY:"auto",padding:"20px 24px",variants:[{props:({ownerState:r})=>r.dividers,style:{padding:"16px 24px",borderTop:`1px solid ${(e.vars||e).palette.divider}`,borderBottom:`1px solid ${(e.vars||e).palette.divider}`}},{props:({ownerState:r})=>!r.dividers,style:{[`.${x5.root} + &`]:{paddingTop:0}}}]}))),k5=j.forwardRef(function(e,r){const o=Mt({props:e,name:"MuiDialogContent"}),{className:i,dividers:l=!1,...u}=o,d={...o,dividers:l},f=w5(d);return q.jsx(S5,{className:ze(f.root,i),ownerState:d,ref:r,...u})}),C5=e=>{const{classes:r}=e;return qt({root:["root"]},b5,r)},$5=je(IE,{name:"MuiDialogTitle",slot:"Root",overridesResolver:(e,r)=>r.root})({padding:"16px 24px",flex:"0 0 auto"}),E5=j.forwardRef(function(e,r){const o=Mt({props:e,name:"MuiDialogTitle"}),{className:i,id:l,...u}=o,d=o,f=C5(d),{titleId:m=l}=j.useContext(O0);return q.jsx($5,{component:"h2",className:ze(f.root,i),ownerState:d,ref:r,variant:"h6",id:l??m,...u})}),T5=e=>q.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"none",...e,children:q.jsx("path",{d:"M15.1191 5L5.11914 15M5.11914 5L15.1191 15",stroke:"currentColor",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"})}),N5=Wa(T5),A5=e=>q.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"none",...e,children:q.jsx("g",{id:"Icons /General",children:q.jsx("path",{d:"M5 7.5L10 12.5L15 7.5",stroke:"currentColor",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"})})}),P5=Wa(A5),_5=e=>q.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"none",...e,children:q.jsx("g",{id:"Icons /General",children:q.jsx("path",{d:"M10.0007 7.49986V10.8332M10.0007 14.1665H10.009M18.109 14.9999L11.4423 3.3332C11.297 3.0767 11.0862 2.86335 10.8314 2.71492C10.5767 2.56649 10.2872 2.48828 9.99234 2.48828C9.69752 2.48828 9.40797 2.56649 9.15324 2.71492C8.8985 2.86335 8.6877 3.0767 8.54234 3.3332L1.87567 14.9999C1.72874 15.2543 1.6517 15.5431 1.65235 15.837C1.653 16.1308 1.73132 16.4192 1.87938 16.673C2.02744 16.9269 2.23996 17.137 2.49542 17.2822C2.75088 17.4274 3.04018 17.5025 3.33401 17.4999H16.6673C16.9598 17.4996 17.2469 17.4223 17.5001 17.2759C17.7532 17.1295 17.9634 16.9191 18.1094 16.6658C18.2555 16.4125 18.3324 16.1252 18.3323 15.8328C18.3322 15.5404 18.2552 15.2531 18.109 14.9999Z",stroke:"currentColor",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"})})}),R5=Wa(_5),M5=e=>q.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"none",...e,children:q.jsx("g",{id:"Icons /General",children:q.jsx("path",{d:"M15 12.5L10 7.5L5 12.5",stroke:"currentColor",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"})})}),O5=Wa(M5);/**
 * @license lucide-react v0.460.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const j5=y0("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]),I5="_dialogTitle_1so5b_1",z5="_dialogTitleText_1so5b_17",D5="_cancelButton_1so5b_49",F5="_cancelButtontext_1so5b_67",B5="_submitButton_1so5b_87",L5="_submitButtonText_1so5b_107",q5="_userAccordian_1so5b_127",W5="_userAccordianSummary_1so5b_151",U5="_userAccordianSummaryTitle_1so5b_165",Q5="_userAccordianDetails_1so5b_185",V5="_userDataFieldSet_1so5b_205",K5="_userData_1so5b_205",G5="_userDataField_1so5b_205",Y5="_userDataFieldHeader_1so5b_275",X5="_inputData_1so5b_299",H5="_dialogActions_1so5b_313",Bt={dialogTitle:I5,dialogTitleText:z5,cancelButton:D5,cancelButtontext:F5,submitButton:B5,submitButtonText:L5,userAccordian:q5,userAccordianSummary:W5,userAccordianSummaryTitle:U5,userAccordianDetails:Q5,userDataFieldSet:V5,userData:K5,userDataField:G5,userDataFieldHeader:Y5,inputData:X5,dialogActions:H5};function J5(e){if(typeof e!="object"||e===null)return!1;const r=e;return typeof r.data=="object"&&r.data!==null&&Array.isArray(r.data.errors)&&r.data.errors.every(o=>typeof o.message=="string"&&typeof o.errorObject=="object"&&o.errorObject!==null)}const Z5=({isOpen:e,onClose:r,userFormsData:o,onUserActionClick:i})=>{const[l,u]=j.useState([0]),[d,f]=j.useState([]),[m,g]=j.useState(!1),[S]=Vx(),y=b=>{u(T=>T.includes(b)?T.filter(N=>N!==b):[...T,b])},v=async()=>{g(!0);const b=o.map(T=>({salutation:T.salutation??"",firstName:T.firstName??"",middleName:T.middleName??"",lastName:T.lastName??"",officialEmailId:T.email??"",userId:T.userId??"",userName:T.username??"",userType:T.userType??"",status:"active",profileCompletion:0}));try{const T=await S(b).unwrap();T.status==="SUCCESS"&&(r(),i==null||i("usersummary",T))}catch(T){if(J5(T)){const N=T.data.errors.map(A=>({message:A.message,user:A.errorObject}));f(N)}else console.error("Unexpected error format",T)}finally{g(!1)}},$=({user:b})=>d.filter(T=>T.user.firstName===b.firstName).map(T=>q.jsxs(Qo,{sx:{width:"100%",flexDirection:"row",justifyContent:"space-between",alignItems:"flex-start"},children:[q.jsx(R5,{size:16}),q.jsx(yt,{variant:"body2",sx:{color:"var(--Theme-Light-Theme-Error-Main , #e1404e)"},children:T.message})]},T==null?void 0:T.message)),E=({user:b})=>{const T=[{label:"Salutation",value:b.salutation},{label:"First Name",value:b.firstName},{label:"Middle Name",value:b.middleName},{label:"Last Name",value:b.lastName},{label:"Official Email ID",value:b.email},{label:"User ID",value:b.userId},{label:"User Name",value:b.username},{label:"User Type",value:b.userType?b.userType.charAt(0).toUpperCase()+b.userType.slice(1):void 0}];return q.jsxs(Br,{className:Bt.userDataFieldSet,children:[q.jsx(Gn,{container:!0,className:Bt.userData,spacing:2,children:T.map(({label:N,value:A})=>q.jsxs(Gn,{item:!0,className:Bt.userDataField,xs:2,children:[q.jsx(yt,{className:Bt.userDataFieldHeader,children:N}),q.jsx(Nb,{TransitionComponent:Mb,title:A??"-",children:q.jsx("span",{children:q.jsx(yt,{variant:"body2",className:Bt.inputData,children:A??"-"})})})]},N))}),$({user:b})]})};return q.jsxs(f5,{open:e,onClose:r,PaperProps:{style:{width:"900px",maxWidth:"900px",height:"auto",backgroundColor:"#F7F6FD"}},children:[q.jsxs(E5,{className:Bt.dialogTitle,children:[q.jsx(yt,{className:Bt.dialogTitleText,children:"Quick Add User Details"}),q.jsx(Br,{sx:{cursor:"pointer"},children:q.jsx(N5,{onClick:r,size:16})})]}),q.jsxs(k5,{sx:{padding:2,paddingBottom:5},children:[m&&q.jsx(Br,{sx:{display:"flex",justifyContent:"center",alignItems:"center",position:"absolute",top:0,left:0,right:0,bottom:0,zIndex:1,backgroundColor:"rgba(247, 246, 253, 0.7)"},children:q.jsx(Ab,{color:"inherit"})}),q.jsx(Br,{children:o.map((b,T)=>q.jsxs(Pb,{className:Bt.userAccordian,expanded:l.includes(T),onChange:()=>y(T),children:[q.jsx(_b,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",borderRadius:"4px 4px 0px 0px",border:"1px solid var(--Light-Theme-Divider-Border-Line-Primary, #D1D5DB)",background:"#EAE9FF"},className:Bt.userAccordianSummary,expandIcon:l.includes(T)?q.jsx(O5,{size:16}):q.jsx(P5,{size:16}),children:q.jsxs(yt,{className:Bt.userAccordianSummaryTitle,children:["User ",T+1]})}),q.jsx(Rb,{className:Bt.userAccordianDetails,children:q.jsx(E,{user:b})})]},T))})]}),q.jsx(y5,{children:q.jsxs(Qo,{className:Bt.dialogActions,children:[q.jsx(qi,{id:"cancel",className:Bt.cancelButton,onClick:r,variant:"tertiary",children:q.jsx(yt,{className:Bt.cancelButtontext,children:"Cancel"})}),q.jsx(qi,{id:"submit",onClick:v,className:Bt.submitButton,disabled:m,startIcon:q.jsx(j5,{size:16,color:"#fff"}),children:q.jsx(yt,{className:Bt.submitButtonText,children:"Submit"})})]})})]})},eT=e=>q.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"none",...e,children:q.jsx("g",{id:"Icons /General",children:q.jsx("path",{d:"M1.875 10H18.125M10 1.875V18.125",stroke:"currentColor",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"})})}),tT=Wa(eT),nT="_container_1ig6m_1",rT="_header_1ig6m_19",oT="_headertext_1ig6m_31",aT="_rightheader_1ig6m_49",iT="_rightHeaderContainer_1ig6m_63",lT="_addUserButtonText_1ig6m_85",sT="_screen_1ig6m_103",Fo={container:nT,header:rT,headertext:oT,rightheader:aT,rightHeaderContainer:iT,addUserButtonText:lT,screen:sT},uT=({onUserActionClick:e})=>{const r=j.useRef(null),[o,i]=j.useState(!1),[l,u]=j.useState([]),d=()=>i(!1),f=m=>{u(m),i(!0)};return q.jsxs(Br,{className:Fo.container,children:[q.jsxs(Br,{className:Fo.header,children:[q.jsx(Tb,{onClick:()=>e==null?void 0:e("usersummary"),id:"backButton",sx:{paddingRight:"0.5rem"},children:q.jsx(xE,{})}),q.jsxs(Qo,{className:Fo.rightheader,children:[q.jsx(yt,{variant:"h4",className:Fo.headertext,children:"Quick Add User"}),q.jsx(qi,{variant:"soft",id:"rightHeaderContainer",className:Fo.rightHeaderContainer,onClick:()=>{var m;return(m=r.current)==null?void 0:m.addUserForm()},startIcon:q.jsx(tT,{size:"xsmall",color:"var(--Light-Theme-Primary-Primary-Main, #3026B9)"}),children:q.jsx(yt,{className:Fo.addUserButtonText,children:"Add User"})})]})]}),q.jsx(Qo,{className:Fo.screen,children:q.jsx(kE,{onUserActionClick:e,ref:r,onSubmit:f})}),o&&q.jsx(Z5,{onUserActionClick:e,isOpen:o,onClose:d,userFormsData:l})]})},cT={users:null,kpiMetrics:null,snackbar:{open:!1,message:"",type:"success"}},zp=Lo({name:"userSummaryReducer",initialState:cT,reducers:{setUsers:(e,r)=>{e.users=r.payload||{}},setUserKpi:(e,r)=>{e.kpiMetrics=r.payload||{}},showSnackbar:(e,r)=>{e.snackbar={open:!0,message:r.payload.message,type:r.payload.type}},hideSnackbar:e=>{e.snackbar.open=!1,e.snackbar.message=""}}});zp.actions;zp.reducer;const dT=i1({reducer:{userSummaryReducer:zp.reducer,[_s.reducerPath]:_s.reducer},middleware:e=>e().concat(_s.middleware)}),pT=e=>r=>q.jsx(Ex,{store:dT,children:q.jsx(e,{...r})}),fT=pT(uT),_T=()=>{const{showSnackbar:e}=Sb(),r=kb();return ch(Cb,{children:ch(fT,{onUserActionClick:(i,l)=>{(i==="home"||i==="usersummary")&&r($b.IWA_USER_MANAGEMENT.USERS_SUMMARY),l&&e(l.message,"info")}})})};export{_T as default};
