import{r as g,q as D,s as ve,cf as ce,a as o,x as K,j as i,T as y,b6 as Ae,E as me,at as je,au as De,G as h,ay as fe,F as ie,bI as se,ca as Be,K as L,b$ as _,ai as R,b as Ee,u as Fe,cd as Ne,al as qe,z as Te,B as V,y as ye,b4 as $e,br as Me,aE as Ce,t as P,aB as ee,aD as xe,cg as te,bp as ze,I as Oe,b1 as Pe,b8 as be}from"./index-17b8d91e.js";import{d as Le}from"./ArrowCircleLeftOutlined-2a09f8e2.js";import{d as _e}from"./EditOutlined-36c8ca4d.js";import{D as Re}from"./DatePicker-68227989.js";import"./dayjs.min-ce01f2c7.js";import{C as ke,d as We}from"./ChangeLog-0f47d713.js";import{a as Ie,b as He,S as we}from"./Stepper-88e4fb0c.js";import"./dateViewRenderers-34586552.js";import"./useSlotProps-e34e1e13.js";import"./InputAdornment-5b0053c5.js";import"./CSSTransition-30917e2c.js";import"./useMediaQuery-6a073ac5.js";import"./DesktopDatePicker-07c19cde.js";import"./useMobilePicker-9978caff.js";function Ue(s,p){return Array.isArray(p)&&p.find(k=>k.code===s)||""}const Ke=({label:s,value:p,length:H,units:k,onSave:W,fieldGroup:S,isEditMode:de,activeTabIndex:J,visibility:m,ccTabs:q,selectedRowData:oe,type:B})=>{var z,N;const[A,w]=g.useState(p),[he,Q]=g.useState(!1),v=D(t=>t.AllDropDown.dropDown),[T,$]=g.useState(!1),C=D(t=>t.costCenter.MultipleCostCenterData),a=ve();Ue(A,v),D(t=>t.edit.payload);let c=-1;for(let t=0;t<(C==null?void 0:C.length);t++)if(C[t].costCenter===oe){C[t],c=t;break}let e=q[J];const E=(t,r)=>{const u=t==null?void 0:t.find(l=>(l==null?void 0:l.fieldName)===r);return u?u.value:""},j=C[c];let b=s.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("");g.useEffect(()=>{w(p)},[p]),g.useEffect(()=>{(m==="0"||m==="Required")&&(e==="Basic Data"?(a(ce(b)),a(ce(["Name"]))):(console.log(b,"+++++++++++++++key"),a(ce(b))))},[e]);const F={label:s,value:A,units:k,type:B,visibility:m},M=(t,r)=>{console.log("fieldGroup",S,t,r),a(se({keyname:b.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""),data:r}));let u=C==null?void 0:C.map((l,re)=>{let U=q[J];if(re===c){let ae=l.viewData,G=l.viewData[U];console.log("temp",G);let O=l.viewData[U][S];return console.log("temp2",O),{...l,viewData:{...ae,[U]:{...G,[S]:O==null?void 0:O.map(I=>I.fieldName===t?{...I,value:r}:I)}}}}else return l});console.log("changedData",u),a(Be(u))};console.log("costinnerdata",j);const X=t=>{console.log("compcode",t);const r=l=>{console.log("value",l),a(R({keyName:"Currency",data:l.body}))},u=l=>{console.log(l,"error in dojax")};L(`/${_}/data/getCurrency?companyCode=${t==null?void 0:t.code}`,"get",r,u)},Y=t=>{console.log("compcode",t);const r=l=>{console.log("value",l),a(R({keyName:"Currency",data:l.body}))},u=l=>{console.log(l,"error in dojax")};L(`/${_}/data/getCurrency?companyCode=${t}`,"get",r,u)},Z=t=>{console.log("countryyyyy",t);const r=l=>{console.log("value",l),a(se({keyname:"Region",data:""})),a(R({keyName:"Region",data:l.body}))},u=l=>{console.log(l,"error in dojax")};L(`/${_}/data/getRegionBasedOnCountry?country=${t==null?void 0:t.code}`,"get",r,u)},ne=t=>{console.log("countryyyyy",t);const r=l=>{console.log("value",l),a(se({keyname:"Region",data:""})),a(R({keyName:"Region",data:l.body}))},u=l=>{console.log(l,"error in dojax")};L(`/${_}/data/getRegionBasedOnCountry?country=${t}`,"get",r,u)};return g.useEffect(()=>{s==="Country/Reg"&&ne(A),s==="Company Code"&&Y(A)},[]),o(h,{item:!0,children:o(K,{children:de?i(ie,{children:[i(y,{variant:"body2",color:"#777",children:[s," ",m==="Required"||m==="0"?o("span",{style:{color:"red"},children:"*"}):""]}),B==="Drop Down"?o(Ae,{options:v[b]??[],value:E(j.viewData[e][S],s)&&((z=v[b])==null?void 0:z.filter(t=>t.code===E(j.viewData[e][S],s)))&&((N=v[b])==null?void 0:N.filter(t=>t.code===E(j.viewData[e][S],s))[0])||"",onChange:(t,r)=>{s==="Comp Code"&&X(r),s==="Country/Reg"&&Z(r),M(s,r==null?void 0:r.code),console.log("newValue",r),w(r==null?void 0:r.code),Q(!0),console.log("keys",b)},getOptionLabel:t=>(console.log("optionn",t),t===""||(t==null?void 0:t.code)===""?"":`${t==null?void 0:t.code} - ${t==null?void 0:t.desc}`??""),renderOption:(t,r)=>(console.log("option vakue",r),o("li",{...t,children:o(y,{style:{fontSize:12},children:`${r==null?void 0:r.code} - ${r==null?void 0:r.desc}`})})),renderInput:t=>o(me,{...t,variant:"outlined",placeholder:`Select ${F.label}`,size:"small",label:null,error:T})}):B==="Input"?o(me,{variant:"outlined",size:"small",value:E(j.viewData[e][S],s).toUpperCase(),placeholder:`Enter ${F.label}`,inputProps:{maxLength:H},onChange:t=>{const r=t.target.value;if(r.length>0&&r[0]===" ")M(s,r.trimStart());else{let u=r.toUpperCase();M(s,u.trimStart())}},error:T}):B==="Calendar"?o(je,{dateAdapter:De,children:o(Re,{slotProps:{textField:{size:"small"}},value:A,placeholder:"Select Date Range",onChange:t=>{M(t),w(t)}})}):B==="Radio Button"?o(h,{item:!0,md:2,children:o(fe,{sx:{padding:0},checked:E(j.viewData[e][S],s)==!0,onChange:t=>{console.log("oncheckbox",s,t.target.checked),M(s,t.target.checked)}})}):""]}):o(ie,{children:i(ie,{children:[i(y,{variant:"body2",color:"#777",children:[s," ",m==="Required"||m==="0"?o("span",{style:{color:"red"},children:"*"}):""]}),i(y,{variant:"body2",fontWeight:"bold",children:[A,B==="Radio Button"?o(fe,{sx:{padding:0},checked:A,disabled:!0}):""]})]})})})})},ct=()=>{var O,I,ge,ue,pe;const s=Ee(),p=ve();g.useState({});const[H,k]=g.useState(0);g.useState([]);const[W,S]=g.useState(!1),[de,J]=g.useState(!0);g.useState([]),g.useState([]);const[m,q]=g.useState(0);g.useState(),D(n=>n.tabsData);const[oe,B]=g.useState(!1),[A,w]=g.useState([]),[he,Q]=g.useState(!1),v=D(n=>n.appSettings),T=Fe(),$=D(n=>n.costCenter.MultipleCostCenterRequestBench),C=T.state.tabsData,a=T.state.rowData,c=T.state.requestbenchRowData;console.log("selectedrowdata",$),console.log(T,"location=================");let e=D(n=>n.userManagement.taskData),E=D(n=>n.userManagement.userData),j=D(n=>n.edit.payload),b=D(n=>n.costCenter.requiredFields);console.log(j,b,"required_field_for_data"),console.log(e==null?void 0:e.processDesc,"task?.processDesc"),console.log(e==null?void 0:e.subject,"task?.subject"),console.log(c==null?void 0:c.requestId,"massCostRowData?.requestId"),console.log(c==null?void 0:c.requestType,"massCostRowData?.requestType");let F="";(e==null?void 0:e.processDesc)==="Mass Change"?(F=e!=null&&e.subject?(O=e==null?void 0:e.subject)==null?void 0:O.slice(3):c==null?void 0:c.requestId.slice(3),e!=null&&e.subject||a.controllingArea,e!=null&&e.subject||a.costCenter):(e==null?void 0:e.processDesc)==="Mass Create"?(F=e!=null&&e.subject?(I=e==null?void 0:e.subject)==null?void 0:I.slice(3):c==null?void 0:c.requestId.slice(3),e!=null&&e.subject||a.controllingArea,e!=null&&e.subject||a.costCenter):(c==null?void 0:c.requestType)==="Mass Create"?(F=e!=null&&e.subject?(ge=e==null?void 0:e.subject)==null?void 0:ge.slice(3):c==null?void 0:c.requestId.slice(3),e!=null&&e.subject||a.controllingArea,e!=null&&e.subject||a.costCenter):(c==null?void 0:c.requestType)==="Mass Change"&&(F=e!=null&&e.subject?(ue=e==null?void 0:e.subject)==null?void 0:ue.slice(3):c==null?void 0:c.requestId.slice(3),e!=null&&e.subject||a.controllingArea,e!=null&&e.subject||a.costCenter);for(let n=0;n<($==null?void 0:$.length);n++)if($[n].costCenter===a.costCenter){$[n];break}const M=(n,d)=>{setActiveTab(d)},X=()=>{const n=l();W?n?(q(d=>d-1),p(te())):Z():(q(d=>d-1),p(te()))},Y=()=>{const n=l();W?n?(q(d=>d+1),p(te())):Z():(q(d=>d+1),p(te()))},Z=()=>{Q(!0)},ne=()=>{S(!0),J(!1)},z=Object.entries(C==null?void 0:C.viewData).filter(n=>typeof n[1]=="object"&&n[1]!=null).map(n=>n[0]),N=Object.entries(C.viewData).filter(n=>typeof n[1]=="object"&&n[1]!=null).map(n=>Object.entries(n[1])),t={};N.map(n=>{n.forEach((d,x)=>{d.forEach((f,Se)=>{Se!==0&&f.forEach(le=>{console.log(le.fieldName),t[le.fieldName.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("")]=le.value})})})}),g.useEffect(()=>{p(Ne(t))},[]),console.log(t,"tempHash");const r=n=>{B(n)},u=()=>{B(!0)};console.log(j,b,"requiredFieldTabWise");const l=()=>ze(j,b,w),re=()=>{Q(!1)},U=n=>{const d=f=>{p(R({keyName:"HierarchyArea",data:f.body}))},x=f=>{console.log(f)};L(`/${_}/data/getHierarchyArea?controllingArea=${n}`,"get",d,x)},ae=n=>{const d=f=>{p(R({keyName:"CompanyCode",data:f.body}))},x=f=>{console.log(f)};L(`/${_}/data/getCompanyCodeBasedOnControllingArea?controllingArea=${n}`,"get",d,x)},G=n=>{const d=f=>{p(R({keyName:"ProfitCenter",data:f.body}))},x=f=>{console.log(f)};L(`/${_}/data/getProfitCenterAsPerControllingArea?controllingArea=${n}`,"get",d,x)};return g.useEffect(()=>{U(a.controllingArea),ae(a.controllingArea),G(a.controllingArea)},[]),console.log("tabcontents",N),i("div",{children:[i(h,{container:!0,style:{...qe,backgroundColor:"#FAFCFF"},children:[A.length!=0&&o(Te,{openSnackBar:he,alertMsg:"Please fill the following Field: "+A.join(", "),handleSnackBarClose:re}),i(h,{sx:{width:"inherit"},children:[i(h,{item:!0,md:12,style:{padding:"16px",display:"flex"},children:[o(h,{item:!0,style:{display:"flex",justifyContent:"flex-end"},children:o(Oe,{color:"primary","aria-label":"upload picture",component:"label",sx:Pe,children:o(Le,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{s(-1)}})})}),i(h,{md:10,children:[o(y,{variant:"h3",children:i("strong",{children:["Multiple Cost Center : ",a.costCenter," "]})}),o(y,{variant:"body2",color:"#777",children:"This view displays details of uploaded Cost Center"})]}),o(h,{md:1,sx:{display:"flex",justifyContent:"flex-end",marginRight:"4px"},children:o(P,{variant:"outlined",size:"small",sx:be,onClick:u,title:"Change Log",children:o(We,{sx:{padding:"2px"},fontSize:"small"})})}),oe&&o(ke,{open:!0,closeModal:r,requestId:F,requestType:"Mass",pageName:"costCenter",controllingArea:a.controllingArea,centerName:a.costCenter}),W?"":(E==null?void 0:E.role)==="Finance"?o(h,{md:1,sx:{display:"flex",justifyContent:"flex-end"},children:o(h,{item:!0,children:i(P,{variant:"outlined",size:"small",sx:be,onClick:ne,children:["Change",o(_e,{sx:{padding:"2px"},fontSize:"small"})]})})}):""]}),o(h,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:i(V,{width:"70%",sx:{marginLeft:"40px"},children:[o(h,{item:!0,sx:{paddingTop:"2px !important"},children:i(K,{flexDirection:"row",children:[o("div",{style:{width:"15%"},children:o(y,{variant:"body2",color:"#777",children:"Cost Center"})}),i(y,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[": ",a.costCenter]})]})}),o(h,{item:!0,sx:{paddingTop:"2px !important"},children:i(K,{flexDirection:"row",children:[o("div",{style:{width:"15%"},children:o(y,{variant:"body2",color:"#777",children:"Controlling Area"})}),i(y,{variant:"body2",fontWeight:"bold",children:[": ",a.controllingArea]})]})}),o(h,{item:!0,sx:{paddingTop:"2px !important"},children:i(K,{flexDirection:"row",children:[o("div",{style:{width:"15%"},children:o(y,{variant:"body2",color:"#777",children:"Valid From"})}),i(y,{variant:"body2",fontWeight:"bold",children:[": ",ye(a.validFrom).format(v==null?void 0:v.dateFormat)]})]})}),o(h,{item:!0,sx:{paddingTop:"2px !important"},children:i(K,{flexDirection:"row",children:[o("div",{style:{width:"15%"},children:o(y,{variant:"body2",color:"#777",children:"Valid To"})}),i(y,{variant:"body2",fontWeight:"bold",children:[": ",ye(a.validTo).format(v==null?void 0:v.dateFormat)]})]})})]})}),i(h,{container:!0,style:{padding:"16px"},children:[o(we,{activeStep:m,onChange:M,variant:"scrollable",sx:{background:"#FFFFFF",borderBottom:"1px solid #BDBDBD",width:"100%",height:"48px"},"aria-label":"mui tabs example",children:z.map((n,d)=>o(Ie,{children:o(He,{sx:{fontWeight:"700"},children:n})},n))}),o(h,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:N&&((pe=N[m])==null?void 0:pe.map((n,d)=>(console.log("narzo",n[0]),o(V,{sx:{width:"100%"},children:i(h,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...$e},children:[o(h,{container:!0,children:o(y,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:n[0]})}),o(V,{children:o(V,{sx:{width:"100%"},children:o(Me,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:o(h,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:[...n[1]].map(x=>(console.log("inneritem",n[1]),o(Ke,{activeTabIndex:m,ccTabs:z,fieldGroup:n[0],selectedRowData:a.costCenter,visibility:x.visibility,label:x.fieldName,value:x.value,length:x.maxLength,onSave:f=>handleFieldSave(x.fieldName,f),isEditMode:W,type:x.fieldType,field:x})))})})})})]})},d))))},N)]})]})]}),W?o(xe,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(Ce,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:H,onChange:n=>{k(n)},children:[o(P,{size:"small",variant:"contained",onClick:()=>{s(-1)},children:"Save"}),o(P,{variant:"contained",size:"small",sx:{...ee,mr:1},onClick:X,disabled:m===0,children:"Back"}),o(P,{variant:"contained",size:"small",sx:{...ee,mr:1},onClick:Y,disabled:m===z.length-1,children:"Next"})]})}):o(xe,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(Ce,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:H,onChange:n=>{k(n)},children:[o(P,{variant:"contained",size:"small",sx:{...ee,mr:1},onClick:X,disabled:m===0,children:"Back"}),o(P,{variant:"contained",size:"small",sx:{...ee,mr:1},onClick:Y,disabled:m===z.length-1,children:"Next"})]})})]})};export{ct as default};
