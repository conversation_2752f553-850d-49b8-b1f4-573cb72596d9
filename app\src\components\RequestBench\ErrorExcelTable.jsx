import React, { useEffect, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  Chip,
  TablePagination,
  Box,
  Tooltip,
} from "@mui/material";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import { useLocation } from "react-router-dom";
import { destination_MaterialMgmt } from "../../destinationVariables";
import { doAjax } from "@components/Common/fetchService";
import useLogger from "@hooks/useLogger";
import { END_POINTS } from "@constant/apiEndPoints";
import { API_CODE, ERROR_MESSAGES } from "@constant/enum";

const getStatusColor = (type) =>
  type.includes("Duplicate") ? "error" : "error";

const ErrorExcelTable = () => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [errorData, setErrorData] = useState([]);
  const location = useLocation();
  const urlSearchParams = new URLSearchParams(location.search.split("?")[1]);
  const RequestID = urlSearchParams.get("RequestId");
  const [backendExcelErrorMessage, setBackendExcelErrorMessage] = useState("");
  const { customError } = useLogger()

  const handleChangePage = (event, newPage) => setPage(newPage);
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const paginatedData = errorData?.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  useEffect(() => {
    handleExcelErrorHistory();
  }, []);

  const handleExcelErrorHistory = () => {
    const hSuccess = (data) => {
      if(data?.statusCode === API_CODE.STATUS_200){
        setErrorData(data?.body);
      }else{
        setBackendExcelErrorMessage(data?.message)
      }
    };
    const hError = (error) => {
      customError(error);
      setBackendExcelErrorMessage(error?.message)
    };
    doAjax(
      `/${destination_MaterialMgmt}/${END_POINTS.ERROR_HISTORY.EXCEL_ERROR_HISTORY}?requestId=${RequestID.slice(
        3
      )}`,
      "get",
      hSuccess,
      hError
    );
  };

  return (
    <Box
      sx={{ mt: 2, display: "flex", flexDirection: "column", height: "100%" }}
    >
      <TableContainer
        component={Paper}
        elevation={3}
        sx={{ borderRadius: 2, maxHeight: "56vh", overflow: "auto" }}
      >
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              {[
                "Sheet Name",
                "Error Type",
                "Row",
                "Column",
                "Error Details",
              ].map((header) => (
                <TableCell
                  key={header}
                  sx={{
                    minWidth: "150px",
                    fontWeight: "bold",
                    
                  }}
                >
                  {header}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {paginatedData?.length > 0 ? (
              paginatedData?.map((error, index) => (
                <TableRow key={index}>
                  <TableCell >{error.sheetName}</TableCell>
                  <TableCell >
                    <Chip
                      icon={
                        getStatusColor(error.errorType) === "error" ? (
                          <ErrorOutlineIcon
                            fontSize="small"
                            sx={{ color: "#dee3e2 !important" }}
                          />
                        ) : (
                          <CheckCircleOutlineIcon
                            fontSize="small"
                            sx={{ color: "#dee3e2 !important" }}
                          />
                        )
                      }
                      label={error.errorType}
                      color={getStatusColor(error.errorType)}
                      size="small"
                      sx={{
                        fontSize: "0.65rem",
                        fontWeight: 500,
                        height: 27,
                        borderRadius: "99px",
                        color: "#dee3e2 !important",
                        p: 0.3,
                      }}
                    />
                  </TableCell>
                  <TableCell >{error.rowNumber}</TableCell>
                  <TableCell >{error.columnNumber}</TableCell>
                  <TableCell>
                    <Tooltip title={error.details} arrow>
                      <Typography
                        variant="body2"
                        Wrap
                        sx={{
                          flex: 1,
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                        }}
                      >
                        {error.errorDetails}
                      </Typography>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={5} align="center">
                {backendExcelErrorMessage || ERROR_MESSAGES.NO_ERROR_FOUND }
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
      <TablePagination
        rowsPerPageOptions={[5, 10, 25]}
        count={errorData?.length ?? 0}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        sx={{ mt: 1 }}
      />
    </Box>
  );
};

export default ErrorExcelTable;
