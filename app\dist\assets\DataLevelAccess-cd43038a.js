import{r as p,o as i}from"./index-75c1660a.js";import{i as m}from"./index-e2f5b037.js";import"./react-beautiful-dnd.esm-db50900e.js";import"./useMediaQuery-33e0a836.js";import"./DialogContentText-ef8524b5.js";import"./CardMedia-f3120f7c.js";import"./Container-754d6379.js";import"./InputAdornment-a22e1655.js";import"./ListItemButton-f13df81b.js";import"./Slider-c4e5ff46.js";import"./Stepper-2dbfb76b.js";import"./StepButton-e06eb73a.js";import"./ToggleButtonGroup-63ceda7a.js";import"./index-257abd9f.js";import"./toConsumableArray-42cf6573.js";import"./Check-1e790252.js";import"./clsx-a965ebfb.js";import"./Add-62a207fb.js";import"./DeleteOutline-a8808975.js";import"./Delete-1d158507.js";import"./asyncToGenerator-88583e02.js";import"./DeleteOutlineOutlined-fefa2376.js";import"./FileDownloadOutlined-329b8f56.js";import"./AddOutlined-0d3405f9.js";import"./EditOutlined-6971b85d.js";import"./Edit-77a8cc20.js";import"./index.esm-93e9b0e6.js";import"./makeStyles-c2a7efc7.js";import"./useSlotProps-da724f1f.js";import"./Settings-bf4ffef5.js";import"./VisibilityOutlined-a5a8c4d9.js";import"./index-19916fa2.js";import"./FiberManualRecord-1a0d6be5.js";import"./dayjs.min-83c0b0e0.js";import"./CheckBox-09a94074.js";import"./DeleteOutlined-fe5b7345.js";let e=class extends p.Component{constructor(r){super(r),this.state={hasError:!1}}static getDerivedStateFromError(){return{hasError:!0}}componentDidCatch(r,o){console.error("Error caught in ErrorBoundary:",r,o)}render(){return this.state.hasError?i.jsx("div",{children:"Error fetching data"}):this.props.children}};const M=({roleDetails:t,destinations:r})=>i.jsx(e,{children:t&&i.jsx(m.TextRules,{orchestration:!1,ruleDetails:t,destinations:r,saveHandler:o=>console.log(o),translationDataObjects:[]})});export{M as default};
