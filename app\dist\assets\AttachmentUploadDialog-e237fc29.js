import{r as o,q as F,a as t,j as a,aF as I,T as l,I as x,aG as O,W as E,B as s,x as j,t as b,V as W}from"./index-75c1660a.js";import{d as L}from"./CloudUpload-d5d09566.js";import{d as M}from"./Delete-1d158507.js";import{i as N}from"./utilityImages-067c3dc2.js";const Y=({artifactId:U="",artifactName:q="",poNumber:T,isAnAttachment:$,setOpen:y,handleUpload:D})=>{const[v,_]=o.useState("Other"),[d,c]=o.useState(!1),[i,f]=o.useState([]);o.useState([]),o.useState(!1),o.useState(["Others"]);const[h,p]=o.useState(!1);o.useState({attachments:[],comments:[]}),F(e=>e.userManagement.userData);let m=()=>{f([]),y(!1)};const S=e=>{e.preventDefault(),c(!0)},B=()=>{c(!1)},k=e=>{e.preventDefault(),c(!1);const r=Array.from(e.dataTransfer.files);g(r)},w=()=>{document.getElementById("fileButton").click()},g=e=>{let r=[];e.forEach(n=>{n.metaData=v,r.push(n)}),f(n=>[...n,...r])};let C=()=>{if(!h&&i[0]){[...i],D(i);return}},u=()=>{m()},z=e=>{let r=i.filter(n=>n.id!==e);f(r)},R=()=>{let e=0;i.forEach(r=>{e+=r.size}),e>5e8?(promptAction_Functions.handleOpenPromptBox("ERROR",{title:"Warning",message:"Files size excceded",severity:"warning",cancelButton:!1}),p(!0)):p(!1)};return o.useEffect(()=>{R()},[i]),t("div",{style:{display:"flex",flexDirection:"row"},children:a(W,{fullWidth:!0,maxWidth:"sm",sx:{"& .MuiDialog-paper":{borderRadius:"12px",padding:"1rem"},overflow:"hidden"},open:!0,onClose:m,children:[a(I,{sx:{padding:"1rem 1.5rem"},children:[t(l,{variant:"h6",sx:{fontWeight:500},children:"Add New Attachment"}),t(x,{"aria-label":"close",onClick:u,sx:e=>({position:"absolute",right:12,top:10,color:e.palette.grey[500]}),children:t(O,{})})]}),a(E,{sx:{padding:"1rem",height:"max-content"},dividers:!0,children:[a(s,{className:`dropzone ${d?"dragover":""}`,sx:{width:"100%",border:`2px dashed ${d?"#3b30c8":"#d0d5dd"}`,borderRadius:"8px",padding:"2rem",backgroundColor:d?"#f8f9ff":"#fafbff",cursor:"pointer",minHeight:"200px",display:"flex",alignItems:"center",justifyContent:"center"},onDragOver:S,onDragLeave:B,onDrop:k,children:[!i[0]&&a(s,{sx:{padding:"2rem",display:"flex",justifyContent:"center",alignItems:"center",flexDirection:"column"},children:[t(L,{sx:{fontSize:48,color:"#3b30c8"}}),t(l,{children:"Drag and drop file here"}),t(l,{children:"or"}),t(l,{children:t("a",{onClick:w,children:"Browse file"})})]}),i.length>0&&a(s,{sx:{padding:"0rem",display:"flex",flexDirection:"column",gap:"1.5rem"},children:[i.map((e,r)=>{var n;return a(s,{sx:{display:"flex",alignItems:"center",padding:"1rem",borderRadius:"10px",backgroundColor:"#fff",border:"1px solid #ddd",boxShadow:"0 2px 6px rgba(0, 0, 0, 0.1)",transition:"background 0.2s ease-in-out, transform 0.1s ease-in-out","&:hover":{backgroundColor:"#f1f5f9"},width:"100%"},children:[t("img",{style:{width:"32px",height:"32px",marginRight:"1rem"},src:N[(n=e.name)==null?void 0:n.split(".")[1]],alt:"file-icon"}),t(l,{variant:"body1",sx:{flexGrow:1,fontWeight:500,fontSize:"1rem"},children:e.name}),a(l,{sx:{marginLeft:"auto",marginRight:"10%",color:h?"error.main":"gray",fontWeight:500,fontSize:"0.9rem"},children:[parseFloat(e.size/1e6).toFixed(2)," MB"]}),t(x,{id:`closeBtn-${e.id}`,size:"small",onClick:A=>{A.stopPropagation(),z(e.id)},sx:{marginLeft:"0.5rem",opacity:.8,"&:hover":{opacity:1}},children:t(M,{fontSize:"small",color:"error"})})]},r)}),t(l,{component:"div",sx:{padding:"",background:"#f9f9f9",borderRadius:"10px",fontSize:"0.95rem",lineHeight:"1.6"},children:a("ul",{style:{margin:"0",paddingLeft:"1.2rem"},children:[t("li",{style:{width:"100%",marginBottom:"0.2rem",padding:"0.5rem",background:"#f5f5f5",borderRadius:"4px"},children:"Mass Upload Process will start in the background. Once file is uploaded, you will receive a notification and an email containing the request ID number."}),a("li",{style:{width:"100%",marginBottom:"0.2rem",padding:"0.5rem",background:"#f5f5f5",borderRadius:"4px"},children:["You can visit the ",t("strong",{children:"Request Bench"})," tab, search for the request ID, and perform further actions on it."]}),a("li",{style:{width:"100%",marginBottom:"0.2rem",padding:"0.5rem",background:"#f5f5f5",borderRadius:"4px"},children:[t("strong",{children:"Note:"})," All request IDs generated in the background will initially have the status ",t("strong",{children:"Draft"})," and will be ",t("strong",{children:"Upload Successful"})," or ",t("strong",{children:"Upload Failed"})," after Uploading the Excel based on it's validation."]})]})})]}),t("input",{id:"fileButton",multiple:!1,accept:".jpeg, .jpg, .xls, .xlsx, .docx, .pdf",type:"file",name:"files",onChange:e=>g([...e.target.files]),style:{display:"none"}})]}),a(j,{direction:"row",sx:{justifyContent:"end",marginTop:"1rem",position:"relative"},children:[t(l,{sx:e=>({color:e.palette.grey,position:"absolute",left:0,top:0}),children:"*Max file size 500 MB"}),t(b,{className:"btn-mr",variant:"contained",onClick:C,children:"Upload"}),t(b,{variant:"outlined",onClick:u,children:"Cancel"})]})]})]})})};export{Y as A};
