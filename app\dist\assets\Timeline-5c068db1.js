import{eA as w,k as S,eB as L,eC as W,eD as F,eE as V,p as r,cI as g,eF as y,cE as $,dv as i,eG as j,eH as P,eI as k}from"./index-75c1660a.js";var B=w;function G(e,a){var t;return B(e,function(s,l,n){return t=a(s,l,n),!t}),!!t}var H=G,O=F,q=L,z=H,J=V,K=W;function M(e,a,t){var s=J(e)?O:z;return t&&K(e,a,t)&&(a=void 0),s(e,q(a))}var Q=M;const U=S(Q);var X=["as","children","classPrefix","last","className","dot","time","INTERNAL_active"],x=r.forwardRef(function(e,a){var t=e.as,s=t===void 0?"li":t,l=e.children,n=e.classPrefix,N=n===void 0?"timeline-item":n,u=e.last,p=e.className,v=e.dot,f=e.time,_=e.INTERNAL_active,T=g(e,X),o=y(N),C=o.merge,E=o.withClassPrefix,m=o.prefix,I=C(p,E({last:u,active:_}));return r.createElement(s,$({},T,{ref:a,className:I}),r.createElement("span",{className:m("tail")}),r.createElement("span",{className:m("dot",{"custom-dot":v})},v),f&&r.createElement("div",{className:m("time")},f),r.createElement("div",{className:m("content")},l))});x.displayName="TimelineItem";x.propTypes={last:i.bool,dot:i.node,className:i.string,children:i.node,classPrefix:i.string,as:i.elementType};const Y=x;var Z=["children","as","classPrefix","className","align","endless","isItemActive"],c=r.forwardRef(function(e,a){var t=j("Timeline",e),s=t.propsWithDefaults,l=s.children,n=s.as,N=n===void 0?"ul":n,u=s.classPrefix,p=u===void 0?"timeline":u,v=s.className,f=s.align,_=f===void 0?"left":f,T=s.endless,o=s.isItemActive,C=o===void 0?c.ACTIVE_LAST:o,E=g(s,Z),m=y(p),I=m.merge,b=m.withClassPrefix,A=P.count(l),D=U(r.Children.toArray(l),function(h){var d;return h==null||(d=h.props)===null||d===void 0?void 0:d.time}),R=I(v,b("align-"+_,{endless:T,"with-time":D}));return r.createElement(N,$({},E,{ref:a,className:R}),P.mapCloneElement(l,function(h,d){return{last:d+1===A,INTERNAL_active:C(d,A),align:_}}))});c.ACTIVE_FIRST=function(e){return e===0};c.ACTIVE_LAST=function(e,a){return e===a-1};c.Item=Y;c.displayName="Timeline";c.propTypes={as:i.elementType,className:i.string,classPrefix:i.string,children:i.node,align:k(["left","right","alternate"]),endless:i.bool};const se=c;export{se as T,U as s};
