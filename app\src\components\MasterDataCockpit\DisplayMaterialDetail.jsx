import React, { Fragment, useEffect, useState } from "react";
import { useSelector } from "react-redux";
// import "./masterDataCockpit.css";
import Box from "@mui/material/Box";
import Card from "@mui/material/Card";
import CardActions from "@mui/material/CardActions";
import CardContent from "@mui/material/CardContent";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import SummarizeIcon from "@mui/icons-material/Summarize";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import CloseIcon from "@mui/icons-material/Close";
import DescriptionIcon from "@mui/icons-material/Description";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import ReusableTable from "../Common/ReusableTable";
import InfoIcon from "@mui/icons-material/Info";
import TrackChangesTwoToneIcon from "@mui/icons-material/TrackChangesTwoTone";
import ChangeLog from "../Changelog/ChangeLog";
import { setDropDown } from "../../app/dropDownDataSlice";
import {
  Autocomplete,
  Checkbox,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  Grid,
  IconButton,
  Stack,
  Tabs,
  TextField,
  Tooltip,
  Paper,
  BottomNavigation,
  FormControl,
  FormGroup,
  FormControlLabel,
  Radio,
  RadioGroup, 
} from "@mui/material";
import {  
  Timeline,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineItem,
  TimelineSeparator,
  timelineItemClasses, } from "@mui/lab";
import InsertDriveFileIcon from "@mui/icons-material/InsertDriveFile";
import Tab from "@mui/material/Tab";
import moment from "moment";
import TabContext from "@mui/lab/TabContext";
import {
  container_Padding,
  customscrollbar,
  font_Small,
  iconButton_SpacingSmall,
  outermostContainer_Information,
  primary_Color,
  button_Outlined,
} from "../Common/commonStyles";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import SaveIcon from "@mui/icons-material/Save";
import EditableField from "./EditFieldForDisplay";
import InputDetails from "./InputDetails";
import { useNavigate, useParams, useLocation } from "react-router-dom";
// import BasicDataTab from "./BasicDataTab";
import { doAjax } from "../Common/fetchService";
import { destination_MaterialMgmt, destination_IDM } from "../../destinationVariables";
import { destination_DocumentManagement } from "../../destinationVariables";
// import { MatDownload, Matview} from "../DocumentManagement/UtilDoc";
import { MatDownload, MatView } from "../DocumentManagement/UtilDoc";
import ReusableAttachementAndComments from "../Common/ReusableAttachmentAndComments/ReusableAttachementAndComments";
import AdditionalData from "./AdditionalData";
import { useDispatch } from "react-redux";
import {
  newAdditionalData,
  newMaterialData,
  newValueForData,
} from "../../app/tabsDetailsSlice";
import ReusableSnackBar from "../Common/ReusableSnackBar";

import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { DemoContainer } from "@mui/x-date-pickers/internals/demo";
import CheckBoxOutlineBlankIcon from "@mui/icons-material/CheckBoxOutlineBlank";
import CheckBoxIcon from "@mui/icons-material/CheckBox";
import { checkIwaAccess, idGenerator } from "../../functions";
import { setPayload, setResponseFromAPI } from "../../app/editPayloadSlice";
import { Label } from "recharts";
import TableType from "../Common/ReusableFilterBox/TableType";
import DuplicateDesc from "./DuplicateDesc";
import { 
  setSingleMaterialPayload, 
  setMatRequiredFieldsGI,
  setGeneralInformation
} from "../../app/payloadslice";
import { ToastContainer, toast, Bounce } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import ReusableToast from "./ReusableToast";
import { CheckCircleOutlineOutlined } from "@mui/icons-material";


const DisplayMaterialDetail = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const location = useLocation();
  // const { reqId, requestId } = useParams();
  const [value, setValue] = useState([]);
  console.log("vallll", value);
  const [selView, setSelView] = useState('Sales')
  const [selectedItems, setSelectedItems] = useState([]);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isExtendMode, setIsExtendMode] = useState(false);
  const [isDisplayMode, setIsDisplayMode] = useState(true);
  const [isChangeLogOpen, setisChangeLogopen] = useState(false);
  const [open, setOpen] = useState(false);
  const [orgElementCode, setOrgElementCode] = useState();
  const [materialDetails, setMaterialDetails] = useState([]);
  console.log("mtdet", materialDetails);
  let [factorsArray, setFactorsArray] = useState([]);
  const [viewNameArray, setViewNameArray] = useState([]);
  console.log("viewNameArray", viewNameArray);
  const [cardName, setCardName] = useState([]);
  const [attachments, setAttachments] = useState([]);
  console.log("attt2", attachments)
  const [selectedPlantNo, setSelectedPlantNo] = useState("");
  const [additionalDataDisplay, setAdditionalDataDisplay] = useState(false);
  const [additionalDataPayload, setAdditionalDataPayload] = useState();
  const [plantLookupData, setPlantLookupData] = useState([]);
  const [salesOrgLookupData, setSalesOrgLookupData] = useState([]);
  const [checkAll, setCheckAll] = useState(false);
  const [distributionChannelLookupData, setDistributionChannelLookupData] =
    useState([]);
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [selectStatus, setSelectStatus] = useState(false);
  const [openSnackbarValidation, setopenSnackbarValidation] = useState(false);
  const [isSubmitForApproval, setIsSubmitForApproval] = useState(false);
  const [displayedFields, setDisplayedFields] = useState([]);
  const [isSaveAsDraft, setIsSaveAsDraft] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [messageDialogSeverity, setMessageDialogSeverity] = useState(false);
  const [messageDialogOK, setMessageDialogOK] = useState(true);
  const [successMsg, setsuccessMsg] = useState(false);
  const [messageDialogExtra, setMessageDialogExtra] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState(false);
  const [correctionPopup, setCorrectionPopoup] = useState(false);
  const [selectedOrgElements, setSelectedOrgElements] = useState([]);
  const [selectedSalesOrg, setSelectedSalesOrg] = useState("");
  const [selectedDistributionChannel, setSelectedDistributionChannel] =
    useState("");
  const [filteredViewType, setFilteredViewType] = useState([]);
  const [fieldReference, setFieldReference] = useState([]);
  const [materialTypeDesc, setMaterialTypeDesc] = useState([]);
  const [materialTypeCode, setMaterialTypeCode] = useState([]);
  const [comments, setComments] = useState([]);
  const [mmNumber, setMmNumber] = useState("");
  const [activeTab, setActiveTab] = useState(0);
  const [openSelectViews, setOpenSelectViews] = useState(false);
  const [isApprovalMode, setIsApprovalMode] = useState(false);
  const [headerData, setHeaderData] = useState();
  const [openSelectCorrection, setOpenSelectCorrection] = useState(false);
  const [additionalDisplayData, setAdditionalDisplayData] = useState();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const appSettings = useSelector((state) => state.appSettings);
  const [IDs, setIDs] = useState();

  const [isLoadingToast, setIsLoadingToast] = useState(false);
  const [toastType, setToastType] = useState('');
  const [toastMessage, setToastMessage] = useState('');
  const [isToastPromise, setIsToastPromise] = useState(false);
  console.log("isLoadingToast", isLoadingToast);
  console.log("toastType", toastType);
  console.log("toastMessage", toastMessage);
  console.log("isToastPromise", isToastPromise);


  const [openApproveBasicDataCreateDialog, setOpenApproveBasicDataCreateDialog] = useState(false);
  const [remarks, setRemarks] = useState("");

  let materialData = location.state;
  console.log("mtdata", materialData);
  let editData = useSelector((state) => state.edit);
  let response = useSelector((state) => state.edit.responseFromAPI);
  console.log(editData?.payload?.ItemCategoryGroup);
  let displayDescription = useSelector(
    (state) => state.commonFilter.NewMaterial
  );
  let additionalData = useSelector((state) => state?.tabsData?.additionalData);
  let displayData = useSelector((state) => state?.tabsData?.dataToSend);
  let taskData = useSelector((state) => state?.initialData?.IWMMyTask);
  let taskRowDetails = useSelector((state) => state.userManagement.taskData);
  const icon = <CheckBoxOutlineBlankIcon fontSize="small" />;
  const checkedIcon = <CheckBoxIcon fontSize="small" />;
  const valueRequiredForSelectViews = useSelector(
    (state) => state.tabsData["value"]
  );
  console.log("valueRequiredForSelectViews", valueRequiredForSelectViews)
  console.log('attachments', attachments)
  let userData = useSelector((state) => state.userManagement.userData);
  let iwaAccessData = useSelector(
    (state) => state.userManagement.entitiesAndActivities?.["Display Material"]
  );
  const applicationConfig = useSelector((state) => state.applicationConfig);

  const singleMatPayload = useSelector((state) => state.payload.singleMatPayload);
  
  const [attachmentFromIdm, setAttachmentFromIdm] = useState([]);

  console.log("userData", userData);
  console.log("materialData", materialData);
  console.log("taskRowDetails", taskRowDetails);
  console.log("editData", editData);
  console.log("displayData", displayData.orgData[0].info.code);
  console.log("taskData", taskData);
  console.log("valueRequiredForSelectViews", valueRequiredForSelectViews);
  console.log("materialDetails", materialDetails);
  console.log("activeTab", activeTab);
  console.log("response", response);
  console.log("selectedOrgElements", selectedOrgElements);
  console.log("displayDescription", displayDescription);
  console.log("requestType", materialData?.requestType);
  console.log(
    "dateeee",
    "/Date(" + Date.parse(editData?.payload?.ValidFrom) + ")/"
  );
  console.log(
    "CombinedCondition:",
    !isEditMode &&
    (taskRowDetails?.requestType === "Extend" ||
      materialData?.requestType === "Extend")
  );
  console.log(
    "CombinedCondition2:",
    !isEditMode &&
    (taskRowDetails?.requestType === "Extend" ||
      materialData?.requestType === "Extend") && (userData?.role === "Approver" &&
        materialDetails[activeTab]?.category === "Basic Data")
  );

  //   let result;
  // if (displayData?.orgData[0]?.info?.code) {
  //   result = displayData?.orgData[0]?.info?.code;
  // } else if (materialData?.result === "Not Available") {
  //   result = "";
  // } else if (materialData?.result) {
  //   result = materialData?.result?.slice(0, 4);
  // } else if (response?.plantOrg) {
  //   result = response?.plantOrg;
  // } else if (taskData?.body?.plantOrg) {
  //   result = taskData?.body?.plantOrg;
  // } else {
  //   result = "";
  // }
  // console.log("result",result);

  const dataToSend = {
    orgData: [
      { info: selectedPlantNo, desc: "Plant" },
      { info: selectedSalesOrg, desc: "Sales Organization" },
      { info: selectedDistributionChannel, desc: "Distribution Channel" },
      // { info: selectedStorageLocation, desc: "Storage Location" },
      // { info: selectedMRPProfile, desc: "MRP Profile" },
      // { info: selectedForecastProfile, desc: "Forecast Profile" },
      // { info: selectedWarehouseNo, desc: "Warehouse No" },
      // { info: selectedStorageType, desc: "StorageType" },
    ],
  };

  let singleGIPayloadMain = useSelector((state) => state.payload.singleMatPayload);
  const questions = useSelector((state) => state.payload.generalInformation);
  console.log("qeeeeee", questions);
  const [ruleData, setRuleData] = useState([]);
  const [attachmentsData, setAttachmentsData] = useState([]);
  const [selectedCheckboxes, setSelectedCheckboxes] = useState([]);

  

  var payload = {
    ProductID: IDs?.ProductID ? IDs?.ProductID : "",
    TaskId: taskRowDetails?.taskId ? taskRowDetails?.taskId : "",
    BasicDataID: IDs?.BasicDataID ? IDs?.BasicDataID : "",
    // ProductID: 17,
    Product: materialData?.materialNumber
      ? materialData?.materialNumber
      : headerData?.Product
        ? headerData?.Product
        : "",
    ProductType: headerData?.ProductType
      ? headerData?.ProductType
      : materialData?.materialType
        ? materialData?.materialType.slice(0, 4)
        : "",
    // ViewNames: isEditMode && (materialData?.requestType==="Extend" || taskRowDetails?.requestType === "Extend")
    // ? materialDetails[activeTab]?.category
    // :factorsArray.join(","),
    ViewNames: viewNameArray.join(","),
    CorrectionViewNames: selectedCheckboxes.join(",") ? selectedCheckboxes.join(",") : "",
    CreationID: headerData?.CreationID ? headerData?.CreationID : "",
    Description: materialData?.materialDesc,
    Remarks: remarks ? remarks : "",
    EditID: headerData?.EditID ? headerData?.EditID : "",
    ExtendID: headerData?.ExtendID ? headerData?.ExtendID : "",
    MassCreationID: headerData?.MassCreationID
      ? headerData?.MassCreationID
      : "",
    MassEditID: headerData?.MassEditID ? headerData?.MassEditID : " ",
    MassExtendID: headerData?.MassExtendID ? headerData?.MassExtendID : "",
    CrossPlantStatus: "01",
    CrossPlantStatusValidityDate: "/Date(1694995200000)/",
    // CrossPlantStatusValidityDate: null,
    // CreationDate: "",
    // CreatedByUser: "",
    // LastChangeDate: "",
    // LastChangeDateTime: "",
    // ReqCreatedOn: headerData?.ReqCreatedOn ? headerData?.ReqCreatedOn : "",
    ReqCreatedOn: headerData?.ReqCreatedOn
      ? "/Date(" + Date.parse(headerData?.ReqCreatedOn) + ")/"
      : "",
    ReqCreatedBy: headerData?.ReqCreatedBy
      ? headerData?.ReqCreatedBy
      : userData?.user_id,
    // ReqUpdatedOn: headerData?.ReqUpdatedOn?headerData?.ReqUpdatedOn:"",
    IsMarkedForDeletion: headerData?.IsMarkedForDeletion
      ? headerData?.IsMarkedForDeletion
      : "",
    ProductOldID: editData?.payload?.Oldmaterialnumber
      ? editData?.payload.Oldmaterialnumber
      : "",
    GrossWeight: editData?.payload?.GrossWeight
      ? editData?.payload?.GrossWeight
      : "",
    PurchaseOrderQuantityUnit: editData?.payload?.OrderUnit
      ? editData?.payload?.OrderUnit
      : "",
    SourceOfSupply: "",
    WeightUnit: editData?.payload?.WeightUnit
      ? editData?.payload?.WeightUnit
      : "",
    NetWeight: editData?.payload?.NetWeight ? editData?.payload?.NetWeight : "",
    CountryOfOrigin: "",
    CompetitorID: "",
    ProductGroup: editData?.payload?.MaterialGroup
      ? editData?.payload?.MaterialGroup
      : "",
    BaseUnit: editData?.payload?.BaseUnit ? editData?.payload?.BaseUnit : "",
    ItemCategoryGroup: editData?.payload?.ItemCategoryGroup
      ? editData?.payload?.ItemCategoryGroup
      : "NORM",
    ProductHierarchy: editData?.payload?.Producthierarchy
      ? editData?.payload?.Producthierarchy
      : "",
    Division: editData?.payload?.Division ? editData?.payload?.Division : "",
    VarblPurOrdUnitIsActive: editData?.payload?.VarPurOrderUnitActive
      ? editData?.payload?.VarPurOrderUnitActive
      : "",
    VolumeUnit: editData?.payload?.VolumeUnit
      ? editData?.payload?.VolumeUnit
      : "",
    MaterialVolume: editData?.payload?.Volume ? editData?.payload?.Volume : "",
    ANPCode: editData?.payload?.ANPCode ? editData?.payload?.ANPCode : "",
    Brand: "",
    ProcurementRule: "",
    ValidityStartDate: "",
    LowLevelCode: "",
    ProdNoInGenProdInPrepackProd: "",
    SerialIdentifierAssgmtProfile: editData?.payload?.SerialNumberProfile
      ? editData?.payload?.SerialNumberProfile
      : "",
    SizeOrDimensionText: editData?.payload?.Sizedimensions
      ? editData?.payload?.Sizedimensions
      : "",
    IndustryStandardName: editData?.payload?.IndustryStandardName
      ? editData?.payload?.IndustryStandardName
      : "",
    ProductStandardID: editData?.payload?.InternationalArticleNumberEANUPC
      ? editData?.payload?.InternationalArticleNumberEANUPC
      : "",
    InternationalArticleNumberCat: editData?.payload
      ?.CategoryofInternationalArticleNumberEAN
      ? editData?.payload?.CategoryofInternationalArticleNumberEAN
      : "",
    ProductIsConfigurable: editData?.payload?.ConfigurableMaterial
      ? editData?.payload?.ConfigurableMaterial
      : "false",
    IsBatchManagementRequired: editData?.payload?.BatchManagement
      ? editData?.payload?.BatchManagement
      : "false",
    ExternalProductGroup: editData?.payload?.ExtMatlGroup
      ? editData?.payload?.ExtMatlGroup
      : "",
    CrossPlantConfigurableProduct: editData?.payload
      ?.CrossPlantConfigurableMaterial
      ? editData?.payload?.CrossPlantConfigurableMaterial
      : "",
    SerialNoExplicitnessLevel: "",
    ProductManufacturerNumber: "",
    ManufacturerNumber: "",
    ManufacturerPartProfile: "",
    QltyMgmtInProcmtIsActive: "false",
    IndustrySector: response?.headerData?.IndustrySector
      ? response?.headerData?.IndustrySector
      : "",
    ChangeNumber: editData?.payload?.DocChangeNumber
      ? editData?.payload?.DocChangeNumber
      : "",
    MaterialRevisionLevel: "",
    HandlingIndicator: editData?.payload?.HandlingIndicator
      ? editData?.payload?.HandlingIndicator
      : "",
    WarehouseProductGroup: editData?.payload?.WarehouseMaterialGroup
      ? editData?.payload?.WarehouseMaterialGroup
      : "",
    WarehouseStorageCondition: editData?.payload?.WarehouseStorageCondition
      ? editData?.payload?.WarehouseStorageCondition
      : "",
    StandardHandlingUnitType: editData?.payload?.StdHUType
      ? editData?.payload?.StdHUType
      : "",
    SerialNumberProfile: editData?.payload?.SerialNumberProfile
      ? editData?.payload?.SerialNumberProfile
      : "",
    AdjustmentProfile: "",
    PreferredUnitOfMeasure: "",
    IsPilferable: editData?.payload?.Pilferable
      ? editData?.payload?.Pilferable
      : "false",
    IsRelevantForHzdsSubstances: editData?.payload
      ?.RelevantforHazardousSubstances
      ? editData?.payload?.RelevantforHazardousSubstances
      : "false",
    QuarantinePeriod: editData?.payload?.QuarantinePeriod
      ? editData?.payload?.QuarantinePeriod
      : "0",
    TimeUnitForQuarantinePeriod: "",
    QualityInspectionGroup: editData?.payload?.QualityInspectionGroup
      ? editData?.payload?.QualityInspectionGroup
      : "",
    AuthorizationGroup: editData?.payload?.AuthorizationGroup
      ? editData?.payload?.AuthorizationGroup
      : "",
    HandlingUnitType: editData?.payload?.HandlingUnitType
      ? editData?.payload?.HandlingUnitType
      : "",
    HasVariableTareWeight: editData?.payload?.VariableTareWeight
      ? editData?.payload?.VariableTareWeight
      : "false",
    MaximumPackagingLength: editData?.payload?.MaximumPackagingLength
      ? editData?.payload?.MaximumPackagingLength
      : "0",
    MaximumPackagingWidth: editData?.payload?.MaximumPackagingWidth
      ? editData?.payload?.MaximumPackagingWidth
      : "0",
    MaximumPackagingHeight: editData?.payload?.MaximumPackagingHeight
      ? editData?.payload?.MaximumPackagingHeight
      : "0",
    UnitForMaxPackagingDimensions: "",
    IsFirstChangeLogCommit: true,
    RequestPriority: singleMatPayload?.ChoosePriorityLevel
        ? singleMatPayload?.ChoosePriorityLevel
        : "",
    BusinessJustification: singleMatPayload?.BusinessJustification
        ? singleMatPayload?.BusinessJustification
        : "",
    to_Description: additionalDisplayData?.Description?.Data?.map((x) => {
      return {
        Product: materialData?.materialNumber
          ? materialData?.materialNumber
          : headerData?.Product
            ? headerData?.Product
            : "",
        DescriptionID: x.DescriptionID ? x.DescriptionID : "",
        // Language: x.Language?x.Language:"EN",
        Language: "EN",
        ProductDescription: x?.MaterialDescription
          ? x?.MaterialDescription
          : "",
      };
    }),
    to_ProductUnitsOfMeasure: additionalDisplayData?.UnitsOfMeasure?.Data?.map(
      (x) => {
        return {
        Product : materialData?.materialNumber
        ? materialData?.materialNumber
        : headerData?.Product
          ? headerData?.Product
          : "",
      AlternativeUnit : "PC",
      QuantityNumerator : "1",
      QuantityDenominator : "1",
      MaterialVolume : "12.000",
      VolumeUnit : "M3",
      GrossWeight : "18.000",
      WeightUnit : "KG",
      GlobalTradeItemNumber : "2150000000000",
      GlobalTradeItemNumberCategory : "EA",
      UnitSpecificProductLength : "0.000",
      UnitSpecificProductWidth : "0.000",
      UnitSpecificProductHeight : "0.000",
      ProductMeasurementUnit : "",
      LowerLevelPackagingUnit : "",
      RemainingVolumeAfterNesting : "0",
      MaximumStackingFactor : 0,
      CapacityUsage : "0.000",
      BaseUnit : "EA"
          // UomID: x?.UnitOfMeasureID ? x?.UnitOfMeasureID : "",
          // Product: materialData?.materialNumber
          //   ? materialData?.materialNumber
          //   : headerData?.Product
          //     ? headerData?.Product
          //     : "",
          // // AlternativeUnit: x?.AlternativeUnit?x?.AlternativeUnit:"",
          // AlternativeUnit: x?.AlternativeUnit ? x?.AlternativeUnit : "",
          // QuantityNumerator: x?.QuantityNumerator ? x?.QuantityNumerator : "",
          // QuantityDenominator: x?.QuantityDenominator
          //   ? x?.QuantityDenominator
          //   : "",
          // MaterialVolume: x?.MaterialVolume ? x?.MaterialVolume : "",
          // VolumeUnit: x?.VolumeUnit ? x?.VolumeUnit : "",
          // GrossWeight: x?.GrossWeight ? x?.GrossWeight : "",
          // WeightUnit: x?.WeightUnit ? x?.WeightUnit : "",
          // GlobalTradeItemNumber: x?.GlobalTradeItemNumber
          //   ? x?.GlobalTradeItemNumber
          //   : "",
          // GlobalTradeItemNumberCategory: x?.GlobalTradeItemNumberCategory
          //   ? x?.GlobalTradeItemNumberCategory
          //   : "",
          // UnitSpecificProductLength: x?.UnitSpecificProductLength
          //   ? x?.UnitSpecificProductLength
          //   : "",
          // UnitSpecificProductWidth: x?.UnitSpecificProductWidth
          //   ? x?.UnitSpecificProductWidth
          //   : "",
          // UnitSpecificProductHeight: x?.UnitSpecificProductHeight
          //   ? x?.UnitSpecificProductHeight
          //   : "",
          // ProductMeasurementUnit: x?.ProductMeasurementUnit
          //   ? x?.ProductMeasurementUnit
          //   : "",
          // LowerLevelPackagingUnit: x?.LowerLevelPackagingUnit
          //   ? x?.LowerLevelPackagingUnit
          //   : "",
          // RemainingVolumeAfterNesting: x?.RemainingVolumeAfterNesting
          //   ? x?.RemainingVolumeAfterNesting
          //   : "",
          // MaximumStackingFactor: x?.MaximumStackingFactor
          //   ? x?.MaximumStackingFactor
          //   : 0,
          // CapacityUsage: x?.CapacityUsage ? x?.CapacityUsage : "",
          // BaseUnit: editData?.payload?.BaseUnit
          //   ? editData?.payload?.BaseUnit
          //   : "",
        };
      }
    ),

    to_ProductStorage: {
      StorageID: "",
      Product: materialData?.materialNumber
        ? materialData?.materialNumber
        : headerData?.Product
          ? headerData?.Product
          : "",
      StorageConditions: "",
      TemperatureConditionInd: "",
      HazardousMaterialNumber: "",
      NmbrOfGROrGISlipsToPrintQty: "",
      LabelType: "",
      LabelForm: "",
      MinRemainingShelfLife: "",
      ExpirationDate: "",
      ShelfLifeExpirationDatePeriod: "",
      TotalShelfLife: "",
      BaseUnit: editData?.payload?.BaseUnit ? editData?.payload?.BaseUnit : "",
    },
    to_ProductSalesTax: [
        {
            SalesTaxID: IDs?.SalesDeliveryID ? IDs?.SalesDeliveryID : "",
            Product: "",
            Country: "US",
            TaxCategory: "UTXJ",
            TaxClassification: "1",
            Status: ""
        },
        {
            SalesTaxID: IDs?.SalesDeliveryID ? IDs?.SalesDeliveryID : "",
            Product: "",
            Country: "IN",
            TaxCategory: "JOSG",
            TaxClassification: "1",
            Status: ""
        },
        {
            SalesTaxID: IDs?.SalesDeliveryID ? IDs?.SalesDeliveryID : "",
            Product: "",
            Country: "IN",
            TaxCategory: "JOIG",
            TaxClassification: "1",
            Status: ""
        },
        {
            SalesTaxID: IDs?.SalesDeliveryID ? IDs?.SalesDeliveryID : "",
            Product: "",
            Country: "IN",
            TaxCategory: "JOUG",
            TaxClassification: "1",
            Status: ""
        },
        {
          SalesTaxID: IDs?.SalesDeliveryID ? IDs?.SalesDeliveryID : "",
          Product: "",
          Country: "IN",
          TaxCategory: "JOCG",
          TaxClassification: "1",
          Status: ""
      }
    ],
    to_ProductSales: {
      SalesID: IDs?.SalesID ? IDs?.SalesID : "",
      Product: materialData?.materialNumber
        ? materialData?.materialNumber
        : headerData?.Product
          ? headerData?.Product
          : "",
      SalesStatus: editData?.payload?.Xdistrchainstatus
        ? editData?.payload?.Xdistrchainstatus
        : "",
      SalesStatusValidityDate: editData?.payload?.ValidFrom1
        ? editData?.payload?.ValidFrom1
        : "",
      // TaxClassification: editData?.payload?.TaxClassification
      //   ? editData?.payload?.TaxClassification
      //   : "1",
      TaxClassification:"1",
      TransportationGroup: editData?.payload?.TranspGroup
        ? editData?.payload?.TranspGroup
        : "",
    },
    to_ProductQualityMgmt: {
      QualityMgmtID: "",
      Product: materialData?.materialNumber
        ? materialData?.materialNumber
        : headerData?.Product
          ? headerData?.Product
          : "",
      QltyMgmtInProcmtIsActive: "false",
    },
    to_ProductPurchaseText: [
      {
        PurchaseTextID: "",
        Product: materialData?.materialNumber
          ? materialData?.materialNumber
          : headerData?.Product
            ? headerData?.Product
            : "",
        Language: "EN",
        LongText: "",
      },
    ],
    to_ProductProcurement: {
      ProcurementID: IDs?.ProcurementID ? IDs?.ProcurementID : "",
      Product: materialData?.materialNumber
        ? materialData?.materialNumber
        : headerData?.Product
          ? headerData?.Product
          : "",
      PurchaseOrderQuantityUnit: editData?.payload?.OrderUnit
        ? editData?.payload?.OrderUnit
        : "",
      VarblPurOrdUnitStatus: editData?.payload?.VarPurOrderUnitActive
        ? editData?.payload?.VarPurOrderUnitActive
        : "",
      PurchasingAcknProfile: editData?.payload?.PurchasingvalueKey
        ? editData?.payload?.PurchasingvalueKey
        : "",
    },
    to_ProductInspectionText: additionalDisplayData?.InspectionText?.Data?.map(
      (x) => {
        return {
          Product: materialData?.materialNumber
            ? materialData?.materialNumber
            : headerData?.Product
              ? headerData?.Product
              : "",
          InspectionTextID: x.InspectionTextID ? x.InspectionTextID : "",
          Language: "EN",
          LongText: x.LongText ? x.LongText : "",
        };
      }
    ),

    to_ProductBasicText: additionalDisplayData?.BasicDataText?.Data?.map(
      (x) => {
        return {
          Product: materialData?.materialNumber
            ? materialData?.materialNumber
            : headerData?.Product
              ? headerData?.Product
              : "",
          BasicDataTextID: x.BasicDataTextID ? x.BasicDataTextID : "",
          Language: "EN",
          LongText: x.LongText ? x.LongText : "",
        };
      }
    ),
    to_Plant: [
      {
        PlantID: IDs?.PlantID ? IDs?.PlantID : "",
        Product: materialData?.materialNumber
          ? materialData?.materialNumber
          : headerData?.Product
            ? headerData?.Product
            : "",
        Plant: dataToSend?.orgData[0]?.info
          ? dataToSend?.orgData[0]?.info?.code
          : materialData?.plant === "Not Available"
            ? ""
            : materialData?.plant
              ? materialData?.plant?.slice(0, 4)
              : response?.plantOrg
                ? response?.plantOrg
                : taskData?.body?.plantOrg
                  ? taskData?.body?.plantOrg
                  : "",
        PurchasingGroup: editData?.payload?.PurchasingGroup
          ? editData?.payload?.PurchasingGroup
          : "",
        // PurchasingGroup: "001",
        CountryOfOrigin: "",
        RegionOfOrigin: "",
        ProductionInvtryManagedLoc: "",
        ProfileCode: editData?.payload?.PlantSpMatStatus
          ? editData?.payload?.PlantSpMatStatus
          : "",
        ProfileValidityStartDate: editData?.payload?.ValidFrom
          ? "/Date(1694995200000)/"
          : // ?`/Date(${new Date(editData?.payload?.ValidFrom).getTime()})/`
          "",
        AvailabilityCheckType: "SR",
        FiscalYearVariant: "",
        PeriodType: "",
        ProfitCenter: "",
        Commodity: "",
        GoodsReceiptDuration: editData?.payload?.GRProcessingTime
          ? editData?.payload?.GRProcessingTime
          : "",
        MaintenanceStatusName: "",
        IsMarkedForDeletion: "false",
        MRPType: "",
        MRPResponsible: "",
        ABCIndicator: "",
        MinimumLotSizeQuantity: "",
        MaximumLotSizeQuantity: "",
        FixedLotSizeQuantity: "",
        ConsumptionTaxCtrlCode: editData?.payload?.ControlCode
          ? editData?.payload?.ControlCode
          : "",
        IsCoProduct: "false",
        ProductIsConfigurable: "",
        StockDeterminationGroup: "",
        StockInTransferQuantity: "",
        StockInTransitQuantity: "",
        HasPostToInspectionStock: editData?.payload?.PostToInspStock
          ? editData?.payload?.PostToInspStock
          : "false",
        IsBatchManagementRequired: editData?.payload?.BatchManagementPlant
          ? editData?.payload?.BatchManagementPlant
          : "false",
        SerialNumberProfile: "",
        IsNegativeStockAllowed: "false",
        GoodsReceiptBlockedStockQty: "",
        HasConsignmentCtrl: "",
        FiscalYearCurrentPeriod: "",
        FiscalMonthCurrentPeriod: "",
        ProcurementType: "",
        IsInternalBatchManaged: editData?.payload?.BatchManagement
          ? editData?.payload?.BatchManagement
          : "",
        ProductCFOPCategory: "",
        ProductIsExciseTaxRelevant: "false",
        BaseUnit: editData?.payload?.BaseUnit
          ? editData?.payload?.BaseUnit
          : "",
        ConfigurableProduct: "",
        GoodsIssueUnit: "",
        MaterialFreightGroup: editData?.payload?.MaterialFreightGroup
          ? editData?.payload?.MaterialFreightGroup
          : "",
        OriginalBatchReferenceMaterial: "",
        OriglBatchManagementIsRequired: "",
        ProductIsCriticalPrt: editData?.payload?.CriticalPart
          ? editData?.payload?.CriticalPart
          : "false",
        ProductLogisticsHandlingGroup: "",
        to_PlantMRPArea: [
          {
            PlantMRPAreaID: "",
          },
        ],
        to_PlantQualityMgmt: {
          PlantQualityMgmtID: "",
          Product: materialData?.materialNumber
            ? materialData?.materialNumber
            : headerData?.Product
              ? headerData?.Product
              : "",
          Plant: dataToSend?.orgData[0]?.info
            ? dataToSend?.orgData[0]?.info?.code
            : materialData?.plant === "Not Available"
              ? ""
              : materialData?.plant
                ? materialData?.plant?.slice(0, 4)
                : response?.plantOrg
                  ? response?.plantOrg
                  : taskData?.body?.plantOrg
                    ? taskData?.body?.plantOrg
                    : "",
          MaximumStoragePeriod: "",
          QualityMgmtCtrlKey: "",
          MatlQualityAuthorizationGroup: "",
          HasPostToInspectionStock: "false",
          InspLotDocumentationIsRequired: "false",
          SuplrQualityManagementSystem: "",
          RecrrgInspIntervalTimeInDays: "",
          ProductQualityCertificateType: "",
        },
        to_PlantSales: {
          PlantSalesID: "",
          Product: materialData?.materialNumber
            ? materialData?.materialNumber
            : headerData?.Product
              ? headerData?.Product
              : "",
          Plant: dataToSend?.orgData[0]?.info
            ? dataToSend?.orgData[0]?.info?.code
            : materialData?.plant === "Not Available"
              ? ""
              : materialData?.plant
                ? materialData?.plant?.slice(0, 4)
                : response?.plantOrg
                  ? response?.plantOrg
                  : taskData?.body?.plantOrg
                    ? taskData?.body?.plantOrg
                    : "",
          LoadingGroup: "",
          ReplacementPartType: "",
          CapPlanningQuantityInBaseUoM: "",
          ProductShippingProcessingTime: "",
          WrkCentersShipgSetupTimeInDays: "",
          AvailabilityCheckType: "SR",
          BaseUnit: editData?.payload?.BaseUnit
            ? editData?.payload?.BaseUnit
            : "",
        },
        to_PlantStorage: {
          PlantStorageID: "",
          Product: materialData?.materialNumber
            ? materialData?.materialNumber
            : headerData?.Product
              ? headerData?.Product
              : "",
          Plant: dataToSend?.orgData[0]?.info
            ? dataToSend?.orgData[0]?.info?.code
            : materialData?.plant === "Not Available"
              ? ""
              : materialData?.plant
                ? materialData?.plant?.slice(0, 4)
                : response?.plantOrg
                  ? response?.plantOrg
                  : taskData?.body?.plantOrg
                    ? taskData?.body?.plantOrg
                    : "",
          InventoryForCycleCountInd: "",
          ProvisioningServiceLevel: "",
          CycleCountingIndicatorIsFixed: "false",
          ProdMaximumStoragePeriodUnit: "",
          WrhsMgmtPtwyAndStkRemovalStrgy: "",
        },
        to_PlantText: [{}],
        to_ProdPlantInternationalTrade: {
          PlantInternationalTradeID: IDs?.PlantInternationalTradeID
            ? IDs?.PlantInternationalTradeID
            : "",
          Product: materialData?.materialNumber
            ? materialData?.materialNumber
            : headerData?.Product
              ? headerData?.Product
              : "",
          Plant: dataToSend?.orgData[0]?.info
            ? dataToSend?.orgData[0]?.info?.code
            : materialData?.plant === "Not Available"
              ? ""
              : materialData?.plant
                ? materialData?.plant?.slice(0, 4)
                : response?.plantOrg
                  ? response?.plantOrg
                  : taskData?.body?.plantOrg
                    ? taskData?.body?.plantOrg
                    : "",
          CountryOfOrigin: editData?.payload?.CountryRegionofOrigin
            ? editData?.payload?.CountryRegionofOrigin
            : "",
          RegionOfOrigin: editData?.payload?.RegionOfOrigin
            ? editData?.payload?.RegionOfOrigin
            : "",
          ConsumptionTaxCtrlCode: "",
          ProductCASNumber: editData?.payload?.CASNumberPharm
            ? editData?.payload?.CASNumberPharm
            : "",
          ProdIntlTradeClassification: editData?.payload?.PRODCOMNo
            ? editData?.payload?.PRODCOMNo
            : "",
          ExportAndImportProductGroup: editData?.payload?.IntrastatGroup
            ? editData?.payload?.IntrastatGroup
            : "",
        },
        to_ProductPlantCosting: {
          PlantCostingID: "",
          Product: materialData?.materialNumber
            ? materialData?.materialNumber
            : headerData?.Product
              ? headerData?.Product
              : "",
          Plant: dataToSend?.orgData[0]?.info
            ? dataToSend?.orgData[0]?.info?.code
            : materialData?.plant === "Not Available"
              ? ""
              : materialData?.plant
                ? materialData?.plant?.slice(0, 4)
                : response?.plantOrg
                  ? response?.plantOrg
                  : taskData?.body?.plantOrg
                    ? taskData?.body?.plantOrg
                    : "",
          IsCoProduct: "false",
          CostingLotSize: "",
          VarianceKey: "",
          BaseUnit: editData?.payload?.BaseUnit
            ? editData?.payload?.BaseUnit
            : "",
          TaskListGroupCounter: "",
          TaskListGroup: "",
          TaskListType: "",
          CostingProductionVersion: "",
          IsFixedPriceCoProduct: "false",
          CostingSpecialProcurementType: "",
          SourceBOMAlternative: "",
          ProductBOMUsage: "",
          ProductIsCostingRelevant: "false",
        },
        to_ProductPlantForecast: {
          PlantForecastID: "",
          Product: materialData?.materialNumber
            ? materialData?.materialNumber
            : headerData?.Product
              ? headerData?.Product
              : "",
          Plant: dataToSend?.orgData[0]?.info
            ? dataToSend?.orgData[0]?.info?.code
            : materialData?.plant === "Not Available"
              ? ""
              : materialData?.plant
                ? materialData?.plant?.slice(0, 4)
                : response?.plantOrg
                  ? response?.plantOrg
                  : taskData?.body?.plantOrg
                    ? taskData?.body?.plantOrg
                    : "",
          ConsumptionRefUsageEndDate: "",
          ConsumptionQtyMultiplier: "0",
          ConsumptionReferenceProduct: "",
          ConsumptionReferencePlant: "",
        },
        to_ProductPlantProcurement: {
          PlantProcurementID: IDs?.PlantProcurementID
            ? IDs?.PlantProcurementID
            : "",
          Product: materialData?.materialNumber
            ? materialData?.materialNumber
            : headerData?.Product
              ? headerData?.Product
              : "",
          Plant: dataToSend?.orgData[0]?.info
            ? dataToSend?.orgData[0]?.info?.code
            : materialData?.plant === "Not Available"
              ? ""
              : materialData?.plant
                ? materialData?.plant?.slice(0, 4)
                : response?.plantOrg
                  ? response?.plantOrg
                  : taskData?.body?.plantOrg
                    ? taskData?.body?.plantOrg
                    : "",
          IsAutoPurOrdCreationAllowed: editData?.payload?.AutomaticPO
            ? editData?.payload?.AutomaticPO
            : "",
          IsSourceListRequired: editData?.payload?.SourceList
            ? editData?.payload?.SourceList
            : "false",
          SourceOfSupplyCategory: "",
          ItmIsRlvtToJITDelivSchedules: editData?.payload?.JITSchedIndicator
            ? editData?.payload?.JITSchedIndicator
            : "",
        },
        to_ProductSupplyPlanning: {
          PlantSupplyPlanningID: IDs?.PlantSupplyPlanningID
            ? IDs?.PlantSupplyPlanningID
            : "",
          Product: materialData?.materialNumber
            ? materialData?.materialNumber
            : headerData?.Product
              ? headerData?.Product
              : "",
          Plant: dataToSend?.orgData[0]?.info
            ? dataToSend?.orgData[0]?.info?.code
            : materialData?.plant === "Not Available"
              ? ""
              : materialData?.plant
                ? materialData?.plant?.slice(0, 4)
                : response?.plantOrg
                  ? response?.plantOrg
                  : taskData?.body?.plantOrg
                    ? taskData?.body?.plantOrg
                    : "",
          FixedLotSizeQuantity: "",
          MaximumLotSizeQuantity: "",
          MinimumLotSizeQuantity: "",
          LotSizeRoundingQuantity: "",
          LotSizingProcedure: "",
          MRPType: "",
          MRPResponsible: "",
          SafetyStockQuantity: "",
          MinimumSafetyStockQuantity: "",
          PlanningTimeFence: "",
          ABCIndicator: "",
          MaximumStockQuantity: "",
          ReorderThresholdQuantity: "",
          PlannedDeliveryDurationInDays: "",
          SafetyDuration: "",
          PlanningStrategyGroup: "",
          TotalReplenishmentLeadTime: "",
          ProcurementType: "",
          ProcurementSubType: "",
          AssemblyScrapPercent: "",
          AvailabilityCheckType: "SR",
          GoodsReceiptDuration: "",
          MRPGroup: "",
          DfltStorageLocationExtProcmt: "",
          ProdRqmtsConsumptionMode: "",
          BackwardCnsmpnPeriodInWorkDays: "",
          FwdConsumptionPeriodInWorkDays: "",
          BaseUnit: editData?.payload?.BaseUnit
            ? editData?.payload?.BaseUnit
            : "",
          PlanAndOrderDayDetermination: "",
          RoundingProfile: "",
          LotSizeIndependentCosts: "",
          MRPPlanningCalendar: "",
          RangeOfCvrgPrflCode: "",
          IsSafetyTime: "",
          PerdPrflForSftyTme: "",
          IsMRPDependentRqmt: "",
          InHouseProductionTime: "",
          ProductIsForCrossProject: "",
          StorageCostsPercentageCode: "",
          SrvcLvl: "0",
          MRPAvailabilityType: "",
          FollowUpProduct: "",
          RepetitiveManufacturingIsAllwd: "false",
          DependentRequirementsType: "",
          IsBulkMaterialComponent: "false",
          RepetitiveManufacturingProfile: "",
          RqmtQtyRcptTaktTmeInWrkgDays: "",
          ForecastRequirementsAreSplit: "",
          EffectiveOutDate: null,
          MRPProfile: "",
          ComponentScrapInPercent: "",
          ProductIsToBeDiscontinued: "",
          ProdRqmtsAreConsolidated: "",
          MatlCompIsMarkedForBackflush: "",
          ProposedProductSupplyArea: "",
          Currency: "",
          PlannedOrderActionControl: "",
        },
        to_ProductWorkScheduling: {
          PlantWorkSchedulingID: IDs?.PlantWorkSchedulingID
            ? IDs?.PlantWorkSchedulingID
            : "",
          Product: materialData?.materialNumber
            ? materialData?.materialNumber
            : headerData?.Product
              ? headerData?.Product
              : "",
          Plant: dataToSend?.orgData[0]?.info
            ? dataToSend?.orgData[0]?.info?.code
            : materialData?.plant === "Not Available"
              ? ""
              : materialData?.plant
                ? materialData?.plant?.slice(0, 4)
                : response?.plantOrg
                  ? response?.plantOrg
                  : taskData?.body?.plantOrg
                    ? taskData?.body?.plantOrg
                    : "",
          MaterialBaseQuantity: "",
          UnlimitedOverDelivIsAllowed: "false",
          OverDelivToleranceLimit: "",
          UnderDelivToleranceLimit: "",
          ProductionInvtryManagedLoc: "",
          BaseUnit: editData?.payload?.BaseUnit
            ? editData?.payload?.BaseUnit
            : "",
          ProductProcessingTime: "",
          ProductionSupervisor: "",
          ProductProductionQuantityUnit: "",
          ProdnOrderIsBatchRequired: "",
          TransitionMatrixProductsGroup: "",
          OrderChangeManagementProfile: "",
          MatlCompIsMarkedForBackflush: "",
          SetupAndTeardownTime: "",
          ProductionSchedulingProfile: "",
          TransitionTime: "",
        },
        to_StorageLocation: [
          {
            StorageLocationID: "",
          },
        ],
      },
    ],
    to_SalesDelivery: [
      {
        SalesDeliveryID: IDs?.SalesDeliveryID ? IDs?.SalesDeliveryID : "",
        Product: materialData?.materialNumber
          ? materialData?.materialNumber
          : headerData?.Product
            ? headerData?.Product
            : "",
        ProductSalesOrg: dataToSend?.orgData[1]?.info
          ? dataToSend?.orgData[1]?.info?.code
          : materialData?.salesOrg
            ? materialData?.salesOrg
            : taskData?.body?.salesOrg
              ? taskData?.body?.salesOrg
              : "",
        ProductDistributionChnl: materialData?.distChnl
          ? materialData?.distChnl
          : taskData?.body?.distChnl
            ? taskData?.body?.distChnl
            : dataToSend?.orgData[2]?.info
              ? dataToSend?.orgData[2]?.info?.code
              : "",
        MinimumOrderQuantity: editData?.payload?.MinOrderQty
          ? editData?.payload?.MinOrderQty
          : "",
        SupplyingPlant: editData?.payload?.DeliveringPlant
          ? editData?.payload?.DeliveringPlant
          : "",
        PriceSpecificationProductGroup: editData?.payload?.MaterialPriceGroup
          ? editData?.payload?.MaterialPriceGroup
          : "",
        AccountDetnProductGroup: editData?.payload
          ?.AccountAssignmentGroupMaterial
          ? editData?.payload?.AccountAssignmentGroupMaterial
          : "",
        DeliveryNoteProcMinDelivQty: editData?.payload?.MinDelQty
          ? editData?.payload?.MinDelQty
          : "",
        ItemCategoryGroup: editData?.payload?.ItemCategoryGroup
          ? editData?.payload?.ItemCategoryGroup
          : "NORM",
        // ItemCategoryGroup:"NORM",
        DeliveryQuantityUnit: "",
        DeliveryQuantity: editData?.payload?.DeliveryUnit
          ? editData?.payload?.DeliveryUnit
          : "5",
        ProductSalesStatus: editData?.payload?.DChainspecstatus
          ? editData?.payload?.DChainspecstatus
          : "",
        ProductSalesStatusValidityDate: editData?.payload?.ValidFrom2
          ? editData?.payload?.ValidFrom2
          : "",
        SalesMeasureUnit: editData?.payload?.SalesUnit
          ? editData?.payload?.SalesUnit
          : "",
        IsMarkedForDeletion: "false",
        ProductHierarchy: editData?.payload?.Producthierarchy
          ? editData?.payload?.Producthierarchy
          : "",
        FirstSalesSpecProductGroup: editData?.payload?.MaterialGroup1
          ? editData?.payload?.MaterialGroup1
          : "",
        SecondSalesSpecProductGroup: editData?.payload?.MaterialGroup2
          ? editData?.payload?.MaterialGroup2
          : "",
        ThirdSalesSpecProductGroup: editData?.payload?.MaterialGroup3
          ? editData?.payload?.MaterialGroup3
          : "",
        FourthSalesSpecProductGroup: editData?.payload?.MaterialGroup4
          ? editData?.payload?.MaterialGroup4
          : "",
        FifthSalesSpecProductGroup: editData?.payload?.MaterialGroup5
          ? editData?.payload?.MaterialGroup5
          : "",
        MinimumMakeToOrderOrderQty: "",
        BaseUnit: editData?.payload?.BaseUnit
          ? editData?.payload?.BaseUnit
          : "",
        LogisticsStatisticsGroup: editData?.payload?.MaterialStatisticsGroup
          ? editData?.payload?.MaterialStatisticsGroup
          : "",
        VolumeRebateGroup: editData?.payload?.VolumeRebateGroup
          ? editData?.payload?.VolumeRebateGroup
          : "",
        ProductCommissionGroup: editData?.payload?.ComissionGroup
          ? editData?.payload?.ComissionGroup
          : "",
        CashDiscountIsDeductible: editData?.payload?.CashDiscount
          ? editData?.payload?.CashDiscount
          : "",
        PricingReferenceProduct: editData?.payload?.PricingRefMaterial
          ? editData?.payload?.PricingRefMaterial
          : "",
        RoundingProfile: editData?.payload?.RoundingProfile
          ? editData?.payload?.RoundingProfile
          : "",
        ProductUnitGroup: editData?.payload?.UnitofMeasureGrp
          ? editData?.payload?.UnitofMeasureGrp
          : "",
        VariableSalesUnitIsNotAllowed: editData?.payload?.SalesUnitNotVariable
          ? editData?.payload?.SalesUnitNotVariable
          : "",
        ProductHasAttributeID01: editData?.payload?.ProductAttribute1
          ? editData?.payload?.ProductAttribute1
          : "",
        ProductHasAttributeID02: editData?.payload?.ProductAttribute2
          ? editData?.payload?.ProductAttribute2
          : "",
        ProductHasAttributeID03: editData?.payload?.ProductAttribute3
          ? editData?.payload?.ProductAttribute3
          : "",
        ProductHasAttributeID04: editData?.payload?.ProductAttribute4
          ? editData?.payload?.ProductAttribute4
          : "",
        ProductHasAttributeID05: editData?.payload?.ProductAttribute5
          ? editData?.payload?.ProductAttribute5
          : "",
        ProductHasAttributeID06: editData?.payload?.ProductAttribute6
          ? editData?.payload?.ProductAttribute6
          : "",
        ProductHasAttributeID07: editData?.payload?.ProductAttribute7
          ? editData?.payload?.ProductAttribute7
          : "",
        ProductHasAttributeID08: editData?.payload?.ProductAttribute8
          ? editData?.payload?.ProductAttribute8
          : "",
        ProductHasAttributeID09: editData?.payload?.ProductAttribute9
          ? editData?.payload?.ProductAttribute9
          : "",
        ProductHasAttributeID10: editData?.payload?.ProductAttribute10
          ? editData?.payload?.ProductAttribute10
          : "",
          to_SalesTax: [  {
            SalesTaxID:"",
          },],
        to_SalesText: [
          {
            SalesTextID: "",
          },
        ],
      },
    ],
    to_Valuation: [
      {
        ValuationID: IDs?.ValuationID ? IDs?.ValuationID : "",
        Product: materialData?.materialNumber
          ? materialData?.materialNumber
          : headerData?.Product
            ? headerData?.Product
            : "",
        ValuationArea: materialData?.plantOrg
          ? materialData?.plantOrg
          : taskData?.plantOrg
            ? taskData?.plantOrg
            : "",
        ValuationType: "",
        ValuationClass: editData?.payload?.ValuationClass
          ? editData?.payload?.ValuationClass
          : "",
        PriceDeterminationControl: editData?.payload?.PriceControl
          ? editData?.payload?.PriceControl
          : "",
        StandardPrice: editData?.payload?.StandardPrice
          ? editData?.payload?.StandardPrice
          : "",
        PriceUnitQty: editData?.payload?.PerUnitPrice
          ? editData?.payload?.PerUnitPrice
          : "",
        InventoryValuationProcedure: "",
        IsMarkedForDeletion: "false",
        MovingAveragePrice: "",
        ValuationCategory: "",
        ProductUsageType: "",
        ProductOriginType: "",
        IsProducedInhouse: "false",
        ProdCostEstNumber: "",
        ProjectStockValuationClass: "",
        ValuationClassSalesOrderStock: "",
        PlannedPrice1InCoCodeCrcy: "",
        PlannedPrice2InCoCodeCrcy: "",
        PlannedPrice3InCoCodeCrcy: "",
        FuturePlndPrice1ValdtyDate: "",
        FuturePlndPrice2ValdtyDate: "",
        FuturePlndPrice3ValdtyDate: "",
        TaxBasedPricesPriceUnitQty: "",
        PriceLastChangeDate: "",
        PlannedPrice: "0",
        PrevInvtryPriceInCoCodeCrcy: "",
        Currency: "",
        BaseUnit: editData?.payload?.BaseUnit
          ? editData?.payload?.BaseUnit
          : "",
        to_MLAccount: [
          {
            MLAccountID: "",
          },
        ],
        to_MLPrices: [
          {
            MLPricesID: "",
          },
        ],
        to_ValuationAccount: {
          ValuationAccountID: IDs?.ValuationAccountID
            ? IDs?.ValuationAccountID
            : "",
          Product: materialData?.materialNumber
            ? materialData?.materialNumber
            : headerData?.Product
              ? headerData?.Product
              : "",
          ValuationArea: materialData?.plantOrg
            ? materialData?.plantOrg
            : taskData?.plantOrg
              ? taskData?.plantOrg
              : "",
          ValuationType: "",
          CommercialPrice1InCoCodeCrcy: "",
          CommercialPrice2InCoCodeCrcy: "",
          CommercialPrice3InCoCodeCrcy: "",
          DevaluationYearCount: "",
          FutureEvaluatedAmountValue: "",
          FuturePriceValidityStartDate: "",
          IsLIFOAndFIFORelevant: "false",
          LIFOValuationPoolNumber: "",
          TaxPricel1InCoCodeCrcy: "",
          TaxPrice2InCoCodeCrcy: "",
          TaxPrice3InCoCodeCrcy: "",
          Currency: "",
        },
        to_ValuationCosting: {
          ValuationCostingID: "",
          Product: materialData?.materialNumber
            ? materialData?.materialNumber
            : headerData?.Product
              ? headerData?.Product
              : "",
          ValuationArea: materialData?.plantOrg
            ? materialData?.plantOrg
            : taskData?.plantOrg
              ? taskData?.plantOrg
              : "",
          ValuationType: "",
          IsMaterialCostedWithQtyStruc: "false",
          IsMaterialRelatedOrigin: "false",
          CostOriginGroup: "",
          CostingOverheadGroup: "",
        },
      },
    ],
  };
  console.log("bd" , materialDetails[activeTab]?.category);
  console.log("factorsArray", factorsArray);

  const getOptionLabel = (fieldName, option) => {
    switch (fieldName) {
      case "Plant":
        return option?.plant === ""
          ? "Select Plant"
          : `${option?.code} - ${option?.desc}`;
      case "Sales Organization":
        return option?.Salesorg === ""
          ? "Select Sales Organization"
          : `${option?.code} - ${option?.desc}`;
      case "Distribution Channel":
        return option?.DistributionChannel === ""
          ? "Select Distribution Channel"
          : `${option?.code} - ${option?.desc}`;
      // case "Storage Location":
      //   return option?.StorgaeLoc === ""
      //     ? "Select Storage Location"
      //     : `${option?.desc}`;
      case "MRP Profile":
        return option?.plant === ""
          ? "Select MRP Profile"
          : `${option?.code} - ${option?.desc}`;
      // case "Forecast Profile":
      //   return option?.plant === ""
      //     ? "Select Forecast Profile"
      //     : `${option?.code} - ${option?.desc}`;
      case "Warehouse No":
        return option?.WareHouseNo === "" ? "Select Plant" : `${option?.code}`;
      // case "Storage Type":
      //   return option?.Description === "" ? "Select Plant" : `${option?.code}`;
      default:
        return "";
    }
  };
  const renderOption = (fieldName, props, option) => {
    switch (fieldName) {
      case "Plant":
      case "Sales Organization":
      case "Distribution Channel":
      // case "Storage Location":
      case "MRP Profile":
      // case "Forecast Profile":
      case "Warehouse No":
        // case "Storage Type":
        return (
          <li {...props}>
            <Typography style={{ fontSize: 12 }}>
              {getOptionLabel(fieldName, option)}
            </Typography>
          </li>
        );
      default:
        return null;
    }
  };
  const plantDependentOrgElement = (e) => {
    // if (displayedFields.includes("Storage Location")) {
    //   const hSuccess = (data) => {
    //     setStorageLocationLookupData(data.body);
    //   };
    //   const hError = (error) => {
    //     console.log(error);
    //   };
    //   doAjax(
    //     `/${destination_MaterialMgmt}/data/getStorageLocationForPlant?plant=${e.code}`,
    //     "get",
    //     hSuccess,
    //     hError
    //   );
    // }
    if (displayedFields.includes("Warehouse No")) {
      const hSuccess = (data) => {
        setWareHouseNoLookupData(data.body);
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        `/${destination_MaterialMgmt}/data/getWareHouseNoForPlant?plant=${e.code}`,
        "get",
        hSuccess,
        hError
      );
    }
  };
  const salesOrgDependentOrgElement = (e) => {
    if (displayedFields.includes("Distribution Channel")) {
      const hSuccess = (data) => {
        setDistributionChannelLookupData(data.body);
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        `/${destination_MaterialMgmt}/data/getDistributionForSalesOrg?salesOrg=${e.code}`,
        "get",
        hSuccess,
        hError
      );
    }
  };

  const checkAllChange = (e) => {
    setSelectStatus(true);
    setValue(autocompleteOptions);
    dispatch(newValueForData(autocompleteOptions));
    nmSearchForm.selectViewsValue = autocompleteOptions;
  };

  const openChangeLog = () => {
    setisChangeLogopen(true);
  };

  const handleFieldChange = (fieldName, selectedValue) => {
    // console.log("selected", selectedValue);
    switch (fieldName) {
      case "Plant":
        plantDependentOrgElement(selectedValue);
        setSelectedPlantNo(selectedValue);
        setSelectedOrgElements([...selectedOrgElements, "Plant"]);
        break;
      case "Sales Organization":
        salesOrgDependentOrgElement(selectedValue);
        setSelectedSalesOrg(selectedValue);
        setSelectedOrgElements([...selectedOrgElements, "Sales Organization"]);
        break;
      case "Distribution Channel":
        // salesOrgDependentOrgElement(selectedValue);
        setSelectedDistributionChannel(selectedValue);
        setSelectedOrgElements([
          ...selectedOrgElements,
          "Distribution Channel",
        ]);
        // Handle distribution channel change
        break;
      case "Warehouse No":
        // warehouseNoDependentOrgElement(selectedValue);
        setSelectedWarehouseNo(selectedValue);
        break;
      // case "Storage Location":
      // salesOrgDependentOrgElement(selectedValue);
      // setSelectedStorageLocation(selectedValue);
      // Handle Storage Location change
      // break;
      case "MRP Profile":
        // salesOrgDependentOrgElement(selectedValue);
        setSelectedMRPProfile(selectedValue);
        // Handle MRP Profile change
        break;
      // case "Forecast Profile":
      //   // salesOrgDependentOrgElement(selectedValue);
      //   setSelectedForecastProfile(selectedValue);
      //   // Handle Forecast Profile change
      //   break;

      // case "Storage Type":
      //   // salesOrgDependentOrgElement(selectedValue);
      //   setSelectedStorageType(selectedValue);
      //   // Handle Storage Type change
      //   break;
      default:
        break;
    }
  };

  const handleTextFieldClick = () => {
    setIsDialogOpen(true);
  };
  const handleDialogProceed = () => {
    dispatch(newMaterialData(dataToSend));
    setIsDisplayMode(true);
    setFactorsArray(valueRequiredForSelectViews.map((item) => item.label));

    setIsExtendMode(false);
    setOpen(false);
    handleTaskCreateForExtend(dataToSend);
    console.log("dataToSend", dataToSend);
  };
  // const saveAsDraft = () => {
  //   setIsEditMode(false);
  //   handleSnackBarOpen();
  //   setIsSaveAsDraft(true);
  //   handleSaveAsDraft();
  // };
  const onSubmitBasicData = () => {
    setIsEditMode(false);
    handleSnackBarOpen();
    setIsSaveAsDraft(true);
    handleSubmitBasicData();
  };

  const onApproveBasicDataCreateDialog = () => {
    setOpenApproveBasicDataCreateDialog(true);
  }
  const handleApproveBasicDataCreateDialog = () => {
    setOpenApproveBasicDataCreateDialog(false);
  }
  const handleRemarks = (e, value) => {
    const newValue = e.target.value;
    if (newValue.length > 0 && newValue[0] === " ") {
      setRemarks(newValue.trimStart());
    } else {
      //let costCenterValue = e.target.value;
      let remarksUpperCase = newValue.toUpperCase();
      setRemarks(remarksUpperCase);
    }
  };
  const onApproveBasicDataCreate = () => {
    setIsEditMode(false);
    handleSnackBarOpen();
    setIsSaveAsDraft(true);
    handleApproveBasicDataCreate();
  };
  const onApproveAllViewsCreate = () => {
    setIsEditMode(false);
    // handleSnackBarOpen();
    setIsSaveAsDraft(true);
    handleApproveAllViewsCreate();
  };

  const onSubmitForReviewSalesCreate = () => {
    setIsEditMode(false);
    // handleSnackBarOpen();
    setIsSaveAsDraft(true);
    handleSubmitForReviewSalesCreate();
  };
  const onSubmitForReviewPurchasingCreate = () => {
    setIsEditMode(false);
    // handleSnackBarOpen();
    setIsSaveAsDraft(true);
    handleSubmitForReviewPurchasingCreate();
  };
  const onSubmitForReviewAccountingCreate = () => {
    setIsEditMode(false);
    // handleSnackBarOpen();
    setIsSaveAsDraft(true);
    handleSubmitForReviewAccountingCreate();
  };
  const onSubmitForApprovalBasicCreate = () => {
    setIsEditMode(false);
    // handleSnackBarOpen();
    setIsSaveAsDraft(true);
    handleSubmitForApprovalBasicCreate();
  };
  const onSubmitForReviewSales = () => {
    setIsEditMode(false);
    // handleSnackBarOpen();
    setIsSaveAsDraft(true);
    handleSubmitForReviewSales();
  };

  const onSubmitForReviewPurchasing = () => {
    setIsEditMode(false);
    // handleSnackBarOpen();
    setIsSaveAsDraft(true);
    handleSubmitForReviewPurchasing();
  };
  const saveAsDraftSalesExtend = () => {
    setIsEditMode(false);
    handleSnackBarOpen();
    setIsSaveAsDraft(true);
    handleSaveAsDraftSalesExtend();
  };
  const saveAsDraftPurchasingExtend = () => {
    setIsEditMode(false);
    handleSnackBarOpen();
    setIsSaveAsDraft(true);
    handleSaveAsDraftPurchasingExtend();
  };
  const saveAsDraftAccountingExtend = () => {
    setIsEditMode(false);
    handleSnackBarOpen();
    setIsSaveAsDraft(true);
    handleSaveAsDraftAccountingExtend();
  };
  const onSubmitForReviewSalesExtend = () => {
    setIsEditMode(false);
    handleSnackBarOpen();
    setIsSaveAsDraft(true);
    handleSubmitForReviewSalesExtend();
  };
  const onSubmitForReviewPurchasingExtend = () => {
    setIsEditMode(false);
    handleSnackBarOpen();
    setIsSaveAsDraft(true);
    handleSubmitForReviewPurchasingExtend();
  };
  const onSubmitForReviewAccountingExtend = () => {
    setIsEditMode(false);
    handleSnackBarOpen();
    setIsSaveAsDraft(true);
    handleSubmitForReviewAccountingExtend();
  };
  const onSubmitForApprovalExtend = () => {
    // setIsEditMode(false);
    // handleSnackBarOpen();
    // setIsSaveAsDraft(true);
    handleSubmitForApprovalExtend();
  };
  const onApproveExtend = () => {
    setIsEditMode(false);
    handleSnackBarOpen();
    setIsSaveAsDraft(true);
    handleApproveExtend();
  };

  const onSubmitForReviewAccounting = () => {
    setIsEditMode(false);
    handleSnackBarOpen();
    setIsSaveAsDraft(true);
    handleSubmitForReviewAccounting();
  };

  const onSubmitForApprovalBasicData = () => {
    setIsEditMode(false);
    // handleSnackBarOpen();
    setIsSaveAsDraft(true);
    handleSubmitForApprovalBasicData();
  };

  const saveAsDraftBasicData = () => {
    setIsEditMode(false);
    handleSnackBarOpen();
    setIsSaveAsDraft(true);
    handleSaveAsDraftBasicData();
  };
  const saveAsDraftSales = () => {
    setIsEditMode(false);
    handleSnackBarOpen();
    setIsSaveAsDraft(true);
    handleSaveAsDraftSales();
  };
  const saveAsDraftPurchasing = () => {
    setIsEditMode(false);
    handleSnackBarOpen();
    setIsSaveAsDraft(true);
    handleSaveAsDraftPurchasing();
  };
  const saveAsDraftAccounting = () => {
    setIsEditMode(false);
    handleSnackBarOpen();
    setIsSaveAsDraft(true);
    handleSaveAsDraftAccounting();
  };
  const onCreateSaveAsDraft = () => {
    if (materialDetails[activeTab]?.category === "Sales") {
      var url = `/${destination_MaterialMgmt}/alter/saveSalesViewAsDraft`;
    } else if (materialDetails[activeTab]?.category === "Purchasing") {
      var url = `/${destination_MaterialMgmt}/alter/savePurchaseViewAsDraft`;
    } else if (materialDetails[activeTab]?.category === "Accounting") {
      var url = `/${destination_MaterialMgmt}/alter/savePurchaseViewAsDraft`;
    }
    const hSuccess = (data) => {
      // console.log("Data edited successfully");
      setMessageDialogMessage(
        `Material has been saved Succesfully with NMS${data.body}`
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(url, "post", hSuccess, hError, payload);
  };

  const onSave = () => {
    handleSnackBarOpen();
    handleSubmitForApproval();
  };

  const onApprove = () => {
    handleSubmitForApprove();
  };
  const onApproveBasicData = () => {
    handleApproveBasicData();
  };
  const onRerevewExtend = () => {
    handleRerevewExtend();
  };
  const correctionExtend = () => {
    handleCorrectionExtend();
  };
  // const onApproveExtend = () => {
  //   handleApproveExtend();
  // };
  const onApprovalSubmitExtend = () => {
    handleApprovalSubmitExtend();
  };

  const commentAction = () => {
    handleSubmitForCorrection();
    setCorrectionPopoup(false);
  };
  const onEdit = () => {
    setIsEditMode(true);
    setIsExtendMode(false);
    setIsDisplayMode(false);
  };

  const handleClosemodalData = (data) => {
    setisChangeLogopen(data);
  };

  const handleCommentClose = () => {
    setCorrectionPopoup(false);
  };
  const onSubmitForApprovalAllViewsCreate = () => {
    handleSubmitForApprovalAllViewsCreate();
  };
  // const handleSaveAsDraft = () => {
  //   const hSuccess = (data) => {
  //     // console.log("Data edited successfully");
  //     setMessageDialogMessage(
  //       `Change id generated for Data Owners CMS${data.body}`
  //     );
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(
  //     `/${destination_MaterialMgmt}/alter/editViewSaveAsDraft`,
  //     "post",
  //     hSuccess,
  //     hError,
  //     payload
  //   );
  // };

  const correctionViewSelect = () => {
    handleCorrectionViewSelect();
  };
  const rereviewViewSelect = () => {
    handleRereviewViewSelect();
  };
  const correctionViewForCreate = () => {
    handleCorrectionViewForCreate();
  };
  const rereviewViewForCreate = () => {
    handleRereviewViewForCreate();
  };

  const openCorrectionDialog = () => {
    setOpenSelectCorrection(true);
  }
  const correctionViewDeselect = () => { };
  const handleCorrectionDialogClose = () => {
    setOpenSelectCorrection(false);
  };
  const handleSaveAsDraftBasicData = () => {
    setIsToastPromise(true);  
    setIsLoadingToast(true);
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        // setMessageDialogTitle("Create");
        // setMessageDialogMessage(
        //   `Basic Data Saved As Draft With ID NMS${data.body} `
        // );
        // setMessageDialogSeverity("success");
        // setMessageDialogOK(false);
        // setsuccessMsg(true);
        // handleSnackBarOpen();
        // setMessageDialogExtra(true);
        // setIsLoading(false);
        setIsLoadingToast(false);
        setToastType('success');
        setToastMessage(`${data.message} With ID: ${data.body}`);
      } else {
        // setMessageDialogTitle("Error");
        // setsuccessMsg(false);
        // setMessageDialogMessage("Failed Saving Basic Data");
        // setMessageDialogSeverity("danger");
        // setMessageDialogOK(false);
        // setMessageDialogExtra(true);
        // handleMessageDialogClickOpen();
        // setIsLoading(false);
        setIsLoadingToast(false);
        setToastType('error');
        setToastMessage('Error occured');
        console.log(error);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/alter/editBasicDataViewSaveAsDraft`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleSubmitBasicData = () => {
    setIsToastPromise(true);  
    setIsLoadingToast(true);
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        // setMessageDialogTitle("Create");
        // setMessageDialogMessage(
        //   `Basic Data Submitted for Approval with ID NMS${data.body} `
        // );
        // setMessageDialogSeverity("success");
        // setMessageDialogOK(false);
        // setsuccessMsg(true);
        // handleSnackBarOpen();
        // setMessageDialogExtra(true);
        // setIsLoading(false);
        setIsLoadingToast(false);
        setToastType('success');
        setToastMessage(`Basic Data Submitted for Approval with ID NMS${data.body} `);
      } else {
        // setMessageDialogTitle("Error");
        // setsuccessMsg(false);
        // setMessageDialogMessage("Failed Submitting Basic Data");
        // setMessageDialogSeverity("danger");
        // setMessageDialogOK(false);
        // setMessageDialogExtra(true);
        // handleMessageDialogClickOpen();
        setIsLoadingToast(false);
        setToastType('error');
        setToastMessage('Error occured');
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleSubmitForApprovalAllViewsCreate = () => {
    setIsToastPromise(true);  
    setIsLoadingToast(true);
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        // setMessageDialogTitle("Create");
        // setMessageDialogMessage(
        //   `Material Submitted for Approval with ID NMS${data.body} `
        // );
        // setMessageDialogSeverity("success");
        // setMessageDialogOK(false);
        // setsuccessMsg(true);
        // handleSnackBarOpen();
        // setMessageDialogExtra(true);
        // setIsLoading(false);
        setIsLoadingToast(false);
        setToastType('success');
        setToastMessage(`Material Submitted for Approval with ID NMS${data.body} `);
      } else {
        // setMessageDialogTitle("Error");
        // setsuccessMsg(false);
        // setMessageDialogMessage("Failed Submitting Material");
        // setMessageDialogSeverity("danger");
        // setMessageDialogOK(false);
        // setMessageDialogExtra(true);
        // handleMessageDialogClickOpen();
        setIsLoadingToast(false);
        setToastType('error');
        setToastMessage(`Material Submitted for Approval with ID NMS${data.body} `);
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/alter/allViewsApprovalSubmit`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  // const handleApproveBasicDataCreate = () => {
  //   const hSuccess = (data) => {
  //     setIsLoading();
  //     if (data.statusCode === 200) {
  //       console.log("success");
  //       setMessageDialogTitle("Create");
  //       setMessageDialogMessage(`${data.message} With ID: ${data.body} `);
  //       setMessageDialogSeverity("success");
  //       setMessageDialogOK(false);
  //       setsuccessMsg(true);
  //       handleSnackBarOpen();
  //       setMessageDialogExtra(true);
  //       setIsLoading(false);
  //     } else {
  //       setMessageDialogTitle("Error");
  //       setsuccessMsg(false);
  //       setMessageDialogMessage("Failed SAP Syndication");
  //       setMessageDialogSeverity("danger");
  //       setMessageDialogOK(false);
  //       setMessageDialogExtra(true);
  //       handleMessageDialogClickOpen();
  //       setIsLoading(false);
  //     }
  //     handleClose();
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(
  //     `/${destination_MaterialMgmt}/alter/createBasicDataApproved`,
  //     "post",
  //     hSuccess,
  //     hError,
  //     payload
  //   );
  // };

  // slightly good
  // const handleApproveBasicDataCreate = () => {
  //   const hSuccess = (data) => {
  //     setIsLoading();
  //     if (data.statusCode === 200) {
  //       console.log("success");
  //       toast.success(`${data.message} With ID: ${data.body}`, {
  //         position: "top-center",
  //         autoClose: 5000,
  //         hideProgressBar: false,
  //         closeOnClick: true,
  //         pauseOnHover: true,
  //         draggable: true,
  //         progress: undefined,
  //         theme: "light",
  //         transition: Bounce,
  //       });
  //       setIsLoading(false);
  //     } else {
  //       toast.error("Failed SAP Syndication", {
  //         position: "top-center",
  //         autoClose: 5000,
  //         hideProgressBar: false,
  //         closeOnClick: true,
  //         pauseOnHover: true,
  //         draggable: true,
  //         progress: undefined,
  //         theme: "light",
  //         transition: Bounce,
  //       });
  //       setIsLoading(false);
  //     }
  //     handleClose();
  //   };
  
  //   const hError = (error) => {
  //     console.log(error);
  //     toast.error("An error occurred", {
  //       position: "top-center",
  //       autoClose: 5000,
  //       hideProgressBar: false,
  //       closeOnClick: true,
  //       pauseOnHover: true,
  //       draggable: true,
  //       progress: undefined,
  //       theme: "light",
  //       transition: Bounce,
  //     });
  //   };
  
  //   doAjax(
  //     `/${destination_MaterialMgmt}/alter/createBasicDataApproved`,
  //     "post",
  //     hSuccess,
  //     hError,
  //     payload
  //   );
  // };

  // best
  // const handleApproveBasicDataCreate = () => {
  //   const loadingToastId = toast.loading('Please wait...');
  
  //   const hSuccess = (data) => {
  //     if(data.statusCode === 200){
  //       toast.update(loadingToastId, {
  //         render: `${data.message} With ID: ${data.body}`,
  //         type: 'success',
  //         isLoading: false,
  //         autoClose: 5000,
  //       });
  //     } else {
  //       toast.update(loadingToastId, {
  //         render: 'Failed SAP Syndication',
  //         type: 'error',
  //         isLoading: false,
  //         autoClose: 5000,
  //       });
  //     }
      
  //   };
  
  //   const hError = (error) => {
  //     toast.update(loadingToastId, {
  //       render: 'An error occurred',
  //       type: 'error',
  //       isLoading: false,
  //       autoClose: 5000,
  //     });
  //     console.log(error);
  //   };
  
  //   doAjax(
  //     `/${destination_MaterialMgmt}/alter/createBasicDataApproved`,
  //     'post',
  //     hSuccess,
  //     hError,
  //     payload
  //   );
  // };

  const handleApproveBasicDataCreate = () => {
    // const loadingToastId = toast.loading('Please wait...');
    setIsToastPromise(true);  
    setIsLoadingToast(true);
    const hSuccess = (data) => {
      if(data.statusCode === 201){
        setIsLoadingToast(false);
        setToastType('success');
        setToastMessage(`${data.message} With ID: ${data.body}`);
      } else {
        setIsLoadingToast(false);
        setToastType('error');
        setToastMessage('Failed SAP Syndication');
      }
      // setIsToastPromise(false);
    };
  
    const hError = (error) => {
      setIsLoadingToast(false);
      setToastType('error');
      setToastMessage('Error occured');
      console.log(error);
      // setIsToastPromise(false);
    };
  
    doAjax(
      `/${destination_MaterialMgmt}/alter/createBasicDataApproved`,
      'post',
      hSuccess,
      hError,
      payload
    );
  };

  const handleApproveAllViewsCreate = () => {
    setIsToastPromise(true);  
    setIsLoadingToast(true);
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200||data.statusCode === 201) {
        console.log("success");
        // setMessageDialogTitle("Create");
        // setMessageDialogMessage(`${data.message} With ID: ${data.body} `);
        // setMessageDialogSeverity("success");
        // setMessageDialogOK(false);
        // setsuccessMsg(true);
        // handleSnackBarOpen();
        // setMessageDialogExtra(true);
        // setIsLoading(false);
        setIsLoadingToast(false);
        setToastType('success');
        setToastMessage(`${data.message} With ID: ${data.body}`);
      } else {
        // setMessageDialogTitle("Error");
        // setsuccessMsg(false);
        // setMessageDialogMessage("Failed SAP Syndication");
        // setMessageDialogSeverity("danger");
        // setMessageDialogOK(false);
        // setMessageDialogExtra(true);
        // handleMessageDialogClickOpen();
        // setIsLoading(false);
        setIsLoadingToast(false);
        setToastType('error');
        setToastMessage('Failed SAP Syndication');
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/alter/allViewsApproved`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleSubmitForReviewSalesCreate = () => {
    setIsToastPromise(true);  
    setIsLoadingToast(true);
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200||data.statusCode===201) {
        console.log("success");
        // setMessageDialogTitle("Create");
        // setMessageDialogMessage(
        //   `Sales Data Submitted for Review with ID NMS${data.body} `
        // );
        // setMessageDialogSeverity("success");
        // setMessageDialogOK(false);
        // setsuccessMsg(true);
        // handleSnackBarOpen();
        // setMessageDialogExtra(true);
        // setIsLoading(false);
        setIsLoadingToast(false);
        setToastType('success');
        setToastMessage(`Sales Data Submitted for Review with ID NMS${data.body} `);
      } else {
        // setMessageDialogTitle("Error");
        // setsuccessMsg(false);
        // setMessageDialogMessage("Failed Submitting Sales Data");
        // setMessageDialogSeverity("danger");
        // setMessageDialogOK(false);
        // setMessageDialogExtra(true);
        // handleMessageDialogClickOpen();
        // setIsLoading(false);
        setIsLoadingToast(false);
        setToastType('error');
        setToastMessage('Failed Submitting Sales Data');
        console.log(error);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/alter/createSalesAndSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleSubmitForReviewPurchasingCreate = () => {
    setIsToastPromise(true);  
    setIsLoadingToast(true);
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        // setMessageDialogTitle("Create");
        // setMessageDialogMessage(
        //   `Purchasing Data Submitted for Review with ID NMS${data.body} `
        // );
        // setMessageDialogSeverity("success");
        // setMessageDialogOK(false);
        // setsuccessMsg(true);
        // handleSnackBarOpen();
        // setMessageDialogExtra(true);
        // setIsLoading(false);
        setIsLoadingToast(false);
        setToastType('success');
        setToastMessage(`Purchasing Data Submitted for Review with ID NMS${data.body} `);
      } else {
        // setMessageDialogTitle("Error");
        // setsuccessMsg(false);
        // setMessageDialogMessage("Failed Submitting Purchasing Data");
        // setMessageDialogSeverity("danger");
        // setMessageDialogOK(false);
        // setMessageDialogExtra(true);
        // handleMessageDialogClickOpen();
        // setIsLoading(false);
        setIsLoadingToast(false);
        setToastType('error');
        setToastMessage('Failed Submitting Purchasing Data');
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/alter/createPurchaseAndSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleSubmitForReviewAccountingCreate = () => {
    const hSuccess = (data) => {
      setIsLoading();
      setIsToastPromise(true);  
      setIsLoadingToast(true);
      if (data.statusCode === 200) {
        console.log("success");
        // setMessageDialogTitle("Create");
        // setMessageDialogMessage(
        //   `Accounting Data Submitted for Review with ID NMS${data.body} `
        // );
        // setMessageDialogSeverity("success");
        // setMessageDialogOK(false);
        // setsuccessMsg(true);
        // handleSnackBarOpen();
        // setMessageDialogExtra(true);
        // setIsLoading(false);
        setIsLoadingToast(false);
        setToastType('success');
        setToastMessage(`Accounting Data Submitted for Review with ID NMS${data.body} `);
      } else {
        // setMessageDialogTitle("Error");
        // setsuccessMsg(false);
        // setMessageDialogMessage("Failed Submitting Accounting Data");
        // setMessageDialogSeverity("danger");
        // setMessageDialogOK(false);
        // setMessageDialogExtra(true);
        // handleMessageDialogClickOpen();
        // setIsLoading(false);
        setIsLoadingToast(false);
        setToastType('error');
        setToastMessage('Failed Submitting Accounting Data');
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/alter/createAccountingAndSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleSubmitForApprovalBasicCreate = () => {
    setIsToastPromise(true);  
    setIsLoadingToast(true);
    const hSuccess = (data) => {
      // console.log("Data edited successfully");
      // setMessageDialogMessage(
      //   // `Change id generated for Data Owners CMS${data.body}`
      //   `Basic Data submitted for approval`
      // );
      setIsLoadingToast(false);
      setToastType('success');
      setToastMessage(`Basic Data submitted for approval`);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/alter/createBasicDataApprovalSubmit`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const handleSubmitForReviewSales = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Sales Data Submitted for Review with ID CMS${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Sales Data");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/alter/editSalesViewAndSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleSubmitForReviewPurchasing = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Purchasing Data Submitted for Review with ID CMS${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Purchasing Data");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/alter/editPurchaseViewAndSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleSaveAsDraftSalesExtend = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Sales Data Saved As Draft With ID EMS${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Saving Sales Data");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/alter/extendSalesViewSaveAsDraft`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleSaveAsDraftPurchasingExtend = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Purchasing Data Submitted for Review with ID EMS${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Saving Purchasing Data");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/alter/extendPurchaseViewSaveAsDraft`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleSaveAsDraftAccountingExtend = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Accounting Data Saved with ID CMS${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Accounting Data");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/alter/extendAccountingViewSaveAsDraft`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleSubmitForReviewSalesExtend = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Sales Data Submitted for Review with ID EMS${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Sales Data");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/alter/extendSalesViewAndSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleSubmitForReviewPurchasingExtend = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Prchasing Data Submitted for Review with ID EMS${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Purchasing Data");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/alter/extendPurchaseViewAndSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleSubmitForReviewAccountingExtend = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Accounting Data Submitted for Review with ID EMS${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Accounting Data");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/alter/extendAccountingViewAndSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleSubmitForApprovalExtend = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Material Submitted for Approval with ID EMS${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Accounting Data");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/alter/extendViewApprovalSubmit`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  // const handleApproveExtend = () => {
  //   const hSuccess = (data) => {
  //     setIsLoading();
  //     if (data.statusCode === 200) {
  //       console.log("success");
  //       setMessageDialogTitle("Create");
  //       setMessageDialogMessage(
  //         `Material Submitted for Approval with ID EMS${data.body} `
  //       );
  //       setMessageDialogSeverity("success");
  //       setMessageDialogOK(false);
  //       setsuccessMsg(true);
  //       handleSnackBarOpen();
  //       setMessageDialogExtra(true);
  //       setIsLoading(false);
  //     } else {
  //       setMessageDialogTitle("Error");
  //       setsuccessMsg(false);
  //       setMessageDialogMessage("Failed Submitting Material Data");
  //       setMessageDialogSeverity("danger");
  //       setMessageDialogOK(false);
  //       setMessageDialogExtra(true);
  //       handleMessageDialogClickOpen();
  //       setIsLoading(false);
  //     }
  //     handleClose();
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(
  //     `/${destination_MaterialMgmt}/alter/extendViewApproved`,
  //     "post",
  //     hSuccess,
  //     hError,
  //     payload
  //   );
  // };
  const handleSubmitForReviewAccounting = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Accounting Data Submitted for Review with ID NMS${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Accounting Data");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/alter/editAccountingViewAndSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const handleSubmitForApprovalBasicData = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Basic Data Submitted for Approval with ID CMS${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Basic Data");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const handleSaveAsDraftSales = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Sales Data Saved As Draft With ID NMS${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Saving Sales Data");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/alter/editSalesViewSaveAsDraft`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleSaveAsDraftPurchasing = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Purchasing Data Saved As Draft With ID NMS${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Purchasing Data");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/alter/editPurchaseViewSaveAsDraft`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleSaveAsDraftAccounting = () => {
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Accounting Data Saved As Draft With ID NMS${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Saving Accounting Data");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/alter/editAccountingViewSaveAsDraft`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleForwardSelection = () => {
    const hSuccess = (data) => {
      // console.log("Data edited successfully");
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/alter/editViewSaveAsDraft`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleSubmitForApproval = () => {
    console.log("apicall");
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Material Submitted for Approval with ID CMS${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Material");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/alter/editViewApprovalSubmit`,
      // `/${destination_MaterialMgmt}/alter/editViewApproved`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleSubmitExtendForApproval = () => {
    console.log("apicall");
    const hSuccess = (data) => {
      setMessageDialogMessage(
        `Create id generated for Data Owners EMS${data.body}`
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/alter/extendViewApprovalSubmit`,
      // `/${destination_MaterialMgmt}/alter/editViewApproved`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleSubmitForApprove = () => {
    console.log("apicallllllllll");
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `${data.message} with Material No: ${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed SAP Syndication");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_MaterialMgmt}/alter/editViewApproved`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleApproveBasicData = () => {
    console.log("apicallllllllll");
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 201) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          ` ${data.message} with Material No: ${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed SAP Syndication");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_MaterialMgmt}/alter/editBasicDataApproved`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  // const handleOnApproveView = () => {
  //   console.log("apicallllllllll");
  //   const hSuccess = (data) => {
  //     setMessageDialogMessage(
  //       `Create id generated for Data Owners CMS${data.body}`
  //     );
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(
  //     // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
  //     `/${destination_MaterialMgmt}/alter/editViewApproved`,
  //     "post",
  //     hSuccess,
  //     hError,
  //     payload
  //   );
  // };
  const handleCorrectionExtend = () => {
    console.log("apicallllllllll");
    const hSuccess = (data) => {
      setMessageDialogMessage(
        `Create id generated for Data Owners CMS${data.body}`
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_MaterialMgmt}/alter/sendSelectedViewsForExtendCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleApproveExtend = () => {
    console.log("apicallllllllll");
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `${data.message} With Material No: ${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed SAP Syndication");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_MaterialMgmt}/alter/extendViewApproved`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleRerevewExtend = () => {
    console.log("apicallllllllll");
    const hSuccess = (data) => {
      setMessageDialogMessage(
        `Create id generated for Data Owners CMS${data.body}`
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_MaterialMgmt}/alter/extendSelectedViewsSendForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCorrectionViewSelect = () => {
    const hSuccess = (data) => {
      // console.log("View Sent For Correction")
      setMessageDialogMessage(`View Sent For Correction`);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_MaterialMgmt}/alter/sendForEditCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleRereviewViewSelect = () => {
    const hSuccess = (data) => {
      // console.log("View Sent For Correction")
      setMessageDialogMessage(`View Sent For Correction`);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_MaterialMgmt}/alter/editViewSendForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCorrectionViewForCreate = () => {
    const hSuccess = (data) => {
      // console.log("View Sent For Correction")
      setMessageDialogMessage(`View Sent For Correction`);
      setOpenSelectCorrection(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_MaterialMgmt}/alter/createSelectedViewsSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleRereviewViewForCreate = () => {
    const hSuccess = (data) => {
      // console.log("View Sent For Correction")
      setMessageDialogMessage(`View Sent For Correction`);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_MaterialMgmt}/alter/createSelectedViewsSendForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleApprovalSubmitExtend = () => {
    console.log("apicallllllllll");
    const hSuccess = (data) => {
      setIsLoading();
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Material Submitted for Approval with ID EMS${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Material");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_MaterialMgmt}/alter/extendViewApprovalSubmit`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  console.log("datatosend", dataToSend);
  const handleTaskCreateForExtend = () => {
    const hSuccess = (data) => {
      console.log("snackbarr")
      setopenSnackbar(true);
      setMessageDialogMessage(
        `Create id generated for Data Owners EMS${data?.body}`
      ); 
    };
    const hError = (error) => {
      console.log(error);
    };
    console.log("resultpayload", payload);
    doAjax(
      `/${destination_MaterialMgmt}/alter/extendOrgElements`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const handleSubmitForReviewCreation = () => {
    if (materialDetails[activeTab]?.category === "Sales") {
      var url = `/${destination_MaterialMgmt}/alter/createSalesAndSubmitForReview`;
    } else if (materialDetails[activeTab]?.category === "Purchasing") {
      var url = `/${destination_MaterialMgmt}/alter/createPurchaseAndSubmitForReview`;
    } else if (materialDetails[activeTab]?.category === "Accounting") {
      var url = `/${destination_MaterialMgmt}/alter/createAccountingAndSubmitForReview`;
    }

    const hSuccess = (data) => {
      console.log("Data submitted for review successfully");
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(url, "post", hSuccess, hError, payload);
  };
  const handleSubmitForReviewExtend = () => {
    const hSuccess = (data) => {
      console.log("Data submitted for review successfully");
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/alter/extendViewAndSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleSubmitForCorrection = () => {
    const hSuccess = (data) => {
      console.log("Data submitted for review successfully");
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/alter/sendForEditCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleSnackBarClose = () => {
    setopenSnackbar(false);
    navigate("/masterDataCockpit/materialMaster/materialSingle");
  };
  const handleSnackBarOpen = () => {
    setopenSnackbar(true);
  };
  useEffect(() => {
    getMaterialDetails();
    setMmNumber(idGenerator("MM"));
  }, []);
  const getMaterialDetails = () => {
    // setIsLoading(true);
    let payload = taskData?.body?.requestId
      ? {
        id: taskData?.body?.id ? taskData?.body?.id : "",
        materialNo: taskData?.body?.materialNo
          ? taskData?.body?.materialNo
          : "",
        requestId: taskData?.body?.requestId,
        reqStatus: taskData?.body?.reqStatus,
        plantOrg: taskData?.body?.plantOrg ? taskData?.body?.plantOrg : "",
        salesOrg: taskData?.body?.salesOrg ? taskData?.body?.salesOrg : "",
        distChnl: taskData?.body?.distChnl ? taskData?.body?.distChnl : "",
        dtName: "MDG_MAT_FIELD_CONFIG",
        version: "v2",
        screenName:
          taskRowDetails?.requestType === "Create" ? "Create" : "Change",
      }
      : materialData?.requestId
        ? {
          id: materialData?.id,
          materialNo: "",
          requestId: materialData?.requestId,
          reqStatus: materialData?.reqStatus,
          // plantOrg: materialData?.plantOrg
          //   ? materialData?.plantOrg?.slice(0, 4)
          //   : "",
          plantOrg: materialData?.plant ? materialData?.plant : "",
          salesOrg: materialData?.salesOrg ? materialData?.salesOrg : "",
          distChnl: materialData?.distChnl ? materialData?.distChnl : "",
          dtName: "MDG_MAT_FIELD_CONFIG",
          version: "v2",
          screenName:
            materialData?.requestType === "Create"
              ? "Create"
              : materialData?.requestType === "Mass Create"
                ? "Create"
                : "Change",
        }
        : {
          id: "",
          materialNo: materialData?.materialNumber,
          requestId: "",
          reqStatus: "Approved",
          distChnl:
            materialData?.distributionChannel === "Not Available"
              ? ""
              : materialData?.distributionChannel,
          plantOrg:
            materialData?.plant === "Not Available"
              ? ""
              : materialData?.plant?.slice(0, 4),
          salesOrg:
            materialData?.salesOrg === "Not Available"
              ? ""
              : materialData?.salesOrg?.slice(0, 4),
              dtName: "MDG_MAT_FIELD_CONFIG",
              version: "v2",
          screenName: "Change",
        };
    const hSuccess = (data) => {
      console.log("dttttt", data);
      const responseBody = data.body.viewData;
      console.log('responseBody', responseBody)
      setHeaderData(data.body.headerData);
      setAdditionalDisplayData(data.body.additionalData);
      setIDs(data.body.IDs);
      console.log("dataview", data.body.viewData);
      const displayAdditionalData = data.body.additionalData;
      const categoryKeys = Object.keys(responseBody);
      // console.log("categorry", data.body);
      console.log("categoryKeys", categoryKeys);
      const viewsSele = data.body.viewNames;
      // debugger
      console.log("viewsSele", viewsSele);
      const updatedViews = ["General Information", ...viewsSele, "Attachments & Comments"];
      setViewNameArray(viewsSele);
      console.log("updatedViews", updatedViews);
      // setFactorsArray(updatedViews);
      const mappedData = categoryKeys.map((category) => ({
        category,
        data: responseBody[category],
      }));
      console.log("mappedData",mappedData);

      let filteredData = [{}];
      if (taskData?.body?.requestId || materialData?.requestId) {
        filteredData = mappedData.filter((item) => updatedViews.includes(item.category));
        setFactorsArray(updatedViews);
      } else {
        filteredData = mappedData.filter((item) => viewsSele.includes(item.category));
        setFactorsArray(viewsSele);
      }
      
      console.log("filteredData", filteredData);
      
      setMaterialDetails(filteredData);
      setAdditionalDataPayload(displayAdditionalData);
      dispatch(setResponseFromAPI(data.body));
      dispatch(newAdditionalData(displayAdditionalData));

      for(const key in data.body["General Information"]) {
        console.log("i m in gi");
      }

      for (const key in data.body["General Information"]) {
        // console.log(
        //   `${key}: ${data.body["General Information"][key]}`,
        //   "keydatapair"
        // );

        if (data.body["General Information"].hasOwnProperty(key)) {
          console.log(
            `${key}: ${data.body["General Information"][key]}`,
            "keydatapair"
          );
        }
      
        dispatch(
          setSingleMaterialPayload({
            keyName: key
              .replaceAll("(", "")
              .replaceAll(")", "")
              .replaceAll("/", "")
              .replaceAll("-", "")
              .replaceAll(".", "")
              .split(" ")
              .join(""),
            data:
              data?.body["General Information"][key] === "false"
                ? "No"
                : data?.body["General Information"][key] === "true"
                ? "Yes"
                : data.body["General Information"][key],
          })
        );
      }
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/displayMaterialDTO`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const getGITemplate = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_GI_MATERIAL_QUESTIONS",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          // "MDG_CONDITIONS.MDG_GI_MODULE": "CC ET",
          "MDG_CONDITIONS.MDG_GI_SCENARIO": "Create",
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    setIsLoading(true);
    // const formData = new FormData();

    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        const questionsData =
          data?.data?.result[0]?.MDG_GI_QUESTIONS_ACTION_TYPE || [];
          questionsData.map((question) => {
            if (question?.MDG_GI_INPUT_OPTION === 'Radio Button') {
              if (question?.MDG_GI_VISIBILITY === " Mandatory") {
  
                console.log("insidevisibilitytest");
                dispatch(setMatRequiredFieldsGI(question.MDG_GI_QUESTION_TYPE
                  .replaceAll("(", "")
                  .replaceAll(")", "")
                  .replaceAll("/", "")
                  .replaceAll("-", "")
                  .replaceAll(".", "")
                  .split(" ")
                  .join(""),))
              }
              if (question?.MDG_GI_QUESTION_TYPE !== "Choose Priority Level") {
                dispatch(
                  setSingleMaterialPayload({
                    keyName: question.MDG_GI_QUESTION_TYPE
                      .replaceAll("(", "")
                      .replaceAll(")", "")
                      .replaceAll("/", "")
                      .replaceAll("-", "")
                      .replaceAll(".", "")
                      .split(" ")
                      .join(""),
                    data: "No",
                  })
                )
              } else {
                dispatch(
                  setSingleMaterialPayload({
                    keyName: question.MDG_GI_QUESTION_TYPE
                      .replaceAll("(", "")
                      .replaceAll(")", "")
                      .replaceAll("/", "")
                      .replaceAll("-", "")
                      .replaceAll(".", "")
                      .split(" ")
                      .join(""),
                    data: "Medium",
                  })
                )
              }
            } else {
              if (question?.MDG_GI_VISIBILITY === " Mandatory") {
                console.log("insidevisibility");
                dispatch(setMatRequiredFieldsGI(question.MDG_GI_QUESTION_TYPE
                  .replaceAll("(", "")
                  .replaceAll(")", "")
                  .replaceAll("/", "")
                  .replaceAll("-", "")
                  .replaceAll(".", "")
                  .split(" ")
                  .join(""),));
              }
              dispatch(
                setSingleMaterialPayload({
                  keyName: question.MDG_GI_QUESTION_TYPE
                    .replaceAll("(", "")
                    .replaceAll(")", "")
                    .replaceAll("/", "")
                    .replaceAll("-", "")
                    .replaceAll(".", "")
                    .split(" ")
                    .join(""),
                  data: singleGIPayloadMain[question.MDG_GI_QUESTION_TYPE
                    .replaceAll("(", "")
                    .replaceAll(")", "")
                    .replaceAll("/", "")
                    .replaceAll("-", "")
                    .replaceAll(".", "")
                    .split(" ")
                    .join("")] ?? "" ,
                }))
            }
  
  
          })
  
          dispatch(setGeneralInformation(questionsData));
      }
    };

    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(
        `/${destination_IDM}/rest/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      doAjax(
        `/${destination_IDM}/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };

  const getAttachmentsIDM = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_ATTACHMENTS_LIST_DT",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE": "Material",
          "MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO": "Create",
          "MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE": 1,
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    setIsLoading(true);
    const hSuccess = (data) => {
      console.log("dtt1", data);
      setIsLoading(false);
      if (data.statusCode === 200) {
        let responseData = data?.data?.result[0]?.MDG_ATTACHMENTS_ACTION_TYPE;
        console.log("dtt2", responseData);
        let templateData = [];
        responseData.map((element, index) => {
          console.log("dtt3", element);

          var tempRow = {
            id: index,
          };
          templateData.push(tempRow);
        });
        setRuleData(templateData);
        const attachmentNames =
          data?.data?.result[0]?.MDG_ATTACHMENTS_ACTION_TYPE || [];
        console.log("dtt4", attachmentNames);
        setAttachmentsData(attachmentNames);
      } else {
        // setMessageDialogTitle("Create");
        // setsuccessMsg(false);
        // setMessageDialogMessage("Creation Failed");
        // setHandleExtrabutton(false);
        // setMessageDialogSeverity("danger");
        // setMessageDialogOK(false);
        // setMessageDialogExtra(true);
        // handleMessageDialogClickOpen();
        // handleCreateDialogClose();
        // setIsLoading(false);
      }
      handleClose();
    };

    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(
        `/${destination_IDM}/rest/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      doAjax(
        `/${destination_IDM}/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  }
  console.log('materialdetails', materialDetails)

  let factorsArrayWithoutBasicData = viewNameArray.slice(1);
  console.log(factorsArrayWithoutBasicData, "hemlo");
  const fieldListAccordingToView = [
    { label: "Basic Data", value: "basicData", fields: [], code: "K" },
    { label: "Classification", value: "classification", fields: [], code: "C" },
    {
      label: "Sales",
      value: "sales",
      fields: ["Plant", "Sales Organization", "Distribution Channel"],
      code: "V",
    },
    { label: "Purchasing", value: "purchasing", fields: ["Plant"], code: "E" },
    // {
    //   label: "MRP",
    //   value: "mrp",
    //   fields: ["Plant", "Storage Location", "MRP Profile"],
    //   code: "D",
    // },
    // { label: "Plant Stock", value: "plantStock", fields: ["Plant"], code: "X" },
    // {
    //   label: "Storage Location Stocks",
    //   value: "storageLocationStocks",
    //   fields: ["Plant", "Storage Location"],
    //   code: "Z",
    // },
    // { label: "Storage", value: "storage", fields: ["Storage Location"], code: "L" },
    // {
    //   label: "Quality Management",
    //   value: "qualityManagement",
    //   fields: ["Plant"],
    //   code: "Q",
    // },
    { label: "Accounting", value: "accounting", fields: ["Plant"], code: "B" },
    // { label: "Costing", value: "costing", fields: ["Plant"], code: "G" },
    // {
    //   label: "Forecasting",
    //   value: "forecasting",
    //   fields: ["Plant", "Forecast Profile"],
    //   code: "P",
    // },
    // {
    //   label: "Production Resources/Tools",
    //   value: "productionResources/tools",
    //   fields: ["Plant"],
    //   code: "F",
    // },
    // {
    //   label: "Warehouse Management",
    //   value: "warehouseManagement",
    //   fields: ["Plant", "Warehouse No", "Storage Type"],
    //   code: "S",
    // },
    // {
    //   label: "Work Scheduling",
    //   value: "workScheduling",
    //   fields: ["Plant"],
    //   code: "A",
    // },
  ];
  // const getOrgElements = () => {
  //   const hSuccess = (data) => {
  //     setOrgElementCode(data.body[0].MaintStatus.split(""));
  //     getAutocompleteOptions(orgElementCode);
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(
  //     `/${destination_MaterialMgmt}/data/getMaintStatusBasedOnMaterialNo?materialNo=${reqId}`,
  //     "get",
  //     hSuccess,
  //     hError
  //   );
  // };
  const getOrgLookupData = (uniqueFieldsItems) => {
    console.log("displayedfieldsss", uniqueFieldsItems);
    if (uniqueFieldsItems.includes("Plant")) {
      const hSuccess = (data) => {
        setPlantLookupData(data.body);
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        `/${destination_MaterialMgmt}/data/getPlant`,
        "get",
        hSuccess,
        hError
      );
    }
    if (uniqueFieldsItems.includes("Sales Organization")) {
      const hSuccess = (data) => {
        setSalesOrgLookupData(data.body);
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        `/${destination_MaterialMgmt}/data/getSalesOrg`,
        "get",
        hSuccess,
        hError
      );
    }

    if (uniqueFieldsItems.includes("MRP Profile")) {
      const hSuccess = (data) => {
        // setMrpProfileLookupData(data.body);
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        // `/${destination_MaterialMgmt}/data/getStorageLocationForPlant?plant=${plantLookupData[0].Salesorg}`,
        `/${destination_MaterialMgmt}/data/getPlant`,
        "get",
        hSuccess,
        hError
      );
    }
    // if (uniqueFieldsItems.includes("Forecast Profile")) {
    //   const hSuccess = (data) => {
    //     // setForecastProfileLookupData(data.body);
    //   };
    //   const hError = (error) => {
    //     console.log(error);
    //   };
    //   doAjax(
    //     // `/${destination_MaterialMgmt}/data/getStorageLocationForPlant?plant=${plantLookupData[0].Salesorg}`,
    //     `/${destination_MaterialMgmt}/data/getPlant`,
    //     "get",
    //     hSuccess,
    //     hError
    //   );
    // }
  };
  const getDataForField = (fieldName) => {
    switch (fieldName) {
      case "Plant":
        return plantLookupData.map((x) => x);
      case "Sales Organization":
        return salesOrgLookupData.map((y) => y);
      case "Distribution Channel":
        return distributionChannelLookupData.map((z) => z);
      // case "Storage Location":
      //   return storageLocationLookupData.map((w) => w);
      case "MRP Profile":
        return plantLookupData.map((p) => p);
      // case "Forecast Profile":
      //   return plantLookupData.map((f) => f);
      case "Warehouse No":
        return warehouseNoLookupData.map((n) => n);
      // case "Storage Type":
      //   return storageTypeLookupData.map((s) => s);
      default:
        return [];
    }
  };
  const handleClose = () => {
    setOpen(false);
  };
  const handleselectedViewsDialogClose = () => {
    setIsDisplayMode(true);
    setIsEditMode(false);
    setIsExtendMode(false);
    setOpenSelectViews(false);
  };
  const displayAdditionalData = () => {
    navigate(
      `/masterDataCockpit/materialMaster/displayMaterialDetail/displayAdditionalData`,
      {
        state: additionalDataPayload,
      }
    );
  };

  const allTabs = useSelector((state) => state.tabsData);
  console.log('alltabs',allTabs,);

  const reference = {
    basicData: "Basic Data",
    salesData: "Sales",
    accountingData: "Accounting",
    purchasingData: "Purchasing",
  };
  console.log(materialDetails, "hye");

  // Object.entries(allTabs).map(item => {
  //   if(factorsArray.findIndex(inneritem=> inneritem?.split(" ")[0]==reference[item[0]]?.split(" ")[0])!=-1){
  //     const mindex= materialDetails.findIndex(i=> i.category?.split(" ")[0] == reference[item[0]]?.split(" ")[0]);
  //     if(mindex!= -1){
  //       return {category:reference[item[0]]?.split(" ")[0],data:materialDetails[mindex].data}
  //     }else{
  //       return {category:reference[item[0]]?.split(" ")[0],data:item[1]}
  //     }

  //   }
  //   return null
  // }).filter(item=> item!=null)

  const columns = [
    {
      field: "id",
      headerName: "ID",
      type: "text",
      hide: "true",
      flex: 1,
    },
    {
      field: "TaxCategory",
      headerName: "Tax Category",

      flex: 1,
    },
    {
      field: "TaxCategoryName",
      headerName: "Tax Category Name",
      type: "text",
      flex: 1,
    },
    {
      field: "TaxClassification",
      headerName: "Tax Classification",
      type: "singleSelect",

      flex: 1,
    },
    {
      field: "TaxClassificationDescription",
      headerName: "Tax Classification Description",
      type: "text",
      flex: 1,
    },
  ];

  const attachmentColumns = [
    {
      field: "id",
      headerName: "Document ID",
      flex: 1,
      hide: true,
    },
    {
      field: "docType",
      headerName: "Type",
      flex: 1,
    },
    {
      field: "docName",
      headerName: "Document Name",
      flex: 1,
    },
    {
      field: "uploadedOn",
      headerName: "Uploaded On",
      flex: 1,
      align: "center",
      headerAlign: "center",
    },
    {
      field: "uploadedBy",
      headerName: "Uploaded By",
      sortable: false,
      flex: 1,
    },
    {
      field: "action",
      headerName: "Action",
      sortable: false,
      filterable: false,
      align: "center",
      headerAlign: "center",
      flex: 1,
      renderCell: (cellValues) => {
        return (
          <>
          {console.log("celll", cellValues)}
            <MatView index={cellValues.row.id} name={cellValues.row.docName} />
            <MatDownload
              index={cellValues.row.id}
              name={cellValues.row.docName}
            />
          </>
        );
      },
    },
  ];

  // console.log("reqId", materialData.requestId);

  const getAttachments = () => {
    console.log('taskRowDetails?.requestId', taskRowDetails?.requestId, materialData?.requestId)
    let requestId = taskRowDetails?.requestId ? taskRowDetails?.requestId : materialData?.requestId;
    console.log("requestIddd", requestId);
    let hSuccess = (data) => {
      console.log("attt1", data)
      var attachmentRows = [];
      data.documentDetailDtoList.forEach((doc) => {
        console.log('document', doc)
        // debugger
        var tempRow = {
          id: doc.documentId,
          docType: doc.attachmentType,
          docName: doc.fileName,
          uploadedOn: moment(doc.docCreationDate).format(appSettings.date),
          uploadedBy: doc.createdBy,
        };
        attachmentRows.push(tempRow);
      });
      console.log('attachmentRows', attachmentRows)
      setAttachments(attachmentRows);
    };
    // invoiceHeaderData?.extInvNum &&
    doAjax(
      `/${destination_DocumentManagement}/documentManagement/getDocByRequestId/${requestId}`,
      "get",
      hSuccess
    );
  };

  const getComments = () => {
    let requestId = taskRowDetails?.requestId
      ? taskRowDetails?.requestId
      : materialData?.requestId;
    console.log("cmrequestId", requestId);
    let hSuccess = (data) => {
      console.log("commentsdata", data);

      var commentRows = [];
      data.body.forEach((cmt) => {
        var tempRow = {
          id: cmt.requestId,
          comment: cmt.comment,
          user: cmt.createdByUser,
          createdAt: cmt.updatedAt,
        };
        commentRows.push(tempRow);
      });
      setComments(commentRows);
      console.log("commentrows", commentRows);
    };

    let hError = (error) => {
      console.log(error);
    };
    // invoiceHeaderData?.extInvNum &&
    doAjax(
      `/${destination_MaterialMgmt}/activitylog/fetchTaskDetailsForRequestId?requestId=${requestId}`,
      "get",
      hSuccess,
      hError
    );
  };

  const [rows, setrows] = useState([
    {
      id: 1,
      TaxCategory: "1",
      TaxCategoryName: "",
      TaxClassification: "",
      TaxClassificationDescription: "",
    },
  ]);

  useEffect(() => {
    getAttachments();
    getGITemplate();
    getComments();
  },[]);

  useEffect(() => {
    getAttachmentsIDM();
  },[]);

  const tabContents = factorsArray
    .map((item) => {
      console.log("item", item);
      const ddata = Object.entries(allTabs).filter((i) => {
        return reference[i[0]]?.split(" ")[0] == item?.split(" ")[0];
      })[0]?.[1];
      console.log("addata", ddata);

      const mdata = materialDetails.filter(
        (ii) => ii.category?.split(" ")[0] == item?.split(" ")[0]
      );
      console.log("mdata", mdata);
      if (mdata.length != 0) {
        return { category: item?.split(" ")[0], data: mdata[0].data };
      }
      return { category: item?.split(" ")[0], data: ddata };
    })
    .map((categoryData, index) => {
      console.log("categoryData", categoryData);

      const sortedCategories = Object.entries(categoryData?.data??{})
        .map(([key, fields]) => ({
          key,
          fields: fields.slice().sort((a, b) => a.sequenceNo - b.sequenceNo), // Create a new array to sort to avoid mutation
          cardSeq: fields[0].cardSeq,
        }))
        .sort((a, b) => a.cardSeq - b.cardSeq);

      if (categoryData?.category === "General") {
        return [
          <>
            {userData?.role === "Finance" ? (
              <Grid container>
                {questions.map((question, index) => (
                  <Grid item md={12} key={index}>
                    <Grid
                      container
                      sx={{
                        backgroundColor: "white",
                        maxHeight: "max-content",
                        height: "max-content",
                        borderRadius: "8px",
                        border: "1px solid #E0E0E0",
                        mt: 0.25,
                        boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                        padding: "16px",
                      }}
                    >
                      <Stack sx={{ width: "100%" }}>
                        <Typography
                          sx={{
                            fontSize: "12px",
                            fontWeight: "700",
                          }}
                        >
                          {question.MDG_GI_QUESTION_TYPE}
                          {question.MDG_GI_VISIBILITY === "Mandatory" ? (
                            <span style={{ color: "red" }}>*</span>
                          ) : (
                            ""
                          )}
                        </Typography>
  
                        {question.MDG_GI_INPUT_OPTION === "Radio Button" ? (
                          <RadioGroup
                            aria-labelledby={`radio-group-label-${index}`}
                            defaultValue=""
                            //name={`radio-group-${index}`}
                            value={
                              singleGIPayloadMain[
                                question.MDG_GI_QUESTION_TYPE.replaceAll(
                                  "(",
                                  ""
                                )
                                  .replaceAll(")", "")
                                  .replaceAll("/", "")
                                  .replaceAll("-", "")
                                  .replaceAll(".", "")
                                  .split(" ")
                                  .join("")
                              ]
                            }
                            name={question.MDG_GI_QUESTION_TYPE}
                            row
                            // onChange={handleRadioChangeGIQuestion}
                            disabled={!isEditMode}
                          >
                            {question.MDG_GI_INPUT_VALUE.split(",").map(
                              (option, optIndex) => (
                                <FormControlLabel
                                  key={optIndex}
                                  value={option}
                                  disabled={!isEditMode}
                                  control={<Radio />}
                                  label={option}
                                />
                              )
                            )}
                          </RadioGroup>
                        ) : 
                        question.MDG_GI_INPUT_OPTION === 'Dropdown' ? (
                          <Grid item md={2}>
                          {console.log(dropDownData[question.MDG_GI_QUESTION_TYPE],question.MDG_GI_QUESTION_TYPE, "hjkl")}
                              <Autocomplete
                                sx={{ height: "31px" }}
                                // disabled
                                fullWidth
                                size="small"
                                disabled={!isEditMode}
                                value={
                                  dropDownData[question.MDG_GI_QUESTION_TYPE]?.find(
                                    (x) =>
                                      x.MDG_LOOKUP_CODE ===
                                      singleGIPayloadMain[
                                        question.MDG_GI_QUESTION_TYPE.replaceAll("(", "")
                                          .replaceAll(")", "")
                                          .replaceAll("/", "")
                                          .replaceAll("-", "")
                                          .replaceAll(".", "")
                                          .split(" ")
                                          .join("")
                                      ]
                                  ) || null
                                }
                
                                // onChange={handleRadioChangeGIQuestion()}
                                onChange={(event, newValue) => handleDropDownChangeGIQuestion(event, newValue, 'Dropdown', question.MDG_GI_QUESTION_TYPE)}
                                // onChange={(event, newValue) => {
                                //   let label = question.MDG_GI_QUESTION_TYPE;
                                //   onEditGI(label, newValue?.MDG_LOOKUP_CODE);
                                // handleCheckValidationErrorGI();}}
                                options={dropDownData[question.MDG_GI_QUESTION_TYPE] ?? []}
                                getOptionLabel={(option) => `${option?.MDG_LOOKUP_CODE} `}
                                renderOption={(props, option) => (
                                  <li {...props}>
                                    <Typography style={{ fontSize: 12 }}>
                                      {option?.MDG_LOOKUP_CODE} 
                                    </Typography>
                                  </li>
                                )}
                                renderInput={(params) => (
                                  <TextField
                                    {...params}
                                    variant="outlined"
                                    placeholder="Please Enter..."
                                    // error={errorFields.includes(keyName)}
                                  />
                                )}
                              />
                        </Grid>
                        ):   
                        (
                          <TextField
                            fullWidth
                            placeholder="Please Enter..."
                            multiline
                            disabled={!isEditMode}
                            name={question.MDG_GI_QUESTION_TYPE}
                            value={
                              singleGIPayloadMain[
                                question.MDG_GI_QUESTION_TYPE.replaceAll(
                                  "(",
                                  ""
                                )
                                  .replaceAll(")", "")
                                  .replaceAll("/", "")
                                  .replaceAll("-", "")
                                  .replaceAll(".", "")
                                  .split(" ")
                                  .join("")
                              ]
                            }
                            // onChange={handleRadioChangeGIQuestionTextField}
                          />
                        )}
                      </Stack>
                    </Grid>
                  </Grid>
                ))}
              </Grid>
            ) : (
              <Grid container>
                {questions.map((question, index) => (
                  <Grid item md={12} key={index}>
                    <Grid
                      container
                      sx={{
                        backgroundColor: "white",
                        maxHeight: "max-content",
                        height: "max-content",
                        borderRadius: "8px",
                        border: "1px solid #E0E0E0",
                        mt: 0.25,
                        boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                        padding: "16px",
                      }}
                    >
                      <Stack sx={{ width: "100%" }}>
                        <Typography
                          sx={{
                            fontSize: "12px",
                            fontWeight: "700",
                          }}
                        >
                          {question.MDG_GI_QUESTION_TYPE}
                          {question.MDG_GI_VISIBILITY === "Mandatory" ? (
                            <span style={{ color: "red" }}>*</span>
                          ) : (
                            ""
                          )}
                        </Typography>
  
                        {question.MDG_GI_INPUT_OPTION === "Radio Button" ? (
                          <RadioGroup
                            aria-labelledby={`radio-group-label-${index}`}
                            defaultValue=""
                            // name={`radio-group-${index}`}
                            value={
                              singleGIPayloadMain[
                                question.MDG_GI_QUESTION_TYPE.replaceAll(
                                  "(",
                                  ""
                                )
                                  .replaceAll(")", "")
                                  .replaceAll("/", "")
                                  .replaceAll("-", "")
                                  .replaceAll(".", "")
                                  .split(" ")
                                  .join("")
                              ]
                            }
                            name={question.MDG_GI_QUESTION_TYPE}
                            row
                            // onChange={handleRadioChangeGIQuestion}
                            disabled={!isEditMode}
                          >
                            {question.MDG_GI_INPUT_VALUE.split(",").map(
                              (option, optIndex) => (
                                <FormControlLabel
                                  key={optIndex}
                                  disabled={!isEditMode}
                                  value={option}
                                  control={<Radio/>}
                                  label={option}
                                />
                              )
                            )}
                          </RadioGroup>
                        ) : 
                        question.MDG_GI_INPUT_OPTION === 'Dropdown' ? (
                          <Grid item md={2}>
                          {console.log(dropDownData[question.MDG_GI_QUESTION_TYPE],question.MDG_GI_QUESTION_TYPE, "hjkl")}
                              <Autocomplete
                                sx={{ height: "31px" }}
                                disabled = {!isEditMode}
                                fullWidth
                                size="small"
                                // value={valueFromPayload[keyName]}
                                // value = {singleGLPayloadMain?.[question.MDG_GI_QUESTION_TYPE]}
                                value={
                                  dropDownData[question.MDG_GI_QUESTION_TYPE]?.find(
                                    (x) =>
                                      x.MDG_LOOKUP_CODE ===
                                      singleGIPayloadMain[
                                        question.MDG_GI_QUESTION_TYPE.replaceAll("(", "")
                                          .replaceAll(")", "")
                                          .replaceAll("/", "")
                                          .replaceAll("-", "")
                                          .replaceAll(".", "")
                                          .split(" ")
                                          .join("")
                                      ]
                                  ) || null
                                }
                
                                // onChange={handleRadioChangeGIQuestion()}
                                onChange={(event, newValue) => handleDropDownChangeGIQuestion(event, newValue, 'Dropdown', question.MDG_GI_QUESTION_TYPE)}
                                // onChange={(event, newValue) => {
                                //   let label = question.MDG_GI_QUESTION_TYPE;
                                //   onEditGI(label, newValue?.MDG_LOOKUP_CODE);
                                // handleCheckValidationErrorGI();}}
                                options={dropDownData[question.MDG_GI_QUESTION_TYPE] ?? []}
                                getOptionLabel={(option) => `${option?.MDG_LOOKUP_CODE} `}
                                renderOption={(props, option) => (
                                  <li {...props}>
                                    <Typography style={{ fontSize: 12 }}>
                                      {option?.MDG_LOOKUP_CODE} 
                                    </Typography>
                                  </li>
                                )}
                                renderInput={(params) => (
                                  <TextField
                                    {...params}
                                    variant="outlined"
                                    placeholder="Please Enter..."
                                    // error={errorFields.includes(keyName)}
                                  />
                                )}
                              />
                        </Grid>
                        ):   
                        (
                          <TextField
                            fullWidth
                            placeholder="Please Enter..."
                            multiline
                            disabled={!isEditMode}
                            name={question.MDG_GI_QUESTION_TYPE}
                            value={
                              singleGIPayloadMain[
                                question.MDG_GI_QUESTION_TYPE.replaceAll(
                                  "(",
                                  ""
                                )
                                  .replaceAll(")", "")
                                  .replaceAll("/", "")
                                  .replaceAll("-", "")
                                  .replaceAll(".", "")
                                  .split(" ")
                                  .join("")
                              ]
                            }
                            // onChange={handleRadioChangeGIQuestion}
                          />
                        )}
                      </Stack>
                    </Grid>
                  </Grid>
                ))}
              </Grid>
            )}
          </>,
        ];
      }
      if (categoryData?.category == "Basic") {
        return [
          <Grid
            key={categoryData.category}
            container
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              // borderRadius: "8px",
              // border: "1px solid #E0E0E0",
              mt: 1,
              // boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
              // padding: "10px",
              mb: 1,
            }}
          >
            {sortedCategories.map(({ key: fieldGroup, fields }) => (
              <Grid
                key={fieldGroup}
                item
                md={12}
                sx={{
                  backgroundColor: "white",
                  maxHeight: "max-content",
                  height: "max-content",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                  mt: 0.25,
                  boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                  ...container_Padding,
                  // ...container_columnGap,
                }}
              >
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                    margin: "0px !important",
                  }}
                >
                  {fieldGroup}
                </Typography>
                <Box sx={{ width: "100%" }}>
                  <CardContent
                    sx={{
                      padding: "0",
                      paddingBottom: "0 !important",
                      paddingTop: "10px !important",
                    }}
                  >
                    <Grid
                      container
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(6,1fr)",
                        gap: "15px",
                      }}
                      justifyContent="space-between"
                      alignItems="flex-start"
                      md={12}
                    >
                      {fields.map((field) => {
                        console.log("fieldDatatttt", field);
                        return (
                          <EditableField
                            // key={field.fieldName}
                            label={field.fieldName}
                            value={field.value}
                            onSave={(newValue) =>
                              handleFieldSave(field.fieldName, newValue)
                            }
                            isEditMode={isEditMode}
                            isExtendMode={isExtendMode}
                            type={field.fieldType}
                            field={field} // Update the type as needed
                          />
                        );
                      })}
                    </Grid>
                  </CardContent>
                </Box>
              </Grid>
            ))}
          </Grid>,
        ];
      } else if (categoryData?.category == "Sales") {
        return [
          <Grid
            key={categoryData.category}
            container
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              // borderRadius: "8px",
              // border: "1px solid #E0E0E0",
              mt: 1,
              // boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
              // padding: "10px ",
              mb: 1,
            }}
          >
            {sortedCategories.map(({ key: fieldGroup, fields }) =>
              fieldGroup === "Tax Data" ? (
                <>
                {console.log("fieldGroup", fieldGroup)}
                <Grid
                  item
                  md={12}
                  sx={{
                    backgroundColor: "white",
                    maxHeight: "max-content",
                    height: "max-content",
                    borderRadius: "8px",
                    border: "1px solid #E0E0E0",
                    mt: 0.25,
                    boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                    ...container_Padding,
                    // ...container_columnGap,
                  }}
                >
                  <Grid container>
                    <Typography
                      sx={{
                        fontSize: "12px",
                        fontWeight: "700",
                      }}
                    >
                      {fieldGroup}
                    </Typography>
                  </Grid>
                  <Box>
                    <ReusableTable
                      width="100%"
                      rows={rows}
                      columns={columns}
                      getRowIdValue={"id"}
                      hideFooter={true}
                      checkboxSelection={false}
                    // experimentalFeatures={{ newEditingApi: true }}
                    />
                  </Box>
                </Grid>
                </>
              ) : (
                <Grid
                  key={fieldGroup}
                  item
                  md={12}
                  sx={{
                    backgroundColor: "white",
                    maxHeight: "max-content",
                    height: "max-content",
                    borderRadius: "8px",
                    border: "1px solid #E0E0E0",
                    mt: 0.25,
                    boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                    ...container_Padding,
                    // ...container_columnGap,
                  }}
                >
                  <Typography
                    sx={{
                      fontSize: "12px",
                      fontWeight: "700",
                      margin: "0px !important",
                    }}
                  >
                    {fieldGroup}
                  </Typography>
                  <Box sx={{ width: "100%" }}>
                    <CardContent>
                      <Grid
                        container
                        style={{
                          display: "grid",
                          gridTemplateColumns: "repeat(6,1fr)",
                          gap: "15px",
                        }}
                        justifyContent="space-between"
                        alignItems="flex-start"
                        md={12}
                      >
                        {fields.map((field) => {
                          console.log("fieldDatatttt2", field);
                          return (
                            <EditableField
                              // key={field.fieldName}
                              label={field.fieldName}
                              value={field.value}
                              onSave={(newValue) =>
                                handleFieldSave(field.fieldName, newValue)
                              }
                              isEditMode={isEditMode}
                              isExtendMode={isExtendMode}
                              type={field.fieldType} // Update the type as needed
                              field={field}
                            />
                          );
                        })}
                      </Grid>
                    </CardContent>
                  </Box>
                </Grid>
              )
            )}
          </Grid>,
        ];
      } else if (categoryData?.category == "Purchasing") {
        return [
          <Grid
            key={categoryData.category}
            container
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              // borderRadius: "8px",
              // border: "1px solid #E0E0E0",
              mt: 1,
              // boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
              // padding: "10px",
              mb: 1,
            }}
          >
            {sortedCategories.map(({ key: fieldGroup, fields }) => (
              <Grid
                key={fieldGroup}
                item
                md={12}
                sx={{
                  backgroundColor: "white",
                  maxHeight: "max-content",
                  height: "max-content",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                  mt: 0.25,
                  boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                  ...container_Padding,
                  // ...container_columnGap,
                }}
              >
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                    margin: "0px !important",
                  }}
                >
                  {fieldGroup}
                </Typography>
                <Box sx={{ width: "100%" }}>
                  <CardContent>
                    <Grid
                      container
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(6,1fr)",
                        gap: "15px",
                      }}
                      justifyContent="space-between"
                      alignItems="flex-start"
                      md={12}
                    >
                      {fields.map((field) => (
                        <EditableField
                          key={field.fieldName}
                          label={field.fieldName}
                          value={field.value}
                          onSave={(newValue) =>
                            handleFieldSave(field.fieldName, newValue)
                          }
                          isEditMode={isEditMode}
                          isExtendMode={isExtendMode}
                          type={field.fieldType} // Update the type as needed
                        />
                      ))}
                    </Grid>
                  </CardContent>
                </Box>
              </Grid>
            ))}
          </Grid>,
        ];
      } else if (categoryData?.category == "MRP") {
        return [
          <Grid
            key={categoryData.category}
            container
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              // borderRadius: "8px",
              // border: "1px solid #E0E0E0",
              mt: 1,
              // boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
              // padding: "10px",
              mb: 1,
            }}
          >
            {sortedCategories.map(({ key: fieldGroup, fields }) => (
              <Grid
                key={fieldGroup}
                item
                md={12}
                sx={{
                  backgroundColor: "white",
                  maxHeight: "max-content",
                  height: "max-content",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                  mt: 0.25,
                  boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                  ...container_Padding,
                  // ...container_columnGap,
                }}
              >
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                    margin: "0px !important",
                  }}
                >
                  {fieldGroup}
                </Typography>
                <Box sx={{ width: "100%" }}>
                  <CardContent>
                    <Grid
                      container
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(6,1fr)",
                        gap: "15px",
                      }}
                      justifyContent="space-between"
                      alignItems="flex-start"
                      md={12}
                    >
                      {fields.map((field) => (
                        <EditableField
                          key={field.fieldName}
                          label={field.fieldName}
                          value={field.value}
                          onSave={(newValue) =>
                            handleFieldSave(field.fieldName, newValue)
                          }
                          isEditMode={isEditMode}
                          isExtendMode={isExtendMode}
                          type={field.fieldType} // Update the type as needed
                        />
                      ))}
                    </Grid>
                  </CardContent>
                </Box>
              </Grid>
            ))}
          </Grid>,
        ];
      } else if (categoryData?.category == "Accounting") {
        return [
          <Grid
            key={categoryData.category}
            container
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              // borderRadius: "8px",
              // border: "1px solid #E0E0E0",
              mt: 1,
              // boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
              // padding: "10px",
              mb: 1,
            }}
          >
            {sortedCategories.map(({ key: fieldGroup, fields }) => (
              <Grid
                key={fieldGroup}
                item
                md={12}
                sx={{
                  backgroundColor: "white",
                  maxHeight: "max-content",
                  height: "max-content",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                  mt: 0.25,
                  boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                  ...container_Padding,
                  // ...container_columnGap,
                }}
              >
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                    margin: "0px !important",
                  }}
                >
                  {fieldGroup}
                </Typography>
                <Box sx={{ width: "100%" }}>
                  <CardContent>
                    <Grid
                      container
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(6,1fr)",
                        gap: "15px",
                      }}
                      justifyContent="space-between"
                      alignItems="flex-start"
                      md={12}
                    >
                      {fields.map((field) => (
                        <EditableField
                          key={field.fieldName}
                          label={field.fieldName}
                          value={field.value}
                          onSave={(newValue) =>
                            handleFieldSave(field.fieldName, newValue)
                          }
                          isEditMode={isEditMode}
                          isExtendMode={isExtendMode}
                          type={field.fieldType} // Update the type as needed
                        />
                      ))}
                    </Grid>
                  </CardContent>
                </Box>
              </Grid>
            ))}
          </Grid>,
        ];
      } else if (categoryData?.category == "Classification") {
        return [
          <Grid
            key={categoryData.category}
            container
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              // borderRadius: "8px",
              // border: "1px solid #E0E0E0",
              mt: 1,
              // boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
              // padding: "10px",
              mb: 1,
            }}
          >
            {Object.keys(categoryData.data).map((fieldGroup) => (
              <Grid
                key={fieldGroup}
                item
                md={12}
                sx={{
                  backgroundColor: "white",
                  maxHeight: "max-content",
                  height: "max-content",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                  mt: 0.25,
                  boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                  ...container_Padding,
                  // ...container_columnGap,
                }}
              >
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                    margin: "0px !important",
                  }}
                >
                  {fieldGroup}
                </Typography>
                <Box sx={{ width: "100%" }}>
                  <CardContent>
                    <Grid
                      container
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(6,1fr)",
                        gap: "15px",
                      }}
                      justifyContent="space-between"
                      alignItems="flex-start"
                      md={12}
                    >
                      {categoryData.data[fieldGroup].map((field) => (
                        <EditableField
                          key={field.fieldName}
                          label={field.fieldName}
                          value={field.value}
                          onSave={(newValue) =>
                            handleFieldSave(field.fieldName, newValue)
                          }
                          isEditMode={isEditMode}
                          isExtendMode={isExtendMode}
                          type={field.fieldType} // Update the type as needed
                        />
                      ))}
                    </Grid>
                  </CardContent>
                </Box>
              </Grid>
            ))}
          </Grid>,
        ];
      } else if (categoryData?.category == "Attachments"){
        return [
          <>
            {isEditMode ? (
              <>
              {attachments.map((attachment, index) => (
                <>
                  <Grid
                    key={index}
                    item
                    md={12}
                    sx={{
                      backgroundColor: "white",
                      maxHeight: "max-content",
                      height: "max-content",
                      borderRadius: "8px",
                      border: "1px solid #E0E0E0",
                      mt: 0.25,
                      boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                      padding: "1rem 1.5rem",
                      // ...container_Padding,
                      // ...container_columnGap,
                    }}
                  >
                    {console.log("attt3", attachment)}
                  <Grid container>
                    <Typography
                      sx={{
                        fontSize: "12px",
                        fontWeight: "700",
                      }}
                    >
                      {attachment.MDG_ATTACHMENTS_NAME}
                    </Typography>
                  </Grid>

                  <Grid container>
                    <Grid item>
                      <ReusableAttachementAndComments
                        title="Material"
                        useMetaData={false}
                        artifactId={`${attachment.MDG_ATTACHMENTS_NAME}_${mmNumber}`}
                        artifactName="Material"
                        attachmentType={attachment.MDG_ATTACHMENTS_NAME}
                      />
                    </Grid>
                  </Grid>
                </Grid>
                </>
              ))}
            </>
            ) : (
              <>
              {attachmentsData.map((attachment, index) => (
              <Grid
                key={index}
                item
                md={12}
                sx={{
                  backgroundColor: "white",
                  maxHeight: "max-content",
                  height: "max-content",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                  mt: 0.25,
                  boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                  padding: "1rem 1.5rem",
                  // ...container_Padding,
                  // ...container_columnGap,
                }}
              >
                <Grid container>
                  <Typography
                    sx={{
                      fontSize: "12px",
                      fontWeight: "700",
                    }}
                  >
                    {attachment.MDG_ATTACHMENTS_NAME}
                  </Typography>
                </Grid>

                <Grid container>
                  <Grid item>
                    <ReusableAttachementAndComments
                      title="Material"
                      useMetaData={false}
                      artifactId={`${attachment.MDG_ATTACHMENTS_NAME}_${mmNumber}`}
                      artifactName="Material"
                      attachmentType={attachment.MDG_ATTACHMENTS_NAME}
                    />
                  </Grid>
                </Grid>
              </Grid>
            ))}
            {/* {Boolean(attachments.length) && ( */}
              <Card sx={{ padding: "1rem 1rem 0rem 1rem" }}>
                <Grid
                  container
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                  }}
                >
                  <Typography variant="h6">
                    <strong>Attachments</strong>
                  </Typography>
                </Grid>
                {Boolean(attachments.length) && (
                  <ReusableTable
                    width="100%"
                    rows={attachments}
                    columns={attachmentColumns}
                    hideFooter={false}
                    getRowIdValue={"id"}
                    disableSelectionOnClick={true}
                    stopPropagation_Column={"action"}
                  />
                )}
                {!Boolean(attachments.length) && (
                  <Typography variant="body2">No Attachments Found</Typography>
                )}
                <br />
                <Typography variant="h6">Comments</Typography>
                {Boolean(comments.length) && (
                  <Timeline
                    sx={{
                      [`& .${timelineItemClasses.root}:before`]: {
                        flex: 0,
                        padding: 0,
                      },
                    }}
                  >
                    {comments.map((comment) => (
                      <TimelineItem>
                        <TimelineSeparator>
                          <TimelineDot>
                            <CheckCircleOutlineOutlined
                              sx={{ color: "#757575" }}
                            />
                          </TimelineDot>
                          <TimelineConnector />
                        </TimelineSeparator>
                        <TimelineContent sx={{ py: "12px", px: 2 }}>
                          <Card
                            elevation={0}
                            sx={{
                              border: 1,
                              borderColor: "#C4C4C4",
                              borderRadius: "8px",
                              width: "650px",
                            }}
                          >
                            <Box sx={{ padding: "1rem" }}>
                              <Stack spacing={1}>
                                <Grid
                                  sx={{
                                    display: "flex",
                                    justifyContent: "space-between",
                                  }}
                                >
                                  <Typography
                                    sx={{
                                      textAlign: "right",
                                      color: " #757575",
                                      fontWeight: "500",
                                      fontSize: "12px",
                                    }}
                                  >
                                    {moment(comment.createdAt).format(
                                      "DD MMM YYYY"
                                    )}
                                  </Typography>
                                </Grid>

                                <Typography
                                  sx={{
                                    fontSize: "12px",

                                    color: " #757575",
                                    fontWeight: "500",
                                  }}
                                >
                                  {comment.user}
                                </Typography>
                                <Typography
                                  sx={{
                                    fontSize: "12px",
                                    color: "#1D1D1D",
                                    fontWeight: "600",
                                  }}
                                >
                                  {comment.comment}
                                </Typography>
                              </Stack>
                            </Box>
                          </Card>
                        </TimelineContent>
                      </TimelineItem>
                    ))}
                  </Timeline>
                )}
                {!Boolean(comments.length) && (
                  <Typography variant="body2">No Comments Found</Typography>
                )}
                <br />
              </Card>
            {/* )} */}
              </>
            )}
    </>
        ];
      }
    });

  const handleFieldSave = (fieldName, newValue) => {
    // Handle saving newValue for the corresponding fieldName
    // console.log(`Saving ${newValue} for field ${fieldName}`);
    // Perform save operation or update state as needed
  };
  console.log("tabContents", tabContents);
  useEffect(() => {
    if (factorsArray.length > 0 && selectedItems.length === 0) {
      setSelectedItems([factorsArray[0]]);
    }
  }, [factorsArray, selectedItems]);
  console.log("materialData.materialType", materialData);
  const handleSelectViewsToExtend = () => {
    handleMaterialType(materialData.materialType.slice(0, 4));
    setOpenSelectViews(true);
  };
  const handleMaterialType = (value) => {
    var tempMaterialType = value;
    setValue(
      fieldListAccordingToView.filter(
        (item) => factorsArray.findIndex((label) => label == item.label) != -1
      )
    );
    dispatch(
      newValueForData(
        fieldListAccordingToView.filter(
          (item) => factorsArray.findIndex((label) => label == item.label) != -1
        )
      )
    );
    setSelectStatus(false);
    getViews(value);
    setMaterialTypeDesc(tempMaterialType.desc);
    setMaterialTypeCode(tempMaterialType.code);
  };
  const getViews = (value) => {
    const hSuccess = (data) => {
      console.log("dataie", data);
      setFilteredViewType(data.body[0].MaintStatus.split(""));
      setFieldReference(data.body[0].MaterialType);
      getAutocompleteOptions(filteredViewType);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getViewForMaterialType?materialType=${value}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getAutocompleteOptions = (codes) => {
    const autocompleteOptions = [];
    fieldListAccordingToView.forEach((item) => {
      if (codes.includes(item.code)) {
        autocompleteOptions.push({
          label: item.label,
          value: item.value,
          fields: item.fields,
        });
      }
    });

    return autocompleteOptions;
  };

  console.log("selectedCheckboxes", selectedCheckboxes.join(","));
  const handleCheckboxChange = (event) => {
    const { name, checked } = event.target;
    setSelectedCheckboxes((prevSelected) =>
      checked
        ? [...prevSelected, name]
        : prevSelected.filter((item) => item !== name)
    );
  };

  

  const handleSelectedViewsDialogProceed = () => {
    // getOrgLookupData();

    // factorsArray = valueRequiredForSelectViews;

    setFactorsArray(value.map((item) => item.label));
    // let SelectedViews = value.map((item)=>item.label)

    // console.log("first",SelectedViews)

    setIsEditMode(false);

    if (
      (value.length <= 2 &&
        value.map((x) => x.label).includes("Classification")) ||
      (value.map((x) => x.label).includes("Basic data") && value.length < 2)
    ) {
      setIsExtendMode(true);
      setOpenSelectViews(false);
    } else {
      const uniqueFields = Array.from(
        new Set(
          value.reduce(
            (fields, fieldListAccordingToView) => [
              ...fields,
              ...fieldListAccordingToView.fields,
            ],
            []
          )
        )
      );
      getOrgLookupData(uniqueFields);
      setDisplayedFields(uniqueFields);
      setOpenSelectViews(false);
      setOpen(true);
    }
  };

  // const handleSelectedViewsCorrectionDialogProceed = () => {

  // };

  const handleChange = (event, newValue) => {
    setActiveTab(newValue);
    // console.log("newValue", newValue);
  };
  const autocompleteOptions = getAutocompleteOptions(filteredViewType);
  console.log("autocompleteOptions", autocompleteOptions);
  return (
    <div style={{ backgroundColor: "#FAFCFF" }}>
      <ReusableToast
        isLoadingToast={isLoadingToast}
        type={toastType}
        message={toastMessage}
        isToastPromise={isToastPromise }
      />
      {/* <ToastContainer />  */}
      <ReusableSnackBar
        openSnackBar={openSnackbar}
        alertMsg={messageDialogMessage}
        handleSnackBarClose={handleSnackBarClose}
      /> 
      {/* select views dialog */}
      <Dialog
        fullWidth="true"
        maxWidth="sm"
        open={openSelectViews}
        onClose={handleselectedViewsDialogClose}
      // sx={{ display: "flex", justifyContent: "center" }}
      >
        <Box>
          <DialogTitle>
            <DescriptionIcon
              style={{
                height: "20px",
                width: "20px",
                marginBottom: "-5px",
              }}
            />
            <span>Select Views to Extend</span>
          </DialogTitle>
          <DialogContent>
            <Grid
              container
              spacing={1}
              style={{ display: "flex", paddingBottom: "5px" }}
            >
              <Grid item md={6} flex={1}>
                <Autocomplete
                  size="small"
                  multiple
                  disableCloseOnSelect
                  id="checkboxes-tags-demo"
                  options={autocompleteOptions ?? []}
                  getOptionLabel={(option) => option.label}
                  isOptionEqualToValue={(option, value) => {
                    return option.value == value.value;
                  }}
                  value={
                    valueRequiredForSelectViews.length
                      ? valueRequiredForSelectViews
                      : value
                  }
                  onChange={(event, newValue, reason) => {
                    console.log("newvalue", newValue, event, reason);
                    let uniqueValue = [...new Set(newValue)];
                    if (reason === "selectOption") {
                      setValue(uniqueValue);
                      dispatch(newValueForData(uniqueValue));
                    } else if (reason === "removeOption") {
                      setCheckAll(false);
                      setSelectStatus(false);
                      if (!newValue.length) {
                        setValue([fieldListAccordingToView[0]]);
                        dispatch(
                          newValueForData([fieldListAccordingToView[0]])
                        );
                      } else {
                        if (uniqueValue.find((a) => a.value == "basicData")) {
                          setValue(uniqueValue);
                          dispatch(newValueForData(uniqueValue));
                        } else {
                          setValue([
                            fieldListAccordingToView[0],
                            ...uniqueValue,
                          ]);
                          dispatch(
                            newValueForData([
                              fieldListAccordingToView[0],
                              ...uniqueValue,
                            ])
                          );
                        }
                      }
                    } else if (reason === "clear") {
                      setValue([fieldListAccordingToView[0]]);
                      dispatch(newValueForData([fieldListAccordingToView[0]]));
                      setCheckAll(false);
                      setSelectStatus(false);
                    }
                  }}
                  renderOption={(props, option, { selected }) => {
                    // console.log("option", option);
                    // console.log("selected", selected);
                    console.log("props", factorsArray, option, value);

                    return (
                      <li {...props}>
                        <Checkbox
                          icon={icon}
                          checkedIcon={checkedIcon}
                          style={{ marginRight: 8 }}
                          checked={
                            // props["data-option-index"] === 0
                            value.findIndex(
                              (item) => item.label == option.label
                            ) != -1
                              ? true
                              : false
                          }
                        />

                        <option>{option.label}</option>
                      </li>
                    );
                  }}
                  style={{ minWidth: "100%" }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      placeholder="Select Views to Extend"
                    />
                  )}
                />
              </Grid>
              <Grid item md={2} sx={{ display: "flex", alignItems: "center" }}>
                {selectStatus ? (
                  <Button variant="outlined" disabled>
                    Select all
                  </Button>
                ) : (
                  <Button
                    variant="outlined"
                    onClick={checkAllChange}
                    onMouseDown={(e) => e.preventDefault()}
                  >
                    Select all
                  </Button>
                )}
              </Grid>
              <Grid
                item
                md={4}
                style={{
                  display: "flex",
                  justifyContent: "end",
                  alignItems: "center",
                }}
              ></Grid>
            </Grid>
          </DialogContent>
          {/* <ReusableSnackBar
          openSnackBar={()=>{}}
          alertMsg={"Helloo"}
          handleSnackBarClose={()=>{}}
        /> */}
          <DialogActions>
            <Button onClick={handleselectedViewsDialogClose}>Cancel</Button>
            <Button onClick={handleSelectedViewsDialogProceed}>Next</Button>
          </DialogActions>
        </Box>
      </Dialog>
      {/* select org elements dialog */}
      <Dialog
        open={open}
        onClose={handleClose}
        sx={{ display: "flex", justifyContent: "center" }}
      >
        <Box sx={{ width: "600px !important" }}>
          <DialogTitle>
            <DescriptionIcon
              style={{
                height: "20px",
                width: "20px",
                marginBottom: "-5px",
              }}
            />
            <span>Select Org Data</span>
          </DialogTitle>
          <DialogContent>
            <Grid container columnSpacing={1}>
              {displayedFields.map((fieldName, index) => (
                <Fragment key={index}>
                  <Grid item md={4}>
                    <Typography sx={font_Small}>
                      {fieldName}
                      <span style={{ color: "red" }}>*</span>
                    </Typography>
                  </Grid>
                  <Grid item md={8}>
                    <Autocomplete
                      options={getDataForField(fieldName) ?? []}
                      sx={{ height: "42px" }}
                      size="small"
                      getOptionLabel={(option) =>
                        getOptionLabel(fieldName, option)
                      }
                      // value={getValueForField(fieldName)}
                      renderOption={(props, option) =>
                        renderOption(fieldName, props, option)
                      }
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          placeholder={`Select ${fieldName}`}
                        // error={!orgTypeValid}
                        />
                      )}
                      // onChange={handleOrgElement}
                      onChange={(e, newValue) => {
                        handleFieldChange(fieldName, newValue);
                        // handleOrgData(fieldName,newValue)
                      }}
                      placeholder="Select from Dropdown"
                    />
                  </Grid>
                </Fragment>
              ))}
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleClose}>Cancel</Button>
            <Button onClick={handleDialogProceed}>Proceed</Button>
          </DialogActions>
        </Box>
      </Dialog>

      <Dialog
        fullWidth="true"
        maxWidth="sm"
        open={openSelectCorrection}
      // onClose={handleSelectedCorrectionClose}
      // sx={{ display: "flex", justifyContent: "center" }}
      > <>
        {console.log("corrrr")}
        <Box>
          <DialogTitle>
            <DescriptionIcon
              style={{
                height: "20px",
                width: "20px",
                marginBottom: "-5px",
              }}
            />
            <span>Select Views to Send for Correction</span>
          </DialogTitle>
          <DialogContent>
            <Grid
              container
              spacing={1}
              style={{
                display: "flex",
                paddingBottom: "5px",
                flexDirection: "column",
              }}
            >
              <Grid item>
                {checkIwaAccess(iwaAccessData, "Display Material", "Change") &&
                  (userData?.role === "MDM Steward"
                    ? factorsArrayWithoutBasicData.map((x) => {
                      return (
                        <>
                          {console.log("insideeee", x)}
                          <FormGroup>
                            <FormControlLabel
                              control={<Checkbox name={x} onChange={handleCheckboxChange}/>}
                              label={x}
                            />
                          </FormGroup>
                        </>
                      );
                    })
                    : userData?.role === "Approver"
                      ? viewNameArray.map((x) => {
                        return (
                          <>
                            <FormGroup>
                              <FormControlLabel
                                control={<Checkbox name={x} onChange={handleCheckboxChange} />}
                                label={x}
                              />
                            </FormGroup>
                          </>
                        );
                      })
                      : "")}
              </Grid>
              <Grid>
                <Typography>Please Confirm Your Selection</Typography>
              </Grid>
            </Grid>
          </DialogContent>
          {/* <ReusableSnackBar
          openSnackBar={()=>{}}
          alertMsg={"Helloo"}
          handleSnackBarClose={()=>{}}
        /> */}
          <DialogActions>
            <Button onClick={handleCorrectionDialogClose}>Cancel</Button>
            <Button onClick={handleCorrectionViewForCreate}>Submit</Button>
          </DialogActions>
        </Box>
        </>
      </Dialog>

      <Grid container sx={outermostContainer_Information}>
        <Grid item md={12} style={{ padding: "16px", display: "flex" }}>
          <Grid md={9} sx={{ display: "flex" }}>
            <Grid>
              <IconButton
                // onClick={handleBacktoRO}
                color="primary"
                aria-label="upload picture"
                component="label"
                sx={iconButton_SpacingSmall}
              >
                <ArrowCircleLeftOutlinedIcon
                  sx={{
                    fontSize: "25px",
                    color: "#000000",
                  }}
                  onClick={() => {
                    navigate(-1);
                  }}
                />
              </IconButton>
            </Grid>
            <Grid>
              {isEditMode && materialData?.requestType === "Create" ? (
                <Grid item md={12}>
                  <Typography variant="h3">
                    <strong>Create Material </strong>
                  </Typography>

                  <Typography variant="body2" color="#777">
                    This view is to fill the Data for Creation
                  </Typography>
                </Grid>
              ) : isEditMode ? (
                materialData?.requestType === "Extend" ? (
                  <Grid item md={12}>
                    <Typography variant="h3">
                      <strong>Extend Material </strong>
                    </Typography>

                    <Typography variant="body2" color="#777">
                      This view extends the material
                    </Typography>
                  </Grid>
                ) : (
                  <Grid item md={12}>
                    <Typography variant="h3">
                      <strong>Change Material </strong>
                    </Typography>

                    <Typography variant="body2" color="#777">
                      This view edits the details of the materials
                    </Typography>
                  </Grid>
                )
              ) : (
                ""
              )}
              {isExtendMode ? (
                <Grid item md={12}>
                  <Typography variant="h3">
                    <strong>Extend Material </strong>
                  </Typography>

                  <Typography variant="body2" color="#777">
                    This view extends the details of the materials
                  </Typography>
                </Grid>
              ) : (
                ""
              )}

              {isDisplayMode && materialData?.requestType === "Create" ? (
                <Grid item md={12}>
                  <Typography variant="h3">
                    <strong>Create Material </strong>
                  </Typography>

                  <Typography variant="body2" color="#777">
                    This view is to fill the Data for Creation
                  </Typography>
                </Grid>
              ) : isDisplayMode ? (
                <Grid item md={12}>
                  <Typography variant="h3">
                    <strong>Display Material </strong>
                  </Typography>

                  <Typography variant="body2" color="#777">
                    This view displays the details of the materials
                  </Typography>
                </Grid>
              ) : (
                ""
              )}
            </Grid>
          </Grid>
          <Grid
            md={3}
            sx={{ display: "flex", justifyContent: "flex-end" }}
            gap={2}
          >
            <Grid>
              <Button
                variant="outlined"
                size="small"
                sx={button_Outlined}
                onClick={openChangeLog}
                title="Change Log"
              >
                <TrackChangesTwoToneIcon
                  sx={{ padding: "2px" }}
                  fontSize="small"
                />
              </Button>
            </Grid>

            {isChangeLogOpen && (
              <>
                <ChangeLog
                open={true}
                closeModal={handleClosemodalData}
                requestId={materialData?.requestId}
                requestType={materialData?.requestType}
                pageName={"Material"}
                controllingArea={
                  materialData?.materialNumber
                   ? materialData?.materialNumber 
                   : headerData?.Product
                }
                submittedView= {factorsArray[activeTab]}
              />
                {console.log("reqqqq1", materialData?.requestId)}
                {console.log("reqqqq2", materialData?.requestType)}
                {console.log("reqqqq3", materialData?.materialNumber
                   ? materialData?.materialNumber 
                   : headerData?.Product)}
                {console.log(factorsArray[activeTab], "subm")}
              </>
              
            )}

            {isDisplayMode ? (
              <Grid gap={1} sx={{ display: "flex" }}>
                {activeTab == 1 ? (
                  <Grid
                    item
                    // md={2}
                    sx={{
                      display: "flex",
                      justifyContent: "flex-end",
                      width: "100%",
                    }}
                  >
                    <Button
                      variant="outlined"
                      size="small"
                      sx={button_Outlined}
                      onClick={displayAdditionalData}
                    >
                      Additional Data
                    </Button>
                  </Grid>


                ) : (
                  ""
                )}

                <Grid
                  gap={1}
                  sx={{ display: "flex", justifyContent: "space-between" }}
                >
                  {checkIwaAccess(
                    iwaAccessData,
                    "Display Material",
                    "Change"
                  ) &&
                    (userData?.role === "Super User" && activeTab !== 0 && activeTab !== factorsArray.length - 1 ? (
                      materialData?.requestType === "Create" ||
                        taskRowDetails?.requestType === "Create" ? (
                        <>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={onEdit}
                            >
                              Fill Details
                              <EditOutlinedIcon
                                sx={{ marginLeft: "5px", padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                        </>
                      ) : materialData?.requestType === "Extend" ||
                        taskRowDetails?.requestType === "Extend" ? (
                        <>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={onEdit}
                            >
                              Fill Details
                              <EditOutlinedIcon
                                sx={{ marginLeft: "5px", padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                        </>
                      ) : (
                        <>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              // style={{paddingRight:"5px"}}
                              onClick={onEdit}
                            >
                              Change
                              <EditOutlinedIcon
                                sx={{ marginLeft: "5px", padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={handleSelectViewsToExtend}
                            >
                              Extend
                            </Button>
                          </Grid>
                        </>
                      )
                    ) : userData?.role === "MDM Steward" &&
                      factorsArray[activeTab] === "Basic Data" ? (
                      materialData?.requestType === "Create" ||
                        taskRowDetails?.requestType === "Create" ? (
                        <>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={onEdit}
                            >
                              Fill Details
                              <EditOutlinedIcon
                                sx={{ marginLeft: "5px", padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                        </>
                      ) : !materialData?.requestType ? (
                        <>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={handleSelectViewsToExtend}
                            >
                              Extend
                              <EditOutlinedIcon
                                sx={{ marginLeft: "5px", padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={onEdit}
                            >
                              Change
                              <EditOutlinedIcon
                                sx={{ marginLeft: "5px", padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                        </>
                      ) : (
                        <>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={onEdit}
                            >
                              Change
                              <EditOutlinedIcon
                                sx={{ padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={handleSelectViewsToExtend}
                            >
                              Extend
                            </Button>
                          </Grid>
                        </>
                      )
                    ) : userData?.role === "Sales" &&
                      factorsArray[activeTab] === "Sales" ? (
                      materialData?.requestType === "Create" ||
                        taskRowDetails?.requestType === "Create" ? (
                        <>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={onEdit}
                            >
                              Fill Details
                              <EditOutlinedIcon
                                sx={{ marginLeft: "5px", padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                        </>
                      ) : materialData?.requestType === "Extend" ||
                        taskRowDetails?.requestType === "Extend" ? (
                        <>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={onEdit}
                            >
                              Fill Details
                              <EditOutlinedIcon
                                sx={{ marginLeft: "5px", padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                        </>
                      ) : !materialData?.requestType ? (
                        <>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={handleSelectViewsToExtend}
                            >
                              Extend
                              <EditOutlinedIcon
                                sx={{ marginLeft: "5px", padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={onEdit}
                            >
                              Change
                              <EditOutlinedIcon
                                sx={{ marginLeft: "5px", padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                        </>
                      ) : (
                        <Grid item>
                          <Button
                            variant="outlined"
                            size="small"
                            sx={button_Outlined}
                            onClick={onEdit}
                          >
                            Change
                            <EditOutlinedIcon
                              sx={{ padding: "2px" }}
                              fontSize="small"
                            />
                          </Button>
                        </Grid>
                      )
                    ) : userData?.role === "Finance" &&
                      factorsArray[activeTab] === "Accounting" ? (
                      materialData?.requestType === "Create" ||
                        taskRowDetails?.requestType === "Create" ? (
                        <>
                        {console.log("fpppp", activeTab, materialDetails[activeTab]?.category)}
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={onEdit}
                            >
                              Fill Details
                              <EditOutlinedIcon
                                sx={{ marginLeft: "5px", padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                        </>
                      ) : materialData?.requestType === "Extend" ||
                        taskRowDetails?.requestType === "Extend" ? (
                        <>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={onEdit}
                            >
                              Fill Details
                              <EditOutlinedIcon
                                sx={{ marginLeft: "5px", padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                        </>
                      ) : !materialData?.requestType ? (
                        <>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={handleSelectViewsToExtend}
                            >
                              Extend
                              <EditOutlinedIcon
                                sx={{ marginLeft: "5px", padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={onEdit}
                            >
                              Change
                              <EditOutlinedIcon
                                sx={{ marginLeft: "5px", padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                        </>
                      ) : (
                        <Grid item>
                          <Button
                            variant="outlined"
                            size="small"
                            sx={button_Outlined}
                            onClick={onEdit}
                          >
                            Change
                            <EditOutlinedIcon
                              sx={{ padding: "2px" }}
                              fontSize="small"
                            />
                          </Button>
                        </Grid>
                      )
                    ) : userData?.role === "Procurement" &&
                      factorsArray[activeTab] === "Purchasing" ? (
                      materialData?.requestType === "Create" ||
                        taskRowDetails?.requestType === "Create" ? (
                        <>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={onEdit}
                            >
                              Fill Details
                              <EditOutlinedIcon
                                sx={{ marginLeft: "5px", padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                        </>
                      ) : materialData?.requestType === "Extend" ||
                        taskRowDetails?.requestType === "Extend" ? (
                        <>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={onEdit}
                            >
                              Fill Details
                              <EditOutlinedIcon
                                sx={{ marginLeft: "5px", padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                        </>
                      ) : !materialData?.requestType ? (
                        <>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={handleSelectViewsToExtend}
                            >
                              Extend
                              <EditOutlinedIcon
                                sx={{ marginLeft: "5px", padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={onEdit}
                            >
                              Change
                              <EditOutlinedIcon
                                sx={{ marginLeft: "5px", padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                        </>
                      ) : (
                        <Grid item>
                          <Button
                            variant="outlined"
                            size="small"
                            sx={button_Outlined}
                            onClick={onEdit}
                          >
                            Change
                            <EditOutlinedIcon
                              sx={{ padding: "2px" }}
                              fontSize="small"
                            />
                          </Button>
                        </Grid>
                      )
                    ) : (
                      ""
                    ))}
                </Grid>
              </Grid>
            ) : (
              ""
            )}
          </Grid>
        </Grid>
        <Grid container display="flex" flexDirection="row" flexWrap="nowrap">
          <Box width="65%" sx={{ marginLeft: "40px" }}>
            <Grid item>
              <Stack flexDirection="row">
                <Typography
                  variant="body2"
                  color="#777"
                  style={{ width: "15%" }}
                >
                  Material
                </Typography>
                <Typography
                  variant="body2"
                  fontWeight="bold"
                  sx={{ width: "5%", textAlign: "center" }}
                >
                  :
                </Typography>

                <Typography
                  variant="body2"
                  fontWeight="bold"
                  justifyContent="flex-start"
                >
                  {materialData?.materialNumber
                    ? materialData?.materialNumber
                    : headerData?.Product}
                </Typography>
              </Stack>
            </Grid>

            <Grid item>
              <Stack flexDirection="row">
                <Typography
                  variant="body2"
                  color="#777"
                  style={{ width: "15%" }}
                >
                  Industry Sector
                </Typography>
                <Typography
                  variant="body2"
                  fontWeight="bold"
                  sx={{ width: "5%", textAlign: "center" }}
                >
                  :
                </Typography>

                <Typography
                  variant="body2"
                  fontWeight="bold"
                  justifyContent="flex-start"
                >
                  {materialData?.industrySector
                    ? materialData?.industrySector
                    : headerData?.IndustrySector}
                </Typography>
              </Stack>
            </Grid>
            <Grid item>
              <Stack flexDirection="row">
                <Typography
                  variant="body2"
                  color="#777"
                  style={{ width: "15%" }}
                >
                  Material Type
                </Typography>
                <Typography
                  variant="body2"
                  fontWeight="bold"
                  sx={{ width: "5%", textAlign: "center" }}
                >
                  :
                </Typography>

                <Typography
                  variant="body2"
                  fontWeight="bold"
                  justifyContent="flex-start"
                >
                  {materialData?.materialType
                    ? materialData?.materialType
                    : headerData?.ProductType}
                </Typography>
              </Stack>
            </Grid>

            <Grid item>
              <Stack flexDirection="row">
                <Typography
                  variant="body2"
                  color="#777"
                  style={{ width: "15%" }}
                >
                  Material Description
                </Typography>
                <Typography
                  variant="body2"
                  fontWeight="bold"
                  sx={{ width: "5%", textAlign: "center" }}
                >
                  :
                </Typography>
                {checkIwaAccess(iwaAccessData, "Display Material", "Change") &&
                  (userData?.role === "MDM Steward" && isEditMode ? (

                    <Grid item md={8} sx={{ padding: "5px", display: "flex", flexDirection: "row" }}>
                      <Grid md={4}>
                        <TextField
                          size="small"
                          value={displayDescription?.description ? displayDescription?.description : headerData?.Description ? headerData?.Description : materialData?.materialDesc
                            ? materialData?.materialDesc : ""}
                          style={{ width: "100%" }}
                          // error={!materialDescValid}
                          // onClick={handleTextFieldClick}
                          // onChange={handleMaterialDesc}
                          placeholder="Enter Material Description"
                        />
                      </Grid>
                      <Grid md={1}>
                        <Button onClick={handleTextFieldClick}>
                          <InfoIcon />
                        </Button>
                      </Grid>
                    </Grid>
                  ) :
                    (<Typography
                      variant="body2"
                      fontWeight="bold"
                      justifyContent="flex-start"
                    >
                      {materialData?.materialDesc
                        ? materialData?.materialDesc
                        : headerData?.Description}
                    </Typography>))}
              </Stack>
            </Grid>


            <DuplicateDesc
              open={isDialogOpen}
              onClose={() => setIsDialogOpen(false)}
            />
          </Box>
          {viewNameArray.length > 1 && 
          <Box width="30%" sx={{ marginLeft: "40px" }}>
            {materialData?.plantOrg !== "Not Available" ||
              response?.plantOrg !== "" ? (
              <Grid item>
                <Stack flexDirection="row">
                  <Typography
                    variant="body2"
                    color="#777"
                    style={{ width: "35%" }}
                  >
                    Plant
                  </Typography>

                  <Typography
                    variant="body2"
                    fontWeight="bold"
                    sx={{ width: "8%", textAlign: "center" }}
                  >
                    :
                  </Typography>

                  <Typography
                    variant="body2"
                    fontWeight="bold"
                    justifyContent="flex-start"
                  >
                    {materialData?.plant
                      ? materialData?.plant
                      : response?.plantOrg
                        ? response?.plantOrg
                        : taskData?.body?.plantOrg
                          ? taskData?.body?.plantOrg
                          : ""}
                  </Typography>
                </Stack>
              </Grid>
            ) : (
              ""
            )}
            {materialData?.salesOrg !== "Not Available" ? (
              <Grid item>
                <Stack flexDirection="row">
                  <Typography
                    variant="body2"
                    color="#777"
                    style={{ width: "35%" }}
                  >
                    SalesOrg
                  </Typography>

                  <Typography
                    variant="body2"
                    fontWeight="bold"
                    sx={{ width: "8%", textAlign: "center" }}
                  >
                    :
                  </Typography>

                  <Typography
                    variant="body2"
                    fontWeight="bold"
                    justifyContent="flex-start"
                  >
                    {materialData?.salesOrg
                      ? materialData?.salesOrg
                      : response?.salesOrg
                        ? response?.salesOrg
                        : taskData?.body?.salesOrg
                          ? taskData?.body?.salesOrg
                          : ""}
                  </Typography>
                </Stack>
              </Grid>
            ) : (
              ""
            )}
            {materialData?.distributionChannel !== "Not Available" ? (
              <Grid item>
                <Stack flexDirection="row">
                  <Typography
                    variant="body2"
                    color="#777"
                    style={{ width: "35%" }}
                  >
                    Distribution Channel
                  </Typography>

                  <Typography
                    variant="body2"
                    fontWeight="bold"
                    sx={{ width: "8%", textAlign: "center" }}
                  >
                    :
                  </Typography>

                  <Typography
                    variant="body2"
                    fontWeight="bold"
                    justifyContent="flex-start"
                  >
                    {materialData?.distChnl
                      ? materialData?.distChnl
                      : response?.distChnl
                        ? response?.distChnl
                        : taskData?.body?.distChnl
                          ? taskData?.body?.distChnl
                          : ""}
                  </Typography>
                </Stack>
              </Grid>
            ) : (
              ""
            )}
          </Box>
          }
        </Grid>

        <Dialog
          open={correctionPopup}
          onClose={handleClose}
          sx={{ display: "flex", justifyContent: "center" }}
        >
          <Box sx={{ width: "600px !important" }}>
            <DialogTitle>
              <DescriptionIcon
                style={{
                  height: "20px",
                  width: "20px",
                  marginBottom: "-5px",
                }}
              />
              <span>Enter Comments for Correction</span>
            </DialogTitle>
            <DialogContent>
              <Grid container columnSpacing={1}>
                <textarea />
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCommentClose}>Cancel</Button>
              <Button onClick={commentAction}>OK</Button>
            </DialogActions>
          </Box>
        </Dialog>

        <Grid container style={{ marginLeft: 25 }}>
          {/* {checkIwaAccess(
            iwaAccessData,
            "Display Material",
            "Display Bottomnavigation"
          ) && ( */}
          <Tabs
            value={activeTab}
            onChange={handleChange}
            variant="scrollable"
            sx={{
              background: "#FFF",
              borderBottom: "1px solid #BDBDBD",
              width: "100%",
            }}
            aria-label="mui tabs example"
          >
            {factorsArray.map((factor, index) => (
              <Tab
                sx={{ fontSize: "12px", fontWeight: "700" }}
                key={index}
                label={factor}
                disabled={
                  userData?.role === "MDM Steward"
                    ? isEditMode && !isSubmitForApproval && index !== 0 && index !== 1 && index !== factorsArray.length-1
                    : userData?.role === "Sales"
                      ? isEditMode && !isSubmitForApproval && factor !== "Sales"
                      : userData?.role === "Procurement"
                        ? isEditMode &&
                        !isSubmitForApproval &&
                        factor !== "Purchasing"
                        : userData?.role === "Finance"
                          ? isEditMode &&
                          !isSubmitForApproval &&
                          factor !== "Accounting"
                          : ""
                }
              />
            ))}
          </Tabs>
          {/* )} */}
          {/* Display the cards of the currently active tab */}
          {tabContents &&
            tabContents[activeTab]?.map((cardContent, index) => (
              <Box key={index} sx={{ mb: 2, width: "100%" }}>
                <Typography variant="body2">{cardContent}</Typography>
              </Box>
            ))}
        </Grid>
      </Grid>

      {(checkIwaAccess(
        iwaAccessData,
        "Display Material",
        "Display Bottomnavigation"
      ) &&
        isEditMode &&
        taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
        materialData?.requestType === "Change") ||
        taskRowDetails?.requestType === "Change" ? (
        <Paper
          sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
          elevation={2}
        >
          <BottomNavigation
            className="container_BottomNav"
            showLabels
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              gap: 1,
            }}
            value={value}
            onChange={(newValue) => {
              setValue(newValue);
            }}
          >
            <>
              {userData?.role === "Super User" &&
                materialDetails[activeTab]?.category === "Basic Data" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForApprovalBasicData}
                  >
                    Submit for Approval
                  </Button>
                </>
              ) : userData?.role === "Super User" &&
                materialDetails[activeTab]?.category === "Sales" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForReviewSales}
                  >
                    Submit for Review
                  </Button>
                </>
              ) : userData?.role === "Super User" &&
                materialDetails[activeTab]?.category === "Purchasing" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForReviewPurchasing}
                  >
                    Submit for Review
                  </Button>
                </>
              ) : userData?.role === "Super User" &&
                materialDetails[activeTab]?.category === "Accounting" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForReviewAccounting}
                  >
                    Submit for Review
                  </Button>
                </>
              ) : userData?.role === "MDM Steward" &&
                materialDetails[activeTab]?.category === "Basic Data" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForApprovalBasicData}
                  >
                    Submit for Approval
                  </Button>
                </>
              ) : userData?.role === "MDM Steward" &&
                materialDetails[activeTab]?.category === "Sales" ? (
                <>
                  <Button size="small" variant="contained" onClick={onSave}>
                    Submit for Approval
                  </Button>
                </>
              ) : userData?.role === "MDM Steward" &&
                materialDetails[activeTab]?.category === "Purchasing" ? (
                <>
                  <Button size="small" variant="contained" onClick={onSave}>
                    Submit for Approval
                  </Button>
                </>
              ) : userData?.role === "MDM Steward" &&
                materialDetails[activeTab]?.category === "Accounting" ? (
                <>
                  <Button size="small" variant="contained" onClick={onSave}>
                    Submit for Approval
                  </Button>
                </>
              ) : userData?.role === "Sales" &&
                materialDetails[activeTab]?.category === "Sales" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForReviewSales}
                  >
                    Submit for Review
                  </Button>
                </>
              ) : userData?.role === "Finance" &&
                materialDetails[activeTab]?.category === "Accounting" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForReviewAccounting}
                  >
                    Submit for Review
                  </Button>
                </>
              ) : userData?.role === "Procurement" &&
                materialDetails[activeTab]?.category === "Purchasing" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForReviewPurchasing}
                  >
                    Submit for Review
                  </Button>
                </>
              ) : userData?.role === "Approver" &&
                materialDetails[activeTab]?.category === "Basic Data" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onApproveBasicData}
                  >
                    Approve
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={rereviewViewSelect}
                  >
                    Send Back
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={correctionViewDeselect}
                  >
                    OK
                  </Button>
                </>
              ) : userData?.role === "Approver" &&
                materialDetails[activeTab]?.category === "Sales" ? (
                <>
                  <Button size="small" variant="contained" onClick={onApprove}>
                    Approve
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={rereviewViewSelect}
                  >
                    Send Back
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={correctionViewDeselect}
                  >
                    OK
                  </Button>
                </>
              ) : userData?.role === "Approver" &&
                materialDetails[activeTab]?.category === "Purchasing" ? (
                <>
                  <Button size="small" variant="contained" onClick={onApprove}>
                    Approve
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={rereviewViewSelect}
                  >
                    Send Back
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={correctionViewDeselect}
                  >
                    OK
                  </Button>
                </>
              ) : userData?.role === "Approver" &&
                materialDetails[activeTab]?.category === "Accounting" ? (
                <>
                  <Button size="small" variant="contained" onClick={onApprove}>
                    Approve
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={rereviewViewSelect}
                  >
                    Send Back
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={correctionViewDeselect}
                  >
                    OK
                  </Button>
                </>
              ) : (
                ""
              )}
            </>
          </BottomNavigation>
        </Paper>
      ) : (checkIwaAccess(
        iwaAccessData,
        "Display Material",
        "Display Bottomnavigation"
      ) &&
        !isEditMode &&
        materialData?.requestType === "Change" && 
        taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN") ||
        taskRowDetails?.requestType === "Change" ? (
        <Paper
          sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
          elevation={2}
        >
          <BottomNavigation
            className="container_BottomNav"
            showLabels
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              gap: 1,
            }}
            value={value}
            onChange={(newValue) => {
              setValue(newValue);
            }}
          >
            <>
              {userData?.role === "Super User" &&
                materialDetails[activeTab]?.category === "Basic Data" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onApproveBasicData}
                  >
                    Approve
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={correctionViewSelect}
                  >
                    Send Back
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={correctionViewDeselect}
                  >
                    OK
                  </Button>
                </>
              ) : userData?.role === "Super User" &&
                materialDetails[activeTab]?.category === "Sales" ? (
                <>
                  <Button size="small" variant="contained" onClick={onSave}>
                    Submit for Approval
                  </Button>
                  <Button size="small" variant="contained" onClick={onApprove}>
                    Approve
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={correctionViewSelect}
                  >
                    Send Back
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={correctionViewDeselect}
                  >
                    OK
                  </Button>
                </>
              ) : userData?.role === "Super User" &&
                materialDetails[activeTab]?.category === "Purchasing" ? (
                <>
                  <Button size="small" variant="contained" onClick={onSave}>
                    Submit for Approval
                  </Button>
                  <Button size="small" variant="contained" onClick={onApprove}>
                    Approve
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={correctionViewSelect}
                  >
                    Send Back
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={correctionViewDeselect}
                  >
                    OK
                  </Button>
                </>
              ) : userData?.role === "Super User" &&
                materialDetails[activeTab]?.category === "Accounting" ? (
                <>
                  <Button size="small" variant="contained" onClick={onSave}>
                    Submit for Approval
                  </Button>
                  <Button size="small" variant="contained" onClick={onApprove}>
                    Approve
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={correctionViewSelect}
                  >
                    Send Back
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={correctionViewDeselect}
                  >
                    OK
                  </Button>
                </>
              ) : userData?.role === "MDM Steward" &&
                materialDetails[activeTab]?.category === "Sales" ? (
                <>
                  <Button size="small" variant="contained" onClick={onSave}>
                    Submit for Approval
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={correctionViewSelect}
                  >
                    Send Back
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={correctionViewDeselect}
                  >
                    OK
                  </Button>
                </>
              ) : userData?.role === "MDM Steward" &&
                materialDetails[activeTab]?.category === "Accounting" ? (
                <>
                  <Button size="small" variant="contained" onClick={onSave}>
                    Submit for Approval
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={correctionViewSelect}
                  >
                    Send Back
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={correctionViewDeselect}
                  >
                    OK
                  </Button>
                </>
              ) : userData?.role === "MDM Steward" &&
                materialDetails[activeTab]?.category === "Purchasing" ? (
                <>
                  <Button size="small" variant="contained" onClick={onSave}>
                    Submit for Approval
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={correctionViewSelect}
                  >
                    Send Back
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={correctionViewDeselect}
                  >
                    OK
                  </Button>
                </>
              ) : userData?.role === "Approver" &&
                materialDetails[activeTab]?.category === "Basic Data" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onApproveBasicData}
                  >
                    Approve
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={rereviewViewSelect}
                  >
                    Send Back
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={correctionViewDeselect}
                  >
                    OK
                  </Button>
                </>
              ) : userData?.role === "Approver" &&
                materialDetails[activeTab]?.category === "Sales" ? (
                <>
                  <Button size="small" variant="contained" onClick={onApprove}>
                    Approve
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={rereviewViewSelect}
                  >
                    Send Back
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={correctionViewDeselect}
                  >
                    OK
                  </Button>
                </>
              ) : userData?.role === "Approver" &&
                materialDetails[activeTab]?.category === "Accounting" ? (
                <>
                  <Button size="small" variant="contained" onClick={onApprove}>
                    Approve
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={rereviewViewSelect}
                  >
                    Send Back
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={correctionViewDeselect}
                  >
                    OK
                  </Button>
                </>
              ) : userData?.role === "Approver" &&
                materialDetails[activeTab]?.category === "Purchasing" ? (
                <>
                  <Button size="small" variant="contained" onClick={onApprove}>
                    Approve
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={rereviewViewSelect}
                  >
                    Send Back
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={correctionViewDeselect}
                  >
                    OK
                  </Button>
                </>
              ) : (
                ""
              )}
            </>
          </BottomNavigation>
        </Paper>
      ) : (checkIwaAccess(
        iwaAccessData,
        "Display Material",
        "Display Bottomnavigation"
      ) &&
        isEditMode &&
        materialData?.requestType === "Extend"
        && taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN") ||
        taskRowDetails?.requestType === "Extend" ? (
        <Paper
          sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
          elevation={2}
        >
          <BottomNavigation
            className="container_BottomNav"
            showLabels
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              gap: 1,
            }}
            value={value}
            onChange={(newValue) => {
              setValue(newValue);
            }}
          >
            <>
              {userData?.role === "Super User" &&
                materialDetails[activeTab]?.category === "Basic Data" ? (
                <></>
              ) : userData?.role === "Super User" &&
                materialDetails[activeTab]?.category === "Sales" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={saveAsDraftSalesExtend}
                  >
                    Save as Draft
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForReviewSalesExtend}
                  >
                    Submit for Review
                  </Button>
                </>
              ) : userData?.role === "Super User" &&
                materialDetails[activeTab]?.category === "Purchasing" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={saveAsDraftPurchasingExtend}
                  >
                    Save as Draft
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForReviewPurchasingExtend}
                  >
                    Submit for Review
                  </Button>
                </>
              ) : userData?.role === "Super User" &&
                materialDetails[activeTab]?.category === "Accounting" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={saveAsDraftAccountingExtend}
                  >
                    Save as Draft
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForReviewAccountingExtend}
                  >
                    Submit for Review
                  </Button>
                </>
              ) : userData?.role === "MDM Steward" &&
                materialDetails[activeTab]?.category === "Basic Data" ? (
                <>
                  {/* <Button
                    size="small"
                    variant="contained"
                    onClick={setOpenSelectCorrection(true)}
                  >
                    Correction
                  </Button> */}
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForApprovalExtend}
                  >
                    Submit for Approval
                  </Button>
                </>
              ) : userData?.role === "MDM Steward" &&
                materialDetails[activeTab]?.category === "Purchasing" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForApprovalExtend}
                  >
                    Submit for Approval
                  </Button>
                </>
              ) : userData?.role === "Approver" &&
                materialDetails[activeTab]?.category === "Basic Data" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                  // onClick={setOpenSelectCorrection(true)}
                  >
                    Correction
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onApproveExtend}
                  >
                    Approve
                  </Button>
                </>
              ) : userData?.role === "Sales" &&
                materialDetails[activeTab]?.category === "Sales" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={saveAsDraftSalesExtend}
                  >
                    Save as Draft
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForReviewSalesExtend}
                  >
                    Submit for Review
                  </Button>
                </>
              ) : userData?.role === "Finance" &&
                materialDetails[activeTab]?.category === "Accounting" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={saveAsDraftAccountingExtend}
                  >
                    Save as Draft
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForReviewAccountingExtend}
                  >
                    Submit for Review
                  </Button>
                </>
              ) : userData?.role === "Procurement" &&
                materialDetails[activeTab]?.category === "Purchasing" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={saveAsDraftPurchasingExtend}
                  >
                    Save as Draft
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForReviewPurchasingExtend}
                  >
                    Submit for Review
                  </Button>
                </>
              ) : (
                ""
              )}
            </>
          </BottomNavigation>
        </Paper>
      ) : (checkIwaAccess(
        iwaAccessData,
        "Display Material",
        "Display Bottomnavigation"
      ) &&
        !isEditMode &&
        materialData?.requestType === "Extend"
        && taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN") ||
        taskRowDetails?.requestType === "Extend" ? (
        <Paper
          sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
          elevation={2}
        >
          <BottomNavigation
            className="container_BottomNav"
            showLabels
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              gap: 1,
            }}
            value={value}
            onChange={(newValue) => {
              setValue(newValue);
            }}
          >
            <>
              {userData?.role === "Super User" &&
                materialDetails[activeTab]?.category === "Basic Data" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onApprovalSubmitExtend}
                  >
                    Submit for Approval
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onApproveExtend}
                  >
                    Approve
                  </Button>
                </>
              ) : userData?.role === "Super User" &&
                materialDetails[activeTab]?.category === "Sales" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                  // onClick={setOpenSelectCorrection(true)}
                  >
                    Correction
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onRerevewExtend}
                  >
                    Send Back
                  </Button>
                </>
              ) : userData?.role === "Super User" &&
                materialDetails[activeTab]?.category === "Purchasing" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                  // onClick={setOpenSelectCorrection(true)}
                  >
                    Correction
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onRerevewExtend}
                  >
                    Send Back
                  </Button>
                </>
              ) : userData?.role === "Super User" &&
                materialDetails[activeTab]?.category === "Accounting" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                  // onClick={setOpenSelectCorrection(true)}
                  >
                    Correction
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onRerevewExtend}
                  >
                    Send Back
                  </Button>
                </>
              ) : userData?.role === "MDM Steward" &&
                materialDetails[activeTab]?.category === "Basic Data" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onApprovalSubmitExtend}
                  >
                    Submit for Approval
                  </Button>
                </>
              ) : userData?.role === "MDM Steward" &&
                materialDetails[activeTab]?.category === "Sales" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                  // onClick={setOpenSelectCorrection(true)}
                  >
                    Correction
                  </Button>
                </>
              ) : userData?.role === "MDM Steward" &&
                materialDetails[activeTab]?.category === "Accounting" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                  // onClick={setOpenSelectCorrection(true)}
                  >
                    Correction
                  </Button>
                </>
              ) : userData?.role === "MDM Steward" &&
                materialDetails[activeTab]?.category === "Purchasing" ? (
                <>
                  <Button size="small" variant="contained">
                    Submit for Approval
                  </Button>
                  {/* <Button
                    size="small"
                    variant="contained"
                    onClick={setOpenSelectCorrection(true)}
                  >
                    Correction
                  </Button> */}
                </>
              ) : userData?.role === "Approver" &&
                materialDetails[activeTab]?.category === "Basic Data" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                  // onClick={onApproveExtend}
                  >
                    Approve
                  </Button>
                </>
              ) : userData?.role === "Approver" &&
                materialDetails[activeTab]?.category === "Sales" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                  // onClick={onRerevewExtend}
                  >
                    Send Back
                  </Button>
                </>
              ) : userData?.role === "Approver" &&
                materialDetails[activeTab]?.category === "Accounting" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                  // onClick={onRerevewExtend}
                  >
                    Send Back
                  </Button>
                </>
              ) : userData?.role === "Approver" &&
                materialDetails[activeTab]?.category === "Purchasing" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                  // onClick={onRerevewExtend}
                  >
                    Send Back
                  </Button>
                </>
              ) : (
                ""
              )}
            </>
          </BottomNavigation>
        </Paper>
      ) : (checkIwaAccess(
        iwaAccessData,
        "Display Material",
        "Display Bottomnavigation"
      ) &&
        !isEditMode &&
        materialData?.requestType === "Create" &&
        taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN") ||
        taskRowDetails?.requestType === "Create" ? (
        <>
        <Paper
          sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
          elevation={2}
        >
          <BottomNavigation
            className="container_BottomNav"
            showLabels
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              gap: 1,
            }}
            value={value}
            onChange={(newValue) => {
              setValue(newValue);
            }}
          >
            <>
              {userData?.role === "Super User" &&
                materialDetails[activeTab]?.category === "Basic Data" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onApproveBasicDataCreate}
                  >
                    Approve
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForApprovalBasicCreate}
                  >
                    Submit for Approval
                  </Button>
                  {/* <Button
                    size="small"
                    variant="contained"
                  // onClick={setOpenSelectCorrection(true)}
                  >
                    Correction
                  </Button> */}
                  <Button
                    size="small"
                    variant="contained"
                    onClick={correctionViewDeselect}
                  >
                    OK
                  </Button>
                </>
              ) : userData?.role === "Super User" &&
                materialDetails[activeTab]?.category === "Sales" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                  // onClick={setOpenSelectCorrection(true)}
                  >
                    Correction
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={correctionViewDeselect}
                  >
                    OK
                  </Button>
                </>
              ) : userData?.role === "Super User" &&
                materialDetails[activeTab]?.category === "Purchasing" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                  // onClick={setOpenSelectCorrection(true)}
                  >
                    Correction
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={correctionViewDeselect}
                  >
                    OK
                  </Button>
                </>
              ) : userData?.role === "Super User" &&
                materialDetails[activeTab]?.category === "Accounting" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                  // onClick={setOpenSelectCorrection(true)}
                  >
                    Correction
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={correctionViewDeselect}
                  >
                    OK
                  </Button>
                </>
              ) : userData?.role === "MDM Steward" &&
                factorsArray[activeTab] === "Attachments & Comments" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForApprovalAllViewsCreate}
                  >
                    Submit for Approval
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                  onClick={openCorrectionDialog}
                  >
                    Correction
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={correctionViewDeselect}
                  >
                    OK
                  </Button>
                </>
              ) : userData?.role === "Approver" &&
                factorsArray[activeTab]  === "Attachments & Comments" ? (
                <>
                  {viewNameArray.length === 1 ? (
                    <>
                      <Button
                        size="small"
                        variant="contained"
                        onClick={onApproveBasicDataCreateDialog}
                      >
                        Approve
                      </Button>

                      <Dialog 
                        hideBackdrop={false}
                        elevation={2}
                        PaperProps={{
                          sx: { boxShadow: "none", marginBottom: "8px", borderRadius: "8px"},
                        }}
                        open={openApproveBasicDataCreateDialog}
                        onClose={handleApproveBasicDataCreateDialog}
                      >
                        <DialogTitle
                          sx={{
                            justifyContent: "space-between",
                            alignItems: "center",
                            height: "max-content",
                            padding: ".5rem",
                            paddingLeft: "1rem",
                            backgroundColor: "#EAE9FF",
                            borderBottom: "3px ",
                            display: "flex",
                            marginBottom: "8px"
                          }}
                        >
                          <Typography variant="h6">Remarks</Typography>
                          
                          <IconButton
                            sx={{ width: "max-content" }}
                            onClick={handleApproveBasicDataCreateDialog}
                            children={<CloseIcon />}
                          />
                        </DialogTitle>
                        <DialogContent sx={{ padding: ".5rem 1rem" }}>
                        <Stack>
                            <Box sx={{ minWidth: 400 }}>
                              <FormControl sx={{ height: "auto" }} fullWidth>
                                <TextField
                                  sx={{ backgroundColor: "#F5F5F5"}}
                                  // value={inputText}
                                  value={remarks}
                                  onChange={handleRemarks}
                                  multiline
                                  placeholder={"Enter Remarks"}
                                  inputProps={{maxLength: 254}}
                                ></TextField>
                              </FormControl>
                            </Box>
                          </Stack>
                        </DialogContent>
                        <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
                          <Button
                            sx={{ width: "max-content", textTransform: "capitalize" }}
                            onClick={handleApproveBasicDataCreateDialog}
                            variant="outlined"
                          >
                            Cancel
                          </Button>
                          <Button
                            className="button_primary--normal"
                            type="save"
                            onClick={onApproveBasicDataCreate}
                            variant="contained"
                          >
                            Submit
                          </Button>
                        </DialogActions>
                      </Dialog>
                    </>
                  ) : (
                    <Button
                      size="small"
                      variant="contained"
                      onClick={onApproveAllViewsCreate}
                    >
                      Approve All View
                    </Button>
                  )}
                  <Button
                    size="small"
                    variant="contained"
                    onClick={openCorrectionDialog}
                  >
                    Correction
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={correctionViewDeselect}
                  >
                    OK
                  </Button>
                </>
              ) : userData?.role === "Sales" &&
                factorsArray[activeTab] === "Sales" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForReviewSalesCreate}
                  >
                    Submit For Review
                  </Button>
                </>
              ) : userData?.role === "Procurement" &&
                factorsArray[activeTab] === "Purchasing" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForReviewPurchasingCreate}
                  >
                    Submit For Review
                  </Button>
                </>
              ) : userData?.role === "Finance" &&
              factorsArray[activeTab] === "Accounting" ? (
              <>
                <Button
                  size="small"
                  variant="contained"
                  onClick={onSubmitForReviewPurchasingCreate}
                >
                  Submit For Review
                </Button>
              </>
            ) : (
                ""
              )}
            </>
          </BottomNavigation>
        </Paper>
        </>
      ) : checkIwaAccess(
        iwaAccessData,
        "Display Material",
        "Display Bottomnavigation"
      ) &&
        isEditMode && taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
        (taskRowDetails?.requestType === "Create" ||
          materialData?.requestType === "Create") ? (
        <Paper
          sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
          elevation={2}
        >
          <BottomNavigation
            className="container_BottomNav"
            showLabels
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              gap: 1,
            }}
            value={value}
            onChange={(newValue) => {
              setValue(newValue);
            }}
          >
            <>
              {userData?.role === "Super User" &&
                materialDetails[activeTab]?.category === "Basic Data" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForApprovalBasicCreate}
                  >
                    Submit for Approval
                  </Button>
                </>
              ) : userData?.role === "Super User" &&
                materialDetails[activeTab]?.category === "Sales" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForReviewSalesCreate}
                  >
                    Submit for Review
                  </Button>
                </>
              ) : userData?.role === "Super User" &&
                materialDetails[activeTab]?.category === "Purchasing" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForReviewPurchasingCreate}
                  >
                    Submit for Review
                  </Button>
                </>
              ) : userData?.role === "Super User" &&
                materialDetails[activeTab]?.category === "Accounting" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForReviewAccountingCreate}
                  >
                    Submit for Review
                  </Button>
                </>
              ) : userData?.role === "MDM Steward" &&
                materialDetails[activeTab]?.category === "Basic Data" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForApprovalBasicCreate}
                  >
                    Submit for Approval
                  </Button>
                </>
              ) : userData?.role === "Sales" &&
                materialDetails[activeTab]?.category === "Sales" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForReviewSalesCreate}
                  >
                    Submit for Review
                  </Button>
                </>
              ) : userData?.role === "Procurement" &&
                materialDetails[activeTab]?.category === "Purchasing" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForReviewPurchasingCreate}
                  >
                    Submit for Review
                  </Button>
                </>
              ) : userData?.role === "Finance" &&
                materialDetails[activeTab]?.category === "Accounting" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForReviewAccountingCreate}
                  >
                    Submit for Review
                  </Button>
                </>
              ) : (
                ""
              )}
            </>
          </BottomNavigation>
        </Paper>
      ) : checkIwaAccess(
        iwaAccessData,
        "Display Material",
        "Display Bottomnavigation"
      ) && taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" && isEditMode ? (
        <Paper
          sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
          elevation={2}
        >
          <BottomNavigation
            className="container_BottomNav"
            showLabels
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              gap: 1,
            }}
            value={value}
            onChange={(newValue) => {
              setValue(newValue);
            }}
          >
            <>
              {userData?.role === "Super User" &&
                materialDetails[activeTab]?.category === "Basic Data" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={saveAsDraftBasicData}
                  >
                    Save as Draft
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitBasicData}
                  >
                    Submit for Approval
                  </Button>
                </>
              ) : userData?.role === "Super User" &&
                materialDetails[activeTab]?.category === "Sales" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={saveAsDraftSales}
                  >
                    Save as Draft
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForReviewSales}
                  >
                    Submit for Review
                  </Button>
                </>
              ) : userData?.role === "Super User" &&
                materialDetails[activeTab]?.category === "Purchasing" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={saveAsDraftPurchasing}
                  >
                    Save as Draft
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForReviewPurchasing}
                  >
                    Submit for Review
                  </Button>
                </>
              ) : userData?.role === "Super User" &&
                materialDetails[activeTab]?.category === "Accounting" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={saveAsDraftAccounting}
                  >
                    Save as Draft
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForReviewAccounting}
                  >
                    Submit for Review
                  </Button>
                </>
              ) : userData?.role === "MDM Steward" &&
                materialDetails[activeTab]?.category === "Basic Data" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={saveAsDraftBasicData}
                  >
                    Save as Draft
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitBasicData}
                  >
                    Submit for Approval
                  </Button>
                </>
              ) : userData?.role === "Sales" &&
                materialDetails[activeTab]?.category === "Sales" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={saveAsDraftSales}
                  >
                    Save as Draft
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForReviewSales}
                  >
                    Submit for Review
                  </Button>
                </>
              ) : userData?.role === "Finance" &&
                materialDetails[activeTab]?.category === "Accounting" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={saveAsDraftAccounting}
                  >
                    Save as Draft
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForReviewAccounting}
                  >
                    Submit for Review
                  </Button>
                </>
              ) : userData?.role === "Procurement" &&
                materialDetails[activeTab]?.category === "Purchasing" ? (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={saveAsDraftPurchasing}
                  >
                    Save as Draft
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={onSubmitForReviewPurchasing}
                  >
                    Submit for Review
                  </Button>
                </>
              ) : (
                ""
              )}
            </>
          </BottomNavigation>
        </Paper>
      ) : (
        ""
      )}

      {/* </Stack> */}
    </div>
  );
};

export default DisplayMaterialDetail;
