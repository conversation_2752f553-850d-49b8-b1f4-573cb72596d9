import{s as De,q as v,r as c,a as l,j as i,aF as Ce,T as n,I as ke,aG as Me,W as ye,G as u,aq as d,ar as b,b6 as D,E as m,t as z,bF as V,bs as Pe,X as ve,V as ze,F as Ie,P as f,K as C,Z as k}from"./index-75c1660a.js";const je=({open:q,onClose:I})=>{const o=De(),t=v(e=>e.commonFilter.DuplicateDesc),N=v(e=>e.commonFilter.descriptionHeader),R=v(e=>e.commonFilter.NewMaterial);console.log("headerDesc",N);const[_,w]=c.useState(!1),[H,K]=c.useState([]),[X,Y]=c.useState([]),[Z,J]=c.useState([]),[we,Q]=c.useState([]);c.useState(!1);const[g,S]=c.useState([]),[F,T]=c.useState(!1),[ee,te]=c.useState(!1),[Te,$]=c.useState(!1),[se,j]=c.useState(!1),[le,ae]=c.useState(!1),M=()=>{I()},ie=e=>{var s=e;let a={...t,prefix:s};o(f({module:"DuplicateDesc",filterData:a}))},ne=(e,s)=>{{var a=s;let r={...t,group:a};o(f({module:"DuplicateDesc",filterData:r}))}},ce=(e,s)=>{{var a=s;let r={...t,baseUnit:a};o(f({module:"DuplicateDesc",filterData:r}))}},re=(e,s)=>{{var a=s;let r={...t,packagingMaterials:a};o(f({module:"DuplicateDesc",filterData:r}))}},ue=()=>{const e=a=>{K(a.body)},s=a=>{console.log(a)};C(`/${k}/data/getMatlGroup`,"get",e,s)},de=()=>{const e=a=>{Y(a.body)},s=a=>{console.log(a)};C(`/${k}/data/getBaseUom`,"get",e,s)},ge=()=>{const e=a=>{J(a.body)},s=a=>{console.log(a)};C(`/${k}/data/getMatGrpPack`,"get",e,s)},oe=e=>{if(e.target.value!==null){var s=e.target.value.toUpperCase();let a={...t,text:s};o(f({module:"DuplicateDesc",filterData:a}))}},xe=()=>{var a,r,h;const e=[t==null?void 0:t.prefix,(a=t==null?void 0:t.group)==null?void 0:a.code,(r=t==null?void 0:t.baseUnit)==null?void 0:r.code,(h=t==null?void 0:t.packagingMaterials)==null?void 0:h.code,t==null?void 0:t.text].join("-");let s={...R,description:e};o(f({module:"NewMaterial",filterData:s})),I()},fe=()=>{var h,L,U,E,O,G,W,B,A;(t==null?void 0:t.prefix)===""||((h=t==null?void 0:t.group)==null?void 0:h.code)===""||((L=t==null?void 0:t.baseUnit)==null?void 0:L.code)===""||((U=t==null?void 0:t.packagingMaterials)==null?void 0:U.code)===""||(t==null?void 0:t.text)===""?j(!0):j(!1);const e=[(E=t==null?void 0:t.group)==null?void 0:E.code,(O=t==null?void 0:t.baseUnit)==null?void 0:O.code,(G=t==null?void 0:t.packagingMaterials)==null?void 0:G.code].join("-"),s=[t==null?void 0:t.prefix,(W=t==null?void 0:t.group)==null?void 0:W.code,(B=t==null?void 0:t.baseUnit)==null?void 0:B.code,(A=t==null?void 0:t.packagingMaterials)==null?void 0:A.code,t==null?void 0:t.text.toLowerCase()].join("-");console.log("concatenatedValues",s),$(!0),te(!0);const a=x=>{Q(x.body),S(x.body),console.log("concatenatedValuesProbables",e);const pe=x.body.some(y=>{const p=y.split("-"),P=p.pop().toLowerCase();return p.join("-")===s.split("-").slice(0,-1).join("-")&&P.includes(t==null?void 0:t.text.toLowerCase())}),me=x.body.some(y=>{const p=y.split("-"),P=p.pop().toLowerCase();return p.join("-")===s.split("-").slice(0,-1).join("-")&&P===(t==null?void 0:t.text.toLowerCase())});T(!!w),w(pe),ae(me),$(!1)},r=x=>{console.log(x)};C(`/${k}/alter/fetchMatDescDupliChk?descriptionToCheck=${s}`,"get",a,r)},he=e=>e.split("-").pop().toLowerCase()===(t==null?void 0:t.text.toLowerCase());return c.useEffect(()=>{ue(),de(),ge()},[]),l(Ie,{children:i(ze,{open:q,onClose:M,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[i(Ce,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF",display:"flex"},children:[l(n,{variant:"h6",children:"Inputs"}),l(ke,{sx:{width:"max-content"},onClick:M,children:l(Me,{})})]}),i(ye,{sx:{padding:".5rem 1rem"},children:[i(u,{container:!0,spacing:1,children:[i(u,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[i(n,{sx:d,children:["Prefix",l("span",{style:{color:"red"},children:"*"})]}),l(b,{fullWidth:!0,children:l(D,{options:["INC"],sx:{height:"42px"},required:!0,size:"small",value:t==null?void 0:t.prefix,onChange:(e,s)=>ie(s),getOptionLabel:e=>e,renderOption:(e,s)=>l("li",{...e,children:l(n,{style:{fontSize:12},children:s})}),renderInput:e=>l(m,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"Select Prefix"})})})]}),i(u,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[i(n,{sx:d,children:["Material Group",l("span",{style:{color:"red"},children:"*"})]}),l(b,{fullWidth:!0,size:"small",children:l(D,{fullWidth:!0,size:"small",value:t==null?void 0:t.group,onChange:ne,options:H??[],getOptionLabel:e=>(e==null?void 0:e.code)===""||(e==null?void 0:e.desc)===""?"":`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`,renderOption:(e,s)=>l("li",{...e,children:l(n,{style:{fontSize:12},children:`${s==null?void 0:s.code} - ${s==null?void 0:s.desc}`})}),renderInput:e=>l(m,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"Select Material Group"})})})]}),i(u,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[i(n,{sx:d,children:["Base Unit",l("span",{style:{color:"red"},children:"*"})]}),l(b,{fullWidth:!0,size:"small",children:l(D,{fullWidth:!0,size:"small",value:t==null?void 0:t.baseUnit,onChange:ce,options:X??[],getOptionLabel:e=>(e==null?void 0:e.code)===""||(e==null?void 0:e.desc)===""?"":`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`,renderOption:(e,s)=>l("li",{...e,children:l(n,{style:{fontSize:12},children:`${s==null?void 0:s.code} - ${s==null?void 0:s.desc}`})}),renderInput:e=>l(m,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"Select Base Unit"})})})]}),i(u,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[i(n,{sx:d,children:["Material Group:Packaging Materials",l("span",{style:{color:"red"},children:"*"})]}),l(b,{fullWidth:!0,size:"small",children:l(D,{fullWidth:!0,size:"small",value:t==null?void 0:t.packagingMaterials,onChange:re,options:Z??[],getOptionLabel:e=>(e==null?void 0:e.code)===""||(e==null?void 0:e.desc)===""?"":`${e==null?void 0:e.code} - ${e==null?void 0:e.desc}`,renderOption:(e,s)=>l("li",{...e,children:l(n,{style:{fontSize:12},children:`${s==null?void 0:s.code} - ${s==null?void 0:s.desc}`})}),renderInput:e=>l(m,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"Select Packaging Materials"})})})]}),i(u,{item:!0,md:6,children:[i(n,{sx:d,children:["Text",l("span",{style:{color:"red"},children:"*"})]}),l(m,{sx:{fontSize:"12px !important"},size:"small",fullWidth:!0,onChange:oe,placeholder:"Enter Text",value:t==null?void 0:t.text,inputProps:{maxLength:22}})]}),l(u,{item:!0,md:6,sx:{display:"flex",justifyContent:"flex-start",alignItems:"center",marginTop:"17px"},children:l(z,{onClick:fe,variant:"contained",children:"Check"})})]}),ee&&(g==null?void 0:g.length)===0?l(V,{sx:{justifyContent:"flex-start",borderRadius:"4px",color:"#000",minWidth:"5rem",fontSize:"12px",margin:"5px",backgroundColor:"#d2f5b3"},label:l(n,{sx:d,children:"No probables found. You can proceed."})}):_&&(g==null?void 0:g.length)>0&&l(u,{container:!0,sx:{paddingTop:"12px"},children:l(u,{item:!0,children:i(Pe,{children:[l(n,{sx:d,children:"Probables"}),g.map((e,s)=>l(V,{sx:{justifyContent:"flex-start",borderRadius:"4px",color:"#000",minWidth:"5rem",fontSize:"12px",margin:"5px",backgroundColor:he(e)?"#ea9999":"#f6f181"},label:l(n,{sx:d,children:e})},s))]})})}),se&&l(u,{children:l(n,{style:{color:"red"},children:"Please fill in all required fields marked with *."})})]}),i(ve,{children:[l(z,{onClick:M,children:"Cancel"}),l(z,{onClick:xe,disabled:!F||le,variant:"contained",children:"Proceed"})]})]})})};export{je as D};
