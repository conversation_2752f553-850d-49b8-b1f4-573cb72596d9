import React, { useEffect, useState } from "react";
import SearchBar from "../common/SearchBar";
import { ToggleButton, ToggleButtonGroup } from "@mui/material";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import DescriptionIcon from "@mui/icons-material/Description";
import useLogger from "@hooks/useLogger";
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  Grid,
  IconButton,
  Stack,
  Table,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,

  Tooltip,
  Typography,
} from "@mui/material";

import {
  container_table,
  outerContainer_Information,
  outermostContainer_Information,
  outermostContainer,
} from "../common/commonStyles";
import {
  ArrowCircleLeftOutlined,
  IosShare,
  Refresh,
} from "@mui/icons-material";
import { doAjax } from "../common/fetchService";
import { destination_MaterialMgmt } from "../../destinationVariables";
import { useDispatch } from "react-redux";
import { commonSearchBarClear } from "../../app/commonSearchBarSlice";
import { saveExcel } from "../../functions";
import CloseIcon from "@mui/icons-material/Close";
import DownloadIcon from "@mui/icons-material/Download";
import LoadingComponent from "../common/LoadingComponent";
import { useLocation, useNavigate } from "react-router-dom";
import ErrorExcelTable from "./ErrorExcelTable";
import { END_POINTS } from "@constant/apiEndPoints";
import { colors } from "@constant/colors";
import { ERROR_MESSAGES } from "@constant/enum";
const ErrorHistory = () => {
  const { customError } = useLogger()
  const getStatusColor = (status) => {
    return status === "Resolved"
      ? "success"
      : status === "Pending"
      ? "warning"
      : "error";
  };

  const formatDate = (timestamp) => new Date(timestamp).toLocaleDateString();
  const formatTime = (timestamp) => new Date(timestamp).toLocaleTimeString();
  const [errorType, setErrorType] = useState("sap");

  const handleChange = (event, newErrorType) => {
    if (newErrorType !== null) {
      setErrorType(newErrorType);
    }
  };
  const dispatch = useDispatch()
  const location = useLocation();
  const urlSearchParams = new URLSearchParams(location.search.split("?")[1]);
  const RequestID = urlSearchParams.get("RequestId");
  const navigate = useNavigate();
  const [massLogData, setMassLogData] = useState([]);
  const [massSearchData, setMassSearchData] = useState([]);
  const [loading, setLoading] = useState(false);
  const display = location.state?.display ?? false;
  const childRequest = location.state?.childRequest ?? false;
  const handleMassHistory = () => {
    const hSuccess = (data) => {
      setMassLogData(data?.body);
      setMassSearchData(data?.body);
      setPageCount(data?.count ?? data?.body?.length);
      setLoading(false);
      dispatch(commonSearchBarClear({ module: "ErrorHistory" }));
    };
    
    const hError = (error) => {
      customError(error);
      setLoading(false);
    };
    
    setLoading(true);
    if(childRequest){
      doAjax(
        `/${destination_MaterialMgmt}/${END_POINTS.ERROR_HISTORY.ERROR_LOG_CHILD}?childRequestId=${RequestID.slice(
          3
        )}`,
        "get",
        hSuccess,
        hError
      );
    }
    else{
      doAjax(
      `/${destination_MaterialMgmt}/${END_POINTS.ERROR_HISTORY.ERROR_LOG}?requestId=${RequestID.slice(
        3
      )}`,
      "get",
      hSuccess,
      hError
    );
  }
  };

  const [showMore, setShowMore] = useState({});
 
  const toggleShowMore = (key) => {
    setShowMore((prev) => ({ ...prev, [key]: !prev[key] }));
  };

  useEffect(() => {
    handleMassHistory();
  }, []);
  const renderValue = (val) => {
    let value = val?.sapMessage;
    if (!value && val?.materialDuplicateError) {
      if (val.materialDuplicateError.includes('$^$')) {
        const [errorMsg, requestId] = val.materialDuplicateError.split('$^$');
        value = `${errorMsg} - This material is already being processed in request ${requestId}`;
      } else {
        value = val.materialDuplicateError;
      }
    }

    if (!value && !val?.materialDuplicateError) {
      return null;
    }

    const isHTML = typeof value === "string" && /<[a-z][\s\S]*>/i.test(value);
    const isSuccess = value === "Validated Successfully" || value === "Syndicated In SAP";
    const materialKey = val?.materialNo;
 
 
    return (
      <Card
        variant="outlined"
        sx={{
          width: "100%",
          minHeight: 100,
          backgroundColor: `${colors.basic.lighterGrey}`,
          borderRadius: 2,
          boxShadow: "0px 2px 6px rgba(0, 0, 0, 0.08)",
          padding: 1,
          margin: "10px 0",
          overflow:'hidden',
        }}
      >
        <CardContent sx={{ padding: 1}}>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={1.5}>
            <Typography
              fontWeight="bold"
              color={isSuccess ? "success.main" : "error.main"}
              sx={{ fontSize: "0.9rem" }}
            >
              Material No. {materialKey}
            </Typography>
            <Chip
              label={isSuccess ? "Success" : "Error"}
              color={isSuccess ? "success" : "error"}
              size="small"
              sx={{ fontWeight: "medium", height: "21px",fontSize:'0.65rem' }}
            />
          </Box>
 
          {value ? (
            <Box sx={{ mt: 1, position: "relative" }}>
              {isHTML ? (
                <div
                  dangerouslySetInnerHTML={{ __html: value }}
                  style={{
                    fontSize: "0.75rem",
                    lineHeight: 1.1,
                    wordBreak: "break-word",
                    overflowWrap: "break-word",
                    width: "100%",
                    overflow:'hidden',
                    display: "-webkit-box",
                    WebkitLineClamp: showMore[materialKey] ? "unset" : 1,
                    WebkitBoxOrient: "vertical",
                  textOverflow: "ellipsis",
                  whiteSpace: "normal",
                  mb:2,
                  }}
                />
              ) : (
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    fontSize: "0.75rem",
                    lineHeight: 1.1,
                    wordBreak: "break-word",
                    overflowWrap: "break-word",
                    width: "100%",
                    overflow:'hidden',
                    display: "-webkit-box",
                    WebkitLineClamp: showMore[materialKey] ? "unset" : 1,
                    WebkitBoxOrient: "vertical",
                  textOverflow: "ellipsis",
                  whiteSpace: "normal",
                  mb:2,
                  }}
                >
                  {value}
                </Typography>
              )}
              <Button
                size="small"
                onClick={() => toggleShowMore(materialKey)}
                sx={{
                  fontSize: "0.75rem",
      
                  minWidth: "auto",
                  textTransform: "none",
                  color: "primary.main",
                  position: "absolute",
                  bottom: "-30px",
                  left: '-10px',
                }}
              >
                {showMore[materialKey] ? "Show Less" : "Show More"}
              </Button>
            </Box>
          ) : (
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{
                fontSize: "0.875rem",
                mt: 1,
              }}
            >
              No Error Found!
            </Typography>
          )}
        </CardContent>
      </Card>
    );
  };

  const handleSearchAction = (value) => {
    if (Boolean(value)) {
      const searchTerm = value.toString().toLowerCase();
      const filterData = massLogData.filter((val) => 
        val?.materialNo?.toString().toLowerCase().includes(searchTerm)
      );
      setMassSearchData(filterData);

    } else {
      handleMassHistory();
    }
  };
  const columns = [
    {
      field: "sapMessage",
      headerName: "SAP Error Log",

      align: "center",
      headerAlign: "center",
      flex: 3,
      renderCell: (params) => (
        <Box sx={{width:'100%'}}>{renderValue(params?.row)}</Box>
      ),
    },
  ];
  const handleChangeRowsPerPage = (event) => {
    setPageSize(parseInt(event.target.value, 10));
    setPageNumber(0);
  };
  const handleChangePage = (event, newPage) => {
    setPageNumber(newPage);
  };
  const [pageCount, setPageCount] = useState(0);

  const [pageNumber, setPageNumber] = useState(0);
  const [pageSize, setPageSize] = useState(10);

  const startIndex = pageNumber * pageSize;
  const endIndex = startIndex + pageSize;
  const displayedData = massSearchData?.slice(startIndex, endIndex);

  const hasNoErrorData = () => {
    return massSearchData.every(item => 
      (!item?.sapMessage && !item?.materialDuplicateError)
    );
  };

  const functions_ExportAsExcel = {
    convertJsonToExcel: () => {
      let excelColumns = [];
      const columnsLocal = [
        {
          field: "materialNo",
          headerName: "Material Number",
        },
        {
          field: "sapMessage",
          headerName: "SAP Error Log",
        },
      ];

      columnsLocal.forEach((item) => {
        if (item.headerName.toLowerCase() !== "action" && !item.hide) {
          excelColumns.push({ header: item.headerName, key: item.field });
        }
      });
      const presentDate = new Date();
      saveExcel({
        fileName: `Material Error Logsheet`,
        columns: excelColumns,
        rows: massLogData,
      });
    },
    button: () => {
      return (
        <Button
          sx={{
            textTransform: "capitalize",
            position: "absolute",
            right: 0,
            top: 0,
          }}
          onClick={() => functions_ExportAsExcel.convertJsonToExcel()}
        >
          Download
        </Button>
      );
    },
  };

  const handleDownload = () => {
    const hSuccess = (response) => {
      const type = RequestID.substring(0, 8);
      const docName = {
        BPGNLCHG: "Mass_BP_Change_Template.xls",
        VCOCOCHG: "Mass_Vendor_CompCode_Change_Template.xls",
        PUORGCHG: "Mass_Purchase_Org_Change_Template.xls",
        CCOCOEXT: "Mass_Customer_CompCode_Change_Template.xls",
        SAORGCHG: "Mass_Sales_Org_Change_Template.xls",
      };
      
      let binaryData = atob(response?.excelFile);

      let byteArray = new Uint8Array(binaryData.length);
      for (let i = 0; i < binaryData.length; i++) {
        byteArray[i] = binaryData.charCodeAt(i);
      }

      let blob = new Blob([byteArray], { type: "application/vnd.ms-excel" });
      let link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = docName[type];
      link.click();
    };
    const hError = (error) => {
      console.error("File upload failed:", error);
    };
    doAjax(
      `/${destination_BP}/${END_POINTS.ERROR_HISTORY.DOWNLOAD_EXCEL_BP_ERROR}?requestId=${RequestID}`,
      "post",
      hSuccess,
      hError
    );
  };
  return (
    <div id={"container_outermost"}>
      <div
        className="purchaseOrder"
        style={{ ...outermostContainer, backgroundColor: `${colors.primary.veryLight}` }}
      >
        <Stack spacing={1}>
          <>
            <Grid
              container
              sx={outermostContainer_Information}
              alignItems="center"
            >
              <Grid
                item
                md={6}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  ...outerContainer_Information,
                }}
              >
                <Stack direction="row" spacing={2} alignItems="center">
                  <IconButton
                    onClick={() => {
                      if (display) {
                        navigate(-1);
                      } else {
                        navigate("/requestBench");
                      }
                    }}
                    color="primary"
                    aria-label="upload picture"
                    component="label"
                  >
                    <ArrowCircleLeftOutlined
                      sx={{ fontSize: "25px", color: "#000000" }}
                    />
                  </IconButton>
                  <Stack>
                    <Typography variant="h3">
                      <strong>Error History - {RequestID}</strong>
                    </Typography>
                    <Typography variant="body2" color="#777">
                      This view displays the error history of a Request
                    </Typography>
                  </Stack>
                </Stack>
              </Grid>

              {massLogData?.length > 0 && (
                <Grid
                  item
                  md={6}
                  sx={{
                    display: "flex",
                    justifyContent: "flex-end",
                    alignItems: "center",
                  }}
                >
                  <Grid
                    container
                    direction="row"
                    justifyContent="flex-end"
                    alignItems="center"
                    spacing={0}
                    marginBottom={"10px"}
                  >
                    <Tooltip
                      slotProps={{ tooltip: { sx: { fontSize: "0.9em" } } }}
                      title="Reload"
                      placement="bottom"
                      arrow
                    >
                      <IconButton onClick={handleMassHistory}>
                        <Refresh />
                      </IconButton>
                    </Tooltip>
                    <Tooltip
                      slotProps={{ tooltip: { sx: { fontSize: "0.9em" } } }}
                      title="Export Table"
                      placement="bottom"
                      arrow
                    >
                      <IconButton
                        onClick={functions_ExportAsExcel.convertJsonToExcel}
                      >
                        <IosShare />
                      </IconButton>
                    </Tooltip>
                    <Tooltip
                      slotProps={{ tooltip: { sx: { fontSize: "0.9em" } } }}
                      title="Search"
                    >
                      <SearchBar
                        title="Type Material Number to know its status"
                        handleSearchAction={(e) => handleSearchAction(e)}
                        keyName="errorHistorySearch"
                        message={"Search"}
                        module="ErrorHistory"
                        clearSearchBar={() => handleMassHistory()}
                      />
                    </Tooltip>
                  </Grid>
                </Grid>
              )}
            </Grid>

            <Stack
              sx={{
                padding: "16px",
                pb: "0 !important",
                width: "100%",
                maxWidth: "100%",
                ...container_table,
              }}
            >
              <ToggleButtonGroup
                value={errorType}
                exclusive
                onChange={handleChange}
                sx={{
                  width: "40%",
                  "& .MuiToggleButton-root": {
                    borderRadius: "0 !important", 
                  },
                  "& .MuiToggleButton-root:first-of-type": {
                    borderTopLeftRadius: "8px !important",
                    borderBottomLeftRadius: "8px !important",
                  },
                  "& .MuiToggleButton-root:last-of-type": {
                    borderTopRightRadius: "8px !important",
                    borderBottomRightRadius: "8px !important",
                  },
                }}
              >
                <ToggleButton
                  value="sap"
                  sx={{
                    flex: 1,
                    p: 1,
                    color: errorType === "sap" ? `${colors.reportTile.blue}` : `${colors.primary.grey}`,
                    backgroundColor:
                      errorType === "sap" ? `${colors.reportTile.lightBlue}` : "transparent",
                    "&:hover": { backgroundColor: `${colors.reportTile.lightBlue}` },
                  }}
                >
                  <ErrorOutlineIcon sx={{ fontSize: 18, mr: 1 }} />
                  SAP Error Log
                </ToggleButton>

                <ToggleButton
                  value="excel"
                  sx={{
                    flex: 1,
                    p: 1,
                    color: errorType === "excel" ? `${colors.error.critical}` : `${colors.primary.grey}`,
                    backgroundColor:
                      errorType === "excel" ? `${colors.reportTile.lightred}` : "transparent",
                    "&:hover": { backgroundColor: `${colors.reportTile.lightred}` },
                  }}
                >
                  <DescriptionIcon sx={{ fontSize: 18, mr: 1 }} />
                  Excel Upload Error
                </ToggleButton>
              </ToggleButtonGroup>

              {errorType === "sap" && massLogData?.length > 0 ? (
                <div
                  id="container_outermost"
                  style={{
                    backgroundColor: `${colors.basic.white}`,
                    borderRadius: "8px",
                    boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                    mt: 2,
                  }}
                >
                  <TableContainer
                    style={{
                      marginTop: "16px",
                      border: "1px solid #e0e0e0",
                      borderRadius: "8px",
                      height: "calc(100vh - 289px)",
                      overflow: "auto",
                      backgroundColor: `${colors.basic.white}`,
                    }}
                  >
                    <Table stickyHeader>
                      <TableHead>
                        <TableRow>
                          {columns.map((column) => (
                            <TableCell
                              key={column.field}
                              style={{
                                minWidth: column.minWidth,
                                backgroundColor: `${colors.background.default}`,
                                padding: "12px 16px",
                                borderBottom: "1px solid #e0e0e0",
                                fontWeight: 600,
                                color: `${colors.black.light}`,
                              }}
                            >
                              <strong>{column.headerName}</strong>
                            </TableCell>
                          ))}
                        </TableRow>
                      </TableHead>
                      {displayedData.length > 0 ? (
                        hasNoErrorData() ? (
                          <TableRow>
                            <TableCell
                              colSpan={columns.length}
                              sx={{
                                padding: "24px",
                                textAlign: "center",
                                borderBottom: "none",
                                color: "#777",
                              }}
                            >
                              <Typography
                                sx={{
                                  whiteSpace: "wrap",
                                  overflow: "hidden",
                                  textOverflow: "ellipsis",
                                  maxWidth: 320,
                                  fontSize: "0.875rem",
                                  fontWeight: 500,
                                }}
                              >
                                {ERROR_MESSAGES.NO_DATA_AVAILABLE}
                              </Typography>
                            </TableCell>
                          </TableRow>
                        ) : (
                          Array(Math.ceil(displayedData.length / 2))
                            .fill()
                            .map((_, pairIndex) => {
                              const firstRowIndex = pairIndex * 2;
                              const secondRowIndex = firstRowIndex + 1;
                              const firstRow = displayedData[firstRowIndex];
                              const secondRow =
                                secondRowIndex < displayedData.length
                                  ? displayedData[secondRowIndex]
                                  : null;

                              const firstRowContent = renderValue(firstRow);
                              const secondRowContent = secondRow ? renderValue(secondRow) : null;
                              
                              if (!firstRowContent && !secondRowContent) {
                                return null;
                              }

                              return (
                                <TableRow
                                  key={firstRowIndex}
                                  sx={{
                                    "&:hover": { backgroundColor: `${colors.basic.lighterGrey}` },
                                    transition:
                                      "background-color 0.2s ease-in-out",
                                  }}
                                >
                                  {columns.map((column) => (
                                    <TableCell
                                      key={column.field}
                                      sx={{
                                        padding: "18px 18px",
                                        borderBottom: "1px solid #e0e0e0",
                                        color: `${colors.black.light}`,
                                      }}
                                    >
                                      <Box
                                        sx={{
                                          display: "flex",
                                          flexDirection: "row",
                                          justifyContent:
                                            displayedData.length >= 2
                                              ? "space-evenly"
                                              : "flex-start",
                                          gap: 2,
                                        }}
                                      >
                                        {firstRowContent && (
                                          <Typography
                                            sx={{
                                              whiteSpace: "nowrap",
                                              overflow: "hidden",
                                              textOverflow: "ellipsis",
                                              width: "100%",
                                              fontSize: "0.875rem",
                                              fontWeight: 500,
                                            }}
                                          >
                                            {column.renderCell
                                              ? column.renderCell({ row: firstRow })
                                              : firstRow[column.field]}
                                          </Typography>
                                        )}
                                        {secondRowContent && secondRow && (
                                          <Typography
                                            sx={{
                                              whiteSpace: "nowrap",
                                              overflow: "hidden",
                                              textOverflow: "ellipsis",
                                              width: "100%",
                                              fontSize: "0.875rem",
                                              color: "#666",
                                            }}
                                            >
                                            {column.renderCell
                                              ? column.renderCell({
                                                  row: secondRow,
                                                })
                                              : secondRow[column.field]}
                                          </Typography>
                                        )}
                                      </Box>
                                    </TableCell>
                                  ))}
                                </TableRow>
                              );
                            })
                        )
                      ) : (
                        <TableRow>
                          <TableCell
                            colSpan={columns.length}
                            sx={{
                              padding: "24px",
                              textAlign: "center",
                              borderBottom: "none",
                              color: "#777",
                            }}
                          >
                            <Typography
                              sx={{
                                whiteSpace: "wrap",
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                maxWidth: 320,
                                fontSize: "0.875rem",
                                fontWeight: 500,
                              }}
                            >
                              No data found
                            </Typography>
                          </TableCell>
                        </TableRow>
                      )}
                    </Table>
                  </TableContainer>
                  <TablePagination
                    component="div"
                    count={pageCount}
                    page={pageNumber}
                    onPageChange={handleChangePage}
                    rowsPerPage={pageSize}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                    style={{
                      borderTop: "1px solid #e0e0e0",
                      backgroundColor: `${colors.basic.white}`,
                      padding: "8px 16px",
                    }}
                  />
                </div>
              ) : (
                errorType !== "excel" && (
                  <Card
                    sx={{
                      width: "100%",
                      marginTop: "16px",
                      border: "1px solid #e0e0e0",
                      borderRadius: "8px",
                      boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                    }}
                  >
                    <CardContent style={{ padding: "24px", mt: 1 }}>
                      <Stack spacing={2} alignItems="center">
                        <Typography
                          variant="h5"
                          style={{
                            color: `${colors.black.light}`,
                            fontWeight: 500,
                          }}
                        >
                          No Data Found for this Request ID
                        </Typography>
                      </Stack>
                    </CardContent>
                  </Card>
                )
              )}
              {errorType === "excel" && <ErrorExcelTable />}
            </Stack>

            {loading && <LoadingComponent />}
          </>
        </Stack>
      </div>
    </div>
  );
};

export default ErrorHistory;
