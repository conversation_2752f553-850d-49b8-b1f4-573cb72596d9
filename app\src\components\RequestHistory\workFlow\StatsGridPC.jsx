import StatCardPC from './StatCardPC';

const StatsGridPC = ({ data }) => {
  console.log("datagrid", data);

  let totalTasks = 0;
  let totalGroups = Object.keys(data).length;
  let slaSum = 0;
  let taskCount = 0;

  Object.values(data).forEach(group => {
    // 1. Requestor Task
    totalTasks += 1;
    slaSum += group.requestor_sla || 0;
    taskCount += 1;

    // 2. Workflow Tasks by level
    group.workflowTaskDetailsByLevel?.forEach(levelObj => {
      const tasksAtLevel = Object.values(levelObj)[0]; // get the array inside
      totalTasks += tasksAtLevel.length;
      taskCount += tasksAtLevel.length;
      tasksAtLevel.forEach(task => {
        slaSum += task.taskSla || 0;
      });
    });

    // 3. MDM Task
    totalTasks += 1;
    slaSum += group.mdmApprover_sla || 0;
    taskCount += 1;
  });

  const avgSLA = taskCount ? (slaSum / taskCount).toFixed(1) : 0;

  return (
    <div
      style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(180px, 1fr))',
        gap: 16,
        margin: '16px 0',
        padding: 8,
        borderRadius: 8,
      }}
    >
      <StatCardPC number={totalGroups} label="Workflow Groups" />
      <StatCardPC number={totalTasks} label="Total Tasks" />
      <StatCardPC number={avgSLA} label="Avg SLA (Days)" />
    </div>
  );
};

export default StatsGridPC;
