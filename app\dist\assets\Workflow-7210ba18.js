import{m as Y,n as K,o as X,r as se,uw as Re}from"./index-17b8d91e.js";import{q as Oe,s as $e,t as Ae,v as Be,w as ve,n as ze,a1 as ge,x as He,W as qe,o as Ue}from"./react-beautiful-dnd.esm-0e527744.js";import{I as Ce,r as Ve,j as Ge,A as Je,a as _e,S as Ye,b as Ke}from"./propData-c3ca5892.js";import{r as Xe}from"./index.esm-be84327e.js";var Qe={},ke={},Q={},Ze=K;Object.defineProperty(Q,"__esModule",{value:!0});Q.default=void 0;var et=Ze(Y()),G=X;Q.default=(0,et.default)([(0,G.jsx)("path",{d:"M12 4c-4.42 0-8 3.58-8 8s3.58 8 8 8 8-3.58 8-8-3.58-8-8-8m-5 9.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5m5 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5m5 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5",opacity:".3"},"0"),(0,G.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8"},"1"),(0,G.jsx)("circle",{cx:"7",cy:"12",r:"1.5"},"2"),(0,G.jsx)("circle",{cx:"12",cy:"12",r:"1.5"},"3"),(0,G.jsx)("circle",{cx:"17",cy:"12",r:"1.5"},"4")],"PendingTwoTone");var Z={},tt=K;Object.defineProperty(Z,"__esModule",{value:!0});Z.default=void 0;var at=tt(Y()),xe=X;Z.default=(0,at.default)([(0,xe.jsx)("path",{d:"M12 4c-4.41 0-8 3.59-8 8s3.59 8 8 8 8-3.59 8-8-3.59-8-8-8m-2 13-4-4 1.41-1.41L10 14.17l6.59-6.59L18 9z",opacity:".3"},"0"),(0,xe.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8m4.59-12.42L10 14.17l-2.59-2.58L6 13l4 4 8-8z"},"1")],"CheckCircleTwoTone");var ee={},lt=K;Object.defineProperty(ee,"__esModule",{value:!0});ee.default=void 0;var nt=lt(Y()),pe=X;ee.default=(0,nt.default)([(0,pe.jsx)("path",{d:"M12 4c-4.41 0-8 3.59-8 8s3.59 8 8 8 8-3.59 8-8-3.59-8-8-8m5 11.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12z",opacity:".3"},"0"),(0,pe.jsx)("path",{d:"M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2m0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8m3.59-13L12 10.59 8.41 7 7 8.41 10.59 12 7 15.59 8.41 17 12 13.41 15.59 17 17 15.59 13.41 12 17 8.41z"},"1")],"CancelTwoTone");var te={},it=K;Object.defineProperty(te,"__esModule",{value:!0});te.default=void 0;var rt=it(Y()),Ee=X;te.default=(0,rt.default)([(0,Ee.jsx)("path",{d:"M12 4c-4.41 0-8 3.59-8 8s3.59 8 8 8 8-3.59 8-8-3.59-8-8-8M7 7h7v2H7zm0 3h7v2H7zm3 5H7v-2h3zm4.05 3.36-2.83-2.83 1.41-1.41 1.41 1.41L17.59 12 19 13.41z",opacity:".3"},"0"),(0,Ee.jsx)("path",{d:"M12 4c4.41 0 8 3.59 8 8s-3.59 8-8 8-8-3.59-8-8 3.59-8 8-8m0-2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m2 8H7v2h7zm0-3H7v2h7zm-7 8h3v-2H7zm12-1.59L17.59 12l-3.54 3.54-1.41-1.41-1.41 1.41 2.83 2.83z"},"1")],"PlaylistAddCheckCircleTwoTone");var Te={};(function(C){Object.defineProperty(C,"__esModule",{value:!0}),C.default=void 0;var c=Xe;function t(o){"@babel/helpers - typeof";return t=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(u){return typeof u}:function(u){return u&&typeof Symbol=="function"&&u.constructor===Symbol&&u!==Symbol.prototype?"symbol":typeof u},t(o)}function _(o,u,h){return(u=R(u))in o?Object.defineProperty(o,u,{value:h,enumerable:!0,configurable:!0,writable:!0}):o[u]=h,o}function R(o){var u=g(o,"string");return t(u)=="symbol"?u:u+""}function g(o,u){if(t(o)!="object"||!o)return o;var h=o[Symbol.toPrimitive];if(h!==void 0){var y=h.call(o,u||"default");if(t(y)!="object")return y;throw new TypeError("@@toPrimitive must return a primitive value.")}return(u==="string"?String:Number)(o)}var O=(0,c.makeStyles)(function(o){return{connectorLineCol:{minHeight:70,margin:"5px auto !important",borderRightWidth:"1.2 !important",backgroundColor:"#3026B9",opacity:.6},connectorHeaderLineCol:{minHeight:30,margin:"5px auto !important",borderRightWidth:"1.2 !important",backgroundColor:"#3026B9",opacity:.6},subjectLine:{color:o.palette.text.primary,fontSize:"14px"},endLineText:{color:"#757575",fontSize:"14px"},subTextLine:{color:o.palette.text.secondary,fontSize:"10px"},SelectedSubTextLine:{color:o.palette.text.primary,fontSize:"10px"},avatarSize:{height:"27px !important",width:"27px !important",fontSize:12},avatarContainerSize:{height:"25px !important",width:"25px !important"},statusButtonStyle:{width:"fit-content",padding:"7px 10px",background:"rgba(255, 255, 255, 0.36)",border:"1px solid #EEEEEE",boxShadow:"0px 1px 1px rgb(0 0 0 / 7%)",borderRadius:"4px"},selectedStatusButtonStyle:{width:"fit-content",padding:"7px 10px",background:"rgba(255, 255, 255, 0.9)",border:"1px solid #EEEEEE",boxShadow:"0px 1px 1px rgb(0 0 0 / 7%)",borderRadius:"4px"},nextProcessText:{border:"2px dashed #B71C1C",borderRadius:"4px",color:"#757575",fontSize:"14px"},hoverDiv:{cursor:"pointer",width:"100%",padding:"3px 3px 5px 8px","&:hover":{backgroundColor:"rgba(29, 29, 17, 0.08)",borderRadius:"3px"}},selectedDiv:{width:"100%",background:"linear-gradient(180.76deg, #C1DCFF -113.11%, rgba(255, 255, 255, 0) 198.03%)!important",borderRadius:"3px"},flowableContainer:{width:"100%"},flowableContainerWithLog:{width:"50%"},flowableImageWidth:{width:"50%"},flowableImageWidthWithLog:{width:"80%"},flowableImageContainer:_(_(_(_({margin:"5px",border:"1px solid #EAE9FF",borderRadius:"4px",background:"#fff",boxShadow:"0px 0px 16px rgb(207 207 207 / 25%), 0px 0px 8px rgb(255 252 252 / 25%), 0px 0px 4px rgb(249 249 249 / 25%), 0px 0px 2px #e0e0e0",height:"14rem",overflow:"hidden",cursor:"pointer",position:"relative",padding:0},"margin","0.5em"),"backgroundRepeat","no-repeat"),"backgroundPosition","center"),"backgroundSize","auto"),flowableActions:{padding:"5px",height:"45px"},flowableDetails:{position:"relative",backgroundSize:"cover",backgroundColor:"#FAFCFF",borderRadius:"4px",height:"160px",marginTop:"112px",padding:"5px",color:"#3026b9",fontSize:"13px",transition:"margin-top .5s ease","&:hover":{marginTop:"90px"}},flowableTitleText:{fontSize:"14px",margin:0,padding:"2px",color:"#373e48"},flowableBasicDetails:{minHeight:"30px",width:"100%",marginTop:"0.3rem",display:"flex",alignItems:"flex-end"},flowableSubText:{fontSize:"13px",color:"#3026b9"}}});C.default=O})(Te);var Ie={};(function(C){Object.defineProperty(C,"__esModule",{value:!0}),C.default=void 0;var c=y(se),t=ge,_=y(Oe),R=y($e),g=y(Ae),O=y(Be),o=y(ve),u=y(ze),h=y(Ce);function y(N){return N&&N.__esModule?N:{default:N}}var k=function(L){var T=L.task,m=(0,t.useSelector)(function(W){var r;return(r=W.app)===null||r===void 0||(r=r.appConfig)===null||r===void 0||(r=r.SERVICE_BASE_URL_MAP)===null||r===void 0?void 0:r.NativeWorkflowServices}),D=(T==null?void 0:T.technicalStatus)==="COMPLETED"?"".concat(m,"/getInteractiveHistoryDiagram/pdd/").concat(T.processName,"/pid/").concat(T.processId):"".concat(m,"/getInteractiveDiagram/pdd/").concat(T.processName,"/pid/").concat(T.processId);return c.default.createElement(c.default.Fragment,null,c.default.createElement("iframe",{title:"Flowable Diagram",src:D,style:{height:"calc(72vh)",width:"calc(100%)"},className:"cwitmIframe"}))},s=function(L){var T=L.openDialog,m=L.updateOpenDialog,D=L.task;return c.default.createElement(_.default,{open:T,fullWidth:!0,maxWidth:"lg"},c.default.createElement(R.default,{sx:{m:0,p:2}},c.default.createElement(o.default,{direction:"row",justifyContent:"space-between",alignItems:"center"},c.default.createElement(O.default,{required:"true",variant:"h6",color:"text.primary",className:"cwitmWeight500"},"Work Flow"),c.default.createElement(o.default,{direction:"row",justifyContent:"end"},c.default.createElement(u.default,{onClick:function(){m(!1)},"aria-label":"close"},c.default.createElement(h.default,{icon:"MaterialIcon.MdClose",sx:{cursor:"pointer"},edge:"end"}))))),c.default.createElement(g.default,null,c.default.createElement(o.default,{sx:{width:"100%"},justifyContent:"center"},c.default.createElement(k,{task:D}))))};C.default=s})(Ie);(function(C){function c(a){"@babel/helpers - typeof";return c=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(d){return typeof d}:function(d){return d&&typeof Symbol=="function"&&d.constructor===Symbol&&d!==Symbol.prototype?"symbol":typeof d},c(a)}Object.defineProperty(C,"__esModule",{value:!0}),C.default=void 0;var t=M(se),_=ge,R=_e,g=Ve(),O=r(Ge),o=r(He),u=r(ve),h=r(qe),y=r(Ue),k=r(Je),s=r(Ce),N=r(Q),L=r(Z);r(Re);var T=r(ee),m=r(te),D=Ye,W=r(Te);r(Ie);function r(a){return a&&a.__esModule?a:{default:a}}function M(a,d){if(typeof WeakMap=="function")var i=new WeakMap,j=new WeakMap;return(M=function(f,$){if(!$&&f&&f.__esModule)return f;var x,p,l={__proto__:null,default:f};if(f===null||c(f)!="object"&&typeof f!="function")return l;if(x=$?j:i){if(x.has(f))return x.get(f);x.set(f,l)}for(var P in f)P!=="default"&&{}.hasOwnProperty.call(f,P)&&((p=(x=Object.defineProperty)&&Object.getOwnPropertyDescriptor(f,P))&&(p.get||p.set)?x(l,P,p):l[P]=f[P]);return l})(a,d)}function S(a){return H(a)||ae(a)||we(a)||z()}function z(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ae(a){if(typeof Symbol<"u"&&a[Symbol.iterator]!=null||a["@@iterator"]!=null)return Array.from(a)}function H(a){if(Array.isArray(a))return le(a)}function q(a,d){return We(a)||De(a,d)||we(a,d)||Ne()}function Ne(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function we(a,d){if(a){if(typeof a=="string")return le(a,d);var i={}.toString.call(a).slice(8,-1);return i==="Object"&&a.constructor&&(i=a.constructor.name),i==="Map"||i==="Set"?Array.from(a):i==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?le(a,d):void 0}}function le(a,d){(d==null||d>a.length)&&(d=a.length);for(var i=0,j=Array(d);i<d;i++)j[i]=a[i];return j}function De(a,d){var i=a==null?null:typeof Symbol<"u"&&a[Symbol.iterator]||a["@@iterator"];if(i!=null){var j,B,f,$,x=[],p=!0,l=!1;try{if(f=(i=i.call(a)).next,d===0){if(Object(i)!==i)return;p=!1}else for(;!(p=(j=f.call(i)).done)&&(x.push(j.value),x.length!==d);p=!0);}catch(P){l=!0,B=P}finally{try{if(!p&&i.return!=null&&($=i.return(),Object($)!==$))return}finally{if(l)throw B}}return x}}function We(a){if(Array.isArray(a))return a}var Le=function(d){var i,j,B,f=d.task,$=d.refresh,x=(0,_.useSelector)(function(E){return E.workflow}),p=(0,_.useSelector)(function(E){return E.app}),l=(0,W.default)(),P=(0,_.useDispatch)(),je=(0,t.useState)(),be=q(je,2),A=be[0],Fe=be[1],U=p==null?void 0:p.userList;(0,_.useSelector)(function(E){var e;return(e=E.userReducer)===null||e===void 0||(e=e.appConfig)===null||e===void 0?void 0:e.SERVICE_BASE_URL_MAP}),p==null||(i=p.appConfig)===null||i===void 0||(i=i.applicationProperties)===null||i===void 0||(i=i.default)===null||i===void 0||i.dateTimeFormat;var v=x==null?void 0:x.workflowData,Me=x==null?void 0:x.loading;(0,t.useEffect)(function(){Fe(null),(f==null?void 0:f.systemId)==="Flowable"&&$&&P((0,R.getWorkflowData)(f.taskId,f.processId))},[]);var ne=function(e,w,b){if(e)switch(w.toUpperCase()){case"GROUP":return(0,g.getGroupNameByID)(e,groupsList,b);case"USER":default:return b?(0,g.getUserNameByID)(e,U,b):(0,g.getUserNameByID)(e,U)}},ie=function(e,w){for(var b,I,F,n=e||[],re=(n==null||(b=n[0])===null||b===void 0?void 0:b.displayName)||ne(n==null||(I=n[0])===null||I===void 0?void 0:I.ownerId,n==null||(F=n[0])===null||F===void 0?void 0:F.ownerType,w),V=1;V<(n==null?void 0:n.length)-1;V++){var oe,ue,de;re+=", ".concat((n==null||(oe=n[V])===null||oe===void 0?void 0:oe.displayName)||ne(n==null||(ue=n[V])===null||ue===void 0?void 0:ue.ownerId,n==null||(de=n[V])===null||de===void 0?void 0:de.ownerType,w))}if((n==null?void 0:n.length)>1){var ce,me,fe;re+=" and ".concat((n==null||(ce=n[n.length-1])===null||ce===void 0?void 0:ce.displayName)||ne(n==null||(me=n[n.length-1])===null||me===void 0?void 0:me.ownerId,n==null||(fe=n[n.length-1])===null||fe===void 0?void 0:fe.ownerType,w))}return re},J=function(e,w){switch(e==null?void 0:e.technicalStatus){case"READY":return ie(e.owners,w);case"RESERVED":return ie(e.owners,w);case"DRAFTED":return ie(e.owners,w);case"COMPLETED":return w?(0,g.getUserNameByID)(e==null?void 0:e.completedBy,U,w):(0,g.getUserNameByID)(e==null?void 0:e.completedBy,U);default:return e==null?void 0:e.updatedBy}},he=function(e){switch(e==null?void 0:e.technicalStatus){case"COMPLETED":return t.default.createElement("div",{className:"".concat((e==null?void 0:e.taskId)===A?l.selectedStatusButtonStyle:l.statusButtonStyle," cwitmHbox cwitmAlignItemsCenter cwitmMT8")},(e==null?void 0:e.color)==="#FF0101"?t.default.createElement(T.default,{sx:{color:e==null?void 0:e.color,width:21,height:21}}):t.default.createElement(D.ApprovedCheckCircleRounded,null),t.default.createElement("span",{className:"".concat(l.subjectLine," cwitmWeight400 cwitmML8")},(0,g.getCustomChipLabel)(e==null?void 0:e.technicalStatus,e==null?void 0:e.itmStatus)));case"READY":return t.default.createElement("div",{className:"".concat((e==null?void 0:e.taskId)===A?l.selectedStatusButtonStyle:l.statusButtonStyle," cwitmHbox cwitmAlignItemsCenter cwitmMT8")},t.default.createElement(L.default,{sx:{width:20,height:20,color:"#007AD4"}}),t.default.createElement("span",{className:"".concat(l.subjectLine," cwitmWeight400 cwitmML8")},"Open"));case"DRAFTED":return t.default.createElement("div",{className:"".concat((e==null?void 0:e.taskId)===A?l.selectedStatusButtonStyle:l.statusButtonStyle," cwitmHbox cwitmAlignItemsCenter cwitmMT8")},t.default.createElement(m.default,{sx:{width:20,height:20,color:"#FF6F00"}}),t.default.createElement("span",{className:"".concat(l.subjectLine," cwitmWeight400 cwitmML8")},"Drafted"));case"RESERVED":return t.default.createElement("div",{className:"".concat((e==null?void 0:e.taskId)===A?l.selectedStatusButtonStyle:l.statusButtonStyle," cwitmHbox cwitmAlignItemsCenter cwitmMT8")},t.default.createElement(N.default,{sx:{width:20,height:20,color:"#FF6F00"}}),t.default.createElement("span",{className:"".concat(l.subjectLine," cwitmWeight400 cwitmML8")},"In Progress"));default:return null}},ye=function(e){var w,b,I=v==null||(w=v.data)===null||w===void 0?void 0:w.filter(function(F){return(F==null?void 0:F.technicalStatus)!=="COMPLETED"});if(e+1===(v==null||(b=v.data)===null||b===void 0?void 0:b.length))return t.default.createElement(t.default.Fragment,null,(I==null?void 0:I.length)>0&&t.default.createElement("div",{className:"cwitmHbox"},t.default.createElement("div",{className:"cwitmVbox"},t.default.createElement("div",{className:"".concat(l.avatarContainerSize," cwitmVbox cwitmJustifyContentCenter cwitmAlignItemsCenter")},t.default.createElement(D.ProcessFlowEllipseIcon,null)),t.default.createElement(o.default,{className:l.connectorHeaderLineCol,orientation:"vertical",flexItem:!0})),t.default.createElement("div",{className:"cwitmML16 cwitmMB8"},t.default.createElement("span",{className:"".concat(l.nextProcessText," cwitmHbox cwitmAlignItemsCenter cwitmPagePadding cwitmWeight400")},"Further steps yet to be determined"))),t.default.createElement("div",{className:"cwitmHbox"},t.default.createElement(D.ProcessFlowEndIcon,null),t.default.createElement("div",{className:"cwitmML16"},t.default.createElement("span",{className:"".concat(l.endLineText," cwitmWeight400")},"End"))))},Se=function(e,w){var b,I,F;return t.default.createElement(t.default.Fragment,null,t.default.createElement("div",{className:"cwitmHbox"},t.default.createElement("div",{className:"cwitmVbox"},t.default.createElement(D.CheckCircleRounded,null),t.default.createElement(o.default,{className:"".concat(l.connectorHeaderLineCol),orientation:"vertical",flexItem:!0})),t.default.createElement("div",{className:"cwitmVbox cwitmML16 cwitmMB16"},t.default.createElement("div",{className:"cwitmVbox"},t.default.createElement("span",{className:"".concat(l.subjectLine," cwitmWeight400")},(0,g.getUserNameByID)(e==null?void 0:e.createdBy,U)," "," initiated the workflow"),t.default.createElement(y.default,{title:"Created at ".concat((0,g.dateTimeFormatter)(v==null||(b=v.data)===null||b===void 0||(b=b[0])===null||b===void 0?void 0:b.createdOnForProcess,null,!0,null)),placement:"bottom-start"},t.default.createElement("span",{className:"".concat(l.subTextLine," cwitmWeight400")},(0,g.dateTimeFormatter)(v==null||(I=v.data)===null||I===void 0||(I=I[0])===null||I===void 0?void 0:I.createdOnForProcess,null,!0,null)))))),t.default.createElement("div",{className:"cwitmHbox"},t.default.createElement("div",{className:"cwitmVbox"},t.default.createElement("div",{className:"".concat(l.avatarContainerSize," cwitmHbox cwitmJustifyContentCenter cwitmAlignItemsCenter")},e!=null&&e.owners&&(Object==null||(F=Object.values(e==null?void 0:e.owners))===null||F===void 0?void 0:F.length)>1?t.default.createElement(s.default,{elementid:"cwitm-workspace-taskcard-nature",style:{width:"25px",height:"25px",padding:"0.25rem",borderRadius:"50%",backgroundColor:"#DBD9FF",fontSize:"16px"},size:"sm",icon:"MaterialIcon.MdPeopleOutline"}):t.default.createElement(k.default,{size:"sm",name:O.default.toHeaderCase(J(e,!0)),className:l.avatarSize})),t.default.createElement(o.default,{className:l.connectorLineCol,orientation:"vertical",flexItem:!0})),t.default.createElement("div",{className:"cwitmVbox cwitmML16 cwitmMB16 ".concat((e==null?void 0:e.taskId)===A?l.selectedDiv:"")},t.default.createElement("div",{className:"cwitmVbox"},t.default.createElement("span",{className:"".concat(l.subjectLine," cwitmWeight400")},J(e)),(e==null?void 0:e.createdOn)!==""&&t.default.createElement(y.default,{title:"Updated at ".concat((0,g.dateTimeFormatter)(e==null?void 0:e.updatedOn,null,!0,null)),placement:"bottom-start"},t.default.createElement("span",{className:"".concat((e==null?void 0:e.taskId)===A?l.SelectedSubTextLine:l.subTextLine," cwitmWeight400")},(0,g.dateTimeFormatter)(e==null?void 0:e.updatedOn,null,!0,null))),he(e)))),ye(w))},Pe=function(e,w){return t.default.createElement(t.default.Fragment,null,t.default.createElement("div",{className:"cwitmHbox"},t.default.createElement("div",{className:"cwitmVbox"},t.default.createElement("div",{className:"".concat(l.avatarContainerSize," cwitmHbox cwitmJustifyContentCenter cwitmAlignItemsCenter")},t.default.createElement(k.default,{size:"sm",name:O.default.toHeaderCase(J(e,!0)),className:l.avatarSize})),t.default.createElement(o.default,{className:l.connectorLineCol,orientation:"vertical",flexItem:!0})),t.default.createElement("div",{className:"cwitmVbox cwitmML16 cwitmMB16 ".concat((e==null?void 0:e.taskId)===A?l.selectedDiv:"")},t.default.createElement("div",{className:"cwitmVbox"},t.default.createElement("span",{className:"".concat(l.subjectLine," cwitmWeight400")},J(e)),(e==null?void 0:e.createdOn)!==""&&t.default.createElement(y.default,{title:"Updated at ".concat((0,g.dateTimeFormatter)(e==null?void 0:e.updatedOn,null,!0,null)),placement:"bottom-start"},t.default.createElement("span",{className:"".concat((e==null?void 0:e.taskId)===A?l.SelectedSubTextLine:l.subTextLine," cwitmWeight400")},(0,g.dateTimeFormatter)(e==null?void 0:e.updatedOn,null,!0,null))),he(e)))),ye(w))};return t.default.createElement(t.default.Fragment,null,t.default.createElement("div",{className:"cwitmScroll cwitmScrollX cwitmSetHeight100 cwitmSetWidth100",style:{padding:"15px 24px 30px 20px"}},Me?t.default.createElement(u.default,{className:"cwitmSetWidth100"},t.default.createElement(u.default,{direction:"row",sx:{height:"90px",paddingTop:"10px"},className:"cwitmSetWidth100",spacing:2,alignItems:"flex-start"},t.default.createElement(h.default,{variant:"circular",width:24,height:24,sx:{marginTop:"3px"}}),t.default.createElement(u.default,{className:"cwitmSetWidth100"},t.default.createElement(h.default,{height:18,width:"20%"}))),S(Array(4).keys()).map(function(E,e){return t.default.createElement(u.default,{key:e,direction:"row",sx:{height:"90px"},spacing:2,alignItems:"flex-start",className:"cwitmSetWidth100"},t.default.createElement(h.default,{variant:"circular",width:24,height:24,sx:{marginTop:"5px"}}),t.default.createElement(u.default,{sx:{paddingTop:"2px"},className:"cwitmSetWidth100"},t.default.createElement(h.default,{height:18,width:"15%"}),t.default.createElement(h.default,{height:10,width:"25%"}),t.default.createElement(h.default,{height:18,width:"80%"})))})):t.default.createElement(t.default.Fragment,null,v&&(v==null?void 0:v.data)&&(v==null||(j=v.data)===null||j===void 0?void 0:j.length)>0&&(v==null||(B=v.data)===null||B===void 0?void 0:B.map(function(E,e){return e===0?Se(E,e):Pe(E,e)})))))};C.default=Le})(ke);(function(C){function c(k){"@babel/helpers - typeof";return c=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(s){return typeof s}:function(s){return s&&typeof Symbol=="function"&&s.constructor===Symbol&&s!==Symbol.prototype?"symbol":typeof s},c(k)}Object.defineProperty(C,"__esModule",{value:!0}),C.default=void 0;var t=h(se),_=ge,R=_e,g=u(ve),O=u(ke),o=u(Ke);function u(k){return k&&k.__esModule?k:{default:k}}function h(k,s){if(typeof WeakMap=="function")var N=new WeakMap,L=new WeakMap;return(h=function(m,D){if(!D&&m&&m.__esModule)return m;var W,r,M={__proto__:null,default:m};if(m===null||c(m)!="object"&&typeof m!="function")return M;if(W=D?L:N){if(W.has(m))return W.get(m);W.set(m,M)}for(var S in m)S!=="default"&&{}.hasOwnProperty.call(m,S)&&((r=(W=Object.defineProperty)&&Object.getOwnPropertyDescriptor(m,S))&&(r.get||r.set)?W(M,S,r):M[S]=m[S]);return M})(k,s)}var y=function(s){var N=s.task,L=s.token,T=s.destinationData,m=s.userData,D=s.useWorkAccess,W=s.useConfigServerDestination,r=s.userList,M=s.configData,S=s.userPreferences,z=(0,_.useDispatch)(),ae=(0,_.useSelector)(function(q){return q.app}),H=(0,_.useSelector)(function(q){return q.TaskDetail.selectedTask});return(0,t.useEffect)(function(){z((0,R.setInitialAppData)({token:L,destinationData:T,userPreferences:S,userData:m,useWorkAccess:D,useConfigServerDestination:W,configData:M})),z((0,R.setUserList)(r)),z((0,R.setSelectedTask)(N))},[]),t.default.createElement(t.default.Fragment,null,ae.initialDataLoadFlag&&(H==null?void 0:H.taskId)&&t.default.createElement(g.default,{direction:"row",justifyContent:"space-between",sx:{padding:0,height:"100%"}},t.default.createElement(O.default,{task:N,refresh:!0})))};C.default=(0,o.default)(y)})(Qe);
