export const colors = {
  primary: {
    light: "#EAE9FF",
    veryLight: "#F8F7FF",
    main: "#3026B9",
    dark: "#0019ae",
    darkPlus: "#001177",
    lightPlus: "#1976d2",
    white: "#FFFFFF",
    grey: "#424242",
    whiteSmoke: "#f5f5f5",
    lightBlue: "#bbdefb",
    ultraLight: "#F5F4FF",
    medium: "#5E56CC",
    accent: "#4C42C7",
    pale: "#D6D4F5",
    border: "#C9C6FF",
    deepDark: "#666",
    chipBackground: "#ECFDF5",
  },
  template: {
    mentionBackground: "#F1F5FE",
    suggestionBorder: "rgba(0,0,0,0.15)",
    suggestionItem: "rgba(0,0,0,0.15)",
    suggestionFocused: "#cee4e5",
    editorBorder: "#ccc",
    editorBackground: "#fff",
    toolbarBackground: "#f5f5f5",
    toolbarBorder: "#ddd",
  },
  secondary: {
    light: "#E3F5EB",
    dark: "#005b2a",
    grey: "#777",
    yellow: "#ebb434",
    lightYellow: "#fff8e1",
    green: "#218041",
    lightGreen: "#B9E6CA",
    amber: "#FFB300",
    lightAmber: "#FFE082",
    lime: "#8BC34A",
    teal: "#009688",
  },
  error: {
    red: "red",
    light: "#FCE9ED",
    dark: "#A9001A",
    mild: "#FF6B6B",
    pale: "#FFEAEA",
    warning: "#FF9800",
    critical: "#D50000",
    lightPink: "#ffecec",
    darkRed: "#b71c1c",
    deepRed: "#DC2626",
  },
  success: {
    light: "#EAF5E9",
    dark: "#2D5F24",
    bright: "#4CAF50",
    pale: "#DCEDC8",
    vibrant: "#00C853",
    muted: "#81C784",
    deepGreen: "#10B981",
    darkGreen: "#059669",
    completed: "#10B981",
    completedDark: "#059669",
    completedBackground: "#ECFDF5",
  },
  info: {
    light: "#E4F1FA",
    dark: "#1f4787",
    medium: "#2196F3",
    pale: "#BBDEFB",
    bright: "#03A9F4",
    navy: "#0D47A1",
  },
  warning: {
    light: "#FCF3ED",
    dark: "#653210",
    amber: "#FFC107",
    orange: "#FF9800",
    deep: "#FF5722",
    pale: "#FFECB3",
  },
  tab: {
    background: "#E0E7FF",
    active: "#C5CAE9",
    hover: "#E8EAF6",
    selected: "#7986CB",
    disabled: "#EEEEEE",
  },
  icon: {
    orange: "#C2752F",
    pink: "#FFF6F6",
    blue: "#1E88E5",
    green: "#43A047",
    purple: "#7E57C2",
    grey: "#78909C",
    red: "#E53935",
    image: "#1A9FB3",
    excel: "#217346",
    pdf: "#F40F02",
    doc: "#2b579a",
    ppt: "#D24726",
    file: "#5F6368",
    matView: "#0288d1",
    matDownload: "#2e7d32",
    delete: "#d32f2f",
  },
  black: {
    dark: "#1D1D1D",
    light: "#333333",
    medium: "#212121",
    charcoal: "#424242",
    graphite: "#616161",
    lightGrey: "#f4f4f4",
  },
  placeholder: {
    color: "#C1C1C1",
    light: "#E0E0E0",
    dark: "#9E9E9E",
    subtle: "#EEEEEE",
    medium: "#BDBDBD",
  },
  blue: {
    main: "#4791db",
    light: "#64B5F6",
    dark: "#1976D2",
    navy: "#0D47A1",
    sky: "#03A9F4",
    indigo: "#3F51B5",
    pending: "#2563EB",
  },
  hover: {
    hoverbg: "#e0e0e0",
    light: "#F5F5F5",
    primary: "rgba(48, 38, 185, 0.08)",
    secondary: "rgba(0, 91, 42, 0.08)",
    subtle: "rgba(0, 0, 0, 0.04)",
  },
  basic: {
    white: "white",
    grey: "#E0E0E0",
    lightGrey: "#F5F5F5",
    lighterGrey: "#f9f9f9",
    mediumGrey: "#9E9E9E",
    darkGrey: "#616161",
    black: "#000000",
    transparent: "transparent",
  },
  legend: {
    grey: "#757575",
    blue: "#2196F3",
    green: "#4CAF50",
    orange: "#FF9800",
    purple: "#9C27B0",
    red: "#F44336",
  },
  neutral: {
    50: "#FAFAFA",
    100: "#F5F5F5",
    200: "#EEEEEE",
    300: "#E0E0E0",
    400: "#BDBDBD",
    500: "#9E9E9E",
    600: "#757575",
    700: "#616161",
    800: "#424242",
    900: "#212121",
  },
  border: {
    light: "#E0E0E0",
    medium: "#BDBDBD",
    dark: "#9E9E9E",
    primary: "rgba(48, 38, 185, 0.3)",
    error: "rgba(244, 67, 54, 0.5)",
    success: "rgba(76, 175, 80, 0.5)",
    cardBorder: "#E2E8F0",
  },
  background: {
    paper: "#FFFFFF",
    default: "#F5F5F5",
    card: "#FAFAFA",
    panel: "#FFFFFF",
    table: "#FFFFFF",
    modal: "#FFFFFF",
    drawer: "#FFFFFF",
    tooltip: "#616161",
    menu: "#FFFFFF",
    container: "#FAFCFF",
    canceled: "#FEF2F2",
    pending: "#EFF6FF",
    header: "#EAE9FF40",
    lightCard: "#FCFDFF",
    subtle: "#EDF2F7",
    templateBg:'#f9fafb'
  },
  text: {
    primary: "#212121",
    secondary: "#757575",
    disabled: "#9E9E9E",
    hint: "#9E9E9E",
    link: "#2196F3",
    error: "#F44336",
    success: "#4CAF50",
    warning: "#FF9800",
    darkGrey: "#64748B",
    darkBlue: "#1E293B",
    greyishBlue: "#334155",
    greyBlue: "#6C757D",
    charcoal: "#212529",
    offWhite: "#F8F9FA",
  },
  shadow: {
    light: "0 1px 3px rgba(0,0,0,0.08)",
    medium: "0 2px 4px rgba(0,0,0,0.12)",
    dark: "0 4px 8px rgba(0,0,0,0.16)",
    focus: "0 0 0 2px rgba(48, 38, 185, 0.2)",
  },
  chart: {
    blue: ["#E3F2FD", "#90CAF9", "#42A5F5", "#1E88E5", "#1565C0"],
    green: ["#E8F5E9", "#A5D6A7", "#66BB6A", "#43A047", "#2E7D32"],
    purple: ["#EDE7F6", "#B39DDB", "#7E57C2", "#5E35B1", "#4527A0"],
    orange: ["#FFF3E0", "#FFCC80", "#FFB74D", "#FFA726", "#F57C00"],
    red: ["#FFEBEE", "#FFCDD2", "#EF9A9A", "#E57373", "#EF5350"],
    default: ["#008FFB", "#00E396", "#FEB019", "#FF4560", "#775DD0", "#546E7A", "#26a69a", "#D10CE8"],
  },
  reportTile: {
    blue: "#1976d2",
    lightBlue: "#e3f2fd",
    lightred: "#ffebee",
  },
  chip: {
    background: "#F8FAFC",
    color: "#475569",
  },
  box: {
    scrollBackground: "#CBD5E1",
  },
  attachmentDialog: {
    other: "#B2D9F5",
    orderChange: "#FCDBDB",
  },
  health: {
    critical: "#e74c3c",
    warning: "#f39c12",
    healthy: "#2ecc71",
  },

  gradient:{
    primary:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
  },

  statusColorMap: {
    // 📝 Neutral / Draft
    draft: "linear-gradient(to right, #FDFBDF, #F5F2C1)",

    // ⏳ Pending / Action Required
    dataentrypending: "linear-gradient(to right, #F9C449, #F6B93B)",
    sentbackbyintermediateuser: "linear-gradient(to right, #F6A623, #FFB15E)",
    sentbackbymdmteam: "linear-gradient(to right, #F6891F, #FF9C42)",

    // 📘 In Progress / Informational
    submittedforreview: "linear-gradient(to right,rgb(97, 214, 230), #6EB3F7)",
    inprogress: "linear-gradient(to right,rgb(97, 214, 230), #6EB3F7)",
    validatedrequestor: "linear-gradient(to right, rgb(172 207 161), #9df2bb)",
    validatedmdm: "linear-gradient(to right, #5E60CE, #8E9DF0)",

    // ❌ Error / Failure States
    validationfailedrequestor: "linear-gradient(to right, #F8A5A5, #FBC6C6)",
    validationfailedmdm: "linear-gradient(to right, #F28B82, #F6B6B5)",
    rejected: "linear-gradient(to right, #F76C6C, #FF9B9B)",
    cancelled: "linear-gradient(to right, #E66A8A, #F5A1B0)",
    syndicationfailed: "linear-gradient(to right, #F88E8E, #FDB3B3)",
    uploadfailed: "linear-gradient(to right, #F17C57, #FDB7A1)",
    syndicationfaileddirect: "linear-gradient(to right, #e9857969, #F9A69A)",

    // ✅ Success / Completed
    uploadsuccessful: "linear-gradient(to right, #2ECC71, #58D68D)",
    syndicatedinsap: "linear-gradient(to right, #A3E4D7, #C6F3E9)",
    syndicatedpartially: "linear-gradient(to right, #FFE680, #FFF3B0)",
    syndicatedinsapdirect: "linear-gradient(to right, #48C9B0, #76E3C3)",
    syndicatedpartiallydirect: "linear-gradient(to right, #85d57c73, #82e376)",
    completed: "linear-gradient(to right, #85d57c73, #82e376)",
    //default
    default: "#df8879",
  },
  alert: {
    success: {
      background: "#E7F6ED",
      text: "#1E4620",
      border: "#A8DAB5",
    },
    error: {
      background: "#FDE7E7",
      text: "#611A15",
      border: "#F5B7B1",
    },
    warning: {
      background: "#FFF4E5",
      text: "#663C00",
      border: "#FFE0B2",
    },
    info: {
      background: "#E5F6FD",
      text: "#0A4361",
      border: "#B3E5FC",
    },
  },

  announcement: {
    // Dialog background gradients
    dialogBackground: "linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%)",
    dialogBorder: "#667eea",

    // Header colors
    headerText: "#667eea",
    headerBackground: "linear-gradient(135deg,rgb(102, 126, 234) 0%, #764ba2 100%)",
    headerHover: "linear-gradient(135deg,rgb(102, 172, 234) 0%,rgb(97, 75, 162) 100%)",

    // Chip colors
    chipBackground: "linear-gradient(135deg,rgb(102, 126, 234) 0%, #764ba2 100%)",
    chipActive: "#4caf50",
    chipInactive: "#ff9800",

    // Description section
    descriptionBackground: "rgba(255, 255, 255, 0.7)",
    descriptionBorder: "rgba(102, 126, 234, 0.1)",

    // Media viewer
    mediaViewerBackground: "rgba(0, 0, 0, 0.9)",
    mediaOverlayBackground: "rgba(0, 0, 0, 0.5)",
    mediaOverlayHover: "rgba(0, 0, 0, 0.7)",
    mediaGradient: "linear-gradient(45deg, #667eea, #764ba2)",
    mediaOverlayGradient: "linear-gradient(transparent, rgba(0,0,0,0.7))",

    // Card hover effects
    cardShadow: "0 8px 25px rgba(102, 126, 234, 0.3)",

    // External link button
    linkBorder: "#667eea",
    linkColor: "#667eea",
    linkBorderHover: "#764ba2",
    linkBackgroundHover: "rgba(102, 126, 234, 0.05)",

    // Meta information
    metaIconColor: "#667eea",

    // Text colors
    textPrimary: "#333",
    textSecondary: "#555",
  },
  dialog: {
    borderBottom: "rgba(0, 0, 0, 0.12)",
  },

  progressColors: {
    complete: {
      start: "#10b981", // Green gradient start
      end: "#059669", // Green gradient end
    },
    high: {
      start: "#fbbf24", // Yellow gradient start
      end: "#d97706", // Yellow gradient end
    },
    medium: {
      start: "#f97316", // Orange gradient start
      end: "#ea580c", // Orange gradient end
    },
    low: {
      start: "#ef4444", // Red gradient start
      end: "#dc2626", // Red gradient end
    },
    minimal: {
      start: "#6b7280", // Gray gradient start
      end: "#4b5563", // Gray gradient end
    },
    inactive: {
      fill: "rgba(255, 255, 255, 0.3)",
      stroke: "rgba(255, 255, 255, 0.5)",
    },
    shadow: {
      dropShadow: "rgba(0, 0, 0, 0.2)",
    },
  },
  colorPalleteDashboard: {
    Pastel: ["#8dd3c7", "#ffffb3", "#bebada", "#fb8072", "#80b1d3", "#fdb462", "#b3de69", "#fccde5", "#d9d9d9", "#bc80bd"],
    Vibrant: ["#ff3333", "#33cc33", "#3366ff", "#ffcc00", "#ff33cc", "#33cccc", "#ff9933", "#9933ff", "#99cc33", "#ff6666"],
    Dark: ["#1e1e2f", "#2c2c38", "#343a40", "#212529", "#0d1117", "#1a1a1a", "#2d2d2d", "#3b3b3b", "#20232a", "#111111", "#1c1c1c", "#262626", "#1f1f1f"],
  },
};
