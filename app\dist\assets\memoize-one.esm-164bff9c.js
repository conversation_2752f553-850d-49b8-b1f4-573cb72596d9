import{fW as b,fX as s}from"./index-75c1660a.js";var o=function(r){return m(r)&&!y(r)};function m(e){return!!e&&typeof e=="object"}function y(e){var r=Object.prototype.toString.call(e);return r==="[object RegExp]"||r==="[object Date]"||j(e)}var O=typeof Symbol=="function"&&Symbol.for,g=O?Symbol.for("react.element"):60103;function j(e){return e.$$typeof===g}function M(e){return Array.isArray(e)?[]:{}}function u(e,r){return r.clone!==!1&&r.isMergeableObject(e)?a(M(e),e,r):e}function A(e,r,n){return e.concat(r).map(function(c){return u(c,n)})}function p(e,r){if(!r.customMerge)return a;var n=r.customMerge(e);return typeof n=="function"?n:a}function E(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter(function(r){return Object.propertyIsEnumerable.call(e,r)}):[]}function f(e){return Object.keys(e).concat(E(e))}function i(e,r){try{return r in e}catch{return!1}}function d(e,r){return i(e,r)&&!(Object.hasOwnProperty.call(e,r)&&Object.propertyIsEnumerable.call(e,r))}function S(e,r,n){var c={};return n.isMergeableObject(e)&&f(e).forEach(function(t){c[t]=u(e[t],n)}),f(r).forEach(function(t){d(e,t)||(i(e,t)&&n.isMergeableObject(r[t])?c[t]=p(t,n)(e[t],r[t],n):c[t]=u(r[t],n))}),c}function a(e,r,n){n=n||{},n.arrayMerge=n.arrayMerge||A,n.isMergeableObject=n.isMergeableObject||o,n.cloneUnlessOtherwiseSpecified=u;var c=Array.isArray(r),t=Array.isArray(e),l=c===t;return l?c?n.arrayMerge(e,r,n):S(e,r,n):u(r,n)}a.all=function(r,n){if(!Array.isArray(r))throw new Error("first argument should be an array");return r.reduce(function(c,t){return a(c,t,n)},{})};var h=a,I=h;const T=b(s);export{I as c,T as r};
