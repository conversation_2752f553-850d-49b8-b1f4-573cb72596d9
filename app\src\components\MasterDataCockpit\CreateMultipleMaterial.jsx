import {
    BottomNavigation,
    Box,
    Button,
    Checkbox,
    Grid,
    IconButton,
    Paper,
    Stack,
    Tab,
    Tabs,
    Typography,
  } from "@mui/material";
  import React, { useState } from "react";
  import {
    iconButton_SpacingSmall,
    outermostContainer,
    button_Primary,
    outerContainer_Information,
    outermostContainer_Information,
  } from "../common/commonStyles";
  import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
//   import ReusableTable from "../../common/ReusableTable";
  import { useNavigate } from "react-router-dom";
import ReusableTable from "../Common/ReusableTable";
  
  const CreateMultipleMaterial = () => {
    const [isLoading, setIsLoading] = useState(false);
    const [value, setValue] = useState("1");
    // const [rowCount, setRowCount] = useState([]);
    const navigate = useNavigate();
  
    const responseFromApi = {
      body: {
        controlingArea: "TZUS",
        tableData: [
          {
            costCenter: "tuk1",
            validFrom: null,
            validTo: null,
            "Basic Data": {
              Names: [
                {
                  fieldName: "Name",
                  sequenceNo: 1,
                  fieldType: "Input",
                  maxLength: 20,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Names",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "WATER",
                },
                {
                  fieldName: "Description",
                  sequenceNo: 2,
                  fieldType: "Input",
                  maxLength: 40,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Names",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "wear",
                },
              ],
              "Basic Data": [
                {
                  fieldName: "User Responsible",
                  sequenceNo: 3,
                  fieldType: "Input",
                  maxLength: 12,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Person Responsible",
                  sequenceNo: 4,
                  fieldType: "Input",
                  maxLength: 20,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Department",
                  sequenceNo: 5,
                  fieldType: "Input",
                  maxLength: 12,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Cost Center Category",
                  sequenceNo: 6,
                  fieldType: "Drop Down",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Comp Code",
                  sequenceNo: 7,
                  fieldType: "Drop Down",
                  maxLength: 4,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Hierarchy area",
                  sequenceNo: 8,
                  fieldType: "Drop Down",
                  maxLength: 12,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Business Area",
                  sequenceNo: 9,
                  fieldType: "Drop Down",
                  maxLength: 4,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Functional Area",
                  sequenceNo: 10,
                  fieldType: "Drop Down",
                  maxLength: 4,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Currency",
                  sequenceNo: 11,
                  fieldType: "",
                  maxLength: 5,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Profit Center",
                  sequenceNo: 12,
                  fieldType: "Drop Down",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
              ],
            },
            Control: {
              Control: [
                {
                  fieldName: "Record Quantity",
                  sequenceNo: 1,
                  fieldType: "Radio Button",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Control",
                  cardName: "Control",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Actual primary costs",
                  sequenceNo: 2,
                  fieldType: "Radio Button",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Control",
                  cardName: "Control",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "  Plan primary costs",
                  sequenceNo: 3,
                  fieldType: "Radio Button",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Control",
                  cardName: "Control",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: " Commitment Update",
                  sequenceNo: 4,
                  fieldType: "Radio Button",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Control",
                  cardName: "Control",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Act. secondary costs",
                  sequenceNo: 5,
                  fieldType: "Radio Button",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Control",
                  cardName: "Control",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Plan Secondary Costs",
                  sequenceNo: 6,
                  fieldType: "Radio Button",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Control",
                  cardName: "Control",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Actual Revenue",
                  sequenceNo: 7,
                  fieldType: "Radio Button",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Control",
                  cardName: "Control",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Plan Revenue",
                  sequenceNo: 8,
                  fieldType: "Radio Button",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Control",
                  cardName: "Control",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
              ],
            },
            Templates: {
              "Formula planning": [
                {
                  fieldName: "Acty-Indep. FormPlng Temp",
                  sequenceNo: 1,
                  fieldType: "Drop Down",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Templates",
                  cardName: "Formula planning",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Acty-Dep. Form.Plng Temp.",
                  sequenceNo: 2,
                  fieldType: "Drop Down",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Templates",
                  cardName: "Formula planning",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
              ],
              "Activity and Business Process Allocation": [
                {
                  fieldName: "Acty-Indep. Alloc. Temp",
                  sequenceNo: 3,
                  fieldType: "Drop Down",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Templates",
                  cardName: "Activity and Business Process Allocation",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Acty-Dep. Alloc. Template",
                  sequenceNo: 4,
                  fieldType: "Drop Down",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Templates",
                  cardName: "Activity and Business Process Allocation",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
              ],
              "Actual Statistical Key Figures": [
                {
                  fieldName: "Templ.: Act. Stat. Key Figure",
                  sequenceNo: 5,
                  fieldType: "Drop Down",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Templates",
                  cardName: "Actual Statistical Key Figures",
                  cardSeq: 3,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Templ.: Act. Stat. Key Figure",
                  sequenceNo: 6,
                  fieldType: "Drop Down",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Templates",
                  cardName: "Actual Statistical Key Figures",
                  cardSeq: 3,
                  visibility: "Optional",
                  value: "",
                },
              ],
              "Overhead rates": [
                {
                  fieldName: "Costing Sheet",
                  sequenceNo: 7,
                  fieldType: "Drop Down",
                  maxLength: 6,
                  dataType: "String",
                  viewName: "Templates",
                  cardName: "Overhead rates",
                  cardSeq: 4,
                  visibility: "Optional",
                  value: "",
                },
              ],
            },
            Address: {
              "Address Data": [
                {
                  fieldName: "Title",
                  sequenceNo: 1,
                  fieldType: "Input",
                  maxLength: 15,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Name 1",
                  sequenceNo: 2,
                  fieldType: "Input",
                  maxLength: 35,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Name 2",
                  sequenceNo: 3,
                  fieldType: "Input",
                  maxLength: 35,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Name 3",
                  sequenceNo: 4,
                  fieldType: "Input",
                  maxLength: 35,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Name 4",
                  sequenceNo: 5,
                  fieldType: "Input",
                  maxLength: 35,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Street",
                  sequenceNo: 6,
                  fieldType: "Input",
                  maxLength: 35,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Location",
                  sequenceNo: 7,
                  fieldType: "Input",
                  maxLength: 35,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "District",
                  sequenceNo: 8,
                  fieldType: "Input",
                  maxLength: 35,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Country/Reg",
                  sequenceNo: 9,
                  fieldType: "Drop Down",
                  maxLength: 3,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Jurisdiction",
                  sequenceNo: 10,
                  fieldType: "Drop Down",
                  maxLength: 15,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "PO Box",
                  sequenceNo: 11,
                  fieldType: "Input",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Postal Code",
                  sequenceNo: 12,
                  fieldType: "Input",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "PO Box Postcod",
                  sequenceNo: 13,
                  fieldType: "Input",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Region",
                  sequenceNo: 14,
                  fieldType: "Drop Down",
                  maxLength: 3,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
              ],
            },
            Communication: {
              "Communication Data": [
                {
                  fieldName: "Language Key",
                  sequenceNo: 1,
                  fieldType: "Drop Down",
                  maxLength: 2,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Telephone 1",
                  sequenceNo: 2,
                  fieldType: "Input",
                  maxLength: 16,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Telephone 2",
                  sequenceNo: 3,
                  fieldType: "Input",
                  maxLength: 16,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Telebox number",
                  sequenceNo: 4,
                  fieldType: "Input",
                  maxLength: 15,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Telex number",
                  sequenceNo: 5,
                  fieldType: "Input",
                  maxLength: 30,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Fax Number",
                  sequenceNo: 6,
                  fieldType: "Input",
                  maxLength: 31,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Teletex number",
                  sequenceNo: 7,
                  fieldType: "Input",
                  maxLength: 30,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Printer Destination",
                  sequenceNo: 8,
                  fieldType: "Input",
                  maxLength: 4,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Data line",
                  sequenceNo: 9,
                  fieldType: "Input",
                  maxLength: 14,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
              ],
            },
          },
          {
            costCenter: "tuk2",
            validFrom: null,
            validTo: null,
            "Basic Data": {
              Names: [
                {
                  fieldName: "Name",
                  sequenceNo: 1,
                  fieldType: "Input",
                  maxLength: 20,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Names",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "FIRE",
                },
                {
                  fieldName: "Description",
                  sequenceNo: 2,
                  fieldType: "Input",
                  maxLength: 40,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Names",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "drink",
                },
              ],
              "Basic Data": [
                {
                  fieldName: "User Responsible",
                  sequenceNo: 3,
                  fieldType: "Input",
                  maxLength: 12,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "a",
                },
                {
                  fieldName: "Person Responsible",
                  sequenceNo: 4,
                  fieldType: "Input",
                  maxLength: 20,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Department",
                  sequenceNo: 5,
                  fieldType: "Input",
                  maxLength: 12,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Cost Center Category",
                  sequenceNo: 6,
                  fieldType: "Drop Down",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "f",
                },
                {
                  fieldName: "Comp Code",
                  sequenceNo: 7,
                  fieldType: "Drop Down",
                  maxLength: 4,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "g",
                },
                {
                  fieldName: "Hierarchy area",
                  sequenceNo: 8,
                  fieldType: "Drop Down",
                  maxLength: 12,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "h",
                },
                {
                  fieldName: "Business Area",
                  sequenceNo: 9,
                  fieldType: "Drop Down",
                  maxLength: 4,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Functional Area",
                  sequenceNo: 10,
                  fieldType: "Drop Down",
                  maxLength: 4,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Currency",
                  sequenceNo: 11,
                  fieldType: "",
                  maxLength: 5,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "l",
                },
                {
                  fieldName: "Profit Center",
                  sequenceNo: 12,
                  fieldType: "Drop Down",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "q",
                },
              ],
            },
            Control: {
              Control: [
                {
                  fieldName: "Record Quantity",
                  sequenceNo: 1,
                  fieldType: "Radio Button",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Control",
                  cardName: "Control",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "w",
                },
                {
                  fieldName: "Actual primary costs",
                  sequenceNo: 2,
                  fieldType: "Radio Button",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Control",
                  cardName: "Control",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "e",
                },
                {
                  fieldName: "  Plan primary costs",
                  sequenceNo: 3,
                  fieldType: "Radio Button",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Control",
                  cardName: "Control",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "r",
                },
                {
                  fieldName: " Commitment Update",
                  sequenceNo: 4,
                  fieldType: "Radio Button",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Control",
                  cardName: "Control",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "t",
                },
                {
                  fieldName: "Act. secondary costs",
                  sequenceNo: 5,
                  fieldType: "Radio Button",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Control",
                  cardName: "Control",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "y",
                },
                {
                  fieldName: "Plan Secondary Costs",
                  sequenceNo: 6,
                  fieldType: "Radio Button",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Control",
                  cardName: "Control",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "u",
                },
                {
                  fieldName: "Actual Revenue",
                  sequenceNo: 7,
                  fieldType: "Radio Button",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Control",
                  cardName: "Control",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "i",
                },
                {
                  fieldName: "Plan Revenue",
                  sequenceNo: 8,
                  fieldType: "Radio Button",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Control",
                  cardName: "Control",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "o",
                },
              ],
            },
            Templates: {
              "Formula planning": [
                {
                  fieldName: "Acty-Indep. FormPlng Temp",
                  sequenceNo: 1,
                  fieldType: "Drop Down",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Templates",
                  cardName: "Formula planning",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "p",
                },
                {
                  fieldName: "Acty-Dep. Form.Plng Temp.",
                  sequenceNo: 2,
                  fieldType: "Drop Down",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Templates",
                  cardName: "Formula planning",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "z",
                },
              ],
              "Activity and Business Process Allocation": [
                {
                  fieldName: "Acty-Indep. Alloc. Temp",
                  sequenceNo: 3,
                  fieldType: "Drop Down",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Templates",
                  cardName: "Activity and Business Process Allocation",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Acty-Dep. Alloc. Template",
                  sequenceNo: 4,
                  fieldType: "Drop Down",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Templates",
                  cardName: "Activity and Business Process Allocation",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
              ],
              "Actual Statistical Key Figures": [
                {
                  fieldName: "Templ.: Act. Stat. Key Figure",
                  sequenceNo: 5,
                  fieldType: "Drop Down",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Templates",
                  cardName: "Actual Statistical Key Figures",
                  cardSeq: 3,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Templ.: Act. Stat. Key Figure",
                  sequenceNo: 6,
                  fieldType: "Drop Down",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Templates",
                  cardName: "Actual Statistical Key Figures",
                  cardSeq: 3,
                  visibility: "Optional",
                  value: "",
                },
              ],
              "Overhead rates": [
                {
                  fieldName: "Costing Sheet",
                  sequenceNo: 7,
                  fieldType: "Drop Down",
                  maxLength: 6,
                  dataType: "String",
                  viewName: "Templates",
                  cardName: "Overhead rates",
                  cardSeq: 4,
                  visibility: "Optional",
                  value: "n",
                },
              ],
            },
            Address: {
              "Address Data": [
                {
                  fieldName: "Title",
                  sequenceNo: 1,
                  fieldType: "Input",
                  maxLength: 15,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "mq",
                },
                {
                  fieldName: "Name 1",
                  sequenceNo: 2,
                  fieldType: "Input",
                  maxLength: 35,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "q",
                },
                {
                  fieldName: "Name 2",
                  sequenceNo: 3,
                  fieldType: "Input",
                  maxLength: 35,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "q",
                },
                {
                  fieldName: "Name 3",
                  sequenceNo: 4,
                  fieldType: "Input",
                  maxLength: 35,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "q",
                },
                {
                  fieldName: "Name 4",
                  sequenceNo: 5,
                  fieldType: "Input",
                  maxLength: 35,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "w",
                },
                {
                  fieldName: "Street",
                  sequenceNo: 6,
                  fieldType: "Input",
                  maxLength: 35,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "e",
                },
                {
                  fieldName: "Location",
                  sequenceNo: 7,
                  fieldType: "Input",
                  maxLength: 35,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "District",
                  sequenceNo: 8,
                  fieldType: "Input",
                  maxLength: 35,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "ty",
                },
                {
                  fieldName: "Country/Reg",
                  sequenceNo: 9,
                  fieldType: "Drop Down",
                  maxLength: 3,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "u",
                },
                {
                  fieldName: "Jurisdiction",
                  sequenceNo: 10,
                  fieldType: "Drop Down",
                  maxLength: 15,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "e",
                },
                {
                  fieldName: "PO Box",
                  sequenceNo: 11,
                  fieldType: "Input",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "ty",
                },
                {
                  fieldName: "Postal Code",
                  sequenceNo: 12,
                  fieldType: "Input",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "r",
                },
                {
                  fieldName: "PO Box Postcod",
                  sequenceNo: 13,
                  fieldType: "Input",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "f",
                },
                {
                  fieldName: "Region",
                  sequenceNo: 14,
                  fieldType: "Drop Down",
                  maxLength: 3,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "df",
                },
              ],
            },
            Communication: {
              "Communication Data": [
                {
                  fieldName: "Language Key",
                  sequenceNo: 1,
                  fieldType: "Drop Down",
                  maxLength: 2,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "g",
                },
                {
                  fieldName: "Telephone 1",
                  sequenceNo: 2,
                  fieldType: "Input",
                  maxLength: 16,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "fd",
                },
                {
                  fieldName: "Telephone 2",
                  sequenceNo: 3,
                  fieldType: "Input",
                  maxLength: 16,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Telebox number",
                  sequenceNo: 4,
                  fieldType: "Input",
                  maxLength: 15,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "gd",
                },
                {
                  fieldName: "Telex number",
                  sequenceNo: 5,
                  fieldType: "Input",
                  maxLength: 30,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "d",
                },
                {
                  fieldName: "Fax Number",
                  sequenceNo: 6,
                  fieldType: "Input",
                  maxLength: 31,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Teletex number",
                  sequenceNo: 7,
                  fieldType: "Input",
                  maxLength: 30,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Printer Destination",
                  sequenceNo: 8,
                  fieldType: "Input",
                  maxLength: 4,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Data line",
                  sequenceNo: 9,
                  fieldType: "Input",
                  maxLength: 14,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "f",
                },
              ],
            },
          },
          {
            costCenter: "tuk3",
            validFrom: null,
            validTo: null,
            "Basic Data": {
              Names: [
                {
                  fieldName: "Name",
                  sequenceNo: 1,
                  fieldType: "Input",
                  maxLength: 20,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Names",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "CLOTH",
                },
                {
                  fieldName: "Description",
                  sequenceNo: 2,
                  fieldType: "Input",
                  maxLength: 40,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Names",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "wash",
                },
              ],
              "Basic Data": [
                {
                  fieldName: "User Responsible",
                  sequenceNo: 3,
                  fieldType: "Input",
                  maxLength: 12,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Person Responsible",
                  sequenceNo: 4,
                  fieldType: "Input",
                  maxLength: 20,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Department",
                  sequenceNo: 5,
                  fieldType: "Input",
                  maxLength: 12,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Cost Center Category",
                  sequenceNo: 6,
                  fieldType: "Drop Down",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Comp Code",
                  sequenceNo: 7,
                  fieldType: "Drop Down",
                  maxLength: 4,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Hierarchy area",
                  sequenceNo: 8,
                  fieldType: "Drop Down",
                  maxLength: 12,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Business Area",
                  sequenceNo: 9,
                  fieldType: "Drop Down",
                  maxLength: 4,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Functional Area",
                  sequenceNo: 10,
                  fieldType: "Drop Down",
                  maxLength: 4,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Currency",
                  sequenceNo: 11,
                  fieldType: "",
                  maxLength: 5,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Profit Center",
                  sequenceNo: 12,
                  fieldType: "Drop Down",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
              ],
            },
            Control: {
              Control: [
                {
                  fieldName: "Record Quantity",
                  sequenceNo: 1,
                  fieldType: "Radio Button",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Control",
                  cardName: "Control",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Actual primary costs",
                  sequenceNo: 2,
                  fieldType: "Radio Button",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Control",
                  cardName: "Control",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "  Plan primary costs",
                  sequenceNo: 3,
                  fieldType: "Radio Button",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Control",
                  cardName: "Control",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: " Commitment Update",
                  sequenceNo: 4,
                  fieldType: "Radio Button",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Control",
                  cardName: "Control",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Act. secondary costs",
                  sequenceNo: 5,
                  fieldType: "Radio Button",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Control",
                  cardName: "Control",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Plan Secondary Costs",
                  sequenceNo: 6,
                  fieldType: "Radio Button",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Control",
                  cardName: "Control",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Actual Revenue",
                  sequenceNo: 7,
                  fieldType: "Radio Button",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Control",
                  cardName: "Control",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Plan Revenue",
                  sequenceNo: 8,
                  fieldType: "Radio Button",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Control",
                  cardName: "Control",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
              ],
            },
            Templates: {
              "Formula planning": [
                {
                  fieldName: "Acty-Indep. FormPlng Temp",
                  sequenceNo: 1,
                  fieldType: "Drop Down",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Templates",
                  cardName: "Formula planning",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Acty-Dep. Form.Plng Temp.",
                  sequenceNo: 2,
                  fieldType: "Drop Down",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Templates",
                  cardName: "Formula planning",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
              ],
              "Activity and Business Process Allocation": [
                {
                  fieldName: "Acty-Indep. Alloc. Temp",
                  sequenceNo: 3,
                  fieldType: "Drop Down",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Templates",
                  cardName: "Activity and Business Process Allocation",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Acty-Dep. Alloc. Template",
                  sequenceNo: 4,
                  fieldType: "Drop Down",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Templates",
                  cardName: "Activity and Business Process Allocation",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
              ],
              "Actual Statistical Key Figures": [
                {
                  fieldName: "Templ.: Act. Stat. Key Figure",
                  sequenceNo: 5,
                  fieldType: "Drop Down",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Templates",
                  cardName: "Actual Statistical Key Figures",
                  cardSeq: 3,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Templ.: Act. Stat. Key Figure",
                  sequenceNo: 6,
                  fieldType: "Drop Down",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Templates",
                  cardName: "Actual Statistical Key Figures",
                  cardSeq: 3,
                  visibility: "Optional",
                  value: "",
                },
              ],
              "Overhead rates": [
                {
                  fieldName: "Costing Sheet",
                  sequenceNo: 7,
                  fieldType: "Drop Down",
                  maxLength: 6,
                  dataType: "String",
                  viewName: "Templates",
                  cardName: "Overhead rates",
                  cardSeq: 4,
                  visibility: "Optional",
                  value: "",
                },
              ],
            },
            Address: {
              "Address Data": [
                {
                  fieldName: "Title",
                  sequenceNo: 1,
                  fieldType: "Input",
                  maxLength: 15,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Name 1",
                  sequenceNo: 2,
                  fieldType: "Input",
                  maxLength: 35,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Name 2",
                  sequenceNo: 3,
                  fieldType: "Input",
                  maxLength: 35,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Name 3",
                  sequenceNo: 4,
                  fieldType: "Input",
                  maxLength: 35,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Name 4",
                  sequenceNo: 5,
                  fieldType: "Input",
                  maxLength: 35,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Street",
                  sequenceNo: 6,
                  fieldType: "Input",
                  maxLength: 35,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Location",
                  sequenceNo: 7,
                  fieldType: "Input",
                  maxLength: 35,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "District",
                  sequenceNo: 8,
                  fieldType: "Input",
                  maxLength: 35,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Country/Reg",
                  sequenceNo: 9,
                  fieldType: "Drop Down",
                  maxLength: 3,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Jurisdiction",
                  sequenceNo: 10,
                  fieldType: "Drop Down",
                  maxLength: 15,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "PO Box",
                  sequenceNo: 11,
                  fieldType: "Input",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Postal Code",
                  sequenceNo: 12,
                  fieldType: "Input",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "PO Box Postcod",
                  sequenceNo: 13,
                  fieldType: "Input",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Region",
                  sequenceNo: 14,
                  fieldType: "Drop Down",
                  maxLength: 3,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
              ],
            },
            Communication: {
              "Communication Data": [
                {
                  fieldName: "Language Key",
                  sequenceNo: 1,
                  fieldType: "Drop Down",
                  maxLength: 2,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Telephone 1",
                  sequenceNo: 2,
                  fieldType: "Input",
                  maxLength: 16,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Telephone 2",
                  sequenceNo: 3,
                  fieldType: "Input",
                  maxLength: 16,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Telebox number",
                  sequenceNo: 4,
                  fieldType: "Input",
                  maxLength: 15,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Telex number",
                  sequenceNo: 5,
                  fieldType: "Input",
                  maxLength: 30,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Fax Number",
                  sequenceNo: 6,
                  fieldType: "Input",
                  maxLength: 31,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Teletex number",
                  sequenceNo: 7,
                  fieldType: "Input",
                  maxLength: 30,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Printer Destination",
                  sequenceNo: 8,
                  fieldType: "Input",
                  maxLength: 4,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Data line",
                  sequenceNo: 9,
                  fieldType: "Input",
                  maxLength: 14,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
              ],
            },
          },
          {
            costCenter: "tuk4",
            validFrom: null,
            validTo: null,
            "Basic Data": {
              Names: [
                {
                  fieldName: "Name",
                  sequenceNo: 1,
                  fieldType: "Input",
                  maxLength: 20,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Names",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "Fabric",
                },
                {
                  fieldName: "Description",
                  sequenceNo: 2,
                  fieldType: "Input",
                  maxLength: 40,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Names",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "stitch",
                },
              ],
              "Basic Data": [
                {
                  fieldName: "User Responsible",
                  sequenceNo: 3,
                  fieldType: "Input",
                  maxLength: 12,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Person Responsible",
                  sequenceNo: 4,
                  fieldType: "Input",
                  maxLength: 20,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Department",
                  sequenceNo: 5,
                  fieldType: "Input",
                  maxLength: 12,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Cost Center Category",
                  sequenceNo: 6,
                  fieldType: "Drop Down",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Comp Code",
                  sequenceNo: 7,
                  fieldType: "Drop Down",
                  maxLength: 4,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Hierarchy area",
                  sequenceNo: 8,
                  fieldType: "Drop Down",
                  maxLength: 12,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Business Area",
                  sequenceNo: 9,
                  fieldType: "Drop Down",
                  maxLength: 4,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Functional Area",
                  sequenceNo: 10,
                  fieldType: "Drop Down",
                  maxLength: 4,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Currency",
                  sequenceNo: 11,
                  fieldType: "",
                  maxLength: 5,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Profit Center",
                  sequenceNo: 12,
                  fieldType: "Drop Down",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Basic Data",
                  cardName: "Basic Data",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
              ],
            },
            Control: {
              Control: [
                {
                  fieldName: "Record Quantity",
                  sequenceNo: 1,
                  fieldType: "Radio Button",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Control",
                  cardName: "Control",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Actual primary costs",
                  sequenceNo: 2,
                  fieldType: "Radio Button",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Control",
                  cardName: "Control",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "  Plan primary costs",
                  sequenceNo: 3,
                  fieldType: "Radio Button",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Control",
                  cardName: "Control",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: " Commitment Update",
                  sequenceNo: 4,
                  fieldType: "Radio Button",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Control",
                  cardName: "Control",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Act. secondary costs",
                  sequenceNo: 5,
                  fieldType: "Radio Button",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Control",
                  cardName: "Control",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Plan Secondary Costs",
                  sequenceNo: 6,
                  fieldType: "Radio Button",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Control",
                  cardName: "Control",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Actual Revenue",
                  sequenceNo: 7,
                  fieldType: "Radio Button",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Control",
                  cardName: "Control",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Plan Revenue",
                  sequenceNo: 8,
                  fieldType: "Radio Button",
                  maxLength: 1,
                  dataType: "String",
                  viewName: "Control",
                  cardName: "Control",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
              ],
            },
            Templates: {
              "Formula planning": [
                {
                  fieldName: "Acty-Indep. FormPlng Temp",
                  sequenceNo: 1,
                  fieldType: "Drop Down",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Templates",
                  cardName: "Formula planning",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Acty-Dep. Form.Plng Temp.",
                  sequenceNo: 2,
                  fieldType: "Drop Down",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Templates",
                  cardName: "Formula planning",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
              ],
              "Activity and Business Process Allocation": [
                {
                  fieldName: "Acty-Indep. Alloc. Temp",
                  sequenceNo: 3,
                  fieldType: "Drop Down",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Templates",
                  cardName: "Activity and Business Process Allocation",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Acty-Dep. Alloc. Template",
                  sequenceNo: 4,
                  fieldType: "Drop Down",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Templates",
                  cardName: "Activity and Business Process Allocation",
                  cardSeq: 2,
                  visibility: "Optional",
                  value: "",
                },
              ],
              "Actual Statistical Key Figures": [
                {
                  fieldName: "Templ.: Act. Stat. Key Figure",
                  sequenceNo: 5,
                  fieldType: "Drop Down",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Templates",
                  cardName: "Actual Statistical Key Figures",
                  cardSeq: 3,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Templ.: Act. Stat. Key Figure",
                  sequenceNo: 6,
                  fieldType: "Drop Down",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Templates",
                  cardName: "Actual Statistical Key Figures",
                  cardSeq: 3,
                  visibility: "Optional",
                  value: "",
                },
              ],
              "Overhead rates": [
                {
                  fieldName: "Costing Sheet",
                  sequenceNo: 7,
                  fieldType: "Drop Down",
                  maxLength: 6,
                  dataType: "String",
                  viewName: "Templates",
                  cardName: "Overhead rates",
                  cardSeq: 4,
                  visibility: "Optional",
                  value: "",
                },
              ],
            },
            Address: {
              "Address Data": [
                {
                  fieldName: "Title",
                  sequenceNo: 1,
                  fieldType: "Input",
                  maxLength: 15,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Name 1",
                  sequenceNo: 2,
                  fieldType: "Input",
                  maxLength: 35,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Name 2",
                  sequenceNo: 3,
                  fieldType: "Input",
                  maxLength: 35,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Name 3",
                  sequenceNo: 4,
                  fieldType: "Input",
                  maxLength: 35,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Name 4",
                  sequenceNo: 5,
                  fieldType: "Input",
                  maxLength: 35,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Street",
                  sequenceNo: 6,
                  fieldType: "Input",
                  maxLength: 35,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Location",
                  sequenceNo: 7,
                  fieldType: "Input",
                  maxLength: 35,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "District",
                  sequenceNo: 8,
                  fieldType: "Input",
                  maxLength: 35,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Country/Reg",
                  sequenceNo: 9,
                  fieldType: "Drop Down",
                  maxLength: 3,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Jurisdiction",
                  sequenceNo: 10,
                  fieldType: "Drop Down",
                  maxLength: 15,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "PO Box",
                  sequenceNo: 11,
                  fieldType: "Input",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Postal Code",
                  sequenceNo: 12,
                  fieldType: "Input",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "PO Box Postcod",
                  sequenceNo: 13,
                  fieldType: "Input",
                  maxLength: 10,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Region",
                  sequenceNo: 14,
                  fieldType: "Drop Down",
                  maxLength: 3,
                  dataType: "String",
                  viewName: "Address",
                  cardName: "Address Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
              ],
            },
            Communication: {
              "Communication Data": [
                {
                  fieldName: "Language Key",
                  sequenceNo: 1,
                  fieldType: "Drop Down",
                  maxLength: 2,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Telephone 1",
                  sequenceNo: 2,
                  fieldType: "Input",
                  maxLength: 16,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Telephone 2",
                  sequenceNo: 3,
                  fieldType: "Input",
                  maxLength: 16,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Telebox number",
                  sequenceNo: 4,
                  fieldType: "Input",
                  maxLength: 15,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Telex number",
                  sequenceNo: 5,
                  fieldType: "Input",
                  maxLength: 30,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Fax Number",
                  sequenceNo: 6,
                  fieldType: "Input",
                  maxLength: 31,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Teletex number",
                  sequenceNo: 7,
                  fieldType: "Input",
                  maxLength: 30,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Printer Destination",
                  sequenceNo: 8,
                  fieldType: "Input",
                  maxLength: 4,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
                {
                  fieldName: "Data line",
                  sequenceNo: 9,
                  fieldType: "Input",
                  maxLength: 14,
                  dataType: "String",
                  viewName: "Communication",
                  cardName: "Communication Data",
                  cardSeq: 1,
                  visibility: "Optional",
                  value: "",
                },
              ],
            },
          },
        ],
      },
    };
    const costCenterValue = responseFromApi.body.tableData.map((item, index) =>
      console.log("funncy", item.costCenter, index)
    );
  
    const row = responseFromApi?.body?.tableData.map((item) => {
      console.log("type", typeof item, item);
      if (typeof item === "object") {
        console.log("item", item);
        let arrayObj = Object.entries(item);
        console.log("aray", arrayObj, arrayObj.length);
        let tempArray = [];
        for (let index = 0; index < arrayObj.length; index++) {
          console.log("binay", index, arrayObj[index], typeof arrayObj[index][1]);
  
          if (
            typeof arrayObj[index][1] === "object" &&
            arrayObj[index][1] != null
          ) {
            console.log("binay p", arrayObj[index][1]);
            tempArray.push(Object.entries(arrayObj[index][1]));
          }
        }
        return tempArray;
      } else {
        return null;
      }
    });
  
    console.log("nihar");
  
    const initialRows = row
      .filter((item) => item != null)
      .map((item) => item.flat())
      .map((item) => {
        console.log("priti", item);
        // array.forEach(element => {
        //   element
        // });
        return item.map((innerItem) => innerItem[1]);
      })
      .map((item) => item.flat())
      ?.map((field, index) => {
        console.log("initialRow", field, index);
  
        return {
          id: index,
          costCenter: responseFromApi.body.tableData[index].costCenter,
          controllingArea: responseFromApi.body.controlingArea,
          description: field?.find(
            (innerField) => innerField?.fieldName === "Description"
          )?.value,
          personResponsible: field?.find(
            (innerField) => innerField?.fieldName === "Person Responsible"
          )?.value,
          companyCode: field?.find(
            (innerField) => innerField?.fieldName === "Comp Code"
          )?.value,
          profitCenter: field?.find(
            (innerField) => innerField?.fieldName === "Profit Center"
          )?.value,
          costCenterCategory: field?.find(
            (innerField) => innerField?.fieldName === "Cost Center Category"
          )?.value,
          validFrom: responseFromApi.body.tableData[index].validFrom,
          validTo: responseFromApi.body.tableData[index].validTo,
        };
      });
    console.log("walker", initialRows);
    const columns = [
      {
        field: "id",
        headerName: "",
        editable: false,
        flex: 1,
        renderCell: (params) => <Checkbox checked={params.value === true} />,
      },
      {
        field: "costCenter",
        headerName: "Cost Center",
        editable: false,
        flex: 1,
      },
      {
        field: "controllingArea",
        headerName: "Controlling Area",
        editable: false,
        flex: 1,
      },
      {
        field: "description",
        headerName: "Description",
        editable: false,
        flex: 1,
      },
      {
        field: "personResponsible",
        headerName: "Person Responsible",
        editable: false,
        flex: 1,
      },
      {
        field: "companyCode",
        headerName: "Company Code",
        editable: false,
        flex: 1,
      },
      {
        field: "profitCenter",
        headerName: "Profit Center",
        editable: false,
        flex: 1,
      },
      {
        field: "costCenterCategory",
        headerName: "Cost Center Category",
        editable: false,
        flex: 1,
      },
  
      {
        field: "validFrom",
        headerName: "Valid From",
        editable: false,
        flex: 1,
      },
      {
        field: "validTo",
        headerName: "Valid To",
        editable: false,
        flex: 1,
      },
    ];
    const onRowsSelectionHandler = (ids) => {
      //Selected Columns stored here
      const selectedRowsData = ids.map((id) =>
        rmDataRows.find((row) => row.id === id)
      );
      var compCodes = selectedRowsData.map((row) => row.company);
      var companySet = new Set(compCodes);
      var vendors = selectedRowsData.map((row) => row.vendor);
      var vendorSet = new Set(vendors);
      var paymentTerms = selectedRowsData.map((row) => row.paymentTerm);
      var paymentTermsSet = new Set(paymentTerms);
      if (selectedRowsData.length > 0) {
        if (companySet.size === 1) {
          if (vendorSet.size === 1) {
            if (paymentTermsSet.size !== 1) {
              setDisableButton(true);
              setMessageDialogTitle("Error");
              setMessageDialogMessage(
                "Invoice cannot be generated for vendors with different payment terms"
              );
              setMessageDialogSeverity("danger");
              handleMessageDialogClickOpen();
            } else setDisableButton(false);
          } else {
            setDisableButton(true);
            setMessageDialogTitle("Error");
            setMessageDialogMessage(
              "Invoice cannot be generated for multiple suppliers"
            );
            setMessageDialogSeverity("danger");
            handleMessageDialogClickOpen();
          }
        } else {
          setDisableButton(true);
          setMessageDialogTitle("Error");
          setMessageDialogMessage(
            "Invoice cannot be generated for multiple companies"
          );
          setMessageDialogSeverity("danger");
          handleMessageDialogClickOpen();
        }
      } else {
        setDisableButton(true); //Enable the Create E-Invoice button when at least one row is selected and no two companys or vendors are same
      }
      setSelectedRow(ids); //Setting the ids(PO Numbers) of selected rows
      setSelectedDetails(selectedRowsData); //Setting the entire data of a selected row
    };
  
    return (
      <div>
        <div style={{ ...outermostContainer, backgroundColor: "#FAFCFF" }}>
          <Grid item sx={outermostContainer_Information}>
            <Grid item md={5} sx={{ display: "flex" }}>
              <Grid>
                <IconButton
                  // onClick={handleBacktoRO}
                  color="primary"
                  aria-label="upload picture"
                  component="label"
                  sx={iconButton_SpacingSmall}
                >
                  <ArrowCircleLeftOutlinedIcon
                    style={{ height: "1em", width: "1em", color: "#000000" }}
                    // sx={{
                    //   fontSize: "1.5em",
                    //   color: "#000000",
                    // }}
                    onClick={() => {
                      navigate("/masterDataCockpit/materialMaster/materialSingle");
                      dispatch(clearPayload());
                      dispatch(clearOrgData());
                    }}
                  />
                </IconButton>
              </Grid>
              <Grid>
                <Typography variant="h3">
                  <strong>Create Multiple Materials</strong>
                </Typography>
                <Typography variant="body2" color="#777">
                  This view creates multiple material
                </Typography>
              </Grid>
            </Grid>
          </Grid>
  
          <Grid item sx={{ position: "relative" }}>
            <Stack>
              <ReusableTable
                isLoading={isLoading}
                width="100%"
                title={"Material Master List (" + initialRows.length + ")"}
                rows={initialRows}
                columns={columns}
                pageSize={10}
                getRowIdValue={"id"}
                hideFooter={false}
                checkboxSelection={false}
                disableSelectionOnClick={true}
                status_onRowSingleClick={true}
                // onRowsSelectionHandler={onRowsSelectionHandler}
                callback_onRowSingleClick={(params) => {
                  console.log("paramss", params);
                  // Adjust this based on your data structure
                  const costCenter = params.row.costCenter;
                  //condition to send a row data
                  const dataToSend = responseFromApi.body.tableData.find(
                    (item) => item.costCenter === costCenter
                  );
  
                  console.log(dataToSend, "pppp");
  
                  navigate(
                    `/masterDataCockpit/costCenter/createMultipleCostCenter/editMultipleCostCenter/${costCenter}`,
                    {
                      state: { rowViewData: dataToSend, selectedRow: params.row },
                    }
                  );
                }}
                // setShowWork={setShowWork}
                stopPropagation_Column={"action"}
                status_onRowDoubleClick={true}
              />
            </Stack>
          </Grid>
        </div>
        <Paper
          sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
          elevation={2}
        >
          <BottomNavigation
            className="container_BottomNav"
            showLabels
            sx={{
              display: "flex",
  
              justifyContent: "flex-end",
            }}
            value={value}
            // onChange={(newValue) => {
            // setValue(newValue);
            // }}
          >
          
              {/* <Button
                variant="contained"
                size="small"
                sx={{ ...button_Primary, mr: 1 }}
                // onClick={onSaveButtonClick}
                
              >
                Save As Draft
              </Button> */}
           
  
         
  
          
              {/* <Button
                variant="contained"
                size="small"
                sx={{ ...button_Primary }}
                // onClick={handleSubmitForReview}
              >
                Submit for Review
              </Button> */}
            
  
           
              <Button
                variant="contained"
                size="small"
                sx={{ ...button_Primary }}
                // onClick={handleSubmitForApproval}
              >
                Submit for Approval
              </Button>
           
          </BottomNavigation>
        </Paper>
      </div>
    );
  };
  
  export default CreateMultipleMaterial;
  