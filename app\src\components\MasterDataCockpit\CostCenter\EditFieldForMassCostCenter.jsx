import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  TextField,
  Grid,
  Autocomplete,
  IconButton,
  FormControlLabel,
  Checkbox,
  ListItem,
} from "@mui/material";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import SaveIcon from "@mui/icons-material/Save";
import { DateRangePicker } from "rsuite";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
// import { setPayload } from "../../app/editPayloadSlice";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";

import { useLocation } from "react-router-dom";
import { setPayload, setPayloadWhole } from "../../../app/editPayloadSlice";
import { setMultipleMaterial } from "../../../app/initialDataSlice";
import { setCCRequiredFields, setMultipleCostCenterData } from "../../../app/costCenterTabsSlice";
import { doAjax } from "../../Common/fetchService";
import { destination_CostCenter } from "../../../destinationVariables";
import { setDropDown } from "../../../app/dropDownDataSlice";

function transformApiData(apiValue, dropDownData) {
  if (Array.isArray(dropDownData)) {
    const matchingOption = dropDownData.find(
      (option) => option.code === apiValue
    );
    return matchingOption || "";
  } else {
    return "";
  }
}

const EditableFieldForMassCostCenter = ({
  label,
  value,
  length,
  fieldGroup,
  isEditMode,
  visibility,
  isExtendMode,
  selectedRowData,
  type,
  activeTabIndex,
  ccTabs,
}) => {
  const [editedValue, setEditedValue] = useState(value);
  const [changeStatus, setChangeStatus] = useState(false);
  const dropDownData = useSelector((state) => state.AllDropDown.dropDown);
  const costCenterData = useSelector(
    (state) => state.costCenter.MultipleCostCenterData
  );
  const dispatch = useDispatch();
  const transformedValue = transformApiData(editedValue, dropDownData);
  const location = useLocation();
  const editField = useSelector((state) => state.edit.payload);
  let activeRow = {};
  let activeIndex = -1;

  for (let index = 0; index < costCenterData?.length; index++) {
    // console.log("ccdata", costCenterData.tableData[index]);
    if (costCenterData[index].costCenter === selectedRowData) {
      activeRow = costCenterData[index];
      activeIndex = index;
      break;
    }
  }
  let activeTabName = ccTabs[activeTabIndex];
  console.log("activerow", activeIndex, activeRow, activeTabName);

  const getValueForFieldName = (data, fieldName) => {
    const field = data?.find((field) => field?.fieldName === fieldName);
    return field ? field.value : "";
  };
  const costCenterInnerData = costCenterData[activeIndex];
  // console.log("puppy", costCenterInnerData[activeTabName][fieldGroup]);
  console.log(costCenterInnerData,"costCenterInnerData")
  // console.log(
  //   "fieldData",
  //   getValueForFieldName(costCenterInnerData[activeTabName][fieldGroup], label)
  // );

  useEffect(() => {
    if (
      visibility === "0" ||
      visibility === "Required"
    ) {
      //dispatch(setCCRequiredFields(key));
      if(activeTabName === 'Basic Data'){
        console.log("active_tab_coming")
        dispatch(setCCRequiredFields(key));
        dispatch(setCCRequiredFields(["Name"]));
      }else{
        dispatch(setCCRequiredFields(key));
      }
      
    }
  }, [activeTabName]);

  const onEdit = (label, newValue) => {
    console.log("label", label, newValue);

    dispatch(
      setPayload({
        keyname: key
          .replaceAll("(", "")
          .replaceAll(")", "")
          .replaceAll("/", "")
          .replaceAll("-", "")
          .replaceAll(".", "")
          .split(" ")
          .join(""),
        data: newValue,
      })
    )

    let changedData = costCenterData?.map((item, index) => {
      let activeTabName = ccTabs[activeTabIndex];
      if (index === activeIndex) {
        let temp0 = item?.viewData;
        let temp = item?.viewData[activeTabName]; //Basic Data
        console.log("temp", temp);
        let temp2 = item?.viewData[activeTabName][fieldGroup]; //names
        console.log("temp2", temp2);
        return {
          ...item,
          viewData: {
            ...temp0,
            [activeTabName]: {
              ...temp,
              [fieldGroup]: temp2?.map((innerItem) => {
                if (innerItem.fieldName === label) {
                  return { ...innerItem, value: newValue };
                  // console.log('...inneritem', ...innerItem)
                } else {
                  return innerItem;
                }
              }),
            },
          },
        };
      } else {
        return item;
      }
    });
    dispatch(setMultipleCostCenterData(changedData))
  };

  let key = label
    .replaceAll("(", "")
    .replaceAll(")", "")
    .replaceAll("/", "")
    .replaceAll("-", "")
    .replaceAll(".", "")
    .split(" ")
    .join("");
    
  useEffect(() => {
    setEditedValue(value);
  }, [value]);

  const getRegion = (value) => {
    // console.log("value",value.code);
    const hSuccess = (data) => {
      console.log("value", data);
      dispatch(setDropDown({ keyName: "Region", data: data.body }));
    };
    const hError = (error) => {
      console.log(error, "error in dojax");
    };
    doAjax(
      `/${destination_CostCenter}/data/getRegionBasedOnCountry?country=${value}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getCurrency = (value) => {
    console.log("compcode1", value);
    const hSuccess = (data) => {
      console.log(data,"data145")
      dispatch(setDropDown({ keyName: "Currency", data: data.body }));
    };
    const hError = (error) => {
      console.log(error, "error in dojax");
    };
    doAjax(
      `/${destination_CostCenter}/data/getCurrency?companyCode=${value}`,
      "get",
      hSuccess,
      hError
    );
  };

  useEffect(() => {
    // console.log('qwerty',value)
    if (label === "Country/Reg") {
      getRegion(editedValue);

      // console.log('qwerty',editedValue)
    }
    if (label === "Company Code") {
      getCurrency(editedValue);

      // console.log('qwerty',editedValue)
    }
  }, []);
 



  return (
    <Grid item>
      <Stack>
        {isEditMode ? (
          <>
            <Typography variant="body2" color="#777">
              {label}{" "}
              {visibility === "Required" || visibility === "0" ? (
                <span style={{ color: "red" }}>*</span>
              ) : (
                ""
              )}
            </Typography>
            {type === "Drop Down" ? (
              <Autocomplete
                options={dropDownData[key] ?? []}
                value={
                  (getValueForFieldName(
                    costCenterInnerData?.viewData[activeTabName][fieldGroup],
                    label
                  ) &&
                    dropDownData[key]?.filter(
                      (x) =>
                        x.code ===
                        getValueForFieldName(
                          costCenterInnerData?.viewData[activeTabName][fieldGroup],
                          label
                        )
                    ))[0] ||
                  ""
                }
                onChange={(event, newValue) => {
                  if (label === "Country/Reg") {
                    getRegion(newValue.code);
                  }
                  if (label === "Comp Code") {
                    getCurrency(newValue.code);
                  }
                  onEdit(label, newValue?.code);
                  setEditedValue(newValue?.code);
                  setChangeStatus(true);
                }}
                getOptionLabel={(option) => {
                 
                  return option === "" || option?.code === ""
                    ? ""
                    : `${option?.code} - ${option?.desc}` ?? "";
                }}
                // isOptionEqualToValue={(a,b)=>{ return a.code===b.code}}
                renderOption={(props, option) => {
                  console.log("option vakue", option);
                  return (
                    <li {...props}>
                      <Typography style={{ fontSize: 12 }}>
                        {`${option?.code} - ${option?.desc}`}
                      </Typography>
                    </li>
                  );
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    variant="outlined"
                    size="small"
                    label={null}
                    placeholder={label}
                  />
                )}
              />
            ) : type === "Input" ? (
              <TextField
                variant="outlined"
                size="small"
                value={getValueForFieldName(
                  costCenterInnerData?.viewData[activeTabName][fieldGroup],
                  label
                ).toUpperCase()}
                inputProps={{maxLength: length}}
                onChange={(event) => {
                  console.log("event", event.target.value);
                  
                  //onEdit(label,event.target.value);
                  //setEditedValue(event.target.value);
                  const newValue = event.target.value;
                  if (newValue.length > 0 && newValue[0] === " ") {
                    onEdit(label,newValue.trimStart())
                  } else {
                    let costCenterUpperCase = newValue.toUpperCase();
                    onEdit(label,costCenterUpperCase)
                  }
                  
                }}
                placeholder={label}
              />
            ) : type === "Calendar" ? (
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  slotProps={{ textField: { size: "small" } }}
                  // value={valueFromPayload[props?.keyName]}
                  placeholder="Select Date Range"
                  // onChange={(newValue) =>
                  //   dispatch(
                  //     setPayload({
                  //       keyName: props.keyName,
                  //       data: "/Date(" + Date.parse(newValue) + ")/",
                  //     })
                  //   )
                  // }
                  // required={
                  //   props.details.visibility === "0" ||
                  //   props.details.visibility === "Required"
                  // }
                />
              </LocalizationProvider>
            ) : type === "Radio Button" ? (
              <Checkbox
                sx={{ borderRadius: "0 !important" }}
                checked={editedValue}
                onChange={(event, newValue) => {
                  onEdit(label, newValue);
                  setEditedValue(newValue);
                }}
              />
            ) : (
              ""
            )}
          </>
        ) : (
          <>
            <>
              <Typography variant="body2" color="#777">
                {label}{" "}
                {visibility === "Required" || visibility === "0" ? (
                  <span style={{ color: "red" }}>*</span>
                ) : (
                  ""
                )}
              </Typography>
              <Typography variant="body2" fontWeight="bold">
                {/* {editedValue} */}
                {type === "Radio Button" ? (
                  <Checkbox
                    sx={{ padding: 0 }}
                    checked={editedValue}
                    disabled
                  />
                ) : (
                  editedValue
                )}
              </Typography>
            </>
          </>
        )}
      </Stack>
    </Grid>
  );
};

export default EditableFieldForMassCostCenter;
