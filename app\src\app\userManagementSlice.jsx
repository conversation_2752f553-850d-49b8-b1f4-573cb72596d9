import { createSlice } from "@reduxjs/toolkit";

export const userManagementSlice = createSlice({
  name: "userManagement",
  initialState: {
    userData: {
      id: "",
      user_id: "",
      firstName: "",
      lastName: "",
      emailId: "",
      displayName: "",
      userName: "",
      role: "",
      purchasingGroupId: "",
      purchasingGroupName: "",
      supplierId: "",
      supplierName: "",
    },
    taskData: {},
    users: [],
    externalIdpUsers: [],
    internalIdpUsers: [],
    roles: [],
    roleTemplates: [],
    applications: [],
    entities: [],
    activities: [],
    groups: [],
    systems: [],
    entitiesAndActivities: {},
    currentSAPSystem:"",
    responseMessage: {
      open: false,
      status: "",
      message: "",
    },
  },
  reducers: {
    setUsers: (state, action) => {
      state.users = action.payload;
    },
    setTaskData: (state, action) => {
      state.taskData = action.payload;
    },
    setExternalIdpUsers: (state, action) => {
      state.externalIdpUsers = action.payload;
    },
    setInternalIdpUsers: (state, action) => {
      state.internalIdpUsers = action.payload;
    },
    setRoles: (state, action) => {
      state.roles = action.payload;
    },
    setRoleTemplates: (state, action) => {
      state.roleTemplates = action.payload;
    },
    setGroups: (state, action) => {
      state.groups = action.payload;
    },
    setApplications: (state, action) => {
      state.applications = action.payload;
    },
    setEntities: (state, action) => {
      state.entities = action.payload;
    },
    setActivities: (state, action) => {
      state.activities = action.payload;
    },
    setSystems: (state, action) => {
      state.systems = action.payload;
    },
    setResponseMessage: (state, action) => {
      state.responseMessage = action.payload;
    },
    setEntitiesAndActivities: (state, action) => {
      state.entitiesAndActivities = action.payload;
    },
    setUserDetails: (state, action) => {
      state.userData = action.payload;
    },
    setCurrentSAPSystem: (state, action) => {
      state.currentSAPSystem = action.payload;
    },
  },
});

export const {
  setUsers,
  setExternalIdpUsers,
  setInternalIdpUsers,
  setRoles,
  setRoleTemplates,
  setApplications,
  setEntities,
  setActivities,
  setGroups,
  setSystems,
  setResponseMessage,
  setEntitiesAndActivities,
  setUserDetails,
  setTaskData,
  setCurrentSAPSystem
} = userManagementSlice.actions;

export default userManagementSlice.reducer;
