import React from "react";
import { Route } from "react-router-dom";
const RequestBench = React.lazy(() => import("../../components/RequestBench/RequestBench"));
const CreateNewRequest = React.lazy(() => import("../../components/RequestBench/CreateNewRequest"));
const ErrorHistory = React.lazy(() => import("../../components/RequestBench/ErrorHistory"));
const RequestHistory = React.lazy(() => import("../../components/RequestHistory/RequestHistory"));
const Upload = React.lazy(() => import("../../components/RequestBench/RequestPages/Upload"))
 
 

export const RequestBenchRoutes = [
  <Route path="/requestBench" element={<RequestBench />} />,
  <Route path="/requestBench/createRequest" element={<CreateNewRequest />} />,
  <Route path="/requestBench/RequestHistory" element={<RequestHistory />} />,
  <Route path="/requestBench/errorHistory" element={<ErrorHistory />} />,
  <Route path="/configCockpit/documentConfigurations" element={<Upload />} />
];