import {
  Backdrop,
  BottomNavigation,
  Box,
  Button,
  Checkbox,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  Paper,
  Stack,
  Tab,
  Tabs,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import CloseIcon from "@mui/icons-material/Close";
import {
  iconButton_SpacingSmall,
  outermostContainer,
  button_Primary,
  outerContainer_Information,
  outermostContainer_Information,
} from "../../common/commonStyles";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import ReusableTable from "../../common/ReusableTable";
import { useNavigate, useLocation } from "react-router-dom";
import {
  destination_DocumentManagement,
  destination_ProfitCenter,
} from "../../../destinationVariables";
import { useDispatch, useSelector } from "react-redux";
import { doAjax } from "../../Common/fetchService";
import ReusableDialog from "../../Common/ReusableDialog";
import ReusableSnackBar from "../../Common/ReusableSnackBar";
import moment from "moment/moment";
import lookup from "../../../data/lookup.json";
import LoadingComponent from "../../Common/LoadingComponent";
import { setDropDown } from "../../../app/dropDownDataSlice";
import { idGenerator } from "../../../functions";
import AttachFileOutlinedIcon from "@mui/icons-material/AttachFileOutlined";
import ReusableAttachementAndComments from "../../Common/ReusableAttachmentAndComments/ReusableAttachementAndComments";

const CreateMultipleProfitCenter = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [blurLoading, setBlurLoading] = useState(false);
  const [value, setValue] = useState("1");
  const [selectedRows, setSelectedRows] = useState([]);
  const [messageDialogTitle, setMessageDialogTitle] = useState(false);
  const [validateFlag, setValidateFlag] = useState(false);
  const [successMsg, setsuccessMsg] = useState(false);
  const [messageDialogExtra, setMessageDialogExtra] = useState(false);
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [messageDialogSeverity, setMessageDialogSeverity] = useState(false);
  const [messageDialogOK, setMessageDialogOK] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [profitValidationError, setProfitValidationErrors] = useState([]);
  const [submitForReviewDisabled, setSubmitForReviewDisabled] = useState(true);
  const [openCorrectionDialog, setOpenCorrectionDialog] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [pcNumber, setPcNumber] = useState("");
  const [remarks, setRemarks] = useState("");
  const [testRun, setTestRun] = useState(false);
  const [directMatchedProfitCenters, setDirectMatchedProfitCenters] = useState(
    []
  );
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const multipleProfitData = useSelector(
    (state) => state.profitCenter.MultipleProfitCenterData
  );

  const appSettings = useSelector((state) => state.appSettings);
  let massHandleType = useSelector(
    (state) => state.profitCenter.handleMassMode
  );
  console.log("massHandleType", massHandleType);
  let userData = useSelector((state) => state.userManagement.userData);

  // Loader and lookup for independent apis start
  const [apiCount, setApiCount] = useState(0);
  const fetchDynamicApiData = (keyName, endPoint) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: keyName, data: data.body }));
      // setIsLoading(false);
      setApiCount((prev) => prev + 1);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/data/${endPoint}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getAllLookups = () => {
    lookup?.profitCenter?.map((item) => {
      fetchDynamicApiData(item?.keyName, item?.endPoint);
    });
  };
  const loaderCount = () => {
    if (apiCount == lookup?.profitCenter?.length) {
      setIsLoading(false);
    } else {
      setIsLoading(true);
    }
  };
  useEffect(() => {
    loaderCount();
  }, [apiCount]);

  // Loader and lookup for independent apis end

  useEffect(() => {
    setPcNumber(idGenerator("PC"));
  }, []);

  useEffect(() => {
    getAllLookups();
  }, []);

  const columns = [
    {
      field: "profitCenter",
      headerName: "Profit Center",
      editable: false,
      flex: 1,
      renderCell: (params) => {
        const isDirectMatch = profitValidationError.find(element=> element.profitCenter===params.value);
        console.log(isDirectMatch, "isDirectMatch")
        console.log(params, "params")
  
        if (isDirectMatch && isDirectMatch.code === 400) {
          return (
            <Typography sx={{ fontSize: "12px", color: "red" }}>
              {params.value}
            </Typography>
          );
        } else {
          return (
            <Typography sx={{ fontSize: "12px" }}>
              {params.value}
            </Typography>
          );
        }
      },
    },
    {
      field: "controllingArea",
      headerName: "Controlling Area",
      editable: false,
      flex: 1,
    },
    {
      field: "profitCenterName",
      headerName: "Profit Center Name",
      editable: false,
      flex: 1,
      renderCell: (params) => {
        const isDirectMatch = directMatchedProfitCenters.includes(
          params.row.profitCenterName
        );
        return (
          <Typography
            sx={{ fontSize: "12px", color: isDirectMatch ? "red" : "inherit" }}
          >
            {params.value}
          </Typography>
        );
      },
    },
    {
      field: "personResponsible",
      headerName: "Person Responsible",
      editable: false,
      flex: 1,
    },
    {
      field: "profitCenterGroup",
      headerName: "Profit Center Group",
      editable: false,
      flex: 1,
    },

    {
      field: "analysisPeriodFrom",
      headerName: "Ananlysis Period From",
      editable: false,
      flex: 1,
      renderCell: (params) => {
        return (
          <Typography sx={{ fontSize: "12px" }}>
            {moment(params.row.analysisPeriodFrom).format(
              appSettings?.dateFormat
            )}
          </Typography>
        );
      },
    },
    {
      field: "analysisPeriodTo",
      headerName: "Analysis Period To",
      editable: false,
      flex: 1,
      renderCell: (params) => {
        return (
          <Typography sx={{ fontSize: "12px" }}>
            {moment(params.row.analysisPeriodTo).format(
              appSettings?.dateFormat
            )}
          </Typography>
        );
      },
    },
  ];
  console.log("multipleProfitData", multipleProfitData);
  const handleSnackBarOpen = () => {
    setopenSnackbar(true);
  };
  const handleOpenDialog = () => {
    setOpenDialog(true);
  };
  const handleCloseDialog = () => {
    setOpenDialog(false);
  };
  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };
  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };
  const handleMessageDialogNavigate = () => {
    // navigate("/masterDataCockpit/materialMaster/materialSingle");
  };
  const handleSnackBarClose = () => {
    if (validateFlag) {
      setopenSnackbar(false);
      setValidateFlag(false);
    } else {
      setopenSnackbar(false);
      navigate("/masterDataCockpit/profitCenter");
    }
  };

  const getValueForFieldName = (data, fieldName) => {
    console.log("getvalueforfieldname", data, fieldName);
    const field = data?.find((field) => field?.fieldName === fieldName);
    return field ? field.value : "";
  };

  const handleSelectionModelChange = (selectedIds) => {
    if (selectedIds.length > 0) {
      setTestRun(true);
      console.log("selectedIds1", selectedIds);
    } else {
      setTestRun(false);
    }
    console.log("selectedIds", selectedIds);
    setSelectedRows(selectedIds);
    // setTestRun(true);
  };

  let compcodeData =
    multipleProfitData[0]?.viewData?.["Comp Codes"]?.[
      "Company Code Assignment for Profit Center"
    ];
  const initialRows = multipleProfitData?.map((pCenter, index) => {
    const headerData = pCenter;
    const basicData = pCenter?.viewData?.["Basic Data"] || {};
    return {
      id: index,
      profitCenter: headerData?.profitCenter,
      controllingArea: headerData?.controllingArea,
      profitCenterName:
        basicData["General Data"].find((field) => field?.fieldName === "Name")
          ?.value || "",
      personResponsible:
        basicData["General Data"].find(
          (field) => field?.fieldName === "Person Responsible"
        )?.value || "",
      profitCenterGroup:
        basicData["General Data"].find(
          (field) => field?.fieldName === "Profit Ctr Group"
        )?.value || "",
      analysisPeriodFrom:
        moment(
          basicData["General Data"].find(
            (field) => field?.fieldName === "Analysis Period From"
          )?.value
        ).format(appSettings?.dateFormat) || "",
      analysisPeriodTo:
        moment(
          basicData["General Data"].find(
            (field) => field?.fieldName === "Analysis Period To"
          )?.value
        ).format(appSettings?.dateFormat) || "",
    };
  });

  let tocompcode = _.zip(
    compcodeData[0].value,
    compcodeData[1].value,
    compcodeData[2].value
  ).map((x) => {
    return {
      CompCodeID: 0,
      CompCode: x[0],
      CompanyName: x[1],
      AssignToPrctr: x[2] == true ? "X" : "",
      Venture: "",
      RecInd: "",
      EquityTyp: "",
      JvOtype: "",
      JvJibcl: "",
      JvJibsa: "",
    };
  });

  var payloadmapping = multipleProfitData.map((x) => {
    console.log("samsung", x);
    return {
      ProfitCenterID: "",
      Action: massHandleType === "Create" ? "I" : "U",
      RequestID: "",
      TaskStatus: "",
      TaskId: "",
      remarks: remarks ? remarks : "",
      ReqCreatedBy: userData?.user_id,
      ReqCreatedOn: userData?.createdOn
        ? "/Date(" + userData?.createdOn + ")/"
        : "",
      RequestStatus: "",
      CreationId: "",
      EditId: "",
      DeleteId: "",
      MassCreationId: "",
      MassEditId: "",
      MassDeleteId: "",
      RequestType: massHandleType === "Create" ? "Mass Create" : "Mass Change",
      MassRequestStatus: "",
      PrctrName: getValueForFieldName(
        x?.viewData["Basic Data"]?.["General Data"],
        "Name"
      ),
      LongText: getValueForFieldName(
        x?.viewData["Basic Data"]?.["General Data"],
        "Long Text"
      ),
      InChargeUser: getValueForFieldName(
        x?.viewData["Basic Data"]?.["General Data"],
        "User Responsible"
      ),
      InCharge: getValueForFieldName(
        x?.viewData["Basic Data"]?.["General Data"],
        "Person Responsible"
      ),
      Department: getValueForFieldName(
        x?.viewData["Basic Data"]?.["General Data"],
        "Department"
      ),
      PrctrHierGrp: getValueForFieldName(
        x?.viewData["Basic Data"]?.["General Data"],
        "Profit Ctr Group"
      ),
      Segment: getValueForFieldName(
        x?.viewData["Basic Data"]?.["General Data"],
        "Segment"
      ),
      LockInd:
        getValueForFieldName(
          x?.viewData["Indicators"]?.["Indicator"],
          "Lock indicator"
        ) === true
          ? "X"
          : "",
      Template: getValueForFieldName(
        x?.viewData["Indicators"]?.["Formula Planning"],
        "Form. Planning Temp"
      ),
      Title: getValueForFieldName(
        x?.viewData["Address"]?.["Address Data"],
        "Title"
      ),
      Name1: getValueForFieldName(
        x?.viewData["Address"]?.["Address Data"],
        "Name 1"
      ),
      Name2: getValueForFieldName(
        x?.viewData["Address"]?.["Address Data"],
        "Name 2"
      ),
      Name3: getValueForFieldName(
        x?.viewData["Address"]?.["Address Data"],
        "Name 3"
      ),
      Name4: getValueForFieldName(
        x?.viewData["Address"]?.["Address Data"],
        "Name 4"
      ),
      Street: getValueForFieldName(
        x?.viewData["Address"]?.["Address Data"],
        "Street"
      ),
      City: getValueForFieldName(
        x?.viewData["Address"]?.["Address Data"],
        "City"
      ),
      District: getValueForFieldName(
        x?.viewData["Address"]?.["Address Data"],
        "District"
      ),
      Country: getValueForFieldName(
        x?.viewData["Address"]?.["Address Data"],
        "Country/Reg."
      ),
      Taxjurcode: getValueForFieldName(
        x?.viewData["Address"]?.["Address Data"],
        "Tax Jur."
      ),
      PoBox: getValueForFieldName(
        x?.viewData["Address"]?.["Address Data"],
        "P.O.Box"
      ),
      PostlCode: getValueForFieldName(
        x?.viewData["Address"]?.["Address Data"],
        "Postal Code"
      ),
      PobxPcd: getValueForFieldName(
        x?.viewData["Address"]?.["Address Data"],
        "PO Box PCode"
      ),
      Region: getValueForFieldName(
        x?.viewData["Address"]?.["Address Data"],
        "Region"
      ),
      Langu: getValueForFieldName(
        x?.viewData["Communication"]?.["Communication Data"],
        "Language"
      ),
      Telephone: getValueForFieldName(
        x?.viewData["Communication"]?.["Communication Data"],
        "Telephone 1"
      ),
      Telephone2: getValueForFieldName(
        x?.viewData["Communication"]?.["Communication Data"],
        "Telephone 2"
      ),
      Telebox: getValueForFieldName(
        x?.viewData["Communication"]?.["Communication Data"],
        "Telebox"
      ),
      Telex: getValueForFieldName(
        x?.viewData["Communication"]?.["Communication Data"],
        "Telex"
      ),
      FaxNumber: getValueForFieldName(
        x?.viewData["Communication"]?.["Communication Data"],
        "Fax Number"
      ),
      Teletex: getValueForFieldName(
        x?.viewData["Communication"]?.["Communication Data"],
        "Teletex"
      ),
      Printer: getValueForFieldName(
        x?.viewData["Communication"]?.["Communication Data"],
        "Printer name"
      ),
      DataLine: getValueForFieldName(
        x?.viewData["Communication"]?.["Communication Data"],
        "Data line"
      ),
      ProfitCenter: x?.profitCenter,
      ControllingArea: x?.controllingArea,
      ValidfromDate: getValueForFieldName(
        x?.viewData["Basic Data"]?.["General Data"],
        "Analysis Period From"
      ),
      ValidtoDate: getValueForFieldName(
        x?.viewData["Basic Data"]?.["General Data"],
        "Analysis Period To"
      ),
      Testrun: testRun,
      Countryiso: "",
      LanguIso: "",
      Logsystem: "",
      ToCompanycode: _.zip(
        x?.viewData?.["Comp Codes"]?.[
          "Company Code Assignment for Profit Center"
        ][0].value,
        x?.viewData?.["Comp Codes"]?.[
          "Company Code Assignment for Profit Center"
        ][1].value,
        x?.viewData?.["Comp Codes"]?.[
          "Company Code Assignment for Profit Center"
        ][2].value
      ).map((x) => {
        return {
          CompCodeID: "",
          CompCode: x[0],
          CompanyName: x[1],
          AssignToPrctr: x[2] === true ? "X" : "X",
          Venture: "",
          RecInd: "",
          EquityTyp: "",
          JvOtype: "",
          JvJibcl: "",
          JvJibsa: "",
        };
      }),
    };
  });

  const checkPCNameEditorNot =(duplicatePCNameViewData,duplicatePCName)=>{

    return duplicatePCNameViewData.every(element => !duplicatePCName.includes(element))

  }

  const handleSubmitForReview = () => {
    const selectedData = initialRows.filter((_, index) =>
      selectedRows.includes(index)
    );
    console.log("selectedData", selectedData);
    const selectedProfitCenterRows = selectedData.map((x) => ({
      // console.log("Data", x)
      ...payloadmapping[x?.id],
    }));
    let payload = payloadmapping;
    payload = selectedProfitCenterRows;
    console.log("selectedProfitCenterRows", selectedProfitCenterRows);
    const hSuccess = (data) => {
      // setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Mass Profit Center Sent for Review with ID NPM${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setBlurLoading(false);
        // setIsLoading(false);
        const secondApiPayload = {
          artifactId: pcNumber,
          createdBy: userData?.emailId,
          artifactType: "ProfitCenter",
          requestId: `NPM${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting the Profit Center for Review "
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setBlurLoading(false);
        // setIsLoading(false);
      }
      handleClose();
      setBlurLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/massAction/profitCentersSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const onValidateProfitCenter = () => {
    setBlurLoading(true);
    const selectedData = initialRows.filter((_, index) =>
      selectedRows.includes(index)
    );
    console.log("selectedData", selectedData);
    const selectedProfitCenterRows = selectedData.map((x) => ({
      ...payloadmapping[x?.id],
    }));
    console.log("selectedProfitCenterRows", selectedProfitCenterRows);
    const duplicateCheckPayload = [];
    const duplicatePCName=[]
    selectedProfitCenterRows.map((x) => {
      var idk = {
        coArea: x?.ControllingArea,
        name: x?.PrctrName.toUpperCase(),
      };
      duplicateCheckPayload.push(idk);
      duplicatePCName.push(x?.PrctrName.toUpperCase())
    });
    const duplicatePCNameViewData=[]
    multipleProfitData.map((pfName) =>{
      duplicatePCNameViewData.push(pfName.profitCenterName?.toUpperCase())
    })
    //console.log(duplicatePCNameViewData,duplicatePCName,"arrayofviewand")

    let isuserEditPCName=checkPCNameEditorNot(duplicatePCNameViewData,duplicatePCName)
    console.log(isuserEditPCName,"isuserEditPCName")

    console.log("duplicateCheckPayload", duplicateCheckPayload.coArea);
    console.log(duplicatePCName,"duplicatePCName")
    let payload = payloadmapping;
    payload = selectedProfitCenterRows;
    const hSuccess = (data) => {
      // setIsLoading(false);
      if (data.statusCode === 400) {
        setProfitValidationErrors(data.body);
        setDialogOpen(true);
        setBlurLoading(false);
      } else {
        setMessageDialogTitle("Create");
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `All Data has been Validated. Profit Center can be Send for Review`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
        setValidateFlag(true);
        // setBlurLoading(false);

        // Now, make the duplicate check API call
        // Ensure that the conditions for making the duplicate check API call are met
        if (
          duplicateCheckPayload.coArea !== "" ||
          duplicateCheckPayload.name !== ""
        ) {
          // payloadmapping.Toitem = duplicateCheckPayload.name;
          // if (massHandleType !== "Create" && !isuserEditPCName){
          //     setBlurLoading(false)
          // }else{

            doAjax(
              `/${destination_ProfitCenter}/alter/fetchPCDescriptionsDupliChk`,
              "post",
              hDuplicateCheckSuccess,
              hDuplicateCheckError,
              duplicateCheckPayload
            );
         // }
        }
      }
      // setBlurLoading(false);
    };

    const hDuplicateCheckSuccess = (data) => {
      console.log("dataaaa", data);
      // Handle success of duplicate check
      if (
        data.body.length === 0 ||
        !data.body.some((item) =>
          duplicateCheckPayload.some(
            //(payloadItem) => payloadItem.name.toUpperCase() === item.matches[0]
            (payloadItem) => item.matches.includes(payloadItem.name.toUpperCase() )
          )
        )
      ) {
        alert(1)
        // No direct match, enable the "Submit for Review" button
        setBlurLoading(false);
        setSubmitForReviewDisabled(false);
        setTestRun(true);
      } else {
        alert(2)
        // Handle direct match
        const directMatches = data.body.map((item) => item.matches[0]);
        setBlurLoading(false);
        setMessageDialogTitle("Duplicate Check");
        setsuccessMsg(false);
        setMessageDialogMessage(
          `There is a direct match for the Profit Center name.`
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setSubmitForReviewDisabled(true);
        setDirectMatchedProfitCenters(directMatches);
      }
    };

    const hDuplicateCheckError = (error) => {
      // Handle error of duplicate check
      console.log(error);
    };

    const hError = (error) => {
      console.log(error);
    };

    // Call the main API for validation
    doAjax(
      `/${destination_ProfitCenter}/massAction/validateMassProfitCenter`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  // const onValidateCostCenter = () => {
  // const onValidateCostCenter = () => {
  //   const selectedData = initialRows.filter((_, index) =>
  //     selectedRows.includes(index)
  //   );
  //   console.log("selectedData", selectedData);
  //   const selectedProfitCenterRows = selectedData.map((x) => ({
  //     ...payloadmapping[x?.id],
  //   }));
  //   let payload = payloadmapping;
  //   payload = selectedProfitCenterRows;
  //   // setTestRun(true);
  //   console.log("selectedProfitCenterRows", selectedProfitCenterRows);
  //   const hSuccess = (data) => {
  //     setIsLoading();
  //     if (data.statusCode === 400) {
  //       setProfitValidationErrors(data.body);
  //       setDialogOpen(true);

  //     } else {
  //       setTestRun(false);
  //       setMessageDialogTitle("Create");
  //       console.log("success");
  //       setMessageDialogTitle("Create");
  //       setMessageDialogMessage(
  //         `All Data has been Validated. Profit Center can be Send for Review`
  //       );
  //       setMessageDialogSeverity("success");
  //       setMessageDialogOK(false);
  //       setsuccessMsg(true);
  //       handleSnackBarOpen();
  //       setMessageDialogExtra(true);
  //       setIsLoading(false);
  //       setValidateFlag(true);

  //     }
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(
  //     `/${destination_ProfitCenter}/massAction/validateMassProfitCenter`,
  //     "post",
  //     hSuccess,
  //     hError,
  //     payload
  //   );
  // };

  const handleSubmitForReviewChange = () => {
    // setIsLoading(true);
    const selectedData = initialRows.filter((_, index) =>
      selectedRows.includes(index)
    );
    console.log("selectedData", selectedData);
    const selectedProfitCenterRows = selectedData.map((x) => ({
      // console.log("Data", x)
      ...payloadmapping[x?.id],
    }));
    let payload = payloadmapping;
    payload = selectedProfitCenterRows;
    console.log("selectedProfitCenterRows", selectedProfitCenterRows);
    const hSuccess = (data) => {
      // setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Mass Profit Center Sent for Review with ID CPM${data.body}`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        setBlurLoading(false);
        // setIsLoading(false);
        const secondApiPayload = {
          artifactId: pcNumber,
          createdBy: userData?.emailId,
          artifactType: "ProfitCenter",
          requestId: `CPM${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting the Profit Center for Review "
        );
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setBlurLoading(false);
        // setIsLoading(false);
      }
      handleClose();
      setBlurLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/massAction/changeProfitCentersSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
  };
  const onCostCenterSubmitRemarks = () => {
    setBlurLoading(true);
    handleRemarksDialogClose();
    if (massHandleType === "Create") {
      handleSubmitForReview();
    } else {
      handleSubmitForReviewChange();
    }
  };
  const handleRemarks = (e, value) => {
    //setRemarks(e.target.value);
    const newValue = e.target.value;
    if (newValue.length > 0 && newValue[0] === " ") {
      setRemarks(newValue.trimStart());
    } else {
      //let costCenterValue = e.target.value;
      let remarksUpperCase = newValue.toUpperCase();
      setRemarks(remarksUpperCase);
    }
  };
  // const handleDialogProceed = () => {};

  const validationColumns = [
    {
      field: "profitCenter",
      headerName: "Profit Center",
      editable: false,
      flex: 1,
      // width: 100,
    },
    {
      field: "error",
      headerName: "Error",
      editable: false,
      flex: 1,
      // width: 400,
    },
  ];
  console.log("profitValidationError", profitValidationError);
  const validationRows = profitValidationError
    ?.filter((row) => row?.code === 400)
    ?.map((item, index) => {
      if (item.code === 400) {
        return {
          id: index,
          profitCenter: item?.profitCenter,
          error: item?.status?.message,
        };
      }
    });
    console.log("validationRows",validationRows)
  const handleRemarksDialogClose = () => {
    setTestRun(true);
    setOpenCorrectionDialog(false);
  };

  const handleOpenCorrectionDialog = () => {
    setTestRun(false);
    setOpenCorrectionDialog(true);
  };

  return (
    <>
      {isLoading === true ? (
        <LoadingComponent />
      ) : (
        <div>
          <Dialog
            hideBackdrop={false}
            elevation={2}
            PaperProps={{
              sx: { boxShadow: "none" },
            }}
            open={openCorrectionDialog}
            onClose={handleRemarksDialogClose}
          >
            {/* <Grid
          container
          sx={{ display: "flex", justifyContent: "space-between" }}
        > */}
            {/* <Grid item> */}
            <DialogTitle
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF40",
                // borderBottom: "1px solid grey",
                display: "flex",
              }}
            >
              <Typography variant="h6">Remarks</Typography>

              <IconButton
                sx={{ width: "max-content" }}
                onClick={handleRemarksDialogClose}
                children={<CloseIcon />}
              />
            </DialogTitle>
            {/* </Grid> */}
            {/* </Grid> */}
            <DialogContent sx={{ padding: ".5rem 1rem" }}>
              <Stack>
                <Box sx={{ minWidth: 400 }}>
                  <FormControl sx={{ height: "auto" }} fullWidth>
                    <TextField
                      sx={{ backgroundColor: "#F5F5F5" }}
                      value={remarks}
                      onChange={handleRemarks}
                      multiline
                      placeholder={"Enter Remarks"}
                      inputProps={{maxLength: 254}}
                    ></TextField>
                  </FormControl>
                </Box>
              </Stack>
              {/* <TextField
            autoFocus
            margin="dense"
            id="name"
            label="Enter Remarks for Correction"
            type="text"
            fullWidth
            variant="standard"
            onChange={handleRemarks}
          /> */}
            </DialogContent>
            <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
              <Button
                sx={{ width: "max-content", textTransform: "capitalize" }}
                onClick={handleRemarksDialogClose}
              >
                Cancel
              </Button>
              <Button
                className="button_primary--normal"
                type="save"
                onClick={onCostCenterSubmitRemarks}
                variant="contained"
              >
                Submit
              </Button>
            </DialogActions>
          </Dialog>

          <ReusableDialog
            dialogState={openMessageDialog}
            openReusableDialog={handleMessageDialogClickOpen}
            closeReusableDialog={handleMessageDialogClose}
            dialogTitle={messageDialogTitle}
            dialogMessage={messageDialogMessage}
            handleDialogConfirm={handleMessageDialogClose}
            dialogOkText={"OK"}
            handleExtraButton={handleMessageDialogNavigate}
            dialogSeverity={messageDialogSeverity}
          />

          {successMsg && (
            <ReusableSnackBar
              openSnackBar={openSnackbar}
              alertMsg={messageDialogMessage}
              handleSnackBarClose={handleSnackBarClose}
            />
          )}

          <div style={{ ...outermostContainer, backgroundColor: "#FAFCFF" }}>
            <Grid container sx={outermostContainer_Information}>
              <Grid item md={12} sx={{ display: "flex", marginBottom: "0" }}>
                <Grid item md={11} sx={{ display: "flex" }}>
                  <Grid>
                    <IconButton
                      // onClick={handleBacktoRO}
                      color="primary"
                      aria-label="upload picture"
                      component="label"
                      sx={iconButton_SpacingSmall}
                    >
                      <ArrowCircleLeftOutlinedIcon
                        style={{
                          height: "1em",
                          width: "1em",
                          color: "#000000",
                        }}
                        // sx={{
                        //   fontSize: "1.5em",
                        //   color: "#000000",
                        // }}
                        onClick={() => {
                          navigate("/masterDataCockpit/profitCenter");
                        }}
                      />
                    </IconButton>
                  </Grid>
                  <Grid>
                    {massHandleType === "Create" ? (
                      <Grid item md={12}>
                        <Typography variant="h3">
                          <strong>Create Multiple Profit Centers</strong>
                        </Typography>
                        <Typography variant="body2" color="#777">
                          This view creates multiple Profit Centers
                        </Typography>
                      </Grid>
                    ) : (
                      <Grid item md={12}>
                        <Typography variant="h3">
                          <strong>Change Multiple Profit Centers</strong>
                        </Typography>
                        <Typography variant="body2" color="#777">
                          This view changes multiple Profit Centers
                        </Typography>
                      </Grid>
                    )}
                  </Grid>
                </Grid>

                <Grid item md={1} sx={{ display: "flex" }}>
                  <Tooltip title="Upload documents if any" arrow>
                    <IconButton onClick={handleOpenDialog}>
                      <AttachFileOutlinedIcon />
                    </IconButton>
                  </Tooltip>
                </Grid>
                <Dialog
                  hideBackdrop={false}
                  elevation={2}
                  PaperProps={{
                    sx: { boxShadow: "none" },
                  }}
                  open={openDialog}
                  onClose={handleCloseDialog}
                >
                  <DialogTitle
                    sx={{
                      justifyContent: "space-between",
                      alignItems: "center",
                      height: "max-content",
                      padding: ".5rem",
                      paddingLeft: "1rem",
                      backgroundColor: "#EAE9FF40",
                      // borderBottom: "1px solid grey",
                      display: "flex",
                    }}
                  >
                    <Typography variant="h6">Add Attachment</Typography>
                  </DialogTitle>
                  <DialogContent sx={{ padding: ".5rem 1rem" }}>
                    <Stack>
                      <Box sx={{ minWidth: 400 }}>
                        <ReusableAttachementAndComments
                          title="ProfitCenter"
                          useMetaData={false}
                          artifactId={pcNumber}
                          artifactName="ProfitCenter"
                        />
                      </Box>
                    </Stack>
                  </DialogContent>
                  <DialogActions>
                    <Button onClick={handleCloseDialog}>Close</Button>
                  </DialogActions>
                </Dialog>
              </Grid>
            </Grid>
            <Grid item sx={{ position: "relative" }}>
              <Stack>
                <ReusableTable
                  isLoading={isLoading}
                  width="100%"
                  title={
                    "Profit Center Master List (" + initialRows.length + ")"
                  }
                  rows={initialRows}
                  columns={columns}
                  pageSize={10}
                  getRowIdValue={"id"}
                  hideFooter={false}
                  checkboxSelection={true}
                  disableSelectionOnClick={true}
                  status_onRowSingleClick={true}
                  onRowsSelectionHandler={handleSelectionModelChange}
                  callback_onRowSingleClick={(params) => {
                    console.log("paramss", params);
                    const profitCenter = params.row.profitCenter;
                    const dataToSend = multipleProfitData.find(
                      (item) => item.profitCenter === profitCenter
                    );
                    navigate(
                      `/masterDataCockpit/profitCenter/createMultipleProfitCenter/editMultipleProfitCenter/${profitCenter}`,
                      {
                        state: { tabsData: dataToSend, rowData: params.row },
                      }
                    );
                  }}
                  stopPropagation_Column={"action"}
                  status_onRowDoubleClick={true}
                />
              </Stack>
            </Grid>
          </div>
          <Paper
            sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
            elevation={2}
          >
            <BottomNavigation
              className="container_BottomNav"
              showLabels
              sx={{
                display: "flex",

                justifyContent: "flex-end",
              }}
              value={value}
              // onChange={(newValue) => {
              // setValue(newValue);
              // }}
            >
              <Button
                variant="contained"
                size="small"
                sx={{ ...button_Primary, mr: 1 }}
                onClick={onValidateProfitCenter}
                disabled={!testRun}
              >
                Validate
              </Button>

              {massHandleType === "Create" ? (
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary }}
                  // disabled={testRun}
                  onClick={handleOpenCorrectionDialog}
                  disabled={submitForReviewDisabled}
                >
                  Submit for Review
                </Button>
              ) : massHandleType === "Change" ? (
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary }}
                  onClick={handleOpenCorrectionDialog}
                  disabled={submitForReviewDisabled}
                >
                  Submit for Review
                </Button>
              ) : (
                ""
              )}
            </BottomNavigation>
          </Paper>
          <Dialog
            open={dialogOpen}
            fullWidth
            onClose={handleDialogClose}
            sx={{
              "&::webkit-scrollbar": {
                width: "1px",
              },
              // paddingBottom:1
            }}
          >
            <DialogTitle
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF40",
                // borderBottom: "1px solid grey",
                display: "flex",
              }}
            >
              <Typography variant="h6" color="red">
                Errors
              </Typography>

              <IconButton
                sx={{ width: "max-content" }}
                onClick={handleDialogClose}
                children={<CloseIcon />}
              />
            </DialogTitle>
            <DialogContent sx={{ padding: ".5rem 1rem" }}>
              {/* <Grid container> */}

              {validationRows && (
                <ReusableTable
                  isLoading={isLoading}
                  width="100%"
                  // title={"Profit Center Master List (" + initialRows.length + ")"}
                  rows={validationRows}
                  columns={validationColumns}
                  pageSize={10}
                  getRowIdValue={"id"}
                  hideFooter={true}
                  checkboxSelection={false}
                  disableSelectionOnClick={true}
                  status_onRowSingleClick={true}
                  stopPropagation_Column={"action"}
                  status_onRowDoubleClick={true}
                />
              )}

              {/* </Grid> */}
            </DialogContent>

            <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
              {/* <Button
            sx={{ width: "max-content", textTransform: "capitalize" }}
            onClick={handleDialogClose}
          >
            Cancel
          </Button>
          <Button
            className="button_primary--normal"
            type="save"
            onClick={handleDialogProceed}
            variant="contained"
          >
            Proceed
          </Button> */}
            </DialogActions>
          </Dialog>
        </div>
      )}
      <Backdrop
        sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
        open={blurLoading}
        // onClick={handleClose}
      >
        <CircularProgress color="inherit" />
      </Backdrop>
    </>
  );
};

export default CreateMultipleProfitCenter;