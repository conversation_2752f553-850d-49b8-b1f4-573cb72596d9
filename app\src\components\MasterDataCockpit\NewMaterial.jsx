/* eslint-disable react/prop-types */
/* eslint-disable no-unused-vars */
import { useState, useEffect, Fragment } from "react";
import { useNavigate } from "react-router-dom";
import AppBar from "@mui/material/AppBar";
import Box from "@mui/material/Box";
import Toolbar from "@mui/material/Toolbar";
import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import IconButton from "@mui/material/IconButton";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import {
  button_Marginleft,
  button_Outlined,
  button_Primary,
  container_filter,
  container_table,
  font_Small,
  iconButton_SpacingSmall,
  outerContainer_Information,
  outermostContainer,
  outermostContainer_Information,
} from "../Common/commonStyles";
import { doAjax } from "../Common/fetchService";
import { destination_MaterialMgmt } from "../../destinationVariables";
// import "./masterDataCockpit.css";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import {
  Autocomplete,
  Card,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  Tooltip,
} from "@mui/material";
import { formValidator } from "../../functions";
import DoneIcon from "@mui/icons-material/Done";
import TextField from "@mui/material/TextField";
import CheckBoxIcon from "@mui/icons-material/CheckBox";
import CheckBoxOutlineBlankIcon from "@mui/icons-material/CheckBoxOutlineBlank";
import Checkbox from "@mui/material/Checkbox";
import DescriptionIcon from "@mui/icons-material/Description";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import {
  commonFilterClear,
  commonFilterUpdate,
  setErrorFields,
} from "../../app/commonFilterSlice";
import { Refresh } from "@mui/icons-material";
import {
  newMaterialData,
  newValueForData,
  newValueForMaterialType,
} from "../../app/tabsDetailsSlice";
import ReusableSnackBar from "../Common/ReusableSnackBar";
import DuplicateDesc from "./DuplicateDesc";

const icon = <CheckBoxOutlineBlankIcon fontSize="small" />;
const checkedIcon = <CheckBoxIcon fontSize="small" />;

const fieldListAccordingToView = [
  { label: "Basic Data", value: "basicData", fields: [], code: "K" },
  // { label: "Classification", value: "classification", fields: [], code: "C" },
  {
    label: "Sales",
    value: "sales",
    fields: ["Plant", "Sales Organization", "Distribution Channel"],
    code: "V",
  },
  { label: "Purchasing", value: "purchasing", fields: ["Plant"], code: "E" },
  // {
  //   label: "MRP",
  //   value: "mrp",
  //   fields: ["Plant", "Storage Location", "MRP Profile"],
  //   code: "D",
  // },
  // { label: "Plant Stock", value: "plantStock", fields: ["Plant"], code: "X" },
  // {
  //   label: "Storage Location Stocks",
  //   value: "storageLocationStocks",
  //   fields: ["Plant", "Storage Location"],
  //   code: "Z",
  // },
  // { label: "Storage", value: "storage", fields: ["Storage Location"], code: "L" },
  // {
  //   label: "Quality Management",
  //   value: "qualityManagement",
  //   fields: ["Plant"],
  //   code: "Q",
  // },
  { label: "Accounting", value: "accounting", fields: ["Plant"], code: "B" },
  // { label: "Costing", value: "costing", fields: ["Plant"], code: "G" },
  // {
  //   label: "Forecasting",
  //   value: "forecasting",
  //   fields: ["Plant", "Forecast Profile"],
  //   code: "P",
  // },
  // {
  //   label: "Production Resources/Tools",
  //   value: "productionResources/tools",
  //   fields: ["Plant"],
  //   code: "F",
  // },
  // {
  //   label: "Warehouse Management",
  //   value: "warehouseManagement",
  //   fields: ["Plant", "Warehouse No", "Storage Type"],
  //   code: "S",
  // },
  // {
  //   label: "Work Scheduling",
  //   value: "workScheduling",
  //   fields: ["Plant"],
  //   code: "A",
  // },
];

const NewMaterial = () => {
  const navigate = useNavigate();

  const dispatch = useDispatch();
  const [selectedAll, setSelectedAll] = useState(false);

  const [value, setValue] = useState([]);
  const [checkAll, setCheckAll] = useState(false);
  const [open, setOpen] = useState(false);
  const [industrySector, setIndustrySector] = useState([]);
  const [materialType, setMaterialType] = useState([]);
  const [materialTypeForNumberRange, setMaterialTypeForNumberRange] = useState(
    []
  );
  const [filteredViewType, setFilteredViewType] = useState([]);
  const [displayedFields, setDisplayedFields] = useState([]);
  console.log("displayedFields", displayedFields);
  const [fieldReference, setFieldReference] = useState([]);
  const [industryReference, setIndustryReference] = useState([]);
  const [industrySectorDesc, setIndustrySectorDesc] = useState([]);
  const [materialTypeDesc, setMaterialTypeDesc] = useState([]);
  const [materialTypeCode, setMaterialTypeCode] = useState([]);
  const [plantLookupData, setPlantLookupData] = useState([]);
  const [proceedEnable, setProceedEnable] = useState(true);
  const [salesOrgLookupData, setSalesOrgLookupData] = useState([]);
  const [distributionChannelLookupData, setDistributionChannelLookupData] =
    useState([]);
  const [storageLocationLookupData, setStorageLocationLookupData] = useState(
    []
  );
  const [warehouseNoLookupData, setWareHouseNoLookupData] = useState([]);
  const [storageTypeLookupData, setStorageTypeLookupData] = useState([]);
  const [selectedPlant, setSelectedPlant] = useState([]);
  const [selectedPlantNo, setSelectedPlantNo] = useState("");
  const [selectedSalesOrg, setSelectedSalesOrg] = useState("");
  const [selectedDistributionChannel, setSelectedDistributionChannel] =
    useState("");
  const [selectedStorageLocation, setSelectedStorageLocation] = useState("");
  const [selectedMRPProfile, setSelectedMRPProfile] = useState("");
  // const [selectedForecastProfile, setSelectedForecastProfile] = useState("");
  const [selectedWarehouseNo, setSelectedWarehouseNo] = useState("");
  // const [selectedStorageType, setSelectedStorageType] = useState("");
  const [materialDescription, setMaterialDescription] = useState("");
  const [copyFromMaterial, setCopyFromMaterial] = useState("");
  const [industrySectorValid, setIndustrySectorValid] = useState(true);
  const [materialTypeValid, setMaterialTypeValid] = useState(true);
  const [materialDescValid, setMaterialDescValid] = useState(true);
  const [materialValid, setMaterialValid] = useState(true);
  const [selectStatus, setSelectStatus] = useState(false);
  const [formValidationErrorItems, setFormValidationErrorItems] = useState([]);
  const [orgElementValue, setOrgElementValue] = useState([]);
  const [selectedOrgElements, setSelectedOrgElements] = useState([]);
  const [messageDialogExtra, setMessageDialogExtra] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [messageDialogSeverity, setMessageDialogSeverity] = useState(false);
  const [successMsg, setsuccessMsg] = useState(false);
  const [messageDialogOK, setMessageDialogOK] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [materialNumber, setMaterialNumber] = useState("");
  const [formData, setFormData] = useState({});
  const [isFormValid, setIsFormValid] = useState(false);

  const dataToSend = {
    orgData: [
      { info: selectedPlantNo, desc: "Plant" },
      { info: selectedSalesOrg, desc: "Sales Organization" },
      { info: selectedDistributionChannel, desc: "Distribution Channel" },
      // { info: selectedStorageLocation, desc: "Storage Location" },
      // { info: selectedMRPProfile, desc: "MRP Profile" },
      // { info: selectedForecastProfile, desc: "Forecast Profile" },
      // { info: selectedWarehouseNo, desc: "Warehouse No" },
      // { info: selectedStorageType, desc: "StorageType" },
    ],
    materialNo: { materialNumber },
    industrySector: { industryReference },
    industrySectorDesc: { industrySectorDesc },
    materialType: { fieldReference },
    materialTypeDesc: { materialTypeDesc },
    materialDescription: { materialDescription },
    selectedViews: { value },
  };
  console.log("datatosend", dataToSend);
  useEffect(() => {
    // dispatch(newValueForData())
    getIndustrySector();
    getMaterialType();
    dispatch(commonFilterClear({ module: "NewMaterial" }));
    // dispatch(commonFilterClear({ module: "DuplicateDesc"}));
    dispatch(newValueForData([]));
    // getNumberRangeForMaterialType();
  }, []);
  useEffect(() => {
    dispatch(setErrorFields(formValidationErrorItems));
  }, [formValidationErrorItems]);

  const getOrgLookupData = (uniqueFieldsItems) => {
    console.log("displayedfieldsss", uniqueFieldsItems);
    if (uniqueFieldsItems.includes("Plant")) {
      const hSuccess = (data) => {
        setPlantLookupData(data.body);
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        `/${destination_MaterialMgmt}/data/getPlant`,
        "get",
        hSuccess,
        hError
      );
    }
    if (uniqueFieldsItems.includes("Sales Organization")) {
      const hSuccess = (data) => {
        setSalesOrgLookupData(data.body);
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        `/${destination_MaterialMgmt}/data/getSalesOrg`,
        "get",
        hSuccess,
        hError
      );
    }

    if (uniqueFieldsItems.includes("MRP Profile")) {
      const hSuccess = (data) => {
        // setMrpProfileLookupData(data.body);
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        // `/${destination_MaterialMgmt}/data/getStorageLocationForPlant?plant=${plantLookupData[0].Salesorg}`,
        `/${destination_MaterialMgmt}/data/getPlant`,
        "get",
        hSuccess,
        hError
      );
    }
    // if (uniqueFieldsItems.includes("Forecast Profile")) {
    //   const hSuccess = (data) => {
    //     // setForecastProfileLookupData(data.body);
    //   };
    //   const hError = (error) => {
    //     console.log(error);
    //   };
    //   doAjax(
    //     // `/${destination_MaterialMgmt}/data/getStorageLocationForPlant?plant=${plantLookupData[0].Salesorg}`,
    //     `/${destination_MaterialMgmt}/data/getPlant`,
    //     "get",
    //     hSuccess,
    //     hError
    //   );
    // }
  };
  console.log(plantLookupData);
  const plantDependentOrgElement = (e) => {
    console.log("eeeeeeee", e);
    // if (displayedFields.includes("Storage Location")) {
    //   const hSuccess = (data) => {
    //     setStorageLocationLookupData(data.body);
    //   };
    //   const hError = (error) => {
    //     console.log(error);
    //   };
    //   doAjax(
    //     `/${destination_MaterialMgmt}/data/getStorageLocationForPlant?plant=${e.code}`,
    //     "get",
    //     hSuccess,
    //     hError
    //   );
    // }
    if (displayedFields.includes("Warehouse No")) {
      const hSuccess = (data) => {
        setWareHouseNoLookupData(data.body);
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        `/${destination_MaterialMgmt}/data/getWareHouseNoForPlant?plant=${e.code}`,
        "get",
        hSuccess,
        hError
      );
    }
  };
  const salesOrgDependentOrgElement = (e) => {
    console.log("eeeeeeeeeee", e.code);
    if (displayedFields.includes("Distribution Channel")) {
      const hSuccess = (data) => {
        setDistributionChannelLookupData(data.body);
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        `/${destination_MaterialMgmt}/data/getDistributionForSalesOrg?salesOrg=${e.code}`,
        "get",
        hSuccess,
        hError
      );
    }
  };
  console.log("forvslidation", formValidationErrorItems);
  console.log("displayfields", displayedFields);
  console.log("dataToSend", selectedOrgElements);
  const handleCheckValidationError = () => {
    if (dataToSend.orgData.map((x) => x.info) === "") {
      alert("Please enter necessary data");
    } else {
      const attachView = { label: "Attachments & Comments", value: "attachments&comments" };
      const giView = { label: "General Information", value: "generalInformation" };
      // Destructure the existing value array and add the new object
      const modifiedViews = [giView, ...value, attachView];
      const updatedValue = { value: modifiedViews };
      console.log("dddd1", value);
      console.log("dddd2", modifiedViews);
      console.log("dddd3", updatedValue);

      // Assign the modified array back to selectedViews
      dataToSend.selectedViews = updatedValue;

      dispatch(newMaterialData(dataToSend));
      navigate("/masterDataCockpit/materialMaster/createMaterialDetail", {
        state: dataToSend,
      });
    }
    // if (
    //   formValidator(selectedOrgElements, displayedFields, setFormValidationErrorItems)
    // ) {
    //   if (formValidationErrorItems.length === 0) {
    //     console.log("hjkdfshkkfgkdgkdf",formValidationErrorItems.length)
    //     alert("successful");
    //     // dispatch(newMaterialData(dataToSend));
    //     // navigate(
    //     //   "/masterDataCockpit/materialMaster/createMaterialDetail",
    //     //   {
    //     //     state: dataToSend,
    //     //   }
    //     // );
    //   }
    // }
  };
  // const warehouseNoDependentOrgElement = (e) => {
  //   console.log("eweeuge", e);
  //   if (displayedFields.includes("Storage Type")) {
  //     const hSuccess = (data) => {
  //       setStorageTypeLookupData(data.body);
  //     };
  //     const hError = (error) => {
  //       console.log(error);
  //     };
  //     doAjax(
  //       `/${destination_MaterialMgmt}/data/getStorageTypeForWareHouseNo?wareHouseNo=${e.code}`,
  //       "get",
  //       hSuccess,
  //       hError
  //     );
  //   }
  // };

  const handleClear = () => {
    dispatch(commonFilterClear({ module: "NewMaterial" }));
  };
  const checkAllChange = (e) => {
    setSelectStatus(true);
    setValue(autocompleteOptions);
    dispatch(newValueForData(autocompleteOptions));
    nmSearchForm.selectViewsValue = autocompleteOptions;
  };

  const handleClickAway = (e) => {
    console.log("Handle Click Away");
    setOpen(false);
  };

  const handleClickOpen = () => {
    const attachView = { label: "Attachments & Comments", value: "attachments&comments" };
    const giView = { label: "General Information", value: "generalInformation" };
      // Destructure the existing value array and add the new object
    const modifiedViews = [giView, ...value, attachView];
    const updatedValue = { value: modifiedViews };

      // Assign the modified array back to selectedViews
     dataToSend.selectedViews = updatedValue;
    dispatch(newMaterialData(dataToSend));
    if (!nmSearchForm?.sector?.code) {
      setIndustrySectorValid(false);
      return;
    }
    if (!nmSearchForm?.type?.code) {
      setMaterialTypeValid(false);
      return;
    }
    if (!nmSearchForm?.description) {
      setMaterialDescValid(false);
      return;
    }
    if (!isInternalRangeAllowed) {
      if (!nmSearchForm?.materialNo) {
        setMaterialValid(false);
        return;
      }
    }

    if (
      (value.length === 2 &&
        value.some((x) => x.label === "Basic Data") &&
        value.some((x) => x.label === "Classification")) ||
      (value.length === 1 && value.some((x) => x.label === "Basic Data"))

      // (value.map((x) => x.label).includes("Basic data") && value.length < 2)||
      // (value.length <= 2 && value.map((x) => x.label).includes("Classification"))
    ) {
      navigate("/masterDataCockpit/materialMaster/createMaterialDetail", {
        state: dataToSend,
      });
      // console.log("valuesselected", dataToSend);
    } else {
      const uniqueFields = Array.from(
        new Set(
          value.reduce(
            (fields, fieldListAccordingToView) => [
              ...fields,
              ...fieldListAccordingToView.fields,
            ],
            []
          )
        )
      );
      console.log("vvlaue", value);
      getOrgLookupData(uniqueFields);

      setDisplayedFields(uniqueFields);
      setOpen(true);

      console.log("valuesselected", uniqueFields);
    }
  };
  // console.log("displayedfields", uniqueFields);
  const handleClose = () => {
    setOpen(false);
  };

  const handleSelectAlll = () => {
    setSelectedAll(!selectedAll);
  };
  const getIndustrySector = () => {
    const hSuccess = (data) => {
      setIndustrySector(data.body);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getIndustrySector`,
      "get",
      hSuccess,
      hError
    );
  };
  const getMaterialType = () => {
    const hSuccess = (data) => {
      setMaterialType(data.body);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getMaterialType`,
      "get",
      hSuccess,
      hError
    );
  };
  const getNumberRangeForMaterialType = (code) => {
    const hSuccess = (data) => {
      setMaterialTypeForNumberRange(data.body);
      console.log("body", data.body);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getNumberRangeForMaterialType?materialType=${code}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getViews = (value) => {
    const hSuccess = (data) => {
      setFilteredViewType(data.body[0].MaintStatus.split(""));
      setFieldReference(data.body[0].MaterialType);
      getAutocompleteOptions(filteredViewType);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getViewForMaterialType?materialType=${value?.code}`,
      "get",
      hSuccess,
      hError
    );
  };

  function getAutocompleteOptions(codes) {
    const autocompleteOptions = [];
    console.log("codes", codes);
    fieldListAccordingToView.forEach((item) => {
      if (codes.includes(item.code)) {
        const hookData = {};
        autocompleteOptions.push({
          label: item.label,
          value: item.value,
          fields: item.fields,
        });
      }
    });
    return autocompleteOptions;
  }

  const autocompleteOptions = getAutocompleteOptions(filteredViewType);

  console.log("autocompleteOptions", filteredViewType);
  const getDataForField = (fieldName) => {
    switch (fieldName) {
      case "Plant":
        return plantLookupData.map((x) => x);
      case "Sales Organization":
        return salesOrgLookupData.map((y) => y);
      case "Distribution Channel":
        return distributionChannelLookupData.map((z) => z);
      // case "Storage Location":
      //   return storageLocationLookupData.map((w) => w);
      case "MRP Profile":
        return plantLookupData.map((p) => p);
      // case "Forecast Profile":
      //   return plantLookupData.map((f) => f);
      case "Warehouse No":
        return warehouseNoLookupData.map((n) => n);
      // case "Storage Type":
      //   return storageTypeLookupData.map((s) => s);
      default:
        return [];
    }
  };
  // const handleOrgData = (fieldName, selectedValue) => {
  //   setOrgDataValue(orgDataValue.push{"fieldName":fieldName,"selectedValue":selectedValue})
  // }
  const handleFieldChange = (fieldName, selectedValue) => {
    console.log("selected", selectedValue);

    const newFormData = { ...formData, [fieldName]: selectedValue };
    setFormData(newFormData);

    const allFieldsFilled = displayedFields.every(
      (field) => newFormData[field]
    );
    setIsFormValid(allFieldsFilled);

    switch (fieldName) {
      case "Plant":
        plantDependentOrgElement(selectedValue);
        setSelectedPlantNo(selectedValue);
        setSelectedOrgElements([...selectedOrgElements, "Plant"]);
        break;
      case "Sales Organization":
        salesOrgDependentOrgElement(selectedValue);
        setSelectedSalesOrg(selectedValue);
        setSelectedOrgElements([...selectedOrgElements, "Sales Organization"]);
        break;
      case "Distribution Channel":
        // salesOrgDependentOrgElement(selectedValue);
        setSelectedDistributionChannel(selectedValue);
        setSelectedOrgElements([
          ...selectedOrgElements,
          "Distribution Channel",
        ]);
        // Handle distribution channel change
        break;
      case "Warehouse No":
        // warehouseNoDependentOrgElement(selectedValue);
        setSelectedWarehouseNo(selectedValue);
        break;
      // case "Storage Location":
      // salesOrgDependentOrgElement(selectedValue);
      // setSelectedStorageLocation(selectedValue);
      // Handle Storage Location change
      // break;
      case "MRP Profile":
        // salesOrgDependentOrgElement(selectedValue);
        setSelectedMRPProfile(selectedValue);
        // Handle MRP Profile change
        break;
      // case "Forecast Profile":
      //   // salesOrgDependentOrgElement(selectedValue);
      //   setSelectedForecastProfile(selectedValue);
      //   // Handle Forecast Profile change
      //   break;

      // case "Storage Type":
      //   // salesOrgDependentOrgElement(selectedValue);
      //   setSelectedStorageType(selectedValue);
      //   // Handle Storage Type change
      //   break;
      default:
        break;
    }
  };

  const getOptionLabel = (fieldName, option) => {
    // console.log(option);
    switch (fieldName) {
      case "Plant":
        return option?.plant === ""
          ? "Select Plant"
          : `${option?.code} - ${option?.desc}`;
      case "Sales Organization":
        return option?.Salesorg === ""
          ? "Select Sales Organization"
          : `${option?.code} - ${option?.desc}`;
      case "Distribution Channel":
        return option?.DistributionChannel === ""
          ? "Select Distribution Channel"
          : `${option?.code} - ${option?.desc}`;
      // case "Storage Location":
      //   return option?.StorgaeLoc === ""
      //     ? "Select Storage Location"
      //     : `${option?.desc}`;
      case "MRP Profile":
        return option?.plant === ""
          ? "Select MRP Profile"
          : `${option?.code} - ${option?.desc}`;
      // case "Forecast Profile":
      //   return option?.plant === ""
      //     ? "Select Forecast Profile"
      //     : `${option?.code} - ${option?.desc}`;
      case "Warehouse No":
        return option?.WareHouseNo === "" ? "Select Plant" : `${option?.code}`;
      // case "Storage Type":
      //   return option?.Description === "" ? "Select Plant" : `${option?.code}`;
      default:
        return "";
    }
  };

  const renderOption = (fieldName, props, option) => {
    switch (fieldName) {
      case "Plant":
      case "Sales Organization":
      case "Distribution Channel":
      // case "Storage Location":
      case "MRP Profile":
      // case "Forecast Profile":
      case "Warehouse No":
        // case "Storage Type":
        return (
          <li {...props}>
            <Typography style={{ fontSize: 12 }}>
              {getOptionLabel(fieldName, option)}
            </Typography>
          </li>
        );
      default:
        return null;
    }
  };
  const nmSearchForm = useSelector(
    (state) => state.commonFilter["NewMaterial"]
  );
  console.log("nmSearchForm", nmSearchForm);
  const valueRequiredForSelectViews = useSelector(
    (state) => state.tabsData["value"]
  );
  const valueRequiredForMaterialType = useSelector(
    (state) => state.tabsData["materialType"]
  );
  console.log("valueRequiredForSelectViews", valueRequiredForSelectViews);
  console.log("valueRequiredForMaterialType", valueRequiredForMaterialType);

  const handleIndustrySector = (e, value) => {
    console.log("binat", disableArray, value);

    var tempIndustrySector = value;
    let tempFilterData = {
      ...nmSearchForm,
      sector: tempIndustrySector,
    };
    dispatch(
      commonFilterUpdate({
        module: "NewMaterial",
        filterData: tempFilterData,
      })
    );
    setIndustrySectorValid(true);

    setIndustryReference(tempIndustrySector.code);
    setIndustrySectorDesc(tempIndustrySector.desc);
  };
  const handleMaterialType = (e, value) => {
    // let clear = "";
    // let clearMaterialNumber = {
    //   ...nmSearchForm,
    //   materialNo: clear,
    // };
    // console.log("change");
    // dispatch(
    //   commonFilterUpdate({
    //     module: "NewMaterial",
    //     filterData: clearMaterialNumber,
    //   })
    // );

    var tempMaterialType = value;
    let tempFilterData = {
      ...nmSearchForm,
      type: tempMaterialType,
    };
    dispatch(
      commonFilterUpdate({
        module: "NewMaterial",
        filterData: tempFilterData,
      })
    );

    setValue([fieldListAccordingToView[0]]);
    console.log("setval", value);
    dispatch(newValueForData([fieldListAccordingToView[0]]));
    setSelectStatus(false);
    getViews(value);
    setMaterialTypeValid(true);
    setMaterialTypeDesc(tempMaterialType.desc);
    setMaterialTypeCode(tempMaterialType.code);
    getNumberRangeForMaterialType(tempMaterialType.code);
    console.log("Valueee", value);
  };
  const handleMaterialNo = (e) => {
    if (e.target.value !== null) {
      var tempMaterialNo = e.target.value;
      let tempFilterData = {
        ...nmSearchForm,
        materialNo: tempMaterialNo,
      };
      dispatch(
        commonFilterUpdate({
          module: "NewMaterial",
          filterData: tempFilterData,
        })
      );
    }
    setMaterialNumber(e.target.value);
  };
  const handleMaterialDesc = (e) => {
    console.log("eeeeeeeee", e.target.value);
    if (e.target.value !== null) {
      var tempMatDesc = e.target.value;

      let tempFilterData = {
        ...nmSearchForm,
        description: tempMatDesc,
      };
      dispatch(
        commonFilterUpdate({
          module: "NewMaterial",
          filterData: tempFilterData,
        })
      );
    }
    setMaterialDescription(e.target.value);
  };
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isNumberRangeDialogOpen, setIsNumberRangeDialogOpen] = useState(false);

  useEffect(() => {
    setProceedEnable(true);
  }, [nmSearchForm.type]);

  // const handleMaterialDesc1 = (e) => {
  //   console.log("eeeeeeeee", e.target.value);
  //   if (e.target.value !== null) {
  //     var tempMatDesc1 = e.target.value;

  //     let tempFilterData = {
  //       ...nmSearchForm,
  //       description1: tempMatDesc1,
  //     };
  //     dispatch(
  //       commonFilterUpdate({
  //         module: "NewMaterial",
  //         filterData: tempFilterData,
  //       })
  //     );
  //   }
  //   setMaterialDescription(e.target.value);
  // };

  const handleSnackBarOpen = () => {
    setopenSnackbar(true);
  };

  const handleSnackBarClose = () => {
    setopenSnackbar(false);
    // navigate("/masterDataCockpit/materialMaster/materialSingle");
  };
  const checkDuplicateForNumberRange = () => {
    setMessageDialogMessage(`Please Wait`);
    const hSuccess = (data) => {
      setMessageDialogTitle("Create");
      if (data.body.length != 0) {
        setMessageDialogMessage(
          `This Material already Exists. Please Enter Different Material Number`
        );
      } else {
        setMessageDialogMessage(
          `No Duplicate Material found with this Material Number`
        );
      }
      setMessageDialogSeverity("success");
      setMessageDialogOK(false);
      setsuccessMsg(true);
      handleSnackBarOpen();
      setMessageDialogExtra(true);
      setIsLoading(false);
      setMaterialValid(data.body.length != 0 ? false : true);
      setProceedEnable(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/alter/fetchMaterialNoDupliChk?materialNoToCheck=${nmSearchForm?.materialNo}`,
      "get",
      hSuccess,
      hError
    );
  };
  const handleTextFieldClick = () => {
    setIsDialogOpen(true);
  };
  const handleNumberRangeClick = () => {
    // setIsDialogOpen(true);
    checkDuplicateForNumberRange();
  };
  const handleCopyFromMaterial = (e) => {
    console.log("eeeeeeeee", e.target.value);
    if (e.target.value !== null) {
      var tempCopyMaterial = e.target.value;

      let tempFilterData = {
        ...nmSearchForm,
        copyMaterial: tempCopyMaterial,
      };
      dispatch(
        commonFilterUpdate({
          module: "NewMaterial",
          filterData: tempFilterData,
        })
      );
    }
    setCopyFromMaterial(e.target.value);
  };
  const handleOrgElement = (e, value) => {
    if (value !== null) {
      var tempOrgData = value;
      let tempFilterData = {
        ...nmSearchForm,
        orgData: tempOrgData,
      };
      dispatch(
        commonFilterUpdate({
          module: "NewMaterial",
          filterData: tempFilterData,
        })
      );
    }
    // handleFieldChange(fieldName, newValue);
  };
  // function refreshPage() {

  //   getIndustrySector();
  //   getMaterialType();

  // }
  const disableArray = ["N"];
  const isInternalRangeAllowed = materialTypeForNumberRange?.some(
    (item) => item.External !== "X"
  );
  const isExternalRangeAllowed = materialTypeForNumberRange?.some(
    (item) => item.External === "X"
  );
  const isExtWOCheckAllowed = materialTypeForNumberRange?.some(
    (item) => item.ExtNAwock === "X"
  );
  //  setViewTypes(autocompleteOptions);
  console.log("isInternalRangeAllowed", isInternalRangeAllowed);
  console.log("isExternalRangeAllowed", isExternalRangeAllowed);
  console.log("isExtWOCheckAllowed", isExtWOCheckAllowed);

  const generateUniqueMessages = (materialTypeForNumberRange) => {
    const uniqueMessages = new Set();
    let notAllowedMessage = null;
  
    materialTypeForNumberRange?.forEach((item) => {
      if (item.External === "X" && item.ExtNAwock === "X") {
        uniqueMessages.add(`External Number Range: Allowed (${item.FromNumber}-${item.ToNumber})`);
        uniqueMessages.add(`Ext W/O Check: Allowed`);
      } else if (item.External !== "X" && item.ExtNAwock === "X") {
        uniqueMessages.add(`Internal Number Range: Allowed`);
        uniqueMessages.add(`Ext W/O Check: Allowed`);
      } else if (item.External === "X" && item.ExtNAwock !== "X") {
        uniqueMessages.add(`External Number Range: Allowed (${item.FromNumber}-${item.ToNumber})`);
        notAllowedMessage = `Ext W/O Check: Not Allowed`;
      } else if (item.External !== "X" && item.ExtNAwock !== "X") {
        uniqueMessages.add(`Internal Number Range: Allowed`);
        notAllowedMessage = `Ext W/O Check: Not Allowed`;
      }
    });
  
    const messageArray = Array.from(uniqueMessages);
    if (notAllowedMessage) {
      messageArray.push(notAllowedMessage);
    }
  
    return messageArray.map((message, index) => (
      <div key={index}>
        <Typography sx={font_Small}>
          {message}
        </Typography>
      </div>
    ));
  };
  
  
  const tooltipContent = generateUniqueMessages(materialTypeForNumberRange);

  return (
    <>
      <div>
        {successMsg && (
          <ReusableSnackBar
            openSnackBar={openSnackbar}
            alertMsg={messageDialogMessage}
            handleSnackBarClose={handleSnackBarClose}
          />
        )}
        <div style={{ backgroundColor: "#FAFCFF" }}>
          <Stack spacing={1} sx={{ padding: "16px" }}>
            <Grid container>
              <Grid item md={5} sx={{ display: "flex" }}>
                <Grid>
                  <IconButton
                    // onClick={handleBacktoRO}
                    color="primary"
                    aria-label="upload picture"
                    component="label"
                    sx={iconButton_SpacingSmall}
                  >
                    <ArrowCircleLeftOutlinedIcon
                      style={{ height: "1em", width: "1em", color: "#000000" }}
                      // sx={{
                      //   fontSize: "1.5em",
                      //   color: "#000000",
                      // }}
                      onClick={() => {
                        navigate(
                          "/masterDataCockpit/materialMaster/materialSingle"
                        );
                      }}
                    />
                  </IconButton>
                </Grid>
                <Grid>
                  <Typography variant="h3">
                    <strong>New Material</strong>
                  </Typography>
                  <Typography variant="body2" color="#777">
                    This view creates a new material
                  </Typography>
                </Grid>
              </Grid>
            </Grid>
            <div style={{ marginTop: 0 }}>
              <div
                className="leftColumn"
                style={{
                  width: "100%",
                  display: "flex",
                  flexDirection: "column",
                }}
              >
                <Grid
                  container
                  item
                  md={12}
                  sx={{
                    backgroundColor: "white",
                    maxHeight: "max-content",
                    height: "max-content",
                    borderRadius: "8px",
                    border: "1px solid #E0E0E0",
                    mt: 1,
                    boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                    padding: "10px",
                  }}
                >
                  <Grid md={12} container>
                    <Grid md={3} sx={{ padding: "5px" }}>
                      <InputLabel id="demo-simple-select-label" sx={font_Small}>
                        Industry Sector<span style={{ color: "red" }}>*</span>
                      </InputLabel>
                      <Autocomplete
                        sx={{ height: "31px" }}
                        fullWidth
                        size="small"
                        // disableCloseOnSelect
                        value={nmSearchForm?.sector}
                        onChange={handleIndustrySector}
                        options={industrySector ?? []}
                        getOptionLabel={(option) => {
                          if (option?.code)
                            return `${option?.code} - ${option?.desc}` ?? "";
                          else return "";
                        }}
                        renderOption={(props, option) => (
                          <li {...props}>
                            <Typography style={{ fontSize: 12 }}>
                              {`${option?.code} - ${option?.desc}`}
                            </Typography>
                          </li>
                        )}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            variant="outlined"
                            placeholder="Select Industry Sector"
                            error={!industrySectorValid}
                          />
                        )}
                      />
                      {!industrySectorValid && (
                        <Typography variant="caption" color="error">
                          Please select an Industry Sector.
                        </Typography>
                      )}
                    </Grid>

                    <Grid md={3} sx={{ padding: "5px" }}>
                      <InputLabel id="demo-simple-select-label" sx={font_Small}>
                        Material Type<span style={{ color: "red" }}>*</span>
                      </InputLabel>
                      <Autocomplete
                        sx={{ height: "31px" }}
                        fullWidth
                        size="small"
                        // value={nmSearchForm?.type}
                        value={nmSearchForm?.type}
                        onChange={handleMaterialType}
                        options={materialType ?? []}
                        getOptionLabel={(option) => {
                          if (option?.code)
                            return `${option?.code} - ${option?.desc}` ?? "";
                          else return "";
                        }}
                        renderOption={(props, option) => (
                          <li {...props}>
                            <Typography style={{ fontSize: 12 }}>
                              {`${option?.code} - ${option?.desc}`}
                            </Typography>
                          </li>
                        )}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            variant="outlined"
                            placeholder="Select Material Type"
                            error={!materialTypeValid}
                          />
                        )}
                      />
                      {!materialTypeValid && (
                        <Typography variant="caption" color="error">
                          Please select an Material Type.
                        </Typography>
                      )}
                    </Grid>
                    <Grid md={3} sx={{ padding: "5px" }}>
                      <div>
                        <InputLabel
                          id="demo-simple-select-label"
                          sx={font_Small}
                        >
                          Material
                        </InputLabel>
                        
                        <Tooltip
                          title={tooltipContent}
                          arrow
                        >
                          <div>
                            <TextField
                              sx={font_Small}
                              size="small"
                              id="outlined-basic"
                              variant="outlined"
                              style={{ width: "100%" }}
                              value={nmSearchForm?.materialNo}
                              onChange={handleMaterialNo}
                              placeholder="Enter Material No"
                              disabled={
                                !isExternalRangeAllowed && !isExtWOCheckAllowed
                              }
                              error={!materialValid}
                              inputProps={{ maxLength: 40 }}
                            />
                            {!materialValid && (
                              <Typography variant="caption" color="error">
                                Please Enter Material Number
                              </Typography>
                            )}
                          </div>
                        </Tooltip>
                      </div>
                    </Grid>

                    {/* <Grid item md={3} sx={{ padding: "5px" }}>
                      <Typography sx={font_Small}>
                        Material Description
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                      <TextField
                        size="small"
                        onChange={handleMaterialDesc}
                        placeholder="Enter Material Description"
                        value={nmSearchForm?.description}
                        inputProps={{ maxLength: 40 }}
                        style={{ width: "100%" }}
                        error={!materialDescValid}
                      />
                      {!materialDescValid && (
                        <Typography variant="caption" color="error">
                          Please Enter Material Description.
                        </Typography>
                      )}
                    </Grid> */}
                    <Grid item md={3} sx={{ padding: "5px" }}>
                      <Typography sx={font_Small}>
                        Material Description
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                      <TextField
                        size="small"
                        value={nmSearchForm?.description}
                        style={{ width: "100%" }}
                        error={!materialDescValid}
                        onClick={handleTextFieldClick}
                        
                        // onChange={handleMaterialDesc}
                        placeholder="Enter Material Description"
                      />
                      {!materialDescValid && (
                        <Typography variant="caption" color="error">
                          Please Enter Material Description.
                        </Typography>
                      )}
                    </Grid>

                    <Grid
                      item
                      md={2}
                      sx={{ padding: "5px" }}
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        justifyContent: "flex-end",
                      }}
                    ></Grid>
                    <Grid sx={{ width: "100%" }}>
                      <Divider />
                      <Grid
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                        }}
                      >
                        <Grid md={3} sx={{ padding: "5px" }}>
                          <InputLabel
                            id="demo-simple-select-label"
                            sx={font_Small}
                          >
                            Copy From Material
                          </InputLabel>
                          <TextField
                            sx={font_Small}
                            size="small"
                            id="outlined-basic"
                            variant="outlined"
                            style={{ width: "100%" }}
                            placeholder="Select Copy from Material"
                            value={nmSearchForm?.copyMaterial}
                            onChange={handleCopyFromMaterial}
                          />
                        </Grid>
                        <DuplicateDesc
                          open={isDialogOpen}
                          onClose={() => setIsDialogOpen(false)}
                        />
                        <Grid
                          md={2}
                          sx={{
                            padding: "5px",
                            display: "flex",
                            justifyContent: "flex-end",
                            alignItems: "center",
                          }}
                        >
                          <Tooltip title = "Duplicate Material Number Check" arrow>
                            <Button
                              variant="contained"
                              onClick={handleNumberRangeClick}
                              disabled={
                                nmSearchForm?.materialNo === "" ||
                                (!isExternalRangeAllowed && !isExtWOCheckAllowed)
                              }
                              sx={{ marginRight: "10px" }}
                            >
                              Check
                            </Button>
                          </Tooltip>
                          <Button
                            variant="outlined"
                            sx={button_Outlined}
                            onClick={handleClear}
                          >
                            Clear
                          </Button>
                        </Grid>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>

                <Grid
                  container
                  item
                  md={12}
                  sx={{
                    backgroundColor: "white",
                    maxHeight: "max-content",
                    height: "max-content",
                    borderRadius: "8px",
                    border: "1px solid #E0E0E0",
                    mt: 1,
                    boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                    padding: "10px",
                    mb: 1,
                  }}
                >
                  <Grid
                    container
                    sx={{ display: "flex", flexDirection: "column" }}
                  >
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "space-between",
                        paddingBottom: "5px",
                      }}
                    >
                      <div>
                        <Typography sx={font_Small}>Select Views</Typography>
                      </div>
                    </div>
                    <Grid
                      container
                      spacing={1}
                      style={{ display: "flex", paddingBottom: "5px" }}
                    >
                      <Grid item md={6}>
                        <Autocomplete
                          size="small"
                          multiple
                          disableCloseOnSelect
                          id="checkboxes-tags-demo"
                          options={autocompleteOptions ?? []}
                          getOptionLabel={(option) => option.label}
                          
                          isOptionEqualToValue={(option, value) => {
                            return option.value == value.value;
                          }}
                          value={
                            (valueRequiredForSelectViews.length ? valueRequiredForSelectViews : value
                            )
                          }

                          onChange={(event, newValue, reason) => {
                            console.log("newvalue", newValue, event);

                            // array of views 
                            let uniqueValue = [...new Set(newValue)];
                            console.log("dattt1", uniqueValue);

                            console.log("reason", reason);
                            console.log("dattt2", newValue);
                            console.log("dattt3", value);
                            if (reason === "selectOption") {
                              setValue(uniqueValue);
                              //setting views
                              dispatch(newValueForData(uniqueValue));
                            } else if (reason === "removeOption") {
                              setCheckAll(false);
                              setSelectStatus(false);
                              if (!newValue.length) {
                                setValue([fieldListAccordingToView[0]]);
                                dispatch(
                                  newValueForData([fieldListAccordingToView[0]])
                                );
                              } else {
                                if (
                                  uniqueValue.find(
                                    (a) => a.value == "basicData"
                                  )
                                ) {
                                  console.log("test");
                                  setValue(uniqueValue);
                                  dispatch(newValueForData(uniqueValue));
                                } else {
                                  setValue([
                                    fieldListAccordingToView[0],
                                    ...uniqueValue,
                                  ]);
                                  dispatch(
                                    newValueForData([
                                      fieldListAccordingToView[0],
                                      ...uniqueValue,
                                    ])
                                  );
                                  console.log("valueee", value);
                                }
                              }
                            } else if (reason === "clear") {
                              setValue([fieldListAccordingToView[0]]);
                              dispatch(
                                newValueForData([fieldListAccordingToView[0]])
                              );
                              setCheckAll(false);
                              setSelectStatus(false);
                            }
                          }}
                          renderOption={(props, option, { selected }) => {
                            console.log("option", option);
                            console.log("selected", selected);
                            console.log("props", props);
                            return (
                              <li {...props}>
                                <Checkbox
                                  icon={icon}
                                  checkedIcon={checkedIcon}
                                  style={{ marginRight: 8 }}
                                  checked={
                                    props["data-option-index"] === 0
                                      ? true
                                      : checkAll
                                      ? checkAll
                                      : selected
                                  }
                                />

                                <option>{option.label}</option>
                              </li>
                            );
                          }}
                          style={{ minWidth: "100%" }}
                          renderInput={(params) => (
                            <TextField {...params} placeholder="Select Views" />
                          )}
                        />
                      </Grid>
                      <Grid
                        item
                        md={2}
                        sx={{ display: "flex", alignItems: "center" }}
                      >
                        {selectStatus ? (
                          <Button variant="outlined" disabled>
                            Select all
                          </Button>
                        ) : (
                          <Button
                            variant="outlined"
                            onClick={checkAllChange}
                            onMouseDown={(e) => e.preventDefault()}
                          >
                            Select all
                          </Button>
                        )}
                      </Grid>
                      <Grid
                        item
                        md={4}
                        style={{
                          display: "flex",
                          justifyContent: "end",
                          alignItems: "center",
                        }}
                      >
                        <Button
                          onClick={handleClickOpen}
                          variant="outlined"
                          // disabled={proceedEnable}
                        >
                          Proceed
                        </Button>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </div>
              <div className="rightColumn">
                <div>
                  <Dialog
                    open={open}
                    onClose={handleClose}
                    sx={{ display: "flex", justifyContent: "center" }}
                  >
                    <Box sx={{ width: "600px !important" }}>
                      <DialogTitle sx={{backgroundColor: "#EAE9FF", marginBottom: ".5rem"}}>
                        <DescriptionIcon
                          style={{
                            height: "20px",
                            width: "20px",
                            marginBottom: "-5px",
                          }}
                        />
                        <span>Select Org Data</span>
                      </DialogTitle>
                      <DialogContent sx={{paddingBottom: ".5rem"}}>
                        <Grid container columnSpacing={1}>
                          {displayedFields.map((fieldName, index) => (
                            <Fragment key={index}>
                              <Grid item md={4}>
                                <Typography sx={font_Small}>
                                  {fieldName}
                                  <span style={{ color: "red" }}>*</span>
                                </Typography>
                              </Grid>
                              <Grid item md={8}>
                                <Autocomplete
                                  options={getDataForField(fieldName) ?? []}
                                  sx={{ height: "42px" }}
                                  size="small"
                                  getOptionLabel={(option) =>
                                    getOptionLabel(fieldName, option)
                                  }
                                  // value={getValueForField(fieldName)}
                                  renderOption={(props, option) =>
                                    renderOption(fieldName, props, option)
                                  }
                                  renderInput={(params) => (
                                    <TextField
                                      {...params}
                                      placeholder={`Select ${fieldName}`}
                                      // error={!orgTypeValid}
                                    />
                                  )}
                                  // onChange={handleOrgElement}
                                  onChange={(e, newValue) => {
                                    handleFieldChange(fieldName, newValue);
                                    // handleOrgData(fieldName,newValue)
                                  }}
                                  placeholder="Select from Dropdown"
                                />
                              </Grid>
                            </Fragment>
                          ))}
                        </Grid>
                      </DialogContent>
                      {/* <ReusableSnackBar
          openSnackBar={()=>{}}
          alertMsg={"Helloo"}
          handleSnackBarClose={()=>{}}
        /> */}
                      <DialogActions>
                        <Button onClick={handleClose} variant="outlined">Cancel</Button>
                        <Button
                          onClick={() => {
                            handleCheckValidationError();
                          }}
                          disabled={!isFormValid}
                          variant="contained"
                        >
                          Proceed
                        </Button>
                      </DialogActions>
                    </Box>
                  </Dialog>
                </div>
              </div>
            </div>
          </Stack>
        </div>
      </div>
    </>
  );
};
export default NewMaterial;
