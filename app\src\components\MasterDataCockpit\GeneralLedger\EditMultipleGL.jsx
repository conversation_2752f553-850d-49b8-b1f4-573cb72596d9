import {
  Box,
  Grid,
  IconButton,
  Tabs,
  Typography,
  Stack,
  TextField,
  Checkbox,
  Autocomplete,
  Paper,
  BottomNavigation,
  Button,
  CardContent,
  Stepper,
  Step,
  StepLabel,
} from "@mui/material";
import Tab from "@mui/material/Tab";
import React, { useState, useEffect } from "react";
import {
  iconButton_SpacingSmall,
  outermostContainer,
  container_Padding,
  outerContainer_Information,
  button_Primary,
  outermostContainer_Information,
  button_Outlined,
} from "../../Common/commonStyles";
import { useSelector, useDispatch } from "react-redux";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import { useNavigate, useLocation } from "react-router-dom";
// import EditableField from "./EditFieldForDisplay";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import { doAjax } from "../../Common/fetchService";
import { setDropDown } from "../../../app/dropDownDataSlice";
import EditFieldForMassGL from "./EditFieldForMassGL";
import { destination_GeneralLedger } from "../../../destinationVariables";
import ChangeLog from "../../Changelog/ChangeLog";
import TrackChangesTwoToneIcon from "@mui/icons-material/TrackChangesTwoTone";
import { clearGeneralLedger } from "../../../app/generalLedgerTabSlice";
import { formValidator } from "../../../functions";
import { setPayloadWhole } from "../../../app/editPayloadSlice";
import ReusableSnackBar from "../../Common/ReusableSnackBar";

const EditMultipleGL = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [dropDownData, setDropDownData] = useState({});
  const [value, setValue] = useState(0);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isDisplayMode, setIsDisplayMode] = useState(true);
  const [activeTab, setActiveTab] = useState(0);
  const [dispCompCode, setDispCompCode] = useState([]);
  const [activeStep, setActiveStep] = useState(0);
  const [isChangeLogopen, setisChangeLogopen] = useState(false);
  const [formValidationErrorItems, setFormValidationErrorItems] = useState([]); //chiranjit
  const [openSnackbarValidation, setOpenSnackbarValidation] = useState(false); //chiranjit
  const location = useLocation();
  const compCodesTabDetails = useSelector(
    (state) => state.profitCenter.profitCenterCompCodes
  );
  console.log("location", location.state);
  const ChangesInMaterialDetail = useSelector(
    (state) => state.initialData.EditMultipleMaterial
  );
  let taskData = useSelector((state) => state?.initialData?.IWMMyTask);
  let generalLedgerRowData = location.state.rowViewData;
  const tabsData = location.state.rowViewData.viewData;
  const selectedRowData = location.state.selectedRow;
  console.log("generalLedgerRowDataaaaa",selectedRowData)
  const requestNumber = location.state.requestNumber;    
  let singleGLPayloadAfterChange = useSelector((state) => state.edit.payload);
  console.log(singleGLPayloadAfterChange, "singleGLPayloadAfterChange");
  //console.log(multipleProfitCenterData, "singlePCPayloadAfterChange");
  let requiredFieldTabWise= useSelector((state) => state.generalLedger.requiredFields);
  console.log(requiredFieldTabWise,"required_field_for_data") //chiranjit
  const PayloadData = useSelector((state) => state.payload);
  const getTaxCategory = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "TaxCategory", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getTaxCategory?companyCode=${
        taskData?.body?.CompCode
          ? taskData?.body?.CompCode
          : generalLedgerRowData?.compCode
      }`,
      "get",
      hSuccess,
      hError
    );
  };

  const getHouseBank = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "HouseBank", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getHouseBank?companyCode=${
        taskData?.body?.CompCode
          ? taskData?.body?.CompCode
          : generalLedgerRowData?.CompCode
      }`,
      "get",
      hSuccess,
      hError
    );
  };
  const getFieldStatusGroup = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "FieldStatusGroup", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getFieldStatusGroup?companyCode=${
        taskData?.body?.CompCode
          ? taskData?.body?.CompCode
          : generalLedgerRowData?.CompCode
      }`,
      "get",
      hSuccess,
      hError
    );
  };
  //chartofaccount dependent lookups
  const getGroupAccountNumber = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "GroupAccountNumber", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getGroupAccountNumber?chartAccount=${
        taskData?.body?.chartOfAccount
          ? taskData?.body?.chartOfAccount
          : generalLedgerRowData?.ChartOfAccount
      }`,
      "get",
      hSuccess,
      hError
    );
  };
  const getAlternativeAccountNumber = () => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "AlternativeAccountNumber", data: data.body })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getAlternativeAccountNumber?chartAccount=${
        taskData?.body?.chartOfAccount
          ? taskData?.body?.chartOfAccount
          : generalLedgerRowData?.ChartOfAccount
      }`,
      "get",
      hSuccess,
      hError
    );
  };
  const getAccountGroup = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "AccountGroup", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getAccountGroupCodeDesc?chartAccount=${
        taskData?.body?.chartOfAccount
          ? taskData?.body?.chartOfAccount
          : generalLedgerRowData?.ChartOfAccount
      }`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCostElementCategory = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CostElementCategory", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getCostElementCategory?accountType=${
        taskData?.body?.accountType
          ? taskData?.body?.accountType
          : selectedRowData?.accountType
      }`,
      "get",
      hSuccess,
      hError
    );
  };
  useEffect(() => {
    getTaxCategory();
    getFieldStatusGroup();
    getHouseBank();
    getAccountGroup();
    getAlternativeAccountNumber();
    getGroupAccountNumber();
    getCostElementCategory();

  }, []);
  useEffect(()=>{
    dispatch(setPayloadWhole(tempHash));
  },[])

  const onEdit = () => {
    setIsEditMode(true);
    setIsDisplayMode(false);
  };

  const handleBack = () => {
    //setActiveStep((prevActiveStep) => prevActiveStep - 1);
    const isValidation = handleCheckValidationError();
    if(isEditMode){
    if (isValidation) {
      setActiveStep((prevActiveStep) => prevActiveStep - 1);
      dispatch(clearGeneralLedger());
    } else {
      handleSnackBarOpenValidation();
    }}else{
      setActiveStep((prevActiveStep) => prevActiveStep - 1);
      dispatch(clearGeneralLedger());
    }
  };
  const handleNext = () => {
    //setActiveStep((prevActiveStep) => prevActiveStep + 1);
    const isValidation = handleCheckValidationError();
    if(isEditMode){
    if (isValidation) {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
      dispatch(clearGeneralLedger());
    } else {
      handleSnackBarOpenValidation();
    }}else{
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
      dispatch(clearGeneralLedger());
    }
  };
  const handleSnackBarOpenValidation = () => {
    setOpenSnackbarValidation(true);
  };
  // console.log("datafromprevious", tabsData, compcodeData);
  const handleChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  const handleClosemodalData = (data) => {
    setisChangeLogopen(data);
  };
  const openChangeLog = () => {
    setisChangeLogopen(true);
  };


  const tabsArray = Object.entries(tabsData)
    .filter((item) => typeof item[1] == "object" && item[1] != null)
    .map((item) => item[0]);
  console.log("tabsArray", tabsArray,activeStep);
  const tabContents = Object.entries(tabsData)
    .filter((item) => typeof item[1] == "object" && item[1] != null)
    .map((item) => Object.entries(item[1]));
    const tempHash = {};
    const gridArr = tabContents.map(item => {
      item.forEach((eachTab, i) => {
        eachTab.forEach((eachItem, i) => {
          if (i !== 0) {
            eachItem.forEach(fieldItem => {
              tempHash[fieldItem.fieldName.
               replaceAll("(", "")
              .replaceAll(")", "")
              .replaceAll("/", "")
              .replaceAll("-", "")
              .replaceAll(".", "")
              .split(" ")
              .join("")] =  fieldItem.value;
            });
          }
        });
      });
    });
    console.log(JSON.stringify(tempHash),"temphash");
  console.log(singleGLPayloadAfterChange,requiredFieldTabWise,"========deeced")
  console.log(activeStep,"activeStep")
  const handleCheckValidationError = () => {
    
    return formValidator(
      singleGLPayloadAfterChange,
      requiredFieldTabWise,
      setFormValidationErrorItems
    );
  };
  const handleSnackBarCloseValidation = () => {
    setOpenSnackbarValidation(false);
  };

  return (
    <div>
      <Grid
        container
        style={{
          ...outermostContainer_Information,
          backgroundColor: "#FAFCFF",
        }}
      >
        {formValidationErrorItems.length != 0 && (
            <ReusableSnackBar
              openSnackBar={openSnackbarValidation}
              alertMsg={
                "Please fill the following Field: " +
                formValidationErrorItems.join(", ")
              }
              handleSnackBarClose={handleSnackBarCloseValidation}
            />
          )}
        <Grid sx={{ width: "inherit" }}>
          <Grid item md={12} style={{ padding: "16px", display: "flex" }}>
            {/* <Grid  sx={{ display: "flex" }}> */}
            <Grid item style={{ display: "flex", justifyContent: "flex-end" }}>
              <IconButton
                // onClick={handleBacktoRO}
                color="primary"
                aria-label="upload picture"
                component="label"
                sx={iconButton_SpacingSmall}
              >
                <ArrowCircleLeftOutlinedIcon
                  style={{ height: "1em", width: "1em", color: "#000000" }}
                  onClick={() => {
                    navigate(
                      "/masterDataCockpit/generalLedger/createMultipleGL"
                    );
                  }}
                />
              </IconButton>
            </Grid>
            <Grid md={8}>
              <Typography variant="h3">
                <strong>Multiple General Ledgers: {selectedRowData?.glAccount} </strong>
              </Typography>
              <Typography variant="body2" color="#777">
                This view displays the detail of Multiple General Ledgers
              </Typography>
            </Grid>
            {location?.state?.requestNumber ?
          <Grid md={.5} sx={{ display: "flex", justifyContent: "flex-end" }}>
            <Button
              variant="outlined"
              size="small"
              sx={button_Outlined}
              onClick={openChangeLog}
              title="Change Log"
            >

              <TrackChangesTwoToneIcon
              sx={{ padding: "2px" }}
              fontSize="small"
              />
            </Button>
          </Grid>:<Grid md={.5} sx={{ display: "flex", justifyContent: "flex-end" }}></Grid>}
            {isChangeLogopen && (
            <ChangeLog
              open={true}
              closeModal={handleClosemodalData}
              requestId={requestNumber}
              requestType={"Mass"}
              pageName={"generalLedger"}
              controllingArea={selectedRowData.companyCode}
              centerName={selectedRowData.glAccount}
            />
          )}
            {!isEditMode ? (
              <Grid md={4} sx={{ display: "flex", justifyContent: "flex-end" }}>
                <Grid item>
                  <Button
                    variant="outlined"
                    size="small"
                    sx={button_Outlined}
                    onClick={onEdit}
                  >
                    Change
                    <EditOutlinedIcon
                      sx={{ padding: "2px" }}
                      fontSize="small"
                    />
                  </Button>
                </Grid>
              </Grid>
            ) : (
              ""
            )}
            {/* </Grid> */}
          </Grid>
          <Grid container display="flex" flexDirection="row" flexWrap="nowrap">
            <Box width="70%" sx={{ marginLeft: "40px" }}>
              <Grid item sx={{ paddingTop: "2px !important" }}>
                <Stack flexDirection="row">
                  <div style={{ width: "15%" }}>
                    <Typography variant="body2" color="#777">
                      General Ledger Account
                    </Typography>
                  </div>
                  <Typography
                    variant="body2"
                    fontWeight="bold"
                    justifyContent="flex-start"
                  >
                    : {selectedRowData?.glAccount}
                  </Typography>
                </Stack>
              </Grid>

              <Grid item sx={{ paddingTop: "2px !important" }}>
                <Stack flexDirection="row">
                  <div style={{ width: "15%" }}>
                    <Typography variant="body2" color="#777">
                      Company Code
                    </Typography>
                  </div>
                  <Typography variant="body2" fontWeight="bold">
                    : {selectedRowData?.companyCode}
                  </Typography>
                </Stack>
              </Grid>
              <Grid item sx={{ paddingTop: "2px !important" }}>
                <Stack flexDirection="row">
                  <div style={{ width: "15%" }}>
                    <Typography variant="body2" color="#777">
                      Chart of Account
                    </Typography>
                  </div>
                  <Typography variant="body2" fontWeight="bold">
                    : {selectedRowData?.chartOfAccount}
                  </Typography>
                </Stack>
              </Grid>
             
            </Box>
          </Grid>

          <Grid container style={{ padding: "16px" }}>
            <Stepper
              activeStep={activeStep}
              sx={{
                background: "#FFFFFF",
                borderBottom: "1px solid #BDBDBD",
                width: "100%",
                height: "48px",
              }}
              aria-label="mui tabs example"
            >
              {tabsArray.map((factor, index) => (
                <Step key={factor}>
                  <StepLabel sx={{ fontWeight: "700" }}>{factor}</StepLabel>
                </Step>
              ))}
            </Stepper>
            <Grid container>
              {tabContents &&
                tabContents[activeStep]?.map((item, index) => {
                  return  (
                    <Box key={index} sx={{ width: "100%" }}>
                      <Grid
                        item
                        md={12}
                        sx={{
                          backgroundColor: "white",
                          maxHeight: "max-content",
                          height: "max-content",
                          borderRadius: "8px",
                          border: "1px solid #E0E0E0",
                          mt: 0.25,
                          boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                          ...container_Padding,
                          // ...container_columnGap,
                        }}
                      >
                        <Grid container>
                          <Typography
                            sx={{
                              fontSize: "12px",
                              fontWeight: "700",
                              margin: "0px !important",
                            }}
                          >
                            {item[0]}
                          </Typography>
                        </Grid>
                        <Box>
                          <Box sx={{ width: "100%" }}>
                            <CardContent
                              sx={{
                                padding: "0",
                                paddingBottom: "0 !important",
                                paddingTop: "10px !important",
                              }}
                            >
                              <Grid
                                container
                                style={{
                                  display: "grid",
                                  gridTemplateColumns: "repeat(6,1fr)",
                                  gap: "15px",
                                }}
                                justifyContent="space-between"
                                alignItems="flex-start"
                                md={12}
                              >
                                {[...item[1]].map((innerItem) => {
                                  console.log("inneritem", innerItem);
                                  return (
                                    <EditFieldForMassGL
                                      activeTabIndex={activeStep}
                                      fieldGroup={item[0]}
                                      selectedRowData={
                                        selectedRowData?.glAccount
                                      }
                                      
                                      pcTabs={tabsArray}
                                      label={innerItem.fieldName}
                                      value={innerItem.value}
                                      length={innerItem.maxLength}
                                      visibility={innerItem.visibility}
                                      onSave={(newValue) =>
                                        handleFieldSave(
                                          innerItem.fieldName,
                                          newValue
                                        )
                                      }
                                      isEditMode={isEditMode}
                                      // isExtendMode={isExtendMode}
                                      type={innerItem.fieldType}
                                      field={innerItem} // Update the type as needed
                                    />
                                  );
                                })}
                              </Grid>
                            </CardContent>
                          </Box>
                        </Box>
                      </Grid>
                      {/* <h1>{cardContent[0]}</h1>
                    {cardContent[1].map((item)=>{
                      return(<p>{item.fieldName}</p>)
                    })} */}
                    </Box>
                  );
                })}
            </Grid>
          </Grid>
        </Grid>
      </Grid>
      {isEditMode ? (
        <Paper
          sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
          elevation={2}
        >
          <BottomNavigation
            className="container_BottomNav"
            showLabels
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              gap: 1,
            }}
            value={value}
            onChange={(newValue) => {
              setValue(newValue);
            }}
          >
             <Button
              size="small"
              sx={{ ...button_Primary, mr: 1 }}
              variant="contained"
              onClick={() => {
                navigate(
                  "/masterDataCockpit/generalLedger/createMultipleGL"
                );
              }}
            >
              Save
            </Button>
            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary, mr: 1 }}
              onClick={handleBack}
              disabled={activeStep === 0}
            >
              Back
            </Button>
            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary, mr: 1 }}
              onClick={handleNext}
              disabled={activeStep === tabsArray.length - 1 ? true : false}
            >
              Next
            </Button>
           
          </BottomNavigation>
        </Paper>
      ) : (
        ""
      )}
      {!isEditMode ? (
        <Paper
          sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
          elevation={2}
        >
          <BottomNavigation
            className="container_BottomNav"
            showLabels
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              gap: 1,
            }}
            value={value}
            onChange={(newValue) => {
              setValue(newValue);
            }}
          >
            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary, mr: 1 }}
              onClick={handleBack}
              disabled={activeStep === 0}
            >
              Back
            </Button>
            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary, mr: 1 }}
              onClick={handleNext}
              disabled={activeStep === tabsArray.length - 1 ? true : false}
            >
              Next
            </Button>
            
          </BottomNavigation>
        </Paper>
      ) : (
        ""
      )}
    </div>
  );
};

export default EditMultipleGL;
