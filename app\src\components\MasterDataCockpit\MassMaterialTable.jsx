import {
  BottomNavigation,
  Box,
  Button,
  Checkbox,
  Grid,
  IconButton,
  Paper,
  Stack,
  Tab,
  Tabs,
  Tooltip,
  Typography,
} from "@mui/material";
import React, { useState } from "react";
import {
  iconButton_SpacingSmall,
  outermostContainer,
  button_Primary,
  outerContainer_Information,
  outermostContainer_Information,
} from "../common/commonStyles";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import ReusableTable from "../Common/ReusableTable";
import { useNavigate, useLocation } from "react-router-dom";
import { doAjax } from "../Common/fetchService";
import { destination_MaterialMgmt } from "../../destinationVariables";
import { useSelector } from "react-redux";
import ReusableSnackBar from "../Common/ReusableSnackBar";
import { InfoOutlined } from "@mui/icons-material";
import { ReusableDialog } from "../Common/ReusablePromptBox/ReusablePromptBox";
import { clearPayload } from "../../app/payloadslice";

const MassMaterialTable = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [value, setValue] = useState("1");
  const [selectedRows, setSelectedRows] = useState([]);
  const [successMsg, setsuccessMsg] = useState(false);
  const [messageDialogSeverity, setMessageDialogSeverity] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [messageDialogOK, setMessageDialogOK] = useState(true);
  const [messageDialogOKChange, setMessageDialogOKChange] = useState(true);
  const [successMsgChange, setsuccessMsgChange] = useState(false);
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [messageDialogSeverityChange, setMessageDialogSeverityChange] = useState(false);
  const [messageDialogTitleChange, setMessageDialogTitleChange] = useState(false);
  const [messageDialogMessageChange, setMessageDialogMessageChange] = useState("");
  const [openSnackbarChange, setopenSnackbarChange] = useState(false);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [openMessageDialogChange, setOpenMessageDialogChange] = useState(false);
  const [isDuplicateDescEnabled, setDuplicateDescEnabled] = useState(false);
const [isSubmitForApprovalEnabled, setIsSubmitForApprovalEnabled] = useState(false);
const [isSubmitForApprovalMaterialNo, setIsSubmitForApprovalMaterialNo] = useState(false);
const [directMatch, setDirectMatch] = useState(false);
const [probables, setProbables] = useState([]);
const [isListOpen, setIsListOpen] = useState(false);
const [duplicateCheck, setDuplicateCheck] = useState(null);
const [responseList, setResponseList] = useState([]);
const [directMatchMaterialNo, setDirectMatchMaterialNo] = useState(false);
const [probablesMaterialNo, setProbablesMaterialNo] = useState([]);
const [isListOpenMaterialNo, setIsListOpenMaterialNo] = useState(false);
  const location = useLocation();
  let massHandleType = location?.state;
  console.log(massHandleType,"massHandleType")
  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };
  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };
  const handleSnackBarOpen = () => {
    setopenSnackbar(true);
  };
  const handleSnackBarClose = () => {
    setopenSnackbar(false);
    navigate("/masterDataCockpit/materialMaster/materialSingle");
  };
  const handleMessageDialogClickOpenChange = () => {
    setOpenMessageDialogChange(true);
  };
  const handleMessageDialogCloseChange = () => {
    setOpenMessageDialogChange(false);
  };
  const handleSnackBarOpenChange = () => {
    setopenSnackbarChange(true);
  };
  const handleSnackBarCloseChange = () => {
    setopenSnackbarChange(false);
    navigate("/masterDataCockpit/materialMaster/materialSingle");
  };
  const navigate = useNavigate();
  const MultipleMaterial = useSelector(
    (state) => state.initialData.MultipleMaterial
  );
  const EditMultipleMaterial = useSelector(
    (state) => state.initialData.EditMultipleMaterial
  );
  let userData = useSelector((state) => state.userManagement.userData);

  const initialRows = MultipleMaterial.map((materialll, index) => {
    const description = materialll?.Description;
    console.log(EditMultipleMaterial,"EditMultipleMaterial")
    const editData = EditMultipleMaterial[description] ?? {};
    console.log(editData,"editData")
    console.log(materialll,"materialll")
    return {
      id: index,
      material: materialll?.Material,
      materialType: materialll?.["Material Type"],
      industrySector: materialll?.["Industry Sector"],
      description,
      language: materialll?.Language,
      baseUnit:
        editData?.["Base Unit"] ??
        materialll["Basic Data"]?.["General Data"][3]?.value,
      materialGroup:
        editData?.["Material Group"] ??
        materialll["Basic Data"]?.["General Data"][5]?.value,
      oldMaterialNo:
      editData?.["Old Material Number"] ??
        materialll?.["Basic Data"]?.["General Data"][7]?.value,
      extMatlGroup:
        editData?.["Ext Matl Group"] ??
        materialll["Basic Data"]?.["General Data"][0]?.value,
      division:
        editData?.["Division"] ??
        materialll["Basic Data"]?.["General Data"][6]?.value,
      productHierarchy:
        editData?.["Product Hierarchy"] ??
        materialll["Basic Data"]?.["General Data"][5]?.value,
    };
  });

  const columns = [
    {
      field: "material",
      headerName: "Material",
      editable: false,
      flex: 1,
      renderCell: (params) => {
        const materialNo = params.row.material;
        const hasDirectMatchMaterialNo = Array.isArray(directMatchMaterialNo)
            ? directMatchMaterialNo.includes(materialNo)
            : directMatchMaterialNo === materialNo;

        const isSelected = selectedRows.includes(params.id);
        const probablesForMaterialNo = probablesMaterialNo[materialNo] || [];
        const isMatchMaterialNo = probablesForMaterialNo.includes(materialNo);
        const showInfoIconMaterialNo =
            isSelected && (hasDirectMatchMaterialNo || probablesForMaterialNo.length > 0);

        const tooltipContentMaterialNo = hasDirectMatchMaterialNo
            ? 'Direct Match'
            : probablesForMaterialNo.length > 0
                ? `Probable Matches: ${probablesForMaterialNo.join(', ')}`
                : '';

        return (
            <div style={{ display: 'flex', alignItems: 'center', color: isMatchMaterialNo ? 'red' : 'unset' }}>
                {params?.value}
                {showInfoIconMaterialNo && (
                    <Tooltip title={tooltipContentMaterialNo}>
                        <IconButton sx={{ fontSize: '12px' }}>
                            <InfoOutlined />
                        </IconButton>
                    </Tooltip>
                )}
            </div>
        );
    },
    },
    {
      field: "materialType",
      headerName: "Material Type",
      editable: false,
      flex: 1,
    },
    {
      field: 'description',
      headerName: 'Material Description',
      editable: false,
      flex: 1,
      renderCell: (params) => {
        // Extract the description from the selected row
        const description = params.row.description;
        const hasDirectMatch = Array.isArray(directMatch) ? directMatch.includes(description) : directMatch === description;
    
        const isSelected = selectedRows.includes(params.id);
        // Display probables for the selected description
        const probablesForDescription = probables[description] || [];
        const isMatch = probablesForDescription.includes(description);
        // Show tooltip and button only for the selected row
        const showInfoIcon = isSelected && (hasDirectMatch || probablesForDescription.length > 0);
    
        // Display probables in the tooltip if no direct match
        const tooltipContent = hasDirectMatch
          ? 'Direct Match'
          : probablesForDescription.length > 0
          ? `Probable Matches: ${probablesForDescription.join(', ')}`
          : '';
    
        return (
          <div style={{ display: 'flex', alignItems: 'center', color: isMatch ? 'red' : 'unset' }}>
            {params?.value}
            {showInfoIcon && (
              <Tooltip title={tooltipContent}>
                <IconButton sx={{ fontSize: '12px' }}>
                  <InfoOutlined />
                </IconButton>
              </Tooltip>
            )}
          </div>
        );
      },
    },
  
    {
      field: "industrySector",
      headerName: "Industry Sector",
      editable: false,
      flex: 1,
    },
    {
      field: "language",
      headerName: "Language",
      editable: false,
      flex: 1,
    },
    {
      field: "baseUnit",
      headerName: "Base Unit",
      editable: false,
      flex: 1,
    },
    {
      field: "materialGroup",
      headerName: "Material Group",
      editable: false,
      flex: 1,
    },
    {
      field: "oldMaterialNo",
      headerName: "Old Material No.",
      editable: false,
      flex: 1,
    },

    {
      field: "extMatlGroup",
      headerName: "Ext Material Group",
      editable: false,
      flex: 1,
    },
    {
      field: "division",
      headerName: "Division",
      editable: false,
      flex: 1,
    },
    {
      field: "productHierarchy",
      headerName: "Product Hierarchy",
      editable: false,
      flex: 1,
    },
  ];

  const getValueForFieldName = (data, fieldName) => {
    const field = data?.find((field) => field?.fieldName === fieldName);
    return field ? field.value : "";
  };

  var payloadmapping = MultipleMaterial.map((x) => {
    return {
      ProductID: "",
      BasicDataID: "",
      // TaskId:"",
      // ProductID: 17,
      Product: x?.["Material"],
      ProductType: x?.["Material Type"],
      ViewNames: "Basic Data",
      CreationID: "",
      Description: x?.Description,
      EditID: "",
      ExtendID: "",
      MassCreationID: "",
      MassEditID: "",
      MassExtendID: "",
      CrossPlantStatus: "01",
      CrossPlantStatusValidityDate: "/Date(1694995200000)/",
      // CreationDate: "",
      // CreatedByUser: "",
      // LastChangeDate: "",
      // LastChangeDateTime: "",
      // ReqCreatedOn: headerData?.ReqCreatedOn ? headerData?.ReqCreatedOn : "",
      ReqCreatedOn: x?.ReqCreatedOn
        ? "/Date(" + Date.parse(x?.ReqCreatedOn) + ")/"
        : "",
      ReqCreatedBy: x?.ReqCreatedBy ? x?.ReqCreatedBy : userData?.user_id,
      // ReqUpdatedOn: headerData?.ReqUpdatedOn?headerData?.ReqUpdatedOn:"",
      IsMarkedForDeletion: x?.IsMarkedForDeletion ? x?.IsMarkedForDeletion : "",
      ProductOldID: getValueForFieldName(
        x?.["Basic Data"]?.["General Data"],
        "Old Material Number"
      ),
      GrossWeight: getValueForFieldName(
        x?.["Basic Data"]?.["Dimensions/ EANS"],
        "Gross Weight"
      )
        ? getValueForFieldName(
            x?.["Basic Data"]?.["Dimensions/ EANS"],
            "Gross Weight"
          )
        : "",
      PurchaseOrderQuantityUnit: x?.payload?.OrderUnit
        ? x?.payload?.OrderUnit
        : "",
      SourceOfSupply: "",
      WeightUnit: getValueForFieldName(
        x?.["Basic Data"]?.["Dimensions/ EANS"],
        "Weight Unit"
      )
        ? getValueForFieldName(
            x?.["Basic Data"]?.["Dimensions/ EANS"],
            "Weight Unit"
          )
        : "",
      NetWeight: getValueForFieldName(
        x?.["Basic Data"]?.["Dimensions/ EANS"],
        "Net Weight"
      )
        ? getValueForFieldName(
            x?.["Basic Data"]?.["Dimensions/ EANS"],
            "Net Weight"
          )
        : "",
      CountryOfOrigin: "",
      CompetitorID: "",
      ProductGroup: getValueForFieldName(
        x?.["Basic Data"]?.["General Data"],
        "Material Group"
      ),
      BaseUnit: getValueForFieldName(
        x?.["Basic Data"]?.["General Data"],
        "Base Unit"
      ),
      ItemCategoryGroup: getValueForFieldName(
        x?.["Basic Data"]?.["General Data"],
        "Item Category Group"
      ),
      ProductHierarchy: getValueForFieldName(
        x?.["Basic Data"]?.["General Data"],
        "Product Hierarchy"
      ),
      Division: getValueForFieldName(
        x?.["Basic Data"]?.["General Data"],
        "Division"
      ),
      VarblPurOrdUnitIsActive: x?.payload?.VarPurOrderUnitActive
        ? x?.payload?.VarPurOrderUnitActive
        : "",
      VolumeUnit: getValueForFieldName(
        x?.["Basic Data"]?.["Dimensions/ EANS"],
        "Volume Unit"
      )
        ? getValueForFieldName(
            x?.["Basic Data"]?.["Dimensions/ EANS"],
            "Volume Unit"
          )
        : "",
      MaterialVolume: getValueForFieldName(
        x?.["Basic Data"]?.["Dimensions/ EANS"],
        "Volume"
      )
        ? getValueForFieldName(
            x?.["Basic Data"]?.["Dimensions/ EANS"],
            "Volume"
          )
        : "",
      ANPCode: getValueForFieldName(
        x?.["Basic Data"]?.["Brazil Tax Data"],
        "ANP Code"
      )
        ? getValueForFieldName(
            x?.["Basic Data"]?.["Brazil Tax Data"],
            "ANP Code"
          )
        : "",
      Brand: "",
      ProcurementRule: "",
      ValidityStartDate: "",
      LowLevelCode: "",
      ProdNoInGenProdInPrepackProd: "",
      // SerialIdentifierAssgmtProfile:getValueForFieldName(x?.["Basic Data"]?.["WM Execution Data"], "Serial Number Profile")
      // ? getValueForFieldName(x?.["Basic Data"]?.["WM Execution Data"], "Serial Number Profile")
      // : "",
      SizeOrDimensionText: getValueForFieldName(
        x?.["Basic Data"]?.["Dimensions/ EANS"],
        "Size/Dimensions"
      ),
      IndustryStandardName: getValueForFieldName(
        x?.["Basic Data"]?.["Other Data"],
        "Ind Std Name"
      ),
      ProductStandardID: getValueForFieldName(
        x?.["Basic Data"]?.["Dimensions/ EANS"],
        "International Article Number (EAN/UPC)"
      ),
      InternationalArticleNumberCat: getValueForFieldName(
        x?.["Basic Data"]?.["Dimensions/ EANS"],
        "Category Of International Article Number (EAN)"
      ),
      ProductIsConfigurable: getValueForFieldName(
        x?.["Basic Data"]?.["Client Specific Configuration"],
        "Configurable Material"
      )
        ? getValueForFieldName(
            x?.["Basic Data"]?.["General Data"],
            "Configurable Material"
          )
        : "false",
      IsBatchManagementRequired: x?.payload?.BatchManagement
        ? x?.payload?.BatchManagement
        : "false",
      ExternalProductGroup: getValueForFieldName(
        x?.["Basic Data"]?.["General Data"],
        "Ext Matl Group"
      ),
      CrossPlantConfigurableProduct: getValueForFieldName(
        x?.["Basic Data"]?.["Client Specific Configuration"],
        "Cross Plant Conf Material"
      ),
      SerialNoExplicitnessLevel: "",
      ProductManufacturerNumber: "",
      ManufacturerNumber: "",
      ManufacturerPartProfile: "",
      QltyMgmtInProcmtIsActive: "false",
      IndustrySector: x?.["Industry Sector"] ? x?.["Industry Sector"] : "",
      ChangeNumber: getValueForFieldName(
        x?.["Basic Data"]?.["Design Drawing"],
        "Doc Change Number"
      )
        ? getValueForFieldName(
            x?.["Basic Data"]?.["Design Drawing"],
            "Doc Change Number"
          )
        : "",
      MaterialRevisionLevel: "",
      HandlingIndicator: getValueForFieldName(
        x?.["Basic Data"]?.["WM Execution Data"],
        "Handling Indicator"
      )
        ? getValueForFieldName(
            x?.["Basic Data"]?.["WM Execution Data"],
            "Handling Indicator"
          )
        : "",
      WarehouseProductGroup: getValueForFieldName(
        x?.["Basic Data"]?.["WM Execution Data"],
        "Warehouse Material Group"
      )
        ? getValueForFieldName(
            x?.["Basic Data"]?.["WM Execution Data"],
            "Warehouse Material Group"
          )
        : "",
      WarehouseStorageCondition: getValueForFieldName(
        x?.["Basic Data"]?.["WM Execution Data"],
        "Warehouse Storage Condition"
      )
        ? getValueForFieldName(
            x?.["Basic Data"]?.["WM Execution Data"],
            "Warehouse Storage Condition"
          )
        : "",
      StandardHandlingUnitType: getValueForFieldName(
        x?.["Basic Data"]?.["WM Execution Data"],
        "Std HU Type"
      )
        ? getValueForFieldName(
            x?.["Basic Data"]?.["WM Execution Data"],
            "Std HU Type"
          )
        : "",
      SerialNumberProfile: getValueForFieldName(
        x?.["Basic Data"]?.["WM Execution Data"],
        "Serial Number Profile"
      )
        ? getValueForFieldName(
            x?.["Basic Data"]?.["WM Execution Data"],
            "Serial Number Profile"
          )
        : "",
      AdjustmentProfile: "",
      PreferredUnitOfMeasure: "",
      IsPilferable: getValueForFieldName(
        x?.["Basic Data"]?.["WM Execution Data"],
        "Pilferable"
      )
        ? getValueForFieldName(
            x?.["Basic Data"]?.["WM Execution Data"],
            "Pilferable"
          )
        : "false",
      IsRelevantForHzdsSubstances: getValueForFieldName(
        x?.["Basic Data"]?.["WM Execution Data"],
        "Relevant For Hazardous Substances"
      )
        ? getValueForFieldName(
            x?.["Basic Data"]?.["WM Execution Data"],
            "PilfeRelevant For Hazardous Substancesrable"
          )
        : "false",
      QuarantinePeriod: getValueForFieldName(
        x?.["Basic Data"]?.["Quality Management"],
        "Relevant For Hazardous Substances"
      )
        ? getValueForFieldName(
            x?.["Basic Data"]?.["Quality Management"],
            "Relevant For Hazardous Substances"
          )
        : "0",
      TimeUnitForQuarantinePeriod: "",
      QualityInspectionGroup: getValueForFieldName(
        x?.["Basic Data"]?.["Quality Management"],
        "Quality Inspection Group"
      )
        ? getValueForFieldName(
            x?.["Basic Data"]?.["Quality Management"],
            "Quality Inspection Group"
          )
        : "",
      AuthorizationGroup: getValueForFieldName(
        x?.["Basic Data"]?.["General Data"],
        "Authorization Group"
      ),
      HandlingUnitType: getValueForFieldName(
        x?.["Basic Data"]?.["General Packaging"],
        "Handling Unit Type"
      )
        ? getValueForFieldName(
            x?.["Basic Data"]?.["General Packaging"],
            "Handling Unit Type"
          )
        : "",
      HasVariableTareWeight: getValueForFieldName(
        x?.["Basic Data"]?.["General Packaging"],
        "Variable Tare Weight"
      )
        ? getValueForFieldName(
            x?.["Basic Data"]?.["General Packaging"],
            "Variable Tare Weight"
          )
        : "false",
      MaximumPackagingLength: getValueForFieldName(
        x?.["Basic Data"]?.["Maximum Length"],
        "Max Packaging Length"
      )
        ? getValueForFieldName(
            x?.["Basic Data"]?.["Maximum Length"],
            "Max Packaging Length"
          )
        : "0",
      MaximumPackagingWidth: getValueForFieldName(
        x?.["Basic Data"]?.["Maximum Length"],
        "Max Packaging Width"
      )
        ? getValueForFieldName(
            x?.["Basic Data"]?.["Maximum Length"],
            "Max Packaging Width"
          )
        : "0",
      MaximumPackagingHeight: getValueForFieldName(
        x?.["Basic Data"]?.["Maximum Length"],
        "Max Packaging Height"
      )
        ? getValueForFieldName(
            x?.["Basic Data"]?.["Maximum Length"],
            "Max Packaging Height"
          )
        : "0",
      UnitForMaxPackagingDimensions: "",
      to_Description: [
        {
          Product: "",
          DescriptionID: "",
          // Language: x.Language?x.Language:"EN",
          Language: x.Language,
          ProductDescription: x.Description,
        },
      ],
    };
  });
  const handleSelectionModelChange = (selectedIds) => {
    console.log("Selected Rows: ", selectedIds);
    setSelectedRows(selectedIds);
    setDuplicateDescEnabled(selectedIds.length > 0);
    
    if (selectedIds.length > 0) {
      // Trigger duplicate check when there is at least one selected row
      handleDuplicateDescClick();
    } else {
      // If no row is selected, reset the state and disable both buttons
      setDirectMatch(false);
      setProbables([]);
      setIsSubmitForApprovalEnabled(false);
      setIsListOpen(false);
    }
  };
  
  const handleDuplicateDescClick = (type) => {
    const selectedData = initialRows.filter((_, index) =>
      selectedRows.includes(index)
    );
  
    const selectedDescriptions = selectedData.map((item) => item.description);
    const selectedMaterialNos = selectedData.map((item) => item.material);
  
    const handleDuplicateCheck = (type) => {
      const hSuccess = (data) => {
        setDuplicateCheck({ ...duplicateCheck, [type]: data.body });
        setResponseList(data.body);
  
        // Check if data.body is defined
        if (data.body) {
          const concatenatedValues = Object.keys(data.body);
          console.log(`Concatenated Values (${type}):`, concatenatedValues);
  
          const probablesForSelectedDescriptions = selectedDescriptions.reduce(
            (result, description) => {
              const probables = data.body[description];
              if (probables && probables.length > 0) {
                result[description] = probables;
              }
              return result;
            },
            {}
          );
  
          console.log(
            `Probables for Selected Descriptions (${type}):`,
            probablesForSelectedDescriptions
          );
  
          // Check if any value from "Probables for Selected Descriptions" matches with "Concatenated Values"
          const isDirectMatch = Object.values(probablesForSelectedDescriptions).some(
            (probables) => probables.some((value) => concatenatedValues.includes(value))
          );
  
          console.log(`isDirectMatch (${type}):`, isDirectMatch);
          if (type === 'description' && setIsListOpen) {
            setIsSubmitForApprovalEnabled(true);
          } else if (type === 'number' && setIsListOpenMaterialNo) {
            setIsSubmitForApprovalMaterialNo(true);
          } else {
            if (type === 'description') {
              setIsSubmitForApprovalEnabled(false);
            } else if (type === 'number') {
              setIsSubmitForApprovalMaterialNo(false);
            }
          }
  
          if (type === 'description') {
            setIsListOpen(isDirectMatch);
            setDirectMatch(isDirectMatch);
            setProbables(probablesForSelectedDescriptions);
        } else if (type === 'number') {
            // Adjust the variable for material numbers
            const probablesForSelectedMaterialNo = selectedMaterialNos.reduce(
                (result, materialNo) => {
                    const probables = data.body[materialNo];
                    if (probables && probables.length > 0) {
                        result[materialNo] = probables;
                    }
                    return result;
                },
                {}
            );
            const isDirectMatchMaterialNo = Object.values(probablesForSelectedMaterialNo).some(
              (probables) => probables.some((value) => concatenatedValues.includes(value))
          );
          setIsListOpenMaterialNo(isDirectMatchMaterialNo);
            setDirectMatchMaterialNo(isDirectMatchMaterialNo);
            setProbablesMaterialNo(probablesForSelectedMaterialNo);
        }

  
          setIsLoading(false);
        } else {
          // Handle the case where data.body is undefined or null
          console.error('Data body is undefined or null');
        }
      };
  
      const hError = (error) => {
        console.log(error);
      };
  
      // Construct the URL with only selected descriptions or numbers based on the type
      const url = `/${destination_MaterialMgmt}/massAction/${
        type === 'description'
          ? 'fetchMatDescsDupliChk'
          : 'fetchMaterialNosDupliChk'
      }?${type === 'description' ? 'descriptionsToCheck' : 'materialNosToCheck'}=${
        type === 'description'
          ? (selectedDescriptions.join(','))
          : (selectedMaterialNos.join(','))
      }`;
      
  
      doAjax(url, 'get', hSuccess, hError);
    };
  
    // Call the function for both description and number checks
    handleDuplicateCheck('description');
    handleDuplicateCheck('number');
  };
  
  const handleSubmitForApprovalCreate = (description) => {
    const selectedData = initialRows.filter((_, index) =>
      selectedRows.includes(index)
    );
    const payload = selectedData.map((x) => ({ ...payloadmapping[x?.id] }));
    // Assuming payloadmapping is an array with a single element
    const hSuccess = (data) => {
      if (data?.statusCode != 201) {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(`${data.body.message[0]}`);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        // setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Mass Create");
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Mass Material Submitted for Approval`
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        // setMessageDialogExtra(true);
        // setIsLoading(false);
      }
    };

    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/massAction/createMaterialsApprovalSubmit`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
 
  const handleSubmitForApprovalChange = () => {
    const selectedData = initialRows.filter((_, index) =>
      selectedRows.includes(index)
    );
    const payload = selectedData.map((x) => ({ ...payloadmapping[x?.id] }));
    // Assuming payloadmapping is an array with a single element
    const hSuccess = (data) => {
      if (data?.statusCode != 201) {
        setMessageDialogTitleChange("Error");
        setsuccessMsgChange(false);
        setMessageDialogMessageChange(`${data.body.message[0]}`);
        setMessageDialogSeverityChange("danger");
        setMessageDialogOKChange(false);
        // setMessageDialogExtra(true);
        handleMessageDialogClickOpenChange();
        // setIsLoading(false);
      } else {
        setMessageDialogTitleChange("Mass Create");
        console.log("success");
        setMessageDialogTitleChange("Create");
        setMessageDialogMessageChange(
          `Mass Material Change Submitted for Approval`
        );
        setMessageDialogSeverityChange("success");
        setMessageDialogOKChange(false);
        setsuccessMsgChange(true);
        handleSnackBarOpenChange();
        // setMessageDialogExtra(true);
        // setIsLoading(false);
      }
    };

    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/massAction/changeMaterialsApprovalSubmit`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  return (
    <div style={{ backgroundColor: "#FAFCFF" }}>
       <ReusableDialog
        dialogState={openMessageDialog}
        openReusableDialog={handleMessageDialogClickOpen}
        closeReusableDialog={handleMessageDialogClose}
        dialogTitle={messageDialogTitle}
        dialogMessage={messageDialogMessage}
        handleDialogConfirm={handleMessageDialogClose}
        dialogOkText={"OK"}
        // handleExtraButton={handleMessageDialogNavigate}
        dialogSeverity={messageDialogSeverity}
      />
       {successMsg && (
        <ReusableSnackBar
          openSnackBar={openSnackbar}
          alertMsg={messageDialogMessage}
          handleSnackBarClose={handleSnackBarClose}
        />
      )}
       <ReusableDialog
        dialogState={openMessageDialogChange}
        openReusableDialog={handleMessageDialogClickOpenChange}
        closeReusableDialog={handleMessageDialogCloseChange}
        dialogTitle={messageDialogTitleChange}
        dialogMessage={messageDialogMessageChange}
        handleDialogConfirm={handleMessageDialogCloseChange}
        dialogOkText={"OK"}
        // handleExtraButton={handleMessageDialogNavigate}
        dialogSeverity={messageDialogSeverityChange}
      />
        {successMsgChange && (
        <ReusableSnackBar
          openSnackBar={openSnackbarChange}
          alertMsg={messageDialogMessageChange}
          handleSnackBarClose={handleSnackBarCloseChange}
        />
      )}
      <div style={{ backgroundColor: "#FAFCFF" }}>
        <Stack spacing={1} sx={{ padding: "16px" }}>
          <Grid container>
            <Grid item md={5} sx={{ display: "flex" }}>
              <Grid>
                <IconButton
                  // onClick={handleBacktoRO}
                  color="primary"
                  aria-label="upload picture"
                  component="label"
                  sx={iconButton_SpacingSmall}
                >
                  <ArrowCircleLeftOutlinedIcon
                    style={{ height: "1em", width: "1em", color: "#000000" }}
                    // sx={{
                    //   fontSize: "1.5em",
                    //   color: "#000000",
                    // }}
                    onClick={() => {
                      navigate(
                        "/masterDataCockpit/materialMaster/materialSingle"
                      );
                      dispatch(clearPayload());
                      dispatch(clearOrgData());
                    }}
                  />
                </IconButton>
              </Grid>

              {/* {massHandleType === "Create" ? (
                <Grid>
                  <Typography variant="h3">
                    <strong>Create Multiple Materials</strong>
                  </Typography>
                  <Typography variant="body2" color="#777">
                    This view displays list of uploaded materials
                  </Typography>
                </Grid>
              ) : massHandleType === "Change" ? (
                <Grid>
                  <Typography variant="h3">
                    <strong>Change Multiple Materials</strong>
                  </Typography>
                  <Typography variant="body2" color="#777">
                    This view displays list of uploaded materials need to be
                    changed
                  </Typography>
                </Grid>
              ) : (
                ""
              )} */}

                <Grid>
                  <Typography variant="h3">
                    <strong>Create Multiple Materials</strong>
                  </Typography>
                  <Typography variant="body2" color="#777">
                    This view displays list of uploaded materials
                  </Typography>
                </Grid>
             
            </Grid>
          </Grid>

          <Grid item sx={{ position: "relative" }}>
            <Stack>
              <ReusableTable
                isLoading={isLoading}
                width="100%"
                title={"Mass Material Master List (" + initialRows.length + ")"}
                rows={initialRows}
                columns={columns}
                pageSize={10}
                getRowIdValue={"id"}
                hideFooter={false}
                checkboxSelection={true}
                disableSelectionOnClick={true}
                status_onRowSingleClick={true}
                onRowsSelectionHandler={handleSelectionModelChange}
                callback_onRowSingleClick={(params) => {
                  // Extract the description from the selected row
                  const description = params.row.description;
                  navigate(
                    `/masterDataCockpit/materialMaster/editMultipleMaterial/${description}`,
                    {
                      state: params.row,
                    }
                  );
                }}
                stopPropagation_Column={"action"}
                status_onRowDoubleClick={true}
              />
            </Stack>
          </Grid>
        </Stack>
      </div>
      <Paper
        sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
        elevation={2}
      >
        <BottomNavigation
          className="container_BottomNav"
          showLabels
          sx={{
            display: "flex",
            gap:1,
            justifyContent: "flex-end",
          }}
          value={value}
        >
          {massHandleType === "Create" ? (
            [
              <Button
                key="duplicateDescButton"
                variant="contained"
                size="small"
                sx={{ ...button_Primary }}
                onClick={handleDuplicateDescClick}
                disabled={!isDuplicateDescEnabled}
              >
                Duplicate Check
              </Button>,
              <Button
              key="submitForApprovalButton"
              variant="contained"
              size="small"
              sx={{ ...button_Primary }}
              onClick={handleSubmitForApprovalCreate}
              disabled={!isSubmitForApprovalEnabled || directMatch}
            >
              Submit for Approval
            </Button>
            ]
          ) : massHandleType === "Change" ? (
            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary }}
              onClick={handleSubmitForApprovalChange}
            >
              Submit for Approval
            </Button>
          ) : (
            ""
          )}
        </BottomNavigation>
      </Paper>
    </div>
  );
};

export default MassMaterialTable;
