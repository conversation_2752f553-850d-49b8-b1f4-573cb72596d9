import{r as _,fU as C,fV as Ht,i as le,k as zt,cB as B,bs as Ft,T as y,E as Yt,aw as Wt,B as Y,bF as Kt,q as Xt,u as qt,b as Gt,K as Ae,j as l,a as n,G as D,M as ce,x as Z,ar as Re,F as Jt,C as Ie,N as de,I as ke,h as Zt,aE as Qt,t as Le,aD as ea,e8 as ta,z as aa,Y as ra,y as ue}from"./index-17b8d91e.js";import{d as oa}from"./ArrowCircleLeftOutlined-2a09f8e2.js";import{a as na,d as sa}from"./SlideshowOutlined-23143d05.js";import{d as ia}from"./CloudUpload-27b6d63e.js";import{S as la}from"./SingleSelectDropdown-29664b58.js";import{l as ca}from"./index-d550e3b0.js";import{c as Ve,r as da}from"./memoize-one.esm-4fe54fe0.js";import{r as He}from"./index-d861d92d.js";import{M as ua}from"./UtilDoc-d76e2af6.js";import{V as pa}from"./icons-35b62b58.js";import{D as $e}from"./DatePicker-e260eb3e.js";var ya=Object.create,Q=Object.defineProperty,ha=Object.getOwnPropertyDescriptor,fa=Object.getOwnPropertyNames,ma=Object.getPrototypeOf,_a=Object.prototype.hasOwnProperty,ga=(e,t)=>{for(var a in t)Q(e,a,{get:t[a],enumerable:!0})},ze=(e,t,a,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of fa(t))!_a.call(e,r)&&r!==a&&Q(e,r,{get:()=>t[r],enumerable:!(o=ha(t,r))||o.enumerable});return e},be=(e,t,a)=>(a=e!=null?ya(ma(e)):{},ze(t||!e||!e.__esModule?Q(a,"default",{value:e,enumerable:!0}):a,e)),Pa=e=>ze(Q({},"__esModule",{value:!0}),e),Fe={};ga(Fe,{callPlayer:()=>La,getConfig:()=>Ia,getSDK:()=>Ra,isBlobUrl:()=>Ua,isMediaStream:()=>$a,lazy:()=>Oa,omit:()=>ka,parseEndTime:()=>xa,parseStartTime:()=>Ca,queryString:()=>Aa,randomString:()=>Ma,supportsWebKitPresentationMode:()=>Ba});var ee=Pa(Fe),ba=be(_),va=be(ca),wa=be(Ve);const Oa=e=>ba.default.lazy(async()=>{const t=await e();return typeof t.default=="function"?t:t.default}),Sa=/[?&#](?:start|t)=([0-9hms]+)/,Ta=/[?&#]end=([0-9hms]+)/,me=/(\d+)(h|m|s)/g,Da=/^\d+$/;function Ye(e,t){if(e instanceof Array)return;const a=e.match(t);if(a){const o=a[1];if(o.match(me))return Ea(o);if(Da.test(o))return parseInt(o)}}function Ea(e){let t=0,a=me.exec(e);for(;a!==null;){const[,o,r]=a;r==="h"&&(t+=parseInt(o,10)*60*60),r==="m"&&(t+=parseInt(o,10)*60),r==="s"&&(t+=parseInt(o,10)),a=me.exec(e)}return t}function Ca(e){return Ye(e,Sa)}function xa(e){return Ye(e,Ta)}function Ma(){return Math.random().toString(36).substr(2,5)}function Aa(e){return Object.keys(e).map(t=>`${t}=${e[t]}`).join("&")}function pe(e){return window[e]?window[e]:window.exports&&window.exports[e]?window.exports[e]:window.module&&window.module.exports&&window.module.exports[e]?window.module.exports[e]:null}const L={},Ra=function(t,a,o=null,r=()=>!0,d=va.default){const S=pe(a);return S&&r(S)?Promise.resolve(S):new Promise((s,T)=>{if(L[t]){L[t].push({resolve:s,reject:T});return}L[t]=[{resolve:s,reject:T}];const M=b=>{L[t].forEach(R=>R.resolve(b))};if(o){const b=window[o];window[o]=function(){b&&b(),M(pe(a))}}d(t,b=>{b?(L[t].forEach(R=>R.reject(b)),L[t]=null):o||M(pe(a))})})};function Ia(e,t){return(0,wa.default)(t.config,e.config)}function ka(e,...t){const a=[].concat(...t),o={},r=Object.keys(e);for(const d of r)a.indexOf(d)===-1&&(o[d]=e[d]);return o}function La(e,...t){if(!this.player||!this.player[e]){let a=`ReactPlayer: ${this.constructor.displayName} player could not call %c${e}%c – `;return this.player?this.player[e]||(a+="The method was not available"):a+="The player was not available",console.warn(a,"font-weight: bold",""),null}return this.player[e](...t)}function $a(e){return typeof window<"u"&&typeof window.MediaStream<"u"&&e instanceof window.MediaStream}function Ua(e){return/^blob:/.test(e)}function Ba(e=document.createElement("video")){const t=/iPhone|iPod/.test(navigator.userAgent)===!1;return e.webkitSupportsPresentationMode&&typeof e.webkitSetPresentationMode=="function"&&t}var ve=Object.defineProperty,Na=Object.getOwnPropertyDescriptor,ja=Object.getOwnPropertyNames,Va=Object.prototype.hasOwnProperty,Ha=(e,t)=>{for(var a in t)ve(e,a,{get:t[a],enumerable:!0})},za=(e,t,a,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of ja(t))!Va.call(e,r)&&r!==a&&ve(e,r,{get:()=>t[r],enumerable:!(o=Na(t,r))||o.enumerable});return e},Fa=e=>za(ve({},"__esModule",{value:!0}),e),We={};Ha(We,{AUDIO_EXTENSIONS:()=>we,DASH_EXTENSIONS:()=>st,FLV_EXTENSIONS:()=>it,HLS_EXTENSIONS:()=>Se,MATCH_URL_DAILYMOTION:()=>at,MATCH_URL_FACEBOOK:()=>Ge,MATCH_URL_FACEBOOK_WATCH:()=>Je,MATCH_URL_KALTURA:()=>nt,MATCH_URL_MIXCLOUD:()=>rt,MATCH_URL_MUX:()=>qe,MATCH_URL_SOUNDCLOUD:()=>Ke,MATCH_URL_STREAMABLE:()=>Ze,MATCH_URL_TWITCH_CHANNEL:()=>tt,MATCH_URL_TWITCH_VIDEO:()=>et,MATCH_URL_VIDYARD:()=>ot,MATCH_URL_VIMEO:()=>Xe,MATCH_URL_WISTIA:()=>Qe,MATCH_URL_YOUTUBE:()=>_e,VIDEO_EXTENSIONS:()=>Oe,canPlay:()=>Wa});var Ya=Fa(We),Ue=ee;const _e=/(?:youtu\.be\/|youtube(?:-nocookie|education)?\.com\/(?:embed\/|v\/|watch\/|watch\?v=|watch\?.+&v=|shorts\/|live\/))((\w|-){11})|youtube\.com\/playlist\?list=|youtube\.com\/user\//,Ke=/(?:soundcloud\.com|snd\.sc)\/[^.]+$/,Xe=/vimeo\.com\/(?!progressive_redirect).+/,qe=/stream\.mux\.com\/(?!\w+\.m3u8)(\w+)/,Ge=/^https?:\/\/(www\.)?facebook\.com.*\/(video(s)?|watch|story)(\.php?|\/).+$/,Je=/^https?:\/\/fb\.watch\/.+$/,Ze=/streamable\.com\/([a-z0-9]+)$/,Qe=/(?:wistia\.(?:com|net)|wi\.st)\/(?:medias|embed)\/(?:iframe\/)?([^?]+)/,et=/(?:www\.|go\.)?twitch\.tv\/videos\/(\d+)($|\?)/,tt=/(?:www\.|go\.)?twitch\.tv\/([a-zA-Z0-9_]+)($|\?)/,at=/^(?:(?:https?):)?(?:\/\/)?(?:www\.)?(?:(?:dailymotion\.com(?:\/embed)?\/video)|dai\.ly)\/([a-zA-Z0-9]+)(?:_[\w_-]+)?(?:[\w.#_-]+)?/,rt=/mixcloud\.com\/([^/]+\/[^/]+)/,ot=/vidyard.com\/(?:watch\/)?([a-zA-Z0-9-_]+)/,nt=/^https?:\/\/[a-zA-Z]+\.kaltura.(com|org)\/p\/([0-9]+)\/sp\/([0-9]+)00\/embedIframeJs\/uiconf_id\/([0-9]+)\/partner_id\/([0-9]+)(.*)entry_id.([a-zA-Z0-9-_].*)$/,we=/\.(m4a|m4b|mp4a|mpga|mp2|mp2a|mp3|m2a|m3a|wav|weba|aac|oga|spx)($|\?)/i,Oe=/\.(mp4|og[gv]|webm|mov|m4v)(#t=[,\d+]+)?($|\?)/i,Se=/\.(m3u8)($|\?)/i,st=/\.(mpd)($|\?)/i,it=/\.(flv)($|\?)/i,ge=e=>{if(e instanceof Array){for(const t of e)if(typeof t=="string"&&ge(t)||ge(t.src))return!0;return!1}return(0,Ue.isMediaStream)(e)||(0,Ue.isBlobUrl)(e)?!0:we.test(e)||Oe.test(e)||Se.test(e)||st.test(e)||it.test(e)},Wa={youtube:e=>e instanceof Array?e.every(t=>_e.test(t)):_e.test(e),soundcloud:e=>Ke.test(e)&&!we.test(e),vimeo:e=>Xe.test(e)&&!Oe.test(e)&&!Se.test(e),mux:e=>qe.test(e),facebook:e=>Ge.test(e)||Je.test(e),streamable:e=>Ze.test(e),wistia:e=>Qe.test(e),twitch:e=>et.test(e)||tt.test(e),dailymotion:e=>at.test(e),mixcloud:e=>rt.test(e),vidyard:e=>ot.test(e),kaltura:e=>nt.test(e),file:ge};var Te=Object.defineProperty,Ka=Object.getOwnPropertyDescriptor,Xa=Object.getOwnPropertyNames,qa=Object.prototype.hasOwnProperty,Ga=(e,t)=>{for(var a in t)Te(e,a,{get:t[a],enumerable:!0})},Ja=(e,t,a,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of Xa(t))!qa.call(e,r)&&r!==a&&Te(e,r,{get:()=>t[r],enumerable:!(o=Ka(t,r))||o.enumerable});return e},Za=e=>Ja(Te({},"__esModule",{value:!0}),e),lt={};Ga(lt,{default:()=>er});var Qa=Za(lt),E=ee,w=Ya,er=[{key:"youtube",name:"YouTube",canPlay:w.canPlay.youtube,lazyPlayer:(0,E.lazy)(()=>C(()=>import("./YouTube-34cf0149.js").then(e=>e.Y),["assets/YouTube-34cf0149.js","assets/index-17b8d91e.js","assets/index-a517bf54.css"]))},{key:"soundcloud",name:"SoundCloud",canPlay:w.canPlay.soundcloud,lazyPlayer:(0,E.lazy)(()=>C(()=>import("./SoundCloud-50f99fbe.js").then(e=>e.S),["assets/SoundCloud-50f99fbe.js","assets/index-17b8d91e.js","assets/index-a517bf54.css"]))},{key:"vimeo",name:"Vimeo",canPlay:w.canPlay.vimeo,lazyPlayer:(0,E.lazy)(()=>C(()=>import("./Vimeo-4ea56b1c.js").then(e=>e.V),["assets/Vimeo-4ea56b1c.js","assets/index-17b8d91e.js","assets/index-a517bf54.css"]))},{key:"mux",name:"Mux",canPlay:w.canPlay.mux,lazyPlayer:(0,E.lazy)(()=>C(()=>import("./Mux-2c270ac2.js").then(e=>e.M),["assets/Mux-2c270ac2.js","assets/index-17b8d91e.js","assets/index-a517bf54.css"]))},{key:"facebook",name:"Facebook",canPlay:w.canPlay.facebook,lazyPlayer:(0,E.lazy)(()=>C(()=>import("./Facebook-818c9140.js").then(e=>e.F),["assets/Facebook-818c9140.js","assets/index-17b8d91e.js","assets/index-a517bf54.css"]))},{key:"streamable",name:"Streamable",canPlay:w.canPlay.streamable,lazyPlayer:(0,E.lazy)(()=>C(()=>import("./Streamable-c6211302.js").then(e=>e.S),["assets/Streamable-c6211302.js","assets/index-17b8d91e.js","assets/index-a517bf54.css"]))},{key:"wistia",name:"Wistia",canPlay:w.canPlay.wistia,lazyPlayer:(0,E.lazy)(()=>C(()=>import("./Wistia-e28a0951.js").then(e=>e.W),["assets/Wistia-e28a0951.js","assets/index-17b8d91e.js","assets/index-a517bf54.css"]))},{key:"twitch",name:"Twitch",canPlay:w.canPlay.twitch,lazyPlayer:(0,E.lazy)(()=>C(()=>import("./Twitch-43a31bd8.js").then(e=>e.T),["assets/Twitch-43a31bd8.js","assets/index-17b8d91e.js","assets/index-a517bf54.css"]))},{key:"dailymotion",name:"DailyMotion",canPlay:w.canPlay.dailymotion,lazyPlayer:(0,E.lazy)(()=>C(()=>import("./DailyMotion-2494443d.js").then(e=>e.D),["assets/DailyMotion-2494443d.js","assets/index-17b8d91e.js","assets/index-a517bf54.css"]))},{key:"mixcloud",name:"Mixcloud",canPlay:w.canPlay.mixcloud,lazyPlayer:(0,E.lazy)(()=>C(()=>import("./Mixcloud-49c81762.js").then(e=>e.M),["assets/Mixcloud-49c81762.js","assets/index-17b8d91e.js","assets/index-a517bf54.css"]))},{key:"vidyard",name:"Vidyard",canPlay:w.canPlay.vidyard,lazyPlayer:(0,E.lazy)(()=>C(()=>import("./Vidyard-279496d6.js").then(e=>e.V),["assets/Vidyard-279496d6.js","assets/index-17b8d91e.js","assets/index-a517bf54.css"]))},{key:"kaltura",name:"Kaltura",canPlay:w.canPlay.kaltura,lazyPlayer:(0,E.lazy)(()=>C(()=>import("./Kaltura-40ded601.js").then(e=>e.K),["assets/Kaltura-40ded601.js","assets/index-17b8d91e.js","assets/index-a517bf54.css"]))},{key:"file",name:"FilePlayer",canPlay:w.canPlay.file,canEnablePIP:e=>w.canPlay.file(e)&&(document.pictureInPictureEnabled||(0,E.supportsWebKitPresentationMode)())&&!w.AUDIO_EXTENSIONS.test(e),lazyPlayer:(0,E.lazy)(()=>C(()=>import("./FilePlayer-fb431e29.js").then(e=>e.F),["assets/FilePlayer-fb431e29.js","assets/index-17b8d91e.js","assets/index-a517bf54.css"]))}],tr=Object.create,te=Object.defineProperty,ar=Object.getOwnPropertyDescriptor,rr=Object.getOwnPropertyNames,or=Object.getPrototypeOf,nr=Object.prototype.hasOwnProperty,sr=(e,t)=>{for(var a in t)te(e,a,{get:t[a],enumerable:!0})},ct=(e,t,a,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of rr(t))!nr.call(e,r)&&r!==a&&te(e,r,{get:()=>t[r],enumerable:!(o=ar(t,r))||o.enumerable});return e},ir=(e,t,a)=>(a=e!=null?tr(or(e)):{},ct(t||!e||!e.__esModule?te(a,"default",{value:e,enumerable:!0}):a,e)),lr=e=>ct(te({},"__esModule",{value:!0}),e),dt={};sr(dt,{defaultProps:()=>ur,propTypes:()=>dr});var ut=lr(dt),cr=ir(Ht);const{string:g,bool:O,number:$,array:ye,oneOfType:z,shape:x,object:v,func:m,node:Be}=cr.default,dr={url:z([g,ye,v]),playing:O,loop:O,controls:O,volume:$,muted:O,playbackRate:$,width:z([g,$]),height:z([g,$]),style:v,progressInterval:$,playsinline:O,pip:O,stopOnUnmount:O,light:z([O,g,v]),playIcon:Be,previewTabIndex:$,previewAriaLabel:g,fallback:Be,oEmbedUrl:g,wrapper:z([g,m,x({render:m.isRequired})]),config:x({soundcloud:x({options:v}),youtube:x({playerVars:v,embedOptions:v,onUnstarted:m}),facebook:x({appId:g,version:g,playerId:g,attributes:v}),dailymotion:x({params:v}),vimeo:x({playerOptions:v,title:g}),mux:x({attributes:v,version:g}),file:x({attributes:v,tracks:ye,forceVideo:O,forceAudio:O,forceHLS:O,forceSafariHLS:O,forceDisableHls:O,forceDASH:O,forceFLV:O,hlsOptions:v,hlsVersion:g,dashVersion:g,flvVersion:g}),wistia:x({options:v,playerId:g,customControls:ye}),mixcloud:x({options:v}),twitch:x({options:v,playerId:g}),vidyard:x({options:v})}),onReady:m,onStart:m,onPlay:m,onPause:m,onBuffer:m,onBufferEnd:m,onEnded:m,onError:m,onDuration:m,onSeek:m,onPlaybackRateChange:m,onPlaybackQualityChange:m,onProgress:m,onClickPreview:m,onEnablePIP:m,onDisablePIP:m},P=()=>{},ur={playing:!1,loop:!1,controls:!1,volume:null,muted:!1,playbackRate:1,width:"640px",height:"360px",style:{},progressInterval:1e3,playsinline:!1,pip:!1,stopOnUnmount:!0,light:!1,fallback:null,wrapper:"div",previewTabIndex:0,previewAriaLabel:"",oEmbedUrl:"https://noembed.com/embed?url={url}",config:{soundcloud:{options:{visual:!0,buying:!1,liking:!1,download:!1,sharing:!1,show_comments:!1,show_playcount:!1}},youtube:{playerVars:{playsinline:1,showinfo:0,rel:0,iv_load_policy:3,modestbranding:1},embedOptions:{},onUnstarted:P},facebook:{appId:"1309697205772819",version:"v3.3",playerId:null,attributes:{}},dailymotion:{params:{api:1,"endscreen-enable":!1}},vimeo:{playerOptions:{autopause:!1,byline:!1,portrait:!1,title:!1},title:null},mux:{attributes:{},version:"2"},file:{attributes:{},tracks:[],forceVideo:!1,forceAudio:!1,forceHLS:!1,forceDASH:!1,forceFLV:!1,hlsOptions:{},hlsVersion:"1.1.4",dashVersion:"3.1.3",flvVersion:"1.5.0",forceDisableHls:!1},wistia:{options:{},playerId:null,customControls:null},mixcloud:{options:{hide_cover:1}},twitch:{options:{},playerId:null},vidyard:{options:{}}},onReady:P,onStart:P,onPlay:P,onPause:P,onBuffer:P,onBufferEnd:P,onEnded:P,onError:P,onDuration:P,onSeek:P,onPlaybackRateChange:P,onPlaybackQualityChange:P,onProgress:P,onClickPreview:P,onEnablePIP:P,onDisablePIP:P};var pr=Object.create,K=Object.defineProperty,yr=Object.getOwnPropertyDescriptor,hr=Object.getOwnPropertyNames,fr=Object.getPrototypeOf,mr=Object.prototype.hasOwnProperty,_r=(e,t,a)=>t in e?K(e,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[t]=a,gr=(e,t)=>{for(var a in t)K(e,a,{get:t[a],enumerable:!0})},pt=(e,t,a,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of hr(t))!mr.call(e,r)&&r!==a&&K(e,r,{get:()=>t[r],enumerable:!(o=yr(t,r))||o.enumerable});return e},yt=(e,t,a)=>(a=e!=null?pr(fr(e)):{},pt(t||!e||!e.__esModule?K(a,"default",{value:e,enumerable:!0}):a,e)),Pr=e=>pt(K({},"__esModule",{value:!0}),e),f=(e,t,a)=>(_r(e,typeof t!="symbol"?t+"":t,a),a),ht={};gr(ht,{default:()=>ae});var br=Pr(ht),Ne=yt(_),vr=yt(He),ft=ut,wr=ee;const Or=5e3;class ae extends Ne.Component{constructor(){super(...arguments),f(this,"mounted",!1),f(this,"isReady",!1),f(this,"isPlaying",!1),f(this,"isLoading",!0),f(this,"loadOnReady",null),f(this,"startOnPlay",!0),f(this,"seekOnPlay",null),f(this,"onDurationCalled",!1),f(this,"handlePlayerMount",t=>{if(this.player){this.progress();return}this.player=t,this.player.load(this.props.url),this.progress()}),f(this,"getInternalPlayer",t=>this.player?this.player[t]:null),f(this,"progress",()=>{if(this.props.url&&this.player&&this.isReady){const t=this.getCurrentTime()||0,a=this.getSecondsLoaded(),o=this.getDuration();if(o){const r={playedSeconds:t,played:t/o};a!==null&&(r.loadedSeconds=a,r.loaded=a/o),(r.playedSeconds!==this.prevPlayed||r.loadedSeconds!==this.prevLoaded)&&this.props.onProgress(r),this.prevPlayed=r.playedSeconds,this.prevLoaded=r.loadedSeconds}}this.progressTimeout=setTimeout(this.progress,this.props.progressFrequency||this.props.progressInterval)}),f(this,"handleReady",()=>{if(!this.mounted)return;this.isReady=!0,this.isLoading=!1;const{onReady:t,playing:a,volume:o,muted:r}=this.props;t(),!r&&o!==null&&this.player.setVolume(o),this.loadOnReady?(this.player.load(this.loadOnReady,!0),this.loadOnReady=null):a&&this.player.play(),this.handleDurationCheck()}),f(this,"handlePlay",()=>{this.isPlaying=!0,this.isLoading=!1;const{onStart:t,onPlay:a,playbackRate:o}=this.props;this.startOnPlay&&(this.player.setPlaybackRate&&o!==1&&this.player.setPlaybackRate(o),t(),this.startOnPlay=!1),a(),this.seekOnPlay&&(this.seekTo(this.seekOnPlay),this.seekOnPlay=null),this.handleDurationCheck()}),f(this,"handlePause",t=>{this.isPlaying=!1,this.isLoading||this.props.onPause(t)}),f(this,"handleEnded",()=>{const{activePlayer:t,loop:a,onEnded:o}=this.props;t.loopOnEnded&&a&&this.seekTo(0),a||(this.isPlaying=!1,o())}),f(this,"handleError",(...t)=>{this.isLoading=!1,this.props.onError(...t)}),f(this,"handleDurationCheck",()=>{clearTimeout(this.durationCheckTimeout);const t=this.getDuration();t?this.onDurationCalled||(this.props.onDuration(t),this.onDurationCalled=!0):this.durationCheckTimeout=setTimeout(this.handleDurationCheck,100)}),f(this,"handleLoaded",()=>{this.isLoading=!1})}componentDidMount(){this.mounted=!0}componentWillUnmount(){clearTimeout(this.progressTimeout),clearTimeout(this.durationCheckTimeout),this.isReady&&this.props.stopOnUnmount&&(this.player.stop(),this.player.disablePIP&&this.player.disablePIP()),this.mounted=!1}componentDidUpdate(t){if(!this.player)return;const{url:a,playing:o,volume:r,muted:d,playbackRate:S,pip:s,loop:T,activePlayer:M,disableDeferredLoading:b}=this.props;if(!(0,vr.default)(t.url,a)){if(this.isLoading&&!M.forceLoad&&!b&&!(0,wr.isMediaStream)(a)){console.warn(`ReactPlayer: the attempt to load ${a} is being deferred until the player has loaded`),this.loadOnReady=a;return}this.isLoading=!0,this.startOnPlay=!0,this.onDurationCalled=!1,this.player.load(a,this.isReady)}!t.playing&&o&&!this.isPlaying&&this.player.play(),t.playing&&!o&&this.isPlaying&&this.player.pause(),!t.pip&&s&&this.player.enablePIP&&this.player.enablePIP(),t.pip&&!s&&this.player.disablePIP&&this.player.disablePIP(),t.volume!==r&&r!==null&&this.player.setVolume(r),t.muted!==d&&(d?this.player.mute():(this.player.unmute(),r!==null&&setTimeout(()=>this.player.setVolume(r)))),t.playbackRate!==S&&this.player.setPlaybackRate&&this.player.setPlaybackRate(S),t.loop!==T&&this.player.setLoop&&this.player.setLoop(T)}getDuration(){return this.isReady?this.player.getDuration():null}getCurrentTime(){return this.isReady?this.player.getCurrentTime():null}getSecondsLoaded(){return this.isReady?this.player.getSecondsLoaded():null}seekTo(t,a,o){if(!this.isReady){t!==0&&(this.seekOnPlay=t,setTimeout(()=>{this.seekOnPlay=null},Or));return}if(a?a==="fraction":t>0&&t<1){const d=this.player.getDuration();if(!d){console.warn("ReactPlayer: could not seek using fraction – duration not yet available");return}this.player.seekTo(d*t,o);return}this.player.seekTo(t,o)}render(){const t=this.props.activePlayer;return t?Ne.default.createElement(t,{...this.props,onMount:this.handlePlayerMount,onReady:this.handleReady,onPlay:this.handlePlay,onPause:this.handlePause,onEnded:this.handleEnded,onLoaded:this.handleLoaded,onError:this.handleError}):null}}f(ae,"displayName","Player");f(ae,"propTypes",ft.propTypes);f(ae,"defaultProps",ft.defaultProps);var Sr=Object.create,X=Object.defineProperty,Tr=Object.getOwnPropertyDescriptor,Dr=Object.getOwnPropertyNames,Er=Object.getPrototypeOf,Cr=Object.prototype.hasOwnProperty,xr=(e,t,a)=>t in e?X(e,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[t]=a,Mr=(e,t)=>{for(var a in t)X(e,a,{get:t[a],enumerable:!0})},mt=(e,t,a,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of Dr(t))!Cr.call(e,r)&&r!==a&&X(e,r,{get:()=>t[r],enumerable:!(o=Tr(t,r))||o.enumerable});return e},q=(e,t,a)=>(a=e!=null?Sr(Er(e)):{},mt(t||!e||!e.__esModule?X(a,"default",{value:e,enumerable:!0}):a,e)),Ar=e=>mt(X({},"__esModule",{value:!0}),e),h=(e,t,a)=>(xr(e,typeof t!="symbol"?t+"":t,a),a),_t={};Mr(_t,{createReactPlayer:()=>jr});var Rr=Ar(_t),U=q(_),Ir=q(Ve),he=q(da),je=q(He),W=ut,gt=ee,kr=q(br);const Lr=(0,gt.lazy)(()=>C(()=>import("./Preview-e418d623.js").then(e=>e.P),["assets/Preview-e418d623.js","assets/index-17b8d91e.js","assets/index-a517bf54.css"])),$r=typeof window<"u"&&window.document&&typeof document<"u",Ur=typeof le<"u"&&le.window&&le.window.document,Br=Object.keys(W.propTypes),Nr=$r||Ur?U.Suspense:()=>null,F=[],jr=(e,t)=>{var a;return a=class extends U.Component{constructor(){super(...arguments),h(this,"state",{showPreview:!!this.props.light}),h(this,"references",{wrapper:o=>{this.wrapper=o},player:o=>{this.player=o}}),h(this,"handleClickPreview",o=>{this.setState({showPreview:!1}),this.props.onClickPreview(o)}),h(this,"showPreview",()=>{this.setState({showPreview:!0})}),h(this,"getDuration",()=>this.player?this.player.getDuration():null),h(this,"getCurrentTime",()=>this.player?this.player.getCurrentTime():null),h(this,"getSecondsLoaded",()=>this.player?this.player.getSecondsLoaded():null),h(this,"getInternalPlayer",(o="player")=>this.player?this.player.getInternalPlayer(o):null),h(this,"seekTo",(o,r,d)=>{if(!this.player)return null;this.player.seekTo(o,r,d)}),h(this,"handleReady",()=>{this.props.onReady(this)}),h(this,"getActivePlayer",(0,he.default)(o=>{for(const r of[...F,...e])if(r.canPlay(o))return r;return t||null})),h(this,"getConfig",(0,he.default)((o,r)=>{const{config:d}=this.props;return Ir.default.all([W.defaultProps.config,W.defaultProps.config[r]||{},d,d[r]||{}])})),h(this,"getAttributes",(0,he.default)(o=>(0,gt.omit)(this.props,Br))),h(this,"renderActivePlayer",o=>{if(!o)return null;const r=this.getActivePlayer(o);if(!r)return null;const d=this.getConfig(o,r.key);return U.default.createElement(kr.default,{...this.props,key:r.key,ref:this.references.player,config:d,activePlayer:r.lazyPlayer||r,onReady:this.handleReady})})}shouldComponentUpdate(o,r){return!(0,je.default)(this.props,o)||!(0,je.default)(this.state,r)}componentDidUpdate(o){const{light:r}=this.props;!o.light&&r&&this.setState({showPreview:!0}),o.light&&!r&&this.setState({showPreview:!1})}renderPreview(o){if(!o)return null;const{light:r,playIcon:d,previewTabIndex:S,oEmbedUrl:s,previewAriaLabel:T}=this.props;return U.default.createElement(Lr,{url:o,light:r,playIcon:d,previewTabIndex:S,previewAriaLabel:T,oEmbedUrl:s,onClick:this.handleClickPreview})}render(){const{url:o,style:r,width:d,height:S,fallback:s,wrapper:T}=this.props,{showPreview:M}=this.state,b=this.getAttributes(o),R=typeof T=="string"?this.references.wrapper:void 0;return U.default.createElement(T,{ref:R,style:{...r,width:d,height:S},...b},U.default.createElement(Nr,{fallback:s},M?this.renderPreview(o):this.renderActivePlayer(o)))}},h(a,"displayName","ReactPlayer"),h(a,"propTypes",W.propTypes),h(a,"defaultProps",W.defaultProps),h(a,"addCustomPlayer",o=>{F.push(o)}),h(a,"removeCustomPlayers",()=>{F.length=0}),h(a,"canPlay",o=>{for(const r of[...F,...e])if(r.canPlay(o))return!0;return!1}),h(a,"canEnablePIP",o=>{for(const r of[...F,...e])if(r.canEnablePIP&&r.canEnablePIP(o))return!0;return!1}),a};var Vr=Object.create,re=Object.defineProperty,Hr=Object.getOwnPropertyDescriptor,zr=Object.getOwnPropertyNames,Fr=Object.getPrototypeOf,Yr=Object.prototype.hasOwnProperty,Wr=(e,t)=>{for(var a in t)re(e,a,{get:t[a],enumerable:!0})},Pt=(e,t,a,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of zr(t))!Yr.call(e,r)&&r!==a&&re(e,r,{get:()=>t[r],enumerable:!(o=Hr(t,r))||o.enumerable});return e},Kr=(e,t,a)=>(a=e!=null?Vr(Fr(e)):{},Pt(t||!e||!e.__esModule?re(a,"default",{value:e,enumerable:!0}):a,e)),Xr=e=>Pt(re({},"__esModule",{value:!0}),e),bt={};Wr(bt,{default:()=>Zr});var qr=Xr(bt),Pe=Kr(Qa),Gr=Rr;const Jr=Pe.default[Pe.default.length-1];var Zr=(0,Gr.createReactPlayer)(Pe.default,Jr);const Qr=zt(qr),eo="569576829775-45noron6i0dj5j5l5ljin32fa635ts1m.apps.googleusercontent.com",to="https://www.googleapis.com/auth/youtube.upload",ao=B(Ft)(({theme:e})=>({padding:e.spacing(3),marginBottom:e.spacing(2),borderRadius:e.spacing(2),boxShadow:"0 2px 12px rgba(0,0,0,0.08)"})),ro=B(y)(({theme:e})=>({fontSize:"18px",fontWeight:600,color:e.palette.text.primary,marginBottom:e.spacing(2),borderBottom:`2px solid ${e.palette.primary.main}`,paddingBottom:e.spacing(1)})),fe=B(Yt)(({theme:e})=>({"& .MuiOutlinedInput-root":{borderRadius:e.spacing(1)}})),oo=B(Wt)(({theme:e})=>({borderRadius:e.spacing(1),backgroundColor:e.palette.background.paper})),no=B(Y)(({theme:e})=>({border:`2px dashed ${e.palette.divider}`,borderRadius:e.spacing(2),padding:e.spacing(3),textAlign:"center",cursor:"pointer",transition:"all 0.3s ease","&:hover":{borderColor:e.palette.primary.main,backgroundColor:e.palette.action.hover}})),so=B(Kt)(({status:e})=>({fontSize:"14px",borderRadius:"4px",fontWeight:500,backgroundColor:e==="Active"?"#cdefd6":e==="Draft"?"#FFC88787":e==="Archived"?"#FAFFC0":"#cddcef"})),io=()=>{const e=Xt(i=>i.userManagement.userData),t=qt(),a=Gt(),r=new URLSearchParams(t.search).get("BroadcastId"),d=new Date,S=new Date;S.setDate(d.getDate()+7);const[s,T]=_.useState({title:"",description:"",category:"",startDate:d,endDate:S,module:"",files:null,file:"",status:"",link:""}),[M,b]=_.useState(!1),[R,oe]=_.useState(!1),[vt,I]=_.useState(!1),[wt,De]=_.useState(!1),[G,Ee]=_.useState(!1),[lo,N]=_.useState(!1),[j,Ce]=_.useState([]),[ne,se]=_.useState(!1),[Ot,V]=_.useState(!1),[St,co]=_.useState(""),[Tt,uo]=_.useState("success"),[Dt,xe]=_.useState(),[Et,Ct]=_.useState(null);_.useEffect(()=>{(()=>{if(!window.google){const c=document.createElement("script");c.src="https://accounts.google.com/gsi/client",c.async=!0,c.defer=!0,document.head.appendChild(c)}})(),At()},[]);const xt=async()=>new Promise((i,c)=>{window.google?window.google.accounts.oauth2.initTokenClient({client_id:eo,scope:to,callback:u=>{u.error?(console.error("Authentication error:",u.error),c(u.error)):(Ct(u.access_token),i(u.access_token))}}).requestAccessToken():c("Google API not loaded")}),Mt=async()=>{try{return Et||await xt()}catch(i){throw console.error("Failed to get authentication token:",i),i}},At=()=>{const i=u=>{const p=u.broadcastDetailsDto;T({title:p.broadcastTitle||"",description:p.description||"",category:p.broadcastCategory||"",startDate:p.createdDate?new Date(p.createdDate):d,endDate:p.endDate?new Date(p.endDate):S,createdBy:p.createdBy||"",module:p.module||"",file:p.fileName||"",status:p.status||"",link:p.externalUrl||"",files:null})},c=u=>{console.error("Error fetching broadcast details:",u)};Ae(`/${de}/broadcastManagement/getBroadcastDetailsById/${r}`,"get",i,c)},k=(i,c)=>{T(u=>({...u,[i]:c})),j.includes(i)&&Ce(u=>u.filter(p=>p!==i))},Rt=i=>{T(c=>({...c,files:i.target.files}))},It=()=>{const i=[],c=["category","title","description"];return s.module&&c.push("module"),c.forEach(u=>{(!s[u]||s[u].trim()==="")&&i.push(u)}),s.startDate>=s.endDate&&i.push("dateRange"),Ce(i),i.length===0},A=()=>["Inactive","Archived"].includes(s.status),kt=async(i,c)=>{V(!0);const u=new FormData;u.append("file",i),c&&u.append("accessToken",c);const p=await fetch("https://cw-mdg-admin-dev.cfapps.eu10-004.hana.ondemand.com/api/videos/upload",{method:"POST",body:u});if(!p.ok){const ie=await p.text();throw new Error(`Upload failed: ${p.status} ${p.statusText} - ${ie}`)}return await p.json()},Lt=async(i=!1)=>{const c=new FormData;s.files&&[...s.files].forEach(p=>c.append("files",p));const u={broadcastId:r,broadcastCategory:s.category,broadcastTitle:s.title,createdBy:(e==null?void 0:e.displayName)||"",startDate:ue(s.startDate).format("YYYY-MM-DD HH:mm:ss.000"),endDate:ue(s.endDate).format("YYYY-MM-DD HH:mm:ss.000"),description:s.description,module:s.module,createdDate:ue(d).format("YYYY-MM-DD HH:mm:ss.000"),externalUrl:s.link,...i&&{status:"Draft"}};return c.append("broadcastDetails",JSON.stringify(u)),c},$t=async(i=!1)=>{if(!It()){N(!0),I(!0);return}try{let c=null;if(s.category==="Videos"&&!i&&s.files&&s.files.length>0){se(!0);try{c=await Mt()}catch{se(!1),N(!0),I(!0);return}se(!1);const J=[...s.files].map(H=>kt(H,c));try{const H=await Promise.all(J)}catch(H){console.error("Video upload failed:",H),N(!0),I(!0);return}}else V(!0);const u=await Lt(i),p=J=>{b(!1),V(!1),xe("Broadcast updated successfully!"),oe(!0)},ie=J=>{console.error("Error updating broadcast:",J),N(!0),I(!0),V(!1),xe("Failed Updating Broadcast!"),oe(!0)};Ae(`/${de}/broadcastManagement/updateBroadcastDetails`,"putformdata",p,ie,u)}catch(c){console.error("Error in submission process:",c),N(!0),I(!0),V(!1)}},Me=()=>{b(!0)},Ut=()=>{$t(!G)},Bt=()=>{De(!0)},Nt=()=>s.category==="Videos"?".mp4":".jpeg,.jpg,.png",jt=()=>s.category==="Videos"?"Only MP4 format supported":"Only PNG, JPEG, JPG formats supported",Vt=`/${de}/broadcastManagement/showBroadcastById/${r}`;return l(Y,{sx:{padding:2,paddingBottom:10},children:[n(D,{container:!0,sx:{borderRadius:2,marginBottom:2},children:n(D,{container:!0,children:n(D,{item:!0,md:7,style:{padding:"16px",paddingLeft:""},children:l(Z,{direction:"row",children:[n(ke,{onClick:()=>a("/configCockpit/broadcastConfigurations"),color:"primary","aria-label":"back",children:n(oa,{sx:{fontSize:"25px",color:"#000000"}})}),l(Y,{children:[n(y,{variant:"h5",paddingTop:"0.3rem",fontSize:"20px",children:l("strong",{children:["Edit Broadcast: ",r]})}),n(y,{variant:"body2",color:"#777",fontSize:"12px",children:"This view displays the details of the broadcast and allows you to edit it"})]})]})})})}),l(ao,{children:[n(ro,{children:"Broadcast Configuration"}),l(D,{container:!0,spacing:3,children:[l(D,{item:!0,xs:12,md:6,lg:3,children:[l(y,{variant:"subtitle2",gutterBottom:!0,children:["Broadcast Category ",n("span",{style:{color:"red"},children:"*"})]}),n(Re,{fullWidth:!0,size:"small",children:l(oo,{value:s.category,onChange:i=>k("category",i.target.value),displayEmpty:!0,disabled:A(),error:j.includes("category"),renderValue:i=>i||n(y,{color:"text.secondary",children:"Select Category"}),children:[n(ce,{value:"",children:n(y,{color:"text.secondary",children:"Select Category"})}),n(ce,{value:"Announcements",children:l(Z,{direction:"row",spacing:1,alignItems:"center",children:[n(na,{color:"primary"}),n(y,{children:"Announcements"})]})}),n(ce,{value:"Videos",children:l(Z,{direction:"row",spacing:1,alignItems:"center",children:[n(sa,{color:"primary"}),n(y,{children:"Videos"})]})})]})})]}),l(D,{item:!0,xs:12,md:6,lg:3,children:[l(y,{variant:"subtitle2",gutterBottom:!0,children:["Module ",s.module&&n("span",{style:{color:"red"},children:"*"})]}),n(Re,{fullWidth:!0,size:"small",children:n(la,{options:[{code:"Material",desc:""},{code:"Cost Center",desc:""}],value:s.module,onChange:i=>k("module",i==null?void 0:i.code),placeholder:"Select Module",disabled:A(),minWidth:"100%",error:j.includes("module")})})]}),l(D,{item:!0,xs:12,md:6,lg:3,children:[l(y,{variant:"subtitle2",gutterBottom:!0,children:["Start Date ",n("span",{style:{color:"red"},children:"*"})]}),n($e,{size:"sm",placeholder:"Select Start Date",value:s.startDate,onChange:i=>k("startDate",i),format:"dd MMM yyyy",disabled:A(),style:{width:"100%",height:"40px"}})]}),l(D,{item:!0,xs:12,md:6,lg:3,children:[l(y,{variant:"subtitle2",gutterBottom:!0,children:["End Date ",n("span",{style:{color:"red"},children:"*"})]}),n($e,{size:"sm",placeholder:"Select End Date",value:s.endDate,onChange:i=>k("endDate",i),format:"dd MMM yyyy",disabled:A(),style:{width:"100%",height:"40px"}})]}),l(D,{item:!0,xs:12,md:6,lg:3,children:[n(y,{variant:"subtitle2",gutterBottom:!0,children:"Broadcast Status"}),n(so,{status:s.status,label:s.status||"Draft",sx:{marginTop:1}})]}),l(D,{item:!0,xs:12,children:[l(y,{variant:"subtitle2",gutterBottom:!0,children:["Broadcast Title ",n("span",{style:{color:"red"},children:"*"}),l(y,{component:"span",variant:"caption",color:"text.secondary",children:[" ","(Max 100 characters)"]})]}),n(fe,{fullWidth:!0,placeholder:"Enter broadcast title",value:s.title,onChange:i=>k("title",i.target.value),disabled:A(),error:j.includes("title"),inputProps:{maxLength:100},helperText:`${s.title.length}/100`})]}),l(D,{item:!0,xs:12,children:[l(y,{variant:"subtitle2",gutterBottom:!0,children:["Broadcast Description ",n("span",{style:{color:"red"},children:"*"}),l(y,{component:"span",variant:"caption",color:"text.secondary",children:[" ","(Max 300 characters)"]})]}),n(fe,{fullWidth:!0,multiline:!0,rows:4,placeholder:"Enter broadcast description",value:s.description,onChange:i=>k("description",i.target.value),disabled:A(),error:j.includes("description"),inputProps:{maxLength:300},helperText:`${s.description.length}/300`})]}),s.file&&l(D,{item:!0,xs:12,children:[n(y,{variant:"subtitle2",gutterBottom:!0,children:"Current Document"}),l(Y,{sx:{display:"flex",alignItems:"center",gap:1},children:[n(y,{variant:"body2",children:s.file}),s.category==="Videos"?n(Zt,{title:"View Video",children:n(ke,{onClick:Bt,size:"small",children:pa})}):n(ua,{index:r,name:s.file,isBroadcast:!0})]})]}),!A()&&l(Jt,{children:[l(D,{item:!0,xs:12,md:8,children:[l(y,{variant:"subtitle2",gutterBottom:!0,children:["Upload New Document",s.category==="Videos"&&n(y,{component:"span",variant:"caption",color:"primary",sx:{ml:1},children:"(Requires Google authentication)"})]}),l(no,{children:[n("input",{accept:Nt(),style:{display:"none"},id:"file-upload",multiple:!0,type:"file",onChange:Rt}),n("label",{htmlFor:"file-upload",children:l(Z,{spacing:1,alignItems:"center",children:[n(ia,{color:"primary",sx:{fontSize:40}}),n(y,{variant:"body2",children:"Click to upload new files"}),n(y,{variant:"caption",color:"text.secondary",children:jt()}),s.files&&l(y,{variant:"caption",color:"primary",children:[s.files.length," file(s) selected"]})]})})]})]}),l(D,{item:!0,xs:12,md:4,children:[n(y,{variant:"subtitle2",gutterBottom:!0,children:"External URL"}),n(fe,{fullWidth:!0,placeholder:"Enter URL (optional)",value:s.link,onChange:i=>k("link",i.target.value),type:"url"})]})]})]})]}),!A()&&n(ea,{sx:{position:"fixed",bottom:0,left:0,right:0,zIndex:5},elevation:2,children:l(Qt,{showLabels:!0,className:"container_BottomNav",sx:{display:"flex",justifyContent:"flex-end"},children:[n(Le,{size:"small",variant:"outlined",onClick:()=>{Ee(!1),Me()},className:"btn-mr",sx:{marginRight:1},disabled:ne,children:"Save As Draft"}),n(Le,{size:"small",variant:"contained",onClick:()=>{Ee(!0),Me()},disabled:ne,children:ne?"Authenticating...":"Publish"})]})}),n(ta,{open:wt,onClose:()=>De(!1),"aria-labelledby":"video-modal",children:n(Y,{sx:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",bgcolor:"background.paper",borderRadius:2,boxShadow:24,p:4,maxWidth:"80vw",maxHeight:"80vh"},children:n(Qr,{url:Vt,controls:!0,width:"100%",height:"400px"})})}),n(Ie,{dialogState:M,closeReusableDialog:()=>b(!1),dialogTitle:"Confirm Broadcast Update",dialogMessage:`Are you sure you want to ${G?"publish":"save as draft"} this broadcast update?${s.category==="Videos"&&G?" You will need to authenticate with Google for video upload.":""}`,handleDialogConfirm:Ut,handleDialogReject:()=>b(!1),showCancelButton:!0,dialogCancelText:"Cancel",dialogOkText:G?"Publish":"Save Draft",dialogSeverity:"success"}),n(aa,{openSnackBar:R,alertMsg:Dt,alertType:Tt,handleSnackBarClose:()=>{oe(!1),a("/configCockpit/broadcastConfigurations")}}),n(Ie,{dialogState:vt,closeReusableDialog:()=>I(!1),dialogTitle:"Validation Error",dialogMessage:"Please fill in all required fields correctly.",handleDialogConfirm:()=>I(!1),dialogOkText:"OK",dialogSeverity:"error"}),n(ra,{blurLoading:Ot,loaderMessage:St})]})},Oo=Object.freeze(Object.defineProperty({__proto__:null,default:io},Symbol.toStringTag,{value:"Module"}));export{Oo as E,Ya as p,ee as u};
