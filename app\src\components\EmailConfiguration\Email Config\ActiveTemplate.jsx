import React, { useState } from "react";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Stack from "@mui/material/Stack";

import Grid from "@mui/material/Grid";
import DisplayTemplateCard from "../utility/DisplayTemplateCard";
import CircularProgress from "@mui/material/CircularProgress";
import Skeleton from "@mui/material/Skeleton";
import { Paper } from "@mui/material";

// import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';

function ActiveTemplate(props) {
  const [openTemplateDialog, setOpenTemplateDialog] = useState({});
  const [isEditing, setIsEditing] = React.useState(null);
  // const [creationType, setCreationType] = React.useState("new");
  // const [selectedRow, setSelectedRow] = React.useState(null);
  const handleCardClick = (key) => {
    setOpenTemplateDialog({ [key]: true });
  };
  const handleDialogClose = () => {
    setOpenTemplateDialog({});
  };

  return (
    <>
      {props.isLoading ? (
        <>
          <Grid container rowSpacing={{xs:2,sm:2,md:3,lg:3}} columnSpacing={{ xs: 2, sm: 2, md: 3 }}>
            {Array.from(Array(10)).map((_, index) => (
              <Grid item xs={2} sm={3} md={3} xl={3}  key={index} height={"10rem"}>
                <Paper width={"100%"}sx={{padding:"1rem"}}>
                  <Stack direction="column" spacing={1} height={"100%"}width={"100%"} justifyContent="center" alignItems="flex-start">
                    <Skeleton variant="text" sx={{ fontSize: "1rem" }} width={200}  p={2} />
                    <Skeleton variant="rounded" width={"80%"} height={60} p={2} />
                    <Stack direction="row" spacing={2} justifyContent="flex-start" alignItems="center"  width={"100%"}>
                      <Skeleton variant="rounded" width={"45%"} height={24} sx={{ borderRadius: "9px" }} />
                      <Skeleton variant="rounded" width={"45%"} height={24} sx={{ borderRadius: "9px" }} />
                    </Stack>
                  </Stack>
                </Paper>
              </Grid>
            ))}
          </Grid>
        </>
      ) : (
        <Grid container rowSpacing={2} columnSpacing={{ xs: 2, sm: 2, md: 3 }}>
          {(props.filteredData.length || props.searchParam !== "" ? props.filteredData : props.active).map((ele, index) => (
            <>
              <DisplayTemplateCard headers={props.headers} cardData={ele} index={index} setCreationType={props.setCreationType} setSelectedRow={props.setSelectedRow} setOpenCreateTemplate={props.setOpenCreateTemplate} setOpenTemplateDialog={setOpenTemplateDialog} mailmappingData={props.mailmappingData} groupList={props.groupList} userList={props.userList} setIsEditing={props.setIsEditing} allGroups={props.allGroups} setScenario={props.setScenario} />
            </>
          ))}
        </Grid>
      )}
    </>
  );
}

export default ActiveTemplate;
